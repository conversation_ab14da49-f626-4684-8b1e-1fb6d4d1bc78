var b,l=l||{};l.scope={};l.findInternal=function(a,c,d){a instanceof String&&(a=String(a));for(var e=a.length,f=0;f<e;f++){var g=a[f];if(c.call(d,g,f,a))return{i:f,v:g}}return{i:-1,v:void 0}};l.ASSUME_ES5=!1;l.ASSUME_NO_NATIVE_MAP=!1;l.ASSUME_NO_NATIVE_SET=!1;l.defineProperty=l.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(a,c,d){a!=Array.prototype&&a!=Object.prototype&&(a[c]=d.value)};
l.getGlobal=function(a){return"undefined"!=typeof window&&window===a?a:"undefined"!=typeof global&&null!=global?global:a};l.global=l.getGlobal(this);l.polyfill=function(a,c){if(c){var d=l.global;a=a.split(".");for(var e=0;e<a.length-1;e++){var f=a[e];f in d||(d[f]={});d=d[f]}a=a[a.length-1];e=d[a];c=c(e);c!=e&&null!=c&&l.defineProperty(d,a,{configurable:!0,writable:!0,value:c})}};
l.checkStringArgs=function(a,c,d){if(null==a)throw new TypeError("The 'this' value for String.prototype."+d+" must not be null or undefined");if(c instanceof RegExp)throw new TypeError("First argument to String.prototype."+d+" must not be a regular expression");return a+""};var n=n||{};n.global=this;n.isDef=function(a){return void 0!==a};n.isString=function(a){return"string"==typeof a};n.isBoolean=function(a){return"boolean"==typeof a};n.isNumber=function(a){return"number"==typeof a};
n.exportPath_=function(a,c,d){a=a.split(".");d=d||n.global;a[0]in d||"undefined"==typeof d.execScript||d.execScript("var "+a[0]);for(var e;a.length&&(e=a.shift());)!a.length&&n.isDef(c)?d[e]=c:d=d[e]&&d[e]!==Object.prototype[e]?d[e]:d[e]={}};n.define=function(a,c){n.exportPath_(a,c)};n.DEBUG=!0;n.LOCALE="en";n.TRUSTED_SITE=!0;n.STRICT_MODE_COMPATIBLE=!1;n.DISALLOW_TEST_ONLY_CODE=!n.DEBUG;n.ENABLE_CHROME_APP_SAFE_SCRIPT_LOADING=!1;
n.provide=function(a){if(n.isInModuleLoader_())throw Error("goog.provide can not be used within a goog.module.");n.constructNamespace_(a)};n.constructNamespace_=function(a,c){n.exportPath_(a,c)};n.VALID_MODULE_RE_=/^[a-zA-Z_$][a-zA-Z0-9._$]*$/;
n.module=function(a){if(!n.isString(a)||!a||-1==a.search(n.VALID_MODULE_RE_))throw Error("Invalid module identifier");if(!n.isInModuleLoader_())throw Error("Module "+a+" has been loaded incorrectly. Note, modules cannot be loaded as normal scripts. They require some kind of pre-processing step. You're likely trying to load a module via a script tag or as a part of a concatenated bundle without rewriting the module. For more info see: https://github.com/google/closure-library/wiki/goog.module:-an-ES6-module-like-alternative-to-goog.provide.");if(n.moduleLoaderState_.moduleName)throw Error("goog.module may only be called once per module.");
n.moduleLoaderState_.moduleName=a};n.module.get=function(){return null};n.module.getInternal_=function(){return null};n.moduleLoaderState_=null;n.isInModuleLoader_=function(){return null!=n.moduleLoaderState_};n.module.declareLegacyNamespace=function(){n.moduleLoaderState_.declareLegacyNamespace=!0};n.setTestOnly=function(a){if(n.DISALLOW_TEST_ONLY_CODE)throw a=a||"",Error("Importing test-only code into non-debug environment"+(a?": "+a:"."));};n.forwardDeclare=function(){};
n.getObjectByName=function(a){a=a.split(".");for(var c=n.global,d=0;d<a.length;d++)if(c=c[a[d]],!n.isDefAndNotNull(c))return null;return c};n.globalize=function(a,c){c=c||n.global;for(var d in a)c[d]=a[d]};n.addDependency=function(a,c,d,e){if(n.DEPENDENCIES_ENABLED){var f=n.getLoader_();f&&f.addDependency(a,c,d,e)}};n.useStrictRequires=!1;n.ENABLE_DEBUG_LOADER=!0;n.logToConsole_=function(a){n.global.console&&n.global.console.error(a)};n.require=function(a){n.ENABLE_DEBUG_LOADER&&n.debugLoader_&&n.getLoader_().earlyProcessLoad(a)};
n.basePath="";n.nullFunction=function(){};n.abstractMethod=function(){throw Error("unimplemented abstract method");};n.addSingletonGetter=function(a){a.instance_=void 0;a.getInstance=function(){if(a.instance_)return a.instance_;n.DEBUG&&(n.instantiatedSingletons_[n.instantiatedSingletons_.length]=a);return a.instance_=new a}};n.instantiatedSingletons_=[];n.LOAD_MODULE_USING_EVAL=!0;n.SEAL_MODULE_EXPORTS=n.DEBUG;n.loadedModules_={};n.DEPENDENCIES_ENABLED=!1;n.TRANSPILE="detect";n.TRANSPILER="transpile.js";
n.DEBUG_LOADER="";n.hasBadLetScoping=null;n.useSafari10Workaround=function(){if(null==n.hasBadLetScoping){try{var a=!eval('"use strict";let x = 1; function f() { return typeof x; };f() == "number";')}catch(c){a=!1}n.hasBadLetScoping=a}return n.hasBadLetScoping};n.workaroundSafari10EvalBug=function(a){return"(function(){"+a+"\n;})();\n"};
n.loadModule=function(a){var c=n.moduleLoaderState_;try{n.moduleLoaderState_={moduleName:"",declareLegacyNamespace:!1};if(n.isFunction(a))var d=a.call(void 0,{});else if(n.isString(a))n.useSafari10Workaround()&&(a=n.workaroundSafari10EvalBug(a)),d=n.loadModuleFromSource_.call(void 0,a);else throw Error("Invalid module definition");var e=n.moduleLoaderState_.moduleName;if(n.isString(e)&&e)n.moduleLoaderState_.declareLegacyNamespace?n.constructNamespace_(e,d):n.SEAL_MODULE_EXPORTS&&Object.seal&&"object"==
typeof d&&null!=d&&Object.seal(d),n.loadedModules_[e]=d;else throw Error('Invalid module name "'+e+'"');}finally{n.moduleLoaderState_=c}};n.loadModuleFromSource_=function(a){eval(a);return{}};n.normalizePath_=function(a){a=a.split("/");for(var c=0;c<a.length;)"."==a[c]?a.splice(c,1):c&&".."==a[c]&&a[c-1]&&".."!=a[c-1]?a.splice(--c,2):c++;return a.join("/")};
n.loadFileSync_=function(a){if(n.global.CLOSURE_LOAD_FILE_SYNC)return n.global.CLOSURE_LOAD_FILE_SYNC(a);try{var c=new n.global.XMLHttpRequest;c.open("get",a,!1);c.send();return 0==c.status||200==c.status?c.responseText:null}catch(d){return null}};
n.transpile_=function(a,c){var d=n.global.$jscomp;d||(n.global.$jscomp=d={});var e=d.transpile;if(!e){var f=n.basePath+n.TRANSPILER,g=n.loadFileSync_(f);if(g){(function(){eval(g+"\n//# sourceURL="+f)}).call(n.global);if(n.global.$gwtExport&&n.global.$gwtExport.$jscomp&&!n.global.$gwtExport.$jscomp.transpile)throw Error('The transpiler did not properly export the "transpile" method. $gwtExport: '+JSON.stringify(n.global.$gwtExport));n.global.$jscomp.transpile=n.global.$gwtExport.$jscomp.transpile;
d=n.global.$jscomp;e=d.transpile}}if(!e){var h=" requires transpilation but no transpiler was found.";h+=' Please add "//javascript/closure:transpiler" as a data dependency to ensure it is included.';e=d.transpile=function(a,c){n.logToConsole_(c+h);return a}}return e(a,c)};
n.typeOf=function(a){var c=typeof a;if("object"==c)if(a){if(a instanceof Array)return"array";if(a instanceof Object)return c;var d=Object.prototype.toString.call(a);if("[object Window]"==d)return"object";if("[object Array]"==d||"number"==typeof a.length&&"undefined"!=typeof a.splice&&"undefined"!=typeof a.propertyIsEnumerable&&!a.propertyIsEnumerable("splice"))return"array";if("[object Function]"==d||"undefined"!=typeof a.call&&"undefined"!=typeof a.propertyIsEnumerable&&!a.propertyIsEnumerable("call"))return"function"}else return"null";
else if("function"==c&&"undefined"==typeof a.call)return"object";return c};n.isNull=function(a){return null===a};n.isDefAndNotNull=function(a){return null!=a};n.isArray=function(a){return"array"==n.typeOf(a)};n.isArrayLike=function(a){var c=n.typeOf(a);return"array"==c||"object"==c&&"number"==typeof a.length};n.isDateLike=function(a){return n.isObject(a)&&"function"==typeof a.getFullYear};n.isFunction=function(a){return"function"==n.typeOf(a)};
n.isObject=function(a){var c=typeof a;return"object"==c&&null!=a||"function"==c};n.getUid=function(a){return a[n.UID_PROPERTY_]||(a[n.UID_PROPERTY_]=++n.uidCounter_)};n.hasUid=function(a){return!!a[n.UID_PROPERTY_]};n.removeUid=function(a){null!==a&&"removeAttribute"in a&&a.removeAttribute(n.UID_PROPERTY_);try{delete a[n.UID_PROPERTY_]}catch(c){}};n.UID_PROPERTY_="closure_uid_"+(1E9*Math.random()>>>0);n.uidCounter_=0;n.getHashCode=n.getUid;n.removeHashCode=n.removeUid;
n.cloneObject=function(a){var c=n.typeOf(a);if("object"==c||"array"==c){if("function"===typeof a.clone)return a.clone();c="array"==c?[]:{};for(var d in a)c[d]=n.cloneObject(a[d]);return c}return a};n.bindNative_=function(a,c,d){return a.call.apply(a.bind,arguments)};
n.bindJs_=function(a,c,d){if(!a)throw Error();if(2<arguments.length){var e=Array.prototype.slice.call(arguments,2);return function(){var d=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(d,e);return a.apply(c,d)}}return function(){return a.apply(c,arguments)}};n.bind=function(a,c,d){Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?n.bind=n.bindNative_:n.bind=n.bindJs_;return n.bind.apply(null,arguments)};
n.partial=function(a,c){var d=Array.prototype.slice.call(arguments,1);return function(){var c=d.slice();c.push.apply(c,arguments);return a.apply(this,c)}};n.mixin=function(a,c){for(var d in c)a[d]=c[d]};n.now=n.TRUSTED_SITE&&Date.now||function(){return+new Date};
n.globalEval=function(a){if(n.global.execScript)n.global.execScript(a,"JavaScript");else if(n.global.eval){if(null==n.evalWorksForGlobals_){try{n.global.eval("var _evalTest_ = 1;")}catch(e){}if("undefined"!=typeof n.global._evalTest_){try{delete n.global._evalTest_}catch(e){}n.evalWorksForGlobals_=!0}else n.evalWorksForGlobals_=!1}if(n.evalWorksForGlobals_)n.global.eval(a);else{var c=n.global.document,d=c.createElement("SCRIPT");d.type="text/javascript";d.defer=!1;d.appendChild(c.createTextNode(a));
c.head.appendChild(d);c.head.removeChild(d)}}else throw Error("goog.globalEval not available");};n.evalWorksForGlobals_=null;
n.getCssName=function(a,c){if("."==String(a).charAt(0))throw Error('className passed in goog.getCssName must not start with ".". You passed: '+a);var d=function(a){return n.cssNameMapping_[a]||a},e=function(a){a=a.split("-");for(var c=[],e=0;e<a.length;e++)c.push(d(a[e]));return c.join("-")};e=n.cssNameMapping_?"BY_WHOLE"==n.cssNameMappingStyle_?d:e:function(a){return a};a=c?a+"-"+e(c):e(a);return n.global.CLOSURE_CSS_NAME_MAP_FN?n.global.CLOSURE_CSS_NAME_MAP_FN(a):a};
n.setCssNameMapping=function(a,c){n.cssNameMapping_=a;n.cssNameMappingStyle_=c};n.getMsg=function(a,c){c&&(a=a.replace(/\{\$([^}]+)}/g,function(a,e){return null!=c&&e in c?c[e]:a}));return a};n.getMsgWithFallback=function(a){return a};n.exportSymbol=function(a,c,d){n.exportPath_(a,c,d)};n.exportProperty=function(a,c,d){a[c]=d};
n.inherits=function(a,c){function d(){}d.prototype=c.prototype;a.superClass_=c.prototype;a.prototype=new d;a.prototype.constructor=a;a.base=function(a,d,g){for(var e=Array(arguments.length-2),f=2;f<arguments.length;f++)e[f-2]=arguments[f];return c.prototype[d].apply(a,e)}};
n.base=function(a,c,d){var e=arguments.callee.caller;if(n.STRICT_MODE_COMPATIBLE||n.DEBUG&&!e)throw Error("arguments.caller not defined.  goog.base() cannot be used with strict mode code. See http://www.ecma-international.org/ecma-262/5.1/#sec-C");if("undefined"!==typeof e.superClass_){for(var f=Array(arguments.length-1),g=1;g<arguments.length;g++)f[g-1]=arguments[g];return e.superClass_.constructor.apply(a,f)}f=Array(arguments.length-2);for(g=2;g<arguments.length;g++)f[g-2]=arguments[g];g=!1;for(var h=
a.constructor;h;h=h.superClass_&&h.superClass_.constructor)if(h.prototype[c]===e)g=!0;else if(g)return h.prototype[c].apply(a,f);if(a[c]===e)return a.constructor.prototype[c].apply(a,f);throw Error("goog.base called from a method of one name to a method of a different name");};n.scope=function(a){if(n.isInModuleLoader_())throw Error("goog.scope is not supported within a goog.module.");a.call(n.global)};
n.defineClass=function(a,c){var d=c.constructor,e=c.statics;d&&d!=Object.prototype.constructor||(d=function(){throw Error("cannot instantiate an interface (no constructor defined).");});d=n.defineClass.createSealingConstructor_(d,a);a&&n.inherits(d,a);delete c.constructor;delete c.statics;n.defineClass.applyProperties_(d.prototype,c);null!=e&&(e instanceof Function?e(d):n.defineClass.applyProperties_(d,e));return d};n.defineClass.SEAL_CLASS_INSTANCES=n.DEBUG;
n.defineClass.createSealingConstructor_=function(a,c){if(!n.defineClass.SEAL_CLASS_INSTANCES)return a;var d=!n.defineClass.isUnsealable_(c),e=function(){var c=a.apply(this,arguments)||this;c[n.UID_PROPERTY_]=c[n.UID_PROPERTY_];this.constructor===e&&d&&Object.seal instanceof Function&&Object.seal(c);return c};return e};n.defineClass.isUnsealable_=function(a){return a&&a.prototype&&a.prototype[n.UNSEALABLE_CONSTRUCTOR_PROPERTY_]};n.defineClass.OBJECT_PROTOTYPE_FIELDS_="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");
n.defineClass.applyProperties_=function(a,c){for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d]);for(var e=0;e<n.defineClass.OBJECT_PROTOTYPE_FIELDS_.length;e++)d=n.defineClass.OBJECT_PROTOTYPE_FIELDS_[e],Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])};n.tagUnsealableClass=function(){};n.UNSEALABLE_CONSTRUCTOR_PROPERTY_="goog_defineClass_legacy_unsealable";
if(n.DEPENDENCIES_ENABLED){n.inHtmlDocument_=function(){var a=n.global.document;return null!=a&&"write"in a};n.findBasePath_=function(){if(n.isDef(n.global.CLOSURE_BASE_PATH)&&n.isString(n.global.CLOSURE_BASE_PATH))n.basePath=n.global.CLOSURE_BASE_PATH;else if(n.inHtmlDocument_()){var a=n.global.document,c=a.currentScript;a=c?[c]:a.getElementsByTagName("SCRIPT");for(c=a.length-1;0<=c;--c){var d=a[c].src,e=d.lastIndexOf("?");e=-1==e?d.length:e;if("base.js"==d.substr(e-7,7)){n.basePath=d.substr(0,e-
7);break}}}};n.findBasePath_();n.Transpiler=function(){this.requiresTranspilation_=null};n.Transpiler.prototype.createRequiresTranspilation_=function(){function a(a,c){e?d[a]=!0:c()?d[a]=!1:e=d[a]=!0}function c(a){try{return!!eval(a)}catch(h){return!1}}var d={es3:!1},e=!1,f=n.global.navigator&&n.global.navigator.userAgent?n.global.navigator.userAgent:"";a("es5",function(){return c("[1,].length==1")});a("es6",function(){var a=f.match(/Edge\/(\d+)(\.\d)*/i);return a&&15>Number(a[1])?!1:c('(()=>{"use strict";class X{constructor(){if(new.target!=String)throw 1;this.x=42}}let q=Reflect.construct(X,[],String);if(q.x!=42||!(q instanceof String))throw 1;for(const a of[2,3]){if(a==2)continue;function f(z={a}){let a=0;return z.a}{function f(){return 0;}}return f()==3}})()')});
a("es6-impl",function(){return!0});a("es7",function(){return c("2 ** 2 == 4")});a("es8",function(){return c("async () => 1, true")});a("es_next",function(){return c("({...rest} = {}), true")});return d};n.Transpiler.prototype.needsTranspile=function(a){if("always"==n.TRANSPILE)return!0;if("never"==n.TRANSPILE)return!1;this.requiresTranspilation_||(this.requiresTranspilation_=this.createRequiresTranspilation_());if(a in this.requiresTranspilation_)return this.requiresTranspilation_[a];throw Error("Unknown language mode: "+
a);};n.Transpiler.prototype.transpile=function(a,c){return n.transpile_(a,c)};n.transpiler_=new n.Transpiler;n.DebugLoader=function(){this.dependencies_={loadFlags:{},nameToPath:{},requires:{},visited:{},written:{},deferred:{}};this.lastNonModuleScriptIndex_=0};n.DebugLoader.IS_OLD_IE_=!(n.global.atob||!n.global.document||!n.global.document.all);b=n.DebugLoader.prototype;b.earlyProcessLoad=function(a){n.DebugLoader.IS_OLD_IE_&&this.maybeProcessDeferredDep_(a)};b.load=function(a){var c=this.getPathFromDeps_(a);
if(c){var d=[],e={},f=this.dependencies_,g=this,h=function(a){if(!(a in f.written||a in f.visited)){f.visited[a]=!0;if(a in f.requires)for(var c in f.requires[a])if(!g.isProvided(c))if(c in f.nameToPath)h(f.nameToPath[c]);else throw Error("Undefined nameToPath for "+c);a in e||(e[a]=!0,d.push(a))}};h(c);for(a=0;a<d.length;a++)c=d[a],this.dependencies_.written[c]=!0;for(a=0;a<d.length;a++)if(c=d[a]){var k=f.loadFlags[c]||{},m=this.getTranspiler().needsTranspile(k.lang||"es3");"goog"==k.module||m?this.importProcessedScript_(n.basePath+
c,"goog"==k.module,m):this.importScript_(n.basePath+c)}else throw Error("Undefined script input");}else if(a="goog.require could not find: "+a,this.logToConsole(a),n.useStrictRequires)throw Error(a);};b.addDependency=function(a,c,d,e){var f;a=a.replace(/\\/g,"/");var g=this.dependencies_;e&&"boolean"!==typeof e||(e=e?{module:"goog"}:{});for(var h=0;f=c[h];h++)g.nameToPath[f]=a,g.loadFlags[a]=e;for(e=0;c=d[e];e++)a in g.requires||(g.requires[a]={}),g.requires[a][c]=!0};b.importScript_=function(a,c){(n.global.CLOSURE_IMPORT_SCRIPT||
n.bind(this.writeScriptTag_,this))(a,c)&&(this.dependencies_.written[a]=!0)};b.importProcessedScript_=function(a,c,d){this.importScript_("",'goog.debugLoader_.retrieveAndExec_("'+a+'", '+c+", "+d+");")};b.maybeProcessDeferredDep_=function(a){this.isDeferredModule_(a)&&this.allDepsAreAvailable_(a)&&this.maybeProcessDeferredPath_(n.basePath+this.getPathFromDeps_(a))};b.isDeferredModule_=function(a){var c=(a=this.getPathFromDeps_(a))&&this.dependencies_.loadFlags[a]||{},d=c.lang||"es3";return a&&("goog"==
c.module||this.getTranspiler().needsTranspile(d))?n.basePath+a in this.dependencies_.deferred:!1};b.allDepsAreAvailable_=function(a){if((a=this.getPathFromDeps_(a))&&a in this.dependencies_.requires)for(var c in this.dependencies_.requires[a])if(!this.isProvided(c)&&!this.isDeferredModule_(c))return!1;return!0};b.maybeProcessDeferredPath_=function(a){if(a in this.dependencies_.deferred){var c=this.dependencies_.deferred[a];delete this.dependencies_.deferred[a];n.globalEval(c)}};b.writeScriptSrcNode_=
function(a){n.global.document.write('<script type="text/javascript" src="'+a+'">\x3c/script>')};b.appendScriptSrcNode_=function(a){var c=n.global.document,d=c.createElement("script");d.type="text/javascript";d.src=a;d.defer=!1;d.async=!1;c.head.appendChild(d)};b.writeScriptTag_=function(a,c){if(this.inHtmlDocument()){var d=n.global.document;if(!n.ENABLE_CHROME_APP_SAFE_SCRIPT_LOADING&&"complete"==d.readyState){if(/\bdeps.js$/.test(a))return!1;throw Error('Cannot write "'+a+'" after document load');
}void 0===c?n.DebugLoader.IS_OLD_IE_?(c=" onreadystatechange='goog.debugLoader_.onScriptLoad_(this, "+ ++this.lastNonModuleScriptIndex_+")' ",d.write('<script type="text/javascript" src="'+a+'"'+c+">\x3c/script>")):n.ENABLE_CHROME_APP_SAFE_SCRIPT_LOADING?this.appendScriptSrcNode_(a):this.writeScriptSrcNode_(a):d.write('<script type="text/javascript">'+this.protectScriptTag_(c)+"\x3c/script>");return!0}return!1};b.protectScriptTag_=function(a){return a.replace(/<\/(SCRIPT)/ig,"\\x3c/$1")};b.getPathFromDeps_=
function(a){return a in this.dependencies_.nameToPath?this.dependencies_.nameToPath[a]:null};b.getTranspiler=function(){return n.transpiler_};b.isProvided=function(a){return n.isProvided_(a)};b.inHtmlDocument=function(){return n.inHtmlDocument_()};b.logToConsole=function(a){n.logToConsole_(a)};n.debugLoader_=null;n.registerDebugLoader=function(){var a=q;if(n.debugLoader_)throw Error("Debug loader already registered!");if(!(a instanceof n.DebugLoader))throw Error("Not a goog.DebugLoader.");n.debugLoader_=
a};n.getLoader_=function(){if(!n.debugLoader_&&n.DEBUG_LOADER)throw Error("Loaded debug loader file but no loader was registered!");n.debugLoader_||(n.debugLoader_=new n.DebugLoader);return n.debugLoader_};var q;n.DEBUG_LOADER&&(q=new n.DebugLoader,q.importScript_(n.basePath+n.DEBUG_LOADER));n.global.CLOSURE_NO_DEPS||(q=q||new n.DebugLoader,n.DEBUG_LOADER||n.registerDebugLoader(),q.importScript_(n.basePath+"deps.js"))};n.craw={};n.craw.AppBackgroundDelegate=function(){};n.craw.AppBackgroundDelegate.impl_=n.craw.AppBackgroundDelegate;n.craw.AppBackgroundDelegate.setImpl=function(){n.craw.AppBackgroundDelegate.impl_=u.BackgroundDelegate};n.craw.AppBackgroundDelegate.createImpl=function(){return new n.craw.AppBackgroundDelegate.impl_};n.craw.AppBackgroundDelegate.prototype.getWindowBounds=function(){};n.craw.WindowConfig=function(a,c){this.url_=a;this.useAuth_=c};n.craw.WindowConfig.prototype.getUrl=function(){return this.url_};n.craw.WindowConfig.prototype.getUseAuth=function(){return this.useAuth_};var u={Errors:{}};u.Errors.ErrorTypes={MINT_JWT_ERROR:"MINT_JWT_ERROR",PURCHASE_CANCELED:"PURCHASE_CANCELED",CONSUME_PURCHASE_ERROR:"CONSUME_PURCHASE_ERROR",GET_PURCHASES_ERROR:"GET_PURCHASES_ERROR",GET_SKU_DETAILS_ERROR:"GET_SKU_DETAILS_ERROR",ENV_NOT_SUPPORTED_ERROR:"ENV_NOT_SUPPORTED_ERROR",TOKEN_MISSING_ERROR:"TOKEN_MISSING_ERROR",INVALID_RESPONSE_ERROR:"INVALID_RESPONSE_ERROR"};u.Errors.getErrorResponse=function(a){return{request:{},response:{errorType:a}}};n.debug={};n.debug.Error=function(a){if(Error.captureStackTrace)Error.captureStackTrace(this,n.debug.Error);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a))};n.inherits(n.debug.Error,Error);n.debug.Error.prototype.name="CustomError";n.dom={};n.dom.NodeType={ELEMENT:1,ATTRIBUTE:2,TEXT:3,CDATA_SECTION:4,ENTITY_REFERENCE:5,ENTITY:6,PROCESSING_INSTRUCTION:7,COMMENT:8,DOCUMENT:9,DOCUMENT_TYPE:10,DOCUMENT_FRAGMENT:11,NOTATION:12};n.asserts={};n.asserts.ENABLE_ASSERTS=n.DEBUG;n.asserts.AssertionError=function(a,c){n.debug.Error.call(this,n.asserts.subs_(a,c))};n.inherits(n.asserts.AssertionError,n.debug.Error);n.asserts.AssertionError.prototype.name="AssertionError";n.asserts.DEFAULT_ERROR_HANDLER=function(a){throw a;};n.asserts.errorHandler_=n.asserts.DEFAULT_ERROR_HANDLER;n.asserts.subs_=function(a,c){a=a.split("%s");for(var d="",e=a.length-1,f=0;f<e;f++)d+=a[f]+(f<c.length?c[f]:"%s");return d+a[e]};
n.asserts.doAssertFailure_=function(a,c,d,e){var f="Assertion failed";if(d){f+=": "+d;var g=e}else a&&(f+=": "+a,g=c);a=new n.asserts.AssertionError(""+f,g||[]);n.asserts.errorHandler_(a)};n.asserts.setErrorHandler=function(a){n.asserts.ENABLE_ASSERTS&&(n.asserts.errorHandler_=a)};n.asserts.assert=function(a,c,d){n.asserts.ENABLE_ASSERTS&&!a&&n.asserts.doAssertFailure_("",null,c,Array.prototype.slice.call(arguments,2));return a};
n.asserts.fail=function(a,c){n.asserts.ENABLE_ASSERTS&&n.asserts.errorHandler_(new n.asserts.AssertionError("Failure"+(a?": "+a:""),Array.prototype.slice.call(arguments,1)))};n.asserts.assertNumber=function(a,c,d){n.asserts.ENABLE_ASSERTS&&!n.isNumber(a)&&n.asserts.doAssertFailure_("Expected number but got %s: %s.",[n.typeOf(a),a],c,Array.prototype.slice.call(arguments,2));return a};
n.asserts.assertString=function(a,c,d){n.asserts.ENABLE_ASSERTS&&!n.isString(a)&&n.asserts.doAssertFailure_("Expected string but got %s: %s.",[n.typeOf(a),a],c,Array.prototype.slice.call(arguments,2))};n.asserts.assertFunction=function(a,c,d){n.asserts.ENABLE_ASSERTS&&!n.isFunction(a)&&n.asserts.doAssertFailure_("Expected function but got %s: %s.",[n.typeOf(a),a],c,Array.prototype.slice.call(arguments,2))};
n.asserts.assertObject=function(a,c,d){n.asserts.ENABLE_ASSERTS&&!n.isObject(a)&&n.asserts.doAssertFailure_("Expected object but got %s: %s.",[n.typeOf(a),a],c,Array.prototype.slice.call(arguments,2));return a};n.asserts.assertArray=function(a,c,d){n.asserts.ENABLE_ASSERTS&&!n.isArray(a)&&n.asserts.doAssertFailure_("Expected array but got %s: %s.",[n.typeOf(a),a],c,Array.prototype.slice.call(arguments,2))};
n.asserts.assertBoolean=function(a,c,d){n.asserts.ENABLE_ASSERTS&&!n.isBoolean(a)&&n.asserts.doAssertFailure_("Expected boolean but got %s: %s.",[n.typeOf(a),a],c,Array.prototype.slice.call(arguments,2));return a};n.asserts.assertElement=function(a,c,d){!n.asserts.ENABLE_ASSERTS||n.isObject(a)&&a.nodeType==n.dom.NodeType.ELEMENT||n.asserts.doAssertFailure_("Expected Element but got %s: %s.",[n.typeOf(a),a],c,Array.prototype.slice.call(arguments,2));return a};
n.asserts.assertInstanceof=function(a,c,d,e){!n.asserts.ENABLE_ASSERTS||a instanceof c||n.asserts.doAssertFailure_("Expected instanceof %s but got %s.",[n.asserts.getType_(c),n.asserts.getType_(a)],d,Array.prototype.slice.call(arguments,3));return a};n.asserts.assertFinite=function(a,c,d){!n.asserts.ENABLE_ASSERTS||"number"==typeof a&&isFinite(a)||n.asserts.doAssertFailure_("Expected %s to be a finite number but it is not.",[a],c,Array.prototype.slice.call(arguments,2));return a};
n.asserts.assertObjectPrototypeIsIntact=function(){for(var a in Object.prototype)n.asserts.fail(a+" should not be enumerable in Object.prototype.")};n.asserts.getType_=function(a){return a instanceof Function?a.displayName||a.name||"unknown type name":a instanceof Object?a.constructor.displayName||a.constructor.name||Object.prototype.toString.call(a):null===a?"null":typeof a};n.array={};n.NATIVE_ARRAY_PROTOTYPES=n.TRUSTED_SITE;n.array.ASSUME_NATIVE_FUNCTIONS=!1;n.array.peek=function(a){return a[a.length-1]};n.array.last=n.array.peek;
n.array.indexOf=n.NATIVE_ARRAY_PROTOTYPES&&(n.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.indexOf)?function(a,c,d){n.asserts.assert(null!=a.length);return Array.prototype.indexOf.call(a,c,d)}:function(a,c,d){d=null==d?0:0>d?Math.max(0,a.length+d):d;if(n.isString(a))return n.isString(c)&&1==c.length?a.indexOf(c,d):-1;for(;d<a.length;d++)if(d in a&&a[d]===c)return d;return-1};
n.array.lastIndexOf=n.NATIVE_ARRAY_PROTOTYPES&&(n.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.lastIndexOf)?function(a,c,d){n.asserts.assert(null!=a.length);return Array.prototype.lastIndexOf.call(a,c,null==d?a.length-1:d)}:function(a,c,d){d=null==d?a.length-1:d;0>d&&(d=Math.max(0,a.length+d));if(n.isString(a))return n.isString(c)&&1==c.length?a.lastIndexOf(c,d):-1;for(;0<=d;d--)if(d in a&&a[d]===c)return d;return-1};
n.array.forEach=n.NATIVE_ARRAY_PROTOTYPES&&(n.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.forEach)?function(a,c,d){n.asserts.assert(null!=a.length);Array.prototype.forEach.call(a,c,d)}:function(a,c,d){for(var e=a.length,f=n.isString(a)?a.split(""):a,g=0;g<e;g++)g in f&&c.call(d,f[g],g,a)};n.array.forEachRight=function(a,c){var d=a.length,e=n.isString(a)?a.split(""):a;for(--d;0<=d;--d)d in e&&c.call(void 0,e[d],d,a)};
n.array.filter=n.NATIVE_ARRAY_PROTOTYPES&&(n.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.filter)?function(a,c,d){n.asserts.assert(null!=a.length);return Array.prototype.filter.call(a,c,d)}:function(a,c,d){for(var e=a.length,f=[],g=0,h=n.isString(a)?a.split(""):a,k=0;k<e;k++)if(k in h){var m=h[k];c.call(d,m,k,a)&&(f[g++]=m)}return f};
n.array.map=n.NATIVE_ARRAY_PROTOTYPES&&(n.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.map)?function(a,c,d){n.asserts.assert(null!=a.length);return Array.prototype.map.call(a,c,d)}:function(a,c,d){for(var e=a.length,f=Array(e),g=n.isString(a)?a.split(""):a,h=0;h<e;h++)h in g&&(f[h]=c.call(d,g[h],h,a));return f};
n.array.reduce=n.NATIVE_ARRAY_PROTOTYPES&&(n.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.reduce)?function(a,c,d,e){n.asserts.assert(null!=a.length);e&&(c=n.bind(c,e));return Array.prototype.reduce.call(a,c,d)}:function(a,c,d,e){var f=d;n.array.forEach(a,function(d,h){f=c.call(e,f,d,h,a)});return f};
n.array.reduceRight=n.NATIVE_ARRAY_PROTOTYPES&&(n.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.reduceRight)?function(a,c,d,e){n.asserts.assert(null!=a.length);n.asserts.assert(null!=c);e&&(c=n.bind(c,e));return Array.prototype.reduceRight.call(a,c,d)}:function(a,c,d,e){var f=d;n.array.forEachRight(a,function(d,h){f=c.call(e,f,d,h,a)});return f};
n.array.some=n.NATIVE_ARRAY_PROTOTYPES&&(n.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.some)?function(a,c,d){n.asserts.assert(null!=a.length);return Array.prototype.some.call(a,c,d)}:function(a,c,d){for(var e=a.length,f=n.isString(a)?a.split(""):a,g=0;g<e;g++)if(g in f&&c.call(d,f[g],g,a))return!0;return!1};
n.array.every=n.NATIVE_ARRAY_PROTOTYPES&&(n.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.every)?function(a,c,d){n.asserts.assert(null!=a.length);return Array.prototype.every.call(a,c,d)}:function(a,c,d){for(var e=a.length,f=n.isString(a)?a.split(""):a,g=0;g<e;g++)if(g in f&&!c.call(d,f[g],g,a))return!1;return!0};n.array.count=function(a,c,d){var e=0;n.array.forEach(a,function(a,g,h){c.call(d,a,g,h)&&++e},d);return e};
n.array.find=function(a,c,d){c=n.array.findIndex(a,c,d);return 0>c?null:n.isString(a)?a.charAt(c):a[c]};n.array.findIndex=function(a,c,d){for(var e=a.length,f=n.isString(a)?a.split(""):a,g=0;g<e;g++)if(g in f&&c.call(d,f[g],g,a))return g;return-1};n.array.findRight=function(a,c,d){c=n.array.findIndexRight(a,c,d);return 0>c?null:n.isString(a)?a.charAt(c):a[c]};
n.array.findIndexRight=function(a,c,d){var e=a.length,f=n.isString(a)?a.split(""):a;for(--e;0<=e;e--)if(e in f&&c.call(d,f[e],e,a))return e;return-1};n.array.contains=function(a,c){return 0<=n.array.indexOf(a,c)};n.array.isEmpty=function(a){return 0==a.length};n.array.clear=function(a){if(!n.isArray(a))for(var c=a.length-1;0<=c;c--)delete a[c];a.length=0};n.array.insert=function(a,c){n.array.contains(a,c)||a.push(c)};n.array.insertAt=function(a,c,d){n.array.splice(a,d,0,c)};
n.array.insertArrayAt=function(a,c,d){n.partial(n.array.splice,a,d,0).apply(null,c)};n.array.insertBefore=function(a,c,d){var e;2==arguments.length||0>(e=n.array.indexOf(a,d))?a.push(c):n.array.insertAt(a,c,e)};n.array.remove=function(a,c){c=n.array.indexOf(a,c);var d;(d=0<=c)&&n.array.removeAt(a,c);return d};n.array.removeLast=function(a,c){c=n.array.lastIndexOf(a,c);return 0<=c?(n.array.removeAt(a,c),!0):!1};
n.array.removeAt=function(a,c){n.asserts.assert(null!=a.length);return 1==Array.prototype.splice.call(a,c,1).length};n.array.removeIf=function(a,c,d){c=n.array.findIndex(a,c,d);return 0<=c?(n.array.removeAt(a,c),!0):!1};n.array.removeAllIf=function(a,c,d){var e=0;n.array.forEachRight(a,function(f,g){c.call(d,f,g,a)&&n.array.removeAt(a,g)&&e++});return e};n.array.concat=function(a){return Array.prototype.concat.apply([],arguments)};n.array.join=function(a){return Array.prototype.concat.apply([],arguments)};
n.array.toArray=function(a){var c=a.length;if(0<c){for(var d=Array(c),e=0;e<c;e++)d[e]=a[e];return d}return[]};n.array.clone=n.array.toArray;n.array.extend=function(a,c){for(var d=1;d<arguments.length;d++){var e=arguments[d];if(n.isArrayLike(e)){var f=a.length||0,g=e.length||0;a.length=f+g;for(var h=0;h<g;h++)a[f+h]=e[h]}else a.push(e)}};n.array.splice=function(a,c,d,e){n.asserts.assert(null!=a.length);return Array.prototype.splice.apply(a,n.array.slice(arguments,1))};
n.array.slice=function(a,c,d){n.asserts.assert(null!=a.length);return 2>=arguments.length?Array.prototype.slice.call(a,c):Array.prototype.slice.call(a,c,d)};n.array.removeDuplicates=function(a,c){c=c||a;for(var d={},e=0,f=0;f<a.length;){var g=a[f++];var h=g;h=n.isObject(h)?"o"+n.getUid(h):(typeof h).charAt(0)+h;Object.prototype.hasOwnProperty.call(d,h)||(d[h]=!0,c[e++]=g)}c.length=e};n.array.binarySearch=function(a,c,d){return n.array.binarySearch_(a,d||n.array.defaultCompare,!1,c)};
n.array.binarySelect=function(a,c,d){return n.array.binarySearch_(a,c,!0,void 0,d)};n.array.binarySearch_=function(a,c,d,e,f){for(var g=0,h=a.length,k;g<h;){var m=g+h>>1;var p=d?c.call(f,a[m],m,a):c(e,a[m]);0<p?g=m+1:(h=m,k=!p)}return k?g:~g};n.array.sort=function(a,c){a.sort(c||n.array.defaultCompare)};
n.array.stableSort=function(a,c){for(var d=Array(a.length),e=0;e<a.length;e++)d[e]={index:e,value:a[e]};var f=c||n.array.defaultCompare;n.array.sort(d,function(a,c){return f(a.value,c.value)||a.index-c.index});for(e=0;e<a.length;e++)a[e]=d[e].value};n.array.sortByKey=function(a,c,d){var e=d||n.array.defaultCompare;n.array.sort(a,function(a,d){return e(c(a),c(d))})};n.array.sortObjectsByKey=function(a,c,d){n.array.sortByKey(a,function(a){return a[c]},d)};
n.array.isSorted=function(a){for(var c=n.array.defaultCompare,d=1;d<a.length;d++)if(0<c(a[d-1],a[d]))return!1;return!0};n.array.equals=function(a,c,d){if(!n.isArrayLike(a)||!n.isArrayLike(c)||a.length!=c.length)return!1;var e=a.length;d=d||n.array.defaultCompareEquality;for(var f=0;f<e;f++)if(!d(a[f],c[f]))return!1;return!0};
n.array.compare3=function(a,c,d){d=d||n.array.defaultCompare;for(var e=Math.min(a.length,c.length),f=0;f<e;f++){var g=d(a[f],c[f]);if(0!=g)return g}return n.array.defaultCompare(a.length,c.length)};n.array.defaultCompare=function(a,c){return a>c?1:a<c?-1:0};n.array.inverseDefaultCompare=function(a,c){return-n.array.defaultCompare(a,c)};n.array.defaultCompareEquality=function(a,c){return a===c};
n.array.binaryInsert=function(a,c,d){d=n.array.binarySearch(a,c,d);return 0>d?(n.array.insertAt(a,c,-(d+1)),!0):!1};n.array.binaryRemove=function(a,c,d){c=n.array.binarySearch(a,c,d);return 0<=c?n.array.removeAt(a,c):!1};n.array.bucket=function(a,c,d){for(var e={},f=0;f<a.length;f++){var g=a[f],h=c.call(d,g,f,a);n.isDef(h)&&(e[h]||(e[h]=[])).push(g)}return e};n.array.toObject=function(a,c,d){var e={};n.array.forEach(a,function(f,g){e[c.call(d,f,g,a)]=f});return e};
n.array.range=function(a,c,d){var e=[],f=0,g=a;d=d||1;void 0!==c&&(f=a,g=c);if(0>d*(g-f))return[];if(0<d)for(a=f;a<g;a+=d)e.push(a);else for(a=f;a>g;a+=d)e.push(a);return e};n.array.repeat=function(a,c){for(var d=[],e=0;e<c;e++)d[e]=a;return d};n.array.flatten=function(a){for(var c=[],d=0;d<arguments.length;d++){var e=arguments[d];if(n.isArray(e))for(var f=0;f<e.length;f+=8192){var g=n.array.slice(e,f,f+8192);g=n.array.flatten.apply(null,g);for(var h=0;h<g.length;h++)c.push(g[h])}else c.push(e)}return c};
n.array.rotate=function(a,c){n.asserts.assert(null!=a.length);a.length&&(c%=a.length,0<c?Array.prototype.unshift.apply(a,a.splice(-c,c)):0>c&&Array.prototype.push.apply(a,a.splice(0,-c)));return a};n.array.moveItem=function(a,c,d){n.asserts.assert(0<=c&&c<a.length);n.asserts.assert(0<=d&&d<a.length);c=Array.prototype.splice.call(a,c,1);Array.prototype.splice.call(a,d,0,c[0])};
n.array.zip=function(a){if(!arguments.length)return[];for(var c=[],d=arguments[0].length,e=1;e<arguments.length;e++)arguments[e].length<d&&(d=arguments[e].length);for(e=0;e<d;e++){for(var f=[],g=0;g<arguments.length;g++)f.push(arguments[g][e]);c.push(f)}return c};n.array.shuffle=function(a,c){c=c||Math.random;for(var d=a.length-1;0<d;d--){var e=Math.floor(c()*(d+1)),f=a[d];a[d]=a[e];a[e]=f}};n.array.copyByIndex=function(a,c){var d=[];n.array.forEach(c,function(c){d.push(a[c])});return d};
n.array.concatMap=function(a,c,d){return n.array.concat.apply([],n.array.map(a,c,d))};n.debug.errorcontext={};n.debug.errorcontext.addErrorContext=function(a,c,d){a[n.debug.errorcontext.CONTEXT_KEY_]||(a[n.debug.errorcontext.CONTEXT_KEY_]={});a[n.debug.errorcontext.CONTEXT_KEY_][c]=d};n.debug.errorcontext.getErrorContext=function(a){return a[n.debug.errorcontext.CONTEXT_KEY_]||{}};n.debug.errorcontext.CONTEXT_KEY_="__closure__error__context__984382";n.string={};n.string.DETECT_DOUBLE_ESCAPING=!1;n.string.FORCE_NON_DOM_HTML_UNESCAPING=!1;n.string.Unicode={NBSP:"\u00a0"};n.string.startsWith=function(a,c){return 0==a.lastIndexOf(c,0)};n.string.endsWith=function(a,c){var d=a.length-c.length;return 0<=d&&a.indexOf(c,d)==d};n.string.caseInsensitiveStartsWith=function(a,c){return 0==n.string.caseInsensitiveCompare(c,a.substr(0,c.length))};
n.string.caseInsensitiveEndsWith=function(a,c){return 0==n.string.caseInsensitiveCompare(c,a.substr(a.length-c.length,c.length))};n.string.caseInsensitiveEquals=function(a){return n.net.XhrIo.CONTENT_TYPE_HEADER.toLowerCase()==a.toLowerCase()};n.string.subs=function(a,c){for(var d=a.split("%s"),e="",f=Array.prototype.slice.call(arguments,1);f.length&&1<d.length;)e+=d.shift()+f.shift();return e+d.join("%s")};
n.string.collapseWhitespace=function(a){return a.replace(/[\s\xa0]+/g," ").replace(/^\s+|\s+$/g,"")};n.string.isEmptyOrWhitespace=function(a){return/^[\s\xa0]*$/.test(a)};n.string.isEmptyString=function(a){return 0==a.length};n.string.isEmpty=n.string.isEmptyOrWhitespace;n.string.isEmptyOrWhitespaceSafe=function(a){return n.string.isEmptyOrWhitespace(n.string.makeSafe(a))};n.string.isEmptySafe=n.string.isEmptyOrWhitespaceSafe;n.string.isBreakingWhitespace=function(a){return!/[^\t\n\r ]/.test(a)};
n.string.isAlpha=function(a){return!/[^a-zA-Z]/.test(a)};n.string.isNumeric=function(a){return!/[^0-9]/.test(a)};n.string.isAlphaNumeric=function(a){return!/[^a-zA-Z0-9]/.test(a)};n.string.isSpace=function(a){return" "==a};n.string.isUnicodeChar=function(a){return 1==a.length&&" "<=a&&"~">=a||"\u0080"<=a&&"\ufffd">=a};n.string.stripNewlines=function(a){return a.replace(/(\r\n|\r|\n)+/g," ")};n.string.canonicalizeNewlines=function(a){return a.replace(/(\r\n|\r|\n)/g,"\n")};
n.string.normalizeWhitespace=function(a){return a.replace(/\xa0|\s/g," ")};n.string.normalizeSpaces=function(a){return a.replace(/\xa0|[ \t]+/g," ")};n.string.collapseBreakingSpaces=function(a){return a.replace(/[\t\r\n ]+/g," ").replace(/^[\t\r\n ]+|[\t\r\n ]+$/g,"")};n.string.trim=n.TRUSTED_SITE&&String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};n.string.trimLeft=function(a){return a.replace(/^[\s\xa0]+/,"")};
n.string.trimRight=function(a){return a.replace(/[\s\xa0]+$/,"")};n.string.caseInsensitiveCompare=function(a,c){a=String(a).toLowerCase();c=String(c).toLowerCase();return a<c?-1:a==c?0:1};
n.string.numberAwareCompare_=function(a,c,d){if(a==c)return 0;if(!a)return-1;if(!c)return 1;for(var e=a.toLowerCase().match(d),f=c.toLowerCase().match(d),g=Math.min(e.length,f.length),h=0;h<g;h++){d=e[h];var k=f[h];if(d!=k)return a=parseInt(d,10),!isNaN(a)&&(c=parseInt(k,10),!isNaN(c)&&a-c)?a-c:d<k?-1:1}return e.length!=f.length?e.length-f.length:a<c?-1:1};n.string.intAwareCompare=function(a,c){return n.string.numberAwareCompare_(a,c,/\d+|\D+/g)};
n.string.floatAwareCompare=function(a,c){return n.string.numberAwareCompare_(a,c,/\d+|\.\d+|\D+/g)};n.string.numerateCompare=n.string.floatAwareCompare;n.string.urlEncode=function(a){return encodeURIComponent(String(a))};n.string.urlDecode=function(a){return decodeURIComponent(a.replace(/\+/g," "))};n.string.newLineToBr=function(a,c){return a.replace(/(\r\n|\r|\n)/g,c?"<br />":"<br>")};
n.string.htmlEscape=function(a){if(!n.string.ALL_RE_.test(a))return a;-1!=a.indexOf("&")&&(a=a.replace(n.string.AMP_RE_,"&amp;"));-1!=a.indexOf("<")&&(a=a.replace(n.string.LT_RE_,"&lt;"));-1!=a.indexOf(">")&&(a=a.replace(n.string.GT_RE_,"&gt;"));-1!=a.indexOf('"')&&(a=a.replace(n.string.QUOT_RE_,"&quot;"));-1!=a.indexOf("'")&&(a=a.replace(n.string.SINGLE_QUOTE_RE_,"&#39;"));-1!=a.indexOf("\x00")&&(a=a.replace(n.string.NULL_RE_,"&#0;"));n.string.DETECT_DOUBLE_ESCAPING&&-1!=a.indexOf("e")&&(a=a.replace(n.string.E_RE_,
"&#101;"));return a};n.string.AMP_RE_=/&/g;n.string.LT_RE_=/</g;n.string.GT_RE_=/>/g;n.string.QUOT_RE_=/"/g;n.string.SINGLE_QUOTE_RE_=/'/g;n.string.NULL_RE_=/\x00/g;n.string.E_RE_=/e/g;n.string.ALL_RE_=n.string.DETECT_DOUBLE_ESCAPING?/[\x00&<>"'e]/:/[\x00&<>"']/;n.string.unescapeEntities=function(a){return n.string.contains(a,"&")?!n.string.FORCE_NON_DOM_HTML_UNESCAPING&&"document"in n.global?n.string.unescapeEntitiesUsingDom_(a):n.string.unescapePureXmlEntities_(a):a};
n.string.unescapeEntitiesWithDocument=function(a,c){return n.string.contains(a,"&")?n.string.unescapeEntitiesUsingDom_(a,c):a};
n.string.unescapeEntitiesUsingDom_=function(a,c){var d={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"'};var e=c?c.createElement("div"):n.global.document.createElement("div");return a.replace(n.string.HTML_ENTITY_PATTERN_,function(a,c){var f=d[a];if(f)return f;"#"==c.charAt(0)&&(c=Number("0"+c.substr(1)),isNaN(c)||(f=String.fromCharCode(c)));f||(e.innerHTML=a+" ",f=e.firstChild.nodeValue.slice(0,-1));return d[a]=f})};
n.string.unescapePureXmlEntities_=function(a){return a.replace(/&([^;]+);/g,function(a,d){switch(d){case "amp":return"&";case "lt":return"<";case "gt":return">";case "quot":return'"';default:return"#"!=d.charAt(0)||(d=Number("0"+d.substr(1)),isNaN(d))?a:String.fromCharCode(d)}})};n.string.HTML_ENTITY_PATTERN_=/&([^;\s<&]+);?/g;n.string.whitespaceEscape=function(a,c){return n.string.newLineToBr(a.replace(/  /g," &#160;"),c)};n.string.preserveSpaces=function(a){return a.replace(/(^|[\n ]) /g,"$1"+n.string.Unicode.NBSP)};
n.string.stripQuotes=function(a,c){for(var d=c.length,e=0;e<d;e++){var f=1==d?c:c.charAt(e);if(a.charAt(0)==f&&a.charAt(a.length-1)==f)return a.substring(1,a.length-1)}return a};n.string.truncate=function(a,c,d){d&&(a=n.string.unescapeEntities(a));a.length>c&&(a=a.substring(0,c-3)+"...");d&&(a=n.string.htmlEscape(a));return a};
n.string.truncateMiddle=function(a,c,d,e){d&&(a=n.string.unescapeEntities(a));e&&a.length>c?(e>c&&(e=c),a=a.substring(0,c-e)+"..."+a.substring(a.length-e)):a.length>c&&(e=Math.floor(c/2),a=a.substring(0,e+c%2)+"..."+a.substring(a.length-e));d&&(a=n.string.htmlEscape(a));return a};n.string.specialEscapeChars_={"\x00":"\\0","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","\t":"\\t","\x0B":"\\x0B",'"':'\\"',"\\":"\\\\","<":"<"};n.string.jsEscapeCache_={"'":"\\'"};
n.string.quote=function(a){a=String(a);for(var c=['"'],d=0;d<a.length;d++){var e=a.charAt(d),f=e.charCodeAt(0);c[d+1]=n.string.specialEscapeChars_[e]||(31<f&&127>f?e:n.string.escapeChar(e))}c.push('"');return c.join("")};n.string.escapeString=function(a){for(var c=[],d=0;d<a.length;d++)c[d]=n.string.escapeChar(a.charAt(d));return c.join("")};
n.string.escapeChar=function(a){if(a in n.string.jsEscapeCache_)return n.string.jsEscapeCache_[a];if(a in n.string.specialEscapeChars_)return n.string.jsEscapeCache_[a]=n.string.specialEscapeChars_[a];var c=a.charCodeAt(0);if(31<c&&127>c)var d=a;else{if(256>c){if(d="\\x",16>c||256<c)d+="0"}else d="\\u",4096>c&&(d+="0");d+=c.toString(16).toUpperCase()}return n.string.jsEscapeCache_[a]=d};n.string.contains=function(a,c){return-1!=a.indexOf(c)};
n.string.caseInsensitiveContains=function(){return n.string.contains(n.labs.userAgent.util.getUserAgent().toLowerCase(),"webkit")};n.string.countOf=function(a,c){return a&&c?a.split(c).length-1:0};n.string.removeAt=function(a){return a};n.string.remove=function(a,c){return a.replace(c,"")};n.string.removeAll=function(a,c){c=new RegExp(n.string.regExpEscape(c),"g");return a.replace(c,"")};
n.string.replaceAll=function(a,c,d){c=new RegExp(n.string.regExpEscape(c),"g");return a.replace(c,d.replace(/\$/g,"$$$$"))};n.string.regExpEscape=function(a){return String(a).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08")};n.string.repeat=String.prototype.repeat?function(a,c){return a.repeat(c)}:function(a,c){return Array(c+1).join(a)};
n.string.padNumber=function(a,c,d){a=n.isDef(d)?a.toFixed(d):String(a);d=a.indexOf(".");-1==d&&(d=a.length);return n.string.repeat("0",Math.max(0,c-d))+a};n.string.makeSafe=function(a){return null==a?"":String(a)};n.string.buildString=function(a){return Array.prototype.join.call(arguments,"")};n.string.getRandomString=function(){return Math.floor(2147483648*Math.random()).toString(36)+Math.abs(Math.floor(2147483648*Math.random())^n.now()).toString(36)};
n.string.compareVersions=function(a,c){var d=0;a=n.string.trim(String(a)).split(".");c=n.string.trim(String(c)).split(".");for(var e=Math.max(a.length,c.length),f=0;0==d&&f<e;f++){var g=a[f]||"",h=c[f]||"";do{g=/(\d*)(\D*)(.*)/.exec(g)||["","","",""];h=/(\d*)(\D*)(.*)/.exec(h)||["","","",""];if(0==g[0].length&&0==h[0].length)break;d=n.string.compareElements_(0==g[1].length?0:parseInt(g[1],10),0==h[1].length?0:parseInt(h[1],10))||n.string.compareElements_(0==g[2].length,0==h[2].length)||n.string.compareElements_(g[2],
h[2]);g=g[3];h=h[3]}while(0==d)}return d};n.string.compareElements_=function(a,c){return a<c?-1:a>c?1:0};n.string.hashCode=function(a){for(var c=0,d=0;d<a.length;++d)c=31*c+a.charCodeAt(d)>>>0;return c};n.string.uniqueStringCounter_=2147483648*Math.random()|0;n.string.createUniqueString=function(){return"goog_"+n.string.uniqueStringCounter_++};n.string.toNumber=function(a){var c=Number(a);return 0==c&&n.string.isEmptyOrWhitespace(a)?NaN:c};n.string.isLowerCamelCase=function(a){return/^[a-z]+([A-Z][a-z]*)*$/.test(a)};
n.string.isUpperCamelCase=function(a){return/^([A-Z][a-z]*)+$/.test(a)};n.string.toCamelCase=function(a){return String(a).replace(/\-([a-z])/g,function(a,d){return d.toUpperCase()})};n.string.toSelectorCase=function(a){return String(a).replace(/([A-Z])/g,"-$1").toLowerCase()};n.string.toTitleCase=function(a,c){c=n.isString(c)?n.string.regExpEscape(c):"\\s";return a.replace(new RegExp("(^"+(c?"|["+c+"]+":"")+")([a-z])","g"),function(a,c,f){return c+f.toUpperCase()})};
n.string.capitalize=function(a){return String(a.charAt(0)).toUpperCase()+String(a.substr(1)).toLowerCase()};n.string.parseInt=function(a){isFinite(a)&&(a=String(a));return n.isString(a)?/^\s*-?0x/i.test(a)?parseInt(a,16):parseInt(a,10):NaN};n.string.splitLimit=function(a,c,d){a=a.split(c);for(var e=[];0<d&&a.length;)e.push(a.shift()),d--;a.length&&e.push(a.join(c));return e};
n.string.lastComponent=function(a,c){if(c)"string"==typeof c&&(c=[c]);else return a;for(var d=-1,e=0;e<c.length;e++)if(""!=c[e]){var f=a.lastIndexOf(c[e]);f>d&&(d=f)}return-1==d?a:a.slice(d+1)};
n.string.editDistance=function(a,c){var d=[],e=[];if(a==c)return 0;if(!a.length||!c.length)return Math.max(a.length,c.length);for(var f=0;f<c.length+1;f++)d[f]=f;for(f=0;f<a.length;f++){e[0]=f+1;for(var g=0;g<c.length;g++)e[g+1]=Math.min(e[g]+1,d[g+1]+1,d[g]+Number(a[f]!=c[g]));for(g=0;g<d.length;g++)d[g]=e[g]}return e[c.length]};n.labs={};n.labs.userAgent={};n.labs.userAgent.util={};n.labs.userAgent.util.getNativeUserAgentString_=function(){var a=n.labs.userAgent.util.getNavigator_();return a&&(a=a.userAgent)?a:""};n.labs.userAgent.util.getNavigator_=function(){return n.global.navigator};n.labs.userAgent.util.userAgent_=n.labs.userAgent.util.getNativeUserAgentString_();n.labs.userAgent.util.setUserAgent=function(a){n.labs.userAgent.util.userAgent_=a||n.labs.userAgent.util.getNativeUserAgentString_()};
n.labs.userAgent.util.getUserAgent=function(){return n.labs.userAgent.util.userAgent_};n.labs.userAgent.util.matchUserAgent=function(a){return n.string.contains(n.labs.userAgent.util.getUserAgent(),a)};n.labs.userAgent.util.matchUserAgentIgnoreCase=function(){return n.string.caseInsensitiveContains()};n.labs.userAgent.util.extractVersionTuples=function(a){for(var c=/(\w[\w ]+)\/([^\s]+)\s*(?:\((.*?)\))?/g,d=[],e;e=c.exec(a);)d.push([e[1],e[2],e[3]||void 0]);return d};n.object={};n.object.is=function(a,c){return a===c?0!==a||1/a===1/c:a!==a&&c!==c};n.object.forEach=function(a,c,d){for(var e in a)c.call(d,a[e],e,a)};n.object.filter=function(a,c,d){var e={},f;for(f in a)c.call(d,a[f],f,a)&&(e[f]=a[f]);return e};n.object.map=function(a,c,d){var e={},f;for(f in a)e[f]=c.call(d,a[f],f,a);return e};n.object.some=function(a,c,d){for(var e in a)if(c.call(d,a[e],e,a))return!0;return!1};n.object.every=function(a,c,d){for(var e in a)if(!c.call(d,a[e],e,a))return!1;return!0};
n.object.getCount=function(a){var c=0,d;for(d in a)c++;return c};n.object.getAnyKey=function(a){for(var c in a)return c};n.object.getAnyValue=function(a){for(var c in a)return a[c]};n.object.contains=function(a,c){return n.object.containsValue(a,c)};n.object.getValues=function(a){var c=[],d=0,e;for(e in a)c[d++]=a[e];return c};n.object.getKeys=function(a){var c=[],d=0,e;for(e in a)c[d++]=e;return c};
n.object.getValueByKeys=function(a,c){var d=n.isArrayLike(c),e=d?c:arguments;for(d=d?0:1;d<e.length;d++){if(null==a)return;a=a[e[d]]}return a};n.object.containsKey=function(a,c){return null!==a&&c in a};n.object.containsValue=function(a,c){for(var d in a)if(a[d]==c)return!0;return!1};n.object.findKey=function(a,c,d){for(var e in a)if(c.call(d,a[e],e,a))return e};n.object.findValue=function(a,c,d){return(c=n.object.findKey(a,c,d))&&a[c]};n.object.isEmpty=function(a){for(var c in a)return!1;return!0};
n.object.clear=function(a){for(var c in a)delete a[c]};n.object.remove=function(a,c){var d;(d=c in a)&&delete a[c];return d};n.object.add=function(a,c,d){if(null!==a&&c in a)throw Error('The object already contains the key "'+c+'"');n.object.set(a,c,d)};n.object.get=function(a,c,d){return null!==a&&c in a?a[c]:d};n.object.set=function(a,c,d){a[c]=d};n.object.setIfUndefined=function(a,c,d){return c in a?a[c]:a[c]=d};
n.object.setWithReturnValueIfNotSet=function(a,c,d){if(c in a)return a[c];d=d();return a[c]=d};n.object.equals=function(a,c){for(var d in a)if(!(d in c)||a[d]!==c[d])return!1;for(d in c)if(!(d in a))return!1;return!0};n.object.clone=function(a){var c={},d;for(d in a)c[d]=a[d];return c};n.object.unsafeClone=function(a){var c=n.typeOf(a);if("object"==c||"array"==c){if(n.isFunction(a.clone))return a.clone();c="array"==c?[]:{};for(var d in a)c[d]=n.object.unsafeClone(a[d]);return c}return a};
n.object.transpose=function(a){var c={},d;for(d in a)c[a[d]]=d;return c};n.object.PROTOTYPE_FIELDS_="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");n.object.extend=function(a,c){for(var d,e,f=1;f<arguments.length;f++){e=arguments[f];for(d in e)a[d]=e[d];for(var g=0;g<n.object.PROTOTYPE_FIELDS_.length;g++)d=n.object.PROTOTYPE_FIELDS_[g],Object.prototype.hasOwnProperty.call(e,d)&&(a[d]=e[d])}};
n.object.create=function(a){var c=arguments.length;if(1==c&&n.isArray(arguments[0]))return n.object.create.apply(null,arguments[0]);if(c%2)throw Error("Uneven number of arguments");for(var d={},e=0;e<c;e+=2)d[arguments[e]]=arguments[e+1];return d};n.object.createSet=function(a){var c=arguments.length;if(1==c&&n.isArray(arguments[0]))return n.object.createSet.apply(null,arguments[0]);for(var d={},e=0;e<c;e++)d[arguments[e]]=!0;return d};
n.object.createImmutableView=function(a){var c=a;Object.isFrozen&&!Object.isFrozen(a)&&(c=Object.create(a),Object.freeze(c));return c};n.object.isImmutableView=function(a){return!!Object.isFrozen&&Object.isFrozen(a)};
n.object.getAllPropertyNames=function(a,c,d){if(!a)return[];if(!Object.getOwnPropertyNames||!Object.getPrototypeOf)return n.object.getKeys(a);for(var e={};a&&(a!==Object.prototype||c)&&(a!==Function.prototype||d);){for(var f=Object.getOwnPropertyNames(a),g=0;g<f.length;g++)e[f[g]]=!0;a=Object.getPrototypeOf(a)}return n.object.getKeys(e)};n.labs.userAgent.browser={};n.labs.userAgent.browser.matchOpera_=function(){return n.labs.userAgent.util.matchUserAgent("Opera")};n.labs.userAgent.browser.matchIE_=function(){return n.labs.userAgent.util.matchUserAgent("Trident")||n.labs.userAgent.util.matchUserAgent("MSIE")};n.labs.userAgent.browser.matchEdge_=function(){return n.labs.userAgent.util.matchUserAgent("Edge")};n.labs.userAgent.browser.matchFirefox_=function(){return n.labs.userAgent.util.matchUserAgent("Firefox")};
n.labs.userAgent.browser.matchSafari_=function(){return n.labs.userAgent.util.matchUserAgent("Safari")&&!(n.labs.userAgent.browser.matchChrome_()||n.labs.userAgent.browser.matchCoast_()||n.labs.userAgent.browser.matchOpera_()||n.labs.userAgent.browser.matchEdge_()||n.labs.userAgent.browser.isSilk()||n.labs.userAgent.util.matchUserAgent("Android"))};n.labs.userAgent.browser.matchCoast_=function(){return n.labs.userAgent.util.matchUserAgent("Coast")};
n.labs.userAgent.browser.matchIosWebview_=function(){return(n.labs.userAgent.util.matchUserAgent("iPad")||n.labs.userAgent.util.matchUserAgent("iPhone"))&&!n.labs.userAgent.browser.matchSafari_()&&!n.labs.userAgent.browser.matchChrome_()&&!n.labs.userAgent.browser.matchCoast_()&&n.labs.userAgent.util.matchUserAgent("AppleWebKit")};n.labs.userAgent.browser.matchChrome_=function(){return(n.labs.userAgent.util.matchUserAgent("Chrome")||n.labs.userAgent.util.matchUserAgent("CriOS"))&&!n.labs.userAgent.browser.matchEdge_()};
n.labs.userAgent.browser.matchAndroidBrowser_=function(){return n.labs.userAgent.util.matchUserAgent("Android")&&!(n.labs.userAgent.browser.isChrome()||n.labs.userAgent.browser.isFirefox()||n.labs.userAgent.browser.isOpera()||n.labs.userAgent.browser.isSilk())};n.labs.userAgent.browser.isOpera=n.labs.userAgent.browser.matchOpera_;n.labs.userAgent.browser.isIE=n.labs.userAgent.browser.matchIE_;n.labs.userAgent.browser.isEdge=n.labs.userAgent.browser.matchEdge_;n.labs.userAgent.browser.isFirefox=n.labs.userAgent.browser.matchFirefox_;
n.labs.userAgent.browser.isSafari=n.labs.userAgent.browser.matchSafari_;n.labs.userAgent.browser.isCoast=n.labs.userAgent.browser.matchCoast_;n.labs.userAgent.browser.isIosWebview=n.labs.userAgent.browser.matchIosWebview_;n.labs.userAgent.browser.isChrome=n.labs.userAgent.browser.matchChrome_;n.labs.userAgent.browser.isAndroidBrowser=n.labs.userAgent.browser.matchAndroidBrowser_;n.labs.userAgent.browser.isSilk=function(){return n.labs.userAgent.util.matchUserAgent("Silk")};
n.labs.userAgent.browser.getVersion=function(){function a(a){a=n.array.find(a,e);return d[a]||""}var c=n.labs.userAgent.util.getUserAgent();if(n.labs.userAgent.browser.isIE())return n.labs.userAgent.browser.getIEVersion_(c);c=n.labs.userAgent.util.extractVersionTuples(c);var d={};n.array.forEach(c,function(a){d[a[0]]=a[1]});var e=n.partial(n.object.containsKey,d);return n.labs.userAgent.browser.isOpera()?a(["Version","Opera"]):n.labs.userAgent.browser.isEdge()?a(["Edge"]):n.labs.userAgent.browser.isChrome()?
a(["Chrome","CriOS"]):(c=c[2])&&c[1]||""};n.labs.userAgent.browser.isVersionOrHigher=function(a){return 0<=n.string.compareVersions(n.labs.userAgent.browser.getVersion(),a)};
n.labs.userAgent.browser.getIEVersion_=function(a){var c=/rv: *([\d\.]*)/.exec(a);if(c&&c[1])return c[1];c="";var d=/MSIE +([\d\.]+)/.exec(a);if(d&&d[1])if(a=/Trident\/(\d.\d)/.exec(a),"7.0"==d[1])if(a&&a[1])switch(a[1]){case "4.0":c="8.0";break;case "5.0":c="9.0";break;case "6.0":c="10.0";break;case "7.0":c="11.0"}else c="7.0";else c=d[1];return c};n.labs.userAgent.engine={};n.labs.userAgent.engine.isPresto=function(){return n.labs.userAgent.util.matchUserAgent("Presto")};n.labs.userAgent.engine.isTrident=function(){return n.labs.userAgent.util.matchUserAgent("Trident")||n.labs.userAgent.util.matchUserAgent("MSIE")};n.labs.userAgent.engine.isEdge=function(){return n.labs.userAgent.util.matchUserAgent("Edge")};n.labs.userAgent.engine.isWebKit=function(){return n.labs.userAgent.util.matchUserAgentIgnoreCase()&&!n.labs.userAgent.engine.isEdge()};
n.labs.userAgent.engine.isGecko=function(){return n.labs.userAgent.util.matchUserAgent("Gecko")&&!n.labs.userAgent.engine.isWebKit()&&!n.labs.userAgent.engine.isTrident()&&!n.labs.userAgent.engine.isEdge()};
n.labs.userAgent.engine.getVersion=function(){var a=n.labs.userAgent.util.getUserAgent();if(a){a=n.labs.userAgent.util.extractVersionTuples(a);var c=n.labs.userAgent.engine.getEngineTuple_(a);if(c)return"Gecko"==c[0]?n.labs.userAgent.engine.getVersionForKey_(a):c[1];a=a[0];var d;if(a&&(d=a[2])&&(d=/Trident\/([^\s;]+)/.exec(d)))return d[1]}return""};
n.labs.userAgent.engine.getEngineTuple_=function(a){if(!n.labs.userAgent.engine.isEdge())return a[1];for(var c=0;c<a.length;c++){var d=a[c];if("Edge"==d[0])return d}};n.labs.userAgent.engine.isVersionOrHigher=function(a){return 0<=n.string.compareVersions(n.labs.userAgent.engine.getVersion(),a)};n.labs.userAgent.engine.getVersionForKey_=function(a){return(a=n.array.find(a,function(a){return"Firefox"==a[0]}))&&a[1]||""};n.labs.userAgent.platform={};n.labs.userAgent.platform.isAndroid=function(){return n.labs.userAgent.util.matchUserAgent("Android")};n.labs.userAgent.platform.isIpod=function(){return n.labs.userAgent.util.matchUserAgent("iPod")};n.labs.userAgent.platform.isIphone=function(){return n.labs.userAgent.util.matchUserAgent("iPhone")&&!n.labs.userAgent.util.matchUserAgent("iPod")&&!n.labs.userAgent.util.matchUserAgent("iPad")};n.labs.userAgent.platform.isIpad=function(){return n.labs.userAgent.util.matchUserAgent("iPad")};
n.labs.userAgent.platform.isIos=function(){return n.labs.userAgent.platform.isIphone()||n.labs.userAgent.platform.isIpad()||n.labs.userAgent.platform.isIpod()};n.labs.userAgent.platform.isMacintosh=function(){return n.labs.userAgent.util.matchUserAgent("Macintosh")};n.labs.userAgent.platform.isLinux=function(){return n.labs.userAgent.util.matchUserAgent("Linux")};n.labs.userAgent.platform.isWindows=function(){return n.labs.userAgent.util.matchUserAgent("Windows")};
n.labs.userAgent.platform.isChromeOS=function(){return n.labs.userAgent.util.matchUserAgent("CrOS")};n.labs.userAgent.platform.isChromecast=function(){return n.labs.userAgent.util.matchUserAgent("CrKey")};
n.labs.userAgent.platform.getVersion=function(){var a=n.labs.userAgent.util.getUserAgent(),c="";n.labs.userAgent.platform.isWindows()?(c=/Windows (?:NT|Phone) ([0-9.]+)/,c=(a=c.exec(a))?a[1]:"0.0"):n.labs.userAgent.platform.isIos()?(c=/(?:iPhone|iPod|iPad|CPU)\s+OS\s+(\S+)/,c=(a=c.exec(a))&&a[1].replace(/_/g,".")):n.labs.userAgent.platform.isMacintosh()?(c=/Mac OS X ([0-9_.]+)/,c=(a=c.exec(a))?a[1].replace(/_/g,"."):"10"):n.labs.userAgent.platform.isAndroid()?(c=/Android\s+([^\);]+)(\)|;)/,c=(a=c.exec(a))&&
a[1]):n.labs.userAgent.platform.isChromeOS()&&(c=/(?:CrOS\s+(?:i686|x86_64)\s+([0-9.]+))/,c=(a=c.exec(a))&&a[1]);return c||""};n.labs.userAgent.platform.isVersionOrHigher=function(a){return 0<=n.string.compareVersions(n.labs.userAgent.platform.getVersion(),a)};n.reflect={};n.reflect.object=function(a,c){return c};n.reflect.objectProperty=function(a){return a};n.reflect.sinkValue=function(a){n.reflect.sinkValue[" "](a);return a};n.reflect.sinkValue[" "]=n.nullFunction;n.reflect.canAccessProperty=function(a){try{return n.reflect.sinkValue(a.nodeName),!0}catch(c){}return!1};n.reflect.cache=function(a,c,d,e){e=e?e(c):c;return Object.prototype.hasOwnProperty.call(a,e)?a[e]:a[e]=d(c)};n.userAgent={};n.userAgent.ASSUME_IE=!1;n.userAgent.ASSUME_EDGE=!1;n.userAgent.ASSUME_GECKO=!1;n.userAgent.ASSUME_WEBKIT=!1;n.userAgent.ASSUME_MOBILE_WEBKIT=!1;n.userAgent.ASSUME_OPERA=!1;n.userAgent.ASSUME_ANY_VERSION=!1;n.userAgent.BROWSER_KNOWN_=n.userAgent.ASSUME_IE||n.userAgent.ASSUME_EDGE||n.userAgent.ASSUME_GECKO||n.userAgent.ASSUME_MOBILE_WEBKIT||n.userAgent.ASSUME_WEBKIT||n.userAgent.ASSUME_OPERA;n.userAgent.getUserAgentString=function(){return n.labs.userAgent.util.getUserAgent()};
n.userAgent.getNavigatorTyped=function(){return n.global.navigator||null};n.userAgent.getNavigator=function(){return n.userAgent.getNavigatorTyped()};n.userAgent.OPERA=n.userAgent.BROWSER_KNOWN_?n.userAgent.ASSUME_OPERA:n.labs.userAgent.browser.isOpera();n.userAgent.IE=n.userAgent.BROWSER_KNOWN_?n.userAgent.ASSUME_IE:n.labs.userAgent.browser.isIE();n.userAgent.EDGE=n.userAgent.BROWSER_KNOWN_?n.userAgent.ASSUME_EDGE:n.labs.userAgent.engine.isEdge();n.userAgent.EDGE_OR_IE=n.userAgent.EDGE||n.userAgent.IE;
n.userAgent.GECKO=n.userAgent.BROWSER_KNOWN_?n.userAgent.ASSUME_GECKO:n.labs.userAgent.engine.isGecko();n.userAgent.WEBKIT=n.userAgent.BROWSER_KNOWN_?n.userAgent.ASSUME_WEBKIT||n.userAgent.ASSUME_MOBILE_WEBKIT:n.labs.userAgent.engine.isWebKit();n.userAgent.isMobile_=function(){return n.userAgent.WEBKIT&&n.labs.userAgent.util.matchUserAgent("Mobile")};n.userAgent.MOBILE=n.userAgent.ASSUME_MOBILE_WEBKIT||n.userAgent.isMobile_();n.userAgent.SAFARI=n.userAgent.WEBKIT;
n.userAgent.determinePlatform_=function(){var a=n.userAgent.getNavigatorTyped();return a&&a.platform||""};n.userAgent.PLATFORM=n.userAgent.determinePlatform_();n.userAgent.ASSUME_MAC=!1;n.userAgent.ASSUME_WINDOWS=!1;n.userAgent.ASSUME_LINUX=!1;n.userAgent.ASSUME_X11=!1;n.userAgent.ASSUME_ANDROID=!1;n.userAgent.ASSUME_IPHONE=!1;n.userAgent.ASSUME_IPAD=!1;n.userAgent.ASSUME_IPOD=!1;
n.userAgent.PLATFORM_KNOWN_=n.userAgent.ASSUME_MAC||n.userAgent.ASSUME_WINDOWS||n.userAgent.ASSUME_LINUX||n.userAgent.ASSUME_X11||n.userAgent.ASSUME_ANDROID||n.userAgent.ASSUME_IPHONE||n.userAgent.ASSUME_IPAD||n.userAgent.ASSUME_IPOD;n.userAgent.MAC=n.userAgent.PLATFORM_KNOWN_?n.userAgent.ASSUME_MAC:n.labs.userAgent.platform.isMacintosh();n.userAgent.WINDOWS=n.userAgent.PLATFORM_KNOWN_?n.userAgent.ASSUME_WINDOWS:n.labs.userAgent.platform.isWindows();
n.userAgent.isLegacyLinux_=function(){return n.labs.userAgent.platform.isLinux()||n.labs.userAgent.platform.isChromeOS()};n.userAgent.LINUX=n.userAgent.PLATFORM_KNOWN_?n.userAgent.ASSUME_LINUX:n.userAgent.isLegacyLinux_();n.userAgent.isX11_=function(){var a=n.userAgent.getNavigatorTyped();return!!a&&n.string.contains(a.appVersion||"","X11")};n.userAgent.X11=n.userAgent.PLATFORM_KNOWN_?n.userAgent.ASSUME_X11:n.userAgent.isX11_();
n.userAgent.ANDROID=n.userAgent.PLATFORM_KNOWN_?n.userAgent.ASSUME_ANDROID:n.labs.userAgent.platform.isAndroid();n.userAgent.IPHONE=n.userAgent.PLATFORM_KNOWN_?n.userAgent.ASSUME_IPHONE:n.labs.userAgent.platform.isIphone();n.userAgent.IPAD=n.userAgent.PLATFORM_KNOWN_?n.userAgent.ASSUME_IPAD:n.labs.userAgent.platform.isIpad();n.userAgent.IPOD=n.userAgent.PLATFORM_KNOWN_?n.userAgent.ASSUME_IPOD:n.labs.userAgent.platform.isIpod();
n.userAgent.IOS=n.userAgent.PLATFORM_KNOWN_?n.userAgent.ASSUME_IPHONE||n.userAgent.ASSUME_IPAD||n.userAgent.ASSUME_IPOD:n.labs.userAgent.platform.isIos();n.userAgent.determineVersion_=function(){var a="",c=n.userAgent.getVersionRegexResult_();c&&(a=c?c[1]:"");return n.userAgent.IE&&(c=n.userAgent.getDocumentMode_(),null!=c&&c>parseFloat(a))?String(c):a};
n.userAgent.getVersionRegexResult_=function(){var a=n.userAgent.getUserAgentString();if(n.userAgent.GECKO)return/rv:([^\);]+)(\)|;)/.exec(a);if(n.userAgent.EDGE)return/Edge\/([\d\.]+)/.exec(a);if(n.userAgent.IE)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(n.userAgent.WEBKIT)return/WebKit\/(\S+)/.exec(a);if(n.userAgent.OPERA)return/(?:Version)[ \/]?(\S+)/.exec(a)};n.userAgent.getDocumentMode_=function(){var a=n.global.document;return a?a.documentMode:void 0};n.userAgent.VERSION=n.userAgent.determineVersion_();
n.userAgent.compare=function(a,c){return n.string.compareVersions(a,c)};n.userAgent.isVersionOrHigherCache_={};n.userAgent.isVersionOrHigher=function(a){return n.userAgent.ASSUME_ANY_VERSION||n.reflect.cache(n.userAgent.isVersionOrHigherCache_,a,function(){return 0<=n.string.compareVersions(n.userAgent.VERSION,a)})};n.userAgent.isVersion=n.userAgent.isVersionOrHigher;n.userAgent.isDocumentModeOrHigher=function(a){return Number(n.userAgent.DOCUMENT_MODE)>=a};n.userAgent.isDocumentMode=n.userAgent.isDocumentModeOrHigher;
var v;var w=n.global.document;v=w&&n.userAgent.IE?n.userAgent.getDocumentMode_()||("CSS1Compat"==w.compatMode?parseInt(n.userAgent.VERSION,10):5):void 0;n.userAgent.DOCUMENT_MODE=v;n.debug.LOGGING_ENABLED=n.DEBUG;n.debug.FORCE_SLOPPY_STACKS=!1;n.debug.catchErrors=function(a,c,d){d=d||n.global;var e=d.onerror,f=!!c;n.userAgent.WEBKIT&&!n.userAgent.isVersionOrHigher("535.3")&&(f=!f);d.onerror=function(c,d,k,m,p){e&&e(c,d,k,m,p);a({message:c,fileName:d,line:k,lineNumber:k,col:m,error:p});return f}};
n.debug.expose=function(a,c){if("undefined"==typeof a)return"undefined";if(null==a)return"NULL";var d=[],e;for(e in a)if(c||!n.isFunction(a[e])){var f=e+" = ";try{f+=a[e]}catch(g){f+="*** "+g+" ***"}d.push(f)}return d.join("\n")};
n.debug.deepExpose=function(a,c){var d=[],e=[],f={},g=function(a,k){var h=k+"  ";try{if(n.isDef(a))if(n.isNull(a))d.push("NULL");else if(n.isString(a))d.push('"'+a.replace(/\n/g,"\n"+k)+'"');else if(n.isFunction(a))d.push(String(a).replace(/\n/g,"\n"+k));else if(n.isObject(a)){n.hasUid(a)||e.push(a);var p=n.getUid(a);if(f[p])d.push("*** reference loop detected (id="+p+") ***");else{f[p]=!0;d.push("{");for(var r in a)if(c||!n.isFunction(a[r]))d.push("\n"),d.push(h),d.push(r+" = "),g(a[r],h);d.push("\n"+
k+"}");delete f[p]}}else d.push(a);else d.push("undefined")}catch(N){d.push("*** "+N+" ***")}};g(a,"");for(a=0;a<e.length;a++)n.removeUid(e[a]);return d.join("")};n.debug.exposeArray=function(a){for(var c=[],d=0;d<a.length;d++)n.isArray(a[d])?c.push(n.debug.exposeArray(a[d])):c.push(a[d]);return"[ "+c.join(", ")+" ]"};
n.debug.normalizeErrorObject=function(a){var c=n.getObjectByName("window.location.href");if(n.isString(a))return{message:a,name:"Unknown error",lineNumber:"Not available",fileName:c,stack:"Not available"};var d=!1;try{var e=a.lineNumber||a.line||"Not available"}catch(g){e="Not available",d=!0}try{var f=a.fileName||a.filename||a.sourceURL||n.global.$googDebugFname||c}catch(g){f="Not available",d=!0}return!d&&a.lineNumber&&a.fileName&&a.stack&&a.message&&a.name?a:{message:a.message||"Not available",
name:a.name||"UnknownError",lineNumber:e,fileName:f,stack:a.stack||"Not available"}};n.debug.enhanceError=function(a,c){a instanceof Error||(a=Error(a),Error.captureStackTrace&&Error.captureStackTrace(a,n.debug.enhanceError));a.stack||(a.stack=n.debug.getStacktrace(n.debug.enhanceError));if(c){for(var d=0;a["message"+d];)++d;a["message"+d]=String(c)}return a};
n.debug.enhanceErrorWithContext=function(a,c){a=n.debug.enhanceError(a);if(c)for(var d in c)n.debug.errorcontext.addErrorContext(a,d,c[d]);return a};
n.debug.getStacktraceSimple=function(a){if(!n.debug.FORCE_SLOPPY_STACKS){var c=n.debug.getNativeStackTrace_(n.debug.getStacktraceSimple);if(c)return c}c=[];for(var d=arguments.callee.caller,e=0;d&&(!a||e<a);){c.push(n.debug.getFunctionName(d));c.push("()\n");try{d=d.caller}catch(f){c.push("[exception trying to get caller]\n");break}e++;if(e>=n.debug.MAX_STACK_DEPTH){c.push("[...long stack...]");break}}a&&e>=a?c.push("[...reached max depth limit...]"):c.push("[end]");return c.join("")};
n.debug.MAX_STACK_DEPTH=50;n.debug.getNativeStackTrace_=function(a){var c=Error();if(Error.captureStackTrace)return Error.captureStackTrace(c,a),String(c.stack);try{throw c;}catch(d){c=d}return(a=c.stack)?String(a):null};n.debug.getStacktrace=function(a){var c;n.debug.FORCE_SLOPPY_STACKS||(c=n.debug.getNativeStackTrace_(a||n.debug.getStacktrace));c||(c=n.debug.getStacktraceHelper_(a||arguments.callee.caller,[]));return c};
n.debug.getStacktraceHelper_=function(a,c){var d=[];if(n.array.contains(c,a))d.push("[...circular reference...]");else if(a&&c.length<n.debug.MAX_STACK_DEPTH){d.push(n.debug.getFunctionName(a)+"(");for(var e=a.arguments,f=0;e&&f<e.length;f++){0<f&&d.push(", ");var g=e[f];switch(typeof g){case "object":g=g?"object":"null";break;case "string":break;case "number":g=String(g);break;case "boolean":g=g?"true":"false";break;case "function":g=(g=n.debug.getFunctionName(g))?g:"[fn]";break;default:g=typeof g}40<
g.length&&(g=g.substr(0,40)+"...");d.push(g)}c.push(a);d.push(")\n");try{d.push(n.debug.getStacktraceHelper_(a.caller,c))}catch(h){d.push("[exception trying to get caller]\n")}}else a?d.push("[...long stack...]"):d.push("[end]");return d.join("")};n.debug.setFunctionResolver=function(a){n.debug.fnNameResolver_=a};
n.debug.getFunctionName=function(a){if(n.debug.fnNameCache_[a])return n.debug.fnNameCache_[a];if(n.debug.fnNameResolver_){var c=n.debug.fnNameResolver_(a);if(c)return n.debug.fnNameCache_[a]=c}a=String(a);n.debug.fnNameCache_[a]||(c=/function ([^\(]+)/.exec(a),n.debug.fnNameCache_[a]=c?c[1]:"[Anonymous]");return n.debug.fnNameCache_[a]};n.debug.makeWhitespaceVisible=function(a){return a.replace(/ /g,"[_]").replace(/\f/g,"[f]").replace(/\n/g,"[n]\n").replace(/\r/g,"[r]").replace(/\t/g,"[t]")};
n.debug.runtimeType=function(a){return a instanceof Function?a.displayName||a.name||"unknown type name":a instanceof Object?a.constructor.displayName||a.constructor.name||Object.prototype.toString.call(a):null===a?"null":typeof a};n.debug.fnNameCache_={};n.debug.freezeInternal_=n.DEBUG&&Object.freeze||function(a){return a};n.debug.freeze=function(a){return n.debug.freezeInternal_(a)};n.debug.entryPointRegistry={};n.debug.EntryPointMonitor=function(){};n.debug.entryPointRegistry.refList_=[];n.debug.entryPointRegistry.monitors_=[];n.debug.entryPointRegistry.monitorsMayExist_=!1;n.debug.entryPointRegistry.register=function(a){n.debug.entryPointRegistry.refList_[n.debug.entryPointRegistry.refList_.length]=a;if(n.debug.entryPointRegistry.monitorsMayExist_)for(var c=n.debug.entryPointRegistry.monitors_,d=0;d<c.length;d++)a(n.bind(c[d].wrap,c[d]))};
n.debug.entryPointRegistry.monitorAll=function(a){n.debug.entryPointRegistry.monitorsMayExist_=!0;for(var c=n.bind(a.wrap,a),d=0;d<n.debug.entryPointRegistry.refList_.length;d++)n.debug.entryPointRegistry.refList_[d](c);n.debug.entryPointRegistry.monitors_.push(a)};
n.debug.entryPointRegistry.unmonitorAllIfPossible=function(a){var c=n.debug.entryPointRegistry.monitors_;n.asserts.assert(a==c[c.length-1],"Only the most recent monitor can be unwrapped.");a=n.bind(a.unwrap,a);for(var d=0;d<n.debug.entryPointRegistry.refList_.length;d++)n.debug.entryPointRegistry.refList_[d](a);c.length--};n.disposable={};n.disposable.IDisposable=function(){};n.Disposable=function(){n.Disposable.MONITORING_MODE!=n.Disposable.MonitoringMode.OFF&&(n.Disposable.instances_[n.getUid(this)]=this);this.disposed_=this.disposed_;this.onDisposeCallbacks_=this.onDisposeCallbacks_};n.Disposable.MonitoringMode={OFF:0,PERMANENT:1,INTERACTIVE:2};n.Disposable.MONITORING_MODE=0;n.Disposable.INCLUDE_STACK_ON_CREATION=!0;n.Disposable.instances_={};
n.Disposable.getUndisposedObjects=function(){var a=[],c;for(c in n.Disposable.instances_)n.Disposable.instances_.hasOwnProperty(c)&&a.push(n.Disposable.instances_[Number(c)]);return a};n.Disposable.clearUndisposedObjects=function(){n.Disposable.instances_={}};n.Disposable.prototype.disposed_=!1;n.Disposable.prototype.isDisposed=function(){return this.disposed_};
n.Disposable.prototype.dispose=function(){if(!this.disposed_&&(this.disposed_=!0,this.disposeInternal(),n.Disposable.MONITORING_MODE!=n.Disposable.MonitoringMode.OFF)){var a=n.getUid(this);if(n.Disposable.MONITORING_MODE==n.Disposable.MonitoringMode.PERMANENT&&!n.Disposable.instances_.hasOwnProperty(a))throw Error(this+" did not call the goog.Disposable base constructor or was disposed of after a clearUndisposedObjects call");if(n.Disposable.MONITORING_MODE!=n.Disposable.MonitoringMode.OFF&&this.onDisposeCallbacks_&&
0<this.onDisposeCallbacks_.length)throw Error(this+" did not empty its onDisposeCallbacks queue. This probably means it overrode dispose() or disposeInternal() without calling the superclass' method.");delete n.Disposable.instances_[a]}};n.Disposable.prototype.disposeInternal=function(){if(this.onDisposeCallbacks_)for(;this.onDisposeCallbacks_.length;)this.onDisposeCallbacks_.shift()()};n.Disposable.isDisposed=function(){return!1};n.dispose=function(a){a&&"function"==typeof a.dispose&&a.dispose()};
n.disposeAll=function(a){for(var c=0,d=arguments.length;c<d;++c){var e=arguments[c];n.isArrayLike(e)?n.disposeAll.apply(null,e):n.dispose(e)}};n.events={};l.scope.purify=function(a){return a()};
n.events.BrowserFeature={HAS_W3C_BUTTON:!n.userAgent.IE||n.userAgent.isDocumentModeOrHigher(9),HAS_W3C_EVENT_SUPPORT:!n.userAgent.IE||n.userAgent.isDocumentModeOrHigher(9),SET_KEY_CODE_TO_PREVENT_DEFAULT:n.userAgent.IE&&!n.userAgent.isVersionOrHigher("9"),HAS_NAVIGATOR_ONLINE_PROPERTY:!n.userAgent.WEBKIT||n.userAgent.isVersionOrHigher("528"),HAS_HTML5_NETWORK_EVENT_SUPPORT:n.userAgent.GECKO&&n.userAgent.isVersionOrHigher("1.9b")||n.userAgent.IE&&n.userAgent.isVersionOrHigher("8")||n.userAgent.OPERA&&
n.userAgent.isVersionOrHigher("9.5")||n.userAgent.WEBKIT&&n.userAgent.isVersionOrHigher("528"),HTML5_NETWORK_EVENTS_FIRE_ON_BODY:n.userAgent.GECKO&&!n.userAgent.isVersionOrHigher("8")||n.userAgent.IE&&!n.userAgent.isVersionOrHigher("9"),TOUCH_ENABLED:"ontouchstart"in n.global||!!(n.global.document&&document.documentElement&&"ontouchstart"in document.documentElement)||!(!n.global.navigator||!n.global.navigator.maxTouchPoints&&!n.global.navigator.msMaxTouchPoints),POINTER_EVENTS:"PointerEvent"in n.global,
MSPOINTER_EVENTS:"MSPointerEvent"in n.global&&!(!n.global.navigator||!n.global.navigator.msPointerEnabled),PASSIVE_EVENTS:(0,l.scope.purify)(function(){if(!n.global.addEventListener||!Object.defineProperty)return!1;var a=!1,c=Object.defineProperty({},"passive",{get:function(){a=!0}});n.global.addEventListener("test",n.nullFunction,c);n.global.removeEventListener("test",n.nullFunction,c);return a})};n.events.EventId=function(a){this.id=a};n.events.EventId.prototype.toString=function(){return this.id};n.events.Event=function(a,c){this.type=a instanceof n.events.EventId?String(a):a;this.currentTarget=this.target=c;this.defaultPrevented=this.propagationStopped_=!1;this.returnValue_=!0};n.events.Event.prototype.stopPropagation=function(){this.propagationStopped_=!0};n.events.Event.prototype.preventDefault=function(){this.defaultPrevented=!0;this.returnValue_=!1};n.events.Event.stopPropagation=function(a){a.stopPropagation()};n.events.Event.preventDefault=function(a){a.preventDefault()};n.events.getVendorPrefixedName_=function(a){return n.userAgent.WEBKIT?"webkit"+a:n.userAgent.OPERA?"o"+a.toLowerCase():a.toLowerCase()};
n.events.EventType={CLICK:"click",RIGHTCLICK:"rightclick",DBLCLICK:"dblclick",MOUSEDOWN:"mousedown",MOUSEUP:"mouseup",MOUSEOVER:"mouseover",MOUSEOUT:"mouseout",MOUSEMOVE:"mousemove",MOUSEENTER:"mouseenter",MOUSELEAVE:"mouseleave",SELECTIONCHANGE:"selectionchange",SELECTSTART:"selectstart",WHEEL:"wheel",KEYPRESS:"keypress",KEYDOWN:"keydown",KEYUP:"keyup",BLUR:"blur",FOCUS:"focus",DEACTIVATE:"deactivate",FOCUSIN:"focusin",FOCUSOUT:"focusout",CHANGE:"change",RESET:"reset",SELECT:"select",SUBMIT:"submit",
INPUT:"input",PROPERTYCHANGE:"propertychange",DRAGSTART:"dragstart",DRAG:"drag",DRAGENTER:"dragenter",DRAGOVER:"dragover",DRAGLEAVE:"dragleave",DROP:"drop",DRAGEND:"dragend",TOUCHSTART:"touchstart",TOUCHMOVE:"touchmove",TOUCHEND:"touchend",TOUCHCANCEL:"touchcancel",BEFOREUNLOAD:"beforeunload",CONSOLEMESSAGE:"consolemessage",CONTEXTMENU:"contextmenu",DEVICECHANGE:"devicechange",DEVICEMOTION:"devicemotion",DEVICEORIENTATION:"deviceorientation",DOMCONTENTLOADED:"DOMContentLoaded",ERROR:"error",HELP:"help",
LOAD:"load",LOSECAPTURE:"losecapture",ORIENTATIONCHANGE:"orientationchange",READYSTATECHANGE:"readystatechange",RESIZE:"resize",SCROLL:"scroll",UNLOAD:"unload",CANPLAY:"canplay",CANPLAYTHROUGH:"canplaythrough",DURATIONCHANGE:"durationchange",EMPTIED:"emptied",ENDED:"ended",LOADEDDATA:"loadeddata",LOADEDMETADATA:"loadedmetadata",PAUSE:"pause",PLAY:"play",PLAYING:"playing",RATECHANGE:"ratechange",SEEKED:"seeked",SEEKING:"seeking",STALLED:"stalled",SUSPEND:"suspend",TIMEUPDATE:"timeupdate",VOLUMECHANGE:"volumechange",
WAITING:"waiting",SOURCEOPEN:"sourceopen",SOURCEENDED:"sourceended",SOURCECLOSED:"sourceclosed",ABORT:"abort",UPDATE:"update",UPDATESTART:"updatestart",UPDATEEND:"updateend",HASHCHANGE:"hashchange",PAGEHIDE:"pagehide",PAGESHOW:"pageshow",POPSTATE:"popstate",COPY:"copy",PASTE:"paste",CUT:"cut",BEFORECOPY:"beforecopy",BEFORECUT:"beforecut",BEFOREPASTE:"beforepaste",ONLINE:"online",OFFLINE:"offline",MESSAGE:"message",CONNECT:"connect",INSTALL:"install",ACTIVATE:"activate",FETCH:"fetch",FOREIGNFETCH:"foreignfetch",
MESSAGEERROR:"messageerror",STATECHANGE:"statechange",UPDATEFOUND:"updatefound",CONTROLLERCHANGE:"controllerchange",ANIMATIONSTART:n.events.getVendorPrefixedName_("AnimationStart"),ANIMATIONEND:n.events.getVendorPrefixedName_("AnimationEnd"),ANIMATIONITERATION:n.events.getVendorPrefixedName_("AnimationIteration"),TRANSITIONEND:n.events.getVendorPrefixedName_("TransitionEnd"),POINTERDOWN:"pointerdown",POINTERUP:"pointerup",POINTERCANCEL:"pointercancel",POINTERMOVE:"pointermove",POINTEROVER:"pointerover",
POINTEROUT:"pointerout",POINTERENTER:"pointerenter",POINTERLEAVE:"pointerleave",GOTPOINTERCAPTURE:"gotpointercapture",LOSTPOINTERCAPTURE:"lostpointercapture",MSGESTURECHANGE:"MSGestureChange",MSGESTUREEND:"MSGestureEnd",MSGESTUREHOLD:"MSGestureHold",MSGESTURESTART:"MSGestureStart",MSGESTURETAP:"MSGestureTap",MSGOTPOINTERCAPTURE:"MSGotPointerCapture",MSINERTIASTART:"MSInertiaStart",MSLOSTPOINTERCAPTURE:"MSLostPointerCapture",MSPOINTERCANCEL:"MSPointerCancel",MSPOINTERDOWN:"MSPointerDown",MSPOINTERENTER:"MSPointerEnter",
MSPOINTERHOVER:"MSPointerHover",MSPOINTERLEAVE:"MSPointerLeave",MSPOINTERMOVE:"MSPointerMove",MSPOINTEROUT:"MSPointerOut",MSPOINTEROVER:"MSPointerOver",MSPOINTERUP:"MSPointerUp",TEXT:"text",TEXTINPUT:n.userAgent.IE?"textinput":"textInput",COMPOSITIONSTART:"compositionstart",COMPOSITIONUPDATE:"compositionupdate",COMPOSITIONEND:"compositionend",BEFOREINPUT:"beforeinput",EXIT:"exit",LOADABORT:"loadabort",LOADCOMMIT:"loadcommit",LOADREDIRECT:"loadredirect",LOADSTART:"loadstart",LOADSTOP:"loadstop",RESPONSIVE:"responsive",
SIZECHANGED:"sizechanged",UNRESPONSIVE:"unresponsive",VISIBILITYCHANGE:"visibilitychange",STORAGE:"storage",DOMSUBTREEMODIFIED:"DOMSubtreeModified",DOMNODEINSERTED:"DOMNodeInserted",DOMNODEREMOVED:"DOMNodeRemoved",DOMNODEREMOVEDFROMDOCUMENT:"DOMNodeRemovedFromDocument",DOMNODEINSERTEDINTODOCUMENT:"DOMNodeInsertedIntoDocument",DOMATTRMODIFIED:"DOMAttrModified",DOMCHARACTERDATAMODIFIED:"DOMCharacterDataModified",BEFOREPRINT:"beforeprint",AFTERPRINT:"afterprint",BEFOREINSTALLPROMPT:"beforeinstallprompt",
APPINSTALLED:"appinstalled"};n.events.getPointerFallbackEventName_=function(a,c,d){return n.events.BrowserFeature.POINTER_EVENTS?a:n.events.BrowserFeature.MSPOINTER_EVENTS?c:d};
n.events.PointerFallbackEventType={POINTERDOWN:n.events.getPointerFallbackEventName_(n.events.EventType.POINTERDOWN,n.events.EventType.MSPOINTERDOWN,n.events.EventType.MOUSEDOWN),POINTERUP:n.events.getPointerFallbackEventName_(n.events.EventType.POINTERUP,n.events.EventType.MSPOINTERUP,n.events.EventType.MOUSEUP),POINTERCANCEL:n.events.getPointerFallbackEventName_(n.events.EventType.POINTERCANCEL,n.events.EventType.MSPOINTERCANCEL,"mousecancel"),POINTERMOVE:n.events.getPointerFallbackEventName_(n.events.EventType.POINTERMOVE,
n.events.EventType.MSPOINTERMOVE,n.events.EventType.MOUSEMOVE),POINTEROVER:n.events.getPointerFallbackEventName_(n.events.EventType.POINTEROVER,n.events.EventType.MSPOINTEROVER,n.events.EventType.MOUSEOVER),POINTEROUT:n.events.getPointerFallbackEventName_(n.events.EventType.POINTEROUT,n.events.EventType.MSPOINTEROUT,n.events.EventType.MOUSEOUT),POINTERENTER:n.events.getPointerFallbackEventName_(n.events.EventType.POINTERENTER,n.events.EventType.MSPOINTERENTER,n.events.EventType.MOUSEENTER),POINTERLEAVE:n.events.getPointerFallbackEventName_(n.events.EventType.POINTERLEAVE,
n.events.EventType.MSPOINTERLEAVE,n.events.EventType.MOUSELEAVE)};n.events.BrowserEvent=function(a,c){n.events.Event.call(this,a?a.type:"");this.relatedTarget=this.currentTarget=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=this.offsetY=this.offsetX=0;this.key="";this.charCode=this.keyCode=0;this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.pointerId=0;this.pointerType="";this.event_=null;if(a){var d=this.type=a.type,e=a.changedTouches?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.currentTarget=
c;(c=a.relatedTarget)?n.userAgent.GECKO&&(n.reflect.canAccessProperty(c)||(c=null)):d==n.events.EventType.MOUSEOVER?c=a.fromElement:d==n.events.EventType.MOUSEOUT&&(c=a.toElement);this.relatedTarget=c;n.isNull(e)?(this.offsetX=n.userAgent.WEBKIT||void 0!==a.offsetX?a.offsetX:a.layerX,this.offsetY=n.userAgent.WEBKIT||void 0!==a.offsetY?a.offsetY:a.layerY,this.clientX=void 0!==a.clientX?a.clientX:a.pageX,this.clientY=void 0!==a.clientY?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||
0):(this.clientX=void 0!==e.clientX?e.clientX:e.pageX,this.clientY=void 0!==e.clientY?e.clientY:e.pageY,this.screenX=e.screenX||0,this.screenY=e.screenY||0);this.button=a.button;this.keyCode=a.keyCode||0;this.key=a.key||"";this.charCode=a.charCode||("keypress"==d?a.keyCode:0);this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.pointerId=a.pointerId||0;this.pointerType=n.events.BrowserEvent.getPointerType_(a);this.state=a.state;this.event_=a;a.defaultPrevented&&
this.preventDefault()}};n.inherits(n.events.BrowserEvent,n.events.Event);n.events.BrowserEvent.MouseButton={LEFT:0,MIDDLE:1,RIGHT:2};n.events.BrowserEvent.PointerType={MOUSE:"mouse",PEN:"pen",TOUCH:"touch"};n.events.BrowserEvent.IEButtonMap=n.debug.freeze([1,4,2]);n.events.BrowserEvent.IE_BUTTON_MAP=n.events.BrowserEvent.IEButtonMap;n.events.BrowserEvent.IE_POINTER_TYPE_MAP=n.debug.freeze({2:n.events.BrowserEvent.PointerType.TOUCH,3:n.events.BrowserEvent.PointerType.PEN,4:n.events.BrowserEvent.PointerType.MOUSE});
n.events.BrowserEvent.prototype.stopPropagation=function(){n.events.BrowserEvent.superClass_.stopPropagation.call(this);this.event_.stopPropagation?this.event_.stopPropagation():this.event_.cancelBubble=!0};
n.events.BrowserEvent.prototype.preventDefault=function(){n.events.BrowserEvent.superClass_.preventDefault.call(this);var a=this.event_;if(a.preventDefault)a.preventDefault();else if(a.returnValue=!1,n.events.BrowserFeature.SET_KEY_CODE_TO_PREVENT_DEFAULT)try{if(a.ctrlKey||112<=a.keyCode&&123>=a.keyCode)a.keyCode=-1}catch(c){}};n.events.BrowserEvent.getPointerType_=function(a){return n.isString(a.pointerType)?a.pointerType:n.events.BrowserEvent.IE_POINTER_TYPE_MAP[a.pointerType]||""};n.events.Listenable=function(){};n.events.Listenable.IMPLEMENTED_BY_PROP="closure_listenable_"+(1E6*Math.random()|0);n.events.Listenable.addImplementation=function(a){a.prototype[n.events.Listenable.IMPLEMENTED_BY_PROP]=!0};n.events.Listenable.isImplementedBy=function(a){return!(!a||!a[n.events.Listenable.IMPLEMENTED_BY_PROP])};n.events.ListenableKey=function(){};n.events.ListenableKey.counter_=0;n.events.ListenableKey.reserveKey=function(){return++n.events.ListenableKey.counter_};n.events.Listener=function(a,c,d,e,f){this.listener=a;this.proxy=null;this.src=c;this.type=d;this.capture=!!e;this.handler=f;this.key=n.events.ListenableKey.reserveKey();this.removed=this.callOnce=!1};n.events.Listener.ENABLE_MONITORING=!1;var x=function(a){a.removed=!0;a.listener=null;a.proxy=null;a.src=null;a.handler=null};n.events.ListenerMap=function(a){this.src=a;this.listeners={};this.typeCount_=0};n.events.ListenerMap.prototype.add=function(a,c,d,e,f){var g=a.toString();a=this.listeners[g];a||(a=this.listeners[g]=[],this.typeCount_++);var h=n.events.ListenerMap.findListenerIndex_(a,c,e,f);-1<h?(c=a[h],d||(c.callOnce=!1)):(c=new n.events.Listener(c,this.src,g,!!e,f),c.callOnce=d,a.push(c));return c};
n.events.ListenerMap.prototype.remove=function(a,c,d,e){a=a.toString();if(!(a in this.listeners))return!1;var f=this.listeners[a];c=n.events.ListenerMap.findListenerIndex_(f,c,d,e);return-1<c?(x(f[c]),n.array.removeAt(f,c),0==f.length&&(delete this.listeners[a],this.typeCount_--),!0):!1};var y=function(a,c){var d=c.type;if(!(d in a.listeners))return!1;var e=n.array.remove(a.listeners[d],c);e&&(x(c),0==a.listeners[d].length&&(delete a.listeners[d],a.typeCount_--));return e};
n.events.ListenerMap.prototype.removeAll=function(a){a=a&&a.toString();var c=0,d;for(d in this.listeners)if(!a||d==a){for(var e=this.listeners[d],f=0;f<e.length;f++)++c,x(e[f]);delete this.listeners[d];this.typeCount_--}return c};n.events.ListenerMap.prototype.getListeners=function(a,c){a=this.listeners[a.toString()];var d=[];if(a)for(var e=0;e<a.length;++e){var f=a[e];f.capture==c&&d.push(f)}return d};
n.events.ListenerMap.prototype.getListener=function(a,c,d,e){a=this.listeners[a.toString()];var f=-1;a&&(f=n.events.ListenerMap.findListenerIndex_(a,c,d,e));return-1<f?a[f]:null};n.events.ListenerMap.prototype.hasListener=function(a,c){var d=n.isDef(a),e=d?a.toString():"",f=n.isDef(c);return n.object.some(this.listeners,function(a){for(var g=0;g<a.length;++g)if(!(d&&a[g].type!=e||f&&a[g].capture!=c))return!0;return!1})};
n.events.ListenerMap.findListenerIndex_=function(a,c,d,e){for(var f=0;f<a.length;++f){var g=a[f];if(!g.removed&&g.listener==c&&g.capture==!!d&&g.handler==e)return f}return-1};n.events.LISTENER_MAP_PROP_="closure_lm_"+(1E6*Math.random()|0);n.events.onString_="on";n.events.onStringMap_={};n.events.CaptureSimulationMode={OFF_AND_FAIL:0,OFF_AND_SILENT:1,ON:2};n.events.CAPTURE_SIMULATION_MODE=2;n.events.listenerCountEstimate_=0;
n.events.listen=function(a,c,d,e,f){if(e&&e.once)return n.events.listenOnce(a,c,d,e,f);if(n.isArray(c)){for(var g=0;g<c.length;g++)n.events.listen(a,c[g],d,e,f);return null}d=n.events.wrapListener(d);return n.events.Listenable.isImplementedBy(a)?a.listen(c,d,n.isObject(e)?!!e.capture:!!e,f):n.events.listen_(a,c,d,!1,e,f)};
n.events.listen_=function(a,c,d,e,f,g){if(!c)throw Error("Invalid event type");var h=n.isObject(f)?!!f.capture:!!f;if(h&&!n.events.BrowserFeature.HAS_W3C_EVENT_SUPPORT){if(n.events.CAPTURE_SIMULATION_MODE==n.events.CaptureSimulationMode.OFF_AND_FAIL)return n.asserts.fail("Can not register capture listener in IE8-."),null;if(n.events.CAPTURE_SIMULATION_MODE==n.events.CaptureSimulationMode.OFF_AND_SILENT)return null}var k=n.events.getListenerMap_(a);k||(a[n.events.LISTENER_MAP_PROP_]=k=new n.events.ListenerMap(a));
d=k.add(c,d,e,h,g);if(d.proxy)return d;e=n.events.getProxy();d.proxy=e;e.src=a;e.listener=d;if(a.addEventListener)n.events.BrowserFeature.PASSIVE_EVENTS||(f=h),void 0===f&&(f=!1),a.addEventListener(c.toString(),e,f);else if(a.attachEvent)a.attachEvent(n.events.getOnString_(c.toString()),e);else if(a.addListener&&a.removeListener)n.asserts.assert("change"===c,"MediaQueryList only has a change event"),a.addListener(e);else throw Error("addEventListener and attachEvent are unavailable.");n.events.listenerCountEstimate_++;
return d};n.events.getProxy=function(){var a=n.events.handleBrowserEvent_,c=n.events.BrowserFeature.HAS_W3C_EVENT_SUPPORT?function(d){return a.call(c.src,c.listener,d)}:function(d){d=a.call(c.src,c.listener,d);if(!d)return d};return c};
n.events.listenOnce=function(a,c,d,e,f){if(n.isArray(c)){for(var g=0;g<c.length;g++)n.events.listenOnce(a,c[g],d,e,f);return null}d=n.events.wrapListener(d);return n.events.Listenable.isImplementedBy(a)?a.listenOnce(c,d,n.isObject(e)?!!e.capture:!!e,f):n.events.listen_(a,c,d,!0,e,f)};n.events.listenWithWrapper=function(a,c,d,e,f){c.listen(a,d,e,f)};
n.events.unlisten=function(a,c,d,e,f){if(n.isArray(c)){for(var g=0;g<c.length;g++)n.events.unlisten(a,c[g],d,e,f);return null}e=n.isObject(e)?!!e.capture:!!e;d=n.events.wrapListener(d);if(n.events.Listenable.isImplementedBy(a))return a.unlisten(c,d,e,f);if(!a)return!1;if(a=n.events.getListenerMap_(a))if(c=a.getListener(c,d,e,f))return n.events.unlistenByKey(c);return!1};
n.events.unlistenByKey=function(a){if(n.isNumber(a)||!a||a.removed)return!1;var c=a.src;if(n.events.Listenable.isImplementedBy(c))return c.unlistenByKey(a);var d=a.type,e=a.proxy;c.removeEventListener?c.removeEventListener(d,e,a.capture):c.detachEvent?c.detachEvent(n.events.getOnString_(d),e):c.addListener&&c.removeListener&&c.removeListener(e);n.events.listenerCountEstimate_--;(d=n.events.getListenerMap_(c))?(y(d,a),0==d.typeCount_&&(d.src=null,c[n.events.LISTENER_MAP_PROP_]=null)):x(a);return!0};
n.events.unlistenWithWrapper=function(a,c,d,e,f){c.unlisten(a,d,e,f)};n.events.removeAll=function(a,c){if(!a)return 0;if(n.events.Listenable.isImplementedBy(a))return a.eventTargetListeners_?a.eventTargetListeners_.removeAll(c):0;a=n.events.getListenerMap_(a);if(!a)return 0;var d=0;c=c&&c.toString();for(var e in a.listeners)if(!c||e==c)for(var f=a.listeners[e].concat(),g=0;g<f.length;++g)n.events.unlistenByKey(f[g])&&++d;return d};
n.events.getListeners=function(a,c){return n.events.Listenable.isImplementedBy(a)?a.getListeners(c,void 0):a?(a=n.events.getListenerMap_(a))?a.getListeners(c,void 0):[]:[]};n.events.getListener=function(a,c,d,e){d=n.events.wrapListener(d);e=!!e;return n.events.Listenable.isImplementedBy(a)?a.getListener(c,d,e,void 0):a?(a=n.events.getListenerMap_(a))?a.getListener(c,d,e,void 0):null:null};
n.events.hasListener=function(a,c,d){if(n.events.Listenable.isImplementedBy(a))return a.hasListener(c,d);a=n.events.getListenerMap_(a);return!!a&&a.hasListener(c,d)};n.events.expose=function(a){var c=[],d;for(d in a)a[d]&&a[d].id?c.push(d+" = "+a[d]+" ("+a[d].id+")"):c.push(d+" = "+a[d]);return c.join("\n")};n.events.getOnString_=function(a){return a in n.events.onStringMap_?n.events.onStringMap_[a]:n.events.onStringMap_[a]=n.events.onString_+a};
n.events.fireListeners=function(a,c,d){return n.events.Listenable.isImplementedBy(a)?a.fireListeners(c,d,void 0):n.events.fireListeners_(a,c,d,void 0)};n.events.fireListeners_=function(a,c,d,e){var f=!0;if(a=n.events.getListenerMap_(a))if(c=a.listeners[c.toString()])for(c=c.concat(),a=0;a<c.length;a++){var g=c[a];g&&g.capture==d&&!g.removed&&(g=n.events.fireListener(g,e),f=f&&!1!==g)}return f};
n.events.fireListener=function(a,c){var d=a.listener,e=a.handler||a.src;a.callOnce&&n.events.unlistenByKey(a);return d.call(e,c)};n.events.getTotalListenerCount=function(){return n.events.listenerCountEstimate_};n.events.dispatchEvent=function(a,c){n.asserts.assert(n.events.Listenable.isImplementedBy(a),"Can not use goog.events.dispatchEvent with non-goog.events.Listenable instance.");return a.dispatchEvent(c)};n.events.protectBrowserEventEntryPoint=function(a){n.events.handleBrowserEvent_=a.protectEntryPoint(n.events.handleBrowserEvent_)};
n.events.handleBrowserEvent_=function(a,c){if(a.removed)return!0;if(!n.events.BrowserFeature.HAS_W3C_EVENT_SUPPORT){var d=c||n.getObjectByName("window.event");c=new n.events.BrowserEvent(d,this);var e=!0;if(n.events.CAPTURE_SIMULATION_MODE==n.events.CaptureSimulationMode.ON){if(!n.events.isMarkedIeEvent_(d)){n.events.markIeEvent_(d);d=[];for(var f=c.currentTarget;f;f=f.parentNode)d.push(f);a=a.type;for(f=d.length-1;!c.propagationStopped_&&0<=f;f--){c.currentTarget=d[f];var g=n.events.fireListeners_(d[f],
a,!0,c);e=e&&g}for(f=0;!c.propagationStopped_&&f<d.length;f++)c.currentTarget=d[f],g=n.events.fireListeners_(d[f],a,!1,c),e=e&&g}}else e=n.events.fireListener(a,c);return e}return n.events.fireListener(a,new n.events.BrowserEvent(c,this))};n.events.markIeEvent_=function(a){var c=!1;if(0==a.keyCode)try{a.keyCode=-1;return}catch(d){c=!0}if(c||void 0==a.returnValue)a.returnValue=!0};n.events.isMarkedIeEvent_=function(a){return 0>a.keyCode||void 0!=a.returnValue};n.events.uniqueIdCounter_=0;
n.events.getUniqueId=function(a){return a+"_"+n.events.uniqueIdCounter_++};n.events.getListenerMap_=function(a){a=a[n.events.LISTENER_MAP_PROP_];return a instanceof n.events.ListenerMap?a:null};n.events.LISTENER_WRAPPER_PROP_="__closure_events_fn_"+(1E9*Math.random()>>>0);
n.events.wrapListener=function(a){n.asserts.assert(a,"Listener can not be null.");if(n.isFunction(a))return a;n.asserts.assert(a.handleEvent,"An object listener must have handleEvent method.");a[n.events.LISTENER_WRAPPER_PROP_]||(a[n.events.LISTENER_WRAPPER_PROP_]=function(c){return a.handleEvent(c)});return a[n.events.LISTENER_WRAPPER_PROP_]};n.debug.entryPointRegistry.register(function(a){n.events.handleBrowserEvent_=a(n.events.handleBrowserEvent_)});n.events.EventTarget=function(){n.Disposable.call(this);this.eventTargetListeners_=new n.events.ListenerMap(this);this.actualEventTarget_=this;this.parentEventTarget_=null};n.inherits(n.events.EventTarget,n.Disposable);n.events.Listenable.addImplementation(n.events.EventTarget);n.events.EventTarget.MAX_ANCESTORS_=1E3;b=n.events.EventTarget.prototype;b.addEventListener=function(a,c,d,e){n.events.listen(this,a,c,d,e)};b.removeEventListener=function(a,c,d,e){n.events.unlisten(this,a,c,d,e)};
b.dispatchEvent=function(a){z(this);var c=this.parentEventTarget_;if(c){var d=[];for(var e=1;c;c=c.parentEventTarget_)d.push(c),n.asserts.assert(++e<n.events.EventTarget.MAX_ANCESTORS_,"infinite loop")}return n.events.EventTarget.dispatchEventInternal_(this.actualEventTarget_,a,d)};b.disposeInternal=function(){n.events.EventTarget.superClass_.disposeInternal.call(this);this.eventTargetListeners_&&this.eventTargetListeners_.removeAll(void 0);this.parentEventTarget_=null};
b.listen=function(a,c,d,e){z(this);return this.eventTargetListeners_.add(String(a),c,!1,d,e)};b.listenOnce=function(a,c,d,e){return this.eventTargetListeners_.add(String(a),c,!0,d,e)};b.unlisten=function(a,c,d,e){return this.eventTargetListeners_.remove(String(a),c,d,e)};b.unlistenByKey=function(a){return y(this.eventTargetListeners_,a)};
b.fireListeners=function(a,c,d){a=this.eventTargetListeners_.listeners[String(a)];if(!a)return!0;a=a.concat();for(var e=!0,f=0;f<a.length;++f){var g=a[f];if(g&&!g.removed&&g.capture==c){var h=g.listener,k=g.handler||g.src;g.callOnce&&this.unlistenByKey(g);e=!1!==h.call(k,d)&&e}}return e&&0!=d.returnValue_};b.getListeners=function(a,c){return this.eventTargetListeners_.getListeners(String(a),c)};b.getListener=function(a,c,d,e){return this.eventTargetListeners_.getListener(String(a),c,d,e)};
b.hasListener=function(a,c){return this.eventTargetListeners_.hasListener(n.isDef(a)?String(a):void 0,c)};var z=function(a){n.asserts.assert(a.eventTargetListeners_,"Event target is not initialized. Did you call the superclass (goog.events.EventTarget) constructor?")};
n.events.EventTarget.dispatchEventInternal_=function(a,c,d){var e=c.type||c;if(n.isString(c))c=new n.events.Event(c,a);else if(c instanceof n.events.Event)c.target=c.target||a;else{var f=c;c=new n.events.Event(e,a);n.object.extend(c,f)}f=!0;if(d)for(var g=d.length-1;!c.propagationStopped_&&0<=g;g--){var h=c.currentTarget=d[g];f=h.fireListeners(e,!0,c)&&f}c.propagationStopped_||(h=c.currentTarget=a,f=h.fireListeners(e,!0,c)&&f,c.propagationStopped_||(f=h.fireListeners(e,!1,c)&&f));if(d)for(g=0;!c.propagationStopped_&&
g<d.length;g++)h=c.currentTarget=d[g],f=h.fireListeners(e,!1,c)&&f;return f};n.functions={};n.functions.constant=function(a){return function(){return a}};n.functions.FALSE=n.functions.constant(!1);n.functions.TRUE=n.functions.constant(!0);n.functions.NULL=n.functions.constant(null);n.functions.identity=function(a){return a};n.functions.error=function(a){return function(){throw Error(a);}};n.functions.fail=function(){};n.functions.lock=function(a,c){c=c||0;return function(){return a.apply(this,Array.prototype.slice.call(arguments,0,c))}};n.functions.nth=function(a){return function(){return arguments[a]}};
n.functions.partialRight=function(a,c){var d=Array.prototype.slice.call(arguments,1);return function(){var c=Array.prototype.slice.call(arguments);c.push.apply(c,d);return a.apply(this,c)}};n.functions.withReturnValue=function(a,c){return n.functions.sequence(a,n.functions.constant(c))};n.functions.equalTo=function(a,c){return function(d){return c?a==d:a===d}};
n.functions.compose=function(a,c){var d=arguments,e=d.length;return function(){var a;e&&(a=d[e-1].apply(this,arguments));for(var c=e-2;0<=c;c--)a=d[c].call(this,a);return a}};n.functions.sequence=function(a){var c=arguments,d=c.length;return function(){for(var a,f=0;f<d;f++)a=c[f].apply(this,arguments);return a}};n.functions.and=function(a){var c=arguments,d=c.length;return function(){for(var a=0;a<d;a++)if(!c[a].apply(this,arguments))return!1;return!0}};
n.functions.or=function(a){var c=arguments,d=c.length;return function(){for(var a=0;a<d;a++)if(c[a].apply(this,arguments))return!0;return!1}};n.functions.not=function(a){return function(){return!a.apply(this,arguments)}};n.functions.create=function(a,c){var d=function(){};d.prototype=a.prototype;d=new d;a.apply(d,Array.prototype.slice.call(arguments,1));return d};n.functions.CACHE_RETURN_VALUE=!0;
n.functions.cacheReturnValue=function(a){var c=!1,d;return function(){if(!n.functions.CACHE_RETURN_VALUE)return a();c||(d=a(),c=!0);return d}};n.functions.once=function(a){var c=a;return function(){if(c){var a=c;c=null;a()}}};n.functions.debounce=function(a,c,d){var e=0;return function(f){n.global.clearTimeout(e);var g=arguments;e=n.global.setTimeout(function(){a.apply(d,g)},c)}};
n.functions.throttle=function(a,c,d){var e=0,f=!1,g=[],h=function(){e=0;f&&(f=!1,k())},k=function(){e=n.global.setTimeout(h,c);a.apply(d,g)};return function(a){g=arguments;e?f=!0:k()}};n.functions.rateLimit=function(a,c,d){var e=0,f=function(){e=0};return function(g){e||(e=n.global.setTimeout(f,c),a.apply(d,arguments))}};n.math={};n.math.randomInt=function(a){return Math.floor(Math.random()*a)};n.math.uniformRandom=function(a,c){return a+Math.random()*(c-a)};n.math.clamp=function(a,c,d){return Math.min(Math.max(a,c),d)};n.math.modulo=function(a,c){a%=c;return 0>a*c?a+c:a};n.math.lerp=function(a,c,d){return a+d*(c-a)};n.math.nearlyEquals=function(a,c,d){return Math.abs(a-c)<=(d||1E-6)};n.math.standardAngle=function(a){return n.math.modulo(a,360)};n.math.standardAngleInRadians=function(a){return n.math.modulo(a,2*Math.PI)};
n.math.toRadians=function(a){return a*Math.PI/180};n.math.toDegrees=function(a){return 180*a/Math.PI};n.math.angleDx=function(a,c){return c*Math.cos(n.math.toRadians(a))};n.math.angleDy=function(a,c){return c*Math.sin(n.math.toRadians(a))};n.math.angle=function(a,c,d,e){return n.math.standardAngle(n.math.toDegrees(Math.atan2(e-c,d-a)))};n.math.angleDifference=function(a,c){a=n.math.standardAngle(c)-n.math.standardAngle(a);180<a?a-=360:-180>=a&&(a=360+a);return a};
n.math.sign=function(a){return 0<a?1:0>a?-1:a};n.math.longestCommonSubsequence=function(a,c,d,e){d=d||function(a,c){return a==c};e=e||function(c){return a[c]};for(var f=a.length,g=c.length,h=[],k=0;k<f+1;k++)h[k]=[],h[k][0]=0;for(var m=0;m<g+1;m++)h[0][m]=0;for(k=1;k<=f;k++)for(m=1;m<=g;m++)d(a[k-1],c[m-1])?h[k][m]=h[k-1][m-1]+1:h[k][m]=Math.max(h[k-1][m],h[k][m-1]);var p=[];k=f;for(m=g;0<k&&0<m;)d(a[k-1],c[m-1])?(p.unshift(e(k-1,m-1)),k--,m--):h[k-1][m]>h[k][m-1]?k--:m--;return p};
n.math.sum=function(a){return n.array.reduce(arguments,function(a,d){return a+d},0)};n.math.average=function(a){return n.math.sum.apply(null,arguments)/arguments.length};n.math.sampleVariance=function(a){var c=arguments.length;if(2>c)return 0;var d=n.math.average.apply(null,arguments);return n.math.sum.apply(null,n.array.map(arguments,function(a){return Math.pow(a-d,2)}))/(c-1)};n.math.standardDeviation=function(a){return Math.sqrt(n.math.sampleVariance.apply(null,arguments))};
n.math.isInt=function(a){return isFinite(a)&&0==a%1};n.math.isFiniteNumber=function(a){return isFinite(a)};n.math.isNegativeZero=function(a){return 0==a&&0>1/a};n.math.log10Floor=function(a){if(0<a){var c=Math.round(Math.log(a)*Math.LOG10E);return c-(parseFloat("1e"+c)>a?1:0)}return 0==a?-Infinity:NaN};n.math.safeFloor=function(a,c){n.asserts.assert(!n.isDef(c)||0<c);return Math.floor(a+(c||2E-15))};n.math.safeCeil=function(a,c){n.asserts.assert(!n.isDef(c)||0<c);return Math.ceil(a-(c||2E-15))};n.iter={};n.iter.StopIteration="StopIteration"in n.global?n.global.StopIteration:{message:"StopIteration",stack:""};n.iter.Iterator=function(){};n.iter.Iterator.prototype.next=function(){throw n.iter.StopIteration;};n.iter.Iterator.prototype.__iterator__=function(){return this};
n.iter.toIterator=function(a){if(a instanceof n.iter.Iterator)return a;if("function"==typeof a.__iterator__)return a.__iterator__(!1);if(n.isArrayLike(a)){var c=0,d=new n.iter.Iterator;d.next=function(){for(;;){if(c>=a.length)throw n.iter.StopIteration;if(c in a)return a[c++];c++}};return d}throw Error("Not implemented");};
n.iter.forEach=function(a,c,d){if(n.isArrayLike(a))try{n.array.forEach(a,c,d)}catch(e){if(e!==n.iter.StopIteration)throw e;}else{a=n.iter.toIterator(a);try{for(;;)c.call(d,a.next(),void 0,a)}catch(e){if(e!==n.iter.StopIteration)throw e;}}};n.iter.filter=function(a,c,d){var e=n.iter.toIterator(a);a=new n.iter.Iterator;a.next=function(){for(;;){var a=e.next();if(c.call(d,a,void 0,e))return a}};return a};n.iter.filterFalse=function(a,c,d){return n.iter.filter(a,n.functions.not(c),d)};
n.iter.range=function(a,c,d){var e=0,f=a,g=d||1;1<arguments.length&&(e=a,f=+c);if(0==g)throw Error("Range step argument must not be zero");var h=new n.iter.Iterator;h.next=function(){if(0<g&&e>=f||0>g&&e<=f)throw n.iter.StopIteration;var a=e;e+=g;return a};return h};n.iter.join=function(a,c){return n.iter.toArray(a).join(c)};n.iter.map=function(a,c,d){var e=n.iter.toIterator(a);a=new n.iter.Iterator;a.next=function(){var a=e.next();return c.call(d,a,void 0,e)};return a};
n.iter.reduce=function(a,c,d,e){var f=d;n.iter.forEach(a,function(a){f=c.call(e,f,a)});return f};n.iter.some=function(a,c,d){a=n.iter.toIterator(a);try{for(;;)if(c.call(d,a.next(),void 0,a))return!0}catch(e){if(e!==n.iter.StopIteration)throw e;}return!1};n.iter.every=function(a,c,d){a=n.iter.toIterator(a);try{for(;;)if(!c.call(d,a.next(),void 0,a))return!1}catch(e){if(e!==n.iter.StopIteration)throw e;}return!0};n.iter.chain=function(a){return n.iter.chainFromIterable(arguments)};
n.iter.chainFromIterable=function(a){var c=n.iter.toIterator(a);a=new n.iter.Iterator;var d=null;a.next=function(){for(;;){if(null==d){var a=c.next();d=n.iter.toIterator(a)}try{return d.next()}catch(f){if(f!==n.iter.StopIteration)throw f;d=null}}};return a};n.iter.dropWhile=function(a,c,d){var e=n.iter.toIterator(a);a=new n.iter.Iterator;var f=!0;a.next=function(){for(;;){var a=e.next();if(!f||!c.call(d,a,void 0,e))return f=!1,a}};return a};
n.iter.takeWhile=function(a,c,d){var e=n.iter.toIterator(a);a=new n.iter.Iterator;a.next=function(){var a=e.next();if(c.call(d,a,void 0,e))return a;throw n.iter.StopIteration;};return a};n.iter.toArray=function(a){if(n.isArrayLike(a))return n.array.toArray(a);a=n.iter.toIterator(a);var c=[];n.iter.forEach(a,function(a){c.push(a)});return c};n.iter.equals=function(a,c,d){a=n.iter.zipLongest({},a,c);var e=d||n.array.defaultCompareEquality;return n.iter.every(a,function(a){return e(a[0],a[1])})};
n.iter.nextOrValue=function(a){try{n.iter.toIterator(a).next()}catch(c){if(c!=n.iter.StopIteration)throw c;}};
n.iter.product=function(a){if(n.array.some(arguments,function(a){return!a.length})||!arguments.length)return new n.iter.Iterator;var c=new n.iter.Iterator,d=arguments,e=n.array.repeat(0,d.length);c.next=function(){if(e){for(var a=n.array.map(e,function(a,c){return d[c][a]}),c=e.length-1;0<=c;c--){n.asserts.assert(e);if(e[c]<d[c].length-1){e[c]++;break}if(0==c){e=null;break}e[c]=0}return a}throw n.iter.StopIteration;};return c};
n.iter.cycle=function(a){var c=n.iter.toIterator(a),d=[],e=0;a=new n.iter.Iterator;var f=!1;a.next=function(){var a=null;if(!f)try{return a=c.next(),d.push(a),a}catch(h){if(h!=n.iter.StopIteration||n.array.isEmpty(d))throw h;f=!0}a=d[e];e=(e+1)%d.length;return a};return a};n.iter.count=function(a,c){var d=a||0,e=n.isDef(c)?c:1;a=new n.iter.Iterator;a.next=function(){var a=d;d+=e;return a};return a};n.iter.repeat=function(a){var c=new n.iter.Iterator;c.next=n.functions.constant(a);return c};
n.iter.accumulate=function(a){var c=n.iter.toIterator(a),d=0;a=new n.iter.Iterator;a.next=function(){return d+=c.next()};return a};n.iter.zip=function(a){var c=arguments,d=new n.iter.Iterator;if(0<c.length){var e=n.array.map(c,n.iter.toIterator);d.next=function(){return n.array.map(e,function(a){return a.next()})}}return d};
n.iter.zipLongest=function(a,c){var d=n.array.slice(arguments,1),e=new n.iter.Iterator;if(0<d.length){var f=n.array.map(d,n.iter.toIterator);e.next=function(){var c=!1,d=n.array.map(f,function(d){try{var e=d.next();c=!0}catch(p){if(p!==n.iter.StopIteration)throw p;e=a}return e});if(!c)throw n.iter.StopIteration;return d}}return e};n.iter.compress=function(a,c){var d=n.iter.toIterator(c);return n.iter.filter(a,function(){return!!d.next()})};
n.iter.GroupByIterator_=function(a,c){this.iterator=n.iter.toIterator(a);this.keyFunc=c||n.functions.identity};n.inherits(n.iter.GroupByIterator_,n.iter.Iterator);
n.iter.GroupByIterator_.prototype.next=function(){for(;this.currentKey==this.targetKey;)this.currentValue=this.iterator.next(),this.currentKey=this.keyFunc(this.currentValue);for(var a=this.targetKey=this.currentKey,c=this.targetKey,d=[];this.currentKey==c;){d.push(this.currentValue);try{this.currentValue=this.iterator.next()}catch(e){if(e!==n.iter.StopIteration)throw e;break}this.currentKey=this.keyFunc(this.currentValue)}return[a,d]};
n.iter.groupBy=function(a,c){return new n.iter.GroupByIterator_(a,c)};n.iter.starMap=function(a,c,d){var e=n.iter.toIterator(a);a=new n.iter.Iterator;a.next=function(){var a=n.iter.toArray(e.next());return c.apply(d,n.array.concat(a,void 0,e))};return a};
n.iter.tee=function(a,c){var d=n.iter.toIterator(a),e=n.array.map(n.array.range(n.isNumber(c)?c:2),function(){return[]}),f=function(){var a=d.next();n.array.forEach(e,function(c){c.push(a)})};return n.array.map(e,function(a){var c=new n.iter.Iterator;c.next=function(){n.array.isEmpty(a)&&f();n.asserts.assert(!n.array.isEmpty(a));return a.shift()};return c})};n.iter.enumerate=function(a,c){return n.iter.zip(n.iter.count(c),a)};
n.iter.limit=function(a,c){n.asserts.assert(n.math.isInt(c)&&0<=c);var d=n.iter.toIterator(a);a=new n.iter.Iterator;var e=c;a.next=function(){if(0<e--)return d.next();throw n.iter.StopIteration;};return a};n.iter.consume=function(a,c){n.asserts.assert(n.math.isInt(c)&&0<=c);for(a=n.iter.toIterator(a);0<c--;)n.iter.nextOrValue(a);return a};
n.iter.slice=function(a,c,d){n.asserts.assert(n.math.isInt(c)&&0<=c);a=n.iter.consume(a,c);n.isNumber(d)&&(n.asserts.assert(n.math.isInt(d)&&d>=c),a=n.iter.limit(a,d-c));return a};n.iter.hasDuplicates_=function(a){var c=[];n.array.removeDuplicates(a,c);return a.length!=c.length};n.iter.permutations=function(a,c){a=n.iter.toArray(a);c=n.array.repeat(a,n.isNumber(c)?c:a.length);c=n.iter.product.apply(void 0,c);return n.iter.filter(c,function(a){return!n.iter.hasDuplicates_(a)})};
n.iter.combinations=function(a,c){function d(a){return e[a]}var e=n.iter.toArray(a);a=n.iter.range(e.length);c=n.iter.permutations(a,c);var f=n.iter.filter(c,function(a){return n.array.isSorted(a)});c=new n.iter.Iterator;c.next=function(){return n.array.map(f.next(),d)};return c};
n.iter.combinationsWithReplacement=function(a,c){function d(a){return e[a]}var e=n.iter.toArray(a);a=n.array.range(e.length);c=n.array.repeat(a,c);c=n.iter.product.apply(void 0,c);var f=n.iter.filter(c,function(a){return n.array.isSorted(a)});c=new n.iter.Iterator;c.next=function(){return n.array.map(f.next(),d)};return c};n.structs={};n.structs.Map=function(a,c){this.map_={};this.keys_=[];this.version_=this.count_=0;var d=arguments.length;if(1<d){if(d%2)throw Error("Uneven number of arguments");for(var e=0;e<d;e+=2)this.set(arguments[e],arguments[e+1])}else a&&this.addAll(a)};b=n.structs.Map.prototype;b.getCount=function(){return this.count_};b.getValues=function(){A(this);for(var a=[],c=0;c<this.keys_.length;c++)a.push(this.map_[this.keys_[c]]);return a};b.getKeys=function(){A(this);return this.keys_.concat()};
b.containsKey=function(a){return n.structs.Map.hasKey_(this.map_,a)};b.containsValue=function(a){for(var c=0;c<this.keys_.length;c++){var d=this.keys_[c];if(n.structs.Map.hasKey_(this.map_,d)&&this.map_[d]==a)return!0}return!1};b.equals=function(a,c){if(this===a)return!0;if(this.count_!=a.getCount())return!1;c=c||n.structs.Map.defaultEquals;A(this);for(var d,e=0;d=this.keys_[e];e++)if(!c(this.get(d),a.get(d)))return!1;return!0};n.structs.Map.defaultEquals=function(a,c){return a===c};
n.structs.Map.prototype.isEmpty=function(){return 0==this.count_};n.structs.Map.prototype.clear=function(){this.map_={};this.version_=this.count_=this.keys_.length=0};n.structs.Map.prototype.remove=function(a){return n.structs.Map.hasKey_(this.map_,a)?(delete this.map_[a],this.count_--,this.version_++,this.keys_.length>2*this.count_&&A(this),!0):!1};
var A=function(a){if(a.count_!=a.keys_.length){for(var c=0,d=0;c<a.keys_.length;){var e=a.keys_[c];n.structs.Map.hasKey_(a.map_,e)&&(a.keys_[d++]=e);c++}a.keys_.length=d}if(a.count_!=a.keys_.length){var f={};for(d=c=0;c<a.keys_.length;)e=a.keys_[c],n.structs.Map.hasKey_(f,e)||(a.keys_[d++]=e,f[e]=1),c++;a.keys_.length=d}};b=n.structs.Map.prototype;b.get=function(a,c){return n.structs.Map.hasKey_(this.map_,a)?this.map_[a]:c};
b.set=function(a,c){n.structs.Map.hasKey_(this.map_,a)||(this.count_++,this.keys_.push(a),this.version_++);this.map_[a]=c};b.addAll=function(a){if(a instanceof n.structs.Map)for(var c=a.getKeys(),d=0;d<c.length;d++)this.set(c[d],a.get(c[d]));else for(c in a)this.set(c,a[c])};b.forEach=function(a,c){for(var d=this.getKeys(),e=0;e<d.length;e++){var f=d[e],g=this.get(f);a.call(c,g,f,this)}};b.clone=function(){return new n.structs.Map(this)};
b.transpose=function(){for(var a=new n.structs.Map,c=0;c<this.keys_.length;c++){var d=this.keys_[c];a.set(this.map_[d],d)}return a};b.toObject=function(){A(this);for(var a={},c=0;c<this.keys_.length;c++){var d=this.keys_[c];a[d]=this.map_[d]}return a};
b.__iterator__=function(a){A(this);var c=0,d=this.version_,e=this,f=new n.iter.Iterator;f.next=function(){if(d!=e.version_)throw Error("The map has changed since the iterator was created");if(c>=e.keys_.length)throw n.iter.StopIteration;var f=e.keys_[c++];return a?f:e.map_[f]};return f};n.structs.Map.hasKey_=function(a,c){return Object.prototype.hasOwnProperty.call(a,c)};n.structs.LinkedMap=function(a){this.maxCount_=a||null;this.cache_=!0;this.evictionCallback_=void 0;this.map_=new n.structs.Map;this.head_=new n.structs.LinkedMap.Node_("",void 0);this.head_.next=this.head_.prev=this.head_};var C=function(a,c){(c=a.map_.get(c))&&a.cache_&&(c.remove(),B(a,c));return c};b=n.structs.LinkedMap.prototype;b.get=function(a,c){return(a=C(this,a))?a.value:c};b.set=function(a,c){var d=C(this,a);d?d.value=c:(d=new n.structs.LinkedMap.Node_(a,c),this.map_.set(a,d),B(this,d))};
b.peek=function(){return this.head_.next.value};b.shift=function(){return D(this,this.head_.next)};b.pop=function(){return D(this,this.head_.prev)};b.remove=function(a){return(a=this.map_.get(a))?(this.removeNode(a),!0):!1};b.removeNode=function(a){a.remove();this.map_.remove(a.key)};b.getCount=function(){return this.map_.getCount()};b.isEmpty=function(){return this.map_.isEmpty()};b.getKeys=function(){return this.map(function(a,c){return c})};b.getValues=function(){return this.map(function(a){return a})};
b.contains=function(a){return this.some(function(c){return c==a})};b.containsKey=function(a){return this.map_.containsKey(a)};b.clear=function(){E(this,0)};b.forEach=function(a,c){for(var d=this.head_.next;d!=this.head_;d=d.next)a.call(c,d.value,d.key,this)};b.map=function(a,c){for(var d=[],e=this.head_.next;e!=this.head_;e=e.next)d.push(a.call(c,e.value,e.key,this));return d};b.some=function(a,c){for(var d=this.head_.next;d!=this.head_;d=d.next)if(a.call(c,d.value,d.key,this))return!0;return!1};
b.every=function(a,c){for(var d=this.head_.next;d!=this.head_;d=d.next)if(!a.call(c,d.value,d.key,this))return!1;return!0};
var B=function(a,c){a.cache_?(c.next=a.head_.next,c.prev=a.head_,a.head_.next=c,c.next.prev=c):(c.prev=a.head_.prev,c.next=a.head_,a.head_.prev=c,c.prev.next=c);null!=a.maxCount_&&E(a,a.maxCount_)},E=function(a,c){for(;a.getCount()>c;){var d=a.cache_?a.head_.prev:a.head_.next;a.removeNode(d);a.evictionCallback_&&a.evictionCallback_(d.key,d.value)}},D=function(a,c){a.head_!=c&&a.removeNode(c);return c.value};n.structs.LinkedMap.Node_=function(a,c){this.key=a;this.value=c};
n.structs.LinkedMap.Node_.prototype.remove=function(){this.prev.next=this.next;this.next.prev=this.prev;delete this.prev;delete this.next};u.LicensesCache=function(a,c){n.events.EventTarget.call(this);this.timeout_=a||36E5;this.cache_=new n.structs.LinkedMap(c||100)};n.inherits(u.LicensesCache,n.events.EventTarget);u.LicensesCache.Entry=function(a){this.timeStamp_=n.now();this.licenses_=a};u.LicensesCache.prototype.get=function(a){a=this.cache_.get(a);var c;if(c=a)c=this.timeout_,c=n.now()-a.timeStamp_<=c;return c?a.licenses_:null};u.LicensesCache.prototype.remove=function(a){this.cache_.remove(a)};n.structs.getCount=function(a){return a.getCount&&"function"==typeof a.getCount?a.getCount():n.isArrayLike(a)||n.isString(a)?a.length:n.object.getCount(a)};n.structs.getValues=function(a){if(a.getValues&&"function"==typeof a.getValues)return a.getValues();if(n.isString(a))return a.split("");if(n.isArrayLike(a)){for(var c=[],d=a.length,e=0;e<d;e++)c.push(a[e]);return c}return n.object.getValues(a)};
n.structs.getKeys=function(a){if(a.getKeys&&"function"==typeof a.getKeys)return a.getKeys();if(!a.getValues||"function"!=typeof a.getValues){if(n.isArrayLike(a)||n.isString(a)){var c=[];a=a.length;for(var d=0;d<a;d++)c.push(d);return c}return n.object.getKeys(a)}};
n.structs.contains=function(a,c){return a.contains&&"function"==typeof a.contains?a.contains(c):a.containsValue&&"function"==typeof a.containsValue?a.containsValue(c):n.isArrayLike(a)||n.isString(a)?n.array.contains(a,c):n.object.containsValue(a,c)};n.structs.isEmpty=function(a){return a.isEmpty&&"function"==typeof a.isEmpty?a.isEmpty():n.isArrayLike(a)||n.isString(a)?n.array.isEmpty(a):n.object.isEmpty(a)};
n.structs.clear=function(a){a.clear&&"function"==typeof a.clear?a.clear():n.isArrayLike(a)?n.array.clear(a):n.object.clear(a)};n.structs.forEach=function(a,c,d){if(a.forEach&&"function"==typeof a.forEach)a.forEach(c,d);else if(n.isArrayLike(a)||n.isString(a))n.array.forEach(a,c,d);else for(var e=n.structs.getKeys(a),f=n.structs.getValues(a),g=f.length,h=0;h<g;h++)c.call(d,f[h],e&&e[h],a)};
n.structs.filter=function(a,c,d){if("function"==typeof a.filter)return a.filter(c,d);if(n.isArrayLike(a)||n.isString(a))return n.array.filter(a,c,d);var e=n.structs.getKeys(a),f=n.structs.getValues(a),g=f.length;if(e){var h={};for(var k=0;k<g;k++)c.call(d,f[k],e[k],a)&&(h[e[k]]=f[k])}else for(h=[],k=0;k<g;k++)c.call(d,f[k],void 0,a)&&h.push(f[k]);return h};
n.structs.map=function(a,c,d){if("function"==typeof a.map)return a.map(c,d);if(n.isArrayLike(a)||n.isString(a))return n.array.map(a,c,d);var e=n.structs.getKeys(a),f=n.structs.getValues(a),g=f.length;if(e){var h={};for(var k=0;k<g;k++)h[e[k]]=c.call(d,f[k],e[k],a)}else for(h=[],k=0;k<g;k++)h[k]=c.call(d,f[k],void 0,a);return h};
n.structs.some=function(a,c,d){if("function"==typeof a.some)return a.some(c,d);if(n.isArrayLike(a)||n.isString(a))return n.array.some(a,c,d);for(var e=n.structs.getKeys(a),f=n.structs.getValues(a),g=f.length,h=0;h<g;h++)if(c.call(d,f[h],e&&e[h],a))return!0;return!1};
n.structs.every=function(a,c,d){if("function"==typeof a.every)return a.every(c,d);if(n.isArrayLike(a)||n.isString(a))return n.array.every(a,c,d);for(var e=n.structs.getKeys(a),f=n.structs.getValues(a),g=f.length,h=0;h<g;h++)if(!c.call(d,f[h],e&&e[h],a))return!1;return!0};n.uri={};n.uri.utils={};n.uri.utils.CharCode_={AMPERSAND:38,EQUAL:61,HASH:35,QUESTION:63};n.uri.utils.buildFromEncodedParts=function(a,c,d,e,f,g,h){var k="";a&&(k+=a+":");d&&(k+="//",c&&(k+=c+"@"),k+=d,e&&(k+=":"+e));f&&(k+=f);g&&(k+="?"+g);h&&(k+="#"+h);return k};n.uri.utils.splitRe_=/^(?:([^:/?#.]+):)?(?:\/\/(?:([^/?#]*)@)?([^/#?]*?)(?::([0-9]+))?(?=[/#?]|$))?([^?#]+)?(?:\?([^#]*))?(?:#([\s\S]*))?$/;n.uri.utils.ComponentIndex={SCHEME:1,USER_INFO:2,DOMAIN:3,PORT:4,PATH:5,QUERY_DATA:6,FRAGMENT:7};
n.uri.utils.split=function(a){return a.match(n.uri.utils.splitRe_)};n.uri.utils.decodeIfPossible_=function(a,c){return a?c?decodeURI(a):decodeURIComponent(a):a};n.uri.utils.getComponentByIndex_=function(a,c){return n.uri.utils.split(c)[a]||null};n.uri.utils.getScheme=function(a){return n.uri.utils.getComponentByIndex_(n.uri.utils.ComponentIndex.SCHEME,a)};
n.uri.utils.getEffectiveScheme=function(a){a=n.uri.utils.getScheme(a);!a&&n.global.self&&n.global.self.location&&(a=n.global.self.location.protocol,a=a.substr(0,a.length-1));return a?a.toLowerCase():""};n.uri.utils.getUserInfoEncoded=function(){return n.uri.utils.getComponentByIndex_(n.uri.utils.ComponentIndex.USER_INFO,void 0)};n.uri.utils.getUserInfo=function(){return n.uri.utils.decodeIfPossible_(n.uri.utils.getUserInfoEncoded())};
n.uri.utils.getDomainEncoded=function(){return n.uri.utils.getComponentByIndex_(n.uri.utils.ComponentIndex.DOMAIN,void 0)};n.uri.utils.getDomain=function(){return n.uri.utils.decodeIfPossible_(n.uri.utils.getDomainEncoded(),!0)};n.uri.utils.getPort=function(){return Number(n.uri.utils.getComponentByIndex_(n.uri.utils.ComponentIndex.PORT,void 0))||null};n.uri.utils.getPathEncoded=function(){return n.uri.utils.getComponentByIndex_(n.uri.utils.ComponentIndex.PATH,void 0)};
n.uri.utils.getPath=function(){return n.uri.utils.decodeIfPossible_(n.uri.utils.getPathEncoded(),!0)};n.uri.utils.getQueryData=function(){return n.uri.utils.getComponentByIndex_(n.uri.utils.ComponentIndex.QUERY_DATA,void 0)};n.uri.utils.getFragmentEncoded=function(){var a=(void 0).indexOf("#");return 0>a?null:(void 0).substr(a+1)};n.uri.utils.setFragmentEncoded=function(a,c){return n.uri.utils.removeFragment(a)+(c?"#"+c:"")};n.uri.utils.getFragment=function(){return n.uri.utils.decodeIfPossible_(n.uri.utils.getFragmentEncoded())};
n.uri.utils.getHost=function(a){a=n.uri.utils.split(a);return n.uri.utils.buildFromEncodedParts(a[n.uri.utils.ComponentIndex.SCHEME],a[n.uri.utils.ComponentIndex.USER_INFO],a[n.uri.utils.ComponentIndex.DOMAIN],a[n.uri.utils.ComponentIndex.PORT])};n.uri.utils.getOrigin=function(a){a=n.uri.utils.split(a);return n.uri.utils.buildFromEncodedParts(a[n.uri.utils.ComponentIndex.SCHEME],null,a[n.uri.utils.ComponentIndex.DOMAIN],a[n.uri.utils.ComponentIndex.PORT])};
n.uri.utils.getPathAndAfter=function(a){a=n.uri.utils.split(a);return n.uri.utils.buildFromEncodedParts(null,null,null,null,a[n.uri.utils.ComponentIndex.PATH],a[n.uri.utils.ComponentIndex.QUERY_DATA],a[n.uri.utils.ComponentIndex.FRAGMENT])};n.uri.utils.removeFragment=function(a){var c=a.indexOf("#");return 0>c?a:a.substr(0,c)};
n.uri.utils.haveSameDomain=function(a,c){a=n.uri.utils.split(a);c=n.uri.utils.split(c);return a[n.uri.utils.ComponentIndex.DOMAIN]==c[n.uri.utils.ComponentIndex.DOMAIN]&&a[n.uri.utils.ComponentIndex.SCHEME]==c[n.uri.utils.ComponentIndex.SCHEME]&&a[n.uri.utils.ComponentIndex.PORT]==c[n.uri.utils.ComponentIndex.PORT]};n.uri.utils.assertNoFragmentsOrQueries_=function(a){n.asserts.assert(0>a.indexOf("#")&&0>a.indexOf("?"),"goog.uri.utils: Fragment or query identifiers are not supported: [%s]",a)};
n.uri.utils.parseQueryData=function(a,c){if(a){a=a.split("&");for(var d=0;d<a.length;d++){var e=a[d].indexOf("="),f=null;if(0<=e){var g=a[d].substring(0,e);f=a[d].substring(e+1)}else g=a[d];c(g,f?n.string.urlDecode(f):"")}}};n.uri.utils.splitQueryData_=function(a){var c=a.indexOf("#");0>c&&(c=a.length);var d=a.indexOf("?");if(0>d||d>c){d=c;var e=""}else e=a.substring(d+1,c);return[a.substr(0,d),e,a.substr(c)]};n.uri.utils.joinQueryData_=function(a){return a[0]+(a[1]?"?"+a[1]:"")+a[2]};
n.uri.utils.appendQueryData_=function(a,c){return c?a?a+"&"+c:c:a};n.uri.utils.appendQueryDataToUri_=function(a,c){if(!c)return a;a=n.uri.utils.splitQueryData_(a);a[1]=n.uri.utils.appendQueryData_(a[1],c);return n.uri.utils.joinQueryData_(a)};n.uri.utils.appendKeyValuePairs_=function(a,c,d){n.asserts.assertString(a);if(n.isArray(c)){n.asserts.assertArray(c);for(var e=0;e<c.length;e++)n.uri.utils.appendKeyValuePairs_(a,String(c[e]),d)}else null!=c&&d.push(a+(""===c?"":"="+n.string.urlEncode(c)))};
n.uri.utils.buildQueryData=function(a,c){n.asserts.assert(0==Math.max(a.length-(c||0),0)%2,"goog.uri.utils: Key/value lists must be even in length.");var d=[];for(c=c||0;c<a.length;c+=2)n.uri.utils.appendKeyValuePairs_(a[c],a[c+1],d);return d.join("&")};n.uri.utils.buildQueryDataFromMap=function(a){var c=[],d;for(d in a)n.uri.utils.appendKeyValuePairs_(d,a[d],c);return c.join("&")};
n.uri.utils.appendParams=function(a,c){var d=2==arguments.length?n.uri.utils.buildQueryData(arguments[1],0):n.uri.utils.buildQueryData(arguments,1);return n.uri.utils.appendQueryDataToUri_(a,d)};n.uri.utils.appendParamsFromMap=function(a,c){c=n.uri.utils.buildQueryDataFromMap(c);return n.uri.utils.appendQueryDataToUri_(a,c)};n.uri.utils.appendParam=function(a,c,d){d=n.isDefAndNotNull(d)?"="+n.string.urlEncode(d):"";return n.uri.utils.appendQueryDataToUri_(a,c+d)};
n.uri.utils.findParam_=function(a,c,d,e){for(var f=d.length;0<=(c=a.indexOf(d,c))&&c<e;){var g=a.charCodeAt(c-1);if(g==n.uri.utils.CharCode_.AMPERSAND||g==n.uri.utils.CharCode_.QUESTION)if(g=a.charCodeAt(c+f),!g||g==n.uri.utils.CharCode_.EQUAL||g==n.uri.utils.CharCode_.AMPERSAND||g==n.uri.utils.CharCode_.HASH)return c;c+=f+1}return-1};n.uri.utils.hashOrEndRe_=/#|$/;n.uri.utils.hasParam=function(a,c){return 0<=n.uri.utils.findParam_(a,0,c,a.search(n.uri.utils.hashOrEndRe_))};
n.uri.utils.getParamValue=function(a,c){var d=a.search(n.uri.utils.hashOrEndRe_),e=n.uri.utils.findParam_(a,0,c,d);if(0>e)return null;var f=a.indexOf("&",e);if(0>f||f>d)f=d;e+=c.length+1;return n.string.urlDecode(a.substr(e,f-e))};n.uri.utils.getParamValues=function(a,c){for(var d=a.search(n.uri.utils.hashOrEndRe_),e=0,f,g=[];0<=(f=n.uri.utils.findParam_(a,e,c,d));){e=a.indexOf("&",f);if(0>e||e>d)e=d;f+=c.length+1;g.push(n.string.urlDecode(a.substr(f,e-f)))}return g};
n.uri.utils.trailingQueryPunctuationRe_=/[?&]($|#)/;n.uri.utils.removeParam=function(a,c){for(var d=a.search(n.uri.utils.hashOrEndRe_),e=0,f,g=[];0<=(f=n.uri.utils.findParam_(a,e,c,d));)g.push(a.substring(e,f)),e=Math.min(a.indexOf("&",f)+1||d,d);g.push(a.substr(e));return g.join("").replace(n.uri.utils.trailingQueryPunctuationRe_,"$1")};
n.uri.utils.setParam=function(a){var c=n.uri.utils.StandardQueryParam.RANDOM,d=n.string.getRandomString();return n.uri.utils.appendParam(n.uri.utils.removeParam(a,c),c,d)};n.uri.utils.setParamsFromMap=function(a,c){a=n.uri.utils.splitQueryData_(a);var d=a[1],e=[];d&&n.array.forEach(d.split("&"),function(a){var d=a.indexOf("=");c.hasOwnProperty(0<=d?a.substr(0,d):a)||e.push(a)});a[1]=n.uri.utils.appendQueryData_(e.join("&"),n.uri.utils.buildQueryDataFromMap(c));return n.uri.utils.joinQueryData_(a)};
n.uri.utils.appendPath=function(a,c){n.uri.utils.assertNoFragmentsOrQueries_(a);n.string.endsWith(a,"/")&&(a=a.substr(0,a.length-1));n.string.startsWith(c,"/")&&(c=c.substr(1));return n.string.buildString(a,"/",c)};n.uri.utils.setPath=function(a,c){n.string.startsWith(c,"/");n.uri.utils.split(a)};n.uri.utils.StandardQueryParam={RANDOM:"zx"};n.uri.utils.makeUnique=function(a){return n.uri.utils.setParam(a)};n.Uri=function(a,c){this.domain_=this.userInfo_=this.scheme_="";this.port_=null;this.fragment_=this.path_="";this.ignoreCase_=this.isReadOnly_=!1;var d;a instanceof n.Uri?(this.ignoreCase_=n.isDef(c)?c:a.ignoreCase_,F(this,a.getScheme()),G(this,a.getUserInfo()),H(this,a.getDomain()),I(this,a.getPort()),this.setPath(a.getPath()),J(this,a.getQueryData().clone()),K(this,a.getFragment())):a&&(d=n.uri.utils.split(String(a)))?(this.ignoreCase_=!!c,F(this,d[n.uri.utils.ComponentIndex.SCHEME]||"",!0),G(this,
d[n.uri.utils.ComponentIndex.USER_INFO]||"",!0),H(this,d[n.uri.utils.ComponentIndex.DOMAIN]||"",!0),I(this,d[n.uri.utils.ComponentIndex.PORT]),this.setPath(d[n.uri.utils.ComponentIndex.PATH]||"",!0),J(this,d[n.uri.utils.ComponentIndex.QUERY_DATA]||"",!0),K(this,d[n.uri.utils.ComponentIndex.FRAGMENT]||"",!0)):(this.ignoreCase_=!!c,this.queryData_=new n.Uri.QueryData(null,this.ignoreCase_))};n.Uri.preserveParameterTypesCompatibilityFlag=!1;n.Uri.RANDOM_PARAM=n.uri.utils.StandardQueryParam.RANDOM;
n.Uri.prototype.toString=function(){var a=[],c=this.getScheme();c&&a.push(n.Uri.encodeSpecialChars_(c,n.Uri.reDisallowedInSchemeOrUserInfo_,!0),":");var d=this.getDomain();if(d||"file"==c)a.push("//"),(c=this.getUserInfo())&&a.push(n.Uri.encodeSpecialChars_(c,n.Uri.reDisallowedInSchemeOrUserInfo_,!0),"@"),a.push(n.Uri.removeDoubleEncoding_(n.string.urlEncode(d))),d=this.getPort(),null!=d&&a.push(":",String(d));if(d=this.getPath())this.domain_&&"/"!=d.charAt(0)&&a.push("/"),a.push(n.Uri.encodeSpecialChars_(d,
"/"==d.charAt(0)?n.Uri.reDisallowedInAbsolutePath_:n.Uri.reDisallowedInRelativePath_,!0));(d=this.queryData_.toString())&&a.push("?",d);(d=this.getFragment())&&a.push("#",n.Uri.encodeSpecialChars_(d,n.Uri.reDisallowedInFragment_));return a.join("")};
n.Uri.prototype.resolve=function(a){var c=this.clone(),d=!!a.scheme_;d?F(c,a.getScheme()):d=!!a.userInfo_;d?G(c,a.getUserInfo()):d=!!a.domain_;d?H(c,a.getDomain()):d=null!=a.port_;var e=a.getPath();if(d)I(c,a.getPort());else if(d=!!a.path_){if("/"!=e.charAt(0))if(this.domain_&&!this.path_)e="/"+e;else{var f=c.getPath().lastIndexOf("/");-1!=f&&(e=c.getPath().substr(0,f+1)+e)}e=n.Uri.removeDotSegments(e)}d?c.setPath(e):d=""!==a.queryData_.toString();d?J(c,a.getQueryData().clone()):d=!!a.fragment_;d&&
K(c,a.getFragment());return c};n.Uri.prototype.clone=function(){return new n.Uri(this)};n.Uri.prototype.getScheme=function(){return this.scheme_};var F=function(a,c,d){L(a);a.scheme_=d?n.Uri.decodeOrEmpty_(c,!0):c;a.scheme_&&(a.scheme_=a.scheme_.replace(/:$/,""))};n.Uri.prototype.getUserInfo=function(){return this.userInfo_};var G=function(a,c,d){L(a);a.userInfo_=d?n.Uri.decodeOrEmpty_(c):c};n.Uri.prototype.getDomain=function(){return this.domain_};
var H=function(a,c,d){L(a);a.domain_=d?n.Uri.decodeOrEmpty_(c,!0):c};n.Uri.prototype.getPort=function(){return this.port_};var I=function(a,c){L(a);if(c){c=Number(c);if(isNaN(c)||0>c)throw Error("Bad port number "+c);a.port_=c}else a.port_=null};n.Uri.prototype.getPath=function(){return this.path_};n.Uri.prototype.setPath=function(a,c){L(this);this.path_=c?n.Uri.decodeOrEmpty_(a,!0):a};
var J=function(a,c,d){L(a);c instanceof n.Uri.QueryData?(a.queryData_=c,a.queryData_.setIgnoreCase(a.ignoreCase_)):(d||(c=n.Uri.encodeSpecialChars_(c,n.Uri.reDisallowedInQuery_)),a.queryData_=new n.Uri.QueryData(c,a.ignoreCase_))};n.Uri.prototype.getQueryData=function(){return this.queryData_};n.Uri.prototype.getFragment=function(){return this.fragment_};var K=function(a,c,d){L(a);a.fragment_=d?n.Uri.decodeOrEmpty_(c):c};
n.Uri.prototype.makeUnique=function(){L(this);var a=n.Uri.RANDOM_PARAM,c=n.string.getRandomString();L(this);this.queryData_.set(a,c);return this};n.Uri.prototype.removeParameter=function(a){L(this);this.queryData_.remove(a);return this};var L=function(a){if(a.isReadOnly_)throw Error("Tried to modify a read-only Uri");};n.Uri.prototype.setIgnoreCase=function(a){this.ignoreCase_=a;this.queryData_&&this.queryData_.setIgnoreCase(a)};
n.Uri.parse=function(a,c){return a instanceof n.Uri?a.clone():new n.Uri(a,c)};n.Uri.create=function(a,c,d,e,f,g,h,k){k=new n.Uri(null,k);a&&F(k,a);c&&G(k,c);d&&H(k,d);e&&I(k,e);f&&k.setPath(f);g&&J(k,g);h&&K(k,h);return k};n.Uri.resolve=function(a,c){a instanceof n.Uri||(a=n.Uri.parse(a));c instanceof n.Uri||(c=n.Uri.parse(c));return a.resolve(c)};
n.Uri.removeDotSegments=function(a){if(".."==a||"."==a)return"";if(n.string.contains(a,"./")||n.string.contains(a,"/.")){var c=n.string.startsWith(a,"/");a=a.split("/");for(var d=[],e=0;e<a.length;){var f=a[e++];"."==f?c&&e==a.length&&d.push(""):".."==f?((1<d.length||1==d.length&&""!=d[0])&&d.pop(),c&&e==a.length&&d.push("")):(d.push(f),c=!0)}return d.join("/")}return a};n.Uri.decodeOrEmpty_=function(a,c){return a?c?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""};
n.Uri.encodeSpecialChars_=function(a,c,d){return n.isString(a)?(a=encodeURI(a).replace(c,n.Uri.encodeChar_),d&&(a=n.Uri.removeDoubleEncoding_(a)),a):null};n.Uri.encodeChar_=function(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)};n.Uri.removeDoubleEncoding_=function(a){return a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")};n.Uri.reDisallowedInSchemeOrUserInfo_=/[#\/\?@]/g;n.Uri.reDisallowedInRelativePath_=/[#\?:]/g;n.Uri.reDisallowedInAbsolutePath_=/[#\?]/g;
n.Uri.reDisallowedInQuery_=/[#\?@]/g;n.Uri.reDisallowedInFragment_=/#/g;n.Uri.haveSameDomain=function(a,c){a=n.uri.utils.split(a);c=n.uri.utils.split(c);return a[n.uri.utils.ComponentIndex.DOMAIN]==c[n.uri.utils.ComponentIndex.DOMAIN]&&a[n.uri.utils.ComponentIndex.PORT]==c[n.uri.utils.ComponentIndex.PORT]};n.Uri.QueryData=function(a,c){this.count_=this.keyMap_=null;this.encodedQuery_=a||null;this.ignoreCase_=!!c};
var M=function(a){a.keyMap_||(a.keyMap_=new n.structs.Map,a.count_=0,a.encodedQuery_&&n.uri.utils.parseQueryData(a.encodedQuery_,function(c,d){a.add(n.string.urlDecode(c),d)}))};n.Uri.QueryData.createFromMap=function(a,c,d){c=n.structs.getKeys(a);if("undefined"==typeof c)throw Error("Keys are undefined");d=new n.Uri.QueryData(null,d);a=n.structs.getValues(a);for(var e=0;e<c.length;e++){var f=c[e],g=a[e];n.isArray(g)?aa(d,f,g):d.add(f,g)}return d};
n.Uri.QueryData.createFromKeysValues=function(a,c,d,e){if(a.length!=c.length)throw Error("Mismatched lengths for keys/values");d=new n.Uri.QueryData(null,e);for(e=0;e<a.length;e++)d.add(a[e],c[e]);return d};b=n.Uri.QueryData.prototype;b.getCount=function(){M(this);return this.count_};b.add=function(a,c){M(this);this.encodedQuery_=null;a=P(this,a);var d=this.keyMap_.get(a);d||this.keyMap_.set(a,d=[]);d.push(c);this.count_=n.asserts.assertNumber(this.count_)+1;return this};
b.remove=function(a){M(this);a=P(this,a);return this.keyMap_.containsKey(a)?(this.encodedQuery_=null,this.count_=n.asserts.assertNumber(this.count_)-this.keyMap_.get(a).length,this.keyMap_.remove(a)):!1};b.clear=function(){this.keyMap_=this.encodedQuery_=null;this.count_=0};b.isEmpty=function(){M(this);return 0==this.count_};b.containsKey=function(a){M(this);a=P(this,a);return this.keyMap_.containsKey(a)};b.containsValue=function(a){var c=this.getValues();return n.array.contains(c,a)};
b.forEach=function(a,c){M(this);this.keyMap_.forEach(function(d,e){n.array.forEach(d,function(d){a.call(c,d,e,this)},this)},this)};b.getKeys=function(){M(this);for(var a=this.keyMap_.getValues(),c=this.keyMap_.getKeys(),d=[],e=0;e<c.length;e++)for(var f=a[e],g=0;g<f.length;g++)d.push(c[e]);return d};
b.getValues=function(a){M(this);var c=[];if(n.isString(a))this.containsKey(a)&&(c=n.array.concat(c,this.keyMap_.get(P(this,a))));else{a=this.keyMap_.getValues();for(var d=0;d<a.length;d++)c=n.array.concat(c,a[d])}return c};b.set=function(a,c){M(this);this.encodedQuery_=null;a=P(this,a);this.containsKey(a)&&(this.count_=n.asserts.assertNumber(this.count_)-this.keyMap_.get(a).length);this.keyMap_.set(a,[c]);this.count_=n.asserts.assertNumber(this.count_)+1;return this};
b.get=function(a,c){a=a?this.getValues(a):[];return n.Uri.preserveParameterTypesCompatibilityFlag?0<a.length?a[0]:c:0<a.length?String(a[0]):c};var aa=function(a,c,d){a.remove(c);0<d.length&&(a.encodedQuery_=null,a.keyMap_.set(P(a,c),n.array.clone(d)),a.count_=n.asserts.assertNumber(a.count_)+d.length)};
n.Uri.QueryData.prototype.toString=function(){if(this.encodedQuery_)return this.encodedQuery_;if(!this.keyMap_)return"";for(var a=[],c=this.keyMap_.getKeys(),d=0;d<c.length;d++){var e=c[d],f=n.string.urlEncode(e);e=this.getValues(e);for(var g=0;g<e.length;g++){var h=f;""!==e[g]&&(h+="="+n.string.urlEncode(e[g]));a.push(h)}}return this.encodedQuery_=a.join("&")};
n.Uri.QueryData.prototype.clone=function(){var a=new n.Uri.QueryData;a.encodedQuery_=this.encodedQuery_;this.keyMap_&&(a.keyMap_=this.keyMap_.clone(),a.count_=this.count_);return a};var P=function(a,c){c=String(c);a.ignoreCase_&&(c=c.toLowerCase());return c};n.Uri.QueryData.prototype.setIgnoreCase=function(a){a&&!this.ignoreCase_&&(M(this),this.encodedQuery_=null,this.keyMap_.forEach(function(a,d){var c=d.toLowerCase();d!=c&&(this.remove(d),aa(this,c,a))},this));this.ignoreCase_=a};
n.Uri.QueryData.prototype.extend=function(a){for(var c=0;c<arguments.length;c++)n.structs.forEach(arguments[c],function(a,c){this.add(c,a)},this)};u.WebStoreService=function(){this.baseUrl_=u.WebStoreService.Environment.SANDBOX;this.baseUrlAndPath_=this.baseUrl_+u.WebStoreService.WEB_STORE_REQUEST_PATH_};u.WebStoreService.Environment={PROD:"https://www.googleapis.com",SANDBOX:"https://www-googleapis-staging.sandbox.google.com"};u.WebStoreService.WEB_STORE_REQUEST_PATH_="/chromewebstore/v1.1";
var Q=function(a,c,d,e,f,g,h,k,m,p,r,N){var sa=d?d:"",ta=k||"application/x-www-form-urlencoded",O=r||null,ba=N||null,ca=function(a,c){if(c&&200==a){a=null;try{a=JSON.parse(c)}catch(Ca){g(u.Errors.ErrorTypes.INVALID_RESPONSE_ERROR);return}O&&ba&&(console.log("Updating the licenses cache for appId: "+O),ba.cache_.set(O,new u.LicensesCache.Entry(a)));f(a)}else g(u.Errors.ErrorTypes.INVALID_RESPONSE_ERROR)};r=function(r){if(r){var t=new XMLHttpRequest;t.open(e,c);t.setRequestHeader("Authorization","Bearer "+
r);t.setRequestHeader("Content-Type",ta);t.onreadystatechange=function(){4==t.readyState&&(401==t.status&&r?chrome.identity.removeCachedAuthToken({token:r},function(){h?Q(a,e,d,e,f,g,!1,k,m,p):(console.log(t),ca(t.status,t.responseText))}):(console.log(t),ca(t.status,t.responseText)))};t.send(sa)}else g(u.Errors.ErrorTypes.TOKEN_MISSING_ERROR)};p?r(p):chrome.identity.getAuthToken({interactive:m||!1},r)},R=function(a,c){c=c.parameters||{};if("env"in c){switch(c.env){case "prod":a.baseUrl_=u.WebStoreService.Environment.PROD;
break;default:a.baseUrl_=u.WebStoreService.Environment.SANDBOX}a.baseUrlAndPath_=a.baseUrl_+u.WebStoreService.WEB_STORE_REQUEST_PATH_}};u.WebStoreService.prototype.onFailure_=function(a,c,d){a(u.Errors.getErrorResponse(d||c))};
var da=function(a,c,d,e,f,g){var h=new n.Uri.QueryData;h.add("projection",f.projection?f.projection:"THIN");var k=new n.Uri(a.baseUrlAndPath_+"/items/"+c+"/payments");J(k,h);e=n.bind(a.onFailure_,a,e,u.Errors.ErrorTypes.GET_PURCHASES_ERROR);console.log("Making a request to apiary to retrieve licenses for appId: "+c);Q(a,k.toString(),null,"GET",d,e,!0,void 0,f.interactive,void 0,c,g)},ea=function(a,c,d,e,f,g){c=a.baseUrlAndPath_+"/items/"+c+"/skus/"+d;f=n.bind(a.onFailure_,a,f,u.Errors.ErrorTypes.CONSUME_PURCHASE_ERROR);
console.log("Sending consume call to apiary.");Q(a,c,null,"DELETE",e,f,!0,void 0,g)},fa=function(a,c,d,e,f,g){var h=new n.Uri.QueryData;h.add("hl",g.hl?g.hl:window.navigator.language);g.gl&&h.add("gl",g.gl);h.add("projection",g.projection?g.projection:"THIN");c=a.baseUrlAndPath_+"/items/"+c+"/skus";f&&(c=c+"/"+f);f=new n.Uri(c);J(f,h);e=n.bind(a.onFailure_,a,e,u.Errors.ErrorTypes.GET_SKU_DETAILS_ERROR);Q(a,f.toString(),null,"GET",d,e,!0,void 0,g.interactive)};u.BackgroundDelegate=function(){this.licensesCache_=new u.LicensesCache;chrome.runtime.onConnectExternal.addListener(n.bind(this.onConnect_,this))};n.inherits(u.BackgroundDelegate,n.craw.AppBackgroundDelegate);n.craw.AppBackgroundDelegate.setImpl();u.BackgroundDelegate.Methods_={BUY:"buy",CONSUME_PURCHASE:"consumePurchase",GET_PURCHASES:"getPurchases",GET_SKU_DETAILS:"getSkuDetails"};u.BackgroundDelegate.METHOD_NAME_="method";
u.BackgroundDelegate.prototype.getWindowBounds=function(){return{width:435,height:345}};
var ha=function(a,c,d,e,f){chrome.identity.getAuthToken({interactive:!1},function(g){chrome.app.window.create("/html/craw_window.html",{bounds:a.getWindowBounds(),frame:"chrome",hidden:!1},function(a){d.onDisconnect.addListener(function(){a.close()});var h=a.contentWindow;h.crawConfig_=new n.craw.WindowConfig("https://www.google.com/intl/en-US/chrome/blank.html",!1);h.iapJwt_=e;h.sku_=f;var m=c.parameters;m||(m={});m.userSessionIndex=0;m.fullscreen=!0;m.oauthToken=g;m.propertyId="ChromeIAP";m.applicationId=
d.sender.id;h.iapParams_=m;a.onClosed.addListener(function(){var a=h.purchase_result,c={};a&&"checkoutOrderId"in a?(c.response={orderId:a.checkoutOrderId},(a=h.payment_data)&&(c.response.paymentData=a),(a=h.signature)&&(c.response.signature=a),console.log("Clearing licenses cache value after payment for appId: "+d.sender.id),this.licensesCache_.remove(d.sender.id)):c=u.Errors.getErrorResponse(u.Errors.ErrorTypes.PURCHASE_CANCELED);d.postMessage(c);d.disconnect()}.bind(this))}.bind(this))}.bind(a))},
ia=function(a,c,d,e,f){var g=function(a){e({response:{details:a}})},h=a.licensesCache_.get(d);h?(console.log("Retrieving licenses cache value for appId: "+d),g(h)):(h=c.parameters||{},R(f,c),da(f,d,g,e,h,a.licensesCache_))},ja=function(a,c,d,e){var f=a.parameters||{};R(e,a);fa(e,c,function(a){d({response:{details:a}})},d,a.sku,f)},ka=function(a,c,d,e,f){a=function(a){console.log("Clearing licenses cache value after payment for appId: "+d);this.licensesCache_.remove(d);e({response:{details:a}})}.bind(a);
var g=c.parameters||{};R(f,c);ea(f,d,c.sku,a,e,g.interactive)};
u.BackgroundDelegate.prototype.onConnect_=function(a){var c=function(c){a.postMessage(c);a.disconnect()},d=new u.WebStoreService;a.onMessage.addListener(n.bind(function(e){!e.sku&&u.BackgroundDelegate.Methods_.GET_SKU_DETAILS!=e[u.BackgroundDelegate.METHOD_NAME_]&&u.BackgroundDelegate.Methods_.GET_PURCHASES!=e[u.BackgroundDelegate.METHOD_NAME_]&&u.BackgroundDelegate.Methods_.CONSUME_PURCHASE!=e[u.BackgroundDelegate.METHOD_NAME_]||"prod"==(e.parameters||{}).env?u.BackgroundDelegate.Methods_.GET_PURCHASES==
e[u.BackgroundDelegate.METHOD_NAME_]?ia(this,e,a.sender.id,c,d):u.BackgroundDelegate.Methods_.GET_SKU_DETAILS==e[u.BackgroundDelegate.METHOD_NAME_]?ja(e,a.sender.id,c,d):u.BackgroundDelegate.Methods_.CONSUME_PURCHASE==e[u.BackgroundDelegate.METHOD_NAME_]?ka(this,e,a.sender.id,c,d):ha(this,e,a,e.jwt,e.sku):c(u.Errors.getErrorResponse(u.Errors.ErrorTypes.ENV_NOT_SUPPORTED_ERROR))},this))};n.async={};n.async.FreeList=function(a,c,d){this.limit_=d;this.create_=a;this.reset_=c;this.occupants_=0;this.head_=null};n.async.FreeList.prototype.get=function(){if(0<this.occupants_){this.occupants_--;var a=this.head_;this.head_=a.next;a.next=null}else a=this.create_();return a};n.async.FreeList.prototype.put=function(a){this.reset_(a);this.occupants_<this.limit_&&(this.occupants_++,a.next=this.head_,this.head_=a)};n.dom.HtmlElement=function(){};n.dom.TagName=function(a){this.tagName_=a};n.dom.TagName.prototype.toString=function(){return this.tagName_};n.dom.TagName.A=new n.dom.TagName("A");n.dom.TagName.ABBR=new n.dom.TagName("ABBR");n.dom.TagName.ACRONYM=new n.dom.TagName("ACRONYM");n.dom.TagName.ADDRESS=new n.dom.TagName("ADDRESS");n.dom.TagName.APPLET=new n.dom.TagName("APPLET");n.dom.TagName.AREA=new n.dom.TagName("AREA");n.dom.TagName.ARTICLE=new n.dom.TagName("ARTICLE");n.dom.TagName.ASIDE=new n.dom.TagName("ASIDE");
n.dom.TagName.AUDIO=new n.dom.TagName("AUDIO");n.dom.TagName.B=new n.dom.TagName("B");n.dom.TagName.BASE=new n.dom.TagName("BASE");n.dom.TagName.BASEFONT=new n.dom.TagName("BASEFONT");n.dom.TagName.BDI=new n.dom.TagName("BDI");n.dom.TagName.BDO=new n.dom.TagName("BDO");n.dom.TagName.BIG=new n.dom.TagName("BIG");n.dom.TagName.BLOCKQUOTE=new n.dom.TagName("BLOCKQUOTE");n.dom.TagName.BODY=new n.dom.TagName("BODY");n.dom.TagName.BR=new n.dom.TagName("BR");n.dom.TagName.BUTTON=new n.dom.TagName("BUTTON");
n.dom.TagName.CANVAS=new n.dom.TagName("CANVAS");n.dom.TagName.CAPTION=new n.dom.TagName("CAPTION");n.dom.TagName.CENTER=new n.dom.TagName("CENTER");n.dom.TagName.CITE=new n.dom.TagName("CITE");n.dom.TagName.CODE=new n.dom.TagName("CODE");n.dom.TagName.COL=new n.dom.TagName("COL");n.dom.TagName.COLGROUP=new n.dom.TagName("COLGROUP");n.dom.TagName.COMMAND=new n.dom.TagName("COMMAND");n.dom.TagName.DATA=new n.dom.TagName("DATA");n.dom.TagName.DATALIST=new n.dom.TagName("DATALIST");
n.dom.TagName.DD=new n.dom.TagName("DD");n.dom.TagName.DEL=new n.dom.TagName("DEL");n.dom.TagName.DETAILS=new n.dom.TagName("DETAILS");n.dom.TagName.DFN=new n.dom.TagName("DFN");n.dom.TagName.DIALOG=new n.dom.TagName("DIALOG");n.dom.TagName.DIR=new n.dom.TagName("DIR");n.dom.TagName.DIV=new n.dom.TagName("DIV");n.dom.TagName.DL=new n.dom.TagName("DL");n.dom.TagName.DT=new n.dom.TagName("DT");n.dom.TagName.EM=new n.dom.TagName("EM");n.dom.TagName.EMBED=new n.dom.TagName("EMBED");
n.dom.TagName.FIELDSET=new n.dom.TagName("FIELDSET");n.dom.TagName.FIGCAPTION=new n.dom.TagName("FIGCAPTION");n.dom.TagName.FIGURE=new n.dom.TagName("FIGURE");n.dom.TagName.FONT=new n.dom.TagName("FONT");n.dom.TagName.FOOTER=new n.dom.TagName("FOOTER");n.dom.TagName.FORM=new n.dom.TagName("FORM");n.dom.TagName.FRAME=new n.dom.TagName("FRAME");n.dom.TagName.FRAMESET=new n.dom.TagName("FRAMESET");n.dom.TagName.H1=new n.dom.TagName("H1");n.dom.TagName.H2=new n.dom.TagName("H2");n.dom.TagName.H3=new n.dom.TagName("H3");
n.dom.TagName.H4=new n.dom.TagName("H4");n.dom.TagName.H5=new n.dom.TagName("H5");n.dom.TagName.H6=new n.dom.TagName("H6");n.dom.TagName.HEAD=new n.dom.TagName("HEAD");n.dom.TagName.HEADER=new n.dom.TagName("HEADER");n.dom.TagName.HGROUP=new n.dom.TagName("HGROUP");n.dom.TagName.HR=new n.dom.TagName("HR");n.dom.TagName.HTML=new n.dom.TagName("HTML");n.dom.TagName.I=new n.dom.TagName("I");n.dom.TagName.IFRAME=new n.dom.TagName("IFRAME");n.dom.TagName.IMG=new n.dom.TagName("IMG");
n.dom.TagName.INPUT=new n.dom.TagName("INPUT");n.dom.TagName.INS=new n.dom.TagName("INS");n.dom.TagName.ISINDEX=new n.dom.TagName("ISINDEX");n.dom.TagName.KBD=new n.dom.TagName("KBD");n.dom.TagName.KEYGEN=new n.dom.TagName("KEYGEN");n.dom.TagName.LABEL=new n.dom.TagName("LABEL");n.dom.TagName.LEGEND=new n.dom.TagName("LEGEND");n.dom.TagName.LI=new n.dom.TagName("LI");n.dom.TagName.LINK=new n.dom.TagName("LINK");n.dom.TagName.MAIN=new n.dom.TagName("MAIN");n.dom.TagName.MAP=new n.dom.TagName("MAP");
n.dom.TagName.MARK=new n.dom.TagName("MARK");n.dom.TagName.MATH=new n.dom.TagName("MATH");n.dom.TagName.MENU=new n.dom.TagName("MENU");n.dom.TagName.MENUITEM=new n.dom.TagName("MENUITEM");n.dom.TagName.META=new n.dom.TagName("META");n.dom.TagName.METER=new n.dom.TagName("METER");n.dom.TagName.NAV=new n.dom.TagName("NAV");n.dom.TagName.NOFRAMES=new n.dom.TagName("NOFRAMES");n.dom.TagName.NOSCRIPT=new n.dom.TagName("NOSCRIPT");n.dom.TagName.OBJECT=new n.dom.TagName("OBJECT");n.dom.TagName.OL=new n.dom.TagName("OL");
n.dom.TagName.OPTGROUP=new n.dom.TagName("OPTGROUP");n.dom.TagName.OPTION=new n.dom.TagName("OPTION");n.dom.TagName.OUTPUT=new n.dom.TagName("OUTPUT");n.dom.TagName.P=new n.dom.TagName("P");n.dom.TagName.PARAM=new n.dom.TagName("PARAM");n.dom.TagName.PICTURE=new n.dom.TagName("PICTURE");n.dom.TagName.PRE=new n.dom.TagName("PRE");n.dom.TagName.PROGRESS=new n.dom.TagName("PROGRESS");n.dom.TagName.Q=new n.dom.TagName("Q");n.dom.TagName.RP=new n.dom.TagName("RP");n.dom.TagName.RT=new n.dom.TagName("RT");
n.dom.TagName.RTC=new n.dom.TagName("RTC");n.dom.TagName.RUBY=new n.dom.TagName("RUBY");n.dom.TagName.S=new n.dom.TagName("S");n.dom.TagName.SAMP=new n.dom.TagName("SAMP");n.dom.TagName.SCRIPT=new n.dom.TagName("SCRIPT");n.dom.TagName.SECTION=new n.dom.TagName("SECTION");n.dom.TagName.SELECT=new n.dom.TagName("SELECT");n.dom.TagName.SMALL=new n.dom.TagName("SMALL");n.dom.TagName.SOURCE=new n.dom.TagName("SOURCE");n.dom.TagName.SPAN=new n.dom.TagName("SPAN");n.dom.TagName.STRIKE=new n.dom.TagName("STRIKE");
n.dom.TagName.STRONG=new n.dom.TagName("STRONG");n.dom.TagName.STYLE=new n.dom.TagName("STYLE");n.dom.TagName.SUB=new n.dom.TagName("SUB");n.dom.TagName.SUMMARY=new n.dom.TagName("SUMMARY");n.dom.TagName.SUP=new n.dom.TagName("SUP");n.dom.TagName.SVG=new n.dom.TagName("SVG");n.dom.TagName.TABLE=new n.dom.TagName("TABLE");n.dom.TagName.TBODY=new n.dom.TagName("TBODY");n.dom.TagName.TD=new n.dom.TagName("TD");n.dom.TagName.TEMPLATE=new n.dom.TagName("TEMPLATE");n.dom.TagName.TEXTAREA=new n.dom.TagName("TEXTAREA");
n.dom.TagName.TFOOT=new n.dom.TagName("TFOOT");n.dom.TagName.TH=new n.dom.TagName("TH");n.dom.TagName.THEAD=new n.dom.TagName("THEAD");n.dom.TagName.TIME=new n.dom.TagName("TIME");n.dom.TagName.TITLE=new n.dom.TagName("TITLE");n.dom.TagName.TR=new n.dom.TagName("TR");n.dom.TagName.TRACK=new n.dom.TagName("TRACK");n.dom.TagName.TT=new n.dom.TagName("TT");n.dom.TagName.U=new n.dom.TagName("U");n.dom.TagName.UL=new n.dom.TagName("UL");n.dom.TagName.VAR=new n.dom.TagName("VAR");n.dom.TagName.VIDEO=new n.dom.TagName("VIDEO");
n.dom.TagName.WBR=new n.dom.TagName("WBR");n.async.throwException=function(a){n.global.setTimeout(function(){throw a;},0)};n.async.nextTick=function(a,c,d){var e=a;c&&(e=n.bind(a,c));e=n.async.nextTick.wrapCallback_(e);n.isFunction(n.global.setImmediate)&&(d||n.async.nextTick.useSetImmediate_())?n.global.setImmediate(e):(n.async.nextTick.setImmediate_||(n.async.nextTick.setImmediate_=n.async.nextTick.getSetImmediateEmulator_()),n.async.nextTick.setImmediate_(e))};
n.async.nextTick.useSetImmediate_=function(){return n.global.Window&&n.global.Window.prototype&&!n.labs.userAgent.browser.isEdge()&&n.global.Window.prototype.setImmediate==n.global.setImmediate?!1:!0};
n.async.nextTick.getSetImmediateEmulator_=function(){var a=n.global.MessageChannel;"undefined"===typeof a&&"undefined"!==typeof window&&window.postMessage&&window.addEventListener&&!n.labs.userAgent.engine.isPresto()&&(a=function(){var a=document.createElement("IFRAME");a.style.display="none";a.src="";document.documentElement.appendChild(a);var c=a.contentWindow;a=c.document;a.open();a.write("");a.close();var d="callImmediate"+Math.random(),e="file:"==c.location.protocol?"*":c.location.protocol+"//"+
c.location.host;a=n.bind(function(a){if(("*"==e||a.origin==e)&&a.data==d)this.port1.onmessage()},this);c.addEventListener("message",a,!1);this.port1={};this.port2={postMessage:function(){c.postMessage(d,e)}}});if("undefined"!==typeof a&&!n.labs.userAgent.browser.isIE()){var c=new a,d={},e=d;c.port1.onmessage=function(){if(n.isDef(d.next)){d=d.next;var a=d.cb;d.cb=null;a()}};return function(a){e.next={cb:a};e=e.next;c.port2.postMessage(0)}}return"undefined"!==typeof document&&"onreadystatechange"in
document.createElement("SCRIPT")?function(a){var c=document.createElement("SCRIPT");c.onreadystatechange=function(){c.onreadystatechange=null;c.parentNode.removeChild(c);c=null;a();a=null};document.documentElement.appendChild(c)}:function(a){n.global.setTimeout(a,0)}};n.async.nextTick.wrapCallback_=n.functions.identity;n.debug.entryPointRegistry.register(function(a){n.async.nextTick.wrapCallback_=a});n.async.WorkQueue=function(){this.workTail_=this.workHead_=null};n.async.WorkQueue.DEFAULT_MAX_UNUSED=100;n.async.WorkQueue.freelist_=new n.async.FreeList(function(){return new n.async.WorkItem},function(a){a.reset()},n.async.WorkQueue.DEFAULT_MAX_UNUSED);n.async.WorkQueue.prototype.add=function(a,c){var d=n.async.WorkQueue.freelist_.get();d.set(a,c);this.workTail_?this.workTail_.next=d:(n.asserts.assert(!this.workHead_),this.workHead_=d);this.workTail_=d};
n.async.WorkQueue.prototype.remove=function(){var a=null;this.workHead_&&(a=this.workHead_,this.workHead_=this.workHead_.next,this.workHead_||(this.workTail_=null),a.next=null);return a};n.async.WorkItem=function(){this.next=this.scope=this.fn=null};n.async.WorkItem.prototype.set=function(a,c){this.fn=a;this.scope=c;this.next=null};n.async.WorkItem.prototype.reset=function(){this.next=this.scope=this.fn=null};n.async.run=function(a,c){n.async.run.schedule_||n.async.run.initializeRunner_();n.async.run.workQueueScheduled_||(n.async.run.schedule_(),n.async.run.workQueueScheduled_=!0);n.async.run.workQueue_.add(a,c)};n.async.run.initializeRunner_=function(){if(-1!=String(n.global.Promise).indexOf("[native code]")){var a=n.global.Promise.resolve(void 0);n.async.run.schedule_=function(){a.then(n.async.run.processWorkQueue)}}else n.async.run.schedule_=function(){n.async.nextTick(n.async.run.processWorkQueue)}};
n.async.run.forceNextTick=function(a){n.async.run.schedule_=function(){n.async.nextTick(n.async.run.processWorkQueue);a&&a(n.async.run.processWorkQueue)}};n.async.run.workQueueScheduled_=!1;n.async.run.workQueue_=new n.async.WorkQueue;n.DEBUG&&(n.async.run.resetQueue=function(){n.async.run.workQueueScheduled_=!1;n.async.run.workQueue_=new n.async.WorkQueue});
n.async.run.processWorkQueue=function(){for(var a;a=n.async.run.workQueue_.remove();){try{a.fn.call(a.scope)}catch(c){n.async.throwException(c)}n.async.WorkQueue.freelist_.put(a)}n.async.run.workQueueScheduled_=!1};n.debug.LogRecord=function(a,c,d,e,f){this.reset(a,c,d,e,f)};n.debug.LogRecord.prototype.exception_=null;n.debug.LogRecord.ENABLE_SEQUENCE_NUMBERS=!0;n.debug.LogRecord.nextSequenceNumber_=0;n.debug.LogRecord.prototype.reset=function(a,c,d,e,f){n.debug.LogRecord.ENABLE_SEQUENCE_NUMBERS&&("number"==typeof f||n.debug.LogRecord.nextSequenceNumber_++);e||n.now();this.level_=a;this.msg_=c;delete this.exception_};n.debug.LogRecord.prototype.setLevel=function(a){this.level_=a};
n.debug.LogRecord.prototype.getMessage=function(){return this.msg_};n.debug.LogBuffer=function(){n.asserts.assert(n.debug.LogBuffer.isBufferingEnabled(),"Cannot use goog.debug.LogBuffer without defining goog.debug.LogBuffer.CAPACITY.");this.clear()};n.debug.LogBuffer.getInstance=function(){n.debug.LogBuffer.instance_||(n.debug.LogBuffer.instance_=new n.debug.LogBuffer);return n.debug.LogBuffer.instance_};n.debug.LogBuffer.CAPACITY=0;n.debug.LogBuffer.isBufferingEnabled=function(){return 0<n.debug.LogBuffer.CAPACITY};
n.debug.LogBuffer.prototype.clear=function(){this.buffer_=Array(n.debug.LogBuffer.CAPACITY);this.curIndex_=-1;this.isFull_=!1};n.debug.Logger=function(a){this.name_=a;this.handlers_=this.children_=this.level_=this.parent_=null};n.debug.Logger.ROOT_LOGGER_NAME="";n.debug.Logger.ENABLE_HIERARCHY=!0;n.debug.Logger.ENABLE_PROFILER_LOGGING=!1;n.debug.Logger.ENABLE_HIERARCHY||(n.debug.Logger.rootHandlers_=[]);n.debug.Logger.Level=function(a,c){this.name=a;this.value=c};n.debug.Logger.Level.prototype.toString=function(){return this.name};n.debug.Logger.Level.OFF=new n.debug.Logger.Level("OFF",Infinity);
n.debug.Logger.Level.SHOUT=new n.debug.Logger.Level("SHOUT",1200);n.debug.Logger.Level.SEVERE=new n.debug.Logger.Level("SEVERE",1E3);n.debug.Logger.Level.WARNING=new n.debug.Logger.Level("WARNING",900);n.debug.Logger.Level.INFO=new n.debug.Logger.Level("INFO",800);n.debug.Logger.Level.CONFIG=new n.debug.Logger.Level("CONFIG",700);n.debug.Logger.Level.FINE=new n.debug.Logger.Level("FINE",500);n.debug.Logger.Level.FINER=new n.debug.Logger.Level("FINER",400);
n.debug.Logger.Level.FINEST=new n.debug.Logger.Level("FINEST",300);n.debug.Logger.Level.ALL=new n.debug.Logger.Level("ALL",0);n.debug.Logger.Level.PREDEFINED_LEVELS=[n.debug.Logger.Level.OFF,n.debug.Logger.Level.SHOUT,n.debug.Logger.Level.SEVERE,n.debug.Logger.Level.WARNING,n.debug.Logger.Level.INFO,n.debug.Logger.Level.CONFIG,n.debug.Logger.Level.FINE,n.debug.Logger.Level.FINER,n.debug.Logger.Level.FINEST,n.debug.Logger.Level.ALL];n.debug.Logger.Level.predefinedLevelsCache_=null;
n.debug.Logger.Level.createPredefinedLevelsCache_=function(){n.debug.Logger.Level.predefinedLevelsCache_={};for(var a=0,c;c=n.debug.Logger.Level.PREDEFINED_LEVELS[a];a++)n.debug.Logger.Level.predefinedLevelsCache_[c.value]=c,n.debug.Logger.Level.predefinedLevelsCache_[c.name]=c};n.debug.Logger.Level.getPredefinedLevel=function(a){n.debug.Logger.Level.predefinedLevelsCache_||n.debug.Logger.Level.createPredefinedLevelsCache_();return n.debug.Logger.Level.predefinedLevelsCache_[a]||null};
n.debug.Logger.Level.getPredefinedLevelByValue=function(a){n.debug.Logger.Level.predefinedLevelsCache_||n.debug.Logger.Level.createPredefinedLevelsCache_();if(a in n.debug.Logger.Level.predefinedLevelsCache_)return n.debug.Logger.Level.predefinedLevelsCache_[a];for(var c=0;c<n.debug.Logger.Level.PREDEFINED_LEVELS.length;++c){var d=n.debug.Logger.Level.PREDEFINED_LEVELS[c];if(d.value<=a)return d}return null};n.debug.Logger.getLogger=function(a){return n.debug.LogManager.getLogger(a)};
n.debug.Logger.logToProfilers=function(a){if(n.debug.Logger.ENABLE_PROFILER_LOGGING){var c=n.global.msWriteProfilerMark;c?c(a):(c=n.global.console)&&c.timeStamp&&c.timeStamp(a)}};b=n.debug.Logger.prototype;b.getName=function(){return this.name_};
b.addHandler=function(a){n.debug.LOGGING_ENABLED&&(n.debug.Logger.ENABLE_HIERARCHY?(this.handlers_||(this.handlers_=[]),this.handlers_.push(a)):(n.asserts.assert(!this.name_,"Cannot call addHandler on a non-root logger when goog.debug.Logger.ENABLE_HIERARCHY is false."),n.debug.Logger.rootHandlers_.push(a)))};b.removeHandler=function(a){if(n.debug.LOGGING_ENABLED){var c=n.debug.Logger.ENABLE_HIERARCHY?this.handlers_:n.debug.Logger.rootHandlers_;return!!c&&n.array.remove(c,a)}return!1};
b.getParent=function(){return this.parent_};b.getChildren=function(){this.children_||(this.children_={});return this.children_};b.setLevel=function(a){n.debug.LOGGING_ENABLED&&(n.debug.Logger.ENABLE_HIERARCHY?this.level_=a:(n.asserts.assert(!this.name_,"Cannot call setLevel() on a non-root logger when goog.debug.Logger.ENABLE_HIERARCHY is false."),n.debug.Logger.rootLevel_=a))};
var la=function(a){if(!n.debug.LOGGING_ENABLED)return n.debug.Logger.Level.OFF;if(!n.debug.Logger.ENABLE_HIERARCHY)return n.debug.Logger.rootLevel_;if(a.level_)return a.level_;if(a.parent_)return la(a.parent_);n.asserts.fail("Root logger has no level set.");return null};
n.debug.Logger.prototype.log=function(a,c,d){if(n.debug.LOGGING_ENABLED&&n.debug.LOGGING_ENABLED&&a.value>=la(this).value){n.isFunction(c)&&(c=c());if(n.debug.LogBuffer.isBufferingEnabled()){var e=n.debug.LogBuffer.getInstance(),f=this.name_,g=(e.curIndex_+1)%n.debug.LogBuffer.CAPACITY;e.curIndex_=g;e.isFull_?(e=e.buffer_[g],e.reset(a,c,f),a=e):(e.isFull_=g==n.debug.LogBuffer.CAPACITY-1,a=e.buffer_[g]=new n.debug.LogRecord(a,c,f))}else a=new n.debug.LogRecord(a,String(c),this.name_);d&&(a.exception_=
d);n.debug.Logger.ENABLE_PROFILER_LOGGING&&n.debug.Logger.logToProfilers("log:"+a.getMessage());if(n.debug.Logger.ENABLE_HIERARCHY)for(d=this;d;){f=d;e=a;if(f.handlers_)for(g=0;c=f.handlers_[g];g++)c(e);d=d.getParent()}else for(d=0;c=n.debug.Logger.rootHandlers_[d++];)c(a)}};n.debug.Logger.prototype.warning=function(a,c){n.debug.LOGGING_ENABLED&&this.log(n.debug.Logger.Level.WARNING,a,c)};n.debug.Logger.prototype.info=function(a,c){n.debug.LOGGING_ENABLED&&this.log(n.debug.Logger.Level.INFO,a,c)};
n.debug.Logger.prototype.fine=function(a,c){n.debug.LOGGING_ENABLED&&this.log(n.debug.Logger.Level.FINE,a,c)};n.debug.LogManager={};n.debug.LogManager.loggers_={};n.debug.LogManager.rootLogger_=null;n.debug.LogManager.initialize=function(){n.debug.LogManager.rootLogger_||(n.debug.LogManager.rootLogger_=new n.debug.Logger(n.debug.Logger.ROOT_LOGGER_NAME),n.debug.LogManager.loggers_[n.debug.Logger.ROOT_LOGGER_NAME]=n.debug.LogManager.rootLogger_,n.debug.LogManager.rootLogger_.setLevel(n.debug.Logger.Level.CONFIG))};
n.debug.LogManager.getLoggers=function(){return n.debug.LogManager.loggers_};n.debug.LogManager.getRoot=function(){n.debug.LogManager.initialize();return n.debug.LogManager.rootLogger_};n.debug.LogManager.getLogger=function(a){n.debug.LogManager.initialize();return n.debug.LogManager.loggers_[a]||n.debug.LogManager.createLogger_(a)};
n.debug.LogManager.createFunctionForCatchErrors=function(a){return function(c){var d=a||n.debug.LogManager.getRoot();n.debug.LOGGING_ENABLED&&d.log(n.debug.Logger.Level.SEVERE,"Error: "+c.message+" ("+c.fileName+" @ Line: "+c.line+")",void 0)}};
n.debug.LogManager.createLogger_=function(a){var c=new n.debug.Logger(a);if(n.debug.Logger.ENABLE_HIERARCHY){var d=a.lastIndexOf("."),e=a.substr(d+1);d=n.debug.LogManager.getLogger(a.substr(0,d));d.getChildren()[e]=c;c.parent_=d}return n.debug.LogManager.loggers_[a]=c};n.json={};n.json.USE_NATIVE_JSON=!1;n.json.TRY_NATIVE_JSON=!1;n.json.isValid=function(a){return/^\s*$/.test(a)?!1:/^[\],:{}\s\u2028\u2029]*$/.test(a.replace(/\\["\\\/bfnrtu]/g,"@").replace(/(?:"[^"\\\n\r\u2028\u2029\x00-\x08\x0a-\x1f]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)[\s\u2028\u2029]*(?=:|,|]|}|$)/g,"]").replace(/(?:^|:|,)(?:[\s\u2028\u2029]*\[)+/g,""))};n.json.errorLogger_=n.nullFunction;n.json.setErrorLogger=function(a){n.json.errorLogger_=a};
n.json.parse=n.json.USE_NATIVE_JSON?n.global.JSON.parse:function(a){if(n.json.TRY_NATIVE_JSON)try{return n.global.JSON.parse(a)}catch(e){var c=e}a=String(a);if(n.json.isValid(a))try{var d=eval("("+a+")");c&&n.json.errorLogger_("Invalid JSON: "+a,c);return d}catch(e){}throw Error("Invalid JSON string: "+a);};n.json.serialize=n.json.USE_NATIVE_JSON?n.global.JSON.stringify:function(a,c){return(new n.json.Serializer(c)).serialize(a)};n.json.Serializer=function(a){this.replacer_=a};
n.json.Serializer.prototype.serialize=function(a){var c=[];S(this,a,c);return c.join("")};
var S=function(a,c,d){if(null==c)d.push("null");else{if("object"==typeof c){if(n.isArray(c)){var e=c;c=e.length;d.push("[");for(var f="",g=0;g<c;g++)d.push(f),f=e[g],S(a,a.replacer_?a.replacer_.call(e,String(g),f):f,d),f=",";d.push("]");return}if(c instanceof String||c instanceof Number||c instanceof Boolean)c=c.valueOf();else{d.push("{");g="";for(e in c)Object.prototype.hasOwnProperty.call(c,e)&&(f=c[e],"function"!=typeof f&&(d.push(g),ma(e,d),d.push(":"),S(a,a.replacer_?a.replacer_.call(c,e,f):
f,d),g=","));d.push("}");return}}switch(typeof c){case "string":ma(c,d);break;case "number":d.push(isFinite(c)&&!isNaN(c)?String(c):"null");break;case "boolean":d.push(String(c));break;case "function":d.push("null");break;default:throw Error("Unknown type: "+typeof c);}}};n.json.Serializer.charToJsonCharCache_={'"':'\\"',"\\":"\\\\","/":"\\/","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","\t":"\\t","\x0B":"\\u000b"};
n.json.Serializer.charsToReplace_=/\uffff/.test("\uffff")?/[\\"\x00-\x1f\x7f-\uffff]/g:/[\\"\x00-\x1f\x7f-\xff]/g;var ma=function(a,c){c.push('"',a.replace(n.json.Serializer.charsToReplace_,function(a){var c=n.json.Serializer.charToJsonCharCache_[a];c||(c="\\u"+(a.charCodeAt(0)|65536).toString(16).substr(1),n.json.Serializer.charToJsonCharCache_[a]=c);return c}),'"')};n.json.hybrid={};n.json.hybrid.stringify=n.json.USE_NATIVE_JSON?n.global.JSON.stringify:function(a){if(n.global.JSON)try{return n.global.JSON.stringify(a)}catch(c){}return n.json.serialize(a)};n.json.hybrid.parse_=function(a){var c=n.json.parse;if(n.global.JSON)try{var d=n.global.JSON.parse(a);n.asserts.assert("object"==typeof d);return d}catch(e){}return c(a)};n.json.hybrid.parse=n.json.USE_NATIVE_JSON?n.global.JSON.parse:function(a){return n.json.hybrid.parse_(a)};n.log={};n.log.ENABLED=n.debug.LOGGING_ENABLED;n.log.ROOT_LOGGER_NAME=n.debug.Logger.ROOT_LOGGER_NAME;n.log.Logger=n.debug.Logger;n.log.Level=n.debug.Logger.Level;n.log.LogRecord=n.debug.LogRecord;n.log.getLogger=function(a){return n.log.ENABLED?n.debug.LogManager.getLogger(a):null};n.log.addHandler=function(a){n.log.ENABLED&&a&&a.addHandler(void 0)};n.log.removeHandler=function(a){return n.log.ENABLED&&a?a.removeHandler(void 0):!1};n.log.log=function(a,c,d,e){n.log.ENABLED&&a&&a.log(c,d,e)};
n.log.error=function(a,c,d){n.log.ENABLED&&a&&n.debug.LOGGING_ENABLED&&a.log(n.debug.Logger.Level.SEVERE,c,d)};n.log.warning=function(a,c){n.log.ENABLED&&a&&a.warning(c,void 0)};n.log.info=function(a,c,d){n.log.ENABLED&&a&&a.info(c,d)};n.log.fine=function(a,c){n.log.ENABLED&&a&&a.fine(c,void 0)};n.net={};n.net.ErrorCode={NO_ERROR:0,ACCESS_DENIED:1,FILE_NOT_FOUND:2,FF_SILENT_ERROR:3,CUSTOM_ERROR:4,EXCEPTION:5,HTTP_ERROR:6,ABORT:7,TIMEOUT:8,OFFLINE:9};
n.net.ErrorCode.getDebugMessage=function(a){switch(a){case n.net.ErrorCode.NO_ERROR:return"No Error";case n.net.ErrorCode.ACCESS_DENIED:return"Access denied to content document";case n.net.ErrorCode.FILE_NOT_FOUND:return"File not found";case n.net.ErrorCode.FF_SILENT_ERROR:return"Firefox silently errored";case n.net.ErrorCode.CUSTOM_ERROR:return"Application custom error";case n.net.ErrorCode.EXCEPTION:return"An exception occurred";case n.net.ErrorCode.HTTP_ERROR:return"Http response at 400 or 500 level";
case n.net.ErrorCode.ABORT:return"Request was aborted";case n.net.ErrorCode.TIMEOUT:return"Request timed out";case n.net.ErrorCode.OFFLINE:return"The resource is not available offline";default:return"Unrecognized error code"}};n.net.EventType={COMPLETE:"complete",SUCCESS:"success",ERROR:"error",ABORT:"abort",READY:"ready",READY_STATE_CHANGE:"readystatechange",TIMEOUT:"timeout",INCREMENTAL_DATA:"incrementaldata",PROGRESS:"progress",DOWNLOAD_PROGRESS:"downloadprogress",UPLOAD_PROGRESS:"uploadprogress"};n.net.HttpStatus={CONTINUE:100,SWITCHING_PROTOCOLS:101,OK:200,CREATED:201,ACCEPTED:202,NON_AUTHORITATIVE_INFORMATION:203,NO_CONTENT:204,RESET_CONTENT:205,PARTIAL_CONTENT:206,MULTIPLE_CHOICES:300,MOVED_PERMANENTLY:301,FOUND:302,SEE_OTHER:303,NOT_MODIFIED:304,USE_PROXY:305,TEMPORARY_REDIRECT:307,BAD_REQUEST:400,UNAUTHORIZED:401,PAYMENT_REQUIRED:402,FORBIDDEN:403,NOT_FOUND:404,METHOD_NOT_ALLOWED:405,NOT_ACCEPTABLE:406,PROXY_AUTHENTICATION_REQUIRED:407,REQUEST_TIMEOUT:408,CONFLICT:409,GONE:410,LENGTH_REQUIRED:411,
PRECONDITION_FAILED:412,REQUEST_ENTITY_TOO_LARGE:413,REQUEST_URI_TOO_LONG:414,UNSUPPORTED_MEDIA_TYPE:415,REQUEST_RANGE_NOT_SATISFIABLE:416,EXPECTATION_FAILED:417,PRECONDITION_REQUIRED:428,TOO_MANY_REQUESTS:429,REQUEST_HEADER_FIELDS_TOO_LARGE:431,INTERNAL_SERVER_ERROR:500,NOT_IMPLEMENTED:501,BAD_GATEWAY:502,SERVICE_UNAVAILABLE:503,GATEWAY_TIMEOUT:504,HTTP_VERSION_NOT_SUPPORTED:505,NETWORK_AUTHENTICATION_REQUIRED:511,QUIRK_IE_NO_CONTENT:1223};
n.net.HttpStatus.isSuccess=function(a){switch(a){case n.net.HttpStatus.OK:case n.net.HttpStatus.CREATED:case n.net.HttpStatus.ACCEPTED:case n.net.HttpStatus.NO_CONTENT:case n.net.HttpStatus.PARTIAL_CONTENT:case n.net.HttpStatus.NOT_MODIFIED:case n.net.HttpStatus.QUIRK_IE_NO_CONTENT:return!0;default:return!1}};n.net.XhrLike=function(){};b=n.net.XhrLike.prototype;b.open=function(){};b.send=function(){};b.abort=function(){};b.setRequestHeader=function(){};b.getResponseHeader=function(){};b.getAllResponseHeaders=function(){};n.net.XmlHttpFactory=function(){};n.net.XmlHttpFactory.prototype.cachedOptions_=null;n.net.XmlHttpFactory.prototype.getOptions=function(){var a;(a=this.cachedOptions_)||(a={},na(this)&&(a[n.net.XmlHttp.OptionType.USE_NULL_FUNCTION]=!0,a[n.net.XmlHttp.OptionType.LOCAL_REQUEST_ERROR]=!0),a=this.cachedOptions_=a);return a};n.net.WrapperXmlHttpFactory=function(a,c){this.xhrFactory_=a;this.optionsFactory_=c};n.inherits(n.net.WrapperXmlHttpFactory,n.net.XmlHttpFactory);n.net.WrapperXmlHttpFactory.prototype.createInstance=function(){return this.xhrFactory_()};n.net.WrapperXmlHttpFactory.prototype.getOptions=function(){return this.optionsFactory_()};n.net.XmlHttp=function(){return n.net.XmlHttp.factory_.createInstance()};n.net.XmlHttp.ASSUME_NATIVE_XHR=!1;n.net.XmlHttpDefines={};n.net.XmlHttpDefines.ASSUME_NATIVE_XHR=!1;n.net.XmlHttp.getOptions=function(){return n.net.XmlHttp.factory_.getOptions()};n.net.XmlHttp.OptionType={USE_NULL_FUNCTION:0,LOCAL_REQUEST_ERROR:1};n.net.XmlHttp.ReadyState={UNINITIALIZED:0,LOADING:1,LOADED:2,INTERACTIVE:3,COMPLETE:4};
n.net.XmlHttp.setFactory=function(a,c){n.net.XmlHttp.setGlobalFactory(new n.net.WrapperXmlHttpFactory(n.asserts.assert(a),n.asserts.assert(c)))};n.net.XmlHttp.setGlobalFactory=function(a){n.net.XmlHttp.factory_=a};n.net.DefaultXmlHttpFactory=function(){};n.inherits(n.net.DefaultXmlHttpFactory,n.net.XmlHttpFactory);n.net.DefaultXmlHttpFactory.prototype.createInstance=function(){var a=na(this);return a?new ActiveXObject(a):new XMLHttpRequest};
var na=function(a){if(n.net.XmlHttp.ASSUME_NATIVE_XHR||n.net.XmlHttpDefines.ASSUME_NATIVE_XHR)return"";if(!a.ieProgId_&&"undefined"==typeof XMLHttpRequest&&"undefined"!=typeof ActiveXObject){for(var c=["MSXML2.XMLHTTP.6.0","MSXML2.XMLHTTP.3.0","MSXML2.XMLHTTP","Microsoft.XMLHTTP"],d=0;d<c.length;d++){var e=c[d];try{return new ActiveXObject(e),a.ieProgId_=e}catch(f){}}throw Error("Could not create ActiveXObject. ActiveX might be disabled, or MSXML might not be installed");}return a.ieProgId_};n.net.XmlHttp.setGlobalFactory(new n.net.DefaultXmlHttpFactory);n.promise={};n.promise.Resolver=function(){};n.Thenable=function(){};n.Thenable.prototype.then=function(){};n.Thenable.IMPLEMENTED_BY_PROP="$goog_Thenable";n.Thenable.addImplementation=function(a){a.prototype.then=a.prototype.then;a.prototype[n.Thenable.IMPLEMENTED_BY_PROP]=!0};n.Thenable.isImplementedBy=function(a){if(!a)return!1;try{return!!a[n.Thenable.IMPLEMENTED_BY_PROP]}catch(c){return!1}};n.Promise=function(a,c){this.state_=n.Promise.State_.PENDING;this.result_=void 0;this.callbackEntriesTail_=this.callbackEntries_=this.parent_=null;this.executing_=!1;0<n.Promise.UNHANDLED_REJECTION_DELAY?this.unhandledRejectionId_=0:0==n.Promise.UNHANDLED_REJECTION_DELAY&&(this.hadUnhandledRejection_=!1);n.Promise.LONG_STACK_TRACES&&(this.stack_=[],T(this,Error("created")),this.currentStep_=0);if(a!=n.nullFunction)try{var d=this;a.call(c,function(a){U(d,n.Promise.State_.FULFILLED,a)},function(a){if(n.DEBUG&&
!(a instanceof n.Promise.CancellationError))try{if(a instanceof Error)throw a;throw Error("Promise rejected.");}catch(f){}U(d,n.Promise.State_.REJECTED,a)})}catch(e){U(this,n.Promise.State_.REJECTED,e)}};n.Promise.LONG_STACK_TRACES=!1;n.Promise.UNHANDLED_REJECTION_DELAY=0;n.Promise.State_={PENDING:0,BLOCKED:1,FULFILLED:2,REJECTED:3};n.Promise.CallbackEntry_=function(){this.next=this.context=this.onRejected=this.onFulfilled=this.child=null;this.always=!1};
n.Promise.CallbackEntry_.prototype.reset=function(){this.context=this.onRejected=this.onFulfilled=this.child=null;this.always=!1};n.Promise.DEFAULT_MAX_UNUSED=100;n.Promise.freelist_=new n.async.FreeList(function(){return new n.Promise.CallbackEntry_},function(a){a.reset()},n.Promise.DEFAULT_MAX_UNUSED);n.Promise.getCallbackEntry_=function(a,c,d){var e=n.Promise.freelist_.get();e.onFulfilled=a;e.onRejected=c;e.context=d;return e};n.Promise.returnEntry_=function(a){n.Promise.freelist_.put(a)};
n.Promise.resolve=function(a){if(a instanceof n.Promise)return a;var c=new n.Promise(n.nullFunction);U(c,n.Promise.State_.FULFILLED,a);return c};n.Promise.reject=function(a){return new n.Promise(function(c,d){d(a)})};n.Promise.resolveThen_=function(a,c,d){n.Promise.maybeThen_(a,c,d,null)||n.async.run(n.partial(c,a))};n.Promise.race=function(a){return new n.Promise(function(c,d){a.length||c(void 0);for(var e=0,f;e<a.length;e++)f=a[e],n.Promise.resolveThen_(f,c,d)})};
n.Promise.all=function(a){return new n.Promise(function(c,d){var e=a.length,f=[];if(e)for(var g=function(a,d){e--;f[a]=d;0==e&&c(f)},h=function(a){d(a)},k=0,m;k<a.length;k++)m=a[k],n.Promise.resolveThen_(m,n.partial(g,k),h);else c(f)})};
n.Promise.allSettled=function(a){return new n.Promise(function(c){var d=a.length,e=[];if(d)for(var f=function(a,f,g){d--;e[a]=f?{fulfilled:!0,value:g}:{fulfilled:!1,reason:g};0==d&&c(e)},g=0,h;g<a.length;g++)h=a[g],n.Promise.resolveThen_(h,n.partial(f,g,!0),n.partial(f,g,!1));else c(e)})};
n.Promise.firstFulfilled=function(a){return new n.Promise(function(c,d){var e=a.length,f=[];if(e)for(var g=function(a){c(a)},h=function(a,c){e--;f[a]=c;0==e&&d(f)},k=0,m;k<a.length;k++)m=a[k],n.Promise.resolveThen_(m,g,n.partial(h,k));else c(void 0)})};n.Promise.withResolver=function(){var a,c,d=new n.Promise(function(d,f){a=d;c=f});return new n.Promise.Resolver_(d,a,c)};
n.Promise.prototype.then=function(a,c,d){null!=a&&n.asserts.assertFunction(a,"opt_onFulfilled should be a function.");null!=c&&n.asserts.assertFunction(c,"opt_onRejected should be a function. Did you pass opt_context as the second argument instead of the third?");n.Promise.LONG_STACK_TRACES&&T(this,Error("then"));return oa(this,n.isFunction(a)?a:null,n.isFunction(c)?c:null,d)};n.Thenable.addImplementation(n.Promise);
var pa=function(a,c){n.Promise.LONG_STACK_TRACES&&T(a,Error("thenCatch"));return oa(a,null,c,void 0)};n.Promise.prototype.cancel=function(a){this.state_==n.Promise.State_.PENDING&&n.async.run(function(){var c=new n.Promise.CancellationError(a);qa(this,c)},this)};
var qa=function(a,c){if(a.state_==n.Promise.State_.PENDING)if(a.parent_){var d=a.parent_;if(d.callbackEntries_){for(var e=0,f=null,g=null,h=d.callbackEntries_;h&&(h.always||(e++,h.child==a&&(f=h),!(f&&1<e)));h=h.next)f||(g=h);f&&(d.state_==n.Promise.State_.PENDING&&1==e?qa(d,c):(g?(e=g,n.asserts.assert(d.callbackEntries_),n.asserts.assert(null!=e),e.next==d.callbackEntriesTail_&&(d.callbackEntriesTail_=e),e.next=e.next.next):ra(d),ua(d,f,n.Promise.State_.REJECTED,c)))}a.parent_=null}else U(a,n.Promise.State_.REJECTED,
c)},wa=function(a,c){a.callbackEntries_||a.state_!=n.Promise.State_.FULFILLED&&a.state_!=n.Promise.State_.REJECTED||va(a);n.asserts.assert(null!=c.onFulfilled);a.callbackEntriesTail_?a.callbackEntriesTail_.next=c:a.callbackEntries_=c;a.callbackEntriesTail_=c},oa=function(a,c,d,e){var f=n.Promise.getCallbackEntry_(null,null,null);f.child=new n.Promise(function(a,h){f.onFulfilled=c?function(d){try{var f=c.call(e,d);a(f)}catch(p){h(p)}}:a;f.onRejected=d?function(c){try{var f=d.call(e,c);!n.isDef(f)&&
c instanceof n.Promise.CancellationError?h(c):a(f)}catch(p){h(p)}}:h});f.child.parent_=a;wa(a,f);return f.child};n.Promise.prototype.unblockAndFulfill_=function(a){n.asserts.assert(this.state_==n.Promise.State_.BLOCKED);this.state_=n.Promise.State_.PENDING;U(this,n.Promise.State_.FULFILLED,a)};n.Promise.prototype.unblockAndReject_=function(a){n.asserts.assert(this.state_==n.Promise.State_.BLOCKED);this.state_=n.Promise.State_.PENDING;U(this,n.Promise.State_.REJECTED,a)};
var U=function(a,c,d){a.state_==n.Promise.State_.PENDING&&(a===d&&(c=n.Promise.State_.REJECTED,d=new TypeError("Promise cannot resolve to itself")),a.state_=n.Promise.State_.BLOCKED,n.Promise.maybeThen_(d,a.unblockAndFulfill_,a.unblockAndReject_,a)||(a.result_=d,a.state_=c,a.parent_=null,va(a),c!=n.Promise.State_.REJECTED||d instanceof n.Promise.CancellationError||n.Promise.addUnhandledRejection_(a,d)))};
n.Promise.maybeThen_=function(a,c,d,e){if(a instanceof n.Promise)return null!=c&&n.asserts.assertFunction(c,"opt_onFulfilled should be a function."),null!=d&&n.asserts.assertFunction(d,"opt_onRejected should be a function. Did you pass opt_context as the second argument instead of the third?"),n.Promise.LONG_STACK_TRACES&&T(a,Error("then")),wa(a,n.Promise.getCallbackEntry_(c||n.nullFunction,d||null,e)),!0;if(n.Thenable.isImplementedBy(a))return a.then(c,d,e),!0;if(n.isObject(a))try{var f=a.then;if(n.isFunction(f))return n.Promise.tryThen_(a,
f,c,d,e),!0}catch(g){return d.call(e,g),!0}return!1};n.Promise.tryThen_=function(a,c,d,e,f){var g=!1,h=function(a){g||(g=!0,d.call(f,a))},k=function(a){g||(g=!0,e.call(f,a))};try{c.call(a,h,k)}catch(m){k(m)}};var va=function(a){a.executing_||(a.executing_=!0,n.async.run(a.executeCallbacks_,a))},ra=function(a){var c=null;a.callbackEntries_&&(c=a.callbackEntries_,a.callbackEntries_=c.next,c.next=null);a.callbackEntries_||(a.callbackEntriesTail_=null);null!=c&&n.asserts.assert(null!=c.onFulfilled);return c};
n.Promise.prototype.executeCallbacks_=function(){for(var a;a=ra(this);)n.Promise.LONG_STACK_TRACES&&this.currentStep_++,ua(this,a,this.state_,this.result_);this.executing_=!1};
var ua=function(a,c,d,e){if(d==n.Promise.State_.REJECTED&&c.onRejected&&!c.always)if(0<n.Promise.UNHANDLED_REJECTION_DELAY)for(;a&&a.unhandledRejectionId_;a=a.parent_)n.global.clearTimeout(a.unhandledRejectionId_),a.unhandledRejectionId_=0;else if(0==n.Promise.UNHANDLED_REJECTION_DELAY)for(;a&&a.hadUnhandledRejection_;a=a.parent_)a.hadUnhandledRejection_=!1;if(c.child)c.child.parent_=null,n.Promise.invokeCallback_(c,d,e);else try{c.always?c.onFulfilled.call(c.context):n.Promise.invokeCallback_(c,
d,e)}catch(f){n.Promise.handleRejection_.call(null,f)}n.Promise.returnEntry_(c)};n.Promise.invokeCallback_=function(a,c,d){c==n.Promise.State_.FULFILLED?a.onFulfilled.call(a.context,d):a.onRejected&&a.onRejected.call(a.context,d)};
var T=function(a,c){if(n.Promise.LONG_STACK_TRACES&&n.isString(c.stack)){var d=c.stack.split("\n",4)[3];c=c.message;c+=Array(11-c.length).join(" ");a.stack_.push(c+d)}},xa=function(a,c){if(n.Promise.LONG_STACK_TRACES&&c&&n.isString(c.stack)&&a.stack_.length){for(var d=["Promise trace:"],e=a;e;e=e.parent_){for(var f=a.currentStep_;0<=f;f--)d.push(e.stack_[f]);d.push("Value: ["+(e.state_==n.Promise.State_.REJECTED?"REJECTED":"FULFILLED")+"] <"+String(e.result_)+">")}c.stack+="\n\n"+d.join("\n")}};
n.Promise.addUnhandledRejection_=function(a,c){0<n.Promise.UNHANDLED_REJECTION_DELAY?a.unhandledRejectionId_=n.global.setTimeout(function(){xa(a,c);n.Promise.handleRejection_.call(null,c)},n.Promise.UNHANDLED_REJECTION_DELAY):0==n.Promise.UNHANDLED_REJECTION_DELAY&&(a.hadUnhandledRejection_=!0,n.async.run(function(){a.hadUnhandledRejection_&&(xa(a,c),n.Promise.handleRejection_.call(null,c))}))};n.Promise.handleRejection_=n.async.throwException;
n.Promise.setUnhandledRejectionHandler=function(a){n.Promise.handleRejection_=a};n.Promise.CancellationError=function(a){n.debug.Error.call(this,a)};n.inherits(n.Promise.CancellationError,n.debug.Error);n.Promise.CancellationError.prototype.name="cancel";n.Promise.Resolver_=function(a,c,d){this.promise=a;this.resolve=c;this.reject=d};n.Timer=function(a,c){n.events.EventTarget.call(this);this.interval_=a||1;this.timerObject_=c||n.Timer.defaultTimerObject;this.boundTick_=n.bind(this.tick_,this);this.last_=n.now()};n.inherits(n.Timer,n.events.EventTarget);n.Timer.MAX_TIMEOUT_=2147483647;n.Timer.INVALID_TIMEOUT_ID_=-1;n.Timer.prototype.enabled=!1;n.Timer.defaultTimerObject=n.global;n.Timer.intervalScale=.8;b=n.Timer.prototype;b.timer_=null;
b.setInterval=function(a){this.interval_=a;this.timer_&&this.enabled?(this.stop(),this.start()):this.timer_&&this.stop()};
b.tick_=function(){if(this.enabled){var a=n.now()-this.last_;0<a&&a<this.interval_*n.Timer.intervalScale?this.timer_=this.timerObject_.setTimeout(this.boundTick_,this.interval_-a):(this.timer_&&(this.timerObject_.clearTimeout(this.timer_),this.timer_=null),this.dispatchEvent(n.Timer.TICK),this.enabled&&(this.timer_=this.timerObject_.setTimeout(this.boundTick_,this.interval_),this.last_=n.now()))}};
b.start=function(){this.enabled=!0;this.timer_||(this.timer_=this.timerObject_.setTimeout(this.boundTick_,this.interval_),this.last_=n.now())};b.stop=function(){this.enabled=!1;this.timer_&&(this.timerObject_.clearTimeout(this.timer_),this.timer_=null)};b.disposeInternal=function(){n.Timer.superClass_.disposeInternal.call(this);this.stop();delete this.timerObject_};n.Timer.TICK="tick";
n.Timer.callOnce=function(a,c,d){if(n.isFunction(a))d&&(a=n.bind(a,d));else if(a&&"function"==typeof a.handleEvent)a=n.bind(a.handleEvent,a);else throw Error("Invalid listener argument");return Number(c)>n.Timer.MAX_TIMEOUT_?n.Timer.INVALID_TIMEOUT_ID_:n.Timer.defaultTimerObject.setTimeout(a,c||0)};n.Timer.clear=function(a){n.Timer.defaultTimerObject.clearTimeout(a)};
n.Timer.promise=function(a,c){var d=null;return pa(new n.Promise(function(e,f){d=n.Timer.callOnce(function(){e(c)},a);d==n.Timer.INVALID_TIMEOUT_ID_&&f(Error("Failed to schedule timer."))}),function(a){n.Timer.clear(d);throw a;})};n.net.XhrIo=function(a){n.events.EventTarget.call(this);this.headers=new n.structs.Map;this.xmlHttpFactory_=a||null;this.active_=!1;this.xhrOptions_=this.xhr_=null;this.lastError_=this.lastMethod_=this.lastUri_="";this.inAbort_=this.inOpen_=this.inSend_=this.errorDispatched_=!1;this.timeoutInterval_=0;this.timeoutId_=null;this.responseType_=n.net.XhrIo.ResponseType.DEFAULT;this.useXhr2Timeout_=this.progressEventsEnabled_=this.withCredentials_=!1};n.inherits(n.net.XhrIo,n.events.EventTarget);
n.net.XhrIo.ResponseType={DEFAULT:"",TEXT:"text",DOCUMENT:"document",BLOB:"blob",ARRAY_BUFFER:"arraybuffer"};n.net.XhrIo.prototype.logger_=n.log.getLogger("goog.net.XhrIo");n.net.XhrIo.CONTENT_TYPE_HEADER="Content-Type";n.net.XhrIo.CONTENT_TRANSFER_ENCODING="Content-Transfer-Encoding";n.net.XhrIo.HTTP_SCHEME_PATTERN=/^https?$/i;n.net.XhrIo.METHODS_WITH_FORM_DATA=["POST","PUT"];n.net.XhrIo.FORM_CONTENT_TYPE="application/x-www-form-urlencoded;charset=utf-8";n.net.XhrIo.XHR2_TIMEOUT_="timeout";
n.net.XhrIo.XHR2_ON_TIMEOUT_="ontimeout";n.net.XhrIo.sendInstances_=[];n.net.XhrIo.send=function(a,c,d,e,f,g,h){var k=new n.net.XhrIo;n.net.XhrIo.sendInstances_.push(k);c&&k.listen(n.net.EventType.COMPLETE,c);k.listenOnce(n.net.EventType.READY,k.cleanupSend_);g&&(k.timeoutInterval_=Math.max(0,g));h&&(k.withCredentials_=h);k.send(a,d,e,f);return k};n.net.XhrIo.cleanup=function(){for(var a=n.net.XhrIo.sendInstances_;a.length;)a.pop().dispose()};
n.net.XhrIo.protectEntryPoints=function(a){n.net.XhrIo.prototype.onReadyStateChangeEntryPoint_=a.protectEntryPoint(n.net.XhrIo.prototype.onReadyStateChangeEntryPoint_)};n.net.XhrIo.prototype.cleanupSend_=function(){this.dispose();n.array.remove(n.net.XhrIo.sendInstances_,this)};
n.net.XhrIo.prototype.send=function(a,c,d,e){if(this.xhr_)throw Error("[goog.net.XhrIo] Object is active with another request="+this.lastUri_+"; newUri="+a);c=c?c.toUpperCase():"GET";this.lastUri_=a;this.lastError_="";this.lastMethod_=c;this.errorDispatched_=!1;this.active_=!0;this.xhr_=this.xmlHttpFactory_?this.xmlHttpFactory_.createInstance():n.net.XmlHttp();this.xhrOptions_=this.xmlHttpFactory_?this.xmlHttpFactory_.getOptions():n.net.XmlHttp.getOptions();this.xhr_.onreadystatechange=n.bind(this.onReadyStateChange_,
this);this.progressEventsEnabled_&&"onprogress"in this.xhr_&&(this.xhr_.onprogress=n.bind(function(a){this.onProgressHandler_(a,!0)},this),this.xhr_.upload&&(this.xhr_.upload.onprogress=n.bind(this.onProgressHandler_,this)));try{n.log.fine(this.logger_,V(this,"Opening Xhr")),this.inOpen_=!0,this.xhr_.open(c,String(a),!0),this.inOpen_=!1}catch(g){n.log.fine(this.logger_,V(this,"Error opening Xhr: "+g.message));ya(this,g);return}a=d||"";var f=this.headers.clone();e&&n.structs.forEach(e,function(a,c){f.set(c,
a)});e=n.array.find(f.getKeys(),n.net.XhrIo.isContentTypeHeader_);d=n.global.FormData&&a instanceof n.global.FormData;!n.array.contains(n.net.XhrIo.METHODS_WITH_FORM_DATA,c)||e||d||f.set(n.net.XhrIo.CONTENT_TYPE_HEADER,n.net.XhrIo.FORM_CONTENT_TYPE);f.forEach(function(a,c){this.xhr_.setRequestHeader(c,a)},this);this.responseType_&&(this.xhr_.responseType=this.responseType_);"withCredentials"in this.xhr_&&this.xhr_.withCredentials!==this.withCredentials_&&(this.xhr_.withCredentials=this.withCredentials_);
try{za(this),0<this.timeoutInterval_&&(this.useXhr2Timeout_=n.net.XhrIo.shouldUseXhr2Timeout_(this.xhr_),n.log.fine(this.logger_,V(this,"Will abort after "+this.timeoutInterval_+"ms if incomplete, xhr2 "+this.useXhr2Timeout_)),this.useXhr2Timeout_?(this.xhr_[n.net.XhrIo.XHR2_TIMEOUT_]=this.timeoutInterval_,this.xhr_[n.net.XhrIo.XHR2_ON_TIMEOUT_]=n.bind(this.timeout_,this)):this.timeoutId_=n.Timer.callOnce(this.timeout_,this.timeoutInterval_,this)),n.log.fine(this.logger_,V(this,"Sending request")),
this.inSend_=!0,this.xhr_.send(a),this.inSend_=!1}catch(g){n.log.fine(this.logger_,V(this,"Send error: "+g.message)),ya(this,g)}};n.net.XhrIo.shouldUseXhr2Timeout_=function(a){return n.userAgent.IE&&n.userAgent.isVersionOrHigher(9)&&n.isNumber(a[n.net.XhrIo.XHR2_TIMEOUT_])&&n.isDef(a[n.net.XhrIo.XHR2_ON_TIMEOUT_])};n.net.XhrIo.isContentTypeHeader_=function(a){return n.string.caseInsensitiveEquals(a)};
n.net.XhrIo.prototype.timeout_=function(){"undefined"!=typeof n&&this.xhr_&&(this.lastError_="Timed out after "+this.timeoutInterval_+"ms, aborting",n.log.fine(this.logger_,V(this,this.lastError_)),this.dispatchEvent(n.net.EventType.TIMEOUT),this.abort(n.net.ErrorCode.TIMEOUT))};var ya=function(a,c){a.active_=!1;a.xhr_&&(a.inAbort_=!0,a.xhr_.abort(),a.inAbort_=!1);a.lastError_=c;Aa(a);W(a)},Aa=function(a){a.errorDispatched_||(a.errorDispatched_=!0,a.dispatchEvent(n.net.EventType.COMPLETE),a.dispatchEvent(n.net.EventType.ERROR))};
n.net.XhrIo.prototype.abort=function(){this.xhr_&&this.active_&&(n.log.fine(this.logger_,V(this,"Aborting")),this.active_=!1,this.inAbort_=!0,this.xhr_.abort(),this.inAbort_=!1,this.dispatchEvent(n.net.EventType.COMPLETE),this.dispatchEvent(n.net.EventType.ABORT),W(this))};n.net.XhrIo.prototype.disposeInternal=function(){this.xhr_&&(this.active_&&(this.active_=!1,this.inAbort_=!0,this.xhr_.abort(),this.inAbort_=!1),W(this,!0));n.net.XhrIo.superClass_.disposeInternal.call(this)};
n.net.XhrIo.prototype.onReadyStateChange_=function(){if(!this.isDisposed())if(this.inOpen_||this.inSend_||this.inAbort_)Ba(this);else this.onReadyStateChangeEntryPoint_()};n.net.XhrIo.prototype.onReadyStateChangeEntryPoint_=function(){Ba(this)};
var Ba=function(a){if(a.active_&&"undefined"!=typeof n)if(a.xhrOptions_[n.net.XmlHttp.OptionType.LOCAL_REQUEST_ERROR]&&X(a)==n.net.XmlHttp.ReadyState.COMPLETE&&2==Y(a))n.log.fine(a.logger_,V(a,"Local request error detected and ignored"));else if(a.inSend_&&X(a)==n.net.XmlHttp.ReadyState.COMPLETE)n.Timer.callOnce(a.onReadyStateChange_,0,a);else if(a.dispatchEvent(n.net.EventType.READY_STATE_CHANGE),Z(a)){n.log.fine(a.logger_,V(a,"Request complete"));a.active_=!1;try{if(a.isSuccess())a.dispatchEvent(n.net.EventType.COMPLETE),
a.dispatchEvent(n.net.EventType.SUCCESS);else{try{var c=X(a)>n.net.XmlHttp.ReadyState.LOADED?a.xhr_.statusText:""}catch(d){n.log.fine(a.logger_,"Can not get status: "+d.message),c=""}a.lastError_=c+" ["+Y(a)+"]";Aa(a)}}finally{W(a)}}};
n.net.XhrIo.prototype.onProgressHandler_=function(a,c){n.asserts.assert(a.type===n.net.EventType.PROGRESS,"goog.net.EventType.PROGRESS is of the same type as raw XHR progress.");this.dispatchEvent(n.net.XhrIo.buildProgressEvent_(a,n.net.EventType.PROGRESS));this.dispatchEvent(n.net.XhrIo.buildProgressEvent_(a,c?n.net.EventType.DOWNLOAD_PROGRESS:n.net.EventType.UPLOAD_PROGRESS))};n.net.XhrIo.buildProgressEvent_=function(a,c){return{type:c,lengthComputable:a.lengthComputable,loaded:a.loaded,total:a.total}};
var W=function(a,c){if(a.xhr_){za(a);var d=a.xhr_,e=a.xhrOptions_[n.net.XmlHttp.OptionType.USE_NULL_FUNCTION]?n.nullFunction:null;a.xhr_=null;a.xhrOptions_=null;c||a.dispatchEvent(n.net.EventType.READY);try{d.onreadystatechange=e}catch(f){n.log.error(a.logger_,"Problem encountered resetting onreadystatechange: "+f.message)}}},za=function(a){a.xhr_&&a.useXhr2Timeout_&&(a.xhr_[n.net.XhrIo.XHR2_ON_TIMEOUT_]=null);a.timeoutId_&&(n.Timer.clear(a.timeoutId_),a.timeoutId_=null)},Z=function(a){return X(a)==
n.net.XmlHttp.ReadyState.COMPLETE};n.net.XhrIo.prototype.isSuccess=function(){var a=Y(this),c;if(!(c=n.net.HttpStatus.isSuccess(a))){if(a=0===a)a=n.uri.utils.getEffectiveScheme(String(this.lastUri_)),a=!n.net.XhrIo.HTTP_SCHEME_PATTERN.test(a);c=a}return c};var X=function(a){return a.xhr_?a.xhr_.readyState:n.net.XmlHttp.ReadyState.UNINITIALIZED},Y=function(a){try{return X(a)>n.net.XmlHttp.ReadyState.LOADED?a.xhr_.status:-1}catch(c){return-1}};
n.net.XhrIo.prototype.getResponseHeader=function(a){if(this.xhr_&&Z(this))return a=this.xhr_.getResponseHeader(a),n.isNull(a)?void 0:a};n.net.XhrIo.prototype.getAllResponseHeaders=function(){return this.xhr_&&Z(this)?this.xhr_.getAllResponseHeaders()||"":""};var V=function(a,c){return c+" ["+a.lastMethod_+" "+a.lastUri_+" "+Y(a)+"]"};n.debug.entryPointRegistry.register(function(a){n.net.XhrIo.prototype.onReadyStateChangeEntryPoint_=a(n.net.XhrIo.prototype.onReadyStateChangeEntryPoint_)});n.craw.AppAuthenticator=function(){};n.craw.AppAuthenticator.UBERAUTH_URL_="https://www.google.com/accounts/OAuthLogin?issueuberauth=1";n.craw.AppBackground=function(){this.delegate_=n.craw.AppBackgroundDelegate.createImpl();chrome.app.runtime.onLaunched.addListener(n.bind(this.launchWindow_,this,null))};n.craw.AppBackground.prototype.launchWindow_=function(a){null!==a&&chrome.app.window.create("/html/craw_window.html",{id:"",bounds:this.delegate_.getWindowBounds(),frame:"chrome",hidden:!1},function(c){c.contentWindow.crawConfig_=new n.craw.WindowConfig(a,!0)})};window.onload=function(){new n.craw.AppBackground};
