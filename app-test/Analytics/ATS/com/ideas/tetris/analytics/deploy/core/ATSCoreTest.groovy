package com.ideas.tetris.analytics.deploy.core

class atsCore{

				def env = System.getenv()
				int fileCOU = 100
				int stepCOU=100
				String file = "100"
				int inc = 1
				String sep = "\\"
				def steplist=[]
				String stepG =""

				def metadataL = new XmlSlurper().parse("ats_properties.xml")
				def setlocallist=metadataL.properties	 
				String ats_repodir="${setlocallist.ATS_REPODIR.text()}"
				String ats_homedir="${setlocallist.ATS_HOMEDIR.text()}"
				String ats_utilsdir="${setlocallist.ATS_UTILSDIR.text()}"
				String ats_homedrive="${setlocallist.ATS_HOMEDRIVE.text()}"
				String ats_rhomedir="${setlocallist.ATS_rHOMEDIR.text()}"
				String ats_rhomedrive="${setlocallist.ATS_rHOMEDRIVE.text()}"
				String ats_settingsfile="${setlocallist.ATS_SETTINGSFILE.text()}"
				String ats_sasdir="${setlocallist.ATS_SASDIR.text()}"
				String ats_tooldir="${setlocallist.ATS_TOOLDIR.text()}"
				String ats_mssql_data="${setlocallist.ATS_MSSQL_DATA.text()}"
				String ats_mssql_log="${setlocallist.ATS_MSSQL_LOG.text()}"
				String testingType = "${setlocallist.ATS_TESTING_TYPE.text()}"
				String m2_home = "${setlocallist.m2_home.text()}"					
				String ats_appVersion="${setlocallist.APPVERSION.text()}"
				String ats_sasexe = "${setlocallist.ATS_SASEXE.text()}"
				
				String sqlcmd="${setlocallist.sqlcmd.text()}"
				String sqlcmdPS="${setlocallist.sqlcmdPS.text()}"
				String atsurl="${setlocallist.ATS_URL.text()}"
				def metadataG = new XmlSlurper().parse("${ats_settingsfile}")
				def setlist=metadataG.profiles.profile.properties
				def remotepwd = "${setlocallist.remotepassword.text()}"		
				def Server = "${setlocallist.server.text()}"
				def UserName = "${setlocallist.remoteUserName.text()}"	
				
				public String resultdir = 'test'
				public String packagename

				public String masterdir=env['ATS_MS_DYN_DIR']
				public String datadir=env['ATS_DATA_DYN_DIR']
				public String reportsdir=env['ATS_REPORTS_DYN_DIR']
				
				String resultdir_unix
				String datadir_unix
				String ats_homedir_unix
				String ats_repodir_unix
				String ats_rhomedir_unix
				
				public setVariables(args) {
					packagename= (args)
					resultdir = (args).replace(".","\\").replace("package ","rs\\")
					masterdir = (resultdir).replace("rs","ms")
					datadir = (resultdir).replace("rs","data")
					reportsdir = (resultdir).replace("rs","reports")

					resultdir_unix=(resultdir).replace("\\","/")
					datadir_unix=(datadir).replace("\\","/")
					ats_homedir_unix=(ats_homedir).replace("\\","/")
					ats_rhomedir_unix=(ats_rhomedir).replace("\\","/")
					ats_repodir_unix=(ats_repodir).replace("\\","/")
			
					resultdir = "${ats_homedir}${sep}${resultdir}"
					masterdir = "${ats_homedir}${sep}${masterdir}"
					datadir = "${ats_rhomedir}${sep}${datadir}"
					reportsdir = "${ats_homedir}${sep}${reportsdir}"

					"cmd /c rd /s /q ${resultdir}".execute().waitFor();					
					"cmd /c mkdir ${resultdir}".execute();
					"cmd /c rd /s /q ${datadir}".execute().waitFor();
					"cmd /c mkdir ${datadir}".execute();
					"cmd /c mkdir ${masterdir}".execute();
					"cmd /c rd /s /q ${reportsdir}".execute().waitFor();
					"cmd /c mkdir ${reportsdir}".execute();
					
					return (resultdir)

				}

				public changePackage(args) {
					packagename= (args)
					resultdir = (args).replace(".","\\").replace("package ","rs\\")
					masterdir = (resultdir).replace("rs","ms")
					datadir = (resultdir).replace("rs","data")
					reportsdir = (resultdir).replace("rs","reports")

					resultdir_unix=(resultdir).replace("\\","/")
					datadir_unix=(datadir).replace("\\","/")
					ats_homedir_unix=(ats_homedir).replace("\\","/")
					ats_rhomedir_unix=(ats_rhomedir).replace("\\","/")
					ats_repodir_unix=(ats_repodir).replace("\\","/")
			
					resultdir = "${ats_homedir}${sep}${resultdir}"
					masterdir = "${ats_homedir}${sep}${masterdir}"
					datadir = "${ats_rhomedir}${sep}${datadir}"
					reportsdir = "${ats_homedir}${sep}${reportsdir}"

					"cmd /c mkdir ${resultdir}".execute();
					"cmd /c mkdir ${datadir}".execute();
					"cmd /c mkdir ${masterdir}".execute();
					"cmd /c mkdir ${reportsdir}".execute();
					fileCOU = 100
					stepCOU=100
					steplist=[]
					return (resultdir)

				}

				public start(){
					def BAT = new File("$resultdir\\scriptTest.bat")
					BAT.write """
					@echo
					setlocal EnableDelayedExpansion
					set ATS_DATE=%date:~4,2%%date:~7,2%%time:~0,2%%time:~3,2%
					set Calib_date=%date:~-4,4%%date:~-10,2%%date:~-7,2%
					set path=%path%;C:\\Program Files\\Microsoft SQL Server\\100\\Tools\\Binn;${ats_homedir}\\utils;
					set sqlcmd=${sqlcmd}
					set envHost=${Server}
					set m2_home=${m2_home}
					set testingType=${testingType}
					"""
					def INDX = new File("$resultdir\\index.html")
					INDX.write """
<html><body>
<h2>Table Level Differences at STEP Level and SUMMARY Level<h2><br>
					"""
				}

				
				public chkpt(String args) {
					def (suffix)=[args]
					fileCOU= fileCOU+1;
					file=fileCOU;
					if (suffix) {	file=fileCOU+suffix }
					println file
				}

				public chkpt(String suffix, String step) {
					if (!(steplist).contains(step)){
						steplist.add(step)
						fileCOU = stepCOU		
					}
					"cmd /c mkdir ${resultdir}$sep$step".execute().waitFor();
					"cmd /c mkdir ${datadir}$sep$step".execute().waitFor();
					"cmd /c mkdir ${reportsdir}$sep$step".execute().waitFor();
					fileCOU= fileCOU+1;
					file=fileCOU;
					if (suffix) {	file=fileCOU+suffix }
					stepG = "$step"
					file="$step$sep$file"
					println file

				}

				public System(String args) {
					def BAT = new File("$resultdir\\scriptTest.bat")
					def String instr = (args).replace("\\","\\\\")	
					BAT.append "${args} && echo on\n"
					inc = inc + 1
				}

				public SystemPS(String Server, String User,String args) {
					def String cmd
					if ("${User}") {
					cmd ='call powershell -command "$pwd =  ConvertTo-SecureString '+"${remotepwd}"+' -AsPlainText -Force;$cred = New-Object System.Management.Automation.PSCredential (\\\"'+"${User}"+'\\\" ,$pwd);$session = New-PsSession -ComputerName '+"${Server}"+'.ideasdev.int -Credential $cred;invoke-command -session $session '+" -scriptblock {${args}}\""
					} 
					else {
					cmd ="call powershell invoke-command -computername ${Server} -scriptblock {${args}}"
					}
//					println cmd
					System("${cmd}")
				}

				
			public SystemPSFile(String Server, String User,String args) {
					def INP = new File("${resultdir}${sep}${file}.ps1")
					//INP.append("import-module .\\utils\\cmdlets4sas;")
					INP.append("${args}\n")	 			
					def String cmd
					if ("${User}") {
					System("copy /y ${resultdir}${sep}${file}.ps1 ${datadir}${sep}${file}.ps1")
					cmd ='call powershell -command "$pwd =  ConvertTo-SecureString '+"${remotepwd}"+' -AsPlainText -Force;$cred = New-Object System.Management.Automation.PSCredential (\\\"'+"${User}"+'\\\" ,$pwd);$session = New-PsSession -ComputerName '+"${Server}"+'.ideasdev.int -Credential $cred;invoke-command -session $session '+" -File ${datadir}${sep}${file}.ps1 \""
					} 
					else {
					cmd ="call powershell  -File ${resultdir}${sep}${file}.ps1"
					}
					
//					cmd ="call powershell  -File ${resultdir}${sep}${file}.ps1"
//					println cmd
					System("${cmd}")
				}


			public SystemPSFileProcess(String Filepath, String workingdir,String args) {
					runSas(Filepath,workingdir,args) 

				}
				public System(String args,String outfile, String infile) {

					def BAT = new File("$resultdir\\scriptTest.bat")
					def String instr = (args).replace("\\","\\\\")	
					BAT.append "${args} > ${outfile} \n"
					inc = inc + 1
				}
				
				
				public compareAll() {
				
				if( "${testingType}" == "regression" ) {
					def INP = new File("${resultdir}${sep}report.cmp")
		//			println INP
					INP.append """
					load $masterdir $resultdir
					expand all
					compare rules-based
					folder-report layout:side-by-side options:column-size,column-version,column-timestamp &
					output-to:${reportsdir}${sep}report.html output-options:html-color 
					"""
					System("call bc2 @${resultdir}${sep}report.cmp")
					
					def INDX = new File("$resultdir\\index.html")
					INDX.append """
					<a href=$atsurl/$resultdir_unix/report.html>Summary of Changes</a><br>
					"""
					}
				}
				public compareFiles() {
				if ( "${testingType}" == "regression" ) {
					System("for /f %%i in ('dir /b ${resultdir}\\${stepG}') do call bc2 @utils\\bcfiles.bat ${masterdir}\\${stepG} ${resultdir}\\${stepG} ${reportsdir}\\${stepG} %%i")
					
					}
				}


				public dumpSASStats(String out, String classby ) {
					
					def INP = new File("${resultdir}${sep}${file}.sas")
					INP.append """
					proc import datafile="${resultdir}${sep}${file}.csv"
					out=${out}
					dbms=DLM
					replace;
					delimiter=';';
					datarow=3;
					getnames=yes;
					run;

					proc means data=work.${out} noprint;
					class ${classby};
					output out=${out}_stat;
					output out=${out}_sum sum=;
					run;
					
					data ${out}_stat;
					set ${out}_stat ${out}_sum(in=in2);
					if in2 then _STAT_ = 'SUM';
					run;
					
					PROC EXPORT DATA= work.${out}_stat
					OUTFILE= "${resultdir}${sep}${file}_${out}_stat.csv" 
					DBMS=CSV REPLACE;
					PUTNAMES=YES;
					RUN;
					
					"""
						def INPS = new File("${resultdir}${sep}${file}.ps1")
						
					INPS.append """
					Start-process -FilePath sas -ArgumentList \"-config c:\\sas\\config\\sasv9.cfg -sysin  ${resultdir}${sep}${file}.sas\" -wait
					"""
					System("call powershell  -File ${resultdir}${sep}${file}.ps1")
					
				}

			public runSas(String Filepath, String workingdir,String args) {
					def User="${UserName}"
					def INP = new File("${resultdir}${sep}${file}.ps1")
					INP.append("Start-process \'${Filepath}\' -WorkingDirectory \'$workingdir\' -ArgumentList \'${args}\' -Wait\n")	
					def String cmd
					if ("${User}") {
					System("copy /y ${resultdir}${sep}${file}.ps1 ${datadir}${sep}${file}.ps1")
					cmd ='call powershell -command "$pwd =  ConvertTo-SecureString '+"${remotepwd}"+' -AsPlainText -Force;$cred = New-Object System.Management.Automation.PSCredential (\\\"'+"${User}"+'\\\" ,$pwd);$session = New-PsSession -ComputerName '+"${Server}"+'.ideasdev.int -Credential $cred;invoke-command -session $session '+" -File ${datadir}${sep}${file}.ps1 \""
					} 
					else {
					cmd ="call powershell  -File ${resultdir}${sep}${file}.ps1"
					}
					
//					cmd ="call powershell  -File ${resultdir}${sep}${file}.ps1"
//					println cmd
					System("${cmd}")				
			
			
			}				
				public sqlcmd(args) {
					def ele=(args)
					def bin=ele[0]
					def end=ele.size()
					def INP = new File("${resultdir}${sep}${file}.inp")
					INP.write("--geneated by automated tool\n")
					INP.write("set ANSI_WARNINGS OFF \n")
					for (i in ele[1..end-1]){
					INP.append("${i}\n")		}
					//	INP.close()
					System("$bin  -i ${resultdir}${sep}${file}.inp -o  ${resultdir}${sep}${file}.csv")

				}	

				public dumpPacman(String pid, String regexp) {
				SystemPSFile("${Server}","","""Add-PSSnapin SqlServerCmdletSnapin100;Add-PSSnapin SqlServerProviderSnapin100
				foreach (\$table in invoke-sqlcmd �ServerInstance "${Server}" -Username G3Services -Password IDeaS123  -query \"SELECT table_name FROM INFORMATION_SCHEMA.TABLES where table_name like '${regexp}'\"  -database "${pid}" ${sqlcmdPS}) {
					\$table.table_name; Invoke-Sqlcmd ${sqlcmdPS} �ServerInstance "${Server}" -Username G3Services -Password IDeaS123  -Database "${pid}"  -Query \"select * from \$(\$table.table_name)\" | Export-csv ${resultdir}\\${stepG}\\\$(\$table.table_name).csv -Notypeinformation}""")
				}
				
				public polljob(String bin,String param1) {

					def INP = new File("${resultdir}\\${file}.sql")
					INP.write("--geneated by automated tool\n")
					INP.write """
		
					use job 
					declare @exitcode nvarchar(100) 
					declare @instanceId int 
					declarte @jobId int 
					select @jobId = \$(jobId) 
					select @instanceId = (select job_instance_id from JOB_EXECUTION where JOB_EXECUTION_ID =@jobId )
					while EXISTS( select @instanceId )

					BEGIN
					start:
					set @exitcode = (select top 1 EXIT_CODE   from JOB_EXECUTION where JOB_INSTANCE_ID =@instanceId order by LAST_UPDATED desc)
					IF (@exitcode = 'COMPLETED')
					BREAK
					ELSE
					waitfor delay '00:00:10'
					goto start
					END
					select @exitcode,@instanceId,@jobId
					"""
					System("${bin} -v %${param1}% < ${resultdir}\\${file}.sql ")

				}	
				
			public sqlvdb_attach(String location, String pid) {
					def String cmd
					if ("${UserName}") {
					cmd ="call ${ats_utilsdir}\\paexec \\\\${Server} -u ${UserName} -p ${remotepwd} -s cmd.exe /c \"sqlvdbcmd create ${pid} ${location}${sep}${pid}.bak -Replace\" "
					} 
					else {
					cmd ="call ${ats_utilsdir}\\paexec -s cmd.exe /c \"sqlvdbcmd create ${pid} ${location}${sep}${pid}.bak  -Replace\" "
					}
					
					System("${cmd}")
				}				
				
				
				public mvn_detach(args)  {
					def ele=(args)
				//	println ele
					def INP = new File("${resultdir}${sep}${file}.inp")
					INP.write("--ba\n")
					INP.write("""\
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
<modelVersion>4.0.0</modelVersion>
	<groupId>com.ideas.tetris.analytics.deploy.test.detach</groupId>
	<artifactId>pacman-db-tenant-analyticaldata</artifactId>
	<packaging>pom</packaging>
	<name>tetris::analytics::deploy::db::pacman-db-tenant-analyticaldata</name>
	<version>1.0</version>
			<build>
				<plugins>
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>sql-maven-plugin</artifactId>
						<version>1.3</version>
						<dependencies>
							<dependency>
						<groupId>com.microsoft.sqlserver</groupId>
                        <artifactId>mssql-jdbc</artifactId>
                        <version>8.4.0.jre8</version>
							</dependency>
						</dependencies>
						<executions>
							<execution>
								<id>detach-analyticaldb</id>
								<phase>pre-integration-test</phase>
								<goals>
									<goal>execute</goal>
								</goals>
								<configuration>
									<driver>\${db.driver}</driver>
									<url>jdbc:sqlserver://\${mssqlHost}:\${mssqlPort}</url>
									<username>\${mssqlUser}</username>
									<password>\${mssqlPassword}</password>
									<autocommit>true</autocommit>
									<sqlCommand>
									USE [master];
								        BEGIN							
								declare @propId nvarchar(50)
								declare @sql nvarchar(500)
								begin
								select @propId='$ele'
					                        IF EXISTS(SELECT 1 FROM master.dbo.sysdatabases WHERE name =@propId )
									set @sql=
											'ALTER DATABASE ['+@propId+'] SET SINGLE_USER WITH ROLLBACK IMMEDIATE'
 									exec(@sql)

									IF EXISTS(SELECT 1 FROM master.dbo.sysdatabases WHERE name =@propId)
									set @sql=
									'drop database ['+@propId+']'
 									exec(@sql)

								end
								END;

									</sqlCommand>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>

</project>



					""")



							System("call %m2_home%\\bin\\mvn clean install -s ${ats_settingsfile} -DenvHost=%envHost%  -f ${resultdir}${sep}${file}.inp  ")

				}

				public mvn_offline(args)  {
					def ele=(args)
				//	println ele
					def INP = new File("${resultdir}${sep}${file}.inp")
					INP.write("--ba\n")
					INP.write("""\
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
<modelVersion>4.0.0</modelVersion>
	<groupId>com.ideas.tetris.analytics.deploy.test.detach</groupId>
	<artifactId>pacman-db-tenant-analyticaldata</artifactId>
	<packaging>pom</packaging>
	<name>tetris::analytics::deploy::db::pacman-db-tenant-analyticaldata</name>
	<version>1.0</version>
			<build>
				<plugins>
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>sql-maven-plugin</artifactId>
						<version>1.3</version>
						<dependencies>
							<dependency>
						<groupId>com.microsoft.sqlserver</groupId>
                        <artifactId>mssql-jdbc</artifactId>
                        <version>8.4.0.jre8</version>
							</dependency>
						</dependencies>
						<executions>
							<execution>
								<id>detach-analyticaldb</id>
								<phase>pre-integration-test</phase>
								<goals>
									<goal>execute</goal>
								</goals>
								<configuration>
									<driver>\${db.driver}</driver>
									<url>jdbc:sqlserver://\${mssqlHost}:\${mssqlPort}</url>
									<username>\${mssqlUser}</username>
									<password>\${mssqlPassword}</password>
									<autocommit>true</autocommit>
									<sqlCommand>
										USE [master];
										
								BEGIN
							
								declare @propId nvarchar(50)
								declare @sql nvarchar(500)

								begin
									select @propId='$ele'

									IF EXISTS(SELECT 1 FROM master.dbo.sysdatabases WHERE name =@propId )
									set @sql=
											'DBCC SHRINKDATABASE(['+@propId+'] )'
 									exec(@sql)
									
									IF EXISTS(SELECT 1 FROM master.dbo.sysdatabases WHERE name =@propId )
									set @sql=
											'ALTER DATABASE ['+@propId+'] SET SINGLE_USER WITH ROLLBACK IMMEDIATE'
 									exec(@sql)

									IF EXISTS(SELECT 1 FROM master.dbo.sysdatabases WHERE name =@propId)
									set @sql=
									'EXEC master.dbo.sp_detach_db @dbname = ['+@propId+'], @skipchecks = "false" '
 									exec(@sql)

								end
								END;

									</sqlCommand>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>

</project>



					""")



							System("call %m2_home%\\bin\\mvn clean install -s ${ats_settingsfile} -DenvHost=%envHost%  -f ${resultdir}${sep}${file}.inp  ")

				}

				public mvn_addPropToG3(String client, String pid, String sid, String pcode)  {
					def INP = new File("${resultdir}${sep}${file}.inp")	
					INP.write("--ba\n")
INP.write("""\
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
<modelVersion>4.0.0</modelVersion>
	<groupId>com.ideas.tetris.analytics.deploy.test.attach</groupId>
	<artifactId>pacman-db-tenant-analyticaldata</artifactId>
	<packaging>pom</packaging>
	<name>tetris::analytics::deploy::db::pacman-db-tenant-analyticaldata</name>
	<version>1.0</version>
			<build>
				<plugins>  				
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>sql-maven-plugin</artifactId>
						<version>1.3</version>
						<dependencies>
							<dependency>
						<groupId>com.microsoft.sqlserver</groupId>
                        <artifactId>mssql-jdbc</artifactId>
                        <version>8.4.0.jre8</version>
							</dependency>
						</dependencies>
						<executions>
							<execution>
								<id>add Property to G3</id>
								<phase>pre-integration-test</phase>
								<goals>
									<goal>execute</goal>
								</goals>
								<configuration>
									<driver>\${db.driver}</driver>
									<url>jdbc:sqlserver://\${mssqlHost}:\${mssqlPort}</url>
									<username>\${mssqlUser}</username>
									<password>\${mssqlPassword}</password>
									<autocommit>true</autocommit>
									<sqlCommand>
										USE [global];
										
							BEGIN
							
								
									-- local variables
								declare @client_name nvarchar(50) --> name of the new client being added
								declare @property_code nvarchar(50) --> property code associated with the client
								declare @property_name nvarchar(150) --> description of the property
								declare @property_id int --> used for setting property_ids manually
								declare @dbname nvarchar(50) --> used for setting property_ids manually
								declare @sas_server_name nvarchar(150) -- used for setting SAS_File_Loc entry
								declare @dbloc_id int --> used for extracting the dbloc entry just added
								declare @client_id int --> used for extracting client id

								select @client_name='${client}'
								select @property_code='${pcode}'
								select @property_name='${pcode}'
								select @property_id ='${pid}'
								select @dbname ='${pid}'
								select @sas_server_name='${Server}.\${envDomain}'
	

								-- step 1 --> setup the client
								IF NOT EXISTS (SELECT 1 FROM [dbo].[Client] where [Client_Code] = @client_name)
								BEGIN
								insert into [dbo].[client] ( [client_code], [client_name], [status_id], [createdate]) 
									values (  @client_name, @client_name,  1, getdate() )
								END

								IF NOT EXISTS (SELECT 1 FROM [dbo].[dbloc] where [DBName]  = @dbname )
								BEGIN
								-- step 2 --> add dbloc entry
								insert into dbloc values
									(
										'${pid}',
										'${Server}.\${envDomain}',
										'G3SQL01',
										1433,
										'java:jboss/datasources/PacmanTenantDS1',
										1,
										4,
										getdate(),
										'java:jboss/datasources/Reports/PacmanTenantDS1'	
									)
								END


								-- step 3 --> add property entry

								IF NOT EXISTS (SELECT 1 FROM [dbo].[Property] where [Property_ID] = @property_id)
								BEGIN
								-- extract the client_id
								select @client_id = client_id from dbo.client where client_code = @client_name

								-- extract the dbloc_id
								select @dbloc_id = dbloc_id from dbloc where dbname = @dbname 

								-- we will have to hardcode the property id for specific reasons.	
								set identity_insert [dbo].[property] on
								insert into [dbo].[property] ([property_id], [client_id], [property_code], [property_name], [status_id], [Created_DTTM] ,[DBLoc_ID],Stage   )
								values ( @property_id, @client_id, @property_code, @property_name, 1, getdate(),@dbloc_id ,'ONE_WAY' )
								set identity_insert [dbo].[property] off

								-- step 4 --> add sas file location for the property	
								insert into [dbo].[sas_file_loc] ( [property_id], [sas_file_location_path], [status_id], [createdate],SAS_Server_Name ) 
								values (  @property_id, 'sas\\data\\properties\\' + cast(@property_id as varchar(15)),  1, getdate(), @sas_server_name )
								END
							
							END;

									</sqlCommand>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>

</project>
						""")
					System("call %m2_home%\\bin\\mvn clean install -s ${ats_settingsfile} -DenvHost=%envHost% -DappVersion=${ats_appVersion} -f ${resultdir}${sep}${file}.inp ")
					//System("copy /y ${resultdir}\\$client"+".ldif  ${datadir}")
					//System("call c:\\programs\\opends-2.3.0\\bat\\import-ldif.bat -h ${Server} -p 8390 -a -n userRoot -b \"dc=ideas,dc=com\" -D \"cn=Directory Manager\" -w password -X -l ${datadir}\\$client"+".ldif  ")
					//System("call ${ats_utilsdir}\\curl -X DELETE \"http://${Server}:8080/pacman-platformsecurity/rest/cache/clientPropertyCache/clear?propertyId=${sid}\" -u <EMAIL>:password  ")
				}

				
				public mvn_attach(eledir,elefile)  {
//					def metadata = new XmlSlurper().parse("${ats_settingsfile}")
//					def list=metadata.profiles.profile.properties
//					def mssqlHome = (list.mssqlHome.text()).replace("C:","${ats_homedrive}")
					def INP = new File("${resultdir}${sep}${file}.inp")	
					INP.write("--ba\n")
INP.write("""\
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
<modelVersion>4.0.0</modelVersion>
	<groupId>com.ideas.tetris.analytics.deploy.test.attach</groupId>
	<artifactId>pacman-db-tenant-analyticaldata</artifactId>
	<packaging>pom</packaging>
	<name>tetris::analytics::deploy::db::pacman-db-tenant-analyticaldata</name>
	<version>1.0</version>
			<build>
				<plugins>  				
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>sql-maven-plugin</artifactId>
						<version>1.3</version>
						<dependencies>
							<dependency>
						<groupId>com.microsoft.sqlserver</groupId>
                        <artifactId>mssql-jdbc</artifactId>
                        <version>8.4.0.jre8</version>
							</dependency>
						</dependencies>
						<executions>
							<execution>
								<id>mssql-attach-analyticaldb</id>
								<phase>pre-integration-test</phase>
								<goals>
									<goal>execute</goal>
								</goals>
								<configuration>
									<driver>\${db.driver}</driver>
									<url>jdbc:sqlserver://\${mssqlHost}:\${mssqlPort}</url>
									<username>\${mssqlUser}</username>
									<password>\${mssqlPassword}</password>
									<autocommit>true</autocommit>
									<sqlCommand>
										USE [master];
										
								BEGIN
							
								declare @propId nvarchar(50)
								declare @sql nvarchar(500)

								begin
									select @propId='$elefile'
								
									set @sql='create database ['+@propId++'] ON
									(FILENAME = N''${ats_mssql_data}\\'+@propId+'.mdf''),
									(FILENAME = N''${ats_mssql_log}\\'+@propId+'.ldf'')
									FOR ATTACH;'
 									exec(@sql)

								end
								END;

									</sqlCommand>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>

</project>
						""")
					//	SystemPS("robocopy ${eledir} ${ats_mssql_data}\\data   ${elefile}* ")
					System("call %m2_home%\\bin\\mvn clean install -s ${ats_settingsfile} -DenvHost=%envHost% -DappVersion=${ats_appVersion} -f ${resultdir}${sep}${file}.inp ")

				}

				public mvn_attachfromBak(eledir,elefile)  {
//					def metadata = new XmlSlurper().parse("${ats_settingsfile}")
//					def list=metadata.profiles.profile.properties
//					def mssqlHome = (list.mssqlHome.text()).replace("C:","${ats_homedrive}")
					def database="$elefile"
					def INP = new File("${resultdir}${sep}${file}.inp")	
					INP.write("--ba\n")
INP.write("""\
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
<modelVersion>4.0.0</modelVersion>
	<groupId>com.ideas.tetris.analytics.deploy.test.attach</groupId>
	<artifactId>pacman-db-tenant-analyticaldata</artifactId>
	<packaging>pom</packaging>
	<name>tetris::analytics::deploy::db::pacman-db-tenant-analyticaldata</name>
	<version>1.0</version>
			<build>
				<plugins>  				
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>sql-maven-plugin</artifactId>
						<version>1.3</version>
						<dependencies>
							<dependency>
						<groupId>com.microsoft.sqlserver</groupId>
                        <artifactId>mssql-jdbc</artifactId>
                        <version>8.4.0.jre8</version>
							</dependency>
						</dependencies>
						<executions>
							<execution>
								<id>mssql-attach-analyticaldb from Bak file</id>
								<phase>pre-integration-test</phase>
								<goals>
									<goal>execute</goal>
								</goals>
								<configuration>
									<driver>\${db.driver}</driver>
									<url>jdbc:sqlserver://\${mssqlHost}:\${mssqlPort}</url>
									<username>\${mssqlUser}</username>
									<password>\${mssqlPassword}</password>
									<autocommit>true</autocommit>
									<mssqlDataDir>\${mssqlDataDir}</mssqlDataDir>
									<mssqlLogDir>\${mssqlLogDir}</mssqlLogDir>
									<sqlCommand>
										USE [master];
								BEGIN
								declare @propId nvarchar(50)							
								declare @sql nvarchar(3000)

								begin
									select @propId='${elefile}'
									IF EXISTS(SELECT 1 FROM master.dbo.sysdatabases WHERE name =@propId )
									set @sql=
											'ALTER DATABASE ['+@propId+'] SET SINGLE_USER WITH ROLLBACK IMMEDIATE'
 									exec(@sql)

									IF EXISTS(SELECT 1 FROM master.dbo.sysdatabases WHERE name =@propId)
									set @sql=
									'drop database ['+@propId+']'
 									exec(@sql)
									
									set @sql=
											'RESTORE DATABASE ['+@propId+'] FROM  DISK = N''${eledir}\\${database}.bak'' WITH  FILE = 1,  MOVE N''${database}'' TO N''\${mssqlDataDir}\\${database}.mdf'',  MOVE N''${database}_log'' TO N''\${mssqlLogDir}\\${database}.ldf'',  NOUNLOAD,  STATS = 10'
 									exec(@sql)			
								end
								END;
									</sqlCommand>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>

</project>
						""")

					System("call %m2_home%\\bin\\mvn clean install -s ${ats_settingsfile} -DenvHost=%envHost% -DappVersion=${ats_appVersion} -f ${resultdir}${sep}${file}.inp ")

				}

				public mvn_attachfromZip(eledir,elefile)  {
//					def metadata = new XmlSlurper().parse("${ats_settingsfile}")
//					def list=metadata.profiles.profile.properties
//					def mssqlHome = (list.mssqlHome.text()).replace("C:","${ats_homedrive}")
					def database="$elefile"
					def INP = new File("${resultdir}${sep}${file}.inp")	
					

					INP.write("--ba\n")
INP.write("""\
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
<modelVersion>4.0.0</modelVersion>
	<groupId>com.ideas.tetris.analytics.deploy.test.attach</groupId>
	<artifactId>pacman-db-tenant-analyticaldata</artifactId>
	<packaging>pom</packaging>
	<name>tetris::analytics::deploy::db::pacman-db-tenant-analyticaldata</name>
	<version>1.0</version>
			<build>
				<plugins> 					
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>sql-maven-plugin</artifactId>
						<version>1.3</version>
						<dependencies>
							<dependency>
						<groupId>com.microsoft.sqlserver</groupId>
                        <artifactId>mssql-jdbc</artifactId>
                        <version>8.4.0.jre8</version>
							</dependency>
						</dependencies>
						<executions>
							<execution>
								<id>mssql-attach-analyticaldb from Bak file</id>
								<phase>pre-integration-test</phase>
								<goals>
									<goal>execute</goal>
								</goals>
								<configuration>
									<driver>\${db.driver}</driver>
									<url>jdbc:sqlserver://\${mssqlHost}:\${mssqlPort}</url>
									<username>\${mssqlUser}</username>
									<password>\${mssqlPassword}</password>
									<autocommit>true</autocommit>
									<mssqlDataDir>\${mssqlDataDir}</mssqlDataDir>
									<mssqlLogDir>\${mssqlLogDir}</mssqlLogDir>
									<sqlCommand>
										USE [master];
								BEGIN
								declare @propId nvarchar(50)							
								declare @sql nvarchar(3000)

								begin
									select @propId='${elefile}'
									IF EXISTS(SELECT 1 FROM master.dbo.sysdatabases WHERE name =@propId )
									set @sql=
											'ALTER DATABASE ['+@propId+'] SET SINGLE_USER WITH ROLLBACK IMMEDIATE'
 									exec(@sql)

									IF EXISTS(SELECT 1 FROM master.dbo.sysdatabases WHERE name =@propId)
									set @sql=
									'drop database ['+@propId+']'
 									exec(@sql)
									
									set @sql=
											'RESTORE DATABASE ['+@propId+'] FROM  DISK = N''c:\\temp\\${database}.bak'' WITH  FILE = 1,  MOVE N''${database}'' TO N''\${mssqlDataDir}\\${database}.mdf'',  MOVE N''${database}_log'' TO N''\${mssqlLogDir}\\${database}.ldf'',  NOUNLOAD,  STATS = 10'
 									exec(@sql)			
								end
								END;
									</sqlCommand>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>

</project>
						""")

					SystemPSFileProcess("cmd","c:\\temp", "/c \"C:\\Program Files\\7-Zip\\7z.exe \" x ${eledir}.zip -oc:\\temp ${database}.bak -y")	
					System("call %m2_home%\\bin\\mvn clean install -s ${ats_settingsfile} -DenvHost=%envHost% -DappVersion=${ats_appVersion} -f ${resultdir}${sep}${file}.inp ")

				}
				
				public mvn_fixupgrade(args)  {
				
				mvn_fixupgrade(args,1)
				}
				
				public mvn_fixupgrade(args,method) {
					def ele=(args)
				//	println ele
					def INP = new File("${resultdir}${sep}${file}.inp")
					INP.write("--ba\n")
INP.write("""\
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
<modelVersion>4.0.0</modelVersion>
	<groupId>com.ideas.tetris.analytics.deploy.test.fixupgrade</groupId>
	<artifactId>pacman-db-tenant-analyticaldata</artifactId>
	<packaging>pom</packaging>
	<name>tetris::analytics::deploy::db::pacman-db-tenant-analyticaldata</name>
	<version>1.0</version>
			<build>
				<plugins>
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>sql-maven-plugin</artifactId>
						<version>1.3</version>
						<dependencies>
							<dependency>
						<groupId>com.microsoft.sqlserver</groupId>
                        <artifactId>mssql-jdbc</artifactId>
                        <version>8.4.0.jre8</version>
							</dependency>
						</dependencies>
						<executions>
							<execution>
								<id>fixupgrade-analyticaldb</id>
								<phase>pre-integration-test</phase>
								<goals>
									<goal>execute</goal>
								</goals>
								<configuration>
									<driver>\${db.driver}</driver>
									<url>jdbc:sqlserver://\${mssqlHost}:\${mssqlPort}</url>
									<username>\${mssqlUser}</username>
									<password>\${mssqlPassword}</password>
									<autocommit>true</autocommit>
									<sqlCommand>
										USE [$ele];
										
								BEGIN
			""")
		
			if("${method}" == "1") {

			INP.append("""										
								insert into dbmaintain_scripts values('scripts/incremental/01_revisions/33_iteration_5/08_#ddl-create-ScheduleReport.sql','1374417048000','1ee834478a46f122fc357af5c0f9268c','2013-10-22 15:25:20',1)
								insert into dbmaintain_scripts values('scripts/incremental/01_revisions/33_iteration_5/09_#ddl-alter-ScheduleReport.sql','1374417048000','2dce8f5c3ae818fb11c3536ceef03406','2013-10-22 15:25:20',1)
								insert into dbmaintain_scripts values('scripts/incremental/01_revisions/33_iteration_5/11_#ddl-alter-ScheduleReport.sql','1374417046000','465b9f5d2eb584bf9927312aa9ae3e28','2013-10-22 15:25:20',1)
								update dbo.dbmaintain_scripts set checksum = '6e0e637428bd9aceb02a4ecc8e0f8ba8' where file_name like '%/51_iteration_2/01_#ddl_create_opera_status_mapping.sql'
								update dbo.dbmaintain_scripts set checksum = 'b1a5574bb5da7c915ab21af23e28ed15' where file_name like '%/54_iteration_1/06_#ddl_add_constraints_to_group_pricing_tables.sql'
								update dbo.dbmaintain_scripts set checksum = '12c6c6f65fbe5be88e96d021509c7edc' where file_name like '%/57_iteration_4/14_#ddl_alter_inventory_sharing_rank_AUD_columns.sql'
								update dbo.dbmaintain_scripts set checksum = '728b50fe1dc69fee1c0bcbf232ddc1a3' where file_name like '%/13_iteration_1/11_#ddl_add_default_value_for_rate_qualified_type_id.sql'

			""")
			}
			if("${method}" == "2") {

			INP.append("""		
								insert into dbmaintain_scripts values('scripts/incremental/01_revisions/33_iteration_5/08_#ddl-create-ScheduleReport.sql','1374417048000','1ee834478a46f122fc357af5c0f9268c','2013-10-22 15:25:20',1)
								insert into dbmaintain_scripts values('scripts/incremental/01_revisions/33_iteration_5/09_#ddl-alter-ScheduleReport.sql','1374417048000','2dce8f5c3ae818fb11c3536ceef03406','2013-10-22 15:25:20',1)
								insert into dbmaintain_scripts values('scripts/incremental/01_revisions/33_iteration_5/11_#ddl-alter-ScheduleReport.sql','1374417046000','465b9f5d2eb584bf9927312aa9ae3e28','2013-10-22 15:25:20',1)
								insert into dbmaintain_scripts values('scripts/incremental/01_revisions/87_iteration_2/04_ddl_rename_dashboard_override_details_ceiling_columns.sql','1374417046000','7e1601585399e3fc6d6c87c534549128','2013-10-22 15:25:20',1)
								update dbo.dbmaintain_scripts set checksum = '6e0e637428bd9aceb02a4ecc8e0f8ba8' where file_name like '%/51_iteration_2/01_#ddl_create_opera_status_mapping.sql'
								update dbo.dbmaintain_scripts set checksum = 'b1a5574bb5da7c915ab21af23e28ed15' where file_name like '%/54_iteration_1/06_#ddl_add_constraints_to_group_pricing_tables.sql'
								update dbo.dbmaintain_scripts set checksum = '12c6c6f65fbe5be88e96d021509c7edc' where file_name like '%/57_iteration_4/14_#ddl_alter_inventory_sharing_rank_AUD_columns.sql'
								update dbo.dbmaintain_scripts set checksum = '728b50fe1dc69fee1c0bcbf232ddc1a3' where file_name like '%/13_iteration_1/11_#ddl_add_default_value_for_rate_qualified_type_id.sql'

			""")
			}
			INP.append("""								
								END;

									</sqlCommand>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>

</project>
					""")

						System("call %m2_home%\\bin\\mvn clean install -s ${ats_settingsfile} -DenvHost=%envHost% -DappVersion=${ats_appVersion} -f ${resultdir}${sep}${file}.inp ")			
				}

				public mvn_fixupgraderemove(args)  {
					mvn_fixupgraderemove(args,1)
				}

				
				public mvn_fixupgraderemove(args,method) {
					def ele=(args)
				//	println ele
					def INP = new File("${resultdir}${sep}${file}.inp")
					INP.write("--ba\n")
INP.write("""\
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
<modelVersion>4.0.0</modelVersion>
	<groupId>com.ideas.tetris.analytics.deploy.test.fixupgrade</groupId>
	<artifactId>pacman-db-tenant-analyticaldata</artifactId>
	<packaging>pom</packaging>
	<name>tetris::analytics::deploy::db::pacman-db-tenant-analyticaldata</name>
	<version>1.0</version>
			<build>
				<plugins>
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>sql-maven-plugin</artifactId>
						<version>1.3</version>
						<dependencies>
							<dependency>
						<groupId>com.microsoft.sqlserver</groupId>
                        <artifactId>mssql-jdbc</artifactId>
                        <version>8.4.0.jre8</version>
							</dependency>
						</dependencies>
						<executions>
							<execution>
								<id>fixupgrade-analyticaldb</id>
								<phase>pre-integration-test</phase>
								<goals>
									<goal>execute</goal>
								</goals>
								<configuration>
									<driver>\${db.driver}</driver>
									<url>jdbc:sqlserver://\${mssqlHost}:\${mssqlPort}</url>
									<username>\${mssqlUser}</username>
									<password>\${mssqlPassword}</password>
									<autocommit>true</autocommit>
									<sqlCommand>
										USE [$ele];
										
								BEGIN
			""")
		
			if("${method}" == "1") {

			INP.append("""										
								delete from dbmaintain_scripts where file_name like 'scripts/incremental/01_revisions/33_iteration_5/08_#ddl-create-ScheduleReport.sql'
								delete from dbmaintain_scripts where file_name like 'scripts/incremental/01_revisions/33_iteration_5/09_#ddl-alter-ScheduleReport.sql'
								delete from dbmaintain_scripts where file_name like 'scripts/incremental/01_revisions/33_iteration_5/11_#ddl-alter-ScheduleReport.sql'
			""")
			}
			if("${method}" == "2") {

			INP.append("""		
								delete from dbmaintain_scripts where file_name like 'scripts/incremental/01_revisions/33_iteration_5/08_#ddl-create-ScheduleReport.sql'
								delete from dbmaintain_scripts where file_name like 'scripts/incremental/01_revisions/33_iteration_5/09_#ddl-alter-ScheduleReport.sql'
								delete from dbmaintain_scripts where file_name like 'scripts/incremental/01_revisions/33_iteration_5/11_#ddl-alter-ScheduleReport.sql'
								delete from dbmaintain_scripts where file_name like 'scripts/incremental/01_revisions/87_iteration_2/04_ddl_rename_dashboard_override_details_ceiling_columns.sql'
			""")
			}
			INP.append("""									
							
								END;

									</sqlCommand>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>

</project>
					""")

						System("call %m2_home%\\bin\\mvn clean install -s ${ats_settingsfile} -DenvHost=%envHost% -DappVersion=${ats_appVersion} -f ${resultdir}${sep}${file}.inp ")			
				}
			
			
				public mvn_upgrade(args)  {
					def ele=(args)
			//		println ele
					def INP = new File("${resultdir}${sep}${file}.inp")
					INP.write("--ba\n")
					INP.write("""\
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
<modelVersion>4.0.0</modelVersion>
	<groupId>com.ideas.tetris.analytics.deploy.db</groupId>
	<artifactId>pacman-db-tenant-analyticaldata</artifactId>
	<packaging>pom</packaging>
	<name>tetris::analytics::deploy::db::pacman-db-tenant-analyticaldata</name>
	<version>1.0</version>
			<build>
				<plugins>
				<plugin>
				<groupId>org.dbmaintain</groupId>
				<artifactId>dbmaintain-maven-plugin</artifactId>
				<version>2.4</version>
				<executions> 
					<execution>
						<id>db-upgrade-$ele</id>
						<phase>pre-integration-test</phase>
						<configuration>
							<scriptEncoding>unicode</scriptEncoding>
							<autoCreateDbMaintainScriptsTable>true</autoCreateDbMaintainScriptsTable>
							<configFile>${ats_homedir}\\dbmaintain.properties</configFile>
							<qualifiers>\${dbQualifiers}</qualifiers>
							<excludedQualifiers>tenant1,tenant2,sandbox,test,000005,000006</excludedQualifiers>			
							<databases>
								<database>
									<name>$ele</name>
									<driverClassName>\${db.driver}</driverClassName>
									<url>jdbc:sqlserver://\${mssqlHost}:\${mssqlPort};DatabaseName=$ele</url>
									<userName>G3Services</userName>
									<password>\${mssqlPassword}</password>
									<schemaNames>dbo</schemaNames>
									<dialect>mssql</dialect>
								</database>								
							</databases>
							<scriptArchiveDependencies>
								<scriptArchiveDependency>
									<groupId>com.ideas.g3.db</groupId>
									<artifactId>tenant</artifactId>
									<version>\${appVersion}</version>
								</scriptArchiveDependency>
							</scriptArchiveDependencies>
						</configuration>
						<goals>
							<goal>updateDatabase</goal>
						</goals>
					</execution>
				</executions>
				<dependencies>
					<dependency>
						<groupId>com.microsoft.sqlserver</groupId>
                        <artifactId>mssql-jdbc</artifactId>
                        <version>8.4.0.jre8</version>			
					</dependency>
				</dependencies>
			</plugin>		
				</plugins>
			</build>

</project>
							""")

						System("call %m2_home%\\bin\\mvn clean install -s ${ats_settingsfile}  -DenvHost=%envHost% -DappVersion=${ats_appVersion} -f ${resultdir}${sep}${file}.inp  ")

				}

				public sasupgrade(String sid,String client, String pcode) {
				
						System("call %m2_home%\\bin\\mvn clean install -s ${ats_settingsfile} -DenvHost=%envHost% -DappVersion=${ats_appVersion} -DscriptDir=${ats_tooldir} -f ${ats_sasdir}\\pom-deploy-data-analytics_prop.xml -DpropertyID=${sid} ")
						System("call %m2_home%\\bin\\mvn clean install -s ${ats_settingsfile} -DenvHost=%envHost% -DappVersion=${ats_appVersion} -DscriptDir=${ats_tooldir} -f ${ats_sasdir}\\pom-deploy-data-ratchet.xml -DpropertyID=${pcode} -Dclient=${client} ")
				}
				
				public remove_overrides(args) {
					def ele= [
						"PACE_Bar_Output", 
						"PACE_Decision_Dailybar_Output",
						"PACE_FPLOS_By_Rank",
						"PACE_LRV",
						"PACE_Ovrbk_Accom",
						"PACE_Ovrbk_Property",
						"PACE_Qualified_FPLOS",
						"PACE_Accom_Occupancy_FCST",
						"PACE_Mkt_Occupancy_FCST",
						"Decision_Bar_Output_OVR_Details",
						"Decision_Bar_Output_OVR", 
						"Decision_Bar_Output", 

						"Decision_COW_Value_OVR",
						"Decision_Dailybar_Output", 
						"Decision_FPLOS_By_Hierarchy", 
						"Decision_FPLOS_By_Rank", 
						"Decision_LRV",
						"Decision_Ovrbk_Accom_OVR_AUD",
						"Decision_Ovrbk_Accom_OVR", 
						"Decision_Ovrbk_Accom",

						"Decision_Ovrbk_Property_OVR",
						"Decision_Ovrbk_Property",

						"Decision_Qualified_FPLOS",

						"Wash_Ind_Group_Fcst_OVR",
						"Wash_Ind_Group_Fcst",

						"Wash_Property_FCST",

						"Wash_Forecast_Group_FCST_OVR",
						"Wash_Forecast_Group_FCST",
						"Wash_FCST",


						"Occupancy_Demand_FCST_OVR",
						"Occupancy_Demand_FCST",
						"Arrival_Demand_FCST_OVR", 
						"Arrival_Demand_FCST",
						"Occupancy_FCST",

						"Unqualified_Demand_FCST_Price",
						"Decision",

						"WebRate",
						"Pace_webrate"

					]
					def end=ele.size()
					def INP = new File("${resultdir}${sep}${file}.inp")
					INP.write("--geneating by automated tool\n")
						for (i in ele[0..end-1]){
						INP.append("${i}\n")		
						}
				System("for /f %%i in (${resultdir}${sep}${file}.inp) do %sqlcmd% -d ${args} -Q \"set QUOTED_IDENTIFIER ON;delete  from  %%i;\"")

				}

				
				public	getDecisionID (args) {
					def INP = new File("${resultdir}${sep}${file}.inp")
					INP.write("--geneating by automated tool\n")
					INP.write("""\
					INSERT INTO [Decision] ([Property_ID],[Business_DT],[Caught_up_DTTM],[Rate_Unqualified_DTTM],
					[Decision_Type_ID],[Start_DTTM],[End_DTTM],[Process_Status_ID])
					VALUES(${args},'2011-11-08','2011-11-08 01:52:00.000','2011-11-08 23:13:21.650','1','2011-11-08 17:53:36.633',
					'2011-11-08 17:53:36.633','13') 
					""")			
					System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}.inp")
				}
		

		
				
				
				public checkRunningJobs(String cid, String sid, int timeout, String server) {
					def BAT = new File("$resultdir\\scriptTest.bat")
					BAT.append """
					:checkRunningJobs
					@echo off
					${ats_utilsdir}\\sleep  ${timeout} 
					${ats_utilsdir}\\curl -s -X POST "http://${Server}:8080/api/applicationUpgrade/hasNoRunningJobs/v1?clientId=${cid}&propertyId=${sid}" -u <EMAIL>:password  > job_${sid}.txt
					for /F %%i in (job_${sid}.txt) do set a=%%i
					if %a% EQU true (echo Successful) ELSE (goto :checkRunningJobs)
					@echo on \n
					"""
				
				}
		

				public end(){
					def BAT = new File("$resultdir\\scriptTest.bat")
					BAT.append("REM Done generating the script")


					def INDX = new File("$resultdir\\index.html")
					INDX.append """
</body></html>
					"""

				}	

				// TODO: delete this method
				public update_ratchet(String pcode, String sysdate) {
				}
				public	dumpResults_MP(args){
						def pid="$args"
						def MP1 = new File("${resultdir}${sep}${file}_hotel.inp")
						MP1.write("--geneating by automated tool\n") 
						MP1.write("set ANSI_WARNINGS OFF \n")
						MP1.write("""\
						set ANSI_WARNINGS OFF;
						declare @sdate date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
						declare @edate date=DATEADD(day,365,@sdate)
						exec usp_dataextraction_report_hotel_thisyear_lastyear ${pid},3,13,@sdate,@edate,-1,-1,-1,-1,-1,0,null,null,0,1
						""")			
						System("%sqlcmd% -d ${args} -s\";\" -i ${resultdir}${sep}${file}_hotel.inp -o  ${resultdir}${sep}${file}_hotel.csv ")

						def MP2 = new File("${resultdir}${sep}${file}_fg.inp")
						MP2.write("--geneating by automated tool\n") 
						MP2.write("set ANSI_WARNINGS OFF \n")
						MP2.write("""\
						set ANSI_WARNINGS OFF;
						declare @sdate date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
						declare @edate date=DATEADD(day,365,@sdate)
						exec usp_dataextraction_report_fg_thisyear_lastyear ${pid},3,13,@sdate,@edate,0,null,null,0						""")			
						System("%sqlcmd% -d ${args} -s\";\" -i ${resultdir}${sep}${file}_fg.inp -o  ${resultdir}${sep}${file}_fg.csv ")
										
						def MP3 = new File("${resultdir}${sep}${file}_acat.inp")
						MP3.write("--geneating by automated tool\n")
						MP3.write("""\
						SET NOCOUNT ON;
						select AT.Property_ID,AT.Accom_Type_ID,AT.Accom_Class_ID,AT.Accom_Type_Code ,AC.Accom_Class_Code,AC.Master_Class,AC.Rank_Order,AT.Accom_Type_Capacity,AT.Status_ID from Accom_Class ac , Accom_Type AT
	                    where AT.Accom_Class_ID = AC.Accom_Class_ID and AC.Status_ID = 1
	 
						""")			
						System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_acat.inp -o  ${resultdir}${sep}${file}_acat.csv  -s\",\" -W -w 700")

					def MP4 = new File("${resultdir}${sep}${file}_CALIB_TEST.inp")
			        MP4.write("--geneating by automated tool\n")
				    MP4.write("""\
	               select 
					E.RESULT,E.Process_Status_ID,E.IP_Cfg_Process_Group_ID,E.Process_Group_Id,E.IP_Cfg_Fcst_Task_ID,E.Task_id,E.Task_Name,E.Calib_DTTM,E.Last_Calib_DTTM,E.Calib_Freq_Days,
					E.Next_Calib_due,E.cur_date,replace(Convert(varchar ,(Calib_DTTM),102),'.','-' ) as REVISION_RUNDATE,
					case when datediff(d,replace(Convert(varchar ,(Calib_DTTM),102),'.','-' ),E.cur_date)= 0  and Calib_Freq_Days > 1 then 'ALERT' else 'OK' end  as IMPACTCHECK  from
					(select C.IP_Cfg_Process_Group_ID,C.Process_Group_Id,c.IP_Cfg_Fcst_Task_ID,c.Task_id,c.Task_Name,c.Process_Status_ID,c.Calib_DTTM,c.Last_Calib_DTTM,c.Calib_Freq_Days,
					c.Next_Calib_due,c.cur_date,(case when dateadd(d,Calib_Freq_Days,c.Cur_date) <= Next_Calib_due then 1 else 2  end ) as RESULT
					from (select aa.IP_Cfg_Process_Group_ID,aa.Process_Group_Id,aa.IP_Cfg_Fcst_Task_ID,aa.Task_id,aa.Task_Name,aa.Process_Status_ID,aa.Calib_DTTM,aa.Last_Calib_DTTM,aa.Calib_Freq_Days,
					aa.Next_Calib_due,bb.cur_date from 				
					
		            (select ICPGS.IP_Cfg_Process_Group_ID,ICPG.Process_Group_Id,ICPGS.IP_Cfg_Fcst_Task_ID,ICFTNL.Task_id,ICFTNL.Task_Name,ICPGS.Process_Status_ID,ICPGS.Calib_DTTM,ICPGS.Last_Calib_DTTM,ICFTNL.Calib_Freq_Days,
		             replace(Convert(varchar ,dateadd(d,Calib_Freq_Days,Last_Calib_DTTM),102),'.','-' )as Next_Calib_due
					 from IP_cfg_Process_Group_Stat ICPGS, IP_Cfg_Fcst_Task_name_List ICFTNL, IP_Cfg_Process_Group ICPG
					 where ICPGS.IP_Cfg_Fcst_Task_ID = ICFTNL.IP_Cfg_Fcst_Task_ID  and ICPGS.IP_Cfg_Process_Group_ID = ICPG.IP_Cfg_Process_Group_ID  
					and ICPGS.Last_Calib_DTTM is not NULL  ) as aa ,  (select max(SnapShot_DT)as Cur_date from File_Metadata  where Record_Type_ID = 3 and Process_Status_ID = 13) as bb) as C ) as E				 
					group by E.IP_Cfg_Process_Group_ID,E.Process_Group_Id,E.IP_Cfg_Fcst_Task_ID,E.Task_id,E.Task_Name,E.Process_Status_ID,E.Calib_DTTM,E.Last_Calib_DTTM,E.Calib_Freq_Days,
					E.Next_Calib_due,E.cur_date,E.RESULT
													
	
					""")			
					System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_CALIB_TEST.inp -o  ${resultdir}${sep}${file}_CALIB_TEST.csv  -s\",\" -W -w 700")

                    def MP5 = new File("${resultdir}${sep}${file}_decisions.inp")
			        MP5.write("--geneating by automated tool\n")
				    MP5.write("""\
	                   set nocount on ;
  declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
                     select C.Decision_ID,C.Property_ID,c.Accom_Class_ID,c.Accom_Class_Code,C.Rank_Order,c.Arrival_DT,c.LOS,c.Rate_Unqualified_ID,c.Rate_Code_Name,c.LRV,c.OCC_PERC,
					 IsBuzy,case when C.Arrival_DT between D.SE_Inst_stdt and D.End_DTTM then 'YES' else 'NO' end as SEDAY,
					c.Derived_Unqualified_Value,c.Decision_Reason_Type_ID ,C.Ovbk_accom,GG.PhyAccomCap,GG.Maint,GG.Other,
					(C.Ovbk_accom/case when GG.PhyAccomCap-GG.Maint-GG.Other <=0 then PhyAccomCap else GG.PhyAccomCap-GG.Maint-GG.Other  end )*100 as OvbkAccomPerc 
					from 
					(select   C.Decision_ID,C.Property_ID,c.Accom_Class_ID,c.Accom_Class_Code,C.Rank_Order,c.Arrival_DT,c.LOS,c.Rate_Unqualified_ID,c.Rate_Code_Name,c.LRV,c.OCC_PERC,
					case when c.Occ_perc > =95 then 'YES' else 'NO' end as IsBuzy,c.Derived_Unqualified_Value,c.Decision_Reason_Type_ID 
					,E.Ovbk_accom 	  from 
					(select A.Decision_ID,A.Property_ID,A.Accom_Class_ID,A.Accom_Class_Code,A.Rank_Order,A.Arrival_DT,A.LOS,A.Rate_Unqualified_ID,A.Rate_Code_Name,A.LRV,B.OCC_PERC,A.Derived_Unqualified_Value,A.Decision_Reason_Type_ID  
					from
					(select DBO.Decision_ID,DBO.Property_ID
					,DBO.Accom_Class_ID,Ac.Accom_Class_Code,AC.Rank_Order,DBO.Arrival_DT,DBO.LOS,DBO.Rate_Unqualified_ID,
					RU.Rate_Code_Name,PBO.Derived_Unqualified_Value,DL.LRV,DBO.Decision_Reason_Type_ID
					from Decision_Bar_Output DBO,Rate_Unqualified RU,Accom_Class AC, Decision_LRV DL,PACE_Bar_Output PBO
					 where DBO.arrival_dt>=@dt and 
					  DBO.Property_ID = RU.Property_ID
					 and DBO.Rate_Unqualified_ID = RU.Rate_Unqualified_ID
					 and DBO.Accom_Class_ID = AC.Accom_Class_ID
					 and DBO.Property_ID = AC.Property_ID
					 and DBO.Property_ID = DL.Property_ID
					 and DBO.Accom_Class_ID= DL.Accom_Class_ID
					 and DBO.Arrival_DT = DL.Occupancy_DT
					 and DBO.Decision_ID = PBO.Decision_ID
					 and DBO.Arrival_DT= PBO.Arrival_DT
					 and DBO.Accom_Class_ID = PBO.Accom_Class_ID
					 and DBO.los = PBO.LOS
				   and  DBo.Accom_Class_ID in (select Accom_Class_ID from Accom_Class where Status_ID = 1))as A, 
					 (select A.Occupancy_DT,A.OCC_FCST,B.Cap,(OCC_FCST/Cap)*100 as OCC_PERC from 
					(select OCF.Occupancy_DT,SUM(occupancy_nbr) as OCC_FCST  from Occupancy_FCST OCF  where OCF.Occupancy_DT >=@dt  group by OCF.Occupancy_DT)as A ,
					
					(select  Occupancy_DT, case when (Total_Accom_Capacity-Rooms_Not_Avail_Maint-Rooms_Not_Avail_Other)<= 0  then Total_Accom_Capacity else 
					Total_Accom_Capacity-Rooms_Not_Avail_Maint-Rooms_Not_Avail_Other end as Cap
					
					 from Total_Activity) as B where
					A.Occupancy_DT= B.Occupancy_DT  ) as B  where A.Arrival_DT = B.occupancy_dt) as C, 					 
					(
					
					
					select Occupancy_DT,AT.Accom_Class_ID,SUM(Overbooking_Decision)as Ovbk_accom from Decision_Ovrbk_Accom DOA ,Accom_Type AT
	                 where Occupancy_DT >=@dt and  DOA.Accom_Type_ID = AT.Accom_Type_ID  and AT.Status_ID = 1 group by Occupancy_DT,AT.Accom_Class_ID ) as  E	
					 where C.Arrival_DT = E.Occupancy_DT
					 and C.Accom_Class_ID= E.Accom_Class_ID
					 ) AS C left outer join 
					 
					 (select PSE.Property_ID,PSE.Special_Event_Type_ID,PSE.Special_Event_Name,PSE.Start_DTTM,PSE.End_DTTM,PSEI.Start_DTTM as SE_Inst_stdt,
					PSEI.End_DTTM SE_Inst_enddt,PSEI.Pre_Event_Days,PSEI.Post_Event_Days,PSEI.Enable_Forecast,PSE.Impact_On_Forecast,PSE.Event_Frequency_ID 
					from Property_Special_Event PSE left outer join  Property_Special_Event_Instance PSEI 
					on PSE.Property_Special_Event_ID = PSEI.Property_Special_Event_ID
					where PSE.Status_ID = 1 and Impact_On_Forecast = 1 )as D on C.Property_ID = D.Property_ID
					and C.Arrival_DT between D.SE_Inst_stdt and D.SE_Inst_enddt
					 left outer join 
					 (select Occupancy_DT,AT.Accom_Class_ID,sum(Accom_Capacity)as PhyAccomCap,sum(Rooms_Not_Avail_Maint)as Maint,sum(Rooms_Not_Avail_Other)as Other  from Accom_Activity AA, Accom_Type AT  
	           	 where Occupancy_DT >=@dt
	           	 and  AA.accom_type_id = AT.accom_type_id and AT.Status_ID = 1 group by Occupancy_DT,AT.Accom_Class_ID) as GG
	           	 on c.arrival_Dt = GG.Occupancy_DT
	           	 and C.Accom_Class_ID = GG.Accom_Class_ID
	           	 
	         
					
					 order by c.Arrival_DT,c.LOS,c.Rank_Order									
	
					""")			
					System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_decisions.inp -o  ${resultdir}${sep}${file}_decisions.csv  -s\",\" -W -w 700")
					
					def MP6 = new File("${resultdir}${sep}${file}_CP_Decisions.inp")
								        MP6.write("--geneating by automated tool\n")
									    MP6.write("""\
									             
										set nocount on ;
                                        declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
                                        select C.Decision_ID,C.Property_ID,C.Accom_Type_ID,C.Accom_Type_Code,c.Accom_Class_ID,c.Accom_Class_Code,C.Rank_Order,c.Arrival_DT,c.LOS,C.optimal_bar,c.LRV,C.Pretty_BAR,C.Floor_Rate,C.Ceil_Rate,C.User_Specified_Rate,C.Final_BAR,C.Rooms_Only_BAR,c.OCC_PERC,
					 IsBuzy,case when C.Arrival_DT between D.SE_Inst_stdt and D.End_DTTM then 'YES' else 'NO' end as SEDAY,
					case when GG.ROOMSSOLD_AC = 0  then 0 else  GG.ROOMREV_AC/GG.ROOMSSOLD_AC end   as ADR_AC,
					
					c.Decision_Reason_Type_ID ,C.Ovbk_accom,(C.Ovbk_accom/case when GG.PhyAccomCap-GG.Maint-GG.Other <=0 then PhyAccomCap else GG.PhyAccomCap-GG.Maint-GG.Other  end )*100 as OvbkAccomPerc ,GG.PhyAccomCap,GG.Maint,GG.Other,GG.ROOMSSOLD_AC,GG.ROOMREV_AC
					
					from 
					(select   C.Decision_ID,C.Property_ID,C.Accom_Type_ID,C.Accom_Type_Code,c.Accom_Class_ID,c.Accom_Class_Code,C.Rank_Order,c.Arrival_DT,c.LOS,C.optimal_bar,c.LRV,C.Pretty_BAR,C.Floor_Rate,C.Ceil_Rate,C.User_Specified_Rate,C.Final_BAR,C.Rooms_Only_BAR,c.OCC_PERC,
					case when c.Occ_perc > =95 then 'YES' else 'NO' end as IsBuzy,c.Decision_Reason_Type_ID 
					,E.Ovbk_accom 	  from 
					
					(
				
					select A.Decision_ID,A.Property_ID,A.Accom_Type_ID,A.Accom_Type_Code,A.Accom_Class_ID,A.Accom_Class_Code,A.Rank_Order,A.Arrival_DT,A.LOS,A.optimal_bar,A.LRV,A.Pretty_BAR,A.Floor_Rate,A.Ceil_Rate,A.User_Specified_Rate,A.Final_BAR,A.Rooms_Only_BAR,B.OCC_PERC,
					A.Decision_Reason_Type_ID  
					from
					(select CPO.Decision_ID,CPO.Property_ID,Arrival_DT,CPO.Accom_Type_ID,AT.Accom_Type_Code,AC.Accom_Class_ID,AC.Accom_Class_Code,AC.Rank_Order ,LOS,optimal_bar,LRV,Pretty_BAR,override,floor_rate,ceil_rate,user_specified_rate,final_bar,rooms_only_bar,Decision_Reason_Type_ID
                                        from CP_Decision_Bar_Output CPO,Accom_Class AC,CP_Cfg_AC CPAC,Accom_Type AT,Decision_LRV DL
					where CPO.Accom_Type_ID =CPAC.Accom_Type_ID and CPAC.Accom_Type_ID=AT.Accom_Type_ID and AT.Accom_Class_ID= AC.Accom_Class_ID
					and CPO.Decision_id =DL.Decision_ID
					and AC.Accom_Class_ID=DL.Accom_Class_ID
					and CPO.Arrival_DT=DL.Occupancy_DT
					and Arrival_DT >=@dt
				   
				   
				   
				   )as A
				   
				   				   
				   , 
					 (select A.Occupancy_DT,A.OCC_FCST,B.Cap,(OCC_FCST/Cap)*100 as OCC_PERC from 
					(select OCF.Occupancy_DT,SUM(occupancy_nbr) as OCC_FCST  from Occupancy_FCST OCF  where OCF.Occupancy_DT >=@dt  group by OCF.Occupancy_DT)as A ,
					
					(select  Occupancy_DT, case when (Total_Accom_Capacity-Rooms_Not_Avail_Maint-Rooms_Not_Avail_Other)<= 0  then Total_Accom_Capacity else 
					Total_Accom_Capacity-Rooms_Not_Avail_Maint-Rooms_Not_Avail_Other end as Cap
					
					 from Total_Activity) as B where
					A.Occupancy_DT= B.Occupancy_DT  ) as B  where A.Arrival_DT = B.occupancy_dt) as C, 					 
					(
					
					
					select Occupancy_DT,AT.Accom_Class_ID,SUM(Overbooking_Decision)as Ovbk_accom from Decision_Ovrbk_Accom DOA ,Accom_Type AT
	                                where Occupancy_DT >=@dt and  DOA.Accom_Type_ID = AT.Accom_Type_ID  and AT.Status_ID = 1 group by Occupancy_DT,AT.Accom_Class_ID ) as  E	
					 where C.Arrival_DT = E.Occupancy_DT
					 and C.Accom_Class_ID= E.Accom_Class_ID
					 ) AS C left outer join 
					 
					 (select PSE.Property_ID,PSE.Special_Event_Type_ID,PSE.Special_Event_Name,PSE.Start_DTTM,PSE.End_DTTM,PSEI.Start_DTTM as SE_Inst_stdt,
					PSEI.End_DTTM SE_Inst_enddt,PSEI.Pre_Event_Days,PSEI.Post_Event_Days,PSEI.Enable_Forecast,PSE.Impact_On_Forecast,PSE.Event_Frequency_ID 
					from Property_Special_Event PSE left outer join  Property_Special_Event_Instance PSEI 
					on PSE.Property_Special_Event_ID = PSEI.Property_Special_Event_ID
					where PSE.Status_ID = 1 and Impact_On_Forecast = 1 )as D on C.Property_ID = D.Property_ID
					and C.Arrival_DT between D.SE_Inst_stdt and D.SE_Inst_enddt
					
					 left outer join 
					 
					 (select Occupancy_DT,AT.Accom_Class_ID,SUM(rooms_sold)as ROOMSSOLD_AC,sum(Accom_Capacity)as PhyAccomCap,sum(Rooms_Not_Avail_Maint)as Maint,
					 sum(Rooms_Not_Avail_Other)as Other,SUM(room_revenue)as ROOMREV_AC  from Accom_Activity AA, Accom_Type AT  
					 where Occupancy_DT >=@dt
					 and  AA.accom_type_id = AT.accom_type_id and AT.Status_ID = 1 group by Occupancy_DT,AT.Accom_Class_ID) as GG
					 on c.arrival_Dt = GG.Occupancy_DT
	           	                 and C.Accom_Class_ID = GG.Accom_Class_ID        	 
	         
					
					 order by c.Arrival_DT,c.LOS,c.Rank_Order
					 
					 
 
														
						
										""")			
					System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_CP_Decisions.inp -o  ${resultdir}${sep}${file}_CP_Decisions.csv  -s\",\" -W -w 700")
						

						}
				
				        public	acatmap(args){
						def INP = new File("${resultdir}${sep}${file}_acat.inp")
						INP.write("--geneating by automated tool\n")
						INP.write("""\
						SET NOCOUNT ON;
						select AT.Property_ID,AT.Accom_Type_ID,AT.Accom_Class_ID,AT.Accom_Type_Code ,AC.Accom_Class_Code,AC.Master_Class,AC.Rank_Order,AT.Accom_Type_Capacity,AT.Status_ID from Accom_Class ac , Accom_Type AT
	                    where AT.Accom_Class_ID = AC.Accom_Class_ID and AC.Status_ID = 1
	 
						""")			
						System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_acat.inp -o  ${resultdir}${sep}${file}_acat.csv  -s\",\" -W -w 700")
						
						
						def INP1 = new File("${resultdir}${sep}${file}_arrdmd.inp")
						INP1.write("--geneating by automated tool\n")
						INP1.write("""\
						SET NOCOUNT ON;
						declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
						select  ADF.Property_ID,ADF.Forecast_Group_ID,FG.Forecast_Group_Code,ADF.Accom_Class_ID,AC.Accom_Class_Code,ADF.Arrival_DT,ADF.LOS,ADF.Peak_Demand as Arrival_Peak_demand,
	                    ADF.Remaining_Demand,ADF.Deviation,ADF.User_Remaining_Demand from Arrival_Demand_FCST ADF,Forecast_Group FG ,Accom_class  AC  where  ADF.Arrival_DT >= @dt and 
	                    ADF.forecast_group_id = FG.Forecast_Group_ID and ADF.Property_ID = Fg.Property_ID and ADF.Accom_Class_ID = AC.Accom_Class_ID  and ADF.Property_ID = AC.Property_ID  order by ADF.Arrival_DT,ADF.Forecast_Group_ID,ADF.Accom_Class_ID,ADF.LOS
	 
						""")			
						System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_arrdmd.inp -o  ${resultdir}${sep}${file}_arrdmd.csv  -s\",\" -W -w 700")
						
						
						def INP2 = new File("${resultdir}${sep}${file}_barlrv.inp")
						INP2.write("--geneating by automated tool\n")
						INP2.write("""\
						SET NOCOUNT ON;
						declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
	
	                    select DBO.Decision_Bar_Output_ID,DBO.Decision_ID,DBO.Property_ID ,DBO.Accom_Class_ID,Ac.Accom_Class_Code,DBO.Arrival_DT,DBO.LOS,DBO.Rate_Unqualified_ID,RU.Rate_Code_Name,DL.LRV,DBO.Decision_Reason_Type_ID
	                    from Decision_Bar_Output DBO,Rate_Unqualified RU,Accom_Class AC, Decision_LRV DL where DBO.Arrival_DT >= @dt and DBO.Property_ID = RU.Property_ID and DBO.Rate_Unqualified_ID = RU.Rate_Unqualified_ID
	                    and DBO.Accom_Class_ID = AC.Accom_Class_ID and DBO.Property_ID = AC.Property_ID  and DBO.Property_ID = DL.Property_ID  and DBO.Accom_Class_ID= DL.Accom_Class_ID  and DBO.Arrival_DT = DL.Occupancy_DT
	                    order by Arrival_DT,Accom_Class_ID,LOS
	 
	 
						""")			
						System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_barlrv.inp -o  ${resultdir}${sep}${file}_barlrv.csv  -s\",\" -W -w 700")
						
						def INP3 = new File("${resultdir}${sep}${file}_cow.inp")
			            INP3.write("--geneating by automated tool\n")
				        INP3.write("""\
				       SET NOCOUNT ON;
				       select * from CostofWalk_Default 
				       """)			
				     System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_cow.inp -o  ${resultdir}${sep}${file}_cow.csv  -s\",\" -W -w 700")
					 
					  def INP4 = new File("${resultdir}${sep}${file}_FPLOS.inp")
			         INP4.write("--geneating by automated tool\n")
				     INP4.write("""\
				     SET NOCOUNT ON;			
	                 declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
	                 select * from Decision_FPLOS_By_Rank where Arrival_DT >= @dt
				     """)			
				    System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_FPLOS.inp -o  ${resultdir}${sep}${file}_FPLOS.csv  -s\",\" -W -w 700")
					
					def INP5 = new File("${resultdir}${sep}${file}_OVBK_ACCOM.inp")
			        INP5.write("--geneating by automated tool\n")
				    INP5.write("""\
				    SET NOCOUNT ON;			
	                 declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
	             
	                select DOA.Property_ID,DOA.Occupancy_DT,DOA.Accom_Type_ID,At.Accom_Type_Code,AC.Accom_Class_Code,DOA.Overbooking_Decision
	                from Decision_Ovrbk_Accom  DOA, Accom_Type AT, Accom_class AC  where DOA.Occupancy_DT >= @dt  and DOA.Accom_Type_ID = AT.Accom_Type_ID
	                and DOA.Property_ID = AT.Property_ID  and AT.Accom_Class_ID = AC.Accom_Class_ID   and DOA.Property_ID = AC.Property_ID  order by DOA.Occupancy_DT,DOA.Accom_Type_ID
				     """)			
				    System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_OVBK_ACCOM.inp -o  ${resultdir}${sep}${file}_OVBK_ACCOM.csv  -s\",\" -W -w 700")
					
				    def INP6 = new File("${resultdir}${sep}${file}_OVBK_PROP.inp")
			        INP6.write("--geneating by automated tool\n")
				    INP6.write("""\
				    SET NOCOUNT ON;			
	                declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
	             
					select DOP.Property_ID,DOP.Occupancy_DT,DOP.Overbooking_Decision,DOP.Expected_Walks
					from Decision_Ovrbk_Property DOP where DOP.Occupancy_DT > @dt 
					order by DOP.Property_ID,DOP.Occupancy_DT
					""")			
					System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_OVBK_PROP.inp -o  ${resultdir}${sep}${file}_OVBK_PROP.csv  -s\",\" -W -w 700")
					
					
					
					def INP7 = new File("${resultdir}${sep}${file}_FG_PG.inp")
			        INP7.write("--geneating by automated tool\n")
				    INP7.write("""\
				    SET NOCOUNT ON;			
	                declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
	             
				select  ICPGS.IP_Cfg_Process_Group_ID,ICPG.Process_Group_Id,ICPGFT.IP_Cfg_Process_Group_Type_ID,FG.Forecast_Group_ID
	            ,FG.Forecast_Group_Code,AC.Accom_Class_ID,ICPGS.Forecast_Type_ID,ICPGFT.IP_Cfg_Process_Group_FCST_Type_ID
				from IP_Cfg_Process_Group_Scope ICPGS, Forecast_Group FG ,Accom_Class AC,IP_Cfg_Process_Group_Fcst_Type ICPGFT,IP_Cfg_Process_Group ICPG  
				where  ICPGS.Forecast_Group_ID = FG.Forecast_Group_ID  and ICPGS.Accom_Class_ID = AC.Accom_Class_ID  and ICPGFT.Forecast_Type_ID = ICPGS.Forecast_Type_ID  and ICPGFT.Forecast_Type_ID = FG.Forecast_Type_ID
				and ICPG.IP_Cfg_Process_Group_ID = ICPGS.IP_Cfg_Process_Group_ID 	and ICPG.Status_ID = 1  and FG.Status_ID = 1  and AC.Status_ID = 1  and AC.Accom_Class_ID = (select Accom_Class_ID 
				from Accom_Class where Master_Class = 1 and Status_ID = 1)  order  by ICPGFT.IP_Cfg_Process_Group_Type_ID,Fg.Forecast_Group_ID,FG.Forecast_Type_ID,ICPGFT.IP_Cfg_Process_Group_FCST_Type_ID,
				ICPGS.IP_Cfg_Process_Group_ID,ICPG.Process_Group_Id ,AC.Accom_Class_ID
					""")			
					System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_FG_PG.inp -o  ${resultdir}${sep}${file}FG_PG.csv  -s\",\" -W -w 700")
					
					
					
					 def INP8 = new File("${resultdir}${sep}${file}_MS_FG_ADR_OCC.inp")
			        INP8.write("--geneating by automated tool\n")
				    INP8.write("""\
				    SET NOCOUNT ON;			
	                declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
	
					select X.property_id,X.Occupancy_DT,X.Mkt_Seg_ID,X.Mkt_Seg_Code,X.Forecast_Group_Code,X.Solds,x.Revenue,Y.FG_ADR,X.OCC_FCST as OCC_FCST_MS from 
	
					(select A.Property_ID as property_id,A.Occupancy_DT,A.Mkt_Seg_ID,MS.Mkt_Seg_Code,FG.Forecast_Group_Code,A.Solds,B.Revenue,C.OCC_FCST
					 from 
					(select MAA.Property_ID,MAA.Occupancy_DT,MAA.Mkt_Seg_ID,sum(MAA.Rooms_Sold)as Solds from Mkt_Accom_Activity MAA 
					where  MAA.Occupancy_DT >= @dt
					group by MAA.Property_ID,MAA.Mkt_Seg_ID,MAA.Occupancy_DT ) as A inner join 
					(select MAA.Property_ID,MAA.Occupancy_DT,MAA.Mkt_Seg_ID,sum(Room_Revenue)as Revenue from Mkt_Accom_Activity MAA 
					where  MAA.Occupancy_DT >= @dt
					group by MAA.Property_ID,MAA.Mkt_Seg_ID,MAA.Occupancy_DT ) as B 
					on A.Mkt_Seg_ID= B.Mkt_Seg_ID
					and A.Occupancy_DT= B.Occupancy_DT
					and A.Property_ID = B.Property_ID
					 inner join
					(select OFC.Property_ID,OFC.Occupancy_DT,OFC.MKT_SEG_ID,sum(OFC.Occupancy_NBR) as OCC_FCST from Occupancy_FCST OFC
					where Occupancy_DT >= @dt group by OFC.Property_ID,OFC.MKT_SEG_ID,OFC.Occupancy_DT)as C 
					on A.Mkt_Seg_ID= B.Mkt_Seg_ID
					and A.Occupancy_DT= B.Occupancy_DT
					and A.Property_ID = B.Property_ID 
					and A.mkt_seg_id = C.MKT_SEG_ID
					and A.property_id = C.Property_ID
					and A.occupancy_dt = C.Occupancy_DT
					,mkt_seg MS,Mkt_Seg_Forecast_Group MSFG,Forecast_Group FG 
					where MS.Mkt_Seg_ID = A.Mkt_Seg_ID
					and MS.Property_ID = A.property_id
					and MS.Mkt_Seg_ID = MSFG.Mkt_Seg_ID
					and FG.Forecast_Group_ID = MSFG.Forecast_Group_ID
					and FG.Property_ID = A.Property_ID
					and  MSFG.Status_ID = 1
					and FG.Status_ID = 1 ) as X  inner join 
	
					(select D.property_id,D.Occupancy_DT,D.Forecast_Group_Code,CAST((sum(D.Revenue)/NULLIF(sum(D.Solds),0)) as INTEGER) as FG_ADR  from 
					(select A.Property_ID as property_id,A.Occupancy_DT,A.Mkt_Seg_ID,MS.Mkt_Seg_Code,FG.Forecast_Group_Code,A.Solds,B.Revenue
					 from 
					(select MAA.Property_ID,MAA.Occupancy_DT,MAA.Mkt_Seg_ID,sum(MAA.Rooms_Sold)as Solds from Mkt_Accom_Activity MAA 
					where  MAA.Occupancy_DT >= @dt
					group by MAA.Property_ID,MAA.Mkt_Seg_ID,MAA.Occupancy_DT ) as A inner join 
					(select MAA.Property_ID,MAA.Occupancy_DT,MAA.Mkt_Seg_ID,sum(Room_Revenue)as Revenue from Mkt_Accom_Activity MAA 
					where  MAA.Occupancy_DT >= @dt
					group by MAA.Property_ID,MAA.Mkt_Seg_ID,MAA.Occupancy_DT ) as B 
					on A.Mkt_Seg_ID= B.Mkt_Seg_ID
					and A.Occupancy_DT= B.Occupancy_DT
					and A.Property_ID = B.Property_ID
					 inner join
					(select OFC.Property_ID,OFC.Occupancy_DT,OFC.MKT_SEG_ID,sum(OFC.Occupancy_NBR) as OCC_FCST from Occupancy_FCST OFC
					where  Occupancy_DT >= @dt group by OFC.Property_ID,OFC.MKT_SEG_ID,OFC.Occupancy_DT)as C 
					on A.Mkt_Seg_ID= B.Mkt_Seg_ID
					and A.Occupancy_DT= B.Occupancy_DT
					and A.Property_ID = B.Property_ID 
					and A.mkt_seg_id = C.MKT_SEG_ID
					and A.property_id = C.Property_ID
					and A.occupancy_dt = C.Occupancy_DT
					,mkt_seg MS,Mkt_Seg_Forecast_Group MSFG,Forecast_Group FG 
					where MS.Mkt_Seg_ID = A.Mkt_Seg_ID
					and MS.Property_ID = A.property_id
					and MS.Mkt_Seg_ID = MSFG.Mkt_Seg_ID
					and FG.Forecast_Group_ID = MSFG.Forecast_Group_ID
					and FG.Property_ID = A.Property_ID
					and  MSFG.Status_ID = 1
					and FG.Status_ID = 1
					) as D group by D.property_id,D.Occupancy_DT,D.Forecast_Group_Code) as Y
	
					on X.property_id = Y.property_id
					and X.Occupancy_DT = Y.Occupancy_DT
					and X.Forecast_Group_Code= Y.Forecast_Group_Code
					order by X.property_id,X.Occupancy_DT,X.Mkt_Seg_ID,Y.Forecast_Group_Code
					""")			
					System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_MS_FG_ADR_OCC.inp -o  ${resultdir}${sep}${file}_MS_FG_ADR_OCC.csv  -s\",\" -W -w 700")
					
					def INP9 = new File("${resultdir}${sep}${file}_MS_FG.inp")
			        INP9.write("--geneating by automated tool\n")
				    INP9.write("""\
				    SET NOCOUNT ON;			
	                declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
	             
					select  ICPGS.IP_Cfg_Process_Group_ID,ICPG.Process_Group_Id,ICPGFT.IP_Cfg_Process_Group_Type_ID,FG.Forecast_Group_ID
					 ,FG.Forecast_Group_Code,AC.Accom_Class_ID,ICPGS.Forecast_Type_ID,ICPGFT.IP_Cfg_Process_Group_FCST_Type_ID
					  from IP_Cfg_Process_Group_Scope ICPGS, Forecast_Group FG ,
					 Accom_Class AC,IP_Cfg_Process_Group_Fcst_Type ICPGFT,IP_Cfg_Process_Group ICPG
					 where  ICPGS.Forecast_Group_ID = FG.Forecast_Group_ID   and ICPGS.Accom_Class_ID = AC.Accom_Class_ID  and ICPGFT.Forecast_Type_ID = ICPGS.Forecast_Type_ID
					 and ICPGFT.Forecast_Type_ID = FG.Forecast_Type_ID  and ICPG.IP_Cfg_Process_Group_ID = ICPGS.IP_Cfg_Process_Group_ID  and ICPG.Status_ID = 1
					 and FG.Status_ID = 1  and AC.Status_ID = 1 and AC.Accom_Class_ID = (select accom_class_id from accom_class where master_class = 1 and status_id = 1) order  by ICPGFT.IP_Cfg_Process_Group_Type_ID,Fg.Forecast_Group_ID,FG.Forecast_Type_ID,ICPGFT.IP_Cfg_Process_Group_FCST_Type_ID,
					 ICPGS.IP_Cfg_Process_Group_ID,ICPG.Process_Group_Id ,AC.Accom_Class_ID 
					 
					 select MS.Mkt_Seg_ID,Mkt_Seg_Code,MSFG.Forecast_Group_ID,FG.Forecast_Group_Code,MSFG.Status_ID as MSFG_status_id from Mkt_Seg MS left outer join 
					 Mkt_Seg_Forecast_Group MSFG on
					MSFG.Mkt_Seg_ID = MS.Mkt_Seg_ID
					and   MSFG.Status_ID = 1 left outer join Forecast_Group FG
					on   MSFG.Forecast_Group_ID = FG.Forecast_Group_ID
					order by Fg.Forecast_Group_ID
					""")			
					System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_MS_FG.inp -o  ${resultdir}${sep}${file}_MS_FG.csv  -s\",\" -W -w 700")
					
					
					def INP10 = new File("${resultdir}${sep}${file}_OCC_PERC_FG_AC_DMD_LRV.inp")
			        INP10.write("--geneating by automated tool\n")
				    INP10.write("""\
				    SET NOCOUNT ON;			
	                declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
				 
					select  A.Occupancy_DT, A.Property_ID,A.Accom_Class_ID, A.Accom_Capacity, A.Rooms_Not_Avail_Other,A.Rooms_Available, B.Forecast_Group_ID,B.Occupancy_NBR,
					((B.Occupancy_NBR/A.Rooms_Available)*100)as OCC_PERC_FG_AC, B.Rooms_Sold,A.LRV as LRV_AC,B.Peak_Demand, B.Remaining_Demand, B.User_Remaining_Demand  from 
					( SELECT     lrv.Decision_LRV_ID AS AC_Capacity_ID, aa.Occupancy_DT, aa.Property_ID, at.Accom_Class_ID, SUM(aa.Accom_Capacity) AS Accom_Capacity, 
	    			  SUM(aa.Rooms_Not_Avail_Maint) AS Rooms_Not_Avail_Maint, SUM(aa.Rooms_Not_Avail_Other) AS Rooms_Not_Avail_Other, lrv.LRV, SUM(aa.Accom_Capacity - (aa.Rooms_Not_Avail_Maint + aa.Rooms_Not_Avail_Other)) 
					  AS Rooms_Available FROM         dbo.Accom_Activity AS aa INNER JOIN dbo.Accom_Type AS at ON aa.Accom_Type_ID = at.Accom_Type_ID INNER JOIN   dbo.Decision_LRV AS lrv ON aa.Property_ID = lrv.Property_ID 
					  AND aa.Occupancy_DT = lrv.Occupancy_DT AND at.Accom_Class_ID = lrv.Accom_Class_ID GROUP BY aa.Occupancy_DT, aa.Property_ID, at.Accom_Class_ID, lrv.Decision_LRV_ID, lrv.LRV ) as A ,
					(SELECT     df.Occupancy_Demand_FCST_ID AS Occupancy_FCST_ID, occ.Occupancy_DT, occ.Property_ID, msfg.Forecast_Group_ID, at.Accom_Class_ID, 
					SUM(occ.Occupancy_NBR) AS Occupancy_NBR, SUM(maa.Rooms_Sold) AS Rooms_Sold, df.Peak_Demand, df.Remaining_Demand,df.User_Remaining_Demand
					FROM         dbo.Occupancy_FCST AS occ INNER JOIN  dbo.Accom_Type AS at ON occ.Accom_Type_ID = at.Accom_Type_ID INNER JOIN
					  dbo.Mkt_Seg_Forecast_Group AS msfg ON occ.MKT_SEG_ID = msfg.Mkt_Seg_ID LEFT OUTER JOIN
					  dbo.Occupancy_Demand_FCST AS df ON occ.Property_ID = df.Property_ID AND occ.Occupancy_DT = df.Occupancy_DT AND 
					  msfg.Forecast_Group_ID = df.Forecast_Group_ID AND at.Accom_Class_ID = df.Accom_Class_ID INNER JOIN
					  dbo.Mkt_Accom_Activity AS maa ON occ.Property_ID = maa.Property_ID AND occ.Occupancy_DT = maa.Occupancy_DT AND 
					  maa.Accom_Type_ID = at.Accom_Type_ID AND maa.Mkt_Seg_ID = msfg.Mkt_Seg_ID WHERE     (msfg.Status_ID = 1)
					  GROUP BY occ.Occupancy_DT, occ.Property_ID, msfg.Forecast_Group_ID, df.Occupancy_Demand_FCST_ID, at.Accom_Class_ID, df.Peak_Demand, df.Remaining_Demand, 
					  df.User_Remaining_Demand) as B where A.Occupancy_DT =  B.Occupancy_DT and A.Accom_Class_ID = B.Accom_Class_ID and    A.Occupancy_DT >= @dt order by A.Occupancy_DT,B.Forecast_Group_ID,A.Accom_Class_ID
	
					""")			
					System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_OCC_PERC_FG_AC_DMD_LRV.inp -o  ${resultdir}${sep}${file}_OCC_PERC_FG_AC_DMD_LRV.csv  -s\",\" -W -w 700")
					
					
					def INP11 = new File("${resultdir}${sep}${file}_OCC-FCST_FG_AC.inp")
			        INP11.write("--geneating by automated tool\n")
				    INP11.write("""\
				             
					set nocount on ;
					declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
					set nocount on ;  
					select OFP.Occupancy_DT,FG.Forecast_Group_Code,AC.Accom_Class_Code,sum(OFP.Occupancy_NBR) as Occ_fcst_FG_AC 
					from Occupancy_FCST OFP  ,Mkt_Seg MS,Accom_Type AT, Mkt_Seg_Forecast_Group MSFG, Forecast_Group FG, Accom_Class AC 
					where  OFP.MKT_SEG_ID = MS.Mkt_Seg_ID and OFP.Property_ID = MS.Property_ID and OFP.Accom_Type_ID = AT.Accom_Type_ID and OFP.Property_ID = AT.Property_ID and OFP.MKT_SEG_ID = MSFg.Mkt_Seg_ID 
					and MSFG.Status_ID = 1 and FG.Status_ID = 1 and MSFG.Forecast_Group_ID = FG.Forecast_Group_ID and OFP.Property_ID = FG.Property_ID and AT.Accom_Class_ID = AC.Accom_Class_ID 
					group by OFP.Occupancy_DT,FG.Forecast_Group_Code, AC.Accom_Class_Code
					order by OFP.Occupancy_DT,Fg.Forecast_Group_Code,AC.Accom_Class_Code 
									
	
					""")			
					System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_OCC-FCST_FG_AC.inp -o  ${resultdir}${sep}${file}_OCC-FCST_FG_AC.csv  -s\",\" -W -w 700")
					
					
					def INP12 = new File("${resultdir}${sep}${file}__FCST_FG_AC_Prop.inp")
			        INP12.write("--geneating by automated tool\n")
				    INP12.write("""\
				             
					set nocount on ;
					declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
				    select OFP.Occupancy_DT,FG.Forecast_Group_Code,AC.Accom_Class_Code,sum(OFP.Occupancy_NBR) as Occ_fcst_FG_AC 
					from Occupancy_FCST OFP  ,Mkt_Seg MS,Accom_Type AT, Mkt_Seg_Forecast_Group MSFG, Forecast_Group FG, Accom_Class AC 
					where  OFP.MKT_SEG_ID = MS.Mkt_Seg_ID and OFP.Property_ID = MS.Property_ID and OFP.Accom_Type_ID = AT.Accom_Type_ID and OFP.Property_ID = AT.Property_ID and OFP.MKT_SEG_ID = MSFg.Mkt_Seg_ID 
					and MSFG.Status_ID = 1 and FG.Status_ID = 1 and MSFG.Forecast_Group_ID = FG.Forecast_Group_ID and OFP.Property_ID = FG.Property_ID and AT.Accom_Class_ID = AC.Accom_Class_ID 
					group by OFP.Occupancy_DT,FG.Forecast_Group_Code, AC.Accom_Class_Code order by OFP.Occupancy_DT,Fg.Forecast_Group_Code,AC.Accom_Class_Code 
									
	
					""")			
					System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_FCST_FG_AC_Prop.inp -o  ${resultdir}${sep}${file}_FCST_FG_AC_Prop.csv  -s\",\" -W -w 700")
					
					def INP13 = new File("${resultdir}${sep}${file}_Occupancy_Dmd.inp")
			        INP13.write("--geneating by automated tool\n")
				    INP13.write("""\
	                 set nocount on ;
					declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
					
				    select  ODF.Property_ID,ODF.Forecast_Group_ID,FG.Forecast_Group_Code,ODF.Accom_Class_ID,AC.Accom_Class_Code,ODF.Occupancy_DT,ODF.Peak_Demand,ODF.Remaining_Demand,ODF.Deviation
				    ,ODF.User_Remaining_Demand   from Occupancy_Demand_FCST ODF,Forecast_Group FG,Accom_class  AC  where   ODF.forecast_group_id = FG.Forecast_Group_ID
				    and ODF.Property_ID = Fg.Property_ID  and ODF.Accom_Class_ID = AC.Accom_Class_ID  and ODF.Property_ID = AC.Property_ID  order by ODF.Occupancy_DT,ODF.Forecast_Group_ID,ODF.Accom_Class_ID
									
	
					""")			
					System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_Occupancy_Dmd.inp -o  ${resultdir}${sep}${file}_Occupancy_Dmd.csv  -s\",\" -W -w 700")
					
					def INP14 = new File("${resultdir}${sep}${file}_Occupancy_Dmd_Property.inp")
			        INP14.write("--geneating by automated tool\n")
				    INP14.write("""\
	                set nocount on ;
					declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
					
					select  ODF.Occupancy_DT,FG.Forecast_Group_Code,sum(ODF.Peak_Demand)as Peak_Demand_Prop 
			        from Occupancy_Demand_FCST ODF,Forecast_Group FG where  
			        ODF.forecast_group_id = FG.Forecast_Group_ID  and ODF.Property_ID = Fg.Property_ID  group by  ODF.Occupancy_DT,FG.Forecast_Group_Code  order by ODF.Occupancy_DT,FG.Forecast_Group_Code
									
	
					""")			
					System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_Occupancy_Dmd_Property.inp -o  ${resultdir}${sep}${file}_Occupancy_Dmd_Property.csv  -s\",\" -W -w 700")
/// commenting as this is not required
/*					
					def INP15 = new File("${resultdir}${sep}${file}_Occupancy_FCST.inp")
			        INP15.write("--geneating by automated tool\n")
				    INP15.write("""\
	                set nocount on ;
					declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
					
					select  OFP.Property_ID,OFP.Occupancy_DT,OFP.MKT_SEG_ID,MS.Mkt_Seg_Code,FG.Forecast_Group_Code,OFP.Accom_Type_ID,AT.Accom_Type_Code,OFP.Occupancy_NBR
					,OFP.Revenue from Occupancy_FCST OFP  ,Mkt_Seg MS,Accom_Type AT, Mkt_Seg_Forecast_Group MSFG, Forecast_Group FG
					where  OFP.MKT_SEG_ID = MS.Mkt_Seg_ID and OFP.Property_ID = MS.Property_ID  and OFP.Accom_Type_ID = AT.Accom_Type_ID and OFP.Property_ID = AT.Property_ID and OFP.MKT_SEG_ID = MSFg.Mkt_Seg_ID
					and MSFG.Status_ID = 1 and FG.Status_ID = 1 and MSFG.Forecast_Group_ID = FG.Forecast_Group_ID and OFP.Property_ID = FG.Property_ID order by OFP.Occupancy_DT,OFP.MKT_SEG_ID,OFP.Accom_Type_ID
									
	
					""")			
					System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_Occupancy_FCST.inp -o  ${resultdir}${sep}${file}_Occupancy_FCST.csv  -s\",\" -W -w 700")
*/					
					def INP16 = new File("${resultdir}${sep}${file}_Rate_unq.inp")
			        INP16.write("--geneating by automated tool\n")
				    INP16.write("""\
	                set nocount on ;
					declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
					
					select AC.Property_ID,RUAC.Rate_Unqualified_Accom_Class_ID,RUAC.Rate_Unqualified_ID,RU.Rate_Code_Name,RUD.Rate_Unqualified_Details_ID,RUAC.Accom_Class_ID,
					AC.Accom_Class_Name,AT.Accom_Type_ID,AT.Accom_Type_Name,RUD.Start_Date_DT,
					RUD.End_Date_DT,RUD.Sunday,RUD.Monday,RUD.Tuesday,RUD.Wednesday,RUD.Thursday,RUD.Friday,RUD.Saturday 
					from Rate_Unqualified_Accom_Class RUAC,Rate_Unqualified RU, Rate_Unqualified_Details RUD,Accom_Class AC, Accom_Type AT 
					where RUAC.Accom_Class_ID= AC.Accom_Class_ID and AC.Accom_Class_ID = AT.Accom_Class_ID and RU.Rate_Unqualified_ID = RUAC.Rate_Unqualified_ID and RUD.Accom_Type_ID = AT.Accom_Type_ID
					and RU.Rate_Unqualified_ID = RUD.Rate_Unqualified_ID order by RU.Rate_Unqualified_ID,Ac.Accom_Class_ID,AT.Accom_Type_ID,rud.Start_Date_DT
									
	
					""")			
					System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_Rate_unq.inp -o  ${resultdir}${sep}${file}_Rate_unq.csv  -s\",\" -W -w 700")
					
					
					
					def INP17 = new File("${resultdir}${sep}${file}_SE.inp")
			        INP17.write("--geneating by automated tool\n")
				    INP17.write("""\
	                set nocount on ;
					declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
					
					select PSE.Property_ID,PSE.Special_Event_Type_ID,PSE.Special_Event_Name,PSE.Start_DTTM,PSE.End_DTTM,PSEI.Start_DTTM as SE_Inst_stdt,
					PSEI.End_DTTM SE_Inst_enddt,PSEI.Pre_Event_Days,PSEI.Post_Event_Days,PSEI.Enable_Forecast,PSE.Impact_On_Forecast,PSE.Event_Frequency_ID 
					from Property_Special_Event PSE left outer join  Property_Special_Event_Instance PSEI 
					on PSE.Property_Special_Event_ID = PSEI.Property_Special_Event_ID
					where PSE.Status_ID = 1 
					order by PSE.Property_ID,PSE.Property_Special_Event_ID
									
	
					""")			
					System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_SE.inp -o  ${resultdir}${sep}${file}_SE.csv  -s\",\" -W -w 700")
					
					
					def INP18 = new File("${resultdir}${sep}${file}_Wash_FCST.inp")
			        INP18.write("--geneating by automated tool\n")
				    INP18.write("""\
	                set nocount on ;
					declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
					
				    select  WF.Property_ID,WF.Occupancy_DT,WF.Forecast_Group_ID,Fg.Forecast_Group_Code,WF.Accom_Class_ID,AC.Accom_Class_Code,WF.System_Wash,WF.User_Wash 
					from Wash_FCST WF,Forecast_Group FG ,Accom_class  AC 
					where  WF.forecast_group_id = FG.Forecast_Group_ID and WF.Property_ID = Fg.Property_ID  and WF.Accom_Class_ID = AC.Accom_Class_ID and WF.Property_ID = AC.Property_ID  
					order by WF.Occupancy_DT,WF.Forecast_Group_ID,WF.Accom_Class_ID
													
	
					""")			
					System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_Wash_FCST.inp -o  ${resultdir}${sep}${file}_Wash_FCST.csv  -s\",\" -W -w 700")
					
					
					def INP19 = new File("${resultdir}${sep}${file}_CALIB_TEST.inp")
			        INP19.write("--geneating by automated tool\n")
				    INP19.write("""\
	              select 
					E.RESULT,E.Process_Status_ID,E.IP_Cfg_Process_Group_ID,E.Process_Group_Id,E.IP_Cfg_Fcst_Task_ID,E.Task_id,E.Task_Name,E.Calib_DTTM,E.Last_Calib_DTTM,E.Calib_Freq_Days,
					E.Next_Calib_due,E.cur_date,replace(Convert(varchar ,(Calib_DTTM),102),'.','-' ) as REVISION_RUNDATE,
					case when datediff(d,replace(Convert(varchar ,(Calib_DTTM),102),'.','-' ),E.cur_date)= 0  and Calib_Freq_Days > 1 then 'ALERT' else 'OK' end  as IMPACTCHECK  from
					(select C.IP_Cfg_Process_Group_ID,C.Process_Group_Id,c.IP_Cfg_Fcst_Task_ID,c.Task_id,c.Task_Name,c.Process_Status_ID,c.Calib_DTTM,c.Last_Calib_DTTM,c.Calib_Freq_Days,
					c.Next_Calib_due,c.cur_date,(case when dateadd(d,Calib_Freq_Days,c.Cur_date) <= Next_Calib_due then 1 else 2  end ) as RESULT
					from (select aa.IP_Cfg_Process_Group_ID,aa.Process_Group_Id,aa.IP_Cfg_Fcst_Task_ID,aa.Task_id,aa.Task_Name,aa.Process_Status_ID,aa.Calib_DTTM,aa.Last_Calib_DTTM,aa.Calib_Freq_Days,
					aa.Next_Calib_due,bb.cur_date from 				
					
		            (select ICPGS.IP_Cfg_Process_Group_ID,ICPG.Process_Group_Id,ICPGS.IP_Cfg_Fcst_Task_ID,ICFTNL.Task_id,ICFTNL.Task_Name,ICPGS.Process_Status_ID,ICPGS.Calib_DTTM,ICPGS.Last_Calib_DTTM,ICFTNL.Calib_Freq_Days,
		             replace(Convert(varchar ,dateadd(d,Calib_Freq_Days,Last_Calib_DTTM),102),'.','-' )as Next_Calib_due
					 from IP_cfg_Process_Group_Stat ICPGS, IP_Cfg_Fcst_Task_name_List ICFTNL, IP_Cfg_Process_Group ICPG
					 where ICPGS.IP_Cfg_Fcst_Task_ID = ICFTNL.IP_Cfg_Fcst_Task_ID  and ICPGS.IP_Cfg_Process_Group_ID = ICPG.IP_Cfg_Process_Group_ID  
					and ICPGS.Last_Calib_DTTM is not NULL  ) as aa ,  (select max(SnapShot_DT)as Cur_date from File_Metadata  where Record_Type_ID = 3 and Process_Status_ID = 13) as bb) as C ) as E				 
					group by E.IP_Cfg_Process_Group_ID,E.Process_Group_Id,E.IP_Cfg_Fcst_Task_ID,E.Task_id,E.Task_Name,E.Process_Status_ID,E.Calib_DTTM,E.Last_Calib_DTTM,E.Calib_Freq_Days,
					E.Next_Calib_due,E.cur_date,E.RESULT
													
	
					""")			
					System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_CALIB_TEST.inp -o  ${resultdir}${sep}${file}_CALIB_TEST.csv  -s\",\" -W -w 700")
					
					def INP20 = new File("${resultdir}${sep}${file}_Wash_Ind_Group_fcst.inp")
			        INP20.write("--geneating by automated tool\n")
				    INP20.write("""\
	               set nocount on ;
					declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
 
					select WIGF.Property_ID,Decision_ID,WIGF.Occupancy_DT,WIGF.group_id,GM.Group_Code,GM.Cut_Off_date,WIGF.Accom_Type_ID,WIGF.mkt_Seg_id,FG.Forecast_Group_Code,WIGF.System_Wash,WIGF.User_Wash,WIGF.Noshow_Value,
					WIGF.disagg_val,WIGF.CreateDate_DTTM
					from Wash_Ind_Group_Fcst WIGF,Group_Master GM,Mkt_Seg_Forecast_Group MSFG,Forecast_Group FG
					where msfg.Status_ID = 1 and FG.Status_ID = 1 and msfg.Forecast_Group_ID = fg.Forecast_Group_ID and msfg.Mkt_Seg_ID = WIGF.Mkt_Seg_ID and 
					WIGF.Group_ID = GM.Group_ID and occupancy_dt > @dt order by Occupancy_DT
   
													
	
					""")			
					System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_Wash_Ind_Group_fcst.inp -o  ${resultdir}${sep}${file}_Wash_Ind_Group_fcst.csv  -s\",\" -W -w 700")



                               def INP21 = new File("${resultdir}${sep}${file}_decisions.inp")
			        INP21.write("--geneating by automated tool\n")
				    INP21.write("""\
	               set nocount on ;
			       declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
                     select C.Decision_ID,C.Property_ID,c.Accom_Class_ID,c.Accom_Class_Code,C.Rank_Order,c.Arrival_DT,c.LOS,c.Rate_Unqualified_ID,c.Rate_Code_Name,c.LRV,c.OCC_PERC,
					 IsBuzy,case when C.Arrival_DT between D.SE_Inst_stdt and D.End_DTTM then 'YES' else 'NO' end as SEDAY,
					c.Derived_Unqualified_Value,c.Decision_Reason_Type_ID ,C.Ovbk_accom,GG.PhyAccomCap,GG.Maint,GG.Other,
					(C.Ovbk_accom/case when GG.PhyAccomCap-GG.Maint-GG.Other <=0 then PhyAccomCap else GG.PhyAccomCap-GG.Maint-GG.Other  end )*100 as OvbkAccomPerc 
					from 
					(select   C.Decision_ID,C.Property_ID,c.Accom_Class_ID,c.Accom_Class_Code,C.Rank_Order,c.Arrival_DT,c.LOS,c.Rate_Unqualified_ID,c.Rate_Code_Name,c.LRV,c.OCC_PERC,
					case when c.Occ_perc > =95 then 'YES' else 'NO' end as IsBuzy,c.Derived_Unqualified_Value,c.Decision_Reason_Type_ID 
					,E.Ovbk_accom 	  from 
					(select A.Decision_ID,A.Property_ID,A.Accom_Class_ID,A.Accom_Class_Code,A.Rank_Order,A.Arrival_DT,A.LOS,A.Rate_Unqualified_ID,A.Rate_Code_Name,A.LRV,B.OCC_PERC,A.Derived_Unqualified_Value,A.Decision_Reason_Type_ID  
					from
					(select DBO.Decision_ID,DBO.Property_ID
					,DBO.Accom_Class_ID,Ac.Accom_Class_Code,AC.Rank_Order,DBO.Arrival_DT,DBO.LOS,DBO.Rate_Unqualified_ID,
					RU.Rate_Code_Name,PBO.Derived_Unqualified_Value,DL.LRV,DBO.Decision_Reason_Type_ID
					from Decision_Bar_Output DBO,Rate_Unqualified RU,Accom_Class AC, Decision_LRV DL,PACE_Bar_Output PBO
					 where DBO.arrival_dt>=@dt and 
					  DBO.Property_ID = RU.Property_ID
					 and DBO.Rate_Unqualified_ID = RU.Rate_Unqualified_ID
					 and DBO.Accom_Class_ID = AC.Accom_Class_ID
					 and DBO.Property_ID = AC.Property_ID
					 and DBO.Property_ID = DL.Property_ID
					 and DBO.Accom_Class_ID= DL.Accom_Class_ID
					 and DBO.Arrival_DT = DL.Occupancy_DT
					 and DBO.Decision_ID = PBO.Decision_ID
					 and DBO.Arrival_DT= PBO.Arrival_DT
					 and DBO.Accom_Class_ID = PBO.Accom_Class_ID
					 and DBO.los = PBO.LOS
				   and  DBo.Accom_Class_ID in (select Accom_Class_ID from Accom_Class where Status_ID = 1))as A, 
					 (select A.Occupancy_DT,A.OCC_FCST,B.Cap,(OCC_FCST/Cap)*100 as OCC_PERC from 
					(select OCF.Occupancy_DT,SUM(occupancy_nbr) as OCC_FCST  from Occupancy_FCST OCF  where OCF.Occupancy_DT >=@dt  group by OCF.Occupancy_DT)as A ,
					
					(select  Occupancy_DT, case when (Total_Accom_Capacity-Rooms_Not_Avail_Maint-Rooms_Not_Avail_Other)<= 0  then Total_Accom_Capacity else 
					Total_Accom_Capacity-Rooms_Not_Avail_Maint-Rooms_Not_Avail_Other end as Cap
					
					 from Total_Activity) as B where
					A.Occupancy_DT= B.Occupancy_DT  ) as B  where A.Arrival_DT = B.occupancy_dt) as C, 					 
					(
					
					
					select Occupancy_DT,AT.Accom_Class_ID,SUM(Overbooking_Decision)as Ovbk_accom from Decision_Ovrbk_Accom DOA ,Accom_Type AT
	                 where Occupancy_DT >=@dt and  DOA.Accom_Type_ID = AT.Accom_Type_ID  and AT.Status_ID = 1 group by Occupancy_DT,AT.Accom_Class_ID ) as  E	
					 where C.Arrival_DT = E.Occupancy_DT
					 and C.Accom_Class_ID= E.Accom_Class_ID
					 ) AS C left outer join 
					 
					 (select PSE.Property_ID,PSE.Special_Event_Type_ID,PSE.Special_Event_Name,PSE.Start_DTTM,PSE.End_DTTM,PSEI.Start_DTTM as SE_Inst_stdt,
					PSEI.End_DTTM SE_Inst_enddt,PSEI.Pre_Event_Days,PSEI.Post_Event_Days,PSEI.Enable_Forecast,PSE.Impact_On_Forecast,PSE.Event_Frequency_ID 
					from Property_Special_Event PSE left outer join  Property_Special_Event_Instance PSEI 
					on PSE.Property_Special_Event_ID = PSEI.Property_Special_Event_ID
					where PSE.Status_ID = 1 and Impact_On_Forecast = 1 )as D on C.Property_ID = D.Property_ID
					and C.Arrival_DT between D.SE_Inst_stdt and D.SE_Inst_enddt
					 left outer join 
					 (	          	  select Occupancy_DT,AT.Accom_Class_ID,sum(Accom_Capacity)as PhyAccomCap,sum(Rooms_Not_Avail_Maint)as Maint,sum(Rooms_Not_Avail_Other)as Other  from Accom_Activity AA, Accom_Type AT  
	           	 where Occupancy_DT >=@dt
	           	 and  AA.accom_type_id = AT.accom_type_id and AT.Status_ID = 1 group by Occupancy_DT,AT.Accom_Class_ID) as GG
	           	 on c.arrival_Dt = GG.Occupancy_DT
	           	 and C.Accom_Class_ID = GG.Accom_Class_ID
	           	 
	         
					
					 order by c.Arrival_DT,c.LOS,c.Rank_Order				
					""")		
					System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_decisions.inp -o  ${resultdir}${sep}${file}_decisions.csv  -s\",\" -W -w 700")



                    def INP22 = new File("${resultdir}${sep}${file}_RECOM_Decision_FPLOS.inp")
			        INP22.write("--geneating by automated tool\n")
				    INP22.write("""\
	               set nocount on ;
					declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
					select V.maxdec,V.Accom_Type_ID_Actual,V.Occupancy_Date,V.Rate_qualified_ID_actual,V.Decision,V.Decision_Date_Time,V.Recom_actual,
					  V.Arrival_DT,V.Accom_Class_ID,V.Accom_Class_Code, V.Accom_Type_ID, V.Accom_Type_Code, V.Rate_Qualified_ID,V.Rate_Code_Name, V.LOS,V.Price,V.LRV, Recom_Exp ,Result  from 
					
					(select W.maxdec,W.Accom_Type_ID as Accom_Type_ID_Actual,W.Occupancy_Date,W.Rate_qualified_ID as Rate_qualified_ID_actual,W.Decision,W.Decision_Date_Time,W.Recom_actual,
					  X.Arrival_DT,X.Accom_Class_ID,X.Accom_Class_Code, X.Accom_Type_ID, X.Accom_Type_Code, X.Rate_Qualified_ID,X.Rate_Code_Name, X.LOS,X.Price,X.LRV, Recom_Exp , case when Recom_Exp <> Recom_actual then 'FAIL' else 'PASS' end as Result 
					   from    
					(select A.maxdec,A.Accom_Type_ID,A.Occupancy_Date,A.Rate_qualified_ID,B.Decision,B.Decision_Date_Time,LEFT(decision,1)as Recom_actual from 
					(select Accom_Type_ID ,Rate_qualified_ID,Occupancy_Date,MAX(Decision_Ack_Status_ID)as maxdec from Decision_Ack_Status where  Occupancy_Date >@dt and
					 Decision_Type = 'FPLOS'  and Rate_Qualified_ID is not null 
					and Accom_Type_ID is NOT null
					group by Occupancy_Date,Accom_Type_ID ,Rate_qualified_ID) A inner join
					(select * from Decision_Ack_Status where Decision_Type = 'FPLOS' and Occupancy_Date > @dt and Rate_Qualified_ID is not null and Accom_Type_ID is NOT null) B
					on A.Occupancy_Date=B.Occupancy_Date and A.Rate_Qualified_ID=B.Rate_qualified_ID and B.Decision_Ack_Status_ID=A.maxdec) as W  left outer join   
					(select A.Arrival_DT,A.Accom_Class_ID,AC.Accom_Class_Code, A.Accom_Type_ID, A.Accom_Type_Code, A.Rate_Qualified_ID,A.Rate_Code_Name, A.LOS,A.Price,DL.LRV,case when A.Price > DL.LRV then 'Y' else 'N' end as Recom_Exp from 
					(SELECT     dbo.Decision_Bar_Output.Accom_Class_ID, dbo.Accom_Type.Accom_Type_ID, dbo.Accom_Type.Accom_Type_Code, dbo.Rate_Qualified.Rate_Qualified_ID, 
				      dbo.Rate_Qualified.Rate_Code_Name, dbo.Decision_Bar_Output.Arrival_DT, dbo.Decision_Bar_Output.LOS, CASE DATEPART(weekday,Decision_Bar_Output.Arrival_DT) WHEN 1 THEN Rate_Qualified_Details.Sunday WHEN 2 THEN Rate_Qualified_Details.Monday WHEN 3 THEN Rate_Qualified_Details.Tuesday WHEN 4 THEN Rate_Qualified_Details.Wednesday
				       WHEN 5 THEN Rate_Qualified_Details.Thursday WHEN 6 THEN Rate_Qualified_Details.Friday WHEN 7 THEN Rate_Qualified_Details.Saturday ELSE 0 END AS Price
				     FROM  dbo.Decision_Bar_Output INNER JOIN 
					dbo.Rate_Qualified ON dbo.Decision_Bar_Output.Property_ID = dbo.Rate_Qualified.Property_ID 
					inner jOIN									
				      dbo.Accom_Type ON dbo.Decision_Bar_Output.Accom_Class_ID = dbo.Accom_Type.Accom_Class_ID INNER JOIN
				      dbo.Rate_Qualified_Details ON dbo.Rate_Qualified.Rate_Qualified_ID = dbo.Rate_Qualified_Details.Rate_Qualified_ID AND 
				      dbo.Accom_Type.Accom_Type_ID = dbo.Rate_Qualified_Details.Accom_Type_ID AND dbo.Decision_Bar_Output.Arrival_DT BETWEEN 
				      dbo.Rate_Qualified_Details.Start_Date_DT AND dbo.Rate_Qualified_Details.End_Date_DT ) as A inner join Accom_Class AC on 
				      A.Accom_Class_ID=AC.Accom_Class_ID  inner join Decision_LRV DL on A.Accom_Class_ID=DL.Accom_Class_ID and A.Arrival_DT=DL.Occupancy_DT
				       and AC.Status_ID=1  and LOS = 1  and Arrival_DT > @dt AND Rate_Qualified_ID = (select Rate_Qualified_ID from Rate_Qualified
				       where Rate_Code_Name= 'LV0')) as X  on 
				       W.Occupancy_Date=X.Arrival_DT
				       and W.Accom_Type_ID=X.Accom_Type_ID
                       and W.Rate_Qualified_ID=X.rate_qualified_id ) as V where V.Result = 'FAIL'												
	
					""")			
					System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_RECOM_Decision_FPLOS.inp -o  ${resultdir}${sep}${file}_RECOM_Decision_FPLOS.csv  -s\",\" -W -w 700")



	def INP23 = new File("${resultdir}${sep}${file}_RECOM_UNQUALIFIED_FPLOS.inp")
			        INP23.write("--geneating by automated tool\n")
				    INP23.write("""\
	               set nocount on ;
					declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
					select A.Occupancy_Date,A.Rate_Unqualified_ID,B.Decision,LEFT(B.Decision,1)as LOS1DECISION,B.Decision_Date_Time from
					(select Rate_Unqualified_ID,Occupancy_Date,MAX(Decision_Ack_Status_ID)as maxdec from Decision_Ack_Status where Decision_Type = 'FPLOS' and Occupancy_Date > @dt 
					group by Occupancy_Date,Rate_Unqualified_ID ) A inner join
					(select * from Decision_Ack_Status where Decision_Type = 'FPLOS' and Occupancy_Date > @dt) B
					on A.Occupancy_Date=B.Occupancy_Date and A.Rate_Unqualified_ID=B.Rate_Unqualified_ID and B.Decision_Ack_Status_ID=A.maxdec 
					 order by A.Occupancy_Date,A.Rate_Unqualified_ID
					
													
	
					""")			
					System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_RECOM_UNQUALIFIED_FPLOS.inp -o  ${resultdir}${sep}${file}_RECOM_UNQUALIFIED_FPLOS.csv  -s\",\" -W -w 700")		
					
					
					
	                             def INP24 = new File("${resultdir}${sep}${file}_RECOM_Property_Overbooking.inp")
				    INP24.write("--geneating by automated tool\n")
				    INP24.write("""\
		                  declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
				  select * from
				  (
				        select maxDecision.Occupancy_Date,maxDecision.Decision as AckDecision, ovbk_Prop.Overbooking_Decision mainDecision,
				        issue=case when maxDecision.Decision!=ovbk_Prop.Overbooking_Decision then '1' else '0' end
				        from
				        (
				              select maxId.Occupancy_Date,ack.Decision from 
				              (
				                    select  Occupancy_Date,MAX(Decision_Ack_Status_ID)Decision_Ack_Status_ID  from  Decision_Ack_Status where Occupancy_Date > @dt and Decision_Type = 'Overbooking'
				                     and accom_type_id is null group by Occupancy_Date,Accom_Type_ID
				              )maxId inner join
				              (
				                    select  Decision_Ack_Status_ID,Occupancy_Date,decision,Decision_Date_Time  from  Decision_Ack_Status where Occupancy_Date > @dt and Decision_Type = 'Overbooking' and accom_type_id is null
				              ) ack on maxId.Decision_Ack_Status_ID=ack.Decision_Ack_Status_ID
				        ) maxDecision inner join
				        (
				              select * from Decision_Ovrbk_Property where Occupancy_Dt > @dt
				        ) ovbk_Prop on maxDecision.Occupancy_Date=ovbk_Prop.Occupancy_DT ) A where issue=1;
																		
					""")			
					System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_RECOM_Property_Overbooking.inp -o  ${resultdir}${sep}${file}_RECOM_Property_Overbooking.csv  -s\",\" -W -w 700")
					
					
					def INP25 = new File("${resultdir}${sep}${file}_RECOM_Accom_Overbooking.inp")
								        INP25.write("--geneating by automated tool\n")
									    INP25.write("""\
						                set nocount on ;
							declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
								select *  from
								(
									select maxDecision.Occupancy_Date,maxDecision.Accom_Type_ID,maxDecision.Decision as AckDecision, ovbk_accom.Overbooking_Decision mainDecision,
									issue=case when maxDecision.Decision!=ovbk_accom.Overbooking_Decision then '1' else '0' end
									 from
									(
										select maxId.Occupancy_Date,maxId.Accom_Type_ID,ack.Decision from 
										(
											select  Occupancy_Date,Accom_Type_ID,MAX(Decision_Ack_Status_ID)Decision_Ack_Status_ID  from  Decision_Ack_Status where Occupancy_Date > @dt  and Decision_Type = 'Overbooking'   and accom_type_id <> '' group by Occupancy_Date,Accom_Type_ID
										)maxId inner join
										(
											select  Decision_Ack_Status_ID,Occupancy_Date,Accom_Type_ID,decision,Decision_Date_Time  from  Decision_Ack_Status where Occupancy_Date > @dt  and Decision_Type = 'Overbooking'   and accom_type_id <> ''
										) ack on maxId.Decision_Ack_Status_ID=ack.Decision_Ack_Status_ID
									) maxDecision inner join (
										select * from Decision_Ovrbk_Accom where Occupancy_Dt > @dt
									) ovbk_accom on maxDecision.Occupancy_Date=ovbk_accom.Occupancy_DT and maxDecision.Accom_Type_ID=ovbk_accom.Accom_Type_ID
								) A where issue=1			
						
										""")			
										System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_RECOM_Accom_Overbooking.inp -o  ${resultdir}${sep}${file}_RECOM_Accom_Overbooking.csv  -s\",\" -W -w 700")	
										
										
				def INP26 = new File("${resultdir}${sep}${file}_CP_Decisions.inp")
								        INP26.write("--geneating by automated tool\n")
									    INP26.write("""\
									             
										set nocount on ;
                                        declare @dt date = (select   max(snapshot_dt) from   File_Metadata where IsBDE = 1  and Process_Status_ID = 13);
                                        select C.Decision_ID,C.Property_ID,C.Accom_Type_ID,C.Accom_Type_Code,c.Accom_Class_ID,c.Accom_Class_Code,C.Rank_Order,c.Arrival_DT,c.LOS,C.optimal_bar,c.LRV,C.Pretty_BAR,C.Floor_Rate,C.Ceil_Rate,C.User_Specified_Rate,C.Final_BAR,C.Rooms_Only_BAR,c.OCC_PERC,
					 IsBuzy,case when C.Arrival_DT between D.SE_Inst_stdt and D.End_DTTM then 'YES' else 'NO' end as SEDAY,
					case when GG.ROOMSSOLD_AC = 0  then 0 else  GG.ROOMREV_AC/GG.ROOMSSOLD_AC end   as ADR_AC,
					
					c.Decision_Reason_Type_ID ,C.Ovbk_accom,(C.Ovbk_accom/case when GG.PhyAccomCap-GG.Maint-GG.Other <=0 then PhyAccomCap else GG.PhyAccomCap-GG.Maint-GG.Other  end )*100 as OvbkAccomPerc ,GG.PhyAccomCap,GG.Maint,GG.Other,GG.ROOMSSOLD_AC,GG.ROOMREV_AC
					
					from 
					(select   C.Decision_ID,C.Property_ID,C.Accom_Type_ID,C.Accom_Type_Code,c.Accom_Class_ID,c.Accom_Class_Code,C.Rank_Order,c.Arrival_DT,c.LOS,C.optimal_bar,c.LRV,C.Pretty_BAR,C.Floor_Rate,C.Ceil_Rate,C.User_Specified_Rate,C.Final_BAR,C.Rooms_Only_BAR,c.OCC_PERC,
					case when c.Occ_perc > =95 then 'YES' else 'NO' end as IsBuzy,c.Decision_Reason_Type_ID 
					,E.Ovbk_accom 	  from 
					
					(
				
					select A.Decision_ID,A.Property_ID,A.Accom_Type_ID,A.Accom_Type_Code,A.Accom_Class_ID,A.Accom_Class_Code,A.Rank_Order,A.Arrival_DT,A.LOS,A.optimal_bar,A.LRV,A.Pretty_BAR,A.Floor_Rate,A.Ceil_Rate,A.User_Specified_Rate,A.Final_BAR,A.Rooms_Only_BAR,B.OCC_PERC,
					A.Decision_Reason_Type_ID  
					from
					(select CPO.Decision_ID,CPO.Property_ID,Arrival_DT,CPO.Accom_Type_ID,AT.Accom_Type_Code,AC.Accom_Class_ID,AC.Accom_Class_Code,AC.Rank_Order ,LOS,optimal_bar,LRV,Pretty_BAR,override,floor_rate,ceil_rate,user_specified_rate,final_bar,rooms_only_bar,Decision_Reason_Type_ID
                                        from CP_Decision_Bar_Output CPO,Accom_Class AC,CP_Cfg_AC CPAC,Accom_Type AT,Decision_LRV DL
					where CPO.Accom_Type_ID =CPAC.Accom_Type_ID and CPAC.Accom_Type_ID=AT.Accom_Type_ID and AT.Accom_Class_ID= AC.Accom_Class_ID
					and CPO.Decision_id =DL.Decision_ID
					and AC.Accom_Class_ID=DL.Accom_Class_ID
					and CPO.Arrival_DT=DL.Occupancy_DT
					and Arrival_DT >=@dt
				   )as A
				   			   				   
				   , 
					 (select A.Occupancy_DT,A.OCC_FCST,B.Cap,(OCC_FCST/Cap)*100 as OCC_PERC from 
					(select OCF.Occupancy_DT,SUM(occupancy_nbr) as OCC_FCST  from Occupancy_FCST OCF  where OCF.Occupancy_DT >=@dt  group by OCF.Occupancy_DT)as A ,
					
					(select  Occupancy_DT, case when (Total_Accom_Capacity-Rooms_Not_Avail_Maint-Rooms_Not_Avail_Other)<= 0  then Total_Accom_Capacity else 
					Total_Accom_Capacity-Rooms_Not_Avail_Maint-Rooms_Not_Avail_Other end as Cap
					
					 from Total_Activity) as B where
					A.Occupancy_DT= B.Occupancy_DT  ) as B  where A.Arrival_DT = B.occupancy_dt) as C, 					 
					(
					
					
					select Occupancy_DT,AT.Accom_Class_ID,SUM(Overbooking_Decision)as Ovbk_accom from Decision_Ovrbk_Accom DOA ,Accom_Type AT
	                                where Occupancy_DT >=@dt and  DOA.Accom_Type_ID = AT.Accom_Type_ID  and AT.Status_ID = 1 group by Occupancy_DT,AT.Accom_Class_ID ) as  E	
					 where C.Arrival_DT = E.Occupancy_DT
					 and C.Accom_Class_ID= E.Accom_Class_ID
					 ) AS C left outer join 
					 
					 (select PSE.Property_ID,PSE.Special_Event_Type_ID,PSE.Special_Event_Name,PSE.Start_DTTM,PSE.End_DTTM,PSEI.Start_DTTM as SE_Inst_stdt,
					PSEI.End_DTTM SE_Inst_enddt,PSEI.Pre_Event_Days,PSEI.Post_Event_Days,PSEI.Enable_Forecast,PSE.Impact_On_Forecast,PSE.Event_Frequency_ID 
					from Property_Special_Event PSE left outer join  Property_Special_Event_Instance PSEI 
					on PSE.Property_Special_Event_ID = PSEI.Property_Special_Event_ID
					where PSE.Status_ID = 1 and Impact_On_Forecast = 1 )as D on C.Property_ID = D.Property_ID
					and C.Arrival_DT between D.SE_Inst_stdt and D.SE_Inst_enddt
					
					 left outer join 
					 
		    		 (select Occupancy_DT,AT.Accom_Class_ID,SUM(rooms_sold)as ROOMSSOLD_AC,sum(Accom_Capacity)as PhyAccomCap,sum(Rooms_Not_Avail_Maint)as Maint,
					 sum(Rooms_Not_Avail_Other)as Other,SUM(room_revenue)as ROOMREV_AC  from Accom_Activity AA, Accom_Type AT  
					 where Occupancy_DT >=@dt
					 and  AA.accom_type_id = AT.accom_type_id and AT.Status_ID = 1 group by Occupancy_DT,AT.Accom_Class_ID) as GG
					 on c.arrival_Dt = GG.Occupancy_DT
	           	     and C.Accom_Class_ID = GG.Accom_Class_ID        	 
	        		 order by c.Arrival_DT,c.LOS,c.Rank_Order
					 
					 
 
														
						
										""")			
					System("%sqlcmd% -d ${args} -i ${resultdir}${sep}${file}_CP_Decisions.inp -o  ${resultdir}${sep}${file}_CP_Decisions.csv  -s\",\" -W -w 700")
					}

				

				
}

