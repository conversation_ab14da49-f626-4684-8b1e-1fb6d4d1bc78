<?xml version="1.0" encoding="utf-8"?>
<recordtable
rxversion="5.1.0.18625"
id="346ff1db-88b9-4ca5-9ecc-cda8c282e8f6"
embeddedrepository="false"
mode="Global">
	<name>
		RateShoppingConfigure
	</name>
	<repeatcount>
		1
	</repeatcount>
	<turbomode>
		False
	</turbomode>
	<genreport>
		True
	</genreport>
	<speedfactor>
		1
	</speedfactor>
	<useDefaultItemLogging>
		True
	</useDefaultItemLogging>
	<codegen
	classname="RateShoppingConfigure"
	namespace="ProdSanity"/>
	<variables/>
	<recorditems>
		<mouseitem
		enabled="True"
		optional="True"
		duration="500ms"
		searchpropertyid="11404dd0-16dd-4e60-96f8-aade35e6734f"
		action="Move"
		button="Left"
		movetime="300ms"
		sx="483"
		sy="143"
		loc="Center">
			<comment/>
			<bindings/>
			<info
			sourcename="Recorder"
			id="d454ea30-af96-4e8c-8fdd-2acc55ff7559">
				<element
				role="unknown"
				prefcap="atag"
				capabilities="webelement, atag"
				flavor="mozillaweb"
				visible="True"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="True"
				index="0"
				rect="442,130,74,15"
				snapElem="">
					<attribute
					name="hidden"
					type="System.String">
						False
					</attribute>
					<attribute
					name="innertext"
					type="System.String">
						&#10;                            Configure
                       &#32;
					</attribute>
					<attribute
					name="tagname"
					type="System.String">
						a
					</attribute>
					<attribute
					name="href"
					type="System.String">
						javascript:void(0);
					</attribute>
				</element>
				<path>
					/dom[@domain='localhost:8080']//ul[#'menu']/li[6]/a[@innertext~'^\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ ']
				</path>
			</info>
			<helperscreenshot
			guid="cb4873bd-f840-4da2-85e9-707b94ee9a04">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************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==
			</helperscreenshot>
		</mouseitem>
		<mouseitem
		enabled="True"
		optional="True"
		duration="100ms"
		searchpropertyid="a423906c-f154-4e21-b730-4c352856bd8c"
		action="DoubleClick"
		button="Left"
		movetime="300ms"
		sx="339"
		sy="165"
		loc="Center">
			<comment/>
			<bindings/>
			<info
			sourcename="Recorder"
			id="44f84431-ae52-4793-895a-660707e0c197">
				<path>
					/dom[@domain='localhost:8080']//ul[#'menu']/li[6]/ul/?/?/a[@innertext~'^Rate\ Shopping\ Configurati']
				</path>
			</info>
		</mouseitem>
		<validationitem
		enabled="True"
		optional="True"
		duration="100ms"
		searchpropertyid="6a61c7bb-91fa-4b9c-828d-042795420ea8"
		action="AttributeEqual">
			<comment/>
			<bindings/>
			<info
			sourcename="ElementTree"
			id="c5d32c80-9011-4933-b9c9-d6b40d8b75ec">
				<element
				role="button"
				prefcap="button"
				capabilities="flexelement"
				flavor="flex"
				visible="True"
				valid="True"
				enabled="False"
				hasfocus="False"
				dontcache="True"
				index="1"
				rect="466,429,57,24"
				snapElem="">
					<attribute
					name="pressed"
					type="System.Boolean">
						False
					</attribute>
					<attribute
					name="text"
					type="System.String">
						Save
					</attribute>
					<attribute
					name="basetype"
					type="System.String">
						mx.controls::Button
					</attribute>
					<attribute
					name="flexuid"
					type="System.String">
						CustomAttributesPortlet0.CustomAttributesView4.VBox6.HBox8._CustomAttributesView_BusyIndicatorCanvas1.attributeDetails.booleanTypeSection.btnUpdateBooleanTypeAttr
					</attribute>
					<attribute
					name="fulltype"
					type="System.String">
						mx.controls::Button
					</attribute>
					<attribute
					name="id"
					type="System.String">
						btnUpdateBooleanTypeAttr
					</attribute>
					<attribute
					name="type"
					type="System.String">
						Button
					</attribute>
					<dynamicattribute
					name="alpha"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="autoRepeat"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="baselinePosition"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						16
					</dynamicattribute>
					<dynamicattribute
					name="blendMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						normal
					</dynamicattribute>
					<dynamicattribute
					name="buttonMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="cacheAsBitmap"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="cachePolicy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						auto
					</dynamicattribute>
					<dynamicattribute
					name="className"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						Button
					</dynamicattribute>
					<dynamicattribute
					name="contentMouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						896
					</dynamicattribute>
					<dynamicattribute
					name="contentMouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-25
					</dynamicattribute>
					<dynamicattribute
					name="currentState"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="doubleClickEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="emphasized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="enabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						False
					</dynamicattribute>
					<dynamicattribute
					name="errorString"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMaxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMaxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="focusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="height"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="helpBundle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="helpId"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						btnUpdateBooleanTypeAttr
					</dynamicattribute>
					<dynamicattribute
					name="iconHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="iconWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="includeInLayout"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="initialized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="instanceIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="isDocument"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="isPopUp"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="label"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						Save
					</dynamicattribute>
					<dynamicattribute
					name="labelPlacement"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						right
					</dynamicattribute>
					<dynamicattribute
					name="maxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						10000
					</dynamicattribute>
					<dynamicattribute
					name="maxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						10000
					</dynamicattribute>
					<dynamicattribute
					name="measuredHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="measuredMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="measuredMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						57
					</dynamicattribute>
					<dynamicattribute
					name="measuredWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						57
					</dynamicattribute>
					<dynamicattribute
					name="minHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="minWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						57
					</dynamicattribute>
					<dynamicattribute
					name="mouseChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="mouseEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseFocusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						896
					</dynamicattribute>
					<dynamicattribute
					name="mouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-25
					</dynamicattribute>
					<dynamicattribute
					name="name"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						btnUpdateBooleanTypeAttr
					</dynamicattribute>
					<dynamicattribute
					name="needsSoftKeyboard"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="nestLevel"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						9
					</dynamicattribute>
					<dynamicattribute
					name="numAutomationChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="numChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						2
					</dynamicattribute>
					<dynamicattribute
					name="percentHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="percentWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="processedDescriptors"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="repeaterIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="reportTheme"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotation"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="scaleX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="scaleY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="scaleZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="selected"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="showInAutomationHierarchy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="tabChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="tabEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="tabIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="toggle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="toolTip"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="uid"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						CustomAttributesPortlet0.CustomAttributesView4.VBox6.HBox8._CustomAttributesView_BusyIndicatorCanvas1.attributeDetails.booleanTypeSection.btnUpdateBooleanTypeAttr
					</dynamicattribute>
					<dynamicattribute
					name="updateCompletePendingFlag"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="useHandCursor"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="validationSubField"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="visible"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						True
					</dynamicattribute>
					<dynamicattribute
					name="width"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						57
					</dynamicattribute>
					<dynamicattribute
					name="x"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						264
					</dynamicattribute>
					<dynamicattribute
					name="y"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="z"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
				</element>
				<path>
					/dom[@domain='localhost:8080']//div[#'portlet_content_one']/flexobject[@id='portlet_flash']//container[@id='_CustomAttributesView_BusyIndicatorCanvas1']/?/?/container[@id='booleanTypeSection']/button[@id='btnUpdateBooleanTypeAttr']
				</path>
			</info>
			<helperscreenshot
			guid="e03fa6fc-ed69-4031-9659-758af8ca5cae">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************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=
			</helperscreenshot>
			<matchname>
				Text
			</matchname>
			<matchvalue>
				Save
			</matchvalue>
		</validationitem>
		<validationitem
		enabled="True"
		optional="False"
		duration="100ms"
		searchpropertyid="a5735ee5-fbe8-4ce6-8438-721b46b5e3fd"
		action="AttributeEqual">
			<comment/>
			<bindings/>
			<info
			sourcename="ElementTree"
			id="ac4204f8-e146-4fa1-a6dd-34b256be1bda">
				<element
				role="text"
				prefcap="text"
				capabilities="flexelement"
				flavor="flex"
				visible="False"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="True"
				index="0"
				rect="12,962,24,20"
				snapElem="">
					<attribute
					name="caption"
					type="System.String">
					</attribute>
					<attribute
					name="selectionlength"
					type="System.Int32">
						0
					</attribute>
					<attribute
					name="selectionstart"
					type="System.Int32">
						0
					</attribute>
					<attribute
					name="selectiontext"
					type="System.String">
					</attribute>
					<attribute
					name="text"
					type="System.String">
					</attribute>
					<attribute
					name="basetype"
					type="System.String">
						mx.controls::Label
					</attribute>
					<attribute
					name="flexuid"
					type="System.String">
						AlertManagerPortlet0.InformationManagerView4.VBox6.StatusBar17.button.statusArea
					</attribute>
					<attribute
					name="fulltype"
					type="System.String">
						mx.controls::Label
					</attribute>
					<attribute
					name="id"
					type="System.String">
						statusArea
					</attribute>
					<attribute
					name="type"
					type="System.String">
						Label
					</attribute>
					<dynamicattribute
					name="alpha"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="baselinePosition"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						14
					</dynamicattribute>
					<dynamicattribute
					name="blendMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						normal
					</dynamicattribute>
					<dynamicattribute
					name="buttonMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="cacheAsBitmap"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="cachePolicy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						auto
					</dynamicattribute>
					<dynamicattribute
					name="className"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						Label
					</dynamicattribute>
					<dynamicattribute
					name="condenseWhite"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="contentMouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1064
					</dynamicattribute>
					<dynamicattribute
					name="contentMouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-284
					</dynamicattribute>
					<dynamicattribute
					name="currentState"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="doubleClickEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="enabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						True
					</dynamicattribute>
					<dynamicattribute
					name="errorString"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMaxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMaxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="focusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="height"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						20
					</dynamicattribute>
					<dynamicattribute
					name="helpBundle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="helpId"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						statusArea
					</dynamicattribute>
					<dynamicattribute
					name="htmlText"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="includeInLayout"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="initialized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="instanceIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="isDocument"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="isPopUp"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="maxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						10000
					</dynamicattribute>
					<dynamicattribute
					name="maxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						10000
					</dynamicattribute>
					<dynamicattribute
					name="measuredHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						20
					</dynamicattribute>
					<dynamicattribute
					name="measuredMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						20
					</dynamicattribute>
					<dynamicattribute
					name="measuredMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="measuredWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="minHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						20
					</dynamicattribute>
					<dynamicattribute
					name="minWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="mouseChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseFocusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1064
					</dynamicattribute>
					<dynamicattribute
					name="mouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-284
					</dynamicattribute>
					<dynamicattribute
					name="name"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						statusArea
					</dynamicattribute>
					<dynamicattribute
					name="needsSoftKeyboard"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="nestLevel"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						7
					</dynamicattribute>
					<dynamicattribute
					name="numAutomationChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="numChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="percentHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="percentWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="processedDescriptors"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="repeaterIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="reportTheme"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotation"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="scaleX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="scaleY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="scaleZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="selectable"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="showInAutomationHierarchy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="tabChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="tabEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="tabIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="textHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="textWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="toolTip"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="uid"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						AlertManagerPortlet0.InformationManagerView4.VBox6.StatusBar17.button.statusArea
					</dynamicattribute>
					<dynamicattribute
					name="updateCompletePendingFlag"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="useHandCursor"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="validationSubField"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="visible"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						False
					</dynamicattribute>
					<dynamicattribute
					name="width"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="x"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						5
					</dynamicattribute>
					<dynamicattribute
					name="y"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="z"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
				</element>
				<path>
					/dom[@caption='IDeaS G3' and @page='information-manager' and @path='/solutions/information-manager' and @browsername='Mozilla']//div[#'portlet_content_one']/flexobject[@id='portlet_flash']/container/container[4]//text[@id='statusArea']
				</path>
			</info>
			<helperscreenshot
			guid="f97d962a-2ce5-4981-8513-3e7196cc6934">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************
qqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqr/wAARCAFmAfQDASIA
AhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQA
AAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3
ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWm
p6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEA
AwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSEx
BhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElK
U1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3
uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwDoKKKK
ACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA
KKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAo
oooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACii
igAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKK
ACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA
KKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAo
oooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACii
igAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKK
ACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA
KKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAo
oooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACii
igAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKK
ACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA
KKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAo
oooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACii
igAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKK
ACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA
KKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAo
oooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACii
igAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKK
ACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA
KKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAo
oooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACii
igAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKK
ACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA
KKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAo
oooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACii
igAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKK
ACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA
KKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAo
oooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACii
igAooooAKKKKACiiigAooooAKKKKACis3+2rb+5L+Q/xo/tq2/uS/kP8annj3I549zSorN/tq2/u
S/kP8aP7atv7kv5D/Gjnj3Dnj3NKis3+2rb+5L+Q/wAaP7atv7kv5D/Gjnj3Dnj3NKis3+2rb+5L
+Q/xo/tq2/uS/kP8aOePcOePc0qKzf7atv7kv5D/ABo/tq2/uS/kP8aOePcOePc0qKzf7atv7kv5
D/Gj+2rb+5L+Q/xo549w549zSorN/tq2/uS/kP8AGj+2rb+5L+Q/xo549w549zSorN/tq2/uS/kP
8aP7atv7kv5D/Gjnj3Dnj3NKis3+2rb+5L+Q/wAaP7atv7kv5D/Gjnj3Dnj3NKis3+2rb+5L+Q/x
o/tq2/uS/kP8aOePcOePc0qKzf7atv7kv5D/ABo/tq2/uS/kP8aOePcOePc0qKzf7atv7kv5D/Gj
+2rb+5L+Q/xo549w549zSorN/tq2/uS/kP8AGj+2rb+5L+Q/xo549w549zSorN/tq2/uS/kP8aP7
atv7kv5D/Gjnj3Dnj3NKis3+2rb+5L+Q/wAaP7atv7kv5D/Gjnj3Dnj3NKis3+2rb+5L+Q/xo/tq
2/uS/kP8aOePcOePc0qKzf7atv7kv5D/ABo/tq2/uS/kP8aOePcOePc0qKzf7atv7kv5D/Gj+2rb
+5L+Q/xo549w549zSorN/tq2/uS/kP8AGj+2rb+5L+Q/xo549w549zSorN/tq2/uS/kP8aP7atv7
kv5D/Gjnj3Dnj3NKis3+2rb+5L+Q/wAaP7atv7kv5D/Gjnj3Dnj3NKis3+2rb+5L+Q/xo/tq2/uS
/kP8aOePcOePc0qKzf7atv7kv5D/ABo/tq2/uS/kP8aOePcOePc0qKzf7atv7kv5D/Gj+2rb+5L+
Q/xo549w549zSorN/tq2/uS/kP8AGj+2rb+5L+Q/xo549w549zSorN/tq2/uS/kP8aP7atv7kv5D
/Gjnj3Dnj3NKis3+2rb+5L+Q/wAaP7atv7kv5D/Gjnj3Dnj3NKis3+2rb+5L+Q/xo/tq2/uS/kP8
aOePcOePc0qKzf7atv7kv5D/ABo/tq2/uS/kP8aOePcOePc0qKzf7atv7kv5D/Gj+2rb+5L+Q/xo
549w549zSorN/tq2/uS/kP8AGj+2rb+5L+Q/xo549w549zSorN/tq2/uS/kP8aKOePcOePc0to/v
N+dG0f3m/OofNo82qLJto/vN+dG0f3m/OofNo82gCbaP7zfnRtH95vzqHzaPNoAm2j+8350bR/eb
86h82jzaAJto/vN+dG0f3m/OofNo82gCbaP7zfnRtH95vzqHzaPNoAm2j+8350bR/eb86h82jzaA
Jto/vN+dG0f3m/OofNo82gCbaP7zfnRtH95vzqHzaPNoAm2j+8350bR/eb86h82jzaAJto/vN+dG
0f3m/OofNo82gCbaP7zfnRtH95vzqHzaPNoAm2j+8350bR/eb86h82jzaAJto/vN+dG0f3m/OofN
o82gCbaP7zfnRtH95vzqHzaPNoAm2j+8350bR/eb86h82jzaAJto/vN+dG0f3m/OofNo82gCbaP7
zfnRtH95vzqHzaPNoAm2j+8350bR/eb86h82jzaAJto/vN+dG0f3m/OofNo82gCbaP7zfnRtH95v
zqHzaPNoAm2j+8350bR/eb86h82jzaAJto/vN+dG0f3m/OofNo82gCbaP7zfnRtH95vzqHzaPNoA
m2j+8350bR/eb86h82jzaAJto/vN+dG0f3m/OofNo82gCbaP7zfnRtH95vzqHzaPNoAm2j+8350b
R/eb86h82jzaAJto/vN+dG0f3m/OofNo82gCbaP7zfnRtH95vzqHzaPNoAm2j+8350bR/eb86h82
jzaAJto/vN+dFQ+bRQBlf2qn/PJvzo/tVP8Ank351D9rk/ur+VH2uT+6v5Vr7GXf+vvOL20u5N/a
qf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+
6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u
5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP
2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vv
D20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eT
fnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GX
f+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tV
P+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/l
R7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvz
o/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P
7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8A
PJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5U
fa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/a
qf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+
6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u
5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP
2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vv
D20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eT
fnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GX
f+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tV
P+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzoqH7XJ/dX8qKPYy7/wBfeHtpdyvR
Wh/ZE/8Az0i/M/4Uf2RP/wA9IvzP+Fb88e5Hs59jPorQ/sif/npF+Z/wo/sif/npF+Z/wo549w9n
PsZ9FaH9kT/89IvzP+FH9kT/APPSL8z/AIUc8e4ezn2M+itD+yJ/+ekX5n/Cj+yJ/wDnpF+Z/wAK
OePcPZz7GfRWh/ZE/wDz0i/M/wCFH9kT/wDPSL8z/hRzx7h7OfYz6K0P7In/AOekX5n/AAo/sif/
AJ6Rfmf8KOePcPZz7GfRWh/ZE/8Az0i/M/4Uf2RP/wA9IvzP+FHPHuHs59jPorQ/sif/AJ6Rfmf8
KP7In/56Rfmf8KOePcPZz7GfRWh/ZE//AD0i/M/4Uf2RP/z0i/M/4Uc8e4ezn2M+itD+yJ/+ekX5
n/Cj+yJ/+ekX5n/Cjnj3D2c+xn0Vof2RP/z0i/M/4Uf2RP8A89IvzP8AhRzx7h7OfYz6K0P7In/5
6Rfmf8KP7In/AOekX5n/AAo549w9nPsZ9FaH9kT/APPSL8z/AIUf2RP/AM9IvzP+FHPHuHs59jPo
rQ/sif8A56Rfmf8ACj+yJ/8AnpF+Z/wo549w9nPsZ9FaH9kT/wDPSL8z/hR/ZE//AD0i/M/4Uc8e
4ezn2M+itD+yJ/8AnpF+Z/wo/sif/npF+Z/wo549w9nPsZ9FaH9kT/8APSL8z/hR/ZE//PSL8z/h
Rzx7h7OfYz6K0P7In/56Rfmf8KP7In/56Rfmf8KOePcPZz7GfRWh/ZE//PSL8z/hR/ZE/wDz0i/M
/wCFHPHuHs59jPorQ/sif/npF+Z/wo/sif8A56Rfmf8ACjnj3D2c+xn0Vof2RP8A89IvzP8AhR/Z
E/8Az0i/M/4Uc8e4ezn2M+itD+yJ/wDnpF+Z/wAKP7In/wCekX5n/Cjnj3D2c+xn0Vof2RP/AM9I
vzP+FH9kT/8APSL8z/hRzx7h7OfYz6K0P7In/wCekX5n/Cj+yJ/+ekX5n/Cjnj3D2c+xn0Vof2RP
/wA9IvzP+FH9kT/89IvzP+FHPHuHs59jPorQ/sif/npF+Z/wo/sif/npF+Z/wo549w9nPsZ9FaH9
kT/89IvzP+FH9kT/APPSL8z/AIUc8e4ezn2M+itD+yJ/+ekX5n/Cj+yJ/wDnpF+Z/wAKOePcPZz7
GfRWh/ZE/wDz0i/M/wCFH9kT/wDPSL8z/hRzx7h7OfYz6K0P7In/AOekX5n/AAo/sif/AJ6Rfmf8
KOePcPZz7GfRWh/ZE/8Az0i/M/4Uf2RP/wA9IvzP+FHPHuHs59jPorQ/sif/AJ6Rfmf8KKOePcPZ
z7GzRRRXGegFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAF
FFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUU
UUAFFFFABRRRQAUUUUAFFFFABRVT7en/ADwuv+/Df4Ufb0/54XX/AH4b/CgC3RVT7en/ADwuf+/D
f4Uf2hEM5jnGBnmIjigC3RVQahGRkQXJB7iBv8KPt6f8+91/34b/AAoAt0VU+3p/z73X/fhv8KPt
6f8APC6/78N/hQBboqp9vT/nhdf9+G/wo+3p/wA8Ln/vw3+FAFuiqv29P+eFz/34b/CmnUYQP9XP
1x/qj19KALlFVPt6f8+91/34b/Cj7en/AD73X/fhv8KALdFVPt6f88Lr/vw3+FH29P8Anhdf9+G/
woAt0VU+3p/zwuv+/Df4Uv29P+eFz/34b/CgC1RVT+0I+f3Vxx1/dNxR/aMWQBFOSRnAiJ4oAt0V
U+3p/wA+91/34b/Cj7en/Pvdf9+G/wAKALdFVPt6f88Lr/vw3+FH29P+eF1/34b/AAoAt0VU+3p/
zwuv+/Df4Ufb0/54XP8A34b/AAoAt0VU/tCMYzFcDPrCwoGoRnO2G4bBwdsLGgC3RVT7en/Pvdf9
+G/wo+3p/wA+91/34b/CgC3RVT7en/PC6/78N/hR9vT/AJ4XX/fhv8KALdFVPt6f88Lr/vw3+FH2
9P8Anhc/9+G/woAt0VUGoR5x5U4PoYiKX7cn/PKf/v0aALVFVftyf88p/wDv0aPtyf8APKf/AL9G
gC1RVX7cn/PKf/v0aPtyf88p/wDv0aALVFVftyf88p/+/Ro+3J/zyn/79GgC1RVX7cn/ADyn/wC/
Ro+3J/zyn/79GgC1RVX7cn/PKf8A79Gj7cn/ADyn/wC/RoAtUVV+3J/zyn/79Gj7cn/PKf8A79Gg
C1RVX7cn/PKf/v0aPtyf88p/+/RoAtUVV+3J/wA8p/8Av0aPtyf88p/+/RoAtUVV+3J/zyn/AO/R
o+3J/wA8p/8Av0aALVFVftyf88p/+/Ro+3J/zyn/AO/RoAtUVV+3J/zyn/79Gj7cn/PKf/v0aALV
FVftyf8APKf/AL9Gj7cn/PKf/v0aALVFVftyf88p/wDv0aPtyf8APKf/AL9GgC1RVX7cn/PKf/v0
aKAH5PdSPwppYZx7ZpguR60gOWJ9h/KtHGxJJmqmof6l/wDrlJ/SrGar3vMLf9cn/pQI0ieaZLMs
UZkc4UdaRm+Y/WquonNlJ+H8xSirtIJOybGvq0I+6jt+lV31eQ/ciUfU5qC3tBLEZpZVhizgMRnJ
+lR3dq1rIAWDKwyrDoRXQoQvY5nOdrm8TTSac1qqoXa5lCgZz8uP5VVhkLSOAXKYBQuBkjnnge1Y
JXOhvWxPmq//AC/x/wDXX/2mamzUP/L4h/6af+0zSGX91NL8nG0AdSxwBTd1Ur6yW5+YOVf68VLv
bQ0hyt+89C/vBPUH0IOQaazhQSTgCudVrixmxyDnp2NdNBDhQ8uN2OnZamMr7mlaj7OzTumVY7iO
UkKSGHVSMGpM0y4MU0qNEgyhzvHGeMf5+lGa0RzsQf8AHw3+4v8AM0yw4mk/65R/+zU4f68/7q/z
NMtOJHP/AEzj/wDZqAL26jNR7qZIxbai9WPb0pWG2OM2W2xqXPTjpTZJtg5GW7gc4pksqxYiRlXj
lvT2qHzFjBKzKw7gkc1kr1XppH8wb5VruTJNvG4YxUmaqyjyZg46HhqnzW3JykRnzobPzF/wJf8A
0IU+wOLX/gb/APoZpkx/dn6r/wChCltDi3H++/8A6GaGUWt1RyTpGCWYDFG6suVhJcO/XJrOc1DU
1hBzvYufbd33V49TSx3Qd9pwCelZ87BeSwHA2iq4nKyAr97PHvW9O1SHNY5J88J8rZu7gaM0jEFV
kxt39j9KbmsoS5kbtWGS/wCtj/3W/pRRJ/rE+jf0oq0SFFFFMAooooAKKKKACiiigAooooAKKKKA
CiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAZbaeXgEk8pQEZ49KcrYwQ29GPDYxTpGaZ
ohGCPl2vnpjuCKa8YjCxqxPOcE5wM1HNJvUofUF1yhH/AEzf+lT1Bc9v+ub/APstUItMfmP1qtfn
No/4fzqdvvH61Bdo0ls6oMk44/GqjuiZbMgjiN5p0UUJXzImOVJxkHvTNTZVS3twwZoUwxHrxx+l
VXhlT70bD8KiroUdb3OZy0tY6QWHzhGlZrdTlYj6+59KZc8XjY/55r/Nqj2n/npL/wB/W/xoC4JO
WJPdmJP61zHTYdUX/L0PZ/8A2nUtQj/j5P8Av/8AtOkUWM03JJAAJJ6Ad6KhkaaGUTREkYwygZI9
x/hQwLbWEU0RFwMkjsfu/Ss7UNSZHSCMFolA3M3/AC0qx58l0AC4Kd9owDUd1bJcRgHgr0IrNxbV
1ub0pxUkp7EkE0c8e6M/UdxUlUrWyEExcSF+MA4xV2ri21qZ1VGMrQd0NH/Hx/wFf5mmW3c/7Cf+
zU9f+Pr/AIAv82qO3+7/AMAT+tMgsbqaz7Bv/i+6Pxopkqh1Cn1rKqrpLoxxHl2iWMJtG7qSM80j
SyqMl4wP9w/41EJCilJl3L64yKZ5sIOUBduwyWxW6irabHNzfzLUJmMuFd8ZOP8AVkVZqCON2k82
Xgj7q+lT0SNIKy2GS/6s/Vf/AEIUsHEQ/wB5v/QjSTf6o/7y/wDoQoj/ANWPq3/oRqTQkLVlNkOy
E4IOK1Ko30Bz50fJ/iFZVYcy0N8PNRlZ9SlKkYbLzFR6Hk/hVeJRNeJHExKk9SOcVa3JIuHAI96f
GY4RiJACfQc1mqslG3U1nh4uVzVeQSSKM5K5NLUFvFsBduXbr7VPWlGDhCzOao05aDH/ANbH9G/9
lpaa/wDro/8Adb/2WnVsZhRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAU
UUUAFFFFABRRRQAUUUUAUzfWpOdsufUDB/nSrqFsvRZf++R/jRRUFC/2lb+kv/fI/wAacsq3eTHk
BVZfm9Tj/CiimhCtfRAnKvnvgD/Gk+3w+kn5D/GiigBP7Qg9JPyH+NNa+tW+/G7fVB/jRRRdhYX+
0rf0l/75H+NH9pW/pL/3yP8AGiii47B/aVv6S/8AfI/xp6yAg3GPlLbsd8bdtFFCEJ9vh9JPyH+N
J/aEHpJ+Q/xooouAf2jAP4ZP++R/jSf2lb+kv/fI/wAaKKLjsH9pW/pL/wB8j/Gj+0rf0l/75H+N
FFFwsOW8j3+dtbawCqMc8ZPP50okFuil8kFQOPb/APXRRTEJ9vh9JPyH+NIb+Dusn/fI/wAaKKVw
D+0YB/DJ/wB8j/Gk/tK39Jf++R/jRRRcdg/tK39Jf++R/jR/aVv6S/8AfI/xooouFhVu47kiOMPk
kHLADoc/0pzXKQHY4bOSeB6nNFFMQn2+H0k/If40n9oQekn5D/GiilcCJ7myY5aFifXYP8aVLyzj
+5E6/RB/jRRQMf8A2lb+kv8A3yP8aP7St/SX/vkf40UUXCxJFOty4eMEKgI+bqc4/wAKmoopolhR
RRTAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKAP/Z
			</helperscreenshot>
			<matchname>
				Text
			</matchname>
			<matchvalue/>
		</validationitem>
		<mouseitem
		enabled="True"
		optional="False"
		duration="500ms"
		searchpropertyid="c922ce58-5bb6-40c9-9517-c787b97bf575"
		action="Click"
		button="Left"
		movetime="300ms"
		sx="1900"
		sy="168"
		loc="Center">
			<comment/>
			<bindings/>
			<info
			sourcename="Recorder"
			id="d7dd2840-f37d-414d-940b-92b8762946bd">
				<element
				role="unknown"
				prefcap="imgtag"
				capabilities="webelement, imgtag"
				flavor="mozillaweb"
				visible="True"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="True"
				index="0"
				rect="1892,163,16,16"
				snapElem="">
					<attribute
					name="hidden"
					type="System.String">
						False
					</attribute>
					<attribute
					name="tagname"
					type="System.String">
						img
					</attribute>
					<attribute
					name="alt"
					type="System.String">
						Help
					</attribute>
					<attribute
					name="height"
					type="System.String">
						16px
					</attribute>
					<attribute
					name="src"
					type="System.String">
						/solutions/images/shell/help.png
					</attribute>
					<attribute
					name="title"
					type="System.String">
						Help
					</attribute>
					<attribute
					name="width"
					type="System.String">
						16px
					</attribute>
				</element>
				<path>
					/dom[@caption='IDeaS G3']//a[#'pageLevelHelpLink']/img[@title='Help']
				</path>
			</info>
			<helperscreenshot
			guid="3160d6d5-8b7e-4a4f-84fe-ed5439a3c0a4">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************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			</helperscreenshot>
		</mouseitem>
		<validationitem
		enabled="True"
		optional="False"
		duration="100ms"
		searchpropertyid="6fb247b7-9c89-4d89-8b2b-d706d95a37fb"
		action="Exists">
			<comment/>
			<bindings/>
			<info
			sourcename="ElementTree"
			id="7ba7ee60-0a1f-4eed-8c7a-cf7fbad54bf5">
				<path>
					/dom[]//iframe[#'topic']/?/?/h1[@innertext='Rate Shopping Overview']
				</path>
			</info>
			<matchname/>
			<matchvalue/>
		</validationitem>
		<mouseitem
		enabled="True"
		optional="False"
		duration="500ms"
		searchpropertyid="54a52b33-844e-465a-bbee-d51a3c467967"
		action="Click"
		button="Left"
		movetime="300ms"
		sx="421"
		sy="21"
		loc="Center">
			<comment/>
			<bindings/>
			<info
			sourcename="Recorder"
			id="ca447c95-5019-4482-9ba6-548a38ed0abc">
				<element
				role="button"
				prefcap="button"
				capabilities="accessible"
				flavor="msaa"
				visible="True"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="False"
				index="0"
				rect="412,8,16,16"
				snapElem="">
					<attribute
					name="pressed"
					type="System.Boolean">
						False
					</attribute>
					<attribute
					name="accessibledefaultaction"
					type="System.String">
						press
					</attribute>
					<attribute
					name="accessiblerole"
					type="System.Windows.Forms.AccessibleRole, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
						PushButton
					</attribute>
					<attribute
					name="accessiblestate"
					type="System.Windows.Forms.AccessibleStates, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
						None
					</attribute>
				</element>
				<path>
					/form[@title~'^Jaspersoft.*']/toolbar[@accessiblename='Browser tabs']/?/?/tabpage[@accessiblename~'^IDeaS\ -\ Information\ Manag']/button[@accessiblerole='PushButton']
				</path>
			</info>
			<helperscreenshot
			guid="d344afca-6309-4c8b-b4dc-c9c607f33c0f">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************
qqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqr/wAARCAAqAGADASIA
AhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQA
AAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3
ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWm
p6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEA
AwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSEx
BhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElK
U1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3
uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwC79itv
+eCflR9itv8Angn5U/z0DrG24Mw4+U4PGevSkiu4JjiNifl3D5SMj245qyBv2K2/54J+VH2K2/54
J+VO+1Q7C25uG24KHdn0xjNPikSZN8ZyuSM4x0oAq3EVnbxh3t1IJxwKr+fp/wDz7H/vkf41Y1b/
AI9V/wB8fyNSnzpbiSOOYIygFVIHzVnJu51whDkTave/UpGew7W5H/ART4Ps05YLAvHcoKtDzori
OOSYOzAllAHy1BaLunvB338fmaE3dBOEOSTSs15+Y/7NB/zxj/75FH2aH/njH/3yKnAyM013jjx5
jquem44zWxxkX2aH/njH/wB8ij7ND/zxj/75FFtOJY03vGJTnKg/0+lT4oAi+xf6UsxdTtORlPm6
YxnPTvij7EfKjQSkFImj3AeuOf0q7sk/55n8x/jRsk/55n8x/jUlGd9l+zR7ldVbzNw8uAlRxj7o
5p9rFP8AZ8+YYy284KDOScg//W96vbJP+eZ/Mf40bJP+eZ/Mf40AZuqKwtvmfcDICBjGBjpU7LPH
cySRRKxYAKxI+WpLu0kuYgmCuGzng/1qD+zrv/n6l/P/AOyrKSdzrhKPIk3a1+j8hIJXurlZGjCt
HlXPQtjIzj8Dz7GksP8Aj6u/9/8AqasWln5+liMyMkqSSBZR1B3nP1BxyP8A9dR2FtJA8yvlpcje
OPfkHPIPr/XIppaonnThK710/MlUYyPTikZEfG9FbHTIzUrQyE5VCD9Rz+tII5e8R/Mf41rc5bFe
CARRruVC4z8wH9amp3ly/wDPJvzH+NHly/8APJvzH+NAFvd7N/3yaN3s3/fJqWiouVYi3ezf98mj
d7N/3yaloouFiLd7N/3yaN3s3/fJqWii4WMtFuoYzJhwIpXdYlBPmKWJOffB4HrjPs7ToreAFIoo
96DAkROXQ8q2R1yOp9Qa0qzdF5+25/guXjX/AGVHRR7DJwPekMu7vZv++TRu9m/75NS0U7isRbvZ
v++TRu9m/wC+TUtFFwsf/9k=
			</helperscreenshot>
		</mouseitem>
		<mouseitem
		enabled="True"
		optional="False"
		duration="500ms"
		searchpropertyid="dd0b5575-4c31-44d5-95b8-55c8f7d69dae"
		action="Click"
		button="Left"
		movetime="300ms"
		sx="262"
		sy="211"
		loc="74;9">
			<comment/>
			<bindings/>
			<info
			sourcename="Recorder"
			id="099f6452-66ca-4075-9806-f16c7ed68589">
				<element
				role="tabpage"
				prefcap="tabpage"
				capabilities="flexelement"
				flavor="flex"
				visible="True"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="True"
				index="3"
				rect="188,202,153,24"
				snapElem="">
					<attribute
					name="selected"
					type="System.Boolean">
						False
					</attribute>
					<attribute
					name="title"
					type="System.String">
						Rate Shopping Data
					</attribute>
					<attribute
					name="basetype"
					type="System.String">
						mx.controls.tabBarClasses::Tab
					</attribute>
					<attribute
					name="flexuid"
					type="System.String">
						WebRateShoppingConfigPortlet0.ApplicationView38.vs.WebRateShoppingPod4.view.mainContainer.Box19.ButtonScrollingCanvas20.Canvas27.tabBar.Tab430
					</attribute>
					<attribute
					name="fulltype"
					type="System.String">
						mx.controls.tabBarClasses::Tab
					</attribute>
					<attribute
					name="id"
					type="System.String">
						mainContainer_rateShoppingDataView
					</attribute>
					<attribute
					name="type"
					type="System.String">
						Tab
					</attribute>
					<dynamicattribute
					name="alpha"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="autoRepeat"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="baselinePosition"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="blendMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="buttonMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="cacheAsBitmap"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="cachePolicy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="className"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="closePolicy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="contentMouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="contentMouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="currentState"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="doubleClickEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="editableLabel"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="emphasized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="enabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						True
					</dynamicattribute>
					<dynamicattribute
					name="errorString"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitMaxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitMaxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="focusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="height"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="helpBundle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="helpId"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="iconHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="iconWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="includeInLayout"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="initialized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="instanceIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="isDocument"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="isPopUp"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="label"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="labelPlacement"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="maxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="maxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="measuredHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="measuredMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="measuredMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="measuredWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="menuIconField"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="menuLabelField"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="minHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="minWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseFocusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="name"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="needsSoftKeyboard"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="nestLevel"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="numAutomationChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="numChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="percentHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="percentWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="processedDescriptors"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="repeaterIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="reportTheme"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotation"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotationX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotationY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotationZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="scaleX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="scaleY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="scaleZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="showInAutomationHierarchy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="showIndicator"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="showMenu"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="tabChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="tabEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="tabIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="toggle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="toolTip"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="uid"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="updateCompletePendingFlag"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="useHandCursor"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="validationSubField"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="visible"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						True
					</dynamicattribute>
					<dynamicattribute
					name="width"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="x"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="y"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="z"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
				</element>
				<path>
					/dom[@caption='IDeaS G3']//div[#'portlet_content_one']/flexobject[@id='portlet_flash']//container[@id='view']/?/?/element/container[@type='Canvas']/container[@type='Canvas']/?/?/tabpage[@id='mainContainer_rateShoppingDataView']
				</path>
			</info>
			<helperscreenshot
			guid="42dfe5de-07cc-487f-b0f9-fec47267d607">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************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			</helperscreenshot>
		</mouseitem>
		<validationitem
		enabled="True"
		optional="False"
		duration="100ms"
		searchpropertyid="31de4d79-24d8-46cf-a19d-cf0b2b19fe30"
		action="AttributeEqual">
			<comment/>
			<bindings/>
			<info
			sourcename="ElementTree"
			id="c5d32c80-9011-4933-b9c9-d6b40d8b75ec">
				<element
				role="button"
				prefcap="button"
				capabilities="flexelement"
				flavor="flex"
				visible="True"
				valid="True"
				enabled="False"
				hasfocus="False"
				dontcache="True"
				index="1"
				rect="1770,965,57,24"
				snapElem="">
					<attribute
					name="pressed"
					type="System.Boolean">
						False
					</attribute>
					<attribute
					name="text"
					type="System.String">
						Save
					</attribute>
					<attribute
					name="basetype"
					type="System.String">
						mx.controls::Button
					</attribute>
					<attribute
					name="flexuid"
					type="System.String">
						WebRateShoppingConfigPortlet0.ApplicationView38.vs.WebRateShoppingPod4.view.mainContainer.rateShoppingDataView._WebRateShoppingDataView_HBox1.SaveBtn
					</attribute>
					<attribute
					name="fulltype"
					type="System.String">
						mx.controls::Button
					</attribute>
					<attribute
					name="id"
					type="System.String">
						SaveBtn
					</attribute>
					<attribute
					name="type"
					type="System.String">
						Button
					</attribute>
					<dynamicattribute
					name="alpha"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="autoRepeat"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="baselinePosition"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						16
					</dynamicattribute>
					<dynamicattribute
					name="blendMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						normal
					</dynamicattribute>
					<dynamicattribute
					name="buttonMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="cacheAsBitmap"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="cachePolicy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						auto
					</dynamicattribute>
					<dynamicattribute
					name="className"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						Button
					</dynamicattribute>
					<dynamicattribute
					name="contentMouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-778
					</dynamicattribute>
					<dynamicattribute
					name="contentMouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-231
					</dynamicattribute>
					<dynamicattribute
					name="currentState"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="doubleClickEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="emphasized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="enabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						False
					</dynamicattribute>
					<dynamicattribute
					name="errorString"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMaxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMaxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="focusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="height"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="helpBundle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="helpId"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						SaveBtn
					</dynamicattribute>
					<dynamicattribute
					name="iconHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="iconWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="includeInLayout"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="initialized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="instanceIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="isDocument"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="isPopUp"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="label"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						Save
					</dynamicattribute>
					<dynamicattribute
					name="labelPlacement"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						right
					</dynamicattribute>
					<dynamicattribute
					name="maxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						10000
					</dynamicattribute>
					<dynamicattribute
					name="maxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						10000
					</dynamicattribute>
					<dynamicattribute
					name="measuredHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="measuredMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="measuredMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						57
					</dynamicattribute>
					<dynamicattribute
					name="measuredWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						57
					</dynamicattribute>
					<dynamicattribute
					name="minHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="minWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						57
					</dynamicattribute>
					<dynamicattribute
					name="mouseChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="mouseEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseFocusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-778
					</dynamicattribute>
					<dynamicattribute
					name="mouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-231
					</dynamicattribute>
					<dynamicattribute
					name="name"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						SaveBtn
					</dynamicattribute>
					<dynamicattribute
					name="needsSoftKeyboard"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="nestLevel"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						10
					</dynamicattribute>
					<dynamicattribute
					name="numAutomationChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="numChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						2
					</dynamicattribute>
					<dynamicattribute
					name="percentHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="percentWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="processedDescriptors"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="repeaterIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="reportTheme"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotation"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="scaleX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="scaleY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="scaleZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="selected"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="showInAutomationHierarchy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="tabChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="tabEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="tabIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="toggle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="toolTip"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="uid"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						WebRateShoppingConfigPortlet0.ApplicationView38.vs.WebRateShoppingPod4.view.mainContainer.rateShoppingDataView._WebRateShoppingDataView_HBox1.SaveBtn
					</dynamicattribute>
					<dynamicattribute
					name="updateCompletePendingFlag"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="useHandCursor"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="validationSubField"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="visible"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						True
					</dynamicattribute>
					<dynamicattribute
					name="width"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						57
					</dynamicattribute>
					<dynamicattribute
					name="x"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1761
					</dynamicattribute>
					<dynamicattribute
					name="y"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="z"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
				</element>
				<path>
					/dom[@caption='IDeaS G3']//button[@text='Save']
				</path>
			</info>
			<helperscreenshot
			guid="5c0583e3-0c29-4219-89c6-d777a5a03936">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************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			</helperscreenshot>
			<matchname>
				Text
			</matchname>
			<matchvalue>
				Save
			</matchvalue>
		</validationitem>
		<validationitem
		enabled="True"
		optional="False"
		duration="100ms"
		searchpropertyid="a5735ee5-fbe8-4ce6-8438-721b46b5e3fd"
		action="AttributeEqual">
			<comment/>
			<bindings/>
			<info
			sourcename="ElementTree"
			id="ac4204f8-e146-4fa1-a6dd-34b256be1bda">
				<element
				role="text"
				prefcap="text"
				capabilities="flexelement"
				flavor="flex"
				visible="False"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="True"
				index="0"
				rect="12,962,24,20"
				snapElem="">
					<attribute
					name="caption"
					type="System.String">
					</attribute>
					<attribute
					name="selectionlength"
					type="System.Int32">
						0
					</attribute>
					<attribute
					name="selectionstart"
					type="System.Int32">
						0
					</attribute>
					<attribute
					name="selectiontext"
					type="System.String">
					</attribute>
					<attribute
					name="text"
					type="System.String">
					</attribute>
					<attribute
					name="basetype"
					type="System.String">
						mx.controls::Label
					</attribute>
					<attribute
					name="flexuid"
					type="System.String">
						AlertManagerPortlet0.InformationManagerView4.VBox6.StatusBar17.button.statusArea
					</attribute>
					<attribute
					name="fulltype"
					type="System.String">
						mx.controls::Label
					</attribute>
					<attribute
					name="id"
					type="System.String">
						statusArea
					</attribute>
					<attribute
					name="type"
					type="System.String">
						Label
					</attribute>
					<dynamicattribute
					name="alpha"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="baselinePosition"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						14
					</dynamicattribute>
					<dynamicattribute
					name="blendMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						normal
					</dynamicattribute>
					<dynamicattribute
					name="buttonMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="cacheAsBitmap"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="cachePolicy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						auto
					</dynamicattribute>
					<dynamicattribute
					name="className"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						Label
					</dynamicattribute>
					<dynamicattribute
					name="condenseWhite"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="contentMouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1064
					</dynamicattribute>
					<dynamicattribute
					name="contentMouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-284
					</dynamicattribute>
					<dynamicattribute
					name="currentState"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="doubleClickEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="enabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						True
					</dynamicattribute>
					<dynamicattribute
					name="errorString"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMaxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMaxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="focusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="height"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						20
					</dynamicattribute>
					<dynamicattribute
					name="helpBundle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="helpId"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						statusArea
					</dynamicattribute>
					<dynamicattribute
					name="htmlText"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="includeInLayout"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="initialized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="instanceIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="isDocument"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="isPopUp"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="maxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						10000
					</dynamicattribute>
					<dynamicattribute
					name="maxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						10000
					</dynamicattribute>
					<dynamicattribute
					name="measuredHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						20
					</dynamicattribute>
					<dynamicattribute
					name="measuredMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						20
					</dynamicattribute>
					<dynamicattribute
					name="measuredMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="measuredWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="minHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						20
					</dynamicattribute>
					<dynamicattribute
					name="minWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="mouseChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseFocusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1064
					</dynamicattribute>
					<dynamicattribute
					name="mouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-284
					</dynamicattribute>
					<dynamicattribute
					name="name"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						statusArea
					</dynamicattribute>
					<dynamicattribute
					name="needsSoftKeyboard"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="nestLevel"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						7
					</dynamicattribute>
					<dynamicattribute
					name="numAutomationChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="numChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="percentHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="percentWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="processedDescriptors"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="repeaterIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="reportTheme"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotation"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="scaleX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="scaleY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="scaleZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="selectable"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="showInAutomationHierarchy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="tabChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="tabEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="tabIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="textHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="textWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="toolTip"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="uid"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						AlertManagerPortlet0.InformationManagerView4.VBox6.StatusBar17.button.statusArea
					</dynamicattribute>
					<dynamicattribute
					name="updateCompletePendingFlag"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="useHandCursor"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="validationSubField"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="visible"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						False
					</dynamicattribute>
					<dynamicattribute
					name="width"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="x"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						5
					</dynamicattribute>
					<dynamicattribute
					name="y"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="z"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
				</element>
				<path>
					/dom[@caption='IDeaS G3' and @page='information-manager' and @path='/solutions/information-manager' and @browsername='Mozilla']//div[#'portlet_content_one']/flexobject[@id='portlet_flash']/container/container[4]//text[@id='statusArea']
				</path>
			</info>
			<helperscreenshot
			guid="f97d962a-2ce5-4981-8513-3e7196cc6934">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************
qqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqr/wAARCAFmAfQDASIA
AhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQA
AAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3
ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWm
p6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEA
AwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSEx
BhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElK
U1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3
uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwDoKKKK
ACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA
KKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAo
oooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACii
igAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKK
ACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA
KKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAo
oooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACii
igAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKK
ACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA
KKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAo
oooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACii
igAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKK
ACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA
KKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAo
oooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACii
igAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKK
ACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA
KKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAo
oooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACii
igAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKK
ACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA
KKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAo
oooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACii
igAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKK
ACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA
KKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAo
oooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACii
igAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKK
ACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA
KKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAo
oooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACii
igAooooAKKKKACiiigAooooAKKKKACis3+2rb+5L+Q/xo/tq2/uS/kP8annj3I549zSorN/tq2/u
S/kP8aP7atv7kv5D/Gjnj3Dnj3NKis3+2rb+5L+Q/wAaP7atv7kv5D/Gjnj3Dnj3NKis3+2rb+5L
+Q/xo/tq2/uS/kP8aOePcOePc0qKzf7atv7kv5D/ABo/tq2/uS/kP8aOePcOePc0qKzf7atv7kv5
D/Gj+2rb+5L+Q/xo549w549zSorN/tq2/uS/kP8AGj+2rb+5L+Q/xo549w549zSorN/tq2/uS/kP
8aP7atv7kv5D/Gjnj3Dnj3NKis3+2rb+5L+Q/wAaP7atv7kv5D/Gjnj3Dnj3NKis3+2rb+5L+Q/x
o/tq2/uS/kP8aOePcOePc0qKzf7atv7kv5D/ABo/tq2/uS/kP8aOePcOePc0qKzf7atv7kv5D/Gj
+2rb+5L+Q/xo549w549zSorN/tq2/uS/kP8AGj+2rb+5L+Q/xo549w549zSorN/tq2/uS/kP8aP7
atv7kv5D/Gjnj3Dnj3NKis3+2rb+5L+Q/wAaP7atv7kv5D/Gjnj3Dnj3NKis3+2rb+5L+Q/xo/tq
2/uS/kP8aOePcOePc0qKzf7atv7kv5D/ABo/tq2/uS/kP8aOePcOePc0qKzf7atv7kv5D/Gj+2rb
+5L+Q/xo549w549zSorN/tq2/uS/kP8AGj+2rb+5L+Q/xo549w549zSorN/tq2/uS/kP8aP7atv7
kv5D/Gjnj3Dnj3NKis3+2rb+5L+Q/wAaP7atv7kv5D/Gjnj3Dnj3NKis3+2rb+5L+Q/xo/tq2/uS
/kP8aOePcOePc0qKzf7atv7kv5D/ABo/tq2/uS/kP8aOePcOePc0qKzf7atv7kv5D/Gj+2rb+5L+
Q/xo549w549zSorN/tq2/uS/kP8AGj+2rb+5L+Q/xo549w549zSorN/tq2/uS/kP8aP7atv7kv5D
/Gjnj3Dnj3NKis3+2rb+5L+Q/wAaP7atv7kv5D/Gjnj3Dnj3NKis3+2rb+5L+Q/xo/tq2/uS/kP8
aOePcOePc0qKzf7atv7kv5D/ABo/tq2/uS/kP8aOePcOePc0qKzf7atv7kv5D/Gj+2rb+5L+Q/xo
549w549zSorN/tq2/uS/kP8AGj+2rb+5L+Q/xo549w549zSorN/tq2/uS/kP8aKOePcOePc0to/v
N+dG0f3m/OofNo82qLJto/vN+dG0f3m/OofNo82gCbaP7zfnRtH95vzqHzaPNoAm2j+8350bR/eb
86h82jzaAJto/vN+dG0f3m/OofNo82gCbaP7zfnRtH95vzqHzaPNoAm2j+8350bR/eb86h82jzaA
Jto/vN+dG0f3m/OofNo82gCbaP7zfnRtH95vzqHzaPNoAm2j+8350bR/eb86h82jzaAJto/vN+dG
0f3m/OofNo82gCbaP7zfnRtH95vzqHzaPNoAm2j+8350bR/eb86h82jzaAJto/vN+dG0f3m/OofN
o82gCbaP7zfnRtH95vzqHzaPNoAm2j+8350bR/eb86h82jzaAJto/vN+dG0f3m/OofNo82gCbaP7
zfnRtH95vzqHzaPNoAm2j+8350bR/eb86h82jzaAJto/vN+dG0f3m/OofNo82gCbaP7zfnRtH95v
zqHzaPNoAm2j+8350bR/eb86h82jzaAJto/vN+dG0f3m/OofNo82gCbaP7zfnRtH95vzqHzaPNoA
m2j+8350bR/eb86h82jzaAJto/vN+dG0f3m/OofNo82gCbaP7zfnRtH95vzqHzaPNoAm2j+8350b
R/eb86h82jzaAJto/vN+dG0f3m/OofNo82gCbaP7zfnRtH95vzqHzaPNoAm2j+8350bR/eb86h82
jzaAJto/vN+dFQ+bRQBlf2qn/PJvzo/tVP8Ank351D9rk/ur+VH2uT+6v5Vr7GXf+vvOL20u5N/a
qf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+
6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u
5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP
2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vv
D20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eT
fnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GX
f+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tV
P+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/l
R7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvz
o/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P
7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8A
PJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5U
fa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/a
qf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+
6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u
5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP
2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vv
D20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eT
fnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GX
f+vvD20u5N/aqf8APJvzo/tVP+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzo/tV
P+eTfnUP2uT+6v5Ufa5P7q/lR7GXf+vvD20u5N/aqf8APJvzoqH7XJ/dX8qKPYy7/wBfeHtpdyvR
Wh/ZE/8Az0i/M/4Uf2RP/wA9IvzP+Fb88e5Hs59jPorQ/sif/npF+Z/wo/sif/npF+Z/wo549w9n
PsZ9FaH9kT/89IvzP+FH9kT/APPSL8z/AIUc8e4ezn2M+itD+yJ/+ekX5n/Cj+yJ/wDnpF+Z/wAK
OePcPZz7GfRWh/ZE/wDz0i/M/wCFH9kT/wDPSL8z/hRzx7h7OfYz6K0P7In/AOekX5n/AAo/sif/
AJ6Rfmf8KOePcPZz7GfRWh/ZE/8Az0i/M/4Uf2RP/wA9IvzP+FHPHuHs59jPorQ/sif/AJ6Rfmf8
KP7In/56Rfmf8KOePcPZz7GfRWh/ZE//AD0i/M/4Uf2RP/z0i/M/4Uc8e4ezn2M+itD+yJ/+ekX5
n/Cj+yJ/+ekX5n/Cjnj3D2c+xn0Vof2RP/z0i/M/4Uf2RP8A89IvzP8AhRzx7h7OfYz6K0P7In/5
6Rfmf8KP7In/AOekX5n/AAo549w9nPsZ9FaH9kT/APPSL8z/AIUf2RP/AM9IvzP+FHPHuHs59jPo
rQ/sif8A56Rfmf8ACj+yJ/8AnpF+Z/wo549w9nPsZ9FaH9kT/wDPSL8z/hR/ZE//AD0i/M/4Uc8e
4ezn2M+itD+yJ/8AnpF+Z/wo/sif/npF+Z/wo549w9nPsZ9FaH9kT/8APSL8z/hR/ZE//PSL8z/h
Rzx7h7OfYz6K0P7In/56Rfmf8KP7In/56Rfmf8KOePcPZz7GfRWh/ZE//PSL8z/hR/ZE/wDz0i/M
/wCFHPHuHs59jPorQ/sif/npF+Z/wo/sif8A56Rfmf8ACjnj3D2c+xn0Vof2RP8A89IvzP8AhR/Z
E/8Az0i/M/4Uc8e4ezn2M+itD+yJ/wDnpF+Z/wAKP7In/wCekX5n/Cjnj3D2c+xn0Vof2RP/AM9I
vzP+FH9kT/8APSL8z/hRzx7h7OfYz6K0P7In/wCekX5n/Cj+yJ/+ekX5n/Cjnj3D2c+xn0Vof2RP
/wA9IvzP+FH9kT/89IvzP+FHPHuHs59jPorQ/sif/npF+Z/wo/sif/npF+Z/wo549w9nPsZ9FaH9
kT/89IvzP+FH9kT/APPSL8z/AIUc8e4ezn2M+itD+yJ/+ekX5n/Cj+yJ/wDnpF+Z/wAKOePcPZz7
GfRWh/ZE/wDz0i/M/wCFH9kT/wDPSL8z/hRzx7h7OfYz6K0P7In/AOekX5n/AAo/sif/AJ6Rfmf8
KOePcPZz7GfRWh/ZE/8Az0i/M/4Uf2RP/wA9IvzP+FHPHuHs59jPorQ/sif/AJ6Rfmf8KKOePcPZ
z7GzRRRXGegFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAF
FFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUU
UUAFFFFABRRRQAUUUUAFFFFABRVT7en/ADwuv+/Df4Ufb0/54XX/AH4b/CgC3RVT7en/ADwuf+/D
f4Uf2hEM5jnGBnmIjigC3RVQahGRkQXJB7iBv8KPt6f8+91/34b/AAoAt0VU+3p/z73X/fhv8KPt
6f8APC6/78N/hQBboqp9vT/nhdf9+G/wo+3p/wA8Ln/vw3+FAFuiqv29P+eFz/34b/CmnUYQP9XP
1x/qj19KALlFVPt6f8+91/34b/Cj7en/AD73X/fhv8KALdFVPt6f88Lr/vw3+FH29P8Anhdf9+G/
woAt0VU+3p/zwuv+/Df4Uv29P+eFz/34b/CgC1RVT+0I+f3Vxx1/dNxR/aMWQBFOSRnAiJ4oAt0V
U+3p/wA+91/34b/Cj7en/Pvdf9+G/wAKALdFVPt6f88Lr/vw3+FH29P+eF1/34b/AAoAt0VU+3p/
zwuv+/Df4Ufb0/54XP8A34b/AAoAt0VU/tCMYzFcDPrCwoGoRnO2G4bBwdsLGgC3RVT7en/Pvdf9
+G/wo+3p/wA+91/34b/CgC3RVT7en/PC6/78N/hR9vT/AJ4XX/fhv8KALdFVPt6f88Lr/vw3+FH2
9P8Anhc/9+G/woAt0VUGoR5x5U4PoYiKX7cn/PKf/v0aALVFVftyf88p/wDv0aPtyf8APKf/AL9G
gC1RVX7cn/PKf/v0aPtyf88p/wDv0aALVFVftyf88p/+/Ro+3J/zyn/79GgC1RVX7cn/ADyn/wC/
Ro+3J/zyn/79GgC1RVX7cn/PKf8A79Gj7cn/ADyn/wC/RoAtUVV+3J/zyn/79Gj7cn/PKf8A79Gg
C1RVX7cn/PKf/v0aPtyf88p/+/RoAtUVV+3J/wA8p/8Av0aPtyf88p/+/RoAtUVV+3J/zyn/AO/R
o+3J/wA8p/8Av0aALVFVftyf88p/+/Ro+3J/zyn/AO/RoAtUVV+3J/zyn/79Gj7cn/PKf/v0aALV
FVftyf8APKf/AL9Gj7cn/PKf/v0aALVFVftyf88p/wDv0aPtyf8APKf/AL9GgC1RVX7cn/PKf/v0
aKAH5PdSPwppYZx7ZpguR60gOWJ9h/KtHGxJJmqmof6l/wDrlJ/SrGar3vMLf9cn/pQI0ieaZLMs
UZkc4UdaRm+Y/WquonNlJ+H8xSirtIJOybGvq0I+6jt+lV31eQ/ciUfU5qC3tBLEZpZVhizgMRnJ
+lR3dq1rIAWDKwyrDoRXQoQvY5nOdrm8TTSac1qqoXa5lCgZz8uP5VVhkLSOAXKYBQuBkjnnge1Y
JXOhvWxPmq//AC/x/wDXX/2mamzUP/L4h/6af+0zSGX91NL8nG0AdSxwBTd1Ur6yW5+YOVf68VLv
bQ0hyt+89C/vBPUH0IOQaazhQSTgCudVrixmxyDnp2NdNBDhQ8uN2OnZamMr7mlaj7OzTumVY7iO
UkKSGHVSMGpM0y4MU0qNEgyhzvHGeMf5+lGa0RzsQf8AHw3+4v8AM0yw4mk/65R/+zU4f68/7q/z
NMtOJHP/AEzj/wDZqAL26jNR7qZIxbai9WPb0pWG2OM2W2xqXPTjpTZJtg5GW7gc4pksqxYiRlXj
lvT2qHzFjBKzKw7gkc1kr1XppH8wb5VruTJNvG4YxUmaqyjyZg46HhqnzW3JykRnzobPzF/wJf8A
0IU+wOLX/gb/APoZpkx/dn6r/wChCltDi3H++/8A6GaGUWt1RyTpGCWYDFG6suVhJcO/XJrOc1DU
1hBzvYufbd33V49TSx3Qd9pwCelZ87BeSwHA2iq4nKyAr97PHvW9O1SHNY5J88J8rZu7gaM0jEFV
kxt39j9KbmsoS5kbtWGS/wCtj/3W/pRRJ/rE+jf0oq0SFFFFMAooooAKKKKACiiigAooooAKKKKA
CiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAZbaeXgEk8pQEZ49KcrYwQ29GPDYxTpGaZ
ohGCPl2vnpjuCKa8YjCxqxPOcE5wM1HNJvUofUF1yhH/AEzf+lT1Bc9v+ub/APstUItMfmP1qtfn
No/4fzqdvvH61Bdo0ls6oMk44/GqjuiZbMgjiN5p0UUJXzImOVJxkHvTNTZVS3twwZoUwxHrxx+l
VXhlT70bD8KiroUdb3OZy0tY6QWHzhGlZrdTlYj6+59KZc8XjY/55r/Nqj2n/npL/wB/W/xoC4JO
WJPdmJP61zHTYdUX/L0PZ/8A2nUtQj/j5P8Av/8AtOkUWM03JJAAJJ6Ad6KhkaaGUTREkYwygZI9
x/hQwLbWEU0RFwMkjsfu/Ss7UNSZHSCMFolA3M3/AC0qx58l0AC4Kd9owDUd1bJcRgHgr0IrNxbV
1ub0pxUkp7EkE0c8e6M/UdxUlUrWyEExcSF+MA4xV2ri21qZ1VGMrQd0NH/Hx/wFf5mmW3c/7Cf+
zU9f+Pr/AIAv82qO3+7/AMAT+tMgsbqaz7Bv/i+6Pxopkqh1Cn1rKqrpLoxxHl2iWMJtG7qSM80j
SyqMl4wP9w/41EJCilJl3L64yKZ5sIOUBduwyWxW6irabHNzfzLUJmMuFd8ZOP8AVkVZqCON2k82
Xgj7q+lT0SNIKy2GS/6s/Vf/AEIUsHEQ/wB5v/QjSTf6o/7y/wDoQoj/ANWPq3/oRqTQkLVlNkOy
E4IOK1Ko30Bz50fJ/iFZVYcy0N8PNRlZ9SlKkYbLzFR6Hk/hVeJRNeJHExKk9SOcVa3JIuHAI96f
GY4RiJACfQc1mqslG3U1nh4uVzVeQSSKM5K5NLUFvFsBduXbr7VPWlGDhCzOao05aDH/ANbH9G/9
lpaa/wDro/8Adb/2WnVsZhRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAU
UUUAFFFFABRRRQAUUUUAUzfWpOdsufUDB/nSrqFsvRZf++R/jRRUFC/2lb+kv/fI/wAacsq3eTHk
BVZfm9Tj/CiimhCtfRAnKvnvgD/Gk+3w+kn5D/GiigBP7Qg9JPyH+NNa+tW+/G7fVB/jRRRdhYX+
0rf0l/75H+NH9pW/pL/3yP8AGiii47B/aVv6S/8AfI/xp6yAg3GPlLbsd8bdtFFCEJ9vh9JPyH+N
J/aEHpJ+Q/xooouAf2jAP4ZP++R/jSf2lb+kv/fI/wAaKKLjsH9pW/pL/wB8j/Gj+0rf0l/75H+N
FFFwsOW8j3+dtbawCqMc8ZPP50okFuil8kFQOPb/APXRRTEJ9vh9JPyH+NIb+Dusn/fI/wAaKKVw
D+0YB/DJ/wB8j/Gk/tK39Jf++R/jRRRcdg/tK39Jf++R/jR/aVv6S/8AfI/xooouFhVu47kiOMPk
kHLADoc/0pzXKQHY4bOSeB6nNFFMQn2+H0k/If40n9oQekn5D/GiilcCJ7myY5aFifXYP8aVLyzj
+5E6/RB/jRRQMf8A2lb+kv8A3yP8aP7St/SX/vkf40UUXCxJFOty4eMEKgI+bqc4/wAKmoopolhR
RRTAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKAP/Z
			</helperscreenshot>
			<matchname>
				Text
			</matchname>
			<matchvalue/>
		</validationitem>
		<mouseitem
		enabled="True"
		optional="False"
		duration="500ms"
		searchpropertyid="c922ce58-5bb6-40c9-9517-c787b97bf575"
		action="Click"
		button="Left"
		movetime="300ms"
		sx="1900"
		sy="168"
		loc="Center">
			<comment/>
			<bindings/>
			<info
			sourcename="Recorder"
			id="d7dd2840-f37d-414d-940b-92b8762946bd">
				<element
				role="unknown"
				prefcap="imgtag"
				capabilities="webelement, imgtag"
				flavor="mozillaweb"
				visible="True"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="True"
				index="0"
				rect="1892,163,16,16"
				snapElem="">
					<attribute
					name="hidden"
					type="System.String">
						False
					</attribute>
					<attribute
					name="tagname"
					type="System.String">
						img
					</attribute>
					<attribute
					name="alt"
					type="System.String">
						Help
					</attribute>
					<attribute
					name="height"
					type="System.String">
						16px
					</attribute>
					<attribute
					name="src"
					type="System.String">
						/solutions/images/shell/help.png
					</attribute>
					<attribute
					name="title"
					type="System.String">
						Help
					</attribute>
					<attribute
					name="width"
					type="System.String">
						16px
					</attribute>
				</element>
				<path>
					/dom[@caption='IDeaS G3']//a[#'pageLevelHelpLink']/img[@title='Help']
				</path>
			</info>
			<helperscreenshot
			guid="3160d6d5-8b7e-4a4f-84fe-ed5439a3c0a4">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************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			</helperscreenshot>
		</mouseitem>
		<validationitem
		enabled="True"
		optional="False"
		duration="100ms"
		searchpropertyid="6fb247b7-9c89-4d89-8b2b-d706d95a37fb"
		action="Exists">
			<comment/>
			<bindings/>
			<info
			sourcename="ElementTree"
			id="542347ce-bf0a-4e30-8710-194e4cda1584">
				<path>
					/dom[]//iframe[#'topic']/?/?/h1[@innertext='Rate Shopping Data']
				</path>
			</info>
			<matchname/>
			<matchvalue/>
		</validationitem>
		<mouseitem
		enabled="True"
		optional="False"
		duration="500ms"
		searchpropertyid="54a52b33-844e-465a-bbee-d51a3c467967"
		action="Click"
		button="Left"
		movetime="300ms"
		sx="421"
		sy="21"
		loc="Center">
			<comment/>
			<bindings/>
			<info
			sourcename="Recorder"
			id="ca447c95-5019-4482-9ba6-548a38ed0abc">
				<element
				role="button"
				prefcap="button"
				capabilities="accessible"
				flavor="msaa"
				visible="True"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="False"
				index="0"
				rect="412,8,16,16"
				snapElem="">
					<attribute
					name="pressed"
					type="System.Boolean">
						False
					</attribute>
					<attribute
					name="accessibledefaultaction"
					type="System.String">
						press
					</attribute>
					<attribute
					name="accessiblerole"
					type="System.Windows.Forms.AccessibleRole, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
						PushButton
					</attribute>
					<attribute
					name="accessiblestate"
					type="System.Windows.Forms.AccessibleStates, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
						None
					</attribute>
				</element>
				<path>
					/form[@title~'^Jaspersoft.*']/toolbar[@accessiblename='Browser tabs']/?/?/tabpage[@accessiblename~'^IDeaS\ -\ Information\ Manag']/button[@accessiblerole='PushButton']
				</path>
			</info>
			<helperscreenshot
			guid="d344afca-6309-4c8b-b4dc-c9c607f33c0f">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************
qqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqr/wAARCAAqAGADASIA
AhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQA
AAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3
ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWm
p6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEA
AwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSEx
BhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElK
U1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3
uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwC79itv
+eCflR9itv8Angn5U/z0DrG24Mw4+U4PGevSkiu4JjiNifl3D5SMj245qyBv2K2/54J+VH2K2/54
J+VO+1Q7C25uG24KHdn0xjNPikSZN8ZyuSM4x0oAq3EVnbxh3t1IJxwKr+fp/wDz7H/vkf41Y1b/
AI9V/wB8fyNSnzpbiSOOYIygFVIHzVnJu51whDkTave/UpGew7W5H/ART4Ps05YLAvHcoKtDzori
OOSYOzAllAHy1BaLunvB338fmaE3dBOEOSTSs15+Y/7NB/zxj/75FH2aH/njH/3yKnAyM013jjx5
jquem44zWxxkX2aH/njH/wB8ij7ND/zxj/75FFtOJY03vGJTnKg/0+lT4oAi+xf6UsxdTtORlPm6
YxnPTvij7EfKjQSkFImj3AeuOf0q7sk/55n8x/jRsk/55n8x/jUlGd9l+zR7ldVbzNw8uAlRxj7o
5p9rFP8AZ8+YYy284KDOScg//W96vbJP+eZ/Mf40bJP+eZ/Mf40AZuqKwtvmfcDICBjGBjpU7LPH
cySRRKxYAKxI+WpLu0kuYgmCuGzng/1qD+zrv/n6l/P/AOyrKSdzrhKPIk3a1+j8hIJXurlZGjCt
HlXPQtjIzj8Dz7GksP8Aj6u/9/8AqasWln5+liMyMkqSSBZR1B3nP1BxyP8A9dR2FtJA8yvlpcje
OPfkHPIPr/XIppaonnThK710/MlUYyPTikZEfG9FbHTIzUrQyE5VCD9Rz+tII5e8R/Mf41rc5bFe
CARRruVC4z8wH9amp3ly/wDPJvzH+NHly/8APJvzH+NAFvd7N/3yaN3s3/fJqWiouVYi3ezf98mj
d7N/3yaloouFiLd7N/3yaN3s3/fJqWii4WMtFuoYzJhwIpXdYlBPmKWJOffB4HrjPs7ToreAFIoo
96DAkROXQ8q2R1yOp9Qa0qzdF5+25/guXjX/AGVHRR7DJwPekMu7vZv++TRu9m/75NS0U7isRbvZ
v++TRu9m/wC+TUtFFwsf/9k=
			</helperscreenshot>
		</mouseitem>
		<mouseitem
		enabled="True"
		optional="False"
		duration="500ms"
		searchpropertyid="2329066a-00d0-4cf2-a5d3-addcae41ce1e"
		action="Click"
		button="Left"
		movetime="300ms"
		sx="478"
		sy="215"
		loc="134;13">
			<comment/>
			<bindings/>
			<info
			sourcename="Recorder"
			id="36d2e0fc-8df4-48b4-8c8e-e5a0a80b69e0">
				<element
				role="tabpage"
				prefcap="tabpage"
				capabilities="flexelement"
				flavor="flex"
				visible="True"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="True"
				index="2"
				rect="344,202,180,24"
				snapElem="">
					<attribute
					name="selected"
					type="System.Boolean">
						False
					</attribute>
					<attribute
					name="title"
					type="System.String">
						Rate Shopping Schedule
					</attribute>
					<attribute
					name="basetype"
					type="System.String">
						mx.controls.tabBarClasses::Tab
					</attribute>
					<attribute
					name="flexuid"
					type="System.String">
						WebRateShoppingConfigPortlet0.ApplicationView38.vs.WebRateShoppingPod4.view.mainContainer.Box19.ButtonScrollingCanvas20.Canvas27.tabBar.Tab436
					</attribute>
					<attribute
					name="fulltype"
					type="System.String">
						mx.controls.tabBarClasses::Tab
					</attribute>
					<attribute
					name="id"
					type="System.String">
						mainContainer_rateShoppingScheduleView
					</attribute>
					<attribute
					name="type"
					type="System.String">
						Tab
					</attribute>
					<dynamicattribute
					name="alpha"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="autoRepeat"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="baselinePosition"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="blendMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="buttonMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="cacheAsBitmap"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="cachePolicy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="className"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="closePolicy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="contentMouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="contentMouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="currentState"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="doubleClickEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="editableLabel"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="emphasized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="enabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						True
					</dynamicattribute>
					<dynamicattribute
					name="errorString"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitMaxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitMaxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="focusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="height"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="helpBundle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="helpId"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="iconHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="iconWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="includeInLayout"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="initialized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="instanceIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="isDocument"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="isPopUp"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="label"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="labelPlacement"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="maxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="maxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="measuredHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="measuredMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="measuredMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="measuredWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="menuIconField"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="menuLabelField"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="minHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="minWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseFocusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="name"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="needsSoftKeyboard"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="nestLevel"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="numAutomationChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="numChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="percentHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="percentWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="processedDescriptors"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="repeaterIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="reportTheme"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotation"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotationX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotationY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotationZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="scaleX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="scaleY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="scaleZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="showInAutomationHierarchy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="showIndicator"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="showMenu"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="tabChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="tabEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="tabIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="toggle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="toolTip"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="uid"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="updateCompletePendingFlag"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="useHandCursor"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="validationSubField"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="visible"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						True
					</dynamicattribute>
					<dynamicattribute
					name="width"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="x"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="y"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="z"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
				</element>
				<path>
					/dom[@caption='IDeaS G3']//div[#'portlet_content_one']/flexobject[@id='portlet_flash']//container[@id='view']/?/?/element/container[@type='Canvas']/container[@type='Canvas']/?/?/tabpage[@id='mainContainer_rateShoppingScheduleView']
				</path>
			</info>
			<helperscreenshot
			guid="c431374c-b2dc-488e-b6ec-8fc1dc9462e9">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************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==
			</helperscreenshot>
		</mouseitem>
		<validationitem
		enabled="True"
		optional="False"
		duration="100ms"
		searchpropertyid="b8ade44e-c411-4e07-a111-8f5ee2a38f6c"
		action="Exists">
			<comment/>
			<bindings/>
			<info
			sourcename="ElementTree"
			id="a5a09215-035b-4a83-a876-48e111f69030">
				<element
				role="text"
				prefcap="text"
				capabilities="flexelement"
				flavor="flex"
				visible="True"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="True"
				index="19"
				rect="35,286,86,20"
				snapElem="">
					<attribute
					name="caption"
					type="System.String">
						For the next&#32;
					</attribute>
					<attribute
					name="selectionlength"
					type="System.Int32">
						0
					</attribute>
					<attribute
					name="selectionstart"
					type="System.Int32">
						0
					</attribute>
					<attribute
					name="selectiontext"
					type="System.String">
					</attribute>
					<attribute
					name="text"
					type="System.String">
						For the next&#32;
					</attribute>
					<attribute
					name="basetype"
					type="System.String">
						mx.controls::Label
					</attribute>
					<attribute
					name="flexuid"
					type="System.String">
						WebRateShoppingConfigPortlet0.ApplicationView38.vs.WebRateShoppingPod4.view.mainContainer.rateShoppingScheduleView.VBox1656.stanlessRowContainer.ListBaseContentHolder1660.RateShoppingStalenessRow2412._RateShoppingStalenessRow_HBox2.Label2415
					</attribute>
					<attribute
					name="fulltype"
					type="System.String">
						mx.controls::Label
					</attribute>
					<attribute
					name="type"
					type="System.String">
						Label
					</attribute>
					<dynamicattribute
					name="alpha"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="baselinePosition"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						14
					</dynamicattribute>
					<dynamicattribute
					name="blendMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						normal
					</dynamicattribute>
					<dynamicattribute
					name="buttonMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="cacheAsBitmap"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="cachePolicy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						auto
					</dynamicattribute>
					<dynamicattribute
					name="className"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						Label
					</dynamicattribute>
					<dynamicattribute
					name="condenseWhite"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="contentMouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						798
					</dynamicattribute>
					<dynamicattribute
					name="contentMouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						249
					</dynamicattribute>
					<dynamicattribute
					name="currentState"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="doubleClickEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="enabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						True
					</dynamicattribute>
					<dynamicattribute
					name="errorString"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMaxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMaxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="focusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="height"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						20
					</dynamicattribute>
					<dynamicattribute
					name="helpBundle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="helpId"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="htmlText"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="includeInLayout"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="initialized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="instanceIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="isDocument"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="isPopUp"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="maxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						10000
					</dynamicattribute>
					<dynamicattribute
					name="maxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						10000
					</dynamicattribute>
					<dynamicattribute
					name="measuredHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						20
					</dynamicattribute>
					<dynamicattribute
					name="measuredMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						20
					</dynamicattribute>
					<dynamicattribute
					name="measuredMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						86
					</dynamicattribute>
					<dynamicattribute
					name="measuredWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						86
					</dynamicattribute>
					<dynamicattribute
					name="minHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						20
					</dynamicattribute>
					<dynamicattribute
					name="minWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						86
					</dynamicattribute>
					<dynamicattribute
					name="mouseChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseFocusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						798
					</dynamicattribute>
					<dynamicattribute
					name="mouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						249
					</dynamicattribute>
					<dynamicattribute
					name="name"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						Label2415
					</dynamicattribute>
					<dynamicattribute
					name="needsSoftKeyboard"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="nestLevel"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						14
					</dynamicattribute>
					<dynamicattribute
					name="numAutomationChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="numChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="percentHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="percentWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="processedDescriptors"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="repeaterIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="reportTheme"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotation"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="scaleX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="scaleY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="scaleZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="selectable"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="showInAutomationHierarchy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="tabChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="tabEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="tabIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="textHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						16
					</dynamicattribute>
					<dynamicattribute
					name="textWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						81
					</dynamicattribute>
					<dynamicattribute
					name="toolTip"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="uid"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						WebRateShoppingConfigPortlet0.ApplicationView38.vs.WebRateShoppingPod4.view.mainContainer.rateShoppingScheduleView.VBox1656.stanlessRowContainer.ListBaseContentHolder1660.RateShoppingStalenessRow2412._RateShoppingStalenessRow_HBox2.Label2415
					</dynamicattribute>
					<dynamicattribute
					name="updateCompletePendingFlag"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="useHandCursor"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="validationSubField"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="visible"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						True
					</dynamicattribute>
					<dynamicattribute
					name="width"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						86
					</dynamicattribute>
					<dynamicattribute
					name="x"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						10
					</dynamicattribute>
					<dynamicattribute
					name="y"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						9
					</dynamicattribute>
					<dynamicattribute
					name="z"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
				</element>
				<path>
					/dom[@caption='IDeaS G3']//div[#'portlet_content_one']/flexobject[@id='portlet_flash']//container[@id='rateShoppingScheduleView']/?/?/list[@id='stanlessRowContainer']/listitem[@text~'^\[object\ WebrateShoppingCo']/container[@id='_RateShoppingStalenessRow_HBox2']/text[@caption='For the next ']
				</path>
			</info>
			<helperscreenshot
			guid="8c1838be-7838-4820-b52f-9ceea62c5180">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************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			</helperscreenshot>
			<matchname>
				Text
			</matchname>
			<matchvalue>
				For the next&#32;
			</matchvalue>
		</validationitem>
		<validationitem
		enabled="True"
		optional="False"
		duration="100ms"
		searchpropertyid="a5735ee5-fbe8-4ce6-8438-721b46b5e3fd"
		action="AttributeEqual">
			<comment/>
			<bindings/>
			<info
			sourcename="ElementTree"
			id="ac4204f8-e146-4fa1-a6dd-34b256be1bda">
				<element
				role="text"
				prefcap="text"
				capabilities="flexelement"
				flavor="flex"
				visible="False"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="True"
				index="0"
				rect="12,962,24,20"
				snapElem="">
					<attribute
					name="caption"
					type="System.String">
					</attribute>
					<attribute
					name="selectionlength"
					type="System.Int32">
						0
					</attribute>
					<attribute
					name="selectionstart"
					type="System.Int32">
						0
					</attribute>
					<attribute
					name="selectiontext"
					type="System.String">
					</attribute>
					<attribute
					name="text"
					type="System.String">
					</attribute>
					<attribute
					name="basetype"
					type="System.String">
						mx.controls::Label
					</attribute>
					<attribute
					name="flexuid"
					type="System.String">
						AlertManagerPortlet0.InformationManagerView4.VBox6.StatusBar17.button.statusArea
					</attribute>
					<attribute
					name="fulltype"
					type="System.String">
						mx.controls::Label
					</attribute>
					<attribute
					name="id"
					type="System.String">
						statusArea
					</attribute>
					<attribute
					name="type"
					type="System.String">
						Label
					</attribute>
					<dynamicattribute
					name="alpha"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="baselinePosition"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						14
					</dynamicattribute>
					<dynamicattribute
					name="blendMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						normal
					</dynamicattribute>
					<dynamicattribute
					name="buttonMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="cacheAsBitmap"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="cachePolicy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						auto
					</dynamicattribute>
					<dynamicattribute
					name="className"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						Label
					</dynamicattribute>
					<dynamicattribute
					name="condenseWhite"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="contentMouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1064
					</dynamicattribute>
					<dynamicattribute
					name="contentMouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-284
					</dynamicattribute>
					<dynamicattribute
					name="currentState"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="doubleClickEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="enabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						True
					</dynamicattribute>
					<dynamicattribute
					name="errorString"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMaxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMaxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="focusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="height"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						20
					</dynamicattribute>
					<dynamicattribute
					name="helpBundle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="helpId"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						statusArea
					</dynamicattribute>
					<dynamicattribute
					name="htmlText"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="includeInLayout"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="initialized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="instanceIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="isDocument"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="isPopUp"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="maxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						10000
					</dynamicattribute>
					<dynamicattribute
					name="maxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						10000
					</dynamicattribute>
					<dynamicattribute
					name="measuredHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						20
					</dynamicattribute>
					<dynamicattribute
					name="measuredMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						20
					</dynamicattribute>
					<dynamicattribute
					name="measuredMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="measuredWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="minHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						20
					</dynamicattribute>
					<dynamicattribute
					name="minWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="mouseChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseFocusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1064
					</dynamicattribute>
					<dynamicattribute
					name="mouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-284
					</dynamicattribute>
					<dynamicattribute
					name="name"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						statusArea
					</dynamicattribute>
					<dynamicattribute
					name="needsSoftKeyboard"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="nestLevel"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						7
					</dynamicattribute>
					<dynamicattribute
					name="numAutomationChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="numChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="percentHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="percentWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="processedDescriptors"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="repeaterIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="reportTheme"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotation"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="scaleX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="scaleY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="scaleZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="selectable"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="showInAutomationHierarchy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="tabChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="tabEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="tabIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="textHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="textWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="toolTip"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="uid"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						AlertManagerPortlet0.InformationManagerView4.VBox6.StatusBar17.button.statusArea
					</dynamicattribute>
					<dynamicattribute
					name="updateCompletePendingFlag"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="useHandCursor"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="validationSubField"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="visible"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						False
					</dynamicattribute>
					<dynamicattribute
					name="width"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="x"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						5
					</dynamicattribute>
					<dynamicattribute
					name="y"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="z"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
				</element>
				<path>
					/dom[@caption='IDeaS G3' and @page='information-manager' and @path='/solutions/information-manager' and @browsername='Mozilla']//div[#'portlet_content_one']/flexobject[@id='portlet_flash']/container/container[4]//text[@id='statusArea']
				</path>
			</info>
			<helperscreenshot
			guid="f97d962a-2ce5-4981-8513-3e7196cc6934">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************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			</helperscreenshot>
			<matchname>
				Text
			</matchname>
			<matchvalue/>
		</validationitem>
		<mouseitem
		enabled="True"
		optional="False"
		duration="500ms"
		searchpropertyid="c922ce58-5bb6-40c9-9517-c787b97bf575"
		action="Click"
		button="Left"
		movetime="300ms"
		sx="1900"
		sy="168"
		loc="Center">
			<comment/>
			<bindings/>
			<info
			sourcename="Recorder"
			id="d7dd2840-f37d-414d-940b-92b8762946bd">
				<element
				role="unknown"
				prefcap="imgtag"
				capabilities="webelement, imgtag"
				flavor="mozillaweb"
				visible="True"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="True"
				index="0"
				rect="1892,163,16,16"
				snapElem="">
					<attribute
					name="hidden"
					type="System.String">
						False
					</attribute>
					<attribute
					name="tagname"
					type="System.String">
						img
					</attribute>
					<attribute
					name="alt"
					type="System.String">
						Help
					</attribute>
					<attribute
					name="height"
					type="System.String">
						16px
					</attribute>
					<attribute
					name="src"
					type="System.String">
						/solutions/images/shell/help.png
					</attribute>
					<attribute
					name="title"
					type="System.String">
						Help
					</attribute>
					<attribute
					name="width"
					type="System.String">
						16px
					</attribute>
				</element>
				<path>
					/dom[@caption='IDeaS G3']//a[#'pageLevelHelpLink']/img[@title='Help']
				</path>
			</info>
			<helperscreenshot
			guid="3160d6d5-8b7e-4a4f-84fe-ed5439a3c0a4">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************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			</helperscreenshot>
		</mouseitem>
		<validationitem
		enabled="True"
		optional="False"
		duration="100ms"
		searchpropertyid="6fb247b7-9c89-4d89-8b2b-d706d95a37fb"
		action="Exists">
			<comment/>
			<bindings/>
			<info
			sourcename="ElementTree"
			id="283aa263-3367-459a-ac69-03949f9ecf0b">
				<path>
					/dom[]//iframe[#'topic']/?/?/h1[@innertext='Rate Shopping Schedule']
				</path>
			</info>
			<matchname/>
			<matchvalue/>
		</validationitem>
		<mouseitem
		enabled="True"
		optional="False"
		duration="500ms"
		searchpropertyid="54a52b33-844e-465a-bbee-d51a3c467967"
		action="Click"
		button="Left"
		movetime="300ms"
		sx="421"
		sy="21"
		loc="Center">
			<comment/>
			<bindings/>
			<info
			sourcename="Recorder"
			id="ca447c95-5019-4482-9ba6-548a38ed0abc">
				<element
				role="button"
				prefcap="button"
				capabilities="accessible"
				flavor="msaa"
				visible="True"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="False"
				index="0"
				rect="412,8,16,16"
				snapElem="">
					<attribute
					name="pressed"
					type="System.Boolean">
						False
					</attribute>
					<attribute
					name="accessibledefaultaction"
					type="System.String">
						press
					</attribute>
					<attribute
					name="accessiblerole"
					type="System.Windows.Forms.AccessibleRole, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
						PushButton
					</attribute>
					<attribute
					name="accessiblestate"
					type="System.Windows.Forms.AccessibleStates, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
						None
					</attribute>
				</element>
				<path>
					/form[@title~'^Jaspersoft.*']/toolbar[@accessiblename='Browser tabs']/?/?/tabpage[@accessiblename~'^IDeaS\ -\ Information\ Manag']/button[@accessiblerole='PushButton']
				</path>
			</info>
			<helperscreenshot
			guid="d344afca-6309-4c8b-b4dc-c9c607f33c0f">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************
qqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqr/wAARCAAqAGADASIA
AhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQA
AAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3
ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWm
p6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEA
AwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSEx
BhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElK
U1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3
uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwC79itv
+eCflR9itv8Angn5U/z0DrG24Mw4+U4PGevSkiu4JjiNifl3D5SMj245qyBv2K2/54J+VH2K2/54
J+VO+1Q7C25uG24KHdn0xjNPikSZN8ZyuSM4x0oAq3EVnbxh3t1IJxwKr+fp/wDz7H/vkf41Y1b/
AI9V/wB8fyNSnzpbiSOOYIygFVIHzVnJu51whDkTave/UpGew7W5H/ART4Ps05YLAvHcoKtDzori
OOSYOzAllAHy1BaLunvB338fmaE3dBOEOSTSs15+Y/7NB/zxj/75FH2aH/njH/3yKnAyM013jjx5
jquem44zWxxkX2aH/njH/wB8ij7ND/zxj/75FFtOJY03vGJTnKg/0+lT4oAi+xf6UsxdTtORlPm6
YxnPTvij7EfKjQSkFImj3AeuOf0q7sk/55n8x/jRsk/55n8x/jUlGd9l+zR7ldVbzNw8uAlRxj7o
5p9rFP8AZ8+YYy284KDOScg//W96vbJP+eZ/Mf40bJP+eZ/Mf40AZuqKwtvmfcDICBjGBjpU7LPH
cySRRKxYAKxI+WpLu0kuYgmCuGzng/1qD+zrv/n6l/P/AOyrKSdzrhKPIk3a1+j8hIJXurlZGjCt
HlXPQtjIzj8Dz7GksP8Aj6u/9/8AqasWln5+liMyMkqSSBZR1B3nP1BxyP8A9dR2FtJA8yvlpcje
OPfkHPIPr/XIppaonnThK710/MlUYyPTikZEfG9FbHTIzUrQyE5VCD9Rz+tII5e8R/Mf41rc5bFe
CARRruVC4z8wH9amp3ly/wDPJvzH+NHly/8APJvzH+NAFvd7N/3yaN3s3/fJqWiouVYi3ezf98mj
d7N/3yaloouFiLd7N/3yaN3s3/fJqWii4WMtFuoYzJhwIpXdYlBPmKWJOffB4HrjPs7ToreAFIoo
96DAkROXQ8q2R1yOp9Qa0qzdF5+25/guXjX/AGVHRR7DJwPekMu7vZv++TRu9m/75NS0U7isRbvZ
v++TRu9m/wC+TUtFFwsf/9k=
			</helperscreenshot>
		</mouseitem>
		<mouseitem
		enabled="True"
		optional="False"
		duration="500ms"
		searchpropertyid="1a368b7e-94e3-487a-a9a6-b8859a735cd6"
		action="Click"
		button="Left"
		movetime="300ms"
		sx="693"
		sy="218"
		loc="163;16">
			<comment/>
			<bindings/>
			<info
			sourcename="Recorder"
			id="eec469bb-1b66-485c-b4b4-b9f1ee385d16">
				<element
				role="tabpage"
				prefcap="tabpage"
				capabilities="flexelement"
				flavor="flex"
				visible="True"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="True"
				index="1"
				rect="530,202,272,24"
				snapElem="">
					<attribute
					name="selected"
					type="System.Boolean">
						False
					</attribute>
					<attribute
					name="title"
					type="System.String">
						Competitive Market Reference Channel
					</attribute>
					<attribute
					name="basetype"
					type="System.String">
						mx.controls.tabBarClasses::Tab
					</attribute>
					<attribute
					name="flexuid"
					type="System.String">
						WebRateShoppingConfigPortlet0.ApplicationView38.vs.WebRateShoppingPod4.view.mainContainer.Box19.ButtonScrollingCanvas20.Canvas27.tabBar.Tab442
					</attribute>
					<attribute
					name="fulltype"
					type="System.String">
						mx.controls.tabBarClasses::Tab
					</attribute>
					<attribute
					name="id"
					type="System.String">
						mainContainer_competitiveMarketReferenceChannelView
					</attribute>
					<attribute
					name="type"
					type="System.String">
						Tab
					</attribute>
					<dynamicattribute
					name="alpha"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="autoRepeat"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="baselinePosition"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="blendMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="buttonMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="cacheAsBitmap"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="cachePolicy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="className"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="closePolicy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="contentMouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="contentMouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="currentState"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="doubleClickEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="editableLabel"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="emphasized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="enabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						True
					</dynamicattribute>
					<dynamicattribute
					name="errorString"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitMaxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitMaxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="focusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="height"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="helpBundle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="helpId"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="iconHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="iconWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="includeInLayout"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="initialized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="instanceIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="isDocument"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="isPopUp"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="label"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="labelPlacement"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="maxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="maxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="measuredHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="measuredMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="measuredMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="measuredWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="menuIconField"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="menuLabelField"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="minHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="minWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseFocusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="name"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="needsSoftKeyboard"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="nestLevel"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="numAutomationChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="numChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="percentHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="percentWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="processedDescriptors"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="repeaterIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="reportTheme"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotation"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotationX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotationY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotationZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="scaleX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="scaleY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="scaleZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="showInAutomationHierarchy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="showIndicator"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="showMenu"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="tabChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="tabEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="tabIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="toggle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="toolTip"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="uid"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="updateCompletePendingFlag"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="useHandCursor"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="validationSubField"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="visible"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						True
					</dynamicattribute>
					<dynamicattribute
					name="width"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="x"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="y"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="z"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
				</element>
				<path>
					/dom[@caption='IDeaS G3']//div[#'portlet_content_one']/flexobject[@id='portlet_flash']//container[@id='view']/?/?/element/container[@type='Canvas']/container[@type='Canvas']/?/?/tabpage[@id='mainContainer_competitiveMarketReferenceChannelView']
				</path>
			</info>
			<helperscreenshot
			guid="2b802954-163e-4844-b482-52cb953d9da9">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************
qqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqr/wAARCABgAeADASIA
AhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQA
AAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3
ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWm
p6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEA
AwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSEx
BhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElK
U1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3
uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwDZ+wR/
89JPzH+FH2CP/npJ+Y/wqbdRuqueXcj2cexD9gj/AOekn5j/AAo+wR/89JPzH+FTbqN1HPLuHs49
iH7BH/z0k/Mf4UfYI/8AnpJ+Y/wqbdRuo55dw9nHsQ/YI/8AnpJ+Y/wo+wR/89JPzH+FTbqN1HPL
uHs49iH7BH/z0k/Mf4UfYI/+ekn5j/Cpt1G6jnl3D2cexD9gj/56SfmP8KPsEf8Az0k/Mf4VNuo3
Uc8u4ezj2IfsEf8Az0k/Mf4UfYI/+ekn5j/Cpt1G6jnl3D2cexD9gj/56SfmP8KPsEf/AD0k/Mf4
VNuo3Uc8u4ezj2IfsEf/AD0k/Mf4UfYI/wDnpJ+Y/wAKm3UbqOeXcPZx7EP2CP8A56SfmP8ACj7B
H/z0k/Mf4VNuo3Uc8u4ezj2IfsEf/PST8x/hR9gj/wCekn5j/Cpt1G6jnl3D2cexD9gj/wCekn5j
/Cj7BH/z0k/Mf4VNuo3Uc8u4ezj2IfsEf/PST8x/hR9gj/56SfmP8Km3UbqOeXcPZx7EP2CP/npJ
+Y/wo+wR/wDPST8x/hU26jdRzy7h7OPYh+wR/wDPST8x/hR9gj/56SfmP8Km3UbqOeXcPZx7EP2C
P/npJ+Y/wo+wR/8APST8x/hU26jdRzy7h7OPYh+wR/8APST8x/hR9gj/AOekn5j/AAqbdRuo55dw
9nHsQ/YI/wDnpJ+Y/wAKPsEf/PST8x/hU26jdRzy7h7OPYh+wR/89JPzH+FH2CP/AJ6SfmP8Km3U
bqOeXcPZx7EP2CP/AJ6SfmP8KPsEf/PST8x/hU26jdRzy7h7OPYh+wR/89JPzH+FH2CP/npJ+Y/w
qbdRuo55dw9nHsQ/YI/+ekn5j/Cj7BH/AM9JPzH+FTbqN1HPLuHs49iH7BH/AM9JPzH+FH2CP/np
J+Y/wqbdRuo55dw9nHsQ/YI/+ekn5j/Cj7BH/wA9JPzH+FTbqN1HPLuHs49iH7BH/wA9JPzH+FH2
CP8A56SfmP8ACpt1G6jnl3D2cexD9gj/AOekn5j/AAo+wR/89JPzH+FTbqN1HPLuHs49iH7BH/z0
k/Mf4UfYI/8AnpJ+Y/wqbdRuo55dw9nHsQ/YI/8AnpJ+Y/wo+wR/89JPzH+FTbqN1HPLuHs49iH7
BH/z0k/Mf4UfYI/+ekn5j/Cpt1G6jnl3D2cexD9gj/56SfmP8KPsEf8Az0k/Mf4VNuo3Uc8u4ezj
2Is0Zp3ln2o8s+1SWNzRmneWfajyz7UANzRmneWfajyz7UANzRmneWfajyz7UANzRmneWfajyz7U
ANzRmneWfajyz7UANzRmneWfajyz7UANzRmneWfajyz7UANzRmneWfajyz7UANzRmneWfajyz7UA
NzRmneWfajyz7UANzRmneWfajyz7UANzRmneWfajyz7UANzRmneWfajyz7UANzRmneWfajyz7UAN
zRmneWfajyz7UANzRmneWfajyz7UANzRmneWfajyz7UANzRmneWfajyz7UANzRmneWfajyz7UANz
RmneWfajyz7UANzRmneWfajyz7UANzRmneWfajyz7UANzRmneWfajyz7UANzRmneWfajyz7UANzR
mneWfajyz7UANzRmneWfajyz7UANzRmneWfajyz7UANzRmneWfajyz7UANzRmneWfajyz7UAO/ef
3V/76/8ArUfvP7q/99f/AFqotqCi53iVzD9n83ywg5GfXOc+1SR6rA+dyyRgReaCwHK/nQBa/ef3
V/76/wDrUfvP7q/99f8A1qr2+oxzzrEIpUZo/MG4DlfwNRXuom3vEgLJCpXd5kilgTnpgY/OgC7+
8/ur/wB9f/Wo/ef3V/76/wDrVWF8EWJXKzyyk7BB0YDvyePzqvJqUkeoEMkvkiAuY9oyCDyfyHrQ
Bo/vP7q/99f/AFqP3n91f++v/rVWfUoQF2JJKTH5pCAcL6nJ/SorrVE8hxbb3kMJkDKBhR6nNAF7
95/dX/vr/wCtR+8/ur/31/8AWqlBqSiFBMHaQW/nMQBgjv8AjTRqGb5SXK2xtfNIIHHPX8qAL/7z
+6v/AH1/9aj95/cH4NUEF/HPKseySNnTeu8D5h+Bq1mgDO1iV0s0aJ2Q+YASpwehqP7BdH7t/KR6
kkf1o1r/AI9f+2w/9Bq4kmEX6VFrydzp53ClHl8yn/Z95/z/AEn5n/Gj+z7z/n+k/M/41ZuLho4s
pjcWCjPTJOKbvaBTLJcPIqqSwKr+mAKfIifrFT+kiD+z7z/n+k/M/wCNH9n3n/P9J+Z/xqRr541Y
yxBSIy6gPnOOo6fSka/kTfuhX5FDnD9vy68UciF9Yqf0kM/s+8/5/pPzP+NH9n3n/P8ASfmf8ake
+dTKRECkR+Y7uSMA8DHvSTaisUjqNh2DLbnwT34HejkQfWKnf8EM/s+8/wCf6T8z/jR/Z95/z/Sf
mf8AGlN3Li6LKrRoTjDkHG0HjA/rSfa5dtyXUNGmcYcggbQew/rRyIPrFT+kg/s+8/5/pPzP+NH9
n3n/AD/Sfmf8anN5jcu35wwULnrnv/P8qjOpKJSvybQ+w5f5s5x930o5EH1ip3/BDP7PvP8An+k/
M/40f2fef8/0n5n/ABqW8uJY/K8k8ljkY+8ACcfpTYrx5bvKMDCYztHqQRzn8cfhRyIPrFT+khn9
n3n/AD/Sfmf8aP7PvP8An+k/M/407+0sCTIjYqhceXJuHHY8cUj3jpcRNKNoKNhFbO45GPxo5EH1
ip/SQn9n3n/P9J+Z/wAaP7PvP+f6T8z/AI1JLfmNlRhGrldxDybQPocc0kN5LLcnYFMRRWALYIzn
Pb/OKORB9Yqf0kM/s+8/5/pPzP8AjR/Z95/z/Sfmf8afd3JSeNfNeNNjFii7jxj2NH21ooYhKU8x
88uwUYHcn16dKORB9Yqf0kM/s+8/5/pPzP8AjR/Z95/z/Sfmf8aPtTXFxBt+5hiwWQjkEDqOo/nm
nx6ksjqBs2vnbh8t+I7dKORB9Yqf0kM/s+8/5/pPzP8AjR/Z95/z/Sfmf8aeNQIt0ldFQSYCAv6+
pxxQNQLBQiI7l9nyvlemc5xRyIPrFTv+CGf2fef8/wBJ+Z/xo/s+8/5/pPzP+NSi+bOzyx5u/Zt3
cdM5zj09qSS/MbBGEavjJDyYHXHBxzRyIPrFT+kiP+z7z/n+k/M/40f2fef8/wBJ+Z/xqw14PsZu
FGRs3AH6Ug84AM1wxPVl2jb+HGf1o5EP6xU/pIg/s+8/5/pPzP8AjR/Z95/z/Sfmf8adaXcghtxK
uRIoAffkk4zyMe3rThf/AL8RMIySSPkk3EEDPIxx0o5EL6xU/pIj/s+8/wCf6T8z/jR/Z95/z/Sf
mf8AGpI752ETPEFWUfLh8npnniiK+d/JLxBVl+6Q+SOM8jFHIg+sVP6SI/7PvP8An+k/M/40f2fe
f8/0n5n/ABpyaksjKBsw+QuHyw47jtU1nKWs4SzEsUGST14o5EH1ip/SRXFhdZw1/KM9Ov8AjS6Z
JIs11HJK8pRgo3HPrVwvnA/2h/OqFi23ULz/AHz/ADNKyTVjRVJTpy5vL8zTy/8AdX/vr/61GX/u
r/31/wDWpnmVW8ySeaQLM0SRnaNoGScA85B9as5S5l/7q/8AfX/1qMv/AHV/76/+tVNrqVG8pFEz
om5mY7c+nY88GmPqRwWjiDIsYkJLYODntjrxQBfy/wDdX/vr/wCtRl/7q/8AfX/1qotqDpvDQjcN
mAH6hjj0pWvzGsvnIFaMA4Vsgg8Dk470AXcv/dX/AL6/+tRl/wC6v/fX/wBas2TUXaMiIxbw6cpJ
uBBOOuP6d6Li8mRiFULIPLz+8JXBYjHT9cd/agDSy/8AdX/vr/61GX/ur/31/wDWqm13KG2LEjOq
7mHmYA9MHHPShL4yyqsUYKlFcszY4Oe2OvFAFzL/AN1f++v/AK1GX/ur/wB9f/WqnPLI13HEkrRq
UZjtAySCPUH1pFu3RJVcxs0TAFnbYCCAcng880ARHT16LIQPs/k4Iz360jWAwMuWAt/JwBgnvnrV
2igCjZQzreiaYYVIRGMgAn8ATVqZZZCwDxmNgPkki3D+YqSigCnHYLCIDBJtkiz8zLkNnrxxTns2
kleWSbLPCYjhMde/WrVFAFL+zlDRMDGzJGIyJI9ynHfGeDSvYk7ikqoXi8px5fGPUAHirlFAFJtP
OF2TAEQeQ2UzkevXilGnqSu+QlRb+QQBj8auUUAVrS1+zMh/cnauMrCFY/U5q55ntTKKAKesHNnn
/psP/QalBOBUGr5FqvPBkBx+Bpn9o2/92X8hUXSk7nV7OU6UeVX3/QsuokQq/INMEJIIklkkUjGG
xj9BUP8AaNv/AHZfyFH9o2/92X8hT549yPq9X+Uk+zAqweR3yuzJI4H5U54Fffkt86bDz25/xqH+
0bf+7L+Qo/tG3/uy/kKOePcPq9X+Ud9mLNJvkbY7A7R3GB149qkaIlyySOhbrtxz+YqH+0bf+7L+
Qo/tG3/uy/kKOePcPq9X+Ula3DeZ87gSDDAY54x6UjW4beN7hZBhlGMHjHpUf9o2/wDdl/IUf2jb
/wB2X8hRzx7h9Xq/yk5iUzLLzuUYHP8An1P50nlEMSkjqCclRjGfyqH+0bf+7L+Qo/tG3/uy/kKO
ePcPq9X+Ussu5kYk5Q5H5Y/rTGgRu2BsKYHAwf8A9VQ/2jb/AN2X8hR/aNv/AHZfyFHPHuH1er/K
SG2DZ3yyNlCnOOAfw9qc8KOwLZOFK49j/wDqqH+0bf8Auy/kKP7Rt/7sv5Cjnj3D6vV/lJfJPBEs
gYDBbjJHvxSiLEgcSOGwAenzY9eKh/tG3/uy/kKP7Rt/7sv5Cjnj3D6vV/lLBQGVZCTkAj8//wBV
MFuFACu67SdpGPlB7fSov7Rt/wC7L+Qo/tG3/uy/kKOePcPq9X+UnWLDq5dmZQRk47//AKqhX93O
Yg0giUKeo2gsSAOmR04/L0yn9o2/92X8hVnS5Irq5usKShjRSHA55empJ7EyozgryQzyVEKRhmGz
G055GKPKyVLSO5Vtwzj0x2FNup4LCbynl3gnCgOC6+zA8/Q/n6mP+0bf+7L+QocktxQpTmrxRKYF
LFgzKxbdkdjjH8qUw8hhLIGxgsMZP6VD/aNv/dl/IUf2jb/3ZfyFLnj3L+r1f5SztymxiWBGDnvT
FhK4HmyFR0XIx/LNQ/2jb/3ZfyFH9o2/92X8hRzx7h9Xq/ykwhULEuWxF93n2xTVtwpT945VPuqc
YHGPSo/7Rt/7sv5Cj+0bf+7L+Qo549w+r1f5SYQKEiXLYj6c+2KFhVViUFsRfd/LFQ/2jb/3ZfyF
H9o2/wDdl/IUc8e4fV6v8pMkOzAWRwg6Jxj+WafGvlxqik4UYGarf2jb/wB2X8hR/aNv/dl/IUc8
e4fV6v8AKXEP7xf94fzqnbf8ft3/ANdD/M0DUrcEECXI9hRpxE09zJk/M2R+JNLmTasWqU4U58y7
fmW8n1qJocuXSR42P3tuOfzBqx5Y9TR5Y9TVnKVWt84IlkVtu0sCMsPfIoNshV1G4BkEeAegGf8A
GrXlj1NHlj1NAFSe3MmSjEMdoz6AHNOFuu197M5fGWJ546dPSrPlj1NHlj1NAFZoC6FXmkbOCCcc
EHPGBSNbBtxaRyzADdxng5HarXlj1NHlj1NAFVoCxz50gbG0sMZYe/FPjiWN9yZHyhcdgB0/nU/l
j1NHlj1NAFeSLfIriR0ZQQCuOh+o9qT7Ou0AM4YNu355JxjPNWfLHqaPLHqaAJvKb1FHlN6ipqKA
IfKb1FHlN6ipqKAIfKb1FHlN6ipqKAIfKb1FHlN6ipqKAIfKb1FHlN6ipqKAIfKb1FHlN6ipqKAI
fKb1FHlN6ipqKAIfKb1FHlN6ipqKAIfKb1FHlN6ipqKAIfKb1FHlN6ipqKAIfKb1FHlN6ipqKAIf
Kb1FHlN6ipqKAIfKb1FHlN6ipqKAIfKb1FHlN6ipqKAIfKb1FHlN6ipqKAIfKb1FHlN6ipqKAIfK
b1FV5LKSWeXc4EMkaoygkFsFuM+nzfj9Ot6igDOhLi4xIcu58uQ+sijg/wDAkwcdBjHU1b8pvUVJ
5aeb5mxfMxt3Y5x6Z9KdQBD5Teoo8pvUVNRQBD5Teoo8pvUVNRQBD5Teoo8pvUVNRQBD5Teoo8pv
UVNRQBD5Teoo8pvUVNRQBD5Teoo8pvUVNRQBD5Teoo8pvUVNRQBD5Teoo8pvUVNRQBD5Teoo8pvU
VNRQBD5Teoo8pvUVNRQBD5Teoo8pvUVNRQBD5Teoo8pvUVNRQAUUUUAFFFFABRRRQAUUUUAFFFFA
BRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAF
FFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQB//2Q==
			</helperscreenshot>
		</mouseitem>
		<validationitem
		enabled="True"
		optional="False"
		duration="100ms"
		searchpropertyid="0f34ccd1-dbf6-48e4-a931-881ec2445390"
		action="AttributeEqual">
			<comment/>
			<bindings/>
			<info
			sourcename="ElementTree"
			id="c5d32c80-9011-4933-b9c9-d6b40d8b75ec">
				<element
				role="button"
				prefcap="button"
				capabilities="flexelement"
				flavor="flex"
				visible="True"
				valid="True"
				enabled="False"
				hasfocus="False"
				dontcache="True"
				index="1"
				rect="1769,967,57,24"
				snapElem="">
					<attribute
					name="pressed"
					type="System.Boolean">
						False
					</attribute>
					<attribute
					name="text"
					type="System.String">
						Save
					</attribute>
					<attribute
					name="basetype"
					type="System.String">
						mx.controls::Button
					</attribute>
					<attribute
					name="flexuid"
					type="System.String">
						WebRateShoppingConfigPortlet0.ApplicationView38.vs.WebRateShoppingPod4.view.mainContainer.competitiveMarketReferenceChannelView.VBox1794.HBox1885.saveChannel
					</attribute>
					<attribute
					name="fulltype"
					type="System.String">
						mx.controls::Button
					</attribute>
					<attribute
					name="id"
					type="System.String">
						saveChannel
					</attribute>
					<attribute
					name="type"
					type="System.String">
						Button
					</attribute>
					<dynamicattribute
					name="alpha"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="autoRepeat"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="baselinePosition"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						16
					</dynamicattribute>
					<dynamicattribute
					name="blendMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						normal
					</dynamicattribute>
					<dynamicattribute
					name="buttonMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="cacheAsBitmap"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="cachePolicy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						auto
					</dynamicattribute>
					<dynamicattribute
					name="className"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						Button
					</dynamicattribute>
					<dynamicattribute
					name="contentMouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-564
					</dynamicattribute>
					<dynamicattribute
					name="contentMouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-232
					</dynamicattribute>
					<dynamicattribute
					name="currentState"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="doubleClickEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="emphasized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="enabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						False
					</dynamicattribute>
					<dynamicattribute
					name="errorString"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMaxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMaxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="focusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="height"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="helpBundle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="helpId"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						saveChannel
					</dynamicattribute>
					<dynamicattribute
					name="iconHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="iconWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="includeInLayout"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="initialized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="instanceIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="isDocument"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="isPopUp"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="label"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						Save
					</dynamicattribute>
					<dynamicattribute
					name="labelPlacement"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						right
					</dynamicattribute>
					<dynamicattribute
					name="maxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						10000
					</dynamicattribute>
					<dynamicattribute
					name="maxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						10000
					</dynamicattribute>
					<dynamicattribute
					name="measuredHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="measuredMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="measuredMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						57
					</dynamicattribute>
					<dynamicattribute
					name="measuredWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						57
					</dynamicattribute>
					<dynamicattribute
					name="minHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="minWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						57
					</dynamicattribute>
					<dynamicattribute
					name="mouseChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="mouseEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseFocusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-564
					</dynamicattribute>
					<dynamicattribute
					name="mouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-232
					</dynamicattribute>
					<dynamicattribute
					name="name"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						saveChannel
					</dynamicattribute>
					<dynamicattribute
					name="needsSoftKeyboard"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="nestLevel"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						11
					</dynamicattribute>
					<dynamicattribute
					name="numAutomationChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="numChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						2
					</dynamicattribute>
					<dynamicattribute
					name="percentHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="percentWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="processedDescriptors"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="repeaterIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="reportTheme"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotation"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="scaleX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="scaleY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="scaleZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="selected"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="showInAutomationHierarchy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="tabChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="tabEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="tabIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="toggle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="toolTip"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="uid"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						WebRateShoppingConfigPortlet0.ApplicationView38.vs.WebRateShoppingPod4.view.mainContainer.competitiveMarketReferenceChannelView.VBox1794.HBox1885.saveChannel
					</dynamicattribute>
					<dynamicattribute
					name="updateCompletePendingFlag"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="useHandCursor"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="validationSubField"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="visible"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						True
					</dynamicattribute>
					<dynamicattribute
					name="width"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						57
					</dynamicattribute>
					<dynamicattribute
					name="x"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1750
					</dynamicattribute>
					<dynamicattribute
					name="y"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						5
					</dynamicattribute>
					<dynamicattribute
					name="z"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
				</element>
				<path>
					/dom[@caption='IDeaS G3']//button[@text='Save']
				</path>
			</info>
			<helperscreenshot
			guid="af769958-60b6-43d6-a17f-1c770e835d1d">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************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			</helperscreenshot>
			<matchname>
				Text
			</matchname>
			<matchvalue>
				Save
			</matchvalue>
		</validationitem>
		<validationitem
		enabled="True"
		optional="False"
		duration="100ms"
		searchpropertyid="a5735ee5-fbe8-4ce6-8438-721b46b5e3fd"
		action="AttributeEqual">
			<comment/>
			<bindings/>
			<info
			sourcename="ElementTree"
			id="ac4204f8-e146-4fa1-a6dd-34b256be1bda">
				<element
				role="text"
				prefcap="text"
				capabilities="flexelement"
				flavor="flex"
				visible="False"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="True"
				index="0"
				rect="12,962,24,20"
				snapElem="">
					<attribute
					name="caption"
					type="System.String">
					</attribute>
					<attribute
					name="selectionlength"
					type="System.Int32">
						0
					</attribute>
					<attribute
					name="selectionstart"
					type="System.Int32">
						0
					</attribute>
					<attribute
					name="selectiontext"
					type="System.String">
					</attribute>
					<attribute
					name="text"
					type="System.String">
					</attribute>
					<attribute
					name="basetype"
					type="System.String">
						mx.controls::Label
					</attribute>
					<attribute
					name="flexuid"
					type="System.String">
						AlertManagerPortlet0.InformationManagerView4.VBox6.StatusBar17.button.statusArea
					</attribute>
					<attribute
					name="fulltype"
					type="System.String">
						mx.controls::Label
					</attribute>
					<attribute
					name="id"
					type="System.String">
						statusArea
					</attribute>
					<attribute
					name="type"
					type="System.String">
						Label
					</attribute>
					<dynamicattribute
					name="alpha"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="baselinePosition"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						14
					</dynamicattribute>
					<dynamicattribute
					name="blendMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						normal
					</dynamicattribute>
					<dynamicattribute
					name="buttonMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="cacheAsBitmap"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="cachePolicy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						auto
					</dynamicattribute>
					<dynamicattribute
					name="className"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						Label
					</dynamicattribute>
					<dynamicattribute
					name="condenseWhite"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="contentMouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1064
					</dynamicattribute>
					<dynamicattribute
					name="contentMouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-284
					</dynamicattribute>
					<dynamicattribute
					name="currentState"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="doubleClickEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="enabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						True
					</dynamicattribute>
					<dynamicattribute
					name="errorString"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMaxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMaxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="focusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="height"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						20
					</dynamicattribute>
					<dynamicattribute
					name="helpBundle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="helpId"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						statusArea
					</dynamicattribute>
					<dynamicattribute
					name="htmlText"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="includeInLayout"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="initialized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="instanceIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="isDocument"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="isPopUp"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="maxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						10000
					</dynamicattribute>
					<dynamicattribute
					name="maxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						10000
					</dynamicattribute>
					<dynamicattribute
					name="measuredHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						20
					</dynamicattribute>
					<dynamicattribute
					name="measuredMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						20
					</dynamicattribute>
					<dynamicattribute
					name="measuredMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="measuredWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="minHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						20
					</dynamicattribute>
					<dynamicattribute
					name="minWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="mouseChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseFocusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1064
					</dynamicattribute>
					<dynamicattribute
					name="mouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-284
					</dynamicattribute>
					<dynamicattribute
					name="name"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						statusArea
					</dynamicattribute>
					<dynamicattribute
					name="needsSoftKeyboard"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="nestLevel"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						7
					</dynamicattribute>
					<dynamicattribute
					name="numAutomationChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="numChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="percentHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="percentWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="processedDescriptors"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="repeaterIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="reportTheme"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotation"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="scaleX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="scaleY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="scaleZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="selectable"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="showInAutomationHierarchy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="tabChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="tabEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="tabIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="textHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="textWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="toolTip"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="uid"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						AlertManagerPortlet0.InformationManagerView4.VBox6.StatusBar17.button.statusArea
					</dynamicattribute>
					<dynamicattribute
					name="updateCompletePendingFlag"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="useHandCursor"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="validationSubField"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="visible"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						False
					</dynamicattribute>
					<dynamicattribute
					name="width"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="x"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						5
					</dynamicattribute>
					<dynamicattribute
					name="y"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="z"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
				</element>
				<path>
					/dom[@caption='IDeaS G3' and @page='information-manager' and @path='/solutions/information-manager' and @browsername='Mozilla']//div[#'portlet_content_one']/flexobject[@id='portlet_flash']/container/container[4]//text[@id='statusArea']
				</path>
			</info>
			<helperscreenshot
			guid="f97d962a-2ce5-4981-8513-3e7196cc6934">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************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			</helperscreenshot>
			<matchname>
				Text
			</matchname>
			<matchvalue/>
		</validationitem>
		<mouseitem
		enabled="True"
		optional="False"
		duration="500ms"
		searchpropertyid="c922ce58-5bb6-40c9-9517-c787b97bf575"
		action="Click"
		button="Left"
		movetime="300ms"
		sx="1900"
		sy="168"
		loc="Center">
			<comment/>
			<bindings/>
			<info
			sourcename="Recorder"
			id="d7dd2840-f37d-414d-940b-92b8762946bd">
				<element
				role="unknown"
				prefcap="imgtag"
				capabilities="webelement, imgtag"
				flavor="mozillaweb"
				visible="True"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="True"
				index="0"
				rect="1892,163,16,16"
				snapElem="">
					<attribute
					name="hidden"
					type="System.String">
						False
					</attribute>
					<attribute
					name="tagname"
					type="System.String">
						img
					</attribute>
					<attribute
					name="alt"
					type="System.String">
						Help
					</attribute>
					<attribute
					name="height"
					type="System.String">
						16px
					</attribute>
					<attribute
					name="src"
					type="System.String">
						/solutions/images/shell/help.png
					</attribute>
					<attribute
					name="title"
					type="System.String">
						Help
					</attribute>
					<attribute
					name="width"
					type="System.String">
						16px
					</attribute>
				</element>
				<path>
					/dom[@caption='IDeaS G3']//a[#'pageLevelHelpLink']/img[@title='Help']
				</path>
			</info>
			<helperscreenshot
			guid="3160d6d5-8b7e-4a4f-84fe-ed5439a3c0a4">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************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			</helperscreenshot>
		</mouseitem>
		<validationitem
		enabled="True"
		optional="False"
		duration="100ms"
		searchpropertyid="6fb247b7-9c89-4d89-8b2b-d706d95a37fb"
		action="Exists">
			<comment/>
			<bindings/>
			<info
			sourcename="ElementTree"
			id="fdb6af56-c71f-4f0c-8e78-612e68fbd8e6">
				<path>
					/dom[]//iframe[#'topic']/?/?/h1[@innertext~'^Competitive\ Market\ Refere']
				</path>
			</info>
			<matchname/>
			<matchvalue/>
		</validationitem>
		<mouseitem
		enabled="True"
		optional="False"
		duration="500ms"
		searchpropertyid="54a52b33-844e-465a-bbee-d51a3c467967"
		action="Click"
		button="Left"
		movetime="300ms"
		sx="421"
		sy="21"
		loc="Center">
			<comment/>
			<bindings/>
			<info
			sourcename="Recorder"
			id="ca447c95-5019-4482-9ba6-548a38ed0abc">
				<element
				role="button"
				prefcap="button"
				capabilities="accessible"
				flavor="msaa"
				visible="True"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="False"
				index="0"
				rect="412,8,16,16"
				snapElem="">
					<attribute
					name="pressed"
					type="System.Boolean">
						False
					</attribute>
					<attribute
					name="accessibledefaultaction"
					type="System.String">
						press
					</attribute>
					<attribute
					name="accessiblerole"
					type="System.Windows.Forms.AccessibleRole, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
						PushButton
					</attribute>
					<attribute
					name="accessiblestate"
					type="System.Windows.Forms.AccessibleStates, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
						None
					</attribute>
				</element>
				<path>
					/form[@title~'^Jaspersoft.*']/toolbar[@accessiblename='Browser tabs']/?/?/tabpage[@accessiblename~'^IDeaS\ -\ Information\ Manag']/button[@accessiblerole='PushButton']
				</path>
			</info>
			<helperscreenshot
			guid="d344afca-6309-4c8b-b4dc-c9c607f33c0f">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************
qqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqr/wAARCAAqAGADASIA
AhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQA
AAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3
ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWm
p6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEA
AwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSEx
BhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElK
U1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3
uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwC79itv
+eCflR9itv8Angn5U/z0DrG24Mw4+U4PGevSkiu4JjiNifl3D5SMj245qyBv2K2/54J+VH2K2/54
J+VO+1Q7C25uG24KHdn0xjNPikSZN8ZyuSM4x0oAq3EVnbxh3t1IJxwKr+fp/wDz7H/vkf41Y1b/
AI9V/wB8fyNSnzpbiSOOYIygFVIHzVnJu51whDkTave/UpGew7W5H/ART4Ps05YLAvHcoKtDzori
OOSYOzAllAHy1BaLunvB338fmaE3dBOEOSTSs15+Y/7NB/zxj/75FH2aH/njH/3yKnAyM013jjx5
jquem44zWxxkX2aH/njH/wB8ij7ND/zxj/75FFtOJY03vGJTnKg/0+lT4oAi+xf6UsxdTtORlPm6
YxnPTvij7EfKjQSkFImj3AeuOf0q7sk/55n8x/jRsk/55n8x/jUlGd9l+zR7ldVbzNw8uAlRxj7o
5p9rFP8AZ8+YYy284KDOScg//W96vbJP+eZ/Mf40bJP+eZ/Mf40AZuqKwtvmfcDICBjGBjpU7LPH
cySRRKxYAKxI+WpLu0kuYgmCuGzng/1qD+zrv/n6l/P/AOyrKSdzrhKPIk3a1+j8hIJXurlZGjCt
HlXPQtjIzj8Dz7GksP8Aj6u/9/8AqasWln5+liMyMkqSSBZR1B3nP1BxyP8A9dR2FtJA8yvlpcje
OPfkHPIPr/XIppaonnThK710/MlUYyPTikZEfG9FbHTIzUrQyE5VCD9Rz+tII5e8R/Mf41rc5bFe
CARRruVC4z8wH9amp3ly/wDPJvzH+NHly/8APJvzH+NAFvd7N/3yaN3s3/fJqWiouVYi3ezf98mj
d7N/3yaloouFiLd7N/3yaN3s3/fJqWii4WMtFuoYzJhwIpXdYlBPmKWJOffB4HrjPs7ToreAFIoo
96DAkROXQ8q2R1yOp9Qa0qzdF5+25/guXjX/AGVHRR7DJwPekMu7vZv++TRu9m/75NS0U7isRbvZ
v++TRu9m/wC+TUtFFwsf/9k=
			</helperscreenshot>
		</mouseitem>
		<mouseitem
		enabled="True"
		optional="False"
		duration="500ms"
		searchpropertyid="a6c8a843-f225-413c-8be3-3a669e1d02a6"
		action="Click"
		button="Left"
		movetime="300ms"
		sx="897"
		sy="205"
		loc="85;3">
			<comment/>
			<bindings/>
			<info
			sourcename="Recorder"
			id="b72d358e-5d3e-4df1-97b9-3af85171bdad">
				<element
				role="tabpage"
				prefcap="tabpage"
				capabilities="flexelement"
				flavor="flex"
				visible="True"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="True"
				index="0"
				rect="812,202,166,24"
				snapElem="">
					<attribute
					name="selected"
					type="System.Boolean">
						False
					</attribute>
					<attribute
					name="title"
					type="System.String">
						Competitor Data Filter
					</attribute>
					<attribute
					name="basetype"
					type="System.String">
						mx.controls.tabBarClasses::Tab
					</attribute>
					<attribute
					name="flexuid"
					type="System.String">
						WebRateShoppingConfigPortlet0.ApplicationView38.vs.WebRateShoppingPod4.view.mainContainer.Box19.ButtonScrollingCanvas20.Canvas27.tabBar.Tab448
					</attribute>
					<attribute
					name="fulltype"
					type="System.String">
						mx.controls.tabBarClasses::Tab
					</attribute>
					<attribute
					name="id"
					type="System.String">
						mainContainer_competitorDataFilterView
					</attribute>
					<attribute
					name="type"
					type="System.String">
						Tab
					</attribute>
					<dynamicattribute
					name="alpha"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="autoRepeat"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="baselinePosition"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="blendMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="buttonMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="cacheAsBitmap"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="cachePolicy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="className"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="closePolicy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="contentMouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="contentMouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="currentState"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="doubleClickEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="editableLabel"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="emphasized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="enabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						True
					</dynamicattribute>
					<dynamicattribute
					name="errorString"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitMaxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitMaxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="focusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="height"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="helpBundle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="helpId"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="iconHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="iconWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="includeInLayout"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="initialized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="instanceIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="isDocument"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="isPopUp"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="label"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="labelPlacement"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="maxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="maxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="measuredHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="measuredMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="measuredMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="measuredWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="menuIconField"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="menuLabelField"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="minHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="minWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseFocusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="name"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="needsSoftKeyboard"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="nestLevel"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="numAutomationChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="numChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="percentHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="percentWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="processedDescriptors"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="repeaterIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="reportTheme"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotation"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotationX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotationY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotationZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="scaleX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="scaleY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="scaleZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="showInAutomationHierarchy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="showIndicator"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="showMenu"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="tabChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="tabEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="tabIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="toggle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="toolTip"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="uid"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="updateCompletePendingFlag"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="useHandCursor"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="validationSubField"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="visible"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						True
					</dynamicattribute>
					<dynamicattribute
					name="width"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="x"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="y"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="z"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
				</element>
				<path>
					/dom[@caption='IDeaS G3']//div[#'portlet_content_one']/flexobject[@id='portlet_flash']//container[@id='view']/?/?/element/container[@type='Canvas']/container[@type='Canvas']/?/?/tabpage[@id='mainContainer_competitorDataFilterView']
				</path>
			</info>
			<helperscreenshot
			guid="1e1e5875-753e-4824-96f6-b938e0ed94c1">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************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			</helperscreenshot>
		</mouseitem>
		<validationitem
		enabled="True"
		optional="False"
		duration="100ms"
		searchpropertyid="3de235c1-6025-44af-a57c-a6dd21273dc4"
		action="AttributeEqual">
			<comment/>
			<bindings/>
			<info
			sourcename="ElementTree"
			id="c5d32c80-9011-4933-b9c9-d6b40d8b75ec">
				<element
				role="button"
				prefcap="button"
				capabilities="flexelement"
				flavor="flex"
				visible="True"
				valid="True"
				enabled="False"
				hasfocus="False"
				dontcache="True"
				index="2"
				rect="1767,954,57,24"
				snapElem="">
					<attribute
					name="pressed"
					type="System.Boolean">
						False
					</attribute>
					<attribute
					name="text"
					type="System.String">
						Save
					</attribute>
					<attribute
					name="basetype"
					type="System.String">
						mx.controls::Button
					</attribute>
					<attribute
					name="flexuid"
					type="System.String">
						WebRateShoppingConfigPortlet0.ApplicationView38.vs.WebRateShoppingPod4.view.mainContainer.competitorDataFilterView.VBox2143.HBox2257.saveBtn
					</attribute>
					<attribute
					name="fulltype"
					type="System.String">
						mx.controls::Button
					</attribute>
					<attribute
					name="id"
					type="System.String">
						saveBtn
					</attribute>
					<attribute
					name="type"
					type="System.String">
						Button
					</attribute>
					<dynamicattribute
					name="alpha"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="autoRepeat"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="baselinePosition"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						16
					</dynamicattribute>
					<dynamicattribute
					name="blendMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						normal
					</dynamicattribute>
					<dynamicattribute
					name="buttonMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="cacheAsBitmap"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="cachePolicy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						auto
					</dynamicattribute>
					<dynamicattribute
					name="className"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						Button
					</dynamicattribute>
					<dynamicattribute
					name="contentMouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-483
					</dynamicattribute>
					<dynamicattribute
					name="contentMouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-427
					</dynamicattribute>
					<dynamicattribute
					name="currentState"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="doubleClickEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="emphasized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="enabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						False
					</dynamicattribute>
					<dynamicattribute
					name="errorString"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMaxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMaxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="focusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="height"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="helpBundle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="helpId"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						saveBtn
					</dynamicattribute>
					<dynamicattribute
					name="iconHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="iconWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="includeInLayout"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="initialized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="instanceIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="isDocument"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="isPopUp"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="label"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						Save
					</dynamicattribute>
					<dynamicattribute
					name="labelPlacement"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						right
					</dynamicattribute>
					<dynamicattribute
					name="maxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						10000
					</dynamicattribute>
					<dynamicattribute
					name="maxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						10000
					</dynamicattribute>
					<dynamicattribute
					name="measuredHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="measuredMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="measuredMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						57
					</dynamicattribute>
					<dynamicattribute
					name="measuredWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						57
					</dynamicattribute>
					<dynamicattribute
					name="minHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="minWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						57
					</dynamicattribute>
					<dynamicattribute
					name="mouseChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="mouseEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseFocusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-483
					</dynamicattribute>
					<dynamicattribute
					name="mouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-427
					</dynamicattribute>
					<dynamicattribute
					name="name"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						saveBtn
					</dynamicattribute>
					<dynamicattribute
					name="needsSoftKeyboard"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="nestLevel"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						11
					</dynamicattribute>
					<dynamicattribute
					name="numAutomationChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="numChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						2
					</dynamicattribute>
					<dynamicattribute
					name="percentHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="percentWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="processedDescriptors"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="repeaterIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="reportTheme"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotation"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="scaleX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="scaleY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="scaleZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="selected"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="showInAutomationHierarchy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="tabChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="tabEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="tabIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="toggle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="toolTip"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="uid"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						WebRateShoppingConfigPortlet0.ApplicationView38.vs.WebRateShoppingPod4.view.mainContainer.competitorDataFilterView.VBox2143.HBox2257.saveBtn
					</dynamicattribute>
					<dynamicattribute
					name="updateCompletePendingFlag"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="useHandCursor"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="validationSubField"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="visible"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						True
					</dynamicattribute>
					<dynamicattribute
					name="width"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						57
					</dynamicattribute>
					<dynamicattribute
					name="x"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1748
					</dynamicattribute>
					<dynamicattribute
					name="y"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="z"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
				</element>
				<path>
					/dom[@caption='IDeaS G3']//button[@text='Save']
				</path>
			</info>
			<helperscreenshot
			guid="41705470-ae79-4061-ac35-d7b9173fd63f">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************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==
			</helperscreenshot>
			<matchname>
				Text
			</matchname>
			<matchvalue>
				Save
			</matchvalue>
		</validationitem>
		<validationitem
		enabled="True"
		optional="False"
		duration="100ms"
		searchpropertyid="a5735ee5-fbe8-4ce6-8438-721b46b5e3fd"
		action="AttributeEqual">
			<comment/>
			<bindings/>
			<info
			sourcename="ElementTree"
			id="ac4204f8-e146-4fa1-a6dd-34b256be1bda">
				<element
				role="text"
				prefcap="text"
				capabilities="flexelement"
				flavor="flex"
				visible="False"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="True"
				index="0"
				rect="12,962,24,20"
				snapElem="">
					<attribute
					name="caption"
					type="System.String">
					</attribute>
					<attribute
					name="selectionlength"
					type="System.Int32">
						0
					</attribute>
					<attribute
					name="selectionstart"
					type="System.Int32">
						0
					</attribute>
					<attribute
					name="selectiontext"
					type="System.String">
					</attribute>
					<attribute
					name="text"
					type="System.String">
					</attribute>
					<attribute
					name="basetype"
					type="System.String">
						mx.controls::Label
					</attribute>
					<attribute
					name="flexuid"
					type="System.String">
						AlertManagerPortlet0.InformationManagerView4.VBox6.StatusBar17.button.statusArea
					</attribute>
					<attribute
					name="fulltype"
					type="System.String">
						mx.controls::Label
					</attribute>
					<attribute
					name="id"
					type="System.String">
						statusArea
					</attribute>
					<attribute
					name="type"
					type="System.String">
						Label
					</attribute>
					<dynamicattribute
					name="alpha"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="baselinePosition"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						14
					</dynamicattribute>
					<dynamicattribute
					name="blendMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						normal
					</dynamicattribute>
					<dynamicattribute
					name="buttonMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="cacheAsBitmap"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="cachePolicy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						auto
					</dynamicattribute>
					<dynamicattribute
					name="className"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						Label
					</dynamicattribute>
					<dynamicattribute
					name="condenseWhite"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="contentMouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1064
					</dynamicattribute>
					<dynamicattribute
					name="contentMouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-284
					</dynamicattribute>
					<dynamicattribute
					name="currentState"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="doubleClickEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="enabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						True
					</dynamicattribute>
					<dynamicattribute
					name="errorString"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMaxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMaxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="focusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="height"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						20
					</dynamicattribute>
					<dynamicattribute
					name="helpBundle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="helpId"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						statusArea
					</dynamicattribute>
					<dynamicattribute
					name="htmlText"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="includeInLayout"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="initialized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="instanceIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="isDocument"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="isPopUp"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="maxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						10000
					</dynamicattribute>
					<dynamicattribute
					name="maxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						10000
					</dynamicattribute>
					<dynamicattribute
					name="measuredHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						20
					</dynamicattribute>
					<dynamicattribute
					name="measuredMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						20
					</dynamicattribute>
					<dynamicattribute
					name="measuredMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="measuredWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="minHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						20
					</dynamicattribute>
					<dynamicattribute
					name="minWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="mouseChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseFocusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1064
					</dynamicattribute>
					<dynamicattribute
					name="mouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-284
					</dynamicattribute>
					<dynamicattribute
					name="name"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						statusArea
					</dynamicattribute>
					<dynamicattribute
					name="needsSoftKeyboard"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="nestLevel"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						7
					</dynamicattribute>
					<dynamicattribute
					name="numAutomationChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="numChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="percentHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="percentWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="processedDescriptors"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="repeaterIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="reportTheme"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotation"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="scaleX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="scaleY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="scaleZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="selectable"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="showInAutomationHierarchy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="tabChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="tabEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="tabIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="textHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="textWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="toolTip"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="uid"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						AlertManagerPortlet0.InformationManagerView4.VBox6.StatusBar17.button.statusArea
					</dynamicattribute>
					<dynamicattribute
					name="updateCompletePendingFlag"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="useHandCursor"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="validationSubField"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="visible"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						False
					</dynamicattribute>
					<dynamicattribute
					name="width"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="x"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						5
					</dynamicattribute>
					<dynamicattribute
					name="y"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="z"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
				</element>
				<path>
					/dom[@caption='IDeaS G3' and @page='information-manager' and @path='/solutions/information-manager' and @browsername='Mozilla']//div[#'portlet_content_one']/flexobject[@id='portlet_flash']/container/container[4]//text[@id='statusArea']
				</path>
			</info>
			<helperscreenshot
			guid="f97d962a-2ce5-4981-8513-3e7196cc6934">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************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			</helperscreenshot>
			<matchname>
				Text
			</matchname>
			<matchvalue/>
		</validationitem>
		<mouseitem
		enabled="True"
		optional="False"
		duration="500ms"
		searchpropertyid="c922ce58-5bb6-40c9-9517-c787b97bf575"
		action="Click"
		button="Left"
		movetime="300ms"
		sx="1900"
		sy="168"
		loc="Center">
			<comment/>
			<bindings/>
			<info
			sourcename="Recorder"
			id="d7dd2840-f37d-414d-940b-92b8762946bd">
				<element
				role="unknown"
				prefcap="imgtag"
				capabilities="webelement, imgtag"
				flavor="mozillaweb"
				visible="True"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="True"
				index="0"
				rect="1892,163,16,16"
				snapElem="">
					<attribute
					name="hidden"
					type="System.String">
						False
					</attribute>
					<attribute
					name="tagname"
					type="System.String">
						img
					</attribute>
					<attribute
					name="alt"
					type="System.String">
						Help
					</attribute>
					<attribute
					name="height"
					type="System.String">
						16px
					</attribute>
					<attribute
					name="src"
					type="System.String">
						/solutions/images/shell/help.png
					</attribute>
					<attribute
					name="title"
					type="System.String">
						Help
					</attribute>
					<attribute
					name="width"
					type="System.String">
						16px
					</attribute>
				</element>
				<path>
					/dom[@caption='IDeaS G3']//a[#'pageLevelHelpLink']/img[@title='Help']
				</path>
			</info>
			<helperscreenshot
			guid="3160d6d5-8b7e-4a4f-84fe-ed5439a3c0a4">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************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			</helperscreenshot>
		</mouseitem>
		<validationitem
		enabled="True"
		optional="False"
		duration="100ms"
		searchpropertyid="6fb247b7-9c89-4d89-8b2b-d706d95a37fb"
		action="Exists">
			<comment/>
			<bindings/>
			<info
			sourcename="ElementTree"
			id="722d87ed-1fae-481e-a09e-72ce010e703e">
				<path>
					/dom[]//iframe[#'topic']/?/?/h1[@innertext='Competitor Data Filter']
				</path>
			</info>
			<matchname/>
			<matchvalue/>
		</validationitem>
		<mouseitem
		enabled="True"
		optional="False"
		duration="500ms"
		searchpropertyid="54a52b33-844e-465a-bbee-d51a3c467967"
		action="Click"
		button="Left"
		movetime="300ms"
		sx="421"
		sy="21"
		loc="Center">
			<comment/>
			<bindings/>
			<info
			sourcename="Recorder"
			id="ca447c95-5019-4482-9ba6-548a38ed0abc">
				<element
				role="button"
				prefcap="button"
				capabilities="accessible"
				flavor="msaa"
				visible="True"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="False"
				index="0"
				rect="412,8,16,16"
				snapElem="">
					<attribute
					name="pressed"
					type="System.Boolean">
						False
					</attribute>
					<attribute
					name="accessibledefaultaction"
					type="System.String">
						press
					</attribute>
					<attribute
					name="accessiblerole"
					type="System.Windows.Forms.AccessibleRole, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
						PushButton
					</attribute>
					<attribute
					name="accessiblestate"
					type="System.Windows.Forms.AccessibleStates, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
						None
					</attribute>
				</element>
				<path>
					/form[@title~'^Jaspersoft.*']/toolbar[@accessiblename='Browser tabs']/?/?/tabpage[@accessiblename~'^IDeaS\ -\ Information\ Manag']/button[@accessiblerole='PushButton']
				</path>
			</info>
			<helperscreenshot
			guid="d344afca-6309-4c8b-b4dc-c9c607f33c0f">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************
qqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqr/wAARCAAqAGADASIA
AhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQA
AAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3
ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWm
p6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEA
AwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSEx
BhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElK
U1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3
uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwC79itv
+eCflR9itv8Angn5U/z0DrG24Mw4+U4PGevSkiu4JjiNifl3D5SMj245qyBv2K2/54J+VH2K2/54
J+VO+1Q7C25uG24KHdn0xjNPikSZN8ZyuSM4x0oAq3EVnbxh3t1IJxwKr+fp/wDz7H/vkf41Y1b/
AI9V/wB8fyNSnzpbiSOOYIygFVIHzVnJu51whDkTave/UpGew7W5H/ART4Ps05YLAvHcoKtDzori
OOSYOzAllAHy1BaLunvB338fmaE3dBOEOSTSs15+Y/7NB/zxj/75FH2aH/njH/3yKnAyM013jjx5
jquem44zWxxkX2aH/njH/wB8ij7ND/zxj/75FFtOJY03vGJTnKg/0+lT4oAi+xf6UsxdTtORlPm6
YxnPTvij7EfKjQSkFImj3AeuOf0q7sk/55n8x/jRsk/55n8x/jUlGd9l+zR7ldVbzNw8uAlRxj7o
5p9rFP8AZ8+YYy284KDOScg//W96vbJP+eZ/Mf40bJP+eZ/Mf40AZuqKwtvmfcDICBjGBjpU7LPH
cySRRKxYAKxI+WpLu0kuYgmCuGzng/1qD+zrv/n6l/P/AOyrKSdzrhKPIk3a1+j8hIJXurlZGjCt
HlXPQtjIzj8Dz7GksP8Aj6u/9/8AqasWln5+liMyMkqSSBZR1B3nP1BxyP8A9dR2FtJA8yvlpcje
OPfkHPIPr/XIppaonnThK710/MlUYyPTikZEfG9FbHTIzUrQyE5VCD9Rz+tII5e8R/Mf41rc5bFe
CARRruVC4z8wH9amp3ly/wDPJvzH+NHly/8APJvzH+NAFvd7N/3yaN3s3/fJqWiouVYi3ezf98mj
d7N/3yaloouFiLd7N/3yaN3s3/fJqWii4WMtFuoYzJhwIpXdYlBPmKWJOffB4HrjPs7ToreAFIoo
96DAkROXQ8q2R1yOp9Qa0qzdF5+25/guXjX/AGVHRR7DJwPekMu7vZv++TRu9m/75NS0U7isRbvZ
v++TRu9m/wC+TUtFFwsf/9k=
			</helperscreenshot>
		</mouseitem>
	</recorditems>
	<repository
	name="ProdSanityRepository"
	id="927162d0-2034-474b-862f-7ae30090842d"
	rxversion="5.1.0.18625"
	relativepath="ProdSanityRepository.rxrep">
		<codegen
		classname="ProdSanityRepository"
		namespace="ProdSanity"/>
		<rootfolder
		name="_rootdummy_"
		id="f7ffdded-4ad3-454f-a757-e041a19f076f"/>
	</repository>
	<usercodefile
	lang="CSharp">
		RateShoppingConfigure.UserCode.cs
	</usercodefile>
	<references/>
</recordtable>
