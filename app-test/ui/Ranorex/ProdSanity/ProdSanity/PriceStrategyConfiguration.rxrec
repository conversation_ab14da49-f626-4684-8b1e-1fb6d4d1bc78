<?xml version="1.0" encoding="utf-8"?>
<recordtable
rxversion="5.1.0.18625"
id="fa5eeb36-1063-469b-a2a3-10c5e42be843"
embeddedrepository="false"
mode="Desktop">
	<name>
		PriceStrategyConfiguration
	</name>
	<repeatcount>
		1
	</repeatcount>
	<turbomode>
		False
	</turbomode>
	<genreport>
		True
	</genreport>
	<speedfactor>
		1
	</speedfactor>
	<useDefaultItemLogging>
		True
	</useDefaultItemLogging>
	<codegen
	classname="PriceStrategyConfiguration"
	namespace="ProdSanity"/>
	<variables/>
	<recorditems>
		<mouseitem
		enabled="True"
		optional="False"
		duration="500ms"
		searchpropertyid="5fb7289b-359b-45f2-ab38-b4889500c91f"
		action="Move"
		button="Left"
		movetime="300ms"
		sx="393"
		sy="139"
		loc="Center">
			<comment/>
			<bindings/>
			<info
			sourcename="Recorder"
			id="869b7b83-d618-4f2e-85ac-ce85c35bab12">
				<element
				role="unknown"
				prefcap="atag"
				capabilities="webelement, atag"
				flavor="mozillaweb"
				visible="True"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="True"
				index="0"
				rect="369,127,73,23"
				snapElem="">
					<attribute
					name="class"
					type="System.String">
						active
					</attribute>
					<attribute
					name="hidden"
					type="System.String">
						False
					</attribute>
					<attribute
					name="innertext"
					type="System.String">
						&#10;                            Decisions
                       &#32;
					</attribute>
					<attribute
					name="tagname"
					type="System.String">
						a
					</attribute>
					<attribute
					name="href"
					type="System.String">
						javascript:void(0);
					</attribute>
				</element>
				<path>
					/dom[@caption='IDeaS G3']//ul[#'menu']/li[5]/a[@innertext~'^\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ ']
				</path>
			</info>
			<helperscreenshot
			guid="303659f8-bd10-4ab4-91e0-0968df71ee43">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************
qqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqr/wAARCADwAPADASIA
AhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQA
AAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3
ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWm
p6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEA
AwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSEx
BhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElK
U1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3
uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwDSxRip
vszf89F/74/+vR9mb/nov/fH/wBetLkWIcUYqb7M3/PRf++P/r0fZm/56L/3x/8AXouFiHFGKm+z
N/z0X/vj/wCvR9mb/nov/fH/ANei4WIcUYqb7M3/AD0X/vj/AOvR9mb/AJ6L/wB8f/XouFiHFGKm
+zN/z0X/AL4/+vR9mb/nov8A3x/9ei4WIcUYqb7M3/PRf++P/r0fZm/56L/3x/8AXouFiHFGKm+z
N/z0X/vj/wCvR9mb/nov/fH/ANei4WIcUYqb7M3/AD0X/vj/AOvR9mb/AJ6L/wB8f/XouFiHFGKm
+zN/z0X/AL4/+vR9mb/nov8A3x/9ei4WIcUYqb7M3/PRf++P/r0fZm/56L/3x/8AXouFiHFGKm+z
N/z0X/vj/wCvR9mb/nov/fH/ANei4WIcUYqb7M3/AD0X/vj/AOvR9mb/AJ6L/wB8f/XouFiHFGKm
+zN/z0X/AL4/+vR9mb/nov8A3x/9ei4WIcUYqb7M3/PRf++P/r0fZm/56L/3x/8AXouFiHFGKm+z
N/z0X/vj/wCvR9mb/nov/fH/ANei4WLG2T0X/vr/AOtRtk9F/wC+v/rUvmr5hXjG0Hdnrk0rSIoJ
Z1AHByelRcuw3bJ6L/31/wDWo2yei/8AfX/1qQ3Ee8oGXcF3ctgU/wAxAwUuoY9Bnk0XEN2yei/9
9f8A1qNsnov/AH1/9anCSMttDqW9AeaZHcLJJtCsOvJUj+n1ouOwu2T0X/vr/wCtRtk9F/76/wDr
UCZNu52VBk4yeo9aaZ/34jGzkA5LYJ+gxzRcLDtsnov/AH1/9ajbJ6L/AN9f/WqSii4WI9snov8A
31/9ajbJ6L/31/8AWqSii4WI9snov/fX/wBajbJ6L/31/wDWqSii4WI9snov/fX/ANajbJ6L/wB9
f/WqSii4WI9snov/AH1/9ajbJ6L/AN9f/WqSii4WI9snov8A31/9ajbJ6L/31/8AWqSii4WI9sno
v/fX/wBajbJ6L/31/wDWqSii4WI9snov/fX/ANajbJ6L/wB9f/WqSii4WI9snov/AH1/9ajbJ6L/
AN9f/WqSii4WI9snov8A31/9ajbJ6L/31/8AWqSii4WI9snov/fX/wBajbJ6L/31/wDWqSii4WKo
tnCYyuQigfUHNOMUx3H5RubJAYjjGOuKdvk9V/75/wDr0b5PVf8Avn/69FhXGC3kCFRt5i2Hnoef
8aUwvlh8u1iCTnkYx/hTt8nqv/fP/wBejfJ6r/3z/wDXosFxBAeOn+sLHB7HP+NC25jVijMzEYGW
PHv1pd8nqv8A3z/9ejfJ6r/3z/8AXosFxsilZoRGqnarYDHHpR5L+WkXy7Vwd2eePQU7fJ6r/wB8
/wD16N8nqv8A3z/9eiwXJqKh3yeq/wDfP/16N8nqv/fP/wBeiwXJqKh3yeq/98//AF6N8nqv/fP/
ANeiwXJqKh3yeq/98/8A16N8nqv/AHz/APXosFyaiod8nqv/AHz/APXo3yeq/wDfP/16LBcmoqHf
J6r/AN8//Xo3yeq/98//AF6LBcmoqHfJ6r/3z/8AXo3yeq/98/8A16LBcmoqHfJ6r/3z/wDXo3ye
q/8AfP8A9eiwXJqKh3yeq/8AfP8A9ejfJ6r/AN8//XosFyaiod8nqv8A3z/9ejfJ6r/3z/8AXosF
yaiod8nqv/fP/wBejfJ6r/3z/wDXosFyaiod8nqv/fP/ANejfJ6r/wB8/wD16LBcNknov/fX/wBa
jZJ6L/31/wDWqaii4WIdknov/fX/ANajZJ6L/wB9f/Wqaii4WIdknov/AH1/9ajZJ6L/AN9f/Wqa
ii4WIdknov8A31/9ajZJ6L/31/8AWqaii4WIdknov/fX/wBajZJ6L/31/wDWqaii4WIdknov/fX/
ANajZJ6L/wB9f/Wqaii4WIdknov/AH1/9ajZJ6L/AN9f/Wqaii4WIdknov8A31/9ajZJ6L/31/8A
Wqaii4WIdknov/fX/wBajZJ6L/31/wDWqaii4WIdknov/fX/ANajZJ6L/wB9f/Wqaii4WIdknov/
AH1/9ajZJ6L/AN9f/Wqaii4WIdknov8A31/9ajZJ6L/31/8AWqaii4WIdknov/fX/wBajZJ6L/31
/wDWqaii4WIdknov/fX/ANajZJ6L/wB9f/Wqaii4WIdknov/AH1/9ajZJ6L/AN9f/Wqaii4WCiop
JWWURom4kZ64pJLgwxq8iYy2MZzikMmopssgjiZz0AzURuCRFsjyZBkDOMUAT0VC85ihMkkeMHGA
c055lWNXHzBiAMe9AElFQSTSRsB5WQW2g7qlQsy5ddp9M5oAdRUKTO5ykWUzjO7n8qckoZnVhtKd
c+nrQBJRUH2glV2xklydoJxx60/fLtz5XzZ5G6gCSioYZnl58rC5IJ3Ukc8kgDLF8pOM7qAJ6KiW
dWuHhxgqM/WleZUnSLGS36UASUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAVJiv2mMyOUGzqD
ill2SRwhTvXeBk8+tWqKAKQDMDCwOIgST6+lIdoS13sVG08g47VeooAqylGtsRsXAYck5702dGiZ
QozEzg4/unNXKKAIbnrD/wBdBU1FFAFJ2jU7rdyshP3PX8KkuowzRHkbm2tjuPSrNFAENx5OFWX5
f7pHGPxpLVmYONxdAcKx7ip6KAILP/Un/fb+dQ2rRKq7pWDbj8u446+lXaKAKbgiaWVR80bA/UY5
oALTQykcuxP0GOKuUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRWH/bNz/ci/I/40f2zc
/wByL8j/AI1p7KRj7eBuUVh/2zc/3IvyP+NH9s3P9yL8j/jR7KQe3gblFYf9s3P9yL8j/jR/bNz/
AHIvyP8AjR7KQe3gblFYf9s3P9yL8j/jR/bNz/ci/I/40eykHt4G5RWH/bNz/ci/I/40f2zc/wBy
L8j/AI0eykHt4G5RXPJqOoTzFY5QuT02jA/SrOdT/wCfyH8v/say17HW6aWjkl/XobFFY+dT/wCf
yH8v/saM6n/z+Q/l/wDY0a9mHJH+df18jYorHzqf/P5D+X/2NGdT/wCfyH8v/saNezDkj/Ov6+Rs
UVj51P8A5/Ify/8AsaM6n/z+Q/l/9jRr2Yckf51/XyNiisfOp/8AP5D+X/2NV573UYGw86n3Cj/C
jXezBU4tpKa1/rsdBRWEms3IQZWMnHUg8/rS/wBs3P8Aci/I/wCNaqnJq5yyqxi2mblFYf8AbNz/
AHIvyP8AjR/bNz/ci/I/40eykL28DcorD/tm5/uRfkf8aP7Zuf7kX5H/ABo9lIPbwNyisP8Atm5/
uRfkf8aP7Zuf7kX5H/Gj2Ug9vA3KKw/7Zuf7kX5H/Gj+2bn+5F+R/wAaPZSD28CjJG0ZG7HIyCGB
H6UP9yP/AHf6mn3EiSFdg6DBO0Ln8BxTHIKp7L/U109jk01sSmGJYo3kkcFwSAqA45x60z7PL5e/
aMY3dRnHrjrT2un8iKON3UKpDAHAPNP+0R8SfN5gj2bcDHTGc/T2pajtEiNtKNuVGXxtG4ZOfakN
vKGVQoJbgbWBH5ipftKi5jlCkhUCkH6YNO+1ICBuZkOQQIlTGRjPHei7C0SD7O+7blAcZz5i4P45
pfs8u9lKgFepLAD8+lPje3jLY3HIGHaMN/46TipGuo2ldtzKpVRjy1YHA9OlF2FokTWjrB5hZQQx
UqWHb8eaY8Eka7mAA6EbgSPqO1SSTQyRMmGjw5ZQBkc9uvFE88ckZAy7kghmQAj8R1oTYNRG6d/x
8P8AT+tXJJXWVURQxYE8tjp+FUtP/wBe/wBP61akV/OSRApwCCCcdce1ZU/h+/8AM6sV/FfovyQ9
Z12kv8pU4I68+3rS+fHt3ZPXGNpzn6dahaBmXcT85bdwSB0xjPWoxGwW5YRBpVMPl/PuOS5BwT0y
BiqbsjGMeZ2LInTJO7gAHG05/wA+1AnViuGxlsYZSD0zVbJkkbeArsNuxsjJUnIB749RmpBC+V3H
gPnG4nAxjqaadxNW3JlnjZtoY557HB+h70iXEbkBWPzdCVIB/GokicCNW27Y+hB5PGKVYWCQLkfu
+v5YpiJUnR2wpPTP3SAfoaq6l91Px/pUsMbo+ThVxjaGJB/A9Kh1H7qfj/Spn8D9Ga0P40PVfmRQ
RB0Lu2xFAycZP0Ap3lK7AQMX4JO4bcfrikgkQRNHIDtbByvUEfzp8ckMZZVMhV1KsxABH0Gfb1qo
3sZVLc79f1Gi2mLlQoyF3Z3DGPXPSpIbQlm83GFTcMOo3c469KDcRhGjUMV8rYpIGSc55oFygt/L
w2fLKfjuzTbkJKIxrdy+FTaNob5nHQ+/Apr28sYJZRhcZIIPXpVgSpcBoySoKJySo5XjuRSzzQkv
EWbbtQBlAPIH1pXYcq3K4tZiCdoAABJLAAZ6Ugt5Sm8KMYzjcMkeuOtTT3MbxOiBuQgGfYUoulKL
klGVdvEStnjHU8ineQrRK0X3z/ut/I0iqXYKvJJwKWMgMc/3T/I063kWKXey7iAdo9+1MnoiX7H/
ANNP0o+x/wDTT9K1/sEX/PST8x/hR9gi/wCekn5j/Cs/ao39izI+x/8ATT9KPsf/AE0/Stf7BF/z
0k/Mf4UfYIv+ekn5j/Cj2qD2LMj7H/00/Sj7H/00/Stf7BF/z0k/Mf4UfYIv+ekn5j/Cj2qD2LMj
7H/00/Sj7H/00/Stf7BF/wA9JPzH+FH2CL/npJ+Y/wAKPaoPYsyPsf8A00/Sj7H/ANNP0rX+wRf8
9JPzH+FH2CL/AJ6SfmP8KPaoPYswYZVtp5M5bkjj61N9vT+41X7+1SGEMruSWxzj0NU44y+TkKo6
segrKKklo/wOqdSlKV5Q19f+ARS3qvE6KGUspAYdvetDSoorjzJVVvKYR/K0m4q6sxxnr6Hn1/Cq
pibIEZEmRn5ATUtnNNZzSN9nmkiYfOEA+Vh35I7Zz9BRJS6slTp7Rjb53LmoWsSWM8qggpDKQM92
O4n65AIrK+3p/card9etdwpBHbTIHcElwvQHPGCe4H61XEUhXcI3IHfaaIqXR2Byp396N/nYZ9vT
+41H29P7jU+KMyvtBA4Jyac0LAAoRIDnBXNP3/5vwFzUP5H9/wDwCL7en9xqhubhZ1AAIIq0YnXA
KOGJxgrQYXGQyOGyMDbSam1bm/AqNSjGSkoPTz/4BWSLKKc9R6Uvk/7X6VO0boAXRlB6EjFDRSIu
5o2UepGK1TsrHLJKUm7EHk/7X6UeT/tfpVlYcx+Y0iICcDOf6CmFCDx8wJwCAcGnzMnkRD5P+1+l
Hk/7X6VYEMpBIjcgdTtPFDRERo4ydy5PHTnFHMHIiv5P+1+lHk/7X6VOsbsQFRiTyMDrT0t5WLAI
wKjJBBzRzByIq+T/ALX6UeT/ALX6VYMTZVQr7j22/wAqTypNxXy23DqMc0czDkRubqN1RZozXKdx
Luo3VFmjNAEu6jdUWaM0AS7qN1RZozQBLuo3VFmjNAEOpnNuv++P5GqMTKYnjdtuSCGI4yKuXql4
QAf4qo+S3qK2gvdOeo7SJzNGEYAruMe0lVwCc/T0pIpEAhYvt8rOVwefpUPkt6ijyW9RVcpHMTxy
RkwMzhPLPIwfXPFP3Kv2eRpMBcnbzk8mqvkt6ijyW9RRyhzDreRUn3NwMH+VPiuAJFyBGihsBc9S
Ki8lvUUeS3qKfKJSsTQyosSqxwctzjpkYzSwyRxAKZQ2HU8A4wCc1B5Leoo8lvUUuUfMSxyqA+75
iZFbHr1p8yGOCTcxJdwRkEHv61X8lvUUeS3qKOUOYlikUWwTfGrBifnTPp7GiOVF37yCVO5CowCf
84/KovJb1FHkt6ijlFzE1s0KFHZl3A5bduz+GOPzpRLGYwhbGYyuSDwc5qDyW9RR5Leoo5R8xMTC
RGpcNtUjJBxnP505pYiNgdQDFtyAcA5z35qv5Leoo8lvUUcocxOJYwix7x/qyu7BwDnNJ5qKjIHy
RFt3AHk5zUPkt6ijyW9RRyhzG15X+1+lHlf7X6U7ePf8jRvHv+RrnOsb5X+1+lHlf7X6U7ePf8jR
vHv+RoAb5X+1+lHlf7X6U7ePf8jRvHv+RoAb5X+1+lHlf7X6U7ePf8jRvHv+RoAb5X+1+lHlf7X6
U7ePf8jRvHv+RoArXibYgc5+aqDyqhAbOT0AUn+VaF6wMIxn73pWZIrm4QpgYU5JXI7VvT+E5avx
EqsHUMpyDQDkcgj61X8gebGGXeuGJJHGSRTVTBHmxlky3G3POeDitLmdi0DkngjH60tU/K3SAFDt
8wHBHbbSlCu5dh8oSDKgdsen1oCxbpgmjKswbhCQ3tUdqFBl2LtXfwMY7DtTEhY89ASdwI68nFIC
yrBlDKcgjIpaplcxRq0Q4QctGW59Palji3nMqbv3S/eHfn9adwsW6KpkFoo1eME7By0ZY59Palx8
qeejOPLGMqTg980XCxboqkEYxxbgCnljGYy+D9BQ0RZXJUuRENpK855/Wi4WLtFVGjZfMES7cqvQ
deefxpvl5Rgo4JX5VjKDrQFi2zBBljgZxTqpywgM4WP5PlOAvHXn9KtR7dg2LtXsMY/SgB1FFFAj
TzRmm5ozXId47NGabmjNADs0ZpuaM0AOzRmm5ozQA7NGabmjNAEV4f3Q/wB6qdW7s/uh9apPIiY3
uq56ZOK6KfwnLV+IdRSAgjIOQaAcjjP4irMhaRlVxhlDD0IzS0hIAJJwB3oAFVVXCgKB2ApaYssb
DKyKQPQ0+gAoopAQQCCCD3FAC0140fG9FbHTIzTqKACiimGWMPsMihvTPNAD6KKKACimCRWYqMkg
4+6cfnT6ACiiigDR2P8A3f1FGx/7v6irFFch3lfY/wDd/UUbH/u/qKsUUAV9j/3f1FGx/wC7+oqx
RQBX2P8A3f1FGx/7v6irFFAFfY/939RRsf8Au/qKsUUAUbtWEQ3DHNZspYXMewAna3U49K19Q/1C
/wC9/Q1n10U/hOWr8RWEZWSOMscEMSFJA6imq+SPNkKpludxHOeBmrdFXYzuU5HYOwLhQANhaQr2
64xzU8wVofnbaOOewNS0UBcpyt5iNllZVKnenHfp1qSPa052uzIqgjDkjqfzqxRRYLlNX3SoEOA2
QR5hJ6HqO1IhC2qeW3PAfLn5f8Ku0UrBcqBz5fMg8vfglXJwMev1oV9qyOjsyI+fvE5GBmrTKGGD
n8CRTREgTbg4Jyck80BcIg3l5b7x5+lRQvEsSxuVD9GU9SfpViimBU3nf98+b5mNm7tn0+lAyI/M
3vu8zH3jjG7HSrdFFguU4gV2bS3MrA/MfehXy0WJGMhJ3ruPHB7dquUwRKH3ck+7E4pWHcrrLlIF
EmX/AIhnnoetKpC2qM7MS4GSXIH59qtUUxXNmiiiuQ7gooooAKKKKACiiigAooooArah/qF/3v6G
s+tDUP8AUL/vf0NZMwDTRo/KHPB6E/5zXRT+E5avxE1FVv8AVtKIuFQAkDp7j8qGlcpuQkq77Vxj
gfjV3M7FmkBBJAIOOvtVfdLlFJZcvjJxnGPbimnfH57iQ/Kw4wPm4HWi4WLdFVpJH/esGK+XjC4H
P1okkf8AesGK+XjC4HP1ouFiwCCSAQcdfagEEcEH6VWVm+0SKPkBcZb8BxQjySSKvmFQd+cAdjxR
cLFqiqySO+xN5BJYFgBk4NPgYtCSzbiCRn8aLhYmorP2v9j2YOzZvz+HT86nnkZQzRs/yDnG3aD7
55ouFizRVZ3kxMwkI8voMD0zRPKwDNGX+UcgbdoPvnmi4WLNFVpXfEzBynljgADnjPehpJWdggb5
QMY24PHfNFwsWaKqtJJlyHIw6qBgY5x/jQZZFDLksRIF3YGcYz9KLhY6Oio903/PJf8Avv8A+tRu
m/55L/33/wDWrkO4koqPdN/zyX/vv/61G6b/AJ5L/wB9/wD1qAJKKj3Tf88l/wC+/wD61G6b/nkv
/ff/ANagCSio903/ADyX/vv/AOtRum/55L/33/8AWoAkoqPfL/zyH/ff/wBapFO5QR0IzQBW1D/U
L/vf0NZzKrjDKGHoRW1RWkZ2VjKVPmd7mKqqq7VUAegFJtXbt2jb0xjituiq9r5E+x8zEWONfuoo
wc8Ck8qMvvKLu9cc1uUUe18g9j5mIURmDMilh0JHIoKIzBmRSw6EjkVt0Ue18g9j5mIUUggqCD14
60BFByFAP0rboo9r5B7HzMKSFZF29BnPCg/zFOjQRoFXoK26KXtfIPY+Zi7V27cDbjGMcU1oo2OW
jUn1Ircop+18g9j5mLtXBG0fN14601oo2OWjUnpkityij2vkHsfMwZbdJX3N6YxgfzxmnPHG+N6K
2PUZrcope08g9j5mJsXn5Ryc9KCikEFQQ3XjrW3RT9r5B7HzP//Z
			</helperscreenshot>
		</mouseitem>
		<mouseitem
		enabled="True"
		optional="False"
		duration="500ms"
		searchpropertyid="28995745-3d18-438b-ab90-e249fc3a7587"
		action="Click"
		button="Left"
		movetime="300ms"
		sx="388"
		sy="170"
		loc="Center">
			<comment/>
			<bindings/>
			<info
			sourcename="Recorder"
			id="4281046f-c3ed-4ea3-b499-ab74b906ea76">
				<element
				role="unknown"
				prefcap="atag"
				capabilities="webelement, atag"
				flavor="web"
				visible="True"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="True"
				index="0"
				rect="355,159,182,25"
				snapElem="">
					<attribute
					name="class"
					type="System.String">
						active
					</attribute>
					<attribute
					name="contenteditable"
					type="System.String">
						inherit
					</attribute>
					<attribute
					name="innertext"
					type="System.String">
						Price Strategy Configuration
					</attribute>
					<attribute
					name="tagname"
					type="System.String">
						a
					</attribute>
					<attribute
					name="tagvalue"
					type="System.String">
					</attribute>
					<attribute
					name="href"
					type="System.String">
						/solutions/price-strategy-configuration?r=2090#/p1360
					</attribute>
					<attribute
					name="tabindex"
					type="System.String">
						0
					</attribute>
					<dynamicattribute
					name="accessKey"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-activedescendant"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-busy"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-checked"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-controls"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-describedby"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-disabled"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-expanded"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-flowto"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-haspopup"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-hidden"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-invalid"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-labelledby"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-level"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-live"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-multiselectable"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-owns"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-posinset"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-pressed"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-readonly"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-relevant"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-required"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-secret"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-selected"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-setsize"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-valuemax"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-valuemin"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="aria-valuenow"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="charset"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="coords"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="dataFld"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="dataFormatAs"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="dataSrc"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="data-workcontext-fragment"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="dir"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="disabled"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="hideFocus"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="hreflang"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="implementation"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="lang"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="language"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="methods"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="role"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="shape"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
					<dynamicattribute
					name="urn"
					type="System.String"
					description="Dynamic HTML attribute."
					readonly="False"/>
				</element>
				<path>
					/dom[@caption='IDeaS G3']//ul[#'menu']/li[5]/ul/?/?/a[@innertext~'^Price\ Strategy\ Configurat']
				</path>
			</info>
			<helperscreenshot
			guid="a749aecb-816f-4309-a47c-4fe3c196e144">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************
qqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqr/wAARCAF0AZIDASIA
AhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQA
AAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3
ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWm
p6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEA
AwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSEx
BhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElK
U1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3
uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwDQxRin
4oxWpmMxRin4oxQAzFGKfijFADMUYp+KMUAMxRin4oxQAzFGKfijFADMUYp+KMUAMxRin4oxQAzF
GKfijFADMUYp+KMUAMxRin4oxQAzFGKfijFADMUYp+KMUAMxRin4oxQAzFGKfijFADMUYp+KMUAM
xRin4oxQAzFGKfijFADMUYp+KMUAMxRin4oxQAzFGKfijFADMUYp+KMUAMxRin4oxQAzFGKfijFA
DMUYp+KMUAMxRT8UUAOxRipfs4/56yf+O/4UfZx/z1k/8d/wqbjsRYoxUv2cf89ZP/Hf8KPs4/56
yf8Ajv8AhRcLEWKMVL9nH/PWT/x3/Cj7OP8AnrJ/47/hRcLEWKMVL9nH/PWT/wAd/wAKPs4/56yf
+O/4UXCxFijFS/Zx/wA9ZP8Ax3/Cj7OP+esn/jv+FFwsRYoxUv2cf89ZP/Hf8KPs4/56yf8Ajv8A
hRcLEWKMVL9nH/PWT/x3/Cj7OP8AnrJ/47/hRcLEWKMVL9nH/PWT/wAd/wAKPs4/56yf+O/4UXCx
FijFS/Zx/wA9ZP8Ax3/Cj7OP+esn/jv+FFwsRYoxUv2cf89ZP/Hf8KPs4/56yf8Ajv8AhRcLEWKM
VL9nH/PWT/x3/Cj7OP8AnrJ/47/hRcLEWKMVL9nH/PWT/wAd/wAKPs4/56yf+O/4UXCxFijFS/Zx
/wA9ZP8Ax3/Cj7OP+esn/jv+FFwsRYoxUv2cf89ZP/Hf8KPs4/56yf8Ajv8AhRcLEWKMVL9nH/PW
T/x3/Cj7OP8AnrJ/47/hRcLEWKMVL9nH/PWT/wAd/wAKPs4/56yf+O/4UXCxFijFS/Zx/wA9ZP8A
x3/Cj7OP+esn/jv+FFwsRYoxUv2cf89ZP/Hf8KPs4/56yf8Ajv8AhRcLEWKMVL9nH/PWT/x3/Cj7
OP8AnrJ/47/hRcLEWKMVL9nH/PWT/wAd/wAKPs4/56yf+O/4UXCxFijFS/Zx/wA9ZP8Ax3/Cj7OP
+esn/jv+FFwsRYoxUv2cf89ZP/Hf8KPs4/56yf8Ajv8AhRcLEWKMVL9nH/PWT/x3/Cj7OP8AnrJ/
47/hRcLEWKMVL9nH/PWT/wAd/wAKPs4/56yf+O/4UXCxFijFS/Zx/wA9ZP8Ax3/Cj7OP+esn/jv+
FFwsRYoqX7OP+esn/jv+FFFwsDXKgZCuw2hiQOgp6yhnZQDhercY/wAaTyE2sBkBk2fh/k0jQBm3
FyeMYIHA71JQgmIC5VmZ8sAAOlDXClTgPjZuyAOB+P0oljZ5kKsVAUjK49vWl8hNrKMgFNn4f5NA
CiUF9qqxA6txgUyO7hk3fMFA5yWH+PH408RAPuVmAPVeMGlii8sYDsVHAU44oAQzDftRS/y7sqR0
/OmRXBdI90bbnGeMY+vX3qR4g8iuzH5eQvGP8aakCpt+ZjtBAz6H/wDVQBG12MN5aEsrAEZB6nHr
TzdRiYRHhu/I4P55oFsoUrvc8AA8cY6dqcIcOWDsM/eHGD+lADftK90cAjcCQOR608zIpO5goBxl
jgZqBI3V92yQqqlQrFcnPp/9ep4I/LiCnr3oAPNBkKhWOOrDGBTBcrtLFHA2lgSB8w9qf5WJCwZh
nqvGDTBbLtKlnI2lQDj5R7UAMluG8tgiOrZHXHQnr1p4lC5Hzu2QuMDOcZpzQqxySe36HNNlixll
EhYtu+UjI4x34oAck6uQAGBOeo9DikWdXICqxyu7twKZHAfLXczI4JPBB6npTxboEK5bBQJ17UAR
i737PLQsC+08g9s+tPE4AwFd2JPAxng0v2cdfMfdu3buM5xj0oMA4KuykEnIx35NACNcqBkK7DaG
JA6Co5LkifYrRYyBzJz/ACqbyE2sBkBk2fh/k04Iobdjn1oAY0uwsxyUXC4A6k02SVimVyhVwGDD
nkilkiJg2LydwJPTvkmhrfcuPNfJOSeMk9u3tQATMyyLh2GSPlx8uPrjr+NRLO5XcSwL42ggbeSB
x3796nMW4jdIxAx8vGD+lNFsuMbmIxhRn7v0oAdCzZdGYsUbGT34zUlNjQIDySScknqadQAUUUUA
FFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQBV8uf+5P8A9/f/ALKj
y5/7k/8A39/+yq9RRcLFHy5/7k//AH9/+yo8uf8AuT/9/f8A7Kr1FFwsUfLn/uT/APf3/wCyo8uf
+5P/AN/f/sqvUUXCxR8uf+5P/wB/f/sqPLn/ALk//f3/AOyq9RRcLFHy5/7k/wD39/8AsqPLn/uT
/wDf3/7Kr1FFwsUfLn/uT/8Af3/7Kjy5/wC5P/39/wDsqvUUXCxR8uf+5P8A9/f/ALKjy5/7k/8A
39/+yq9RRcLFHy5/7k//AH9/+yo8uf8AuT/9/f8A7Kr1FFwsUfLn/uT/APf3/wCyo8uf+5P/AN/f
/sqvUUXCxR8uf+5P/wB/f/sqPLn/ALk//f3/AOyq9RRcLFHy5/7k/wD39/8AsqPLn/uT/wDf3/7K
r1FFwsUfLn/uT/8Af3/7Kjy5/wC5P/39/wDsqvUUXCxR8uf+5P8A9/f/ALKjy5/7k/8A39/+yq9R
RcLFHy5/7k//AH9/+yo8uf8AuT/9/f8A7Kr1FFwsUfLn/uT/APf3/wCyo8uf+5P/AN/f/sqvUUXC
xR8uf+5P/wB/f/sqPLn/ALk//f3/AOyq9RRcLFHy5/7k/wD39/8AsqPLn/uT/wDf3/7Kr1FFwsUf
Ln/uT/8Af3/7Kjy5/wC5P/39/wDsqvUUXCxR8uf+5P8A9/f/ALKjy5/7k/8A39/+yq9RRcLFHy5/
7k//AH9/+yo8uf8AuT/9/f8A7Kr1FFwsUfLn/uT/APf3/wCyo8uf+5P/AN/f/sqvUUXCxR8uf+5P
/wB/f/sqPLn/ALk//f3/AOyq9RRcLFHy5/7k/wD39/8AsqPLn/uT/wDf3/7Kr1FFwsUfLn/uT/8A
f3/7Kjy5/wC5P/39/wDsqvUUXCxR8uf+5P8A9/f/ALKjy5/7k/8A39/+yq9RRcLFHy5/7k//AH9/
+yoq9RRcLBRRRSGFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUU
UUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRVY
+cRhS4b+LI4ByOn61NCXKEyDByeKAH0UUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRR
QAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFA
BRRRQAUUUUAFFZP9t/8ATv8A+P8A/wBaj+2/+nf/AMf/APrVp7OXYy9tDua1FZP9t/8ATv8A+P8A
/wBaj+2/+nf/AMf/APrUezl2D20O5rUVk/23/wBO/wD4/wD/AFqP7b/6d/8Ax/8A+tR7OXYPbQ7m
tRWT/bf/AE7/APj/AP8AWo/tv/p3/wDH/wD61Hs5dg9tDua1FZP9t/8ATv8A+P8A/wBaj+2/+nf/
AMf/APrUezl2D20O5rUVk/23/wBO/wD4/wD/AFqP7b/6d/8Ax/8A+tR7OXYPbQ7mtRWT/bf/AE7/
APj/AP8AWpU1lnYKttkntv8A/rUvZy7B7aHc1aKpfbLn/nzH/f4UfbLn/nzH/f5aXIyvaRLtFUvt
lz/z5j/v8tH2y5/58x/3+WjkYe0iXaKpfbLn/nzH/f5aPtlz/wA+Y/7/AC0cjD2kS7RVL7Zc/wDP
mP8Av8tI19cKCTZjA9JQafIw9pEvUVk/23/07/8Aj/8A9aj+2/8Ap3/8f/8ArU/Zy7E+2h3Naisn
+2/+nf8A8f8A/rUf23/07/8Aj/8A9aj2cuwe2h3Naisn+2/+nf8A8f8A/rUf23/07/8Aj/8A9aj2
cuwe2h3Naisn+2/+nf8A8f8A/rUf23/07/8Aj/8A9aj2cuwe2h3Naisn+2/+nf8A8f8A/rUf23/0
7/8Aj/8A9aj2cuwe2h3Naisn+2/+nf8A8f8A/rUf23/07/8Aj/8A9aj2cuwe2h3Naisn+2/+nf8A
8f8A/rUf23/07/8Aj/8A9aj2cuwe2h3Naisn+2/+nf8A8f8A/rUf23/07/8Aj/8A9aj2cuwe2h3N
aisn+2/+nf8A8f8A/rUf23/07/8Aj/8A9aj2cuwe2h3Naisn+2/+nf8A8f8A/rUf23/07/8Aj/8A
9aj2cuwe2h3Naisn+2/+nf8A8f8A/rUf23/07/8Aj/8A9aj2cuwe2h3Naisn+2/+nf8A8f8A/rUf
23/07/8Aj/8A9aj2cuwe2h3Naisn+2/+nf8A8f8A/rUf23/07/8Aj/8A9aj2cuwe2h3Naisn+2/+
nf8A8f8A/rUf23/07/8Aj/8A9aj2cuwe2h3Naisn+2/+nf8A8f8A/rUUezl2D20O5nC1mZA4UYIJ
HzDJH060n2eXy9+3jG7qM49cdcVY86KMwy7maRYzhQBjOT1Off0pv2iL/WYfzPL2bccdMZz9O1dF
2clojUspSxDlUO0ty4z0z60z7NLtLYXAz/GOcdcc8/hTzcIbppCGCMm08cj5cU5ZoVh2EvIACArR
r1Poc5FF2CUSvGAWOf7p/kadbxCaXaW2jBJIGegzTYvvn/db+Rp9tKIZd5z90gY65IpvqJW0uAhE
h/cFmAGWLgKB+OaBazZb5QNmNxLAAZ6cmnJcbkkS4eRt4HzfeIx9aWa4R4mjUMB8oXPoARzS1HaJ
GbeUR7yoxjP3hnHrjrStbSqBuCjJA5ccZ9eePxqdrxWUsCVcrgqI1/8AQuuKa01s1x5zK7FmyUIG
B+vPP0ouwtEi+zS7gu0HIyCGGMfXpTvsjiJnLICpAxvXnIznOalN1HhAGcbQwJESgHOP4emKZJND
IjphkBKkFVHJAI6Z460rsdokTwSRpuYADjPzDIz6jqKksf8AXH/dpZ545IyBudiRhmQAgfUdfxpt
l/rj/u1WttSWknoXJJXEioiKxIJ5bH9PekW4XYS4KkHaV6nP4dabIsnnK6BThSCCcent7VG0DEbi
QX3biAxUdMYyOagsnM6BQ2TzwAFOfy60eeuC27I4wApzzUKxOhV1C7hnKlievufpStG7BiQrFiDj
cR29aAJRMpIIbAwTggg8fyoFxGVJ3EADJyCOPWofIZlwzfwsvXOM4/Oho5JMl9oO3aADnv8A/WoA
mFxGQTk8dQVIP5U5JA4yufxBH86hkjZmYgK2QBgkjoT37U+FWRMMc8+pOPxPWmBnzf65/wDeNS+R
GioZpSpcZAVN2B781FL/AK5/941M0kMyx+aZFZFCnaAdwH48VTIViMW8pTeFBGCRyMkeoHXFKbaY
JuKjG3d94Zx6461KtzEDHIQ++NNoXseuDn8fSm/aE3k4bmHy/wAcYpXY7RHT2yxAgKWKqCzCRcdu
341EbWYLkqO2RuGRnpxnPepHuFLzMAfnUAfhj/CppHjRZZg2XkKkLuU45BPQn09qV2h2TvYpiCQs
VC8qwQ89zTjayhipCgjr844+vPFT/aIFZmXzCWlVzkAYAJ46+9RxXCKZgwIEhyDsDY/A/WnditEg
dGjcq4wR1pZQBK4HADGnXEgklLAkjAGSAO3oOlNm/wBe/wDvH+dMTtrYkNsRaCbdz3XHQcjP5ime
RJuK7eVXceRwP8mrJvIy7r5QEbJsyM7sdu+KaZ4SHb597xhMYGB0759qV2O0QNkVllTcGKrlSGHq
Ovp1qL7NLuK7RwN2dwxj69KkknjZ5XXfmVMEEDg5Hv7Uv2iNovKbeFKKpIGSCCT0/GleQ2okQtZs
t8oGzG4lgAM9OTS/Z3ZwqqASBw0i859KdNcI8TRqGA+ULn0AI5p6XEIYuTIriNVVgoOOME9ad2K0
SEW0pLKApZSQV3jPHtnmni1b90eHEgztDgHvUlvcQQ7DhsqxJPlqS3pyTx+FMjniVoHbfujG0gAY
PJ7596V2FokS28rJvVeDkjkZP0HU0ptpQm8qMbd33hnHrjrUsdxGDFIwfzIl2hQOD1xz26037Qm8
nDcw+X+OMU7sLRsRvBIib2AA4z8wyM+o6imgDymPfcP61PPPG8RHzO5xhmQAjHuOv41AP9S3+8P6
0a21FZX0HxQh4nldiqJgfKuTk014xhmjYugx8xGOvtToHVNx82WNuxj9PzFSyXEUvmK+8BgvzAAk
kdyM980a3GkrEQtZju+UDbjOWAxnp3pRaTEkbQMNt+ZgOfTmppJYJkkLM6qXXAABbhSPWlkmhmjL
yMy/vchVAJxgD1pXY+VFdbaZs4TocYJAJPoM9adFaSOY87UVzgEsB3x0zzUpu45WV5VZSjlwF5By
c49qja5DTQybTlDkj/gRNO8hWiNNs4JG6L/v6v8AjRQwtixPmy8n/nmP/iqKLsGl/TF+ySeq/nR9
kk9V/Otf7A3/AD2H/fH/ANej7A3/AD2H/fH/ANep9ou5fsZdjI+ySeq/nR9kk9V/Otf7A3/PYf8A
fH/16PsDf89h/wB8f/Xo9ou4exl2Mj7JJ6r+dH2ST1X861/sDf8APYf98f8A16PsDf8APYf98f8A
16PaLuHsZdjI+ySeq/nR9kk9V/Otf7A3/PYf98f/AF6PsDf89h/3x/8AXo9ou4exl2Mj7JJ6r+dH
2ST1X861/sDf89h/3x/9ej7A3/PYf98f/Xo9ou4exl2Mj7JJ6r+dH2ST1X861/sDf89h/wB8f/Xo
+wN/z2H/AHx/9ej2i7h7GXYyPsknqv51JbwNE5ZiMYxxWn9gb/nsP++P/r1HPamFAxkDZOMbcf1o
9onoHsmtbEGaM1G8mxgoRmJBPGP60omjIB3gbugJxTFYfmjNMMqKoLui5/2uKN6jJZlABx1oAfmj
NN8xAm/eu31zxSqysuVIYHuDQIXNGahNzGITLzgHGO9P81AwUuoY9ATzQOxXe2dnZgVwSTSfZJPV
fzq15ibtu9dx7Z5oEiF9gdSw7Z5p3J5Sr9kk9V/Oj7JJ6r+dWhJGX2B13emeaBIh3YdTt689KLhy
lX7JJ6r+dH2ST1X86s+dF/z1Trj7wpWdFIDOoJ6AnrRcOUq/ZJPVfzo+ySeq/nVoyIG2l1DHjGea
RJUkJCsCQcEZ5ouHKVvsknqv50fZJPVfzq5RRcOVFP7JJ6r+dH2ST1X86uUUXDlRT+ySeq/nR9kk
9V/OrlFFw5UU/sknqv50fZJPVfzrTgtTMhYSBcHGNuf61J9gb/nsP++P/r1LqJFqk3qkZH2ST1X8
6Psknqv51r/YG/57D/vj/wCvR9gb/nsP++P/AK9HtF3D2MuxkfZJPVfzo+ySeq/nWv8AYG/57D/v
j/69H2Bv+ew/74/+vR7Rdw9jLsZH2ST1X86Psknqv51r/YG/57D/AL4/+vR9gb/nsP8Avj/69HtF
3D2MuxkfZJPVfzo+ySeq/nWv9gb/AJ7D/vj/AOvR9gb/AJ7D/vj/AOvR7Rdw9jLsZH2ST1X86Psk
nqv51r/YG/57D/vj/wCvR9gb/nsP++P/AK9HtF3D2MuxkfZJPVfzo+ySeq/nWv8AYG/57D/vj/69
H2Bv+ew/74/+vR7Rdw9jLsZH2ST1X86K1/sDf89h/wB8f/Xoo9ou4exl2LW6jdUWTRk1ynaS7qN1
RZNGTQBLuo3VFk0ZNAEu6jdUWTRk0AS7qN1RZNGTQBLuo3VFk0ZNAEu6q96cwj/ep+TWfqN4YpFh
Ee7I3Zz9aakou7H7OVROMSKSNmmUhioCkEjHt60JEElJA+XYAD+dQ/bH/wCeDfn/APWo+2P/AM8G
/P8A+tWvtYGP1Wt2X3r/ADHKjx7T5Zf5NuARxThG28Erx5m78MVH9sf/AJ4N+f8A9aj7Y/8Azwb8
/wD61HtYD+q1uy+9f5jn/ct5jgY3HjcB1781Jb5MWSMZJP5moftj/wDPBvz/APrUfbH/AOeDfn/9
aj2sBfVavZfev8xWtmMR/vbCNvv60sqzNlQGxxjGMfjnmm/bH/54N+f/ANaj7Y//ADwb8/8A61Ht
ID+q1uy+9f5khjO1vl5Mgb8MikKzNKu4NgNnttx/OmfbH/54N+f/ANaj7Y//ADwb8/8A61HtYB9V
rdl96/zBG3GOJcZTOSGB7EZ4oEUmwqQ+QhUZK4/DFH2x/wDng35//Wo+2P8A88G/P/61HtIB9Vrd
l96/zJmjJdiFH+r2iomik54chlAwpX075pPtj/8APBvz/wDrUfbH/wCeDfn/APWo9rAPqtbsvvX+
ZIYm2yfLyzAj36U+MMrMpU4ySG4xUH2x/wDng35//Wo+2P8A88G/P/61HtYC+qVey+9f5luiqn2x
/wDng35//Wo+2P8A88G/P/61P2sP6uH1Sr2X3r/Mt0VU+2P/AM8G/P8A+tR9sf8A54N+f/1qPaw/
q4fVKvZfev8AMt0VU+2P/wA8G/P/AOtR9sf/AJ4N+f8A9aj2sP6uH1Sr2X3r/M2LI4hP+9VjdWJH
qUkMeTbNtJ6k4GfyrWyawclJuxv7KdOK5iXdRuqLJoyaCSXdRuqLJoyaAJd1G6osmjJoAl3Ubqiy
aMmgCXdRuqLJoyaAJd1G6osmjJoAl3UVFk0UAHlt6UeW3pU9FAEHlt6UeW3pU9FAEHlt6UeW3pU9
FAEHlt6UeW3pU9FAEHlt6UeW3pU9FAEHlt6UeW3pU9FAEHlt6Vk6mCuoxg/88/8AGt2sTV/+QnF/
1z/qaT3Xqa09pej/ACGJE8gJXGBwSWA/nTWVkYqwwR1FTRNGLaQOCcsOA2D3p0lwzQ5VtpZzkKe2
BXTd3PPsrFZhg4BB9xRV53yZPJlVXMmSdwGRj1qORwYokRjtd0RwJAM5YA8deh6j+lLm0Hy62K6I
0jYUZ4z1xilMMg7AjG7IYEYpIX8qaWPKqN7oolOA4DEYBPUjjvVpCsZIjZY5GjIIV+M545z/AFo5
uwcvcp0qIzsFUZJqwZGIhjMxUHO8hu+49amEgVoiZDuDEZMgYgEeo7UNgolCirsTkLy+ZA2W/fAZ
HbnuKdCxMkYidVQMdyhsZ59O/GKLhylClRGkbagyasq4Fr5p/wBYuY1/x/AZqK3I/eIWCl1wCfrT
uKwx42QAtjB6EMD/ACptWIV8pmy6BypCkMDg/XtT2l2Ix8wGXywCwPU59e5xRcLFeOMuGbcqqvUm
mMMHAIPuKuPMRC5WT5iqZw3JPeld8tJ5Miq5Yc7sZGPWlcfKilRV2bDu6h1BWXJ3HHGKZNL8pVJM
BpWztPancHGxVVSzBRyScClK4zkgEHGKvM6oATKW2yKQTIGOO546UwuAx85w48xT97d8vNK4cpTo
q8kmHBmlVm3kqdwOBg/l24qvLIZIELtucMepyccU0xONiG6B/swHt539K2vLb0rHuv8AkEj/AK7/
APstb9cz+Nnf/wAuIfP9CDy29KPLb0qeimZkHlt6UeW3pU9FAEHlt6UeW3pU9FAEHlt6UeW3pU9F
AEHlt6UeW3pU9FAEHlt6UeW3pU9FAEHlt6UVPRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAVi6s
M6pFj/nn/U1tVmarZmWVJ1lKEDZgD696Vm2rGlOUVfmdrpopbW/un8qNrf3T+VNaB0GXu2UepOP6
0LbyMAVumIPcf/rrfml/L+Rz+ypf8/F9z/yHbW/un8qIYWedfmk8xZY3VNuQUDLn3BBOfp+ifZZf
+fl/1/xoEU1u63CzNI0R3hcfe45H4jI/Gk3JrYahSjrzp/J/5GxLp6PFIiuwLJKgJ5x5hyf1rKnj
Ed5cRxriNHAUAcDKg4/MmtMaraEZDSkf9cH/AMKyBDLMWnMzxtKd5U5yue34DA/CpjdPRDahLSUr
Dtrf3T+VG1v7p/KmtbyKpZrpwB9f8aU20g/5eX/I/wCNXzS/l/In2VL/AJ+L7n/kLtb+6fypwaYJ
sBcKewJxTPssv/Py/wCv+NH2WX/n5f8AX/Gi8v5fyD2dL/n4vuf+Q7D4Aw2B0FJtb+6fyppt5AQD
dMCenv8ArR9ncsV+1NkdR/k0c0v5fyD2VL/n4vuf+Q7a390/lRtb+6fypPssv/Py/wCv+NH2WX/n
5f8AX/Gjml/L+Qeypf8APxfc/wDIXa390/lTkaWPOwuueuMimfZZf+fl/wBf8aPssv8Az8v+v+NF
5fy/kHs6X/Pxfc/8hdrf3T+VG1v7p/Kk+yy/8/L/AK/40fZZf+fl/wBf8aOaX8v5B7Kl/wA/F9z/
AMhdrf3T+VG1v7p/Kk+yy/8APy/6/wCNBtpB1umH+frRzS/l/IPZUv8An4vuf+Qu1v7p/Kja390/
lSfZZf8An5f9f8aPssv/AD8v+v8AjRzS/l/IPZUv+fi+5/5D7oEaSMjH7/8A9lrerFi0yS4h+a7b
aG+6RkZ9etbVYa8zbR0ycVTjGLva4UUUUzIKKKKACiiigAooooAKKKKACiiigAooooAr+dJ/s/l/
9ejzpP8AZ/L/AOvUPmp6/pR5qev6UATedJ/s/l/9ejzpP9n8v/r1D5qev6Ueanr+lAE3nSf7P5f/
AF6POk/2fy/+vUPmp6/pR5qev6UATedJ/s/l/wDXo86T/Z/L/wCvUPmp6/pR5qev6UATedJ/s/l/
9ejzpP8AZ/L/AOvUPmp6/pR5qev6UATedJ/s/l/9ejzpP9n8v/r1D5qev6Ueanr+lAE3nSf7P5f/
AF6huXZowGxjPYUeanr+lMmdWQAHvVQ+JET+FlOQqlwrucLtIBPQGowcCSVM7A+eOhGOTVqiumxy
XKjs4VCxwr5Jy5UD0GadHmRlDOSNpPyscHn171ZoosFynEw+Ty3LPk7l3ZwOe3btSq7bQY3ZpNpL
gnODj07c1bopWHcpswKMI3LjZlstnB/p3p+5s/MxEm8ZGeMZ7VZop2FcrDIw+5iTIR97jGT2pEYF
4/nJkLHeu48cHtVqmCNQ+7kn3YnFKwXI7hS7IB15I+tRDMckjODuZAWwcY5PerlFFguU1YspUPxv
XBVy36052CB1JYjcACXIxx3PWrVFFguVEfKr5km1MtzvP4c0hc7Uy3yfNy0hXv61adA4wS34MR/K
lUBVAAwBRYdysNzqxLscRgjaxAzzzQxdA2xmJKZ5JPNWqKLCuU1YkELIMZX7shbHPqadIoGQzNsV
1OSx4/GrVFFguIuNo2nIxwc5paKKYizbOyxkLjGe4qbzpP8AZ/L/AOvVaF1VCCe9P81PX9K5p/Ez
sh8KJvOk/wBn8v8A69HnSf7P5f8A16h81PX9KPNT1/SpLJvOk/2fy/8Ar0edJ/s/l/8AXqHzU9f0
o81PX9KAJvOk/wBn8v8A69HnSf7P5f8A16h81PX9KPNT1/SgCbzpP9n8v/r0edJ/s/l/9eofNT1/
SjzU9f0oAm86T/Z/L/69HnSf7P5f/XqHzU9f0o81PX9KAJvOk/2fy/8Ar0edJ/s/l/8AXqHzU9f0
o81PX9KAJvOk/wBn8v8A69FQ+anr+lFAFeiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA
KKKKACiiigAooooAKKKKAGyOI42c5woJOKjjuVdkBR08wZTdjn8qfMhkgkQYyykDP0qultIxhEpV
VhXA2kkk4x6cUAWRLGzlFkUsOqg80iyxu21ZEZvQMCaqw2bR7Ayo+wnDF26fToKI7OUB08wpEykb
d27H04FAFkTwkEiWMgdfmHFHnoXVVYNuJGQw4x+NQPBNJZGAiNTtABDHt+FPa2xPA8YRVjyWAGM5
FAEgnhb7sqHjPDCmR3cUkauv8TbcEgEfrVazt5THbl1CCIsec7jn27U9bOUW8UWU/dyhs5PI59qA
Lfmx+Zs8xd/93PNJ50Rbb5ibs4xuGc1W+ySZ25XZ53m7s/N9Mf1pDZyc4K5+0eb+H+NAF2iiigAo
oooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKAJvs0v8Asf8AfR/wo+zS/wCx/wB9H/CrlFAF
P7NL/sf99H/Cj7NL/sf99H/CrlFAFP7NL/sf99H/AAo+zS/7H/fR/wAKuUUAU/s0v+x/30f8KPs0
v+x/30f8KuUUAU/s0v8Asf8AfR/wo+zS/wCx/wB9H/CrlFAFP7NL/sf99H/Cj7NL/sf99H/CrlFA
FP7NL/sf99H/AAo+zS/7H/fR/wAKuUUAU/s0v+x/30f8KPs0v+x/30f8KuUUAU/s0v8Asf8AfR/w
o+zS/wCx/wB9H/CrlFAFP7NL/sf99H/Cj7NL/sf99H/CrlFAFP7NL/sf99H/AAo+zS/7H/fR/wAK
uUUAU/s0v+x/30f8KPs0v+x/30f8KuUUAU/s0v8Asf8AfR/wo+zS/wCx/wB9H/CrbMFUsxAA5JJ6
UFlXG5gMnAyepoAqfZpf9j/vo/4UfZpf9j/vo/4VaZ0UkMyjA3HJ7etEckcq7o3VxnGVOaAKv2aX
/Y/76P8AhR9ml/2P++j/AIVcooAp/Zpf9j/vo/4UfZpf9j/vo/4VcooAp/Zpf9j/AL6P+FH2aX/Y
/wC+j/hVyigCn9ml/wBj/vo/4UfZpf8AY/76P+FXKKAKf2aX/Y/76P8AhR9ml/2P++j/AIVcooAp
/Zpf9j/vo/4UfZpf9j/vo/4VcooAp/Zpf9j/AL6P+FH2aX/Y/wC+j/hVyigCn9ml/wBj/vo/4UfZ
pf8AY/76P+FXKKAKf2aX/Y/76P8AhR9ml/2P++j/AIVcooAp/Zpf9j/vo/4UfZpf9j/vo/4VcooA
p/Zpf9j/AL6P+FH2aX/Y/wC+j/hVyigCn9ml/wBj/vo/4UVcooAKKKKACiiigAooooAKKKKACiii
gAooooAKKKKACiiigAooooAKKKKACiiigAooooAr3/8Ax4T/APXM/wAqZK8dzaskEqPIoDKFYEgj
kfrVuigDMZxcW8l1g7GZBjH8CkE/rn8qs27LJeTSRMGQqo3Kcgtz/TFWqKACiiigAooooAKKKKAC
iiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKAG7jRuNJRQAu40bjSUUALuNG40lFAC
7jRuNJRQAu40bjSUUALuNG40lFAC7jRuNJRQAu40bjSUUALuNG40lFAC7jRuNJRQAu40bjSUUALu
NG40lFAC7jRuNRyyCKJ5GyQoycUkkqxorMDhmAyO2fWgCXcaNxqIyqJGTDEqu44GeP8AIohlWaPe
gIGSORjocUAS7jRuNJRQAu40bjSUUALuNG40lFAC7jRuNJRQAu40bjSUUALuNG40lFAC7jRuNJRQ
Au40bjSUUALuNG40lFAC7jRuNJRQAu40bjSUUALuNFJRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRR
QAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQBDdo0lpKiDLMhAFNYNcQPC8Tx5XGWI6/gTViigCtarK
sbyzJ+9c5KgjsMAen/66LIOsbLJGyHezDJBzkk9jVmigAooooAKKKKACiiigAooooAKKKKACiiig
AooooAKKKKACiiigAooooAKKKKACiiigB+B6UYHpRRQAYHpRgelFFABgelGB6UUUAGB6UYHpRRQA
YHpRgelFFABgelGB6UUUAGB6UYHpRRQAYHpRgelFFABgelGB6UUUAGB6UYHpRRQAYHpRgelFFABg
elGB6UUUAGB6UYHpUV1IYbaWRQCUUkZpLiZoYBKAMAgvn+7nmgCbA9KMD0qAySvNJHFsGwLywPU/
/W/nRBJI0sschRimPmUYHPbGTz/jQBPgelGB6UUUAGB6UYHpRRQAYHpRgelFFABgelGB6UUUAGB6
UYHpRRQAYHpRgelFFABgelGB6UUUAGB6UYHpRRQAYHpRgelFFABgelGB6UUUAGB6UYHpRRQAYHpR
RRQAUUZHrRketABRRketGR60AFFGR60ZHrQAUUZHrRketABRRketGR60AFFGR60ZHrQAUUZHrRke
tABRRketGR60AFFGR60ZHrQAUUZHrRketABRRketGR60AFFGR60ZHrQBHcRedbyRZ271K5xnFNWK
RkaOZ0dGXbhUK/1NTZHrRketAFaK1kitGiWf963JlK/0+gp9vE8K7WZCO21SPzyTmpsj1oyPWgAo
oyPWjI9aACijI9aMj1oAKKMj1oyPWgAooyPWjI9aACijI9aMj1oAKKMj1oyPWgAooyPWjI9aACij
I9aMj1oAKKMj1oyPWgAooyPWjI9aACijI9aMj1oAKKMj1ooAZRRRQAUUUUAFFFFABRRRQAUUUUAF
FFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRUF4xWzmZSQQhII7cUl0XFr5iE7kw/B6gdR+VAF
iiqqf6TLKRIwjACja2M9yf1AotSwmlQ71AClUkbcR15zzxx69qALVFFFABRRRQAUUUUAFFFFABRR
RQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFLtNG00AJRS7TRtNACUUu00bTQAlFLtNG00
AJRS7TRtNACUUu00bTQAlFLtNG00AJRS7TRtNACUUu00bTQAlFLtNG00AJRS7TRtNACUUu00bTQA
yRFkjZHGVYYIpscKR5wXORjDOWH6mpdpo2mgCBbaJIPJVcRjnAJ9c9etOihSLJQHLdSWJJ/E1LtN
G00AJRS7TRtNACUUu00bTQAlFLtNG00AJRS7TRtNACUUu00bTQAlFLtNG00AJRS7TRtNACUUu00b
TQAlFLtNG00AJRS7TRtNACUUu00bTQAlFLtNFADqKKKACiiigAooooAKKKKACiiigAooooAKKKKA
CiiigAooooAKKKKACiiigAooooARmCqWYgAckk9KCyrjcwGTgZPU1Bf/APHhP/1zP8qZK8dzaskE
qPIoDKFYEgjkfrQBa3Lu25G7GcZ5xQrqxIVgdpwcHoapQyPPHLdQoSXwqDjIUdTz7k/lS6d8slwo
jdAHH3iD/COuD170AXaKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiig
AooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKAC
iiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigD//Z
			</helperscreenshot>
		</mouseitem>
		<mouseitem
		enabled="True"
		optional="False"
		duration="500ms"
		searchpropertyid="ddb76604-ef07-4abf-8892-05fbbc3076f9"
		action="Click"
		button="Left"
		movetime="300ms"
		sx="85"
		sy="190"
		loc="Center">
			<comment/>
			<bindings/>
			<info
			sourcename="Recorder"
			id="51ea8252-5ce1-4025-a489-c6d114c7dfd1">
				<element
				role="tabpage"
				prefcap="tabpage"
				capabilities="flexelement"
				flavor="flex"
				visible="True"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="True"
				index="1"
				rect="23,172,89,25"
				snapElem="">
					<attribute
					name="index"
					type="System.Int32">
						1
					</attribute>
					<attribute
					name="selected"
					type="System.Boolean">
						True
					</attribute>
					<attribute
					name="title"
					type="System.String">
						Rate Plan
					</attribute>
					<attribute
					name="basetype"
					type="System.String">
						mx.controls::Button
					</attribute>
					<attribute
					name="flexuid"
					type="System.String">
						BarPortlet0.ApplicationView23.vs.BarView4.tabNav.tabBar.Tab37
					</attribute>
					<attribute
					name="fulltype"
					type="System.String">
						mx.controls.tabBarClasses::Tab
					</attribute>
					<attribute
					name="type"
					type="System.String">
						Tab
					</attribute>
					<dynamicattribute
					name="alpha"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="autoRepeat"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="baselinePosition"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="blendMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="buttonMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="cacheAsBitmap"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="cachePolicy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="className"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="contentMouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="contentMouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="currentState"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="doubleClickEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="emphasized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="enabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						True
					</dynamicattribute>
					<dynamicattribute
					name="errorString"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitMaxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitMaxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="focusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="height"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="helpBundle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="helpId"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="iconHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="iconWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="includeInLayout"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="initialized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="instanceIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="isDocument"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="isPopUp"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="label"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="labelPlacement"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="maxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="maxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="measuredHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="measuredMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="measuredMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="measuredWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="minHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="minWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseFocusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="name"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="needsSoftKeyboard"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="nestLevel"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="numAutomationChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="numChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="percentHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="percentWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="processedDescriptors"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="repeaterIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="reportTheme"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotation"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotationX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotationY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotationZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="scaleX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="scaleY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="scaleZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="showInAutomationHierarchy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="tabChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="tabEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="tabIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="toggle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="toolTip"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="uid"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="updateCompletePendingFlag"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="useHandCursor"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="validationSubField"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="visible"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						True
					</dynamicattribute>
					<dynamicattribute
					name="width"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="x"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="y"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="z"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
				</element>
				<path>
					/dom[@caption='IDeaS G3']//div[#'portlet_content_one']/flexobject[@id='portlet_flash']//container[@id='tabNav']/?/?/tabpage[@title='Rate Plan']
				</path>
			</info>
			<helperscreenshot
			guid="579891d8-6234-4652-98c1-0a1a48ef2373">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************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==
			</helperscreenshot>
		</mouseitem>
		<validationitem
		enabled="True"
		optional="False"
		duration="100ms"
		searchpropertyid="a61a853b-ccec-4205-a895-81b68a6dbc94"
		action="AttributeEqual">
			<comment/>
			<bindings/>
			<info
			sourcename="ElementTree"
			id="c5d32c80-9011-4933-b9c9-d6b40d8b75ec">
				<element
				role="button"
				prefcap="button"
				capabilities="flexelement"
				flavor="flex"
				visible="True"
				valid="True"
				enabled="False"
				hasfocus="False"
				dontcache="True"
				index="1"
				rect="1778,964,57,24"
				snapElem="">
					<attribute
					name="pressed"
					type="System.Boolean">
						False
					</attribute>
					<attribute
					name="text"
					type="System.String">
						Save
					</attribute>
					<attribute
					name="basetype"
					type="System.String">
						mx.controls::Button
					</attribute>
					<attribute
					name="flexuid"
					type="System.String">
						BarPortlet0.ApplicationView23.vs.BarView4.tabNav.rateView._RateView_BusyIndicatorCanvas1.viewStack.listView._ListView_HBox3._ListView_Button1
					</attribute>
					<attribute
					name="fulltype"
					type="System.String">
						mx.controls::Button
					</attribute>
					<attribute
					name="id"
					type="System.String">
						_ListView_Button1
					</attribute>
					<attribute
					name="type"
					type="System.String">
						Button
					</attribute>
					<dynamicattribute
					name="alpha"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="autoRepeat"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="baselinePosition"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						16
					</dynamicattribute>
					<dynamicattribute
					name="blendMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						normal
					</dynamicattribute>
					<dynamicattribute
					name="buttonMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="cacheAsBitmap"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="cachePolicy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						auto
					</dynamicattribute>
					<dynamicattribute
					name="className"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						Button
					</dynamicattribute>
					<dynamicattribute
					name="contentMouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-629
					</dynamicattribute>
					<dynamicattribute
					name="contentMouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-226
					</dynamicattribute>
					<dynamicattribute
					name="currentState"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="doubleClickEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="emphasized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="enabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						False
					</dynamicattribute>
					<dynamicattribute
					name="errorString"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMaxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMaxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="explicitWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="focusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="height"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="helpBundle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="helpId"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						_ListView_Button1
					</dynamicattribute>
					<dynamicattribute
					name="iconHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="iconWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="includeInLayout"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="initialized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="instanceIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="isDocument"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="isPopUp"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="label"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						Save
					</dynamicattribute>
					<dynamicattribute
					name="labelPlacement"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						right
					</dynamicattribute>
					<dynamicattribute
					name="maxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						10000
					</dynamicattribute>
					<dynamicattribute
					name="maxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						10000
					</dynamicattribute>
					<dynamicattribute
					name="measuredHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="measuredMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="measuredMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						57
					</dynamicattribute>
					<dynamicattribute
					name="measuredWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						57
					</dynamicattribute>
					<dynamicattribute
					name="minHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						24
					</dynamicattribute>
					<dynamicattribute
					name="minWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						57
					</dynamicattribute>
					<dynamicattribute
					name="mouseChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="mouseEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseFocusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="mouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-629
					</dynamicattribute>
					<dynamicattribute
					name="mouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-226
					</dynamicattribute>
					<dynamicattribute
					name="name"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						_ListView_Button1
					</dynamicattribute>
					<dynamicattribute
					name="needsSoftKeyboard"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="nestLevel"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						12
					</dynamicattribute>
					<dynamicattribute
					name="numAutomationChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="numChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						2
					</dynamicattribute>
					<dynamicattribute
					name="percentHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="percentWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						NaN
					</dynamicattribute>
					<dynamicattribute
					name="processedDescriptors"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="repeaterIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="reportTheme"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotation"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="rotationZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
					<dynamicattribute
					name="scaleX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="scaleY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="scaleZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1
					</dynamicattribute>
					<dynamicattribute
					name="selected"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="showInAutomationHierarchy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="tabChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="tabEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						true
					</dynamicattribute>
					<dynamicattribute
					name="tabIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						-1
					</dynamicattribute>
					<dynamicattribute
					name="toggle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="toolTip"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="uid"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						BarPortlet0.ApplicationView23.vs.BarView4.tabNav.rateView._RateView_BusyIndicatorCanvas1.viewStack.listView._ListView_HBox3._ListView_Button1
					</dynamicattribute>
					<dynamicattribute
					name="updateCompletePendingFlag"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="useHandCursor"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						false
					</dynamicattribute>
					<dynamicattribute
					name="validationSubField"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="visible"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						True
					</dynamicattribute>
					<dynamicattribute
					name="width"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						57
					</dynamicattribute>
					<dynamicattribute
					name="x"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						1769
					</dynamicattribute>
					<dynamicattribute
					name="y"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						10
					</dynamicattribute>
					<dynamicattribute
					name="z"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						0
					</dynamicattribute>
				</element>
				<path>
					/dom[@caption='IDeaS G3']//button[@text='Save']
				</path>
			</info>
			<helperscreenshot
			guid="7d726c05-9d84-4bb7-8a51-a2fbee19a957">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************
qqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqr/wAARCAA9ANgDASIA
AhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQA
AAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3
ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWm
p6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEA
AwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSEx
BhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElK
U1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3
uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwDd2/Wj
b9afRQAzb9aNv1p9FADNv1o2/Wn0UAM2/Wjb9afRQAzb9aNv1p9NkkSJd0jqgzjLHFACbfrRt+tM
+123/PxF/wB9ij7Xbf8APxF/32KV0K6H7frRt+tM+123/PxF/wB9ij7Xbf8APxF/32KLoLoft+tG
360z7Xbf8/EX/fYo+123/PxF/wB9ii6C6H7frRt+tM+123/PxF/32KdHPDK22OVHPXCsDRdBdC7f
rRt+tPopjGbfrRt+tPooAZt+tG360+igBm360bfrT6KAK12uLWT6UU68/wCPST6UVvS2Zy190T0U
UVgdQUUUUAFFFFABRRRQAVm67/x5J/10H8jWlWbrv/Hkn/XQfyNTP4WRP4WPXTLTHMWT/vH/ABpf
7Ns/+eP/AI8f8al3mkckgDjr0PetORdibR7Ef9m2f/PH/wAeP+NH9m2f/PH/AMeP+NPDYBAG3ntQ
JGxknoeaXJHsHKuwz+zbP/nj/wCPH/Gj+zbP/nj/AOPH/Gn+Y3HPXmnK7Y5o5F2C0exB/ZtmHGYs
g8fePH61FaRJBrM8cS7VEfAzn+7VssSyf71Vov8AkO3H/XMf+y1E4pNWFZaW7mlj6/nRge/50maj
OGlw3IxwD0qjYlwPf86MD3/OoiQMKhI9lx/WkV3YqN2Mg54FAE2B7/nRge/51CrsxC5x159aN7kk
KScAdMYNAE2Aeh6e9HQ1HGfmf6/0pxPT60AR3n/HpJ9KKLz/AI9JPpRW9LZnLX3RPRRRWB1BRRRQ
AUUUUAFFFFABWbrv/Hkn/XQfyNaVUtWgluLZUhXcwcHGQOMGplsTP4WPZWQ4Kk+4ppOeqH9Kh87V
f+fWL8//ALKjzdV/59YfzH/xVV7TyI08ybPGNhx+FGeMbD+lQ+bqv/PrD+Y/+Ko83Vf+fWH8x/8A
FUe08g08yYnPVD+lAOOiH9Kh83Vf+fWH8x/8VR5uq/8APrD+Y/8AiqPaeQaeZYRWZwcYAqvCM69c
f9cx/wCy0edqv/PrF+f/ANlRZQ3X9oSXFzEE3pjgjGePf2qHLmaDski+TjqKQlWGCufqKkpKs1I/
kxjZwO2KBtHRcY9qkooAjO0jBXj6UHYeq5/CpKKAGAgdBR94jin0tAEF5/x6SfSii8/49JPpRW9L
ZnLX3RL5K+if98ijyV9E/wC+RRuNG41gdQeSvon/AHyKPJX0T/vkUbjRuNAB5K+if98ijyV9E/75
FG40bjQAeSvon/fIo8lfRP8AvkUbjRuNAB5K+if98ijyV9E/75FG40bjQAeSvon/AHyKPJX0T/vk
UbjRuNAB5K+if98ijyV9E/75FG40bjQAeSvon/fIo8lfRP8AvkUbjRuNAB5K+if98ijyV9E/75FG
40bjQAeSvon/AHyKPJX0T/vkUbjRuNAB5K+if98ijyV9E/75FG40bjQAeSvon/fIo8lfRP8AvkUb
jRuNAB5K+if98ijyV9E/75FG40bjQBHcRKLeQ4XhD/D7UUXDH7PJ/uH+VFb0tmctfdH/2Q==
			</helperscreenshot>
			<matchname>
				Text
			</matchname>
			<matchvalue>
				Save
			</matchvalue>
		</validationitem>
		<mouseitem
		enabled="True"
		optional="False"
		duration="500ms"
		searchpropertyid="c922ce58-5bb6-40c9-9517-c787b97bf575"
		action="Click"
		button="Left"
		movetime="300ms"
		sx="1900"
		sy="168"
		loc="Center">
			<comment/>
			<bindings/>
			<info
			sourcename="Recorder"
			id="d7dd2840-f37d-414d-940b-92b8762946bd">
				<element
				role="unknown"
				prefcap="imgtag"
				capabilities="webelement, imgtag"
				flavor="mozillaweb"
				visible="True"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="True"
				index="0"
				rect="1892,163,16,16"
				snapElem="">
					<attribute
					name="hidden"
					type="System.String">
						False
					</attribute>
					<attribute
					name="tagname"
					type="System.String">
						img
					</attribute>
					<attribute
					name="alt"
					type="System.String">
						Help
					</attribute>
					<attribute
					name="height"
					type="System.String">
						16px
					</attribute>
					<attribute
					name="src"
					type="System.String">
						/solutions/images/shell/help.png
					</attribute>
					<attribute
					name="title"
					type="System.String">
						Help
					</attribute>
					<attribute
					name="width"
					type="System.String">
						16px
					</attribute>
				</element>
				<path>
					/dom[@caption='IDeaS G3']//a[#'pageLevelHelpLink']/img[@title='Help']
				</path>
			</info>
			<helperscreenshot
			guid="3160d6d5-8b7e-4a4f-84fe-ed5439a3c0a4">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************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			</helperscreenshot>
		</mouseitem>
		<validationitem
		enabled="True"
		optional="False"
		duration="100ms"
		searchpropertyid="6fb247b7-9c89-4d89-8b2b-d706d95a37fb"
		action="Exists">
			<comment/>
			<bindings/>
			<info
			sourcename="ElementTree"
			id="765c6997-c1ab-4eb0-847a-3a0efc4f891b">
				<path>
					/dom[]//iframe[#'topic']/?/?/h1[@innertext~'^Price\ Strategy\ by\ Rate\ Pl']
				</path>
			</info>
			<matchname/>
			<matchvalue/>
		</validationitem>
		<mouseitem
		enabled="True"
		optional="False"
		duration="500ms"
		searchpropertyid="54a52b33-844e-465a-bbee-d51a3c467967"
		action="Click"
		button="Left"
		movetime="300ms"
		sx="421"
		sy="21"
		loc="Center">
			<comment/>
			<bindings/>
			<info
			sourcename="Recorder"
			id="ca447c95-5019-4482-9ba6-548a38ed0abc">
				<element
				role="button"
				prefcap="button"
				capabilities="accessible"
				flavor="msaa"
				visible="True"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="False"
				index="0"
				rect="412,8,16,16"
				snapElem="">
					<attribute
					name="pressed"
					type="System.Boolean">
						False
					</attribute>
					<attribute
					name="accessibledefaultaction"
					type="System.String">
						press
					</attribute>
					<attribute
					name="accessiblerole"
					type="System.Windows.Forms.AccessibleRole, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
						PushButton
					</attribute>
					<attribute
					name="accessiblestate"
					type="System.Windows.Forms.AccessibleStates, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
						None
					</attribute>
				</element>
				<path>
					/form[@title~'^Jaspersoft.*']/toolbar[@accessiblename='Browser tabs']/?/?/tabpage[@accessiblename~'^IDeaS\ -\ Information\ Manag']/button[@accessiblerole='PushButton']
				</path>
			</info>
			<helperscreenshot
			guid="d344afca-6309-4c8b-b4dc-c9c607f33c0f">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************
qqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqr/wAARCAAqAGADASIA
AhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQA
AAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3
ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWm
p6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEA
AwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSEx
BhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElK
U1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3
uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwC79itv
+eCflR9itv8Angn5U/z0DrG24Mw4+U4PGevSkiu4JjiNifl3D5SMj245qyBv2K2/54J+VH2K2/54
J+VO+1Q7C25uG24KHdn0xjNPikSZN8ZyuSM4x0oAq3EVnbxh3t1IJxwKr+fp/wDz7H/vkf41Y1b/
AI9V/wB8fyNSnzpbiSOOYIygFVIHzVnJu51whDkTave/UpGew7W5H/ART4Ps05YLAvHcoKtDzori
OOSYOzAllAHy1BaLunvB338fmaE3dBOEOSTSs15+Y/7NB/zxj/75FH2aH/njH/3yKnAyM013jjx5
jquem44zWxxkX2aH/njH/wB8ij7ND/zxj/75FFtOJY03vGJTnKg/0+lT4oAi+xf6UsxdTtORlPm6
YxnPTvij7EfKjQSkFImj3AeuOf0q7sk/55n8x/jRsk/55n8x/jUlGd9l+zR7ldVbzNw8uAlRxj7o
5p9rFP8AZ8+YYy284KDOScg//W96vbJP+eZ/Mf40bJP+eZ/Mf40AZuqKwtvmfcDICBjGBjpU7LPH
cySRRKxYAKxI+WpLu0kuYgmCuGzng/1qD+zrv/n6l/P/AOyrKSdzrhKPIk3a1+j8hIJXurlZGjCt
HlXPQtjIzj8Dz7GksP8Aj6u/9/8AqasWln5+liMyMkqSSBZR1B3nP1BxyP8A9dR2FtJA8yvlpcje
OPfkHPIPr/XIppaonnThK710/MlUYyPTikZEfG9FbHTIzUrQyE5VCD9Rz+tII5e8R/Mf41rc5bFe
CARRruVC4z8wH9amp3ly/wDPJvzH+NHly/8APJvzH+NAFvd7N/3yaN3s3/fJqWiouVYi3ezf98mj
d7N/3yaloouFiLd7N/3yaN3s3/fJqWii4WMtFuoYzJhwIpXdYlBPmKWJOffB4HrjPs7ToreAFIoo
96DAkROXQ8q2R1yOp9Qa0qzdF5+25/guXjX/AGVHRR7DJwPekMu7vZv++TRu9m/75NS0U7isRbvZ
v++TRu9m/wC+TUtFFwsf/9k=
			</helperscreenshot>
		</mouseitem>
		<mouseitem
		enabled="True"
		optional="False"
		duration="500ms"
		searchpropertyid="96142b4f-933d-4a59-af1f-a421a17f9735"
		action="Click"
		button="Left"
		movetime="300ms"
		sx="176"
		sy="186"
		loc="Center">
			<comment/>
			<bindings/>
			<info
			sourcename="Recorder"
			id="50d42d73-a503-49c6-93fb-8db51b1c0db6">
				<element
				role="tabpage"
				prefcap="tabpage"
				capabilities="flexelement"
				flavor="flex"
				visible="True"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="True"
				index="0"
				rect="117,172,99,25"
				snapElem="">
					<attribute
					name="index"
					type="System.Int32">
						0
					</attribute>
					<attribute
					name="selected"
					type="System.Boolean">
						False
					</attribute>
					<attribute
					name="title"
					type="System.String">
						Room Class
					</attribute>
					<attribute
					name="basetype"
					type="System.String">
						mx.controls::Button
					</attribute>
					<attribute
					name="flexuid"
					type="System.String">
						BarPortlet0.ApplicationView23.vs.BarView4.tabNav.tabBar.Tab39
					</attribute>
					<attribute
					name="fulltype"
					type="System.String">
						mx.controls.tabBarClasses::Tab
					</attribute>
					<attribute
					name="type"
					type="System.String">
						Tab
					</attribute>
					<dynamicattribute
					name="alpha"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="autoRepeat"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="baselinePosition"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="blendMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="buttonMode"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="cacheAsBitmap"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="cachePolicy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="className"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="contentMouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="contentMouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="currentState"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="doubleClickEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="emphasized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="enabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						True
					</dynamicattribute>
					<dynamicattribute
					name="errorString"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitMaxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitMaxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="explicitWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="focusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="height"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="helpBundle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="helpId"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="iconHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="iconWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="includeInLayout"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="initialized"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="instanceIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="isDocument"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="isPopUp"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="label"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="labelPlacement"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="maxHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="maxWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="measuredHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="measuredMinHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="measuredMinWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="measuredWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="minHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="minWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseFocusEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="mouseY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="name"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="needsSoftKeyboard"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="nestLevel"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="numAutomationChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="numChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="percentHeight"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="percentWidth"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="processedDescriptors"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="repeaterIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="reportTheme"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotation"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotationX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotationY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="rotationZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="scaleX"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="scaleY"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="scaleZ"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="showInAutomationHierarchy"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="tabChildren"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="tabEnabled"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="tabIndex"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="toggle"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="toolTip"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="uid"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="updateCompletePendingFlag"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="useHandCursor"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="validationSubField"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="visible"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False">
						True
					</dynamicattribute>
					<dynamicattribute
					name="width"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="x"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="y"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
					<dynamicattribute
					name="z"
					type="System.String"
					description="Dynamic flash object property."
					readonly="False"/>
				</element>
				<path>
					/dom[@caption='IDeaS G3']//div[#'portlet_content_one']/flexobject[@id='portlet_flash']//container[@id='tabNav']/?/?/tabpage[@title='Room Class']
				</path>
			</info>
			<helperscreenshot
			guid="8b21e279-7f5d-4221-9fb6-876b82dbcb93">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************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			</helperscreenshot>
		</mouseitem>
		<validationitem
		enabled="True"
		optional="False"
		duration="100ms"
		searchpropertyid="ace91b2b-43ce-4090-8717-37d4721e020d"
		action="AttributeEqual">
			<comment/>
			<bindings/>
			<info
			sourcename="ElementTree"
			id="e2399a77-1259-4105-93b6-7461409ac4fc">
				<element
				role="cell"
				prefcap="cell"
				capabilities="flexdataitem"
				flavor="flex"
				visible="True"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="True"
				index="0"
				rect="9,207,762,30"
				snapElem="">
					<attribute
					name="columnindex"
					type="System.Int32">
						0
					</attribute>
					<attribute
					name="isheader"
					type="System.Boolean">
						True
					</attribute>
					<attribute
					name="rowindex"
					type="System.Int32">
						-1
					</attribute>
					<attribute
					name="text"
					type="System.String">
						Room Class Name
					</attribute>
					<attribute
					name="dataitemtype"
					type="System.String">
						String
					</attribute>
					<attribute
					name="expandable"
					type="System.Boolean">
						False
					</attribute>
					<attribute
					name="expanded"
					type="System.Boolean">
						False
					</attribute>
				</element>
				<path>
					/dom[@caption='IDeaS G3']//div[#'portlet_content_one']/flexobject[@id='portlet_flash']//container[@id='accomodationClassView']//container[@id='listView']/container[@id='_ListView_ViewStack1']/?/?/table[@id='accomClasslistViewDataGrid']/column[@text='Room Class Name']/cell[@text='Room Class Name']
				</path>
			</info>
			<helperscreenshot
			guid="097c5d7b-0729-4533-bfcb-fc2406334610">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************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			</helperscreenshot>
			<matchname>
				Text
			</matchname>
			<matchvalue>
				Room Class Name
			</matchvalue>
		</validationitem>
		<mouseitem
		enabled="True"
		optional="False"
		duration="500ms"
		searchpropertyid="c922ce58-5bb6-40c9-9517-c787b97bf575"
		action="Click"
		button="Left"
		movetime="300ms"
		sx="1900"
		sy="168"
		loc="Center">
			<comment/>
			<bindings/>
			<info
			sourcename="Recorder"
			id="d7dd2840-f37d-414d-940b-92b8762946bd">
				<element
				role="unknown"
				prefcap="imgtag"
				capabilities="webelement, imgtag"
				flavor="mozillaweb"
				visible="True"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="True"
				index="0"
				rect="1892,163,16,16"
				snapElem="">
					<attribute
					name="hidden"
					type="System.String">
						False
					</attribute>
					<attribute
					name="tagname"
					type="System.String">
						img
					</attribute>
					<attribute
					name="alt"
					type="System.String">
						Help
					</attribute>
					<attribute
					name="height"
					type="System.String">
						16px
					</attribute>
					<attribute
					name="src"
					type="System.String">
						/solutions/images/shell/help.png
					</attribute>
					<attribute
					name="title"
					type="System.String">
						Help
					</attribute>
					<attribute
					name="width"
					type="System.String">
						16px
					</attribute>
				</element>
				<path>
					/dom[@caption='IDeaS G3']//a[#'pageLevelHelpLink']/img[@title='Help']
				</path>
			</info>
			<helperscreenshot
			guid="3160d6d5-8b7e-4a4f-84fe-ed5439a3c0a4">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************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			</helperscreenshot>
		</mouseitem>
		<validationitem
		enabled="True"
		optional="False"
		duration="100ms"
		searchpropertyid="6fb247b7-9c89-4d89-8b2b-d706d95a37fb"
		action="Exists">
			<comment/>
			<bindings/>
			<info
			sourcename="ElementTree"
			id="cef5e6e7-f2cc-476b-a8db-448cb6c7f7a8">
				<path>
					/dom[]//iframe[#'topic']/?/?/h1[@innertext~'^Price\ Strategy\ by\ Room\ Cl']
				</path>
			</info>
			<matchname/>
			<matchvalue/>
		</validationitem>
		<mouseitem
		enabled="True"
		optional="False"
		duration="500ms"
		searchpropertyid="54a52b33-844e-465a-bbee-d51a3c467967"
		action="Click"
		button="Left"
		movetime="300ms"
		sx="421"
		sy="21"
		loc="Center">
			<comment/>
			<bindings/>
			<info
			sourcename="Recorder"
			id="ca447c95-5019-4482-9ba6-548a38ed0abc">
				<element
				role="button"
				prefcap="button"
				capabilities="accessible"
				flavor="msaa"
				visible="True"
				valid="True"
				enabled="True"
				hasfocus="False"
				dontcache="False"
				index="0"
				rect="412,8,16,16"
				snapElem="">
					<attribute
					name="pressed"
					type="System.Boolean">
						False
					</attribute>
					<attribute
					name="accessibledefaultaction"
					type="System.String">
						press
					</attribute>
					<attribute
					name="accessiblerole"
					type="System.Windows.Forms.AccessibleRole, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
						PushButton
					</attribute>
					<attribute
					name="accessiblestate"
					type="System.Windows.Forms.AccessibleStates, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
						None
					</attribute>
				</element>
				<path>
					/form[@title~'^Jaspersoft.*']/toolbar[@accessiblename='Browser tabs']/?/?/tabpage[@accessiblename~'^IDeaS\ -\ Information\ Manag']/button[@accessiblerole='PushButton']
				</path>
			</info>
			<helperscreenshot
			guid="d344afca-6309-4c8b-b4dc-c9c607f33c0f">
				/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDABwTFRgVERwYFhgfHRwhKUUtKSYmKVQ8QDJFZFhpZ2JY
YF9ufJ6GbnWWd19giruLlqOpsbOxa4TC0MGszp6usar/********************************
qqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqr/wAARCAAqAGADASIA
AhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQA
AAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3
ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWm
p6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEA
AwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSEx
BhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElK
U1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3
uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwC79itv
+eCflR9itv8Angn5U/z0DrG24Mw4+U4PGevSkiu4JjiNifl3D5SMj245qyBv2K2/54J+VH2K2/54
J+VO+1Q7C25uG24KHdn0xjNPikSZN8ZyuSM4x0oAq3EVnbxh3t1IJxwKr+fp/wDz7H/vkf41Y1b/
AI9V/wB8fyNSnzpbiSOOYIygFVIHzVnJu51whDkTave/UpGew7W5H/ART4Ps05YLAvHcoKtDzori
OOSYOzAllAHy1BaLunvB338fmaE3dBOEOSTSs15+Y/7NB/zxj/75FH2aH/njH/3yKnAyM013jjx5
jquem44zWxxkX2aH/njH/wB8ij7ND/zxj/75FFtOJY03vGJTnKg/0+lT4oAi+xf6UsxdTtORlPm6
YxnPTvij7EfKjQSkFImj3AeuOf0q7sk/55n8x/jRsk/55n8x/jUlGd9l+zR7ldVbzNw8uAlRxj7o
5p9rFP8AZ8+YYy284KDOScg//W96vbJP+eZ/Mf40bJP+eZ/Mf40AZuqKwtvmfcDICBjGBjpU7LPH
cySRRKxYAKxI+WpLu0kuYgmCuGzng/1qD+zrv/n6l/P/AOyrKSdzrhKPIk3a1+j8hIJXurlZGjCt
HlXPQtjIzj8Dz7GksP8Aj6u/9/8AqasWln5+liMyMkqSSBZR1B3nP1BxyP8A9dR2FtJA8yvlpcje
OPfkHPIPr/XIppaonnThK710/MlUYyPTikZEfG9FbHTIzUrQyE5VCD9Rz+tII5e8R/Mf41rc5bFe
CARRruVC4z8wH9amp3ly/wDPJvzH+NHly/8APJvzH+NAFvd7N/3yaN3s3/fJqWiouVYi3ezf98mj
d7N/3yaloouFiLd7N/3yaN3s3/fJqWii4WMtFuoYzJhwIpXdYlBPmKWJOffB4HrjPs7ToreAFIoo
96DAkROXQ8q2R1yOp9Qa0qzdF5+25/guXjX/AGVHRR7DJwPekMu7vZv++TRu9m/75NS0U7isRbvZ
v++TRu9m/wC+TUtFFwsf/9k=
			</helperscreenshot>
		</mouseitem>
	</recorditems>
	<repository
	name="ProdSanityRepository"
	id="927162d0-2034-474b-862f-7ae30090842d"
	rxversion="5.1.0.18625"
	relativepath="ProdSanityRepository.rxrep">
		<codegen
		classname="ProdSanityRepository"
		namespace="ProdSanity"/>
		<rootfolder
		name="_rootdummy_"
		id="f7ffdded-4ad3-454f-a757-e041a19f076f"/>
	</repository>
	<usercodefile
	lang="CSharp">
		PriceStrategyConfiguration.UserCode.cs
	</usercodefile>
	<references/>
</recordtable>
