{"info": {"_postman_id": "ace6d9f0-94e1-414f-a23d-8aac23ee979a", "name": "Scenario: When a sharer added in normal reservation and both break later", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Given that property configuration is saved in nucleusVendorConfigParams collection", "event": [{"listen": "prerequest", "script": {"id": "6643367b-6d41-4b31-870d-d25665687a78", "exec": ["//variables", "var moment = require('moment');", "pm.globals.set(\"timestamp\", moment().format(\"YYYY-MM-DD\"));", "pm.globals.set(\"timestampWithoutYear\", moment().format(\"MM-DD\"));", "pm.globals.set(\"timestampAfter3Year\", moment().add('years', 3).format(\"YYYY-MM-DD\"));", "pm.globals.set(\"client_Code\", \"SandBox\");", "pm.globals.set(\"property_Code\", \"OPER1\");", "pm.globals.set(\"inboundVendorId\", \"5b6320b6ccb81834a0a1260b\");", "// pm.globals.set(\"systemDate\", \"2018-01-01\");", "// pm.globals.set(\"systemEndDate\", \"2020-12-31\");", "// pm.globals.set(\"rateMsgEndDate\", \"2020-12-31\");", "// pm.globals.set(\"bookingDate\", \"2018-01-20\");", "// pm.globals.set(\"bookingFrom\", \"2018-01-20\");", "", "//pm.globals.set(\"midSeasonStartDate\", \"2016-10-30\");", "pm.globals.set(\"propertyId\", \"11020\");", "pm.globals.set(\"databaseId\", \"011020\");", "", "var collection = JSON.parse('[\"nucleusQualifiedRatePlans\",\"nucleusUnqualifiedRatePlans\",\"oxiPackages\",\"nucleusGroupBlockMasters\",\"nucleusVendorConfigParamses\",\"currencyExchanges\",\"oxiShareReservationReferences\"]');", "pm.globals.set(\"collectionName\", collection);", "", "//cleaning up NGI collections", "eval(pm.globals.get(\"cleanUpScript\"));", "eval(pm.globals.get(\"cleanUpScriptForIndividualCollection\"));", "", "", "pm.globals.get(\"cleanUpCollection\")", "", "pm.globals.set(\"param_externalSystem\", \"pacman.core.property.externalSystem\");", "pm.globals.set(\"param_externalSubSystem\", \"pacman.core.property.externalSystem.subSystem\");", "pm.globals.set(\"propertyStage\", \"Population\");", "", "", "console.log(\"Setting external parameters\");", "", "var parameters = [pm.globals.get(\"param_externalSystem\")+'/pacman.'+pm.globals.get(\"client_Code\")+'.'+pm.globals.get(\"property_Code\")+'/NGI',pm.globals.get(\"param_externalSubSystem\")+'/pacman.'+pm.globals.get(\"client_Code\")+'.'+pm.globals.get(\"property_Code\")+'/OXI'];", "pm.globals.set(\"params\", parameters);\t\t\t\t\t\t\t\t ", "eval(pm.globals.get(\"setStage\"));", "eval(pm.globals.get(\"setParameter\"));"], "type": "text/javascript"}}, {"listen": "test", "script": {"id": "90f55228-fe4a-468c-9db2-548e48b97450", "exec": ["eval(pm.globals.get(\"setStage\"));\r", "eval(pm.globals.get(\"setParameter\"));\r", "\r", "pm.sendRequest({url: pm.globals.get('hostName')+':'+pm.globals.get('g3Port')+'/api/configParam/v1/pacman.feature.RoomTypeRecodingEnabled?context=pacman.SandBox.'+ pm.globals.get('property_Code') + '&value=false&propertyId='+pm.globals.get('propertyId'),method: 'PUT',header:{'Authorization': 'Basic ********************************','Accept': '*/*'}}, function (err, res) {});\r", ""], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"inboundVendorId\": \"{{inboundVendorId}}\",\r\n    \"outboundVendorId\": null,\r\n    \"customReplyToAddress\": null,\r\n    \"useCustomSoapAction\": null,\r\n    \"name\": null,\r\n    \"inboundCredentials\": null,\r\n    \"outboundCredentials\": null,\r\n    \"integrationType\": \"OXI_PMS\",\r\n    \"chains\": [\r\n        {\r\n            \"chainCode\": \"{{client_Code}}\",\r\n            \"outboundCredentials\": null,\r\n            \"inboundCredentials\": null,\r\n            \"clientEnvironmentName\": \"g3\",\r\n            \"baseCurrencyCode\": \"USD\",\r\n            \"hotels\": [\r\n                {\r\n                    \"hotelCode\": \"{{property_Code}}\",\r\n                    \"g3HotelCode\": null,\r\n                    \"g2HotelCode\": null,\r\n                    \"inboundHotelCode\": null,\r\n                    \"propertyName\": \"{{property_Code}}\",\r\n                    \"outgoingUrl\": \"{{hostName}}:{{ngiPort}}/ngipublic/rest/mockserver/oxi/requestroomtype\",\r\n                    \"oxiInterfaceName\": \"OXI_PMS\",\r\n                    \"taxAdjustmentValue\": 10,\r\n                    \"outboundCredentials\": null,\r\n                    \"inboundCredentials\": null,\r\n                    \"baseCurrencyCode\": \"USD\",\r\n                    \"pastDays\": null,\r\n                    \"futureDays\": null,\r\n                    \"assumeTaxIncluded\": null,\r\n                    \"assumePackageIncluded\": null,\r\n                    \"installMode\": false,\r\n                    \"scheduledDeferredDelivery\": null,\r\n                    \"inCatchup\": null,\r\n                    \"calculateNonPickedUpBlocksUsingSummaryData\": null,\r\n                    \"handlePreviouslyStraightMarketSegmentsInAms\": null,\r\n                    \"unqualifiedRatesDirectPopulationDisabled\": false,\r\n                    \"qualifiedRatesDirectPopulationDisabled\": null,\r\n                    \"roomRevenuePackages\": null,\r\n                    \"installationReservationsThreshold\": null,\r\n                    \"populatePackageDataEnabled\": null,\r\n                    \"defaultRoomType\": null\r\n                }\r\n            ],\r\n            \"unqualifiedRatesDirectPopulationDisabled\": null,\r\n            \"qualifiedRatesDirectPopulationDisabled\": null,\r\n            \"installationReservationsThreshold\": null,\r\n            \"populatePackageDataEnabled\": null\r\n        }\r\n    ],\r\n    \"configurations\": null,\r\n    \"unqualifiedRatesDirectPopulationDisabled\": null,\r\n    \"qualifiedRatesDirectPopulationDisabled\": null,\r\n    \"cancelMultiUnitDecrements\": null,\r\n    \"installationReservationsThreshold\": null,\r\n    \"_links\": {\r\n        \"self\": {\r\n            \"href\": \"{{hostName}}:{{ngiPort}}/nucleusVendorConfigParamses/{{inboundVendorId}}\"\r\n        },\r\n        \"nucleusVendorConfigParams\": {\r\n            \"href\": \"{{hostName}}:{{ngiPort}}/nucleusVendorConfigParamses/{{inboundVendorId}}\"\r\n        }\r\n    }\r\n}"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusVendorConfigParamses/{{inboundVendorId}}", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusVendorConfigParamses", "{{inboundVendorId}}"]}}, "response": []}, {"name": "When a non-share reservation with Id=946904 having arrival date=30-11-2016 is sent", "event": [{"listen": "prerequest", "script": {"id": "1af7b338-3400-4cd8-84e7-1adce1e9495c", "exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"id": "81eed463-d460-450e-a68f-5d4d65e36217", "exec": ["//verifing status", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "text/xml"}], "body": {"mode": "raw", "raw": "<?xml version = '1.0' encoding = 'UTF-8'?>\r\n<?Label SandBox-OPER1|RESERVATION|20151106001|SUCCESS?>\r\n<!--Reservation_ShareBaseCase_Msg1: New reservation without sharer to start scenario\r\nIn his message: Reservation 946904 for 2 nights, 2 adults at $325 \r\nNote: /Reservation/RoomStays/RoomStay/resGuestRPHs = 0 pointing to only guest whose reservation id is 946904 -->\r\n<Reservation xmlns=\"reservation.fidelio.5.0\" mfShareAction=\"NA\" mfReservationAction=\"ADD\">\r\n   <HotelReference>\r\n      <hotelCode>SandBox-OPER1</hotelCode>\r\n   </HotelReference>\r\n   <reservationID>946904</reservationID>\r\n   <reservationOriginatorCode>SUPERVISOR</reservationOriginatorCode>\r\n   <originalBookingDate>2015-07-02T11:18:31.000</originalBookingDate>\r\n   <StayDateRange timeUnitType=\"DAY\">\r\n      <startTime>2016-12-01T00:00:00.000</startTime>\r\n      <numberOfTimeUnits>2</numberOfTimeUnits>\r\n   </StayDateRange>\r\n   <GuestCounts>\r\n      <GuestCount>\r\n         <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n         <mfCount>2</mfCount>\r\n      </GuestCount>\r\n      <GuestCount>\r\n         <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n         <mfCount>0</mfCount>\r\n      </GuestCount>\r\n   </GuestCounts>\r\n   <ResGuests>\r\n      <ResGuest reservationActionType=\"NEW\">\r\n         <resGuestRPH>0</resGuestRPH>\r\n         <profileRPHs>0</profileRPHs>\r\n         <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n         <InHouseTimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-12-01T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>2</numberOfTimeUnits>\r\n         </InHouseTimeSpan>\r\n         <ArrivalTransport/>\r\n         <DepartureTransport/>\r\n         <arrivalTime>2016-12-01T00:00:00.000</arrivalTime>\r\n         <departureTime>2016-12-03T00:00:00.000</departureTime>\r\n         <reservationID>946904</reservationID>\r\n         <ReservationReferences>\r\n            <ReservationReference type=\"GUESTID\" referenceNumber=\"946904\" legNumber=\"1\"/>\r\n            <ReservationReference type=\"PMSID\" referenceNumber=\"946904\" legNumber=\"1\"/>\r\n         </ReservationReferences>\r\n         <preRegistered>0</preRegistered>\r\n         <commissionPaidTo>N</commissionPaidTo>\r\n      </ResGuest>\r\n   </ResGuests>\r\n   <ResProfiles>\r\n      <ResProfile>\r\n         <Profile xmlns=\"profile.fidelio.5.0\" profileType=\"GUEST\" gender=\"U\" multiProperty=\"0\" miniProfile=\"0\" pseudoProfile=\"0\" replaceAddress=\"0\">\r\n            <creatorCode>SUPERVISOR</creatorCode>\r\n            <createdDate>2015-07-02T11:18:12.000</createdDate>\r\n            <lastUpdaterCode>SUPERVISOR</lastUpdaterCode>\r\n            <lastUpdated>2015-07-02T11:18:31.000</lastUpdated>\r\n            <genericName>Johnson</genericName>\r\n            <IndividualName>\r\n               <nameFirst>Steve</nameFirst>\r\n               <nameSur>Johnson</nameSur>\r\n            </IndividualName>\r\n            <primaryLanguageID>E</primaryLanguageID>\r\n            <PostalAddresses>\r\n               <PostalAddress addressType=\"HOME\">\r\n                  <countryCode>US</countryCode>\r\n                  <mfPrimaryYN>Y</mfPrimaryYN>\r\n                  <mfAddressLanguage>E</mfAddressLanguage>\r\n                  <cleansed>0</cleansed>\r\n               </PostalAddress>\r\n            </PostalAddresses>\r\n            <mfResort>SANDBOX-OXI3</mfResort>\r\n            <mfResortProfileID>608450</mfResortProfileID>\r\n            <mfAllowMail>YES</mfAllowMail>\r\n            <mfAllowEMail>YES</mfAllowEMail>\r\n            <mfGuestPriv>NO</mfGuestPriv>\r\n            <mfAllowPhone>0</mfAllowPhone>\r\n            <mfAllowSMS>0</mfAllowSMS>\r\n            <SalesExtention/>\r\n            <PrivacyOption>\r\n               <mfAllowMail>Y</mfAllowMail>\r\n               <mfAllowEMail>Y</mfAllowEMail>\r\n               <mfAllowPhone>0</mfAllowPhone>\r\n               <mfAllowSMS>0</mfAllowSMS>\r\n               <mfAllowHistory>1</mfAllowHistory>\r\n               <mfAllowMarketResearch>1</mfAllowMarketResearch>\r\n               <mfAllowThirdParty>1</mfAllowThirdParty>\r\n            </PrivacyOption>\r\n            <ResortList>SANDBOX-OXI3</ResortList>\r\n            <MultiResortEntities>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>CREDIT_CARDS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>RESORT_ARS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>COMMENTS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>PREFERENCES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>NEGOTIATED_RATES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>RESORT_NEG_RATES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>CHANNEL_ACCESS_CODES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>YIELD_ADJUSTMENTS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>RELATIONSHIPS</entity>\r\n               </MultiResortEntity>\r\n            </MultiResortEntities>\r\n         </Profile>\r\n         <resProfileRPH>0</resProfileRPH>\r\n      </ResProfile>\r\n   </ResProfiles>\r\n   <RoomStays>\r\n      <RoomStay mfShareAction=\"NA\" mfReservationAction=\"NA\" reservationActionType=\"NEW\" reservationStatusType=\"RESERVED\">\r\n         <roomInventoryCode>DLX</roomInventoryCode>\r\n         <TimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-12-01T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>2</numberOfTimeUnits>\r\n         </TimeSpan>\r\n         <GuestCounts>\r\n            <GuestCount>\r\n               <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n               <mfCount>2</mfCount>\r\n            </GuestCount>\r\n            <GuestCount>\r\n               <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n               <mfCount>0</mfCount>\r\n            </GuestCount>\r\n         </GuestCounts>\r\n         <RatePlans>\r\n            <RatePlan reservationActionType=\"NEW\">\r\n               <ratePlanRPH>0</ratePlanRPH>\r\n               <ratePlanCode>BAR0</ratePlanCode>\r\n               <TimeSpan timeUnitType=\"DAY\">\r\n                  <startTime>2016-12-01T00:00:00.000</startTime>\r\n                  <numberOfTimeUnits>2</numberOfTimeUnits>\r\n               </TimeSpan>\r\n               <mfMarketCode>LTRAN</mfMarketCode>\r\n               <Rates>\r\n                  <Rate reservationActionType=\"NEW\" rateBasisTimeUnitType=\"DAY\">\r\n                     <rateRPH>0</rateRPH>\r\n                     <Amount currencyCode=\"USD\">\r\n                        <valueNum>325</valueNum>\r\n                     </Amount>\r\n                     <rateBasisUnits>1</rateBasisUnits>\r\n                     <TimeSpan timeUnitType=\"DAY\">\r\n                        <startTime>2016-12-01T00:00:00.000</startTime>\r\n                        <numberOfTimeUnits>2</numberOfTimeUnits>\r\n                     </TimeSpan>\r\n                     <mfAdults>2</mfAdults>\r\n                     <mfChildren>0</mfChildren>\r\n                     <mfCribs>0</mfCribs>\r\n                     <mfExtraBeds>0</mfExtraBeds>\r\n                     <mfsourceCode>WI</mfsourceCode>\r\n                     <mfMarketCode>LTRAN</mfMarketCode>\r\n                  </Rate>\r\n               </Rates>\r\n               <mfsourceCode>WI</mfsourceCode>\r\n            </RatePlan>\r\n         </RatePlans>\r\n         <marketSegmentCode>LTRAN</marketSegmentCode>\r\n         <resGuestRPHs>0</resGuestRPHs>\r\n         <GuaranteeInfo guaranteeType=\"NA\">\r\n            <mfGuaranteeType>6PM</mfGuaranteeType>\r\n            <GuaranteeDeposit>\r\n               <Amount currencyCode=\"USD\"/>\r\n               <cancelIfNotReceived>0</cancelIfNotReceived>\r\n            </GuaranteeDeposit>\r\n         </GuaranteeInfo>\r\n         <CancelPenalties>\r\n            <CancelPenalty mfRuleType=\"CANCEL\">\r\n               <cancelByDate>2016-11-29T12:00:00.000</cancelByDate>\r\n               <Amount currencyCode=\"USD\">\r\n                  <valueNum>338</valueNum>\r\n               </Amount>\r\n               <mfRuleScope>R</mfRuleScope>\r\n               <mfPercentage>50</mfPercentage>\r\n               <mfCancelPercentDue>100</mfCancelPercentDue>\r\n               <mfRuleDescription>Cancel 48 Hours prior to arrival - 50% penalty</mfRuleDescription>\r\n               <ruleCode>48H50P</ruleCode>\r\n            </CancelPenalty>\r\n         </CancelPenalties>\r\n         <PaymentInstructions>\r\n            <PaymentInstruction paymentMethodType=\"NA\">\r\n               <mfPaymentMethod>CA</mfPaymentMethod>\r\n               <PaymentDue>\r\n                  <Amount currencyCode=\"USD\"/>\r\n                  <cancelIfNotReceived>0</cancelIfNotReceived>\r\n               </PaymentDue>\r\n            </PaymentInstruction>\r\n         </PaymentInstructions>\r\n         <mfsourceCode>WI</mfsourceCode>\r\n         <mfchannelCode>PMS</mfchannelCode>\r\n         <mfconfidentialRate>0</mfconfidentialRate>\r\n         <mfAsbProrated>0</mfAsbProrated>\r\n      </RoomStay>\r\n   </RoomStays>\r\n   <resProfileRPHs>0</resProfileRPHs>\r\n   <mfupdateDate>2015-07-02T11:19:13.000</mfupdateDate>\r\n   <mfcomplementary>0</mfcomplementary>\r\n   <mfImage>\r\n      <numRooms>1</numRooms>\r\n      <Describe>\r\n         <resortName>Property 1 Opera Demo Multi Hotel</resortName>\r\n         <insertUser>SUPERVISOR</insertUser>\r\n         <updateUser>SUPERVISOR</updateUser>\r\n         <roomCategory>Standard - These rooms measure 30 square metres (325 square feet).  They include a comfortable chair for relaxation, an antique writing desk and cordless phone.  The marble bathroom offers a large vanity with basin, and combination tub and shower test.</roomCategory>\r\n         <rateCode>Room Charge</rateCode>\r\n         <marketCode>Local Transient</marketCode>\r\n         <guarantee>6PM: 6pm Guarantee</guarantee>\r\n         <company>Micros Hotels and Resorts</company>\r\n      </Describe>\r\n      <Change>\r\n         <bSource>1</bSource>\r\n         <bArrivalDate>1</bArrivalDate>\r\n         <bNumNights>1</bNumNights>\r\n         <bNumAdults>1</bNumAdults>\r\n         <bNumChildren>1</bNumChildren>\r\n         <bNumRooms>1</bNumRooms>\r\n         <bCribs>0</bCribs>\r\n         <bRoomCategory>1</bRoomCategory>\r\n         <bRateCode>1</bRateCode>\r\n         <bRateAmount>1</bRateAmount>\r\n         <bMarketCode>1</bMarketCode>\r\n         <bPaymentType>1</bPaymentType>\r\n         <bGuarType>1</bGuarType>\r\n         <bDiscountReason>0</bDiscountReason>\r\n         <bMultipleRateYN>0</bMultipleRateYN>\r\n         <bResvStatus>1</bResvStatus>\r\n      </Change>\r\n   </mfImage>\r\n</Reservation>\r\n"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/ngipublic/rest/oxi/pms", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["ngipublic", "rest", "oxi", "pms"]}}, "response": []}, {"name": "When EOD for 2016-07-01", "event": [{"listen": "test", "script": {"id": "d36afe48-eda1-4d09-8f00-da2c8df4e3c7", "exec": ["pm.globals.unset(\"fiscal_Date\");", "pm.globals.set(\"fiscal_Date\",\"2016-07-01\");", "", "//verifing status", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{hostName}}:{{ngiPort}}/oxi/admin/pms/endOfDay?propertyCode=OPER1&clientCode=SandBox&fiscalDate=2016-07-01T02:01:01-04:00", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["oxi", "admin", "pms", "endOfDay"], "query": [{"key": "propertyCode", "value": "OPER1"}, {"key": "clientCode", "value": "SandBox"}, {"key": "fiscalDate", "value": "2016-07-01T02:01:01-04:00"}]}}, "response": []}, {"name": "Then unique correlationId populates in nucleusStatisticsCorrelations", "event": [{"listen": "test", "script": {"id": "b173f210-65b1-4089-9970-405d552e8cd1", "exec": ["tv4.validate", "var jsonData = pm.response.json();", "", "function pollDOM () {", "   console.log(\"current status is\" + jsonData.status)", "  if (jsonData.status == \"SENT_FOR_PROCESSING\"|| jsonData.status == \"PROCESSED\") {", "    //Setting global variables", "    pm.globals.unset(\"corrID\");", "    pm.globals.set(\"corrID\",jsonData.id);", "    pm.globals.set(\"JobType\",\"NGIDeferredDeliveryJob\");", "    value = jsonData.activityStats.CURRENT_ROOM_TYPE_MARKET_SEGMENT.correlationId;", "    pm.globals.unset(\"correlationId\");", "    pm.globals.set(\"correlationId\",value);", "    console.log('query fetched from parameter corrID is -'+pm.globals.get(\"corrID\"));", "    //Setting JobID", "    eval(pm.globals.get(\"getJobID\"));", "  } else {", "      pm.sendRequest({url: pm.globals.get('hostName')+':'+pm.globals.get('ngiPort')+'/nucleusStatisticsCorrelations/search/findByClientCodeAndPropertyCodeAndFiscalDate?clientCode='+ pm.globals.get('client_Code')+'&propertyCode='+pm.globals.get('property_Code')+'&fiscalDate='+pm.globals.get('fiscal_Date'),method: 'GET',header:{'Authorization': 'Basic ********************************','Accept': '*/*'}}, function (err, res) {", "            jsonData = res.json();", "            console.log(\"Status Inside: \" + jsonData.status);", "            console.log(\"Response\" + JSON.stringify(res.json()));", "        });", "    jsonData = pm.response.json();", "    console.log(\"Inside Data\" + JSON.stringify(jsonData));", "    setTimeout(pollDOM, 2000); // try again in 300 milliseconds", "  }", "}", "pollDOM();"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "cdb1cd47-b15c-4bff-a7e1-6f5dafcf021e", "exec": ["var millisecondsToWait = 5000;\r", "setTimeout(function() {\r", "    // Whatever you want to do after the wait\r", "}, millisecondsToWait);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusStatisticsCorrelations/search/findByClientCodeAndPropertyCodeAndFiscalDate?clientCode={{client_Code}}&propertyCode={{property_Code}}&fiscalDate={{fiscal_Date}}", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusStatisticsCorrelations", "search", "findByClientCodeAndPropertyCodeAndFiscalDate"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "fiscalDate", "value": "{{fiscal_Date}}"}]}}, "response": []}, {"name": "Then NGIDeferredDelivery Job gets triggered in Monitoring Dashboard", "event": [{"listen": "test", "script": {"id": "d5e50de1-c8af-4904-9be6-f63c5a395fc5", "exec": ["eval(pm.globals.get(\"waitForJobToComplete\"));", "", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "propertyid", "type": "text", "value": "5"}, {"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{ \"query\": \"select EXECUTION_STATUS from [Job].[dbo].JOB_STATE where job_instance_id={{jobId}}\"\n}"}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/G3Tables/executeQuery/v1", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "G3Tables", "execute<PERSON>uery"]}}, "response": []}, {"name": "Then Summary is build for all the Share reservation and sold is updated accordingly", "event": [{"listen": "test", "script": {"id": "017b243b-0797-4c27-b54f-6d949db253f9", "exec": ["//variables", "var jsonData = pm.response.json();", "var totalDocuments =  jsonData.page.totalElements;", "", "", "delete jsonData[\"_links\"];", "delete jsonData[\"page\"];", "", "for (var i=0;i<totalDocuments;i++){", "   ", "//delete jsonData[\"page\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"_links\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"pseudoRoom\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"dayUse\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"statisticsCorrelationId\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"createDate\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"lastModifiedDate\"];", "}", "", "", "//value to compare with", "var reservationJSONToBeCompared = JSON.parse('{\"_embedded\":{\"nucleusOccupancySummaries\":[{\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"reservationId\":\"946904\",\"reservationStatus\":\"RESERVED\",\"occupancyDate\":\"2016-12-03 00:00:00.000\",\"marketSegmentCode\":\"LTRAN\",\"roomTypeCode\":\"DLX\",\"roomNumber\":null,\"rateCode\":\"BAR0\",\"block\":null,\"pickup\":null,\"groupOccupancy\":false,\"roomsSold\":0,\"transientSold\":null,\"groupSold\":null,\"roomRevenue\":0,\"foodRevenue\":0,\"beverageRevenue\":0,\"otherRevenue\":0,\"totalRevenue\":0,\"noShowRoomRevenue\":null,\"arrivals\":0,\"departures\":1,\"cancellations\":0,\"noShows\":0,\"accomCapacity\":null,\"roomsNotAvailableMaintenance\":null,\"roomsNotAvailableOther\":null,\"groupId\":null,\"roomRate\":0,\"correlationId\":null,\"amsInPlay\":false,\"analyticalMarketSegmentCode\":\"LTRAN\",\"groupStatus\":null},{\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"reservationId\":\"946904\",\"reservationStatus\":\"RESERVED\",\"occupancyDate\":\"2016-12-02 00:00:00.000\",\"marketSegmentCode\":\"LTRAN\",\"roomTypeCode\":\"DLX\",\"roomNumber\":null,\"rateCode\":\"BAR0\",\"block\":null,\"pickup\":null,\"groupOccupancy\":false,\"roomsSold\":1,\"transientSold\":null,\"groupSold\":null,\"roomRevenue\":325,\"foodRevenue\":0,\"beverageRevenue\":0,\"otherRevenue\":0,\"totalRevenue\":325,\"noShowRoomRevenue\":null,\"arrivals\":0,\"departures\":0,\"cancellations\":0,\"noShows\":0,\"accomCapacity\":null,\"roomsNotAvailableMaintenance\":null,\"roomsNotAvailableOther\":null,\"groupId\":null,\"roomRate\":325,\"correlationId\":null,\"amsInPlay\":false,\"analyticalMarketSegmentCode\":\"LTRAN\",\"groupStatus\":null},{\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"reservationId\":\"946904\",\"reservationStatus\":\"RESERVED\",\"occupancyDate\":\"2016-12-01 00:00:00.000\",\"marketSegmentCode\":\"LTRAN\",\"roomTypeCode\":\"DLX\",\"roomNumber\":null,\"rateCode\":\"BAR0\",\"block\":null,\"pickup\":null,\"groupOccupancy\":false,\"roomsSold\":1,\"transientSold\":null,\"groupSold\":null,\"roomRevenue\":325,\"foodRevenue\":0,\"beverageRevenue\":0,\"otherRevenue\":0,\"totalRevenue\":325,\"noShowRoomRevenue\":null,\"arrivals\":1,\"departures\":0,\"cancellations\":0,\"noShows\":0,\"accomCapacity\":null,\"roomsNotAvailableMaintenance\":null,\"roomsNotAvailableOther\":null,\"groupId\":null,\"roomRate\":325,\"correlationId\":null,\"amsInPlay\":false,\"analyticalMarketSegmentCode\":\"LTRAN\",\"groupStatus\":null}]}}');", "", "//assertions", "assertJSONData(\"Verifying Client Code in OxiMessage\", jsonData,reservationJSONToBeCompared);", "", "", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue).to.eql(expectedValue);", "});}", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusOccupancySummaries/criteria?search=clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BreservationId%3Din%3D%28946904%29&size=300&sort=occupancyDate,desc", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusOccupancySummaries", "criteria"], "query": [{"key": "search", "value": "clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BreservationId%3Din%3D%28946904%29"}, {"key": "size", "value": "300"}, {"key": "sort", "value": "occupancyDate,desc"}]}}, "response": []}, {"name": "Then NGI updates nucleusRoomTypeMarketSegmentActivity collection", "event": [{"listen": "test", "script": {"id": "a831bd0c-bba2-4f6a-a0ba-1e4a28d88d79", "exec": ["var jsonData = pm.response.json();", "jsonData = jsonData._embedded.nucleusRoomTypeMarketSegmentShardedActivities;", "console.log(JSON.stringify(jsonData));", "", "var nucleusRoomTypeActivityJSON ={\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"roomsSold\":0,\"arrivals\":0,\"departures\":1,\"cancellations\":null,\"noShows\":null,\"roomRevenue\":0,\"foodRevenue\":0,\"totalRevenue\":0,\"occupancyDate\":\"2016-12-03 00:00:00.000\",\"fiscalDate\":\"2016-07-01 00:00:00.000\"", ",\"noShowRoomRevenue\":null,\"marketSegmentCode\":\"LTRAN\",\"roomTypeCode\":\"DLX\",\"provided\":false};", "", "var nucleusRoomTypeActivityJSON1 ={\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"roomsSold\":1,\"arrivals\":0,\"departures\":0,\"cancellations\":null,\"noShows\":null,\"roomRevenue\":325,\"foodRevenue\":0,\"totalRevenue\":325,\"occupancyDate\":\"2016-12-02 00:00:00.000\",\"fiscalDate\":\"2016-07-01 00:00:00.000\",\"noShowRoomRevenue\":null,\"marketSegmentCode\":\"LTRAN\",\"roomTypeCode\":\"DLX\",\"provided\":false};", "", "var nucleusRoomTypeActivityJSON2 ={\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"roomsSold\":1,\"arrivals\":1,\"departures\":0,\"cancellations\":null,\"noShows\":null,\"roomRevenue\":325,\"foodRevenue\":0,\"totalRevenue\":325,\"occupancyDate\":\"2016-12-01 00:00:00.000\",\"fiscalDate\":\"2016-07-01 00:00:00.000\",\"noShowRoomRevenue\":null,\"marketSegmentCode\":\"LTRAN\",\"roomTypeCode\":\"DLX\",\"provided\":false};", "", "arr = [];", "arr.push(nucleusRoomTypeActivityJSON,nucleusRoomTypeActivityJSON1,nucleusRoomTypeActivityJSON2);", "", "//Calling assertions", "for (item of arr) {", "pm.globals.unset(\"actualjson\");", "pm.globals.set(\"actualjson\", jsonData);", "pm.globals.unset(\"expectedjson\");", "pm.globals.set(\"expectedjson\", nucleusRoomTypeActivityJSON);", "eval(pm.globals.get(\"VerifynucleusRTMSActivities\"));", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusRoomTypeMarketSegmentShardedActivities/criteria?search=clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BcorrelationId%3D%3D{{correlationId}}&sort=occupancyDate,desc", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusRoomTypeMarketSegmentShardedActivities", "criteria"], "query": [{"key": "search", "value": "clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BcorrelationId%3D%3D{{correlationId}}"}, {"key": "sort", "value": "occupancyDate,desc"}]}}, "response": []}, {"name": "When a sharer is added to reservationId: 946904", "event": [{"listen": "prerequest", "script": {"id": "42c316d6-02a5-4950-a593-9243343deac6", "exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"id": "8354c5b2-1178-4f79-b9d4-eecd9aeeb1a5", "exec": ["//verifing status", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "text/xml"}], "body": {"mode": "raw", "raw": "<?xml version = '1.0' encoding = 'UTF-8'?>\r\n<?Label SandBox-OPER1|RESERVATION|20151106002|SUCCESS?>\r\n<!--Reservation_ShareBaseCase_Msg2: Guest added to reservation as sharer for same stay\r\nIn his message: Reservation 946904 for 2 nights, 3 adults at $340 (No changes to RoomStay other than Triple Occupancy rate applies) \r\nNotes:\r\nNew ResGuest added with reservation id 946905;\r\n/Reservation/RoomStays/RoomStay/resGuestRPHs = 1 pointing to guest with reservation 946904 (primary)\r\n/Reservation/RoomStays/RoomStay/mfsharedGuestRPHs = 0 indicating 946905 is sharing guest\r\n/Reservation/RoomStays/RoomStay/mfshareCode = 1169037 so a share id has been assigned -->\r\n<Reservation xmlns=\"reservation.fidelio.5.0\" mfShareAction=\"NA\" mfReservationAction=\"EDIT\">\r\n   <HotelReference>\r\n      <hotelCode>SandBox-OPER1</hotelCode>\r\n   </HotelReference>\r\n   <reservationID>946904</reservationID>\r\n   <reservationOriginatorCode>SUPERVISOR</reservationOriginatorCode>\r\n   <originalBookingDate>2015-07-02T11:18:31.000</originalBookingDate>\r\n   <StayDateRange timeUnitType=\"DAY\">\r\n      <startTime>2016-12-01T00:00:00.000</startTime>\r\n      <numberOfTimeUnits>2</numberOfTimeUnits>\r\n   </StayDateRange>\r\n   <GuestCounts>\r\n      <GuestCount>\r\n         <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n         <mfCount>3</mfCount>\r\n      </GuestCount>\r\n      <GuestCount>\r\n         <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n         <mfCount>0</mfCount>\r\n      </GuestCount>\r\n   </GuestCounts>\r\n   <ResGuests>\r\n      <ResGuest reservationActionType=\"CHANGE\">\r\n         <resGuestRPH>0</resGuestRPH>\r\n         <profileRPHs>0</profileRPHs>\r\n         <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n         <InHouseTimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-12-01T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>2</numberOfTimeUnits>\r\n         </InHouseTimeSpan>\r\n         <ArrivalTransport/>\r\n         <DepartureTransport/>\r\n         <arrivalTime>2016-12-01T00:00:00.000</arrivalTime>\r\n         <departureTime>2016-12-03T00:00:00.000</departureTime>\r\n         <mfCRSShareID>946905</mfCRSShareID>\r\n         <reservationID>946905</reservationID>\r\n         <ReservationReferences>\r\n            <ReservationReference type=\"GUESTID\" referenceNumber=\"946905\" legNumber=\"1\"/>\r\n            <ReservationReference type=\"PMSID\" referenceNumber=\"946905\" legNumber=\"1\"/>\r\n         </ReservationReferences>\r\n         <preRegistered>0</preRegistered>\r\n         <commissionPaidTo>N</commissionPaidTo>\r\n      </ResGuest>\r\n      <ResGuest reservationActionType=\"CHANGE\">\r\n         <resGuestRPH>1</resGuestRPH>\r\n         <profileRPHs>1</profileRPHs>\r\n         <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n         <InHouseTimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-12-01T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>2</numberOfTimeUnits>\r\n         </InHouseTimeSpan>\r\n         <ArrivalTransport/>\r\n         <DepartureTransport/>\r\n         <arrivalTime>2016-12-01T00:00:00.000</arrivalTime>\r\n         <departureTime>2016-12-03T00:00:00.000</departureTime>\r\n         <mfCRSShareID>946904</mfCRSShareID>\r\n         <reservationID>946904</reservationID>\r\n         <ReservationReferences>\r\n            <ReservationReference type=\"GUESTID\" referenceNumber=\"946904\" legNumber=\"1\"/>\r\n            <ReservationReference type=\"PMSID\" referenceNumber=\"946904\" legNumber=\"1\"/>\r\n         </ReservationReferences>\r\n         <preRegistered>0</preRegistered>\r\n         <commissionPaidTo>N</commissionPaidTo>\r\n      </ResGuest>\r\n   </ResGuests>\r\n   <ResProfiles>\r\n      <ResProfile>\r\n         <Profile xmlns=\"profile.fidelio.5.0\" profileType=\"GUEST\" gender=\"M\" multiProperty=\"0\" miniProfile=\"0\" pseudoProfile=\"0\" replaceAddress=\"0\">\r\n            <creatorCode>OEDS$ADS</creatorCode>\r\n            <createdDate>2009-11-11T17:16:27.000</createdDate>\r\n            <lastUpdaterCode>SUPERVISOR</lastUpdaterCode>\r\n            <lastUpdated>2015-06-04T15:52:56.000</lastUpdated>\r\n            <genericName>Smith</genericName>\r\n            <IndividualName>\r\n               <namePrefix>Mr</namePrefix>\r\n               <nameFirst>Abby</nameFirst>\r\n               <nameSur>Smith</nameSur>\r\n               <nameSuffix>12</nameSuffix>\r\n            </IndividualName>\r\n            <primaryLanguageID>E</primaryLanguageID>\r\n            <Memberships>\r\n               <Membership>\r\n                  <programCode>AA</programCode>\r\n                  <accountID>56478</accountID>\r\n                  <startDate>2014-06-13</startDate>\r\n                  <nameOnCard>ABBY SMITH</nameOnCard>\r\n                  <displaySequence>100</displaySequence>\r\n                  <pointIndicator>0</pointIndicator>\r\n                  <enrollmentSource>N</enrollmentSource>\r\n                  <enrolledAt>N</enrolledAt>\r\n               </Membership>\r\n               <Membership>\r\n                  <programCode>OCIS</programCode>\r\n                  <accountID>987654</accountID>\r\n                  <levelCode>BRON</levelCode>\r\n                  <expireDate>2015-06-30</expireDate>\r\n                  <startDate>2014-06-13</startDate>\r\n                  <nameOnCard>ABBY SMITH</nameOnCard>\r\n                  <displaySequence>50</displaySequence>\r\n                  <enrollmentCode>HOT</enrollmentCode>\r\n                  <pointIndicator>1</pointIndicator>\r\n                  <enrollmentSource>N</enrollmentSource>\r\n                  <enrolledAt>N</enrolledAt>\r\n               </Membership>\r\n               <Membership>\r\n                  <programCode>OR</programCode>\r\n                  <accountID>741852</accountID>\r\n                  <startDate>2014-06-13</startDate>\r\n                  <nameOnCard>ABBY SMITH</nameOnCard>\r\n                  <displaySequence>75</displaySequence>\r\n                  <pointIndicator>0</pointIndicator>\r\n                  <enrollmentSource>N</enrollmentSource>\r\n                  <enrolledAt>N</enrolledAt>\r\n               </Membership>\r\n            </Memberships>\r\n            <PostalAddresses>\r\n               <PostalAddress addressType=\"HOME\">\r\n                  <address1>67 Westwood Parkway5</address1>\r\n                  <city>Lynnwood</city>\r\n                  <stateCode>WA</stateCode>\r\n                  <postalCode>98037</postalCode>\r\n                  <countryCode>US</countryCode>\r\n                  <mfPrimaryYN>Y</mfPrimaryYN>\r\n                  <mfAddressLanguage>E</mfAddressLanguage>\r\n                  <cleansed>0</cleansed>\r\n               </PostalAddress>\r\n            </PostalAddresses>\r\n            <PhoneNumbers>\r\n               <PhoneNumber phoneNumberType=\"HOME\">\r\n                  <phoneNumber>(123)456-7890</phoneNumber>\r\n                  <mfPrimaryYN>Y</mfPrimaryYN>\r\n                  <confirmation>0</confirmation>\r\n               </PhoneNumber>\r\n               <PhoneNumber phoneNumberType=\"BUSINESS\">\r\n                  <phoneNumber>************</phoneNumber>\r\n                  <mfPrimaryYN>N</mfPrimaryYN>\r\n                  <confirmation>0</confirmation>\r\n               </PhoneNumber>\r\n            </PhoneNumbers>\r\n            <SpecialRequests>\r\n               <SpecialRequest mfSpecialRequestType=\"SPE\">\r\n                  <requestCode>HOTSOS</requestCode>\r\n                  <requestComments>HotSOS Test</requestComments>\r\n                  <mfResort>SANDBOX-OXI3</mfResort>\r\n               </SpecialRequest>\r\n            </SpecialRequests>\r\n            <mfResort>SANDBOX-OXI3</mfResort>\r\n            <mfResortProfileID>83830</mfResortProfileID>\r\n            <mfAllowMail>YES</mfAllowMail>\r\n            <mfAllowEMail>NO</mfAllowEMail>\r\n            <mfGuestPriv>NO</mfGuestPriv>\r\n            <mfAllowPhone>0</mfAllowPhone>\r\n            <mfAllowSMS>0</mfAllowSMS>\r\n            <SalesExtention/>\r\n            <PrivacyOption>\r\n               <mfAllowMail>Y</mfAllowMail>\r\n               <mfAllowEMail>N</mfAllowEMail>\r\n               <mfAllowPhone>0</mfAllowPhone>\r\n               <mfAllowSMS>0</mfAllowSMS>\r\n               <mfAllowHistory>1</mfAllowHistory>\r\n               <mfAllowMarketResearch>1</mfAllowMarketResearch>\r\n               <mfAllowThirdParty>1</mfAllowThirdParty>\r\n            </PrivacyOption>\r\n            <ResortList>SANDBOX-OXI3</ResortList>\r\n            <MultiResortEntities>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>CREDIT_CARDS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>RESORT_ARS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>COMMENTS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>PREFERENCES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>NEGOTIATED_RATES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>RESORT_NEG_RATES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>CHANNEL_ACCESS_CODES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>YIELD_ADJUSTMENTS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>RELATIONSHIPS</entity>\r\n               </MultiResortEntity>\r\n            </MultiResortEntities>\r\n            <ResortSpecialRequests>\r\n               <SpecialRequest mfSpecialRequestType=\"SPE\">\r\n                  <requestCode>HOTSOS</requestCode>\r\n                  <requestComments>HotSOS Test</requestComments>\r\n                  <mfResort>SANDBOX-OXI3</mfResort>\r\n               </SpecialRequest>\r\n            </ResortSpecialRequests>\r\n         </Profile>\r\n         <resProfileRPH>0</resProfileRPH>\r\n      </ResProfile>\r\n      <ResProfile>\r\n         <Profile xmlns=\"profile.fidelio.5.0\" profileType=\"GUEST\" gender=\"U\" multiProperty=\"0\" miniProfile=\"0\" pseudoProfile=\"0\" replaceAddress=\"0\">\r\n            <creatorCode>SUPERVISOR</creatorCode>\r\n            <createdDate>2015-07-02T11:18:12.000</createdDate>\r\n            <lastUpdaterCode>SUPERVISOR</lastUpdaterCode>\r\n            <lastUpdated>2015-07-02T11:18:31.000</lastUpdated>\r\n            <genericName>Johnson</genericName>\r\n            <IndividualName>\r\n               <nameFirst>Steve</nameFirst>\r\n               <nameSur>Johnson</nameSur>\r\n            </IndividualName>\r\n            <primaryLanguageID>E</primaryLanguageID>\r\n            <PostalAddresses>\r\n               <PostalAddress addressType=\"HOME\">\r\n                  <countryCode>US</countryCode>\r\n                  <mfPrimaryYN>Y</mfPrimaryYN>\r\n                  <mfAddressLanguage>E</mfAddressLanguage>\r\n                  <cleansed>0</cleansed>\r\n               </PostalAddress>\r\n            </PostalAddresses>\r\n            <mfResort>SANDBOX-OXI3</mfResort>\r\n            <mfResortProfileID>608450</mfResortProfileID>\r\n            <mfAllowMail>YES</mfAllowMail>\r\n            <mfAllowEMail>YES</mfAllowEMail>\r\n            <mfGuestPriv>NO</mfGuestPriv>\r\n            <mfAllowPhone>0</mfAllowPhone>\r\n            <mfAllowSMS>0</mfAllowSMS>\r\n            <SalesExtention/>\r\n            <PrivacyOption>\r\n               <mfAllowMail>Y</mfAllowMail>\r\n               <mfAllowEMail>Y</mfAllowEMail>\r\n               <mfAllowPhone>0</mfAllowPhone>\r\n               <mfAllowSMS>0</mfAllowSMS>\r\n               <mfAllowHistory>1</mfAllowHistory>\r\n               <mfAllowMarketResearch>1</mfAllowMarketResearch>\r\n               <mfAllowThirdParty>1</mfAllowThirdParty>\r\n            </PrivacyOption>\r\n            <ResortList>SANDBOX-OXI3</ResortList>\r\n            <MultiResortEntities>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>CREDIT_CARDS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>RESORT_ARS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>COMMENTS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>PREFERENCES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>NEGOTIATED_RATES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>RESORT_NEG_RATES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>CHANNEL_ACCESS_CODES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>YIELD_ADJUSTMENTS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>RELATIONSHIPS</entity>\r\n               </MultiResortEntity>\r\n            </MultiResortEntities>\r\n         </Profile>\r\n         <resProfileRPH>1</resProfileRPH>\r\n      </ResProfile>\r\n   </ResProfiles>\r\n   <RoomStays>\r\n      <RoomStay mfShareAction=\"NA\" mfReservationAction=\"NA\" reservationActionType=\"CHANGE\" reservationStatusType=\"RESERVED\">\r\n         <roomInventoryCode>DLX</roomInventoryCode>\r\n         <TimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-12-01T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>2</numberOfTimeUnits>\r\n         </TimeSpan>\r\n         <GuestCounts>\r\n            <GuestCount>\r\n               <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n               <mfCount>3</mfCount>\r\n            </GuestCount>\r\n            <GuestCount>\r\n               <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n               <mfCount>0</mfCount>\r\n            </GuestCount>\r\n         </GuestCounts>\r\n         <RatePlans>\r\n            <RatePlan reservationActionType=\"CHANGE\">\r\n               <ratePlanRPH>0</ratePlanRPH>\r\n               <ratePlanCode>BAR0</ratePlanCode>\r\n               <TimeSpan timeUnitType=\"DAY\">\r\n                  <startTime>2016-12-01T00:00:00.000</startTime>\r\n                  <numberOfTimeUnits>2</numberOfTimeUnits>\r\n               </TimeSpan>\r\n               <mfMarketCode>LTRAN</mfMarketCode>\r\n               <Rates>\r\n                  <Rate reservationActionType=\"CHANGE\" rateBasisTimeUnitType=\"DAY\">\r\n                     <rateRPH>0</rateRPH>\r\n                     <Amount currencyCode=\"USD\">\r\n                        <valueNum>340</valueNum>\r\n                     </Amount>\r\n                     <rateBasisUnits>1</rateBasisUnits>\r\n                     <TimeSpan timeUnitType=\"DAY\">\r\n                        <startTime>2016-12-01T00:00:00.000</startTime>\r\n                        <numberOfTimeUnits>2</numberOfTimeUnits>\r\n                     </TimeSpan>\r\n                     <mfAdults>3</mfAdults>\r\n                     <mfChildren>0</mfChildren>\r\n                     <mfCribs>0</mfCribs>\r\n                     <mfExtraBeds>0</mfExtraBeds>\r\n                     <mfsourceCode>WI</mfsourceCode>\r\n                     <mfMarketCode>LTRAN</mfMarketCode>\r\n                  </Rate>\r\n               </Rates>\r\n               <mfsourceCode>WI</mfsourceCode>\r\n            </RatePlan>\r\n         </RatePlans>\r\n         <marketSegmentCode>LTRAN</marketSegmentCode>\r\n         <resGuestRPHs>1</resGuestRPHs>\r\n         <GuaranteeInfo guaranteeType=\"NA\">\r\n            <mfGuaranteeType>6PM</mfGuaranteeType>\r\n            <GuaranteeDeposit>\r\n               <Amount currencyCode=\"USD\"/>\r\n               <cancelIfNotReceived>0</cancelIfNotReceived>\r\n            </GuaranteeDeposit>\r\n         </GuaranteeInfo>\r\n         <CancelPenalties>\r\n            <CancelPenalty mfRuleType=\"CANCEL\">\r\n               <cancelByDate>2016-11-29T12:00:00.000</cancelByDate>\r\n               <Amount currencyCode=\"USD\">\r\n                  <valueNum>353.6</valueNum>\r\n               </Amount>\r\n               <mfRuleScope>R</mfRuleScope>\r\n               <mfPercentage>50</mfPercentage>\r\n               <mfCancelPercentDue>100</mfCancelPercentDue>\r\n               <mfRuleDescription>Cancel 48 Hours prior to arrival - 50% penalty</mfRuleDescription>\r\n               <ruleCode>48H50P</ruleCode>\r\n            </CancelPenalty>\r\n         </CancelPenalties>\r\n         <PaymentInstructions>\r\n            <PaymentInstruction paymentMethodType=\"NA\">\r\n               <mfPaymentMethod>CA</mfPaymentMethod>\r\n               <PaymentDue>\r\n                  <Amount currencyCode=\"USD\"/>\r\n                  <cancelIfNotReceived>0</cancelIfNotReceived>\r\n               </PaymentDue>\r\n            </PaymentInstruction>\r\n         </PaymentInstructions>\r\n         <mfsourceCode>WI</mfsourceCode>\r\n         <mfshareCode>1169037</mfshareCode>\r\n         <mfchannelCode>PMS</mfchannelCode>\r\n         <mfconfidentialRate>0</mfconfidentialRate>\r\n         <mfsharedGuestRPHs>0</mfsharedGuestRPHs>\r\n         <mfAsbProrated>0</mfAsbProrated>\r\n      </RoomStay>\r\n   </RoomStays>\r\n   <resProfileRPHs>0, 1</resProfileRPHs>\r\n   <mfupdateDate>2015-07-02T11:20:10.000</mfupdateDate>\r\n   <mfcomplementary>0</mfcomplementary>\r\n   <mfImage>\r\n      <numRooms>1</numRooms>\r\n      <Describe>\r\n         <resortName>Property 1 Opera Demo Multi Hotel</resortName>\r\n         <insertUser>SUPERVISOR</insertUser>\r\n         <updateUser>SUPERVISOR</updateUser>\r\n         <roomCategory>Standard - These rooms measure 30 square metres (325 square feet).  They include a comfortable chair for relaxation, an antique writing desk and cordless phone.  The marble bathroom offers a large vanity with basin, and combination tub and shower test.</roomCategory>\r\n         <rateCode>Room Charge</rateCode>\r\n         <marketCode>Local Transient</marketCode>\r\n         <guarantee>6PM: 6pm Guarantee</guarantee>\r\n         <company>Micros Hotels and Resorts</company>\r\n      </Describe>\r\n      <Change>\r\n         <bSource>0</bSource>\r\n         <bArrivalDate>0</bArrivalDate>\r\n         <bNumNights>0</bNumNights>\r\n         <bNumAdults>0</bNumAdults>\r\n         <bNumChildren>0</bNumChildren>\r\n         <bNumRooms>0</bNumRooms>\r\n         <bCribs>0</bCribs>\r\n         <bRoomCategory>0</bRoomCategory>\r\n         <bRateCode>0</bRateCode>\r\n         <bRateAmount>0</bRateAmount>\r\n         <bMarketCode>0</bMarketCode>\r\n         <bPaymentType>0</bPaymentType>\r\n         <bGuarType>0</bGuarType>\r\n         <bDiscountReason>0</bDiscountReason>\r\n         <bMultipleRateYN>0</bMultipleRateYN>\r\n         <bResvStatus>0</bResvStatus>\r\n      </Change>\r\n   </mfImage>\r\n</Reservation>\r\n"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/ngipublic/rest/oxi/pms", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["ngipublic", "rest", "oxi", "pms"]}}, "response": []}, {"name": "When EOD for 2016-07-02", "event": [{"listen": "test", "script": {"id": "652948f9-5ea8-4322-8af6-af84db2ce4bb", "exec": ["pm.globals.unset(\"fiscal_Date\");", "pm.globals.set(\"fiscal_Date\",\"2016-07-02\");", "", "//verifing status", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{hostName}}:{{ngiPort}}/oxi/admin/pms/endOfDay?propertyCode=OPER1&clientCode=SandBox&fiscalDate=2016-07-02T02:01:01-04:00", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["oxi", "admin", "pms", "endOfDay"], "query": [{"key": "propertyCode", "value": "OPER1"}, {"key": "clientCode", "value": "SandBox"}, {"key": "fiscalDate", "value": "2016-07-02T02:01:01-04:00"}]}}, "response": []}, {"name": "Then unique correlationId populates in nucleusStatisticsCorrelations", "event": [{"listen": "test", "script": {"id": "f144c45f-12da-46bc-b8a3-2e5aba6f8ab5", "exec": ["tv4.validate", "var jsonData = pm.response.json();", "", "function pollDOM () {", "   console.log(\"current status is\" + jsonData.status)", "  if (jsonData.status == \"SENT_FOR_PROCESSING\"|| jsonData.status == \"PROCESSED\") {", "    //Setting global variables", "    pm.globals.unset(\"corrID\");", "    pm.globals.set(\"corrID\",jsonData.id);", "    pm.globals.set(\"JobType\",\"NGIDeferredDeliveryJob\");", "    value = jsonData.activityStats.CURRENT_ROOM_TYPE_MARKET_SEGMENT.correlationId;", "    pm.globals.unset(\"correlationId\");", "    pm.globals.set(\"correlationId\",value);", "    console.log('query fetched from parameter corrID is -'+pm.globals.get(\"corrID\"));", "    //Setting JobID", "    eval(pm.globals.get(\"getJobID\"));", "  } else {", "      pm.sendRequest({url: pm.globals.get('hostName')+':'+pm.globals.get('ngiPort')+'/nucleusStatisticsCorrelations/search/findByClientCodeAndPropertyCodeAndFiscalDate?clientCode='+ pm.globals.get('client_Code')+'&propertyCode='+pm.globals.get('property_Code')+'&fiscalDate='+pm.globals.get('fiscal_Date'),method: 'GET',header:{'Authorization': 'Basic ********************************','Accept': '*/*'}}, function (err, res) {", "            jsonData = res.json();", "            console.log(\"Status Inside: \" + jsonData.status);", "            console.log(\"Response\" + JSON.stringify(res.json()));", "        });", "    jsonData = pm.response.json();", "    console.log(\"Inside Data\" + JSON.stringify(jsonData));", "    setTimeout(pollDOM, 2000); // try again in 300 milliseconds", "  }", "}", "pollDOM();"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "8a0eea3c-545a-4cd2-a81e-9e741b307ffa", "exec": ["var millisecondsToWait = 5000;\r", "setTimeout(function() {\r", "    // Whatever you want to do after the wait\r", "}, millisecondsToWait);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusStatisticsCorrelations/search/findByClientCodeAndPropertyCodeAndFiscalDate?clientCode={{client_Code}}&propertyCode={{property_Code}}&fiscalDate={{fiscal_Date}}", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusStatisticsCorrelations", "search", "findByClientCodeAndPropertyCodeAndFiscalDate"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "fiscalDate", "value": "{{fiscal_Date}}"}]}}, "response": []}, {"name": "Then NGIDeferredDelivery Job gets triggered in Monitoring Dashboard", "event": [{"listen": "test", "script": {"id": "d0814bef-dedd-4231-a05b-dc3fd8ede454", "exec": ["eval(pm.globals.get(\"waitForJobToComplete\"));", "", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "propertyid", "type": "text", "value": "5"}, {"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{ \"query\": \"select EXECUTION_STATUS from [Job].[dbo].JOB_STATE where job_instance_id={{jobId}}\"\n}"}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/G3Tables/executeQuery/v1", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "G3Tables", "execute<PERSON>uery"]}}, "response": []}, {"name": "Then the mapping of reservation is also stored in oxiShareReservationReferences Collection", "event": [{"listen": "test", "script": {"id": "86f76563-054c-4571-8eee-e14e8a922fda", "exec": ["//variables", "var jsonData = pm.response.json();", "", "", "//value to compare with", "var reservationJSONToBeCompared = {\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"reservationId\":\"946904\",\"primaryReservationId\":\"946904\",\"shareCode\":\"1169037\",\"sharers\":[{\"reservationId\":\"946905\",\"shareParent\":false,\"startedOn\":\"2015-07-02T16:20:10Z\",\"endedOn\":null,\"arrivalDate\":\"2016-12-01\",\"departureDate\":\"2016-12-03\",\"revenue\":null,\"participating\":true}]};", "", "var reservationJSONToBeCompared1 = {\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"reservationId\":\"946904\",\"primaryReservationId\":\"946904\",\"shareCode\":\"1169037\",\"sharers\":[{\"reservationId\":\"946904\",\"shareParent\":true,\"startedOn\":\"2015-07-02T16:20:10Z\",\"endedOn\":null,\"arrivalDate\":\"2016-12-01\",\"departureDate\":\"2016-12-03\",\"revenue\":null,\"participating\":true}]};", "", "//assertions", "arr = [];", "arr.push(reservationJSONToBeCompared,reservationJSONToBeCompared1);", "", "//Calling Comparisons", "for (item of arr) {", "     pm.globals.unset(\"actualjson\");", "    pm.globals.set(\"actualjson\", jsonData);", "    pm.globals.unset(\"expectedjson\");", "    pm.globals.set(\"expectedjson\", item);", "    eval(pm.globals.get(\"verifySharesReservationCollection\"));", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/oxiShareReservationReferences/search/findOneByClientCodeAndPropertyCodeAndReservationId?clientCode={{client_Code}}&propertyCode={{property_Code}}&reservationId=946904", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["oxiShareReservationReferences", "search", "findOneByClientCodeAndPropertyCodeAndReservationId"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "reservationId", "value": "946904"}]}}, "response": []}, {"name": "Then Summary is build for all the share reservation and sold is updated accordingly", "event": [{"listen": "test", "script": {"id": "8a0c7e40-9c26-48c8-8217-3278d0c8e3ae", "exec": ["//variables", "var jsonData = pm.response.json();", "var totalDocuments =  jsonData.page.totalElements;", "", "", "delete jsonData[\"_links\"];", "delete jsonData[\"page\"];", "", "for (var i=0;i<totalDocuments;i++){", "   ", "//delete jsonData[\"page\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"_links\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"pseudoRoom\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"dayUse\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"statisticsCorrelationId\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"createDate\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"lastModifiedDate\"];", "}", "", "", "//value to compare with", "var reservationJSONToBeCompared = JSON.parse('{\"_embedded\":{\"nucleusOccupancySummaries\":[{\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"reservationId\":\"946904\",\"reservationStatus\":\"RESERVED\",\"occupancyDate\":\"2016-12-03 00:00:00.000\",\"marketSegmentCode\":\"LTRAN\",\"roomTypeCode\":\"DLX\",\"roomNumber\":null,\"rateCode\":\"BAR0\",\"block\":null,\"pickup\":null,\"groupOccupancy\":false,\"roomsSold\":0,\"transientSold\":null,\"groupSold\":null,\"roomRevenue\":0,\"foodRevenue\":0,\"beverageRevenue\":0,\"otherRevenue\":0,\"totalRevenue\":0,\"noShowRoomRevenue\":null,\"arrivals\":0,\"departures\":1,\"cancellations\":0,\"noShows\":0,\"accomCapacity\":null,\"roomsNotAvailableMaintenance\":null,\"roomsNotAvailableOther\":null,\"groupId\":null,\"roomRate\":0,\"correlationId\":null,\"amsInPlay\":false,\"analyticalMarketSegmentCode\":\"LTRAN\",\"groupStatus\":null},{\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"reservationId\":\"946904\",\"reservationStatus\":\"RESERVED\",\"occupancyDate\":\"2016-12-02 00:00:00.000\",\"marketSegmentCode\":\"LTRAN\",\"roomTypeCode\":\"DLX\",\"roomNumber\":null,\"rateCode\":\"BAR0\",\"block\":null,\"pickup\":null,\"groupOccupancy\":false,\"roomsSold\":1,\"transientSold\":null,\"groupSold\":null,\"roomRevenue\":340,\"foodRevenue\":0,\"beverageRevenue\":0,\"otherRevenue\":0,\"totalRevenue\":340,\"noShowRoomRevenue\":null,\"arrivals\":0,\"departures\":0,\"cancellations\":0,\"noShows\":0,\"accomCapacity\":null,\"roomsNotAvailableMaintenance\":null,\"roomsNotAvailableOther\":null,\"groupId\":null,\"roomRate\":340,\"correlationId\":null,\"amsInPlay\":false,\"analyticalMarketSegmentCode\":\"LTRAN\",\"groupStatus\":null},{\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"reservationId\":\"946904\",\"reservationStatus\":\"RESERVED\",\"occupancyDate\":\"2016-12-01 00:00:00.000\",\"marketSegmentCode\":\"LTRAN\",\"roomTypeCode\":\"DLX\",\"roomNumber\":null,\"rateCode\":\"BAR0\",\"block\":null,\"pickup\":null,\"groupOccupancy\":false,\"roomsSold\":1,\"transientSold\":null,\"groupSold\":null,\"roomRevenue\":340,\"foodRevenue\":0,\"beverageRevenue\":0,\"otherRevenue\":0,\"totalRevenue\":340,\"noShowRoomRevenue\":null,\"arrivals\":1,\"departures\":0,\"cancellations\":0,\"noShows\":0,\"accomCapacity\":null,\"roomsNotAvailableMaintenance\":null,\"roomsNotAvailableOther\":null,\"groupId\":null,\"roomRate\":340,\"correlationId\":null,\"amsInPlay\":false,\"analyticalMarketSegmentCode\":\"LTRAN\",\"groupStatus\":null}]}}');", "", "//assertions", "assertJSONData(\"Verifying Client Code in OxiMessage\", jsonData,reservationJSONToBeCompared);", "", "", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue).to.eql(expectedValue);", "});}", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusOccupancySummaries/criteria?search=clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BreservationId%3Din%3D%28946904%29&size=300&sort=occupancyDate,desc", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusOccupancySummaries", "criteria"], "query": [{"key": "search", "value": "clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BreservationId%3Din%3D%28946904%29"}, {"key": "size", "value": "300"}, {"key": "sort", "value": "occupancyDate,desc"}]}}, "response": []}, {"name": "Then NGI updates nucleusRoomTypeMarketSegmentActivity collection", "event": [{"listen": "test", "script": {"id": "fa8c9eae-f07a-4263-9c27-5797a8935d11", "exec": ["var jsonData = pm.response.json();", "jsonData = jsonData._embedded.nucleusRoomTypeMarketSegmentShardedActivities;", "", "console.log(jsonData);", "", "var nucleusRoomTypeActivityJSON = {\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"roomsSold\":0,\"arrivals\":0,\"departures\":1,\"cancellations\":null,\"noShows\":null,\"roomRevenue\":0,\"foodRevenue\":0,\"totalRevenue\":0,\"occupancyDate\":\"2016-12-03 00:00:00.000\",\"fiscalDate\":\"2016-07-02 00:00:00.000\",\"correlationId\":\"90601050-2607-49bc-a767-5fa8aaaf75a7\",\"noShowRoomRevenue\":null,\"marketSegmentCode\":\"LTRAN\",\"roomTypeCode\":\"DLX\",\"provided\":false};", "var nucleusRoomTypeActivityJSON1 = {\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"roomsSold\":1,\"arrivals\":0,\"departures\":0,\"cancellations\":null,\"noShows\":null,\"roomRevenue\":340,\"foodRevenue\":0,\"totalRevenue\":340,\"occupancyDate\":\"2016-12-02 00:00:00.000\",\"fiscalDate\":\"2016-07-02 00:00:00.000\",\"correlationId\":\"90601050-2607-49bc-a767-5fa8aaaf75a7\",\"noShowRoomRevenue\":null,\"marketSegmentCode\":\"LTRAN\",\"roomTypeCode\":\"DLX\",\"provided\":false};", "var nucleusRoomTypeActivityJSON2 = {\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"roomsSold\":1,\"arrivals\":1,\"departures\":0,\"cancellations\":null,\"noShows\":null,\"roomRevenue\":340,\"foodRevenue\":0,\"totalRevenue\":340,\"occupancyDate\":\"2016-12-01 00:00:00.000\",\"fiscalDate\":\"2016-07-02 00:00:00.000\",\"noShowRoomRevenue\":null,\"marketSegmentCode\":\"LTRAN\",\"roomTypeCode\":\"DLX\",\"provided\":false};", "", "arr = [];", "arr.push(nucleusRoomTypeActivityJSON,nucleusRoomTypeActivityJSON1,nucleusRoomTypeActivityJSON2);", "", "//Calling assertions", "for (item of arr) {", "pm.globals.unset(\"actualjson\");", "pm.globals.set(\"actualjson\", jsonData);", "pm.globals.unset(\"expectedjson\");", "pm.globals.set(\"expectedjson\", nucleusRoomTypeActivityJSON);", "eval(pm.globals.get(\"VerifynucleusRTMSActivities\"));", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusRoomTypeMarketSegmentShardedActivities/criteria?search=clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BcorrelationId%3D%3D{{correlationId}}&sort=occupancyDate,desc", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusRoomTypeMarketSegmentShardedActivities", "criteria"], "query": [{"key": "search", "value": "clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BcorrelationId%3D%3D{{correlationId}}"}, {"key": "sort", "value": "occupancyDate,desc"}]}}, "response": []}, {"name": "Then the individualTrans Table in G3 gets updated", "event": [{"listen": "test", "script": {"id": "b00fd017-cf65-4f2b-acde-f9a57d06d5a0", "exec": ["//variables", "var jsonData = pm.response.json();", "", "pm.globals.set(\"File_Metadata_ID\",jsonData[0][0]);", "", "var individualTransData = ('[' + pm.globals.get('File_Metadata_ID') + ',\"946904\",\"SS\",\"2016-12-01\",\"2016-12-03\",\"2015-07-02\",null,\"DLX\",\"LTRAN\",\"DLX\",680,null,null,null,null,680,\"WI\",\"US\",\"BAR0\",340,null,\"6PM\",0,3,null,\"PMS\"]');", "", "", "arr = [];", "arr.push(individualTransData);", "", "//Calling assertions", "for (item of arr) {", "    pm.globals.unset(\"actualjson\");", "    pm.globals.set(\"actualjson\", jsonData);", "    pm.globals.unset(\"expectedjson\");", "    pm.globals.set(\"expectedjson\", item);", "    eval(pm.globals.get(\"VerifyG3TableValue\"));", "}"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "aedfc397-23c9-4c85-b020-299a9fe167ba", "exec": ["pm.globals.unset(\"whereCondition\");\r", "pm.globals.set(\"whereCondition\", \" where reservation_identifier in ('946904','946905') \");\r", "\r", "var millisecondsToWait = 5000;\r", "setTimeout(function() {\r", "    // Whatever you want to do after the wait\r", "}, millisecondsToWait);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "propertyId", "type": "text", "value": "5"}], "body": {"mode": "raw", "raw": "{ \"query\": \"{{individualTransQuery}}\"\n}"}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/G3Tables/executeQuery/v1", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "G3Tables", "execute<PERSON>uery"]}}, "response": []}, {"name": "Then the Reservation_Night Table in G3 gets populated", "event": [{"listen": "test", "script": {"id": "22881b50-788f-43f7-b2c1-3a8c0ee3f457", "exec": ["//variables", "var jsonData = pm.response.json();", "pm.globals.set(\"File_Metadata_ID\",jsonData[0][0]);", "", "var reservationNightData = ('[' + pm.globals.get('File_Metadata_ID') + ',\"946904\",\"RESERVED\",\"2016-12-01\",\"2016-12-03\",\"2015-07-02\",null,\"DLX\",\"LTRAN\",\"DLX\",340,null,null,null,null,340,\"WI\",\"US\",\"BAR0\",340,null,\"6PM\",0,3,null,\"PMS\",null,null,\"LTRAN\"]');", "", "", "", "", "arr = [];", "arr.push(reservationNightData);", "", "//Calling assertions", "for (item of arr) {", "    pm.globals.unset(\"actualjson\");", "    pm.globals.set(\"actualjson\", jsonData);", "    pm.globals.unset(\"expectedjson\");", "    pm.globals.set(\"expectedjson\", item);", "    eval(pm.globals.get(\"VerifyG3TableValue\"));", "}"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "74f860b4-6513-42cb-a1ec-3709371eb0cc", "exec": ["pm.globals.unset(\"whereCondition\");\r", "pm.globals.set(\"whereCondition\", \" where reservation_identifier in ('946904','946905') \");"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "propertyId", "type": "text", "value": "5"}], "body": {"mode": "raw", "raw": "{ \"query\": \"{{reservationNightQuery}}\"\n}"}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/G3Tables/executeQuery/v1", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "G3Tables", "execute<PERSON>uery"]}}, "response": []}, {"name": "Then Mkt_Accom_Activity Table gets populated with solds", "event": [{"listen": "test", "script": {"id": "77bc70f7-cd57-47f4-9b5c-b60d63cc6aea", "exec": ["//variables", "var jsonData = pm.response.json();", "", "arr = [];", "", "var totalActivity = ('[\"2016-12-01\",\"LTRAN\",\"DLX\",1,1,0,0,0,340,0,340,0]');", "var totalActivity1 = ('[\"2016-12-02\",\"LTRAN\",\"DLX\",1,0,0,0,0,340,0,340,0]');", "var totalActivity2 = ('[\"2016-12-03\",\"LTRAN\",\"DLX\",0,0,1,0,0,0,0,0,0]');", "", "arr.push(totalActivity, totalActivity1, totalActivity2);", "", "//Calling assertions", "for (item of arr) {", "    pm.globals.unset(\"actualjson\");", "    pm.globals.set(\"actualjson\", jsonData);", "    pm.globals.unset(\"expectedjson\");", "    pm.globals.set(\"expectedjson\", item);", "    eval(pm.globals.get(\"VerifyG3TableValue\"));", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "propertyId", "type": "text", "value": "5"}], "body": {"mode": "raw", "raw": "{ \"query\": \"{{mktAccomActivityQuery}}\"\n}"}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/G3Tables/executeQuery/v1", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "G3Tables", "execute<PERSON>uery"]}}, "response": []}, {"name": "Then Total_Activity Table gets populated with solds", "event": [{"listen": "test", "script": {"id": "acf5be74-596c-4a3f-bbd6-d2297889b529", "exec": ["//variables", "var jsonData = pm.response.json();", "", "arr = [];", "", "var totalActivity = ('[\"2016-12-01\",0,0,0,1,0,0,0,340,0]');", "var totalActivity1 = ('[\"2016-12-02\",0,0,0,0,0,0,0,340,0]');", "var totalActivity2 = ('[\"2016-12-03\",0,0,0,0,1,0,0,0,0]');", "", "arr.push(totalActivity, totalActivity1,totalActivity2 );", "", "//Calling assertions", "for (item of arr) {", "    pm.globals.unset(\"actualjson\");", "    pm.globals.set(\"actualjson\", jsonData);", "    pm.globals.unset(\"expectedjson\");", "    pm.globals.set(\"expectedjson\", item);", "    eval(pm.globals.get(\"VerifyG3TableValue\"));", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "propertyId", "type": "text", "value": "5"}], "body": {"mode": "raw", "raw": "{ \"query\": \"{{totalActivityQuery}}\"\n}\n\n"}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/G3Tables/executeQuery/v1", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "G3Tables", "execute<PERSON>uery"]}}, "response": []}, {"name": "When LOS for primary reservation with Id=946904 is updated to 1", "event": [{"listen": "prerequest", "script": {"id": "4d5aa8e5-3fe0-4b2f-a713-c947bb050a84", "exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"id": "60d55579-ad69-4b1f-af28-f317c721f79d", "exec": ["//verifing status", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "text/xml"}], "body": {"mode": "raw", "raw": "<?xml version = '1.0' encoding = 'UTF-8'?>\r\n<?Label SandBox-OPER1|RESERVATION|20151106003|SUCCESS?>\r\n<!--Reservation_ShareBaseCase_Msg3: Changed <PERSON><PERSON> for Share stay\r\nIn his message: Reservation 946904 for 1 night, 3 adults at $340 \r\nNotes: \r\n/Reservation/RoomStays/RoomStay/resGuestRPHs = 1 pointing to guest with reservation 946904 (primary)\r\n/Reservation/RoomStays/RoomStay/mfsharedGuestRPHs = 0 indicating 946905 is sharing guest\r\n/Reservation/RoomStays/RoomStay/mfshareCode = 1169037 so a share id has been assigned -->\r\n<Reservation xmlns=\"reservation.fidelio.5.0\" mfShareAction=\"NA\" mfReservationAction=\"EDIT\">\r\n   <HotelReference>\r\n      <hotelCode>SandBox-OPER1</hotelCode>\r\n   </HotelReference>\r\n   <reservationID>946904</reservationID>\r\n   <reservationOriginatorCode>SUPERVISOR</reservationOriginatorCode>\r\n   <originalBookingDate>2015-07-02T11:18:31.000</originalBookingDate>\r\n   <StayDateRange timeUnitType=\"DAY\">\r\n      <startTime>2016-12-01T00:00:00.000</startTime>\r\n      <numberOfTimeUnits>1</numberOfTimeUnits>\r\n   </StayDateRange>\r\n   <GuestCounts>\r\n      <GuestCount>\r\n         <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n         <mfCount>3</mfCount>\r\n      </GuestCount>\r\n      <GuestCount>\r\n         <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n         <mfCount>0</mfCount>\r\n      </GuestCount>\r\n   </GuestCounts>\r\n   <ResGuests>\r\n      <ResGuest reservationActionType=\"CHANGE\">\r\n         <resGuestRPH>0</resGuestRPH>\r\n         <profileRPHs>0</profileRPHs>\r\n         <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n         <InHouseTimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-12-01T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>1</numberOfTimeUnits>\r\n         </InHouseTimeSpan>\r\n         <ArrivalTransport/>\r\n         <DepartureTransport/>\r\n         <arrivalTime>2016-12-01T00:00:00.000</arrivalTime>\r\n         <departureTime>2016-12-02T00:00:00.000</departureTime>\r\n         <mfCRSShareID>946905</mfCRSShareID>\r\n         <reservationID>946905</reservationID>\r\n         <ReservationReferences>\r\n            <ReservationReference type=\"GUESTID\" referenceNumber=\"946905\" legNumber=\"1\"/>\r\n            <ReservationReference type=\"PMSID\" referenceNumber=\"946905\" legNumber=\"1\"/>\r\n         </ReservationReferences>\r\n         <preRegistered>0</preRegistered>\r\n         <commissionPaidTo>N</commissionPaidTo>\r\n      </ResGuest>\r\n      <ResGuest reservationActionType=\"CHANGE\">\r\n         <resGuestRPH>1</resGuestRPH>\r\n         <profileRPHs>1</profileRPHs>\r\n         <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n         <InHouseTimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-12-01T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>1</numberOfTimeUnits>\r\n         </InHouseTimeSpan>\r\n         <ArrivalTransport/>\r\n         <DepartureTransport/>\r\n         <arrivalTime>2016-12-01T00:00:00.000</arrivalTime>\r\n         <departureTime>2016-12-02T00:00:00.000</departureTime>\r\n         <mfCRSShareID>946904</mfCRSShareID>\r\n         <reservationID>946904</reservationID>\r\n         <ReservationReferences>\r\n            <ReservationReference type=\"GUESTID\" referenceNumber=\"946904\" legNumber=\"1\"/>\r\n            <ReservationReference type=\"PMSID\" referenceNumber=\"946904\" legNumber=\"1\"/>\r\n         </ReservationReferences>\r\n         <preRegistered>0</preRegistered>\r\n         <commissionPaidTo>N</commissionPaidTo>\r\n      </ResGuest>\r\n   </ResGuests>\r\n   <ResProfiles>\r\n      <ResProfile>\r\n         <Profile xmlns=\"profile.fidelio.5.0\" profileType=\"GUEST\" gender=\"M\" multiProperty=\"0\" miniProfile=\"0\" pseudoProfile=\"0\" replaceAddress=\"0\">\r\n            <creatorCode>OEDS$ADS</creatorCode>\r\n            <createdDate>2009-11-11T17:16:27.000</createdDate>\r\n            <lastUpdaterCode>SUPERVISOR</lastUpdaterCode>\r\n            <lastUpdated>2015-06-04T15:52:56.000</lastUpdated>\r\n            <genericName>Smith</genericName>\r\n            <IndividualName>\r\n               <namePrefix>Mr</namePrefix>\r\n               <nameFirst>Abby</nameFirst>\r\n               <nameSur>Smith</nameSur>\r\n               <nameSuffix>12</nameSuffix>\r\n            </IndividualName>\r\n            <primaryLanguageID>E</primaryLanguageID>\r\n            <Memberships>\r\n               <Membership>\r\n                  <programCode>AA</programCode>\r\n                  <accountID>56478</accountID>\r\n                  <startDate>2014-06-13</startDate>\r\n                  <nameOnCard>ABBY SMITH</nameOnCard>\r\n                  <displaySequence>100</displaySequence>\r\n                  <pointIndicator>0</pointIndicator>\r\n                  <enrollmentSource>N</enrollmentSource>\r\n                  <enrolledAt>N</enrolledAt>\r\n               </Membership>\r\n               <Membership>\r\n                  <programCode>OCIS</programCode>\r\n                  <accountID>987654</accountID>\r\n                  <levelCode>BRON</levelCode>\r\n                  <expireDate>2015-06-30</expireDate>\r\n                  <startDate>2014-06-13</startDate>\r\n                  <nameOnCard>ABBY SMITH</nameOnCard>\r\n                  <displaySequence>50</displaySequence>\r\n                  <enrollmentCode>HOT</enrollmentCode>\r\n                  <pointIndicator>1</pointIndicator>\r\n                  <enrollmentSource>N</enrollmentSource>\r\n                  <enrolledAt>N</enrolledAt>\r\n               </Membership>\r\n               <Membership>\r\n                  <programCode>OR</programCode>\r\n                  <accountID>741852</accountID>\r\n                  <startDate>2014-06-13</startDate>\r\n                  <nameOnCard>ABBY SMITH</nameOnCard>\r\n                  <displaySequence>75</displaySequence>\r\n                  <pointIndicator>0</pointIndicator>\r\n                  <enrollmentSource>N</enrollmentSource>\r\n                  <enrolledAt>N</enrolledAt>\r\n               </Membership>\r\n            </Memberships>\r\n            <PostalAddresses>\r\n               <PostalAddress addressType=\"HOME\">\r\n                  <address1>67 Westwood Parkway5</address1>\r\n                  <city>Lynnwood</city>\r\n                  <stateCode>WA</stateCode>\r\n                  <postalCode>98037</postalCode>\r\n                  <countryCode>US</countryCode>\r\n                  <mfPrimaryYN>Y</mfPrimaryYN>\r\n                  <mfAddressLanguage>E</mfAddressLanguage>\r\n                  <cleansed>0</cleansed>\r\n               </PostalAddress>\r\n            </PostalAddresses>\r\n            <PhoneNumbers>\r\n               <PhoneNumber phoneNumberType=\"HOME\">\r\n                  <phoneNumber>(123)456-7890</phoneNumber>\r\n                  <mfPrimaryYN>Y</mfPrimaryYN>\r\n                  <confirmation>0</confirmation>\r\n               </PhoneNumber>\r\n               <PhoneNumber phoneNumberType=\"BUSINESS\">\r\n                  <phoneNumber>************</phoneNumber>\r\n                  <mfPrimaryYN>N</mfPrimaryYN>\r\n                  <confirmation>0</confirmation>\r\n               </PhoneNumber>\r\n            </PhoneNumbers>\r\n            <SpecialRequests>\r\n               <SpecialRequest mfSpecialRequestType=\"SPE\">\r\n                  <requestCode>HOTSOS</requestCode>\r\n                  <requestComments>HotSOS Test</requestComments>\r\n                  <mfResort>SANDBOX-OXI3</mfResort>\r\n               </SpecialRequest>\r\n            </SpecialRequests>\r\n            <mfResort>SANDBOX-OXI3</mfResort>\r\n            <mfResortProfileID>83830</mfResortProfileID>\r\n            <mfAllowMail>YES</mfAllowMail>\r\n            <mfAllowEMail>NO</mfAllowEMail>\r\n            <mfGuestPriv>NO</mfGuestPriv>\r\n            <mfAllowPhone>0</mfAllowPhone>\r\n            <mfAllowSMS>0</mfAllowSMS>\r\n            <SalesExtention/>\r\n            <PrivacyOption>\r\n               <mfAllowMail>Y</mfAllowMail>\r\n               <mfAllowEMail>N</mfAllowEMail>\r\n               <mfAllowPhone>0</mfAllowPhone>\r\n               <mfAllowSMS>0</mfAllowSMS>\r\n               <mfAllowHistory>1</mfAllowHistory>\r\n               <mfAllowMarketResearch>1</mfAllowMarketResearch>\r\n               <mfAllowThirdParty>1</mfAllowThirdParty>\r\n            </PrivacyOption>\r\n            <ResortList>SANDBOX-OXI3</ResortList>\r\n            <MultiResortEntities>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>CREDIT_CARDS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>RESORT_ARS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>COMMENTS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>PREFERENCES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>NEGOTIATED_RATES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>RESORT_NEG_RATES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>CHANNEL_ACCESS_CODES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>YIELD_ADJUSTMENTS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>RELATIONSHIPS</entity>\r\n               </MultiResortEntity>\r\n            </MultiResortEntities>\r\n            <ResortSpecialRequests>\r\n               <SpecialRequest mfSpecialRequestType=\"SPE\">\r\n                  <requestCode>HOTSOS</requestCode>\r\n                  <requestComments>HotSOS Test</requestComments>\r\n                  <mfResort>SANDBOX-OXI3</mfResort>\r\n               </SpecialRequest>\r\n            </ResortSpecialRequests>\r\n         </Profile>\r\n         <resProfileRPH>0</resProfileRPH>\r\n      </ResProfile>\r\n      <ResProfile>\r\n         <Profile xmlns=\"profile.fidelio.5.0\" profileType=\"GUEST\" gender=\"U\" multiProperty=\"0\" miniProfile=\"0\" pseudoProfile=\"0\" replaceAddress=\"0\">\r\n            <creatorCode>SUPERVISOR</creatorCode>\r\n            <createdDate>2015-07-02T11:18:12.000</createdDate>\r\n            <lastUpdaterCode>SUPERVISOR</lastUpdaterCode>\r\n            <lastUpdated>2015-07-02T11:18:31.000</lastUpdated>\r\n            <genericName>Johnson</genericName>\r\n            <IndividualName>\r\n               <nameFirst>Steve</nameFirst>\r\n               <nameSur>Johnson</nameSur>\r\n            </IndividualName>\r\n            <primaryLanguageID>E</primaryLanguageID>\r\n            <PostalAddresses>\r\n               <PostalAddress addressType=\"HOME\">\r\n                  <countryCode>US</countryCode>\r\n                  <mfPrimaryYN>Y</mfPrimaryYN>\r\n                  <mfAddressLanguage>E</mfAddressLanguage>\r\n                  <cleansed>0</cleansed>\r\n               </PostalAddress>\r\n            </PostalAddresses>\r\n            <mfResort>SANDBOX-OXI3</mfResort>\r\n            <mfResortProfileID>608450</mfResortProfileID>\r\n            <mfAllowMail>YES</mfAllowMail>\r\n            <mfAllowEMail>YES</mfAllowEMail>\r\n            <mfGuestPriv>NO</mfGuestPriv>\r\n            <mfAllowPhone>0</mfAllowPhone>\r\n            <mfAllowSMS>0</mfAllowSMS>\r\n            <SalesExtention/>\r\n            <PrivacyOption>\r\n               <mfAllowMail>Y</mfAllowMail>\r\n               <mfAllowEMail>Y</mfAllowEMail>\r\n               <mfAllowPhone>0</mfAllowPhone>\r\n               <mfAllowSMS>0</mfAllowSMS>\r\n               <mfAllowHistory>1</mfAllowHistory>\r\n               <mfAllowMarketResearch>1</mfAllowMarketResearch>\r\n               <mfAllowThirdParty>1</mfAllowThirdParty>\r\n            </PrivacyOption>\r\n            <ResortList>SANDBOX-OXI3</ResortList>\r\n            <MultiResortEntities>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>CREDIT_CARDS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>RESORT_ARS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>COMMENTS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>PREFERENCES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>NEGOTIATED_RATES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>RESORT_NEG_RATES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>CHANNEL_ACCESS_CODES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>YIELD_ADJUSTMENTS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>RELATIONSHIPS</entity>\r\n               </MultiResortEntity>\r\n            </MultiResortEntities>\r\n         </Profile>\r\n         <resProfileRPH>1</resProfileRPH>\r\n      </ResProfile>\r\n   </ResProfiles>\r\n   <RoomStays>\r\n      <RoomStay mfShareAction=\"NA\" mfReservationAction=\"NA\" reservationActionType=\"CHANGE\" reservationStatusType=\"RESERVED\">\r\n         <roomInventoryCode>STD</roomInventoryCode>\r\n         <TimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-12-01T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>1</numberOfTimeUnits>\r\n         </TimeSpan>\r\n         <GuestCounts>\r\n            <GuestCount>\r\n               <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n               <mfCount>3</mfCount>\r\n            </GuestCount>\r\n            <GuestCount>\r\n               <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n               <mfCount>0</mfCount>\r\n            </GuestCount>\r\n         </GuestCounts>\r\n         <RatePlans>\r\n            <RatePlan reservationActionType=\"CHANGE\">\r\n               <ratePlanRPH>0</ratePlanRPH>\r\n               <ratePlanCode>BAR0</ratePlanCode>\r\n               <TimeSpan timeUnitType=\"DAY\">\r\n                  <startTime>2016-12-01T00:00:00.000</startTime>\r\n                  <numberOfTimeUnits>1</numberOfTimeUnits>\r\n               </TimeSpan>\r\n               <mfMarketCode>LTRAN</mfMarketCode>\r\n               <Rates>\r\n                  <Rate reservationActionType=\"CHANGE\" rateBasisTimeUnitType=\"DAY\">\r\n                     <rateRPH>0</rateRPH>\r\n                     <Amount currencyCode=\"USD\">\r\n                        <valueNum>340</valueNum>\r\n                     </Amount>\r\n                     <rateBasisUnits>1</rateBasisUnits>\r\n                     <TimeSpan timeUnitType=\"DAY\">\r\n                        <startTime>2016-12-01T00:00:00.000</startTime>\r\n                        <numberOfTimeUnits>1</numberOfTimeUnits>\r\n                     </TimeSpan>\r\n                     <mfAdults>3</mfAdults>\r\n                     <mfChildren>0</mfChildren>\r\n                     <mfCribs>0</mfCribs>\r\n                     <mfExtraBeds>0</mfExtraBeds>\r\n                     <mfsourceCode>WI</mfsourceCode>\r\n                     <mfMarketCode>LTRAN</mfMarketCode>\r\n                  </Rate>\r\n               </Rates>\r\n               <mfsourceCode>WI</mfsourceCode>\r\n            </RatePlan>\r\n         </RatePlans>\r\n         <marketSegmentCode>LTRAN</marketSegmentCode>\r\n         <resGuestRPHs>1</resGuestRPHs>\r\n         <GuaranteeInfo guaranteeType=\"NA\">\r\n            <mfGuaranteeType>6PM</mfGuaranteeType>\r\n            <GuaranteeDeposit>\r\n               <Amount currencyCode=\"USD\"/>\r\n               <cancelIfNotReceived>0</cancelIfNotReceived>\r\n            </GuaranteeDeposit>\r\n         </GuaranteeInfo>\r\n         <CancelPenalties>\r\n            <CancelPenalty mfRuleType=\"CANCEL\">\r\n               <cancelByDate>2016-11-29T12:00:00.000</cancelByDate>\r\n               <Amount currencyCode=\"USD\">\r\n                  <valueNum>176.8</valueNum>\r\n               </Amount>\r\n               <mfRuleScope>R</mfRuleScope>\r\n               <mfPercentage>50</mfPercentage>\r\n               <mfCancelPercentDue>100</mfCancelPercentDue>\r\n               <mfRuleDescription>Cancel 48 Hours prior to arrival - 50% penalty</mfRuleDescription>\r\n               <ruleCode>48H50P</ruleCode>\r\n            </CancelPenalty>\r\n         </CancelPenalties>\r\n         <PaymentInstructions>\r\n            <PaymentInstruction paymentMethodType=\"NA\">\r\n               <mfPaymentMethod>CA</mfPaymentMethod>\r\n               <PaymentDue>\r\n                  <Amount currencyCode=\"USD\"/>\r\n                  <cancelIfNotReceived>0</cancelIfNotReceived>\r\n               </PaymentDue>\r\n            </PaymentInstruction>\r\n         </PaymentInstructions>\r\n         <mfsourceCode>WI</mfsourceCode>\r\n         <mfshareCode>1169037</mfshareCode>\r\n         <mfchannelCode>PMS</mfchannelCode>\r\n         <mfconfidentialRate>0</mfconfidentialRate>\r\n         <mfsharedGuestRPHs>0</mfsharedGuestRPHs>\r\n         <mfAsbProrated>0</mfAsbProrated>\r\n      </RoomStay>\r\n   </RoomStays>\r\n   <resProfileRPHs>0, 1</resProfileRPHs>\r\n   <mfupdateDate>2015-07-02T18:20:10.000</mfupdateDate>\r\n   <mfcomplementary>0</mfcomplementary>\r\n   <mfImage>\r\n      <numRooms>1</numRooms>\r\n      <Describe>\r\n         <resortName>Property 1 Opera Demo Multi Hotel</resortName>\r\n         <insertUser>SUPERVISOR</insertUser>\r\n         <updateUser>SUPERVISOR</updateUser>\r\n         <roomCategory>Standard - These rooms measure 30 square metres (325 square feet).  They include a comfortable chair for relaxation, an antique writing desk and cordless phone.  The marble bathroom offers a large vanity with basin, and combination tub and shower test.</roomCategory>\r\n         <rateCode>Room Charge</rateCode>\r\n         <marketCode>Local Transient</marketCode>\r\n         <guarantee>6PM: 6pm Guarantee</guarantee>\r\n         <company>Micros Hotels and Resorts</company>\r\n      </Describe>\r\n      <Change>\r\n         <bSource>0</bSource>\r\n         <bArrivalDate>0</bArrivalDate>\r\n         <bNumNights>1</bNumNights>\r\n         <bNumAdults>0</bNumAdults>\r\n         <bNumChildren>0</bNumChildren>\r\n         <bNumRooms>0</bNumRooms>\r\n         <bCribs>0</bCribs>\r\n         <bRoomCategory>0</bRoomCategory>\r\n         <bRateCode>0</bRateCode>\r\n         <bRateAmount>1</bRateAmount>\r\n         <bMarketCode>0</bMarketCode>\r\n         <bPaymentType>0</bPaymentType>\r\n         <bGuarType>0</bGuarType>\r\n         <bDiscountReason>0</bDiscountReason>\r\n         <bMultipleRateYN>0</bMultipleRateYN>\r\n         <bResvStatus>0</bResvStatus>\r\n      </Change>\r\n   </mfImage>\r\n</Reservation>\r\n"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/ngipublic/rest/oxi/pms", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["ngipublic", "rest", "oxi", "pms"]}}, "response": []}, {"name": "When EOD for 2016-07-03", "event": [{"listen": "test", "script": {"id": "f5146043-5050-46b0-b248-5af0e8869afc", "exec": ["pm.globals.unset(\"fiscal_Date\");", "pm.globals.set(\"fiscal_Date\",\"2016-07-03\");", "", "//verifing status", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{hostName}}:{{ngiPort}}/oxi/admin/pms/endOfDay?propertyCode=OPER1&clientCode=SandBox&fiscalDate=2016-07-03T02:01:01-04:00", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["oxi", "admin", "pms", "endOfDay"], "query": [{"key": "propertyCode", "value": "OPER1"}, {"key": "clientCode", "value": "SandBox"}, {"key": "fiscalDate", "value": "2016-07-03T02:01:01-04:00"}]}}, "response": []}, {"name": "Then unique correlationId populates in nucleusStatisticsCorrelations", "event": [{"listen": "test", "script": {"id": "b96d5ca3-aef0-40fd-8b0c-55892c8614da", "exec": ["tv4.validate", "var jsonData = pm.response.json();", "", "function pollDOM () {", "   console.log(\"current status is\" + jsonData.status)", "  if (jsonData.status == \"SENT_FOR_PROCESSING\"|| jsonData.status == \"PROCESSED\") {", "    //Setting global variables", "    pm.globals.unset(\"corrID\");", "    pm.globals.set(\"corrID\",jsonData.id);", "    pm.globals.set(\"JobType\",\"NGIDeferredDeliveryJob\");", "    value = jsonData.activityStats.CURRENT_ROOM_TYPE_MARKET_SEGMENT.correlationId;", "    pm.globals.unset(\"correlationId\");", "    pm.globals.set(\"correlationId\",value);", "    console.log('query fetched from parameter corrID is -'+pm.globals.get(\"corrID\"));", "    //Setting JobID", "    eval(pm.globals.get(\"getJobID\"));", "  } else {", "      pm.sendRequest({url: pm.globals.get('hostName')+':'+pm.globals.get('ngiPort')+'/nucleusStatisticsCorrelations/search/findByClientCodeAndPropertyCodeAndFiscalDate?clientCode='+ pm.globals.get('client_Code')+'&propertyCode='+pm.globals.get('property_Code')+'&fiscalDate='+pm.globals.get('fiscal_Date'),method: 'GET',header:{'Authorization': 'Basic ********************************','Accept': '*/*'}}, function (err, res) {", "            jsonData = res.json();", "            console.log(\"Status Inside: \" + jsonData.status);", "            console.log(\"Response\" + JSON.stringify(res.json()));", "        });", "    jsonData = pm.response.json();", "    console.log(\"Inside Data\" + JSON.stringify(jsonData));", "    setTimeout(pollDOM, 2000); // try again in 300 milliseconds", "  }", "}", "pollDOM();"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "89384007-38ce-4661-a0c1-458a6481766c", "exec": ["var millisecondsToWait = 5000;\r", "setTimeout(function() {\r", "    // Whatever you want to do after the wait\r", "}, millisecondsToWait);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusStatisticsCorrelations/search/findByClientCodeAndPropertyCodeAndFiscalDate?clientCode={{client_Code}}&propertyCode={{property_Code}}&fiscalDate={{fiscal_Date}}", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusStatisticsCorrelations", "search", "findByClientCodeAndPropertyCodeAndFiscalDate"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "fiscalDate", "value": "{{fiscal_Date}}"}]}}, "response": []}, {"name": "Then NGIDeferredDelivery Job gets triggered in Monitoring Dashboard", "event": [{"listen": "test", "script": {"id": "fa9a14bb-bada-4a7f-8d64-4e99efe30196", "exec": ["eval(pm.globals.get(\"waitForJobToComplete\"));", "", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "propertyid", "type": "text", "value": "5"}, {"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{ \"query\": \"select EXECUTION_STATUS from [Job].[dbo].JOB_STATE where job_instance_id={{jobId}}\"\n}"}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/G3Tables/executeQuery/v1", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "G3Tables", "execute<PERSON>uery"]}}, "response": []}, {"name": "Then the LOS for Reservation gets updated in nucleusReservation Collection in NGI", "event": [{"listen": "test", "script": {"id": "58ec4d6e-9ee3-479a-b5dc-283c87e95790", "exec": ["//variables", "var jsonData = pm.response.json();", "var totalDocuments =  jsonData.page.totalElements;", "", "", "delete jsonData[\"_links\"];", "delete jsonData[\"page\"];", "", "for (var i=0;i<totalDocuments;i++){", "delete jsonData[\"_links\"];", "delete jsonData[\"page\"];", "delete jsonData._embedded.nucleusReservations[i][\"_links\"];", "delete jsonData._embedded.nucleusReservations[i][\"correlationId\"];", "delete jsonData._embedded.nucleusReservations[i][\"statisticsCorrelationId\"];", "delete jsonData._embedded.nucleusReservations[i][\"createDate\"];", "delete jsonData._embedded.nucleusReservations[i][\"lastModifiedDate\"];", "}", "", "", "//value to compare with", "var reservationJSONToBeCompared =JSON.parse('{\"_embedded\":{\"nucleusReservations\":[{\"versionId\":20151106001,\"reservationId\":\"946904\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"bookingDate\":\"2015-07-02T11:18:31.000\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"RESERVED\",\"nationality\":\"US\",\"channel\":\"PMS\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2016-12-01\",\"departureDate\":\"2016-12-03\",\"marketCode\":\"LTRAN\",\"sourceBookingCode\":\"WI\",\"numberOfChildren\":0,\"numberOfAdults\":2,\"bookingType\":\"6PM\",\"invTypeCode\":\"DLX\",\"bookedAccomTypeCode\":\"DLX\",\"rateCode\":\"BAR0\",\"invBlockCode\":null,\"roomNumber\":null,\"rates\":[{\"rateValue\":325,\"startDate\":\"2016-12-01\",\"endDate\":\"2016-12-03\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":325,\"currencyCode\":null}],\"services\":[],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"LTRAN\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"byDay\":false,\"profileId\":null,\"primary\":null,\"latestDepartureDate\":\"2016-12-03\",\"earliestArrivalDate\":\"2016-12-01\"},{\"versionId\":20151106002,\"reservationId\":\"946904\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"bookingDate\":\"2015-07-02T11:18:31.000\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"RESERVED\",\"nationality\":\"US\",\"channel\":\"PMS\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2016-12-01\",\"departureDate\":\"2016-12-03\",\"marketCode\":\"LTRAN\",\"sourceBookingCode\":\"WI\",\"numberOfChildren\":0,\"numberOfAdults\":3,\"bookingType\":\"6PM\",\"invTypeCode\":\"DLX\",\"bookedAccomTypeCode\":\"DLX\",\"rateCode\":\"BAR0\",\"invBlockCode\":null,\"roomNumber\":null,\"rates\":[{\"rateValue\":340,\"startDate\":\"2016-12-01\",\"endDate\":\"2016-12-03\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":340,\"currencyCode\":null}],\"services\":[],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"LTRAN\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"byDay\":false,\"profileId\":null,\"primary\":null,\"latestDepartureDate\":\"2016-12-03\",\"earliestArrivalDate\":\"2016-12-01\"},{\"versionId\":20151106003,\"reservationId\":\"946904\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"bookingDate\":\"2015-07-02T11:18:31.000\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"RESERVED\",\"nationality\":\"US\",\"channel\":\"PMS\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2016-12-01\",\"departureDate\":\"2016-12-02\",\"marketCode\":\"LTRAN\",\"sourceBookingCode\":\"WI\",\"numberOfChildren\":0,\"numberOfAdults\":3,\"bookingType\":\"6PM\",\"invTypeCode\":\"STD\",\"bookedAccomTypeCode\":\"DLX\",\"rateCode\":\"BAR0\",\"invBlockCode\":null,\"roomNumber\":null,\"rates\":[{\"rateValue\":340,\"startDate\":\"2016-12-01\",\"endDate\":\"2016-12-02\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":340,\"currencyCode\":null}],\"services\":[],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"LTRAN\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"byDay\":false,\"profileId\":null,\"primary\":null,\"latestDepartureDate\":\"2016-12-02\",\"earliestArrivalDate\":\"2016-12-01\"}]}}');", "", "//assertions", "assertJSONData(\"Verifying Client Code in OxiMessage\", jsonData,reservationJSONToBeCompared);", "", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue).to.eql(expectedValue);", "});}", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusReservations/criteria?search=clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BreservationId%3Din%3D%28946904%29&size=100&sort=reservationId,desc", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusReservations", "criteria"], "query": [{"key": "search", "value": "clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BreservationId%3Din%3D%28946904%29"}, {"key": "size", "value": "100"}, {"key": "sort", "value": "reservationId,desc"}]}}, "response": []}, {"name": "Then the LOS in oxiShareReservationReferences Collection is updated accordingly", "event": [{"listen": "test", "script": {"id": "e6bab710-41c3-47f6-9f0c-e9852d6e9097", "exec": ["//variables", "var jsonData = pm.response.json();", "", "//value to compare with", "var reservationJSONToBeCompared = {\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"reservationId\":\"946904\",\"primaryReservationId\":\"946904\",\"shareCode\":\"1169037\",\"sharers\":[{\"reservationId\":\"946905\",\"shareParent\":false,\"startedOn\":\"2015-07-02T16:20:10Z\",\"endedOn\":null,\"arrivalDate\":\"2016-12-01\",\"departureDate\":\"2016-12-02\",\"revenue\":null,\"participating\":true}]};", "", "var reservationJSONToBeCompared1 = {\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"reservationId\":\"946904\",\"primaryReservationId\":\"946904\",\"shareCode\":\"1169037\",\"sharers\":[{\"reservationId\":\"946904\",\"shareParent\":true,\"startedOn\":\"2015-07-02T16:20:10Z\",\"endedOn\":null,\"arrivalDate\":\"2016-12-01\",\"departureDate\":\"2016-12-02\",\"revenue\":null,\"participating\":true}]};", "", "//assertions", "arr = [];", "arr.push(reservationJSONToBeCompared,reservationJSONToBeCompared1);", "", "//Calling Comparisons", "for (item of arr) {", "     pm.globals.unset(\"actualjson\");", "    pm.globals.set(\"actualjson\", jsonData);", "    pm.globals.unset(\"expectedjson\");", "    pm.globals.set(\"expectedjson\", item);", "    eval(pm.globals.get(\"verifySharesReservationCollection\"));", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/oxiShareReservationReferences/search/findOneByClientCodeAndPropertyCodeAndReservationId?clientCode={{client_Code}}&propertyCode={{property_Code}}&reservationId=946904&size=100&sort=reservationId,desc", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["oxiShareReservationReferences", "search", "findOneByClientCodeAndPropertyCodeAndReservationId"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "reservationId", "value": "946904"}, {"key": "size", "value": "100"}, {"key": "sort", "value": "reservationId,desc"}]}}, "response": []}, {"name": "Then Summary is build for all the share reservation and sold is updated accordingly", "event": [{"listen": "test", "script": {"id": "2dec66c6-2c6c-4c95-a321-7363e1818a72", "exec": ["//variables", "var jsonData = pm.response.json();", "var totalDocuments =  jsonData.page.totalElements;", "", "", "delete jsonData[\"_links\"];", "delete jsonData[\"page\"];", "", "for (var i=0;i<totalDocuments;i++){", "   ", "//delete jsonData[\"page\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"_links\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"pseudoRoom\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"dayUse\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"statisticsCorrelationId\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"createDate\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"lastModifiedDate\"];", "}", "", "", "//value to compare with", "var reservationJSONToBeCompared = JSON.parse('{\"_embedded\":{\"nucleusOccupancySummaries\":[{\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"reservationId\":\"946904\",\"reservationStatus\":\"RESERVED\",\"occupancyDate\":\"2016-12-02 00:00:00.000\",\"marketSegmentCode\":\"LTRAN\",\"roomTypeCode\":\"STD\",\"roomNumber\":null,\"rateCode\":\"BAR0\",\"block\":null,\"pickup\":null,\"groupOccupancy\":false,\"roomsSold\":0,\"transientSold\":null,\"groupSold\":null,\"roomRevenue\":0,\"foodRevenue\":0,\"beverageRevenue\":0,\"otherRevenue\":0,\"totalRevenue\":0,\"noShowRoomRevenue\":null,\"arrivals\":0,\"departures\":1,\"cancellations\":0,\"noShows\":0,\"accomCapacity\":null,\"roomsNotAvailableMaintenance\":null,\"roomsNotAvailableOther\":null,\"groupId\":null,\"roomRate\":0,\"correlationId\":null,\"amsInPlay\":false,\"analyticalMarketSegmentCode\":\"LTRAN\",\"groupStatus\":null},{\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"reservationId\":\"946904\",\"reservationStatus\":\"RESERVED\",\"occupancyDate\":\"2016-12-01 00:00:00.000\",\"marketSegmentCode\":\"LTRAN\",\"roomTypeCode\":\"STD\",\"roomNumber\":null,\"rateCode\":\"BAR0\",\"block\":null,\"pickup\":null,\"groupOccupancy\":false,\"roomsSold\":1,\"transientSold\":null,\"groupSold\":null,\"roomRevenue\":340,\"foodRevenue\":0,\"beverageRevenue\":0,\"otherRevenue\":0,\"totalRevenue\":340,\"noShowRoomRevenue\":null,\"arrivals\":1,\"departures\":0,\"cancellations\":0,\"noShows\":0,\"accomCapacity\":null,\"roomsNotAvailableMaintenance\":null,\"roomsNotAvailableOther\":null,\"groupId\":null,\"roomRate\":340,\"correlationId\":null,\"amsInPlay\":false,\"analyticalMarketSegmentCode\":\"LTRAN\",\"groupStatus\":null}]}}');", "", "//assertions", "assertJSONData(\"Verifying Client Code in OxiMessage\", jsonData,reservationJSONToBeCompared);", "", "", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue).to.eql(expectedValue);", "});}", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusOccupancySummaries/criteria?search=clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BreservationId%3Din%3D%28946904%29&size=300&sort=occupancyDate,desc", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusOccupancySummaries", "criteria"], "query": [{"key": "search", "value": "clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BreservationId%3Din%3D%28946904%29"}, {"key": "size", "value": "300"}, {"key": "sort", "value": "occupancyDate,desc"}]}}, "response": []}, {"name": "When PMS updates LOS of sharer to 1 in primary reservation", "event": [{"listen": "prerequest", "script": {"id": "73d65ac3-5b92-445d-a28b-be5488b11458", "exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"id": "3d4fa073-a562-4ffe-8be2-448b4b2f192a", "exec": ["//verifing status", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "text/xml"}], "body": {"mode": "raw", "raw": "<?xml version = '1.0' encoding = 'UTF-8'?>\r\n<?Label SandBox-OPER1|RESERVATION|20151106004|SUCCESS?>\r\n<!--Reservation_ShareBaseCase_Msg4: Changed <PERSON><PERSON> for Share stay, from other sharer side\r\nIn his message: Reservation 946904 for 1 night, 3 adults at $340 \r\nNotes: \r\n/Reservation/RoomStays/RoomStay/resGuestRPHs = 0 pointing to guest with reservation 946904 (primary)\r\n/Reservation/RoomStays/RoomStay/mfsharedGuestRPHs = 1 indicating 946905 is sharing guest\r\n/Reservation/RoomStays/RoomStay/mfshareCode = 1169037 so a share id has been assigned -->\r\n<Reservation xmlns=\"reservation.fidelio.5.0\" mfShareAction=\"NA\" mfReservationAction=\"EDIT\">\r\n   <HotelReference>\r\n      <hotelCode>SandBox-OPER1</hotelCode>\r\n   </HotelReference>\r\n   <reservationID>946904</reservationID>\r\n   <reservationOriginatorCode>SUPERVISOR</reservationOriginatorCode>\r\n   <originalBookingDate>2015-07-02T11:18:31.000</originalBookingDate>\r\n   <StayDateRange timeUnitType=\"DAY\">\r\n      <startTime>2016-12-01T00:00:00.000</startTime>\r\n      <numberOfTimeUnits>1</numberOfTimeUnits>\r\n   </StayDateRange>\r\n   <GuestCounts>\r\n      <GuestCount>\r\n         <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n         <mfCount>3</mfCount>\r\n      </GuestCount>\r\n      <GuestCount>\r\n         <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n         <mfCount>0</mfCount>\r\n      </GuestCount>\r\n   </GuestCounts>\r\n   <ResGuests>\r\n      <ResGuest reservationActionType=\"CHANGE\">\r\n         <resGuestRPH>0</resGuestRPH>\r\n         <profileRPHs>0</profileRPHs>\r\n         <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n         <InHouseTimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-12-01T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>1</numberOfTimeUnits>\r\n         </InHouseTimeSpan>\r\n         <ArrivalTransport/>\r\n         <DepartureTransport/>\r\n         <arrivalTime>2016-12-01T00:00:00.000</arrivalTime>\r\n         <departureTime>2016-12-02T00:00:00.000</departureTime>\r\n         <mfCRSShareID>946904</mfCRSShareID>\r\n         <reservationID>946904</reservationID>\r\n         <ReservationReferences>\r\n            <ReservationReference type=\"GUESTID\" referenceNumber=\"946904\" legNumber=\"1\"/>\r\n            <ReservationReference type=\"PMSID\" referenceNumber=\"946904\" legNumber=\"1\"/>\r\n         </ReservationReferences>\r\n         <preRegistered>0</preRegistered>\r\n         <commissionPaidTo>N</commissionPaidTo>\r\n      </ResGuest>\r\n      <ResGuest reservationActionType=\"CHANGE\">\r\n         <resGuestRPH>1</resGuestRPH>\r\n         <profileRPHs>1</profileRPHs>\r\n         <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n         <InHouseTimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-12-01T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>1</numberOfTimeUnits>\r\n         </InHouseTimeSpan>\r\n         <ArrivalTransport/>\r\n         <DepartureTransport/>\r\n         <arrivalTime>2016-12-01T00:00:00.000</arrivalTime>\r\n         <departureTime>2016-12-02T00:00:00.000</departureTime>\r\n         <mfCRSShareID>946905</mfCRSShareID>\r\n         <reservationID>946905</reservationID>\r\n         <ReservationReferences>\r\n            <ReservationReference type=\"GUESTID\" referenceNumber=\"946905\" legNumber=\"1\"/>\r\n            <ReservationReference type=\"PMSID\" referenceNumber=\"946905\" legNumber=\"1\"/>\r\n         </ReservationReferences>\r\n         <preRegistered>0</preRegistered>\r\n         <commissionPaidTo>N</commissionPaidTo>\r\n      </ResGuest>\r\n   </ResGuests>\r\n   <ResProfiles>\r\n      <ResProfile>\r\n         <Profile xmlns=\"profile.fidelio.5.0\" profileType=\"GUEST\" gender=\"U\" multiProperty=\"0\" miniProfile=\"0\" pseudoProfile=\"0\" replaceAddress=\"0\">\r\n            <creatorCode>SUPERVISOR</creatorCode>\r\n            <createdDate>2015-07-02T11:18:12.000</createdDate>\r\n            <lastUpdaterCode>SUPERVISOR</lastUpdaterCode>\r\n            <lastUpdated>2015-07-02T11:18:31.000</lastUpdated>\r\n            <genericName>Johnson</genericName>\r\n            <IndividualName>\r\n               <nameFirst>Steve</nameFirst>\r\n               <nameSur>Johnson</nameSur>\r\n            </IndividualName>\r\n            <primaryLanguageID>E</primaryLanguageID>\r\n            <PostalAddresses>\r\n               <PostalAddress addressType=\"HOME\">\r\n                  <countryCode>US</countryCode>\r\n                  <mfPrimaryYN>Y</mfPrimaryYN>\r\n                  <mfAddressLanguage>E</mfAddressLanguage>\r\n                  <cleansed>0</cleansed>\r\n               </PostalAddress>\r\n            </PostalAddresses>\r\n            <mfResort>SANDBOX-OXI3</mfResort>\r\n            <mfResortProfileID>608450</mfResortProfileID>\r\n            <mfAllowMail>YES</mfAllowMail>\r\n            <mfAllowEMail>YES</mfAllowEMail>\r\n            <mfGuestPriv>NO</mfGuestPriv>\r\n            <mfAllowPhone>0</mfAllowPhone>\r\n            <mfAllowSMS>0</mfAllowSMS>\r\n            <SalesExtention/>\r\n            <PrivacyOption>\r\n               <mfAllowMail>Y</mfAllowMail>\r\n               <mfAllowEMail>Y</mfAllowEMail>\r\n               <mfAllowPhone>0</mfAllowPhone>\r\n               <mfAllowSMS>0</mfAllowSMS>\r\n               <mfAllowHistory>1</mfAllowHistory>\r\n               <mfAllowMarketResearch>1</mfAllowMarketResearch>\r\n               <mfAllowThirdParty>1</mfAllowThirdParty>\r\n            </PrivacyOption>\r\n            <ResortList>SANDBOX-OXI3</ResortList>\r\n            <MultiResortEntities>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>CREDIT_CARDS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>RESORT_ARS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>COMMENTS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>PREFERENCES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>NEGOTIATED_RATES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>RESORT_NEG_RATES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>CHANNEL_ACCESS_CODES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>YIELD_ADJUSTMENTS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>RELATIONSHIPS</entity>\r\n               </MultiResortEntity>\r\n            </MultiResortEntities>\r\n         </Profile>\r\n         <resProfileRPH>0</resProfileRPH>\r\n      </ResProfile>\r\n      <ResProfile>\r\n         <Profile xmlns=\"profile.fidelio.5.0\" profileType=\"GUEST\" gender=\"M\" multiProperty=\"0\" miniProfile=\"0\" pseudoProfile=\"0\" replaceAddress=\"0\">\r\n            <creatorCode>OEDS$ADS</creatorCode>\r\n            <createdDate>2009-11-11T17:16:27.000</createdDate>\r\n            <lastUpdaterCode>SUPERVISOR</lastUpdaterCode>\r\n            <lastUpdated>2015-06-04T15:52:56.000</lastUpdated>\r\n            <genericName>Smith</genericName>\r\n            <IndividualName>\r\n               <namePrefix>Mr</namePrefix>\r\n               <nameFirst>Abby</nameFirst>\r\n               <nameSur>Smith</nameSur>\r\n               <nameSuffix>12</nameSuffix>\r\n            </IndividualName>\r\n            <primaryLanguageID>E</primaryLanguageID>\r\n            <Memberships>\r\n               <Membership>\r\n                  <programCode>AA</programCode>\r\n                  <accountID>56478</accountID>\r\n                  <startDate>2014-06-13</startDate>\r\n                  <nameOnCard>ABBY SMITH</nameOnCard>\r\n                  <displaySequence>100</displaySequence>\r\n                  <pointIndicator>0</pointIndicator>\r\n                  <enrollmentSource>N</enrollmentSource>\r\n                  <enrolledAt>N</enrolledAt>\r\n               </Membership>\r\n               <Membership>\r\n                  <programCode>OCIS</programCode>\r\n                  <accountID>987654</accountID>\r\n                  <levelCode>BRON</levelCode>\r\n                  <expireDate>2015-06-30</expireDate>\r\n                  <startDate>2014-06-13</startDate>\r\n                  <nameOnCard>ABBY SMITH</nameOnCard>\r\n                  <displaySequence>50</displaySequence>\r\n                  <enrollmentCode>HOT</enrollmentCode>\r\n                  <pointIndicator>1</pointIndicator>\r\n                  <enrollmentSource>N</enrollmentSource>\r\n                  <enrolledAt>N</enrolledAt>\r\n               </Membership>\r\n               <Membership>\r\n                  <programCode>OR</programCode>\r\n                  <accountID>741852</accountID>\r\n                  <startDate>2014-06-13</startDate>\r\n                  <nameOnCard>ABBY SMITH</nameOnCard>\r\n                  <displaySequence>75</displaySequence>\r\n                  <pointIndicator>0</pointIndicator>\r\n                  <enrollmentSource>N</enrollmentSource>\r\n                  <enrolledAt>N</enrolledAt>\r\n               </Membership>\r\n            </Memberships>\r\n            <PostalAddresses>\r\n               <PostalAddress addressType=\"HOME\">\r\n                  <address1>67 Westwood Parkway5</address1>\r\n                  <city>Lynnwood</city>\r\n                  <stateCode>WA</stateCode>\r\n                  <postalCode>98037</postalCode>\r\n                  <countryCode>US</countryCode>\r\n                  <mfPrimaryYN>Y</mfPrimaryYN>\r\n                  <mfAddressLanguage>E</mfAddressLanguage>\r\n                  <cleansed>0</cleansed>\r\n               </PostalAddress>\r\n            </PostalAddresses>\r\n            <PhoneNumbers>\r\n               <PhoneNumber phoneNumberType=\"HOME\">\r\n                  <phoneNumber>(123)456-7890</phoneNumber>\r\n                  <mfPrimaryYN>Y</mfPrimaryYN>\r\n                  <confirmation>0</confirmation>\r\n               </PhoneNumber>\r\n               <PhoneNumber phoneNumberType=\"BUSINESS\">\r\n                  <phoneNumber>************</phoneNumber>\r\n                  <mfPrimaryYN>N</mfPrimaryYN>\r\n                  <confirmation>0</confirmation>\r\n               </PhoneNumber>\r\n            </PhoneNumbers>\r\n            <SpecialRequests>\r\n               <SpecialRequest mfSpecialRequestType=\"SPE\">\r\n                  <requestCode>HOTSOS</requestCode>\r\n                  <requestComments>HotSOS Test</requestComments>\r\n                  <mfResort>SANDBOX-OXI3</mfResort>\r\n               </SpecialRequest>\r\n            </SpecialRequests>\r\n            <mfResort>SANDBOX-OXI3</mfResort>\r\n            <mfResortProfileID>83830</mfResortProfileID>\r\n            <mfAllowMail>YES</mfAllowMail>\r\n            <mfAllowEMail>NO</mfAllowEMail>\r\n            <mfGuestPriv>NO</mfGuestPriv>\r\n            <mfAllowPhone>0</mfAllowPhone>\r\n            <mfAllowSMS>0</mfAllowSMS>\r\n            <SalesExtention/>\r\n            <PrivacyOption>\r\n               <mfAllowMail>Y</mfAllowMail>\r\n               <mfAllowEMail>N</mfAllowEMail>\r\n               <mfAllowPhone>0</mfAllowPhone>\r\n               <mfAllowSMS>0</mfAllowSMS>\r\n               <mfAllowHistory>1</mfAllowHistory>\r\n               <mfAllowMarketResearch>1</mfAllowMarketResearch>\r\n               <mfAllowThirdParty>1</mfAllowThirdParty>\r\n            </PrivacyOption>\r\n            <ResortList>SANDBOX-OXI3</ResortList>\r\n            <MultiResortEntities>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>CREDIT_CARDS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>RESORT_ARS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>COMMENTS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>PREFERENCES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>NEGOTIATED_RATES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>RESORT_NEG_RATES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>CHANNEL_ACCESS_CODES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>YIELD_ADJUSTMENTS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>RELATIONSHIPS</entity>\r\n               </MultiResortEntity>\r\n            </MultiResortEntities>\r\n            <ResortSpecialRequests>\r\n               <SpecialRequest mfSpecialRequestType=\"SPE\">\r\n                  <requestCode>HOTSOS</requestCode>\r\n                  <requestComments>HotSOS Test</requestComments>\r\n                  <mfResort>SANDBOX-OXI3</mfResort>\r\n               </SpecialRequest>\r\n            </ResortSpecialRequests>\r\n         </Profile>\r\n         <resProfileRPH>1</resProfileRPH>\r\n      </ResProfile>\r\n   </ResProfiles>\r\n   <RoomStays>\r\n      <RoomStay mfShareAction=\"NA\" mfReservationAction=\"NA\" reservationActionType=\"CHANGE\" reservationStatusType=\"RESERVED\">\r\n         <roomInventoryCode>STD</roomInventoryCode>\r\n         <TimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-12-01T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>1</numberOfTimeUnits>\r\n         </TimeSpan>\r\n         <GuestCounts>\r\n            <GuestCount>\r\n               <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n               <mfCount>3</mfCount>\r\n            </GuestCount>\r\n            <GuestCount>\r\n               <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n               <mfCount>0</mfCount>\r\n            </GuestCount>\r\n         </GuestCounts>\r\n         <RatePlans>\r\n            <RatePlan reservationActionType=\"CHANGE\">\r\n               <ratePlanRPH>0</ratePlanRPH>\r\n               <ratePlanCode>BAR0</ratePlanCode>\r\n               <TimeSpan timeUnitType=\"DAY\">\r\n                  <startTime>2016-12-01T00:00:00.000</startTime>\r\n                  <numberOfTimeUnits>1</numberOfTimeUnits>\r\n               </TimeSpan>\r\n               <mfMarketCode>LTRAN</mfMarketCode>\r\n               <Rates>\r\n                  <Rate reservationActionType=\"CHANGE\" rateBasisTimeUnitType=\"DAY\">\r\n                     <rateRPH>0</rateRPH>\r\n                     <Amount currencyCode=\"USD\">\r\n                        <valueNum>340</valueNum>\r\n                     </Amount>\r\n                     <rateBasisUnits>1</rateBasisUnits>\r\n                     <TimeSpan timeUnitType=\"DAY\">\r\n                        <startTime>2016-12-01T00:00:00.000</startTime>\r\n                        <numberOfTimeUnits>1</numberOfTimeUnits>\r\n                     </TimeSpan>\r\n                     <mfAdults>3</mfAdults>\r\n                     <mfChildren>0</mfChildren>\r\n                     <mfCribs>0</mfCribs>\r\n                     <mfExtraBeds>0</mfExtraBeds>\r\n                     <mfsourceCode>WI</mfsourceCode>\r\n                     <mfMarketCode>LTRAN</mfMarketCode>\r\n                  </Rate>\r\n               </Rates>\r\n               <mfsourceCode>WI</mfsourceCode>\r\n            </RatePlan>\r\n         </RatePlans>\r\n         <marketSegmentCode>LTRAN</marketSegmentCode>\r\n         <resGuestRPHs>0</resGuestRPHs>\r\n         <GuaranteeInfo guaranteeType=\"NA\">\r\n            <mfGuaranteeType>6PM</mfGuaranteeType>\r\n            <GuaranteeDeposit>\r\n               <Amount currencyCode=\"USD\"/>\r\n               <cancelIfNotReceived>0</cancelIfNotReceived>\r\n            </GuaranteeDeposit>\r\n         </GuaranteeInfo>\r\n         <CancelPenalties>\r\n            <CancelPenalty mfRuleType=\"CANCEL\">\r\n               <cancelByDate>2016-11-29T12:00:00.000</cancelByDate>\r\n               <Amount currencyCode=\"USD\">\r\n                  <valueNum>176.8</valueNum>\r\n               </Amount>\r\n               <mfRuleScope>R</mfRuleScope>\r\n               <mfPercentage>50</mfPercentage>\r\n               <mfCancelPercentDue>100</mfCancelPercentDue>\r\n               <mfRuleDescription>Cancel 48 Hours prior to arrival - 50% penalty</mfRuleDescription>\r\n               <ruleCode>48H50P</ruleCode>\r\n            </CancelPenalty>\r\n         </CancelPenalties>\r\n         <PaymentInstructions>\r\n            <PaymentInstruction paymentMethodType=\"NA\">\r\n               <mfPaymentMethod>CA</mfPaymentMethod>\r\n               <PaymentDue>\r\n                  <Amount currencyCode=\"USD\"/>\r\n                  <cancelIfNotReceived>0</cancelIfNotReceived>\r\n               </PaymentDue>\r\n            </PaymentInstruction>\r\n         </PaymentInstructions>\r\n         <mfsourceCode>WI</mfsourceCode>\r\n         <mfshareCode>1169037</mfshareCode>\r\n         <mfchannelCode>PMS</mfchannelCode>\r\n         <mfconfidentialRate>0</mfconfidentialRate>\r\n         <mfsharedGuestRPHs>1</mfsharedGuestRPHs>\r\n         <mfAsbProrated>0</mfAsbProrated>\r\n      </RoomStay>\r\n   </RoomStays>\r\n   <resProfileRPHs>0, 1</resProfileRPHs>\r\n   <mfupdateDate>2015-07-02T18:20:10.000</mfupdateDate>\r\n   <mfcomplementary>0</mfcomplementary>\r\n   <mfImage>\r\n      <numRooms>1</numRooms>\r\n      <Describe>\r\n         <resortName>Property 1 Opera Demo Multi Hotel</resortName>\r\n         <insertUser>SUPERVISOR</insertUser>\r\n         <updateUser>SUPERVISOR</updateUser>\r\n         <roomCategory>Standard - These rooms measure 30 square metres (325 square feet).  They include a comfortable chair for relaxation, an antique writing desk and cordless phone.  The marble bathroom offers a large vanity with basin, and combination tub and shower test.</roomCategory>\r\n         <rateCode>Room Charge</rateCode>\r\n         <marketCode>Local Transient</marketCode>\r\n         <guarantee>6PM: 6pm Guarantee</guarantee>\r\n         <company>Micros Hotels and Resorts</company>\r\n      </Describe>\r\n      <Change>\r\n         <bSource>0</bSource>\r\n         <bArrivalDate>0</bArrivalDate>\r\n         <bNumNights>1</bNumNights>\r\n         <bNumAdults>0</bNumAdults>\r\n         <bNumChildren>0</bNumChildren>\r\n         <bNumRooms>0</bNumRooms>\r\n         <bCribs>0</bCribs>\r\n         <bRoomCategory>0</bRoomCategory>\r\n         <bRateCode>0</bRateCode>\r\n         <bRateAmount>1</bRateAmount>\r\n         <bMarketCode>0</bMarketCode>\r\n         <bPaymentType>0</bPaymentType>\r\n         <bGuarType>0</bGuarType>\r\n         <bDiscountReason>0</bDiscountReason>\r\n         <bMultipleRateYN>0</bMultipleRateYN>\r\n         <bResvStatus>0</bResvStatus>\r\n      </Change>\r\n   </mfImage>\r\n</Reservation>\r\n"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/ngipublic/rest/oxi/pms", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["ngipublic", "rest", "oxi", "pms"]}}, "response": []}, {"name": "Then no change happens in oxiShareReservationReferences collection as this message is generated by default whenever sharer information is updated in PMS", "event": [{"listen": "test", "script": {"id": "be4014e4-96cf-4b2c-9e01-185cf8a75455", "exec": ["//variables", "var jsonData = pm.response.json();", "", "//value to compare with", "var reservationJSONToBeCompared = {\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"reservationId\":\"946904\",\"primaryReservationId\":\"946904\",\"shareCode\":\"1169037\",\"sharers\":[{\"reservationId\":\"946905\",\"shareParent\":false,\"startedOn\":\"2015-07-02T16:20:10Z\",\"endedOn\":null,\"arrivalDate\":\"2016-12-01\",\"departureDate\":\"2016-12-02\",\"revenue\":null,\"participating\":true}]};", "", "var reservationJSONToBeCompared1 = {\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"reservationId\":\"946904\",\"primaryReservationId\":\"946904\",\"shareCode\":\"1169037\",\"sharers\":[{\"reservationId\":\"946904\",\"shareParent\":true,\"startedOn\":\"2015-07-02T16:20:10Z\",\"endedOn\":null,\"arrivalDate\":\"2016-12-01\",\"departureDate\":\"2016-12-02\",\"revenue\":null,\"participating\":true}]};", "", "//assertions", "arr = [];", "arr.push(reservationJSONToBeCompared,reservationJSONToBeCompared1);", "", "//Calling Comparisons", "for (item of arr) {", "     pm.globals.unset(\"actualjson\");", "    pm.globals.set(\"actualjson\", jsonData);", "    pm.globals.unset(\"expectedjson\");", "    pm.globals.set(\"expectedjson\", item);", "    eval(pm.globals.get(\"verifySharesReservationCollection\"));", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/oxiShareReservationReferences/search/findOneByClientCodeAndPropertyCodeAndReservationId?clientCode={{client_Code}}&propertyCode={{property_Code}}&reservationId=946904&size=100&sort=reservationId,desc", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["oxiShareReservationReferences", "search", "findOneByClientCodeAndPropertyCodeAndReservationId"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "reservationId", "value": "946904"}, {"key": "size", "value": "100"}, {"key": "sort", "value": "reservationId,desc"}]}}, "response": []}, {"name": "When break for primary reservation with Id=946904 is sent", "event": [{"listen": "prerequest", "script": {"id": "a01fe6b4-6482-4b91-b636-38d0fd4b34e9", "exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"id": "00808965-033f-4bff-abed-41c17ea39fd3", "exec": ["//verifing status", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "text/xml"}], "body": {"mode": "raw", "raw": "<?xml version = '1.0' encoding = 'UTF-8'?>\r\n<?Label SandBox-OPER1|RESERVATION|20151106005|SUCCESS?>\r\n<!--Reservation_ShareBaseCase_Msg5: Update with Break Share for Primary Guest\r\nIn his message: Reservation 946904 for 1 night, 2 adults at $325 (Same as before sharer joined)\r\nNotes:\r\noriginalBookingDate is the same as has been used for share updates\r\n/Reservation/RoomStays/RoomStay/resGuestRPHs = 0 now pointing to only guest with reservation 946904\r\n/Reservation/RoomStays/RoomStay/mfsharedGuestRPHs no longer defined\r\n/Reservation/RoomStays/RoomStay/mfshareCode no longer defined -->\r\n<Reservation xmlns=\"reservation.fidelio.5.0\" mfShareAction=\"NA\" mfReservationAction=\"EDIT\">\r\n   <HotelReference>\r\n      <hotelCode>SandBox-OPER1</hotelCode>\r\n   </HotelReference>\r\n   <reservationID>946904</reservationID>\r\n   <reservationOriginatorCode>SUPERVISOR</reservationOriginatorCode>\r\n   <originalBookingDate>2015-07-02T11:18:31.000</originalBookingDate>\r\n   <StayDateRange timeUnitType=\"DAY\">\r\n      <startTime>2016-12-01T00:00:00.000</startTime>\r\n      <numberOfTimeUnits>1</numberOfTimeUnits>\r\n   </StayDateRange>\r\n   <GuestCounts>\r\n      <GuestCount>\r\n         <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n         <mfCount>2</mfCount>\r\n      </GuestCount>\r\n      <GuestCount>\r\n         <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n         <mfCount>0</mfCount>\r\n      </GuestCount>\r\n   </GuestCounts>\r\n   <ResGuests>\r\n      <ResGuest reservationActionType=\"CHANGE\">\r\n         <resGuestRPH>0</resGuestRPH>\r\n         <profileRPHs>0</profileRPHs>\r\n         <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n         <InHouseTimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-12-01T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>1</numberOfTimeUnits>\r\n         </InHouseTimeSpan>\r\n         <ArrivalTransport/>\r\n         <DepartureTransport/>\r\n         <arrivalTime>2016-12-01T00:00:00.000</arrivalTime>\r\n         <departureTime>2016-12-02T00:00:00.000</departureTime>\r\n         <reservationID>946904</reservationID>\r\n         <ReservationReferences>\r\n            <ReservationReference type=\"GUESTID\" referenceNumber=\"946904\" legNumber=\"1\"/>\r\n            <ReservationReference type=\"PMSID\" referenceNumber=\"946904\" legNumber=\"1\"/>\r\n         </ReservationReferences>\r\n         <preRegistered>0</preRegistered>\r\n         <commissionPaidTo>N</commissionPaidTo>\r\n      </ResGuest>\r\n   </ResGuests>\r\n   <ResProfiles>\r\n      <ResProfile>\r\n         <Profile xmlns=\"profile.fidelio.5.0\" profileType=\"GUEST\" gender=\"U\" multiProperty=\"0\" miniProfile=\"0\" pseudoProfile=\"0\" replaceAddress=\"0\">\r\n            <creatorCode>SUPERVISOR</creatorCode>\r\n            <createdDate>2015-07-02T11:18:12.000</createdDate>\r\n            <lastUpdaterCode>SUPERVISOR</lastUpdaterCode>\r\n            <lastUpdated>2015-07-02T11:18:31.000</lastUpdated>\r\n            <genericName>Johnson</genericName>\r\n            <IndividualName>\r\n               <nameFirst>Steve</nameFirst>\r\n               <nameSur>Johnson</nameSur>\r\n            </IndividualName>\r\n            <primaryLanguageID>E</primaryLanguageID>\r\n            <PostalAddresses>\r\n               <PostalAddress addressType=\"HOME\">\r\n                  <countryCode>US</countryCode>\r\n                  <mfPrimaryYN>Y</mfPrimaryYN>\r\n                  <mfAddressLanguage>E</mfAddressLanguage>\r\n                  <cleansed>0</cleansed>\r\n               </PostalAddress>\r\n            </PostalAddresses>\r\n            <mfResort>SANDBOX-OXI3</mfResort>\r\n            <mfResortProfileID>608450</mfResortProfileID>\r\n            <mfAllowMail>YES</mfAllowMail>\r\n            <mfAllowEMail>YES</mfAllowEMail>\r\n            <mfGuestPriv>NO</mfGuestPriv>\r\n            <mfAllowPhone>0</mfAllowPhone>\r\n            <mfAllowSMS>0</mfAllowSMS>\r\n            <SalesExtention/>\r\n            <PrivacyOption>\r\n               <mfAllowMail>Y</mfAllowMail>\r\n               <mfAllowEMail>Y</mfAllowEMail>\r\n               <mfAllowPhone>0</mfAllowPhone>\r\n               <mfAllowSMS>0</mfAllowSMS>\r\n               <mfAllowHistory>1</mfAllowHistory>\r\n               <mfAllowMarketResearch>1</mfAllowMarketResearch>\r\n               <mfAllowThirdParty>1</mfAllowThirdParty>\r\n            </PrivacyOption>\r\n            <ResortList>SANDBOX-OXI3</ResortList>\r\n            <MultiResortEntities>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>CREDIT_CARDS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>RESORT_ARS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>COMMENTS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>PREFERENCES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>NEGOTIATED_RATES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>RESORT_NEG_RATES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>CHANNEL_ACCESS_CODES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>YIELD_ADJUSTMENTS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>RELATIONSHIPS</entity>\r\n               </MultiResortEntity>\r\n            </MultiResortEntities>\r\n         </Profile>\r\n         <resProfileRPH>0</resProfileRPH>\r\n      </ResProfile>\r\n   </ResProfiles>\r\n   <RoomStays>\r\n      <RoomStay mfShareAction=\"NA\" mfReservationAction=\"NA\" reservationActionType=\"CHANGE\" reservationStatusType=\"RESERVED\">\r\n         <roomInventoryCode>STD</roomInventoryCode>\r\n         <TimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-12-01T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>1</numberOfTimeUnits>\r\n         </TimeSpan>\r\n         <GuestCounts>\r\n            <GuestCount>\r\n               <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n               <mfCount>2</mfCount>\r\n            </GuestCount>\r\n            <GuestCount>\r\n               <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n               <mfCount>0</mfCount>\r\n            </GuestCount>\r\n         </GuestCounts>\r\n         <RatePlans>\r\n            <RatePlan reservationActionType=\"CHANGE\">\r\n               <ratePlanRPH>0</ratePlanRPH>\r\n               <ratePlanCode>BAR0</ratePlanCode>\r\n               <TimeSpan timeUnitType=\"DAY\">\r\n                  <startTime>2016-12-01T00:00:00.000</startTime>\r\n                  <numberOfTimeUnits>1</numberOfTimeUnits>\r\n               </TimeSpan>\r\n               <mfMarketCode>LTRAN</mfMarketCode>\r\n               <Rates>\r\n                  <Rate reservationActionType=\"CHANGE\" rateBasisTimeUnitType=\"DAY\">\r\n                     <rateRPH>0</rateRPH>\r\n                     <Amount currencyCode=\"USD\">\r\n                        <valueNum>325</valueNum>\r\n                     </Amount>\r\n                     <rateBasisUnits>1</rateBasisUnits>\r\n                     <TimeSpan timeUnitType=\"DAY\">\r\n                        <startTime>2016-12-01T00:00:00.000</startTime>\r\n                        <numberOfTimeUnits>1</numberOfTimeUnits>\r\n                     </TimeSpan>\r\n                     <mfAdults>2</mfAdults>\r\n                     <mfChildren>0</mfChildren>\r\n                     <mfCribs>0</mfCribs>\r\n                     <mfExtraBeds>0</mfExtraBeds>\r\n                     <mfsourceCode>WI</mfsourceCode>\r\n                     <mfMarketCode>LTRAN</mfMarketCode>\r\n                  </Rate>\r\n               </Rates>\r\n               <mfsourceCode>WI</mfsourceCode>\r\n            </RatePlan>\r\n         </RatePlans>\r\n         <marketSegmentCode>LTRAN</marketSegmentCode>\r\n         <resGuestRPHs>0</resGuestRPHs>\r\n         <GuaranteeInfo guaranteeType=\"NA\">\r\n            <mfGuaranteeType>6PM</mfGuaranteeType>\r\n            <GuaranteeDeposit>\r\n               <Amount currencyCode=\"USD\"/>\r\n               <cancelIfNotReceived>0</cancelIfNotReceived>\r\n            </GuaranteeDeposit>\r\n         </GuaranteeInfo>\r\n         <CancelPenalties>\r\n            <CancelPenalty mfRuleType=\"CANCEL\">\r\n               <cancelByDate>2016-11-29T12:00:00.000</cancelByDate>\r\n               <Amount currencyCode=\"USD\">\r\n                  <valueNum>169</valueNum>\r\n               </Amount>\r\n               <mfRuleScope>R</mfRuleScope>\r\n               <mfPercentage>50</mfPercentage>\r\n               <mfCancelPercentDue>100</mfCancelPercentDue>\r\n               <mfRuleDescription>Cancel 48 Hours prior to arrival - 50% penalty</mfRuleDescription>\r\n               <ruleCode>48H50P</ruleCode>\r\n            </CancelPenalty>\r\n         </CancelPenalties>\r\n         <PaymentInstructions>\r\n            <PaymentInstruction paymentMethodType=\"NA\">\r\n               <mfPaymentMethod>CA</mfPaymentMethod>\r\n               <PaymentDue>\r\n                  <Amount currencyCode=\"USD\"/>\r\n                  <cancelIfNotReceived>0</cancelIfNotReceived>\r\n               </PaymentDue>\r\n            </PaymentInstruction>\r\n         </PaymentInstructions>\r\n         <mfsourceCode>WI</mfsourceCode>\r\n         <mfchannelCode>PMS</mfchannelCode>\r\n         <mfconfidentialRate>0</mfconfidentialRate>\r\n         <mfAsbProrated>0</mfAsbProrated>\r\n      </RoomStay>\r\n   </RoomStays>\r\n   <resProfileRPHs>0</resProfileRPHs>\r\n   <mfupdateDate>2015-08-06T10:38:04.000</mfupdateDate>\r\n   <mfcomplementary>0</mfcomplementary>\r\n   <mfImage>\r\n      <numRooms>1</numRooms>\r\n      <Describe>\r\n         <resortName>Property 1 Opera Demo Multi Hotel</resortName>\r\n         <insertUser>SUPERVISOR</insertUser>\r\n         <updateUser>SUPERVISOR</updateUser>\r\n         <roomCategory>Standard - These rooms measure 30 square metres (325 square feet).  They include a comfortable chair for relaxation, an antique writing desk and cordless phone.  The marble bathroom offers a large vanity with basin, and combination tub and shower test.</roomCategory>\r\n         <rateCode>Room Charge</rateCode>\r\n         <marketCode>Local Transient</marketCode>\r\n         <guarantee>6PM: 6pm Guarantee</guarantee>\r\n         <company>Micros Hotels and Resorts</company>\r\n      </Describe>\r\n      <Change>\r\n         <bSource>0</bSource>\r\n         <bArrivalDate>0</bArrivalDate>\r\n         <bNumNights>0</bNumNights>\r\n         <bNumAdults>0</bNumAdults>\r\n         <bNumChildren>0</bNumChildren>\r\n         <bNumRooms>0</bNumRooms>\r\n         <bCribs>0</bCribs>\r\n         <bRoomCategory>0</bRoomCategory>\r\n         <bRateCode>0</bRateCode>\r\n         <bRateAmount>1</bRateAmount>\r\n         <bMarketCode>0</bMarketCode>\r\n         <bPaymentType>0</bPaymentType>\r\n         <bGuarType>0</bGuarType>\r\n         <bDiscountReason>0</bDiscountReason>\r\n         <bMultipleRateYN>0</bMultipleRateYN>\r\n         <bResvStatus>0</bResvStatus>\r\n      </Change>\r\n   </mfImage>\r\n</Reservation>\r\n"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/ngipublic/rest/oxi/pms", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["ngipublic", "rest", "oxi", "pms"]}}, "response": []}, {"name": "When EOD for 2016-07-05", "event": [{"listen": "test", "script": {"id": "a82de31b-86b4-458c-9ba8-4f3e1f92776d", "exec": ["pm.globals.unset(\"fiscal_Date\");", "pm.globals.set(\"fiscal_Date\",\"2016-07-05\");", "", "//verifing status", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{hostName}}:{{ngiPort}}/oxi/admin/pms/endOfDay?propertyCode=OPER1&clientCode=SandBox&fiscalDate=2016-07-05T02:01:01-04:00", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["oxi", "admin", "pms", "endOfDay"], "query": [{"key": "propertyCode", "value": "OPER1"}, {"key": "clientCode", "value": "SandBox"}, {"key": "fiscalDate", "value": "2016-07-05T02:01:01-04:00"}]}}, "response": []}, {"name": "Then unique correlationId populates in nucleusStatisticsCorrelations", "event": [{"listen": "test", "script": {"id": "74fb06f6-6967-4003-9cf7-dd7d7e5dd298", "exec": ["tv4.validate", "var jsonData = pm.response.json();", "", "function pollDOM () {", "   console.log(\"current status is\" + jsonData.status)", "  if (jsonData.status == \"SENT_FOR_PROCESSING\"|| jsonData.status == \"PROCESSED\") {", "    //Setting global variables", "    pm.globals.unset(\"corrID\");", "    pm.globals.set(\"corrID\",jsonData.id);", "    pm.globals.set(\"JobType\",\"NGIDeferredDeliveryJob\");", "    value = jsonData.activityStats.CURRENT_ROOM_TYPE_MARKET_SEGMENT.correlationId;", "    pm.globals.unset(\"correlationId\");", "    pm.globals.set(\"correlationId\",value);", "    console.log('query fetched from parameter corrID is -'+pm.globals.get(\"corrID\"));", "    //Setting JobID", "    eval(pm.globals.get(\"getJobID\"));", "  } else {", "      pm.sendRequest({url: pm.globals.get('hostName')+':'+pm.globals.get('ngiPort')+'/nucleusStatisticsCorrelations/search/findByClientCodeAndPropertyCodeAndFiscalDate?clientCode='+ pm.globals.get('client_Code')+'&propertyCode='+pm.globals.get('property_Code')+'&fiscalDate='+pm.globals.get('fiscal_Date'),method: 'GET',header:{'Authorization': 'Basic ********************************','Accept': '*/*'}}, function (err, res) {", "            jsonData = res.json();", "            console.log(\"Status Inside: \" + jsonData.status);", "            console.log(\"Response\" + JSON.stringify(res.json()));", "        });", "    jsonData = pm.response.json();", "    console.log(\"Inside Data\" + JSON.stringify(jsonData));", "    setTimeout(pollDOM, 2000); // try again in 300 milliseconds", "  }", "}", "pollDOM();"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "e3bd4532-a129-4d11-a921-23ab7c730e93", "exec": ["var millisecondsToWait = 5000;\r", "setTimeout(function() {\r", "    // Whatever you want to do after the wait\r", "}, millisecondsToWait);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusStatisticsCorrelations/search/findByClientCodeAndPropertyCodeAndFiscalDate?clientCode={{client_Code}}&propertyCode={{property_Code}}&fiscalDate={{fiscal_Date}}", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusStatisticsCorrelations", "search", "findByClientCodeAndPropertyCodeAndFiscalDate"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "fiscalDate", "value": "{{fiscal_Date}}"}]}}, "response": []}, {"name": "Then NGIDeferredDelivery Job gets triggered in Monitoring Dashboard", "event": [{"listen": "test", "script": {"id": "db506039-8e7c-4c57-ae48-66b5270ee81c", "exec": ["eval(pm.globals.get(\"waitForJobToComplete\"));", "", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "propertyid", "type": "text", "value": "5"}, {"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{ \"query\": \"select EXECUTION_STATUS from [Job].[dbo].JOB_STATE where job_instance_id={{jobId}}\"\n}"}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/G3Tables/executeQuery/v1", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "G3Tables", "execute<PERSON>uery"]}}, "response": []}, {"name": "Then the mapping of reservation is also stored in oxiShareReservationReferences Collection", "event": [{"listen": "test", "script": {"id": "65d83d60-e444-4821-b7c4-d38e04917e6f", "exec": ["//variables", "var jsonData = pm.response.json();", "", "//value to compare with", "var reservationJSONToBeCompared = {\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"reservationId\":\"946904\",\"primaryReservationId\":\"946904\",\"shareCode\":\"1169037\",\"sharers\":[{\"reservationId\":\"946904\",\"shareParent\":true,\"startedOn\":\"2015-07-02T16:20:10Z\",\"endedOn\":null,\"arrivalDate\":\"2016-12-01\",\"departureDate\":\"2016-12-02\",\"revenue\":null,\"participating\":true}]};", "", "var reservationJSONToBeCompared1 = {\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"reservationId\":\"946904\",\"primaryReservationId\":\"946904\",\"shareCode\":\"1169037\",\"sharers\":[{\"reservationId\":\"946904\",\"shareParent\":true,\"startedOn\":\"2015-07-02T16:20:10Z\",\"endedOn\":null,\"arrivalDate\":\"2016-12-01\",\"departureDate\":\"2016-12-02\",\"revenue\":null,\"participating\":true}]};", "", "//assertions", "arr = [];", "arr.push(reservationJSONToBeCompared,reservationJSONToBeCompared1);", "", "//Calling Comparisons", "for (item of arr) {", "     pm.globals.unset(\"actualjson\");", "    pm.globals.set(\"actualjson\", jsonData);", "    pm.globals.unset(\"expectedjson\");", "    pm.globals.set(\"expectedjson\", item);", "    eval(pm.globals.get(\"verifySharesReservationCollection\"));", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/oxiShareReservationReferences/search/findOneByClientCodeAndPropertyCodeAndReservationId?clientCode={{client_Code}}&propertyCode={{property_Code}}&reservationId=946904&size=100&sort=reservationId,desc", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["oxiShareReservationReferences", "search", "findOneByClientCodeAndPropertyCodeAndReservationId"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "reservationId", "value": "946904"}, {"key": "size", "value": "100"}, {"key": "sort", "value": "reservationId,desc"}]}}, "response": []}, {"name": "Then Summary is build for all the share reservation and sold is updated accordingly", "event": [{"listen": "test", "script": {"id": "0bd453e2-26f7-4f98-83b8-9c7d3265b87b", "exec": ["//variables", "var jsonData = pm.response.json();", "var totalDocuments =  jsonData.page.totalElements;", "", "", "delete jsonData[\"_links\"];", "delete jsonData[\"page\"];", "", "for (var i=0;i<totalDocuments;i++){", "   ", "//delete jsonData[\"page\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"_links\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"pseudoRoom\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"dayUse\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"statisticsCorrelationId\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"createDate\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"lastModifiedDate\"];", "}", "", "", "//value to compare with", "var reservationJSONToBeCompared = JSON.parse('{\"_embedded\":{\"nucleusOccupancySummaries\":[{\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"reservationId\":\"946904\",\"reservationStatus\":\"RESERVED\",\"occupancyDate\":\"2016-12-02 00:00:00.000\",\"marketSegmentCode\":\"LTRAN\",\"roomTypeCode\":\"STD\",\"roomNumber\":null,\"rateCode\":\"BAR0\",\"block\":null,\"pickup\":null,\"groupOccupancy\":false,\"roomsSold\":0,\"transientSold\":null,\"groupSold\":null,\"roomRevenue\":0,\"foodRevenue\":0,\"beverageRevenue\":0,\"otherRevenue\":0,\"totalRevenue\":0,\"noShowRoomRevenue\":null,\"arrivals\":0,\"departures\":1,\"cancellations\":0,\"noShows\":0,\"accomCapacity\":null,\"roomsNotAvailableMaintenance\":null,\"roomsNotAvailableOther\":null,\"groupId\":null,\"roomRate\":0,\"correlationId\":null,\"amsInPlay\":false,\"analyticalMarketSegmentCode\":\"LTRAN\",\"groupStatus\":null},{\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"reservationId\":\"946904\",\"reservationStatus\":\"RESERVED\",\"occupancyDate\":\"2016-12-01 00:00:00.000\",\"marketSegmentCode\":\"LTRAN\",\"roomTypeCode\":\"STD\",\"roomNumber\":null,\"rateCode\":\"BAR0\",\"block\":null,\"pickup\":null,\"groupOccupancy\":false,\"roomsSold\":1,\"transientSold\":null,\"groupSold\":null,\"roomRevenue\":325,\"foodRevenue\":0,\"beverageRevenue\":0,\"otherRevenue\":0,\"totalRevenue\":325,\"noShowRoomRevenue\":null,\"arrivals\":1,\"departures\":0,\"cancellations\":0,\"noShows\":0,\"accomCapacity\":null,\"roomsNotAvailableMaintenance\":null,\"roomsNotAvailableOther\":null,\"groupId\":null,\"roomRate\":325,\"correlationId\":null,\"amsInPlay\":false,\"analyticalMarketSegmentCode\":\"LTRAN\",\"groupStatus\":null}]}}');", "", "//assertions", "assertJSONData(\"Verifying Client Code in OxiMessage\", jsonData,reservationJSONToBeCompared);", "", "", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue).to.eql(expectedValue);", "});}", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusOccupancySummaries/criteria?search=clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BreservationId%3Din%3D%28946904%29&size=300&sort=occupancyDate,desc", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusOccupancySummaries", "criteria"], "query": [{"key": "search", "value": "clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BreservationId%3Din%3D%28946904%29"}, {"key": "size", "value": "300"}, {"key": "sort", "value": "occupancyDate,desc"}]}}, "response": []}, {"name": "Then NGI updates nucleusRoomTypeMarketSegmentActivity collection Copy", "event": [{"listen": "test", "script": {"id": "d10718a6-ef96-4b19-ba11-7edf46416420", "exec": ["var jsonData = pm.response.json();", "jsonData =  jsonData._embedded.nucleusRoomTypeMarketSegmentShardedActivities;", "", "console.log(jsonData);", "", "var nucleusRoomTypeActivityJSON = {\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"roomsSold\":0,\"arrivals\":0,\"departures\":1,\"cancellations\":null,\"noShows\":null,\"roomRevenue\":0,\"foodRevenue\":0,\"totalRevenue\":0,\"occupancyDate\":\"2016-12-02 00:00:00.000\",\"fiscalDate\":\"2016-07-05 00:00:00.000\",\"noShowRoomRevenue\":null,\"marketSegmentCode\":\"LTRAN\",\"roomTypeCode\":\"STD\",\"provided\":false};", "", "var nucleusRoomTypeActivityJSON1 = {\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"roomsSold\":1,\"arrivals\":1,\"departures\":0,\"cancellations\":null,\"noShows\":null,\"roomRevenue\":325,\"foodRevenue\":0,\"totalRevenue\":325,\"occupancyDate\":\"2016-12-01 00:00:00.000\",\"fiscalDate\":\"2016-07-05 00:00:00.000\",\"noShowRoomRevenue\":null,\"marketSegmentCode\":\"LTRAN\",\"roomTypeCode\":\"STD\",\"provided\":false}", "", "arr = [];", "arr.push(nucleusRoomTypeActivityJSON,nucleusRoomTypeActivityJSON1);", "", "//Calling assertions", "for (item of arr) {", "pm.globals.unset(\"actualjson\");", "pm.globals.set(\"actualjson\", jsonData);", "pm.globals.unset(\"expectedjson\");", "pm.globals.set(\"expectedjson\", nucleusRoomTypeActivityJSON);", "eval(pm.globals.get(\"VerifynucleusRTMSActivities\"));", "}", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusRoomTypeMarketSegmentShardedActivities/criteria?search=clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BcorrelationId%3D%3D{{correlationId}}&sort=occupancyDate,desc", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusRoomTypeMarketSegmentShardedActivities", "criteria"], "query": [{"key": "search", "value": "clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BcorrelationId%3D%3D{{correlationId}}"}, {"key": "sort", "value": "occupancyDate,desc"}]}}, "response": []}, {"name": "Then the individualTrans Table in G3 gets updated", "event": [{"listen": "test", "script": {"id": "5b515479-f628-49dc-8f83-744309b189aa", "exec": ["//variables", "var jsonData = pm.response.json();", "", "pm.globals.set(\"File_Metadata_ID\",jsonData[0][0]);", "", "var individualTransData = ('[' + pm.globals.get('File_Metadata_ID') + ',\"946904\",\"SS\",\"2016-12-01\",\"2016-12-02\",\"2015-07-02\",null,\"DLX\",\"LTRAN\",\"STD\",325,null,null,null,null,325,\"WI\",\"US\",\"BAR0\",325,null,\"6PM\",0,2,null,\"PMS\"]');", "", "", "arr = [];", "arr.push(individualTransData);", "", "//Calling assertions", "for (item of arr) {", "    pm.globals.unset(\"actualjson\");", "    pm.globals.set(\"actualjson\", jsonData);", "    pm.globals.unset(\"expectedjson\");", "    pm.globals.set(\"expectedjson\", item);", "    eval(pm.globals.get(\"VerifyG3TableValue\"));", "}"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "cb7384a1-7586-4e82-8f45-3e320c9949d2", "exec": ["pm.globals.unset(\"whereCondition\");\r", "pm.globals.set(\"whereCondition\", \" where reservation_identifier in ('946904','946905') \");\r", "var millisecondsToWait = 5000;\r", "setTimeout(function() {\r", "    // Whatever you want to do after the wait\r", "}, millisecondsToWait);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "propertyId", "type": "text", "value": "5"}], "body": {"mode": "raw", "raw": "{ \"query\": \"{{individualTransQuery}}\"\n}"}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/G3Tables/executeQuery/v1", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "G3Tables", "execute<PERSON>uery"]}}, "response": []}, {"name": "Then the Reservation_Night Table in G3 gets populated", "event": [{"listen": "test", "script": {"id": "cd20d502-98e8-4950-a6d2-363495133ec7", "exec": ["//variables", "var jsonData = pm.response.json();", "pm.globals.set(\"File_Metadata_ID\",jsonData[0][0]);", "", "var reservationNightData = ('[' + pm.globals.get('File_Metadata_ID') + ',\"946904\",\"RESERVED\",\"2016-12-01\",\"2016-12-02\",\"2015-07-02\",null,\"DLX\",\"LTRAN\",\"STD\",325,null,null,null,null,325,\"WI\",\"US\",\"BAR0\",325,null,\"6PM\",0,2,null,\"PMS\",null,null,\"LTRAN\"]');", "", "", "arr = [];", "arr.push(reservationNightData);", "", "//Calling assertions", "for (item of arr) {", "    pm.globals.unset(\"actualjson\");", "    pm.globals.set(\"actualjson\", jsonData);", "    pm.globals.unset(\"expectedjson\");", "    pm.globals.set(\"expectedjson\", item);", "    eval(pm.globals.get(\"VerifyG3TableValue\"));", "}"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "9168210a-94ea-41a6-813e-c91dff1616ec", "exec": ["pm.globals.unset(\"whereCondition\");\r", "pm.globals.set(\"whereCondition\", \" where reservation_identifier in ('946904','946905') \");"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "propertyId", "type": "text", "value": "5"}], "body": {"mode": "raw", "raw": "{ \"query\": \"{{reservationNightQuery}}\"\n}"}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/G3Tables/executeQuery/v1", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "G3Tables", "execute<PERSON>uery"]}}, "response": []}, {"name": "Then Mkt_Accom_Activity Table gets populated with solds", "event": [{"listen": "test", "script": {"id": "63d762ac-a4c3-4d4d-a308-b95629c301ca", "exec": ["//variables", "var jsonData = pm.response.json();", "", "arr = [];", "", "var totalActivity = ('[\"2016-12-01\",\"LTRAN\",\"STD\",1,1,0,0,0,325,0,325,0]');", "var totalActivity1 = ('[\"2016-12-02\",\"LTRAN\",\"STD\",0,0,1,0,0,0,0,0,0]');", "", "", "arr.push(totalActivity, totalActivity1, );", "", "//Calling assertions", "for (item of arr) {", "    pm.globals.unset(\"actualjson\");", "    pm.globals.set(\"actualjson\", jsonData);", "    pm.globals.unset(\"expectedjson\");", "    pm.globals.set(\"expectedjson\", item);", "    eval(pm.globals.get(\"VerifyG3TableValue\"));", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "propertyId", "type": "text", "value": "5"}], "body": {"mode": "raw", "raw": "{ \"query\": \"{{mktAccomActivityQuery}}\"\n}"}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/G3Tables/executeQuery/v1", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "G3Tables", "execute<PERSON>uery"]}}, "response": []}, {"name": "When break for sharer reservation with Id=946905 is sent", "event": [{"listen": "prerequest", "script": {"id": "74fa41e5-e2ea-466f-956d-4fcb8cfc46a0", "exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"id": "96d08d8b-3f61-4a48-89ab-0a2f901cbdd1", "exec": ["//verifing status", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "text/xml"}], "body": {"mode": "raw", "raw": "<?xml version = '1.0' encoding = 'UTF-8'?>\r\n<?Label SandBox-OPER1|RESERVATION|20151106006|SUCCESS?>\r\n<!--Reservation_ShareBaseCase_Msg6: Update with Break Share for Secondary Guest\r\nIn his message: Reservation 946905 for 1 night, 1 adults at $150 (Had not seen this reservation until now)\r\nNotes:\r\noriginalBookingDate changed to time this guest was added to the share\r\n/Reservation/RoomStays/RoomStay/resGuestRPHs = 0 now pointing to only guest with reservation 946905\r\n/Reservation/RoomStays/RoomStay/mfsharedGuestRPHs no longer defined\r\n/Reservation/RoomStays/RoomStay/mfshareCode no longer defined\r\nBooking date reflects time it joined the share, no need to revise it -->\r\n<Reservation xmlns=\"reservation.fidelio.5.0\" mfShareAction=\"NA\" mfReservationAction=\"EDIT\">\r\n   <HotelReference>\r\n      <hotelCode>SandBox-OPER1</hotelCode>\r\n   </HotelReference>\r\n   <reservationID>946905</reservationID>\r\n   <reservationOriginatorCode>SUPERVISOR</reservationOriginatorCode>\r\n   <originalBookingDate>2015-07-02T11:20:00.000</originalBookingDate>\r\n   <StayDateRange timeUnitType=\"DAY\">\r\n      <startTime>2016-12-01T00:00:00.000</startTime>\r\n      <numberOfTimeUnits>1</numberOfTimeUnits>\r\n   </StayDateRange>\r\n   <GuestCounts>\r\n      <GuestCount>\r\n         <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n         <mfCount>1</mfCount>\r\n      </GuestCount>\r\n      <GuestCount>\r\n         <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n         <mfCount>0</mfCount>\r\n      </GuestCount>\r\n   </GuestCounts>\r\n   <ResGuests>\r\n      <ResGuest reservationActionType=\"CHANGE\">\r\n         <resGuestRPH>0</resGuestRPH>\r\n         <profileRPHs>0</profileRPHs>\r\n         <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n         <InHouseTimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-12-01T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>1</numberOfTimeUnits>\r\n         </InHouseTimeSpan>\r\n         <ArrivalTransport/>\r\n         <DepartureTransport/>\r\n         <arrivalTime>2016-12-01T00:00:00.000</arrivalTime>\r\n         <departureTime>2016-12-02T00:00:00.000</departureTime>\r\n         <reservationID>946905</reservationID>\r\n         <ReservationReferences>\r\n            <ReservationReference type=\"GUESTID\" referenceNumber=\"946905\" legNumber=\"1\"/>\r\n            <ReservationReference type=\"PMSID\" referenceNumber=\"946905\" legNumber=\"1\"/>\r\n         </ReservationReferences>\r\n         <preRegistered>0</preRegistered>\r\n         <commissionPaidTo>N</commissionPaidTo>\r\n      </ResGuest>\r\n   </ResGuests>\r\n   <ResProfiles>\r\n      <ResProfile>\r\n         <Profile xmlns=\"profile.fidelio.5.0\" profileType=\"GUEST\" gender=\"M\" multiProperty=\"0\" miniProfile=\"0\" pseudoProfile=\"0\" replaceAddress=\"0\">\r\n            <creatorCode>OEDS$ADS</creatorCode>\r\n            <createdDate>2009-11-11T17:16:27.000</createdDate>\r\n            <lastUpdaterCode>SUPERVISOR</lastUpdaterCode>\r\n            <lastUpdated>2015-06-04T15:52:56.000</lastUpdated>\r\n            <genericName>Smith</genericName>\r\n            <IndividualName>\r\n               <namePrefix>Mr</namePrefix>\r\n               <nameFirst>Abby</nameFirst>\r\n               <nameSur>Smith</nameSur>\r\n               <nameSuffix>12</nameSuffix>\r\n            </IndividualName>\r\n            <primaryLanguageID>E</primaryLanguageID>\r\n            <Memberships>\r\n               <Membership>\r\n                  <programCode>AA</programCode>\r\n                  <accountID>56478</accountID>\r\n                  <startDate>2014-06-13</startDate>\r\n                  <nameOnCard>ABBY SMITH</nameOnCard>\r\n                  <displaySequence>100</displaySequence>\r\n                  <pointIndicator>0</pointIndicator>\r\n                  <enrollmentSource>N</enrollmentSource>\r\n                  <enrolledAt>N</enrolledAt>\r\n               </Membership>\r\n               <Membership>\r\n                  <programCode>OCIS</programCode>\r\n                  <accountID>987654</accountID>\r\n                  <levelCode>BRON</levelCode>\r\n                  <expireDate>2016-08-31</expireDate>\r\n                  <startDate>2014-06-13</startDate>\r\n                  <nameOnCard>ABBY SMITH</nameOnCard>\r\n                  <displaySequence>50</displaySequence>\r\n                  <enrollmentCode>HOT</enrollmentCode>\r\n                  <pointIndicator>1</pointIndicator>\r\n                  <enrollmentSource>N</enrollmentSource>\r\n                  <enrolledAt>N</enrolledAt>\r\n               </Membership>\r\n               <Membership>\r\n                  <programCode>OR</programCode>\r\n                  <accountID>741852</accountID>\r\n                  <startDate>2014-06-13</startDate>\r\n                  <nameOnCard>ABBY SMITH</nameOnCard>\r\n                  <displaySequence>75</displaySequence>\r\n                  <pointIndicator>0</pointIndicator>\r\n                  <enrollmentSource>N</enrollmentSource>\r\n                  <enrolledAt>N</enrolledAt>\r\n               </Membership>\r\n            </Memberships>\r\n            <PostalAddresses>\r\n               <PostalAddress addressType=\"HOME\">\r\n                  <address1>67 Westwood Parkway5</address1>\r\n                  <city>Lynnwood</city>\r\n                  <stateCode>WA</stateCode>\r\n                  <postalCode>98037</postalCode>\r\n                  <countryCode>US</countryCode>\r\n                  <mfPrimaryYN>Y</mfPrimaryYN>\r\n                  <mfAddressLanguage>E</mfAddressLanguage>\r\n                  <cleansed>0</cleansed>\r\n               </PostalAddress>\r\n            </PostalAddresses>\r\n            <PhoneNumbers>\r\n               <PhoneNumber phoneNumberType=\"HOME\">\r\n                  <phoneNumber>(123)456-7890</phoneNumber>\r\n                  <mfPrimaryYN>Y</mfPrimaryYN>\r\n                  <confirmation>0</confirmation>\r\n               </PhoneNumber>\r\n               <PhoneNumber phoneNumberType=\"BUSINESS\">\r\n                  <phoneNumber>************</phoneNumber>\r\n                  <mfPrimaryYN>N</mfPrimaryYN>\r\n                  <confirmation>0</confirmation>\r\n               </PhoneNumber>\r\n            </PhoneNumbers>\r\n            <SpecialRequests>\r\n               <SpecialRequest mfSpecialRequestType=\"SPE\">\r\n                  <requestCode>HOTSOS</requestCode>\r\n                  <requestComments>HotSOS Test</requestComments>\r\n                  <mfResort>SANDBOX-OXI3</mfResort>\r\n               </SpecialRequest>\r\n            </SpecialRequests>\r\n            <mfResort>SANDBOX-OXI3</mfResort>\r\n            <mfResortProfileID>83830</mfResortProfileID>\r\n            <mfAllowMail>YES</mfAllowMail>\r\n            <mfAllowEMail>NO</mfAllowEMail>\r\n            <mfGuestPriv>NO</mfGuestPriv>\r\n            <mfAllowPhone>0</mfAllowPhone>\r\n            <mfAllowSMS>0</mfAllowSMS>\r\n            <SalesExtention/>\r\n            <PrivacyOption>\r\n               <mfAllowMail>Y</mfAllowMail>\r\n               <mfAllowEMail>N</mfAllowEMail>\r\n               <mfAllowPhone>0</mfAllowPhone>\r\n               <mfAllowSMS>0</mfAllowSMS>\r\n               <mfAllowHistory>1</mfAllowHistory>\r\n               <mfAllowMarketResearch>1</mfAllowMarketResearch>\r\n               <mfAllowThirdParty>1</mfAllowThirdParty>\r\n            </PrivacyOption>\r\n            <ResortList>SANDBOX-OXI3</ResortList>\r\n            <MultiResortEntities>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>CREDIT_CARDS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>RESORT_ARS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>COMMENTS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>PREFERENCES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>NEGOTIATED_RATES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>RESORT_NEG_RATES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>CHANNEL_ACCESS_CODES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>YIELD_ADJUSTMENTS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>RELATIONSHIPS</entity>\r\n               </MultiResortEntity>\r\n            </MultiResortEntities>\r\n            <ResortSpecialRequests>\r\n               <SpecialRequest mfSpecialRequestType=\"SPE\">\r\n                  <requestCode>HOTSOS</requestCode>\r\n                  <requestComments>HotSOS Test</requestComments>\r\n                  <mfResort>SANDBOX-OXI3</mfResort>\r\n               </SpecialRequest>\r\n            </ResortSpecialRequests>\r\n         </Profile>\r\n         <resProfileRPH>0</resProfileRPH>\r\n      </ResProfile>\r\n   </ResProfiles>\r\n   <RoomStays>\r\n      <RoomStay mfShareAction=\"NA\" mfReservationAction=\"NA\" reservationActionType=\"CHANGE\" reservationStatusType=\"RESERVED\">\r\n         <roomInventoryCode>STD</roomInventoryCode>\r\n         <TimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-12-01T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>1</numberOfTimeUnits>\r\n         </TimeSpan>\r\n         <GuestCounts>\r\n            <GuestCount>\r\n               <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n               <mfCount>1</mfCount>\r\n            </GuestCount>\r\n            <GuestCount>\r\n               <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n               <mfCount>0</mfCount>\r\n            </GuestCount>\r\n         </GuestCounts>\r\n         <RatePlans>\r\n            <RatePlan reservationActionType=\"CHANGE\">\r\n               <ratePlanRPH>0</ratePlanRPH>\r\n               <ratePlanCode>BAR0</ratePlanCode>\r\n               <TimeSpan timeUnitType=\"DAY\">\r\n                  <startTime>2016-12-01T00:00:00.000</startTime>\r\n                  <numberOfTimeUnits>1</numberOfTimeUnits>\r\n               </TimeSpan>\r\n               <mfMarketCode>LTRAN</mfMarketCode>\r\n               <Rates>\r\n                  <Rate reservationActionType=\"CHANGE\" rateBasisTimeUnitType=\"DAY\">\r\n                     <rateRPH>0</rateRPH>\r\n                     <Amount currencyCode=\"USD\">\r\n                        <valueNum>150</valueNum>\r\n                     </Amount>\r\n                     <rateBasisUnits>1</rateBasisUnits>\r\n                     <TimeSpan timeUnitType=\"DAY\">\r\n                        <startTime>2016-12-01T00:00:00.000</startTime>\r\n                        <numberOfTimeUnits>1</numberOfTimeUnits>\r\n                     </TimeSpan>\r\n                     <mfAdults>1</mfAdults>\r\n                     <mfChildren>0</mfChildren>\r\n                     <mfCribs>0</mfCribs>\r\n                     <mfExtraBeds>0</mfExtraBeds>\r\n                     <mfsourceCode>WI</mfsourceCode>\r\n                     <mfMarketCode>LTRAN</mfMarketCode>\r\n                  </Rate>\r\n               </Rates>\r\n               <mfsourceCode>WI</mfsourceCode>\r\n            </RatePlan>\r\n         </RatePlans>\r\n         <marketSegmentCode>LTRAN</marketSegmentCode>\r\n         <resGuestRPHs>0</resGuestRPHs>\r\n         <selectedMembershipRPHs>0</selectedMembershipRPHs>\r\n         <GuaranteeInfo guaranteeType=\"NA\">\r\n            <mfGuaranteeType>6PM</mfGuaranteeType>\r\n            <GuaranteeDeposit>\r\n               <Amount currencyCode=\"USD\"/>\r\n               <cancelIfNotReceived>0</cancelIfNotReceived>\r\n            </GuaranteeDeposit>\r\n         </GuaranteeInfo>\r\n         <CancelPenalties>\r\n            <CancelPenalty mfRuleType=\"CANCEL\">\r\n               <cancelByDate>2016-11-29T12:00:00.000</cancelByDate>\r\n               <Amount currencyCode=\"USD\">\r\n                  <valueNum>88.4</valueNum>\r\n               </Amount>\r\n               <mfRuleScope>R</mfRuleScope>\r\n               <mfPercentage>50</mfPercentage>\r\n               <mfCancelPercentDue>100</mfCancelPercentDue>\r\n               <mfRuleDescription>Cancel 48 Hours prior to arrival - 50% penalty</mfRuleDescription>\r\n               <ruleCode>48H50P</ruleCode>\r\n            </CancelPenalty>\r\n         </CancelPenalties>\r\n         <PaymentInstructions>\r\n            <PaymentInstruction paymentMethodType=\"NA\">\r\n               <mfPaymentMethod>CA</mfPaymentMethod>\r\n               <PaymentDue>\r\n                  <Amount currencyCode=\"USD\"/>\r\n                  <cancelIfNotReceived>0</cancelIfNotReceived>\r\n               </PaymentDue>\r\n            </PaymentInstruction>\r\n         </PaymentInstructions>\r\n         <mfsourceCode>WI</mfsourceCode>\r\n         <mfchannelCode>PMS</mfchannelCode>\r\n         <mfconfidentialRate>0</mfconfidentialRate>\r\n         <mfAsbProrated>0</mfAsbProrated>\r\n      </RoomStay>\r\n   </RoomStays>\r\n   <SelectedMemberships>\r\n      <SelectedMembership reservationActionType=\"CHANGE\">\r\n         <selectedMembershipRPH>0</selectedMembershipRPH>\r\n         <programCode>OCIS</programCode>\r\n         <accountID>987654</accountID>\r\n         <mfmembershipCategory>BRON</mfmembershipCategory>\r\n      </SelectedMembership>\r\n   </SelectedMemberships>\r\n   <resProfileRPHs>0</resProfileRPHs>\r\n   <mfupdateDate>2015-08-06T10:38:04.000</mfupdateDate>\r\n   <mfcomplementary>0</mfcomplementary>\r\n   <mfImage>\r\n      <numRooms>1</numRooms>\r\n      <Describe>\r\n         <resortName>Property 1 Opera Demo Multi Hotel</resortName>\r\n         <insertUser>SUPERVISOR</insertUser>\r\n         <updateUser>SUPERVISOR</updateUser>\r\n         <roomCategory>Standard - These rooms measure 30 square metres (325 square feet).  They include a comfortable chair for relaxation, an antique writing desk and cordless phone.  The marble bathroom offers a large vanity with basin, and combination tub and shower test.</roomCategory>\r\n         <rateCode>Room Charge</rateCode>\r\n         <marketCode>Local Transient</marketCode>\r\n         <guarantee>6PM: 6pm Guarantee</guarantee>\r\n         <company>Micros Hotels and Resorts</company>\r\n      </Describe>\r\n      <Change>\r\n         <bSource>0</bSource>\r\n         <bArrivalDate>0</bArrivalDate>\r\n         <bNumNights>0</bNumNights>\r\n         <bNumAdults>0</bNumAdults>\r\n         <bNumChildren>0</bNumChildren>\r\n         <bNumRooms>0</bNumRooms>\r\n         <bCribs>0</bCribs>\r\n         <bRoomCategory>0</bRoomCategory>\r\n         <bRateCode>0</bRateCode>\r\n         <bRateAmount>1</bRateAmount>\r\n         <bMarketCode>0</bMarketCode>\r\n         <bPaymentType>0</bPaymentType>\r\n         <bGuarType>0</bGuarType>\r\n         <bDiscountReason>0</bDiscountReason>\r\n         <bMultipleRateYN>0</bMultipleRateYN>\r\n         <bResvStatus>0</bResvStatus>\r\n      </Change>\r\n   </mfImage>\r\n</Reservation>\r\n"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/ngipublic/rest/oxi/pms", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["ngipublic", "rest", "oxi", "pms"]}}, "response": []}, {"name": "When EOD for 2016-07-06", "event": [{"listen": "test", "script": {"id": "49af80b6-9bf4-404c-b919-bcf9b1322a9c", "exec": ["pm.globals.unset(\"fiscal_Date\");", "pm.globals.set(\"fiscal_Date\",\"2016-07-06\");", "", "//verifing status", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{hostName}}:{{ngiPort}}/oxi/admin/pms/endOfDay?propertyCode=OPER1&clientCode=SandBox&fiscalDate=2016-07-06T02:01:01-04:00", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["oxi", "admin", "pms", "endOfDay"], "query": [{"key": "propertyCode", "value": "OPER1"}, {"key": "clientCode", "value": "SandBox"}, {"key": "fiscalDate", "value": "2016-07-06T02:01:01-04:00"}]}}, "response": []}, {"name": "Then unique correlationId populates in nucleusStatisticsCorrelations", "event": [{"listen": "test", "script": {"id": "00b1ac64-cab7-47cb-94c8-42a0807aded1", "exec": ["tv4.validate", "var jsonData = pm.response.json();", "", "function pollDOM () {", "   console.log(\"current status is\" + jsonData.status)", "  if (jsonData.status == \"SENT_FOR_PROCESSING\"|| jsonData.status == \"PROCESSED\") {", "    //Setting global variables", "    pm.globals.unset(\"corrID\");", "    pm.globals.set(\"corrID\",jsonData.id);", "    pm.globals.set(\"JobType\",\"NGIDeferredDeliveryJob\");", "    value = jsonData.activityStats.CURRENT_ROOM_TYPE_MARKET_SEGMENT.correlationId;", "    pm.globals.unset(\"correlationId\");", "    pm.globals.set(\"correlationId\",value);", "    console.log('query fetched from parameter corrID is -'+pm.globals.get(\"corrID\"));", "    //Setting JobID", "    eval(pm.globals.get(\"getJobID\"));", "  } else {", "      pm.sendRequest({url: pm.globals.get('hostName')+':'+pm.globals.get('ngiPort')+'/nucleusStatisticsCorrelations/search/findByClientCodeAndPropertyCodeAndFiscalDate?clientCode='+ pm.globals.get('client_Code')+'&propertyCode='+pm.globals.get('property_Code')+'&fiscalDate='+pm.globals.get('fiscal_Date'),method: 'GET',header:{'Authorization': 'Basic ********************************','Accept': '*/*'}}, function (err, res) {", "            jsonData = res.json();", "            console.log(\"Status Inside: \" + jsonData.status);", "            console.log(\"Response\" + JSON.stringify(res.json()));", "        });", "    jsonData = pm.response.json();", "    console.log(\"Inside Data\" + JSON.stringify(jsonData));", "    setTimeout(pollDOM, 2000); // try again in 300 milliseconds", "  }", "}", "pollDOM();"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "9b09b9b3-d40a-4a42-8dd7-11848a4e4a20", "exec": ["var millisecondsToWait = 5000;\r", "setTimeout(function() {\r", "    // Whatever you want to do after the wait\r", "}, millisecondsToWait);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusStatisticsCorrelations/search/findByClientCodeAndPropertyCodeAndFiscalDate?clientCode={{client_Code}}&propertyCode={{property_Code}}&fiscalDate={{fiscal_Date}}", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusStatisticsCorrelations", "search", "findByClientCodeAndPropertyCodeAndFiscalDate"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "fiscalDate", "value": "{{fiscal_Date}}"}]}}, "response": []}, {"name": "Then NGIDeferredDelivery Job gets triggered in Monitoring Dashboard", "event": [{"listen": "test", "script": {"id": "3702d315-ead3-45e2-b135-c7294c<PERSON><PERSON>c", "exec": ["eval(pm.globals.get(\"waitForJobToComplete\"));", "", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "propertyid", "type": "text", "value": "5"}, {"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{ \"query\": \"select EXECUTION_STATUS from [Job].[dbo].JOB_STATE where job_instance_id={{jobId}}\"\n}"}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/G3Tables/executeQuery/v1", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "G3Tables", "execute<PERSON>uery"]}}, "response": []}, {"name": "Then  the reservation's multiReservationId updated as 946904 i.e it points to earlier primary sharer in nucleusReservation Collection in NGI", "event": [{"listen": "test", "script": {"id": "ec1c3a1a-d8a1-4be5-b72a-25c642aa4582", "exec": ["//variables", "var jsonData = pm.response.json();", "var totalDocuments =  jsonData.page.totalElements;", "", "", "delete jsonData[\"_links\"];", "delete jsonData[\"page\"];", "", "for (var i=0;i<totalDocuments;i++){", "delete jsonData[\"_links\"];", "delete jsonData[\"page\"];", "delete jsonData._embedded.nucleusReservations[i][\"_links\"];", "delete jsonData._embedded.nucleusReservations[i][\"correlationId\"];", "delete jsonData._embedded.nucleusReservations[i][\"statisticsCorrelationId\"];", "delete jsonData._embedded.nucleusReservations[i][\"createDate\"];", "delete jsonData._embedded.nucleusReservations[i][\"lastModifiedDate\"];", "}", "", "", "//value to compare with", "var reservationJSONToBeCompared =JSON.parse('{\"_embedded\":{\"nucleusReservations\":[{\"versionId\":20151106006,\"reservationId\":\"946905\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"bookingDate\":\"2015-07-02T11:18:31.000\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"RESERVED\",\"nationality\":\"US\",\"channel\":\"PMS\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2016-12-01\",\"departureDate\":\"2016-12-02\",\"marketCode\":\"LTRAN\",\"sourceBookingCode\":\"WI\",\"numberOfChildren\":0,\"numberOfAdults\":1,\"bookingType\":\"6PM\",\"invTypeCode\":\"STD\",\"bookedAccomTypeCode\":\"DLX\",\"rateCode\":\"BAR0\",\"invBlockCode\":null,\"roomNumber\":null,\"rates\":[{\"rateValue\":150,\"startDate\":\"2016-12-01\",\"endDate\":\"2016-12-02\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":150,\"currencyCode\":null}],\"services\":[],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"LTRAN\"}],\"multiReservationId\":\"946904\",\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"byDay\":false,\"profileId\":null,\"primary\":null,\"latestDepartureDate\":\"2016-12-02\",\"earliestArrivalDate\":\"2016-12-01\"}]}}');", "", "//assertions", "assertJSONData(\"Verifying multireservationId=946904 gets updated in Sharer reservation exists in nucleus Reservation\", jsonData,reservationJSONToBeCompared);", "", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue).to.eql(expectedValue);", "});}", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusReservations/criteria?search=clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BreservationId%3Din%3D%28946905%29&size=100&sort=reservationId,desc", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusReservations", "criteria"], "query": [{"key": "search", "value": "clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BreservationId%3Din%3D%28946905%29"}, {"key": "size", "value": "100"}, {"key": "sort", "value": "reservationId,desc"}]}}, "response": []}, {"name": "Then primary reservation with Id=946904 still exists", "event": [{"listen": "test", "script": {"id": "26ea8054-5fb3-441d-88e6-f57d8cfc0f03", "exec": ["//variables", "var jsonData = pm.response.json();", "var totalDocuments =  jsonData.page.totalElements;", "", "", "delete jsonData[\"_links\"];", "delete jsonData[\"page\"];", "", "for (var i=0;i<totalDocuments;i++){", "delete jsonData[\"_links\"];", "delete jsonData[\"page\"];", "delete jsonData._embedded.nucleusReservations[i][\"_links\"];", "delete jsonData._embedded.nucleusReservations[i][\"correlationId\"];", "delete jsonData._embedded.nucleusReservations[i][\"statisticsCorrelationId\"];", "delete jsonData._embedded.nucleusReservations[i][\"createDate\"];", "delete jsonData._embedded.nucleusReservations[i][\"lastModifiedDate\"];", "}", "", "", "", "//value to compare with", "var reservationJSONToBeCompared =JSON.parse('{\"_embedded\":{\"nucleusReservations\":[{\"versionId\":20151106001,\"reservationId\":\"946904\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"bookingDate\":\"2015-07-02T11:18:31.000\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"RESERVED\",\"nationality\":\"US\",\"channel\":\"PMS\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2016-12-01\",\"departureDate\":\"2016-12-03\",\"marketCode\":\"LTRAN\",\"sourceBookingCode\":\"WI\",\"numberOfChildren\":0,\"numberOfAdults\":2,\"bookingType\":\"6PM\",\"invTypeCode\":\"DLX\",\"bookedAccomTypeCode\":\"DLX\",\"rateCode\":\"BAR0\",\"invBlockCode\":null,\"roomNumber\":null,\"rates\":[{\"rateValue\":325,\"startDate\":\"2016-12-01\",\"endDate\":\"2016-12-03\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":325,\"currencyCode\":null}],\"services\":[],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"LTRAN\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"byDay\":false,\"profileId\":null,\"primary\":null,\"latestDepartureDate\":\"2016-12-03\",\"earliestArrivalDate\":\"2016-12-01\"},{\"versionId\":20151106002,\"reservationId\":\"946904\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"bookingDate\":\"2015-07-02T11:18:31.000\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"RESERVED\",\"nationality\":\"US\",\"channel\":\"PMS\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2016-12-01\",\"departureDate\":\"2016-12-03\",\"marketCode\":\"LTRAN\",\"sourceBookingCode\":\"WI\",\"numberOfChildren\":0,\"numberOfAdults\":3,\"bookingType\":\"6PM\",\"invTypeCode\":\"DLX\",\"bookedAccomTypeCode\":\"DLX\",\"rateCode\":\"BAR0\",\"invBlockCode\":null,\"roomNumber\":null,\"rates\":[{\"rateValue\":340,\"startDate\":\"2016-12-01\",\"endDate\":\"2016-12-03\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":340,\"currencyCode\":null}],\"services\":[],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"LTRAN\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"byDay\":false,\"profileId\":null,\"primary\":null,\"latestDepartureDate\":\"2016-12-03\",\"earliestArrivalDate\":\"2016-12-01\"},{\"versionId\":20151106003,\"reservationId\":\"946904\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"bookingDate\":\"2015-07-02T11:18:31.000\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"RESERVED\",\"nationality\":\"US\",\"channel\":\"PMS\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2016-12-01\",\"departureDate\":\"2016-12-02\",\"marketCode\":\"LTRAN\",\"sourceBookingCode\":\"WI\",\"numberOfChildren\":0,\"numberOfAdults\":3,\"bookingType\":\"6PM\",\"invTypeCode\":\"STD\",\"bookedAccomTypeCode\":\"DLX\",\"rateCode\":\"BAR0\",\"invBlockCode\":null,\"roomNumber\":null,\"rates\":[{\"rateValue\":340,\"startDate\":\"2016-12-01\",\"endDate\":\"2016-12-02\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":340,\"currencyCode\":null}],\"services\":[],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"LTRAN\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"byDay\":false,\"profileId\":null,\"primary\":null,\"latestDepartureDate\":\"2016-12-02\",\"earliestArrivalDate\":\"2016-12-01\"},{\"versionId\":20151106005,\"reservationId\":\"946904\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"bookingDate\":\"2015-07-02T11:18:31.000\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"RESERVED\",\"nationality\":\"US\",\"channel\":\"PMS\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2016-12-01\",\"departureDate\":\"2016-12-02\",\"marketCode\":\"LTRAN\",\"sourceBookingCode\":\"WI\",\"numberOfChildren\":0,\"numberOfAdults\":2,\"bookingType\":\"6PM\",\"invTypeCode\":\"STD\",\"bookedAccomTypeCode\":\"DLX\",\"rateCode\":\"BAR0\",\"invBlockCode\":null,\"roomNumber\":null,\"rates\":[{\"rateValue\":325,\"startDate\":\"2016-12-01\",\"endDate\":\"2016-12-02\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":325,\"currencyCode\":null}],\"services\":[],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"LTRAN\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"byDay\":false,\"profileId\":null,\"primary\":null,\"latestDepartureDate\":\"2016-12-02\",\"earliestArrivalDate\":\"2016-12-01\"}]}}');", "", "//assertions", "assertJSONData(\"Verifying Primary Share reservation exists in nucleus Reservation\", jsonData,reservationJSONToBeCompared);", "", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue).to.eql(expectedValue);", "});}", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusReservations/criteria?search=clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BreservationId%3Din%3D%28946904%29&size=100&sort=reservationId,desc", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusReservations", "criteria"], "query": [{"key": "search", "value": "clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BreservationId%3Din%3D%28946904%29"}, {"key": "size", "value": "100"}, {"key": "sort", "value": "reservationId,desc"}]}}, "response": []}, {"name": "Then the mapping of reservation is also stored in oxiShareReservationReferences Collection", "event": [{"listen": "test", "script": {"id": "291538b6-c8c4-4ad2-ab92-6082a7d4df24", "exec": ["//variables", "var jsonData = pm.response.json();", "", "//value to compare with", "var reservationJSONToBeCompared = {\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"reservationId\":\"946904\",\"primaryReservationId\":\"946904\",\"shareCode\":\"1169037\",\"sharers\":[{\"reservationId\":\"946904\",\"shareParent\":true,\"startedOn\":\"2015-07-02T16:20:10Z\",\"endedOn\":null,\"arrivalDate\":\"2016-12-01\",\"departureDate\":\"2016-12-02\",\"revenue\":null,\"participating\":true}]};", "", "var reservationJSONToBeCompared1 = {\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"reservationId\":\"946904\",\"primaryReservationId\":\"946904\",\"shareCode\":\"1169037\",\"sharers\":[{\"reservationId\":\"946904\",\"shareParent\":true,\"startedOn\":\"2015-07-02T16:20:10Z\",\"endedOn\":null,\"arrivalDate\":\"2016-12-01\",\"departureDate\":\"2016-12-02\",\"revenue\":null,\"participating\":true}]};", "", "//assertions", "arr = [];", "arr.push(reservationJSONToBeCompared,reservationJSONToBeCompared1);", "", "//Calling Comparisons", "for (item of arr) {", "     pm.globals.unset(\"actualjson\");", "    pm.globals.set(\"actualjson\", jsonData);", "    pm.globals.unset(\"expectedjson\");", "    pm.globals.set(\"expectedjson\", item);", "    eval(pm.globals.get(\"verifySharesReservationCollection\"));", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/oxiShareReservationReferences/search/findOneByClientCodeAndPropertyCodeAndReservationId?clientCode={{client_Code}}&propertyCode={{property_Code}}&reservationId=946904&size=100&sort=reservationId,desc", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["oxiShareReservationReferences", "search", "findOneByClientCodeAndPropertyCodeAndReservationId"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "reservationId", "value": "946904"}, {"key": "size", "value": "100"}, {"key": "sort", "value": "reservationId,desc"}]}}, "response": []}, {"name": "Then Summary is build for all the share reservation and sold is updated accordingly", "event": [{"listen": "test", "script": {"id": "f143b1c7-2483-47f6-afd8-c8098800391a", "exec": ["//variables", "var jsonData = pm.response.json();", "var totalDocuments =  jsonData.page.totalElements;", "", "", "delete jsonData[\"_links\"];", "delete jsonData[\"page\"];", "", "for (var i=0;i<totalDocuments;i++){", "   ", "//delete jsonData[\"page\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"_links\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"pseudoRoom\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"dayUse\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"statisticsCorrelationId\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"createDate\"];", "delete jsonData._embedded.nucleusOccupancySummaries[i][\"lastModifiedDate\"];", "}", "", "", "//value to compare with", "var reservationJSONToBeCompared = JSON.parse('{\"_embedded\":{\"nucleusOccupancySummaries\":[{\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"reservationId\":\"946904\",\"reservationStatus\":\"RESERVED\",\"occupancyDate\":\"2016-12-02 00:00:00.000\",\"marketSegmentCode\":\"LTRAN\",\"roomTypeCode\":\"STD\",\"roomNumber\":null,\"rateCode\":\"BAR0\",\"block\":null,\"pickup\":null,\"groupOccupancy\":false,\"roomsSold\":0,\"transientSold\":null,\"groupSold\":null,\"roomRevenue\":0,\"foodRevenue\":0,\"beverageRevenue\":0,\"otherRevenue\":0,\"totalRevenue\":0,\"noShowRoomRevenue\":null,\"arrivals\":0,\"departures\":1,\"cancellations\":0,\"noShows\":0,\"accomCapacity\":null,\"roomsNotAvailableMaintenance\":null,\"roomsNotAvailableOther\":null,\"groupId\":null,\"roomRate\":0,\"correlationId\":null,\"amsInPlay\":false,\"analyticalMarketSegmentCode\":\"LTRAN\",\"groupStatus\":null},{\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"reservationId\":\"946904\",\"reservationStatus\":\"RESERVED\",\"occupancyDate\":\"2016-12-01 00:00:00.000\",\"marketSegmentCode\":\"LTRAN\",\"roomTypeCode\":\"STD\",\"roomNumber\":null,\"rateCode\":\"BAR0\",\"block\":null,\"pickup\":null,\"groupOccupancy\":false,\"roomsSold\":1,\"transientSold\":null,\"groupSold\":null,\"roomRevenue\":325,\"foodRevenue\":0,\"beverageRevenue\":0,\"otherRevenue\":0,\"totalRevenue\":325,\"noShowRoomRevenue\":null,\"arrivals\":1,\"departures\":0,\"cancellations\":0,\"noShows\":0,\"accomCapacity\":null,\"roomsNotAvailableMaintenance\":null,\"roomsNotAvailableOther\":null,\"groupId\":null,\"roomRate\":325,\"correlationId\":null,\"amsInPlay\":false,\"analyticalMarketSegmentCode\":\"LTRAN\",\"groupStatus\":null}]}}');", "", "//assertions", "assertJSONData(\"Verifying Client Code in OxiMessage\", jsonData,reservationJSONToBeCompared);", "", "", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue).to.eql(expectedValue);", "});}", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusOccupancySummaries/criteria?search=clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BreservationId%3Din%3D%28946904%29&size=300&sort=occupancyDate,desc", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusOccupancySummaries", "criteria"], "query": [{"key": "search", "value": "clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BreservationId%3Din%3D%28946904%29"}, {"key": "size", "value": "300"}, {"key": "sort", "value": "occupancyDate,desc"}]}}, "response": []}, {"name": "Then NGI updates nucleusRoomTypeMarketSegmentActivity collection", "event": [{"listen": "test", "script": {"id": "1967f3dd-c48f-465f-a401-92ae0e1bc452", "exec": ["var jsonData = pm.response.json();", "jsonData = jsonData._embedded.nucleusRoomTypeMarketSegmentShardedActivities;", "", "console.log(jsonData);", "", "var nucleusRoomTypeActivityJSON = {\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"roomsSold\":0,\"arrivals\":0,\"departures\":2,\"cancellations\":null,\"noShows\":null,\"roomRevenue\":0,\"foodRevenue\":0,\"totalRevenue\":0,\"occupancyDate\":\"2016-12-02 00:00:00.000\",\"fiscalDate\":\"2016-07-06 00:00:00.000\",\"noShowRoomRevenue\":null,\"marketSegmentCode\":\"LTRAN\",\"roomTypeCode\":\"STD\",\"provided\":false};", "", "var nucleusRoomTypeActivityJSON1 = {\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"roomsSold\":2,\"arrivals\":2,\"departures\":0,\"cancellations\":null,\"noShows\":null,\"roomRevenue\":475,\"foodRevenue\":0,\"totalRevenue\":475,\"occupancyDate\":\"2016-12-01 00:00:00.000\",\"fiscalDate\":\"2016-07-06 00:00:00.000\",\"noShowRoomRevenue\":null,\"marketSegmentCode\":\"LTRAN\",\"roomTypeCode\":\"STD\",\"provided\":false};", "", "arr = [];", "arr.push(nucleusRoomTypeActivityJSON,nucleusRoomTypeActivityJSON1);", "", "//Calling assertions", "for (item of arr) {", "pm.globals.unset(\"actualjson\");", "pm.globals.set(\"actualjson\", jsonData);", "pm.globals.unset(\"expectedjson\");", "pm.globals.set(\"expectedjson\", nucleusRoomTypeActivityJSON);", "eval(pm.globals.get(\"VerifynucleusRTMSActivities\"));", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusRoomTypeMarketSegmentShardedActivities/criteria?search=clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BcorrelationId%3D%3D{{correlationId}}&sort=occupancyDate,desc", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusRoomTypeMarketSegmentShardedActivities", "criteria"], "query": [{"key": "search", "value": "clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BcorrelationId%3D%3D{{correlationId}}"}, {"key": "sort", "value": "occupancyDate,desc"}]}}, "response": []}, {"name": "Then the individualTrans Table in G3 gets updated", "event": [{"listen": "test", "script": {"id": "6436bad8-ce7a-46f7-b9b1-8235aea76a31", "exec": ["//variables", "var jsonData = pm.response.json();", "", "pm.globals.set(\"File_Metadata_ID\",jsonData[0][0]);", "", "var individualTransData = ('[' + pm.globals.get('File_Metadata_ID') + ',\"946904\",\"SS\",\"2016-12-01\",\"2016-12-02\",\"2015-07-02\",null,\"DLX\",\"LTRAN\",\"STD\",325,null,null,null,null,325,\"WI\",\"US\",\"BAR0\",325,null,\"6PM\",0,2,null,\"PMS\"]');", "", "var individualTransData1 = ('[' + pm.globals.get('File_Metadata_ID') + ',\"946905\",\"SS\",\"2016-12-01\",\"2016-12-02\",\"2015-07-02\",null,\"DLX\",\"LTRAN\",\"STD\",150,null,null,null,null,150,\"WI\",\"US\",\"BAR0\",150,null,\"6PM\",0,1,null,\"PMS\"]');", "", "", "arr = [];", "arr.push(individualTransData1,individualTransData);", "", "//Calling assertions", "for (item of arr) {", "    pm.globals.unset(\"actualjson\");", "    pm.globals.set(\"actualjson\", jsonData);", "    pm.globals.unset(\"expectedjson\");", "    pm.globals.set(\"expectedjson\", item);", "    eval(pm.globals.get(\"VerifyG3TableValue\"));", "}"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "73f26093-4c4c-403f-954e-74cca0b1b17b", "exec": ["pm.globals.unset(\"whereCondition\");\r", "pm.globals.set(\"whereCondition\", \" where reservation_identifier in ('946904','946905') \");\r", "var millisecondsToWait = 5000;\r", "setTimeout(function() {\r", "    // Whatever you want to do after the wait\r", "}, millisecondsToWait);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "propertyId", "type": "text", "value": "5"}], "body": {"mode": "raw", "raw": "{ \"query\": \"{{individualTransQuery}}\"\n}"}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/G3Tables/executeQuery/v1", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "G3Tables", "execute<PERSON>uery"]}}, "response": []}, {"name": "Then the Reservation_Night Table in G3 gets populated", "event": [{"listen": "test", "script": {"id": "00bfcc62-f4cb-43a8-816e-f0728042d14b", "exec": ["//variables", "var jsonData = pm.response.json();", "pm.globals.set(\"File_Metadata_ID\",jsonData[0][0]);", "", "var reservationNightData = ('[' + pm.globals.get('File_Metadata_ID') + ',\"946904\",\"RESERVED\",\"2016-12-01\",\"2016-12-02\",\"2015-07-02\",null,\"DLX\",\"LTRAN\",\"STD\",325,null,null,null,null,325,\"WI\",\"US\",\"BAR0\",325,null,\"6PM\",0,2,null,\"PMS\",null,null,\"LTRAN\"]');", "", "var reservationNightData1 = ('[' + pm.globals.get('File_Metadata_ID') + ',\"946905\",\"RESERVED\",\"2016-12-01\",\"2016-12-02\",\"2015-07-02\",null,\"DLX\",\"LTRAN\",\"STD\",150,null,null,null,null,150,\"WI\",\"US\",\"BAR0\",150,null,\"6PM\",0,1,null,\"PMS\",null,null,\"LTRAN\"]');", "", "", "arr = [];", "arr.push(reservationNightData,reservationNightData1);", "", "//Calling assertions", "for (item of arr) {", "    pm.globals.unset(\"actualjson\");", "    pm.globals.set(\"actualjson\", jsonData);", "    pm.globals.unset(\"expectedjson\");", "    pm.globals.set(\"expectedjson\", item);", "    eval(pm.globals.get(\"VerifyG3TableValue\"));", "}"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "52b76d13-c17a-4f5f-b3b9-cb283046795d", "exec": ["pm.globals.unset(\"whereCondition\");\r", "pm.globals.set(\"whereCondition\", \" where reservation_identifier in ('946904','946905') \");"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "propertyId", "type": "text", "value": "5"}], "body": {"mode": "raw", "raw": "{ \"query\": \"{{reservationNightQuery}}\"\n}"}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/G3Tables/executeQuery/v1", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "G3Tables", "execute<PERSON>uery"]}}, "response": []}, {"name": "Then Mkt_Accom_Activity Table gets populated with solds", "event": [{"listen": "test", "script": {"id": "03c8e377-b783-442b-bad3-216201f8b4f5", "exec": ["//variables", "var jsonData = pm.response.json();", "", "arr = [];", "", "var totalActivity = ('[\"2016-12-01\",\"LTRAN\",\"STD\",2,2,0,0,0,475,0,475,0]');", "var totalActivity1 = ('[\"2016-12-02\",\"LTRAN\",\"STD\",0,0,2,0,0,0,0,0,0]');", "", "", "arr.push(totalActivity, totalActivity1, );", "", "//Calling assertions", "for (item of arr) {", "    pm.globals.unset(\"actualjson\");", "    pm.globals.set(\"actualjson\", jsonData);", "    pm.globals.unset(\"expectedjson\");", "    pm.globals.set(\"expectedjson\", item);", "    eval(pm.globals.get(\"VerifyG3TableValue\"));", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "propertyId", "type": "text", "value": "5"}], "body": {"mode": "raw", "raw": "{ \"query\": \"{{mktAccomActivityQuery}}\"\n}"}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/G3Tables/executeQuery/v1", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "G3Tables", "execute<PERSON>uery"]}}, "response": []}, {"name": "When one of the two messages is flagged as NEW reservation(946904 in this case)", "event": [{"listen": "prerequest", "script": {"id": "708d5031-6a3d-46d7-8cb4-d55f471d190b", "exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"id": "870eaab9-7ac8-43bf-a101-4617d23ba4aa", "exec": ["//verifing status", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "text/xml"}], "body": {"mode": "raw", "raw": "<?xml version = '1.0' encoding = 'UTF-8'?>\r\n<?Label SandBox-OPER1|RESERVATION|20151106005|SUCCESS?>\r\n<!--Reservation_ShareBaseCase_Msg7: Official Break Share update - one of the two reservations is flagged as new\r\nIn his message: Reservation 946904 for 1 night, 2 adults at $325 (Everything is same as Msg5)\r\nNotes: \r\n/Reservation/RoomStays/RoomStay/resGuestRPHs = 0 pointing to only guest with reservation 946904\r\n/Reservation/RoomStays/RoomStay/mfsharedGuestRPHs no longer defined\r\n/Reservation/RoomStays/RoomStay/mfshareCode no longer defined -->\r\n<Reservation xmlns=\"reservation.fidelio.5.0\" mfShareAction=\"NA\" mfReservationAction=\"ADD\">\r\n   <HotelReference>\r\n      <hotelCode>SandBox-OPER1</hotelCode>\r\n   </HotelReference>\r\n   <reservationID>946904</reservationID>\r\n   <reservationOriginatorCode>SUPERVISOR</reservationOriginatorCode>\r\n   <originalBookingDate>2015-07-02T11:18:31.000</originalBookingDate>\r\n   <StayDateRange timeUnitType=\"DAY\">\r\n      <startTime>2016-12-01T00:00:00.000</startTime>\r\n      <numberOfTimeUnits>1</numberOfTimeUnits>\r\n   </StayDateRange>\r\n   <GuestCounts>\r\n      <GuestCount>\r\n         <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n         <mfCount>2</mfCount>\r\n      </GuestCount>\r\n      <GuestCount>\r\n         <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n         <mfCount>0</mfCount>\r\n      </GuestCount>\r\n   </GuestCounts>\r\n   <ResGuests>\r\n      <ResGuest reservationActionType=\"NEW\">\r\n         <resGuestRPH>0</resGuestRPH>\r\n         <profileRPHs>0</profileRPHs>\r\n         <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n         <InHouseTimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-12-01T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>1</numberOfTimeUnits>\r\n         </InHouseTimeSpan>\r\n         <ArrivalTransport/>\r\n         <DepartureTransport/>\r\n         <arrivalTime>2016-12-01T00:00:00.000</arrivalTime>\r\n         <departureTime>2016-12-02T00:00:00.000</departureTime>\r\n         <reservationID>946904</reservationID>\r\n         <ReservationReferences>\r\n            <ReservationReference type=\"GUESTID\" referenceNumber=\"946904\" legNumber=\"1\"/>\r\n            <ReservationReference type=\"PMSID\" referenceNumber=\"946904\" legNumber=\"1\"/>\r\n         </ReservationReferences>\r\n         <preRegistered>0</preRegistered>\r\n         <commissionPaidTo>N</commissionPaidTo>\r\n      </ResGuest>\r\n   </ResGuests>\r\n   <ResProfiles>\r\n      <ResProfile>\r\n         <Profile xmlns=\"profile.fidelio.5.0\" profileType=\"GUEST\" gender=\"U\" multiProperty=\"0\" miniProfile=\"0\" pseudoProfile=\"0\" replaceAddress=\"0\">\r\n            <creatorCode>SUPERVISOR</creatorCode>\r\n            <createdDate>2015-07-02T11:18:12.000</createdDate>\r\n            <lastUpdaterCode>SUPERVISOR</lastUpdaterCode>\r\n            <lastUpdated>2015-07-02T11:18:31.000</lastUpdated>\r\n            <genericName>Johnson</genericName>\r\n            <IndividualName>\r\n               <nameFirst>Steve</nameFirst>\r\n               <nameSur>Johnson</nameSur>\r\n            </IndividualName>\r\n            <primaryLanguageID>E</primaryLanguageID>\r\n            <PostalAddresses>\r\n               <PostalAddress addressType=\"HOME\">\r\n                  <countryCode>US</countryCode>\r\n                  <mfPrimaryYN>Y</mfPrimaryYN>\r\n                  <mfAddressLanguage>E</mfAddressLanguage>\r\n                  <cleansed>0</cleansed>\r\n               </PostalAddress>\r\n            </PostalAddresses>\r\n            <mfResort>SANDBOX-OXI3</mfResort>\r\n            <mfResortProfileID>608450</mfResortProfileID>\r\n            <mfAllowMail>YES</mfAllowMail>\r\n            <mfAllowEMail>YES</mfAllowEMail>\r\n            <mfGuestPriv>NO</mfGuestPriv>\r\n            <mfAllowPhone>0</mfAllowPhone>\r\n            <mfAllowSMS>0</mfAllowSMS>\r\n            <SalesExtention/>\r\n            <PrivacyOption>\r\n               <mfAllowMail>Y</mfAllowMail>\r\n               <mfAllowEMail>Y</mfAllowEMail>\r\n               <mfAllowPhone>0</mfAllowPhone>\r\n               <mfAllowSMS>0</mfAllowSMS>\r\n               <mfAllowHistory>1</mfAllowHistory>\r\n               <mfAllowMarketResearch>1</mfAllowMarketResearch>\r\n               <mfAllowThirdParty>1</mfAllowThirdParty>\r\n            </PrivacyOption>\r\n            <ResortList>SANDBOX-OXI3</ResortList>\r\n            <MultiResortEntities>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>CREDIT_CARDS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>RESORT_ARS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>COMMENTS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>PREFERENCES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>NEGOTIATED_RATES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>RESORT_NEG_RATES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>CHANNEL_ACCESS_CODES</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"1\">\r\n                  <entity>YIELD_ADJUSTMENTS</entity>\r\n               </MultiResortEntity>\r\n               <MultiResortEntity included=\"0\">\r\n                  <entity>RELATIONSHIPS</entity>\r\n               </MultiResortEntity>\r\n            </MultiResortEntities>\r\n         </Profile>\r\n         <resProfileRPH>0</resProfileRPH>\r\n      </ResProfile>\r\n   </ResProfiles>\r\n   <RoomStays>\r\n      <RoomStay mfShareAction=\"NA\" mfReservationAction=\"NA\" reservationActionType=\"NEW\" reservationStatusType=\"RESERVED\">\r\n         <roomInventoryCode>STD</roomInventoryCode>\r\n         <TimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-12-01T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>1</numberOfTimeUnits>\r\n         </TimeSpan>\r\n         <GuestCounts>\r\n            <GuestCount>\r\n               <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n               <mfCount>2</mfCount>\r\n            </GuestCount>\r\n            <GuestCount>\r\n               <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n               <mfCount>0</mfCount>\r\n            </GuestCount>\r\n         </GuestCounts>\r\n         <RatePlans>\r\n            <RatePlan reservationActionType=\"NEW\">\r\n               <ratePlanRPH>0</ratePlanRPH>\r\n               <ratePlanCode>BAR0</ratePlanCode>\r\n               <TimeSpan timeUnitType=\"DAY\">\r\n                  <startTime>2016-12-01T00:00:00.000</startTime>\r\n                  <numberOfTimeUnits>1</numberOfTimeUnits>\r\n               </TimeSpan>\r\n               <mfMarketCode>LTRAN</mfMarketCode>\r\n               <Rates>\r\n                  <Rate reservationActionType=\"NEW\" rateBasisTimeUnitType=\"DAY\">\r\n                     <rateRPH>0</rateRPH>\r\n                     <Amount currencyCode=\"USD\">\r\n                        <valueNum>335</valueNum>\r\n                     </Amount>\r\n                     <rateBasisUnits>1</rateBasisUnits>\r\n                     <TimeSpan timeUnitType=\"DAY\">\r\n                        <startTime>2016-12-01T00:00:00.000</startTime>\r\n                        <numberOfTimeUnits>1</numberOfTimeUnits>\r\n                     </TimeSpan>\r\n                     <mfAdults>2</mfAdults>\r\n                     <mfChildren>0</mfChildren>\r\n                     <mfCribs>0</mfCribs>\r\n                     <mfExtraBeds>0</mfExtraBeds>\r\n                     <mfsourceCode>WI</mfsourceCode>\r\n                     <mfMarketCode>LTRAN</mfMarketCode>\r\n                  </Rate>\r\n               </Rates>\r\n               <mfsourceCode>WI</mfsourceCode>\r\n            </RatePlan>\r\n         </RatePlans>\r\n         <marketSegmentCode>LTRAN</marketSegmentCode>\r\n         <resGuestRPHs>0</resGuestRPHs>\r\n         <GuaranteeInfo guaranteeType=\"NA\">\r\n            <mfGuaranteeType>6PM</mfGuaranteeType>\r\n            <GuaranteeDeposit>\r\n               <Amount currencyCode=\"USD\"/>\r\n               <cancelIfNotReceived>0</cancelIfNotReceived>\r\n            </GuaranteeDeposit>\r\n         </GuaranteeInfo>\r\n         <CancelPenalties>\r\n            <CancelPenalty mfRuleType=\"CANCEL\">\r\n               <cancelByDate>2016-11-29T12:00:00.000</cancelByDate>\r\n               <Amount currencyCode=\"USD\">\r\n                  <valueNum>169</valueNum>\r\n               </Amount>\r\n               <mfRuleScope>R</mfRuleScope>\r\n               <mfPercentage>50</mfPercentage>\r\n               <mfCancelPercentDue>100</mfCancelPercentDue>\r\n               <mfRuleDescription>Cancel 48 Hours prior to arrival - 50% penalty</mfRuleDescription>\r\n               <ruleCode>48H50P</ruleCode>\r\n            </CancelPenalty>\r\n         </CancelPenalties>\r\n         <PaymentInstructions>\r\n            <PaymentInstruction paymentMethodType=\"NA\">\r\n               <mfPaymentMethod>CA</mfPaymentMethod>\r\n               <PaymentDue>\r\n                  <Amount currencyCode=\"USD\"/>\r\n                  <cancelIfNotReceived>0</cancelIfNotReceived>\r\n               </PaymentDue>\r\n            </PaymentInstruction>\r\n         </PaymentInstructions>\r\n         <mfsourceCode>WI</mfsourceCode>\r\n         <mfchannelCode>PMS</mfchannelCode>\r\n         <mfconfidentialRate>0</mfconfidentialRate>\r\n         <mfAsbProrated>0</mfAsbProrated>\r\n      </RoomStay>\r\n   </RoomStays>\r\n   <resProfileRPHs>0</resProfileRPHs>\r\n   <mfupdateDate>2015-08-06T10:38:04.000</mfupdateDate>\r\n   <mfcomplementary>0</mfcomplementary>\r\n   <mfImage>\r\n      <numRooms>1</numRooms>\r\n      <Describe>\r\n         <resortName>Property 1 Opera Demo Multi Hotel</resortName>\r\n         <insertUser>SUPERVISOR</insertUser>\r\n         <updateUser>SUPERVISOR</updateUser>\r\n         <roomCategory>Standard - These rooms measure 30 square metres (325 square feet).  They include a comfortable chair for relaxation, an antique writing desk and cordless phone.  The marble bathroom offers a large vanity with basin, and combination tub and shower test.</roomCategory>\r\n         <rateCode>Room Charge</rateCode>\r\n         <marketCode>Local Transient</marketCode>\r\n         <guarantee>6PM: 6pm Guarantee</guarantee>\r\n         <company>Micros Hotels and Resorts</company>\r\n      </Describe>\r\n      <Change>\r\n         <bSource>0</bSource>\r\n         <bArrivalDate>0</bArrivalDate>\r\n         <bNumNights>0</bNumNights>\r\n         <bNumAdults>0</bNumAdults>\r\n         <bNumChildren>0</bNumChildren>\r\n         <bNumRooms>0</bNumRooms>\r\n         <bCribs>0</bCribs>\r\n         <bRoomCategory>0</bRoomCategory>\r\n         <bRateCode>0</bRateCode>\r\n         <bRateAmount>0</bRateAmount>\r\n         <bMarketCode>0</bMarketCode>\r\n         <bPaymentType>0</bPaymentType>\r\n         <bGuarType>0</bGuarType>\r\n         <bDiscountReason>0</bDiscountReason>\r\n         <bMultipleRateYN>0</bMultipleRateYN>\r\n         <bResvStatus>0</bResvStatus>\r\n      </Change>\r\n   </mfImage>\r\n</Reservation>\r\n"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/ngipublic/rest/oxi/pms", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["ngipublic", "rest", "oxi", "pms"]}}, "response": []}, {"name": "Then the Reservation gets updated in nucleusReservation Collection in NGI", "event": [{"listen": "test", "script": {"id": "0b58967b-d225-4bf9-8fbe-adf179494ada", "exec": ["//variables", "var jsonData = pm.response.json();", "var totalDocuments =  jsonData.page.totalElements;", "", "", "delete jsonData[\"_links\"];", "delete jsonData[\"page\"];", "", "for (var i=0;i<totalDocuments;i++){", "delete jsonData[\"_links\"];", "delete jsonData[\"page\"];", "delete jsonData._embedded.nucleusReservations[i][\"_links\"];", "delete jsonData._embedded.nucleusReservations[i][\"correlationId\"];", "delete jsonData._embedded.nucleusReservations[i][\"statisticsCorrelationId\"];", "delete jsonData._embedded.nucleusReservations[i][\"createDate\"];", "delete jsonData._embedded.nucleusReservations[i][\"lastModifiedDate\"];", "}", "", "", "//value to compare with", "var reservationJSONToBeCompared =JSON.parse('{\"_embedded\":{\"nucleusReservations\":[{\"versionId\":20151106001,\"reservationId\":\"946904\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"bookingDate\":\"2015-07-02T11:18:31.000\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"RESERVED\",\"nationality\":\"US\",\"channel\":\"PMS\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2016-12-01\",\"departureDate\":\"2016-12-03\",\"marketCode\":\"LTRAN\",\"sourceBookingCode\":\"WI\",\"numberOfChildren\":0,\"numberOfAdults\":2,\"bookingType\":\"6PM\",\"invTypeCode\":\"DLX\",\"bookedAccomTypeCode\":\"DLX\",\"rateCode\":\"BAR0\",\"invBlockCode\":null,\"roomNumber\":null,\"rates\":[{\"rateValue\":325,\"startDate\":\"2016-12-01\",\"endDate\":\"2016-12-03\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":325,\"currencyCode\":null}],\"services\":[],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"LTRAN\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"byDay\":false,\"profileId\":null,\"primary\":null,\"latestDepartureDate\":\"2016-12-03\",\"earliestArrivalDate\":\"2016-12-01\"},{\"versionId\":20151106002,\"reservationId\":\"946904\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"bookingDate\":\"2015-07-02T11:18:31.000\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"RESERVED\",\"nationality\":\"US\",\"channel\":\"PMS\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2016-12-01\",\"departureDate\":\"2016-12-03\",\"marketCode\":\"LTRAN\",\"sourceBookingCode\":\"WI\",\"numberOfChildren\":0,\"numberOfAdults\":3,\"bookingType\":\"6PM\",\"invTypeCode\":\"DLX\",\"bookedAccomTypeCode\":\"DLX\",\"rateCode\":\"BAR0\",\"invBlockCode\":null,\"roomNumber\":null,\"rates\":[{\"rateValue\":340,\"startDate\":\"2016-12-01\",\"endDate\":\"2016-12-03\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":340,\"currencyCode\":null}],\"services\":[],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"LTRAN\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"byDay\":false,\"profileId\":null,\"primary\":null,\"latestDepartureDate\":\"2016-12-03\",\"earliestArrivalDate\":\"2016-12-01\"},{\"versionId\":20151106003,\"reservationId\":\"946904\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"bookingDate\":\"2015-07-02T11:18:31.000\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"RESERVED\",\"nationality\":\"US\",\"channel\":\"PMS\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2016-12-01\",\"departureDate\":\"2016-12-02\",\"marketCode\":\"LTRAN\",\"sourceBookingCode\":\"WI\",\"numberOfChildren\":0,\"numberOfAdults\":3,\"bookingType\":\"6PM\",\"invTypeCode\":\"STD\",\"bookedAccomTypeCode\":\"DLX\",\"rateCode\":\"BAR0\",\"invBlockCode\":null,\"roomNumber\":null,\"rates\":[{\"rateValue\":340,\"startDate\":\"2016-12-01\",\"endDate\":\"2016-12-02\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":340,\"currencyCode\":null}],\"services\":[],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"LTRAN\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"byDay\":false,\"profileId\":null,\"primary\":null,\"latestDepartureDate\":\"2016-12-02\",\"earliestArrivalDate\":\"2016-12-01\"},{\"versionId\":20151106005,\"reservationId\":\"946904\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"bookingDate\":\"2015-07-02T11:18:31.000\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"RESERVED\",\"nationality\":\"US\",\"channel\":\"PMS\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2016-12-01\",\"departureDate\":\"2016-12-02\",\"marketCode\":\"LTRAN\",\"sourceBookingCode\":\"WI\",\"numberOfChildren\":0,\"numberOfAdults\":2,\"bookingType\":\"6PM\",\"invTypeCode\":\"STD\",\"bookedAccomTypeCode\":\"DLX\",\"rateCode\":\"BAR0\",\"invBlockCode\":null,\"roomNumber\":null,\"rates\":[{\"rateValue\":325,\"startDate\":\"2016-12-01\",\"endDate\":\"2016-12-02\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":325,\"currencyCode\":null}],\"services\":[],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"LTRAN\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"byDay\":false,\"profileId\":null,\"primary\":null,\"latestDepartureDate\":\"2016-12-02\",\"earliestArrivalDate\":\"2016-12-01\"},{\"versionId\":20151106005,\"reservationId\":\"946904\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"bookingDate\":\"2015-07-02T11:18:31.000\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"RESERVED\",\"nationality\":\"US\",\"channel\":\"PMS\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2016-12-01\",\"departureDate\":\"2016-12-02\",\"marketCode\":\"LTRAN\",\"sourceBookingCode\":\"WI\",\"numberOfChildren\":0,\"numberOfAdults\":2,\"bookingType\":\"6PM\",\"invTypeCode\":\"STD\",\"bookedAccomTypeCode\":\"DLX\",\"rateCode\":\"BAR0\",\"invBlockCode\":null,\"roomNumber\":null,\"rates\":[{\"rateValue\":335,\"startDate\":\"2016-12-01\",\"endDate\":\"2016-12-02\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":335,\"currencyCode\":null}],\"services\":[],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"LTRAN\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"byDay\":false,\"profileId\":null,\"primary\":null,\"latestDepartureDate\":\"2016-12-02\",\"earliestArrivalDate\":\"2016-12-01\"}]}}');", "", "//assertions", "assertJSONData(\"Verifying Client Code in OxiMessage\", jsonData,reservationJSONToBeCompared);", "", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue).to.eql(expectedValue);", "});}", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusReservations/criteria?search=clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BreservationId%3Din%3D%28946904%29&size=100&sort=reservationId,desc", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusReservations", "criteria"], "query": [{"key": "search", "value": "clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BreservationId%3Din%3D%28946904%29"}, {"key": "size", "value": "100"}, {"key": "sort", "value": "reservationId,desc"}]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"id": "cb8a6f5e-e4df-4da4-bc84-d808bdb7862f", "type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"id": "f024f08e-ac6f-456e-9568-f7d0bddc810c", "type": "text/javascript", "exec": [""]}}], "protocolProfileBehavior": {}}