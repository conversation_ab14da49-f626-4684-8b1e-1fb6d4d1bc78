{"info": {"_postman_id": "f0651b96-e1ed-465d-b40c-f972fd426f1b", "name": "Scenario: Package Rates with VendorConfig present", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Given that property configuration is saved in nucleusVendorConfigParams collection  as taxIncluded=true,assumePackageIncluded=true", "event": [{"listen": "prerequest", "script": {"id": "32bf7e77-dd2b-41dc-9605-8d8a2bb029ec", "exec": ["//variables", "var moment = require('moment');", "pm.globals.set(\"timestamp\", moment().format(\"YYYY-MM-DD\"));", "pm.globals.set(\"timestampWithoutYear\", moment().format(\"MM-DD\"));", "pm.globals.set(\"timestampAfter3Year\", moment().add('years', 3).format(\"YYYY-MM-DD\"));", "pm.globals.set(\"client_Code\", \"SandBox\");", "pm.globals.set(\"property_Code\", \"OPER1\");", "pm.globals.set(\"inboundVendorId\", \"5b6320b6ccb81834a0a1260b\");", "pm.globals.set(\"systemDate\", \"2018-01-01\");", "pm.globals.set(\"systemEndDate\", \"2020-12-31\");", "pm.globals.set(\"rateMsgEndDate\", \"2020-12-31\");", "pm.globals.set(\"bookingDate\", \"2018-01-20\");", "pm.globals.set(\"bookingFrom\", \"2018-01-20\");", "", "//pm.globals.set(\"midSeasonStartDate\", \"2016-10-30\");", "pm.globals.set(\"propertyId\", \"11020\");", "pm.globals.set(\"databaseId\", \"011020\");", "//pm.globals.set(\"resAutoCancellation\", \"resAutoCancellation\");", "", "", "var collection = JSON.parse('[\"nucleusQualifiedRatePlans\",\"oxiPackages\"]');", "pm.globals.set(\"collectionName\", collection);", "", "//cleaning up NGI collections", "eval(pm.globals.get(\"cleanUpScript\"));", "eval(pm.globals.get(\"cleanUpScriptForIndividualCollection\"));", "", "pm.globals.get(\"cleanUpCollection\")", "", "pm.globals.set(\"param_externalSystem\", \"pacman.core.property.externalSystem\");", "pm.globals.set(\"param_externalSubSystem\", \"pacman.core.property.externalSystem.subSystem\");", "pm.globals.set(\"propertyStage\", \"Population\");", "", "//variables", "var parameters = [pm.globals.get(\"param_externalSystem\")+'/pacman.'+pm.globals.get(\"client_Code\")+'.'+pm.globals.get(\"property_Code\")+'/NGI',pm.globals.get(\"param_externalSubSystem\")+'/pacman.'+pm.globals.get(\"client_Code\")+'.'+pm.globals.get(\"property_Code\")+'/OXI'];", "pm.globals.set(\"params\", parameters);", "", "//setting parameter values", "eval(pm.globals.get(\"setParameter\"));", "", "// //setting stage", "eval(pm.globals.get(\"setStage\"));"], "type": "text/javascript"}}, {"listen": "test", "script": {"id": "01435908-e46b-4070-952e-0100d85a6cd4", "exec": ["//setting parameter values", "eval(pm.globals.get(\"setParameter\"));", "", "// //setting stage", "eval(pm.globals.get(\"setStage\"));"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"inboundVendorId\": \"{{inboundVendorId}}\",\r\n    \"outboundVendorId\": null,\r\n    \"customReplyToAddress\": null,\r\n    \"useCustomSoapAction\": null,\r\n    \"name\": null,\r\n    \"inboundCredentials\": null,\r\n    \"outboundCredentials\": null,\r\n    \"integrationType\": \"OXI_PMS\",\r\n    \"chains\": [\r\n        {\r\n            \"chainCode\": \"{{client_Code}}\",\r\n            \"outboundCredentials\": null,\r\n            \"inboundCredentials\": null,\r\n            \"clientEnvironmentName\": \"g3\",\r\n            \"baseCurrencyCode\": \"USD\",\r\n            \"hotels\": [\r\n                {\r\n                    \"hotelCode\": \"{{property_Code}}\",\r\n                    \"g3HotelCode\": null,\r\n                    \"g2HotelCode\": null,\r\n                    \"inboundHotelCode\": null,\r\n                    \"propertyName\": \"{{property_Code}}\",\r\n                    \"outgoingUrl\": \"{{hostName}}:{{ngiPort}}/ngipublic/rest/mockserver/oxi/requestroomtype\",\r\n                    \"oxiInterfaceName\": \"OXI_PMS\",\r\n                    \"taxAdjustmentValue\": 10,\r\n                    \"outboundCredentials\": null,\r\n                    \"inboundCredentials\": null,\r\n                    \"baseCurrencyCode\": \"USD\",\r\n                    \"pastDays\": null,\r\n                    \"futureDays\": null,\r\n                    \"assumeTaxIncluded\": true,\r\n                    \"assumePackageIncluded\": true,\r\n                    \"installMode\": false,\r\n                    \"scheduledDeferredDelivery\": null,\r\n                    \"inCatchup\": null,\r\n                    \"calculateNonPickedUpBlocksUsingSummaryData\": null,\r\n                    \"handlePreviouslyStraightMarketSegmentsInAms\": null,\r\n                    \"unqualifiedRatesDirectPopulationDisabled\": false,\r\n                    \"qualifiedRatesDirectPopulationDisabled\": null,\r\n                    \"roomRevenuePackages\": \"BKFS,BF_REGULAR_LSTORNO2\",\r\n                    \"installationReservationsThreshold\": null,\r\n                    \"populatePackageDataEnabled\": null,\r\n                    \"defaultRoomType\": null\r\n                }\r\n            ],\r\n            \"unqualifiedRatesDirectPopulationDisabled\": null,\r\n            \"qualifiedRatesDirectPopulationDisabled\": null,\r\n            \"installationReservationsThreshold\": null,\r\n            \"populatePackageDataEnabled\": null\r\n        }\r\n    ],\r\n    \"configurations\": null,\r\n    \"unqualifiedRatesDirectPopulationDisabled\": null,\r\n    \"qualifiedRatesDirectPopulationDisabled\": null,\r\n    \"cancelMultiUnitDecrements\": null,\r\n    \"installationReservationsThreshold\": null,\r\n    \"_links\": {\r\n        \"self\": {\r\n            \"href\": \"{{hostName}}:{{ngiPort}}/nucleusVendorConfigParamses/{{inboundVendorId}}\"\r\n        },\r\n        \"nucleusVendorConfigParams\": {\r\n            \"href\": \"{{hostName}}:{{ngiPort}}/nucleusVendorConfigParamses/{{inboundVendorId}}\"\r\n        }\r\n    }\r\n}"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusVendorConfigParamses/{{inboundVendorId}}", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusVendorConfigParamses", "{{inboundVendorId}}"]}}, "response": []}, {"name": "When Reservation for Rate having pkg BKFS and VendorConfig has taxIncluded=true,assumePackageIncluded=true", "event": [{"listen": "test", "script": {"id": "bb5b32d6-69c9-46a4-bb88-8710c4d918fe", "exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\\\"1.0\\\" encoding=\\\"UTF-8\\\"?>\r\n<?Label SandBox-OPER1|RESERVATION|*********|SUCCESS|2018060120191231|166/3898?>\r\n<Reservation\r\n    xmlns=\\\"reservation.fidelio.5.0\\\" mfShareAction=\\\"NA\\\" mfReservationAction=\\\"CHECKOUT\\\">\r\n    <HotelReference>\r\n        <hotelCode>SandBox-OPER1</hotelCode>\r\n    </HotelReference>\r\n    <reservationID>*********</reservationID>\r\n    <reservationOriginatorCode>EUNICE_LAU</reservationOriginatorCode>\r\n    <originalBookingDate>2018-07-31T14:56:56.000</originalBookingDate>\r\n    <StayDateRange timeUnitType=\\\"DAY\\\">\r\n        <startTime>2018-08-26T17:33:35.000</startTime>\r\n        <numberOfTimeUnits>1</numberOfTimeUnits>\r\n    </StayDateRange>\r\n    <GuestCounts>\r\n        <GuestCount>\r\n            <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n            <mfCount>2</mfCount>\r\n        </GuestCount>\r\n        <GuestCount>\r\n            <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n            <mfCount>2</mfCount>\r\n        </GuestCount>\r\n    </GuestCounts>\r\n    <ResGuests>\r\n        <ResGuest reservationActionType=\\\"SYNC\\\">\r\n            <resGuestRPH>0</resGuestRPH>\r\n            <profileRPHs>0, 1</profileRPHs>\r\n            <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n            <InHouseTimeSpan timeUnitType=\\\"DAY\\\">\r\n                <startTime>2018-08-26T17:33:35.000</startTime>\r\n                <numberOfTimeUnits>1</numberOfTimeUnits>\r\n            </InHouseTimeSpan>\r\n            <ArrivalTransport/>\r\n            <DepartureTransport/>\r\n            <arrivalTime>2018-08-26T17:33:35.000</arrivalTime>\r\n            <departureTime>2018-08-27T08:34:00.000</departureTime>\r\n            <reservationID>*********</reservationID>\r\n            <ReservationReferences>\r\n                <ReservationReference type=\\\"GUESTID\\\" referenceNumber=\\\"*********\\\" legNumber=\\\"1\\\"/>\r\n                <ReservationReference type=\\\"TA_RECORD_LOCATOR\\\" referenceNumber=\\\"1449708029\\\"/>\r\n            </ReservationReferences>\r\n            <preRegistered>0</preRegistered>\r\n            <commissionPaidTo>T</commissionPaidTo>\r\n        </ResGuest>\r\n    </ResGuests>\r\n    <ResProfiles>\r\n        <ResProfile>\r\n            <Profile\r\n                xmlns=\\\"profile.fidelio.5.0\\\" profileType=\\\"GUEST\\\" multiProperty=\\\"0\\\" miniProfile=\\\"0\\\" pseudoProfile=\\\"0\\\" replaceAddress=\\\"0\\\">\r\n                <creatorCode>SARAH_RYAN1</creatorCode>\r\n                <createdDate>2018-07-31T14:56:05.000</createdDate>\r\n                <lastUpdaterCode>BRYONY_MCLANAGH</lastUpdaterCode>\r\n                <lastUpdated>2018-08-26T18:33:35.000</lastUpdated>\r\n                <IndividualName>\r\n                    <nameSur/>\r\n                </IndividualName>\r\n                <primaryLanguageID>E</primaryLanguageID>\r\n                <PostalAddresses>\r\n                    <PostalAddress addressType=\\\"HOME\\\">\r\n                        <postalCode>Z894K</postalCode>\r\n                        <countryCode>GB</countryCode>\r\n                        <mfPrimaryYN>Y</mfPrimaryYN>\r\n                        <mfAddressLanguage>E</mfAddressLanguage>\r\n                        <cleansed>0</cleansed>\r\n                    </PostalAddress>\r\n                </PostalAddresses>\r\n                <PhoneNumbers>\r\n                    <PhoneNumber phoneNumberType=\\\"HOME\\\">\r\n                        <mfPrimaryYN>Y</mfPrimaryYN>\r\n                        <confirmation>0</confirmation>\r\n                    </PhoneNumber>\r\n                </PhoneNumbers>\r\n                <mfResort>SandBox-OPER1</mfResort>\r\n                <mfResortProfileID>290184999</mfResortProfileID>\r\n                <mfAllowMail>NO</mfAllowMail>\r\n                <mfAllowEMail>NO</mfAllowEMail>\r\n                <mfGuestPriv>NO</mfGuestPriv>\r\n                <mfAllowPhone>0</mfAllowPhone>\r\n                <mfAllowSMS>0</mfAllowSMS>\r\n                <SalesExtention/>\r\n                <PrivacyOption>\r\n                    <mfAllowMail>N</mfAllowMail>\r\n                    <mfAllowEMail>N</mfAllowEMail>\r\n                    <mfAllowPhone>0</mfAllowPhone>\r\n                    <mfAllowSMS>0</mfAllowSMS>\r\n                    <mfAllowHistory>1</mfAllowHistory>\r\n                    <mfAllowMarketResearch>0</mfAllowMarketResearch>\r\n                    <mfAllowThirdParty>0</mfAllowThirdParty>\r\n                </PrivacyOption>\r\n                <ResortList>SandBox-OPER1</ResortList>\r\n                <MultiResortEntities>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>CREDIT_CARDS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RESORT_ARS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>COMMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>PREFERENCES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>NEGOTIATED_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_NEG_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_COMMISSIONS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>CHANNEL_ACCESS_CODES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>YIELD_ADJUSTMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RELATIONSHIPS</entity>\r\n                    </MultiResortEntity>\r\n                </MultiResortEntities>\r\n            </Profile>\r\n            <resProfileRPH>0</resProfileRPH>\r\n        </ResProfile>\r\n        <ResProfile>\r\n            <Profile\r\n                xmlns=\\\"profile.fidelio.5.0\\\" profileType=\\\"TRAVEL\\\" multiProperty=\\\"0\\\" miniProfile=\\\"0\\\" pseudoProfile=\\\"0\\\" replaceAddress=\\\"0\\\">\r\n                <creatorCode>OXI-OPERA</creatorCode>\r\n                <createdDate>2012-08-10T21:36:40.000</createdDate>\r\n                <lastUpdaterCode>NIKITA_AGGARWAL</lastUpdaterCode>\r\n                <lastUpdated>2018-09-10T17:39:10.000</lastUpdated>\r\n                <genericName>booking.com</genericName>\r\n                <IndividualName>\r\n                    <nameSur/>\r\n                </IndividualName>\r\n                <mfResort>SandBox-OPER1</mfResort>\r\n                <mfResortProfileID>5371710</mfResortProfileID>\r\n                <mfNameCode>96040394</mfNameCode>\r\n                <mfAllowMail>YES</mfAllowMail>\r\n                <mfAllowEMail>NO</mfAllowEMail>\r\n                <mfGuestPriv>NO</mfGuestPriv>\r\n                <mfAllowPhone>0</mfAllowPhone>\r\n                <mfAllowSMS>0</mfAllowSMS>\r\n                <SalesExtention/>\r\n                <PrivacyOption>\r\n                    <mfAllowMail>Y</mfAllowMail>\r\n                    <mfAllowEMail>N</mfAllowEMail>\r\n                    <mfAllowPhone>0</mfAllowPhone>\r\n                    <mfAllowSMS>0</mfAllowSMS>\r\n                    <mfAllowHistory>1</mfAllowHistory>\r\n                    <mfAllowMarketResearch>1</mfAllowMarketResearch>\r\n                    <mfAllowThirdParty>1</mfAllowThirdParty>\r\n                </PrivacyOption>\r\n                <ResortList>SandBox-OPER1</ResortList>\r\n                <MultiResortEntities>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>CREDIT_CARDS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RESORT_ARS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>COMMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>PREFERENCES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>NEGOTIATED_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_NEG_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_COMMISSIONS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>CHANNEL_ACCESS_CODES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>YIELD_ADJUSTMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RELATIONSHIPS</entity>\r\n                    </MultiResortEntity>\r\n                </MultiResortEntities>\r\n            </Profile>\r\n            <resProfileRPH>1</resProfileRPH>\r\n        </ResProfile>\r\n    </ResProfiles>\r\n    <RoomStays>\r\n        <RoomStay mfShareAction=\\\"NA\\\" mfReservationAction=\\\"NA\\\" reservationActionType=\\\"SYNC\\\" reservationStatusType=\\\"CHECKEDOUT\\\">\r\n            <roomInventoryCode>CKC</roomInventoryCode>\r\n            <roomID>311</roomID>\r\n            <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                <startTime>2018-08-26T17:33:35.000</startTime>\r\n                <numberOfTimeUnits>1</numberOfTimeUnits>\r\n            </TimeSpan>\r\n            <GuestCounts>\r\n                <GuestCount>\r\n                    <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n                    <mfCount>2</mfCount>\r\n                </GuestCount>\r\n                <GuestCount>\r\n                    <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n                    <mfCount>2</mfCount>\r\n                </GuestCount>\r\n            </GuestCounts>\r\n            <RatePlans>\r\n                <RatePlan reservationActionType=\\\"SYNC\\\">\r\n                    <ratePlanRPH>0</ratePlanRPH>\r\n                    <ratePlanCode>OTAGENA1</ratePlanCode>\r\n                    <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                        <startTime>2018-08-26T00:00:00.000</startTime>\r\n                        <numberOfTimeUnits>1</numberOfTimeUnits>\r\n                    </TimeSpan>\r\n                    <mfMarketCode>OTA</mfMarketCode>\r\n                    <Rates>\r\n                        <Rate reservationActionType=\\\"SYNC\\\" rateBasisTimeUnitType=\\\"DAY\\\">\r\n                            <rateRPH>0</rateRPH>\r\n                            <Amount currencyCode=\\\"USD\\\">\r\n                                <valueNum>169</valueNum>\r\n                            </Amount>\r\n                            <rateBasisUnits>1</rateBasisUnits>\r\n                            <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                                <startTime>2018-08-26T00:00:00.000</startTime>\r\n                                <numberOfTimeUnits>1</numberOfTimeUnits>\r\n                            </TimeSpan>\r\n                            <mfAdults>2</mfAdults>\r\n                            <mfChildren>2</mfChildren>\r\n                            <mfCribs>0</mfCribs>\r\n                            <mfExtraBeds>0</mfExtraBeds>\r\n                            <mfsourceCode>BC</mfsourceCode>\r\n                            <mfMarketCode>OTA</mfMarketCode>\r\n                        </Rate>\r\n                    </Rates>\r\n                    <mfsourceCode>BC</mfsourceCode>\r\n                </RatePlan>\r\n            </RatePlans>\r\n            <marketSegmentCode>OTA</marketSegmentCode>\r\n            <resGuestRPHs>0</resGuestRPHs>\r\n            <resCommentRPHs>0</resCommentRPHs>\r\n            <GuaranteeInfo guaranteeType=\\\"NA\\\">\r\n                <mfGuaranteeType>CHECKED IN</mfGuaranteeType>\r\n                <GuaranteeDeposit>\r\n                    <Amount currencyCode=\\\"USD\\\"/>\r\n                    <cancelIfNotReceived>0</cancelIfNotReceived>\r\n                </GuaranteeDeposit>\r\n            </GuaranteeInfo>\r\n            <CancelPenalties>\r\n                <CancelPenalty mfRuleType=\\\"CANCEL\\\" manualRule=\\\"0\\\">\r\n                    <cancelByDate>2018-08-26T14:00:00.000</cancelByDate>\r\n                    <Amount currencyCode=\\\"USD\\\">\r\n                        <valueNum>169</valueNum>\r\n                    </Amount>\r\n                    <mfRuleScope>R</mfRuleScope>\r\n                    <mfPercentage>100</mfPercentage>\r\n                    <mfCancelPercentDue>100</mfCancelPercentDue>\r\n                    <mfCancelRoomNights>1</mfCancelRoomNights>\r\n                    <mfRuleDescription>Cancel by 2pm on the Day of Arrival</mfRuleDescription>\r\n                    <ruleCode>2PM</ruleCode>\r\n                </CancelPenalty>\r\n            </CancelPenalties>\r\n            <PaymentInstructions>\r\n                <PaymentInstruction paymentMethodType=\\\"NA\\\">\r\n                    <mfPaymentMethod>CA</mfPaymentMethod>\r\n                    <PaymentDue>\r\n                        <Amount currencyCode=\\\"USD\\\"/>\r\n                        <cancelIfNotReceived>0</cancelIfNotReceived>\r\n                    </PaymentDue>\r\n                </PaymentInstruction>\r\n            </PaymentInstructions>\r\n            <mfcomplementaryCode>FAM</mfcomplementaryCode>\r\n            <mfsourceCode>BC</mfsourceCode>\r\n            <mfchannelCode>OTA</mfchannelCode>\r\n            <mfconfidentialRate>1</mfconfidentialRate>\r\n            <mfAsbProrated>0</mfAsbProrated>\r\n        </RoomStay>\r\n    </RoomStays>\r\n    <resCommentRPHs>0</resCommentRPHs>\r\n    <resProfileRPHs>0, 1</resProfileRPHs>\r\n    <mfupdateDate>2018-08-27T09:34:20.000</mfupdateDate>\r\n    <mfcomplementary>0</mfcomplementary>\r\n    <mfImage>\r\n        <numRooms>1</numRooms>\r\n        <Describe>\r\n            <resortName>Huntingtower Hotel Perth - Boutique by Leonardo</resortName>\r\n            <insertUser>SARAH_RYAN1</insertUser>\r\n            <updateUser>EUNICE_LAU</updateUser>\r\n            <roomCategory>Standard King room with Sofa</roomCategory>\r\n            <rateCode>Flexible Rate Inc Breakfast</rateCode>\r\n            <marketCode>Online Travel Agents</marketCode>\r\n            <guarantee>CHECKED IN: Checked In</guarantee>\r\n            <company>Jurys Inns Group Limited</company>\r\n        </Describe>\r\n        <Change>\r\n            <bArrivalDate>0</bArrivalDate>\r\n            <bNumNights>0</bNumNights>\r\n            <bNumAdults>0</bNumAdults>\r\n            <bNumChildren>0</bNumChildren>\r\n            <bNumRooms>0</bNumRooms>\r\n            <bCribs>0</bCribs>\r\n            <bRoomCategory>0</bRoomCategory>\r\n            <bPaymentType>0</bPaymentType>\r\n            <bGuarType>0</bGuarType>\r\n            <bDiscountReason>0</bDiscountReason>\r\n            <bMultipleRateYN>0</bMultipleRateYN>\r\n            <bResvStatus>0</bResvStatus>\r\n        </Change>\r\n    </mfImage>\r\n    <RateServices>\r\n        <RateService>\r\n            <Service reservationActionType=\\\"SYNC\\\" servicePricingType=\\\"NA\\\" reservationStatusType=\\\"NA\\\">\r\n                <serviceRPH>0</serviceRPH>\r\n                <serviceInventoryCode>BKFS</serviceInventoryCode>\r\n                <ratePlanCode>OTAGENA1</ratePlanCode>\r\n                <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                    <startTime>2018-08-26T00:00:00.000</startTime>\r\n                    <numberOfTimeUnits>1</numberOfTimeUnits>\r\n                </TimeSpan>\r\n                <Price currencyCode=\\\"USD\\\">\r\n                    <valueNum>12</valueNum>\r\n                </Price>\r\n                <quantity>1</quantity>\r\n                <ServicePrices>\r\n                    <ServicePrice>\r\n                        <beginDate>2018-08-26</beginDate>\r\n                        <endDate>2018-08-26</endDate>\r\n                        <unitPrice>6</unitPrice>\r\n                    </ServicePrice>\r\n                </ServicePrices>\r\n            </Service>\r\n        </RateService>\r\n    </RateServices>\r\n</Reservation>"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/ngipublic/rest/oxi/pms", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["ngipublic", "rest", "oxi", "pms"]}}, "response": []}, {"name": "Then NGI calculates rateValue for Reservation having rateCode=OTAGENA1, serviceName=BKFS", "event": [{"listen": "test", "script": {"id": "345e44b7-ccfb-4f1d-9242-58967c01a216", "exec": ["//variables", "var jsonData = pm.response.json();", "", "", "//value to compare with", "var reservationJSONToBeCompared ={\"versionId\":*********,\"reservationId\":\"*********\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"statisticsCorrelationId\":null,\"bookingDate\":\"2018-07-31T14:56:56.000\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"CHECKED_OUT\",\"nationality\":\"GB\",\"channel\":\"OTA\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2018-08-26\",\"departureDate\":\"2018-08-27\",\"marketCode\":\"OTA\",\"sourceBookingCode\":\"BC\",\"numberOfChildren\":2,\"numberOfAdults\":2,\"bookingType\":\"CHECKED IN\",\"invTypeCode\":\"CKC\",\"bookedAccomTypeCode\":\"FAM\",\"rateCode\":\"OTAGENA1\",\"invBlockCode\":null,\"roomNumber\":\"311\",\"rates\":[{\"rateValue\":153.63636,\"startDate\":\"2018-08-26\",\"endDate\":\"2018-08-27\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":169,\"currencyCode\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"serviceValue\":12,\"inclusive\":true,\"roomRevenuePackage\":true,\"reservationPackage\":false,\"startDate\":\"2018-08-26\",\"endDate\":\"2018-08-27\",\"inclusionType\":null}],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"OTA\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"earliestArrivalDate\":\"2018-08-26\",\"latestDepartureDate\":\"2018-08-27\",\"primary\":null};", "", "//assertions", "assertJSONData(\"Verifying Client Code in OxiMessage\", jsonData,reservationJSONToBeCompared);", "", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", " //   pm.expect(actualValue).to.eql(expectedValue);", "    pm.expect(actualValue.versionId).to.eql(expectedValue.versionId);", "    pm.expect(actualValue.clientCode).to.eql(expectedValue.clientCode);", "    pm.expect(actualValue.propertyCode).to.eql(expectedValue.propertyCode);", "    pm.expect(actualValue.status).to.eql(expectedValue.status);", "    pm.expect(actualValue.nationality).to.eql(expectedValue.nationality);", "    pm.expect(actualValue.channel).to.eql(expectedValue.channel);", "     pm.expect(actualValue.reservationId).to.eql(expectedValue.reservationId);", "    pm.expect(actualValue.roomStays.arrivalDate).to.eql(expectedValue.roomStays.arrivalDate);", "    pm.expect(actualValue.roomStays.departureDate).to.eql(expectedValue.roomStays.departureDate);", "    pm.expect(actualValue.roomStays.marketCode).to.eql(expectedValue.roomStays.marketCode);", "    pm.expect(actualValue.roomStays.sourceBookingCode).to.eql(expectedValue.roomStays.sourceBookingCode);", "    pm.expect(actualValue.roomStays.numberOfChildren).to.eql(expectedValue.roomStays.numberOfChildren);", "    pm.expect(actualValue.roomStays.numberOfAdults).to.eql(expectedValue.roomStays.numberOfAdults);", "    pm.expect(actualValue.roomStays.bookingType).to.eql(expectedValue.roomStays.bookingType);", "    pm.expect(actualValue.roomStays.invTypeCode).to.eql(expectedValue.roomStays.invTypeCode);", "    pm.expect(actualValue.roomStays.bookedAccomTypeCode).to.eql(expectedValue.roomStays.bookedAccomTypeCode);", "    pm.expect(actualValue.roomStays.rateCode).to.eql(expectedValue.roomStays.rateCode);", "    pm.expect(actualValue.roomStays[0].rates[0].rateValue).to.eql(expectedValue.roomStays[0].rates[0].rateValue);", "    pm.expect(actualValue.roomStays[0].rates[0].startDate).to.eql(expectedValue.roomStays[0].rates[0].startDate);", "    pm.expect(actualValue.roomStays[0].rates[0].endDate).to.eql(expectedValue.roomStays[0].rates[0].endDate);", "    pm.expect(actualValue.roomStays[0].rates[0].grossRate).to.eql(expectedValue.roomStays[0].rates[0].grossRate);", "    pm.expect(actualValue.roomStays[0].services[0].serviceName).to.eql(expectedValue.roomStays[0].services[0].serviceName);", "    pm.expect(actualValue.roomStays[0].services[0].serviceValue).to.eql(expectedValue.roomStays[0].services[0].serviceValue);", "    pm.expect(actualValue.roomStays[0].services[0].inclusive).to.eql(expectedValue.roomStays[0].services[0].inclusive);", "    pm.expect(actualValue.roomStays[0].services[0].roomRevenuePackage).to.eql(expectedValue.roomStays[0].services[0].roomRevenuePackage);", "    pm.expect(actualValue.roomStays[0].services[0].reservationPackage).to.eql(expectedValue.roomStays[0].services[0].reservationPackage);", "    pm.expect(actualValue.roomStays[0].services[0].endDate).to.eql(expectedValue.roomStays[0].services[0].endDate);", "    pm.expect(actualValue.roomStays[0].services[0].startDate).to.eql(expectedValue.roomStays[0].services[0].startDate);", "   pm.expect(actualValue.roomStays.originalCurrencyCode).to.eql(expectedValue.roomStays.originalCurrencyCode);", "   pm.expect(actualValue.roomStays.analyticalMarketSegmentCode).to.eql(expectedValue.roomStays.analyticalMarketSegmentCode);", "});}"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "7909fd4d-ca1e-4b2b-ae95-615bafd6588f", "exec": ["/*var millisecondsToWait = 5000;\r", "setTimeout(function() {\r", "    // Whatever you want to do after the wait\r", "}, millisecondsToWait);*/"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusReservations/search/findByClientCodeAndPropertyCodeAndReservationId?clientCode={{client_Code}}&propertyCode={{property_Code}}&reservationId=*********", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusReservations", "search", "findByClientCodeAndPropertyCodeAndReservationId"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "reservationId", "value": "*********"}]}}, "response": []}, {"name": "When Reservation for Rate with multiple rateCodes  and VendorConfig has taxIncluded=true,assumePackageIncluded=true", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<?Label SandBox-OPER1|RESERVATION|20160127003|SUCCESS?>\r\n<!-- Reservation_PackagesOneDay_Msg1 - Reservation using Rate Plans with Included Packages in Complex Ways\r\nIn this message: Reservation is made for 2 adults and 2 kids for 7 nights with 2 nights on Rate CEN26 then 4 nights on FAMILT and 1 night again on CEN26.\r\nCEN26 is $375 but includes BKFST posted each night at $100 for all people so the net rate is $275\r\nFAMILT is $309 but includes ARCADE at $20 for first night (net rate $289), FUNBAGS at $40 next 2 nights (net rate $269) and \r\nPHOTOS at $90 for last night with Rate (net rate $219) \r\nPASSES is not defined in Rate so have to assume it is not included and those values ignored -->\r\n<Reservation xmlns=\"reservation.fidelio.5.0\" mfShareAction=\"NA\" mfReservationAction=\"EDIT\">\r\n  <HotelReference>\r\n    <hotelCode>SandBox-OPER1</hotelCode>\r\n  </HotelReference>\r\n  <reservationID>673794</reservationID>\r\n  <reservationOriginatorCode>SUPERVISOR</reservationOriginatorCode>\r\n  <originalBookingDate>2015-04-20T19:06:51.000</originalBookingDate>\r\n  <StayDateRange timeUnitType=\"DAY\">\r\n    <startTime>2016-05-18T00:00:00.000</startTime>\r\n    <numberOfTimeUnits>7</numberOfTimeUnits>\r\n  </StayDateRange>\r\n  <GuestCounts>\r\n    <GuestCount>\r\n      <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n      <mfCount>2</mfCount>\r\n    </GuestCount>\r\n    <GuestCount>\r\n      <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n      <mfCount>2</mfCount>\r\n    </GuestCount>\r\n  </GuestCounts>\r\n  <ResGuests>\r\n    <ResGuest reservationActionType=\"SYNC\">\r\n      <resGuestRPH>0</resGuestRPH>\r\n      <profileRPHs>0</profileRPHs>\r\n      <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n      <InHouseTimeSpan timeUnitType=\"DAY\">\r\n        <startTime>2016-05-18T00:00:00.000</startTime>\r\n        <numberOfTimeUnits>7</numberOfTimeUnits>\r\n      </InHouseTimeSpan>\r\n      <ArrivalTransport>\r\n        <transportID>AA/421</transportID>\r\n        <locationCode>HEA</locationCode>\r\n        <transportTime>2016-05-18T14:30:00.000</transportTime>\r\n      </ArrivalTransport>\r\n      <DepartureTransport transportType=\"Train\">\r\n        <transportID>DE/1421</transportID>\r\n        <locationCode>RSW</locationCode>\r\n        <transportTime>2016-05-25T16:30:00.000</transportTime>\r\n      </DepartureTransport>\r\n      <arrivalTime>2016-05-18T00:00:00.000</arrivalTime>\r\n      <departureTime>2011-05-25T00:00:00.000</departureTime>\r\n      <reservationID>673794</reservationID>\r\n      <ReservationReferences>\r\n        <ReservationReference type=\"GUESTID\" referenceNumber=\"673794\" legNumber=\"1\"/>\r\n        <ReservationReference type=\"PMSID\" referenceNumber=\"673794\" legNumber=\"1\"/>\r\n      </ReservationReferences>\r\n      <preRegistered>0</preRegistered>\r\n      <commissionPaidTo>N</commissionPaidTo>\r\n    </ResGuest>\r\n  </ResGuests>\r\n  <ResProfiles>\r\n    <ResProfile>\r\n      <Profile xmlns=\"profile.fidelio.5.0\" profileType=\"GUEST\" gender=\"U\" multiProperty=\"0\" miniProfile=\"0\" pseudoProfile=\"0\" replaceAddress=\"0\">\r\n        <creatorCode>SUPERVISOR</creatorCode>\r\n        <createdDate>2006-06-06T11:10:05.000</createdDate>\r\n        <lastUpdaterCode>*ORS*</lastUpdaterCode>\r\n        <lastUpdated>2014-08-08T13:33:21.000</lastUpdated>\r\n        <preferredRatePlanCode>MEMBER</preferredRatePlanCode>\r\n        <genericName>Morgenstern</genericName>\r\n        <IndividualName>\r\n          <nameFirst>Eric1</nameFirst>\r\n          <nameSur>Morgenstern</nameSur>\r\n        </IndividualName>\r\n        <corporatePosition>GEFY CO</corporatePosition>\r\n        <dateOfBirth>1970-03-09</dateOfBirth>\r\n        <primaryLanguageID>E</primaryLanguageID>\r\n        <ElectronicAddresses>\r\n          <ElectronicAddress electronicAddressType=\"EMAIL\">\r\n            <eAddress><EMAIL></eAddress>\r\n            <mfPrimaryYN>Y</mfPrimaryYN>\r\n            <confirmation>0</confirmation>\r\n          </ElectronicAddress>\r\n        </ElectronicAddresses>\r\n        <Memberships>\r\n          <Membership>\r\n            <programCode>DL</programCode>\r\n            <accountID>**********</accountID>\r\n            <levelCode>BASE</levelCode>\r\n            <startDate>2013-04-06</startDate>\r\n            <nameOnCard>ERIC1 MORGENSTERN</nameOnCard>\r\n            <displaySequence>4</displaySequence>\r\n            <pointIndicator>0</pointIndicator>\r\n            <enrollmentSource>N</enrollmentSource>\r\n            <enrolledAt>N</enrolledAt>\r\n          </Membership>\r\n          <Membership>\r\n            <programCode>ENCORE</programCode>\r\n            <accountID>4808263</accountID>\r\n            <startDate>2009-09-03</startDate>\r\n            <nameOnCard>ERIC1 MORGENSTERN</nameOnCard>\r\n            <displaySequence>2</displaySequence>\r\n            <pointIndicator>0</pointIndicator>\r\n            <enrollmentSource>N</enrollmentSource>\r\n            <enrolledAt>N</enrolledAt>\r\n          </Membership>\r\n          <Membership>\r\n            <programCode>OCIS</programCode>\r\n            <accountID>********</accountID>\r\n            <levelCode>GOLD</levelCode>\r\n            <expireDate>2016-06-30</expireDate>\r\n            <startDate>2004-07-23</startDate>\r\n            <nameOnCard>ERIC1 MORGENSTERN</nameOnCard>\r\n            <membershipStatus>TEMP</membershipStatus>\r\n            <displaySequence>1</displaySequence>\r\n            <enrollmentCode>WEB</enrollmentCode>\r\n            <pointIndicator>1</pointIndicator>\r\n            <enrollmentSource>ON THE WEB</enrollmentSource>\r\n            <enrolledAt>MICROS WEBSITE</enrolledAt>\r\n          </Membership>\r\n          <Membership>\r\n            <programCode>US</programCode>\r\n            <accountID>4R931D6</accountID>\r\n            <levelCode>PLATINUM</levelCode>\r\n            <startDate>2013-04-06</startDate>\r\n            <nameOnCard>ERIC1 MORGENSTERN</nameOnCard>\r\n            <displaySequence>5</displaySequence>\r\n            <pointIndicator>0</pointIndicator>\r\n            <enrollmentSource>N</enrollmentSource>\r\n            <enrolledAt>N</enrolledAt>\r\n          </Membership>\r\n          <Membership mfInactiveDate=\"2014-05-21T00:00:00.000\">\r\n            <programCode>XYZ</programCode>\r\n            <accountID>**********</accountID>\r\n            <startDate>2013-04-06</startDate>\r\n            <nameOnCard>ERIC1 MORGENSTERN</nameOnCard>\r\n            <displaySequence>3</displaySequence>\r\n            <pointIndicator>0</pointIndicator>\r\n            <enrollmentSource>N</enrollmentSource>\r\n            <enrolledAt>N</enrolledAt>\r\n          </Membership>\r\n        </Memberships>\r\n        <PostalAddresses>\r\n          <PostalAddress addressType=\"BUSINESS\">\r\n            <address1>7031 Columbia Gateway Drive</address1>\r\n            <address2>OGTS - 2nd Floor</address2>\r\n            <city>Columbia</city>\r\n            <stateCode>MD</stateCode>\r\n            <postalCode>21046</postalCode>\r\n            <countryCode>IN</countryCode>\r\n            <mfPrimaryYN>Y</mfPrimaryYN>\r\n            <mfAddressLanguage>E</mfAddressLanguage>\r\n            <cleansed>0</cleansed>\r\n            <barCode>THISISABARCODE1234FORMAT</barCode>\r\n          </PostalAddress>\r\n          <PostalAddress addressType=\"HOME\">\r\n            <countryCode>GB</countryCode>\r\n            <mfPrimaryYN>N</mfPrimaryYN>\r\n            <mfAddressLanguage>E</mfAddressLanguage>\r\n            <cleansed>0</cleansed>\r\n          </PostalAddress>\r\n          <PostalAddress addressType=\"HOME\">\r\n            <address1>2640 Parkway De la Puerta De oro</address1>\r\n            <address2>Habitaci�n 315</address2>\r\n            <city>N�poles</city>\r\n            <stateCode>FL</stateCode>\r\n            <postalCode>34105</postalCode>\r\n            <countryCode>US</countryCode>\r\n            <mfPrimaryYN>N</mfPrimaryYN>\r\n            <mfAddressLanguage>S</mfAddressLanguage>\r\n            <cleansed>0</cleansed>\r\n          </PostalAddress>\r\n          <PostalAddress addressType=\"HOME\">\r\n            <address1>7031 Columbia Gateway Drive</address1>\r\n            <address2>OGTS - 2nd Floor / Cube 5</address2>\r\n            <city>Columbia</city>\r\n            <stateCode>MD</stateCode>\r\n            <postalCode>21046</postalCode>\r\n            <countryCode>US</countryCode>\r\n            <mfPrimaryYN>N</mfPrimaryYN>\r\n            <cleansed>0</cleansed>\r\n          </PostalAddress>\r\n        </PostalAddresses>\r\n        <PhoneNumbers>\r\n          <PhoneNumber phoneNumberType=\"BUSINESS\">\r\n            <phoneNumber>************</phoneNumber>\r\n            <mfPrimaryYN>Y</mfPrimaryYN>\r\n            <confirmation>0</confirmation>\r\n          </PhoneNumber>\r\n          <PhoneNumber phoneNumberType=\"MOBILE\">\r\n            <phoneNumber>**************</phoneNumber>\r\n            <mfPrimaryYN>N</mfPrimaryYN>\r\n            <confirmation>0</confirmation>\r\n          </PhoneNumber>\r\n        </PhoneNumbers>\r\n        <SpecialRequests>\r\n          <SpecialRequest mfSpecialRequestType=\"FEA\">\r\n            <requestCode>NS</requestCode>\r\n          </SpecialRequest>\r\n        </SpecialRequests>\r\n        <Comments>\r\n          <Comment>\r\n            <commentOriginatorCode>*ORS*</commentOriginatorCode>\r\n            <lastUpdated>2014-08-08T13:33:21.000</lastUpdated>\r\n            <commentStr>Backgound NOtes</commentStr>\r\n            <commentType>BKG</commentType>\r\n            <commentTitle>Profile Backgrd Note</commentTitle>\r\n          </Comment>\r\n          <Comment>\r\n            <commentOriginatorCode>*ORS*</commentOriginatorCode>\r\n            <lastUpdated>2014-08-08T13:33:21.000</lastUpdated>\r\n            <commentStr>Global Background Note</commentStr>\r\n            <commentType>BKG</commentType>\r\n            <commentTitle>Background Notes</commentTitle>\r\n          </Comment>\r\n          <Comment>\r\n            <commentOriginatorCode>SUPERVISOR</commentOriginatorCode>\r\n            <lastUpdated>2014-06-25T12:32:20.000</lastUpdated>\r\n            <commentStr>Profile is banned</commentStr>\r\n            <commentType>OWS</commentType>\r\n            <commentTitle>BANNED</commentTitle>\r\n          </Comment>\r\n       </Comments>\r\n        <mfResort>SandBox-OPER1</mfResort>\r\n        <mfResortProfileID>18082</mfResortProfileID>\r\n        <mfVipStatus>2</mfVipStatus>\r\n        <mfARNumber>EJM101</mfARNumber>\r\n        <mfAllowMail>YES</mfAllowMail>\r\n        <mfAllowEMail>NO</mfAllowEMail>\r\n        <mfGuestPriv>NO</mfGuestPriv>\r\n        <mfNegotiatedRates>\r\n          <NegotiatedRate>\r\n            <mfResort>SandBox-OPER1</mfResort>\r\n            <rateCode>MEMBER</rateCode>\r\n            <rateBeginDate>2007-03-05</rateBeginDate>\r\n          </NegotiatedRate>\r\n        </mfNegotiatedRates>\r\n        <mfAllowPhone>1</mfAllowPhone>\r\n        <mfAllowSMS>0</mfAllowSMS>\r\n        <SalesExtention>\r\n          <actionCode>SPA</actionCode>\r\n          <businessSegment>LR</businessSegment>\r\n        </SalesExtention>\r\n        <PrivacyOption>\r\n          <mfAllowMail>Y</mfAllowMail>\r\n          <mfAllowEMail>N</mfAllowEMail>\r\n          <mfAllowPhone>1</mfAllowPhone>\r\n          <mfAllowSMS>0</mfAllowSMS>\r\n          <mfAllowHistory>1</mfAllowHistory>\r\n         <mfAllowMarketResearch>0</mfAllowMarketResearch>\r\n          <mfAllowThirdParty>0</mfAllowThirdParty>\r\n        </PrivacyOption>\r\n        <ResortList>SandBox-OPER1</ResortList>\r\n        <MultiResortEntities>\r\n          <MultiResortEntity included=\"0\">\r\n            <entity>CREDIT_CARDS</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"0\">\r\n            <entity>RESORT_ARS</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"1\">\r\n            <entity>COMMENTS</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"1\">\r\n            <entity>PREFERENCES</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"1\">\r\n            <entity>NEGOTIATED_RATES</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"1\">\r\n            <entity>RESORT_NEG_RATES</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"1\">\r\n            <entity>CHANNEL_ACCESS_CODES</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"1\">\r\n            <entity>YIELD_ADJUSTMENTS</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"0\">\r\n            <entity>RELATIONSHIPS</entity>\r\n          </MultiResortEntity>\r\n        </MultiResortEntities>\r\n        <ResortComments>\r\n          <ResortComment>\r\n            <Comment>\r\n              <commentOriginatorCode>*ORS*</commentOriginatorCode>\r\n              <guestViewable>1</guestViewable>\r\n              <lastUpdated>2014-08-08T13:33:21.000</lastUpdated>\r\n              <commentStr>Backgound NOtes</commentStr>\r\n              <commentType>BKG</commentType>\r\n              <commentTitle>Profile Backgrd Note</commentTitle>\r\n            </Comment>\r\n          </ResortComment>\r\n          <ResortComment>\r\n            <Comment>\r\n              <commentOriginatorCode>*ORS*</commentOriginatorCode>\r\n              <guestViewable>1</guestViewable>\r\n              <lastUpdated>2014-08-08T13:33:21.000</lastUpdated>\r\n              <commentStr>Global Background Note</commentStr>\r\n              <commentType>BKG</commentType>\r\n              <commentTitle>Background Notes</commentTitle>\r\n            </Comment>\r\n          </ResortComment>\r\n          <ResortComment>\r\n            <Comment>\r\n              <commentOriginatorCode>SUPERVISOR</commentOriginatorCode>\r\n              <guestViewable>1</guestViewable>\r\n              <lastUpdated>2014-06-25T12:32:20.000</lastUpdated>\r\n              <commentStr>Profile is banned</commentStr>\r\n              <commentType>OWS</commentType>\r\n              <commentTitle>BANNED</commentTitle>\r\n            </Comment>\r\n          </ResortComment>\r\n        </ResortComments>\r\n        <ResortSpecialRequests>\r\n          <SpecialRequest mfSpecialRequestType=\"FEA\">\r\n            <requestCode>NS</requestCode>\r\n          </SpecialRequest>\r\n        </ResortSpecialRequests>\r\n        <WebAccounts>\r\n          <WebAccount>\r\n            <loginName>EMORGENSTERN</loginName>\r\n            <password>lGo17E/imFU=</password>\r\n            <secQuestion>Hp4riAzXmuw=</secQuestion>\r\n            <secAnswer>yjraoU8Yov0=</secAnswer>\r\n            <lastLoginDate>2010-10-20T09:21:29.000</lastLoginDate>\r\n            <insertSource>OWS</insertSource>\r\n            <passwordChangeDate>2012-05-01T16:29:26.000</passwordChangeDate>\r\n            <pwdSystemGenerated>0</pwdSystemGenerated>\r\n          </WebAccount>\r\n        </WebAccounts>\r\n        <ResortNegotiatedRates>\r\n          <NegotiatedRate>\r\n            <mfResort>SandBox-OPER1</mfResort>\r\n            <rateCode>MEMBER</rateCode>\r\n            <rateBeginDate>2007-03-05</rateBeginDate>\r\n          </NegotiatedRate>\r\n        </ResortNegotiatedRates>\r\n        <nationality>E</nationality>\r\n      </Profile>\r\n      <resProfileRPH>0</resProfileRPH>\r\n    </ResProfile>\r\n  </ResProfiles>\r\n  <RoomStays>\r\n    <RoomStay mfShareAction=\"NA\" mfReservationAction=\"NA\" reservationActionType=\"SYNC\" reservationStatusType=\"RESERVED\">\r\n      <roomInventoryCode>KDLX</roomInventoryCode>\r\n      <TimeSpan timeUnitType=\"DAY\">\r\n        <startTime>2016-05-18T00:00:00.000</startTime>\r\n        <numberOfTimeUnits>7</numberOfTimeUnits>\r\n      </TimeSpan>\r\n      <GuestCounts>\r\n        <GuestCount>\r\n          <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n          <mfCount>2</mfCount>\r\n        </GuestCount>\r\n        <GuestCount>\r\n          <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n          <mfCount>2</mfCount>\r\n        </GuestCount>\r\n      </GuestCounts>\r\n      <RatePlans>\r\n        <RatePlan reservationActionType=\"SYNC\">\r\n          <ratePlanRPH>0</ratePlanRPH>\r\n          <ratePlanCode>CEN26</ratePlanCode>\r\n          <TimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-05-18T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>2</numberOfTimeUnits>\r\n          </TimeSpan>\r\n          <mfMarketCode>NTRAN</mfMarketCode>\r\n          <Rates>\r\n            <Rate reservationActionType=\"SYNC\" rateBasisTimeUnitType=\"DAY\">\r\n              <rateRPH>0</rateRPH>\r\n              <Amount currencyCode=\"USD\">\r\n                <valueNum>375</valueNum>\r\n              </Amount>\r\n              <rateBasisUnits>1</rateBasisUnits>\r\n              <TimeSpan timeUnitType=\"DAY\">\r\n                <startTime>2016-05-18T00:00:00.000</startTime>\r\n                <numberOfTimeUnits>2</numberOfTimeUnits>\r\n              </TimeSpan>\r\n              <mfAdults>2</mfAdults>\r\n              <mfChildren>2</mfChildren>\r\n              <mfCribs>0</mfCribs>\r\n              <mfExtraBeds>0</mfExtraBeds>\r\n              <mfsourceCode>WEB</mfsourceCode>\r\n              <mfMarketCode>NTRAN</mfMarketCode>\r\n            </Rate>\r\n          </Rates>\r\n          <mfsourceCode>WEB</mfsourceCode>\r\n        </RatePlan>\r\n        <RatePlan reservationActionType=\"SYNC\">\r\n          <ratePlanRPH>1</ratePlanRPH>\r\n          <ratePlanCode>FAMILT</ratePlanCode>\r\n          <TimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-05-20T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>4</numberOfTimeUnits>\r\n          </TimeSpan>\r\n          <mfMarketCode>ITRAN</mfMarketCode>\r\n          <Rates>\r\n            <Rate reservationActionType=\"SYNC\" rateBasisTimeUnitType=\"DAY\">\r\n              <rateRPH>1</rateRPH>\r\n              <Amount currencyCode=\"USD\">\r\n                <valueNum>309</valueNum>\r\n              </Amount>\r\n              <rateBasisUnits>1</rateBasisUnits>\r\n              <TimeSpan timeUnitType=\"DAY\">\r\n                <startTime>2016-05-20T00:00:00.000</startTime>\r\n                <numberOfTimeUnits>4</numberOfTimeUnits>\r\n              </TimeSpan>\r\n              <mfAdults>2</mfAdults>\r\n              <mfChildren>2</mfChildren>\r\n              <mfCribs>0</mfCribs>\r\n              <mfExtraBeds>0</mfExtraBeds>\r\n              <mfsourceCode>WEB</mfsourceCode>\r\n              <mfMarketCode>ITRAN</mfMarketCode>\r\n            </Rate>\r\n          </Rates>\r\n          <mfsourceCode>WEB</mfsourceCode>\r\n        </RatePlan>\r\n        <RatePlan reservationActionType=\"SYNC\">\r\n          <ratePlanRPH>2</ratePlanRPH>\r\n          <ratePlanCode>CEN26</ratePlanCode>\r\n          <TimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-05-24T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>1</numberOfTimeUnits>\r\n          </TimeSpan>\r\n          <mfMarketCode>NTRAN</mfMarketCode>\r\n          <Rates>\r\n            <Rate reservationActionType=\"SYNC\" rateBasisTimeUnitType=\"DAY\">\r\n              <rateRPH>2</rateRPH>\r\n              <Amount currencyCode=\"USD\">\r\n                <valueNum>375</valueNum>\r\n              </Amount>\r\n              <rateBasisUnits>1</rateBasisUnits>\r\n              <TimeSpan timeUnitType=\"DAY\">\r\n                <startTime>2016-05-24T00:00:00.000</startTime>\r\n                <numberOfTimeUnits>1</numberOfTimeUnits>\r\n              </TimeSpan>\r\n              <mfAdults>2</mfAdults>\r\n              <mfChildren>2</mfChildren>\r\n              <mfCribs>0</mfCribs>\r\n              <mfExtraBeds>0</mfExtraBeds>\r\n              <mfsourceCode>WEB</mfsourceCode>\r\n              <mfMarketCode>NTRAN</mfMarketCode>\r\n            </Rate>\r\n          </Rates>\r\n          <mfsourceCode>WEB</mfsourceCode>\r\n        </RatePlan>\r\n      </RatePlans>\r\n      <marketSegmentCode>PROMO</marketSegmentCode>\r\n      <resGuestRPHs>0</resGuestRPHs>\r\n      <GuaranteeInfo guaranteeType=\"NA\">\r\n        <mfGuaranteeType>CC</mfGuaranteeType>\r\n        <GuaranteeDeposit>\r\n          <dueDate>2015-11-18T00:00:00.000</dueDate>\r\n          <Amount currencyCode=\"USD\">\r\n            <valueNum>642.2</valueNum>\r\n          </Amount>\r\n          <cancelIfNotReceived>0</cancelIfNotReceived>\r\n        </GuaranteeDeposit>\r\n      </GuaranteeInfo>\r\n      <CancelPenalties>\r\n        <CancelPenalty mfRuleType=\"DEPOSIT\">\r\n          <cancelByDate>2015-11-18T00:00:00.000</cancelByDate>\r\n          <Amount currencyCode=\"USD\">\r\n            <valueNum>642.2</valueNum>\r\n          </Amount>\r\n          <mfRuleScope>R</mfRuleScope>\r\n          <mfPercentage>50</mfPercentage>\r\n          <mfRuleDescription>50 Percent due</mfRuleDescription>\r\n          <activityDeposit>0</activityDeposit>\r\n          <roomDeposit>642.2</roomDeposit>\r\n          <ruleCode>50PCT</ruleCode>\r\n        </CancelPenalty>\r\n      </CancelPenalties>\r\n      <PaymentInstructions>\r\n        <PaymentInstruction paymentMethodType=\"NA\">\r\n          <mfPaymentMethod>AX</mfPaymentMethod>\r\n          <PaymentDue>\r\n            <Amount currencyCode=\"USD\"/>\r\n            <cancelIfNotReceived>0</cancelIfNotReceived>\r\n          </PaymentDue>\r\n        </PaymentInstruction>\r\n      </PaymentInstructions>\r\n      <mfsourceCode>WEB</mfsourceCode>\r\n      <mfchannelCode>TAD</mfchannelCode>\r\n      <mfconfidentialRate>0</mfconfidentialRate>\r\n      <mfAsbProrated>0</mfAsbProrated>\r\n    </RoomStay>\r\n  </RoomStays>\r\n  <resProfileRPHs>0</resProfileRPHs>\r\n  <mfupdateDate>2015-08-22T16:34:23.000</mfupdateDate>\r\n  <mfcomplementary>0</mfcomplementary>\r\n  <mfImage>\r\n    <numRooms>1</numRooms>\r\n    <Describe>\r\n      <resortName>Property 1 Opera Demo Multi Hotel</resortName>\r\n      <insertUser>OEDS$ADS</insertUser>\r\n      <updateUser>SUPERVISOR</updateUser>\r\n      <roomCategory>King Deluxe Room</roomCategory>\r\n      <rateCode>Tarpon Family Package</rateCode>\r\n      <guarantee>CC: Credit Card Guaranteed</guarantee>\r\n      <company>Micros Hotels and Resorts</company>\r\n    </Describe>\r\n    <Change>\r\n      <bArrivalDate>0</bArrivalDate>\r\n      <bNumNights>0</bNumNights>\r\n      <bNumAdults>0</bNumAdults>\r\n      <bNumChildren>0</bNumChildren>\r\n      <bNumRooms>0</bNumRooms>\r\n      <bCribs>0</bCribs>\r\n      <bRoomCategory>0</bRoomCategory>\r\n      <bPaymentType>0</bPaymentType>\r\n      <bGuarType>0</bGuarType>\r\n      <bDiscountReason>0</bDiscountReason>\r\n      <bMultipleRateYN>1</bMultipleRateYN>\r\n      <bResvStatus>0</bResvStatus>\r\n    </Change>\r\n  </mfImage>\r\n  <Services>  \r\n    <Service reservationActionType=\"SYNC\" servicePricingType=\"NA\" reservationStatusType=\"NA\">\r\n      <serviceRPH>0</serviceRPH>\r\n      <serviceInventoryCode>BF_REGULAR_LSTORNO2</serviceInventoryCode>        \r\n      <TimeSpan timeUnitType=\"DAY\">\r\n        <startTime>2016-05-18T00:00:00.000</startTime>\r\n        <numberOfTimeUnits>7</numberOfTimeUnits>\r\n      </TimeSpan>\r\n      <Price currencyCode=\"USD\">\r\n        <valueNum>100</valueNum>\r\n      </Price>\r\n      <quantity>1</quantity>\r\n      <ServicePrices>\r\n        <ServicePrice>\r\n          <beginDate>2016-05-18</beginDate>\r\n          <endDate>2016-05-19</endDate>\r\n          <unitPrice>25</unitPrice>\r\n        </ServicePrice>\r\n        <ServicePrice>\r\n          <beginDate>2016-05-24</beginDate>\r\n          <endDate>2016-05-24</endDate>\r\n          <unitPrice>25</unitPrice>\r\n        </ServicePrice>\r\n      </ServicePrices>\r\n    </Service>\r\n  </Services>\r\n</Reservation>\r\n"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/ngipublic/rest/oxi/pms", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["ngipublic", "rest", "oxi", "pms"]}}, "response": []}, {"name": "Then NGI calculates rateValue for Reservation having rateCode=CEN16,FAMILT, serviceName=BF_REGULAR_LSTORNO2", "event": [{"listen": "test", "script": {"id": "bf4843af-117d-44a6-a692-cb86dec6fff6", "exec": ["//variables", "var jsonData = pm.response.json();", "", "//value to compare with", "// RoomStaysData for arrivalDate 2016-05-18", "var reservationJSONToBeCompared ={\"createDate\":\"2020-03-27 16:26:02.602\",\"lastModifiedDate\":\"2020-03-27 16:26:30.016\",\"versionId\":20160127003,\"reservationId\":\"673794\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"correlationId\":\"20160127003\",\"statisticsCorrelationId\":null,\"bookingDate\":\"2015-04-20T19:06:51.000\",\"earliestArrivalDate\":\"2016-05-18\",\"latestDepartureDate\":\"2016-05-25\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"RESERVED\",\"nationality\":\"GB\",\"channel\":\"TAD\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2016-05-18\",\"departureDate\":\"2016-05-20\",\"marketCode\":\"NTRAN\",\"sourceBookingCode\":\"WEB\",\"numberOfChildren\":2,\"numberOfAdults\":2,\"bookingType\":\"CC\",\"invTypeCode\":\"KDLX\",\"bookedAccomTypeCode\":\"KDLX\",\"rateCode\":\"CEN26\",\"invBlockCode\":null,\"roomNumber\":null,\"rates\":[{\"rateValue\":340.90909,\"startDate\":\"2016-05-18\",\"endDate\":\"2016-05-20\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":375,\"currencyCode\":null}],\"services\":[{\"serviceName\":\"BF_REGULAR_LSTORNO2\",\"serviceValue\":100,\"inclusive\":true,\"roomRevenuePackage\":true,\"reservationPackage\":true,\"startDate\":\"2016-05-18\",\"endDate\":\"2016-05-20\",\"inclusionType\":null}],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"NTRAN\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"byDay\":false,\"profileId\":null,\"primary\":null};", "// RoomStaysData for arrivalDate 2016-05-20", "var reservationJSONToBeCompared1 ={\"createDate\":\"2020-03-27 16:26:02.602\",\"lastModifiedDate\":\"2020-03-27 16:26:30.016\",\"versionId\":20160127003,\"reservationId\":\"673794\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"correlationId\":\"20160127003\",\"statisticsCorrelationId\":null,\"bookingDate\":\"2015-04-20T19:06:51.000\",\"earliestArrivalDate\":\"2016-05-18\",\"latestDepartureDate\":\"2016-05-25\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"RESERVED\",\"nationality\":\"GB\",\"channel\":\"TAD\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2016-05-20\",\"departureDate\":\"2016-05-24\",\"marketCode\":\"ITRAN\",\"sourceBookingCode\":\"WEB\",\"numberOfChildren\":2,\"numberOfAdults\":2,\"bookingType\":\"CC\",\"invTypeCode\":\"KDLX\",\"bookedAccomTypeCode\":\"KDLX\",\"rateCode\":\"FAMILT\",\"invBlockCode\":null,\"roomNumber\":null,\"rates\":[{\"rateValue\":280.90909,\"startDate\":\"2016-05-20\",\"endDate\":\"2016-05-24\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":309,\"currencyCode\":null}],\"services\":[],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"ITRAN\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"byDay\":false,\"profileId\":null,\"primary\":null};", "", "// RoomStaysData for arrivalDate 2016-05-24", "var reservationJSONToBeCompared2 ={\"createDate\":\"2020-03-27 16:26:02.602\",\"lastModifiedDate\":\"2020-03-27 16:26:30.016\",\"versionId\":20160127003,\"reservationId\":\"673794\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"correlationId\":\"20160127003\",\"statisticsCorrelationId\":null,\"bookingDate\":\"2015-04-20T19:06:51.000\",\"earliestArrivalDate\":\"2016-05-18\",\"latestDepartureDate\":\"2016-05-25\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"RESERVED\",\"nationality\":\"GB\",\"channel\":\"TAD\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2016-05-24\",\"departureDate\":\"2016-05-25\",\"marketCode\":\"NTRAN\",\"sourceBookingCode\":\"WEB\",\"numberOfChildren\":2,\"numberOfAdults\":2,\"bookingType\":\"CC\",\"invTypeCode\":\"KDLX\",\"bookedAccomTypeCode\":\"KDLX\",\"rateCode\":\"CEN26\",\"invBlockCode\":null,\"roomNumber\":null,\"rates\":[{\"rateValue\":340.90909,\"startDate\":\"2016-05-24\",\"endDate\":\"2016-05-25\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":375,\"currencyCode\":null}],\"services\":[{\"serviceName\":\"BF_REGULAR_LSTORNO2\",\"serviceValue\":100,\"inclusive\":true,\"roomRevenuePackage\":true,\"reservationPackage\":true,\"startDate\":\"2016-05-24\",\"endDate\":\"2016-05-25\",\"inclusionType\":null}],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"NTRAN\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"byDay\":false,\"profileId\":null,\"primary\":null};", "", "arr=[];", "", "arr.push(reservationJSONToBeCompared,reservationJSONToBeCompared1,reservationJSONToBeCompared2);", "var i=0;", "//assertions", "for(item of arr)", "{", "    console.log(i);", "    assertJSONData(\"Verifying Client Code in OxiMessage\", jsonData,item);", "    assertRoomStaysData(\"Verifying Room stays json in OxiMessage\", jsonData.roomStays[i],item.roomStays[0]);", "    i++;", "}", "", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "     pm.expect(actualValue.versionId).to.eql(expectedValue.versionId);", "    pm.expect(actualValue.clientCode).to.eql(expectedValue.clientCode);", "    pm.expect(actualValue.propertyCode).to.eql(expectedValue.propertyCode);", "    pm.expect(actualValue.status).to.eql(expectedValue.status);", "    pm.expect(actualValue.nationality).to.eql(expectedValue.nationality);", "    pm.expect(actualValue.channel).to.eql(expectedValue.channel);", "     pm.expect(actualValue.reservationId).to.eql(expectedValue.reservationId);", "    pm.expect(actualValue.roomStays.arrivalDate).to.eql(expectedValue.roomStays.arrivalDate);", "    pm.expect(actualValue.roomStays.departureDate).to.eql(expectedValue.roomStays.departureDate);", "    pm.expect(actualValue.roomStays.marketCode).to.eql(expectedValue.roomStays.marketCode);", "    pm.expect(actualValue.roomStays.sourceBookingCode).to.eql(expectedValue.roomStays.sourceBookingCode);", "    pm.expect(actualValue.roomStays.numberOfChildren).to.eql(expectedValue.roomStays.numberOfChildren);", "    pm.expect(actualValue.roomStays.numberOfAdults).to.eql(expectedValue.roomStays.numberOfAdults);", "    pm.expect(actualValue.roomStays.bookingType).to.eql(expectedValue.roomStays.bookingType);", "    pm.expect(actualValue.roomStays.invTypeCode).to.eql(expectedValue.roomStays.invTypeCode);", "    pm.expect(actualValue.roomStays.bookedAccomTypeCode).to.eql(expectedValue.roomStays.bookedAccomTypeCode);", "    pm.expect(actualValue.roomStays.rateCode).to.eql(expectedValue.roomStays.rateCode);", "   pm.expect(actualValue.roomStays.originalCurrencyCode).to.eql(expectedValue.roomStays.originalCurrencyCode);", "   pm.expect(actualValue.roomStays.analyticalMarketSegmentCode).to.eql(expectedValue.roomStays.analyticalMarketSegmentCode);", "});}", "", "function assertRoomStaysData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue.rates.rateValue).to.eql(expectedValue.rates.rateValue);", "    pm.expect(actualValue.rates.startDate).to.eql(expectedValue.rates.startDate);", "    pm.expect(actualValue.rates.endDate).to.eql(expectedValue.rates.endDate);", "    pm.expect(actualValue.rates.grossRate).to.eql(expectedValue.rates.grossRate);", "    pm.expect(actualValue.services.serviceName).to.eql(expectedValue.rates.serviceName);", "    pm.expect(actualValue.services.serviceValue).to.eql(expectedValue.rates.serviceValue);", "    pm.expect(actualValue.services.inclusive).to.eql(expectedValue.rates.inclusive);", "    pm.expect(actualValue.services.startDate).to.eql(expectedValue.rates.startDate);", "    pm.expect(actualValue.services.endDate).to.eql(expectedValue.rates.endDate);", "    pm.expect(actualValue.services.roomRevenuePackage).to.eql(expectedValue.rates.roomRevenuePackage);", "});}"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "dd24d087-3c85-4fc0-90ab-4bc674d82c25", "exec": ["/*var millisecondsToWait = 5000;\r", "setTimeout(function() {\r", "    // Whatever you want to do after the wait\r", "}, millisecondsToWait);*/"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusReservations/search/findByClientCodeAndPropertyCodeAndReservationId?clientCode={{client_Code}}&propertyCode={{property_Code}}&reservationId=673794", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusReservations", "search", "findByClientCodeAndPropertyCodeAndReservationId"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "reservationId", "value": "673794"}]}}, "response": []}, {"name": "When the property configuration is changed in nucleusVendorConfigParams collection as taxIncluded=false,assumePackageIncluded=true", "event": [{"listen": "prerequest", "script": {"id": "93fc454f-b07f-4f39-a6a9-e646d3f801c0", "exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"id": "45bef566-e120-4620-aff0-f0b7a6389bbf", "exec": [""], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"inboundVendorId\": \"{{inboundVendorId}}\",\r\n    \"outboundVendorId\": null,\r\n    \"customReplyToAddress\": null,\r\n    \"useCustomSoapAction\": null,\r\n    \"name\": null,\r\n    \"inboundCredentials\": null,\r\n    \"outboundCredentials\": null,\r\n    \"integrationType\": \"OXI_PMS\",\r\n    \"chains\": [\r\n        {\r\n            \"chainCode\": \"{{client_Code}}\",\r\n            \"outboundCredentials\": null,\r\n            \"inboundCredentials\": null,\r\n            \"clientEnvironmentName\": \"g3\",\r\n            \"baseCurrencyCode\": \"USD\",\r\n            \"hotels\": [\r\n                {\r\n                    \"hotelCode\": \"{{property_Code}}\",\r\n                    \"g3HotelCode\": null,\r\n                    \"g2HotelCode\": null,\r\n                    \"inboundHotelCode\": null,\r\n                    \"propertyName\": \"{{property_Code}}\",\r\n                    \"outgoingUrl\": \"{{hostName}}:{{ngiPort}}/ngipublic/rest/mockserver/oxi/requestroomtype\",\r\n                    \"oxiInterfaceName\": \"OXI_PMS\",\r\n                    \"taxAdjustmentValue\": 10,\r\n                    \"outboundCredentials\": null,\r\n                    \"inboundCredentials\": null,\r\n                    \"baseCurrencyCode\": \"USD\",\r\n                    \"pastDays\": null,\r\n                    \"futureDays\": null,\r\n                    \"assumeTaxIncluded\": false,\r\n                    \"assumePackageIncluded\": true,\r\n                    \"installMode\": false,\r\n                    \"scheduledDeferredDelivery\": null,\r\n                    \"inCatchup\": null,\r\n                    \"calculateNonPickedUpBlocksUsingSummaryData\": null,\r\n                    \"handlePreviouslyStraightMarketSegmentsInAms\": null,\r\n                    \"unqualifiedRatesDirectPopulationDisabled\": false,\r\n                    \"qualifiedRatesDirectPopulationDisabled\": null,\r\n                    \"roomRevenuePackages\": \"BKFS1,BKFS4,BKFS6,BF_REGULAR_LSTORNO2\",\r\n                    \"installationReservationsThreshold\": null,\r\n                    \"populatePackageDataEnabled\": null,\r\n                    \"defaultRoomType\": null\r\n                }\r\n            ],\r\n            \"unqualifiedRatesDirectPopulationDisabled\": null,\r\n            \"qualifiedRatesDirectPopulationDisabled\": null,\r\n            \"installationReservationsThreshold\": null,\r\n            \"populatePackageDataEnabled\": null\r\n        }\r\n    ],\r\n    \"configurations\": null,\r\n    \"unqualifiedRatesDirectPopulationDisabled\": null,\r\n    \"qualifiedRatesDirectPopulationDisabled\": null,\r\n    \"cancelMultiUnitDecrements\": null,\r\n    \"installationReservationsThreshold\": null,\r\n    \"_links\": {\r\n        \"self\": {\r\n            \"href\": \"{{hostName}}:{{ngiPort}}/nucleusVendorConfigParamses/{{inboundVendorId}}\"\r\n        },\r\n        \"nucleusVendorConfigParams\": {\r\n            \"href\": \"{{hostName}}:{{ngiPort}}/nucleusVendorConfigParamses/{{inboundVendorId}}\"\r\n        }\r\n    }\r\n}"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusVendorConfigParamses/{{inboundVendorId}}", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusVendorConfigParamses", "{{inboundVendorId}}"]}}, "response": []}, {"name": "When Reservation for Rate having pkg BKFS1  and VendorConfig has taxIncluded=false,assumePackageIncluded=true", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\\\"1.0\\\" encoding=\\\"UTF-8\\\"?>\r\n<?Label SandBox-OPER1|RESERVATION|*********|SUCCESS|2018060120191231|166/3898?>\r\n<Reservation\r\n    xmlns=\\\"reservation.fidelio.5.0\\\" mfShareAction=\\\"NA\\\" mfReservationAction=\\\"CHECKOUT\\\">\r\n    <HotelReference>\r\n        <hotelCode>SandBox-OPER1</hotelCode>\r\n    </HotelReference>\r\n    <reservationID>*********</reservationID>\r\n    <reservationOriginatorCode>EUNICE_LAU</reservationOriginatorCode>\r\n    <originalBookingDate>2018-07-31T14:56:56.000</originalBookingDate>\r\n    <StayDateRange timeUnitType=\\\"DAY\\\">\r\n        <startTime>2018-08-26T17:33:35.000</startTime>\r\n        <numberOfTimeUnits>1</numberOfTimeUnits>\r\n    </StayDateRange>\r\n    <GuestCounts>\r\n        <GuestCount>\r\n            <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n            <mfCount>2</mfCount>\r\n        </GuestCount>\r\n        <GuestCount>\r\n            <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n            <mfCount>2</mfCount>\r\n        </GuestCount>\r\n    </GuestCounts>\r\n    <ResGuests>\r\n        <ResGuest reservationActionType=\\\"SYNC\\\">\r\n            <resGuestRPH>0</resGuestRPH>\r\n            <profileRPHs>0, 1</profileRPHs>\r\n            <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n            <InHouseTimeSpan timeUnitType=\\\"DAY\\\">\r\n                <startTime>2018-08-26T17:33:35.000</startTime>\r\n                <numberOfTimeUnits>1</numberOfTimeUnits>\r\n            </InHouseTimeSpan>\r\n            <ArrivalTransport/>\r\n            <DepartureTransport/>\r\n            <arrivalTime>2018-08-26T17:33:35.000</arrivalTime>\r\n            <departureTime>2018-08-27T08:34:00.000</departureTime>\r\n            <reservationID>*********</reservationID>\r\n            <ReservationReferences>\r\n                <ReservationReference type=\\\"GUESTID\\\" referenceNumber=\\\"*********\\\" legNumber=\\\"1\\\"/>\r\n                <ReservationReference type=\\\"TA_RECORD_LOCATOR\\\" referenceNumber=\\\"1449708029\\\"/>\r\n            </ReservationReferences>\r\n            <preRegistered>0</preRegistered>\r\n            <commissionPaidTo>T</commissionPaidTo>\r\n        </ResGuest>\r\n    </ResGuests>\r\n    <ResProfiles>\r\n        <ResProfile>\r\n            <Profile\r\n                xmlns=\\\"profile.fidelio.5.0\\\" profileType=\\\"GUEST\\\" multiProperty=\\\"0\\\" miniProfile=\\\"0\\\" pseudoProfile=\\\"0\\\" replaceAddress=\\\"0\\\">\r\n                <creatorCode>SARAH_RYAN1</creatorCode>\r\n                <createdDate>2018-07-31T14:56:05.000</createdDate>\r\n                <lastUpdaterCode>BRYONY_MCLANAGH</lastUpdaterCode>\r\n                <lastUpdated>2018-08-26T18:33:35.000</lastUpdated>\r\n                <IndividualName>\r\n                    <nameSur/>\r\n                </IndividualName>\r\n                <primaryLanguageID>E</primaryLanguageID>\r\n                <PostalAddresses>\r\n                    <PostalAddress addressType=\\\"HOME\\\">\r\n                        <postalCode>Z894K</postalCode>\r\n                        <countryCode>GB</countryCode>\r\n                        <mfPrimaryYN>Y</mfPrimaryYN>\r\n                        <mfAddressLanguage>E</mfAddressLanguage>\r\n                        <cleansed>0</cleansed>\r\n                    </PostalAddress>\r\n                </PostalAddresses>\r\n                <PhoneNumbers>\r\n                    <PhoneNumber phoneNumberType=\\\"HOME\\\">\r\n                        <mfPrimaryYN>Y</mfPrimaryYN>\r\n                        <confirmation>0</confirmation>\r\n                    </PhoneNumber>\r\n                </PhoneNumbers>\r\n                <mfResort>SandBox-OPER1</mfResort>\r\n                <mfResortProfileID>290184999</mfResortProfileID>\r\n                <mfAllowMail>NO</mfAllowMail>\r\n                <mfAllowEMail>NO</mfAllowEMail>\r\n                <mfGuestPriv>NO</mfGuestPriv>\r\n                <mfAllowPhone>0</mfAllowPhone>\r\n                <mfAllowSMS>0</mfAllowSMS>\r\n                <SalesExtention/>\r\n                <PrivacyOption>\r\n                    <mfAllowMail>N</mfAllowMail>\r\n                    <mfAllowEMail>N</mfAllowEMail>\r\n                    <mfAllowPhone>0</mfAllowPhone>\r\n                    <mfAllowSMS>0</mfAllowSMS>\r\n                    <mfAllowHistory>1</mfAllowHistory>\r\n                    <mfAllowMarketResearch>0</mfAllowMarketResearch>\r\n                    <mfAllowThirdParty>0</mfAllowThirdParty>\r\n                </PrivacyOption>\r\n                <ResortList>SandBox-OPER1</ResortList>\r\n                <MultiResortEntities>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>CREDIT_CARDS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RESORT_ARS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>COMMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>PREFERENCES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>NEGOTIATED_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_NEG_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_COMMISSIONS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>CHANNEL_ACCESS_CODES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>YIELD_ADJUSTMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RELATIONSHIPS</entity>\r\n                    </MultiResortEntity>\r\n                </MultiResortEntities>\r\n            </Profile>\r\n            <resProfileRPH>0</resProfileRPH>\r\n        </ResProfile>\r\n        <ResProfile>\r\n            <Profile\r\n                xmlns=\\\"profile.fidelio.5.0\\\" profileType=\\\"TRAVEL\\\" multiProperty=\\\"0\\\" miniProfile=\\\"0\\\" pseudoProfile=\\\"0\\\" replaceAddress=\\\"0\\\">\r\n                <creatorCode>OXI-OPERA</creatorCode>\r\n                <createdDate>2012-08-10T21:36:40.000</createdDate>\r\n                <lastUpdaterCode>NIKITA_AGGARWAL</lastUpdaterCode>\r\n                <lastUpdated>2018-09-10T17:39:10.000</lastUpdated>\r\n                <genericName>booking.com</genericName>\r\n                <IndividualName>\r\n                    <nameSur/>\r\n                </IndividualName>\r\n                <mfResort>SandBox-OPER1</mfResort>\r\n                <mfResortProfileID>5371710</mfResortProfileID>\r\n                <mfNameCode>96040394</mfNameCode>\r\n                <mfAllowMail>YES</mfAllowMail>\r\n                <mfAllowEMail>NO</mfAllowEMail>\r\n                <mfGuestPriv>NO</mfGuestPriv>\r\n                <mfAllowPhone>0</mfAllowPhone>\r\n                <mfAllowSMS>0</mfAllowSMS>\r\n                <SalesExtention/>\r\n                <PrivacyOption>\r\n                    <mfAllowMail>Y</mfAllowMail>\r\n                    <mfAllowEMail>N</mfAllowEMail>\r\n                    <mfAllowPhone>0</mfAllowPhone>\r\n                    <mfAllowSMS>0</mfAllowSMS>\r\n                    <mfAllowHistory>1</mfAllowHistory>\r\n                    <mfAllowMarketResearch>1</mfAllowMarketResearch>\r\n                    <mfAllowThirdParty>1</mfAllowThirdParty>\r\n                </PrivacyOption>\r\n                <ResortList>SandBox-OPER1</ResortList>\r\n                <MultiResortEntities>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>CREDIT_CARDS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RESORT_ARS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>COMMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>PREFERENCES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>NEGOTIATED_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_NEG_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_COMMISSIONS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>CHANNEL_ACCESS_CODES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>YIELD_ADJUSTMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RELATIONSHIPS</entity>\r\n                    </MultiResortEntity>\r\n                </MultiResortEntities>\r\n            </Profile>\r\n            <resProfileRPH>1</resProfileRPH>\r\n        </ResProfile>\r\n    </ResProfiles>\r\n    <RoomStays>\r\n        <RoomStay mfShareAction=\\\"NA\\\" mfReservationAction=\\\"NA\\\" reservationActionType=\\\"SYNC\\\" reservationStatusType=\\\"CHECKEDOUT\\\">\r\n            <roomInventoryCode>CKC</roomInventoryCode>\r\n            <roomID>311</roomID>\r\n            <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                <startTime>2018-08-26T17:33:35.000</startTime>\r\n                <numberOfTimeUnits>1</numberOfTimeUnits>\r\n            </TimeSpan>\r\n            <GuestCounts>\r\n                <GuestCount>\r\n                    <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n                    <mfCount>2</mfCount>\r\n                </GuestCount>\r\n                <GuestCount>\r\n                    <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n                    <mfCount>2</mfCount>\r\n                </GuestCount>\r\n            </GuestCounts>\r\n            <RatePlans>\r\n                <RatePlan reservationActionType=\\\"SYNC\\\">\r\n                    <ratePlanRPH>0</ratePlanRPH>\r\n                    <ratePlanCode>OTAGENA2</ratePlanCode>\r\n                    <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                        <startTime>2018-08-26T00:00:00.000</startTime>\r\n                        <numberOfTimeUnits>1</numberOfTimeUnits>\r\n                    </TimeSpan>\r\n                    <mfMarketCode>OTA</mfMarketCode>\r\n                    <Rates>\r\n                        <Rate reservationActionType=\\\"SYNC\\\" rateBasisTimeUnitType=\\\"DAY\\\">\r\n                            <rateRPH>0</rateRPH>\r\n                            <Amount currencyCode=\\\"USD\\\">\r\n                                <valueNum>169</valueNum>\r\n                            </Amount>\r\n                            <rateBasisUnits>1</rateBasisUnits>\r\n                            <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                                <startTime>2018-08-26T00:00:00.000</startTime>\r\n                                <numberOfTimeUnits>1</numberOfTimeUnits>\r\n                            </TimeSpan>\r\n                            <mfAdults>2</mfAdults>\r\n                            <mfChildren>2</mfChildren>\r\n                            <mfCribs>0</mfCribs>\r\n                            <mfExtraBeds>0</mfExtraBeds>\r\n                            <mfsourceCode>BC</mfsourceCode>\r\n                            <mfMarketCode>OTA</mfMarketCode>\r\n                        </Rate>\r\n                    </Rates>\r\n                    <mfsourceCode>BC</mfsourceCode>\r\n                </RatePlan>\r\n            </RatePlans>\r\n            <marketSegmentCode>OTA</marketSegmentCode>\r\n            <resGuestRPHs>0</resGuestRPHs>\r\n            <resCommentRPHs>0</resCommentRPHs>\r\n            <GuaranteeInfo guaranteeType=\\\"NA\\\">\r\n                <mfGuaranteeType>CHECKED IN</mfGuaranteeType>\r\n                <GuaranteeDeposit>\r\n                    <Amount currencyCode=\\\"USD\\\"/>\r\n                    <cancelIfNotReceived>0</cancelIfNotReceived>\r\n                </GuaranteeDeposit>\r\n            </GuaranteeInfo>\r\n            <CancelPenalties>\r\n                <CancelPenalty mfRuleType=\\\"CANCEL\\\" manualRule=\\\"0\\\">\r\n                    <cancelByDate>2018-08-26T14:00:00.000</cancelByDate>\r\n                    <Amount currencyCode=\\\"USD\\\">\r\n                        <valueNum>169</valueNum>\r\n                    </Amount>\r\n                    <mfRuleScope>R</mfRuleScope>\r\n                    <mfPercentage>100</mfPercentage>\r\n                    <mfCancelPercentDue>100</mfCancelPercentDue>\r\n                    <mfCancelRoomNights>1</mfCancelRoomNights>\r\n                    <mfRuleDescription>Cancel by 2pm on the Day of Arrival</mfRuleDescription>\r\n                    <ruleCode>2PM</ruleCode>\r\n                </CancelPenalty>\r\n            </CancelPenalties>\r\n            <PaymentInstructions>\r\n                <PaymentInstruction paymentMethodType=\\\"NA\\\">\r\n                    <mfPaymentMethod>CA</mfPaymentMethod>\r\n                    <PaymentDue>\r\n                        <Amount currencyCode=\\\"USD\\\"/>\r\n                        <cancelIfNotReceived>0</cancelIfNotReceived>\r\n                    </PaymentDue>\r\n                </PaymentInstruction>\r\n            </PaymentInstructions>\r\n            <mfcomplementaryCode>FAM</mfcomplementaryCode>\r\n            <mfsourceCode>BC</mfsourceCode>\r\n            <mfchannelCode>OTA</mfchannelCode>\r\n            <mfconfidentialRate>1</mfconfidentialRate>\r\n            <mfAsbProrated>0</mfAsbProrated>\r\n        </RoomStay>\r\n    </RoomStays>\r\n    <resCommentRPHs>0</resCommentRPHs>\r\n    <resProfileRPHs>0, 1</resProfileRPHs>\r\n    <mfupdateDate>2018-08-27T09:34:20.000</mfupdateDate>\r\n    <mfcomplementary>0</mfcomplementary>\r\n    <mfImage>\r\n        <numRooms>1</numRooms>\r\n        <Describe>\r\n            <resortName>Huntingtower Hotel Perth - Boutique by Leonardo</resortName>\r\n            <insertUser>SARAH_RYAN1</insertUser>\r\n            <updateUser>EUNICE_LAU</updateUser>\r\n            <roomCategory>Standard King room with Sofa</roomCategory>\r\n            <rateCode>Flexible Rate Inc Breakfast</rateCode>\r\n            <marketCode>Online Travel Agents</marketCode>\r\n            <guarantee>CHECKED IN: Checked In</guarantee>\r\n            <company>Jurys Inns Group Limited</company>\r\n        </Describe>\r\n        <Change>\r\n            <bArrivalDate>0</bArrivalDate>\r\n            <bNumNights>0</bNumNights>\r\n            <bNumAdults>0</bNumAdults>\r\n            <bNumChildren>0</bNumChildren>\r\n            <bNumRooms>0</bNumRooms>\r\n            <bCribs>0</bCribs>\r\n            <bRoomCategory>0</bRoomCategory>\r\n            <bPaymentType>0</bPaymentType>\r\n            <bGuarType>0</bGuarType>\r\n            <bDiscountReason>0</bDiscountReason>\r\n            <bMultipleRateYN>0</bMultipleRateYN>\r\n            <bResvStatus>0</bResvStatus>\r\n        </Change>\r\n    </mfImage>\r\n    <RateServices>\r\n        <RateService>\r\n            <Service reservationActionType=\\\"SYNC\\\" servicePricingType=\\\"NA\\\" reservationStatusType=\\\"NA\\\">\r\n                <serviceRPH>0</serviceRPH>\r\n                <serviceInventoryCode>BKFS1</serviceInventoryCode>\r\n                <ratePlanCode>OTAGENA2</ratePlanCode>\r\n                <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                    <startTime>2018-08-26T00:00:00.000</startTime>\r\n                    <numberOfTimeUnits>1</numberOfTimeUnits>\r\n                </TimeSpan>\r\n                <Price currencyCode=\\\"USD\\\">\r\n                    <valueNum>12</valueNum>\r\n                </Price>\r\n                <quantity>1</quantity>\r\n                <ServicePrices>\r\n                    <ServicePrice>\r\n                        <beginDate>2018-08-26</beginDate>\r\n                        <endDate>2018-08-26</endDate>\r\n                        <unitPrice>6</unitPrice>\r\n                    </ServicePrice>\r\n                </ServicePrices>\r\n            </Service>\r\n        </RateService>\r\n    </RateServices>\r\n</Reservation>"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/ngipublic/rest/oxi/pms", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["ngipublic", "rest", "oxi", "pms"]}}, "response": []}, {"name": "Then NGI calculates rateValue for Reservation having rateCode=OTAGENA2, serviceName=BKFS1", "event": [{"listen": "test", "script": {"id": "ff746b8b-8456-4fd4-b4bd-abbb83d974cb", "exec": ["//variables", "var jsonData = pm.response.json();", "", "//value to compare with", "var reservationJSONToBeCompared ={\"versionId\":*********,\"reservationId\":\"*********\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"statisticsCorrelationId\":null,\"bookingDate\":\"2018-07-31T14:56:56.000\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"CHECKED_OUT\",\"nationality\":\"GB\",\"channel\":\"OTA\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2018-08-26\",\"departureDate\":\"2018-08-27\",\"marketCode\":\"OTA\",\"sourceBookingCode\":\"BC\",\"numberOfChildren\":2,\"numberOfAdults\":2,\"bookingType\":\"CHECKED IN\",\"invTypeCode\":\"CKC\",\"bookedAccomTypeCode\":\"FAM\",\"rateCode\":\"OTAGENA2\",\"invBlockCode\":null,\"roomNumber\":\"311\",\"rates\":[{\"rateValue\":169,\"startDate\":\"2018-08-26\",\"endDate\":\"2018-08-27\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":169,\"currencyCode\":null}],\"services\":[{\"serviceName\":\"BKFS1\",\"serviceValue\":12,\"inclusive\":true,\"roomRevenuePackage\":true,\"reservationPackage\":false,\"startDate\":\"2018-08-26\",\"endDate\":\"2018-08-27\",\"inclusionType\":null}],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"OTA\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"earliestArrivalDate\":\"2018-08-26\",\"latestDepartureDate\":\"2018-08-27\",\"primary\":null};", "", "//assertions", "assertJSONData(\"Verifying Client Code in OxiMessage\", jsonData,reservationJSONToBeCompared);", "", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue.versionId).to.eql(expectedValue.versionId);", "    pm.expect(actualValue.clientCode).to.eql(expectedValue.clientCode);", "    pm.expect(actualValue.propertyCode).to.eql(expectedValue.propertyCode);", "    pm.expect(actualValue.status).to.eql(expectedValue.status);", "    pm.expect(actualValue.nationality).to.eql(expectedValue.nationality);", "    pm.expect(actualValue.channel).to.eql(expectedValue.channel);", "     pm.expect(actualValue.reservationId).to.eql(expectedValue.reservationId);", "    pm.expect(actualValue.roomStays.arrivalDate).to.eql(expectedValue.roomStays.arrivalDate);", "    pm.expect(actualValue.roomStays.departureDate).to.eql(expectedValue.roomStays.departureDate);", "    pm.expect(actualValue.roomStays.marketCode).to.eql(expectedValue.roomStays.marketCode);", "    pm.expect(actualValue.roomStays.sourceBookingCode).to.eql(expectedValue.roomStays.sourceBookingCode);", "    pm.expect(actualValue.roomStays.numberOfChildren).to.eql(expectedValue.roomStays.numberOfChildren);", "    pm.expect(actualValue.roomStays.numberOfAdults).to.eql(expectedValue.roomStays.numberOfAdults);", "    pm.expect(actualValue.roomStays.bookingType).to.eql(expectedValue.roomStays.bookingType);", "    pm.expect(actualValue.roomStays.invTypeCode).to.eql(expectedValue.roomStays.invTypeCode);", "    pm.expect(actualValue.roomStays.bookedAccomTypeCode).to.eql(expectedValue.roomStays.bookedAccomTypeCode);", "    pm.expect(actualValue.roomStays.rateCode).to.eql(expectedValue.roomStays.rateCode);", "    pm.expect(actualValue.roomStays[0].rates[0].rateValue).to.eql(expectedValue.roomStays[0].rates[0].rateValue);", "    pm.expect(actualValue.roomStays[0].rates[0].startDate).to.eql(expectedValue.roomStays[0].rates[0].startDate);", "    pm.expect(actualValue.roomStays[0].rates[0].endDate).to.eql(expectedValue.roomStays[0].rates[0].endDate);", "    pm.expect(actualValue.roomStays[0].rates[0].grossRate).to.eql(expectedValue.roomStays[0].rates[0].grossRate);", "    pm.expect(actualValue.roomStays[0].services[0].serviceName).to.eql(expectedValue.roomStays[0].services[0].serviceName);", "    pm.expect(actualValue.roomStays[0].services[0].serviceValue).to.eql(expectedValue.roomStays[0].services[0].serviceValue);", "    pm.expect(actualValue.roomStays[0].services[0].inclusive).to.eql(expectedValue.roomStays[0].services[0].inclusive);", "    pm.expect(actualValue.roomStays[0].services[0].roomRevenuePackage).to.eql(expectedValue.roomStays[0].services[0].roomRevenuePackage);", "    pm.expect(actualValue.roomStays[0].services[0].reservationPackage).to.eql(expectedValue.roomStays[0].services[0].reservationPackage);", "    pm.expect(actualValue.roomStays[0].services[0].endDate).to.eql(expectedValue.roomStays[0].services[0].endDate);", "    pm.expect(actualValue.roomStays[0].services[0].startDate).to.eql(expectedValue.roomStays[0].services[0].startDate);", "   pm.expect(actualValue.roomStays.originalCurrencyCode).to.eql(expectedValue.roomStays.originalCurrencyCode);", "   pm.expect(actualValue.roomStays.analyticalMarketSegmentCode).to.eql(expectedValue.roomStays.analyticalMarketSegmentCode);", "});}"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "53a71e40-22e3-4d68-940c-bea115baea4f", "exec": ["/*var millisecondsToWait = 5000;\r", "setTimeout(function() {\r", "    // Whatever you want to do after the wait\r", "}, millisecondsToWait);*/"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusReservations/search/findByClientCodeAndPropertyCodeAndReservationId?clientCode={{client_Code}}&propertyCode={{property_Code}}&reservationId=*********", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusReservations", "search", "findByClientCodeAndPropertyCodeAndReservationId"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "reservationId", "value": "*********"}]}}, "response": []}, {"name": "When Reservation for Rate having multiple rateCodes  and VendorConfig has taxIncluded=false,assumePackageIncluded=true", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<?Label SandBox-OPER1|RESERVATION|20160127003|SUCCESS?>\r\n<!-- Reservation_PackagesOneDay_Msg1 - Reservation using Rate Plans with Included Packages in Complex Ways\r\nIn this message: Reservation is made for 2 adults and 2 kids for 7 nights with 2 nights on Rate CEN27 then 4 nights on FAMILT and 1 night again on CEN27.\r\nCEN27 is $375 but includes BKFST posted each night at $100 for all people so the net rate is $275\r\nFAMILT is $309 but includes ARCADE at $20 for first night (net rate $289), FUNBAGS at $40 next 2 nights (net rate $269) and \r\nPHOTOS at $90 for last night with Rate (net rate $219) \r\nPASSES is not defined in Rate so have to assume it is not included and those values ignored -->\r\n<Reservation xmlns=\"reservation.fidelio.5.0\" mfShareAction=\"NA\" mfReservationAction=\"EDIT\">\r\n  <HotelReference>\r\n    <hotelCode>SandBox-OPER1</hotelCode>\r\n  </HotelReference>\r\n  <reservationID>673795</reservationID>\r\n  <reservationOriginatorCode>SUPERVISOR</reservationOriginatorCode>\r\n  <originalBookingDate>2015-04-20T19:06:51.000</originalBookingDate>\r\n  <StayDateRange timeUnitType=\"DAY\">\r\n    <startTime>2016-05-18T00:00:00.000</startTime>\r\n    <numberOfTimeUnits>7</numberOfTimeUnits>\r\n  </StayDateRange>\r\n  <GuestCounts>\r\n    <GuestCount>\r\n      <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n      <mfCount>2</mfCount>\r\n    </GuestCount>\r\n    <GuestCount>\r\n      <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n      <mfCount>2</mfCount>\r\n    </GuestCount>\r\n  </GuestCounts>\r\n  <ResGuests>\r\n    <ResGuest reservationActionType=\"SYNC\">\r\n      <resGuestRPH>0</resGuestRPH>\r\n      <profileRPHs>0</profileRPHs>\r\n      <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n      <InHouseTimeSpan timeUnitType=\"DAY\">\r\n        <startTime>2016-05-18T00:00:00.000</startTime>\r\n        <numberOfTimeUnits>7</numberOfTimeUnits>\r\n      </InHouseTimeSpan>\r\n      <ArrivalTransport>\r\n        <transportID>AA/421</transportID>\r\n        <locationCode>HEA</locationCode>\r\n        <transportTime>2016-05-18T14:30:00.000</transportTime>\r\n      </ArrivalTransport>\r\n      <DepartureTransport transportType=\"Train\">\r\n        <transportID>DE/1421</transportID>\r\n        <locationCode>RSW</locationCode>\r\n        <transportTime>2016-05-25T16:30:00.000</transportTime>\r\n      </DepartureTransport>\r\n      <arrivalTime>2016-05-18T00:00:00.000</arrivalTime>\r\n      <departureTime>2011-05-25T00:00:00.000</departureTime>\r\n      <reservationID>673795</reservationID>\r\n      <ReservationReferences>\r\n        <ReservationReference type=\"GUESTID\" referenceNumber=\"673795\" legNumber=\"1\"/>\r\n        <ReservationReference type=\"PMSID\" referenceNumber=\"673795\" legNumber=\"1\"/>\r\n      </ReservationReferences>\r\n      <preRegistered>0</preRegistered>\r\n      <commissionPaidTo>N</commissionPaidTo>\r\n    </ResGuest>\r\n  </ResGuests>\r\n  <ResProfiles>\r\n    <ResProfile>\r\n      <Profile xmlns=\"profile.fidelio.5.0\" profileType=\"GUEST\" gender=\"U\" multiProperty=\"0\" miniProfile=\"0\" pseudoProfile=\"0\" replaceAddress=\"0\">\r\n        <creatorCode>SUPERVISOR</creatorCode>\r\n        <createdDate>2006-06-06T11:10:05.000</createdDate>\r\n        <lastUpdaterCode>*ORS*</lastUpdaterCode>\r\n        <lastUpdated>2014-08-08T13:33:21.000</lastUpdated>\r\n        <preferredRatePlanCode>MEMBER</preferredRatePlanCode>\r\n        <genericName>Morgenstern</genericName>\r\n        <IndividualName>\r\n          <nameFirst>Eric1</nameFirst>\r\n          <nameSur>Morgenstern</nameSur>\r\n        </IndividualName>\r\n        <corporatePosition>GEFY CO</corporatePosition>\r\n        <dateOfBirth>1970-03-09</dateOfBirth>\r\n        <primaryLanguageID>E</primaryLanguageID>\r\n        <ElectronicAddresses>\r\n          <ElectronicAddress electronicAddressType=\"EMAIL\">\r\n            <eAddress><EMAIL></eAddress>\r\n            <mfPrimaryYN>Y</mfPrimaryYN>\r\n            <confirmation>0</confirmation>\r\n          </ElectronicAddress>\r\n        </ElectronicAddresses>\r\n        <Memberships>\r\n          <Membership>\r\n            <programCode>DL</programCode>\r\n            <accountID>**********</accountID>\r\n            <levelCode>BASE</levelCode>\r\n            <startDate>2013-04-06</startDate>\r\n            <nameOnCard>ERIC1 MORGENSTERN</nameOnCard>\r\n            <displaySequence>4</displaySequence>\r\n            <pointIndicator>0</pointIndicator>\r\n            <enrollmentSource>N</enrollmentSource>\r\n            <enrolledAt>N</enrolledAt>\r\n          </Membership>\r\n          <Membership>\r\n            <programCode>ENCORE</programCode>\r\n            <accountID>4808263</accountID>\r\n            <startDate>2009-09-03</startDate>\r\n            <nameOnCard>ERIC1 MORGENSTERN</nameOnCard>\r\n            <displaySequence>2</displaySequence>\r\n            <pointIndicator>0</pointIndicator>\r\n            <enrollmentSource>N</enrollmentSource>\r\n            <enrolledAt>N</enrolledAt>\r\n          </Membership>\r\n          <Membership>\r\n            <programCode>OCIS</programCode>\r\n            <accountID>********</accountID>\r\n            <levelCode>GOLD</levelCode>\r\n            <expireDate>2016-06-30</expireDate>\r\n            <startDate>2004-07-23</startDate>\r\n            <nameOnCard>ERIC1 MORGENSTERN</nameOnCard>\r\n            <membershipStatus>TEMP</membershipStatus>\r\n            <displaySequence>1</displaySequence>\r\n            <enrollmentCode>WEB</enrollmentCode>\r\n            <pointIndicator>1</pointIndicator>\r\n            <enrollmentSource>ON THE WEB</enrollmentSource>\r\n            <enrolledAt>MICROS WEBSITE</enrolledAt>\r\n          </Membership>\r\n          <Membership>\r\n            <programCode>US</programCode>\r\n            <accountID>4R931D6</accountID>\r\n            <levelCode>PLATINUM</levelCode>\r\n            <startDate>2013-04-06</startDate>\r\n            <nameOnCard>ERIC1 MORGENSTERN</nameOnCard>\r\n            <displaySequence>5</displaySequence>\r\n            <pointIndicator>0</pointIndicator>\r\n            <enrollmentSource>N</enrollmentSource>\r\n            <enrolledAt>N</enrolledAt>\r\n          </Membership>\r\n          <Membership mfInactiveDate=\"2014-05-21T00:00:00.000\">\r\n            <programCode>XYZ</programCode>\r\n            <accountID>**********</accountID>\r\n            <startDate>2013-04-06</startDate>\r\n            <nameOnCard>ERIC1 MORGENSTERN</nameOnCard>\r\n            <displaySequence>3</displaySequence>\r\n            <pointIndicator>0</pointIndicator>\r\n            <enrollmentSource>N</enrollmentSource>\r\n            <enrolledAt>N</enrolledAt>\r\n          </Membership>\r\n        </Memberships>\r\n        <PostalAddresses>\r\n          <PostalAddress addressType=\"BUSINESS\">\r\n            <address1>7031 Columbia Gateway Drive</address1>\r\n            <address2>OGTS - 2nd Floor</address2>\r\n            <city>Columbia</city>\r\n            <stateCode>MD</stateCode>\r\n            <postalCode>21046</postalCode>\r\n            <countryCode>IN</countryCode>\r\n            <mfPrimaryYN>Y</mfPrimaryYN>\r\n            <mfAddressLanguage>E</mfAddressLanguage>\r\n            <cleansed>0</cleansed>\r\n            <barCode>THISISABARCODE1234FORMAT</barCode>\r\n          </PostalAddress>\r\n          <PostalAddress addressType=\"HOME\">\r\n            <countryCode>GB</countryCode>\r\n            <mfPrimaryYN>N</mfPrimaryYN>\r\n            <mfAddressLanguage>E</mfAddressLanguage>\r\n            <cleansed>0</cleansed>\r\n          </PostalAddress>\r\n          <PostalAddress addressType=\"HOME\">\r\n            <address1>2640 Parkway De la Puerta De oro</address1>\r\n            <address2>Habitaci�n 315</address2>\r\n            <city>N�poles</city>\r\n            <stateCode>FL</stateCode>\r\n            <postalCode>34105</postalCode>\r\n            <countryCode>US</countryCode>\r\n            <mfPrimaryYN>N</mfPrimaryYN>\r\n            <mfAddressLanguage>S</mfAddressLanguage>\r\n            <cleansed>0</cleansed>\r\n          </PostalAddress>\r\n          <PostalAddress addressType=\"HOME\">\r\n            <address1>7031 Columbia Gateway Drive</address1>\r\n            <address2>OGTS - 2nd Floor / Cube 5</address2>\r\n            <city>Columbia</city>\r\n            <stateCode>MD</stateCode>\r\n            <postalCode>21046</postalCode>\r\n            <countryCode>US</countryCode>\r\n            <mfPrimaryYN>N</mfPrimaryYN>\r\n            <cleansed>0</cleansed>\r\n          </PostalAddress>\r\n        </PostalAddresses>\r\n        <PhoneNumbers>\r\n          <PhoneNumber phoneNumberType=\"BUSINESS\">\r\n            <phoneNumber>************</phoneNumber>\r\n            <mfPrimaryYN>Y</mfPrimaryYN>\r\n            <confirmation>0</confirmation>\r\n          </PhoneNumber>\r\n          <PhoneNumber phoneNumberType=\"MOBILE\">\r\n            <phoneNumber>**************</phoneNumber>\r\n            <mfPrimaryYN>N</mfPrimaryYN>\r\n            <confirmation>0</confirmation>\r\n          </PhoneNumber>\r\n        </PhoneNumbers>\r\n        <SpecialRequests>\r\n          <SpecialRequest mfSpecialRequestType=\"FEA\">\r\n            <requestCode>NS</requestCode>\r\n          </SpecialRequest>\r\n        </SpecialRequests>\r\n        <Comments>\r\n          <Comment>\r\n            <commentOriginatorCode>*ORS*</commentOriginatorCode>\r\n            <lastUpdated>2014-08-08T13:33:21.000</lastUpdated>\r\n            <commentStr>Backgound NOtes</commentStr>\r\n            <commentType>BKG</commentType>\r\n            <commentTitle>Profile Backgrd Note</commentTitle>\r\n          </Comment>\r\n          <Comment>\r\n            <commentOriginatorCode>*ORS*</commentOriginatorCode>\r\n            <lastUpdated>2014-08-08T13:33:21.000</lastUpdated>\r\n            <commentStr>Global Background Note</commentStr>\r\n            <commentType>BKG</commentType>\r\n            <commentTitle>Background Notes</commentTitle>\r\n          </Comment>\r\n          <Comment>\r\n            <commentOriginatorCode>SUPERVISOR</commentOriginatorCode>\r\n            <lastUpdated>2014-06-25T12:32:20.000</lastUpdated>\r\n            <commentStr>Profile is banned</commentStr>\r\n            <commentType>OWS</commentType>\r\n            <commentTitle>BANNED</commentTitle>\r\n          </Comment>\r\n       </Comments>\r\n        <mfResort>SandBox-OPER1</mfResort>\r\n        <mfResortProfileID>18082</mfResortProfileID>\r\n        <mfVipStatus>2</mfVipStatus>\r\n        <mfARNumber>EJM101</mfARNumber>\r\n        <mfAllowMail>YES</mfAllowMail>\r\n        <mfAllowEMail>NO</mfAllowEMail>\r\n        <mfGuestPriv>NO</mfGuestPriv>\r\n        <mfNegotiatedRates>\r\n          <NegotiatedRate>\r\n            <mfResort>SandBox-OPER1</mfResort>\r\n            <rateCode>MEMBER</rateCode>\r\n            <rateBeginDate>2007-03-05</rateBeginDate>\r\n          </NegotiatedRate>\r\n        </mfNegotiatedRates>\r\n        <mfAllowPhone>1</mfAllowPhone>\r\n        <mfAllowSMS>0</mfAllowSMS>\r\n        <SalesExtention>\r\n          <actionCode>SPA</actionCode>\r\n          <businessSegment>LR</businessSegment>\r\n        </SalesExtention>\r\n        <PrivacyOption>\r\n          <mfAllowMail>Y</mfAllowMail>\r\n          <mfAllowEMail>N</mfAllowEMail>\r\n          <mfAllowPhone>1</mfAllowPhone>\r\n          <mfAllowSMS>0</mfAllowSMS>\r\n          <mfAllowHistory>1</mfAllowHistory>\r\n         <mfAllowMarketResearch>0</mfAllowMarketResearch>\r\n          <mfAllowThirdParty>0</mfAllowThirdParty>\r\n        </PrivacyOption>\r\n        <ResortList>SandBox-OPER1</ResortList>\r\n        <MultiResortEntities>\r\n          <MultiResortEntity included=\"0\">\r\n            <entity>CREDIT_CARDS</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"0\">\r\n            <entity>RESORT_ARS</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"1\">\r\n            <entity>COMMENTS</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"1\">\r\n            <entity>PREFERENCES</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"1\">\r\n            <entity>NEGOTIATED_RATES</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"1\">\r\n            <entity>RESORT_NEG_RATES</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"1\">\r\n            <entity>CHANNEL_ACCESS_CODES</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"1\">\r\n            <entity>YIELD_ADJUSTMENTS</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"0\">\r\n            <entity>RELATIONSHIPS</entity>\r\n          </MultiResortEntity>\r\n        </MultiResortEntities>\r\n        <ResortComments>\r\n          <ResortComment>\r\n            <Comment>\r\n              <commentOriginatorCode>*ORS*</commentOriginatorCode>\r\n              <guestViewable>1</guestViewable>\r\n              <lastUpdated>2014-08-08T13:33:21.000</lastUpdated>\r\n              <commentStr>Backgound NOtes</commentStr>\r\n              <commentType>BKG</commentType>\r\n              <commentTitle>Profile Backgrd Note</commentTitle>\r\n            </Comment>\r\n          </ResortComment>\r\n          <ResortComment>\r\n            <Comment>\r\n              <commentOriginatorCode>*ORS*</commentOriginatorCode>\r\n              <guestViewable>1</guestViewable>\r\n              <lastUpdated>2014-08-08T13:33:21.000</lastUpdated>\r\n              <commentStr>Global Background Note</commentStr>\r\n              <commentType>BKG</commentType>\r\n              <commentTitle>Background Notes</commentTitle>\r\n            </Comment>\r\n          </ResortComment>\r\n          <ResortComment>\r\n            <Comment>\r\n              <commentOriginatorCode>SUPERVISOR</commentOriginatorCode>\r\n              <guestViewable>1</guestViewable>\r\n              <lastUpdated>2014-06-25T12:32:20.000</lastUpdated>\r\n              <commentStr>Profile is banned</commentStr>\r\n              <commentType>OWS</commentType>\r\n              <commentTitle>BANNED</commentTitle>\r\n            </Comment>\r\n          </ResortComment>\r\n        </ResortComments>\r\n        <ResortSpecialRequests>\r\n          <SpecialRequest mfSpecialRequestType=\"FEA\">\r\n            <requestCode>NS</requestCode>\r\n          </SpecialRequest>\r\n        </ResortSpecialRequests>\r\n        <WebAccounts>\r\n          <WebAccount>\r\n            <loginName>EMORGENSTERN</loginName>\r\n            <password>lGo17E/imFU=</password>\r\n            <secQuestion>Hp4riAzXmuw=</secQuestion>\r\n            <secAnswer>yjraoU8Yov0=</secAnswer>\r\n            <lastLoginDate>2010-10-20T09:21:29.000</lastLoginDate>\r\n            <insertSource>OWS</insertSource>\r\n            <passwordChangeDate>2012-05-01T16:29:26.000</passwordChangeDate>\r\n            <pwdSystemGenerated>0</pwdSystemGenerated>\r\n          </WebAccount>\r\n        </WebAccounts>\r\n        <ResortNegotiatedRates>\r\n          <NegotiatedRate>\r\n            <mfResort>SandBox-OPER1</mfResort>\r\n            <rateCode>MEMBER</rateCode>\r\n            <rateBeginDate>2007-03-05</rateBeginDate>\r\n          </NegotiatedRate>\r\n        </ResortNegotiatedRates>\r\n        <nationality>E</nationality>\r\n      </Profile>\r\n      <resProfileRPH>0</resProfileRPH>\r\n    </ResProfile>\r\n  </ResProfiles>\r\n  <RoomStays>\r\n    <RoomStay mfShareAction=\"NA\" mfReservationAction=\"NA\" reservationActionType=\"SYNC\" reservationStatusType=\"RESERVED\">\r\n      <roomInventoryCode>KDLX</roomInventoryCode>\r\n      <TimeSpan timeUnitType=\"DAY\">\r\n        <startTime>2016-05-18T00:00:00.000</startTime>\r\n        <numberOfTimeUnits>7</numberOfTimeUnits>\r\n      </TimeSpan>\r\n      <GuestCounts>\r\n        <GuestCount>\r\n          <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n          <mfCount>2</mfCount>\r\n        </GuestCount>\r\n        <GuestCount>\r\n          <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n          <mfCount>2</mfCount>\r\n        </GuestCount>\r\n      </GuestCounts>\r\n      <RatePlans>\r\n        <RatePlan reservationActionType=\"SYNC\">\r\n          <ratePlanRPH>0</ratePlanRPH>\r\n          <ratePlanCode>CEN27</ratePlanCode>\r\n          <TimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-05-18T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>2</numberOfTimeUnits>\r\n          </TimeSpan>\r\n          <mfMarketCode>NTRAN</mfMarketCode>\r\n          <Rates>\r\n            <Rate reservationActionType=\"SYNC\" rateBasisTimeUnitType=\"DAY\">\r\n              <rateRPH>0</rateRPH>\r\n              <Amount currencyCode=\"USD\">\r\n                <valueNum>375</valueNum>\r\n              </Amount>\r\n              <rateBasisUnits>1</rateBasisUnits>\r\n              <TimeSpan timeUnitType=\"DAY\">\r\n                <startTime>2016-05-18T00:00:00.000</startTime>\r\n                <numberOfTimeUnits>2</numberOfTimeUnits>\r\n              </TimeSpan>\r\n              <mfAdults>2</mfAdults>\r\n              <mfChildren>2</mfChildren>\r\n              <mfCribs>0</mfCribs>\r\n              <mfExtraBeds>0</mfExtraBeds>\r\n              <mfsourceCode>WEB</mfsourceCode>\r\n              <mfMarketCode>NTRAN</mfMarketCode>\r\n            </Rate>\r\n          </Rates>\r\n          <mfsourceCode>WEB</mfsourceCode>\r\n        </RatePlan>\r\n        <RatePlan reservationActionType=\"SYNC\">\r\n          <ratePlanRPH>1</ratePlanRPH>\r\n          <ratePlanCode>FAMILT</ratePlanCode>\r\n          <TimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-05-20T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>4</numberOfTimeUnits>\r\n          </TimeSpan>\r\n          <mfMarketCode>ITRAN</mfMarketCode>\r\n          <Rates>\r\n            <Rate reservationActionType=\"SYNC\" rateBasisTimeUnitType=\"DAY\">\r\n              <rateRPH>1</rateRPH>\r\n              <Amount currencyCode=\"USD\">\r\n                <valueNum>309</valueNum>\r\n              </Amount>\r\n              <rateBasisUnits>1</rateBasisUnits>\r\n              <TimeSpan timeUnitType=\"DAY\">\r\n                <startTime>2016-05-20T00:00:00.000</startTime>\r\n                <numberOfTimeUnits>4</numberOfTimeUnits>\r\n              </TimeSpan>\r\n              <mfAdults>2</mfAdults>\r\n              <mfChildren>2</mfChildren>\r\n              <mfCribs>0</mfCribs>\r\n              <mfExtraBeds>0</mfExtraBeds>\r\n              <mfsourceCode>WEB</mfsourceCode>\r\n              <mfMarketCode>ITRAN</mfMarketCode>\r\n            </Rate>\r\n          </Rates>\r\n          <mfsourceCode>WEB</mfsourceCode>\r\n        </RatePlan>\r\n        <RatePlan reservationActionType=\"SYNC\">\r\n          <ratePlanRPH>2</ratePlanRPH>\r\n          <ratePlanCode>CEN27</ratePlanCode>\r\n          <TimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-05-24T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>1</numberOfTimeUnits>\r\n          </TimeSpan>\r\n          <mfMarketCode>NTRAN</mfMarketCode>\r\n          <Rates>\r\n            <Rate reservationActionType=\"SYNC\" rateBasisTimeUnitType=\"DAY\">\r\n              <rateRPH>2</rateRPH>\r\n              <Amount currencyCode=\"USD\">\r\n                <valueNum>375</valueNum>\r\n              </Amount>\r\n              <rateBasisUnits>1</rateBasisUnits>\r\n              <TimeSpan timeUnitType=\"DAY\">\r\n                <startTime>2016-05-24T00:00:00.000</startTime>\r\n                <numberOfTimeUnits>1</numberOfTimeUnits>\r\n              </TimeSpan>\r\n              <mfAdults>2</mfAdults>\r\n              <mfChildren>2</mfChildren>\r\n              <mfCribs>0</mfCribs>\r\n              <mfExtraBeds>0</mfExtraBeds>\r\n              <mfsourceCode>WEB</mfsourceCode>\r\n              <mfMarketCode>NTRAN</mfMarketCode>\r\n            </Rate>\r\n          </Rates>\r\n          <mfsourceCode>WEB</mfsourceCode>\r\n        </RatePlan>\r\n      </RatePlans>\r\n      <marketSegmentCode>PROMO</marketSegmentCode>\r\n      <resGuestRPHs>0</resGuestRPHs>\r\n      <GuaranteeInfo guaranteeType=\"NA\">\r\n        <mfGuaranteeType>CC</mfGuaranteeType>\r\n        <GuaranteeDeposit>\r\n          <dueDate>2015-11-18T00:00:00.000</dueDate>\r\n          <Amount currencyCode=\"USD\">\r\n            <valueNum>642.2</valueNum>\r\n          </Amount>\r\n          <cancelIfNotReceived>0</cancelIfNotReceived>\r\n        </GuaranteeDeposit>\r\n      </GuaranteeInfo>\r\n      <CancelPenalties>\r\n        <CancelPenalty mfRuleType=\"DEPOSIT\">\r\n          <cancelByDate>2015-11-18T00:00:00.000</cancelByDate>\r\n          <Amount currencyCode=\"USD\">\r\n            <valueNum>642.2</valueNum>\r\n          </Amount>\r\n          <mfRuleScope>R</mfRuleScope>\r\n          <mfPercentage>50</mfPercentage>\r\n          <mfRuleDescription>50 Percent due</mfRuleDescription>\r\n          <activityDeposit>0</activityDeposit>\r\n          <roomDeposit>642.2</roomDeposit>\r\n          <ruleCode>50PCT</ruleCode>\r\n        </CancelPenalty>\r\n      </CancelPenalties>\r\n      <PaymentInstructions>\r\n        <PaymentInstruction paymentMethodType=\"NA\">\r\n          <mfPaymentMethod>AX</mfPaymentMethod>\r\n          <PaymentDue>\r\n            <Amount currencyCode=\"USD\"/>\r\n            <cancelIfNotReceived>0</cancelIfNotReceived>\r\n          </PaymentDue>\r\n        </PaymentInstruction>\r\n      </PaymentInstructions>\r\n      <mfsourceCode>WEB</mfsourceCode>\r\n      <mfchannelCode>TAD</mfchannelCode>\r\n      <mfconfidentialRate>0</mfconfidentialRate>\r\n      <mfAsbProrated>0</mfAsbProrated>\r\n    </RoomStay>\r\n  </RoomStays>\r\n  <resProfileRPHs>0</resProfileRPHs>\r\n  <mfupdateDate>2015-08-22T16:34:23.000</mfupdateDate>\r\n  <mfcomplementary>0</mfcomplementary>\r\n  <mfImage>\r\n    <numRooms>1</numRooms>\r\n    <Describe>\r\n      <resortName>Property 1 Opera Demo Multi Hotel</resortName>\r\n      <insertUser>OEDS$ADS</insertUser>\r\n      <updateUser>SUPERVISOR</updateUser>\r\n      <roomCategory>King Deluxe Room</roomCategory>\r\n      <rateCode>Tarpon Family Package</rateCode>\r\n      <guarantee>CC: Credit Card Guaranteed</guarantee>\r\n      <company>Micros Hotels and Resorts</company>\r\n    </Describe>\r\n    <Change>\r\n      <bArrivalDate>0</bArrivalDate>\r\n      <bNumNights>0</bNumNights>\r\n      <bNumAdults>0</bNumAdults>\r\n      <bNumChildren>0</bNumChildren>\r\n      <bNumRooms>0</bNumRooms>\r\n      <bCribs>0</bCribs>\r\n      <bRoomCategory>0</bRoomCategory>\r\n      <bPaymentType>0</bPaymentType>\r\n      <bGuarType>0</bGuarType>\r\n      <bDiscountReason>0</bDiscountReason>\r\n      <bMultipleRateYN>1</bMultipleRateYN>\r\n      <bResvStatus>0</bResvStatus>\r\n    </Change>\r\n  </mfImage>\r\n  <Services>  \r\n    <Service reservationActionType=\"SYNC\" servicePricingType=\"NA\" reservationStatusType=\"NA\">\r\n      <serviceRPH>0</serviceRPH>\r\n      <serviceInventoryCode>BF_REGULAR_LSTORNO2</serviceInventoryCode>        \r\n      <TimeSpan timeUnitType=\"DAY\">\r\n        <startTime>2016-05-18T00:00:00.000</startTime>\r\n        <numberOfTimeUnits>7</numberOfTimeUnits>\r\n      </TimeSpan>\r\n      <Price currencyCode=\"USD\">\r\n        <valueNum>100</valueNum>\r\n      </Price>\r\n      <quantity>1</quantity>\r\n      <ServicePrices>\r\n        <ServicePrice>\r\n          <beginDate>2016-05-18</beginDate>\r\n          <endDate>2016-05-19</endDate>\r\n          <unitPrice>25</unitPrice>\r\n        </ServicePrice>\r\n        <ServicePrice>\r\n          <beginDate>2016-05-24</beginDate>\r\n          <endDate>2016-05-24</endDate>\r\n          <unitPrice>25</unitPrice>\r\n        </ServicePrice>\r\n      </ServicePrices>\r\n    </Service>\r\n  </Services>\r\n</Reservation>\r\n"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/ngipublic/rest/oxi/pms", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["ngipublic", "rest", "oxi", "pms"]}}, "response": []}, {"name": "Then NGI calculates rateValue for Reservation having rateCode=CEN14,FAMILT, serviceName=BF_REGULAR_LSTORNO", "event": [{"listen": "test", "script": {"id": "e66884fa-a48b-493e-b526-18cccaf61b4b", "exec": ["//variables", "var jsonData = pm.response.json();", "", "", "//value to compare with", "var reservationJSONToBeCompared ={\"versionId\":20160127003,\"reservationId\":\"673795\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"statisticsCorrelationId\":null,\"bookingDate\":\"2015-04-20T19:06:51.000\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"RESERVED\",\"nationality\":\"GB\",\"channel\":\"TAD\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2016-05-18\",\"departureDate\":\"2016-05-20\",\"marketCode\":\"NTRAN\",\"sourceBookingCode\":\"WEB\",\"numberOfChildren\":2,\"numberOfAdults\":2,\"bookingType\":\"CC\",\"invTypeCode\":\"KDLX\",\"bookedAccomTypeCode\":\"KDLX\",\"rateCode\":\"CEN27\",\"invBlockCode\":null,\"roomNumber\":null,\"rates\":[{\"rateValue\":375,\"startDate\":\"2016-05-18\",\"endDate\":\"2016-05-20\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":375,\"currencyCode\":null}],\"services\":[{\"serviceName\":\"BF_REGULAR_LSTORNO2\",\"serviceValue\":100,\"inclusive\":true,\"roomRevenuePackage\":true,\"reservationPackage\":true,\"startDate\":\"2016-05-18\",\"endDate\":\"2016-05-20\",\"inclusionType\":null}],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"NTRAN\"},{\"arrivalDate\":\"2016-05-20\",\"departureDate\":\"2016-05-24\",\"marketCode\":\"ITRAN\",\"sourceBookingCode\":\"WEB\",\"numberOfChildren\":2,\"numberOfAdults\":2,\"bookingType\":\"CC\",\"invTypeCode\":\"KDLX\",\"bookedAccomTypeCode\":\"KDLX\",\"rateCode\":\"FAMILT\",\"invBlockCode\":null,\"roomNumber\":null,\"rates\":[{\"rateValue\":309,\"startDate\":\"2016-05-20\",\"endDate\":\"2016-05-24\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":309,\"currencyCode\":null}],\"services\":[],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"ITRAN\"},{\"arrivalDate\":\"2016-05-24\",\"departureDate\":\"2016-05-25\",\"marketCode\":\"NTRAN\",\"sourceBookingCode\":\"WEB\",\"numberOfChildren\":2,\"numberOfAdults\":2,\"bookingType\":\"CC\",\"invTypeCode\":\"KDLX\",\"bookedAccomTypeCode\":\"KDLX\",\"rateCode\":\"CEN27\",\"invBlockCode\":null,\"roomNumber\":null,\"rates\":[{\"rateValue\":375,\"startDate\":\"2016-05-24\",\"endDate\":\"2016-05-25\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":375,\"currencyCode\":null}],\"services\":[{\"serviceName\":\"BF_REGULAR_LSTORNO2\",\"serviceValue\":100,\"inclusive\":true,\"roomRevenuePackage\":true,\"reservationPackage\":true,\"startDate\":\"2016-05-24\",\"endDate\":\"2016-05-25\",\"inclusionType\":null}],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"NTRAN\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"earliestArrivalDate\":\"2016-05-18\",\"latestDepartureDate\":\"2016-05-25\",\"primary\":null};", "", "//assertions", "assertJSONData(\"Verifying Client Code in OxiMessage\", jsonData,reservationJSONToBeCompared);", "", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue.versionId).to.eql(expectedValue.versionId);", "    pm.expect(actualValue.clientCode).to.eql(expectedValue.clientCode);", "    pm.expect(actualValue.propertyCode).to.eql(expectedValue.propertyCode);", "    pm.expect(actualValue.status).to.eql(expectedValue.status);", "    pm.expect(actualValue.nationality).to.eql(expectedValue.nationality);", "    pm.expect(actualValue.channel).to.eql(expectedValue.channel);", "     pm.expect(actualValue.reservationId).to.eql(expectedValue.reservationId);", "    pm.expect(actualValue.roomStays.arrivalDate).to.eql(expectedValue.roomStays.arrivalDate);", "    pm.expect(actualValue.roomStays.departureDate).to.eql(expectedValue.roomStays.departureDate);", "    pm.expect(actualValue.roomStays.marketCode).to.eql(expectedValue.roomStays.marketCode);", "    pm.expect(actualValue.roomStays.sourceBookingCode).to.eql(expectedValue.roomStays.sourceBookingCode);", "    pm.expect(actualValue.roomStays.numberOfChildren).to.eql(expectedValue.roomStays.numberOfChildren);", "    pm.expect(actualValue.roomStays.numberOfAdults).to.eql(expectedValue.roomStays.numberOfAdults);", "    pm.expect(actualValue.roomStays.bookingType).to.eql(expectedValue.roomStays.bookingType);", "    pm.expect(actualValue.roomStays.invTypeCode).to.eql(expectedValue.roomStays.invTypeCode);", "    pm.expect(actualValue.roomStays.bookedAccomTypeCode).to.eql(expectedValue.roomStays.bookedAccomTypeCode);", "    pm.expect(actualValue.roomStays.rateCode).to.eql(expectedValue.roomStays.rateCode);", "    pm.expect(actualValue.roomStays[0].rates[0].rateValue).to.eql(expectedValue.roomStays[0].rates[0].rateValue);", "    pm.expect(actualValue.roomStays[0].rates[0].startDate).to.eql(expectedValue.roomStays[0].rates[0].startDate);", "    pm.expect(actualValue.roomStays[0].rates[0].endDate).to.eql(expectedValue.roomStays[0].rates[0].endDate);", "    pm.expect(actualValue.roomStays[0].rates[0].grossRate).to.eql(expectedValue.roomStays[0].rates[0].grossRate);", "    pm.expect(actualValue.roomStays[0].services[0].serviceName).to.eql(expectedValue.roomStays[0].services[0].serviceName);", "    pm.expect(actualValue.roomStays[0].services[0].serviceValue).to.eql(expectedValue.roomStays[0].services[0].serviceValue);", "    pm.expect(actualValue.roomStays[0].services[0].inclusive).to.eql(expectedValue.roomStays[0].services[0].inclusive);", "    pm.expect(actualValue.roomStays[0].services[0].roomRevenuePackage).to.eql(expectedValue.roomStays[0].services[0].roomRevenuePackage);", "    pm.expect(actualValue.roomStays[0].services[0].reservationPackage).to.eql(expectedValue.roomStays[0].services[0].reservationPackage);", "    pm.expect(actualValue.roomStays[0].services[0].endDate).to.eql(expectedValue.roomStays[0].services[0].endDate);", "    pm.expect(actualValue.roomStays[0].services[0].startDate).to.eql(expectedValue.roomStays[0].services[0].startDate);", "   pm.expect(actualValue.roomStays.originalCurrencyCode).to.eql(expectedValue.roomStays.originalCurrencyCode);", "   pm.expect(actualValue.roomStays.analyticalMarketSegmentCode).to.eql(expectedValue.roomStays.analyticalMarketSegmentCode);", "});}"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "d7b923bc-e644-4439-af45-1afdec892d49", "exec": ["/*var millisecondsToWait = 5000;\r", "setTimeout(function() {\r", "    // Whatever you want to do after the wait\r", "}, millisecondsToWait);*/"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusReservations/search/findByClientCodeAndPropertyCodeAndReservationId?clientCode={{client_Code}}&propertyCode={{property_Code}}&reservationId=673795", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusReservations", "search", "findByClientCodeAndPropertyCodeAndReservationId"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "reservationId", "value": "673795"}]}}, "response": []}, {"name": "When the property configuration is changed in nucleusVendorConfigParams collection as taxIncluded=true,assumePackageIncluded=false", "event": [{"listen": "prerequest", "script": {"id": "a6f7ac1c-9e7e-42de-a19c-2f195b0eac72", "exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"id": "4340f2d2-4897-427d-aef3-52b10f5b800f", "exec": [""], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"inboundVendorId\": \"{{inboundVendorId}}\",\r\n    \"outboundVendorId\": null,\r\n    \"customReplyToAddress\": null,\r\n    \"useCustomSoapAction\": null,\r\n    \"name\": null,\r\n    \"inboundCredentials\": null,\r\n    \"outboundCredentials\": null,\r\n    \"integrationType\": \"OXI_PMS\",\r\n    \"chains\": [\r\n        {\r\n            \"chainCode\": \"{{client_Code}}\",\r\n            \"outboundCredentials\": null,\r\n            \"inboundCredentials\": null,\r\n            \"clientEnvironmentName\": \"g3\",\r\n            \"baseCurrencyCode\": \"USD\",\r\n            \"hotels\": [\r\n                {\r\n                    \"hotelCode\": \"{{property_Code}}\",\r\n                    \"g3HotelCode\": null,\r\n                    \"g2HotelCode\": null,\r\n                    \"inboundHotelCode\": null,\r\n                    \"propertyName\": \"{{property_Code}}\",\r\n                    \"outgoingUrl\": \"{{hostName}}:{{ngiPort}}/ngipublic/rest/mockserver/oxi/requestroomtype\",\r\n                    \"oxiInterfaceName\": \"OXI_PMS\",\r\n                    \"taxAdjustmentValue\": 10,\r\n                    \"outboundCredentials\": null,\r\n                    \"inboundCredentials\": null,\r\n                    \"baseCurrencyCode\": \"USD\",\r\n                    \"pastDays\": null,\r\n                    \"futureDays\": null,\r\n                    \"assumeTaxIncluded\": true,\r\n                    \"assumePackageIncluded\": false,\r\n                    \"installMode\": false,\r\n                    \"scheduledDeferredDelivery\": null,\r\n                    \"inCatchup\": null,\r\n                    \"calculateNonPickedUpBlocksUsingSummaryData\": null,\r\n                    \"handlePreviouslyStraightMarketSegmentsInAms\": null,\r\n                    \"unqualifiedRatesDirectPopulationDisabled\": false,\r\n                    \"qualifiedRatesDirectPopulationDisabled\": null,\r\n                    \"roomRevenuePackages\": \"BKFS3,BF_REGULAR_LSTORNO3\",\r\n                    \"installationReservationsThreshold\": null,\r\n                    \"populatePackageDataEnabled\": null,\r\n                    \"defaultRoomType\": null\r\n                }\r\n            ],\r\n            \"unqualifiedRatesDirectPopulationDisabled\": null,\r\n            \"qualifiedRatesDirectPopulationDisabled\": null,\r\n            \"installationReservationsThreshold\": null,\r\n            \"populatePackageDataEnabled\": null\r\n        }\r\n    ],\r\n    \"configurations\": null,\r\n    \"unqualifiedRatesDirectPopulationDisabled\": null,\r\n    \"qualifiedRatesDirectPopulationDisabled\": null,\r\n    \"cancelMultiUnitDecrements\": null,\r\n    \"installationReservationsThreshold\": null,\r\n    \"_links\": {\r\n        \"self\": {\r\n            \"href\": \"{{hostName}}:{{ngiPort}}/nucleusVendorConfigParamses/{{inboundVendorId}}\"\r\n        },\r\n        \"nucleusVendorConfigParams\": {\r\n            \"href\": \"{{hostName}}:{{ngiPort}}/nucleusVendorConfigParamses/{{inboundVendorId}}\"\r\n        }\r\n    }\r\n}"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusVendorConfigParamses/{{inboundVendorId}}", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusVendorConfigParamses", "{{inboundVendorId}}"]}}, "response": []}, {"name": "When Reservation for Rate having pkg BKFS3 and VendorConfig has taxIncluded=true,assumePackageIncluded=false", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\\\"1.0\\\" encoding=\\\"UTF-8\\\"?>\r\n<?Label SandBox-OPER1|RESERVATION|*********|SUCCESS|2018060120191232|166/3898?>\r\n<Reservation\r\n    xmlns=\\\"reservation.fidelio.5.0\\\" mfShareAction=\\\"NA\\\" mfReservationAction=\\\"CHECKOUT\\\">\r\n    <HotelReference>\r\n        <hotelCode>SandBox-OPER1</hotelCode>\r\n    </HotelReference>\r\n    <reservationID>*********</reservationID>\r\n    <reservationOriginatorCode>EUNICE_LAU</reservationOriginatorCode>\r\n    <originalBookingDate>2018-07-31T14:56:56.000</originalBookingDate>\r\n    <StayDateRange timeUnitType=\\\"DAY\\\">\r\n        <startTime>2018-08-26T17:33:35.000</startTime>\r\n        <numberOfTimeUnits>1</numberOfTimeUnits>\r\n    </StayDateRange>\r\n    <GuestCounts>\r\n        <GuestCount>\r\n            <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n            <mfCount>2</mfCount>\r\n        </GuestCount>\r\n        <GuestCount>\r\n            <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n            <mfCount>2</mfCount>\r\n        </GuestCount>\r\n    </GuestCounts>\r\n    <ResGuests>\r\n        <ResGuest reservationActionType=\\\"SYNC\\\">\r\n            <resGuestRPH>0</resGuestRPH>\r\n            <profileRPHs>0, 1</profileRPHs>\r\n            <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n            <InHouseTimeSpan timeUnitType=\\\"DAY\\\">\r\n                <startTime>2018-08-26T17:33:35.000</startTime>\r\n                <numberOfTimeUnits>1</numberOfTimeUnits>\r\n            </InHouseTimeSpan>\r\n            <ArrivalTransport/>\r\n            <DepartureTransport/>\r\n            <arrivalTime>2018-08-26T17:33:35.000</arrivalTime>\r\n            <departureTime>2018-08-27T08:34:00.000</departureTime>\r\n            <reservationID>*********</reservationID>\r\n            <ReservationReferences>\r\n                <ReservationReference type=\\\"GUESTID\\\" referenceNumber=\\\"*********\\\" legNumber=\\\"1\\\"/>\r\n                <ReservationReference type=\\\"TA_RECORD_LOCATOR\\\" referenceNumber=\\\"1449708029\\\"/>\r\n            </ReservationReferences>\r\n            <preRegistered>0</preRegistered>\r\n            <commissionPaidTo>T</commissionPaidTo>\r\n        </ResGuest>\r\n    </ResGuests>\r\n    <ResProfiles>\r\n        <ResProfile>\r\n            <Profile\r\n                xmlns=\\\"profile.fidelio.5.0\\\" profileType=\\\"GUEST\\\" multiProperty=\\\"0\\\" miniProfile=\\\"0\\\" pseudoProfile=\\\"0\\\" replaceAddress=\\\"0\\\">\r\n                <creatorCode>SARAH_RYAN1</creatorCode>\r\n                <createdDate>2018-07-31T14:56:05.000</createdDate>\r\n                <lastUpdaterCode>BRYONY_MCLANAGH</lastUpdaterCode>\r\n                <lastUpdated>2018-08-26T18:33:35.000</lastUpdated>\r\n                <IndividualName>\r\n                    <nameSur/>\r\n                </IndividualName>\r\n                <primaryLanguageID>E</primaryLanguageID>\r\n                <PostalAddresses>\r\n                    <PostalAddress addressType=\\\"HOME\\\">\r\n                        <postalCode>Z894K</postalCode>\r\n                        <countryCode>GB</countryCode>\r\n                        <mfPrimaryYN>Y</mfPrimaryYN>\r\n                        <mfAddressLanguage>E</mfAddressLanguage>\r\n                        <cleansed>0</cleansed>\r\n                    </PostalAddress>\r\n                </PostalAddresses>\r\n                <PhoneNumbers>\r\n                    <PhoneNumber phoneNumberType=\\\"HOME\\\">\r\n                        <mfPrimaryYN>Y</mfPrimaryYN>\r\n                        <confirmation>0</confirmation>\r\n                    </PhoneNumber>\r\n                </PhoneNumbers>\r\n                <mfResort>SandBox-OPER1</mfResort>\r\n                <mfResortProfileID>290184999</mfResortProfileID>\r\n                <mfAllowMail>NO</mfAllowMail>\r\n                <mfAllowEMail>NO</mfAllowEMail>\r\n                <mfGuestPriv>NO</mfGuestPriv>\r\n                <mfAllowPhone>0</mfAllowPhone>\r\n                <mfAllowSMS>0</mfAllowSMS>\r\n                <SalesExtention/>\r\n                <PrivacyOption>\r\n                    <mfAllowMail>N</mfAllowMail>\r\n                    <mfAllowEMail>N</mfAllowEMail>\r\n                    <mfAllowPhone>0</mfAllowPhone>\r\n                    <mfAllowSMS>0</mfAllowSMS>\r\n                    <mfAllowHistory>1</mfAllowHistory>\r\n                    <mfAllowMarketResearch>0</mfAllowMarketResearch>\r\n                    <mfAllowThirdParty>0</mfAllowThirdParty>\r\n                </PrivacyOption>\r\n                <ResortList>SandBox-OPER1</ResortList>\r\n                <MultiResortEntities>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>CREDIT_CARDS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RESORT_ARS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>COMMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>PREFERENCES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>NEGOTIATED_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_NEG_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_COMMISSIONS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>CHANNEL_ACCESS_CODES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>YIELD_ADJUSTMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RELATIONSHIPS</entity>\r\n                    </MultiResortEntity>\r\n                </MultiResortEntities>\r\n            </Profile>\r\n            <resProfileRPH>0</resProfileRPH>\r\n        </ResProfile>\r\n        <ResProfile>\r\n            <Profile\r\n                xmlns=\\\"profile.fidelio.5.0\\\" profileType=\\\"TRAVEL\\\" multiProperty=\\\"0\\\" miniProfile=\\\"0\\\" pseudoProfile=\\\"0\\\" replaceAddress=\\\"0\\\">\r\n                <creatorCode>OXI-OPERA</creatorCode>\r\n                <createdDate>2012-08-10T21:36:40.000</createdDate>\r\n                <lastUpdaterCode>NIKITA_AGGARWAL</lastUpdaterCode>\r\n                <lastUpdated>2018-09-10T17:39:10.000</lastUpdated>\r\n                <genericName>booking.com</genericName>\r\n                <IndividualName>\r\n                    <nameSur/>\r\n                </IndividualName>\r\n                <mfResort>SandBox-OPER1</mfResort>\r\n                <mfResortProfileID>5371710</mfResortProfileID>\r\n                <mfNameCode>96040394</mfNameCode>\r\n                <mfAllowMail>YES</mfAllowMail>\r\n                <mfAllowEMail>NO</mfAllowEMail>\r\n                <mfGuestPriv>NO</mfGuestPriv>\r\n                <mfAllowPhone>0</mfAllowPhone>\r\n                <mfAllowSMS>0</mfAllowSMS>\r\n                <SalesExtention/>\r\n                <PrivacyOption>\r\n                    <mfAllowMail>Y</mfAllowMail>\r\n                    <mfAllowEMail>N</mfAllowEMail>\r\n                    <mfAllowPhone>0</mfAllowPhone>\r\n                    <mfAllowSMS>0</mfAllowSMS>\r\n                    <mfAllowHistory>1</mfAllowHistory>\r\n                    <mfAllowMarketResearch>1</mfAllowMarketResearch>\r\n                    <mfAllowThirdParty>1</mfAllowThirdParty>\r\n                </PrivacyOption>\r\n                <ResortList>SandBox-OPER1</ResortList>\r\n                <MultiResortEntities>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>CREDIT_CARDS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RESORT_ARS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>COMMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>PREFERENCES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>NEGOTIATED_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_NEG_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_COMMISSIONS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>CHANNEL_ACCESS_CODES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>YIELD_ADJUSTMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RELATIONSHIPS</entity>\r\n                    </MultiResortEntity>\r\n                </MultiResortEntities>\r\n            </Profile>\r\n            <resProfileRPH>1</resProfileRPH>\r\n        </ResProfile>\r\n    </ResProfiles>\r\n    <RoomStays>\r\n        <RoomStay mfShareAction=\\\"NA\\\" mfReservationAction=\\\"NA\\\" reservationActionType=\\\"SYNC\\\" reservationStatusType=\\\"CHECKEDOUT\\\">\r\n            <roomInventoryCode>CKC</roomInventoryCode>\r\n            <roomID>311</roomID>\r\n            <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                <startTime>2018-08-26T17:33:35.000</startTime>\r\n                <numberOfTimeUnits>1</numberOfTimeUnits>\r\n            </TimeSpan>\r\n            <GuestCounts>\r\n                <GuestCount>\r\n                    <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n                    <mfCount>2</mfCount>\r\n                </GuestCount>\r\n                <GuestCount>\r\n                    <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n                    <mfCount>2</mfCount>\r\n                </GuestCount>\r\n            </GuestCounts>\r\n            <RatePlans>\r\n                <RatePlan reservationActionType=\\\"SYNC\\\">\r\n                    <ratePlanRPH>0</ratePlanRPH>\r\n                    <ratePlanCode>OTAGENA3</ratePlanCode>\r\n                    <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                        <startTime>2018-08-26T00:00:00.000</startTime>\r\n                        <numberOfTimeUnits>1</numberOfTimeUnits>\r\n                    </TimeSpan>\r\n                    <mfMarketCode>OTA</mfMarketCode>\r\n                    <Rates>\r\n                        <Rate reservationActionType=\\\"SYNC\\\" rateBasisTimeUnitType=\\\"DAY\\\">\r\n                            <rateRPH>0</rateRPH>\r\n                            <Amount currencyCode=\\\"USD\\\">\r\n                                <valueNum>169</valueNum>\r\n                            </Amount>\r\n                            <rateBasisUnits>1</rateBasisUnits>\r\n                            <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                                <startTime>2018-08-26T00:00:00.000</startTime>\r\n                                <numberOfTimeUnits>1</numberOfTimeUnits>\r\n                            </TimeSpan>\r\n                            <mfAdults>2</mfAdults>\r\n                            <mfChildren>2</mfChildren>\r\n                            <mfCribs>0</mfCribs>\r\n                            <mfExtraBeds>0</mfExtraBeds>\r\n                            <mfsourceCode>BC</mfsourceCode>\r\n                            <mfMarketCode>OTA</mfMarketCode>\r\n                        </Rate>\r\n                    </Rates>\r\n                    <mfsourceCode>BC</mfsourceCode>\r\n                </RatePlan>\r\n            </RatePlans>\r\n            <marketSegmentCode>OTA</marketSegmentCode>\r\n            <resGuestRPHs>0</resGuestRPHs>\r\n            <resCommentRPHs>0</resCommentRPHs>\r\n            <GuaranteeInfo guaranteeType=\\\"NA\\\">\r\n                <mfGuaranteeType>CHECKED IN</mfGuaranteeType>\r\n                <GuaranteeDeposit>\r\n                    <Amount currencyCode=\\\"USD\\\"/>\r\n                    <cancelIfNotReceived>0</cancelIfNotReceived>\r\n                </GuaranteeDeposit>\r\n            </GuaranteeInfo>\r\n            <CancelPenalties>\r\n                <CancelPenalty mfRuleType=\\\"CANCEL\\\" manualRule=\\\"0\\\">\r\n                    <cancelByDate>2018-08-26T14:00:00.000</cancelByDate>\r\n                    <Amount currencyCode=\\\"USD\\\">\r\n                        <valueNum>169</valueNum>\r\n                    </Amount>\r\n                    <mfRuleScope>R</mfRuleScope>\r\n                    <mfPercentage>100</mfPercentage>\r\n                    <mfCancelPercentDue>100</mfCancelPercentDue>\r\n                    <mfCancelRoomNights>1</mfCancelRoomNights>\r\n                    <mfRuleDescription>Cancel by 2pm on the Day of Arrival</mfRuleDescription>\r\n                    <ruleCode>2PM</ruleCode>\r\n                </CancelPenalty>\r\n            </CancelPenalties>\r\n            <PaymentInstructions>\r\n                <PaymentInstruction paymentMethodType=\\\"NA\\\">\r\n                    <mfPaymentMethod>CA</mfPaymentMethod>\r\n                    <PaymentDue>\r\n                        <Amount currencyCode=\\\"USD\\\"/>\r\n                        <cancelIfNotReceived>0</cancelIfNotReceived>\r\n                    </PaymentDue>\r\n                </PaymentInstruction>\r\n            </PaymentInstructions>\r\n            <mfcomplementaryCode>FAM</mfcomplementaryCode>\r\n            <mfsourceCode>BC</mfsourceCode>\r\n            <mfchannelCode>OTA</mfchannelCode>\r\n            <mfconfidentialRate>1</mfconfidentialRate>\r\n            <mfAsbProrated>0</mfAsbProrated>\r\n        </RoomStay>\r\n    </RoomStays>\r\n    <resCommentRPHs>0</resCommentRPHs>\r\n    <resProfileRPHs>0, 1</resProfileRPHs>\r\n    <mfupdateDate>2018-08-27T09:34:20.000</mfupdateDate>\r\n    <mfcomplementary>0</mfcomplementary>\r\n    <mfImage>\r\n        <numRooms>1</numRooms>\r\n        <Describe>\r\n            <resortName>Huntingtower Hotel Perth - Boutique by Leonardo</resortName>\r\n            <insertUser>SARAH_RYAN1</insertUser>\r\n            <updateUser>EUNICE_LAU</updateUser>\r\n            <roomCategory>Standard King room with Sofa</roomCategory>\r\n            <rateCode>Flexible Rate Inc Breakfast</rateCode>\r\n            <marketCode>Online Travel Agents</marketCode>\r\n            <guarantee>CHECKED IN: Checked In</guarantee>\r\n            <company>Jurys Inns Group Limited</company>\r\n        </Describe>\r\n        <Change>\r\n            <bArrivalDate>0</bArrivalDate>\r\n            <bNumNights>0</bNumNights>\r\n            <bNumAdults>0</bNumAdults>\r\n            <bNumChildren>0</bNumChildren>\r\n            <bNumRooms>0</bNumRooms>\r\n            <bCribs>0</bCribs>\r\n            <bRoomCategory>0</bRoomCategory>\r\n            <bPaymentType>0</bPaymentType>\r\n            <bGuarType>0</bGuarType>\r\n            <bDiscountReason>0</bDiscountReason>\r\n            <bMultipleRateYN>0</bMultipleRateYN>\r\n            <bResvStatus>0</bResvStatus>\r\n        </Change>\r\n    </mfImage>\r\n    <RateServices>\r\n        <RateService>\r\n            <Service reservationActionType=\\\"SYNC\\\" servicePricingType=\\\"NA\\\" reservationStatusType=\\\"NA\\\">\r\n                <serviceRPH>0</serviceRPH>\r\n                <serviceInventoryCode>BKFS3</serviceInventoryCode>\r\n                <ratePlanCode>OTAGENA3</ratePlanCode>\r\n                <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                    <startTime>2018-08-26T00:00:00.000</startTime>\r\n                    <numberOfTimeUnits>1</numberOfTimeUnits>\r\n                </TimeSpan>\r\n                <Price currencyCode=\\\"USD\\\">\r\n                    <valueNum>12</valueNum>\r\n                </Price>\r\n                <quantity>1</quantity>\r\n                <ServicePrices>\r\n                    <ServicePrice>\r\n                        <beginDate>2018-08-26</beginDate>\r\n                        <endDate>2018-08-26</endDate>\r\n                        <unitPrice>6</unitPrice>\r\n                    </ServicePrice>\r\n                </ServicePrices>\r\n            </Service>\r\n        </RateService>\r\n    </RateServices>\r\n</Reservation>"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/ngipublic/rest/oxi/pms", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["ngipublic", "rest", "oxi", "pms"]}}, "response": []}, {"name": "Then NGI calculates rateValue for Reservation having rateCode=OTAGENA3, serviceName=BKFS3", "event": [{"listen": "test", "script": {"id": "bd7dc8d5-9c8f-4f18-8fa0-9dfb4664da90", "exec": ["//variables", "var jsonData = pm.response.json();", "", "", "//value to compare with", "var reservationJSONToBeCompared ={\"versionId\":*********,\"reservationId\":\"*********\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"correlationId\":\"*********\",\"statisticsCorrelationId\":null,\"bookingDate\":\"2018-07-31T14:56:56.000\",\"earliestArrivalDate\":\"2018-08-26\",\"latestDepartureDate\":\"2018-08-27\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"CHECKED_OUT\",\"nationality\":\"GB\",\"channel\":\"OTA\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2018-08-26\",\"departureDate\":\"2018-08-27\",\"marketCode\":\"OTA\",\"sourceBookingCode\":\"BC\",\"numberOfChildren\":2,\"numberOfAdults\":2,\"bookingType\":\"CHECKED IN\",\"invTypeCode\":\"CKC\",\"bookedAccomTypeCode\":\"FAM\",\"rateCode\":\"OTAGENA3\",\"invBlockCode\":null,\"roomNumber\":\"311\",\"rates\":[{\"rateValue\":164.54545,\"startDate\":\"2018-08-26\",\"endDate\":\"2018-08-27\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":169,\"currencyCode\":null}],\"services\":[{\"serviceName\":\"BKFS3\",\"serviceValue\":12,\"inclusive\":false,\"roomRevenuePackage\":true,\"reservationPackage\":false,\"startDate\":\"2018-08-26\",\"endDate\":\"2018-08-27\",\"inclusionType\":null}],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"OTA\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"byDay\":false,\"profileId\":\"5371710\",\"primary\":null};", "", "//assertions", "assertJSONData(\"Verifying Client Code in OxiMessage\", jsonData,reservationJSONToBeCompared);", "", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "     pm.expect(actualValue.versionId).to.eql(expectedValue.versionId);", "    pm.expect(actualValue.clientCode).to.eql(expectedValue.clientCode);", "    pm.expect(actualValue.propertyCode).to.eql(expectedValue.propertyCode);", "    pm.expect(actualValue.status).to.eql(expectedValue.status);", "    pm.expect(actualValue.nationality).to.eql(expectedValue.nationality);", "    pm.expect(actualValue.channel).to.eql(expectedValue.channel);", "     pm.expect(actualValue.reservationId).to.eql(expectedValue.reservationId);", "    pm.expect(actualValue.roomStays.arrivalDate).to.eql(expectedValue.roomStays.arrivalDate);", "    pm.expect(actualValue.roomStays.departureDate).to.eql(expectedValue.roomStays.departureDate);", "    pm.expect(actualValue.roomStays.marketCode).to.eql(expectedValue.roomStays.marketCode);", "    pm.expect(actualValue.roomStays.sourceBookingCode).to.eql(expectedValue.roomStays.sourceBookingCode);", "    pm.expect(actualValue.roomStays.numberOfChildren).to.eql(expectedValue.roomStays.numberOfChildren);", "    pm.expect(actualValue.roomStays.numberOfAdults).to.eql(expectedValue.roomStays.numberOfAdults);", "    pm.expect(actualValue.roomStays.bookingType).to.eql(expectedValue.roomStays.bookingType);", "    pm.expect(actualValue.roomStays.invTypeCode).to.eql(expectedValue.roomStays.invTypeCode);", "    pm.expect(actualValue.roomStays.bookedAccomTypeCode).to.eql(expectedValue.roomStays.bookedAccomTypeCode);", "    pm.expect(actualValue.roomStays.rateCode).to.eql(expectedValue.roomStays.rateCode);", "    pm.expect(actualValue.roomStays[0].rates[0].rateValue).to.eql(expectedValue.roomStays[0].rates[0].rateValue);", "    pm.expect(actualValue.roomStays[0].rates[0].startDate).to.eql(expectedValue.roomStays[0].rates[0].startDate);", "    pm.expect(actualValue.roomStays[0].rates[0].endDate).to.eql(expectedValue.roomStays[0].rates[0].endDate);", "    pm.expect(actualValue.roomStays[0].rates[0].grossRate).to.eql(expectedValue.roomStays[0].rates[0].grossRate);", "    pm.expect(actualValue.roomStays[0].services[0].serviceName).to.eql(expectedValue.roomStays[0].services[0].serviceName);", "    pm.expect(actualValue.roomStays[0].services[0].serviceValue).to.eql(expectedValue.roomStays[0].services[0].serviceValue);", "    pm.expect(actualValue.roomStays[0].services[0].inclusive).to.eql(expectedValue.roomStays[0].services[0].inclusive);", "    pm.expect(actualValue.roomStays[0].services[0].roomRevenuePackage).to.eql(expectedValue.roomStays[0].services[0].roomRevenuePackage);", "    pm.expect(actualValue.roomStays[0].services[0].reservationPackage).to.eql(expectedValue.roomStays[0].services[0].reservationPackage);", "    pm.expect(actualValue.roomStays[0].services[0].endDate).to.eql(expectedValue.roomStays[0].services[0].endDate);", "    pm.expect(actualValue.roomStays[0].services[0].startDate).to.eql(expectedValue.roomStays[0].services[0].startDate);", "   pm.expect(actualValue.roomStays.originalCurrencyCode).to.eql(expectedValue.roomStays.originalCurrencyCode);", "   pm.expect(actualValue.roomStays.analyticalMarketSegmentCode).to.eql(expectedValue.roomStays.analyticalMarketSegmentCode);", "});}"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "fa5d8519-b28f-4fc3-895f-0556f0a57cd2", "exec": ["/*var millisecondsToWait = 5000;\r", "setTimeout(function() {\r", "    // Whatever you want to do after the wait\r", "}, millisecondsToWait);*/"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusReservations/search/findByClientCodeAndPropertyCodeAndReservationId?clientCode={{client_Code}}&propertyCode={{property_Code}}&reservationId=*********", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusReservations", "search", "findByClientCodeAndPropertyCodeAndReservationId"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "reservationId", "value": "*********"}]}}, "response": []}, {"name": "When Reservation for Rate having multiple rateCodes and VendorConfig has taxIncluded=false,assumePackageIncluded=true", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<?Label SandBox-OPER1|RESERVATION|20160127003|SUCCESS?>\r\n<!-- Reservation_PackagesOneDay_Msg1 - Reservation using Rate Plans with Included Packages in Complex Ways\r\nIn this message: Reservation is made for 2 adults and 2 kids for 7 nights with 2 nights on Rate CEN28 then 4 nights on FAMILT and 1 night again on CEN28.\r\nCEN28 is $375 but includes BKFST posted each night at $100 for all people so the net rate is $275\r\nFAMILT is $309 but includes ARCADE at $20 for first night (net rate $289), FUNBAGS at $40 next 2 nights (net rate $269) and \r\nPHOTOS at $90 for last night with Rate (net rate $219) \r\nPASSES is not defined in Rate so have to assume it is not included and those values ignored -->\r\n<Reservation xmlns=\"reservation.fidelio.5.0\" mfShareAction=\"NA\" mfReservationAction=\"EDIT\">\r\n  <HotelReference>\r\n    <hotelCode>SandBox-OPER1</hotelCode>\r\n  </HotelReference>\r\n  <reservationID>673796</reservationID>\r\n  <reservationOriginatorCode>SUPERVISOR</reservationOriginatorCode>\r\n  <originalBookingDate>2015-04-20T19:06:51.000</originalBookingDate>\r\n  <StayDateRange timeUnitType=\"DAY\">\r\n    <startTime>2016-05-18T00:00:00.000</startTime>\r\n    <numberOfTimeUnits>7</numberOfTimeUnits>\r\n  </StayDateRange>\r\n  <GuestCounts>\r\n    <GuestCount>\r\n      <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n      <mfCount>2</mfCount>\r\n    </GuestCount>\r\n    <GuestCount>\r\n      <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n      <mfCount>2</mfCount>\r\n    </GuestCount>\r\n  </GuestCounts>\r\n  <ResGuests>\r\n    <ResGuest reservationActionType=\"SYNC\">\r\n      <resGuestRPH>0</resGuestRPH>\r\n      <profileRPHs>0</profileRPHs>\r\n      <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n      <InHouseTimeSpan timeUnitType=\"DAY\">\r\n        <startTime>2016-05-18T00:00:00.000</startTime>\r\n        <numberOfTimeUnits>7</numberOfTimeUnits>\r\n      </InHouseTimeSpan>\r\n      <ArrivalTransport>\r\n        <transportID>AA/421</transportID>\r\n        <locationCode>HEA</locationCode>\r\n        <transportTime>2016-05-18T14:30:00.000</transportTime>\r\n      </ArrivalTransport>\r\n      <DepartureTransport transportType=\"Train\">\r\n        <transportID>DE/1421</transportID>\r\n        <locationCode>RSW</locationCode>\r\n        <transportTime>2016-05-25T16:30:00.000</transportTime>\r\n      </DepartureTransport>\r\n      <arrivalTime>2016-05-18T00:00:00.000</arrivalTime>\r\n      <departureTime>2011-05-25T00:00:00.000</departureTime>\r\n      <reservationID>673796</reservationID>\r\n      <ReservationReferences>\r\n        <ReservationReference type=\"GUESTID\" referenceNumber=\"673796\" legNumber=\"1\"/>\r\n        <ReservationReference type=\"PMSID\" referenceNumber=\"673796\" legNumber=\"1\"/>\r\n      </ReservationReferences>\r\n      <preRegistered>0</preRegistered>\r\n      <commissionPaidTo>N</commissionPaidTo>\r\n    </ResGuest>\r\n  </ResGuests>\r\n  <ResProfiles>\r\n    <ResProfile>\r\n      <Profile xmlns=\"profile.fidelio.5.0\" profileType=\"GUEST\" gender=\"U\" multiProperty=\"0\" miniProfile=\"0\" pseudoProfile=\"0\" replaceAddress=\"0\">\r\n        <creatorCode>SUPERVISOR</creatorCode>\r\n        <createdDate>2006-06-06T11:10:05.000</createdDate>\r\n        <lastUpdaterCode>*ORS*</lastUpdaterCode>\r\n        <lastUpdated>2014-08-08T13:33:21.000</lastUpdated>\r\n        <preferredRatePlanCode>MEMBER</preferredRatePlanCode>\r\n        <genericName>Morgenstern</genericName>\r\n        <IndividualName>\r\n          <nameFirst>Eric1</nameFirst>\r\n          <nameSur>Morgenstern</nameSur>\r\n        </IndividualName>\r\n        <corporatePosition>GEFY CO</corporatePosition>\r\n        <dateOfBirth>1970-03-09</dateOfBirth>\r\n        <primaryLanguageID>E</primaryLanguageID>\r\n        <ElectronicAddresses>\r\n          <ElectronicAddress electronicAddressType=\"EMAIL\">\r\n            <eAddress><EMAIL></eAddress>\r\n            <mfPrimaryYN>Y</mfPrimaryYN>\r\n            <confirmation>0</confirmation>\r\n          </ElectronicAddress>\r\n        </ElectronicAddresses>\r\n        <Memberships>\r\n          <Membership>\r\n            <programCode>DL</programCode>\r\n            <accountID>**********</accountID>\r\n            <levelCode>BASE</levelCode>\r\n            <startDate>2013-04-06</startDate>\r\n            <nameOnCard>ERIC1 MORGENSTERN</nameOnCard>\r\n            <displaySequence>4</displaySequence>\r\n            <pointIndicator>0</pointIndicator>\r\n            <enrollmentSource>N</enrollmentSource>\r\n            <enrolledAt>N</enrolledAt>\r\n          </Membership>\r\n          <Membership>\r\n            <programCode>ENCORE</programCode>\r\n            <accountID>4808263</accountID>\r\n            <startDate>2009-09-03</startDate>\r\n            <nameOnCard>ERIC1 MORGENSTERN</nameOnCard>\r\n            <displaySequence>2</displaySequence>\r\n            <pointIndicator>0</pointIndicator>\r\n            <enrollmentSource>N</enrollmentSource>\r\n            <enrolledAt>N</enrolledAt>\r\n          </Membership>\r\n          <Membership>\r\n            <programCode>OCIS</programCode>\r\n            <accountID>********</accountID>\r\n            <levelCode>GOLD</levelCode>\r\n            <expireDate>2016-06-30</expireDate>\r\n            <startDate>2004-07-23</startDate>\r\n            <nameOnCard>ERIC1 MORGENSTERN</nameOnCard>\r\n            <membershipStatus>TEMP</membershipStatus>\r\n            <displaySequence>1</displaySequence>\r\n            <enrollmentCode>WEB</enrollmentCode>\r\n            <pointIndicator>1</pointIndicator>\r\n            <enrollmentSource>ON THE WEB</enrollmentSource>\r\n            <enrolledAt>MICROS WEBSITE</enrolledAt>\r\n          </Membership>\r\n          <Membership>\r\n            <programCode>US</programCode>\r\n            <accountID>4R931D6</accountID>\r\n            <levelCode>PLATINUM</levelCode>\r\n            <startDate>2013-04-06</startDate>\r\n            <nameOnCard>ERIC1 MORGENSTERN</nameOnCard>\r\n            <displaySequence>5</displaySequence>\r\n            <pointIndicator>0</pointIndicator>\r\n            <enrollmentSource>N</enrollmentSource>\r\n            <enrolledAt>N</enrolledAt>\r\n          </Membership>\r\n          <Membership mfInactiveDate=\"2014-05-21T00:00:00.000\">\r\n            <programCode>XYZ</programCode>\r\n            <accountID>**********</accountID>\r\n            <startDate>2013-04-06</startDate>\r\n            <nameOnCard>ERIC1 MORGENSTERN</nameOnCard>\r\n            <displaySequence>3</displaySequence>\r\n            <pointIndicator>0</pointIndicator>\r\n            <enrollmentSource>N</enrollmentSource>\r\n            <enrolledAt>N</enrolledAt>\r\n          </Membership>\r\n        </Memberships>\r\n        <PostalAddresses>\r\n          <PostalAddress addressType=\"BUSINESS\">\r\n            <address1>7031 Columbia Gateway Drive</address1>\r\n            <address2>OGTS - 2nd Floor</address2>\r\n            <city>Columbia</city>\r\n            <stateCode>MD</stateCode>\r\n            <postalCode>21046</postalCode>\r\n            <countryCode>IN</countryCode>\r\n            <mfPrimaryYN>Y</mfPrimaryYN>\r\n            <mfAddressLanguage>E</mfAddressLanguage>\r\n            <cleansed>0</cleansed>\r\n            <barCode>THISISABARCODE1234FORMAT</barCode>\r\n          </PostalAddress>\r\n          <PostalAddress addressType=\"HOME\">\r\n            <countryCode>GB</countryCode>\r\n            <mfPrimaryYN>N</mfPrimaryYN>\r\n            <mfAddressLanguage>E</mfAddressLanguage>\r\n            <cleansed>0</cleansed>\r\n          </PostalAddress>\r\n          <PostalAddress addressType=\"HOME\">\r\n            <address1>2640 Parkway De la Puerta De oro</address1>\r\n            <address2>Habitaci�n 315</address2>\r\n            <city>N�poles</city>\r\n            <stateCode>FL</stateCode>\r\n            <postalCode>34105</postalCode>\r\n            <countryCode>US</countryCode>\r\n            <mfPrimaryYN>N</mfPrimaryYN>\r\n            <mfAddressLanguage>S</mfAddressLanguage>\r\n            <cleansed>0</cleansed>\r\n          </PostalAddress>\r\n          <PostalAddress addressType=\"HOME\">\r\n            <address1>7031 Columbia Gateway Drive</address1>\r\n            <address2>OGTS - 2nd Floor / Cube 5</address2>\r\n            <city>Columbia</city>\r\n            <stateCode>MD</stateCode>\r\n            <postalCode>21046</postalCode>\r\n            <countryCode>US</countryCode>\r\n            <mfPrimaryYN>N</mfPrimaryYN>\r\n            <cleansed>0</cleansed>\r\n          </PostalAddress>\r\n        </PostalAddresses>\r\n        <PhoneNumbers>\r\n          <PhoneNumber phoneNumberType=\"BUSINESS\">\r\n            <phoneNumber>************</phoneNumber>\r\n            <mfPrimaryYN>Y</mfPrimaryYN>\r\n            <confirmation>0</confirmation>\r\n          </PhoneNumber>\r\n          <PhoneNumber phoneNumberType=\"MOBILE\">\r\n            <phoneNumber>**************</phoneNumber>\r\n            <mfPrimaryYN>N</mfPrimaryYN>\r\n            <confirmation>0</confirmation>\r\n          </PhoneNumber>\r\n        </PhoneNumbers>\r\n        <SpecialRequests>\r\n          <SpecialRequest mfSpecialRequestType=\"FEA\">\r\n            <requestCode>NS</requestCode>\r\n          </SpecialRequest>\r\n        </SpecialRequests>\r\n        <Comments>\r\n          <Comment>\r\n            <commentOriginatorCode>*ORS*</commentOriginatorCode>\r\n            <lastUpdated>2014-08-08T13:33:21.000</lastUpdated>\r\n            <commentStr>Backgound NOtes</commentStr>\r\n            <commentType>BKG</commentType>\r\n            <commentTitle>Profile Backgrd Note</commentTitle>\r\n          </Comment>\r\n          <Comment>\r\n            <commentOriginatorCode>*ORS*</commentOriginatorCode>\r\n            <lastUpdated>2014-08-08T13:33:21.000</lastUpdated>\r\n            <commentStr>Global Background Note</commentStr>\r\n            <commentType>BKG</commentType>\r\n            <commentTitle>Background Notes</commentTitle>\r\n          </Comment>\r\n          <Comment>\r\n            <commentOriginatorCode>SUPERVISOR</commentOriginatorCode>\r\n            <lastUpdated>2014-06-25T12:32:20.000</lastUpdated>\r\n            <commentStr>Profile is banned</commentStr>\r\n            <commentType>OWS</commentType>\r\n            <commentTitle>BANNED</commentTitle>\r\n          </Comment>\r\n       </Comments>\r\n        <mfResort>SandBox-OPER1</mfResort>\r\n        <mfResortProfileID>18082</mfResortProfileID>\r\n        <mfVipStatus>2</mfVipStatus>\r\n        <mfARNumber>EJM101</mfARNumber>\r\n        <mfAllowMail>YES</mfAllowMail>\r\n        <mfAllowEMail>NO</mfAllowEMail>\r\n        <mfGuestPriv>NO</mfGuestPriv>\r\n        <mfNegotiatedRates>\r\n          <NegotiatedRate>\r\n            <mfResort>SandBox-OPER1</mfResort>\r\n            <rateCode>MEMBER</rateCode>\r\n            <rateBeginDate>2007-03-05</rateBeginDate>\r\n          </NegotiatedRate>\r\n        </mfNegotiatedRates>\r\n        <mfAllowPhone>1</mfAllowPhone>\r\n        <mfAllowSMS>0</mfAllowSMS>\r\n        <SalesExtention>\r\n          <actionCode>SPA</actionCode>\r\n          <businessSegment>LR</businessSegment>\r\n        </SalesExtention>\r\n        <PrivacyOption>\r\n          <mfAllowMail>Y</mfAllowMail>\r\n          <mfAllowEMail>N</mfAllowEMail>\r\n          <mfAllowPhone>1</mfAllowPhone>\r\n          <mfAllowSMS>0</mfAllowSMS>\r\n          <mfAllowHistory>1</mfAllowHistory>\r\n         <mfAllowMarketResearch>0</mfAllowMarketResearch>\r\n          <mfAllowThirdParty>0</mfAllowThirdParty>\r\n        </PrivacyOption>\r\n        <ResortList>SandBox-OPER1</ResortList>\r\n        <MultiResortEntities>\r\n          <MultiResortEntity included=\"0\">\r\n            <entity>CREDIT_CARDS</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"0\">\r\n            <entity>RESORT_ARS</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"1\">\r\n            <entity>COMMENTS</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"1\">\r\n            <entity>PREFERENCES</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"1\">\r\n            <entity>NEGOTIATED_RATES</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"1\">\r\n            <entity>RESORT_NEG_RATES</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"1\">\r\n            <entity>CHANNEL_ACCESS_CODES</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"1\">\r\n            <entity>YIELD_ADJUSTMENTS</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"0\">\r\n            <entity>RELATIONSHIPS</entity>\r\n          </MultiResortEntity>\r\n        </MultiResortEntities>\r\n        <ResortComments>\r\n          <ResortComment>\r\n            <Comment>\r\n              <commentOriginatorCode>*ORS*</commentOriginatorCode>\r\n              <guestViewable>1</guestViewable>\r\n              <lastUpdated>2014-08-08T13:33:21.000</lastUpdated>\r\n              <commentStr>Backgound NOtes</commentStr>\r\n              <commentType>BKG</commentType>\r\n              <commentTitle>Profile Backgrd Note</commentTitle>\r\n            </Comment>\r\n          </ResortComment>\r\n          <ResortComment>\r\n            <Comment>\r\n              <commentOriginatorCode>*ORS*</commentOriginatorCode>\r\n              <guestViewable>1</guestViewable>\r\n              <lastUpdated>2014-08-08T13:33:21.000</lastUpdated>\r\n              <commentStr>Global Background Note</commentStr>\r\n              <commentType>BKG</commentType>\r\n              <commentTitle>Background Notes</commentTitle>\r\n            </Comment>\r\n          </ResortComment>\r\n          <ResortComment>\r\n            <Comment>\r\n              <commentOriginatorCode>SUPERVISOR</commentOriginatorCode>\r\n              <guestViewable>1</guestViewable>\r\n              <lastUpdated>2014-06-25T12:32:20.000</lastUpdated>\r\n              <commentStr>Profile is banned</commentStr>\r\n              <commentType>OWS</commentType>\r\n              <commentTitle>BANNED</commentTitle>\r\n            </Comment>\r\n          </ResortComment>\r\n        </ResortComments>\r\n        <ResortSpecialRequests>\r\n          <SpecialRequest mfSpecialRequestType=\"FEA\">\r\n            <requestCode>NS</requestCode>\r\n          </SpecialRequest>\r\n        </ResortSpecialRequests>\r\n        <WebAccounts>\r\n          <WebAccount>\r\n            <loginName>EMORGENSTERN</loginName>\r\n            <password>lGo17E/imFU=</password>\r\n            <secQuestion>Hp4riAzXmuw=</secQuestion>\r\n            <secAnswer>yjraoU8Yov0=</secAnswer>\r\n            <lastLoginDate>2010-10-20T09:21:29.000</lastLoginDate>\r\n            <insertSource>OWS</insertSource>\r\n            <passwordChangeDate>2012-05-01T16:29:26.000</passwordChangeDate>\r\n            <pwdSystemGenerated>0</pwdSystemGenerated>\r\n          </WebAccount>\r\n        </WebAccounts>\r\n        <ResortNegotiatedRates>\r\n          <NegotiatedRate>\r\n            <mfResort>SandBox-OPER1</mfResort>\r\n            <rateCode>MEMBER</rateCode>\r\n            <rateBeginDate>2007-03-05</rateBeginDate>\r\n          </NegotiatedRate>\r\n        </ResortNegotiatedRates>\r\n        <nationality>E</nationality>\r\n      </Profile>\r\n      <resProfileRPH>0</resProfileRPH>\r\n    </ResProfile>\r\n  </ResProfiles>\r\n  <RoomStays>\r\n    <RoomStay mfShareAction=\"NA\" mfReservationAction=\"NA\" reservationActionType=\"SYNC\" reservationStatusType=\"RESERVED\">\r\n      <roomInventoryCode>KDLX</roomInventoryCode>\r\n      <TimeSpan timeUnitType=\"DAY\">\r\n        <startTime>2016-05-18T00:00:00.000</startTime>\r\n        <numberOfTimeUnits>7</numberOfTimeUnits>\r\n      </TimeSpan>\r\n      <GuestCounts>\r\n        <GuestCount>\r\n          <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n          <mfCount>2</mfCount>\r\n        </GuestCount>\r\n        <GuestCount>\r\n          <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n          <mfCount>2</mfCount>\r\n        </GuestCount>\r\n      </GuestCounts>\r\n      <RatePlans>\r\n        <RatePlan reservationActionType=\"SYNC\">\r\n          <ratePlanRPH>0</ratePlanRPH>\r\n          <ratePlanCode>CEN28</ratePlanCode>\r\n          <TimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-05-18T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>2</numberOfTimeUnits>\r\n          </TimeSpan>\r\n          <mfMarketCode>NTRAN</mfMarketCode>\r\n          <Rates>\r\n            <Rate reservationActionType=\"SYNC\" rateBasisTimeUnitType=\"DAY\">\r\n              <rateRPH>0</rateRPH>\r\n              <Amount currencyCode=\"USD\">\r\n                <valueNum>375</valueNum>\r\n              </Amount>\r\n              <rateBasisUnits>1</rateBasisUnits>\r\n              <TimeSpan timeUnitType=\"DAY\">\r\n                <startTime>2016-05-18T00:00:00.000</startTime>\r\n                <numberOfTimeUnits>2</numberOfTimeUnits>\r\n              </TimeSpan>\r\n              <mfAdults>2</mfAdults>\r\n              <mfChildren>2</mfChildren>\r\n              <mfCribs>0</mfCribs>\r\n              <mfExtraBeds>0</mfExtraBeds>\r\n              <mfsourceCode>WEB</mfsourceCode>\r\n              <mfMarketCode>NTRAN</mfMarketCode>\r\n            </Rate>\r\n          </Rates>\r\n          <mfsourceCode>WEB</mfsourceCode>\r\n        </RatePlan>\r\n        <RatePlan reservationActionType=\"SYNC\">\r\n          <ratePlanRPH>1</ratePlanRPH>\r\n          <ratePlanCode>FAMILT</ratePlanCode>\r\n          <TimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-05-20T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>4</numberOfTimeUnits>\r\n          </TimeSpan>\r\n          <mfMarketCode>ITRAN</mfMarketCode>\r\n          <Rates>\r\n            <Rate reservationActionType=\"SYNC\" rateBasisTimeUnitType=\"DAY\">\r\n              <rateRPH>1</rateRPH>\r\n              <Amount currencyCode=\"USD\">\r\n                <valueNum>309</valueNum>\r\n              </Amount>\r\n              <rateBasisUnits>1</rateBasisUnits>\r\n              <TimeSpan timeUnitType=\"DAY\">\r\n                <startTime>2016-05-20T00:00:00.000</startTime>\r\n                <numberOfTimeUnits>4</numberOfTimeUnits>\r\n              </TimeSpan>\r\n              <mfAdults>2</mfAdults>\r\n              <mfChildren>2</mfChildren>\r\n              <mfCribs>0</mfCribs>\r\n              <mfExtraBeds>0</mfExtraBeds>\r\n              <mfsourceCode>WEB</mfsourceCode>\r\n              <mfMarketCode>ITRAN</mfMarketCode>\r\n            </Rate>\r\n          </Rates>\r\n          <mfsourceCode>WEB</mfsourceCode>\r\n        </RatePlan>\r\n        <RatePlan reservationActionType=\"SYNC\">\r\n          <ratePlanRPH>2</ratePlanRPH>\r\n          <ratePlanCode>CEN28</ratePlanCode>\r\n          <TimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-05-24T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>1</numberOfTimeUnits>\r\n          </TimeSpan>\r\n          <mfMarketCode>NTRAN</mfMarketCode>\r\n          <Rates>\r\n            <Rate reservationActionType=\"SYNC\" rateBasisTimeUnitType=\"DAY\">\r\n              <rateRPH>2</rateRPH>\r\n              <Amount currencyCode=\"USD\">\r\n                <valueNum>375</valueNum>\r\n              </Amount>\r\n              <rateBasisUnits>1</rateBasisUnits>\r\n              <TimeSpan timeUnitType=\"DAY\">\r\n                <startTime>2016-05-24T00:00:00.000</startTime>\r\n                <numberOfTimeUnits>1</numberOfTimeUnits>\r\n              </TimeSpan>\r\n              <mfAdults>2</mfAdults>\r\n              <mfChildren>2</mfChildren>\r\n              <mfCribs>0</mfCribs>\r\n              <mfExtraBeds>0</mfExtraBeds>\r\n              <mfsourceCode>WEB</mfsourceCode>\r\n              <mfMarketCode>NTRAN</mfMarketCode>\r\n            </Rate>\r\n          </Rates>\r\n          <mfsourceCode>WEB</mfsourceCode>\r\n        </RatePlan>\r\n      </RatePlans>\r\n      <marketSegmentCode>PROMO</marketSegmentCode>\r\n      <resGuestRPHs>0</resGuestRPHs>\r\n      <GuaranteeInfo guaranteeType=\"NA\">\r\n        <mfGuaranteeType>CC</mfGuaranteeType>\r\n        <GuaranteeDeposit>\r\n          <dueDate>2015-11-18T00:00:00.000</dueDate>\r\n          <Amount currencyCode=\"USD\">\r\n            <valueNum>642.2</valueNum>\r\n          </Amount>\r\n          <cancelIfNotReceived>0</cancelIfNotReceived>\r\n        </GuaranteeDeposit>\r\n      </GuaranteeInfo>\r\n      <CancelPenalties>\r\n        <CancelPenalty mfRuleType=\"DEPOSIT\">\r\n          <cancelByDate>2015-11-18T00:00:00.000</cancelByDate>\r\n          <Amount currencyCode=\"USD\">\r\n            <valueNum>642.2</valueNum>\r\n          </Amount>\r\n          <mfRuleScope>R</mfRuleScope>\r\n          <mfPercentage>50</mfPercentage>\r\n          <mfRuleDescription>50 Percent due</mfRuleDescription>\r\n          <activityDeposit>0</activityDeposit>\r\n          <roomDeposit>642.2</roomDeposit>\r\n          <ruleCode>50PCT</ruleCode>\r\n        </CancelPenalty>\r\n      </CancelPenalties>\r\n      <PaymentInstructions>\r\n        <PaymentInstruction paymentMethodType=\"NA\">\r\n          <mfPaymentMethod>AX</mfPaymentMethod>\r\n          <PaymentDue>\r\n            <Amount currencyCode=\"USD\"/>\r\n            <cancelIfNotReceived>0</cancelIfNotReceived>\r\n          </PaymentDue>\r\n        </PaymentInstruction>\r\n      </PaymentInstructions>\r\n      <mfsourceCode>WEB</mfsourceCode>\r\n      <mfchannelCode>TAD</mfchannelCode>\r\n      <mfconfidentialRate>0</mfconfidentialRate>\r\n      <mfAsbProrated>0</mfAsbProrated>\r\n    </RoomStay>\r\n  </RoomStays>\r\n  <resProfileRPHs>0</resProfileRPHs>\r\n  <mfupdateDate>2015-08-22T16:34:23.000</mfupdateDate>\r\n  <mfcomplementary>0</mfcomplementary>\r\n  <mfImage>\r\n    <numRooms>1</numRooms>\r\n    <Describe>\r\n      <resortName>Property 1 Opera Demo Multi Hotel</resortName>\r\n      <insertUser>OEDS$ADS</insertUser>\r\n      <updateUser>SUPERVISOR</updateUser>\r\n      <roomCategory>King Deluxe Room</roomCategory>\r\n      <rateCode>Tarpon Family Package</rateCode>\r\n      <guarantee>CC: Credit Card Guaranteed</guarantee>\r\n      <company>Micros Hotels and Resorts</company>\r\n    </Describe>\r\n    <Change>\r\n      <bArrivalDate>0</bArrivalDate>\r\n      <bNumNights>0</bNumNights>\r\n      <bNumAdults>0</bNumAdults>\r\n      <bNumChildren>0</bNumChildren>\r\n      <bNumRooms>0</bNumRooms>\r\n      <bCribs>0</bCribs>\r\n      <bRoomCategory>0</bRoomCategory>\r\n      <bPaymentType>0</bPaymentType>\r\n      <bGuarType>0</bGuarType>\r\n      <bDiscountReason>0</bDiscountReason>\r\n      <bMultipleRateYN>1</bMultipleRateYN>\r\n      <bResvStatus>0</bResvStatus>\r\n    </Change>\r\n  </mfImage>\r\n  <Services>  \r\n    <Service reservationActionType=\"SYNC\" servicePricingType=\"NA\" reservationStatusType=\"NA\">\r\n      <serviceRPH>0</serviceRPH>\r\n      <serviceInventoryCode>BF_REGULAR_LSTORNO3</serviceInventoryCode>        \r\n      <TimeSpan timeUnitType=\"DAY\">\r\n        <startTime>2016-05-18T00:00:00.000</startTime>\r\n        <numberOfTimeUnits>7</numberOfTimeUnits>\r\n      </TimeSpan>\r\n      <Price currencyCode=\"USD\">\r\n        <valueNum>100</valueNum>\r\n      </Price>\r\n      <quantity>1</quantity>\r\n      <ServicePrices>\r\n        <ServicePrice>\r\n          <beginDate>2016-05-18</beginDate>\r\n          <endDate>2016-05-19</endDate>\r\n          <unitPrice>25</unitPrice>\r\n        </ServicePrice>\r\n        <ServicePrice>\r\n          <beginDate>2016-05-24</beginDate>\r\n          <endDate>2016-05-24</endDate>\r\n          <unitPrice>25</unitPrice>\r\n        </ServicePrice>\r\n      </ServicePrices>\r\n    </Service>\r\n  </Services>\r\n</Reservation>\r\n"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/ngipublic/rest/oxi/pms", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["ngipublic", "rest", "oxi", "pms"]}}, "response": []}, {"name": "Then NGI calculates rateValue for Reservation having rateCode=CEN18,FAMILT, serviceName=BF_REGULAR_LSTORNO3", "event": [{"listen": "test", "script": {"id": "26f86b79-891d-4a9b-874b-ea6c44bd5331", "exec": ["//variables", "var jsonData = pm.response.json();", "", "//value to compare with", "// RoomStaysData for arrivalDate 2016-05-18", "var reservationJSONToBeCompared = {\"createDate\":\"2020-03-27 18:24:02.280\",\"lastModifiedDate\":\"2020-03-27 18:24:02.280\",\"versionId\":20160127003,\"reservationId\":\"673796\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"correlationId\":\"20160127003\",\"statisticsCorrelationId\":null,\"bookingDate\":\"2015-04-20T19:06:51.000\",\"earliestArrivalDate\":\"2016-05-18\",\"latestDepartureDate\":\"2016-05-25\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"RESERVED\",\"nationality\":\"GB\",\"channel\":\"TAD\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2016-05-18\",\"departureDate\":\"2016-05-20\",\"marketCode\":\"NTRAN\",\"sourceBookingCode\":\"WEB\",\"numberOfChildren\":2,\"numberOfAdults\":2,\"bookingType\":\"CC\",\"invTypeCode\":\"KDLX\",\"bookedAccomTypeCode\":\"KDLX\",\"rateCode\":\"CEN28\",\"invBlockCode\":null,\"roomNumber\":null,\"rates\":[{\"rateValue\":340.90909,\"startDate\":\"2016-05-18\",\"endDate\":\"2016-05-20\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":375,\"currencyCode\":null}],\"services\":[{\"serviceName\":\"BF_REGULAR_LSTORNO3\",\"serviceValue\":100,\"inclusive\":false,\"roomRevenuePackage\":true,\"reservationPackage\":true,\"startDate\":\"2016-05-18\",\"endDate\":\"2016-05-20\",\"inclusionType\":null}],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"NTRAN\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"byDay\":false,\"profileId\":null,\"primary\":null};", "// RoomStaysData for arrivalDate 2016-05-20", "var reservationJSONToBeCompared1 = {\"createDate\":\"2020-03-27 18:24:02.280\",\"lastModifiedDate\":\"2020-03-27 18:24:02.280\",\"versionId\":20160127003,\"reservationId\":\"673796\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"correlationId\":\"20160127003\",\"statisticsCorrelationId\":null,\"bookingDate\":\"2015-04-20T19:06:51.000\",\"earliestArrivalDate\":\"2016-05-18\",\"latestDepartureDate\":\"2016-05-25\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"RESERVED\",\"nationality\":\"GB\",\"channel\":\"TAD\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2016-05-20\",\"departureDate\":\"2016-05-24\",\"marketCode\":\"ITRAN\",\"sourceBookingCode\":\"WEB\",\"numberOfChildren\":2,\"numberOfAdults\":2,\"bookingType\":\"CC\",\"invTypeCode\":\"KDLX\",\"bookedAccomTypeCode\":\"KDLX\",\"rateCode\":\"FAMILT\",\"invBlockCode\":null,\"roomNumber\":null,\"rates\":[{\"rateValue\":280.90909,\"startDate\":\"2016-05-20\",\"endDate\":\"2016-05-24\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":309,\"currencyCode\":null}],\"services\":[],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"ITRAN\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"byDay\":false,\"profileId\":null,\"primary\":null};", "// RoomStaysData for arrivalDate 2016-05-24", "var reservationJSONToBeCompared2 = {\"createDate\":\"2020-03-27 18:24:02.280\",\"lastModifiedDate\":\"2020-03-27 18:24:02.280\",\"versionId\":20160127003,\"reservationId\":\"673796\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"correlationId\":\"20160127003\",\"statisticsCorrelationId\":null,\"bookingDate\":\"2015-04-20T19:06:51.000\",\"earliestArrivalDate\":\"2016-05-18\",\"latestDepartureDate\":\"2016-05-25\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"RESERVED\",\"nationality\":\"GB\",\"channel\":\"TAD\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2016-05-24\",\"departureDate\":\"2016-05-25\",\"marketCode\":\"NTRAN\",\"sourceBookingCode\":\"WEB\",\"numberOfChildren\":2,\"numberOfAdults\":2,\"bookingType\":\"CC\",\"invTypeCode\":\"KDLX\",\"bookedAccomTypeCode\":\"KDLX\",\"rateCode\":\"CEN28\",\"invBlockCode\":null,\"roomNumber\":null,\"rates\":[{\"rateValue\":340.90909,\"startDate\":\"2016-05-24\",\"endDate\":\"2016-05-25\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":375,\"currencyCode\":null}],\"services\":[{\"serviceName\":\"BF_REGULAR_LSTORNO3\",\"serviceValue\":100,\"inclusive\":false,\"roomRevenuePackage\":true,\"reservationPackage\":true,\"startDate\":\"2016-05-24\",\"endDate\":\"2016-05-25\",\"inclusionType\":null}],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"NTRAN\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"byDay\":false,\"profileId\":null,\"primary\":null};", "", "arr=[];", "", "arr.push(reservationJSONToBeCompared,reservationJSONToBeCompared1,reservationJSONToBeCompared2);", "var i=0;", "//assertions", "for(item of arr)", "{", "    console.log(i);", "    assertJSONData(\"Verifying Client Code in OxiMessage\", jsonData,item);", "    assertRoomStaysData(\"Verifying Room stays json in OxiMessage\", jsonData.roomStays[i],item.roomStays[0]);", "    i++;", "}", "", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "     pm.expect(actualValue.versionId).to.eql(expectedValue.versionId);", "    pm.expect(actualValue.clientCode).to.eql(expectedValue.clientCode);", "    pm.expect(actualValue.propertyCode).to.eql(expectedValue.propertyCode);", "    pm.expect(actualValue.status).to.eql(expectedValue.status);", "    pm.expect(actualValue.nationality).to.eql(expectedValue.nationality);", "    pm.expect(actualValue.channel).to.eql(expectedValue.channel);", "     pm.expect(actualValue.reservationId).to.eql(expectedValue.reservationId);", "    pm.expect(actualValue.roomStays.arrivalDate).to.eql(expectedValue.roomStays.arrivalDate);", "    pm.expect(actualValue.roomStays.departureDate).to.eql(expectedValue.roomStays.departureDate);", "    pm.expect(actualValue.roomStays.marketCode).to.eql(expectedValue.roomStays.marketCode);", "    pm.expect(actualValue.roomStays.sourceBookingCode).to.eql(expectedValue.roomStays.sourceBookingCode);", "    pm.expect(actualValue.roomStays.numberOfChildren).to.eql(expectedValue.roomStays.numberOfChildren);", "    pm.expect(actualValue.roomStays.numberOfAdults).to.eql(expectedValue.roomStays.numberOfAdults);", "    pm.expect(actualValue.roomStays.bookingType).to.eql(expectedValue.roomStays.bookingType);", "    pm.expect(actualValue.roomStays.invTypeCode).to.eql(expectedValue.roomStays.invTypeCode);", "    pm.expect(actualValue.roomStays.bookedAccomTypeCode).to.eql(expectedValue.roomStays.bookedAccomTypeCode);", "    pm.expect(actualValue.roomStays.rateCode).to.eql(expectedValue.roomStays.rateCode);", "   pm.expect(actualValue.roomStays.originalCurrencyCode).to.eql(expectedValue.roomStays.originalCurrencyCode);", "   pm.expect(actualValue.roomStays.analyticalMarketSegmentCode).to.eql(expectedValue.roomStays.analyticalMarketSegmentCode);", "});}", "", "function assertRoomStaysData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue.rates.rateValue).to.eql(expectedValue.rates.rateValue);", "    pm.expect(actualValue.rates.startDate).to.eql(expectedValue.rates.startDate);", "    pm.expect(actualValue.rates.endDate).to.eql(expectedValue.rates.endDate);", "    pm.expect(actualValue.rates.grossRate).to.eql(expectedValue.rates.grossRate);", "    pm.expect(actualValue.services.serviceName).to.eql(expectedValue.rates.serviceName);", "    pm.expect(actualValue.services.serviceValue).to.eql(expectedValue.rates.serviceValue);", "    pm.expect(actualValue.services.inclusive).to.eql(expectedValue.rates.inclusive);", "    pm.expect(actualValue.services.startDate).to.eql(expectedValue.rates.startDate);", "    pm.expect(actualValue.services.endDate).to.eql(expectedValue.rates.endDate);", "    pm.expect(actualValue.services.roomRevenuePackage).to.eql(expectedValue.rates.roomRevenuePackage);", "});}"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "b1192518-9b81-4c8e-9e40-29b8234f3caf", "exec": ["/*var millisecondsToWait = 5000;\r", "setTimeout(function() {\r", "    // Whatever you want to do after the wait\r", "}, millisecondsToWait);*/"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusReservations/search/findByClientCodeAndPropertyCodeAndReservationId?clientCode={{client_Code}}&propertyCode={{property_Code}}&reservationId=673796", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusReservations", "search", "findByClientCodeAndPropertyCodeAndReservationId"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "reservationId", "value": "673796"}]}}, "response": []}, {"name": "When the property configuration is changed in nucleusVendorConfigParams collection as taxIncluded=false,assumePackageIncluded=false", "event": [{"listen": "prerequest", "script": {"id": "951fec04-9d8e-448e-b00d-c770a8513243", "exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"id": "d751228c-aa2d-49c4-8dc2-33fd19eac382", "exec": [""], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"inboundVendorId\": \"{{inboundVendorId}}\",\r\n    \"outboundVendorId\": null,\r\n    \"customReplyToAddress\": null,\r\n    \"useCustomSoapAction\": null,\r\n    \"name\": null,\r\n    \"inboundCredentials\": null,\r\n    \"outboundCredentials\": null,\r\n    \"integrationType\": \"OXI_PMS\",\r\n    \"chains\": [\r\n        {\r\n            \"chainCode\": \"{{client_Code}}\",\r\n            \"outboundCredentials\": null,\r\n            \"inboundCredentials\": null,\r\n            \"clientEnvironmentName\": \"g3\",\r\n            \"baseCurrencyCode\": \"USD\",\r\n            \"hotels\": [\r\n                {\r\n                    \"hotelCode\": \"{{property_Code}}\",\r\n                    \"g3HotelCode\": null,\r\n                    \"g2HotelCode\": null,\r\n                    \"inboundHotelCode\": null,\r\n                    \"propertyName\": \"{{property_Code}}\",\r\n                    \"outgoingUrl\": \"{{hostName}}:{{ngiPort}}/ngipublic/rest/mockserver/oxi/requestroomtype\",\r\n                    \"oxiInterfaceName\": \"OXI_PMS\",\r\n                    \"taxAdjustmentValue\": 10,\r\n                    \"outboundCredentials\": null,\r\n                    \"inboundCredentials\": null,\r\n                    \"baseCurrencyCode\": \"USD\",\r\n                    \"pastDays\": null,\r\n                    \"futureDays\": null,\r\n                    \"assumeTaxIncluded\": false,\r\n                    \"assumePackageIncluded\": false,\r\n                    \"installMode\": false,\r\n                    \"scheduledDeferredDelivery\": null,\r\n                    \"inCatchup\": null,\r\n                    \"calculateNonPickedUpBlocksUsingSummaryData\": null,\r\n                    \"handlePreviouslyStraightMarketSegmentsInAms\": null,\r\n                    \"unqualifiedRatesDirectPopulationDisabled\": false,\r\n                    \"qualifiedRatesDirectPopulationDisabled\": null,\r\n                    \"roomRevenuePackages\": \"BKFS5,BF_REGULAR_LSTORNO4\",\r\n                    \"installationReservationsThreshold\": null,\r\n                    \"populatePackageDataEnabled\": null,\r\n                    \"defaultRoomType\": null\r\n                }\r\n            ],\r\n            \"unqualifiedRatesDirectPopulationDisabled\": null,\r\n            \"qualifiedRatesDirectPopulationDisabled\": null,\r\n            \"installationReservationsThreshold\": null,\r\n            \"populatePackageDataEnabled\": null\r\n        }\r\n    ],\r\n    \"configurations\": null,\r\n    \"unqualifiedRatesDirectPopulationDisabled\": null,\r\n    \"qualifiedRatesDirectPopulationDisabled\": null,\r\n    \"cancelMultiUnitDecrements\": null,\r\n    \"installationReservationsThreshold\": null,\r\n    \"_links\": {\r\n        \"self\": {\r\n            \"href\": \"{{hostName}}:{{ngiPort}}/nucleusVendorConfigParamses/{{inboundVendorId}}\"\r\n        },\r\n        \"nucleusVendorConfigParams\": {\r\n            \"href\": \"{{hostName}}:{{ngiPort}}/nucleusVendorConfigParamses/{{inboundVendorId}}\"\r\n        }\r\n    }\r\n}"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusVendorConfigParamses/{{inboundVendorId}}", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusVendorConfigParamses", "{{inboundVendorId}}"]}}, "response": []}, {"name": "When Reservation for Rate having pkg BKFS5 and VendorConfig has taxIncluded=false, assumePackageIncluded=false", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\\\"1.0\\\" encoding=\\\"UTF-8\\\"?>\r\n<?Label SandBox-OPER1|RESERVATION|*********|SUCCESS|2018060120191232|166/3898?>\r\n<Reservation\r\n    xmlns=\\\"reservation.fidelio.5.0\\\" mfShareAction=\\\"NA\\\" mfReservationAction=\\\"CHECKOUT\\\">\r\n    <HotelReference>\r\n        <hotelCode>SandBox-OPER1</hotelCode>\r\n    </HotelReference>\r\n    <reservationID>*********</reservationID>\r\n    <reservationOriginatorCode>EUNICE_LAU</reservationOriginatorCode>\r\n    <originalBookingDate>2018-07-31T14:56:56.000</originalBookingDate>\r\n    <StayDateRange timeUnitType=\\\"DAY\\\">\r\n        <startTime>2018-08-26T17:33:35.000</startTime>\r\n        <numberOfTimeUnits>1</numberOfTimeUnits>\r\n    </StayDateRange>\r\n    <GuestCounts>\r\n        <GuestCount>\r\n            <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n            <mfCount>2</mfCount>\r\n        </GuestCount>\r\n        <GuestCount>\r\n            <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n            <mfCount>2</mfCount>\r\n        </GuestCount>\r\n    </GuestCounts>\r\n    <ResGuests>\r\n        <ResGuest reservationActionType=\\\"SYNC\\\">\r\n            <resGuestRPH>0</resGuestRPH>\r\n            <profileRPHs>0, 1</profileRPHs>\r\n            <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n            <InHouseTimeSpan timeUnitType=\\\"DAY\\\">\r\n                <startTime>2018-08-26T17:33:35.000</startTime>\r\n                <numberOfTimeUnits>1</numberOfTimeUnits>\r\n            </InHouseTimeSpan>\r\n            <ArrivalTransport/>\r\n            <DepartureTransport/>\r\n            <arrivalTime>2018-08-26T17:33:35.000</arrivalTime>\r\n            <departureTime>2018-08-27T08:34:00.000</departureTime>\r\n            <reservationID>*********</reservationID>\r\n            <ReservationReferences>\r\n                <ReservationReference type=\\\"GUESTID\\\" referenceNumber=\\\"*********\\\" legNumber=\\\"1\\\"/>\r\n                <ReservationReference type=\\\"TA_RECORD_LOCATOR\\\" referenceNumber=\\\"1449708029\\\"/>\r\n            </ReservationReferences>\r\n            <preRegistered>0</preRegistered>\r\n            <commissionPaidTo>T</commissionPaidTo>\r\n        </ResGuest>\r\n    </ResGuests>\r\n    <ResProfiles>\r\n        <ResProfile>\r\n            <Profile\r\n                xmlns=\\\"profile.fidelio.5.0\\\" profileType=\\\"GUEST\\\" multiProperty=\\\"0\\\" miniProfile=\\\"0\\\" pseudoProfile=\\\"0\\\" replaceAddress=\\\"0\\\">\r\n                <creatorCode>SARAH_RYAN1</creatorCode>\r\n                <createdDate>2018-07-31T14:56:05.000</createdDate>\r\n                <lastUpdaterCode>BRYONY_MCLANAGH</lastUpdaterCode>\r\n                <lastUpdated>2018-08-26T18:33:35.000</lastUpdated>\r\n                <IndividualName>\r\n                    <nameSur/>\r\n                </IndividualName>\r\n                <primaryLanguageID>E</primaryLanguageID>\r\n                <PostalAddresses>\r\n                    <PostalAddress addressType=\\\"HOME\\\">\r\n                        <postalCode>Z894K</postalCode>\r\n                        <countryCode>GB</countryCode>\r\n                        <mfPrimaryYN>Y</mfPrimaryYN>\r\n                        <mfAddressLanguage>E</mfAddressLanguage>\r\n                        <cleansed>0</cleansed>\r\n                    </PostalAddress>\r\n                </PostalAddresses>\r\n                <PhoneNumbers>\r\n                    <PhoneNumber phoneNumberType=\\\"HOME\\\">\r\n                        <mfPrimaryYN>Y</mfPrimaryYN>\r\n                        <confirmation>0</confirmation>\r\n                    </PhoneNumber>\r\n                </PhoneNumbers>\r\n                <mfResort>SandBox-OPER1</mfResort>\r\n                <mfResortProfileID>290184999</mfResortProfileID>\r\n                <mfAllowMail>NO</mfAllowMail>\r\n                <mfAllowEMail>NO</mfAllowEMail>\r\n                <mfGuestPriv>NO</mfGuestPriv>\r\n                <mfAllowPhone>0</mfAllowPhone>\r\n                <mfAllowSMS>0</mfAllowSMS>\r\n                <SalesExtention/>\r\n                <PrivacyOption>\r\n                    <mfAllowMail>N</mfAllowMail>\r\n                    <mfAllowEMail>N</mfAllowEMail>\r\n                    <mfAllowPhone>0</mfAllowPhone>\r\n                    <mfAllowSMS>0</mfAllowSMS>\r\n                    <mfAllowHistory>1</mfAllowHistory>\r\n                    <mfAllowMarketResearch>0</mfAllowMarketResearch>\r\n                    <mfAllowThirdParty>0</mfAllowThirdParty>\r\n                </PrivacyOption>\r\n                <ResortList>SandBox-OPER1</ResortList>\r\n                <MultiResortEntities>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>CREDIT_CARDS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RESORT_ARS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>COMMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>PREFERENCES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>NEGOTIATED_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_NEG_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_COMMISSIONS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>CHANNEL_ACCESS_CODES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>YIELD_ADJUSTMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RELATIONSHIPS</entity>\r\n                    </MultiResortEntity>\r\n                </MultiResortEntities>\r\n            </Profile>\r\n            <resProfileRPH>0</resProfileRPH>\r\n        </ResProfile>\r\n        <ResProfile>\r\n            <Profile\r\n                xmlns=\\\"profile.fidelio.5.0\\\" profileType=\\\"TRAVEL\\\" multiProperty=\\\"0\\\" miniProfile=\\\"0\\\" pseudoProfile=\\\"0\\\" replaceAddress=\\\"0\\\">\r\n                <creatorCode>OXI-OPERA</creatorCode>\r\n                <createdDate>2012-08-10T21:36:40.000</createdDate>\r\n                <lastUpdaterCode>NIKITA_AGGARWAL</lastUpdaterCode>\r\n                <lastUpdated>2018-09-10T17:39:10.000</lastUpdated>\r\n                <genericName>booking.com</genericName>\r\n                <IndividualName>\r\n                    <nameSur/>\r\n                </IndividualName>\r\n                <mfResort>SandBox-OPER1</mfResort>\r\n                <mfResortProfileID>5371710</mfResortProfileID>\r\n                <mfNameCode>96040394</mfNameCode>\r\n                <mfAllowMail>YES</mfAllowMail>\r\n                <mfAllowEMail>NO</mfAllowEMail>\r\n                <mfGuestPriv>NO</mfGuestPriv>\r\n                <mfAllowPhone>0</mfAllowPhone>\r\n                <mfAllowSMS>0</mfAllowSMS>\r\n                <SalesExtention/>\r\n                <PrivacyOption>\r\n                    <mfAllowMail>Y</mfAllowMail>\r\n                    <mfAllowEMail>N</mfAllowEMail>\r\n                    <mfAllowPhone>0</mfAllowPhone>\r\n                    <mfAllowSMS>0</mfAllowSMS>\r\n                    <mfAllowHistory>1</mfAllowHistory>\r\n                    <mfAllowMarketResearch>1</mfAllowMarketResearch>\r\n                    <mfAllowThirdParty>1</mfAllowThirdParty>\r\n                </PrivacyOption>\r\n                <ResortList>SandBox-OPER1</ResortList>\r\n                <MultiResortEntities>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>CREDIT_CARDS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RESORT_ARS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>COMMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>PREFERENCES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>NEGOTIATED_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_NEG_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_COMMISSIONS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>CHANNEL_ACCESS_CODES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>YIELD_ADJUSTMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RELATIONSHIPS</entity>\r\n                    </MultiResortEntity>\r\n                </MultiResortEntities>\r\n            </Profile>\r\n            <resProfileRPH>1</resProfileRPH>\r\n        </ResProfile>\r\n    </ResProfiles>\r\n    <RoomStays>\r\n        <RoomStay mfShareAction=\\\"NA\\\" mfReservationAction=\\\"NA\\\" reservationActionType=\\\"SYNC\\\" reservationStatusType=\\\"CHECKEDOUT\\\">\r\n            <roomInventoryCode>CKC</roomInventoryCode>\r\n            <roomID>311</roomID>\r\n            <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                <startTime>2018-08-26T17:33:35.000</startTime>\r\n                <numberOfTimeUnits>1</numberOfTimeUnits>\r\n            </TimeSpan>\r\n            <GuestCounts>\r\n                <GuestCount>\r\n                    <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n                    <mfCount>2</mfCount>\r\n                </GuestCount>\r\n                <GuestCount>\r\n                    <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n                    <mfCount>2</mfCount>\r\n                </GuestCount>\r\n            </GuestCounts>\r\n            <RatePlans>\r\n                <RatePlan reservationActionType=\\\"SYNC\\\">\r\n                    <ratePlanRPH>0</ratePlanRPH>\r\n                    <ratePlanCode>OTAGENA5</ratePlanCode>\r\n                    <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                        <startTime>2018-08-26T00:00:00.000</startTime>\r\n                        <numberOfTimeUnits>1</numberOfTimeUnits>\r\n                    </TimeSpan>\r\n                    <mfMarketCode>OTA</mfMarketCode>\r\n                    <Rates>\r\n                        <Rate reservationActionType=\\\"SYNC\\\" rateBasisTimeUnitType=\\\"DAY\\\">\r\n                            <rateRPH>0</rateRPH>\r\n                            <Amount currencyCode=\\\"USD\\\">\r\n                                <valueNum>169</valueNum>\r\n                            </Amount>\r\n                            <rateBasisUnits>1</rateBasisUnits>\r\n                            <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                                <startTime>2018-08-26T00:00:00.000</startTime>\r\n                                <numberOfTimeUnits>1</numberOfTimeUnits>\r\n                            </TimeSpan>\r\n                            <mfAdults>2</mfAdults>\r\n                            <mfChildren>2</mfChildren>\r\n                            <mfCribs>0</mfCribs>\r\n                            <mfExtraBeds>0</mfExtraBeds>\r\n                            <mfsourceCode>BC</mfsourceCode>\r\n                            <mfMarketCode>OTA</mfMarketCode>\r\n                        </Rate>\r\n                    </Rates>\r\n                    <mfsourceCode>BC</mfsourceCode>\r\n                </RatePlan>\r\n            </RatePlans>\r\n            <marketSegmentCode>OTA</marketSegmentCode>\r\n            <resGuestRPHs>0</resGuestRPHs>\r\n            <resCommentRPHs>0</resCommentRPHs>\r\n            <GuaranteeInfo guaranteeType=\\\"NA\\\">\r\n                <mfGuaranteeType>CHECKED IN</mfGuaranteeType>\r\n                <GuaranteeDeposit>\r\n                    <Amount currencyCode=\\\"USD\\\"/>\r\n                    <cancelIfNotReceived>0</cancelIfNotReceived>\r\n                </GuaranteeDeposit>\r\n            </GuaranteeInfo>\r\n            <CancelPenalties>\r\n                <CancelPenalty mfRuleType=\\\"CANCEL\\\" manualRule=\\\"0\\\">\r\n                    <cancelByDate>2018-08-26T14:00:00.000</cancelByDate>\r\n                    <Amount currencyCode=\\\"USD\\\">\r\n                        <valueNum>169</valueNum>\r\n                    </Amount>\r\n                    <mfRuleScope>R</mfRuleScope>\r\n                    <mfPercentage>100</mfPercentage>\r\n                    <mfCancelPercentDue>100</mfCancelPercentDue>\r\n                    <mfCancelRoomNights>1</mfCancelRoomNights>\r\n                    <mfRuleDescription>Cancel by 2pm on the Day of Arrival</mfRuleDescription>\r\n                    <ruleCode>2PM</ruleCode>\r\n                </CancelPenalty>\r\n            </CancelPenalties>\r\n            <PaymentInstructions>\r\n                <PaymentInstruction paymentMethodType=\\\"NA\\\">\r\n                    <mfPaymentMethod>CA</mfPaymentMethod>\r\n                    <PaymentDue>\r\n                        <Amount currencyCode=\\\"USD\\\"/>\r\n                        <cancelIfNotReceived>0</cancelIfNotReceived>\r\n                    </PaymentDue>\r\n                </PaymentInstruction>\r\n            </PaymentInstructions>\r\n            <mfcomplementaryCode>FAM</mfcomplementaryCode>\r\n            <mfsourceCode>BC</mfsourceCode>\r\n            <mfchannelCode>OTA</mfchannelCode>\r\n            <mfconfidentialRate>1</mfconfidentialRate>\r\n            <mfAsbProrated>0</mfAsbProrated>\r\n        </RoomStay>\r\n    </RoomStays>\r\n    <resCommentRPHs>0</resCommentRPHs>\r\n    <resProfileRPHs>0, 1</resProfileRPHs>\r\n    <mfupdateDate>2018-08-27T09:34:20.000</mfupdateDate>\r\n    <mfcomplementary>0</mfcomplementary>\r\n    <mfImage>\r\n        <numRooms>1</numRooms>\r\n        <Describe>\r\n            <resortName>Huntingtower Hotel Perth - Boutique by Leonardo</resortName>\r\n            <insertUser>SARAH_RYAN1</insertUser>\r\n            <updateUser>EUNICE_LAU</updateUser>\r\n            <roomCategory>Standard King room with Sofa</roomCategory>\r\n            <rateCode>Flexible Rate Inc Breakfast</rateCode>\r\n            <marketCode>Online Travel Agents</marketCode>\r\n            <guarantee>CHECKED IN: Checked In</guarantee>\r\n            <company>Jurys Inns Group Limited</company>\r\n        </Describe>\r\n        <Change>\r\n            <bArrivalDate>0</bArrivalDate>\r\n            <bNumNights>0</bNumNights>\r\n            <bNumAdults>0</bNumAdults>\r\n            <bNumChildren>0</bNumChildren>\r\n            <bNumRooms>0</bNumRooms>\r\n            <bCribs>0</bCribs>\r\n            <bRoomCategory>0</bRoomCategory>\r\n            <bPaymentType>0</bPaymentType>\r\n            <bGuarType>0</bGuarType>\r\n            <bDiscountReason>0</bDiscountReason>\r\n            <bMultipleRateYN>0</bMultipleRateYN>\r\n            <bResvStatus>0</bResvStatus>\r\n        </Change>\r\n    </mfImage>\r\n    <RateServices>\r\n        <RateService>\r\n            <Service reservationActionType=\\\"SYNC\\\" servicePricingType=\\\"NA\\\" reservationStatusType=\\\"NA\\\">\r\n                <serviceRPH>0</serviceRPH>\r\n                <serviceInventoryCode>BKFS5</serviceInventoryCode>\r\n                <ratePlanCode>OTAGENA5</ratePlanCode>\r\n                <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                    <startTime>2018-08-26T00:00:00.000</startTime>\r\n                    <numberOfTimeUnits>1</numberOfTimeUnits>\r\n                </TimeSpan>\r\n                <Price currencyCode=\\\"USD\\\">\r\n                    <valueNum>12</valueNum>\r\n                </Price>\r\n                <quantity>1</quantity>\r\n                <ServicePrices>\r\n                    <ServicePrice>\r\n                        <beginDate>2018-08-26</beginDate>\r\n                        <endDate>2018-08-26</endDate>\r\n                        <unitPrice>6</unitPrice>\r\n                    </ServicePrice>\r\n                </ServicePrices>\r\n            </Service>\r\n        </RateService>\r\n    </RateServices>\r\n</Reservation>"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/ngipublic/rest/oxi/pms", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["ngipublic", "rest", "oxi", "pms"]}}, "response": []}, {"name": "Then NGI calculates rateValue for Reservation having rateCode=OTAGENA5, serviceName=BKFS5", "event": [{"listen": "test", "script": {"id": "e21427ad-da97-42ce-880b-f28510bc7287", "exec": ["//variables", "var jsonData = pm.response.json();", "", "//value to compare with", "var reservationJSONToBeCompared ={\"versionId\":*********,\"reservationId\":\"*********\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"statisticsCorrelationId\":null,\"bookingDate\":\"2018-07-31T14:56:56.000\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"CHECKED_OUT\",\"nationality\":\"GB\",\"channel\":\"OTA\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2018-08-26\",\"departureDate\":\"2018-08-27\",\"marketCode\":\"OTA\",\"sourceBookingCode\":\"BC\",\"numberOfChildren\":2,\"numberOfAdults\":2,\"bookingType\":\"CHECKED IN\",\"invTypeCode\":\"CKC\",\"bookedAccomTypeCode\":\"FAM\",\"rateCode\":\"OTAGENA5\",\"invBlockCode\":null,\"roomNumber\":\"311\",\"rates\":[{\"rateValue\":181,\"startDate\":\"2018-08-26\",\"endDate\":\"2018-08-27\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":169,\"currencyCode\":null}],\"services\":[{\"serviceName\":\"BKFS5\",\"serviceValue\":12,\"inclusive\":false,\"roomRevenuePackage\":true,\"reservationPackage\":false,\"startDate\":\"2018-08-26\",\"endDate\":\"2018-08-27\",\"inclusionType\":null}],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"OTA\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"earliestArrivalDate\":\"2018-08-26\",\"latestDepartureDate\":\"2018-08-27\",\"primary\":null};", "", "//assertions", "assertJSONData(\"Verifying Client Code in OxiMessage\", jsonData,reservationJSONToBeCompared);", "", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "     pm.expect(actualValue.versionId).to.eql(expectedValue.versionId);", "    pm.expect(actualValue.clientCode).to.eql(expectedValue.clientCode);", "    pm.expect(actualValue.propertyCode).to.eql(expectedValue.propertyCode);", "    pm.expect(actualValue.status).to.eql(expectedValue.status);", "    pm.expect(actualValue.nationality).to.eql(expectedValue.nationality);", "    pm.expect(actualValue.channel).to.eql(expectedValue.channel);", "     pm.expect(actualValue.reservationId).to.eql(expectedValue.reservationId);", "    pm.expect(actualValue.roomStays.arrivalDate).to.eql(expectedValue.roomStays.arrivalDate);", "    pm.expect(actualValue.roomStays.departureDate).to.eql(expectedValue.roomStays.departureDate);", "    pm.expect(actualValue.roomStays.marketCode).to.eql(expectedValue.roomStays.marketCode);", "    pm.expect(actualValue.roomStays.sourceBookingCode).to.eql(expectedValue.roomStays.sourceBookingCode);", "    pm.expect(actualValue.roomStays.numberOfChildren).to.eql(expectedValue.roomStays.numberOfChildren);", "    pm.expect(actualValue.roomStays.numberOfAdults).to.eql(expectedValue.roomStays.numberOfAdults);", "    pm.expect(actualValue.roomStays.bookingType).to.eql(expectedValue.roomStays.bookingType);", "    pm.expect(actualValue.roomStays.invTypeCode).to.eql(expectedValue.roomStays.invTypeCode);", "    pm.expect(actualValue.roomStays.bookedAccomTypeCode).to.eql(expectedValue.roomStays.bookedAccomTypeCode);", "    pm.expect(actualValue.roomStays.rateCode).to.eql(expectedValue.roomStays.rateCode);", "    pm.expect(actualValue.roomStays[0].rates[0].rateValue).to.eql(expectedValue.roomStays[0].rates[0].rateValue);", "    pm.expect(actualValue.roomStays[0].rates[0].startDate).to.eql(expectedValue.roomStays[0].rates[0].startDate);", "    pm.expect(actualValue.roomStays[0].rates[0].endDate).to.eql(expectedValue.roomStays[0].rates[0].endDate);", "    pm.expect(actualValue.roomStays[0].rates[0].grossRate).to.eql(expectedValue.roomStays[0].rates[0].grossRate);", "    pm.expect(actualValue.roomStays[0].services[0].serviceName).to.eql(expectedValue.roomStays[0].services[0].serviceName);", "    pm.expect(actualValue.roomStays[0].services[0].serviceValue).to.eql(expectedValue.roomStays[0].services[0].serviceValue);", "    pm.expect(actualValue.roomStays[0].services[0].inclusive).to.eql(expectedValue.roomStays[0].services[0].inclusive);", "    pm.expect(actualValue.roomStays[0].services[0].roomRevenuePackage).to.eql(expectedValue.roomStays[0].services[0].roomRevenuePackage);", "    pm.expect(actualValue.roomStays[0].services[0].reservationPackage).to.eql(expectedValue.roomStays[0].services[0].reservationPackage);", "    pm.expect(actualValue.roomStays[0].services[0].endDate).to.eql(expectedValue.roomStays[0].services[0].endDate);", "    pm.expect(actualValue.roomStays[0].services[0].startDate).to.eql(expectedValue.roomStays[0].services[0].startDate);", "   pm.expect(actualValue.roomStays.originalCurrencyCode).to.eql(expectedValue.roomStays.originalCurrencyCode);", "   pm.expect(actualValue.roomStays.analyticalMarketSegmentCode).to.eql(expectedValue.roomStays.analyticalMarketSegmentCode);", "});}"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "deb00675-ee4c-4762-9b72-ea02caaed320", "exec": ["/*var millisecondsToWait = 5000;\r", "setTimeout(function() {\r", "    // Whatever you want to do after the wait\r", "}, millisecondsToWait);*/"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusReservations/search/findByClientCodeAndPropertyCodeAndReservationId?clientCode={{client_Code}}&propertyCode={{property_Code}}&reservationId=*********", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusReservations", "search", "findByClientCodeAndPropertyCodeAndReservationId"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "reservationId", "value": "*********"}]}}, "response": []}, {"name": "When Reservation for Rate having multiple rateCodes and VendorConfig has taxIncluded=false, assumePackageIncluded=false", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<?Label SandBox-OPER1|RESERVATION|20160127003|SUCCESS?>\r\n<!-- Reservation_PackagesOneDay_Msg1 - Reservation using Rate Plans with Included Packages in Complex Ways\r\nIn this message: Reservation is made for 2 adults and 2 kids for 7 nights with 2 nights on Rate CEN29 then 4 nights on FAMILT and 1 night again on CEN29.\r\nCEN29 is $375 but includes BKFST posted each night at $100 for all people so the net rate is $275\r\nFAMILT is $309 but includes ARCADE at $20 for first night (net rate $289), FUNBAGS at $40 next 2 nights (net rate $269) and \r\nPHOTOS at $90 for last night with Rate (net rate $219) \r\nPASSES is not defined in Rate so have to assume it is not included and those values ignored -->\r\n<Reservation xmlns=\"reservation.fidelio.5.0\" mfShareAction=\"NA\" mfReservationAction=\"EDIT\">\r\n  <HotelReference>\r\n    <hotelCode>SandBox-OPER1</hotelCode>\r\n  </HotelReference>\r\n  <reservationID>673797</reservationID>\r\n  <reservationOriginatorCode>SUPERVISOR</reservationOriginatorCode>\r\n  <originalBookingDate>2015-04-20T19:06:51.000</originalBookingDate>\r\n  <StayDateRange timeUnitType=\"DAY\">\r\n    <startTime>2016-05-18T00:00:00.000</startTime>\r\n    <numberOfTimeUnits>7</numberOfTimeUnits>\r\n  </StayDateRange>\r\n  <GuestCounts>\r\n    <GuestCount>\r\n      <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n      <mfCount>2</mfCount>\r\n    </GuestCount>\r\n    <GuestCount>\r\n      <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n      <mfCount>2</mfCount>\r\n    </GuestCount>\r\n  </GuestCounts>\r\n  <ResGuests>\r\n    <ResGuest reservationActionType=\"SYNC\">\r\n      <resGuestRPH>0</resGuestRPH>\r\n      <profileRPHs>0</profileRPHs>\r\n      <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n      <InHouseTimeSpan timeUnitType=\"DAY\">\r\n        <startTime>2016-05-18T00:00:00.000</startTime>\r\n        <numberOfTimeUnits>7</numberOfTimeUnits>\r\n      </InHouseTimeSpan>\r\n      <ArrivalTransport>\r\n        <transportID>AA/421</transportID>\r\n        <locationCode>HEA</locationCode>\r\n        <transportTime>2016-05-18T14:30:00.000</transportTime>\r\n      </ArrivalTransport>\r\n      <DepartureTransport transportType=\"Train\">\r\n        <transportID>DE/1421</transportID>\r\n        <locationCode>RSW</locationCode>\r\n        <transportTime>2016-05-25T16:30:00.000</transportTime>\r\n      </DepartureTransport>\r\n      <arrivalTime>2016-05-18T00:00:00.000</arrivalTime>\r\n      <departureTime>2011-05-25T00:00:00.000</departureTime>\r\n      <reservationID>673797</reservationID>\r\n      <ReservationReferences>\r\n        <ReservationReference type=\"GUESTID\" referenceNumber=\"673797\" legNumber=\"1\"/>\r\n        <ReservationReference type=\"PMSID\" referenceNumber=\"673797\" legNumber=\"1\"/>\r\n      </ReservationReferences>\r\n      <preRegistered>0</preRegistered>\r\n      <commissionPaidTo>N</commissionPaidTo>\r\n    </ResGuest>\r\n  </ResGuests>\r\n  <ResProfiles>\r\n    <ResProfile>\r\n      <Profile xmlns=\"profile.fidelio.5.0\" profileType=\"GUEST\" gender=\"U\" multiProperty=\"0\" miniProfile=\"0\" pseudoProfile=\"0\" replaceAddress=\"0\">\r\n        <creatorCode>SUPERVISOR</creatorCode>\r\n        <createdDate>2006-06-06T11:10:05.000</createdDate>\r\n        <lastUpdaterCode>*ORS*</lastUpdaterCode>\r\n        <lastUpdated>2014-08-08T13:33:21.000</lastUpdated>\r\n        <preferredRatePlanCode>MEMBER</preferredRatePlanCode>\r\n        <genericName>Morgenstern</genericName>\r\n        <IndividualName>\r\n          <nameFirst>Eric1</nameFirst>\r\n          <nameSur>Morgenstern</nameSur>\r\n        </IndividualName>\r\n        <corporatePosition>GEFY CO</corporatePosition>\r\n        <dateOfBirth>1970-03-09</dateOfBirth>\r\n        <primaryLanguageID>E</primaryLanguageID>\r\n        <ElectronicAddresses>\r\n          <ElectronicAddress electronicAddressType=\"EMAIL\">\r\n            <eAddress><EMAIL></eAddress>\r\n            <mfPrimaryYN>Y</mfPrimaryYN>\r\n            <confirmation>0</confirmation>\r\n          </ElectronicAddress>\r\n        </ElectronicAddresses>\r\n        <Memberships>\r\n          <Membership>\r\n            <programCode>DL</programCode>\r\n            <accountID>**********</accountID>\r\n            <levelCode>BASE</levelCode>\r\n            <startDate>2013-04-06</startDate>\r\n            <nameOnCard>ERIC1 MORGENSTERN</nameOnCard>\r\n            <displaySequence>4</displaySequence>\r\n            <pointIndicator>0</pointIndicator>\r\n            <enrollmentSource>N</enrollmentSource>\r\n            <enrolledAt>N</enrolledAt>\r\n          </Membership>\r\n          <Membership>\r\n            <programCode>ENCORE</programCode>\r\n            <accountID>4808263</accountID>\r\n            <startDate>2009-09-03</startDate>\r\n            <nameOnCard>ERIC1 MORGENSTERN</nameOnCard>\r\n            <displaySequence>2</displaySequence>\r\n            <pointIndicator>0</pointIndicator>\r\n            <enrollmentSource>N</enrollmentSource>\r\n            <enrolledAt>N</enrolledAt>\r\n          </Membership>\r\n          <Membership>\r\n            <programCode>OCIS</programCode>\r\n            <accountID>********</accountID>\r\n            <levelCode>GOLD</levelCode>\r\n            <expireDate>2016-06-30</expireDate>\r\n            <startDate>2004-07-23</startDate>\r\n            <nameOnCard>ERIC1 MORGENSTERN</nameOnCard>\r\n            <membershipStatus>TEMP</membershipStatus>\r\n            <displaySequence>1</displaySequence>\r\n            <enrollmentCode>WEB</enrollmentCode>\r\n            <pointIndicator>1</pointIndicator>\r\n            <enrollmentSource>ON THE WEB</enrollmentSource>\r\n            <enrolledAt>MICROS WEBSITE</enrolledAt>\r\n          </Membership>\r\n          <Membership>\r\n            <programCode>US</programCode>\r\n            <accountID>4R931D6</accountID>\r\n            <levelCode>PLATINUM</levelCode>\r\n            <startDate>2013-04-06</startDate>\r\n            <nameOnCard>ERIC1 MORGENSTERN</nameOnCard>\r\n            <displaySequence>5</displaySequence>\r\n            <pointIndicator>0</pointIndicator>\r\n            <enrollmentSource>N</enrollmentSource>\r\n            <enrolledAt>N</enrolledAt>\r\n          </Membership>\r\n          <Membership mfInactiveDate=\"2014-05-21T00:00:00.000\">\r\n            <programCode>XYZ</programCode>\r\n            <accountID>**********</accountID>\r\n            <startDate>2013-04-06</startDate>\r\n            <nameOnCard>ERIC1 MORGENSTERN</nameOnCard>\r\n            <displaySequence>3</displaySequence>\r\n            <pointIndicator>0</pointIndicator>\r\n            <enrollmentSource>N</enrollmentSource>\r\n            <enrolledAt>N</enrolledAt>\r\n          </Membership>\r\n        </Memberships>\r\n        <PostalAddresses>\r\n          <PostalAddress addressType=\"BUSINESS\">\r\n            <address1>7031 Columbia Gateway Drive</address1>\r\n            <address2>OGTS - 2nd Floor</address2>\r\n            <city>Columbia</city>\r\n            <stateCode>MD</stateCode>\r\n            <postalCode>21046</postalCode>\r\n            <countryCode>IN</countryCode>\r\n            <mfPrimaryYN>Y</mfPrimaryYN>\r\n            <mfAddressLanguage>E</mfAddressLanguage>\r\n            <cleansed>0</cleansed>\r\n            <barCode>THISISABARCODE1234FORMAT</barCode>\r\n          </PostalAddress>\r\n          <PostalAddress addressType=\"HOME\">\r\n            <countryCode>GB</countryCode>\r\n            <mfPrimaryYN>N</mfPrimaryYN>\r\n            <mfAddressLanguage>E</mfAddressLanguage>\r\n            <cleansed>0</cleansed>\r\n          </PostalAddress>\r\n          <PostalAddress addressType=\"HOME\">\r\n            <address1>2640 Parkway De la Puerta De oro</address1>\r\n            <address2>Habitaci�n 315</address2>\r\n            <city>N�poles</city>\r\n            <stateCode>FL</stateCode>\r\n            <postalCode>34105</postalCode>\r\n            <countryCode>US</countryCode>\r\n            <mfPrimaryYN>N</mfPrimaryYN>\r\n            <mfAddressLanguage>S</mfAddressLanguage>\r\n            <cleansed>0</cleansed>\r\n          </PostalAddress>\r\n          <PostalAddress addressType=\"HOME\">\r\n            <address1>7031 Columbia Gateway Drive</address1>\r\n            <address2>OGTS - 2nd Floor / Cube 5</address2>\r\n            <city>Columbia</city>\r\n            <stateCode>MD</stateCode>\r\n            <postalCode>21046</postalCode>\r\n            <countryCode>US</countryCode>\r\n            <mfPrimaryYN>N</mfPrimaryYN>\r\n            <cleansed>0</cleansed>\r\n          </PostalAddress>\r\n        </PostalAddresses>\r\n        <PhoneNumbers>\r\n          <PhoneNumber phoneNumberType=\"BUSINESS\">\r\n            <phoneNumber>************</phoneNumber>\r\n            <mfPrimaryYN>Y</mfPrimaryYN>\r\n            <confirmation>0</confirmation>\r\n          </PhoneNumber>\r\n          <PhoneNumber phoneNumberType=\"MOBILE\">\r\n            <phoneNumber>**************</phoneNumber>\r\n            <mfPrimaryYN>N</mfPrimaryYN>\r\n            <confirmation>0</confirmation>\r\n          </PhoneNumber>\r\n        </PhoneNumbers>\r\n        <SpecialRequests>\r\n          <SpecialRequest mfSpecialRequestType=\"FEA\">\r\n            <requestCode>NS</requestCode>\r\n          </SpecialRequest>\r\n        </SpecialRequests>\r\n        <Comments>\r\n          <Comment>\r\n            <commentOriginatorCode>*ORS*</commentOriginatorCode>\r\n            <lastUpdated>2014-08-08T13:33:21.000</lastUpdated>\r\n            <commentStr>Backgound NOtes</commentStr>\r\n            <commentType>BKG</commentType>\r\n            <commentTitle>Profile Backgrd Note</commentTitle>\r\n          </Comment>\r\n          <Comment>\r\n            <commentOriginatorCode>*ORS*</commentOriginatorCode>\r\n            <lastUpdated>2014-08-08T13:33:21.000</lastUpdated>\r\n            <commentStr>Global Background Note</commentStr>\r\n            <commentType>BKG</commentType>\r\n            <commentTitle>Background Notes</commentTitle>\r\n          </Comment>\r\n          <Comment>\r\n            <commentOriginatorCode>SUPERVISOR</commentOriginatorCode>\r\n            <lastUpdated>2014-06-25T12:32:20.000</lastUpdated>\r\n            <commentStr>Profile is banned</commentStr>\r\n            <commentType>OWS</commentType>\r\n            <commentTitle>BANNED</commentTitle>\r\n          </Comment>\r\n       </Comments>\r\n        <mfResort>SandBox-OPER1</mfResort>\r\n        <mfResortProfileID>18082</mfResortProfileID>\r\n        <mfVipStatus>2</mfVipStatus>\r\n        <mfARNumber>EJM101</mfARNumber>\r\n        <mfAllowMail>YES</mfAllowMail>\r\n        <mfAllowEMail>NO</mfAllowEMail>\r\n        <mfGuestPriv>NO</mfGuestPriv>\r\n        <mfNegotiatedRates>\r\n          <NegotiatedRate>\r\n            <mfResort>SandBox-OPER1</mfResort>\r\n            <rateCode>MEMBER</rateCode>\r\n            <rateBeginDate>2007-03-05</rateBeginDate>\r\n          </NegotiatedRate>\r\n        </mfNegotiatedRates>\r\n        <mfAllowPhone>1</mfAllowPhone>\r\n        <mfAllowSMS>0</mfAllowSMS>\r\n        <SalesExtention>\r\n          <actionCode>SPA</actionCode>\r\n          <businessSegment>LR</businessSegment>\r\n        </SalesExtention>\r\n        <PrivacyOption>\r\n          <mfAllowMail>Y</mfAllowMail>\r\n          <mfAllowEMail>N</mfAllowEMail>\r\n          <mfAllowPhone>1</mfAllowPhone>\r\n          <mfAllowSMS>0</mfAllowSMS>\r\n          <mfAllowHistory>1</mfAllowHistory>\r\n         <mfAllowMarketResearch>0</mfAllowMarketResearch>\r\n          <mfAllowThirdParty>0</mfAllowThirdParty>\r\n        </PrivacyOption>\r\n        <ResortList>SandBox-OPER1</ResortList>\r\n        <MultiResortEntities>\r\n          <MultiResortEntity included=\"0\">\r\n            <entity>CREDIT_CARDS</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"0\">\r\n            <entity>RESORT_ARS</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"1\">\r\n            <entity>COMMENTS</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"1\">\r\n            <entity>PREFERENCES</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"1\">\r\n            <entity>NEGOTIATED_RATES</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"1\">\r\n            <entity>RESORT_NEG_RATES</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"1\">\r\n            <entity>CHANNEL_ACCESS_CODES</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"1\">\r\n            <entity>YIELD_ADJUSTMENTS</entity>\r\n          </MultiResortEntity>\r\n          <MultiResortEntity included=\"0\">\r\n            <entity>RELATIONSHIPS</entity>\r\n          </MultiResortEntity>\r\n        </MultiResortEntities>\r\n        <ResortComments>\r\n          <ResortComment>\r\n            <Comment>\r\n              <commentOriginatorCode>*ORS*</commentOriginatorCode>\r\n              <guestViewable>1</guestViewable>\r\n              <lastUpdated>2014-08-08T13:33:21.000</lastUpdated>\r\n              <commentStr>Backgound NOtes</commentStr>\r\n              <commentType>BKG</commentType>\r\n              <commentTitle>Profile Backgrd Note</commentTitle>\r\n            </Comment>\r\n          </ResortComment>\r\n          <ResortComment>\r\n            <Comment>\r\n              <commentOriginatorCode>*ORS*</commentOriginatorCode>\r\n              <guestViewable>1</guestViewable>\r\n              <lastUpdated>2014-08-08T13:33:21.000</lastUpdated>\r\n              <commentStr>Global Background Note</commentStr>\r\n              <commentType>BKG</commentType>\r\n              <commentTitle>Background Notes</commentTitle>\r\n            </Comment>\r\n          </ResortComment>\r\n          <ResortComment>\r\n            <Comment>\r\n              <commentOriginatorCode>SUPERVISOR</commentOriginatorCode>\r\n              <guestViewable>1</guestViewable>\r\n              <lastUpdated>2014-06-25T12:32:20.000</lastUpdated>\r\n              <commentStr>Profile is banned</commentStr>\r\n              <commentType>OWS</commentType>\r\n              <commentTitle>BANNED</commentTitle>\r\n            </Comment>\r\n          </ResortComment>\r\n        </ResortComments>\r\n        <ResortSpecialRequests>\r\n          <SpecialRequest mfSpecialRequestType=\"FEA\">\r\n            <requestCode>NS</requestCode>\r\n          </SpecialRequest>\r\n        </ResortSpecialRequests>\r\n        <WebAccounts>\r\n          <WebAccount>\r\n            <loginName>EMORGENSTERN</loginName>\r\n            <password>lGo17E/imFU=</password>\r\n            <secQuestion>Hp4riAzXmuw=</secQuestion>\r\n            <secAnswer>yjraoU8Yov0=</secAnswer>\r\n            <lastLoginDate>2010-10-20T09:21:29.000</lastLoginDate>\r\n            <insertSource>OWS</insertSource>\r\n            <passwordChangeDate>2012-05-01T16:29:26.000</passwordChangeDate>\r\n            <pwdSystemGenerated>0</pwdSystemGenerated>\r\n          </WebAccount>\r\n        </WebAccounts>\r\n        <ResortNegotiatedRates>\r\n          <NegotiatedRate>\r\n            <mfResort>SandBox-OPER1</mfResort>\r\n            <rateCode>MEMBER</rateCode>\r\n            <rateBeginDate>2007-03-05</rateBeginDate>\r\n          </NegotiatedRate>\r\n        </ResortNegotiatedRates>\r\n        <nationality>E</nationality>\r\n      </Profile>\r\n      <resProfileRPH>0</resProfileRPH>\r\n    </ResProfile>\r\n  </ResProfiles>\r\n  <RoomStays>\r\n    <RoomStay mfShareAction=\"NA\" mfReservationAction=\"NA\" reservationActionType=\"SYNC\" reservationStatusType=\"RESERVED\">\r\n      <roomInventoryCode>KDLX</roomInventoryCode>\r\n      <TimeSpan timeUnitType=\"DAY\">\r\n        <startTime>2016-05-18T00:00:00.000</startTime>\r\n        <numberOfTimeUnits>7</numberOfTimeUnits>\r\n      </TimeSpan>\r\n      <GuestCounts>\r\n        <GuestCount>\r\n          <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n          <mfCount>2</mfCount>\r\n        </GuestCount>\r\n        <GuestCount>\r\n          <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n          <mfCount>2</mfCount>\r\n        </GuestCount>\r\n      </GuestCounts>\r\n      <RatePlans>\r\n        <RatePlan reservationActionType=\"SYNC\">\r\n          <ratePlanRPH>0</ratePlanRPH>\r\n          <ratePlanCode>CEN29</ratePlanCode>\r\n          <TimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-05-18T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>2</numberOfTimeUnits>\r\n          </TimeSpan>\r\n          <mfMarketCode>NTRAN</mfMarketCode>\r\n          <Rates>\r\n            <Rate reservationActionType=\"SYNC\" rateBasisTimeUnitType=\"DAY\">\r\n              <rateRPH>0</rateRPH>\r\n              <Amount currencyCode=\"USD\">\r\n                <valueNum>375</valueNum>\r\n              </Amount>\r\n              <rateBasisUnits>1</rateBasisUnits>\r\n              <TimeSpan timeUnitType=\"DAY\">\r\n                <startTime>2016-05-18T00:00:00.000</startTime>\r\n                <numberOfTimeUnits>2</numberOfTimeUnits>\r\n              </TimeSpan>\r\n              <mfAdults>2</mfAdults>\r\n              <mfChildren>2</mfChildren>\r\n              <mfCribs>0</mfCribs>\r\n              <mfExtraBeds>0</mfExtraBeds>\r\n              <mfsourceCode>WEB</mfsourceCode>\r\n              <mfMarketCode>NTRAN</mfMarketCode>\r\n            </Rate>\r\n          </Rates>\r\n          <mfsourceCode>WEB</mfsourceCode>\r\n        </RatePlan>\r\n        <RatePlan reservationActionType=\"SYNC\">\r\n          <ratePlanRPH>1</ratePlanRPH>\r\n          <ratePlanCode>FAMILT</ratePlanCode>\r\n          <TimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-05-20T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>4</numberOfTimeUnits>\r\n          </TimeSpan>\r\n          <mfMarketCode>ITRAN</mfMarketCode>\r\n          <Rates>\r\n            <Rate reservationActionType=\"SYNC\" rateBasisTimeUnitType=\"DAY\">\r\n              <rateRPH>1</rateRPH>\r\n              <Amount currencyCode=\"USD\">\r\n                <valueNum>309</valueNum>\r\n              </Amount>\r\n              <rateBasisUnits>1</rateBasisUnits>\r\n              <TimeSpan timeUnitType=\"DAY\">\r\n                <startTime>2016-05-20T00:00:00.000</startTime>\r\n                <numberOfTimeUnits>4</numberOfTimeUnits>\r\n              </TimeSpan>\r\n              <mfAdults>2</mfAdults>\r\n              <mfChildren>2</mfChildren>\r\n              <mfCribs>0</mfCribs>\r\n              <mfExtraBeds>0</mfExtraBeds>\r\n              <mfsourceCode>WEB</mfsourceCode>\r\n              <mfMarketCode>ITRAN</mfMarketCode>\r\n            </Rate>\r\n          </Rates>\r\n          <mfsourceCode>WEB</mfsourceCode>\r\n        </RatePlan>\r\n        <RatePlan reservationActionType=\"SYNC\">\r\n          <ratePlanRPH>2</ratePlanRPH>\r\n          <ratePlanCode>CEN29</ratePlanCode>\r\n          <TimeSpan timeUnitType=\"DAY\">\r\n            <startTime>2016-05-24T00:00:00.000</startTime>\r\n            <numberOfTimeUnits>1</numberOfTimeUnits>\r\n          </TimeSpan>\r\n          <mfMarketCode>NTRAN</mfMarketCode>\r\n          <Rates>\r\n            <Rate reservationActionType=\"SYNC\" rateBasisTimeUnitType=\"DAY\">\r\n              <rateRPH>2</rateRPH>\r\n              <Amount currencyCode=\"USD\">\r\n                <valueNum>375</valueNum>\r\n              </Amount>\r\n              <rateBasisUnits>1</rateBasisUnits>\r\n              <TimeSpan timeUnitType=\"DAY\">\r\n                <startTime>2016-05-24T00:00:00.000</startTime>\r\n                <numberOfTimeUnits>1</numberOfTimeUnits>\r\n              </TimeSpan>\r\n              <mfAdults>2</mfAdults>\r\n              <mfChildren>2</mfChildren>\r\n              <mfCribs>0</mfCribs>\r\n              <mfExtraBeds>0</mfExtraBeds>\r\n              <mfsourceCode>WEB</mfsourceCode>\r\n              <mfMarketCode>NTRAN</mfMarketCode>\r\n            </Rate>\r\n          </Rates>\r\n          <mfsourceCode>WEB</mfsourceCode>\r\n        </RatePlan>\r\n      </RatePlans>\r\n      <marketSegmentCode>PROMO</marketSegmentCode>\r\n      <resGuestRPHs>0</resGuestRPHs>\r\n      <GuaranteeInfo guaranteeType=\"NA\">\r\n        <mfGuaranteeType>CC</mfGuaranteeType>\r\n        <GuaranteeDeposit>\r\n          <dueDate>2015-11-18T00:00:00.000</dueDate>\r\n          <Amount currencyCode=\"USD\">\r\n            <valueNum>642.2</valueNum>\r\n          </Amount>\r\n          <cancelIfNotReceived>0</cancelIfNotReceived>\r\n        </GuaranteeDeposit>\r\n      </GuaranteeInfo>\r\n      <CancelPenalties>\r\n        <CancelPenalty mfRuleType=\"DEPOSIT\">\r\n          <cancelByDate>2015-11-18T00:00:00.000</cancelByDate>\r\n          <Amount currencyCode=\"USD\">\r\n            <valueNum>642.2</valueNum>\r\n          </Amount>\r\n          <mfRuleScope>R</mfRuleScope>\r\n          <mfPercentage>50</mfPercentage>\r\n          <mfRuleDescription>50 Percent due</mfRuleDescription>\r\n          <activityDeposit>0</activityDeposit>\r\n          <roomDeposit>642.2</roomDeposit>\r\n          <ruleCode>50PCT</ruleCode>\r\n        </CancelPenalty>\r\n      </CancelPenalties>\r\n      <PaymentInstructions>\r\n        <PaymentInstruction paymentMethodType=\"NA\">\r\n          <mfPaymentMethod>AX</mfPaymentMethod>\r\n          <PaymentDue>\r\n            <Amount currencyCode=\"USD\"/>\r\n            <cancelIfNotReceived>0</cancelIfNotReceived>\r\n          </PaymentDue>\r\n        </PaymentInstruction>\r\n      </PaymentInstructions>\r\n      <mfsourceCode>WEB</mfsourceCode>\r\n      <mfchannelCode>TAD</mfchannelCode>\r\n      <mfconfidentialRate>0</mfconfidentialRate>\r\n      <mfAsbProrated>0</mfAsbProrated>\r\n    </RoomStay>\r\n  </RoomStays>\r\n  <resProfileRPHs>0</resProfileRPHs>\r\n  <mfupdateDate>2015-08-22T16:34:23.000</mfupdateDate>\r\n  <mfcomplementary>0</mfcomplementary>\r\n  <mfImage>\r\n    <numRooms>1</numRooms>\r\n    <Describe>\r\n      <resortName>Property 1 Opera Demo Multi Hotel</resortName>\r\n      <insertUser>OEDS$ADS</insertUser>\r\n      <updateUser>SUPERVISOR</updateUser>\r\n      <roomCategory>King Deluxe Room</roomCategory>\r\n      <rateCode>Tarpon Family Package</rateCode>\r\n      <guarantee>CC: Credit Card Guaranteed</guarantee>\r\n      <company>Micros Hotels and Resorts</company>\r\n    </Describe>\r\n    <Change>\r\n      <bArrivalDate>0</bArrivalDate>\r\n      <bNumNights>0</bNumNights>\r\n      <bNumAdults>0</bNumAdults>\r\n      <bNumChildren>0</bNumChildren>\r\n      <bNumRooms>0</bNumRooms>\r\n      <bCribs>0</bCribs>\r\n      <bRoomCategory>0</bRoomCategory>\r\n      <bPaymentType>0</bPaymentType>\r\n      <bGuarType>0</bGuarType>\r\n      <bDiscountReason>0</bDiscountReason>\r\n      <bMultipleRateYN>1</bMultipleRateYN>\r\n      <bResvStatus>0</bResvStatus>\r\n    </Change>\r\n  </mfImage>\r\n  <Services>  \r\n    <Service reservationActionType=\"SYNC\" servicePricingType=\"NA\" reservationStatusType=\"NA\">\r\n      <serviceRPH>0</serviceRPH>\r\n      <serviceInventoryCode>BF_REGULAR_LSTORNO4</serviceInventoryCode>        \r\n      <TimeSpan timeUnitType=\"DAY\">\r\n        <startTime>2016-05-18T00:00:00.000</startTime>\r\n        <numberOfTimeUnits>7</numberOfTimeUnits>\r\n      </TimeSpan>\r\n      <Price currencyCode=\"USD\">\r\n        <valueNum>100</valueNum>\r\n      </Price>\r\n      <quantity>1</quantity>\r\n      <ServicePrices>\r\n        <ServicePrice>\r\n          <beginDate>2016-05-18</beginDate>\r\n          <endDate>2016-05-19</endDate>\r\n          <unitPrice>25</unitPrice>\r\n        </ServicePrice>\r\n        <ServicePrice>\r\n          <beginDate>2016-05-24</beginDate>\r\n          <endDate>2016-05-24</endDate>\r\n          <unitPrice>25</unitPrice>\r\n        </ServicePrice>\r\n      </ServicePrices>\r\n    </Service>\r\n  </Services>\r\n</Reservation>\r\n"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/ngipublic/rest/oxi/pms", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["ngipublic", "rest", "oxi", "pms"]}}, "response": []}, {"name": "Then NGI calculates rateValue for Reservation having rateCode=CEN15,FAMILT, serviceName=BF_REGULAR_LSTORNO1", "event": [{"listen": "test", "script": {"id": "6b102fab-32e3-42c9-9bd3-d2db30f938b6", "exec": ["//variables", "var jsonData = pm.response.json();", "", "//value to compare with", "// RoomStaysData for arrivalDate 2016-05-18", "var reservationJSONToBeCompared = {\"createDate\":\"2020-03-27 18:44:50.825\",\"lastModifiedDate\":\"2020-03-27 18:44:50.825\",\"versionId\":20160127003,\"reservationId\":\"673797\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"correlationId\":\"20160127003\",\"statisticsCorrelationId\":null,\"bookingDate\":\"2015-04-20T19:06:51.000\",\"earliestArrivalDate\":\"2016-05-18\",\"latestDepartureDate\":\"2016-05-25\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"RESERVED\",\"nationality\":\"GB\",\"channel\":\"TAD\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2016-05-18\",\"departureDate\":\"2016-05-20\",\"marketCode\":\"NTRAN\",\"sourceBookingCode\":\"WEB\",\"numberOfChildren\":2,\"numberOfAdults\":2,\"bookingType\":\"CC\",\"invTypeCode\":\"KDLX\",\"bookedAccomTypeCode\":\"KDLX\",\"rateCode\":\"CEN29\",\"invBlockCode\":null,\"roomNumber\":null,\"rates\":[{\"rateValue\":375,\"startDate\":\"2016-05-18\",\"endDate\":\"2016-05-20\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":375,\"currencyCode\":null}],\"services\":[{\"serviceName\":\"BF_REGULAR_LSTORNO4\",\"serviceValue\":100,\"inclusive\":false,\"roomRevenuePackage\":true,\"reservationPackage\":true,\"startDate\":\"2016-05-18\",\"endDate\":\"2016-05-20\",\"inclusionType\":null}],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"NTRAN\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"byDay\":false,\"profileId\":null,\"primary\":null};", "", "// RoomStaysData for arrivalDate 2016-05-20", "var reservationJSONToBeCompared1 ={\"createDate\":\"2020-03-27 18:44:50.825\",\"lastModifiedDate\":\"2020-03-27 18:44:50.825\",\"versionId\":20160127003,\"reservationId\":\"673797\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"correlationId\":\"20160127003\",\"statisticsCorrelationId\":null,\"bookingDate\":\"2015-04-20T19:06:51.000\",\"earliestArrivalDate\":\"2016-05-18\",\"latestDepartureDate\":\"2016-05-25\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"RESERVED\",\"nationality\":\"GB\",\"channel\":\"TAD\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2016-05-20\",\"departureDate\":\"2016-05-24\",\"marketCode\":\"ITRAN\",\"sourceBookingCode\":\"WEB\",\"numberOfChildren\":2,\"numberOfAdults\":2,\"bookingType\":\"CC\",\"invTypeCode\":\"KDLX\",\"bookedAccomTypeCode\":\"KDLX\",\"rateCode\":\"FAMILT\",\"invBlockCode\":null,\"roomNumber\":null,\"rates\":[{\"rateValue\":309,\"startDate\":\"2016-05-20\",\"endDate\":\"2016-05-24\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":309,\"currencyCode\":null}],\"services\":[],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"ITRAN\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"byDay\":false,\"profileId\":null,\"primary\":null};", "", "// RoomStaysData for arrivalDate 2016-05-24", "var reservationJSONToBeCompared2 ={\"createDate\":\"2020-03-27 18:44:50.825\",\"lastModifiedDate\":\"2020-03-27 18:44:50.825\",\"versionId\":20160127003,\"reservationId\":\"673797\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"correlationId\":\"20160127003\",\"statisticsCorrelationId\":null,\"bookingDate\":\"2015-04-20T19:06:51.000\",\"earliestArrivalDate\":\"2016-05-18\",\"latestDepartureDate\":\"2016-05-25\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"RESERVED\",\"nationality\":\"GB\",\"channel\":\"TAD\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2016-05-24\",\"departureDate\":\"2016-05-25\",\"marketCode\":\"NTRAN\",\"sourceBookingCode\":\"WEB\",\"numberOfChildren\":2,\"numberOfAdults\":2,\"bookingType\":\"CC\",\"invTypeCode\":\"KDLX\",\"bookedAccomTypeCode\":\"KDLX\",\"rateCode\":\"CEN29\",\"invBlockCode\":null,\"roomNumber\":null,\"rates\":[{\"rateValue\":375,\"startDate\":\"2016-05-24\",\"endDate\":\"2016-05-25\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":375,\"currencyCode\":null}],\"services\":[{\"serviceName\":\"BF_REGULAR_LSTORNO4\",\"serviceValue\":100,\"inclusive\":false,\"roomRevenuePackage\":true,\"reservationPackage\":true,\"startDate\":\"2016-05-24\",\"endDate\":\"2016-05-25\",\"inclusionType\":null}],\"revenue\":null,\"originalCurrencyCode\":\"USD\",\"currencyExchangeId\":null,\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"NTRAN\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"byDay\":false,\"profileId\":null,\"primary\":null};", "", "", "arr=[];", "", "arr.push(reservationJSONToBeCompared,reservationJSONToBeCompared1,reservationJSONToBeCompared2);", "var i=0;", "//assertions", "for(item of arr)", "{", "    console.log(i);", "    assertJSONData(\"Verifying Client Code in OxiMessage\", jsonData,item);", "    assertRoomStaysData(\"Verifying Room stays json in OxiMessage\", jsonData.roomStays[i],item.roomStays[0]);", "    i++;", "}", "", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "     pm.expect(actualValue.versionId).to.eql(expectedValue.versionId);", "    pm.expect(actualValue.clientCode).to.eql(expectedValue.clientCode);", "    pm.expect(actualValue.propertyCode).to.eql(expectedValue.propertyCode);", "    pm.expect(actualValue.status).to.eql(expectedValue.status);", "    pm.expect(actualValue.nationality).to.eql(expectedValue.nationality);", "    pm.expect(actualValue.channel).to.eql(expectedValue.channel);", "     pm.expect(actualValue.reservationId).to.eql(expectedValue.reservationId);", "    pm.expect(actualValue.roomStays.arrivalDate).to.eql(expectedValue.roomStays.arrivalDate);", "    pm.expect(actualValue.roomStays.departureDate).to.eql(expectedValue.roomStays.departureDate);", "    pm.expect(actualValue.roomStays.marketCode).to.eql(expectedValue.roomStays.marketCode);", "    pm.expect(actualValue.roomStays.sourceBookingCode).to.eql(expectedValue.roomStays.sourceBookingCode);", "    pm.expect(actualValue.roomStays.numberOfChildren).to.eql(expectedValue.roomStays.numberOfChildren);", "    pm.expect(actualValue.roomStays.numberOfAdults).to.eql(expectedValue.roomStays.numberOfAdults);", "    pm.expect(actualValue.roomStays.bookingType).to.eql(expectedValue.roomStays.bookingType);", "    pm.expect(actualValue.roomStays.invTypeCode).to.eql(expectedValue.roomStays.invTypeCode);", "    pm.expect(actualValue.roomStays.bookedAccomTypeCode).to.eql(expectedValue.roomStays.bookedAccomTypeCode);", "    pm.expect(actualValue.roomStays.rateCode).to.eql(expectedValue.roomStays.rateCode);", "   pm.expect(actualValue.roomStays.originalCurrencyCode).to.eql(expectedValue.roomStays.originalCurrencyCode);", "   pm.expect(actualValue.roomStays.analyticalMarketSegmentCode).to.eql(expectedValue.roomStays.analyticalMarketSegmentCode);", "});}", "", "function assertRoomStaysData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue.rates.rateValue).to.eql(expectedValue.rates.rateValue);", "    pm.expect(actualValue.rates.startDate).to.eql(expectedValue.rates.startDate);", "    pm.expect(actualValue.rates.endDate).to.eql(expectedValue.rates.endDate);", "    pm.expect(actualValue.rates.grossRate).to.eql(expectedValue.rates.grossRate);", "    pm.expect(actualValue.services.serviceName).to.eql(expectedValue.rates.serviceName);", "    pm.expect(actualValue.services.serviceValue).to.eql(expectedValue.rates.serviceValue);", "    pm.expect(actualValue.services.inclusive).to.eql(expectedValue.rates.inclusive);", "    pm.expect(actualValue.services.startDate).to.eql(expectedValue.rates.startDate);", "    pm.expect(actualValue.services.endDate).to.eql(expectedValue.rates.endDate);", "    pm.expect(actualValue.services.roomRevenuePackage).to.eql(expectedValue.rates.roomRevenuePackage);", "});}"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "fae1d6e5-0b7c-4a6d-abc7-66c07bba0485", "exec": ["/*var millisecondsToWait = 5000;\r", "setTimeout(function() {\r", "    // Whatever you want to do after the wait\r", "}, millisecondsToWait);*/"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusReservations/search/findByClientCodeAndPropertyCodeAndReservationId?clientCode={{client_Code}}&propertyCode={{property_Code}}&reservationId=673797", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusReservations", "search", "findByClientCodeAndPropertyCodeAndReservationId"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "reservationId", "value": "673797"}]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"id": "72c7c3e9-3519-4ff2-85f5-2afb2c24b6aa", "type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"id": "32336d45-f65d-4bd2-a007-6b51f8130564", "type": "text/javascript", "exec": [""]}}], "protocolProfileBehavior": {}}