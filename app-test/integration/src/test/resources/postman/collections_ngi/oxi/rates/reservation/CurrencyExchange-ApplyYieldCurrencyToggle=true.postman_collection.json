{"info": {"_postman_id": "47eba09a-bab4-49eb-a1ea-bc35b358776d", "name": "Currency Exchange: ApplyYieldCurrency Toggle = true", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Given that property configuration is saved in nucleusVendorConfigParams collection", "event": [{"listen": "prerequest", "script": {"exec": ["//variables", "var moment = require('moment');", "pm.globals.set(\"timestamp\", moment().format(\"YYYY-MM-DD\"));", "pm.globals.set(\"timestampWithoutYear\", moment().format(\"MM-DD\"));", "pm.globals.set(\"timestampAfter3Year\", moment().add('years', 3).format(\"YYYY-MM-DD\"));", "pm.globals.set(\"client_Code\", \"SandBox\");", "pm.globals.set(\"property_Code\", \"OPER1\");", "pm.globals.set(\"propertyId\", \"11020\");", "pm.globals.set(\"databaseId\", \"011020\");", "pm.globals.set(\"inboundVendorId\", \"5b6320b6ccb81834a0a1260b\");", "pm.globals.set(\"systemDate\", \"2018-01-01\");", "pm.globals.set(\"systemEndDate\", \"2020-12-31\");", "pm.globals.set(\"rateMsgEndDate\", \"2020-12-31\");", "pm.globals.set(\"bookingDate\", \"2018-01-20\");", "pm.globals.set(\"bookingFrom\", \"2018-01-20\");", "", "//pm.globals.set(\"midSeasonStartDate\", \"2016-10-30\");", "//pm.globals.set(\"resAutoCancellation\", \"resAutoCancellation\");", "", "", "", "var collection = JSON.parse('[\"nucleusQualifiedRatePlans\",\"nucleusUnqualifiedRatePlans\",\"oxiPackages\",\"nucleusGroupBlockMasters\",\"nucleusVendorConfigParamses\",\"currencyExchanges\"]');", "pm.globals.set(\"collectionName\", collection);", "", "//cleaning up NGI collections", "eval(pm.globals.get(\"cleanUpScript\"));", "eval(pm.globals.get(\"cleanUpScriptForIndividualCollection\"));", "", "pm.globals.set(\"baseCurrency\", \"GBP\");", "eval(pm.globals.get(\"cleanCurrencyExchangeColl\"));", "", "pm.globals.set(\"baseCurrency\", \"USD\");", "eval(pm.globals.get(\"cleanCurrencyExchangeColl\"));", "", "pm.globals.get(\"cleanUpCollection\")", "pm.globals.set(\"param_externalSystem\", \"pacman.core.property.externalSystem\");", "pm.globals.set(\"param_externalSubSystem\", \"pacman.core.property.externalSystem.subSystem\");", "pm.globals.set(\"propertyStage\", \"Population\");", "", "//variables", "var parameters = [pm.globals.get(\"param_externalSystem\")+'/pacman.'+pm.globals.get(\"client_Code\")+'.'+pm.globals.get(\"property_Code\")+'/NGI',pm.globals.get(\"param_externalSubSystem\")+'/pacman.'+pm.globals.get(\"client_Code\")+'.'+pm.globals.get(\"property_Code\")+'/OXI'];", "pm.globals.set(\"params\", parameters);", "//setting parameter values", "eval(pm.globals.get(\"setParameter\"));", "// //setting stage", "eval(pm.globals.get(\"setStage\"));"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["//setting parameter values", "eval(pm.globals.get(\"setParameter\"));", "", "// //setting stage", "eval(pm.globals.get(\"setStage\"));", "", "pm.sendRequest({url: pm.globals.get('hostName')+':'+pm.globals.get('g3Port')+'/api/configParam/v1/pacman.feature.RoomTypeRecodingEnabled?context=pacman.SandBox.'+ pm.globals.get('property_Code') + '&value=false&propertyId='+pm.globals.get('propertyId'),method: 'PUT',header:{'Authorization': 'Basic ********************************','Accept': '*/*'}}, function (err, res) {});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"inboundVendorId\": \"{{inboundVendorId}}\",\r\n    \"outboundVendorId\": null,\r\n    \"customReplyToAddress\": null,\r\n    \"useCustomSoapAction\": null,\r\n    \"name\": null,\r\n    \"inboundCredentials\": null,\r\n    \"outboundCredentials\": null,\r\n    \"integrationType\": \"OXI_PMS\",\r\n    \"chains\": [\r\n        {\r\n            \"chainCode\": \"{{client_Code}}\",\r\n            \"outboundCredentials\": null,\r\n            \"inboundCredentials\": null,\r\n            \"clientEnvironmentName\": \"g3\",\r\n            \"baseCurrencyCode\": \"USD\",\r\n            \"hotels\": [\r\n                {\r\n                    \"hotelCode\": \"{{property_Code}}\",\r\n                    \"g3HotelCode\": null,\r\n                    \"g2HotelCode\": null,\r\n                    \"inboundHotelCode\": null,\r\n                    \"propertyName\": \"{{property_Code}}\",\r\n                    \"outgoingUrl\": \"{{hostName}}:{{ngiPort}}/ngipublic/rest/mockserver/oxi/requestroomtype\",\r\n                    \"oxiInterfaceName\": \"OXI_PMS\",\r\n                    \"taxAdjustmentValue\": 10,\r\n                    \"outboundCredentials\": null,\r\n                    \"inboundCredentials\": null,\r\n                    \"baseCurrencyCode\": \"USD\",\r\n                    \"pastDays\": null,\r\n                    \"futureDays\": null,\r\n                    \"assumeTaxIncluded\": true,\r\n                    \"assumePackageIncluded\": true,\r\n                    \"installMode\": false,\r\n                    \"scheduledDeferredDelivery\": null,\r\n                    \"inCatchup\": null,\r\n                    \"calculateNonPickedUpBlocksUsingSummaryData\": null,\r\n                    \"handlePreviouslyStraightMarketSegmentsInAms\": null,\r\n                    \"unqualifiedRatesDirectPopulationDisabled\": false,\r\n                    \"qualifiedRatesDirectPopulationDisabled\": null,\r\n                    \"roomRevenuePackages\": \"BKFS\",\r\n                    \"installationReservationsThreshold\": null,\r\n                    \"populatePackageDataEnabled\": null,\r\n                    \"defaultRoomType\": null\r\n                }\r\n            ],\r\n            \"unqualifiedRatesDirectPopulationDisabled\": null,\r\n            \"qualifiedRatesDirectPopulationDisabled\": null,\r\n            \"installationReservationsThreshold\": null,\r\n            \"populatePackageDataEnabled\": null\r\n        }\r\n    ],\r\n    \"configurations\": null,\r\n    \"unqualifiedRatesDirectPopulationDisabled\": null,\r\n    \"qualifiedRatesDirectPopulationDisabled\": null,\r\n    \"cancelMultiUnitDecrements\": null,\r\n    \"installationReservationsThreshold\": null,\r\n    \"_links\": {\r\n        \"self\": {\r\n            \"href\": \"{{hostName}}:{{ngiPort}}/nucleusVendorConfigParamses/{{inboundVendorId}}\"\r\n        },\r\n        \"nucleusVendorConfigParams\": {\r\n            \"href\": \"{{hostName}}:{{ngiPort}}/nucleusVendorConfigParamses/{{inboundVendorId}}\"\r\n        }\r\n    }\r\n}"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusVendorConfigParamses/{{inboundVendorId}}", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusVendorConfigParamses", "{{inboundVendorId}}"]}}, "response": []}, {"name": "Set Parameter: pacman.core.property.ApplyYieldCurrency = true", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/configParam/v1/pacman.core.property.ApplyYieldCurrency?context=pacman.SandBox.OPER1&value=true&propertyId={{propertyId}}", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "config<PERSON><PERSON><PERSON>", "pacman.core.property.ApplyYieldCurrency"], "query": [{"key": "context", "value": "pacman.SandBox.OPER1"}, {"key": "value", "value": "true"}, {"key": "propertyId", "value": "{{propertyId}}"}]}}, "response": []}, {"name": "Set Parameter: pacman.integration.ratchet.yieldCurrencyCode = GBP", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/configParam/v1/pacman.core.property.YieldCurrencyCode?context=pacman.SandBox.OPER1&value=GBP&propertyId={{propertyId}}", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "config<PERSON><PERSON><PERSON>", "pacman.core.property.YieldCurrencyCode"], "query": [{"key": "context", "value": "pacman.SandBox.OPER1"}, {"key": "value", "value": "GBP"}, {"key": "propertyId", "value": "{{propertyId}}"}]}}, "response": []}, {"name": "Set Parameter: pacman.core.property.BaseCurrencyCode = USD", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/configParam/v1/pacman.core.property.BaseCurrencyCode?context=pacman.SandBox.OPER1&value=USD&propertyId={{propertyId}}", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "config<PERSON><PERSON><PERSON>", "pacman.core.property.BaseCurrencyCode"], "query": [{"key": "context", "value": "pacman.SandBox.OPER1"}, {"key": "value", "value": "USD"}, {"key": "propertyId", "value": "{{propertyId}}"}]}}, "response": []}, {"name": "Given Exchange Rates is added in NGI from GBP to USD dated 2018-07-01 & 2019-01-20", "event": [{"listen": "prerequest", "script": {"exec": ["pm.globals.set(\"propertyId\", \"11020\");\r", "pm.globals.set(\"databaseId\", \"011020\");\r", "var today = new Date().toISOString().slice(0,10);\r", "\r", "console.log(today);\r", "pm.globals.set(\"todayDate\", today);\r", "\r", "var tomorrow = new Date();\r", "tomorrow.setDate(tomorrow.getDate() + 1);\r", "var tommorowDt = tomorrow.toISOString().slice(0,10);\r", "console.log(tommorowDt);\r", "pm.globals.set(\"tomorrowDate\", tommorowDt);\r", "\r", "\r", "var dayAfterTomorrow = new Date();\r", "dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2);\r", "var afterTommorowDt = dayAfterTomorrow.toISOString().slice(0,10);\r", "console.log(afterTommorowDt);\r", "pm.globals.set(\"dayAfterTomorrowDate\", afterTommorowDt);\r", "\r", "\r", "\r", "var after3Days = new Date();\r", "after3Days.setDate(after3Days.getDate() + 3);\r", "var after3DaysDt = after3Days.toISOString().slice(0,10);\r", "console.log(after3DaysDt);\r", "pm.globals.set(\"dayAfter3Days\", after3DaysDt);\r", "\r", "\r", "var yesterday = new Date();\r", "yesterday.setDate(yesterday.getDate() - 1);\r", "var ydayDt = yesterday.toISOString().slice(0,10);\r", "console.log(ydayDt);\r", "pm.globals.set(\"yesterday\", ydayDt);\r", "\r", "var dayBeforeYesterday = new Date();\r", "dayBeforeYesterday.setDate(dayBeforeYesterday.getDate() - 2);\r", "var beforeYdayDt = dayBeforeYesterday.toISOString().slice(0,10);\r", "console.log(beforeYdayDt);\r", "pm.globals.set(\"dayBeforeYesterday\", beforeYdayDt);\r", "\r", "var weekAfterToday = new Date();\r", "weekAfterToday.setDate(weekAfterToday.getDate() + 7 );\r", "var weekAfterTodayDt = weekAfterToday.toISOString().slice(0,10);\r", "console.log(weekAfterTodayDt);\r", "pm.globals.set(\"weekAfterToday\", weekAfterTodayDt);\r", "\r", "var twoWeekAfterToday = new Date();\r", "twoWeekAfterToday.setDate(twoWeekAfterToday.getDate() + 15 );\r", "var twoWeekAfterTodayDt = twoWeekAfterToday.toISOString().slice(0,10);\r", "console.log(twoWeekAfterTodayDt);\r", "pm.globals.set(\"twoWeekAfterToday\", twoWeekAfterTodayDt);\r", "\r", "\r", "var now = new Date();\r", "var oneYr = new Date();\r", "oneYr.setYear(now.getFullYear() - 1);\r", "\r", "console.log(\"Current Year =\"+now.getFullYear());\r", "pm.globals.set(\"currentYear\", now.getFullYear());\r", "\r", "console.log(\"Last Year =\"+oneYr.getFullYear());\r", "pm.globals.set(\"lastYear\", oneYr.getFullYear());\r", "\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n\"to\":\"USD\",\r\n\"from\":\"GBP\",\r\n\"fromDate\":\"2018-06-01\",\r\n\"toDate\":\"{{dayBeforeYesterday}}\",\r\n\"exchangeRate\":\"100\",\r\n\"inverse\":\"true\"\r\n}\r\n"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/currencyExchange/GBP", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["currencyExchange", "GBP"]}}, "response": []}, {"name": "Given Exchange Rates is added in NGI from GBP to USD dated 2019-01-21 to Current Date", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n\"to\":\"USD\",\r\n\"from\":\"GBP\",\r\n\"fromDate\":\"{{yesterday}}\",\r\n\"toDate\":\"{{todayDate}}\",\r\n\"exchangeRate\":\"200\",\r\n\"inverse\":\"true\"\r\n}\r\n"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/currencyExchange/GBP", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["currencyExchange", "GBP"]}}, "response": []}, {"name": "When Reservation is sent having currency in GBP and Occupancy Date > CurrentDate", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\\\"1.0\\\" encoding=\\\"UTF-8\\\"?>\r\n<?Label SandBox-OPER1|RESERVATION|278719417|SUCCESS|2018060120191231|166/3898?>\r\n<Reservation\r\n    xmlns=\\\"reservation.fidelio.5.0\\\" mfShareAction=\\\"NA\\\" mfReservationAction=\\\"CHECKOUT\\\">\r\n    <HotelReference>\r\n        <hotelCode>SandBox-OPER1</hotelCode>\r\n    </HotelReference>\r\n    <reservationID>458952088</reservationID>\r\n    <reservationOriginatorCode>EUNICE_LAU</reservationOriginatorCode>\r\n    <originalBookingDate>2018-07-31T14:56:56.000</originalBookingDate>\r\n    <StayDateRange timeUnitType=\\\"DAY\\\">\r\n        <startTime>{{tomorrowDate}}T17:33:35.000</startTime>\r\n        <numberOfTimeUnits>1</numberOfTimeUnits>\r\n    </StayDateRange>\r\n    <GuestCounts>\r\n        <GuestCount>\r\n            <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n            <mfCount>2</mfCount>\r\n        </GuestCount>\r\n        <GuestCount>\r\n            <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n            <mfCount>2</mfCount>\r\n        </GuestCount>\r\n    </GuestCounts>\r\n    <ResGuests>\r\n        <ResGuest reservationActionType=\\\"SYNC\\\">\r\n            <resGuestRPH>0</resGuestRPH>\r\n            <profileRPHs>0, 1</profileRPHs>\r\n            <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n            <InHouseTimeSpan timeUnitType=\\\"DAY\\\">\r\n                <startTime>{{tomorrowDate}}T17:33:35.000</startTime>\r\n                <numberOfTimeUnits>1</numberOfTimeUnits>\r\n            </InHouseTimeSpan>\r\n            <ArrivalTransport/>\r\n            <DepartureTransport/>\r\n            <arrivalTime>{{tomorrowDate}}T17:33:35.000</arrivalTime>\r\n            <departureTime>{{dayAfterTomorrowDate}}T08:34:00.000</departureTime>\r\n            <reservationID>358952088</reservationID>\r\n            <ReservationReferences>\r\n                <ReservationReference type=\\\"GUESTID\\\" referenceNumber=\\\"358952088\\\" legNumber=\\\"1\\\"/>\r\n                <ReservationReference type=\\\"TA_RECORD_LOCATOR\\\" referenceNumber=\\\"1449708029\\\"/>\r\n            </ReservationReferences>\r\n            <preRegistered>0</preRegistered>\r\n            <commissionPaidTo>T</commissionPaidTo>\r\n        </ResGuest>\r\n    </ResGuests>\r\n    <ResProfiles>\r\n        <ResProfile>\r\n            <Profile\r\n                xmlns=\\\"profile.fidelio.5.0\\\" profileType=\\\"GUEST\\\" multiProperty=\\\"0\\\" miniProfile=\\\"0\\\" pseudoProfile=\\\"0\\\" replaceAddress=\\\"0\\\">\r\n                <creatorCode>SARAH_RYAN1</creatorCode>\r\n                <createdDate>2018-07-31T14:56:05.000</createdDate>\r\n                <lastUpdaterCode>BRYONY_MCLANAGH</lastUpdaterCode>\r\n                <lastUpdated>{{tomorrowDate}}T18:33:35.000</lastUpdated>\r\n                <IndividualName>\r\n                    <nameSur/>\r\n                </IndividualName>\r\n                <primaryLanguageID>E</primaryLanguageID>\r\n                <PostalAddresses>\r\n                    <PostalAddress addressType=\\\"HOME\\\">\r\n                        <postalCode>Z894K</postalCode>\r\n                        <countryCode>GB</countryCode>\r\n                        <mfPrimaryYN>Y</mfPrimaryYN>\r\n                        <mfAddressLanguage>E</mfAddressLanguage>\r\n                        <cleansed>0</cleansed>\r\n                    </PostalAddress>\r\n                </PostalAddresses>\r\n                <PhoneNumbers>\r\n                    <PhoneNumber phoneNumberType=\\\"HOME\\\">\r\n                        <mfPrimaryYN>Y</mfPrimaryYN>\r\n                        <confirmation>0</confirmation>\r\n                    </PhoneNumber>\r\n                </PhoneNumbers>\r\n                <mfResort>SandBox-OPER1</mfResort>\r\n                <mfResortProfileID>290184999</mfResortProfileID>\r\n                <mfAllowMail>NO</mfAllowMail>\r\n                <mfAllowEMail>NO</mfAllowEMail>\r\n                <mfGuestPriv>NO</mfGuestPriv>\r\n                <mfAllowPhone>0</mfAllowPhone>\r\n                <mfAllowSMS>0</mfAllowSMS>\r\n                <SalesExtention/>\r\n                <PrivacyOption>\r\n                    <mfAllowMail>N</mfAllowMail>\r\n                    <mfAllowEMail>N</mfAllowEMail>\r\n                    <mfAllowPhone>0</mfAllowPhone>\r\n                    <mfAllowSMS>0</mfAllowSMS>\r\n                    <mfAllowHistory>1</mfAllowHistory>\r\n                    <mfAllowMarketResearch>0</mfAllowMarketResearch>\r\n                    <mfAllowThirdParty>0</mfAllowThirdParty>\r\n                </PrivacyOption>\r\n                <ResortList>SandBox-OPER1</ResortList>\r\n                <MultiResortEntities>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>CREDIT_CARDS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RESORT_ARS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>COMMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>PREFERENCES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>NEGOTIATED_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_NEG_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_COMMISSIONS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>CHANNEL_ACCESS_CODES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>YIELD_ADJUSTMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RELATIONSHIPS</entity>\r\n                    </MultiResortEntity>\r\n                </MultiResortEntities>\r\n            </Profile>\r\n            <resProfileRPH>0</resProfileRPH>\r\n        </ResProfile>\r\n        <ResProfile>\r\n            <Profile\r\n                xmlns=\\\"profile.fidelio.5.0\\\" profileType=\\\"TRAVEL\\\" multiProperty=\\\"0\\\" miniProfile=\\\"0\\\" pseudoProfile=\\\"0\\\" replaceAddress=\\\"0\\\">\r\n                <creatorCode>OXI-OPERA</creatorCode>\r\n                <createdDate>2012-08-10T21:36:40.000</createdDate>\r\n                <lastUpdaterCode>NIKITA_AGGARWAL</lastUpdaterCode>\r\n                <lastUpdated>2018-09-10T17:39:10.000</lastUpdated>\r\n                <genericName>booking.com</genericName>\r\n                <IndividualName>\r\n                    <nameSur/>\r\n                </IndividualName>\r\n                <mfResort>SandBox-OPER1</mfResort>\r\n                <mfResortProfileID>5371710</mfResortProfileID>\r\n                <mfNameCode>96040394</mfNameCode>\r\n                <mfAllowMail>YES</mfAllowMail>\r\n                <mfAllowEMail>NO</mfAllowEMail>\r\n                <mfGuestPriv>NO</mfGuestPriv>\r\n                <mfAllowPhone>0</mfAllowPhone>\r\n                <mfAllowSMS>0</mfAllowSMS>\r\n                <SalesExtention/>\r\n                <PrivacyOption>\r\n                    <mfAllowMail>Y</mfAllowMail>\r\n                    <mfAllowEMail>N</mfAllowEMail>\r\n                    <mfAllowPhone>0</mfAllowPhone>\r\n                    <mfAllowSMS>0</mfAllowSMS>\r\n                    <mfAllowHistory>1</mfAllowHistory>\r\n                    <mfAllowMarketResearch>1</mfAllowMarketResearch>\r\n                    <mfAllowThirdParty>1</mfAllowThirdParty>\r\n                </PrivacyOption>\r\n                <ResortList>SandBox-OPER1</ResortList>\r\n                <MultiResortEntities>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>CREDIT_CARDS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RESORT_ARS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>COMMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>PREFERENCES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>NEGOTIATED_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_NEG_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_COMMISSIONS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>CHANNEL_ACCESS_CODES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>YIELD_ADJUSTMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RELATIONSHIPS</entity>\r\n                    </MultiResortEntity>\r\n                </MultiResortEntities>\r\n            </Profile>\r\n            <resProfileRPH>1</resProfileRPH>\r\n        </ResProfile>\r\n    </ResProfiles>\r\n    <RoomStays>\r\n        <RoomStay mfShareAction=\\\"NA\\\" mfReservationAction=\\\"NA\\\" reservationActionType=\\\"SYNC\\\" reservationStatusType=\\\"CHECKEDOUT\\\">\r\n            <roomInventoryCode>CKC</roomInventoryCode>\r\n            <roomID>311</roomID>\r\n            <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                <startTime>{{tomorrowDate}}T17:33:35.000</startTime>\r\n                <numberOfTimeUnits>1</numberOfTimeUnits>\r\n            </TimeSpan>\r\n            <GuestCounts>\r\n                <GuestCount>\r\n                    <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n                    <mfCount>2</mfCount>\r\n                </GuestCount>\r\n                <GuestCount>\r\n                    <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n                    <mfCount>2</mfCount>\r\n                </GuestCount>\r\n            </GuestCounts>\r\n            <RatePlans>\r\n                <RatePlan reservationActionType=\\\"SYNC\\\">\r\n                    <ratePlanRPH>0</ratePlanRPH>\r\n                    <ratePlanCode>OTAGENA1</ratePlanCode>\r\n                    <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                        <startTime>{{tomorrowDate}}T00:00:00.000</startTime>\r\n                        <numberOfTimeUnits>1</numberOfTimeUnits>\r\n                    </TimeSpan>\r\n                    <mfMarketCode>OTA</mfMarketCode>\r\n                    <Rates>\r\n                        <Rate reservationActionType=\\\"SYNC\\\" rateBasisTimeUnitType=\\\"DAY\\\">\r\n                            <rateRPH>0</rateRPH>\r\n                            <Amount currencyCode=\\\"GBP\\\">\r\n                                <valueNum>169</valueNum>\r\n                            </Amount>\r\n                            <rateBasisUnits>1</rateBasisUnits>\r\n                            <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                                <startTime>{{tomorrowDate}}T00:00:00.000</startTime>\r\n                                <numberOfTimeUnits>1</numberOfTimeUnits>\r\n                            </TimeSpan>\r\n                            <mfAdults>2</mfAdults>\r\n                            <mfChildren>2</mfChildren>\r\n                            <mfCribs>0</mfCribs>\r\n                            <mfExtraBeds>0</mfExtraBeds>\r\n                            <mfsourceCode>BC</mfsourceCode>\r\n                            <mfMarketCode>OTA</mfMarketCode>\r\n                        </Rate>\r\n                    </Rates>\r\n                    <mfsourceCode>BC</mfsourceCode>\r\n                </RatePlan>\r\n            </RatePlans>\r\n            <marketSegmentCode>OTA</marketSegmentCode>\r\n            <resGuestRPHs>0</resGuestRPHs>\r\n            <resCommentRPHs>0</resCommentRPHs>\r\n            <GuaranteeInfo guaranteeType=\\\"NA\\\">\r\n                <mfGuaranteeType>CHECKED IN</mfGuaranteeType>\r\n                <GuaranteeDeposit>\r\n                    <Amount currencyCode=\\\"GBP\\\"/>\r\n                    <cancelIfNotReceived>0</cancelIfNotReceived>\r\n                </GuaranteeDeposit>\r\n            </GuaranteeInfo>\r\n            <CancelPenalties>\r\n                <CancelPenalty mfRuleType=\\\"CANCEL\\\" manualRule=\\\"0\\\">\r\n                    <cancelByDate>{{tomorrowDate}}T14:00:00.000</cancelByDate>\r\n                    <Amount currencyCode=\\\"GBP\\\">\r\n                        <valueNum>169</valueNum>\r\n                    </Amount>\r\n                    <mfRuleScope>R</mfRuleScope>\r\n                    <mfPercentage>100</mfPercentage>\r\n                    <mfCancelPercentDue>100</mfCancelPercentDue>\r\n                    <mfCancelRoomNights>1</mfCancelRoomNights>\r\n                    <mfRuleDescription>Cancel by 2pm on the Day of Arrival</mfRuleDescription>\r\n                    <ruleCode>2PM</ruleCode>\r\n                </CancelPenalty>\r\n            </CancelPenalties>\r\n            <PaymentInstructions>\r\n                <PaymentInstruction paymentMethodType=\\\"NA\\\">\r\n                    <mfPaymentMethod>CA</mfPaymentMethod>\r\n                    <PaymentDue>\r\n                        <Amount currencyCode=\\\"GBP\\\"/>\r\n                        <cancelIfNotReceived>0</cancelIfNotReceived>\r\n                    </PaymentDue>\r\n                </PaymentInstruction>\r\n            </PaymentInstructions>\r\n            <mfcomplementaryCode>FAM</mfcomplementaryCode>\r\n            <mfsourceCode>BC</mfsourceCode>\r\n            <mfchannelCode>OTA</mfchannelCode>\r\n            <mfconfidentialRate>1</mfconfidentialRate>\r\n            <mfAsbProrated>0</mfAsbProrated>\r\n        </RoomStay>\r\n    </RoomStays>\r\n    <resCommentRPHs>0</resCommentRPHs>\r\n    <resProfileRPHs>0, 1</resProfileRPHs>\r\n    <mfupdateDate>{{dayAfterTomorrowDate}}T09:34:20.000</mfupdateDate>\r\n    <mfcomplementary>0</mfcomplementary>\r\n    <mfImage>\r\n        <numRooms>1</numRooms>\r\n        <Describe>\r\n            <resortName>Huntingtower Hotel Perth - Boutique by Leonardo</resortName>\r\n            <insertUser>SARAH_RYAN1</insertUser>\r\n            <updateUser>EUNICE_LAU</updateUser>\r\n            <roomCategory>Standard King room with Sofa</roomCategory>\r\n            <rateCode>Flexible Rate Inc Breakfast</rateCode>\r\n            <marketCode>Online Travel Agents</marketCode>\r\n            <guarantee>CHECKED IN: Checked In</guarantee>\r\n            <company>Jurys Inns Group Limited</company>\r\n        </Describe>\r\n        <Change>\r\n            <bArrivalDate>0</bArrivalDate>\r\n            <bNumNights>0</bNumNights>\r\n            <bNumAdults>0</bNumAdults>\r\n            <bNumChildren>0</bNumChildren>\r\n            <bNumRooms>0</bNumRooms>\r\n            <bCribs>0</bCribs>\r\n            <bRoomCategory>0</bRoomCategory>\r\n            <bPaymentType>0</bPaymentType>\r\n            <bGuarType>0</bGuarType>\r\n            <bDiscountReason>0</bDiscountReason>\r\n            <bMultipleRateYN>0</bMultipleRateYN>\r\n            <bResvStatus>0</bResvStatus>\r\n        </Change>\r\n    </mfImage>\r\n    <RateServices>\r\n        <RateService>\r\n            <Service reservationActionType=\\\"SYNC\\\" servicePricingType=\\\"NA\\\" reservationStatusType=\\\"NA\\\">\r\n                <serviceRPH>0</serviceRPH>\r\n                <serviceInventoryCode>BKFS</serviceInventoryCode>\r\n                <ratePlanCode>OTAGENA1</ratePlanCode>\r\n                <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                    <startTime>{{tomorrowDate}}T00:00:00.000</startTime>\r\n                    <numberOfTimeUnits>1</numberOfTimeUnits>\r\n                </TimeSpan>\r\n                <Price currencyCode=\\\"GBP\\\">\r\n                    <valueNum>12</valueNum>\r\n                </Price>\r\n                <quantity>1</quantity>\r\n                <ServicePrices>\r\n                    <ServicePrice>\r\n                        <beginDate>{{tomorrowDate}}</beginDate>\r\n                        <endDate>{{tomorrowDate}}</endDate>\r\n                        <unitPrice>6</unitPrice>\r\n                    </ServicePrice>\r\n                </ServicePrices>\r\n            </Service>\r\n        </RateService>\r\n    </RateServices>\r\n</Reservation>"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/ngipublic/rest/oxi/pms", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["ngipublic", "rest", "oxi", "pms"]}}, "response": []}, {"name": "Then NGI calculates rateValue for Reservation from GBP to USD as Property base currency is USD for past", "event": [{"listen": "test", "script": {"exec": ["//variables", "var jsonData = pm.response.json();", "", "", "//value to compare with", "var reservationJSONToBeCompared =JSON.parse('{\"versionId\":278719417,\"reservationId\":\"458952088\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"statisticsCorrelationId\":null,\"bookingDate\":\"2018-07-31T14:56:56.000\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"CHECKED_OUT\",\"nationality\":\"GB\",\"channel\":\"OTA\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"'", "+pm.globals.get(\"tomorrowDate\")", "+'\",\"departureDate\":\"'", "+pm.globals.get(\"dayAfterTomorrowDate\")", "+'\",\"marketCode\":\"OTA\",\"sourceBookingCode\":\"BC\",\"numberOfChildren\":2,\"numberOfAdults\":2,\"bookingType\":\"CHECKED IN\",\"invTypeCode\":\"CKC\",\"bookedAccomTypeCode\":\"FAM\",\"rateCode\":\"OTAGENA1\",\"invBlockCode\":null,\"roomNumber\":\"311\",\"rates\":[{\"rateValue\":30727.27273,\"startDate\":\"'", "+pm.globals.get(\"tomorrowDate\")", "+'\",\"endDate\":\"'", "+pm.globals.get(\"dayAfterTomorrowDate\")+'\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":169,\"currencyCode\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"serviceValue\":12,\"inclusive\":true,\"roomRevenuePackage\":true,\"reservationPackage\":false,\"startDate\":\"'", "+pm.globals.get(\"tomorrowDate\")", "+'\",\"endDate\":\"'", "+pm.globals.get(\"dayAfterTomorrowDate\")", "+'\",\"inclusionType\":null}],\"revenue\":null,\"originalCurrencyCode\":\"GBP\",\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"OTA\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"primary\":null,\"earliestArrivalDate\":\"'", "+pm.globals.get(\"tomorrowDate\")", "+'\",\"latestDepartureDate\":\"'", "+pm.globals.get(\"dayAfterTomorrowDate\")+'\"}');", "", "//assertions", "assertJSONData(\"Verifying Client Code in OxiMessage\", jsonData,reservationJSONToBeCompared);", "", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue.versionId).to.eql(expectedValue.versionId);", "     pm.expect(actualValue.reservationId).to.eql(expectedValue.reservationId);", "     pm.expect(actualValue.clientCode).to.eql(expectedValue.clientCode);", "     pm.expect(actualValue.propertyCode).to.eql(expectedValue.propertyCode);", "     pm.expect(actualValue.bookingDate).to.eql(expectedValue.bookingDate);", "     pm.expect(actualValue.earliestArrivalDate).to.eql(expectedValue.earliestArrivalDate);", "     pm.expect(actualValue.latestDepartureDate).to.eql(expectedValue.latestDepartureDate);", "     pm.expect(actualValue.status).to.eql(expectedValue.status);", "     pm.expect(actualValue.nationality).to.eql(expectedValue.nationality);", "     pm.expect(actualValue.channel).to.eql(expectedValue.channel);", "     pm.expect(actualValue.roomStays[0].arrivalDate).to.eql(expectedValue.roomStays[0].arrivalDate);", "     pm.expect(actualValue.roomStays[0].departureDate).to.eql(expectedValue.roomStays[0].departureDate);", "     pm.expect(actualValue.roomStays[0].marketCode).to.eql(expectedValue.roomStays[0].marketCode);", "     pm.expect(actualValue.roomStays[0].sourceBookingCode).to.eql(expectedValue.roomStays[0].sourceBookingCode);", "     pm.expect(actualValue.roomStays[0].numberOfChildren).to.eql(expectedValue.roomStays[0].numberOfChildren);", "     pm.expect(actualValue.roomStays[0].numberOfAdults).to.eql(expectedValue.roomStays[0].numberOfAdults);", "     pm.expect(actualValue.roomStays[0].bookingType).to.eql(expectedValue.roomStays[0].bookingType);", "     pm.expect(actualValue.roomStays[0].invTypeCode).to.eql(expectedValue.roomStays[0].invTypeCode);", "     pm.expect(actualValue.roomStays[0].bookedAccomTypeCode).to.eql(expectedValue.roomStays[0].bookedAccomTypeCode);", "     pm.expect(actualValue.roomStays[0].rateCode).to.eql(expectedValue.roomStays[0].rateCode);", "     pm.expect(actualValue.roomStays[0].roomNumber).to.eql(expectedValue.roomStays[0].roomNumber);", "     pm.expect(actualValue.roomStays[0].originalCurrencyCode).to.eql(expectedValue.roomStays[0].originalCurrencyCode);", "     pm.expect(actualValue.roomStays[0].analyticalMarketSegmentCode).to.eql(expectedValue.roomStays[0].analyticalMarketSegmentCode);", "     pm.expect(actualValue.roomStays[0].rates[0].rateValue).to.eql(expectedValue.roomStays[0].rates[0].rateValue);", "     pm.expect(actualValue.roomStays[0].rates[0].startDate).to.eql(expectedValue.roomStays[0].rates[0].startDate);", "     pm.expect(actualValue.roomStays[0].rates[0].endDate).to.eql(expectedValue.roomStays[0].rates[0].endDate);", "     pm.expect(actualValue.roomStays[0].rates[0].grossRate).to.eql(expectedValue.roomStays[0].rates[0].grossRate);", "     pm.expect(actualValue.roomStays[0].services[0].serviceName).to.eql(expectedValue.roomStays[0].services[0].serviceName);", "     pm.expect(actualValue.roomStays[0].services[0].serviceValue).to.eql(expectedValue.roomStays[0].services[0].serviceValue);", "     pm.expect(actualValue.roomStays[0].services[0].inclusive).to.eql(expectedValue.roomStays[0].services[0].inclusive);", "     pm.expect(actualValue.roomStays[0].services[0].roomRevenuePackage).to.eql(expectedValue.roomStays[0].services[0].roomRevenuePackage);", "     pm.expect(actualValue.roomStays[0].services[0].startDate).to.eql(expectedValue.roomStays[0].services[0].startDate);", "     pm.expect(actualValue.roomStays[0].services[0].endDate).to.eql(expectedValue.roomStays[0].services[0].endDate);", "     pm.expect(actualValue.roomStays[0].services[0].reservationPackage).to.eql(expectedValue.roomStays[0].services[0].reservationPackage);", "});}"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["/*var millisecondsToWait = 5000;\r", "setTimeout(function() {\r", "    // Whatever you want to do after the wait\r", "}, millisecondsToWait);*/"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusReservations/search/findByClientCodeAndPropertyCodeAndReservationId?clientCode={{client_Code}}&propertyCode={{property_Code}}&reservationId=458952088", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusReservations", "search", "findByClientCodeAndPropertyCodeAndReservationId"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "reservationId", "value": "458952088"}]}}, "response": []}, {"name": "When Reservation is sent having currency in GBP and Occupancy Date = CurrentDate", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\\\"1.0\\\" encoding=\\\"UTF-8\\\"?>\r\n<?Label SandBox-OPER1|RESERVATION|278719417|SUCCESS|2018060120191231|166/3898?>\r\n<Reservation\r\n    xmlns=\\\"reservation.fidelio.5.0\\\" mfShareAction=\\\"NA\\\" mfReservationAction=\\\"CHECKOUT\\\">\r\n    <HotelReference>\r\n        <hotelCode>SandBox-OPER1</hotelCode>\r\n    </HotelReference>\r\n    <reservationID>458952089</reservationID>\r\n    <reservationOriginatorCode>EUNICE_LAU</reservationOriginatorCode>\r\n    <originalBookingDate>{{todayDate}}T14:56:56.000</originalBookingDate>\r\n    <StayDateRange timeUnitType=\\\"DAY\\\">\r\n        <startTime>{{todayDate}}T17:33:35.000</startTime>\r\n        <numberOfTimeUnits>1</numberOfTimeUnits>\r\n    </StayDateRange>\r\n    <GuestCounts>\r\n        <GuestCount>\r\n            <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n            <mfCount>2</mfCount>\r\n        </GuestCount>\r\n        <GuestCount>\r\n            <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n            <mfCount>2</mfCount>\r\n        </GuestCount>\r\n    </GuestCounts>\r\n    <ResGuests>\r\n        <ResGuest reservationActionType=\\\"SYNC\\\">\r\n            <resGuestRPH>0</resGuestRPH>\r\n            <profileRPHs>0, 1</profileRPHs>\r\n            <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n            <InHouseTimeSpan timeUnitType=\\\"DAY\\\">\r\n                <startTime>{{todayDate}}T17:33:35.000</startTime>\r\n                <numberOfTimeUnits>1</numberOfTimeUnits>\r\n            </InHouseTimeSpan>\r\n            <ArrivalTransport/>\r\n            <DepartureTransport/>\r\n            <arrivalTime>{{todayDate}}T17:33:35.000</arrivalTime>\r\n            <departureTime>2018-08-27T08:34:00.000</departureTime>\r\n            <reservationID>358952088</reservationID>\r\n            <ReservationReferences>\r\n                <ReservationReference type=\\\"GUESTID\\\" referenceNumber=\\\"358952088\\\" legNumber=\\\"1\\\"/>\r\n                <ReservationReference type=\\\"TA_RECORD_LOCATOR\\\" referenceNumber=\\\"1449708029\\\"/>\r\n            </ReservationReferences>\r\n            <preRegistered>0</preRegistered>\r\n            <commissionPaidTo>T</commissionPaidTo>\r\n        </ResGuest>\r\n    </ResGuests>\r\n    <ResProfiles>\r\n        <ResProfile>\r\n            <Profile\r\n                xmlns=\\\"profile.fidelio.5.0\\\" profileType=\\\"GUEST\\\" multiProperty=\\\"0\\\" miniProfile=\\\"0\\\" pseudoProfile=\\\"0\\\" replaceAddress=\\\"0\\\">\r\n                <creatorCode>SARAH_RYAN1</creatorCode>\r\n                <createdDate>2018-07-31T14:56:05.000</createdDate>\r\n                <lastUpdaterCode>BRYONY_MCLANAGH</lastUpdaterCode>\r\n                <lastUpdated>{{todayDate}}T18:33:35.000</lastUpdated>\r\n                <IndividualName>\r\n                    <nameSur/>\r\n                </IndividualName>\r\n                <primaryLanguageID>E</primaryLanguageID>\r\n                <PostalAddresses>\r\n                    <PostalAddress addressType=\\\"HOME\\\">\r\n                        <postalCode>Z894K</postalCode>\r\n                        <countryCode>GB</countryCode>\r\n                        <mfPrimaryYN>Y</mfPrimaryYN>\r\n                        <mfAddressLanguage>E</mfAddressLanguage>\r\n                        <cleansed>0</cleansed>\r\n                    </PostalAddress>\r\n                </PostalAddresses>\r\n                <PhoneNumbers>\r\n                    <PhoneNumber phoneNumberType=\\\"HOME\\\">\r\n                        <mfPrimaryYN>Y</mfPrimaryYN>\r\n                        <confirmation>0</confirmation>\r\n                    </PhoneNumber>\r\n                </PhoneNumbers>\r\n                <mfResort>SandBox-OPER1</mfResort>\r\n                <mfResortProfileID>290184999</mfResortProfileID>\r\n                <mfAllowMail>NO</mfAllowMail>\r\n                <mfAllowEMail>NO</mfAllowEMail>\r\n                <mfGuestPriv>NO</mfGuestPriv>\r\n                <mfAllowPhone>0</mfAllowPhone>\r\n                <mfAllowSMS>0</mfAllowSMS>\r\n                <SalesExtention/>\r\n                <PrivacyOption>\r\n                    <mfAllowMail>N</mfAllowMail>\r\n                    <mfAllowEMail>N</mfAllowEMail>\r\n                    <mfAllowPhone>0</mfAllowPhone>\r\n                    <mfAllowSMS>0</mfAllowSMS>\r\n                    <mfAllowHistory>1</mfAllowHistory>\r\n                    <mfAllowMarketResearch>0</mfAllowMarketResearch>\r\n                    <mfAllowThirdParty>0</mfAllowThirdParty>\r\n                </PrivacyOption>\r\n                <ResortList>SandBox-OPER1</ResortList>\r\n                <MultiResortEntities>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>CREDIT_CARDS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RESORT_ARS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>COMMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>PREFERENCES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>NEGOTIATED_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_NEG_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_COMMISSIONS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>CHANNEL_ACCESS_CODES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>YIELD_ADJUSTMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RELATIONSHIPS</entity>\r\n                    </MultiResortEntity>\r\n                </MultiResortEntities>\r\n            </Profile>\r\n            <resProfileRPH>0</resProfileRPH>\r\n        </ResProfile>\r\n        <ResProfile>\r\n            <Profile\r\n                xmlns=\\\"profile.fidelio.5.0\\\" profileType=\\\"TRAVEL\\\" multiProperty=\\\"0\\\" miniProfile=\\\"0\\\" pseudoProfile=\\\"0\\\" replaceAddress=\\\"0\\\">\r\n                <creatorCode>OXI-OPERA</creatorCode>\r\n                <createdDate>2012-08-10T21:36:40.000</createdDate>\r\n                <lastUpdaterCode>NIKITA_AGGARWAL</lastUpdaterCode>\r\n                <lastUpdated>2018-09-10T17:39:10.000</lastUpdated>\r\n                <genericName>booking.com</genericName>\r\n                <IndividualName>\r\n                    <nameSur/>\r\n                </IndividualName>\r\n                <mfResort>SandBox-OPER1</mfResort>\r\n                <mfResortProfileID>5371710</mfResortProfileID>\r\n                <mfNameCode>96040394</mfNameCode>\r\n                <mfAllowMail>YES</mfAllowMail>\r\n                <mfAllowEMail>NO</mfAllowEMail>\r\n                <mfGuestPriv>NO</mfGuestPriv>\r\n                <mfAllowPhone>0</mfAllowPhone>\r\n                <mfAllowSMS>0</mfAllowSMS>\r\n                <SalesExtention/>\r\n                <PrivacyOption>\r\n                    <mfAllowMail>Y</mfAllowMail>\r\n                    <mfAllowEMail>N</mfAllowEMail>\r\n                    <mfAllowPhone>0</mfAllowPhone>\r\n                    <mfAllowSMS>0</mfAllowSMS>\r\n                    <mfAllowHistory>1</mfAllowHistory>\r\n                    <mfAllowMarketResearch>1</mfAllowMarketResearch>\r\n                    <mfAllowThirdParty>1</mfAllowThirdParty>\r\n                </PrivacyOption>\r\n                <ResortList>SandBox-OPER1</ResortList>\r\n                <MultiResortEntities>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>CREDIT_CARDS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RESORT_ARS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>COMMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>PREFERENCES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>NEGOTIATED_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_NEG_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_COMMISSIONS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>CHANNEL_ACCESS_CODES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>YIELD_ADJUSTMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RELATIONSHIPS</entity>\r\n                    </MultiResortEntity>\r\n                </MultiResortEntities>\r\n            </Profile>\r\n            <resProfileRPH>1</resProfileRPH>\r\n        </ResProfile>\r\n    </ResProfiles>\r\n    <RoomStays>\r\n        <RoomStay mfShareAction=\\\"NA\\\" mfReservationAction=\\\"NA\\\" reservationActionType=\\\"SYNC\\\" reservationStatusType=\\\"CHECKEDOUT\\\">\r\n            <roomInventoryCode>CKC</roomInventoryCode>\r\n            <roomID>311</roomID>\r\n            <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                <startTime>{{todayDate}}T17:33:35.000</startTime>\r\n                <numberOfTimeUnits>1</numberOfTimeUnits>\r\n            </TimeSpan>\r\n            <GuestCounts>\r\n                <GuestCount>\r\n                    <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n                    <mfCount>2</mfCount>\r\n                </GuestCount>\r\n                <GuestCount>\r\n                    <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n                    <mfCount>2</mfCount>\r\n                </GuestCount>\r\n            </GuestCounts>\r\n            <RatePlans>\r\n                <RatePlan reservationActionType=\\\"SYNC\\\">\r\n                    <ratePlanRPH>0</ratePlanRPH>\r\n                    <ratePlanCode>OTAGENA1</ratePlanCode>\r\n                    <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                        <startTime>{{todayDate}}T00:00:00.000</startTime>\r\n                        <numberOfTimeUnits>1</numberOfTimeUnits>\r\n                    </TimeSpan>\r\n                    <mfMarketCode>OTA</mfMarketCode>\r\n                    <Rates>\r\n                        <Rate reservationActionType=\\\"SYNC\\\" rateBasisTimeUnitType=\\\"DAY\\\">\r\n                            <rateRPH>0</rateRPH>\r\n                            <Amount currencyCode=\\\"GBP\\\">\r\n                                <valueNum>169</valueNum>\r\n                            </Amount>\r\n                            <rateBasisUnits>1</rateBasisUnits>\r\n                            <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                                <startTime>{{todayDate}}T00:00:00.000</startTime>\r\n                                <numberOfTimeUnits>1</numberOfTimeUnits>\r\n                            </TimeSpan>\r\n                            <mfAdults>2</mfAdults>\r\n                            <mfChildren>2</mfChildren>\r\n                            <mfCribs>0</mfCribs>\r\n                            <mfExtraBeds>0</mfExtraBeds>\r\n                            <mfsourceCode>BC</mfsourceCode>\r\n                            <mfMarketCode>OTA</mfMarketCode>\r\n                        </Rate>\r\n                    </Rates>\r\n                    <mfsourceCode>BC</mfsourceCode>\r\n                </RatePlan>\r\n            </RatePlans>\r\n            <marketSegmentCode>OTA</marketSegmentCode>\r\n            <resGuestRPHs>0</resGuestRPHs>\r\n            <resCommentRPHs>0</resCommentRPHs>\r\n            <GuaranteeInfo guaranteeType=\\\"NA\\\">\r\n                <mfGuaranteeType>CHECKED IN</mfGuaranteeType>\r\n                <GuaranteeDeposit>\r\n                    <Amount currencyCode=\\\"GBP\\\"/>\r\n                    <cancelIfNotReceived>0</cancelIfNotReceived>\r\n                </GuaranteeDeposit>\r\n            </GuaranteeInfo>\r\n            <CancelPenalties>\r\n                <CancelPenalty mfRuleType=\\\"CANCEL\\\" manualRule=\\\"0\\\">\r\n                    <cancelByDate>{{todayDate}}T14:00:00.000</cancelByDate>\r\n                    <Amount currencyCode=\\\"GBP\\\">\r\n                        <valueNum>169</valueNum>\r\n                    </Amount>\r\n                    <mfRuleScope>R</mfRuleScope>\r\n                    <mfPercentage>100</mfPercentage>\r\n                    <mfCancelPercentDue>100</mfCancelPercentDue>\r\n                    <mfCancelRoomNights>1</mfCancelRoomNights>\r\n                    <mfRuleDescription>Cancel by 2pm on the Day of Arrival</mfRuleDescription>\r\n                    <ruleCode>2PM</ruleCode>\r\n                </CancelPenalty>\r\n            </CancelPenalties>\r\n            <PaymentInstructions>\r\n                <PaymentInstruction paymentMethodType=\\\"NA\\\">\r\n                    <mfPaymentMethod>CA</mfPaymentMethod>\r\n                    <PaymentDue>\r\n                        <Amount currencyCode=\\\"GBP\\\"/>\r\n                        <cancelIfNotReceived>0</cancelIfNotReceived>\r\n                    </PaymentDue>\r\n                </PaymentInstruction>\r\n            </PaymentInstructions>\r\n            <mfcomplementaryCode>FAM</mfcomplementaryCode>\r\n            <mfsourceCode>BC</mfsourceCode>\r\n            <mfchannelCode>OTA</mfchannelCode>\r\n            <mfconfidentialRate>1</mfconfidentialRate>\r\n            <mfAsbProrated>0</mfAsbProrated>\r\n        </RoomStay>\r\n    </RoomStays>\r\n    <resCommentRPHs>0</resCommentRPHs>\r\n    <resProfileRPHs>0, 1</resProfileRPHs>\r\n    <mfupdateDate>2018-08-27T09:34:20.000</mfupdateDate>\r\n    <mfcomplementary>0</mfcomplementary>\r\n    <mfImage>\r\n        <numRooms>1</numRooms>\r\n        <Describe>\r\n            <resortName>Huntingtower Hotel Perth - Boutique by Leonardo</resortName>\r\n            <insertUser>SARAH_RYAN1</insertUser>\r\n            <updateUser>EUNICE_LAU</updateUser>\r\n            <roomCategory>Standard King room with Sofa</roomCategory>\r\n            <rateCode>Flexible Rate Inc Breakfast</rateCode>\r\n            <marketCode>Online Travel Agents</marketCode>\r\n            <guarantee>CHECKED IN: Checked In</guarantee>\r\n            <company>Jurys Inns Group Limited</company>\r\n        </Describe>\r\n        <Change>\r\n            <bArrivalDate>0</bArrivalDate>\r\n            <bNumNights>0</bNumNights>\r\n            <bNumAdults>0</bNumAdults>\r\n            <bNumChildren>0</bNumChildren>\r\n            <bNumRooms>0</bNumRooms>\r\n            <bCribs>0</bCribs>\r\n            <bRoomCategory>0</bRoomCategory>\r\n            <bPaymentType>0</bPaymentType>\r\n            <bGuarType>0</bGuarType>\r\n            <bDiscountReason>0</bDiscountReason>\r\n            <bMultipleRateYN>0</bMultipleRateYN>\r\n            <bResvStatus>0</bResvStatus>\r\n        </Change>\r\n    </mfImage>\r\n    <RateServices>\r\n        <RateService>\r\n            <Service reservationActionType=\\\"SYNC\\\" servicePricingType=\\\"NA\\\" reservationStatusType=\\\"NA\\\">\r\n                <serviceRPH>0</serviceRPH>\r\n                <serviceInventoryCode>BKFS</serviceInventoryCode>\r\n                <ratePlanCode>OTAGENA1</ratePlanCode>\r\n                <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                    <startTime>{{todayDate}}T00:00:00.000</startTime>\r\n                    <numberOfTimeUnits>1</numberOfTimeUnits>\r\n                </TimeSpan>\r\n                <Price currencyCode=\\\"GBP\\\">\r\n                    <valueNum>12</valueNum>\r\n                </Price>\r\n                <quantity>1</quantity>\r\n                <ServicePrices>\r\n                    <ServicePrice>\r\n                        <beginDate>{{todayDate}}</beginDate>\r\n                        <endDate>{{todayDate}}</endDate>\r\n                        <unitPrice>6</unitPrice>\r\n                    </ServicePrice>\r\n                </ServicePrices>\r\n            </Service>\r\n        </RateService>\r\n    </RateServices>\r\n</Reservation>"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/ngipublic/rest/oxi/pms", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["ngipublic", "rest", "oxi", "pms"]}}, "response": []}, {"name": "Then NGI calculates rateValue for Reservation from GBP to USD as Property base currency is USD for present day", "event": [{"listen": "test", "script": {"exec": ["//variables", "var jsonData = pm.response.json();", "", "//value to compare with", "var reservationJSONToBeCompared =JSON.parse('{\"versionId\":278719417,\"reservationId\":\"458952089\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"statisticsCorrelationId\":null,\"bookingDate\":\"'", "+pm.globals.get(\"todayDate\")", "+'T14:56:56.000\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"CHECKED_OUT\",\"nationality\":\"GB\",\"channel\":\"OTA\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"'", "+pm.globals.get(\"todayDate\")", "+'\",\"departureDate\":\"'", "+pm.globals.get(\"tomorrowDate\")", "+'\",\"marketCode\":\"OTA\",\"sourceBookingCode\":\"BC\",\"numberOfChildren\":2,\"numberOfAdults\":2,\"bookingType\":\"CHECKED IN\",\"invTypeCode\":\"CKC\",\"bookedAccomTypeCode\":\"FAM\",\"rateCode\":\"OTAGENA1\",\"invBlockCode\":null,\"roomNumber\":\"311\",\"rates\":[{\"rateValue\":30727.27273,\"startDate\":\"'", "+pm.globals.get(\"todayDate\")", "+'\",\"endDate\":\"'", "+pm.globals.get(\"tomorrowDate\")+'\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":169,\"currencyCode\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"serviceValue\":12,\"inclusive\":true,\"roomRevenuePackage\":true,\"reservationPackage\":false,\"startDate\":\"'", "+pm.globals.get(\"todayDate\")", "+'\",\"endDate\":\"'", "+pm.globals.get(\"tomorrowDate\")", "+'\",\"inclusionType\":null}],\"revenue\":null,\"originalCurrencyCode\":\"GBP\",\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"OTA\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"primary\":null,\"earliestArrivalDate\":\"'", "+pm.globals.get(\"todayDate\")", "+'\",\"latestDepartureDate\":\"'", "+pm.globals.get(\"tomorrowDate\")+'\"}');", "", "//assertions", "assertJSONData(\"Verifying Client Code in OxiMessage\", jsonData,reservationJSONToBeCompared);", "", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue.versionId).to.eql(expectedValue.versionId);", "     pm.expect(actualValue.reservationId).to.eql(expectedValue.reservationId);", "     pm.expect(actualValue.clientCode).to.eql(expectedValue.clientCode);", "     pm.expect(actualValue.propertyCode).to.eql(expectedValue.propertyCode);", "     pm.expect(actualValue.bookingDate).to.eql(expectedValue.bookingDate);", "     pm.expect(actualValue.earliestArrivalDate).to.eql(expectedValue.earliestArrivalDate);", "     pm.expect(actualValue.latestDepartureDate).to.eql(expectedValue.latestDepartureDate);", "     pm.expect(actualValue.status).to.eql(expectedValue.status);", "     pm.expect(actualValue.nationality).to.eql(expectedValue.nationality);", "     pm.expect(actualValue.channel).to.eql(expectedValue.channel);", "     pm.expect(actualValue.roomStays[0].arrivalDate).to.eql(expectedValue.roomStays[0].arrivalDate);", "     pm.expect(actualValue.roomStays[0].departureDate).to.eql(expectedValue.roomStays[0].departureDate);", "     pm.expect(actualValue.roomStays[0].marketCode).to.eql(expectedValue.roomStays[0].marketCode);", "     pm.expect(actualValue.roomStays[0].sourceBookingCode).to.eql(expectedValue.roomStays[0].sourceBookingCode);", "     pm.expect(actualValue.roomStays[0].numberOfChildren).to.eql(expectedValue.roomStays[0].numberOfChildren);", "     pm.expect(actualValue.roomStays[0].numberOfAdults).to.eql(expectedValue.roomStays[0].numberOfAdults);", "     pm.expect(actualValue.roomStays[0].bookingType).to.eql(expectedValue.roomStays[0].bookingType);", "     pm.expect(actualValue.roomStays[0].invTypeCode).to.eql(expectedValue.roomStays[0].invTypeCode);", "     pm.expect(actualValue.roomStays[0].bookedAccomTypeCode).to.eql(expectedValue.roomStays[0].bookedAccomTypeCode);", "     pm.expect(actualValue.roomStays[0].rateCode).to.eql(expectedValue.roomStays[0].rateCode);", "     pm.expect(actualValue.roomStays[0].roomNumber).to.eql(expectedValue.roomStays[0].roomNumber);", "     pm.expect(actualValue.roomStays[0].originalCurrencyCode).to.eql(expectedValue.roomStays[0].originalCurrencyCode);", "     pm.expect(actualValue.roomStays[0].analyticalMarketSegmentCode).to.eql(expectedValue.roomStays[0].analyticalMarketSegmentCode);", "     pm.expect(actualValue.roomStays[0].rates[0].rateValue).to.eql(expectedValue.roomStays[0].rates[0].rateValue);", "     pm.expect(actualValue.roomStays[0].rates[0].startDate).to.eql(expectedValue.roomStays[0].rates[0].startDate);", "     pm.expect(actualValue.roomStays[0].rates[0].endDate).to.eql(expectedValue.roomStays[0].rates[0].endDate);", "     pm.expect(actualValue.roomStays[0].rates[0].grossRate).to.eql(expectedValue.roomStays[0].rates[0].grossRate);", "     pm.expect(actualValue.roomStays[0].services[0].serviceName).to.eql(expectedValue.roomStays[0].services[0].serviceName);", "     pm.expect(actualValue.roomStays[0].services[0].serviceValue).to.eql(expectedValue.roomStays[0].services[0].serviceValue);", "     pm.expect(actualValue.roomStays[0].services[0].inclusive).to.eql(expectedValue.roomStays[0].services[0].inclusive);", "     pm.expect(actualValue.roomStays[0].services[0].roomRevenuePackage).to.eql(expectedValue.roomStays[0].services[0].roomRevenuePackage);", "     pm.expect(actualValue.roomStays[0].services[0].startDate).to.eql(expectedValue.roomStays[0].services[0].startDate);", "     pm.expect(actualValue.roomStays[0].services[0].endDate).to.eql(expectedValue.roomStays[0].services[0].endDate);", "     pm.expect(actualValue.roomStays[0].services[0].reservationPackage).to.eql(expectedValue.roomStays[0].services[0].reservationPackage);", "});}"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["/*var millisecondsToWait = 5000;\r", "setTimeout(function() {\r", "    // Whatever you want to do after the wait\r", "}, millisecondsToWait);*/"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusReservations/search/findByClientCodeAndPropertyCodeAndReservationId?clientCode={{client_Code}}&propertyCode={{property_Code}}&reservationId=458952089", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusReservations", "search", "findByClientCodeAndPropertyCodeAndReservationId"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "reservationId", "value": "458952089"}]}}, "response": []}, {"name": "When Reservation is sent having currency in GBP and Occupancy Date  < CurrentDate", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\\\"1.0\\\" encoding=\\\"UTF-8\\\"?>\r\n<?Label SandBox-OPER1|RESERVATION|278719418|SUCCESS|2018060120191231|166/3898?>\r\n<Reservation\r\n    xmlns=\\\"reservation.fidelio.5.0\\\" mfShareAction=\\\"NA\\\" mfReservationAction=\\\"CHECKOUT\\\">\r\n    <HotelReference>\r\n        <hotelCode>SandBox-OPER1</hotelCode>\r\n    </HotelReference>\r\n    <reservationID>458952090</reservationID>\r\n    <reservationOriginatorCode>EUNICE_LAU</reservationOriginatorCode>\r\n    <originalBookingDate>2019-01-22T14:56:56.000</originalBookingDate>\r\n    <StayDateRange timeUnitType=\\\"DAY\\\">\r\n        <startTime>2018-07-26T17:33:35.000</startTime>\r\n        <numberOfTimeUnits>1</numberOfTimeUnits>\r\n    </StayDateRange>\r\n    <GuestCounts>\r\n        <GuestCount>\r\n            <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n            <mfCount>2</mfCount>\r\n        </GuestCount>\r\n        <GuestCount>\r\n            <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n            <mfCount>2</mfCount>\r\n        </GuestCount>\r\n    </GuestCounts>\r\n    <ResGuests>\r\n        <ResGuest reservationActionType=\\\"SYNC\\\">\r\n            <resGuestRPH>0</resGuestRPH>\r\n            <profileRPHs>0, 1</profileRPHs>\r\n            <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n            <InHouseTimeSpan timeUnitType=\\\"DAY\\\">\r\n                <startTime>2018-07-26T17:33:35.000</startTime>\r\n                <numberOfTimeUnits>1</numberOfTimeUnits>\r\n            </InHouseTimeSpan>\r\n            <ArrivalTransport/>\r\n            <DepartureTransport/>\r\n            <arrivalTime>2018-07-26T17:33:35.000</arrivalTime>\r\n            <departureTime>2018-07-27T08:34:00.000</departureTime>\r\n            <reservationID>458952090</reservationID>\r\n            <ReservationReferences>\r\n                <ReservationReference type=\\\"GUESTID\\\" referenceNumber=\\\"458952090\\\" legNumber=\\\"1\\\"/>\r\n                <ReservationReference type=\\\"TA_RECORD_LOCATOR\\\" referenceNumber=\\\"1449708029\\\"/>\r\n            </ReservationReferences>\r\n            <preRegistered>0</preRegistered>\r\n            <commissionPaidTo>T</commissionPaidTo>\r\n        </ResGuest>\r\n    </ResGuests>\r\n    <ResProfiles>\r\n        <ResProfile>\r\n            <Profile\r\n                xmlns=\\\"profile.fidelio.5.0\\\" profileType=\\\"GUEST\\\" multiProperty=\\\"0\\\" miniProfile=\\\"0\\\" pseudoProfile=\\\"0\\\" replaceAddress=\\\"0\\\">\r\n                <creatorCode>SARAH_RYAN1</creatorCode>\r\n                <createdDate>2019-01-22T14:56:05.000</createdDate>\r\n                <lastUpdaterCode>BRYONY_MCLANAGH</lastUpdaterCode>\r\n                <lastUpdated>2018-07-26T18:33:35.000</lastUpdated>\r\n                <IndividualName>\r\n                    <nameSur/>\r\n                </IndividualName>\r\n                <primaryLanguageID>E</primaryLanguageID>\r\n                <PostalAddresses>\r\n                    <PostalAddress addressType=\\\"HOME\\\">\r\n                        <postalCode>Z894K</postalCode>\r\n                        <countryCode>GB</countryCode>\r\n                        <mfPrimaryYN>Y</mfPrimaryYN>\r\n                        <mfAddressLanguage>E</mfAddressLanguage>\r\n                        <cleansed>0</cleansed>\r\n                    </PostalAddress>\r\n                </PostalAddresses>\r\n                <PhoneNumbers>\r\n                    <PhoneNumber phoneNumberType=\\\"HOME\\\">\r\n                        <mfPrimaryYN>Y</mfPrimaryYN>\r\n                        <confirmation>0</confirmation>\r\n                    </PhoneNumber>\r\n                </PhoneNumbers>\r\n                <mfResort>SandBox-OPER1</mfResort>\r\n                <mfResortProfileID>290184999</mfResortProfileID>\r\n                <mfAllowMail>NO</mfAllowMail>\r\n                <mfAllowEMail>NO</mfAllowEMail>\r\n                <mfGuestPriv>NO</mfGuestPriv>\r\n                <mfAllowPhone>0</mfAllowPhone>\r\n                <mfAllowSMS>0</mfAllowSMS>\r\n                <SalesExtention/>\r\n                <PrivacyOption>\r\n                    <mfAllowMail>N</mfAllowMail>\r\n                    <mfAllowEMail>N</mfAllowEMail>\r\n                    <mfAllowPhone>0</mfAllowPhone>\r\n                    <mfAllowSMS>0</mfAllowSMS>\r\n                    <mfAllowHistory>1</mfAllowHistory>\r\n                    <mfAllowMarketResearch>0</mfAllowMarketResearch>\r\n                    <mfAllowThirdParty>0</mfAllowThirdParty>\r\n                </PrivacyOption>\r\n                <ResortList>SandBox-OPER1</ResortList>\r\n                <MultiResortEntities>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>CREDIT_CARDS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RESORT_ARS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>COMMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>PREFERENCES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>NEGOTIATED_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_NEG_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_COMMISSIONS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>CHANNEL_ACCESS_CODES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>YIELD_ADJUSTMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RELATIONSHIPS</entity>\r\n                    </MultiResortEntity>\r\n                </MultiResortEntities>\r\n            </Profile>\r\n            <resProfileRPH>0</resProfileRPH>\r\n        </ResProfile>\r\n        <ResProfile>\r\n            <Profile\r\n                xmlns=\\\"profile.fidelio.5.0\\\" profileType=\\\"TRAVEL\\\" multiProperty=\\\"0\\\" miniProfile=\\\"0\\\" pseudoProfile=\\\"0\\\" replaceAddress=\\\"0\\\">\r\n                <creatorCode>OXI-OPERA</creatorCode>\r\n                <createdDate>2012-08-10T21:36:40.000</createdDate>\r\n                <lastUpdaterCode>NIKITA_AGGARWAL</lastUpdaterCode>\r\n                <lastUpdated>2018-09-10T17:39:10.000</lastUpdated>\r\n                <genericName>booking.com</genericName>\r\n                <IndividualName>\r\n                    <nameSur/>\r\n                </IndividualName>\r\n                <mfResort>SandBox-OPER1</mfResort>\r\n                <mfResortProfileID>5371710</mfResortProfileID>\r\n                <mfNameCode>96040394</mfNameCode>\r\n                <mfAllowMail>YES</mfAllowMail>\r\n                <mfAllowEMail>NO</mfAllowEMail>\r\n                <mfGuestPriv>NO</mfGuestPriv>\r\n                <mfAllowPhone>0</mfAllowPhone>\r\n                <mfAllowSMS>0</mfAllowSMS>\r\n                <SalesExtention/>\r\n                <PrivacyOption>\r\n                    <mfAllowMail>Y</mfAllowMail>\r\n                    <mfAllowEMail>N</mfAllowEMail>\r\n                    <mfAllowPhone>0</mfAllowPhone>\r\n                    <mfAllowSMS>0</mfAllowSMS>\r\n                    <mfAllowHistory>1</mfAllowHistory>\r\n                    <mfAllowMarketResearch>1</mfAllowMarketResearch>\r\n                    <mfAllowThirdParty>1</mfAllowThirdParty>\r\n                </PrivacyOption>\r\n                <ResortList>SandBox-OPER1</ResortList>\r\n                <MultiResortEntities>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>CREDIT_CARDS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RESORT_ARS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>COMMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>PREFERENCES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>NEGOTIATED_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_NEG_RATES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>RESORT_COMMISSIONS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>CHANNEL_ACCESS_CODES</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"1\\\">\r\n                        <entity>YIELD_ADJUSTMENTS</entity>\r\n                    </MultiResortEntity>\r\n                    <MultiResortEntity included=\\\"0\\\">\r\n                        <entity>RELATIONSHIPS</entity>\r\n                    </MultiResortEntity>\r\n                </MultiResortEntities>\r\n            </Profile>\r\n            <resProfileRPH>1</resProfileRPH>\r\n        </ResProfile>\r\n    </ResProfiles>\r\n    <RoomStays>\r\n        <RoomStay mfShareAction=\\\"NA\\\" mfReservationAction=\\\"NA\\\" reservationActionType=\\\"SYNC\\\" reservationStatusType=\\\"CHECKEDOUT\\\">\r\n            <roomInventoryCode>CKC</roomInventoryCode>\r\n            <roomID>311</roomID>\r\n            <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                <startTime>2018-07-26T17:33:35.000</startTime>\r\n                <numberOfTimeUnits>1</numberOfTimeUnits>\r\n            </TimeSpan>\r\n            <GuestCounts>\r\n                <GuestCount>\r\n                    <ageQualifyingCode>ADULT</ageQualifyingCode>\r\n                    <mfCount>2</mfCount>\r\n                </GuestCount>\r\n                <GuestCount>\r\n                    <ageQualifyingCode>CHILD</ageQualifyingCode>\r\n                    <mfCount>2</mfCount>\r\n                </GuestCount>\r\n            </GuestCounts>\r\n            <RatePlans>\r\n                <RatePlan reservationActionType=\\\"SYNC\\\">\r\n                    <ratePlanRPH>0</ratePlanRPH>\r\n                    <ratePlanCode>OTAGENA1</ratePlanCode>\r\n                    <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                        <startTime>2018-07-26T00:00:00.000</startTime>\r\n                        <numberOfTimeUnits>1</numberOfTimeUnits>\r\n                    </TimeSpan>\r\n                    <mfMarketCode>OTA</mfMarketCode>\r\n                    <Rates>\r\n                        <Rate reservationActionType=\\\"SYNC\\\" rateBasisTimeUnitType=\\\"DAY\\\">\r\n                            <rateRPH>0</rateRPH>\r\n                            <Amount currencyCode=\\\"GBP\\\">\r\n                                <valueNum>169</valueNum>\r\n                            </Amount>\r\n                            <rateBasisUnits>1</rateBasisUnits>\r\n                            <TimeSpan timeUnitType=\\\"DAY\\\">\r\n                                <startTime>2018-07-26T00:00:00.000</startTime>\r\n                                <numberOfTimeUnits>1</numberOfTimeUnits>\r\n                            </TimeSpan>\r\n                            <mfAdults>2</mfAdults>\r\n                            <mfChildren>2</mfChildren>\r\n                            <mfCribs>0</mfCribs>\r\n                            <mfExtraBeds>0</mfExtraBeds>\r\n                            <mfsourceCode>BC</mfsourceCode>\r\n                            <mfMarketCode>OTA</mfMarketCode>\r\n                        </Rate>\r\n                    </Rates>\r\n                    <mfsourceCode>BC</mfsourceCode>\r\n                </RatePlan>\r\n            </RatePlans>\r\n            <marketSegmentCode>OTA</marketSegmentCode>\r\n            <resGuestRPHs>0</resGuestRPHs>\r\n            <resCommentRPHs>0</resCommentRPHs>\r\n            <GuaranteeInfo guaranteeType=\\\"NA\\\">\r\n                <mfGuaranteeType>CHECKED IN</mfGuaranteeType>\r\n                <GuaranteeDeposit>\r\n                    <Amount currencyCode=\\\"GBP\\\"/>\r\n                    <cancelIfNotReceived>0</cancelIfNotReceived>\r\n                </GuaranteeDeposit>\r\n            </GuaranteeInfo>\r\n            <CancelPenalties>\r\n                <CancelPenalty mfRuleType=\\\"CANCEL\\\" manualRule=\\\"0\\\">\r\n                    <cancelByDate>2018-07-26T14:00:00.000</cancelByDate>\r\n                    <Amount currencyCode=\\\"GBP\\\">\r\n                        <valueNum>169</valueNum>\r\n                    </Amount>\r\n                    <mfRuleScope>R</mfRuleScope>\r\n                    <mfPercentage>100</mfPercentage>\r\n                    <mfCancelPercentDue>100</mfCancelPercentDue>\r\n                    <mfCancelRoomNights>1</mfCancelRoomNights>\r\n                    <mfRuleDescription>Cancel by 2pm on the Day of Arrival</mfRuleDescription>\r\n                    <ruleCode>2PM</ruleCode>\r\n                </CancelPenalty>\r\n            </CancelPenalties>\r\n            <PaymentInstructions>\r\n                <PaymentInstruction paymentMethodType=\\\"NA\\\">\r\n                    <mfPaymentMethod>CA</mfPaymentMethod>\r\n                    <PaymentDue>\r\n                        <Amount currencyCode=\\\"GBP\\\"/>\r\n                        <cancelIfNotReceived>0</cancelIfNotReceived>\r\n                    </PaymentDue>\r\n                </PaymentInstruction>\r\n            </PaymentInstructions>\r\n            <mfcomplementaryCode>FAM</mfcomplementaryCode>\r\n            <mfsourceCode>BC</mfsourceCode>\r\n            <mfchannelCode>OTA</mfchannelCode>\r\n            <mfconfidentialRate>1</mfconfidentialRate>\r\n            <mfAsbProrated>0</mfAsbProrated>\r\n        </RoomStay>\r\n    </RoomStays>\r\n    <resCommentRPHs>0</resCommentRPHs>\r\n    <resProfileRPHs>0, 1</resProfileRPHs>\r\n    <mfupdateDate>2018-07-27T09:34:20.000</mfupdateDate>\r\n    <mfcomplementary>0</mfcomplementary>\r\n    <mfImage>\r\n\t<numRooms>1</numRooms>\r\n        <Describe>\r\n            <resortName>Huntingtower Hotel Perth - Boutique by Leonardo</resortName>\r\n            <insertUser>SARAH_RYAN1</insertUser>\r\n            <updateUser>EUNICE_LAU</updateUser>\r\n            <roomCategory>Standard King room with Sofa</roomCategory>\r\n            <rateCode>Flexible Rate Inc Breakfast</rateCode>\r\n            <marketCode>Online Travel Agents</marketCode>\r\n            <guarantee>CHECKED IN: Checked In</guarantee>\r\n            <company>Jurys Inns Group Limited</company>\r\n        </Describe>\r\n        <Change>\r\n            <bArrivalDate>0</bArrivalDate>\r\n            <bNumNights>0</bNumNights>\r\n            <bNumAdults>0</bNumAdults>\r\n            <bNumChildren>0</bNumChildren>\r\n            <bNumRooms>0</bNumRooms>\r\n            <bCribs>0</bCribs>\r\n            <bRoomCategory>0</bRoomCategory>\r\n            <bPaymentType>0</bPaymentType>\r\n            <bGuarType>0</bGuarType>\r\n            <bDiscountReason>0</bDiscountReason>\r\n            <bMultipleRateYN>0</bMultipleRateYN>\r\n            <bResvStatus>0</bResvStatus>\r\n        </Change>\r\n    </mfImage>\r\n</Reservation>"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/ngipublic/rest/oxi/pms", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["ngipublic", "rest", "oxi", "pms"]}}, "response": []}, {"name": "Then NGI calculates rateValue for Reservation from GBP to USD as Property base currency is USD for future", "event": [{"listen": "test", "script": {"exec": ["//variables", "var jsonData = pm.response.json();", "", "//value to compare with", "var reservationJSONToBeCompared ={\"versionId\":278719418,\"reservationId\":\"458952090\",\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"statisticsCorrelationId\":null,\"bookingDate\":\"2019-01-22T14:56:56.000\",\"cancellationDate\":null,\"cancellationReason\":null,\"cancellationId\":null,\"status\":\"CHECKED_OUT\",\"nationality\":\"GB\",\"channel\":\"OTA\",\"confirmationNumber\":null,\"customerValue\":null,\"roomStays\":[{\"arrivalDate\":\"2018-07-26\",\"departureDate\":\"2018-07-27\",\"marketCode\":\"OTA\",\"sourceBookingCode\":\"BC\",\"numberOfChildren\":2,\"numberOfAdults\":2,\"bookingType\":\"CHECKED IN\",\"invTypeCode\":\"CKC\",\"bookedAccomTypeCode\":\"FAM\",\"rateCode\":\"OTAGENA1\",\"invBlockCode\":null,\"roomNumber\":\"311\",\"rates\":[{\"rateValue\":15363.63636,\"startDate\":\"2018-07-26\",\"endDate\":\"2018-07-27\",\"originalCurrencyCode\":null,\"currencyExchangeId\":null,\"grossRate\":169,\"currencyCode\":null}],\"services\":[],\"revenue\":null,\"originalCurrencyCode\":\"GBP\",\"fromPrimary\":false,\"analyticalMarketSegmentCode\":\"OTA\"}],\"multiReservationId\":null,\"tempLookupId\":null,\"numberOfRooms\":1,\"sharers\":[],\"stayRevenue\":null,\"previousSharers\":null,\"primary\":null,\"earliestArrivalDate\":\"2018-07-26\",\"latestDepartureDate\":\"2018-07-27\"};", "", "//assertions", "assertJSONData(\"Verifying Client Code in OxiMessage\", jsonData,reservationJSONToBeCompared);", "", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue.versionId).to.eql(expectedValue.versionId);", "     pm.expect(actualValue.reservationId).to.eql(expectedValue.reservationId);", "     pm.expect(actualValue.clientCode).to.eql(expectedValue.clientCode);", "     pm.expect(actualValue.propertyCode).to.eql(expectedValue.propertyCode);", "     pm.expect(actualValue.bookingDate).to.eql(expectedValue.bookingDate);", "     pm.expect(actualValue.earliestArrivalDate).to.eql(expectedValue.earliestArrivalDate);", "     pm.expect(actualValue.latestDepartureDate).to.eql(expectedValue.latestDepartureDate);", "     pm.expect(actualValue.status).to.eql(expectedValue.status);", "     pm.expect(actualValue.nationality).to.eql(expectedValue.nationality);", "     pm.expect(actualValue.channel).to.eql(expectedValue.channel);", "     pm.expect(actualValue.roomStays[0].arrivalDate).to.eql(expectedValue.roomStays[0].arrivalDate);", "     pm.expect(actualValue.roomStays[0].departureDate).to.eql(expectedValue.roomStays[0].departureDate);", "     pm.expect(actualValue.roomStays[0].marketCode).to.eql(expectedValue.roomStays[0].marketCode);", "     pm.expect(actualValue.roomStays[0].sourceBookingCode).to.eql(expectedValue.roomStays[0].sourceBookingCode);", "     pm.expect(actualValue.roomStays[0].numberOfChildren).to.eql(expectedValue.roomStays[0].numberOfChildren);", "     pm.expect(actualValue.roomStays[0].numberOfAdults).to.eql(expectedValue.roomStays[0].numberOfAdults);", "     pm.expect(actualValue.roomStays[0].bookingType).to.eql(expectedValue.roomStays[0].bookingType);", "     pm.expect(actualValue.roomStays[0].invTypeCode).to.eql(expectedValue.roomStays[0].invTypeCode);", "     pm.expect(actualValue.roomStays[0].bookedAccomTypeCode).to.eql(expectedValue.roomStays[0].bookedAccomTypeCode);", "     pm.expect(actualValue.roomStays[0].rateCode).to.eql(expectedValue.roomStays[0].rateCode);", "     pm.expect(actualValue.roomStays[0].roomNumber).to.eql(expectedValue.roomStays[0].roomNumber);", "     pm.expect(actualValue.roomStays[0].originalCurrencyCode).to.eql(expectedValue.roomStays[0].originalCurrencyCode);", "     pm.expect(actualValue.roomStays[0].analyticalMarketSegmentCode).to.eql(expectedValue.roomStays[0].analyticalMarketSegmentCode);", "     pm.expect(actualValue.roomStays[0].rates[0].rateValue).to.eql(expectedValue.roomStays[0].rates[0].rateValue);", "     pm.expect(actualValue.roomStays[0].rates[0].startDate).to.eql(expectedValue.roomStays[0].rates[0].startDate);", "     pm.expect(actualValue.roomStays[0].rates[0].endDate).to.eql(expectedValue.roomStays[0].rates[0].endDate);", "     pm.expect(actualValue.roomStays[0].rates[0].grossRate).to.eql(expectedValue.roomStays[0].rates[0].grossRate);", "});}"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["/*var millisecondsToWait = 5000;\r", "setTimeout(function() {\r", "    // Whatever you want to do after the wait\r", "}, millisecondsToWait);*/"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusReservations/search/findByClientCodeAndPropertyCodeAndReservationId?clientCode={{client_Code}}&propertyCode={{property_Code}}&reservationId=458952090", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusReservations", "search", "findByClientCodeAndPropertyCodeAndReservationId"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "reservationId", "value": "458952090"}]}}, "response": []}, {"name": "When a qualified rate message is sent to NGI which is before currentDate", "event": [{"listen": "test", "script": {"exec": ["//verifing status", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\\\"1.0\\\" encoding=\\\"UTF-8\\\"?>\r\n<?Label SandBox-OPER1|RATE|*********|SUCCESS?>\r\n<RateHeader\r\n    xmlns=\\\"rate.fidelio.6.0\\\" rateStatus=\\\"ACTIVE\\\" taxIncluded=\\\"YES\\\" yieldStatus=\\\"FULLY\\\" sellSequence=\\\"150\\\" rateMessageType=\\\"HEADERWITHDETAIL\\\">\r\n    <hotelCode>SandBox-OPER1</hotelCode>\r\n    <RateCategory>\r\n        <catDescription>OTA Rate Codes</catDescription>\r\n        <category>OTA</category>\r\n    </RateCategory>\r\n    <rateCode>OTAGENA1_GBP_USD</rateCode>\r\n    <startSell>2018-07-01</startSell>\r\n    <endSell>2019-12-31</endSell>\r\n    <advancedBookingDays>0</advancedBookingDays>\r\n    <displayText>Flexible Rate Inc Breakfast</displayText>\r\n    <folioText>Accommodation BB</folioText>\r\n    <longInfo>Flexible Rate Inc Breakfast</longInfo>\r\n    <shortInfo>Flexible Rate Inc Breakfast2pm CLX</shortInfo>\r\n    <minLos>1</minLos>\r\n    <maxLos>99</maxLos>\r\n    <packages>BKFS</packages>\r\n    <marketCode>OTA</marketCode>\r\n    <foreignCurrencyDepartmentCode>GBP</foreignCurrencyDepartmentCode>\r\n    <departmentCode>1000</departmentCode>\r\n    <confidential>N</confidential>\r\n    <printRate>0</printRate>\r\n    <rateTiered>0</rateTiered>\r\n    <negotiated>1</negotiated>\r\n    <dayUse>0</dayUse>\r\n    <complimentary>0</complimentary>\r\n    <houseUse>0</houseUse>\r\n    <dayType>0</dayType>\r\n    <discount>0</discount>\r\n    <packageTxnCode>7500</packageTxnCode>\r\n    <RateDetails>\r\n        <RateDetail>\r\n            <rateCode>OTAGENA1_GBP_USD</rateCode>\r\n            <startDate>2018-06-01</startDate>\r\n            <endDate>2018-06-30</endDate>\r\n            <roomTypeList>EKB</roomTypeList>\r\n            <roomTypeList>FAM</roomTypeList>\r\n            <roomTypeList>CTB</roomTypeList>\r\n            <roomTypeList>CKC</roomTypeList>\r\n            <roomTypeList>C1D</roomTypeList>\r\n            <roomTypeList>SKC</roomTypeList>\r\n            <rate1>500</rate1>\r\n            <rate2>500</rate2>\r\n            <rate3>500</rate3>\r\n            <RateDaysOfWeek>\r\n                <monday>1</monday>\r\n                <tuesday>1</tuesday>\r\n                <wednesday>1</wednesday>\r\n                <thursday>1</thursday>\r\n                <friday>1</friday>\r\n                <saturday>1</saturday>\r\n                <sunday>1</sunday>\r\n            </RateDaysOfWeek>\r\n            <freeChildren>1</freeChildren>\r\n            <childExcThreshold1>0</childExcThreshold1>\r\n            <childExcThreshold2>0</childExcThreshold2>\r\n            <childExcThreshold3>0</childExcThreshold3>\r\n            <bar>0</bar>\r\n            <barFltPct>FLT</barFltPct>\r\n            <barRounding>N</barRounding>\r\n        </RateDetail>\r\n        <RateDetail>\r\n            <rateCode>OTAGENA1_GBP_USD</rateCode>\r\n            <startDate>2018-07-01</startDate>\r\n            <endDate>2019-12-31</endDate>\r\n            <roomTypeList>PM</roomTypeList>\r\n            <roomTypeList>PF</roomTypeList>\r\n            <roomTypeList>PI</roomTypeList>\r\n            <rate1>200</rate1>\r\n            <rate2>200</rate2>\r\n            <rate3>200</rate3>\r\n            <RateDaysOfWeek>\r\n                <monday>1</monday>\r\n                <tuesday>1</tuesday>\r\n                <wednesday>1</wednesday>\r\n                <thursday>1</thursday>\r\n                <friday>1</friday>\r\n                <saturday>1</saturday>\r\n                <sunday>1</sunday>\r\n            </RateDaysOfWeek>\r\n            <freeChildren>1</freeChildren>\r\n            <childExcThreshold1>0</childExcThreshold1>\r\n            <childExcThreshold2>0</childExcThreshold2>\r\n            <childExcThreshold3>0</childExcThreshold3>\r\n            <bar>0</bar>\r\n            <barFltPct>FLT</barFltPct>\r\n            <barRounding>N</barRounding>\r\n        </RateDetail>\r\n    </RateDetails>\r\n    <gdsAllowed>1</gdsAllowed>\r\n    <RatePackages>\r\n        <RatePackage>\r\n            <Package\r\n                xmlns=\\\"packages.fidelio.3.0\\\" calculationRule=\\\"A\\\" postingRhythm=\\\"D\\\" packageElement=\\\"ELEMENT\\\" addToRateMode=\\\"INCLUDE\\\">\r\n                <hotelCode>SandBox-OPER1</hotelCode>\r\n                <packageCode>BKFS</packageCode>\r\n                <packageDescription>Breakfast</packageDescription>\r\n                <printSeperately>0</printSeperately>\r\n                <addToRate>0</addToRate>\r\n                <taxIncluded>1</taxIncluded>\r\n                <allowance>0</allowance>\r\n                <currencyCode>GBP</currencyCode>\r\n                <sellSeparate>0</sellSeparate>\r\n                <postNextDay>0</postNextDay>\r\n                <cateringPackage>0</cateringPackage>\r\n                <forecastNextDay>1</forecastNextDay>\r\n                <overrideFixedRate>0</overrideFixedRate>\r\n                <containsInactiveDetails>1</containsInactiveDetails>\r\n            </Package>\r\n        </RatePackage>\r\n    </RatePackages>\r\n    <channelRestriction>1</channelRestriction>\r\n    <dailyRate>0</dailyRate>\r\n    <rod>0</rod>\r\n    <rodBased>0</rodBased>\r\n    <bbar>0</bbar>\r\n    <bbarBased>0</bbarBased>\r\n    <roomTypeList>PM</roomTypeList>\r\n    <roomTypeList>PF</roomTypeList>\r\n    <roomTypeList>PI</roomTypeList>\r\n    <roomTypeList>C1D</roomTypeList>\r\n    <roomTypeList>CTB</roomTypeList>\r\n    <roomTypeList>CKC</roomTypeList>\r\n    <roomTypeList>SKC</roomTypeList>\r\n    <roomTypeList>EKB</roomTypeList>\r\n    <roomTypeList>FAM</roomTypeList>\r\n    <mfnUpload>1</mfnUpload>\r\n    <membership>0</membership>\r\n    <exchangePostingType>P</exchangePostingType>\r\n    <bbarCompare>0</bbarCompare>\r\n    <fullDailyRateDetails>0</fullDailyRateDetails>\r\n    <redemption>0</redemption>\r\n    <apartmentBillingCycle>NA</apartmentBillingCycle>\r\n    <advBaseCompare>0</advBaseCompare>\r\n    <defaultToHighestBar>0</defaultToHighestBar>\r\n    <repeatPostingRhythm>1</repeatPostingRhythm>\r\n    <discountRatePercentage>0</discountRatePercentage>\r\n</RateHeader>"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/ngipublic/rest/oxi/pms", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["ngipublic", "rest", "oxi", "pms"]}}, "response": []}, {"name": "Then rate message is processed at NGI and saved in oxiMessages collection for past", "event": [{"listen": "test", "script": {"exec": ["//variables", "var jsonData = pm.response.json();", "var allRates =  jsonData.rates;", "", "", "console.log(jsonData);", "", "", "//value to compare with", "var reservationJSONToBeCompared ={\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"QUALIFIED_NEGOTIATED\",\"name\":\"OTAGENA1_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2018-07-01\",\"endDate\":\"2019-12-31\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"EKB\",\"originalCurrencyCode\":null,\"startDate\":\"2018-06-01\",\"endDate\":\"2018-06-30\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"referenceRateCode\":\"None\",\"qualified\":true};", "", "var reservationJSONToBeCompared1 ={\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"QUALIFIED_NEGOTIATED\",\"name\":\"OTAGENA1_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2018-07-01\",\"endDate\":\"2019-12-31\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"FAM\",\"originalCurrencyCode\":null,\"startDate\":\"2018-06-01\",\"endDate\":\"2018-06-30\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"referenceRateCode\":\"None\",\"qualified\":true};", "", "var reservationJSONToBeCompared2 ={\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"QUALIFIED_NEGOTIATED\",\"name\":\"OTAGENA1_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2018-07-01\",\"endDate\":\"2019-12-31\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"CTB\",\"originalCurrencyCode\":null,\"startDate\":\"2018-06-01\",\"endDate\":\"2018-06-30\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"referenceRateCode\":\"None\",\"qualified\":true};", "", "var reservationJSONToBeCompared3 ={\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"QUALIFIED_NEGOTIATED\",\"name\":\"OTAGENA1_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2018-07-01\",\"endDate\":\"2019-12-31\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"CKC\",\"originalCurrencyCode\":null,\"startDate\":\"2018-06-01\",\"endDate\":\"2018-06-30\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"referenceRateCode\":\"None\",\"qualified\":true};", "", "var reservationJSONToBeCompared4 ={\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"QUALIFIED_NEGOTIATED\",\"name\":\"OTAGENA1_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2018-07-01\",\"endDate\":\"2019-12-31\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"C1D\",\"originalCurrencyCode\":null,\"startDate\":\"2018-06-01\",\"endDate\":\"2018-06-30\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"referenceRateCode\":\"None\",\"qualified\":true};", "", "var reservationJSONToBeCompared5 ={\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"QUALIFIED_NEGOTIATED\",\"name\":\"OTAGENA1_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2018-07-01\",\"endDate\":\"2019-12-31\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"SKC\",\"originalCurrencyCode\":null,\"startDate\":\"2018-06-01\",\"endDate\":\"2018-06-30\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"referenceRateCode\":\"None\",\"qualified\":true};", "", "var reservationJSONToBeCompared6 ={\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"QUALIFIED_NEGOTIATED\",\"name\":\"OTAGENA1_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2018-07-01\",\"endDate\":\"2019-12-31\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"PM\",\"originalCurrencyCode\":null,\"startDate\":\"2018-07-01\",\"endDate\":\"2019-12-31\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"referenceRateCode\":\"None\",\"qualified\":true};", "", "var reservationJSONToBeCompared7 ={\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"QUALIFIED_NEGOTIATED\",\"name\":\"OTAGENA1_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2018-07-01\",\"endDate\":\"2019-12-31\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"PF\",\"originalCurrencyCode\":null,\"startDate\":\"2018-07-01\",\"endDate\":\"2019-12-31\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"referenceRateCode\":\"None\",\"qualified\":true};", "", "var reservationJSONToBeCompared8 ={\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"QUALIFIED_NEGOTIATED\",\"name\":\"OTAGENA1_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2018-07-01\",\"endDate\":\"2019-12-31\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"PI\",\"originalCurrencyCode\":null,\"startDate\":\"2018-07-01\",\"endDate\":\"2019-12-31\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"referenceRateCode\":\"None\",\"qualified\":true};", "", "arr=[];", "", "arr.push(reservationJSONToBeCompared,reservationJSONToBeCompared1,reservationJSONToBeCompared2,reservationJSONToBeCompared3,reservationJSONToBeCompared4,reservationJSONToBeCompared5,reservationJSONToBeCompared6,reservationJSONToBeCompared7,reservationJSONToBeCompared8);", "", "var i=0;", "//assertions", "for(item of arr)", "{", "    assertJSONData(\"Verifying Client Code in OxiMessage\", jsonData,item);", "    assertRatesData(\"Verifying rates json in OxiMessage\", jsonData.rates[i],item.rates[0]);", "    i++;", "}", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue.versionId).to.eql(expectedValue.versionId);", "    pm.expect(actualValue.clientCode).to.eql(expectedValue.clientCode);", "    pm.expect(actualValue.propertyCode).to.eql(expectedValue.propertyCode);", "    pm.expect(actualValue.ratePlanType).to.eql(expectedValue.ratePlanType);", "    pm.expect(actualValue.name).to.eql(expectedValue.name);", "    pm.expect(actualValue.notifType).to.eql(expectedValue.notifType);", "    pm.expect(actualValue.marketCode).to.eql(expectedValue.marketCode);", "    pm.expect(actualValue.originalCurrencyCode).to.eql(expectedValue.originalCurrencyCode);", "    pm.expect(actualValue.qualified).to.eql(expectedValue.qualified);", "    ", "    pm.expect(actualValue.services[0].serviceName).to.eql(expectedValue.services[0].serviceName);", "    pm.expect(actualValue.services[0].includedInRate).to.eql(expectedValue.services[0].includedInRate);", "    pm.expect(actualValue.services[0].inclusionType).to.eql(expectedValue.services[0].inclusionType);", "});}", "", "function assertRatesData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue.invTypeCode).to.eql(expectedValue.invTypeCode);", "    pm.expect(actualValue.monday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue.monday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.monday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.monday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.monday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.monday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.monday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.monday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.monday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.monday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.monday.childAdditionalGuestAmount.amount).to.eql(expectedValue.monday.childAdditionalGuestAmount.amount);", "    ", "    pm.expect(actualValue.tuesday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue.tuesday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.tuesday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.tuesday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.tuesday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.tuesday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.tuesday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.tuesday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.tuesday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.tuesday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.tuesday.childAdditionalGuestAmount.amount).to.eql(expectedValue.tuesday.childAdditionalGuestAmount.amount);", "    ", "    pm.expect(actualValue.wednesday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue.wednesday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.wednesday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.wednesday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.wednesday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.wednesday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.wednesday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.wednesday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.wednesday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.wednesday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.wednesday.childAdditionalGuestAmount.amount).to.eql(expectedValue.wednesday.childAdditionalGuestAmount.amount);", "    ", "    pm.expect(actualValue.thursday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue", "    .thursday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.thursday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.thursday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.thursday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.thursday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.thursday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.thursday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.thursday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.thursday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.thursday.childAdditionalGuestAmount.amount).to.eql(expectedValue.thursday.childAdditionalGuestAmount.amount);", "    ", "    pm.expect(actualValue.friday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue.friday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.friday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.friday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.friday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.friday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.friday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.friday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.friday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.friday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.friday.childAdditionalGuestAmount.amount).to.eql(expectedValue.friday.childAdditionalGuestAmount.amount);", "    ", "    pm.expect(actualValue.sunday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue.sunday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.sunday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.sunday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.sunday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.sunday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.sunday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.sunday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.sunday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.sunday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.sunday.childAdditionalGuestAmount.amount).to.eql(expectedValue.sunday.childAdditionalGuestAmount.amount);", "});}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusQualifiedRatePlans/search/findByClientCodeAndPropertyCodeAndName?clientCode={{client_Code}}&propertyCode={{property_Code}}&name=OTAGENA1_GBP_USD", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusQualifiedRatePlans", "search", "findByClientCodeAndPropertyCodeAndName"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "name", "value": "OTAGENA1_GBP_USD"}]}}, "response": []}, {"name": "When a qualified rate message is sent to NGI which is after currentDate", "event": [{"listen": "test", "script": {"exec": ["//verifing status", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\\\"1.0\\\" encoding=\\\"UTF-8\\\"?>\r\n<?Label SandBox-OPER1|RATE|*********|SUCCESS?>\r\n<RateHeader\r\n    xmlns=\\\"rate.fidelio.6.0\\\" rateStatus=\\\"ACTIVE\\\" taxIncluded=\\\"YES\\\" yieldStatus=\\\"FULLY\\\" sellSequence=\\\"150\\\" rateMessageType=\\\"HEADERWITHDETAIL\\\">\r\n    <hotelCode>SandBox-OPER1</hotelCode>\r\n    <RateCategory>\r\n        <catDescription>OTA Rate Codes</catDescription>\r\n        <category>OTA</category>\r\n    </RateCategory>\r\n    <rateCode>OTAGENA2_GBP_USD</rateCode>\r\n    <startSell>{{todayDate}}</startSell>\r\n    <endSell>{{twoWeekAfterToday}}</endSell>\r\n    <advancedBookingDays>0</advancedBookingDays>\r\n    <displayText>Flexible Rate Inc Breakfast</displayText>\r\n    <folioText>Accommodation BB</folioText>\r\n    <longInfo>Flexible Rate Inc Breakfast</longInfo>\r\n    <shortInfo>Flexible Rate Inc Breakfast2pm CLX</shortInfo>\r\n    <minLos>1</minLos>\r\n    <maxLos>99</maxLos>\r\n    <packages>BKFS</packages>\r\n    <marketCode>OTA</marketCode>\r\n    <foreignCurrencyDepartmentCode>GBP</foreignCurrencyDepartmentCode>\r\n    <departmentCode>1000</departmentCode>\r\n    <confidential>N</confidential>\r\n    <printRate>0</printRate>\r\n    <rateTiered>0</rateTiered>\r\n    <negotiated>1</negotiated>\r\n    <dayUse>0</dayUse>\r\n    <complimentary>0</complimentary>\r\n    <houseUse>0</houseUse>\r\n    <dayType>0</dayType>\r\n    <discount>0</discount>\r\n    <packageTxnCode>7500</packageTxnCode>\r\n    <RateDetails>\r\n        <RateDetail>\r\n            <rateCode>CEN13</rateCode>\r\n            <startDate>{{todayDate}}</startDate>\r\n            <endDate>{{weekAfterToday}}</endDate>\r\n            <roomTypeList>EKB</roomTypeList>\r\n            <roomTypeList>FAM</roomTypeList>\r\n            <roomTypeList>CTB</roomTypeList>\r\n            <roomTypeList>CKC</roomTypeList>\r\n            <roomTypeList>C1D</roomTypeList>\r\n            <roomTypeList>SKC</roomTypeList>\r\n            <rate1>500</rate1>\r\n            <rate2>500</rate2>\r\n            <rate3>500</rate3>\r\n            <RateDaysOfWeek>\r\n                <monday>1</monday>\r\n                <tuesday>1</tuesday>\r\n                <wednesday>1</wednesday>\r\n                <thursday>1</thursday>\r\n                <friday>1</friday>\r\n                <saturday>1</saturday>\r\n                <sunday>1</sunday>\r\n            </RateDaysOfWeek>\r\n            <freeChildren>1</freeChildren>\r\n            <childExcThreshold1>0</childExcThreshold1>\r\n            <childExcThreshold2>0</childExcThreshold2>\r\n            <childExcThreshold3>0</childExcThreshold3>\r\n            <bar>0</bar>\r\n            <barFltPct>FLT</barFltPct>\r\n            <barRounding>N</barRounding>\r\n        </RateDetail>\r\n        <RateDetail>\r\n            <rateCode>CEN14</rateCode>\r\n            <startDate>{{weekAfterToday}}</startDate>\r\n            <endDate>{{twoWeekAfterToday}}</endDate>\r\n            <roomTypeList>PM</roomTypeList>\r\n            <roomTypeList>PF</roomTypeList>\r\n            <roomTypeList>PI</roomTypeList>\r\n            <rate1>200</rate1>\r\n            <rate2>200</rate2>\r\n            <rate3>200</rate3>\r\n            <RateDaysOfWeek>\r\n                <monday>1</monday>\r\n                <tuesday>1</tuesday>\r\n                <wednesday>1</wednesday>\r\n                <thursday>1</thursday>\r\n                <friday>1</friday>\r\n                <saturday>1</saturday>\r\n                <sunday>1</sunday>\r\n            </RateDaysOfWeek>\r\n            <freeChildren>1</freeChildren>\r\n            <childExcThreshold1>0</childExcThreshold1>\r\n            <childExcThreshold2>0</childExcThreshold2>\r\n            <childExcThreshold3>0</childExcThreshold3>\r\n            <bar>0</bar>\r\n            <barFltPct>FLT</barFltPct>\r\n            <barRounding>N</barRounding>\r\n        </RateDetail>\r\n    </RateDetails>\r\n    <gdsAllowed>1</gdsAllowed>\r\n    <RatePackages>\r\n        <RatePackage>\r\n            <Package\r\n                xmlns=\\\"packages.fidelio.3.0\\\" calculationRule=\\\"A\\\" postingRhythm=\\\"D\\\" packageElement=\\\"ELEMENT\\\" addToRateMode=\\\"INCLUDE\\\">\r\n                <hotelCode>SandBox-OPER1</hotelCode>\r\n                <packageCode>BKFS</packageCode>\r\n                <packageDescription>Breakfast</packageDescription>\r\n                <printSeperately>0</printSeperately>\r\n                <addToRate>0</addToRate>\r\n                <taxIncluded>1</taxIncluded>\r\n                <allowance>0</allowance>\r\n                <currencyCode>GBP</currencyCode>\r\n                <sellSeparate>0</sellSeparate>\r\n                <postNextDay>0</postNextDay>\r\n                <cateringPackage>0</cateringPackage>\r\n                <forecastNextDay>1</forecastNextDay>\r\n                <overrideFixedRate>0</overrideFixedRate>\r\n                <containsInactiveDetails>1</containsInactiveDetails>\r\n            </Package>\r\n        </RatePackage>\r\n    </RatePackages>\r\n    <channelRestriction>1</channelRestriction>\r\n    <dailyRate>0</dailyRate>\r\n    <rod>0</rod>\r\n    <rodBased>0</rodBased>\r\n    <bbar>0</bbar>\r\n    <bbarBased>0</bbarBased>\r\n    <roomTypeList>PM</roomTypeList>\r\n    <roomTypeList>PF</roomTypeList>\r\n    <roomTypeList>PI</roomTypeList>\r\n    <roomTypeList>C1D</roomTypeList>\r\n    <roomTypeList>CTB</roomTypeList>\r\n    <roomTypeList>CKC</roomTypeList>\r\n    <roomTypeList>SKC</roomTypeList>\r\n    <roomTypeList>EKB</roomTypeList>\r\n    <roomTypeList>FAM</roomTypeList>\r\n    <mfnUpload>1</mfnUpload>\r\n    <membership>0</membership>\r\n    <exchangePostingType>P</exchangePostingType>\r\n    <bbarCompare>0</bbarCompare>\r\n    <fullDailyRateDetails>0</fullDailyRateDetails>\r\n    <redemption>0</redemption>\r\n    <apartmentBillingCycle>NA</apartmentBillingCycle>\r\n    <advBaseCompare>0</advBaseCompare>\r\n    <defaultToHighestBar>0</defaultToHighestBar>\r\n    <repeatPostingRhythm>1</repeatPostingRhythm>\r\n    <discountRatePercentage>0</discountRatePercentage>\r\n</RateHeader>"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/ngipublic/rest/oxi/pms", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["ngipublic", "rest", "oxi", "pms"]}}, "response": []}, {"name": "Then rate message is processed at NGI and saved in oxiMessages collection for future", "event": [{"listen": "test", "script": {"exec": ["//variables", "var jsonData = pm.response.json();", "var allRates =  jsonData.rates;", "", "", "", "", "//value to compare with", "var reservationJSONToBeCompared ={\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"QUALIFIED_NEGOTIATED\",\"correlationId\":\"15277f91-87dd-4696-8aed-d57953de2ba5\",\"name\":\"OTAGENA2_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-03-31\",\"endDate\":\"2020-04-15\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"EKB\",\"originalCurrencyCode\":null,\"startDate\":\"2020-03-31\",\"endDate\":\"2020-04-07\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e830abe5f87761fb4e8d379\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"referenceRateCode\":\"None\",\"qualified\":true};", "", "var reservationJSONToBeCompared1 ={\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"QUALIFIED_NEGOTIATED\",\"correlationId\":\"15277f91-87dd-4696-8aed-d57953de2ba5\",\"name\":\"OTAGENA2_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-03-31\",\"endDate\":\"2020-04-15\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"FAM\",\"originalCurrencyCode\":null,\"startDate\":\"2020-03-31\",\"endDate\":\"2020-04-07\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e830abe5f87761fb4e8d379\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"referenceRateCode\":\"None\",\"qualified\":true};", "", "var reservationJSONToBeCompared2 ={\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"QUALIFIED_NEGOTIATED\",\"correlationId\":\"15277f91-87dd-4696-8aed-d57953de2ba5\",\"name\":\"OTAGENA2_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-03-31\",\"endDate\":\"2020-04-15\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"CTB\",\"originalCurrencyCode\":null,\"startDate\":\"2020-03-31\",\"endDate\":\"2020-04-07\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e830abe5f87761fb4e8d379\",\"currency\":null},],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"referenceRateCode\":\"None\",\"qualified\":true};", "", "var reservationJSONToBeCompared3 ={\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"QUALIFIED_NEGOTIATED\",\"correlationId\":\"15277f91-87dd-4696-8aed-d57953de2ba5\",\"name\":\"OTAGENA2_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-03-31\",\"endDate\":\"2020-04-15\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"CKC\",\"originalCurrencyCode\":null,\"startDate\":\"2020-03-31\",\"endDate\":\"2020-04-07\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e830abe5f87761fb4e8d379\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"referenceRateCode\":\"None\",\"qualified\":true};", "", "var reservationJSONToBeCompared4 ={\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"QUALIFIED_NEGOTIATED\",\"correlationId\":\"15277f91-87dd-4696-8aed-d57953de2ba5\",\"name\":\"OTAGENA2_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-03-31\",\"endDate\":\"2020-04-15\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"C1D\",\"originalCurrencyCode\":null,\"startDate\":\"2020-03-31\",\"endDate\":\"2020-04-07\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e830abe5f87761fb4e8d379\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"referenceRateCode\":\"None\",\"qualified\":true};", "", "var reservationJSONToBeCompared5 ={\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"QUALIFIED_NEGOTIATED\",\"correlationId\":\"15277f91-87dd-4696-8aed-d57953de2ba5\",\"name\":\"OTAGENA2_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-03-31\",\"endDate\":\"2020-04-15\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"SKC\",\"originalCurrencyCode\":null,\"startDate\":\"2020-03-31\",\"endDate\":\"2020-04-07\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e830abe5f87761fb4e8d379\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"referenceRateCode\":\"None\",\"qualified\":true};", "", "var reservationJSONToBeCompared6 ={\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"QUALIFIED_NEGOTIATED\",\"correlationId\":\"15277f91-87dd-4696-8aed-d57953de2ba5\",\"name\":\"OTAGENA2_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-03-31\",\"endDate\":\"2020-04-15\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"PM\",\"originalCurrencyCode\":null,\"startDate\":\"2020-04-07\",\"endDate\":\"2020-04-15\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e830abe5f87761fb4e8d379\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"referenceRateCode\":\"None\",\"qualified\":true};", "", "var reservationJSONToBeCompared7 ={\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"QUALIFIED_NEGOTIATED\",\"correlationId\":\"15277f91-87dd-4696-8aed-d57953de2ba5\",\"name\":\"OTAGENA2_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-03-31\",\"endDate\":\"2020-04-15\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"PF\",\"originalCurrencyCode\":null,\"startDate\":\"2020-04-07\",\"endDate\":\"2020-04-15\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e830abe5f87761fb4e8d379\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"referenceRateCode\":\"None\",\"qualified\":true};", "", "var reservationJSONToBeCompared8 ={\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"QUALIFIED_NEGOTIATED\",\"correlationId\":\"15277f91-87dd-4696-8aed-d57953de2ba5\",\"name\":\"OTAGENA2_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-03-31\",\"endDate\":\"2020-04-15\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"PI\",\"originalCurrencyCode\":null,\"startDate\":\"2020-04-07\",\"endDate\":\"2020-04-15\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e830abe5f87761fb4e8d379\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"referenceRateCode\":\"None\",\"qualified\":true};", "", "console.log(JSON.stringify(jsonData));", "", "arr=[];", "", "arr.push(reservationJSONToBeCompared,reservationJSONToBeCompared1,reservationJSONToBeCompared2,reservationJSONToBeCompared3,reservationJSONToBeCompared4,reservationJSONToBeCompared5,reservationJSONToBeCompared6,reservationJSONToBeCompared7,reservationJSONToBeCompared8);", "", "var i=0;", "//assertions", "for(item of arr)", "{", "    assertJSONData(\"Verifying Client Code in OxiMessage\", jsonData,item);", "    assertRatesData(\"Verifying rates json in OxiMessage\", jsonData.rates[i],item.rates[0]);", "    i++;", "}", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue.versionId).to.eql(expectedValue.versionId);", "    pm.expect(actualValue.clientCode).to.eql(expectedValue.clientCode);", "    pm.expect(actualValue.propertyCode).to.eql(expectedValue.propertyCode);", "    pm.expect(actualValue.ratePlanType).to.eql(expectedValue.ratePlanType);", "    pm.expect(actualValue.name).to.eql(expectedValue.name);", "    pm.expect(actualValue.notifType).to.eql(expectedValue.notifType);", "    pm.expect(actualValue.marketCode).to.eql(expectedValue.marketCode);", "    pm.expect(actualValue.originalCurrencyCode).to.eql(expectedValue.originalCurrencyCode);", "    pm.expect(actualValue.qualified).to.eql(expectedValue.qualified);", "    pm.expect(actualValue.services[0].serviceName).to.eql(expectedValue.services[0].serviceName);", "    pm.expect(actualValue.services[0].includedInRate).to.eql(expectedValue.services[0].includedInRate);", "    pm.expect(actualValue.services[0].inclusionType).to.eql(expectedValue.services[0].inclusionType);", "});}", "", "function assertRatesData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue.invTypeCode).to.eql(expectedValue.invTypeCode);", "    pm.expect(actualValue.monday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue.monday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.monday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.monday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.monday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.monday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.monday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.monday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.monday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.monday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.monday.childAdditionalGuestAmount.amount).to.eql(expectedValue.monday.childAdditionalGuestAmount.amount);", "    ", "    pm.expect(actualValue.tuesday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue.tuesday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.tuesday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.tuesday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.tuesday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.tuesday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.tuesday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.tuesday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.tuesday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.tuesday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.tuesday.childAdditionalGuestAmount.amount).to.eql(expectedValue.tuesday.childAdditionalGuestAmount.amount);", "    ", "    pm.expect(actualValue.wednesday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue.wednesday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.wednesday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.wednesday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.wednesday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.wednesday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.wednesday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.wednesday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.wednesday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.wednesday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.wednesday.childAdditionalGuestAmount.amount).to.eql(expectedValue.wednesday.childAdditionalGuestAmount.amount);", "    ", "    pm.expect(actualValue.thursday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue", "    .thursday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.thursday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.thursday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.thursday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.thursday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.thursday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.thursday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.thursday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.thursday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.thursday.childAdditionalGuestAmount.amount).to.eql(expectedValue.thursday.childAdditionalGuestAmount.amount);", "    ", "    pm.expect(actualValue.friday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue.friday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.friday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.friday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.friday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.friday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.friday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.friday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.friday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.friday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.friday.childAdditionalGuestAmount.amount).to.eql(expectedValue.friday.childAdditionalGuestAmount.amount);", "    ", "    pm.expect(actualValue.sunday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue.sunday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.sunday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.sunday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.sunday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.sunday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.sunday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.sunday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.sunday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.sunday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.sunday.childAdditionalGuestAmount.amount).to.eql(expectedValue.sunday.childAdditionalGuestAmount.amount);", "});}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusQualifiedRatePlans/search/findByClientCodeAndPropertyCodeAndName?clientCode={{client_Code}}&propertyCode={{property_Code}}&name=OTAGENA2_GBP_USD", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusQualifiedRatePlans", "search", "findByClientCodeAndPropertyCodeAndName"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "name", "value": "OTAGENA2_GBP_USD"}]}}, "response": []}, {"name": "When a Unqualified rate message is sent to NGI which is before currentDate", "event": [{"listen": "test", "script": {"exec": ["//verifing status", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\\\"1.0\\\" encoding=\\\"UTF-8\\\"?>\r\n<?Label SandBox-OPER1|RATE|*********|SUCCESS?>\r\n<RateHeader\r\n    xmlns=\\\"rate.fidelio.6.0\\\" rateStatus=\\\"ACTIVE\\\" taxIncluded=\\\"YES\\\" yieldStatus=\\\"FULLY\\\" sellSequence=\\\"150\\\" rateMessageType=\\\"HEADERWITHDETAIL\\\">\r\n    <hotelCode>SandBox-OPER1</hotelCode>\r\n    <RateCategory>\r\n        <catDescription>OTA Rate Codes</catDescription>\r\n        <category>OTA</category>\r\n    </RateCategory>\r\n    <rateCode>OTAGENA3_GBP_USD</rateCode>\r\n    <startSell>2016-01-01</startSell>\r\n    <endSell>2019-12-31</endSell>\r\n    <advancedBookingDays>0</advancedBookingDays>\r\n    <displayText>Flexible Rate Inc Breakfast</displayText>\r\n    <folioText>Accommodation BB</folioText>\r\n    <longInfo>Flexible Rate Inc Breakfast</longInfo>\r\n    <shortInfo>Flexible Rate Inc Breakfast2pm CLX</shortInfo>\r\n    <minLos>1</minLos>\r\n    <maxLos>99</maxLos>\r\n    <packages>BKFS</packages>\r\n    <marketCode>OTA</marketCode>\r\n    <foreignCurrencyDepartmentCode>GBP</foreignCurrencyDepartmentCode>\r\n    <departmentCode>1000</departmentCode>\r\n    <confidential>N</confidential>\r\n    <printRate>0</printRate>\r\n    <rateTiered>0</rateTiered>\r\n    <negotiated>1</negotiated>\r\n    <dayUse>0</dayUse>\r\n    <complimentary>0</complimentary>\r\n    <houseUse>0</houseUse>\r\n    <dayType>0</dayType>\r\n    <discount>0</discount>\r\n    <packageTxnCode>7500</packageTxnCode>\r\n    <RateDetails>\r\n        <RateDetail>\r\n            <rateCode>OTAGENA3_GBP_USD</rateCode>\r\n            <startDate>2018-06-01</startDate>\r\n            <endDate>2018-06-30</endDate>\r\n            <roomTypeList>EKB</roomTypeList>\r\n            <roomTypeList>FAM</roomTypeList>\r\n            <roomTypeList>CTB</roomTypeList>\r\n            <roomTypeList>CKC</roomTypeList>\r\n            <roomTypeList>C1D</roomTypeList>\r\n            <roomTypeList>SKC</roomTypeList>\r\n            <rate1>500</rate1>\r\n            <rate2>500</rate2>\r\n            <rate3>500</rate3>\r\n            <RateDaysOfWeek>\r\n                <monday>1</monday>\r\n                <tuesday>1</tuesday>\r\n                <wednesday>1</wednesday>\r\n                <thursday>1</thursday>\r\n                <friday>1</friday>\r\n                <saturday>1</saturday>\r\n                <sunday>1</sunday>\r\n            </RateDaysOfWeek>\r\n            <freeChildren>1</freeChildren>\r\n            <childExcThreshold1>0</childExcThreshold1>\r\n            <childExcThreshold2>0</childExcThreshold2>\r\n            <childExcThreshold3>0</childExcThreshold3>\r\n            <bar>0</bar>\r\n            <barFltPct>FLT</barFltPct>\r\n            <barRounding>N</barRounding>\r\n        </RateDetail>\r\n        <RateDetail>\r\n            <rateCode>OTAGENA3_GBP_USD</rateCode>\r\n            <startDate>2018-07-01</startDate>\r\n            <endDate>2019-12-31</endDate>\r\n            <roomTypeList>PM</roomTypeList>\r\n            <roomTypeList>PF</roomTypeList>\r\n            <roomTypeList>PI</roomTypeList>\r\n            <rate1>200</rate1>\r\n            <rate2>200</rate2>\r\n            <rate3>200</rate3>\r\n            <RateDaysOfWeek>\r\n                <monday>1</monday>\r\n                <tuesday>1</tuesday>\r\n                <wednesday>1</wednesday>\r\n                <thursday>1</thursday>\r\n                <friday>1</friday>\r\n                <saturday>1</saturday>\r\n                <sunday>1</sunday>\r\n            </RateDaysOfWeek>\r\n            <freeChildren>1</freeChildren>\r\n            <childExcThreshold1>0</childExcThreshold1>\r\n            <childExcThreshold2>0</childExcThreshold2>\r\n            <childExcThreshold3>0</childExcThreshold3>\r\n            <bar>0</bar>\r\n            <barFltPct>FLT</barFltPct>\r\n            <barRounding>N</barRounding>\r\n        </RateDetail>\r\n    </RateDetails>\r\n    <gdsAllowed>1</gdsAllowed>\r\n    <RatePackages>\r\n        <RatePackage>\r\n            <Package\r\n                xmlns=\\\"packages.fidelio.3.0\\\" calculationRule=\\\"A\\\" postingRhythm=\\\"D\\\" packageElement=\\\"ELEMENT\\\" addToRateMode=\\\"INCLUDE\\\">\r\n                <hotelCode>SandBox-OPER1</hotelCode>\r\n                <packageCode>BKFS</packageCode>\r\n                <packageDescription>Breakfast</packageDescription>\r\n                <printSeperately>0</printSeperately>\r\n                <addToRate>0</addToRate>\r\n                <taxIncluded>1</taxIncluded>\r\n                <allowance>0</allowance>\r\n                <currencyCode>GBP</currencyCode>\r\n                <sellSeparate>0</sellSeparate>\r\n                <postNextDay>0</postNextDay>\r\n                <cateringPackage>0</cateringPackage>\r\n                <forecastNextDay>1</forecastNextDay>\r\n                <overrideFixedRate>0</overrideFixedRate>\r\n                <containsInactiveDetails>1</containsInactiveDetails>\r\n            </Package>\r\n        </RatePackage>\r\n    </RatePackages>\r\n    <channelRestriction>1</channelRestriction>\r\n    <dailyRate>0</dailyRate>\r\n    <rod>0</rod>\r\n    <rodBased>0</rodBased>\r\n    <bbar>1</bbar>\r\n    <bbarBased>0</bbarBased>\r\n    <roomTypeList>PM</roomTypeList>\r\n    <roomTypeList>PF</roomTypeList>\r\n    <roomTypeList>PI</roomTypeList>\r\n    <roomTypeList>C1D</roomTypeList>\r\n    <roomTypeList>CTB</roomTypeList>\r\n    <roomTypeList>CKC</roomTypeList>\r\n    <roomTypeList>SKC</roomTypeList>\r\n    <roomTypeList>EKB</roomTypeList>\r\n    <roomTypeList>FAM</roomTypeList>\r\n    <mfnUpload>1</mfnUpload>\r\n    <membership>0</membership>\r\n    <exchangePostingType>P</exchangePostingType>\r\n    <bbarCompare>0</bbarCompare>\r\n    <fullDailyRateDetails>0</fullDailyRateDetails>\r\n    <redemption>0</redemption>\r\n    <apartmentBillingCycle>NA</apartmentBillingCycle>\r\n    <advBaseCompare>0</advBaseCompare>\r\n    <defaultToHighestBar>0</defaultToHighestBar>\r\n    <repeatPostingRhythm>1</repeatPostingRhythm>\r\n    <discountRatePercentage>0</discountRatePercentage>\r\n</RateHeader>"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/ngipublic/rest/oxi/pms", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["ngipublic", "rest", "oxi", "pms"]}}, "response": []}, {"name": "Then rate message is processed at NGI and saved in oxiMessages collection for past", "event": [{"listen": "test", "script": {"exec": ["//variables", "var jsonData = pm.response.json();", "var allRates =  jsonData._embedded.nucleusUnqualifiedRatePlans[0];", "", "//value to compare with", "var reservationJSONToBeCompared = {\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"name\":\"OTAGENA3_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2016-01-01\",\"endDate\":\"2019-12-31\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"EKB\",\"originalCurrencyCode\":null,\"startDate\":\"2018-06-01\",\"endDate\":\"2018-06-30\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false};", "", "var reservationJSONToBeCompared1 = {\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"name\":\"OTAGENA3_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2016-01-01\",\"endDate\":\"2019-12-31\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"FAM\",\"originalCurrencyCode\":null,\"startDate\":\"2018-06-01\",\"endDate\":\"2018-06-30\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currency\":null},],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false};", "", "var reservationJSONToBeCompared2 = {\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"name\":\"OTAGENA3_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2016-01-01\",\"endDate\":\"2019-12-31\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"CTB\",\"originalCurrencyCode\":null,\"startDate\":\"2018-06-01\",\"endDate\":\"2018-06-30\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false};", "", "var reservationJSONToBeCompared3 = {\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"name\":\"OTAGENA3_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2016-01-01\",\"endDate\":\"2019-12-31\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"CKC\",\"originalCurrencyCode\":null,\"startDate\":\"2018-06-01\",\"endDate\":\"2018-06-30\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false};", "", "var reservationJSONToBeCompared4 ={\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"name\":\"OTAGENA3_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2016-01-01\",\"endDate\":\"2019-12-31\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"C1D\",\"originalCurrencyCode\":null,\"startDate\":\"2018-06-01\",\"endDate\":\"2018-06-30\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false};", "", "var reservationJSONToBeCompared5 ={\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"name\":\"OTAGENA3_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2016-01-01\",\"endDate\":\"2019-12-31\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"SKC\",\"originalCurrencyCode\":null,\"startDate\":\"2018-06-01\",\"endDate\":\"2018-06-30\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":45454.54545,\"amountAfterTax\":50000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false};", "", "var reservationJSONToBeCompared6 ={\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"name\":\"OTAGENA3_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2016-01-01\",\"endDate\":\"2019-12-31\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"PM\",\"originalCurrencyCode\":null,\"startDate\":\"2018-07-01\",\"endDate\":\"2019-12-31\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false};", "", "var reservationJSONToBeCompared7 ={\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"name\":\"OTAGENA3_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2016-01-01\",\"endDate\":\"2019-12-31\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"PF\",\"originalCurrencyCode\":null,\"startDate\":\"2018-07-01\",\"endDate\":\"2019-12-31\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false};", "", "var reservationJSONToBeCompared8 ={\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"name\":\"OTAGENA3_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2016-01-01\",\"endDate\":\"2019-12-31\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"PI\",\"originalCurrencyCode\":null,\"startDate\":\"2018-07-01\",\"endDate\":\"2019-12-31\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":18181.81818,\"amountAfterTax\":20000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false};", "", "console.log(JSON.stringify(jsonData));", "", "arr=[];", "", "arr.push(reservationJSONToBeCompared,reservationJSONToBeCompared1,reservationJSONToBeCompared2,reservationJSONToBeCompared3,reservationJSONToBeCompared4,reservationJSONToBeCompared5,reservationJSONToBeCompared6,reservationJSONToBeCompared7,reservationJSONToBeCompared8);", "", "var i=0;", "//assertions", "for(item of arr)", "{", "    assertJSONData(\"Verifying Client Code in OxiMessage\", allRates,item);", "    assertRatesData(\"Verifying rates json in OxiMessage\", allRates.rates[i],item.rates[0]);", "    i++;", "}", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue.versionId).to.eql(expectedValue.versionId);", "    pm.expect(actualValue.clientCode).to.eql(expectedValue.clientCode);", "    pm.expect(actualValue.propertyCode).to.eql(expectedValue.propertyCode);", "    pm.expect(actualValue.ratePlanType).to.eql(expectedValue.ratePlanType);", "    pm.expect(actualValue.name).to.eql(expectedValue.name);", "    pm.expect(actualValue.notifType).to.eql(expectedValue.notifType);", "    pm.expect(actualValue.marketCode).to.eql(expectedValue.marketCode);", "    pm.expect(actualValue.originalCurrencyCode).to.eql(expectedValue.originalCurrencyCode);", "    pm.expect(actualValue.qualified).to.eql(expectedValue.qualified);", "    pm.expect(actualValue.services[0].serviceName).to.eql(expectedValue.services[0].serviceName);", "    pm.expect(actualValue.services[0].includedInRate).to.eql(expectedValue.services[0].includedInRate);", "    pm.expect(actualValue.services[0].inclusionType).to.eql(expectedValue.services[0].inclusionType);", "});}", "", "function assertRatesData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue.invTypeCode).to.eql(expectedValue.invTypeCode);", "    pm.expect(actualValue.monday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue.monday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.monday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.monday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.monday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.monday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.monday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.monday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.monday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.monday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.monday.childAdditionalGuestAmount.amount).to.eql(expectedValue.monday.childAdditionalGuestAmount.amount);", "    ", "    pm.expect(actualValue.tuesday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue.tuesday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.tuesday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.tuesday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.tuesday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.tuesday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.tuesday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.tuesday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.tuesday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.tuesday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.tuesday.childAdditionalGuestAmount.amount).to.eql(expectedValue.tuesday.childAdditionalGuestAmount.amount);", "    ", "    pm.expect(actualValue.wednesday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue.wednesday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.wednesday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.wednesday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.wednesday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.wednesday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.wednesday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.wednesday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.wednesday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.wednesday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.wednesday.childAdditionalGuestAmount.amount).to.eql(expectedValue.wednesday.childAdditionalGuestAmount.amount);", "    ", "    pm.expect(actualValue.thursday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue", "    .thursday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.thursday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.thursday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.thursday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.thursday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.thursday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.thursday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.thursday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.thursday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.thursday.childAdditionalGuestAmount.amount).to.eql(expectedValue.thursday.childAdditionalGuestAmount.amount);", "    ", "    pm.expect(actualValue.friday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue.friday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.friday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.friday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.friday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.friday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.friday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.friday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.friday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.friday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.friday.childAdditionalGuestAmount.amount).to.eql(expectedValue.friday.childAdditionalGuestAmount.amount);", "    ", "    pm.expect(actualValue.sunday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue.sunday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.sunday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.sunday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.sunday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.sunday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.sunday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.sunday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.sunday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.sunday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.sunday.childAdditionalGuestAmount.amount).to.eql(expectedValue.sunday.childAdditionalGuestAmount.amount);", "});}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusUnqualifiedRatePlans/criteria?search=clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3Bname%3D%3DOTAGENA3_GBP_USD", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusUnqualifiedRatePlans", "criteria"], "query": [{"key": "search", "value": "clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3Bname%3D%3DOTAGENA3_GBP_USD"}]}}, "response": []}, {"name": "When a Unqualified rate message is sent to NGI which is after currentDate", "event": [{"listen": "test", "script": {"exec": ["//verifing status", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\\\"1.0\\\" encoding=\\\"UTF-8\\\"?>\r\n<?Label SandBox-OPER1|RATE|*********|SUCCESS?>\r\n<RateHeader\r\n    xmlns=\\\"rate.fidelio.6.0\\\" rateStatus=\\\"ACTIVE\\\" taxIncluded=\\\"YES\\\" yieldStatus=\\\"FULLY\\\" sellSequence=\\\"150\\\" rateMessageType=\\\"HEADERWITHDETAIL\\\">\r\n    <hotelCode>SandBox-OPER1</hotelCode>\r\n    <RateCategory>\r\n        <catDescription>OTA Rate Codes</catDescription>\r\n        <category>OTA</category>\r\n    </RateCategory>\r\n    <rateCode>OTAGENA4_GBP_USD</rateCode>\r\n    <startSell>{{todayDate}}</startSell>\r\n    <endSell>{{twoWeekAfterToday}}</endSell>\r\n    <advancedBookingDays>0</advancedBookingDays>\r\n    <displayText>Flexible Rate Inc Breakfast</displayText>\r\n    <folioText>Accommodation BB</folioText>\r\n    <longInfo>Flexible Rate Inc Breakfast</longInfo>\r\n    <shortInfo>Flexible Rate Inc Breakfast2pm CLX</shortInfo>\r\n    <minLos>1</minLos>\r\n    <maxLos>99</maxLos>\r\n    <packages>BKFS</packages>\r\n    <marketCode>OTA</marketCode>\r\n    <foreignCurrencyDepartmentCode>GBP</foreignCurrencyDepartmentCode>\r\n    <departmentCode>1000</departmentCode>\r\n    <confidential>N</confidential>\r\n    <printRate>0</printRate>\r\n    <rateTiered>0</rateTiered>\r\n    <negotiated>1</negotiated>\r\n    <dayUse>0</dayUse>\r\n    <complimentary>0</complimentary>\r\n    <houseUse>0</houseUse>\r\n    <dayType>0</dayType>\r\n    <discount>0</discount>\r\n    <packageTxnCode>7500</packageTxnCode>\r\n    <RateDetails>\r\n        <RateDetail>\r\n            <rateCode>CEN15</rateCode>\r\n            <startDate>{{todayDate}}</startDate>\r\n            <endDate>{{weekAfterToday}}</endDate>\r\n            <roomTypeList>EKB</roomTypeList>\r\n            <roomTypeList>FAM</roomTypeList>\r\n            <roomTypeList>CTB</roomTypeList>\r\n            <roomTypeList>CKC</roomTypeList>\r\n            <roomTypeList>C1D</roomTypeList>\r\n            <roomTypeList>SKC</roomTypeList>\r\n            <rate1>500</rate1>\r\n            <rate2>500</rate2>\r\n            <rate3>500</rate3>\r\n            <RateDaysOfWeek>\r\n                <monday>1</monday>\r\n                <tuesday>1</tuesday>\r\n                <wednesday>1</wednesday>\r\n                <thursday>1</thursday>\r\n                <friday>1</friday>\r\n                <saturday>1</saturday>\r\n                <sunday>1</sunday>\r\n            </RateDaysOfWeek>\r\n            <freeChildren>1</freeChildren>\r\n            <childExcThreshold1>0</childExcThreshold1>\r\n            <childExcThreshold2>0</childExcThreshold2>\r\n            <childExcThreshold3>0</childExcThreshold3>\r\n            <bar>0</bar>\r\n            <barFltPct>FLT</barFltPct>\r\n            <barRounding>N</barRounding>\r\n        </RateDetail>\r\n        <RateDetail>\r\n            <rateCode>CEN16</rateCode>\r\n            <startDate>{{weekAfterToday}}</startDate>\r\n            <endDate>{{twoWeekAfterToday}}</endDate>\r\n            <roomTypeList>PM</roomTypeList>\r\n            <roomTypeList>PF</roomTypeList>\r\n            <roomTypeList>PI</roomTypeList>\r\n            <rate1>200</rate1>\r\n            <rate2>200</rate2>\r\n            <rate3>200</rate3>\r\n            <RateDaysOfWeek>\r\n                <monday>1</monday>\r\n                <tuesday>1</tuesday>\r\n                <wednesday>1</wednesday>\r\n                <thursday>1</thursday>\r\n                <friday>1</friday>\r\n                <saturday>1</saturday>\r\n                <sunday>1</sunday>\r\n            </RateDaysOfWeek>\r\n            <freeChildren>1</freeChildren>\r\n            <childExcThreshold1>0</childExcThreshold1>\r\n            <childExcThreshold2>0</childExcThreshold2>\r\n            <childExcThreshold3>0</childExcThreshold3>\r\n            <bar>0</bar>\r\n            <barFltPct>FLT</barFltPct>\r\n            <barRounding>N</barRounding>\r\n        </RateDetail>\r\n    </RateDetails>\r\n    <gdsAllowed>1</gdsAllowed>\r\n    <RatePackages>\r\n        <RatePackage>\r\n            <Package\r\n                xmlns=\\\"packages.fidelio.3.0\\\" calculationRule=\\\"A\\\" postingRhythm=\\\"D\\\" packageElement=\\\"ELEMENT\\\" addToRateMode=\\\"INCLUDE\\\">\r\n                <hotelCode>SandBox-OPER1</hotelCode>\r\n                <packageCode>BKFS</packageCode>\r\n                <packageDescription>Breakfast</packageDescription>\r\n                <printSeperately>0</printSeperately>\r\n                <addToRate>0</addToRate>\r\n                <taxIncluded>1</taxIncluded>\r\n                <allowance>0</allowance>\r\n                <currencyCode>GBP</currencyCode>\r\n                <sellSeparate>0</sellSeparate>\r\n                <postNextDay>0</postNextDay>\r\n                <cateringPackage>0</cateringPackage>\r\n                <forecastNextDay>1</forecastNextDay>\r\n                <overrideFixedRate>0</overrideFixedRate>\r\n                <containsInactiveDetails>1</containsInactiveDetails>\r\n            </Package>\r\n        </RatePackage>\r\n    </RatePackages>\r\n    <channelRestriction>1</channelRestriction>\r\n    <dailyRate>0</dailyRate>\r\n    <rod>0</rod>\r\n    <rodBased>0</rodBased>\r\n    <bbar>1</bbar>\r\n    <bbarBased>0</bbarBased>\r\n    <roomTypeList>PM</roomTypeList>\r\n    <roomTypeList>PF</roomTypeList>\r\n    <roomTypeList>PI</roomTypeList>\r\n    <roomTypeList>C1D</roomTypeList>\r\n    <roomTypeList>CTB</roomTypeList>\r\n    <roomTypeList>CKC</roomTypeList>\r\n    <roomTypeList>SKC</roomTypeList>\r\n    <roomTypeList>EKB</roomTypeList>\r\n    <roomTypeList>FAM</roomTypeList>\r\n    <mfnUpload>1</mfnUpload>\r\n    <membership>0</membership>\r\n    <exchangePostingType>P</exchangePostingType>\r\n    <bbarCompare>0</bbarCompare>\r\n    <fullDailyRateDetails>0</fullDailyRateDetails>\r\n    <redemption>0</redemption>\r\n    <apartmentBillingCycle>NA</apartmentBillingCycle>\r\n    <advBaseCompare>0</advBaseCompare>\r\n    <defaultToHighestBar>0</defaultToHighestBar>\r\n    <repeatPostingRhythm>1</repeatPostingRhythm>\r\n    <discountRatePercentage>0</discountRatePercentage>\r\n</RateHeader>"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/ngipublic/rest/oxi/pms", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["ngipublic", "rest", "oxi", "pms"]}}, "response": []}, {"name": "Then rate message is processed at NGI and saved in oxiMessages collection for future", "event": [{"listen": "test", "script": {"exec": ["//variables", "var jsonData = pm.response.json();", "var allRates =  jsonData._embedded.nucleusUnqualifiedRatePlans[0];", "", "//value to compare with", "var reservationJSONToBeCompared = {\"_embedded\":{\"nucleusUnqualifiedRatePlans\":[{\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"correlationId\":\"224c35b3-7109-4384-87e9-60860d4228ab\",\"name\":\"OTAGENA4_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-21\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"EKB\",\"originalCurrencyCode\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-13\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e8b26905f87761fb4e8fd4a\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false}]}};", "", "var reservationJSONToBeCompared1 = {\"_embedded\":{\"nucleusUnqualifiedRatePlans\":[{\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"correlationId\":\"224c35b3-7109-4384-87e9-60860d4228ab\",\"name\":\"OTAGENA4_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-21\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"FAM\",\"originalCurrencyCode\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-13\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e8b26905f87761fb4e8fd4a\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false}]}};", "", "var reservationJSONToBeCompared2 = {\"_embedded\":{\"nucleusUnqualifiedRatePlans\":[{\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"correlationId\":\"224c35b3-7109-4384-87e9-60860d4228ab\",\"name\":\"OTAGENA4_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-21\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"CTB\",\"originalCurrencyCode\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-13\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e8b26905f87761fb4e8fd4a\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false}]}};", "", "var reservationJSONToBeCompared3 = {\"_embedded\":{\"nucleusUnqualifiedRatePlans\":[{\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"correlationId\":\"224c35b3-7109-4384-87e9-60860d4228ab\",\"name\":\"OTAGENA4_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-21\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"CKC\",\"originalCurrencyCode\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-13\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e8b26905f87761fb4e8fd4a\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false}]}};", "", "var reservationJSONToBeCompared4 ={\"_embedded\":{\"nucleusUnqualifiedRatePlans\":[{\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"correlationId\":\"224c35b3-7109-4384-87e9-60860d4228ab\",\"name\":\"OTAGENA4_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-21\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"C1D\",\"originalCurrencyCode\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-13\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e8b26905f87761fb4e8fd4a\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false}]}};", "", "var reservationJSONToBeCompared5 ={\"_embedded\":{\"nucleusUnqualifiedRatePlans\":[{\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"correlationId\":\"224c35b3-7109-4384-87e9-60860d4228ab\",\"name\":\"OTAGENA4_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-21\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"SKC\",\"originalCurrencyCode\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-13\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e8b26905f87761fb4e8fd4a\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false}]}};", "", "var reservationJSONToBeCompared6 = {\"_embedded\":{\"nucleusUnqualifiedRatePlans\":[{\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"correlationId\":\"224c35b3-7109-4384-87e9-60860d4228ab\",\"name\":\"OTAGENA4_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-21\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"PM\",\"originalCurrencyCode\":null,\"startDate\":\"2020-04-13\",\"endDate\":\"2020-04-21\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e8b26905f87761fb4e8fd4a\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false}]}};", "", "var reservationJSONToBeCompared7 ={\"_embedded\":{\"nucleusUnqualifiedRatePlans\":[{\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"correlationId\":\"224c35b3-7109-4384-87e9-60860d4228ab\",\"name\":\"OTAGENA4_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-21\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"PF\",\"originalCurrencyCode\":null,\"startDate\":\"2020-04-13\",\"endDate\":\"2020-04-21\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e8b26905f87761fb4e8fd4a\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false}]}};", "", "var reservationJSONToBeCompared8 ={\"_embedded\":{\"nucleusUnqualifiedRatePlans\":[{\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"correlationId\":\"224c35b3-7109-4384-87e9-60860d4228ab\",\"name\":\"OTAGENA4_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-21\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"PI\",\"originalCurrencyCode\":null,\"startDate\":\"2020-04-13\",\"endDate\":\"2020-04-21\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e8b26905f87761fb4e8fd4a\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false}]}};", "", "console.log(JSON.stringify(jsonData));", "", "arr=[];", "", "arr.push(reservationJSONToBeCompared,reservationJSONToBeCompared1,reservationJSONToBeCompared2,reservationJSONToBeCompared3,reservationJSONToBeCompared4,reservationJSONToBeCompared5,reservationJSONToBeCompared6,reservationJSONToBeCompared7,reservationJSONToBeCompared8);", "", "var i=0;", "//assertions", "for(item of arr)", "{", "    assertJSONData(\"Verifying Client Code in OxiMessage\", allRates,item._embedded.nucleusUnqualifiedRatePlans[0]);", "    assertRatesData(\"Verifying rates json in OxiMessage\", allRates.rates[i],item._embedded.nucleusUnqualifiedRatePlans[0].rates[0]);", "    i++;", "}", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue.versionId).to.eql(expectedValue.versionId);", "    pm.expect(actualValue.clientCode).to.eql(expectedValue.clientCode);", "    pm.expect(actualValue.propertyCode).to.eql(expectedValue.propertyCode);", "    pm.expect(actualValue.ratePlanType).to.eql(expectedValue.ratePlanType);", "    pm.expect(actualValue.name).to.eql(expectedValue.name);", "    pm.expect(actualValue.notifType).to.eql(expectedValue.notifType);", "    pm.expect(actualValue.marketCode).to.eql(expectedValue.marketCode);", "    pm.expect(actualValue.originalCurrencyCode).to.eql(expectedValue.originalCurrencyCode);", "    pm.expect(actualValue.qualified).to.eql(expectedValue.qualified);", "    pm.expect(actualValue.services[0].serviceName).to.eql(expectedValue.services[0].serviceName);", "    pm.expect(actualValue.services[0].includedInRate).to.eql(expectedValue.services[0].includedInRate);", "    pm.expect(actualValue.services[0].inclusionType).to.eql(expectedValue.services[0].inclusionType);", "});}", "", "function assertRatesData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue.invTypeCode).to.eql(expectedValue.invTypeCode);", "    pm.expect(actualValue.monday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue.monday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.monday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.monday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.monday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.monday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.monday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.monday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.monday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.monday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.monday.childAdditionalGuestAmount.amount).to.eql(expectedValue.monday.childAdditionalGuestAmount.amount);", "    ", "    pm.expect(actualValue.tuesday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue.tuesday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.tuesday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.tuesday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.tuesday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.tuesday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.tuesday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.tuesday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.tuesday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.tuesday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.tuesday.childAdditionalGuestAmount.amount).to.eql(expectedValue.tuesday.childAdditionalGuestAmount.amount);", "    ", "    pm.expect(actualValue.wednesday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue.wednesday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.wednesday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.wednesday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.wednesday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.wednesday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.wednesday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.wednesday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.wednesday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.wednesday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.wednesday.childAdditionalGuestAmount.amount).to.eql(expectedValue.wednesday.childAdditionalGuestAmount.amount);", "    ", "    pm.expect(actualValue.thursday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue", "    .thursday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.thursday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.thursday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.thursday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.thursday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.thursday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.thursday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.thursday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.thursday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.thursday.childAdditionalGuestAmount.amount).to.eql(expectedValue.thursday.childAdditionalGuestAmount.amount);", "    ", "    pm.expect(actualValue.friday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue.friday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.friday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.friday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.friday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.friday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.friday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.friday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.friday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.friday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.friday.childAdditionalGuestAmount.amount).to.eql(expectedValue.friday.childAdditionalGuestAmount.amount);", "    ", "    pm.expect(actualValue.sunday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue.sunday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.sunday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.sunday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.sunday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.sunday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.sunday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.sunday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.sunday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.sunday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.sunday.childAdditionalGuestAmount.amount).to.eql(expectedValue.sunday.childAdditionalGuestAmount.amount);", "});}//variables", "var jsonData = pm.response.json();", "var allRates =  jsonData._embedded.nucleusUnqualifiedRatePlans[0];", "", "//value to compare with", "var reservationJSONToBeCompared = {\"_embedded\":{\"nucleusUnqualifiedRatePlans\":[{\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"correlationId\":\"224c35b3-7109-4384-87e9-60860d4228ab\",\"name\":\"OTAGENA4_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-21\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"EKB\",\"originalCurrencyCode\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-13\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e8b26905f87761fb4e8fd4a\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false}]}};", "", "var reservationJSONToBeCompared1 = {\"_embedded\":{\"nucleusUnqualifiedRatePlans\":[{\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"correlationId\":\"224c35b3-7109-4384-87e9-60860d4228ab\",\"name\":\"OTAGENA4_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-21\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"FAM\",\"originalCurrencyCode\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-13\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e8b26905f87761fb4e8fd4a\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false}]}};", "", "var reservationJSONToBeCompared2 = {\"_embedded\":{\"nucleusUnqualifiedRatePlans\":[{\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"correlationId\":\"224c35b3-7109-4384-87e9-60860d4228ab\",\"name\":\"OTAGENA4_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-21\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"CTB\",\"originalCurrencyCode\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-13\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e8b26905f87761fb4e8fd4a\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false}]}};", "", "var reservationJSONToBeCompared3 = {\"_embedded\":{\"nucleusUnqualifiedRatePlans\":[{\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"correlationId\":\"224c35b3-7109-4384-87e9-60860d4228ab\",\"name\":\"OTAGENA4_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-21\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"CKC\",\"originalCurrencyCode\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-13\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e8b26905f87761fb4e8fd4a\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false}]}};", "", "var reservationJSONToBeCompared4 ={\"_embedded\":{\"nucleusUnqualifiedRatePlans\":[{\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"correlationId\":\"224c35b3-7109-4384-87e9-60860d4228ab\",\"name\":\"OTAGENA4_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-21\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"C1D\",\"originalCurrencyCode\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-13\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e8b26905f87761fb4e8fd4a\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false}]}};", "", "var reservationJSONToBeCompared5 ={\"_embedded\":{\"nucleusUnqualifiedRatePlans\":[{\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"correlationId\":\"224c35b3-7109-4384-87e9-60860d4228ab\",\"name\":\"OTAGENA4_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-21\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"SKC\",\"originalCurrencyCode\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-13\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":90909.09091,\"amountAfterTax\":100000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e8b26905f87761fb4e8fd4a\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false}]}};", "", "var reservationJSONToBeCompared6 = {\"_embedded\":{\"nucleusUnqualifiedRatePlans\":[{\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"correlationId\":\"224c35b3-7109-4384-87e9-60860d4228ab\",\"name\":\"OTAGENA4_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-21\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"PM\",\"originalCurrencyCode\":null,\"startDate\":\"2020-04-13\",\"endDate\":\"2020-04-21\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e8b26905f87761fb4e8fd4a\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false}]}};", "", "var reservationJSONToBeCompared7 ={\"_embedded\":{\"nucleusUnqualifiedRatePlans\":[{\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"correlationId\":\"224c35b3-7109-4384-87e9-60860d4228ab\",\"name\":\"OTAGENA4_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-21\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"PF\",\"originalCurrencyCode\":null,\"startDate\":\"2020-04-13\",\"endDate\":\"2020-04-21\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e8b26905f87761fb4e8fd4a\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false}]}};", "", "var reservationJSONToBeCompared8 ={\"_embedded\":{\"nucleusUnqualifiedRatePlans\":[{\"versionId\":*********,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"ratePlanType\":\"UNQUALIFIED\",\"correlationId\":\"224c35b3-7109-4384-87e9-60860d4228ab\",\"name\":\"OTAGENA4_GBP_USD\",\"notifType\":\"OVERLAY\",\"category\":null,\"yieldDeltaAmount\":null,\"marketCode\":\"OTA\",\"isInventoryAllocated\":null,\"isCommissionable\":null,\"shortDescription\":\"Flexible Rate Inc Breakfast2pm CLX\",\"longDescription\":\"Flexible Rate Inc Breakfast\",\"remarks\":null,\"originalCurrencyCode\":\"GBP\",\"postingRules\":null,\"adjustmentType\":null,\"startDate\":\"2020-04-06\",\"endDate\":\"2020-04-21\",\"yieldable\":true,\"priceRelative\":false,\"includesPackage\":true,\"taxIncluded\":true,\"derived\":null,\"rates\":[{\"invTypeCode\":\"PI\",\"originalCurrencyCode\":null,\"startDate\":\"2020-04-13\",\"endDate\":\"2020-04-21\",\"monday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"tuesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"wednesday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"thursday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"friday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"saturday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"sunday\":{\"singleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"doubleOccupantAmount\":{\"numberOfGuests\":null,\"ageQualifyingCode\":null,\"amountBeforeTax\":36363.63636,\"amountAfterTax\":40000},\"adultAdditionalGuestAmount\":null,\"childAdditionalGuestAmount\":{\"ageQualifyingCode\":\"8\",\"percent\":null,\"amount\":0}},\"status\":null,\"rateTimeUnit\":null,\"unitMultiplier\":null,\"currencyExchangeId\":\"5e8b26905f87761fb4e8fd4a\",\"currency\":null}],\"services\":[{\"serviceName\":\"BKFS\",\"includedInRate\":true,\"inclusionType\":\"INCLUDE\"}],\"currencyExchangeId\":null,\"includePayload\":false,\"derivedRateCode\":\"None\",\"qualified\":false}]}};", "", "console.log(JSON.stringify(jsonData));", "", "arr=[];", "", "arr.push(reservationJSONToBeCompared,reservationJSONToBeCompared1,reservationJSONToBeCompared2,reservationJSONToBeCompared3,reservationJSONToBeCompared4,reservationJSONToBeCompared5,reservationJSONToBeCompared6,reservationJSONToBeCompared7,reservationJSONToBeCompared8);", "", "var i=0;", "//assertions", "for(item of arr)", "{", "    assertJSONData(\"Verifying Client Code in OxiMessage\", allRates,item._embedded.nucleusUnqualifiedRatePlans[0]);", "    assertRatesData(\"Verifying rates json in OxiMessage\", allRates.rates[i],item._embedded.nucleusUnqualifiedRatePlans[0].rates[0]);", "    i++;", "}", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue.versionId).to.eql(expectedValue.versionId);", "    pm.expect(actualValue.clientCode).to.eql(expectedValue.clientCode);", "    pm.expect(actualValue.propertyCode).to.eql(expectedValue.propertyCode);", "    pm.expect(actualValue.ratePlanType).to.eql(expectedValue.ratePlanType);", "    pm.expect(actualValue.name).to.eql(expectedValue.name);", "    pm.expect(actualValue.notifType).to.eql(expectedValue.notifType);", "    pm.expect(actualValue.marketCode).to.eql(expectedValue.marketCode);", "    pm.expect(actualValue.originalCurrencyCode).to.eql(expectedValue.originalCurrencyCode);", "    pm.expect(actualValue.qualified).to.eql(expectedValue.qualified);", "    pm.expect(actualValue.services[0].serviceName).to.eql(expectedValue.services[0].serviceName);", "    pm.expect(actualValue.services[0].includedInRate).to.eql(expectedValue.services[0].includedInRate);", "    pm.expect(actualValue.services[0].inclusionType).to.eql(expectedValue.services[0].inclusionType);", "});}", "", "function assertRatesData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue.invTypeCode).to.eql(expectedValue.invTypeCode);", "    pm.expect(actualValue.monday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue.monday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.monday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.monday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.monday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.monday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.monday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.monday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.monday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.monday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.monday.childAdditionalGuestAmount.amount).to.eql(expectedValue.monday.childAdditionalGuestAmount.amount);", "    ", "    pm.expect(actualValue.tuesday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue.tuesday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.tuesday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.tuesday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.tuesday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.tuesday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.tuesday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.tuesday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.tuesday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.tuesday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.tuesday.childAdditionalGuestAmount.amount).to.eql(expectedValue.tuesday.childAdditionalGuestAmount.amount);", "    ", "    pm.expect(actualValue.wednesday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue.wednesday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.wednesday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.wednesday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.wednesday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.wednesday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.wednesday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.wednesday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.wednesday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.wednesday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.wednesday.childAdditionalGuestAmount.amount).to.eql(expectedValue.wednesday.childAdditionalGuestAmount.amount);", "    ", "    pm.expect(actualValue.thursday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue", "    .thursday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.thursday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.thursday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.thursday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.thursday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.thursday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.thursday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.thursday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.thursday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.thursday.childAdditionalGuestAmount.amount).to.eql(expectedValue.thursday.childAdditionalGuestAmount.amount);", "    ", "    pm.expect(actualValue.friday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue.friday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.friday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.friday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.friday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.friday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.friday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.friday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.friday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.friday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.friday.childAdditionalGuestAmount.amount).to.eql(expectedValue.friday.childAdditionalGuestAmount.amount);", "    ", "    pm.expect(actualValue.sunday.singleOccupantAmount.amountBeforeTax).to.eql(expectedValue.sunday.singleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.sunday.singleOccupantAmount.amountAfterTax).to.eql(expectedValue.sunday.singleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.sunday.doubleOccupantAmount.amountBeforeTax).to.eql(expectedValue.sunday.doubleOccupantAmount.amountBeforeTax);", "    pm.expect(actualValue.sunday.doubleOccupantAmount.amountAfterTax).to.eql(expectedValue.sunday.doubleOccupantAmount.amountAfterTax);", "    pm.expect(actualValue.sunday.childAdditionalGuestAmount.ageQualifyingCode).to.eql(expectedValue.sunday.childAdditionalGuestAmount.ageQualifyingCode);", "    pm.expect(actualValue.sunday.childAdditionalGuestAmount.amount).to.eql(expectedValue.sunday.childAdditionalGuestAmount.amount);", "});}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusUnqualifiedRatePlans/criteria?search=clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3Bname%3D%3DOTAGENA4_GBP_USD", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusUnqualifiedRatePlans", "criteria"], "query": [{"key": "search", "value": "clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3Bname%3D%3DOTAGENA4_GBP_USD"}]}}, "response": []}, {"name": "When Allotment message is sent to NGI which is before CurrentDate", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<?Label SandBox-OPER1|ALLOTMENT|20180611002|NEW?>\r\n<InventoryBlockNotification xmlns=\"allotment.fidelio.6.0\" inventoryBlockCodeType=\"GROUPBLOCKCODE\" inventoryBlockNotificationType=\"NEW\" inventoryBlockStatusType=\"INITIAL\" mfBlockType=\"NONELASTIC\" mfBlockMessageType=\"HEADERWITHDETAIL\" mfBookingType=\"I\" subAllotment=\"0\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:schemaLocation=\"allotment.fidelio.6.0 ../Schemas/allotment.fidelio.6.0.xsd\">\r\n\t<HotelReference>\r\n\t\t<hotelCode>SandBox-OPER1</hotelCode>\r\n\t</HotelReference>\r\n\t<inventoryBlockName>GROUP_STATUS_DEF</inventoryBlockName>\r\n\t<inventoryBlockGroupingCode>GROUP_STATUS_DEF</inventoryBlockGroupingCode>\r\n\t<inventoryBlockCodes>RESVTYPE=NON</inventoryBlockCodes>\r\n\t<BlockTimeSpan timeUnitType=\"DAY\">\r\n\t\t<startTime>2018-07-01</startTime>\r\n\t\t<numberOfTimeUnits>3</numberOfTimeUnits>\r\n\t</BlockTimeSpan>\r\n\t<priceViewable>1</priceViewable>\r\n\t<pricePrintable>1</pricePrintable>\r\n\t<AssociatedProfiles>\r\n\t\t<Profile xmlns=\"profile.fidelio.5.0\" profileType=\"GROUP\" gender=\"UNKNOWN\" multiProperty=\"0\" miniProfile=\"0\" pseudoProfile=\"0\" replaceAddress=\"0\">\r\n\t\t\t<creatorCode>SUPERVISOR</creatorCode>\r\n\t\t\t<createdDate>2018-06-19T11:05:53.000</createdDate>\r\n\t\t\t<lastUpdaterCode>SUPERVISOR</lastUpdaterCode>\r\n\t\t\t<lastUpdated>2018-12-19T11:05:53.000</lastUpdated>\r\n\t\t\t<genericName>IDEAS TEST BLOCK 3</genericName>\r\n\t\t\t<IndividualName>\r\n\t\t\t\t<nameSur>IDEAS TEST BLOCK 3</nameSur>\r\n\t\t\t</IndividualName>\r\n\t\t\t<primaryLanguageID>EN</primaryLanguageID>\r\n\t\t\t<PostalAddresses>\r\n\t\t\t\t<PostalAddress addressType=\"BUSINESS\">\r\n\t\t\t\t\t<countryCode>US</countryCode>\r\n\t\t\t\t\t<mfPrimaryYN>Y</mfPrimaryYN>\r\n\t\t\t\t\t<cleansed>0</cleansed>\r\n\t\t\t\t</PostalAddress>\r\n\t\t\t</PostalAddresses>\r\n\t\t\t<mfResort>SandBox-OPER1</mfResort>\r\n\t\t\t<mfResortProfileID>607921</mfResortProfileID>\r\n\t\t\t<mfAllowMail>NO</mfAllowMail>\r\n\t\t\t<mfAllowEMail>NO</mfAllowEMail>\r\n\t\t\t<mfGuestPriv>NO</mfGuestPriv>\r\n\t\t\t<mfAllowPhone>0</mfAllowPhone>\r\n\t\t\t<mfAllowSMS>0</mfAllowSMS>\r\n\t\t\t<SalesExtention/>\r\n\t\t\t<PrivacyOption>\r\n\t\t\t\t<mfAllowMail>N</mfAllowMail>\r\n\t\t\t\t<mfAllowEMail>N</mfAllowEMail>\r\n\t\t\t\t<mfAllowPhone>0</mfAllowPhone>\r\n\t\t\t\t<mfAllowSMS>0</mfAllowSMS>\r\n\t\t\t\t<mfAllowHistory>1</mfAllowHistory>\r\n\t\t\t</PrivacyOption>\r\n\t\t\t<ResortList>SAMPLE-HOTEL1</ResortList>\r\n\t\t\t<ResortList>293595</ResortList>\r\n\t\t\t<MultiResortEntities>\r\n\t\t\t\t<MultiResortEntity included=\"1\">\r\n\t\t\t\t\t<entity>CREDIT_CARDS</entity>\r\n\t\t\t\t</MultiResortEntity>\r\n\t\t\t\t<MultiResortEntity included=\"0\">\r\n\t\t\t\t\t<entity>RESORT_ARS</entity>\r\n\t\t\t\t</MultiResortEntity>\r\n\t\t\t\t<MultiResortEntity included=\"1\">\r\n\t\t\t\t\t<entity>COMMENTS</entity>\r\n\t\t\t\t</MultiResortEntity>\r\n\t\t\t\t<MultiResortEntity included=\"1\">\r\n\t\t\t\t\t<entity>PREFERENCES</entity>\r\n\t\t\t\t</MultiResortEntity>\r\n\t\t\t\t<MultiResortEntity included=\"1\">\r\n\t\t\t\t\t<entity>NEGOTIATED_RATES</entity>\r\n\t\t\t\t</MultiResortEntity>\r\n\t\t\t\t<MultiResortEntity included=\"1\">\r\n\t\t\t\t\t<entity>CHANNEL_ACCESS_CODES</entity>\r\n\t\t\t\t</MultiResortEntity>\r\n\t\t\t\t<MultiResortEntity included=\"1\">\r\n\t\t\t\t\t<entity>YIELD_ADJUSTMENTS</entity>\r\n\t\t\t\t</MultiResortEntity>\r\n\t\t\t\t<MultiResortEntity included=\"0\">\r\n\t\t\t\t\t<entity>RELATIONSHIPS</entity>\r\n\t\t\t\t</MultiResortEntity>\r\n\t\t\t</MultiResortEntities>\r\n\t\t</Profile>\r\n\t</AssociatedProfiles>\r\n\t<InventoryBlocks>\r\n\t\t<InventoryBlock actionType=\"NEW\">\r\n\t\t\t<inventoryCode>GRP_1</inventoryCode>\r\n\t\t\t<numberToBlock>5</numberToBlock>\r\n\t\t\t<mfInventoryDate>2018-07-01</mfInventoryDate>\r\n\t\t\t<mfNumberSold>1</mfNumberSold>\r\n\t\t\t<mfProjectedOcc1>1</mfProjectedOcc1>\r\n\t\t\t<mfCutoffDate>2018-07-01</mfCutoffDate>\r\n\t\t\t<mfRate1>100</mfRate1>\r\n\t\t\t<mfPickedUpOcc1>0</mfPickedUpOcc1>\r\n\t\t\t<mfPickedUpOcc2>0</mfPickedUpOcc2>\r\n\t\t\t<mfPickedUpOcc3>0</mfPickedUpOcc3>\r\n\t\t\t<mfPickedUpOcc4>0</mfPickedUpOcc4>\r\n\t\t\t<elastic>0</elastic>\r\n\t\t\t<fixedRate>0</fixedRate>\r\n\t\t</InventoryBlock>\r\n\t\t<InventoryBlock actionType=\"NEW\">\r\n\t\t\t<inventoryCode>GRP_1</inventoryCode>\r\n\t\t\t<numberToBlock>5</numberToBlock>\r\n\t\t\t<mfInventoryDate>2018-07-02</mfInventoryDate>\r\n\t\t\t<mfNumberSold>1</mfNumberSold>\r\n\t\t\t<mfProjectedOcc1>1</mfProjectedOcc1>\r\n\t\t\t<mfCutoffDate>2018-07-01</mfCutoffDate>\r\n\t\t\t<mfRate1>200</mfRate1>\r\n\t\t\t<mfPickedUpOcc1>0</mfPickedUpOcc1>\r\n\t\t\t<mfPickedUpOcc2>0</mfPickedUpOcc2>\r\n\t\t\t<mfPickedUpOcc3>0</mfPickedUpOcc3>\r\n\t\t\t<mfPickedUpOcc4>0</mfPickedUpOcc4>\r\n\t\t\t<elastic>0</elastic>\r\n\t\t\t<fixedRate>0</fixedRate>\r\n\t\t</InventoryBlock>\r\n\t\t<InventoryBlock actionType=\"NEW\">\r\n\t\t\t<inventoryCode>GRP_1</inventoryCode>\r\n\t\t\t<numberToBlock>5</numberToBlock>\r\n\t\t\t<mfInventoryDate>2018-07-03</mfInventoryDate>\r\n\t\t\t<mfNumberSold>1</mfNumberSold>\r\n\t\t\t<mfProjectedOcc1>1</mfProjectedOcc1>\r\n\t\t\t<mfCutoffDate>2018-07-01</mfCutoffDate>\r\n\t\t\t<mfRate1>300</mfRate1>\r\n\t\t\t<mfPickedUpOcc1>0</mfPickedUpOcc1>\r\n\t\t\t<mfPickedUpOcc2>0</mfPickedUpOcc2>\r\n\t\t\t<mfPickedUpOcc3>0</mfPickedUpOcc3>\r\n\t\t\t<mfPickedUpOcc4>0</mfPickedUpOcc4>\r\n\t\t\t<elastic>0</elastic>\r\n\t\t\t<fixedRate>0</fixedRate>\r\n\t\t</InventoryBlock>\t\t\r\n\t</InventoryBlocks>\r\n\t<mfMarketCode>MS_GRP_1</mfMarketCode>\r\n\t<mfSourceCode>SALE</mfSourceCode>\r\n\t<mfChannelCode>PMS</mfChannelCode>\r\n\t<mfCutoffDate>2018-07-01</mfCutoffDate>\r\n\t<mfCutoffDays>0</mfCutoffDays>\r\n\t<mfReservationType>NON</mfReservationType>\r\n\t<mfBookingStatus>DEF</mfBookingStatus>\r\n\t<mfCurrencyCode>GBP</mfCurrencyCode>\r\n\t<mfCateringFlag>N</mfCateringFlag>\r\n\t<Udfs>\r\n\t\t<Udf xmlns=\"udf.fidelio.2.0\">\r\n\t\t\t<UdfDefinition xmlns=\"udfdefinition.fidelio.2.0\">\r\n\t\t\t\t<pmsTableName>ALLOTMENT_HEADER</pmsTableName>\r\n\t\t\t\t<pmsColumnName>UDFC01</pmsColumnName>\r\n\t\t\t</UdfDefinition>\r\n\t\t\t<udfValue>500.00</udfValue>\r\n\t\t</Udf>\r\n\t\t<Udf xmlns=\"udf.fidelio.2.0\">\r\n\t\t\t<UdfDefinition xmlns=\"udfdefinition.fidelio.2.0\">\r\n\t\t\t\t<pmsTableName>ALLOTMENT_HEADER</pmsTableName>\r\n\t\t\t\t<pmsColumnName>CONTROL_BLOCK_YN</pmsColumnName>\r\n\t\t\t</UdfDefinition>\r\n\t\t\t<udfValue>Y</udfValue>\r\n\t\t</Udf>\r\n\t</Udfs>\r\n\t<decisionDate>2018-12-01T00:00:00.000</decisionDate>\r\n\t<Catering>\r\n\t\t<statusCode>INQ</statusCode>\r\n\t\t<attendees>0</attendees>\r\n\t\t<contractFinalized>0</contractFinalized>\r\n\t\t<detailsOk>1</detailsOk>\r\n\t</Catering>\r\n\t<fbAgendas>\r\n\t\t<fbAgenda>\r\n\t\t\t<fbaComments>\r\n\t\t\t\t<AllotmentNote/>\r\n\t\t\t</fbaComments>\r\n\t\t\t<fbaResources>\r\n\t\t\t\t<fbaResource/>\r\n\t\t\t</fbaResources>\r\n\t\t\t<fbaRevenues>\r\n\t\t\t\t<fbaRevenue/>\r\n\t\t\t</fbaRevenues>\r\n\t\t</fbAgenda>\r\n\t</fbAgendas>\r\n\t<owner>ALL</owner>\r\n\t<mfResortAllotmentId>136453</mfResortAllotmentId>\r\n\t<syncContract>0</syncContract>\r\n\t<allowAlternateDates>0</allowAlternateDates>\r\n\t<cateringStatus>INQ</cateringStatus>\r\n\t<agendaCurrencyCode>USD</agendaCurrencyCode>\r\n\t<rateOverride>0</rateOverride>\r\n\t<allotmentClassification>REGULAR_BOOKING</allotmentClassification>\r\n\t<RemoteOwners>\r\n\t\t<RemoteOwner>\r\n\t\t\t<ownerResort>293595</ownerResort>\r\n\t\t\t<ownerCode>ALL</ownerCode>\r\n\t\t\t<ownerOrigin>BOOK</ownerOrigin>\r\n\t\t\t<lastName>Supervisor</lastName>\r\n\t\t\t<firstName>Opera</firstName>\r\n\t\t\t<primaryEmail><EMAIL></primaryEmail>\r\n\t\t</RemoteOwner>\r\n\t</RemoteOwners>\r\n\t<rateProtection>N</rateProtection>\r\n\t<nonCompete>N</nonCompete>\r\n\t<rateGuaranteed>0</rateGuaranteed>\r\n\t<AllotmentEntity>\r\n\t\t<AllotDateProtection>1</AllotDateProtection>\r\n\t\t<AllotPackages>1</AllotPackages>\r\n\t</AllotmentEntity>\r\n</InventoryBlockNotification>\r\n"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/ngipublic/rest/oxi/pms", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["ngipublic", "rest", "oxi", "pms"]}}, "response": []}, {"name": "Then Allotment message is processed at NGI and saved in oxiMessages collection for future", "event": [{"listen": "test", "script": {"exec": ["//variables", "var jsonData = pm.response.json();", "jsonData = jsonData._embedded.nucleusGroupBlockMasters[0];", "", "//value to compare with", "var reservationJSONToBeCompared ={\"_embedded\":{\"nucleusGroupBlockMasters\":[{\"versionId\":20180611002,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"correlationId\":\"20180611002\",\"statisticsCorrelationId\":null,\"code\":\"GROUP_STATUS_DEF\",\"action\":null,\"name\":\"GROUP_STATUS_DEF\",\"description\":null,\"masterGroupCode\":null,\"groupStatusCode\":\"DEFINITE\",\"originalGroupStatusCode\":\"DEF\",\"marketSegmentCode\":\"MS_GRP_1\",\"analyticalMarketSegmentCode\":\"MS_GRP_1\",\"startDate\":\"2018-07-01\",\"endDate\":\"2018-07-04\",\"earliestArrivalDate\":\"2018-07-01\",\"latestDepartureDate\":\"2018-07-03\",\"shoulderStartDate\":null,\"shoulderEndDate\":null,\"groupTypeCode\":\"GROUP\",\"bookingDate\":null,\"pickupTypeCode\":\"INDV\",\"sourcePickupTypeCode\":null,\"cancelDate\":null,\"bookingType\":\"I\",\"salesPerson\":\"ALL\",\"cutoffDays\":0,\"cutoffDate\":\"2018-07-01\",\"groupId\":null,\"masterGroupId\":null,\"firstTime\":true,\"groupBlocks\":[{\"occupancyDate\":\"2018-07-01\",\"roomTypeCode\":\"GRP_1\",\"blocks\":5,\"pickup\":1,\"originalBlocks\":0,\"rate\":9090.90909,\"doubleRate\":null,\"tripleRate\":null,\"quadRate\":null,\"singleOccupancy\":1,\"doubleOccupancy\":null,\"tripleOccupancy\":null,\"quadOccupancy\":null,\"originalCurrencyCode\":\"GBP\",\"extraRate\":null,\"currencyExchangeId\":\"5e8b268a5f87761fb4e8fac5\",\"groupBlockDetailId\":null,\"blockDetailActive\":null}],\"services\":[],\"profileId\":\"607921\",\"timeStamp\":null}]}};", "", "var reservationJSONToBeCompared1 ={\"_embedded\":{\"nucleusGroupBlockMasters\":[{\"versionId\":20180611002,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"correlationId\":\"20180611002\",\"statisticsCorrelationId\":null,\"code\":\"GROUP_STATUS_DEF\",\"action\":null,\"name\":\"GROUP_STATUS_DEF\",\"description\":null,\"masterGroupCode\":null,\"groupStatusCode\":\"DEFINITE\",\"originalGroupStatusCode\":\"DEF\",\"marketSegmentCode\":\"MS_GRP_1\",\"analyticalMarketSegmentCode\":\"MS_GRP_1\",\"startDate\":\"2018-07-01\",\"endDate\":\"2018-07-04\",\"earliestArrivalDate\":\"2018-07-01\",\"latestDepartureDate\":\"2018-07-03\",\"shoulderStartDate\":null,\"shoulderEndDate\":null,\"groupTypeCode\":\"GROUP\",\"bookingDate\":null,\"pickupTypeCode\":\"INDV\",\"sourcePickupTypeCode\":null,\"cancelDate\":null,\"bookingType\":\"I\",\"salesPerson\":\"ALL\",\"cutoffDays\":0,\"cutoffDate\":\"2018-07-01\",\"groupId\":null,\"masterGroupId\":null,\"firstTime\":true,\"groupBlocks\":[{\"occupancyDate\":\"2018-07-02\",\"roomTypeCode\":\"GRP_1\",\"blocks\":5,\"pickup\":1,\"originalBlocks\":0,\"rate\":18181.81818,\"doubleRate\":null,\"tripleRate\":null,\"quadRate\":null,\"singleOccupancy\":1,\"doubleOccupancy\":null,\"tripleOccupancy\":null,\"quadOccupancy\":null,\"originalCurrencyCode\":\"GBP\",\"extraRate\":null,\"currencyExchangeId\":\"5e8b268a5f87761fb4e8fac6\",\"groupBlockDetailId\":null,\"blockDetailActive\":null}],\"services\":[],\"profileId\":\"607921\",\"timeStamp\":null}]}};", "", "var reservationJSONToBeCompared2 ={\"_embedded\":{\"nucleusGroupBlockMasters\":[{\"versionId\":20180611002,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"correlationId\":\"20180611002\",\"statisticsCorrelationId\":null,\"code\":\"GROUP_STATUS_DEF\",\"action\":null,\"name\":\"GROUP_STATUS_DEF\",\"description\":null,\"masterGroupCode\":null,\"groupStatusCode\":\"DEFINITE\",\"originalGroupStatusCode\":\"DEF\",\"marketSegmentCode\":\"MS_GRP_1\",\"analyticalMarketSegmentCode\":\"MS_GRP_1\",\"startDate\":\"2018-07-01\",\"endDate\":\"2018-07-04\",\"earliestArrivalDate\":\"2018-07-01\",\"latestDepartureDate\":\"2018-07-03\",\"shoulderStartDate\":null,\"shoulderEndDate\":null,\"groupTypeCode\":\"GROUP\",\"bookingDate\":null,\"pickupTypeCode\":\"INDV\",\"sourcePickupTypeCode\":null,\"cancelDate\":null,\"bookingType\":\"I\",\"salesPerson\":\"ALL\",\"cutoffDays\":0,\"cutoffDate\":\"2018-07-01\",\"groupId\":null,\"masterGroupId\":null,\"firstTime\":true,\"groupBlocks\":[{\"occupancyDate\":\"2018-07-03\",\"roomTypeCode\":\"GRP_1\",\"blocks\":5,\"pickup\":1,\"originalBlocks\":0,\"rate\":27272.72727,\"doubleRate\":null,\"tripleRate\":null,\"quadRate\":null,\"singleOccupancy\":1,\"doubleOccupancy\":null,\"tripleOccupancy\":null,\"quadOccupancy\":null,\"originalCurrencyCode\":\"GBP\",\"extraRate\":null,\"currencyExchangeId\":\"5e8b268a5f87761fb4e8fac7\",\"groupBlockDetailId\":null,\"blockDetailActive\":null}],\"services\":[],\"profileId\":\"607921\",\"timeStamp\":null}]}};", "", "arr=[];", "", "arr.push(reservationJSONToBeCompared,reservationJSONToBeCompared1,reservationJSONToBeCompared2);", "", "var i=0;", "//assertions", "for(item of arr)", "{", "   assertJSONData(\"Verifying Client Code in nucleus Group Block Masters\", jsonData,item._embedded.nucleusGroupBlockMasters[0]);", "   assertRatesData(\"Verifying group Blocks json in nucleus Group Block Masters\", jsonData.groupBlocks[i],item._embedded.nucleusGroupBlockMasters[0].groupBlocks[0]);", "    i++;", "}", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue.versionId).to.eql(expectedValue.versionId);", "    pm.expect(actualValue.clientCode).to.eql(expectedValue.clientCode);", "    pm.expect(actualValue.propertyCode).to.eql(expectedValue.propertyCode);", "    pm.expect(actualValue.code).to.eql(expectedValue.code);", "    pm.expect(actualValue.name).to.eql(expectedValue.name);", "    pm.expect(actualValue.groupStatusCode).to.eql(expectedValue.groupStatusCode);", "    pm.expect(actualValue.originalGroupStatusCode).to.eql(expectedValue.originalGroupStatusCode);", "    pm.expect(actualValue.marketSegmentCode).to.eql(expectedValue.marketSegmentCode);", "    pm.expect(actualValue.startDate).to.eql(expectedValue.startDate);", "    pm.expect(actualValue.endDate).to.eql(expectedValue.endDate);", "    pm.expect(actualValue.earliestArrivalDate).to.eql(expectedValue.earliestArrivalDate);", "    pm.expect(actualValue.latestDepartureDate).to.eql(expectedValue.latestDepartureDate);", "    pm.expect(actualValue.groupTypeCode).to.eql(expectedValue.groupTypeCode);", "    pm.expect(actualValue.pickupTypeCode).to.eql(expectedValue.pickupTypeCode);", "    pm.expect(actualValue.bookingType).to.eql(expectedValue.bookingType);", "    pm.expect(actualValue.salesPerson).to.eql(expectedValue.salesPerson);", "    pm.expect(actualValue.cutoffDate).to.eql(expectedValue.cutoffDate);", "    pm.expect(actualValue.firstTime).to.eql(expectedValue.firstTime);", "    pm.expect(actualValue.profileId).to.eql(expectedValue.profileId);", "});}", "", "function assertRatesData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue.occupancyDate).to.eql(expectedValue.occupancyDate);", "    pm.expect(actualValue.roomTypeCode).to.eql(expectedValue.roomTypeCode);", "    pm.expect(actualValue.blocks).to.eql(expectedValue.blocks);", "    pm.expect(actualValue.pickup).to.eql(expectedValue.pickup);", "    ", "    pm.expect(actualValue.originalBlocks).to.eql(expectedValue.originalBlocks);", "    pm.expect(actualValue.rate).to.eql(expectedValue.rate);", "    pm.expect(actualValue.singleOccupancy).to.eql(expectedValue.singleOccupancy);", "    pm.expect(actualValue.originalCurrencyCode).to.eql(expectedValue.originalCurrencyCode);", "});}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusGroupBlockMasters/search/findByClientCodeAndPropertyCodeAndCode?clientCode={{client_Code}}&propertyCode={{property_Code}}&code=GROUP_STATUS_DEF", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusGroupBlockMasters", "search", "findByClientCodeAndPropertyCodeAndCode"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "code", "value": "GROUP_STATUS_DEF"}]}}, "response": []}, {"name": "When Allotment message is sent to NGI which is after Current Date", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "text/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<?Label SandBox-OPER1|ALLOTMENT|20150611003|NEW?>\r\n<InventoryBlockNotification xmlns=\"allotment.fidelio.6.0\" inventoryBlockCodeType=\"GROUPBLOCKCODE\" inventoryBlockNotificationType=\"NEW\" inventoryBlockStatusType=\"INITIAL\" mfBlockType=\"NONELASTIC\" mfBlockMessageType=\"HEADERWITHDETAIL\" mfBookingType=\"I\" subAllotment=\"0\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:schemaLocation=\"allotment.fidelio.6.0 ../Schemas/allotment.fidelio.6.0.xsd\">\r\n\t<HotelReference>\r\n\t\t<hotelCode>SandBox-OPER1</hotelCode>\r\n\t</HotelReference>\r\n\t<inventoryBlockName>GROUP_STATUS_DEF1</inventoryBlockName>\r\n\t<inventoryBlockGroupingCode>GROUP_STATUS_DEF1</inventoryBlockGroupingCode>\r\n\t<inventoryBlockCodes>RESVTYPE=NON</inventoryBlockCodes>\r\n\t<BlockTimeSpan timeUnitType=\"DAY\">\r\n\t\t<startTime>{{yesterday}}</startTime>\r\n\t\t<numberOfTimeUnits>3</numberOfTimeUnits>\r\n\t</BlockTimeSpan>\r\n\t<priceViewable>1</priceViewable>\r\n\t<pricePrintable>1</pricePrintable>\r\n\t<AssociatedProfiles>\r\n\t\t<Profile xmlns=\"profile.fidelio.5.0\" profileType=\"GROUP\" gender=\"UNKNOWN\" multiProperty=\"0\" miniProfile=\"0\" pseudoProfile=\"0\" replaceAddress=\"0\">\r\n\t\t\t<creatorCode>SUPERVISOR</creatorCode>\r\n\t\t\t<createdDate>2019-01-19T11:05:53.000</createdDate>\r\n\t\t\t<lastUpdaterCode>SUPERVISOR</lastUpdaterCode>\r\n\t\t\t<lastUpdated>2019-01-19T11:05:53.000</lastUpdated>\r\n\t\t\t<genericName>IDEAS TEST BLOCK 3</genericName>\r\n\t\t\t<IndividualName>\r\n\t\t\t\t<nameSur>IDEAS TEST BLOCK 3</nameSur>\r\n\t\t\t</IndividualName>\r\n\t\t\t<primaryLanguageID>EN</primaryLanguageID>\r\n\t\t\t<PostalAddresses>\r\n\t\t\t\t<PostalAddress addressType=\"BUSINESS\">\r\n\t\t\t\t\t<countryCode>US</countryCode>\r\n\t\t\t\t\t<mfPrimaryYN>Y</mfPrimaryYN>\r\n\t\t\t\t\t<cleansed>0</cleansed>\r\n\t\t\t\t</PostalAddress>\r\n\t\t\t</PostalAddresses>\r\n\t\t\t<mfResort>SandBox-OPER1</mfResort>\r\n\t\t\t<mfResortProfileID>607921</mfResortProfileID>\r\n\t\t\t<mfAllowMail>NO</mfAllowMail>\r\n\t\t\t<mfAllowEMail>NO</mfAllowEMail>\r\n\t\t\t<mfGuestPriv>NO</mfGuestPriv>\r\n\t\t\t<mfAllowPhone>0</mfAllowPhone>\r\n\t\t\t<mfAllowSMS>0</mfAllowSMS>\r\n\t\t\t<SalesExtention/>\r\n\t\t\t<PrivacyOption>\r\n\t\t\t\t<mfAllowMail>N</mfAllowMail>\r\n\t\t\t\t<mfAllowEMail>N</mfAllowEMail>\r\n\t\t\t\t<mfAllowPhone>0</mfAllowPhone>\r\n\t\t\t\t<mfAllowSMS>0</mfAllowSMS>\r\n\t\t\t\t<mfAllowHistory>1</mfAllowHistory>\r\n\t\t\t</PrivacyOption>\r\n\t\t\t<ResortList>SAMPLE-HOTEL1</ResortList>\r\n\t\t\t<ResortList>293595</ResortList>\r\n\t\t\t<MultiResortEntities>\r\n\t\t\t\t<MultiResortEntity included=\"1\">\r\n\t\t\t\t\t<entity>CREDIT_CARDS</entity>\r\n\t\t\t\t</MultiResortEntity>\r\n\t\t\t\t<MultiResortEntity included=\"0\">\r\n\t\t\t\t\t<entity>RESORT_ARS</entity>\r\n\t\t\t\t</MultiResortEntity>\r\n\t\t\t\t<MultiResortEntity included=\"1\">\r\n\t\t\t\t\t<entity>COMMENTS</entity>\r\n\t\t\t\t</MultiResortEntity>\r\n\t\t\t\t<MultiResortEntity included=\"1\">\r\n\t\t\t\t\t<entity>PREFERENCES</entity>\r\n\t\t\t\t</MultiResortEntity>\r\n\t\t\t\t<MultiResortEntity included=\"1\">\r\n\t\t\t\t\t<entity>NEGOTIATED_RATES</entity>\r\n\t\t\t\t</MultiResortEntity>\r\n\t\t\t\t<MultiResortEntity included=\"1\">\r\n\t\t\t\t\t<entity>CHANNEL_ACCESS_CODES</entity>\r\n\t\t\t\t</MultiResortEntity>\r\n\t\t\t\t<MultiResortEntity included=\"1\">\r\n\t\t\t\t\t<entity>YIELD_ADJUSTMENTS</entity>\r\n\t\t\t\t</MultiResortEntity>\r\n\t\t\t\t<MultiResortEntity included=\"0\">\r\n\t\t\t\t\t<entity>RELATIONSHIPS</entity>\r\n\t\t\t\t</MultiResortEntity>\r\n\t\t\t</MultiResortEntities>\r\n\t\t</Profile>\r\n\t</AssociatedProfiles>\r\n\t<InventoryBlocks>\r\n\t\t<InventoryBlock actionType=\"NEW\">\r\n\t\t\t<inventoryCode>GRP_1</inventoryCode>\r\n\t\t\t<numberToBlock>5</numberToBlock>\r\n\t\t\t<mfInventoryDate>{{todayDate}}</mfInventoryDate>\r\n\t\t\t<mfNumberSold>1</mfNumberSold>\r\n\t\t\t<mfProjectedOcc1>1</mfProjectedOcc1>\r\n\t\t\t<mfCutoffDate>{{todayDate}}</mfCutoffDate>\r\n\t\t\t<mfRate1>100</mfRate1>\r\n\t\t\t<mfPickedUpOcc1>0</mfPickedUpOcc1>\r\n\t\t\t<mfPickedUpOcc2>0</mfPickedUpOcc2>\r\n\t\t\t<mfPickedUpOcc3>0</mfPickedUpOcc3>\r\n\t\t\t<mfPickedUpOcc4>0</mfPickedUpOcc4>\r\n\t\t\t<elastic>0</elastic>\r\n\t\t\t<fixedRate>0</fixedRate>\r\n\t\t</InventoryBlock>\r\n\t\t<InventoryBlock actionType=\"NEW\">\r\n\t\t\t<inventoryCode>GRP_1</inventoryCode>\r\n\t\t\t<numberToBlock>5</numberToBlock>\r\n\t\t\t<mfInventoryDate>{{tomorrowDate}}</mfInventoryDate>\r\n\t\t\t<mfNumberSold>1</mfNumberSold>\r\n\t\t\t<mfProjectedOcc1>1</mfProjectedOcc1>\r\n\t\t\t<mfCutoffDate>{{todayDate}}</mfCutoffDate>\r\n\t\t\t<mfRate1>200</mfRate1>\r\n\t\t\t<mfPickedUpOcc1>0</mfPickedUpOcc1>\r\n\t\t\t<mfPickedUpOcc2>0</mfPickedUpOcc2>\r\n\t\t\t<mfPickedUpOcc3>0</mfPickedUpOcc3>\r\n\t\t\t<mfPickedUpOcc4>0</mfPickedUpOcc4>\r\n\t\t\t<elastic>0</elastic>\r\n\t\t\t<fixedRate>0</fixedRate>\r\n\t\t</InventoryBlock>\r\n\t\t<InventoryBlock actionType=\"NEW\">\r\n\t\t\t<inventoryCode>GRP_1</inventoryCode>\r\n\t\t\t<numberToBlock>5</numberToBlock>\r\n\t\t\t<mfInventoryDate>{{dayAfterTomorrowDate}}</mfInventoryDate>\r\n\t\t\t<mfNumberSold>1</mfNumberSold>\r\n\t\t\t<mfProjectedOcc1>1</mfProjectedOcc1>\r\n\t\t\t<mfCutoffDate>{{todayDate}}</mfCutoffDate>\r\n\t\t\t<mfRate1>300</mfRate1>\r\n\t\t\t<mfPickedUpOcc1>0</mfPickedUpOcc1>\r\n\t\t\t<mfPickedUpOcc2>0</mfPickedUpOcc2>\r\n\t\t\t<mfPickedUpOcc3>0</mfPickedUpOcc3>\r\n\t\t\t<mfPickedUpOcc4>0</mfPickedUpOcc4>\r\n\t\t\t<elastic>0</elastic>\r\n\t\t\t<fixedRate>0</fixedRate>\r\n\t\t</InventoryBlock>\t\t\r\n\t</InventoryBlocks>\r\n\t<mfMarketCode>MS_GRP_1</mfMarketCode>\r\n\t<mfSourceCode>SALE</mfSourceCode>\r\n\t<mfChannelCode>PMS</mfChannelCode>\r\n\t<mfCutoffDate>{{todayDate}}</mfCutoffDate>\r\n\t<mfCutoffDays>0</mfCutoffDays>\r\n\t<mfReservationType>NON</mfReservationType>\r\n\t<mfBookingStatus>DEF</mfBookingStatus>\r\n\t<mfCurrencyCode>GBP</mfCurrencyCode>\r\n\t<mfCateringFlag>N</mfCateringFlag>\r\n\t<Udfs>\r\n\t\t<Udf xmlns=\"udf.fidelio.2.0\">\r\n\t\t\t<UdfDefinition xmlns=\"udfdefinition.fidelio.2.0\">\r\n\t\t\t\t<pmsTableName>ALLOTMENT_HEADER</pmsTableName>\r\n\t\t\t\t<pmsColumnName>UDFC01</pmsColumnName>\r\n\t\t\t</UdfDefinition>\r\n\t\t\t<udfValue>500.00</udfValue>\r\n\t\t</Udf>\r\n\t\t<Udf xmlns=\"udf.fidelio.2.0\">\r\n\t\t\t<UdfDefinition xmlns=\"udfdefinition.fidelio.2.0\">\r\n\t\t\t\t<pmsTableName>ALLOTMENT_HEADER</pmsTableName>\r\n\t\t\t\t<pmsColumnName>CONTROL_BLOCK_YN</pmsColumnName>\r\n\t\t\t</UdfDefinition>\r\n\t\t\t<udfValue>Y</udfValue>\r\n\t\t</Udf>\r\n\t</Udfs>\r\n\t<decisionDate>2015-12-01T00:00:00.000</decisionDate>\r\n\t<Catering>\r\n\t\t<statusCode>INQ</statusCode>\r\n\t\t<attendees>0</attendees>\r\n\t\t<contractFinalized>0</contractFinalized>\r\n\t\t<detailsOk>1</detailsOk>\r\n\t</Catering>\r\n\t<fbAgendas>\r\n\t\t<fbAgenda>\r\n\t\t\t<fbaComments>\r\n\t\t\t\t<AllotmentNote/>\r\n\t\t\t</fbaComments>\r\n\t\t\t<fbaResources>\r\n\t\t\t\t<fbaResource/>\r\n\t\t\t</fbaResources>\r\n\t\t\t<fbaRevenues>\r\n\t\t\t\t<fbaRevenue/>\r\n\t\t\t</fbaRevenues>\r\n\t\t</fbAgenda>\r\n\t</fbAgendas>\r\n\t<owner>ALL</owner>\r\n\t<mfResortAllotmentId>136453</mfResortAllotmentId>\r\n\t<syncContract>0</syncContract>\r\n\t<allowAlternateDates>0</allowAlternateDates>\r\n\t<cateringStatus>INQ</cateringStatus>\r\n\t<agendaCurrencyCode>USD</agendaCurrencyCode>\r\n\t<rateOverride>0</rateOverride>\r\n\t<allotmentClassification>REGULAR_BOOKING</allotmentClassification>\r\n\t<RemoteOwners>\r\n\t\t<RemoteOwner>\r\n\t\t\t<ownerResort>293595</ownerResort>\r\n\t\t\t<ownerCode>ALL</ownerCode>\r\n\t\t\t<ownerOrigin>BOOK</ownerOrigin>\r\n\t\t\t<lastName>Supervisor</lastName>\r\n\t\t\t<firstName>Opera</firstName>\r\n\t\t\t<primaryEmail><EMAIL></primaryEmail>\r\n\t\t</RemoteOwner>\r\n\t</RemoteOwners>\r\n\t<rateProtection>N</rateProtection>\r\n\t<nonCompete>N</nonCompete>\r\n\t<rateGuaranteed>0</rateGuaranteed>\r\n\t<AllotmentEntity>\r\n\t\t<AllotDateProtection>1</AllotDateProtection>\r\n\t\t<AllotPackages>1</AllotPackages>\r\n\t</AllotmentEntity>\r\n</InventoryBlockNotification>\r\n"}, "url": {"raw": "{{hostName}}:{{ngiPort}}/ngipublic/rest/oxi/pms", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["ngipublic", "rest", "oxi", "pms"]}}, "response": []}, {"name": "Then Allotment message is processed at NGI and saved in oxiMessages collection for past", "event": [{"listen": "test", "script": {"exec": ["//variables", "var jsonData = pm.response.json();", "jsonData = jsonData._embedded.nucleusGroupBlockMasters[0];", "", "//value to compare with", "var reservationJSONToBeCompared ={\"versionId\":20150611003,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"correlationId\":\"20150611003\",\"statisticsCorrelationId\":null,\"code\":\"GROUP_STATUS_DEF1\",\"action\":null,\"name\":\"GROUP_STATUS_DEF1\",\"description\":null,\"masterGroupCode\":null,\"groupStatusCode\":\"DEFINITE\",\"originalGroupStatusCode\":\"DEF\",\"marketSegmentCode\":\"MS_GRP_1\",\"analyticalMarketSegmentCode\":\"MS_GRP_1\",\"startDate\":\"2020-04-05\",\"endDate\":\"2020-04-08\",\"earliestArrivalDate\":\"2020-04-06\",\"latestDepartureDate\":\"2020-04-08\",\"shoulderStartDate\":null,\"shoulderEndDate\":null,\"groupTypeCode\":\"GROUP\",\"bookingDate\":null,\"pickupTypeCode\":\"INDV\",\"sourcePickupTypeCode\":null,\"cancelDate\":null,\"bookingType\":\"I\",\"salesPerson\":\"ALL\",\"cutoffDays\":0,\"cutoffDate\":\"2020-04-06\",\"groupId\":null,\"masterGroupId\":null,\"firstTime\":true,\"groupBlocks\":[{\"occupancyDate\":\"2020-04-06\",\"roomTypeCode\":\"GRP_1\",\"blocks\":5,\"pickup\":1,\"originalBlocks\":0,\"rate\":18181.81818,\"doubleRate\":null,\"tripleRate\":null,\"quadRate\":null,\"singleOccupancy\":1,\"doubleOccupancy\":null,\"tripleOccupancy\":null,\"quadOccupancy\":null,\"originalCurrencyCode\":\"GBP\",\"extraRate\":null,\"currencyExchangeId\":\"5e8b26905f87761fb4e8fd4a\",\"groupBlockDetailId\":null,\"blockDetailActive\":null}],\"services\":[],\"profileId\":\"607921\",\"timeStamp\":null};", "", "var reservationJSONToBeCompared1 ={\"versionId\":20150611003,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"correlationId\":\"20150611003\",\"statisticsCorrelationId\":null,\"code\":\"GROUP_STATUS_DEF1\",\"action\":null,\"name\":\"GROUP_STATUS_DEF1\",\"description\":null,\"masterGroupCode\":null,\"groupStatusCode\":\"DEFINITE\",\"originalGroupStatusCode\":\"DEF\",\"marketSegmentCode\":\"MS_GRP_1\",\"analyticalMarketSegmentCode\":\"MS_GRP_1\",\"startDate\":\"2020-04-05\",\"endDate\":\"2020-04-08\",\"earliestArrivalDate\":\"2020-04-06\",\"latestDepartureDate\":\"2020-04-08\",\"shoulderStartDate\":null,\"shoulderEndDate\":null,\"groupTypeCode\":\"GROUP\",\"bookingDate\":null,\"pickupTypeCode\":\"INDV\",\"sourcePickupTypeCode\":null,\"cancelDate\":null,\"bookingType\":\"I\",\"salesPerson\":\"ALL\",\"cutoffDays\":0,\"cutoffDate\":\"2020-04-06\",\"groupId\":null,\"masterGroupId\":null,\"firstTime\":true,\"groupBlocks\":[{\"occupancyDate\":\"2020-04-07\",\"roomTypeCode\":\"GRP_1\",\"blocks\":5,\"pickup\":1,\"originalBlocks\":0,\"rate\":36363.63636,\"doubleRate\":null,\"tripleRate\":null,\"quadRate\":null,\"singleOccupancy\":1,\"doubleOccupancy\":null,\"tripleOccupancy\":null,\"quadOccupancy\":null,\"originalCurrencyCode\":\"GBP\",\"extraRate\":null,\"currencyExchangeId\":\"5e8b26905f87761fb4e8fd4a\",\"groupBlockDetailId\":null,\"blockDetailActive\":null}],\"services\":[],\"profileId\":\"607921\",\"timeStamp\":null};", "", "var reservationJSONToBeCompared2 ={\"versionId\":20150611003,\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"correlationId\":\"20150611003\",\"statisticsCorrelationId\":null,\"code\":\"GROUP_STATUS_DEF1\",\"action\":null,\"name\":\"GROUP_STATUS_DEF1\",\"description\":null,\"masterGroupCode\":null,\"groupStatusCode\":\"DEFINITE\",\"originalGroupStatusCode\":\"DEF\",\"marketSegmentCode\":\"MS_GRP_1\",\"analyticalMarketSegmentCode\":\"MS_GRP_1\",\"startDate\":\"2020-04-05\",\"endDate\":\"2020-04-08\",\"earliestArrivalDate\":\"2020-04-06\",\"latestDepartureDate\":\"2020-04-08\",\"shoulderStartDate\":null,\"shoulderEndDate\":null,\"groupTypeCode\":\"GROUP\",\"bookingDate\":null,\"pickupTypeCode\":\"INDV\",\"sourcePickupTypeCode\":null,\"cancelDate\":null,\"bookingType\":\"I\",\"salesPerson\":\"ALL\",\"cutoffDays\":0,\"cutoffDate\":\"2020-04-06\",\"groupId\":null,\"masterGroupId\":null,\"firstTime\":true,\"groupBlocks\":[{\"occupancyDate\":\"2020-04-08\",\"roomTypeCode\":\"GRP_1\",\"blocks\":5,\"pickup\":1,\"originalBlocks\":0,\"rate\":54545.45455,\"doubleRate\":null,\"tripleRate\":null,\"quadRate\":null,\"singleOccupancy\":1,\"doubleOccupancy\":null,\"tripleOccupancy\":null,\"quadOccupancy\":null,\"originalCurrencyCode\":\"GBP\",\"extraRate\":null,\"currencyExchangeId\":\"5e8b26905f87761fb4e8fd4a\",\"groupBlockDetailId\":null,\"blockDetailActive\":null}],\"services\":[],\"profileId\":\"607921\",\"timeStamp\":null};", "", "arr=[];", "", "arr.push(reservationJSONToBeCompared,reservationJSONToBeCompared1,reservationJSONToBeCompared2);", "", "var i=0;", "//assertions", "for(item of arr)", "{", "   assertJSONData(\"Verifying Client Code in nucleus Group Block Masters\", jsonData,item);", "   assertRatesData(\"Verifying group Blocks json in nucleus Group Block Masters\", jsonData.groupBlocks[i],item.groupBlocks[0]);", "    i++;", "}", "//methods", "function assertJSONData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue.versionId).to.eql(expectedValue.versionId);", "    pm.expect(actualValue.clientCode).to.eql(expectedValue.clientCode);", "    pm.expect(actualValue.propertyCode).to.eql(expectedValue.propertyCode);", "    pm.expect(actualValue.code).to.eql(expectedValue.code);", "    pm.expect(actualValue.name).to.eql(expectedValue.name);", "    pm.expect(actualValue.groupStatusCode).to.eql(expectedValue.groupStatusCode);", "    pm.expect(actualValue.originalGroupStatusCode).to.eql(expectedValue.originalGroupStatusCode);", "    pm.expect(actualValue.marketSegmentCode).to.eql(expectedValue.marketSegmentCode);", "", "    pm.expect(actualValue.groupTypeCode).to.eql(expectedValue.groupTypeCode);", "    pm.expect(actualValue.pickupTypeCode).to.eql(expectedValue.pickupTypeCode);", "    pm.expect(actualValue.bookingType).to.eql(expectedValue.bookingType);", "    pm.expect(actualValue.salesPerson).to.eql(expectedValue.salesPerson);", "", "    pm.expect(actualValue.firstTime).to.eql(expectedValue.firstTime);", "    pm.expect(actualValue.profileId).to.eql(expectedValue.profileId);", "});}", "", "function assertRatesData(description,actualValue,expectedValue){", "pm.test(description, function(){", "    console.log('Actual value is - '+JSON.stringify(actualValue))", "    console.log('Expected value is - '+JSON.stringify(expectedValue));", "    pm.expect(actualValue.roomTypeCode).to.eql(expectedValue.roomTypeCode);", "    pm.expect(actualValue.blocks).to.eql(expectedValue.blocks);", "    pm.expect(actualValue.pickup).to.eql(expectedValue.pickup);", "    ", "    pm.expect(actualValue.originalBlocks).to.eql(expectedValue.originalBlocks);", "    pm.expect(actualValue.rate).to.eql(expectedValue.rate);", "    pm.expect(actualValue.singleOccupancy).to.eql(expectedValue.singleOccupancy);", "    pm.expect(actualValue.originalCurrencyCode).to.eql(expectedValue.originalCurrencyCode);", "});}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusGroupBlockMasters/search/findByClientCodeAndPropertyCodeAndCode?clientCode={{client_Code}}&propertyCode={{property_Code}}&code=GROUP_STATUS_DEF1", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusGroupBlockMasters", "search", "findByClientCodeAndPropertyCodeAndCode"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "code", "value": "GROUP_STATUS_DEF1"}]}}, "response": []}, {"name": "When next BDE arrives for 01-01-2018", "event": [{"listen": "test", "script": {"exec": ["pm.globals.unset(\"fiscal_Date\");", "pm.globals.set(\"fiscal_Date\",\"2018-01-01\");", "", "//verifing status", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{hostName}}:{{ngiPort}}/oxi/admin/pms/endOfDay?propertyCode=OPER1&clientCode=SandBox&fiscalDate=2018-01-01T02:01:01-04:00", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["oxi", "admin", "pms", "endOfDay"], "query": [{"key": "propertyCode", "value": "OPER1"}, {"key": "clientCode", "value": "SandBox"}, {"key": "fiscalDate", "value": "2018-01-01T02:01:01-04:00"}]}}, "response": []}, {"name": "Then unique correlationId populates in nucleusStatisticsCorrelations", "event": [{"listen": "test", "script": {"exec": ["", "var jsonData = pm.response.json();", "", "function pollDOM () {", "   console.log(\"current status is\" + jsonData.status)", "  if (jsonData.status == \"SENT_FOR_PROCESSING\"|| jsonData.status == \"PROCESSED\") {", "    //Setting global variables", "    pm.globals.unset(\"corrID\");", "    pm.globals.set(\"corrID\",jsonData.id);", "    pm.globals.set(\"JobType\",\"NGIDeferredDeliveryJob\");", "    value = jsonData.activityStats.CURRENT_ROOM_TYPE_MARKET_SEGMENT.correlationId;", "    pm.globals.unset(\"correlationId\");", "    pm.globals.set(\"correlationId\",value);", "    console.log('query fetched from parameter corrID is -'+pm.globals.get(\"corrID\"));", "    //Setting JobID", "    eval(pm.globals.get(\"getJobID\"));", "  } else {", "      pm.sendRequest({url: pm.globals.get('hostName')+':'+pm.globals.get('ngiPort')+'/nucleusStatisticsCorrelations/search/findByClientCodeAndPropertyCodeAndFiscalDate?clientCode='+ pm.globals.get('client_Code')+'&propertyCode='+pm.globals.get('property_Code')+'&fiscalDate='+pm.globals.get('fiscal_Date'),method: 'GET',header:{'Authorization': 'Basic ********************************','Accept': '*/*'}}, function (err, res) {", "            jsonData = res.json();", "            console.log(\"Status Inside: \" + jsonData.status);", "            console.log(\"Response\" + JSON.stringify(res.json()));", "        });", "    jsonData = pm.response.json();", "    console.log(\"Inside Data\" + JSON.stringify(jsonData));", "    setTimeout(pollDOM, 2000); // try again in 300 milliseconds", "  }", "}", "pollDOM();"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var millisecondsToWait = 5000;\r", "setTimeout(function() {\r", "    // Whatever you want to do after the wait\r", "}, millisecondsToWait);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusStatisticsCorrelations/search/findByClientCodeAndPropertyCodeAndFiscalDate?clientCode={{client_Code}}&propertyCode={{property_Code}}&fiscalDate={{fiscal_Date}}", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusStatisticsCorrelations", "search", "findByClientCodeAndPropertyCodeAndFiscalDate"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "fiscalDate", "value": "{{fiscal_Date}}"}]}}, "response": []}, {"name": "Then NGIDeferredDelivery Job gets triggered in Monitoring Dashboard", "event": [{"listen": "test", "script": {"exec": ["eval(pm.globals.get(\"waitForJobToComplete\"));", "", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var millisecondsToWait = 5000;\r", "setTimeout(function() {\r", "    // Whatever you want to do after the wait\r", "}, millisecondsToWait);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "propertyid", "type": "text", "value": "5"}, {"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{ \"query\": \"select EXECUTION_STATUS from [Job].[dbo].JOB_STATE where job_instance_id={{jobId}}\"\n}"}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/G3Tables/executeQuery/v1", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "G3Tables", "execute<PERSON>uery"]}}, "response": []}, {"name": "Then NGI updates nucleusRoomTypeMarketSegmentActivity collection", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();", "", "jsonData = jsonData._embedded.nucleusRoomTypeMarketSegmentShardedActivities;", "", "", "var nucleusRoomTypeActivityJSON = {\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"roomsSold\":0,\"arrivals\":0,\"departures\":1,\"cancellations\":null,\"noShows\":null,\"roomRevenue\":0,\"foodRevenue\":0,\"totalRevenue\":0,\"occupancyDate\":\"2018-07-27 00:00:00.000\",\"fiscalDate\":\"2018-01-01 00:00:00.000\",\"correlationId\":\"3bc1a9c3-3084-4d3f-8caa-52c945104568\",\"noShowRoomRevenue\":null,\"marketSegmentCode\":\"OTA\",\"roomTypeCode\":\"CKC\",\"provided\":false};", "", "var nucleusRoomTypeActivityJSON1 = {\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"roomsSold\":1,\"arrivals\":1,\"departures\":0,\"cancellations\":null,\"noShows\":null,\"roomRevenue\":15363.63636,\"foodRevenue\":0,\"totalRevenue\":15363.63636,\"occupancyDate\":\"2018-07-26 00:00:00.000\",\"fiscalDate\":\"2018-01-01 00:00:00.000\",\"correlationId\":\"3bc1a9c3-3084-4d3f-8caa-52c945104568\",\"noShowRoomRevenue\":null,\"marketSegmentCode\":\"OTA\",\"roomTypeCode\":\"CKC\",\"provided\":false};", "", "var nucleusRoomTypeActivityJSON2 = {\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"roomsSold\":0,\"arrivals\":0,\"departures\":4,\"cancellations\":null,\"noShows\":null,\"roomRevenue\":0,\"foodRevenue\":0,\"totalRevenue\":0,\"occupancyDate\":\"2018-07-04 00:00:00.000\",\"fiscalDate\":\"2018-01-01 00:00:00.000\",\"correlationId\":\"3bc1a9c3-3084-4d3f-8caa-52c945104568\",\"noShowRoomRevenue\":null,\"marketSegmentCode\":\"MS_GRP_1\",\"roomTypeCode\":\"GRP_1\",\"provided\":false};", "", "var nucleusRoomTypeActivityJSON3 = {\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"roomsSold\":4,\"arrivals\":0,\"departures\":0,\"cancellations\":null,\"noShows\":null,\"roomRevenue\":27272.72727,\"foodRevenue\":0,\"totalRevenue\":27272.72727,\"occupancyDate\":\"2018-07-03 00:00:00.000\",\"fiscalDate\":\"2018-01-01 00:00:00.000\",\"correlationId\":\"3bc1a9c3-3084-4d3f-8caa-52c945104568\",\"noShowRoomRevenue\":null,\"marketSegmentCode\":\"MS_GRP_1\",\"roomTypeCode\":\"GRP_1\",\"provided\":false};", "", "var nucleusRoomTypeActivityJSON4 = {\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"roomsSold\":4,\"arrivals\":0,\"departures\":0,\"cancellations\":null,\"noShows\":null,\"roomRevenue\":18181.81818,\"foodRevenue\":0,\"totalRevenue\":18181.81818,\"occupancyDate\":\"2018-07-02 00:00:00.000\",\"fiscalDate\":\"2018-01-01 00:00:00.000\",\"correlationId\":\"3bc1a9c3-3084-4d3f-8caa-52c945104568\",\"noShowRoomRevenue\":null,\"marketSegmentCode\":\"MS_GRP_1\",\"roomTypeCode\":\"GRP_1\",\"provided\":false};", "", "var nucleusRoomTypeActivityJSON5 = {\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"roomsSold\":4,\"arrivals\":4,\"departures\":0,\"cancellations\":null,\"noShows\":null,\"roomRevenue\":9090.90909,\"foodRevenue\":0,\"totalRevenue\":9090.90909,\"occupancyDate\":\"2018-07-01 00:00:00.000\",\"fiscalDate\":\"2018-01-01 00:00:00.000\",\"correlationId\":\"3bc1a9c3-3084-4d3f-8caa-52c945104568\",\"noShowRoomRevenue\":null,\"marketSegmentCode\":\"MS_GRP_1\",\"roomTypeCode\":\"GRP_1\",\"provided\":false};", "", "arr = [];", "", "arr.push(nucleusRoomTypeActivityJSON,nucleusRoomTypeActivityJSON1,nucleusRoomTypeActivityJSON2,nucleusRoomTypeActivityJSON3,nucleusRoomTypeActivityJSON4,nucleusRoomTypeActivityJSON5);", "", "console.log(\"Complete object: \" + arr );", "", "i=0;", "for (item of arr) {", "    pm.globals.unset(\"actualjson\");", "    pm.globals.set(\"actualjson\", jsonData);", "    pm.globals.unset(\"expectedjson\");", "    pm.globals.set(\"expectedjson\", item);", "    eval(pm.globals.get(\"VerifynucleusRTMSActivities\"));", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusRoomTypeMarketSegmentShardedActivities/criteria?search=clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BcorrelationId%3D%3D{{correlationId}}&sort=occupancyDate,desc", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusRoomTypeMarketSegmentShardedActivities", "criteria"], "query": [{"key": "search", "value": "clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BcorrelationId%3D%3D{{correlationId}}"}, {"key": "sort", "value": "occupancyDate,desc"}]}}, "response": []}, {"name": "Then the individualTrans Table in G3 gets updated", "event": [{"listen": "test", "script": {"exec": ["//variables", "var jsonData = pm.response.json();", "", "pm.globals.set(\"File_Metadata_ID\",jsonData[0][0]);", "pm.globals.set(\"Mkt_Seg_ID\",jsonData[0][6]);", "", "for (var i=0;i < Object.keys(jsonData).length;i++)", "{", "    delete jsonData[i][0];", "}", "", "//Declaring Expected variables", "var individualTransData = ('[null,\"458952088\",\"CO\",\"'+ pm.globals.get(\"tomorrowDate\") +'\",\"' + pm.globals.get(\"dayAfterTomorrowDate\") + '\",\"2018-07-31\",null,\"FAM\",\"OTA\",\"CKC\",153.63635,0,0,0,0,153.63635,\"BC\",\"GB\",\"OTAGENA1\",153.63635,\"311\",\"CHECKED IN\",2,2,null,\"OTA\"]');", "", "var individualTransData1 = ('[null,\"458952089\",\"CO\",\"'+ pm.globals.get(\"todayDate\") +'\",\"'+ pm.globals.get(\"tomorrowDate\") +'\",\"'+ pm.globals.get(\"todayDate\") +'\",null,\"FAM\",\"OTA\",\"CKC\",153.63635,0,0,0,0,153.63635,\"BC\",\"GB\",\"OTAGENA1\",153.63635,\"311\",\"CHECKED IN\",2,2,null,\"OTA\"]');", "", "var individualTransData2 = ('[null,\"458952090\",\"CO\",\"2018-07-26\",\"2018-07-27\",\"2019-01-22\",null,\"FAM\",\"OTA\",\"CKC\",153.63635,0,0,0,0,153.63635,\"BC\",\"GB\",\"OTAGENA1\",153.63635,\"311\",\"CHECKED IN\",2,2,null,\"OTA\"]');", "", "console.log('Actual value is - '+JSON.stringify(jsonData));", "", "//Creating array of positive cases", "arr = [];", "arr.push(individualTransData,individualTransData1,individualTransData2);", "", "//assertions for positive cases", "for (item of arr) {", "     pm.globals.unset(\"actualjson\");", "    pm.globals.set(\"actualjson\", jsonData);", "    pm.globals.unset(\"expectedjson\");", "    pm.globals.set(\"expectedjson\", item);", "    eval(pm.globals.get(\"VerifyG3TableValue\"));", "}"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["pm.globals.unset(\"whereCondition\");\r", "pm.globals.set(\"whereCondition\", \" where reservation_identifier in ('458952088','458952089','458952090') and Individual_Status='CO' \");\r", "var millisecondsToWait = 5000;\r", "setTimeout(function() {\r", "    // Whatever you want to do after the wait\r", "}, millisecondsToWait);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "propertyId", "type": "text", "value": "5"}], "body": {"mode": "raw", "raw": "{ \"query\": \"{{individualTransQuery}}\"\n}"}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/G3Tables/executeQuery/v1", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "G3Tables", "execute<PERSON>uery"]}}, "response": []}, {"name": "Then Mkt_Accom_Activity Table gets populated with solds", "event": [{"listen": "test", "script": {"exec": ["//variables", "var jsonData = pm.response.json();", "", "console.log(JSON.stringify(jsonData));", "", "var Mkt_Accom_Activity = ('[\"2018-07-01\",\"MS_GRP_1\",\"GRP_1\",4,4,0,0,0,90.90909,0,90.90909,0]');", "", "var Mkt_Accom_Activity1 = ('[\"2018-07-02\",\"MS_GRP_1\",\"GRP_1\",4,0,0,0,0,181.81818,0,181.81818,0]');", "", "var Mkt_Accom_Activity2 = ('[\"2018-07-03\",\"MS_GRP_1\",\"GRP_1\",4,0,0,0,0,272.72727,0,272.72727,0]');", "", "var Mkt_Accom_Activity3 = ('[\"2018-07-04\",\"MS_GRP_1\",\"GRP_1\",0,0,4,0,0,0,0,0,0]');", "", "var Mkt_Accom_Activity4 = ('[\"2018-07-26\",\"OTA\",\"CKC\",1,1,0,0,0,153.63636,0,153.63636,0]');", "", "var Mkt_Accom_Activity5 = ('[\"2018-07-27\",\"OTA\",\"CKC\",0,0,1,0,0,0,0,0,0]');", "", "", "var arr=[];", "", "//assertions", "arr.push(Mkt_Accom_Activity,Mkt_Accom_Activity1,Mkt_Accom_Activity2,Mkt_Accom_Activity3,Mkt_Accom_Activity4,Mkt_Accom_Activity5);", "", "", "//Calling assertions", "for (item of arr) {", "    pm.globals.unset(\"actualjson\");", "    pm.globals.set(\"actualjson\", jsonData);", "    pm.globals.unset(\"expectedjson\");", "    pm.globals.set(\"expectedjson\", item);", "    eval(pm.globals.get(\"VerifyG3TableValue\"));", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "propertyId", "type": "text", "value": "5"}], "body": {"mode": "raw", "raw": "{ \"query\": \"{{mktAccomActivityQuery}}\"\n}"}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/G3Tables/executeQuery/v1", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "G3Tables", "execute<PERSON>uery"]}}, "response": []}, {"name": "When next BDE arrives for Current Year", "event": [{"listen": "test", "script": {"exec": ["pm.globals.unset(\"fiscal_Date\");", "pm.globals.set(\"fiscal_Date\",pm.globals.get(\"currentYear\")+\"-01-01\");", "", "//verifing status", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{hostName}}:{{ngiPort}}/oxi/admin/pms/endOfDay?propertyCode=OPER1&clientCode=SandBox&fiscalDate={{currentYear}}-01-01T02:01:01-04:00", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["oxi", "admin", "pms", "endOfDay"], "query": [{"key": "propertyCode", "value": "OPER1"}, {"key": "clientCode", "value": "SandBox"}, {"key": "fiscalDate", "value": "{{currentYear}}-01-01T02:01:01-04:00"}]}}, "response": []}, {"name": "Then unique correlationId populates in nucleusStatisticsCorrelations", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();", "", "function pollDOM () {", "   console.log(\"current status is\" + jsonData.status)", "  if (jsonData.status == \"SENT_FOR_PROCESSING\"|| jsonData.status == \"PROCESSED\") {", "    //Setting global variables", "    pm.globals.unset(\"corrID\");", "    pm.globals.set(\"corrID\",jsonData.id);", "    pm.globals.set(\"JobType\",\"NGIDeferredDeliveryJob\");", "    value = jsonData.activityStats.CURRENT_ROOM_TYPE_MARKET_SEGMENT.correlationId;", "    pm.globals.unset(\"correlationId\");", "    pm.globals.set(\"correlationId\",value);", "    console.log('query fetched from parameter corrID is -'+pm.globals.get(\"corrID\"));", "    //Setting JobID", "    eval(pm.globals.get(\"getJobID\"));", "  } else {", "      pm.sendRequest({url: pm.globals.get('hostName')+':'+pm.globals.get('ngiPort')+'/nucleusStatisticsCorrelations/search/findByClientCodeAndPropertyCodeAndFiscalDate?clientCode='+ pm.globals.get('client_Code')+'&propertyCode='+pm.globals.get('property_Code')+'&fiscalDate='+pm.globals.get('fiscal_Date'),method: 'GET',header:{'Authorization': 'Basic ********************************','Accept': '*/*'}}, function (err, res) {", "            jsonData = res.json();", "            console.log(\"Status Inside: \" + jsonData.status);", "            console.log(\"Response\" + JSON.stringify(res.json()));", "        });", "    jsonData = pm.response.json();", "    console.log(\"Inside Data\" + JSON.stringify(jsonData));", "    setTimeout(pollDOM, 2000); // try again in 300 milliseconds", "  }", "}", "pollDOM();"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var millisecondsToWait = 5000;\r", "setTimeout(function() {\r", "    // Whatever you want to do after the wait\r", "}, millisecondsToWait);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusStatisticsCorrelations/search/findByClientCodeAndPropertyCodeAndFiscalDate?clientCode={{client_Code}}&propertyCode={{property_Code}}&fiscalDate={{fiscal_Date}}", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusStatisticsCorrelations", "search", "findByClientCodeAndPropertyCodeAndFiscalDate"], "query": [{"key": "clientCode", "value": "{{client_Code}}"}, {"key": "propertyCode", "value": "{{property_Code}}"}, {"key": "fiscalDate", "value": "{{fiscal_Date}}"}]}}, "response": []}, {"name": "Then NGIDeferredDelivery Job gets triggered in Monitoring Dashboard", "event": [{"listen": "test", "script": {"exec": ["eval(pm.globals.get(\"waitForJobToComplete\"));", "", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "propertyid", "type": "text", "value": "5"}, {"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{ \"query\": \"select EXECUTION_STATUS from [Job].[dbo].JOB_STATE where job_instance_id={{jobId}}\"\n}"}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/G3Tables/executeQuery/v1", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "G3Tables", "execute<PERSON>uery"]}}, "response": []}, {"name": "Then NGI updates nucleusRoomTypeMarketSegmentActivity collection", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();", "", "jsonData = jsonData._embedded.nucleusRoomTypeMarketSegmentShardedActivities;", "console.log(JSON.stringify(jsonData));", "", "var nucleusRoomTypeActivityJSON = JSON.parse('{\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"roomsSold\":0,\"arrivals\":0,\"departures\":4,\"cancellations\":null,\"noShows\":null,\"roomRevenue\":0,\"foodRevenue\":0,\"totalRevenue\":0,\"occupancyDate\":\"'+ pm.globals.get('dayAfter3Days') +' 00:00:00.000\",\"fiscalDate\":\"2021-01-01 00:00:00.000\",\"noShowRoomRevenue\":null,\"marketSegmentCode\":\"MS_GRP_1\",\"roomTypeCode\":\"GRP_1\",\"provided\":false}');", "", "var nucleusRoomTypeActivityJSON1 = JSON.parse('{\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"roomsSold\":4,\"arrivals\":0,\"departures\":0,\"cancellations\":null,\"noShows\":null,\"roomRevenue\":54545.45455,\"foodRevenue\":0,\"totalRevenue\":54545.45455,\"occupancyDate\":\"'+ pm.globals.get('dayAfterTomorrowDate') +' 00:00:00.000\",\"fiscalDate\":\"2021-01-01 00:00:00.000\",\"noShowRoomRevenue\":null,\"marketSegmentCode\":\"MS_GRP_1\",\"roomTypeCode\":\"GRP_1\",\"provided\":false}');", "", "var nucleusRoomTypeActivityJSON2 = JSON.parse('{\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"roomsSold\":0,\"arrivals\":0,\"departures\":1,\"cancellations\":null,\"noShows\":null,\"roomRevenue\":0,\"foodRevenue\":0,\"totalRevenue\":0,\"occupancyDate\":\"'+ pm.globals.get('dayAfterTomorrowDate') +' 00:00:00.000\",\"fiscalDate\":\"2021-01-01 00:00:00.000\",\"noShowRoomRevenue\":null,\"marketSegmentCode\":\"OTA\",\"roomTypeCode\":\"CKC\",\"provided\":false}');", "", "var nucleusRoomTypeActivityJSON3 = JSON.parse('{\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"roomsSold\":4,\"arrivals\":0,\"departures\":0,\"cancellations\":null,\"noShows\":null,\"roomRevenue\":36363.63636,\"foodRevenue\":0,\"totalRevenue\":36363.63636,\"occupancyDate\":\"'+ pm.globals.get('tomorrowDate') +' 00:00:00.000\",\"fiscalDate\":\"2021-01-01 00:00:00.000\",\"noShowRoomRevenue\":null,\"marketSegmentCode\":\"MS_GRP_1\",\"roomTypeCode\":\"GRP_1\",\"provided\":false}');", "", "var nucleusRoomTypeActivityJSON4 = JSON.parse('{\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"roomsSold\":1,\"arrivals\":1,\"departures\":1,\"cancellations\":null,\"noShows\":null,\"roomRevenue\":30727.27273,\"foodRevenue\":0,\"totalRevenue\":30727.27273,\"occupancyDate\":\"'+ pm.globals.get('tomorrowDate') +' 00:00:00.000\",\"fiscalDate\":\"2021-01-01 00:00:00.000\",\"noShowRoomRevenue\":null,\"marketSegmentCode\":\"OTA\",\"roomTypeCode\":\"CKC\",\"provided\":false}');", "", "var nucleusRoomTypeActivityJSON5 = JSON.parse('{\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"roomsSold\":4,\"arrivals\":4,\"departures\":0,\"cancellations\":null,\"noShows\":null,\"roomRevenue\":18181.81818,\"foodRevenue\":0,\"totalRevenue\":18181.81818,\"occupancyDate\":\"'+ pm.globals.get('todayDate') +' 00:00:00.000\",\"fiscalDate\":\"2021-01-01 00:00:00.000\",\"noShowRoomRevenue\":null,\"marketSegmentCode\":\"MS_GRP_1\",\"roomTypeCode\":\"GRP_1\",\"provided\":false}');", "", "var nucleusRoomTypeActivityJSON6 = JSON.parse('{\"clientCode\":\"SandBox\",\"propertyCode\":\"OPER1\",\"roomsSold\":1,\"arrivals\":1,\"departures\":0,\"cancellations\":null,\"noShows\":null,\"roomRevenue\":30727.27273,\"foodRevenue\":0,\"totalRevenue\":30727.27273,\"occupancyDate\":\"'+ pm.globals.get('todayDate') +' 00:00:00.000\",\"fiscalDate\":\"2021-01-01 00:00:00.000\",\"noShowRoomRevenue\":null,\"marketSegmentCode\":\"OTA\",\"roomTypeCode\":\"CKC\",\"provided\":false}');", "", "arr = [];", "", "arr.push(nucleusRoomTypeActivityJSON,nucleusRoomTypeActivityJSON1,nucleusRoomTypeActivityJSON2,nucleusRoomTypeActivityJSON3,nucleusRoomTypeActivityJSON4,nucleusRoomTypeActivityJSON5,nucleusRoomTypeActivityJSON6);", "", "console.log(\"Complete object: \" + arr );", "", "for (item of arr) {", "    pm.globals.unset(\"actualjson\");", "    pm.globals.set(\"actualjson\", jsonData);", "    pm.globals.unset(\"expectedjson\");", "    pm.globals.set(\"expectedjson\", item);", "    eval(pm.globals.get(\"VerifynucleusRTMSActivities\"));", "}", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{hostName}}:{{ngiPort}}/nucleusRoomTypeMarketSegmentShardedActivities/criteria?search=clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BcorrelationId%3D%3D{{correlationId}}&sort=occupancyDate,roomTypeCode,desc", "host": ["{{hostName}}"], "port": "{{ngiPort}}", "path": ["nucleusRoomTypeMarketSegmentShardedActivities", "criteria"], "query": [{"key": "search", "value": "clientCode%3D%3D{{client_Code}}%3BpropertyCode%3D%3D{{property_Code}}%3BcorrelationId%3D%3D{{correlationId}}"}, {"key": "sort", "value": "occupancyDate,roomTypeCode,desc"}]}}, "response": []}, {"name": "Then the individualTrans Table in G3 gets updated with CANCEL info", "event": [{"listen": "test", "script": {"exec": ["//variables", "var jsonData = pm.response.json();", "", "pm.globals.set(\"File_Metadata_ID\",jsonData[0][0]);", "pm.globals.set(\"Mkt_Seg_ID\",jsonData[0][6]);", "", "for (var i=0;i < Object.keys(jsonData).length;i++)", "{", "    delete jsonData[i][0];", "}", "", "//Declaring Expected variables", "var individualTransData = ('[null,\"458952088\",\"CO\",\"'+ pm.globals.get(\"tomorrowDate\") +'\",\"' + pm.globals.get(\"dayAfterTomorrowDate\") + '\",\"2018-07-31\",null,\"FAM\",\"OTA\",\"CKC\",153.63635,0,0,0,0,153.63635,\"BC\",\"GB\",\"OTAGENA1\",153.63635,\"311\",\"CHECKED IN\",2,2,null,\"OTA\"]');", "", "var individualTransData1 = ('[null,\"458952089\",\"CO\",\"'+ pm.globals.get(\"todayDate\") +'\",\"'+ pm.globals.get(\"tomorrowDate\") +'\",\"'+ pm.globals.get(\"todayDate\") +'\",null,\"FAM\",\"OTA\",\"CKC\",153.63635,0,0,0,0,153.63635,\"BC\",\"GB\",\"OTAGENA1\",153.63635,\"311\",\"CHECKED IN\",2,2,null,\"OTA\"]');", "", "var individualTransData2 = ('[null,\"458952090\",\"CO\",\"2018-07-26\",\"2018-07-27\",\"2019-01-22\",null,\"FAM\",\"OTA\",\"CKC\",153.63635,0,0,0,0,153.63635,\"BC\",\"GB\",\"OTAGENA1\",153.63635,\"311\",\"CHECKED IN\",2,2,null,\"OTA\"]');", "", "console.log('Actual value is - '+JSON.stringify(jsonData));", "", "//Creating array of positive cases", "arr = [];", "arr.push(individualTransData,individualTransData1,individualTransData2);", "", "//assertions for positive cases", "for (item of arr) {", "    pm.globals.unset(\"actualjson\");", "    pm.globals.set(\"actualjson\", jsonData);", "    pm.globals.unset(\"expectedjson\");", "    pm.globals.set(\"expectedjson\", item);", "    eval(pm.globals.get(\"VerifyG3TableValue\"));", "}"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["pm.globals.unset(\"whereCondition\");\r", "pm.globals.set(\"whereCondition\", \" where reservation_identifier in ('458952088','458952089','458952090') and Individual_Status='CO' \");\r", "var millisecondsToWait = 5000;\r", "setTimeout(function() {\r", "    // Whatever you want to do after the wait\r", "}, millisecondsToWait);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "propertyId", "type": "text", "value": "5"}], "body": {"mode": "raw", "raw": "{ \"query\": \"{{individualTransQuery}}\"\n}"}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/G3Tables/executeQuery/v1", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "G3Tables", "execute<PERSON>uery"]}}, "response": []}, {"name": "Then the Reservation_Night Table in G3 gets populated with CANCEL info as well", "event": [{"listen": "test", "script": {"exec": ["//variables", "var jsonData = pm.response.json();", "", "pm.globals.set(\"File_Metadata_ID\",jsonData[0][0]);", "pm.globals.set(\"Mkt_Seg_ID\",jsonData[0][10]);", "", "for (var i=0;i < Object.keys(jsonData).length;i++)", "{", "    delete jsonData[i][0];", "}", "", "var reservationNightsData = ('[null,\"458952088\",\"CHECKED_OUT\",\"'+ pm.globals.get(\"tomorrowDate\") +'\",\"' + pm.globals.get(\"dayAfterTomorrowDate\") + '\",\"2018-07-31\",null,\"FAM\",\"OTA\",\"CKC\",153.63635,0,0,0,0,153.63635,\"BC\",\"GB\",\"OTAGENA1\",153.63635,\"311\",\"CHECKED IN\",2,2,null,\"OTA\",null,null,\"OTA\"]');", "", "var reservationNightsData1 = ('[null,\"458952089\",\"CHECKED_OUT\",\"'+ pm.globals.get(\"todayDate\") +'\",\"'+ pm.globals.get(\"tomorrowDate\") +'\",\"'+ pm.globals.get(\"todayDate\") +'\",null,\"FAM\",\"OTA\",\"CKC\",153.63635,0,0,0,0,153.63635,\"BC\",\"GB\",\"OTAGENA1\",153.63635,\"311\",\"CHECKED IN\",2,2,null,\"OTA\",null,null,\"OTA\"]');", "", "var reservationNightsData2 = ('[null,\"458952090\",\"CHECKED_OUT\",\"2018-07-26\",\"2018-07-27\",\"2019-01-22\",null,\"FAM\",\"OTA\",\"CKC\",153.63635,0,0,0,0,153.63635,\"BC\",\"GB\",\"OTAGENA1\",153.63635,\"311\",\"CHECKED IN\",2,2,null,\"OTA\",null,null,\"OTA\"]');", "", "console.log('Actual value is - '+JSON.stringify(jsonData));", "", "//Creating array of positive cases", "arr = [];", "arr.push(reservationNightsData,reservationNightsData1,reservationNightsData2);", "", "//assertions for positive cases", "for (item of arr) {", "    pm.globals.unset(\"actualjson\");", "    pm.globals.set(\"actualjson\", jsonData);", "    pm.globals.unset(\"expectedjson\");", "    pm.globals.set(\"expectedjson\", item);", "    eval(pm.globals.get(\"VerifyG3TableValue\"));", "}", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["pm.globals.unset(\"whereCondition\");\r", "pm.globals.set(\"whereCondition\", \" where reservation_identifier in ('458952088','458952089','458952090') \");"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "propertyId", "type": "text", "value": "5"}], "body": {"mode": "raw", "raw": "{ \"query\": \"{{reservationNightQuery}}\"\n}"}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/G3Tables/executeQuery/v1", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "G3Tables", "execute<PERSON>uery"]}}, "response": []}, {"name": "Then Mkt_Accom_Activity Table gets populated with solds", "event": [{"listen": "test", "script": {"exec": ["//variables", "var jsonData = pm.response.json();", "", "console.log(JSON.stringify(jsonData));", "", "var Mkt_Accom_Activity = ('[\"' + pm.globals.get(\"todayDate\") +'\",\"MS_GRP_1\",\"GRP_1\",4,4,0,0,0,90.90909,0,90.90909,0]');", "", "var Mkt_Accom_Activity1 = ('[\"' + pm.globals.get(\"todayDate\") +'\",\"OTA\",\"CKC\",1,1,0,0,0,153.63636,0,153.63636,0]');", "", "var Mkt_Accom_Activity2 = ('[\"' + pm.globals.get(\"tomorrowDate\") +'\",\"MS_GRP_1\",\"GRP_1\",4,0,0,0,0,181.81818,0,181.81818,0]');", "", "var Mkt_Accom_Activity3 = ('[\"' + pm.globals.get(\"tomorrowDate\") +'\",\"OTA\",\"CKC\",1,1,1,0,0,153.63636,0,153.63636,0]');", "", "var Mkt_Accom_Activity4 = ('[\"' + pm.globals.get(\"dayAfterTomorrowDate\") +'\",\"MS_GRP_1\",\"GRP_1\",4,0,0,0,0,272.72727,0,272.72727,0]');", "", "var Mkt_Accom_Activity5 = ('[\"' + pm.globals.get(\"dayAfterTomorrowDate\") +'\",\"OTA\",\"CKC\",0,0,1,0,0,0,0,0,0]');", "", "var Mkt_Accom_Activity6 = ('[\"' + pm.globals.get(\"dayAfter3Days\") +'\",\"MS_GRP_1\",\"GRP_1\",0,0,4,0,0,0,0,0,0]');", "", "", "var arr=[];", "", "//assertions", "arr.push(Mkt_Accom_Activity,Mkt_Accom_Activity1,Mkt_Accom_Activity2,Mkt_Accom_Activity3,Mkt_Accom_Activity4,Mkt_Accom_Activity5,Mkt_Accom_Activity6);", "", "//Calling assertions", "for (item of arr) {", "    pm.globals.unset(\"actualjson\");", "    pm.globals.set(\"actualjson\", jsonData);", "    pm.globals.unset(\"expectedjson\");", "    pm.globals.set(\"expectedjson\", item);", "    eval(pm.globals.get(\"VerifyG3TableValue\"));", "}", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "propertyId", "type": "text", "value": "5"}], "body": {"mode": "raw", "raw": "{ \"query\": \"{{mktAccomActivityQuery}}\"\n}"}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/G3Tables/executeQuery/v1", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "G3Tables", "execute<PERSON>uery"]}}, "response": []}, {"name": "Then Group Block Table gets populated with pickup and Rate", "event": [{"listen": "test", "script": {"exec": ["//variables", "var jsonData = pm.response.json();", "", "console.log(JSON.stringify(jsonData));", "", "var Mkt_Accom_Activity=('[\"2018-07-01\",\"GROUP_STATUS_DEF\",\"DEFINITE\",\"2018-07-01\",\"2018-07-04\",5,1,0,90.90909]');", "", "var Mkt_Accom_Activity1=('[\"2018-07-02\",\"GROUP_STATUS_DEF\",\"DEFINITE\",\"2018-07-01\",\"2018-07-04\",5,1,0,181.81818]');", "", "var Mkt_Accom_Activity2=('[\"2018-07-03\",\"GROUP_STATUS_DEF\",\"DEFINITE\",\"2018-07-01\",\"2018-07-04\",5,1,0,272.72727]');", "", "var Mkt_Accom_Activity3=('[\"'+pm.globals.get(\"todayDate\")+'\",\"GROUP_STATUS_DEF1\",\"DEFINITE\",\"'+pm.globals.get(\"yesterday\")+'\",\"'+pm.globals.get(\"dayAfterTomorrowDate\")+'\",5,1,0,90.90909]');", "", "var Mkt_Accom_Activity4=('[\"'+pm.globals.get(\"tomorrowDate\")+'\",\"GROUP_STATUS_DEF1\",\"DEFINITE\",\"'+pm.globals.get(\"yesterday\")+'\",\"'+pm.globals.get(\"dayAfterTomorrowDate\")+'\",5,1,0,181.81818]');", "", "var Mkt_Accom_Activity5=('[\"'+pm.globals.get(\"dayAfterTomorrowDate\")+'\",\"GROUP_STATUS_DEF1\",\"DEFINITE\",\"'+pm.globals.get(\"yesterday\")+'\",\"'+pm.globals.get(\"dayAfterTomorrowDate\")+'\",5,1,0,272.72727]');", "", "", "var arr=[];", "", "//assertions", "arr.push(Mkt_Accom_Activity,Mkt_Accom_Activity1,Mkt_Accom_Activity2,Mkt_Accom_Activity3,Mkt_Accom_Activity4,Mkt_Accom_Activity5);", "", "//Calling assertions", "for (item of arr) {", "    pm.globals.unset(\"actualjson\");", "    pm.globals.set(\"actualjson\", jsonData);", "    pm.globals.unset(\"expectedjson\");", "    pm.globals.set(\"expectedjson\", item);", "    eval(pm.globals.get(\"VerifyG3TableValue\"));", "}", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["pm.globals.unset(\"whereCondition\");\r", "pm.globals.set(\"whereCondition\", \" where gm.Group_Name in ('GROUP_STATUS_DEF','GROUP_STATUS_DEF1') \")"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "propertyId", "type": "text", "value": "5"}], "body": {"mode": "raw", "raw": "{ \"query\": \"{{getGroupBlockData}}\"\n}"}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/G3Tables/executeQuery/v1", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "G3Tables", "execute<PERSON>uery"]}}, "response": []}, {"name": "Set Parameter: pacman.core.property.ApplyYieldCurrency =false", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{hostName}}:{{g3Port}}/api/configParam/v1/pacman.core.property.ApplyYieldCurrency?context=pacman.SandBox.OPER1&value=false&propertyId={{propertyId}}", "host": ["{{hostName}}"], "port": "{{g3Port}}", "path": ["pacman-platformsecurity", "rest", "config<PERSON><PERSON><PERSON>", "pacman.core.property.ApplyYieldCurrency"], "query": [{"key": "context", "value": "pacman.SandBox.OPER1"}, {"key": "value", "value": "false"}, {"key": "propertyId", "value": "{{propertyId}}"}]}}, "response": []}], "auth": {"type": "basic", "basic": [{"key": "password", "value": "{{sso_passwd}}", "type": "string"}, {"key": "username", "value": "{{sso_user}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}