@Decision @AgileProductQualifiedFPLOSDecisionGeneration
Feature:  FPLOS Decisions for Agile Product linked Qualified Rate Plans At SRP FPLOS At Total Level

  Scenario: Validate Qualified FPLOS Decisions For Restriction Rates At SRP FPLOS At Total Level
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "false"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have Agile Product configured
      | Product_ID | Name      | Code        | Status_ID |
      | 5          | Product A | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 1          | 100.00000 | -1  |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "5" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 5          | 90.00000  | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have not linked the rate plan with agile product
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYYYYYYY |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYYYYYYY |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |

  Scenario: Validate Qualified FPLOS Decisions For Restriction Rates At SRP FPLOS At Total Level When Servicing Cost is Enabled
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set isProfitOptimizationEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "false"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 2                 | 1             | 2017-10-10    | 2017-10-17  | 80     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 4                         | 2                 | 8             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 5                         | 3                 | 1             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 6                         | 3                 | 8             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I create a Servicing Cost configuration as below
      | Servicing_Cost_Cfg_ID | Property_ID | Business_Type_ID | Accom_Class_ID | Default_Evaluation_Method | Rate_Code | Full_Turn_Servicing_Cost | Interim_Servicing_Cost | Full_Servicing_Cost | Full_Servicing_Interval_Days |
      | 2                     | 11033       | 2                | 2              | 1                         | NULL      | 50                       | 40                     | 45                  | 1                            |
      | 3                     | 11033       | 2                | 3              | 1                         | NULL      | 50                       | 40                     | 45                  | 1                            |
    And I have Agile Product configured
      | Product_ID | Name      | Code        | Status_ID |
      | 5          | Product A | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 1          | 100.00000 | -1  |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "5" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 5          | 90.00000  | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
      | 2017-10-10   | 38.00000  | 3              |
      | 2017-10-11   | 132.00000 | 3              |
      | 2017-10-12   | 132.00000 | 3              |
      | 2017-10-13   | 85.00000  | 3              |
      | 2017-10-14   | 94.00000  | 3              |
      | 2017-10-15   | 132.00000 | 3              |
      | 2017-10-16   | 132.00000 | 3              |
      | 2017-10-17   | 132.00000 | 3              |
    And I have not linked the rate plan with agile product
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YNNNNNNN |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YNNNNNNN |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |

  Scenario: Validate Qualified FPLOS Decisions For Restriction Rates At SRP FPLOS At Total Level For Special Srp Id LV0
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "false"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 4                 | 7088             | 11033       | LV0            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 5                         | 4                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 6                         | 4                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I create a rate unqualified plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Managed_In_G3 |
      | 3                 | 7088             | 11033       | LV0            | 2017-01-01    | 2017-12-31  | 1         | 1         | 1             |
    And I have Agile Product configured
      | Product_ID | Name      | Code        | Status_ID |
      | 5          | Product A | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 1          | 100.00000 | -1  |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "5" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 5          | 90.00000  | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have not linked the rate plan with agile product
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYYYYYYY |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |
      | 2017-10-10 | 5             | 4                 | YYYYYYYY |
      | 2017-10-10 | 6             | 4                 | YYYYYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYYYYYYY |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |
      | 2017-10-10 | 5             | 4                 | YYYYYYYY |
      | 2017-10-10 | 6             | 4                 | YYYYYYYY |

  Scenario: Validate Qualified FPLOS Decisions For Restriction Rates At SRP FPLOS At Total Level For Special Srp Id LV0 When Servicing Cost Enabled and Configured
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set isProfitOptimizationEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "false"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 4                 | 7088             | 11033       | LV0            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 5                         | 4                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 6                         | 4                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I create a rate unqualified plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Managed_In_G3 |
      | 3                 | 7088             | 11033       | LV0            | 2017-01-01    | 2017-12-31  | 1         | 1         | 1             |
    And I create a Servicing Cost configuration as below
      | Servicing_Cost_Cfg_ID | Property_ID | Business_Type_ID | Accom_Class_ID | Default_Evaluation_Method | Rate_Code | Full_Turn_Servicing_Cost | Interim_Servicing_Cost | Full_Servicing_Cost | Full_Servicing_Interval_Days |
      | 2                     | 11033       | 2                | 2              | 1                         | NULL      | 50                       | 40                     | 45                  | 1                            |
      | 3                     | 11033       | 2                | 3              | 1                         | NULL      | 50                       | 40                     | 45                  | 1                            |
    And I have Agile Product configured
      | Product_ID | Name      | Code        | Status_ID |
      | 5          | Product A | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 1          | 100.00000 | -1  |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "5" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 5          | 90.00000  | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have not linked the rate plan with agile product
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YNNNNNNN |
      | 2017-10-10 | 5             | 3                 | YNNNNNNN |
      | 2017-10-10 | 5             | 4                 | YNNNNNNN |
      | 2017-10-10 | 6             | 4                 | YNNNNNNN |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YNNNNNNN |
      | 2017-10-10 | 5             | 3                 | YNNNNNNN |
      | 2017-10-10 | 5             | 4                 | YNNNNNNN |
      | 2017-10-10 | 6             | 4                 | YNNNNNNN |

  Scenario: Validate Qualified FPLOS Decisions For Restriction Rates not linked with agile product And isAgileRateFPLOSGenerationEnabled is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have Agile Product configured
      | Product_ID | Name      | Code        | Status_ID |
      | 5          | Product A | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "5" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 5          | 90.00000  | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have not linked the rate plan with agile product
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYYYYYYY |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYYYYYYY |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |

  Scenario: Validate Qualified FPLOS Decisions For Restriction Rates linked with Agile Product and isAgileRateFPLOSGenerationEnabled is false
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "false"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have Agile Product configured
      | Product_ID | Name      | Code        | Status_ID |
      | 5          | Product A | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "5" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 5          | 90.00000  | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 5          | 2                 |
      | 5          | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYYYYYYY |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYYYYYYY |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |

  Scenario: Validate Qualified FPLOS Decisions For Restriction Rates linked with inactive Agile Product and isAgileRateFPLOSGenerationEnabled is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have inactive agile Product configured
      | Product_ID | Name      | Code        | Status_ID |
      | 5          | Product A | AGILE_RATES | 4         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "5" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 5          | 90.00000  | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 5          | 2                 |
#      | 5          | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |

  Scenario: Validate Qualified FPLOS Decisions For Restriction Rates linked with deleted Agile Product and isAgileRateFPLOSGenerationEnabled is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have Deleted agile Product configured
      | Product_ID | Name      | Code        | Status_ID |
      | 5          | Product A | AGILE_RATES | 2         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "5" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 5          | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 5          | 90.00000  | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 5          | 2                 |
      | 5          | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decisions Not Generated
    And Pace Qualified FPLOS Decisions Not Generated

  Scenario: Validate Qualified FPLOS Decisions For Restriction Rates linked with Agile Product and isAgileRateFPLOSGenerationEnabled is true but decisions are not available for agile product
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
      | 24         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decisions Not Generated
    And Pace Qualified FPLOS Decisions Not Generated

  Scenario: Validate Qualified FPLOS Decisions For Restriction Rates with non master room type linked with Agile Product and isAgileRateFPLOSGenerationEnabled is true but decisions are not available for agile product
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
#      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
#      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 1                         | 2                 | 1             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 2                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 3                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
#      | 24         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-17"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-17"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |


  Scenario: Validate Qualified FPLOS Decisions For Restriction Rates linked with Agile Product and isAgileRateFPLOSGenerationEnabled is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
      | 24         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYNNYYYY |
      | 2017-10-10 | 5             | 3                 | YYNNYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYNNYYYY |
      | 2017-10-10 | 5             | 3                 | YYNNYYYY |

  Scenario: Validate Qualified FPLOS Decisions For Restriction Rates linked with Agile Product and isAgileRateFPLOSGenerationEnabled is true and Channel Restriction Adjustments is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I set isChannelRestrictionsAdjustmentsEnabled parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And I have Channel Restrictions Adjustments configured
      | Channel_Restriction_Adjustment_ID | Is_Global_Level_SRP | SRP_Group_Code | Sunday_Percentage_Adjustment | Sunday_Fixed_Adjustment | Sunday_Fixed_Adjustment_By_Yield | Monday_Percentage_Adjustment | Monday_Fixed_Adjustment | Monday_Fixed_Adjustment_By_Yield | Tuesday_Percentage_Adjustment | Tuesday_Fixed_Adjustment | Tuesday_Fixed_Adjustment_By_Yield | Wednesday_Percentage_Adjustment | Wednesday_Fixed_Adjustment | Wednesday_Fixed_Adjustment_By_Yield | Thursday_Percentage_Adjustment | Thursday_Fixed_Adjustment | Thursday_Fixed_Adjustment_By_Yield | Friday_Percentage_Adjustment | Friday_Fixed_Adjustment | Friday_Fixed_Adjustment_By_Yield | Saturday_Percentage_Adjustment | Saturday_Fixed_Adjustment | Saturday_Fixed_Adjustment_By_Yield |
      | 1                                 | 0                   | RLL1           | 10                           | 12                      | 130                              | 35                           | 14                      | 50                               | 38                            | 15                       | 170                               | 39                              | 18                         | 160                                 | 49                             | 14                        | 180                                | 28                           | 22                      | 110                              | 49                             | 10                        | 100                                |
      | 2                                 | 0                   | RLL2           | 50                           | 15                      | 170                              | 23                           | 16                      | 80                               | 27                            | 18                       | 190                               | 34                              | 21                         | 180                                 | 24                             | 18                        | 170                                | 21                           | 5                       | 150                              | 44                             | 20                        | 200                                |
      | 3                                 | 0                   | RLL3           | 90                           | 16                      | 200                              | 21                           | 17                      | 100                              | 35                            | 22                       | 200                               | 36                              | 5                          | 190                                 | 32                             | 12                        | 110                                | 48                           | 17                      | 190                              | 48                             | 15                        | 150                                |
    And I have SRP Group SRP Code Mapping configured
      | Channel_Srp_Group_Included_Srp_Mapping_ID | Channel_Srp_Group_Code | Included_Srp_Code | Channel_Restriction_Adjustment_ID |
      | 1                                         | RLL1                   | RL1               | 1                                 |
      | 2                                         | RLL2                   | RL2               | 2                                 |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
      | 24         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | NNNNNNNN |
      | 2017-10-10 | 5             | 3                 | NNNNNNNN |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | NNNNNNNN |
      | 2017-10-10 | 5             | 3                 | NNNNNNNN |


  Scenario: Validate Qualified FPLOS Decisions For a non-yieldable Restriction Rate linked with Agile Product
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 0         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product A | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
      | 24         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYNNYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYNNYYYY |

  Scenario: Validate Qualified FPLOS Decisions For a inactive Restriction Rate linked with Agile Product
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 4         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
      | 24         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 3                 | YYNNYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 3                 | YYNNYYYY |

  Scenario: Validate Qualified FPLOS Decisions For Restriction Rates linked with Agile Product and rate plan configuration date range is larger than agile product configuration date range in FPLOS generation date range
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "false"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2018-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-24  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-24  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-24"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
      | 2017-10-18   | 132.00000 | 2              |
      | 2017-10-19   | 132.00000 | 2              |
      | 2017-10-20   | 132.00000 | 2              |
      | 2017-10-21   | 132.00000 | 2              |
      | 2017-10-22   | 132.00000 | 2              |
      | 2017-10-23   | 132.00000 | 2              |
      | 2017-10-24   | 132.00000 | 2              |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-17"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |
      | 2017-10-11 | 5             | 3                 | NNNNYNNN |
      | 2017-10-12 | 5             | 3                 | NYYYYNNN |
      | 2017-10-13 | 5             | 3                 | YYYYYNNN |
      | 2017-10-14 | 5             | 3                 | YYYNNNNN |
      | 2017-10-15 | 5             | 3                 | YYNNNNNN |
      | 2017-10-16 | 5             | 3                 | NNNNNNNN |
      | 2017-10-17 | 5             | 3                 | NNNNNNNN |
    And Pace Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-17"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |
      | 2017-10-11 | 5             | 3                 | NNNNYNNN |
      | 2017-10-12 | 5             | 3                 | NYYYYNNN |
      | 2017-10-13 | 5             | 3                 | YYYYYNNN |
      | 2017-10-14 | 5             | 3                 | YYYNNNNN |
      | 2017-10-15 | 5             | 3                 | YYNNNNNN |
      | 2017-10-16 | 5             | 3                 | NNNNNNNN |
      | 2017-10-17 | 5             | 3                 | NNNNNNNN |
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 3                 |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 50.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 40.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-18 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-19 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-20 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-21 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-22 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-23 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-24 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 70.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 50.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-18 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-19 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-20 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-21 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-22 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-23 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-24 | 6             | 24         | 150.00000 | -1  |
    And I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-12" and "2017-10-17"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-12 | 5             | 3                 | NNNNNNNY |
      | 2017-10-13 | 5             | 3                 | NYYYYYYY |
      | 2017-10-14 | 5             | 3                 | YYYYYYYY |
      | 2017-10-15 | 5             | 3                 | YYYYYYYY |
      | 2017-10-16 | 5             | 3                 | YYYYYYYY |
      | 2017-10-17 | 5             | 3                 | YYYYNYYY |
    And Qualified FPLOS Decision are not available for Accom_Type_ID "5" and Rate_Qualified_ID "3" and Arrival_DT between "2017-10-10" and "2017-10-11"
    And Pace Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-17"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |
      | 2017-10-11 | 5             | 3                 | NNNNYNNN |
      | 2017-10-12 | 5             | 3                 | NYYYYNNN |
      | 2017-10-12 | 5             | 3                 | NNNNNNNY |
      | 2017-10-13 | 5             | 3                 | YYYYYNNN |
      | 2017-10-13 | 5             | 3                 | NYYYYYYY |
      | 2017-10-14 | 5             | 3                 | YYYNNNNN |
      | 2017-10-14 | 5             | 3                 | YYYYYYYY |
      | 2017-10-15 | 5             | 3                 | YYNNNNNN |
      | 2017-10-15 | 5             | 3                 | YYYYYYYY |
      | 2017-10-16 | 5             | 3                 | NNNNNNNN |
      | 2017-10-16 | 5             | 3                 | YYYYYYYY |
      | 2017-10-17 | 5             | 3                 | NNNNNNNN |
      | 2017-10-17 | 5             | 3                 | YYYYNYYY |

  Scenario: Validate Qualified FPLOS Decisions For Restriction Rates linked with Agile Product and isAgileRateFPLOSGenerationEnabled is true but with insufficient decisions to generate FPLOS
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-18 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-18 | 6             | 24         | 150.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
      | 24         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decisions Not Generated
    And Pace Qualified FPLOS Decisions Not Generated

  Scenario: Validate Qualified FPLOS Decisions For Restriction Rates linked with Agile Product and isAgileRateFPLOSGenerationEnabled is true but with Final BAR is null because of zero capacity RT
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | NULL      | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-18 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | NULL      | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-18 | 6             | 24         | 150.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
      | 24         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decisions Not Generated
    And Pace Qualified FPLOS Decisions Not Generated

  Scenario: Validate Qualified FPLOS Decisions when rate plan configuration date range is less than agile product configuration date range for FPLOS generation and considerRestrictionSeasonDatesForAgileFPLOS is false
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I set Consider Restriction Seasons Dates For Agile Qualified FPLOS parameter to "false"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-11    | 2017-10-18  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-11    | 2017-10-18  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-11    | 2017-10-18  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-11    | 2017-10-18  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-18 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-18 | 6             | 24         | 150.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
      | 2017-10-18   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
      | 24         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-11"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYNNYYYY |
      | 2017-10-10 | 5             | 3                 | YYNNYYYY |
      | 2017-10-11 | 5             | 2                 | NNNNNNNY |
      | 2017-10-11 | 5             | 3                 | NNNNNNNY |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYNNYYYY |
      | 2017-10-10 | 5             | 3                 | YYNNYYYY |
      | 2017-10-11 | 5             | 2                 | NNNNNNNY |
      | 2017-10-11 | 5             | 3                 | NNNNNNNY |


  Scenario: Validate Qualified FPLOS Decisions when rate plan configuration date range is less than agile product configuration date range for FPLOS generation and considerRestrictionSeasonDatesForAgileFPLOS is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I set Consider Restriction Seasons Dates For Agile Qualified FPLOS parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-11    | 2017-10-18  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-11    | 2017-10-18  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-11    | 2017-10-18  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-11    | 2017-10-18  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-18 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-18 | 6             | 24         | 150.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
      | 2017-10-18   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
      | 24         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-11"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-11 | 5             | 2                 | NNNNNNNY |
      | 2017-10-11 | 5             | 3                 | NNNNNNNY |
    And Qualified FPLOS Decision are not available for Accom_Type_ID "5" and Rate_Qualified_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-10"
    And Qualified FPLOS Decision are not available for Accom_Type_ID "5" and Rate_Qualified_ID "3" and Arrival_DT between "2017-10-10" and "2017-10-10"
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-11 | 5             | 2                 | NNNNNNNY |
      | 2017-10-11 | 5             | 3                 | NNNNNNNY |


  Scenario: Validate Qualified FPLOS Decisions when rate plan configuration has multiple seasons and its date range is less than agile product configuration date range for FPLOS generation and considerRestrictionSeasonDatesForAgileFPLOS is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I set Consider Restriction Seasons Dates For Agile Qualified FPLOS parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-11    | 2017-10-18  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-11    | 2017-10-18  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 2                 | 9             | 2017-10-19    | 2017-10-26  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 9             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 9             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 9             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 9             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-14 | 9             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 9             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 9             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 9             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-18 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-18 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-18 | 9             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-19 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-19 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-19 | 9             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-20 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-20 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-20 | 9             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-21 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-21 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-21 | 9             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-22 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-22 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-22 | 9             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-23 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-23 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-23 | 9             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-24 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-24 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-24 | 9             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-25 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-25 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-25 | 9             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-26 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-26 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-26 | 9             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-27 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-27 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-27 | 9             | 24         | 90.00000  | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
      | 2017-10-18   | 132.00000 | 2              |
      | 2017-10-19   | 38.00000  | 2              |
      | 2017-10-20   | 132.00000 | 2              |
      | 2017-10-21   | 132.00000 | 2              |
      | 2017-10-22   | 85.00000  | 2              |
      | 2017-10-23   | 94.00000  | 2              |
      | 2017-10-24   | 132.00000 | 2              |
      | 2017-10-25   | 132.00000 | 2              |
      | 2017-10-26   | 132.00000 | 2              |
      | 2017-10-27   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
      | 24         | 2                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-27"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-11 | 5             | 2                 | NNNNNNNY |
      | 2017-10-12 | 5             | 2                 | NNYYYYYY |
      | 2017-10-13 | 5             | 2                 | NYYYYYYY |
      | 2017-10-14 | 5             | 2                 | YYYYYYYY |
      | 2017-10-15 | 5             | 2                 | YYYYYYYY |
      | 2017-10-16 | 5             | 2                 | YYYYYYYY |
      | 2017-10-17 | 5             | 2                 | YYYYYYYY |
      | 2017-10-18 | 5             | 2                 | YYYYYYYY |
      | 2017-10-19 | 5             | 2                 | YYYYYYYY |
      | 2017-10-20 | 5             | 2                 | YYYYYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-27"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-11 | 5             | 2                 | NNNNNNNY |
      | 2017-10-12 | 5             | 2                 | NNYYYYYY |
      | 2017-10-13 | 5             | 2                 | NYYYYYYY |
      | 2017-10-14 | 5             | 2                 | YYYYYYYY |
      | 2017-10-15 | 5             | 2                 | YYYYYYYY |
      | 2017-10-16 | 5             | 2                 | YYYYYYYY |
      | 2017-10-17 | 5             | 2                 | YYYYYYYY |
      | 2017-10-18 | 5             | 2                 | YYYYYYYY |
      | 2017-10-19 | 5             | 2                 | YYYYYYYY |
      | 2017-10-20 | 5             | 2                 | YYYYYYYY |

  Scenario: Validate Qualified FPLOS gets at total level generated for all dates for different qualified rate mapped to different product having weekly seasons having start date is equal to or after caught up date and end dates is less than optimization end dates when rate plan configuration date range is less than agile product configuration date range for FPLOS generation and considerRestrictionSeasonDatesForAgileFPLOS is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I set Consider Restriction Seasons Dates For Agile Qualified FPLOS parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 6             | 2017-11-01    | 2017-11-07  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-11-08    | 2017-11-15  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 3                         | 2                 | 6             | 2017-11-16    | 2017-11-23  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 2                 | 6             | 2017-11-24    | 2017-11-30  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 5                         | 2                 | 6             | 2017-12-01    | 2017-12-07  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 7                         | 2                 | 6             | 2017-12-08    | 2017-12-15  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 8                         | 2                 | 6             | 2017-12-16    | 2017-12-23  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 9                         | 2                 | 6             | 2017-12-24    | 2017-12-31  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 10                        | 3                 | 6             | 2017-11-01    | 2017-11-07  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 11                        | 3                 | 6             | 2017-11-08    | 2017-11-15  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 12                        | 3                 | 6             | 2017-11-16    | 2017-11-23  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 13                        | 3                 | 6             | 2017-11-24    | 2017-11-30  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 14                        | 3                 | 6             | 2017-12-01    | 2017-12-07  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 15                        | 3                 | 6             | 2017-12-08    | 2017-12-15  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 16                        | 3                 | 6             | 2017-12-16    | 2017-12-23  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 17                        | 3                 | 6             | 2017-12-24    | 2017-12-31  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 21         | 2                 |
      | 18         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service with rate qualified ids = "2" and last loop = "false"
    Then Qualified FPLOS Decision table has "61" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "61" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    When I invoke Decision Qualified FPLOS generation Service with rate qualified ids = "3" and last loop = "true"
    Then Qualified FPLOS Decision table has "23" records for Accom_Type_ID "5" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "23" records for Accom_Type_ID "5" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"

  Scenario: Validate Qualified FPLOS gets at total level generated for all dates for different qualified rate mapped to different product having weekly seasons having start date is equal to or after caught up date and end dates is less than optimization end dates when rate plan configuration date range is less than agile product configuration date range for FPLOS generation and considerRestrictionSeasonDatesForAgileFPLOS is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I set Consider Restriction Seasons Dates For Agile Qualified FPLOS parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 6             | 2017-11-01    | 2017-11-07  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-11-08    | 2017-11-15  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 3                         | 2                 | 6             | 2017-11-16    | 2017-11-23  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 2                 | 6             | 2017-11-24    | 2017-11-30  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 5                         | 2                 | 6             | 2017-12-01    | 2017-12-07  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 7                         | 2                 | 6             | 2017-12-08    | 2017-12-15  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 8                         | 2                 | 6             | 2017-12-16    | 2017-12-23  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 9                         | 2                 | 6             | 2017-12-24    | 2017-12-31  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 10                        | 3                 | 6             | 2017-11-01    | 2017-11-07  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 11                        | 3                 | 6             | 2017-11-08    | 2017-11-15  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 12                        | 3                 | 6             | 2017-11-16    | 2017-11-23  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 13                        | 3                 | 6             | 2017-11-24    | 2017-11-30  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 14                        | 3                 | 6             | 2017-12-01    | 2017-12-07  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 15                        | 3                 | 6             | 2017-12-08    | 2017-12-15  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 16                        | 3                 | 6             | 2017-12-16    | 2017-12-23  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 17                        | 3                 | 6             | 2017-12-24    | 2017-12-31  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 21         | 2                 |
      | 18         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service with rate qualified ids = "2" and last loop = "false"
    Then Qualified FPLOS Decision table has "61" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "61" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    When I invoke Decision Qualified FPLOS generation Service with rate qualified ids = "3" and last loop = "true"
    Then Qualified FPLOS Decision table has "23" records for Accom_Type_ID "5" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "23" records for Accom_Type_ID "5" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"

  Scenario: Validate Qualified FPLOS at total level gets generated for all dates for qualified rate having weekly seasons having start date is equal to or after caught up date and end dates is less than optimization end dates when rate plan configuration date range is less than agile product configuration date range for FPLOS generation and considerRestrictionSeasonDatesForAgileFPLOS is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I set Consider Restriction Seasons Dates For Agile Qualified FPLOS parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 8             | 2017-11-01    | 2017-11-07  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 8             | 2017-11-08    | 2017-11-15  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 3                         | 2                 | 8             | 2017-11-16    | 2017-11-23  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 2                 | 8             | 2017-11-24    | 2017-11-30  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 5                         | 2                 | 8             | 2017-12-01    | 2017-12-07  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 7                         | 2                 | 8             | 2017-12-08    | 2017-12-15  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 8                         | 2                 | 8             | 2017-12-16    | 2017-12-23  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 9                         | 2                 | 8             | 2017-12-24    | 2017-12-31  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 21         | 2                 |
      | 21         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service with rate qualified ids = "2" and last loop = "true"
    Then Qualified FPLOS Decision table has "61" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "61" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"

  Scenario: Validate Qualified FPLOS at total level gets generated for all dates for qualified rate for seasons having start date is equal to or after caught up date and end dates is less than optimization end dates when rate plan configuration date range is less than agile product configuration date range for FPLOS generation and considerRestrictionSeasonDatesForAgileFPLOS is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I set Consider Restriction Seasons Dates For Agile Qualified FPLOS parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-11-30  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 5             | 2017-12-01    | 2017-12-31  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 3                         | 2                 | 5             | 2018-09-01    | 2018-09-30  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 2                 | 6             | 2017-10-10    | 2017-11-30  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 5                         | 2                 | 6             | 2017-12-01    | 2017-12-31  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 6                         | 2                 | 6             | 2018-09-01    | 2018-09-30  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 7                         | 3                 | 5             | 2017-10-10    | 2017-11-30  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 8                         | 3                 | 5             | 2018-02-01    | 2018-02-28  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 9                         | 3                 | 5             | 2018-09-01    | 2018-09-30  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 10                        | 3                 | 6             | 2017-10-10    | 2017-11-30  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 11                        | 3                 | 6             | 2018-02-01    | 2018-02-28  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 12                        | 3                 | 6             | 2018-09-01    | 2018-09-30  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |

    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 21         | 2                 |
      | 21         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service with rate qualified ids = "2" and last loop = "false"
    Then Qualified FPLOS Decision table has "113" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "113" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    When I invoke Decision Qualified FPLOS generation Service with rate qualified ids = "3" and last loop = "true"
    And Qualified FPLOS Decision table has "110" records for Accom_Type_ID "5" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "110" records for Accom_Type_ID "5" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"

  Scenario: Validate Qualified FPLOS at total level gets generated with start date as optimization start date and end date as optimization window minus 7 days and only for room types fow which rates were given in qualified rate having start date as past and end date is after optimization end date when toggle considerRestrictionSeasonDatesForAgileFPLOS is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I set Consider Restriction Seasons Dates For Agile Qualified FPLOS parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-09-01    | 2018-12-31  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-09-01    | 2018-12-31  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 7             | 2017-09-01    | 2018-12-31  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 8             | 2017-09-01    | 2018-12-31  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 21         | 2                 |
      | 21         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service with rate qualified ids = "2" and last loop = "false"
    Then Qualified FPLOS Decision table has "358" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "358" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "6" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "6" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
#fplos should not be generated for Accom Type 7 and 8 as their details were not given in qualified rate screen for rate R1 so will be ignored in agle fplos too
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "7" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "7" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "8" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "8" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    When I invoke Decision Qualified FPLOS generation Service with rate qualified ids = "3" and last loop = "true"
    And Qualified FPLOS Decision table has "358" records for Accom_Type_ID "5" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "358" records for Accom_Type_ID "5" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "6" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "6" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
#fplos should not be generated for Accom Type 5 and 6 as their details were not given in qualified rate screen for rate R2 so will be ignored in agle fplos too
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "7" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "7" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "8" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "8" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"


  Scenario: Validate Qualified FPLOS correctly gets generated considering only final bar of only those room types for which rates were configured in restriction when considerRestrictionSeasonDatesForAgileFPLOS is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I set Consider Restriction Seasons Dates For Agile Qualified FPLOS parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 1             | 2017-10-20    | 2017-10-30  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 3                 | 1             | 2017-10-20    | 2017-10-30  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-20 | 1             | 24         | 131.00000 | -1  |
      | 11033       | 97          | 2017-10-21 | 1             | 24         | 131.00000 | -1  |
      | 11033       | 97          | 2017-10-22 | 1             | 24         | 131.00000 | -1  |
      | 11033       | 97          | 2017-10-23 | 1             | 24         | 131.00000 | -1  |
      | 11033       | 97          | 2017-10-24 | 1             | 24         | 131.00000 | -1  |
      | 11033       | 97          | 2017-10-25 | 1             | 24         | 131.00000 | -1  |
      | 11033       | 97          | 2017-10-26 | 1             | 24         | 131.00000 | -1  |
      | 11033       | 97          | 2017-10-27 | 1             | 24         | 131.00000 | -1  |
      | 11033       | 97          | 2017-10-28 | 1             | 24         | 131.00000 | -1  |
      | 11033       | 97          | 2017-10-29 | 1             | 24         | 131.00000 | -1  |
      | 11033       | 97          | 2017-10-30 | 1             | 24         | 131.00000 | -1  |
      | 11033       | 97          | 2017-10-20 | 2             | 24         | 141.00000 | -1  |
      | 11033       | 97          | 2017-10-21 | 2             | 24         | 141.00000 | -1  |
      | 11033       | 97          | 2017-10-22 | 2             | 24         | 141.00000 | -1  |
      | 11033       | 97          | 2017-10-23 | 2             | 24         | 141.00000 | -1  |
      | 11033       | 97          | 2017-10-24 | 2             | 24         | 141.00000 | -1  |
      | 11033       | 97          | 2017-10-25 | 2             | 24         | 141.00000 | -1  |
      | 11033       | 97          | 2017-10-26 | 2             | 24         | 141.00000 | -1  |
      | 11033       | 97          | 2017-10-27 | 2             | 24         | 141.00000 | -1  |
      | 11033       | 97          | 2017-10-28 | 2             | 24         | 141.00000 | -1  |
      | 11033       | 97          | 2017-10-29 | 2             | 24         | 141.00000 | -1  |
      | 11033       | 97          | 2017-10-30 | 2             | 24         | 141.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-20" and "2017-10-30"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-20   | 130.00000 | 2              |
      | 2017-10-21   | 130.00000 | 2              |
      | 2017-10-22   | 130.00000 | 2              |
      | 2017-10-23   | 130.00000 | 2              |
      | 2017-10-24   | 130.00000 | 2              |
      | 2017-10-25   | 130.00000 | 2              |
      | 2017-10-26   | 130.00000 | 2              |
      | 2017-10-27   | 130.00000 | 2              |
      | 2017-10-28   | 130.00000 | 2              |
      | 2017-10-29   | 130.00000 | 2              |
      | 2017-10-30   | 130.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
      | 24         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    #When I invoke Decision Qualified FPLOS generation Service with rate qualified ids = "2" and last loop = "false"
    Then Qualified FPLOS Decision table has "4" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "4" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-20" and "2017-10-23"
    And Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Rate_Qualified_ID = "2" and Arrival_DT between "2017-10-20" and "2017-10-30"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-20 | 5             | 2                 | NNNNNNNN |
      | 2017-10-21 | 5             | 2                 | NNNNNNNN |
      | 2017-10-22 | 5             | 2                 | NNNNNNNN |
      | 2017-10-23 | 5             | 2                 | NNNNNNNN |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID "5" and Rate_Qualified_ID = "2" and Arrival_DT between "2017-10-20" and "2017-10-23"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-20 | 5             | 2                 | NNNNNNNN |
      | 2017-10-21 | 5             | 2                 | NNNNNNNN |
      | 2017-10-22 | 5             | 2                 | NNNNNNNN |
      | 2017-10-23 | 5             | 2                 | NNNNNNNN |
    #When I invoke Decision Qualified FPLOS generation Service with rate qualified ids = "3" and last loop = "true"
    And Qualified FPLOS Decision table has "4" records for Accom_Type_ID "5" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "4" records for Accom_Type_ID "5" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Rate_Qualified_ID = "3" and Arrival_DT between "2017-10-20" and "2017-10-30"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-20 | 5             | 3                 | NNNNNNNN |
      | 2017-10-21 | 5             | 3                 | NNNNNNNN |
      | 2017-10-22 | 5             | 3                 | NNNNNNNN |
      | 2017-10-23 | 5             | 3                 | NNNNNNNN |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID "5" and Rate_Qualified_ID = "3" and Arrival_DT between "2017-10-20" and "2017-10-23"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-20 | 5             | 3                 | NNNNNNNN |
      | 2017-10-21 | 5             | 3                 | NNNNNNNN |
      | 2017-10-22 | 5             | 3                 | NNNNNNNN |
      | 2017-10-23 | 5             | 3                 | NNNNNNNN |

  Scenario: Validate Qualified FPLOS correctly gets generated considering only final bar of all room types mapped to agile product even if the rates are not configured for it in restriction when considerRestrictionSeasonDatesForAgileFPLOS is false
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I set Consider Restriction Seasons Dates For Agile Qualified FPLOS parameter to "false"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 1             | 2017-10-20    | 2017-10-30  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 3                 | 1             | 2017-10-20    | 2017-10-30  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-20 | 1             | 24         | 131.00000 | -1  |
      | 11033       | 97          | 2017-10-21 | 1             | 24         | 131.00000 | -1  |
      | 11033       | 97          | 2017-10-22 | 1             | 24         | 131.00000 | -1  |
      | 11033       | 97          | 2017-10-23 | 1             | 24         | 131.00000 | -1  |
      | 11033       | 97          | 2017-10-24 | 1             | 24         | 131.00000 | -1  |
      | 11033       | 97          | 2017-10-25 | 1             | 24         | 131.00000 | -1  |
      | 11033       | 97          | 2017-10-26 | 1             | 24         | 131.00000 | -1  |
      | 11033       | 97          | 2017-10-27 | 1             | 24         | 131.00000 | -1  |
      | 11033       | 97          | 2017-10-28 | 1             | 24         | 131.00000 | -1  |
      | 11033       | 97          | 2017-10-29 | 1             | 24         | 131.00000 | -1  |
      | 11033       | 97          | 2017-10-30 | 1             | 24         | 131.00000 | -1  |
      | 11033       | 97          | 2017-10-20 | 2             | 24         | 141.00000 | -1  |
      | 11033       | 97          | 2017-10-21 | 2             | 24         | 141.00000 | -1  |
      | 11033       | 97          | 2017-10-22 | 2             | 24         | 141.00000 | -1  |
      | 11033       | 97          | 2017-10-23 | 2             | 24         | 141.00000 | -1  |
      | 11033       | 97          | 2017-10-24 | 2             | 24         | 141.00000 | -1  |
      | 11033       | 97          | 2017-10-25 | 2             | 24         | 141.00000 | -1  |
      | 11033       | 97          | 2017-10-26 | 2             | 24         | 141.00000 | -1  |
      | 11033       | 97          | 2017-10-27 | 2             | 24         | 141.00000 | -1  |
      | 11033       | 97          | 2017-10-28 | 2             | 24         | 141.00000 | -1  |
      | 11033       | 97          | 2017-10-29 | 2             | 24         | 141.00000 | -1  |
      | 11033       | 97          | 2017-10-30 | 2             | 24         | 141.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-20" and "2017-10-30"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-20   | 130.00000 | 2              |
      | 2017-10-21   | 130.00000 | 2              |
      | 2017-10-22   | 130.00000 | 2              |
      | 2017-10-23   | 130.00000 | 2              |
      | 2017-10-24   | 130.00000 | 2              |
      | 2017-10-25   | 130.00000 | 2              |
      | 2017-10-26   | 130.00000 | 2              |
      | 2017-10-27   | 130.00000 | 2              |
      | 2017-10-28   | 130.00000 | 2              |
      | 2017-10-29   | 130.00000 | 2              |
      | 2017-10-30   | 130.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
      | 24         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    #When I invoke Decision Qualified FPLOS generation Service with rate qualified ids = "2" and last loop = "false"
    Then Qualified FPLOS Decision table has "4" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-20" and "2017-10-23"
    And Pace Qualified FPLOS Decision table also has "4" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Rate_Qualified_ID = "2" and Arrival_DT between "2017-10-20" and "2017-10-30"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-20 | 5             | 2                 | YYYYYYYY |
      | 2017-10-21 | 5             | 2                 | YYYYYYYY |
      | 2017-10-22 | 5             | 2                 | YYYYYYYY |
      | 2017-10-23 | 5             | 2                 | YYYYYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID "5" and Rate_Qualified_ID = "2" and Arrival_DT between "2017-10-20" and "2017-10-23"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-20 | 5             | 2                 | YYYYYYYY |
      | 2017-10-21 | 5             | 2                 | YYYYYYYY |
      | 2017-10-22 | 5             | 2                 | YYYYYYYY |
      | 2017-10-23 | 5             | 2                 | YYYYYYYY |
    #When I invoke Decision Qualified FPLOS generation Service with rate qualified ids = "3" and last loop = "true"
    Then Qualified FPLOS Decision table has "4" records for Accom_Type_ID "5" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "4" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Rate_Qualified_ID = "3" and Arrival_DT between "2017-10-20" and "2017-10-30"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-20 | 5             | 3                 | YYYYYYYY |
      | 2017-10-21 | 5             | 3                 | YYYYYYYY |
      | 2017-10-22 | 5             | 3                 | YYYYYYYY |
      | 2017-10-23 | 5             | 3                 | YYYYYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID "5" and Rate_Qualified_ID = "3" and Arrival_DT between "2017-10-20" and "2017-10-23"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-20 | 5             | 3                 | YYYYYYYY |
      | 2017-10-21 | 5             | 3                 | YYYYYYYY |
      | 2017-10-22 | 5             | 3                 | YYYYYYYY |
      | 2017-10-23 | 5             | 3                 | YYYYYYYY |

  Scenario: Validate Qualified FPLOS Decisions For Restriction Rates linked with Agile Product and isAgileRateFPLOSGenerationEnabled is true When servicing cost is enabled and configured
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set isProfitOptimizationEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I create a Servicing Cost configuration as below
      | Servicing_Cost_Cfg_ID | Property_ID | Business_Type_ID | Accom_Class_ID | Default_Evaluation_Method | Rate_Code | Full_Turn_Servicing_Cost | Interim_Servicing_Cost | Full_Servicing_Cost | Full_Servicing_Interval_Days |
      | 2                     | 11033       | 2                | 2              | 1                         | NULL      | 50                       | 40                     | 45                  | 1                            |
      | 2                     | 11033       | 2                | 3              | 1                         | NULL      | 60                       | 30                     | 55                  | 1                            |
      | 2                     | 11033       | 2                | 4              | 1                         | NULL      | 70                       | 20                     | 65                  | 1                            |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-10 | 1             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 1             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 1             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 1             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 1             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 1             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 1             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 1             | 24         | 150.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
      | 24         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YNNNNNNN |
      | 2017-10-10 | 5             | 3                 | YNNNNNNN |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YNNNNNNN |
      | 2017-10-10 | 5             | 3                 | YNNNNNNN |

  Scenario: Validate Only Master Class Room Types Should Be Consider While Generating Qualified FPLOS Decisions For Restriction Rates linked with Agile Product
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-10 | 1             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 1             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 1             | 24         | 110.00000 | -1  |
      | 11033       | 97          | 2017-10-13 | 1             | 24         | 120.00000 | -1  |
      | 11033       | 97          | 2017-10-14 | 1             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 1             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 1             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 1             | 24         | 150.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
      | 24         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYNNYYYY |
      | 2017-10-10 | 5             | 3                 | YYNNYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYNNYYYY |
      | 2017-10-10 | 5             | 3                 | YYNNYYYY |


  Scenario: Validate Weighted Average Should Consider For Only Master Class Room Types And Max Average Should Consider For Non Master Room Types For FPLOS Of Restriction Rates linked with Agile Product
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 1             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 8             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have a new Agile Product configured as below
      | Product_ID | Name       | Code        | Status_ID |
      | 24         | Product Z  | AGILE_RATES | 1         |
      | 25         | Product NM | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-10 | 1             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 1             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 1             | 24         | 110.00000 | -1  |
      | 11033       | 97          | 2017-10-13 | 1             | 24         | 120.00000 | -1  |
      | 11033       | 97          | 2017-10-14 | 1             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 1             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 1             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 1             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-10 | 1             | 25         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 1             | 25         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 1             | 25         | 110.00000 | -1  |
      | 11033       | 97          | 2017-10-13 | 1             | 25         | 120.00000 | -1  |
      | 11033       | 97          | 2017-10-14 | 1             | 25         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 1             | 25         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 1             | 25         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 1             | 25         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-10 | 8             | 25         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-11 | 8             | 25         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 8             | 25         | 110.00000 | -1  |
      | 11033       | 97          | 2017-10-13 | 8             | 25         | 110.00000 | -1  |
      | 11033       | 97          | 2017-10-14 | 8             | 25         | 160.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 8             | 25         | 140.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 8             | 25         | 170.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 8             | 25         | 150.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
      | 25         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYNNYYYY |
      | 2017-10-10 | 5             | 3                 | YYNYYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYNNYYYY |
      | 2017-10-10 | 5             | 3                 | YYNYYYYY |

  Scenario: Validate Qualified FPLOS Decisions for Restriction linked with Agile At Total Level gets generated for both the non overlapping season start date using Weighted Average of Master for first Season and Max of Non Master Room Type
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set isProfitOptimizationEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 2                 | 1             | 2017-10-18    | 2017-10-25  | 110    | 60     | 60      | 60        | 60       | 60     | 48       |
      | 4                         | 2                 | 8             | 2017-10-18    | 2017-10-25  | 115    | 65     | 65      | 65        | 65       | 65     | 58       |

    And I create a Servicing Cost configuration as below
      | Servicing_Cost_Cfg_ID | Property_ID | Business_Type_ID | Accom_Class_ID | Default_Evaluation_Method | Rate_Code | Full_Turn_Servicing_Cost | Interim_Servicing_Cost | Full_Servicing_Cost | Full_Servicing_Interval_Days |
      | 2                     | 11033       | 1                | 2              | 1                         | NULL      | 50                       | 40                     | 45                  | 1                            |
      | 3                     | 11033       | 1                | 3              | 1                         | NULL      | 60                       | 30                     | 55                  | 1                            |
      | 4                     | 11033       | 1                | 4              | 1                         | NULL      | 70                       | 20                     | 65                  | 1                            |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-18 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-19 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-20 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-21 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-22 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-23 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-24 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-25 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-18 | 8             | 24         | 132.00000 | -1  |
      | 11033       | 97          | 2017-10-19 | 8             | 24         | 132.00000 | -1  |
      | 11033       | 97          | 2017-10-20 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-21 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-22 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-23 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-24 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-25 | 8             | 24         | 105.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-25"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
      | 2017-10-18   | 38.00000  | 2              |
      | 2017-10-19   | 132.00000 | 2              |
      | 2017-10-20   | 132.00000 | 2              |
      | 2017-10-21   | 85.00000  | 2              |
      | 2017-10-22   | 94.00000  | 2              |
      | 2017-10-23   | 132.00000 | 2              |
      | 2017-10-24   | 132.00000 | 2              |
      | 2017-10-25   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
    When I invoke Decision Qualified FPLOS generation Service with rate qualified ids = "2" and last loop = "true"
    Then Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYNNYYYY |
    And Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-18"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-18 | 5             | 2                 | YYNNNNNN |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYNNYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-18"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-18 | 5             | 2                 | YYNNNNNN |

  Scenario: Validate Qualified FPLOS Decisions for Restriction linked with Agile Product At Total Level gets generated using Master Class Rate When Restriction have rates defined for both Master Class and Non Master Class
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set isProfitOptimizationEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 2                 | 1             | 2017-10-10    | 2017-10-17  | 110    | 60     | 60      | 60        | 60       | 60     | 48       |
      | 4                         | 2                 | 8             | 2017-10-10    | 2017-10-17  | 115    | 65     | 65      | 65        | 65       | 65     | 58       |
    And I create a Servicing Cost configuration as below
      | Servicing_Cost_Cfg_ID | Property_ID | Business_Type_ID | Accom_Class_ID | Default_Evaluation_Method | Rate_Code | Full_Turn_Servicing_Cost | Interim_Servicing_Cost | Full_Servicing_Cost | Full_Servicing_Interval_Days |
      | 2                     | 11033       | 1                | 2              | 1                         | NULL      | 50                       | 40                     | 45                  | 1                            |
      | 3                     | 11033       | 1                | 3              | 1                         | NULL      | 60                       | 30                     | 55                  | 1                            |
      | 4                     | 11033       | 1                | 4              | 1                         | NULL      | 70                       | 20                     | 65                  | 1                            |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-18 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-19 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-20 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-21 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-22 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-23 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-24 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-25 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-18 | 8             | 24         | 132.00000 | -1  |
      | 11033       | 97          | 2017-10-19 | 8             | 24         | 132.00000 | -1  |
      | 11033       | 97          | 2017-10-20 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-21 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-22 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-23 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-24 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-25 | 8             | 24         | 105.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-25"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
      | 2017-10-18   | 38.00000  | 2              |
      | 2017-10-19   | 132.00000 | 2              |
      | 2017-10-20   | 132.00000 | 2              |
      | 2017-10-21   | 85.00000  | 2              |
      | 2017-10-22   | 94.00000  | 2              |
      | 2017-10-23   | 132.00000 | 2              |
      | 2017-10-24   | 132.00000 | 2              |
      | 2017-10-25   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
    When I invoke Decision Qualified FPLOS generation Service with rate qualified ids = "2" and last loop = "true"
    Then Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYNNYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYNNYYYY |

  Scenario: Validate Qualified FPLOS Decisions for Restriction linked with Agile At Total Level gets generated Considering Service Cost When Configured for both the non overlapping season start date using Weighted Average of Master for first Season and Max of Non Master Room Type
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set isProfitOptimizationEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 2                 | 1             | 2017-10-18    | 2017-10-25  | 110    | 60     | 60      | 60        | 60       | 60     | 48       |
      | 4                         | 2                 | 8             | 2017-10-18    | 2017-10-25  | 115    | 65     | 65      | 65        | 65       | 65     | 58       |

    And I create a Servicing Cost configuration as below
      | Servicing_Cost_Cfg_ID | Property_ID | Business_Type_ID | Accom_Class_ID | Default_Evaluation_Method | Rate_Code | Full_Turn_Servicing_Cost | Interim_Servicing_Cost | Full_Servicing_Cost | Full_Servicing_Interval_Days |
      | 2                     | 11033       | 2                | 2              | 1                         | NULL      | 50                       | 40                     | 45                  | 1                            |
      | 3                     | 11033       | 1                | 3              | 1                         | NULL      | 60                       | 30                     | 55                  | 1                            |
      | 4                     | 11033       | 1                | 4              | 1                         | NULL      | 70                       | 20                     | 65                  | 1                            |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-18 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-19 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-20 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-21 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-22 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-23 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-24 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-25 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-18 | 8             | 24         | 132.00000 | -1  |
      | 11033       | 97          | 2017-10-19 | 8             | 24         | 132.00000 | -1  |
      | 11033       | 97          | 2017-10-20 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-21 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-22 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-23 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-24 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-25 | 8             | 24         | 105.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-25"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
      | 2017-10-18   | 38.00000  | 2              |
      | 2017-10-19   | 132.00000 | 2              |
      | 2017-10-20   | 132.00000 | 2              |
      | 2017-10-21   | 85.00000  | 2              |
      | 2017-10-22   | 94.00000  | 2              |
      | 2017-10-23   | 132.00000 | 2              |
      | 2017-10-24   | 132.00000 | 2              |
      | 2017-10-25   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
    When I invoke Decision Qualified FPLOS generation Service with rate qualified ids = "2" and last loop = "true"
    Then Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YNNNNNNN |
    And Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-18"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-18 | 5             | 2                 | NNNNNNNN |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YNNNNNNN |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-18"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-18 | 5             | 2                 | NNNNNNNN |

  Scenario: Validate Qualified FPLOS Decisions for Restriction linked with Agile At Total Level gets generated Considering Service Cost When Configured for both the non overlapping season start date after Caught Up Date using Weighted Average of Master for first Season and Max of Non Master Room Type
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set isProfitOptimizationEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-11    | 2017-10-18  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-11    | 2017-10-18  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 2                 | 1             | 2017-10-19    | 2017-10-26  | 110    | 60     | 60      | 60        | 60       | 60     | 48       |
      | 4                         | 2                 | 8             | 2017-10-19    | 2017-10-26  | 115    | 65     | 65      | 65        | 65       | 65     | 58       |

    And I create a Servicing Cost configuration as below
      | Servicing_Cost_Cfg_ID | Property_ID | Business_Type_ID | Accom_Class_ID | Default_Evaluation_Method | Rate_Code | Full_Turn_Servicing_Cost | Interim_Servicing_Cost | Full_Servicing_Cost | Full_Servicing_Interval_Days |
      | 2                     | 11033       | 1                | 2              | 1                         | NULL      | 50                       | 40                     | 45                  | 1                            |
      | 3                     | 11033       | 1                | 3              | 1                         | NULL      | 60                       | 30                     | 55                  | 1                            |
      | 4                     | 11033       | 1                | 4              | 1                         | NULL      | 70                       | 20                     | 65                  | 1                            |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-18 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-18 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-18 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-19 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-20 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-21 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-22 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-23 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-24 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-25 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-26 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-19 | 8             | 24         | 132.00000 | -1  |
      | 11033       | 97          | 2017-10-20 | 8             | 24         | 132.00000 | -1  |
      | 11033       | 97          | 2017-10-21 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-22 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-23 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-24 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-25 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-26 | 8             | 24         | 105.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-26"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
      | 2017-10-18   | 38.00000  | 2              |
      | 2017-10-19   | 132.00000 | 2              |
      | 2017-10-20   | 132.00000 | 2              |
      | 2017-10-21   | 132.00000 | 2              |
      | 2017-10-22   | 132.00000 | 2              |
      | 2017-10-23   | 132.00000 | 2              |
      | 2017-10-24   | 132.00000 | 2              |
      | 2017-10-25   | 132.00000 | 2              |
      | 2017-10-26   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
    When I invoke Decision Qualified FPLOS generation Service with rate qualified ids = "2" and last loop = "true"
    Then Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-11"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-11 | 5             | 2                 | NNNNNNNN |
    And Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-19"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-19 | 5             | 2                 | YYNNNNNN |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-11"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-11 | 5             | 2                 | NNNNNNNN |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-19"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-19 | 5             | 2                 | YYNNNNNN |

  Scenario: Qualified FPLOS Decisions for Restriction linked with Agile having Rate configured Only for Master Accom Class Accom Type At Total Level gets generated Considering the Resetriction Start and End Date
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set isProfitOptimizationEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I set Consider Restriction Seasons Dates For Agile Qualified FPLOS parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I create a Servicing Cost configuration as below
      | Servicing_Cost_Cfg_ID | Property_ID | Business_Type_ID | Accom_Class_ID | Default_Evaluation_Method | Rate_Code | Full_Turn_Servicing_Cost | Interim_Servicing_Cost | Full_Servicing_Cost | Full_Servicing_Interval_Days |
      | 2                     | 11033       | 1                | 2              | 1                         | NULL      | 50                       | 40                     | 45                  | 1                            |
      | 3                     | 11033       | 1                | 3              | 1                         | NULL      | 60                       | 30                     | 55                  | 1                            |
      | 4                     | 11033       | 1                | 4              | 1                         | NULL      | 70                       | 20                     | 65                  | 1                            |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-18 | 5             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-19 | 5             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-20 | 5             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-21 | 5             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-22 | 5             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-23 | 5             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-24 | 5             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-25 | 5             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-26 | 5             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-27 | 5             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-28 | 5             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-29 | 5             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-30 | 5             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-18 | 6             | 24         | 132.00000 | -1  |
      | 11033       | 97          | 2017-10-19 | 6             | 24         | 132.00000 | -1  |
      | 11033       | 97          | 2017-10-20 | 6             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-21 | 6             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-22 | 6             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-23 | 6             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-24 | 6             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-25 | 6             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-26 | 6             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-27 | 6             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-28 | 6             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-29 | 6             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-30 | 6             | 24         | 105.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-18"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
      | 2017-10-18   | 38.00000  | 2              |
      | 2017-10-19   | 132.00000 | 2              |
      | 2017-10-20   | 132.00000 | 2              |
      | 2017-10-21   | 85.00000  | 2              |
      | 2017-10-22   | 94.00000  | 2              |
      | 2017-10-23   | 132.00000 | 2              |
      | 2017-10-24   | 132.00000 | 2              |
      | 2017-10-25   | 132.00000 | 2              |
      | 2017-10-26   | 132.00000 | 2              |
      | 2017-10-27   | 132.00000 | 2              |
      | 2017-10-28   | 132.00000 | 2              |
      | 2017-10-29   | 132.00000 | 2              |
      | 2017-10-30   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
    When I invoke Decision Qualified FPLOS generation Service with rate qualified ids = "2" and last loop = "true"
    Then Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYNNYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYNNYYYY |
    And Qualified FPLOS Decision table has "8" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2017-10-30"
    And Pace Qualified FPLOS Decision table also has "8" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2017-10-30"

  Scenario: Qualified FPLOS Decisions for Restriction linked with Agile having Rate configured Only for Non Master Accom Class Accom Type At Total Level gets generated Considering the Resetriction Start and End Date
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set isProfitOptimizationEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Enable FPLOS Qualified Recommendation loop step parameter to "true"
    And I set Consider Restriction Seasons Dates For Agile Qualified FPLOS parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 1             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 8             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I create a Servicing Cost configuration as below
      | Servicing_Cost_Cfg_ID | Property_ID | Business_Type_ID | Accom_Class_ID | Default_Evaluation_Method | Rate_Code | Full_Turn_Servicing_Cost | Interim_Servicing_Cost | Full_Servicing_Cost | Full_Servicing_Interval_Days |
      | 2                     | 11033       | 1                | 2              | 1                         | NULL      | 50                       | 40                     | 45                  | 1                            |
      | 3                     | 11033       | 1                | 3              | 1                         | NULL      | 60                       | 30                     | 55                  | 1                            |
      | 4                     | 11033       | 1                | 4              | 1                         | NULL      | 70                       | 20                     | 65                  | 1                            |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 1             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 1             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 1             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 1             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 1             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 1             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 1             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 1             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-18 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-19 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-20 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-21 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-22 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-23 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-24 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-25 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-26 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-27 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-28 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-29 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-30 | 1             | 24         | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-10 | 8             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 8             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 8             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 8             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 8             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 8             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 8             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 8             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-18 | 8             | 24         | 132.00000 | -1  |
      | 11033       | 97          | 2017-10-19 | 8             | 24         | 132.00000 | -1  |
      | 11033       | 97          | 2017-10-20 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-21 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-22 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-23 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-24 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-25 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-26 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-27 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-28 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-29 | 8             | 24         | 105.00000 | -1  |
      | 11033       | 97          | 2017-10-30 | 8             | 24         | 105.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-18"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
      | 2017-10-18   | 38.00000  | 2              |
      | 2017-10-19   | 132.00000 | 2              |
      | 2017-10-20   | 132.00000 | 2              |
      | 2017-10-21   | 85.00000  | 2              |
      | 2017-10-22   | 94.00000  | 2              |
      | 2017-10-23   | 132.00000 | 2              |
      | 2017-10-24   | 132.00000 | 2              |
      | 2017-10-25   | 132.00000 | 2              |
      | 2017-10-26   | 132.00000 | 2              |
      | 2017-10-27   | 132.00000 | 2              |
      | 2017-10-28   | 132.00000 | 2              |
      | 2017-10-29   | 132.00000 | 2              |
      | 2017-10-30   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
    When I invoke Decision Qualified FPLOS generation Service with rate qualified ids = "2" and last loop = "true"
    Then Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYNNYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Accom_Type_ID = "5" and Arrival_DT "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYNNYYYY |
    And Qualified FPLOS Decision table has "8" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2017-10-30"
    And Pace Qualified FPLOS Decision table also has "8" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2017-10-30"