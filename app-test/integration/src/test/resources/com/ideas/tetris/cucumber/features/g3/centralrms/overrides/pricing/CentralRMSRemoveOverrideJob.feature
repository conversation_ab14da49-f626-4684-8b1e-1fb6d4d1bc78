@CentralRMS @CentralRMSRemoveOvrJob @DailyRun
Feature: Verify G3 Central RMS Remove Override Job functionality

  Background:
    Given Configure CPIP01 for CentralRMS

    ############## For Only Special events dates ##########################

@Smoke
  Scenario: SC1-AA -Only Special Events- Verify Central RMS Remove Override  Job -where window contains the Special Event window and Ceil-Floor Ovr exists
    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-01 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "Only Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-13 | 3             | FLOORANDCEIL  | 100   | 250  |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-14 | 2021-05-18 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-19 | 2021-05-31 | 3             | FLOORANDCEIL      | 100       | 250      |
  @Smoke
  Scenario: SC1-BB - Only Special Events-Verify Central RMS Remove Override  Job -where  window contains the Special Event window and Spl Event window with DOW selected in input and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-01 | 2021-05-31 | "SUNDAY"   | "Only Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-15 | 3             | FLOORANDCEIL  | 100   | 250  |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-17 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-16 | 2021-05-16 | 3             | FLOORANDCEIL      | 100       | 250      |

@smoke
  Scenario: SC1-CC - Only Special Events-Verify Central RMS Remove Override  Job -where  window start date and Special Event start date are same and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-14 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "Only Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-14 | 2021-05-18 | 3             | FLOORANDCEIL  | 100   | 250  |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-19 | 2021-05-31 | 3             | FLOORANDCEIL      | 100       | 250      |
@smoke
  Scenario: SC1-DD - Only Special Events- Verify Central RMS Remove Override Job -where window start date is beyond  Special Event start date are same and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-15 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "Only Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-14 | 3             | FLOORANDCEIL  | 100   | 250  |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-19 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-15 | 2021-05-18 | 3             | FLOORANDCEIL      | 100       | 250      |

@smoke
  Scenario: SC2-AA - Only Special Events- Verify Central RMS Remove Override Job -where override window contains the 2 Special Events and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Important Day  | National Holiday | 2021-05-25 | 2021-05-26 | 1   | 0    | No               |

    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-01 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "Only Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-14 | 2021-05-18 | 3             | FLOORANDCEIL  | 100   | 250  |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-24 | 2021-05-26 | 3             | FLOORANDCEIL  | 100   | 250  |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-01 | 2021-05-13 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-19 | 2021-05-23 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-27 | 2021-05-31 | 3             | FLOORANDCEIL      | 100       | 250      |
@smoke
  Scenario: SC2-BB - Only Special Events -Verify Central RMS Remove Override Job -where override window contains the 2 Special Events and ovr window start date falls on SPl Event-1 Start Date and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Important Day  | National Holiday | 2021-05-25 | 2021-05-26 | 1   | 0    | No               |

    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-14 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "Only Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-13 | 3             | FLOORANDCEIL  | 100   | 250  |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-19 | 2021-05-23 | 3             | FLOORANDCEIL  | 100   | 250  |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-27 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-14 | 2021-05-18 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-24 | 2021-05-26 | 3             | FLOORANDCEIL      | 100       | 250      |

@smoke
  Scenario: SC2-CC - Only Special Events- Verify Central RMS Remove Override Job -where override window contains the 2 Special Events and ovr window start date falls within SPl Event-1 window and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Important Day  | National Holiday | 2021-05-25 | 2021-05-26 | 1   | 0    | No               |

    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-16 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "Only Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-15 | 3             | FLOORANDCEIL  | 100   | 250  |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-19 | 2021-05-23 | 3             | FLOORANDCEIL  | 100   | 250  |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-27 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-16 | 2021-05-18 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-24 | 2021-05-26 | 3             | FLOORANDCEIL      | 100       | 250      |
@smoke
  Scenario: SC2-DD - Only Special Events- Verify Central RMS Remove Override Job -where override window contains the 2 Special Events and ovr window overlaps SPl Event-1 and Spl Event-2 and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Important Day  | National Holiday | 2021-05-25 | 2021-05-26 | 1   | 0    | No               |

    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings     | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-16 | 2021-05-25 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "Only Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-15 | 3             | FLOORANDCEIL  | 100   | 250  |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-19 | 2021-05-23 | 3             | FLOORANDCEIL  | 100   | 250  |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-16 | 2021-05-18 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-24 | 2021-05-25 | 3             | FLOORANDCEIL      | 100       | 250      |
@smoke
  Scenario: SC2-EE - Only Special Events-Verify Central RMS Remove Override Job -where override window contains the 2 Special Events and ovr window starts from Spl Event-2 End Date and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Important Day  | National Holiday | 2021-05-25 | 2021-05-26 | 1   | 0    | No               |

    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-26 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "Only Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-25 | 3             | FLOORANDCEIL  | 100   | 250  |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-27 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-26 | 2021-05-26 | 3             | FLOORANDCEIL      | 100       | 250      |
@smoke
  Scenario: SC2-FF - Only Special Events -Verify Central RMS Remove Override Job -where override window contains the 2 Special Events and ovr window starts from Spl Event-2 End Date and Info Only Event Exists and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Important Day  | National Holiday | 2021-05-25 | 2021-05-26 | 1   | 0    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Info Only      | Local Observance | 2021-05-15 | 2021-05-30 | 1   | 0    | Yes              |

    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-26 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "Only Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-25 | 3             | FLOORANDCEIL  | 100   | 250  |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-26 | 2021-05-26 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-27 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |

@smoke
  Scenario: SC3-AA - Only Special Events -Verify Central RMS Remove Override  Job -where window contains the Special Event window and Specific Ovr exists
    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-01 | 2021-05-31 | 3             | USER          | 265      |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-01 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "Only Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-14 | 2021-05-18 | 3             | USER          | 265      |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Specific |
      | 2021-05-01 | 2021-05-13 | 3             | USER              | 265      |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Specific |
      | 2021-05-19 | 2021-05-31 | 3             | USER              | 265      |


  Scenario: SC3-BB - Only Special Events- Verify Central RMS Remove Override  Job -where window contains the Special Event window and Specific Ovr exists for All RTs
    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-01 | 2021-05-31 | All           | USER          | 265      |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |
    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              |  specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-01 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" |  "Only Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-14 | 2021-05-18 | All           | USER          | 265      |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Specific |
      | 2021-05-01 | 2021-05-13 | All           | USER              | 265      |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Specific |
      | 2021-05-19 | 2021-05-31 | All           | USER              | 265      |

  Scenario: SC3-CC - Only Special Events-Verify Central RMS Remove Override Job -where override window contains the 2 Special Events and Specific Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Important Day  | National Holiday | 2021-05-25 | 2021-05-26 | 1   | 0    | No               |

    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-01 | 2021-05-31 | 3             | USER          | 265      |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-01 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "Only Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-14 | 2021-05-18 | 3             | USER          | 265      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-24 | 2021-05-26 | 3             | USER          | 265      |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Specific |
      | 2021-05-01 | 2021-05-13 | 3             | USER              | 265      |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Specific |
      | 2021-05-19 | 2021-05-23 | 3             | USER              | 265      |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Specific |
      | 2021-05-27 | 2021-05-31 | 3             | USER              | 265      |


  Scenario: SC3-DD -Only Special Events- Verify Central RMS Remove Override Job -where override window contains the 2 Special Events and ovr window start date falls on SPl Event-1 Start Date and Specific Ovr exists
    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Important Day  | National Holiday | 2021-05-25 | 2021-05-26 | 1   | 0    | No               |

    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-01 | 2021-05-31 | 3             | USER          | 265      |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-14 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "Only Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-01 | 2021-05-13 | 3             | USER          | 265      |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-19 | 2021-05-23 | 3             | USER          | 265      |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-27 | 2021-05-31 | 3             | USER          | 265      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Specific |
      | 2021-05-14 | 2021-05-18 | 3             | USER              | 265      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Specific |
      | 2021-05-24 | 2021-05-26 | 3             | USER              | 265      |

  ################ Only Non-Special Event dates #######################################
  @smoke
  Scenario: SC1-AAA - Only Non-Special Event dates - Verify Central RMS Remove Override  Job -where window contains the Special Event window and Ceil-Floor Ovr exists
    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings          | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-01 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "Only Non-Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-13 | 3             | FLOORANDCEIL  | 100   | 250  |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-14 | 2021-05-18 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-19 | 2021-05-31 | 3             | FLOORANDCEIL      | 100       | 250      |


  Scenario: SC1-BBB - Only Non-Special Event dates - Verify Central RMS Remove Override  Job -where  window contains the Special Event window and Spl Event window with DOW selected in input and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek | specialEventsSettings           | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-01 | 2021-05-31 | "SUNDAY"   | "Only Non-Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-01 | 3             | FLOORANDCEIL  | 100   | 250  |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-03 | 2021-05-08 | 3             | FLOORANDCEIL  | 100   | 250  |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-10 | 2021-05-22 | 3             | FLOORANDCEIL  | 100   | 250  |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-24 | 2021-05-29 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-31 | 2021-05-31 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-02 | 2021-05-02 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-09 | 2021-05-09 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-23 | 2021-05-23 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-30 | 2021-05-30 | 3             | FLOORANDCEIL      | 100       | 250      |

  Scenario: SC1-CCC - Only Non-Special Event dates-Verify Central RMS Remove Override  Job -where  window start date and Special Event start date are same and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-14 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "Only Non-Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-14 | 2021-05-18 | 3             | FLOORANDCEIL  | 100   | 250  |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-19 | 2021-05-31 | 3             | FLOORANDCEIL      | 100       | 250      |

  Scenario: SC1-DDD - Only Non-Special Event dates- Verify Central RMS Remove Override Job -where window start date is beyond  Special Event start date are same and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-15 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "Only Non-Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-14 | 3             | FLOORANDCEIL  | 100   | 250  |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-19 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-15 | 2021-05-18 | 3             | FLOORANDCEIL      | 100       | 250      |


  Scenario: SC2-AAA - Only Non-Special Event dates- Verify Central RMS Remove Override Job -where override window contains the 2 Special Events and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Important Day  | National Holiday | 2021-05-25 | 2021-05-26 | 1   | 0    | No               |

    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-01 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "Only Non-Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-14 | 2021-05-18 | 3             | FLOORANDCEIL  | 100   | 250  |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-24 | 2021-05-26 | 3             | FLOORANDCEIL  | 100   | 250  |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-01 | 2021-05-13 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-19 | 2021-05-23 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-27 | 2021-05-31 | 3             | FLOORANDCEIL      | 100       | 250      |

  Scenario: SC2-BBB - Only Non-Special Event dates -Verify Central RMS Remove Override Job -where override window contains the 2 Special Events and ovr window start date falls on SPl Event-1 Start Date and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Important Day  | National Holiday | 2021-05-25 | 2021-05-26 | 1   | 0    | No               |

    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-14 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "Only Non-Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-19 | 2021-05-23 | 3             | FLOORANDCEIL  | 100   | 250  |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-27 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-14 | 2021-05-18 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-24 | 2021-05-26 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-13 | 3             | FLOORANDCEIL  | 100   | 250  |



  Scenario: SC2-CCC - Only Non-Special Event dates- Verify Central RMS Remove Override Job -where override window contains the 2 Special Events and ovr window start date falls within SPl Event-1 window and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Important Day  | National Holiday | 2021-05-25 | 2021-05-26 | 1   | 0    | No               |

    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-16 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "Only Non-Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-15 | 3             | FLOORANDCEIL  | 100   | 250  |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-16 | 2021-05-18 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-24 | 2021-05-26 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-19 | 2021-05-23 | 3             | FLOORANDCEIL  | 100   | 250  |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-27 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |

  @smoke
  Scenario: SC2-DDD - Only Non-Special Event dates- Verify Central RMS Remove Override Job -where override window contains the 2 Special Events and ovr window overlaps SPl Event-1 and Spl Event-2 and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Important Day  | National Holiday | 2021-05-25 | 2021-05-26 | 1   | 0    | No               |

    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings     | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-16 | 2021-05-25 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "Only Non-Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-15 | 3             | FLOORANDCEIL  | 100   | 250  |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-26 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-16 | 2021-05-18 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-24 | 2021-05-25 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-19 | 2021-05-23 | 3             | FLOORANDCEIL  | 100   | 250  |

  Scenario: SC2-EEE - Only Non-Special Event dates-Verify Central RMS Remove Override Job -where override window contains the 2 Special Events and ovr window starts from Spl Event-2 End Date and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Important Day  | National Holiday | 2021-05-25 | 2021-05-26 | 1   | 0    | No               |

    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-26 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "Only Non-Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-25 | 3             | FLOORANDCEIL  | 100   | 250  |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-26 | 2021-05-26 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-27 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |


  Scenario: SC2-FFF - Only Non-Special Event dates -Verify Central RMS Remove Override Job -where override window contains the 2 Special Events and ovr window starts from Spl Event-2 End Date and Info Only Event Exists and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Important Day  | National Holiday | 2021-05-25 | 2021-05-26 | 1   | 0    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Info Only      | Local Observance | 2021-05-15 | 2021-05-30 | 1   | 0    | Yes              |

    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-26 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "Only Non-Special Event dates" | "National Holiday","Local Observance" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-25 | 3             | FLOORANDCEIL  | 100   | 250  |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-26 | 2021-05-26 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-27 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |


  Scenario: SC3-AAA - Only Non-Special Event dates -Verify Central RMS Remove Override  Job -where window contains the Special Event window and Specific Ovr exists
    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-01 | 2021-05-31 | 3             | USER          | 265      |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-01 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "Only Non-Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-14 | 2021-05-18 | 3             | USER          | 265      |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Specific |
      | 2021-05-01 | 2021-05-13 | 3             | USER              | 265      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Specific |
      | 2021-05-19 | 2021-05-31 | 3             | USER              | 265      |


  Scenario: SC3-BBB - Only Non-Special Event dates- Verify Central RMS Remove Override  Job -where window contains the Special Event window and Specific Ovr exists for All RTs
    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-01 | 2021-05-31 | All           | USER          | 265      |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |
    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              |  specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-01 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" |  "Only Non-Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-14 | 2021-05-18 | All           | USER          | 265      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Specific |
      | 2021-05-01 | 2021-05-13 | All           | USER              | 265      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Specific |
      | 2021-05-19 | 2021-05-31 | All           | USER              | 265      |

  Scenario: SC3-CCC - Only Non-Special Event dates-Verify Central RMS Remove Override Job -where override window contains the 2 Special Events and Specific Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Important Day  | National Holiday | 2021-05-25 | 2021-05-26 | 1   | 0    | No               |

    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-01 | 2021-05-31 | 3             | USER          | 265      |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-01 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "Only Non-Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-14 | 2021-05-18 | 3             | USER          | 265      |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-24 | 2021-05-26 | 3             | USER          | 265      |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Specific |
      | 2021-05-01 | 2021-05-13 | 3             | USER              | 265      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Specific |
      | 2021-05-19 | 2021-05-23 | 3             | USER              | 265      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Specific |
      | 2021-05-27 | 2021-05-31 | 3             | USER              | 265      |


  Scenario: SC3-DDD -Only Non-Special Event dates- Verify Central RMS Remove Override Job -where override window contains the 2 Special Events and ovr window start date falls on SPl Event-1 Start Date and Specific Ovr exists
    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Important Day  | National Holiday | 2021-05-25 | 2021-05-26 | 1   | 0    | No               |

    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-01 | 2021-05-31 | 3             | USER          | 265      |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-14 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "Only Non-Special Event dates" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-19 | 2021-05-23 | 3             | USER          | 265      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-27 | 2021-05-31 | 3             | USER          | 265      |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Specific |
      | 2021-05-14 | 2021-05-18 | 3             | USER              | 265      |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Specific |
      | 2021-05-24 | 2021-05-26 | 3             | USER              | 265      |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-01 | 2021-05-13 | 3             | USER          | 265      |

    ##################### For all date ranges ##################################
  @smoke
  Scenario: SC1-AAAA - All dates in the date range - Verify Central RMS Remove Override  Job -where window contains the Special Event window and Ceil-Floor Ovr exists
    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings          | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-01 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "All dates in the date range" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |

  @smoke
  Scenario: SC1-BBBB - All dates in the date range - Verify Central RMS Remove Override  Job -where  window contains the Special Event window and Spl Event window with DOW selected in input and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek | specialEventsSettings           | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-01 | 2021-05-31 | "SUNDAY"   | "All dates in the date range" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-01 | 3             | FLOORANDCEIL  | 100   | 250  |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-03 | 2021-05-08 | 3             | FLOORANDCEIL  | 100   | 250  |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-10 | 2021-05-15 | 3             | FLOORANDCEIL  | 100   | 250  |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-17 | 2021-05-22 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-24 | 2021-05-29 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-02 | 2021-05-02 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-09 | 2021-05-09 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-23 | 2021-05-23 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-30 | 2021-05-30 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-16 | 2021-05-16 | 3             | FLOORANDCEIL      | 100       | 250      |

  Scenario: SC1-CCCC - All dates in the date range-Verify Central RMS Remove Override  Job -where  window start date and Special Event start date are same and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-14 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "All dates in the date range" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |


    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-14 | 2021-05-31 | 3             | FLOORANDCEIL      | 100       | 250      |


  Scenario: SC1-DDDD - All dates in the date range- Verify Central RMS Remove Override Job -where window start date is beyond  Special Event start date are same and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-15 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "All dates in the date range" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |



    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-15 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |




  Scenario: SC2-AAAA - All dates in the date range- Verify Central RMS Remove Override Job -where override window contains the 2 Special Events and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Important Day  | National Holiday | 2021-05-25 | 2021-05-26 | 1   | 0    | No               |

    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-01 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "All dates in the date range" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL      | 100       | 250      |


  Scenario: SC2-BBBB - All dates in the date range -Verify Central RMS Remove Override Job -where override window contains the 2 Special Events and ovr window start date falls on SPl Event-1 Start Date and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Important Day  | National Holiday | 2021-05-25 | 2021-05-26 | 1   | 0    | No               |

    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-14 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "All dates in the date range" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-14 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-13 | 3             | FLOORANDCEIL  | 100   | 250  |



  Scenario: SC2-CCCC - All dates in the date range- Verify Central RMS Remove Override Job -where override window contains the 2 Special Events and ovr window start date falls within SPl Event-1 window and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Important Day  | National Holiday | 2021-05-25 | 2021-05-26 | 1   | 0    | No               |

    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-16 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "All dates in the date range" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-15 | 3             | FLOORANDCEIL  | 100   | 250  |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-16 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |



  Scenario: SC2-DDDD - All dates in the date range- Verify Central RMS Remove Override Job -where override window contains the 2 Special Events and ovr window overlaps SPl Event-1 and Spl Event-2 and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Important Day  | National Holiday | 2021-05-25 | 2021-05-26 | 1   | 0    | No               |

    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings     | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-16 | 2021-05-25 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "All dates in the date range" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-15 | 3             | FLOORANDCEIL  | 100   | 250  |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-26 | 2021-05-31 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-16 | 2021-05-25 | 3             | FLOORANDCEIL  | 100   | 250  |


  Scenario: SC2-EEEE - All dates in the date range-Verify Central RMS Remove Override Job -where override window contains the 2 Special Events and ovr window starts from Spl Event-2 End Date and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Important Day  | National Holiday | 2021-05-25 | 2021-05-26 | 1   | 0    | No               |

    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-26 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "All dates in the date range" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Old_Floor | Old_Ceil |
      | 2021-05-01 | 2021-05-25 | 3             | FLOORANDCEIL      | 100       | 250      |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-26 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |


  Scenario: SC2-FFF - All dates in the date range -Verify Central RMS Remove Override Job -where override window contains the 2 Special Events and ovr window starts from Spl Event-2 End Date and Info Only Event Exists and Ceil-Floor Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Important Day  | National Holiday | 2021-05-25 | 2021-05-26 | 1   | 0    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Info Only      | Local Observance | 2021-05-15 | 2021-05-30 | 1   | 0    | Yes              |

    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings         | specialEvents                         | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-26 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "All dates in the date range" | "National Holiday","Local Observance" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-01 | 2021-05-25 | 3             | FLOORANDCEIL  | 100   | 250  |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Floor | Ceil |
      | 2021-05-26 | 2021-05-31 | 3             | FLOORANDCEIL  | 100   | 250  |


  Scenario: SC3-AAAA - All dates in the date range -Verify Central RMS Remove Override  Job -where window contains the Special Event window and Specific Ovr exists
    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-01 | 2021-05-31 | 3             | USER          | 265      |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-01 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "All dates in the date range" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Specific |
      | 2021-05-01 | 2021-05-31 | 3             | USER              | 265      |


  Scenario: SC3-BBBB - All dates in the date range- Verify Central RMS Remove Override  Job -where window contains the Special Event window and Specific Ovr exists for All RTs
    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-01 | 2021-05-31 | All           | USER          | 265      |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |
    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              |  specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-01 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" |  "All dates in the date range" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |
    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-01 | 2021-05-31 | All           | USER          | 265      |


  Scenario: SC3-CCCC - All dates in the date range-Verify Central RMS Remove Override Job -where override window contains the 2 Special Events and Specific Ovr exists

    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Important Day  | National Holiday | 2021-05-25 | 2021-05-26 | 1   | 0    | No               |

    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-01 | 2021-05-31 | 3             | USER          | 265      |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-01 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "All dates in the date range" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Old_Override_Type | Specific |
      | 2021-05-01 | 2021-05-31 | 3             | USER              | 265      |


  Scenario: SC3-DDDD -All dates in the date range- Verify Central RMS Remove Override Job -where override window contains the 2 Special Events and ovr window start date falls on SPl Event-1 Start Date and Specific Ovr exists
    Given CP_Decision_Bar_Output data exists for specified date range
      | Start_Date | End_Date   |
      | 2021-05-01 | 2021-05-31 |
    Given Special Events are cleared
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Victoria Day   | National Holiday | 2021-05-17 | 2021-05-17 | 3   | 1    | No               |
    Given Following Special Event Exists
      | Spl_Event_Name | Spl_Event_Type   | Start_Date | End_Date   | Pre | Post | Information_Only |
      | Important Day  | National Holiday | 2021-05-25 | 2021-05-26 | 1   | 0    | No               |

    Given Following overrides are added for specified date range
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-01 | 2021-05-31 | 3             | USER          | 265      |
    Given All Seasons are cleared
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | NULL       | NULL     | 150.00            | 300.00           | 150.00            | 300.00           | 150.00             | 300.00            | 150.00               | 300.00              | 150.00              | 300.00             | 150.00            | 300.00           | 150.00              | 300.00             | Default     | 150.00                  | 300.00                 | 150.00                  | 300.00                 | 150.00                   | 300.00                  | 150.00                     | 300.00                    | 150.00                    | 300.00                   | 150.00                  | 300.00                 | 150.00                    | 300.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | NULL       | NULL     | 175.00            | 400.00           | 175.00            | 400.00           | 175.00             | 400.00            | 175.00               | 400.00              | 175.00              | 400.00             | 175.00            | 400.00           | 175.00              | 400.00             | Default     | 175.00                  | 400.00                 | 175.00                  | 400.00                 | 175.00                   | 400.00                  | 175.00                     | 400.00                    | 175.00                    | 400.00                   | 175.00                  | 400.00                 | 175.00                    | 400.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | STANDARD    | 3             | 2021-05-08 | 2021-05-15 | 250.00            | 450.00           | 250.00            | 450.00           | 250.00             | 450.00            | 250.00               | 450.00              | 250.00              | 450.00             | 250.00            | 450.00           | 250.00              | 450.00             | Season_01Jan2021_31Dec2021 | 250.00                  | 450.00                 | 250.00                  | 450.00                 | 250.00                   | 450.00                  | 250.00                     | 450.00                    | 250.00                    | 450.00                   | 250.00                  | 450.00                 | 250.00                    | 450.00                   |
    Given Existing Config is present in Pricing configuration
      | Accom_Class | Accom_Type_ID | Start_Date | End_Date   | Sunday_Floor_Rate | Sunday_Ceil_Rate | Monday_Floor_Rate | Monday_Ceil_Rate | Tuesday_Floor_Rate | Tuesday_Ceil_Rate | Wednesday_Floor_Rate | Wednesday_Ceil_Rate | Thursday_Floor_Rate | Thursday_Ceil_Rate | Friday_Floor_Rate | Friday_Ceil_Rate | Saturday_Floor_Rate | Saturday_Ceil_Rate | Season_Name                | Sunday_Floor_Rate_w_Tax | Sunday_Ceil_Rate_w_Tax | Monday_Floor_Rate_w_Tax | Monday_Ceil_Rate_w_Tax | Tuesday_Floor_Rate_w_Tax | Tuesday_Ceil_Rate_w_Tax | Wednesday_Floor_Rate_w_Tax | Wednesday_Ceil_Rate_w_Tax | Thursday_Floor_Rate_w_Tax | Thursday_Ceil_Rate_w_Tax | Friday_Floor_Rate_w_Tax | Friday_Ceil_Rate_w_Tax | Saturday_Floor_Rate_w_Tax | Saturday_Ceil_Rate_w_Tax |
      | DELUXE      | 7             | 2021-05-08 | 2021-05-15 | 400.00            | 500.00           | 400.00            | 500.00           | 400.00             | 500.00            | 400.00               | 500.00              | 400.00              | 500.00             | 400.00            | 500.00           | 400.00              | 500.00             | Season_01Jan2021_31Dec2021 | 400.00                  | 500.00                 | 400.00                  | 500.00                 | 400.00                   | 500.00                  | 400.00                     | 500.00                    | 400.00                    | 500.00                   | 400.00                  | 500.00                 | 400.00                    | 500.00                   |

    When CentralRMS Remove Override JobV2 is triggered with Following settings
      | propertyCodes | startDate  | endDate    | daysOfWeek                                                              | specialEventsSettings      | specialEvents      | overridesToRemove            | roomClasses         |
      | YYZBO         | 2021-05-14 | 2021-05-31 | "SUNDAY", "MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY" | "All dates in the date range" | "National Holiday" | "SPECIFIC","FLOOR","CEILING" | "STANDARD","DELUXE" |

    Then Overrides for following dates are deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-14 | 2021-05-31 | 3             | USER          | 265      |
    Then Overrides for following dates are not deleted
      | Start_Date | End_Date   | Accom_Type_ID | Override_Type | Specific |
      | 2021-05-01 | 2021-05-13 | 3             | USER          | 265      |

