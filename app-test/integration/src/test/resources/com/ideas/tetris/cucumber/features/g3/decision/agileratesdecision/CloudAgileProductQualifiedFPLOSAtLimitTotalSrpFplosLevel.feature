@Decision @AgileProductQualifiedFPLOSDecisionGeneration
Feature:  FPLOS Decisions for Agile Product linked Qualified Rate Plans At Limit Total SRP FPLOS Level

  Scenario: Validate Qualified FPLOS Decisions For Restriction Rates At Limit Total SRP FPLOS Level
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set enableFplosService parameter to "true"
    And I set isDiffEnabledFPLOS parameter to "true"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "false"
    And I set GenerateLimitTotalSRPRates parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "false"
    And I set Agile Rates Enabled parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have configured following Limit Total Qualified Rates
      | Rate_Qualified_ID |
      | 2                 |
    And I have Agile Product configured
      | Product_ID | Name      | Code        | Status_ID |
      | 5          | Product A | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 1          | 100.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 1          | 100.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have not linked the rate plan with agile product
    When I execute Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYYYYYYY |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |
      | 2017-10-10 | 6             | 3                 | YYYYYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYYYYYYY |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |
      | 2017-10-10 | 6             | 3                 | YYYYYYYY |

  Scenario: Validate Qualified FPLOS Decisions For Restriction Rates not linked with agile product And isAgileRateFPLOSGenerationEnabled is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set enableFplosService parameter to "true"
    And I set isDiffEnabledFPLOS parameter to "true"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "false"
    And I set GenerateLimitTotalSRPRates parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have configured following Limit Total Qualified Rates
      | Rate_Qualified_ID |
      | 2                 |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have not linked the rate plan with agile product
    When I execute Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYYYYYYY |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |
      | 2017-10-10 | 6             | 3                 | YYYYYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYYYYYYY |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |
      | 2017-10-10 | 6             | 3                 | YYYYYYYY |

  Scenario: Validate Qualified FPLOS Decisions For Restriction Rates linked with Agile Product and isAgileRateFPLOSGenerationEnabled is false
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set enableFplosService parameter to "true"
    And I set isDiffEnabledFPLOS parameter to "true"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "false"
    And I set GenerateLimitTotalSRPRates parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "false"
    And I set Agile Rates Enabled parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have configured following Limit Total Qualified Rates
      | Rate_Qualified_ID |
      | 2                 |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
      | 24         | 3                 |
    When I execute Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYYYYYYY |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |
      | 2017-10-10 | 6             | 3                 | YYYYYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYYYYYYY |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |
      | 2017-10-10 | 6             | 3                 | YYYYYYYY |

  Scenario: Validate Qualified FPLOS Decisions For Restriction Rates linked with inactive Agile Product and isAgileRateFPLOSGenerationEnabled is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set enableFplosService parameter to "true"
    And I set isDiffEnabledFPLOS parameter to "true"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "false"
    And I set GenerateLimitTotalSRPRates parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have configured following Limit Total Qualified Rates
      | Rate_Qualified_ID |
      | 2                 |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 4         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
    When I execute Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |
      | 2017-10-10 | 6             | 3                 | YYYYYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |
      | 2017-10-10 | 6             | 3                 | YYYYYYYY |

  Scenario: Validate Qualified FPLOS Decisions For Restriction Rates linked with deleted Agile Product and isAgileRateFPLOSGenerationEnabled is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set enableFplosService parameter to "true"
    And I set isDiffEnabledFPLOS parameter to "true"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "false"
    And I set GenerateLimitTotalSRPRates parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have configured following Limit Total Qualified Rates
      | Rate_Qualified_ID |
      | 2                 |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 2         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
      | 24         | 3                 |
    When I execute Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decisions Not Generated
    And Pace Qualified FPLOS Decisions Not Generated

  Scenario: Validate Qualified FPLOS Decisions For Restriction Rates linked with Agile Product and isAgileRateFPLOSGenerationEnabled is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set enableFplosService parameter to "true"
    And I set isDiffEnabledFPLOS parameter to "true"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "false"
    And I set GenerateLimitTotalSRPRates parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have configured following Limit Total Qualified Rates
      | Rate_Qualified_ID |
      | 2                 |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
      | 24         | 3                 |
    When I execute Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYNNYYYY |
      | 2017-10-10 | 5             | 3                 | YNNNNNNN |
      | 2017-10-10 | 6             | 3                 | YYNNYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYNNYYYY |
      | 2017-10-10 | 5             | 3                 | YNNNNNNN |
      | 2017-10-10 | 6             | 3                 | YYNNYYYY |

  Scenario: Validate Qualified FPLOS Decisions For a non-yieldable Restriction Rate linked with Agile Product
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set enableFplosService parameter to "true"
    And I set isDiffEnabledFPLOS parameter to "true"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "false"
    And I set GenerateLimitTotalSRPRates parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 0         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have configured following Limit Total Qualified Rates
      | Rate_Qualified_ID |
      | 2                 |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product A | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
      | 24         | 3                 |
    When I execute Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYNNYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYNNYYYY |

  Scenario: Validate Qualified FPLOS Decisions For a inactive Restriction Rate linked with Agile Product
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set enableFplosService parameter to "true"
    And I set isDiffEnabledFPLOS parameter to "true"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "false"
    And I set GenerateLimitTotalSRPRates parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 4         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have configured following Limit Total Qualified Rates
      | Rate_Qualified_ID |
      | 3                 |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
      | 24         | 3                 |
    When I execute Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 3                 | YYNNYYYY |
    And Pace Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-10"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 3                 | YYNNYYYY |

  Scenario: Validate Qualified FPLOS Decisions For Restriction Rates linked with Agile Product and rate plan configuration date range is larger than agile product configuration date range in FPLOS generation date range
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set enableFplosService parameter to "true"
    And I set isDiffEnabledFPLOS parameter to "true"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "false"
    And I set GenerateLimitTotalSRPRates parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "false"
    And I set Agile Rates Enabled parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2018-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2018-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-24  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-24  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-24  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-24  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have configured following Limit Total Qualified Rates
      | Rate_Qualified_ID |
      | 2                 |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-24"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
      | 2017-10-18   | 132.00000 | 2              |
      | 2017-10-19   | 132.00000 | 2              |
      | 2017-10-20   | 132.00000 | 2              |
      | 2017-10-21   | 132.00000 | 2              |
      | 2017-10-22   | 132.00000 | 2              |
      | 2017-10-23   | 132.00000 | 2              |
      | 2017-10-24   | 132.00000 | 2              |
    When I execute Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-17"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYYYYYYY |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |
      | 2017-10-10 | 6             | 3                 | YYYYYYYY |
      | 2017-10-11 | 5             | 2                 | NNNNYNNN |
      | 2017-10-11 | 5             | 3                 | NNYYYYYY |
      | 2017-10-11 | 6             | 3                 | NNNNYNNN |
      | 2017-10-12 | 5             | 2                 | NYYYYNNN |
      | 2017-10-12 | 5             | 3                 | NYYYYYYY |
      | 2017-10-12 | 6             | 3                 | NNYYYNNN |
      | 2017-10-13 | 5             | 2                 | YYYYYNNN |
      | 2017-10-13 | 5             | 3                 | YYYYYYYY |
      | 2017-10-13 | 6             | 3                 | YYYYYNNN |
      | 2017-10-14 | 5             | 2                 | YYYNNNNN |
      | 2017-10-14 | 5             | 3                 | YYYYYNNN |
      | 2017-10-14 | 6             | 3                 | YYYNNNNN |
      | 2017-10-15 | 5             | 2                 | YYNNNNNN |
      | 2017-10-15 | 5             | 3                 | YYYNNNNN |
      | 2017-10-15 | 6             | 3                 | YYNNNNNN |
      | 2017-10-16 | 5             | 2                 | NNNNNNNN |
      | 2017-10-16 | 5             | 3                 | NNNNNNNN |
      | 2017-10-16 | 6             | 3                 | NNNNNNNN |
      | 2017-10-17 | 5             | 2                 | NNNNNNNN |
      | 2017-10-17 | 5             | 3                 | NNNNNNNN |
      | 2017-10-17 | 6             | 3                 | NNNNNNNN |
    And Pace Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-17"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYYYYYYY |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |
      | 2017-10-10 | 6             | 3                 | YYYYYYYY |
      | 2017-10-11 | 5             | 2                 | NNNNYNNN |
      | 2017-10-11 | 5             | 3                 | NNYYYYYY |
      | 2017-10-11 | 6             | 3                 | NNNNYNNN |
      | 2017-10-12 | 5             | 2                 | NYYYYNNN |
      | 2017-10-12 | 5             | 3                 | NYYYYYYY |
      | 2017-10-12 | 6             | 3                 | NNYYYNNN |
      | 2017-10-13 | 5             | 2                 | YYYYYNNN |
      | 2017-10-13 | 5             | 3                 | YYYYYYYY |
      | 2017-10-13 | 6             | 3                 | YYYYYNNN |
      | 2017-10-14 | 5             | 2                 | YYYNNNNN |
      | 2017-10-14 | 5             | 3                 | YYYYYNNN |
      | 2017-10-14 | 6             | 3                 | YYYNNNNN |
      | 2017-10-15 | 5             | 2                 | YYNNNNNN |
      | 2017-10-15 | 5             | 3                 | YYYNNNNN |
      | 2017-10-15 | 6             | 3                 | YYNNNNNN |
      | 2017-10-16 | 5             | 2                 | NNNNNNNN |
      | 2017-10-16 | 5             | 3                 | NNNNNNNN |
      | 2017-10-16 | 6             | 3                 | NNNNNNNN |
      | 2017-10-17 | 5             | 2                 | NNNNNNNN |
      | 2017-10-17 | 5             | 3                 | NNNNNNNN |
      | 2017-10-17 | 6             | 3                 | NNNNNNNN |
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 50.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 40.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-18 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-19 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-20 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-21 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-22 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-23 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-24 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 70.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 50.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-18 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-19 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-20 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-21 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-22 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-23 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-24 | 6             | 24         | 150.00000 | -1  |
    And I execute Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has records as below for Arrival_DT between "2017-10-10" and "2017-10-17"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |
      | 2017-10-10 | 6             | 3                 | YYYYYYYY |
      | 2017-10-11 | 5             | 3                 | NNYYYYYY |
      | 2017-10-11 | 6             | 3                 | NNNNYNNN |
      | 2017-10-12 | 5             | 2                 | NNNNNNNY |
      | 2017-10-12 | 5             | 3                 | NYYYYYYY |
      | 2017-10-12 | 6             | 3                 | NNYYYNNN |
      | 2017-10-13 | 5             | 2                 | NYYYYYYY |
      | 2017-10-13 | 5             | 3                 | YYYYYYYY |
      | 2017-10-13 | 6             | 3                 | YYYYYNNN |
      | 2017-10-14 | 5             | 2                 | YYYYYYYY |
      | 2017-10-14 | 5             | 3                 | YYYYYNNN |
      | 2017-10-14 | 6             | 3                 | YYYNNNNN |
      | 2017-10-15 | 5             | 2                 | YYYYYYYY |
      | 2017-10-15 | 5             | 3                 | YYYNNNNN |
      | 2017-10-15 | 6             | 3                 | YYNNNNNN |
      | 2017-10-16 | 5             | 2                 | YYYYYYYY |
      | 2017-10-16 | 5             | 3                 | NNNNNNNN |
      | 2017-10-16 | 6             | 3                 | NNNNNNNN |
      | 2017-10-17 | 5             | 2                 | YYYYNYYY |
      | 2017-10-17 | 5             | 3                 | NNNNNNNN |
      | 2017-10-17 | 6             | 3                 | NNNNNNNN |
    And Pace Qualified FPLOS Decision for last pace point has records as below for Arrival_DT between "2017-10-10" and "2017-10-17"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-10 | 5             | 2                 | YYYYYYYY |
      | 2017-10-10 | 5             | 3                 | YYYYYYYY |
      | 2017-10-10 | 6             | 3                 | YYYYYYYY |
      | 2017-10-11 | 5             | 2                 | NNNNYNNN |
      | 2017-10-11 | 5             | 3                 | NNYYYYYY |
      | 2017-10-11 | 6             | 3                 | NNNNYNNN |
      | 2017-10-12 | 5             | 2                 | NYYYYNNN |
      | 2017-10-12 | 5             | 3                 | NYYYYYYY |
      | 2017-10-12 | 6             | 3                 | NNYYYNNN |
      | 2017-10-13 | 5             | 2                 | YYYYYNNN |
      | 2017-10-13 | 5             | 3                 | YYYYYYYY |
      | 2017-10-13 | 6             | 3                 | YYYYYNNN |
      | 2017-10-14 | 5             | 2                 | YYYNNNNN |
      | 2017-10-14 | 5             | 3                 | YYYYYNNN |
      | 2017-10-14 | 6             | 3                 | YYYNNNNN |
      | 2017-10-15 | 5             | 2                 | YYNNNNNN |
      | 2017-10-15 | 5             | 3                 | YYYNNNNN |
      | 2017-10-15 | 6             | 3                 | YYNNNNNN |
      | 2017-10-16 | 5             | 2                 | NNNNNNNN |
      | 2017-10-16 | 5             | 3                 | NNNNNNNN |
      | 2017-10-16 | 6             | 3                 | NNNNNNNN |
      | 2017-10-17 | 5             | 2                 | NNNNNNNN |
      | 2017-10-17 | 5             | 3                 | NNNNNNNN |
      | 2017-10-17 | 6             | 3                 | NNNNNNNN |
    And Pace Qualified FPLOS Decision for latest pace point has records as below for Arrival_DT between "2017-10-10" and "2017-10-17"
      | Arrival_DT | Accom_Type_ID | Rate_Qualified_ID | FPLOS    |
      | 2017-10-12 | 5             | 2                 | NNNNNNNY |
      | 2017-10-13 | 5             | 2                 | NYYYYYYY |
      | 2017-10-14 | 5             | 2                 | YYYYYYYY |
      | 2017-10-15 | 5             | 2                 | YYYYYYYY |
      | 2017-10-16 | 5             | 2                 | YYYYYYYY |
      | 2017-10-17 | 5             | 2                 | YYYYNYYY |

  Scenario: Validate Qualified FPLOS Decisions For Restriction Rates linked with Agile Product and isAgileRateFPLOSGenerationEnabled is true but with insufficient decisions to generate FPLOS
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set enableFplosService parameter to "true"
    And I set isDiffEnabledFPLOS parameter to "true"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "false"
    And I set GenerateLimitTotalSRPRates parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have configured following Limit Total Qualified Rates
      | Rate_Qualified_ID |
      | 2                 |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-18 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-18 | 6             | 24         | 150.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
      | 24         | 3                 |
    When I execute Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decisions Not Generated
    And Pace Qualified FPLOS Decisions Not Generated

  Scenario: Validate Qualified FPLOS Decisions For Restriction Rates linked with Agile Product and isAgileRateFPLOSGenerationEnabled is true but with Final BAR is null because of zero capacity RT
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set enableFplosService parameter to "true"
    And I set isDiffEnabledFPLOS parameter to "true"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "false"
    And I set GenerateLimitTotalSRPRates parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-01-01    | 2017-12-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 5             | 2017-10-10    | 2017-10-17  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-10-17  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have configured following Limit Total Qualified Rates
      | Rate_Qualified_ID |
      | 2                 |
    And I have a new Agile Product configured as below
      | Product_ID | Name      | Code        | Status_ID |
      | 24         | Product Z | AGILE_RATES | 1         |
    And Agile Product decisions are generated as below for Accom_Type_ID "5" and configured Product_ID "1" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 1          | 80.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 1          | 80.00000  | -1  |
    And Agile Product decisions are generated as below
      | Property_ID | Decision_ID | Arrival_DT | Accom_Type_ID | Product_ID | Final_BAR | LOS |
      | 11033       | 97          | 2017-10-10 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 5             | 24         | NULL      | -1  |
      | 11033       | 97          | 2017-10-12 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 5             | 24         | 60.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-15 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-16 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-17 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-18 | 5             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-10 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-11 | 6             | 24         | NULL      | -1  |
      | 11033       | 97          | 2017-10-12 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-13 | 6             | 24         | 90.00000  | -1  |
      | 11033       | 97          | 2017-10-14 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-15 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-16 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-17 | 6             | 24         | 150.00000 | -1  |
      | 11033       | 97          | 2017-10-18 | 6             | 24         | 150.00000 | -1  |
    And LRV Decisions are already available for Accom_Class_ID "2" and Arrival_DT between "2017-10-10" and "2017-10-17"
      | Occupancy_DT | LRV       | Accom_Class_ID |
      | 2017-10-10   | 38.00000  | 2              |
      | 2017-10-11   | 132.00000 | 2              |
      | 2017-10-12   | 132.00000 | 2              |
      | 2017-10-13   | 85.00000  | 2              |
      | 2017-10-14   | 94.00000  | 2              |
      | 2017-10-15   | 132.00000 | 2              |
      | 2017-10-16   | 132.00000 | 2              |
      | 2017-10-17   | 132.00000 | 2              |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 24         | 2                 |
      | 24         | 3                 |
    When I execute Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decisions Not Generated
    And Pace Qualified FPLOS Decisions Not Generated

  Scenario: Validate Qualified FPLOS gets generated for all dates for qualified rate having weekly seasons having start date is equal to or after caught up date and end dates is less than optimization end dates when rate plan configuration date range is less than agile product configuration date range for FPLOS generation with toggle limit total as true and total level as false and considerRestrictionSeasonDatesForAgileFPLOS is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set enableFplosService parameter to "true"
    And I set isDiffEnabledFPLOS parameter to "true"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "false"
    And I set GenerateLimitTotalSRPRates parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Consider Restriction Seasons Dates For Agile Qualified FPLOS parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 6             | 2017-11-01    | 2017-11-07  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-11-08    | 2017-11-15  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 3                         | 2                 | 6             | 2017-11-16    | 2017-11-23  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 2                 | 6             | 2017-11-24    | 2017-11-30  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 5                         | 2                 | 6             | 2017-12-01    | 2017-12-07  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 7                         | 2                 | 6             | 2017-12-08    | 2017-12-15  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 8                         | 2                 | 6             | 2017-12-16    | 2017-12-23  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 9                         | 2                 | 6             | 2017-12-24    | 2017-12-31  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have configured following Limit Total Qualified Rates
      | Rate_Qualified_ID |
      | 2                 |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 21         | 2                 |
      | 21         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has "61" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "61" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"

  Scenario: Validate Qualified FPLOS gets generated for all dates for both qualified rate  mapeed to different product having weekly seasons having start date is equal to or after caught up date and end dates is less than optimization end dates when rate plan configuration date range is less than agile product configuration date range for FPLOS generation with toggle limit total as true and total level as false and considerRestrictionSeasonDatesForAgileFPLOS is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set enableFplosService parameter to "true"
    And I set isDiffEnabledFPLOS parameter to "true"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "false"
    And I set GenerateLimitTotalSRPRates parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Consider Restriction Seasons Dates For Agile Qualified FPLOS parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 6             | 2017-11-01    | 2017-11-07  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-11-08    | 2017-11-15  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 3                         | 2                 | 6             | 2017-11-16    | 2017-11-23  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 2                 | 6             | 2017-11-24    | 2017-11-30  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 5                         | 2                 | 6             | 2017-12-01    | 2017-12-07  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 7                         | 2                 | 6             | 2017-12-08    | 2017-12-15  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 8                         | 2                 | 6             | 2017-12-16    | 2017-12-23  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 9                         | 2                 | 6             | 2017-12-24    | 2017-12-31  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 10                        | 3                 | 3             | 2017-11-01    | 2017-11-07  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 11                        | 3                 | 3             | 2017-11-08    | 2017-11-15  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 12                        | 3                 | 3             | 2017-11-16    | 2017-11-23  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 13                        | 3                 | 3             | 2017-11-24    | 2017-11-30  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 14                        | 3                 | 2             | 2017-12-01    | 2017-12-07  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 15                        | 3                 | 2             | 2017-12-08    | 2017-12-15  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 16                        | 3                 | 2             | 2017-12-16    | 2017-12-23  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 17                        | 3                 | 2             | 2017-12-24    | 2017-12-31  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have configured following Limit Total Qualified Rates
      | Rate_Qualified_ID |
      | 2                 |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 21         | 2                 |
      | 22         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has "61" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "61" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    Then Qualified FPLOS Decision table has "31" records for Accom_Type_ID "2" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "31" records for Accom_Type_ID "2" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    Then Qualified FPLOS Decision table has "0" records for Accom_Type_ID "3" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "3" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"

  Scenario: Validate Qualified FPLOS at limit total level gets generated for all dates for qualified rate for seasons having start date is equal to or after caught up date and end dates is less than optimization end dates when rate plan configuration date range is less than agile product configuration date range for FPLOS generation with toggle limit total as true and total level as false and considerRestrictionSeasonDatesForAgileFPLOS is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set enableFplosService parameter to "true"
    And I set isDiffEnabledFPLOS parameter to "true"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "false"
    And I set GenerateLimitTotalSRPRates parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Consider Restriction Seasons Dates For Agile Qualified FPLOS parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-11-30  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 5             | 2017-12-01    | 2017-12-31  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 3                         | 2                 | 5             | 2018-09-01    | 2018-09-30  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-11-30  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 5                         | 3                 | 6             | 2018-02-01    | 2018-02-28  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 6                         | 3                 | 6             | 2018-09-01    | 2018-09-30  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have configured following Limit Total Qualified Rates
      | Rate_Qualified_ID |
      | 3                 |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 21         | 2                 |
      | 17        | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has "113" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "113" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "80" records for Accom_Type_ID "5" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "80" records for Accom_Type_ID "5" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"

  Scenario: Validate Qualified FPLOS gets generated with start date as optimization start date and end date as optimization window minus 7 days and only for room types fow which rates were given in qualified rate having start date as past and end date is after optimization end date with toggle limit total as true and total level as false and toggle considerRestrictionSeasonDatesForAgileFPLOS is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set enableFplosService parameter to "true"
    And I set isDiffEnabledFPLOS parameter to "true"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "false"
    And I set GenerateLimitTotalSRPRates parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Consider Restriction Seasons Dates For Agile Qualified FPLOS parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-09-01    | 2018-12-31  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-09-01    | 2018-12-31  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 6             | 2017-10-11    | 2017-10-18  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 4                         | 3                 | 7             | 2017-09-01    | 2018-12-31  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 5                         | 3                 | 8             | 2017-09-01    | 2018-12-31  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
    And I have configured following Limit Total Qualified Rates
      | Rate_Qualified_ID |
      | 3                 |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 21         | 2                 |
      | 21         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has "358" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "358" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "358" records for Accom_Type_ID "6" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "358" records for Accom_Type_ID "6" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
#fplos should not be generated for Accom Type 7 and 8 as their details were not given in qualified rate screen for rate R1 so will be ignored in agle fplos too
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "7" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "7" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "8" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "8" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "358" records for Accom_Type_ID "5" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "358" records for Accom_Type_ID "5" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "6" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "6" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "7" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "7" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "8" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "8" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"

  Scenario: Validate Qualified FPLOS gets generated with start date as optimization start date and end date as optimization window minus 7 days and only for room types fow which rates were given in qualified rate having start date as past and end date is after optimization end date with toggle limit total as true and total level as true and toggle considerRestrictionSeasonDatesForAgileFPLOS is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set enableFplosService parameter to "true"
    And I set isDiffEnabledFPLOS parameter to "true"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set GenerateLimitTotalSRPRates parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Consider Restriction Seasons Dates For Agile Qualified FPLOS parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-09-01    | 2018-12-31  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-09-01    | 2018-12-31  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 6             | 2017-10-11    | 2017-10-18  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 4                         | 3                 | 7             | 2017-09-01    | 2018-12-31  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 5                         | 3                 | 8             | 2017-09-01    | 2018-12-31  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
    And I have configured following Limit Total Qualified Rates
      | Rate_Qualified_ID |
      | 3                 |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 21         | 2                 |
      | 21         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has "358" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "358" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "6" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "6" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
#fplos should not be generated for Accom Type 7 and 8 as their details were not given in qualified rate screen for rate R1 so will be ignored in agle fplos too
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "7" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "7" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "8" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "8" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "358" records for Accom_Type_ID "5" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "358" records for Accom_Type_ID "5" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "6" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "6" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "7" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "7" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "8" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "8" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"

  Scenario: Validate Qualified FPLOS gets generated for all dates for qualified rate having weekly seasons having start date is equal to or after caught up date and end dates is less than optimization end dates when rate plan configuration date range is less than agile product configuration date range for FPLOS generation with toggle limit total as true and total level as false and considerRestrictionSeasonDatesForAgileFPLOS is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set enableFplosService parameter to "true"
    And I set isDiffEnabledFPLOS parameter to "true"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "false"
    And I set GenerateLimitTotalSRPRates parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Consider Restriction Seasons Dates For Agile Qualified FPLOS parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 6             | 2017-11-01    | 2017-11-07  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-11-08    | 2017-11-15  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 3                         | 2                 | 6             | 2017-11-16    | 2017-11-23  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 2                 | 6             | 2017-11-24    | 2017-11-30  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 5                         | 2                 | 6             | 2017-12-01    | 2017-12-07  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 7                         | 2                 | 6             | 2017-12-08    | 2017-12-15  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 8                         | 2                 | 6             | 2017-12-16    | 2017-12-23  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 9                         | 2                 | 6             | 2017-12-24    | 2017-12-31  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have configured following Limit Total Qualified Rates
      | Rate_Qualified_ID |
      | 2                 |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 21         | 2                 |
      | 21         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has "61" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "61" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"

  Scenario: Validate Qualified FPLOS gets generated for all dates for both qualified rate  mapeed to different product having weekly seasons having start date is equal to or after caught up date and end dates is less than optimization end dates when rate plan configuration date range is less than agile product configuration date range for FPLOS generation with toggle limit total as true and total level as false and considerRestrictionSeasonDatesForAgileFPLOS is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set enableFplosService parameter to "true"
    And I set isDiffEnabledFPLOS parameter to "true"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "false"
    And I set GenerateLimitTotalSRPRates parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Consider Restriction Seasons Dates For Agile Qualified FPLOS parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 6             | 2017-11-01    | 2017-11-07  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-11-08    | 2017-11-15  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 3                         | 2                 | 6             | 2017-11-16    | 2017-11-23  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 2                 | 6             | 2017-11-24    | 2017-11-30  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 5                         | 2                 | 6             | 2017-12-01    | 2017-12-07  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 7                         | 2                 | 6             | 2017-12-08    | 2017-12-15  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 8                         | 2                 | 6             | 2017-12-16    | 2017-12-23  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 9                         | 2                 | 6             | 2017-12-24    | 2017-12-31  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 10                        | 3                 | 3             | 2017-11-01    | 2017-11-07  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 11                        | 3                 | 3             | 2017-11-08    | 2017-11-15  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 12                        | 3                 | 3             | 2017-11-16    | 2017-11-23  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 13                        | 3                 | 3             | 2017-11-24    | 2017-11-30  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 14                        | 3                 | 2             | 2017-12-01    | 2017-12-07  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 15                        | 3                 | 2             | 2017-12-08    | 2017-12-15  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 16                        | 3                 | 2             | 2017-12-16    | 2017-12-23  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 17                        | 3                 | 2             | 2017-12-24    | 2017-12-31  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have configured following Limit Total Qualified Rates
      | Rate_Qualified_ID |
      | 2                 |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 21         | 2                 |
      | 22         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has "61" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "61" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "31" records for Accom_Type_ID "2" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "31" records for Accom_Type_ID "2" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "3" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "3" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"

  Scenario: Validate Qualified FPLOS at limit total level gets generated for all dates for qualified rate for seasons having start date is equal to or after caught up date and end dates is less than optimization end dates when rate plan configuration date range is less than agile product configuration date range for FPLOS generation with toggle limit total as true and total level as false and considerRestrictionSeasonDatesForAgileFPLOS is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set enableFplosService parameter to "true"
    And I set isDiffEnabledFPLOS parameter to "true"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "false"
    And I set GenerateLimitTotalSRPRates parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Consider Restriction Seasons Dates For Agile Qualified FPLOS parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-10-10    | 2017-11-30  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 5             | 2017-12-01    | 2017-12-31  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 3                         | 2                 | 5             | 2018-09-01    | 2018-09-30  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 4                         | 3                 | 6             | 2017-10-10    | 2017-11-30  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 5                         | 3                 | 6             | 2018-02-01    | 2018-02-28  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 6                         | 3                 | 6             | 2018-09-01    | 2018-09-30  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
    And I have configured following Limit Total Qualified Rates
      | Rate_Qualified_ID |
      | 3                 |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 21         | 2                 |
      | 17        | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has "113" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "113" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "80" records for Accom_Type_ID "5" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "80" records for Accom_Type_ID "5" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"

  Scenario: Validate Qualified FPLOS gets generated with start date as optimization start date and end date as optimization window minus 7 days and only for room types fow which rates were given in qualified rate having start date as past and end date is after optimization end date with toggle limit total as true and total level as false and toggle considerRestrictionSeasonDatesForAgileFPLOS is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set enableFplosService parameter to "true"
    And I set isDiffEnabledFPLOS parameter to "true"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "false"
    And I set GenerateLimitTotalSRPRates parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Consider Restriction Seasons Dates For Agile Qualified FPLOS parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-09-01    | 2018-12-31  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-09-01    | 2018-12-31  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 6             | 2017-10-11    | 2017-10-18  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 4                         | 3                 | 7             | 2017-09-01    | 2018-12-31  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 5                         | 3                 | 8             | 2017-09-01    | 2018-12-31  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
    And I have configured following Limit Total Qualified Rates
      | Rate_Qualified_ID |
      | 3                 |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 21         | 2                 |
      | 21         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has "358" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "358" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "358" records for Accom_Type_ID "6" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "358" records for Accom_Type_ID "6" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
#fplos should not be generated for Accom Type 7 and 8 as their details were not given in qualified rate screen for rate R1 so will be ignored in agle fplos too
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "7" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "7" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "8" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "8" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "358" records for Accom_Type_ID "5" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "358" records for Accom_Type_ID "5" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "6" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "6" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "7" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "7" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "8" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "8" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"

  Scenario: Validate Qualified FPLOS gets generated with start date as optimization start date and end date as optimization window minus 7 days and only for room types fow which rates were given in qualified rate having start date as past and end date is after optimization end date with toggle limit total as true and total level as true and toggle considerRestrictionSeasonDatesForAgileFPLOS is true
    Given I have set property stage to TwoWay
    And I set isContinuousPricingEnabled parameter to "true"
    And I set BarDecision parameter to "RateOfDay"
    And I set enableFplosService parameter to "true"
    And I set isDiffEnabledFPLOS parameter to "true"
    And I set isLRAEnabled parameter to "false"
    And I set isDerivedQualifiedRatePlanEnabled parameter to "true"
    And I set srpFPLOSatTotal parameter to "true"
    And I set GenerateLimitTotalSRPRates parameter to "true"
    And I set isAgileRateFPLOSGenerationEnabled parameter to "true"
    And I set Agile Rates Enabled parameter to "true"
    And I set Consider Restriction Seasons Dates For Agile Qualified FPLOS parameter to "true"
    And I create a restriction rate plan as below
      | Rate_Qualified_ID | File_Metadata_ID | Property_ID | Rate_Code_Name | Start_Date_DT | End_Date_DT | Yieldable | Status_ID | Rate_Qualified_Type_Id | Managed_In_G3 |
      | 2                 | 7088             | 11033       | RL1            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
      | 3                 | 7088             | 11033       | RL2            | 2017-09-01    | 2018-10-31  | 1         | 1         | 2                      | 1             |
    And I create rate plan details as below
      | Rate_Qualified_Details_ID | Rate_Qualified_ID | Accom_Type_ID | Start_Date_DT | End_Date_DT | Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday |
      | 1                         | 2                 | 5             | 2017-09-01    | 2018-12-31  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 2                         | 2                 | 6             | 2017-09-01    | 2018-12-31  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 3                         | 3                 | 6             | 2017-10-11    | 2017-10-18  | 90     | 40     | 40      | 40        | 40       | 30     | 28       |
      | 4                         | 3                 | 7             | 2017-09-01    | 2018-12-31  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
      | 5                         | 3                 | 8             | 2017-09-01    | 2018-12-31  | 100    | 50     | 50      | 50        | 50       | 40     | 38       |
    And I have configured following Limit Total Qualified Rates
      | Rate_Qualified_ID |
      | 3                 |
    And I have linked the rate plan with agile product as below
      | Product_ID | Rate_Qualified_ID |
      | 21         | 2                 |
      | 21         | 3                 |
    When I invoke Decision Qualified FPLOS generation Service
    Then Qualified FPLOS Decision table has "358" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "358" records for Accom_Type_ID "5" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "6" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "6" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
#fplos should not be generated for Accom Type 7 and 8 as their details were not given in qualified rate screen for rate R1 so will be ignored in agle fplos too
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "7" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "7" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "8" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "8" and rate qualified id = "2" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "358" records for Accom_Type_ID "5" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "358" records for Accom_Type_ID "5" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "6" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "6" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "7" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "7" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Qualified FPLOS Decision table has "0" records for Accom_Type_ID "8" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"
    And Pace Qualified FPLOS Decision table also has "0" records for Accom_Type_ID "8" and rate qualified id = "3" for Arrival_DT between "2017-10-10" and "2018-10-09"