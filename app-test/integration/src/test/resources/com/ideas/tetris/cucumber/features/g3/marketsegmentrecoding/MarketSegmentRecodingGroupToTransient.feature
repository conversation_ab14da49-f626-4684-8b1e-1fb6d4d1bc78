@MarketSegmentRecoding
Feature: Ms Recoding

  Scenario: Verify MarketSegment Recoding When Group Market Segment Is Converted Into Straight Transient With Change In Market Code and Attribution
    Given Market Segment Recoding configuration is set in Mkt_Seg_Recoding_Config as Below
      | Client_ID | Property_ID | Old_Sys_Date            | Recoding_State               | Created_by_User_ID | Created_DTTM            | Last_Updated_DTTM       | Last_Updated_by_User_ID | Is_Backup_Created |
      | 6         | 11033       | 2020-02-20 00:00:00.000 | MKT_SEG_RECODING_NOT_STARTED | 11403              | 2020-02-21 15:27:16.063 | 2020-02-21 16:17:53.263 | 1                       | 0                 |
    And Old Market Segments in Mkt_Seg table is as below
      | Mkt_Seg_ID | Property_ID | Mkt_Seg_Code | Mkt_Seg_Name | Mkt_Seg_Description | Status_ID | complimentary |
      | 37         | 11033       | GC-MEET      | GC-MEET      | GC-MEET             | 1         | 0             |
    And Old Market Segments related rules in Analytical_Market_Segment is as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code | Mapped_Market_Code | Attribute | Rate_Code_Type | Rank | complimentary | Preserved |
      | 79                    | GC-MEET     | NULL      | GC-MEET            | GROUP     | ALL            | 1    | 0             | 0         |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code | Number_Of_Records |
      | 37         | GC-MEET     | CF77      | 5                 |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 37         | 1                 |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 37         | 0                 |
    And User has provided MS Mapping Configurations in PMS_Migration_Mapping As Below
      | G3 RMS Property Code | Code Type      | Current Code | New Equivalent Code | Remove and permanently delete the history | Is Primary Code for One to Many Splits |
      | CPGP02               | MARKET_SEGMENT | GC-MEET      | GC-MEETNew          | 0                                         | NULL                                   |
    And System copies the missing mappings into PMS_Migration_Mapping
    And User has provided AMS Mapping Configurations in PMS_Migration_Mapping_amsRule As below
      | Market Code | Rate Code | Attribute                              | Forecast Type |
      | GC-MEETNew  | NULL      | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | 1             |
    And Assuming User has accepted Business Type Shift popup adding below entry in MS_Recoding_Business_Type_Shift
      | Market_Segment_Name | Previous_Business_Type | New_Business_Type |
      | GC-MEETNew          | Group                  | Transient         |
    And Property stage is ONE_WAY
    When MktSegRecoding job is triggered
    Then Property stage is set as DORMANT
    And Relevant tables are backed up
    And AnalyticalMarketSegment now has rules for new market segments in Analytical_Market_Segment as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code | Mapped_Market_Code | Attribute                              | Rate_Code_Type | Rank | complimentary | Preserved |
      | 79                    | GC-MEET     | NULL      | GC-MEETNew         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | ALL            | 1    | 0             | 1         |
      | 828                   | GC-MEETNew  | NULL      | GC-MEETNew         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | ALL            | 1    | 0             | 0         |
    And New market segments are populated in MarketSegment table Mkt_Seg as below
      | Mkt_Seg_ID | Property_ID | Mkt_Seg_Code | Mkt_Seg_Name | Mkt_Seg_Description | Status_ID | complimentary |
      | 92         | 11033       | GC-MEETNew   | GC-MEETNew   | GC-MEETNew          | 1         | 0             |
    And Following market segments are deleted from Mkt_Seg table
      | Mkt_Seg_ID | Mkt_Seg_Code |
      | 37         | GC-MEET      |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code | Number_Of_Records |
      | 92         | GC-MEETNew  | CF77      | 5                 |
      | 37         | GC-MEET     | CF77      | 0                 |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 92         | 1                 |
      | 37         | 0                 |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 92         | 1                 |
      | 37         | 0                 |
#    And ReservationNightChange records are updated
#    And Old AnalyticalMarketSegment rules are preserved and mappedMarketCodes are updated
#    And InformationManager alerts,notifications are cleaned up
#    And Relevant scheduled reports are cleaned up
#    And All overrides are cleaned up
#    And Redundant market segments are cleaned up
#    And Activity data for new market segment is populated in MktAccomActivity
#    And Pacebackfill executed successfully for new market segments
    And Postbackfill data validation step failed and report is generated
    And I continue the Postbackfill data validation step
    And CreateCommitForecastGroup alert is generated
    And I trigger Create/Commit Forecast Group job
    And I resolve the CreateCommitForecastGroup alert


  Scenario: Verify MarketSegment Recoding When Group Market Segment Is Converted Into Straight Transient Without renaming the Market Code
    Given Market Segment Recoding configuration is set in Mkt_Seg_Recoding_Config as Below
      | Client_ID | Property_ID | Old_Sys_Date            | Recoding_State               | Created_by_User_ID | Created_DTTM            | Last_Updated_DTTM       | Last_Updated_by_User_ID | Is_Backup_Created |
      | 6         | 11033       | 2020-02-20 00:00:00.000 | MKT_SEG_RECODING_NOT_STARTED | 11403              | 2020-02-21 15:27:16.063 | 2020-02-21 16:17:53.263 | 1                       | 0                 |
    And Old Market Segments in Mkt_Seg table is as below
      | Mkt_Seg_ID | Property_ID | Mkt_Seg_Code | Mkt_Seg_Name | Mkt_Seg_Description | Status_ID | complimentary |
      | 37         | 11033       | GC-MEET      | GC-MEET      | GC-MEET             | 1         | 0             |
    And Old Market Segments related rules in Analytical_Market_Segment is as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code | Mapped_Market_Code | Attribute | Rate_Code_Type | Rank | complimentary | Preserved |
      | 79                    | GC-MEET     | NULL      | GC-MEET            | GROUP     | ALL            | 1    | 0             | 0         |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code | Number_Of_Records |
      | 37         | GC-MEET     | CF77      | 5                 |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 37         | 1                 |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 37         | 0                 |
    And User has provided MS Mapping Configurations in PMS_Migration_Mapping As Below
      | G3 RMS Property Code | Code Type      | Current Code | New Equivalent Code | Remove and permanently delete the history | Is Primary Code for One to Many Splits |
      | CPGP02               | MARKET_SEGMENT | GC-MEET      | GC-MEET             | 0                                         | NULL                                   |
    And System copies the missing mappings into PMS_Migration_Mapping
    And User has provided AMS Mapping Configurations in PMS_Migration_Mapping_amsRule As below
      | Market Code | Rate Code | Attribute                              | Forecast Type |
      | GC-MEET     | NULL      | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | 1             |
    And Assuming User has accepted Business Type Shift popup adding below entry in MS_Recoding_Business_Type_Shift
      | Market_Segment_Name | Previous_Business_Type | New_Business_Type |
      | GC-MEET             | Group                  | Transient         |
    And Property stage is ONE_WAY
    When MktSegRecoding job is triggered
    Then Property stage is set as DORMANT
    And Relevant tables are backed up
    And AnalyticalMarketSegment now has rules for new market segments in Analytical_Market_Segment as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code | Mapped_Market_Code | Attribute                              | Rate_Code_Type | Rank | complimentary | Preserved |
      | 79                    | GC-MEET     | NULL      | GC-MEET            | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | ALL            | 1    | 0             | 0         |
    And New market segments are populated in MarketSegment table Mkt_Seg as below
      | Mkt_Seg_ID | Property_ID | Mkt_Seg_Code | Mkt_Seg_Name | Mkt_Seg_Description | Status_ID | complimentary |
      | 37         | 11033       | GC-MEET      | GC-MEET      | GC-MEET             | 1         | 0             |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code | Number_Of_Records |
      | 37         | GC-MEET     | CF77      | 5                 |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 37         | 1                 |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 37         | 1                 |
#    And ReservationNightChange records are updated
#    And Old AnalyticalMarketSegment rules are preserved and mappedMarketCodes are updated
#    And InformationManager alerts,notifications are cleaned up
#    And Relevant scheduled reports are cleaned up
#    And All overrides are cleaned up
#    And Redundant market segments are cleaned up
#    And Activity data for new market segment is populated in MktAccomActivity
#    And Pacebackfill executed successfully for new market segments
    And Postbackfill data validation step failed and report is generated
    And I continue the Postbackfill data validation step
    And CreateCommitForecastGroup alert is generated
    And I trigger Create/Commit Forecast Group job
    And I resolve the CreateCommitForecastGroup alert

  Scenario: Verify MarketSegment Recoding When Group Market Segment Is Converted Into Non Straight Transient Without Change In Market Code
    Given Market Segment Recoding configuration is set in Mkt_Seg_Recoding_Config as Below
      | Client_ID | Property_ID | Old_Sys_Date            | Recoding_State               | Created_by_User_ID | Created_DTTM            | Last_Updated_DTTM       | Last_Updated_by_User_ID | Is_Backup_Created |
      | 6         | 11033       | 2020-02-20 00:00:00.000 | MKT_SEG_RECODING_NOT_STARTED | 11403              | 2020-02-21 15:27:16.063 | 2020-02-21 16:17:53.263 | 1                       | 0                 |
    And Old Market Segments in Mkt_Seg table is as below
      | Mkt_Seg_ID | Property_ID | Mkt_Seg_Code | Mkt_Seg_Name | Mkt_Seg_Description | Status_ID | complimentary |
      | 37         | 11033       | GC-MEET      | GC-MEET      | GC-MEET             | 1         | 0             |
    And Old Market Segments related rules in Analytical_Market_Segment is as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code | Mapped_Market_Code | Attribute | Rate_Code_Type | Rank | complimentary | Preserved |
      | 79                    | GC-MEET     | NULL      | GC-MEET            | GROUP     | ALL            | 1    | 0             | 0         |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code | Number_Of_Records |
      | 37         | GC-MEET     | CF77      | 5                 |
    And User has provided MS Mapping Configurations in PMS_Migration_Mapping As Below
      | G3 RMS Property Code | Code Type      | Current Code | New Equivalent Code | Remove and permanently delete the history | Is Primary Code for One to Many Splits |
      | CPGP02               | MARKET_SEGMENT | GC-MEET      | GC-MEET             | 0                                         | NULL                                   |
    And System copies the missing mappings into PMS_Migration_Mapping
    And User has provided AMS Mapping Configurations in PMS_Migration_Mapping_amsRule As below
      | Market Code | Rate Code | Attribute                               | Forecast Type |
      | GC-MEET     | CF77      | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | 1             |
      | GC-MEET     | DEFAULT   | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | 1             |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 37         | 1                 |
    And Create pace records in Pace_Group_Master for market segment given below
      | Mkt_Seg_ID |
      | 37         |
    And Number of records in Pace_Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 37         | 1                 |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 37         | 0                 |
    And Assuming User has accepted Business Type Shift popup adding below entry in MS_Recoding_Business_Type_Shift
      | Market_Segment_Name | Previous_Business_Type | New_Business_Type |
      | GC-MEET             | Group                  | Transient         |
    And Property stage is ONE_WAY
    When MktSegRecoding job is triggered
    Then Property stage is set as DORMANT
    And Relevant tables are backed up
    And AnalyticalMarketSegment now has rules for new market segments in Analytical_Market_Segment as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code | Mapped_Market_Code | Attribute                               | Rate_Code_Type | Rank | complimentary | Preserved |
      | 828                   | GC-MEET     | CF77      | GC-MEET_QY         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 0         |
      | 829                   | GC-MEET     | NULL      | GC-MEET_DEF        | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | DEFAULT        | 999  | 0             | 0         |
    And New market segments are populated in MarketSegment table Mkt_Seg as below
      | Mkt_Seg_ID | Property_ID | Mkt_Seg_Code | Mkt_Seg_Name | Mkt_Seg_Description | Status_ID | complimentary |
      | 92         | 11033       | GC-MEET_QY   | GC-MEET_QY   | GC-MEET_QY          | 1         | 0             |
      | 93         | 11033       | GC-MEET_DEF  | GC-MEET_DEF  | GC-MEET_DEF         | 1         | 0             |
    And Following market segments are deleted from Mkt_Seg table
      | Mkt_Seg_ID | Mkt_Seg_Code |
      | 37         | GC-MEET      |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code | Number_Of_Records |
      | 9          | GC-MEET     | CF77      | 0                 |
      | 92         | GC-MEET     | CF77      | 5                 |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 93         | 1                 |
      | 37         | 0                 |
    And Number of records in Pace_Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 93         | 1                 |
      | 37         | 0                 |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 93         | 1                 |
      | 37         | 0                 |
#    And ReservationNight records are updated
#    And ReservationNightChange records are updated
#    And Old AnalyticalMarketSegment rules are preserved and mappedMarketCodes are updated
#    And InformationManager alerts,notifications are cleaned up
#    And Relevant scheduled reports are cleaned up
#    And All overrides are cleaned up
#    And Redundant market segments are cleaned up
#    And Activity data for new market segment is populated in MktAccomActivity
#    And Pacebackfill executed successfully for new market segments
    And Postbackfill data validation step failed and report is generated
    And I continue the Postbackfill data validation step
    And CreateCommitForecastGroup alert is generated
    And I trigger Create/Commit Forecast Group job
    And I resolve the CreateCommitForecastGroup alert


  Scenario: Verify MarketSegment Recoding When Group Market Segment Is Converted Into Non Straight Transient With Change In Market Code
    Given Market Segment Recoding configuration is set in Mkt_Seg_Recoding_Config as Below
      | Client_ID | Property_ID | Old_Sys_Date            | Recoding_State               | Created_by_User_ID | Created_DTTM            | Last_Updated_DTTM       | Last_Updated_by_User_ID | Is_Backup_Created |
      | 6         | 11033       | 2020-02-20 00:00:00.000 | MKT_SEG_RECODING_NOT_STARTED | 11403              | 2020-02-21 15:27:16.063 | 2020-02-21 16:17:53.263 | 1                       | 0                 |
    And Old Market Segments in Mkt_Seg table is as below
      | Mkt_Seg_ID | Property_ID | Mkt_Seg_Code | Mkt_Seg_Name | Mkt_Seg_Description | Status_ID | complimentary |
      | 37         | 11033       | GC-MEET      | GC-MEET      | GC-MEET             | 1         | 0             |
    And Old Market Segments related rules in Analytical_Market_Segment is as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code | Mapped_Market_Code | Attribute | Rate_Code_Type | Rank | complimentary | Preserved |
      | 79                    | GC-MEET     | NULL      | GC-MEET            | GROUP     | ALL            | 1    | 0             | 0         |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code | Number_Of_Records |
      | 37         | GC-MEET     | CF77      | 5                 |
    And User has provided MS Mapping Configurations in PMS_Migration_Mapping As Below
      | G3 RMS Property Code | Code Type      | Current Code | New Equivalent Code | Remove and permanently delete the history | Is Primary Code for One to Many Splits |
      | CPGP02               | MARKET_SEGMENT | GC-MEET      | GC-MEETNew          | 0                                         | NULL                                   |
    And System copies the missing mappings into PMS_Migration_Mapping
    And User has provided AMS Mapping Configurations in PMS_Migration_Mapping_amsRule As below
      | Market Code | Rate Code | Attribute                               | Forecast Type |
      | GC-MEETNew  | CF77      | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | 1             |
      | GC-MEETNew  | DEFAULT   | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | 1             |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 37         | 1                 |
    And Create pace records in Pace_Group_Master for market segment given below
      | Mkt_Seg_ID |
      | 37         |
    And Number of records in Pace_Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 37         | 1                 |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 37         | 0                 |
    And Assuming User has accepted Business Type Shift popup adding below entry in MS_Recoding_Business_Type_Shift
      | Market_Segment_Name | Previous_Business_Type | New_Business_Type |
      | GC-MEETNew          | Group                  | Transient         |
    And Property stage is ONE_WAY
    When MktSegRecoding job is triggered
    Then Property stage is set as DORMANT
    And Relevant tables are backed up
    And AnalyticalMarketSegment now has rules for new market segments in Analytical_Market_Segment as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code | Mapped_Market_Code | Attribute                               | Rate_Code_Type | Rank | complimentary | Preserved |
      | 828                   | GC-MEETNew  | CF77      | GC-MEETNew_QY      | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 0         |
      | 829                   | GC-MEETNew  | NULL      | GC-MEETNew_DEF     | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | DEFAULT        | 999  | 0             | 0         |
      | 830                   | GC-MEET     | CF77      | GC-MEETNew_QY      | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 1         |
      | 831                   | GC-MEET     | NULL      | GC-MEETNew_DEF     | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | DEFAULT        | 999  | 0             | 1         |
    And New market segments are populated in MarketSegment table Mkt_Seg as below
      | Mkt_Seg_ID | Property_ID | Mkt_Seg_Code   | Mkt_Seg_Name   | Mkt_Seg_Description | Status_ID | complimentary |
      | 92         | 11033       | GC-MEETNew_QY  | GC-MEETNew_QY  | GC-MEETNew_QY       | 1         | 0             |
      | 93         | 11033       | GC-MEETNew_DEF | GC-MEETNew_DEF | GC-MEETNew_DEF      | 1         | 0             |
    And Following market segments are deleted from Mkt_Seg table
      | Mkt_Seg_ID | Mkt_Seg_Code |
      | 37         | GC-MEET      |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code | Number_Of_Records |
      | 9          | GC-MEET     | CF77      | 0                 |
      | 92         | GC-MEETNew  | CF77      | 5                 |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 93         | 1                 |
      | 37         | 0                 |
    And Number of records in Pace_Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 93         | 1                 |
      | 37         | 0                 |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 93         | 1                 |
      | 37         | 0                 |
#    And ReservationNight records are updated
#    And ReservationNightChange records are updated
#    And Old AnalyticalMarketSegment rules are preserved and mappedMarketCodes are updated
#    And InformationManager alerts,notifications are cleaned up
#    And Relevant scheduled reports are cleaned up
#    And All overrides are cleaned up
#    And Redundant market segments are cleaned up
#    And Activity data for new market segment is populated in MktAccomActivity
#    And Pacebackfill executed successfully for new market segments
    And Postbackfill data validation step failed and report is generated
    And I continue the Postbackfill data validation step
    And CreateCommitForecastGroup alert is generated
    And I trigger Create/Commit Forecast Group job
    And I resolve the CreateCommitForecastGroup alert

  Scenario: Verify MarketSegment Recoding When Group Market Segment Is Split and Converted Into Non Straight Transient With Change In Market Code
    Given Market Segment Recoding configuration is set in Mkt_Seg_Recoding_Config as Below
      | Client_ID | Property_ID | Old_Sys_Date            | Recoding_State               | Created_by_User_ID | Created_DTTM            | Last_Updated_DTTM       | Last_Updated_by_User_ID | Is_Backup_Created |
      | 6         | 11033       | 2020-02-20 00:00:00.000 | MKT_SEG_RECODING_NOT_STARTED | 11403              | 2020-02-21 15:27:16.063 | 2020-02-21 16:17:53.263 | 1                       | 0                 |
    And Old Market Segments in Mkt_Seg table is as below
      | Mkt_Seg_ID | Property_ID | Mkt_Seg_Code | Mkt_Seg_Name | Mkt_Seg_Description | Status_ID | complimentary |
      | 7          | 11033       | CORP         | CORP         | CORP                | 1         | 0             |
    And Old Market Segments related rules in Analytical_Market_Segment is as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code | Mapped_Market_Code | Attribute | Rate_Code_Type | Rank | complimentary | Preserved |
      | 315                   | CORP        | NULL      | CORP               | GROUP     | ALL            | 1    | 0             | 0         |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code | Number_Of_Records |
      | 7          | CORP        | CF74      | 103               |
      | 7          | CORP        | CF65      | 60                |
      | 7          | CORP        | C79       | 92                |
    And User has provided MS Mapping Configurations in PMS_Migration_Mapping As Below
      | G3 RMS Property Code | Code Type      | Current Code | New Equivalent Code | Remove and permanently delete the history | Is Primary Code for One to Many Splits |
      | CPGP02               | MARKET_SEGMENT | CORP         | CORPNew1            | 0                                         | 1                                      |
      | CPGP02               | MARKET_SEGMENT | CORP         | CORPNew2            | 0                                         | 0                                      |
    And System copies the missing mappings into PMS_Migration_Mapping
    And User has provided AMS Mapping Configurations in PMS_Migration_Mapping_amsRule As below
      | Market Code | Rate Code | Attribute                               | Forecast Type |
      | CORPNew1    | CF74      | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | 1             |
      | CORPNew1    | CF65      | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | 1             |
      | CORPNew1    | DEFAULT   | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | 1             |
      | CORPNew2    | C79       | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | 1             |
      | CORPNew2    | DEFAULT   | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | 1             |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 12                |
    And Create pace records in Pace_Group_Master for market segment given below
      | Mkt_Seg_ID |
      | 7          |
    And Number of records in Pace_Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 12                |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 0                 |
    And Assuming User has accepted Business Type Shift popup adding below entry in MS_Recoding_Business_Type_Shift
      | Market_Segment_Name | Previous_Business_Type | New_Business_Type |
      | CORPNEW1            | Group                  | Transient         |
      | CORPNEW2            | Group                  | Transient         |
    And Property stage is ONE_WAY
    When MktSegRecoding job is triggered
    Then Property stage is set as DORMANT
    And Relevant tables are backed up
    And AnalyticalMarketSegment now has rules for new market segments in Analytical_Market_Segment as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code | Mapped_Market_Code | Attribute                               | Rate_Code_Type | Rank | complimentary | Preserved |
      | 828                   | CORPNew1    | CF74      | CORPNew1_QY        | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 0         |
      | 829                   | CORPNew1    | CF65      | CORPNew1_QY        | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 0         |
      | 830                   | CORPNew1    | NULL      | CORPNew1_DEF       | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | DEFAULT        | 999  | 0             | 0         |
      | 831                   | CORPNew2    | C79       | CORPNew2_QY        | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 0         |
      | 832                   | CORPNew2    | NULL      | CORPNew2_DEF       | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | DEFAULT        | 999  | 0             | 0         |
      | 833                   | CORP        | C79       | CORPNew2_QY        | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 1         |
      | 834                   | CORP        | CF65      | CORPNew1_QY        | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 1         |
      | 835                   | CORP        | CF74      | CORPNew1_QY        | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 1         |
      | 836                   | CORP        | NULL      | CORPNew1_DEF       | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | DEFAULT        | 999  | 0             | 1         |
    And New market segments are populated in MarketSegment table Mkt_Seg as below
      | Mkt_Seg_ID | Property_ID | Mkt_Seg_Code | Mkt_Seg_Name | Mkt_Seg_Description | Status_ID | complimentary |
      | 92         | 11033       | CORPNew1_QY  | CORPNew1_QY  | CORPNew1_QY         | 1         | 0             |
      | 93         | 11033       | CORPNew1_DEF | CORPNew1_DEF | CORPNew1_DEF        | 1         | 0             |
      | 94         | 11033       | CORPNew2_QY  | CORPNew2_QY  | CORPNew2_QY         | 1         | 0             |
      | 95         | 11033       | CORPNew2_DEF | CORPNew2_DEF | CORPNew2_DEF        | 1         | 0             |
    And Following market segments are deleted from Mkt_Seg table
      | Mkt_Seg_ID | Mkt_Seg_Code |
      | 7          | CORP         |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code | Number_Of_Records |
      | 37         | CORP        | CF74      | 0                 |
      | 37         | CORP        | CF65      | 0                 |
      | 37         | CORP        | C79       | 0                 |
      | 92         | CORPNew1    | CF74      | 103               |
      | 92         | CORPNew1    | CF65      | 60                |
      | 94         | CORPNew2    | C79       | 92                |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 93         | 12                |
      | 7          | 0                 |
    And Number of records in Pace_Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 93         | 12                |
      | 7          | 0                 |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 0                 |
      | 93         | 1                 |
#    And ReservationNight records are updated
#    And ReservationNightChange records are updated
#    And Old AnalyticalMarketSegment rules are preserved and mappedMarketCodes are updated
#    And InformationManager alerts,notifications are cleaned up
#    And Relevant scheduled reports are cleaned up
#    And All overrides are cleaned up
#    And Redundant market segments are cleaned up
#    And Activity data for new market segment is populated in MktAccomActivity
#    And Pacebackfill executed successfully for new market segments
    And Postbackfill data validation step failed and report is generated
    And I continue the Postbackfill data validation step
    And CreateCommitForecastGroup alert is generated
    And I trigger Create/Commit Forecast Group job
    And I resolve the CreateCommitForecastGroup alert

  Scenario: Verify MarketSegment Recoding When Group Market Segments Are Merged and Converted Into New Non Straight Transient With Change In Market Code
    Given Market Segment Recoding configuration is set in Mkt_Seg_Recoding_Config as Below
      | Client_ID | Property_ID | Old_Sys_Date            | Recoding_State               | Created_by_User_ID | Created_DTTM            | Last_Updated_DTTM       | Last_Updated_by_User_ID | Is_Backup_Created |
      | 6         | 11033       | 2020-02-20 00:00:00.000 | MKT_SEG_RECODING_NOT_STARTED | 11403              | 2020-02-21 15:27:16.063 | 2020-02-21 16:17:53.263 | 1                       | 0                 |
    And Old Market Segments in Mkt_Seg table is as below
      | Mkt_Seg_ID | Property_ID | Mkt_Seg_Code | Mkt_Seg_Name | Mkt_Seg_Description | Status_ID | complimentary |
      | 7          | 11033       | CORP         | CORP         | CORP                | 1         | 0             |
      | 37         | 11033       | GC-MEET      | GC-MEET      | GC-MEET             | 1         | 0             |
    And Old Market Segments related rules in Analytical_Market_Segment is as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code | Mapped_Market_Code | Attribute | Rate_Code_Type | Rank | complimentary | Preserved |
      | 315                   | CORP        | NULL      | CORP               | GROUP     | ALL            | 1    | 0             | 0         |
      | 79                    | GC-MEET     | NULL      | GC-MEET            | GROUP     | ALL            | 1    | 0             | 0         |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code | Number_Of_Records |
      | 7          | CORP        | CF74      | 103               |
      | 7          | CORP        | CF65      | 60                |
      | 7          | CORP        | C79       | 92                |
      | 37         | GC-MEET     | CF77      | 5                 |
    And User has provided MS Mapping Configurations in PMS_Migration_Mapping As Below
      | G3 RMS Property Code | Code Type      | Current Code | New Equivalent Code | Remove and permanently delete the history | Is Primary Code for One to Many Splits |
      | CPGP02               | MARKET_SEGMENT | GC-MEET      | GC-MEETCORP         | 0                                         | NULL                                   |
      | CPGP02               | MARKET_SEGMENT | CORP         | GC-MEETCORP         | 0                                         | NULL                                   |
    And System copies the missing mappings into PMS_Migration_Mapping
    And User has provided AMS Mapping Configurations in PMS_Migration_Mapping_amsRule As below
      | Market Code | Rate Code | Attribute                               | Forecast Type |
      | GC-MEETCORP | CF74      | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | 1             |
      | GC-MEETCORP | CF65      | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | 1             |
      | GC-MEETCORP | C79       | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | 1             |
      | GC-MEETCORP | CF77      | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | 1             |
      | GC-MEETCORP | DEFAULT   | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | 1             |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 12                |
      | 37         | 1                 |
    And Create pace records in Pace_Group_Master for market segment given below
      | Mkt_Seg_ID |
      | 7          |
      | 37         |
    And Number of records in Pace_Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 12                |
      | 37         | 1                 |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 0                 |
      | 37         | 0                 |
    And Assuming User has accepted Business Type Shift popup adding below entry in MS_Recoding_Business_Type_Shift
      | Market_Segment_Name | Previous_Business_Type | New_Business_Type |
      | GC-MEETCORP         | Group                  | Transient         |
    And Property stage is ONE_WAY
    When MktSegRecoding job is triggered
    Then Property stage is set as DORMANT
    And Relevant tables are backed up
    And AnalyticalMarketSegment now has rules for new market segments in Analytical_Market_Segment as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code | Mapped_Market_Code | Attribute                               | Rate_Code_Type | Rank | complimentary | Preserved |
      | 828                   | GC-MEETCORP | CF74      | GC-MEETCORP_QY     | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 0         |
      | 829                   | GC-MEETCORP | CF65      | GC-MEETCORP_QY     | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 0         |
      | 830                   | GC-MEETCORP | C79       | GC-MEETCORP_QY     | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 0         |
      | 831                   | GC-MEETCORP | CF77      | GC-MEETCORP_QY     | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 0         |
      | 832                   | GC-MEETCORP | NULL      | GC-MEETCORP_DEF    | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | DEFAULT        | 999  | 0             | 0         |
      | 833                   | CORP        | C79       | GC-MEETCORP_QY     | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 1         |
      | 834                   | GC-MEET     | CF77      | GC-MEETCORP_QY     | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 1         |
      | 835                   | CORP        | CF65      | GC-MEETCORP_QY     | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 1         |
      | 836                   | CORP        | CF74      | GC-MEETCORP_QY     | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 1         |
      | 837                   | GC-MEET     | NULL      | GC-MEETCORP_DEF    | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | DEFAULT        | 999  | 0             | 1         |
      | 838                   | CORP        | NULL      | GC-MEETCORP_DEF    | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | DEFAULT        | 999  | 0             | 1         |
    And New market segments are populated in MarketSegment table Mkt_Seg as below
      | Mkt_Seg_ID | Property_ID | Mkt_Seg_Code    | Mkt_Seg_Name    | Mkt_Seg_Description | Status_ID | complimentary |
      | 92         | 11033       | GC-MEETCORP_QY  | GC-MEETCORP_QY  | GC-MEETCORP_QY      | 1         | 0             |
      | 93         | 11033       | GC-MEETCORP_DEF | GC-MEETCORP_DEF | GC-MEETCORP_DEF     | 1         | 0             |
    And Following market segments are deleted from Mkt_Seg table
      | Mkt_Seg_ID | Mkt_Seg_Code |
      | 7          | CORP         |
      | 37         | GC-MEET      |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code | Number_Of_Records |
      | 7          | CORP        | CF74      | 0                 |
      | 7          | CORP        | CF65      | 0                 |
      | 7          | CORP        | C79       | 0                 |
      | 37         | GC-MEET     | CF77      | 0                 |
      | 92         | GC-MEETCORP | CF74      | 103               |
      | 92         | GC-MEETCORP | CF65      | 60                |
      | 92         | GC-MEETCORP | C79       | 92                |
      | 92         | GC-MEETCORP | CF77      | 5                 |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 0                 |
      | 37         | 0                 |
      | 93         | 13                |
    And Number of records in Pace_Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 0                 |
      | 37         | 0                 |
      | 93         | 13                |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 0                 |
      | 37         | 0                 |
      | 93         | 1                 |
#    And ReservationNight records are updated
#    And ReservationNightChange records are updated
#    And Old AnalyticalMarketSegment rules are preserved and mappedMarketCodes are updated
#    And InformationManager alerts,notifications are cleaned up
#    And Relevant scheduled reports are cleaned up
#    And All overrides are cleaned up
#    And Redundant market segments are cleaned up
#    And Activity data for new market segment is populated in MktAccomActivity
#    And Pacebackfill executed successfully for new market segments
    And Postbackfill data validation step failed and report is generated
    And I continue the Postbackfill data validation step
    And CreateCommitForecastGroup alert is generated
    And I trigger Create/Commit Forecast Group job
    And I resolve the CreateCommitForecastGroup alert

  Scenario: Verify MarketSegment Recoding When Group Market Segments Are Merged and Converted Into New Straight Transient With Change In Market Code
    Given Market Segment Recoding configuration is set in Mkt_Seg_Recoding_Config as Below
      | Client_ID | Property_ID | Old_Sys_Date            | Recoding_State               | Created_by_User_ID | Created_DTTM            | Last_Updated_DTTM       | Last_Updated_by_User_ID | Is_Backup_Created |
      | 6         | 11033       | 2020-02-20 00:00:00.000 | MKT_SEG_RECODING_NOT_STARTED | 11403              | 2020-02-21 15:27:16.063 | 2020-02-21 16:17:53.263 | 1                       | 0                 |
    And Old Market Segments in Mkt_Seg table is as below
      | Mkt_Seg_ID | Property_ID | Mkt_Seg_Code | Mkt_Seg_Name | Mkt_Seg_Description | Status_ID | complimentary |
      | 7          | 11033       | CORP         | CORP         | CORP                | 1         | 0             |
      | 37         | 11033       | GC-MEET      | GC-MEET      | GC-MEET             | 1         | 0             |
    And Old Market Segments related rules in Analytical_Market_Segment is as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code | Mapped_Market_Code | Attribute | Rate_Code_Type | Rank | complimentary | Preserved |
      | 315                   | CORP        | NULL      | CORP               | GROUP     | ALL            | 1    | 0             | 0         |
      | 79                    | GC-MEET     | NULL      | GC-MEET            | GROUP     | ALL            | 1    | 0             | 0         |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code | Number_Of_Records |
      | 7          | CORP        | CF74      | 103               |
      | 7          | CORP        | CF65      | 60                |
      | 7          | CORP        | C79       | 92                |
      | 37         | GC-MEET     | CF77      | 5                 |
    And User has provided MS Mapping Configurations in PMS_Migration_Mapping As Below
      | G3 RMS Property Code | Code Type      | Current Code | New Equivalent Code | Remove and permanently delete the history | Is Primary Code for One to Many Splits |
      | CPGP02               | MARKET_SEGMENT | GC-MEET      | GC-MEETCORP         | 0                                         | NULL                                   |
      | CPGP02               | MARKET_SEGMENT | CORP         | GC-MEETCORP         | 0                                         | NULL                                   |
    And System copies the missing mappings into PMS_Migration_Mapping
    And User has provided AMS Mapping Configurations in PMS_Migration_Mapping_amsRule As below
      | Market Code | Rate Code | Attribute                              | Forecast Type |
      | GC-MEETCORP | NULL      | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | 1             |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 12                |
      | 37         | 1                 |
    And Create pace records in Pace_Group_Master for market segment given below
      | Mkt_Seg_ID |
      | 7          |
      | 37         |
    And Number of records in Pace_Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 12                |
      | 37         | 1                 |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 0                 |
      | 37         | 0                 |
    And Assuming User has accepted Business Type Shift popup adding below entry in MS_Recoding_Business_Type_Shift
      | Market_Segment_Name | Previous_Business_Type | New_Business_Type |
      | GC-MEETCORP         | Group                  | Transient         |
    And Property stage is ONE_WAY
    When MktSegRecoding job is triggered
    Then Property stage is set as DORMANT
    And Relevant tables are backed up
    And AnalyticalMarketSegment now has rules for new market segments in Analytical_Market_Segment as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code | Mapped_Market_Code | Attribute                              | Rate_Code_Type | Rank | complimentary | Preserved |
      | 79                    | GC-MEET     | NULL      | GC-MEETCORP        | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | ALL            | 1    | 0             | 1         |
      | 315                   | CORP        | NULL      | GC-MEETCORP        | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | ALL            | 1    | 0             | 1         |
      | 828                   | GC-MEETCORP | NULL      | GC-MEETCORP        | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | ALL            | 1    | 0             | 0         |
    And New market segments are populated in MarketSegment table Mkt_Seg as below
      | Mkt_Seg_ID | Property_ID | Mkt_Seg_Code | Mkt_Seg_Name | Mkt_Seg_Description | Status_ID | complimentary |
      | 92         | 11033       | GC-MEETCORP  | GC-MEETCORP  | GC-MEETCORP         | 1         | 0             |
    And Following market segments are deleted from Mkt_Seg table
      | Mkt_Seg_ID | Mkt_Seg_Code |
      | 7          | CORP         |
      | 37         | GC-MEET      |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code | Number_Of_Records |
      | 7          | CORP        | CF74      | 0                 |
      | 7          | CORP        | CF65      | 0                 |
      | 7          | CORP        | C79       | 0                 |
      | 37         | GC-MEET     | CF77      | 0                 |
      | 92         | GC-MEETCORP | CF74      | 103               |
      | 92         | GC-MEETCORP | CF65      | 60                |
      | 92         | GC-MEETCORP | C79       | 92                |
      | 92         | GC-MEETCORP | CF77      | 5                 |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 0                 |
      | 37         | 0                 |
      | 92         | 13                |
    And Number of records in Pace_Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 0                 |
      | 37         | 0                 |
      | 92         | 13                |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 0                 |
      | 37         | 0                 |
      | 92         | 1                 |
#    And ReservationNight records are updated
#    And ReservationNightChange records are updated
#    And Old AnalyticalMarketSegment rules are preserved and mappedMarketCodes are updated
#    And InformationManager alerts,notifications are cleaned up
#    And Relevant scheduled reports are cleaned up
#    And All overrides are cleaned up
#    And Redundant market segments are cleaned up
#    And Activity data for new market segment is populated in MktAccomActivity
#    And Pacebackfill executed successfully for new market segments
    And Postbackfill data validation step failed and report is generated
    And I continue the Postbackfill data validation step
    And CreateCommitForecastGroup alert is generated
    And I trigger Create/Commit Forecast Group job
    And I resolve the CreateCommitForecastGroup alert

  Scenario: Verify MarketSegment Recoding When Group Market Segments Are Merged and Converted Into Existing Straight Transient With Change In Market Code
    Given Market Segment Recoding configuration is set in Mkt_Seg_Recoding_Config as Below
      | Client_ID | Property_ID | Old_Sys_Date            | Recoding_State               | Created_by_User_ID | Created_DTTM            | Last_Updated_DTTM       | Last_Updated_by_User_ID | Is_Backup_Created |
      | 6         | 11033       | 2020-02-20 00:00:00.000 | MKT_SEG_RECODING_NOT_STARTED | 11403              | 2020-02-21 15:27:16.063 | 2020-02-21 16:17:53.263 | 1                       | 0                 |
    And Old Market Segments in Mkt_Seg table is as below
      | Mkt_Seg_ID | Property_ID | Mkt_Seg_Code | Mkt_Seg_Name | Mkt_Seg_Description | Status_ID | complimentary |
      | 7          | 11033       | CORP         | CORP         | CORP                | 1         | 0             |
      | 9          | 11033       | FAM          | FAM          | FAM                 | 1         | 0             |
      | 37         | 11033       | GC-MEET      | GC-MEET      | GC-MEET             | 1         | 0             |
    And Old Market Segments related rules in Analytical_Market_Segment is as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code | Mapped_Market_Code | Attribute                              | Rate_Code_Type | Rank | complimentary | Preserved |
      | 315                   | CORP        | NULL      | CORP               | GROUP                                  | ALL            | 1    | 0             | 0         |
      | 304                   | FAM         | NULL      | FAM                | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | ALL            | 1    | 0             | 0         |
      | 79                    | GC-MEET     | NULL      | GC-MEET            | GROUP                                  | ALL            | 1    | 0             | 0         |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code | Number_Of_Records |
      | 7          | CORP        | CF74      | 103               |
      | 7          | CORP        | CF65      | 60                |
      | 7          | CORP        | C79       | 92                |
      | 37         | GC-MEET     | CF77      | 5                 |
      | 9          | FAM         | COMP      | 3                 |
    And User has provided MS Mapping Configurations in PMS_Migration_Mapping As Below
      | G3 RMS Property Code | Code Type                | Current Code | New Equivalent Code | Remove and permanently delete the history | Is Primary Code for One to Many Splits |
      | CPGP02               | MARKET_SEGMENT           | GC-MEET      | FAM                 | 0                                         | NULL                                   |
      | CPGP02               | MARKET_SEGMENT           | CORP         | FAM                 | 0                                         | NULL                                   |
      | CPGP02               | MARKET_SEGMENT_NON_GROUP | FAM          | FAM                 | 0                                         | NULL                                   |
    And System copies the missing mappings into PMS_Migration_Mapping
    And User has provided AMS Mapping Configurations in PMS_Migration_Mapping_amsRule As below
      | Market Code | Rate Code | Attribute                              | Forecast Type |
      | FAM         | NULL      | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | 1             |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 12                |
      | 37         | 1                 |
    And Create pace records in Pace_Group_Master for market segment given below
      | Mkt_Seg_ID |
      | 7          |
      | 37         |
    And Number of records in Pace_Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 12                |
      | 37         | 1                 |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 0                 |
      | 37         | 0                 |
    And Assuming User has accepted Business Type Shift popup adding below entry in MS_Recoding_Business_Type_Shift
      | Market_Segment_Name | Previous_Business_Type | New_Business_Type |
      | FAM                 | Group                  | Transient         |
    And Property stage is ONE_WAY
    When MktSegRecoding job is triggered
    Then Property stage is set as DORMANT
    And Relevant tables are backed up
    And AnalyticalMarketSegment now has rules for new market segments in Analytical_Market_Segment as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code | Mapped_Market_Code | Attribute                              | Rate_Code_Type | Rank | complimentary | Preserved |
      | 79                    | GC-MEET     | NULL      | FAM                | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | ALL            | 1    | 0             | 1         |
      | 304                   | FAM         | NULL      | FAM                | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | ALL            | 1    | 0             | 0         |
      | 315                   | CORP        | NULL      | FAM                | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | ALL            | 1    | 0             | 1         |
    And New market segments are populated in MarketSegment table Mkt_Seg as below
      | Mkt_Seg_ID | Property_ID | Mkt_Seg_Code | Mkt_Seg_Name | Mkt_Seg_Description | Status_ID | complimentary |
      | 9          | 11033       | FAM          | FAM          | FAM                 | 1         | 0             |
    And Following market segments are deleted from Mkt_Seg table
      | Mkt_Seg_ID | Mkt_Seg_Code |
      | 7          | CORP         |
      | 37         | GC-MEET      |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code | Number_Of_Records |
      | 7          | CORP        | CF74      | 0                 |
      | 7          | CORP        | CF65      | 0                 |
      | 7          | CORP        | C79       | 0                 |
      | 37         | GC-MEET     | CF77      | 0                 |
      | 9          | FAM         | CF74      | 103               |
      | 9          | FAM         | CF65      | 60                |
      | 9          | FAM         | C79       | 92                |
      | 9          | FAM         | CF77      | 5                 |
      | 9          | FAM         | COMP      | 3                 |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 0                 |
      | 37         | 0                 |
      | 9          | 13                |
    And Number of records in Pace_Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 0                 |
      | 37         | 0                 |
      | 9          | 13                |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 0                 |
      | 37         | 0                 |
      | 9          | 1                 |
#    And ReservationNight records are updated
#    And ReservationNightChange records are updated
#    And Old AnalyticalMarketSegment rules are preserved and mappedMarketCodes are updated
#    And InformationManager alerts,notifications are cleaned up
#    And Relevant scheduled reports are cleaned up
#    And All overrides are cleaned up
#    And Redundant market segments are cleaned up
#    And Activity data for new market segment is populated in MktAccomActivity
#    And Pacebackfill executed successfully for new market segments
    And Postbackfill data validation step failed and report is generated
    And I continue the Postbackfill data validation step
    And CreateCommitForecastGroup alert is generated
    And I trigger Create/Commit Forecast Group job
    And I resolve the CreateCommitForecastGroup alert

  Scenario: Verify MarketSegment Recoding When Group Market Segments Are Merged and Converted Into Existing Non Straight Transient With Change In Market Code
    Given Market Segment Recoding configuration is set in Mkt_Seg_Recoding_Config as Below
      | Client_ID | Property_ID | Old_Sys_Date            | Recoding_State               | Created_by_User_ID | Created_DTTM            | Last_Updated_DTTM       | Last_Updated_by_User_ID | Is_Backup_Created |
      | 6         | 11033       | 2020-02-20 00:00:00.000 | MKT_SEG_RECODING_NOT_STARTED | 11403              | 2020-02-21 15:27:16.063 | 2020-02-21 16:17:53.263 | 1                       | 0                 |
    And Old Market Segments in Mkt_Seg table is as below
      | Mkt_Seg_ID | Property_ID | Mkt_Seg_Code | Mkt_Seg_Name | Mkt_Seg_Description | Status_ID | complimentary |
      | 7          | 11033       | CORP         | CORP         | CORP                | 1         | 0             |
      | 37         | 11033       | GC-MEET      | GC-MEET      | GC-MEET             | 1         | 0             |
      | 42         | 11033       | IN-CDNL      | IN-CDNL      | IN-CDNL             | 1         | 0             |
    And Old Market Segments related rules in Analytical_Market_Segment is as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code  | Mapped_Market_Code | Attribute                              | Rate_Code_Type | Rank | complimentary | Preserved |
      | 315                   | CORP        | NULL       | CORP               | GROUP                                  | ALL            | 1    | 0             | 0         |
      | 79                    | GC-MEET     | NULL       | GC-MEET            | GROUP                                  | ALL            | 1    | 0             | 0         |
      | 678                   | IN-CDNL     | C CHEMSAR  | IN-CDNL_QY         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | EQUALS         | 10   | 0             | 0         |
      | 679                   | IN-CDNL     | L-CARLS    | IN-CDNL_QY         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | EQUALS         | 10   | 0             | 0         |
      | 680                   | IN-CDNL     | C SWEDBANK | IN-CDNL_QY         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | EQUALS         | 10   | 0             | 0         |
      | 681                   | IN-CDNL     | NULL       | IN-CDNL_DEF        | QUALIFIED_NONBLOCK_LINKED_YIELDABLE    | DEFAULT        | 999  | 0             | 0         |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code  | Number_Of_Records |
      | 7          | CORP        | CF74       | 103               |
      | 7          | CORP        | CF65       | 60                |
      | 7          | CORP        | C79        | 92                |
      | 37         | GC-MEET     | CF77       | 5                 |
      | 42         | IN-CDNL     | C CHEMSAR  | 6                 |
      | 42         | IN-CDNL     | C SWEDBANK | 88                |
      | 42         | IN-CDNL     | L-CARLS    | 33                |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 0                 |
      | 37         | 0                 |
    And User has provided MS Mapping Configurations in PMS_Migration_Mapping As Below
      | G3 RMS Property Code | Code Type                | Current Code | New Equivalent Code | Remove and permanently delete the history | Is Primary Code for One to Many Splits |
      | CPGP02               | MARKET_SEGMENT           | GC-MEET      | IN-CDNL             | 0                                         | NULL                                   |
      | CPGP02               | MARKET_SEGMENT           | CORP         | IN-CDNL             | 0                                         | NULL                                   |
      | CPGP02               | MARKET_SEGMENT_NON_GROUP | IN-CDNL      | IN-CDNL             | 0                                         | NULL                                   |
    And System copies the missing mappings into PMS_Migration_Mapping
    And User has provided AMS Mapping Configurations in PMS_Migration_Mapping_amsRule As below
      | Market Code | Rate Code  | Attribute                              | Forecast Type |
      | IN-CDNL     | CF74       | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | 1             |
      | IN-CDNL     | CF65       | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | 1             |
      | IN-CDNL     | C79        | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | 1             |
      | IN-CDNL     | CF77       | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | 1             |
      | IN-CDNL     | C CHEMSAR  | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | 1             |
      | IN-CDNL     | C SWEDBANK | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | 1             |
      | IN-CDNL     | L-CARLS    | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | 1             |
      | IN-CDNL     | DEFAULT    | QUALIFIED_NONBLOCK_LINKED_YIELDABLE    | 1             |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 12                |
      | 37         | 1                 |
    And Create pace records in Pace_Group_Master for market segment given below
      | Mkt_Seg_ID |
      | 7          |
      | 37         |
    And Number of records in Pace_Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 12                |
      | 37         | 1                 |
    And Assuming User has accepted Business Type Shift popup adding below entry in MS_Recoding_Business_Type_Shift
      | Market_Segment_Name | Previous_Business_Type | New_Business_Type |
      | IN-CDNL             | Group                  | Transient         |
    And Property stage is ONE_WAY
    When MktSegRecoding job is triggered
    Then Property stage is set as DORMANT
    And Relevant tables are backed up
    And AnalyticalMarketSegment now has rules for new market segments in Analytical_Market_Segment as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code  | Mapped_Market_Code | Attribute                              | Rate_Code_Type | Rank | complimentary | Preserved |
      | 678                   | IN-CDNL     | C CHEMSAR  | IN-CDNL_QY         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | EQUALS         | 10   | 0             | 0         |
      | 679                   | IN-CDNL     | L-CARLS    | IN-CDNL_QY         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | EQUALS         | 10   | 0             | 0         |
      | 680                   | IN-CDNL     | C SWEDBANK | IN-CDNL_QY         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | EQUALS         | 10   | 0             | 0         |
      | 681                   | IN-CDNL     | NULL       | IN-CDNL_DEF        | QUALIFIED_NONBLOCK_LINKED_YIELDABLE    | DEFAULT        | 999  | 0             | 0         |
      | 828                   | IN-CDNL     | CF74       | IN-CDNL_QY         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | EQUALS         | 10   | 0             | 0         |
      | 829                   | IN-CDNL     | CF65       | IN-CDNL_QY         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | EQUALS         | 10   | 0             | 0         |
      | 830                   | IN-CDNL     | C79        | IN-CDNL_QY         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | EQUALS         | 10   | 0             | 0         |
      | 831                   | IN-CDNL     | CF77       | IN-CDNL_QY         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | EQUALS         | 10   | 0             | 0         |
      | 832                   | CORP        | C79        | IN-CDNL_QY         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | EQUALS         | 10   | 0             | 1         |
      | 833                   | GC-MEET     | CF77       | IN-CDNL_QY         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | EQUALS         | 10   | 0             | 1         |
      | 834                   | CORP        | CF65       | IN-CDNL_QY         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | EQUALS         | 10   | 0             | 1         |
      | 835                   | CORP        | CF74       | IN-CDNL_QY         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | EQUALS         | 10   | 0             | 1         |
      | 836                   | CORP        | NULL       | IN-CDNL_DEF        | QUALIFIED_NONBLOCK_LINKED_YIELDABLE    | DEFAULT        | 999  | 0             | 1         |
      | 837                   | GC-MEET     | NULL       | IN-CDNL_DEF        | QUALIFIED_NONBLOCK_LINKED_YIELDABLE    | DEFAULT        | 999  | 0             | 1         |
    And Following market segments are deleted from Mkt_Seg table
      | Mkt_Seg_ID | Mkt_Seg_Code |
      | 7          | CORP         |
      | 37         | GC-MEET      |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code  | Number_Of_Records |
      | 7          | CORP        | CF74       | 0                 |
      | 7          | CORP        | CF65       | 0                 |
      | 7          | CORP        | C79        | 0                 |
      | 37         | GC-MEET     | CF77       | 0                 |
      | 42         | IN-CDNL     | CF74       | 103               |
      | 42         | IN-CDNL     | CF65       | 60                |
      | 42         | IN-CDNL     | C79        | 92                |
      | 42         | IN-CDNL     | CF77       | 5                 |
      | 42         | IN-CDNL     | C CHEMSAR  | 6                 |
      | 42         | IN-CDNL     | C SWEDBANK | 88                |
      | 42         | IN-CDNL     | L-CARLS    | 33                |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 0                 |
      | 37         | 0                 |
      | 92         | 13                |
    And Number of records in Pace_Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 0                 |
      | 37         | 0                 |
      | 92         | 13                |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 0                 |
      | 37         | 0                 |
      | 42         | 1                 |
      | 79         | 1                 |
      | 92         | 1                 |
#    And ReservationNight records are updated
#    And ReservationNightChange records are updated
#    And Old AnalyticalMarketSegment rules are preserved and mappedMarketCodes are updated
#    And InformationManager alerts,notifications are cleaned up
#    And Relevant scheduled reports are cleaned up
#    And All overrides are cleaned up
#    And Redundant market segments are cleaned up
#    And Activity data for new market segment is populated in MktAccomActivity
#    And Pacebackfill executed successfully for new market segments
    And Postbackfill data validation step failed and report is generated
    And I continue the Postbackfill data validation step
    And CreateCommitForecastGroup alert is generated
    And I trigger Create/Commit Forecast Group job
    And I resolve the CreateCommitForecastGroup alert

  Scenario: Verify MarketSegment Recoding When Group Market Segment Is Split and Converted Into New Straight Transient With Change In Market Code
    Given Market Segment Recoding configuration is set in Mkt_Seg_Recoding_Config as Below
      | Client_ID | Property_ID | Old_Sys_Date            | Recoding_State               | Created_by_User_ID | Created_DTTM            | Last_Updated_DTTM       | Last_Updated_by_User_ID | Is_Backup_Created |
      | 6         | 11033       | 2020-02-20 00:00:00.000 | MKT_SEG_RECODING_NOT_STARTED | 11403              | 2020-02-21 15:27:16.063 | 2020-02-21 16:17:53.263 | 1                       | 0                 |
    And Old Market Segments in Mkt_Seg table is as below
      | Mkt_Seg_ID | Property_ID | Mkt_Seg_Code | Mkt_Seg_Name | Mkt_Seg_Description | Status_ID | complimentary |
      | 7          | 11033       | CORP         | CORP         | CORP                | 1         | 0             |
    And Old Market Segments related rules in Analytical_Market_Segment is as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code | Mapped_Market_Code | Attribute | Rate_Code_Type | Rank | complimentary | Preserved |
      | 315                   | CORP        | NULL      | CORP               | GROUP     | ALL            | 1    | 0             | 0         |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code | Number_Of_Records |
      | 7          | CORP        | CF74      | 103               |
      | 7          | CORP        | CF65      | 60                |
      | 7          | CORP        | C79       | 92                |
    And User has provided MS Mapping Configurations in PMS_Migration_Mapping As Below
      | G3 RMS Property Code | Code Type      | Current Code | New Equivalent Code | Remove and permanently delete the history | Is Primary Code for One to Many Splits |
      | CPGP02               | MARKET_SEGMENT | CORP         | CORPNew1            | 0                                         | 1                                      |
      | CPGP02               | MARKET_SEGMENT | CORP         | CORPNew2            | 0                                         | 0                                      |
    And System copies the missing mappings into PMS_Migration_Mapping
    And User has provided AMS Mapping Configurations in PMS_Migration_Mapping_amsRule As below
      | Market Code | Rate Code | Attribute                              | Forecast Type |
      | CORPNew1    | NULL      | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | 1             |
      | CORPNew2    | NULL      | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | 1             |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 12                |
    And Create pace records in Pace_Group_Master for market segment given below
      | Mkt_Seg_ID |
      | 7          |
    And Number of records in Pace_Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 12                |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 0                 |
    And Assuming User has accepted Business Type Shift popup adding below entry in MS_Recoding_Business_Type_Shift
      | Market_Segment_Name | Previous_Business_Type | New_Business_Type |
      | CORPNew1            | Group                  | Transient         |
      | CORPNew2            | Group                  | Transient         |
    And Property stage is ONE_WAY
    When MktSegRecoding job is triggered
    Then Property stage is set as DORMANT
    And Relevant tables are backed up
    And AnalyticalMarketSegment now has rules for new market segments in Analytical_Market_Segment as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code | Mapped_Market_Code | Attribute                              | Rate_Code_Type | Rank | complimentary | Preserved |
      | 315                   | CORP        | NULL      | CORPNew1           | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | ALL            | 1    | 0             | 1         |
      | 828                   | CORPNew1    | NULL      | CORPNew1           | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | ALL            | 1    | 0             | 0         |
      | 829                   | CORPNew2    | NULL      | CORPNew2           | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | ALL            | 1    | 0             | 0         |
    And New market segments are populated in MarketSegment table Mkt_Seg as below
      | Mkt_Seg_ID | Property_ID | Mkt_Seg_Code | Mkt_Seg_Name | Mkt_Seg_Description | Status_ID | complimentary |
      | 92         | 11033       | CORPNew1     | CORPNew1     | CORPNew1            | 1         | 0             |
      | 93         | 11033       | CORPNew2     | CORPNew2     | CORPNew2            | 1         | 0             |
    And Following market segments are deleted from Mkt_Seg table
      | Mkt_Seg_ID | Mkt_Seg_Code |
      | 7          | CORP         |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code | Number_Of_Records |
      | 7          | CORP        | CF74      | 0                 |
      | 7          | CORP        | CF65      | 0                 |
      | 7          | CORP        | C79       | 0                 |
      | 92         | CORPNew1    | CF74      | 103               |
      | 92         | CORPNew1    | CF65      | 60                |
      | 92         | CORPNew1    | C79       | 92                |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 92         | 12                |
      | 7          | 0                 |
    And Number of records in Pace_Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 92         | 12                |
      | 7          | 0                 |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 0                 |
      | 92         | 1                 |
#    And ReservationNight records are updated
#    And ReservationNightChange records are updated
#    And Old AnalyticalMarketSegment rules are preserved and mappedMarketCodes are updated
#    And InformationManager alerts,notifications are cleaned up
#    And Relevant scheduled reports are cleaned up
#    And All overrides are cleaned up
#    And Redundant market segments are cleaned up
#    And Activity data for new market segment is populated in MktAccomActivity
#    And Pacebackfill executed successfully for new market segments
    And Postbackfill data validation step failed and report is generated
    And I continue the Postbackfill data validation step
    And CreateCommitForecastGroup alert is generated
    And I trigger Create/Commit Forecast Group job
    And I resolve the CreateCommitForecastGroup alert

  Scenario: Verify MarketSegment Recoding When Group Market Segment Is Split and Converted Into Existing Non Straight Transient With Change In Market Code
    Given Market Segment Recoding configuration is set in Mkt_Seg_Recoding_Config as Below
      | Client_ID | Property_ID | Old_Sys_Date            | Recoding_State               | Created_by_User_ID | Created_DTTM            | Last_Updated_DTTM       | Last_Updated_by_User_ID | Is_Backup_Created |
      | 6         | 11033       | 2020-02-20 00:00:00.000 | MKT_SEG_RECODING_NOT_STARTED | 11403              | 2020-02-21 15:27:16.063 | 2020-02-21 16:17:53.263 | 1                       | 0                 |
    And Old Market Segments in Mkt_Seg table is as below
      | Mkt_Seg_ID | Property_ID | Mkt_Seg_Code | Mkt_Seg_Name | Mkt_Seg_Description | Status_ID | complimentary |
      | 7          | 11033       | CORP         | CORP         | CORP                | 1         | 0             |
      | 42         | 11033       | IN-CDNL      | IN-CDNL      | IN-CDNL             | 1         | 0             |
      | 44         | 11033       | IN-CLUB      | IN-CLUB      | IN-CLUB             | 1         | 0             |
    And Old Market Segments related rules in Analytical_Market_Segment is as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code    | Mapped_Market_Code | Attribute                               | Rate_Code_Type | Rank | complimentary | Preserved |
      | 315                   | CORP        | NULL         | CORP               | GROUP                                   | ALL            | 1    | 0             | 0         |
      | 650                   | IN-CLUB     | DAILY_1CLUBO | IN-CLUB_QSL        | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | EQUALS         | 10   | 0             | 0         |
      | 651                   | IN-CLUB     | DAILY_CLUBO  | IN-CLUB_QSL        | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | EQUALS         | 10   | 0             | 0         |
      | 652                   | IN-CLUB     | NULL         | IN-CLUB_DEF        | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | DEFAULT        | 999  | 0             | 0         |
      | 678                   | IN-CDNL     | C CHEMSAR    | IN-CDNL_QY         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 0         |
      | 679                   | IN-CDNL     | L-CARLS      | IN-CDNL_QY         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 0         |
      | 680                   | IN-CDNL     | C SWEDBANK   | IN-CDNL_QY         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 0         |
      | 681                   | IN-CDNL     | NULL         | IN-CDNL_DEF        | QUALIFIED_NONBLOCK_LINKED_YIELDABLE     | DEFAULT        | 999  | 0             | 0         |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code    | Number_Of_Records |
      | 7          | CORP        | CF74         | 103               |
      | 7          | CORP        | CF65         | 60                |
      | 7          | CORP        | C79          | 92                |
      | 42         | IN-CDNL     | C CHEMSAR    | 6                 |
      | 42         | IN-CDNL     | C SWEDBANK   | 88                |
      | 42         | IN-CDNL     | L-CARLS      | 33                |
      | 44         | IN-CLUB     | DAILY_1CLUBO | 4                 |
      | 44         | IN-CLUB     | DAILY_CLUBO  | 3                 |
    And User has provided MS Mapping Configurations in PMS_Migration_Mapping As Below
      | G3 RMS Property Code | Code Type                | Current Code | New Equivalent Code | Remove and permanently delete the history | Is Primary Code for One to Many Splits |
      | CPGP02               | MARKET_SEGMENT           | CORP         | IN-CDNL             | 0                                         | 1                                      |
      | CPGP02               | MARKET_SEGMENT           | CORP         | IN-CLUB             | 0                                         | 0                                      |
      | CPGP02               | MARKET_SEGMENT_NON_GROUP | IN-CDNL      | IN-CDNL             | 0                                         | NULL                                   |
      | CPGP02               | MARKET_SEGMENT_NON_GROUP | IN-CLUB      | IN-CLUB             | 0                                         | NULL                                   |
    And System copies the missing mappings into PMS_Migration_Mapping
    And User has provided AMS Mapping Configurations in PMS_Migration_Mapping_amsRule As below
      | Market Code | Rate Code    | Attribute                               | Forecast Type |
      | IN-CDNL     | CF74         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | 1             |
      | IN-CDNL     | CF65         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | 1             |
      | IN-CDNL     | C CHEMSAR    | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | 1             |
      | IN-CDNL     | C SWEDBANK   | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | 1             |
      | IN-CDNL     | L-CARLS      | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | 1             |
      | IN-CDNL     | DEFAULT      | QUALIFIED_NONBLOCK_LINKED_YIELDABLE     | 1             |
      | IN-CLUB     | DAILY_1CLUBO | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | 1             |
      | IN-CLUB     | DAILY_CLUBO  | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | 1             |
      | IN-CLUB     | C79          | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | 1             |
      | IN-CLUB     | DEFAULT      | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | 1             |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 12                |
    And Create pace records in Pace_Group_Master for market segment given below
      | Mkt_Seg_ID |
      | 7          |
    And Number of records in Pace_Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 12                |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 0                 |
    And Assuming User has accepted Business Type Shift popup adding below entry in MS_Recoding_Business_Type_Shift
      | Market_Segment_Name | Previous_Business_Type | New_Business_Type |
      | IN-CDNL             | Group                  | Transient         |
      | IN-CLUB             | Group                  | Transient         |
    And Property stage is ONE_WAY
    When MktSegRecoding job is triggered
    Then Property stage is set as DORMANT
    And Relevant tables are backed up
    And AnalyticalMarketSegment now has rules for new market segments in Analytical_Market_Segment as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code    | Mapped_Market_Code | Attribute                               | Rate_Code_Type | Rank | complimentary | Preserved |
      | 650                   | IN-CLUB     | DAILY_1CLUBO | IN-CLUB_QSL        | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | EQUALS         | 10   | 0             | 0         |
      | 651                   | IN-CLUB     | DAILY_CLUBO  | IN-CLUB_QSL        | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | EQUALS         | 10   | 0             | 0         |
      | 652                   | IN-CLUB     | NULL         | IN-CLUB_DEF        | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | DEFAULT        | 999  | 0             | 0         |
      | 678                   | IN-CDNL     | C CHEMSAR    | IN-CDNL_QY         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 0         |
      | 679                   | IN-CDNL     | L-CARLS      | IN-CDNL_QY         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 0         |
      | 680                   | IN-CDNL     | C SWEDBANK   | IN-CDNL_QY         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 0         |
      | 681                   | IN-CDNL     | NULL         | IN-CDNL_DEF        | QUALIFIED_NONBLOCK_LINKED_YIELDABLE     | DEFAULT        | 999  | 0             | 0         |
      | 828                   | IN-CDNL     | CF74         | IN-CDNL_QY         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 0         |
      | 829                   | IN-CDNL     | CF65         | IN-CDNL_QY         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 0         |
      | 830                   | IN-CLUB     | C79          | IN-CLUB_QSL        | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | EQUALS         | 10   | 0             | 0         |
      | 831                   | CORP        | C79          | IN-CLUB_QSL        | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE | EQUALS         | 10   | 0             | 1         |
      | 832                   | CORP        | CF65         | IN-CDNL_QY         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 1         |
      | 833                   | CORP        | CF74         | IN-CDNL_QY         | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE  | EQUALS         | 10   | 0             | 1         |
    And New market segments are populated in MarketSegment table Mkt_Seg as below
      | Mkt_Seg_ID | Property_ID | Mkt_Seg_Code | Mkt_Seg_Name | Mkt_Seg_Description | Status_ID | complimentary |
      | 42         | 11033       | IN-CDNL      | IN-CDNL      | IN-CDNL             | 1         | 0             |
      | 44         | 11033       | IN-CLUB      | IN-CLUB      | IN-CLUB             | 1         | 0             |
    And Following market segments are deleted from Mkt_Seg table
      | Mkt_Seg_ID | Mkt_Seg_Code |
      | 7          | CORP         |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code    | Number_Of_Records |
      | 7          | CORP        | CF74         | 0                 |
      | 7          | CORP        | CF65         | 0                 |
      | 7          | CORP        | C79          | 0                 |
      | 42         | IN-CDNL     | C CHEMSAR    | 6                 |
      | 42         | IN-CDNL     | C SWEDBANK   | 88                |
      | 42         | IN-CDNL     | CF65         | 60                |
      | 42         | IN-CDNL     | CF74         | 103               |
      | 42         | IN-CDNL     | L-CARLS      | 33                |
      | 44         | IN-CLUB     | C79          | 92                |
      | 44         | IN-CLUB     | DAILY_1CLUBO | 4                 |
      | 44         | IN-CLUB     | DAILY_CLUBO  | 3                 |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 93         | 12                |
      | 7          | 0                 |
    And Number of records in Pace_Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 93         | 12                |
      | 7          | 0                 |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 42         | 1                 |
      | 44         | 1                 |
      | 92         | 1                 |
      | 93         | 1                 |
      | 7          | 0                 |
#    And ReservationNight records are updated
#    And ReservationNightChange records are updated
#    And Old AnalyticalMarketSegment rules are preserved and mappedMarketCodes are updated
#    And InformationManager alerts,notifications are cleaned up
#    And Relevant scheduled reports are cleaned up
#    And All overrides are cleaned up
#    And Redundant market segments are cleaned up
#    And Activity data for new market segment is populated in MktAccomActivity
#    And Pacebackfill executed successfully for new market segments
    And Postbackfill data validation step failed and report is generated
    And I continue the Postbackfill data validation step
    And CreateCommitForecastGroup alert is generated
    And I trigger Create/Commit Forecast Group job
    And I resolve the CreateCommitForecastGroup alert

  Scenario: Verify MarketSegment Recoding When Group Market Segment Is Split and Converted Into Existing Straight Transient With Change In Market Code
    Given Market Segment Recoding configuration is set in Mkt_Seg_Recoding_Config as Below
      | Client_ID | Property_ID | Old_Sys_Date            | Recoding_State               | Created_by_User_ID | Created_DTTM            | Last_Updated_DTTM       | Last_Updated_by_User_ID | Is_Backup_Created |
      | 6         | 11033       | 2020-02-20 00:00:00.000 | MKT_SEG_RECODING_NOT_STARTED | 11403              | 2020-02-21 15:27:16.063 | 2020-02-21 16:17:53.263 | 1                       | 0                 |
    And Old Market Segments in Mkt_Seg table is as below
      | Mkt_Seg_ID | Property_ID | Mkt_Seg_Code | Mkt_Seg_Name | Mkt_Seg_Description | Status_ID | complimentary |
      | 7          | 11033       | CORP         | CORP         | CORP                | 1         | 0             |
      | 9          | 11033       | FAM          | FAM          | FAM                 | 1         | 0             |
      | 61         | 11033       | LNG          | LNG          | LNG                 | 1         | 0             |
    And Old Market Segments related rules in Analytical_Market_Segment is as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code | Mapped_Market_Code | Attribute                              | Rate_Code_Type | Rank | complimentary | Preserved |
      | 302                   | LNG         | NULL      | LNG                | FENCED_AND_PACKAGED                    | ALL            | 1    | 0             | 0         |
      | 304                   | FAM         | NULL      | FAM                | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | ALL            | 1    | 0             | 0         |
      | 315                   | CORP        | NULL      | CORP               | GROUP                                  | ALL            | 1    | 0             | 0         |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code | Number_Of_Records |
      | 7          | CORP        | CF74      | 103               |
      | 7          | CORP        | CF65      | 60                |
      | 7          | CORP        | C79       | 92                |
      | 9          | FAM         | COMP      | 3                 |
      | 9          | FAM         | COMP      | 3                 |
      | 61         | LNG         | DAILY_2   | 32                |
      | 61         | LNG         | STAY3     | 26                |
      | 61         | LNG         | STAY3-ONL | 66                |
      | 61         | LNG         | TS 2N     | 45                |
      | 61         | LNG         | TS 2N17   | 82                |
      | 61         | LNG         | TS 3N     | 14                |
      | 61         | LNG         | TS 3N17   | 100               |
      | 61         | LNG         | TS 3N17 0 | 9                 |
      | 61         | LNG         | TS OFFFM  | 2                 |
    And User has provided MS Mapping Configurations in PMS_Migration_Mapping As Below
      | G3 RMS Property Code | Code Type                | Current Code | New Equivalent Code | Remove and permanently delete the history | Is Primary Code for One to Many Splits |
      | CPGP02               | MARKET_SEGMENT           | CORP         | FAM                 | 0                                         | 1                                      |
      | CPGP02               | MARKET_SEGMENT           | CORP         | LNG                 | 0                                         | 0                                      |
      | CPGP02               | MARKET_SEGMENT_NON_GROUP | FAM          | FAM                 | 0                                         | 0                                      |
      | CPGP02               | MARKET_SEGMENT_NON_GROUP | LNG          | LNG                 | 0                                         | 0                                      |
    And System copies the missing mappings into PMS_Migration_Mapping
    And User has provided AMS Mapping Configurations in PMS_Migration_Mapping_amsRule As below
      | Market Code | Rate Code | Attribute                              | Forecast Type |
      | FAM         | NULL      | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | 1             |
      | LNG         | NULL      | FENCED_AND_PACKAGED                    | 1             |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 12                |
    And Create pace records in Pace_Group_Master for market segment given below
      | Mkt_Seg_ID |
      | 7          |
    And Number of records in Pace_Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 12                |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 7          | 0                 |
    And Assuming User has accepted Business Type Shift popup adding below entry in MS_Recoding_Business_Type_Shift
      | Market_Segment_Name | Previous_Business_Type | New_Business_Type |
      | FAM                 | Group                  | Transient         |
      | LNG                 | Group                  | Transient         |
    And Property stage is ONE_WAY
    When MktSegRecoding job is triggered
    Then Property stage is set as DORMANT
    And Relevant tables are backed up
    And AnalyticalMarketSegment now has rules for new market segments in Analytical_Market_Segment as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code | Mapped_Market_Code | Attribute                              | Rate_Code_Type | Rank | complimentary | Preserved |
      | 302                   | LNG         | NULL      | LNG                | FENCED_AND_PACKAGED                    | ALL            | 1    | 0             | 0         |
      | 304                   | FAM         | NULL      | FAM                | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | ALL            | 1    | 0             | 0         |
      | 315                   | CORP        | NULL      | FAM                | QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE | ALL            | 1    | 0             | 1         |
    And New market segments are populated in MarketSegment table Mkt_Seg as below
      | Mkt_Seg_ID | Property_ID | Mkt_Seg_Code | Mkt_Seg_Name | Mkt_Seg_Description | Status_ID | complimentary |
      | 9          | 11033       | FAM          | FAM          | FAM                 | 1         | 0             |
      | 61         | 11033       | LNG          | LNG          | LNG                 | 1         | 0             |
    And Following market segments are deleted from Mkt_Seg table
      | Mkt_Seg_ID | Mkt_Seg_Code |
      | 7          | CORP         |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code | Number_Of_Records |
      | 7          | CORP        | CF74      | 0                 |
      | 7          | CORP        | CF65      | 0                 |
      | 7          | CORP        | C79       | 0                 |
      | 9          | FAM         | CF74      | 103               |
      | 9          | FAM         | CF65      | 60                |
      | 9          | FAM         | C79       | 92                |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 9          | 12                |
      | 7          | 0                 |
    And Number of records in Pace_Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 9          | 12                |
      | 7          | 0                 |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 9          | 1                 |
      | 7          | 0                 |
#    And ReservationNight records are updated
#    And ReservationNightChange records are updated
#    And Old AnalyticalMarketSegment rules are preserved and mappedMarketCodes are updated
#    And InformationManager alerts,notifications are cleaned up
#    And Relevant scheduled reports are cleaned up
#    And All overrides are cleaned up
#    And Redundant market segments are cleaned up
#    And Activity data for new market segment is populated in MktAccomActivity
#    And Pacebackfill executed successfully for new market segments
    And Postbackfill data validation step failed and report is generated
    And I continue the Postbackfill data validation step
    And CreateCommitForecastGroup alert is generated
    And I trigger Create/Commit Forecast Group job
    And I resolve the CreateCommitForecastGroup alert

  Scenario: Verify MarketSegment Recoding When Group Market Segment Is Converted Into Non Straight Transient With Change In Attribution and Market Code Renamed By Changing Case
    Given Market Segment Recoding configuration is set in Mkt_Seg_Recoding_Config as Below
      | Client_ID | Property_ID | Old_Sys_Date            | Recoding_State               | Created_by_User_ID | Created_DTTM            | Last_Updated_DTTM       | Last_Updated_by_User_ID | Is_Backup_Created |
      | 6         | 11033       | 2020-02-20 00:00:00.000 | MKT_SEG_RECODING_NOT_STARTED | 11403              | 2020-02-21 15:27:16.063 | 2020-02-21 16:17:53.263 | 1                       | 0                 |
    And Old Market Segments in Mkt_Seg table is as below
      | Mkt_Seg_ID | Property_ID | Mkt_Seg_Code | Mkt_Seg_Name | Mkt_Seg_Description | Status_ID | complimentary |
      | 2          | 11033       | COMP         | COMP         | COMP                | 1         | 0             |
    And Old Market Segments related rules in Analytical_Market_Segment is as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code | Mapped_Market_Code | Attribute | Rate_Code_Type | Rank | complimentary | Preserved |
      | 827                   | COMP        | NULL      | COMP               | GROUP     | ALL            | 1    | 0             | 0         |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code  | Number_Of_Records |
      | 2          | COMP        | CF61       | 37                |
      | 2          | COMP        | COMP       | 547               |
      | 2          | COMP        | CREWPRO    | 3                 |
      | 2          | COMP        | GIFT CARD  | 3                 |
      | 2          | COMP        | L-FOC      | 108               |
      | 2          | COMP        | L-ID FOC   | 5                 |
      | 2          | COMP        | SF65       | 2                 |
      | 2          | COMP        | TS FOC     | 134               |
      | 2          | COMP        | TS GRP1N   | 1                 |
      | 2          | COMP        | TS VIPTRIP | 1                 |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 2          | 28                |
    And Create pace records in Pace_Group_Master for market segment given below
      | Mkt_Seg_ID |
      | 2          |
    And Number of records in Pace_Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 2          | 28                |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 2          | 0                 |
    And User has provided MS Mapping Configurations in PMS_Migration_Mapping As Below
      | G3 RMS Property Code | Code Type      | Current Code | New Equivalent Code | Remove and permanently delete the history | Is Primary Code for One to Many Splits |
      | CPGP02               | MARKET_SEGMENT | COMP         | comp                | 0                                         | NULL                                   |
    And System copies the missing mappings into PMS_Migration_Mapping
    And User has provided AMS Mapping Configurations in PMS_Migration_Mapping_amsRule As below
      | Market Code | Rate Code  | Attribute                                 | Forecast Type |
      | comp        | CF61       | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | 1             |
      | comp        | COMP       | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | 1             |
      | comp        | CREWPRO    | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | 1             |
      | comp        | GIFT CARD  | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | 1             |
      | comp        | L-FOC      | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | 1             |
      | comp        | L-ID FOC   | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | 1             |
      | comp        | SF65       | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | 1             |
      | comp        | TS FOC     | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | 1             |
      | comp        | TS GRP1N   | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | 1             |
      | comp        | TS VIPTRIP | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | 1             |
      | comp        | DEFAULT    | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE   | 1             |
    And Assuming User has accepted Business Type Shift popup adding below entry in MS_Recoding_Business_Type_Shift
      | Market_Segment_Name | Previous_Business_Type | New_Business_Type |
      | comp                | Group                  | Transient         |
    And Property stage is ONE_WAY
    When MktSegRecoding job is triggered
    Then Property stage is set as DORMANT
    And Relevant tables are backed up
    And AnalyticalMarketSegment now has rules for new market segments in Analytical_Market_Segment as below
      | Analytical_Mkt_Seg_ID | Market_Code | Rate_Code  | Mapped_Market_Code | Attribute                                 | Rate_Code_Type | Rank | complimentary | Preserved |
      | 828                   | comp        | CF61       | comp_QN            | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | EQUALS         | 10   | 0             | 0         |
      | 829                   | comp        | COMP       | comp_QN            | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | EQUALS         | 10   | 0             | 0         |
      | 830                   | comp        | CREWPRO    | comp_QN            | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | EQUALS         | 10   | 0             | 0         |
      | 831                   | comp        | GIFT CARD  | comp_QN            | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | EQUALS         | 10   | 0             | 0         |
      | 832                   | comp        | L-FOC      | comp_QN            | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | EQUALS         | 10   | 0             | 0         |
      | 833                   | comp        | L-ID FOC   | comp_QN            | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | EQUALS         | 10   | 0             | 0         |
      | 834                   | comp        | SF65       | comp_QN            | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | EQUALS         | 10   | 0             | 0         |
      | 835                   | comp        | TS FOC     | comp_QN            | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | EQUALS         | 10   | 0             | 0         |
      | 836                   | comp        | TS GRP1N   | comp_QN            | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | EQUALS         | 10   | 0             | 0         |
      | 837                   | comp        | TS VIPTRIP | comp_QN            | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | EQUALS         | 10   | 0             | 0         |
      | 838                   | comp        | NULL       | comp_DEF           | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE   | DEFAULT        | 999  | 0             | 0         |
      | 839                   | COMP        | SF65       | comp_QN            | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | EQUALS         | 10   | 0             | 1         |
      | 840                   | COMP        | COMP       | comp_QN            | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | EQUALS         | 10   | 0             | 1         |
      | 841                   | COMP        | TS FOC     | comp_QN            | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | EQUALS         | 10   | 0             | 1         |
      | 842                   | COMP        | GIFT CARD  | comp_QN            | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | EQUALS         | 10   | 0             | 1         |
      | 843                   | COMP        | L-ID FOC   | comp_QN            | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | EQUALS         | 10   | 0             | 1         |
      | 844                   | COMP        | CREWPRO    | comp_QN            | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | EQUALS         | 10   | 0             | 1         |
      | 845                   | COMP        | TS VIPTRIP | comp_QN            | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | EQUALS         | 10   | 0             | 1         |
      | 846                   | COMP        | L-FOC      | comp_QN            | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | EQUALS         | 10   | 0             | 1         |
      | 847                   | COMP        | CF61       | comp_QN            | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | EQUALS         | 10   | 0             | 1         |
      | 848                   | COMP        | TS GRP1N   | comp_QN            | QUALIFIED_NONBLOCK_NOTLINKED_NONYIELDABLE | EQUALS         | 10   | 0             | 1         |
      | 849                   | COMP        | NULL       | comp_DEF           | QUALIFIED_NONBLOCK_LINKED_SEMIYIELDABLE   | DEFAULT        | 999  | 0             | 1         |
    And New market segments are populated in MarketSegment table Mkt_Seg as below
      | Mkt_Seg_ID | Property_ID | Mkt_Seg_Code | Mkt_Seg_Name | Mkt_Seg_Description | Status_ID | complimentary |
      | 92         | 11033       | comp_QN      | comp_QN      | comp_QN             | 1         | 0             |
      | 93         | 11033       | comp_DEF     | comp_DEF     | comp_DEF            | 1         | 0             |
    And Following market segments are deleted from Mkt_Seg table
      | Mkt_Seg_ID | Mkt_Seg_Code |
      | 2          | COMP         |
    And Number of records in reservations for given market segment are as below
      | Mkt_Seg_ID | Market_Code | Rate_Code  | Number_Of_Records |
      | 92         | comp        | CF61       | 37                |
      | 92         | comp        | COMP       | 547               |
      | 92         | comp        | CREWPRO    | 3                 |
      | 92         | comp        | GIFT CARD  | 3                 |
      | 92         | comp        | L-FOC      | 108               |
      | 92         | comp        | L-ID FOC   | 5                 |
      | 92         | comp        | SF65       | 2                 |
      | 92         | comp        | TS FOC     | 134               |
      | 92         | comp        | TS GRP1N   | 1                 |
      | 92         | comp        | TS VIPTRIP | 1                 |
    And Number of records in Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 93         | 28                |
      | 2          | 0                 |
    And Number of records in Pace_Group_Master for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 93         | 28                |
      | 2          | 0                 |
    And Number of records in Mkt_Seg_Details_Proposed for given market segment are as below
      | Mkt_Seg_ID | Number_Of_Records |
      | 92         | 1                 |
      | 93         | 1                 |
      | 2          | 0                 |
#    And ReservationNightChange records are updated
#    And Old AnalyticalMarketSegment rules are preserved and mappedMarketCodes are updated
#    And InformationManager alerts,notifications are cleaned up
#    And Relevant scheduled reports are cleaned up
#    And All overrides are cleaned up
#    And Redundant market segments are cleaned up
#    And Activity data for new market segment is populated in MktAccomActivity
#    And Pacebackfill executed successfully for new market segments
    And Postbackfill data validation step failed and report is generated
    And I continue the Postbackfill data validation step
    And CreateCommitForecastGroup alert is generated
    And I trigger Create/Commit Forecast Group job
    And I resolve the CreateCommitForecastGroup alert