<OTA_HotelRatePlanNotifRQ Target='Production' Version='3.000'  MessageContentCode='8' xmlns:tag0='http://www.opentravel.org/OTA/2003/05'><RatePlans HotelCode='60737'><RatePlan CurrencyCode='USD' Start='2017-10-10' RatePlanNotifType='Overlay' End='2018-10-09' RatePlanCode='Product G'><Rates><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-10-10' End='2017-10-10' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-10-11' End='2017-10-11' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-10-12' End='2017-10-12' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-10-13' End='2017-10-13' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-10-14' End='2017-10-14' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-10-15' End='2017-10-15' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-10-16' End='2017-10-16' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-10-17' End='2017-10-17' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-10-18' End='2017-10-18' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-10-19' End='2017-10-19' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-10-20' End='2017-10-20' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-10-21' End='2017-10-21' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-10-22' End='2017-10-22' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-10-23' End='2017-10-23' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-10-24' End='2017-10-24' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-10-25' End='2017-10-25' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-10-26' End='2017-10-26' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-10-27' End='2017-10-27' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-10-28' End='2017-10-28' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-10-29' End='2017-10-29' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-10-30' End='2017-10-30' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-10-31' End='2017-10-31' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-01' End='2017-11-01' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='111.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='117.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-02' End='2017-11-02' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='111.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='117.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-03' End='2017-11-03' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='132.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='137.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-04' End='2017-11-04' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='132.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='137.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-05' End='2017-11-05' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='106.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='111.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-06' End='2017-11-06' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='106.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='111.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-07' End='2017-11-07' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='106.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='111.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-08' End='2017-11-08' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='108.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='113.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-09' End='2017-11-09' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='109.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='114.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-10' End='2017-11-10' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='132.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='137.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-11' End='2017-11-11' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='132.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='137.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-12' End='2017-11-12' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='106.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='111.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-13' End='2017-11-13' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='106.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='111.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-14' End='2017-11-14' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='106.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='111.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-15' End='2017-11-15' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='108.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='113.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-16' End='2017-11-16' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='109.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='114.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-17' End='2017-11-17' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='132.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='137.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-18' End='2017-11-18' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='132.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='137.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-19' End='2017-11-19' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='106.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='111.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-20' End='2017-11-20' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='106.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='111.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-21' End='2017-11-21' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='106.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='111.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-22' End='2017-11-22' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='108.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='113.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-23' End='2017-11-23' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='109.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='114.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-24' End='2017-11-24' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='132.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='137.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-25' End='2017-11-25' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='132.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='137.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-26' End='2017-11-26' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='106.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='111.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-27' End='2017-11-27' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='106.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='111.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-28' End='2017-11-28' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='106.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='111.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-29' End='2017-11-29' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='107.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='112.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-11-30' End='2017-11-30' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='107.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='112.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-01' End='2017-12-01' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-02' End='2017-12-02' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-03' End='2017-12-03' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-04' End='2017-12-04' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-05' End='2017-12-05' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-06' End='2017-12-06' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-07' End='2017-12-07' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-08' End='2017-12-08' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-09' End='2017-12-09' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-10' End='2017-12-10' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-11' End='2017-12-11' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-12' End='2017-12-12' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-13' End='2017-12-13' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-14' End='2017-12-14' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-15' End='2017-12-15' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-16' End='2017-12-16' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-17' End='2017-12-17' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-18' End='2017-12-18' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-19' End='2017-12-19' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-20' End='2017-12-20' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-21' End='2017-12-21' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-22' End='2017-12-22' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-23' End='2017-12-23' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-24' End='2017-12-24' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-25' End='2017-12-25' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-26' End='2017-12-26' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-27' End='2017-12-27' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-28' End='2017-12-28' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-29' End='2017-12-29' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='187.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='192.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-30' End='2017-12-30' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='187.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='192.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2017-12-31' End='2017-12-31' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='182.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='187.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.57' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-01' End='2018-01-01' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='175.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='181.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-02' End='2018-01-02' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='175.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='181.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-03' End='2018-01-03' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='163.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='169.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-04' End='2018-01-04' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='165.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='171.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-05' End='2018-01-05' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='170.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='176.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-06' End='2018-01-06' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='172.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='178.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-07' End='2018-01-07' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='185.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='191.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-08' End='2018-01-08' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='175.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='181.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-09' End='2018-01-09' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='177.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='183.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-10' End='2018-01-10' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='177.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='183.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-11' End='2018-01-11' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='180.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='186.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-12' End='2018-01-12' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='182.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='188.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-13' End='2018-01-13' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='182.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='188.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-14' End='2018-01-14' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='176.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='182.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-15' End='2018-01-15' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='177.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='183.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-16' End='2018-01-16' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='178.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='184.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-17' End='2018-01-17' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='177.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='183.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-18' End='2018-01-18' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='180.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='186.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-19' End='2018-01-19' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='182.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='188.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-20' End='2018-01-20' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='182.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='188.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-21' End='2018-01-21' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='176.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='182.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-22' End='2018-01-22' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='177.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='183.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-23' End='2018-01-23' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='178.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='184.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-24' End='2018-01-24' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='178.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='184.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-25' End='2018-01-25' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='180.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='186.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-26' End='2018-01-26' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='182.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='188.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-27' End='2018-01-27' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='183.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='189.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-28' End='2018-01-28' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='175.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='181.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-29' End='2018-01-29' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='176.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='182.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-30' End='2018-01-30' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='177.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='183.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-01-31' End='2018-01-31' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='178.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='184.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-01' End='2018-02-01' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='177.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='183.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-02' End='2018-02-02' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='192.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='198.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-03' End='2018-02-03' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='184.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='190.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-04' End='2018-02-04' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='172.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='178.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-05' End='2018-02-05' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='180.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='186.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-06' End='2018-02-06' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='179.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='185.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-07' End='2018-02-07' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='168.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='174.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-08' End='2018-02-08' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='175.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='181.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-09' End='2018-02-09' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='194.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='200.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-10' End='2018-02-10' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='186.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='192.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-11' End='2018-02-11' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='172.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='178.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-12' End='2018-02-12' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='180.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='186.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-13' End='2018-02-13' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='179.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='185.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-14' End='2018-02-14' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='170.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='176.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-15' End='2018-02-15' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='175.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='181.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-16' End='2018-02-16' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='195.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='201.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-17' End='2018-02-17' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='186.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='192.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-18' End='2018-02-18' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='172.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='178.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-19' End='2018-02-19' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='180.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='186.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-20' End='2018-02-20' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='179.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='185.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-21' End='2018-02-21' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='170.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='176.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-22' End='2018-02-22' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='175.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='181.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-23' End='2018-02-23' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='195.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='201.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-24' End='2018-02-24' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='186.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='192.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-25' End='2018-02-25' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='172.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='178.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-26' End='2018-02-26' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='181.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='187.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-27' End='2018-02-27' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='179.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='185.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-02-28' End='2018-02-28' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='170.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='176.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-01' End='2018-03-01' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='179.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='185.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-02' End='2018-03-02' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='180.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='186.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-03' End='2018-03-03' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='168.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='174.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-04' End='2018-03-04' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='145.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='151.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-05' End='2018-03-05' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='165.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='171.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-06' End='2018-03-06' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='165.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='171.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-07' End='2018-03-07' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='157.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='163.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-08' End='2018-03-08' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='179.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='185.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-09' End='2018-03-09' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='180.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='186.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-10' End='2018-03-10' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='168.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='174.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-11' End='2018-03-11' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='145.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='151.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-12' End='2018-03-12' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='165.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='171.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-13' End='2018-03-13' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='165.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='171.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-14' End='2018-03-14' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='157.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='163.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-15' End='2018-03-15' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='179.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='185.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-16' End='2018-03-16' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='180.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='186.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-17' End='2018-03-17' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='168.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='174.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-18' End='2018-03-18' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='145.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='151.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-19' End='2018-03-19' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='165.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='171.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-20' End='2018-03-20' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='165.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='171.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-21' End='2018-03-21' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='157.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='163.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-22' End='2018-03-22' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='179.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='185.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-23' End='2018-03-23' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='180.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='186.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-24' End='2018-03-24' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='168.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='174.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-25' End='2018-03-25' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='145.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='151.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-26' End='2018-03-26' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='165.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='171.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-27' End='2018-03-27' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='165.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='171.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-28' End='2018-03-28' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='157.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='163.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-29' End='2018-03-29' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='177.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='183.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-30' End='2018-03-30' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='180.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='186.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-03-31' End='2018-03-31' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='168.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='174.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-01' End='2018-04-01' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='176.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='182.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-02' End='2018-04-02' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='177.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='183.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-03' End='2018-04-03' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='177.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='183.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-04' End='2018-04-04' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='179.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='185.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-05' End='2018-04-05' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='180.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='186.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-06' End='2018-04-06' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='181.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='187.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-07' End='2018-04-07' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='204.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='210.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-08' End='2018-04-08' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='174.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='180.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-09' End='2018-04-09' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='175.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='181.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-10' End='2018-04-10' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='176.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='182.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-11' End='2018-04-11' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='178.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='184.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-12' End='2018-04-12' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='179.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='185.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-13' End='2018-04-13' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='182.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='188.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-14' End='2018-04-14' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='203.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='209.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-15' End='2018-04-15' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='173.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='179.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-16' End='2018-04-16' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='175.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='181.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-17' End='2018-04-17' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='176.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='182.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-18' End='2018-04-18' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='178.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='184.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-19' End='2018-04-19' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='179.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='185.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-20' End='2018-04-20' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='182.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='188.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-21' End='2018-04-21' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='203.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='209.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-22' End='2018-04-22' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='173.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='179.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-23' End='2018-04-23' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='175.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='181.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-24' End='2018-04-24' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='176.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='182.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-25' End='2018-04-25' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='178.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='184.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-26' End='2018-04-26' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='178.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='184.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-27' End='2018-04-27' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='182.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='188.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-28' End='2018-04-28' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='202.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='208.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-29' End='2018-04-29' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='172.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='178.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-04-30' End='2018-04-30' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='172.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='178.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='46.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-01' End='2018-05-01' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='164.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='170.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-02' End='2018-05-02' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='165.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='171.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-03' End='2018-05-03' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='165.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='171.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-04' End='2018-05-04' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='167.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='173.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-05' End='2018-05-05' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='183.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='189.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-06' End='2018-05-06' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='159.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='165.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-07' End='2018-05-07' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='160.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='166.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-08' End='2018-05-08' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='161.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='167.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-09' End='2018-05-09' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='163.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='169.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-10' End='2018-05-10' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='164.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='170.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-11' End='2018-05-11' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='167.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='173.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-12' End='2018-05-12' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='185.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='191.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-13' End='2018-05-13' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='158.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='164.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-14' End='2018-05-14' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='160.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='166.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-15' End='2018-05-15' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='161.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='167.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-16' End='2018-05-16' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='163.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='169.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-17' End='2018-05-17' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='164.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='170.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-18' End='2018-05-18' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='184.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='190.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-19' End='2018-05-19' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='185.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='191.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-20' End='2018-05-20' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='158.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='164.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-21' End='2018-05-21' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='160.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='166.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-22' End='2018-05-22' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='161.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='167.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-23' End='2018-05-23' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='163.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='169.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-24' End='2018-05-24' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='163.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='169.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-25' End='2018-05-25' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='183.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='189.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-26' End='2018-05-26' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='184.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='190.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-27' End='2018-05-27' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='158.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='164.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-28' End='2018-05-28' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='159.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='165.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-29' End='2018-05-29' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='160.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='166.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-30' End='2018-05-30' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='161.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='167.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-05-31' End='2018-05-31' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='161.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='167.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-01' End='2018-06-01' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='162.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='168.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-02' End='2018-06-02' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='175.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='181.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-03' End='2018-06-03' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='151.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='157.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-04' End='2018-06-04' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='153.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='159.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-05' End='2018-06-05' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='155.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='161.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-06' End='2018-06-06' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='155.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='161.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-07' End='2018-06-07' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='156.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='162.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-08' End='2018-06-08' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='174.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='180.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-09' End='2018-06-09' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='175.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='181.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-10' End='2018-06-10' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='150.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='156.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-11' End='2018-06-11' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='152.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='158.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-12' End='2018-06-12' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='155.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='161.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-13' End='2018-06-13' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='155.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='161.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-14' End='2018-06-14' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='155.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='161.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-15' End='2018-06-15' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='173.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='179.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-16' End='2018-06-16' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='176.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='182.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-17' End='2018-06-17' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='150.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='156.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-18' End='2018-06-18' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='152.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='158.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-19' End='2018-06-19' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='155.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='161.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-20' End='2018-06-20' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='155.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='161.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-21' End='2018-06-21' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='155.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='161.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-22' End='2018-06-22' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='174.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='180.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-23' End='2018-06-23' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='175.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='181.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-24' End='2018-06-24' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='150.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='156.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-25' End='2018-06-25' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='151.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='157.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-26' End='2018-06-26' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='154.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='160.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-27' End='2018-06-27' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='154.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='160.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-28' End='2018-06-28' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='154.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='160.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-29' End='2018-06-29' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='173.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='179.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-06-30' End='2018-06-30' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='174.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='180.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-01' End='2018-07-01' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='144.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='150.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-02' End='2018-07-02' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='144.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='150.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-03' End='2018-07-03' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='144.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='150.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-04' End='2018-07-04' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='148.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='154.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-05' End='2018-07-05' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='149.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='155.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-06' End='2018-07-06' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='163.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='169.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-07' End='2018-07-07' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='165.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='171.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-08' End='2018-07-08' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='144.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='150.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-09' End='2018-07-09' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='144.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='150.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-10' End='2018-07-10' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='144.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='150.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-11' End='2018-07-11' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='147.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='153.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-12' End='2018-07-12' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='148.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='154.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-13' End='2018-07-13' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='163.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='169.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-14' End='2018-07-14' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='164.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='170.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-15' End='2018-07-15' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='144.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='150.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-16' End='2018-07-16' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='144.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='150.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-17' End='2018-07-17' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='144.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='150.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-18' End='2018-07-18' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='147.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='153.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-19' End='2018-07-19' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='148.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='154.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-20' End='2018-07-20' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='163.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='169.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-21' End='2018-07-21' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='164.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='170.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-22' End='2018-07-22' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='144.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='150.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-23' End='2018-07-23' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='144.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='150.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-24' End='2018-07-24' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='144.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='150.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-25' End='2018-07-25' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='147.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='153.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-26' End='2018-07-26' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='147.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='153.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-27' End='2018-07-27' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='162.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='168.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-28' End='2018-07-28' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='163.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='169.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-29' End='2018-07-29' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='144.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='150.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-30' End='2018-07-30' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='144.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='150.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-07-31' End='2018-07-31' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='144.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='150.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-01' End='2018-08-01' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='144.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='150.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-02' End='2018-08-02' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='144.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='150.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-03' End='2018-08-03' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='161.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='167.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-04' End='2018-08-04' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='161.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='167.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-05' End='2018-08-05' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='144.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='150.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-06' End='2018-08-06' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='144.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='150.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-07' End='2018-08-07' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='144.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='150.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-08' End='2018-08-08' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='144.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='150.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-09' End='2018-08-09' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='144.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='150.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-10' End='2018-08-10' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='161.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='167.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-11' End='2018-08-11' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='161.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='167.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-12' End='2018-08-12' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='133.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='139.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-13' End='2018-08-13' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-14' End='2018-08-14' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='140.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-15' End='2018-08-15' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='140.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-16' End='2018-08-16' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='142.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-17' End='2018-08-17' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='154.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='160.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-18' End='2018-08-18' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='155.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='161.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-19' End='2018-08-19' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='133.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='139.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-20' End='2018-08-20' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-21' End='2018-08-21' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='140.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-22' End='2018-08-22' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='140.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-23' End='2018-08-23' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='142.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-24' End='2018-08-24' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='154.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='160.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-25' End='2018-08-25' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='155.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='161.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-26' End='2018-08-26' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='133.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='139.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-27' End='2018-08-27' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-28' End='2018-08-28' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='133.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='139.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-29' End='2018-08-29' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='133.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='139.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-30' End='2018-08-30' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='135.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='141.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-08-31' End='2018-08-31' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='154.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='160.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-01' End='2018-09-01' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='154.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='160.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-02' End='2018-09-02' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='110.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='116.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-03' End='2018-09-03' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='125.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='131.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-04' End='2018-09-04' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='124.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-05' End='2018-09-05' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='128.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-06' End='2018-09-06' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='143.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='149.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-07' End='2018-09-07' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='154.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='160.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-08' End='2018-09-08' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='154.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='160.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-09' End='2018-09-09' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='120.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='126.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-10' End='2018-09-10' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='124.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-11' End='2018-09-11' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='123.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-12' End='2018-09-12' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='128.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-13' End='2018-09-13' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='143.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='149.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-14' End='2018-09-14' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='154.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='160.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-15' End='2018-09-15' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='154.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='160.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-16' End='2018-09-16' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='120.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='126.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-17' End='2018-09-17' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='124.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-18' End='2018-09-18' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='123.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-19' End='2018-09-19' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='128.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='134.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-20' End='2018-09-20' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='143.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='149.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-21' End='2018-09-21' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='154.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='160.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-22' End='2018-09-22' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='154.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='160.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-23' End='2018-09-23' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='120.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='126.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-24' End='2018-09-24' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='124.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-25' End='2018-09-25' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='123.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='129.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-26' End='2018-09-26' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='139.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='145.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-27' End='2018-09-27' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='142.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='148.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-28' End='2018-09-28' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='154.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='160.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-29' End='2018-09-29' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='154.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='160.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-09-30' End='2018-09-30' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-10-01' End='2018-10-01' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-10-02' End='2018-10-02' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-10-03' End='2018-10-03' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-10-04' End='2018-10-04' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-10-05' End='2018-10-05' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='135.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='141.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-10-06' End='2018-10-06' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='135.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='141.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-10-07' End='2018-10-07' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-10-08' End='2018-10-08' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate><Rate CurrencyCode='USD' Status='Open' UnitMultiplier='1' Start='2018-10-09' End='2018-10-09' InvTypeCode='DLX' RateTimeUnit='Day'><BaseByGuestAmts><BaseByGuestAmt NumberOfGuests='1' AmountBeforeTax='130.00' AgeQualifyingCode='10'></BaseByGuestAmt><BaseByGuestAmt NumberOfGuests='2' AmountBeforeTax='136.00' AgeQualifyingCode='10'></BaseByGuestAmt></BaseByGuestAmts><AdditionalGuestAmounts><AdditionalGuestAmount Amount='34.92' AgeQualifyingCode='10'></AdditionalGuestAmount><AdditionalGuestAmount Amount='0.00' AgeQualifyingCode='8'></AdditionalGuestAmount></AdditionalGuestAmounts></Rate></Rates><Description Name='Short Description'><Text>Product G</Text></Description><Description Name='Long Description'><Text>Product G</Text></Description></RatePlan></RatePlans></OTA_HotelRatePlanNotifRQ>