package common

import net.sf.json.JSONArray


class OperaAgentDecisions {

    void verifyLRVDecisions(JSONArray decisions, Boolean tax, boolean YC ) {
        def expectedDecisions
        if(YC==true)
        {
        expectedDecisions = '[{occupancyDate:2009-09-09,roomClass:STANDARD,opportunityCost:443.919985,deltaValue:0,ceilingValue:0,maxSolds:11},' +
                    '{occupancyDate:2009-09-09,roomClass:CITY,opportunityCost:0,deltaValue:0,ceilingValue:1,maxSolds:11},' +
                    '{occupancyDate:2009-09-10,roomClass:STANDARD,opportunityCost:511.315165,deltaValue:0,ceilingValue:0,maxSolds:11},' +
                    '{occupancyDate:2009-09-10,roomClass:CITY,opportunityCost:0,deltaValue:0,ceilingValue:0,maxSolds:11},' +
                    '{occupancyDate:2009-09-11,roomClass:STANDARD,opportunityCost:0.011285,deltaValue:0.239765,ceilingValue:9,maxSolds:29},' +
                    '{occupancyDate:2009-09-11,roomClass:CITY,opportunityCost:0,deltaValue:0.608385,ceilingValue:13,maxSolds:29},' +
                    '{occupancyDate:2009-09-12,roomClass:STANDARD,opportunityCost:506.62283,deltaValue:19.16741,ceilingValue:2,maxSolds:11},' +
                    '{occupancyDate:2009-09-12,roomClass:CITY,opportunityCost:5.63502,deltaValue:0,ceilingValue:0,maxSolds:11},' +
                    '{occupancyDate:2009-09-13,roomClass:STANDARD,opportunityCost:770.471065,deltaValue:0,ceilingValue:5,maxSolds:11},' +
                    '{occupancyDate:2009-09-13,roomClass:CITY,opportunityCost:722.258515,deltaValue:0,ceilingValue:0,maxSolds:11},' +
                    '{occupancyDate:2009-09-14,roomClass:STANDARD,opportunityCost:0,deltaValue:2.868885,ceilingValue:102,maxSolds:124},' +
                    '{occupancyDate:2009-09-14,roomClass:CITY,opportunityCost:0,deltaValue:0,ceilingValue:13,maxSolds:124},' +
                    '{occupancyDate:2009-09-15,roomClass:STANDARD,opportunityCost:0,deltaValue:0,ceilingValue:26,maxSolds:33},' +
                    '{occupancyDate:2009-09-15,roomClass:CITY,opportunityCost:0,deltaValue:0.037215,ceilingValue:3,maxSolds:33},' +
                    '{occupancyDate:2009-09-16,roomClass:STANDARD,opportunityCost:949.748195,deltaValue:0,ceilingValue:0,maxSolds:11},' +
                    '{occupancyDate:2009-09-16,roomClass:CITY,opportunityCost:949.525065,deltaValue:0,ceilingValue:1,maxSolds:11},' +
                    '{occupancyDate:2009-09-17,roomClass:STANDARD,opportunityCost:178.033155,deltaValue:31.23528,ceilingValue:9,maxSolds:21},' +
                    '{occupancyDate:2009-09-17,roomClass:CITY,opportunityCost:0,deltaValue:0,ceilingValue:11,maxSolds:21},' +
                    '{occupancyDate:2009-09-18,roomClass:STANDARD,opportunityCost:0,deltaValue:0,ceilingValue:72,maxSolds:94},' +
                    '{occupancyDate:2009-09-18,roomClass:CITY,opportunityCost:0,deltaValue:0,ceilingValue:15,maxSolds:94}]'
       } else
        if(tax==true){
       expectedDecisions = '[{occupancyDate:2009-09-09,roomClass:STANDARD,opportunityCost:994.380766,deltaValue:0,ceilingValue:0,maxSolds:11},' +
                '{occupancyDate:2009-09-09,roomClass:CITY,opportunityCost:0,deltaValue:0,ceilingValue:1,maxSolds:11},' +
                '{occupancyDate:2009-09-10,roomClass:STANDARD,opportunityCost:1145.34597,deltaValue:0,ceilingValue:0,maxSolds:11},' +
                '{occupancyDate:2009-09-10,roomClass:CITY,opportunityCost:0,deltaValue:0,ceilingValue:0,maxSolds:11},' +
                '{occupancyDate:2009-09-11,roomClass:STANDARD,opportunityCost:0.025278,deltaValue:0.537074,ceilingValue:9,maxSolds:29},' +
                '{occupancyDate:2009-09-11,roomClass:CITY,opportunityCost:0,deltaValue:1.362782,ceilingValue:13,maxSolds:29},' +
                '{occupancyDate:2009-09-12,roomClass:STANDARD,opportunityCost:1134.835139,deltaValue:42.934998,ceilingValue:2,maxSolds:11},' +
                '{occupancyDate:2009-09-12,roomClass:CITY,opportunityCost:12.622445,deltaValue:0,ceilingValue:0,maxSolds:11},' +
                '{occupancyDate:2009-09-13,roomClass:STANDARD,opportunityCost:1725.855186,deltaValue:0,ceilingValue:5,maxSolds:11},' +
                '{occupancyDate:2009-09-13,roomClass:CITY,opportunityCost:1617.859074,deltaValue:0,ceilingValue:0,maxSolds:11},' +
                '{occupancyDate:2009-09-14,roomClass:STANDARD,opportunityCost:0,deltaValue:6.426302,ceilingValue:102,maxSolds:124},' +
                '{occupancyDate:2009-09-14,roomClass:CITY,opportunityCost:0,deltaValue:0,ceilingValue:13,maxSolds:124},' +
                '{occupancyDate:2009-09-15,roomClass:STANDARD,opportunityCost:0,deltaValue:0,ceilingValue:26,maxSolds:33},' +
                '{occupancyDate:2009-09-15,roomClass:CITY,opportunityCost:0,deltaValue:0.083362,ceilingValue:3,maxSolds:33},' +
                '{occupancyDate:2009-09-16,roomClass:STANDARD,opportunityCost:2127.435957,deltaValue:0,ceilingValue:0,maxSolds:11},' +
                '{occupancyDate:2009-09-16,roomClass:CITY,opportunityCost:2126.936146,deltaValue:0,ceilingValue:1,maxSolds:11},' +
                '{occupancyDate:2009-09-17,roomClass:STANDARD,opportunityCost:398.794267,deltaValue:69.967027,ceilingValue:9,maxSolds:21},' +
                '{occupancyDate:2009-09-17,roomClass:CITY,opportunityCost:0,deltaValue:0,ceilingValue:11,maxSolds:21},' +
                '{occupancyDate:2009-09-18,roomClass:STANDARD,opportunityCost:0,deltaValue:0,ceilingValue:72,maxSolds:94},' +
                '{occupancyDate:2009-09-18,roomClass:CITY,opportunityCost:0,deltaValue:0,ceilingValue:15,maxSolds:94}]'
        }
        else {
        expectedDecisions = '[{occupancyDate:2009-09-09,roomClass:STANDARD,opportunityCost:887.83997,deltaValue:0,ceilingValue:0,maxSolds:11},' +
                    '{occupancyDate:2009-09-09,roomClass:CITY,opportunityCost:0,deltaValue:0,ceilingValue:1,maxSolds:11},' +
                    '{occupancyDate:2009-09-10,roomClass:STANDARD,opportunityCost:1022.63033,deltaValue:0,ceilingValue:0,maxSolds:11},' +
                    '{occupancyDate:2009-09-10,roomClass:CITY,opportunityCost:0,deltaValue:0,ceilingValue:0,maxSolds:11},' +
                    '{occupancyDate:2009-09-11,roomClass:STANDARD,opportunityCost:0.02257,deltaValue:0.47953,ceilingValue:9,maxSolds:29},' +
                    '{occupancyDate:2009-09-11,roomClass:CITY,opportunityCost:0,deltaValue:1.21677,ceilingValue:13,maxSolds:29},' +
                    '{occupancyDate:2009-09-12,roomClass:STANDARD,opportunityCost:1013.24566,deltaValue:38.33482,ceilingValue:2,maxSolds:11},' +
                    '{occupancyDate:2009-09-12,roomClass:CITY,opportunityCost:11.27004,deltaValue:0,ceilingValue:0,maxSolds:11},' +
                    '{occupancyDate:2009-09-13,roomClass:STANDARD,opportunityCost:1540.94213,deltaValue:0,ceilingValue:5,maxSolds:11},' +
                    '{occupancyDate:2009-09-13,roomClass:CITY,opportunityCost:1444.51703,deltaValue:0,ceilingValue:0,maxSolds:11},' +
                    '{occupancyDate:2009-09-14,roomClass:STANDARD,opportunityCost:0,deltaValue:5.73777,ceilingValue:102,maxSolds:124},' +
                    '{occupancyDate:2009-09-14,roomClass:CITY,opportunityCost:0,deltaValue:0,ceilingValue:13,maxSolds:124},' +
                    '{occupancyDate:2009-09-15,roomClass:STANDARD,opportunityCost:0,deltaValue:0,ceilingValue:26,maxSolds:33},' +
                    '{occupancyDate:2009-09-15,roomClass:CITY,opportunityCost:0,deltaValue:0.07443,ceilingValue:3,maxSolds:33},' +
                    '{occupancyDate:2009-09-16,roomClass:STANDARD,opportunityCost:1899.49639,deltaValue:0,ceilingValue:0,maxSolds:11},' +
                    '{occupancyDate:2009-09-16,roomClass:CITY,opportunityCost:1899.05013,deltaValue:0,ceilingValue:1,maxSolds:11},' +
                    '{occupancyDate:2009-09-17,roomClass:STANDARD,opportunityCost:356.06631,deltaValue:62.47056,ceilingValue:9,maxSolds:21},' +
                    '{occupancyDate:2009-09-17,roomClass:CITY,opportunityCost:0,deltaValue:0,ceilingValue:11,maxSolds:21},' +
                    '{occupancyDate:2009-09-18,roomClass:STANDARD,opportunityCost:0,deltaValue:0,ceilingValue:72,maxSolds:94},' +
                    '{occupancyDate:2009-09-18,roomClass:CITY,opportunityCost:0,deltaValue:0,ceilingValue:15,maxSolds:94}]'
        }

        def actualDecisions = decisions.toString().replace('"', '')
        assert actualDecisions.equals(expectedDecisions)
    }


    void verifyBARFPLOSByRoomTypeDecisions(JSONArray decisions, String uploadType){
        def expectedDecisions
        if(uploadType=='full'){
            expectedDecisions = 	'[{arrivalDate:2009-09-10,rateCode:LV0,roomType:A1B,fplos:YNYYNYY,roomClass:null},' +
                    '{arrivalDate:2009-09-10,rateCode:LV1,roomType:A1B,fplos:YYYYYYY,roomClass:null},' +
                    '{arrivalDate:2009-09-10,rateCode:LV2,roomType:A1B,fplos:YNYYNNY,roomClass:null},' +
                    '{arrivalDate:2009-09-10,rateCode:LV0,roomType:A2B,fplos:YNYYNYY,roomClass:null},' +
                    '{arrivalDate:2009-09-10,rateCode:LV1,roomType:A2B,fplos:YYYYYYY,roomClass:null},' +
                    '{arrivalDate:2009-09-10,rateCode:LV2,roomType:A2B,fplos:YNYYNNY,roomClass:null},' +
                    '{arrivalDate:2009-09-10,rateCode:LV0,roomType:A3B,fplos:YNYYNYY,roomClass:null},' +
                    '{arrivalDate:2009-09-10,rateCode:LV1,roomType:A3B,fplos:YYYYYYY,roomClass:null},' +
                    '{arrivalDate:2009-09-10,rateCode:LV2,roomType:A3B,fplos:YYYYYYY,roomClass:null},' +
                    '{arrivalDate:2009-09-10,rateCode:LV0,roomType:PH,fplos:YNYYNYY,roomClass:null},' +
                    '{arrivalDate:2009-09-10,rateCode:LV1,roomType:PH,fplos:YYYYYYY,roomClass:null},' +
                    '{arrivalDate:2009-09-10,rateCode:LV2,roomType:PH,fplos:YYYYNNY,roomClass:null}]'
        }
        else {
            expectedDecisions = '[{arrivalDate:2009-09-10,rateCode:LV2,roomType:A3B,fplos:YYYYYYY,roomClass:null}]'
        }
        def actualDecisions = decisions.toString().replace('"', '')
        assert actualDecisions.equals(expectedDecisions)
    }

    void verifyBARFPLOSByHotelDecisions(JSONArray decisions, String uploadType){
        def expectedDecisions
        if(uploadType=='full'){
            expectedDecisions = 	'[{arrivalDate:2009-09-10,rateCode:LV0,roomType:A3B,fplos:YNYYNYY,roomClass:null},' +
                    '{arrivalDate:2009-09-10,rateCode:LV1,roomType:A3B,fplos:YYYYYYY,roomClass:null},' +
                    '{arrivalDate:2009-09-10,rateCode:LV2,roomType:A3B,fplos:YYYYYYY,roomClass:null}]'
        }
        else {
            expectedDecisions = '[{arrivalDate:2009-09-10,rateCode:LV2,roomType:A3B,fplos:YYYYYYY,roomClass:null}]'
        }
        def actualDecisions = decisions.toString().replace('"', '')
        assert actualDecisions.equals(expectedDecisions)
    }

    void verifyBARFPLOSByHierarchyDecisions(JSONArray decisions, String uploadType){
        def expectedDecisions
        if(uploadType=='full'){
            expectedDecisions = '[{arrivalDate:2009-09-10,rateCode:LV0,roomType:A3B,fplos:NNYYNYN,roomClass:null},'+
            '{arrivalDate:2009-09-10,rateCode:LV1,roomType:A3B,fplos:NNNNNNN,roomClass:null},'+
            '{arrivalDate:2009-09-10,rateCode:LV2,roomType:A3B,fplos:NNYYNNN,roomClass:null},'+
            '{arrivalDate:2009-09-10,rateCode:LV0,roomType:A2B,fplos:NNYYNYN,roomClass:null},'+
            '{arrivalDate:2009-09-10,rateCode:LV1,roomType:A2B,fplos:NYYYYYN,roomClass:null},'+
            '{arrivalDate:2009-09-10,rateCode:LV2,roomType:A2B,fplos:NNYYNNN,roomClass:null},'+
            '{arrivalDate:2009-09-10,rateCode:LV0,roomType:A1B,fplos:NNYYNYN,roomClass:null},'+
            '{arrivalDate:2009-09-10,rateCode:LV1,roomType:A1B,fplos:NYYYYYN,roomClass:null},'+
            '{arrivalDate:2009-09-10,rateCode:LV2,roomType:A1B,fplos:NNYYNNN,roomClass:null},'+
            '{arrivalDate:2009-09-10,rateCode:LV0,roomType:PH,fplos:NNYYNYN,roomClass:null},'+
            '{arrivalDate:2009-09-10,rateCode:LV1,roomType:PH,fplos:NYYYYYN,roomClass:null},'+
            '{arrivalDate:2009-09-10,rateCode:LV2,roomType:PH,fplos:NNYYNNN,roomClass:null}]'
        }
        else {
            expectedDecisions = '[{arrivalDate:2009-09-10,rateCode:LV1,roomType:A3B,fplos:NNNNNNN,roomClass:null}]'
        }
        def actualDecisions = decisions.toString().replace('"', '')
        assert actualDecisions.equals(expectedDecisions)
    }

    void verifyBARByLosDecisions(JSONArray decisions, String uploadType){
        def expectedDecisions
        if(uploadType=='full'){
            expectedDecisions = '[{rateCode:RACK,rateDate:2009-09-19,los:1},'+
            '{rateCode:RACK,rateDate:2009-09-19,los:2},'+
            '{rateCode:RACK,rateDate:2009-09-19,los:3},'+
            '{rateCode:RACK,rateDate:2009-09-19,los:4},'+
            '{rateCode:RACK,rateDate:2009-09-19,los:5},'+
            '{rateCode:RACK,rateDate:2009-09-19,los:6},'+
            '{rateCode:RACK,rateDate:2009-09-19,los:7},'+
            '{rateCode:RACK,rateDate:2009-09-20,los:1},'+
            '{rateCode:RACK,rateDate:2009-09-20,los:2},'+
            '{rateCode:RACK,rateDate:2009-09-20,los:3},'+
            '{rateCode:RACK,rateDate:2009-09-20,los:4},'+
            '{rateCode:RACK,rateDate:2009-09-20,los:5},'+
            '{rateCode:RACK,rateDate:2009-09-20,los:6},'+
            '{rateCode:RACK,rateDate:2009-09-20,los:7}]'
        }
        else {
            expectedDecisions = '[{rateCode:RACK,rateDate:2009-09-20,los:1},'+
                    '{rateCode:RACK,rateDate:2009-09-20,los:2},'+
                    '{rateCode:RACK,rateDate:2009-09-20,los:3},'+
                    '{rateCode:RACK,rateDate:2009-09-20,los:4},'+
                    '{rateCode:RACK,rateDate:2009-09-20,los:5},'+
                    '{rateCode:RACK,rateDate:2009-09-20,los:6},'+
                    '{rateCode:RACK,rateDate:2009-09-20,los:7}]'
        }
        def actualDecisions = decisions.toString().replace('"', '')
        assert actualDecisions.equals(expectedDecisions)
    }
    void verifyDailyBARDecisionsYC(JSONArray decisions, boolean YC ,boolean  uploadChildAgeBucket) {
        if (YC == false && uploadChildAgeBucket == false) {
            def expectedDecisions = '[{rateCode:RACK,roomType:A1B,rateDate:2009-09-09,oneAdultRate:0,twoAdultRate:0,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-09,oneAdultRate:570.2,twoAdultRate:620.2,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-09,oneAdultRate:617.06,twoAdultRate:667.06,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-10,oneAdultRate:0,twoAdultRate:50,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-10,oneAdultRate:165.385,twoAdultRate:330.77,extraAdultRate:50,extraChildRate:330.77,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-10,oneAdultRate:165,twoAdultRate:215,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-11,oneAdultRate:400,twoAdultRate:400,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-11,oneAdultRate:150.05,twoAdultRate:300.1,extraAdultRate:50,extraChildRate:300.1,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-11,oneAdultRate:150,twoAdultRate:172.68,extraAdultRate:120.875,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-12,oneAdultRate:555,twoAdultRate:555,extraAdultRate:55,extraChildRate:55,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-12,oneAdultRate:157.6,twoAdultRate:330.96,extraAdultRate:55,extraChildRate:364.055,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-12,oneAdultRate:157.5,twoAdultRate:181.315,extraAdultRate:90.655,extraChildRate:55,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-13,oneAdultRate:560.39,twoAdultRate:560.39,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-13,oneAdultRate:231.33,twoAdultRate:508.925,extraAdultRate:712.495,extraChildRate:712.495,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-13,oneAdultRate:165,twoAdultRate:165,extraAdultRate:49.5,extraChildRate:70,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-14,oneAdultRate:617.06,twoAdultRate:617.06,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-14,oneAdultRate:267.25,twoAdultRate:403.875,extraAdultRate:686.585,extraChildRate:80.775,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-14,oneAdultRate:172,twoAdultRate:163.67,extraAdultRate:0,extraChildRate:85,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-15,oneAdultRate:570.2,twoAdultRate:570.2,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-15,oneAdultRate:36.85,twoAdultRate:44.22,extraAdultRate:8.845,extraChildRate:4.42,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-15,oneAdultRate:130.2,twoAdultRate:130.2,extraAdultRate:0,extraChildRate:70.2,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-16,oneAdultRate:34.05,twoAdultRate:39.405,extraAdultRate:4.19,extraChildRate:4.19,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-16,oneAdultRate:33.55,twoAdultRate:36.905,extraAdultRate:3.69,extraChildRate:3.69,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-16,oneAdultRate:0,twoAdultRate:0,extraAdultRate:0,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-17,oneAdultRate:0,twoAdultRate:0,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-17,oneAdultRate:103,twoAdultRate:206,extraAdultRate:206,extraChildRate:206,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-17,oneAdultRate:65,twoAdultRate:71.58,extraAdultRate:7.245,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null}]'
            def actualDecisions = decisions.toString().replace('"', '')
            assert actualDecisions.equals(expectedDecisions)
        }
        else
        if (YC == true && uploadChildAgeBucket == false)
        {
            def expectedDecisions = '[{rateCode:RACK,roomType:A1B,rateDate:2009-09-09,oneAdultRate:0,twoAdultRate:0,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-09,oneAdultRate:1140.4,twoAdultRate:1240.4,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-09,oneAdultRate:1234.12,twoAdultRate:1334.12,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-10,oneAdultRate:0,twoAdultRate:100,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-10,oneAdultRate:330.77,twoAdultRate:661.54,extraAdultRate:100,extraChildRate:661.54,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-10,oneAdultRate:330,twoAdultRate:430,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-11,oneAdultRate:800,twoAdultRate:800,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-11,oneAdultRate:300.1,twoAdultRate:600.2,extraAdultRate:100,extraChildRate:600.2,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-11,oneAdultRate:300,twoAdultRate:345.36,extraAdultRate:241.75,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-12,oneAdultRate:1110,twoAdultRate:1110,extraAdultRate:110,extraChildRate:110,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-12,oneAdultRate:315.2,twoAdultRate:661.92,extraAdultRate:110,extraChildRate:728.11,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-12,oneAdultRate:315,twoAdultRate:362.63,extraAdultRate:181.31,extraChildRate:110,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-13,oneAdultRate:1120.78,twoAdultRate:1120.78,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-13,oneAdultRate:462.66,twoAdultRate:1017.85,extraAdultRate:1424.99,extraChildRate:1424.99,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-13,oneAdultRate:330,twoAdultRate:330,extraAdultRate:99,extraChildRate:140,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-14,oneAdultRate:1234.12,twoAdultRate:1234.12,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-14,oneAdultRate:534.5,twoAdultRate:807.75,extraAdultRate:1373.17,extraChildRate:161.55,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-14,oneAdultRate:344,twoAdultRate:327.34,extraAdultRate:0,extraChildRate:170,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-15,oneAdultRate:1140.4,twoAdultRate:1140.4,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-15,oneAdultRate:73.7,twoAdultRate:88.44,extraAdultRate:17.69,extraChildRate:8.84,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-15,oneAdultRate:260.4,twoAdultRate:260.4,extraAdultRate:0,extraChildRate:140.4,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-16,oneAdultRate:68.1,twoAdultRate:78.81,extraAdultRate:8.38,extraChildRate:8.38,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-16,oneAdultRate:67.1,twoAdultRate:73.81,extraAdultRate:7.38,extraChildRate:7.38,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-16,oneAdultRate:0,twoAdultRate:0,extraAdultRate:0,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-17,oneAdultRate:0,twoAdultRate:0,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-17,oneAdultRate:206,twoAdultRate:412,extraAdultRate:412,extraChildRate:412,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-17,oneAdultRate:130,twoAdultRate:143.16,extraAdultRate:14.49,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null}]'
            def actualDecisions = decisions.toString().replace('"', '')
            assert actualDecisions.equals(expectedDecisions)
        }

        else if (YC == false && uploadChildAgeBucket == true) {
            def expectedDecisions = '[{rateCode:RACK,roomType:A1B,rateDate:2009-09-09,oneAdultRate:0,twoAdultRate:0,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-09,oneAdultRate:570.2,twoAdultRate:620.2,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-09,oneAdultRate:617.06,twoAdultRate:667.06,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-10,oneAdultRate:0,twoAdultRate:50,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-10,oneAdultRate:165.385,twoAdultRate:330.77,extraAdultRate:50,extraChildRate:330.77,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-10,oneAdultRate:165,twoAdultRate:215,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-11,oneAdultRate:400,twoAdultRate:400,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-11,oneAdultRate:150.05,twoAdultRate:300.1,extraAdultRate:50,extraChildRate:300.1,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-11,oneAdultRate:150,twoAdultRate:172.68,extraAdultRate:120.875,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-12,oneAdultRate:555,twoAdultRate:555,extraAdultRate:55,extraChildRate:55,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-12,oneAdultRate:157.6,twoAdultRate:330.96,extraAdultRate:55,extraChildRate:364.055,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-12,oneAdultRate:157.5,twoAdultRate:181.315,extraAdultRate:90.655,extraChildRate:55,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-13,oneAdultRate:560.39,twoAdultRate:560.39,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-13,oneAdultRate:231.33,twoAdultRate:508.925,extraAdultRate:712.495,extraChildRate:712.495,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-13,oneAdultRate:165,twoAdultRate:165,extraAdultRate:49.5,extraChildRate:70,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-14,oneAdultRate:617.06,twoAdultRate:617.06,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-14,oneAdultRate:267.25,twoAdultRate:403.875,extraAdultRate:686.585,extraChildRate:80.775,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-14,oneAdultRate:172,twoAdultRate:163.67,extraAdultRate:0,extraChildRate:85,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-15,oneAdultRate:570.2,twoAdultRate:570.2,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-15,oneAdultRate:36.85,twoAdultRate:44.22,extraAdultRate:8.845,extraChildRate:4.42,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-15,oneAdultRate:130.2,twoAdultRate:130.2,extraAdultRate:0,extraChildRate:70.2,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-16,oneAdultRate:34.05,twoAdultRate:39.405,extraAdultRate:4.19,extraChildRate:4.19,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-16,oneAdultRate:33.55,twoAdultRate:36.905,extraAdultRate:3.69,extraChildRate:3.69,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-16,oneAdultRate:0,twoAdultRate:0,extraAdultRate:0,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-17,oneAdultRate:0,twoAdultRate:0,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-17,oneAdultRate:103,twoAdultRate:206,extraAdultRate:206,extraChildRate:206,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-17,oneAdultRate:65,twoAdultRate:71.58,extraAdultRate:7.245,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null}]'
            def actualDecisions = decisions.toString().replace('"', '')
            assert actualDecisions.equals(expectedDecisions)
        }
        else
        if (YC == true && uploadChildAgeBucket == true)
        {
            def expectedDecisions = '[{rateCode:RACK,roomType:A1B,rateDate:2009-09-09,oneAdultRate:0,twoAdultRate:0,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-09,oneAdultRate:1140.4,twoAdultRate:1240.4,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-09,oneAdultRate:1234.12,twoAdultRate:1334.12,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-10,oneAdultRate:0,twoAdultRate:100,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-10,oneAdultRate:330.77,twoAdultRate:661.54,extraAdultRate:100,extraChildRate:661.54,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-10,oneAdultRate:330,twoAdultRate:430,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-11,oneAdultRate:800,twoAdultRate:800,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-11,oneAdultRate:300.1,twoAdultRate:600.2,extraAdultRate:100,extraChildRate:600.2,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-11,oneAdultRate:300,twoAdultRate:345.36,extraAdultRate:241.75,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-12,oneAdultRate:1110,twoAdultRate:1110,extraAdultRate:110,extraChildRate:110,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-12,oneAdultRate:315.2,twoAdultRate:661.92,extraAdultRate:110,extraChildRate:728.11,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-12,oneAdultRate:315,twoAdultRate:362.63,extraAdultRate:181.31,extraChildRate:110,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-13,oneAdultRate:1120.78,twoAdultRate:1120.78,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-13,oneAdultRate:462.66,twoAdultRate:1017.85,extraAdultRate:1424.99,extraChildRate:1424.99,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-13,oneAdultRate:330,twoAdultRate:330,extraAdultRate:99,extraChildRate:140,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-14,oneAdultRate:1234.12,twoAdultRate:1234.12,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-14,oneAdultRate:534.5,twoAdultRate:807.75,extraAdultRate:1373.17,extraChildRate:161.55,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-14,oneAdultRate:344,twoAdultRate:327.34,extraAdultRate:0,extraChildRate:170,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-15,oneAdultRate:1140.4,twoAdultRate:1140.4,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-15,oneAdultRate:73.7,twoAdultRate:88.44,extraAdultRate:17.69,extraChildRate:8.84,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-15,oneAdultRate:260.4,twoAdultRate:260.4,extraAdultRate:0,extraChildRate:140.4,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-16,oneAdultRate:68.1,twoAdultRate:78.81,extraAdultRate:8.38,extraChildRate:8.38,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-16,oneAdultRate:67.1,twoAdultRate:73.81,extraAdultRate:7.38,extraChildRate:7.38,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-16,oneAdultRate:0,twoAdultRate:0,extraAdultRate:0,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-17,oneAdultRate:0,twoAdultRate:0,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-17,oneAdultRate:206,twoAdultRate:412,extraAdultRate:412,extraChildRate:412,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-17,oneAdultRate:130,twoAdultRate:143.16,extraAdultRate:14.49,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null}]'
            def actualDecisions = decisions.toString().replace('"', '')
            assert actualDecisions.equals(expectedDecisions)
        }
    }

    void verifyAgileRateDecisionsYC(JSONArray decisions, boolean YC ,boolean  uploadChildAgeBucket) {
        if (YC == false && uploadChildAgeBucket == false) {
            def expectedDecisions = '[{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-09,oneAdultRate:0,twoAdultRate:0,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-09,oneAdultRate:570.2,twoAdultRate:620.2,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-09,oneAdultRate:617.06,twoAdultRate:667.06,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-10,oneAdultRate:0,twoAdultRate:50,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-10,oneAdultRate:165.385,twoAdultRate:330.77,extraAdultRate:50,extraChildRate:330.77,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-10,oneAdultRate:165,twoAdultRate:215,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-11,oneAdultRate:400,twoAdultRate:400,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-11,oneAdultRate:150.05,twoAdultRate:300.1,extraAdultRate:50,extraChildRate:300.1,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-11,oneAdultRate:150,twoAdultRate:172.68,extraAdultRate:120.875,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-12,oneAdultRate:555,twoAdultRate:555,extraAdultRate:55,extraChildRate:55,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-12,oneAdultRate:157.6,twoAdultRate:330.96,extraAdultRate:55,extraChildRate:364.055,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-12,oneAdultRate:157.5,twoAdultRate:181.315,extraAdultRate:90.655,extraChildRate:55,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-13,oneAdultRate:560.39,twoAdultRate:560.39,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-13,oneAdultRate:231.33,twoAdultRate:508.925,extraAdultRate:712.495,extraChildRate:712.495,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-13,oneAdultRate:165,twoAdultRate:165,extraAdultRate:49.5,extraChildRate:70,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-14,oneAdultRate:617.06,twoAdultRate:617.06,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-14,oneAdultRate:267.25,twoAdultRate:403.875,extraAdultRate:686.585,extraChildRate:80.775,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-14,oneAdultRate:172,twoAdultRate:163.67,extraAdultRate:0,extraChildRate:85,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-15,oneAdultRate:570.2,twoAdultRate:570.2,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-15,oneAdultRate:36.85,twoAdultRate:44.22,extraAdultRate:8.845,extraChildRate:4.42,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-15,oneAdultRate:130.2,twoAdultRate:130.2,extraAdultRate:0,extraChildRate:70.2,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-16,oneAdultRate:34.05,twoAdultRate:39.405,extraAdultRate:4.19,extraChildRate:4.19,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-16,oneAdultRate:33.55,twoAdultRate:36.905,extraAdultRate:3.69,extraChildRate:3.69,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-16,oneAdultRate:0,twoAdultRate:0,extraAdultRate:0,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-17,oneAdultRate:0,twoAdultRate:0,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-17,oneAdultRate:103,twoAdultRate:206,extraAdultRate:206,extraChildRate:206,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-17,oneAdultRate:65,twoAdultRate:71.58,extraAdultRate:7.245,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null}]'
            def actualDecisions = decisions.toString().replace('"', '')
            assert actualDecisions.equals(expectedDecisions)
        }
        else
        if (YC == true && uploadChildAgeBucket == false)
        {
            def expectedDecisions = '[{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-09,oneAdultRate:0,twoAdultRate:0,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-09,oneAdultRate:1140.4,twoAdultRate:1240.4,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-09,oneAdultRate:1234.12,twoAdultRate:1334.12,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-10,oneAdultRate:0,twoAdultRate:100,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-10,oneAdultRate:330.77,twoAdultRate:661.54,extraAdultRate:100,extraChildRate:661.54,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-10,oneAdultRate:330,twoAdultRate:430,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-11,oneAdultRate:800,twoAdultRate:800,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-11,oneAdultRate:300.1,twoAdultRate:600.2,extraAdultRate:100,extraChildRate:600.2,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-11,oneAdultRate:300,twoAdultRate:345.36,extraAdultRate:241.75,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-12,oneAdultRate:1110,twoAdultRate:1110,extraAdultRate:110,extraChildRate:110,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-12,oneAdultRate:315.2,twoAdultRate:661.92,extraAdultRate:110,extraChildRate:728.11,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-12,oneAdultRate:315,twoAdultRate:362.63,extraAdultRate:181.31,extraChildRate:110,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-13,oneAdultRate:1120.78,twoAdultRate:1120.78,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-13,oneAdultRate:462.66,twoAdultRate:1017.85,extraAdultRate:1424.99,extraChildRate:1424.99,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-13,oneAdultRate:330,twoAdultRate:330,extraAdultRate:99,extraChildRate:140,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-14,oneAdultRate:1234.12,twoAdultRate:1234.12,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-14,oneAdultRate:534.5,twoAdultRate:807.75,extraAdultRate:1373.17,extraChildRate:161.55,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-14,oneAdultRate:344,twoAdultRate:327.34,extraAdultRate:0,extraChildRate:170,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-15,oneAdultRate:1140.4,twoAdultRate:1140.4,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-15,oneAdultRate:73.7,twoAdultRate:88.44,extraAdultRate:17.69,extraChildRate:8.84,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-15,oneAdultRate:260.4,twoAdultRate:260.4,extraAdultRate:0,extraChildRate:140.4,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-16,oneAdultRate:68.1,twoAdultRate:78.81,extraAdultRate:8.38,extraChildRate:8.38,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-16,oneAdultRate:67.1,twoAdultRate:73.81,extraAdultRate:7.38,extraChildRate:7.38,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-16,oneAdultRate:0,twoAdultRate:0,extraAdultRate:0,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-17,oneAdultRate:0,twoAdultRate:0,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-17,oneAdultRate:206,twoAdultRate:412,extraAdultRate:412,extraChildRate:412,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-17,oneAdultRate:130,twoAdultRate:143.16,extraAdultRate:14.49,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null}]'
            def actualDecisions = decisions.toString().replace('"', '')
            assert actualDecisions.equals(expectedDecisions)
        }

        else if (YC == false && uploadChildAgeBucket == true) {
            def expectedDecisions = '[{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-09,oneAdultRate:0,twoAdultRate:0,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-09,oneAdultRate:570.2,twoAdultRate:620.2,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-09,oneAdultRate:617.06,twoAdultRate:667.06,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-10,oneAdultRate:0,twoAdultRate:50,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-10,oneAdultRate:165.385,twoAdultRate:330.77,extraAdultRate:50,extraChildRate:330.77,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-10,oneAdultRate:165,twoAdultRate:215,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-11,oneAdultRate:400,twoAdultRate:400,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-11,oneAdultRate:150.05,twoAdultRate:300.1,extraAdultRate:50,extraChildRate:300.1,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-11,oneAdultRate:150,twoAdultRate:172.68,extraAdultRate:120.875,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-12,oneAdultRate:555,twoAdultRate:555,extraAdultRate:55,extraChildRate:55,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-12,oneAdultRate:157.6,twoAdultRate:330.96,extraAdultRate:55,extraChildRate:364.055,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-12,oneAdultRate:157.5,twoAdultRate:181.315,extraAdultRate:90.655,extraChildRate:55,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-13,oneAdultRate:560.39,twoAdultRate:560.39,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-13,oneAdultRate:231.33,twoAdultRate:508.925,extraAdultRate:712.495,extraChildRate:712.495,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-13,oneAdultRate:165,twoAdultRate:165,extraAdultRate:49.5,extraChildRate:70,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-14,oneAdultRate:617.06,twoAdultRate:617.06,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-14,oneAdultRate:267.25,twoAdultRate:403.875,extraAdultRate:686.585,extraChildRate:80.775,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-14,oneAdultRate:172,twoAdultRate:163.67,extraAdultRate:0,extraChildRate:85,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-15,oneAdultRate:570.2,twoAdultRate:570.2,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-15,oneAdultRate:36.85,twoAdultRate:44.22,extraAdultRate:8.845,extraChildRate:4.42,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-15,oneAdultRate:130.2,twoAdultRate:130.2,extraAdultRate:0,extraChildRate:70.2,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-16,oneAdultRate:34.05,twoAdultRate:39.405,extraAdultRate:4.19,extraChildRate:4.19,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-16,oneAdultRate:33.55,twoAdultRate:36.905,extraAdultRate:3.69,extraChildRate:3.69,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-16,oneAdultRate:0,twoAdultRate:0,extraAdultRate:0,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-17,oneAdultRate:0,twoAdultRate:0,extraAdultRate:50,extraChildRate:50,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-17,oneAdultRate:103,twoAdultRate:206,extraAdultRate:206,extraChildRate:206,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-17,oneAdultRate:65,twoAdultRate:71.58,extraAdultRate:7.245,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null}]'
            def actualDecisions = decisions.toString().replace('"', '')
            assert actualDecisions.equals(expectedDecisions)
        }
        else
        if (YC == true && uploadChildAgeBucket == true)
        {
            def expectedDecisions = '[{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-09,oneAdultRate:0,twoAdultRate:0,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-09,oneAdultRate:1140.4,twoAdultRate:1240.4,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-09,oneAdultRate:1234.12,twoAdultRate:1334.12,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-10,oneAdultRate:0,twoAdultRate:100,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-10,oneAdultRate:330.77,twoAdultRate:661.54,extraAdultRate:100,extraChildRate:661.54,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-10,oneAdultRate:330,twoAdultRate:430,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-11,oneAdultRate:800,twoAdultRate:800,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-11,oneAdultRate:300.1,twoAdultRate:600.2,extraAdultRate:100,extraChildRate:600.2,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-11,oneAdultRate:300,twoAdultRate:345.36,extraAdultRate:241.75,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-12,oneAdultRate:1110,twoAdultRate:1110,extraAdultRate:110,extraChildRate:110,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-12,oneAdultRate:315.2,twoAdultRate:661.92,extraAdultRate:110,extraChildRate:728.11,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-12,oneAdultRate:315,twoAdultRate:362.63,extraAdultRate:181.31,extraChildRate:110,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-13,oneAdultRate:1120.78,twoAdultRate:1120.78,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-13,oneAdultRate:462.66,twoAdultRate:1017.85,extraAdultRate:1424.99,extraChildRate:1424.99,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-13,oneAdultRate:330,twoAdultRate:330,extraAdultRate:99,extraChildRate:140,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-14,oneAdultRate:1234.12,twoAdultRate:1234.12,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-14,oneAdultRate:534.5,twoAdultRate:807.75,extraAdultRate:1373.17,extraChildRate:161.55,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-14,oneAdultRate:344,twoAdultRate:327.34,extraAdultRate:0,extraChildRate:170,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-15,oneAdultRate:1140.4,twoAdultRate:1140.4,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-15,oneAdultRate:73.7,twoAdultRate:88.44,extraAdultRate:17.69,extraChildRate:8.84,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-15,oneAdultRate:260.4,twoAdultRate:260.4,extraAdultRate:0,extraChildRate:140.4,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-16,oneAdultRate:68.1,twoAdultRate:78.81,extraAdultRate:8.38,extraChildRate:8.38,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-16,oneAdultRate:67.1,twoAdultRate:73.81,extraAdultRate:7.38,extraChildRate:7.38,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-16,oneAdultRate:0,twoAdultRate:0,extraAdultRate:0,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-17,oneAdultRate:0,twoAdultRate:0,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-17,oneAdultRate:206,twoAdultRate:412,extraAdultRate:412,extraChildRate:412,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-17,oneAdultRate:130,twoAdultRate:143.16,extraAdultRate:14.49,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null}]'
            def actualDecisions = decisions.toString().replace('"', '')
            assert actualDecisions.equals(expectedDecisions)
        }
    }

    void verifyDailyBARDecisions(JSONArray decisions, String uploadType,Boolean  applyTax,Boolean  miscAdjustment,Boolean uploadChildAgeBucket) {
        if(uploadType=='full' && miscAdjustment==true && applyTax==true && uploadChildAgeBucket==false ){
            def expectedDecisions = '[{rateCode:RACK,roomType:A1B,rateDate:2009-09-09,oneAdultRate:0,twoAdultRate:0,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-09,oneAdultRate:1288,twoAdultRate:1400,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-09,oneAdultRate:1393,twoAdultRate:1505,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-10,oneAdultRate:11,twoAdultRate:123,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-10,oneAdultRate:382,twoAdultRate:752,extraAdultRate:112,extraChildRate:741,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-10,oneAdultRate:381,twoAdultRate:493,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-11,oneAdultRate:907,twoAdultRate:907,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-11,oneAdultRate:347,twoAdultRate:683,extraAdultRate:112,extraChildRate:672,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-11,oneAdultRate:347,twoAdultRate:398,extraAdultRate:271,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-12,oneAdultRate:1254,twoAdultRate:1254,extraAdultRate:123,extraChildRate:123,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-12,oneAdultRate:364,twoAdultRate:753,extraAdultRate:123,extraChildRate:815,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-12,oneAdultRate:364,twoAdultRate:417,extraAdultRate:203,extraChildRate:123,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-13,oneAdultRate:1266,twoAdultRate:1266,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-13,oneAdultRate:529,twoAdultRate:1151,extraAdultRate:1596,extraChildRate:1596,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-13,oneAdultRate:381,twoAdultRate:381,extraAdultRate:111,extraChildRate:157,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-14,oneAdultRate:1393,twoAdultRate:1393,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-14,oneAdultRate:610,twoAdultRate:916,extraAdultRate:1538,extraChildRate:181,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-14,oneAdultRate:396,twoAdultRate:378,extraAdultRate:0,extraChildRate:190,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-15,oneAdultRate:1288,twoAdultRate:1288,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-15,oneAdultRate:94,twoAdultRate:110,extraAdultRate:20,extraChildRate:10,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-15,oneAdultRate:303,twoAdultRate:303,extraAdultRate:0,extraChildRate:157,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-16,oneAdultRate:87,twoAdultRate:99,extraAdultRate:9,extraChildRate:9,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-16,oneAdultRate:86,twoAdultRate:94,extraAdultRate:8,extraChildRate:8,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-16,oneAdultRate:0,twoAdultRate:0,extraAdultRate:0,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-17,oneAdultRate:11,twoAdultRate:11,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-17,oneAdultRate:242,twoAdultRate:473,extraAdultRate:461,extraChildRate:461,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-17,oneAdultRate:157,twoAdultRate:172,extraAdultRate:16,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null}]'
            def actualDecisions = decisions.toString().replace('"', '')
            assert actualDecisions.equals(expectedDecisions)
        }
        else if(uploadType=='full' && applyTax==true && miscAdjustment==false && uploadChildAgeBucket==false){
            def expectedDecisions = '[{rateCode:RACK,roomType:A1B,rateDate:2009-09-09,oneAdultRate:0,twoAdultRate:0,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-09,oneAdultRate:1277.25,twoAdultRate:1389.25,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-09,oneAdultRate:1382.21,twoAdultRate:1494.21,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-10,oneAdultRate:0,twoAdultRate:112,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-10,oneAdultRate:370.46,twoAdultRate:740.92,extraAdultRate:112,extraChildRate:740.92,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-10,oneAdultRate:369.6,twoAdultRate:481.6,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-11,oneAdultRate:896,twoAdultRate:896,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-11,oneAdultRate:336.11,twoAdultRate:672.22,extraAdultRate:112,extraChildRate:672.22,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-11,oneAdultRate:336,twoAdultRate:386.8,extraAdultRate:270.76,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-12,oneAdultRate:1243.2,twoAdultRate:1243.2,extraAdultRate:123.2,extraChildRate:123.2,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-12,oneAdultRate:353.02,twoAdultRate:741.35,extraAdultRate:123.2,extraChildRate:815.49,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-12,oneAdultRate:352.8,twoAdultRate:406.14,extraAdultRate:203.07,extraChildRate:123.2,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-13,oneAdultRate:1255.27,twoAdultRate:1255.27,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-13,oneAdultRate:518.18,twoAdultRate:1139.99,extraAdultRate:1595.99,extraChildRate:1595.99,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-13,oneAdultRate:369.6,twoAdultRate:369.6,extraAdultRate:110.88,extraChildRate:156.8,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-14,oneAdultRate:1382.21,twoAdultRate:1382.21,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-14,oneAdultRate:598.64,twoAdultRate:904.68,extraAdultRate:1537.96,extraChildRate:180.94,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-14,oneAdultRate:385.28,twoAdultRate:366.62,extraAdultRate:0,extraChildRate:190.4,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-15,oneAdultRate:1277.25,twoAdultRate:1277.25,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-15,oneAdultRate:82.54,twoAdultRate:99.05,extraAdultRate:19.81,extraChildRate:9.91,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-15,oneAdultRate:291.65,twoAdultRate:291.65,extraAdultRate:0,extraChildRate:157.25,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-16,oneAdultRate:76.27,twoAdultRate:88.27,extraAdultRate:9.39,extraChildRate:9.39,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-16,oneAdultRate:75.15,twoAdultRate:82.67,extraAdultRate:8.27,extraChildRate:8.27,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-16,oneAdultRate:0,twoAdultRate:0,extraAdultRate:0,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-17,oneAdultRate:0,twoAdultRate:0,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-17,oneAdultRate:230.72,twoAdultRate:461.44,extraAdultRate:461.44,extraChildRate:461.44,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-17,oneAdultRate:145.6,twoAdultRate:160.33,extraAdultRate:16.23,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null}]'
            def actualDecisions = decisions.toString().replace('"', '')
            assert actualDecisions.equals(expectedDecisions)
        }
        else if(uploadType=='full' && miscAdjustment==false && applyTax==false && uploadChildAgeBucket==false){
            def expectedDecisions = '[{rateCode:RACK,roomType:A1B,rateDate:2009-09-09,oneAdultRate:0,twoAdultRate:0,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-09,oneAdultRate:1140.4,twoAdultRate:1240.4,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-09,oneAdultRate:1234.12,twoAdultRate:1334.12,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-10,oneAdultRate:0,twoAdultRate:100,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-10,oneAdultRate:330.77,twoAdultRate:661.54,extraAdultRate:100,extraChildRate:661.54,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-10,oneAdultRate:330,twoAdultRate:430,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-11,oneAdultRate:800,twoAdultRate:800,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-11,oneAdultRate:300.1,twoAdultRate:600.2,extraAdultRate:100,extraChildRate:600.2,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-11,oneAdultRate:300,twoAdultRate:345.36,extraAdultRate:241.75,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-12,oneAdultRate:1110,twoAdultRate:1110,extraAdultRate:110,extraChildRate:110,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-12,oneAdultRate:315.2,twoAdultRate:661.92,extraAdultRate:110,extraChildRate:728.11,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-12,oneAdultRate:315,twoAdultRate:362.63,extraAdultRate:181.31,extraChildRate:110,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-13,oneAdultRate:1120.78,twoAdultRate:1120.78,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-13,oneAdultRate:462.66,twoAdultRate:1017.85,extraAdultRate:1424.99,extraChildRate:1424.99,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-13,oneAdultRate:330,twoAdultRate:330,extraAdultRate:99,extraChildRate:140,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-14,oneAdultRate:1234.12,twoAdultRate:1234.12,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-14,oneAdultRate:534.5,twoAdultRate:807.75,extraAdultRate:1373.17,extraChildRate:161.55,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-14,oneAdultRate:344,twoAdultRate:327.34,extraAdultRate:0,extraChildRate:170,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-15,oneAdultRate:1140.4,twoAdultRate:1140.4,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-15,oneAdultRate:73.7,twoAdultRate:88.44,extraAdultRate:17.69,extraChildRate:8.84,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-15,oneAdultRate:260.4,twoAdultRate:260.4,extraAdultRate:0,extraChildRate:140.4,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-16,oneAdultRate:68.1,twoAdultRate:78.81,extraAdultRate:8.38,extraChildRate:8.38,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-16,oneAdultRate:67.1,twoAdultRate:73.81,extraAdultRate:7.38,extraChildRate:7.38,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-16,oneAdultRate:0,twoAdultRate:0,extraAdultRate:0,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-17,oneAdultRate:0,twoAdultRate:0,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-17,oneAdultRate:206,twoAdultRate:412,extraAdultRate:412,extraChildRate:412,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-17,oneAdultRate:130,twoAdultRate:143.16,extraAdultRate:14.49,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null}]'
            def actualDecisions = decisions.toString().replace('"', '')
            assert actualDecisions.equals(expectedDecisions)
        }
        else if(uploadType=='full' && miscAdjustment==true && applyTax==true && uploadChildAgeBucket==true ){
            def expectedDecisions = '[{rateCode:RACK,roomType:A1B,rateDate:2009-09-09,oneAdultRate:0,twoAdultRate:0,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-09,oneAdultRate:1288,twoAdultRate:1400,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-09,oneAdultRate:1393,twoAdultRate:1505,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-10,oneAdultRate:11,twoAdultRate:123,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-10,oneAdultRate:382,twoAdultRate:752,extraAdultRate:112,extraChildRate:741,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-10,oneAdultRate:381,twoAdultRate:493,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-11,oneAdultRate:907,twoAdultRate:907,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-11,oneAdultRate:347,twoAdultRate:683,extraAdultRate:112,extraChildRate:672,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-11,oneAdultRate:347,twoAdultRate:398,extraAdultRate:271,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-12,oneAdultRate:1254,twoAdultRate:1254,extraAdultRate:123,extraChildRate:123,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-12,oneAdultRate:364,twoAdultRate:753,extraAdultRate:123,extraChildRate:815,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-12,oneAdultRate:364,twoAdultRate:417,extraAdultRate:203,extraChildRate:123,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-13,oneAdultRate:1266,twoAdultRate:1266,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-13,oneAdultRate:529,twoAdultRate:1151,extraAdultRate:1596,extraChildRate:1596,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-13,oneAdultRate:381,twoAdultRate:381,extraAdultRate:111,extraChildRate:157,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-14,oneAdultRate:1393,twoAdultRate:1393,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-14,oneAdultRate:610,twoAdultRate:916,extraAdultRate:1538,extraChildRate:181,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-14,oneAdultRate:396,twoAdultRate:378,extraAdultRate:0,extraChildRate:190,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-15,oneAdultRate:1288,twoAdultRate:1288,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-15,oneAdultRate:94,twoAdultRate:110,extraAdultRate:20,extraChildRate:10,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-15,oneAdultRate:303,twoAdultRate:303,extraAdultRate:0,extraChildRate:157,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-16,oneAdultRate:87,twoAdultRate:99,extraAdultRate:9,extraChildRate:9,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-16,oneAdultRate:86,twoAdultRate:94,extraAdultRate:8,extraChildRate:8,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-16,oneAdultRate:0,twoAdultRate:0,extraAdultRate:0,extraChildRate:0,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-17,oneAdultRate:11,twoAdultRate:11,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-17,oneAdultRate:242,twoAdultRate:473,extraAdultRate:461,extraChildRate:461,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-17,oneAdultRate:157,twoAdultRate:172,extraAdultRate:16,extraChildRate:0,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112}]'
            def actualDecisions = decisions.toString().replace('"', '')
            assert actualDecisions.equals(expectedDecisions)
        }
        else if(uploadType=='full' && applyTax==true && miscAdjustment==false && uploadChildAgeBucket==true){
            def expectedDecisions = '[{rateCode:RACK,roomType:A1B,rateDate:2009-09-09,oneAdultRate:0,twoAdultRate:0,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-09,oneAdultRate:1277.25,twoAdultRate:1389.25,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-09,oneAdultRate:1382.21,twoAdultRate:1494.21,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-10,oneAdultRate:0,twoAdultRate:112,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-10,oneAdultRate:370.46,twoAdultRate:740.92,extraAdultRate:112,extraChildRate:740.92,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-10,oneAdultRate:369.6,twoAdultRate:481.6,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-11,oneAdultRate:896,twoAdultRate:896,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-11,oneAdultRate:336.11,twoAdultRate:672.22,extraAdultRate:112,extraChildRate:672.22,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-11,oneAdultRate:336,twoAdultRate:386.8,extraAdultRate:270.76,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-12,oneAdultRate:1243.2,twoAdultRate:1243.2,extraAdultRate:123.2,extraChildRate:123.2,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-12,oneAdultRate:353.02,twoAdultRate:741.35,extraAdultRate:123.2,extraChildRate:815.49,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-12,oneAdultRate:352.8,twoAdultRate:406.14,extraAdultRate:203.07,extraChildRate:123.2,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-13,oneAdultRate:1255.27,twoAdultRate:1255.27,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-13,oneAdultRate:518.18,twoAdultRate:1139.99,extraAdultRate:1595.99,extraChildRate:1595.99,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-13,oneAdultRate:369.6,twoAdultRate:369.6,extraAdultRate:110.88,extraChildRate:156.8,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-14,oneAdultRate:1382.21,twoAdultRate:1382.21,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-14,oneAdultRate:598.64,twoAdultRate:904.68,extraAdultRate:1537.96,extraChildRate:180.94,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-14,oneAdultRate:385.28,twoAdultRate:366.62,extraAdultRate:0,extraChildRate:190.4,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-15,oneAdultRate:1277.25,twoAdultRate:1277.25,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-15,oneAdultRate:82.54,twoAdultRate:99.05,extraAdultRate:19.81,extraChildRate:9.91,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-15,oneAdultRate:291.65,twoAdultRate:291.65,extraAdultRate:0,extraChildRate:157.25,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-16,oneAdultRate:76.27,twoAdultRate:88.27,extraAdultRate:9.39,extraChildRate:9.39,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-16,oneAdultRate:75.15,twoAdultRate:82.67,extraAdultRate:8.27,extraChildRate:8.27,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-16,oneAdultRate:0,twoAdultRate:0,extraAdultRate:0,extraChildRate:0,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-17,oneAdultRate:0,twoAdultRate:0,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-17,oneAdultRate:230.72,twoAdultRate:461.44,extraAdultRate:461.44,extraChildRate:461.44,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-17,oneAdultRate:145.6,twoAdultRate:160.33,extraAdultRate:16.23,extraChildRate:0,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112}]'
            def actualDecisions = decisions.toString().replace('"', '')
            assert actualDecisions.equals(expectedDecisions)
        }
        else if(uploadType=='full' && miscAdjustment==false && applyTax==false && uploadChildAgeBucket==true){
            def expectedDecisions = '[{rateCode:RACK,roomType:A1B,rateDate:2009-09-09,oneAdultRate:0,twoAdultRate:0,extraAdultRate:100,extraChildRate:100,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-09,oneAdultRate:1140.4,twoAdultRate:1240.4,extraAdultRate:100,extraChildRate:100,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-09,oneAdultRate:1234.12,twoAdultRate:1334.12,extraAdultRate:100,extraChildRate:100,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-10,oneAdultRate:0,twoAdultRate:100,extraAdultRate:100,extraChildRate:100,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-10,oneAdultRate:330.77,twoAdultRate:661.54,extraAdultRate:100,extraChildRate:661.54,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-10,oneAdultRate:330,twoAdultRate:430,extraAdultRate:100,extraChildRate:100,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-11,oneAdultRate:800,twoAdultRate:800,extraAdultRate:100,extraChildRate:100,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-11,oneAdultRate:300.1,twoAdultRate:600.2,extraAdultRate:100,extraChildRate:600.2,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-11,oneAdultRate:300,twoAdultRate:345.36,extraAdultRate:241.75,extraChildRate:100,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-12,oneAdultRate:1110,twoAdultRate:1110,extraAdultRate:110,extraChildRate:110,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-12,oneAdultRate:315.2,twoAdultRate:661.92,extraAdultRate:110,extraChildRate:728.11,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-12,oneAdultRate:315,twoAdultRate:362.63,extraAdultRate:181.31,extraChildRate:110,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-13,oneAdultRate:1120.78,twoAdultRate:1120.78,extraAdultRate:100,extraChildRate:100,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-13,oneAdultRate:462.66,twoAdultRate:1017.85,extraAdultRate:1424.99,extraChildRate:1424.99,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-13,oneAdultRate:330,twoAdultRate:330,extraAdultRate:99,extraChildRate:140,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-14,oneAdultRate:1234.12,twoAdultRate:1234.12,extraAdultRate:100,extraChildRate:100,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-14,oneAdultRate:534.5,twoAdultRate:807.75,extraAdultRate:1373.17,extraChildRate:161.55,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-14,oneAdultRate:344,twoAdultRate:327.34,extraAdultRate:0,extraChildRate:170,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-15,oneAdultRate:1140.4,twoAdultRate:1140.4,extraAdultRate:100,extraChildRate:100,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-15,oneAdultRate:73.7,twoAdultRate:88.44,extraAdultRate:17.69,extraChildRate:8.84,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-15,oneAdultRate:260.4,twoAdultRate:260.4,extraAdultRate:0,extraChildRate:140.4,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-16,oneAdultRate:68.1,twoAdultRate:78.81,extraAdultRate:8.38,extraChildRate:8.38,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-16,oneAdultRate:67.1,twoAdultRate:73.81,extraAdultRate:7.38,extraChildRate:7.38,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-16,oneAdultRate:0,twoAdultRate:0,extraAdultRate:0,extraChildRate:0,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A1B,rateDate:2009-09-17,oneAdultRate:0,twoAdultRate:0,extraAdultRate:100,extraChildRate:100,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-17,oneAdultRate:206,twoAdultRate:412,extraAdultRate:412,extraChildRate:412,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-17,oneAdultRate:130,twoAdultRate:143.16,extraAdultRate:14.49,extraChildRate:0,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100}]'
            def actualDecisions = decisions.toString().replace('"', '')
            assert actualDecisions.equals(expectedDecisions)
        }

        else if(uploadType=='differential' && miscAdjustment==false && applyTax==false && uploadChildAgeBucket==true) {
            def expectedDecisions = '[{rateCode:RACK,roomType:A1B,rateDate:2009-09-14,oneAdultRate:1234.12,twoAdultRate:1234.12,extraAdultRate:100,extraChildRate:100,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-14,oneAdultRate:534.5,twoAdultRate:807.75,extraAdultRate:1373.17,extraChildRate:161.55,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-14,oneAdultRate:344,twoAdultRate:327.34,extraAdultRate:0,extraChildRate:170,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100}]'
            def actualDecisions = decisions.toString().replace('"', '')
            assert actualDecisions.equals(expectedDecisions)
        }
        else {
            def expectedDecisions = '[{rateCode:RACK,roomType:A1B,rateDate:2009-09-14,oneAdultRate:1234.12,twoAdultRate:1234.12,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A2B,rateDate:2009-09-14,oneAdultRate:534.5,twoAdultRate:807.75,extraAdultRate:1373.17,extraChildRate:161.55,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:RACK,roomType:A3B,rateDate:2009-09-14,oneAdultRate:344,twoAdultRate:327.34,extraAdultRate:0,extraChildRate:170,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null}]'
            def actualDecisions = decisions.toString().replace('"', '')
            assert actualDecisions.equals(expectedDecisions)
        }
    }

    void verifyAgileRateDecisions(JSONArray decisions, String uploadType,Boolean  applyTax,Boolean  miscAdjustment,Boolean uploadChildAgeBucket) {
        if(uploadType=='full' && miscAdjustment==true && applyTax==true && uploadChildAgeBucket==false ){
            def expectedDecisions = '[{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-09,oneAdultRate:0,twoAdultRate:0,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-09,oneAdultRate:1288,twoAdultRate:1400,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-09,oneAdultRate:1393,twoAdultRate:1505,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-10,oneAdultRate:11,twoAdultRate:123,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-10,oneAdultRate:382,twoAdultRate:752,extraAdultRate:112,extraChildRate:741,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-10,oneAdultRate:381,twoAdultRate:493,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-11,oneAdultRate:907,twoAdultRate:907,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-11,oneAdultRate:347,twoAdultRate:683,extraAdultRate:112,extraChildRate:672,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-11,oneAdultRate:347,twoAdultRate:398,extraAdultRate:271,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-12,oneAdultRate:1254,twoAdultRate:1254,extraAdultRate:123,extraChildRate:123,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-12,oneAdultRate:364,twoAdultRate:753,extraAdultRate:123,extraChildRate:815,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-12,oneAdultRate:364,twoAdultRate:417,extraAdultRate:203,extraChildRate:123,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-13,oneAdultRate:1266,twoAdultRate:1266,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-13,oneAdultRate:529,twoAdultRate:1151,extraAdultRate:1596,extraChildRate:1596,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-13,oneAdultRate:381,twoAdultRate:381,extraAdultRate:111,extraChildRate:157,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-14,oneAdultRate:1393,twoAdultRate:1393,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-14,oneAdultRate:610,twoAdultRate:916,extraAdultRate:1538,extraChildRate:181,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-14,oneAdultRate:396,twoAdultRate:378,extraAdultRate:0,extraChildRate:190,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-15,oneAdultRate:1288,twoAdultRate:1288,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-15,oneAdultRate:94,twoAdultRate:110,extraAdultRate:20,extraChildRate:10,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-15,oneAdultRate:303,twoAdultRate:303,extraAdultRate:0,extraChildRate:157,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-16,oneAdultRate:87,twoAdultRate:99,extraAdultRate:9,extraChildRate:9,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-16,oneAdultRate:86,twoAdultRate:94,extraAdultRate:8,extraChildRate:8,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-16,oneAdultRate:0,twoAdultRate:0,extraAdultRate:0,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-17,oneAdultRate:11,twoAdultRate:11,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-17,oneAdultRate:242,twoAdultRate:473,extraAdultRate:461,extraChildRate:461,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-17,oneAdultRate:157,twoAdultRate:172,extraAdultRate:16,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null}]'
            def actualDecisions = decisions.toString().replace('"', '')
            assert actualDecisions.equals(expectedDecisions)
        }
        else if(uploadType=='full' && applyTax==true && miscAdjustment==false && uploadChildAgeBucket==false){
            def expectedDecisions = '[{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-09,oneAdultRate:0,twoAdultRate:0,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-09,oneAdultRate:1277.25,twoAdultRate:1389.25,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-09,oneAdultRate:1382.21,twoAdultRate:1494.21,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-10,oneAdultRate:0,twoAdultRate:112,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-10,oneAdultRate:370.46,twoAdultRate:740.92,extraAdultRate:112,extraChildRate:740.92,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-10,oneAdultRate:369.6,twoAdultRate:481.6,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-11,oneAdultRate:896,twoAdultRate:896,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-11,oneAdultRate:336.11,twoAdultRate:672.22,extraAdultRate:112,extraChildRate:672.22,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-11,oneAdultRate:336,twoAdultRate:386.8,extraAdultRate:270.76,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-12,oneAdultRate:1243.2,twoAdultRate:1243.2,extraAdultRate:123.2,extraChildRate:123.2,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-12,oneAdultRate:353.02,twoAdultRate:741.35,extraAdultRate:123.2,extraChildRate:815.49,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-12,oneAdultRate:352.8,twoAdultRate:406.14,extraAdultRate:203.07,extraChildRate:123.2,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-13,oneAdultRate:1255.27,twoAdultRate:1255.27,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-13,oneAdultRate:518.18,twoAdultRate:1139.99,extraAdultRate:1595.99,extraChildRate:1595.99,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-13,oneAdultRate:369.6,twoAdultRate:369.6,extraAdultRate:110.88,extraChildRate:156.8,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-14,oneAdultRate:1382.21,twoAdultRate:1382.21,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-14,oneAdultRate:598.64,twoAdultRate:904.68,extraAdultRate:1537.96,extraChildRate:180.94,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-14,oneAdultRate:385.28,twoAdultRate:366.62,extraAdultRate:0,extraChildRate:190.4,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-15,oneAdultRate:1277.25,twoAdultRate:1277.25,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-15,oneAdultRate:82.54,twoAdultRate:99.05,extraAdultRate:19.81,extraChildRate:9.91,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-15,oneAdultRate:291.65,twoAdultRate:291.65,extraAdultRate:0,extraChildRate:157.25,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-16,oneAdultRate:76.27,twoAdultRate:88.27,extraAdultRate:9.39,extraChildRate:9.39,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-16,oneAdultRate:75.15,twoAdultRate:82.67,extraAdultRate:8.27,extraChildRate:8.27,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-16,oneAdultRate:0,twoAdultRate:0,extraAdultRate:0,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-17,oneAdultRate:0,twoAdultRate:0,extraAdultRate:112,extraChildRate:112,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-17,oneAdultRate:230.72,twoAdultRate:461.44,extraAdultRate:461.44,extraChildRate:461.44,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-17,oneAdultRate:145.6,twoAdultRate:160.33,extraAdultRate:16.23,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null}]'
            def actualDecisions = decisions.toString().replace('"', '')
            assert actualDecisions.equals(expectedDecisions)
        }
        else if(uploadType=='full' && miscAdjustment==false && applyTax==false && uploadChildAgeBucket==false){
            def expectedDecisions = '[{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-09,oneAdultRate:0,twoAdultRate:0,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-09,oneAdultRate:1140.4,twoAdultRate:1240.4,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-09,oneAdultRate:1234.12,twoAdultRate:1334.12,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-10,oneAdultRate:0,twoAdultRate:100,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-10,oneAdultRate:330.77,twoAdultRate:661.54,extraAdultRate:100,extraChildRate:661.54,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-10,oneAdultRate:330,twoAdultRate:430,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-11,oneAdultRate:800,twoAdultRate:800,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-11,oneAdultRate:300.1,twoAdultRate:600.2,extraAdultRate:100,extraChildRate:600.2,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-11,oneAdultRate:300,twoAdultRate:345.36,extraAdultRate:241.75,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-12,oneAdultRate:1110,twoAdultRate:1110,extraAdultRate:110,extraChildRate:110,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-12,oneAdultRate:315.2,twoAdultRate:661.92,extraAdultRate:110,extraChildRate:728.11,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-12,oneAdultRate:315,twoAdultRate:362.63,extraAdultRate:181.31,extraChildRate:110,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-13,oneAdultRate:1120.78,twoAdultRate:1120.78,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-13,oneAdultRate:462.66,twoAdultRate:1017.85,extraAdultRate:1424.99,extraChildRate:1424.99,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-13,oneAdultRate:330,twoAdultRate:330,extraAdultRate:99,extraChildRate:140,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-14,oneAdultRate:1234.12,twoAdultRate:1234.12,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-14,oneAdultRate:534.5,twoAdultRate:807.75,extraAdultRate:1373.17,extraChildRate:161.55,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-14,oneAdultRate:344,twoAdultRate:327.34,extraAdultRate:0,extraChildRate:170,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-15,oneAdultRate:1140.4,twoAdultRate:1140.4,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-15,oneAdultRate:73.7,twoAdultRate:88.44,extraAdultRate:17.69,extraChildRate:8.84,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-15,oneAdultRate:260.4,twoAdultRate:260.4,extraAdultRate:0,extraChildRate:140.4,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-16,oneAdultRate:68.1,twoAdultRate:78.81,extraAdultRate:8.38,extraChildRate:8.38,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-16,oneAdultRate:67.1,twoAdultRate:73.81,extraAdultRate:7.38,extraChildRate:7.38,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-16,oneAdultRate:0,twoAdultRate:0,extraAdultRate:0,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-17,oneAdultRate:0,twoAdultRate:0,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-17,oneAdultRate:206,twoAdultRate:412,extraAdultRate:412,extraChildRate:412,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-17,oneAdultRate:130,twoAdultRate:143.16,extraAdultRate:14.49,extraChildRate:0,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null}]'
            def actualDecisions = decisions.toString().replace('"', '')
            assert actualDecisions.equals(expectedDecisions)
        }
        else if(uploadType=='full' && miscAdjustment==true && applyTax==true && uploadChildAgeBucket==true ){
            def expectedDecisions = '[{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-09,oneAdultRate:0,twoAdultRate:0,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-09,oneAdultRate:1288,twoAdultRate:1400,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-09,oneAdultRate:1393,twoAdultRate:1505,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-10,oneAdultRate:11,twoAdultRate:123,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-10,oneAdultRate:382,twoAdultRate:752,extraAdultRate:112,extraChildRate:741,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-10,oneAdultRate:381,twoAdultRate:493,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-11,oneAdultRate:907,twoAdultRate:907,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-11,oneAdultRate:347,twoAdultRate:683,extraAdultRate:112,extraChildRate:672,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-11,oneAdultRate:347,twoAdultRate:398,extraAdultRate:271,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-12,oneAdultRate:1254,twoAdultRate:1254,extraAdultRate:123,extraChildRate:123,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-12,oneAdultRate:364,twoAdultRate:753,extraAdultRate:123,extraChildRate:815,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-12,oneAdultRate:364,twoAdultRate:417,extraAdultRate:203,extraChildRate:123,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-13,oneAdultRate:1266,twoAdultRate:1266,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-13,oneAdultRate:529,twoAdultRate:1151,extraAdultRate:1596,extraChildRate:1596,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-13,oneAdultRate:381,twoAdultRate:381,extraAdultRate:111,extraChildRate:157,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-14,oneAdultRate:1393,twoAdultRate:1393,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-14,oneAdultRate:610,twoAdultRate:916,extraAdultRate:1538,extraChildRate:181,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-14,oneAdultRate:396,twoAdultRate:378,extraAdultRate:0,extraChildRate:190,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-15,oneAdultRate:1288,twoAdultRate:1288,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-15,oneAdultRate:94,twoAdultRate:110,extraAdultRate:20,extraChildRate:10,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-15,oneAdultRate:303,twoAdultRate:303,extraAdultRate:0,extraChildRate:157,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-16,oneAdultRate:87,twoAdultRate:99,extraAdultRate:9,extraChildRate:9,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-16,oneAdultRate:86,twoAdultRate:94,extraAdultRate:8,extraChildRate:8,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-16,oneAdultRate:0,twoAdultRate:0,extraAdultRate:0,extraChildRate:0,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-17,oneAdultRate:11,twoAdultRate:11,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-17,oneAdultRate:242,twoAdultRate:473,extraAdultRate:461,extraChildRate:461,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-17,oneAdultRate:157,twoAdultRate:172,extraAdultRate:16,extraChildRate:0,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112}]'
            def actualDecisions = decisions.toString().replace('"', '')
            assert actualDecisions.equals(expectedDecisions)
        }
        else if(uploadType=='full' && applyTax==true && miscAdjustment==false && uploadChildAgeBucket==true){
            def expectedDecisions = '[{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-09,oneAdultRate:0,twoAdultRate:0,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-09,oneAdultRate:1277.25,twoAdultRate:1389.25,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-09,oneAdultRate:1382.21,twoAdultRate:1494.21,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-10,oneAdultRate:0,twoAdultRate:112,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-10,oneAdultRate:370.46,twoAdultRate:740.92,extraAdultRate:112,extraChildRate:740.92,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-10,oneAdultRate:369.6,twoAdultRate:481.6,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-11,oneAdultRate:896,twoAdultRate:896,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-11,oneAdultRate:336.11,twoAdultRate:672.22,extraAdultRate:112,extraChildRate:672.22,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-11,oneAdultRate:336,twoAdultRate:386.8,extraAdultRate:270.76,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-12,oneAdultRate:1243.2,twoAdultRate:1243.2,extraAdultRate:123.2,extraChildRate:123.2,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-12,oneAdultRate:353.02,twoAdultRate:741.35,extraAdultRate:123.2,extraChildRate:815.49,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-12,oneAdultRate:352.8,twoAdultRate:406.14,extraAdultRate:203.07,extraChildRate:123.2,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-13,oneAdultRate:1255.27,twoAdultRate:1255.27,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-13,oneAdultRate:518.18,twoAdultRate:1139.99,extraAdultRate:1595.99,extraChildRate:1595.99,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-13,oneAdultRate:369.6,twoAdultRate:369.6,extraAdultRate:110.88,extraChildRate:156.8,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-14,oneAdultRate:1382.21,twoAdultRate:1382.21,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-14,oneAdultRate:598.64,twoAdultRate:904.68,extraAdultRate:1537.96,extraChildRate:180.94,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-14,oneAdultRate:385.28,twoAdultRate:366.62,extraAdultRate:0,extraChildRate:190.4,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-15,oneAdultRate:1277.25,twoAdultRate:1277.25,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-15,oneAdultRate:82.54,twoAdultRate:99.05,extraAdultRate:19.81,extraChildRate:9.91,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-15,oneAdultRate:291.65,twoAdultRate:291.65,extraAdultRate:0,extraChildRate:157.25,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-16,oneAdultRate:76.27,twoAdultRate:88.27,extraAdultRate:9.39,extraChildRate:9.39,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-16,oneAdultRate:75.15,twoAdultRate:82.67,extraAdultRate:8.27,extraChildRate:8.27,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-16,oneAdultRate:0,twoAdultRate:0,extraAdultRate:0,extraChildRate:0,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-17,oneAdultRate:0,twoAdultRate:0,extraAdultRate:112,extraChildRate:112,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-17,oneAdultRate:230.72,twoAdultRate:461.44,extraAdultRate:461.44,extraChildRate:461.44,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-17,oneAdultRate:145.6,twoAdultRate:160.33,extraAdultRate:16.23,extraChildRate:0,childAgeOneRate:112,childAgeTwoRate:112,childAgeThreeRate:112}]'
            def actualDecisions = decisions.toString().replace('"', '')
            assert actualDecisions.equals(expectedDecisions)
        }
        else if(uploadType=='full' && miscAdjustment==false && applyTax==false && uploadChildAgeBucket==true){
            def expectedDecisions = '[{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-09,oneAdultRate:0,twoAdultRate:0,extraAdultRate:100,extraChildRate:100,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-09,oneAdultRate:1140.4,twoAdultRate:1240.4,extraAdultRate:100,extraChildRate:100,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-09,oneAdultRate:1234.12,twoAdultRate:1334.12,extraAdultRate:100,extraChildRate:100,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-10,oneAdultRate:0,twoAdultRate:100,extraAdultRate:100,extraChildRate:100,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-10,oneAdultRate:330.77,twoAdultRate:661.54,extraAdultRate:100,extraChildRate:661.54,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-10,oneAdultRate:330,twoAdultRate:430,extraAdultRate:100,extraChildRate:100,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-11,oneAdultRate:800,twoAdultRate:800,extraAdultRate:100,extraChildRate:100,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-11,oneAdultRate:300.1,twoAdultRate:600.2,extraAdultRate:100,extraChildRate:600.2,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-11,oneAdultRate:300,twoAdultRate:345.36,extraAdultRate:241.75,extraChildRate:100,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-12,oneAdultRate:1110,twoAdultRate:1110,extraAdultRate:110,extraChildRate:110,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-12,oneAdultRate:315.2,twoAdultRate:661.92,extraAdultRate:110,extraChildRate:728.11,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-12,oneAdultRate:315,twoAdultRate:362.63,extraAdultRate:181.31,extraChildRate:110,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-13,oneAdultRate:1120.78,twoAdultRate:1120.78,extraAdultRate:100,extraChildRate:100,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-13,oneAdultRate:462.66,twoAdultRate:1017.85,extraAdultRate:1424.99,extraChildRate:1424.99,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-13,oneAdultRate:330,twoAdultRate:330,extraAdultRate:99,extraChildRate:140,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-14,oneAdultRate:1234.12,twoAdultRate:1234.12,extraAdultRate:100,extraChildRate:100,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-14,oneAdultRate:534.5,twoAdultRate:807.75,extraAdultRate:1373.17,extraChildRate:161.55,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-14,oneAdultRate:344,twoAdultRate:327.34,extraAdultRate:0,extraChildRate:170,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-15,oneAdultRate:1140.4,twoAdultRate:1140.4,extraAdultRate:100,extraChildRate:100,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-15,oneAdultRate:73.7,twoAdultRate:88.44,extraAdultRate:17.69,extraChildRate:8.84,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-15,oneAdultRate:260.4,twoAdultRate:260.4,extraAdultRate:0,extraChildRate:140.4,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-16,oneAdultRate:68.1,twoAdultRate:78.81,extraAdultRate:8.38,extraChildRate:8.38,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-16,oneAdultRate:67.1,twoAdultRate:73.81,extraAdultRate:7.38,extraChildRate:7.38,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-16,oneAdultRate:0,twoAdultRate:0,extraAdultRate:0,extraChildRate:0,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-17,oneAdultRate:0,twoAdultRate:0,extraAdultRate:100,extraChildRate:100,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-17,oneAdultRate:206,twoAdultRate:412,extraAdultRate:412,extraChildRate:412,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-17,oneAdultRate:130,twoAdultRate:143.16,extraAdultRate:14.49,extraChildRate:0,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100}]'
            def actualDecisions = decisions.toString().replace('"', '')
            assert actualDecisions.equals(expectedDecisions)
        }

        else if(uploadType=='differential' && miscAdjustment==false && applyTax==false && uploadChildAgeBucket==true) {
            def expectedDecisions = '[{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-14,oneAdultRate:1234.12,twoAdultRate:1234.12,extraAdultRate:100,extraChildRate:100,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-14,oneAdultRate:534.5,twoAdultRate:807.75,extraAdultRate:1373.17,extraChildRate:161.55,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-14,oneAdultRate:344,twoAdultRate:327.34,extraAdultRate:0,extraChildRate:170,childAgeOneRate:100,childAgeTwoRate:100,childAgeThreeRate:100}]'
            def actualDecisions = decisions.toString().replace('"', '')
            assert actualDecisions.equals(expectedDecisions)
        }
        else {
            def expectedDecisions = '[{rateCode:ANUJ,roomType:A1B,rateDate:2009-09-14,oneAdultRate:1234.12,twoAdultRate:1234.12,extraAdultRate:100,extraChildRate:100,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A2B,rateDate:2009-09-14,oneAdultRate:534.5,twoAdultRate:807.75,extraAdultRate:1373.17,extraChildRate:161.55,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null},' +
                    '{rateCode:ANUJ,roomType:A3B,rateDate:2009-09-14,oneAdultRate:344,twoAdultRate:327.34,extraAdultRate:0,extraChildRate:170,childAgeOneRate:null,childAgeTwoRate:null,childAgeThreeRate:null}]'
            def actualDecisions = decisions.toString().replace('"', '')
            assert actualDecisions.equals(expectedDecisions)
        }
    }
    void verifyDBARDecisions(JSONArray decisions) {
        def expectedDecisions = 	'[{"rateCode":"RACK","rateDate":"2009-09-19","los":1},{"rateCode":"RACK","rateDate":"2009-09-19","los":2},{"rateCode":"RACK","rateDate":"2009-09-19","los":3},{"rateCode":"RACK","rateDate":"2009-09-19","los":4},{"rateCode":"RACK","rateDate":"2009-09-19","los":5},{"rateCode":"RACK","rateDate":"2009-09-19","los":6},{"rateCode":"RACK","rateDate":"2009-09-19","los":7},{"rateCode":"RACK","rateDate":"2009-09-20","los":1},{"rateCode":"RACK","rateDate":"2009-09-20","los":2},{"rateCode":"RACK","rateDate":"2009-09-20","los":3},{"rateCode":"RACK","rateDate":"2009-09-20","los":4},{"rateCode":"RACK","rateDate":"2009-09-20","los":5},{"rateCode":"RACK","rateDate":"2009-09-20","los":6},{"rateCode":"RACK","rateDate":"2009-09-20","los":7}]'
//		def actualDecisions = decisions.toString().replace('"', '')
        def actualDecisions = decisions.toString()
        assert actualDecisions.equals(expectedDecisions)
    }

    void verifyFPLOSByRateCodeByRoomTypeDecisions(JSONArray decisions,String uploadType) {
        def expectedDecisions
        if(uploadType=='full'){
            expectedDecisions = 	'[{arrivalDate:2009-09-09,rateCode:LV0,roomType:A1B,fplos:NNNNNNNN,roomClass:null},' +
                    '{arrivalDate:2009-09-10,rateCode:LV0,roomType:A1B,fplos:NNNNNNNN,roomClass:null},' +
                    '{arrivalDate:2009-09-09,rateCode:LV1,roomType:A1B,fplos:NNNNNNNN,roomClass:null},' +
                    '{arrivalDate:2009-09-10,rateCode:LV1,roomType:A1B,fplos:NNNNNNNN,roomClass:null},' +
                    '{arrivalDate:2009-09-09,rateCode:LV0,roomType:A2B,fplos:YYYYYYYY,roomClass:null},' +
                    '{arrivalDate:2009-09-10,rateCode:LV0,roomType:A2B,fplos:YYYYYYYY,roomClass:null},' +
                    '{arrivalDate:2009-09-09,rateCode:LV1,roomType:A2B,fplos:YYYYYYYY,roomClass:null},' +
                    '{arrivalDate:2009-09-10,rateCode:LV1,roomType:A2B,fplos:YYYYYYYY,roomClass:null},' +
                    '{arrivalDate:2009-09-09,rateCode:LV0,roomType:A3B,fplos:YYYYYYYY,roomClass:null},' +
                    '{arrivalDate:2009-09-10,rateCode:LV0,roomType:A3B,fplos:NNNNYYNN,roomClass:null},' +
                    '{arrivalDate:2009-09-09,rateCode:LV1,roomType:A3B,fplos:YYYYYYYY,roomClass:null},' +
                    '{arrivalDate:2009-09-10,rateCode:LV1,roomType:A3B,fplos:NNNNYYNN,roomClass:null},' +
                    '{arrivalDate:2009-09-09,rateCode:LV0,roomType:PH,fplos:NNNNYYNN,roomClass:null},' +
                    '{arrivalDate:2009-09-10,rateCode:LV0,roomType:PH,fplos:NNNYYYNN,roomClass:null},' +
                    '{arrivalDate:2009-09-09,rateCode:LV1,roomType:PH,fplos:NNNNYYNN,roomClass:null},' +
                    '{arrivalDate:2009-09-10,rateCode:LV1,roomType:PH,fplos:NNNYYYNN,roomClass:null}]'
        }
        else {
            expectedDecisions = '[{arrivalDate:2009-09-10,rateCode:LV0,roomType:A3B,fplos:NNNNYYNN,roomClass:null},' +
                    '{arrivalDate:2009-09-10,rateCode:LV1,roomType:A3B,fplos:NNNNYYNN,roomClass:null},' +
                    '{arrivalDate:2009-09-10,rateCode:LV0,roomType:PH,fplos:NNNYYYNN,roomClass:null},' +
                    '{arrivalDate:2009-09-10,rateCode:LV1,roomType:PH,fplos:NNNYYYNN,roomClass:null}]'
        }
        def actualDecisions = decisions.toString().replace('"', '')
        assert actualDecisions.equals(expectedDecisions)
    }

    void verifyHotelOverbookingDecisions(JSONArray decisions,String uploadType) {
        def expectedDecisions
        if(uploadType=='full'){
        expectedDecisions = 	'[{occupancyDate:2009-09-09,overbooking:0,authorizedCapacity:239,expectedWalks:0.15},' +
                '{occupancyDate:2009-09-10,overbooking:0,authorizedCapacity:239,expectedWalks:0.07},' +
                '{occupancyDate:2009-09-11,overbooking:0,authorizedCapacity:239,expectedWalks:0},' +
                '{occupancyDate:2009-09-12,overbooking:0,authorizedCapacity:239,expectedWalks:0},' +
                '{occupancyDate:2009-09-13,overbooking:0,authorizedCapacity:239,expectedWalks:0},' +
                '{occupancyDate:2009-09-14,overbooking:0,authorizedCapacity:239,expectedWalks:0},' +
                '{occupancyDate:2009-09-15,overbooking:0,authorizedCapacity:239,expectedWalks:0},' +
                '{occupancyDate:2009-09-16,overbooking:0,authorizedCapacity:239,expectedWalks:0.3},' +
                '{occupancyDate:2009-09-17,overbooking:0,authorizedCapacity:239,expectedWalks:0},' +
                '{occupancyDate:2009-09-18,overbooking:0,authorizedCapacity:239,expectedWalks:0},' +
                '{occupancyDate:2009-09-19,overbooking:0,authorizedCapacity:239,expectedWalks:0},' +
                '{occupancyDate:2009-09-20,overbooking:0,authorizedCapacity:239,expectedWalks:0}]'
        }
        else {
            expectedDecisions = '[{occupancyDate:2009-09-09,overbooking:0,authorizedCapacity:239,expectedWalks:0.15},' +
                    '{occupancyDate:2009-09-18,overbooking:0,authorizedCapacity:239,expectedWalks:0},' +
                    '{occupancyDate:2009-09-20,overbooking:0,authorizedCapacity:239,expectedWalks:0}]'
        }
        def actualDecisions = decisions.toString().replace('"', '')
        assert actualDecisions.equals(expectedDecisions)
    }

    void verifyAccomTypeOverbookingDecisions(JSONArray decisions,String uploadType) {
        def expectedDecisions
        if(uploadType=='full'){
        expectedDecisions = 	'[{accomType:PH,occupancyDate:2009-09-09,overbooking:0,authorizedCapacity:201},' +
                '{accomType:PH,occupancyDate:2009-09-10,overbooking:0,authorizedCapacity:201},' +
                '{accomType:PH,occupancyDate:2009-09-11,overbooking:0,authorizedCapacity:201},' +
                '{accomType:PH,occupancyDate:2009-09-12,overbooking:0,authorizedCapacity:201},' +
                '{accomType:PH,occupancyDate:2009-09-13,overbooking:0,authorizedCapacity:201},' +
                '{accomType:PH,occupancyDate:2009-09-14,overbooking:0,authorizedCapacity:201},' +
                '{accomType:PH,occupancyDate:2009-09-15,overbooking:0,authorizedCapacity:201}]'
        }
        else {
            expectedDecisions = 	'[{accomType:PH,occupancyDate:2009-09-09,overbooking:0,authorizedCapacity:201},' +
                    '{accomType:PH,occupancyDate:2009-09-14,overbooking:0,authorizedCapacity:201},' +
                    '{accomType:PH,occupancyDate:2009-09-15,overbooking:0,authorizedCapacity:201}]'
        }
        def actualDecisions = decisions.toString().replace('"', '')
        assert actualDecisions.equals(expectedDecisions)
    }

}
