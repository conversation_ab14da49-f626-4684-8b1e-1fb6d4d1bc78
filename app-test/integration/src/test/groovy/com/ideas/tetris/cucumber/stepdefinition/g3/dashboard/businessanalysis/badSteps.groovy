package com.ideas.tetris.cucumber.stepdefinition.g3.dashboard.businessanalysis

import com.ideas.g3.client.rest.RestServiceUtil
import com.ideas.g3.client.rest.TetrisRESTClient
import com.ideas.tetris.util.DbConnection
import com.ideas.tetris.util.PropertiesTestHelper
import com.ideas.tetris.util.propertyrollout.PropertyRolloutRESTUtil
import cucumber.api.groovy.EN
import cucumber.api.groovy.Hooks
import groovy.json.JsonSlurper
import groovy.transform.Field
import groovyx.net.http.ContentType
import groovyx.net.http.HttpResponseDecorator
import groovyx.net.http.HttpResponseException
import junit.framework.Assert
import net.sf.json.test.JSONAssert
import java.sql.CallableStatement
import java.sql.Connection
import java.sql.ResultSet
import java.sql.ResultSetMetaData
import java.sql.Statement

this.metaClass.mixin(Hooks)
this.metaClass.mixin(EN)
@Field String DatabaseName = "010022"
@Field TetrisRESTClient tetrisRESTClient

Before('@BusinessAnalysisValidation') {
    tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
    tetrisRESTClient.headers.clientId = 6
    tetrisRESTClient.headers.propertyId = 10022
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.bar.webRateShoppingEnabled', 'pacman.SandBox.TLHTL', 'TRUE');
    PropertyRolloutRESTUtil.setConfigParamValue(tetrisRESTClient, 'pacman.feature.EnablePhysicalCapacityConsideration', 'pacman.SandBox', 'FALSE');
}

After('@BusinessAnalysisValidation') {
    tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
    tetrisRESTClient.headers.clientId = 6
    tetrisRESTClient.headers.propertyId = 10022

}

Given(~/I select "([^"]*)" client with property "([^"]*)"$/) { String client, String Property ->

}


And(~/I set Room type "([^"]*)" as Inactive$/) { String roomType ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute("update accom_type set display_status_id=2 where accom_type_code='" + roomType + "'");
    connection.close();
}

And(~/I reset Room type "([^"]*)" as Active$/) { String roomType ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute("update accom_type set display_status_id=1 where accom_type_code='" + roomType + "'");
    connection.close();
}

And(~/I set Market Segment "([^"]*)" is Excluded$/) { String mktSegId ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute("Update Mkt_Seg set Exclude_CompHouse_Data_Display= 1 where Mkt_Seg_ID = '" + mktSegId + "'");
    connection.close();
}

And(~/I reset Market Segment "([^"]*)" is Included$/) { String mktSegId ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute("Update Mkt_Seg set Exclude_CompHouse_Data_Display= 0 where Mkt_Seg_ID = '" + mktSegId + "'");
    connection.close();
}

Then(~/I create an Inventory Group with Name "([^"]*)"$/) { String InventoryGroupName ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute("delete from Inventory_Sharing_Rank;\n" +
            " delete from Inventory_Group_Details;\n" +
            " delete from Inventory_Group;SET IDENTITY_INSERT Inventory_Group ON;\n" +
            "insert into Inventory_Group \n" +
            "(Inventory_Group_ID,Inventory_Group_Name,Inventory_Group_Description,\n" +
            " Base_Accom_Class_Id,Created_by_User_ID,Created_DTTM,Last_Updated_by_User_ID,Last_Updated_DTTM)\n" +
            " values(1,'TLHTLInventoryGroup','TLHTLInventoryGroup',2,11403,'2018-04-30 11:20:06.210',\n" +
            "        11403,'2018-04-30 11:20:06.210');\n" +
            "SET IDENTITY_INSERT Inventory_Group OFF;\n" +
            "insert into Inventory_Group_Details\n" +
            "(Inventory_Group_ID,Accom_Class_ID,Created_by_User_ID,Created_DTTM,\n" +
            " Last_Updated_by_User_ID,Last_Updated_DTTM) \n" +
            " values(1,3,11403,'2018-04-30 11:20:06.227',11403,'2018-04-30 11:20:06.227');\n" +
            "insert into Inventory_Group_Details\n" +
            "(Inventory_Group_ID,Accom_Class_ID,Created_by_User_ID,Created_DTTM,\n" +
            " Last_Updated_by_User_ID,Last_Updated_DTTM) \n" +
            " values(1,2,11403,'2018-04-30 11:20:06.227',11403,'2018-04-30 11:20:06.227');\n" +
            " insert into Inventory_Sharing_Rank\n" +
            " (Accom_Class_Sharing_Group_ID,Accom_Class_ID,Rank,Created_By_User_ID,Created_DTTM,\n" +
            " Last_Updated_By_User_ID,Last_Updated_DTTM)\n" +
            " values (1,3,1,1,'2012-09-25 02:51:12.143',1,'2012-09-25 02:51:12.143');\n" +
            "  insert into Inventory_Sharing_Rank\n" +
            " (Accom_Class_Sharing_Group_ID,Accom_Class_ID,Rank,Created_By_User_ID,Created_DTTM,\n" +
            " Last_Updated_By_User_ID,Last_Updated_DTTM)\n" +
            " values (1,2,2,1,'2012-09-25 02:51:12.143',1,'2012-09-25 02:51:12.143');");
    connection.close();
}

Then(~/I create an Business Group with Name "([^"]*)"$/) { String BusinessGroupName ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute("delete from mkt_seg_Business_Group;\n" +
            "delete from Business_Group;\n" +
            "SET IDENTITY_INSERT Business_Group ON;\n" +
            "insert into Business_Group (Business_Group_id, Business_Group_Name, Business_Group_Description, Ranking, Status_id, created_by_user_id, last_updated_by_user_id, last_updated_DTTM, created_DTTM, property_id) values (1, '"+BusinessGroupName+"', '"+BusinessGroupName+"', 1, 1, 11403, 11404, GETDATE(), GETDATE(), 10022);\n" +
            "SET IDENTITY_INSERT Business_Group OFF;\n" +
            "SET IDENTITY_INSERT mkt_seg_Business_Group ON;\n" +
            "insert into mkt_seg_Business_Group (Mkt_Seg_Business_Group_ID, Business_Group_ID, Mkt_Seg_ID, Ranking, Created_By_User_ID, Created_DTTM, Last_Updated_By_User_ID, Last_Updated_DTTM) values (1, 1, 3, 1, 11403, GETDATE(), 11403, GETDATE());\n" +
            "insert into mkt_seg_Business_Group (Mkt_Seg_Business_Group_ID, Business_Group_ID, Mkt_Seg_ID, Ranking, Created_By_User_ID, Created_DTTM, Last_Updated_By_User_ID, Last_Updated_DTTM) values (2, 1, 4, 1, 11403, GETDATE(), 11403, GETDATE())\n" +
            "SET IDENTITY_INSERT mkt_seg_Business_Group OFF;\n");
    connection.close();
}

Then(~/I Delete Inventory Group with Name "([^"]*)"$/) { String InventoryGroupName ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute("delete from Inventory_Sharing_Rank;\n" +
            " delete from Inventory_Group_Details;\n" +
            " delete from Inventory_Group;");
    connection.close();
}

Then(~/I Delete Business Group with name "([^"]*)"$/) { String BusinessGroupName ->
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute("delete from mkt_seg_Business_Group;\n" +
            "delete from Business_Group;");
    connection.close();
}


public def getBADResponse(String myDate) {
    def response
    HttpResponseException duplicateException
    try {

        tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
        tetrisRESTClient.headers.clientId = 6
        tetrisRESTClient.headers.propertyId = 10022
        tetrisRESTClient = this.tetrisRESTClient

        response = tetrisRESTClient.get(
                path: RestServiceUtil.getPacmanRestURL()+"business-analysis/v1/" + myDate + "/" + myDate + "",
                requestContentType: ContentType.JSON)
    }
    catch (HttpResponseException ex) {
        duplicateException = ex
    }
    return response
}

public def getBADIndicators(String myDate) {
    def response
    HttpResponseException duplicateException
    try {
        tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
        tetrisRESTClient.headers.clientId = 6
        tetrisRESTClient.headers.propertyId = 10022
        tetrisRESTClient = this.tetrisRESTClient
        response = tetrisRESTClient.get(
                path: RestServiceUtil.getPacmanRestURL()+"business-analysis/indicators/v1/" + myDate + "/" + myDate + "",
                requestContentType: ContentType.JSON)
    }
    catch (HttpResponseException ex) {
        duplicateException = ex
    }
    return response
}

public def getBadExceptionDetails(String myDate) {
    def response
    HttpResponseException duplicateException
    try {
        tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
        tetrisRESTClient.headers.clientId = 6
        tetrisRESTClient.headers.propertyId = 10022
        tetrisRESTClient = this.tetrisRESTClient
        response = tetrisRESTClient.get(
                path: RestServiceUtil.getPacmanRestURL()+"business-analysis/HighestScoreException/v1/" + myDate + "",
                requestContentType: ContentType.JSON)
    }
    catch (HttpResponseException ex) {
        duplicateException = ex
    }
    return response
}


public def getBADBarRateDetails(String myDate) {
    def response
    HttpResponseException duplicateException
    try {
        tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
        tetrisRESTClient.headers.clientId = 6
        tetrisRESTClient.headers.propertyId = 10022
        tetrisRESTClient = this.tetrisRESTClient
        response = tetrisRESTClient.get(
                path: RestServiceUtil.getPacmanRestURL()+"business-analysis/BARbyLOS/v1/" + myDate + "/-1",
                requestContentType: ContentType.JSON)
    }
    catch (HttpResponseException ex) {
        duplicateException = ex
    }
    return response
}

public def getBADDailyOverviewDetails(String myDate) {
    def response
    HttpResponseException duplicateException
    try {
        tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
        tetrisRESTClient.headers.clientId = 6
        tetrisRESTClient.headers.propertyId = 10022
        tetrisRESTClient = this.tetrisRESTClient
        response = tetrisRESTClient.get(
                path: RestServiceUtil.getPacmanRestURL()+"business-analysis/overview/v1/" + myDate + "/-1",
                requestContentType: ContentType.JSON)
    }
    catch (HttpResponseException ex) {
        duplicateException = ex
    }
    return response
}

public def getPropertyLevelDetails(String myDate1, String myDate2) {
    def response
    HttpResponseException duplicateException
    try {
        tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
        tetrisRESTClient.headers.clientId = 6
        tetrisRESTClient.headers.propertyId = 10022
        tetrisRESTClient = this.tetrisRESTClient
        response = tetrisRESTClient.get(
                path: RestServiceUtil.getPacmanRestURL()+"business-analysis/property-details/v1/" + myDate1 + "/" + myDate2 + "/true/7/1",
                requestContentType: ContentType.JSON)
    }
    catch (HttpResponseException ex) {
        duplicateException = ex
    }
    return response
}

public def getBusinessTypeLevelDetails(String myDate1, String myDate2) {
    def response
    HttpResponseException duplicateException
    try {
        tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
        tetrisRESTClient.headers.clientId = 6
        tetrisRESTClient.headers.propertyId = 10022
        tetrisRESTClient = this.tetrisRESTClient
        response = tetrisRESTClient.get(
                path: RestServiceUtil.getPacmanRestURL()+"business-analysis/forecast-group-details/v1/" + myDate1 + "/" + myDate2 + "/true/7/1/0",
                requestContentType: ContentType.JSON)
    }
    catch (HttpResponseException ex) {
        duplicateException = ex
    }
    return response
}

public def getBusinessTypeLevelDetailsExcludeMktSeg(String myDate1, String myDate2) {
    def response
    HttpResponseException duplicateException
    try {
        tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
        tetrisRESTClient.headers.clientId = 6
        tetrisRESTClient.headers.propertyId = 10022
        tetrisRESTClient = this.tetrisRESTClient
        response = tetrisRESTClient.get(
                path: RestServiceUtil.getPacmanRestURL()+"business-analysis/forecast-group-details/v1/" + myDate1 + "/" + myDate2 + "/true/7/1/true",
                requestContentType: ContentType.JSON)
    }
    catch (HttpResponseException ex) {
        duplicateException = ex
    }
    return response
}

public def getForecastGroupDetailsUnderTransient(String myDate1, String myDate2) {
    def response
    HttpResponseException duplicateException
    try {
        tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
        tetrisRESTClient.headers.clientId = 6
        tetrisRESTClient.headers.propertyId = 10022
        tetrisRESTClient = this.tetrisRESTClient
        response = tetrisRESTClient.get(
                path: RestServiceUtil.getPacmanRestURL()+"business-analysis/forecast-group-details/v1/" + myDate1 + "/" + myDate2 + "/true/2/7/1/0",
                requestContentType: ContentType.JSON)
    }
    catch (HttpResponseException ex) {
        duplicateException = ex
    }
    return response
}

public def getForecastGroupDetailsUnderTransientWithExcludedMktSeg(String myDate1, String myDate2) {
    def response
    HttpResponseException duplicateException
    try {
        tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
        tetrisRESTClient.headers.clientId = 6
        tetrisRESTClient.headers.propertyId = 10022
        tetrisRESTClient = this.tetrisRESTClient
        response = tetrisRESTClient.get(
                path: RestServiceUtil.getPacmanRestURL()+"business-analysis/forecast-group-details/v1/" + myDate1 + "/" + myDate2 + "/true/2/7/1/1",
                requestContentType: ContentType.JSON)
    }
    catch (HttpResponseException ex) {
        duplicateException = ex
    }
    return response
}

public def getForecastGroupDetailsUnderGroup(String myDate1, String myDate2) {
    def response
    HttpResponseException duplicateException
    try {
        tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
        tetrisRESTClient.headers.clientId = 6
        tetrisRESTClient.headers.propertyId = 10022
        tetrisRESTClient = this.tetrisRESTClient
        response = tetrisRESTClient.get(
                path: RestServiceUtil.getPacmanRestURL()+"business-analysis/forecast-group-details/v1/" + myDate1 + "/" + myDate2 + "/true/1/7/1/0",
                requestContentType: ContentType.JSON)
    }
    catch (HttpResponseException ex) {
        duplicateException = ex
    }
    return response
}

public def getRoomClassLevelDetails(String myDate1, String myDate2) {
    def response
    HttpResponseException duplicateException
    try {
        tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
        tetrisRESTClient.headers.clientId = 6
        tetrisRESTClient.headers.propertyId = 10022
        tetrisRESTClient = this.tetrisRESTClient
        response = tetrisRESTClient.get(
                path: RestServiceUtil.getPacmanRestURL()+"business-analysis/room-class-details/v1/" + myDate1 + "/" + myDate2 + "/true/7/0/1",
                requestContentType: ContentType.JSON)
    }
    catch (HttpResponseException ex) {
        duplicateException = ex
    }
    return response
}

public def getRoomClassLevelDetailsWithRoomTypeStatus(String myDate1, String myDate2, String roomTypeStatus) {
    def response
    HttpResponseException duplicateException
    try {
        tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
        tetrisRESTClient.headers.clientId = 6
        tetrisRESTClient.headers.propertyId = 10022
        tetrisRESTClient = this.tetrisRESTClient
        if (roomTypeStatus.equalsIgnoreCase("Active"))
            response = tetrisRESTClient.get(
                    path: RestServiceUtil.getPacmanRestURL()+"business-analysis/room-class-details/v1/" + myDate1 + "/" + myDate2 + "/true/7/0/1",
                    requestContentType: ContentType.JSON)
        else
            response = tetrisRESTClient.get(
                    path: RestServiceUtil.getPacmanRestURL()+"business-analysis/room-class-details/v1/" + myDate1 + "/" + myDate2 + "/true/7/0/1,2",
                    requestContentType: ContentType.JSON)

    }
    catch (HttpResponseException ex) {
        duplicateException = ex
    }
    return response
}


public def getRoomTypeLevelDetailsUnderSTD(String myDate1, String myDate2) {
    def response
    HttpResponseException duplicateException
    try {
        tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
        tetrisRESTClient.headers.clientId = 6
        tetrisRESTClient.headers.propertyId = 10022
        tetrisRESTClient = this.tetrisRESTClient
        response = tetrisRESTClient.get(
                path: RestServiceUtil.getPacmanRestURL()+"business-analysis/room-class-details/v1/" + myDate1 + "/" + myDate2 + "/true/2/7/0/1",
                requestContentType: ContentType.JSON)
    }
    catch (HttpResponseException ex) {
        duplicateException = ex
    }
    return response
}

public def getRoomTypeLevelDetailsUnderDLX(String myDate1, String myDate2) {
    def response
    HttpResponseException duplicateException
    try {
        tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
        tetrisRESTClient.headers.clientId = 6
        tetrisRESTClient.headers.propertyId = 10022
        tetrisRESTClient = this.tetrisRESTClient
        response = tetrisRESTClient.get(
                path: RestServiceUtil.getPacmanRestURL()+"business-analysis/room-class-details/v1/" + myDate1 + "/" + myDate2 + "/true/3/7/0/1",
                requestContentType: ContentType.JSON)
    }
    catch (HttpResponseException ex) {
        duplicateException = ex
    }
    return response
}

public def getRoomTypeLevelDetailsUnderSTDForRoomTypes(String myDate1, String myDate2, String roomTypeStatus) {
    def response
    HttpResponseException duplicateException
    try {
        tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
        tetrisRESTClient.headers.clientId = 6
        tetrisRESTClient.headers.propertyId = 10022
        tetrisRESTClient = this.tetrisRESTClient
        if (roomTypeStatus.equalsIgnoreCase("Active"))
            response = tetrisRESTClient.get(
                    path: RestServiceUtil.getPacmanRestURL()+"business-analysis/room-class-details/v1/" + myDate1 + "/" + myDate2 + "/true/2/7/0/1",
                    requestContentType: ContentType.JSON)
        else
            response = tetrisRESTClient.get(
                    path: RestServiceUtil.getPacmanRestURL()+"business-analysis/room-class-details/v1/" + myDate1 + "/" + myDate2 + "/true/2/7/0/1,2",
                    requestContentType: ContentType.JSON)

    }
    catch (HttpResponseException ex) {
        duplicateException = ex
    }
    return response
}


public def getRoomTypeLevelDetailsUnderSTDForRoomTypesUnderInventoryGroup(String myDate1, String myDate2, String roomTypeStatus) {
    def response
    HttpResponseException duplicateException
    try {
        tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
        tetrisRESTClient.headers.clientId = 6
        tetrisRESTClient.headers.propertyId = 10022
        tetrisRESTClient = this.tetrisRESTClient
        if (roomTypeStatus.equalsIgnoreCase("Active"))
            response = tetrisRESTClient.get(
                    path: RestServiceUtil.getPacmanRestURL()+"business-analysis/room-class-details-at-inventorygroup/v1/" + myDate1 + "/" + myDate2 + "/true/2/7/0/1/1",
                    requestContentType: ContentType.JSON)
        else
            response = tetrisRESTClient.get(
                    path: RestServiceUtil.getPacmanRestURL()+"business-analysis/room-class-details-at-inventorygroup/v1/" + myDate1 + "/" + myDate2 + "/true/2/7/0/1/1,2",
                    requestContentType: ContentType.JSON)

    }
    catch (HttpResponseException ex) {
        duplicateException = ex
    }
    return response
}

public def getRoomTypeLevelDetailsUnderDLXForRoomTypesUnderInventoryGroup(String myDate1, String myDate2, String roomTypeStatus) {
    def response
    HttpResponseException duplicateException
    try {
        tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
        tetrisRESTClient.headers.clientId = 6
        tetrisRESTClient.headers.propertyId = 10022
        tetrisRESTClient = this.tetrisRESTClient
        if (roomTypeStatus.equalsIgnoreCase("Active"))
            response = tetrisRESTClient.get(
                    path: RestServiceUtil.getPacmanRestURL()+"business-analysis/room-class-details-at-inventorygroup/v1/" + myDate1 + "/" + myDate2 + "/true/3/7/0/1/1",
                    requestContentType: ContentType.JSON)
        else
            response = tetrisRESTClient.get(
                    path: RestServiceUtil.getPacmanRestURL()+"business-analysis/room-class-details-at-inventorygroup/v1/" + myDate1 + "/" + myDate2 + "/true/3/7/0/1/1,2",
                    requestContentType: ContentType.JSON)

    }
    catch (HttpResponseException ex) {
        duplicateException = ex
    }
    return response
}


public def getRoomTypeLevelDetailsUnderDLXForRoomTypes(String myDate1, String myDate2, String roomTypeStatus) {
    def response
    HttpResponseException duplicateException
    try {
        tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
        tetrisRESTClient.headers.clientId = 6
        tetrisRESTClient.headers.propertyId = 10022
        tetrisRESTClient = this.tetrisRESTClient
        if (roomTypeStatus.equalsIgnoreCase("Active"))
            response = tetrisRESTClient.get(
                    path: RestServiceUtil.getPacmanRestURL()+"business-analysis/room-class-details/v1/" + myDate1 + "/" + myDate2 + "/true/3/7/0/1",
                    requestContentType: ContentType.JSON)
        else
            response = tetrisRESTClient.get(
                    path: RestServiceUtil.getPacmanRestURL()+"business-analysis/room-class-details/v1/" + myDate1 + "/" + myDate2 + "/true/3/7/0/1,2",
                    requestContentType: ContentType.JSON)
    }
    catch (HttpResponseException ex) {
        duplicateException = ex
    }
    return response
}

public def getMarketSegmentLevelDetails(String myDate1, String myDate2) {
    def response
    HttpResponseException duplicateException
    try {
        tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
        tetrisRESTClient.headers.clientId = 6
        tetrisRESTClient.headers.propertyId = 10022
        tetrisRESTClient = this.tetrisRESTClient
        response = tetrisRESTClient.get(
                path: RestServiceUtil.getPacmanRestURL()+"business-analysis/forecast-group-details/v1/" + myDate1 + "/" + myDate2 + "/true/2/12/7/mktsegstatus/1/0",
                requestContentType: ContentType.JSON)
    }
    catch (HttpResponseException ex) {
        duplicateException = ex
    }
    return response
}

public def getExcludedMarketSegmentLevelDetails(String myDate1, String myDate2) {
    def response
    HttpResponseException duplicateException
    try {
        tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
        tetrisRESTClient.headers.clientId = 6
        tetrisRESTClient.headers.propertyId = 10022
        tetrisRESTClient = this.tetrisRESTClient
        response = tetrisRESTClient.get(
                path: RestServiceUtil.getPacmanRestURL()+"business-analysis/forecast-group-details/v1/" + myDate1 + "/" + myDate2 + "/true/2/12/7/mktsegstatus/1/1",
                requestContentType: ContentType.JSON)
    }
    catch (HttpResponseException ex) {
        duplicateException = ex
    }
    return response
}

public def getBusinessViewLevelDetails(String myDate1, String myDate2) {
    def response
    HttpResponseException duplicateException
    try {
        tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
        tetrisRESTClient.headers.clientId = 6
        tetrisRESTClient.headers.propertyId = 10022
        tetrisRESTClient = this.tetrisRESTClient
        response = tetrisRESTClient.get(
                path: RestServiceUtil.getPacmanRestURL()+"business-analysis-data-details/business-view-details/v1/" + myDate1 + "/" + myDate2 + "/true/2/1,2,3,4/1/0",
                requestContentType: ContentType.JSON)
    }
    catch (HttpResponseException ex) {
        duplicateException = ex
    }
    return response
}

public def getBusinessViewLevelDetailsForUnassigned(String myDate1, String myDate2) {
    def response
    HttpResponseException duplicateException
    try {
        tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
        tetrisRESTClient.headers.clientId = 6
        tetrisRESTClient.headers.propertyId = 10022
        tetrisRESTClient = this.tetrisRESTClient
        response = tetrisRESTClient.get(
                path: RestServiceUtil.getPacmanRestURL()+"business-analysis-data-details/business-view-details/v1/" + myDate1 + "/" + myDate2 + "/true/2/-1/1/0",
                requestContentType: ContentType.JSON)
    }
    catch (HttpResponseException ex) {
        duplicateException = ex
    }
    return response
}

public def getRateCodeLevelDetails(String myDate1, String myDate2) {
    def response
    HttpResponseException duplicateException
    try {
        tetrisRESTClient = PropertiesTestHelper.login(RestServiceUtil.getPacmanRestURL(), "<EMAIL>", "password")
        tetrisRESTClient.headers.clientId = 6
        tetrisRESTClient.headers.propertyId = 10022
        tetrisRESTClient = this.tetrisRESTClient
        response = tetrisRESTClient.get(
                path: RestServiceUtil.getPacmanRestURL()+"business-analysis/forecast-group-details/v1/" + myDate1 + "/" + myDate2 + "/true/2/12/20/7/0",
                requestContentType: ContentType.JSON)
    }
    catch (HttpResponseException ex) {
        duplicateException = ex
    }
    return response
}

Then(~/I should see expected data$/) { ->

}


Given(~/I query to summary page with past date "([^"]*)"$/) { String startDate ->
    HttpResponseDecorator badResponse = getBADResponse(startDate)
    badResponse.getData().getAt("date")[0].putAt("millis", null)

    def slurper = new JsonSlurper()
    def response = slurper.parseText(badResponse.getData().toString())[0] // single day in response array
    Assert.assertEquals(27, response.date.date)
    Assert.assertEquals(9, response.date.month)
    Assert.assertEquals(2012, response.date.year)
    Assert.assertEquals("Sat", response.dayOfWeek)
    Assert.assertEquals(278, 78, response.lrv)
    Assert.assertEquals("LV0", response.barCode)
    Assert.assertEquals(299, response.barRate)
    Assert.assertEquals(99, response.capacity)
    Assert.assertEquals(0, response.unconstrainedRemainingDemand)
    Assert.assertEquals(0, response.groups)
    Assert.assertEquals(99, response.occupancyForecast)
    Assert.assertEquals(0, response.outOfOrder)
    Assert.assertEquals(1.81, response.wash)
    Assert.assertEquals(141.56, response.revpar)
    Assert.assertEquals(141.56, response.adr)
    Assert.assertEquals(100, response.occupancyForecastPerc)
    Assert.assertEquals(1, response.decisionReasonTypeId)
    Assert.assertEquals("LV0", response.upperBoundName)
    Assert.assertEquals("LV8", response.lowerBoundName)
    Assert.assertNotNull(response.upperBound)
    Assert.assertNotNull(response.lowerBound)
}

And(~/I query to summary page with current date "([^"]*)"$/) { String startDate ->
    HttpResponseDecorator badResponse = getBADResponse(startDate)
    badResponse.getData().getAt("date")[0].putAt("millis", null)
    def slurper = new JsonSlurper()
    def response = slurper.parseText(badResponse.getData().toString())[0] // single day in response array
    Assert.assertEquals(30, response.date.date)
    Assert.assertEquals(9, response.date.month)
    Assert.assertEquals(2012, response.date.year)
    Assert.assertEquals("Tue", response.dayOfWeek)
    Assert.assertEquals(0, response.lrv)
    Assert.assertNull(response.barCode)
    Assert.assertNull(response.barRate)
    Assert.assertEquals(99, response.capacity)
    Assert.assertEquals(73, response.onBooks)
    Assert.assertEquals(2.07, response.unconstrainedRemainingDemand)
    Assert.assertEquals(0, response.groups)
    Assert.assertEquals(74.3, response.occupancyForecast)
    Assert.assertEquals(0, response.outOfOrder)
    Assert.assertEquals(0.97, response.wash)
    Assert.assertEquals(88.92, response.revpar)
    Assert.assertEquals(120.58, response.adr)
    Assert.assertEquals(75.03, response.occupancyForecastPerc)
    Assert.assertNull(response.decisionReasonTypeId)
    Assert.assertNull(response.upperBoundName)
    Assert.assertNull(response.lowerBoundName)
    Assert.assertNull(response.upperBound)
    Assert.assertNull(response.lowerBound)
}
And(~/I query to summary page with future date "([^"]*)"$/) { String startDate ->
    HttpResponseDecorator badResponse = getBADResponse(startDate)
    badResponse.getData().getAt("date")[0].putAt("millis", null)
    def slurper = new JsonSlurper()
    def response = slurper.parseText(badResponse.getData().toString())[0] // single day in response array
    Assert.assertEquals(9, response.date.date)
    Assert.assertEquals(10, response.date.month)
    Assert.assertEquals(2012, response.date.year)
    Assert.assertEquals("Fri", response.dayOfWeek)
    Assert.assertEquals(135.09, response.lrv)
    Assert.assertEquals("LV0", response.barCode)
    Assert.assertEquals(189, response.barRate)
    Assert.assertEquals(1, response.overbookings)
    Assert.assertEquals(99, response.capacity)
    Assert.assertEquals(98, response.onBooks)
    Assert.assertEquals(27.33, response.unconstrainedRemainingDemand)
    Assert.assertEquals(8, response.groups)
    Assert.assertEquals(98.1, response.occupancyForecast)
    Assert.assertEquals(0, response.outOfOrder)
    Assert.assertEquals(4.83, response.wash)
    Assert.assertEquals(120.57, response.revpar)
    Assert.assertEquals(121.8, response.adr)
    Assert.assertEquals(99.07, response.occupancyForecastPerc)
    Assert.assertEquals(1, response.decisionReasonTypeId)
    Assert.assertNull(response.upperBoundName)
    Assert.assertNull(response.lowerBoundName)
    Assert.assertNull(response.upperBound)
    Assert.assertNull(response.lowerBound)
}

And(~/I query to summary page for a date "([^"]*)" where Special Event and Highest BAR Restricted indications are present$/) { String startDate ->
    HttpResponseDecorator badIndicatorsdata1 = getBADIndicators(startDate)
    badIndicatorsdata1.getData().getAt("date")[0].putAt("millis", null)
    String expectedResponse = "[{\"date\":{\"date\":23,\"month\":10,\"year\":2012},\"businessAnalysisDailyDataDto\":null,\"specialEventImpactFCST\":true,\"specialEventInfoOnly\":false,\"highestBARRestricted\":true,\"occupancyDemandOverriden\":false,\"arrivalDemandOverriden\":false,\"washOverriden\":false,\"valueOverbookingOverriden\":false,\"limitOverbookingOverriden\":false,\"userBAROverriden\":false,\"floorBAROverriden\":false,\"ceilingBAROverriden\":false,\"groupFloorBAROverriden\":false,\"exception\":false,\"notification\":false,\"suboptimalBARDecisionDueToLRA\":true,\"closeLV0AccomTypes\":[],\"outOfOrderOverridden\":false,\"manualRestrictionsPresent\":false,\"productRateOffsetOverriden\":false,\"accomClassSummary\":null,\"groupOccupancyForecastOverridden\":false}]"
    Assert.assertEquals(expectedResponse, badIndicatorsdata1.getData().toString());
}
And(~/I query to summary page for a date "([^"]*)" where BAR Specific Override indication is present$/) { String startDate ->
    HttpResponseDecorator badIndicatorsdata2 = getBADIndicators(startDate)
    badIndicatorsdata2.getData().getAt("date")[0].putAt("millis", null)
    String expectedResponse = "[{\"date\":{\"date\":3,\"month\":10,\"year\":2012},\"businessAnalysisDailyDataDto\":null,\"specialEventImpactFCST\":false,\"specialEventInfoOnly\":false,\"highestBARRestricted\":false,\"occupancyDemandOverriden\":false,\"arrivalDemandOverriden\":false,\"washOverriden\":false,\"valueOverbookingOverriden\":false,\"limitOverbookingOverriden\":false,\"userBAROverriden\":true,\"floorBAROverriden\":false,\"ceilingBAROverriden\":false,\"groupFloorBAROverriden\":false,\"exception\":false,\"notification\":false,\"suboptimalBARDecisionDueToLRA\":false,\"closeLV0AccomTypes\":[],\"outOfOrderOverridden\":false,\"manualRestrictionsPresent\":false,\"productRateOffsetOverriden\":false,\"accomClassSummary\":null,\"groupOccupancyForecastOverridden\":false}]"
    Assert.assertEquals(expectedResponse, badIndicatorsdata2.getData().toString());
}
And(~/I query to summary page for a date "([^"]*)" where Floor Override indication is present$/) { String startDate ->
    HttpResponseDecorator badIndicatorsdata3 = getBADIndicators(startDate)
    badIndicatorsdata3.getData().getAt("date")[0].putAt("millis", null)
    String expectedResponse = "[{\"date\":{\"date\":5,\"month\":10,\"year\":2012},\"businessAnalysisDailyDataDto\":null,\"specialEventImpactFCST\":false,\"specialEventInfoOnly\":false,\"highestBARRestricted\":false,\"occupancyDemandOverriden\":false,\"arrivalDemandOverriden\":false,\"washOverriden\":false,\"valueOverbookingOverriden\":false,\"limitOverbookingOverriden\":false,\"userBAROverriden\":false,\"floorBAROverriden\":true,\"ceilingBAROverriden\":false,\"groupFloorBAROverriden\":false,\"exception\":false,\"notification\":false,\"suboptimalBARDecisionDueToLRA\":false,\"closeLV0AccomTypes\":[],\"outOfOrderOverridden\":false,\"manualRestrictionsPresent\":false,\"productRateOffsetOverriden\":false,\"accomClassSummary\":null,\"groupOccupancyForecastOverridden\":false}]"
    Assert.assertEquals(expectedResponse, badIndicatorsdata3.getData().toString());
}
And(~/I query to summary page for a date "([^"]*)" where Wash Override indication is present$/) { String startDate ->
    HttpResponseDecorator badIndicatorsdata4 = getBADIndicators(startDate)
    badIndicatorsdata4.getData().getAt("date")[0].putAt("millis", null)
    String expectedResponse = "[{\"date\":{\"date\":7,\"month\":10,\"year\":2012},\"businessAnalysisDailyDataDto\":null,\"specialEventImpactFCST\":false,\"specialEventInfoOnly\":false,\"highestBARRestricted\":false,\"occupancyDemandOverriden\":false,\"arrivalDemandOverriden\":false,\"washOverriden\":true,\"valueOverbookingOverriden\":false,\"limitOverbookingOverriden\":false,\"userBAROverriden\":false,\"floorBAROverriden\":false,\"ceilingBAROverriden\":false,\"groupFloorBAROverriden\":false,\"exception\":false,\"notification\":false,\"suboptimalBARDecisionDueToLRA\":false,\"closeLV0AccomTypes\":[],\"outOfOrderOverridden\":false,\"manualRestrictionsPresent\":false,\"productRateOffsetOverriden\":false,\"accomClassSummary\":null,\"groupOccupancyForecastOverridden\":false}]"
    Assert.assertEquals(expectedResponse, badIndicatorsdata4.getData().toString());
}
And(~/I query to summary page for a date "([^"]*)" where Occupancy Demand Override indication is present$/) { String startDate ->
    HttpResponseDecorator badIndicatorsdata5 = getBADIndicators(startDate)
    badIndicatorsdata5.getData().getAt("date")[0].putAt("millis", null)
    String expectedResponse = "[{\"date\":{\"date\":15,\"month\":10,\"year\":2012},\"businessAnalysisDailyDataDto\":null,\"specialEventImpactFCST\":false,\"specialEventInfoOnly\":false,\"highestBARRestricted\":false,\"occupancyDemandOverriden\":true,\"arrivalDemandOverriden\":false,\"washOverriden\":false,\"valueOverbookingOverriden\":false,\"limitOverbookingOverriden\":false,\"userBAROverriden\":false,\"floorBAROverriden\":false,\"ceilingBAROverriden\":false,\"groupFloorBAROverriden\":false,\"exception\":false,\"notification\":false,\"suboptimalBARDecisionDueToLRA\":false,\"closeLV0AccomTypes\":[],\"outOfOrderOverridden\":false,\"manualRestrictionsPresent\":false,\"productRateOffsetOverriden\":false,\"accomClassSummary\":null,\"groupOccupancyForecastOverridden\":false}]"
    Assert.assertEquals(expectedResponse, badIndicatorsdata5.getData().toString());

}

And(~/I query to summary page for a date "([^"]*)" where Arrival By LOS Override indication is present$/) { String startDate ->
    HttpResponseDecorator badIndicatorsdata6 = getBADIndicators(startDate)
    badIndicatorsdata6.getData().getAt("date")[0].putAt("millis", null)
    String expectedResponse = "[{\"date\":{\"date\":16,\"month\":10,\"year\":2012},\"businessAnalysisDailyDataDto\":null,\"specialEventImpactFCST\":false,\"specialEventInfoOnly\":false,\"highestBARRestricted\":false,\"occupancyDemandOverriden\":false,\"arrivalDemandOverriden\":true,\"washOverriden\":false,\"valueOverbookingOverriden\":false,\"limitOverbookingOverriden\":false,\"userBAROverriden\":false,\"floorBAROverriden\":false,\"ceilingBAROverriden\":false,\"groupFloorBAROverriden\":false,\"exception\":false,\"notification\":false,\"suboptimalBARDecisionDueToLRA\":false,\"closeLV0AccomTypes\":[],\"outOfOrderOverridden\":false,\"manualRestrictionsPresent\":false,\"productRateOffsetOverriden\":false,\"accomClassSummary\":null,\"groupOccupancyForecastOverridden\":false}]"
    Assert.assertEquals(expectedResponse, badIndicatorsdata6.getData().toString());
}

And(~/I query to summary page for a date "([^"]*)" where Exceptions indication is present$/) { String startDate ->
    HttpResponseDecorator badIndicatorsdata7 = getBADIndicators(startDate)
    badIndicatorsdata7.getData().getAt("date")[0].putAt("millis", null)
    String expectedResponse = "[{\"date\":{\"date\":29,\"month\":2,\"year\":2013},\"businessAnalysisDailyDataDto\":null,\"specialEventImpactFCST\":true,\"specialEventInfoOnly\":false,\"highestBARRestricted\":false,\"occupancyDemandOverriden\":false,\"arrivalDemandOverriden\":false,\"washOverriden\":false,\"valueOverbookingOverriden\":false,\"limitOverbookingOverriden\":false,\"userBAROverriden\":false,\"floorBAROverriden\":false,\"ceilingBAROverriden\":false,\"groupFloorBAROverriden\":false,\"exception\":true,\"notification\":false,\"suboptimalBARDecisionDueToLRA\":false,\"closeLV0AccomTypes\":[],\"outOfOrderOverridden\":false,\"manualRestrictionsPresent\":false,\"productRateOffsetOverriden\":false,\"accomClassSummary\":null,\"groupOccupancyForecastOverridden\":false}]"
    Assert.assertEquals(expectedResponse, badIndicatorsdata7.getData().toString());
}

And(~/I query to summary page to validate Exceptions details on a date "([^"]*)"$/) { String startDate ->
    HttpResponseDecorator badExceptionDetails = getBadExceptionDetails(startDate)
    badExceptionDetails.getData().getAt("occupancyDate").putAt("millis", null)
    String expectedResponse = "{\"occupancyDate\":{\"date\":29,\"month\":2,\"year\":2013},\"id\":4,\"description\":\"OOO Rooms are affecting the Demand\",\"detail\":\"Occupancy Date:29-Mar-2013,Rooms Out of Order:3,Last Room Value:73.27 - Deluxe\",\"score\":1800,\"propertyId\":10022}"
    Assert.assertEquals(expectedResponse, badExceptionDetails.getData().toString());
}

And(~/I query to Rate Details for the date range "([^"]*)"$/) { String startDate ->
    HttpResponseDecorator badRateDetails = getBADBarRateDetails(startDate)
    badRateDetails.getData().getAt("date").putAt("millis", null)
    String expectedResponse = "{\"date\":{\"date\":11,\"month\":10,\"year\":2012},\"competitor\":\"Hampton\",\"competitorRate\":\"119.00\",\"los1Level\":\"LV6\",\"los1Rate\":129,\"los1ArrivalDemand\":9.58,\"los2Level\":\"LV6\",\"los2Rate\":129,\"los2ArrivalDemand\":1.89,\"los3Level\":\"LV5\",\"los3Rate\":139,\"los3ArrivalDemand\":0.85,\"los4Level\":\"LV5\",\"los4Rate\":139,\"los4ArrivalDemand\":0.78,\"los5Level\":\"LV5\",\"los5Rate\":139,\"los5ArrivalDemand\":1.51,\"los6Level\":\"LV4\",\"los6Rate\":149,\"los6ArrivalDemand\":0.07,\"los7Level\":\"LV4\",\"los7Rate\":149,\"los7ArrivalDemand\":0.02,\"los8Level\":null,\"los8Rate\":0,\"los8ArrivalDemand\":null}"
    Assert.assertEquals(badRateDetails.getData().toString(), expectedResponse);
}
And(~/I query to Daily Overview Details for the date "([^"]*)"$/) { String startDate ->
    HttpResponseDecorator badDailyOverviewDetails = getBADDailyOverviewDetails(startDate)
    String expectedResponse = "{\"name\":\"Hotel\",\"lrv\":null,\"rateCode\":null,\"rate\":null,\"overbooking\":1,\"children\":[{\"name\":\"Standard\",\"lrv\":0,\"rateCode\":\"LV6\",\"rate\":null,\"overbooking\":null,\"children\":[{\"name\":\"K1\",\"lrv\":null,\"rateCode\":\"LV6\",\"rate\":129,\"overbooking\":1,\"children\":null},{\"name\":\"K1RRC\",\"lrv\":null,\"rateCode\":\"LV6\",\"rate\":129,\"overbooking\":0,\"children\":null},{\"name\":\"K1RRD\",\"lrv\":null,\"rateCode\":\"LV6\",\"rate\":129,\"overbooking\":0,\"children\":null},{\"name\":\"Q2\",\"lrv\":null,\"rateCode\":\"LV6\",\"rate\":129,\"overbooking\":0,\"children\":null}]},{\"name\":\"Deluxe\",\"lrv\":0.01059,\"rateCode\":\"LV6\",\"rate\":null,\"overbooking\":null,\"children\":[{\"name\":\"K1X\",\"lrv\":null,\"rateCode\":\"LV6\",\"rate\":159,\"overbooking\":0,\"children\":null}]}]}"
    Assert.assertEquals(expectedResponse, badDailyOverviewDetails.getData().toString());
}

And(~/I query to Property level Details for the date range "([^"]*)" to "([^"]*)" which is partially in past and partially in future$/) { String startDate, String endDate ->
    HttpResponseDecorator badPropertyLevelDetails = getPropertyLevelDetails(startDate, endDate)
    String expectedResponse = "[{\"id\":10022,\"name\":\"Hilton Garden Inn - Tallahassee\",\"onBooks\":2473,\"systemDemand\":5.51,\"userDemand\":5.51,\"occupancyForecast\":2476.6,\"adrOnBooks\":120.92,\"adrForecast\":120.86,\"revenue\":299042.31,\"revenueForecast\":299308.08,\"revpar\":99.05,\"revparForecast\":99.14,\"lastYearOnBooks\":2357,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":2387,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":124.03,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":124.1,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":292338.03,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":296229.38,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":97.58,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":98.87,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":144,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":14248.18,\"adrPickUp\":-1.36,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":3069,\"effectiveCapacity\":3019}]"
    JSONAssert.assertEquals(expectedResponse, badPropertyLevelDetails.getData().toString());
}
And(~/I query to Business Type level Details for the date range "([^"]*)" to "([^"]*)" which is partially in past and partially in future$/) { String startDate, String endDate ->
    HttpResponseDecorator badBusinessTypeLevelDetails = getBusinessTypeLevelDetails(startDate, endDate)
    String expectedResponse = "[{\"id\":2,\"name\":\"Transient\",\"onBooks\":2358,\"systemDemand\":5.34,\"userDemand\":5.34,\"occupancyForecast\":2361.4,\"adrOnBooks\":122.02,\"adrForecast\":121.94,\"revenue\":287712.33,\"revenueForecast\":287960.36,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":2312,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":2343,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":123.61,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":123.63,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":285785.1,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":289676.45,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":144,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":14248.18,\"adrPickUp\":-1.5,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":1,\"name\":\"Group\",\"onBooks\":115,\"systemDemand\":0.17,\"userDemand\":0.17,\"occupancyForecast\":115.2,\"adrOnBooks\":98.52,\"adrForecast\":98.55,\"revenue\":11330,\"revenueForecast\":11347.74,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":67,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":67,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":99.15,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":99.15,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":6643,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":6643,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null}]"
    Assert.assertEquals(expectedResponse, badBusinessTypeLevelDetails.getData().toString());
}

And(~/I query to Business Type level Details with exclude mkt seg for the date range "([^"]*)" to "([^"]*)" which is partially in past and partially in future$/) { String startDate, String endDate ->
    HttpResponseDecorator badBusinessTypeLevelDetails = getBusinessTypeLevelDetailsExcludeMktSeg(startDate, endDate)
    String expectedResponse = "[{\"id\":2,\"name\":\"Transient\",\"onBooks\":2358,\"systemDemand\":5.34,\"userDemand\":5.34,\"occupancyForecast\":2361.4,\"adrOnBooks\":122.02,\"adrForecast\":121.94,\"revenue\":287712.33,\"revenueForecast\":287960.36,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":2312,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":2343,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":123.61,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":123.63,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":285785.1,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":289676.45,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":144,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":14248.18,\"adrPickUp\":-1.5,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":1,\"name\":\"Group\",\"onBooks\":114,\"systemDemand\":0.17,\"userDemand\":0.17,\"occupancyForecast\":114.1,\"adrOnBooks\":98.39,\"adrForecast\":98.4,\"revenue\":11216,\"revenueForecast\":11224.35,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":67,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":67,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":99.15,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":99.15,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":6643,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":6643,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null}]"
    JSONAssert.assertEquals(expectedResponse, badBusinessTypeLevelDetails.getData().toString());
}

And(~/I query to Forecast Group level details present under Transient Business for the date range "([^"]*)" to "([^"]*)" which is partially in past and partially in future$/) { String startDate, String endDate ->
    HttpResponseDecorator badForecastGroupDetailsUnderTransient = getForecastGroupDetailsUnderTransient(startDate, endDate)
    String expectedResponse = "[{\"id\":2,\"name\":\"FG_QUALIFIED_LINKED_NONYLD_NSB_1\",\"onBooks\":0,\"systemDemand\":0,\"userDemand\":0,\"occupancyForecast\":0,\"adrOnBooks\":0,\"adrForecast\":0,\"revenue\":0,\"revenueForecast\":0,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":3,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":3,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":104.67,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":104.67,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":314,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":314,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":3,\"name\":\"FG_QUALIFIED_LINKED_SEMIYLD_NSB_1\",\"onBooks\":420,\"systemDemand\":0,\"userDemand\":0,\"occupancyForecast\":419.9,\"adrOnBooks\":105.18,\"adrForecast\":105.18,\"revenue\":44174.73,\"revenueForecast\":44166.51,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":323,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":329,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":101.01,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":101.02,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":32624.99,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":33236.99,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":18,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":1559.67,\"adrPickUp\":-0.83,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":4,\"name\":\"FG_QUALIFIED_LINKED_YLD_NSB_1\",\"onBooks\":363,\"systemDemand\":0,\"userDemand\":0,\"occupancyForecast\":362.7,\"adrOnBooks\":120.53,\"adrForecast\":120.53,\"revenue\":43753.45,\"revenueForecast\":43718.02,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":358,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":370,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":119.15,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":119.53,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":42656.26,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":44224.36,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":38,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":5304.26,\"adrPickUp\":2.23,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":5,\"name\":\"FG_QUALIFIED_NONYLD_NSB_1\",\"onBooks\":48,\"systemDemand\":0.22,\"userDemand\":0.22,\"occupancyForecast\":48.2,\"adrOnBooks\":26.83,\"adrForecast\":26.83,\"revenue\":1288,\"revenueForecast\":1293.42,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":34,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":34,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":15,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":15,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":510,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":510,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":4,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":132,\"adrPickUp\":0.56,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":11,\"name\":\"FG_QUALIFIED_SEMIYLD_NSB_1\",\"onBooks\":75,\"systemDemand\":0.64,\"userDemand\":0.64,\"occupancyForecast\":75,\"adrOnBooks\":15,\"adrForecast\":15,\"revenue\":1125,\"revenueForecast\":1125,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":20,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":20,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":15,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":15,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":300,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":300,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":6,\"name\":\"FG_QUALIFIED_YLD_NSB_1\",\"onBooks\":274,\"systemDemand\":3.47,\"userDemand\":3.47,\"occupancyForecast\":277.2,\"adrOnBooks\":84.28,\"adrForecast\":84.26,\"revenue\":23092.47,\"revenueForecast\":23356.07,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":199,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":195,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":87.02,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":87.17,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":17316.07,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":16997.32,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":38,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":3249.85,\"adrPickUp\":0.2,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":7,\"name\":\"FG_QUALIFIED_YLD_NSB_2\",\"onBooks\":137,\"systemDemand\":1.01,\"userDemand\":1.01,\"occupancyForecast\":137.9,\"adrOnBooks\":108.53,\"adrForecast\":108.53,\"revenue\":14868,\"revenueForecast\":14970.29,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":187,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":191,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":104.51,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":104.45,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":19543,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":19949,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":6,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":669,\"adrPickUp\":0.14,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":8,\"name\":\"FG_TRANSIENT_BLOCK_NSB_1\",\"onBooks\":0,\"systemDemand\":0,\"userDemand\":0,\"occupancyForecast\":0,\"adrOnBooks\":0,\"adrForecast\":0,\"revenue\":0,\"revenueForecast\":0,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":0,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":0,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":0,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":12,\"name\":\"FG_UNQUALIFIED_NSB_1\",\"onBooks\":197,\"systemDemand\":0,\"userDemand\":0,\"occupancyForecast\":197,\"adrOnBooks\":139.63,\"adrForecast\":139.62,\"revenue\":27507.48,\"revenueForecast\":27500.17,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":243,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":245,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":120.5,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":120.9,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":29282.71,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":29620.71,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":11,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":1463.4,\"adrPickUp\":-0.39,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":9,\"name\":\"FG_UNQUALIFIED_SB_1\",\"onBooks\":844,\"systemDemand\":0,\"userDemand\":0,\"occupancyForecast\":843.5,\"adrOnBooks\":156.28,\"adrForecast\":156.3,\"revenue\":131903.2,\"revenueForecast\":131830.88,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":945,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":956,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":151.57,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":151.18,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":143238.07,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":144524.07,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":29,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":1870,\"adrPickUp\":-3.27,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null}]"
    Assert.assertEquals(expectedResponse, badForecastGroupDetailsUnderTransient.getData().toString());
}

And(~/I query to Forecast Group level details present under Transient Business with Excluded Mkt Seg for the date range "([^"]*)" to "([^"]*)" which is partially in past and partially in future$/) { String startDate, String endDate ->
    HttpResponseDecorator badForecastGroupDetailsUnderTransient = getForecastGroupDetailsUnderTransientWithExcludedMktSeg(startDate, endDate)
    String expectedResponse = "[{\"id\":2,\"name\":\"FG_QUALIFIED_LINKED_NONYLD_NSB_1\",\"onBooks\":0,\"systemDemand\":0,\"userDemand\":0,\"occupancyForecast\":0,\"adrOnBooks\":0,\"adrForecast\":0,\"revenue\":0,\"revenueForecast\":0,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":3,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":3,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":104.67,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":104.67,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":314,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":314,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":3,\"name\":\"FG_QUALIFIED_LINKED_SEMIYLD_NSB_1\",\"onBooks\":420,\"systemDemand\":0,\"userDemand\":0,\"occupancyForecast\":419.9,\"adrOnBooks\":105.18,\"adrForecast\":105.18,\"revenue\":44174.73,\"revenueForecast\":44166.51,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":323,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":329,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":101.01,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":101.02,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":32624.99,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":33236.99,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":18,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":1559.67,\"adrPickUp\":-0.83,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":4,\"name\":\"FG_QUALIFIED_LINKED_YLD_NSB_1\",\"onBooks\":363,\"systemDemand\":0,\"userDemand\":0,\"occupancyForecast\":362.7,\"adrOnBooks\":120.53,\"adrForecast\":120.53,\"revenue\":43753.45,\"revenueForecast\":43718.02,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":358,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":370,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":119.15,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":119.53,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":42656.26,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":44224.36,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":38,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":5304.26,\"adrPickUp\":2.23,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":5,\"name\":\"FG_QUALIFIED_NONYLD_NSB_1\",\"onBooks\":48,\"systemDemand\":0.22,\"userDemand\":0.22,\"occupancyForecast\":48.2,\"adrOnBooks\":26.83,\"adrForecast\":26.83,\"revenue\":1288,\"revenueForecast\":1293.42,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":34,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":34,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":15,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":15,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":510,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":510,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":4,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":132,\"adrPickUp\":0.56,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":11,\"name\":\"FG_QUALIFIED_SEMIYLD_NSB_1\",\"onBooks\":75,\"systemDemand\":0.64,\"userDemand\":0.64,\"occupancyForecast\":75,\"adrOnBooks\":15,\"adrForecast\":15,\"revenue\":1125,\"revenueForecast\":1125,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":20,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":20,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":15,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":15,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":300,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":300,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":6,\"name\":\"FG_QUALIFIED_YLD_NSB_1\",\"onBooks\":274,\"systemDemand\":3.47,\"userDemand\":3.47,\"occupancyForecast\":277.2,\"adrOnBooks\":84.28,\"adrForecast\":84.26,\"revenue\":23092.47,\"revenueForecast\":23356.07,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":199,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":195,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":87.02,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":87.17,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":17316.07,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":16997.32,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":38,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":3249.85,\"adrPickUp\":0.2,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":7,\"name\":\"FG_QUALIFIED_YLD_NSB_2\",\"onBooks\":137,\"systemDemand\":1.01,\"userDemand\":1.01,\"occupancyForecast\":137.9,\"adrOnBooks\":108.53,\"adrForecast\":108.53,\"revenue\":14868,\"revenueForecast\":14970.29,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":187,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":191,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":104.51,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":104.45,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":19543,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":19949,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":6,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":669,\"adrPickUp\":0.14,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":8,\"name\":\"FG_TRANSIENT_BLOCK_NSB_1\",\"onBooks\":0,\"systemDemand\":0,\"userDemand\":0,\"occupancyForecast\":0,\"adrOnBooks\":0,\"adrForecast\":0,\"revenue\":0,\"revenueForecast\":0,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":0,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":0,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":0,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":12,\"name\":\"FG_UNQUALIFIED_NSB_1\",\"onBooks\":197,\"systemDemand\":0,\"userDemand\":0,\"occupancyForecast\":197,\"adrOnBooks\":139.63,\"adrForecast\":139.62,\"revenue\":27507.48,\"revenueForecast\":27500.17,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":243,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":245,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":120.5,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":120.9,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":29282.71,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":29620.71,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":11,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":1463.4,\"adrPickUp\":-0.39,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":9,\"name\":\"FG_UNQUALIFIED_SB_1\",\"onBooks\":844,\"systemDemand\":0,\"userDemand\":0,\"occupancyForecast\":843.5,\"adrOnBooks\":156.28,\"adrForecast\":156.3,\"revenue\":131903.2,\"revenueForecast\":131830.88,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":945,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":956,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":151.57,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":151.18,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":143238.07,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":144524.07,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":29,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":1870,\"adrPickUp\":-3.27,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null}]"
    JSONAssert.assertEquals(expectedResponse, badForecastGroupDetailsUnderTransient.getData().toString());

}

And(~/I query to Forecast Group level details present under Group Business for the date range "([^"]*)" to "([^"]*)" which is partially in past and partially in future$/) { String startDate, String endDate ->
    HttpResponseDecorator badForecastGroupDetailsUnderGroup = getForecastGroupDetailsUnderGroup(startDate, endDate)
    String expectedJson = '[{"id":10,"name":"FG_GROUP_NSB_1","onBooks":115,"systemDemand":0.17,"userDemand":0.17,"occupancyForecast":115.2,"adrOnBooks":98.52,"adrForecast":98.55,"revenue":11330,"revenueForecast":11347.74,"revpar":null,"revparForecast":null,"lastYearOnBooks":67,"last2YearsOnBooks":null,"year2019OnBooks":null,"lastYearSystemDemand":null,"last2YearsSystemDemand":null,"year2019SystemDemand":null,"lastYearUserDemand":null,"last2YearsUserDemand":null,"year2019UserDemand":null,"lastYearOccupancyForecast":67,"last2YearsOccupancyForecast":null,"year2019OccupancyForecast":null,"lastYearAdrOnBooks":99.15,"last2YearsAdrOnBooks":null,"year2019AdrOnBooks":null,"lastYearAdrForecast":99.15,"last2YearsAdrForecast":null,"year2019AdrForecast":null,"lastYearRevenue":6643,"last2YearsRevenue":null,"year2019Revenue":null,"lastYearRevenueForecast":6643,"last2YearsRevenueForecast":null,"year2019RevenueForecast":null,"lastYearRevpar":null,"last2YearsRevpar":null,"year2019Revpar":null,"lastYearRevparForecast":null,"last2YearsRevparForecast":null,"year2019RevparForecast":null,"onBooksPickUp":0,"budgetRooms":null,"budgetRevenue":null,"budgetAdr":null,"budgetRevpar":null,"userForecastRooms":null,"userForecastRevenue":null,"userForecastAdr":null,"userForecastRevpar":null,"viewOrder":null,"revenuePickUp":0,"adrPickUp":0,"proPOROnBooks":null,"lastYearProPOROnBooks":null,"lastYearProPORForecast":null,"last2YearsProPOROnBooks":null,"last2YearsProPORForecast":null,"year2019ProPOROnBooks":null,"year2019ProPORForecast":null,"proPORForecast":null,"profitOnBooks":null,"lastYearProfitOnBooks":null,"lastYearProfitForecast":null,"last2YearsProfitOnBooks":null,"last2YearsProfitForecast":null,"year2019ProfitOnBooks":null,"year2019ProfitForecast":null,"profitForecast":null,"proPAROnBooks":null,"lastYearProPAROnBooks":null,"lastYearProPARForecast":null,"last2YearsProPAROnBooks":null,"last2YearsProPARForecast":null,"year2019ProPAROnBooks":null,"year2019ProPARForecast":null,"proPARForecast":null,"capacity":null,"capacityForecast":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{"id":1,"name":"FG_NOFCST_NSB_1","onBooks":0,"systemDemand":null,"userDemand":null,"occupancyForecast":0,"adrOnBooks":0,"adrForecast":0,"revenue":0,"revenueForecast":0,"revpar":null,"revparForecast":null,"lastYearOnBooks":0,"last2YearsOnBooks":null,"year2019OnBooks":null,"lastYearSystemDemand":null,"last2YearsSystemDemand":null,"year2019SystemDemand":null,"lastYearUserDemand":null,"last2YearsUserDemand":null,"year2019UserDemand":null,"lastYearOccupancyForecast":0,"last2YearsOccupancyForecast":null,"year2019OccupancyForecast":null,"lastYearAdrOnBooks":0,"last2YearsAdrOnBooks":null,"year2019AdrOnBooks":null,"lastYearAdrForecast":0,"last2YearsAdrForecast":null,"year2019AdrForecast":null,"lastYearRevenue":0,"last2YearsRevenue":null,"year2019Revenue":null,"lastYearRevenueForecast":0,"last2YearsRevenueForecast":null,"year2019RevenueForecast":null,"lastYearRevpar":null,"last2YearsRevpar":null,"year2019Revpar":null,"lastYearRevparForecast":null,"last2YearsRevparForecast":null,"year2019RevparForecast":null,"onBooksPickUp":0,"budgetRooms":null,"budgetRevenue":null,"budgetAdr":null,"budgetRevpar":null,"userForecastRooms":null,"userForecastRevenue":null,"userForecastAdr":null,"userForecastRevpar":null,"viewOrder":null,"revenuePickUp":0,"adrPickUp":0,"proPOROnBooks":null,"lastYearProPOROnBooks":null,"lastYearProPORForecast":null,"last2YearsProPOROnBooks":null,"last2YearsProPORForecast":null,"year2019ProPOROnBooks":null,"year2019ProPORForecast":null,"proPORForecast":null,"profitOnBooks":null,"lastYearProfitOnBooks":null,"lastYearProfitForecast":null,"last2YearsProfitOnBooks":null,"last2YearsProfitForecast":null,"year2019ProfitOnBooks":null,"year2019ProfitForecast":null,"profitForecast":null,"proPAROnBooks":null,"lastYearProPAROnBooks":null,"lastYearProPARForecast":null,"last2YearsProPAROnBooks":null,"last2YearsProPARForecast":null,"year2019ProPAROnBooks":null,"year2019ProPARForecast":null,"proPARForecast":null,"capacity":null,"capacityForecast":null,\"physicalCapacity\":null,\"effectiveCapacity\":null}]';
    println(badForecastGroupDetailsUnderGroup.getData().toString());
    JSONAssert.assertEquals(expectedJson, badForecastGroupDetailsUnderGroup.getData());
}

And(~/I query to Room Class level details for the date range "([^"]*)" to "([^"]*)" which is partially in past and partially in future$/) { String startDate, String endDate ->
    HttpResponseDecorator badRoomClassLevelDetails = getRoomClassLevelDetails(startDate, endDate)
    String expectedResponse = '[{"id":2,"name":"Standard","onBooks":2323,"systemDemand":5.03,"userDemand":5.03,"occupancyForecast":2326.2,"adrOnBooks":119.82,"adrForecast":119.75,"revenue":278335.64,"revenueForecast":278566.09,"revpar":98.14,"revparForecast":98.22,"lastYearOnBooks":2246,"last2YearsOnBooks":null,"year2019OnBooks":null,"lastYearSystemDemand":null,"last2YearsSystemDemand":null,"year2019SystemDemand":null,"lastYearUserDemand":null,"last2YearsUserDemand":null,"year2019UserDemand":null,"lastYearOccupancyForecast":2274,"last2YearsOccupancyForecast":null,"year2019OccupancyForecast":null,"lastYearAdrOnBooks":123.03,"last2YearsAdrOnBooks":null,"year2019AdrOnBooks":null,"lastYearAdrForecast":123.09,"last2YearsAdrForecast":null,"year2019AdrForecast":null,"lastYearRevenue":276324.61,"last2YearsRevenue":null,"year2019Revenue":null,"lastYearRevenueForecast":279897.96,"last2YearsRevenueForecast":null,"year2019RevenueForecast":null,"lastYearRevpar":97.23,"last2YearsRevpar":null,"year2019Revpar":null,"lastYearRevparForecast":98.49,"last2YearsRevparForecast":null,"year2019RevparForecast":null,"onBooksPickUp":122,"budgetRooms":null,"budgetRevenue":null,"budgetAdr":null,"budgetRevpar":null,"userForecastRooms":null,"userForecastRevenue":null,"userForecastAdr":null,"userForecastRevpar":null,"viewOrder":1,"revenuePickUp":11187.6,"adrPickUp":-1.56,"proPOROnBooks":null,"lastYearProPOROnBooks":null,"lastYearProPORForecast":null,"last2YearsProPOROnBooks":null,"last2YearsProPORForecast":null,"year2019ProPOROnBooks":null,"year2019ProPORForecast":null,"proPORForecast":null,"profitOnBooks":null,"lastYearProfitOnBooks":null,"lastYearProfitForecast":null,"last2YearsProfitOnBooks":null,"last2YearsProfitForecast":null,"year2019ProfitOnBooks":null,"year2019ProfitForecast":null,"profitForecast":null,"proPAROnBooks":null,"lastYearProPAROnBooks":null,"lastYearProPARForecast":null,"last2YearsProPAROnBooks":null,"last2YearsProPARForecast":null,"year2019ProPAROnBooks":null,"year2019ProPARForecast":null,"proPARForecast":null,"capacity":null,"capacityForecast":null,"physicalCapacity":2883,"effectiveCapacity":2836},{"id":3,"name":"Deluxe","onBooks":150,"systemDemand":0.48,"userDemand":0.48,"occupancyForecast":150.4,"adrOnBooks":138.04,"adrForecast":137.89,"revenue":20706.69,"revenueForecast":20742.01,"revpar":113.15,"revparForecast":113.34,"lastYearOnBooks":111,"last2YearsOnBooks":null,"year2019OnBooks":null,"lastYearSystemDemand":null,"last2YearsSystemDemand":null,"year2019SystemDemand":null,"lastYearUserDemand":null,"last2YearsUserDemand":null,"year2019UserDemand":null,"lastYearOccupancyForecast":113,"last2YearsOccupancyForecast":null,"year2019OccupancyForecast":null,"lastYearAdrOnBooks":144.27,"last2YearsAdrOnBooks":null,"year2019AdrOnBooks":null,"lastYearAdrForecast":144.53,"last2YearsAdrForecast":null,"year2019AdrForecast":null,"lastYearRevenue":16013.48,"last2YearsRevenue":null,"year2019Revenue":null,"lastYearRevenueForecast":16331.48,"last2YearsRevenueForecast":null,"year2019RevenueForecast":null,"lastYearRevpar":103.98,"last2YearsRevpar":null,"year2019Revpar":null,"lastYearRevparForecast":106.05,"last2YearsRevparForecast":null,"year2019RevparForecast":null,"onBooksPickUp":22,"budgetRooms":null,"budgetRevenue":null,"budgetAdr":null,"budgetRevpar":null,"userForecastRooms":null,"userForecastRevenue":null,"userForecastAdr":null,"userForecastRevpar":null,"viewOrder":2,"revenuePickUp":3060.58,"adrPickUp":0.18,"proPOROnBooks":null,"lastYearProPOROnBooks":null,"lastYearProPORForecast":null,"last2YearsProPOROnBooks":null,"last2YearsProPORForecast":null,"year2019ProPOROnBooks":null,"year2019ProPORForecast":null,"proPORForecast":null,"profitOnBooks":null,"lastYearProfitOnBooks":null,"lastYearProfitForecast":null,"last2YearsProfitOnBooks":null,"last2YearsProfitForecast":null,"year2019ProfitOnBooks":null,"year2019ProfitForecast":null,"profitForecast":null,"proPAROnBooks":null,"lastYearProPAROnBooks":null,"lastYearProPARForecast":null,"last2YearsProPAROnBooks":null,"last2YearsProPARForecast":null,"year2019ProPAROnBooks":null,"year2019ProPARForecast":null,"proPARForecast":null,"capacity":null,"capacityForecast":null,"physicalCapacity":186,"effectiveCapacity":183}]'
    println(badRoomClassLevelDetails.getData().toString());
    JSONAssert.assertEquals(expectedResponse, badRoomClassLevelDetails.getData());
}

And(~/I query to Room Class level details for the date range "([^"]*)" to "([^"]*)" which is partially in past and partially in future for "([^"]*)" Room Types Only$/) { String startDate, String endDate ,String roomTypeStatus->
    HttpResponseDecorator badRoomClassLevelDetails = getRoomClassLevelDetailsWithRoomTypeStatus(startDate, endDate,roomTypeStatus)
    String expectedResponse = '[{"id":2,"name":"Standard","onBooks":1515,"systemDemand":5.03,"userDemand":5.03,"occupancyForecast":1515.6,"adrOnBooks":119.02,"adrForecast":118.99,"revenue":180314.29,"revenueForecast":180333.75,"revpar":104.05,"revparForecast":104.06,"lastYearOnBooks":1495,"last2YearsOnBooks":null,"year2019OnBooks":null,"lastYearSystemDemand":null,"last2YearsSystemDemand":null,"year2019SystemDemand":null,"lastYearUserDemand":null,"last2YearsUserDemand":null,"year2019UserDemand":null,"lastYearOccupancyForecast":1512,"last2YearsOccupancyForecast":null,"year2019OccupancyForecast":null,"lastYearAdrOnBooks":121.64,"last2YearsAdrOnBooks":null,"year2019AdrOnBooks":null,"lastYearAdrForecast":121.41,"last2YearsAdrForecast":null,"year2019AdrForecast":null,"lastYearRevenue":181856.83,"last2YearsRevenue":null,"year2019Revenue":null,"lastYearRevenueForecast":183571.18,"last2YearsRevenueForecast":null,"year2019RevenueForecast":null,"lastYearRevpar":105,"last2YearsRevpar":null,"year2019Revpar":null,"lastYearRevparForecast":105.99,"last2YearsRevparForecast":null,"year2019RevparForecast":null,"onBooksPickUp":71,"budgetRooms":null,"budgetRevenue":null,"budgetAdr":null,"budgetRevpar":null,"userForecastRooms":null,"userForecastRevenue":null,"userForecastAdr":null,"userForecastRevpar":null,"viewOrder":1,"revenuePickUp":7544.09,"adrPickUp":-0.63,"proPOROnBooks":null,"lastYearProPOROnBooks":null,"lastYearProPORForecast":null,"last2YearsProPOROnBooks":null,"last2YearsProPORForecast":null,"year2019ProPOROnBooks":null,"year2019ProPORForecast":null,"proPORForecast":null,"profitOnBooks":null,"lastYearProfitOnBooks":null,"lastYearProfitForecast":null,"last2YearsProfitOnBooks":null,"last2YearsProfitForecast":null,"year2019ProfitOnBooks":null,"year2019ProfitForecast":null,"profitForecast":null,"proPAROnBooks":null,"lastYearProPAROnBooks":null,"lastYearProPARForecast":null,"last2YearsProPAROnBooks":null,"last2YearsProPARForecast":null,"year2019ProPAROnBooks":null,"year2019ProPARForecast":null,"proPARForecast":null,"capacity":null,"capacityForecast":null,"physicalCapacity":1767,"effectiveCapacity":1733},{"id":3,"name":"Deluxe","onBooks":150,"systemDemand":0.48,"userDemand":0.48,"occupancyForecast":150.4,"adrOnBooks":138.04,"adrForecast":137.89,"revenue":20706.69,"revenueForecast":20742.01,"revpar":113.15,"revparForecast":113.34,"lastYearOnBooks":111,"last2YearsOnBooks":null,"year2019OnBooks":null,"lastYearSystemDemand":null,"last2YearsSystemDemand":null,"year2019SystemDemand":null,"lastYearUserDemand":null,"last2YearsUserDemand":null,"year2019UserDemand":null,"lastYearOccupancyForecast":113,"last2YearsOccupancyForecast":null,"year2019OccupancyForecast":null,"lastYearAdrOnBooks":144.27,"last2YearsAdrOnBooks":null,"year2019AdrOnBooks":null,"lastYearAdrForecast":144.53,"last2YearsAdrForecast":null,"year2019AdrForecast":null,"lastYearRevenue":16013.48,"last2YearsRevenue":null,"year2019Revenue":null,"lastYearRevenueForecast":16331.48,"last2YearsRevenueForecast":null,"year2019RevenueForecast":null,"lastYearRevpar":103.98,"last2YearsRevpar":null,"year2019Revpar":null,"lastYearRevparForecast":106.05,"last2YearsRevparForecast":null,"year2019RevparForecast":null,"onBooksPickUp":22,"budgetRooms":null,"budgetRevenue":null,"budgetAdr":null,"budgetRevpar":null,"userForecastRooms":null,"userForecastRevenue":null,"userForecastAdr":null,"userForecastRevpar":null,"viewOrder":2,"revenuePickUp":3060.58,"adrPickUp":0.18,"proPOROnBooks":null,"lastYearProPOROnBooks":null,"lastYearProPORForecast":null,"last2YearsProPOROnBooks":null,"last2YearsProPORForecast":null,"year2019ProPOROnBooks":null,"year2019ProPORForecast":null,"proPORForecast":null,"profitOnBooks":null,"lastYearProfitOnBooks":null,"lastYearProfitForecast":null,"last2YearsProfitOnBooks":null,"last2YearsProfitForecast":null,"year2019ProfitOnBooks":null,"year2019ProfitForecast":null,"profitForecast":null,"proPAROnBooks":null,"lastYearProPAROnBooks":null,"lastYearProPARForecast":null,"last2YearsProPAROnBooks":null,"last2YearsProPARForecast":null,"year2019ProPAROnBooks":null,"year2019ProPARForecast":null,"proPARForecast":null,"capacity":null,"capacityForecast":null,"physicalCapacity":186,"effectiveCapacity":183}]'
    println(badRoomClassLevelDetails.getData().toString());
    JSONAssert.assertEquals(expectedResponse, badRoomClassLevelDetails.getData());
}


And(~/I query to Room Class level details for the date range "([^"]*)" to "([^"]*)" which is partially in past and partially in future for "([^"]*)" Room Types$/) { String startDate, String endDate, String roomTypeStatus ->
    HttpResponseDecorator badRoomClassLevelDetails = getRoomClassLevelDetailsWithRoomTypeStatus(startDate, endDate, roomTypeStatus)
    String expectedResponse = '[{"id":2,"name":"Standard","onBooks":2323,"systemDemand":5.03,"userDemand":5.03,"occupancyForecast":2326.2,"adrOnBooks":119.82,"adrForecast":119.75,"revenue":278335.64,"revenueForecast":278566.09,"revpar":98.14,"revparForecast":98.22,"lastYearOnBooks":2246,"last2YearsOnBooks":null,"year2019OnBooks":null,"lastYearSystemDemand":null,"last2YearsSystemDemand":null,"year2019SystemDemand":null,"lastYearUserDemand":null,"last2YearsUserDemand":null,"year2019UserDemand":null,"lastYearOccupancyForecast":2274,"last2YearsOccupancyForecast":null,"year2019OccupancyForecast":null,"lastYearAdrOnBooks":123.03,"last2YearsAdrOnBooks":null,"year2019AdrOnBooks":null,"lastYearAdrForecast":123.09,"last2YearsAdrForecast":null,"year2019AdrForecast":null,"lastYearRevenue":276324.61,"last2YearsRevenue":null,"year2019Revenue":null,"lastYearRevenueForecast":279897.96,"last2YearsRevenueForecast":null,"year2019RevenueForecast":null,"lastYearRevpar":97.23,"last2YearsRevpar":null,"year2019Revpar":null,"lastYearRevparForecast":98.49,"last2YearsRevparForecast":null,"year2019RevparForecast":null,"onBooksPickUp":122,"budgetRooms":null,"budgetRevenue":null,"budgetAdr":null,"budgetRevpar":null,"userForecastRooms":null,"userForecastRevenue":null,"userForecastAdr":null,"userForecastRevpar":null,"viewOrder":1,"revenuePickUp":11187.6,"adrPickUp":-1.56,"proPOROnBooks":null,"lastYearProPOROnBooks":null,"lastYearProPORForecast":null,"last2YearsProPOROnBooks":null,"last2YearsProPORForecast":null,"year2019ProPOROnBooks":null,"year2019ProPORForecast":null,"proPORForecast":null,"profitOnBooks":null,"lastYearProfitOnBooks":null,"lastYearProfitForecast":null,"last2YearsProfitOnBooks":null,"last2YearsProfitForecast":null,"year2019ProfitOnBooks":null,"year2019ProfitForecast":null,"profitForecast":null,"proPAROnBooks":null,"lastYearProPAROnBooks":null,"lastYearProPARForecast":null,"last2YearsProPAROnBooks":null,"last2YearsProPARForecast":null,"year2019ProPAROnBooks":null,"year2019ProPARForecast":null,"proPARForecast":null,"capacity":null,"capacityForecast":null,"physicalCapacity":2883,"effectiveCapacity":2836},{"id":3,"name":"Deluxe","onBooks":150,"systemDemand":0.48,"userDemand":0.48,"occupancyForecast":150.4,"adrOnBooks":138.04,"adrForecast":137.89,"revenue":20706.69,"revenueForecast":20742.01,"revpar":113.15,"revparForecast":113.34,"lastYearOnBooks":111,"last2YearsOnBooks":null,"year2019OnBooks":null,"lastYearSystemDemand":null,"last2YearsSystemDemand":null,"year2019SystemDemand":null,"lastYearUserDemand":null,"last2YearsUserDemand":null,"year2019UserDemand":null,"lastYearOccupancyForecast":113,"last2YearsOccupancyForecast":null,"year2019OccupancyForecast":null,"lastYearAdrOnBooks":144.27,"last2YearsAdrOnBooks":null,"year2019AdrOnBooks":null,"lastYearAdrForecast":144.53,"last2YearsAdrForecast":null,"year2019AdrForecast":null,"lastYearRevenue":16013.48,"last2YearsRevenue":null,"year2019Revenue":null,"lastYearRevenueForecast":16331.48,"last2YearsRevenueForecast":null,"year2019RevenueForecast":null,"lastYearRevpar":103.98,"last2YearsRevpar":null,"year2019Revpar":null,"lastYearRevparForecast":106.05,"last2YearsRevparForecast":null,"year2019RevparForecast":null,"onBooksPickUp":22,"budgetRooms":null,"budgetRevenue":null,"budgetAdr":null,"budgetRevpar":null,"userForecastRooms":null,"userForecastRevenue":null,"userForecastAdr":null,"userForecastRevpar":null,"viewOrder":2,"revenuePickUp":3060.58,"adrPickUp":0.18,"proPOROnBooks":null,"lastYearProPOROnBooks":null,"lastYearProPORForecast":null,"last2YearsProPOROnBooks":null,"last2YearsProPORForecast":null,"year2019ProPOROnBooks":null,"year2019ProPORForecast":null,"proPORForecast":null,"profitOnBooks":null,"lastYearProfitOnBooks":null,"lastYearProfitForecast":null,"last2YearsProfitOnBooks":null,"last2YearsProfitForecast":null,"year2019ProfitOnBooks":null,"year2019ProfitForecast":null,"profitForecast":null,"proPAROnBooks":null,"lastYearProPAROnBooks":null,"lastYearProPARForecast":null,"last2YearsProPAROnBooks":null,"last2YearsProPARForecast":null,"year2019ProPAROnBooks":null,"year2019ProPARForecast":null,"proPARForecast":null,"capacity":null,"capacityForecast":null,"physicalCapacity":186,"effectiveCapacity":183}]';
    println(badRoomClassLevelDetails.getData().toString());
    JSONAssert.assertEquals(expectedResponse, badRoomClassLevelDetails.getData());
}

And(~/I query to Room Class level details with exclude mkt seg for the date range "([^"]*)" to "([^"]*)" which is partially in past and partially in future$/) { String startDate, String endDate ->
    HttpResponseDecorator badRoomClassLevelDetails = getRoomClassLevelDetails(startDate, endDate)
    String expectedResponse = '[{"id":2,"name":"Standard","onBooks":2323,"systemDemand":5.03,"userDemand":5.03,"occupancyForecast":2326.2,"adrOnBooks":119.82,"adrForecast":119.75,"revenue":278335.64,"revenueForecast":278566.09,"revpar":98.14,"revparForecast":98.22,"lastYearOnBooks":2246,"last2YearsOnBooks":null,"year2019OnBooks":null,"lastYearSystemDemand":null,"last2YearsSystemDemand":null,"year2019SystemDemand":null,"lastYearUserDemand":null,"last2YearsUserDemand":null,"year2019UserDemand":null,"lastYearOccupancyForecast":2274,"last2YearsOccupancyForecast":null,"year2019OccupancyForecast":null,"lastYearAdrOnBooks":123.03,"last2YearsAdrOnBooks":null,"year2019AdrOnBooks":null,"lastYearAdrForecast":123.09,"last2YearsAdrForecast":null,"year2019AdrForecast":null,"lastYearRevenue":276324.61,"last2YearsRevenue":null,"year2019Revenue":null,"lastYearRevenueForecast":279897.96,"last2YearsRevenueForecast":null,"year2019RevenueForecast":null,"lastYearRevpar":97.23,"last2YearsRevpar":null,"year2019Revpar":null,"lastYearRevparForecast":98.49,"last2YearsRevparForecast":null,"year2019RevparForecast":null,"onBooksPickUp":122,"budgetRooms":null,"budgetRevenue":null,"budgetAdr":null,"budgetRevpar":null,"userForecastRooms":null,"userForecastRevenue":null,"userForecastAdr":null,"userForecastRevpar":null,"viewOrder":1,"revenuePickUp":11187.6,"adrPickUp":-1.56,"proPOROnBooks":null,"lastYearProPOROnBooks":null,"lastYearProPORForecast":null,"last2YearsProPOROnBooks":null,"last2YearsProPORForecast":null,"year2019ProPOROnBooks":null,"year2019ProPORForecast":null,"proPORForecast":null,"profitOnBooks":null,"lastYearProfitOnBooks":null,"lastYearProfitForecast":null,"last2YearsProfitOnBooks":null,"last2YearsProfitForecast":null,"year2019ProfitOnBooks":null,"year2019ProfitForecast":null,"profitForecast":null,"proPAROnBooks":null,"lastYearProPAROnBooks":null,"lastYearProPARForecast":null,"last2YearsProPAROnBooks":null,"last2YearsProPARForecast":null,"year2019ProPAROnBooks":null,"year2019ProPARForecast":null,"proPARForecast":null,"capacity":null,"capacityForecast":null,"physicalCapacity":2883,"effectiveCapacity":2836},{"id":3,"name":"Deluxe","onBooks":150,"systemDemand":0.48,"userDemand":0.48,"occupancyForecast":150.4,"adrOnBooks":138.04,"adrForecast":137.89,"revenue":20706.69,"revenueForecast":20742.01,"revpar":113.15,"revparForecast":113.34,"lastYearOnBooks":111,"last2YearsOnBooks":null,"year2019OnBooks":null,"lastYearSystemDemand":null,"last2YearsSystemDemand":null,"year2019SystemDemand":null,"lastYearUserDemand":null,"last2YearsUserDemand":null,"year2019UserDemand":null,"lastYearOccupancyForecast":113,"last2YearsOccupancyForecast":null,"year2019OccupancyForecast":null,"lastYearAdrOnBooks":144.27,"last2YearsAdrOnBooks":null,"year2019AdrOnBooks":null,"lastYearAdrForecast":144.53,"last2YearsAdrForecast":null,"year2019AdrForecast":null,"lastYearRevenue":16013.48,"last2YearsRevenue":null,"year2019Revenue":null,"lastYearRevenueForecast":16331.48,"last2YearsRevenueForecast":null,"year2019RevenueForecast":null,"lastYearRevpar":103.98,"last2YearsRevpar":null,"year2019Revpar":null,"lastYearRevparForecast":106.05,"last2YearsRevparForecast":null,"year2019RevparForecast":null,"onBooksPickUp":22,"budgetRooms":null,"budgetRevenue":null,"budgetAdr":null,"budgetRevpar":null,"userForecastRooms":null,"userForecastRevenue":null,"userForecastAdr":null,"userForecastRevpar":null,"viewOrder":2,"revenuePickUp":3060.58,"adrPickUp":0.18,"proPOROnBooks":null,"lastYearProPOROnBooks":null,"lastYearProPORForecast":null,"last2YearsProPOROnBooks":null,"last2YearsProPORForecast":null,"year2019ProPOROnBooks":null,"year2019ProPORForecast":null,"proPORForecast":null,"profitOnBooks":null,"lastYearProfitOnBooks":null,"lastYearProfitForecast":null,"last2YearsProfitOnBooks":null,"last2YearsProfitForecast":null,"year2019ProfitOnBooks":null,"year2019ProfitForecast":null,"profitForecast":null,"proPAROnBooks":null,"lastYearProPAROnBooks":null,"lastYearProPARForecast":null,"last2YearsProPAROnBooks":null,"last2YearsProPARForecast":null,"year2019ProPAROnBooks":null,"year2019ProPARForecast":null,"proPARForecast":null,"capacity":null,"capacityForecast":null,"physicalCapacity":186,"effectiveCapacity":183}]';
    println(badRoomClassLevelDetails.getData().toString());
    JSONAssert.assertEquals(expectedResponse, badRoomClassLevelDetails.getData());
}

And(~/I query to Room Type level details under "Standard" Room Class for the date range "([^"]*)" to "([^"]*)" which is partially in past and partially in future$/) { String startDate, String endDate ->
    HttpResponseDecorator badRoomTypeLevelDetailsUnderSTD = getRoomTypeLevelDetailsUnderSTD(startDate, endDate)
    String expectedResponse = "[{\"id\":1,\"name\":\"HOUS\",\"onBooks\":0,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":0,\"adrOnBooks\":0,\"adrForecast\":0,\"revenue\":0,\"revenueForecast\":0,\"revpar\":0,\"revparForecast\":0,\"lastYearOnBooks\":0,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":0,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":0,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":0,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":0,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":0,\"effectiveCapacity\":0},{\"id\":2,\"name\":\"K1\",\"onBooks\":1417,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":1417.1,\"adrOnBooks\":120.26,\"adrForecast\":120.23,\"revenue\":170404.11,\"revenueForecast\":170380.85,\"revpar\":107.85,\"revparForecast\":107.84,\"lastYearOnBooks\":1428,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":1442,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":121.93,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":121.65,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":174117.53,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":175414.88,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":108.28,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":109.09,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":73,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":7761.09,\"adrPickUp\":-0.76,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":1612,\"effectiveCapacity\":1580},{\"id\":3,\"name\":\"K1RRC\",\"onBooks\":79,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":79.4,\"adrOnBooks\":97.07,\"adrForecast\":97.01,\"revenue\":7668.3,\"revenueForecast\":7699.92,\"revpar\":62.85,\"revparForecast\":63.11,\"lastYearOnBooks\":67,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":70,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":115.51,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":116.52,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":7739.3,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":8156.3,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":62.41,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":65.78,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":-5,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":-614,\"adrPickUp\":-1.53,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":124,\"effectiveCapacity\":122},{\"id\":5,\"name\":\"Q2\",\"onBooks\":808,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":810.6,\"adrOnBooks\":121.31,\"adrForecast\":121.19,\"revenue\":98021.35,\"revenueForecast\":98232.34,\"revpar\":88.87,\"revparForecast\":89.06,\"lastYearOnBooks\":751,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":762,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":125.79,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":126.41,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":94467.78,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":96326.78,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":85.11,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":86.78,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":51,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":3643.51,\"adrPickUp\":-3.36,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":1116,\"effectiveCapacity\":1103},{\"id\":6,\"name\":\"RR\",\"onBooks\":0,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":0,\"adrOnBooks\":0,\"adrForecast\":0,\"revenue\":0,\"revenueForecast\":0,\"revpar\":0,\"revparForecast\":0,\"lastYearOnBooks\":0,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":0,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":0,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":0,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":0,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":0,\"effectiveCapacity\":0},{\"id\":7,\"name\":\"K1RRD\",\"onBooks\":19,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":19.1,\"adrOnBooks\":117.99,\"adrForecast\":117.9,\"revenue\":2241.88,\"revenueForecast\":2252.98,\"revpar\":72.32,\"revparForecast\":72.68,\"lastYearOnBooks\":0,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":0,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":0,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":0,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":0,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":3,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":397,\"adrPickUp\":2.69,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":31,\"effectiveCapacity\":31}]"
    Assert.assertEquals(expectedResponse, badRoomTypeLevelDetailsUnderSTD.getData().toString());
}

And(~/I query to Room Type level details under "Standard" Room Class exclude comp room for the date range "([^"]*)" to "([^"]*)" which is partially in past and partially in future$/) { String startDate, String endDate ->
    HttpResponseDecorator badRoomTypeLevelDetailsUnderSTD = getRoomTypeLevelDetailsUnderSTD(startDate, endDate)
    String expectedResponse = "[{\"id\":1,\"name\":\"HOUS\",\"onBooks\":0,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":0,\"adrOnBooks\":0,\"adrForecast\":0,\"revenue\":0,\"revenueForecast\":0,\"revpar\":0,\"revparForecast\":0,\"lastYearOnBooks\":0,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":0,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":0,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":0,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":0,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":0,\"effectiveCapacity\":0},{\"id\":2,\"name\":\"K1\",\"onBooks\":1417,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":1417.1,\"adrOnBooks\":120.26,\"adrForecast\":120.23,\"revenue\":170404.11,\"revenueForecast\":170380.85,\"revpar\":107.85,\"revparForecast\":107.84,\"lastYearOnBooks\":1428,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":1442,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":121.93,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":121.65,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":174117.53,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":175414.88,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":108.28,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":109.09,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":73,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":7761.09,\"adrPickUp\":-0.76,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":1612,\"effectiveCapacity\":1580},{\"id\":3,\"name\":\"K1RRC\",\"onBooks\":79,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":79.4,\"adrOnBooks\":97.07,\"adrForecast\":97.01,\"revenue\":7668.3,\"revenueForecast\":7699.92,\"revpar\":62.85,\"revparForecast\":63.11,\"lastYearOnBooks\":67,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":70,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":115.51,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":116.52,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":7739.3,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":8156.3,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":62.41,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":65.78,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":-5,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":-614,\"adrPickUp\":-1.53,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":124,\"effectiveCapacity\":122},{\"id\":5,\"name\":\"Q2\",\"onBooks\":808,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":810.6,\"adrOnBooks\":121.31,\"adrForecast\":121.19,\"revenue\":98021.35,\"revenueForecast\":98232.34,\"revpar\":88.87,\"revparForecast\":89.06,\"lastYearOnBooks\":751,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":762,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":125.79,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":126.41,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":94467.78,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":96326.78,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":85.11,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":86.78,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":51,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":3643.51,\"adrPickUp\":-3.36,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":1116,\"effectiveCapacity\":1103},{\"id\":6,\"name\":\"RR\",\"onBooks\":0,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":0,\"adrOnBooks\":0,\"adrForecast\":0,\"revenue\":0,\"revenueForecast\":0,\"revpar\":0,\"revparForecast\":0,\"lastYearOnBooks\":0,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":0,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":0,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":0,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":0,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":0,\"effectiveCapacity\":0},{\"id\":7,\"name\":\"K1RRD\",\"onBooks\":19,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":19.1,\"adrOnBooks\":117.99,\"adrForecast\":117.9,\"revenue\":2241.88,\"revenueForecast\":2252.98,\"revpar\":72.32,\"revparForecast\":72.68,\"lastYearOnBooks\":0,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":0,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":0,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":0,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":0,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":3,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":397,\"adrPickUp\":2.69,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":31,\"effectiveCapacity\":31}]"
    Assert.assertEquals(expectedResponse, badRoomTypeLevelDetailsUnderSTD.getData().toString());
}

And(~/I query to Room Type level details under "Standard" Room Class for the date range "([^"]*)" to "([^"]*)" which is partially in past and partially in future for "([^"]*)" Room Types Only$/) { String startDate, String endDate, String roomTypeStatus ->
    HttpResponseDecorator badRoomTypeLevelDetailsUnderSTD = getRoomTypeLevelDetailsUnderSTDForRoomTypes(startDate, endDate, roomTypeStatus)
    String expectedResponse = ""
    if (!roomTypeStatus.equalsIgnoreCase("Active")) {
        expectedResponse = "[{\"id\":1,\"name\":\"HOUS\",\"onBooks\":0,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":0,\"adrOnBooks\":0,\"adrForecast\":0,\"revenue\":0,\"revenueForecast\":0,\"revpar\":0,\"revparForecast\":0,\"lastYearOnBooks\":0,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":0,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":0,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":0,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":0,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":0,\"effectiveCapacity\":0},{\"id\":2,\"name\":\"K1\",\"onBooks\":1417,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":1417.1,\"adrOnBooks\":120.26,\"adrForecast\":120.23,\"revenue\":170404.11,\"revenueForecast\":170380.85,\"revpar\":107.85,\"revparForecast\":107.84,\"lastYearOnBooks\":1428,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":1442,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":121.93,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":121.65,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":174117.53,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":175414.88,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":108.28,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":109.09,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":73,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":7761.09,\"adrPickUp\":-0.76,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":1612,\"effectiveCapacity\":1580},{\"id\":3,\"name\":\"K1RRC\",\"onBooks\":79,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":79.4,\"adrOnBooks\":97.07,\"adrForecast\":97.01,\"revenue\":7668.3,\"revenueForecast\":7699.92,\"revpar\":62.85,\"revparForecast\":63.11,\"lastYearOnBooks\":67,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":70,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":115.51,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":116.52,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":7739.3,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":8156.3,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":62.41,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":65.78,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":-5,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":-614,\"adrPickUp\":-1.53,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":124,\"effectiveCapacity\":122},{\"id\":5,\"name\":\"Q2\",\"onBooks\":808,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":810.6,\"adrOnBooks\":121.31,\"adrForecast\":121.19,\"revenue\":98021.35,\"revenueForecast\":98232.34,\"revpar\":88.87,\"revparForecast\":89.06,\"lastYearOnBooks\":751,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":762,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":125.79,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":126.41,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":94467.78,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":96326.78,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":85.11,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":86.78,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":51,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":3643.51,\"adrPickUp\":-3.36,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":1116,\"effectiveCapacity\":1103},{\"id\":6,\"name\":\"RR\",\"onBooks\":0,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":0,\"adrOnBooks\":0,\"adrForecast\":0,\"revenue\":0,\"revenueForecast\":0,\"revpar\":0,\"revparForecast\":0,\"lastYearOnBooks\":0,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":0,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":0,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":0,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":0,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":0,\"effectiveCapacity\":0},{\"id\":7,\"name\":\"K1RRD\",\"onBooks\":19,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":19.1,\"adrOnBooks\":117.99,\"adrForecast\":117.9,\"revenue\":2241.88,\"revenueForecast\":2252.98,\"revpar\":72.32,\"revparForecast\":72.68,\"lastYearOnBooks\":0,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":0,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":0,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":0,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":0,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":3,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":397,\"adrPickUp\":2.69,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":31,\"effectiveCapacity\":31}]"
    } else {
        expectedResponse = "[{\"id\":1,\"name\":\"HOUS\",\"onBooks\":0,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":0,\"adrOnBooks\":0,\"adrForecast\":0,\"revenue\":0,\"revenueForecast\":0,\"revpar\":0,\"revparForecast\":0,\"lastYearOnBooks\":0,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":0,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":0,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":0,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":0,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":0,\"effectiveCapacity\":0},{\"id\":2,\"name\":\"K1\",\"onBooks\":1417,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":1417.1,\"adrOnBooks\":120.26,\"adrForecast\":120.23,\"revenue\":170404.11,\"revenueForecast\":170380.85,\"revpar\":107.85,\"revparForecast\":107.84,\"lastYearOnBooks\":1428,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":1442,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":121.93,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":121.65,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":174117.53,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":175414.88,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":108.28,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":109.09,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":73,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":7761.09,\"adrPickUp\":-0.76,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":1612,\"effectiveCapacity\":1580},{\"id\":3,\"name\":\"K1RRC\",\"onBooks\":79,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":79.4,\"adrOnBooks\":97.07,\"adrForecast\":97.01,\"revenue\":7668.3,\"revenueForecast\":7699.92,\"revpar\":62.85,\"revparForecast\":63.11,\"lastYearOnBooks\":67,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":70,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":115.51,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":116.52,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":7739.3,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":8156.3,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":62.41,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":65.78,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":-5,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":-614,\"adrPickUp\":-1.53,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":124,\"effectiveCapacity\":122},{\"id\":6,\"name\":\"RR\",\"onBooks\":0,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":0,\"adrOnBooks\":0,\"adrForecast\":0,\"revenue\":0,\"revenueForecast\":0,\"revpar\":0,\"revparForecast\":0,\"lastYearOnBooks\":0,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":0,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":0,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":0,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":0,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":0,\"effectiveCapacity\":0},{\"id\":7,\"name\":\"K1RRD\",\"onBooks\":19,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":19.1,\"adrOnBooks\":117.99,\"adrForecast\":117.9,\"revenue\":2241.88,\"revenueForecast\":2252.98,\"revpar\":72.32,\"revparForecast\":72.68,\"lastYearOnBooks\":0,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":0,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":0,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":0,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":0,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":3,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":397,\"adrPickUp\":2.69,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":31,\"effectiveCapacity\":31}]"
    }
    Assert.assertEquals(expectedResponse, badRoomTypeLevelDetailsUnderSTD.getData().toString());
}


And(~/I query to Room Type level details under "Standard" Room Class for the date range "([^"]*)" to "([^"]*)" which is partially in past and partially in future for "([^"]*)" Room Types Only Under Inventory Group$/) { String startDate, String endDate, String roomTypeStatus ->
    HttpResponseDecorator badRoomTypeLevelDetailsUnderSTD = getRoomTypeLevelDetailsUnderSTDForRoomTypesUnderInventoryGroup(startDate, endDate, roomTypeStatus)
    String expectedResponse = ""
    if (!roomTypeStatus.equalsIgnoreCase("Active")) {
        expectedResponse = "[{\"id\":2,\"name\":\"K1\",\"onBooks\":1417,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":1417.1,\"adrOnBooks\":120.26,\"adrForecast\":120.23,\"revenue\":170404.11,\"revenueForecast\":170380.85,\"revpar\":107.85,\"revparForecast\":107.84,\"lastYearOnBooks\":1428,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":1442,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":121.93,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":121.65,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":174117.53,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":175414.88,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":108.28,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":109.09,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":73,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":7761.09,\"adrPickUp\":-0.76,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":1612,\"effectiveCapacity\":1580},{\"id\":3,\"name\":\"K1RRC\",\"onBooks\":79,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":79.4,\"adrOnBooks\":97.07,\"adrForecast\":97.01,\"revenue\":7668.3,\"revenueForecast\":7699.92,\"revpar\":62.85,\"revparForecast\":63.11,\"lastYearOnBooks\":67,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":70,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":115.51,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":116.52,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":7739.3,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":8156.3,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":62.41,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":65.78,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":-5,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":-614,\"adrPickUp\":-1.53,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":124,\"effectiveCapacity\":122},{\"id\":5,\"name\":\"Q2\",\"onBooks\":808,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":810.6,\"adrOnBooks\":121.31,\"adrForecast\":121.19,\"revenue\":98021.35,\"revenueForecast\":98232.34,\"revpar\":88.87,\"revparForecast\":89.06,\"lastYearOnBooks\":751,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":762,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":125.79,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":126.41,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":94467.78,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":96326.78,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":85.11,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":86.78,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":51,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":3643.51,\"adrPickUp\":-3.36,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":1116,\"effectiveCapacity\":1103},{\"id\":7,\"name\":\"K1RRD\",\"onBooks\":19,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":19.1,\"adrOnBooks\":117.99,\"adrForecast\":117.9,\"revenue\":2241.88,\"revenueForecast\":2252.98,\"revpar\":72.32,\"revparForecast\":72.68,\"lastYearOnBooks\":null,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":null,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":null,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":3,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":397,\"adrPickUp\":2.69,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":31,\"effectiveCapacity\":31},{\"id\":1,\"name\":\"HOUS\",\"onBooks\":null,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":null,\"adrOnBooks\":null,\"adrForecast\":null,\"revenue\":null,\"revenueForecast\":null,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":null,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":null,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":null,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":null,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":null,\"adrPickUp\":null,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":6,\"name\":\"RR\",\"onBooks\":null,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":null,\"adrOnBooks\":null,\"adrForecast\":null,\"revenue\":null,\"revenueForecast\":null,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":null,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":null,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":null,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":null,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":null,\"adrPickUp\":null,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null}]"
    } else {
        expectedResponse = "[{\"id\":2,\"name\":\"K1\",\"onBooks\":1417,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":1417.1,\"adrOnBooks\":120.26,\"adrForecast\":120.23,\"revenue\":170404.11,\"revenueForecast\":170380.85,\"revpar\":107.85,\"revparForecast\":107.84,\"lastYearOnBooks\":1428,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":1442,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":121.93,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":121.65,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":174117.53,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":175414.88,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":108.28,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":109.09,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":73,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":7761.09,\"adrPickUp\":-0.76,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":1612,\"effectiveCapacity\":1580},{\"id\":3,\"name\":\"K1RRC\",\"onBooks\":79,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":79.4,\"adrOnBooks\":97.07,\"adrForecast\":97.01,\"revenue\":7668.3,\"revenueForecast\":7699.92,\"revpar\":62.85,\"revparForecast\":63.11,\"lastYearOnBooks\":67,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":70,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":115.51,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":116.52,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":7739.3,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":8156.3,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":62.41,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":65.78,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":-5,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":-614,\"adrPickUp\":-1.53,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":124,\"effectiveCapacity\":122},{\"id\":7,\"name\":\"K1RRD\",\"onBooks\":19,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":19.1,\"adrOnBooks\":117.99,\"adrForecast\":117.9,\"revenue\":2241.88,\"revenueForecast\":2252.98,\"revpar\":72.32,\"revparForecast\":72.68,\"lastYearOnBooks\":null,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":null,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":null,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":3,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":397,\"adrPickUp\":2.69,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":31,\"effectiveCapacity\":31},{\"id\":1,\"name\":\"HOUS\",\"onBooks\":null,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":null,\"adrOnBooks\":null,\"adrForecast\":null,\"revenue\":null,\"revenueForecast\":null,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":null,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":null,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":null,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":null,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":null,\"adrPickUp\":null,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":6,\"name\":\"RR\",\"onBooks\":null,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":null,\"adrOnBooks\":null,\"adrForecast\":null,\"revenue\":null,\"revenueForecast\":null,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":null,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":null,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":null,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":null,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":null,\"adrPickUp\":null,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null}]"
    }
    Assert.assertEquals(expectedResponse, badRoomTypeLevelDetailsUnderSTD.getData().toString());
}

And(~/I query to Room Type level details under "Standard" Room Class exlcude comp room for the date range "([^"]*)" to "([^"]*)" which is partially in past and partially in future for "([^"]*)" Room Types Only Under Inventory Group$/) { String startDate, String endDate, String roomTypeStatus ->
    HttpResponseDecorator badRoomTypeLevelDetailsUnderSTD = getRoomTypeLevelDetailsUnderSTDForRoomTypesUnderInventoryGroup(startDate, endDate, roomTypeStatus)
    String expectedResponse = ""
    if (!roomTypeStatus.equalsIgnoreCase("Active")) {
        expectedResponse = "[{\"id\":2,\"name\":\"K1\",\"onBooks\":1417,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":1417.1,\"adrOnBooks\":120.26,\"adrForecast\":120.23,\"revenue\":170404.11,\"revenueForecast\":170380.85,\"revpar\":107.85,\"revparForecast\":107.84,\"lastYearOnBooks\":1428,\"last2YearsOnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"lastYearOccupancyForecast\":1442,\"last2YearsOccupancyForecast\":null,\"lastYearAdrOnBooks\":121.93,\"last2YearsAdrOnBooks\":null,\"lastYearAdrForecast\":121.65,\"last2YearsAdrForecast\":null,\"lastYearRevenue\":174117.53,\"last2YearsRevenue\":null,\"lastYearRevenueForecast\":175414.88,\"last2YearsRevenueForecast\":null,\"lastYearRevpar\":108.28,\"last2YearsRevpar\":null,\"lastYearRevparForecast\":109.09,\"last2YearsRevparForecast\":null,\"onBooksPickUp\":73,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":7761.09,\"adrPickUp\":-0.76,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"proPARForecast\":null},{\"id\":3,\"name\":\"K1RRC\",\"onBooks\":79,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":79.4,\"adrOnBooks\":97.07,\"adrForecast\":97.01,\"revenue\":7668.3,\"revenueForecast\":7699.92,\"revpar\":62.85,\"revparForecast\":63.11,\"lastYearOnBooks\":67,\"last2YearsOnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"lastYearOccupancyForecast\":70,\"last2YearsOccupancyForecast\":null,\"lastYearAdrOnBooks\":115.51,\"last2YearsAdrOnBooks\":null,\"lastYearAdrForecast\":116.52,\"last2YearsAdrForecast\":null,\"lastYearRevenue\":7739.3,\"last2YearsRevenue\":null,\"lastYearRevenueForecast\":8156.3,\"last2YearsRevenueForecast\":null,\"lastYearRevpar\":62.41,\"last2YearsRevpar\":null,\"lastYearRevparForecast\":65.78,\"last2YearsRevparForecast\":null,\"onBooksPickUp\":-5,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":-614,\"adrPickUp\":-1.53,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"proPARForecast\":null},{\"id\":5,\"name\":\"Q2\",\"onBooks\":808,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":810.6,\"adrOnBooks\":121.31,\"adrForecast\":121.19,\"revenue\":98021.35,\"revenueForecast\":98232.34,\"revpar\":88.87,\"revparForecast\":89.06,\"lastYearOnBooks\":751,\"last2YearsOnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"lastYearOccupancyForecast\":762,\"last2YearsOccupancyForecast\":null,\"lastYearAdrOnBooks\":125.79,\"last2YearsAdrOnBooks\":null,\"lastYearAdrForecast\":126.41,\"last2YearsAdrForecast\":null,\"lastYearRevenue\":94467.78,\"last2YearsRevenue\":null,\"lastYearRevenueForecast\":96326.78,\"last2YearsRevenueForecast\":null,\"lastYearRevpar\":85.11,\"last2YearsRevpar\":null,\"lastYearRevparForecast\":86.78,\"last2YearsRevparForecast\":null,\"onBooksPickUp\":51,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":3643.51,\"adrPickUp\":-3.36,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"proPARForecast\":null},{\"id\":7,\"name\":\"K1RRD\",\"onBooks\":19,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":19.1,\"adrOnBooks\":117.99,\"adrForecast\":117.9,\"revenue\":2241.88,\"revenueForecast\":2252.98,\"revpar\":72.32,\"revparForecast\":72.68,\"lastYearOnBooks\":null,\"last2YearsOnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"lastYearAdrOnBooks\":null,\"last2YearsAdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"lastYearRevenue\":null,\"last2YearsRevenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"onBooksPickUp\":3,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":397,\"adrPickUp\":2.69,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"proPARForecast\":null,\"physicalCapacity\":1610,\"effectiveCapacity\":1580},{\"id\":1,\"name\":\"HOUS\",\"onBooks\":null,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":null,\"adrOnBooks\":null,\"adrForecast\":null,\"revenue\":null,\"revenueForecast\":null,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":null,\"last2YearsOnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"lastYearAdrOnBooks\":null,\"last2YearsAdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"lastYearRevenue\":null,\"last2YearsRevenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"onBooksPickUp\":null,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":null,\"adrPickUp\":null,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"proPARForecast\":null},{\"id\":6,\"name\":\"RR\",\"onBooks\":null,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":null,\"adrOnBooks\":null,\"adrForecast\":null,\"revenue\":null,\"revenueForecast\":null,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":null,\"last2YearsOnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"lastYearAdrOnBooks\":null,\"last2YearsAdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"lastYearRevenue\":null,\"last2YearsRevenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"onBooksPickUp\":null,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":null,\"adrPickUp\":null,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":0,\"effectiveCapacity\":0}]"
    } else {
        expectedResponse = "[{\"id\":2,\"name\":\"K1\",\"onBooks\":1417,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":1417.1,\"adrOnBooks\":120.26,\"adrForecast\":120.23,\"revenue\":170404.11,\"revenueForecast\":170380.85,\"revpar\":107.85,\"revparForecast\":107.84,\"lastYearOnBooks\":1428,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":1442,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":121.93,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":121.65,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":174117.53,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":175414.88,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":108.28,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":109.09,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":73,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":7761.09,\"adrPickUp\":-0.76,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":1612,\"effectiveCapacity\":1580},{\"id\":3,\"name\":\"K1RRC\",\"onBooks\":79,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":79.4,\"adrOnBooks\":97.07,\"adrForecast\":97.01,\"revenue\":7668.3,\"revenueForecast\":7699.92,\"revpar\":62.85,\"revparForecast\":63.11,\"lastYearOnBooks\":67,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":70,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":115.51,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":116.52,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":7739.3,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":8156.3,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":62.41,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":65.78,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":-5,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":-614,\"adrPickUp\":-1.53,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":124,\"effectiveCapacity\":122},{\"id\":7,\"name\":\"K1RRD\",\"onBooks\":19,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":19.1,\"adrOnBooks\":117.99,\"adrForecast\":117.9,\"revenue\":2241.88,\"revenueForecast\":2252.98,\"revpar\":72.32,\"revparForecast\":72.68,\"lastYearOnBooks\":null,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":null,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":null,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":3,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":397,\"adrPickUp\":2.69,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":31,\"effectiveCapacity\":31},{\"id\":1,\"name\":\"HOUS\",\"onBooks\":null,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":null,\"adrOnBooks\":null,\"adrForecast\":null,\"revenue\":null,\"revenueForecast\":null,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":null,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":null,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":null,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":null,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":null,\"adrPickUp\":null,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":6,\"name\":\"RR\",\"onBooks\":null,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":null,\"adrOnBooks\":null,\"adrForecast\":null,\"revenue\":null,\"revenueForecast\":null,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":null,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":null,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":null,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":null,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":null,\"adrPickUp\":null,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null}]"
    }
    Assert.assertEquals(expectedResponse, badRoomTypeLevelDetailsUnderSTD.getData().toString());
}

And(~/I query to Room Type level details under "Deluxe" Room Class for the date range "([^"]*)" to "([^"]*)" which is partially in past and partially in future for "([^"]*)" Room Types Only Under Inventory Group$/) { String startDate, String endDate, String roomTypeStatus ->
    HttpResponseDecorator badRoomTypeLevelDetailsUnderDLX = getRoomTypeLevelDetailsUnderDLXForRoomTypesUnderInventoryGroup(startDate, endDate, roomTypeStatus)
    String expectedResponse = "[{\"id\":4,\"name\":\"K1X\",\"onBooks\":150,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":150.4,\"adrOnBooks\":138.04,\"adrForecast\":137.89,\"revenue\":20706.69,\"revenueForecast\":20742.01,\"revpar\":113.15,\"revparForecast\":113.34,\"lastYearOnBooks\":111,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":113,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":144.27,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":144.53,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":16013.48,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":16331.48,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":103.98,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":106.05,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":22,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":3060.58,\"adrPickUp\":0.18,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":186,\"effectiveCapacity\":183}]"
    Assert.assertEquals(expectedResponse, badRoomTypeLevelDetailsUnderDLX.getData().toString());
}

And(~/I query to Room Type level details under "Deluxe" Room Class exlcude comp room for the date range "([^"]*)" to "([^"]*)" which is partially in past and partially in future for "([^"]*)" Room Types Only Under Inventory Group$/) { String startDate, String endDate, String roomTypeStatus ->
    HttpResponseDecorator badRoomTypeLevelDetailsUnderDLX = getRoomTypeLevelDetailsUnderDLXForRoomTypesUnderInventoryGroup(startDate, endDate, roomTypeStatus)
    String expectedResponse = "[{\"id\":4,\"name\":\"K1X\",\"onBooks\":150,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":150.4,\"adrOnBooks\":138.04,\"adrForecast\":137.89,\"revenue\":20706.69,\"revenueForecast\":20742.01,\"revpar\":113.15,\"revparForecast\":113.34,\"lastYearOnBooks\":111,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":113,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":144.27,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":144.53,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":16013.48,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":16331.48,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":103.98,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":106.05,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":22,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":3060.58,\"adrPickUp\":0.18,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":186,\"effectiveCapacity\":183}]"
    Assert.assertEquals(expectedResponse, badRoomTypeLevelDetailsUnderDLX.getData().toString());
}

And(~/I query to Room Type level details under "Deluxe" Room Class for the date range "([^"]*)" to "([^"]*)" which is partially in past and partially in future for "([^"]*)" Room Types Only$/) { String startDate, String endDate, String roomTypeStatus ->
    HttpResponseDecorator badRoomTypeLevelDetailsUnderSTD = getRoomTypeLevelDetailsUnderDLXForRoomTypes(startDate, endDate, roomTypeStatus)
    String expectedResponse = "[{\"id\":4,\"name\":\"K1X\",\"onBooks\":150,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":150.4,\"adrOnBooks\":138.04,\"adrForecast\":137.89,\"revenue\":20706.69,\"revenueForecast\":20742.01,\"revpar\":113.15,\"revparForecast\":113.34,\"lastYearOnBooks\":111,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":113,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":144.27,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":144.53,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":16013.48,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":16331.48,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":103.98,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":106.05,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":22,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":3060.58,\"adrPickUp\":0.18,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":186,\"effectiveCapacity\":183}]"
    Assert.assertEquals(expectedResponse, badRoomTypeLevelDetailsUnderSTD.getData().toString());
}


And(~/I query to Room Type level details under "Deluxe" Room Class for the date range "([^"]*)" to "([^"]*)" which is partially in past and partially in future$/) { String startDate, String endDate ->
    HttpResponseDecorator badRoomTypeLevelDetailsUnderDLX = getRoomTypeLevelDetailsUnderDLX(startDate, endDate)
    String expectedResponse = "[{\"id\":4,\"name\":\"K1X\",\"onBooks\":150,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":150.4,\"adrOnBooks\":138.04,\"adrForecast\":137.89,\"revenue\":20706.69,\"revenueForecast\":20742.01,\"revpar\":113.15,\"revparForecast\":113.34,\"lastYearOnBooks\":111,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":113,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":144.27,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":144.53,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":16013.48,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":16331.48,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":103.98,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":106.05,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":22,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":3060.58,\"adrPickUp\":0.18,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":186,\"effectiveCapacity\":183}]"
    Assert.assertEquals(expectedResponse, badRoomTypeLevelDetailsUnderDLX.getData().toString());
}

And(~/I query to Room Type level details under "Deluxe" Room Class exclude comp room for the date range "([^"]*)" to "([^"]*)" which is partially in past and partially in future$/) { String startDate, String endDate ->
    HttpResponseDecorator badRoomTypeLevelDetailsUnderDLX = getRoomTypeLevelDetailsUnderDLX(startDate, endDate)
    String expectedResponse = "[{\"id\":4,\"name\":\"K1X\",\"onBooks\":150,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":150.4,\"adrOnBooks\":138.04,\"adrForecast\":137.89,\"revenue\":20706.69,\"revenueForecast\":20742.01,\"revpar\":113.15,\"revparForecast\":113.34,\"lastYearOnBooks\":111,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":113,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":144.27,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":144.53,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":16013.48,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":16331.48,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":103.98,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":106.05,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":22,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":3060.58,\"adrPickUp\":0.18,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":186,\"effectiveCapacity\":183}]"
    Assert.assertEquals(expectedResponse, badRoomTypeLevelDetailsUnderDLX.getData().toString());
}

And(~/I query to Market Segment level details for the date range "([^"]*)" to "([^"]*)" which is partially in past and partially in future$/) { String startDate, String endDate ->
    HttpResponseDecorator badMarketSegmentLevelDetails = getMarketSegmentLevelDetails(startDate, endDate)
    String expectedResponse = "[{\"id\":11,\"name\":\"DISC_U\",\"onBooks\":57,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":57,\"adrOnBooks\":166.63,\"adrForecast\":166.63,\"revenue\":9497.67,\"revenueForecast\":9497.67,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":52,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":52,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":144.84,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":144.84,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":7531.54,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":7531.54,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":2,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":342.4,\"adrPickUp\":0.17,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":12,\"name\":\"DISC_UF\",\"onBooks\":31,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":31,\"adrOnBooks\":91.13,\"adrForecast\":91.13,\"revenue\":2825.11,\"revenueForecast\":2825.11,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":61,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":61,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":91.6,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":91.6,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":5587.69,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":5587.69,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":31,\"name\":\"DISC_UFP\",\"onBooks\":0,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":0,\"adrOnBooks\":0,\"adrForecast\":0,\"revenue\":0,\"revenueForecast\":0,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":0,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":0,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":0,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":24,\"name\":\"MKT_DEF_FUT\",\"onBooks\":0,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":0,\"adrOnBooks\":0,\"adrForecast\":0,\"revenue\":0,\"revenueForecast\":0,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":0,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":0,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":0,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":18,\"name\":\"MKT_DEF_HIST\",\"onBooks\":0,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":0,\"adrOnBooks\":0,\"adrForecast\":0,\"revenue\":0,\"revenueForecast\":0,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":0,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":0,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":0,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":20,\"name\":\"MKT_UF\",\"onBooks\":25,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":25,\"adrOnBooks\":104.35,\"adrForecast\":104.35,\"revenue\":2608.7,\"revenueForecast\":2608.7,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":87,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":87,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":112.72,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":112.72,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":9806.48,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":9806.48,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":29,\"name\":\"MKT_UFP\",\"onBooks\":0,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":0,\"adrOnBooks\":0,\"adrForecast\":0,\"revenue\":0,\"revenueForecast\":0,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":0,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":0,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":0,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":21,\"name\":\"MKT_UP\",\"onBooks\":84,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":84,\"adrOnBooks\":149.71,\"adrForecast\":149.7,\"revenue\":12576,\"revenueForecast\":12568.69,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":43,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":45,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":147.84,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":148.78,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":6357,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":6695,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":9,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":1121,\"adrPickUp\":-3.02,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null}]"
    JSONAssert.assertEquals(expectedResponse, badMarketSegmentLevelDetails.getData().toString());
}

And(~/I query to Excluded Market Segment level details for the date range "([^"]*)" to "([^"]*)" which is partially in past and partially in future$/) { String startDate, String endDate ->
    HttpResponseDecorator badMarketSegmentLevelDetails = getExcludedMarketSegmentLevelDetails(startDate, endDate)
    String expectedResponse = "[{\"id\":11,\"name\":\"DISC_U\",\"onBooks\":57,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":57,\"adrOnBooks\":166.63,\"adrForecast\":166.63,\"revenue\":9497.67,\"revenueForecast\":9497.67,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":52,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":52,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":144.84,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":144.84,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":7531.54,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":7531.54,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":2,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":342.4,\"adrPickUp\":0.17,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":12,\"name\":\"DISC_UF\",\"onBooks\":31,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":31,\"adrOnBooks\":91.13,\"adrForecast\":91.13,\"revenue\":2825.11,\"revenueForecast\":2825.11,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":61,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":61,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":91.6,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":91.6,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":5587.69,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":5587.69,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":31,\"name\":\"DISC_UFP\",\"onBooks\":0,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":0,\"adrOnBooks\":0,\"adrForecast\":0,\"revenue\":0,\"revenueForecast\":0,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":0,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":0,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":0,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":24,\"name\":\"MKT_DEF_FUT\",\"onBooks\":0,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":0,\"adrOnBooks\":0,\"adrForecast\":0,\"revenue\":0,\"revenueForecast\":0,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":0,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":0,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":0,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":18,\"name\":\"MKT_DEF_HIST\",\"onBooks\":0,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":0,\"adrOnBooks\":0,\"adrForecast\":0,\"revenue\":0,\"revenueForecast\":0,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":0,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":0,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":0,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":20,\"name\":\"MKT_UF\",\"onBooks\":25,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":25,\"adrOnBooks\":104.35,\"adrForecast\":104.35,\"revenue\":2608.7,\"revenueForecast\":2608.7,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":87,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":87,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":112.72,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":112.72,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":9806.48,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":9806.48,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":29,\"name\":\"MKT_UFP\",\"onBooks\":0,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":0,\"adrOnBooks\":0,\"adrForecast\":0,\"revenue\":0,\"revenueForecast\":0,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":0,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":0,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":0,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":0,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":0,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":0,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":21,\"name\":\"MKT_UP\",\"onBooks\":84,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":84,\"adrOnBooks\":149.71,\"adrForecast\":149.7,\"revenue\":12576,\"revenueForecast\":12568.69,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":43,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":45,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":147.84,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":148.78,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":6357,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":6695,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":9,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":1121,\"adrPickUp\":-3.02,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null}]"
    JSONAssert.assertEquals(expectedResponse, badMarketSegmentLevelDetails.getData().toString());
}


And(~/I query to Rate Code level details for the date range "([^"]*)" to "([^"]*)" which is partially in past and partially in future$/) { String startDate, String endDate ->
    HttpResponseDecorator badRateCodeLevelDetails = getRateCodeLevelDetails(startDate, endDate)
    String expectedResponse = "[{\"id\":20,\"name\":\"QO\",\"onBooks\":14,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":null,\"adrOnBooks\":105.4,\"adrForecast\":null,\"revenue\":1475.6,\"revenueForecast\":null,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":null,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":44,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":null,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":105.51818,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":null,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":4642.8,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null},{\"id\":20,\"name\":\"QP\",\"onBooks\":11,\"systemDemand\":null,\"userDemand\":null,\"occupancyForecast\":null,\"adrOnBooks\":103.00909,\"adrForecast\":null,\"revenue\":1133.1,\"revenueForecast\":null,\"revpar\":null,\"revparForecast\":null,\"lastYearOnBooks\":null,\"last2YearsOnBooks\":null,\"year2019OnBooks\":null,\"lastYearSystemDemand\":null,\"last2YearsSystemDemand\":null,\"year2019SystemDemand\":null,\"lastYearUserDemand\":null,\"last2YearsUserDemand\":null,\"year2019UserDemand\":null,\"lastYearOccupancyForecast\":43,\"last2YearsOccupancyForecast\":null,\"year2019OccupancyForecast\":null,\"lastYearAdrOnBooks\":null,\"last2YearsAdrOnBooks\":null,\"year2019AdrOnBooks\":null,\"lastYearAdrForecast\":120.08372,\"last2YearsAdrForecast\":null,\"year2019AdrForecast\":null,\"lastYearRevenue\":null,\"last2YearsRevenue\":null,\"year2019Revenue\":null,\"lastYearRevenueForecast\":5163.6,\"last2YearsRevenueForecast\":null,\"year2019RevenueForecast\":null,\"lastYearRevpar\":null,\"last2YearsRevpar\":null,\"year2019Revpar\":null,\"lastYearRevparForecast\":null,\"last2YearsRevparForecast\":null,\"year2019RevparForecast\":null,\"onBooksPickUp\":0,\"budgetRooms\":null,\"budgetRevenue\":null,\"budgetAdr\":null,\"budgetRevpar\":null,\"userForecastRooms\":null,\"userForecastRevenue\":null,\"userForecastAdr\":null,\"userForecastRevpar\":null,\"viewOrder\":null,\"revenuePickUp\":0,\"adrPickUp\":0,\"proPOROnBooks\":null,\"lastYearProPOROnBooks\":null,\"lastYearProPORForecast\":null,\"last2YearsProPOROnBooks\":null,\"last2YearsProPORForecast\":null,\"year2019ProPOROnBooks\":null,\"year2019ProPORForecast\":null,\"proPORForecast\":null,\"profitOnBooks\":null,\"lastYearProfitOnBooks\":null,\"lastYearProfitForecast\":null,\"last2YearsProfitOnBooks\":null,\"last2YearsProfitForecast\":null,\"year2019ProfitOnBooks\":null,\"year2019ProfitForecast\":null,\"profitForecast\":null,\"proPAROnBooks\":null,\"lastYearProPAROnBooks\":null,\"lastYearProPARForecast\":null,\"last2YearsProPAROnBooks\":null,\"last2YearsProPARForecast\":null,\"year2019ProPAROnBooks\":null,\"year2019ProPARForecast\":null,\"proPARForecast\":null,\"capacity\":null,\"capacityForecast\":null,\"physicalCapacity\":null,\"effectiveCapacity\":null}]"
    Assert.assertEquals(expectedResponse, badRateCodeLevelDetails.getData().toString());
}

And(~/I query to Business View level details for the date range "([^"]*)" to "([^"]*)" which is partially in past and partially in future$/) { String startDate, String endDate ->
    insertBusinessGroups(DatabaseName)
    HttpResponseDecorator businessViewLevelDetails = getBusinessViewLevelDetails(startDate, endDate)
    String expectedResponse = '[{"id":1,"name":"BG1","onBooks":350,"systemDemand":null,"userDemand":null,"occupancyForecast":362.8,"adrOnBooks":139.45,"adrForecast":140.3,"revenue":48808.82,"revenueForecast":50902.25,"revpar":null,"revparForecast":null,"lastYearOnBooks":413,"last2YearsOnBooks":null,"year2019OnBooks":null,"lastYearSystemDemand":null,"last2YearsSystemDemand":null,"year2019SystemDemand":null,"lastYearUserDemand":null,"last2YearsUserDemand":null,"year2019UserDemand":null,"lastYearOccupancyForecast":464,"last2YearsOccupancyForecast":null,"year2019OccupancyForecast":null,"lastYearAdrOnBooks":148.79,"last2YearsAdrOnBooks":null,"year2019AdrOnBooks":null,"lastYearAdrForecast":143.84,"last2YearsAdrForecast":null,"year2019AdrForecast":null,"lastYearRevenue":61448.73,"last2YearsRevenue":null,"year2019Revenue":null,"lastYearRevenueForecast":66741.03,"last2YearsRevenueForecast":null,"year2019RevenueForecast":null,"lastYearRevpar":null,"last2YearsRevpar":null,"year2019Revpar":null,"lastYearRevparForecast":null,"last2YearsRevparForecast":null,"year2019RevparForecast":null,"onBooksPickUp":27,"budgetRooms":null,"budgetRevenue":null,"budgetAdr":null,"budgetRevpar":null,"userForecastRooms":null,"userForecastRevenue":null,"userForecastAdr":null,"userForecastRevpar":null,"viewOrder":null,"revenuePickUp":3101.99,"adrPickUp":-2.05,"proPOROnBooks":null,"lastYearProPOROnBooks":null,"lastYearProPORForecast":null,"last2YearsProPOROnBooks":null,"last2YearsProPORForecast":null,"year2019ProPOROnBooks":null,"year2019ProPORForecast":null,"proPORForecast":null,"profitOnBooks":null,"lastYearProfitOnBooks":null,"lastYearProfitForecast":null,"last2YearsProfitOnBooks":null,"last2YearsProfitForecast":null,"year2019ProfitOnBooks":null,"year2019ProfitForecast":null,"profitForecast":null,"proPAROnBooks":null,"lastYearProPAROnBooks":null,"lastYearProPARForecast":null,"last2YearsProPAROnBooks":null,"last2YearsProPARForecast":null,"year2019ProPAROnBooks":null,"year2019ProPARForecast":null,"proPARForecast":null,"capacity":null,"capacityForecast":null,"physicalCapacity":null,"effectiveCapacity":null},{"id":2,"name":"BG2","onBooks":129,"systemDemand":null,"userDemand":null,"occupancyForecast":138.1,"adrOnBooks":101.32,"adrForecast":101.33,"revenue":13070.1,"revenueForecast":13993.33,"revpar":null,"revparForecast":null,"lastYearOnBooks":104,"last2YearsOnBooks":null,"year2019OnBooks":null,"lastYearSystemDemand":null,"last2YearsSystemDemand":null,"year2019SystemDemand":null,"lastYearUserDemand":null,"last2YearsUserDemand":null,"year2019UserDemand":null,"lastYearOccupancyForecast":149,"last2YearsOccupancyForecast":null,"year2019OccupancyForecast":null,"lastYearAdrOnBooks":97.82,"last2YearsAdrOnBooks":null,"year2019AdrOnBooks":null,"lastYearAdrForecast":102.7,"last2YearsAdrForecast":null,"year2019AdrForecast":null,"lastYearRevenue":10173.08,"last2YearsRevenue":null,"year2019Revenue":null,"lastYearRevenueForecast":15301.58,"last2YearsRevenueForecast":null,"year2019RevenueForecast":null,"lastYearRevpar":null,"last2YearsRevpar":null,"year2019Revpar":null,"lastYearRevparForecast":null,"last2YearsRevparForecast":null,"year2019RevparForecast":null,"onBooksPickUp":11,"budgetRooms":null,"budgetRevenue":null,"budgetAdr":null,"budgetRevpar":null,"userForecastRooms":null,"userForecastRevenue":null,"userForecastAdr":null,"userForecastRevpar":null,"viewOrder":null,"revenuePickUp":1145,"adrPickUp":0.26,"proPOROnBooks":null,"lastYearProPOROnBooks":null,"lastYearProPORForecast":null,"last2YearsProPOROnBooks":null,"last2YearsProPORForecast":null,"year2019ProPOROnBooks":null,"year2019ProPORForecast":null,"proPORForecast":null,"profitOnBooks":null,"lastYearProfitOnBooks":null,"lastYearProfitForecast":null,"last2YearsProfitOnBooks":null,"last2YearsProfitForecast":null,"year2019ProfitOnBooks":null,"year2019ProfitForecast":null,"profitForecast":null,"proPAROnBooks":null,"lastYearProPAROnBooks":null,"lastYearProPARForecast":null,"last2YearsProPAROnBooks":null,"last2YearsProPARForecast":null,"year2019ProPAROnBooks":null,"year2019ProPARForecast":null,"proPARForecast":null,"capacity":null,"capacityForecast":null,"physicalCapacity":null,"effectiveCapacity":null}]'
    println(businessViewLevelDetails.getData().toString())
    deleteDataFromBusinessGroup(DatabaseName)
    JSONAssert.assertEquals(expectedResponse, businessViewLevelDetails.getData());
}

private void deleteDataFromBusinessGroup(String DatabaseName) {
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute(" delete from Mkt_Seg_Business_Group; \n " +
                       " delete from Business_Group; \n ");
    connection.close();
}

private void insertBusinessGroups(String DatabaseName) {
    deleteDataFromBusinessGroup(DatabaseName)
    def connection = DbConnection.getTenantDatabaseConnection(DatabaseName)
    connection.execute(" SET IDENTITY_INSERT Business_Group ON; \n " +
            " insert into Business_Group \n " +
            " (Business_Group_ID, Business_Group_Name, Business_Group_Description, \n " +
            " Ranking, Status_ID, Created_by_User_ID, Last_Updated_by_User_ID, Last_Updated_DTTM, Created_DTTM, Property_ID) \n " +
            " values (1, 'BG1', 'BG1', 1, 1, 11403, 11403, GETDATE(), GETDATE(), 10022), \n " +
            " (2, 'BG2', 'BG2', 2, 1, 11403, 11403, GETDATE(), GETDATE(), 10022); \n " +
            " SET IDENTITY_INSERT Business_Group OFF; \n " +
            " SET IDENTITY_INSERT Mkt_Seg_Business_Group ON; \n " +
            " insert into Mkt_Seg_Business_Group \n " +
            " (Mkt_Seg_Business_Group_ID, Business_Group_ID, Mkt_Seg_ID, Ranking, Created_By_User_ID, Created_DTTM, Last_Updated_By_User_ID, Last_Updated_DTTM) \n " +
            " values (1, 1, 1, 1, 11403, GETDATE(), 11403, GETDATE()), \n " +
            " (2, 1, 2, 2, 11403, GETDATE(), 11403, GETDATE()), \n " +
            " (3, 1, 3, 3, 11403, GETDATE(), 11403, GETDATE()), \n " +
            " (4, 1, 4, 4, 11403, GETDATE(), 11403, GETDATE()), \n " +
            " (5, 1, 5, 5, 11403, GETDATE(), 11403, GETDATE()), \n " +
            " (6, 2, 6, 6, 11403, GETDATE(), 11403, GETDATE()), \n " +
            " (7, 2, 7, 7, 11403, GETDATE(), 11403, GETDATE()), \n " +
            " (8, 2, 8, 8, 11403, GETDATE(), 11403, GETDATE()), \n " +
            " (9, 2, 9, 9, 11403, GETDATE(), 11403, GETDATE()), \n " +
            " (10, 2, 10, 10, 11403, GETDATE(), 11403, GETDATE()); \n " +
            " SET IDENTITY_INSERT Mkt_Seg_Business_Group OFF; \n ");
    connection.close();
}

And(~/I query to Business View level details for the date range "([^"]*)" to "([^"]*)" which is partially in past and partially in future for unassigned group$/) { String startDate, String endDate ->
    HttpResponseDecorator businessViewLevelDetailsForUnassigned = getBusinessViewLevelDetailsForUnassigned(startDate, endDate)
    String expectedResponse = '[{"id":-1,"name":"Unassigned","onBooks":1373,"systemDemand":null,"userDemand":null,"occupancyForecast":1376.6,"adrOnBooks":117.19,"adrForecast":117.08,"revenue":160908.66,"revenueForecast":161174.43,"revpar":null,"revparForecast":null,"lastYearOnBooks":1324,"last2YearsOnBooks":null,"year2019OnBooks":null,"lastYearSystemDemand":null,"last2YearsSystemDemand":null,"year2019SystemDemand":null,"lastYearUserDemand":null,"last2YearsUserDemand":null,"year2019UserDemand":null,"lastYearOccupancyForecast":1355,"last2YearsOccupancyForecast":null,"year2019OccupancyForecast":null,"lastYearAdrOnBooks":130.71,"last2YearsAdrOnBooks":null,"year2019AdrOnBooks":null,"lastYearAdrForecast":130.59,"last2YearsAdrForecast":null,"year2019AdrForecast":null,"lastYearRevenue":173059.63,"last2YearsRevenue":null,"year2019Revenue":null,"lastYearRevenueForecast":176950.98,"last2YearsRevenueForecast":null,"year2019RevenueForecast":null,"lastYearRevpar":null,"last2YearsRevpar":null,"year2019Revpar":null,"lastYearRevparForecast":null,"last2YearsRevparForecast":null,"year2019RevparForecast":null,"onBooksPickUp":29,"budgetRooms":null,"budgetRevenue":null,"budgetAdr":null,"budgetRevpar":null,"userForecastRooms":null,"userForecastRevenue":null,"userForecastAdr":null,"userForecastRevpar":null,"viewOrder":null,"revenuePickUp":3387.05,"adrPickUp":-0.01,"proPOROnBooks":null,"lastYearProPOROnBooks":null,"lastYearProPORForecast":null,"last2YearsProPOROnBooks":null,"last2YearsProPORForecast":null,"year2019ProPOROnBooks":null,"year2019ProPORForecast":null,"proPORForecast":null,"profitOnBooks":null,"lastYearProfitOnBooks":null,"lastYearProfitForecast":null,"last2YearsProfitOnBooks":null,"last2YearsProfitForecast":null,"year2019ProfitOnBooks":null,"year2019ProfitForecast":null,"profitForecast":null,"proPAROnBooks":null,"lastYearProPAROnBooks":null,"lastYearProPARForecast":null,"last2YearsProPAROnBooks":null,"last2YearsProPARForecast":null,"year2019ProPAROnBooks":null,"year2019ProPARForecast":null,"proPARForecast":null,"capacity":null,"capacityForecast":null,"physicalCapacity":null,"effectiveCapacity":null}]'
    JSONAssert.assertEquals(expectedResponse, businessViewLevelDetailsForUnassigned.getData());
}

And(~/I query to BAD Screen for MS details for inventory group for the date range partially in past and partially in future for dynamic pace$/){->
    CallableStatement cs = null;
    connection = DbConnection.getDatabaseConnection_TLHTL();
    connection.call("SET NOCOUNT ON");
    String queryToRetrieveForecastGroupPropertyLevelDynamicPaceData = "{call usp_bad_load_property_bt_fg_ms_details_for_inventorygroup_dynamicPace(?,?,?,?,?,?,?,?,?,?)}";
    def connection1 = connection.getConnection();
    setNoCountOnBeforeCallingStoredProc(connection1);
    cs = connection1.prepareCall(queryToRetrieveForecastGroupPropertyLevelDynamicPaceData);
    cs.setInt(1, 10022)
    cs.setInt(2, 1)
    cs.setInt(3, 5)
    cs.setInt(4, 2)
    cs.setString(5, "2012-10-15")
    cs.setString(6, "2012-11-15")
    cs.setString(7, "2012-10-23")
    cs.setString(8, "1,3")
    cs.setInt(9, 0)
    cs.setInt(10, 0)
    cs.setEscapeProcessing(true);
    ResultSet rs = cs.executeQuery();
    String data;
    while(rs.next()){
        ResultSetMetaData rsmd = rs.getMetaData();
        int columnsNumber = rsmd.getColumnCount();
        for (int i = 1; i <= columnsNumber; i++) {
            myData = rs.getString(i);
            data = data + " " + myData;
        }
    }
    String expectedData = "null 34 CNR_DEF_FUT 19 19.7 112.95 107.12 2146.00 2114.54 0 0.00 0.00 8 DISC_QN 55 81.1 24.60 25.36 1353.00 2056.83 8 264.00 1.43";
    org.testng.Assert.assertEquals(data, expectedData);
    rs.close();
    connection1.close();
    cs.close();
}

And(~/I query to BAD Screen for MS details for inventory group with as of business date for the date range partially in past and partially in future for dynamic pace$/){->
    CallableStatement cs = null;
    connection = DbConnection.getDatabaseConnection_TLHTL();
    connection.call("SET NOCOUNT ON");
    String queryToRetrieveForecastGroupPropertyLevelDynamicPaceData = "{call usp_bad_load_property_bt_fg_ms_details_asof_businessdate_for_inventorygroup_dynamicPace(?,?,?,?,?,?,?,?,?,?)}";
    def connection1 = connection.getConnection();
    setNoCountOnBeforeCallingStoredProc(connection1);
    cs = connection1.prepareCall(queryToRetrieveForecastGroupPropertyLevelDynamicPaceData);
    cs.setInt(1, 10022)
    cs.setInt(2, 1)
    cs.setInt(3, 5)
    cs.setInt(4, 2)
    cs.setString(5, "2012-10-15")
    cs.setString(6, "2012-11-15")
    cs.setString(7, "2012-10-30")
    cs.setString(8, "1,3")
    cs.setInt(9, 0)
    cs.setInt(10,0)
    cs.setEscapeProcessing(true);
    ResultSet rs = cs.executeQuery();
    String data;
    while(rs.next()){
        ResultSetMetaData rsmd = rs.getMetaData();
        int columnsNumber = rsmd.getColumnCount();
        for (int i = 1; i <= columnsNumber; i++) {
            myData = rs.getString(i);
            data = data + " " + myData;
        }
    }
    String expectedData = "null 34 CNR_DEF_FUT 19 19.7 112.95 107.12 2146.00 2114.54 8 DISC_QN 55 81.1 24.60 25.36 1353.00 2056.83";
    org.testng.Assert.assertEquals(data, expectedData);
    rs.close();
    connection1.close();
    cs.close();
}

And(~/I query to BAD Screen for FG details for inventory group for the date range partially in past and partially in future for dynamic pace$/){->
    CallableStatement cs = null;
    connection = DbConnection.getDatabaseConnection_TLHTL();
    connection.call("SET NOCOUNT ON");
    String queryToRetrieveForecastGroupPropertyLevelDynamicPaceData = "{call usp_bad_load_property_bt_fg_details_for_inventoyryGroup_dynamicPace(?,?,?,?,?,?,?,?,?)}";
    def connection1 = connection.getConnection();
    setNoCountOnBeforeCallingStoredProc(connection1);
    cs = connection1.prepareCall(queryToRetrieveForecastGroupPropertyLevelDynamicPaceData);
    cs.setInt(1, 10022)
    cs.setInt(2, 1)
    cs.setInt(3, 2)
    cs.setString(4, "2012-10-15")
    cs.setString(5, "2012-11-15")
    cs.setString(6, "2012-10-23")
    cs.setString(7, "1,3")
    cs.setInt(8, 0)
    cs.setInt(9,0)
    cs.setEscapeProcessing(true);
    ResultSet rs = cs.executeQuery();
    String data;
    while(rs.next()){
        ResultSetMetaData rsmd = rs.getMetaData();
        int columnsNumber = rsmd.getColumnCount();
        for (int i = 1; i <= columnsNumber; i++) {
            myData = rs.getString(i);
            data = data + " " + myData;
        }
    }
    String expectedData = "null 2 FG_QUALIFIED_LINKED_NONYLD_NSB_1 0 0.00 0.00 0.1 0.00 0.02 0.00 0.00 0 0.00 0.00 3 FG_QUALIFIED_LINKED_SEMIYLD_NSB_1 273 40.31 38.43 310.4 105.53 108.46 28810.34 33664.51 45 4303.86 -1.95 4 FG_QUALIFIED_LINKED_YLD_NSB_1 321 53.98 53.98 369.4 123.55 126.63 39660.39 46775.71 49 6310.88 0.94 5 FG_QUALIFIED_NONYLD_NSB_1 74 38.19 38.19 100.9 47.28 41.36 3499.00 4171.36 8 264.00 -1.73 11 FG_QUALIFIED_SEMIYLD_NSB_1 51 15.59 15.59 58.6 15.00 15.07 765.00 883.12 2 30.00 0.00 6 FG_QUALIFIED_YLD_NSB_1 267 124.18 124.18 360.3 84.98 84.81 22690.30 30555.41 53 4303.30 -0.94 7 FG_QUALIFIED_YLD_NSB_2 81 28.07 28.07 106.6 109.93 109.17 8904.00 11633.91 6 664.00 0.06 8 FG_TRANSIENT_BLOCK_NSB_1 0 0.00 0.00 0.0 0.00 0.00 0.00 0.00 0 0.00 0.00 12 FG_UNQUALIFIED_NSB_1 198 14.70 14.70 208.6 138.83 139.42 27488.82 29078.12 16 1582.60 -3.51 9 FG_UNQUALIFIED_SB_1 684 118.95 118.95 784.2 137.04 139.35 93737.00 109279.48 91 9611.00 -4.82";
    org.testng.Assert.assertEquals(data, expectedData);
    rs.close();
    connection1.close();
    cs.close();
}

And(~/I query to BAD Screen for FG details for inventory group with as of business date for the date range partially in past and partially in future for dynamic pace$/) {
    ->
    CallableStatement cs = null;
    connection = DbConnection.getDatabaseConnection_TLHTL();
    connection.call("SET NOCOUNT ON");
    String queryToRetrieveForecastGroupPropertyLevelDynamicPaceData = "{call usp_bad_load_property_bt_fg_details_asof_businessdate_for_inventoyryGroup_dynamicPace(?,?,?,?,?,?,?,?,?)}";
    def connection1 = connection.getConnection();
    setNoCountOnBeforeCallingStoredProc(connection1);
    cs = connection1.prepareCall(queryToRetrieveForecastGroupPropertyLevelDynamicPaceData);
    cs.setInt(1, 10022)
    cs.setInt(2, 1)
    cs.setInt(3, 2)
    cs.setString(4, "2012-10-15")
    cs.setString(5, "2012-11-15")
    cs.setString(6, "2012-10-30")
    cs.setString(7, "1,3")
    cs.setInt(8, 0)
    cs.setInt(9, 0)
    cs.setEscapeProcessing(true);
    ResultSet rs = cs.executeQuery();
    String data;
    while (rs.next()) {
        ResultSetMetaData rsmd = rs.getMetaData();
        int columnsNumber = rsmd.getColumnCount();
        for (int i = 1; i <= columnsNumber; i++) {
            myData = rs.getString(i);
            data = data + " " + myData;
        }
    }
    String expectedData = "null 2 FG_QUALIFIED_LINKED_NONYLD_NSB_1 0 0.1 0.00 0.02 0.00 0.00 3 FG_QUALIFIED_LINKED_SEMIYLD_NSB_1 273 310.4 105.53 108.46 28810.34 33664.51 4 FG_QUALIFIED_LINKED_YLD_NSB_1 321 369.4 123.55 126.63 39660.39 46775.71 5 FG_QUALIFIED_NONYLD_NSB_1 74 100.9 47.28 41.36 3499.00 4171.36 11 FG_QUALIFIED_SEMIYLD_NSB_1 51 58.6 15.00 15.07 765.00 883.12 6 FG_QUALIFIED_YLD_NSB_1 267 360.3 86.22 84.81 23020.05 30555.41 7 FG_QUALIFIED_YLD_NSB_2 81 106.6 109.93 109.17 8904.00 11633.91 8 FG_TRANSIENT_BLOCK_NSB_1 0 0.0 0.00 0.00 0.00 0.00 12 FG_UNQUALIFIED_NSB_1 200 208.6 139.51 139.42 27901.82 29078.12 9 FG_UNQUALIFIED_SB_1 685 784.2 136.99 139.35 93836.00 109279.48"
    org.testng.Assert.assertEquals(data, expectedData);
    rs.close();
    connection1.close();
    cs.close();
}

And(~/I query to BAD Screen for Business Type details for inventory group for the date range partially in past and partially in future for dynamic pace$/){->
    CallableStatement cs = null;
    connection = DbConnection.getDatabaseConnection_TLHTL();
    connection.call("SET NOCOUNT ON");
    String queryToRetrieveForecastGroupPropertyLevelDynamicPaceData = "{call usp_bad_load_inventory_group_view_bt_details_dynamicPace(?,?,?,?,?,?,?,?)}";
    def connection1 = connection.getConnection();
    setNoCountOnBeforeCallingStoredProc(connection1);
    cs = connection1.prepareCall(queryToRetrieveForecastGroupPropertyLevelDynamicPaceData);
    cs.setInt(1, 10022)
    cs.setInt(2, 1)
    cs.setString(3, "2012-10-15")
    cs.setString(4, "2012-11-15")
    cs.setString(5, "2012-10-23")
    cs.setString(6, "1,3")
    cs.setInt(7, 0)
    cs.setInt(8, 0)
    cs.setEscapeProcessing(true);
    ResultSet rs = cs.executeQuery();
    String data;
    while(rs.next()){
        ResultSetMetaData rsmd = rs.getMetaData();
        int columnsNumber = rsmd.getColumnCount();
        for (int i = 1; i <= columnsNumber; i++) {
            myData = rs.getString(i);
            data = data + " " + myData;
        }
    }
    String expectedData = "null 2 Transient 1949 433.97 432.09 2298.9 115.73 115.73 225554.85 266041.62 257 null null null 39120.00600 5.54 1 Group 160 3.01 3.01 152.6 96.58 97.04 15452.30 14803.93 -2 null null null 0.00000 1.19";
    org.testng.Assert.assertEquals(data, expectedData);
    rs.close();
    connection1.close();
    cs.close();
}

And(~/I query to BAD Screen for Business Type details for inventory group with as of business date for the date range partially in past and partially in future for dynamic pace$/){->
    CallableStatement cs = null;
    connection = DbConnection.getDatabaseConnection_TLHTL();
    connection.call("SET NOCOUNT ON");
    String queryToRetrieveForecastGroupPropertyLevelDynamicPaceData = "{call usp_bad_load_inventory_group_view_bt_details_asof_businessdate_dynamicPace(?,?,?,?,?,?,?,?)}";
    def connection1 = connection.getConnection();
    setNoCountOnBeforeCallingStoredProc(connection1);
    cs = connection1.prepareCall(queryToRetrieveForecastGroupPropertyLevelDynamicPaceData);
    cs.setInt(1, 10022)
    cs.setInt(2, 1)
    cs.setString(3, "2012-10-15")
    cs.setString(4, "2012-11-15")
    cs.setString(5, "2012-10-30")
    cs.setString(6, "1,3")
    cs.setInt(7, 0)
    cs.setInt(8, 0)
    cs.setEscapeProcessing(true);
    ResultSet rs = cs.executeQuery();
    String data;
    while(rs.next()){
        ResultSetMetaData rsmd = rs.getMetaData();
        int columnsNumber = rsmd.getColumnCount();
        for (int i = 1; i <= columnsNumber; i++) {
            myData = rs.getString(i);
            data = data + " " + myData;
        }
    }
    String expectedData = "null 2 Transient 1952 2298.90 115.98 115.73 226396.60 266041.62 1 Group 170 152.55 96.72 97.04 16442.30 14803.93";
    org.testng.Assert.assertEquals(data, expectedData);
    rs.close();
    connection1.close();
    cs.close();
}

And(~/I query to BAD Screen for Business View details for inventory group for the date range partially in past and partially in future for dynamic pace$/){->
    CallableStatement cs = null;
    connection = DbConnection.getDatabaseConnection_TLHTL();
    connection.call("SET NOCOUNT ON");
    String queryToRetrieveForecastGroupPropertyLevelDynamicPaceData = "{call usp_bad_load_property_bv_details_for_inventorygroup_dynamicPace(?,?,?,?,?,?,?,?,?)}";
    def connection1 = connection.getConnection();
    setNoCountOnBeforeCallingStoredProc(connection1);
    cs = connection1.prepareCall(queryToRetrieveForecastGroupPropertyLevelDynamicPaceData);
    cs.setInt(1, 10022)
    cs.setInt(2, 1)
    cs.setString(3, "2012-10-15")
    cs.setString(4, "2012-11-15")
    cs.setString(5, "2012-10-23")
    cs.setString(6, "1")
    cs.setString(7, "1,3")
    cs.setInt(8, 0)
    cs.setInt(9, 0)
    cs.setEscapeProcessing(true);
    ResultSet rs = cs.executeQuery();
    String data;
    while(rs.next()){
        ResultSetMetaData rsmd = rs.getMetaData();
        int columnsNumber = rsmd.getColumnCount();
        for (int i = 1; i <= columnsNumber; i++) {
            myData = rs.getString(i);
            data = data + " " + myData;
        }
    }
    String expectedData = "null 1 TLHTLBusinessGroup 312 353.7 107.96 110.80 33684.43 39190.33 53 5291.54 -1.66";
    org.testng.Assert.assertEquals(data, expectedData);
    rs.close();
    connection1.close();
    cs.close();
}

And(~/I query to BAD Screen for Business View details for inventory group with as of business date for the date range partially in past and partially in future for dynamic pace$/){->
    CallableStatement cs = null;
    connection = DbConnection.getDatabaseConnection_TLHTL();
    connection.call("SET NOCOUNT ON");
    String queryToRetrieveForecastGroupPropertyLevelDynamicPaceData = "{call usp_bad_load_property_bv_details_asof_businessdate_for_inventorygroup_dynamicPace(?,?,?,?,?,?,?,?,?)}";
    def connection1 = connection.getConnection();
    setNoCountOnBeforeCallingStoredProc(connection1);
    cs = connection1.prepareCall(queryToRetrieveForecastGroupPropertyLevelDynamicPaceData);
    cs.setInt(1, 10022)
    cs.setInt(2, 1)
    cs.setString(3, "2012-10-15")
    cs.setString(4, "2012-11-15")
    cs.setString(5, "2012-10-30")
    cs.setString(6, "1")
    cs.setString(7, "1,3")
    cs.setInt(8, 0)
    cs.setInt(9, 0)
    cs.setEscapeProcessing(true);
    ResultSet rs = cs.executeQuery();
    String data;
    while(rs.next()){
        ResultSetMetaData rsmd = rs.getMetaData();
        int columnsNumber = rsmd.getColumnCount();
        for (int i = 1; i <= columnsNumber; i++) {
            myData = rs.getString(i);
            data = data + " " + myData;
        }
    }
    String expectedData = "null 1 TLHTLBusinessGroup 312 353.7 107.96 110.80 33684.43 39190.33";
    org.testng.Assert.assertEquals(data, expectedData);
    rs.close();
    connection1.close();
    cs.close();
}

Then(~/I should see expected data for given condition of BAD Screen$/) { ->
}

private void setNoCountOnBeforeCallingStoredProc(Connection connection1) {
    Statement stmt = connection1.createStatement()
    stmt.execute("SET NOCOUNT ON;")
    stmt.close()
}