package com.ideas.tetris.cucumber.stepdefinition.g3.marketsegmentrecoding

import com.fasterxml.jackson.databind.ObjectMapper
import com.ideas.g3.client.rest.RestServiceUtil
import com.ideas.g3.client.rest.TetrisRESTClient
import com.ideas.tetris.util.DbConnection
import com.ideas.tetris.util.PropertiesTestHelper
import com.ideas.tetris.util.propertyrollout.PropertyRolloutRESTUtil
import common.ForecastGroupUtil
import common.JemsTest
import common.JemsUtil
import common.OperaUtil
import groovyx.net.http.ContentType
import groovyx.net.http.HttpResponseDecorator
import groovyx.net.http.HttpResponseException
import io.cucumber.datatable.DataTable
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang.StringUtils
import org.apache.log4j.Logger

import java.text.MessageFormat

import static org.apache.commons.lang.StringUtils.isBlank
import static org.apache.commons.lang.StringUtils.rightPad
import static org.junit.Assert.assertTrue

class MarketSegmentRecodingTestUtil {
    static final String PROPERTY_ID = "11033"
    static final String DATABASE_NAME = "011033"
    static final String PROPERTY_CODE = "CPGP02"
    static final String REST_BASE_URI = RestServiceUtil.getPacmanRestURL()
    static final String MARKET_SEGMENT_RECODING_JOB = "MktSegRecodingJob"
    private static TetrisRESTClient tetrisRestClient
    static final String PROPERTY_CLIENT_NODE_NAME = "pacman.SandBox." + PROPERTY_CODE
    static Integer mktSegRecordsCountBeforeBackup
    static Integer analyticalMktSegRecordsCountBeforeBackup
    static final String RESOLVE_ALERT_URL = "alerts/doAlertStep/v1"
    def static LOGGER = Logger.getLogger(MarketSegmentRecodingTestUtil.class)
    static final String COLLATE = " COLLATE SQL_Latin1_General_CP1_CS_AS "

    static void setup() {
        tetrisRestClient = createTetrisRestClientForRestCall()
        PropertyRolloutRESTUtil.setConfigParamValue(tetrisRestClient, 'pacman.feature.AnalyticalMarketSegmentEnabled',PROPERTY_CLIENT_NODE_NAME, "true");
        PropertyRolloutRESTUtil.setConfigParamValue(tetrisRestClient, 'pacman.feature.MSRecodingEnabled',PROPERTY_CLIENT_NODE_NAME, "true");
        PropertiesTestHelper.setPropertyStage(tetrisRestClient, PROPERTY_ID, "OneWay")

        def globalDbConnection = DbConnection.getGlobalConnection();
        globalDbConnection.call("IF EXISTS (SELECT * FROM sys.objects WHERE object_id = Object_id('[Mkt_Seg_Recoding_Config]') AND type IN ('U')) truncate table Mkt_Seg_Recoding_Config")
        globalDbConnection.close()

        def connection = DbConnection.getTenantDatabaseConnection(DATABASE_NAME);
        connection.call("IF EXISTS (SELECT * FROM sys.objects WHERE object_id = Object_id('[PMS_Migration_Mapping]') AND type IN ('U')) truncate table PMS_Migration_Mapping")
        connection.call("IF EXISTS (SELECT * FROM sys.objects WHERE object_id = Object_id('[PMS_Revamp_New_AMS_Rule]') AND type IN ('U')) truncate table PMS_Revamp_New_AMS_Rule")
        connection.call("IF NOT EXISTS (SELECT * FROM sys.objects " +
                "  WHERE object_id = Object_id('[PMS_Revamp_New_AMS_Rule]') AND type IN ('U')) " +
                "  create table dbo.PMS_Revamp_New_AMS_Rule (" +
                "  PMS_Revamp_New_AMS_Rule_ID [int] IDENTITY(1,1) NOT NULL," +
                "  Market_Code [nvarchar](50) NOT NULL," +
                "  Rate_Code [nvarchar](255) NULL," +
                "  Attribute [nvarchar](50) NULL," +
                "  Forecast_Activity_Type_Id [int] NULL," +
                "  Product_Id [int] NULL," +
                "  CONSTRAINT [PK_PMS_Revamp_New_AMS_Rule] PRIMARY KEY CLUSTERED([PMS_Revamp_New_AMS_Rule_ID]))")

        //MS_Recoding_Business_Type_Shift
        connection.call("IF EXISTS (SELECT * FROM sys.objects WHERE object_id = Object_id('[MS_Recoding_Business_Type_Shift]') AND type IN ('U')) truncate table MS_Recoding_Business_Type_Shift")
        connection.call("IF NOT EXISTS (SELECT * FROM sys.objects " +
                "   WHERE object_id = Object_id('[MS_Recoding_Business_Type_Shift]') AND type IN ('U'))" +
                "       create table dbo.MS_Recoding_Business_Type_Shift(" +
                "           MS_Recoding_Business_Type_Shift_ID [int] IDENTITY(1,1) NOT NULL," +
                "           Market_Segment_Name [nvarchar](100) NOT NULL," +
                "           Previous_Business_Type [nvarchar](100) NOT NULL," +
                "           New_Business_Type [nvarchar](100) NOT NULL," +
                "           CONSTRAINT [PK_MS_Recoding_Business_Type_Shift] PRIMARY KEY CLUSTERED([MS_Recoding_Business_Type_Shift_ID])" +
                "       )")

        connection.close()
        //this property has reservations without MktCode which we need to update using MktSegID then only our pre-condition assertions will work
        assignMarketCodesToReservationsWithBlankMarketCodes("Reservation_Night");
        assignMarketCodesToReservationsWithBlankMarketCodes("Reservation_Night_Change");
        calculateMktSegRecordsCountBeforeBackup()
        analyticalMktSegRecordsCountBeforeBackup = getTenantTableRecordsCountByQuery("SELECT COUNT(*) as cnt FROM Analytical_Mkt_Seg")
    }

    private static void calculateMktSegRecordsCountBeforeBackup() {
        mktSegRecordsCountBeforeBackup = getTenantTableRecordsCountByQuery("SELECT COUNT(*) as cnt FROM Mkt_Seg")
    }

    private static TetrisRESTClient createTetrisRestClientForRestCall() {
        TetrisRESTClient tetrisRESTClient = PropertiesTestHelper.login(REST_BASE_URI, "<EMAIL>", "password")
        tetrisRESTClient.headers = [clientId :6 ,propertyId : PROPERTY_ID, Authorization: "Basic ********************************"]
        return tetrisRESTClient;
    }

    static void assignMarketCodesToReservationsWithBlankMarketCodes(String tableName) {
        try {
            def response = tetrisRestClient.put(path: REST_BASE_URI + "reservations/update-blank-reservations/v1/" + tableName,
                    query: ['startDate': "2015-09-13", 'endDate': "2017-10-30"], requestContentType: 'application/json')
        } catch (HttpResponseException ex) {
            println("\n" + ex)
        }
    }

    static def importNewAMSRules(DataTable NewAMSRulesData) {
        def connection = DbConnection.getTenantDatabaseConnection(DATABASE_NAME)
        connection.call("SET IDENTITY_INSERT PMS_Revamp_New_AMS_Rule OFF;")
        def mktSegWiseRateCodes = [:]
        for (int i = 1; i < NewAMSRulesData.cells().size(); i++) {
            String marketCode = NewAMSRulesData.cell(i,0).toString()
            def rateCodeString = NewAMSRulesData.cell(i,1).toString()
            String rateCode = (isBlank(rateCodeString) || (rateCodeString.equals("NULL")) ? null : ("'" + rateCodeString + "'"));
            String attribute = NewAMSRulesData.cell(i,2).toString()
            String forecastType = NewAMSRulesData.cell(i,3).toString()
            String query = "insert into PMS_Revamp_New_AMS_Rule (Market_Code, Rate_Code, Attribute, Forecast_Activity_Type_Id) values('" + marketCode + "'," + rateCode + ",'" + attribute + "','" + forecastType + "')"
            println(query)
            List rateCodes = mktSegWiseRateCodes.get(marketCode)
            if (null == rateCodes) {
                rateCodes = new ArrayList()
            }
            if (!rateCode.equals("DEFAULT")) {
                rateCodes.add(rateCode)
                mktSegWiseRateCodes.put(marketCode, rateCodes)
            }
            connection.call(query)
        }
        connection.close()
        println("MktSegWiseRateCodes : " + mktSegWiseRateCodes)
        return mktSegWiseRateCodes
    }

    static void insertBusinessTypeShiftData(DataTable BusinessTypeShiftData) {
        def connection = DbConnection.getTenantDatabaseConnection(DATABASE_NAME)
        connection.call("SET IDENTITY_INSERT MS_Recoding_Business_Type_Shift OFF;")
        for (int i = 1; i < BusinessTypeShiftData.cells().size(); i++) {
            String marketSegmentName = BusinessTypeShiftData.cell(i, 0).toString()
            String previousBusinessType = BusinessTypeShiftData.cell(i, 1).toString()
            String newBusinessType = BusinessTypeShiftData.cell(i, 2).toString()
            String query = "insert into MS_Recoding_Business_Type_Shift values('" + marketSegmentName + "','" + previousBusinessType + "','" + newBusinessType + "')"
            println(query)
            connection.call(query)
        }
        connection.close()
    }

    static void importNewAMSRulesWithByConsideringRateCodeShiftAndIgnoreMissingRateCodes(DataTable NewAMSRulesData) {
        def mktSegWiseRateCodes = importNewAMSRules(NewAMSRulesData)
        def rateCodeShiftAssociationResponse = callFindRateCodeShiftAssociations(mktSegWiseRateCodes)
        println("After conversion of Rate code associations: " + rateCodeShiftAssociationResponse)
        callSaveRateCodeShiftAssociations(rateCodeShiftAssociationResponse)
        def response = callCalculateMissingRateCodes(mktSegWiseRateCodes)
        if (null != response) {
            def mapMissingRateCodes = (Map) response.data
            def ignoreRateCodes = convertMissingRateCodeMapToObject(mapMissingRateCodes)
            callSaveMissingRateCodes(ignoreRateCodes)
        }
    }

    static void importNewAMSRulesWithIgnoreMissingRateCodes(DataTable NewAMSRulesData) {
        def mktSegWiseRateCodes = importNewAMSRules(NewAMSRulesData)
        def response = callCalculateMissingRateCodes(mktSegWiseRateCodes)
        def mapMissingRateCodes = (Map) response.data
        def ignoreRateCodes = convertMissingRateCodeMapToObject(mapMissingRateCodes)
        callSaveMissingRateCodes(ignoreRateCodes)
    }

    private static void callSaveMissingRateCodes(ArrayList<IgnoreRateCode> ignoreRateCodes) {
        def saveMissingRateCodesResponse
        try {
            saveMissingRateCodesResponse = tetrisRestClient.post(
                    path: REST_BASE_URI + "pmsRevampAMSConfig/ignoreMismatchedRateCodes/v1",
                    requestContentType: 'application/json',
                    body: ignoreRateCodes)
            println("response from save ignore missing rate codes : " + saveMissingRateCodesResponse)
        } catch (HttpResponseException ex) {
            println("\n" + ex)
        }
    }

    private static def convertMissingRateCodeMapToObject(Map mapMissingRateCodes) {
        List ignoreRateCodes = new ArrayList()
        def iterator = mapMissingRateCodes.keySet().iterator()
        while (iterator.hasNext()) {
            def marketCode = iterator.next()
            List rateCodes = mapMissingRateCodes.get(marketCode)
            def rateCodeIterator = rateCodes.iterator()
            while (rateCodeIterator.hasNext()) {
                def rateCode = rateCodeIterator.next()
                def ignoreRateCode = new IgnoreRateCode()
                ignoreRateCode.setMarketSegment(marketCode)
                ignoreRateCode.setRateCode(rateCode)
                ignoreRateCode.setMismatchType("MISSING")
                ignoreRateCodes.add(ignoreRateCode)
            }
        }
        ignoreRateCodes
    }

    private static def callSaveRateCodeShiftAssociations(def rateCodeAssociations) {
        def response
        try {
            response = tetrisRestClient.post(
                    path: REST_BASE_URI + "mktSegRecoding/saveRateCodeShiftAssociations/v1",
                    requestContentType: 'application/json',
                    body: rateCodeAssociations)
            println("response from saveRateCodeShiftAssociations : " + response)
        } catch (HttpResponseException ex) {
            println("\n" + ex)
        }
        print("response=" + response)
    }

    private static def getPaceBackFillMktSegIds() {
        def response
        try {
            response = tetrisRestClient.get(
                    path: REST_BASE_URI + "mktSegRecoding/getPaceBackFillMarketSegmentIdsForMSRecoding/v1",
                    requestContentType: 'application/json')
            println("response for pace backfill Mkt Seg Ids : " + response.data)
        } catch (HttpResponseException ex) {
            println("\n" + ex)
        }
        return response.data
    }
    private static def getPaceBackFillMktSegIdsUsingJobExecutionContext() {
        ObjectMapper mapper = new ObjectMapper();
        def response = MarketSegmentRecodingTestUtil.getJobExecutionContext()
        Map<String, String> map = mapper.readValue(response, Map.class);
        def keyList = map.values().collect().get(0)
        def contextValues = keyList[0].getAt("entry")
        def mktSegIds = contextValues.getAt(8).toString().split(":").getAt(3).toString().replace("[", "").replace("]", "").split(", ").collect()
    }

    private static def callFindRateCodeShiftAssociations(LinkedHashMap<Object, Object> mktSegWiseRateCodes) {
        def response
        try {
            response = tetrisRestClient.post(
                    path: REST_BASE_URI + "mktSegRecoding/findRateCodeIsShiftedFromPreviousMarketSegmentToOther/v1",
                    requestContentType: 'application/json',
                    body: mktSegWiseRateCodes)
            println("response from find rate code shift associations : " + response.data)
        } catch (HttpResponseException ex) {
            println("\n" + ex)
        }
        return response.data
    }

    private static def callCalculateMissingRateCodes(LinkedHashMap<Object, Object> mktSegWiseRateCodes) {
        def response
        try {
            response = tetrisRestClient.post(
                    path: REST_BASE_URI + "mktSegRecoding/calculateMissingRateCodes/v1",
                    requestContentType: 'application/json',
                    body: mktSegWiseRateCodes)
            println("response from calculate missing rate codes : " + response)
        } catch (HttpResponseException ex) {
            println("\n" + ex)
        }
        print("response=" + response)
        return response
    }

    static int getTenantTableRecordsCountByQuery(String query) {
        def connection = DbConnection.getTenantDatabaseConnection(DATABASE_NAME)
        def rows_cnt = connection.rows(query)[0].cnt
        connection.close()
        return rows_cnt;
    }

    static void importMarketSegmentRecodingConfiguration(DataTable Mkt_Seg_Recoding_Config) {
        def connection = DbConnection.getGlobalConnection()
        connection.call("SET IDENTITY_INSERT Mkt_Seg_Recoding_Config OFF;")
        for (int i = 1; i < Mkt_Seg_Recoding_Config.cells().size(); i++) {
            int clientId = Mkt_Seg_Recoding_Config.cell(i,0).toInteger()
            int propertyId = Mkt_Seg_Recoding_Config.cell(i,1).toInteger()
            String oldSysDate = Mkt_Seg_Recoding_Config.cell(i,2).toString()
            String recodingDate = Mkt_Seg_Recoding_Config.cell(i,3).toString()
            int created_by_User_ID = Mkt_Seg_Recoding_Config.cell(i,4).toInteger()
            String created_DTTM = Mkt_Seg_Recoding_Config.cell(i,5).toString()
            String last_Updated_DTTM = Mkt_Seg_Recoding_Config.cell(i,6).toString()
            String last_Updated_by_User_ID = Mkt_Seg_Recoding_Config.cell(i,7).toString()
            int is_Backup_Created = Mkt_Seg_Recoding_Config.cell(i,8).toInteger()

            String query = "insert into Mkt_Seg_Recoding_Config values(" + clientId + "," + propertyId + ",'" + oldSysDate + "','" + recodingDate + "'," + created_by_User_ID + ",'" + created_DTTM + "','" + last_Updated_DTTM + "'," + last_Updated_by_User_ID + "," + is_Backup_Created + ")"
            println(query)
            connection.call(query)
        }
        connection.close()
    }

    static void importMappingConfiguration(DataTable PMS_Migration_Mapping) {
        def connection = DbConnection.getTenantDatabaseConnection(DATABASE_NAME)
        connection.call("SET IDENTITY_INSERT PMS_Migration_Mapping OFF;")
        for (int i = 1; i < PMS_Migration_Mapping.cells().size(); i++) {
            String pmsPropertyCode = PMS_Migration_Mapping.cell(i,0).toString()
            String codeType = PMS_Migration_Mapping.cell(i,1).toString()
            String currentCode = PMS_Migration_Mapping.cell(i,2).toString()
            String newEquivalentCode = PMS_Migration_Mapping.cell(i,3).toString()
            String isDiscontinued = PMS_Migration_Mapping.cell(i,4).toString()
            String isPrimary = PMS_Migration_Mapping.cell(i,5).toString()
            String isPrimaryParam = isPrimary.equalsIgnoreCase("NULL") || isPrimary.isEmpty() ? "NULL" : "'" + isPrimary + "'"
            String query = "insert into PMS_Migration_Mapping values('" + pmsPropertyCode + "','" + codeType + "','" + currentCode + "','" + newEquivalentCode + "','" + isDiscontinued + "'," + isPrimaryParam + ")"
            println(query)
            connection.call(query)
        }
        connection.close()
    }

    static void assertAMSTable(DataTable Analytical_Market_Segment) {
        def connection = DbConnection.getTenantDatabaseConnection(DATABASE_NAME)
        for (int i = 1; i < Analytical_Market_Segment.cells().size(); i++) {
            int analyticalMktSegId = Analytical_Market_Segment.cell(i, 0).toInteger()
            String marketCode = Analytical_Market_Segment.cell(i, 1).toString()
            String rateCodeString = Analytical_Market_Segment.cell(i, 2).toString()
            String rateCode = isBlank(rateCodeString) ? '' : rateCodeString;
            String mappedMarketCode = Analytical_Market_Segment.cell(i, 3).toString()
            String attribute = Analytical_Market_Segment.cell(i, 4).toString()
            String rateCodeType = Analytical_Market_Segment.cell(i, 5).toString()
            int rank = Analytical_Market_Segment.cell(i, 6).toInteger()
            int complementary = Analytical_Market_Segment.cell(i, 7).toInteger()
            int preserved = Analytical_Market_Segment.cell(i, 8).toInteger()

            String query;
            if (rateCodeString.equalsIgnoreCase("NULL")) {
                query = "(select count(*) as cnt from Analytical_Mkt_Seg ams where ams.Analytical_Mkt_Seg_ID=" + analyticalMktSegId +
                        " and ams.Market_Code " + COLLATE + " ='" + marketCode + "' " + COLLATE + " and Rate_Code IS NULL and Mapped_Market_Code " + COLLATE + "='" + mappedMarketCode + "' and " +
                        " Attribute='" + attribute + "' and Rate_Code_Type='" + rateCodeType + "' and Rank=" + rank + " and complimentary=" + complementary +
                        " and Preserved=" + preserved + ")"
            } else {
                query = "(select count(*) as cnt from Analytical_Mkt_Seg ams where ams.Analytical_Mkt_Seg_ID=" + analyticalMktSegId +
                        " and ams.Market_Code " + COLLATE + "='" + marketCode + "' " + COLLATE + " and Rate_Code='" + rateCode + "' and Mapped_Market_Code " + COLLATE + "='" + mappedMarketCode + "' " + COLLATE + " and " +
                        " Attribute='" + attribute + "' and Rate_Code_Type='" + rateCodeType + "' and Rank=" + rank + " and complimentary=" + complementary +
                        " and Preserved=" + preserved + ")"
            }

            println(query)
            def count = getTenantTableRecordsCountByQuery(query)
            assert 1 == count: "Given record not found in Analytical_Mkt_Seg."
        }

        connection.close()
    }

    static void assertRateCodeShiftAssociations(DataTable rateCodeShiftAssociations){
        def connection = DbConnection.getTenantDatabaseConnection(DATABASE_NAME)
        for (int i = 1; i < rateCodeShiftAssociations.cells().size(); i++) {
            String rateCode = rateCodeShiftAssociations.cell(i,0).toString()
            String previousMarketSegment = rateCodeShiftAssociations.cell(i,1).toString()
            String newMarketSegment = rateCodeShiftAssociations.cell(i,2).toString()
            def query = "select count(*) as cnt from MS_Recoding_Rate_Code_Shift where Rate_Code =" +
                    " '"+rateCode+"' and Previous_Market_Segment = '"+previousMarketSegment+"' " +
                    "and New_Market_Segment = '"+newMarketSegment+"' "
            def count = getTenantTableRecordsCountByQuery(query)
            assert 1 == count : "Given record not found in MS_Recoding_Rate_Code_Shift."
        }
        connection.close()
    }

    static void assertMissingRateCodesNotAvailable(){
        def connection = DbConnection.getTenantDatabaseConnection(DATABASE_NAME)
        def query = "IF EXISTS (SELECT * FROM sys.objects " +
                " WHERE object_id = Object_id('[PMS_Revamp_Ignored_Rate_Code]') AND type IN ('U')) " +
                " select 1 as cnt ELSE select 0 as cnt "
        def count = getTenantTableRecordsCountByQuery(query)
        if(count == 1){
            count = getTenantTableRecordsCountByQuery("select count(*) as cnt from PMS_Revamp_Ignored_Rate_Code ")
        }
        assert 0 == count : "There are records found in PMS_Revamp_Ignored_Rate_Code."

        connection.close()
    }

    static void assertMissingRateCodes(DataTable missingRateCodes){
        def connection = DbConnection.getTenantDatabaseConnection(DATABASE_NAME)
        for (int i = 1; i < missingRateCodes.cells().size(); i++) {
            String marketCode = missingRateCodes.cell(i,0).toString()
            String rateCodeString = missingRateCodes.cell(i,1).toString()
            def query = "select count(*) as cnt from PMS_Revamp_Ignored_Rate_Code where Market_Code =" +
                    " '"+marketCode+"' and Rate_Code = '"+rateCodeString+"' "
            def count = getTenantTableRecordsCountByQuery(query)
            assert 1 == count : "Given record not found in PMS_Revamp_Ignored_Rate_Code."
        }
        connection.close()
    }

    static void assertPropertyStageTo(String expectedPropertyStage) {
        String propertyStage = MarketSegmentRecodingTestUtil.findByQueryInGlobalDb("select stage from property where property_id  = '${MarketSegmentRecodingTestUtil.PROPERTY_ID}';").get("stage").toString()
        assert expectedPropertyStage.equals(propertyStage): "Property stage should be " + expectedPropertyStage + " but it is " + propertyStage
    }

    static void assertTablesBackup() {
        int countAfterBackup = getTenantTableRecordsCountByQuery("SELECT COUNT(*) as cnt FROM Mkt_Seg_Pms_Bkp")
        assert mktSegRecordsCountBeforeBackup == countAfterBackup: "Backup activity failed for Mkt_Seg"

        countAfterBackup = getTenantTableRecordsCountByQuery("SELECT COUNT(*) as cnt FROM Analytical_Mkt_Seg_Pms_Bkp")
        assert analyticalMktSegRecordsCountBeforeBackup == countAfterBackup: "Backup activity failed for Analytical_Mkt_Seg"
    }

    static def findByQuery(String query) {
        def connection = DbConnection.getTenantDatabaseConnection(DATABASE_NAME)
        def result = connection.firstRow(query)
        connection.close()
        return result
    }

    static def findByQueryInGlobalDb(String query){
        def connection = DbConnection.getGlobalConnection()
        def result = connection.firstRow(query)
        connection.close()
        return result
    }

    static void assertMktSegTable(DataTable Mkt_Seg) {
        def connection = DbConnection.getTenantDatabaseConnection(DATABASE_NAME)

        for (int i = 1; i < Mkt_Seg.cells().size(); i++) {
            int marketSegmentId = Mkt_Seg.cell(i, 0).toInteger()
            int propertyId = Mkt_Seg.cell(i, 1).toInteger()
            String marketSegmentCode = Mkt_Seg.cell(i, 2).toString()
            String marketSegmentName = Mkt_Seg.cell(i, 3).toString()
            String marketSegmentDescription = Mkt_Seg.cell(i, 4).toString()
            int statusId = Mkt_Seg.cell(i, 5).toInteger()
            int complimentary = Mkt_Seg.cell(i, 6).toInteger()
            String query = "(select count(*) as cnt from Mkt_Seg ms where Mkt_Seg_ID=" + marketSegmentId + " and Property_ID=" + propertyId +
                    " and Mkt_Seg_Code " + COLLATE + "='" + marketSegmentCode + "' " + COLLATE +
                    " and Mkt_Seg_Name " + COLLATE + "='" + marketSegmentName + "' " + COLLATE +
                    " and Mkt_Seg_Description='" + marketSegmentDescription + "' and Status_ID=" + statusId + " and complimentary=" + complimentary + ")"
            println(query)
            def count = getTenantTableRecordsCountByQuery(query)
            assert 1 == count: "Given record not found in Mkt_Seg table."
        }

        connection.close()
    }

    static void assertThatOldMarketSegmentsAreDeletedFromMktSegTable(DataTable Mkt_Seg) {
        def connection = DbConnection.getTenantDatabaseConnection(DATABASE_NAME)

        for (int i = 1; i < Mkt_Seg.cells().size(); i++) {
            int marketSegmentId = Mkt_Seg.cell(i, 0).toInteger()
            int propertyId = Mkt_Seg.cell(i, 1).toInteger()
            String marketSegmentCode = Mkt_Seg.cell(i, 2).toString()
            String marketSegmentName = Mkt_Seg.cell(i, 3).toString()
            String marketSegmentDescription = Mkt_Seg.cell(i, 4).toString()
            int statusId = Mkt_Seg.cell(i, 5).toInteger()
            int complimentary = Mkt_Seg.cell(i, 6).toInteger()
            String query = "(select count(*) as cnt from Mkt_Seg ms where Mkt_Seg_ID=" + marketSegmentId + " and Property_ID=" + propertyId +
                    " and Mkt_Seg_Code='" + marketSegmentCode + "' and Mkt_Seg_Name='" + marketSegmentName + "' and Mkt_Seg_Description='" + marketSegmentDescription + "' and Status_ID=" + statusId + " and complimentary=" + complimentary + ")"
            println(query)
            def count = getTenantTableRecordsCountByQuery(query)
            assert 0 == count: "Given record found in Mkt_Seg table."
        }

        connection.close()
    }

    static void assertDeletionOfMktSegTable(DataTable Mkt_Seg) {
        def connection = DbConnection.getTenantDatabaseConnection(DATABASE_NAME)
        for (int i = 1; i < Mkt_Seg.cells().size(); i++) {
            int marketSegmentId = Mkt_Seg.cell(i,0).toInteger()
            String marketSegmentCode = Mkt_Seg.cell(i,1).toString()
            String query = "(select count(*) as cnt from Mkt_Seg ms " +
                    " where Mkt_Seg_ID=" + marketSegmentId +
                    " and Mkt_Seg_Code " + COLLATE + "='" + marketSegmentCode + "' " + COLLATE + ")"
            println(query)
            def count = getTenantTableRecordsCountByQuery(query)
            assert 0 == count : "Market Segment "+marketSegmentCode+" found in Mkt_Seg table which is not expected."
        }
        connection.close()
    }


    static String getJobInstanceExitStatus(Long jobInstanceId) {
        def sql = "select exit_code from Job_Execution where job_execution_id in "+
                "(select max(job_execution_id) from Job_Execution where Job_Instance_Id = ${jobInstanceId})"
        return (String) com.ideas.tetris.util.db.DbConnection.getJobDbConnection().firstRow(sql).getAt(0)
    }

    static boolean abandonFailedJobByJobExecutionId(Long jobInstanceId) {
        def status = getJobInstanceExitStatus(jobInstanceId)
        final String URL_JOB_ABANDON = "jobs/abandon/v1/{0,number,#}"
        if(status.equals("FAILED")) {
            def response = invokeJobActionService(jobInstanceId, URL_JOB_ABANDON)
            return true;
        }
        print("Job is already abandoned or completed...")
        return false;
    }

    static void triggerMktSegRecodingJob() {
        try {
            def response = tetrisRestClient.post(path: RestServiceUtil.getPacmanRestURL() + "mktSegRecoding/startMktSegRecodingJob/v1", requestContentType: 'application/json')
        } catch (HttpResponseException ex) {
            //OK
            println("\n" + ex)

        }
        OperaUtil.checkJobStatus(MARKET_SEGMENT_RECODING_JOB)
    }

    static def invokeJobActionService(Long jobInstanceOrExecutionId, String url) {
        def path = MessageFormat.format(url, jobInstanceOrExecutionId)
        Map<String, Object> parameters = new HashMap<String, Object>()
        parameters.put("propertyId", PROPERTY_ID)
        def httpResponse
        try {
            httpResponse = tetrisRestClient.post(path: path, query: parameters)
            sleep(200) // Let rest service execute
        } catch (HttpResponseException ex) {
            println "Failed to execute job service " + path + " for job " + jobInstanceOrExecutionId + ": " + ex.response.data
            assert !ex.response: ("Failed to execute job service " + path + " for job " + jobInstanceOrExecutionId)
        }
        return httpResponse
    }

    static def resolveAlertAndResumeStep(String alertName) {
        try{
            HttpResponseDecorator alertID = tetrisRestClient.get(path: REST_BASE_URI + "alerts/property/v1/" + alertName + "/New/" + PROPERTY_ID, requestContentType: 'text/plain')
            tetrisRestClient.post(path: REST_BASE_URI + RESOLVE_ALERT_URL, query: ['propertyId': PROPERTY_ID, 'alertId': alertID.responseData.getAt("alertId"), 'stepId': 503, 'label': "ACTION"], requestContentType: ContentType.JSON)
        }catch (HttpResponseException ex) {
            println ex.response.data
            assert !ex.response
        }
    }

    static String checkJobStatusAndGetFailedJob() {
        Long jobInstanceID = DbConnection.getJobDbConnection().rows(" select max(ji.job_instance_id) jobInstanceID " +
                "from Job_Instance ji inner join job_instance_work_context jwc on ji.job_instance_id=jwc.job_instance_id " +
                "where ji.Job_Name='" + MARKET_SEGMENT_RECODING_JOB + "' and jwc.property_Id = "+ PROPERTY_ID.toInteger())[0].jobInstanceID
        JemsTest jemsTest = new JemsTest() {
            @Override
            protected String getPropertyId() {
                return null
            }

            @Override
            protected String getPropertyCode() {
                return null
            }
        }

        jemsTest.setJobDbConnection(DbConnection.getJobDbConnection())
        jemsTest.JOB_INSTANCES_LAUNCHED.get().add(jobInstanceID);
        jemsTest.waitForRunningJobs()
        return getFailedStepName(jemsTest)
    }

    static private String getFailedStepName(JemsTest jemsTest) {
        // Build a Map of non-completed job instances
        Map<Long, String> nonCompletedJobInstances = new TreeMap<Long, String>()
        for (Long jobInstanceId : jemsTest.JOB_INSTANCES_LAUNCHED.get()) {
            String status = jemsTest.getJobInstanceStatus(jobInstanceId)
            if ("FAILED".equals(status)) {
                nonCompletedJobInstances.put(jobInstanceId, status)
            }
        }
        // If we have non-completed job instances, let's get the problem details and write out what failed (purposely not filtering on a specific client/property)
        if (!nonCompletedJobInstances.isEmpty()) {
            // Print out a list of all non-completed job instances with their current status
            println "================================================================================================================================================="
            println "NON-COMPLETED JOB INSTANCES LAUNCHED DURING TEST"
            println "================================================================================================================================================="
            println "ID      	       CURRENT STATUS"
            println "--------------------------------"
            for (Long jobInstanceId : nonCompletedJobInstances.keySet()) {
                println rightPad(String.valueOf(jobInstanceId), 18) + " " + nonCompletedJobInstances.get(jobInstanceId)
            }
            println "--------------------------------"

            // Get a list of active problems
            def problems = tetrisRestClient.get(path: REST_BASE_URI + "problem/active/v1", requestContentType: ContentType.JSON)
            if (problems.responseData != null) {
                def problem = problems.responseData as List
                problem= problem.sort {it.creationDate  }
                problem = problem[-1]
                if ((problem.getAt("propertyId") == PROPERTY_ID.toInteger()) && (problem.getAt("jobName").equals(MARKET_SEGMENT_RECODING_JOB))) {
                        return problem.getAt("stepName")
                }
            }
            println "================================================================================================================================================="
        }
        return "";
    }

    static void continueFailedJobByProblem() {
        String problemId = getProblemIdForFailedJob()
        try{
            tetrisRestClient.post(path: REST_BASE_URI + "problem/continue/v1/" + problemId, requestContentType: ContentType.JSON)
        }catch (HttpResponseException ex) {
            println ex.response.data
            assert !ex.response
        }
    }

    static String getProblemIdForFailedJob() {
        // Get a list of active problems
        def problems = tetrisRestClient.get(path: REST_BASE_URI + "problem/active/v1/", requestContentType: ContentType.JSON)
        if (problems.responseData != null) {
            def problem = problems.responseData as List
            problem = problem[-1]
                if ((problem.getAt("propertyId") == PROPERTY_ID.toInteger()) && (problem.getAt("jobName").equals(MARKET_SEGMENT_RECODING_JOB))) {
                    return problem.getAt("problemId")
                }
        }
        println "================================================================================================================================================="
    }

    static void insertMissingAMSRules() {
        try {
            def response = tetrisRestClient.post(path: REST_BASE_URI + "pmsMigration/addMissingPMSMappingsForTest/v1", requestContentType: 'application/json')
        } catch (HttpResponseException ex) {
            println("\n" + ex)
        }
    }

    static void assertInconsistentMappingsValidationAlertGeneration() {
        assertStepFailureAndAlertGeneration("validateRequiredMktSegRecodingConfigurationStep", "PMSMigrationInconsistentMappingsValidation")
    }

    static void assertCreateCommitForecastGroupAlertGeneration() {
        assertStepFailureAndAlertGeneration("createCommitForecastGroupAlertStep", "PMSMigrationCCFGValidation")
    }

    static void assertPostbackfillDataValidationStepFailure() {
        assertStepFailure("mktSegRecodingPostBackfillDataValidationStep")
    }

    static void assertStepFailureAndAlertGeneration(String expectedFailedStepName, String alertName) {
        assertStepFailure(expectedFailedStepName)
        def connection = DbConnection.getTenantDatabaseConnection(DATABASE_NAME)
        def rows = connection.rows("select COUNT(*) as cnt from Info_Mgr_Instance where Info_Mgr_Type_ID=(select Info_Mgr_Type_ID from info_mgr_type where Name='" + alertName + "') and Info_Mgr_Status_ID=1")
        assertTrue("expected is 1 but in actual it is " + rows.get(0).cnt + "", rows.get(0).cnt == 1)
        connection.close()
    }

    static void assertStepFailure(String expectedFailedStepName) {
        OperaUtil.checkJobStatus(MARKET_SEGMENT_RECODING_JOB)
        def connection = DbConnection.getTenantDatabaseConnection(DATABASE_NAME)
        String failedStepName = checkJobStatusAndGetFailedJob()
        assert expectedFailedStepName.equals(failedStepName): "Unexpected step '" + failedStepName + "' failed."
        connection.close()
    }

    static void forceFullyAbandonFailedJob() {
        def jobDbConnection = DbConnection.getJobDbConnection()
        Long jobInstanceID = jobDbConnection.rows(" select max(job_instance_id) jobInstanceID from job.dbo.Job_Instance where Job_Name='" + MARKET_SEGMENT_RECODING_JOB + "'")[0].jobInstanceID
        def sql = """select status from Job_Execution where job_execution_id in
            (select max(job_execution_id) from Job_Execution where Job_Instance_Id =  ${jobInstanceID})"""
        String jobStatus = (String) jobDbConnection.firstRow(sql).getAt(0)

        if ("FAILED".equals(jobStatus)) {
            abandonFailedJobByJobExecutionId(jobInstanceID)
        }
    }

    static void reset() {
        //forceFullyAbandonFailedJob()
    }

    //work in progress
    static void runDeployG3SandBoxProperty() {
        String[] command= ["powershell.exe", "Deploy-G3Sandbox -SandboxProperty CPGP02"];
        Runtime rt = Runtime.getRuntime();

        def proc = rt.exec(command)
        BufferedReader stdInput = new BufferedReader(new InputStreamReader(proc.getInputStream()));

        BufferedReader stdError = new BufferedReader(new InputStreamReader(proc.getErrorStream()));

        // read the output from the command
        System.out.println("Here is the standard output of the command:\n");
        String buffer = null;
        while ((buffer = stdInput.readLine()) != null) {
            System.out.println(buffer);
        }

        // read any errors from the attempted command
        System.out.println("Here is the standard error of the command (if any):\n");
        while ((buffer = stdError.readLine()) != null) {
            System.out.println(buffer);
        }

        if(!proc.exitValue() == 0) {
            LOGGER.error("Error while executing PowerShell Command 'Deploy-G3Sandbox -SandboxProperty '")
        }

    }

    static void assertOldMarketSegmentsDataIsDeleted(DataTable mktSegIds) {
        Set<Integer> mktSegIdList = new HashSet<Integer>()
        for (int i = 1; i < mktSegIds.cells().size(); i++) {
            int mktSegId = mktSegIds.cell(i,0).toInteger()
            mktSegIdList.add(mktSegId)
        }
        assertMarketSegmentCleanUpFor(mktSegIdList)
    }

    private static void assertMarketSegmentCleanUpFor(Set<Integer> mktSegIds) {
        if (CollectionUtils.isEmpty(mktSegIds)) {
            println("MktSegIds set to check Market Segment Cleaned Up step is empty")
            return
        }
        String commaSeparatedMktSegIds = StringUtils.join(mktSegIds, ",")

        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "GRP_EVL_FCT_SPC_ARR_DT_DAY_PART", String.format("where GRP_EVL_FCT_SPC_ARR_DT_ID in ( select GRP_EVL_FCT_SPC_ARR_DT_ID from GRP_EVL_FCT_SPC_ARR_DT where GRP_EVL_ARR_DT_ID in ( select GRP_EVL_ARR_DT_ID from GRP_EVL_ARR_DT where GRP_EVL_ID in ( select GRP_EVL_ID from GRP_EVL where MKT_SEG_ID in(%s))))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "GRP_EVL_ARR_DT_FG_DT_AC", String.format("where GRP_EVL_ARR_DT_FG_ID in ( select GRP_EVL_ARR_DT_FG_ID from GRP_EVL_ARR_DT_FG where GRP_EVL_ARR_DT_ID in ( select GRP_EVL_ARR_DT_ID from GRP_EVL_ARR_DT where GRP_EVL_ID in ( select GRP_EVL_ID from GRP_EVL where MKT_SEG_ID in(%s))))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "GRP_EVL_ARR_DT_FG_DT", String.format("where GRP_EVL_ARR_DT_FG_ID in ( select GRP_EVL_ARR_DT_FG_ID from GRP_EVL_ARR_DT_FG where GRP_EVL_ARR_DT_ID in ( select GRP_EVL_ARR_DT_ID from GRP_EVL_ARR_DT where GRP_EVL_ID in ( select GRP_EVL_ID from GRP_EVL where MKT_SEG_ID in(%s))))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "GRP_EVL_ARR_DT_AT", String.format("where GRP_EVL_ARR_DT_AC_ID in ( select GRP_EVL_ARR_DT_AC_ID from GRP_EVL_ARR_DT_AC where GRP_EVL_ARR_DT_ID in ( select GRP_EVL_ARR_DT_ID from GRP_EVL_ARR_DT where GRP_EVL_ID in ( select GRP_EVL_ID from GRP_EVL where MKT_SEG_ID in(%s))))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "FS_EVENT_REVENUE", String.format("where FS_EVENT_ID in ( select FS_EVENT_ID from FS_EVENT where FS_BOOKING_ID in ( select FS_BOOKING_ID from FS_BOOKING where FS_CFG_MKT_SEG_ID in ( select FS_CFG_MKT_SEG_ID from FS_CFG_MKT_SEG where MKT_SEG_ID in(%s))))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "GRP_EVL_ROOM_TYPE_DAY_OF_STAY", String.format("where GRP_EVL_ROOM_TYPE_ID in ( select GRP_EVL_ROOM_TYPE_ID from GRP_EVL_ROOM_TYPE where GRP_EVL_ID in ( select GRP_EVL_ID from GRP_EVL where MKT_SEG_ID in (%s)))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "GRP_EVL_FCT_SPC_FCT_ROOM", String.format("where GRP_EVL_FCT_SPC_ID in ( select GRP_EVL_FCT_SPC_ID from GRP_EVL_FCT_SPC where GRP_EVL_ID in ( select GRP_EVL_ID from GRP_EVL where MKT_SEG_ID in (%s)))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "GRP_EVL_FCT_SPC_ARR_DT", String.format("where GRP_EVL_ARR_DT_ID in ( select GRP_EVL_ARR_DT_ID from GRP_EVL_ARR_DT where GRP_EVL_ID in ( select GRP_EVL_ID from GRP_EVL where MKT_SEG_ID in (%s)))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "GRP_EVL_ARR_DT_FG", String.format("where GRP_EVL_ARR_DT_ID in ( select GRP_EVL_ARR_DT_ID from GRP_EVL_ARR_DT where GRP_EVL_ID in ( select GRP_EVL_ID from GRP_EVL where MKT_SEG_ID in (%s)))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "GRP_EVL_ARR_DT_AC", String.format("where GRP_EVL_ARR_DT_ID in ( select GRP_EVL_ARR_DT_ID from GRP_EVL_ARR_DT where GRP_EVL_ID in ( select GRP_EVL_ID from GRP_EVL where MKT_SEG_ID in (%s)))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "FS_EVENT", String.format("where FS_BOOKING_ID in ( select FS_BOOKING_ID from FS_BOOKING where FS_CFG_MKT_SEG_ID in ( select FS_CFG_MKT_SEG_ID from FS_CFG_MKT_SEG where MKT_SEG_ID in (%s)))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "FS_BOOKING_REVENUE", String.format("where FS_BOOKING_ID in ( select FS_BOOKING_ID from FS_BOOKING where FS_CFG_MKT_SEG_ID in ( select FS_CFG_MKT_SEG_ID from FS_CFG_MKT_SEG where MKT_SEG_ID in (%s)))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "FS_BOOKING_PACE", String.format("where FS_BOOKING_ID in ( select FS_BOOKING_ID from FS_BOOKING where FS_CFG_MKT_SEG_ID in ( select FS_CFG_MKT_SEG_ID from FS_CFG_MKT_SEG where MKT_SEG_ID in (%s)))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "FS_BOOKING_GUEST_ROOM", String.format("where FS_BOOKING_ID in ( select FS_BOOKING_ID from FS_BOOKING where FS_CFG_MKT_SEG_ID in ( select FS_CFG_MKT_SEG_ID from FS_CFG_MKT_SEG where MKT_SEG_ID in (%s)))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "GRP_EVL_ROOM_TYPE", String.format("where GRP_EVL_ID in ( select GRP_EVL_ID from GRP_EVL where MKT_SEG_ID in (%s))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "GRP_EVL_FCT_SPC_CONF_BANQ", String.format("where GRP_EVL_ID in ( select GRP_EVL_ID from GRP_EVL where MKT_SEG_ID in (%s))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "GRP_EVL_FCT_SPC", String.format("where GRP_EVL_ID in ( select GRP_EVL_ID from GRP_EVL where MKT_SEG_ID in (%s))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "GRP_EVL_DAY_OF_STAY", String.format("where GRP_EVL_ID in ( select GRP_EVL_ID from GRP_EVL where MKT_SEG_ID in (%s))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "GRP_EVL_COST", String.format("where GRP_EVL_ID in ( select GRP_EVL_ID from GRP_EVL where MKT_SEG_ID in (%s))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "GRP_EVL_CONF_BANQ", String.format("where GRP_EVL_ID in ( select GRP_EVL_ID from GRP_EVL where MKT_SEG_ID in (%s))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "GRP_EVL_ARR_DT", String.format("where GRP_EVL_ID in ( select GRP_EVL_ID from GRP_EVL where MKT_SEG_ID in (%s))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "GRP_EVL_ANC", String.format("where GRP_EVL_ID in ( select GRP_EVL_ID from GRP_EVL where MKT_SEG_ID in (%s))", commaSeparatedMktSegIds))
        if (tableExist("WASH_IND_GROUP_FCST_OVR_GRP")) {
            assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "WASH_IND_GROUP_FCST_OVR_GRP", String.format("where GROUP_ID in ( select GROUP_ID from GROUP_MASTER where MKT_SEG_ID in (%s))", commaSeparatedMktSegIds))
        }
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "WASH_IND_GROUP_FCST", String.format("where GROUP_ID in ( select GROUP_ID from GROUP_MASTER where MKT_SEG_ID in (%s))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "PACE_GROUP_MASTER", String.format("where GROUP_ID in ( select GROUP_ID from GROUP_MASTER where MKT_SEG_ID in (%s))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "PACE_GROUP_BLOCK", String.format("where GROUP_ID in ( select GROUP_ID from GROUP_MASTER where MKT_SEG_ID in (%s))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "GROUP_FLOOR_OVR_ALERT_DETAILS", String.format("where GROUP_ID in ( select GROUP_ID from GROUP_MASTER where MKT_SEG_ID in (%s))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "GROUP_FLOOR_OVR", String.format("where GROUP_ID in ( select GROUP_ID from GROUP_MASTER where MKT_SEG_ID in (%s))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "GROUP_FLOOR_MFN_CFG", String.format("where GROUP_ID in ( select GROUP_ID from GROUP_MASTER where MKT_SEG_ID in (%s))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "GROUP_BLOCK", String.format("where GROUP_ID in ( select GROUP_ID from GROUP_MASTER where MKT_SEG_ID in (%s))", commaSeparatedMktSegIds))
        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "FS_BOOKING", String.format("where FS_CFG_MKT_SEG_ID in ( select FS_CFG_MKT_SEG_ID from FS_CFG_MKT_SEG where MKT_SEG_ID in (%s))", commaSeparatedMktSegIds))

        if (tableExist("WASH_IND_GROUP_FCST_OVR_MKT")) {
            assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "WASH_IND_GROUP_FCST_OVR_MKT")
        }
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "RESERVATION_NIGHT")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "RATE_UNQUALIFIED_MKT_SEG")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "RATE_QUALIFIED_MKT_SEG")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "PACE_MKT_OCCUPANCY_FCST_NOTIFICATION")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "PACE_MKT_OCCUPANCY_FCST")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "PACE_MKT_ACTIVITY")
        if (tableExist("PACE_GROUP_MASTER_MKT_SEG")) {
            assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "PACE_GROUP_MASTER_MKT_SEG")
        }
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "OCCUPANCY_FCST")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "MKT_SEG_FORECAST_GROUP")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "MKT_SEG_DETAILS_PROPOSED")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "MKT_SEG_DETAILS")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "MKT_SEG_BUSINESS_GROUP")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "MKT_ACCOM_ACTIVITY")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "IP_CFG_FCST_GROUP_OVRD_DETAIL")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "GRP_EVL")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "GROUP_MASTER")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "GROUP_FLOOR_MKT_SEG_CFG")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "FS_CFG_MKT_SEG")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "FORECAST_GROUP_MKT_SEG_PROPOSED")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "ES_MKT_SEG_PRODUCT_MAPPING")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "CR_MKT_ACCOM_ACTIVITY")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "BENEFIT_MKT_ACCOM_RESULTS")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "AMS_COMPOSITION_CHANGE")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "ANALYTIC_MKT_ACCOM_LOS_INV")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "ANALYTICS_TRANS")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "BUSINESS_ACCOM_FCST")

        assertMktSegCleanUpForIndirectTableReference(commaSeparatedMktSegIds, "CHANNEL_COST_CFG", String.format("where CHANNEL_ID in ( select CHANNEL_ID from CHANNEL where MKT_SEG_ID in (%s))", commaSeparatedMktSegIds))

        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "CHANNEL")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "CURRENT_MKT_ACCOM_ACTIVITY")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "GRP_PRC_CFG_ANC_ASSGN")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "LAST_OPTIMIZATION_MKT_ACCOM_ACTIVITY")
        assertMarketSegmentCleanUpFor(commaSeparatedMktSegIds, "OCC_FCST_ORG")
    }

    static boolean tableExist(String tableName) {
        String query = "IF EXISTS (SELECT * FROM sys.objects " +
                "  WHERE object_id = Object_id('[" + tableName + "]') AND type IN ('U')) " +
                "  select 1 as exist ELSE select 0 as exist"
        def connection = DbConnection.getTenantDatabaseConnection(DATABASE_NAME)
        def exist = connection.rows(query)[0].exist
        connection.close()

        return exist == 1
    }

    private static void assertMktSegCleanUpForIndirectTableReference(String commaSeparatedMktSegIds, String tableName, String whereClause) {
        String query = String.format("select count(*) as cnt from %s %s", tableName, whereClause, commaSeparatedMktSegIds)
        assertMktSegCleanUpFor(query, tableName, commaSeparatedMktSegIds)
    }

    private static void assertMarketSegmentCleanUpFor(String commaSeparatedMktSegIds, String tableName) {
        String query = String.format("select count(*) as cnt from %s where Mkt_Seg_Id in(%s)", tableName, commaSeparatedMktSegIds)
        assertMktSegCleanUpFor(query, tableName, commaSeparatedMktSegIds)
    }

    private static void assertMktSegCleanUpFor(String query, String tableName, String commaSeparatedMktSegIds) {
        println(query)
        def count = getTenantTableRecordsCountByQuery(query)
        assert 0 == count: "Records found in " + tableName + " table for mkt seg ids [ " + commaSeparatedMktSegIds + "] which is not expected."
    }

    static void assertNumberOfRowsInGroupMaster(DataTable mktSegIdsWithRowCount) {
        assertNumberOfRecordsFor(mktSegIdsWithRowCount, "group_master")
    }

    static void assertNumberOfRowsInPaceGroupMaster(DataTable mktSegIdsWithRowCount) {
        assertNumberOfRecordsFor(mktSegIdsWithRowCount, "pace_group_master")
    }

    static void assertNumberOfRowsInMktSegDetailsProposed(DataTable mktSegIdsWithRowCount) {
        assertNumberOfRecordsFor(mktSegIdsWithRowCount, "Mkt_Seg_Details_Proposed")
    }

    private static void assertNumberOfRecordsFor(DataTable mktSegIdsWithRowCount, String tableName) {
        for (int i = 1; i < mktSegIdsWithRowCount.cells().size(); i++) {
            int mktSegId = mktSegIdsWithRowCount.cell(i, 0).toInteger()
            int numberOfRecords = mktSegIdsWithRowCount.cell(i, 1).toInteger()

            def resultCount = getTenantTableRecordsCountByQuery(
                    "SELECT COUNT(*) AS cnt FROM " + tableName + " WHERE mkt_seg_id = " + mktSegId)
            assert numberOfRecords == resultCount: "Expected number of records for Mkt Seg Id: " + mktSegId +
                    " in " + tableName + " is " + numberOfRecords
        }
    }

    static void assertMktSegIdsPassedForPaceBackFillInRecoding(DataTable mktSegId) {
            def actualPaceBackFillMktSegIdsList = []
            def expectedPaceBackFillMktSegIdsList = []
            actualPaceBackFillMktSegIdsList = MarketSegmentRecodingTestUtil.getPaceBackFillMktSegIdsUsingJobExecutionContext()
            //actualPaceBackFillMktSegIdsList = actualPaceBackFillMktSegIdsList.sort()
            for (int i = 1; i < mktSegId.cells().size(); i++) {
                expectedPaceBackFillMktSegIdsList.add(mktSegId.cell(i, 0))
            }
            expectedPaceBackFillMktSegIdsList = expectedPaceBackFillMktSegIdsList.sort()
            assert actualPaceBackFillMktSegIdsList == expectedPaceBackFillMktSegIdsList: "Expected Mkt Seg Id to be Passed for PaceBackFill " + expectedPaceBackFillMktSegIdsList + "Actual Mkt Seg ID Passed for PaceBackFill " + actualPaceBackFillMktSegIdsList

            //assert sas datasets population
            assertPopulationOfSaSDatasetsWithNewMktSegIds(expectedPaceBackFillMktSegIdsList as ArrayList)

    }

    static void assertPopulationOfSaSDatasetsWithNewMktSegIds(ArrayList mktSegIds) {

        // mkt_accom_activity table is synced with sas ma_mkt_seg_id datasets which is present in sas partitions folder.
        def connection = DbConnection.getTenantDatabaseConnection(DATABASE_NAME)
        def sasDatasetConnection = DbConnection.getSasDatasetPartitionConnection("10914")
        def statement = sasDatasetConnection.createStatement();
        def sasResult

        StringBuilder sqlQuery = new StringBuilder("select mkt_seg_id,accom_type_id, sum(rooms_sold) as sum_rs,sum(arrivals) as sum_arr,sum(departures) as sum_de," +
                "sum(room_revenue) as sum_rr," +
                "sum(food_revenue) as sum_fr,sum(total_revenue)  as sum_tr " +
                "from mkt_accom_activity where mkt_seg_id in(")

        for(String mktSegId : mktSegIds) {
            sqlQuery.append(mktSegId).append(", ")
        }
        sqlQuery.setLength(sqlQuery.length()-2) //remove trailing comma and whitespace
        sqlQuery.append(") ")
        sqlQuery.append("group by mkt_seg_id,accom_type_id order by mkt_seg_id,accom_type_id ")

        connection.eachRow(sqlQuery.toString()) {
            int mktSegId = it.mkt_seg_id as Integer
            int accomTypeId = it.accom_type_id as Integer
            int sum_rs = it.sum_rs as Integer
            int sum_arr = it.sum_arr as Integer
            int sum_de = it.sum_de as Integer
            Double sum_rr = it.sum_rr as Double
            Double sum_fr = it.sum_fr as Double
            Double sum_tr = it.sum_tr as Double

            sasResult = statement.executeQuery("select Accom_Type_ID, sum(ROOMS_SOLD) as sum_rs, sum(ARRIVALS) as sum_arr, sum(DEPARTURES) as sum_de," +
                    " sum(ROOM_REVENUE) as sum_rr, sum(food_revenue) as sum_fr, sum(TOTAL_REVENUE) as sum_tr from parLib.ma_" + mktSegId + " where Accom_Type_ID = " + accomTypeId + " group by Accom_Type_ID")

            while (sasResult.next()) {
                assert sum_rs == sasResult.getInt("sum_rs"): "sas ma dataset values not matching with mkt_accom_activity for mkt_id $mktSegId and accom_id $accomTypeId"
                assert sum_arr == sasResult.getInt("sum_arr"): "sas ma dataset values not matching with mkt_accom_activity for mkt_id $mktSegId and accom_id $accomTypeId"
                assert sum_de == sasResult.getInt("sum_de"): "sas ma dataset values not matching with mkt_accom_activity for mkt_id $mktSegId and accom_id $accomTypeId"
                assert sum_rr.round(0) == sasResult.getDouble("sum_rr").round(0): "sas ma dataset values not matching with mkt_accom_activity for mkt_id $mktSegId and accom_id $accomTypeId"
                assert sum_fr.round(0) == sasResult.getDouble("sum_fr").round(0): "sas ma dataset values not matching with mkt_accom_activity for mkt_id $mktSegId and accom_id $accomTypeId"
                assert sum_tr.round(0) == sasResult.getDouble("sum_tr").round(0): "sas ma dataset values not matching with mkt_accom_activity for mkt_id $mktSegId and accom_id $accomTypeId"
            }
        }

        connection.close()
        sasDatasetConnection.close()
    }

    static void triggerCCFGJob() {
        println "!!! JEMS create/commit forecast groups"
        ForecastGroupUtil.setupEnvironment(tetrisRestClient, PROPERTY_CODE, "America/Phoenix", PROPERTY_ID)
        ForecastGroupUtil.triggerJob(tetrisRestClient, "createforecastgroups/create/v1")
        waitForJobToComplete()
        ForecastGroupUtil.triggerJob(tetrisRestClient, "proposedforecastgroups/commit/v1")
        waitForJobToComplete()
    }

    static void waitForJobToComplete() {
        def connection = DbConnection.getJobDbConnection()/*   getTenantDatabaseConnection("Job")*/
        def jobID = connection.firstRow("select JOB_INSTANCE_ID from JOB_INSTANCE order by JOB_INSTANCE_ID desc").JOB_INSTANCE_ID
        JemsUtil.waitForRunningJob(jobID)
        def status = connection.firstRow("select EXECUTION_STATUS from JOB_STATE where JOB_INSTANCE_ID='" + jobID + "'").EXECUTION_STATUS
        connection.close()
    }

    static void createPaceGroupMasterForGivenMktSegs(DataTable Mkt_Segs) {
        def connection = DbConnection.getTenantDatabaseConnection(DATABASE_NAME)
        for (int i = 1; i < Mkt_Segs.cells().size(); i++) {
            int mktSegId = Mkt_Segs.cell(i, 0).toInteger()
            String query = "insert into Pace_Group_Master select gm.Group_ID, getDate(), gm.Group_Code, gm.Group_Name, gm.Group_Description,\n" +
                    "gm.Master_Group_ID, gm.Master_Group_Code, gm.Group_Status_Code, gm.Group_Type_Code, gm.Mkt_Seg_ID, gm.Start_DT,\n" +
                    "gm.End_DT, gm.Booking_DT, gm.Pickup_Type_Code, gm.Cancel_DT, gm.Booking_type, gm.Sales_Person, gm.Cut_Off_date,\n" +
                    "gm.Cut_Off_days, 7088  from Group_Master as gm where Mkt_Seg_ID = " + mktSegId
            println(query)
            connection.call(query)
        }
        connection.close()
    }

    static void createGroupMasterForGivenMktSegsWithDetails(DataTable groupMasterDetails) {
        def connection = DbConnection.getTenantDatabaseConnection(DATABASE_NAME)
        for (int i = 1; i < groupMasterDetails.cells().size(); i++) {
            int mktSegId = groupMasterDetails.cell(i, 0).toInteger()
            int propertyId = groupMasterDetails.cell(i, 1).toInteger()
            String groupCode = groupMasterDetails.cell(i, 2).toString()
            String query = "INSERT INTO Group_Master " +
                    "(Property_ID,Group_Code,Group_Name,Group_Description,Master_Group_ID,Master_Group_Code,Group_Status_Code,	Group_Type_Code,Mkt_Seg_ID,Start_DT,End_DT,Booking_DT,Pickup_Type_Code,Cancel_DT,Booking_type,Sales_Person,Cut_Off_date,Cut_Off_days) " +
                    "VALUES(" + propertyId + ", '" + groupCode + "', '" + groupCode + "', '" + groupCode + "', NULL, NULL, 'DEFINITE', 'TRANS', " + mktSegId + ", getdate(),getdate(),getdate(),'INDV',NULL,NULL,'Testcase',getdate(),0)";
            println(query)
            connection.call(query)
        }
        connection.close()
    }

    static void createMarketSegments(DataTable mktSegDetails) {
        def connection = DbConnection.getTenantDatabaseConnection(DATABASE_NAME)
        for (int i = 1; i < mktSegDetails.cells().size(); i++) {
            int propertyId = mktSegDetails.cell(i, 0).toInteger()
            String mktSegCode = mktSegDetails.cell(i, 1).toString()
            int statusId = mktSegDetails.cell(i, 2).toInteger()
            int complimentary = mktSegDetails.cell(i, 3).toInteger()
            String query = "INSERT INTO Mkt_Seg " +
                    "(Property_ID, Mkt_Seg_Code, Mkt_Seg_Name, Mkt_Seg_Description, Status_ID, Last_Updated_DTTM, Is_Editable, Created_By_User_ID, Created_DTTM,	Last_Updated_By_User_ID, complimentary) " +
                    "VALUES(" + propertyId + ",'" + mktSegCode + "','" + mktSegCode + "','" + mktSegCode + "'," + statusId + ",getdate(),0,1,getdate(),1," + complimentary + ")";
            println(query)
            connection.call(query)
        }
        connection.close()
    }

    static void updateReservationsDataFor(DataTable reservationUpdateTable) {
        def connection = DbConnection.getTenantDatabaseConnection(DATABASE_NAME)
        for (int i = 1; i < reservationUpdateTable.cells().size(); i++) {
            int currentMktSegId = reservationUpdateTable.cell(i, 0).toInteger()
            String rateCode = reservationUpdateTable.cell(i, 1).toString()
            int newMktSegId = reservationUpdateTable.cell(i, 2).toInteger()
            String reservationNightQuery = "UPDATE Reservation_Night SET Mkt_Seg_ID = " + newMktSegId + " WHERE Mkt_Seg_ID = " + currentMktSegId + " AND Rate_Code = '" + rateCode + "'";
            println(reservationNightQuery)
            connection.call(reservationNightQuery)

            String reservationNightChangeQuery = "UPDATE Reservation_Night_Change SET Mkt_Seg_ID = " + newMktSegId + " WHERE Mkt_Seg_ID = " + currentMktSegId + " AND Rate_Code = '" + rateCode+"'";
            println(reservationNightChangeQuery)
            connection.call(reservationNightChangeQuery)
        }
        connection.close()
    }

    static void updateReservationsForMarketCodeRateCode(DataTable reservationUpdateTable) {
        def connection = DbConnection.getTenantDatabaseConnection(DATABASE_NAME)
        for (int i = 1; i < reservationUpdateTable.cells().size(); i++) {
            int mktSegId = reservationUpdateTable.cell(i, 0).toInteger()
            String marketCode = reservationUpdateTable.cell(i, 1).toString()
            String rateCode = reservationUpdateTable.cell(i, 2).toString()
            String reservationNightQuery = "UPDATE Reservation_Night SET Mkt_Seg_ID = " + mktSegId + " WHERE Market_Code = '" + marketCode + "' AND Rate_Code = '" + rateCode + "'";
            println(reservationNightQuery)
            connection.call(reservationNightQuery)

            String reservationNightChangeQuery = "UPDATE Reservation_Night_Change SET Mkt_Seg_ID = " + mktSegId + " WHERE Market_Code = '" + marketCode + "' AND Rate_Code = '" + rateCode+"'";
            println(reservationNightChangeQuery)
            connection.call(reservationNightChangeQuery)
        }
        connection.close()
    }

    static String getJobExecutionContext() {
        def sql = "Select top 1 JEC.SERIALIZED_CONTEXT from JOB_EXECUTION_CONTEXT JEC"+
                  " inner join JOB_EXECUTION JE "+
                  " on JE.JOB_EXECUTION_ID=JEC.JOB_EXECUTION_ID"+
                  " inner join JOB_INSTANCE JI"+
                  " on JI.JOB_INSTANCE_ID = JE.JOB_INSTANCE_ID"+
                  " where JE.JOB_INSTANCE_ID=("+
                  " select max(job_instance_id) jobInstanceID from job.dbo.Job_Instance where Job_Name='" + MARKET_SEGMENT_RECODING_JOB +"')"+
                  " and SERIALIZED_CONTEXT like '%mktSegIds%'"+
                  " order by JEC.JOB_EXECUTION_ID desc"
        return (String) com.ideas.tetris.util.db.DbConnection.getJobDbConnection().firstRow(sql).getAt(0)
    }


}
