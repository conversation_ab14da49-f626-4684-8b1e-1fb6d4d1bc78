<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2" properties="5.0" jmeter="5.0 r1840935">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="GeminiRPPerformancePlan" enabled="true">
      <stringProp name="TestPlan.comments"></stringProp>
      <boolProp name="TestPlan.functional_mode">false</boolProp>
      <boolProp name="TestPlan.serialize_threadgroups">false</boolProp>
      <elementProp name="TestPlan.user_defined_variables" elementType="Arguments" guiclass="ArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
        <collectionProp name="Arguments.arguments">
          <elementProp name="jmeterThreads" elementType="Argument">
            <stringProp name="Argument.name">jmeterThreads</stringProp>
            <stringProp name="Argument.value">${__P(jmeterThreads)}</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
          <elementProp name="jmeterRampUpTime" elementType="Argument">
            <stringProp name="Argument.name">jmeterRampUpTime</stringProp>
            <stringProp name="Argument.value">${__P(jmeterRampUpTime)}</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
          <elementProp name="jmeterLoopCount" elementType="Argument">
            <stringProp name="Argument.name">jmeterLoopCount</stringProp>
            <stringProp name="Argument.value">${__P(jmeterLoopCount)}</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
        </collectionProp>
      </elementProp>
      <stringProp name="TestPlan.user_define_classpath"></stringProp>
    </TestPlan>
    <hashTree>
      <Arguments guiclass="ArgumentsPanel" testclass="Arguments" testname="EnableLocal" enabled="true">
        <collectionProp name="Arguments.arguments">
          <elementProp name="jmeterThreads" elementType="Argument">
            <stringProp name="Argument.name">jmeterThreads</stringProp>
            <stringProp name="Argument.value">1</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
          <elementProp name="jmeterRampUpTime" elementType="Argument">
            <stringProp name="Argument.name">jmeterRampUpTime</stringProp>
            <stringProp name="Argument.value">1</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
          <elementProp name="jmeterLoopCount" elementType="Argument">
            <stringProp name="Argument.name">jmeterLoopCount</stringProp>
            <stringProp name="Argument.value">1</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
        </collectionProp>
        <stringProp name="TestPlan.comments">Enable enablelocal when working on localhost</stringProp>
      </Arguments>
      <hashTree/>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="GeminiPerformanceThreads" enabled="true">
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControlPanel" testclass="LoopController" testname="Loop Controller" enabled="true">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <stringProp name="LoopController.loops">${jmeterLoopCount}</stringProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">${jmeterThreads}</stringProp>
        <stringProp name="ThreadGroup.ramp_time">${jmeterRampUpTime}</stringProp>
        <boolProp name="ThreadGroup.scheduler">false</boolProp>
        <stringProp name="ThreadGroup.duration"></stringProp>
        <stringProp name="ThreadGroup.delay"></stringProp>
      </ThreadGroup>
      <hashTree>
        <Arguments guiclass="ArgumentsPanel" testclass="Arguments" testname="G3PassedProperties" enabled="false">
          <collectionProp name="Arguments.arguments">
            <elementProp name="g3UrlProtocol" elementType="Argument">
              <stringProp name="Argument.name">g3UrlProtocol</stringProp>
              <stringProp name="Argument.value">${__P(g3UrlProtocol)</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="g3UrlDomainName" elementType="Argument">
              <stringProp name="Argument.name">g3UrlDomainName</stringProp>
              <stringProp name="Argument.value">${__P(g3UrlDomainName)}</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="g3BinaryDir" elementType="Argument">
              <stringProp name="Argument.name">g3BinaryDir</stringProp>
              <stringProp name="Argument.value">${__P(g3BinaryDir)}</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="g3PropertyList" elementType="Argument">
              <stringProp name="Argument.name">g3PropertyList</stringProp>
              <stringProp name="Argument.value">${__P(g3PropertyList)}</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="jmeterRunScriptFor" elementType="Argument">
              <stringProp name="Argument.name">jmeterRunScriptFor</stringProp>
              <stringProp name="Argument.value">${__P(jmeterRunScriptFor)}</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
              <stringProp name="Argument.desc">RPtHomePage,SelectRCRange,SelectDateRange,SelectMultiPropertyGroups</stringProp>
            </elementProp>
            <elementProp name="g3Port" elementType="Argument">
              <stringProp name="Argument.name">g3Port</stringProp>
              <stringProp name="Argument.value">${__P(g3Port)}</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
          </collectionProp>
        </Arguments>
        <hashTree/>
        <Arguments guiclass="ArgumentsPanel" testclass="Arguments" testname="EnableLocal" enabled="true">
          <collectionProp name="Arguments.arguments">
            <elementProp name="g3UrlProtocol" elementType="Argument">
              <stringProp name="Argument.name">g3UrlProtocol</stringProp>
              <stringProp name="Argument.value">http</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="g3UrlDomainName" elementType="Argument">
              <stringProp name="Argument.name">g3UrlDomainName</stringProp>
              <stringProp name="Argument.value">localhost</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="g3Port" elementType="Argument">
              <stringProp name="Argument.name">g3Port</stringProp>
              <stringProp name="Argument.value">8080</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="g3BinaryDir" elementType="Argument">
              <stringProp name="Argument.name">g3BinaryDir</stringProp>
              <stringProp name="Argument.value">C:\Tetris\app-test\jmeter\src\test\binaries</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="g3PropertyList" elementType="Argument">
              <stringProp name="Argument.name">g3PropertyList</stringProp>
              <stringProp name="Argument.value">C:\Tetris\app-test\jmeter\src\test\properties\GeminiStressboxUI.csv</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="jmeterRunScriptFor" elementType="Argument">
              <stringProp name="Argument.name">jmeterRunScriptFor</stringProp>
              <stringProp name="Argument.value">RPtHomePage</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
              <stringProp name="Argument.desc">RatePlanReportPage</stringProp>
            </elementProp>
          </collectionProp>
        </Arguments>
        <hashTree/>
        <Arguments guiclass="ArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="false">
          <collectionProp name="Arguments.arguments">
            <elementProp name="GeminiPort" elementType="Argument">
              <stringProp name="Argument.name">GeminiPort</stringProp>
              <stringProp name="Argument.value">8080</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="GeminiUrlDomainName" elementType="Argument">
              <stringProp name="Argument.name">GeminiUrlDomainName</stringProp>
              <stringProp name="Argument.value">localhost</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="GeminiUrlProtocol" elementType="Argument">
              <stringProp name="Argument.name">GeminiUrlProtocol</stringProp>
              <stringProp name="Argument.value">http</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="jmeterRunScriptFor" elementType="Argument">
              <stringProp name="Argument.name">jmeterRunScriptFor</stringProp>
              <stringProp name="Argument.value">RatePlanReportPages</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
              <stringProp name="Argument.desc">RatePlanReportPage</stringProp>
            </elementProp>
            <elementProp name="GeminiPropertyId" elementType="Argument">
              <stringProp name="Argument.name">GeminiPropertyId</stringProp>
              <stringProp name="Argument.value">11032</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="GeminiClientID" elementType="Argument">
              <stringProp name="Argument.name">GeminiClientID</stringProp>
              <stringProp name="Argument.value">6</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="GeminiClientCode" elementType="Argument">
              <stringProp name="Argument.name">GeminiClientCode</stringProp>
              <stringProp name="Argument.value">SandBox</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="GeminiPropertyCode" elementType="Argument">
              <stringProp name="Argument.name">GeminiPropertyCode</stringProp>
              <stringProp name="Argument.value">CPGP01</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="StartRCRange" elementType="Argument">
              <stringProp name="Argument.name">StartRCRange</stringProp>
              <stringProp name="Argument.value">1</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="EndRCRange" elementType="Argument">
              <stringProp name="Argument.name">EndRCRange</stringProp>
              <stringProp name="Argument.value">40</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="StartDate" elementType="Argument">
              <stringProp name="Argument.name">StartDate</stringProp>
              <stringProp name="Argument.value">21</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="StartMonth" elementType="Argument">
              <stringProp name="Argument.name">StartMonth</stringProp>
              <stringProp name="Argument.value">6</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="StartYear" elementType="Argument">
              <stringProp name="Argument.name">StartYear</stringProp>
              <stringProp name="Argument.value">2016</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="EndDate" elementType="Argument">
              <stringProp name="Argument.name">EndDate</stringProp>
              <stringProp name="Argument.value">21</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="EndMonth" elementType="Argument">
              <stringProp name="Argument.name">EndMonth</stringProp>
              <stringProp name="Argument.value">7</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="EndYear" elementType="Argument">
              <stringProp name="Argument.name">EndYear</stringProp>
              <stringProp name="Argument.value">2017</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="gropuName" elementType="Argument">
              <stringProp name="Argument.name">gropuName</stringProp>
              <stringProp name="Argument.value">MultiProperty Group</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
          </collectionProp>
        </Arguments>
        <hashTree/>
        <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="false">
          <collectionProp name="HeaderManager.headers"/>
        </HeaderManager>
        <hashTree/>
        <CSVDataSet guiclass="TestBeanGUI" testclass="CSVDataSet" testname="GeminiPropertiesFile" enabled="true">
          <stringProp name="TestPlan.comments">${__P(jmeterSourceDir)}\binaries\${__P(jmeterUserList)}</stringProp>
          <stringProp name="delimiter">,</stringProp>
          <stringProp name="fileEncoding"></stringProp>
          <stringProp name="filename">${g3PropertyList}</stringProp>
          <boolProp name="ignoreFirstLine">true</boolProp>
          <boolProp name="quotedData">true</boolProp>
          <boolProp name="recycle">true</boolProp>
          <stringProp name="shareMode">shareMode.all</stringProp>
          <boolProp name="stopThread">true</boolProp>
          <stringProp name="variableNames">Execute,GeminiPropertyId,g3UserName,g3UserPassword,userID,Group,propertyGroupID,GeminiClientCode,GeminiClientID,GeminiPropertyCode,StartMonth,StartDate,StartYear,EndMonth,EndDate,EndYear,StartRCRange,EndRCRange,gropuName</stringProp>
        </CSVDataSet>
        <hashTree/>
        <IfController guiclass="IfControllerPanel" testclass="IfController" testname="GeminiPropertyExecute" enabled="true">
          <stringProp name="IfController.condition">&apos;${Execute}&apos; == &apos;Yes&apos;</stringProp>
          <boolProp name="IfController.evaluateAll">false</boolProp>
        </IfController>
        <hashTree>
          <CounterConfig guiclass="CounterConfigGui" testclass="CounterConfig" testname="Counter" enabled="true">
            <stringProp name="CounterConfig.start">0</stringProp>
            <stringProp name="CounterConfig.end"></stringProp>
            <stringProp name="CounterConfig.incr">1</stringProp>
            <stringProp name="CounterConfig.name">threadCounter</stringProp>
            <stringProp name="CounterConfig.format"></stringProp>
            <boolProp name="CounterConfig.per_user">false</boolProp>
          </CounterConfig>
          <hashTree/>
          <BeanShellPreProcessor guiclass="TestBeanGUI" testclass="BeanShellPreProcessor" testname="DomainSwitcher" enabled="true">
            <boolProp name="resetInterpreter">false</boolProp>
            <stringProp name="parameters"></stringProp>
            <stringProp name="filename"></stringProp>
            <stringProp name="script">String[] urls = vars.get(&quot;g3UrlDomainName&quot;).split(&quot;,&quot;);
String[] ports = vars.get(&quot;g3Port&quot;).toString().split(&quot;,&quot;);
String[] protocols = vars.get(&quot;g3UrlProtocol&quot;).toString().split(&quot;,&quot;);
int counter = Integer.parseInt(vars.get(&quot;threadCounter&quot;).toString());
</stringProp>
          </BeanShellPreProcessor>
          <hashTree/>
          <ResultCollector guiclass="ViewResultsFullVisualizer" testclass="ResultCollector" testname="View Results Tree" enabled="true">
            <boolProp name="ResultCollector.error_logging">false</boolProp>
            <objProp>
              <name>saveConfig</name>
              <value class="SampleSaveConfiguration">
                <time>true</time>
                <latency>true</latency>
                <timestamp>true</timestamp>
                <success>true</success>
                <label>true</label>
                <code>true</code>
                <message>true</message>
                <threadName>true</threadName>
                <dataType>true</dataType>
                <encoding>false</encoding>
                <assertions>true</assertions>
                <subresults>true</subresults>
                <responseData>false</responseData>
                <samplerData>false</samplerData>
                <xml>false</xml>
                <fieldNames>true</fieldNames>
                <responseHeaders>false</responseHeaders>
                <requestHeaders>false</requestHeaders>
                <responseDataOnError>false</responseDataOnError>
                <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
                <assertionsResultsToSave>0</assertionsResultsToSave>
                <bytes>true</bytes>
                <sentBytes>true</sentBytes>
                <url>true</url>
                <threadCounts>true</threadCounts>
                <idleTime>true</idleTime>
                <connectTime>true</connectTime>
              </value>
            </objProp>
            <stringProp name="filename"></stringProp>
          </ResultCollector>
          <hashTree/>
          <ResultCollector guiclass="SummaryReport" testclass="ResultCollector" testname="Summary Report" enabled="true">
            <boolProp name="ResultCollector.error_logging">false</boolProp>
            <objProp>
              <name>saveConfig</name>
              <value class="SampleSaveConfiguration">
                <time>true</time>
                <latency>true</latency>
                <timestamp>true</timestamp>
                <success>true</success>
                <label>true</label>
                <code>true</code>
                <message>true</message>
                <threadName>true</threadName>
                <dataType>true</dataType>
                <encoding>false</encoding>
                <assertions>true</assertions>
                <subresults>true</subresults>
                <responseData>false</responseData>
                <samplerData>false</samplerData>
                <xml>false</xml>
                <fieldNames>true</fieldNames>
                <responseHeaders>false</responseHeaders>
                <requestHeaders>false</requestHeaders>
                <responseDataOnError>false</responseDataOnError>
                <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
                <assertionsResultsToSave>0</assertionsResultsToSave>
                <bytes>true</bytes>
                <sentBytes>true</sentBytes>
                <url>true</url>
                <threadCounts>true</threadCounts>
                <idleTime>true</idleTime>
                <connectTime>true</connectTime>
              </value>
            </objProp>
            <stringProp name="filename"></stringProp>
          </ResultCollector>
          <hashTree/>
          <ResultCollector guiclass="StatVisualizer" testclass="ResultCollector" testname="Aggregate Report" enabled="true">
            <boolProp name="ResultCollector.error_logging">false</boolProp>
            <objProp>
              <name>saveConfig</name>
              <value class="SampleSaveConfiguration">
                <time>true</time>
                <latency>true</latency>
                <timestamp>true</timestamp>
                <success>true</success>
                <label>true</label>
                <code>true</code>
                <message>true</message>
                <threadName>true</threadName>
                <dataType>true</dataType>
                <encoding>false</encoding>
                <assertions>true</assertions>
                <subresults>true</subresults>
                <responseData>false</responseData>
                <samplerData>false</samplerData>
                <xml>false</xml>
                <fieldNames>true</fieldNames>
                <responseHeaders>false</responseHeaders>
                <requestHeaders>false</requestHeaders>
                <responseDataOnError>false</responseDataOnError>
                <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
                <assertionsResultsToSave>0</assertionsResultsToSave>
                <bytes>true</bytes>
                <sentBytes>true</sentBytes>
                <url>true</url>
                <threadCounts>true</threadCounts>
                <idleTime>true</idleTime>
                <connectTime>true</connectTime>
              </value>
            </objProp>
            <stringProp name="filename"></stringProp>
          </ResultCollector>
          <hashTree/>
          <ResultCollector guiclass="AssertionVisualizer" testclass="ResultCollector" testname="Assertion Results" enabled="true">
            <boolProp name="ResultCollector.error_logging">false</boolProp>
            <objProp>
              <name>saveConfig</name>
              <value class="SampleSaveConfiguration">
                <time>true</time>
                <latency>true</latency>
                <timestamp>true</timestamp>
                <success>true</success>
                <label>true</label>
                <code>true</code>
                <message>true</message>
                <threadName>true</threadName>
                <dataType>true</dataType>
                <encoding>false</encoding>
                <assertions>true</assertions>
                <subresults>true</subresults>
                <responseData>false</responseData>
                <samplerData>false</samplerData>
                <xml>false</xml>
                <fieldNames>true</fieldNames>
                <responseHeaders>false</responseHeaders>
                <requestHeaders>false</requestHeaders>
                <responseDataOnError>false</responseDataOnError>
                <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
                <assertionsResultsToSave>0</assertionsResultsToSave>
                <bytes>true</bytes>
                <sentBytes>true</sentBytes>
                <url>true</url>
                <threadCounts>true</threadCounts>
                <idleTime>true</idleTime>
                <connectTime>true</connectTime>
              </value>
            </objProp>
            <stringProp name="filename"></stringProp>
          </ResultCollector>
          <hashTree/>
          <ConfigTestElement guiclass="HttpDefaultsGui" testclass="ConfigTestElement" testname="HTTP Request Defaults" enabled="true">
            <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
              <collectionProp name="Arguments.arguments"/>
            </elementProp>
            <stringProp name="HTTPSampler.domain">${g3UrlDomainName}</stringProp>
            <stringProp name="HTTPSampler.port">${g3Port}</stringProp>
            <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
            <stringProp name="HTTPSampler.contentEncoding"></stringProp>
            <stringProp name="HTTPSampler.path"></stringProp>
            <boolProp name="HTTPSampler.image_parser">true</boolProp>
            <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
            <stringProp name="HTTPSampler.implementation">HttpClient4</stringProp>
            <stringProp name="HTTPSampler.connect_timeout"></stringProp>
            <stringProp name="HTTPSampler.response_timeout"></stringProp>
          </ConfigTestElement>
          <hashTree/>
          <CookieManager guiclass="CookiePanel" testclass="CookieManager" testname="HTTP Cookie Manager" enabled="true">
            <collectionProp name="CookieManager.cookies"/>
            <boolProp name="CookieManager.clearEachIteration">false</boolProp>
          </CookieManager>
          <hashTree/>
          <CacheManager guiclass="CacheManagerGui" testclass="CacheManager" testname="HTTP Cache Manager" enabled="true">
            <boolProp name="clearEachIteration">false</boolProp>
            <boolProp name="useExpires">false</boolProp>
          </CacheManager>
          <hashTree/>
          <TransactionController guiclass="TransactionControllerGui" testclass="TransactionController" testname="Login" enabled="true">
            <boolProp name="TransactionController.parent">true</boolProp>
            <boolProp name="TransactionController.includeTimers">false</boolProp>
          </TransactionController>
          <hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions_Login Page" enabled="true">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments"/>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding"></stringProp>
              <stringProp name="HTTPSampler.path">/solutions</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">false</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/_Login Page" enabled="true">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments"/>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding"></stringProp>
              <stringProp name="HTTPSampler.path">/solutions/</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">false</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">true</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/api/auth/login_Login Page" enabled="true">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments"/>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding"></stringProp>
              <stringProp name="HTTPSampler.path">/solutions/api/auth/login</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">false</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">true</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/support/rest/announcement/getAllAnnouncements_Login Page" enabled="true">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments"/>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding"></stringProp>
              <stringProp name="HTTPSampler.path">/support/rest/announcement/getAllAnnouncements</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">false</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">true</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                  </elementProp>
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/api/auth/login</stringProp>
                  </elementProp>
                  <elementProp name="X-Requested-With" elementType="Header">
                    <stringProp name="Header.name">X-Requested-With</stringProp>
                    <stringProp name="Header.value">XMLHttpRequest</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
          </hashTree>
          <TransactionController guiclass="TransactionControllerGui" testclass="TransactionController" testname="Post Login" enabled="true">
            <boolProp name="TransactionController.parent">true</boolProp>
            <boolProp name="TransactionController.includeTimers">false</boolProp>
          </TransactionController>
          <hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/api/auth/login_Post Login" enabled="true">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="username" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">true</boolProp>
                    <stringProp name="Argument.name">username</stringProp>
                    <stringProp name="Argument.value"><EMAIL></stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                  <elementProp name="password" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">password</stringProp>
                    <stringProp name="Argument.value">password</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                  <elementProp name="submit" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">true</boolProp>
                    <stringProp name="Argument.name">submit</stringProp>
                    <stringProp name="Argument.value">Log In</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding">utf-8</stringProp>
              <stringProp name="HTTPSampler.path">/solutions/api/auth/login/</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">false</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.implementation">HttpClient4</stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Content-Type" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/x-www-form-urlencoded</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                  </elementProp>
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/api/auth/login</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/vaadin/VAADIN" enabled="true">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="sessionId" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">sessionId</stringProp>
                    <stringProp name="Argument.value">true</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding"></stringProp>
              <stringProp name="HTTPSampler.path">/vaadin/VAADIN</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Cache-Control" elementType="Header">
                    <stringProp name="Header.name">Cache-Control</stringProp>
                    <stringProp name="Header.value">no-cache</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">application/json, text/javascript, */*; q=0.01</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2062.124 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                  </elementProp>
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">http://us1sapp03.ideasstg.int:8080/solutions/api/page/postLogin</stringProp>
                  </elementProp>
                  <elementProp name="X-Requested-With" elementType="Header">
                    <stringProp name="Header.name">X-Requested-With</stringProp>
                    <stringProp name="Header.value">XMLHttpRequest</stringProp>
                  </elementProp>
                  <elementProp name="Pragma" elementType="Header">
                    <stringProp name="Header.name">Pragma</stringProp>
                    <stringProp name="Header.value">no-cache</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
              <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="Regular Expression Extractor" enabled="true">
                <stringProp name="RegexExtractor.useHeaders">false</stringProp>
                <stringProp name="RegexExtractor.refname">COOKIE_VAADIN</stringProp>
                <stringProp name="RegexExtractor.regex">jsessionid&quot;:&quot;(.*)&quot;</stringProp>
                <stringProp name="RegexExtractor.template">$1$</stringProp>
                <stringProp name="RegexExtractor.default">Not found SOA</stringProp>
                <stringProp name="RegexExtractor.match_number"></stringProp>
              </RegexExtractor>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/jsession" enabled="true">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments"/>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding"></stringProp>
              <stringProp name="HTTPSampler.path">/pacman-platformsecurity/jsession</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Cache-Control" elementType="Header">
                    <stringProp name="Header.name">Cache-Control</stringProp>
                    <stringProp name="Header.value">no-cache</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">application/json, text/javascript, */*; q=0.01</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2062.124 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                  </elementProp>
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">http://us1sapp03.ideasstg.int:8080/solutions/api/page/postLogin</stringProp>
                  </elementProp>
                  <elementProp name="X-Requested-With" elementType="Header">
                    <stringProp name="Header.name">X-Requested-With</stringProp>
                    <stringProp name="Header.value">XMLHttpRequest</stringProp>
                  </elementProp>
                  <elementProp name="Pragma" elementType="Header">
                    <stringProp name="Header.name">Pragma</stringProp>
                    <stringProp name="Header.value">no-cache</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
              <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="Regular Expression Extractor" enabled="true">
                <stringProp name="RegexExtractor.useHeaders">false</stringProp>
                <stringProp name="RegexExtractor.refname">COOKIE_SOA</stringProp>
                <stringProp name="RegexExtractor.regex">jsessionid&quot;:&quot;(.*)&quot;</stringProp>
                <stringProp name="RegexExtractor.template">$1$</stringProp>
                <stringProp name="RegexExtractor.default">Not found SOA</stringProp>
                <stringProp name="RegexExtractor.match_number"></stringProp>
              </RegexExtractor>
              <hashTree/>
              <BeanShellPostProcessor guiclass="TestBeanGUI" testclass="BeanShellPostProcessor" testname="BeanShell PostProcessor" enabled="true">
                <boolProp name="resetInterpreter">false</boolProp>
                <stringProp name="parameters"></stringProp>
                <stringProp name="filename"></stringProp>
                <stringProp name="script">import org.apache.jmeter.protocol.http.control.CookieManager;
import org.apache.jmeter.protocol.http.control.Cookie;
CookieManager manager = ctx.getCurrentSampler().getProperty(&quot;HTTPSampler.cookie_manager&quot;).getObjectValue();
Cookie cookie = new Cookie(&quot;SOA_JSESSIONID&quot;,&quot;${COOKIE_SOA}&quot;,&quot;${g3UrlDomainName}&quot;,&quot;/solutions&quot;,false,0);
manager.add(cookie);
cookie = new Cookie(&quot;VAADIN_JSESSIONID&quot;,&quot;${COOKIE_VAADIN}&quot;,&quot;${g3UrlDomainName}&quot;,&quot;/solutions&quot;,false,0);
manager.add(cookie);


</stringProp>
              </BeanShellPostProcessor>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/rest/workcontext/default/StressBox" enabled="true">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="_" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">_</stringProp>
                    <stringProp name="Argument.value">1369903470006</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding"></stringProp>
              <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/workcontext/default/${GeminiClientCode}</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">application/json, text/javascript, */*; q=0.01</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                  </elementProp>
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                  </elementProp>
                  <elementProp name="X-Requested-With" elementType="Header">
                    <stringProp name="Header.name">X-Requested-With</stringProp>
                    <stringProp name="Header.value">XMLHttpRequest</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/rest/user/11403" enabled="false">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="_" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">_</stringProp>
                    <stringProp name="Argument.value">1369903470001</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding"></stringProp>
              <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/user/11403</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="clientId" elementType="Header">
                    <stringProp name="Header.name">clientId</stringProp>
                    <stringProp name="Header.value">2</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                  </elementProp>
                  <elementProp name="clientCode" elementType="Header">
                    <stringProp name="Header.name">clientCode</stringProp>
                    <stringProp name="Header.value">BSTN</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                  </elementProp>
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                  </elementProp>
                  <elementProp name="X-Requested-With" elementType="Header">
                    <stringProp name="Header.name">X-Requested-With</stringProp>
                    <stringProp name="Header.value">XMLHttpRequest</stringProp>
                  </elementProp>
                  <elementProp name="propertyGroupId" elementType="Header">
                    <stringProp name="Header.name">propertyGroupId</stringProp>
                    <stringProp name="Header.value">713</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/rest/user/11403" enabled="false">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="_" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">_</stringProp>
                    <stringProp name="Argument.value">1369903470746</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding"></stringProp>
              <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/user/11403</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="clientId" elementType="Header">
                    <stringProp name="Header.name">clientId</stringProp>
                    <stringProp name="Header.value">${clientID}</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                  </elementProp>
                  <elementProp name="clientCode" elementType="Header">
                    <stringProp name="Header.name">clientCode</stringProp>
                    <stringProp name="Header.value">${clientCode}</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                  </elementProp>
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                  </elementProp>
                  <elementProp name="X-Requested-With" elementType="Header">
                    <stringProp name="Header.name">X-Requested-With</stringProp>
                    <stringProp name="Header.value">XMLHttpRequest</stringProp>
                  </elementProp>
                  <elementProp name="propertyId" elementType="Header">
                    <stringProp name="Header.name">propertyId</stringProp>
                    <stringProp name="Header.value">${propertyID}</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/api/page/permission/information-manager" enabled="false">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="_" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">_</stringProp>
                    <stringProp name="Argument.value">1369903470748</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/solutions/api/page/permission/information-manager</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="clientId" elementType="Header">
                    <stringProp name="Header.name">clientId</stringProp>
                    <stringProp name="Header.value">${clientID}</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                  </elementProp>
                  <elementProp name="clientCode" elementType="Header">
                    <stringProp name="Header.name">clientCode</stringProp>
                    <stringProp name="Header.value">${clientCode}</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                  </elementProp>
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                  </elementProp>
                  <elementProp name="X-Requested-With" elementType="Header">
                    <stringProp name="Header.name">X-Requested-With</stringProp>
                    <stringProp name="Header.value">XMLHttpRequest</stringProp>
                  </elementProp>
                  <elementProp name="propertyId" elementType="Header">
                    <stringProp name="Header.name">propertyId</stringProp>
                    <stringProp name="Header.value">${propertyID}</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/rest/authorization/propertygroup" enabled="false">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="_" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">_</stringProp>
                    <stringProp name="Argument.value">1369903470748</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding"></stringProp>
              <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/authorization/propertygroup</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="clientId" elementType="Header">
                    <stringProp name="Header.name">clientId</stringProp>
                    <stringProp name="Header.value">${clientID}</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                  </elementProp>
                  <elementProp name="clientCode" elementType="Header">
                    <stringProp name="Header.name">clientCode</stringProp>
                    <stringProp name="Header.value">${clientCode}</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">application/json, text/javascript, */*; q=0.01</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                  </elementProp>
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                  </elementProp>
                  <elementProp name="X-Requested-With" elementType="Header">
                    <stringProp name="Header.name">X-Requested-With</stringProp>
                    <stringProp name="Header.value">XMLHttpRequest</stringProp>
                  </elementProp>
                  <elementProp name="propertyId" elementType="Header">
                    <stringProp name="Header.name">propertyId</stringProp>
                    <stringProp name="Header.value">${propertyID}</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/api/page/clientlogo/StressBox" enabled="false">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="_" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">_</stringProp>
                    <stringProp name="Argument.value">1369903471558</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding">utf-8</stringProp>
              <stringProp name="HTTPSampler.path">/solutions/api/page/clientlogo/StressBox</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="clientId" elementType="Header">
                    <stringProp name="Header.name">clientId</stringProp>
                    <stringProp name="Header.value">7</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                  </elementProp>
                  <elementProp name="clientCode" elementType="Header">
                    <stringProp name="Header.name">clientCode</stringProp>
                    <stringProp name="Header.value">StressBox</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                  </elementProp>
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                  </elementProp>
                  <elementProp name="X-Requested-With" elementType="Header">
                    <stringProp name="Header.name">X-Requested-With</stringProp>
                    <stringProp name="Header.value">XMLHttpRequest</stringProp>
                  </elementProp>
                  <elementProp name="propertyId" elementType="Header">
                    <stringProp name="Header.name">propertyId</stringProp>
                    <stringProp name="Header.value">60850</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/rest/sync/event" enabled="false">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">{&quot;eventType&quot;:&quot;LOGIN&quot;}</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding"></stringProp>
              <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/sync/event</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Content-Type" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/json</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                  <elementProp name="clientCode" elementType="Header">
                    <stringProp name="Header.name">clientCode</stringProp>
                    <stringProp name="Header.value">StressBox</stringProp>
                  </elementProp>
                  <elementProp name="Origin" elementType="Header">
                    <stringProp name="Header.name">Origin</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="propertyId" elementType="Header">
                    <stringProp name="Header.name">propertyId</stringProp>
                    <stringProp name="Header.value">60850</stringProp>
                  </elementProp>
                  <elementProp name="clientId" elementType="Header">
                    <stringProp name="Header.name">clientId</stringProp>
                    <stringProp name="Header.value">7</stringProp>
                  </elementProp>
                  <elementProp name="X-Requested-With" elementType="Header">
                    <stringProp name="Header.name">X-Requested-With</stringProp>
                    <stringProp name="Header.value">XMLHttpRequest</stringProp>
                  </elementProp>
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/api/page/menu/information-manager" enabled="false">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="_" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">_</stringProp>
                    <stringProp name="Argument.value">1369903471559</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/solutions/api/page/menu/information-manager</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="clientId" elementType="Header">
                    <stringProp name="Header.name">clientId</stringProp>
                    <stringProp name="Header.value">7</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                  </elementProp>
                  <elementProp name="clientCode" elementType="Header">
                    <stringProp name="Header.name">clientCode</stringProp>
                    <stringProp name="Header.value">StressBox</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                  </elementProp>
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                  </elementProp>
                  <elementProp name="X-Requested-With" elementType="Header">
                    <stringProp name="Header.name">X-Requested-With</stringProp>
                    <stringProp name="Header.value">XMLHttpRequest</stringProp>
                  </elementProp>
                  <elementProp name="propertyId" elementType="Header">
                    <stringProp name="Header.name">propertyId</stringProp>
                    <stringProp name="Header.value">60850</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/rest/sync" enabled="false">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="_" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">_</stringProp>
                    <stringProp name="Argument.value">1369903471575</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding"></stringProp>
              <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/sync</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="clientId" elementType="Header">
                    <stringProp name="Header.name">clientId</stringProp>
                    <stringProp name="Header.value">7</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                  </elementProp>
                  <elementProp name="clientCode" elementType="Header">
                    <stringProp name="Header.name">clientCode</stringProp>
                    <stringProp name="Header.value">StressBox</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">application/json, text/javascript, */*; q=0.01</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                  </elementProp>
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                  </elementProp>
                  <elementProp name="X-Requested-With" elementType="Header">
                    <stringProp name="Header.name">X-Requested-With</stringProp>
                    <stringProp name="Header.value">XMLHttpRequest</stringProp>
                  </elementProp>
                  <elementProp name="propertyId" elementType="Header">
                    <stringProp name="Header.name">propertyId</stringProp>
                    <stringProp name="Header.value">60850</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/api/page/userMenu/information-manager" enabled="false">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="_" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">_</stringProp>
                    <stringProp name="Argument.value">1369903471559</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/solutions/api/page/userMenu/information-manager</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="clientId" elementType="Header">
                    <stringProp name="Header.name">clientId</stringProp>
                    <stringProp name="Header.value">7</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                  </elementProp>
                  <elementProp name="clientCode" elementType="Header">
                    <stringProp name="Header.name">clientCode</stringProp>
                    <stringProp name="Header.value">StressBox</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                  </elementProp>
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                  </elementProp>
                  <elementProp name="X-Requested-With" elementType="Header">
                    <stringProp name="Header.name">X-Requested-With</stringProp>
                    <stringProp name="Header.value">XMLHttpRequest</stringProp>
                  </elementProp>
                  <elementProp name="propertyId" elementType="Header">
                    <stringProp name="Header.name">propertyId</stringProp>
                    <stringProp name="Header.value">60850</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/rest/dates/system" enabled="false">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="_" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">_</stringProp>
                    <stringProp name="Argument.value">1369903471576</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding"></stringProp>
              <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/dates/system</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="clientId" elementType="Header">
                    <stringProp name="Header.name">clientId</stringProp>
                    <stringProp name="Header.value">7</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                  </elementProp>
                  <elementProp name="clientCode" elementType="Header">
                    <stringProp name="Header.name">clientCode</stringProp>
                    <stringProp name="Header.value">StressBox</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                  </elementProp>
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                  </elementProp>
                  <elementProp name="X-Requested-With" elementType="Header">
                    <stringProp name="Header.name">X-Requested-With</stringProp>
                    <stringProp name="Header.value">XMLHttpRequest</stringProp>
                  </elementProp>
                  <elementProp name="propertyId" elementType="Header">
                    <stringProp name="Header.name">propertyId</stringProp>
                    <stringProp name="Header.value">60850</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/rest/sync/event" enabled="false">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">{&quot;pageCode&quot;:&quot;information-manager&quot;,&quot;eventType&quot;:&quot;PAGE_LOADED&quot;}</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding"></stringProp>
              <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/sync/event</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Content-Type" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/json</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                  <elementProp name="clientCode" elementType="Header">
                    <stringProp name="Header.name">clientCode</stringProp>
                    <stringProp name="Header.value">StressBox</stringProp>
                  </elementProp>
                  <elementProp name="Origin" elementType="Header">
                    <stringProp name="Header.name">Origin</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="propertyId" elementType="Header">
                    <stringProp name="Header.name">propertyId</stringProp>
                    <stringProp name="Header.value">60850</stringProp>
                  </elementProp>
                  <elementProp name="clientId" elementType="Header">
                    <stringProp name="Header.name">clientId</stringProp>
                    <stringProp name="Header.value">7</stringProp>
                  </elementProp>
                  <elementProp name="X-Requested-With" elementType="Header">
                    <stringProp name="Header.name">X-Requested-With</stringProp>
                    <stringProp name="Header.value">XMLHttpRequest</stringProp>
                  </elementProp>
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
          </hashTree>
          <TransactionController guiclass="TransactionControllerGui" testclass="TransactionController" testname="RPtHomePage" enabled="true">
            <boolProp name="TransactionController.includeTimers">false</boolProp>
            <stringProp name="TestPlan.comments">50 property groups - 200 rate code range foe one year</stringProp>
            <boolProp name="TransactionController.parent">true</boolProp>
          </TransactionController>
          <hashTree>
            <GenericController guiclass="LogicControllerGui" testclass="GenericController" testname=" Select Client and Property and Multiproperty-rate-plan-report" enabled="true"/>
            <hashTree/>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/vaadin/multiPropertyRatePlanReport/?&amp;v-1540892002994" enabled="true">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="v-browserDetails" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">v-browserDetails</stringProp>
                    <stringProp name="Argument.value">1</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                  <elementProp name="theme" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">theme</stringProp>
                    <stringProp name="Argument.value">tetris-valo</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                  <elementProp name="v-appId" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">v-appId</stringProp>
                    <stringProp name="Argument.value">vaadin-app</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                  <elementProp name="v-sh" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">v-sh</stringProp>
                    <stringProp name="Argument.value">960</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                  <elementProp name="v-sw" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">v-sw</stringProp>
                    <stringProp name="Argument.value">1280</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                  <elementProp name="v-cw" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">v-cw</stringProp>
                    <stringProp name="Argument.value">1280</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                  <elementProp name="v-ch" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">v-ch</stringProp>
                    <stringProp name="Argument.value">818</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                  <elementProp name="v-curdate" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">v-curdate</stringProp>
                    <stringProp name="Argument.value">1540892002994</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                  <elementProp name="v-tzo" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">v-tzo</stringProp>
                    <stringProp name="Argument.value">-330</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                  <elementProp name="v-dstd" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">v-dstd</stringProp>
                    <stringProp name="Argument.value">0</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                  <elementProp name="v-rtzo" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">v-rtzo</stringProp>
                    <stringProp name="Argument.value">-330</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                  <elementProp name="v-dston" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">v-dston</stringProp>
                    <stringProp name="Argument.value">false</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                  <elementProp name="v-tzid" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">true</boolProp>
                    <stringProp name="Argument.name">v-tzid</stringProp>
                    <stringProp name="Argument.value">Asia/Calcutta</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                  <elementProp name="v-vw" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">v-vw</stringProp>
                    <stringProp name="Argument.value">1280</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                  <elementProp name="v-vh" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">v-vh</stringProp>
                    <stringProp name="Argument.value">0</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                  <elementProp name="v-loc" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">true</boolProp>
                    <stringProp name="Argument.name">v-loc</stringProp>
                    <stringProp name="Argument.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}/solutions/multiproperty-rate-plan-report#/p20001</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                  <elementProp name="v-wn" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">v-wn</stringProp>
                    <stringProp name="Argument.value">vaadin-app-0.12326387761862012</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/vaadin/multiPropertyRatePlanReport/?&amp;v-1540892002994</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">true</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}/solutions/multiproperty-rate-plan-report</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
                  </elementProp>
                  <elementProp name="Origin" elementType="Header">
                    <stringProp name="Header.name">Origin</stringProp>
                    <stringProp name="Header.value">http://${g3UrlDomainName}:8080</stringProp>
                  </elementProp>
                  <elementProp name="Content-type" elementType="Header">
                    <stringProp name="Header.name">Content-type</stringProp>
                    <stringProp name="Header.value">application/x-www-form-urlencoded</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.77 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
              <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="Regular Expression Extractor" enabled="true">
                <stringProp name="RegexExtractor.useHeaders">false</stringProp>
                <stringProp name="RegexExtractor.refname">key</stringProp>
                <stringProp name="RegexExtractor.regex">\\&quot;Vaadin-Security-Key\\&quot;\:\\&quot;(.+?)\\&quot;</stringProp>
                <stringProp name="RegexExtractor.template">$1$</stringProp>
                <stringProp name="RegexExtractor.default">no_key</stringProp>
                <stringProp name="RegexExtractor.match_number">1</stringProp>
              </RegexExtractor>
              <hashTree/>
              <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="Regular Expression Extractor" enabled="true">
                <stringProp name="RegexExtractor.useHeaders">false</stringProp>
                <stringProp name="RegexExtractor.refname">stateID</stringProp>
                <stringProp name="RegexExtractor.regex">\\&quot;state\\&quot;\:\{\\&quot;(.+?)\\&quot;</stringProp>
                <stringProp name="RegexExtractor.template">$1$</stringProp>
                <stringProp name="RegexExtractor.default">no_state</stringProp>
                <stringProp name="RegexExtractor.match_number">1</stringProp>
              </RegexExtractor>
              <hashTree/>
              <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="Regular Expression Extractor" enabled="true">
                <stringProp name="RegexExtractor.useHeaders">false</stringProp>
                <stringProp name="RegexExtractor.refname">vuid</stringProp>
                <stringProp name="RegexExtractor.regex">\&quot;v-uiId\&quot;\:(.+?)</stringProp>
                <stringProp name="RegexExtractor.template">$1$</stringProp>
                <stringProp name="RegexExtractor.default">no_vuid</stringProp>
                <stringProp name="RegexExtractor.match_number">1</stringProp>
              </RegexExtractor>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/vaadin/UIDL/MultiPropertyRatePlan Report Home Page" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">{&quot;csrfToken&quot;:&quot;${key}&quot;,&quot;rpc&quot;:[[&quot;${newStateId}&quot;,&quot;com.vaadin.ui.JavaScript$JavaScriptCallbackRpc&quot;,&quot;call&quot;,[&quot;ideas.vaadin.sendSetupData&quot;,[{&quot;workContext&quot;:{&quot;isInternal&quot;:true,&quot;clientId&quot;:${GeminiClientID},&quot;propertyCode&quot;:&quot;${GeminiPropertyCode}&quot;,&quot;clientCode&quot;:&quot;${GeminiClientCode}&quot;,&quot;propertyGroupId&quot;:null,&quot;isContinuousPricingEnabled&quot;:&quot;false&quot;,&quot;propertyId&quot;:${GeminiPropertyId},&quot;userId&quot;:&quot;11403&quot;},&quot;language&quot;:&quot;en&quot;,&quot;userPreferences&quot;:{&quot;dn&quot;:&quot;uid=11403,ou=users,o=ideas,dc=ideas,dc=com&quot;,&quot;cn&quot;:&quot;SSO User&quot;,&quot;givenName&quot;:&quot;SSO&quot;,&quot;mail&quot;:&quot;<EMAIL>&quot;,&quot;sn&quot;:&quot;User&quot;,&quot;uid&quot;:&quot;11403&quot;,&quot;active&quot;:true,&quot;client&quot;:&quot;ideas&quot;,&quot;defaultProperty&quot;:&quot;66001&quot;,&quot;defaultPropertyGroup&quot;:&quot;&quot;,&quot;defaultPropertyOrGroup&quot;:&quot;property&quot;,&quot;defaultLandingPage&quot;:&quot;information-manager&quot;,&quot;viewingPreference&quot;:&quot;calendar&quot;,&quot;salesforceOrganizationId&quot;:&quot;00DV0000000DUHl&quot;,&quot;salesforcePortalId&quot;:&quot;060V00000000QB1&quot;,&quot;isCorporate&quot;:true,&quot;hasSalesforceAccess&quot;:false,&quot;defaultDateFormat&quot;:&quot;dd-mmm-yyyy&quot;,&quot;passwordActiveDuration&quot;:&quot;-1&quot;,&quot;notifyPasswordExpiration&quot;:false,&quot;daysRemainingToExpirePassword&quot;:90,&quot;enableFiscalCalendar&quot;:false,&quot;sunFmSaml2NameIdInfo&quot;:null,&quot;sunFmSaml2NameIdInfokey&quot;:null,&quot;hasLearningAccess&quot;:false,&quot;language&quot;:&quot;en_US&quot;,&quot;passwordNeverExpire&quot;:true,&quot;propertyRoles&quot;:[],&quot;internal&quot;:true,&quot;authGroupRoles&quot;:[{&quot;roleId&quot;:&quot;-666&quot;,&quot;authGroupId&quot;:&quot;-666&quot;}]}}]]]],&quot;syncId&quot;:0,&quot;clientId&quot;:0,&quot;wsver&quot;:&quot;8.4.3&quot;}</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/vaadin/UIDL/?v-uiId=${vuid}</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">true</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}/solutions/multiproperty-rate-plan-report</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
                  </elementProp>
                  <elementProp name="Origin" elementType="Header">
                    <stringProp name="Header.name">Origin</stringProp>
                    <stringProp name="Header.value">http://${g3UrlDomainName}:8080</stringProp>
                  </elementProp>
                  <elementProp name="Content-Type" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/json; charset=UTF-8</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.77 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
              <BeanShellPreProcessor guiclass="TestBeanGUI" testclass="BeanShellPreProcessor" testname="BeanShell PreProcessor" enabled="true">
                <boolProp name="resetInterpreter">false</boolProp>
                <stringProp name="parameters"></stringProp>
                <stringProp name="filename"></stringProp>
                <stringProp name="script">vars.put(&quot;newStateId&quot;,Integer.parseInt(vars.get(&quot;stateID&quot;))+1 + &quot;&quot;);</stringProp>
              </BeanShellPreProcessor>
              <hashTree/>
              <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="RegExp-stateIDSlider" enabled="true">
                <stringProp name="RegexExtractor.useHeaders">false</stringProp>
                <stringProp name="RegexExtractor.refname">stateIdSlider</stringProp>
                <stringProp name="RegexExtractor.regex">\&quot;rpc\&quot; \: \[\[\&quot;(\d+)\&quot;</stringProp>
                <stringProp name="RegexExtractor.template">$1$</stringProp>
                <stringProp name="RegexExtractor.default">no_stateIdSlider</stringProp>
                <stringProp name="RegexExtractor.match_number">1</stringProp>
              </RegexExtractor>
              <hashTree/>
              <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="RegExprpc_stdate" enabled="true">
                <stringProp name="RegexExtractor.useHeaders">false</stringProp>
                <stringProp name="RegexExtractor.refname">rpcStdate</stringProp>
                <stringProp name="RegexExtractor.regex">\&quot;(\d+)\&quot;\:\{\&quot;direction\&quot;\:10\,\&quot;caption\&quot;\:\&quot;\Date Range\&quot;</stringProp>
                <stringProp name="RegexExtractor.template">$1$</stringProp>
                <stringProp name="RegexExtractor.default">no_rpcStdate</stringProp>
                <stringProp name="RegexExtractor.match_number">1</stringProp>
              </RegexExtractor>
              <hashTree/>
              <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="RegExp_RPC_Report1" enabled="true">
                <stringProp name="RegexExtractor.useHeaders">false</stringProp>
                <stringProp name="RegexExtractor.refname">rpcReport1</stringProp>
                <stringProp name="RegexExtractor.regex">\[\&quot;box\-light\-grey\&quot;\]\}\,\&quot;(\d+)\&quot;</stringProp>
                <stringProp name="RegexExtractor.template">$1$</stringProp>
                <stringProp name="RegexExtractor.default">no_rpcReport1</stringProp>
                <stringProp name="RegexExtractor.match_number">1</stringProp>
              </RegexExtractor>
              <hashTree/>
              <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="RegExp_RPC_Report6" enabled="true">
                <stringProp name="RegexExtractor.useHeaders">false</stringProp>
                <stringProp name="RegexExtractor.refname">rpcReport6</stringProp>
                <stringProp name="RegexExtractor.regex">\&quot;pid\&quot;\:\&quot;(\d+)\&quot;</stringProp>
                <stringProp name="RegexExtractor.template">$1$</stringProp>
                <stringProp name="RegexExtractor.default">no_rpcReport6</stringProp>
                <stringProp name="RegexExtractor.match_number">1</stringProp>
              </RegexExtractor>
              <hashTree/>
              <ResponseAssertion guiclass="AssertionGui" testclass="ResponseAssertion" testname="Response Assertion" enabled="false">
                <collectionProp name="Asserion.test_strings">
                  <stringProp name="275968282">multipropertyrateplanreportpage</stringProp>
                </collectionProp>
                <stringProp name="Assertion.custom_message"></stringProp>
                <stringProp name="Assertion.test_field">Assertion.response_data</stringProp>
                <boolProp name="Assertion.assume_success">false</boolProp>
                <intProp name="Assertion.test_type">2</intProp>
              </ResponseAssertion>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/vaadin/UIDL/MultiPropertyRatePlan Report Home Page" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">{&quot;csrfToken&quot;:&quot;${key}&quot;,&quot;rpc&quot;:[[&quot;${stateID}&quot;,&quot;com.vaadin.shared.ui.ui.UIServerRpc&quot;,&quot;resize&quot;,[1280,2,1280,818]]],&quot;syncId&quot;:1,&quot;clientId&quot;:1}</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/vaadin/UIDL/?v-uiId=${vuid}</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">true</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}/solutions/multiproperty-rate-plan-report</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
                  </elementProp>
                  <elementProp name="Origin" elementType="Header">
                    <stringProp name="Header.name">Origin</stringProp>
                    <stringProp name="Header.value">http://${g3UrlDomainName}:8080</stringProp>
                  </elementProp>
                  <elementProp name="Content-Type" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/json; charset=UTF-8</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.77 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/vaadin/UIDL/MultiPropertyRatePlan Report Home Page" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">{&quot;csrfToken&quot;:&quot;${key}&quot;,&quot;rpc&quot;:[[&quot;${stateID}&quot;,&quot;com.vaadin.shared.ui.ui.UIServerRpc&quot;,&quot;resize&quot;,[1263,1087,1263,818]]],&quot;syncId&quot;:2,&quot;clientId&quot;:2}</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/vaadin/UIDL/?v-uiId=${vuid}</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">true</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}/solutions/multiproperty-rate-plan-report</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
                  </elementProp>
                  <elementProp name="Origin" elementType="Header">
                    <stringProp name="Header.name">Origin</stringProp>
                    <stringProp name="Header.value">http://us1s3ap01.ideasstg.int:8080</stringProp>
                  </elementProp>
                  <elementProp name="Content-Type" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/json; charset=UTF-8</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.77 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
          </hashTree>
          <TransactionController guiclass="TransactionControllerGui" testclass="TransactionController" testname="SelectRCRange" enabled="true">
            <boolProp name="TransactionController.includeTimers">false</boolProp>
            <boolProp name="TransactionController.parent">true</boolProp>
          </TransactionController>
          <hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/vaadin/UIDL/Rate Code Range Selection using slider" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">{&quot;csrfToken&quot;:&quot;${key}&quot;,&quot;rpc&quot;:[[&quot;${stateIdSlider}&quot;,&quot;com.ideas.tetris.ui.common.component.slider.dualslider.DualSliderServerRpc&quot;,&quot;onValuesChange&quot;,[${StartRCRange},${EndRCRange}]]],&quot;syncId&quot;:3,&quot;clientId&quot;:3}</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/vaadin/UIDL/?v-uiId=${vuid}</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">true</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}/solutions/multiproperty-rate-plan-report</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
                  </elementProp>
                  <elementProp name="Origin" elementType="Header">
                    <stringProp name="Header.name">Origin</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}</stringProp>
                  </elementProp>
                  <elementProp name="Content-Type" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/json; charset=UTF-8</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate, br</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT ${StartRCRange}0.0; Win${GeminiClientID}4; x${GeminiClientID}4) AppleWebKit/537.3${GeminiClientID} (KHTML, like Gecko) Chrome/70.0.3538.${StartRCRange}02 Safari/537.3${GeminiClientID}</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/vaadin/UIDL/Rate Code Range Selection" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">{&quot;csrfToken&quot;:&quot;${key}&quot;,&quot;rpc&quot;:[[&quot;${stateID}&quot;,&quot;com.vaadin.shared.ui.ui.UIServerRpc&quot;,&quot;resize&quot;,[2${StartRCRange}${StartRCRange}4,74${GeminiClientID},2${StartRCRange}${StartRCRange}4,4${StartRCRange}4]]],&quot;syncId&quot;:4,&quot;clientId&quot;:4}</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain">${g3UrlDomainName}</stringProp>
              <stringProp name="HTTPSampler.port">${g3Port}</stringProp>
              <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/vaadin/UIDL/?v-uiId=${vuid}</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">true</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}/solutions/multiproperty-rate-plan-report</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
                  </elementProp>
                  <elementProp name="Origin" elementType="Header">
                    <stringProp name="Header.name">Origin</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}</stringProp>
                  </elementProp>
                  <elementProp name="Content-Type" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/json; charset=UTF-8</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate, br</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT ${StartRCRange}0.0; Win${GeminiClientID}4; x${GeminiClientID}4) AppleWebKit/537.3${GeminiClientID} (KHTML, like Gecko) Chrome/70.0.3538.${StartRCRange}02 Safari/537.3${GeminiClientID}</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/vaadin/UIDL/Rate Code Range Selection" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">{&quot;csrfToken&quot;:&quot;${key}&quot;,&quot;rpc&quot;:[[&quot;${stateID}&quot;,&quot;com.vaadin.shared.ui.ui.UIServerRpc&quot;,&quot;resize&quot;,[2${StartRCRange}${StartRCRange}4,747,2${StartRCRange}${StartRCRange}4,4${StartRCRange}4]]],&quot;syncId&quot;:5,&quot;clientId&quot;:5}</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain">${g3UrlDomainName}</stringProp>
              <stringProp name="HTTPSampler.port">${g3Port}</stringProp>
              <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/vaadin/UIDL/?v-uiId=${vuid}</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">true</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}/solutions/multiproperty-rate-plan-report</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
                  </elementProp>
                  <elementProp name="Origin" elementType="Header">
                    <stringProp name="Header.name">Origin</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}</stringProp>
                  </elementProp>
                  <elementProp name="Content-Type" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/json; charset=UTF-8</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate, br</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT ${StartRCRange}0.0; Win${GeminiClientID}4; x${GeminiClientID}4) AppleWebKit/537.3${GeminiClientID} (KHTML, like Gecko) Chrome/70.0.3538.${StartRCRange}02 Safari/537.3${GeminiClientID}</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/vaadin/UIDL/Rate Code Range Selection" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">{&quot;csrfToken&quot;:&quot;${key}&quot;,&quot;rpc&quot;:[[&quot;${rpcStdate}&quot;,&quot;org.vaadin.hene.popupbutton.widgetset.client.ui.PopupButtonServerRpc&quot;,&quot;setPopupVisible&quot;,[true]],[&quot;${rpcStdate}&quot;,&quot;com.vaadin.shared.ui.button.ButtonServerRpc&quot;,&quot;click&quot;,[{&quot;altKey&quot;:false,&quot;button&quot;:&quot;LEFT&quot;,&quot;clientX&quot;:2${EndMonth}8,&quot;clientY&quot;:${StartRCRange}55,&quot;ctrlKey&quot;:false,&quot;metaKey&quot;:false,&quot;relativeX&quot;:${StartRCRange}${StartRCRange}8,&quot;relativeY&quot;:${StartRCRange}${StartRCRange},&quot;shiftKey&quot;:false,&quot;type&quot;:${StartRCRange}}]]],&quot;syncId&quot;:6,&quot;clientId&quot;:6}</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain">${g3UrlDomainName}</stringProp>
              <stringProp name="HTTPSampler.port">${g3Port}</stringProp>
              <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/vaadin/UIDL/?v-uiId=${vuid}</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">true</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}/solutions/multiproperty-rate-plan-report</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
                  </elementProp>
                  <elementProp name="Origin" elementType="Header">
                    <stringProp name="Header.name">Origin</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}</stringProp>
                  </elementProp>
                  <elementProp name="Content-Type" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/json; charset=UTF-8</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate, br</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT ${StartRCRange}0.0; Win${StartMonth}4; x${StartMonth}4) AppleWebKit/53${EndMonth}.3${StartMonth} (KHTML, like Gecko) Chrome/${EndMonth}0.0.3538.${StartRCRange}02 Safari/53${EndMonth}.3${StartMonth}</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
              <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="RegExp_RPCDate1" enabled="true">
                <stringProp name="RegexExtractor.useHeaders">false</stringProp>
                <stringProp name="RegexExtractor.refname">RPCDate1</stringProp>
                <stringProp name="RegexExtractor.regex">\&quot;(\d+)\&quot;\:\{\&quot;direction\&quot;\:10\,\&quot;caption\&quot;</stringProp>
                <stringProp name="RegexExtractor.template">$1$</stringProp>
                <stringProp name="RegexExtractor.default">no_rpc_Date1</stringProp>
                <stringProp name="RegexExtractor.match_number">1</stringProp>
              </RegexExtractor>
              <hashTree/>
              <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="RegExp_rpc_EdDate4" enabled="true">
                <stringProp name="RegexExtractor.useHeaders">false</stringProp>
                <stringProp name="RegexExtractor.refname">rpc_EdDate4</stringProp>
                <stringProp name="RegexExtractor.regex">\&quot;text\&quot;\:\&quot;End Date\: \&quot;\,\&quot;immediate\&quot;\:true\}\,\&quot;(\d+)\&quot;</stringProp>
                <stringProp name="RegexExtractor.template">$1$</stringProp>
                <stringProp name="RegexExtractor.default">No_rpc_EdDate3</stringProp>
                <stringProp name="RegexExtractor.match_number">1</stringProp>
              </RegexExtractor>
              <hashTree/>
            </hashTree>
          </hashTree>
          <TransactionController guiclass="TransactionControllerGui" testclass="TransactionController" testname="SelectDateRange" enabled="true">
            <boolProp name="TransactionController.includeTimers">false</boolProp>
            <boolProp name="TransactionController.parent">true</boolProp>
          </TransactionController>
          <hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/vaadin/UIDL/Date Range - Start date selection" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">{&quot;csrfToken&quot;:&quot;${key}&quot;,&quot;rpc&quot;:[[&quot;${RPCDate1}&quot;,&quot;org.vaadin.hene.popupbutton.widgetset.client.ui.PopupButtonServerRpc&quot;,&quot;setPopupVisible&quot;,[true]],[&quot;${RPCDate1}&quot;,&quot;com.vaadin.shared.ui.button.ButtonServerRpc&quot;,&quot;click&quot;,[{&quot;altKey&quot;:false,&quot;button&quot;:&quot;LEFT&quot;,&quot;clientX&quot;:298,&quot;clientY&quot;:${EndRCRange}4,&quot;ctrlKey&quot;:false,&quot;metaKey&quot;:false,&quot;relativeX&quot;:39,&quot;relativeY&quot;:8,&quot;shiftKey&quot;:false,&quot;type&quot;:${StartRCRange}}]]],&quot;syncId&quot;:7,&quot;clientId&quot;:7}</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain">${g3UrlDomainName}</stringProp>
              <stringProp name="HTTPSampler.port">${g3Port}</stringProp>
              <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/vaadin/UIDL/?v-uiId=${vuid}</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">true</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}/solutions/multiproperty-rate-plan-report?r=${StartMonth}${StartMonth}82</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
                  </elementProp>
                  <elementProp name="Origin" elementType="Header">
                    <stringProp name="Header.name">Origin</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}</stringProp>
                  </elementProp>
                  <elementProp name="Content-Type" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/json; charset=UTF-8</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate, br</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT ${StartRCRange}0.0; Win${StartMonth}4; x${StartMonth}4) AppleWebKit/53${EndMonth}.3${StartMonth} (KHTML, like Gecko) Chrome/${EndMonth}0.0.3538.${StartRCRange}02 Safari/53${EndMonth}.3${StartMonth}</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
              <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="RegExp_RPC_Date2" enabled="true">
                <stringProp name="RegexExtractor.useHeaders">false</stringProp>
                <stringProp name="RegexExtractor.refname">RPCDate2</stringProp>
                <stringProp name="RegexExtractor.regex">\&quot;id\&quot;\:\&quot;(\d+)\&quot;\,\&quot;locale\&quot;\:\&quot;en_US\&quot;\,\&quot;format\&quot;</stringProp>
                <stringProp name="RegexExtractor.template">$1$</stringProp>
                <stringProp name="RegexExtractor.default">no_rpc_Date2</stringProp>
                <stringProp name="RegexExtractor.match_number">1</stringProp>
              </RegexExtractor>
              <hashTree/>
              <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="RegExp_rpc_EdDate1" enabled="true">
                <stringProp name="RegexExtractor.useHeaders">false</stringProp>
                <stringProp name="RegexExtractor.refname">rpc_EdDate1</stringProp>
                <stringProp name="RegexExtractor.regex">\&quot;(\d+)\&quot;\:\{\&quot;direction\&quot;\:10\,\&quot;disableOnClick\&quot;\:true\,\&quot;caption\&quot;\:\&quot;Apply\&quot;</stringProp>
                <stringProp name="RegexExtractor.template">$1$</stringProp>
                <stringProp name="RegexExtractor.default">No_rpc_EdDate1</stringProp>
                <stringProp name="RegexExtractor.match_number">1</stringProp>
              </RegexExtractor>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/vaadin/UIDL/Selecting Day, Month and year for start date range" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">{&quot;csrfToken&quot;:&quot;${key}&quot;,&quot;rpc&quot;:[[&quot;${RPCDate2}&quot;,&quot;v&quot;,&quot;v&quot;,[&quot;year&quot;,[&quot;i&quot;,${EndRCRange}${StartRCRange}${StartMonth}]]],[&quot;${RPCDate2}&quot;,&quot;v&quot;,&quot;v&quot;,[&quot;month&quot;,[&quot;i&quot;,${StartMonth}]]],[&quot;${RPCDate2}&quot;,&quot;v&quot;,&quot;v&quot;,[&quot;day&quot;,[&quot;i&quot;,${EndDate}]]]],&quot;syncId&quot;:8,&quot;clientId&quot;:8}</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain">${g3UrlDomainName}</stringProp>
              <stringProp name="HTTPSampler.port">${g3Port}</stringProp>
              <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/vaadin/UIDL/?v-uiId=${vuid}</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">true</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}/solutions/multiproperty-rate-plan-report?r=${StartMonth}${StartMonth}82</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
                  </elementProp>
                  <elementProp name="Origin" elementType="Header">
                    <stringProp name="Header.name">Origin</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}</stringProp>
                  </elementProp>
                  <elementProp name="Content-Type" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/json; charset=UTF-8</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate, br</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT ${StartRCRange}0.0; Win${StartMonth}4; x${StartMonth}4) AppleWebKit/53${EndMonth}.3${StartMonth} (KHTML, like Gecko) Chrome/${EndMonth}0.0.3538.${StartRCRange}02 Safari/53${EndMonth}.3${StartMonth}</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <GenericController guiclass="LogicControllerGui" testclass="GenericController" testname="Selecting the End date" enabled="true"/>
            <hashTree/>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/vaadin/UIDL/Date Range - End date selection" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">{&quot;csrfToken&quot;:&quot;${key}&quot;,&quot;rpc&quot;:[[&quot;${rpc_EdDate1}&quot;,&quot;org.vaadin.hene.popupbutton.widgetset.client.ui.PopupButtonServerRpc&quot;,&quot;setPopupVisible&quot;,[true]],[&quot;${rpc_EdDate1}&quot;,&quot;com.vaadin.shared.ui.button.ButtonServerRpc&quot;,&quot;disableOnClick&quot;,[]],[&quot;${rpc_EdDate1}&quot;,&quot;com.vaadin.shared.ui.button.ButtonServerRpc&quot;,&quot;click&quot;,[{&quot;altKey&quot;:false,&quot;button&quot;:&quot;LEFT&quot;,&quot;clientX&quot;:35${StartMonth},&quot;clientY&quot;:525,&quot;ctrlKey&quot;:false,&quot;metaKey&quot;:false,&quot;relativeX&quot;:3${StartMonth},&quot;relativeY&quot;:${StartRCRange}5,&quot;shiftKey&quot;:false,&quot;type&quot;:${StartRCRange}}]]],&quot;syncId&quot;:9,&quot;clientId&quot;:9}</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain">${g3UrlDomainName}</stringProp>
              <stringProp name="HTTPSampler.port">${g3Port}</stringProp>
              <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/vaadin/UIDL/?v-uiId=${vuid}</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">true</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}/solutions/multiproperty-rate-plan-report?r=${StartMonth}${StartMonth}82</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
                  </elementProp>
                  <elementProp name="Origin" elementType="Header">
                    <stringProp name="Header.name">Origin</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}</stringProp>
                  </elementProp>
                  <elementProp name="Content-Type" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/json; charset=UTF-8</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate, br</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT ${StartRCRange}0.0; Win${StartMonth}4; x${StartMonth}4) AppleWebKit/53${EndMonth}.3${StartMonth} (KHTML, like Gecko) Chrome/${EndMonth}0.0.3538.${StartRCRange}02 Safari/53${EndMonth}.3${StartMonth}</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
              <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="RegExp_endDate3" enabled="true">
                <stringProp name="RegexExtractor.useHeaders">false</stringProp>
                <stringProp name="RegexExtractor.refname">endDate3</stringProp>
                <stringProp name="RegexExtractor.regex">\&quot;id\&quot;\:\&quot;(\d+)\&quot;\,\&quot;v\&quot;\:\{\&quot;action\&quot;\:\&quot;\&quot;\}</stringProp>
                <stringProp name="RegexExtractor.template">$1$</stringProp>
                <stringProp name="RegexExtractor.default">no_rpc_endDate3</stringProp>
                <stringProp name="RegexExtractor.match_number">1</stringProp>
              </RegexExtractor>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/vaadin/UIDL/Date Range - End date selection" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">{&quot;csrfToken&quot;:&quot;${key}&quot;,&quot;rpc&quot;:[[&quot;${endDate3}&quot;,&quot;com.vaadin.shared.ui.ui.UIServerRpc&quot;,&quot;resize&quot;,[${StartRCRange}499,${EndMonth}4${EndMonth},${StartRCRange}499,${StartMonth}${StartRCRange}0]]],&quot;syncId&quot;:10,&quot;clientId&quot;:10}</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain">${g3UrlDomainName}</stringProp>
              <stringProp name="HTTPSampler.port">${g3Port}</stringProp>
              <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/vaadin/UIDL/?v-uiId=${vuid}</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">true</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}/solutions/multiproperty-rate-plan-report?r=${StartMonth}${StartMonth}82</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
                  </elementProp>
                  <elementProp name="Origin" elementType="Header">
                    <stringProp name="Header.name">Origin</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}</stringProp>
                  </elementProp>
                  <elementProp name="Content-Type" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/json; charset=UTF-8</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate, br</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT ${StartRCRange}0.0; Win${StartMonth}4; x${StartMonth}4) AppleWebKit/53${EndMonth}.3${StartMonth} (KHTML, like Gecko) Chrome/${EndMonth}0.0.3538.${StartRCRange}02 Safari/53${EndMonth}.3${StartMonth}</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/vaadin/UIDL/Date Range - End date selection" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">{&quot;csrfToken&quot;:&quot;${key}&quot;,&quot;rpc&quot;:[[&quot;${rpc_EdDate4}&quot;,&quot;org.vaadin.hene.popupbutton.widgetset.client.ui.PopupButtonServerRpc&quot;,&quot;setPopupVisible&quot;,[true]],[&quot;${rpc_EdDate4}&quot;,&quot;com.vaadin.shared.ui.button.ButtonServerRpc&quot;,&quot;click&quot;,[{&quot;altKey&quot;:false,&quot;button&quot;:&quot;LEFT&quot;,&quot;clientX&quot;:53${EndMonth},&quot;clientY&quot;:${EndDate}3,&quot;ctrlKey&quot;:false,&quot;metaKey&quot;:false,&quot;relativeX&quot;:82,&quot;relativeY&quot;:${StartRCRange}${EndMonth},&quot;shiftKey&quot;:false,&quot;type&quot;:${StartRCRange}}]]],&quot;syncId&quot;:11,&quot;clientId&quot;:11}</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain">${g3UrlDomainName}</stringProp>
              <stringProp name="HTTPSampler.port">${g3Port}</stringProp>
              <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/vaadin/UIDL/?v-uiId=${vuid}</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">true</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="RegExp_rpc_EndDate6" enabled="true">
                <stringProp name="RegexExtractor.useHeaders">false</stringProp>
                <stringProp name="RegexExtractor.refname">rpc_EndDate6</stringProp>
                <stringProp name="RegexExtractor.regex">\: Click to View\&quot;\}\,\&quot;(\d+)\&quot;</stringProp>
                <stringProp name="RegexExtractor.template">$1$</stringProp>
                <stringProp name="RegexExtractor.default">No_rpc_EdDate5</stringProp>
                <stringProp name="RegexExtractor.match_number">1</stringProp>
              </RegexExtractor>
              <hashTree/>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}/solutions/multiproperty-rate-plan-report?r=${StartMonth}${StartMonth}82</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
                  </elementProp>
                  <elementProp name="Origin" elementType="Header">
                    <stringProp name="Header.name">Origin</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}</stringProp>
                  </elementProp>
                  <elementProp name="Content-Type" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/json; charset=UTF-8</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate, br</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT ${StartRCRange}0.0; Win${StartMonth}4; x${StartMonth}4) AppleWebKit/53${EndMonth}.3${StartMonth} (KHTML, like Gecko) Chrome/${EndMonth}0.0.3538.${StartRCRange}02 Safari/53${EndMonth}.3${StartMonth}</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
              <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="RegExp_rpc_EndDate5" enabled="true">
                <stringProp name="RegexExtractor.useHeaders">false</stringProp>
                <stringProp name="RegexExtractor.refname">rpc_EndDate5</stringProp>
                <stringProp name="RegexExtractor.regex">\&quot;id\&quot;\:\&quot;(\d+)\&quot;\,\&quot;locale\&quot;\:\&quot;en_US\&quot;\,\&quot;format\&quot;</stringProp>
                <stringProp name="RegexExtractor.template">$1$</stringProp>
                <stringProp name="RegexExtractor.default">No_rpc_EdDate4</stringProp>
                <stringProp name="RegexExtractor.match_number">1</stringProp>
              </RegexExtractor>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/vaadin/UIDL/Selecting Day, Month and year for End date range" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">{&quot;csrfToken&quot;:&quot;${key}&quot;,&quot;rpc&quot;:[[&quot;${rpc_EndDate5}&quot;,&quot;v&quot;,&quot;v&quot;,[&quot;year&quot;,[&quot;i&quot;,${EndRCRange}${StartRCRange}${EndMonth}]]],[&quot;${rpc_EndDate5}&quot;,&quot;v&quot;,&quot;v&quot;,[&quot;month&quot;,[&quot;i&quot;,${EndMonth}]]],[&quot;${rpc_EndDate5}&quot;,&quot;v&quot;,&quot;v&quot;,[&quot;day&quot;,[&quot;i&quot;,${EndDate}]]]],&quot;syncId&quot;:12,&quot;clientId&quot;:12}</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain">${g3UrlDomainName}</stringProp>
              <stringProp name="HTTPSampler.port">${g3Port}</stringProp>
              <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/vaadin/UIDL/?v-uiId=${vuid}</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">true</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}/solutions/multiproperty-rate-plan-report?r=${StartMonth}${StartMonth}82</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
                  </elementProp>
                  <elementProp name="Origin" elementType="Header">
                    <stringProp name="Header.name">Origin</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}</stringProp>
                  </elementProp>
                  <elementProp name="Content-Type" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/json; charset=UTF-8</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate, br</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT ${StartRCRange}0.0; Win${StartMonth}4; x${StartMonth}4) AppleWebKit/53${EndMonth}.3${StartMonth} (KHTML, like Gecko) Chrome/${EndMonth}0.0.3538.${StartRCRange}02 Safari/53${EndMonth}.3${StartMonth}</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/api/systemDiagnostics/keepAlive" enabled="true">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="_" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">_</stringProp>
                    <stringProp name="Argument.value">${StartRCRange}542${StartMonth}0${EndMonth}0${EndMonth}3${StartMonth}59</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain">${g3UrlDomainName}</stringProp>
              <stringProp name="HTTPSampler.port">${g3Port}</stringProp>
              <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/solutions/api/systemDiagnostics/keepAlive</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">true</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}/solutions/multiproperty-rate-plan-report?r=${StartMonth}${StartMonth}82</stringProp>
                  </elementProp>
                  <elementProp name="clientId" elementType="Header">
                    <stringProp name="Header.name">clientId</stringProp>
                    <stringProp name="Header.value">${StartMonth}</stringProp>
                  </elementProp>
                  <elementProp name="clientCode" elementType="Header">
                    <stringProp name="Header.name">clientCode</stringProp>
                    <stringProp name="Header.value">${GeminiClientCode}</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
                  </elementProp>
                  <elementProp name="X-Requested-With" elementType="Header">
                    <stringProp name="Header.name">X-Requested-With</stringProp>
                    <stringProp name="Header.value">XMLHttpRequest</stringProp>
                  </elementProp>
                  <elementProp name="propertyId" elementType="Header">
                    <stringProp name="Header.name">propertyId</stringProp>
                    <stringProp name="Header.value">${GeminiPropertyId}</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate, br</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">application/json, text/javascript, */*; q=0.0${StartRCRange}</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT ${StartRCRange}0.0; Win${StartMonth}4; x${StartMonth}4) AppleWebKit/53${EndMonth}.3${StartMonth} (KHTML, like Gecko) Chrome/${EndMonth}0.0.3538.${StartRCRange}02 Safari/53${EndMonth}.3${StartMonth}</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/rest/sync" enabled="false">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="_" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">_</stringProp>
                    <stringProp name="Argument.value">${StartRCRange}542${StartMonth}0${EndMonth}0${EndMonth}3${StartMonth}49</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain">${g3UrlDomainName}</stringProp>
              <stringProp name="HTTPSampler.port">${g3Port}</stringProp>
              <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
              <stringProp name="HTTPSampler.contentEncoding"></stringProp>
              <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/sync</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">true</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}/solutions/multiproperty-rate-plan-report?r=${StartMonth}${StartMonth}82</stringProp>
                  </elementProp>
                  <elementProp name="clientId" elementType="Header">
                    <stringProp name="Header.name">clientId</stringProp>
                    <stringProp name="Header.value">${StartMonth}</stringProp>
                  </elementProp>
                  <elementProp name="clientCode" elementType="Header">
                    <stringProp name="Header.name">clientCode</stringProp>
                    <stringProp name="Header.value">${GeminiClientCode}</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
                  </elementProp>
                  <elementProp name="X-Requested-With" elementType="Header">
                    <stringProp name="Header.name">X-Requested-With</stringProp>
                    <stringProp name="Header.value">XMLHttpRequest</stringProp>
                  </elementProp>
                  <elementProp name="propertyId" elementType="Header">
                    <stringProp name="Header.name">propertyId</stringProp>
                    <stringProp name="Header.value">${GeminiPropertyId}</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate, br</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">application/json, text/javascript, */*; q=0.0${StartRCRange}</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT ${StartRCRange}0.0; Win${StartMonth}4; x${StartMonth}4) AppleWebKit/53${EndMonth}.3${StartMonth} (KHTML, like Gecko) Chrome/${EndMonth}0.0.3538.${StartRCRange}02 Safari/53${EndMonth}.3${StartMonth}</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/vaadin/UIDL/Date Range Selected" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">{&quot;csrfToken&quot;:&quot;${key}&quot;,&quot;rpc&quot;:[[&quot;${rpc_EndDate6}&quot;,&quot;org.vaadin.hene.popupbutton.widgetset.client.ui.PopupButtonServerRpc&quot;,&quot;setPopupVisible&quot;,[true]],[&quot;${rpc_EndDate6}&quot;,&quot;com.vaadin.shared.ui.button.ButtonServerRpc&quot;,&quot;disableOnClick&quot;,[]],[&quot;${rpc_EndDate6}&quot;,&quot;com.vaadin.shared.ui.button.ButtonServerRpc&quot;,&quot;click&quot;,[{&quot;altKey&quot;:false,&quot;button&quot;:&quot;LEFT&quot;,&quot;clientX&quot;:55${EndMonth},&quot;clientY&quot;:525,&quot;ctrlKey&quot;:false,&quot;metaKey&quot;:false,&quot;relativeX&quot;:4${StartRCRange},&quot;relativeY&quot;:${StartRCRange}5,&quot;shiftKey&quot;:false,&quot;type&quot;:${StartRCRange}}]]],&quot;syncId&quot;:13,&quot;clientId&quot;:13}</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain">${g3UrlDomainName}</stringProp>
              <stringProp name="HTTPSampler.port">${g3Port}</stringProp>
              <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/vaadin/UIDL/?v-uiId=${vuid}</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">true</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}/solutions/multiproperty-rate-plan-report?r=${StartMonth}${StartMonth}82</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
                  </elementProp>
                  <elementProp name="Origin" elementType="Header">
                    <stringProp name="Header.name">Origin</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}</stringProp>
                  </elementProp>
                  <elementProp name="Content-Type" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/json; charset=UTF-8</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate, br</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT ${StartRCRange}0.0; Win${StartMonth}4; x${StartMonth}4) AppleWebKit/53${EndMonth}.3${StartMonth} (KHTML, like Gecko) Chrome/${EndMonth}0.0.3538.${StartRCRange}02 Safari/53${EndMonth}.3${StartMonth}</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
              <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="RegExp_rpc_EndDate7" enabled="true">
                <stringProp name="RegexExtractor.useHeaders">false</stringProp>
                <stringProp name="RegexExtractor.refname">rpc_EndDate7</stringProp>
                <stringProp name="RegexExtractor.regex"> \&quot;pid\&quot;\:\&quot;(\d+)\&quot;</stringProp>
                <stringProp name="RegexExtractor.template">$1$</stringProp>
                <stringProp name="RegexExtractor.default">No_rpc_EdDate6</stringProp>
                <stringProp name="RegexExtractor.match_number">1</stringProp>
              </RegexExtractor>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/vaadin/UIDL/Date Range Selected" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">{&quot;csrfToken&quot;:&quot;${key}&quot;,&quot;rpc&quot;:[[&quot;${rpc_EndDate7}&quot;,&quot;com.vaadin.shared.ui.ui.UIServerRpc&quot;,&quot;resize&quot;,[${StartRCRange}499,${EndMonth}4${EndMonth},${StartRCRange}499,2${EndDate}]]],&quot;syncId&quot;:14,&quot;clientId&quot;:14}</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain">${g3UrlDomainName}</stringProp>
              <stringProp name="HTTPSampler.port">${g3Port}</stringProp>
              <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/vaadin/UIDL/?v-uiId=${vuid}</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">true</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}/solutions/multiproperty-rate-plan-report?r=${StartMonth}${StartMonth}82</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
                  </elementProp>
                  <elementProp name="Origin" elementType="Header">
                    <stringProp name="Header.name">Origin</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}</stringProp>
                  </elementProp>
                  <elementProp name="Content-Type" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/json; charset=UTF-8</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate, br</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT ${StartRCRange}0.0; Win${StartMonth}4; x${StartMonth}4) AppleWebKit/53${EndMonth}.3${StartMonth} (KHTML, like Gecko) Chrome/${EndMonth}0.0.3538.${StartRCRange}02 Safari/53${EndMonth}.3${StartMonth}</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
          </hashTree>
          <TransactionController guiclass="TransactionControllerGui" testclass="TransactionController" testname="SelectMultiPropertyGroups" enabled="true">
            <boolProp name="TransactionController.includeTimers">false</boolProp>
            <boolProp name="TransactionController.parent">true</boolProp>
          </TransactionController>
          <hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/vaadin/UIDL/property Groups selection popup" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">{&quot;csrfToken&quot;:&quot;${key}&quot;,&quot;rpc&quot;:[[&quot;${rpcReport1}&quot;,&quot;org.vaadin.hene.popupbutton.widgetset.client.ui.PopupButtonServerRpc&quot;,&quot;setPopupVisible&quot;,[true]],[&quot;${rpcReport1}&quot;,&quot;com.vaadin.shared.ui.button.ButtonServerRpc&quot;,&quot;click&quot;,[{&quot;altKey&quot;:false,&quot;button&quot;:&quot;LEFT&quot;,&quot;clientX&quot;:71,&quot;clientY&quot;:166,&quot;ctrlKey&quot;:false,&quot;metaKey&quot;:false,&quot;relativeX&quot;:51,&quot;relativeY&quot;:21,&quot;shiftKey&quot;:false,&quot;type&quot;:1}]]],&quot;syncId&quot;:15},&quot;clientId&quot;:15}</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain">${g3UrlDomainName}</stringProp>
              <stringProp name="HTTPSampler.port">${g3Port}</stringProp>
              <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/vaadin/UIDL/?v-uiId=${vuid}</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">true</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}/solutions/multiproperty-rate-plan-report</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
                  </elementProp>
                  <elementProp name="Origin" elementType="Header">
                    <stringProp name="Header.name">Origin</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}</stringProp>
                  </elementProp>
                  <elementProp name="Content-Type" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/json; charset=UTF-8</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.77 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
              <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="RegExp_RPC_Report2" enabled="true">
                <stringProp name="RegexExtractor.useHeaders">false</stringProp>
                <stringProp name="RegexExtractor.refname">rpcReport2</stringProp>
                <stringProp name="RegexExtractor.regex">\&quot;id\&quot;\:\&quot;(\d+)\&quot;\,\&quot;type\&quot;\:\&quot;optiongroup\&quot;</stringProp>
                <stringProp name="RegexExtractor.template">$1$</stringProp>
                <stringProp name="RegexExtractor.default">no_rpcReport2</stringProp>
                <stringProp name="RegexExtractor.match_number">1</stringProp>
              </RegexExtractor>
              <hashTree/>
              <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="RegExp_RPC_Report5" enabled="true">
                <stringProp name="RegexExtractor.useHeaders">false</stringProp>
                <stringProp name="RegexExtractor.refname">rpcReport5</stringProp>
                <stringProp name="RegexExtractor.regex">\: Click to View\&quot;\}\,\&quot;(\d+)\&quot;</stringProp>
                <stringProp name="RegexExtractor.template">$1$</stringProp>
                <stringProp name="RegexExtractor.default">no_rpcReport5</stringProp>
                <stringProp name="RegexExtractor.match_number">1</stringProp>
              </RegexExtractor>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/vaadin/UIDL/?property selected" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">{&quot;csrfToken&quot;:&quot;${key}&quot;,&quot;rpc&quot;:[[&quot;${rpcReport2}&quot;,&quot;v&quot;,&quot;v&quot;,[&quot;selected&quot;,[&quot;S&quot;,[&quot;2&quot;]]]]],&quot;syncId&quot;:16,&quot;clientId&quot;:16}</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain">${g3UrlDomainName}</stringProp>
              <stringProp name="HTTPSampler.port">${g3Port}</stringProp>
              <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/vaadin/UIDL/?v-uiId=${vuid}</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">true</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}/solutions/multiproperty-rate-plan-report</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
                  </elementProp>
                  <elementProp name="Origin" elementType="Header">
                    <stringProp name="Header.name">Origin</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}</stringProp>
                  </elementProp>
                  <elementProp name="Content-Type" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/json; charset=UTF-8</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.77 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
              <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="RegExp_rpcGropuname" enabled="true">
                <stringProp name="RegexExtractor.useHeaders">false</stringProp>
                <stringProp name="RegexExtractor.refname">rpcGroupReport</stringProp>
                <stringProp name="RegexExtractor.regex">\&quot;caption\&quot;\:\&quot;${gropuName}\&quot;\,\&quot;key\&quot;\:\&quot;(\d+)\&quot;</stringProp>
                <stringProp name="RegexExtractor.template">$1$</stringProp>
                <stringProp name="RegexExtractor.default">no_rpcGroupReport</stringProp>
                <stringProp name="RegexExtractor.match_number">1</stringProp>
              </RegexExtractor>
              <hashTree/>
              <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="RegExp_RPC_Report3" enabled="true">
                <stringProp name="RegexExtractor.useHeaders">false</stringProp>
                <stringProp name="RegexExtractor.refname">rpcReport3</stringProp>
                <stringProp name="RegexExtractor.regex">\&quot;id\&quot;\:\&quot;(\d+)&quot;\,\&quot;type\&quot;\:\&quot;list\&quot;\,\&quot;selectmode\&quot;</stringProp>
                <stringProp name="RegexExtractor.template">$1$</stringProp>
                <stringProp name="RegexExtractor.default">no_rpcReport3</stringProp>
                <stringProp name="RegexExtractor.match_number">1</stringProp>
              </RegexExtractor>
              <hashTree/>
              <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="RegExp_RPC_Report4" enabled="true">
                <stringProp name="RegexExtractor.useHeaders">false</stringProp>
                <stringProp name="RegexExtractor.refname">rpcReport4</stringProp>
                <stringProp name="RegexExtractor.regex">\{\&quot;width\&quot;\:\&quot;50\.0px\&quot;\}\,\&quot;(\d+)\&quot;</stringProp>
                <stringProp name="RegexExtractor.template">$1$</stringProp>
                <stringProp name="RegexExtractor.default">no_rpcReport3</stringProp>
                <stringProp name="RegexExtractor.match_number">1</stringProp>
              </RegexExtractor>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/vaadin/UIDL/Selection of property Groups" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">{&quot;csrfToken&quot;:&quot;${key}&quot;,&quot;rpc&quot;:[[&quot;${rpcReport3}&quot;,&quot;v&quot;,&quot;v&quot;,[&quot;selected&quot;,[&quot;S&quot;,[&quot;${rpcGroupReport}&quot;]]]]],&quot;syncId&quot;:17,&quot;clientId&quot;:17}</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain">${g3UrlDomainName}</stringProp>
              <stringProp name="HTTPSampler.port">${g3Port}</stringProp>
              <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/vaadin/UIDL/?v-uiId=${vuid}</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">true</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}/solutions/multiproperty-rate-plan-report</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
                  </elementProp>
                  <elementProp name="Origin" elementType="Header">
                    <stringProp name="Header.name">Origin</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}</stringProp>
                  </elementProp>
                  <elementProp name="Content-Type" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/json; charset=UTF-8</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.77 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/vaadin/UIDL/Apply the selection" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">{&quot;csrfToken&quot;:&quot;${key}&quot;,&quot;rpc&quot;:[[&quot;${rpcReport4}&quot;,&quot;com.vaadin.shared.ui.button.ButtonServerRpc&quot;,&quot;click&quot;,[{&quot;altKey&quot;:false,&quot;button&quot;:&quot;LEFT&quot;,&quot;clientX&quot;:399,&quot;clientY&quot;:310,&quot;ctrlKey&quot;:false,&quot;metaKey&quot;:false,&quot;relativeX&quot;:21,&quot;relativeY&quot;:14,&quot;shiftKey&quot;:false,&quot;type&quot;:1}]]],&quot;syncId&quot;:18,&quot;clientId&quot;:18}</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain">${g3UrlDomainName}</stringProp>
              <stringProp name="HTTPSampler.port">${g3Port}</stringProp>
              <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/vaadin/UIDL/?v-uiId=${vuid}</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">true</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}/solutions/multiproperty-rate-plan-report</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
                  </elementProp>
                  <elementProp name="Origin" elementType="Header">
                    <stringProp name="Header.name">Origin</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}</stringProp>
                  </elementProp>
                  <elementProp name="Content-Type" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/json; charset=UTF-8</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.77 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
          </hashTree>
          <TransactionController guiclass="TransactionControllerGui" testclass="TransactionController" testname="Fetch_Report" enabled="true">
            <boolProp name="TransactionController.includeTimers">false</boolProp>
            <boolProp name="TransactionController.parent">true</boolProp>
          </TransactionController>
          <hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/vaadin/UIDL/Multi property rate plan report for multiple groups selection" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">{&quot;csrfToken&quot;:&quot;${key}&quot;,&quot;rpc&quot;:[[&quot;${rpcReport5}&quot;,&quot;org.vaadin.hene.popupbutton.widgetset.client.ui.PopupButtonServerRpc&quot;,&quot;setPopupVisible&quot;,[true]],[&quot;${rpcReport5}&quot;,&quot;com.vaadin.shared.ui.button.ButtonServerRpc&quot;,&quot;disableOnClick&quot;,[]],[&quot;${rpcReport5}&quot;,&quot;com.vaadin.shared.ui.button.ButtonServerRpc&quot;,&quot;click&quot;,[{&quot;altKey&quot;:false,&quot;button&quot;:&quot;LEFT&quot;,&quot;clientX&quot;:628,&quot;clientY&quot;:536,&quot;ctrlKey&quot;:false,&quot;metaKey&quot;:false,&quot;relativeX&quot;:6,&quot;relativeY&quot;:18,&quot;shiftKey&quot;:false,&quot;type&quot;:1}]]],&quot;syncId&quot;:19,&quot;clientId&quot;:19}</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain">${g3UrlDomainName}</stringProp>
              <stringProp name="HTTPSampler.port">${g3Port}</stringProp>
              <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/vaadin/UIDL/?v-uiId=${vuid}</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">true</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}/solutions/multiproperty-rate-plan-report</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
                  </elementProp>
                  <elementProp name="Origin" elementType="Header">
                    <stringProp name="Header.name">Origin</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}</stringProp>
                  </elementProp>
                  <elementProp name="Content-Type" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/json; charset=UTF-8</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.77 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/vaadin/UIDL/Multi property rate plan report" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">{&quot;csrfToken&quot;:&quot;${key}&quot;,&quot;rpc&quot;:[[&quot;${rpcReport6}&quot;,&quot;com.vaadin.shared.data.DataRequestRpc&quot;,&quot;requestRows&quot;,[40,25,0,40]]],&quot;syncId&quot;:20,&quot;clientId&quot;:20}</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain">${g3UrlDomainName}</stringProp>
              <stringProp name="HTTPSampler.port">${g3Port}</stringProp>
              <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
              <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
              <stringProp name="HTTPSampler.path">/vaadin/UIDL/?v-uiId=${vuid}</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">true</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}/solutions/multiproperty-rate-plan-report</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.9</stringProp>
                  </elementProp>
                  <elementProp name="Origin" elementType="Header">
                    <stringProp name="Header.name">Origin</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}:${g3Port}</stringProp>
                  </elementProp>
                  <elementProp name="Content-Type" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/json; charset=UTF-8</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.77 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
          </hashTree>
          <TransactionController guiclass="TransactionControllerGui" testclass="TransactionController" testname="Logout" enabled="true">
            <boolProp name="TransactionController.parent">true</boolProp>
            <boolProp name="TransactionController.includeTimers">false</boolProp>
          </TransactionController>
          <hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/api/auth/logout" enabled="true">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments"/>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding"></stringProp>
              <stringProp name="HTTPSampler.path">/solutions/api/auth/logout/</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">false</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
              <stringProp name="TestPlan.comments">Detected the start of a redirect chain</stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2062.124 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip,deflate</stringProp>
                  </elementProp>
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">https://stageg3.ideas.com/solutions/information-manager</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/" enabled="false">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="postLogin" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.name">postLogin</stringProp>
                    <stringProp name="Argument.value">false</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding"></stringProp>
              <stringProp name="HTTPSampler.path">/solutions/</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">false</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
              <stringProp name="TestPlan.comments">Detected the start of a redirect chain</stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2062.124 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip,deflate</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/api/auth/login" enabled="true">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments"/>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding">utf-8</stringProp>
              <stringProp name="HTTPSampler.path">/solutions/api/auth/login</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2062.124 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip,deflate</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/support/rest/announcement/getAllAnnouncements" enabled="true">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments"/>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding"></stringProp>
              <stringProp name="HTTPSampler.path">/support/rest/announcement/getAllAnnouncements</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">*/*</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2062.124 Safari/537.36</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip,deflate</stringProp>
                  </elementProp>
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">https://stageg3.ideas.com/solutions/api/auth/login</stringProp>
                  </elementProp>
                  <elementProp name="X-Requested-With" elementType="Header">
                    <stringProp name="Header.name">X-Requested-With</stringProp>
                    <stringProp name="Header.value">XMLHttpRequest</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
          </hashTree>
        </hashTree>
      </hashTree>
      <ResultCollector guiclass="ViewResultsFullVisualizer" testclass="ResultCollector" testname="View Results Tree" enabled="true">
        <boolProp name="ResultCollector.error_logging">false</boolProp>
        <objProp>
          <name>saveConfig</name>
          <value class="SampleSaveConfiguration">
            <time>true</time>
            <latency>true</latency>
            <timestamp>true</timestamp>
            <success>true</success>
            <label>true</label>
            <code>true</code>
            <message>true</message>
            <threadName>true</threadName>
            <dataType>true</dataType>
            <encoding>false</encoding>
            <assertions>true</assertions>
            <subresults>true</subresults>
            <responseData>false</responseData>
            <samplerData>false</samplerData>
            <xml>false</xml>
            <fieldNames>true</fieldNames>
            <responseHeaders>false</responseHeaders>
            <requestHeaders>false</requestHeaders>
            <responseDataOnError>false</responseDataOnError>
            <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
            <assertionsResultsToSave>0</assertionsResultsToSave>
            <bytes>true</bytes>
            <sentBytes>true</sentBytes>
            <url>true</url>
            <threadCounts>true</threadCounts>
            <idleTime>true</idleTime>
            <connectTime>true</connectTime>
          </value>
        </objProp>
        <stringProp name="filename"></stringProp>
      </ResultCollector>
      <hashTree/>
      <ProxyControl guiclass="ProxyControlGui" testclass="ProxyControl" testname="HTTP(S) Test Script Recorder" enabled="true">
        <stringProp name="ProxyControlGui.port">6666</stringProp>
        <collectionProp name="ProxyControlGui.exclude_list"/>
        <collectionProp name="ProxyControlGui.include_list"/>
        <boolProp name="ProxyControlGui.capture_http_headers">true</boolProp>
        <intProp name="ProxyControlGui.grouping_mode">1</intProp>
        <boolProp name="ProxyControlGui.add_assertion">false</boolProp>
        <stringProp name="ProxyControlGui.sampler_type_name"></stringProp>
        <boolProp name="ProxyControlGui.sampler_redirect_automatically">false</boolProp>
        <boolProp name="ProxyControlGui.sampler_follow_redirects">true</boolProp>
        <boolProp name="ProxyControlGui.use_keepalive">true</boolProp>
        <boolProp name="ProxyControlGui.sampler_download_images">true</boolProp>
        <intProp name="ProxyControlGui.proxy_http_sampler_naming_mode">0</intProp>
        <stringProp name="ProxyControlGui.proxy_prefix_http_sampler_name"></stringProp>
        <stringProp name="ProxyControlGui.proxy_pause_http_sampler"></stringProp>
        <boolProp name="ProxyControlGui.notify_child_sl_filtered">false</boolProp>
        <boolProp name="ProxyControlGui.regex_match">false</boolProp>
        <stringProp name="ProxyControlGui.content_type_include"></stringProp>
        <stringProp name="ProxyControlGui.content_type_exclude"></stringProp>
      </ProxyControl>
      <hashTree/>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
