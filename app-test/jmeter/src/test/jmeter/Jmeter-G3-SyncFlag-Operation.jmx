<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2" properties="2.6" jmeter="2.10 r1533061">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="G3PerformancePlan" enabled="true">
      <stringProp name="TestPlan.comments"></stringProp>
      <boolProp name="TestPlan.functional_mode">false</boolProp>
      <boolProp name="TestPlan.serialize_threadgroups">false</boolProp>
      <elementProp name="TestPlan.user_defined_variables" elementType="Arguments" guiclass="ArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
        <collectionProp name="Arguments.arguments">
          <elementProp name="jmeterThreads" elementType="Argument">
            <stringProp name="Argument.name">jmeterThreads</stringProp>
            <stringProp name="Argument.value">${__P(jmeterThreads)}</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
          <elementProp name="jmeterRampUpTime" elementType="Argument">
            <stringProp name="Argument.name">jmeterRampUpTime</stringProp>
            <stringProp name="Argument.value">${__P(jmeterRampUpTime)}</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
          <elementProp name="jmeterLoopCount" elementType="Argument">
            <stringProp name="Argument.name">jmeterLoopCount</stringProp>
            <stringProp name="Argument.value">${__P(jmeterLoopCount)}</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
          <elementProp name="jmeterThreads" elementType="Argument">
            <stringProp name="Argument.name">jmeterThreads</stringProp>
            <stringProp name="Argument.value">${__P(jmeterThreads)}</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
          <elementProp name="jmeterRampUpTime" elementType="Argument">
            <stringProp name="Argument.name">jmeterRampUpTime</stringProp>
            <stringProp name="Argument.value">${__P(jmeterRampUpTime)}</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
          <elementProp name="jmeterLoopCount" elementType="Argument">
            <stringProp name="Argument.name">jmeterLoopCount</stringProp>
            <stringProp name="Argument.value">${__P(jmeterLoopCount)}</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
        </collectionProp>
      </elementProp>
      <stringProp name="TestPlan.user_define_classpath"></stringProp>
    </TestPlan>
    <hashTree>
      <Arguments guiclass="ArgumentsPanel" testclass="Arguments" testname="EnableLocal" enabled="true">
        <collectionProp name="Arguments.arguments">
          <elementProp name="jmeterThreads" elementType="Argument">
            <stringProp name="Argument.name">jmeterThreads</stringProp>
            <stringProp name="Argument.value">2</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
          <elementProp name="jmeterRampUpTime" elementType="Argument">
            <stringProp name="Argument.name">jmeterRampUpTime</stringProp>
            <stringProp name="Argument.value">2</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
          <elementProp name="jmeterLoopCount" elementType="Argument">
            <stringProp name="Argument.name">jmeterLoopCount</stringProp>
            <stringProp name="Argument.value">1</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
          </elementProp>
        </collectionProp>
      </Arguments>
      <hashTree/>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="G3PerformanceThreads" enabled="true">
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControlPanel" testclass="LoopController" testname="Loop Controller" enabled="true">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <stringProp name="LoopController.loops">${jmeterLoopCount}</stringProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">${jmeterThreads}</stringProp>
        <stringProp name="ThreadGroup.ramp_time">${jmeterRampUpTime}</stringProp>
        <longProp name="ThreadGroup.start_time">1364931679000</longProp>
        <longProp name="ThreadGroup.end_time">1364931679000</longProp>
        <boolProp name="ThreadGroup.scheduler">false</boolProp>
        <stringProp name="ThreadGroup.duration"></stringProp>
        <stringProp name="ThreadGroup.delay"></stringProp>
      </ThreadGroup>
      <hashTree>
        <Arguments guiclass="ArgumentsPanel" testclass="Arguments" testname="G3PassedProperties" enabled="true">
          <collectionProp name="Arguments.arguments">
            <elementProp name="g3UrlProtocol" elementType="Argument">
              <stringProp name="Argument.name">g3UrlProtocol</stringProp>
              <stringProp name="Argument.value">${__P(g3UrlProtocol)}</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="g3UrlDomainName" elementType="Argument">
              <stringProp name="Argument.name">g3UrlDomainName</stringProp>
              <stringProp name="Argument.value">${__P(g3UrlDomainName)}</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="g3Port" elementType="Argument">
              <stringProp name="Argument.name">g3Port</stringProp>
              <stringProp name="Argument.value">${__P(g3Port)}</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="g3BinaryDir" elementType="Argument">
              <stringProp name="Argument.name">g3BinaryDir</stringProp>
              <stringProp name="Argument.value">${__P(g3BinaryDir)}</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="g3PropertyList" elementType="Argument">
              <stringProp name="Argument.name">g3PropertyList</stringProp>
              <stringProp name="Argument.value">${__P(g3PropertyList)}</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
          </collectionProp>
        </Arguments>
        <hashTree/>
        <Arguments guiclass="ArgumentsPanel" testclass="Arguments" testname="EnableLocal" enabled="true">
          <collectionProp name="Arguments.arguments">
            <elementProp name="g3UrlProtocol" elementType="Argument">
              <stringProp name="Argument.name">g3UrlProtocol</stringProp>
              <stringProp name="Argument.value">http</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="g3UrlDomainName" elementType="Argument">
              <stringProp name="Argument.name">g3UrlDomainName</stringProp>
              <stringProp name="Argument.value">us1s3ap01.ideasstg.int</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="g3Port" elementType="Argument">
              <stringProp name="Argument.name">g3Port</stringProp>
              <stringProp name="Argument.value">8080</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="g3BinaryDir" elementType="Argument">
              <stringProp name="Argument.name">g3BinaryDir</stringProp>
              <stringProp name="Argument.value">C:\Tetris\test\jmeter\src\test\binaries</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
            <elementProp name="g3PropertyList" elementType="Argument">
              <stringProp name="Argument.name">g3PropertyList</stringProp>
              <stringProp name="Argument.value">C:\Tetris\test\jmeter\src\test\properties\G3StressboxCRSProcessing-62.csv</stringProp>
              <stringProp name="Argument.metadata">=</stringProp>
            </elementProp>
          </collectionProp>
        </Arguments>
        <hashTree/>
        <CSVDataSet guiclass="TestBeanGUI" testclass="CSVDataSet" testname="G3PropertiesFile" enabled="true">
          <stringProp name="filename">${g3PropertyList}</stringProp>
          <stringProp name="fileEncoding"></stringProp>
          <stringProp name="variableNames">Execute,propertyID,g3UserName,g3UserPassword,clientCode,clientID,propertyCode,g3CRSExtractFile</stringProp>
          <stringProp name="delimiter">,</stringProp>
          <boolProp name="quotedData">true</boolProp>
          <boolProp name="recycle">true</boolProp>
          <boolProp name="stopThread">true</boolProp>
          <stringProp name="shareMode">shareMode.all</stringProp>
          <stringProp name="TestPlan.comments">${__P(jmeterSourceDir)}\binaries\${__P(jmeterUserList)}</stringProp>
        </CSVDataSet>
        <hashTree/>
        <IfController guiclass="IfControllerPanel" testclass="IfController" testname="G3PropertyExecute" enabled="true">
          <stringProp name="IfController.condition">&apos;${Execute}&apos; == &apos;Yes&apos;</stringProp>
          <boolProp name="IfController.evaluateAll">false</boolProp>
        </IfController>
        <hashTree>
          <ResultCollector guiclass="ViewResultsFullVisualizer" testclass="ResultCollector" testname="View Results Tree" enabled="true">
            <boolProp name="ResultCollector.error_logging">false</boolProp>
            <objProp>
              <value class="SampleSaveConfiguration">
                <time>true</time>
                <latency>true</latency>
                <timestamp>true</timestamp>
                <success>true</success>
                <label>true</label>
                <code>true</code>
                <message>true</message>
                <threadName>true</threadName>
                <dataType>true</dataType>
                <encoding>false</encoding>
                <assertions>true</assertions>
                <subresults>true</subresults>
                <responseData>false</responseData>
                <samplerData>false</samplerData>
                <xml>false</xml>
                <fieldNames>false</fieldNames>
                <responseHeaders>false</responseHeaders>
                <requestHeaders>false</requestHeaders>
                <responseDataOnError>false</responseDataOnError>
                <saveAssertionResultsFailureMessage>false</saveAssertionResultsFailureMessage>
                <assertionsResultsToSave>0</assertionsResultsToSave>
                <bytes>true</bytes>
              </value>
            </objProp>
            <objProp>
              <value class="SampleSaveConfiguration">
                <time>true</time>
                <latency>true</latency>
                <timestamp>true</timestamp>
                <success>true</success>
                <label>true</label>
                <code>true</code>
                <message>true</message>
                <threadName>true</threadName>
                <dataType>true</dataType>
                <encoding>false</encoding>
                <assertions>true</assertions>
                <subresults>true</subresults>
                <responseData>false</responseData>
                <samplerData>false</samplerData>
                <xml>true</xml>
                <fieldNames>false</fieldNames>
                <responseHeaders>false</responseHeaders>
                <requestHeaders>false</requestHeaders>
                <responseDataOnError>false</responseDataOnError>
                <saveAssertionResultsFailureMessage>false</saveAssertionResultsFailureMessage>
                <assertionsResultsToSave>0</assertionsResultsToSave>
                <bytes>true</bytes>
              </value>
            </objProp>
            <stringProp name="filename"></stringProp>
          </ResultCollector>
          <hashTree/>
          <ResultCollector guiclass="SummaryReport" testclass="ResultCollector" testname="Summary Report" enabled="true">
            <boolProp name="ResultCollector.error_logging">false</boolProp>
            <objProp>
              <value class="SampleSaveConfiguration">
                <time>true</time>
                <latency>true</latency>
                <timestamp>true</timestamp>
                <success>true</success>
                <label>true</label>
                <code>true</code>
                <message>true</message>
                <threadName>true</threadName>
                <dataType>true</dataType>
                <encoding>false</encoding>
                <assertions>true</assertions>
                <subresults>true</subresults>
                <responseData>false</responseData>
                <samplerData>false</samplerData>
                <xml>false</xml>
                <fieldNames>false</fieldNames>
                <responseHeaders>false</responseHeaders>
                <requestHeaders>false</requestHeaders>
                <responseDataOnError>false</responseDataOnError>
                <saveAssertionResultsFailureMessage>false</saveAssertionResultsFailureMessage>
                <assertionsResultsToSave>0</assertionsResultsToSave>
                <bytes>true</bytes>
              </value>
            </objProp>
            <objProp>
              <value class="SampleSaveConfiguration">
                <time>true</time>
                <latency>true</latency>
                <timestamp>true</timestamp>
                <success>true</success>
                <label>true</label>
                <code>true</code>
                <message>true</message>
                <threadName>true</threadName>
                <dataType>true</dataType>
                <encoding>false</encoding>
                <assertions>true</assertions>
                <subresults>true</subresults>
                <responseData>false</responseData>
                <samplerData>false</samplerData>
                <xml>true</xml>
                <fieldNames>false</fieldNames>
                <responseHeaders>false</responseHeaders>
                <requestHeaders>false</requestHeaders>
                <responseDataOnError>false</responseDataOnError>
                <saveAssertionResultsFailureMessage>false</saveAssertionResultsFailureMessage>
                <assertionsResultsToSave>0</assertionsResultsToSave>
                <bytes>true</bytes>
              </value>
            </objProp>
            <stringProp name="filename"></stringProp>
          </ResultCollector>
          <hashTree/>
          <ResultCollector guiclass="StatVisualizer" testclass="ResultCollector" testname="Aggregate Report" enabled="true">
            <boolProp name="ResultCollector.error_logging">false</boolProp>
            <objProp>
              <value class="SampleSaveConfiguration">
                <time>true</time>
                <latency>true</latency>
                <timestamp>true</timestamp>
                <success>true</success>
                <label>true</label>
                <code>true</code>
                <message>true</message>
                <threadName>true</threadName>
                <dataType>true</dataType>
                <encoding>false</encoding>
                <assertions>true</assertions>
                <subresults>true</subresults>
                <responseData>false</responseData>
                <samplerData>false</samplerData>
                <xml>false</xml>
                <fieldNames>false</fieldNames>
                <responseHeaders>false</responseHeaders>
                <requestHeaders>false</requestHeaders>
                <responseDataOnError>false</responseDataOnError>
                <saveAssertionResultsFailureMessage>false</saveAssertionResultsFailureMessage>
                <assertionsResultsToSave>0</assertionsResultsToSave>
                <bytes>true</bytes>
              </value>
            </objProp>
            <objProp>
              <value class="SampleSaveConfiguration">
                <time>true</time>
                <latency>true</latency>
                <timestamp>true</timestamp>
                <success>true</success>
                <label>true</label>
                <code>true</code>
                <message>true</message>
                <threadName>true</threadName>
                <dataType>true</dataType>
                <encoding>false</encoding>
                <assertions>true</assertions>
                <subresults>true</subresults>
                <responseData>false</responseData>
                <samplerData>false</samplerData>
                <xml>true</xml>
                <fieldNames>false</fieldNames>
                <responseHeaders>false</responseHeaders>
                <requestHeaders>false</requestHeaders>
                <responseDataOnError>false</responseDataOnError>
                <saveAssertionResultsFailureMessage>false</saveAssertionResultsFailureMessage>
                <assertionsResultsToSave>0</assertionsResultsToSave>
                <bytes>true</bytes>
              </value>
            </objProp>
            <stringProp name="filename"></stringProp>
          </ResultCollector>
          <hashTree/>
          <ConfigTestElement guiclass="HttpDefaultsGui" testclass="ConfigTestElement" testname="HTTP Request Defaults" enabled="true">
            <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
              <collectionProp name="Arguments.arguments"/>
            </elementProp>
            <stringProp name="HTTPSampler.domain">${g3UrlDomainName}</stringProp>
            <stringProp name="HTTPSampler.port">${g3Port}</stringProp>
            <stringProp name="HTTPSampler.connect_timeout"></stringProp>
            <stringProp name="HTTPSampler.response_timeout"></stringProp>
            <stringProp name="HTTPSampler.protocol"></stringProp>
            <stringProp name="HTTPSampler.contentEncoding"></stringProp>
            <stringProp name="HTTPSampler.path"></stringProp>
            <stringProp name="HTTPSampler.concurrentPool">4</stringProp>
          </ConfigTestElement>
          <hashTree/>
          <CookieManager guiclass="CookiePanel" testclass="CookieManager" testname="HTTP Cookie Manager" enabled="true">
            <collectionProp name="CookieManager.cookies"/>
            <boolProp name="CookieManager.clearEachIteration">true</boolProp>
          </CookieManager>
          <hashTree/>
          <CacheManager guiclass="CacheManagerGui" testclass="CacheManager" testname="HTTP Cache Manager" enabled="true">
            <boolProp name="clearEachIteration">false</boolProp>
            <boolProp name="useExpires">false</boolProp>
            <intProp name="maxSize">50000</intProp>
          </CacheManager>
          <hashTree/>
          <BeanShellPreProcessor guiclass="TestBeanGUI" testclass="BeanShellPreProcessor" testname="EnableUserPropertyGroups" enabled="true">
            <boolProp name="resetInterpreter">false</boolProp>
            <stringProp name="parameters"></stringProp>
            <stringProp name="filename"></stringProp>
            <stringProp name="script">if (&quot;True&quot;.equals(vars.get(&quot;Group&quot;))) {
vars.put( &quot;parameter&quot;, &quot;clientId=&quot;+vars.get(&quot;clientID&quot;)+&quot;&amp;propertyId=&quot;+vars.get(&quot;propertyID&quot;)+&quot;&amp;propertyGroupId=&quot;+vars.get(&quot;propertyGroupID&quot;) ); 
vars.put( &quot;parameter1&quot;,&quot;propertyGroupId&quot;);
vars.put( &quot;parameter1val&quot;,vars.get(&quot;propertyGroupID&quot;));
}
else{
vars.put( &quot;parameter&quot;, &quot;clientId=&quot;+vars.get(&quot;clientID&quot;)+&quot;&amp;propertyId=&quot;+vars.get(&quot;propertyID&quot;) ); 
vars.put( &quot;parameter1&quot;,&quot;propertyId&quot;);
vars.put( &quot;parameter1val&quot;,vars.get(&quot;propertyID&quot;));
}</stringProp>
          </BeanShellPreProcessor>
          <hashTree/>
          <TransactionController guiclass="TransactionControllerGui" testclass="TransactionController" testname="G3ProcessCRS" enabled="true">
            <boolProp name="TransactionController.includeTimers">false</boolProp>
            <boolProp name="TransactionController.parent">false</boolProp>
          </TransactionController>
          <hashTree>
            <TransactionController guiclass="TransactionControllerGui" testclass="TransactionController" testname="Login" enabled="false">
              <boolProp name="TransactionController.parent">true</boolProp>
              <boolProp name="TransactionController.includeTimers">false</boolProp>
            </TransactionController>
            <hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions_Login Page" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments"/>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">http</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/solutions</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip, deflate</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/_Login Page" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments"/>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">http</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/solutions/</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip, deflate</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/api/auth/login_Login Page" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments"/>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">http</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/solutions/api/auth/login</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip, deflate</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/support/rest/announcement/getAllAnnouncements_Login Page" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments"/>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">http</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/support/rest/announcement/getAllAnnouncements</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">*/*</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/api/auth/login</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip, deflate</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/api/auth/login_Post Login" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="username" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">true</boolProp>
                      <stringProp name="Argument.name">username</stringProp>
                      <stringProp name="Argument.value">${g3UserName}</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                    <elementProp name="password" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.name">password</stringProp>
                      <stringProp name="Argument.value">${g3UserPassword}</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                    <elementProp name="submit" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">true</boolProp>
                      <stringProp name="Argument.name">submit</stringProp>
                      <stringProp name="Argument.value">Log In</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
                <stringProp name="HTTPSampler.contentEncoding">utf-8</stringProp>
                <stringProp name="HTTPSampler.path">/solutions/api/auth/login</stringProp>
                <stringProp name="HTTPSampler.method">POST</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="Content-Type" elementType="Header">
                      <stringProp name="Header.name">Content-Type</stringProp>
                      <stringProp name="Header.value">application/x-www-form-urlencoded</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/api/auth/login</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip, deflate</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/rest/workcontext/default/StressBox" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="_" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.name">_</stringProp>
                      <stringProp name="Argument.value">1369903470006</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/workcontext/default/${clientCode}</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">application/json, text/javascript, */*; q=0.01</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/rest/authorization/property_At A glance" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="_" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.name">_</stringProp>
                      <stringProp name="Argument.value">1365037170292</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">http</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/authorization/property</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">application/json, text/javascript, */*; q=0.01</stringProp>
                    </elementProp>
                    <elementProp name="clientCode" elementType="Header">
                      <stringProp name="Header.name">clientCode</stringProp>
                      <stringProp name="Header.value">${clientCode}</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.57 Safari/537.36</stringProp>
                    </elementProp>
                    <elementProp name="propertyId" elementType="Header">
                      <stringProp name="Header.name">${parameter1}</stringProp>
                      <stringProp name="Header.value">${parameter1val}</stringProp>
                    </elementProp>
                    <elementProp name="Cache-Control" elementType="Header">
                      <stringProp name="Header.name">Cache-Control</stringProp>
                      <stringProp name="Header.value">max-age=0</stringProp>
                    </elementProp>
                    <elementProp name="clientId" elementType="Header">
                      <stringProp name="Header.name">clientId</stringProp>
                      <stringProp name="Header.value">${clientID}</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/information-manager" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments"/>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">http</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/solutions/information-manager</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/api/auth/login</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip, deflate</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/rest/authorization/client_information-manager" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="_" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.name">_</stringProp>
                      <stringProp name="Argument.value">1365036923063</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">http</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/authorization/client</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="clientId" elementType="Header">
                      <stringProp name="Header.name">clientId</stringProp>
                      <stringProp name="Header.value">${clientID}</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                    </elementProp>
                    <elementProp name="clientCode" elementType="Header">
                      <stringProp name="Header.name">clientCode</stringProp>
                      <stringProp name="Header.value">${clientCode}</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">application/json, text/javascript, */*; q=0.01</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip, deflate</stringProp>
                    </elementProp>
                    <elementProp name="propertyId" elementType="Header">
                      <stringProp name="Header.name">${parameter1}</stringProp>
                      <stringProp name="Header.value">${parameter1val}</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/api/page/permission/information-manager" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="_" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.name">_</stringProp>
                      <stringProp name="Argument.value">1365036923066</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">http</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/solutions/api/page/permission/information-manager</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="clientId" elementType="Header">
                      <stringProp name="Header.name">clientId</stringProp>
                      <stringProp name="Header.value">${clientID}</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                    </elementProp>
                    <elementProp name="clientCode" elementType="Header">
                      <stringProp name="Header.name">clientCode</stringProp>
                      <stringProp name="Header.value">${clientCode}</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">*/*</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip, deflate</stringProp>
                    </elementProp>
                    <elementProp name="" elementType="Header">
                      <stringProp name="Header.name">${parameter1}</stringProp>
                      <stringProp name="Header.value">${parameter1val}</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/rest/authorization/propertygroup_information-manager" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="_" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.name">_</stringProp>
                      <stringProp name="Argument.value">1365036923065</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">http</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/authorization/propertygroup</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="clientId" elementType="Header">
                      <stringProp name="Header.name">clientId</stringProp>
                      <stringProp name="Header.value">${clientID}</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                    </elementProp>
                    <elementProp name="clientCode" elementType="Header">
                      <stringProp name="Header.name">clientCode</stringProp>
                      <stringProp name="Header.value">${clientCode}</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">application/json, text/javascript, */*; q=0.01</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip, deflate</stringProp>
                    </elementProp>
                    <elementProp name="propertyId" elementType="Header">
                      <stringProp name="Header.name">${parameter1}</stringProp>
                      <stringProp name="Header.value">${parameter1val}</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/api/page/clientlogo/${clientCode}_information-manager" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="_" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.name">_</stringProp>
                      <stringProp name="Argument.value">1365036924151</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">http</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/solutions/api/page/clientlogo/${clientCode}</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="clientId" elementType="Header">
                      <stringProp name="Header.name">clientId</stringProp>
                      <stringProp name="Header.value">${clientID}</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                    </elementProp>
                    <elementProp name="clientCode" elementType="Header">
                      <stringProp name="Header.name">clientCode</stringProp>
                      <stringProp name="Header.value">${clientCode}</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">*/*</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip, deflate</stringProp>
                    </elementProp>
                    <elementProp name="propertyId" elementType="Header">
                      <stringProp name="Header.name">${parameter1}</stringProp>
                      <stringProp name="Header.value">${parameter1val}</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/rest/authorization/property_information-manager" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="_" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.name">_</stringProp>
                      <stringProp name="Argument.value">1365036923064</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">http</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/authorization/property</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="clientId" elementType="Header">
                      <stringProp name="Header.name">clientId</stringProp>
                      <stringProp name="Header.value">${clientID}</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                    </elementProp>
                    <elementProp name="clientCode" elementType="Header">
                      <stringProp name="Header.name">clientCode</stringProp>
                      <stringProp name="Header.value">${clientCode}</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">application/json, text/javascript, */*; q=0.01</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip, deflate</stringProp>
                    </elementProp>
                    <elementProp name="propertyId" elementType="Header">
                      <stringProp name="Header.name">${parameter1}</stringProp>
                      <stringProp name="Header.value">${parameter1val}</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/api/page/menu/information-manager" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="_" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.name">_</stringProp>
                      <stringProp name="Argument.value">1365036924152</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">http</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/solutions/api/page/menu/information-manager</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="clientId" elementType="Header">
                      <stringProp name="Header.name">clientId</stringProp>
                      <stringProp name="Header.value">${clientID}</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                    </elementProp>
                    <elementProp name="clientCode" elementType="Header">
                      <stringProp name="Header.name">clientCode</stringProp>
                      <stringProp name="Header.value">${clientCode}</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">*/*</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip, deflate</stringProp>
                    </elementProp>
                    <elementProp name="propertyId" elementType="Header">
                      <stringProp name="Header.name">${parameter1}</stringProp>
                      <stringProp name="Header.value">${parameter1val}</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/api/page/userMenu/information-manager" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="_" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.name">_</stringProp>
                      <stringProp name="Argument.value">1365036924152</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">http</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/solutions/api/page/userMenu/information-manager</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="clientId" elementType="Header">
                      <stringProp name="Header.name">clientId</stringProp>
                      <stringProp name="Header.value">${clientID}</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                    </elementProp>
                    <elementProp name="clientCode" elementType="Header">
                      <stringProp name="Header.name">clientCode</stringProp>
                      <stringProp name="Header.value">${clientCode}</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">*/*</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip, deflate</stringProp>
                    </elementProp>
                    <elementProp name="propertyId" elementType="Header">
                      <stringProp name="Header.name">${parameter1}</stringProp>
                      <stringProp name="Header.value">${parameter1val}</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/rest/sync_information-manager" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="_" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.name">_</stringProp>
                      <stringProp name="Argument.value">1365036924154</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">http</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/sync</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="clientId" elementType="Header">
                      <stringProp name="Header.name">clientId</stringProp>
                      <stringProp name="Header.value">${clientID}</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                    </elementProp>
                    <elementProp name="clientCode" elementType="Header">
                      <stringProp name="Header.name">clientCode</stringProp>
                      <stringProp name="Header.value">${clientCode}</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">application/json, text/javascript, */*; q=0.01</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip, deflate</stringProp>
                    </elementProp>
                    <elementProp name="propertyId" elementType="Header">
                      <stringProp name="Header.name">${parameter1}</stringProp>
                      <stringProp name="Header.value">${parameter1val}</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/rest/sync/event_information-manager" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.value">{&quot;pageCode&quot;:&quot;information-manager&quot;,&quot;eventType&quot;:&quot;PAGE_LOADED&quot;}</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">http</stringProp>
                <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
                <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/sync/event</stringProp>
                <stringProp name="HTTPSampler.method">POST</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="Content-Type" elementType="Header">
                      <stringProp name="Header.name">Content-Type</stringProp>
                      <stringProp name="Header.value">application/json; charset=UTF-8</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">*/*</stringProp>
                    </elementProp>
                    <elementProp name="clientCode" elementType="Header">
                      <stringProp name="Header.name">clientCode</stringProp>
                      <stringProp name="Header.value">${clientCode}</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                    </elementProp>
                    <elementProp name="propertyId" elementType="Header">
                      <stringProp name="Header.name">propertyId</stringProp>
                      <stringProp name="Header.value">${propertyID}</stringProp>
                    </elementProp>
                    <elementProp name="Pragma" elementType="Header">
                      <stringProp name="Header.name">Pragma</stringProp>
                      <stringProp name="Header.value">no-cache</stringProp>
                    </elementProp>
                    <elementProp name="Cache-Control" elementType="Header">
                      <stringProp name="Header.name">Cache-Control</stringProp>
                      <stringProp name="Header.value">no-cache</stringProp>
                    </elementProp>
                    <elementProp name="clientId" elementType="Header">
                      <stringProp name="Header.name">clientId</stringProp>
                      <stringProp name="Header.value">${clientID}</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip, deflate</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/rest/alerts/new/countAll/_information-manager" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="_" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.name">_</stringProp>
                      <stringProp name="Argument.value">1365036925646</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">http</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/alerts/new/countAll/</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="clientId" elementType="Header">
                      <stringProp name="Header.name">clientId</stringProp>
                      <stringProp name="Header.value">${clientID}</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                    </elementProp>
                    <elementProp name="clientCode" elementType="Header">
                      <stringProp name="Header.name">clientCode</stringProp>
                      <stringProp name="Header.value">${clientCode}</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">application/json, text/javascript, */*; q=0.01</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip, deflate</stringProp>
                    </elementProp>
                    <elementProp name="propertyId" elementType="Header">
                      <stringProp name="Header.name">propertyId</stringProp>
                      <stringProp name="Header.value">${propertyID}</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
            </hashTree>
            <TransactionController guiclass="TransactionControllerGui" testclass="TransactionController" testname="Login" enabled="true">
              <boolProp name="TransactionController.parent">true</boolProp>
              <boolProp name="TransactionController.includeTimers">false</boolProp>
            </TransactionController>
            <hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions_Login Page" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments"/>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/solutions</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip, deflate</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/_Login Page" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments"/>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/solutions/</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip, deflate</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/api/auth/login_Login Page" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments"/>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/solutions/api/auth/login</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip, deflate</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/support/rest/announcement/getAllAnnouncements_Login Page" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments"/>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/support/rest/announcement/getAllAnnouncements</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">*/*</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/api/auth/login</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip, deflate</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
            </hashTree>
            <TransactionController guiclass="TransactionControllerGui" testclass="TransactionController" testname="Post Login" enabled="true">
              <boolProp name="TransactionController.parent">true</boolProp>
              <boolProp name="TransactionController.includeTimers">false</boolProp>
            </TransactionController>
            <hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/api/auth/login_Post Login" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="username" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">true</boolProp>
                      <stringProp name="Argument.name">username</stringProp>
                      <stringProp name="Argument.value"><EMAIL></stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                    <elementProp name="password" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.name">password</stringProp>
                      <stringProp name="Argument.value">password</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                    <elementProp name="submit" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">true</boolProp>
                      <stringProp name="Argument.name">submit</stringProp>
                      <stringProp name="Argument.value">Log In</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
                <stringProp name="HTTPSampler.contentEncoding">utf-8</stringProp>
                <stringProp name="HTTPSampler.path">/solutions/api/auth/login</stringProp>
                <stringProp name="HTTPSampler.method">POST</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient4</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="Content-Type" elementType="Header">
                      <stringProp name="Header.name">Content-Type</stringProp>
                      <stringProp name="Header.value">application/x-www-form-urlencoded</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/api/auth/login</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip, deflate</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/vaadin/VAADIN" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="sessionId" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.name">sessionId</stringProp>
                      <stringProp name="Argument.value">true</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">http</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/vaadin/VAADIN</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="Cache-Control" elementType="Header">
                      <stringProp name="Header.name">Cache-Control</stringProp>
                      <stringProp name="Header.value">no-cache</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">application/json, text/javascript, */*; q=0.01</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2062.124 Safari/537.36</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">http://us1sapp03.ideasstg.int:8080/solutions/api/page/postLogin</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="Pragma" elementType="Header">
                      <stringProp name="Header.name">Pragma</stringProp>
                      <stringProp name="Header.value">no-cache</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
                <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="Regular Expression Extractor" enabled="true">
                  <stringProp name="RegexExtractor.useHeaders">false</stringProp>
                  <stringProp name="RegexExtractor.refname">COOKIE_VAADIN</stringProp>
                  <stringProp name="RegexExtractor.regex">jsessionid&quot;:&quot;(.*)&quot;</stringProp>
                  <stringProp name="RegexExtractor.template">$1$</stringProp>
                  <stringProp name="RegexExtractor.default">Not found SOA</stringProp>
                  <stringProp name="RegexExtractor.match_number"></stringProp>
                </RegexExtractor>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/jsession" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments"/>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/pacman-platformsecurity/jsession</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="Cache-Control" elementType="Header">
                      <stringProp name="Header.name">Cache-Control</stringProp>
                      <stringProp name="Header.value">no-cache</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">application/json, text/javascript, */*; q=0.01</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2062.124 Safari/537.36</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">http://us1sapp03.ideasstg.int:8080/solutions/api/page/postLogin</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="Pragma" elementType="Header">
                      <stringProp name="Header.name">Pragma</stringProp>
                      <stringProp name="Header.value">no-cache</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
                <RegexExtractor guiclass="RegexExtractorGui" testclass="RegexExtractor" testname="Regular Expression Extractor" enabled="true">
                  <stringProp name="RegexExtractor.useHeaders">false</stringProp>
                  <stringProp name="RegexExtractor.refname">COOKIE_SOA</stringProp>
                  <stringProp name="RegexExtractor.regex">jsessionid&quot;:&quot;(.*)&quot;</stringProp>
                  <stringProp name="RegexExtractor.template">$1$</stringProp>
                  <stringProp name="RegexExtractor.default">Not found SOA</stringProp>
                  <stringProp name="RegexExtractor.match_number"></stringProp>
                </RegexExtractor>
                <hashTree/>
                <BeanShellPostProcessor guiclass="TestBeanGUI" testclass="BeanShellPostProcessor" testname="BeanShell PostProcessor" enabled="true">
                  <boolProp name="resetInterpreter">false</boolProp>
                  <stringProp name="parameters"></stringProp>
                  <stringProp name="filename"></stringProp>
                  <stringProp name="script">import org.apache.jmeter.protocol.http.control.CookieManager;
import org.apache.jmeter.protocol.http.control.Cookie;
CookieManager manager = ctx.getCurrentSampler().getProperty(&quot;HTTPSampler.cookie_manager&quot;).getObjectValue();
Cookie cookie = new Cookie(&quot;SOA_JSESSIONID&quot;,&quot;${COOKIE_SOA}&quot;,&quot;${g3UrlDomainName}&quot;,&quot;/solutions&quot;,false,0);
manager.add(cookie);
cookie = new Cookie(&quot;VAADIN_JSESSIONID&quot;,&quot;${COOKIE_VAADIN}&quot;,&quot;${g3UrlDomainName}&quot;,&quot;/solutions&quot;,false,0);
manager.add(cookie);


</stringProp>
                </BeanShellPostProcessor>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/rest/workcontext/default/StressBox" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="_" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.name">_</stringProp>
                      <stringProp name="Argument.value">1369903470006</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/workcontext/default/${clientCode}</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">application/json, text/javascript, */*; q=0.01</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/rest/user/11403" enabled="false">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="_" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.name">_</stringProp>
                      <stringProp name="Argument.value">1369903470001</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/user/11403</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="clientId" elementType="Header">
                      <stringProp name="Header.name">clientId</stringProp>
                      <stringProp name="Header.value">2</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="clientCode" elementType="Header">
                      <stringProp name="Header.name">clientCode</stringProp>
                      <stringProp name="Header.value">BSTN</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">*/*</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="propertyGroupId" elementType="Header">
                      <stringProp name="Header.name">propertyGroupId</stringProp>
                      <stringProp name="Header.value">713</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/rest/user/11403" enabled="false">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="_" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.name">_</stringProp>
                      <stringProp name="Argument.value">1369903470746</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/user/11403</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="clientId" elementType="Header">
                      <stringProp name="Header.name">clientId</stringProp>
                      <stringProp name="Header.value">${clientID}</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="clientCode" elementType="Header">
                      <stringProp name="Header.name">clientCode</stringProp>
                      <stringProp name="Header.value">${clientCode}</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">*/*</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="propertyId" elementType="Header">
                      <stringProp name="Header.name">propertyId</stringProp>
                      <stringProp name="Header.value">${propertyID}</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/api/page/permission/information-manager" enabled="false">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="_" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.name">_</stringProp>
                      <stringProp name="Argument.value">1369903470748</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
                <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
                <stringProp name="HTTPSampler.path">/solutions/api/page/permission/information-manager</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="clientId" elementType="Header">
                      <stringProp name="Header.name">clientId</stringProp>
                      <stringProp name="Header.value">${clientID}</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="clientCode" elementType="Header">
                      <stringProp name="Header.name">clientCode</stringProp>
                      <stringProp name="Header.value">${clientCode}</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">*/*</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="propertyId" elementType="Header">
                      <stringProp name="Header.name">propertyId</stringProp>
                      <stringProp name="Header.value">${propertyID}</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/rest/authorization/propertygroup" enabled="false">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="_" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.name">_</stringProp>
                      <stringProp name="Argument.value">1369903470748</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/authorization/propertygroup</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="clientId" elementType="Header">
                      <stringProp name="Header.name">clientId</stringProp>
                      <stringProp name="Header.value">${clientID}</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="clientCode" elementType="Header">
                      <stringProp name="Header.name">clientCode</stringProp>
                      <stringProp name="Header.value">${clientCode}</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">application/json, text/javascript, */*; q=0.01</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="propertyId" elementType="Header">
                      <stringProp name="Header.name">propertyId</stringProp>
                      <stringProp name="Header.value">${propertyID}</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/api/page/clientlogo/StressBox" enabled="false">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="_" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.name">_</stringProp>
                      <stringProp name="Argument.value">1369903471558</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
                <stringProp name="HTTPSampler.contentEncoding">utf-8</stringProp>
                <stringProp name="HTTPSampler.path">/solutions/api/page/clientlogo/StressBox</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="clientId" elementType="Header">
                      <stringProp name="Header.name">clientId</stringProp>
                      <stringProp name="Header.value">7</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="clientCode" elementType="Header">
                      <stringProp name="Header.name">clientCode</stringProp>
                      <stringProp name="Header.value">StressBox</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">*/*</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="propertyId" elementType="Header">
                      <stringProp name="Header.name">propertyId</stringProp>
                      <stringProp name="Header.value">60850</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/rest/sync/event" enabled="false">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.value">{&quot;eventType&quot;:&quot;LOGIN&quot;}</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/sync/event</stringProp>
                <stringProp name="HTTPSampler.method">POST</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="Content-Type" elementType="Header">
                      <stringProp name="Header.name">Content-Type</stringProp>
                      <stringProp name="Header.value">application/json</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">*/*</stringProp>
                    </elementProp>
                    <elementProp name="clientCode" elementType="Header">
                      <stringProp name="Header.name">clientCode</stringProp>
                      <stringProp name="Header.value">StressBox</stringProp>
                    </elementProp>
                    <elementProp name="Origin" elementType="Header">
                      <stringProp name="Header.name">Origin</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                    </elementProp>
                    <elementProp name="propertyId" elementType="Header">
                      <stringProp name="Header.name">propertyId</stringProp>
                      <stringProp name="Header.value">60850</stringProp>
                    </elementProp>
                    <elementProp name="clientId" elementType="Header">
                      <stringProp name="Header.name">clientId</stringProp>
                      <stringProp name="Header.value">7</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/api/page/userMenu/information-manager" enabled="false">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="_" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.name">_</stringProp>
                      <stringProp name="Argument.value">1369903471559</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
                <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
                <stringProp name="HTTPSampler.path">/solutions/api/page/userMenu/information-manager</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="clientId" elementType="Header">
                      <stringProp name="Header.name">clientId</stringProp>
                      <stringProp name="Header.value">7</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="clientCode" elementType="Header">
                      <stringProp name="Header.name">clientCode</stringProp>
                      <stringProp name="Header.value">StressBox</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">*/*</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="propertyId" elementType="Header">
                      <stringProp name="Header.name">propertyId</stringProp>
                      <stringProp name="Header.value">60850</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/api/page/menu/information-manager" enabled="false">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="_" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.name">_</stringProp>
                      <stringProp name="Argument.value">1369903471559</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
                <stringProp name="HTTPSampler.contentEncoding">UTF-8</stringProp>
                <stringProp name="HTTPSampler.path">/solutions/api/page/menu/information-manager</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="clientId" elementType="Header">
                      <stringProp name="Header.name">clientId</stringProp>
                      <stringProp name="Header.value">7</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="clientCode" elementType="Header">
                      <stringProp name="Header.name">clientCode</stringProp>
                      <stringProp name="Header.value">StressBox</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">*/*</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="propertyId" elementType="Header">
                      <stringProp name="Header.name">propertyId</stringProp>
                      <stringProp name="Header.value">60850</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/rest/sync" enabled="false">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="_" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.name">_</stringProp>
                      <stringProp name="Argument.value">1369903471575</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/sync</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="clientId" elementType="Header">
                      <stringProp name="Header.name">clientId</stringProp>
                      <stringProp name="Header.value">7</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="clientCode" elementType="Header">
                      <stringProp name="Header.name">clientCode</stringProp>
                      <stringProp name="Header.value">StressBox</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">application/json, text/javascript, */*; q=0.01</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="propertyId" elementType="Header">
                      <stringProp name="Header.name">propertyId</stringProp>
                      <stringProp name="Header.value">60850</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/rest/dates/system" enabled="false">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="_" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.name">_</stringProp>
                      <stringProp name="Argument.value">1369903471576</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/dates/system</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="clientId" elementType="Header">
                      <stringProp name="Header.name">clientId</stringProp>
                      <stringProp name="Header.value">7</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="clientCode" elementType="Header">
                      <stringProp name="Header.name">clientCode</stringProp>
                      <stringProp name="Header.value">StressBox</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">*/*</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="propertyId" elementType="Header">
                      <stringProp name="Header.name">propertyId</stringProp>
                      <stringProp name="Header.value">60850</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/pacman-platformsecurity/rest/sync/event" enabled="false">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.value">{&quot;pageCode&quot;:&quot;information-manager&quot;,&quot;eventType&quot;:&quot;PAGE_LOADED&quot;}</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">${g3UrlProtocol}</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/sync/event</stringProp>
                <stringProp name="HTTPSampler.method">POST</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="Content-Type" elementType="Header">
                      <stringProp name="Header.name">Content-Type</stringProp>
                      <stringProp name="Header.value">application/json</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">*/*</stringProp>
                    </elementProp>
                    <elementProp name="clientCode" elementType="Header">
                      <stringProp name="Header.name">clientCode</stringProp>
                      <stringProp name="Header.value">StressBox</stringProp>
                    </elementProp>
                    <elementProp name="Origin" elementType="Header">
                      <stringProp name="Header.name">Origin</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                    </elementProp>
                    <elementProp name="propertyId" elementType="Header">
                      <stringProp name="Header.name">propertyId</stringProp>
                      <stringProp name="Header.value">60850</stringProp>
                    </elementProp>
                    <elementProp name="clientId" elementType="Header">
                      <stringProp name="Header.name">clientId</stringProp>
                      <stringProp name="Header.value">7</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="SyncEnable-SPECIAL_EVENTS_CHANGED" enabled="false">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="_" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">1365036924242</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    <stringProp name="Argument.name">_</stringProp>
                    <stringProp name="Argument.desc">false</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding"></stringProp>
              <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/sync/SPECIAL_EVENTS_CHANGED</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">true</boolProp>
              <boolProp name="HTTPSampler.monitor">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="clientId" elementType="Header">
                    <stringProp name="Header.name">clientId</stringProp>
                    <stringProp name="Header.value">${clientID}</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                  </elementProp>
                  <elementProp name="clientCode" elementType="Header">
                    <stringProp name="Header.name">clientCode</stringProp>
                    <stringProp name="Header.value">${clientCode}</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">application/json, text/javascript, */*; q=0.01</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                  </elementProp>
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                  </elementProp>
                  <elementProp name="X-Requested-With" elementType="Header">
                    <stringProp name="Header.name">X-Requested-With</stringProp>
                    <stringProp name="Header.value">XMLHttpRequest</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate</stringProp>
                  </elementProp>
                  <elementProp name="propertyId" elementType="Header">
                    <stringProp name="Header.name">${parameter1}</stringProp>
                    <stringProp name="Header.value">${parameter1val}</stringProp>
                  </elementProp>
                  <elementProp name="" elementType="Header">
                    <stringProp name="Header.name">propertyCode</stringProp>
                    <stringProp name="Header.value">${propertyCode}</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="SyncEnable-ACCOMMODATION_CONFIG_CHANGED" enabled="false">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="_" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">1365036924242</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    <stringProp name="Argument.name">_</stringProp>
                    <stringProp name="Argument.desc">false</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding"></stringProp>
              <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/sync/ACCOMMODATION_CONFIG_CHANGED</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">true</boolProp>
              <boolProp name="HTTPSampler.monitor">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="clientId" elementType="Header">
                    <stringProp name="Header.name">clientId</stringProp>
                    <stringProp name="Header.value">${clientID}</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                  </elementProp>
                  <elementProp name="clientCode" elementType="Header">
                    <stringProp name="Header.name">clientCode</stringProp>
                    <stringProp name="Header.value">${clientCode}</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">application/json, text/javascript, */*; q=0.01</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                  </elementProp>
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                  </elementProp>
                  <elementProp name="X-Requested-With" elementType="Header">
                    <stringProp name="Header.name">X-Requested-With</stringProp>
                    <stringProp name="Header.value">XMLHttpRequest</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate</stringProp>
                  </elementProp>
                  <elementProp name="propertyId" elementType="Header">
                    <stringProp name="Header.name">${parameter1}</stringProp>
                    <stringProp name="Header.value">${parameter1val}</stringProp>
                  </elementProp>
                  <elementProp name="" elementType="Header">
                    <stringProp name="Header.name">propertyCode</stringProp>
                    <stringProp name="Header.value">${propertyCode}</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="SyncDisable-SPECIAL_EVENTS_CHANGED" enabled="false">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="_" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">1365036924242</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    <stringProp name="Argument.name">_</stringProp>
                    <stringProp name="Argument.desc">false</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding"></stringProp>
              <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/sync/clear/SPECIAL_EVENTS_CHANGED</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">true</boolProp>
              <boolProp name="HTTPSampler.monitor">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="clientId" elementType="Header">
                    <stringProp name="Header.name">clientId</stringProp>
                    <stringProp name="Header.value">${clientID}</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                  </elementProp>
                  <elementProp name="clientCode" elementType="Header">
                    <stringProp name="Header.name">clientCode</stringProp>
                    <stringProp name="Header.value">${clientCode}</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">application/json, text/javascript, */*; q=0.01</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                  </elementProp>
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                  </elementProp>
                  <elementProp name="X-Requested-With" elementType="Header">
                    <stringProp name="Header.name">X-Requested-With</stringProp>
                    <stringProp name="Header.value">XMLHttpRequest</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate</stringProp>
                  </elementProp>
                  <elementProp name="propertyId" elementType="Header">
                    <stringProp name="Header.name">${parameter1}</stringProp>
                    <stringProp name="Header.value">${parameter1val}</stringProp>
                  </elementProp>
                  <elementProp name="" elementType="Header">
                    <stringProp name="Header.name">propertyCode</stringProp>
                    <stringProp name="Header.value">${propertyCode}</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="SyncRun-SPECIAL_EVENTS_CHANGED" enabled="false">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="_" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">1365036924242</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    <stringProp name="Argument.name">_</stringProp>
                    <stringProp name="Argument.desc">false</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding"></stringProp>
              <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/sync/syncAll</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">true</boolProp>
              <boolProp name="HTTPSampler.monitor">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="clientId" elementType="Header">
                    <stringProp name="Header.name">clientId</stringProp>
                    <stringProp name="Header.value">${clientID}</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                  </elementProp>
                  <elementProp name="clientCode" elementType="Header">
                    <stringProp name="Header.name">clientCode</stringProp>
                    <stringProp name="Header.value">${clientCode}</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">application/json, text/javascript, */*; q=0.01</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                  </elementProp>
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                  </elementProp>
                  <elementProp name="X-Requested-With" elementType="Header">
                    <stringProp name="Header.name">X-Requested-With</stringProp>
                    <stringProp name="Header.value">XMLHttpRequest</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate</stringProp>
                  </elementProp>
                  <elementProp name="propertyId" elementType="Header">
                    <stringProp name="Header.name">${parameter1}</stringProp>
                    <stringProp name="Header.value">${parameter1val}</stringProp>
                  </elementProp>
                  <elementProp name="" elementType="Header">
                    <stringProp name="Header.name">propertyCode</stringProp>
                    <stringProp name="Header.value">${propertyCode}</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Run-Services" enabled="false">
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="_" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">1365036924242</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    <stringProp name="Argument.name">_</stringProp>
                    <stringProp name="Argument.desc">false</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding"></stringProp>
              <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/sas/forecastingService?operationType=BDE&amp;propertyId=${propertyID}</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">true</boolProp>
              <boolProp name="HTTPSampler.monitor">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="clientId" elementType="Header">
                    <stringProp name="Header.name">clientId</stringProp>
                    <stringProp name="Header.value">${clientID}</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                  </elementProp>
                  <elementProp name="clientCode" elementType="Header">
                    <stringProp name="Header.name">clientCode</stringProp>
                    <stringProp name="Header.value">${clientCode}</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">application/json, text/javascript, */*; q=0.01</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                  </elementProp>
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                  </elementProp>
                  <elementProp name="X-Requested-With" elementType="Header">
                    <stringProp name="Header.name">X-Requested-With</stringProp>
                    <stringProp name="Header.value">XMLHttpRequest</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate</stringProp>
                  </elementProp>
                  <elementProp name="propertyId" elementType="Header">
                    <stringProp name="Header.name">${parameter1}</stringProp>
                    <stringProp name="Header.value">${parameter1val}</stringProp>
                  </elementProp>
                  <elementProp name="" elementType="Header">
                    <stringProp name="Header.name">propertyCode</stringProp>
                    <stringProp name="Header.value">${propertyCode}</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Group Evaluation" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">    {&#xd;
      &quot;id&quot;:&quot;1&quot;,&#xd;
      &quot;propertyId&quot; : &quot;${propertyID}&quot;,&#xd;
      &quot;groupName&quot; : &quot;Test 2 Group&quot;,&#xd;
      &quot;marketSegment&quot; : {&quot;id&quot;:14},&#xd;
      &quot;evaluationMethod&quot; : &quot;ROH&quot;,&#xd;
      &quot;evaluationType&quot; : &quot;GUEST_ROOM_ONLY&quot;,&#xd;
      &quot;evaluationDate&quot; : 1443540304000,&#xd;
      &quot;materializationStatus&quot; : &quot;SCENARIO&quot;,&#xd;
      &quot;groupEvaluationArrivalDates&quot; : [&#xd;
        {&quot;preferredDate&quot; : &quot;true&quot;, &quot;arrivalDate&quot;: 1444490133000} &#xd;
      ],&#xd;
      &quot;groupEvaluationDayOfStays&quot; : [&#xd;
        {&quot;dayOfStay&quot; : 1, &quot;numberOfRooms&quot; : 9},&#xd;
        {&quot;dayOfStay&quot; : 2, &quot;numberOfRooms&quot; : 9}&#xd;
&#xd;
      ]&#xd;
    }&#xd;
</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding"></stringProp>
              <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/groupEvaluationService/evaluate?propertyId=${propertyID}</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">true</boolProp>
              <boolProp name="HTTPSampler.monitor">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <ResultSaver guiclass="ResultSaverGui" testclass="ResultSaver" testname="Save Responses to a file" enabled="false">
                <stringProp name="FileSaver.filename">pre</stringProp>
                <boolProp name="FileSaver.errorsonly">false</boolProp>
                <boolProp name="FileSaver.skipautonumber">false</boolProp>
                <boolProp name="FileSaver.skipsuffix">false</boolProp>
                <boolProp name="FileSaver.successonly">false</boolProp>
                <stringProp name="FileSaver.variablename">result</stringProp>
              </ResultSaver>
              <hashTree/>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="clientId" elementType="Header">
                    <stringProp name="Header.name">clientId</stringProp>
                    <stringProp name="Header.value">${clientID}</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                  </elementProp>
                  <elementProp name="clientCode" elementType="Header">
                    <stringProp name="Header.name">clientCode</stringProp>
                    <stringProp name="Header.value">${clientCode}</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">application/json, text/javascript, */*; q=0.01</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                  </elementProp>
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                  </elementProp>
                  <elementProp name="X-Requested-With" elementType="Header">
                    <stringProp name="Header.name">X-Requested-With</stringProp>
                    <stringProp name="Header.value">XMLHttpRequest</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate</stringProp>
                  </elementProp>
                  <elementProp name="propertyId" elementType="Header">
                    <stringProp name="Header.name">${parameter1}</stringProp>
                    <stringProp name="Header.value">${parameter1val}</stringProp>
                  </elementProp>
                  <elementProp name="" elementType="Header">
                    <stringProp name="Header.name">propertyCode</stringProp>
                    <stringProp name="Header.value">${propertyCode}</stringProp>
                  </elementProp>
                  <elementProp name="" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/json</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Group Evaluation" enabled="false">
              <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">    {&#xd;
      &quot;id&quot;:&quot;1&quot;,&#xd;
      &quot;propertyId&quot; : &quot;${propertyID}&quot;,&#xd;
      &quot;groupName&quot; : &quot;Test 2 Group&quot;,&#xd;
      &quot;marketSegment&quot; : {&quot;id&quot;:14},&#xd;
      &quot;evaluationMethod&quot; : &quot;ROH&quot;,&#xd;
      &quot;evaluationType&quot; : &quot;GUEST_ROOM_ONLY&quot;,&#xd;
      &quot;evaluationDate&quot; : 1443540304000,&#xd;
      &quot;materializationStatus&quot; : &quot;SCENARIO&quot;,&#xd;
      &quot;groupEvaluationCosts&quot; :[{&quot;groupEvaluationCostType&quot; : &quot;COMMISSION_FIXED&quot;,&quot;percentage&quot; : 10.00}],      &#xd;
      &quot;groupEvaluationArrivalDates&quot; : [&#xd;
        {&quot;preferredDate&quot; : &quot;true&quot;, &quot;arrivalDate&quot;: 1444490133000} &#xd;
      ],&#xd;
      &quot;groupEvaluationDayOfStays&quot; : [&#xd;
        {&quot;dayOfStay&quot; : 1, &quot;numberOfRooms&quot; : 9},&#xd;
        {&quot;dayOfStay&quot; : 2, &quot;numberOfRooms&quot; : 9}&#xd;
&#xd;
      ]&#xd;
    }&#xd;
</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain"></stringProp>
              <stringProp name="HTTPSampler.port"></stringProp>
              <stringProp name="HTTPSampler.connect_timeout"></stringProp>
              <stringProp name="HTTPSampler.response_timeout"></stringProp>
              <stringProp name="HTTPSampler.protocol"></stringProp>
              <stringProp name="HTTPSampler.contentEncoding"></stringProp>
              <stringProp name="HTTPSampler.path">/pacman-platformsecurity/rest/groupEvaluationService/evaluate?propertyId=${propertyID}</stringProp>
              <stringProp name="HTTPSampler.method">POST</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">true</boolProp>
              <boolProp name="HTTPSampler.monitor">false</boolProp>
              <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
            </HTTPSamplerProxy>
            <hashTree>
              <ResultSaver guiclass="ResultSaverGui" testclass="ResultSaver" testname="Save Responses to a file" enabled="false">
                <stringProp name="FileSaver.filename">pre</stringProp>
                <boolProp name="FileSaver.errorsonly">false</boolProp>
                <boolProp name="FileSaver.skipautonumber">false</boolProp>
                <boolProp name="FileSaver.skipsuffix">false</boolProp>
                <boolProp name="FileSaver.successonly">false</boolProp>
                <stringProp name="FileSaver.variablename">result</stringProp>
              </ResultSaver>
              <hashTree/>
              <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                <collectionProp name="HeaderManager.headers">
                  <elementProp name="clientId" elementType="Header">
                    <stringProp name="Header.name">clientId</stringProp>
                    <stringProp name="Header.value">${clientID}</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Language" elementType="Header">
                    <stringProp name="Header.name">Accept-Language</stringProp>
                    <stringProp name="Header.value">en-US,en;q=0.5</stringProp>
                  </elementProp>
                  <elementProp name="clientCode" elementType="Header">
                    <stringProp name="Header.name">clientCode</stringProp>
                    <stringProp name="Header.value">${clientCode}</stringProp>
                  </elementProp>
                  <elementProp name="Accept" elementType="Header">
                    <stringProp name="Header.name">Accept</stringProp>
                    <stringProp name="Header.value">application/json, text/javascript, */*; q=0.01</stringProp>
                  </elementProp>
                  <elementProp name="User-Agent" elementType="Header">
                    <stringProp name="Header.name">User-Agent</stringProp>
                    <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64; rv:19.0) Gecko/20100101 Firefox/19.0</stringProp>
                  </elementProp>
                  <elementProp name="Referer" elementType="Header">
                    <stringProp name="Header.name">Referer</stringProp>
                    <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/information-manager</stringProp>
                  </elementProp>
                  <elementProp name="X-Requested-With" elementType="Header">
                    <stringProp name="Header.name">X-Requested-With</stringProp>
                    <stringProp name="Header.value">XMLHttpRequest</stringProp>
                  </elementProp>
                  <elementProp name="Accept-Encoding" elementType="Header">
                    <stringProp name="Header.name">Accept-Encoding</stringProp>
                    <stringProp name="Header.value">gzip, deflate</stringProp>
                  </elementProp>
                  <elementProp name="propertyId" elementType="Header">
                    <stringProp name="Header.name">${parameter1}</stringProp>
                    <stringProp name="Header.value">${parameter1val}</stringProp>
                  </elementProp>
                  <elementProp name="" elementType="Header">
                    <stringProp name="Header.name">propertyCode</stringProp>
                    <stringProp name="Header.value">${propertyCode}</stringProp>
                  </elementProp>
                  <elementProp name="" elementType="Header">
                    <stringProp name="Header.name">Content-Type</stringProp>
                    <stringProp name="Header.value">application/json</stringProp>
                  </elementProp>
                </collectionProp>
              </HeaderManager>
              <hashTree/>
            </hashTree>
            <TransactionController guiclass="TransactionControllerGui" testclass="TransactionController" testname="Logout" enabled="true">
              <boolProp name="TransactionController.parent">true</boolProp>
              <boolProp name="TransactionController.includeTimers">false</boolProp>
            </TransactionController>
            <hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/api/auth/logout" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments"/>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">http</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/solutions/api/auth/logout</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/group-wash</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="postLogin" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.name">postLogin</stringProp>
                      <stringProp name="Argument.value">false</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">http</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/solutions/</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/group-wash</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/solutions/api/auth/login" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments"/>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">http</stringProp>
                <stringProp name="HTTPSampler.contentEncoding">utf-8</stringProp>
                <stringProp name="HTTPSampler.path">/solutions/api/auth/login</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/group-wash</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="/support/rest/announcement/getAllAnnouncements" enabled="true">
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" enabled="true">
                  <collectionProp name="Arguments.arguments"/>
                </elementProp>
                <stringProp name="HTTPSampler.domain"></stringProp>
                <stringProp name="HTTPSampler.port"></stringProp>
                <stringProp name="HTTPSampler.connect_timeout"></stringProp>
                <stringProp name="HTTPSampler.response_timeout"></stringProp>
                <stringProp name="HTTPSampler.protocol">http</stringProp>
                <stringProp name="HTTPSampler.contentEncoding"></stringProp>
                <stringProp name="HTTPSampler.path">/support/rest/announcement/getAllAnnouncements</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <stringProp name="HTTPSampler.implementation">HttpClient3.1</stringProp>
                <boolProp name="HTTPSampler.monitor">false</boolProp>
                <stringProp name="HTTPSampler.embedded_url_re"></stringProp>
              </HTTPSamplerProxy>
              <hashTree>
                <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
                  <collectionProp name="HeaderManager.headers">
                    <elementProp name="Accept-Language" elementType="Header">
                      <stringProp name="Header.name">Accept-Language</stringProp>
                      <stringProp name="Header.value">en-US,en;q=0.8</stringProp>
                    </elementProp>
                    <elementProp name="Accept" elementType="Header">
                      <stringProp name="Header.name">Accept</stringProp>
                      <stringProp name="Header.value">*/*</stringProp>
                    </elementProp>
                    <elementProp name="User-Agent" elementType="Header">
                      <stringProp name="Header.name">User-Agent</stringProp>
                      <stringProp name="Header.value">Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36</stringProp>
                    </elementProp>
                    <elementProp name="Accept-Encoding" elementType="Header">
                      <stringProp name="Header.name">Accept-Encoding</stringProp>
                      <stringProp name="Header.value">gzip,deflate,sdch</stringProp>
                    </elementProp>
                    <elementProp name="Referer" elementType="Header">
                      <stringProp name="Header.name">Referer</stringProp>
                      <stringProp name="Header.value">${g3UrlProtocol}://${g3UrlDomainName}/solutions/api/auth/login</stringProp>
                    </elementProp>
                    <elementProp name="X-Requested-With" elementType="Header">
                      <stringProp name="Header.name">X-Requested-With</stringProp>
                      <stringProp name="Header.value">XMLHttpRequest</stringProp>
                    </elementProp>
                  </collectionProp>
                </HeaderManager>
                <hashTree/>
              </hashTree>
            </hashTree>
          </hashTree>
        </hashTree>
      </hashTree>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
