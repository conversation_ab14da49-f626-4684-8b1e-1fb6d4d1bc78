package com.ideas.tetris.pacman.services.iamkeyrotation;

import com.amazonaws.AmazonServiceException;
import com.amazonaws.services.identitymanagement.AmazonIdentityManagement;
import com.amazonaws.services.identitymanagement.model.*;
import com.ideas.infra.tetris.security.EncryptionDecryption;
import com.ideas.tetris.pacman.services.retryservice.RetryService;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.Comparator;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig.getS3UserAccessKey;
import static com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig.getS3UserSecretKey;
import static com.ideas.tetris.platform.common.errorhandling.ErrorCode.S3_CLIENT_ERROR;


@Component
@Transactional
public class IAMKeyOperations {
    private static final Logger LOGGER = Logger.getLogger(IAMKeyOperations.class.getName());
    private static final int MAX_ATTEMPTS = 5;
    private static final int DURATION_IN_MILLIS = 5000;
    @Autowired
    IAMConnectionService iamConnectionService;
    @Autowired
    RetryService retryService;

    public IAMCredentialsDto rotateKey(IAMCredentialsDto iamCredentialsDto, boolean shouldDeleteSystemAccessKey) {
        AmazonIdentityManagement iamClient = getValidIamClient(iamCredentialsDto);

        deleteOldIamCredentials(iamCredentialsDto, iamClient, shouldDeleteSystemAccessKey);

        CreateAccessKeyResult response = createNewIamCredentials(iamCredentialsDto, iamClient);
        LOGGER.info("Created new Access and Secret key.");

        String encodedLatestAccessKey = EncryptionDecryption.doStrongTextEncryption(response.getAccessKey().getAccessKeyId());
        String encodedLatestSecretKey = EncryptionDecryption.doStrongTextEncryption(response.getAccessKey().getSecretAccessKey());

        iamCredentialsDto.setPreviousAccessKey(iamCredentialsDto.getLatestAccessKey());
        iamCredentialsDto.setPreviousSecretKey(iamCredentialsDto.getLatestSecretKey());

        iamCredentialsDto.setLatestAccessKey(encodedLatestAccessKey);
        iamCredentialsDto.setLatestSecretKey(encodedLatestSecretKey);
        retryService.attempt(() -> getValidIamClient(iamCredentialsDto), MAX_ATTEMPTS, Duration.ofMillis(DURATION_IN_MILLIS), shouldRetry());
        return iamCredentialsDto;
    }

    public AmazonIdentityManagement getValidIamClient(IAMCredentialsDto iamCredentialsDto) {
        AmazonIdentityManagement iamClient = iamConnectionService.getIamClient(iamCredentialsDto);
        if (validateIamCredentials(iamCredentialsDto, iamClient)) {
            return iamClient;
        }
        throw new TetrisException("Unable to login with the given latest credentials");
    }

    private CreateAccessKeyResult createNewIamCredentials(IAMCredentialsDto iamCredentialsDto, AmazonIdentityManagement iamClient) {
        try {
            CreateAccessKeyRequest accessKeyRequest = new CreateAccessKeyRequest().withUserName(iamCredentialsDto.getUserName());
            return iamClient.createAccessKey(accessKeyRequest);
        } catch (Exception exception) {
            if (exception instanceof AmazonServiceException && ((AmazonServiceException) exception).getStatusCode() == HttpStatus.SC_FORBIDDEN) {
                LOGGER.error("Permission denied while creating Access and Secret key.", exception);
            }
            throw throwTetrisException(exception, "Error while creating aws key");
        }
    }

    private void deleteOldIamCredentials(IAMCredentialsDto iamCredentialsDto, AmazonIdentityManagement iamClient, boolean shouldDeleteSystemAccessKey) {
        List<AccessKeyMetadata> accessKeyMetadata = listAndGetAccessKeys(iamCredentialsDto, iamClient);
        try {
            String message = "Deleted oldest Access and Secret Key.";
            if (accessKeyMetadata.size() > 1) {
                String oldestKey = accessKeyMetadata.get(0).getAccessKeyId();
                if (StringUtils.equals(oldestKey, getS3UserAccessKey()) && !shouldDeleteSystemAccessKey) {
                    oldestKey = accessKeyMetadata.get(1).getAccessKeyId();
                    message = "Deleted latest Access and Secret Key.";
                }
                deleteKey(iamClient, oldestKey);
                if (StringUtils.equals(oldestKey, accessKeyMetadata.get(1).getAccessKeyId())) {
                    iamCredentialsDto.setLatestAccessKey(EncryptionDecryption.doStrongTextEncryption(getS3UserAccessKey()));
                    iamCredentialsDto.setLatestSecretKey(EncryptionDecryption.doStrongTextEncryption(getS3UserSecretKey()));
                }
            } else {
                message = "Only one Access and Secret key is present, Skipped deleting Access and Secret key.";
            }
            LOGGER.info(message);
        } catch (Exception exception) {
            if (exception instanceof AmazonServiceException && ((AmazonServiceException) exception).getStatusCode() == HttpStatus.SC_FORBIDDEN) {
                LOGGER.error("Permission denied while deleting Access and Secret key.", exception);
            }
            throw throwTetrisException(exception, "Error while deleting aws key");
        }
    }

    private TetrisException throwTetrisException(Exception exception, String message) {
        TetrisException deleteException = new TetrisException(S3_CLIENT_ERROR, message, exception);
        LOGGER.error(deleteException.getBaseMessage(), exception);
        return deleteException;
    }

    private void deleteKey(AmazonIdentityManagement iamClient, String accessKeyId) {
        iamClient.deleteAccessKey(new DeleteAccessKeyRequest().withAccessKeyId(accessKeyId));
    }

    private List<AccessKeyMetadata> listAndGetAccessKeys(IAMCredentialsDto iamCredentialsDto, AmazonIdentityManagement iamClient) {
        ListAccessKeysRequest listKeysRequest = new ListAccessKeysRequest().withUserName(iamCredentialsDto.getUserName());
        ListAccessKeysResult listAccessKeysResult = iamClient.listAccessKeys(listKeysRequest);
        return listAccessKeysResult.getAccessKeyMetadata()
                .stream()
                .sorted(Comparator.comparing(AccessKeyMetadata::getCreateDate))
                .collect(Collectors.toList());
    }

    private boolean validateIamCredentials(IAMCredentialsDto iamCredentialsDto, AmazonIdentityManagement iamClient) {
        try {
            return CollectionUtils.isNotEmpty(listAndGetAccessKeys(iamCredentialsDto, iamClient));
        } catch (AmazonServiceException exception) {
            if (exception.getStatusCode() == HttpStatus.SC_FORBIDDEN) { // Invalid access or secret key
                LOGGER.error("Cannot establish connection with given IAM credentials.", exception);
            }
        }
        return false;
    }

    private Function<Exception, Boolean> shouldRetry() {
        return TetrisException.class::isInstance;
    }
}

