package com.ideas.tetris.pacman.services.informationmanager.entity;

import com.ideas.g3.rule.CrudServiceBeanExtension;
import com.ideas.tetris.pacman.services.informationmanager.enums.ExceptionSubType;
import com.ideas.tetris.pacman.services.informationmanager.enums.LevelType;
import com.ideas.tetris.pacman.services.informationmanager.enums.MetricType;
import com.ideas.tetris.pacman.services.informationmanager.enums.RelationalOperator;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;

import java.math.BigDecimal;
import java.util.List;


public class UniqueInfoMgrAlertConfigCreator {

    public static InformationMgrAlertConfigEntity createForPropertyAndAlertType(int propertyId, String alertTypeName) {
        InformationMgrAlertConfigEntity config = new InformationMgrAlertConfigEntity();
        InformationMgrLevelEntity objExceptionLevelEntity =
                CrudServiceBeanExtension.getTenantCrudService()
                        .findByNamedQuerySingleResult(InformationMgrLevelEntity.BY_NAME,
                                QueryParameter.with("name", LevelType.ROOM_CLASS.getCode()).parameters());
        InformationMgrSubTypeEntity objExceptionSubTypeEntity =
                CrudServiceBeanExtension.getTenantCrudService()
                        .findByNamedQuerySingleResult(InformationMgrSubTypeEntity.BY_NAME,
                                QueryParameter.with("name", ExceptionSubType.LRV.getCode()).parameters());

        List<InformationMgrSubLevelEntity> sublevels = CrudServiceBeanExtension.getTenantCrudService()
                .findByNamedQuery(InformationMgrSubLevelEntity.BY_EXCEPTION_LEVEL, QueryParameter.with("levelId", objExceptionLevelEntity.getId()).parameters());

        InfoMgrTypeEntity type = CrudServiceBeanExtension.getTenantCrudService().findByNamedQuerySingleResult(InfoMgrTypeEntity.BY_NAME,
                QueryParameter.with("name", alertTypeName).parameters());
        config.setPropertyId(propertyId);
        config.setAlertTypeEntity(type);
        config.setCreatedByUserId(1);
        config.setDisabled(false);
        config.setExceptionLevel(objExceptionLevelEntity);
        config.setExceptionSubLevel(sublevels.get(0).getId());
        config.setExceptionSubType(objExceptionSubTypeEntity);
        config.setStartDate("Today+1");
        config.setEndDate("Today+1");
        config.setFrequency("1");
        config.setStatusId(1);
        config.setThresholdOperator(RelationalOperator.GREATER_OR_EQUAL.getCode());
        config.setThresholdValue(BigDecimal.ZERO);
        config.setThresholdMetricType(MetricType.CURRENCY);
        config = CrudServiceBeanExtension.getTenantCrudService().save(config);
        return config;

    }
}
