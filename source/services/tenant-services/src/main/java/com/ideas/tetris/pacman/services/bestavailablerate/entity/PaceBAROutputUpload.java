package com.ideas.tetris.pacman.services.bestavailablerate.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.platform.common.entity.IdAwareEntity;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedNativeQueries;
import javax.persistence.NamedNativeQuery;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@NamedNativeQueries({
        @NamedNativeQuery(name = PaceBAROutputUpload.BY_DATE_RANGE_FOR_MAX_DECISION,
                query = "select pbr.PACE_Bar_Output_Upload_ID ,pbr.Property_ID ,pace.Accom_Class_ID, pace.Arrival_DT," +
                        " pace.LOS, pbr.Rate_Unqualified_ID, pbr.Decision_id , " +
                        " pbr.Derived_Unqualified_Value ," +
                        " pbr.Override ," +
                        " pbr.Floor_Rate_Unqualified_ID , " +
                        " pbr.Decision_Reason_Type_ID ," +
                        " pbr.Month_ID ," +
                        " pbr.Year_ID ," +
                        " pbr.CreateDate ," +
                        " pbr.Ceil_Rate_Unqualified_ID" +
                        " from ( " +
                        "       select Arrival_DT, Accom_Class_ID, MAX(D.Decision_id) as Decision_id, Los " +
                        "       from PACE_Bar_Output_Upload PBO " +
                        "       inner join Decision D on PBO.Decision_ID = D.Decision_ID " +
                        "       and Arrival_DT between :startDate and :endDate " +
                        "       group by Arrival_DT, Accom_Class_ID, LOS " +
                        " ) as pace " +
                        " inner join ( " +
                        "       select PACE_Bar_Output_Upload_ID,Property_ID,Arrival_DT, Accom_Class_ID, Los, Rate_Unqualified_ID, " +
                        "       Decision_ID,Derived_Unqualified_Value, " +
                        "       Override,Floor_Rate_Unqualified_ID,Decision_Reason_Type_ID,Month_ID,Year_ID,CreateDate, " +
                        "       Ceil_Rate_Unqualified_ID " +
                        "       from PACE_Bar_Output_Upload " +
                        " ) as pbr on pace.Accom_Class_ID = pbr.Accom_Class_ID " +
                        " and pace.Arrival_DT = pbr.Arrival_DT " +
                        " and pace.LOS = pbr.LOS " +
                        " and pace.Decision_id = pbr.Decision_ID order by pace.Arrival_DT,pace.Accom_Class_ID,pace.LOS",
                resultClass = PaceBAROutputUpload.class),
        @NamedNativeQuery(name = PaceBAROutputUpload.DIFFERENTIAL_INSERT,
                query = "insert into PACE_Bar_Output_Upload (Decision_ID, Property_ID, " +
                        "Accom_Class_ID,Arrival_DT,Rate_Unqualified_ID,Derived_Unqualified_Value,LOS,Override,Floor_Rate_Unqualified_ID, " +
                        "Decision_Reason_Type_ID,Month_ID,Year_ID,CreateDate,Ceil_Rate_Unqualified_ID)     " +
                        "select np.Decision_ID, np.Property_ID,np.Accom_Class_ID,np.Arrival_DT,np.Rate_Unqualified_ID,1.1,np.LOS,np.Override, " +
                        "np.Floor_Rate_Unqualified_ID,np.Decision_Reason_Type_ID,np.Month_ID,np.Year_ID,np.CreateDate,np.Ceil_Rate_Unqualified_ID " +
                        "from " +
                        "Decision_Bar_Output np inner join Accom_Class ac " +
                        "on np.Property_ID= :propertyId     " +
                        "and np.Arrival_DT >= :startDate     " +
                        "and np.Arrival_DT <= :endDate " +
                        "and np.Accom_Class_ID = ac.Accom_Class_ID " +
                        "left join PACE_Bar_Output_Upload as p " +
                        "   on np.Property_ID = p.Property_ID " +
                        "   and np.Arrival_DT = p.Arrival_DT " +
                        "   and np.Accom_Class_ID = p.Accom_Class_ID " +
                        "   and np.LOS = p.LOS " +
                        "where p.Property_ID is null    " +
                        "and p.Arrival_DT is null     " +
                        "and p.Accom_Class_ID is null     " +
                        "and p.LOS is null  " +
                        "union all " +
                        "select np.Decision_ID, np.Property_ID,np.Accom_Class_ID,np.Arrival_DT,np.Rate_Unqualified_ID,1.1,np.LOS,np.Override, " +
                        "np.Floor_Rate_Unqualified_ID,np.Decision_Reason_Type_ID,np.Month_ID,np.Year_ID,np.CreateDate,np.Ceil_Rate_Unqualified_ID " +
                        "from Decision_Bar_Output np inner join Accom_Class ac     " +
                        "on np.Property_ID= :propertyId     " +
                        "and np.Arrival_DT >= :startDate     " +
                        "and np.Arrival_DT <= :endDate     " +
                        "and np.Accom_Class_ID = ac.Accom_Class_ID  " +
                        "inner join           " +
                        "(             " +
                        "   select Arrival_DT, Accom_Class_ID, MAX(D.Decision_id) as Decision_id, Los " +
                        "   from PACE_Bar_Output_Upload PBO " +
                        "   inner join Decision D on PBO.Decision_ID = D.Decision_ID " +
                        "   and Arrival_DT between :startDate and :endDate " +
                        "   group by Arrival_DT, Accom_Class_ID, LOS             " +
                        ") as pace   " +
                        "   inner join PACE_Bar_Output_Upload as pbr on pace.Accom_Class_ID = pbr.Accom_Class_ID    " +
                        "   and pace.Arrival_DT = pbr.Arrival_DT       " +
                        "   and pace.LOS = pbr.LOS      " +
                        "   and pace.Decision_id = pbr.Decision_ID   " +
                        "   on np.Accom_Class_ID = pace.Accom_Class_ID    " +
                        "   and np.Arrival_DT = pace.Arrival_DT     " +
                        "   and np.LOS = pace.LOS  " +
                        "   and np.Rate_Unqualified_ID <> pbr.Rate_Unqualified_ID "
        )
})

@Table(name = "PACE_Bar_Output_Upload")
public class PaceBAROutputUpload extends IdAwareEntity<Integer> {

    public static final String BY_DATE_RANGE_FOR_MAX_DECISION = "com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceBAROutputUpload.BY_DATE_RANGE_FOR_MAX_DECISION";
    public static final String DIFFERENTIAL_INSERT = "com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceBAROutputUpload.DIFFERENTIAL_INSERT";

    private Integer id;
    private Decision decision;
    private Integer propertyID;
    private Integer accomClassId;
    private Date arrivalDate;
    private RateUnqualified rateUnqualified;
    private BigDecimal derivedUnqualifiedValue;
    private Integer lengthOfStay;
    private String override;
    private RateUnqualified floorRateUnqualified;
    private RateUnqualified ceilingRateUnqualified;
    /**
     * @deprecated Use calendar_dim to join on decision date
     */
    @Deprecated
    private Integer monthId;
    /**
     * @deprecated Use calendar_dim to join on decision date
     */
    @Deprecated
    private Integer yearId;
    private Date createDate;
    private Integer reasonTypeId;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "PACE_Bar_Output_Upload_ID", columnDefinition = "BIGINT")
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "Property_ID")
    public Integer getPropertyID() {
        return propertyID;
    }

    public void setPropertyID(Integer propertyID) {
        this.propertyID = propertyID;
    }

    @Column(name = "Accom_Class_ID")
    public Integer getAccomClassId() {
        return accomClassId;
    }

    public void setAccomClassId(Integer accomClassId) {
        this.accomClassId = accomClassId;
    }

    @Column(name = "Arrival_DT")
    @Temporal(TemporalType.DATE)
    @JsonFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    public Date getArrivalDate() {
        return arrivalDate;
    }

    public void setArrivalDate(Date arrivalDate) {
        this.arrivalDate = arrivalDate;
    }

    @ManyToOne
    @JoinColumn(name = "Rate_Unqualified_ID")
    public RateUnqualified getRateUnqualified() {
        return rateUnqualified;
    }

    public void setRateUnqualified(RateUnqualified rateUnqualified) {
        this.rateUnqualified = rateUnqualified;
    }

    @Column(name = "LOS")
    public Integer getLengthOfStay() {
        return lengthOfStay;
    }

    public void setLengthOfStay(Integer lengthOfStay) {
        this.lengthOfStay = lengthOfStay;
    }

    @Column(name = "CreateDate")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "Override")
    public String getOverride() {
        return override;
    }

    public void setOverride(String override) {
        this.override = override;
    }

    @ManyToOne
    @JoinColumn(name = "Floor_Rate_Unqualified_ID")
    public RateUnqualified getFloorRateUnqualified() {
        return floorRateUnqualified;
    }

    public void setFloorRateUnqualified(RateUnqualified floorRateUnqualified) {
        this.floorRateUnqualified = floorRateUnqualified;
    }

    @ManyToOne
    @JoinColumn(name = "Ceil_Rate_Unqualified_ID")
    public RateUnqualified getCeilingRateUnqualified() {
        return ceilingRateUnqualified;
    }

    public void setCeilingRateUnqualified(RateUnqualified ceilingRateUnqualified) {
        this.ceilingRateUnqualified = ceilingRateUnqualified;
    }

    /**
     * @deprecated Do not populate or read this data.  To get at the actual month,
     * join to the calendar_dim table based on the occupancy_date
     */
    @Deprecated
    @Column(name = "Month_ID")
    public Integer getMonthId() {
        return monthId;
    }

    /**
     * @deprecated Do not populate or read this data.  To get at the actual month,
     * join to the calendar_dim table based on the occupancy_date
     */
    @Deprecated
    public void setMonthId(Integer monthId) {
        this.monthId = monthId;
    }

    /**
     * @deprecated Do not populate or read this data.  To get at the actual month,
     * join to the calendar_dim table based on the occupancy_date
     */
    @Deprecated
    @Column(name = "YEAR_ID")
    public Integer getYearId() {
        return yearId;
    }

    /**
     * @deprecated Do not populate or read this data.  To get at the actual month,
     * join to the calendar_dim table based on the occupancy_date
     */
    @Deprecated
    public void setYearId(Integer yearId) {
        this.yearId = yearId;
    }

    @ManyToOne
    @JoinColumn(name = "Decision_ID")
    public Decision getDecision() {
        return decision;
    }

    public void setDecision(Decision decision) {
        this.decision = decision;
    }

    @Column(name = "Derived_Unqualified_Value")
    public BigDecimal getDerivedUnqualifiedValue() {
        return derivedUnqualifiedValue;
    }

    public void setDerivedUnqualifiedValue(BigDecimal derivedUnqualifiedValue) {
        this.derivedUnqualifiedValue = derivedUnqualifiedValue;
    }

    @Column(name = "Decision_Reason_Type_ID")
    public Integer getReasonTypeId() {
        return reasonTypeId;
    }

    public void setReasonTypeId(Integer reasonTypeId) {
        this.reasonTypeId = reasonTypeId;
    }

}
