//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vJAXB 2.1.10 in JDK 6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.01.16 at 05:10:35 PM IST 
//


package com.ideas.tetris.pacman.services.tars.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>Java class for anonymous complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element ref="{http://www.accorhotels.com/tars-rms}dateControl" maxOccurs="unbounded"/>
 *       &lt;/sequence>
 *       &lt;attribute name="levelType">
 *         &lt;simpleType>
 *           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *             &lt;enumeration value="RL"/>
 *           &lt;/restriction>
 *         &lt;/simpleType>
 *       &lt;/attribute>
 *       &lt;attribute name="type" use="required">
 *         &lt;simpleType>
 *           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *             &lt;enumeration value="Update"/>
 *             &lt;enumeration value="Bulk"/>
 *           &lt;/restriction>
 *         &lt;/simpleType>
 *       &lt;/attribute>
 *       &lt;attribute name="hotelCode" use="required">
 *         &lt;simpleType>
 *           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *           &lt;/restriction>
 *         &lt;/simpleType>
 *       &lt;/attribute>
 *       &lt;attribute name="codeExp" use="required">
 *         &lt;simpleType>
 *           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *             &lt;enumeration value="EZY"/>
 *             &lt;enumeration value="OPT"/>
 *             &lt;enumeration value="PRM"/>
 *             &lt;enumeration value="FBK"/>
 *             &lt;enumeration value="IDE"/>
 *           &lt;/restriction>
 *         &lt;/simpleType>
 *       &lt;/attribute>
 *       &lt;attribute name="timeStamp" use="required">
 *         &lt;simpleType>
 *           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}dateTime">
 *           &lt;/restriction>
 *         &lt;/simpleType>
 *       &lt;/attribute>
 *       &lt;attribute name="code" use="required">
 *         &lt;simpleType>
 *           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *           &lt;/restriction>
 *         &lt;/simpleType>
 *       &lt;/attribute>
 *       &lt;attribute name="stamp" use="required">
 *         &lt;simpleType>
 *           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *           &lt;/restriction>
 *         &lt;/simpleType>
 *       &lt;/attribute>
 *       &lt;attribute name="system" use="required">
 *         &lt;simpleType>
 *           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *           &lt;/restriction>
 *         &lt;/simpleType>
 *       &lt;/attribute>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "dateControl"
})
@XmlRootElement(name = "upload")
public class Upload {

    @XmlElement(required = true)
    protected List<DateControl> dateControl;
    @XmlAttribute
    protected String levelType;
    @XmlAttribute(required = true)
    protected String type;
    @XmlAttribute(required = true)
    protected String hotelCode;
    @XmlAttribute(required = true)
    protected String codeExp;
    @XmlAttribute(required = true)
    @JsonFormat(pattern = DateUtil.DATE_TIME_T_MILLIS_FORMAT, timezone = "UTC")
    protected XMLGregorianCalendar timeStamp;
    @XmlAttribute(required = true)
    protected String code;
    @XmlAttribute(required = true)
    protected String stamp;
    @XmlAttribute(required = true)
    protected String system;

    /**
     * Gets the value of the dateControl property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the dateControl property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getDateControl().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link DateControl }
     */
    public List<DateControl> getDateControl() {
        if (dateControl == null) {
            dateControl = new ArrayList<DateControl>();
        }
        return this.dateControl;
    }

    /**
     * Gets the value of the levelType property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getLevelType() {
        return levelType;
    }

    /**
     * Sets the value of the levelType property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setLevelType(String value) {
        this.levelType = value;
    }

    /**
     * Gets the value of the type property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getType() {
        return type;
    }

    /**
     * Sets the value of the type property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setType(String value) {
        this.type = value;
    }

    /**
     * Gets the value of the hotelCode property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getHotelCode() {
        return hotelCode;
    }

    /**
     * Sets the value of the hotelCode property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setHotelCode(String value) {
        this.hotelCode = value;
    }

    /**
     * Gets the value of the codeExp property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getCodeExp() {
        return codeExp;
    }

    /**
     * Sets the value of the codeExp property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCodeExp(String value) {
        this.codeExp = value;
    }

    /**
     * Gets the value of the timeStamp property.
     *
     * @return possible object is
     * {@link XMLGregorianCalendar }
     */
    public XMLGregorianCalendar getTimeStamp() {
        return timeStamp;
    }

    /**
     * Sets the value of the timeStamp property.
     *
     * @param value allowed object is
     *              {@link XMLGregorianCalendar }
     */
    public void setTimeStamp(XMLGregorianCalendar value) {
        this.timeStamp = value;
    }

    /**
     * Gets the value of the code property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getCode() {
        return code;
    }

    /**
     * Sets the value of the code property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCode(String value) {
        this.code = value;
    }

    /**
     * Gets the value of the stamp property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getStamp() {
        return stamp;
    }

    /**
     * Sets the value of the stamp property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setStamp(String value) {
        this.stamp = value;
    }

    /**
     * Gets the value of the system property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getSystem() {
        return system;
    }

    /**
     * Sets the value of the system property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setSystem(String value) {
        this.system = value;
    }

    @Override
    public String toString() {
        return "Upload{" +
                "dateControl=" + dateControl +
                ", levelType='" + levelType + '\'' +
                ", type='" + type + '\'' +
                ", hotelCode='" + hotelCode + '\'' +
                ", codeExp='" + codeExp + '\'' +
                ", timeStamp=" + timeStamp +
                ", code='" + code + '\'' +
                ", stamp='" + stamp + '\'' +
                ", system='" + system + '\'' +
                '}';
    }
}
