package com.ideas.tetris.pacman.services.ngi.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

public class FPLOS extends AbstractDecision {

    @NotNull
    private String roomTypeCode;

    @NotNull
    private String rateCode;

    @NotNull
    private String los1;

    @NotNull
    private String los2;

    @NotNull
    private String los3;

    @NotNull
    private String los4;

    @NotNull
    private String los5;

    @NotNull
    private String los6;

    @NotNull
    private String los7;

    private String correlationId;

    @NotNull
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date arrivalDate;

    public Date getArrivalDate() {
        return arrivalDate;
    }

    public void setArrivalDate(Date arrivalDate) {
        this.arrivalDate = arrivalDate;
    }

    public String getRoomTypeCode() {
        return roomTypeCode;
    }

    public void setRoomTypeCode(String roomTypeCode) {
        this.roomTypeCode = roomTypeCode;
    }

    public String getRateCode() {
        return rateCode;
    }

    public void setRateCode(String rateCode) {
        this.rateCode = rateCode;
    }

    public String getLos1() {
        return los1;
    }

    public void setLos1(String los1) {
        this.los1 = los1;
    }

    public String getLos2() {
        return los2;
    }

    public void setLos2(String los2) {
        this.los2 = los2;
    }

    public String getLos3() {
        return los3;
    }

    public void setLos3(String los3) {
        this.los3 = los3;
    }

    public String getLos4() {
        return los4;
    }

    public void setLos4(String los4) {
        this.los4 = los4;
    }

    public String getLos5() {
        return los5;
    }

    public void setLos5(String los5) {
        this.los5 = los5;
    }

    public String getLos6() {
        return los6;
    }

    public void setLos6(String los6) {
        this.los6 = los6;
    }

    public String getLos7() {
        return los7;
    }

    public void setLos7(String los7) {
        this.los7 = los7;
    }

    public String getCorrelationId() {
        return correlationId;
    }

    public void setCorrelationId(String correlationId) {
        this.correlationId = correlationId;
    }
}
