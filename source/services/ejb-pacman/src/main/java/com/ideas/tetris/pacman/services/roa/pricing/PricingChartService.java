package com.ideas.tetris.pacman.services.roa.pricing;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.businessinsights.BusinessInsightsService;
import com.ideas.tetris.pacman.services.businessinsights.BusinessInsightsViewNameEnum;
import com.ideas.tetris.pacman.services.businessinsights.entity.BusinessInsightsTrans;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompetitors;
import com.ideas.tetris.pacman.services.webrate.service.WebrateShoppingDataService;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class PricingChartService {

    @Autowired
    private DateService dateService;

    @Autowired
    private AccommodationService accommodationService;

    @Autowired
    private WebrateShoppingDataService webrateShoppingDataService;

    @Autowired
    private PacmanConfigParamsService configParamsService;

    @Autowired
    private PricingChartRepository repository;

    @Autowired
    private BusinessInsightsService businessInsightsService;


    private static final String BASE_RTS = "baseRTs";
    private static final String END_DATE = "endDate";
    private static final String PROPERTY_ID = "propertyID";
    private static final String PRODUCT_IDS = "productIds";
    private final Integer MAX_COMPETITORS = 15;
    private final Integer FCST_LENGTH = 730;
    public Map<String,Object> getFilterData() {
        LocalDate startDate = dateService.getCaughtUpJavaLocalDate();
        LocalDate endDate = dateService.getForecastingWindowBDEJavaLocalEndDate();
        List<AccomClass> roomClasses = accommodationService.getAssignedAccomClassesByViewOrder();

        Map<String,Object> dataMap = new LinkedHashMap<>();
        dataMap.put("forecastHistoryDate",startDate.minusDays(FCST_LENGTH+1));
        dataMap.put("startDate",startDate);
        dataMap.put("endDate",endDate);
        dataMap.put("roomClasses",roomClasses);
        dataMap.put("competitors",getWebrateCompetitors());
        return dataMap;
    }

    public List<PricingChartDTO> getPriceData(){
        return isBarDecisionRateOfDayCPEnabled() ? getPricingByDayData() : Collections.emptyList();
    }

    public List<PricingChartDTO> getCompetitorData(){
        return isBarDecisionRateOfDayCPEnabled() ? getCompetitorByDayData() : Collections.emptyList();
    }

    public List<PricingChartDTO> getCeilingFloorData(){
        return isBarDecisionRateOfDayCPEnabled() ? getCeilingFloorByDayData() : Collections.emptyList();
    }

    public List<PricingChartDTO> getADR() {
        QueryParameter queryParameters = getQueryParametersForADR();
        return mapPricingDTOADR(repository.getADR(queryParameters));
    }

    private List<PricingChartDTO> mapPricingDTOADR(List<Object[]> resultSet) {
        if (resultSet == null) {
            return Collections.emptyList();
        }
        BigDecimal maskedADRValue = new BigDecimal(-99999999.00000);
        return resultSet.stream()
                .map(row -> {
                    PricingChartDTO dto = new PricingChartDTO();
                    dto.setOccupancyDate(LocalDate.parse(row[0].toString()));
                    dto.setDayOfWeek((String) row[3]);
                    dto.setAccomClass((String) row[1]);

                    BigDecimal adr = (BigDecimal) row[2];

                    if(Objects.isNull(adr)) {
                        dto.setAdr(BigDecimal.ZERO);
                    } else if (BigDecimalUtil.equals(adr, maskedADRValue)) {
                        dto.setAdr(null);
                    } else {
                        dto.setAdr(adr);
                    }
                    return dto;
                })
                .collect(Collectors.toList());
    }

    public QueryParameter getQueryParametersForADR() {
        Date businessDate = dateService.getBusinessDate();
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        final Class<? extends BusinessInsightsTrans> transactions = businessInsightsService.getViewEntityClassBasedOnChannelCostVersion();
        BusinessInsightsViewNameEnum viewNameEnum = BusinessInsightsViewNameEnum.getEnumByValue(transactions.getSimpleName());

        return QueryParameter.with("property_id", propertyId)
                .and("businessDate", businessDate)
                .and("transactions", viewNameEnum.getValue())
                .and("FCST_LENGTH",FCST_LENGTH);
    }

    public List<PricingChartDTO> getCeilingFloorByDayData(){
        QueryParameter queryParameter = getCeilingFloorQueryParameters(getBaseRTIDs(),dateService.getForecastingWindowBDEJavaLocalEndDate(),getBarProductID());
        List<Object[]> result = repository.getCeilingFloorDateWise(queryParameter);
        return mapPricingDTOForCeilingFloor(result);
    }



    public List<PricingChartDTO> getPricingByDayData() {
        LocalDate startDate = dateService.getCaughtUpJavaLocalDate();
        LocalDate endDate = dateService.getForecastingWindowBDEJavaLocalEndDate();
        QueryParameter queryParameters = getDefaultPricingQueryParameters(startDate,endDate);
        StringBuilder compIdPramString = appendCompetitorQueries(queryParameters,false);

        List<Object[]> resultSet = repository.getPricingByDayCP(queryParameters,compIdPramString);
        return mapPricingDTO(resultSet);
    }
    public List<PricingChartDTO> getCompetitorByDayData() {
        LocalDate startDate = dateService.getCaughtUpJavaLocalDate().minusDays(FCST_LENGTH+1);
        LocalDate endDate = dateService.getForecastingWindowBDEJavaLocalEndDate();
        QueryParameter queryParameters = getDefaultPricingQueryParameters(startDate,endDate);
        StringBuilder compIdPramString = appendCompetitorQueries(queryParameters,true);

        List<Object[]> resultSet = repository.getPricingByDayCP(queryParameters,compIdPramString);
        return mapCompetitorDTO(resultSet);
    }

    private QueryParameter getCeilingFloorQueryParameters(String baseRTs, LocalDate endDate,String productIds){
        return QueryParameter.with(BASE_RTS, baseRTs)
                .and(END_DATE,endDate)
                .and(PRODUCT_IDS,productIds)
                .and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
    }

    private QueryParameter getDefaultPricingQueryParameters(LocalDate startDate, LocalDate endDate) {
        String baseRTStr = getBaseRTIDs();
        String BARProductID = getBarProductID();

        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        return QueryParameter.with("property_id", propertyId)
                .and("start_date", java.sql.Date.valueOf(startDate))
                .and("end_date", java.sql.Date.valueOf(endDate))
                .and("RoomClasses", baseRTStr)
                .and("isRollingDate", 0)
                .and("rolling_start_date", StringUtils.EMPTY)
                .and("rolling_end_date", StringUtils.EMPTY)
                .and("products",BARProductID)
                .and("filterDtsWhenMatchesGFO",0)
                .and("includeIndependentProductsInReport","1")
                .and("isRdlEnabled",isRDLEnabled() ? "1" : "0");

    }

    private StringBuilder appendCompetitorQueries(QueryParameter queryParameters, boolean getActualCompetitor) {
        List<Integer> competitorIds = getActualCompetitor ? getCompetitorIDs() : getEmptyCompetitorIDs();

        StringBuilder compIdParamString = new StringBuilder();
        int size = competitorIds.size();
        for (int i = 0; i < MAX_COMPETITORS; i++) {
            String paramName = "comp_id_" + (i + 1);
            if (i > 0) {
                compIdParamString.append(", ");
            }
            compIdParamString.append(":").append(paramName);
            if(i<size){
                queryParameters.and(paramName, competitorIds.get(i) != null ? competitorIds.get(i) : -1);
            }else{
                queryParameters.and(paramName,-1);
            }
        }

        return compIdParamString;
    }


    private List<PricingChartDTO> mapPricingDTO(List<Object[]> resultSet) {
        if (resultSet == null) {
            return Collections.emptyList();
        }

        return resultSet.stream()
                .map(row -> {
                    PricingChartDTO dto = new PricingChartDTO();
                    dto.setOccupancyDate(LocalDate.parse(row[0].toString()));
                    dto.setDayOfWeek((String) row[2]);
                    dto.setSystemPrice((BigDecimal) row[10]);
                    dto.setAccomClass((String) row[11]);
                    dto.setLRV((BigDecimal) row[43]);
                    return dto;
                })
                .collect(Collectors.toList());
    }

    private List<PricingChartDTO> mapCompetitorDTO(List<Object[]> resultSet) {
        if (resultSet == null) {
            return Collections.emptyList();
        }

        return resultSet.stream()
                .map(row -> {
                    PricingChartDTO dto = new PricingChartDTO();
                    dto.setOccupancyDate(LocalDate.parse(row[0].toString()));
                    dto.setDayOfWeek((String) row[2]);
                    dto.setAccomClass((String) row[11]);
                    dto.setCompetitorPrices(mapCompetitors(row));
                    return dto;
                })
                .collect(Collectors.toList());
    }

    private List<CompetitorPriceDTO> mapCompetitors(Object[] row) {
        List<CompetitorPriceDTO> competitorPriceDTOS = new ArrayList<>();
        for (int i = 13; i < 42; i += 2) {
            if (row[i+1] == null) {
                break;
            }
            competitorPriceDTOS.add(new CompetitorPriceDTO((String) row[i+1], (BigDecimal) row[i]));
        }
        return competitorPriceDTOS;
    }

    private List<PricingChartDTO> mapPricingDTOForCeilingFloor(List<Object[]> resultSet) {
        if (resultSet == null) {
            return Collections.emptyList();
        }

        return resultSet.stream()
                .map(row -> {
                    PricingChartDTO dto = new PricingChartDTO();
                    dto.setOccupancyDate(LocalDate.parse(row[0].toString()));
                    dto.setDayOfWeek((String) row[1]);
                    dto.setFloor((BigDecimal) row[2]);
                    dto.setCeiling((BigDecimal) row[3]);
                    dto.setAccomClass((String) row[6]);
                    return dto;
                })
                .collect(Collectors.toList());
    }

    public boolean isBarDecisionRateOfDayCPEnabled() {
        return Constants.BAR_DECISION_VALUE_RATEOFDAY.equalsIgnoreCase(
                configParamsService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value())
        ) && configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value());
    }

    public List<AccomType> getBaseRT()
    {
        return repository.getBaseRT();
    }


    private String getBaseRTIDs() {
        List<AccomType> rts = repository.getBaseRT();
        return (rts == null || rts.isEmpty())
                ? StringUtils.EMPTY
                : rts.stream()
                .map(at -> at.getId().toString())
                .collect(Collectors.joining(","));
    }
    private List<Integer> getCompetitorIDs() {
        return getWebrateCompetitors().stream().map(WebrateCompetitors::getId).collect(Collectors.toList());
    }

    private List<Integer> getEmptyCompetitorIDs() {
        return Stream.generate(() -> -1)
                .limit(15)
                .collect(Collectors.toList());
    }

    private String getBarProductID() {
        Product barProduct = accommodationService.getBARProduct();
        return (barProduct != null) ? barProduct.getId().toString() : StringUtils.EMPTY;
    }

    public boolean isRDLEnabled(){
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED.value());
    }

    public List<WebrateCompetitors> getWebrateCompetitors(){
        return webrateShoppingDataService.getAllCompetitorsByProperty().stream().filter(com-> com.getIsSelfCompetitor()==0).limit(MAX_COMPETITORS).collect(Collectors.toList());
    }
}
