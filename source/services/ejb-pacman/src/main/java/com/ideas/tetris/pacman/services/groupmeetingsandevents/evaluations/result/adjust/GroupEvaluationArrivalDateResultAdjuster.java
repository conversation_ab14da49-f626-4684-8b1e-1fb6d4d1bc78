package com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations.result.adjust;

import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.functionspace.evaluation.results.adjustedoutput.AdjustmentOutputUtil;
import com.ideas.tetris.pacman.services.functionspace.evaluation.results.adjustedoutput.UserAdjustmentArrivalDateWrapper;
import com.ideas.tetris.pacman.services.functionspace.util.GuestRoomRentalEnum;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations.dto.result.adjustment.UserAdjustmentOverride;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations.dto.result.adjustment.UserAdjustmentOverride.UserAdjustmentType;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations.dto.result.adjustment.UserAdjustmentsRequest;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations.dto.result.adjustment.UserAdjustmentsResponse;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationArrivalDateAccomType;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationFunctionSpacePackageDetail;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.packaging.GroupEvaluationGroupPricingPackageDetail;
import com.ideas.tetris.pacman.services.tax.util.TaxUtil;
import com.ideas.tetris.pacman.util.BigDecimalUtil;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;

import static com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations.dto.result.adjustment.UserAdjustmentOverride.UserAdjustmentType.*;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.convertJavaToJodaLocalDate;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.convertJodaToJavaLocalDate;
import static java.util.Collections.emptyList;
import static java.util.Collections.singletonList;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.toList;

public class GroupEvaluationArrivalDateResultAdjuster {

    private final UserAdjustmentArrivalDateWrapper userAdjustmentArrivalDateWrapper;
    private final boolean disAllowNegativeValueForUserAdjustedFSEval;
    private final boolean useGroupPricingEvaluationForFSRMGuestRoomOnlyRequest;

    public GroupEvaluationArrivalDateResultAdjuster(UserAdjustmentArrivalDateWrapper userAdjustmentArrivalDateWrapper,
                                                    boolean disAllowNegativeValueForUserAdjustedFSEval,
                                                    boolean useGroupPricingEvaluationForFSRMGuestRoomOnlyRequest) {
        this.userAdjustmentArrivalDateWrapper = userAdjustmentArrivalDateWrapper;
        this.disAllowNegativeValueForUserAdjustedFSEval = disAllowNegativeValueForUserAdjustedFSEval;
        this.useGroupPricingEvaluationForFSRMGuestRoomOnlyRequest = useGroupPricingEvaluationForFSRMGuestRoomOnlyRequest;
    }

    public void applyExistingUserAdjustments(UserAdjustmentsRequest userAdjustmentsRequest) {
        Optional.ofNullable(userAdjustmentsRequest.getAdjustments())
                .orElse(emptyList())
                .stream()
                .filter(userAdjustmentOverride -> isNull(userAdjustmentOverride.getIsAdjusted()))
                .forEach(userAdjustmentOverride -> {
                    switch (userAdjustmentOverride.getAdjustmentType()) {
                        case GUEST_ROOM_ROH:
                            applyGuestRoomROHAdjustment(userAdjustmentOverride);
                            break;
                        case GUEST_ROOM_RC:
                            applyGuestRoomRCAdjustment(userAdjustmentOverride);
                            break;
                        case GUEST_ROOM_ROH_PER_NIGHT:
                            applyGuestRoomROHPerNightAdjustment(userAdjustmentOverride);
                            break;
                        case CONFERENCE_AND_BANQUET:
                            applyConfAndBanqRevenueStreamAdjustment(userAdjustmentOverride);
                            break;
                        default: break;
                    }
                });
    }

    public UserAdjustmentsResponse buildUserAdjustmentsResponse(UserAdjustmentsRequest userAdjustmentsRequest) {
        UserAdjustmentsResponse userAdjustmentsResponse = new UserAdjustmentsResponse(userAdjustmentsRequest);
        updateOverrides(userAdjustmentsRequest, userAdjustmentsResponse);
        updateTotals(userAdjustmentsResponse);
        return userAdjustmentsResponse;
    }

    private void updateOverrides(UserAdjustmentsRequest userAdjustmentsRequest,
                                 UserAdjustmentsResponse userAdjustmentsResponse) {
        List<UserAdjustmentOverride> overrides = getOverrides(userAdjustmentsRequest);
        userAdjustmentsResponse.setAdjustments(overrides);
    }

    private void updateTotals(UserAdjustmentsResponse userAdjustmentsResponse) {
        userAdjustmentsResponse.setContractualRevenue(getUserAdjustedContractedRevenue());
        userAdjustmentsResponse.setProfitPercentage(userAdjustmentArrivalDateWrapper.getUserAdjustedProfitPercentage());
    }

    private BigDecimal getUserAdjustedContractedRevenue() {
        if (useGroupPricingEvaluationForFSRMGuestRoomOnlyRequest) {
            return userAdjustmentArrivalDateWrapper.getUserAdjustedContractedRevenueForFSRMGuestRoomOnlyRequest();
        }
        return userAdjustmentArrivalDateWrapper.getUserAdjustedContractedRevenueForGPEvaluation();
    }

    private List<UserAdjustmentOverride> getOverrides(UserAdjustmentsRequest userAdjustmentsRequest) {
        UserAdjustmentOverride userAdjustedRevenueStream = filterAdjustmentOverride(userAdjustmentsRequest, UserAdjustmentOverride::isAdjustedByUser);
        if (isNull(userAdjustedRevenueStream)) {
            return emptyList();
        }

        BigDecimal profitBeforeAdjustment = sumOfProfit(userAdjustmentsRequest.getAdjustments(), true);
        List<UserAdjustmentOverride> overrides = calculateUserAdjustmentOverrides(userAdjustedRevenueStream);
        UserAdjustmentOverride revenueStreamToAdjust = filterAdjustmentOverride(userAdjustmentsRequest,
                userAdjustmentOverride -> nonNull(userAdjustmentOverride.getIsAdjusted()) && !userAdjustmentOverride.getIsAdjusted());
        if (nonNull(revenueStreamToAdjust)) {
            List<UserAdjustmentOverride> adjustmentOverrides = adjustRevenueStream(revenueStreamToAdjust, userAdjustmentsRequest.getAdjustments(), profitBeforeAdjustment);
            overrides.addAll(adjustmentOverrides);
        } else {
            overrides.addAll(updateGuestRoomROHRateForPerNightAdjustment(userAdjustmentsRequest));
        }

        return overrides;
    }

    private List<UserAdjustmentOverride> updateGuestRoomROHRateForPerNightAdjustment(UserAdjustmentsRequest userAdjustmentsRequest) {
        return onlyOneROHPerNightRateWasOverridden(userAdjustmentsRequest) ?
                getGuestRoomROHAdjustmentForPerNightOverride() :
                emptyList();
    }

    private boolean onlyOneROHPerNightRateWasOverridden(UserAdjustmentsRequest userAdjustmentsRequest) {
        UserAdjustmentOverride userAdjustedRevenueStream = filterAdjustmentOverride(userAdjustmentsRequest, UserAdjustmentOverride::isAdjustedByUser);
        UserAdjustmentOverride revenueStreamToAdjust = filterAdjustmentOverride(userAdjustmentsRequest,
                userAdjustmentOverride -> nonNull(userAdjustmentOverride.getIsAdjusted()) && !userAdjustmentOverride.getIsAdjusted());
        return GUEST_ROOM_ROH_PER_NIGHT.equals(userAdjustedRevenueStream.getAdjustmentType()) && isNull(revenueStreamToAdjust);
    }

    private List<UserAdjustmentOverride> calculateUserAdjustmentOverrides(UserAdjustmentOverride userAdjustmentOverrideStream) {
        switch (userAdjustmentOverrideStream.getAdjustmentType()) {
            case GUEST_ROOM_ROH:
                List<UserAdjustmentOverride> userAdjustmentOverrides = new ArrayList<>(getGuestRoomROHAdjustment(userAdjustmentOverrideStream));
                userAdjustmentOverrides.addAll(getPackageRateAdjustments());
                return userAdjustmentOverrides;
            case GUEST_ROOM_RC:
                return getGuestRoomRCAdjustment(userAdjustmentOverrideStream);
            case GUEST_ROOM_ROH_PER_NIGHT:
                return getGuestRoomROHPerNightAdjustment(userAdjustmentOverrideStream);
            case CONFERENCE_AND_BANQUET:
                return getConfAndBanqRevenueStreamAdjustments(userAdjustmentOverrideStream);
            default:
                return emptyList();
        }
    }

    private List<UserAdjustmentOverride> getGuestRoomROHAdjustmentForPerNightOverride() {
        BigDecimal blendedRevenue = userAdjustmentArrivalDateWrapper.getRohGuestRoomRatesWrapperList()
                .stream()
                .map(wrapper -> wrapper.getUserAdjustedRate().multiply(BigDecimal.valueOf(wrapper.getNumberOfRooms())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal blendedGuestRoomRate = BigDecimalUtil.divide(blendedRevenue, userAdjustmentArrivalDateWrapper.getTotalNumberOfRooms());
        BigDecimal adjustedGuestRoomRate = userAdjustmentArrivalDateWrapper.isIncludeTax() ? blendedGuestRoomRate :
                TaxUtil.removeTaxFromRoomRate(userAdjustmentArrivalDateWrapper.getTaxRate(), blendedGuestRoomRate);
        userAdjustmentArrivalDateWrapper.setUserAdjustedRoomRate(blendedGuestRoomRate);
        return new ArrayList<>(buildGuestRoomROHOverride(adjustedGuestRoomRate));
    }

    private static UserAdjustmentOverride filterAdjustmentOverride(UserAdjustmentsRequest userAdjustmentsRequest,
                                                                   Predicate<UserAdjustmentOverride> userAdjustmentOverridePredicate) {
        return Optional.ofNullable(userAdjustmentsRequest.getAdjustments())
                .orElse(emptyList())
                .stream()
                .filter(userAdjustmentOverridePredicate)
                .findFirst()
                .orElse(null);
    }

    private List<UserAdjustmentOverride> getGuestRoomROHAdjustment(UserAdjustmentOverride userAdjustmentOverride) {
        applyGuestRoomROHAdjustment(userAdjustmentOverride);
        BigDecimal userAdjustedROHRate = userAdjustmentArrivalDateWrapper.isIncludeTax() ?
                userAdjustmentArrivalDateWrapper.getUserAdjustedRoomRate() :
                userAdjustmentArrivalDateWrapper.getUserAdjustedRoomRateMinusTax();
        return Optional.ofNullable(userAdjustedROHRate)
                .map(this::buildGuestRoomROHOverride)
                .orElse(emptyList());
    }

    private void applyGuestRoomROHAdjustment(UserAdjustmentOverride userAdjustmentOverride) {
        userAdjustmentArrivalDateWrapper.setUserAdjustedRoomRate(userAdjustmentOverride.getOverrideRate());
    }

    private List<UserAdjustmentOverride> getGuestRoomRCAdjustment(UserAdjustmentOverride userAdjustmentOverrideStream) {
        applyGuestRoomRCAdjustment(userAdjustmentOverrideStream);
        return buildGuestRoomRCOverride(userAdjustmentOverrideStream);
    }

    private List<UserAdjustmentOverride> buildGuestRoomRCOverride(UserAdjustmentOverride userAdjustmentOverrideStream) {
        BigDecimal userAdjustedRate = userAdjustmentArrivalDateWrapper.isIncludeTax() ? userAdjustmentOverrideStream.getOverrideRate() :
                TaxUtil.removeTaxFromRoomRate(userAdjustmentArrivalDateWrapper.getTaxRate(), userAdjustmentOverrideStream.getOverrideRate());
        UserAdjustmentOverride override = new UserAdjustmentOverride();
        override.setAdjustmentType(GUEST_ROOM_RC);
        override.setRoomTypeId(userAdjustmentOverrideStream.getRoomTypeId());
        override.setOverrideRate(userAdjustedRate);
        return new ArrayList<>(List.of(override));
    }

    private List<UserAdjustmentOverride> getGuestRoomROHPerNightAdjustment(UserAdjustmentOverride userAdjustmentOverrideStream) {
        applyGuestRoomROHPerNightAdjustment(userAdjustmentOverrideStream);
        return buildGuestRoomROHPerNightOverride(userAdjustmentOverrideStream);
    }

    private List<UserAdjustmentOverride> buildGuestRoomROHPerNightOverride(UserAdjustmentOverride userAdjustmentOverrideStream) {
        BigDecimal userAdjustedRate = userAdjustmentArrivalDateWrapper.isIncludeTax() ? userAdjustmentOverrideStream.getOverrideRate() :
                TaxUtil.removeTaxFromRoomRate(userAdjustmentArrivalDateWrapper.getTaxRate(), userAdjustmentOverrideStream.getOverrideRate());
        UserAdjustmentOverride override = new UserAdjustmentOverride();
        override.setAdjustmentType(GUEST_ROOM_ROH_PER_NIGHT);
        override.setStayDate(userAdjustmentOverrideStream.getStayDate());
        override.setOverrideRate(userAdjustedRate);
        return new ArrayList<>(List.of(override));
    }

    private void applyGuestRoomROHPerNightAdjustment(UserAdjustmentOverride userAdjustmentOverride) {
        LocalDate stayDate = Optional.ofNullable(userAdjustmentOverride.getStayDate())
                .orElseThrow(() -> new IllegalArgumentException("Stay Date must be specified for user adjustment override of type GUEST_ROOM_ROH_PER_NIGHT"));
        filterCorrespondingROHPerNightWrapper(stayDate)
                .ifPresent(wrapper -> wrapper.setUserAdjustmentRate(userAdjustmentOverride.getOverrideRate()));
    }

    private Optional<UserAdjustmentArrivalDateWrapper.RohGuestRoomRatesWrapper> filterCorrespondingROHPerNightWrapper(LocalDate stayDate) {
        return userAdjustmentArrivalDateWrapper.getRohGuestRoomRatesWrapperList().stream()
                .filter(rohWrapper -> stayDate.equals(convertJodaToJavaLocalDate(rohWrapper.getOccupancyDate())))
                .findFirst();
    }

    private void applyGuestRoomRCAdjustment(UserAdjustmentOverride userAdjustmentOverride) {
        Optional<GroupEvaluationArrivalDateAccomType> arrivalDateAccomType = getGroupEvaluationArrivalDateAccomType(userAdjustmentOverride.getRoomTypeId());
        arrivalDateAccomType.ifPresent(accomType -> accomType.setUserAdjustedRate(userAdjustmentOverride.getOverrideRate()));
    }

    private List<UserAdjustmentOverride> buildGuestRoomROHOverride(BigDecimal userAdjustedGuestRoomRate) {
        UserAdjustmentOverride override = new UserAdjustmentOverride();
        override.setAdjustmentType(GUEST_ROOM_ROH);
        override.setOverrideRate(userAdjustedGuestRoomRate);
        return singletonList(override);
    }

    private List<UserAdjustmentOverride> getPackageRateAdjustments() {
        userAdjustmentArrivalDateWrapper.applyPackageRateAdjustments();
        return buildPackageRateAdjustmentOverrides();
    }

    private List<UserAdjustmentOverride> buildPackageRateAdjustmentOverrides() {
        if (userAdjustmentArrivalDateWrapper.shouldUseFSObjectsForPackageResult()) {
            return userAdjustmentArrivalDateWrapper.getGroupEvalFSPackageUserAdjustmentWrapperList().stream()
                    .map(GroupEvaluationArrivalDateResultAdjuster::buildPackageRateAdjustmentUsingFSObjects)
                    .collect(toList());
        } else if (userAdjustmentArrivalDateWrapper.checkIfGroupPricingPackageChangesAreApplicable()) {
            return userAdjustmentArrivalDateWrapper.getUserAdjustmentGPPackageWrapperList().stream()
                    .map(GroupEvaluationArrivalDateResultAdjuster::buildGPPackageRateAdjustmentGPObjects)
                    .collect(toList());
        }

        return emptyList();
    }

    private static UserAdjustmentOverride buildGPPackageRateAdjustmentGPObjects(UserAdjustmentArrivalDateWrapper.GroupEvalGPPackageUserAdjustmentWrapper packageWrapper) {
        GroupEvaluationGroupPricingPackageDetail packageDetail = packageWrapper.getPackageRevenueByArrivalDate().getGroupEvaluationGroupPricingPackageDetail();
        UserAdjustmentOverride userAdjustmentOverride = new UserAdjustmentOverride();
        userAdjustmentOverride.setPackageName(packageDetail.getPackageName());
        userAdjustmentOverride.setPackageCategory(GuestRoomRentalEnum.getValueFromCaptionKey(packageDetail.getPackageCategory()).name());
        userAdjustmentOverride.setOverrideRate(packageWrapper.getUserAdjustedPackageRate());
        userAdjustmentOverride.setAdjustmentType(UserAdjustmentOverride.UserAdjustmentType.PACKAGE_REVENUE);
        return userAdjustmentOverride;
    }

    private static UserAdjustmentOverride buildPackageRateAdjustmentUsingFSObjects(UserAdjustmentArrivalDateWrapper.GroupEvalFSPackageUserAdjustmentWrapper packageWrapper) {
        GroupEvaluationFunctionSpacePackageDetail packageDetail = packageWrapper.getGroupEvaluationFunctionSpaceArrivalDatePackage().getGroupEvaluationFunctionSpacePackageDetail();
        UserAdjustmentOverride userAdjustmentOverride = new UserAdjustmentOverride();
        userAdjustmentOverride.setPackageName(packageDetail.getPackageName());
        userAdjustmentOverride.setPackageCategory(GuestRoomRentalEnum.getValueFromCaptionKey(packageDetail.getPackageCategory()).name());
        userAdjustmentOverride.setOverrideRate(packageWrapper.getUserAdjustedPackageRate());
        userAdjustmentOverride.setAdjustmentType(UserAdjustmentOverride.UserAdjustmentType.PACKAGE_REVENUE);
        return userAdjustmentOverride;
    }

    private List<UserAdjustmentOverride> getConfAndBanqRevenueStreamAdjustments(UserAdjustmentOverride userAdjustmentOverride) {
        applyConfAndBanqRevenueStreamAdjustment(userAdjustmentOverride);
        return new ArrayList<>(List.of(userAdjustmentOverride));
    }

    private void applyConfAndBanqRevenueStreamAdjustment(UserAdjustmentOverride userAdjustmentOverride) {
        Optional<UserAdjustmentArrivalDateWrapper.RevenueStreamWrapper> revenueStreamWrapper = filterCorrespondingRevenueStreamWrapper(userAdjustmentOverride.getRevenueStreamName());
        revenueStreamWrapper.ifPresent(revenueStream ->
                userAdjustmentArrivalDateWrapper.updateFunctionSpaceConfAndBanqStream(revenueStream, userAdjustmentOverride.getOverrideRate()));
    }

    private Optional<UserAdjustmentArrivalDateWrapper.RevenueStreamWrapper> filterCorrespondingRevenueStreamWrapper(String revenueStreamName) {
        return userAdjustmentArrivalDateWrapper.getRevenueStreams().stream()
                .filter(revenueStream -> revenueStream.getName().equals(revenueStreamName))
                .findFirst();
    }

    protected List<UserAdjustmentOverride> adjustRevenueStream(UserAdjustmentOverride revenueStreamToAdjust,
                                       List<UserAdjustmentOverride> userAdjustmentOverrides,
                                       BigDecimal profitBeforeAdjustment) {
        switch (revenueStreamToAdjust.getAdjustmentType()) {
            case GUEST_ROOM_ROH:
                return adjustGuestRoomROH(revenueStreamToAdjust, userAdjustmentOverrides, profitBeforeAdjustment);
            case GUEST_ROOM_RC:
                return adjustGuestRoomRC(revenueStreamToAdjust, userAdjustmentOverrides, profitBeforeAdjustment);
            case GUEST_ROOM_ROH_PER_NIGHT:
                return adjustGuestRoomROHPerNight(revenueStreamToAdjust, userAdjustmentOverrides, profitBeforeAdjustment);
            case CONFERENCE_AND_BANQUET:
                return adjustConferenceBanquetRevenueStream(revenueStreamToAdjust, userAdjustmentOverrides, profitBeforeAdjustment);
            default:
                return emptyList();
        }
    }

    private List<UserAdjustmentOverride> adjustGuestRoomROHPerNight(UserAdjustmentOverride revenueStreamToAdjust,
                                                                    List<UserAdjustmentOverride> userAdjustmentOverrides,
                                                                    BigDecimal profitBeforeAdjustment) {
        LocalDate stayDate = revenueStreamToAdjust.getStayDate();
        BigDecimal numberOfRoomsOnStayDate = userAdjustmentArrivalDateWrapper.getNumberOfRoomsForOccupancyDate(convertJavaToJodaLocalDate(stayDate));
        BigDecimal blendedGuestRoomRateRevenue = userAdjustmentArrivalDateWrapper.getUserAdjustedRoomRate().multiply(userAdjustmentArrivalDateWrapper.getTotalNumberOfRooms());
        BigDecimal revenueOfOtherStayDates = userAdjustmentArrivalDateWrapper.getRohGuestRoomRatesWrapperList()
                .stream()
                .filter(wrapper -> !stayDate.equals(convertJodaToJavaLocalDate(wrapper.getOccupancyDate())))
                .map(wrapper -> wrapper.getUserAdjustedRate().multiply(BigDecimal.valueOf(wrapper.getNumberOfRooms())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal newRate = BigDecimalUtil.divide(blendedGuestRoomRateRevenue.subtract(revenueOfOtherStayDates), numberOfRoomsOnStayDate);

        BigDecimal newRateWithoutTax = TaxUtil.removeTaxFromRoomRate(userAdjustmentArrivalDateWrapper.getTaxRate(), newRate);
        BigDecimal adjustedRate = userAdjustmentArrivalDateWrapper.isIncludeTax() ? newRate : newRateWithoutTax;
        if (shouldAdjustNegativeRevenueToZero(adjustedRate)) {
            adjustedRate = BigDecimal.ZERO;
        }
        revenueStreamToAdjust.setOverrideRate(adjustedRate);
        updateGuestRoomOverrideForStayDate(newRate, stayDate);
        return List.of(revenueStreamToAdjust);
    }

    private List<UserAdjustmentOverride> adjustGuestRoomROH(UserAdjustmentOverride revenueStreamToAdjust,
                                                            List<UserAdjustmentOverride> userAdjustmentOverrides,
                                                            BigDecimal profitBeforeAdjustment) {
        BigDecimal profitWithoutGuestRoom = sumOfProfit(userAdjustmentOverrides, false);
        BigDecimal profitForGuestRoom = profitBeforeAdjustment.subtract(profitWithoutGuestRoom);
        BigDecimal totalPerRoomCost = userAdjustmentArrivalDateWrapper.getPerRoomServicingCost().multiply(userAdjustmentArrivalDateWrapper.getTotalNumberOfRooms());
        profitForGuestRoom = profitForGuestRoom.add(totalPerRoomCost);
        BigDecimal newRate = AdjustmentOutputUtil.getRate(profitForGuestRoom, userAdjustmentArrivalDateWrapper);

        if (userAdjustmentArrivalDateWrapper.isIncludeTax()) {
            newRate = TaxUtil.applyTaxToRoomRate(userAdjustmentArrivalDateWrapper.getTaxRate(), newRate);
        }
        if (shouldAdjustNegativeRevenueToZero(newRate)) {
            newRate = BigDecimal.ZERO;
        }
        revenueStreamToAdjust.setOverrideRate(newRate);
        userAdjustmentArrivalDateWrapper.setUserAdjustedRoomRate(newRate);
        return List.of(revenueStreamToAdjust);
    }

    private List<UserAdjustmentOverride> adjustGuestRoomRC(UserAdjustmentOverride revenueStreamToAdjust,
                                                           List<UserAdjustmentOverride> userAdjustments,
                                                           BigDecimal profitBeforeAdjustment) {
        BigDecimal profitAfterAdjustment = sumOfProfit(userAdjustments, true);
        BigDecimal profitDifference = profitBeforeAdjustment.subtract(profitAfterAdjustment);
        BigDecimal newProfitForRevenueStreamToBeAdjusted = getProfit(revenueStreamToAdjust).add(profitDifference);
        BigDecimal newRate = AdjustmentOutputUtil.getRate(newProfitForRevenueStreamToBeAdjusted,
                                                          getNumberOfRoomsForAccomType(revenueStreamToAdjust.getRoomTypeId()),
                                                          userAdjustmentArrivalDateWrapper.getTotalNumberOfRooms(),
                                                          getAccomTypeForRCAdjustment(revenueStreamToAdjust),
                                                          userAdjustmentArrivalDateWrapper);
        BigDecimal newRateWithTax = TaxUtil.applyTaxToRoomRate(userAdjustmentArrivalDateWrapper.getTaxRate(), newRate);
        BigDecimal adjustedRate = userAdjustmentArrivalDateWrapper.isIncludeTax() ? newRateWithTax : newRate;
        if (shouldAdjustNegativeRevenueToZero(adjustedRate)) {
            adjustedRate = BigDecimal.ZERO;
        }
        revenueStreamToAdjust.setOverrideRate(adjustedRate);
        updateGuestRoomOverrideForAccomType(newRateWithTax, revenueStreamToAdjust.getRoomTypeId());
        return List.of(revenueStreamToAdjust);
    }

    private List<UserAdjustmentOverride> adjustConferenceBanquetRevenueStream(UserAdjustmentOverride revenueStreamToAdjust,
                                                                              List<UserAdjustmentOverride> userAdjustments,
                                                                              BigDecimal profitBeforeAdjustment) {
        Optional<UserAdjustmentArrivalDateWrapper.RevenueStreamWrapper> revenueStreamWrapper = filterCorrespondingRevenueStreamWrapper(revenueStreamToAdjust.getRevenueStreamName());
        if (revenueStreamWrapper.isEmpty()) {
            return emptyList();
        }

        BigDecimal profitAfterAdjustment = sumOfProfit(userAdjustments, true);
        BigDecimal newAdjustedContractualRevenue = calculateNewConferenceBanquetRate(profitBeforeAdjustment, profitAfterAdjustment, revenueStreamWrapper.get());
        revenueStreamToAdjust.setOverrideRate(newAdjustedContractualRevenue);
        userAdjustmentArrivalDateWrapper.updateFunctionSpaceConfAndBanqStream(revenueStreamWrapper.get(), newAdjustedContractualRevenue);
        return List.of(revenueStreamToAdjust);
    }

    private BigDecimal calculateNewConferenceBanquetRate(BigDecimal profitBeforeAdjustment,
                                                         BigDecimal sumOfProfitAfterAdjustment,
                                                         UserAdjustmentArrivalDateWrapper.RevenueStreamWrapper revenueStreamWrapper) {
        // confBanqProfit = (revenue * (1 - commissionPercentage)) * profitPercentage
        BigDecimal profitDifference = profitBeforeAdjustment.subtract(sumOfProfitAfterAdjustment);
        BigDecimal oneMinusCommissionFactor = BigDecimalUtil.subtract(BigDecimal.ONE, revenueStreamWrapper.getCommissionPercentage());
        BigDecimal contractualRevenueToBeAdjusted = BigDecimalUtil.divide(profitDifference, revenueStreamWrapper.getProfitPercent());
        contractualRevenueToBeAdjusted = BigDecimalUtil.divide(contractualRevenueToBeAdjusted, oneMinusCommissionFactor);

        BigDecimal newAdjustedContractualRevenue = revenueStreamWrapper.getUserAdjustedRevenue().add(contractualRevenueToBeAdjusted);
        if (shouldAdjustNegativeRevenueToZero(newAdjustedContractualRevenue)) {
            newAdjustedContractualRevenue = BigDecimal.ZERO;
        }

        return newAdjustedContractualRevenue;
    }

    private boolean shouldAdjustNegativeRevenueToZero(BigDecimal newAdjustedContractualRevenue) {
        return (newAdjustedContractualRevenue.compareTo(BigDecimal.ZERO) < 0) && (disAllowNegativeValueForUserAdjustedFSEval);
    }

    private BigDecimal sumOfProfit(List<UserAdjustmentOverride> overrides,
                                   boolean includeGuestRoom) {
        return overrides.stream()
                .map(userAdjustmentOverride -> {
                    UserAdjustmentType userAdjustmentType = userAdjustmentOverride.getAdjustmentType();
                    if (shouldIncludeProfit(userAdjustmentType, includeGuestRoom)) {
                        return getProfit(userAdjustmentOverride);
                    }

                    return BigDecimal.ZERO;
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private static boolean shouldIncludeProfit(UserAdjustmentType userAdjustmentType, boolean includeGuestRoom) {
        return !userAdjustmentType.equals(UserAdjustmentType.GUEST_ROOM_ROH_PER_NIGHT) &&
                (includeGuestRoom ||
                 (!userAdjustmentType.equals(UserAdjustmentType.GUEST_ROOM_ROH) &&
                  !userAdjustmentType.equals(UserAdjustmentType.GUEST_ROOM_RC)));
    }

    public BigDecimal getProfit(UserAdjustmentOverride userAdjustmentOverride) {
        switch (userAdjustmentOverride.getAdjustmentType()) {
            case GUEST_ROOM_ROH:
                return AdjustmentOutputUtil.getROHAdjustedProfit(userAdjustmentArrivalDateWrapper, getGuestRoomROHRateMinusTax());
            case GUEST_ROOM_RC:
                return AdjustmentOutputUtil.getRCAdjustedNetProfit(getAccomTypeForRCAdjustment(userAdjustmentOverride),
                                                                   getGuestRoomRCRateMinusTax(userAdjustmentOverride),
                                                                   userAdjustmentArrivalDateWrapper);
            case CONFERENCE_AND_BANQUET:
                Optional<UserAdjustmentArrivalDateWrapper.RevenueStreamWrapper> revenueStreamWrapper = filterCorrespondingRevenueStreamWrapper(userAdjustmentOverride.getRevenueStreamName());
                return revenueStreamWrapper
                        .map(this::getConferenceBanquetProfit)
                        .orElse(BigDecimal.ZERO);
            default:
                return BigDecimal.ZERO;
        }
    }

    private AccomType getAccomTypeForRCAdjustment(UserAdjustmentOverride userAdjustmentOverride) {
        Integer roomTypeId = Optional.ofNullable(userAdjustmentOverride.getRoomTypeId())
                .orElseThrow(() -> new IllegalArgumentException("Room Type Id not specified for user adjustment override of type GUEST_ROOM_RC"));
        Optional<AccomType> accomType = userAdjustmentArrivalDateWrapper.getGroupEvaluationArrivalDateAccomClasses().stream()
                .flatMap(accomClass -> accomClass.getGroupEvaluationArrivalDateAccomTypes().stream())
                .map(GroupEvaluationArrivalDateAccomType::getAccomType)
                .filter(at -> at.getId().equals(roomTypeId))
                .findFirst();
        return accomType.orElseThrow(() -> new IllegalArgumentException("Room Type Id " + userAdjustmentOverride.getRoomTypeId() + " not found for this evaluation"));
    }

    private BigDecimal getGuestRoomRCRateMinusTax(UserAdjustmentOverride userAdjustmentOverride) {
                Integer roomTypeId = userAdjustmentOverride.getRoomTypeId();
        Optional<GroupEvaluationArrivalDateAccomType> arrivalDateAccomType = getGroupEvaluationArrivalDateAccomType(roomTypeId);

        BigDecimal guestRoomRCRate = arrivalDateAccomType
                .map(accomType -> Optional.ofNullable(accomType.getUserAdjustedRate()).orElse(accomType.getRate()))
                .orElseThrow(() -> new IllegalArgumentException("Room Type Id " + userAdjustmentOverride.getRoomTypeId() + " not found for this evaluation"));

        BigDecimal taxRate = userAdjustmentArrivalDateWrapper.getTaxRate();
        if (nonNull(taxRate)) {
            return TaxUtil.removeTaxFromRoomRate(taxRate, guestRoomRCRate);
        }
        return guestRoomRCRate;
    }

    private BigDecimal getGuestRoomROHRateMinusTax() {
        BigDecimal guestRoomRate = userAdjustmentArrivalDateWrapper.getUserAdjustedRoomRate();
        BigDecimal taxRate = userAdjustmentArrivalDateWrapper.getTaxRate();
        if (taxRate != null) {
            return TaxUtil.removeTaxFromRoomRate(taxRate, guestRoomRate);
        }

        return guestRoomRate;
    }

    private BigDecimal getConferenceBanquetProfit(UserAdjustmentArrivalDateWrapper.RevenueStreamWrapper revenueStreamWrapper) {
        BigDecimal confBanqRevenue = revenueStreamWrapper.getUserAdjustedRevenue();
        BigDecimal commissionPercentage = revenueStreamWrapper.getCommissionPercentage();
        BigDecimal profitPercentage = revenueStreamWrapper.getProfitPercent();
        return confBanqRevenue
                    .subtract(confBanqRevenue.multiply(commissionPercentage)).multiply(profitPercentage);
    }

    private void updateGuestRoomOverrideForAccomType(BigDecimal newRate, Integer accomTypeId) {
        Optional<GroupEvaluationArrivalDateAccomType> arrivalDateAccomType = getGroupEvaluationArrivalDateAccomType(accomTypeId);
        arrivalDateAccomType.ifPresent(at -> at.setUserAdjustedRate(newRate));
    }

    private void updateGuestRoomOverrideForStayDate(BigDecimal newRate, LocalDate stayDate) {
        Optional<UserAdjustmentArrivalDateWrapper.RohGuestRoomRatesWrapper> stayDateWrapper =
                userAdjustmentArrivalDateWrapper.getRohGuestRoomRatesWrapperList()
                        .stream()
                        .filter(wrapper -> stayDate.equals(convertJodaToJavaLocalDate(wrapper.getOccupancyDate()))).findFirst();
        stayDateWrapper.ifPresent(wrapper -> wrapper.setUserAdjustmentRate(newRate));
    }

    public Optional<GroupEvaluationArrivalDateAccomType> getGroupEvaluationArrivalDateAccomType(Integer accomTypeId) {
        return userAdjustmentArrivalDateWrapper.getGroupEvaluationArrivalDateAccomClasses().stream()
                .flatMap(geac -> geac.getGroupEvaluationArrivalDateAccomTypes().stream())
                .filter(geat -> geat.getAccomType().getId().equals(accomTypeId))
                .findFirst();
    }

    public BigDecimal getNumberOfRoomsForAccomType(Integer accomtypeId) {
        Optional<GroupEvaluationArrivalDateAccomType> arrivalDateAccomType = getGroupEvaluationArrivalDateAccomType(accomtypeId);
        return arrivalDateAccomType.map(at -> userAdjustmentArrivalDateWrapper.getTotalNumberOfRooms(at.getAccomType())).orElse(BigDecimal.ZERO);
    }
}