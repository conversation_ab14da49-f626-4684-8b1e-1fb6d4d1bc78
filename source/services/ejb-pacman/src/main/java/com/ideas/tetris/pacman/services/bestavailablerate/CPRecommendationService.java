package com.ideas.tetris.pacman.services.bestavailablerate;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesDecisionsSentBy;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesOffsetMethod;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ChildPricingType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomTypeSupplementValue;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigMergedOffset;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionDailybarOutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionDailybarOutputKey;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionDailybarOutputNonHiltonCRS;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OffsetMethod;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.Supplement;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.TableBatch;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.joda.time.LocalDate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.bestavailablerate.CPRecommendationUtils.retrieveAdjustment;
import static com.ideas.tetris.pacman.util.BigDecimalUtil.*;

@Slf4j
@Component
@Transactional
public class CPRecommendationService extends CPBarDecisionService {

    public static final String INSERT_PACE_DAILYBAR_OUTPUT = "insert into PACE_Dailybar_Output(Product_ID,Decision_ID,Occupancy_Date, Accom_Type_ID,Rate_Unqualified_ID,Single_Rate,Double_Rate, Adult_Rate,Child_Rate,Triple_Rate,Quad_Rate,Quint_Rate,One_Child_Rate,Two_Child_Rate,Three_Child_Rate,Four_Child_Rate,Five_Child_Rate,Child_Age_1_Rate,Child_Age_2_Rate,Child_Age_3_Rate,Child_Age_4_Rate) select Product_ID,Decision_ID,Occupancy_Date, Accom_Type_ID, Rate_Unqualified_ID,Single_Rate,Double_Rate,Adult_Rate,Child_Rate,Triple_Rate,Quad_Rate,Quint_Rate,One_Child_Rate,Two_Child_Rate,Three_Child_Rate,Four_Child_Rate,Five_Child_Rate,Child_Age_1_Rate,Child_Age_2_Rate,Child_Age_3_Rate,Child_Age_4_Rate from Decision_Dailybar_Output where decision_id = :decisionId and Occupancy_Date between :startDate and :endDate";
    public static final String INSERT_PACE_DAILYBAR_OUTPUT_NONHILTONCRS = "insert into Pace_Dailybar_Output_NonHiltonCRS(Product_ID,Decision_ID,Occupancy_Date, Accom_Type_ID,Rate_Unqualified_ID,Single_Rate,Double_Rate, Adult_Rate,Child_Rate,Triple_Rate,Quad_Rate,Quint_Rate,One_Child_Rate,Two_Child_Rate,Three_Child_Rate,Four_Child_Rate,Five_Child_Rate,Child_Age_1_Rate,Child_Age_2_Rate,Child_Age_3_Rate,Child_Age_4_Rate) select Product_ID,Decision_ID,Occupancy_Date, Accom_Type_ID, Rate_Unqualified_ID,Single_Rate,Double_Rate,Adult_Rate,Child_Rate,Triple_Rate,Quad_Rate,Quint_Rate,One_Child_Rate,Two_Child_Rate,Three_Child_Rate,Four_Child_Rate,Five_Child_Rate,Child_Age_1_Rate,Child_Age_2_Rate,Child_Age_3_Rate,Child_Age_4_Rate from Decision_Dailybar_Output_NonHiltonCRS where decision_id = :decisionId and Occupancy_Date between :startDate and :endDate";
    private static final String CAUGHTUP_DATE = "caughtUpDate";

    public Integer recommendFinalBARs(Date startDate, Date endDate) {
        // Create a new decisionId
        Integer decisionId = decisionService.createDailyBarDecision().getId();

        // Recommend Decisions
        recommendFinalBARs(decisionId, LocalDate.fromDateFields(startDate), LocalDate.fromDateFields(endDate));

        // Update the Decision status
        decisionService.updateDescisionProcessStatus(decisionId, Constants.PROCESS_STATUS_SUCCESSFUL);

        return decisionId;
    }

    public void recommendFinalBARs(Integer decisionId, LocalDate startDate, LocalDate endDate) {
        // For Continuous Pricing, we still need to identify a rate plan, so we are going to use the default property rate plan which is 'None'
        RateUnqualified defaultRateUnqualified = tenantCrudService.findByNamedQuerySingleResult(RateUnqualified.DEFAULT_BY_PROPERTY_ID, QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());

        // For each CPDecisionBarOutput, check the Decision_Dailybar_Output records to see if they changed
        // by more than the minimum change criteria.  If they did, we need to doSave the new rate values in the DecisionDailybarOutput.
        handleDecisions(startDate, endDate, decisionId, defaultRateUnqualified);

        // handle free upgrade
        handleFreeUpgrade(startDate, endDate);

        // Remove 0 capacity decisions for future Decision_Dailybar_Outputs (non-pace)
        deleteDecisionsOfZeroCapacityAccomTypes();

        // Create Pace Records
        int paceDailybarInsertCount = tenantCrudService.executeUpdateByNativeQuery(INSERT_PACE_DAILYBAR_OUTPUT, QueryParameter.with(DECISION_ID, decisionId).and(START_DATE, startDate).and(END_DATE, endDate).parameters());
        log.debug("Number of records inserted into PACE_Dailybar_Output table are {}", paceDailybarInsertCount);
        if (isNonHiltonCrsSendPriceAdjustmentEnabled()) {
            int paceDailybarNonHiltonInsertCount = tenantCrudService.executeUpdateByNativeQuery(INSERT_PACE_DAILYBAR_OUTPUT_NONHILTONCRS, QueryParameter.with(DECISION_ID, decisionId).and(START_DATE, startDate).and(END_DATE, endDate).parameters());
            log.debug("Number of records inserted into Pace_Dailybar_Output_NonHiltonCRS table are {}", paceDailybarNonHiltonInsertCount);
        }
        tenantCrudService.flush();
    }

    @Override
    protected TableBatch buildTableBatch() {
        if (SystemConfig.isTableBatchForDecisionDailyBAREnabled()) {
            TableBatch tableBatchDecisionDailyBar = new TableBatch("usp_Decision_Dailybar_Output_Update", "Decision_Dailybar_Output_Update");
            tableBatchDecisionDailyBar.addColumn("Decision_Dailybar_Output_ID", Types.BIGINT);
            tableBatchDecisionDailyBar.addColumn("Decision_ID", Types.BIGINT);
            tableBatchDecisionDailyBar.addColumn("Occupancy_Date", Types.DATE);
            tableBatchDecisionDailyBar.addColumn("Accom_Type_ID", Types.INTEGER);
            tableBatchDecisionDailyBar.addColumn("Rate_Unqualified_ID", Types.INTEGER);
            tableBatchDecisionDailyBar.addColumn("Single_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBar.addColumn("Double_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBar.addColumn("Triple_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBar.addColumn("Quad_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBar.addColumn("Quint_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBar.addColumn("Adult_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBar.addColumn("Child_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBar.addColumn("Product_ID", Types.BIGINT);
            tableBatchDecisionDailyBar.addColumn("One_Child_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBar.addColumn("Two_Child_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBar.addColumn("Three_Child_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBar.addColumn("Four_Child_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBar.addColumn("Five_Child_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBar.addColumn("Child_Age_1_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBar.addColumn("Child_Age_2_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBar.addColumn("Child_Age_3_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBar.addColumn("Child_Age_4_Rate", Types.DECIMAL);
            return tableBatchDecisionDailyBar;
        }
        return null;
    }

    @VisibleForTesting
	public
    void deleteDecisionsOfZeroCapacityAccomTypes() {
        List<String> zeroCapacityRoomTypesExcludingHospitalityRooms = hospitalityRoomsService.getZeroCapacityRoomTypesExcludingHospitalityRooms();
        if (CollectionUtils.isEmpty(zeroCapacityRoomTypesExcludingHospitalityRooms)) {
            return;
        }

        tenantCrudService.executeUpdateByNamedQuery(DecisionDailybarOutput.DELETE_FUTURE_DECISIONS_FOR_ZERO_CAPACITY_ROOM_TYPES,
                QueryParameter.with(CAUGHTUP_DATE, new LocalDate(dateService.getCaughtUpDate()))
                        .and("accomTypeCodes", zeroCapacityRoomTypesExcludingHospitalityRooms)
                        .parameters());
        if (isNonHiltonCrsSendPriceAdjustmentEnabled()) {
            tenantCrudService.executeUpdateByNamedQuery(DecisionDailybarOutputNonHiltonCRS.DELETE_FUTURE_DECISIONS_FOR_ZERO_CAPACITY_ROOM_TYPES,
                    QueryParameter.with(CAUGHTUP_DATE, new LocalDate(dateService.getCaughtUpDate()))
                            .and("accomTypeCodes", zeroCapacityRoomTypesExcludingHospitalityRooms)
                            .parameters());
        }
    }

    @SuppressWarnings("squid:S3776")
    @Override
    protected void handleCPBarDecisionChange(Integer decisionId, RateUnqualified defaultRateUnqualified, CPDecisionContext cpDecisionContext, TableBatch tableBatch, TableBatch tableBatchNonHiltonCRS, CPBarDecisionChange cpBarDecisionChange, List<CPDecisionBAROutput> productOutputsForComparison, List<DecisionDailybarOutput> barDecisionDailybarOutputs,
                                             Map<DecisionDailybarOutputKey, DecisionDailybarOutput> previousDecisionDailybarOutputs, Product primaryProduct) {

        if (cpBarDecisionChange.getCPDecisionBAROutput().getProduct().isFreeNightEnabled()) {
            if (!cpDecisionContext.isConsortiaFreeNightEnabled()) {
                // this is not likely to happen, since fn products are deleted when toggle is turned off. Added as fail-safe
                log.info("free night toggle is disabled, so decisions will not be generated for free night product");
                return;
            }

            if (!cpBarDecisionChange.isUploadable()) {
                log.info("Upload is not enabled, so decisions need not be generated for free night product");
                return;
            }
            CPDecisionBAROutput cpDecisionBAROutput = cpBarDecisionChange.getCPDecisionBAROutput();
            DecisionDailybarOutput previousDailybarOutput = cpBarDecisionChange.getPreviousDailybarOutput();
            DecisionDailybarOutput currentDailybarOutput = createDailybarOutput(decisionId, defaultRateUnqualified, cpDecisionBAROutput, previousDailybarOutput);
            Product product = cpDecisionBAROutput.getProduct();

            handleFreeNightProduct(currentDailybarOutput, cpDecisionBAROutput, barDecisionDailybarOutputs,
                    tableBatch, product, cpDecisionContext, previousDecisionDailybarOutputs);

            return;
        }
        if (cpBarDecisionChange.isUploadable() && cpBarDecisionChange.isUpdateRequired()) {

            // Pull some of the variables out of the cpBarDecisionChange to make it easier to work with
            CPDecisionBAROutput cpDecisionBAROutput = cpBarDecisionChange.getCPDecisionBAROutput();
            DecisionDailybarOutput previousDailybarOutput = cpBarDecisionChange.getPreviousDailybarOutput();
            DecisionDailybarOutputNonHiltonCRS previousNonHiltonCRSDecisionDailybarOutput = cpBarDecisionChange.getNonHiltonCRSDecisionDailybarOutput();

            log.debug("CPRecommendationService.handleCPBarDecisionChange - PropertyId: {}; Product: {}; Received for Date: {}; RoomType: {}, DecisionId: {}",
                    PacmanWorkContextHelper.getPropertyId(), cpDecisionBAROutput.getProduct().getName(),
                    cpDecisionBAROutput.getArrivalDate(), cpDecisionBAROutput.getAccomType().getName(),
                    cpDecisionBAROutput.getDecisionId());

            // Create a current DecisionDailybarOutput and set the rates
            DecisionDailybarOutput currentDailybarOutput = createDailybarOutput(decisionId, defaultRateUnqualified, cpDecisionBAROutput, previousDailybarOutput);
            Product product = cpDecisionBAROutput.getProduct();
            boolean isHiltonSendByAdjustment = isHiltonSendByAdjustment(cpDecisionContext, currentDailybarOutput);
            boolean isHiltonUpdateValuesSentForExtraAdultExtraChild = isHiltonUpdateValuesSentForExtraAdultExtraChild(cpDecisionContext, currentDailybarOutput);
            boolean isIHotelierEnabledAndProductSendByAdjustment = isIHotelierEnabledAndProductSendByAdjustment(currentDailybarOutput.getProduct());
            boolean perPersonPricingEnabled = isPerPersonPricingEnabled(cpDecisionContext);
            boolean childAgeBucketsEnabled = isChildAgeBucketsEnabled(cpDecisionContext);
            boolean finalBarCalculatedFromProductFloor = finalBarCalculatedFromProductFloor(cpDecisionContext, cpDecisionBAROutput, cpDecisionContext.isBaseRoomType(cpDecisionBAROutput));
            if (isHiltonUpdateValuesSentForExtraAdultExtraChild) {
                BigDecimal adjustmentValue = cpDecisionBAROutput.getAdjustmentValue();
                if (product.isOffsetForExtraAdult()) {
                    currentDailybarOutput.setAdultRate(adjustmentValue);
                } else {
                    currentDailybarOutput.setAdultRate(BigDecimal.ZERO);
                }
                currentDailybarOutput.setSingleRate(adjustmentValue);
                currentDailybarOutput.setDoubleRate(adjustmentValue);
                if (perPersonPricingEnabled) {
                    //Set adjustment value if occupancy type is configured for RT
                    OccupancyType maxAdultOccupancyType = cpDecisionContext.getMaxAdultOccupancyType();
                    int maxAdultsForAccomType = cpDecisionContext.getMaxAdultsForAccomType(cpDecisionBAROutput);
                    currentDailybarOutput.setTripleRate(getAdjustment(maxAdultOccupancyType, maxAdultsForAccomType, OccupancyType.getNonBaseAdults(), OccupancyType.THREE_ADULTS, adjustmentValue));
                    currentDailybarOutput.setQuadRate(getAdjustment(maxAdultOccupancyType, maxAdultsForAccomType, OccupancyType.getNonBaseAdults(), OccupancyType.FOUR_ADULTS, adjustmentValue));
                    currentDailybarOutput.setQuintRate(getAdjustment(maxAdultOccupancyType, maxAdultsForAccomType, OccupancyType.getNonBaseAdults(), OccupancyType.FIVE_ADULTS, adjustmentValue));
                } else {
                    currentDailybarOutput.setTripleRate(adjustmentValue);
                    currentDailybarOutput.setQuadRate(adjustmentValue);
                    currentDailybarOutput.setQuintRate(null);
                }
            } else if (isHiltonSendByAdjustment) {
                currentDailybarOutput.setAdultRate(cpBarDecisionChange.getExtraAdult());
                currentDailybarOutput.setChildRate(cpBarDecisionChange.getExtraChild());
                currentDailybarOutput.setSingleRate(cpDecisionBAROutput.getAdjustmentValue());
                currentDailybarOutput.setDoubleRate(cpDecisionBAROutput.getAdjustmentValue());
                //old flow does not set triple, quad or quint rate
                currentDailybarOutput.setTripleRate(null);
                currentDailybarOutput.setQuadRate(null);
                currentDailybarOutput.setQuintRate(null);
            } else {
                currentDailybarOutput.setSingleRate(cpBarDecisionChange.getSingleRate());
                currentDailybarOutput.setDoubleRate(cpBarDecisionChange.getDoubleRate());
                currentDailybarOutput.setAdultRate(cpBarDecisionChange.getExtraAdult());
                currentDailybarOutput.setChildRate(cpBarDecisionChange.getExtraChild());

                // Set the adult occupancy types
                OccupancyType maxAdultOccupancyType = cpDecisionContext.getMaxAdultOccupancyType();
                int maxAdultsForAccomType = cpDecisionContext.getMaxAdultsForAccomType(cpDecisionBAROutput);
                currentDailybarOutput.setTripleRate(getRate(maxAdultOccupancyType, maxAdultsForAccomType, OccupancyType.getNonBaseAdults(), cpBarDecisionChange, OccupancyType.THREE_ADULTS));
                currentDailybarOutput.setQuadRate(getRate(maxAdultOccupancyType, maxAdultsForAccomType, OccupancyType.getNonBaseAdults(), cpBarDecisionChange, OccupancyType.FOUR_ADULTS));
                currentDailybarOutput.setQuintRate(getRate(maxAdultOccupancyType, maxAdultsForAccomType, OccupancyType.getNonBaseAdults(), cpBarDecisionChange, OccupancyType.FIVE_ADULTS));
            }

            DecisionDailybarOutputNonHiltonCRS decisionDailybarOutputNonHiltonCRS = null;

            if (isIHotelierEnabledAndProductSendByAdjustment) {
                decisionDailybarOutputNonHiltonCRS =
                        createNonHiltonCRSDecisionDailybarOutput(decisionId, defaultRateUnqualified, cpDecisionBAROutput, previousNonHiltonCRSDecisionDailybarOutput);
                decisionDailybarOutputNonHiltonCRS.setSingleRate(cpBarDecisionChange.getSingleRate());
                decisionDailybarOutputNonHiltonCRS.setDoubleRate(cpBarDecisionChange.getDoubleRate());
                decisionDailybarOutputNonHiltonCRS.setAdultRate(cpBarDecisionChange.getExtraAdult());
                decisionDailybarOutputNonHiltonCRS.setChildRate(cpBarDecisionChange.getExtraChild());

                OccupancyType maxAdultOccupancyType = cpDecisionContext.getMaxAdultOccupancyType();
                int maxAdultsForAccomType = cpDecisionContext.getMaxAdultsForAccomType(cpDecisionBAROutput);
                decisionDailybarOutputNonHiltonCRS.setTripleRate(getRate(maxAdultOccupancyType, maxAdultsForAccomType, OccupancyType.getNonBaseAdults(), cpBarDecisionChange, OccupancyType.THREE_ADULTS));
                decisionDailybarOutputNonHiltonCRS.setQuadRate(getRate(maxAdultOccupancyType, maxAdultsForAccomType, OccupancyType.getNonBaseAdults(), cpBarDecisionChange, OccupancyType.FOUR_ADULTS));
                decisionDailybarOutputNonHiltonCRS.setQuintRate(getRate(maxAdultOccupancyType, maxAdultsForAccomType, OccupancyType.getNonBaseAdults(), cpBarDecisionChange, OccupancyType.FIVE_ADULTS));
            }

            // Set the child quantity occupancy types
            OccupancyType maxChildQuantityOccupancyType = cpDecisionContext.getMaxChildQuantityOccupancyType();
            int maxChildrenForAccomType = cpDecisionContext.getMaxChildrenForAccomType(cpDecisionBAROutput);
            OccupancyType maxChildBucketOccupancyType = cpDecisionContext.getMaxChildAgeBucketOccupancyType();
            BigDecimal adjustmentValue = cpDecisionBAROutput.getAdjustmentValue();
            boolean shouldExtraRatesBeCalculatedUsingBaseProductRates = SystemConfig.shouldExtraRatesBeCalculatedUsingBaseProductRates();
            if (isHiltonUpdateValuesSentForExtraAdultExtraChild) {
                if (product.isOffsetForExtraChild()) {
                    currentDailybarOutput.setChildRate(adjustmentValue);
                } else {
                    currentDailybarOutput.setChildRate(BigDecimal.ZERO);
                }

                if (perPersonPricingEnabled || childAgeBucketsEnabled) {
                    if (product.getChildPricingType().equals(ChildPricingType.CUSTOM_FOR_PRODUCT.getId())) {
                        AccomTypeSupplementValue accomTypeSupplementValue = cpDecisionContext.getSupplementFor(cpDecisionBAROutput, OccupancyType.DOUBLE).orElse(Supplement.zeroSupplement());
                        if (product.isOffsetForExtraChild()) {
                            CPConfigMergedOffset childBucket1Offset = cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_1);
                            BigDecimal childBucket1Rate = getRateWithoutPackages(maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), childBucket1Offset, getCustomOffsetRateWithoutPackage(cpDecisionBAROutput, cpDecisionContext, cpBarDecisionChange.getDoubleRate(), OccupancyType.CHILD_BUCKET_1, accomTypeSupplementValue), OccupancyType.CHILD_BUCKET_1);
                            BigDecimal childBucket1RateWithPackage = getPackagesRate(cpDecisionBAROutput, cpDecisionContext, childBucket1Rate, OccupancyType.CHILD_BUCKET_1);
                            currentDailybarOutput.setChildAgeOneRate(getAdjustedRate(childBucket1RateWithPackage, adjustmentValue, product.getOffsetMethod()));

                            CPConfigMergedOffset childBucket2Offset = cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_2);
                            BigDecimal childBucket2Rate = getRateWithoutPackages(maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), childBucket2Offset, getCustomOffsetRateWithoutPackage(cpDecisionBAROutput, cpDecisionContext, cpBarDecisionChange.getDoubleRate(), OccupancyType.CHILD_BUCKET_2, accomTypeSupplementValue), OccupancyType.CHILD_BUCKET_2);
                            BigDecimal childBucket2RateWithPackage = getPackagesRate(cpDecisionBAROutput, cpDecisionContext, childBucket2Rate, OccupancyType.CHILD_BUCKET_2);
                            currentDailybarOutput.setChildAgeTwoRate(getAdjustedRate(childBucket2RateWithPackage, adjustmentValue, product.getOffsetMethod()));

                            CPConfigMergedOffset childBucket3Offset = cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_3);
                            BigDecimal childBucket3Rate = getRateWithoutPackages(maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), childBucket3Offset, getCustomOffsetRateWithoutPackage(cpDecisionBAROutput, cpDecisionContext, cpBarDecisionChange.getDoubleRate(), OccupancyType.CHILD_BUCKET_3, accomTypeSupplementValue), OccupancyType.CHILD_BUCKET_3);
                            BigDecimal childBucket3RateWithPackage = getPackagesRate(cpDecisionBAROutput, cpDecisionContext, childBucket3Rate, OccupancyType.CHILD_BUCKET_3);
                            currentDailybarOutput.setChildAgeThreeRate(getAdjustedRate(childBucket3RateWithPackage, adjustmentValue, product.getOffsetMethod()));
                        } else {
                            // Set the Child Bucket values to the default values
                            currentDailybarOutput.setChildAgeOneRate(getCustomOffsetRate(cpDecisionBAROutput, cpDecisionContext, cpBarDecisionChange.getDoubleRate(), OccupancyType.CHILD_BUCKET_1, accomTypeSupplementValue));
                            currentDailybarOutput.setChildAgeTwoRate(getCustomOffsetRate(cpDecisionBAROutput, cpDecisionContext, cpBarDecisionChange.getDoubleRate(), OccupancyType.CHILD_BUCKET_2, accomTypeSupplementValue));
                            currentDailybarOutput.setChildAgeThreeRate(getCustomOffsetRate(cpDecisionBAROutput, cpDecisionContext, cpBarDecisionChange.getDoubleRate(), OccupancyType.CHILD_BUCKET_3, accomTypeSupplementValue));
                        }
                    } else if (product.getChildPricingType().equals(ChildPricingType.DO_NOT_UPLOAD_AGE_BASED_PRICING.getId())) {
                        currentDailybarOutput.setChildAgeOneRate(null);
                        currentDailybarOutput.setChildAgeTwoRate(null);
                        currentDailybarOutput.setChildAgeThreeRate(null);
                    } else {
                        if (product.isOffsetForExtraChild()) {
                            // Set the Child Bucket values to adjusted values
                            currentDailybarOutput.setChildAgeOneRate(getAdjustedRate(getRate(cpDecisionBAROutput, cpDecisionContext, maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_1), getBarDailyBarDecisionChildRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_1), OccupancyType.CHILD_BUCKET_1), adjustmentValue, product.getOffsetMethod()));
                            currentDailybarOutput.setChildAgeTwoRate(getAdjustedRate(getRate(cpDecisionBAROutput, cpDecisionContext, maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_2), getBarDailyBarDecisionChildRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_2), OccupancyType.CHILD_BUCKET_2), adjustmentValue, product.getOffsetMethod()));
                            currentDailybarOutput.setChildAgeThreeRate(getAdjustedRate(getRate(cpDecisionBAROutput, cpDecisionContext, maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_3), getBarDailyBarDecisionChildRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_3), OccupancyType.CHILD_BUCKET_3), adjustmentValue, product.getOffsetMethod()));
                        } else {
                            // Set the Child Bucket values to the default values
                            currentDailybarOutput.setChildAgeOneRate(getRate(cpDecisionBAROutput, cpDecisionContext, maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_1), getBarDailyBarDecisionChildRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_1), OccupancyType.CHILD_BUCKET_1));
                            currentDailybarOutput.setChildAgeTwoRate(getRate(cpDecisionBAROutput, cpDecisionContext, maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_2), getBarDailyBarDecisionChildRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_2), OccupancyType.CHILD_BUCKET_2));
                            currentDailybarOutput.setChildAgeThreeRate(getRate(cpDecisionBAROutput, cpDecisionContext, maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_3), getBarDailyBarDecisionChildRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_3), OccupancyType.CHILD_BUCKET_3));
                        }
                    }
                } else {
                    currentDailybarOutput.setChildAgeOneRate(null);
                    currentDailybarOutput.setChildAgeTwoRate(null);
                    currentDailybarOutput.setChildAgeThreeRate(null);
                }

                if (perPersonPricingEnabled) {
                    //Set adjustment value if occupancy type is configured for RT
                    currentDailybarOutput.setOneChildRate(getAdjustment(maxChildQuantityOccupancyType, maxChildrenForAccomType, OccupancyType.getChildQuantities(), OccupancyType.ONE_CHILD, adjustmentValue));
                    currentDailybarOutput.setTwoChildRate(getAdjustment(maxChildQuantityOccupancyType, maxChildrenForAccomType, OccupancyType.getChildQuantities(), OccupancyType.TWO_CHILDREN, adjustmentValue));
                    currentDailybarOutput.setThreeChildRate(getAdjustment(maxChildQuantityOccupancyType, maxChildrenForAccomType, OccupancyType.getChildQuantities(), OccupancyType.THREE_CHILDREN, adjustmentValue));
                    currentDailybarOutput.setFourChildRate(getAdjustment(maxChildQuantityOccupancyType, maxChildrenForAccomType, OccupancyType.getChildQuantities(), OccupancyType.FOUR_CHILDREN, adjustmentValue));
                    currentDailybarOutput.setFiveChildRate(getAdjustment(maxChildQuantityOccupancyType, maxChildrenForAccomType, OccupancyType.getChildQuantities(), OccupancyType.FIVE_CHILDREN, adjustmentValue));
                } else {
                    // Setting Child Rates to null
                    currentDailybarOutput.setOneChildRate(null);
                    currentDailybarOutput.setTwoChildRate(null);
                    currentDailybarOutput.setThreeChildRate(null);
                    currentDailybarOutput.setFourChildRate(null);
                    currentDailybarOutput.setFiveChildRate(null);
                }
            } else {
                currentDailybarOutput.setOneChildRate(getRate(maxChildQuantityOccupancyType, maxChildrenForAccomType, OccupancyType.getChildQuantities(), cpBarDecisionChange, OccupancyType.ONE_CHILD));
                currentDailybarOutput.setTwoChildRate(getRate(maxChildQuantityOccupancyType, maxChildrenForAccomType, OccupancyType.getChildQuantities(), cpBarDecisionChange, OccupancyType.TWO_CHILDREN));
                currentDailybarOutput.setThreeChildRate(getRate(maxChildQuantityOccupancyType, maxChildrenForAccomType, OccupancyType.getChildQuantities(), cpBarDecisionChange, OccupancyType.THREE_CHILDREN));
                currentDailybarOutput.setFourChildRate(getRate(maxChildQuantityOccupancyType, maxChildrenForAccomType, OccupancyType.getChildQuantities(), cpBarDecisionChange, OccupancyType.FOUR_CHILDREN));
                currentDailybarOutput.setFiveChildRate(getRate(maxChildQuantityOccupancyType, maxChildrenForAccomType, OccupancyType.getChildQuantities(), cpBarDecisionChange, OccupancyType.FIVE_CHILDREN));

                // Set the Child Bucket values
                if (product.isSystemDefaultOrIndependentProduct()) {
                    currentDailybarOutput.setChildAgeOneRate(getRate(maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), cpBarDecisionChange, OccupancyType.CHILD_BUCKET_1));
                    currentDailybarOutput.setChildAgeTwoRate(getRate(maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), cpBarDecisionChange, OccupancyType.CHILD_BUCKET_2));
                    currentDailybarOutput.setChildAgeThreeRate(getRate(maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), cpBarDecisionChange, OccupancyType.CHILD_BUCKET_3));
                } else {
                    if (product.getChildPricingType().equals(ChildPricingType.CUSTOM_FOR_PRODUCT.getId())) {
                        AccomTypeSupplementValue accomTypeSupplementValue = cpDecisionContext.getSupplementFor(cpDecisionBAROutput, OccupancyType.DOUBLE).orElse(Supplement.zeroSupplement());
                        if (product.isOffsetForExtraChild()) {
                            CPConfigMergedOffset childBucket1Offset = cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_1);
                            BigDecimal childBucket1Rate = getRateWithoutPackages(maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), childBucket1Offset, getCustomOffsetRateWithoutPackage(cpDecisionBAROutput, cpDecisionContext, cpBarDecisionChange.getDoubleRate(), OccupancyType.CHILD_BUCKET_1, accomTypeSupplementValue), OccupancyType.CHILD_BUCKET_1);
                            BigDecimal childBucket1RateWithPackage = getPackagesRate(cpDecisionBAROutput, cpDecisionContext, childBucket1Rate, OccupancyType.CHILD_BUCKET_1);
                            BigDecimal productRateOffset1 = cpDecisionContext.getProductRateOffset(cpDecisionBAROutput, childBucket1RateWithPackage, childBucket1Offset, childBucket1Rate);
                            if (shouldExtraRatesBeCalculatedUsingBaseProductRates && finalBarCalculatedFromProductFloor && childBucket1RateWithPackage != null) {
                                childBucket1RateWithPackage = negativeValueToZero(round(childBucket1RateWithPackage.add(retrieveAdjustment(cpDecisionBAROutput, childBucket1RateWithPackage)), 2));
                            } else if (childBucket1RateWithPackage != null && productRateOffset1 != null) {
                                childBucket1RateWithPackage = childBucket1RateWithPackage.add(productRateOffset1);
                            }
                            currentDailybarOutput.setChildAgeOneRate(childBucket1RateWithPackage);

                            CPConfigMergedOffset childBucket2Offset = cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_2);
                            BigDecimal childBucket2Rate = getRateWithoutPackages(maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), childBucket2Offset, getCustomOffsetRateWithoutPackage(cpDecisionBAROutput, cpDecisionContext, cpBarDecisionChange.getDoubleRate(), OccupancyType.CHILD_BUCKET_2, accomTypeSupplementValue), OccupancyType.CHILD_BUCKET_2);
                            BigDecimal childBucket2RateWithPackage = getPackagesRate(cpDecisionBAROutput, cpDecisionContext, childBucket2Rate, OccupancyType.CHILD_BUCKET_2);
                            BigDecimal productRateOffset2 = cpDecisionContext.getProductRateOffset(cpDecisionBAROutput, childBucket2RateWithPackage, childBucket2Offset, childBucket2Rate);
                            if (shouldExtraRatesBeCalculatedUsingBaseProductRates && finalBarCalculatedFromProductFloor && childBucket2RateWithPackage != null) {
                                childBucket2RateWithPackage = negativeValueToZero(round(childBucket2RateWithPackage.add(retrieveAdjustment(cpDecisionBAROutput, childBucket2RateWithPackage)), 2));
                            } else if (childBucket2RateWithPackage != null && productRateOffset2 != null) {
                                childBucket2RateWithPackage = childBucket2RateWithPackage.add(productRateOffset2);
                            }
                            currentDailybarOutput.setChildAgeTwoRate(childBucket2RateWithPackage);

                            CPConfigMergedOffset childBucket3Offset = cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_3);
                            BigDecimal childBucket3Rate = getRateWithoutPackages(maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), childBucket3Offset, getCustomOffsetRateWithoutPackage(cpDecisionBAROutput, cpDecisionContext, cpBarDecisionChange.getDoubleRate(), OccupancyType.CHILD_BUCKET_3, accomTypeSupplementValue), OccupancyType.CHILD_BUCKET_3);
                            BigDecimal childBucket3RateWithPackage = getPackagesRate(cpDecisionBAROutput, cpDecisionContext, childBucket3Rate, OccupancyType.CHILD_BUCKET_3);
                            BigDecimal productRateOffset3 = cpDecisionContext.getProductRateOffset(cpDecisionBAROutput, childBucket3RateWithPackage, childBucket3Offset, childBucket3Rate);
                            if (shouldExtraRatesBeCalculatedUsingBaseProductRates && finalBarCalculatedFromProductFloor && childBucket3RateWithPackage != null) {
                                childBucket3RateWithPackage = negativeValueToZero(round(childBucket3RateWithPackage.add(retrieveAdjustment(cpDecisionBAROutput, childBucket3RateWithPackage)), 2));
                            } else if (childBucket3RateWithPackage != null && productRateOffset3 != null) {
                                childBucket3RateWithPackage = childBucket3RateWithPackage.add(productRateOffset3);
                            }
                            currentDailybarOutput.setChildAgeThreeRate(childBucket3RateWithPackage);
                        } else {
                            currentDailybarOutput.setChildAgeOneRate(getCustomOffsetRate(cpDecisionBAROutput, cpDecisionContext, cpBarDecisionChange.getDoubleRate(), OccupancyType.CHILD_BUCKET_1, accomTypeSupplementValue));
                            currentDailybarOutput.setChildAgeTwoRate(getCustomOffsetRate(cpDecisionBAROutput, cpDecisionContext, cpBarDecisionChange.getDoubleRate(), OccupancyType.CHILD_BUCKET_2, accomTypeSupplementValue));
                            currentDailybarOutput.setChildAgeThreeRate(getCustomOffsetRate(cpDecisionBAROutput, cpDecisionContext, cpBarDecisionChange.getDoubleRate(), OccupancyType.CHILD_BUCKET_3, accomTypeSupplementValue));
                        }
                    } else if (product.getChildPricingType().equals(ChildPricingType.DO_NOT_UPLOAD_AGE_BASED_PRICING.getId())) {
                        currentDailybarOutput.setChildAgeOneRate(null);
                        currentDailybarOutput.setChildAgeTwoRate(null);
                        currentDailybarOutput.setChildAgeThreeRate(null);
                    } else {
                        if (product.isOffsetForExtraChild()) {
                            CPConfigMergedOffset childBucket1Offset = cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_1);
                            BigDecimal childBucket1Rate = getRate(cpDecisionBAROutput, cpDecisionContext, maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), childBucket1Offset, getBarDailyBarDecisionChildRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_1), OccupancyType.CHILD_BUCKET_1);
                            BigDecimal parentDoubleRate1 = getParentRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_1);
                            BigDecimal productRateOffset1 = cpDecisionContext.getProductRateOffset(cpDecisionBAROutput, childBucket1Rate, childBucket1Offset, parentDoubleRate1);
                            if (shouldExtraRatesBeCalculatedUsingBaseProductRates && finalBarCalculatedFromProductFloor && childBucket1Rate != null && parentDoubleRate1 != null) {
                                childBucket1Rate = negativeValueToZero(round(childBucket1Rate.add(retrieveAdjustment(cpDecisionBAROutput, parentDoubleRate1)), 2));
                            } else if (childBucket1Rate != null && productRateOffset1 != null) {
                                childBucket1Rate = childBucket1Rate.add(productRateOffset1);
                            }
                            currentDailybarOutput.setChildAgeOneRate(childBucket1Rate);

                            CPConfigMergedOffset childBucket2Offset = cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_2);
                            BigDecimal childBucket2Rate = getRate(cpDecisionBAROutput, cpDecisionContext, maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), childBucket2Offset, getBarDailyBarDecisionChildRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_2), OccupancyType.CHILD_BUCKET_2);
                            BigDecimal parentDoubleRate2 = getParentRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_2);
                            BigDecimal productRateOffset2 = cpDecisionContext.getProductRateOffset(cpDecisionBAROutput, childBucket2Rate, childBucket2Offset, parentDoubleRate2);
                            if (shouldExtraRatesBeCalculatedUsingBaseProductRates && finalBarCalculatedFromProductFloor && childBucket2Rate != null && parentDoubleRate2 != null) {
                                childBucket2Rate = negativeValueToZero(round(childBucket2Rate.add(retrieveAdjustment(cpDecisionBAROutput, parentDoubleRate2)), 2));
                            } else if (childBucket2Rate != null && productRateOffset2 != null) {
                                childBucket2Rate = childBucket2Rate.add(productRateOffset2);
                            }
                            currentDailybarOutput.setChildAgeTwoRate(childBucket2Rate);

                            CPConfigMergedOffset childBucket3Offset = cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_3);
                            BigDecimal childBucket3Rate = getRate(cpDecisionBAROutput, cpDecisionContext, maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), childBucket3Offset, getBarDailyBarDecisionChildRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_3), OccupancyType.CHILD_BUCKET_3);
                            BigDecimal parentDoubleRate3 = getParentRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_3);
                            BigDecimal productRateOffset3 = cpDecisionContext.getProductRateOffset(cpDecisionBAROutput, childBucket3Rate, childBucket3Offset, parentDoubleRate3);
                            if (shouldExtraRatesBeCalculatedUsingBaseProductRates && finalBarCalculatedFromProductFloor && childBucket3Rate != null && parentDoubleRate3 != null) {
                                childBucket3Rate = negativeValueToZero(round(childBucket3Rate.add(retrieveAdjustment(cpDecisionBAROutput, parentDoubleRate3)), 2));
                            } else if (childBucket3Rate != null && productRateOffset3 != null) {
                                childBucket3Rate = childBucket3Rate.add(productRateOffset3);
                            }
                            currentDailybarOutput.setChildAgeThreeRate(childBucket3Rate);
                        } else {
                            currentDailybarOutput.setChildAgeOneRate(getRate(cpDecisionBAROutput, cpDecisionContext, maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_1), getBarDailyBarDecisionChildRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_1), OccupancyType.CHILD_BUCKET_1));
                            currentDailybarOutput.setChildAgeTwoRate(getRate(cpDecisionBAROutput, cpDecisionContext, maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_2), getBarDailyBarDecisionChildRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_2), OccupancyType.CHILD_BUCKET_2));
                            currentDailybarOutput.setChildAgeThreeRate(getRate(cpDecisionBAROutput, cpDecisionContext, maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_3), getBarDailyBarDecisionChildRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_3), OccupancyType.CHILD_BUCKET_3));
                        }
                    }
                }
            }

            if (isIHotelierEnabledAndProductSendByAdjustment) {
                decisionDailybarOutputNonHiltonCRS.setOneChildRate(getRate(maxChildQuantityOccupancyType, maxChildrenForAccomType, OccupancyType.getChildQuantities(), cpBarDecisionChange, OccupancyType.ONE_CHILD));
                decisionDailybarOutputNonHiltonCRS.setTwoChildRate(getRate(maxChildQuantityOccupancyType, maxChildrenForAccomType, OccupancyType.getChildQuantities(), cpBarDecisionChange, OccupancyType.TWO_CHILDREN));
                decisionDailybarOutputNonHiltonCRS.setThreeChildRate(getRate(maxChildQuantityOccupancyType, maxChildrenForAccomType, OccupancyType.getChildQuantities(), cpBarDecisionChange, OccupancyType.THREE_CHILDREN));
                decisionDailybarOutputNonHiltonCRS.setFourChildRate(getRate(maxChildQuantityOccupancyType, maxChildrenForAccomType, OccupancyType.getChildQuantities(), cpBarDecisionChange, OccupancyType.FOUR_CHILDREN));
                decisionDailybarOutputNonHiltonCRS.setFiveChildRate(getRate(maxChildQuantityOccupancyType, maxChildrenForAccomType, OccupancyType.getChildQuantities(), cpBarDecisionChange, OccupancyType.FIVE_CHILDREN));

                if (product.getChildPricingType().equals(ChildPricingType.CUSTOM_FOR_PRODUCT.getId())) {
                    AccomTypeSupplementValue accomTypeSupplementValue = cpDecisionContext.getSupplementFor(cpDecisionBAROutput, OccupancyType.DOUBLE).orElse(Supplement.zeroSupplement());
                    if (product.isOffsetForExtraChild()) {
                        CPConfigMergedOffset childBucket1Offset = cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_1);
                        BigDecimal childBucket1Rate = getRateWithoutPackages(maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), childBucket1Offset, getCustomOffsetRateWithoutPackage(cpDecisionBAROutput, cpDecisionContext, cpBarDecisionChange.getDoubleRate(), OccupancyType.CHILD_BUCKET_1, accomTypeSupplementValue), OccupancyType.CHILD_BUCKET_1);
                        BigDecimal childBucket1RateWithPackage = getPackagesRate(cpDecisionBAROutput, cpDecisionContext, childBucket1Rate, OccupancyType.CHILD_BUCKET_1);
                        BigDecimal productRateOffset1 = cpDecisionContext.getProductRateOffset(cpDecisionBAROutput, childBucket1RateWithPackage, childBucket1Offset, childBucket1Rate);
                        if (childBucket1RateWithPackage != null && productRateOffset1 != null) {
                            childBucket1RateWithPackage = childBucket1RateWithPackage.add(productRateOffset1);
                        }
                        decisionDailybarOutputNonHiltonCRS.setChildAgeOneRate(childBucket1RateWithPackage);

                        CPConfigMergedOffset childBucket2Offset = cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_2);
                        BigDecimal childBucket2Rate = getRateWithoutPackages(maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), childBucket2Offset, getCustomOffsetRateWithoutPackage(cpDecisionBAROutput, cpDecisionContext, cpBarDecisionChange.getDoubleRate(), OccupancyType.CHILD_BUCKET_2, accomTypeSupplementValue), OccupancyType.CHILD_BUCKET_2);
                        BigDecimal childBucket2RateWithPackage = getPackagesRate(cpDecisionBAROutput, cpDecisionContext, childBucket2Rate, OccupancyType.CHILD_BUCKET_2);
                        BigDecimal productRateOffset2 = cpDecisionContext.getProductRateOffset(cpDecisionBAROutput, childBucket2RateWithPackage, childBucket2Offset, childBucket2Rate);
                        if (childBucket2RateWithPackage != null && productRateOffset2 != null) {
                            childBucket2RateWithPackage = childBucket2RateWithPackage.add(productRateOffset2);
                        }
                        decisionDailybarOutputNonHiltonCRS.setChildAgeTwoRate(childBucket2RateWithPackage);

                        CPConfigMergedOffset childBucket3Offset = cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_3);
                        BigDecimal childBucket3Rate = getRateWithoutPackages(maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), childBucket3Offset, getCustomOffsetRateWithoutPackage(cpDecisionBAROutput, cpDecisionContext, cpBarDecisionChange.getDoubleRate(), OccupancyType.CHILD_BUCKET_3, accomTypeSupplementValue), OccupancyType.CHILD_BUCKET_3);
                        BigDecimal childBucket3RateWithPackage = getPackagesRate(cpDecisionBAROutput, cpDecisionContext, childBucket3Rate, OccupancyType.CHILD_BUCKET_3);
                        BigDecimal productRateOffset3 = cpDecisionContext.getProductRateOffset(cpDecisionBAROutput, childBucket3RateWithPackage, childBucket3Offset, childBucket3Rate);
                        if (childBucket3RateWithPackage != null && productRateOffset3 != null) {
                            childBucket3RateWithPackage = childBucket3RateWithPackage.add(productRateOffset3);
                        }
                        decisionDailybarOutputNonHiltonCRS.setChildAgeThreeRate(childBucket3RateWithPackage);
                    } else {
                        decisionDailybarOutputNonHiltonCRS.setChildAgeOneRate(getCustomOffsetRate(cpDecisionBAROutput, cpDecisionContext, cpBarDecisionChange.getDoubleRate(), OccupancyType.CHILD_BUCKET_1, accomTypeSupplementValue));
                        decisionDailybarOutputNonHiltonCRS.setChildAgeTwoRate(getCustomOffsetRate(cpDecisionBAROutput, cpDecisionContext, cpBarDecisionChange.getDoubleRate(), OccupancyType.CHILD_BUCKET_2, accomTypeSupplementValue));
                        decisionDailybarOutputNonHiltonCRS.setChildAgeThreeRate(getCustomOffsetRate(cpDecisionBAROutput, cpDecisionContext, cpBarDecisionChange.getDoubleRate(), OccupancyType.CHILD_BUCKET_3, accomTypeSupplementValue));
                    }
                } else if (product.getChildPricingType().equals(ChildPricingType.DO_NOT_UPLOAD_AGE_BASED_PRICING.getId())) {
                    decisionDailybarOutputNonHiltonCRS.setChildAgeOneRate(null);
                    decisionDailybarOutputNonHiltonCRS.setChildAgeTwoRate(null);
                    decisionDailybarOutputNonHiltonCRS.setChildAgeThreeRate(null);
                } else {
                    if (product.isOffsetForExtraChild()) {
                        CPConfigMergedOffset childBucket1Offset = cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_1);
                        BigDecimal childBucket1Rate = getRate(cpDecisionBAROutput, cpDecisionContext, maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), childBucket1Offset, getBarDailyBarDecisionChildRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_1), OccupancyType.CHILD_BUCKET_1);
                        BigDecimal parentDoubleRate1 = getParentRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_1);
                        BigDecimal productRateOffset1 = cpDecisionContext.getProductRateOffset(cpDecisionBAROutput, childBucket1Rate, childBucket1Offset, parentDoubleRate1);
                        if (childBucket1Rate != null && productRateOffset1 != null) {
                            childBucket1Rate = childBucket1Rate.add(productRateOffset1);
                        }
                        decisionDailybarOutputNonHiltonCRS.setChildAgeOneRate(childBucket1Rate);

                        CPConfigMergedOffset childBucket2Offset = cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_2);
                        BigDecimal childBucket2Rate = getRate(cpDecisionBAROutput, cpDecisionContext, maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), childBucket2Offset, getBarDailyBarDecisionChildRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_2), OccupancyType.CHILD_BUCKET_2);
                        BigDecimal parentDoubleRate2 = getParentRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_2);
                        BigDecimal productRateOffset2 = cpDecisionContext.getProductRateOffset(cpDecisionBAROutput, childBucket2Rate, childBucket2Offset, parentDoubleRate2);
                        if (childBucket2Rate != null && productRateOffset2 != null) {
                            childBucket2Rate = childBucket2Rate.add(productRateOffset2);
                        }
                        decisionDailybarOutputNonHiltonCRS.setChildAgeTwoRate(childBucket2Rate);

                        CPConfigMergedOffset childBucket3Offset = cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_3);
                        BigDecimal childBucket3Rate = getRate(cpDecisionBAROutput, cpDecisionContext, maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), childBucket3Offset, getBarDailyBarDecisionChildRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_3), OccupancyType.CHILD_BUCKET_3);
                        BigDecimal parentDoubleRate3 = getParentRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_3);
                        BigDecimal productRateOffset3 = cpDecisionContext.getProductRateOffset(cpDecisionBAROutput, childBucket3Rate, childBucket3Offset, parentDoubleRate3);
                        if (childBucket3Rate != null && productRateOffset3 != null) {
                            childBucket3Rate = childBucket3Rate.add(productRateOffset3);
                        }
                        decisionDailybarOutputNonHiltonCRS.setChildAgeThreeRate(childBucket3Rate);
                    } else {
                        decisionDailybarOutputNonHiltonCRS.setChildAgeOneRate(getRate(cpDecisionBAROutput, cpDecisionContext, maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_1), getBarDailyBarDecisionChildRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_1), OccupancyType.CHILD_BUCKET_1));
                        decisionDailybarOutputNonHiltonCRS.setChildAgeTwoRate(getRate(cpDecisionBAROutput, cpDecisionContext, maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_2), getBarDailyBarDecisionChildRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_2), OccupancyType.CHILD_BUCKET_2));
                        decisionDailybarOutputNonHiltonCRS.setChildAgeThreeRate(getRate(cpDecisionBAROutput, cpDecisionContext, maxChildBucketOccupancyType, -1, OccupancyType.getChildBuckets(), cpDecisionContext.getOffset(cpDecisionBAROutput, OccupancyType.CHILD_BUCKET_3), getBarDailyBarDecisionChildRate(currentDailybarOutput, barDecisionDailybarOutputs, OccupancyType.CHILD_BUCKET_3), OccupancyType.CHILD_BUCKET_3));
                    }
                }
            }

            if (product.isSystemDefaultOrIndependentProduct()) {
                barDecisionDailybarOutputs.add(currentDailybarOutput);
            }

            if (isIHotelierEnabledAndProductSendByAdjustment) {
                if (tableBatchNonHiltonCRS != null) {
                    tableBatchNonHiltonCRS.addRow(decisionDailybarOutputNonHiltonCRS.getId(), decisionDailybarOutputNonHiltonCRS.getDecisionId(),
                            decisionDailybarOutputNonHiltonCRS.getOccupancyDate(), decisionDailybarOutputNonHiltonCRS.getAccomType().getId(),
                            decisionDailybarOutputNonHiltonCRS.getRateUnqualified().getId(), decisionDailybarOutputNonHiltonCRS.getSingleRate(),
                            decisionDailybarOutputNonHiltonCRS.getDoubleRate(), decisionDailybarOutputNonHiltonCRS.getTripleRate(),
                            decisionDailybarOutputNonHiltonCRS.getQuadRate(), decisionDailybarOutputNonHiltonCRS.getQuintRate(),
                            decisionDailybarOutputNonHiltonCRS.getAdultRate(), decisionDailybarOutputNonHiltonCRS.getChildRate(),
                            decisionDailybarOutputNonHiltonCRS.getProduct().getId(),
                            decisionDailybarOutputNonHiltonCRS.getOneChildRate(), decisionDailybarOutputNonHiltonCRS.getTwoChildRate(),
                            decisionDailybarOutputNonHiltonCRS.getThreeChildRate(), decisionDailybarOutputNonHiltonCRS.getFourChildRate(),
                            decisionDailybarOutputNonHiltonCRS.getFiveChildRate(), decisionDailybarOutputNonHiltonCRS.getChildAgeOneRate(),
                            decisionDailybarOutputNonHiltonCRS.getChildAgeTwoRate(), decisionDailybarOutputNonHiltonCRS.getChildAgeThreeRate());
                    tenantCrudService.detach(currentDailybarOutput);
                } else {
                    tenantCrudService.save(decisionDailybarOutputNonHiltonCRS);
                }
            }

            // Save the DecisionDailybarOutput
            if (tableBatch != null) {
                addToTableBatch(tableBatch, currentDailybarOutput);
                tenantCrudService.detach(currentDailybarOutput);
            } else {
                tenantCrudService.save(currentDailybarOutput);
            }
        }
    }

    @VisibleForTesting
	public void handleFreeNightProduct(DecisionDailybarOutput currentDailybarOutput, CPDecisionBAROutput cpDecisionBAROutput,
                                List<DecisionDailybarOutput> barDecisionDailybarOutputs, TableBatch tableBatch,
                                Product product, CPDecisionContext context,
                                Map<DecisionDailybarOutputKey, DecisionDailybarOutput> previousDecisionDailybarOutputs) {

        String logSuffix = "product name = " + currentDailybarOutput.getProduct().getName()
                + ", occupancy date = " + currentDailybarOutput.getOccupancyDate()
                + ", accom type = " + currentDailybarOutput.getAccomType();

        boolean applyAdjustmentForExtraAdult = product.isOffsetForExtraAdult();
        boolean applyAdjustmentForExtraChild = product.isOffsetForExtraChild();
        boolean isPerPersonPricing = isPerPersonPricingEnabled(context);

        DecisionDailybarOutput parentDecision = barDecisionDailybarOutputs.stream()
                .filter(parent -> Objects.equals(parent.getProduct().getId(), currentDailybarOutput.getProduct().getDependentProductId()))
                .filter(parent -> Objects.equals(parent.getOccupancyDate(), currentDailybarOutput.getOccupancyDate()))
                .filter(parent -> Objects.equals(parent.getAccomType(), currentDailybarOutput.getAccomType()))
                .findFirst().orElse(null);

        // this happens when parent decision was not changed, hence not present in barDecisionDailybarOutputs
        if (parentDecision == null) {
            log.debug("Fetching parent decision daily bar, since it was not changed. {}", logSuffix);

            DecisionDailybarOutputKey decisionDailybarOutputKey = new DecisionDailybarOutputKey(currentDailybarOutput.getProduct().getDependentProductId(),
                    cpDecisionBAROutput.getArrivalDate(), currentDailybarOutput.getAccomType().getId());
            parentDecision = previousDecisionDailybarOutputs.get(decisionDailybarOutputKey);
            if (parentDecision == null) {
                throw new TetrisException("No parent decision found in db for current free night decision, " + logSuffix);
            }
        }

        if (isFreeNightEligibleForCurrentFreeNightDecision(cpDecisionBAROutput)) {
            log.debug("Creating recommendations for free night product where occupancy night is free. {}", logSuffix);
            currentDailybarOutput.setSingleRate(BigDecimal.ZERO);
            currentDailybarOutput.setDoubleRate(BigDecimal.ZERO);
            currentDailybarOutput.setTripleRate(BigDecimal.ZERO);
            currentDailybarOutput.setQuadRate(BigDecimal.ZERO);
            currentDailybarOutput.setQuintRate(null);

            currentDailybarOutput.setAdultRate(applyAdjustmentForExtraAdult ? BigDecimal.ZERO : parentDecision.getAdultRate());
            currentDailybarOutput.setChildRate(applyAdjustmentForExtraChild ? BigDecimal.ZERO : parentDecision.getChildRate());

            if (isPerPersonPricing) {
                currentDailybarOutput.setOneChildRate(applyAdjustmentForExtraChild ? BigDecimal.ZERO : parentDecision.getOneChildRate());
                currentDailybarOutput.setTwoChildRate(applyAdjustmentForExtraChild ? BigDecimal.ZERO : parentDecision.getTwoChildRate());
                currentDailybarOutput.setThreeChildRate(applyAdjustmentForExtraChild ? BigDecimal.ZERO : parentDecision.getThreeChildRate());
                currentDailybarOutput.setFourChildRate(applyAdjustmentForExtraChild ? BigDecimal.ZERO : parentDecision.getFourChildRate());
                currentDailybarOutput.setFiveChildRate(applyAdjustmentForExtraChild ? BigDecimal.ZERO : parentDecision.getFiveChildRate());
            } else {
                setNullChildRates(currentDailybarOutput);
            }
            setChildAgeBucketRates(currentDailybarOutput, applyAdjustmentForExtraChild, parentDecision);
        } else {
            log.debug("Creating recommendations for free night product where occupancy night is NOT free. {}", logSuffix);
            // set rates to bar
            currentDailybarOutput.setSingleRate(parentDecision.getSingleRate());
            currentDailybarOutput.setDoubleRate(parentDecision.getDoubleRate());
            currentDailybarOutput.setTripleRate(parentDecision.getTripleRate());
            currentDailybarOutput.setQuadRate(parentDecision.getQuadRate());
            currentDailybarOutput.setQuintRate(null);

            currentDailybarOutput.setAdultRate(parentDecision.getAdultRate());
            currentDailybarOutput.setChildRate(parentDecision.getChildRate());

            if (isPerPersonPricing) {
                currentDailybarOutput.setOneChildRate(parentDecision.getOneChildRate());
                currentDailybarOutput.setTwoChildRate(parentDecision.getTwoChildRate());
                currentDailybarOutput.setThreeChildRate(parentDecision.getThreeChildRate());
                currentDailybarOutput.setFourChildRate(parentDecision.getFourChildRate());
                currentDailybarOutput.setFiveChildRate(parentDecision.getFiveChildRate());
            } else {
                setNullChildRates(currentDailybarOutput);
            }
            currentDailybarOutput.setChildAgeOneRate(parentDecision.getChildAgeOneRate());
            currentDailybarOutput.setChildAgeTwoRate(parentDecision.getChildAgeTwoRate());
            currentDailybarOutput.setChildAgeThreeRate(parentDecision.getChildAgeThreeRate());
        }

        addToTableBatch(tableBatch, currentDailybarOutput);
        tenantCrudService.detach(currentDailybarOutput);
    }

    private BigDecimal getChildBucketRateForFreeNightProductWhenFreeNight(boolean applyAdjustmentForExtraChild, BigDecimal parentRate) {
        if (applyAdjustmentForExtraChild) {
            return parentRate != null ? BigDecimal.ZERO : null;
        }
        return parentRate;
    }

    private void setChildAgeBucketRates(DecisionDailybarOutput currentDailybarOutput,
                                        boolean applyAdjustmentForExtraChild, DecisionDailybarOutput parentDecision) {
        BigDecimal childAgeOneRate = getChildBucketRateForFreeNightProductWhenFreeNight(applyAdjustmentForExtraChild, parentDecision.getChildAgeOneRate());
        BigDecimal childAgeTwoRate = getChildBucketRateForFreeNightProductWhenFreeNight(applyAdjustmentForExtraChild, parentDecision.getChildAgeTwoRate());
        BigDecimal childAgeThreeRate = getChildBucketRateForFreeNightProductWhenFreeNight(applyAdjustmentForExtraChild, parentDecision.getChildAgeThreeRate());
        ;
        currentDailybarOutput.setChildAgeOneRate(childAgeOneRate);
        currentDailybarOutput.setChildAgeTwoRate(childAgeTwoRate);
        currentDailybarOutput.setChildAgeThreeRate(childAgeThreeRate);
    }

    private void setNullChildRates(DecisionDailybarOutput currentDecision) {
        currentDecision.setOneChildRate(null);
        currentDecision.setTwoChildRate(null);
        currentDecision.setThreeChildRate(null);
        currentDecision.setFourChildRate(null);
        currentDecision.setFiveChildRate(null);
    }

    private void addToTableBatch(TableBatch tableBatch, DecisionDailybarOutput currentDailybarOutput) {
        tableBatch.addRow(currentDailybarOutput.getId(), currentDailybarOutput.getDecisionId(),
                currentDailybarOutput.getOccupancyDate(), currentDailybarOutput.getAccomType().getId(),
                currentDailybarOutput.getRateUnqualified().getId(), currentDailybarOutput.getSingleRate(),
                currentDailybarOutput.getDoubleRate(), currentDailybarOutput.getTripleRate(),
                currentDailybarOutput.getQuadRate(), currentDailybarOutput.getQuintRate(),
                currentDailybarOutput.getAdultRate(), currentDailybarOutput.getChildRate(),
                currentDailybarOutput.getProduct().getId(),
                currentDailybarOutput.getOneChildRate(), currentDailybarOutput.getTwoChildRate(),
                currentDailybarOutput.getThreeChildRate(), currentDailybarOutput.getFourChildRate(),
                currentDailybarOutput.getFiveChildRate(), currentDailybarOutput.getChildAgeOneRate(),
                currentDailybarOutput.getChildAgeTwoRate(), currentDailybarOutput.getChildAgeThreeRate());
    }

    private boolean finalBarCalculatedFromProductFloor(CPDecisionContext cpDecisionContext, CPDecisionBAROutput cpDecisionBAROutput, boolean isBaseRoomType) {
        BigDecimal productFloorRate = calculateBaseOccupancyRateUsingProductFloor(cpDecisionContext, cpDecisionBAROutput, isBaseRoomType);
        BigDecimal scaledProductFloorRate = productFloorRate.setScale(DEFAULT_PRECISION, RoundingMode.HALF_UP);
        return scaledProductFloorRate.equals(cpDecisionBAROutput.getFinalBAR()) || shouldReplaceFinalBarWithProductFloor(cpDecisionBAROutput);
    }

    private BigDecimal calculateBaseOccupancyRateUsingProductFloor(CPDecisionContext cpDecisionContext, CPDecisionBAROutput cpDecisionBAROutput, boolean isBaseRoomType) {
        BigDecimal productFloor = cpDecisionBAROutput.getProduct().getFloor();

        if (productFloor == null) {
            return BigDecimal.ZERO;
        }

        if (!isBaseRoomType) {
            AccomTypeSupplementValue accomTypeSupplementValue = cpDecisionContext.getSupplementFor(cpDecisionBAROutput).orElse(Supplement.zeroSupplement());
            productFloor = subtractBaseRoomTypeSupplementFrom(cpDecisionContext, cpDecisionBAROutput, productFloor);
            productFloor = productFloor.add(cpDecisionContext.getOffset(cpDecisionBAROutput, productFloor))
                    .add(Supplement.getSupplementToAdd(productFloor, accomTypeSupplementValue));
        }
        return negativeValueToZero(round(productFloor, 2));
    }

    private BigDecimal subtractBaseRoomTypeSupplementFrom(CPDecisionContext cpDecisionContext, CPDecisionBAROutput cpDecisionBAROutput, BigDecimal baseOccupancyTypeRate) {
        AccomTypeSupplementValue accomTypeSupplementValue = cpDecisionContext.getBaseAccomTypeSupplementFor(cpDecisionBAROutput,
                cpDecisionContext.getBaseOccupancyType()).orElse(Supplement.zeroSupplement());
        return Supplement.removeSupplementFrom(baseOccupancyTypeRate, accomTypeSupplementValue);
    }

    private boolean shouldReplaceFinalBarWithProductFloor(CPDecisionBAROutput cpDecisionBAROutput) {
        return cpDecisionBAROutput.getProduct().isOptimized() &&
                DecisionReasonType.OPTIMAL_PRICE_BELOW_FLOOR_RATE == DecisionReasonType.fromId(cpDecisionBAROutput.getDecisionReasonTypeId());
    }

    private BigDecimal getParentRate(DecisionDailybarOutput currentDailybarOutput, List<DecisionDailybarOutput> barDecisionDailybarOutputs, OccupancyType occupancyType) {
        DecisionDailybarOutput parentDecisionDailybarOutput = barDecisionDailybarOutputs.stream().filter(ddbo -> ddbo.getOccupancyDate().equals(currentDailybarOutput.getOccupancyDate()) && ddbo.getAccomType().equals(currentDailybarOutput.getAccomType())).findFirst().orElse(null);
        BigDecimal parentRate = BigDecimal.ZERO;
        if (parentDecisionDailybarOutput != null) {
            if (OccupancyType.CHILD_BUCKET_1.equals(occupancyType)) {
                parentRate = parentDecisionDailybarOutput.getChildAgeOneRate();
            } else if (OccupancyType.CHILD_BUCKET_2.equals(occupancyType)) {
                parentRate = parentDecisionDailybarOutput.getChildAgeTwoRate();
            } else if (OccupancyType.CHILD_BUCKET_3.equals(occupancyType)) {
                parentRate = parentDecisionDailybarOutput.getChildAgeThreeRate();
            }
        }
        return parentRate;
    }

    @VisibleForTesting
    protected boolean isPerPersonPricingEnabled(CPDecisionContext cpDecisionContext) {
        return cpDecisionContext.isPerPersonPricingEnabled();
    }

    @VisibleForTesting
    protected boolean isChildAgeBucketsEnabled(CPDecisionContext cpDecisionContext) {
        return cpDecisionContext.isChildAgeBucketsEnabled();
    }

    @VisibleForTesting
    protected boolean isChildAgeBucketPackagesEnabled(CPDecisionContext cpDecisionContext) {
        return cpDecisionContext.isChildAgeBucketPackagesEnabled();
    }

    @VisibleForTesting
    protected boolean isHiltonSendByAdjustment(CPDecisionContext cpDecisionContext, DecisionDailybarOutput currentDailybarOutput) {
        return cpDecisionContext.isHiltonSendAdjustmentEnabled() &&
                !currentDailybarOutput.getProduct().isSystemDefaultOrIndependentProduct() &&
                currentDailybarOutput.getProduct().getDecisionsSentBy().equals(AgileRatesDecisionsSentBy.ADJUSTMENT);
    }

    @VisibleForTesting
    protected boolean isHiltonUpdateValuesSentForExtraAdultExtraChild(CPDecisionContext cpDecisionContext, DecisionDailybarOutput currentDailybarOutput) {
        return cpDecisionContext.isHiltonSendAdjustmentEnabled() &&
                cpDecisionContext.isHiltonUpdateValuesSentForExtraAdultExtraChild() &&
                !currentDailybarOutput.getProduct().isSystemDefaultOrIndependentProduct() &&
                currentDailybarOutput.getProduct().getDecisionsSentBy().equals(AgileRatesDecisionsSentBy.ADJUSTMENT);
    }

    public boolean isIHotelierEnabledAndProductSendByAdjustment(Product product) {
        return isNonHiltonCrsSendPriceAdjustmentEnabled() &&
                product.getDecisionsSentBy().equals(AgileRatesDecisionsSentBy.ADJUSTMENT);
    }

    public boolean isIHotelierEnabledAndProductSendByPrice(Product product) {
        return isNonHiltonCrsSendPriceAdjustmentEnabled() &&
                product.getDecisionsSentBy().equals(AgileRatesDecisionsSentBy.PRICE);
    }

    @VisibleForTesting
    protected BigDecimal getAdjustment(OccupancyType maxOccupancyType, int maxOccupantQuantity, List<OccupancyType> occupancyTypes, OccupancyType occupancyType, BigDecimal adjustmentValue) {
        // If there is no maxOccupancyType - we shouldn't be setting any of these values
        // Also, if the OccupancyType's quantity is greater than the max configured for the AccomType - we should skip it
        if (maxOccupancyType == null || (maxOccupantQuantity != -1 && occupancyType.getOccupantQuantity() != -1 && occupancyType.getOccupantQuantity() > maxOccupantQuantity)) {
            return null;
        }

        // If the occupancyType index is less than or equal to the max, then return adjustment value - otherwise return null
        int maxOccupancyTypeIndex = occupancyTypes.indexOf(maxOccupancyType);
        int occupancyTypeIndex = occupancyTypes.indexOf(occupancyType);
        if (occupancyTypeIndex <= maxOccupancyTypeIndex) {
            return adjustmentValue;
        }

        // If the occupancyType is greater than the max - return null
        return null;
    }

    @VisibleForTesting
    public BigDecimal getRate(CPDecisionBAROutput cpDecisionBAROutput, CPDecisionContext cpDecisionContext, OccupancyType maxOccupancyType, int maxOccupantQuantity, List<OccupancyType> occupancyTypes, CPConfigMergedOffset offset, BigDecimal barDecisionDailyBarChildRate, OccupancyType occupancyType) {
        // If there is no maxOccupancyType - we shouldn't be setting any of these values
        // Also, if the OccupancyType's quantity is greater than the max configured for the AccomType - we should skip it
        if (maxOccupancyType == null || (maxOccupantQuantity != -1 && occupancyType.getOccupantQuantity() != -1 && occupancyType.getOccupantQuantity() > maxOccupantQuantity)) {
            return null;
        }

        BigDecimal price = null;

        // If the occupancyType index is less than or equal to the max, then return it - otherwise return null
        int maxOccupancyTypeIndex = occupancyTypes.indexOf(maxOccupancyType);
        int occupancyTypeIndex = occupancyTypes.indexOf(occupancyType);
        if (occupancyTypeIndex <= maxOccupancyTypeIndex) {
            if (offset != null) {
                OffsetMethod method = offset.getOffsetMethod();
                BigDecimal rate = offset.getOffsetValue();
                if (method.equals(OffsetMethod.PERCENTAGE)) {
                    price = barDecisionDailyBarChildRate;
                } else {
                    price = rate;
                }
            }
        }

        if (price != null && cpDecisionBAROutput.getProduct().isAgileRatesProduct()) {
            BigDecimal productPackageOffset = cpDecisionContext.getProductPackageOffset(cpDecisionBAROutput.getProduct(), occupancyType, price, cpDecisionBAROutput.getArrivalDate());
            price = price.add(productPackageOffset).setScale(2, RoundingMode.HALF_UP);
        }

        return price;
    }

    @VisibleForTesting
    public BigDecimal getRateWithoutPackages(OccupancyType maxOccupancyType, int maxOccupantQuantity, List<OccupancyType> occupancyTypes, CPConfigMergedOffset offset, BigDecimal barDecisionDailyBarChildRate, OccupancyType occupancyType) {
        // If there is no maxOccupancyType - we shouldn't be setting any of these values
        // Also, if the OccupancyType's quantity is greater than the max configured for the AccomType - we should skip it
        if (maxOccupancyType == null || (maxOccupantQuantity != -1 && occupancyType.getOccupantQuantity() != -1 && occupancyType.getOccupantQuantity() > maxOccupantQuantity)) {
            return null;
        }

        BigDecimal price = null;

        // If the occupancyType index is less than or equal to the max, then return it - otherwise return null
        int maxOccupancyTypeIndex = occupancyTypes.indexOf(maxOccupancyType);
        int occupancyTypeIndex = occupancyTypes.indexOf(occupancyType);
        if (occupancyTypeIndex <= maxOccupancyTypeIndex) {
            if (offset != null) {
                OffsetMethod method = offset.getOffsetMethod();
                BigDecimal rate = offset.getOffsetValue();
                if (method.equals(OffsetMethod.PERCENTAGE)) {
                    price = barDecisionDailyBarChildRate;
                } else {
                    price = rate;
                }
            }
        }

        return price;
    }

    @VisibleForTesting
    public BigDecimal getPackagesRate(CPDecisionBAROutput cpDecisionBAROutput, CPDecisionContext cpDecisionContext, BigDecimal price, OccupancyType occupancyType) {
        BigDecimal priceWithPackage = price;
        if (priceWithPackage != null && cpDecisionBAROutput.getProduct().isAgileRatesProduct()) {
            BigDecimal productPackageOffset = cpDecisionContext.getProductPackageOffset(cpDecisionBAROutput.getProduct(), occupancyType, price, cpDecisionBAROutput.getArrivalDate());
            priceWithPackage = priceWithPackage.add(productPackageOffset).setScale(2, RoundingMode.HALF_UP);
        }

        return priceWithPackage;
    }


    private BigDecimal getRate(OccupancyType maxOccupancyType, int maxOccupantQuantity, List<OccupancyType> occupancyTypes, CPBarDecisionChange cpBarDecisionChange, OccupancyType occupancyType) {
        // If there is no maxOccupancyType - we shouldn't be setting any of these values
        // Also, if the OccupancyType's quantity is greater than the max configured for the AccomType - we should skip it
        if (maxOccupancyType == null || (maxOccupantQuantity != -1 && occupancyType.getOccupantQuantity() != -1 && occupancyType.getOccupantQuantity() > maxOccupantQuantity)) {
            return null;
        }

        // If the occupancyType index is less than or equal to the max, then return it - otherwise return null
        int maxOccupancyTypeIndex = occupancyTypes.indexOf(maxOccupancyType);
        int occupancyTypeIndex = occupancyTypes.indexOf(occupancyType);
        if (occupancyTypeIndex <= maxOccupancyTypeIndex) {
            return cpBarDecisionChange.getRate(occupancyType);
        }

        // If the occupancyType is greater than the max - set null
        return null;
    }

    private BigDecimal getCustomOffsetRate(CPDecisionBAROutput cpDecisionBAROutput, CPDecisionContext cpDecisionContext,
                                           BigDecimal doubleRate, OccupancyType occupancyType, AccomTypeSupplementValue accomTypeSupplementValue) {
        CPConfigMergedOffset offset = cpDecisionContext.getOffset(cpDecisionBAROutput, occupancyType);

        BigDecimal price = null;
        if (offset != null) {
            OffsetMethod offsetMethod = offset.getOffsetMethod();
            if (OffsetMethod.PERCENTAGE.equals(offsetMethod)) {
                price = Supplement.removeSupplementFrom(doubleRate, accomTypeSupplementValue);
            } else {
                price = doubleRate;
            }

            price = offset.getOffsetValueForPrice(price);

            BigDecimal productPackageOffset = cpDecisionContext.getProductPackageOffset(cpDecisionBAROutput.getProduct(), occupancyType, price, cpDecisionBAROutput.getArrivalDate());
            price = price.add(productPackageOffset).setScale(2, RoundingMode.HALF_UP);
        }

        return price;
    }

    private BigDecimal getCustomOffsetRateWithoutPackage(CPDecisionBAROutput cpDecisionBAROutput, CPDecisionContext cpDecisionContext,
                                                         BigDecimal doubleRate, OccupancyType occupancyType, AccomTypeSupplementValue accomTypeSupplementValue) {
        CPConfigMergedOffset offset = cpDecisionContext.getOffset(cpDecisionBAROutput, occupancyType);

        BigDecimal price = null;
        if (offset != null) {
            OffsetMethod offsetMethod = offset.getOffsetMethod();
            if (OffsetMethod.PERCENTAGE.equals(offsetMethod)) {
                price = Supplement.removeSupplementFrom(doubleRate, accomTypeSupplementValue);
            } else {
                price = doubleRate;
            }

            price = offset.getOffsetValueForPrice(price);
        }

        return price;
    }

    @VisibleForTesting
    public BigDecimal getAdjustedRate(BigDecimal rate, BigDecimal adjustmentValue, AgileRatesOffsetMethod offsetMethod) {
        if (rate == null || !BigDecimalUtil.isNotZeroOrNull(adjustmentValue)) {
            return rate;
        }
        if (offsetMethod != null && offsetMethod.equals(AgileRatesOffsetMethod.PERCENTAGE)) {
            return adjustRateByAdjustmentValuePercentage(rate, adjustmentValue);
        }
        return adjustRateByAdjustmentValueFixed(rate, adjustmentValue);
    }

    private BigDecimal adjustRateByAdjustmentValueFixed(BigDecimal rate, BigDecimal adjustmentValue) {
        return rate.add(adjustmentValue).setScale(2, RoundingMode.HALF_UP);
    }

    private BigDecimal adjustRateByAdjustmentValuePercentage(BigDecimal rate, BigDecimal adjustmentValue) {
        if (!BigDecimalUtil.isNotZeroOrNull(rate)) {
            return rate;
        }
        return rate.multiply((BigDecimal.valueOf(100).add(adjustmentValue)).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP)).setScale(2, RoundingMode.HALF_UP);
    }

    @VisibleForTesting
    protected BigDecimal getBarDailyBarDecisionChildRate(DecisionDailybarOutput currentDailybarOutput, List<DecisionDailybarOutput> barDecisionDailybarOutputs, OccupancyType childBucket) {
        DecisionDailybarOutput barDecisionDailybarOutput = barDecisionDailybarOutputs.stream()
                .filter(decisionDailybarOutput -> decisionDailybarOutput.getAccomType().equals(currentDailybarOutput.getAccomType()) &&
                        decisionDailybarOutput.getOccupancyDate().equals(currentDailybarOutput.getOccupancyDate())
                        &&
                        (decisionDailybarOutput.getProduct().getId().equals(currentDailybarOutput.getProduct().getDependentProductId()))
                )
                .findFirst().orElse(null);
        if (barDecisionDailybarOutput != null) {
            if (childBucket.equals(OccupancyType.CHILD_BUCKET_1)) {
                return barDecisionDailybarOutput.getChildAgeOneRate();
            } else if (childBucket.equals(OccupancyType.CHILD_BUCKET_2)) {
                return barDecisionDailybarOutput.getChildAgeTwoRate();
            } else if (childBucket.equals(OccupancyType.CHILD_BUCKET_3)) {
                return barDecisionDailybarOutput.getChildAgeThreeRate();
            }
        }
        return null;
    }

    private DecisionDailybarOutput createDailybarOutput(Integer decisionId, RateUnqualified defaultRateUnqualified, CPDecisionBAROutput cpDecisionBAROutput, DecisionDailybarOutput previousDailybarOutput) {
        // Create a new version of the DecisionDailybarOutput - where values will be updated
        DecisionDailybarOutput currentDailybarOutput = null;
        if (previousDailybarOutput == null) {
            currentDailybarOutput = new DecisionDailybarOutput();
            currentDailybarOutput.setDecisionId(decisionId);
            currentDailybarOutput.setRateUnqualified(defaultRateUnqualified);
            currentDailybarOutput.setProduct(cpDecisionBAROutput.getProduct());
            currentDailybarOutput.setOccupancyDate(cpDecisionBAROutput.getArrivalDate());
            currentDailybarOutput.setAccomType(cpDecisionBAROutput.getAccomType());
            currentDailybarOutput.setCreateDate(new Date());
        } else {
            currentDailybarOutput = new DecisionDailybarOutput(previousDailybarOutput);
            currentDailybarOutput.setDecisionId(decisionId);
        }

        return currentDailybarOutput;
    }

    private DecisionDailybarOutputNonHiltonCRS createNonHiltonCRSDecisionDailybarOutput(
            Integer decisionId, RateUnqualified defaultRateUnqualified, CPDecisionBAROutput cpDecisionBAROutput, DecisionDailybarOutputNonHiltonCRS previousNonHiltonCRSDecisionDailybarOutput) {
        DecisionDailybarOutputNonHiltonCRS nonHiltonCRSDailyBarDecisions;
        if (previousNonHiltonCRSDecisionDailybarOutput == null) {
            nonHiltonCRSDailyBarDecisions = new DecisionDailybarOutputNonHiltonCRS();
            nonHiltonCRSDailyBarDecisions.setDecisionId(decisionId);
            nonHiltonCRSDailyBarDecisions.setRateUnqualified(defaultRateUnqualified);
            nonHiltonCRSDailyBarDecisions.setProduct(cpDecisionBAROutput.getProduct());
            nonHiltonCRSDailyBarDecisions.setOccupancyDate(cpDecisionBAROutput.getArrivalDate());
            nonHiltonCRSDailyBarDecisions.setAccomType(cpDecisionBAROutput.getAccomType());
            nonHiltonCRSDailyBarDecisions.setCreateDate(new Date());
        } else {
            nonHiltonCRSDailyBarDecisions = new DecisionDailybarOutputNonHiltonCRS(previousNonHiltonCRSDecisionDailybarOutput);
            nonHiltonCRSDailyBarDecisions.setDecisionId(decisionId);
        }
        return nonHiltonCRSDailyBarDecisions;
    }

    protected void setAgilePrettyBarToNullToRecalculateTheRates(Product product, List<CPDecisionBAROutput> productOutputs, Set<AccomType> crAccomTypes) {
        //Do nothing.
        //We get the calculated pretty bar from CPOptimalBarStep for component Rooms
        //We should not set pretty bar to null in recommendation step as we don't save the pretty bar = baseOccupancy in this step .
    }

    public void deleteFutureDecisionsForNonUploadableProducts(Product product) {
        if (null == product.getId() || product.isUpload()) {
            return;
        }

        LocalDate caughtUpLocalDate = dateService.getCaughtUpLocalDate();

        deleteFutureDecisionDailyBarOutput(product, caughtUpLocalDate);

        tenantCrudService.executeUpdateByNamedQuery(
                CPDecisionBAROutput.DELETE_BY_PRODUCT,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("product", product)
                        .and("arrivalDate", caughtUpLocalDate)
                        .parameters());
    }

    public void deleteFutureDecisionDailyBarOutput(Product product, LocalDate caughtUpLocalDate) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("caughtUpDate", caughtUpLocalDate);
        parameters.put("nonUploadableProductId", product.getId());
        tenantCrudService.executeUpdateByNamedQuery(DecisionDailybarOutput.DELETE_FUTURE_DECISIONS_FOR_NON_UPLOADABLE_AGILE_PRODUCTS, parameters);
    }

    @VisibleForTesting
	public
    void handleFreeUpgrade(LocalDate startDate, LocalDate endDate) {
        CPDecisionContext cpDecisionContext = pricingConfigurationService.getCPDecisionContext(startDate, endDate);
        if (!cpDecisionContext.isConsortiaFreeNightEnabled()) {
            return;
        }
        Set<Product> freeUpgradeProducts = getFreeUpgradeProducts(cpDecisionContext);
        if (freeUpgradeProducts.isEmpty()) {
            return;
        }

        // returns a map where key is accom type after upgrade and value is accom type before upgrade
        Map<String, String> upgradeMap = getUpgradePaths();

        // set of room types which are present either as source or target in vendor mapping
        Set<String> accomTypesToConsider = new HashSet<>(upgradeMap.keySet());
        accomTypesToConsider.addAll(upgradeMap.values());

        List<DecisionDailybarOutput> decisionDailybarOutputs = tenantCrudService.findByNamedQuery(DecisionDailybarOutput.FIND_BY_OCCUPANCY_DATE_BETWEEN,
                QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());

        Map<Product, Map<LocalDate, List<DecisionDailybarOutput>>> decisionMap = decisionDailybarOutputs.stream()
                .filter(decision -> freeUpgradeProducts.contains(decision.getProduct())) // check if product is free upgrade
                .filter(decision -> accomTypesToConsider.contains(decision.getAccomType().getAccomTypeCode())) // check if accom type is part of upgrade
                .collect(Collectors.groupingBy(DecisionDailybarOutput::getProduct, Collectors.groupingBy(DecisionDailybarOutput::getOccupancyDate)));


        // loop for each product and date. for each of product/date, we get a set of decisions - one per accom type
        // for each upgrade path, update appropriate accom type decisions and save
        decisionMap.forEach((product, decisionsForAProduct) -> decisionsForAProduct.forEach((occupancyDate, decisionsForProductAndDate) -> {

            List<DecisionDailybarOutput> upgradedDecisions = new ArrayList<>();

            Map<String, DecisionDailybarOutput> decisionsToCheckByAccomType = decisionsForProductAndDate.stream()
                    .collect(Collectors.toMap(d -> d.getAccomType().getAccomTypeCode(), Function.identity()));

            Map<String, DecisionDailybarOutput> originalDecisionClones = new HashMap<>();
            decisionsToCheckByAccomType.forEach((accomTypeCode, decision) ->
                    originalDecisionClones.put(accomTypeCode, createDecisionDailybarCopy(decision)));

            upgradeMap.forEach((destination, source) -> {
                DecisionDailybarOutput sourceDecision = originalDecisionClones.get(source);
                DecisionDailybarOutput destinationDecision = decisionsToCheckByAccomType.get(destination);
                copyDecisionDailybarRateValues(sourceDecision, destinationDecision);
                upgradedDecisions.add(destinationDecision);
            });

            tenantCrudService.save(upgradedDecisions);

        }));
        tenantCrudService.flush();
    }

    private DecisionDailybarOutput createDecisionDailybarCopy(DecisionDailybarOutput original) {
        DecisionDailybarOutput clone = new DecisionDailybarOutput();
        copyDecisionDailybarRateValues(original, clone);
        return clone;
    }

    private void copyDecisionDailybarRateValues(DecisionDailybarOutput source, DecisionDailybarOutput target) {
        target.setSingleRate(source.getSingleRate());
        target.setDoubleRate(source.getDoubleRate());
        target.setTripleRate(source.getTripleRate());
        target.setQuadRate(source.getQuadRate());
        target.setQuintRate(source.getQuintRate());
        target.setAdultRate(source.getAdultRate());
        target.setChildRate(source.getChildRate());
        target.setOneChildRate(source.getOneChildRate());
        target.setTwoChildRate(source.getTwoChildRate());
        target.setThreeChildRate(source.getThreeChildRate());
        target.setFourChildRate(source.getFourChildRate());
        target.setFiveChildRate(source.getFiveChildRate());
        target.setChildAgeOneRate(source.getChildAgeOneRate());
        target.setChildAgeTwoRate(source.getChildAgeTwoRate());
        target.setChildAgeThreeRate(source.getChildAgeThreeRate());
    }
}
