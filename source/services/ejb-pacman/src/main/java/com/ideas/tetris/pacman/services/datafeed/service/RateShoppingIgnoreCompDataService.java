package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.datafeed.dto.rateshopping.RateShoppingIgnoreCompetitor;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompChannelMapping;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateOverrideCompetitor;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateOverrideCompetitorDetails;
import com.ideas.tetris.pacman.services.webrate.service.CompetitorDataFilterService;
import com.ideas.tetris.pacman.services.webrate.service.WebrateShoppingDataService;
import com.ideas.tetris.pacman.util.CollectionUtils;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.Objects.nonNull;

/**
 * Created by idnekp on 2/16/2016.
 */
@Component
@Transactional
public class RateShoppingIgnoreCompDataService {

    @Autowired
    CompetitorDataFilterService competitorDataFilterService;

    @Autowired
    AgileRatesConfigurationService agileRatesConfigurationService;

    @Autowired
    WebrateShoppingDataService webrateShoppingDataService;

    @Autowired
    private PacmanConfigParamsService configParamsService;


    public List<RateShoppingIgnoreCompetitor> getRateShoppingIgnoreCompData(Integer propertyId, Date startDate) {

        List<Product> independentSystemDefaultProducts = agileRatesConfigurationService.findAgileAndSystemDefaultProductsAndIndependentProducts();
        Map<Integer, String> independentSystemDefaultProductsMap = independentSystemDefaultProducts.stream()
                .collect(Collectors.toMap(Product::getId, Product::getName));

        List<RateShoppingIgnoreCompetitor> rateShoppingIgnoreCompetitors = new ArrayList<>();
        List<WebrateOverrideCompetitor> webrateOverrideCompetitorList = competitorDataFilterService.getAllOverrideCompetitorByProperty(propertyId);
        webrateOverrideCompetitorList = webrateOverrideCompetitorList.stream()
                .filter(competitor -> competitor.getCompetitorOverrideEndDT().after(startDate) || competitor.getCompetitorOverrideEndDT().equals(startDate))
                .collect(Collectors.toList());

        webrateOverrideCompetitorList.forEach(competitor -> {
            competitor.getWebrateOverrideCompetitorDetails().forEach(competitorDetails -> {

                RateShoppingIgnoreCompetitor rateShoppingIgnoreCompetitor = new RateShoppingIgnoreCompetitor();
                rateShoppingIgnoreCompetitor.setCompetitorName(competitorDetails.getWebrateCompetitorsAccomClass().getWebrateCompetitor().getWebrateCompetitorsName());
                rateShoppingIgnoreCompetitor.setStartDate(competitor.getCompetitorOverrideStartDT());
                rateShoppingIgnoreCompetitor.setEndDate(competitor.getCompetitorOverrideEndDT());
                rateShoppingIgnoreCompetitor.setRoomClassCode(competitorDetails.getWebrateCompetitorsAccomClass().getAccomClass().getCode());
                rateShoppingIgnoreCompetitor.setSunday(getStringValueFromInteger(competitor.getIgnoreCompetitorDataOnSunday()));
                rateShoppingIgnoreCompetitor.setMonday(getStringValueFromInteger(competitor.getIgnoreCompetitorDataOnMonday()));
                rateShoppingIgnoreCompetitor.setTuesday(getStringValueFromInteger(competitor.getIgnoreCompetitorDataOnTuesday()));
                rateShoppingIgnoreCompetitor.setWednesday(getStringValueFromInteger(competitor.getIgnoreCompetitorDataOnWednesday()));
                rateShoppingIgnoreCompetitor.setThursday(getStringValueFromInteger(competitor.getIgnoreCompetitorDataOnThursday()));
                rateShoppingIgnoreCompetitor.setFriday(getStringValueFromInteger(competitor.getIgnoreCompetitorDataOnFriday()));
                rateShoppingIgnoreCompetitor.setSaturday(getStringValueFromInteger(competitor.getIgnoreCompetitorDataOnSaturday()));
                rateShoppingIgnoreCompetitor.setNotes(competitor.getNotes());

                Integer productId = competitorDetails.getProductID();
                rateShoppingIgnoreCompetitor.setProductName(independentSystemDefaultProductsMap.getOrDefault(productId, independentSystemDefaultProductsMap.get(1)));
                if (productId != null && shouldIncludeChannelDetailsInDatafeed()) {
                    rateShoppingIgnoreCompetitors.addAll(getCompetitorWithChannelDetails(competitorDetails, rateShoppingIgnoreCompetitor));
                }
                else {
                    rateShoppingIgnoreCompetitors.add(rateShoppingIgnoreCompetitor);
                }
            });
        });

        return rateShoppingIgnoreCompetitors;
    }

    private boolean shouldIncludeChannelDetailsInDatafeed() {
        return configParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_CHANNEL_ENABLED_IN_IGNORE_COMPETITOR_DATAFEED);
    }

    public List<RateShoppingIgnoreCompetitor> getCompetitorWithChannelDetails(WebrateOverrideCompetitorDetails competitorDetails,
                                                                              RateShoppingIgnoreCompetitor rateShoppingIgnoreCompetitor) {
        Set<Integer> competitorIds = Collections.singleton(competitorDetails.getWebrateCompetitorsAccomClass().getWebrateCompetitor().getId());
        List<WebrateCompChannelMapping> channelMappings = getChannelMappings(competitorDetails.getProductID(), competitorIds);
        return addCompetitorsWithChannels(getAllChannels(channelMappings), rateShoppingIgnoreCompetitor);
    }

    private List<WebrateCompChannelMapping> getChannelMappings(Integer productId, Set<Integer> competitorIds) {
        return webrateShoppingDataService.getWebrateCompChannelMappingsByProductIdAndCompetitorIds(productId, competitorIds);
    }

    private Set<String> getAllChannels(List<WebrateCompChannelMapping> channelMappings) {
        if (CollectionUtils.isEmpty(channelMappings)) {
            return Collections.emptySet();
        }
        return channelMappings.stream()
                .map(mapping -> nonNull(mapping.getWebrateChannel())? mapping.getWebrateChannel().getWebrateChannelName() : null)
                .filter(Objects::nonNull)
                .filter(channel -> !channel.trim().isEmpty())
                .collect(Collectors.toSet());
    }

    private List<RateShoppingIgnoreCompetitor> addCompetitorsWithChannels(Set<String> channels, RateShoppingIgnoreCompetitor competitor) {
        if(CollectionUtils.isEmpty(channels)) {
            return Collections.singletonList(competitor);
        }
        return channels.stream().map(channel -> cloneWithChannel(competitor, channel)).collect(Collectors.toList());
    }

    private RateShoppingIgnoreCompetitor cloneWithChannel(RateShoppingIgnoreCompetitor templateCompetitor, String channel) {
        RateShoppingIgnoreCompetitor competitor = new RateShoppingIgnoreCompetitor();
        competitor.setCompetitorName(templateCompetitor.getCompetitorName());
        competitor.setStartDate(templateCompetitor.getStartDate());
        competitor.setEndDate(templateCompetitor.getEndDate());
        competitor.setRoomClassCode(templateCompetitor.getRoomClassCode());
        competitor.setSunday(templateCompetitor.getSunday());
        competitor.setMonday(templateCompetitor.getMonday());
        competitor.setTuesday(templateCompetitor.getTuesday());
        competitor.setWednesday(templateCompetitor.getWednesday());
        competitor.setThursday(templateCompetitor.getThursday());
        competitor.setFriday(templateCompetitor.getFriday());
        competitor.setSaturday(templateCompetitor.getSaturday());
        competitor.setNotes(templateCompetitor.getNotes());
        competitor.setProductName(templateCompetitor.getProductName());
        competitor.setChannel(channel);
        return competitor;
    }
    private String getStringValueFromInteger(Integer dayValue) {
        return dayValue == 1 ? "Yes" : "No";
    }

}
