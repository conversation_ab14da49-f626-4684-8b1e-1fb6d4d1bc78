package com.ideas.tetris.pacman.services.rcrtautoconfig;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClassPriceRank;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClassPriceRankStatus;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccomClassPriceRankService;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomClassMinPriceDiff;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.groupfinalforecast.GroupFinalForecastConfigService;
import com.ideas.tetris.pacman.services.job.JobMonitorService;
import com.ideas.tetris.pacman.services.job.entity.JobView;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegDetails;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegDetailsProposed;
import com.ideas.tetris.pacman.services.overbooking.entity.OverbookingAccom;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.common.constants.Constants.COMMA;
import static com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig.getG3DataFolder;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class RCRTAutoConfigurationService {

    public static final String PROPERTY_ID = "propertyId";
    private static final String FOLDER_NAME = "RCRTAutoConfiguration";
    private static final String INVALID_CSV_FILE_CONTENTS = "Invalid content in CSV file";
    private static final String ACCOM_CLASS = "RC";
    private static final long JOB_TIMEOUT = 120000;
    private static final Logger LOGGER = Logger.getLogger(RCRTAutoConfigurationService.class);
    public static final int ACCOMMODATION_OVERBOOKING_TYPE = 1;
    public static final BigDecimal MINIMUM_PRICE_DIFFERENTIAL = BigDecimal.valueOf(0.01);

    @Autowired
    JobServiceLocal jobService;

    @Autowired
	private JobMonitorService jobMonitorService;

    @Autowired
    AccommodationService accommodationService;

    @Autowired
    AccomClassPriceRankService accomClassPriceRankService;

    @Autowired
    GroupFinalForecastConfigService groupFinalForecastConfigService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    @ForTesting


    public JobView triggerRCRTAutoConfigurationJob() {
        Long jobExecutionId = triggerRoomClassRoomTypeAutoConfigurationJob();
        Long jobInstanceId = jobMonitorService.getJobInstanceId(jobExecutionId);
        return jobMonitorService.getJobViewWaitUntilCompletedOrAbandoned(jobInstanceId, false, JOB_TIMEOUT);
    }


    public Long triggerRoomClassRoomTypeAutoConfigurationJob() {
        Map<String, Object> params = new HashMap<>();
        params.put(JobParameterKey.PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
        return jobService.startGuaranteedNewInstance(JobName.RCRTAutoConfigurationJob, params);
    }

    public void saveRCRTAutoConfigurationMacroOutput() {
        try (InputStream inputStream = new FileInputStream(getRCRTAutoConfigurationMacroExcelOutputPath())) {
            saveToTenantDB(inputStream);
        } catch (IOException e) {
            throw new TetrisException(ErrorCode.FILE_PROCESSING_ERROR, "RC Automation file not found or not populated", e);
        }
    }

    private void saveToTenantDB(InputStream inputStream) {
        List<AccomClassProposed> proposedMappingList = createAccomClassProposed(inputStream);
        proposedMappingList.sort(new Comparator<>() {
            @Override
            public int compare(AccomClassProposed proposedClass1, AccomClassProposed proposedClass2) {
                return getProposedClassNumber(proposedClass1) - getProposedClassNumber(proposedClass2);
            }

            private int getProposedClassNumber(AccomClassProposed proposed) {
                String proposedClassNumber = proposed.getAccomClassName().replace(ACCOM_CLASS, "");
                return proposedClassNumber.isEmpty() ? 0 : Integer.parseInt(proposedClassNumber);
            }
        });
        assignMasterClass(proposedMappingList);
        tenantCrudService.deleteAll(AccomClassProposed.class);
        tenantCrudService.save(proposedMappingList);
    }

    protected void assignMasterClass(List<AccomClassProposed> proposedMappingList) {
        Map<String, Integer> accomClassProposedMap = new HashMap<>();
        for (AccomClassProposed proposedClass : proposedMappingList) {
            if (!accomClassProposedMap.containsKey(proposedClass.getAccomClassName())) {
                accomClassProposedMap.put(proposedClass.getAccomClassName(), proposedClass.getAccomTypeCapacity());
            } else {
                accomClassProposedMap.put(proposedClass.getAccomClassName(), accomClassProposedMap.get(proposedClass.getAccomClassName()) + proposedClass.getAccomTypeCapacity());
            }
        }
        String masterAccomClassName = Collections.max(accomClassProposedMap.entrySet(), Map.Entry.comparingByValue()).getKey();
        for (AccomClassProposed proposedClass : proposedMappingList) {
            if (proposedClass.getAccomClassName().equals(masterAccomClassName)) {
                proposedClass.setMasterClassBoolean(true);
            }
        }
    }

    private List<AccomClassProposed> createAccomClassProposed(InputStream inputStream) {
        Map<Integer, AccomType> accomTypeMap = getAccomTypes().stream().collect(Collectors.toMap(AccomType::getId, Function.identity()));
        try (BufferedReader br = new BufferedReader(new InputStreamReader(inputStream))) {
            return br.lines().skip(1).filter(this::isValidRow).map((String line) -> mapToEntity(line, accomTypeMap)).collect(toList());
        } catch (IOException e) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, INVALID_CSV_FILE_CONTENTS, e);
        }
    }

    public void createAndSaveAccomClasses(Map<Integer, List<Integer>> rcRTMap) {
        List<AccomType> accomTypes = getAccomTypes();
        List<AccomClass> accomClasses = createAccomClasses(rcRTMap, accomTypes);
        List<AccomType> unassignedAccomTypes = accomTypes.stream().filter(accomType -> accomType.getAccomClass().getSystemDefault() == 1).collect(toList());
        assignMasterClassToUnassignedAccomTypes(accomClasses, unassignedAccomTypes);
        updateAccomClassOrders(accomClasses);
        tenantCrudService.save(accomClasses);
    }

    protected void updateAccomClassOrders(List<AccomClass> accomClasses) {
        accomClasses.sort(Comparator.comparingInt(AccomClass::getRankOrder));
        IntStream.range(0, accomClasses.size()).forEach(i -> {
            accomClasses.get(i).setRankOrder(i + 1);
            accomClasses.get(i).setViewOrder(i + 1);
        });
    }

    private List<AccomClass> createAccomClasses(Map<Integer, List<Integer>> rcRtMap, List<AccomType> accomTypes) {
        ArrayList<AccomClass> accomClasses = new ArrayList<>();
        rcRtMap.forEach((acId, atIdList) -> {
            List<AccomType> accomTypesForID = accomTypes.stream().filter(accomType -> atIdList.contains(accomType.getId())).collect(toList());
            accomClasses.add(createAccomClass(acId, accomTypesForID));
        });
        return accomClasses;
    }

    protected void assignMasterClassToUnassignedAccomTypes(List<AccomClass> accomClassesSet, List<AccomType> unassignedAccomTypes) {
        Optional<AccomClass> masterAccomClass = accomClassesSet.stream().max(Comparator.comparing(AccomClass::getCapacity));
        masterAccomClass.ifPresent(accomClass -> {
            accomClass.setMasterClassBoolean(true);
            accomClass.setAccomTypes(Stream.concat(accomClass.getAccomTypes().stream(), unassignedAccomTypes.stream()).collect(Collectors.toSet()));
            unassignedAccomTypes.forEach(accomType -> accomType.setAccomClass(accomClass));
        });
    }

    protected AccomClass createAccomClass(Integer accomClassId, List<AccomType> accomTypes) {
        AccomClass accomClass = new AccomClass();
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        accomClass.setPropertyId(propertyId);
        accomClass.setName(ACCOM_CLASS + accomClassId);
        accomClass.setCode(ACCOM_CLASS + accomClassId);
        accomClass.setStatusId(Constants.ACTIVE_STATUS_ID);
        accomClass.setAccomTypes(Set.copyOf(accomTypes));
        accomClass.setCapacity(accomTypes.stream().map(AccomType::getAccomTypeCapacity).reduce(0, Integer::sum));
        accomClass.setSystemDefault(0);
        accomClass.setDescription(ACCOM_CLASS + accomClassId);
        accomClass.setMasterClass(0);
        accomClass.setViewOrder(accomClassId);
        accomClass.setRankOrder(accomClassId);
        LOGGER.info("Creating AccomClass: " + accomClass.getName() + " for Property: " + propertyId);
        accomTypes.forEach(accomType -> accomType.setAccomClass(accomClass));
        return accomClass;
    }


    private List<AccomType> getAccomTypes() {
        return accommodationService.getAllActiveAccomTypes();
    }

    private AccomClassProposed mapToEntity(String line, Map<Integer, AccomType> accomTypeMap) {
        String[] row = line.split(COMMA);
        AccomClassProposed accomClassProposed = new AccomClassProposed();
        accomClassProposed.setAccomClassName(ACCOM_CLASS + row[1]);
        accomClassProposed.setAccomType(accomTypeMap.get(Integer.parseInt(row[2])));
        accomClassProposed.setMasterClassBoolean(false);
        accomClassProposed.setAccomTypeCapacity(accomClassProposed.getAccomType().getAccomTypeCapacity());
        accomClassProposed.setCreatedByUserId(Integer.valueOf(Objects.requireNonNull(PacmanWorkContextHelper.getUserId())));
        accomClassProposed.setCreateDate(LocalDateTime.now());
        return accomClassProposed;
    }

    protected Boolean isValidRow(String row) {
        List<String> rows = Stream.of(row.split(COMMA)).collect(toList());
        return rows.size() == 3 && rows.stream().allMatch(s -> StringUtils.isNotEmpty(s) && StringUtils.isNumeric(s));
    }

    public String getRCRTAutoConfigurationMacroExcelOutputPath() {
        return getRecommendationFilesOutputDirectory() + File.separator + "ac_grouping" + File.separator
                + String.format("accom_type_%d.csv", PacmanWorkContextHelper.getPropertyId());
    }

    public String getRecommendationFilesOutputDirectory() {
        return getG3DataFolder() + FOLDER_NAME + File.separator +
                PacmanWorkContextHelper.getClientCode() + File.separator + PacmanWorkContextHelper.getPropertyCode();
    }

    public List<AccomClassProposed> getProposedAccomClasses() {
        return tenantCrudService.findAll(AccomClassProposed.class);
    }

    public boolean marketSegmentDetailsArePopulated() {
        return CollectionUtils.isNotEmpty(tenantCrudService.findAll(MktSegDetails.class))
                || CollectionUtils.isNotEmpty(tenantCrudService.findAll(MktSegDetailsProposed.class));
    }

    public List<AccomClass> getAccomClasses() {
        List<AccomClass> accomClasses = tenantCrudService.findByNamedQuery(AccomClass.ALL, QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
        return accomClasses.stream().filter(accomClass -> accomClass.getSystemDefault().equals(0)).collect(toList());
    }

    public void autoConfigureOverbookingTypeForAllRoomTypes() {
        List<AccomType> accomTypes = getAllAccomTypes();
        List<OverbookingAccom> overbookingAccomsForSave = accomTypes.stream()
                .map(this::saveOverbookingAccomFor)
                .collect(toList());
        tenantCrudService.save(overbookingAccomsForSave);
    }

    public void autoConfigureOverbookingTypeForROHRoomType() {
        getAllAccomTypes().stream()
                .filter(accomType -> accomType.getAccomClass().isMstClsDefined())
                .max(Comparator.comparing(AccomType::getAccomTypeCapacity))
                .ifPresent(accomType -> {
                    accomType.setRohType(1);
                    LOGGER.debug("Accom Type " + accomType.getName() + " has been assigned ROH Overbooking Type");
                    tenantCrudService.save(accomType);
                });
    }

    private OverbookingAccom saveOverbookingAccomFor(AccomType accomType) {
        OverbookingAccom overbookingAccom = getOverbookingAccomByAccomType(accomType);
        if (null == overbookingAccom) {
            overbookingAccom = new OverbookingAccom();
        }
        overbookingAccom.setAccomTypeId(accomType.getId());
        overbookingAccom.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        overbookingAccom.setSundayCeiling(-1);
        overbookingAccom.setMondayCeiling(-1);
        overbookingAccom.setTuesdayCeiling(-1);
        overbookingAccom.setWednesdayCeiling(-1);
        overbookingAccom.setThursdayCeiling(-1);
        overbookingAccom.setFridayCeiling(-1);
        overbookingAccom.setSaturdayCeiling(-1);
        overbookingAccom.setSundayOverbookingTypeId(ACCOMMODATION_OVERBOOKING_TYPE);
        overbookingAccom.setMondayOverbookingTypeId(ACCOMMODATION_OVERBOOKING_TYPE);
        overbookingAccom.setTuesdayOverbookingTypeId(ACCOMMODATION_OVERBOOKING_TYPE);
        overbookingAccom.setWednesdayOverbookingTypeId(ACCOMMODATION_OVERBOOKING_TYPE);
        overbookingAccom.setThursdayOverbookingTypeId(ACCOMMODATION_OVERBOOKING_TYPE);
        overbookingAccom.setFridayOverbookingTypeId(ACCOMMODATION_OVERBOOKING_TYPE);
        overbookingAccom.setSaturdayOverbookingTypeId(ACCOMMODATION_OVERBOOKING_TYPE);
        return overbookingAccom;
    }

    private OverbookingAccom getOverbookingAccomByAccomType(AccomType accomType) {
        return tenantCrudService.findByNamedQuerySingleResult(
                OverbookingAccom.BY_ACCOMTYPEID, QueryParameter.with("accomTypeId", accomType.getId())
                        .parameters());
    }

    private List<AccomType> getAllAccomTypes() {
        return accommodationService.getAllActiveAccomTypes();
    }

    public void autoConfigurePriceRankingAndUpgradePath() {
        List<AccomClass> accomClassesByRankOrder = accommodationService.getAssignedAccomClassesByRankOrder();
        List<AccomClassPriceRank> accomClassPriceRanks = new ArrayList<>();
        for (int i = 0; i < accomClassesByRankOrder.size() - 1; i++) {
            accomClassPriceRanks.add(new AccomClassPriceRank(accomClassesByRankOrder.get(i), accomClassesByRankOrder.get(i + 1), true));
        }
        tenantCrudService.save(accomClassPriceRanks);
        accomClassesByRankOrder
                .forEach(accomClass -> accomClass.setIsPriceRankConfigured(AccomClassPriceRankStatus.COMPLETE_RANKED));
        tenantCrudService.save(accomClassesByRankOrder);

    }

    public void autoConfigureMinimumPriceDifferential() {
        List<AccomClassPriceRank> accomClassPriceRanks = getAllAccomClassPriceRanks();
        List<AccomClassMinPriceDiff> accomClassMinPriceDiffList = accomClassPriceRanks.stream()
                .map(this::createAccomClassMinDiff)
                .collect(toList());
        tenantCrudService.save(accomClassMinPriceDiffList);
    }

    private AccomClassMinPriceDiff createAccomClassMinDiff(AccomClassPriceRank accomClassPriceRank) {
        AccomClassMinPriceDiff accomClassMinPriceDiff = new AccomClassMinPriceDiff();
        accomClassMinPriceDiff.setAccomClassPriceRank(accomClassPriceRank);
        accomClassMinPriceDiff.setSundayDiff(MINIMUM_PRICE_DIFFERENTIAL);
        accomClassMinPriceDiff.setMondayDiff(MINIMUM_PRICE_DIFFERENTIAL);
        accomClassMinPriceDiff.setTuesdayDiff(MINIMUM_PRICE_DIFFERENTIAL);
        accomClassMinPriceDiff.setWednesdayDiff(MINIMUM_PRICE_DIFFERENTIAL);
        accomClassMinPriceDiff.setThursdayDiff(MINIMUM_PRICE_DIFFERENTIAL);
        accomClassMinPriceDiff.setFridayDiff(MINIMUM_PRICE_DIFFERENTIAL);
        accomClassMinPriceDiff.setSaturdayDiff(MINIMUM_PRICE_DIFFERENTIAL);
        accomClassMinPriceDiff.setSundayDiffWithTax(MINIMUM_PRICE_DIFFERENTIAL);
        accomClassMinPriceDiff.setMondayDiffWithTax(MINIMUM_PRICE_DIFFERENTIAL);
        accomClassMinPriceDiff.setTuesdayDiffWithTax(MINIMUM_PRICE_DIFFERENTIAL);
        accomClassMinPriceDiff.setWednesdayDiffWithTax(MINIMUM_PRICE_DIFFERENTIAL);
        accomClassMinPriceDiff.setThursdayDiffWithTax(MINIMUM_PRICE_DIFFERENTIAL);
        accomClassMinPriceDiff.setFridayDiffWithTax(MINIMUM_PRICE_DIFFERENTIAL);
        accomClassMinPriceDiff.setSaturdayDiffWithTax(MINIMUM_PRICE_DIFFERENTIAL);
        return accomClassMinPriceDiff;
    }

    public List<AccomClassPriceRank> getAllAccomClassPriceRanks() {
        return tenantCrudService.findAll(AccomClassPriceRank.class);
    }

    public void autoConfigureRoomClassCapacityRatio() {
        groupFinalForecastConfigService.populateGffConfigWithDefaults(PacmanWorkContextHelper.getPropertyId());
    }

    public Map<Integer, List<Integer>> getRCRTMappingFromCSV() {
        Map<Integer, List<Integer>> rcrtResultMap = new HashMap<>();
        try (FileReader reader = new FileReader(getRCRTAutoConfigurationMacroExcelOutputPath())) {
            CSVParser records = getCSVRecords(reader);
            rcrtResultMap.putAll(buildRCRTMappingfromCSVRecords(records));
        } catch (FileNotFoundException e) {
            throw new TetrisException(ErrorCode.FILE_NOT_PRESENT, "SAS macro output for RC-RT Auto Configuration was not found", e);
        } catch (IOException | NumberFormatException e) {
            throw new TetrisException(ErrorCode.FILE_PROCESSING_ERROR, e.getMessage(), e);
        }
        if (rcrtResultMap.isEmpty()) {
            throw new TetrisException(ErrorCode.FILE_PROCESSING_ERROR, "No accom classes mappings were received in SAS macro output for RC-RT Auto Configuration");
        }
        return rcrtResultMap;
    }

    private Map<Integer, List<Integer>> buildRCRTMappingfromCSVRecords(CSVParser records) {
        return records.stream()
                .filter(csvRecord -> isNotBlank(csvRecord.get(1)))
                .collect(groupingBy(csvRecord -> Integer.parseInt(csvRecord.get(1).trim()),
                        mapping(csvRecord -> Integer.parseInt(csvRecord.get(2).trim()), toList())));
    }

    private static CSVParser getCSVRecords(FileReader reader) throws IOException {
        return CSVFormat.DEFAULT.builder()
                .setHeader("property_id", "accom_class_id", "accom_type_id")
                .setSkipHeaderRecord(true)
                .build()
                .parse(reader);
    }
}
