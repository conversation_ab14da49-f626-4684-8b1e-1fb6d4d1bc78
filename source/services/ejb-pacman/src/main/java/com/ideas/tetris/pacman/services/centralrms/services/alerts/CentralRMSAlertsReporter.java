package com.ideas.tetris.pacman.services.centralrms.services.alerts;

import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.centralrms.dto.alerts.ceilingfloor.ExtendedCeilingFloorAlertReport;
import com.ideas.tetris.pacman.services.centralrms.dto.alerts.ceilingfloorconstraints.*;
import com.ideas.tetris.pacman.services.centralrms.dto.alerts.competitoroutlier.CompetitorOutlierAlertReport;
import com.ideas.tetris.pacman.services.centralrms.dto.alerts.groupcutoff.GroupCutoffAlertReport;
import com.ideas.tetris.pacman.services.centralrms.dto.alerts.hoteloutlier.HotelOutlierAlertReport;
import com.ideas.tetris.pacman.services.centralrms.dto.alerts.overbokingconfiguration.OverbookingConfigurationAlertReport;
import com.ideas.tetris.pacman.services.centralrms.dto.alerts.overbookingconstraint.OverbookingConstraintAlertReport;
import com.ideas.tetris.pacman.services.centralrms.dto.alerts.overbookingconstraint.OverbookingConstraintAlertRevenueReport;
import com.ideas.tetris.pacman.services.centralrms.dto.alerts.overbookingconstraint.OverbookingOverrideAlertReport;
import com.ideas.tetris.pacman.services.centralrms.dto.alerts.smallgap.SmallGapAlertReport;
import com.ideas.tetris.pacman.services.centralrms.dto.alerts.unexpectedbookingpace.UnexpectedBookingPaceAlertReport;
import com.ideas.tetris.pacman.services.centralrms.dto.alerts.unexpectedbookingpace.UnexpectedBookingPaceScoringAlertReport;
import com.ideas.tetris.pacman.services.centralrms.dto.rooms.CentralRMSRoom;
import com.ideas.tetris.pacman.services.retryservice.RetryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.List;
import java.util.function.Function;

import static com.ideas.tetris.pacman.common.constants.Constants.*;

@Slf4j
@Component
@Transactional
public class CentralRMSAlertsReporter {

    @Autowired
    private RestTemplate centralRMSCommonAlertsRestTemplate;

    @Autowired
	private RetryService retryService;

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void reportExtendedCeilingFloorAlerts(ExtendedCeilingFloorAlertReport report) {
        HttpHeaders headers = getCentralRMSHeaders();
        HttpEntity<ExtendedCeilingFloorAlertReport> entity = new HttpEntity<>(report, headers);
        retryService.attempt(
                () -> centralRMSCommonAlertsRestTemplate.postForObject(getCentralRMSAlertsBaseUri() + "ceilingfloor/v2/report", entity, Void.class),
                3,
                Duration.ofMillis(100),
                http5xxServerErrorRetryClassifier()
        );
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void reportHotelOutlierAlerts(HotelOutlierAlertReport report) {
        HttpHeaders headers = getCentralRMSHeaders();
        HttpEntity<HotelOutlierAlertReport> entity = new HttpEntity<>(report, headers);
        retryService.attempt(
                () -> centralRMSCommonAlertsRestTemplate.postForObject(getCentralRMSAlertsBaseUri() + "hotel-outlier/v1/report", entity, Void.class),
                3,
                Duration.ofMillis(100),
                http5xxServerErrorRetryClassifier()
        );
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void reportCompetitorOutlierAlerts(CompetitorOutlierAlertReport report) {
        HttpHeaders headers = getCentralRMSHeaders();
        HttpEntity<CompetitorOutlierAlertReport> entity = new HttpEntity<>(report, headers);
        retryService.attempt(
                () -> centralRMSCommonAlertsRestTemplate.postForObject(getCentralRMSAlertsBaseUri() + "competitor-outlier/v1/report", entity, Void.class),
                3,
                Duration.ofMillis(100),
                http5xxServerErrorRetryClassifier()
        );
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void reportSmallGapAlerts(SmallGapAlertReport report) {
        HttpHeaders headers = getCentralRMSHeaders();
        HttpEntity<SmallGapAlertReport> entity = new HttpEntity<>(report, headers);
        retryService.attempt(
                () -> centralRMSCommonAlertsRestTemplate.postForObject(getCentralRMSAlertsBaseUri() + "small-gap/v1/report", entity, Void.class),
                3,
                Duration.ofMillis(100),
                http5xxServerErrorRetryClassifier()
        );
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void reportUnexpectedBookingPaceAlerts(UnexpectedBookingPaceAlertReport report) {
        HttpHeaders headers = getCentralRMSHeaders();
        HttpEntity<UnexpectedBookingPaceAlertReport> entity = new HttpEntity<>(report, headers);
        retryService.attempt(
                () -> centralRMSCommonAlertsRestTemplate.postForObject(getCentralRMSAlertsBaseUri() + "unexpected-booking-pace/v1/report", entity, Void.class),
                3,
                Duration.ofMillis(100),
                http5xxServerErrorRetryClassifier()
        );
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void reportUnexpectedBookingScoringPaceAlerts(UnexpectedBookingPaceScoringAlertReport report) {
        HttpHeaders headers = getCentralRMSHeaders();
        HttpEntity<UnexpectedBookingPaceScoringAlertReport> entity = new HttpEntity<>(report, headers);
        retryService.attempt(
                () -> centralRMSCommonAlertsRestTemplate.postForObject(getCentralRMSAlertsBaseUri() + "unexpected-booking-pace/v2/report", entity, Void.class),
                3,
                Duration.ofMillis(100),
                http5xxServerErrorRetryClassifier()
        );
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void reportCeilingFloorConstraintsAlerts(CeilingFloorConstraintsAlertReport report) {
        HttpHeaders headers = getCentralRMSHeaders();
        HttpEntity<CeilingFloorConstraintsAlertReport> entity = new HttpEntity<>(report, headers);
        retryService.attempt(
                () -> centralRMSCommonAlertsRestTemplate.postForObject(getCentralRMSAlertsBaseUri() + "ceiling-floor-constraints/v1/report", entity, Void.class),
                3,
                Duration.ofMillis(100),
                http5xxServerErrorRetryClassifier()
        );
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void reportCeilingFloorConstraintsAlertsV2(CeilingFloorConstraintsAlertReportV2 report) {
        HttpHeaders headers = getCentralRMSHeaders();
        HttpEntity<CeilingFloorConstraintsAlertReportV2> entity = new HttpEntity<>(report, headers);
        retryService.attempt(
                () -> centralRMSCommonAlertsRestTemplate.postForObject(getCentralRMSAlertsBaseUri() + "ceiling-floor-constraints/v2/report", entity, Void.class),
                3,
                Duration.ofMillis(100),
                http5xxServerErrorRetryClassifier()
        );
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void reportGroupCutoffAlerts(GroupCutoffAlertReport report) {
        HttpHeaders headers = getCentralRMSHeaders();
        HttpEntity<GroupCutoffAlertReport> entity = new HttpEntity<>(report, headers);
        retryService.attempt(
                () -> centralRMSCommonAlertsRestTemplate.postForObject(getCentralRMSAlertsBaseUri() + "group-cutoff/v1/report", entity, Void.class),
                3,
                Duration.ofMillis(100),
                http5xxServerErrorRetryClassifier()
        );
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void reportCeilingFloorConstraintsAlertsAnalyticsGeneratedData(List<CeilingFloorConstraintsAnalyticsData> report) {
        HttpHeaders headers = getCentralRMSHeaders();
        HttpEntity<List<CeilingFloorConstraintsAnalyticsData>> entity = new HttpEntity<>(report, headers);
        retryService.attempt(
                () -> centralRMSCommonAlertsRestTemplate.postForObject(getCentralRMSAlertsBaseUri() + "ceiling-floor-constraints/v1/report/analytics-generated-data", entity, Void.class),
                3,
                Duration.ofMillis(100),
                http5xxServerErrorRetryClassifier()
        );
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void reportCeilingFloorConstraintsAlertsAnalyticsGeneratedDataV2(CeilingFloorConstraintsAnalyticsDataReportV2 report) {
        HttpHeaders headers = getCentralRMSHeaders();

        HttpEntity<CeilingFloorConstraintsAnalyticsDataReportV2> entity = new HttpEntity<>(report, headers);
        retryService.attempt(
                () -> centralRMSCommonAlertsRestTemplate.postForObject(getCentralRMSAlertsBaseUri() + "ceiling-floor-constraints/v2/report/analytics-generated-data", entity, Void.class),
                3,
                Duration.ofMillis(100),
                http5xxServerErrorRetryClassifier()
        );
    }


    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void reportOverbookingConstraintAlerts(OverbookingConstraintAlertReport report) {
        HttpHeaders headers = getCentralRMSHeaders();
        HttpEntity<OverbookingConstraintAlertReport> entity = new HttpEntity<>(report, headers);
        retryService.attempt(
                () -> centralRMSCommonAlertsRestTemplate.postForObject(getCentralRMSAlertsBaseUri() + "overbooking-constraints/v1/report", entity, Void.class),
                3,
                Duration.ofMillis(100),
                http5xxServerErrorRetryClassifier()
        );
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void reportOverbookingConfigurationAlerts(OverbookingConfigurationAlertReport report) {
        HttpHeaders headers = getCentralRMSHeaders();
        HttpEntity<OverbookingConfigurationAlertReport> entity = new HttpEntity<>(report, headers);
        retryService.attempt(
                () -> centralRMSCommonAlertsRestTemplate.postForObject(getCentralRMSAlertsBaseUri() + "overbooking-constraints-config/v1/report", entity, Void.class),
                3,
                Duration.ofMillis(100),
                http5xxServerErrorRetryClassifier()
        );
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void reportOverbookingOverrideAlerts(OverbookingOverrideAlertReport report) {
        HttpHeaders headers = getCentralRMSHeaders();
        HttpEntity<OverbookingOverrideAlertReport> entity = new HttpEntity<>(report, headers);
        retryService.attempt(
                () -> centralRMSCommonAlertsRestTemplate.postForObject(getCentralRMSAlertsBaseUri() + "overbooking-constraint-override/v1/report", entity, Void.class),
                3,
                Duration.ofMillis(100),
                http5xxServerErrorRetryClassifier()
        );
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void reportOverbookingConstraintAlertRevenue(OverbookingConstraintAlertRevenueReport report) {
        HttpHeaders headers = getCentralRMSHeaders();
        HttpEntity<OverbookingConstraintAlertRevenueReport> entity = new HttpEntity<>(report, headers);
        retryService.attempt(
                () -> centralRMSCommonAlertsRestTemplate.postForObject(getCentralRMSAlertsBaseUri() + "overbooking-constraints/v1/revenue/report", entity, Void.class),
                3,
                Duration.ofMillis(100),
                http5xxServerErrorRetryClassifier()
        );
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void reportCeilingFloorConfiguration(CeilingFloorConfigurationReport report) {
        HttpHeaders headers = getCentralRMSHeaders();
        HttpEntity<CeilingFloorConfigurationReport> entity = new HttpEntity<>(report, headers);
        retryService.attempt(
                () -> centralRMSCommonAlertsRestTemplate.postForObject(getCentralRMSAlertsBaseUri() + "ceiling-floor-constraints/v1/report/ceiling-floor-data", entity, Void.class),
                3,
                Duration.ofMillis(100),
                http5xxServerErrorRetryClassifier()
        );
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void reportCeilingFloorConfigurationV2(CeilingFloorConfigurationReportV2 report) {
        HttpHeaders headers = getCentralRMSHeaders();
        HttpEntity<CeilingFloorConfigurationReportV2> entity = new HttpEntity<>(report, headers);
        retryService.attempt(
                () -> centralRMSCommonAlertsRestTemplate.postForObject(getCentralRMSAlertsBaseUri() + "ceiling-floor-constraints/v2/report/ceiling-floor-data", entity, Void.class),
                3,
                Duration.ofMillis(100),
                http5xxServerErrorRetryClassifier()
        );
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void triggerCeilingFloorConstraintsAlertGeneration() {
        HttpHeaders headers = getCentralRMSHeaders();
        HttpEntity<CeilingFloorConfigurationReport> entity = new HttpEntity<>(headers);
        retryService.attempt(
                () -> centralRMSCommonAlertsRestTemplate.postForObject(getCentralRMSAlertsBaseUri() + "ceiling-floor-constraints/v1/generate-suggestions", entity, Void.class),
                3,
                Duration.ofMillis(100),
                http5xxServerErrorRetryClassifier()
        );
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void triggerCeilingFloorConstraintsAlertGenerationV2() {
        HttpHeaders headers = getCentralRMSHeaders();
        HttpEntity<CeilingFloorConfigurationReport> entity = new HttpEntity<>(headers);
        retryService.attempt(
                () -> centralRMSCommonAlertsRestTemplate.postForObject(getCentralRMSAlertsBaseUri() + "ceiling-floor-constraints/v2/generate-suggestions", entity, Void.class),
                3,
                Duration.ofMillis(100),
                http5xxServerErrorRetryClassifier()
        );
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void reportActiveRoomClassRoomTypeDetails(List<CentralRMSRoom> report) {
        HttpHeaders headers = getCentralRMSHeaders();
        HttpEntity<List<CentralRMSRoom> > entity = new HttpEntity<>(report, headers);
        retryService.attempt(
                () -> centralRMSCommonAlertsRestTemplate.postForObject(getCentralRMSBaseUri() + "/rooms/v1", entity, Void.class),
                3,
                Duration.ofMillis(100),
                http5xxServerErrorRetryClassifier()
        );
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void reportCeilingFloorConstraintsProductDetails(List<ProductWithMappedRooms> report) {
        HttpHeaders headers = getCentralRMSHeaders();
        HttpEntity<List<ProductWithMappedRooms> > entity = new HttpEntity<>(report, headers);
        retryService.attempt(
                () -> centralRMSCommonAlertsRestTemplate.postForObject(getCentralRMSAlertsBaseUri() + "ceiling-floor-constraints/v1/product-details", entity, Void.class),
                3,
                Duration.ofMillis(100),
                http5xxServerErrorRetryClassifier()
        );
    }

    private HttpHeaders getCentralRMSHeaders() {
        String clientCode = PacmanWorkContextHelper.getClientCode();
        String propertyCode = PacmanWorkContextHelper.getPropertyCode();

        HttpHeaders headers = new HttpHeaders();
        headers.set(CLIENT_CODE, clientCode);
        headers.set(PROPERTY_CODE, propertyCode);

        return headers;
    }

    private String getCentralRMSAlertsBaseUri() {
        return SystemConfig.getCentralRMSCommonAlertsBaseUri();
    }

    private String getCentralRMSBaseUri() {
        return SystemConfig.getCentralRMSBaseUri();
    }

    private Function<Exception, Boolean> http5xxServerErrorRetryClassifier() {
        return ex -> {
            if (!(ex instanceof HttpStatusCodeException)) {
                return false;
            } else {
                HttpStatusCodeException statusCodeException = (HttpStatusCodeException) ex;
                return statusCodeException.getStatusCode().is5xxServerError();
            }
        };
    }
}
