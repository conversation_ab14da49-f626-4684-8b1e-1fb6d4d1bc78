package com.ideas.tetris.pacman.services.investigator;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.bestavailablerate.BarDecisionService;
import com.ideas.tetris.pacman.services.bestavailablerate.CPManagementService;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BARDetails;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.CeilingFloor;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutput;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.decisionsupport.DecisionSupportService;
import com.ideas.tetris.pacman.services.decisionsupport.entity.DecisionSupport;
import com.ideas.tetris.pacman.services.pricedroprestriction.PriceDropRestrictionService;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.TransientPricingBaseAccomType;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.pacman.services.roa.attribute.entity.PropertyAttribute;
import com.ideas.tetris.pacman.services.roa.attribute.entity.PropertyAttributeEnum;
import com.ideas.tetris.pacman.services.roa.attribute.service.ROAPropertyAttributeService;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedDetails;
import com.ideas.tetris.pacman.services.webrate.dto.CompetitorRateInfo;
import com.ideas.tetris.pacman.services.webrate.service.CompetitorRateInfoService;
import com.ideas.tetris.pacman.util.BigDecimalFormatter;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static org.apache.commons.collections.CollectionUtils.isNotEmpty;

@Component
@Transactional
public class DecisionReasonTypeExplanationService {
    private static final Logger LOGGER = LoggerFactory.getLogger(DecisionReasonTypeExplanationService.class);
    private static final String LOW_RANGE = "Low Range";
    private static final String HIGH_RANGE = "High Range";
    private static final String DECISION_REASON_TYPES = "2, 3, 4, 6, 7, 10, 11, 12, 13";

    private static final String EXEC_IS_ANY_COMPETITOR_WITH_ALL_RATES_CLOSED = "EXEC dbo.usp_is_any_competitor_with_all_rates_closed :date, :accomClassId, :channelId, :los";

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService tenantCrudService;

    @Autowired
	private PacmanConfigParamsService pacmanConfigParamsService;


    @Autowired
	private InvestigatorService investigatorService;

    @Autowired
    BarDecisionService barDecisionService;

    @Autowired
    InvestigatorDemandByPriceService demandByPriceService;

    @Autowired
    CompetitorRateInfoService competitorRateInfoService;

    @Autowired
    DecisionSupportService decisionSupportService;

    @Autowired
    AccommodationService accommodationService;

    @Autowired
    CPManagementService cpManagementService;

    @Autowired
    ROAPropertyAttributeService roaPropertyAttributeService;

    @Autowired
    PriceDropRestrictionService priceDropRestrictionService;

    public static final String DECISION_REASON_BAR_EQUALS_FLOOR = "decision.reason.bar.equals.floor";
    public static final String DECISION_REASON_BAR_EQUALS_CEILING = "decision.reason.bar.equals.ceiling";
    public static final String DECISION_REASON_2 = "decision.reason.2";
    public static final String DECISION_REASON_2_BARBYLOS = "decision.reason.2.barbylos";
    private static final Set<String> DECISION_REASON_2_SET = Set.of(DECISION_REASON_2, DECISION_REASON_2_BARBYLOS);

    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }

    private static final String FETCH_DECISION_REASON_FOR_OCCUPANCY_DATE_ACCOM_TYPE_BAR_BY_LOS = new StringBuilder()
            .append("SELECT dbo.decision_reason_type_id, drt.decision_reason_type_explanation ").append("FROM decision_bar_output AS dbo    ")
            .append("       INNER JOIN decision_reason_type AS drt")
            .append("               ON dbo.decision_reason_type_id = drt.decision_reason_type_id ")
            .append("WHERE  dbo.arrival_dt = :occupancyDate ")
            .append("       AND dbo.accom_class_id = :accomClassId ")
            .append("       AND dbo.los = :los ")
            .append("       AND dbo.decision_reason_type_id in(")
            .append(DECISION_REASON_TYPES)
            .append(") ").toString();

    private static final String FETCH_DECISION_REASON_FOR_OCCUPANCY_DATE_ACCOM_TYPE = new StringBuilder()
            .append("SELECT cpbo.decision_reason_type_id, drt.decision_reason_type_explanation ").append("FROM cp_decision_bar_output AS cpbo    ")
            .append("       INNER JOIN decision_reason_type AS drt")
            .append("               ON cpbo.decision_reason_type_id = drt.decision_reason_type_id ")
            .append("WHERE  cpbo.arrival_dt = :occupancyDate ")
            .append("       AND cpbo.accom_type_id = :accomTypeId ")
            .append("       AND cpbo.decision_reason_type_id in(")
            .append(DECISION_REASON_TYPES)
            .append(") ")
            .append("    AND cpbo.Product_ID = 1; ").toString();

    public List<String> fetchDecisionReasons(DecisionReasonsParameter decisionReasonsParameter, AccomType accomType) {
        final List<Map<Integer, String>> resultList = getReasonTypeListCP(decisionReasonsParameter.occupancyDate, accomType.getId());
        List<String> decisionReasons = fetchDecisionReasonTypeExplanation(resultList, decisionReasonsParameter.occupancyDate, accomType.getId());
        if (!checkIfPriceExcludedAccomClassAndAddReason(decisionReasons, accomType)) {
            addReasonForBarEqualsCeilingFloor(decisionReasons, decisionReasonsParameter.finalBAR, decisionReasonsParameter.floor, decisionReasonsParameter.ceiling);
        }
        addReasonForCompetitorValue(decisionReasons, decisionReasonsParameter.finalBAR, decisionReasonsParameter.competitiveMarketPositionConstraint, decisionReasonsParameter.lowestCompetitorPrice, decisionReasonsParameter.highestCompetitorPrice);
        if (!decisionReasons.contains(DECISION_REASON_BAR_EQUALS_FLOOR) && checkIfLRVequalsOptimalBAR(accomType, decisionReasonsParameter.occupancyDate)) {
            addReasonForLowestAboveLRV(decisionReasons);
        }
        addDecisionReasonForClosedCompetitor(decisionReasonsParameter, accomType, decisionReasons, 1, decisionReasonsParameter.occupancyDate);
        addDecisionSupportReasons(accomType.getId(), decisionReasonsParameter.occupancyDate, decisionReasons);

        final Map<CPDecisionBAROutput, CeilingFloor> cpDecisionBAROutputCeilingFloorMap = getCPDecisionBAROutputCeilingFloorMap(accomType, JavaLocalDateUtils.fromDate(decisionReasonsParameter.occupancyDate));
        addDecisionReasonsForBarEqualsNeighboursFloorCeiling(decisionReasons, cpDecisionBAROutputCeilingFloorMap);
        filterReasonsBasedOnNeighboringPrice(decisionReasons, cpDecisionBAROutputCeilingFloorMap);

        return decisionReasons;
    }

    @VisibleForTesting
	public
    Map<CPDecisionBAROutput, CeilingFloor> getCPDecisionBAROutputCeilingFloorMap(AccomType accomType, LocalDate selectedDate) {
        final Map<CPDecisionBAROutput, CeilingFloor> cpDecisionBAROutputCeilingFloorMap = new HashMap<>();
        List<CPDecisionBAROutput> neighbouringBarsForAccomType = getNeighbouringBarsForAccomType1(selectedDate, accomType).stream().filter(cpBarOutput -> !JavaLocalDateUtils.toJavaLocalDate(cpBarOutput.getArrivalDate()).isEqual(selectedDate)).collect(Collectors.toList());
        for (CPDecisionBAROutput neighbour : neighbouringBarsForAccomType) {
            final LocalDate arrivalDate = JavaLocalDateUtils.toJavaLocalDate(neighbour.getArrivalDate());
            final CeilingFloor neighbourCeilingFloor = getCeilingFloorByAccomType(neighbour, accomType, arrivalDate);
            cpDecisionBAROutputCeilingFloorMap.put(neighbour, neighbourCeilingFloor);
        }
        return cpDecisionBAROutputCeilingFloorMap;
    }

    @VisibleForTesting
	public
    void addDecisionReasonsForBarEqualsNeighboursFloorCeiling(List<String> decisionReasons, Map<CPDecisionBAROutput, CeilingFloor> cpDecisionBAROutputCeilingFloorMap) {

        boolean isCeiling = false;
        boolean isFloor = false;

        for (Map.Entry<CPDecisionBAROutput, CeilingFloor> neighbourSet : cpDecisionBAROutputCeilingFloorMap.entrySet()) {

            final CeilingFloor neighbourCeilingFloor = neighbourSet.getValue();
            final BigDecimal neighbourFinalBar = convertToTwoDecimal(neighbourSet.getKey().getFinalBAR());

            if (isEqual(neighbourFinalBar, neighbourCeilingFloor.getCeiling())) {
                isCeiling = true;
            }

            if (isEqual(neighbourFinalBar, neighbourCeilingFloor.getFloor())) {
                isFloor = true;
            }

            if (isEqual(neighbourCeilingFloor.getCeiling(), neighbourCeilingFloor.getFloor())) {
                LOGGER.info("Investigator: Ignoring decision reason neighbouring ceiling/floor message for date {} due to same floor and ceiling", neighbourSet.getKey().getArrivalDate());
            }
        }

        if (isCeiling && !isFloor) {
            decisionReasons.add("decision.reason.increase.ceiling.neighbor");
        } else if (isFloor && !isCeiling) {
            decisionReasons.add("decision.reason.decrease.floor.neighbor");
        }
    }


    private CeilingFloor getCeilingFloorByAccomType(CPDecisionBAROutput cpDecisionBAROutput, AccomType accomType, LocalDate selectedDate) {
        return cpManagementService.getFloorAndCeilingByAccomType(cpDecisionBAROutput, accomType, JavaLocalDateUtils.toJodaLocalDate(selectedDate));
    }

    private List<CPDecisionBAROutput> getNeighbouringBarsForAccomType1(LocalDate arrivalDate, AccomType accomType) {
        return cpManagementService.findCPDecisionsBetweenDatesForAccomTypes(JavaLocalDateUtils.toJodaLocalDate(arrivalDate.minusDays(1)), JavaLocalDateUtils.toJodaLocalDate(arrivalDate.plusDays(1)), List.of(accomType));
    }

    private BigDecimal convertToTwoDecimal(BigDecimal value) {
        return null != value ? BigDecimalFormatter.format(value, Constants.TWO_DECIMAL_FORMAT_STRICT) : new BigDecimal(-1);
    }

    private void filterReasonsBasedOnNeighboringPrice(List<String> decisionReasons, Map<CPDecisionBAROutput, CeilingFloor> cpDecisionBAROutputCeilingFloorMap) {
        if (decisionReasons.contains("decision.reason.decrease.sold.out") && decisionReasons.contains("decision.reason.increase.strong.demand")) {
            boolean isNeighboringPriceHigh = isNeighboringPriceHigh(cpDecisionBAROutputCeilingFloorMap);
            if (isNeighboringPriceHigh) {
                decisionReasons.remove("decision.reason.decrease.sold.out");
            }
        }
    }

    private void addDecisionSupportReasons(Integer accomTypeId, Date selectedDate, List<String> decisionReasons) {
        DecisionSupport decisionSupport = decisionSupportService.getDecisionSupport(accomTypeId, selectedDate);

        if (decisionSupport == null) {
            return;
        }

        List<CompetitorRateInfo> competitorDataPresentForRC = competitorRateInfoService.getCompetitorsRateForDate(LocalDateUtils.fromDate(selectedDate), decisionSupport.getAccomClassId(), 1);

        addLRVDecisionSupportReason(decisionSupport, decisionReasons);

        if (decisionSupport.getCompEffect() != null && decisionSupport.getDemandEffect() != null) {
            if (decisionSupport.getCompEffect() > decisionSupport.getDemandEffect() / 5) {
                if (!competitorDataPresentForRC.isEmpty()) {
                    decisionReasons.add("decision.reason.increase.competitor.pricing");
                } else if (isroomClassDetailsAvailable(selectedDate)) {
                    decisionReasons.add("decision.reason.increase.another.rc.competitor.pricing");
                }
            } else if (decisionSupport.getCompEffect() < decisionSupport.getDemandEffect() / -5) {
                if (!competitorDataPresentForRC.isEmpty()) {
                    decisionReasons.add("decision.reason.decrease.competitor.pricing");
                } else if (isroomClassDetailsAvailable(selectedDate)) {
                    decisionReasons.add("decision.reason.decrease.another.rc.competitor.pricing");
                }
            }
        }
        if (isShowNewPriceLinkedValueMessagesForPrimaryPricedProductsEnabled()) {
            if (decisionSupport.getPriceLinked() != null) {
                if (decisionSupport.getPriceLinked() == 1) {
                    decisionReasons.add("decision.reason.decrease.higher.rc");
                } else if (decisionSupport.getPriceLinked() == 2) {
                    decisionReasons.add("decision.reason.increase.lower.rc");
                } else if (decisionSupport.getPriceLinked() == 4) {
                    decisionReasons.add("decision.reason.decrease.higher.pr");
                } else if (decisionSupport.getPriceLinked() == 5) {
                    decisionReasons.add("decision.reason.decrease.higher.price.ranking.rc");
                } else if (decisionSupport.getPriceLinked() == 8) {
                    decisionReasons.add("decision.reason.increase.lower.pr");
                } else if (decisionSupport.getPriceLinked() == 10) {
                    decisionReasons.add("decision.reason.increase.lower.price.ranking.rc");
                }
            }
        }

        if (decisionSupport.getCancelRebookPct() != null && decisionSupport.getCancelRebookPct() > 10) {
            decisionReasons.add("decision.reason.prevent.cancel");
        }

        if (decisionSupport.getLrvImbalance() != null && decisionSupport.getLrvImbalance() == 1) {
            decisionReasons.add("decision.reason.increase.elevated.lrv");
        }

        if (isPriceDropRestrictionEnabled() && decisionSupport.getBarDropAdj() != null && decisionSupport.getBarDropAdj() > 0) {
            decisionReasons.add("decision.reason.prevent.bar.drop");
        }

        if (decisionSupport.getSoldOutPct() != null && decisionSupport.getSoldOutPct() > 5) {
            decisionReasons.add("decision.reason.decrease.sold.out");
        }

        if (decisionSupport.getInsufficientCompetitors() != null && decisionSupport.getInsufficientCompetitors() == 1) {
            decisionReasons.add("decision.reason.decrease.not.enough.competitors");
        }
    }

    private boolean isNeighboringPriceHigh(Map<CPDecisionBAROutput, CeilingFloor> cpDecisionBAROutputCeilingFloorMap) {

        for (Map.Entry<CPDecisionBAROutput, CeilingFloor> cpDecisionBAROutputSet : cpDecisionBAROutputCeilingFloorMap.entrySet()) {

            CeilingFloor ceilingFloor = cpDecisionBAROutputSet.getValue();
            BigDecimal finalBar = convertToTwoDecimal(cpDecisionBAROutputSet.getKey().getFinalBAR());

            if (ceilingFloor.getCeiling() != null) {
                BigDecimal percentage = convertToTwoDecimal(BigDecimal.valueOf(0.8));
                boolean isNeighboringPriceHigh = (finalBar.compareTo(ceilingFloor.getCeiling().multiply(percentage))) >= 0;
                if (isNeighboringPriceHigh) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean isroomClassDetailsAvailable(Date selectedDate) {
        boolean roomClassDetails = false;
        List<AccomClass> allActiveAccomClasses = accommodationService.getAllActiveAccomClasses();
        if (isNotEmpty(allActiveAccomClasses)) {
            for (AccomClass accomClass : allActiveAccomClasses) {
                List<CompetitorRateInfo> competitorDataPresentForALlRC = competitorRateInfoService.getCompetitorsRateForDate(LocalDateUtils.fromDate(selectedDate), accomClass.getId(), 1);
                if (isNotEmpty(competitorDataPresentForALlRC)) {
                    roomClassDetails = true;
                    break;
                }
            }
        }
        return roomClassDetails;
    }

    private void addLRVDecisionSupportReason(DecisionSupport decisionSupport, List<String> decisionReasons) {
        boolean isProfitOptimization = pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PROFIT_OPTIMIZATION_ENABLED);
        boolean ancillaryWithoutCostsGreaterLRV = false;
        boolean ancillaryWithoutCostsComparableLRV = false;
        if (decisionSupport.getAncillaryEffect() != null && decisionSupport.getServiceCostEffect() != null && decisionSupport.getChannelCostEffect() != null && decisionSupport.getLrvEffect() != null) {
            ancillaryWithoutCostsGreaterLRV = decisionSupport.getAncillaryEffect() - decisionSupport.getServiceCostEffect() - decisionSupport.getChannelCostEffect() > decisionSupport.getLrvEffect();
            //comparable means not much less/greater
            ancillaryWithoutCostsComparableLRV = decisionSupport.getAncillaryEffect() - decisionSupport.getServiceCostEffect() - decisionSupport.getChannelCostEffect() > decisionSupport.getLrvEffect() / 10;
        }

        boolean dmdPriceAndCompEffectComparableLrv = false;
        if (decisionSupport.getLrvEffect() != null && decisionSupport.getDemandEffect() != null && decisionSupport.getCompEffect() != null) {
            dmdPriceAndCompEffectComparableLrv = decisionSupport.getLrvEffect() > (decisionSupport.getDemandEffect() + decisionSupport.getCompEffect()) / 5;
        }

        if (isProfitOptimization) {
            if (ancillaryWithoutCostsGreaterLRV) {
                decisionReasons.add("decision.reason.decrease.ancillary.exceeds.lrv");
            } else if (ancillaryWithoutCostsComparableLRV) {
                decisionReasons.add("decision.reason.decrease.ancillary.reducing.lrv");
            }

            if (!ancillaryWithoutCostsComparableLRV && dmdPriceAndCompEffectComparableLrv) {
                decisionReasons.add("decision.reason.increase.strong.demand");
            }
        } else {
            if (dmdPriceAndCompEffectComparableLrv) {
                decisionReasons.add("decision.reason.increase.strong.demand");
            }
        }
    }

    private void addDecisionReasonForClosedCompetitor(DecisionReasonsParameter decisionReasonsParameter, AccomType accomType, List<String> decisionReasons, int lengthOfStay, Date selectedDate) {
        if (null != accomType) {
            DecisionSupport decisionSupport = decisionSupportService.getDecisionSupport(accomType.getId(), selectedDate);
            if (decisionSupport == null) {
                return;
            }
            List<CompetitorRateInfo> competitorDataPresentForRC = competitorRateInfoService.getCompetitorsRateForDate(LocalDateUtils.fromDate(selectedDate), decisionSupport.getAccomClassId(), 1);
            if (!competitorDataPresentForRC.isEmpty() && decisionSupport.getCompEffect() > 0) {
                List<Integer> hasClosedAndRestrictedCompetitor = isAnyCompetitorWithAllRatesCloseAndRestricted(decisionReasonsParameter, accomType, lengthOfStay);
                if (isNotEmpty(hasClosedAndRestrictedCompetitor)) {
                    Integer hasClosedAndRestrictedCompetitorIgnored = isAnyCompetitorWithAllRatesClosedIgnored(decisionReasonsParameter, hasClosedAndRestrictedCompetitor);
                    if (hasClosedAndRestrictedCompetitorIgnored < hasClosedAndRestrictedCompetitor.size()) {
                        decisionReasons.add("at.least.one.competitor.with.all.rates.closed");
                    }
                }
            }
        }
    }

    private List<Integer> isAnyCompetitorWithAllRatesCloseAndRestricted(DecisionReasonsParameter decisionReasonsParameter, AccomType accomType, Integer lengthOfStay) {
        Integer channelIdForTheDay = competitorRateInfoService.findChannelIdForTheDay(LocalDateUtils.fromDate(decisionReasonsParameter.occupancyDate));
        QueryParameter parameters = QueryParameter.with("accomClassId", accomType.getAccomClass().getId())
                .and("date", decisionReasonsParameter.occupancyDate)
                .and("channelId", channelIdForTheDay)
                .and("los", lengthOfStay)
                .and("productId", 1);
        List<Integer> hasClosedAndRestrictedCompetitor = tenantCrudService.findByNativeQuery("SELECT DISTINCT Webrate_Competitors_ID as IDs\n" +
                        "                        FROM Vw_Webrate_full w\n" +
                        "                        WHERE Occupancy_DT = :date\n" +
                        "                          AND Webrate_Channel_ID = :channelId\n" +
                        "                          AND Webrate_Status IN ('C','F')\n" +
                        "                          AND w.Accom_Class_ID = :accomClassId\n" +
                        "                          AND los = :los\n" +
                        "                          AND Product_ID = :productId",
                parameters.parameters());
        return hasClosedAndRestrictedCompetitor;
    }

    private Integer isAnyCompetitorWithAllRatesClosedIgnored(DecisionReasonsParameter decisionReasonsParameter, List<Integer> hasClosedAndRestrictedCompetitor) {
        QueryParameter parameters = QueryParameter.with("hasClosedAndRestrictedCompetitor", hasClosedAndRestrictedCompetitor)
                .and("date", decisionReasonsParameter.occupancyDate);

        Integer checkIfClosedOrRestrictedCompetitorIsIgnored = tenantCrudService.findByNativeQuerySingleResult(
                new StringBuilder("select ")
                        .append("count(*) ")
                        .append("from ")
                        .append("Webrate_Override_Competitor WOC ")
                        .append("   inner join ")
                        .append("   Webrate_OVR_Competitor_DTLS WOCD ")
                        .append("   on ")
                        .append("   WOC.Webrate_Override_Competitor_ID = WOCD.Webrate_Override_Competitor_ID ")
                        .append("   inner join ")
                        .append("   Webrate_Competitors_Class WCC ")
                        .append("   on ")
                        .append("   WCC.Webrate_Competitors_Class_ID = WOCD.Webrate_Competitors_Class_ID ")
                        .append("   where ")
                        .append("   WCC.Webrate_Competitors_ID in (:hasClosedAndRestrictedCompetitor) ")
                        .append("   And  ")
                        .append("   :date  between woc.Competitor_Override_Start_DT and woc.Competitor_Override_End_DT ")
                        .toString(),
                parameters.parameters());

        return checkIfClosedOrRestrictedCompetitorIsIgnored;
    }


    private void addReasonForLowestAboveLRV(List<String> decisionReasons) {
        decisionReasons.add("decision.reason.lowest.above.lrv");
    }

    private boolean checkIfLRVequalsOptimalBAR(AccomType accomType, Date occupancyDate) {
        BigDecimal lastRoomValue = investigatorService.getLastRoomValue(DateUtil.convertJavaUtilDateToLocalDate(occupancyDate, true), accomType.getAccomClass().getId());
        CPDecisionBAROutput cpDecisionBAROutput = investigatorService.getBarByAccomTypeId(DateUtil.convertJavaUtilDateToLocalDate(occupancyDate, true), accomType.getId(), null);
        return lastRoomValue.setScale(2, RoundingMode.HALF_UP).equals(cpDecisionBAROutput.getOptimalBAR().setScale(2, RoundingMode.HALF_UP));
    }

    public List<BigDecimal> getRatePlan(AccomType accomType, Date date, int lengthOfStay) {
        List<BigDecimal> dataPoints = new ArrayList<>();
        BARDetails barDetails = barDecisionService.getBARDetailsByDateRange(accomType.getAccomClass().getId(), date, date, lengthOfStay);
        if (barDetails != null && barDetails.getPriceMapByRateUnqualifiedName().size() > 0) {
            List<String> ratePlanNamesByPrice = demandByPriceService.sortByPriceInAscendingOrderOfRates(barDetails);
            dataPoints = getRatePlanPoints(accomType, barDetails, ratePlanNamesByPrice);
        }
        return dataPoints;
    }

    private List<BigDecimal> getRatePlanPoints(AccomType accomType, BARDetails barDetails, List<String> ratePlanNamesByPrice) {
        List<BigDecimal> dataPoints = new ArrayList<>();
        for (String ratePlanName : ratePlanNamesByPrice) {
            Map<String, BigDecimal> priceMap = barDetails.getPriceMapByRateUnqualifiedName().get(ratePlanName);
            BigDecimal bigDecimal = priceMap.get(accomType.getName());
            if (null != bigDecimal) {
                dataPoints.add(bigDecimal.setScale(2, RoundingMode.HALF_UP));
            }
        }
        return dataPoints;
    }

    public List<String> fetchDecisionReasonsBARByLOS(DecisionReasonsParameter decisionReasonsParameter, AccomClass accomClass, int lengthOfStay, int selectedAccomTypeId) {
        AccomType accomType = tenantCrudService.find(AccomType.class, selectedAccomTypeId);
        final List<Map<Integer, String>> resultList = getReasonTypeListBARByLOS(decisionReasonsParameter.occupancyDate, accomClass.getId(), lengthOfStay);
        List<String> decisionReasons = fetchDecisionReasonTypeExplanation(resultList, decisionReasonsParameter.occupancyDate, selectedAccomTypeId);
        addReasonForBarEqualsCeilingFloor(decisionReasons, decisionReasonsParameter.finalBAR, decisionReasonsParameter.floor, decisionReasonsParameter.ceiling);
        addReasonForCompetitorValue(decisionReasons, decisionReasonsParameter.finalBAR, decisionReasonsParameter.competitiveMarketPositionConstraint, decisionReasonsParameter.lowestCompetitorPrice, decisionReasonsParameter.highestCompetitorPrice);
        if (!decisionReasons.contains(DECISION_REASON_BAR_EQUALS_FLOOR) && null != accomType &&
                checkIfBARIsLowestAboveLRV(accomType, decisionReasonsParameter.occupancyDate, decisionReasonsParameter.finalBAR.setScale(2, RoundingMode.HALF_UP), lengthOfStay)) {
            addReasonForLowestAboveLRV(decisionReasons);
        }
        addDecisionReasonForClosedCompetitor(decisionReasonsParameter, accomType, decisionReasons, lengthOfStay, decisionReasonsParameter.occupancyDate);
        addDecisionSupportReasons(selectedAccomTypeId, decisionReasonsParameter.occupancyDate, decisionReasons);
        return decisionReasons;
    }

    public List<String> fetchDecisionReasonTypeExplanation(List<Map<Integer, String>> resultList, Date occupancyDate, int accomTypeID) {
        if (isDecisionReasonCode(3, resultList)) {
            return fetchDecisionReasonForCode3(occupancyDate, accomTypeID);
        } else {
            return fetchDecisionReasonTypeExplanation(resultList);
        }
    }

    public List<String> fetchDecisionReasonForCode3(Date occupancyDate, int accomTypeID) {
        List<String> decisionReasonTypeExplanation = new ArrayList<>();
        return handleDecisionReasonCode3(decisionReasonTypeExplanation, occupancyDate, accomTypeID);
    }

    private List<String> handleDecisionReasonCode3(List<String> decisionReasonTypeExplanation, Date occupancyDate, int accomTypeID) {
        boolean isPropertyCP = pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED);
        if (isCPFloorCeilingNotConfigured(isPropertyCP, accomTypeID, occupancyDate) || isNonCPRateNotDefined(isPropertyCP, accomTypeID, occupancyDate)) {
            decisionReasonTypeExplanation.add("decision.reason.3.1");
        } else {
            decisionReasonTypeExplanation.add("decision.reason.3.2");
        }
        return decisionReasonTypeExplanation;
    }

    private boolean isNonCPRateNotDefined(boolean isPropertyCP, int accomTypeID, Date occupancyDate) {
        return !isPropertyCP && !isRateConfiguredForOccupancyDate(occupancyDate, accomTypeID);
    }

    private boolean isCPFloorCeilingNotConfigured(boolean isPropertyCP, int accomTypeID, Date occupancyDate) {
        return isPropertyCP && !isPriceConfiguredForOccupancyDate(occupancyDate, accomTypeID);
    }

    private boolean isRateConfiguredForOccupancyDate(Date occupancyDate, int accomTypeID) {
        return 0 != (int) tenantCrudService.findByNativeQuerySingleResult(RateUnqualifiedDetails.COUNT_BY_OCCUPANCY_DATE_ACCOM_TYPE,
                QueryParameter.with("occupancyDate", occupancyDate).and("accomTypeID", accomTypeID).parameters());
    }

    private boolean isPriceConfiguredForOccupancyDate(Date occupancyDate, int accomTypeID) {
        return 0 != (int) tenantCrudService.findByNativeQuerySingleResult(TransientPricingBaseAccomType.COUNT_BY_ALL_DATE_ACCOM_TYPE, QueryParameter.with("occupancyDate", occupancyDate).and("accomTypeID", accomTypeID).parameters());
    }

    protected boolean checkIfBARIsLowestAboveLRV(AccomType accomType, Date occupancyDate, BigDecimal finalBAR, int lengthOfStay) {
        List<BigDecimal> ratePlanPoints = getRatePlan(accomType, occupancyDate, lengthOfStay);
        ratePlanPoints.remove(finalBAR);
        BigDecimal lastRoomValue = investigatorService.getLastRoomValueByLos(DateUtil.convertJavaUtilDateToLocalDate(occupancyDate, true),
                accomType.getAccomClass().getId(), lengthOfStay).setScale(2, RoundingMode.HALF_UP);
        if (lastRoomValue.compareTo(finalBAR) < 0) {
            List<BigDecimal> filteredRatePlanPointsList = ratePlanPoints.stream().filter(bigDecimal ->
                            (bigDecimal.compareTo(lastRoomValue) > 0 || bigDecimal.compareTo(lastRoomValue) == 0)
                                    && bigDecimal.compareTo(finalBAR) < 0)
                    .collect(Collectors.toList());
            return filteredRatePlanPointsList.isEmpty();
        } else if (lastRoomValue.compareTo(finalBAR) == 0) {
            return true;
        }
        return false;
    }

    public void addReasonForBarEqualsCeilingFloor(List<String> decisionReasons, BigDecimal finalBAR, BigDecimal floor, BigDecimal ceiling) {
        if (addIfFinalBarMatchesPricingValue(decisionReasons, finalBAR, ceiling, DECISION_REASON_BAR_EQUALS_CEILING)) {
            decisionReasons.removeAll(DECISION_REASON_2_SET);
        } else {
            addIfFinalBarMatchesPricingValue(decisionReasons, finalBAR, floor, DECISION_REASON_BAR_EQUALS_FLOOR);
        }
    }

    private boolean addIfFinalBarMatchesPricingValue(List<String> decisionReasons, BigDecimal finalBAR, BigDecimal pricingValue, String messageKey) {
        if (isEqual(finalBAR, pricingValue)) {
            decisionReasons.add(messageKey);
            return true;
        }
        return false;
    }

    private void addReasonForCompetitorValue(List<String> decisionReasons, BigDecimal finalBAR, String competitiveMarketPositionConstraint, BigDecimal lowestCompetitorPrice, BigDecimal highestCompetitorPrice) {
        if (isCompetitiveMarketPositionConstraintSetToRange(competitiveMarketPositionConstraint, LOW_RANGE) && isEqual(finalBAR, lowestCompetitorPrice)) {
            decisionReasons.add("decision.reason.lowest.competitor.value");
        } else if (isCompetitiveMarketPositionConstraintSetToRange(competitiveMarketPositionConstraint, HIGH_RANGE) && isEqual(finalBAR, highestCompetitorPrice)) {
            decisionReasons.add("decision.reason.highest.competitor.value");
        }
    }

    private boolean isCompetitiveMarketPositionConstraintSetToRange(String competitiveMarketPositionConstraint, String range) {
        return StringUtils.equalsIgnoreCase(range, competitiveMarketPositionConstraint);
    }

    private boolean isEqual(BigDecimal value1, BigDecimal value2) {
        if (null == value2 || null == value1) {
            return false;
        }
        return 0 == value1.compareTo(value2);
    }

    private boolean checkIsPropertyBARByLOS() {
        String value = pacmanConfigParamsService.getValue(getContext(), IPConfigParamName.BAR_BAR_DECISION.value());
        return StringUtils.equalsIgnoreCase(value, "BARByLOS");
    }

    private String getContext() {
        return String.format("%s.%s.%s", Constants.CONFIG_PARAMS_NODE_PREFIX, PacmanWorkContextHelper.getClientCode(),
                PacmanWorkContextHelper.getPropertyCode());
    }

    private boolean checkIfPriceExcludedAccomClassAndAddReason(List<String> decisionReasons, AccomType accomType) {
        if (isPriceExcluded(accomType)) {
            decisionReasons.add("decision.reason.price.excluded");
            return true;
        }
        return false;
    }

    private boolean isPriceExcluded(AccomType accomType) {
        PricingAccomClass pricingAccomClass = tenantCrudService
                .findByNamedQuerySingleResult(PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_CLASS,
                        QueryParameter.with(Constants.PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                                .and(Constants.ACCOM_CLASS_ID, accomType.getAccomClass().getId()).parameters());

        return (null != pricingAccomClass && pricingAccomClass.isPriceExcluded());
    }

    public List<String> fetchDecisionReasonTypeExplanation(List<Map<Integer, String>> resultList) {
        List<String> decisionReasonTypeExplanations = new ArrayList<>();
        boolean isPropertyBARByLOS = checkIsPropertyBARByLOS();
        if (handleDecisionReasonCode2(isPropertyBARByLOS, decisionReasonTypeExplanations, resultList)) {
            return decisionReasonTypeExplanations;
        }
        if (handleDecisionReasonCode6(decisionReasonTypeExplanations, resultList)) {
            return decisionReasonTypeExplanations;
        }
        decisionReasonTypeExplanations.addAll(getCollection(resultList));
        return decisionReasonTypeExplanations;
    }

    protected List<Map<Integer, String>> getReasonTypeListCP(Date occupancyDate, Integer id) {
        return tenantCrudService.findByNativeQuery(FETCH_DECISION_REASON_FOR_OCCUPANCY_DATE_ACCOM_TYPE,
                QueryParameter.with(Constants.IM_LABEL_OCCUPANCY_DATE, occupancyDate).and(Constants.ACCOM_TYPE_ID, id).parameters(), new RowMapper<Map<Integer, String>>() {
                    @Override
                    public Map<Integer, String> mapRow(Object[] row) {
                        Map<Integer, String> rowMapper = new HashMap<Integer, String>();
                        rowMapper.put((Integer) row[0], (String) row[1]);
                        return rowMapper;
                    }
                });
    }

    protected List<Map<Integer, String>> getReasonTypeListBARByLOS(Date occupancyDate, Integer accomClassId, int los) {
        return tenantCrudService.findByNativeQuery(FETCH_DECISION_REASON_FOR_OCCUPANCY_DATE_ACCOM_TYPE_BAR_BY_LOS,
                QueryParameter.with(Constants.IM_LABEL_OCCUPANCY_DATE, occupancyDate)
                        .and(Constants.ACCOM_CLASS_ID, accomClassId)
                        .and("los", los)
                        .parameters(), new RowMapper<Map<Integer, String>>() {
                    @Override
                    public Map<Integer, String> mapRow(Object[] row) {
                        Map<Integer, String> rowMapper = new HashMap<Integer, String>();
                        rowMapper.put((Integer) row[0], (String) row[1]);
                        return rowMapper;
                    }
                });
    }

    private boolean handleDecisionReasonCode6
            (List<String> decisionReasonTypeExplanations, List<Map<Integer, String>> resultList) {
        if (isDecisionReasonCode(6, resultList)) {
            boolean lraEnabled = isLraEnabled();

            if (isCancelRebookPercentConfigured()) {
                decisionReasonTypeExplanations.add(lraEnabled ? "decision.reason.6.3" :
                        isHighestBarRestrictedEnabled() ? "decision.reason.6.4" : "decision.reason.6.5");
            } else {
                decisionReasonTypeExplanations.add(lraEnabled ? "decision.reason.6.1" : "decision.reason.6.2");
            }
            return true;
        }
        return false;
    }

    private boolean isCancelRebookPercentConfigured() {
        String attributeValue = tenantCrudService.findByNamedQuerySingleResult(
                PropertyAttribute.FIND_OVERRIDDEN_VALUE_OR_VALUE_BY_ATTRIBUTE_NAME, QueryParameter.with("attributeName", PropertyAttributeEnum.CANCEL_REBOOK_PCT.getAttributeName()).parameters());
        return !StringUtils.equals("0", attributeValue);
    }

    private boolean isLraEnabled() {
        String paramValue = pacmanConfigParamsService.getValue(getContext(), FeatureTogglesConfigParamName.LRAENABLED.value());
        return StringUtils.equalsIgnoreCase(paramValue, "true");
    }

    private boolean isHighestBarRestrictedEnabled() {
        return pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED);
    }

    private boolean handleDecisionReasonCode2(boolean isPropertyBARByLOS, List<
            String> decisionReasonTypeExplanations, List<Map<Integer, String>> resultList) {
        if (isDecisionReasonCode(2, resultList)) {
            String decisionReason = isPropertyBARByLOS ? DECISION_REASON_2_BARBYLOS : DECISION_REASON_2;
            decisionReasonTypeExplanations.add(decisionReason);
            return true;
        }
        return false;
    }

    private boolean isDecisionReasonCode(int reasonCode, List<Map<Integer, String>> resultList) {
        return null != resultList && resultList.get(0).containsKey(reasonCode);
    }

    private Collection<String> getCollection(List<Map<Integer, String>> resultList) {
        return null != resultList ? resultList.get(0).values() : Collections.EMPTY_LIST;
    }

    public Boolean isShowNewPriceLinkedValueMessagesForPrimaryPricedProductsEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.SHOW_NEW_PRICE_LINKED_MESSAGES_FOR_PRIMARY_PRODUCTS_ENABLED);
    }

    private boolean isPriceDropRestrictionEnabled() {
        return (isPriceDropRestrictionFeatureEnabled() && !getAttributeValueNameMap().isEmpty()) || (isPriceDropRestrictionFeatureDOWAndSeasonsFeatureEnabled() && isDefaultPriceDropRestrictionConfigPresent());
    }

    private boolean isPriceDropRestrictionFeatureEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_PRICE_DROP_RESTRICTION_FEATURE);
    }

    private Map<String, String> getAttributeValueNameMap() {
        return roaPropertyAttributeService.getAttributeValueWithNameByAttributeNames(Arrays.asList("PRICE_DROP_MIN_DTA", "PRICE_DROP_MIN_REV_GAIN", "PRICE_DROP_MAX_VALUE"));
    }

    private boolean isPriceDropRestrictionFeatureDOWAndSeasonsFeatureEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_PRICE_DROP_RESTRICTION_FEATURE_DOW_AND_SEASONS);
    }

    private boolean isDefaultPriceDropRestrictionConfigPresent(){
        return Objects.nonNull(priceDropRestrictionService.findDefault());
    }


    public static class DecisionReasonsParameter {
        private Date occupancyDate;
        private BigDecimal highestCompetitorPrice;
        private BigDecimal lowestCompetitorPrice;
        private String competitiveMarketPositionConstraint;
        private BigDecimal finalBAR;
        private BigDecimal ceiling;
        private BigDecimal floor;

        public DecisionReasonsParameter(Date occupancyDate, BigDecimal finalBAR,
                                        BigDecimal highestCompetitorPrice, BigDecimal lowestCompetitorPrice,
                                        String competitiveMarketPositionConstraint, BigDecimal ceiling, BigDecimal floor) {
            this.occupancyDate = occupancyDate;
            this.finalBAR = finalBAR;
            this.competitiveMarketPositionConstraint = competitiveMarketPositionConstraint;
            this.highestCompetitorPrice = highestCompetitorPrice;
            this.lowestCompetitorPrice = lowestCompetitorPrice;
            this.ceiling = ceiling;
            this.floor = floor;
        }

    }

    @ForTesting
    public void setPacmanConfigParamsService(PacmanConfigParamsService pacmanConfigParamsService) {
        this.pacmanConfigParamsService = pacmanConfigParamsService;
    }

    @ForTesting
    public void setBARDecisionService(BarDecisionService barDecisionService) {
        this.barDecisionService = barDecisionService;
    }

    @ForTesting
    public void setInvestigatorService(InvestigatorService investigatorService) {
        this.investigatorService = investigatorService;
    }

    @ForTesting
    public void setInvestigatorDemandByPriceService(InvestigatorDemandByPriceService demandByPriceService) {
        this.demandByPriceService = demandByPriceService;
    }

    @ForTesting
    public void setAccommodationService(AccommodationService accommodationService) {
        this.accommodationService = accommodationService;
    }

}
