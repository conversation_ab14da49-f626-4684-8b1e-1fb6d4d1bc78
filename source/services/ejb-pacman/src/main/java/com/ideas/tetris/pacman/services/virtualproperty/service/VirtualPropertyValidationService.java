package com.ideas.tetris.pacman.services.virtualproperty.service;

import com.ideas.tetris.pacman.common.utils.pojo.Pair;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.virtualproperty.dto.VirtualPropertyInboundMSRTLevelDto;
import com.ideas.tetris.pacman.services.virtualproperty.dto.VirtualPropertyInboundRTLevelDto;
import com.ideas.tetris.pacman.services.virtualproperty.dto.VirtualPropertyValidationMSRTLevelExcelDto;
import com.ideas.tetris.pacman.services.virtualproperty.dto.VirtualPropertyValidationRTLevelExcelDto;
import com.ideas.tetris.pacman.services.virtualproperty.repository.VirtualPropertyMetricRepository;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.daoandentities.entity.VirtualPropertyMapping;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.UNDERSCORE;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class VirtualPropertyValidationService {
    private static final Logger LOGGER = Logger.getLogger(VirtualPropertyValidationService.class);
    private static final String DATE_FORMAT = "yyyy-MM-dd";

    @Autowired
	private VirtualPropertyMetricRepository repository;

    @Autowired
	private VirtualPropertyMappingService virtualPropertyMappingService;

    @Autowired
	private PropertyService propertyService;

    public List<VirtualPropertyValidationRTLevelExcelDto> excelExportInboundRoomTypeData(int virtualPropertyId, Date startDate, Date endDate) {

        List<VirtualPropertyMapping> physicalPropertyMappings = virtualPropertyMappingService.getMappingsForVirtualProperty(virtualPropertyId);
        List<Pair<Integer, String>> physicalPropertyIds = getPhysicalPropertyIds(physicalPropertyMappings);
        Map<String, List<VirtualPropertyInboundRTLevelDto>> physicalPropertyData = new HashMap<>();
        List<VirtualPropertyInboundRTLevelDto> virtualPropertyData = repository.getPropertyInboundRTLevelDtos(virtualPropertyId, startDate, endDate);
        physicalPropertyIds.forEach(property -> physicalPropertyData.put(property.getSecond(), repository.getPropertyInboundRTLevelDtos(property.getFirst(), startDate, endDate)));

        String virtualPropertyCode = propertyService.getPropertyById(virtualPropertyId).getCode();
        return getExcelDtosForRoomTypeLevel(virtualPropertyCode, physicalPropertyData, virtualPropertyData);
    }

    public List<VirtualPropertyValidationMSRTLevelExcelDto> excelExportInboundMSRTData(int virtualPropertyId, Date startDate, Date endDate) {

        List<VirtualPropertyMapping> physicalPropertyMappings = virtualPropertyMappingService.getMappingsForVirtualProperty(virtualPropertyId);
        List<Pair<Integer, String>> physicalPropertyIds = getPhysicalPropertyIds(physicalPropertyMappings);
        Map<String, List<VirtualPropertyInboundMSRTLevelDto>> physicalPropertyData = new HashMap<>();
        List<VirtualPropertyInboundMSRTLevelDto> virtualPropertyData = repository.getPropertyInboundMSRTLevelDtos(virtualPropertyId, startDate, endDate);

        physicalPropertyIds.forEach(property -> physicalPropertyData.put(property.getSecond(), repository.getPropertyInboundMSRTLevelDtos(property.getFirst(), startDate, endDate)));

        String virtualPropertyCode = propertyService.getPropertyById(virtualPropertyId).getCode();
        return getExcelDtosForMSRTLevel(virtualPropertyCode, physicalPropertyData, virtualPropertyData);
    }

    public List<VirtualPropertyValidationRTLevelExcelDto> getExcelDtosForRoomTypeLevel(String virtualPropertyCode,
                                                                                Map<String, List<VirtualPropertyInboundRTLevelDto>> physicalPropertyData,
                                                                                List<VirtualPropertyInboundRTLevelDto> virtualPropertyData) {

        Map<InboundKeyPair, List<VirtualPropertyInboundRTLevelDto>> vpInboundMap = new HashMap<>();
        Map<String, Map<InboundKeyPair, VirtualPropertyInboundRTLevelDto>> ppInboundMap = new HashMap<>();

        populateRTLevelVpInboundMap(virtualPropertyData, vpInboundMap);
        populateRTLevelPpInboundMap(physicalPropertyData, ppInboundMap);

        return createExcelDtoRTLevelList(virtualPropertyCode, vpInboundMap, ppInboundMap);
    }

    public List<VirtualPropertyValidationMSRTLevelExcelDto> getExcelDtosForMSRTLevel(String virtualPropertyCode,
                                                                              Map<String, List<VirtualPropertyInboundMSRTLevelDto>> physicalPropertyData,
                                                                              List<VirtualPropertyInboundMSRTLevelDto> virtualPropertyData) {

        Map<InboundKeyTriple, List<VirtualPropertyInboundMSRTLevelDto>> vpInboundMap = new HashMap<>();
        Map<String, Map<InboundKeyTriple, VirtualPropertyInboundMSRTLevelDto>> ppInboundMap = new HashMap<>();

        populateMSRTLevelVpInboundMap(virtualPropertyData, vpInboundMap);
        populateMSRTLevelPpInboundMap(physicalPropertyData, ppInboundMap);

        return createExcelDtoMSRTLevelList(virtualPropertyCode, vpInboundMap, ppInboundMap);
    }

    public List<VirtualPropertyValidationMSRTLevelExcelDto> createExcelDtoMSRTLevelList(String virtualPropertyCode,
                                                                                 Map<InboundKeyTriple, List<VirtualPropertyInboundMSRTLevelDto>> vpInboundMap,
                                                                                 Map<String, Map<InboundKeyTriple, VirtualPropertyInboundMSRTLevelDto>> ppInboundMap) {
        Set<InboundKeyTriple> usedKeys = new HashSet<>();
        List<VirtualPropertyValidationMSRTLevelExcelDto> excelExportDtoList = new ArrayList<>();

        HashSet<InboundKeyTriple> keyTripleHashSet = new HashSet<>(vpInboundMap.keySet());
        ppInboundMap.entrySet().forEach(entry -> {
            Map<InboundKeyTriple, VirtualPropertyInboundMSRTLevelDto> map = entry.getValue();
            keyTripleHashSet.addAll(map.keySet().stream().map(k -> {
                String accomType = k.getAccomType();
                return new InboundKeyTriple(k.getOccupancyDate(), accomType, k.marketSegment);
            }).collect(Collectors.toList()));
        });

        keyTripleHashSet.forEach(key -> {
            List<VirtualPropertyInboundMSRTLevelDto> value = vpInboundMap.get(key);
            VirtualPropertyValidationMSRTLevelExcelDto excelDto = new VirtualPropertyValidationMSRTLevelExcelDto();
            if (usedKeys.contains(key)) {
                return;
            }
            setMSRTLevelExcelDto(virtualPropertyCode, ppInboundMap, key, value, excelDto);
            excelExportDtoList.add(excelDto);
            usedKeys.add(key);
        });

        return excelExportDtoList;
    }

    public void setMSRTLevelExcelDto(String virtualPropertyCode, Map<String, Map<InboundKeyTriple, VirtualPropertyInboundMSRTLevelDto>> ppInboundMap, InboundKeyTriple key, List<VirtualPropertyInboundMSRTLevelDto> value, VirtualPropertyValidationMSRTLevelExcelDto excelDto) {
        excelDto.setOccupancyDate(key.getOccupancyDate());
        excelDto.setAccomType(key.getAccomType());
        excelDto.setMarketSegment(key.getMarketSegment());

        Map<String, BigDecimal> arrivalsMap = new HashMap<>();
        Map<String, BigDecimal> departuresMap = new HashMap<>();
        Map<String, BigDecimal> noShowsMap = new HashMap<>();
        Map<String, BigDecimal> cancellationsMap = new HashMap<>();
        Map<String, BigDecimal> revenueMap = new HashMap<>();
        Map<String, BigDecimal> onBooksRevenueMap = new HashMap<>();

        final BigDecimal[] sumArrivals = {new BigDecimal(0)};
        final BigDecimal[] sumDepartures = {new BigDecimal(0)};
        final BigDecimal[] sumNoShows = {new BigDecimal(0)};
        final BigDecimal[] sumCancellations = {new BigDecimal(0)};
        final BigDecimal[] sumRevenue = {new BigDecimal(0)};
        final BigDecimal[] sumOnBooksRevenue = {new BigDecimal(0)};

        if (null != value) {
            for (VirtualPropertyInboundMSRTLevelDto vpDto : value) {
                sumArrivals[0] = add(sumArrivals[0], vpDto.getArrivals());
                sumDepartures[0] = add(sumDepartures[0], vpDto.getDepartures());
                sumNoShows[0] = add(sumNoShows[0], vpDto.getNoShow());
                sumCancellations[0] = add(sumCancellations[0], vpDto.getCancellations());
                sumRevenue[0] = add(sumRevenue[0], vpDto.getRevenue());
                sumOnBooksRevenue[0] = add(sumOnBooksRevenue[0], vpDto.getOnBooksRevenue());
            }
        } else {
            LOGGER.debug("Could not found VP Inbound MSRT record for key :[AccomType =" + key.getAccomType()
                    + ", MktSeg=" + key.getMarketSegment() + ", OccupancyDT=" + DateUtil.formatDate(key.getOccupancyDate(), DATE_FORMAT) + "]");
        }
        arrivalsMap.put(virtualPropertyCode, sumArrivals[0]);
        departuresMap.put(virtualPropertyCode, sumDepartures[0]);
        noShowsMap.put(virtualPropertyCode, sumNoShows[0]);
        cancellationsMap.put(virtualPropertyCode, sumCancellations[0]);
        revenueMap.put(virtualPropertyCode, sumRevenue[0]);
        onBooksRevenueMap.put(virtualPropertyCode, sumOnBooksRevenue[0]);

        ppInboundMap.forEach((propertyCode, tempMap) -> {
            VirtualPropertyInboundMSRTLevelDto ppDto = tempMap.get(key);
            if (Objects.isNull(ppDto)) {
                arrivalsMap.put(propertyCode, null);
                departuresMap.put(propertyCode, null);
                noShowsMap.put(propertyCode, null);
                cancellationsMap.put(propertyCode, null);
                revenueMap.put(propertyCode, null);
                onBooksRevenueMap.put(propertyCode, null);
                return;
            }
            arrivalsMap.put(propertyCode, ppDto.getArrivals());
            departuresMap.put(propertyCode, ppDto.getDepartures());
            noShowsMap.put(propertyCode, ppDto.getNoShow());
            cancellationsMap.put(propertyCode, ppDto.getCancellations());
            revenueMap.put(propertyCode, ppDto.getRevenue());
            onBooksRevenueMap.put(propertyCode, ppDto.getOnBooksRevenue());
        });

        excelDto.setArrivalsMap(arrivalsMap);
        excelDto.setDeparturesMap(departuresMap);
        excelDto.setNoShowsMap(noShowsMap);
        excelDto.setCancellationsMap(cancellationsMap);
        excelDto.setRevenueMap(revenueMap);
        excelDto.setOnBooksRevenueMap(onBooksRevenueMap);
    }

    public List<VirtualPropertyValidationRTLevelExcelDto> createExcelDtoRTLevelList(String virtualPropertyCode,
                                                                             Map<InboundKeyPair, List<VirtualPropertyInboundRTLevelDto>> vpInboundMap,
                                                                             Map<String, Map<InboundKeyPair, VirtualPropertyInboundRTLevelDto>> ppInboundMap) {


        Set<InboundKeyPair> usedKeys = new HashSet<>();
        List<VirtualPropertyValidationRTLevelExcelDto> excelExportDtoList = new ArrayList<>();
        HashSet<InboundKeyPair> keyPairHashSet = new HashSet<>(vpInboundMap.keySet());
        ppInboundMap.entrySet().forEach(entry -> {
            Map<InboundKeyPair, VirtualPropertyInboundRTLevelDto> map = entry.getValue();
            keyPairHashSet.addAll(map.keySet().stream().map(k -> {
                String accomType = k.getAccomType();
                return new InboundKeyPair(k.getOccupancyDate(), accomType);
            }).collect(Collectors.toList()));
        });

        keyPairHashSet.forEach(key -> {
            List<VirtualPropertyInboundRTLevelDto> value = vpInboundMap.get(key);

            VirtualPropertyValidationRTLevelExcelDto excelDto = new VirtualPropertyValidationRTLevelExcelDto();
            if (usedKeys.contains(key)) {
                return;
            }
            setRTLevelExcelDto(virtualPropertyCode, ppInboundMap, key, value, excelDto);
            excelExportDtoList.add(excelDto);
            usedKeys.add(key);
        });

        return excelExportDtoList;
    }

    private void setRTLevelExcelDto(String virtualPropertyCode, Map<String, Map<InboundKeyPair, VirtualPropertyInboundRTLevelDto>> ppInboundMap, InboundKeyPair key, List<VirtualPropertyInboundRTLevelDto> value, VirtualPropertyValidationRTLevelExcelDto excelDto) {
        excelDto.setOccupancyDate(key.getOccupancyDate());
        excelDto.setAccomType(key.getAccomType());
        Map<String, BigDecimal> arrivalsMap = new HashMap<>();
        Map<String, BigDecimal> departuresMap = new HashMap<>();
        Map<String, BigDecimal> totalCapacityMap = new HashMap<>();
        Map<String, BigDecimal> outOfOrderMap = new HashMap<>();
        Map<String, BigDecimal> remainingCapacityMap = new HashMap<>();
        Map<String, BigDecimal> onBooksRevenueMap = new HashMap<>();

        final BigDecimal[] sumArrivals = {new BigDecimal(0)};
        final BigDecimal[] sumDepartures = {new BigDecimal(0)};
        final BigDecimal[] sumOnBooksRevenue = {new BigDecimal(0)};
        final BigDecimal[] sumTotalCapacity = {new BigDecimal(0)};
        final BigDecimal[] sumRemainingCapacity = {new BigDecimal(0)};
        final BigDecimal[] sumOutOfOrder = {new BigDecimal(0)};

        if (null != value) {
            for (VirtualPropertyInboundRTLevelDto vpDto : value) {
                sumArrivals[0] = add(sumArrivals[0], vpDto.getArrivals());
                sumDepartures[0] = add(sumDepartures[0], vpDto.getDepartures());
                sumOnBooksRevenue[0] = add(sumOnBooksRevenue[0], vpDto.getOnBooksRevenue());
                sumTotalCapacity[0] = add(sumTotalCapacity[0], vpDto.getTotalCapacity());
                sumRemainingCapacity[0] = add(sumRemainingCapacity[0], vpDto.getRemainingCapacity());
                sumOutOfOrder[0] = add(sumOutOfOrder[0], vpDto.getOutOfOrder());
            }
        } else {
            LOGGER.debug("Could not found VP Inbound RT record for key :[AccomType =" + key.getAccomType()
                    + ", OccupancyDT=" + DateUtil.formatDate(key.getOccupancyDate(), DATE_FORMAT) + "]");
        }
        arrivalsMap.put(virtualPropertyCode, sumArrivals[0]);
        departuresMap.put(virtualPropertyCode, sumDepartures[0]);
        totalCapacityMap.put(virtualPropertyCode, sumTotalCapacity[0]);
        outOfOrderMap.put(virtualPropertyCode, sumOutOfOrder[0]);
        remainingCapacityMap.put(virtualPropertyCode, sumRemainingCapacity[0]);
        onBooksRevenueMap.put(virtualPropertyCode, sumOnBooksRevenue[0]);

        ppInboundMap.forEach((propertyCode, tempMap) -> {
            VirtualPropertyInboundRTLevelDto ppDto = tempMap.get(key);
            if (Objects.isNull(ppDto)) {
                arrivalsMap.put(propertyCode, null);
                departuresMap.put(propertyCode, null);
                totalCapacityMap.put(propertyCode, null);
                outOfOrderMap.put(propertyCode, null);
                remainingCapacityMap.put(propertyCode, null);
                onBooksRevenueMap.put(propertyCode, null);
                return;
            }
            arrivalsMap.put(propertyCode, ppDto.getArrivals());
            departuresMap.put(propertyCode, ppDto.getDepartures());
            totalCapacityMap.put(propertyCode, ppDto.getTotalCapacity());
            outOfOrderMap.put(propertyCode, ppDto.getOutOfOrder());
            remainingCapacityMap.put(propertyCode, ppDto.getRemainingCapacity());
            onBooksRevenueMap.put(propertyCode, ppDto.getOnBooksRevenue());
        });

        excelDto.setArrivalsMap(arrivalsMap);
        excelDto.setDeparturesMap(departuresMap);
        excelDto.setTotalCapacityMap(totalCapacityMap);
        excelDto.setOutOfOrderMap(outOfOrderMap);
        excelDto.setRemainingCapacityMap(remainingCapacityMap);
        excelDto.setOnBooksRevenueMap(onBooksRevenueMap);
    }

    private BigDecimal add(BigDecimal b1, BigDecimal b2) {
        if (b1 == null) {
            return b2;
        } else if (b2 == null) {
            return b1;
        } else {
            return b1.add(b2);
        }
    }

    private void populateRTLevelPpInboundMap(Map<String, List<VirtualPropertyInboundRTLevelDto>> physicalPropertyData, Map<String, Map<InboundKeyPair, VirtualPropertyInboundRTLevelDto>> ppInboundMap) {
        physicalPropertyData.entrySet().stream().forEach(entry -> {
            List<VirtualPropertyInboundRTLevelDto> list = entry.getValue();
            Map<InboundKeyPair, VirtualPropertyInboundRTLevelDto> tempMap = new HashMap<>();
            if (Objects.nonNull(list)) {
                list.stream().forEach(dto -> {
                    InboundKeyPair keyPair = new InboundKeyPair(dto.getOccupancyDate(), dto.getAccomType());
                    tempMap.put(keyPair, dto);
                });
            }
            ppInboundMap.put(entry.getKey(), tempMap);
        });
    }

    private void populateRTLevelVpInboundMap(List<VirtualPropertyInboundRTLevelDto> virtualPropertyData, Map<InboundKeyPair, List<VirtualPropertyInboundRTLevelDto>> vpInboundMap) {
        virtualPropertyData.stream().forEach(dto -> {
            InboundKeyPair keyPair = new InboundKeyPair(dto.getOccupancyDate(), getPPRoomType(dto.getAccomType()));
            vpInboundMap.computeIfAbsent(keyPair, k -> new ArrayList<>()).add(dto);
        });
    }

    private void populateMSRTLevelPpInboundMap(Map<String, List<VirtualPropertyInboundMSRTLevelDto>> physicalPropertyData, Map<String, Map<InboundKeyTriple, VirtualPropertyInboundMSRTLevelDto>> ppInboundMap) {
        physicalPropertyData.entrySet().stream().forEach(entry -> {
            List<VirtualPropertyInboundMSRTLevelDto> list = entry.getValue();
            Map<InboundKeyTriple, VirtualPropertyInboundMSRTLevelDto> tempMap = new HashMap<>();
            if (Objects.nonNull(list)) {
                list.stream().forEach(dto -> {
                    InboundKeyTriple keyTriple = new InboundKeyTriple(dto.getOccupancyDate(), dto.getAccomTypeCode(), dto.getMarketSegment());
                    tempMap.put(keyTriple, dto);
                });
            }
            ppInboundMap.put(entry.getKey(), tempMap);
        });
    }

    private void populateMSRTLevelVpInboundMap(List<VirtualPropertyInboundMSRTLevelDto> virtualPropertyData, Map<InboundKeyTriple, List<VirtualPropertyInboundMSRTLevelDto>> vpInboundMap) {
        virtualPropertyData.stream().forEach(dto -> {
            InboundKeyTriple keyTriple = new InboundKeyTriple(dto.getOccupancyDate(), getPPRoomType(dto.getAccomTypeCode()), dto.getMarketSegment());
            vpInboundMap.computeIfAbsent(keyTriple, k -> new ArrayList<>()).add(dto);
        });
    }


    private String getPPRoomType(String roomType) {
        if (roomType != null) {
            String[] split = roomType.split(UNDERSCORE, 2);
            if (split.length == 2) {
                return split[1];
            }
        }
        return roomType;
    }


    private List<Pair<Integer, String>> getPhysicalPropertyIds(List<VirtualPropertyMapping> physicalPropertyMappings) {
        return physicalPropertyMappings.stream().map(vpm -> {
            final String propertyCode = vpm.getPhysicalPropertyCode();
            final String clientCode = PacmanWorkContextHelper.getClientCode();
            Property property = propertyService.getPropertyByCode(clientCode, propertyCode);
            return (new Pair<>(property.getId(), property.getCode()));
        }).collect(Collectors.toList());
    }

    protected static class InboundKeyTriple {
        private Date occupancyDate;
        private String accomType;
        private String marketSegment;

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            InboundKeyTriple that = (InboundKeyTriple) o;
            return Objects.equals(occupancyDate, that.occupancyDate) && Objects.equals(accomType, that.accomType) && Objects.equals(marketSegment, that.marketSegment);
        }

        @Override
        public int hashCode() {
            return Objects.hash(occupancyDate, accomType, marketSegment);
        }

        public InboundKeyTriple(Date date, String accomType, String marketSegment) {
            this.occupancyDate = date;
            this.accomType = accomType;
            this.marketSegment = marketSegment;
        }

        public Date getOccupancyDate() {
            return occupancyDate;
        }

        public String getAccomType() {
            return accomType;
        }

        public String getMarketSegment() {
            return marketSegment;
        }
    }

    protected static class InboundKeyPair {
        private Date occupancyDate;
        private String accomType;

        public InboundKeyPair(Date date, String accomType) {
            this.occupancyDate = date;
            this.accomType = accomType;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            InboundKeyPair keyPair = (InboundKeyPair) o;
            return Objects.equals(occupancyDate, keyPair.occupancyDate) && Objects.equals(accomType, keyPair.accomType);
        }

        @Override
        public int hashCode() {
            return Objects.hash(occupancyDate, accomType);
        }

        public Date getOccupancyDate() {
            return occupancyDate;
        }

        public String getAccomType() {
            return accomType;
        }
    }

}
