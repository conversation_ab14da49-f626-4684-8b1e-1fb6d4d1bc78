package com.ideas.tetris.pacman.services.property;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.infra.tetris.security.domain.PropertyRoleMapping;
import com.ideas.infra.tetris.security.domain.Role;
import com.ideas.tetris.pacman.common.configparams.*;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.constants.QuestionnaireStatus;
import com.ideas.tetris.pacman.common.fds.G3SNSService;
import com.ideas.tetris.pacman.common.fds.dto.EventType;
import com.ideas.tetris.pacman.common.utils.pojo.Pair;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.integration.ratchet.services.RatchetService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyForecast;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.TotalActivity;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.*;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantProperty;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.database.DBMaintainService;
import com.ideas.tetris.pacman.services.datasourceswitching.DataSourceCacheService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.demandoverride.entity.LastRoomValue;
import com.ideas.tetris.pacman.services.fds.ups.UPSService;
import com.ideas.tetris.pacman.services.fds.ups.model.UPSProperty;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationMultiProperty;
import com.ideas.tetris.pacman.services.hotstart.HotStartPropertyService;
import com.ideas.tetris.pacman.services.limiteddatabuild.LDBService;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.PropertyDailyProcessing;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.dto.ActivitySummary;
import com.ideas.tetris.pacman.services.property.dto.PropertyDTO;
import com.ideas.tetris.pacman.services.property.dto.RoomTypeChange;
import com.ideas.tetris.pacman.services.property.dto.RoomTypeChangeRowMapper;
import com.ideas.tetris.pacman.services.propertymigration.dto.MovePropertyDTO;
import com.ideas.tetris.pacman.services.propertymigration.dto.UserRoleMappingDTO;
import com.ideas.tetris.pacman.services.rateshopper.RateShopperServiceQueryConstants;
import com.ideas.tetris.pacman.services.rdl.RDLInformation;
import com.ideas.tetris.pacman.services.remoteAgent.RemoteAgentConfigService;
import com.ideas.tetris.pacman.services.remoteAgent.RemoteTaskService;
import com.ideas.tetris.pacman.services.remoteAgent.entity.AgentProperty;
import com.ideas.tetris.pacman.services.remoteAgent.entity.RemoteAgent;
import com.ideas.tetris.pacman.services.reports.operations.OperationsReportService;
import com.ideas.tetris.pacman.services.reports.operations.dto.OperationsReportDTO;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.pacman.services.roa.attribute.entity.PropertyAttribute;
import com.ideas.tetris.pacman.services.roa.attribute.entity.PropertyAttributeOverride;
import com.ideas.tetris.pacman.services.roa.attribute.service.ROAPropertyAttributeService;
import com.ideas.tetris.pacman.services.runtask.BackendRunTaskDto;
import com.ideas.tetris.pacman.services.salesforce.SalesForceProxy;
import com.ideas.tetris.pacman.services.scheduledreport.entity.Language;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ScheduledReport;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;
import com.ideas.tetris.pacman.services.security.*;
import com.ideas.tetris.pacman.services.systemconfig.PacmanSystemConfigService;
import com.ideas.tetris.pacman.util.Pagination;
import com.ideas.tetris.platform.common.cache.ConfigParameterNameValueCache;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.externalsystem.ExternalSubSystem;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystemHelper;
import com.ideas.tetris.platform.common.externalsystem.ReservationSystem;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.client.ClientCodePropertyCodeMappingService;
import com.ideas.tetris.platform.services.client.ClientProperty;
import com.ideas.tetris.platform.services.clientservices.dto.pacman.PacmanProperty;
import com.ideas.tetris.platform.services.clientservices.dto.pacman.PacmanSubscriberDto;
import com.ideas.tetris.platform.services.clientservices.dto.pacman.PacmanSubscriberSearchCriteria;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.*;
import com.ideas.tetris.platform.services.globalproperty.service.ClientPropertyCacheService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED;
import static com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName.HILTON_STREAMING_ROLLOUT_STATUS;
import static com.ideas.tetris.pacman.common.constants.Constants.*;
import static com.ideas.tetris.pacman.common.constants.NotificationKeyConstants.*;
import static com.ideas.tetris.platform.common.externalsystem.ExternalSystem.TARS;
import static com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig.shouldUseFunctionForClientPropertyView;
import static com.ideas.tetris.platform.services.daoandentities.entity.PropertyAudit.DELETED_ACTION;
import static com.ideas.tetris.platform.services.daoandentities.entity.PropertyAudit.FIND_PROPERTY_ID_FOR_DELETED_PROPERTY_WITH_CODE;
import static java.util.Objects.nonNull;

@Transactional(propagation = Propagation.REQUIRES_NEW)
@Component
public class PropertyService {
    public static final int DECISION_GENERATION_DURATION = 105;
    public static final String STANDARD_BUILD_TYPE_KEY = "standardBuild";
    public static final String LDB_BUILD_TYPE_KEY = "limitedDataBuild";
    public static final String HOT_START_BUILD_TYPE_KEY = "hotStart";
    public static final String HYBRID_LDB_BUILD_TYPE_KEY = "coreSettings.limitedDataBuildByRoomType";
    public static final String CLIENT_CONTEXT = "pacman.%s";
    public static final String STAYED_DATA = "stayed.room.type.data";
    public static final String BOOKED_DATA = "booked.room.type.data";
    private static final String PROPERTY_NOT_FOUND_MSG = "No such property with id %s exist in system or not same as set in context";

    private static final Logger LOGGER = Logger.getLogger(PropertyService.class.getName());
    private static final String CLIENT_CODE = "clientCode";
    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
    PacmanSystemConfigService pacmanSystemConfigService;
    @Autowired
    SalesForceProxy salesForceProxy;
    @Autowired
    RatchetService ratchetService;
    @Autowired
    ClientPropertyCacheService clientPropertyCacheService;
    @Autowired
    UserAuthorizedPropertyCache userAuthorizedPropertyCache;
    @Autowired
    DateService dateService;
    @Autowired
    AbstractMultiPropertyCrudService multiPropertyCrudService;
    @Autowired
    RoleService roleService;
    @Autowired
    RemoteAgentConfigService remoteAgentConfigService;
    @Autowired
    RemoteTaskService remoteTaskService;
    @Autowired
    UserService userService;
    @Autowired
    ExternalSystemHelper externalSystemHelper;
    @Autowired
    OperationsReportService operationsReportService;
    @Autowired
    DBMaintainService dbService;
    @GlobalCrudServiceBean.Qualifier
	@Qualifier("globalCrudServiceBean")
    @Autowired
	private CrudService globalCrudService;
    @TenantCrudServiceBean.Qualifier
    @Autowired
	private CrudService tenantCrudService;
    @Autowired
	private ROAPropertyAttributeService propertyAttributeService;
    @Autowired
	private PropertyJobService propertyJobService;
    @Autowired
    @Qualifier("ldbService")
	private LDBService ldbService;
    @Autowired
	private ConfigParameterNameValueCache configParameterNameValueCache;
    @Autowired
	private DataSourceCacheService dataSourceCacheService;
    @Autowired
	private ClientCodePropertyCodeMappingService clientCodePropertyCodeMappingService;
    @Autowired
	private UPSService upsService;
    @Autowired
    G3SNSService g3SNSService;
    @Autowired
    HotStartPropertyService hotStartPropertyService;

    public boolean isPropertyVirtual() {
        WorkContextType workContext = PacmanWorkContextHelper.getWorkContext();
        if (workContext != null && workContext.getPropertyId() != null) {
            Property property = getPropertyById(workContext.getPropertyId());
            return nonNull(property) && property.isVirtualProperty();
        } else {
            return false;
        }
    }

    public boolean isPropertyVirtual(Integer propertyId) {
        Property property = getPropertyById(propertyId);
        return nonNull(property) && property.isVirtualProperty();
    }

    public List<Property> getTarsSupportedTwoWayProperties(String clientCode) {
        List<Property> twoWayProperties = globalCrudService.findByNamedQuery(Property.BY_CLIENT_CODE_ACTIVE_STAGE,
                QueryParameter.with(CLIENT_CODE, clientCode).and("stage", Stage.TWO_WAY).parameters());
        List<Property> properties = new ArrayList<>();
        twoWayProperties.forEach(property -> {
            if (tarsConfigurationParametersAreSetFor(clientCode, property.getCode())) {
                properties.add(property);
            }
        });
        return properties;
    }

    private boolean tarsConfigurationParametersAreSetFor(String clientCode, String propertyCode) {
        return isValueSetToFullOrDifferentialFor(IntegrationConfigParamName.MIN_MAX_LOS_BY_RATE_CODE_UPLOADTYPE, clientCode, propertyCode)
                || isValueSetToFullOrDifferentialFor(IntegrationConfigParamName.DAILY_BARBY_RATE_CODE_UPLOADTYPE, clientCode, propertyCode);
    }

    private boolean isValueSetToFullOrDifferentialFor(ConfigParamName parameterName, String clientCode, String propertyCode) {
        String parameterValue = pacmanConfigParamsService.getParameterValue(clientCode, propertyCode, parameterName, TARS);
        return (null != parameterValue) && (FULL.equalsIgnoreCase(parameterValue) || DIFFERENTIAL.equalsIgnoreCase(parameterValue));
    }

    public void setGlobalCrudService(CrudService globalCrudService) {
        this.globalCrudService = globalCrudService;
    }

    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }

    public void setClientPropertyCacheService(ClientPropertyCacheService clientPropertyCacheService) {
        this.clientPropertyCacheService = clientPropertyCacheService;
    }

    public void setMultiPropertyCrudService(AbstractMultiPropertyCrudService multiPropertyCrudService) {
        this.multiPropertyCrudService = multiPropertyCrudService;
    }

    public void deleteProperty(Integer propId) {
        Property property = globalCrudService.find(Property.class, propId);
        if (property != null) {
            clientPropertyCacheService.removePropertyFromCache(property.getClient(), propId);
            globalCrudService.delete(property);
        }
    }

    public void deletePropertyReferences(Integer propId) {
        globalCrudService.executeUpdateByNativeQuery("delete from Property_Connectivity_Details where Property_Id = " + propId);
    }

    @ForTesting
    public void createDatasourceFile(int propertyId) {
        Property property = getPropertyById(propertyId);
        if (property != null) {
            String clientCode = property.getClient().getCode();
            String propertyCode = property.getCode();
            String jndiName = String.format("%s-%s", clientCode, propertyCode);
            DBLoc dbLoc = dataSourceCacheService.getDBLoc(property.getId());
            dbService.createCmtDatasourceFile(getDatabaseName(propertyId), dbLoc, jndiName);
        }
    }

    @ForTesting
    public void saveProperty(Property toSave) {
        Property property = globalCrudService.find(Property.class, toSave.getId());
        toSave.setClient(property.getClient());
        toSave.setDbLocId(property.getDbLocId());
        toSave.setStatus(property.getStatus());
        toSave.setCreateDate(property.getCreateDate());
        toSave.setLastPurgedDate(property.getLastPurgedDate());
        toSave.setLastSasPurgedDate(property.getLastSasPurgedDate());
        toSave.setEstimatedCapacity(property.getEstimatedCapacity());
        LOGGER.info("#ExtraLogsForDBLocID : PropertyService - Logging DBLocId : " + property.getDbLocId() + " from global property for property: " + property.getId());
        globalCrudService.save(toSave);
    }

    public Property persistProperty(Property property) {
        property.setStatus(Status.ACTIVE);

        // load the client into the entity manager
        Client mappedClient = globalCrudService.find(Client.class, property.getClient().getId());
        property.setClient(mappedClient);

        return globalCrudService.save(property);
    }

    @SuppressWarnings("RV_RETURN_VALUE_IGNORED_NO_SIDE_EFFECT")
    public Property updateProperty(Property property) {
        Property updatedProperty = globalCrudService.save(property);

        if (updatedProperty != null) {
            globalCrudService.flush();
            // hydrate entity so that consumers can call getClient()
            updatedProperty.getClient().getCode();
            clientPropertyCacheService.reloadProperty(updatedProperty.getId());
        }

        return updatedProperty;
    }

    public void setDeploymentStatus(String deploymentStatus) {
        Property property = getPropertyById(PacmanWorkContextHelper.getPropertyId());
        property.setDeploymentStatus(deploymentStatus);
        globalCrudService.save(property);
    }

    public void setUPSId(int propertyId, RDLInformation rdlInformation) {
        Property property = getPropertyById(propertyId);
        TenantProperty tenantProperty = tenantCrudService.findByNamedQuerySingleResult(TenantProperty.GET_BY_ID,
                QueryParameter.with(PROPERTY_ID, propertyId).parameters());

        if (null == property || null == tenantProperty) {
            throw new TetrisException(ErrorCode.PROPERTY_NOT_FOUND, String.format(PROPERTY_NOT_FOUND_MSG, propertyId), null);
        }

        property.setUpsId(rdlInformation.getUnifiedPropertyId());
        globalCrudService.save(property);
        tenantProperty.setUpsId(rdlInformation.getUnifiedPropertyId());
        tenantCrudService.save(tenantProperty);
    }

    @SuppressWarnings("unchecked")
    public List<Property> getAllPropertyDetails() {
        return globalCrudService.findByNamedQuery(Property.ALL);
    }

    public List<Property> getAllTenantPropertyDetails() {
        List<Property> allPropertyList = getPropertiesForClient();
        return setDisplayLabelFieldForProperties(allPropertyList);
    }

    public boolean isPropertyConfiguredToRebuildIndexes() {
        final Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        return isPropertyConfiguredToRebuildIndexes(propertyId);
    }

    public boolean isPropertyConfiguredToRebuildIndexes(final Integer propertyId) {
        if (tenantCrudService == null) {
            LOGGER.error("TenantCrudService not initialized properly");
            return false;
        }
        TenantProperty property = tenantCrudService.findByNamedQuerySingleResult(TenantProperty.GET_BY_ID,
                QueryParameter.with("propertyId", propertyId).parameters());
        return property != null && property.isApplicationManagesIndexRebuild();
    }

    public int getPropertyId(String clientCode, String propertyCode) {
        Property foundProperty = clientPropertyCacheService.getProperty(clientCode, propertyCode);
        return foundProperty != null ? foundProperty.getId() :
                (Integer) globalCrudService.findByNamedQuery(Property.GET_ID_BY_CLIENT_CODE_PROPERTY_CODE,
                        QueryParameter.with(Property.PARAM_CLIENT_CODE, clientCode)
                                .and(Property.PARAM_PROPERTY_CODE, propertyCode).parameters()).get(0);
    }

    public Property getPropertyById(int propertyId) {
        Property foundProperty = clientPropertyCacheService.getProperty(propertyId);
        return foundProperty != null ? foundProperty :
                globalCrudService.findByNamedQuerySingleResult(Property.BY_ID_WITH_CLIENT,
                        QueryParameter.with(Property.PARAM_PROPERTY_ID, propertyId).parameters());
    }

    public int getPropertyByClientCodeAndPropertyCode(String clientcode, String propertyCode) {
        Property foundProperty = getPropertyByCode(clientcode, propertyCode);
        return foundProperty.getId();
    }

    public String getPropertyUsingBookedOrStayedData(int propertyId) {
        String bookedOrStayedInfo;
        boolean showHideBookedDataInfo = pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.DISPLAY_BOOKED_VS_STAYED_IN_IMPORTANT_INFORMATION.value());
        if (!showHideBookedDataInfo) {
            bookedOrStayedInfo = "HIDE_BOOKED_DATA_INFO";
        } else {
            String useBookedData;
            useBookedData = getBookedOrStayed();
            bookedOrStayedInfo = "1".equals(useBookedData) ? BOOKED_DATA : STAYED_DATA;
            bookedOrStayedInfo = ResourceUtil.getOptionalText(bookedOrStayedInfo, Language.ENGLISH).orElse("");
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("result", bookedOrStayedInfo);
        return jsonObject.toJSONString();
    }

    public String getBookedOrStayed() {
        String usedBookedData;
        PropertyAttribute propertyAttribute = (PropertyAttribute) tenantCrudService.findByNamedQuerySingleResult(PropertyAttribute.FIND_BY_ATTRIBUTE_NAME,
                QueryParameter.with(PropertyAttribute.PARAM_NAME, "use_book_data").parameters());
        PropertyAttributeOverride attributeOverride = (PropertyAttributeOverride) tenantCrudService.findByNamedQuerySingleResult(PropertyAttributeOverride.FIND_BY_ATTRIBUTE_ID,
                QueryParameter.with("attributeId", propertyAttribute.getId()).parameters());
        if (attributeOverride != null) {
            usedBookedData = attributeOverride.getValue();
        } else {
            usedBookedData = propertyAttribute.getValue();
        }
        return usedBookedData;
    }

    public JSONObject getRoomTypeChange(String noOfDays) {
        String queryStr = "with tempAudit as (\n" +
                "select accom_type_id,\n" +
                "REVTYPE,\n" +
                "Accom_Type_Code,\n" +
                "Accom_Type_Capacity,\n" +
                "Accom_Class_ID,\n" +
                "Roh_Type,\n" +
                "Last_Updated_DTTM,\n" +
                "Last_Updated_By_User_id,\n" +
                "isComponentRoom,\n" +
                "status_id,\n" +
                "display_status_id,\n" +
                " ROW_NUMBER() over(partition by accom_type_id order by accom_type_id, Created_DTTM) as rn\n" +
                "from Accom_Type_AUD where Last_Updated_DTTM > DATEADD(DAY,-" + noOfDays + ",GETDATE()))\n" +
                "select temp.accom_type_id as RoomTypeID,\n" +
                "temp.Accom_Type_Code as RoomTypeCode,\n" +
                "temp.Accom_Type_Capacity as OldRoomCapacity,\n" +
                "temp2.Accom_Type_Capacity as NewRoomCapacity,\n" +
                "ac.Accom_Class_Code as OldRoomClassCode,\n" +
                "ac2.Accom_Class_Code as NewRoomClassCode,\n" +
                "case when temp.status_id =1 then 'Active'\n" +
                "\twhen temp.status_id =2 then 'Deactive' end as OldStatus,\n" +
                "case when temp2.status_id =1 then 'Active'\n" +
                "\twhen temp2.status_id =2 then 'Deactive' end as NewStatus,\n" +
                "case when temp.display_status_id =1 then 'Active'\n" +
                "\twhen temp.display_status_id =2 then 'Deactive' end as OldDisplayStatus,\n" +
                "case when temp2.display_status_id =1 then 'Active'\n" +
                "\twhen temp2.display_status_id =2 then 'Deactive' end as NewDisplayStatus,\n" +
                "u.User_Name as UpdatedByUser,\n" +
                "case when temp2.REVTYPE =0 then 'Created'\n" +
                "\twhen temp2.REVTYPE= 1 then 'Update'\n" +
                "\twhen temp2.REVTYPE = 2 then 'Delete' end as ChangeType,\n" +
                "temp2.Last_Updated_DTTM as UpdatedDate\n" +
                " from tempAudit as temp inner join tempAudit as temp2 \n" +
                "\t\ton temp.Accom_Type_ID = temp2.Accom_Type_ID \n" +
                "\t\tand temp.rn = temp2.rn-1 \n" +
                "\t\tand (temp.Accom_Class_ID <> temp2.Accom_Class_ID\n" +
                "\t\tor temp.Accom_Type_Capacity <> temp2.Accom_Type_Capacity\n" +
                "\t\tor temp.status_id <> temp2.status_id \n" +
                "\t\tor temp.display_status_id <> temp2.display_status_id)\n" +
                " inner join Accom_Class as AC \n" +
                "\t\ton ac.Accom_Class_ID = temp.Accom_Class_ID\n" +
                "inner join Accom_Class as AC2 \n" +
                "\t\ton ac2.Accom_Class_ID = temp2.Accom_Class_ID\n" +
                "inner join Users as u on u.user_id = temp2.Last_Updated_By_User_id \n" +
                "order by temp.rn desc;\n" +
                "\n";
        List<RoomTypeChange> roomTypeChanges = tenantCrudService.findByNativeQuery(queryStr, null, new RoomTypeChangeRowMapper());
        JSONArray changes = new JSONArray();
        if (CollectionUtils.isNotEmpty(roomTypeChanges)) {
            changes.addAll(roomTypeChanges);
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("content", changes);
        return jsonObject;
    }

    public String getStreamingRolloutStatus(String clientCode, String propertyCode) {
        return pacmanConfigParamsService.getParameterValue(clientCode, propertyCode, HILTON_STREAMING_ROLLOUT_STATUS);
    }

    public String getPropertySystemMode(int propertyId) {

        Property property = getPropertyById(propertyId);
        String systemMode = "";
        if (property != null) {
            Stage stage = property.getStage();
            if (stage != null) {
                systemMode = ResourceUtil.getOptionalText(stage.getCode().toLowerCase(), Language.ENGLISH).orElse("");
            }
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("result", systemMode);
        return jsonObject.toJSONString();
    }

    public Integer getReservationDataVersion() {
        return getReservationDataVersion(PacmanWorkContextHelper.getPropertyId());
    }

    public Integer getReservationDataVersion(Integer propertyId) {
        Property property = getPropertyById(propertyId, false);
        return property == null ? null : property.getReservationDataVersion();
    }

    public void updateReservationDataVersion(Integer reservationDataVersion) {
        Property property = getPropertyById(PacmanWorkContextHelper.getPropertyId(), false);
        if (property != null) {
            property.setReservationDataVersion(reservationDataVersion);
            globalCrudService.save(property);
        }
    }

    public String getPropertyBuildType() {
        String buildType = getPropertyBuildTypeKey();
        String systemBuild = ResourceUtil.getOptionalText(buildType, Language.ENGLISH).orElse("");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("buildType", systemBuild);
        return jsonObject.toJSONString();
    }

    public String getPropertyBuildTypeKey() {
        if(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_HOT_START_PROPERTY)) {
            return  HOT_START_BUILD_TYPE_KEY;
        }

        String buildType = STANDARD_BUILD_TYPE_KEY;
        if (pacmanConfigParamsService.getBooleanParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED.value())) {
            if (ldbService.isHybridLdb()) {
                buildType = HYBRID_LDB_BUILD_TYPE_KEY;
            } else {
                buildType = LDB_BUILD_TYPE_KEY;
            }
        }
        return buildType;
    }

    public String getRateShoppingVendor() {
        String vendor = pacmanConfigParamsService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SOURCE.value());
        return StringUtils.isBlank(vendor) ? StringUtils.EMPTY : vendor;
    }

    public Property getPropertyById(int propertyId, boolean useCache) {
        Property foundProperty = null;

        if (useCache) {
            foundProperty = clientPropertyCacheService.getProperty(propertyId);
        }

        if (foundProperty == null) {
            foundProperty = globalCrudService.findByNamedQuerySingleResult(Property.BY_ID_WITH_CLIENT,
                    QueryParameter.with(Property.PARAM_PROPERTY_ID, propertyId).parameters());
        }
        return foundProperty;
    }

    public Stage getPropertyStage(int propertyId) {
        return getPropertyById(propertyId).getStage();
    }

    @SuppressWarnings("unchecked")
    public List<Property> getAllPropertiesFilterByPredicate(Predicate<Property> propertyFilterPredicate) {
        return globalCrudService.<Property>findByNamedQuery(Property.ALL_ACTIVE)
                .stream()
                .filter(propertyFilterPredicate)
                .collect(Collectors.toList());
    }

    public Property getPropertyByCode(String clientCode, String propertyCode) {
        return getPropertyByCode(clientCode, propertyCode, true);
    }

    @SuppressWarnings("unchecked")
    public Property getPropertyByCode(String clientCode, String propertyCode, boolean useCache) {
        Property property = null;

        if (useCache) {
            property = clientPropertyCacheService.getProperty(clientCode, propertyCode);
        }

        if (null == property) {
            List<Property> result = globalCrudService.findByNamedQuery(
                    Property.BY_CLIENT_CODE_PROPERTY_CODE,
                    QueryParameter.with(Property.PARAM_CLIENT_CODE, clientCode)
                            .and(Property.PARAM_PROPERTY_CODE, propertyCode).parameters());
            property = CollectionUtils.isNotEmpty(result) ? result.get(0) : null;
        }

        return property;
    }

    public Property getPropertyFromWorkContext() {
        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
        String clientCode = workContext.getClientCode();
        String propertyCode = workContext.getPropertyCode();
        return getPropertyByCode(clientCode, propertyCode);
    }

    public Integer getTenantPropertyIDByCode(String propertyCode) {
        return globalCrudService.findByNamedQuerySingleResult(Property.GET_ID_BY_CLIENT_CODE_PROPERTY_CODE,
                QueryParameter.with(Property.PARAM_CLIENT_CODE, PacmanWorkContextHelper.getClientCode())
                        .and(Property.PARAM_PROPERTY_CODE, propertyCode).parameters());
    }

    public List<Property> getPropertiesForClient() {
        return clientPropertyCacheService.getClientPropertiesByClientId(PacmanWorkContextHelper.getClientId());
    }

    public List<Property> getPropertiesForClient(Integer clientId) {
        return clientPropertyCacheService.getClientPropertiesByClientId(clientId);
    }

    public List<Property> findPropertyByIds(List<Integer> propertyIds) {
        if (CollectionUtils.isEmpty(propertyIds)) {
            return Collections.emptyList();
        }
        return globalCrudService.findByNamedQuery(Property.BY_IDS,
                QueryParameter.with(Property.PARAM_PROPERTY_LIST_IDS, propertyIds).parameters());
    }

    public String getPropertySubscriptionType() {
        return pacmanConfigParamsService.getParameterValue(getContext(), FeatureTogglesConfigParamName.G3_RMS_CORE_SUBSCRIPTION_TYPE);
    }

    public String getContext() {
        return pacmanConfigParamsService.propertyNode(PacmanWorkContextHelper.getWorkContext());
    }

    public List<Property> getPropertiesWithDisplayLabelFieldByIds(List<Integer> propertyIds) {
        if (CollectionUtils.isEmpty(propertyIds)) {
            return Collections.emptyList();
        }
        List<Property> properties = globalCrudService.findByNamedQuery(Property.BY_IDS,
                QueryParameter.with(Property.PARAM_PROPERTY_LIST_IDS, propertyIds).parameters());

        return setDisplayLabelFieldForProperties(properties);
    }

    private List<Property> setDisplayLabelFieldForProperties(List<Property> properties) {
        String displayCodeOrName = pacmanConfigParamsService.getValue("pacman." + PacmanWorkContextHelper.getClientCode(), GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value());

        return properties.stream()
                .peek(property -> property.setDisplayLabelField(property.evaluateDisplayLabel(displayCodeOrName)))
                .sorted(Comparator.comparing(Property::getDisplayLabelField, String.CASE_INSENSITIVE_ORDER))
                .collect(Collectors.toList());
    }

    private String getDisplayLabel(String displayCodeOrName, Property property) {
        return "Code".equalsIgnoreCase(displayCodeOrName) ? property.getCode() : property.getName();
    }

    @SuppressWarnings("unchecked")
    public List<Property> getPropertySummariesForClient() {
        return globalCrudService.findByNamedQuery(Property.BY_CLIENT_ID_ACTIVE,
                QueryParameter.with(Property.PARAM_CLIENT_ID,
                        PacmanWorkContextHelper.getClientId()).parameters());
    }

    public List<Property> getActivePropertiesForClientCode(String clientCode) {
        return globalCrudService.findByNamedQuery(Property.BY_CLIENT_CODE_ACTIVE,
                QueryParameter.with(Property.PARAM_CLIENT_CODE,
                        clientCode).parameters());
    }

    public List<Property> getNonDummyPropertiesForClientCode(String clientCode) {
        return globalCrudService.findByNamedQuery(Property.GET_NON_DUMMY_PROPERTIES_BY_CLIENT_CODE,
                QueryParameter.with(Property.PARAM_CLIENT_CODE,
                        clientCode).parameters());
    }

    @SuppressWarnings("unchecked")
    public Property getAnotherPropertyForClient(Integer propertyId) {
        List<Property> clientOtherProperties = globalCrudService.findByNamedQuery(
                Property.GET_ANOTHER_BY_CLIENT_ID,
                QueryParameter.with(Property.PARAM_CLIENT_ID, PacmanWorkContextHelper.getClientId())
                        .and(Property.PARAM_PROPERTY_ID, propertyId).parameters());
        return !clientOtherProperties.isEmpty() ? clientOtherProperties.get(0) : null;
    }

    public List<Integer> getPropertyIdsForClient() {
        return getPropertyIdsForClient(PacmanWorkContextHelper.getClientId());
    }

    @SuppressWarnings("unchecked")
    public List<Integer> getPropertyIdsForClient(Integer clientId) {
        return globalCrudService.findByNamedQuery(Property.GET_ALL_IDS_BY_CLIENT,
                QueryParameter.with(Property.PARAM_CLIENT_ID, clientId).parameters());
    }

    public List<Integer> getActivePropertyIdsForClient() {
        return getActivePropertyIdsForClient(PacmanWorkContextHelper.getClientId());
    }

    @SuppressWarnings("unchecked")
    public List<Integer> getActivePropertyIdsForClient(Integer clientId) {
        return globalCrudService.findByNamedQuery(Property.GET_ALL_ACTIVE_IDS_BY_CLIENT,
                QueryParameter.with(Property.PARAM_CLIENT_ID, clientId).parameters());
    }

    @SuppressWarnings("unchecked")
    public List<Property> getActiveProperties(String clientCode) {
        return globalCrudService.findByNamedQuery(Property.GET_ACTIVE_PROPERTY_BY_CLIENT_CODE,
                QueryParameter.with(Property.PARAM_CLIENT_CODE, clientCode).parameters());
    }

    public List<PropertyIdCode> getActivePropertyIdCodesByClient(String clientCode) {
        return transform(clientPropertyCacheService.getClientProperties(clientPropertyCacheService.getClient(clientCode)));
    }

    private List<PropertyIdCode> transform(List<Property> properties) {
        return properties
                .stream()
                .map(p -> transform(p))
                .collect(Collectors.toList());
    }

    private PropertyIdCode transform(Property property) {
        return new PropertyIdCode(property.getId(), property.getCode());
    }

    public Property updatePropertyDataInGlobalandSalesforce(Property property, Map<String, String> updates) {
        Property updatedProperty = null;
        try {
            TenantProperty tp = tenantCrudService.find(TenantProperty.class, property.getId());
            tp.setName(property.getName());
            tp.setLastUpdatedByUserId(Integer.parseInt(PacmanWorkContextHelper.getUserId()));
            tp.setLastUpdatedDate(new Date());
            tenantCrudService.save(tp);

            if (pacmanSystemConfigService.isSalesforcePropertyNameUpdateEnabled()) {
                salesForceProxy.updateAccount(property.getSfdcAccountNo(), updates);
            }

            if (externalSystemHelper.getExternalSystem(property.getClient().getCode(), property.getCode()).getIsRatchet()) {
                ratchetService.updatePropertyName(property.getClient().getCode(), property.getCode(), property.getName());
            }

            Property globalProperty = globalCrudService.find(Property.class, property.getId());
            globalProperty.setName(property.getName());
            updatedProperty = updateProperty(globalProperty);
        } catch (TetrisException exception) {
            LOGGER.info("Property name update failed for " + property.getCode(), exception);
            throw new TetrisException(ErrorCode.UPDATE_PROPERTY_NAME_FAILED,
                    "Property name can not be saved. Please try again later. If this problem continues, please contact Administrator.", exception);
        } catch (IllegalArgumentException exception) {
            LOGGER.info("Property name could not be updated in Ratchet Database as the external System Is not properly configured. Property name update failed for " + property.getCode(), exception);
        }

        return updatedProperty;
    }

    public ReservationSystem getReservationSystem(Integer propertyId) {
        Property property = getPropertyById(propertyId);
        return externalSystemHelper.getExternalSystem(property.getClient().getCode(), property.getCode());
    }

    public ExternalSubSystem getReservationSubSystem(Integer propertyId) {
        Property property = getPropertyById(propertyId);
        return externalSystemHelper.getExternalSubSystem(property.getClient().getCode(), property.getCode());
    }

    public boolean isNGI(Integer propertyId) {
        Property property = getPropertyById(propertyId);
        return externalSystemHelper.isNGI(property.getClient().getCode(), property.getCode());
    }

    public void invalidateCache(Integer propertyId) {
        clientPropertyCacheService.reloadProperty(propertyId);
    }

    public List<Client> getClients(ClientCriteria clientCriteria) {
        return globalCrudService.findByCriteria(clientCriteria);
    }

    public List<Property> getProperties(PropertyCriteria propertyCriteria) {
        if (propertyCriteria.getSortablePropertyIds().isEmpty()) {
            propertyCriteria.setSortPropertyIds(new String[]{"code"});
        }
        return globalCrudService.findByCriteria(propertyCriteria);
    }

    public int getPropertyCount(PropertyCriteria propertyCriteria) {
        return globalCrudService.findCountByCriteria(propertyCriteria);
    }

    public String getDatabaseName(Integer propertyId) {
        if (propertyId == null) {
            return null;
        }
        DBLoc dbLoc = dataSourceCacheService.getDBLoc(propertyId);
        if (null == dbLoc) {
            return null;
        }
        return dbLoc.getDbName();
    }

    public String getDatabaseServerName(Integer propertyId) {
        if (propertyId == null) {
            return null;
        }
        DBLoc dbLoc = dataSourceCacheService.getDBLoc(propertyId);
        if (null == dbLoc) {
            return null;
        }

        return dbLoc.getServerName();
    }

    public void removePropertyAssociationWithUserRole(Integer propertyId) {
        globalCrudService.executeUpdateByNamedQuery(UserIndividualPropertyRole.DELETE_BY_PROPERTY,
                QueryParameter.with(UserIndividualPropertyRole.PARAM_PROPERTY_ID, propertyId).parameters());
    }

    public List<PropertyAuditDto> getPropertyAuditInfo(PropertyAuditCriteria criteria) {
        List<PropertyAuditDto> results = new ArrayList<>();
        List<PropertyAudit> entities = globalCrudService.findByCriteria(criteria);
        for (PropertyAudit entity : entities) {
            results.add(new PropertyAuditDto(entity, globalCrudService));
        }
        return results;
    }

    public Integer findDeletedPropertyId(String clientCode, String propertyCode) {
        return globalCrudService.findByNamedQuerySingleResult(FIND_PROPERTY_ID_FOR_DELETED_PROPERTY_WITH_CODE,
                QueryParameter.with("propertyCode", propertyCode)
                        .and("clientCode", clientCode)
                        .and("revType", DELETED_ACTION).parameters());
    }

    public boolean isStageAtLeast(Stage stageToCompare) {
        return isStageAtLeast(stageToCompare, PacmanWorkContextHelper.getPropertyId());
    }

    public boolean isStageAtLeast(Stage stageToCompare, Integer propertyId) {
        Property property = getPropertyById(propertyId);
        Stage currentStage = property.getStage();
        return currentStage != null && currentStage.getOrder() >= stageToCompare.getOrder();
    }

    public boolean isStageAtLeast(Stage stageToCompare, Stage currentStage) {
        return currentStage != null && currentStage.getOrder() >= stageToCompare.getOrder();
    }

    public boolean canRollbackOrDeleteProperty(Property property) {
        Stage stage = property.getStage();
        return (stage != null && (stage.equals(Stage.DATA_CAPTURE) || stage.equals(Stage.CATCHUP) || stage.equals(Stage.POPULATION)));
    }

    public LocalDateTime getLocalDateTime(String clientCode, String propertyCode) {
        String propertyTimeZone = getPropertyTimeZone(clientCode, propertyCode);
        return propertyTimeZone == null ? null : LocalDateTime.now(DateTimeZone.forID(propertyTimeZone));
    }

    public ZonedDateTime getZonedDateTime(String clientCode, String propertyCode) {
        String propertyTimeZone = getPropertyTimeZone(clientCode, propertyCode);
        return propertyTimeZone == null ? null : ZonedDateTime.now(ZoneId.of(propertyTimeZone));
    }

    public String getPropertyTimeZone(String clientCode, String propertyCode) {
        if (clientCode != null && propertyCode != null) {
            return pacmanConfigParamsService.getValue("pacman." + clientCode + "." + propertyCode, IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value());
        }

        return null;
    }

    public String getClientTimeZone(String clientCode) {
        if (clientCode != null) {
            return pacmanConfigParamsService.getValue("pacman." + clientCode, IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value());
        }

        return null;
    }

    public LocalDateTime getLocalDateTime(Integer propertyId) {
        String propertyTimeZone = getPropertyTimeZone(propertyId);
        return propertyTimeZone == null ? null : LocalDateTime.now(DateTimeZone.forID(propertyTimeZone));
    }

    public String getPropertyTimeZone(Integer propertyId) {
        if (propertyId != null) {
            Property property = getPropertyById(propertyId);
            return getPropertyTimeZone(property.getClient().getCode(), property.getCode());
        }

        return null;
    }

    @SuppressWarnings("unchecked")
    public List<Integer> getPropertyIdsByClientCode(String clientCode) {
        return globalCrudService.findByNamedQuery(Property.GET_ID_BY_CLIENT_CODE, QueryParameter.with(CLIENT_CODE, clientCode).parameters());
    }

    @SuppressWarnings("unchecked")
    public List<Integer> getPropertyIdsByClientCodeAndPropertyCodes(String clientCode, List<String> propertyCodes) {
        return globalCrudService.findByNamedQuery(Property.GET_ID_BY_CLIENT_CODE_AND_PROPERTY_CODES, QueryParameter.with(CLIENT_CODE, clientCode).and("propertyCodes", propertyCodes).parameters());
    }

    public Boolean getPropertyReadOnlyOverrideFlag(int propertyId) {
        Boolean readOnlyOverride = Boolean.FALSE;
        Property property = getPropertyById(propertyId, false);
        if (property != null) {
            readOnlyOverride = property.getReadOnlyOverride();
            if (readOnlyOverride == null) {
                readOnlyOverride = Boolean.FALSE;
            }
        }
        return readOnlyOverride;
    }

    public Boolean setPropertyReadOnlyOverride(boolean override, int propertyId) {
        Boolean overrideStatus = Boolean.FALSE;
        Property property = getPropertyById(propertyId, false);
        if (property != null) {
            property.setReadOnlyOverride(override);
            updateProperty(property);
            overrideStatus = override;
        }
        return overrideStatus;
    }

    public void copyClientCode(Integer propertyId) {
        final String UPDATE_CLIENT_CODE_IN_PROPERTY = "UPDATE Property SET Client_Code = :clientCode where Property_Id = :propertyId";
        final Property property = getPropertyById(propertyId);
        if (null != property) {
            LOGGER.debug("Updating Property table for property id: " + propertyId + " and client code: " + property.getClient().getCode());

            multiPropertyCrudService.executeNativeUpdateOnSingleProperty(propertyId, UPDATE_CLIENT_CODE_IN_PROPERTY,
                    QueryParameter.with(CLIENT_CODE, property.getClient().getCode()).and(Constants.PROPERTY_ID, propertyId).parameters());
        }
    }

    @SuppressWarnings("unchecked")
    public List<Integer> getAllPropertyIds() {
        return globalCrudService.findByNamedQuery(Property.GET_ALL_ACTIVE_PROPERTY_IDS);
    }

    public String getDisplayNameForProperty() {
        Property property = globalCrudService.find(Property.class, PacmanWorkContextHelper.getPropertyId());
        return getDisplayNameForProperty(property);
    }

    public String getDisplayNameForProperty(Property property) {
        String parameterValue = pacmanConfigParamsService.getValue(Constants.getPropertyConfigContext(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode()), GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value());
        return property.evaluateDisplayLabel(parameterValue);
    }

    public List<Property> getClientSpecificActivePropertiesExcluding() {
        List<Object[]> rows = globalCrudService.findByNamedQuery(Property.GET_CLIENT_PROPERTIES_EXCLUDEING_GIVEN_PARAM_AND_LDB_PROPERTY,
                QueryParameter.with("propertyCode", PacmanWorkContextHelper.getPropertyCode()).and(CLIENT_CODE, PacmanWorkContextHelper.getClientCode()).parameters());
        List<Property> properties = new ArrayList<>();
        for (Object[] row : rows) {
            Property property = new Property();
            property.setId((Integer) row[0]);
            property.setName((String) row[1]);
            property.setCode((String) row[2]);
            properties.add(property);
        }
        return properties;
    }

    public List<BackendRunTaskDto> getEligibleMonitorProcessProperties() {
        int batchSize = getMonitorProcessPropertiesBatchSize();
        List<BackendRunTaskDto> backendRunTaskDtoList = new ArrayList<>();

        List<Property> propertyListFromPropertyTable = globalCrudService.findByNamedQuery(Property.GET_MONITOR_PROCESS_PROPERTIES,
                QueryParameter.with("today", DateUtil.getCurrentDateWithoutTime()).parameters());
        for (Property property : propertyListFromPropertyTable) {
            if (!isBatchFull(batchSize, backendRunTaskDtoList) && isValidProperty(property)) {
                Date propertyCaughtUpDate = dateService.getCaughtUpDate(property.getId());
                BackendRunTaskDto backendRunTaskDto = new BackendRunTaskDto(property, DateUtil.addDaysToDate(propertyCaughtUpDate, -91), DateUtil.addDaysToDate(propertyCaughtUpDate, -1));
                backendRunTaskDtoList.add(backendRunTaskDto);
            }
            if (isBatchFull(batchSize, backendRunTaskDtoList)) {
                break;
            }
        }
        return backendRunTaskDtoList;
    }

    public void updateIsReadOnly(int value, int propertyId) {
        globalCrudService.executeUpdateByNamedQuery(Property.UPDATE_IS_READ_ONLY,
                QueryParameter.with("isReadOnly", value)
                        .and("propertyID", propertyId)
                        .parameters());
    }

    public boolean isPropertySetToReadOnlyDuringIDP(int propertyId) {
        Object isReadOnly = globalCrudService.findByNamedQuerySingleResult(Property.IS_READ_ONLY,
                QueryParameter.with("propertyID", propertyId).parameters());
        if (Objects.isNull(isReadOnly)) {
            return Boolean.FALSE;
        }
        return (byte) isReadOnly > 0 ? Boolean.TRUE : Boolean.FALSE;
    }

    public boolean isValidProperty(Property property) {
        return isPropertyOneWayAndAbove(property);
    }

    public boolean isPropertyOneWayAndAbove(Property property) {
        return this.isStageAtLeast(Stage.ONE_WAY, property.getId());
    }

    private boolean isBatchFull(int batchSize, List<BackendRunTaskDto> backendRunTaskDtoList) {
        return backendRunTaskDtoList.size() >= batchSize;
    }

    public int getMonitorProcessPropertiesBatchSize() {
        String context = Constants.CONFIG_PARAMS_NODE_PREFIX;
        String monitorProcessJobClientsBatchSize = pacmanConfigParamsService.getValue(context,
                IPConfigParamName.CORE_MONITOR_PROCESS_JOB_CLIENTS_BATCH_SIZE.value());
        return Integer.valueOf(monitorProcessJobClientsBatchSize);
    }

    public Property updateNextMonitorProcessRunDate(Integer propertyId, Date nextRunDate) {
        Property property = globalCrudService.find(Property.class, propertyId);
        property.setMonitorProcessNextRunDate(nextRunDate);
        globalCrudService.save(property);
        return property;
    }

    // TECH-DEBT: This should become its own job
    public Boolean moveProperty(MovePropertyDTO movePropertyDTO) {
        String clientId = "clientId";

        movePropertyDTO.getProperty().setClient(movePropertyDTO.getTargetClient());
        movePropertyDTO.getProperty().setLastUpdatedByUserId(Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        Date lastUpdatedDate = new Date();
        movePropertyDTO.getProperty().setLastUpdatedDate(lastUpdatedDate);
        globalCrudService.save(movePropertyDTO.getProperty());

        globalCrudService.executeUpdateByNamedQuery(PropertyDailyProcessing.UPDATE_CLIENT,
                QueryParameter.with("currentClientCode", movePropertyDTO.getSourceClient().getCode())
                        .and("propertyCode", movePropertyDTO.getProperty().getCode())
                        .and("clientName", movePropertyDTO.getTargetClient().getName())
                        .and("newClientCode", movePropertyDTO.getTargetClient().getCode())
                        .parameters());

        globalCrudService.executeUpdateByNamedQuery(PropertyPropertyGroup.DELETE_BY_PROPERTY,
                QueryParameter.with(Constants.PROPERTY_ID, movePropertyDTO.getProperty().getId())
                        .parameters());

        globalCrudService.executeUpdateByNativeQuery("delete from Auth_Group_Property where Property_Id = " + movePropertyDTO.getProperty().getId());

        movePropertyDTO.getAgent().entrySet().forEach(entry -> migrateAgent(movePropertyDTO, entry));

        globalCrudService.executeUpdateByNativeQuery("delete from Grp_Evl_Multi_Property_AUD where Property_ID = " + movePropertyDTO.getProperty().getId());

        deleteGroupEvaluationMultiProperty(movePropertyDTO.getProperty().getId());

        globalCrudService.executeUpdateByNativeQuery("update Scheduled_Report_AUD SET Client_ID = " + movePropertyDTO.getTargetClient().getId()
                + " where Property_ID = " + movePropertyDTO.getProperty().getId());

        globalCrudService.executeUpdateByNamedQuery(ScheduledReport.UPDATE_CLIENT,
                QueryParameter.with(clientId, movePropertyDTO.getTargetClient().getId())
                        .and(Constants.PROPERTY_ID, movePropertyDTO.getProperty().getId())
                        .parameters());

        globalCrudService.executeUpdateByNamedQuery(ClientPropertyAttributePairing.DELETE_BY_PROPERTY_ID,
                QueryParameter.with(Constants.PROPERTY_ID, movePropertyDTO.getProperty().getId())
                        .parameters());

        if (SystemConfig.isMovePropertyInFDSEnabled()) {
            String upsPropertyUuid = movePropertyDTO.getProperty().getUpsId();
            Integer propertyId = movePropertyDTO.getProperty().getId();
            String propertyCode = movePropertyDTO.getProperty().getCode();
            String newUpsClientUuid = movePropertyDTO.getTargetClient().getUpsClientUuid();
            String clientCode = movePropertyDTO.getTargetClient().getCode();
            String oldUpsClientUuid = movePropertyDTO.getSourceClient().getUpsClientUuid();
            if (upsPropertyUuid != null && newUpsClientUuid != null) {
                movePropertyToNewClientInFDS(upsPropertyUuid, newUpsClientUuid, oldUpsClientUuid, propertyId, propertyCode, clientCode);
            } else {
                String message = "Failed to move property in FDS as either property or client aren't mapped to FDS. Property UpsId: " + upsPropertyUuid + " ; New Client UpsId: " + newUpsClientUuid;
                LOGGER.error(message);
                throw new TetrisException(ErrorCode.DATABASE_EXCEPTION, message);
            }
        } else {
            //This will be handled in FDS when processing the PROPERTY_MOVE message
            if (pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FDS_CLIENT_ATTRIBUTES_ENABLED)) {
                //Remove attribute assignments for this property
                upsService.updateCustomAttributeAssignmentsForProperties(Arrays.asList(movePropertyDTO.getProperty().getId()));
            }
        }

        globalCrudService.executeUpdateByNativeQuery("UPDATE Config_Parameter_Value SET Context = 'pacman." + movePropertyDTO.getTargetClient().getCode() + "." + movePropertyDTO.getProperty().getCode() + "' WHERE Context = 'pacman." + movePropertyDTO.getSourceClient().getCode() + "." + movePropertyDTO.getProperty().getCode() + "'");
        configParameterNameValueCache.reloadOneContext(buildContext(movePropertyDTO.getSourceClient().getCode(), movePropertyDTO.getProperty().getCode()));
        configParameterNameValueCache.reloadOneContext(buildContext(movePropertyDTO.getTargetClient().getCode(), movePropertyDTO.getProperty().getCode()));

        updateTenantDB(movePropertyDTO, lastUpdatedDate);

        addTargetClientUsersToMovedProperty(movePropertyDTO, lastUpdatedDate);

        propertyJobService.updateJobDB(movePropertyDTO);

        final List<UserRoleMappingDTO> userRoleMappings = movePropertyDTO.getUserRoleMappings();
        if (null != userRoleMappings) {
            userRoleMappings.forEach(userRoleMapping -> updateUserRoleReferences(userRoleMapping, movePropertyDTO));
            removePropertyReferencesFromUserIndividualPropertyRolesForNonMigratingUsers(movePropertyDTO.getProperty().getId(), userRoleMappings);
        }

        updateCmtDataSourceFile(movePropertyDTO);

        updateCache(movePropertyDTO);

        return Boolean.TRUE;
    }

    private void movePropertyToNewClientInFDS(String upsPropertyUuid, String newUpsClientUuid, String oldUpsClientUuid, Integer propertyId, String propertyCode, String clientCode) {
        try {
            g3SNSService.publishToSNS(EventType.PROPERTY_MOVE, prepareEventSource(upsPropertyUuid, newUpsClientUuid, oldUpsClientUuid, propertyId, propertyCode, clientCode));
        } catch (Exception e) {
            String message = "Failed to move property for upsPropertyUuid: " + upsPropertyUuid + "; newUpsClientUuid: " + newUpsClientUuid + "; oldUpsClientUuid: " + oldUpsClientUuid;
            LOGGER.error(message);
            throw new TetrisException(ErrorCode.DATABASE_EXCEPTION, message, e);
        }
    }

    private Map<String, Object> prepareEventSource(String upsPropertyUuid, String newUpsClientUuid, String oldUpsClientUuid, Integer propertyId, String propertyCode, String clientCode) {
        Map<String, Object> eventSource = new HashMap<>();
        eventSource.put(PRODUCTION_ENV, SystemConfig.getEnvironmentCluster());
        eventSource.put(PROCESS_TYPE, PROPERTY_MOVE);
        eventSource.put(SYSTEM_DATE, java.time.LocalDate.now().format(DateTimeFormatter.ofPattern(SYSTEM_DATE_FORMAT)));
        eventSource.put(UNIFIED_PROPERTY_ID_KEY, upsPropertyUuid);
        eventSource.put(UNIFIED_CLIENT_ID_KEY, newUpsClientUuid);
        eventSource.put(OLD_UNIFIED_CLIENT_ID_KEY, oldUpsClientUuid);
        eventSource.put(PROPERTY_ID, propertyId);
        eventSource.put(CLIENT_CODE, clientCode);
        eventSource.put(PROPERTY_CODE, propertyCode);
        return eventSource;
    }

    private String buildContext(String clientCode, String propertyCode) {
        return String.join(FULL_STOP, CONFIG_PARAMS_NODE_PREFIX, clientCode, propertyCode);
    }

    public void deleteGroupEvaluationMultiProperty(Integer propertyId) {
        globalCrudService.executeUpdateByNamedQuery(GroupEvaluationMultiProperty.DELETE_BY_PROPERTY_ID,
                QueryParameter.with(Constants.PROPERTY_ID, propertyId)
                        .parameters());
    }

    public void updateCmtDataSourceFile(MovePropertyDTO movePropertyDTO) {
        Integer propertyId = movePropertyDTO.getProperty().getId();
        String propertyCode = movePropertyDTO.getProperty().getCode();
        String sourceClientCode = movePropertyDTO.getSourceClient().getCode();
        String targetClientCode = movePropertyDTO.getTargetClient().getCode();
        String oldJndiName = String.format("%s-%s", sourceClientCode, propertyCode);
        String newJndiName = String.format("%s-%s", targetClientCode, propertyCode);

        updateCmaDataSourceFileWithNewJndiName(propertyId, oldJndiName, newJndiName);
    }

    public void updateCmaDataSourceFileWithNewJndiName(Integer propertyId, String oldJndiName, String newJndiName) {
        if (!SystemConfig.isCmtDatasourceCreationEnabled()) {
            LOGGER.warn("Skipping CMT-Datasource File update for " + propertyId + " as system parameter 'cmt.datasource.enabled' is false");
            return;
        }

        DBLoc dbLoc = dbService.getDbLocForPropertyId(propertyId);
        String dbName = dbLoc.getDbName();
        LOGGER.info("Deleting CMT-Datasource File at JBoss SOA Node for Property " + propertyId);
        try {
            dbService.getDataSourceWriter().deleteCmtDatasourceFile(SystemConfig.getCMTJBossDatasourceDir(), oldJndiName);
            LOGGER.info("Creating CMT-Datasource File at JBoss SOA Node for Property " + propertyId);
            dbService.createCmtDatasourceFile(dbName, dbLoc, newJndiName);
        } catch (Exception e) {
            String message = "Failed to delete CMT-Datasource and Local-TX-Datasource file for property " + propertyId;
            LOGGER.error(message);
            throw new TetrisException(ErrorCode.DATABASE_EXCEPTION, message, e);
        }
    }

    public TimeZone getPropertyTimeZone() {
        return Optional.ofNullable(PacmanWorkContextHelper.getPropertyId())
                .map(this::getPropertyById)
                .map(p -> TimeZone.getTimeZone(pacmanConfigParamsService.<String>getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE)))
                .orElse(Calendar.getInstance().getTimeZone());
    }

    private void removePropertyReferencesFromUserIndividualPropertyRolesForNonMigratingUsers(Integer propertyId, List<UserRoleMappingDTO> userRoleMappings) {
        String movedUsers = userRoleMappings
                .stream()
                .map(userRoleMapping -> userRoleMapping.getUser().getId().toString())
                .collect(Collectors.joining(","));
        if (!StringUtils.isEmpty(movedUsers)) {
            globalCrudService.executeUpdateByNativeQuery("delete from User_Individual_Property_Role where User_ID not in (" + movedUsers + ") and Property_ID = " + propertyId);
        }
    }

    private void migrateAgent(MovePropertyDTO movePropertyDTO, Map.Entry<AgentProperty, String> entry) {
        String regex = "\\d+";
        final AgentProperty sourceAgentProperty = entry.getKey();
        if (!StringUtils.isEmpty(entry.getValue()) && entry.getValue().matches(regex)) {
            removeAgent(movePropertyDTO, sourceAgentProperty);
            RemoteAgent remoteAgent = globalCrudService.find(RemoteAgent.class, Integer.parseInt(entry.getValue()));
            remoteAgentConfigService.addPropertyToAgent(remoteAgent, movePropertyDTO.getProperty());
        } else if (Constants.LEAVE_AGENT_AS_IS.equals(entry.getValue())) {
            globalCrudService.executeUpdateByNamedQuery(RemoteAgent.UPDATE_CLIENT,
                    QueryParameter.with("clientId", movePropertyDTO.getTargetClient().getId())
                            .and("remoteAgentId", sourceAgentProperty.getRemoteAgent().getRemoteAgentId())
                            .parameters());
        } else if (Constants.REMOVE_AGENT.equals(entry.getValue())) {
            removeAgent(movePropertyDTO, sourceAgentProperty);
        }
    }

    private void updateUserRoleReferences(UserRoleMappingDTO userRoleMapping, MovePropertyDTO movePropertyDTO) {
        if (!movePropertyDTO.getTargetClient().getCode().equalsIgnoreCase(userRoleMapping.getRole().getClientCode())) {
            userRoleMapping.getRole().setClientCode(movePropertyDTO.getTargetClient().getCode());
            userRoleMapping.getRole().setUniqueIdentifier(null);
            Role role = roleService.createRoleIfNotExists(userRoleMapping.getRole());
            String roleIdToUse = role.getUniqueIdentifier();
            userRoleMapping.getRole().setUniqueIdentifier(roleIdToUse);
        }
        List<PropertyRoleMapping> propertyRoleMappings = Collections.singletonList(new PropertyRoleMapping(userRoleMapping.getRole().getUniqueIdentifier(), movePropertyDTO.getProperty().getId().toString()));

        userService.moveUserUnderNewClient(userRoleMapping.getUser().getId().toString(), movePropertyDTO.getTargetClient().getCode(), propertyRoleMappings, movePropertyDTO.getProperty().getId().toString());

        globalCrudService.executeUpdateByNamedQuery(GlobalUser.UPDATE_CLIENT,
                QueryParameter.with(CLIENT_CODE, movePropertyDTO.getTargetClient().getCode())
                        .and("id", userRoleMapping.getUser().getId()).parameters());
        int recordsUpdated = globalCrudService.executeUpdateByNativeQuery("UPDATE User_Individual_Property_Role SET Role_ID = '" + userRoleMapping.getRole().getUniqueIdentifier() + "' WHERE User_ID = " + userRoleMapping.getUser().getId() + " AND Property_ID = " + movePropertyDTO.getProperty().getId());
        if (0 == recordsUpdated) {
            globalCrudService.executeUpdateByNativeQuery("INSERT INTO User_Individual_Property_Role(User_ID, Property_ID, Role_ID, Created_DTTM, Created_By_User_ID, Last_Updated_by_User_ID, Last_Updated_DTTM) VALUES(" + userRoleMapping.getUser().getId() + ", " + movePropertyDTO.getProperty().getId() + ", '" + userRoleMapping.getRole().getUniqueIdentifier() + "', GETDATE()," + Integer.parseInt(PacmanWorkContextHelper.getUserId()) + "," + Integer.parseInt(PacmanWorkContextHelper.getUserId()) + ",GETDATE())");
        }
        globalCrudService.executeUpdateByNativeQuery("delete from User_Individual_Property_Role where User_ID = " + userRoleMapping.getUser().getId() + " and Property_ID <> " + movePropertyDTO.getProperty().getId());

        globalCrudService.executeUpdateByNativeQuery("delete from User_Auth_Group_Role where User_ID = " + userRoleMapping.getUser().getId());

        updateClientAnnouncements(movePropertyDTO, userRoleMapping.getUser().getId());

        globalCrudService.executeUpdateByNativeQuery("UPDATE Client_User SET Client_ID = " + movePropertyDTO.getTargetClient().getId() + " WHERE User_Id = " + userRoleMapping.getUser().getId());
    }

    @VisibleForTesting
    protected int updateClientAnnouncements(MovePropertyDTO movePropertyDTO, Integer userId) {
        return globalCrudService.executeUpdateByNativeQuery("UPDATE ac SET ac.Client_ID = " + movePropertyDTO.getTargetClient().getId() + " FROM Announcement_Client ac INNER JOIN Announcement_User au on au.Announcement_id = ac.Announcement_id WHERE au.User_ID = " + userId +
                " AND ac.Client_ID = " + movePropertyDTO.getSourceClient().getId() + " AND NOT EXISTS (select null FROM Announcement_Client ac INNER JOIN Announcement_User au on au.Announcement_id = ac.Announcement_id WHERE au.User_ID = " + userId + " and ac.Client_ID = " + movePropertyDTO.getTargetClient().getId() + ")");
    }

    private void removeAgent(MovePropertyDTO movePropertyDTO, AgentProperty sourceAgentProperty) {
        remoteTaskService.deleteRemoteTasks(sourceAgentProperty.getRemoteAgent().getRemoteAgentId(), movePropertyDTO.getProperty().getId());
        remoteTaskService.createDeletePropertyTask(sourceAgentProperty.getRemoteAgent().getRemoteAgentId(), movePropertyDTO.getProperty().getId());
        remoteAgentConfigService.removePropertyFromAgent(sourceAgentProperty);
    }

    private void updateCache(MovePropertyDTO movePropertyDTO) {
        userAuthorizedPropertyCache.remove(movePropertyDTO.getSourceClient().getId());
        userAuthorizedPropertyCache.remove(movePropertyDTO.getTargetClient().getId());
        clientPropertyCacheService.updateProperty(movePropertyDTO.getSourceClient(), movePropertyDTO.getTargetClient(), movePropertyDTO.getProperty().getId());
    }

    private void updateTenantDB(MovePropertyDTO movePropertyDTO, Date lastUpdatedDate) {
        WorkContextType workContextType = PacmanWorkContextHelper.getWorkContext();
        WorkContextType tempWorkContextType = new WorkContextType();
        tempWorkContextType.setPropertyId(movePropertyDTO.getProperty().getId());
        tempWorkContextType.setClientCode(movePropertyDTO.getSourceClient().getCode());
        try {
            int lastUpdatedByUserId = Integer.parseInt(PacmanWorkContextHelper.getUserId());
            PacmanWorkContextHelper.setWorkContext(tempWorkContextType);
            TenantProperty tenantProperty = tenantCrudService.find(TenantProperty.class, movePropertyDTO.getProperty().getId());
            tenantProperty.setClientCode(movePropertyDTO.getTargetClient().getCode());
            tenantProperty.setLastUpdatedDate(lastUpdatedDate);
            tenantProperty.setLastUpdatedByUserId(lastUpdatedByUserId);
            tenantCrudService.save(tenantProperty);
        } finally {
            PacmanWorkContextHelper.setWorkContext(workContextType);
        }
    }

    private void addTargetClientUsersToMovedProperty(MovePropertyDTO movePropertyDTO, Date lastUpdatedDate) {
        int lastUpdatedByUserId = Integer.parseInt(PacmanWorkContextHelper.getUserId());

        List<User> tenantPropertyUsers = multiPropertyCrudService.findByNamedQueryForSinglePropertyGeneric(movePropertyDTO.getProperty().getId(), User.ALL, null);

        List<GlobalUser> targetClientGlobalUsers = globalCrudService.findByNamedQuery(GlobalUser.BY_CLIENT_CODE, QueryParameter.with(CLIENT_CODE, movePropertyDTO.getTargetClient().getCode()).parameters());
        Set<String> tenantUsersEmail = tenantPropertyUsers.stream().map(User::getEmail).collect(Collectors.toSet());
        List<GlobalUser> missingClientGlobalUsersInTenantDB = targetClientGlobalUsers.stream().
                filter(globalUser -> !tenantUsersEmail.contains(globalUser.getEmail())).collect(Collectors.toList());

        missingClientGlobalUsersInTenantDB.forEach(globalUser -> {
            User user = new User();
            user.setId(globalUser.getId());
            user.setName(globalUser.getFullName());
            user.setScreenName(globalUser.getScreenName());
            user.setEmail(globalUser.getEmail());
            user.setStatusId(globalUser.getStatusId());
            user.setLastUpdatedDate(lastUpdatedDate);
            user.setLastUpdatedByUserId(lastUpdatedByUserId);
            multiPropertyCrudService.save(movePropertyDTO.getProperty().getId(), user);
        });
    }

    public ActivitySummary getActivitySummary(String startDateString, String endDateString) {
        ActivitySummary results = new ActivitySummary();
        LocalDate startDate = new LocalDate(startDateString);
        LocalDate endDate = new LocalDate(endDateString);
        List<OperationsReportDTO> operationsDtos = operationsReportService.generateReport(JavaLocalDateUtils.toJavaLocalDate(startDate), JavaLocalDateUtils.toJavaLocalDate(endDate));
        operationsDtos.forEach(results::addOperationsDto);
        List<OccupancyForecast> occupancyForecasts = tenantCrudService.findByNamedQuery(OccupancyForecast.FOR_DATE_RANGE, QueryParameter.with("startDate", startDate.toDate()).and("endDate", endDate.toDate()).parameters());
        occupancyForecasts.forEach(results::addOccupancyForecast);
        List<TotalActivity> totalActivities = tenantCrudService.findByNamedQuery(TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                QueryParameter.with("startDate", startDate.toDate()).
                        and("endDate", endDate.toDate()).
                        and("propertyId", PacmanWorkContextHelper.getPropertyId()).
                        parameters());
        totalActivities.forEach(results::addTotalActivity);
        Product product = tenantCrudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
        List<CPDecisionBAROutput> barDecisions = tenantCrudService.findByNamedQuery(CPDecisionBAROutput.GET_MASTER_CLASS_DECISION_FOR_DATE, CPDecisionBAROutput.params(product, startDate));
        barDecisions.forEach(results::addBarDecision);
        List<LastRoomValue> lrvDecisions = tenantCrudService.findByNamedQuery(LastRoomValue.BY_OCCUPANCY_DATE_FOR_MASTER_CLASS, QueryParameter.with("occupancyDate", startDate.toDate()).parameters());
        lrvDecisions.forEach(results::addLrvDecision);
        return results;
    }

    public boolean isForceFullDecisions(int propertyId) {
        Property property = getPropertyById(propertyId, false);
        if (property == null) {
            return false;
        }
        if (property.isForceFullDecisions() == null) {
            property.setForceFullDecisions(false);
            updateProperty(property);
            return false;
        }
        return property.isForceFullDecisions();
    }

    public void setForceFullDecisions(int propertyId, boolean force) {
        Property property = getPropertyById(propertyId, false);
        if (property != null) {
            property.setForceFullDecisions(force);
            updateProperty(property);
        }
    }

    public QuestionnaireStatus getQuestionnaireStatus(int propertyId) {
        Property property = getPropertyById(propertyId, false);
        if (property == null) {
            return QuestionnaireStatus.NOT_STARTED;
        }
        return property.getQuestionnaireStatus();
    }

    public void setQuestionnaireStatus(int propertyId, QuestionnaireStatus status) {
        Property property = getPropertyById(propertyId, false);
        if (property != null) {
            property.setQuestionnaireStatus(status);
            updateProperty(property);
        }
    }

    public boolean isPropertyTwoWay() {
        Property property = getPropertyById(PacmanWorkContextHelper.getPropertyId());
        if (property != null) {
            return Stage.TWO_WAY.equals(property.getStage());
        }
        return false;
    }

    public List<Integer> getPropertyIdsByStage(List<Stage> stages) {
        return globalCrudService.findByNamedQuery(Property.GET_ID_BY_STAGE, QueryParameter.with(Property.PARAM_STAGES, stages).parameters());
    }

    public List<PropertyDTO> getPropertiesInfoByClientCode(String clientCode) {
        final List<Property> properties = globalCrudService.findByNamedQuery(Property.BY_CLIENT_CODE_ACTIVE, QueryParameter.with(CLIENT_CODE, clientCode).parameters());
        return properties.stream().map(PropertyDTO::new).collect(Collectors.toList());
    }

    public ClientPropertyView getClientPropertyIntegrationDto(Integer propertyId) {
        String query = shouldUseFunctionForClientPropertyView() ? ClientPropertyView.BY_PROPERTY_ID_WITH_FUNCTION : ClientPropertyView.BY_PROPERTY_ID;
        return globalCrudService.findByNamedQuerySingleResult(query, QueryParameter.with(PROPERTY_ID, propertyId).parameters());
    }

    public ClientPropertyView getClientPropertyIntegrationDto(String clientCode, String propertyCode) {
        String query = shouldUseFunctionForClientPropertyView() ? ClientPropertyView.BY_CLIENT_CODE_PROPERTY_CODE_WITH_FUNCTION : ClientPropertyView.BY_CLIENT_CODE_PROPERTY_CODE;
        return globalCrudService.findByNamedQuerySingleResult(query, QueryParameter.with(CLIENT_CODE, clientCode).and(PROPERTY_CODE, propertyCode).parameters());
    }

    public Set<Property> getProperties(Client client, Set<String> propertyCodes) {
        Set<Property> properties = new HashSet<>();
        if (CollectionUtils.isNotEmpty(propertyCodes) && nonNull(client)) {
            properties = propertyCodes.stream()
                    .map(p -> clientPropertyCacheService.getProperty(client.getCode(), p))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
        }
        return properties;
    }

    public boolean canShowSpecialCareFlag(String clientCode, String propertyCode) {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_SPECIAL_CARE_FUNCTIONALITY_ENABLED.getParameterName(),
                clientCode, propertyCode) &&
                pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.ENABLE_SPECIAL_CARE.getParameterName(), clientCode, propertyCode) &&
                (pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_SPECIAL_CARE_SCHEDULING.getParameterName(), clientCode, propertyCode) ?
                        isCurrentBetweenSpecialCareStartAndEndDate(clientCode, propertyCode) : Boolean.TRUE);
    }

    private boolean isCurrentBetweenSpecialCareStartAndEndDate(String clientCode, String propertyCode) {
        String context = pacmanConfigParamsService.propertyNode(clientCode, propertyCode);
        Date specialCareStartDate = pacmanConfigParamsService.getParameterValue(context, GUIConfigParamName.SPECIAL_CARE_START_DATE);
        Date specialCareEndDate = pacmanConfigParamsService.getParameterValue(context, GUIConfigParamName.SPECIAL_CARE_END_DATE);
        if (specialCareStartDate == null && specialCareEndDate == null) {
            return true;
        } else if (specialCareStartDate != null && specialCareEndDate != null) {
            String timezone = getTimeZone(clientCode, propertyCode);
            if (StringUtils.isNotEmpty(timezone)) {
                TimeZone propertyTimeZone = TimeZone.getTimeZone(timezone);
                Date currentPropertyDate = propertyTimeZone != null ?
                        DateUtil.currentDateForZoneId(propertyTimeZone.toZoneId()) :
                        DateUtil.currentDate();
                return DateUtil.isDateBetween(DateUtil.removeTimeFromDate(specialCareStartDate),
                        DateUtil.removeTimeFromDate(specialCareEndDate),
                        DateUtil.removeTimeFromDate(currentPropertyDate));
            }
        }
        return false;
    }

    private String getTimeZone(String clientCode, String propertyCode) {
        StringBuilder context = new StringBuilder();
        if (clientCode != null) {
            context.append(Constants.CONFIG_PARAMS_NODE_PREFIX).append(".").append(clientCode);
            if (propertyCode != null) {
                context.append(".").append(propertyCode);
            }
        }
        return StringUtils.isEmpty(context.toString()) ? null :
                pacmanConfigParamsService.getValue(context.toString(), IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value());
    }

    public boolean canShowDoNotMonitorFlag(String clientCode, String propertyCode, Date creationDateTime) {
        return !pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.DAILY_PROCESSING_MONITORING_ENABLED.getParameterName(), clientCode, propertyCode)
                || isProcessingBetweenDoNotMonitorTimeFrame(clientCode, propertyCode, creationDateTime);
    }

    public boolean isProcessingBetweenDoNotMonitorTimeFrame(String clientCode, String propertyCode, Date creationDateTime) {

        String context = pacmanConfigParamsService.propertyNode(clientCode, propertyCode);
        Date doNotMonitorFromDate = pacmanConfigParamsService.getParameterValue(context, GUIConfigParamName.DO_NOT_MONITOR_FROM_DATE);
        Date doNotMonitorToDate = pacmanConfigParamsService.getParameterValue(context, GUIConfigParamName.DO_NOT_MONITOR_TO_DATE);
        if (doNotMonitorFromDate != null && doNotMonitorToDate != null) {
            return DateUtil.isDateBetween(DateUtil.removeTimeFromDate(doNotMonitorFromDate), DateUtil.removeTimeFromDate(doNotMonitorToDate), DateUtil.removeTimeFromDate(creationDateTime)) &&
                    canMonitorProcessingForDay(context, creationDateTime);
        }
        return false;
    }

    private boolean canMonitorProcessingForDay(String context, Date date) {
        boolean enableDNMDaysOfWeek = pacmanConfigParamsService.getParameterValue(context, PreProductionConfigParamName.ENABLE_DNM_DAYS_OF_WEEK);
        if (!enableDNMDaysOfWeek) {
            return true;
        }

        String daysOfWeek = pacmanConfigParamsService.getParameterValue(context, GUIConfigParamName.DO_NOT_MONITOR_DAYS_OF_WEEK);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(daysOfWeek.trim())) {
            Set<String> daysOfWeekSet = new HashSet<>();
            List<String> list = Arrays.asList(StringUtils.split(daysOfWeek.toLowerCase().trim(), ","));
            list.forEach(day -> daysOfWeekSet.add(StringUtils.capitalize(day.trim())));
            String currentDay = getDay(date);
            return daysOfWeekSet.contains(currentDay);
        }
        return true;
    }

    private String getDay(Date date) {
        SimpleDateFormat simpleDateformat = new SimpleDateFormat("EEEE");
        return simpleDateformat.format(date);
    }

    public boolean isActionableSpecialCare(String clientCode, String propertyCode) {
        return pacmanConfigParamsService.getParameterValue(clientCode, propertyCode, GUIConfigParamName.IS_ACTIONABLE_SPECIAL_CARE);
    }

    public DBLoc getDbLoc(Integer dbLocId) {
        if (null == dbLocId) {
            return null;
        }
        return globalCrudService.find(DBLoc.class, dbLocId);
    }

    @SuppressWarnings("unchecked")
    public List<Property> getActivePropertiesByClientCodeAndStages(String clientCode) {
        List<Stage> stages = Arrays.asList(Stage.POPULATION, Stage.ONE_WAY, Stage.TWO_WAY);
        List<Property> properties = globalCrudService.findByNamedQuery(Property.BY_CLIENT_CODE_AND_STAGES, QueryParameter.with(Property.PARAM_STAGES, stages).and(CLIENT_CODE, clientCode).parameters());
        return properties == null ? new ArrayList<>() : properties;
    }

    public Property translateAndGetNGIProperty(String clientCode, String propertyCode) {
        Property property = getPropertyByCode(clientCode, propertyCode);
        if (property == null) {
            ClientProperty translatedClientProperty = clientCodePropertyCodeMappingService.translateNGIClientPropertyCodes(clientCode, propertyCode);
            // If (clientCode, propertyCode) same after translation, we don't have this property
            if (!translatedClientProperty.equals(new ClientProperty(clientCode, propertyCode))) {
                property = getPropertyByCode(translatedClientProperty.getClientCode(), translatedClientProperty.getPropertyCode());
            }
        }
        return property;
    }

    public int startBackfillProcess(boolean isWebrate, boolean isCP, boolean adjustOriginalWebrateValueWithRoomRevenueTax, String startDate, String endDate) {
        String table = isWebrate ? "Webrate" : "Pace_Webrate_Differential";
        try {
            LOGGER.info("WebrateRatevalueDisplayBackfillJob - Backfill started for " + table + " table");
            if (isWebrate) {
                if (isCP) {
                    if (!adjustOriginalWebrateValueWithRoomRevenueTax) {
                        return tenantCrudService.executeUpdateByNativeQuery(RateShopperServiceQueryConstants.WEBRATE_BACKFILL_WITH_RSS_ADJUSTMENT,
                                QueryParameter.with("start_date", startDate)
                                        .and("end_date", endDate)
                                        .parameters());
                    }
                    return tenantCrudService.executeUpdateByNativeQuery(RateShopperServiceQueryConstants.WEBRATE_BACKFILL_WITH_ROOM_TAX_ADJUSTMENT,
                            QueryParameter.with("start_date", startDate)
                                    .and("end_date", endDate)
                                    .parameters());
                }
                return tenantCrudService.executeUpdateByNativeQuery(RateShopperServiceQueryConstants.WEBRATE_BACKFILL,
                        QueryParameter.with("start_date", startDate)
                                .and("end_date", endDate)
                                .parameters());
            }
            if (isCP) {
                if (!adjustOriginalWebrateValueWithRoomRevenueTax) {
                    return tenantCrudService.executeUpdateByNativeQuery(RateShopperServiceQueryConstants.PACE_WEBRATE_DIFFERENTIAL_BACKFILL_WITH_RSS_ADJUSTMENT,
                            QueryParameter.with("start_date", startDate)
                                    .and("end_date", endDate)
                                    .parameters());
                }
                return tenantCrudService.executeUpdateByNativeQuery(RateShopperServiceQueryConstants.PACE_WEBRATE_DIFFERENTIAL_BACKFILL_WITH_ROOM_TAX_ADJUSTMENT,
                        QueryParameter.with("start_date", startDate)
                                .and("end_date", endDate)
                                .parameters());
            }
            return tenantCrudService.executeUpdateByNativeQuery(RateShopperServiceQueryConstants.PACE_WEBRATE_DIFFERENTIAL_BACKFILL,
                    QueryParameter.with("start_date", startDate)
                            .and("end_date", endDate)
                            .parameters());
        } catch (Exception e) {
            LOGGER.error("WebrateRatevalueDisplayBackfillJob - Exception occured for " + table + " table");
            LOGGER.info(e);
        }
        LOGGER.info("WebrateRatevalueDisplayBackfillJob - Backfill finished for " + table + " table");
        return 0;
    }

    public boolean isPropertyCP(Property property) {
        if (nonNull(property)) {
            String context = "pacman." + property.getClient().getCode() + "." + property.getCode();
            return pacmanConfigParamsService.getParameterValue(context, IS_CONTINUOUS_PRICING_ENABLED);
        }
        return false;
    }

    public String getVirtualPropertyDisplayCode(Integer propertyId) {
        Property property = globalCrudService.findByNamedQuerySingleResult(Property.BY_ID,
                QueryParameter.with("id", propertyId).parameters());
        return (null != property && property.isVirtualProperty()) ? property.getVirtualPropertyDisplayCode() : null;
    }

    public List<Object[]> getPropertyCodeDisplayCodeForVP() {
        List<Object[]> resultSet = globalCrudService.findByNamedQuery(Property.ALL_PROP_CODE_AND_DISPLAY_CODE_OF_VP);
        if (CollectionUtils.isEmpty(resultSet)) {
            return Collections.emptyList();
        }
        return resultSet;
    }

    public List<Pair<String, String>> getPropertiesByVirtualPropertyDisplayCodesAndClientId(List<String> virtualPropertyDisplayCodes, Integer clientId) {
        List<Pair<String, String>> result = new ArrayList<>();
        int totalNumberOfRecords = virtualPropertyDisplayCodes.size();
        int pageNo = 0;
        while (totalNumberOfRecords > 0) {
            pageNo++;
            result.addAll(getPropertiesByVirtualPropertyDisplayCodesAndClientIdPageWise(pageNo, virtualPropertyDisplayCodes, clientId));
            totalNumberOfRecords = totalNumberOfRecords - Constants.PAGE_SIZE;
        }
        return result;
    }

    private List<Pair<String, String>> getPropertiesByVirtualPropertyDisplayCodesAndClientIdPageWise(Integer pageNo, List<String> virtualPropertyDisplayCodes, Integer clientId) {
        List<String> virtualPropertyDisplayCodesBatch = Pagination.paginateSublistForPageNoByPageSize(pageNo, virtualPropertyDisplayCodes, Constants.PAGE_SIZE);

        List<Object[]> rows = globalCrudService.findByNamedQuery(Property.BY_VIRTUAL_PROPERTY_DISPLAY_CODES_AND_CLIENT_ID,
                QueryParameter.with("vpDisplayCodes", virtualPropertyDisplayCodesBatch)
                        .and("clientId", clientId)
                        .parameters());
        return rows.stream().map(row -> new Pair<>((String) row[0], (String) row[1]))
                .collect(Collectors.toList());
    }

    public List<Integer> getPropertyIdsByClientCodeAndStage(String clientCode) {
        return globalCrudService.findByNamedQuery(Property.GET_ID_BY_CLIENT_CODE_AND_STAGES,
                QueryParameter.with(CLIENT_CODE, clientCode).and("stage", Stage.TWO_WAY).parameters());
    }

    public Property getPropertyByVirtualDisplayCode(String virtualPropertyDisplayCode) {
        return globalCrudService.findByNamedQuerySingleResult(Property.BY_VIRTUAL_PROPERTY_DISPLAY_CODE,
                QueryParameter.with("virtualPropertyDisplayCode", virtualPropertyDisplayCode)
                        .parameters());
    }

    public String getPropertyZoneDate() {
        String clientCode = PacmanWorkContextHelper.getClientCode();
        String propertyCode = PacmanWorkContextHelper.getPropertyCode();
        Date propertyDate = DateUtil.convertZonedDateTimeToJavaUtilDate(getZonedDateTime(clientCode, propertyCode));
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("propertyZoneDate", propertyDate.toString());
        return jsonObject.toJSONString();
    }

    public List<String> getNearByPropertiesUpsIds(String clientId, String upsId, int radius) {
        List<String> nearByPropertiesUpsIds = new ArrayList<>();
        if(StringUtils.isNotBlank(upsId)){
            List<UPSProperty> properties = upsService.getNearByProperties(upsId, radius, clientId);
            nearByPropertiesUpsIds = properties.stream()
                    .filter(upsProperty -> Objects.nonNull(upsProperty.getClientId()))
                    .filter(upsProperty -> upsProperty.getClientId().equalsIgnoreCase(clientId))
                    .map(UPSProperty::getUnifiedId)
                    .collect(Collectors.toList());
            LOGGER.info("Nearby properties UPS Ids List :"+ nearByPropertiesUpsIds);
        } else {
            LOGGER.error("Property doesn't have a UpsId defined for propertyId: " + PacmanWorkContextHelper.getPropertyId());
        }
        return nearByPropertiesUpsIds;
    }

    public Long getPropertyCountByClientCode(String clientCode) {
        return globalCrudService.findByNamedQuerySingleResult(Property.NUMBER_OF_PROPERTIES_BY_CLIENT_CODE,
                QueryParameter.with(Property.PARAM_CLIENT_CODE, clientCode)
                        .parameters());
    }

    public Property getByUPSId(String upsId){
        return globalCrudService.findByNamedQuerySingleResult(Property.GET_BY_UNIFIED_PROPERTY_ID,
                QueryParameter.with(Property.PARAM_UNIFIED_PROPERTY_ID, upsId)
                        .parameters());

    }

    public boolean canShowMigrationFlag(String clientCode, String propertyCode) {
        return pacmanConfigParamsService.getBooleanParameterValue(GUIConfigParamName.ENABLE_MIGRATION.getParameterName(), clientCode, propertyCode);
    }

    public Long getPropertyNumberOfDays(Integer propertyId) {
        Integer oldPropertyId = PacmanWorkContextHelper.getPropertyId();
        PacmanWorkContextHelper.setPropertyId(propertyId);
        long daysSinceFirstArrivalDate = hotStartPropertyService.getDaysSinceFirstArrivaldate();
        PacmanWorkContextHelper.setPropertyId(oldPropertyId);
        return daysSinceFirstArrivalDate;
    }
}
