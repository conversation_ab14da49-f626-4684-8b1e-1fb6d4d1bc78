package com.ideas.tetris.pacman.services.businessinsights.criteria;

import com.ideas.tetris.platform.common.errorhandling.TetrisException;

import java.math.BigDecimal;

public enum BusinessInsightsValue {
    ARRIVALS("arrivals", BusinessInsightsAggregator.SUM, false, "arrivals", Long.class),
    CANCELLATIONS("cancellations", BusinessInsightsAggregator.SUM, false, "CANCELLATIONS", Long.class),
    DEPARTURES("departures", BusinessInsightsAggregator.SUM, false, "DEPARTURES", Long.class),
    ROOM_REVENUE("roomRevenue", BusinessInsightsAggregator.SUM, false, "roomrevenue", BigDecimal.class),
    ROOMS_SOLD("roomsSold", BusinessInsightsAggregator.SUM, false, "roomssold", Long.class),
    TOTAL_REVENUE("totalRevenue", BusinessInsightsAggregator.SUM, false, "common.total.revenue", BigDecimal.class),
    CAPACITY("capacity", BusinessInsightsAggregator.SUM, false, "common.effective.capacity", Long.class),
    NO_SHOW("noShow", BusinessInsightsAggregator.SUM, false, "NO_SHOW", Long.class),

    ARRIVALS_LAST_YEAR("arrivals", BusinessInsightsAggregator.SUM, true, "common.arrivals.last.year", Long.class),
    CANCELLATIONS_LAST_YEAR("cancellations", BusinessInsightsAggregator.SUM, true, "common.cancellations.last.year", Long.class),
    DEPARTURES_LAST_YEAR("departures", BusinessInsightsAggregator.SUM, true, "common.departures.last.year", Long.class),
    ROOM_REVENUE_LAST_YEAR("roomRevenue", BusinessInsightsAggregator.SUM, true, "common.room.revenue.last.year", BigDecimal.class),
    OCCUPANCY_ON_BOOKS_LAST_YEAR("roomsSold", BusinessInsightsAggregator.SUM, true, "common.occupancyonbooks.last.year", Long.class),
    TOTAL_REVENUE_LAST_YEAR("totalRevenue", BusinessInsightsAggregator.SUM, true, "common.total.revenue.last.year", BigDecimal.class),
    CAPACITY_LAST_YEAR("capacity", BusinessInsightsAggregator.SUM, true, "common.effective.capacity.last.year", Long.class),

    NO_SHOW_LAST_YEAR("noShow", BusinessInsightsAggregator.SUM, true, "common.no.show.last.year", Long.class),

    AVG_DAILY_RATE(ROOM_REVENUE, ROOMS_SOLD, "avgDailyRate", BigDecimal.class),
    AVG_DAILY_RATE_LAST_YEAR(ROOM_REVENUE_LAST_YEAR, OCCUPANCY_ON_BOOKS_LAST_YEAR, "common.avg.daily.rate.last.year", BigDecimal.class),
    TOTAL_REVENUE_AVG(TOTAL_REVENUE, ROOMS_SOLD, "common.total.revenue.avg", BigDecimal.class),
    TOTAL_REVENUE_LAST_YEAR_AVG(TOTAL_REVENUE_LAST_YEAR, OCCUPANCY_ON_BOOKS_LAST_YEAR, "common.total.revenue.last.year.avg", BigDecimal.class),
    REVENUE_PER_AVAILABLE_ROOM(ROOM_REVENUE, CAPACITY, "REVENUE_PER_AVAIL_ROOM", BigDecimal.class),
    REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR(ROOM_REVENUE_LAST_YEAR, CAPACITY_LAST_YEAR, "REVENUE_PER_AVAIL_ROOM.LAST.YEAR", BigDecimal.class),
    ACCOM_CLASS_CODE("accomClassCode", BusinessInsightsAggregator.UNIQUE, false, "room.class", String.class),
    ARRIVAL_DATE("arrivalDate", BusinessInsightsAggregator.UNIQUE, false, "common.arrivalDate", String.class),
    DAY_OF_WEEK("dayOfWeek", BusinessInsightsAggregator.UNIQUE, false, "report.dow", String.class),
    DAYS_TO_ARRIVAL("daysToArrival", BusinessInsightsAggregator.UNIQUE, false, "daysToArrival", Long.class),
    LENGTH_OF_STAY("lengthOfStay", BusinessInsightsAggregator.UNIQUE, false, "report.los", Long.class),
    MARKET_SEGMENT_CODE("marketSegCode", BusinessInsightsAggregator.UNIQUE, false, "market.segment", String.class),
    NATIONALITY("nationality", BusinessInsightsAggregator.UNIQUE, false, "common.nationality", String.class),
    OCCUPANCY_DATE("primaryKey" + "-" +
            "occupancyDate",
            BusinessInsightsAggregator.UNIQUE, false, "occupancyDate", String.class),
    RATE_CODE("rateCode", BusinessInsightsAggregator.UNIQUE, false, "rate.code", String.class),
    RATE_VALUE("rateValue", BusinessInsightsAggregator.SUM, false, "common.rate.value", BigDecimal.class),
    RATE_VALUE_LAST_YEAR("rateValue", BusinessInsightsAggregator.SUM, true, "common.rate.value.last.year", BigDecimal.class),
    NET_RATE_VALUE("netRateValue", BusinessInsightsAggregator.SUM, false, "common.net.rate.value", BigDecimal.class),
    NET_RATE_VALUE_LAST_YEAR("netRateValue", BusinessInsightsAggregator.SUM, true, "common.net.rate.value.last.year", BigDecimal.class),
    SOURCE_BOOKING("sourceBooking", BusinessInsightsAggregator.UNIQUE, false, "common.source.booking", String.class),
    CHANNEL("channel", BusinessInsightsAggregator.UNIQUE, false, "channel", String.class),
    CHANNEL_SOURCE_BOOKING("channelSourceBooking", BusinessInsightsAggregator.UNIQUE, false, "common.channel.source.booking", String.class),
    ROOM_TYPE_CODE("accomTypeCode", BusinessInsightsAggregator.UNIQUE, false, "roomTypeCode", String.class),
    BOOKING_DATE("bookingDate", BusinessInsightsAggregator.UNIQUE, false, "common.booking.date", String.class),
    INDIVIDUAL_STATUS("individualStatus", BusinessInsightsAggregator.UNIQUE, false, "common.individual.status", String.class),
    TOTAL_CHANNEL_COST("totalChannelCost", BusinessInsightsAggregator.SUM, false, "common.total.channel.cost", BigDecimal.class),
    TOTAL_CHANNEL_COST_LAST_YEAR("totalChannelCost", BusinessInsightsAggregator.SUM, true, "common.total.channel.cost.last.year", BigDecimal.class),
    TOTAL_ACQUISITION_COST("totalAcquisitionCost", BusinessInsightsAggregator.SUM, false, "common.total.acquisition.cost", BigDecimal.class),
    TOTAL_ACQUISITION_COST_LAST_YEAR("totalAcquisitionCost", BusinessInsightsAggregator.SUM, true, "common.total.acquisition.cost.last.year", BigDecimal.class),
    NET_REVENUE("netRevenue", BusinessInsightsAggregator.SUM, false, "common.net.room.revenue", BigDecimal.class),
    NET_REVENUE_LAST_YEAR("netRevenue", BusinessInsightsAggregator.SUM, true, "common.net.room.revenue.last.year", BigDecimal.class),
    NET_AVG_DAILY_RATE(NET_REVENUE, ROOMS_SOLD, "common.net.adr", BigDecimal.class),
    NET_AVG_DAILY_RATE_LAST_YEAR(NET_REVENUE_LAST_YEAR, OCCUPANCY_ON_BOOKS_LAST_YEAR, "common.net.adr.last.year", BigDecimal.class),
    NET_REVENUE_PER_AVAILABLE_ROOM(NET_REVENUE, CAPACITY, "NET_REVENUE_PER_AVAIL_ROOM", BigDecimal.class),
    NET_REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR(NET_REVENUE_LAST_YEAR, CAPACITY_LAST_YEAR, "NET_REVENUE_PER_AVAIL_ROOM.LAST.YEAR", BigDecimal.class),

    CANCELLATION_DATE("cancellationDate", BusinessInsightsAggregator.UNIQUE, false, "common.cancellation.date", String.class);

    private String field;
    private BusinessInsightsAggregator aggregator;
    private boolean lastYear;
    private String caption;
    private Class<?> fieldClassForExcelOutput;

    private BusinessInsightsValue constituentValue1;
    private BusinessInsightsValue constituentValue2;

    BusinessInsightsValue(String field, BusinessInsightsAggregator aggregator, boolean lastYear, String caption, Class<?> fieldClassForExcelOutput) {
        this.field = field;
        this.aggregator = aggregator;
        this.lastYear = lastYear;
        this.caption = caption;
        this.fieldClassForExcelOutput = fieldClassForExcelOutput;
    }

    BusinessInsightsValue(String field, BusinessInsightsAggregator aggregator, boolean lastYear, String caption) {
        this.field = field;
        this.aggregator = aggregator;
        this.lastYear = lastYear;
        this.caption = caption;
    }


    /**
     * This is a composite BusinessInsightsValue enum. Its value is derived from the value of its constituents.
     * This enum should not be used as part of a Category or Filter as composite values does not make any sense for them.
     * e.g. Having ADR (which is a composite) as an option in BI Filter does not make any sense.
     */
    BusinessInsightsValue(BusinessInsightsValue v1, BusinessInsightsValue v2, String caption) {
        this(
                v1.getField() + "_" + v2.getField(),
                null,
                v1.lastYear,
                caption
        );
        constituentValue1 = v1;
        constituentValue2 = v2;
    }

    BusinessInsightsValue(BusinessInsightsValue v1, BusinessInsightsValue v2, String caption, Class<?> fieldClassForExcelOutput) {
        this(
                v1.getField() + "_" + v2.getField(),
                null,
                v1.lastYear,
                caption
        );
        constituentValue1 = v1;
        constituentValue2 = v2;
        this.fieldClassForExcelOutput = fieldClassForExcelOutput;
    }

    public String getField() {
        return field;
    }

    public String getTableAlias() {
        if (this.aggregator == BusinessInsightsAggregator.UNIQUE) {
            return field;
        }

        if (isCompositeValue()) {
            return constituentValue1.getTableAlias() + "_" + constituentValue2.getTableAlias();
        }
        return field + "_" + this.aggregator.toString();
    }

    public BusinessInsightsAggregator getAggregator() {
        return aggregator;
    }

    public static BusinessInsightsValue fromTableAlias(String tableAlias, boolean lastYear) {
        for (BusinessInsightsValue value : BusinessInsightsValue.values()) {
            if (tableAlias.equals(value.getTableAlias()) && (lastYear == value.isLastYear())) {
                return value;
            }
        }

        throw new TetrisException("Can't find BusinessInsightsValue for table alias: " + tableAlias + "," + lastYear);
    }

    public boolean isLastYear() {
        return lastYear;
    }

    public String getCaption() {
        return caption;
    }

    public Class<?> getFieldClassForExcelOutput() {
        return fieldClassForExcelOutput;
    }

    public static BusinessInsightsValue toBusinessInsightsValue(String byKey) {
        for (BusinessInsightsValue businessInsightsValue : BusinessInsightsValue.values()) {
            if (businessInsightsValue.getCaption().equalsIgnoreCase(byKey)) {
                return businessInsightsValue;
            }
        }
        throw new IllegalArgumentException(String.format("Object Not Found for %s", byKey));
    }

    public static void updateValuesForAllInclusive(int totalRateEnabledValue) {
        boolean booleanToggleValue = totalRateEnabledValue == 1;
        RATE_VALUE.setCaption(booleanToggleValue ? "common.all.inclusive.revenue" : "common.rate.value");
        RATE_VALUE_LAST_YEAR.setCaption(booleanToggleValue ? "common.all.inclusive.revenue.last.year" : "common.rate.value.last.year");
        NET_RATE_VALUE.setCaption(booleanToggleValue ? "common.all.inclusive.net.rate.value" : "common.net.rate.value");
        NET_RATE_VALUE_LAST_YEAR.setCaption(booleanToggleValue ? "common.all.inclusive.net.rate.value.last.year" : "common.net.rate.value.last.year");

        AVG_DAILY_RATE.setConstituentValue1(booleanToggleValue ? RATE_VALUE : ROOM_REVENUE);
        AVG_DAILY_RATE_LAST_YEAR.setConstituentValue1(booleanToggleValue ? RATE_VALUE_LAST_YEAR : ROOM_REVENUE_LAST_YEAR);
        REVENUE_PER_AVAILABLE_ROOM.setConstituentValue1(booleanToggleValue ? RATE_VALUE : ROOM_REVENUE);
        REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR.setConstituentValue1(booleanToggleValue ? RATE_VALUE_LAST_YEAR : ROOM_REVENUE_LAST_YEAR);
        NET_AVG_DAILY_RATE.setConstituentValue1(booleanToggleValue ? NET_RATE_VALUE : NET_REVENUE);
        NET_AVG_DAILY_RATE_LAST_YEAR.setConstituentValue1(booleanToggleValue ? NET_RATE_VALUE_LAST_YEAR : NET_REVENUE_LAST_YEAR);
        NET_REVENUE_PER_AVAILABLE_ROOM.setConstituentValue1(booleanToggleValue ? NET_RATE_VALUE : NET_REVENUE);
        NET_REVENUE_PER_AVAILABLE_ROOM_LAST_YEAR.setConstituentValue1(booleanToggleValue ? NET_RATE_VALUE_LAST_YEAR : NET_REVENUE_LAST_YEAR);

    }

    public void setCaption(String caption) {
        this.caption = caption;
    }

    public void setConstituentValue1(BusinessInsightsValue constituentValue1) {
        this.constituentValue1 = constituentValue1;
    }

    public BusinessInsightsValue getConstituentValue1() {
        return constituentValue1;
    }

    public BusinessInsightsValue getConstituentValue2() {
        return constituentValue2;
    }

    public boolean isCompositeValue() {
        return constituentValue1 != null && constituentValue2 != null;
    }
}
