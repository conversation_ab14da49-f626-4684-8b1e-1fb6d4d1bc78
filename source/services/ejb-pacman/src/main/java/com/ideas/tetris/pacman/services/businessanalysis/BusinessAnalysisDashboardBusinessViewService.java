package com.ideas.tetris.pacman.services.businessanalysis;

import com.google.common.collect.ImmutableMap;
import com.ideas.tetris.pacman.Service;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.businessanalysis.dto.BusinessAnalysisDataDetailsDto;
import com.ideas.tetris.pacman.services.businessanalysis.repository.BusinessAnalysisDashboardBusinessViewRepository;
import com.ideas.tetris.pacman.services.businessgroup.service.BusinessGroupService;
import com.ideas.tetris.pacman.services.dashboard.util.DateCalculator;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSegBusinessGroup;
import com.ideas.tetris.pacman.services.marketsegment.service.MarketSegmentService;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.businessanalysis.BusinessAnalysisDashboardService.DYNAMIC_PACE;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

@Service
@Component
public class BusinessAnalysisDashboardBusinessViewService {

    private static final Logger LOGGER = Logger.getLogger(BusinessAnalysisDashboardBusinessViewService.class.getName());

    @Autowired
	private BusinessAnalysisDashboardService businessAnalysisDashboardService;
    @Autowired
	private BusinessAnalysisDashboardBusinessViewRepository businessAnalysisDashboardBusinessViewRepository;
    @Autowired
	protected DateService dateService;
    @Autowired
	protected MarketSegmentService marketSegmentService;
    @Autowired
	private BusinessGroupService businessGroupService;

    @Autowired
    private PacmanConfigParamsService configParamService;

    public static final String CURRENT_YEAR = "currentYear";
    public static final String LAST_YEAR = "lastYear";
    private static final String LAST_TWO_YEARS = "last2Years";
    private static final String YEAR_2019 = "year2019";
    private static final String INVENTORY_GROUP_ID = "inventoryGroupId";
    private static final String PROPERTY_ID = "propertyId";
    private static final String START_DATE = "startDate";
    private static final String START_DATE_LOG_ENTRY = ", StartDate: ";
    private static final String END_DATE = "endDate";
    private static final String END_DATE_LOG_ENTRY = ", EndDate: ";
    private static final String PACE_DAYS_LOG_ENTRY = ", Pace Days: ";
    private static final String INCLUDE_LAST_YEAR_LOG_ENTRY = ", Include Last Year: ";
    private static final String INCLUDE_LAST_TWO_YEARS_LOG_ENTRY = ", Include Last 2 Years: ";
    private static final String BUSINESS_DATE = "businessDate";
    private static final String BUSINESS_GROUP_ID = "businessGroupId";
    public static final String PRODUCT_ID = "productId";
    private static final String UNASSIGNED = "Unassigned";
    private static final String COMP_ROOMS_FILTER = "compRoomsFilter";

    private static final String TOTAL_RATE_ENABLED = "totalRateEnabled";

    private static final int EIGHT = 8;
    private static final int NINE = 9;
    private static final int TEN = 10;
    private static final int ELEVEN = 11;
    private static final int TWELVE = 12;
    private static final int THIRTEEN = 13;
    private static final int FOURTEEN = 14;
    private static final int FIFTEEN = 15;
    private static final int SIXTEEN = 16;
    private static final int UNASSIGNED_ID = -1;

    private static final String LOAD_BUSINESS_VIEW_BUSINESS_ANALYSIS_DATA_DETAILS_FOR_PROPERTY = "Loading Business View BusinessAnalysisDataDetailsDtos for Property: ";

    public List<BusinessAnalysisDataDetailsDto> getBusinessViewBusinessAnalysisDataDetailDtos
            (Date startDate, Date endDate, boolean includeLastYear, boolean includeLast2Years, boolean includeYear2019, int pastDays, String businessGroupIds, String msStatusIds, boolean compRoomsFilter) {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();

        LOGGER.debug(LOAD_BUSINESS_VIEW_BUSINESS_ANALYSIS_DATA_DETAILS_FOR_PROPERTY + propertyId + START_DATE_LOG_ENTRY + startDate + END_DATE_LOG_ENTRY + endDate + INCLUDE_LAST_YEAR_LOG_ENTRY + includeLastYear + INCLUDE_LAST_TWO_YEARS_LOG_ENTRY + includeLast2Years + PACE_DAYS_LOG_ENTRY + pastDays + ", businessGroupIds " + businessGroupIds + "," +COMP_ROOMS_FILTER + compRoomsFilter);
        String procedure = "dbo.usp_bad_load_property_bv_details :propertyId, :startDate,:endDate,:businessDate, :businessGroupId, :msStatusIds,:compRoomsFilter";

        Map<String, Object> parameters = new HashMap<>();
        parameters.put(PROPERTY_ID, propertyId);
        parameters.put(START_DATE, startDate);
        parameters.put(END_DATE, endDate);
        parameters.put(BUSINESS_GROUP_ID, businessGroupIds);
        parameters.put(BUSINESS_DATE, businessAnalysisDashboardService.calculateBusinessDateForPastDays(pastDays));
        parameters.put("msStatusIds", msStatusIds);
        parameters.put(COMP_ROOMS_FILTER, compRoomsFilter);

        RowMapper<BusinessAnalysisDataDetailsDto> rowMapper = row -> {
            BusinessAnalysisDataDetailsDto dto = new BusinessAnalysisDataDetailsDto();
            businessAnalysisDashboardService.getBusinessAnalysisDataDetailDtoFromRowLy(dto, row);
            dto.setOnBooksPickUp((BigDecimal) row[EIGHT]);
            dto.setRevenuePickUp((BigDecimal) row[NINE]);
            dto.setAdrPickUp((BigDecimal) row[TEN]);
            getBudgetAndUserForecastRows(dto, row);
            return dto;
        };

        RowMapper<BusinessAnalysisDataDetailsDto> lyRowMapper = row -> {
            BusinessAnalysisDataDetailsDto dto = new BusinessAnalysisDataDetailsDto();
            businessAnalysisDashboardService.getBusinessAnalysisDataDetailDtoFromRowLy(dto, row);
            return dto;
        };

        RowMapper<BusinessAnalysisDataDetailsDto> l2yRowMapper = row -> {
            BusinessAnalysisDataDetailsDto dto = new BusinessAnalysisDataDetailsDto();
            businessAnalysisDashboardService.getBusinessAnalysisDataDetailDtoFromRowL2y(dto, row);
            return dto;
        };

        RowMapper<BusinessAnalysisDataDetailsDto> year2019RowMapper = row -> {
            BusinessAnalysisDataDetailsDto dto = new BusinessAnalysisDataDetailsDto();
            businessAnalysisDashboardService.getBusinessAnalysisDataDetailDtoFromRowYear2019(dto, row);
            return dto;
        };

        Map<String, String> procedureMap = ImmutableMap.of(CURRENT_YEAR, procedure, LAST_YEAR, procedure, LAST_TWO_YEARS, procedure, YEAR_2019, procedure);
        Map<String, Map<String, Object>> parametersMap = ImmutableMap.of(CURRENT_YEAR, parameters, LAST_YEAR, parameters, LAST_TWO_YEARS, parameters, YEAR_2019, parameters);
        return executeProcedureWithParameters(procedureMap, parametersMap, rowMapper, includeLastYear, lyRowMapper, includeLast2Years, l2yRowMapper, includeYear2019, year2019RowMapper, true);
    }

    public List<BusinessAnalysisDataDetailsDto> getBusinessViewByInventoryBusinessAnalysisDataDetailDtos
            (Integer inventoryGroupId, Date startDate, Date endDate, boolean includeLastYear, boolean includeLast2Years, boolean includeYear2019,
             int pastDays, String businessGroupIds, String msStatusIds, boolean compRoomsFilter, boolean includeDynamicPace) {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        LOGGER.debug("Loading Business View BusinessAnalysisDataDetailsDtos for InventoryGroupView: " + propertyId + INVENTORY_GROUP_ID + inventoryGroupId + START_DATE_LOG_ENTRY + startDate + END_DATE_LOG_ENTRY + endDate + INCLUDE_LAST_YEAR_LOG_ENTRY + includeLastYear + INCLUDE_LAST_TWO_YEARS_LOG_ENTRY + includeLast2Years + PACE_DAYS_LOG_ENTRY + pastDays + ", businessGroupIds " + businessGroupIds + "," +COMP_ROOMS_FILTER + compRoomsFilter);
        String procedure = new StringBuilder("dbo.usp_bad_load_property_bv_details_for_inventorygroup")
                .append(includeDynamicPace ? DYNAMIC_PACE + " " : " ")
                .append(":propertyId, :inventoryGroupId, :startDate,:endDate, :businessDate, :businessGroupId, :msStatusIds,:compRoomsFilter").toString();

        Map<String, Object> parameters = new HashMap<>();
        parameters.put(PROPERTY_ID, propertyId);
        parameters.put(INVENTORY_GROUP_ID, inventoryGroupId);
        parameters.put(START_DATE, startDate);
        parameters.put(END_DATE, endDate);
        parameters.put(BUSINESS_GROUP_ID, businessGroupIds);
        parameters.put(BUSINESS_DATE, businessAnalysisDashboardService.calculateBusinessDateForPastDays(pastDays));
        parameters.put("msStatusIds", msStatusIds);
        parameters.put(COMP_ROOMS_FILTER, compRoomsFilter);
        Map<String, Object> mandatoryParams = new HashMap<>(parameters);
        String procedureY2019;
        if (includeDynamicPace) {
            procedureY2019 = procedure.replace(DYNAMIC_PACE, "");
            procedure +=",:totalRateEnabled ";
            parameters.put(TOTAL_RATE_ENABLED,configParamService.getIntegerParameterValue(FeatureTogglesConfigParamName.TOTAL_RATE_ENABLED.value()));
        }
        else procedureY2019 = procedure;

        RowMapper<BusinessAnalysisDataDetailsDto> rowMapper = row -> {
            BusinessAnalysisDataDetailsDto dto = new BusinessAnalysisDataDetailsDto();
            businessAnalysisDashboardService.getBusinessAnalysisDataDetailDtoFromRowLy(dto, row);
            dto.setOnBooksPickUp((BigDecimal) row[EIGHT]);
            dto.setRevenuePickUp(row[NINE] != null ? (BigDecimal) row[NINE] : null);
            dto.setAdrPickUp(row[TEN] != null ? (BigDecimal) row[TEN] : null);
            return dto;
        };

        RowMapper<BusinessAnalysisDataDetailsDto> lyRowMapper = row -> {
            BusinessAnalysisDataDetailsDto dto = new BusinessAnalysisDataDetailsDto();
            businessAnalysisDashboardService.getBusinessAnalysisDataDetailDtoFromRowLy(dto, row);
            return dto;
        };

        RowMapper<BusinessAnalysisDataDetailsDto> l2yRowMapper = row -> {
            BusinessAnalysisDataDetailsDto dto = new BusinessAnalysisDataDetailsDto();
            businessAnalysisDashboardService.getBusinessAnalysisDataDetailDtoFromRowL2y(dto, row);
            return dto;
        };

        RowMapper<BusinessAnalysisDataDetailsDto> year2019RowMapper = row -> {
            BusinessAnalysisDataDetailsDto dto = new BusinessAnalysisDataDetailsDto();
            businessAnalysisDashboardService.getBusinessAnalysisDataDetailDtoFromRowYear2019(dto, row);
            return dto;
        };

        Map<String, String> procedureMap = ImmutableMap.of(CURRENT_YEAR, procedure, LAST_YEAR, procedure, LAST_TWO_YEARS, procedure, YEAR_2019, procedureY2019);
        Map<String, Map<String, Object>> parametersMap = ImmutableMap.of(CURRENT_YEAR, parameters, LAST_YEAR, parameters, LAST_TWO_YEARS, parameters, YEAR_2019, mandatoryParams);
        return executeProcedureWithParameters(procedureMap, parametersMap, rowMapper, includeLastYear, lyRowMapper,
                includeLast2Years, l2yRowMapper, includeYear2019, year2019RowMapper, true);
    }

    private void getBudgetAndUserForecastRows(BusinessAnalysisDataDetailsDto dto, Object[] row) {
        dto.setBudgetRooms((BigDecimal) row[ELEVEN]);
        dto.setBudgetRevenue((BigDecimal) row[TWELVE]);
        dto.setBudgetAdr((BigDecimal) row[THIRTEEN]);
        dto.setUserForecastRooms((BigDecimal) row[FOURTEEN]);
        dto.setUserForecastRevenue((BigDecimal) row[FIFTEEN]);
        dto.setUserForecastAdr((BigDecimal) row[SIXTEEN]);
    }

    List<BusinessAnalysisDataDetailsDto> executeProcedureWithParameters(Map<String, String> procedureMap, Map<String, Map<String, Object>> parametersMap, RowMapper<BusinessAnalysisDataDetailsDto> rowMapper,
                                                                        boolean includeLastYear, RowMapper<BusinessAnalysisDataDetailsDto> lyRowMapper,
                                                                        boolean includeLast2Years, RowMapper<BusinessAnalysisDataDetailsDto> l2yRowMapper,
                                                                        boolean includeYear2019, RowMapper<BusinessAnalysisDataDetailsDto> year2019RowMapper, boolean dowAdjustForDate) {
        final Map<String, Object> parameters = parametersMap.get(CURRENT_YEAR);
        List<BusinessAnalysisDataDetailsDto> businessAnalysisDataDetailsDtos = businessAnalysisDashboardBusinessViewRepository.getCurrentYearBusinessAnalysisDataDetailsDtos(procedureMap, rowMapper, parameters);

        final Date currentStartDate = (Date) parameters.get(START_DATE);
        final Date currentEndDate = (Date) parameters.get(END_DATE);

        if (includeLastYear) {
            String lyProc = procedureMap.get(LAST_YEAR).replace("_details", "_details_asof_businessdate");
            Date businessDate = DateUtil.removeTimeFromDate(dateService.getAdjustedBusinessDate(-1));
            Map<String, Object> lastYearParameters = parametersMap.get(LAST_YEAR);
            lastYearParameters.put(START_DATE, DateCalculator.calculateDateForLastYear(currentStartDate, dowAdjustForDate));
            lastYearParameters.put(END_DATE, DateCalculator.calculateDateForLastYear(currentEndDate, dowAdjustForDate));
            lastYearParameters.put(BUSINESS_DATE, DateCalculator.calculateDateForLastYear(businessDate, true));

            List<BusinessAnalysisDataDetailsDto> lastYearBusinessAnalysisDataDetailsDtos = businessAnalysisDashboardBusinessViewRepository.getLastYearBusinessAnalysisDataDetailsDtos(lyRowMapper, lyProc, lastYearParameters);
            businessAnalysisDashboardService.overlayLastYearBusinessAnalysisDataDetailsDtos(businessAnalysisDataDetailsDtos, lastYearBusinessAnalysisDataDetailsDtos);
        }

        if (includeLast2Years) {
            String lyProc = procedureMap.get(LAST_TWO_YEARS).replace("_details", "_details_asof_businessdate");
            Date businessDate = DateUtil.removeTimeFromDate(dateService.getAdjustedBusinessDate(-1));

            Map<String, Object> last2YearsParameters = parametersMap.get(LAST_TWO_YEARS);

            last2YearsParameters.put(START_DATE, DateCalculator.calculateDateForLast2Year(currentStartDate, dowAdjustForDate));
            last2YearsParameters.put(END_DATE, DateCalculator.calculateDateForLast2Year(currentEndDate, dowAdjustForDate));
            last2YearsParameters.put(BUSINESS_DATE, DateCalculator.calculateDateForLast2Year(businessDate, true));

            List<BusinessAnalysisDataDetailsDto> last2YearsBusinessAnalysisDataDetailsDtos = businessAnalysisDashboardBusinessViewRepository.getLastTwoYearBusinessAnalysisDataDetailsDtos(l2yRowMapper, lyProc, last2YearsParameters);
            businessAnalysisDashboardService.overlayLastTwoYearBusinessAnalysisDataDetailsDtos(businessAnalysisDataDetailsDtos, last2YearsBusinessAnalysisDataDetailsDtos);
        }

        if (includeYear2019) {
            String lyProc = procedureMap.get(YEAR_2019).replace("_details", "_details_asof_businessdate");
            Date businessDate = DateUtil.removeTimeFromDate(dateService.getAdjustedBusinessDate(-1));
            Map<String, Object> year2019Parameters = parametersMap.get(YEAR_2019);

            Map<Integer, Map<String, Object>> dates = DateCalculator.calculateDateRangeForYear2019(currentStartDate, currentEndDate, businessDate, dowAdjustForDate);
            List<BusinessAnalysisDataDetailsDto> year2019BusinessAnalysisDataDetailsDtos = new ArrayList<>();
            List<BusinessAnalysisDataDetailsDto> dataDetailsDtos;

            for (Map<String, Object> date : dates.values()) {
                year2019Parameters.put(START_DATE, date.get(START_DATE));
                year2019Parameters.put(END_DATE, date.get(END_DATE));
                year2019Parameters.put(BUSINESS_DATE, date.get(BUSINESS_DATE));

                dataDetailsDtos = businessAnalysisDashboardBusinessViewRepository.getYear2019BusinessAnalysisDataDetailsDtos(year2019RowMapper, lyProc, year2019Parameters);
                if (CollectionUtils.isNotEmpty(dataDetailsDtos)) {
                    year2019BusinessAnalysisDataDetailsDtos.addAll(dataDetailsDtos);
                }
            }
            businessAnalysisDashboardService.overlayYear2019BusinessAnalysisDataDetailsDtos(businessAnalysisDataDetailsDtos, year2019BusinessAnalysisDataDetailsDtos);
        }

        return businessAnalysisDataDetailsDtos;
    }

    public String getListOfBusinessGroupIds() {
        List<BusinessGroup> businessGroupList = getBusinessGroups(getMarketSegments());
        return businessGroupList.stream().map(bg -> String.valueOf(bg.getId()))
                .collect(Collectors.joining(","));
    }

    private List<BusinessGroup> getBusinessGroups(List<MktSeg> mktSegList) {
        Set<MktSeg> unassignedMktSeg = new HashSet<>(mktSegList);
        List<BusinessGroup> businessGroups = businessGroupService.getAllBusinessGroupsAssociatedWithMktSeg();
        Set<MktSeg> assignedMS = new HashSet<>();
        businessGroups.forEach(businessGroup -> assignedMS.addAll(
                businessGroup.getMktSegBusinessGroups().stream().map(MktSegBusinessGroup::getMktSeg).collect(Collectors.toSet())));
        unassignedMktSeg.removeAll(assignedMS);
        if (!unassignedMktSeg.isEmpty()) {
            businessGroups.add(createUnassigned(unassignedMktSeg));
        }
        businessGroups.sort(Comparator.comparing(BusinessGroup::getName));
        return businessGroups;
    }

    private List<MktSeg> getMarketSegments() {
        return marketSegmentService.getMktSegByPropertyId();
    }

    private BusinessGroup createUnassigned(Set<MktSeg> unassignedMktSeg) {
        BusinessGroup businessGroup = new BusinessGroup();
        businessGroup.setName(UNASSIGNED);
        businessGroup.setId(UNASSIGNED_ID);
        Set<MktSegBusinessGroup> setOfUniqueMktSegBusinessGrp = unassignedMktSeg.stream().map(mktSeg -> {
            MktSegBusinessGroup mktSegBusinessGroup = new MktSegBusinessGroup();
            mktSegBusinessGroup.setMktSeg(mktSeg);
            return mktSegBusinessGroup;
        }).collect(Collectors.toSet());
        businessGroup.setMktSegBusinessGroups(setOfUniqueMktSegBusinessGrp);
        return businessGroup;
    }

    public void setBusinessAnalysisDashboardService(BusinessAnalysisDashboardService businessAnalysisDashboardService) {
        this.businessAnalysisDashboardService = businessAnalysisDashboardService;
    }

    void setBusinessAnalysisDashboardBusinessViewRepository(BusinessAnalysisDashboardBusinessViewRepository businessAnalysisDashboardBusinessViewRepository) {
        this.businessAnalysisDashboardBusinessViewRepository = businessAnalysisDashboardBusinessViewRepository;
    }

    public void setDateService(DateService dateService) {
        this.dateService = dateService;
    }

}
