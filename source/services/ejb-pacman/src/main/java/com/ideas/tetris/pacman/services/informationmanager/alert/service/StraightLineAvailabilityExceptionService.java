package com.ideas.tetris.pacman.services.informationmanager.alert.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.security.StraightLineAvailabilityUtility;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.informationmanager.dto.ExceptionAlert;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrExcepNotifEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrTypeEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrAlertConfigEntity;
import com.ideas.tetris.pacman.services.informationmanager.exception.service.ExceptionAlertService;
import com.ideas.tetris.pacman.services.straightlineavailability.StraightLineAvailabilityService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.collections.CollectionUtils;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
public class StraightLineAvailabilityExceptionService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;
    @Autowired
	private ExceptionAlertService exceptionAlertService;
    @Autowired
	private PacmanConfigParamsService configParamsService;
    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	private CrudService globalCrudService;

    private static final int MAXLOS = 7;
    private static final int MINLOS = 2;
    private static final int SINGLEROOM = 1;

    private static final String straightLineBaseDataQuery = "select * from\n" +
            "(\n" +
            "\tselect rc.Accom_Class_ID,aa.Occupancy_DT,-1 as  Accom_Type_ID,\n" +
            "\tSUM(aa.Accom_Capacity) + SUM(doa.Overbooking_Decision) - sum(aa.Rooms_Sold) - sum(Rooms_Not_Avail_Maint) - sum(Rooms_Not_Avail_Other) as rooms_Remaining\n" +
            "\t ,rc.accom_class_name from Accom_Activity aa\n" +
            "\tinner join  Accom_Type rt \n" +
            "\ton aa.Accom_Type_ID = rt.Accom_Type_ID and aa.Property_ID = rt.Property_ID\n" +
            "\tinner join accom_class rc \n" +
            "\ton rc.Accom_Class_ID = rt.Accom_Class_ID and rc.Property_ID = rt.Property_ID\n" +
            "\tinner join Decision_Ovrbk_Accom doa \n" +
            "\ton doa.Accom_Type_ID = rt.Accom_Type_ID and doa.Occupancy_DT = aa.Occupancy_DT and aa.Property_ID = doa.Property_ID\n" +
            "\twhere rt.Status_ID = 1 and rc.Status_ID = 1 and rt.Property_ID = :Property_ID and rc.Accom_Class_ID = :Accom_Class_ID and aa.Occupancy_DT > :businessDate\n" +
            "\tgroup by aa.Property_ID,rc.Accom_Class_ID,rc.accom_class_name,aa.Occupancy_DT\n" +
            ") RCLevel\n" +
            "UNION ALL\n" +
            "(\n" +
            "\tselect rc.Accom_Class_ID,  aa.Occupancy_DT, rt.Accom_Type_ID,\n" +
            "\taa.Accom_Capacity + doa.Overbooking_Decision - aa.Rooms_Sold - Rooms_Not_Avail_Maint - Rooms_Not_Avail_Other as rooms_Remaining\n" +
            "\t ,rc.accom_class_name from Accom_Activity aa\n" +
            "\tinner join  Accom_Type rt \n" +
            "\ton aa.Accom_Type_ID = rt.Accom_Type_ID and aa.Property_ID = rt.Property_ID\n" +
            "\tinner join accom_class rc \n" +
            "\ton rc.Accom_Class_ID = rt.Accom_Class_ID and rc.Property_ID = rt.Property_ID\n" +
            "\tinner join Decision_Ovrbk_Accom doa \n" +
            "\ton doa.Accom_Type_ID = rt.Accom_Type_ID and doa.Occupancy_DT = aa.Occupancy_DT and aa.Property_ID = doa.Property_ID\n" +
            "\twhere rt.Status_ID = 1 and rc.Status_ID = 1 and rt.Property_ID = :Property_ID and rc.Accom_Class_ID = :Accom_Class_ID and aa.Occupancy_DT > :businessDate \n" +
            ") \n" +
            "order by Accom_Class_ID,Occupancy_DT , Accom_Type_ID ";

    private static final String straightLineBaseDataCRQuery = "select rc.Accom_Class_ID,  aa.Occupancy_DT, rt.Accom_Type_ID,\n" +
            "\taa.Remaining_Capacity + doa.Overbooking_Decision as rooms_Remaining\n" +
            "\t ,rc.accom_class_name from CR_Accom_Activity aa\n" +
            "\tinner join  Accom_Type rt \n" +
            "\ton aa.Accom_Type_ID = rt.Accom_Type_ID and aa.Property_ID = rt.Property_ID\n" +
            "\tinner join accom_class rc \n" +
            "\ton rc.Accom_Class_ID = rt.Accom_Class_ID and rc.Property_ID = rt.Property_ID\n" +
            "\tinner join Decision_Ovrbk_Accom doa \n" +
            "\ton doa.Accom_Type_ID = rt.Accom_Type_ID and doa.Occupancy_DT = aa.Occupancy_DT and aa.Property_ID = doa.Property_ID\n" +
            "\twhere rt.Status_ID = 1 and rc.Status_ID = 1 and rt.Property_ID = :Property_ID and rc.Accom_Class_ID = :Accom_Class_ID and aa.Occupancy_DT > :businessDate";

    private List<StraightLineAvailabilityBaseDataDTO> getBaseData(Integer accomClassID, String businessDate, Integer propertyId, String query) {
        Map<String, Object> parameters = getParameterMap(accomClassID, businessDate, propertyId);
        List<StraightLineAvailabilityBaseDataDTO> baseData = crudService.findByNativeQuery(query, parameters,
                row -> new StraightLineAvailabilityBaseDataDTO((Integer) row[0], DateUtil.convertJavaUtilDateToLocalDate((Date) row[1], true), (Integer) row[2], (BigDecimal) row[3], row[4].toString()));
        return baseData == null ? new ArrayList<>() : baseData;
    }

    private Map<String, Object> getParameterMap(Integer accomClassID, String businessDate, Integer propertyId) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("Property_ID", propertyId);
        parameters.put("Accom_Class_ID", accomClassID);
        parameters.put("businessDate", businessDate);
        return parameters;
    }

    public List<ExceptionAlert> evaluate(InformationMgrAlertConfigEntity config, String alertType, AccomClass objAccomClass, String businessDate) {
        List<ExceptionAlert> listOfExceptionCreatedUpdated = new ArrayList<>();
        InfoMgrTypeEntity alertTypeEntity = exceptionAlertService.getAlertType(alertType);
        final List<StraightLineAvailabilityBaseDataDTO> baseData = getData(objAccomClass, businessDate);
        final List<StraightLineAvailabilityBaseDataDTO> slabLockers = new StraightLineAvailabilityService(MAXLOS, MINLOS, SINGLEROOM).getSLABlockers(baseData, false);
        Map<Date, InfoMgrExcepNotifEntity> existingExceptions = getAllExistingExceptions(alertTypeEntity, config, getExceptionDates(slabLockers));
        List<InfoMgrExcepNotifEntity> entitiesToBeSaved = new ArrayList<>();
        for (StraightLineAvailabilityBaseDataDTO slabLocker : slabLockers) {
            String details = Constants.IM_LABEL_ROOM_CLASS + Constants.COLON + slabLocker.getAccomClassName() + "," +
                    Constants.IM_LABEL_OCCUPANCY_DATE + Constants.COLON +
                    Constants.IM_DATE_IDENTIFICATION_CODE + Constants.DECISION_FILE_NAME_SEPRATOR +
                    DateUtil.formatDate(slabLocker.getOccupancyDate(), "dd-MMM-yyyy") +
                    Constants.DECISION_FILE_NAME_SEPRATOR +
                    Constants.IM_DATE_IDENTIFICATION_CODE;
            InfoMgrExcepNotifEntity excepNotifEntity = exceptionAlertService.createExceptionAlert(alertTypeEntity, details,
                    AlertType.StraightLineAvailabilityEx, slabLocker.getOccupancyDate(), config, existingExceptions);
            if (Objects.nonNull(excepNotifEntity)) {
                entitiesToBeSaved.add(excepNotifEntity);
            }
        }
        List<InfoMgrExcepNotifEntity> savedEntities = (List<InfoMgrExcepNotifEntity>) crudService.save(entitiesToBeSaved);

        exceptionAlertService.createAndSaveExceptionHistory(listOfExceptionCreatedUpdated, existingExceptions, savedEntities);
        return listOfExceptionCreatedUpdated;
    }

    private List<Date> getExceptionDates(List<StraightLineAvailabilityBaseDataDTO> slabLockers) {
        return slabLockers.stream().map(slabLocker -> slabLocker.getOccupancyDate()).collect(Collectors.toList());
    }

    public Map<Date, InfoMgrExcepNotifEntity> getAllExistingExceptions(InfoMgrTypeEntity alertTypeEntity, InformationMgrAlertConfigEntity config, List<Date> exceptionDates) {
        if (CollectionUtils.isNotEmpty(exceptionDates)) {
            List<InfoMgrExcepNotifEntity> existingExceptions = crudService.findByNamedQuery(InfoMgrExcepNotifEntity.ALL_UNRESOLVED_BY_TYPE_AND_OCCUPANCY_DATES,
                    QueryParameter.with("alertType", alertTypeEntity.getName()).and("occupancyDates", exceptionDates).and("propertyId", config.getPropertyId())
                            .and("notResolved", Constants.ALERT_STATUS_RESOLVED_ID).and("configId", config.getId()).parameters());
            return existingExceptions.stream().collect(Collectors.toMap(InfoMgrExcepNotifEntity::getOccupancyDate, Function.identity(), (o, n) -> o));
        }
        return null;
    }

    List<StraightLineAvailabilityBaseDataDTO> getData(AccomClass objAccomClass, String businessDate) {
        final Integer propertyId = PacmanWorkContextHelper.getPropertyId();

        final List<StraightLineAvailabilityBaseDataDTO> baseData = getBaseData(objAccomClass.getId(), businessDate, propertyId, straightLineBaseDataQuery);
        final List<StraightLineAvailabilityBaseDataDTO> baseDataCR = getCRData(objAccomClass.getId(), businessDate, propertyId);
        StraightLineAvailabilityUtility.overrideCRDataIntoBaseData(baseData, baseDataCR);

        return baseData;
    }

    private List<StraightLineAvailabilityBaseDataDTO> getCRData(Integer accomClassID, String businessDate, Integer propertyId) {
        return isComponentRoomEnabled(propertyId) ? getBaseData(accomClassID, businessDate, propertyId, straightLineBaseDataCRQuery) : null;
    }

    private boolean isComponentRoomEnabled(Integer propertyId) {
        Property property = globalCrudService.find(Property.class, propertyId);
        final String clientCode = property.getClient().getCode();
        final String propertyCode = property.getCode();
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED.value(), clientCode, propertyCode);
    }

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    public void setExceptionAlertService(ExceptionAlertService exceptionAlertService) {
        this.exceptionAlertService = exceptionAlertService;
    }

    public void setConfigParamsService(PacmanConfigParamsService configParamsService) {
        this.configParamsService = configParamsService;
    }

    public void setGlobalCrudService(CrudService globalCrudService) {
        this.globalCrudService = globalCrudService;
    }
}
