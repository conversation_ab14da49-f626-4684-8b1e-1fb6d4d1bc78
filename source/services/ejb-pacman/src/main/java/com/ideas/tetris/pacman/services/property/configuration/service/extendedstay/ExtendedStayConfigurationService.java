package com.ideas.tetris.pacman.services.property.configuration.service.extendedstay;

import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.integration.ratchet.services.RatchetService;
import com.ideas.tetris.pacman.integration.ratchet.services.pcrs.entity.ExtendedStayRateLevelMapping;
import com.ideas.tetris.pacman.integration.ratchet.services.pcrs.entity.RatchetClient;
import com.ideas.tetris.pacman.integration.ratchet.services.pcrs.entity.RatchetProperty;
import com.ideas.tetris.pacman.services.crudservice.RatchetCrudServiceBean;
import com.ideas.tetris.pacman.services.hiltoncpmigration.entity.ExtendedStayRateMapping;
import com.ideas.tetris.pacman.services.hiltoncpmigration.enums.ExtendedStayTier;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.hiltoncpmigration.entity.ExtendedStayRateMapping;
import com.ideas.tetris.pacman.services.hiltoncpmigration.enums.ExtendedStayTier;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationRecordType;
import com.ideas.tetris.pacman.services.property.configuration.dto.impl.ExtendedStayPropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.service.AbstractPropertyConfigurationService;
import com.ideas.tetris.pacman.services.property.configuration.service.PropertyConfigurationRecordFailure;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import org.apache.commons.lang3.tuple.Pair;

import java.util.stream.Stream;
import javax.inject.Inject;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import lombok.extern.slf4j.Slf4j;

import static com.ideas.tetris.pacman.services.purge.PurgeConstants.EMPTY;
import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import static java.util.Collections.emptyList;
import static org.apache.commons.lang3.StringUtils.contains;
import static org.apache.commons.lang3.StringUtils.split;

@Slf4j
@ExtendedStayConfigurationService.Qualifier
@Component
@Transactional
public class ExtendedStayConfigurationService extends AbstractPropertyConfigurationService {

    private static final Date START_DATE = DateUtil.getDateWithoutTime(1, Calendar.JANUARY, 2001);
    private static final Date END_DATE = DateUtil.getDateWithoutTime(31, Calendar.DECEMBER, 2173);

	private static final Date VP_START_DATE = DateUtil.getDateWithoutTime(1, Calendar.JANUARY, 1970);
	private static final Date VP_END_DATE = DateUtil.getDateWithoutTime(1, Calendar.JANUARY, 2073);

	private static final String [] EX_ST_RATES = {"LV0", "LV1", "LV2", "LV3", "LV4", "LV5", "LV6", "LV7", "LV8", "LV9"};
	private static final String EX_ST_RATE_STARTS_WITH = "LV";
	private static final String [] OLD_MKT_SEG = {"", "Tier1", "Tier2", "Tier3", "Tier4"};
	private static final String NON_EXT_MKT_SEG = "BAR";

    @RatchetCrudServiceBean.Qualifier
    @Autowired
    @org.springframework.beans.factory.annotation.Qualifier("ratchetCrudServiceBean")
    protected CrudService ratchetCrudService;


    @Autowired
	PacmanConfigParamsService configParamsServiceLocal;

    @Autowired
    RatchetService ratchetService;

    @Autowired
	protected PropertyService propertyService;

    @Override
    public PropertyConfigurationRecordType getPropertyConfigurationRecordType() {
        return PropertyConfigurationRecordType.EXST;
    }

	@Override
	public List<PropertyConfigurationRecordFailure> doValidate(PropertyConfigurationDto propertyConfigurationDto, Integer propertyId) {
		List<PropertyConfigurationRecordFailure> failures = new ArrayList<PropertyConfigurationRecordFailure>();
		ExtendedStayPropertyConfigurationDto dto = (ExtendedStayPropertyConfigurationDto) propertyConfigurationDto;

		if (dto.getCutOffDate() == null) {
			failures.add(new PropertyConfigurationRecordFailure("cutoff date is required"));
		}

		return failures;
	}

	@Override
	public void doHandle(PropertyConfigurationDto pcd, Integer propertyId) {
		ExtendedStayPropertyConfigurationDto dto = (ExtendedStayPropertyConfigurationDto) pcd;

		Property currentProperty = propertyService.getPropertyById(propertyId);
		String physicalPropertyCode = null;
		if (currentProperty.isVirtualProperty()) {
			physicalPropertyCode = getPhysicalPropertyCode(dto);
			final Property propertyByCode = propertyService.getPropertyByCode(Client.HILTON, physicalPropertyCode);
			if (propertyByCode != null) {
				log.info(String.format("%s is a virtual property and its physical property %s is present in G3 system." +
						" Its existing ExtendedStayRateLevelMappings will be copied to the VP", currentProperty.getCode(), propertyByCode.getCode()));
				return;
			}
		}

		handleRatchetExtendedStayConfiguration(dto, physicalPropertyCode, currentProperty);
		handleTenantExtendedStayConfiguration(dto, propertyId);
	}

	private void handleRatchetExtendedStayConfiguration(ExtendedStayPropertyConfigurationDto dto, String physicalPropertyCode, Property currentProperty) {

		String oldExStStart = StringUtils.substringAfter(dto.getOldExStRateLevel_1(), EX_ST_RATE_STARTS_WITH );
		int oldExtStStartPos = Integer.parseInt(oldExStStart);
		String newExStStart = StringUtils.substringAfter(dto.getNewExStRateLevel_1(), EX_ST_RATE_STARTS_WITH);
		int newExtStStartPos = Integer.parseInt(newExStStart);


		List<ExtendedStayRateLevelMapping> mappings = populateMapping(dto, oldExtStStartPos, newExtStStartPos, physicalPropertyCode, currentProperty.isVirtualProperty());

		ratchetCrudService.save(mappings);

		String context = Constants.CONFIG_PARAMS_NODE_PREFIX + "." + PacmanWorkContextHelper.getClientCode() + "." + dto.getPropertyCode();
		configParamsServiceLocal.updateParameterValue(context, IntegrationConfigParamName.USE_EXTENDED_STAY_MAPPING.value(Constants.RATCHET), "true");
	}

	private void handleTenantExtendedStayConfiguration(ExtendedStayPropertyConfigurationDto dto, Integer propertyId) {

		List<ExtendedStayRateMapping> mappings = Stream.of(
					Pair.of(dto.getNewExStRateLevel_1(), dto.getMktSegment_1()),
					Pair.of(dto.getNewExStRateLevel_2(), dto.getMktSegment_2()),
					Pair.of(dto.getNewExStRateLevel_3(), dto.getMktSegment_3()),
					Pair.of(dto.getNewExStRateLevel_4(), dto.getMktSegment_4())
				)
				.filter(pair -> isNotEmpty(pair.getKey()) && isNotEmpty(pair.getValue()))
				.map(pair -> createExtendedStayRateMapping(propertyId, pair.getKey(), pair.getValue()))
				.collect(Collectors.toList());

		crudService.save(mappings);
	}

	private ExtendedStayRateMapping createExtendedStayRateMapping(Integer propertyId, String rateLevel, String extendedStayTier) {
		ExtendedStayRateMapping extendedStayRateMapping = new ExtendedStayRateMapping();
		extendedStayRateMapping.setPropertyId(propertyId);
		extendedStayRateMapping.setNewRateLevel(rateLevel);
		extendedStayRateMapping.setExtendedStayTier(ExtendedStayTier.valueOf(extendedStayTier));
		extendedStayRateMapping.setMaxLos(0);
		extendedStayRateMapping.setMinLos(0);
		return extendedStayRateMapping;
	}

	private List<ExtendedStayRateLevelMapping> populateMapping(ExtendedStayPropertyConfigurationDto dto,
															   int oldExtStStartPos, int newExtStStartPos,
															   String physicalPropertyCode, boolean isVirtualProperty) {

		// we need to find the ratchet client and property
		RatchetProperty ratchetProperty = findRatchetProperty(dto.getPropertyCode());
		Date cutoffDate = dto.getCutOffDate();

		// 5,6,7 - 3 or 7 - 6 or 6 - 3
		final String physicalPropertyPrefix = isVirtualProperty ? String.format("%s_", physicalPropertyCode) : EMPTY;

		List<ExtendedStayRateLevelMapping> mappings = createDateBoundNonExtendedStayRatesMapping(0, oldExtStStartPos, START_DATE, END_DATE, dto, ratchetProperty, physicalPropertyPrefix); //0, 1, 2

		IntStream.range(oldExtStStartPos, newExtStStartPos).forEach(i -> {
			String rateLevel = EX_ST_RATE_STARTS_WITH + i;
			boolean newContains = newExStRateLevelContains(rateLevel, dto, isVirtualProperty);
			if (!newContains) {
				boolean oldContains = oldExStRateLevelContains(rateLevel, dto, isVirtualProperty);
				mappings.addAll(oldContains
						? createDateBoundNonExtendedStayRatesMapping(i, i + 1, cutoffDate, END_DATE, dto, ratchetProperty, physicalPropertyPrefix) //5,6,7 - 3, 4, 5
						: createDateBoundNonExtendedStayRatesMapping(i, i + 1, START_DATE, END_DATE, dto, ratchetProperty, physicalPropertyPrefix)); //5,6,7 - 3 [4 is nowhere]
			}
		});

		if(isNotEmpty(dto.getOldExStRateLevel_1())) {
			mappings.add(createOldExtendedStayRatesMapping(dto.getOldExStRateLevel_1(), OLD_MKT_SEG[1], dto, ratchetProperty));
		}
		if(isNotEmpty(dto.getOldExStRateLevel_2())) {
			mappings.add(createOldExtendedStayRatesMapping(dto.getOldExStRateLevel_2(), OLD_MKT_SEG[2], dto, ratchetProperty));
		}
		if(isNotEmpty(dto.getOldExStRateLevel_3())) {
			mappings.add(createOldExtendedStayRatesMapping(dto.getOldExStRateLevel_3(), OLD_MKT_SEG[3], dto, ratchetProperty));
		}
		if(isNotEmpty(dto.getOldExStRateLevel_4())) {
			mappings.add(createOldExtendedStayRatesMapping(dto.getOldExStRateLevel_4(), OLD_MKT_SEG[4], dto, ratchetProperty));
		}

		if(isNotEmpty(dto.getNewExStRateLevel_1())) {
			mappings.add(createNewExtendedStayRatesMapping(dto.getNewExStRateLevel_1(), dto.getMktSegment_1(), dto, ratchetProperty));
		}
		if(isNotEmpty(dto.getNewExStRateLevel_2())) {
			mappings.add(createNewExtendedStayRatesMapping(dto.getNewExStRateLevel_2(), dto.getMktSegment_2(), dto, ratchetProperty));
		}
		if(isNotEmpty(dto.getNewExStRateLevel_3())) {
			mappings.add(createNewExtendedStayRatesMapping(dto.getNewExStRateLevel_3(), dto.getMktSegment_3(), dto, ratchetProperty));
		}
		if(isNotEmpty(dto.getNewExStRateLevel_4())) {
			mappings.add(createNewExtendedStayRatesMapping(dto.getNewExStRateLevel_4(), dto.getMktSegment_4(), dto, ratchetProperty));
		}

		return mappings;
	}

	private boolean isNotEmpty(String check) {
		return check != null && !check.isEmpty() && !"Null".equalsIgnoreCase(check.trim());
	}

    private String getPhysicalPropertyCode(ExtendedStayPropertyConfigurationDto dto) {
        if (contains(dto.getOldExStRateLevel_1(), "_")) {
            return split(dto.getOldExStRateLevel_1(), "_")[0];
        }
        if (contains(dto.getOldExStRateLevel_2(), "_")) {
            return split(dto.getOldExStRateLevel_2(), "_")[0];
        }
        if (contains(dto.getOldExStRateLevel_3(), "_")) {
            return split(dto.getOldExStRateLevel_3(), "_")[0];
        }
        if (contains(dto.getOldExStRateLevel_4(), "_")) {
            return split(dto.getOldExStRateLevel_4(), "_")[0];
        }
        throw new TetrisException(ErrorCode.APPLY_CONFIGURATION_FAILED,
                String.format("%s is a virtual property but no physical property code found in configuration file record", dto.getPropertyCode()));
    }

    private boolean oldExStRateLevelContains(String rateLevel, ExtendedStayPropertyConfigurationDto dto, boolean isVirtualProperty) {
        return rateLevel.equals(getOriginalRateLevel(isVirtualProperty, dto.getOldExStRateLevel_1()))
                || rateLevel.equals(getOriginalRateLevel(isVirtualProperty, dto.getOldExStRateLevel_2()))
                || rateLevel.equals(getOriginalRateLevel(isVirtualProperty, dto.getOldExStRateLevel_3()))
                || rateLevel.equals(getOriginalRateLevel(isVirtualProperty, dto.getOldExStRateLevel_4()));
    }

	private String getOriginalRateLevel(boolean isVirtualProperty, String rateLevel) {
		return isVirtualProperty && contains(rateLevel, "_")
				? split(rateLevel, "_")[1]
				: rateLevel;
	}

    private boolean newExStRateLevelContains(String rateLevel, ExtendedStayPropertyConfigurationDto dto, boolean isVirtualProperty) {
        return rateLevel.equals(getOriginalRateLevel(isVirtualProperty, dto.getNewExStRateLevel_1()))
                || rateLevel.equals(getOriginalRateLevel(isVirtualProperty, dto.getNewExStRateLevel_2()))
                || rateLevel.equals(getOriginalRateLevel(isVirtualProperty, dto.getNewExStRateLevel_3()))
                || rateLevel.equals(getOriginalRateLevel(isVirtualProperty, dto.getNewExStRateLevel_4()));
    }

	private List<ExtendedStayRateLevelMapping> createDateBoundNonExtendedStayRatesMapping(int extStStartPos, int extStEndPos, Date startDate,
																						  Date endDate, ExtendedStayPropertyConfigurationDto dto, RatchetProperty ratchetProperty, String prefix) {
		List<ExtendedStayRateLevelMapping> mappings = new ArrayList<>();
		for(int i = extStStartPos; i < extStEndPos; i++) {
			ExtendedStayRateLevelMapping mapping = new ExtendedStayRateLevelMapping();
			mapping.setCreateDate(DateUtil.getCurrentDateWithoutTime());
			mapping.setRateLevel(prefix + EX_ST_RATES[i]);
			mapping.setEsMarketSegment(NON_EXT_MKT_SEG);
			mapping.setIsExtendedStay(Boolean.FALSE.toString());
			mapping.setStartDate(startDate);
			mapping.setEndDate(endDate);
			mapping.setRatchetProperty(ratchetProperty);
			log.info("Creating ExtendedStayRateLevelMapping for Property: " + dto.getPropertyCode() + " and rate level " + EX_ST_RATES[i]);
			mappings.add(mapping);
		}
		return mappings;
	}

	private ExtendedStayRateLevelMapping createOldExtendedStayRatesMapping(String oldExStRateLevel, String mktSegments,
																		   ExtendedStayPropertyConfigurationDto dto, RatchetProperty ratchetProperty) {
		ExtendedStayRateLevelMapping mapping = new ExtendedStayRateLevelMapping();
		mapping.setCreateDate(DateUtil.getCurrentDateWithoutTime());
		mapping.setRateLevel(oldExStRateLevel);
		mapping.setEsMarketSegment(mktSegments);
		mapping.setIsExtendedStay(Boolean.TRUE.toString());
		mapping.setStartDate(START_DATE);
		Date cutoffDate = DateUtil.addDaysToDate(dto.getCutOffDate(), -1);
		mapping.setEndDate(cutoffDate);
		mapping.setRatchetProperty(ratchetProperty);
		log.info("Creating ExtendedStayRateLevelMapping for Property: " + dto.getPropertyCode() + " and rate level " + oldExStRateLevel);
		return mapping;
	}

	private ExtendedStayRateLevelMapping createNewExtendedStayRatesMapping(String newExStRateLevel, String mktSegments,
																		   ExtendedStayPropertyConfigurationDto dto, RatchetProperty ratchetProperty) {
		if (-1 != mktSegments.indexOf(' ')) {
			// DE797 no spaces in market segment (probably warrants more robust validation but this addresses
			// bug and avoids dirty data in db)
			String message = "Invalid extended stay market segment: " + mktSegments;
			log.error(message);
			throw new TetrisException(ErrorCode.ADD_PROPERTY_FAILED, message);
		}

		ExtendedStayRateLevelMapping mapping = new ExtendedStayRateLevelMapping();
		mapping.setCreateDate(DateUtil.getCurrentDateWithoutTime());
		mapping.setRateLevel(newExStRateLevel);
		mapping.setEsMarketSegment(mktSegments);
		mapping.setIsExtendedStay(Boolean.TRUE.toString());
		mapping.setStartDate(dto.getCutOffDate());
		mapping.setEndDate(END_DATE);
		mapping.setRatchetProperty(ratchetProperty);
		log.info("Creating ExtendedStayRateLevelMapping for Property: " + dto.getPropertyCode() + " and rate level " + newExStRateLevel);
		return mapping;
	}
	public List<ExtendedStayRateLevelMapping> getExtendedStayRateLevelMappings(List<String> propertyCodes) {
		if (propertyCodes.isEmpty()) {
			return emptyList();
		}
		return ratchetCrudService.findByNamedQuery(ExtendedStayRateLevelMapping.BY_RATCHET_PROPERTY_CODES,
				QueryParameter.with("propertyCodes", propertyCodes).parameters());
	}


    // if file contains es code and property is existing pp then don't update
    // if file does not contain es code then don't update
    public void saveESMappings(List<ExtendedStayRateLevelMapping> extendedStayRateLevelMappings, List<String> nonExtendedStayProperties, String propertyCode, String clientCode) {
        RatchetProperty ratchetProperty = ratchetService.getProperty(clientCode, propertyCode);
        List<ExtendedStayRateLevelMapping> barExtendedStayRateLevels = createBarRateLevelsFor(nonExtendedStayProperties, ratchetProperty);
        List<ExtendedStayRateLevelMapping> copiedESRateLevels = copyESStayRateLevels(extendedStayRateLevelMappings, ratchetProperty);
        List<ExtendedStayRateLevelMapping> allMappings = mergeESMappings(barExtendedStayRateLevels, copiedESRateLevels);
        ratchetCrudService.save(allMappings);
    }

    private List<ExtendedStayRateLevelMapping> copyESStayRateLevels(List<ExtendedStayRateLevelMapping> extendedStayRateLevelMappings, RatchetProperty ratchetProperty) {
        return extendedStayRateLevelMappings.stream()
                .map(currentMapping -> transformMappings(currentMapping.getRatchetProperty(), ratchetProperty,
                        currentMapping.getRateLevel(), currentMapping.getEsMarketSegment(), currentMapping.getStartDate(), currentMapping.getEndDate(), currentMapping.getIsExtendedStay()))
                .collect(Collectors.toList());
    }

    private List<ExtendedStayRateLevelMapping> mergeESMappings(List<ExtendedStayRateLevelMapping> barExtendedStayRateLevels, List<ExtendedStayRateLevelMapping> newRateLevelMappings) {
        barExtendedStayRateLevels.addAll(newRateLevelMappings);
        return barExtendedStayRateLevels;
    }

    private ExtendedStayRateLevelMapping transformMappings(RatchetProperty currentProperty, RatchetProperty ratchetProperty, String oldRateLevel, String esMarketSegment, Date startDate, Date endDate, String isExtendedStay) {
        ExtendedStayRateLevelMapping esMapping = new ExtendedStayRateLevelMapping();
        esMapping.setRatchetProperty(ratchetProperty);
        esMapping.setRateLevel(getNewRateLevel(currentProperty, oldRateLevel));
        esMapping.setEsMarketSegment(esMarketSegment);
        esMapping.setIsExtendedStay(isExtendedStay);
        esMapping.setCreateDate(DateUtil.getCurrentDateWithoutTime());
        esMapping.setStartDate(startDate);
        esMapping.setEndDate(endDate);
        return esMapping;
    }

    private String getNewRateLevel(RatchetProperty ratchetProperty, String rateLevel) {
        return String.format("%s_%s", ratchetProperty.getCode(), rateLevel);
    }

    private List<ExtendedStayRateLevelMapping> createBarRateLevelsFor(List<String> nonExtendedStayProperties, RatchetProperty ratchetProperty) {
        List<ExtendedStayRateLevelMapping> barMappings = new ArrayList<>();

        for (String propertyCode : nonExtendedStayProperties) {
            // LV0 to LV8
            IntStream.range(0, 9).mapToObj(rateLevel -> transformMappings(createDummyRatchetProperty(propertyCode),
                    ratchetProperty,
                    EX_ST_RATES[rateLevel],
                    NON_EXT_MKT_SEG,
                    VP_START_DATE,
                    VP_END_DATE,
                    Boolean.FALSE.toString())).forEachOrdered(barMappings::add);
        }
        return barMappings;
    }

    private RatchetProperty createDummyRatchetProperty(String propertyCode) {
        RatchetProperty dummyRatchetProperty = new RatchetProperty();
        dummyRatchetProperty.setCode(propertyCode);
        return dummyRatchetProperty;
    }

	RatchetClient findRatchetClient() {
		WorkContextType workContext = PacmanThreadLocalContextHolder.getWorkContext();
		RatchetClient ratchetClient = ratchetCrudService.findByNamedQuerySingleResult(RatchetClient.BY_CLIENT_CODE,
				QueryParameter.with("clientCode", workContext.getClientCode()).parameters());
		if (ratchetClient == null) {
			throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Unable to find ratchet client for client code " + workContext.getClientCode());
		}
		return ratchetClient;
	}

	RatchetProperty findRatchetProperty(String dtoPropertyCode) {
		RatchetClient ratchetClient = findRatchetClient();
		RatchetProperty ratchetProperty = ratchetCrudService.findByNamedQuerySingleResult(
				RatchetProperty.BY_RATCHET_PROPERTY_CODE_AND_CLIENT_ID,
				QueryParameter
						.with("propertyCode", dtoPropertyCode)
						.and("clientId", ratchetClient.getId())
						.parameters());
		if (ratchetProperty == null) {
			throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Unable to find ratchet property for client id " +
					ratchetClient.getId() + " and property code " + dtoPropertyCode);
		}
		return ratchetProperty;
	}


    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface Qualifier {
    }
}
