package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.datafeed.dto.pricingdata.ProductClassificationDTO;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class ProductClassificationService {

    private static final String PRIMARY_PRICED = "Primary Priced";
    private static final String INDEPENDENTLY_PRICED = "Independent";
    private static final String LINKED_PRODUCT = "Linked";

    @Autowired
	private AgileRatesConfigurationService agileRatesConfigurationService;

    @Autowired
	private AgileRatesDataFeedService agileRatesDataFeedService;

    @Autowired
	private PacmanConfigParamsService configParamsService;


    public List<ProductClassificationDTO> getProductClassifications(String datafeedName) {
        if (!isContinuousPricingEnabled()) {
            return Collections.emptyList();
        }
        List<Product> allProducts = agileRatesConfigurationService.findAllProducts();
        List<Product> filteredProducts = allProducts.stream()
                .filter(filterProductBasedOnApplicableProductCode(datafeedName))
                .collect(Collectors.toList());
        return getProductClassificationDTOS(filteredProducts);
    }

    private Predicate<Product> filterProductBasedOnApplicableProductCode(String datafeedName) {
        if (configParamsService.getBooleanParameterValue(PreProductionConfigParamName.IS_DATAFEED_PRODUCT_NAME_POPULATION_ENABLED_VIA_DATABASE)) {
            List<String> productCodes = agileRatesDataFeedService.getApplicableNonSystemDefaultProductCodes(datafeedName);
            return p -> productCodes.contains(p.getProductCode().getProductCodeName());
        }
        return product -> true;
    }

    private List<ProductClassificationDTO> getProductClassificationDTOS(final List<Product> products) {
        if (CollectionUtils.isEmpty(products)) {
            return Collections.emptyList();
        }

        final List<ProductClassificationDTO> productClassificationDTOS = new ArrayList<>(products.size());

        final Optional<Product> primaryProduct = products.stream().filter(Product::isSystemDefault).findFirst();
        primaryProduct.ifPresent(value -> productClassificationDTOS.add(getProductClassificationDTO(value)));

        productClassificationDTOS.addAll(
                products.stream().filter(product -> !product.isSystemDefault())
                        .map(this::getProductClassificationDTO)
                        .collect(Collectors.toList())
        );

        return productClassificationDTOS;
    }

    private ProductClassificationDTO getProductClassificationDTO(final Product product) {
        return new ProductClassificationDTO(product.getName(), getProductClassification(product));
    }

    private String getProductClassification(final Product product) {
        if (product.isSystemDefault()) {
            return PRIMARY_PRICED;
        }
        if (product.isIndependentProduct()) {
            return INDEPENDENTLY_PRICED;
        }
        return LINKED_PRODUCT;
    }

    private boolean isContinuousPricingEnabled() {
        return configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED);
    }
}
