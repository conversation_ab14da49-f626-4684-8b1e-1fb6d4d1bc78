package com.ideas.tetris.pacman.services.functionspace.configuration.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.configautomation.dto.GuestRoomTypeToRMSRoomTypeDTO;
import com.ideas.tetris.pacman.services.configautomation.dto.SalesAndCateringMSToRMSMarketSegmentDTO;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.functionspace.activity.entity.FunctionSpaceForecastOverride;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.*;
import com.ideas.tetris.pacman.services.functionspace.dto.FunctionSpaceLimitsDto;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.service.GroupEvaluationService;
import com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentSummary;
import com.ideas.tetris.pacman.util.Season;
import com.ideas.tetris.pacman.util.SeasonService;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.rest.mapper.RestClient;
import com.ideas.tetris.platform.common.rest.mapper.RestEndpoints;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.hibernate.Hibernate;
import org.joda.time.LocalDate;
import org.joda.time.LocalTime;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.apache.commons.collections.CollectionUtils.isEmpty;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;

@Component
@Transactional
public class FunctionSpaceConfigurationService {
    private static final String PROPERTY_ID = "propertyId";
    private static final String OCCUPANCY_DATE = "occupancyDate";
    private static final String DAY_PART = "dayPart";
    private static final String ABBREVIATION = "abbreviation";

    @TenantCrudServiceBean.Qualifier
	@Qualifier("tenantCrudServiceBean")
    @Autowired
	private CrudService tenantCrudService;

    @Autowired
    SyncEventAggregatorService syncEventAggregatorService;

    @Autowired
	private SeasonService seasonService;

    @Autowired
	private DateService dateService;

    @Autowired
	private AccommodationService accommodationService;

    @Autowired
	private PacmanConfigParamsService pacmanConfigParamsService;

    @Autowired
	private GroupEvaluationService groupEvaluationService;

    @Autowired
    AbstractMultiPropertyCrudService multiPropertyCrudService;

    private static final String AHWS_CLIENT_CODE_NAME = "ahwsClientCode";
    private static final String AHWS_PROPERTY_CODE_NAME = "ahwsPropertyCode";

    private static final Logger LOGGER = Logger.getLogger(FunctionSpaceConfigurationService.class);

    private RestClient restClient;

    @Autowired
    public void setRestClient(RestClient restClient) {
        this.restClient = restClient;
    }
    public void saveDayParts(List<FunctionSpaceDayPart> dayParts) {
        for (FunctionSpaceDayPart dayPart : dayParts) {
            if (dayPart.isDelete()) {
                dayPart.setStatus(Status.INACTIVE);
            } else {
                if (dayPart.getPropertyId() == null) {
                    dayPart.setPropertyId(PacmanWorkContextHelper.getPropertyId());
                    dayPart.setStatus(Status.ACTIVE);
                }
                setDayPartAssociation(dayPart);
            }

            saveDayPart(dayPart);
        }

        sync();
    }

    @SuppressWarnings("unchecked")
    public List<FunctionSpaceDayPart> getDayParts() {
        return tenantCrudService.findByNamedQuery(
                FunctionSpaceDayPart.FIND_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @SuppressWarnings("unchecked")
    public List<FunctionSpaceCombinationFunctionRoom> getCombinationFunctionRooms() {
        return tenantCrudService.findByNamedQuery(
                FunctionSpaceCombinationFunctionRoom.FIND_COMBINATION_ROOMS_BY_PROPERTY,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @SuppressWarnings("unchecked")
    public List<FunctionSpaceFunctionRoom> getIndivisibleFunctionRooms() {
        List<FunctionSpaceFunctionRoom> rooms = tenantCrudService.findByNamedQuery(
                FunctionSpaceFunctionRoom.FIND_NON_COMBINATION_ROOMS_BY_PROPERTY,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());

        // initialize any of the mar seasons because if we don't, ui will bomb
        // when trying to fetch them
        if (rooms != null) {
            for (FunctionSpaceFunctionRoom room : rooms) {
                room.getFunctionSpaceFunctionRoomMARSeasons().size();
            }
        }

        return rooms;
    }

    public void saveFunctionRooms(List<FunctionSpaceFunctionRoom> functionRooms) {
        tenantCrudService.save(functionRooms);
        updateCombinationRoomForPricing();
        sync();
    }

    @SuppressWarnings("unchecked")
    public List<FunctionSpaceEventType> getFunctionSpaceOutOfServiceEventTypes() {
        return tenantCrudService.findByNamedQuery(
                FunctionSpaceEventType.FIND_BY_PROPERTY,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public void saveOutOfServiceEventTypes(List<FunctionSpaceEventType> functionSpaceOutOfServiceEventTypes) {
        tenantCrudService.save(functionSpaceOutOfServiceEventTypes);
        sync();
    }

    public void saveForecestLevel(FunctionSpaceForecastLevel forecastLevel) {
        if (forecastLevel.getPropertyId() == null) {
            forecastLevel.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        }
        tenantCrudService.save(forecastLevel);
    }

    public FunctionSpaceForecastLevel getForecastLevel() {
        return (FunctionSpaceForecastLevel) tenantCrudService.findByNamedQuerySingleResult(
                FunctionSpaceForecastLevel.FIND_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public void saveMarketSegment(FunctionSpaceMarketSegment marketSegment) {
        if (marketSegment.getPropertyId() == null) {
            marketSegment.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        }
        tenantCrudService.save(marketSegment);
    }

    public void saveMarketSegments(List<FunctionSpaceMarketSegment> marketSegments) {
        for (FunctionSpaceMarketSegment marketSegment : marketSegments) {
            saveMarketSegment(marketSegment);
        }
        sync();
    }

    public void clearMarketSegmentMapping() {
        for (FunctionSpaceMarketSegment functionSpaceMarketSegment : getMarketSegments()) {
            functionSpaceMarketSegment.setMarketSegmentId(null);
            tenantCrudService.save(functionSpaceMarketSegment);
        }
    }

    @SuppressWarnings("unchecked")
    public List<FunctionSpaceMarketSegment> getMarketSegments() {
        return tenantCrudService.findByNamedQuery(
                FunctionSpaceMarketSegment.FIND_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    /**
     * This will create market segment mapping using G3 RMS market segment with same market segment name for both Sales & Catering and G3 RMS.
     * It will also update the existing unmapped market segment if the market segment name is same as G3 RMS market segment.
     */
    public List<String> syncMarketSegmentMapping() {
        return saveFunctionSpaceMarketSegments(prepareFunctionSpaceMarketSegmentsUsingGroupMarketSegments());
    }

    private List<String> saveFunctionSpaceMarketSegments(List<FunctionSpaceMarketSegment> functionSpaceMarketSegments) {
        List<String> savedMarketSegmentNames = new ArrayList<>();
        if (isNotEmpty(functionSpaceMarketSegments)) {
            final Collection<FunctionSpaceMarketSegment> marketSegments = tenantCrudService.save(functionSpaceMarketSegments);
            savedMarketSegmentNames = getMarketSegmentNames(marketSegments);
            sync();
        }
        return savedMarketSegmentNames;
    }

    private List<String> getMarketSegmentNames(Collection<FunctionSpaceMarketSegment> marketSegments) {
        return marketSegments.stream()
                .map(FunctionSpaceMarketSegment::getName)
                .collect(Collectors.toList());
    }

    private List<FunctionSpaceMarketSegment> prepareFunctionSpaceMarketSegmentsUsingGroupMarketSegments() {
        List<FunctionSpaceMarketSegment> functionSpaceMarketSegments = new ArrayList<>();
        final Map<String, FunctionSpaceMarketSegment> functionSpaceMarketSegmentsByName = getFunctionSpaceMarketSegmentsByName(getMarketSegments());
        final List<MarketSegmentSummary> marketSegmentsForGroupForecastType = groupEvaluationService.getMarketSegmentsForGroupForecastType();

        marketSegmentsForGroupForecastType.forEach(marketSegmentSummary -> {
            FunctionSpaceMarketSegment functionSpaceMarketSegment = prepareFunctionSpaceMarketSegment(functionSpaceMarketSegmentsByName, marketSegmentSummary);
            if (nonNull(functionSpaceMarketSegment)) {
                functionSpaceMarketSegments.add(functionSpaceMarketSegment);
            }
        });
        return functionSpaceMarketSegments;
    }

    private Map<String, FunctionSpaceMarketSegment> getFunctionSpaceMarketSegmentsByName(List<FunctionSpaceMarketSegment> marketSegments) {
        return marketSegments.stream()
                .collect(Collectors.toMap(FunctionSpaceMarketSegment::getName, Function.identity()));
    }

    private FunctionSpaceMarketSegment prepareFunctionSpaceMarketSegment(Map<String, FunctionSpaceMarketSegment> functionSpaceMarketSegmentByNameMap, MarketSegmentSummary marketSegmentSummary) {
        FunctionSpaceMarketSegment functionSpaceMarketSegment = null;
        FunctionSpaceMarketSegment existingFunctionSpaceMarketSegment = functionSpaceMarketSegmentByNameMap.getOrDefault(marketSegmentSummary.getName(), null);
        if (isNull(existingFunctionSpaceMarketSegment)) {
            functionSpaceMarketSegment = buildFunctionSpaceMarketSegment(marketSegmentSummary);
        } else if (unMappedMarketSegment(existingFunctionSpaceMarketSegment)) {
            functionSpaceMarketSegment = existingFunctionSpaceMarketSegment;
            functionSpaceMarketSegment.setMarketSegmentId(marketSegmentSummary.getId());

        }
        return functionSpaceMarketSegment;
    }

    private boolean unMappedMarketSegment(FunctionSpaceMarketSegment existingFunctionSpaceMarketSegment) {
        return isNull(existingFunctionSpaceMarketSegment.getMarketSegmentId());
    }

    private FunctionSpaceMarketSegment buildFunctionSpaceMarketSegment(MarketSegmentSummary marketSegmentSummary) {
        FunctionSpaceMarketSegment functionSpaceMarketSegment = new FunctionSpaceMarketSegment();
        functionSpaceMarketSegment.setName(marketSegmentSummary.getName());
        functionSpaceMarketSegment.setAbbreviation(marketSegmentSummary.getName());
        functionSpaceMarketSegment.setMarketSegmentId(marketSegmentSummary.getId());
        functionSpaceMarketSegment.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        return functionSpaceMarketSegment;
    }

    @SuppressWarnings("unchecked")
    public List<FunctionSpaceGuestRoomCategory> getGuestRoomCategories() {
        return tenantCrudService.findByNamedQuery(
                FunctionSpaceGuestRoomCategory.FIND_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public void saveGuestRoomCategory(FunctionSpaceGuestRoomCategory guestRoomCategory) {
        if (guestRoomCategory.getPropertyId() == null) {
            guestRoomCategory.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        }
        tenantCrudService.save(guestRoomCategory);
    }

    public void saveGuestRoomCategories(List<FunctionSpaceGuestRoomCategory> guestRoomCategories) {
        for (FunctionSpaceGuestRoomCategory guestRoomCategory : guestRoomCategories) {
            saveGuestRoomCategory(guestRoomCategory);
        }
        sync();
    }

    /**
     * This will create Guest Room Type mapping using G3 RMS Room Types with same room type name for Sales & Catering and G3 RMS.
     * It will also update the existing unmapped Guest Room Type if the room type name is same as G3 RMS room type.
     */
    public List<String> syncGuestRoomTypeMapping() {
        return saveFunctionSpaceGuestRoomCategories(prepareGuestRoomCategoriesUsingAccomTypes());
    }

    private List<String> saveFunctionSpaceGuestRoomCategories(List<FunctionSpaceGuestRoomCategory> guestRoomCategoriesToSave) {
        List<String> savedGuestRoomCategories = new ArrayList<>();
        if (isNotEmpty(guestRoomCategoriesToSave)) {
            final Collection<FunctionSpaceGuestRoomCategory> functionSpaceGuestRoomCategories = tenantCrudService.save(guestRoomCategoriesToSave);
            savedGuestRoomCategories = getGuestRoomCategories(functionSpaceGuestRoomCategories);
            sync();
        }
        return savedGuestRoomCategories;
    }

    private List<String> getGuestRoomCategories(Collection<FunctionSpaceGuestRoomCategory> functionSpaceGuestRoomCategories) {
        return functionSpaceGuestRoomCategories.stream()
                .map(FunctionSpaceGuestRoomCategory::getRoomCategory)
                .collect(Collectors.toList());
    }

    private List<FunctionSpaceGuestRoomCategory> prepareGuestRoomCategoriesUsingAccomTypes() {
        List<FunctionSpaceGuestRoomCategory> guestRoomCategoriesToSave = new ArrayList<>();
        final Map<String, FunctionSpaceGuestRoomCategory> guestRoomCategoriesByRoomCategory = getGuestRoomCategoriesByRoomCategory(getGuestRoomCategories());
        getAssignedAccomTypes().forEach(accomType -> {
            FunctionSpaceGuestRoomCategory functionSpaceGuestRoomCategory = prepareGetGuestRoomCategory(guestRoomCategoriesByRoomCategory, accomType, accomType.getName());
            if (nonNull(functionSpaceGuestRoomCategory)) {
                guestRoomCategoriesToSave.add(functionSpaceGuestRoomCategory);
            }
        });
        return guestRoomCategoriesToSave;
    }

    private FunctionSpaceGuestRoomCategory prepareGetGuestRoomCategory(Map<String, FunctionSpaceGuestRoomCategory> guestRoomCategoriesByRoomCategory, AccomType accomType, String name) {
        FunctionSpaceGuestRoomCategory functionSpaceGuestRoomCategory = null;
        final FunctionSpaceGuestRoomCategory guestRoomCategory = guestRoomCategoriesByRoomCategory.getOrDefault(name, null);
        if (isNull(guestRoomCategory)) {
            functionSpaceGuestRoomCategory = buildGuestRoomCategory(accomType, name);
        } else if (unMappedGuestRoomCategory(guestRoomCategory)) {
            functionSpaceGuestRoomCategory = guestRoomCategory;
            functionSpaceGuestRoomCategory.setAccomTypeId(accomType.getId());
        }
        return functionSpaceGuestRoomCategory;
    }

    private boolean unMappedGuestRoomCategory(FunctionSpaceGuestRoomCategory guestRoomCategory) {
        return isNull(guestRoomCategory.getAccomTypeId());
    }

    private Map<String, FunctionSpaceGuestRoomCategory> getGuestRoomCategoriesByRoomCategory(List<FunctionSpaceGuestRoomCategory> guestRoomCategories) {
        return guestRoomCategories.stream()
                .collect(Collectors.toMap(FunctionSpaceGuestRoomCategory::getRoomCategory, Function.identity()));
    }

    private FunctionSpaceGuestRoomCategory buildGuestRoomCategory(AccomType accomType, String name) {
        FunctionSpaceGuestRoomCategory guestRoomCategory = new FunctionSpaceGuestRoomCategory();
        guestRoomCategory.setAccomTypeId(accomType.getId());
        guestRoomCategory.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        guestRoomCategory.setRoomCategory(name);
        return guestRoomCategory;
    }

    public List<AccomType> getAssignedAccomTypes() {
        List<AccomType> accomTypes = accommodationService.getAllAssignedAccomTypes();
        if (roomTypeRecodingUIEnabled()) {
            accomTypes = filterByDisplayStatusAsActive(accomTypes);
        }
        return accomTypes;
    }

    private List<AccomType> filterByDisplayStatusAsActive(List<AccomType> accomTypes) {
        return accomTypes.stream()
                .filter(accomType -> Objects.equals(accomType.getDisplayStatusId(), Status.ACTIVE.getId()))
                .collect(Collectors.toList());
    }

    private boolean roomTypeRecodingUIEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ROOM_TYPE_RECODING_UI_ENABLED);
    }

    @SuppressWarnings("unchecked")
    public List<FunctionSpaceRevenueGroup> getActiveRevenueGroups() {
        List<FunctionSpaceRevenueGroup> groups = findActiveFunctionSpaceRevenueGroupsByPropertyId(PacmanWorkContextHelper.getPropertyId());
        // initialize so the UI doesn't bomb when trying to access resource type
        if (groups != null) {
            for (FunctionSpaceRevenueGroup group : groups) {
                Hibernate.initialize(group.getResourceType());
            }
        }

        return groups;
    }

    public List<FunctionSpaceRevenueGroup> findActiveFunctionSpaceRevenueGroupsByPropertyId(Integer propertyId) {
        return tenantCrudService.findByNamedQuery(FunctionSpaceRevenueGroup.FIND_ACTIVE_REVENUE_GROUPS_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, propertyId).parameters());
    }

    public List<FunctionSpaceRevenueGroup> getActiveRevenueGroupsByProperty(Integer propertyId) {
        List<FunctionSpaceRevenueGroup> groups = multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId,
                FunctionSpaceRevenueGroup.FIND_ACTIVE_REVENUE_GROUPS_BY_PROPERTY_ID_WITH_RESOURCE_TYPE,
                QueryParameter.with("propertyId", propertyId).parameters());

        // initialize so the UI doesn't bomb when trying to access resource type
        if (groups != null) {
            for (FunctionSpaceRevenueGroup group : groups) {
                Hibernate.initialize(group.getResourceType());
            }
        }
        return groups;
    }

    public FunctionSpaceRevenueGroup findRevenueGroupByAbbreviation(String abbreviation) {
        return tenantCrudService.findByNamedQuerySingleResult(FunctionSpaceRevenueGroup.FIND_ACTIVE_REVENUE_GROUP_BY_ABBREVIATION_WITH_RESOURCE_TYPE,
                        QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                                .and(ABBREVIATION, abbreviation).parameters());
    }

    public void saveRevenueGroup(FunctionSpaceRevenueGroup revenueGroup) {
        tenantCrudService.save(revenueGroup);
    }

    public void saveRevenueGroups(List<FunctionSpaceRevenueGroup> revenueGroups) {
        for (FunctionSpaceRevenueGroup revenueGroup : revenueGroups) {
            saveRevenueGroup(revenueGroup);
        }

        sync();
    }

    @SuppressWarnings("unchecked")
    public List<FunctionSpaceFunctionRoom> getAllActiveRoomsIncludedForPricing() {
        return tenantCrudService.findByNamedQuery(FunctionSpaceFunctionRoom.FIND_ALL_ACTIVE_ROOMS_INCLUDED_FOR_PRICING,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public List<FunctionSpaceFunctionRoom> getAllTbaAndNonActiveRoomsIncludedForPricing() {
        return tenantCrudService.findByNamedQuery(FunctionSpaceFunctionRoom.FIND_ALL_TBA_AND_NOT_INCLUDED_ROOMS_FOR_PRICING,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> getTotalSqFeetForFunctionRoomsPerPriceTier() {
        Map<FunctionSpaceFunctionRoomPriceTier, BigDecimal> tierMap = new HashMap<FunctionSpaceFunctionRoomPriceTier, BigDecimal>();

        List<FunctionSpaceFunctionRoom> allRooms = getAllActiveRoomsIncludedForPricing();
        BigDecimal sqFeetPerTier = BigDecimal.ZERO;

        for (FunctionSpaceFunctionRoom room : allRooms) {
            // only get indivisible rooms
            if (!room.isCombo()) {
                if (tierMap.containsKey(room.getFunctionSpaceFunctionRoomPriceTier())) {
                    sqFeetPerTier = room.getAreaSqFeet().add(
                            (tierMap.get(room.getFunctionSpaceFunctionRoomPriceTier())));
                    tierMap.put(room.getFunctionSpaceFunctionRoomPriceTier(), sqFeetPerTier);
                } else {
                    tierMap.put(room.getFunctionSpaceFunctionRoomPriceTier(), room.getAreaSqFeet());
                }
            }
        }

        return tierMap;
    }

    public FunctionSpaceDayPart findDayPartById(Integer dayPartId) {
        return tenantCrudService.find(FunctionSpaceDayPart.class, dayPartId);
    }

    public FunctionSpaceFunctionRoom findFunctionRoomById(Integer functionRoomId) {
        return tenantCrudService.find(FunctionSpaceFunctionRoom.class, functionRoomId);
    }

    public BigDecimal getTotalDayPartHoursAvailablePerDay() {
        BigDecimal hoursAvailable = BigDecimal.ZERO;
        List<FunctionSpaceDayPart> dayParts = getDayParts();

        for (FunctionSpaceDayPart dayPart : dayParts) {
            if (dayPart.isIncluded()) {
                hoursAvailable = hoursAvailable.add(new BigDecimal(dayPart.getTotalMinutesForDayPart()));
            }
        }

        return hoursAvailable.divide(new BigDecimal(60).setScale(2, RoundingMode.HALF_UP));
    }

    public List<FunctionSpaceDayPart> getAllIncludedDayParts() {
        List<FunctionSpaceDayPart> includedDayParts = new ArrayList<FunctionSpaceDayPart>();

        for (FunctionSpaceDayPart dayPart : getDayParts()) {
            if (dayPart.isIncluded()) {
                includedDayParts.add(dayPart);
            }
        }

        return includedDayParts;
    }

    private void setDayPartAssociation(FunctionSpaceDayPart dayPart) {
        LocalTime thresholdTime = new LocalTime(16, 0, 0);
        if (dayPart.getBeginTime().isBefore(thresholdTime)) {
            dayPart.setAssociation(-1);
        } else {
            dayPart.setAssociation(0);
        }
    }

    public boolean willSplitOccur(List<FunctionSpaceFunctionRoomMARSeason> seasons,
                                  FunctionSpaceFunctionRoomMARSeason changedSeason) {
        return seasonService.willSplitOccur(seasons, changedSeason);
    }

    public void applySplit(List<FunctionSpaceFunctionRoomMARSeason> seasons,
                           FunctionSpaceFunctionRoomMARSeason changedSeason) {
        List<? extends Season> splitSeasons = seasonService.applySplit(seasons, changedSeason, new LocalDate(
                dateService.getCaughtUpDate()));
        for (Season splitSeason : splitSeasons) {
            clearOutOfRangeDayOfWeekValues((FunctionSpaceFunctionRoomMARSeason) splitSeason);
            clearOutOfRangeDayOfWeekValuesForRoomRentalMAR((FunctionSpaceFunctionRoomMARSeason) splitSeason);

        }
    }

    protected void clearOutOfRangeDayOfWeekValues(FunctionSpaceFunctionRoomMARSeason season) {
        if (season.getStartDate() == null || season.getEndDate() == null) {
            return;
        }
        List<DayOfWeek> outOfRangeDayOfWeeks = LocalDateUtils.getOutOfRangeDayOfWeeks(season.getStartDate(),
                season.getEndDate());
        for (DayOfWeek outOfRangeDayOfWeek : outOfRangeDayOfWeeks) {
            switch (outOfRangeDayOfWeek) {
                case SUNDAY:
                    season.setSundayMAR(null);
                    break;
                case MONDAY:
                    season.setMondayMAR(null);
                    break;
                case TUESDAY:
                    season.setTuesdayMAR(null);
                    break;
                case WEDNESDAY:
                    season.setWednesdayMAR(null);
                    break;
                case THURSDAY:
                    season.setThursdayMAR(null);
                    break;
                case FRIDAY:
                    season.setFridayMAR(null);
                    break;
                case SATURDAY:
                    season.setSaturdayMAR(null);
                    break;
            }
        }
    }

    protected void clearOutOfRangeDayOfWeekValuesForRoomRentalMAR(FunctionSpaceFunctionRoomMARSeason season) {
        if (season.getStartDate() == null || season.getEndDate() == null) {
            return;
        }
        List<DayOfWeek> outOfRangeDayOfWeeks = LocalDateUtils.getOutOfRangeDayOfWeeks(season.getStartDate(),
                season.getEndDate());
        for (DayOfWeek outOfRangeDayOfWeek : outOfRangeDayOfWeeks) {
            switch (outOfRangeDayOfWeek) {
                case SUNDAY:
                    season.getFunctionSpaceFunctionRoomRentalMARSeasonDetails().setSundayRoomRentalMAR(null);
                    break;
                case MONDAY:
                    season.getFunctionSpaceFunctionRoomRentalMARSeasonDetails().setMondayRoomRentalMAR(null);
                    break;
                case TUESDAY:
                    season.getFunctionSpaceFunctionRoomRentalMARSeasonDetails().setTuesdayRoomRentalMAR(null);
                    break;
                case WEDNESDAY:
                    season.getFunctionSpaceFunctionRoomRentalMARSeasonDetails().setWednesdayRoomRentalMAR(null);
                    break;
                case THURSDAY:
                    season.getFunctionSpaceFunctionRoomRentalMARSeasonDetails().setThursdayRoomRentalMAR(null);
                    break;
                case FRIDAY:
                    season.getFunctionSpaceFunctionRoomRentalMARSeasonDetails().setFridayRoomRentalMAR(null);
                    break;
                case SATURDAY:
                    season.getFunctionSpaceFunctionRoomRentalMARSeasonDetails().setSaturdayRoomRentalMAR(null);
                    break;
            }
        }
    }

    public void removeAndJoin(List<FunctionSpaceFunctionRoomMARSeason> marSeasons,
                              FunctionSpaceFunctionRoomMARSeason marSeason) {
        seasonService.removeAndJoin(marSeasons, marSeason, new LocalDate(dateService.getCaughtUpDate()));
    }

    public Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> getMARByPriceTierMap(
            List<FunctionSpaceFunctionRoom> allRooms, LocalDate startDate) {
        Map<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>> marByPriceTier = new HashMap<FunctionSpaceFunctionRoomPriceTier, Map<DayOfWeek, FunctionSpaceLimitsDto>>();

        for (FunctionSpaceFunctionRoomPriceTier priceTier : FunctionSpaceFunctionRoomPriceTier.values()) {
            Map<DayOfWeek, FunctionSpaceLimitsDto> map = buildMapOfMAR(getRoomsAtPriceTier(priceTier, allRooms),
                    startDate);
            marByPriceTier.put(priceTier, map);
        }

        return marByPriceTier;
    }

    public FunctionSpaceResourceType findFunctionSpaceResourceType(String resourceType) {
        return (FunctionSpaceResourceType) tenantCrudService.findByNamedQuerySingleResult(
                FunctionSpaceResourceType.FIND_BY_NAME,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("name", resourceType)
                        .parameters());
    }

    public List<FunctionSpaceFunctionRoom> getIncludedForPricingIndivisibleRooms() {
        return getAllActiveRoomsIncludedForPricing().stream()
                .filter(room -> !room.isCombo())
                .collect(Collectors.toList());
    }

    public int removeDisplayRevenueAsMappingFor(FunctionSpaceFunctionRoom selectedRoom) {
        List<FunctionSpaceFunctionRoom> roomsToUpdate = fetchPreFunctionRoomsMappedTo(selectedRoom).stream()
                .filter(room -> room.getDisplayRevenueAs().equals(selectedRoom.getId()))
                .collect(Collectors.toList());

        roomsToUpdate.forEach(functionSpaceFunctionRoom -> functionSpaceFunctionRoom.setDisplayRevenueAs(null));
        tenantCrudService.save(roomsToUpdate);
        return roomsToUpdate.size();
    }

    public void updateDisplayRevenueAsFor(FunctionSpaceFunctionRoom selectedRoom) {
        Integer roomToUpdate = selectedRoom.getId();
        Integer displayRevenueAs = selectedRoom.getDisplayRevenueAs();
        tenantCrudService.executeUpdateByNamedQuery(FunctionSpaceFunctionRoom.UPDATE_DISPLAY_REVENUE_AS_BY_ROOM_ID,
                QueryParameter.with("displayRevenueAs", displayRevenueAs).and("funcRoomID", roomToUpdate).parameters());
    }

    private List<FunctionSpaceFunctionRoom> fetchPreFunctionRoomsMappedTo(FunctionSpaceFunctionRoom selectedRoom) {
        return tenantCrudService.findAll(FunctionSpaceFunctionRoom.class).stream()
                .filter(room -> room.getDisplayRevenueAs() != null)
                .filter(room -> room.getDisplayRevenueAs().equals(selectedRoom.getId()))
                .collect(Collectors.toList());
    }

    protected void updateCombinationRoomForPricing() {
        List<FunctionSpaceCombinationFunctionRoom> comboRooms = getCombinationFunctionRooms();
        List<FunctionSpaceFunctionRoom> allComboRoomParts = new ArrayList<FunctionSpaceFunctionRoom>();

        for (FunctionSpaceCombinationFunctionRoom comboRoom : comboRooms) {
            allComboRoomParts.addAll(comboRoom.getIndivisibleFunctionRoomParts());
            boolean hasAtLeastOneRoomWithPricingSet = false;

            for (FunctionSpaceFunctionRoom updatedIndivsableRoom : allComboRoomParts) {
                if (allComboRoomParts.contains(updatedIndivsableRoom) && updatedIndivsableRoom.isIncludeForPricing()) {
                    hasAtLeastOneRoomWithPricingSet = true;
                    break;
                }
            }

            comboRoom.setIncludeForPricing(hasAtLeastOneRoomWithPricingSet);
            allComboRoomParts.clear();
        }

        tenantCrudService.save(comboRooms);
    }

    protected Map<DayOfWeek, FunctionSpaceLimitsDto> buildMapOfMAR(List<FunctionSpaceFunctionRoom> roomsAtPriceTier,
                                                                   LocalDate startDate) {
        Map<DayOfWeek, FunctionSpaceLimitsDto> dowMap = new HashMap<DayOfWeek, FunctionSpaceLimitsDto>();
        FunctionSpaceLimitsDto dto;

        for (DayOfWeek dow : DayOfWeek.values()) {
            BigDecimal marValueForDOW = null;
            BigDecimal maxValueForDOW = BigDecimal.ZERO;

            for (FunctionSpaceFunctionRoom room : roomsAtPriceTier) {
                BigDecimal roomMARValue = room.getMARWithSeasonalAdjustment(startDate, dow);
                BigDecimal roomMaxValue = room.getUpperLimitForDayOfWeek(dow);

                if (marValueForDOW == null) {
                    marValueForDOW = room.getMARWithSeasonalAdjustment(startDate, dow);
                }

                if (roomMARValue.compareTo(marValueForDOW) == -1) {
                    marValueForDOW = roomMARValue;
                }

                if (roomMaxValue.compareTo(maxValueForDOW) == 1) {
                    maxValueForDOW = roomMaxValue;
                }
            }

            dto = new FunctionSpaceLimitsDto(dow, marValueForDOW, maxValueForDOW);
            dowMap.put(dow, dto);
        }

        return dowMap;
    }

    private List<FunctionSpaceFunctionRoom> getRoomsAtPriceTier(FunctionSpaceFunctionRoomPriceTier priceTier,
                                                                List<FunctionSpaceFunctionRoom> allRooms) {
        List<FunctionSpaceFunctionRoom> roomsAtPriceTier = new ArrayList<FunctionSpaceFunctionRoom>();

        for (FunctionSpaceFunctionRoom room : allRooms) {
            if (!room.isCombo()) {
                if (room.getFunctionSpaceFunctionRoomPriceTier().equals(priceTier)) {
                    roomsAtPriceTier.add(room);
                }
            }
        }

        return roomsAtPriceTier;
    }

    private void saveDayPart(FunctionSpaceDayPart dayPart) {
        tenantCrudService.save(dayPart);

        if (dayPart.isDelete() || dayPart.hasBeenExcluded()) {
            deleteForecastOverrideWhenDayPartHasBeenDeleted(dayPart);
        }
    }

    private void sync() {
        syncEventAggregatorService.registerSyncEvent(SyncEvent.FUNCTION_SPACE_CONFIG_CHANGED);
    }

    protected void deleteForecastOverrideWhenDayPartHasBeenDeleted(FunctionSpaceDayPart dayPart) {
        List<FunctionSpaceForecastOverride> forecastOverrides = findAllForecastOverridesForDayPart(dayPart);

        for (FunctionSpaceForecastOverride forecastOverride : forecastOverrides) {
            forecastOverride.setStatus(Status.INACTIVE);
            tenantCrudService.save(forecastOverride);
        }
    }

    @SuppressWarnings("unchecked")
    protected List<FunctionSpaceForecastOverride> findAllForecastOverridesForDayPart(FunctionSpaceDayPart dayPart) {
        Map<String, Object> overrideParams = new HashMap<String, Object>();
        overrideParams.put(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
        overrideParams.put(OCCUPANCY_DATE, new LocalDate(dateService.getCaughtUpDate()));
        overrideParams.put(DAY_PART, dayPart);

        return tenantCrudService.findByNamedQuery(FunctionSpaceForecastOverride.FIND_ACTIVE_OVERRIDES_BY_DAY_PART,
                overrideParams);
    }

    public List<FunctionSpaceStatus> getFunctionSpaceStatuses() {
        return tenantCrudService.findByNamedQuery(FunctionSpaceStatus.FIND_BY_PROPERTY_AND_ORDER_BY_STATUS,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public List<FunctionSpaceStatus> getFunctionSpaceStatusesByProperty() {
        return tenantCrudService.findByNamedQuery(FunctionSpaceStatus.FIND_BY_PROPERTY,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public void saveFunctionSpaceStatuses(List<FunctionSpaceStatus> dto) {
        tenantCrudService.save(dto);
    }

    public List<SalesAndCateringMSToRMSMarketSegmentDTO> getSalesAndCateringMSToRMSMarketSegmentMapping(Map<Integer, String> mktSegmentsById) {
        List<FunctionSpaceMarketSegment> salesAndCateringMS = getMarketSegments();
        return salesAndCateringMS.stream()
                .map(salesAndCatering -> mapToSalesAndCateringMSToRMSMarketSegmentDTO(salesAndCatering, mktSegmentsById))
                .collect(Collectors.toList());
    }

    private SalesAndCateringMSToRMSMarketSegmentDTO mapToSalesAndCateringMSToRMSMarketSegmentDTO(FunctionSpaceMarketSegment salesAndCateringMS,
                                                                                                 Map<Integer, String> mktSegmentsById) {
        SalesAndCateringMSToRMSMarketSegmentDTO salesAndCateringMSToRMSMarketSegmentDTO = new SalesAndCateringMSToRMSMarketSegmentDTO();
        salesAndCateringMSToRMSMarketSegmentDTO.setSaleAndCateringMS(salesAndCateringMS.getName());
        salesAndCateringMSToRMSMarketSegmentDTO.setRmsMarketSegment(mktSegmentsById.get(salesAndCateringMS.getMarketSegmentId()));
        return salesAndCateringMSToRMSMarketSegmentDTO;
    }

    public void addSalesCateringToRMSMarketSegmentMapping(List<SalesAndCateringMSToRMSMarketSegmentDTO> salesAndCateringMSToRMSMarketSegments,
                                                             Map<String, Integer> mktSegmentIdsByCode, boolean persistConfigurations) {
        List<FunctionSpaceMarketSegment> functionSpaceMarketSegments = salesAndCateringMSToRMSMarketSegments.stream()
                .map(salesAndCateringMSToRMSMarketSegmentDTO ->
                        mapToFunctionSpaceMarketSegment(salesAndCateringMSToRMSMarketSegmentDTO, mktSegmentIdsByCode))
                .collect(Collectors.toList());
        if(shouldDeleteExistingConfigurations(persistConfigurations)) {
            deleteAllByPropertyId();
            saveFunctionSpaceMarketSegments(functionSpaceMarketSegments);
        }
        if(persistConfigurations) {
            addNewMarketSegments(functionSpaceMarketSegments);
        }
    }

    private void addNewMarketSegments(List<FunctionSpaceMarketSegment> functionSpaceMarketSegments) {
        if(isNotEmpty(functionSpaceMarketSegments)) {
            List<FunctionSpaceMarketSegment> existingFunctionSpaceMarketSegments = getMarketSegments();
            List<FunctionSpaceMarketSegment> newFunctionSpaceMarketSegments = functionSpaceMarketSegments.stream().filter(requestedFunctionSpaceMarketSegment -> existingFunctionSpaceMarketSegments
                            .stream().noneMatch(
                                    existingFunctionSpaceMarketSegment -> existingFunctionSpaceMarketSegment.getAbbreviation()
                                            .equals(requestedFunctionSpaceMarketSegment.getAbbreviation())))
                    .collect(Collectors.toList());
            if (isNotEmpty(newFunctionSpaceMarketSegments)) {
                saveFunctionSpaceMarketSegments(newFunctionSpaceMarketSegments);
            }else {
                LOGGER.info("AUTOMATION GROUP PRICING CONFIGURATION: Failed to add Market Segment");
            }
        }
    }

    public boolean validateSaleAndCateringToRmsMsRequest(List<SalesAndCateringMSToRMSMarketSegmentDTO> salesAndCateringMSToRMSMarketSegments,
                                                         Map<String, Integer> mktSegmentIdsByCode) {
        if (isSalesAndCateringConfigurationVisible()) {
            throw new TetrisException("pacman.integration.SalesAndCateringEvaluationEnabled toggle need to be enabled to use this api.");
        }
        if (!checkIfSalesAndCateringMSToRMSMarketSegmentsRequestContainsNullOrEmptyValues(salesAndCateringMSToRMSMarketSegments))
            return false;
        if (salesAndCateringMSToRMSMarketSegments.size() != salesAndCateringMSToRMSMarketSegments.stream()
                .map(salesAndCateringMSToRMSMarketSegmentDTO -> salesAndCateringMSToRMSMarketSegmentDTO.getSaleAndCateringMS().toUpperCase())
                .collect(Collectors.toSet()).size())
            return false;
        return salesAndCateringMSToRMSMarketSegments.stream().map(SalesAndCateringMSToRMSMarketSegmentDTO::getRmsMarketSegment)
                .filter(rmsMarketSegment -> !mktSegmentIdsByCode.containsKey(rmsMarketSegment))
                .collect(Collectors.toSet()).isEmpty();
    }

    private boolean checkIfSalesAndCateringMSToRMSMarketSegmentsRequestContainsNullOrEmptyValues(List<SalesAndCateringMSToRMSMarketSegmentDTO> salesAndCateringMSToRMSMarketSegments) {
        return salesAndCateringMSToRMSMarketSegments.stream().noneMatch(salesAndCateringMSToRMSMarketSegmentDTO -> (salesAndCateringMSToRMSMarketSegmentDTO.getRmsMarketSegment() == null
                || salesAndCateringMSToRMSMarketSegmentDTO.getRmsMarketSegment().isEmpty()
                || salesAndCateringMSToRMSMarketSegmentDTO.getSaleAndCateringMS() == null
                || salesAndCateringMSToRMSMarketSegmentDTO.getSaleAndCateringMS().isEmpty()));
    }

    private FunctionSpaceMarketSegment mapToFunctionSpaceMarketSegment(SalesAndCateringMSToRMSMarketSegmentDTO salesAndCateringMSToRMSMarketSegment,
                                                                       Map<String, Integer> mktSegmentIdsByCode) {
        FunctionSpaceMarketSegment functionSpaceMarketSegment = new FunctionSpaceMarketSegment();
        functionSpaceMarketSegment.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        functionSpaceMarketSegment.setName(salesAndCateringMSToRMSMarketSegment.getSaleAndCateringMS());
        functionSpaceMarketSegment.setAbbreviation(salesAndCateringMSToRMSMarketSegment.getSaleAndCateringMS());
        functionSpaceMarketSegment.setMarketSegmentId(mktSegmentIdsByCode.get(salesAndCateringMSToRMSMarketSegment.getRmsMarketSegment()));
        return functionSpaceMarketSegment;
    }

    public void deleteAllByPropertyId() {
        tenantCrudService.executeUpdateByNamedQuery(FunctionSpaceMarketSegment.DELETE_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    private boolean isSalesAndCateringConfigurationVisible() {
        return !pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED);
    }

    public List<GuestRoomTypeToRMSRoomTypeDTO> getGuestRoomTypeToRMSRoomTypeMapping() {
        List<FunctionSpaceGuestRoomCategory> guestRoomCategories = getGuestRoomCategories();
        Map<Integer, String> rmsAccommodationTypeById = getAssignedAccomTypes().stream()
                .collect(Collectors.toMap(AccomType::getId, AccomType::getName));
        return guestRoomCategories.stream()
                .map(guestRoomCategory -> mapToGuestRoomTypeToRMSRoomTypeDTO(guestRoomCategory, rmsAccommodationTypeById))
                .collect(Collectors.toList());
    }

    private GuestRoomTypeToRMSRoomTypeDTO mapToGuestRoomTypeToRMSRoomTypeDTO(FunctionSpaceGuestRoomCategory guestRoomCategory, Map<Integer, String> rmsAccommodationTypeById) {
        GuestRoomTypeToRMSRoomTypeDTO guestRoomTypeToRMSRoomTypeDTO = new GuestRoomTypeToRMSRoomTypeDTO();
        guestRoomTypeToRMSRoomTypeDTO.setGuestRoomType(guestRoomCategory.getRoomCategory());
        guestRoomTypeToRMSRoomTypeDTO.setRmsRoomType(rmsAccommodationTypeById.get(guestRoomCategory.getAccomTypeId()));
        return guestRoomTypeToRMSRoomTypeDTO;
    }


    public boolean addGuestRoomTypeToRMSRoomTypeMapping(List<GuestRoomTypeToRMSRoomTypeDTO> guestRoomTypeToRMSRoomTypes, boolean persistConfigurations) {
        List<FunctionSpaceGuestRoomCategory> guestRoomCategories = new ArrayList<>();
        if (shouldDeleteExistingConfigurations(persistConfigurations)) {
            Map<String, Integer> rmsAccommodationTypeIdsByName = getAssignedAccomTypes().stream()
                    .collect(Collectors.toMap(AccomType::getName, AccomType::getId));
            if (validateGuestRoomTypeToRMSRoomTypeRequest(guestRoomTypeToRMSRoomTypes, rmsAccommodationTypeIdsByName)) {
                guestRoomCategories = guestRoomTypeToRMSRoomTypes.stream()
                        .map(guestRoomTypeToRMSRoomType -> mapToFunctionSpaceGuestRoomCategories(guestRoomTypeToRMSRoomType, rmsAccommodationTypeIdsByName)).collect(Collectors.toList());
                deleteAllFunctionSpaceGuestRoomCategoriesByPropertyId();
                saveFunctionSpaceGuestRoomCategories(guestRoomCategories);
                return true;
            }
            return false;
        }

        if (shouldAddNewGuestRoomTypes(guestRoomTypeToRMSRoomTypes, persistConfigurations)) {
            if (isSalesAndCateringConfigurationVisible()) {
                throw new TetrisException("pacman.integration.SalesAndCateringEvaluationEnabled toggle need to be enabled to use this api.");
            }
            guestRoomCategories = getFunctionSpaceGuestRoomCategories();
            if (isNotEmpty(guestRoomCategories)) {
                saveFunctionSpaceGuestRoomCategories(guestRoomCategories);
                return true;
            }
        }
        return false;
    }

    private List<FunctionSpaceGuestRoomCategory> getFunctionSpaceGuestRoomCategories() {
        List<FunctionSpaceGuestRoomCategory> guestRoomCategories;
        List<JSONObject> guestRoomTypesByLocation = restClient.getDataFromEndpoint(RestEndpoints.AHWS_GET_GUEST_ROOM_TYPES, buildParameters(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode()));
        try {
            if(CollectionUtils.isEmpty(guestRoomTypesByLocation)){
                LOGGER.info("AUTOMATION GROUP PRICING CONFIGURATION : Failed to add guest room type");
            }
            List<AHWSGuestRoomType> guestRoomTypes = mapJsonToGuestRoomType(guestRoomTypesByLocation);
            guestRoomCategories = new ArrayList<>();

            List<FunctionSpaceGuestRoomCategory> existingFSGuestRoomCategories = getGuestRoomCategories();
            Map<String, Integer> rmsAccommodationTypeIdsByName = getAssignedAccomTypes().stream()
                    .collect(Collectors.toMap(AccomType::getName, AccomType::getId));
            for (AHWSGuestRoomType guestRoomType : guestRoomTypes) {
                populateFunctionSpaceGuestRoomCategoriesUsingAccomTypes(guestRoomCategories, existingFSGuestRoomCategories, guestRoomType, rmsAccommodationTypeIdsByName);
            }
        } catch (IOException ioe) {
            LOGGER.error("Received IOException. Error: " + ioe.getMessage(), ioe);
            return null;
        }
        return guestRoomCategories;
    }

    private void populateFunctionSpaceGuestRoomCategoriesUsingAccomTypes(List<FunctionSpaceGuestRoomCategory> guestRoomCategories, List<FunctionSpaceGuestRoomCategory> existingFSGuestRoomCategories, AHWSGuestRoomType guestRoomType, Map<String, Integer> rmsAccommodationTypeIdsByName) {
        String name = guestRoomType.getName();
        String abbreviation = guestRoomType.getAbbreviation();

        if (!existingFSGuestRoomCategories.stream().anyMatch(existingFSGuestRoomCategory ->
                existingFSGuestRoomCategory.getRoomCategory().equalsIgnoreCase(name) && Objects.nonNull(existingFSGuestRoomCategory.getAccomTypeId()))) {
            AccomType acoomType = tenantCrudService.findByNamedQuerySingleResult(AccomType.BY_CODE_AND_STATUS, QueryParameter.with("code", abbreviation).and("statusId", 1).parameters());
            if (acoomType == null) {
                AccomType accomTypeWithMaxCapacityOfMasterClass = findAccomTypeWithMaxCapacityOfMasterClass(PacmanWorkContextHelper.getPropertyId());
                if (validateAccomType(rmsAccommodationTypeIdsByName, accomTypeWithMaxCapacityOfMasterClass.getName())) {
                    createFunctionSpaceGuestRoomCategory(guestRoomCategories, accomTypeWithMaxCapacityOfMasterClass, name);
                }
            } else if (validateAccomType(rmsAccommodationTypeIdsByName, acoomType.getName())) {
                createFunctionSpaceGuestRoomCategory(guestRoomCategories, acoomType, name);
            }
        }
    }

    private boolean validateAccomType(Map<String, Integer> rmsAccommodationTypeIdsByName, String acoomTypeName) {
        return rmsAccommodationTypeIdsByName.containsKey(acoomTypeName);
    }

    private void createFunctionSpaceGuestRoomCategory(List<FunctionSpaceGuestRoomCategory> guestRoomCategories, AccomType accomType, String name) {
        final Map<String, FunctionSpaceGuestRoomCategory> guestRoomCategoriesByRoomCategory = getGuestRoomCategoriesByRoomCategory(getGuestRoomCategories());
        FunctionSpaceGuestRoomCategory functionSpaceGuestRoomCategory = prepareGetGuestRoomCategory(guestRoomCategoriesByRoomCategory, accomType, name);

        guestRoomCategories.add(functionSpaceGuestRoomCategory);
    }

    private boolean shouldAddNewGuestRoomTypes(List<GuestRoomTypeToRMSRoomTypeDTO> guestRoomTypeToRMSRoomTypes, boolean persistConfigurations) {
        return persistConfigurations && isEmpty(guestRoomTypeToRMSRoomTypes);
    }

    public AccomType findAccomTypeWithMaxCapacityOfMasterClass(Integer propertyId) {
        Object[] accomTypeWithHighestCapacityFromMasterClass = tenantCrudService.findByNativeQuerySingleResult("select top 1 at.* from Accom_type at\n" +
                        " inner join Accom_Class ac\n" +
                        " on at.Accom_Class_ID = ac.Accom_Class_ID\n" +
                        " and ac.Master_Class = 1 and  at.Property_ID = :propertyId and at.Status_ID =1\n" +
                        " order by at.Accom_Type_Capacity desc",
                QueryParameter.with(PROPERTY_ID, propertyId).parameters());
        return mapAccomTypeObjectToAccomTypeEntity(accomTypeWithHighestCapacityFromMasterClass);
    }

    private AccomType mapAccomTypeObjectToAccomTypeEntity(Object[] accomTypeWithHighestCapacityFromMasterClass) {
        AccomType accomType = new AccomType();
        accomType.setId((Integer) accomTypeWithHighestCapacityFromMasterClass[0]);
        accomType.setPropertyId((Integer) accomTypeWithHighestCapacityFromMasterClass[1]);
        accomType.setName((String) accomTypeWithHighestCapacityFromMasterClass[2]);
        accomType.setAccomTypeCode((String) accomTypeWithHighestCapacityFromMasterClass[3]);
        accomType.setDescription((String) accomTypeWithHighestCapacityFromMasterClass[4]);
        accomType.setAccomTypeCapacity(((BigDecimal) accomTypeWithHighestCapacityFromMasterClass[5]).intValue());
        accomType.setSystemDefault((Integer) accomTypeWithHighestCapacityFromMasterClass[7]);
        accomType.setStatusId((Integer) accomTypeWithHighestCapacityFromMasterClass[8]);
        accomType.setRohType((Integer) accomTypeWithHighestCapacityFromMasterClass[10]);
        accomType.setIsComponentRoom((String) accomTypeWithHighestCapacityFromMasterClass[14]);
        accomType.setDisplayStatusId((Integer) accomTypeWithHighestCapacityFromMasterClass[15]);
        accomType.setExcludedFromSoldout((Integer) accomTypeWithHighestCapacityFromMasterClass[16]);
        accomType.setSupplementOverbookingType((Integer) accomTypeWithHighestCapacityFromMasterClass[17]);
        return accomType;
    }

    private List<AHWSGuestRoomType> mapJsonToGuestRoomType(List<JSONObject> guestRoomTypeJSONResponse) throws IOException {
        List<AHWSGuestRoomType> guestRoomTypes = new ArrayList<>();

        ObjectMapper objMapper = new ObjectMapper();

        for (JSONObject json : guestRoomTypeJSONResponse) {
            AHWSGuestRoomType e = objMapper.readValue(json.toString(), AHWSGuestRoomType.class);
            guestRoomTypes.add(e);
        }

        return guestRoomTypes;
    }

    private Map<String, String> buildParameters(String clientCode, String propertyCode) {
        Map<String, String> parameters = new HashMap<>();
        parameters.put(AHWS_CLIENT_CODE_NAME, clientCode);
        parameters.put(AHWS_PROPERTY_CODE_NAME, propertyCode);
        return parameters;
    }

    private boolean shouldDeleteExistingConfigurations(boolean persistConfigurations) {
        return !persistConfigurations;
    }
    public void autoConfigureGuestRoomTypesToAccomTypes() {
        Map<String, AccomType> rmsAccommodationTypeByCode = getAssignedAccomTypes().stream()
                .collect(Collectors.toMap(accomType -> accomType.getAccomTypeCode().toUpperCase(), Function.identity()));
        List<FunctionSpaceGuestRoomCategory> guestRoomCategories = getGuestRoomCategories();
        guestRoomCategories.stream().filter(category -> rmsAccommodationTypeByCode.containsKey(category.getRoomCategory().toUpperCase()))
                .forEach(category -> category.setAccomTypeId(rmsAccommodationTypeByCode.get(category.getRoomCategory().toUpperCase()).getId()));
        tenantCrudService.save(guestRoomCategories);
    }

    private boolean validateGuestRoomTypeToRMSRoomTypeRequest(List<GuestRoomTypeToRMSRoomTypeDTO> guestRoomTypeToRMSRoomTypes, Map<String, Integer> rmsAccommodationTypeIdsByName) {
        if (isSalesAndCateringConfigurationVisible()) {
            throw new TetrisException("pacman.integration.SalesAndCateringEvaluationEnabled toggle need to be enabled to use this api.");
        }
        if (!checkIfGuestRoomTypeToRMSRoomTypesRequestContainsNullOrEmptyValues(guestRoomTypeToRMSRoomTypes))
            return false;
        if (guestRoomTypeToRMSRoomTypes.size() != guestRoomTypeToRMSRoomTypes.stream()
                .map(guestRoomTypeToRMSRoomTypeDTO -> guestRoomTypeToRMSRoomTypeDTO.getGuestRoomType().toUpperCase())
                .collect(Collectors.toSet()).size())
            return false;
        return guestRoomTypeToRMSRoomTypes.stream().map(GuestRoomTypeToRMSRoomTypeDTO::getRmsRoomType)
                .filter(StringUtils::isNotBlank)
                .filter(rmsAccommodationType -> !rmsAccommodationTypeIdsByName.containsKey(rmsAccommodationType))
                .collect(Collectors.toSet()).isEmpty();
    }

    private boolean checkIfGuestRoomTypeToRMSRoomTypesRequestContainsNullOrEmptyValues(List<GuestRoomTypeToRMSRoomTypeDTO> guestRoomTypeToRMSRoomTypes) {
        return guestRoomTypeToRMSRoomTypes.stream().noneMatch(guestRoomTypeToRMSRoomTypeDTO -> (guestRoomTypeToRMSRoomTypeDTO.getGuestRoomType() == null
                || guestRoomTypeToRMSRoomTypeDTO.getGuestRoomType().isEmpty()));
    }

    private void deleteAllFunctionSpaceGuestRoomCategoriesByPropertyId() {
        tenantCrudService.executeUpdateByNamedQuery(FunctionSpaceGuestRoomCategory.DELETE_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    private FunctionSpaceGuestRoomCategory mapToFunctionSpaceGuestRoomCategories(GuestRoomTypeToRMSRoomTypeDTO guestRoomTypeToRMSRoomType, Map<String, Integer> rmsAccommodationTypeIdsByName) {
        FunctionSpaceGuestRoomCategory functionSpaceGuestRoomCategory = new FunctionSpaceGuestRoomCategory();
        functionSpaceGuestRoomCategory.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        functionSpaceGuestRoomCategory.setRoomCategory(guestRoomTypeToRMSRoomType.getGuestRoomType());
        functionSpaceGuestRoomCategory.setAccomTypeId(rmsAccommodationTypeIdsByName.get(guestRoomTypeToRMSRoomType.getRmsRoomType()));
        return functionSpaceGuestRoomCategory;
    }

    public FunctionSpaceFunctionRoomDefaultTime getFunctionSpaceDefaultTime() {
        List<FunctionSpaceFunctionRoomDefaultTime> ls = tenantCrudService.findByNamedQuery(
                FunctionSpaceFunctionRoomDefaultTime.FIND_DEFAULT_TIME);
        return ls.isEmpty() ? new FunctionSpaceFunctionRoomDefaultTime() : ls.iterator().next();
    }

    public void saveFunctionSpaceDefaultTime(FunctionSpaceFunctionRoomDefaultTime defaultTime) {
        tenantCrudService.save(defaultTime);
    }

    public FunctionSpaceRevenueGroup getFunctionSpaceRevenueGroupById(Integer id) {
        return tenantCrudService.findByNamedQuerySingleResult(FunctionSpaceRevenueGroup.FIND_BY_ID, Map.of("id", id));
    }

    public void softDeleteFunctionSpaceRevenueGroup(FunctionSpaceRevenueGroup functionSpaceRevenueGroup) {
        functionSpaceRevenueGroup.setStatus(TenantStatusEnum.DELETED);
        tenantCrudService.save(functionSpaceRevenueGroup);
    }

    public boolean saveFunctionSpaceStatus(List<FunctionSpaceStatus> functionSpaceStatuses) {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        functionSpaceStatuses.forEach(functionSpaceStatus ->
                functionSpaceStatus.setPropertyId(propertyId));
        if (isNotEmpty(functionSpaceStatuses)) {
            if (validateStatusCode(functionSpaceStatuses) && !checkIfDuplicateStatusCodesAreSent(functionSpaceStatuses)) {
                List<FunctionSpaceStatus> existingFunctionSpaceStatuses = getFunctionSpaceStatusesByProperty();
                List<FunctionSpaceStatus> newFunctionSpaceStatuses = functionSpaceStatuses.stream().filter(requestedFunctionSpaceStatus -> existingFunctionSpaceStatuses.stream().noneMatch(existingFunctionSpaceStatus -> existingFunctionSpaceStatus.getStatusCode().equals(requestedFunctionSpaceStatus.getStatusCode())))
                        .collect(Collectors.toList());
                if (isNotEmpty(newFunctionSpaceStatuses)) {
                    saveFunctionSpaceStatuses(newFunctionSpaceStatuses);
                    return true;
                }
            }
        }
        LOGGER.info("AUTOMATION GROUP PRICING CONFIGURATION: Failed to configure status code mapping");
        return false;
    }

    private boolean validateStatusCode(List<FunctionSpaceStatus> functionSpaceStatuses) {
        return functionSpaceStatuses.stream().noneMatch(request -> (request.getStatusCode() == null
                || request.getStatusCode().isEmpty()));
    }

    private static boolean checkIfDuplicateStatusCodesAreSent(List<FunctionSpaceStatus> functionSpaceStatuses) {
        return functionSpaceStatuses.size() != functionSpaceStatuses.stream()
                .map(functionSpaceStatus -> functionSpaceStatus.getStatusCode().toUpperCase())
                .collect(Collectors.toSet()).size();
    }
}
