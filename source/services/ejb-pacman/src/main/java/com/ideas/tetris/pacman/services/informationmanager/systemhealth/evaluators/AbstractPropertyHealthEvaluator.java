package com.ideas.tetris.pacman.services.informationmanager.systemhealth.evaluators;

import com.ideas.tetris.pacman.services.informationmanager.systemhealth.dto.PropertyHealthColor;
import com.ideas.tetris.pacman.services.informationmanager.systemhealth.dto.PropertyHealthDetails;
import com.ideas.tetris.pacman.services.informationmanager.systemhealth.dto.SystemHealthCondition;
import com.ideas.tetris.pacman.services.informationmanager.systemhealth.evaluators.dal.PropertyHealthEvaluationDAO;
import com.ideas.tetris.pacman.services.informationmanager.systemhealth.evaluators.dal.PropertyHealthEvaluationDO;
import com.ideas.tetris.pacman.services.informationmanager.systemhealth.evaluators.dal.dos.InfoMgrSysHealthCondition;
import com.ideas.tetris.pacman.services.informationmanager.systemhealth.evaluators.dal.dos.InfoMgrSysHealthDetails;
import com.ideas.tetris.pacman.services.informationmanager.systemhealth.evaluators.dal.dos.InfoMgrSysHealthDetailsNameLookup;
import com.ideas.tetris.pacman.services.informationmanager.systemhealth.evaluators.dal.dos.InfoMgrSysHealthExtendedDetails;
import com.ideas.tetris.pacman.services.informationmanager.systemhealth.evaluators.dal.dos.InfoMgrSysHealthSummary;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

public abstract class AbstractPropertyHealthEvaluator {
    private static final Logger LOGGER = Logger.getLogger(AbstractPropertyHealthEvaluator.class.getName());
    private static final BigDecimal NO_RESULT_FOUND = BigDecimal.ZERO;

    public boolean isRemoveRequiresNewForAbstractPropertyHealthEvaluatorExecuteMethodEnabled() {
        return Boolean.parseBoolean(System.getProperty("remove.requiresNew.AbstractPropertyHealthEvaluator.execute", "false").trim());
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public boolean execute(int propertyId) {
        return evaluate(propertyId);
    }

    public boolean executeWithoutRequiresNew(int propertyId) {
        return evaluate(propertyId);
    }

    private boolean evaluate(int propertyId) {
        try {
            //Validations
            long noOfRecordsAvailable = validateData(propertyId);
            if (noOfRecordsAvailable <= 0) {
                updateOrCreateSummary(propertyId, NO_RESULT_FOUND, PropertyHealthColor.GRAY, true);
                return true;
            }
            List<PropertyHealthDetails> healthDetails = getEvaluatedDetails(propertyId);
            InfoMgrSysHealthSummary summaryDo = getInfoMgrSysHealthSummary(propertyId, healthDetails, true);
            List<InfoMgrSysHealthDetails> systemHealthDetails = createDetails(healthDetails, summaryDo);
            //Update summary table, delete existing details records and insert this evaluation's details.
            updateThisEvaluationInDB(summaryDo, systemHealthDetails);
        } catch (Exception t) {
            LOGGER.error(t.getMessage(), t);
            return false;
        }
        return true;
    }

    InfoMgrSysHealthSummary getInfoMgrSysHealthSummary(int propertyId, List<PropertyHealthDetails> healthDetails, boolean persistSummary) throws PropertyHealthEvaluatorException {
        PropertyHealthColor healthColor = PropertyHealthColor.GREEN;
        BigDecimal evalResult = NO_RESULT_FOUND;
        PropertyHealthEvaluationDO evaluationDO = evaluate(propertyId, healthDetails);
        if (null != evaluationDO) {
            healthColor = evaluateHealthColor(evaluationDO.getPercent());
            evalResult = evaluationDO.getPercent();
        }
        InfoMgrSysHealthSummary summaryDo = null;
        summaryDo = updateOrCreateSummary(propertyId, evalResult, healthColor, persistSummary);
        return summaryDo;
    }

    protected abstract long validateData(int propertyId);

    public abstract List<PropertyHealthDetails> getCurrentHealthDetails(List<InfoMgrSysHealthDetails> systemHealthDetailsList);

    private void updateThisEvaluationInDB(InfoMgrSysHealthSummary summaryDo,
                                          List<InfoMgrSysHealthDetails> systemHealthDetails) {
        boolean isDeleteSuccessful = deleteExistingDetailsRecords(summaryDo);

        if (!isDeleteSuccessful) {
            LOGGER.warn("No details deleted. Check if this was expected");
        }
        getDAO().updateNewEvaluationInDB(summaryDo, systemHealthDetails);
    }

    protected List<InfoMgrSysHealthDetails> createDetails(List<PropertyHealthDetails> healthDetailsList,
                                                          InfoMgrSysHealthSummary systemHealthSummary) {
        List<InfoMgrSysHealthDetails> persistentDetailsList = new ArrayList<InfoMgrSysHealthDetails>();
        for (PropertyHealthDetails healthDetails : healthDetailsList) {
            InfoMgrSysHealthDetails persistentDetails = new InfoMgrSysHealthDetails();
            persistentDetails.setSystemHealthSummary(systemHealthSummary);

            List<InfoMgrSysHealthExtendedDetails> extendedDetailsList = new LinkedList<InfoMgrSysHealthExtendedDetails>();
            createHealthDetails(persistentDetails, healthDetails, extendedDetailsList);

            persistentDetails.setExtendedDetailsList(extendedDetailsList);
            persistentDetailsList.add(persistentDetails);
        }
        return persistentDetailsList;
    }

    protected abstract void createHealthDetails(InfoMgrSysHealthDetails persistentDetails, PropertyHealthDetails propertyHealthDetails,
                                                List<InfoMgrSysHealthExtendedDetails> extendedDetailsList);

    protected void createExtendedHealthDetails(InfoMgrSysHealthDetails persistentDetails, String detailName, String detailValue,
                                               List<InfoMgrSysHealthExtendedDetails> extendedDetails) {
        InfoMgrSysHealthExtendedDetails extendedDetail = new InfoMgrSysHealthExtendedDetails();
        extendedDetail.setSystemHealthDetails(persistentDetails);
        InfoMgrSysHealthDetailsNameLookup nameLookup = getDetailNameToDBMaping().get(detailName);
        extendedDetail.setSystemHealthDetailsNameLookup(nameLookup);
        extendedDetail.setDetailValue(detailValue);
        extendedDetails.add(extendedDetail);
    }

    protected InfoMgrSysHealthSummary updateOrCreateSummary(Integer propertyId, BigDecimal evaluationResult,
                                                            PropertyHealthColor healthColor, boolean persistSummary) throws PropertyHealthEvaluatorException {
        PropertyHealthEvaluationDAO dao = getDAO();
        InfoMgrSysHealthSummary evalSummary = null;
        evalSummary = dao.findSystemHealthEvalSummary(propertyId, getEvaluationCondition());
        if (null != evalSummary) {
            setResultsInSummary(evalSummary, evaluationResult, healthColor);
        } else {
            String errString = "\nsummary record could not be found for system health condtion. "
                    + "\nInserting new one for " + getEvaluationCondition() + ", property [" + propertyId + "].";
            LOGGER.warn(errString);
            evalSummary = createEvalSummary(propertyId, getPropertyCode(propertyId), evaluationResult, healthColor, persistSummary);
        }
        return evalSummary;
    }

    private String getPropertyCode(Integer propertyId) throws PropertyHealthEvaluatorException {
        String propertyCode = getDAO().findPropertyCodeById(propertyId);
        if (StringUtils.isEmpty(propertyCode)) {
            throw new PropertyHealthEvaluatorException("Property code could not retrieved.");
        }
        return propertyCode;
    }

    private InfoMgrSysHealthSummary createEvalSummary(Integer propertyId,
                                                      String propertyCode, BigDecimal result,
                                                      PropertyHealthColor healthColor, boolean persistSummary) {
        InfoMgrSysHealthSummary summary = new InfoMgrSysHealthSummary();
        summary.setPropertyId(propertyId);
        summary.setEvaluationCondition(getInfoMgrHealthCondition(getEvaluationCondition()));
        summary.setPropertyCode(propertyCode);

        /**
         * TODO - Check if this way of setting creation date is correct. Should DateService be called?
         */
        summary.setCreationDate(new Date());

        setResultsInSummary(summary, result, healthColor);
        if (persistSummary) {
            getDAO().createEvalSummary(summary);
        }
        return summary;
    }

    private InfoMgrSysHealthCondition getInfoMgrHealthCondition(SystemHealthCondition evaluationCondition) {
        return getDAO().findInfoMgrSysHealthConditionByCondition(evaluationCondition);
    }

    private void setResultsInSummary(InfoMgrSysHealthSummary evalSummary, BigDecimal result,
                                     PropertyHealthColor healthColor) {
        evalSummary.setEvaluationResult(result);
        evalSummary.setHealthColor(healthColor);
        /**
         * TODO - Check if this way of setting last modified date is correct. Should DateService be called?
         */
        evalSummary.setLastModifiedDate(new Date());
    }

    protected abstract SystemHealthCondition getEvaluationCondition();

    protected abstract PropertyHealthEvaluationDAO getDAO();

    protected abstract PropertyHealthEvaluationDO evaluate(int propertyId, List<PropertyHealthDetails> alPHD);

    protected PropertyHealthColor evaluateHealthColor(BigDecimal percent) {
        PropertyHealthColor healthColor = null;
        if (percent.floatValue() < getGreen()) {
            healthColor = PropertyHealthColor.GREEN;
        } else if (percent.floatValue() >= getGreen() && percent.floatValue() < getYellow()) {
            healthColor = PropertyHealthColor.YELLOW;
        } else if (percent.floatValue() >= getYellow()) {
            healthColor = PropertyHealthColor.RED;
        }
        return healthColor;
    }

    protected abstract float getYellow();

    protected abstract float getGreen();

    protected abstract List<PropertyHealthDetails> getEvaluatedDetails(int propertyId);

    protected boolean deleteExistingDetailsRecords(InfoMgrSysHealthSummary summaryDo) {
        return getDAO().deleteLastEvaluationResults(summaryDo);
    }

    public abstract void setDao(PropertyHealthEvaluationDAO dao);

    public static void initDetailDBNameMapper(
            SystemHealthCondition condition, List<String> detailNames,
            PropertyHealthEvaluationDAO dao, ConcurrentMap<String, InfoMgrSysHealthDetailsNameLookup> detailDBNameMap) {
        List<InfoMgrSysHealthDetailsNameLookup> namesInDB = dao.findSystemHealthDetailNameLookup(condition);

        if (namesInDB.size() != detailNames.size()) {
            LOGGER.warn("Number of detail names in DB and number of detail names passed for this condition are different." +
                    "Please make sure that details in DB and in Java are exactly same.\n");
        }

        for (String detailName : detailNames) {
            for (InfoMgrSysHealthDetailsNameLookup dbName : namesInDB) {
                if (detailName.equalsIgnoreCase(dbName.getDetailName())) {
                    detailDBNameMap.putIfAbsent(detailName, dbName);
                }
            }
        }
    }

    private ConcurrentMap<String, InfoMgrSysHealthDetailsNameLookup> getDetailNameToDBMaping() {
        ConcurrentMap<String, InfoMgrSysHealthDetailsNameLookup> detailDBNameMapping = getDetailNameMap();

        if (null == detailDBNameMapping || detailDBNameMapping.isEmpty()) {
            detailDBNameMapping = new ConcurrentHashMap<String, InfoMgrSysHealthDetailsNameLookup>();
            initDetailDBNameMapper(getEvaluationCondition(), getDetailNameList(), getDAO(), detailDBNameMapping);
        }

        return detailDBNameMapping;
    }

    protected abstract List<String> getDetailNameList();

    protected abstract ConcurrentMap<String, InfoMgrSysHealthDetailsNameLookup> getDetailNameMap();

    public List<InfoMgrSysHealthDetails> evaluateDQI(int propertyId) throws PropertyHealthEvaluatorException {
        List<PropertyHealthDetails> dqiHealthDetails = getEvaluatedDetails(propertyId);
        InfoMgrSysHealthSummary dqiHealthSummary = getInfoMgrSysHealthSummary(propertyId, dqiHealthDetails, false);
        List<InfoMgrSysHealthDetails> dqiHealthSummaryDetails = createDetails(dqiHealthDetails, dqiHealthSummary);
        return dqiHealthSummaryDetails;
    }

}
