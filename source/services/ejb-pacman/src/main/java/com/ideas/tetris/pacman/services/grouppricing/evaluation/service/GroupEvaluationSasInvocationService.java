package com.ideas.tetris.pacman.services.grouppricing.evaluation.service;

import com.ideas.sas.service.SASClientService;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.utils.xmlutil.XmlUtil;
import com.ideas.tetris.pacman.common.xml.schema.optimization.request.v1.ArrDateDetails;
import com.ideas.tetris.pacman.common.xml.schema.optimization.request.v1.SASRequest;
import com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.*;
import com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.ArrivalDateType.ForecastGroup;
import com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.ArrivalDateType.ForecastGroup.OccupancyDate;
import com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.ArrivalDateType.FunctionSpace;
import com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.ArrivalDateType.FunctionSpace.OccupancyDate.DayPart;
import com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.ArrivalDateType.OptimalRCRates;
import com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.ArrivalDateType.OptimalRCRates.RoomClass;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigurationLimit;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.GroupPricingRateConfigurationService;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluation;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationArrivalDateResultCode;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationRoomType;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationType;
import com.ideas.tetris.pacman.services.sasoptimization.service.OptimizationServiceUtilityBean;
import com.ideas.tetris.pacman.util.jaxb.optimization.request.OptimizationRequestJAXBUtil.OptimizationRequestQualifier;
import com.ideas.tetris.pacman.util.jaxb.optimization.response.SimplifiedOptimizationResponseJAXBUtil.SimplifiedOptimizationResponseQualifier;
import com.ideas.tetris.platform.common.Justification;
import com.ideas.tetris.platform.common.businessservice.async.AsyncCallbackData;
import com.ideas.tetris.platform.common.businessservice.async.AsyncCallbackDataBuilder;
import com.ideas.tetris.platform.common.businessservice.async.AsyncJobCallback;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobStepContext;
import com.ideas.tetris.platform.common.util.jaxb.JAXBUtilLocal;
import com.ideas.tetris.platform.common.utils.systemconfig.SASSettings;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;

import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import org.springframework.scheduling.annotation.Async;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.Future;

import org.springframework.transaction.annotation.Transactional;

@Justification("In some cases it is taking SAS longer than 5 minutes to process the evaluation. Relaxing the default tx timeout to accomadate this")
@Component
@Transactional(timeout = 420)
public class GroupEvaluationSasInvocationService {
    private static final Logger LOGGER = Logger.getLogger(GroupEvaluationSasInvocationService.class);

    @Autowired
	private GroupEvaluationTransformer groupEvaluationTransformer;
    @Autowired
	private OptimizationServiceUtilityBean optimizationServiceUtility;
    @OptimizationRequestQualifier
    @Autowired
	@Qualifier("optimizationRequestJAXBUtil")
	private JAXBUtilLocal requestJaxbUtil;
    @Autowired
	private SASClientService sasClientService;
    @SimplifiedOptimizationResponseQualifier
    @Autowired
	@Qualifier("simplifiedOptimizationResponseJAXBUtil")
	private JAXBUtilLocal responseJaxbUtil;
    @Autowired
	private GroupPricingRateConfigurationService groupPricingRateConfigurationService;
    @Autowired
	private PacmanConfigParamsService configParamsService;
    @Autowired
	private AbstractMultiPropertyCrudService multiPropertyCrudService;

    private static final String FUNCTION_SPACE_ENABLED_OPERATION_NAME = "groupEvaluation";
    private static final String GROUP_PRICING_ENABLED_OPERATION_NAME = "groupPriceRequest";
    public static final int PERF_LOG_LEVEL = 2;

    @SuppressWarnings("unchecked")
    @Async
    @AsyncJobCallback
    public Future<Object> evaluate(WorkContextType workContext, JobStepContext jobStepContext,
                                   GroupEvaluation groupEvaluation) {

        AsyncCallbackData asyncCallbackData = AsyncCallbackDataBuilder.build();
        try {
            boolean useMockResult = !SystemConfig.isSASGroupPricingInvocationEvaluation();

            String responseXML = evaluateSynchronous(groupEvaluation, asyncCallbackData, useMockResult);

            // Set the response onto the callback
            asyncCallbackData.setResponseString(responseXML);
            asyncCallbackData.setResponse(groupEvaluation);
        } catch (TetrisException te) {
            te.setPayload(asyncCallbackData);
            throw te;
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.SAS_INTEGRATION_ERROR,
                    "Unable to successfully call SAS for a GroupEvaluation", e, asyncCallbackData);
        }

        return AsyncCallbackDataBuilder.buildFuture(asyncCallbackData);
    }

    public String evaluateSynchronous(GroupEvaluation groupEvaluation, boolean useMockResult) {
        return evaluateSynchronous(groupEvaluation, AsyncCallbackDataBuilder.build(), useMockResult);
    }

    private boolean isSASPerformanceDebugEnabled() {
        return configParamsService.getBooleanParameterValue(IPConfigParamName.SAS_LOG_SASPERFORMANCE_DEBUG_ENABLED.value());
    }

    @SuppressWarnings("squid:S3776")
    public String evaluateSynchronous(GroupEvaluation groupEvaluation, AsyncCallbackData asyncCallbackData,
                                      boolean useMockResult) {
        // Build the SASRequest
        SASRequest sasRequest = new SASRequest();
        sasRequest.setRequestHeader(optimizationServiceUtility.createRequestHeader());
        sasRequest.setGroupPriceRequest(groupEvaluationTransformer.createRequest(groupEvaluation));

        if (isSASPerformanceDebugEnabled()) {
            sasRequest.getRequestHeader().setPerfLogLevel(PERF_LOG_LEVEL);
        }

        // Set the Operation Name
        boolean functionSpaceEnabled = configParamsService
                .getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value());
        String operationName = getOperationName(functionSpaceEnabled, groupEvaluation);
        sasRequest.getRequestHeader().setOperationName(operationName);

        // Marshall the request
        String requestXML = requestJaxbUtil.marshall(sasRequest);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("GroupEvaluation SASRequest: ");
            LOGGER.debug("\n" + requestXML);
        }
        asyncCallbackData.setRequestString(requestXML);

        /****************************************************************************
         * Call SAS stored proc
         ***************************************************************************/
        String responseXML = executeSASProcedure(requestXML, asyncCallbackData, sasRequest, useMockResult,
                groupEvaluation);

        // Log the response
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("GroupEvaluation SASResponse: ");
            LOGGER.debug("\n" + responseXML);
        }

        // Unmarshall the response
        SASResponse sasResponse = (SASResponse) responseJaxbUtil.unmarshall(responseXML);

        if (functionSpaceEnabled) {
            GroupResponseType groupResponse = sasResponse.getGroupResponse();
            if (groupResponse == null || groupResponse.getArrivalDate() == null
                    || CollectionUtils.isEmpty(groupResponse.getArrivalDate())) {
                if (groupResponse == null) {
                    LOGGER.error("The function space GroupResponseType object is null!");
                } else if (groupResponse.getArrivalDate() == null) {
                    LOGGER.error("The function space GroupResponseType object has a null ArrivalDate object!");
                } else if (CollectionUtils.isEmpty(groupResponse.getArrivalDate())) {
                    LOGGER.error("The function space GroupResponseType object has an empty ArrivalDate object!");
                }
                LOGGER.debug("GroupEvaluation SASResponse: ");
                LOGGER.debug("\n" + responseXML);
                throw new TetrisException(ErrorCode.SAS_INTEGRATION_ERROR, "common.sas.error.evaluationFailed");
            }

            // Apply the SASResponse with the group pricing sas response
            groupEvaluationTransformer.applyResults(groupEvaluation, groupResponse);

        } else {
            GroupPriceResponseType groupPriceResponse = sasResponse.getGroupPriceResponse();
            if (groupPriceResponse == null || groupPriceResponse.getArrivalDate() == null
                    || CollectionUtils.isEmpty(groupPriceResponse.getArrivalDate())) {
                if (groupPriceResponse == null) {
                    LOGGER.error("The GroupPriceResponseType object is null!");
                } else if (groupPriceResponse.getArrivalDate() == null) {
                    LOGGER.error("The GroupPriceResponseType object has a null ArrivalDate object!");
                } else if (CollectionUtils.isEmpty(groupPriceResponse.getArrivalDate())) {
                    LOGGER.error("The GroupPriceResponseType object has an empty ArrivalDate object!");
                }
                LOGGER.debug("GroupEvaluation SASResponse: ");
                LOGGER.debug("\n" + responseXML);
                throw new TetrisException(ErrorCode.SAS_INTEGRATION_ERROR, "common.sas.error.evaluationFailed");
            }

            // Apply the SASResponse with the group pricing sas response
            groupEvaluationTransformer.applyResults(groupEvaluation, groupPriceResponse);
        }

        // Quick validation on the response
        return responseXML;
    }

    protected String getOperationName(boolean functionSpaceEnabled, GroupEvaluation groupEvaluation) {
        boolean isGroupPricingEvaluationForFSRMGuestRoomOnlyRequest = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.USE_GROUP_PRICING_EVALUATION_FOR_FSRM_GUEST_ROOM_ONLY_REQUEST.value());
        if(functionSpaceEnabled){
            return isGroupPricingEvaluationForFSRMGuestRoomOnlyRequest && groupEvaluation.getEvaluationType().equals(GroupEvaluationType.GUEST_ROOM_ONLY)
                    ? GROUP_PRICING_ENABLED_OPERATION_NAME : FUNCTION_SPACE_ENABLED_OPERATION_NAME;
        }
        return GROUP_PRICING_ENABLED_OPERATION_NAME;
    }

    public String executeSASProcedure(String requestXML, AsyncCallbackData asyncCallbackData, SASRequest sasRequest,
                               boolean useMockResult, GroupEvaluation groupEvaluation) {
        // Check the system properties to determine whether or not we should call SAS for GroupEvaluation. If set to false,
        // we inherently return mocked data
        if (!useMockResult) {
            String logPath = SASSettings.getLogPathUsing(sasRequest.getRequestHeader().getRequestId());

            asyncCallbackData.setResponseFileReferences(Arrays.asList(logPath));
            LOGGER.info("GroupEvaluationService running via the sync TK call: " + logPath);
            return sasClientService.executeTKSynchronously(sasRequest.getRequestHeader().getRequestId(), requestXML);
        }

        LOGGER.warn("Using mock result...");
        SASResponse sasResponse = new MockSASResponseBuilder().buildSASResponse(sasRequest, groupEvaluation);

        return responseJaxbUtil.marshall(sasResponse);
    }

    protected boolean isTKOnDemand() {
        return configParamsService.getBooleanParameterValue(IntegrationConfigParamName.USE_TKON_DEMAND.value());
    }

    protected Integer getSasTKPort() {
        Integer sasTKPort = isTKOnDemand() ? SystemConfig.getSASTKPortOnDemand() : SystemConfig.getSASTKPort();
        LOGGER.info("GroupEvaluation using SAS TK Port: " + sasTKPort);
        return sasTKPort;
    }

    class MockSASResponseBuilder {
        @SuppressWarnings("squid:S3776")
        public SASResponse buildSASResponse(SASRequest sasRequest, GroupEvaluation groupEvaluation) {
            Integer propertyId = groupEvaluation.getPropertyId();
            // Need different Group Response object if Function Space Enabled
            boolean functionSpaceEnabled = configParamsService
                    .getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value());
            GroupResponseType groupResponseType = null;
            GroupPriceResponseType groupPriceResponse = null;

            if (functionSpaceEnabled) {
                groupResponseType = new GroupResponseType();

            } else {
                groupPriceResponse = new GroupPriceResponseType();
            }

            // Look up the limits to make the suggested rate "reasonable"
            GroupPricingConfigurationLimit groupPricingConfigurationLimit = groupPricingRateConfigurationService
                    .getGroupPricingConfigurationLimit();
            if (groupPricingConfigurationLimit == null) {
                groupPricingConfigurationLimit = new GroupPricingConfigurationLimit();
                groupPricingConfigurationLimit.setSundayDefaultMAR(BigDecimal.ZERO);
                groupPricingConfigurationLimit.setSundayUpperLimit(new BigDecimal(100));
            }

            // Used to Randomize rates
            Random random = new Random();

            XmlUtil xmlUtil = new XmlUtil();

            List<ArrDateDetails> arrivalDates = sasRequest.getGroupPriceRequest().getGroupPriceParams().getArrival();
            if (arrivalDates != null) {
                for (int i = 0; i < arrivalDates.size(); i++) {
                    ArrDateDetails arrDateDetail = arrivalDates.get(i);

                    /**
                     * Time to build some random results
                     */
                    ArrivalDateType arrivalDateType = new ArrivalDateType();
                    arrivalDateType.setDate(arrDateDetail.getArrivalDate().get(0));
                    arrivalDateType.setReferencePrice(BigDecimal.TEN);

                    if (functionSpaceEnabled) {
                        groupResponseType.getArrivalDate().add(arrivalDateType);
                    } else {
                        groupPriceResponse.getArrivalDate().add(arrivalDateType);
                    }

                    // Randomly determine suggested rate based on the default mar
                    // and upper limit
                    BigDecimal defaultMAR = groupPricingConfigurationLimit.getSundayDefaultMAR();
                    BigDecimal upperLimit = groupPricingConfigurationLimit.getSundayUpperLimit();
                    int suggestedRate = random.nextInt((upperLimit.intValue() - defaultMAR.intValue()) + 1)
                            + defaultMAR.intValue();
                    arrivalDateType.setOptimalRate(new BigDecimal(suggestedRate));
                    arrivalDateType.setOptimalFSRate(new BigDecimal(600));

                    if (groupEvaluation.isRoomTypeEvaluation()) {
                        handleRoomTypeMocks(groupEvaluation, arrivalDateType, suggestedRate);
                    }

                    if (i == arrivalDates.size() - 1) {
                        // The last arrival date is always profitable
                        arrivalDateType.setStatusCode(GroupEvaluationArrivalDateResultCode.ACCEPTABLE.getId());
                        arrivalDateType.setAvgWeightedMAR(defaultMAR);
                        arrivalDateType.setBreakevenRate(defaultMAR.add(BigDecimal.ONE));
                        arrivalDateType.setBreakevenFSRate(defaultMAR.add(BigDecimal.TEN));
                    } else if (i == arrivalDates.size() - 2) {
                        // The second to last arrival date has a mar configuration
                        // issue
                        arrivalDateType.setStatusCode(GroupEvaluationArrivalDateResultCode.CONFIG_ISSUE_MAR.getId());
                        arrivalDateType.setAvgWeightedMAR(arrivalDateType.getOptimalRate());
                        arrivalDateType.setBreakevenRate(arrivalDateType.getOptimalRate());
                        arrivalDateType.setBreakevenFSRate(defaultMAR.add(BigDecimal.TEN));
                    } else if (i == arrivalDates.size() - 3) {
                        // No rate is profitable - but we still show the average
                        // weighted mar?
                        arrivalDateType.setOptimalRate(BigDecimal.ZERO);
                        arrivalDateType.setBreakevenRate(BigDecimal.ZERO);
                        arrivalDateType.setBreakevenFSRate(BigDecimal.ZERO);
                        arrivalDateType.setAvgWeightedMAR(defaultMAR);
                        arrivalDateType.setStatusCode(GroupEvaluationArrivalDateResultCode.NOT_PROFITABLE.getId());
                    } else {
                        // Any other arrival dates will result in a valid rate of
                        // some sort
                        arrivalDateType.setAvgWeightedMAR(defaultMAR);
                        arrivalDateType.setBreakevenRate(defaultMAR.add(BigDecimal.ONE));
                        arrivalDateType.setStatusCode(GroupEvaluationArrivalDateResultCode.ACCEPTABLE.getId());
                    }

                    arrivalDateType.setIncRooms(new BigDecimal(random.nextInt(10)));

                    // Assume 10 net rooms and a profit percentage of 10
                    BigDecimal revenue = arrivalDateType.getOptimalRate().multiply(BigDecimal.TEN);
                    arrivalDateType.setNetIncRev(revenue.setScale(2, RoundingMode.HALF_UP));
                    arrivalDateType
                            .setNetIncProfit(revenue.multiply(BigDecimal.valueOf(0.9)).setScale(2, RoundingMode.HALF_UP));
                    arrivalDateType.setAncillaryProfit(new BigDecimal(random.nextInt(100)));
                    arrivalDateType.setAncillaryRev(new BigDecimal(random.nextInt(200)));
                    arrivalDateType.setCommissions(new BigDecimal(random.nextInt(100)));
                    arrivalDateType.setConcessions(new BigDecimal(random.nextInt(100)));
                    arrivalDateType.setConfBanqProfit(new BigDecimal(random.nextInt(200)));
                    arrivalDateType.setConfBanqRev(new BigDecimal(random.nextInt(100)));
                    arrivalDateType.setDisplacedProfit(new BigDecimal(random.nextInt(10)));
                    arrivalDateType.setGrossRoomRev(new BigDecimal(random.nextInt(300)));
                    arrivalDateType.setIncRoomProfit(new BigDecimal(random.nextInt(100)));
                    arrivalDateType.setNetIncRoomRev(new BigDecimal(random.nextInt(100)));
                    arrivalDateType.setNetRoomRev(new BigDecimal(random.nextInt(100)));
                    arrivalDateType.setRoomProfit(new BigDecimal(random.nextInt(300)));
                    arrivalDateType.setTransAncillaryProfit(new BigDecimal(random.nextInt(100)));
                    arrivalDateType.setTransAncillaryRev(new BigDecimal(random.nextInt(100)));
                    arrivalDateType.setGrossFSRev(new BigDecimal(random.nextInt(200)));
                    arrivalDateType.setNetFSRev(new BigDecimal(random.nextInt(100)));
                    arrivalDateType.setNetIncFSRev(new BigDecimal(random.nextInt(300)));
                    arrivalDateType.setFsProfit(new BigDecimal(random.nextInt(50)));
                    arrivalDateType.setIncFSProfit(new BigDecimal(random.nextInt(50)));
                    arrivalDateType.setDisplacedConfBanqProfit(new BigDecimal(random.nextInt(10)));
                    arrivalDateType.setDisplacedConfBanqRev(new BigDecimal(random.nextInt(20)));
                    arrivalDateType.setCostOfWalk(new BigDecimal(random.nextInt(150)));
                    arrivalDateType.setExpectedWalkedRoomNights(BigDecimal.valueOf(1));

                    ArrivalDateType.PreStay preStay = new ArrivalDateType.PreStay();
                    ArrivalDateType.PostStay postStay = new ArrivalDateType.PostStay();
                    arrivalDateType.setPreStay(preStay);
                    arrivalDateType.setPostStay(postStay);

                    preStay.setDisplacedRoomRev(new BigDecimal((200)));
                    preStay.setDisplacedRoomProfit(new BigDecimal((150)));
                    postStay.setDisplacedRoomRev(new BigDecimal(30));
                    postStay.setDisplacedRoomProfit(new BigDecimal(35));

                    if (groupEvaluation.isRoomTypeEvaluation()) {
                        ArrivalDateType.PreStay.RoomClass rc = new ArrivalDateType.PreStay.RoomClass();
                        rc.setRcID(1);
                        rc.setOccFcstWithGroup(new BigDecimal(230));
                        rc.setOccFcstWithoutGroup(new BigDecimal(240));
                        rc.setDisplacedRooms(BigDecimal.ONE);
                        arrivalDateType.getPreStay().getRoomClass().add(rc);
                        // Setup PostStay
                        ArrivalDateType.PostStay.RoomClass rcps = new ArrivalDateType.PostStay.RoomClass();
                        rcps.setOccFcstWithGroup(new BigDecimal(230));
                        rcps.setOccFcstWithoutGroup(new BigDecimal(240));
                        rcps.setDisplacedRooms(BigDecimal.ONE);
                        arrivalDateType.getPostStay().getRoomClass().add(rcps);

                    } else {
                        preStay.setOccFcstWithGroup(new BigDecimal((25)));
                        preStay.setOccFcstWithoutGroup(new BigDecimal((30)));
                        postStay.setOccFcstWithGroup(new BigDecimal(233));
                        postStay.setOccFcstWithoutGroup(new BigDecimal(273));
                    }

                    @SuppressWarnings("unchecked")
                    List<com.ideas.tetris.pacman.services.marketsegment.entity.ForecastGroup> forecastGroups = multiPropertyCrudService
                            .findByNamedQueryForSingleProperty(propertyId,
                                    com.ideas.tetris.pacman.services.marketsegment.entity.ForecastGroup.BY_PROPERTY,
                                    QueryParameter.with("propertyId", propertyId).parameters());

                    if (forecastGroups != null) {
                        for (com.ideas.tetris.pacman.services.marketsegment.entity.ForecastGroup fg : forecastGroups) {
                            ForecastGroup forecastGroup = new ForecastGroup();
                            forecastGroup.setFgID(fg.getId());
                            forecastGroup.setDisplacedRoomProfit(new BigDecimal(random.nextInt(10)));
                            forecastGroup.setDisplacedRoomRev(new BigDecimal(random.nextInt(10)));
                            arrivalDateType.getForecastGroup().add(forecastGroup);

                            Date arrivalDate = xmlUtil.convertXMLGregorianToDate(arrivalDateType.getDate());
                            LocalDate startDate = new LocalDate(arrivalDate);
                            // int windowSize = sasRequest.getGroupPriceRequest().getGroupPriceParams().getWindowSize();
                            int windowSize = 1;
                            startDate = startDate.minusDays(windowSize);

                            int numberOfNights = arrDateDetail.getNumNights();
                            LocalDate endDate = new LocalDate(arrivalDate);
                            endDate = endDate.plusDays(numberOfNights);

                            while (!startDate.isAfter(endDate)) {
                                OccupancyDate occupancyDate = new OccupancyDate();
                                occupancyDate.setDate(xmlUtil.convertDateToXMLGregorian(startDate.toDate()));

                                if (groupEvaluation.isRoomTypeEvaluation()) {
                                    List<OptimalRCRates> rcRates = arrivalDateType.getOptimalRCRates();
                                    for (OptimalRCRates rate : rcRates) {
                                        List<RoomClass> roomClasses = rate.getRoomClass();

                                        for (RoomClass roomClass : roomClasses) {

                                            com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.ArrivalDateType.ForecastGroup.OccupancyDate.RoomClass fgRoomClass = new com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.ArrivalDateType.ForecastGroup.OccupancyDate.RoomClass();

                                            fgRoomClass.setRcID(roomClass.getRcID());
                                            fgRoomClass.setOccFcstWithGroup(new BigDecimal(random.nextInt(10)));
                                            fgRoomClass.setOccFcstWithoutGroup(new BigDecimal(random.nextInt(10)));
                                            fgRoomClass.setDisplacedRooms(BigDecimal.valueOf(random.nextDouble())
                                                    .setScale(2, RoundingMode.HALF_UP));
                                            occupancyDate.getRoomClass().add(fgRoomClass);
                                        }
                                    }

                                } else {
                                    occupancyDate.setDisplacedRooms(
                                            BigDecimal.valueOf(random.nextDouble()).setScale(2, RoundingMode.HALF_UP));
                                    occupancyDate.setOccFcstWithGroup(new BigDecimal(random.nextInt(10)));
                                    occupancyDate.setOccFcstWithoutGroup(new BigDecimal(random.nextInt(10)));
                                }

                                forecastGroup.getOccupancyDate().add(occupancyDate);
                                startDate = startDate.plusDays(1);

                            }
                        }
                    }

                    Date arrivalDate = xmlUtil.convertXMLGregorianToDate(arrivalDateType.getDate());
                    LocalDate startDate = new LocalDate(arrivalDate);
                    // int windowSize = sasRequest.getGroupPriceRequest().getGroupPriceParams().getWindowSize();
                    // SAS is going to roll up pre/post stay, so just mock 1 day pre/post day for response
                    int windowSize = 1;
                    startDate = startDate.minusDays(windowSize);

                    int numberOfNights = arrDateDetail.getNumNights();
                    LocalDate endDate = new LocalDate(arrivalDate);
                    endDate = endDate.plusDays(numberOfNights + windowSize);

                    FunctionSpace functionSpace = new FunctionSpace();

                    while (!startDate.isAfter(endDate)) {

                        com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.ArrivalDateType.FunctionSpace.OccupancyDate occupancyDate = new com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.ArrivalDateType.FunctionSpace.OccupancyDate();

                        DayPart dayPart1 = new DayPart();
                        dayPart1.setDayPartID(2);
                        dayPart1.setDisplacedFSProfit(new BigDecimal(random.nextInt(13)));
                        dayPart1.setDisplacedFSRev(new BigDecimal(random.nextInt(12)));
                        dayPart1.setFsUtilFcstWithGroup(new BigDecimal(random.nextInt(15)));
                        dayPart1.setFsUtilFcstWithoutGroup(new BigDecimal(random.nextInt(18)));

                        occupancyDate.getDayPart().add(dayPart1);

                        DayPart dayPart2 = new DayPart();
                        dayPart2.setDayPartID(3);
                        dayPart2.setDisplacedFSProfit(new BigDecimal(random.nextInt(14)));
                        dayPart2.setDisplacedFSRev(new BigDecimal(random.nextInt(15)));
                        dayPart2.setFsUtilFcstWithGroup(new BigDecimal(random.nextInt(18)));
                        dayPart2.setFsUtilFcstWithoutGroup(new BigDecimal(random.nextInt(19)));

                        occupancyDate.getDayPart().add(dayPart2);
                        occupancyDate.setDate(xmlUtil.convertDateToXMLGregorian(startDate.toDate()));

                        functionSpace.getOccupancyDate().add(occupancyDate);
                        startDate = startDate.plusDays(1);
                    }

                    arrivalDateType.setFunctionSpace(functionSpace);
                }
            }

            SASResponse sasResponse = new SASResponse();

            if (functionSpaceEnabled) {
                sasResponse.setGroupResponse(groupResponseType);
            } else {
                sasResponse.setGroupPriceResponse(groupPriceResponse);
            }

            ResponseHeaderType responseHeader = new ResponseHeaderType();
            responseHeader.setRequestId(sasRequest.getRequestHeader().getRequestId());
            responseHeader.setPropertyId(sasRequest.getRequestHeader().getPropertyId());
            responseHeader.setOperationName(sasRequest.getRequestHeader().getOperationName());
            sasResponse.setResponseHeader(responseHeader);

            return sasResponse;
        }

        private void handleRoomTypeMocks(GroupEvaluation groupEvaluation, ArrivalDateType arrivalDateType,
                                         int suggestedRate) {

            // Randomly determine room class rates based on room types selected
            Set<GroupEvaluationRoomType> roomTypes = groupEvaluation.getGroupEvaluationRoomTypes();
            // Get unique Room Class IDs to determine number of room class optimal rates
            List<Integer> roomClassIDs = new ArrayList<Integer>();

            for (GroupEvaluationRoomType roomType : roomTypes) {
                if (!roomClassIDs.contains(roomType.getRoomType().getAccomClass().getId())) {
                    roomClassIDs.add(roomType.getRoomType().getAccomClass().getId());
                }
            }

            int offsetAmt = 0;
            OptimalRCRates optimalRcRates = new OptimalRCRates();

            for (Integer roomClassId : roomClassIDs) {
                RoomClass roomClass = new RoomClass();
                roomClass.setRcID(roomClassId);
                roomClass.setOptimalRate(new BigDecimal(suggestedRate).add(new BigDecimal(offsetAmt)));
                roomClass.setReferencePrice(BigDecimal.TEN);
                optimalRcRates.getRoomClass().add(roomClass);

                offsetAmt += 10;
            }

            arrivalDateType.getOptimalRCRates().add(optimalRcRates);
        }
    }
}
