package com.ideas.tetris.pacman.services.bestavailablerate;

import com.ideas.infra.tetris.security.domain.Role;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.dto.AccomTypeSummary;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.AccomClassDetails;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.AvailableRateDataForLOS;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BARAccomClassPriceMapDetails;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BARDecisionInfo;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BARDetails;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BARFilterCriteria;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BAROvrUnavailableDtRangeAndPriceDetails;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BarDecision;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BarDecisionDetails;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BarDecisionDetailsData;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BarDetailsInfo;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BarOverrideDetails;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.CompetitorInfo;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.LRAAffectedDateRange;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.UnqualifiedDemandForecastPrice;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dashboard.mapper.NumberUtils;
import com.ideas.tetris.pacman.services.datafeed.dto.RolePermission;
import com.ideas.tetris.pacman.services.datafeed.service.RolePermissionService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.decisiontranslator.DecisionUtils;
import com.ideas.tetris.pacman.services.demandoverride.entity.ArrivalDemandOverride;
import com.ideas.tetris.pacman.services.demandoverride.entity.LastRoomValue;
import com.ideas.tetris.pacman.services.demandoverride.entity.OccupancyDemandOverride;
import com.ideas.tetris.pacman.services.demandoverride.entity.WashOverride;
import com.ideas.tetris.pacman.services.pricing.ProductManagementService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.reports.pricing.PricingService;
import com.ideas.tetris.pacman.services.reports.pricing.dto.PricingByDay;
import com.ideas.tetris.pacman.services.reports.pricing.dto.PricingByLos;
import com.ideas.tetris.pacman.services.security.RoleService;
import com.ideas.tetris.pacman.services.security.login.util.RoleModulePermissionMapperUtil;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedDetails;
import com.ideas.tetris.pacman.services.webrate.entity.DailyBARDecisionDTO;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompetitors;
import com.ideas.tetris.pacman.services.webrate.service.WebrateShoppingDataService;
import com.ideas.tetris.pacman.util.Pagination;
import com.ideas.tetris.pacman.util.jdbc.RowUtil;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.time.LocalDateInterval;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.NotImplementedException;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;

import javax.persistence.Query;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.BAR_DECISION_VALUE_RATEOFDAY;
import static com.ideas.tetris.pacman.common.constants.Constants.BAR_OVRD_DISPLAY_COMPETITOR_VALUE_ABSOLUTE;
import static org.apache.commons.lang3.math.NumberUtils.isNumber;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class BarDecisionService {
    private static final Logger LOGGER = Logger.getLogger(BarDecisionService.class.getName());
    private static final String NONE_RATE_UNQUALIFIED_NAME = "None";
    private static final String ACCOM_CLASS_ID = "accomClassId";
    private static final String EXPIRATION_DATE = "expirationDate";
    private static final String END_DATE = "endDate";
    private static final String START_DATE = "startDate";
    private static final String PROPERTY_ID = "propertyId";
    private static final String LRV = "lrv";
    private static final String ROOMS_OUT_OF_ORDER = "roomsOutOfOrder";
    private static final String TOTAL_ACCOM_CAPACITY = "totalCapacity";
    private static final String OCCUPANCY_NBR = "occupancyNBR";
    private static final String OCCUPANCY_FORECAST_PERCENT = "occupancyForecastPercent";
    private static final String STATUS = "status";
    private static final String PRICE = "price";
    private static final String ALIAS = "alias";
    private static final String OCCUPANCY_DATE = "occupancyDate";
    private static final String RATE_PLAN_STATUS_ID = "ratePlanStatusId";
    private static final String REASON_TYPE_ID = "reasonTypeId";
    private static final String FLOOR_RATE_CODE = "floorRateCode";
    private static final String FLOOR_RATE_ID = "floorRateId";
    private static final String CEILING_RATE_CODE = "ceilingRateCode";
    private static final String CEILING_RATE_ID = "ceilingRateId";
    private static final String RATE_CODE = "rateCode";
    private static final String RATE_ID = "rateId";
    private static final String OVERRIDE = "override";
    private static final String LOS = "los";
    private static final String ARRIVAL_DATE = "arrivalDate";
    private static final String CAUGHT_UP_DATE = "caughtUpDate";
    private static final String COMPETITOR_ID = "competitorId";
    private static final String PRODUCT_ID = "productId";
    private static final String[] barRatesQueryColumns = new String[]{ARRIVAL_DATE, LOS, OVERRIDE, RATE_ID, RATE_CODE, FLOOR_RATE_ID,
            FLOOR_RATE_CODE, CEILING_RATE_ID, CEILING_RATE_CODE, REASON_TYPE_ID, RATE_PLAN_STATUS_ID};
    private static final String[] competitorRatesQueryColumns = new String[]{OCCUPANCY_DATE, LOS, ALIAS, PRICE, STATUS, COMPETITOR_ID};
    private static final String[] competitorRatesQueryColumnsAllAccomClass = new String[]{OCCUPANCY_DATE, LOS, ALIAS, PRICE, STATUS, COMPETITOR_ID, ACCOM_CLASS_ID, PRODUCT_ID};

    private static final String[] occFcstQueryColumns = new String[]{OCCUPANCY_DATE, OCCUPANCY_FORECAST_PERCENT, ROOMS_OUT_OF_ORDER};
    private static final String[] occFcstAndActualCapacityQueryColumns = new String[]{OCCUPANCY_DATE, OCCUPANCY_FORECAST_PERCENT,
            ROOMS_OUT_OF_ORDER, TOTAL_ACCOM_CAPACITY, OCCUPANCY_NBR};
    private static final String[] lrvQueryColumns = new String[]{OCCUPANCY_DATE, LRV};

    public static final String FETCH_LRA_AFFECTED_DATE_RANGE = "select MIN(arrivalDate) as startR, MAX(arrivalDate) as endR,los from ( "
            + "select decisiontable.Arrival_DT as arrivalDate ,los, DATEDIFF(D,ROW_NUMBER()over (order by cast(decisiontable.Arrival_DT as date)), "
            + "cast(decisiontable.Arrival_DT as date)) as dtrange from Decision_Bar_Output as decisiontable where decisiontable.Arrival_Dt "
            + "between :startDate and :endDate and Accom_Class_ID = :accomClassId and Decision_Reason_Type_ID = :decisionReasonTypeId and decisiontable.LOS IN (:los) ) as tabledata "
            + "group by dtrange,los order by startR,los";
    private static final String YYYY_MM_DD = "yyyy-MM-dd";

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;
    @Autowired
	private SQLHelper sqlHelper;
    @Autowired
	private PacmanConfigParamsService configService;
    @Autowired
	private PriceService priceService;
    @Autowired
	private DateService dateService;
    @Autowired
	private DecisionUtils decisionUtils;
    @Autowired
	private CloseHighestBarService closeHighestBarService;
    @Autowired
	private PricingService pricingService;
    @Autowired
	private WebrateShoppingDataService webrateShoppingDataService;
    @Autowired
	protected RolePermissionService rolePermissionService;
    @Autowired
	private PricingConfigurationService pricingConfigurationService;
    @Autowired
	private CPManagementService cpManagementService;
    @Autowired
	private RoleService roleService;
    @Autowired
	private JobServiceLocal jobService;
    @Autowired
	private PropertyService propertyService;
    @Autowired
    ProductManagementService managementService;


    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    public void setSQLHelper(SQLHelper sqlHelper) {
        this.sqlHelper = sqlHelper;
    }

    public void setConfigService(PacmanConfigParamsService configService) {
        this.configService = configService;
    }

    public void setCloseHighestBarService(CloseHighestBarService closeHighestBarService) {
        this.closeHighestBarService = closeHighestBarService;
    }

    public void setPriceService(PriceService priceService) {
        this.priceService = priceService;
    }

    public void setDateService(DateService dateService) {
        this.dateService = dateService;
    }

    public void setPricingService(PricingService pricingService) {
        this.pricingService = pricingService;
    }

    private Property getProperty() {
        return propertyService.getPropertyById(PacmanWorkContextHelper.getPropertyId());
    }

    public String triggerFutureBarDecisionsCleanupJob() {
        Property property = getProperty();
        HashMap<String, Object> parameters = new HashMap<>();
        parameters.put(JobParameterKey.CLIENT_CODE, property.getClient().getCode());
        parameters.put(JobParameterKey.PROPERTY_CODES, property.getCode());
        parameters.put(JobParameterKey.PROPERTY_STAGE, property.getStage().getCode());
        jobService.startGuaranteedNewInstance(JobName.FutureBarDecisionsCleanupJob, parameters);
        return "FutureBarDecisionsCleanupJob triggered!";
    }

    public String truncateDerivedPaceTableDecisions() {
        StringBuilder results = new StringBuilder();
        String truncated = "Truncated ";

        int count = crudService.executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[PACE_FPLOS_By_Hierarchy]");
        results.append(truncated).append(count).append(" PACE_FPLOS_By_Hierarchy records\n");

        count = crudService.executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[PACE_FPLOS_By_RoomType]");
        results.append(truncated).append(count).append(" PACE_FPLOS_By_RoomType records\n");

        count = crudService.executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[Pace_Decision_LRA_FPLOS]");
        results.append(truncated).append(count).append(" Pace_Decision_LRA_FPLOS records\n");

        count = crudService.executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[Pace_Decision_LRA_minLOS]");
        results.append(truncated).append(count).append(" Pace_Decision_LRA_minLOS records\n");

        count = crudService.executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[PACE_FPLOS_By_Rank]");
        results.append(truncated).append(count).append(" PACE_FPLOS_By_Rank records\n");

        count = crudService.executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[PACE_Bar_Output_NOTIFICATION]");
        results.append(truncated).append(count).append(" PACE_Bar_Output_NOTIFICATION records\n");

        count = crudService.executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[PACE_Bar_Output_Upload]");
        results.append(truncated).append(count).append(" PACE_Bar_Output_Upload records\n");

        LOGGER.info(results);
        return results.toString();
    }

    public String deleteFutureBarDecision() {
        // strip time out of date to get midnight
        Date caughtUpDate = LocalDate.fromDateFields(dateService.getCaughtUpDate()).toDate();
        StringBuilder results = new StringBuilder();
        String deleted = "deleted ";

        int count = deleteDecisionBarOutputRecords(caughtUpDate);
        results.append(deleted).append(count).append(" Decision_Bar_Output records\n");

        count = deleteDecisionBarOutputOverrideDetailsRecords(caughtUpDate);
        results.append(deleted).append(count).append(" Decision_Bar_Output_OVR_Details records\n");

        count = deleteDecisionBarOutputOverrideRecords(caughtUpDate);
        results.append(deleted).append(count).append(" Decision_Bar_Output_OVR records\n");

        count = deleteDecisionDailyBarOutputRecords(caughtUpDate);
        results.append(deleted).append(count).append(" Decision_Dailybar_Output records\n");
        LOGGER.info(results);
        return results.toString();
    }

    public String deleteFutureBarByLOSDecision() {
        // strip time out of date to get midnight
        Date caughtUpDate = LocalDate.fromDateFields(dateService.getCaughtUpDate()).toDate();
        StringBuilder results = new StringBuilder();
        String deleted = "deleted ";

        int count = deleteDecisionFplosByHierarchyRecords(caughtUpDate);
        results.append(deleted).append(count).append(" Decision_FPLOS_By_Hierarchy records\n");

        count = deleteDecisionFplosByRoomTypeRecords(caughtUpDate);
        results.append(deleted).append(count).append(" Decision_FPLOS_By_RoomType records\n");

        count = deleteDecisionLraFplosRecords(caughtUpDate);
        results.append(deleted).append(count).append(" Decision_LRA_FPLOS records\n");

        count = deleteDecisionLraMinLOSRecords(caughtUpDate);
        results.append(deleted).append(count).append(" Decision_LRA_minLOS records\n");
        LOGGER.info(results);
        return results.toString();
    }

    public String deleteFutureCPBarDecision() {
        // strip time out of date to get midnight
        Date caughtUpDate = LocalDate.fromDateFields(dateService.getCaughtUpDate()).toDate();
        StringBuilder results = new StringBuilder();
        String deleted = "deleted ";

        int count = deleteCPDecisionBarOutputRecords(caughtUpDate);
        results.append(deleted).append(count).append(" CP_Decision_Bar_Output records\n");

        count = deleteCPDecisionBarOutputOverrideRecords(caughtUpDate);
        results.append(deleted).append(count).append(" CP_Decision_Bar_Output_OVR records\n");
        LOGGER.info(results);
        return results.toString();
    }

    public int deleteCPDecisionBarOutputRecords(Date caughtUpDate) {
        Query query = crudService.getEntityManager().createNativeQuery("delete from CP_Decision_Bar_Output where Arrival_DT >= :caughtUpDate");
        query.setParameter(CAUGHT_UP_DATE, caughtUpDate);
        return query.executeUpdate();
    }

    public int deleteDecisionBarOutputRecords(Date caughtUpDate) {
        Query query = crudService.getEntityManager().createNativeQuery("delete from Decision_Bar_Output where Arrival_Dt >= :caughtUpDate");
        query.setParameter(CAUGHT_UP_DATE, caughtUpDate);
        return query.executeUpdate();
    }

    public int deleteCPDecisionBarOutputOverrideRecords(Date caughtUpDate) {
        Query query = crudService.getEntityManager().createNativeQuery("delete from CP_Decision_Bar_Output_OVR where Arrival_DT >= :caughtUpDate");
        query.setParameter(CAUGHT_UP_DATE, caughtUpDate);
        return query.executeUpdate();
    }

    public int deleteDecisionBarOutputOverrideRecords(Date caughtUpDate) {
        Query query = crudService.getEntityManager().createNativeQuery("delete from Decision_Bar_Output_OVR where Arrival_Dt >= :caughtUpDate");
        query.setParameter(CAUGHT_UP_DATE, caughtUpDate);
        return query.executeUpdate();
    }

    public int deleteDecisionBarOutputOverrideDetailsRecords(Date caughtUpDate) {
        Query query = crudService.getEntityManager().createNativeQuery("delete from Decision_Bar_Output_OVR_Details where Decision_Bar_Output_OVR_ID IN (select Decision_Bar_Output_OVR_ID from Decision_Bar_Output_OVR where Arrival_Dt >= :caughtUpDate)");
        query.setParameter(CAUGHT_UP_DATE, caughtUpDate);
        return query.executeUpdate();
    }

    public int deleteDecisionFplosByHierarchyRecords(Date caughtUpDate) {
        Query query = crudService.getEntityManager().createNativeQuery("delete from Decision_FPLOS_By_Hierarchy where Arrival_Dt >= :caughtUpDate");
        query.setParameter(CAUGHT_UP_DATE, caughtUpDate);
        return query.executeUpdate();
    }

    public int deleteDecisionFplosByRoomTypeRecords(Date caughtUpDate) {
        Query query = crudService.getEntityManager().createNativeQuery("delete from Decision_FPLOS_By_RoomType where Arrival_Dt >= :caughtUpDate");
        query.setParameter(CAUGHT_UP_DATE, caughtUpDate);
        return query.executeUpdate();
    }

    public int deleteDecisionLraFplosRecords(Date caughtUpDate) {
        Query query = crudService.getEntityManager().createNativeQuery("delete from Decision_LRA_FPLOS where Arrival_Dt >= :caughtUpDate");
        query.setParameter(CAUGHT_UP_DATE, caughtUpDate);
        return query.executeUpdate();
    }

    public int deleteDecisionLraMinLOSRecords(Date caughtUpDate) {
        Query query = crudService.getEntityManager().createNativeQuery("delete from Decision_LRA_minLOS where Arrival_Dt >= :caughtUpDate");
        query.setParameter(CAUGHT_UP_DATE, caughtUpDate);
        return query.executeUpdate();
    }

    public int deleteDecisionDailyBarOutputRecords(Date caughtUpDate) {
        Query query = crudService.getEntityManager().createNativeQuery("delete from Decision_Dailybar_Output where Occupancy_Date >= :caughtUpDate");
        query.setParameter(CAUGHT_UP_DATE, caughtUpDate);
        return query.executeUpdate();
    }

    public int deletePaceFplosByHierarchyRecords() {
        return crudService.executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[PACE_FPLOS_By_Hierarchy]");
    }

    public int deletePaceFplosByRoomTypeRecords() {
        return crudService.executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[PACE_FPLOS_By_RoomType]");
    }

    public int deletePaceLraFplosRecords() {
        return crudService.executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[Pace_Decision_LRA_FPLOS]");
    }

    public int deletePaceLraMinLOSRecords() {
        return crudService.executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[Pace_Decision_LRA_minLOS]");
    }

    public String getAbsoluteCompetitorAlias() {
        String competitorDisplay = configService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value());
        if (competitorDisplay.equals(BAR_OVRD_DISPLAY_COMPETITOR_VALUE_ABSOLUTE)) {
            String name = configService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_ABSOLUTE_COMPETITOR.value());
            WebrateCompetitors competitor = crudService.findByNamedQuerySingleResult(
                    WebrateCompetitors.BY_PROPERTY_ID_AND_ALIAS, QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                            .and("competitorAlias", name).parameters());
            return competitor.getWebrateCompetitorsAlias();
        }
        return null;
    }


    public BARDetails getBARDetails(int accomClassId, Date arrivalDate) {
        return getBARDetails(accomClassId, arrivalDate, getDefaultLOS());
    }


    public BARDetails getBARDetailsByDateRange(int accomClassId, Date startDate, Date endDate) {
        return getBARDetailsByDateRange(accomClassId, startDate, endDate, getDefaultLOS());
    }

    public BARDetails getBARDetailsByDateRangeByRoomType(int accomClassId, List<Integer> accomTypeIds, Date startDate, Date endDate, int lengthOfStay) {
        return getBarDetails(accomClassId, startDate, endDate, lengthOfStay, accomTypeIds);
    }

    public BARDetails getBARDetailsByDateRange(int accomClassId, Date startDate, Date endDate, int lengthOfStay) {
        List<Integer> accomTypeIds = crudService.findByNamedQuery(AccomType.ID_BY_ACCOM_CLASS_ID, QueryParameter.with(ACCOM_CLASS_ID, accomClassId).parameters());

        return getBarDetails(accomClassId, startDate, endDate, lengthOfStay, accomTypeIds);
    }
    public BARDetails getBARDetailsByDateRangeForActiveRoomTypes(int accomClassId, Date startDate, Date endDate, int lengthOfStay) {
        List<Integer> accomTypeIds = crudService.findByNamedQuery(AccomType.ACTIVE_ID_BY_ACCOM_CLASS_ID, QueryParameter.with(ACCOM_CLASS_ID, accomClassId).parameters());

        return getBarDetails(accomClassId, startDate, endDate, lengthOfStay, accomTypeIds);
    }

    private BARDetails getBarDetails(int accomClassId, Date startDate, Date endDate, int lengthOfStay, List<Integer> accomTypeIds) {
        BARDetails result = null;
        Integer originalAccomClassId = accomClassId;
        if (isSingleBarDecisionEnabled()) {
            accomClassId = getMasterClassId();
        }

        List availableRates = getAvailableRates(accomClassId, startDate, endDate, lengthOfStay);
        Set<Integer> ratePlanIds = (Set<Integer>) availableRates.stream().map(o -> (Integer) ((Object[]) o)[0]).collect(Collectors.toSet());
        List<RateUnqualifiedDetails> rateUnqualifiedDetails = priceService.getRateUnqualifiedDetails(new ArrayList<Integer>(ratePlanIds), startDate, endDate, accomTypeIds);

        for (int i = 0; i < availableRates.size(); i++) {
            if (result == null) {
                result = new BARDetails();
            }
            Object[] resultArr = (Object[]) availableRates.get(i);
            result.getRemainingDemandByRateUnqualifiedName().put((String) resultArr[1], (BigDecimal) resultArr[2]);

            Integer rateUnqualifiedId = (Integer) resultArr[0];
            Date arrivalDate = (Date) resultArr[3];
            List<RateUnqualifiedDetails> rateUnqualifiedDetailsList = rateUnqualifiedDetails.stream()
                    .filter(rud -> rud.getRateUnqualifiedId().equals(rateUnqualifiedId) &&
                            DateUtil.removeTimeFromDate(rud.getStartDate()).compareTo(arrivalDate) <= 0 &&
                            DateUtil.removeTimeFromDate(rud.getEndDate()).compareTo(arrivalDate) >= 0).collect(Collectors.toList());

            Map<String, BigDecimal> priceMap = priceService.getPriceMap(arrivalDate, accomTypeIds, rateUnqualifiedDetailsList);

            if (null != priceMap && !priceMap.isEmpty()) {
                result.getPriceMapByRateUnqualifiedName().put((String) resultArr[1], priceMap);
            }
        }

        if (result != null) {
            result.setConflictingRatePlanNames(sqlHelper.buildQueryForUnavailableRates(accomClassId, startDate, lengthOfStay)
                    .getResultList());
        }

        if (configService.getBooleanParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED.value())) {
            if (result == null) {
                result = new BARDetails();
            }
            result.setRestrictHighestBARSelected(closeHighestBarService.isClosedForHighestBar(originalAccomClassId, startDate, endDate, lengthOfStay));
            result.setAccomTypeSummaryList(closeHighestBarService.getAccomTypes(originalAccomClassId));
        }
        return result;
    }

    public BAROvrUnavailableDtRangeAndPriceDetails getUnavailableBarDecisionDatesMock(int accomClassId, int specificId, int floorId,
                                                                                      DateParameter startDate, DateParameter endDate, List<Integer> lengthOfStay) {
        // TODO:: fix me
        // Multiday override works only when this service call returns data
        // Hence, trying to add logic so that I can make Floor/ Specif Multiday override in UI
        // Remove this when implemented.
        String overrrideType = "";
        if (floorId != 0) {
            specificId = floorId;
            overrrideType = Constants.BARDECISIONOVERRIDE_FLOOR;
        } else {
            overrrideType = Constants.BARDECISIONOVERRIDE_USER;
        }
        return getUnavailableBarDecisionDates(accomClassId, specificId, overrrideType, startDate, endDate, lengthOfStay);
    }


    public BAROvrUnavailableDtRangeAndPriceDetails getUnavailableBarDecisionDates(int accomClassId,
                                                                                  int specificId,
                                                                                  int floorId,
                                                                                  int ceilingId,
                                                                                  String start,
                                                                                  String end,
                                                                                  String los) {
        try {
            Date startDate = DateUtil.parseDate(start, YYYY_MM_DD);
            Date endDate = DateUtil.parseDate(end, YYYY_MM_DD);
            String[] values = los.split(",");
            List<Integer> lengthOfStay = new ArrayList<>();
            for (String s : values) {
                lengthOfStay.add(Integer.valueOf(s));
            }
            return getUnavailableBarDecisionDates(accomClassId, specificId, floorId, ceilingId, startDate, endDate, lengthOfStay);
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error parsing parameters", e);
        }
    }


    public BAROvrUnavailableDtRangeAndPriceDetails getUnavailableBarDecisionDates(int accomClassId, int specificId, int floorId,
                                                                                  int ceilingId, DateParameter startDate, DateParameter endDate, List<Integer> lengthOfStay) {
        return getUnavailableBarDecisionDates(accomClassId, specificId, floorId, ceilingId, startDate.getTime(), endDate.getTime(),
                lengthOfStay);
    }

    public BAROvrUnavailableDtRangeAndPriceDetails getUnavailableBarDecisionDates(int accomClassId, int specificId, int floorId,
                                                                                  int ceilingId, Date startDate, Date endDate, List<Integer> lengthOfStay) {

        if (!SystemConfig.isOverrideForValidRateEnabled()) {
            throw new NotImplementedException(); // is this ever not enabled?
        }

        BAROvrUnavailableDtRangeAndPriceDetails result = new BAROvrUnavailableDtRangeAndPriceDetails();
        Map<Integer, List<AvailableRateDataForLOS>> losDataMap = null;

        if (specificId > 0) {
            losDataMap = createLosMap(accomClassId, lengthOfStay, startDate, endDate, specificId);
        } else {
            if (floorId > 0) {
                losDataMap = createLosMap(accomClassId, lengthOfStay, startDate, endDate, floorId);
            }
            if (ceilingId > 0) {
                Map<Integer, List<AvailableRateDataForLOS>> ceilingLosMap = createLosMap(accomClassId, lengthOfStay, startDate, endDate,
                        ceilingId);

                if (losDataMap != null) {
                    for (Entry<Integer, List<AvailableRateDataForLOS>> entry : losDataMap.entrySet()) {
                        // compare ceiling and floor availability lists and only keep if it's found in both
                        // sorry about the mess
                        List<AvailableRateDataForLOS> newList = new ArrayList<AvailableRateDataForLOS>();

                        // if the ceiling list allows any available dates at all...
                        Integer los = entry.getKey();
                        if (ceilingLosMap.containsKey(los)) {
                            // get the available ceiling dates
                            List<Date> ceilingDates = ceilingLosMap.get(los).stream().map(AvailableRateDataForLOS::getArrivalDate).collect(Collectors.toList());

                            // and only keep if there is date is available for floor and ceiling
                            for (AvailableRateDataForLOS floorAvailableRateData : entry.getValue()) {
                                if (ceilingDates.contains(floorAvailableRateData.getArrivalDate())) {
                                    newList.add(floorAvailableRateData);
                                }
                            }
                        }

                        // replace the list
                        losDataMap.put(los, newList);
                    }
                } else {
                    losDataMap = ceilingLosMap;
                }
            }
        }

        result.setAvailableBarRatesByLOS(losDataMap);
        SortedMap<Date, List<Integer>> datesWithLOS = identifyGap(losDataMap, startDate, endDate, lengthOfStay);
        formatGapsAccordingToRangeAndLOSdata(result, datesWithLOS);

        Date currentDate = startDate;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd-MMM-yyyy");

        if (floorId > 0) { // do we need to do this for ceiling also?
            Map<String, BARAccomClassPriceMapDetails> dataMap = new HashMap<String, BARAccomClassPriceMapDetails>();
            BARAccomClassPriceMapDetails priceDetailsByDate;

            while (DateUtil.isDateBetween(startDate, endDate, currentDate)) {
                Map<String, BigDecimal> priceMap = priceService.getPriceMap(floorId, accomClassId, currentDate);

                if (dataMap.containsKey(currentDate)) {
                    priceDetailsByDate = dataMap.get(currentDate);
                    priceDetailsByDate.getAccomClassWithPriceDetails().putAll(priceMap);
                } else if (!datesWithLOS.containsKey(currentDate)) {
                    priceDetailsByDate = new BARAccomClassPriceMapDetails();
                    priceDetailsByDate.setAccomClassWithPriceDetails(priceMap);
                    priceDetailsByDate.setArrivalDate(currentDate);
                    dataMap.put(simpleDateFormat.format(currentDate), priceDetailsByDate);
                }

                currentDate = DateUtil.getNextDay(currentDate);
            }

            result.setDateAndPriceMapByRateUnqualifiedName(dataMap);
        }

        return result;
    }


    public BAROvrUnavailableDtRangeAndPriceDetails getUnavailableBarDecisionDates(int accomClassId,
                                                                                  int rateUnqualifiedId,
                                                                                  String overrideType,
                                                                                  String start,
                                                                                  String end,
                                                                                  String los) {
        try {
            Date startDate = DateUtil.parseDate(start, YYYY_MM_DD);
            Date endDate = DateUtil.parseDate(end, YYYY_MM_DD);
            String[] values = los.split(",");
            List<Integer> lengthOfStay = new ArrayList<>();
            for (String s : values) {
                lengthOfStay.add(Integer.valueOf(s));
            }
            return getUnavailableBarDecisionDates(accomClassId, rateUnqualifiedId, overrideType, startDate, endDate, lengthOfStay);
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Parsing parameters", e);
        }
    }


    public BAROvrUnavailableDtRangeAndPriceDetails getUnavailableBarDecisionDates(int accomClassId, int rateUnqualifiedId,
                                                                                  String overrideType, Date startDate, Date endDate, List<Integer> lengthOfStay) {
        if (SystemConfig.isOverrideForValidRateEnabled()) {
            return getUnavailableBarDecisionDatesByLOS(accomClassId, rateUnqualifiedId, overrideType, startDate, endDate, lengthOfStay);
        } else {
            return getUnavailableBarDecisonDatesOLD(accomClassId, rateUnqualifiedId, overrideType, startDate, endDate, lengthOfStay);
        }
    }

    /**
     * @param accomClassId
     * @param rateUnqualifiedId
     * @param overrideType
     * @param startDate
     * @param endDate
     * @param lengthOfStay
     * @return
     * @deprecated
     */
    @Deprecated
    @SuppressWarnings({"rawtypes", "unchecked"})
    public BAROvrUnavailableDtRangeAndPriceDetails getUnavailableBarDecisonDatesOLD(int accomClassId, int rateUnqualifiedId,
                                                                                    String overrideType, Date startDate, Date endDate, List<Integer> lengthOfStay) {
        BAROvrUnavailableDtRangeAndPriceDetails result = new BAROvrUnavailableDtRangeAndPriceDetails();

        Query query = sqlHelper.buildQueryForUnavailableBarDecisionDates(rateUnqualifiedId, accomClassId, startDate, endDate);
        List resultList = query.getResultList();

        List<Map> formattedList = new ArrayList<Map>();

        for (int i = 0; i < resultList.size(); i++) {
            Object[] resultArr = (Object[]) resultList.get(i);
            Map rangeMap = new HashMap<String, String>();
            rangeMap.put(START_DATE, resultArr[0]);
            rangeMap.put(END_DATE, resultArr[1]);
            formattedList.add(rangeMap);
        }

        result.setUnavailableDateRanges(formattedList);
        Date tempStartDate = startDate;
        if (null != overrideType && "Floor".equals(overrideType)) {
            Map<String, BARAccomClassPriceMapDetails> dataMap = new HashMap<String, BARAccomClassPriceMapDetails>();
            BARAccomClassPriceMapDetails priceDetailsByDate;
            while (DateUtil.isDateBetween(startDate, endDate, tempStartDate)) {
                Map<String, BigDecimal> priceMap = priceService.getPriceMap(rateUnqualifiedId, accomClassId, tempStartDate);
                if (dataMap.containsKey(tempStartDate)) {
                    priceDetailsByDate = dataMap.get(tempStartDate);
                    priceDetailsByDate.getAccomClassWithPriceDetails().putAll(priceMap);
                } else {
                    priceDetailsByDate = new BARAccomClassPriceMapDetails();
                    priceDetailsByDate.setAccomClassWithPriceDetails(priceMap);
                    priceDetailsByDate.setArrivalDate(tempStartDate);
                    dataMap.put(tempStartDate.toString(), priceDetailsByDate);
                }
                tempStartDate = DateUtil.getNextDay(tempStartDate);
            }

            result.setDateAndPriceMapByRateUnqualifiedName(dataMap);
        }

        return result;
    }

    /**
     * @param accomClassId
     * @param rateUnqualifiedId
     * @param overrideType
     * @param startDate
     * @param endDate
     * @param lengthOfStay
     * @return
     */
    @SuppressWarnings("unchecked")
    private BAROvrUnavailableDtRangeAndPriceDetails getUnavailableBarDecisionDatesByLOS(int accomClassId, int rateUnqualifiedId,
                                                                                        String overrideType, Date startDate, Date endDate, List<Integer> lengthOfStay) {
        BAROvrUnavailableDtRangeAndPriceDetails result = new BAROvrUnavailableDtRangeAndPriceDetails();
        Map<Integer, List<AvailableRateDataForLOS>> losDataMap = createLosMap(accomClassId, lengthOfStay, startDate, endDate, rateUnqualifiedId);

        result.setAvailableBarRatesByLOS(losDataMap);
        SortedMap<Date, List<Integer>> datesWithLOS = identifyGap(losDataMap, startDate, endDate, lengthOfStay);
        formatGapsAccordingToRangeAndLOSdata(result, datesWithLOS);

        Date tempStartDate = startDate;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd-MMM-yyyy");
        if ("Floor".equals(overrideType)) {
            Map<String, BARAccomClassPriceMapDetails> dataMap = new HashMap<String, BARAccomClassPriceMapDetails>();
            BARAccomClassPriceMapDetails priceDetailsByDate;
            while (DateUtil.isDateBetween(startDate, endDate, tempStartDate)) {
                Map<String, BigDecimal> priceMap = priceService.getPriceMap(rateUnqualifiedId, accomClassId, tempStartDate);
                if (dataMap.containsKey(tempStartDate)) {
                    priceDetailsByDate = dataMap.get(tempStartDate);
                    priceDetailsByDate.getAccomClassWithPriceDetails().putAll(priceMap);
                } else if (!datesWithLOS.containsKey(tempStartDate)) {
                    priceDetailsByDate = new BARAccomClassPriceMapDetails();
                    priceDetailsByDate.setAccomClassWithPriceDetails(priceMap);
                    priceDetailsByDate.setArrivalDate(tempStartDate);
                    dataMap.put(simpleDateFormat.format(tempStartDate), priceDetailsByDate);
                }
                tempStartDate = DateUtil.getNextDay(tempStartDate);
            }

            result.setDateAndPriceMapByRateUnqualifiedName(dataMap);
        }

        return result;
    }

    private SortedMap<Date, List<Integer>> identifyGap(Map<Integer, List<AvailableRateDataForLOS>> availableRateForLos, Date startDate,
                                                       Date endDate, List<Integer> lengthOfStay) {
        Date currentDate = startDate;

        SortedMap<Date, List<Integer>> datesWithLOS = new TreeMap<Date, List<Integer>>();

        while (DateUtil.isDateBetween(startDate, endDate, currentDate)) {
            for (Integer los : lengthOfStay) {
                List<AvailableRateDataForLOS> availableRates = availableRateForLos.get(los);
                boolean isDataAvialble = false;
                if (null != availableRates && !availableRates.isEmpty()) {
                    isDataAvialble = false;
                    for (AvailableRateDataForLOS availableRateDataForLOS : availableRates) {
                        Date arrivalDate = availableRateDataForLOS.getArrivalDate();
                        if (0 == currentDate.compareTo(arrivalDate)) {
                            isDataAvialble = true;
                            break;
                        }
                    }
                }

                if (!isDataAvialble) {
                    if (datesWithLOS.containsKey(currentDate)) {
                        List<Integer> losList = datesWithLOS.get(currentDate);
                        losList.add(los);
                    } else {
                        List<Integer> losList = new ArrayList<Integer>();
                        losList.add(los);
                        if (null != currentDate) {
                            datesWithLOS.put(currentDate, losList);
                        }
                    }
                }
            }
            currentDate = DateUtil.getNextDay(currentDate);
        }

        return datesWithLOS;
    }

    private void formatGapsAccordingToRangeAndLOSdata(BAROvrUnavailableDtRangeAndPriceDetails result, SortedMap<Date, List<Integer>> datesWithLOS) {

        Date startDateRange = null;
        Date endDateRange = null;
        List<Map<String, List<Integer>>> unavailableRateListByDateRangeAndLOS = new ArrayList<Map<String, List<Integer>>>();
        List<Map<LocalDateInterval, List<Integer>>> unavailableRateListByLocalDateIntervalAndLOS = new ArrayList<Map<LocalDateInterval, List<Integer>>>();

        SimpleDateFormat dateFormat = new SimpleDateFormat(SystemConfig.isUserDefinedDateFormatEnabled() ? dateService
                .getUserDateFormat() : "MM/dd/yyyy");

        HashMap<String, List<Integer>> finalDataMap = new LinkedHashMap<String, List<Integer>>();
        HashMap<LocalDateInterval, List<Integer>> finalLocalDateIntervalMap = new LinkedHashMap<LocalDateInterval, List<Integer>>();

        StringBuilder dateRangeKey = new StringBuilder();

        for (Entry<Date, List<Integer>> dateForLOS : datesWithLOS.entrySet()) {
            Date stDate = dateForLOS.getKey();
            List<Integer> orignalValues = new ArrayList<Integer>(dateForLOS.getValue());
            if (null != startDateRange && null != endDateRange && DateUtil.isDateBetween(startDateRange, endDateRange, stDate)) {
                continue;
            }
            startDateRange = stDate;
            endDateRange = stDate;
            Date tempDate = stDate;

            for (Map.Entry<Date, List<Integer>> dateForLOS1 : datesWithLOS.entrySet()) {
                Date stDate1 = dateForLOS1.getKey();
                List<Integer> values = dateForLOS1.getValue();
                long diffDays = DateUtil.daysBetween(tempDate, stDate1);
                if (0 == diffDays) {
                    endDateRange = stDate1;
                } else if (diffDays == 1) {
                    boolean isDataPreset = orignalValues.containsAll(values);
                    if (isDataPreset && orignalValues.size() == values.size()) {
                        endDateRange = stDate1;
                        tempDate = stDate1;
                    } else {
                        break;
                    }
                } else {
                    break;
                }
            }

            dateRangeKey.append(dateFormat.format(startDateRange)).append(" - ");
            dateRangeKey.append(dateFormat.format(endDateRange));

            LocalDateInterval interval = new LocalDateInterval(new LocalDate(startDateRange), new LocalDate(endDateRange));

            if (!finalDataMap.containsKey(dateRangeKey)) {
                Map<String, List<Integer>> tempDataMap = new HashMap<String, List<Integer>>();
                if (startDateRange.compareTo(endDateRange) == 0) {
                    finalDataMap.put(dateRangeKey.toString(), dateForLOS.getValue());
                    tempDataMap.put(dateRangeKey.toString(), dateForLOS.getValue());
                } else {
                    finalDataMap.put(dateRangeKey.toString(), orignalValues);
                    tempDataMap.put(dateRangeKey.toString(), dateForLOS.getValue());
                }
                unavailableRateListByDateRangeAndLOS.add(tempDataMap);
            }
            if (!finalLocalDateIntervalMap.containsKey(interval)) {
                Map<LocalDateInterval, List<Integer>> tempDataMap = new HashMap<LocalDateInterval, List<Integer>>();
                if (startDateRange.compareTo(endDateRange) == 0) {
                    finalLocalDateIntervalMap.put(interval, dateForLOS.getValue());
                    tempDataMap.put(interval, dateForLOS.getValue());
                } else {
                    finalLocalDateIntervalMap.put(interval, orignalValues);
                    tempDataMap.put(interval, dateForLOS.getValue());
                }
                unavailableRateListByLocalDateIntervalAndLOS.add(tempDataMap);
            }
            dateRangeKey = new StringBuilder();
        }
        result.setUnavailableRateListByLocalDateIntervalAndLOS(unavailableRateListByLocalDateIntervalAndLOS);
        result.setUnavailableRateListByDateRangeAndLOS(unavailableRateListByDateRangeAndLOS);
    }

    @SuppressWarnings("unchecked")

    public BARDetails getBARDetails(int accomClassId, Date arrivalDate, int lengthOfStay) {
        BARDetails result = null;

        List<UnqualifiedDemandForecastPrice> l = null;
        if (isSingleBarDecisionEnabled()) {
            l = crudService.findByNamedQuery(UnqualifiedDemandForecastPrice.BY_ARRIVALDATE_AND_ACCOMCLASSID_AND_LOS,
                    QueryParameter.with(ARRIVAL_DATE, arrivalDate).and(ACCOM_CLASS_ID, getMasterClassId())
                            .and("lengthOfStay", lengthOfStay).parameters());
        } else {
            l = crudService.findByNamedQuery(UnqualifiedDemandForecastPrice.BY_ARRIVALDATE_AND_ACCOMCLASSID_AND_LOS,
                    QueryParameter.with(ARRIVAL_DATE, arrivalDate).and(ACCOM_CLASS_ID, accomClassId).and("lengthOfStay", lengthOfStay)
                            .parameters());
        }

        for (int i = 0; i < l.size(); i++) {
            if (result == null) {
                result = new BARDetails();
            }
            result.getRemainingDemandByRateUnqualifiedName().put(l.get(i).getRateUnqualified().getName(), l.get(i).getRemainingDemand());
            result.getPriceMapByRateUnqualifiedName().put(l.get(i).getRateUnqualified().getName(),
                    priceService.getPriceMap(l.get(i).getRateUnqualified().getId(), accomClassId, arrivalDate));
        }

        if (result != null) {
            result.setConflictingRatePlanNames(sqlHelper.buildQueryForUnavailableRates(accomClassId, arrivalDate, lengthOfStay)
                    .getResultList());

            if (configService.getBooleanParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED.value())) {
                result.setRestrictHighestBARSelected(closeHighestBarService.isClosedForHighestBar(accomClassId, arrivalDate, arrivalDate, lengthOfStay));
            }
        }

        return result;
    }

    public List getAvailableRates(int accomClassId, Date startDate, Date endDate, int lengthOfStay) {
        Query query = sqlHelper.buildQueryForValidBarRatesByDateRange(accomClassId, startDate, endDate, lengthOfStay);
        return query.getResultList();

    }

    protected int getMasterClassId() {
        AccomClass masterClass = crudService.findByNamedQuerySingleResult(AccomClass.GET_MASTER_CLASS,
                QueryParameter.with(PROPERTY_ID, PacmanThreadLocalContextHolder.getWorkContext().getPropertyId()).parameters());
        return masterClass.getId();
    }

    private boolean isSingleBarDecisionEnabled() {
        String s = configService.getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value());
        return "true".equalsIgnoreCase(s);
    }


    @SuppressWarnings("unchecked")
    public Map<Date, BARDecisionInfo> getBarDecisions(int accomClassId, int lengthOfStay, Date startDate, Date endDate) {
        Map<Date, BARDecisionInfo> result = new LinkedHashMap<>();
        Map<Date, List<AccomTypeSummary>> closedRoomTypes = new HashMap<>();
        boolean isLV0Closed = false;
        Map<String, Object> parameters = QueryParameter.with(ACCOM_CLASS_ID, accomClassId).and("lengthOfStay", lengthOfStay)
                .and(START_DATE, startDate).and(END_DATE, endDate).parameters();
        List<DecisionBAROutput> barDecisions = crudService.findByNamedQuery(DecisionBAROutput.BY_ARRIVALDATERANGE_AND_ACCOMCLASSID_AND_LOS,
                parameters);
        if (CollectionUtils.isNotEmpty(barDecisions)) {
            isLV0Closed = configService.getBooleanParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED.value());
        }

        if (isLV0Closed) {
            closedRoomTypes = closeHighestBarService.getSelectedAccomTypes(accomClassId, lengthOfStay, startDate, endDate);
        }

        boolean ceilingOverrideEnabled = configService.getBooleanParameterValue(FeatureTogglesConfigParamName.CEILING_OVERRIDE_ENABLED);
        for (DecisionBAROutput d : barDecisions) {
            final BARDecisionInfo barDecisionInfo = buildBARDecisionInfo(d, ceilingOverrideEnabled);
            if (isLV0Closed) {
                barDecisionInfo.setRestrictHighestBarOverride(closedRoomTypes.containsKey(d.getArrivalDate()));
                barDecisionInfo.setClosedRoomTypes(closedRoomTypes.get(d.getArrivalDate()));
            }
            result.put(d.getArrivalDate(), barDecisionInfo);
        }

        return result;
    }


    @SuppressWarnings("unchecked")
    public Map<Date, Map<Integer, BARDecisionInfo>> getBarDecisionsByArrivalDateAndLengthOfStayUsingMaxLOS(int accomClassId,
                                                                                                           int maxLengthOfStay, Date startDate, Date endDate) {
        Map<Date, Map<Integer, BARDecisionInfo>> result = new HashMap<Date, Map<Integer, BARDecisionInfo>>();
        Boolean isLV0Closed = configService.getBooleanParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED.value());
        List<DecisionBAROutput> barDecisions = crudService.findByNamedQuery(
                DecisionBAROutput.BY_ARRIVALDATERANGE_AND_ACCOMCLASSID_AND_LESS_THAN_OR_EQUAL_LOS,
                QueryParameter.with(START_DATE, startDate).and(END_DATE, endDate).and(ACCOM_CLASS_ID, accomClassId)
                        .and("maxLengthOfStay", maxLengthOfStay).parameters());
        if (barDecisions != null) {
            Map<Date, Map<Integer, List<AccomTypeSummary>>> closedRoomTypes = isLV0Closed ? closeHighestBarService.getRoomTypesForMaxLOS(accomClassId, maxLengthOfStay, startDate, endDate) : new HashMap<>();
            boolean ceilingOverrideEnabled = configService.getBooleanParameterValue(FeatureTogglesConfigParamName.CEILING_OVERRIDE_ENABLED);
            for (DecisionBAROutput d : barDecisions) {
                Map<Integer, BARDecisionInfo> arrivalDateBarDecisions = result.get(d.getArrivalDate());

                if (arrivalDateBarDecisions == null) {
                    arrivalDateBarDecisions = new HashMap<Integer, BARDecisionInfo>();
                    result.put(d.getArrivalDate(), arrivalDateBarDecisions);
                }

                BARDecisionInfo barDecisionInfo = buildBARDecisionInfo(d, ceilingOverrideEnabled);
                if (isLV0Closed) {
                    barDecisionInfo.setClosedRoomTypes(closedRoomTypes.get(d.getArrivalDate()) == null ? null : closedRoomTypes.get(d.getArrivalDate()).get(d.getLengthOfStay()));
                    barDecisionInfo.setRestrictHighestBarOverride(CollectionUtils.isNotEmpty(barDecisionInfo.getClosedRoomTypes()));

                }
                arrivalDateBarDecisions.put(barDecisionInfo.getLengthOfStay(), barDecisionInfo);
            }
        }
        return result;
    }

    public Map<Date, Map<Integer, Map<Integer, BARDecisionInfo>>> getBarDecisionsForDateRangeAndLengthOfStayUsingMaxLOS(Integer maxLengthOfStay, Date startDate, Date endDate) {
        Map<Date, Map<Integer, Map<Integer, BARDecisionInfo>>> result = new HashMap<>();

        Boolean isLV0Closed = configService.getBooleanParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED.value());

        Map<String, Object> parameters = QueryParameter.with(START_DATE, startDate).and(END_DATE, endDate).and("maxLengthOfStay", maxLengthOfStay).parameters();
        List<DecisionBAROutput> decisionBAROutputs = crudService.findByNamedQuery(DecisionBAROutput.BY_ARRIVALDATERANGE_AND_All_ACCOMCLASS_AND_LESS_THAN_OR_EQUAL_LOS, parameters);

        if (decisionBAROutputs != null) {
            Map<Date, Map<Integer, Map<Integer, List<AccomTypeSummary>>>> closedRoomTypes = isLV0Closed ? closeHighestBarService.getRoomTypesForMaxLOSForAllAccomClasses(maxLengthOfStay, startDate, endDate) : new HashMap<>();
            boolean ceilingOverrideEnabled = configService.getBooleanParameterValue(FeatureTogglesConfigParamName.CEILING_OVERRIDE_ENABLED);

            for (DecisionBAROutput decisionBAROutput : decisionBAROutputs) {
                if (!result.containsKey(decisionBAROutput.getArrivalDate())) {
                    result.put(decisionBAROutput.getArrivalDate(), new HashMap<>());
                }
                Map<Integer, Map<Integer, BARDecisionInfo>> arrivalDateBarDecisions = result.get(decisionBAROutput.getArrivalDate());

                if (!arrivalDateBarDecisions.containsKey(decisionBAROutput.getAccomClassId())) {
                    arrivalDateBarDecisions.put(decisionBAROutput.getAccomClassId(), new HashMap<>());
                }
                Map<Integer, BARDecisionInfo> accomClassToBarDecision = arrivalDateBarDecisions.get(decisionBAROutput.getAccomClassId());

                BARDecisionInfo barDecisionInfo = buildBARDecisionInfo(decisionBAROutput, ceilingOverrideEnabled);
                if (isLV0Closed) {
                    barDecisionInfo.setClosedRoomTypes(isAccomTypeSummaryAbsent(closedRoomTypes, decisionBAROutput) ? null : closedRoomTypes.get(decisionBAROutput.getArrivalDate()).get(decisionBAROutput.getAccomClassId()).get(decisionBAROutput.getLengthOfStay()));
                    barDecisionInfo.setRestrictHighestBarOverride(CollectionUtils.isNotEmpty(barDecisionInfo.getClosedRoomTypes()));
                }
                accomClassToBarDecision.put(barDecisionInfo.getLengthOfStay(), barDecisionInfo);
            }
        }
        return result;
    }

    private boolean isAccomTypeSummaryAbsent(Map<Date, Map<Integer, Map<Integer, List<AccomTypeSummary>>>> closedRoomTypes, DecisionBAROutput decisionBAROutput) {
        Map<Integer, Map<Integer, List<AccomTypeSummary>>> dateToAccomClassSummary = closedRoomTypes.get(decisionBAROutput.getArrivalDate());
        return dateToAccomClassSummary == null || dateToAccomClassSummary.get(decisionBAROutput.getAccomClassId()) == null;
    }


    @SuppressWarnings("unchecked")
    //todo unused method should get removed
    public Map<Date, Map<Integer, BARDecisionInfo>> getBarDecisions(BARFilterCriteria criteria) {
        Map<Date, Map<Integer, BARDecisionInfo>> result = new HashMap<>();
        boolean isLV0Closed = false;
        Query q = sqlHelper.buildQueryForBarRates(criteria);
        List<Object[]> resultList = q.getResultList();
        if (CollectionUtils.isNotEmpty(resultList)) {
            isLV0Closed = configService.getBooleanParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_RESTRICTED_ENABLED.value());
        }

        for (Object[] row : resultList) {
            Map<String, Object> rowValues = RowUtil.convertToMap(row, barRatesQueryColumns);
            rowValues.put(ACCOM_CLASS_ID, criteria.getAccomClassId());

            Date arrivalDate = (Date) rowValues.get(ARRIVAL_DATE);
            result.computeIfAbsent(arrivalDate, k -> new HashMap<>());

            Integer los = (Integer) rowValues.get(LOS);
            if (result.get(arrivalDate).get(los) == null) {
                final BARDecisionInfo barDecisionInfo = buildBARDecisionInfoFromRowValues(rowValues);
                if (isLV0Closed) {
                    barDecisionInfo.setRestrictHighestBarOverride(closeHighestBarService.isClosedForHighestBar(barDecisionInfo.getAccomClassId(), barDecisionInfo.getArrivalDate(), barDecisionInfo.getArrivalDate(), los));
                }
                result.get(arrivalDate).put(los, barDecisionInfo);
            }
        }
        return result;
    }

    private BARDecisionInfo buildBARDecisionInfoFromRowValues(Map<String, Object> rowValues) {
        BARDecisionInfo result = new BARDecisionInfo();
        result.setArrivalDate((Date) rowValues.get(ARRIVAL_DATE));
        result.setLengthOfStay((Integer) rowValues.get(LOS));
        result.setOverride((String) rowValues.get(OVERRIDE));
        result.setRatePlanId((Integer) rowValues.get(RATE_ID));
        result.setRatePlanName((String) rowValues.get(RATE_CODE));
        result.setFloorRatePlanId((Integer) rowValues.get(FLOOR_RATE_ID));
        result.setFloorRatePlanName((String) rowValues.get(FLOOR_RATE_CODE));
        result.setCeilingRatePlanId((Integer) rowValues.get(CEILING_RATE_ID));
        result.setCeilingRatePlanName((String) rowValues.get(CEILING_RATE_CODE));
        result.setReasonTypeId((Integer) rowValues.get(REASON_TYPE_ID));
        result.setRatePlanStatusId((Integer) rowValues.get(RATE_PLAN_STATUS_ID));
        result.setAccomClassId((Integer) rowValues.get(ACCOM_CLASS_ID));
        return result;
    }

    @SuppressWarnings("unchecked")

    public Map<Date, BARDecisionInfo> getBarDecisionWithConflictingOverrides(int accomClassId, int lengthOfStay, Date startDate,
                                                                             Date endDate) {
        BARFilterCriteria criteria = new BARFilterCriteria();
        criteria.setAccomClassId(accomClassId);
        criteria.setStartDate(startDate);
        criteria.setEndDate(endDate);
        criteria.setIncludedDaysOfWeek(Arrays.asList(1, 2, 3, 4, 5, 6, 7));
        criteria.setIncludedLengthsOfStay(Collections.singletonList(lengthOfStay));
        criteria.setOnlyShowDatesWithOverridesConflictingRateStrategy(true);

        Map<Date, BARDecisionInfo> result = new HashMap<>();

        Query q = sqlHelper.buildQueryForBarRates(criteria);
        List<Object[]> resultList = q.getResultList();
        for (Object[] row : resultList) {
            Map<String, Object> rowValues = RowUtil.convertToMap(row, barRatesQueryColumns);
            Date arrivalDate = (Date) rowValues.get(ARRIVAL_DATE);
            result.computeIfAbsent(arrivalDate, k -> buildBARDecisionInfoFromRowValues(rowValues));
        }
        return result;
    }

    @SuppressWarnings("unchecked")
    public Map<Date, Map<Integer, CompetitorInfo>> getCompetitorInfo(BARFilterCriteria criteria) {
        Map<Date, Map<Integer, CompetitorInfo>> result = new HashMap<>();
        if ("true".equalsIgnoreCase(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value()))) {
            String competitorDisplay = configService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value());

            if (criteria.getIncludedLengthsOfStay().remove(Integer.valueOf(-1))) {
                criteria.getIncludedLengthsOfStay().add(1);
            }
            Query q = sqlHelper.buildQueryForCompetitorRates(criteria, competitorDisplay, getSelfCompetitor());
            List<Object[]> resultList = q.getResultList();
            for (Object[] row : resultList) {
                populateCompetitorData(result, competitorDisplay, row);
            }
        }
        return result;
    }

    protected void populateCompetitorData(Map<Date, Map<Integer, CompetitorInfo>> result, String competitorDisplay, Object[] row) {
        Map<String, Object> rowValues = RowUtil.convertToMap(row, competitorRatesQueryColumns);
        rowValues.put("competitorDisplay", competitorDisplay);
        Date occupancyDate = (Date) row[0];
        result.computeIfAbsent(occupancyDate, k -> new HashMap<>());
        BigDecimal los = (BigDecimal) row[1];
        if (result.get(occupancyDate).get(los.intValue()) == null) {
            result.get(occupancyDate).put(los.intValue(), buildCompetitorInfoFromRowValues(rowValues));
        }
        result.get(occupancyDate).get(los.intValue()).getCompetitorNames().add((String) rowValues.get(ALIAS));
    }


    @SuppressWarnings("unchecked")
    public Map<Date, CompetitorInfo> getCompetitorInfo(int accomClassId, int lengthOfStay, Date startDate, Date endDate) {
        Map<Date, CompetitorInfo> result = new HashMap<>();
        if ("true".equalsIgnoreCase(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value()))) {
            String competitorDisplay = configService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value());
            BARFilterCriteria criteria = createBarFilterCriteria(accomClassId, lengthOfStay, startDate, endDate);
            Query q = sqlHelper.buildQueryForCompetitorRates(criteria, competitorDisplay, getSelfCompetitor());
            List<Object[]> resultList = q.getResultList();
            populateCompetitorData(result, competitorDisplay, resultList);
        }
        return result;
    }

    public Map<Integer, Map<Date, CompetitorInfo>> getCompetitorInfoMap(int lengthOfStay, Date startDate, Date endDate) {
        Map<Integer, Map<Date, CompetitorInfo>> result = new HashMap<>();
        if ("true".equalsIgnoreCase(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value()))) {
            String competitorDisplay = configService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value());
            BARFilterCriteria criteria = createBarFilterCriteria(0, lengthOfStay, startDate, endDate);
            Query q = sqlHelper.buildQueryForCompetitorRates(criteria, competitorDisplay, getSelfCompetitor());
            List<Object[]> resultList = q.getResultList();
            populateCompetitorDataMap(result, competitorDisplay, resultList);
        }
        return result;
    }

    public Map<Integer, Map<Date, Map<Integer, CompetitorInfo>>> getCompetitorInfoMapDayCardLayout(int lengthOfStay, Date startDate, Date endDate) {
        Map<Integer, Map<Date, Map<Integer, CompetitorInfo>>> result = new HashMap<>();
        if ("true".equalsIgnoreCase(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value()))) {
            String competitorDisplay = configService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value());
            BARFilterCriteria criteria = createBarFilterCriteria(0, lengthOfStay, startDate, endDate);
            Query q = sqlHelper.buildQueryForCompetitorRates(criteria, competitorDisplay, getSelfCompetitor());
            List<Object[]> resultList = q.getResultList();
            populateCompetitorDataMapDayCardLayout(result, competitorDisplay, resultList);
        }
        return result;
    }

    public Map<Integer, Map<Date, Map<Integer, CompetitorInfo>>> getCompetitorInfoMapDayCardLayout(Set<Integer> lengthsOfStay, Date startDate, Date endDate) {
        Map<Integer, Map<Date, Map<Integer, CompetitorInfo>>> result = new HashMap<>();
        if ("true".equalsIgnoreCase(configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value()))) {
            String competitorDisplay = configService.getParameterValue(IPConfigParamName.BAR_BAR_OVRD_DISPLAY_COMPETITOR.value());
            BARFilterCriteria criteria = createBarFilterCriteria(0, lengthsOfStay, startDate, endDate);
            Query q = sqlHelper.buildQueryForCompetitorRates(criteria, competitorDisplay, getSelfCompetitor());
            List<Object[]> resultList = q.getResultList();
            populateCompetitorDataMapDayCardLayout(result, competitorDisplay, resultList);
        }
        return result;
    }


    private Integer getSelfCompetitor() {
        WebrateCompetitors selfCompetitor = new WebrateCompetitors();
        String webRateHotelId = configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS);
        if (StringUtils.isNotBlank(webRateHotelId)) {
            selfCompetitor = crudService.findByNamedQuerySingleResult(
                    WebrateCompetitors.IDS_PROPERTY_ID_AND_WRHOTEL_ID, QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                            .and("webrateHotelID", webRateHotelId).parameters());
        }
        return null != selfCompetitor ? selfCompetitor.getId() : null;
    }

    protected BARFilterCriteria createBarFilterCriteria(int accomClassId, int lengthOfStay, Date startDate, Date endDate) {
        BARFilterCriteria criteria = new BARFilterCriteria();
        criteria.setAccomClassId(accomClassId);
        criteria.setStartDate(startDate);
        criteria.setEndDate(endDate);
        if (lengthOfStay == -1) {
            lengthOfStay = 1;
        }
        criteria.setIncludedLengthsOfStay(Collections.singletonList(lengthOfStay));
        criteria.setIncludedDaysOfWeek(Arrays.asList(1, 2, 3, 4, 5, 6, 7));
        return criteria;
    }

    protected BARFilterCriteria createBarFilterCriteria(int accomClassId, Set<Integer> lengthsOfStay, Date startDate, Date endDate) {
        BARFilterCriteria criteria = new BARFilterCriteria();
        criteria.setAccomClassId(accomClassId);
        criteria.setStartDate(startDate);
        criteria.setEndDate(endDate);
        criteria.setIncludedLengthsOfStay(new ArrayList<>(lengthsOfStay));
        criteria.setIncludedDaysOfWeek(Arrays.asList(1, 2, 3, 4, 5, 6, 7));
        return criteria;
    }

    protected void populateCompetitorData(Map<Date, CompetitorInfo> result, String competitorDisplay, List<Object[]> resultList) {
        for (Object[] row : resultList) {
            Map<String, Object> rowValues = RowUtil.convertToMap(row, competitorRatesQueryColumns);
            rowValues.put("competitorDisplay", competitorDisplay);
            Date occupancyDate = (Date) rowValues.get(OCCUPANCY_DATE);
            result.computeIfAbsent(occupancyDate, k -> buildCompetitorInfoFromRowValues(rowValues));
            result.get(occupancyDate).getCompetitorNames().add((String) rowValues.get(ALIAS));
            result.get(occupancyDate).getCompetitorIds().add((Integer) rowValues.get(COMPETITOR_ID));
        }
    }

    protected void populateCompetitorDataMap(Map<Integer, Map<Date, CompetitorInfo>> result, String competitorDisplay, List<Object[]> resultList) {
        for (Object[] row : resultList) {
            Map<String, Object> rowValues = RowUtil.convertToMap(row, competitorRatesQueryColumnsAllAccomClass);
            rowValues.put("competitorDisplay", competitorDisplay);
            Date occupancyDate = (Date) rowValues.get(OCCUPANCY_DATE);
            Integer accomClassId = (Integer) rowValues.get(ACCOM_CLASS_ID);
            result.computeIfAbsent(accomClassId, ignored -> new HashMap<>());
            Map<Date, CompetitorInfo> accomClassResult = result.get(accomClassId);
            accomClassResult.computeIfAbsent(occupancyDate, k -> buildCompetitorInfoFromRowValues(rowValues));
            accomClassResult.get(occupancyDate).getCompetitorNames().add((String) rowValues.get(ALIAS));
            accomClassResult.get(occupancyDate).getCompetitorIds().add((Integer) rowValues.get(COMPETITOR_ID));
            accomClassResult.get(occupancyDate).getProductId().add((BigInteger) rowValues.get(PRODUCT_ID));
        }
    }

    protected void populateCompetitorDataMapDayCardLayout(Map<Integer, Map<Date, Map<Integer, CompetitorInfo>>> result, String competitorDisplay, List<Object[]> resultList) {
        for (Object[] row : resultList) {
            Map<String, Object> rowValues = RowUtil.convertToMap(row, competitorRatesQueryColumnsAllAccomClass);
            rowValues.put("competitorDisplay", competitorDisplay);
            Date occupancyDate = (Date) rowValues.get(OCCUPANCY_DATE);
            Integer accomClassId = (Integer) rowValues.get(ACCOM_CLASS_ID);
            result.computeIfAbsent(accomClassId, ignored -> new HashMap<>());
            Map<Date, Map<Integer, CompetitorInfo>> accomClassResultForDate = result.get(accomClassId);
            accomClassResultForDate.computeIfAbsent(occupancyDate, k -> new HashMap<>());

            Map<Integer, CompetitorInfo> competitorInfoMap = accomClassResultForDate.get(occupancyDate);
            BigInteger productId = (BigInteger) rowValues.get(PRODUCT_ID);
            competitorInfoMap.computeIfAbsent(productId.intValue(), k -> buildCompetitorInfoFromRowValues(rowValues));
        }
    }

    private CompetitorInfo buildCompetitorInfoFromRowValues(Map<String, Object> rowValues) {
        CompetitorInfo result = new CompetitorInfo();
        Date occupancyDate = (Date) rowValues.get(OCCUPANCY_DATE);
        BigDecimal los = (BigDecimal) rowValues.get(LOS);
        String alias = (String) rowValues.get(ALIAS);
        BigDecimal compPriceInDecimal = (BigDecimal) rowValues.get(PRICE);
        String price = compPriceInDecimal.setScale(2, RoundingMode.HALF_UP).toString();
        String status = (String) rowValues.get(STATUS);
        String competitorDisplay = (String) rowValues.get("competitorDisplay");
        Integer competitorId = (Integer) rowValues.get(COMPETITOR_ID);
        BigInteger productId = (BigInteger) rowValues.get(PRODUCT_ID);
        if (competitorDisplay.equals(BAR_OVRD_DISPLAY_COMPETITOR_VALUE_ABSOLUTE)) {
            if (status.equals(Constants.WEBRATE_ACTIVE_STATUS_CODE)) {
                result.setCompetitorPrice(price);
            } else if (status.equals(Constants.WEBRATE_CLOSED_STATUS_CODE)) {
                result.setCompetitorPrice(Constants.WEBRATE_CLOSED_STATUS);
            } else if (status.equals(Constants.WEBRATE_FENCED_STATUS_CODE)) {
                result.setCompetitorPrice(Constants.WEBRATE_RATE_RESTRICTED_STATUS);
            } else {
                result.setCompetitorPrice(status);
            }
        } else {
            result.setCompetitorPrice(price);
        }
        result.setWebrateStatus(status);
        result.setArrivalDate(occupancyDate);
        result.setLengthOfStay(los.intValue());
        result.getCompetitorNames().add(alias);
        result.getCompetitorIds().add(competitorId);
        result.setProductId(productId);
        return result;
    }

    @SuppressWarnings("unchecked")
    public Map<Date, BARDecisionInfo> getEarlierBarDecisions(Date timestamp, int accomClassId, int lengthOfStay, Date startDate,
                                                             Date endDate) {

        Map<Date, BARDecisionInfo> result = new HashMap<>();

        BARFilterCriteria criteria = new BARFilterCriteria();
        criteria.setAccomClassId(accomClassId);
        criteria.setStartDate(startDate);
        criteria.setEndDate(endDate);
        criteria.setIncludedLengthsOfStay(Collections.singletonList(lengthOfStay));
        criteria.setIncludedDaysOfWeek(Arrays.asList(1, 2, 3, 4, 5, 6, 7));
        criteria.setLastViewedTime(timestamp);

        Query q = sqlHelper.buildQueryForPaceBarRates(criteria);
        List<Object[]> resultList = q.getResultList();
        for (Object[] row : resultList) {
            Map<String, Object> rowValues = RowUtil.convertToMap(row, barRatesQueryColumns);
            rowValues.put(ACCOM_CLASS_ID, criteria.getAccomClassId());

            Date arrivalDate = (Date) rowValues.get(ARRIVAL_DATE);
            result.computeIfAbsent(arrivalDate, k -> buildBARDecisionInfoFromRowValues(rowValues));
        }
        return result;
    }

    @SuppressWarnings("unchecked")
    public Map<Date, Map<Integer, BARDecisionInfo>> getEarlierBarDecisions(BARFilterCriteria criteria) {
        Map<Date, Map<Integer, BARDecisionInfo>> result = new HashMap<>();

        Query q = sqlHelper.buildQueryForPaceBarRates(criteria);
        List<Object[]> resultList = q.getResultList();
        for (Object[] row : resultList) {
            Map<String, Object> rowValues = RowUtil.convertToMap(row, barRatesQueryColumns);
            rowValues.put(ACCOM_CLASS_ID, criteria.getAccomClassId());

            Date arrivalDate = (Date) rowValues.get(ARRIVAL_DATE);
            Integer los = (Integer) rowValues.get(LOS);

            result.computeIfAbsent(arrivalDate, k -> new HashMap<>());
            if (result.get(arrivalDate).get(los) == null) {
                result.get(arrivalDate).put(los, buildBARDecisionInfoFromRowValues(rowValues));
            }
        }
        return result;
    }

    public Map<Date, Float> getOccupancyForecast(Date startDate, Date endDate) {
        BARFilterCriteria criteria = getBarFilterCriteria(startDate, endDate);
        return getOccupancyForecast(criteria);
    }

    @SuppressWarnings("unchecked")
    public Map<Date, Float> getOccupancyForecast(BARFilterCriteria criteria) {
        List<Object[]> resultList = getOccupancyForecastAndRoomsOOOData(criteria);
        return getOccupancyForecast(resultList);
    }

    public Map<Date, Float> getOccupancyForecast(List<Object[]> resultList) {
        boolean enablePhysicalCapacityConsideration = configService.isEnablePhysicalCapacityConsideration();
        Map<Date, Float> result = new HashMap<>();
        for (Object[] row : resultList) {
            Map<String, Object> rowValues;
            rowValues = RowUtil.convertToMap(row, enablePhysicalCapacityConsideration ? occFcstAndActualCapacityQueryColumns : occFcstQueryColumns);
            Date occupancyDate = (Date) rowValues.get(OCCUPANCY_DATE);
            Float percent;
            if (enablePhysicalCapacityConsideration) {
                BigDecimal occupancyNumber = (BigDecimal) rowValues.get(OCCUPANCY_NBR);
                BigDecimal totalAccomCapacity = (BigDecimal) rowValues.get(TOTAL_ACCOM_CAPACITY);
                if (occupancyNumber == null || totalAccomCapacity == null || totalAccomCapacity.intValue() == 0) {
                    percent = (float) 0;
                } else {
                    percent = occupancyNumber.divide(totalAccomCapacity, 4, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.valueOf(100).setScale(2)).floatValue();
                }
            } else {
                percent = (rowValues.get(OCCUPANCY_FORECAST_PERCENT)) == null ? (float) 0 : ((BigDecimal) rowValues.get(OCCUPANCY_FORECAST_PERCENT)).floatValue();
            }
            result.put(occupancyDate, percent);
        }
        return result;
    }

    public List<Object[]> getOccupancyForecastAndRoomsOOOData(Date startDate, Date endDate) {
        BARFilterCriteria criteria = getBarFilterCriteria(startDate, endDate);
        return getOccupancyForecastAndRoomsOOOData(criteria);
    }

    @SuppressWarnings("unchecked")
    private List<Object[]> getOccupancyForecastAndRoomsOOOData(BARFilterCriteria criteria) {
        Query q = sqlHelper.buildQueryForOccupancyForecastAndRoomsOutOfOrder(criteria);
        return (List<Object[]>) q.getResultList();
    }

    public Map<Date, Integer> getRoomsOutOfOrder(Date startDate, Date endDate) {
        BARFilterCriteria criteria = getBarFilterCriteria(startDate, endDate);
        return getRoomsOutOfOrder(criteria);
    }

    private BARFilterCriteria getBarFilterCriteria(Date startDate, Date endDate) {
        BARFilterCriteria criteria = new BARFilterCriteria();
        criteria.setStartDate(startDate);
        criteria.setEndDate(endDate);
        return criteria;
    }

    @SuppressWarnings("unchecked")
    public Map<Date, Integer> getRoomsOutOfOrder(BARFilterCriteria criteria) {
        List<Object[]> resultList = getOccupancyForecastAndRoomsOOOData(criteria);
        return getRoomsOutOfOrder(resultList);
    }

    public Map<Date, Integer> getRoomsOutOfOrder(List<Object[]> resultList) {
        Map<Date, Integer> result = new HashMap<>();
        for (Object[] row : resultList) {
            Map<String, Object> rowValues = RowUtil.convertToMap(row, occFcstQueryColumns);
            Date occupancyDate = (Date) rowValues.get(OCCUPANCY_DATE);
            Integer ooo = ((BigDecimal) rowValues.get(ROOMS_OUT_OF_ORDER)).intValue();
            result.put(occupancyDate, ooo);
        }
        return result;
    }

    public BigDecimal getLastRoomValue(final int accomClassId, final Date occupancyDate, Integer lengthOfStay) {
        Map<Date, BigDecimal> lastRoomValue = getLastRoomValue(accomClassId, occupancyDate, occupancyDate, lengthOfStay);
        if (lastRoomValue.isEmpty()) {
            return null;
        }
        return lastRoomValue.entrySet().iterator().next().getValue();
    }

    public Map<Date, BigDecimal> getLastRoomValue(final int accomClassId, final Date startDate, final Date endDate, Integer lengthOfStay) {
        if (lengthOfStay > 1) {
            Map<Date, BigDecimal> result = new HashMap<>();
            int dayDiff = Integer.valueOf((int) DateUtil.daysBetween(startDate, endDate));
            Date endDateWithLos = DateUtil.addDaysToDate(endDate, lengthOfStay - 1);
            Map<String, BigDecimal> lrvMap = getLastRoomValues(accomClassId, startDate, endDateWithLos)
                    .stream().collect(Collectors.toMap(l -> DateUtil.sqlDate(l.getOccupancyDate()), LastRoomValue::getValue));
            for (int i = dayDiff; i >= 0; i--) {
                Date lrvDate = DateUtil.addDaysToDate(startDate, i);
                BigDecimal lrvValue = BigDecimal.ZERO;
                int dataCount = 0;
                for (int j = 0; j < lengthOfStay; j++) {
                    BigDecimal value = lrvMap.get(DateUtil.sqlDate(DateUtil.addDaysToDate(lrvDate, j)));
                    if (value != null) {
                        lrvValue = lrvValue.add(value);
                        dataCount++;
                    }
                }
                if (dataCount == lengthOfStay) {
                    result.put(lrvDate, lrvValue.divide(BigDecimal.valueOf(dataCount), 4, RoundingMode.HALF_UP));
                }
            }
            return result;
        } else {
            return getLastRoomValues(accomClassId, startDate, endDate)
                    .stream().collect(Collectors.toMap(LastRoomValue::getOccupancyDate, LastRoomValue::getValue));
        }
    }

    @SuppressWarnings({"unchecked", "serial"})
    public Map<Date, BigDecimal> getLastRoomValue(final int accomClassId, final Date startDate, final Date endDate) {
        List<LastRoomValue> lrvs = getLastRoomValues(accomClassId, startDate, endDate);
        return lrvs.stream().collect(Collectors.toMap(LastRoomValue::getOccupancyDate, LastRoomValue::getValue));
    }

    public List<LastRoomValue> getLastRoomValues(int accomClassId, Date startDate, Date endDate) {
        return crudService.findByNamedQuery(LastRoomValue.BY_OCCUPANCY_DATE_RANGE_AND_PROPERTY_ID_AND_ACCOM_CLASS_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and(ACCOM_CLASS_ID, accomClassId)
                        .and(START_DATE, startDate).and(END_DATE, endDate).parameters());
    }

    @SuppressWarnings("unchecked")
    public Map<Date, BigDecimal> getLastRoomValue(BARFilterCriteria criteria) {
        Map<Date, BigDecimal> result = new HashMap<>();

        Query q = sqlHelper.buildQueryForLastRoomValue(criteria);
        List<Object[]> resultList = q.getResultList();
        for (Object[] row : resultList) {
            Map<String, Object> rowValues = RowUtil.convertToMap(row, lrvQueryColumns);
            Date occupancyDate = (Date) rowValues.get(OCCUPANCY_DATE);
            BigDecimal lrv = (BigDecimal) rowValues.get(LRV);
            result.put(occupancyDate, lrv);
        }

        return result;
    }

    private BARDecisionInfo buildBARDecisionInfo(DecisionBAROutput d, boolean ceilingOverrideEnabled) {
        BARDecisionInfo o = new BARDecisionInfo();
        o.setArrivalDate(d.getArrivalDate());
        o.setAccomClassId(d.getAccomClassId());
        o.setLengthOfStay(d.getLengthOfStay());
        o.setOverride(d.getOverride());
        o.setRatePlanId(d.getRateUnqualified().getId());
        o.setRatePlanName(d.getRateUnqualified().getName());
        o.setRatePlanStatusId(d.getRateUnqualified().getStatusId());
        o.setReasonTypeId(d.getReasonTypeId());
        if (d.getOverride().equalsIgnoreCase(Constants.BARDECISIONOVERRIDE_USER)) {
            o.setSpecificRatePlanId(d.getRateUnqualified().getId());
            o.setSpecificRatePlanName(d.getRateUnqualified().getName());
        }
        if (d.getFloorRateUnqualified() != null) {
            o.setFloorRatePlanId(d.getFloorRateUnqualified().getId());
            o.setFloorRatePlanName(d.getFloorRateUnqualified().getName());
        }
        if (d.getCeilingRateUnqualified() != null) {
            o.setCeilingRatePlanId(d.getCeilingRateUnqualified().getId());
            o.setCeilingRatePlanName(d.getCeilingRateUnqualified().getName());
        }

        /*
         * Deleting a rate plan might set the floor/ceiling to None because older code requires that. The new UI with
         * BAR ceiling overrides requires these to be null instead of None.
         */
        if (ceilingOverrideEnabled) {
            if (NONE_RATE_UNQUALIFIED_NAME.equals(o.getFloorRatePlanName())) {
                o.setFloorRatePlanId(null);
                o.setFloorRatePlanName(null);
            }
            if (NONE_RATE_UNQUALIFIED_NAME.equals(o.getCeilingRatePlanName())) {
                o.setCeilingRatePlanId(null);
                o.setCeilingRatePlanName(null);
            }
        }

        return o;
    }

    public int getDefaultLOS() {
        if (configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value()).equals(BAR_DECISION_VALUE_RATEOFDAY)) {
            return -1;
        } else {
            return 1;
        }
    }


    @SuppressWarnings("unchecked")
    public Map<String, Integer> getRatePlanIds() {
        List<RateUnqualified> ratePlans = crudService.findByNamedQuery(RateUnqualified.BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
        return ratePlans.stream().collect(Collectors.toMap(RateUnqualified::getName, RateUnqualified::getId));
    }


    @SuppressWarnings("unchecked")
    //todo can use java 8
    public Map<Date, BARDecisionInfo> getArrivalOrOccupancyDemandOverride(Date startDate, Date endDate) {
        Map<Date, BARDecisionInfo> result = new HashMap<>();

        List<ArrivalDemandOverride> listArrivalDemandOverride = crudService.findByNamedQuery(
                ArrivalDemandOverride.BY_ARRIVAL_DATE_RANGE_AND_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and(START_DATE, startDate).and(END_DATE, endDate)
                        .parameters());

        ArrivalDemandOverride objArrivalDemandOverride;
        for (ArrivalDemandOverride aListArrivalDemandOverride : listArrivalDemandOverride) {

            objArrivalDemandOverride = aListArrivalDemandOverride;
            Date arrivalDate = objArrivalDemandOverride.getArrivalDate();
            result.computeIfAbsent(arrivalDate, k -> new BARDecisionInfo());
            result.get(arrivalDate).setArrivalDate(arrivalDate);
            result.get(arrivalDate).setAccomClassId(objArrivalDemandOverride.getAccomClassID());
            result.get(arrivalDate).setLengthOfStay(objArrivalDemandOverride.getLengthOfStay());
            result.get(arrivalDate).setOverride(Constants.BARDECISIONOVERRIDE_ARRIVAL);
            result.get(arrivalDate).setExistingArrivalDemandOverride(true);
        }

        List<OccupancyDemandOverride> listOccupancyDemandOverride = crudService.findByNamedQuery(
                OccupancyDemandOverride.BY_OCCUPANCY_DATE_RANGE_AND_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and(START_DATE, startDate).and(END_DATE, endDate)
                        .parameters());

        OccupancyDemandOverride objOccupancyDemandOverride;

        for (OccupancyDemandOverride aListOccupancyDemandOverride : listOccupancyDemandOverride) {
            objOccupancyDemandOverride = aListOccupancyDemandOverride;
            Date arrivalDate = objOccupancyDemandOverride.getOccupancyDate();
            result.computeIfAbsent(arrivalDate, k -> new BARDecisionInfo());
            result.get(arrivalDate).setArrivalDate(arrivalDate);
            result.get(arrivalDate).setAccomClassId(objOccupancyDemandOverride.getAccomClassID());
            result.get(arrivalDate).setOverride(Constants.BARDECISIONOVERRIDE_OCCUPANCY);
            result.get(arrivalDate).setExistingOccupancyDemandOverride(true);
        }
        return result;
    }


    public List<BARDecisionInfo> getNonDecisions(Date startDate, Date endDate) {
        List<BARDecisionInfo> result = new ArrayList<>();

        Map<String, Object> parameters = QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                .and(START_DATE, startDate).and(END_DATE, endDate).parameters();
        List<DecisionBAROutput> barDecisions = crudService.findByNamedQuery(
                DecisionBAROutput.NON_DECISIONS_BY_PROPERTY_ID_AND_ARRIVALDATERANGE, parameters);

        boolean ceilingOverrideEnabled = configService.getBooleanParameterValue(FeatureTogglesConfigParamName.CEILING_OVERRIDE_ENABLED);
        for (DecisionBAROutput dbo : barDecisions) {
            result.add(buildBARDecisionInfo(dbo, ceilingOverrideEnabled));
        }
        return result;
    }


    public boolean nonDecisionsPresent(Date startDate, Date endDate) {
        Map<String, Object> parameters = QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                .and(START_DATE, startDate).and(END_DATE, endDate).parameters();
        List<DecisionBAROutput> barDecisions = crudService.findByNamedQuery(
                DecisionBAROutput.NON_DECISIONS_BY_PROPERTY_ID_AND_ARRIVALDATERANGE, parameters);
        return !barDecisions.isEmpty();
    }


    @SuppressWarnings("unchecked")
    //todo can use java 8
    public Map<Date, BARDecisionInfo> getWashOverrides(Date startDate, Date endDate) {
        Map<Date, BARDecisionInfo> result = new HashMap<>();
        List<WashOverride> listWashOverrides = crudService
                .findByNamedQuery(
                        WashOverride.BY_OCCUPANCY_DATE_RANGE_AND_PROPERTY_ID_FUTURE_EXPIRATION,
                        QueryParameter.with(PROPERTY_ID, PacmanThreadLocalContextHolder.getWorkContext().getPropertyId())
                                .and(START_DATE, startDate).and(END_DATE, endDate).and(EXPIRATION_DATE, dateService.getCaughtUpDate())
                                .parameters());
        WashOverride objWashOverride = null;
        for (int i = 0; i < listWashOverrides.size(); i++) {

            objWashOverride = listWashOverrides.get(i);
            Date occupancyDate = objWashOverride.getOccupancyDate();
            result.computeIfAbsent(occupancyDate, k -> new BARDecisionInfo());
            result.get(occupancyDate).setArrivalDate(occupancyDate);
            result.get(occupancyDate).setOverride(Constants.BARDECISIONOVERRIDE_WASH);
        }
        return result;
    }

    private static String GET_ROOMS_ON_BOOKS_QUERY = "select AA.Occupancy_DT, SUM(Rooms_Sold) from Accom_Activity AA inner join Accom_Type AT "
            + " on (AA.Accom_Type_ID = AT.Accom_Type_ID) and at.isComponentRoom ='N' "
            + " where AA.Property_ID =:propertyId  and AA.Occupancy_DT >=:startDate and AA.Occupancy_DT <=:endDate "
            + " group by AA.Occupancy_DT order by AA.Occupancy_DT";

    /**
     * This method returns the Rooms On Books values for the Given date range.
     *
     * @return
     */
    @SuppressWarnings("unchecked")
    public Map<Date, BigDecimal> getRoomsOnBooks(BARFilterCriteria criteria) {
        Map<Date, BigDecimal> result = new HashMap<>();
        Query q = crudService.getEntityManager().createNativeQuery(GET_ROOMS_ON_BOOKS_QUERY);
        q.setParameter(END_DATE, criteria.getEndDate());
        q.setParameter(START_DATE, criteria.getStartDate());
        q.setParameter("propertyId", PacmanThreadLocalContextHolder.getWorkContext().getPropertyId());

        List<Object[]> listAccomActivity = q.getResultList();

        for (Object[] objAccomActivity : listAccomActivity) {
            result.put((Date) objAccomActivity[0], (BigDecimal) objAccomActivity[1]);
        }

        return result;
    }


    public Map<Date, BARDecisionInfo> getArrivalOrOccupancyDemandOverride(LocalDate startDate, LocalDate endDate) {

        return getArrivalOrOccupancyDemandOverride(startDate.toDate(), endDate.toDate());
    }


    public Map<Date, BARDecisionInfo> getBarDecisionWithConflictingOverrides(int accomClassId, int lengthOfStay, LocalDate startDate,
                                                                             LocalDate endDate) {
        return getBarDecisionWithConflictingOverrides(accomClassId, lengthOfStay, startDate.toDate(), endDate.toDate());
    }


    public Map<Date, BARDecisionInfo> getBarDecisions(int accomClassId, int lengthOfStay, LocalDate startDate, LocalDate endDate) {
        return getBarDecisions(accomClassId, lengthOfStay, startDate.toDate(), endDate.toDate());
    }


    public Map<Date, CompetitorInfo> getCompetitorInfo(int accomClassId, int lengthOfStay, LocalDate startDate, LocalDate endDate) {
        return getCompetitorInfo(accomClassId, lengthOfStay, startDate.toDate(), endDate.toDate());
    }


    public Map<Date, BigDecimal> getLastRoomValue(int accomClassId, LocalDate startDate, LocalDate endDate) {
        return getLastRoomValue(accomClassId, startDate.toDate(), endDate.toDate());
    }


    public List<BARDecisionInfo> getNonDecisions(LocalDate startDate, LocalDate endDate) {
        return getNonDecisions(startDate.toDate(), endDate.toDate());
    }


    public Map<Date, Float> getOccupancyForecast(LocalDate startDate, LocalDate endDate) {
        return getOccupancyForecast(startDate.toDate(), endDate.toDate());
    }


    public Map<Date, Integer> getRoomsOutOfOrder(LocalDate startDate, LocalDate endDate) {
        return getRoomsOutOfOrder(startDate.toDate(), endDate.toDate());
    }


    public Map<Date, BARDecisionInfo> getWashOverrides(LocalDate startDate, LocalDate endDate) {
        return getWashOverrides(startDate.toDate(), endDate.toDate());
    }


    public boolean nonDecisionsPresent(LocalDate startDate, LocalDate endDate) {
        return nonDecisionsPresent(startDate.toDate(), endDate.toDate());
    }


    public BARDetails getBARDetails(int accomClassId, LocalDate arrivalDate) {
        return getBARDetails(accomClassId, arrivalDate.toDate());
    }


    public BARDetails getBARDetails(int accomClassId, LocalDate arrivalDate, int lengthOfStay) {
        return getBARDetails(accomClassId, arrivalDate.toDate(), lengthOfStay);
    }


    public BARDetails getBARDetailsByDateRange(int accomClassId, LocalDate startDate, LocalDate endDate) {
        return getBARDetailsByDateRange(accomClassId, startDate.toDate(), endDate.toDate());
    }


    public BARDetails getBARDetailsByDateRange(int accomClassId, LocalDate startDate, LocalDate endDate, int lengthOfStay) {
        return getBARDetailsByDateRange(accomClassId, startDate.toDate(), endDate.toDate(), lengthOfStay);
    }


    public Map<Date, BARDecisionInfo> getEarlierBarDecisions(Date timestamp, int accomClassId, int lengthOfStay, LocalDate startDate,
                                                             LocalDate endDate) {
        return getEarlierBarDecisions(timestamp, accomClassId, lengthOfStay, startDate.toDate(), endDate.toDate());
    }


    public BAROvrUnavailableDtRangeAndPriceDetails getUnavailableBarDecisionDates(int accomClassId, int rateUnqualifiedId,
                                                                                  String overridetype, LocalDate startDate, LocalDate endDate, List<Integer> lengthOfStay) {
        return getUnavailableBarDecisionDates(accomClassId, rateUnqualifiedId, overridetype, startDate.toDate(), endDate.toDate(),
                lengthOfStay);
    }


    public List<LRAAffectedDateRange> getLraAffectedDateRange(Integer accomClassId, LocalDate startDate, LocalDate endDate,
                                                              List<Integer> los) {
        return crudService.findByNativeQuery(FETCH_LRA_AFFECTED_DATE_RANGE,
                QueryParameter.with(START_DATE, startDate.toDate()).and(END_DATE, endDate.toDate()).and(ACCOM_CLASS_ID, accomClassId)
                        .and("decisionReasonTypeId", 6).and("los", los).parameters(), new RowMapper<LRAAffectedDateRange>() {

                    @Override
                    public LRAAffectedDateRange mapRow(Object[] row) {
                        LRAAffectedDateRange lraAffectedDateRange = new LRAAffectedDateRange();
                        lraAffectedDateRange.setStartDate((Date) row[0]);
                        lraAffectedDateRange.setEndDate((Date) row[1]);
                        lraAffectedDateRange.setLos((Integer) row[2]);
                        return lraAffectedDateRange;
                    }
                });
    }


    ///////////////////////
    //DEPRECATED METHODS///
    //////////////////////

    /**
     * @param accomClassId
     * @param startDate
     * @param endDate
     * @return
     * @deprecated
     */
    @Deprecated
    public Map<Date, BARDecisionInfo> getArrivalOrOccupancyDemandOverride(int accomClassId, DateParameter startDate, DateParameter endDate) {

        return getArrivalOrOccupancyDemandOverride(startDate.getTime(), endDate.getTime());
    }

    /**
     * @param accomClassId
     * @param lengthOfStay
     * @param startDate
     * @param endDate
     * @return
     * @deprecated
     */
    @Deprecated
    public Map<Date, BARDecisionInfo> getBarDecisionWithConflictingOverrides(int accomClassId, int lengthOfStay, DateParameter startDate,
                                                                             DateParameter endDate) {
        return getBarDecisionWithConflictingOverrides(accomClassId, lengthOfStay, startDate.getTime(), endDate.getTime());
    }

    /**
     * @param accomClassId
     * @param lengthOfStay
     * @param startDate
     * @param endDate
     * @return
     * @deprecated
     */
    @Deprecated
    public Map<Date, BARDecisionInfo> getBarDecisions(int accomClassId, int lengthOfStay, DateParameter startDate, DateParameter endDate) {
        return getBarDecisions(accomClassId, lengthOfStay, startDate.getTime(), endDate.getTime());
    }

    /**
     * @param accomClassId
     * @param lengthOfStay
     * @param startDate
     * @param endDate
     * @return
     * @deprecated
     */
    @Deprecated
    public Map<Date, CompetitorInfo> getCompetitorInfo(int accomClassId, int lengthOfStay, DateParameter startDate, DateParameter endDate) {
        return getCompetitorInfo(accomClassId, lengthOfStay, startDate.getTime(), endDate.getTime());
    }

    /**
     * @param accomClassId
     * @param startDate
     * @param endDate
     * @return
     * @deprecated
     */
    @Deprecated
    public Map<Date, BigDecimal> getLastRoomValue(int accomClassId, DateParameter startDate, DateParameter endDate) {
        return getLastRoomValue(accomClassId, startDate.getTime(), endDate.getTime());
    }

    /**
     * @param startDate
     * @param endDate
     * @return
     * @deprecated
     */
    @Deprecated
    public List<BARDecisionInfo> getNonDecisions(DateParameter startDate, DateParameter endDate) {
        return getNonDecisions(startDate.getTime(), endDate.getTime());
    }

    /**
     * @param startDate
     * @param endDate
     * @return
     * @deprecated
     */
    @Deprecated
    public Map<Date, Float> getOccupancyForecast(DateParameter startDate, DateParameter endDate) {
        return getOccupancyForecast(startDate.getTime(), endDate.getTime());
    }

    /**
     * @param startDate
     * @param endDate
     * @return
     * @deprecated
     */
    @Deprecated
    public Map<Date, Integer> getRoomsOutOfOrder(DateParameter startDate, DateParameter endDate) {
        return getRoomsOutOfOrder(startDate.getTime(), endDate.getTime());
    }

    /**
     * @param startDate
     * @param endDate
     * @return
     * @deprecated
     */
    @Deprecated
    public Map<Date, BARDecisionInfo> getWashOverrides(DateParameter startDate, DateParameter endDate) {
        return getWashOverrides(startDate.getTime(), endDate.getTime());
    }

    /**
     * @param startDate
     * @param endDate
     * @return
     * @deprecated
     */
    @Deprecated
    public boolean nonDecisionsPresent(DateParameter startDate, DateParameter endDate) {
        return nonDecisionsPresent(startDate.getTime(), endDate.getTime());
    }

    /**
     * @param accomClassId
     * @param arrivalDate
     * @return
     * @deprecated
     */
    @Deprecated
    public BARDetails getBARDetails(int accomClassId, DateParameter arrivalDate) {
        return getBARDetails(accomClassId, arrivalDate.getTime());
    }

    /**
     * @param accomClassId
     * @param arrivalDate
     * @param lengthOfStay
     * @return
     * @deprecated
     */
    @Deprecated
    public BARDetails getBARDetails(int accomClassId, DateParameter arrivalDate, int lengthOfStay) {
        return getBARDetails(accomClassId, arrivalDate.getTime(), lengthOfStay);
    }

    /**
     * @param accomClassId
     * @param startDate
     * @param endDate
     * @return
     * @deprecated
     */
    @Deprecated
    public BARDetails getBARDetailsByDateRange(int accomClassId, DateParameter startDate, DateParameter endDate) {
        return getBARDetailsByDateRange(accomClassId, startDate.getTime(), endDate.getTime());
    }

    /**
     * @param accomClassId
     * @param startDate
     * @param endDate
     * @param lengthOfStay
     * @return
     * @deprecated
     */
    @Deprecated
    public BARDetails getBARDetailsByDateRange(int accomClassId, DateParameter startDate, DateParameter endDate, int lengthOfStay) {
        return getBARDetailsByDateRange(accomClassId, startDate.getTime(), endDate.getTime(), lengthOfStay);
    }

    /**
     * @param timestamp
     * @param accomClassId
     * @param lengthOfStay
     * @param startDate
     * @param endDate
     * @return
     * @deprecated
     */
    @Deprecated
    public Map<Date, BARDecisionInfo> getEarlierBarDecisions(Date timestamp, int accomClassId, int lengthOfStay, DateParameter startDate,
                                                             DateParameter endDate) {
        return getEarlierBarDecisions(timestamp, accomClassId, lengthOfStay, startDate.getTime(), endDate.getTime());
    }

    @SuppressWarnings("unchecked")
    public Map<Date, List<Integer>> getBarDecisionsWithOverrideConflicts(BARFilterCriteria criteria) {
        Map<Date, List<Integer>> result = new HashMap<Date, List<Integer>>();

        Query q = sqlHelper.buildQueryForConflictingOverrides(criteria.getAccomClassId(), criteria.getStartDateParameter().getTime(),
                criteria.getEndDateParameter().getTime(), criteria.getIncludedLengthsOfStay());
        List<Object[]> resultList = q.getResultList();
        for (Object[] row : resultList) {

            Date arrivalDate = (Date) row[0];
            result.computeIfAbsent(arrivalDate, k -> new ArrayList<>());

            Integer los = (Integer) row[1];
            if (!result.get(arrivalDate).contains(los)) {
                result.get(arrivalDate).add(los);
            }
        }
        return result;
    }

    /**
     * @param accomClassId
     * @param rateUnqualifiedId
     * @param overridetype
     * @param startDate
     * @param endDate
     * @param lengthOfStay
     * @return
     * @deprecated
     */
    @Deprecated
    public BAROvrUnavailableDtRangeAndPriceDetails getUnavailableBarDecisionDates(int accomClassId, int rateUnqualifiedId,
                                                                                  String overridetype, DateParameter startDate, DateParameter endDate, List<Integer> lengthOfStay) {
        return getUnavailableBarDecisionDates(accomClassId, rateUnqualifiedId, overridetype, startDate.getTime(), endDate.getTime(),
                lengthOfStay);
    }

    /**
     * @param accomClassId
     * @param startDate
     * @param endDate
     * @param los
     * @return
     * @deprecated
     */
    @Deprecated
    public List<LRAAffectedDateRange> getLraAffectedDateRange(Integer accomClassId, DateParameter startDate, DateParameter endDate,
                                                              List<Integer> los) {

        return crudService.findByNativeQuery(FETCH_LRA_AFFECTED_DATE_RANGE,
                QueryParameter.with(START_DATE, startDate.getTime()).and(END_DATE, endDate.getTime()).and(ACCOM_CLASS_ID, accomClassId)
                        .and("decisionReasonTypeId", 6).and("los", los).parameters(), new RowMapper<LRAAffectedDateRange>() {

                    @Override
                    public LRAAffectedDateRange mapRow(Object[] row) {
                        LRAAffectedDateRange lraAffectedDateRange = new LRAAffectedDateRange();
                        lraAffectedDateRange.setStartDate((Date) row[0]);
                        lraAffectedDateRange.setEndDate((Date) row[1]);
                        lraAffectedDateRange.setLos((Integer) row[2]);
                        return lraAffectedDateRange;
                    }
                });
    }

    private Map<Integer, List<AvailableRateDataForLOS>> createLosMap(int accomClassId, List<Integer> lengthOfStay, Date startDate,
                                                                     Date endDate, int rateUnqualifiedId) {
        Map<Integer, List<AvailableRateDataForLOS>> losDataMap = new HashMap<Integer, List<AvailableRateDataForLOS>>();
        for (int los : lengthOfStay) {
            List<AvailableRateDataForLOS> singlelosResultList = sqlHelper.buildQueryForUnavailableLengthOfStays(rateUnqualifiedId,
                    accomClassId, startDate, endDate, los);
            if (null != singlelosResultList && !singlelosResultList.isEmpty()) {
                losDataMap.put(los, singlelosResultList);
            }
        }
        return losDataMap;
    }

    public BarDecisionDetailsData getBarDetails(String occupancyDate, Integer roomClassId) {
        BarDecisionDetailsData barDecisionDetailsData = new BarDecisionDetailsData();
        List<AccomClass> validAccomClasses = filterValidAccomClasses(getPropertyAccomClassList());
        if (roomClassId == -1) {
            roomClassId = getMasterRoomClassId(validAccomClasses);
        }

        boolean isBARByDay = Constants.BAR_DECISION_VALUE_RATEOFDAY.equals(configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value()));

        boolean isPhysicalCapacityEnabled = configService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_PHYSICAL_CAPACITY_CONSIDERATION.value());

        if (isBARByDay) {
            addBarByDayBarDecisionDetails(pricingService.getPricingByDayData(java.time.LocalDate.parse(occupancyDate), java.time.LocalDate.parse(occupancyDate), roomClassId.toString(), new ArrayList<>(Collections.nCopies(15, -1)), 0, null, null, isPhysicalCapacityEnabled), roomClassId, LocalDate.parse(occupancyDate).toDate(), barDecisionDetailsData);
        } else {
            addBarByLOSBarDecisionDetails(pricingService.getPricingByLosData(java.time.LocalDate.parse(occupancyDate), java.time.LocalDate.parse(occupancyDate), roomClassId.toString(), new ArrayList<>(Collections.nCopies(15, -1)), 0, null, null, isPhysicalCapacityEnabled), LocalDate.parse(occupancyDate).toDate(), roomClassId, configService.getIntegerParameterValue(IPConfigParamName.BAR_MAX_LOS.value()), barDecisionDetailsData);
        }

        barDecisionDetailsData.getBarDecisionDetails().getAccomClassDetailsList().addAll(validAccomClasses.stream().map(accomClass -> new AccomClassDetails(accomClass.getId(), accomClass.getName(), accomClass.getMasterClassBoolean())).collect(Collectors.toList()));
        barDecisionDetailsData.setPricingType(isBARByDay ? Constants.BAR_DECISION_VALUE_RATEOFDAY : Constants.BAR_DECISION_VALUE_LOS);
        barDecisionDetailsData.setCompetitorsDataAvailable(configService.getBooleanParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value()));
        return barDecisionDetailsData;
    }

    public BarDetailsInfo getBarOverrideDetails(String occupancyDate, Integer roomClassId, Integer lengthOfStay) {
        BarDetailsInfo barDetailsInfo = new BarDetailsInfo();
        Date date = LocalDate.parse(occupancyDate).toDate();
        barDetailsInfo.setDow(DateUtil.getDayOfWeekShortName(date));
        barDetailsInfo.setOccupancyDate(DateUtil.formatDate(date, DateUtil.DATE_FORMAT_MM_DD_YYYY));
        if (roomClassId == -1) {
            roomClassId = getMasterClassId();
        }
        barDetailsInfo.setCompetitor(getCompetitorPrice(date, roomClassId, (lengthOfStay > 0 ? 1 : -1)));//Always show CompetitorPrice of LOS1 for BarByLOS or -1 for BarByDay
        barDetailsInfo.setLrv(getLastRoomVal(date, roomClassId, (lengthOfStay > 0 ? 1 : -1)));//Always show LRV of LOS1 for BarByLOS or -1 for BarByDay
        barDetailsInfo.setPropertyOccupancyForecastPerc(getOccupancyForecastPerc(date));
        barDetailsInfo.setRoomClassName(crudService.findByNamedQuerySingleResult(AccomClass.GET_NAME,
                QueryParameter.with(ACCOM_CLASS_ID, roomClassId).parameters()));
        barDetailsInfo.setEnableSingleBarDecision(configService.getBooleanParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value()));
        barDetailsInfo.setCeilingEnabled(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.CEILING_OVERRIDE_ENABLED.value()));
        //bar override permissions are set here
        applyBarOverridePermissions(barDetailsInfo);
        barDetailsInfo.setBarOverrideDetailsList(prepareBarOverrideDetailsList(date, roomClassId, lengthOfStay));
        return barDetailsInfo;
    }

    public List<BarOverrideDetails> prepareBarOverrideDetailsList(Date date, Integer roomClassId, Integer lengthOfStay) {
        List<BarOverrideDetails> barOverrideDetailsList = new ArrayList<>();
        BARDetails barDetailsByDateRange = getBARDetailsByDateRange(roomClassId, date, date, lengthOfStay);
        if (barDetailsByDateRange != null) {
            Map<Date, BARDecisionInfo> barDecisions = getBarDecisions(roomClassId, lengthOfStay, date, date);
            Map<String, BigDecimal> remainingDemandByRateUnqualifiedName = barDetailsByDateRange.getRemainingDemandByRateUnqualifiedName();
            BARDecisionInfo barDecisionInfo = barDecisions.get(date);
            Map<String, Integer> ratePlanIds = getRatePlanIds();
            for (Entry<String, BigDecimal> entry : remainingDemandByRateUnqualifiedName.entrySet()) {
                String barName = entry.getKey();
                barOverrideDetailsList.add(
                        new BarOverrideDetails(date, roomClassId, lengthOfStay, barName, barDetailsByDateRange.getRateUnqualifiedValue(barName), entry.getValue(), barDecisionInfo.getRatePlanName(), ratePlanIds));
            }
            applyBarOverride(barOverrideDetailsList, barDecisionInfo);
            Collections.sort(barOverrideDetailsList, (o1, o2) -> o2.getBarPrice().compareTo(o1.getBarPrice()));
        }
        return barOverrideDetailsList;
    }

    public void applyBarOverride(List<BarOverrideDetails> barOverrideDetailsList, BARDecisionInfo barDecisionInfo) {
        final String override = barDecisionInfo.getOverride();
        if ("Floor".equalsIgnoreCase(override)) {
            barOverrideDetailsList.stream().filter(barOverrideDetailsItem -> StringUtils.equals(barDecisionInfo.getFloorRatePlanName(), barOverrideDetailsItem.getBarName())).findFirst().ifPresent(barOverrideDetails1 ->
                    barOverrideDetails1.setFloorRateUnqualifiedId(barDecisionInfo.getFloorRatePlanId()));
        } else if ("User".equalsIgnoreCase(override)) {
            barOverrideDetailsList.stream().filter(barOverrideDetailsItem -> StringUtils.equals(barDecisionInfo.getSpecificRatePlanName(), barOverrideDetailsItem.getBarName())).findFirst().ifPresent(barOverrideDetails1 ->
                    barOverrideDetails1.setSpecificRateUnqualifiedId(barDecisionInfo.getSpecificRatePlanId()));
        } else if ("FloorAndCeil".equalsIgnoreCase(override)) {
            barOverrideDetailsList.stream().filter(barOverrideDetailsItem -> StringUtils.equals(barDecisionInfo.getFloorRatePlanName(), barOverrideDetailsItem.getBarName())).findFirst().ifPresent(barOverrideDetails1 ->
                    barOverrideDetails1.setFloorRateUnqualifiedId(barDecisionInfo.getFloorRatePlanId()));

            barOverrideDetailsList.stream().filter(barOverrideDetailsItem -> StringUtils.equals(barDecisionInfo.getCeilingRatePlanName(), barOverrideDetailsItem.getBarName())).findFirst().ifPresent(barOverrideDetails1 ->
                    barOverrideDetails1.setCeilingRateUnqualifiedId(barDecisionInfo.getCeilingRatePlanId()));
        } else if ("Ceil".equalsIgnoreCase(override)) {
            barOverrideDetailsList.stream().filter(barOverrideDetailsItem -> StringUtils.equals(barDecisionInfo.getCeilingRatePlanName(), barOverrideDetailsItem.getBarName())).findFirst().ifPresent(barOverrideDetails1 ->
                    barOverrideDetails1.setCeilingRateUnqualifiedId(barDecisionInfo.getCeilingRatePlanId()));
        }
    }

    public void applyBarOverridePermissions(BarDetailsInfo barDetailsInfo) {
        final Role role = roleService.getPropertyUserRole(PacmanWorkContextHelper.getUserId(), PacmanWorkContextHelper.getPropertyId());
        List<RolePermission> currentRolePermissions = rolePermissionService.getAccessibleRolePermissions().stream().filter(rolePermission -> role.getRoleName().equals(rolePermission.getRoleName())).collect(Collectors.toList());

        Optional<RolePermission> specificFloorOverridePermission = currentRolePermissions.stream().filter(rolePermission -> "Apply/Edit Single Day BAR Override (Specific and Floor)".equalsIgnoreCase(rolePermission.getModuleName())).findFirst();
        Optional<RolePermission> floorOverridePermission = currentRolePermissions.stream().filter(rolePermission -> "Apply/Edit Single Day BAR Override (Floor Only)".equalsIgnoreCase(rolePermission.getModuleName())).findFirst();
        Optional<RolePermission> removeSpecificFloorOverridePermission = currentRolePermissions.stream().filter(rolePermission -> "Remove Single Day BAR Override (Specific and Floor)".equalsIgnoreCase(rolePermission.getModuleName())).findFirst();

        specificFloorOverridePermission.ifPresent(rolePermission -> {
            barDetailsInfo.setUserOverridePermission(RoleModulePermissionMapperUtil.getAccessLabelMap().get(rolePermission.getPermission()));
            barDetailsInfo.setFloorOverridePermission(RoleModulePermissionMapperUtil.getAccessLabelMap().get(rolePermission.getPermission()));
        });

        floorOverridePermission.ifPresent(rolePermission -> barDetailsInfo.setFloorOverridePermission(RoleModulePermissionMapperUtil.getAccessLabelMap().get(rolePermission.getPermission())));

        removeSpecificFloorOverridePermission.ifPresent(rolePermission -> {
            barDetailsInfo.setRemoveUserOverridePermission(RoleModulePermissionMapperUtil.getAccessLabelMap().get(rolePermission.getPermission()));
            barDetailsInfo.setRemoveFloorOverridePermission(RoleModulePermissionMapperUtil.getAccessLabelMap().get(rolePermission.getPermission()));
        });
    }

    public String getOccupancyForecastPerc(Date date) {
        Map<Date, Float> occupancyForecastMap = getOccupancyForecast(date, date);
        if (MapUtils.isNotEmpty(occupancyForecastMap)) {
            final Float occupancyForecastPerc = occupancyForecastMap.get(date);
            return occupancyForecastPerc == null ? null : String.format("%.2f", occupancyForecastPerc);
        }
        return null;
    }

    public String getLastRoomVal(Date date, Integer roomClassId, Integer lengthOfStay) {
        Map<Date, BigDecimal> lrvMap = getLastRoomValue(roomClassId, date, date, lengthOfStay);
        if (MapUtils.isNotEmpty(lrvMap)) {
            BigDecimal lastRoomValue = lrvMap.get(date);
            return lastRoomValue == null ? null : NumberUtils.numberFormatter(lastRoomValue.setScale(2, BigDecimal.ROUND_HALF_UP), 2);
        }
        return null;
    }

    public String getCompetitorPrice(Date date, Integer roomClassId, Integer lengthOfStay) {
        Map<Date, CompetitorInfo> dateCompetitorInfoMap = getCompetitorInfo(roomClassId, lengthOfStay, date, date);
        if (MapUtils.isNotEmpty(dateCompetitorInfoMap)) {
            final CompetitorInfo competitorInfo = dateCompetitorInfoMap.get(date);
            return formatCompetitorPrice(competitorInfo);
        }
        return null;
    }

    public String getCompetitorPriceBarByDay(Integer roomClassId, Date date) {
        Integer lengthOfStay = configService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value()) ? 1 : -1;
        Map<Date, CompetitorInfo> competitorInfoMap = getCompetitorInfo(roomClassId, lengthOfStay, date, date);
        if (MapUtils.isNotEmpty(competitorInfoMap)) {
            CompetitorInfo competitorInfo = competitorInfoMap.get(date);
            return formatCompetitorPrice(competitorInfo);
        }
        return null;
    }

    public String getCompetitorPriceByLOS(Integer los, Map<Integer, CompetitorInfo> competitorInfoByLOS) {
        if (MapUtils.isNotEmpty(competitorInfoByLOS)) {
            CompetitorInfo competitorInfo = competitorInfoByLOS.get(los);
            return formatCompetitorPrice(competitorInfo);
        }
        return null;
    }

    private String formatCompetitorPrice(CompetitorInfo competitorInfo) {
        String competitorPrice = null;
        if (competitorInfo != null) {
            if (isNumber(competitorInfo.getCompetitorPrice())) {
                competitorPrice = NumberUtils.numberFormatter(new BigDecimal(competitorInfo.getCompetitorPrice()), 2);
            } else if (StringUtils.isAlpha(competitorInfo.getCompetitorPrice())) {
                competitorPrice = competitorInfo.getCompetitorPrice();
            }
        }
        return competitorPrice;
    }

    public BARFilterCriteria getBarFilterCriteria(Integer accomClassId, Integer maxLOS, Date occupancyDate) {
        BARFilterCriteria filterCriteria = new BARFilterCriteria();
        filterCriteria.setIncludedLengthsOfStay(getLOSList(maxLOS));
        filterCriteria.setAccomClassId(accomClassId == null ? 0 : accomClassId);
        filterCriteria.setStartDate(occupancyDate);
        filterCriteria.setEndDate(occupancyDate);
        filterCriteria.setOnlyShowDatesWithOverridesConflictingRateStrategy(false);
        filterCriteria.setIncludeChangedOnly(false);
        Integer[] days = {1, 2, 3, 4, 5, 6, 7};
        filterCriteria.setIncludedDaysOfWeek(Arrays.asList(days));
        return filterCriteria;
    }

    public List<Integer> getLOSList(Integer maxLOS) {
        List<Integer> result = new ArrayList<>();
        for (int i = 1; i <= maxLOS; i++) {
            result.add(i);
        }
        return result;
    }

    public List<AccomClass> getPropertyAccomClassList() {
        return crudService.findByNamedQuery(AccomClass.BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public List<AccomClass> filterValidAccomClasses(List<AccomClass> propertyAccomClasses) {
        Predicate<AccomClass> validAccomClassPredicate = accomClass -> (!"Unassigned".equalsIgnoreCase(accomClass.getName()) && Objects.equals(Status.ACTIVE.getId(), accomClass.getStatusId()));
        Predicate<AccomClass> accomClassWithAccomTypesPredicate = accomClass -> CollectionUtils.isNotEmpty(accomClass.getAccomTypes());
        return propertyAccomClasses.stream().filter(validAccomClassPredicate).filter(accomClassWithAccomTypesPredicate).collect(Collectors.toList());
    }

    public Integer getMasterRoomClassId(List<AccomClass> propertyAccomClasses) {
        Predicate<AccomClass> masterRoomClassPredicate = accomClass -> accomClass.getMasterClassBoolean() && (Objects.equals(accomClass.getStatusId(), Status.ACTIVE.getId())) && (Objects.equals(accomClass.getSystemDefault(), 0));
        AccomClass masterAccomClass = propertyAccomClasses.stream().filter(masterRoomClassPredicate).findFirst().get();
        return masterAccomClass.getId();
    }

    public void addBarByDayBarDecisionDetails(List<PricingByDay> pricingByDayList, Integer roomClassId, Date date, BarDecisionDetailsData barDecisionDetailsData) {
        BarDecisionDetails barDecisionDetails = new BarDecisionDetails();
        if (CollectionUtils.isNotEmpty(pricingByDayList)) {
            PricingByDay pricingByDay = pricingByDayList.get(0);
            if (pricingByDay != null) {
                Optional.ofNullable(pricingByDay.getPropertyOccupancyForecastPercent()).ifPresent(
                        occupancyForecastPercent -> barDecisionDetailsData.setPropertyOccupancyForecastPerc(occupancyForecastPercent.setScale(2, RoundingMode.HALF_UP).toString()));
                barDecisionDetailsData.setOccupancyDate(DateUtil.formatDate(pricingByDay.getArrivalDate(), DateUtil.DATE_FORMAT_MM_DD_YYYY));
                barDecisionDetailsData.setDow(pricingByDay.getDow());
                Optional.ofNullable(pricingByDay.getLRV()).ifPresent(lrv -> barDecisionDetailsData.setLrv(NumberUtils.numberFormatter(lrv.setScale(2, BigDecimal.ROUND_HALF_UP), 2)));
                String competitorPriceBarByDay = getCompetitorPriceBarByDay(roomClassId, date);
                barDecisionDetails.setCompetitor(competitorPriceBarByDay);
                barDecisionDetails.setRoomClassName(pricingByDay.getAccomClassName());
                BarDecision barDecision = new BarDecision(pricingByDay, competitorPriceBarByDay);
                barDecisionDetails.setBarDecisionList(Arrays.asList(barDecision));
            }
        }
        barDecisionDetailsData.setBarDecisionDetails(barDecisionDetails);
    }


    public void addBarByLOSBarDecisionDetails(List<PricingByLos> pricingByLosList, Date occupancyDate, Integer roomClassId, Integer maxLOS, BarDecisionDetailsData barDecisionDetailsData) {
        BarDecisionDetails barDecisionDetails = new BarDecisionDetails();
        if (CollectionUtils.isNotEmpty(pricingByLosList)) {
            final PricingByLos pricingByLos = pricingByLosList.get(0);
            Map<Date, Map<Integer, CompetitorInfo>> competitorInfo = getCompetitorInfo(getBarFilterCriteria(roomClassId, maxLOS, occupancyDate));
            final Map<Integer, CompetitorInfo> competitorInfoByLOS = competitorInfo.get(occupancyDate);
            barDecisionDetailsData.setOccupancyDate(DateUtil.formatDate(pricingByLos.getArrivalDate(), DateUtil.DATE_FORMAT_MM_DD_YYYY));
            Optional.ofNullable(pricingByLos.getLRV()).ifPresent(lrv -> barDecisionDetailsData.setLrv(NumberUtils.numberFormatter(lrv.setScale(2, BigDecimal.ROUND_HALF_UP), 2)));
            Optional.ofNullable(
                    pricingByLos.getPropertyOccupancyForecastPercent()).ifPresent(
                    propertyOccupancyForecastPercent -> barDecisionDetailsData.setPropertyOccupancyForecastPerc(propertyOccupancyForecastPercent.setScale(2, BigDecimal.ROUND_HALF_UP).toString())
            );
            barDecisionDetails.setCompetitor(getCompetitorPriceByLOS(1, competitorInfoByLOS));
            barDecisionDetails.setRoomClassName(pricingByLos.getAccomClassName());
            barDecisionDetailsData.setDow(pricingByLos.getDow());

            if (maxLOS > 0) {
                barDecisionDetails.getBarDecisionList().add(
                        new BarDecision(pricingByLos.getLOS1(), pricingByLos.getRateCodeName1(), pricingByLos.getLOSPrice1(), getCompetitorPriceByLOS(1, competitorInfoByLOS), getBarOverrideType(pricingByLos.getLOSPrice1(), pricingByLos.getOverride1())));
            }

            if (maxLOS > 1) {
                barDecisionDetails.getBarDecisionList().add(
                        new BarDecision(pricingByLos.getLOS2(), pricingByLos.getRateCodeName2(), pricingByLos.getLOSPrice2(), getCompetitorPriceByLOS(2, competitorInfoByLOS), getBarOverrideType(pricingByLos.getLOSPrice2(), pricingByLos.getOverride2())));
            }

            if (maxLOS > 2) {
                barDecisionDetails.getBarDecisionList().add(
                        new BarDecision(pricingByLos.getLOS3(), pricingByLos.getRateCodeName3(), pricingByLos.getLOSPrice3(), getCompetitorPriceByLOS(3, competitorInfoByLOS), getBarOverrideType(pricingByLos.getLOSPrice3(), pricingByLos.getOverride3())));
            }

            if (maxLOS > 3) {
                barDecisionDetails.getBarDecisionList().add(
                        new BarDecision(pricingByLos.getLOS4(), pricingByLos.getRateCodeName4(), pricingByLos.getLOSPrice4(), getCompetitorPriceByLOS(4, competitorInfoByLOS), getBarOverrideType(pricingByLos.getLOSPrice4(), pricingByLos.getOverride4())));
            }

            if (maxLOS > 4) {
                barDecisionDetails.getBarDecisionList().add(
                        new BarDecision(pricingByLos.getLOS5(), pricingByLos.getRateCodeName5(), pricingByLos.getLOSPrice5(), getCompetitorPriceByLOS(5, competitorInfoByLOS), getBarOverrideType(pricingByLos.getLOSPrice5(), pricingByLos.getOverride5())));
            }

            if (maxLOS > 5) {
                barDecisionDetails.getBarDecisionList().add(
                        new BarDecision(pricingByLos.getLOS6(), pricingByLos.getRateCodeName6(), pricingByLos.getLOSPrice6(), getCompetitorPriceByLOS(6, competitorInfoByLOS), getBarOverrideType(pricingByLos.getLOSPrice6(), pricingByLos.getOverride6())));
            }

            if (maxLOS > 6) {
                barDecisionDetails.getBarDecisionList().add(
                        new BarDecision(pricingByLos.getLOS7(), pricingByLos.getRateCodeName7(), pricingByLos.getLOSPrice7(), getCompetitorPriceByLOS(7, competitorInfoByLOS), getBarOverrideType(pricingByLos.getLOSPrice7(), pricingByLos.getOverride7())));
            }

            if (maxLOS > 7) {
                barDecisionDetails.getBarDecisionList().add(
                        new BarDecision(pricingByLos.getLOS8(), pricingByLos.getRateCodeName8(), pricingByLos.getLOSPrice8(), getCompetitorPriceByLOS(8, competitorInfoByLOS), getBarOverrideType(pricingByLos.getLOSPrice8(), pricingByLos.getOverride8())));
            }

            Collections.sort(barDecisionDetails.getBarDecisionList(), Comparator.comparing(BarDecision::getLos));
        }

        barDecisionDetailsData.setBarDecisionDetails(barDecisionDetails);
    }


    public String getBarOverrideType(final BigDecimal rateCode, final String override) {

        if (rateCode == null) {
            return null;
        }

        String overrideValue = (override != null && !override.equalsIgnoreCase(Constants.BARDECISIONOVERRIDE_NONE) && !override.equalsIgnoreCase(Constants.BARDECISIONOVERRIDE_PENDING)) ? override.toUpperCase() : "";
        return overrideValue.trim();
    }

    public void setCPManagementService(CPManagementService cpManagementService) {
        this.cpManagementService = cpManagementService;
    }

    public List<Product> findAllActiveProducts() {
        List<Product> products;
        boolean isIndependentProductsEnabled = configService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
        if (isIndependentProductsEnabled) {
            products = managementService.findSystemDefaultAndAllActiveLinkedAndIndependentProducts();
        } else {
            products = managementService.findSystemDefaultAndAllActiveLinkedProducts();
        }
        products.sort(Comparator.comparing(Product::getDisplayOrder));
        return products;
    }

    public PaginatedDecisionsResponse getDailyBARDecisionsByDateRange(Date startDate, Date endDate, int pageSize, int pageNumber) {
        List<DailyBARDecisionDTO> result = crudService.findByNativeQuery(DecisionBAROutput.GET_DAILY_BAR_DECISIONS_BY_DATE_RANGE,
                QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters(), this::rowMapper);

        if(Objects.isNull(result) || result.isEmpty()){
            return new PaginatedDecisionsResponse(Collections.emptyList(),0,0);
        }
        int totalDecisionsCount = result.size();
        int totalPages = Pagination.getTotalPages(totalDecisionsCount, pageSize);
        List<DailyBARDecisionDTO> paginatedDecisions = (pageNumber >= totalPages) ? Collections.emptyList() :
                Pagination.paginateSublistForPageNoByPageSize(pageNumber + 1, result, pageSize);

        return new PaginatedDecisionsResponse(paginatedDecisions, totalPages, pageNumber);
    }

    private DailyBARDecisionDTO rowMapper(Object[] row) {
        DailyBARDecisionDTO dto = new DailyBARDecisionDTO();
        dto.setPropertyId((Integer) row[0]);
        dto.setArrivalDate((Date) row[1]);
        dto.setRateCodeName((String) row[2]);
        dto.setAccomClassName((String) row[3]);
        dto.setAccomTypeName((String) row[4]);
        dto.setLos((Integer) row[5]);
        dto.setRate((BigDecimal) row[6]);
        return dto;
    }

    public static class PaginatedDecisionsResponse {

        private final List<DailyBARDecisionDTO> decisions;
        private final int totalPages;
        private final int pageNumber;

        public PaginatedDecisionsResponse(List<DailyBARDecisionDTO> decisions, int totalPages, int pageNumber) {
            this.decisions = decisions;
            this.totalPages = totalPages;
            this.pageNumber = pageNumber;
        }

        public List<DailyBARDecisionDTO> getDecisions() {
            return decisions;
        }

       public int getTotalPages() {
            return totalPages;
        }

       public int getPageNumber() {
            return pageNumber;
        }
    }

}
