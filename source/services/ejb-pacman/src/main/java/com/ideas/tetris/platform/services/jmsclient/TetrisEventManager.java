package com.ideas.tetris.platform.services.jmsclient;

import com.ideas.tetris.platform.common.contextholder.PlatformThreadLocalContextHolder;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.event.MetaData;
import com.ideas.tetris.platform.common.event.TetrisEvent;
import com.ideas.tetris.platform.common.event.TetrisEventType;
import com.ideas.tetris.platform.common.event.publisher.EjbPacmanEventPublisher;
import com.ideas.tetris.platform.common.job.JobStepContext;
import com.ideas.tetris.platform.common.mdb.TopicMessageProducer;
import com.ideas.tetris.platform.services.Stage;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.UUID;

import static com.ideas.tetris.pacman.common.constants.Constants.TOPIC_NAME_NEW;

@Component
@Transactional
public class TetrisEventManager {
    private static final Logger LOGGER = Logger.getLogger(TetrisEventManager.class);
    private static final long DEFAULT_BDE_DECISIONS_UPLOADED_DELAY = 300000; // 5
    // minutes

    @Autowired
    private EjbPacmanEventPublisher ejbPacmanEventPublisher;

    @Autowired
    private TopicMessageProducer topicMessageProducer;

    public TetrisEvent buildCapacityChangeEvent(int propertyId, String newCapacity) {
        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        mdList.add(new MetaData(MetaData.NEW_CAPACITY, newCapacity));
        return new TetrisEvent(TetrisEventType.CAPACITY_CHANGED, propertyId, new Date(), mdList);
    }

    public TetrisEvent buildCatchupCompletedEvent(int propertyId) {
        return new TetrisEvent(TetrisEventType.CATCHUP_COMPLETED, propertyId, new Date(), new ArrayList<MetaData>());
    }

    /*
     * (non-Javadoc)
     *
     * @see com.ideas.tetris.platform.services.jmsclient.BookkeepingMessageSenderLocal #propertyCatchupTetrisEvent(int,
     * java.util.List, java.util.List)
     */
    public TetrisEvent buildCatchupInitiatedEvent(int propertyId, Date startDate, Date endDate,
                                                  boolean includeWebRates, String userId) {
        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("MM/dd/yyyy");
        mdList.add(new MetaData(MetaData.EXTRACT_START_DATE, dateFormat.format(startDate)));
        mdList.add(new MetaData(MetaData.EXTRACT_END_DATE, dateFormat.format(endDate)));
        mdList.add(new MetaData(MetaData.INCLUDE_WEB_RATES, new Boolean(includeWebRates).toString()));
        mdList.add(new MetaData(MetaData.INITIATED_BY, userId));
        return new TetrisEvent(TetrisEventType.CATCHUP_INITIATED, propertyId, new Date(), mdList);
    }

    public TetrisEvent buildDeletePropertyEvent(int propertyId, String propertyCode, String clientCode, String userId,
                                                String sfdcCaseNumber) {
        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        mdList.add(new MetaData(MetaData.INITIATED_BY, userId));
        mdList.add(new MetaData(MetaData.SFDC_CASE_NUMBER, sfdcCaseNumber));
        mdList.add(new MetaData(MetaData.PROPERTY_CODE, propertyCode));
        mdList.add(new MetaData(MetaData.CLIENT_CODE, clientCode));
        return new TetrisEvent(TetrisEventType.DELETE_PROPERTY, propertyId, new Date(), mdList);
    }

    public TetrisEvent buildPropertyAddedEvent(int propertyId, String propertyCode, String clientCode, String userId) {
        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        mdList.add(new MetaData(MetaData.INITIATED_BY, userId));
        mdList.add(new MetaData(MetaData.PROPERTY_CODE, propertyCode));
        mdList.add(new MetaData(MetaData.CLIENT_CODE, clientCode));
        return new TetrisEvent(TetrisEventType.PROPERTY_ADDED, propertyId, new Date(), mdList);
    }

    public TetrisEvent buildPropertyConfiguredEvent(int propertyId, String userId) {
        // Note: May want to lock down. I believe throws exception is userId
        // null
        // DE1849: Cannot insert the value NULL into column 'Data_Value', table
        // 'Bookkeeping.dbo.Detail_Meta_Data'
        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        mdList.add(new MetaData(MetaData.INITIATED_BY, userId));
        return new TetrisEvent(TetrisEventType.PROPERTY_CONFIGURED, propertyId, new Date(), mdList);
    }

    public TetrisEvent buildPropertyStageChangedEvent(int propertyId, String user, Stage previousStage, Stage newStage,
                                                      String note) {
        return buildPropertyStageChangedEvent(propertyId, user, previousStage, newStage, note, null, null);
    }

    public TetrisEvent buildPropertyStageChangedEvent(int propertyId, String user, Stage previousStage, Stage newStage,
                                                      String note, Boolean decisionStageChangeFlag, Boolean srpFplosAtTotalLevel) {
        return buildPropertyStageChangedEvent(propertyId, user, previousStage, newStage, note, null, null, decisionStageChangeFlag, srpFplosAtTotalLevel);
    }

    public TetrisEvent buildPropertyStageChangedEvent(int propertyId, String user, Stage previousStage, Stage newStage,
                                                      String note, String decisions, String scheduledTwoWayDate, Boolean decisionStageChangeFlag, Boolean srpFplosAtTotalLevel) {
        ArrayList<MetaData> metadata = new ArrayList<MetaData>();
        metadata.add(new MetaData(MetaData.INITIATED_BY, user));
        metadata.add(new MetaData(MetaData.NOTE, note));
        metadata.add(new MetaData(MetaData.NEW_STAGE, newStage.getCode()));
        if (previousStage != null) {
            metadata.add(new MetaData(MetaData.PREVIOUS_STAGE, previousStage.getCode()));
        }
        if (decisionStageChangeFlag != null) {
            metadata.add(new MetaData(MetaData.DECISION_STAGE_CHANGE_FLAG, decisionStageChangeFlag.toString()));
        }
        if (srpFplosAtTotalLevel != null) {
            metadata.add(new MetaData(MetaData.SRP_FPLOS_AT_TOTAL_LEVEL, srpFplosAtTotalLevel.toString()));
        }
        if (decisions != null) {
            metadata.add(new MetaData(MetaData.DECISIONS, decisions));
        }
        if (scheduledTwoWayDate != null) {
            metadata.add(new MetaData(MetaData.SCHEDULED_TWO_WAY_DATE, scheduledTwoWayDate));
        }

        return new TetrisEvent(TetrisEventType.STAGE_CHANGED, propertyId, new Date(), metadata);
    }

    public TetrisEvent buildRollbackInitiatedEvent(int propertyId, String userId) {
        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        mdList.add(new MetaData(MetaData.INITIATED_BY, userId));
        return new TetrisEvent(TetrisEventType.ROLLBACK_INITIATED, propertyId, new Date(), mdList);
    }

    protected String cleanFileName(String fileName) {
        try {
            File file = new File(fileName);
            return file.getName();
        } catch (Exception e) {
            LOGGER.error("BookkeepingMessageSender: cleanFileName failed.", e);
            return fileName;
        }
    }

    public TetrisEvent createBDECompletedEvent(int propertyId, String inputIdentifier, String type2FileName) {
        ArrayList<MetaData> mdList = getCompletedEventMataDataList(inputIdentifier, type2FileName);
        return new TetrisEvent(TetrisEventType.BDE_COMPLETED, propertyId, new Date(), mdList);
    }

    public TetrisEvent createBDEDecisionsUploadedEvent(int propertyId, String decisionIdentifier, String inputIdentifier) {
        ArrayList<MetaData> mdList = getDecisionUploadEventMetaDataList(decisionIdentifier, inputIdentifier);
        return new TetrisEvent(TetrisEventType.BDE_DECISIONS_UPLOADED, propertyId, new Date(), mdList);
    }

    public TetrisEvent createBDEErrorEvent(int propertyId, String inputIdentifier, Long problemId) {
        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        mdList.add(new MetaData(MetaData.INPUT_IDENTIFIER, cleanFileName(inputIdentifier)));
        mdList.add(new MetaData(MetaData.PROBLEM_ID, problemId.toString()));
        setJobInstanceIdInMetaDataList(mdList);
        return new TetrisEvent(TetrisEventType.BDE_ERROR, propertyId, new Date(), mdList);
    }

    public TetrisEvent createBDEInitiatedEvent(int propertyId, String inputIdentifier, String fileSize, String stage) {
        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        mdList.add(new MetaData(MetaData.INPUT_IDENTIFIER, cleanFileName(inputIdentifier)));
        mdList.add(new MetaData(MetaData.FILE_SIZE, fileSize));
        mdList.add(new MetaData(MetaData.STAGE, stage));
        setJobInstanceIdInMetaDataList(mdList);
        return new TetrisEvent(TetrisEventType.BDE_INITIATED, propertyId, new Date(), mdList);
    }

    public TetrisEvent createBDEProcessingStartedEvent(int propertyId, String inputIdentifier) {
        ArrayList<MetaData> mdList = getMetaDataForProcessingStartedEvent(inputIdentifier);
        return new TetrisEvent(TetrisEventType.BDE_PROCESSING_STARTED, propertyId, new Date(), mdList);
    }

    public TetrisEvent createCDPCompletedEvent(int propertyId, String correlationId) {
        ArrayList<MetaData> mdList = getMetaDataForProcessingStartedEvent(correlationId);
        return new TetrisEvent(TetrisEventType.CDP_COMPLETED, propertyId, new Date(), mdList);
    }

    public TetrisEvent createCDPDecisionsUploadedEvent(int propertyId, String decisionIdentifier, String inputIdentifier) {
        ArrayList<MetaData> mdList = getDecisionUploadEventMetaDataList(decisionIdentifier, inputIdentifier);
        return new TetrisEvent(TetrisEventType.CDP_DECISIONS_UPLOADED, propertyId, new Date(), mdList);
    }

    public TetrisEvent createCDPProcessingStartedEvent(int propertyId, String inputIdentifier) {
        ArrayList<MetaData> mdList = getMetaDataForProcessingStartedEvent(inputIdentifier);
        return new TetrisEvent(TetrisEventType.CDP_PROCESSING_STARTED, propertyId, new Date(), mdList);
    }

    public TetrisEvent createCDPProcessingStartedEvent(String correlationId, int propertyId) {
        ArrayList<MetaData> mdList = getMetaDataForProcessingStartedEvent(correlationId);
        return new TetrisEvent(TetrisEventType.CDP_PROCESSING_STARTED, propertyId, new Date(), mdList);
    }

    public TetrisEvent createFunctionSpaceCompletedEvent(int propertyId, String inputIdentifier) {
        ArrayList<MetaData> metaData = getEventMetaDataListAsPerIdentifierAndJobInstance(inputIdentifier);
        return new TetrisEvent(TetrisEventType.FUNCTION_SPACE_DATA_LOAD_COMPLETED, propertyId, new Date(), metaData);
    }

    public TetrisEvent createFunctionSpaceInitiatedEvent(int propertyId, String inputIdentifier) {
        ArrayList<MetaData> mdList = getMetaDataForProcessingStartedEvent(inputIdentifier);
        return new TetrisEvent(TetrisEventType.FUNCTION_SPACE_DATA_LOAD_INITIATED, propertyId, new Date(), mdList);
    }

    public TetrisEvent createScheduledReportsInitiatedEvent(int propertyId, String inputIdentifier) {
        ArrayList<MetaData> mdList = getMetaDataForProcessingStartedEvent(inputIdentifier);
        return new TetrisEvent(TetrisEventType.SCHEDULED_REPORTS_INITIATED, propertyId, new Date(), mdList);
    }

    public TetrisEvent createScheduledReportsCompletedEvent(int propertyId, String inputIdentifier) {
        ArrayList<MetaData> metaData = getEventMetaDataListAsPerIdentifierAndJobInstance(inputIdentifier);
        return new TetrisEvent(TetrisEventType.SCHEDULED_REPORTS_COMPLETED, propertyId, new Date(), metaData);
    }

    public TetrisEvent createPacmanExtractGeneratedEvent(int propertyId, String inputIdentifier,
                                                         String pacmanExtractFile) {
        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        mdList.add(new MetaData(MetaData.INPUT_IDENTIFIER, cleanFileName(inputIdentifier)));
        mdList.add(new MetaData(MetaData.TYPE_2_FILE_NAME, cleanFileName(pacmanExtractFile)));
        setJobInstanceIdInMetaDataList(mdList);

        return new TetrisEvent(TetrisEventType.PACMAN_EXTRACT_GENERATED, propertyId, new Date(), mdList);
    }

    private ArrayList<MetaData> getMetaDataForProcessingStartedEvent(String inputIdentifier) {
        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        mdList.add(new MetaData(MetaData.INPUT_IDENTIFIER, cleanFileName(inputIdentifier)));
        setJobInstanceIdInMetaDataList(mdList);
        return mdList;
    }

    private ArrayList<MetaData> getDecisionUploadEventMetaDataList(String decisionIdentifier, String inputIdentifier) {
        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        mdList.add(new MetaData(MetaData.INPUT_IDENTIFIER, inputIdentifier));
        mdList.add(new MetaData(MetaData.DECISIONS_IDENTIFIER, decisionIdentifier));
        setJobInstanceIdInMetaDataList(mdList);
        return mdList;
    }

    private ArrayList<MetaData> getCompletedEventMataDataList(String inputIdentifier, String type2FileName) {
        ArrayList<MetaData> mdList = new ArrayList<MetaData>();
        if (inputIdentifier != null) {
            mdList.add(new MetaData(MetaData.INPUT_IDENTIFIER, cleanFileName(inputIdentifier)));
        } else {
            mdList.add(new MetaData(MetaData.TYPE_2_FILE_NAME, cleanFileName(type2FileName)));
        }
        setJobInstanceIdInMetaDataList(mdList);
        return mdList;
    }

    private void setJobInstanceIdInMetaDataList(ArrayList<MetaData> mdList) {
        JobStepContext jobStepContext = PlatformThreadLocalContextHolder.getJobStepContext();
        if (jobStepContext != null && jobStepContext.getJobInstanceId() != null) {
            mdList.add(new MetaData(MetaData.JOB_INSTANCE_ID, jobStepContext.getJobInstanceId().toString()));
        }
    }

    private ArrayList<MetaData> getEventMetaDataListAsPerIdentifierAndJobInstance(String inputIdentifier) {
        ArrayList<MetaData> metaData = new ArrayList<MetaData>();
        if (inputIdentifier != null) {
            metaData.add(new MetaData(MetaData.INPUT_IDENTIFIER, inputIdentifier));
        }
        setJobInstanceIdInMetaDataList(metaData);
        return metaData;
    }

    private long getBDEDecisionsUploadedEventDelay() {
        return DEFAULT_BDE_DECISIONS_UPLOADED_DELAY;
    }

    protected TopicMessageProducer getTopicMessageProducer() {
        return topicMessageProducer;
    }


    public String raise(String propertyId, String type) {
        try {
            TetrisEvent tetrisEvent = new TetrisEvent();
            tetrisEvent.setType(TetrisEventType.valueOf(type));
            tetrisEvent.setPropertyId(Integer.parseInt(propertyId));
            tetrisEvent.setDate(new Date());

            tetrisEvent.getMetaData().add(new MetaData(MetaData.INITIATED_BY, "REST"));
            tetrisEvent.getMetaData().add(new MetaData(MetaData.INPUT_IDENTIFIER, UUID.randomUUID().toString()));

            raiseEvent(tetrisEvent);
        } catch (TetrisException e) {
            LOGGER.debug("Problem in setting Tetris Event, returning with failure", e);
            return "failure";
        }

        return "success";
    }

    public void raiseEvent(TetrisEvent tetrisEvent) {
        try {
            LOGGER.info("Sending TetrisEvent " + tetrisEvent.getType() + " " + tetrisEvent.getPropertyId() + " to "
                                + TOPIC_NAME_NEW);
            if (TetrisEventType.BDE_DECISIONS_UPLOADED.equals(tetrisEvent.getType())) {
                /*
                 * Special handling for the BDE_DECISIONS_UPLOADED event. We want to delay the delivery of this message
                 * because it can get processed out ahead of the BDE_DECISIONS_GENERATED event in a multi-node
                 * environment. See DE2072
                 */
                sendMessageWithDelay(tetrisEvent);
            } else {
                sendMessage(tetrisEvent);
            }

            if (TetrisEventType.BDE_COMPLETED.equals(tetrisEvent.getType())
                        || TetrisEventType.CDP_COMPLETED.equals(tetrisEvent.getType())) {
                ejbPacmanEventPublisher.publishTetrisEvent(tetrisEvent);
            }
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.JMS_ERROR, " Exception occurred while sending JMS Message "
                                                                   + tetrisEvent.getType() + " " + tetrisEvent.getPropertyId() + " to " + TOPIC_NAME_NEW + " "
                                                                   + e.getMessage(), e);
        }
    }

    private void sendMessage(TetrisEvent tetrisEvent) {
        getTopicMessageProducer().sendTopicMessage(TOPIC_NAME_NEW, tetrisEvent);
    }

    private void sendMessageWithDelay(TetrisEvent tetrisEvent) {
        long delay = getBDEDecisionsUploadedEventDelay();
        getTopicMessageProducer().sendTopicMessage(TOPIC_NAME_NEW, tetrisEvent, System.currentTimeMillis() + delay);
    }
}
