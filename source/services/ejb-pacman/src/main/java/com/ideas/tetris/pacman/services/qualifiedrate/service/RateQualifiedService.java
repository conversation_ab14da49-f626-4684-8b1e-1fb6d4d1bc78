package com.ideas.tetris.pacman.services.qualifiedrate.service;

import com.ideas.api.client.rate.model.RatePlanDto;
import com.ideas.recommendation.common.RecommendationCommonConstants;
import com.ideas.recommendation.compression.GzipCompressionHelper;
import com.ideas.recommendation.model.RateDetails;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductRateCode;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetaDataBuffer;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.RecordType;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dateservice.dto.DateRange;
import com.ideas.tetris.pacman.services.filemetadata.FileMetadataService;
import com.ideas.tetris.pacman.services.job.JobMonitorService;
import com.ideas.tetris.pacman.services.job.JobViewCriteria;
import com.ideas.tetris.pacman.services.job.entity.JobView;
import com.ideas.tetris.pacman.services.linkedsrp.entity.LinkedSRPMappings;
import com.ideas.tetris.pacman.services.linkedsrp.repository.RateQualifiedFixedRepository;
import com.ideas.tetris.pacman.services.linkedsrp.service.LinkedSrpAPIService;
import com.ideas.tetris.pacman.services.marketsegment.entity.ProcessStatus;
import com.ideas.tetris.pacman.services.product.InvalidReason;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.qualifiedrate.component.RateQualifiedComponent;
import com.ideas.tetris.pacman.services.qualifiedrate.dto.*;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualified;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualifiedDetails;
import com.ideas.tetris.pacman.services.ratepopulation.entity.RateQualifiedFixedDetails;
import com.ideas.tetris.pacman.services.rates.QualifiedRateConverter;
import com.ideas.tetris.pacman.services.rates.RateClientAPIService;
import com.ideas.tetris.pacman.services.rates.SemiYieldableRateConverter;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.AbstractRate;
import com.ideas.tetris.pacman.util.ConfigParamsUtil;
import com.ideas.tetris.pacman.util.SeasonService;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystemHelper;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.rest.mapper.RestClient;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.client.ClientCodePropertyCodeMappingService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.time.StopWatch;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.ws.rs.QueryParam;
import javax.ws.rs.client.Entity;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.*;
import static com.ideas.tetris.platform.common.rest.mapper.RestEndpoints.QUALIFIED_RATE_DERIVED_STATUS_RATE_REQUEST;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.getCurrentDate;
import static java.util.Objects.isNull;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.collections.CollectionUtils.isEmpty;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang.math.RandomUtils.nextInt;

@Slf4j
@Component
@Transactional
public class RateQualifiedService {
    public static final String CYCLIC_SRP_EXCEPTION_MESSAGE = "The statement terminated. The maximum recursion 100 has been exhausted before statement completion.";
    public static final String YIELDABLE_RATES_COUNT_BY_PROPERTY_ID = "select COUNT(*) from (select Top 1 1 as cnt from Rate_Qualified where Property_ID=:propertyId and Yieldable=1 and status_Id=1) a;";
    protected static final String FETCH_RATE_QUALIFIED_DETAILS_WITH_ACCOM_TYPES = "SELECT rt.Accom_Type_ID as accomTypeId, rt.Accom_Type_Name, " +
            "innerTable.Start_Date_DT as startDateDt, innerTable.End_Date_DT as endDateDt, rud.*, rt.Status_ID, " +
            "ac.Accom_Class_Name as accomClassName, ac.Rank_Order as accomClassRankOrder, rt.Display_Status_ID FROM " +
            "[Accom_Type] rt CROSS JOIN (SELECT DISTINCT Rate_Qualified_ID, Start_Date_DT, End_Date_DT FROM [Rate_Qualified_Details] where " +
            "Rate_Qualified_ID = (:rateQualifiedId) ) as innerTable LEFT JOIN [Rate_Qualified_Details] as rud " +
            "ON (rud.Accom_Type_ID = rt.Accom_Type_ID AND innerTable.Start_Date_DT = rud.Start_Date_DT AND innerTable.End_Date_DT = rud.End_Date_DT " +
            "AND rud.Rate_Qualified_ID = innerTable.Rate_Qualified_ID) inner join Accom_Class as ac on (ac.Accom_Class_ID = rt.Accom_Class_ID) " +
            "WHERE rt.Property_ID = :propertyId AND ac.System_Default = 0 ORDER BY innerTable.Start_Date_DT, innerTable.End_Date_DT, ac.Rank_Order, rt.Accom_Type_Name";
    private static final Logger LOGGER = Logger.getLogger(RateQualifiedService.class);
    private static final String CURRENCY = "currency";
    private static final String START_DATE = "startDate";
    private static final String END_DATE = "endDate";
    private static final String IDS = "ids";
    private static final String TODAY_DATE = "todayDate";
    private static final String FETCH_RATE_QUALIFIED_DETAILS_FOR_RATE_QUALIFIED_WITHIN_DATE_RANGE = "SELECT rt.Accom_Type_ID as accomTypeId, rt.Accom_Type_Name, " +
            "innerTable.Start_Date_DT as startDateDt, innerTable.End_Date_DT as endDateDt, rud.*, rt.Status_ID, " +
            "ac.Accom_Class_Name as accomClassName, ac.Rank_Order as accomClassRankOrder, rt.Display_Status_ID FROM " +
            "[Accom_Type] rt CROSS JOIN (SELECT DISTINCT Rate_Qualified_ID, Start_Date_DT, End_Date_DT FROM [Rate_Qualified_Details] where " +
            "Rate_Qualified_ID = (:rateQualifiedId) AND Start_Date_DT = :startDate and End_Date_DT = :endDate) as innerTable LEFT JOIN [Rate_Qualified_Details] as rud " +
            "ON (rud.Accom_Type_ID = rt.Accom_Type_ID AND innerTable.Start_Date_DT = rud.Start_Date_DT AND innerTable.End_Date_DT = rud.End_Date_DT " +
            "AND rud.Rate_Qualified_ID = innerTable.Rate_Qualified_ID) inner join Accom_Class as ac on (ac.Accom_Class_ID = rt.Accom_Class_ID) " +
            "WHERE rt.Property_ID = :propertyId AND ac.System_Default = 0 ORDER BY innerTable.Start_Date_DT, innerTable.End_Date_DT, ac.Rank_Order, rt.Accom_Type_Name";
    private static final String FETCH_SEASON_BETWEEN_DATE_RANGE = new StringBuilder()
            .append(" SELECT rq.Rate_Qualified_ID,rq.Rate_Code_Name, ")
            .append(" rq.Start_Date_DT as rateHeaderStartDate,rq.End_Date_DT as rateHeaderEndDate, ac.Accom_Class_Name, at.Accom_Type_Code, ")
            .append(" rqd.Start_Date_DT as seasonStartDate,rqd.End_Date_DT as seasonEndDate,  ")
            .append(" rqd.Sunday,rqd.Monday,rqd.Tuesday,rqd.Wednesday,rqd.Thursday,rqd.Friday,rqd.Saturday, ac.Accom_Class_Code, rq.Managed_In_G3,   ")
            .append(" at.Accom_Type_ID, ac.Accom_Class_ID,  rq.Property_Id, rq.Yieldable, rq.Rate_Qualified_Type_Id,  rq.Rate_Code_Description, at.Accom_Type_Name,ac.Rank_Order   ")
            .append(" FROM Rate_Qualified rq ")
            .append(" INNER JOIN Rate_Qualified_Details rqd ON (rqd.Rate_Qualified_ID = rq.Rate_Qualified_ID AND rq.Status_ID = 1 AND rqd.End_Date_DT >=:todayDate AND ")
            .append(" rqd.Rate_qualified_ID IN (:ids) AND NOT ((rqd.End_Date_DT < :startDate) OR (rqd.Start_Date_DT > :endDate))) ")
            .append(" INNER JOIN Accom_type at ON ( at.Accom_Type_ID = rqd.Accom_Type_ID AND at.Status_ID = 1 AND at.System_Default = 0 ) ")
            .append(" INNER JOIN Accom_Class ac ON (ac.accom_class_id = at.accom_class_id and ac.Status_ID = 1 AND ac.System_Default = 0 ) ")
            .append("  order by rq.Rate_Code_Name, seasonStartDate, seasonEndDate, ac.Rank_Order, at.Accom_Type_Name ")
            .toString();
    private static final String FETCH_ALL_FUTURE_SEASONS = new StringBuilder()
            .append(" SELECT rq.Rate_Qualified_ID,rq.Rate_Code_Name, ")
            .append(" rq.Start_Date_DT as rateHeaderStartDate,rq.End_Date_DT as rateHeaderEndDate, ac.Accom_Class_Name, at.Accom_Type_Code, ")
            .append(" rqd.Start_Date_DT as seasonStartDate,rqd.End_Date_DT as seasonEndDate,  ")
            .append(" rqd.Sunday,rqd.Monday,rqd.Tuesday,rqd.Wednesday,rqd.Thursday,rqd.Friday,rqd.Saturday, ac.Accom_Class_Code, rq.Managed_In_G3,   ")
            .append(" at.Accom_Type_ID, ac.Accom_Class_ID,  rq.Property_Id, rq.Yieldable, rq.Rate_Qualified_Type_Id,  rq.Rate_Code_Description, at.Accom_Type_Name,ac.Rank_Order   ")
            .append(" FROM Rate_Qualified rq ")
            .append(" INNER JOIN Rate_Qualified_Details rqd ON (rqd.Rate_Qualified_ID = rq.Rate_Qualified_ID AND rq.Status_ID = 1 AND rqd.End_Date_DT >=:todayDate ) ")
            .append(" INNER JOIN Accom_type at ON ( at.Accom_Type_ID = rqd.Accom_Type_ID AND at.Status_ID = 1 AND at.System_Default = 0 ) ")
            .append(" INNER JOIN Accom_Class ac ON (ac.accom_class_id = at.accom_class_id and ac.Status_ID = 1 AND ac.System_Default = 0 ) ")
            .append("  order by rq.Rate_Code_Name, seasonStartDate, seasonEndDate, ac.Rank_Order, at.Accom_Type_Name ")
            .toString();
    private static final String FETCH_ACTIVE_RATE_QUALIFIED_DETAILS_WITHIN_DATE_RANGE = "SELECT at.Accom_Type_ID as accomTypeId," +
            " at.Accom_Type_Name,rqd.Start_Date_DT  as seasonStartDate,rqd.End_Date_DT as seasonEndDate,rqd.*,at.status_id, " +
            " ac.Accom_Class_Name as accomClassName, ac.Rank_Order as accomClassRankOrder, at.Display_Status_ID FROM Rate_Qualified rq INNER JOIN " +
            " Rate_Qualified_Details rqd ON (rqd.Rate_Qualified_ID = rq.Rate_Qualified_ID  AND rq.Status_ID = 1 AND rqd.Rate_qualified_ID =:id " +
            " AND NOT ((rqd.End_Date_DT < :startDate) OR (rqd.Start_Date_DT > :endDate))) INNER JOIN Accom_type at ON " +
            " ( at.Accom_Type_ID = rqd.Accom_Type_ID AND at.Status_ID = 1 AND at.System_Default = 0 ) INNER JOIN Accom_Class ac ON " +
            " (ac.accom_class_id = at.accom_class_id and ac.Status_ID = 1 AND ac.System_Default = 0 )  order by rq.Rate_Code_Name, " +
            " Start_Date_DT, End_Date_DT,accomClassRankOrder, at.Accom_Type_Name ";
    private static final String FETCH_ACTIVE_RATE_QUALIFIED_COUNT_WITHIN_DATE_RANGE = new StringBuilder()
            .append("SELECT count(*) FROM Rate_Qualified rq ")
            .append("INNER JOIN Rate_Qualified_Details rqd ON (rqd.Rate_Qualified_ID = rq.Rate_Qualified_ID  AND rq.Status_ID = 1 ")
            .append("AND NOT ((rqd.End_Date_DT < :startDate) OR (rqd.Start_Date_DT > :endDate))) ")
            .append("INNER JOIN Accom_type as at ON ( at.Accom_Type_ID = rqd.Accom_Type_ID AND at.Status_ID = 1 AND at.System_Default = 0 ) ")
            .append("INNER JOIN Accom_Class as ac ON (ac.accom_class_id = at.accom_class_id and ac.Status_ID = 1 AND ac.System_Default = 0 ) ")
            .toString();
    private static final String FIND_RATE_QUALIFIED_IDS_WITH_MATCHING_DATE_RANGE_OF_SOURCE_RATE =
            "select distinct rq.Rate_Qualified_ID from Rate_Qualified_Details rqd " +
                    "join Rate_Qualified rq on rq.Rate_Qualified_ID = rqd.Rate_Qualified_ID and rq.Status_ID = 1 " +
                    "where rqd.End_Date_DT >= :caughtUpDate " +
                    "and not (rqd.End_Date_DT < :rateHeaderStartDate OR rqd.Start_Date_DT > :rateHeaderEndDate)";
    private static final String FIND_MISSING_ACCOM_TYPES_IDS =
            "select Accom_Type_ID from Accom_Type where Status_ID = 1 and Accom_Type_ID not in (" +
                    "select distinct accom_Type_ID from Rate_Qualified rq inner join Rate_qualified_Details rqd on rq.Rate_Qualified_ID = rqd.Rate_Qualified_ID where rate_code_name = 'LV0')";
    private static final String DEACTIVATE_ALL_DERIVED_RATES = "update Rate_Qualified set Status_ID = 2 where Rate_Qualified_Type_Id in (1,2) and Managed_In_G3 = 0";
    private static final String CAUGHT_UP_DATE = "caughtUpDate";
    private static final String OPTIMIZATION_END_DATE = "optimizationEndDate";
    private static final String STATUS_ID = "Status_ID";
    public static final String RATE_QUALIFIED_IDS = "rateQualifiedIds";
    public static final String DATE = "date";
    public static final String PRODUCT_ID = "productId";
    public static final String RATE_CODE = "rateCode";
    public static final String FILE_METADATA_IDS = "fileMetadataIds";
    public static final String STATUS = "statusId";
    public static final String RATE_CODE_NAME = "rateCodeName";
    @Autowired
    DateService dateService;
    @Autowired
    SeasonService seasonService;
    @Autowired
    ExternalSystemHelper externalSystemHelper;
    @Autowired
    PacmanConfigParamsService configService;
    @Autowired
    AbstractMultiPropertyCrudService multiPropertyCrudService;
    @Autowired
    private RateQualifiedComponent rateQualifiedComponent;
    @TenantCrudServiceBean.Qualifier
    @Autowired
    private CrudService crudService;
    @Autowired
    private QualifiedRateConverter rateConverter;
    @Autowired
    private SemiYieldableRateConverter semiYieldableRateConverter;
    @Autowired
    private ConfigParamsUtil configParamsUtil;
    @Autowired
    private JobMonitorService jobMonitorService;
    @Autowired
    JobServiceLocal jobService;
    @Autowired
    private RestClient restClient;
    @Autowired
    private PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
    RateClientAPIService rateClientAPIService;
    @Autowired
    private ClientCodePropertyCodeMappingService clientCodePropertyCodeMappingService;
    @Autowired
    private RateQualifiedFixedRepository rateQualifiedFixedRepository;
    @Autowired
    private FileMetadataService fileMetadataService;
    @Autowired
    private LinkedSrpAPIService linkedSrpAPIService;
    @Autowired
    private AgileRatesConfigurationService agileRatesConfigurationService;

    public static final BigDecimal ZERO_OFFSET = new BigDecimal("0.00000");

    public void setSeasonService(SeasonService seasonService) {
        this.seasonService = seasonService;
    }

    public void setCrudService(CrudService tenantCrudService) {
        this.crudService = tenantCrudService;
    }

    public void setDateService(DateService dateService) {
        this.dateService = dateService;
    }

    public void setQualifiedComponent(RateQualifiedComponent rateQualifiedComponent) {
        this.rateQualifiedComponent = rateQualifiedComponent;
    }

    public List<RateQualified> getRateQualifiedByProperty() {
        return rateQualifiedComponent.getRateQualifiedByProperty();
    }

    private void saveRateHeader(RateHeader rateHeader, Integer fileMetadataId) {
        RateQualified rateQualified = getRateQualifiedEntity(rateHeader);

        notifyDerivedStatusChange(rateHeader);

        rateQualified.setUserProvidedValues(rateHeader, fileMetadataId);
        rateQualified = crudService.save(rateQualified);
        rateHeader.setId(rateQualified.getId());
    }

    public RateQualified getRateQualifiedEntity(RateHeader rateHeader) {
        return existing(rateHeader) ? crudService.find(RateQualified.class, rateHeader.getId()) : newRateQualifiedWithDefaultValues();
    }

    private boolean existing(RateHeader rateHeader) {
        return rateHeader.getId() != null;
    }

    private RateQualified newRateQualifiedWithDefaultValues() {
        RateQualified rateQualified = new RateQualified();
        rateQualified.setDefaultValues();
        return rateQualified;
    }


    public FileMetadata createFileMetadata() {
        String fileName = String.format("%s_%d_%s", FILE_NAME, nextInt(1000), getCurrentDate());
        FileMetadata metaData = new FileMetaDataBuffer(fileName, Constants.RATE_PLANS_BY_USER, Constants.FILE_LOCATION).value();
        return crudService.save(metaData);
    }


    /**
     * It accepts the list of RateHeader dto, generates file metadata id, validates rate header and calls saveRateHeader method to save changes of each RateHeader.
     *
     * @param rateHeaders
     */
    public void saveQualifiedRates(List<RateHeader> rateHeaders) {
        Integer fileMetadataId = createFileMetadata().getId();
        for (RateHeader rateHeader : rateHeaders) {
            if (rateHeader.isDeleted()) {
                deleteRateHeader(rateHeader);
            } else {
                saveRateHeaderWithSeason(fileMetadataId, rateHeader);
            }
        }
    }

    public void saveYieldableQualifiedRates(final List<RateQualified> qualifiedRates) {

        final Integer fileMetadataId = createFileMetadata().getId();
        qualifiedRates.forEach(qualifiedRate -> qualifiedRate.setFileMetadataId(fileMetadataId));
        crudService.save(qualifiedRates);
    }

    private void saveRateHeaderWithSeason(Integer fileMetadataId, RateHeader rateHeader) {
        rateHeader.validate();
        saveRateHeader(rateHeader, fileMetadataId);
        saveLimitTotalRates(rateHeader);
        if (isNotEmpty(rateHeader.getSeasons())) {
            saveSeasons(rateHeader.getSeasons(), rateHeader.getId());
        }
    }

    public void saveLimitTotalRates(RateHeader rateHeader) {
        if (rateHeader.isLimitTotal()) {
            crudService.executeUpdateByNativeQuery("IF NOT EXISTS (select 1 from [Limit_Total_Rate_Qualified] where Limit_Total_Rate_Qualified_ID=" + rateHeader.getId() + " ) " +
                    "INSERT into [Limit_Total_Rate_Qualified] (Limit_Total_Rate_Qualified_ID) values (" + rateHeader.getId() + ");"
            );
        } else {
            deleteLimitTotalRateQualified(rateHeader);
        }
    }

    public List<RateQualifiedDetails> findAll() {
        return crudService.findAll(RateQualifiedDetails.class);
    }

    private void deleteLimitTotalRateQualified(RateHeader rateHeader) {
        crudService.executeUpdateByNativeQuery(
                "IF EXISTS (select 1 from [Limit_Total_Rate_Qualified] where Limit_Total_Rate_Qualified_ID =" + rateHeader.getId() + " ) " +
                        "DELETE from [Limit_Total_Rate_Qualified]  where Limit_Total_Rate_Qualified_ID =" + rateHeader.getId() + ";");
    }

    public List<RateQualifiedDetails> getRateQualifiedDetails(Date startDate, Date endDate) {
        DateRange dateRange = populateDates(startDate, endDate);
        return crudService.findByNamedQuery(RateQualifiedDetails.BY_PROPERTY_ID_AND_ACTIVE_IN_RANGE, getParamMap(dateRange));
    }

    public List<RateQualifiedFixedDetails> getRateQualifiedFixedDetails(Date startDate, Date endDate) {
        DateRange dateRange = populateDates(startDate, endDate);
        return crudService.findByNamedQuery(RateQualifiedFixedDetails.BY_PROPERTY_ID_AND_ACTIVE_IN_RANGE, getParamMap(dateRange));
    }

    public List<RateQualifiedFixedDetails> getRateQualifiedFixedDetailsByRateId(Date startDate, Date endDate, List<Integer> rateIds) {
        DateRange dateRange = populateDates(startDate, endDate);
        return crudService.findByNamedQuery(RateQualifiedFixedDetails.BY_PROPERTY_ID_AND_ACTIVE_IN_RANGE_BY_RATE_ID, getParamMap(dateRange, rateIds));
    }

    private Map<String, Object> getParamMap(DateRange dateRange) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("propertyId", PacmanWorkContextHelper.getPropertyId());
        parameters.put("startDate", dateRange.getStartDate());
        parameters.put("endDate", dateRange.getEndDate());
        return parameters;
    }

    private Map<String, Object> getParamMap(DateRange dateRange, List<Integer> rateIds) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("propertyId", PacmanWorkContextHelper.getPropertyId());
        parameters.put("startDate", dateRange.getStartDate());
        parameters.put("endDate", dateRange.getEndDate());
        parameters.put("rateIds", rateIds);
        return parameters;
    }

    private DateRange populateDates(Date startDate, Date endDate) {
        if (startDate == null) {
            startDate = dateService.getOptimizationWindowStartDate();
        }
        if (endDate == null) {
            endDate = dateService.getDecisionUploadWindowEndDate();
        }
        return new DateRange(startDate, endDate);
    }

    public List<RateHeader> fetchQualifiedRates() {
        List<RateHeader> rateHeaders = fetchAllActiveRateHeaders();
        for (RateHeader rateHeader : rateHeaders) {
            List<QualifiedRateSeason> seasons = fetchSeasonsForRateHeader(rateHeader);
            rateHeader.setSeasons(seasons);
        }
        return rateHeaders;
    }


    public List<RateHeader> fetchActiveRateHeaders() {
        if (isAgileRatesEnabled()) {
            return crudService.findByNamedQuery(RateQualified.ALL_RATE_HEADERS_NOT_LINKED_TO_AGILE_PRODUCT_BY_PROPERTY_AND_STATUS,
                    QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                            .and("statusId", Constants.ACTIVE_STATUS_ID).parameters());
        }
        return fetchAllActiveRateHeaders();
    }

    protected boolean isAgileRatesEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED);
    }

    public List<RateHeader> fetchAllActiveRateHeaders() {
        return crudService.findByNamedQuery(RateQualified.ALL_RATE_HEADERS_BY_PROPERTY_AND_STATUS,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("statusId", Constants.ACTIVE_STATUS_ID).parameters());
    }

    public List<Integer> fetchAllTotalLevelRates() {
        return crudService.findByNativeQuery("select Limit_Total_Rate_Qualified_ID from Limit_Total_Rate_Qualified");
    }

    public List<RateQualified> fetchAllActiveRates() {
        return crudService.findByNamedQuery(RateQualified.ALL_RATES_BY_PROPERTY_AND_STATUS,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("statusId", Constants.ACTIVE_STATUS_ID).parameters());

    }

    public SeasonDetail saveSeasonDetail(SeasonDetail seasonDetail, Integer rateQualifiedId,
                                  LocalDate startDate, LocalDate endDate) {
        RateQualifiedDetails rateQualifiedDetails = buildRateQualifiedDetailsEntity(seasonDetail);
        if (isDayOfWeeksLessThanSevenDaysFor(startDate, endDate)) {
            clearOutOfRangeDayOfWeekValues(seasonDetail, startDate, endDate);
        }
        setValuesFromDtoToEntity(seasonDetail, rateQualifiedDetails);
        rateQualifiedDetails.setRateQualifiedId(rateQualifiedId);
        rateQualifiedDetails.setStartDate(startDate.toDate());
        rateQualifiedDetails.setEndDate(endDate.toDate());

        rateQualifiedDetails = saveRateQualifiedDetail(rateQualifiedDetails);
        seasonDetail.setId(rateQualifiedDetails.getId());
        return seasonDetail;
    }

    public RateQualifiedDetails saveRateQualifiedDetail(RateQualifiedDetails rateQualifiedDetails) {
        rateQualifiedDetails = crudService.save(rateQualifiedDetails);
        return rateQualifiedDetails;
    }

    private boolean isDayOfWeeksLessThanSevenDaysFor(LocalDate startDate, LocalDate endDate) {
        return LocalDateUtils.getOutOfRangeDayOfWeeks(startDate, endDate).size() < 7;
    }

    public RateQualifiedDetails buildRateQualifiedDetailsEntity(SeasonDetail seasonDetail) {
        if (null != seasonDetail.getId()) {
            return crudService.find(RateQualifiedDetails.class, seasonDetail.getId());
        } else {
            return new RateQualifiedDetails();
        }
    }

    public void setValuesFromDtoToEntity(SeasonDetail seasonDetail,
                                  RateQualifiedDetails rateQualifiedDetails) {
        rateQualifiedDetails.setDaysOfWeekForGiven(seasonDetail);
        rateQualifiedDetails.setAccomTypeId(seasonDetail.getAccomTypeId());
    }


    public void clearOutOfRangeDayOfWeekValues(SeasonDetail seasonDetail, LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null) {
            return;
        }
        BigDecimal noRateValue = new BigDecimal(-1);
        List<DayOfWeek> outOfRangeDayOfWeeks = LocalDateUtils.getOutOfRangeDayOfWeeks(startDate, endDate);
        for (DayOfWeek outOfRangeDayOfWeek : outOfRangeDayOfWeeks) {
            switch (outOfRangeDayOfWeek) {
                case SUNDAY:
                    seasonDetail.setSunday(noRateValue);
                    break;
                case MONDAY:
                    seasonDetail.setMonday(noRateValue);
                    break;
                case TUESDAY:
                    seasonDetail.setTuesday(noRateValue);
                    break;
                case WEDNESDAY:
                    seasonDetail.setWednesday(noRateValue);
                    break;
                case THURSDAY:
                    seasonDetail.setThursday(noRateValue);
                    break;
                case FRIDAY:
                    seasonDetail.setFriday(noRateValue);
                    break;
                case SATURDAY:
                    seasonDetail.setSaturday(noRateValue);
                    break;
            }
        }
    }

    public void saveSeasons(List<QualifiedRateSeason> seasons, Integer rateQualifiedId) {
        Collections.sort(seasons);
        identifyAndDeleteSeasons(seasons);
        identifyAndSaveSeasons(seasons, rateQualifiedId);
    }

    protected void identifyAndSaveSeasons(List<QualifiedRateSeason> seasons, Integer rateQualifiedId) {
        StopWatch timerForSave = new StopWatch();
        timerForSave.start();
        List<QualifiedRateSeason> seasonsForSave =
                seasons.stream().filter(season -> !season.isDeleted()).collect(Collectors.toList());
        saveSeasonDetailsForRoomClass(rateQualifiedId, seasonsForSave);
        LOGGER.info("Seasons for Save took time: " + timerForSave.getTime() + "ms");
    }

    protected void identifyAndDeleteSeasons(List<QualifiedRateSeason> seasons) {
        List<QualifiedRateSeason> seasonsForDeletion =
                seasons.stream().filter(QualifiedRateSeason::isDeleted).collect(Collectors.toList());
        StopWatch timer = new StopWatch();
        timer.start();
        for (QualifiedRateSeason seasonForDeletion : seasonsForDeletion) {
            deleteSeason(seasonForDeletion);
        }
        if (isNotEmpty(seasonsForDeletion)) {
            crudService.flush();
        }
        LOGGER.info("Seasons for deletion took time: " + timer.getTime() + "ms");
    }

    public boolean isPastSeason(QualifiedRateSeason season) {
        Date businessDate = dateService.getBusinessDate();
        Date seasonEndDate = season.getEndDate().toDate();
        return seasonEndDate.before(businessDate);
    }

    private void saveSeasonDetailsForRoomClass(Integer rateQualifiedId,
                                               List<QualifiedRateSeason> seasons) {
        List<SeasonDetail> seasonDetailsForDeletion = new ArrayList<>();
        List<SeasonDetail> seasonDetailsForUpdation = new ArrayList<>();
        List<SeasonDetail> seasonDetailsForCreation = new ArrayList<>();
        for (QualifiedRateSeason season : seasons) {
            for (RoomClass roomClass : season.getRoomClasses()) {
                for (SeasonDetail seasonDetail : roomClass.getSeasonDetails()) {
                    boolean hasNoRateValueForAllDows = hasNoRateValueForAllDows(seasonDetail);
                    if (hasNoRateValueForAllDows && seasonDetail.getId() != null) {
                        seasonDetailsForDeletion.add(seasonDetail);
                    } else if (!shouldSkip(seasonDetail, hasNoRateValueForAllDows)) {
                        seasonDetail.setStartDate(season.getStartDate());
                        seasonDetail.setEndDate(season.getEndDate());
                        if (isNull(seasonDetail.getId())) {
                            seasonDetailsForCreation.add(seasonDetail);
                        } else {
                            seasonDetailsForUpdation.add(seasonDetail);
                        }
                    }
                }
            }
        }
        deleteSeasonDetails(seasonDetailsForDeletion);
        saveSeasonDetails(rateQualifiedId, seasonDetailsForUpdation, seasonDetailsForCreation);
    }

    private void saveSeasonDetails(Integer rateQualifiedId,
                                   List<SeasonDetail> seasonDetailsForUpdation,
                                   List<SeasonDetail> seasonDetailsForCreation) {
        StopWatch timerForSave = new StopWatch();
        timerForSave.start();
        seasonDetailsForUpdation.forEach(seasonDetail -> saveSeasonDetail(seasonDetail, rateQualifiedId,
                seasonDetail.getStartDate(), seasonDetail.getEndDate()));
        if (isNotEmpty(seasonDetailsForUpdation)) {
            crudService.flush();
        }
        seasonDetailsForCreation.forEach(seasonDetail -> saveSeasonDetail(seasonDetail, rateQualifiedId,
                seasonDetail.getStartDate(), seasonDetail.getEndDate()));
        if (isNotEmpty(seasonDetailsForCreation)) {
            crudService.flush();
        }
        LOGGER.info("In saveSeasonDetailsForRoomClass(): Seasons for Save took time: " + timerForSave.getTime() + "ms");
    }

    private void deleteSeasonDetails(List<SeasonDetail> seasonDetailsForDeletion) {
        StopWatch timerForDeletion = new StopWatch();
        timerForDeletion.start();
        seasonDetailsForDeletion.forEach(seasonDetail -> deleteSeasonDetail(seasonDetail));
        if (isNotEmpty(seasonDetailsForDeletion)) {
            crudService.flush();
        }
        LOGGER.info("In saveSeasonDetailsForRoomClass(): Seasons for Deletion took time: " + timerForDeletion.getTime() + "ms");
    }

    private boolean shouldSkip(SeasonDetail seasonDetail,
                               boolean hasNoRateValueForAllDows) {
        return hasNoRateValueForAllDows && seasonDetail.getId() == null;
    }

    public boolean hasNoRateValueForAllDows(SeasonDetail seasonDetail) {
        return seasonDetail.hasNoRateValueForAllDows();

    }

    public boolean isDerivedTypeEnabled() {
        return configService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_DERIVED_QUALIFIED_RATE_PLAN_ENABLED.value());
    }

    public List<Integer> findAccomTypeIdMissingInRateQualified() {
        return crudService.findByNativeQuery(FIND_MISSING_ACCOM_TYPES_IDS);
    }

    public RateQualified findRateQualifiedLV0() {
        return crudService.findByNamedQuerySingleResult(RateQualified.GET_ACTIVE_RATE_QUALIFIED_BY_NAME, QueryParameter.with("name", "LV0").parameters());
    }

    public List<Integer> findRateQualifiedIdsMatchingDateRangeOfSourceRateHeader(LocalDate startDate, LocalDate endDate) {
        List<Integer> rateQualifiedIds = crudService.findByNativeQuery(
                FIND_RATE_QUALIFIED_IDS_WITH_MATCHING_DATE_RANGE_OF_SOURCE_RATE,
                QueryParameter.with("rateHeaderStartDate", startDate).and("rateHeaderEndDate", endDate).and(CAUGHT_UP_DATE, dateService.getCaughtUpLocalDate()).parameters(),
                mapper -> (Integer) mapper[0]
        );

        if (rateQualifiedIds == null) {
            return Collections.emptyList();
        }

        return rateQualifiedIds;
    }

    public List<QualifiedRateSeason> fetchSeasonsForRateHeader(RateHeader rateHeader) {
        QueryParameter queryParameters = QueryParameter.with("rateQualifiedId", rateHeader.getId())
                .and("propertyId", PacmanWorkContextHelper.getPropertyId());

        return getSeasonsFor(queryParameters, FETCH_RATE_QUALIFIED_DETAILS_WITH_ACCOM_TYPES);
    }

    public List<QualifiedRateSeason> fetchSeasonForRateQualifiedWithinDateRange(Integer rateQualifiedId, LocalDate startDate, LocalDate endDate) {
        QueryParameter queryParameters = QueryParameter.with("rateQualifiedId", rateQualifiedId)
                .and("propertyId", PacmanWorkContextHelper.getPropertyId())
                .and("startDate", startDate)
                .and("endDate", endDate);

        return getSeasonsFor(queryParameters, FETCH_RATE_QUALIFIED_DETAILS_FOR_RATE_QUALIFIED_WITHIN_DATE_RANGE);
    }

    public List<QualifiedRateSeason> fetchActiveSeasonsWithinDateRange(Integer rateQualifiedId, LocalDate startDate, LocalDate endDate) {
        QueryParameter queryParameters = QueryParameter.with("id", rateQualifiedId)
                .and("startDate", startDate.toDate())
                .and("endDate", endDate.toDate());

        return getSeasonsFor(queryParameters, FETCH_ACTIVE_RATE_QUALIFIED_DETAILS_WITHIN_DATE_RANGE);
    }

    public int fetchActiveSeasonsCountWithinDateRange(LocalDate startDate, LocalDate endDate) {
        Map<String, Object> queryParameters = QueryParameter.with("startDate", startDate.toDate())
                .and("endDate", endDate.toDate()).parameters();
        return crudService.findByNativeQuerySingleResult(FETCH_ACTIVE_RATE_QUALIFIED_COUNT_WITHIN_DATE_RANGE, queryParameters);
    }

    private List<QualifiedRateSeason> getSeasonsFor(QueryParameter queryParameters, String fetchRateQuery) {
        final List<QualifiedRateSeason> seasons = new ArrayList<>();
        final Map<String, QualifiedRateSeason> seasonStartDateEndDateMap = new HashMap<>();
        final Map<QualifiedRateSeason, Map<String, RoomClass>> seasonRoomClassMap = new HashMap<>();

        crudService.findByNativeQuery(fetchRateQuery,
                queryParameters.parameters(), row -> {
                    boolean isAccomTypeActive = isAccomTypeActive(row);
                    boolean isAccomTypeDisplayActive = isAccomTypeDisplayActive(row);
                    Integer rateQualifiedId = (Integer) row[5];
                    if (!isAccomTypeActive && ObjectUtils.equals(rateQualifiedId, null)) {
                        return null;
                    }
                    SeasonDetail localSeasonDetail = extractSeasonDetailFromDbValues(
                            row, isAccomTypeActive, rateQualifiedId, isAccomTypeDisplayActive);
                    QualifiedRateSeason season = extractSeasonFromDbValues(seasons,
                            seasonStartDateEndDateMap, row);

                    String roomClassName = (String) row[21];
                    Integer roomClassOrder = (Integer) row[22];

                    Map<String, RoomClass> roomClassMap = seasonRoomClassMap.get(season);
                    if (roomClassMap == null) {
                        addRoomClassToSeason(seasonRoomClassMap, localSeasonDetail, season, roomClassName, roomClassOrder);
                    } else {
                        updateRoomClassToSeason(seasonRoomClassMap, localSeasonDetail, season, roomClassName, roomClassOrder, roomClassMap);
                    }
                    return localSeasonDetail;
                });
        return seasons;
    }

    public List<MassRestrictionUploadDTO> fetchSeasonForRateQualifiedWithinDateRangeAndRateIds(List<Integer> rateQualifiedIds, Date startDate, Date endDate) {
        QueryParameter queryParameters = QueryParameter.with(IDS, rateQualifiedIds)
                .and(START_DATE, startDate)
                .and(TODAY_DATE, dateService.getCaughtUpLocalDate())
                .and(END_DATE, endDate);
        return getMassRestrictionDTOS(FETCH_SEASON_BETWEEN_DATE_RANGE, queryParameters);
    }

    public List<MassRestrictionUploadDTO> fetchSeasonForRateQualifiedWithinDateRangeAndRateIds(int limit, List<Integer> rateQualifiedIds, Date startDate, Date endDate) {
        QueryParameter queryParameters = QueryParameter.with(IDS, rateQualifiedIds)
                .and(START_DATE, startDate)
                .and(TODAY_DATE, dateService.getCaughtUpLocalDate())
                .and(END_DATE, endDate);
        return getMassRestrictionDTOS(FETCH_SEASON_BETWEEN_DATE_RANGE.replace("SELECT", " SELECT TOP " + limit), queryParameters);
    }

    public List<MassRestrictionUploadDTO> fetchAllSeasonForRateQualified() {
        QueryParameter queryParameters = QueryParameter.with(TODAY_DATE, dateService.getCaughtUpLocalDate());
        return getMassRestrictionDTOS(FETCH_ALL_FUTURE_SEASONS, queryParameters);
    }

    private List<MassRestrictionUploadDTO> getMassRestrictionDTOS(String query, QueryParameter queryParameters) {
        List<MassRestrictionUploadDTO> massRestrictionUploadDTOS = new ArrayList<>();
        crudService.findByNativeQuery(query, queryParameters.parameters(), row -> {
            MassRestrictionUploadDTO massRestrictionUploadDTO = mapMassRestrictionUploadDTO(row);
            massRestrictionUploadDTOS.add(massRestrictionUploadDTO);
            return massRestrictionUploadDTO;
        });
        return massRestrictionUploadDTOS;
    }

    public List<MassRestrictionUploadDTO> fetchAllSeasonForRateQualified(int limit) {
        QueryParameter queryParameters = QueryParameter.with(TODAY_DATE, dateService.getCaughtUpLocalDate());
        return getMassRestrictionDTOS(FETCH_ALL_FUTURE_SEASONS.replace("SELECT", " SELECT TOP " + limit), queryParameters);
    }

    private MassRestrictionUploadDTO mapMassRestrictionUploadDTO(Object[] row) {
        MassRestrictionUploadDTO massRestrictionUploadDTO = new MassRestrictionUploadDTO();
        massRestrictionUploadDTO.setRateQualifiedId((Integer) row[0]);
        massRestrictionUploadDTO.setRateCodeName((String) row[1]);
        massRestrictionUploadDTO.setRateHeaderStartDate((Date) row[2]);
        massRestrictionUploadDTO.setRateHeaderEndDate((Date) row[3]);
        massRestrictionUploadDTO.setAccomClassName((String) row[4]);
        massRestrictionUploadDTO.setAccomTypeCode((String) row[5]);
        massRestrictionUploadDTO.setSeasonStartDate((Date) row[6]);
        massRestrictionUploadDTO.setSeasonEndDate((Date) row[7]);
        massRestrictionUploadDTO.setSunday((BigDecimal) row[8]);
        massRestrictionUploadDTO.setMonday((BigDecimal) row[9]);
        massRestrictionUploadDTO.setTuesday((BigDecimal) row[10]);
        massRestrictionUploadDTO.setWednesday((BigDecimal) row[11]);
        massRestrictionUploadDTO.setThursday((BigDecimal) row[12]);
        massRestrictionUploadDTO.setFriday((BigDecimal) row[13]);
        massRestrictionUploadDTO.setSaturday((BigDecimal) row[14]);
        massRestrictionUploadDTO.setAccomClassCode((String) row[15]);
        massRestrictionUploadDTO.setManagedInG3((Integer) row[16]);
        massRestrictionUploadDTO.setAccomTypeId((Integer) row[17]);
        massRestrictionUploadDTO.setAccomClassId((Integer) row[18]);
        massRestrictionUploadDTO.setPropertyId((Integer) row[19]);
        massRestrictionUploadDTO.setYieldable((Integer) row[20]);
        massRestrictionUploadDTO.setRateQualifiedTypeId((Integer) row[21]);
        massRestrictionUploadDTO.setRateCodeDescription((String) row[22]);
        massRestrictionUploadDTO.setAccomTypeName((String) row[23]);
        massRestrictionUploadDTO.setRankOrder((Integer) row[24]);
        return massRestrictionUploadDTO;
    }

    public void updateRoomClassToSeason(Map<QualifiedRateSeason, Map<String, RoomClass>> seasonRoomClassMap,
                                 SeasonDetail localSeasonDetail, QualifiedRateSeason season, String roomClassName, Integer roomClassOrder,
                                 Map<String, RoomClass> roomClassMap) {
        RoomClass roomClass;
        roomClass = findOrBuildRoomClass(roomClassName, roomClassOrder, roomClassMap);
        addOrReplaceRoomClassToSeason(localSeasonDetail, season, roomClass);
        roomClassMap.put(roomClassName, roomClass);
        seasonRoomClassMap.put(season, roomClassMap);
    }

    public RoomClass findOrBuildRoomClass(String roomClassName,
                                   Integer roomClassOrder, Map<String, RoomClass> roomClassMap) {
        RoomClass roomClass;
        roomClass = roomClassMap.get(roomClassName);
        if (roomClass == null) {
            roomClass = buildNewRoomClass(roomClassName, roomClassOrder);
        }
        return roomClass;
    }

    public void addOrReplaceRoomClassToSeason(SeasonDetail seasonDetail,
                                       QualifiedRateSeason season, RoomClass roomClass) {
        int index = season.getRoomClasses().indexOf(roomClass);
        roomClass.addSeasonDetails(seasonDetail);
        if (index != -1) {
            season.getRoomClasses().set(index, roomClass);
        } else {
            season.addRoomClasses(roomClass);
        }
    }

    void addRoomClassToSeason(Map<QualifiedRateSeason, Map<String, RoomClass>> seasonRoomClassMap,
                              SeasonDetail localSeasonDetail, QualifiedRateSeason season, String roomClassName, Integer roomClassOrder) {
        RoomClass roomClass;
        Map<String, RoomClass> roomClassMap;
        roomClassMap = new HashMap<>();
        roomClass = buildNewRoomClass(roomClassName, roomClassOrder);
        roomClass.addSeasonDetails(localSeasonDetail);
        roomClassMap.put(roomClassName, roomClass);
        seasonRoomClassMap.put(season, roomClassMap);
        season.addRoomClasses(roomClass);
    }

    public RoomClass buildNewRoomClass(String roomClassName,
                                Integer roomClassOrder) {
        RoomClass roomClass = new RoomClass();
        roomClass.setRoomClassName(roomClassName);
        roomClass.setRoomClassOrder(roomClassOrder);
        return roomClass;
    }

    public void extractRoomClassFromDbValues(Object[] row, SeasonDetail localSeasonDetail,
                                      Map<String, RoomClass> roomClassDetailsMap, QualifiedRateSeason season) {
        String roomClassName = (String) row[21];
        Integer roomClassOrder = (Integer) row[22];
        RoomClass roomClass = roomClassDetailsMap.get(roomClassName);
        if (roomClass == null) {
            roomClass = buildNewRoomClass(roomClassName, roomClassOrder);
            season.addRoomClasses(roomClass);
            roomClassDetailsMap.put(roomClassName, roomClass);
        }
        roomClass.addSeasonDetails(localSeasonDetail);
    }

    public QualifiedRateSeason extractSeasonFromDbValues(List<QualifiedRateSeason> seasons,
                                                  Map<String, QualifiedRateSeason> seasonStartDateEndDateMap,
                                                  Object[] row) {
        Date startDate = (Date) row[2];
        Date endDate = (Date) row[3];
        String startDateEndDateKey = formStartDateEndDateKey(startDate, endDate);
        QualifiedRateSeason season = seasonStartDateEndDateMap.get(startDateEndDateKey);
        if (null == season) {
            season = new QualifiedRateSeason();
            seasons.add(season);
            season.setStartDate(LocalDateUtils.fromDate(startDate));
            season.setEndDate(LocalDateUtils.fromDate(endDate));
            seasonStartDateEndDateMap.put(startDateEndDateKey, season);
        }
        return season;
    }


    public SeasonDetail extractSeasonDetailFromDbValues(Object[] row, boolean isAccomTypeActive, Integer rateQualifiedId, boolean isAccomTypeDisplayActive) {
        SeasonDetail localSeasonDetail = new SeasonDetail();
        localSeasonDetail.setAccomTypeActive(isAccomTypeActive);
        localSeasonDetail.setAccomTypeDisplayActive(isAccomTypeDisplayActive);
        localSeasonDetail.setAccomTypeId((Integer) row[0]);
        localSeasonDetail.setAccomTypeName((String) row[1]);
        if (ObjectUtils.notEqual(rateQualifiedId, null)) {
            localSeasonDetail.setId((Integer) row[4]);
            localSeasonDetail.setSunday(applyDecimalFormatToBigDecimal((BigDecimal) row[9]));
            localSeasonDetail.setMonday(applyDecimalFormatToBigDecimal((BigDecimal) row[10]));
            localSeasonDetail.setTuesday(applyDecimalFormatToBigDecimal((BigDecimal) row[11]));
            localSeasonDetail.setWednesday(applyDecimalFormatToBigDecimal((BigDecimal) row[12]));
            localSeasonDetail.setThursday(applyDecimalFormatToBigDecimal((BigDecimal) row[13]));
            localSeasonDetail.setFriday(applyDecimalFormatToBigDecimal((BigDecimal) row[14]));
            localSeasonDetail.setSaturday(applyDecimalFormatToBigDecimal((BigDecimal) row[15]));
        }
        return localSeasonDetail;
    }


    public boolean isAccomTypeActive(Object[] row) {
        return Constants.ACTIVE_STATUS_ID == row[20];
    }

    private boolean isAccomTypeDisplayActive(Object[] row) {
        return Constants.ACTIVE_STATUS_ID == row[23];
    }

    private BigDecimal applyDecimalFormatToBigDecimal(BigDecimal bd) {
        DecimalFormat df = new DecimalFormat(".###");
        String str = df.format(bd);
        return new BigDecimal(str);
    }

    private String formStartDateEndDateKey(Date startDate, Date endDate) {
        return String.format("%s&%s", startDate.toString(), endDate.toString());
    }

    public void validateRateHeaderDatesWithSeasonDatesConsideringExternalSystem(RateHeader rateHeader) {
        if (isExternalSystemNGI()) {
            return;
        }
        validateRateHeaderDatesWithSeasonDates(rateHeader);
    }

    public void validateRateHeaderDatesWithSeasonDates(RateHeader rateHeader) {
        Object[] dates = getSeasonLatestEndDateEarlierStartDateForRateHeader(rateHeader);
        if (dates != null && dates.length > 0) {
            if (dates[0] != null && dates[1] != null) {
                Date latestEndDate = (Date) dates[0];
                Date earlierStartDate = (Date) dates[1];
                if (rateHeader.getStartDate().toDate().after(earlierStartDate) || rateHeader.getEndDate().toDate().before(latestEndDate)) {
                    throw new TetrisException(ErrorCode.INVALID_INPUT, "The Rate Header start date must be earlier than its earliest season's start date and the Rate Header end date must be later than its latest season's end date.");
                }
            }
        }
    }

    public Object[] getSeasonLatestEndDateEarlierStartDateForRateHeader(
            RateHeader rateHeader) {
        return (Object[]) crudService.findByNamedQuerySingleResult(
                RateQualifiedDetails.LATEST_END_DATE_EARLIER_START_DATE_BY_RATE_QUALIFIED_ID,
                QueryParameter.with("rateQualifiedId", rateHeader.getId()).parameters());
    }

    public void validateSeasonEndDate(QualifiedRateSeason season) {
        Date businessDate = dateService.getBusinessDate();
        Date seasonEndDate = season.getEndDate().toDate();
        if (seasonEndDate.before(businessDate)) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "Season end date can not be before system date");
        }
    }

    public void deleteSeasonDetail(SeasonDetail seasonDetail) {
        crudService.delete(RateQualifiedDetails.class, seasonDetail.getId());
//        crudService.flush();
    }

    public QualifiedRateSeason fetchEmptySeason() {
        QualifiedRateSeason season = new QualifiedRateSeason();
        List<RoomClass> roomClasses = fetchAllActiveNonEmptyRoomClasses();
        for (RoomClass roomClass : roomClasses) {
            List<SeasonDetail> seasonDetails = crudService.findByNamedQuery(AccomType.BY_ACCOM_CLASS_PROPERTY_ID_TO_SEASON_DETAIL, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                    .and("accomClassId", roomClass.getId()).parameters());
            roomClass.setSeasonDetails(seasonDetails);
        }
        season.setRoomClasses(roomClasses);
        return season;
    }

    public List<RoomClass> fetchAllActiveNonEmptyRoomClasses() {
        return crudService.findByNamedQuery(AccomClass.ALL_ACTIVE_NON_DEFAULT_TO_ROOM_CLASS,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public void deleteRateHeader(RateHeader rateHeader) {
        RateQualified rateQualified = crudService.find(RateQualified.class, rateHeader.getId());
        rateQualified.setStatusId(Constants.INACTIVE_STATUS_ID);
        crudService.save(rateQualified);
        deleteLimitTotalRateQualified(rateHeader);

    }

    private void deleteSeason(QualifiedRateSeason season) {
        for (RoomClass roomClass : season.getRoomClasses()) {
            for (SeasonDetail seasonDetail : roomClass.getSeasonDetails()) {
                if (seasonDetail.getId() != null) {
                    deleteSeasonDetail(seasonDetail);
                }
            }
        }
    }

    public boolean isSplitRequired(QualifiedRateSeason seasonToSplit) {
        LocalDate caughtUpDate = loadCaughtupDate();
        return !(caughtUpDate.isAfter(seasonToSplit.getEndDate()) || caughtUpDate.compareTo(seasonToSplit.getStartDate()) <= 0);
    }

    public boolean willSplitOccur(List<QualifiedRateSeason> seasons, QualifiedRateSeason changedSeason) {
        return seasonService.willSplitOccur(seasons, changedSeason);
    }

    @SuppressWarnings("unchecked")
    public List<QualifiedRateSeason> applySplitForAddSeason(List<QualifiedRateSeason> existingSeasons, QualifiedRateSeason seasonToAdd) {
        LocalDate caughtUpDate = loadCaughtupDate();
        return seasonService.applySplitForAddSeasonWithMarkedForDeletion(existingSeasons, seasonToAdd, caughtUpDate, seasonToAdd);
    }

    public List<QualifiedRateSeason> applySplitForDeleteSeason(QualifiedRateSeason seasonToRemove) {

        LocalDate caughtUpDate = loadCaughtupDate();
        if (xyz(seasonToRemove, caughtUpDate)) {
            return Collections.emptyList();
        }
        List<QualifiedRateSeason> seasons = new ArrayList<>();
        seasons.add(seasonToRemove);
        if (caughtUpDate.compareTo(seasonToRemove.getStartDate()) <= 0) {
            seasonToRemove.setDeleted(true);
            return seasons;
        }
        return seasonService.applySplitForDeleteSeasonWithMarkedForDeletion(seasons, seasonToRemove, caughtUpDate, seasonToRemove);
    }

    private boolean xyz(QualifiedRateSeason seasonToRemove, LocalDate caughtUpDate) {
        return caughtUpDate.isAfter(seasonToRemove.getEndDate());
    }

    public LocalDate loadCaughtupDate() {
        return LocalDateUtils.fromDate(dateService.getCaughtUpDate());
    }


    public List<QualifiedRateSeason> applySplitForUpdateSeason(QualifiedRateSeason seasonToUpdate, Integer rateQualifiedId) {
        LocalDate caughtUpDate = loadCaughtupDate();
        if (caughtUpDate.isAfter(seasonToUpdate.getEndDate())) {
            return Collections.emptyList();
        }
        List<QualifiedRateSeason> seasons = fetchSeasonForRateQualifiedWithinDateRange(rateQualifiedId, seasonToUpdate.getStartDate(), seasonToUpdate.getEndDate());
        List<QualifiedRateSeason> qualifiedRateSeasonsAfterSpilt = seasonService.applySplitForUpdateSeasonWithMarkedForDeletion(seasons, seasonToUpdate, caughtUpDate, seasons.get(0));
        for (QualifiedRateSeason qualifiedRateSeason : qualifiedRateSeasonsAfterSpilt) {
            clearValuesForOutOfRangeDOWs(qualifiedRateSeason);
        }
        return qualifiedRateSeasonsAfterSpilt;
    }

    private void clearValuesForOutOfRangeDOWs(QualifiedRateSeason qualifiedRateSeason) {
        List<RoomClass> roomClasses = isEmpty(qualifiedRateSeason.getRoomClasses()) ? Collections.emptyList() : qualifiedRateSeason.getRoomClasses();
        for (RoomClass roomClass : roomClasses) {
            List<SeasonDetail> seasonDetails = roomClass.getSeasonDetails();
            for (SeasonDetail seasonDetail : seasonDetails) {
                clearOutOfRangeDayOfWeekValues(seasonDetail, qualifiedRateSeason.getStartDate(), qualifiedRateSeason.getEndDate());
            }
        }
    }

    public List<AbstractRate> saveFromMap(List<? extends Map<String, Object>> qualifiedRatesMap) {
        final String currency = configParamsUtil.getCurrency();

        // temporary fix to set the required type so rates from NGI will save correctly
        qualifiedRatesMap.forEach(rate -> rate.put(CURRENCY, currency));
        List<AbstractRate> result = rateConverter.convert(qualifiedRatesMap);
        if (isHiltonYieldAsRefinementEnabled()) {
            semiYieldableRateConverter.saveOriginalSemiYieldableRates(qualifiedRatesMap, result);
        }
        return result;
    }

    public boolean isHiltonYieldAsRefinementEnabled() {
        return configService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_YIELD_AS_REFINEMENT_ENABLED);

    }

    public void deleteFutureSeasons(RateHeader rateHeader) {
        rateHeader.setSeasons(fetchSeasonsForRateHeader(rateHeader));
        if (isNotEmpty(rateHeader.getSeasons())) {
            for (QualifiedRateSeason season : rateHeader.getSeasons()) {
                determineAndMarkSeasonForDeletion(season);
            }
        }
    }

    public void determineAndMarkSeasonForDeletion(QualifiedRateSeason season) {
        if (isSplitRequired(season)) {
            season.setEndDate(loadCaughtupDate().minusDays(1));
            clearValuesForOutOfRangeDOWs(season);
        } else if (!isPastSeason(season)) {
            season.setDeleted(true);
        }
    }

    @ForTesting //To Be Used only for Vaadin UI Tests
    public void addPastRateForVaadinTestBench(int propertyId) {
        LocalDate caughtUpDate = new LocalDate(dateService.getCaughtUpDate());
        String insertPastRatePlan = ""
                + "INSERT INTO Rate_Qualified ([file_metadata_id], [property_id], [rate_code_name], [rate_code_description], [start_date_dt], [end_date_dt], [status_id], [managed_in_g3]) VALUES      (1, "
                + propertyId + " , 'PastRate', 'Used only for Vaadin UI Test', '" + caughtUpDate.minusDays(15) + "' , '" + caughtUpDate.minusDays(2) + "', 1 , 0)";
        String insertPastRateDetails = ""
                + "INSERT INTO Rate_Qualified_Details ([Rate_Qualified_ID] ,[Accom_Type_ID] ,[Start_Date_DT] ,[End_Date_DT] ,"
                + "[Sunday] ,[Monday] ,[Tuesday] ,[Wednesday] ,[Thursday] ,[Friday] ,[Saturday] ,"
                + "[Created_By_User_ID] ,[Last_Updated_By_User_ID])  "
                + "VALUES ((select Rate_Qualified_ID from Rate_Qualified where rate_code_name ='PastRate' and Rate_Code_Description='Used only for Vaadin UI Test' and Status_ID=1) "
                + "           ,4 ,'" + caughtUpDate.minusDays(15) + "' , '" + caughtUpDate.minusDays(2) + "' ,11 ,22 ,33 ,44 ,55 ,66 ,77 ,11403 ,11403 )";
        crudService.executeUpdateByNativeQuery(insertPastRatePlan);
        crudService.executeUpdateByNativeQuery(insertPastRateDetails);
    }

    @ForTesting //To Be Used only for Vaadin UI Tests.
    public void deletePastRateForVaadinTestBench(@QueryParam("propertyId") int propertyId) {
        String deleteRateDetails = "DELETE from Rate_Qualified_Details where Rate_Qualified_ID = (select Rate_Qualified_ID from Rate_Qualified where rate_code_name ='PastRate' and Property_ID = " + propertyId + ")";
        String deleteRateHeader = "DELETE from Rate_Qualified where rate_code_name ='PastRate' and Property_ID = " + propertyId;
        crudService.executeUpdateByNativeQuery(deleteRateDetails);
        crudService.executeUpdateByNativeQuery(deleteRateHeader);
    }

    public List<RateQualified> fetchG3ManagedRates() {
        return crudService.findByNamedQuery(RateQualified.DERIVED_AND_NON_YIELDABLE_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("derivedRateTypes", Arrays.asList(QUALIFIED_RATE_PLAN_VALUE_TYPE, QUALIFIED_RATE_PLAN_PERCENTAGE_TYPE)).parameters());
    }

    public List<RateQualified> fetchDerivedActiveAndYieldableRates() {
        return crudService.findByNamedQuery(RateQualified.DERIVED_ACTIVE_AND_YIELDABLE_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public List<RateQualified> fetchAllRatesByPropertyId() {
        return crudService
                .findByNamedQuery(RateQualified.ALL_BY_PROPERTY_ID,
                        QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public int updateToNotManagedInG3(Integer propertyId) {
        return multiPropertyCrudService.executeNamedUpdateOnSingleProperty(propertyId, RateQualified.UPDATE_MANAGED_IN_G3_TO_FALSE, MapUtils.EMPTY_MAP);
    }

    public boolean isManagedByG3() {
        return isExternalSystemNGI() || isDerivedTypeEnabled();
    }

    private boolean isExternalSystemNGI() {
        return externalSystemHelper.isNGI();
    }

    public List<RateQualifiedFamily> fetchActiveRateQualifiedAsFamily(List<? extends Integer> rateQualifiedIds){
        return  com.ideas.tetris.pacman.util.CollectionUtils.chunk(rateQualifiedIds, PAGE_SIZE)
                .stream()
                .map(chunkIds -> Optional.ofNullable(crudService.findByNativeQuery(RateQualified.GET_ACTIVE_RATE_QUALIFIED_FAMILY,
                                Map.of("rateQualifiedIds", chunkIds), RateQualifiedService::getRateQualifiedFamily))
                        .orElse(List.of())).flatMap(List::stream).collect(toList());
    }

    private static RateQualifiedFamily getRateQualifiedFamily(Object[] row) {
        RateQualifiedFamily rateQualifiedFamily = new RateQualifiedFamily();
        rateQualifiedFamily.setName((String) row[0]);
        rateQualifiedFamily.setId((Integer) row[1]);
        rateQualifiedFamily.setFamilyTree((String) row[2]);
        return rateQualifiedFamily;
    }

    public Map<String, RateQualifiedEntityDto> getActiveRateQualifiedWithDetails(Date caughtUpDate, Date optimizationEndDate) {
        Map<String, RateQualifiedEntityDto> rateQualifiedWithDetailsMap = new HashMap<>();
        crudService.findByNativeQuery(RateQualified.GET_ACTIVE_RATE_QUALIFIED_WITH_DETAILS_FOR_RATE_CALC,
                QueryParameter.with(STATUS_ID, ACTIVE_STATUS_ID).and("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and(CAUGHT_UP_DATE, caughtUpDate)
                        .and(OPTIMIZATION_END_DATE, optimizationEndDate)
                        .parameters(), row -> mapResultToRateQualifiedWithDetails(row, rateQualifiedWithDetailsMap, caughtUpDate, optimizationEndDate));
        return rateQualifiedWithDetailsMap;
    }

    public Map<String, RateQualifiedEntityDto> getActiveRateQualifiedWithDetailsByRateNames(Date caughtUpDate, Date optimizationEndDate,List<String> rateCodeNames) {
        Map<String, RateQualifiedEntityDto> rateQualifiedWithDetailsMap = new HashMap<>();
        crudService.findByNativeQuery(RateQualified.GET_ACTIVE_RATE_QUALIFIED_WITH_DETAILS_FOR_RATE_CALC_BY_RATE_NAME,
                QueryParameter.with(STATUS_ID, ACTIVE_STATUS_ID).and("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and(CAUGHT_UP_DATE, caughtUpDate)
                        .and(OPTIMIZATION_END_DATE, optimizationEndDate)
                        .and("rateCodeNames", rateCodeNames)
                        .parameters(), row -> mapResultToRateQualifiedWithDetails(row, rateQualifiedWithDetailsMap, caughtUpDate, optimizationEndDate));
        return rateQualifiedWithDetailsMap;
    }



    public RateQualifiedEntityDto getLv0RateQualifiedWithDetails() {
        Date caughtUpDate = dateService.getOptimizationWindowStartDate();
        Date optimizationEndDate = dateService.getOptimizationWindowEndDateBDE();
        Map<String, RateQualifiedEntityDto> rateQualifiedWithDetailsMap = new HashMap<>();
        crudService.findByNativeQuery(RateQualified.GET_LV0_QUALIFIED_WITH_DETAILS_FOR_RATE_CALC,
                QueryParameter.with(STATUS_ID, ACTIVE_STATUS_ID).and("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and(CAUGHT_UP_DATE, caughtUpDate)
                        .and(OPTIMIZATION_END_DATE, optimizationEndDate)
                        .parameters(), row -> mapResultToRateQualifiedWithDetails(row, rateQualifiedWithDetailsMap, caughtUpDate, optimizationEndDate));
        return rateQualifiedWithDetailsMap.get("LV0");
    }

    private String mapResultToRateQualifiedWithDetails(Object[] row, Map<String, RateQualifiedEntityDto> rateQualifiedWithDetailsMap, Date caughtUpDate, Date optimizationEndDate) {
        String rateCodeName = (String) row[1];
        Integer id = (Integer) row[0];
        rateQualifiedWithDetailsMap.computeIfAbsent(rateCodeName, value -> RateQualifiedEntityDto.builder()
                        .id(id)
                        .name(rateCodeName)
                        .referenceRateCode((String) row[2])
                        .rateQualifiedTypeId((Integer) row[3])
                        .startDate((Date) row[4])
                        .endDate((Date) row[5])
                        .description((String) row[6])
                        .remarks((String) row[7])
                        .currency((String) row[8])
                        .yieldable((Integer) row[9])
                        .priceRelative((Integer) row[10])
                        .includesPackage((Integer) row[11])
                        .statusId((Integer) row[12])
                        .managedInG3((Integer) row[13])
                        .fileMetadataId((Integer) row[14])
                        .propertyId((Integer) row[15])
                        .build())

                .addToDetails(RateQualifiedDetailsEntityDto.builder().rateQualifiedId(id)
                        .startDate(getStartDateWRTCaughtUpDate(row, caughtUpDate))
                        .endDate(getEndDateWRTOptimisationWindow(row, optimizationEndDate))
                        .accomTypeId((Integer) row[18])
                        .sunday((BigDecimal) row[19])
                        .monday((BigDecimal) row[20])
                        .tuesday((BigDecimal) row[21])
                        .wednesday((BigDecimal) row[22])
                        .thursday((BigDecimal) row[23])
                        .friday((BigDecimal) row[24])
                        .saturday((BigDecimal) row[25])
                        .build());
        return rateCodeName;
    }

    private Date getEndDateWRTOptimisationWindow(Object[] row, Date optimizationEndDate) {
        Date endDate = (Date) row[17];
        return endDate.after(optimizationEndDate) ? optimizationEndDate : endDate;
    }

    private Date getStartDateWRTCaughtUpDate(Object[] row, Date caughtUpDate) {
        Date startDate = (Date) row[16];
        return startDate.before(caughtUpDate) ? caughtUpDate : startDate;
    }

    public JobView getMassRestrictionUploadLatestJobStatus() {
        JobViewCriteria criteria = new JobViewCriteria();
        criteria.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        criteria.setJobNames(Arrays.asList(JobName.MassRestrictionUploadJob.name()));
        criteria.setSortPropertyIds(new String[]{"startDate"});
        List<String> sortableList = new ArrayList();
        sortableList.add("startDate");
        criteria.setSortablePropertyIds((ArrayList<String>) sortableList);
        criteria.setSortPropertyAscendingStates(new boolean[]{false});
        List<JobView> jobs = jobMonitorService.getJobs(criteria);
        if (CollectionUtils.isEmpty(jobs)) {
            return new JobView();
        }
        return jobs.get(0);
    }

    public Integer getYieldableQualifiedRatesCount() {
        return crudService.findByNativeQuerySingleResult(YIELDABLE_RATES_COUNT_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public List<Integer> getAllRateQualifiedIds() {
        return crudService.findByNamedQuery(RateQualified.ALL_ACTIVE_IDS);
    }

    public Set<Integer> getAllYieldableActiveRateQualifiedIds() {
        List<Integer> yieldableRateIds = crudService.findByNamedQuery(RateQualified.ALL_YIELDABLE_AND_ACTIVE_IDS);
        return new HashSet<>(yieldableRateIds);
    }

    public List<Integer> getRateQualifiedIds() {
        return crudService.findByNamedQuery(RateQualified.ALL_IDS);
    }

    public List<RateQualified> getAllActiveRateQualifieds() {
        return crudService.findByNamedQuery(RateQualified.ALL_ACTIVE);
    }

    public void notifyDerivedStatusChange(RateHeader rateHeader) {
        if (!isNGI() || rateHeader == null || rateHeader.getRateType() == null) {
            // Don't notify if not an NGI property or the rate information is indeterminant
            return;
        }
        Boolean isDerived = isDerived(rateHeader);

        // Update status in nucleus
        final Map<String, Object> parameters = parameters();
        clientCodePropertyCodeMappingService.updateClientCodePropertyCodeBeforeCallingNgi(parameters);
        rateClientAPIService.updateQualified((String) parameters.get(CLIENT_CODE), (String) parameters.get(PROPERTY_CODE), rateHeader.getRatePlanName(), createRatePlanDto(isDerived));

        requestRates(rateHeader, isDerived);
    }

    private boolean isDerived(RateHeader rateHeader) {
        return rateHeader.getManagedInG3() || rateHeader.getNonYieldable() || !QUALIFIED_RATE_PLAN_FIXED_TYPE.equals(rateHeader.getRateType());
    }

    private boolean isNGI() {
        return externalSystemHelper.isNGI(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode());
    }

    private boolean isOXI() {
        return externalSystemHelper.isOXI(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode());
    }

    private boolean isDerivedStatusNew(RateQualified rateQualified) {
        return rateQualified == null || rateQualified.getRateQualifiedTypeId() == null;
    }

    private boolean isDerivedStatusChanged(RateHeader rateHeader, RateQualified rateQualified) {
        return QUALIFIED_RATE_PLAN_FIXED_TYPE.equals(rateHeader.getRateType()) &&
                !QUALIFIED_RATE_PLAN_FIXED_TYPE.equals(rateQualified.getRateQualifiedTypeId()) ||
                !QUALIFIED_RATE_PLAN_FIXED_TYPE.equals(rateHeader.getRateType()) &&
                        QUALIFIED_RATE_PLAN_FIXED_TYPE.equals(rateQualified.getRateQualifiedTypeId());
    }

    private void requestRates(RateHeader rateHeader, boolean isDerived) {
        if (isOXI() && !isDerived) {
            // Request rates
            try {
                restClient.put(
                        QUALIFIED_RATE_DERIVED_STATUS_RATE_REQUEST,
                        Entity.entity("", MediaType.APPLICATION_JSON_TYPE),
                        RestClient.OrderedNamedParams.add(1, "clientCode", PacmanWorkContextHelper.getClientCode())
                                .and(2, "propertyCode", PacmanWorkContextHelper.getPropertyCode())
                                .and(3, "name", rateHeader.getRatePlanName())
                );
            } catch (Exception e) {
                LOGGER.warn("Unhandled exception in external system requesting rates for derived status change.", e);
            }
        }
    }

    public void deactivateAllDerivedRates() {
        crudService.executeUpdateByNativeQuery(DEACTIVATE_ALL_DERIVED_RATES);
    }

    private RatePlanDto createRatePlanDto(boolean isDerived) {
        RatePlanDto dto = new RatePlanDto();
        dto.setDerived(isDerived);

        return dto;
    }

    private boolean isRateHeaderDerived(RateHeader header) {
        return !QUALIFIED_RATE_PLAN_FIXED_TYPE.equals(header.getRateType());
    }

    private Map<String, Object> parameters() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("clientCode", PacmanWorkContextHelper.getClientCode());
        parameters.put("propertyCode", PacmanWorkContextHelper.getPropertyCode());
        return parameters;
    }

    public List<RateQualified> getRateQualifiedsByIds(List<? extends Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return crudService.findByNamedQuery(RateQualified.BY_IDS, QueryParameter.with("ids", ids).parameters());
    }

    private Map<Integer, List<RateQualifiedDetails>> getRateQualifiedDetailsByIds(List<? extends Integer> ids, java.time.LocalDate caughtDate) {
        List<RateQualifiedDetails> rateDetailsOnly = crudService.findByNamedQuery(RateQualifiedDetails.BY_RATE_QUALIFIED_IDS_AND_END_DATE,
                QueryParameter.with("rateQualifiedIds", ids).and("date", LocalDateUtils.toDate(caughtDate)).parameters());
        return rateDetailsOnly.stream().collect(Collectors.groupingBy(RateQualifiedDetails::getRateQualifiedId));
    }


    public Map<Integer, List<RateQualifiedDetails>> getRateQualifiedDetailsByIdsChunked(Collection<? extends Integer> ids, java.time.LocalDate caughtUpDate) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        return com.ideas.tetris.pacman.util.CollectionUtils.chunk(ids, PAGE_SIZE)
                .stream()
                .map(chunkIds -> getRateQualifiedDetailsByIds(chunkIds, caughtUpDate)).collect(HashMap::new, Map::putAll, Map::putAll);
    }

    public void truncateFixedDetails() {
        rateQualifiedFixedRepository.truncateRateQualifiedFixedDetails();
    }

    public List<RateQualified> getRateQualifiedByNames(Collection<String> extraRateCodes) {
        if (extraRateCodes.isEmpty()) {
            return List.of();
        }
        return crudService.findByNamedQuery(RateQualified.GET_ALL_ACTIVE_RATE_QUALIFIED_BY_NAMES,
                QueryParameter.with("names", extraRateCodes).parameters());
    }

    public Map<Integer, RateQualified> getRateQualifiedById(Collection<String> extraRateCodes) {
        var rateQualifieds = getRateQualifiedByNames(extraRateCodes);
        return rateQualifieds.stream().collect(Collectors.toMap(RateQualified::getId, r -> r));
    }

    public Map<Integer, RateQualified> getRateQualifiedById() {
        List<RateQualified> rateQualifieds = crudService.findByNamedQuery(RateQualified.ALL_ACTIVE);
        return rateQualifieds.stream().collect(Collectors.toMap(RateQualified::getId, r -> r));
    }

    public List<String> getActiveRateQualifiedRatesById(List<? extends Integer> rateQualifiedIds) {
        if(rateQualifiedIds.isEmpty()) {
            return Collections.emptyList();
        }
        return crudService.findByNamedQuery(RateQualified.ALL_ACTIVE_RATES_BY_ID, QueryParameter.with(RATE_QUALIFIED_IDS, rateQualifiedIds).parameters());
    }

    public HashMap<Integer, byte[]> loadRateDetailsV2Map(Date startDate, Date endDate, List<RateQualified> rateQualifieds) {
        var map = new HashMap<Integer, byte[]>();
        var rateIds = rateQualifieds.stream()
                .map(RateQualified::getId)
                .collect(toList());

        log.info("Number of rateIds: {}, with batch size {}", rateIds.size(), configService.getIntegerParameterValue(PreProductionConfigParamName.RATE_ID_SIZE_TO_LOAD_FOR_RATE_DETAILS.value()));

        for (var rateIdChunks : splitList(rateIds, configService.getIntegerParameterValue(PreProductionConfigParamName.RATE_ID_SIZE_TO_LOAD_FOR_RATE_DETAILS.value()))) {
            log.debug("Loading rate details into compressed map for rateIds: {}", rateIdChunks);
            var details = getRateQualifiedFixedDetailsByRateId(startDate, endDate, rateIdChunks);
            var groupDetails = details.stream().collect(Collectors.groupingBy(RateQualifiedFixedDetails::getRateQualifiedId));

            for (var groupedEntry : groupDetails.entrySet()) {
                try {
                    map.put(Math.toIntExact(groupedEntry.getKey()), GzipCompressionHelper.compressValue(new RateDetailsCompressedV2(groupedEntry.getValue().stream().map(this::rateDetail).collect(Collectors.toList())), RecommendationCommonConstants.objectMapper()));
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        return map;
    }

    public RateDetails.RateDetail rateDetail(RateQualifiedFixedDetails other) {
        if (other == null) {
            return null;
        }
        RateDetails.RateDetail rateDetail = new RateDetails.RateDetail();
        rateDetail.setRateQualifiedID(other.getRateQualifiedId());
        rateDetail.setAccomTypeID(other.getAccomTypeId());
        rateDetail.setStartDateDT(convertToString(other.getStartDate()));
        rateDetail.setEndDateDT(convertToString(other.getEndDate()));
        rateDetail.setSunday(other.getSunday());
        rateDetail.setMonday(other.getMonday());
        rateDetail.setTuesday(other.getTuesday());
        rateDetail.setWednesday(other.getWednesday());
        rateDetail.setThursday(other.getThursday());
        rateDetail.setFriday(other.getFriday());
        rateDetail.setSaturday(other.getSaturday());
        return rateDetail;
    }

    public String convertToString(java.util.Date dateToConvert) {
        var localDate = convertToLocalDateViaInstant(dateToConvert);
        return convertToString(localDate);
    }

    public String convertToString(java.time.LocalDate localDate) {
        return localDate == null ? null : localDate.toString();
    }

    public java.time.LocalDate convertToLocalDateViaInstant(java.util.Date dateToConvert) {
        if (dateToConvert == null) {
            return null;
        }
        try {
            var instant = dateToConvert.toInstant();
            return instant.atZone(ZoneId.systemDefault()).toLocalDate();
        } catch (UnsupportedOperationException e) {
            return ((java.sql.Date) dateToConvert).toLocalDate();
        }
    }

    @Data
    @NoArgsConstructor
    public static class RateDetailsCompressedV2 {
        private List<RateDetails.RateDetail> value;

        public RateDetailsCompressedV2(List<RateDetails.RateDetail> value) {
            this.value = value;
        }
    }

    public static List<List<Integer>> splitList(List<Integer> list, int chunkSize) {
        List<List<Integer>> result = new ArrayList<>();

        // Calculate the number of chunks
        int numberOfChunks = (int) Math.ceil((double) list.size() / chunkSize);

        // Split the list into chunks
        for (int i = 0; i < numberOfChunks; i++) {
            int fromIndex = i * chunkSize;
            int toIndex = Math.min((i + 1) * chunkSize, list.size());
            result.add(new ArrayList<>(list.subList(fromIndex, toIndex)));
        }

        return result;
    }
    @ForTesting
    public void createProductRateCode(Integer propertyId) {
        List<RateQualified> rateQualifiedList = getRateQualifiedList();
        createProductRateCode(rateQualifiedList, propertyId);
    }
    public void createProductRateCode(List<RateQualified> rateQualifiedList, Integer propertyId) {
        List<Integer> rateQualifiedIds = rateQualifiedList.stream().map(RateQualified::getId).collect(Collectors.toList());
        List<RateQualifiedFamily> rateQualifiedFamilyList = fetchActiveRateQualifiedAsFamily(rateQualifiedIds);
        Map<Integer, RateQualifiedFamily> rateQualifiedFamilyMap = rateQualifiedFamilyList.stream()
                .collect(Collectors.toMap(RateQualifiedFamily::getId, Function.identity()));
        List<Product> productList = agileRatesConfigurationService.findAgileRatesProducts();
        processRateQualifiedList(rateQualifiedList, propertyId, rateQualifiedFamilyMap, productList);
    }
    private void processRateQualifiedList(List<RateQualified> rateQualifiedList, Integer propertyId, Map<Integer, RateQualifiedFamily> rateQualifiedFamilyMap, List<Product> productList) {
        List<ProductRateCode> productRateCodes = crudService.findAll(ProductRateCode.class);
        rateQualifiedList.forEach(rateQualified -> {
            String rateCode = rateQualified.getName();
            Product product = productList.stream().filter(p -> p.getName().equalsIgnoreCase(rateCode) || p.getName().equalsIgnoreCase(rateCode.substring(rateCode.indexOf(UNDERSCORE) + 1))).findFirst().orElse(null);
            if (null != product) {
                saveProductRateCode(rateCode, product, productRateCodes);
            } else {
                RateQualifiedFamily rateQualifiedFamily = rateQualifiedFamilyMap.get(rateQualified.getId());
                if(rateQualifiedFamily != null) {
                    String[] hierarchy = rateQualifiedFamily.getFamilyTree().split(",");
                    Product parentProduct = getParentProduct(productList, hierarchy);
                    if (parentProduct != null) {
                        saveProductRateCode(parentProduct.getName(), parentProduct, productRateCodes);
                        Date caughtUpDate = dateService.getCaughtUpDate();
                        int index = getIndexOfParentProduct(Arrays.asList(hierarchy), parentProduct.getName());
                        String currentRateCode;
                        boolean brokenHierarchy = false;
                        for (int i = index+1; i < hierarchy.length; i++) {
                            currentRateCode = hierarchy[i];
                            RateQualified rate = findSingleRateByName(currentRateCode, propertyId);
                            if (null != rate) {
                                List<RateQualifiedDetails> seasons = getRateQualifiedDetails(rate.getId(), caughtUpDate);
                                boolean allZeroOffsetSeasonsPresent = isAllZeroOffsetSeasonsPresent(seasons, caughtUpDate);
                                if (allZeroOffsetSeasonsPresent) {
                                    saveProductRateCode(currentRateCode, parentProduct, productRateCodes);
                                } else {
                                    brokenHierarchy = true;
                                    deleteExistingProductRateCode(productRateCodes, rateCode);
                                    break;
                                }
                            }
                        }
                        deleteProductRateCode(rateCode, parentProduct, brokenHierarchy);
                    } else {
                        deleteExistingProductRateCode(productRateCodes, rateCode);
                    }
                }
            }
        });
    }
    private void deleteExistingProductRateCode(List<ProductRateCode> productRateCodes, String rateCode) {
        List<ProductRateCode> existingMappings = productRateCodes.stream().filter(productRateCode -> productRateCode.getRateCode().equalsIgnoreCase(rateCode) && productRateCode.getProduct().isAgileRatesProduct()).collect(toList());
        if(!existingMappings.isEmpty()) {
            crudService.delete(existingMappings);
        }
    }
    private int getIndexOfParentProduct(List<String> list, String name) {
        for(int i=0; i<list.size(); ++i) {
            if (list.get(i).contains(name)) {
                return i;
            }
        }
        return -1;
    }
    private void deleteProductRateCode(String rateCode, Product parentProduct, boolean brokenHierarchy) {
        if (brokenHierarchy) {
            ProductRateCode productRateCode = getProductRateCodeByRateCode(rateCode, parentProduct.getId());
            if (null != productRateCode) {
                crudService.delete(productRateCode);
            }
        }
    }
    public void automateLinkedProductSrpAssociation(List<RateQualified> rateQualifiedList, Integer propertyId) {
        createProductRateCode(rateQualifiedList, propertyId);
        updateRestrictionsAssociationForTheSRPMappings(rateQualifiedList);
    }
    public void invalidateMissingRateCodes() {
        List<ProductRateCode> allProductRateCodes = crudService.findAll(ProductRateCode.class);
        List<Product> allProducts = agileRatesConfigurationService.findAgileRatesProducts();
        for (Product p : allProducts) {
            List<ProductRateCode> productRateCodes = allProductRateCodes.stream()
                    .filter(productRateCode -> productRateCode.getProduct().getId().equals(p.getId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(productRateCodes) && p.getStatus() != TenantStatusEnum.INVALID) {
                p.setInvalidReason(InvalidReason.MISSING_RATE_CODES);
                p.setStatus(TenantStatusEnum.INVALID);
            }
        }
        crudService.save(allProducts);
    }
    public boolean isRequiredExternalSystem(String clientCode, String propertyCode) {
        return externalSystemHelper.isHilstar(clientCode, propertyCode) || externalSystemHelper.isPCRS(clientCode, propertyCode);
    }

    public boolean areRequiredConfigParamsEnabled() {
        return configService.getBooleanParameterValue(FeatureTogglesConfigParamName.POPULATE_YIELD_AS_FROM_STREAMING_ENABLED) && configService.getBooleanParameterValue(FeatureTogglesConfigParamName.LINKED_PRODUCT_SRP_AUTOMATION_ENABLED);
    }
    public void startLinkedProductSRPAutomationJob() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(JobParameterKey.PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
        jobService.startGuaranteedNewInstance(JobName.LinkedProductSRPAutomationJob, parameters);
    }

    @ForTesting
    public void updateRestrictionsAssociationForTheSRPMappings() {
        List<RateQualified> rateQualifiedList = getRateQualifiedList();
        updateRestrictionsAssociationForTheSRPMappings(rateQualifiedList);
    }

    public void updateRestrictionsAssociationForTheSRPMappings(List<RateQualified> rateQualifiedList) {
        Map<Integer, Set<RateQualified>> srpCodesToAddInAssociation = new HashMap<>();
        Set<RateQualified> rateCodesToRemoveFromAssociation = new HashSet<>();
        List<Product> activeProducts = crudService.findByNamedQuery(Product.GET_ACTIVE_AGILE_RATE_PRODUCTS,
                QueryParameter.with(Constants.STATUS, TenantStatusEnum.ACTIVE).parameters());

        rateQualifiedList.forEach(rateCode -> {
            LinkedSRPMappings linkedSRPMapping = linkedSrpAPIService.getMapping(rateCode.getName());
            if (linkedSRPMapping != null) {
                Product parentProduct = activeProducts.stream()
                        .filter(p -> p.getName().equalsIgnoreCase(linkedSRPMapping.getLinkedSrpCode()) || p.getName().equalsIgnoreCase(linkedSRPMapping.getLinkedSrpCode().substring(linkedSRPMapping.getLinkedSrpCode().indexOf(UNDERSCORE) + 1)) )
                        .findFirst().orElse(null);
                if (parentProduct != null && linkedSRPMapping.isYieldAsParent()) {
                    addToMapOrUpdateMapIfKeyExists(srpCodesToAddInAssociation, parentProduct.getId(), rateCode);
                } else {
                    updateAssociationForRateCode(rateCode, activeProducts, srpCodesToAddInAssociation, rateCodesToRemoveFromAssociation);
                }
            } else {
                updateAssociationForRateCode(rateCode, activeProducts, srpCodesToAddInAssociation, rateCodesToRemoveFromAssociation);
            }
        });

        if (!srpCodesToAddInAssociation.isEmpty()) {
            agileRatesConfigurationService.saveAgileProductRestrictionAssociation(srpCodesToAddInAssociation);
        }
        if (!rateCodesToRemoveFromAssociation.isEmpty()) {
            agileRatesConfigurationService.removeAgileProductRestrictionAssociation(rateCodesToRemoveFromAssociation);
        }
    }

    public List<RateQualified> getRateQualifiedList() {
        Date latestSnapShotDate = fileMetadataService.getLatestSnapShotDate();
        List<FileMetadata> fileMetadataCDPBDEList =  fileMetadataService.getRecordsByStatusRecordTypeAndSnapshotDateLaterThan(ProcessStatus.SUCCESSFUL,
                RecordType.T2SNAP_RECORD_TYPE_ID, DateUtil.addDaysToDate(latestSnapShotDate, -1));

        FileMetadata fileMetadata = fileMetadataCDPBDEList.stream().filter(metaData -> metaData.getBde() == 0)
                .min(Comparator.comparing(FileMetadata::getId)).orElse(null);
        if(null == fileMetadata) {
            List<FileMetadata> currentFileMetadataList = fileMetadataCDPBDEList.stream().filter(metaData -> metaData.getBde() == 1 && metaData.getSnapshotDt().equals(latestSnapShotDate)).collect(toList());
            fileMetadata = currentFileMetadataList.isEmpty() ? null : currentFileMetadataList.get(0);
        }

        List<FileMetadata> fileMetadataList =  fileMetadataService.getRecordsByStatusRecordTypeAndSnapshotDateLaterThan(ProcessStatus.SUCCESSFUL,
                RecordType.QUALIFIED_RATE_RECORD_TYPE_ID, DateUtil.addDaysToDate(latestSnapShotDate, -1));

        if(!fileMetadataList.isEmpty() && null != fileMetadata) {
            FileMetadata finalFileMetadata = fileMetadata;
            List<Integer> list = fileMetadataList.stream()
                    .map(FileMetadata::getId).filter(id-> id > finalFileMetadata.getId())
                    .collect(Collectors.toList());
            return list.isEmpty() ? Collections.emptyList() : crudService.findByNamedQuery(RateQualified.BY_FILEMETADATA_IDS_RATE_QUALIFIED_TYPE_AND_STATUS, QueryParameter.with(FILE_METADATA_IDS, list).parameters());
        }
        return Collections.emptyList();
    }

    private Product getParentProduct(List<Product> activeProducts, String[] hierarchy) {
        Product parentProduct = null;
        for (String rateCodeName : hierarchy) {
            Product product = activeProducts.stream().filter(product1 -> product1.getName().equalsIgnoreCase(rateCodeName) || product1.getName().equalsIgnoreCase(rateCodeName.substring(rateCodeName.indexOf(UNDERSCORE) + 1))).findFirst().orElse(null);
            if (product != null) {
                parentProduct = product;
            }
        }
        return parentProduct;
    }
    private void saveProductRateCode(String rateCode, Product product, List<ProductRateCode> productRateCodes) {
        ProductRateCode productRateCode = getProductRateCodeByRateCode(rateCode, product.getId());
        if (null == productRateCode) {
            deleteExistingProductRateCode(productRateCodes, rateCode);
            ProductRateCode newProductRateCode = new ProductRateCode();
            newProductRateCode.setProduct(product);
            newProductRateCode.setRateCode(rateCode);
            crudService.save(newProductRateCode);
        }
    }

    boolean isAllZeroOffsetSeasonsPresent(List<RateQualifiedDetails> seasons, Date caughtUpDate) {
        if(seasons.isEmpty()) {
            return false;
        }
        for (RateQualifiedDetails season : seasons) {
            Set<java.time.DayOfWeek> effectiveDows = getDowsFallingInTheRange(season.getStartDate(), season.getEndDate(), caughtUpDate);
            for (java.time.DayOfWeek dayOfWeek : effectiveDows) {
                if (!isDayPriceEqualToZero(season, dayOfWeek)) {
                    return false;
                }
            }
        }
        return true;
    }

    List<RateQualifiedDetails> getRateQualifiedDetails(Integer rateQualifiedId, Date caughtUpDate) {
        var parameters = QueryParameter.with(RATE_QUALIFIED_IDS, List.of(rateQualifiedId)).and(DATE, caughtUpDate).parameters();
        return crudService.findByNamedQuery(RateQualifiedDetails.BY_RATE_QUALIFIED_IDS_AND_END_DATE, parameters);
    }

    RateQualified findSingleRateByName(String rateCodeName, Integer propertyId) {
        return crudService.findByNamedQuerySingleResult(RateQualified.BY_PROPERTY_AND_RATE_CODE_NAME_AND_STATUS,
                QueryParameter.with(PROPERTY_ID, propertyId).and(RATE_CODE_NAME,
                        rateCodeName).and(STATUS, 1).parameters());
    }

    ProductRateCode getProductRateCodeByRateCode(String rateCode, Integer productId) {
        return crudService.findByNamedQuerySingleResult(ProductRateCode.BY_RATE_CODE_AND_PRODUCT, QueryParameter.with(RATE_CODE, rateCode).and(PRODUCT_ID, productId).parameters());
    }

    Set<java.time.DayOfWeek> getDowsFallingInTheRange(Date startDate, Date endDate, Date caughtUpDate) {
        if(startDate.before(caughtUpDate)) {
            startDate = caughtUpDate;
        }
        if (DateUtil.getTotalNoOfDays(startDate, endDate) < 7) {
            Set<java.time.DayOfWeek> dows = new HashSet<>();
            for (Date date = startDate; date.before(DateUtil.addDaysToDate(endDate, 1));
                 date = DateUtil.addDaysToDate(date, 1)) {
                dows.add(DateUtil.convertJavaUtilDateToLocalDate(date).getDayOfWeek());
            }
            return dows;
        }
        return Set.of(java.time.DayOfWeek.SUNDAY, java.time.DayOfWeek.MONDAY, java.time.DayOfWeek.TUESDAY, java.time.DayOfWeek.WEDNESDAY, java.time.DayOfWeek.THURSDAY, java.time.DayOfWeek.FRIDAY, java.time.DayOfWeek.SATURDAY);
    }

    boolean isDayPriceEqualToZero(RateQualifiedDetails rateQualifiedDetails, java.time.DayOfWeek dayOfWeek) {
        switch (dayOfWeek) {
            case SUNDAY:
                return rateQualifiedDetails.getSunday().equals(ZERO_OFFSET);
            case MONDAY:
                return rateQualifiedDetails.getMonday().equals(ZERO_OFFSET);
            case TUESDAY:
                return rateQualifiedDetails.getTuesday().equals(ZERO_OFFSET);
            case WEDNESDAY:
                return rateQualifiedDetails.getWednesday().equals(ZERO_OFFSET);
            case THURSDAY:
                return rateQualifiedDetails.getThursday().equals(ZERO_OFFSET);
            case FRIDAY:
                return rateQualifiedDetails.getFriday().equals(ZERO_OFFSET);
            case SATURDAY:
                return rateQualifiedDetails.getSaturday().equals(ZERO_OFFSET);
        }
        return false;
    }

    public void addToMapOrUpdateMapIfKeyExists(Map<Integer, Set<RateQualified>> map, Integer key, RateQualified srpCode) {
        if (map.containsKey(key)) {
            map.get(key).add(srpCode);
        } else {
            Set<RateQualified> newSet = new HashSet<>();
            newSet.add(srpCode);
            map.put(key, newSet);
        }
    }

    private void updateAssociationForRateCode(RateQualified rateCode, List<Product> activeProducts, Map<Integer, Set<RateQualified>> srpCodesToAddInAssociation, Set<RateQualified> rateCodesToRemoveFromAssociation) {
        Product childProduct = activeProducts.stream()
                .filter(p -> p.getName().equalsIgnoreCase(rateCode.getName()) || p.getName().equalsIgnoreCase(rateCode.getName().substring(rateCode.getName().indexOf(UNDERSCORE) + 1)))
                .findFirst().orElse(null);
        if (childProduct != null) {
            addToMapOrUpdateMapIfKeyExists(srpCodesToAddInAssociation, childProduct.getId(), rateCode);
        } else {
            rateCodesToRemoveFromAssociation.add(rateCode);
        }
    }
}
