package com.ideas.tetris.pacman.services.opera;

import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

/**
 * Created by idnrbk on 5/13/2015.
 */
@Component
public class OperaOccupancySummaryFeedValidationService {

    private static final Logger LOGGER = Logger.getLogger(OperaOccupancySummaryFeedValidationService.class.getName());
    @TenantCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("tenantCrudServiceBean")
    CrudService crudService;

    public static final String QUERY_DUPLICATE_MSRT_SUMMARY_RECORDS = "Select Occupancy_DT ,Room_Type ,Market_Code from opera.History_Occupancy_Summary " +
            "where Data_Load_Metadata_ID in (:dlmIds) group by Occupancy_DT,Room_Type,Market_Code having count(Occupancy_DT) > 1";

    public static final String QUERY_DUPLICATE_TOTAL_SUMMARY_RECORDS = "Select Occupancy_DT from opera.History_Occupancy_Summary " +
            "where Data_Load_Metadata_ID in (:dlmIds) group by Occupancy_DT having count(Occupancy_DT) > 1";

    private static final String QUERY_COUNT_OCCUPANCY_SUMMARY_WITH_MISSING_OCC_DATE = "Select count(*) from " +
            "opera.History_Occupancy_Summary where Data_Load_Metadata_ID in (:dlmIds) and Occupancy_DT = '' ";

    private static final String QUERY_OCCUPANCY_SUMMARY_WITH_MISSING_ROOM_TYPE = "Select Occupancy_DT from " +
            "opera.History_Occupancy_Summary where Data_Load_Metadata_ID in (:dlmIds) and Occupancy_DT <> '' and Room_Type = '' ";

    private static final String QUERY_OCCUPANCY_SUMMARY_WITH_MISSING_MARKET_SEGMENT = "Select Occupancy_DT from " +
            "opera.History_Occupancy_Summary where Data_Load_Metadata_ID in (:dlmIds) and Occupancy_DT <> '' and Market_Code = '' ";

    public static final String PRIMARY_ATTRIBUTE = "Occupancy Date";
    public static final String MSG_INVALID_RT = " have invalid Room Type";
    public static final String MSG_INVALID_MS = " have invalid Market Segment";

    protected boolean isOccupancySummaryMissingOccupancyDate(String correlationId, List<Integer> operaDataLoadTypeCodes, String msgMissingPrimaryAttribute) {
        List<Integer> queryResults = crudService.findByNativeQuery(QUERY_COUNT_OCCUPANCY_SUMMARY_WITH_MISSING_OCC_DATE,
                QueryParameter.with("dlmIds", operaDataLoadTypeCodes).parameters());
        Integer count = queryResults.get(0);
        if (count > 0) {
            LOGGER.error("OperaFeedValidationStep Failed: " + count + " " + msgMissingPrimaryAttribute);
            return true;
        }
        return false;
    }

    protected boolean isOccupancySummaryMissingRoomType(String correlationId, List<Integer> operaDataLoadTypeCodes) {
        List<String> queryResults = crudService.findByNativeQuery(QUERY_OCCUPANCY_SUMMARY_WITH_MISSING_ROOM_TYPE,
                QueryParameter.with("dlmIds", operaDataLoadTypeCodes).parameters());
        return logMissingDataError(queryResults, MSG_INVALID_RT, PRIMARY_ATTRIBUTE);
    }

    protected boolean isOccupancySummaryMissingMarketSegment(String correlationId, List<Integer> operaDataLoadTypeCodes) {
        List<String> queryResults = crudService.findByNativeQuery(QUERY_OCCUPANCY_SUMMARY_WITH_MISSING_MARKET_SEGMENT,
                QueryParameter.with("dlmIds", operaDataLoadTypeCodes).parameters());
        return logMissingDataError(queryResults, MSG_INVALID_MS, PRIMARY_ATTRIBUTE);
    }

    protected boolean logMissingDataError(List<String> queryResults, String missingAttribute, String initialMessage) {
        if (!CollectionUtils.isEmpty(queryResults)) {
            LOGGER.error((new StringBuffer("OperaFeedValidationStep Failed: ").append(initialMessage).append(" : ").append(queryResults.toString()).append(missingAttribute)).toString());
            return true;
        }
        return false;
    }
}
