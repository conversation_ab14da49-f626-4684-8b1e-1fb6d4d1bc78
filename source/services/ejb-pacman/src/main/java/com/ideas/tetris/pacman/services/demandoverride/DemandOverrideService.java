package com.ideas.tetris.pacman.services.demandoverride;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Lists;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.ArrivalDemandForecast;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyDemandForecast;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalOverrideValue;
import com.ideas.tetris.pacman.services.demandoverride.dto.DemandOverride;
import com.ideas.tetris.pacman.services.demandoverride.dto.DemandWashOverrideFGACDetailsDto;
import com.ideas.tetris.pacman.services.demandoverride.dto.ForecastGroupWashOverride;
import com.ideas.tetris.pacman.services.demandoverride.dto.WashOverride;
import com.ideas.tetris.pacman.services.demandoverride.entity.ArrivalDemandOverride;
import com.ideas.tetris.pacman.services.demandoverride.entity.OccupancyDemandOverride;
import com.ideas.tetris.pacman.services.demandoverride.entity.OccupancyForecastDetailView;
import com.ideas.tetris.pacman.services.demandoverride.entity.OccupancyForecastDetailViewForCR;
import com.ideas.tetris.pacman.services.demandoverride.entity.WashForecast;
import com.ideas.tetris.pacman.services.demandoverride.entity.WashForecastForecastGroup;
import com.ideas.tetris.pacman.services.groupwash.WashOverrideByGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastType;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.pacman.services.security.User;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.rest.annotation.DateFormat;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.joda.time.LocalDate;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class DemandOverrideService {

    private static final int BATCH_SIZE = 1000;
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;
    @Autowired
    DecisionService decisionService;
    @Autowired
	private PacmanConfigParamsService configParamService;
    @Autowired
	private DateService dateService;

    private static final String PROPERTY_ID = "propertyId";
    private static final String FORECAST_GROUP_ID = "forecastGroupId";
    private static final String FORECAST_GROUP_IDS = "forecastGroupIds";
    private static final String ACCOM_CLASS_ID = "accomClassId";
    private static final String ACCOM_CLASS_IDS = "accomClassIds";
    private static final String OCCUPANCY_DATE = "occupancyDate";
    private static final String OCCUPANCY_DATES = "occupancyDates";
    private static final String ARRIVAL_DATE = "arrivalDate";
    private static final String ARRIVAL_DATES = "arrivalDates";
    private static final String START_DATE = "startDate";
    private static final String END_DATE = "endDate";
    private static final String PRODUCT = "product";
    public static final String MAX_LOS = "pacman.bar.maxLOS";
    private static final String FIND_OVERRIDES_FOR_CANCELLED_GROUPS = "select distinct pgm.Group_Code, gb.Group_ID,  gb.Occupancy_DT, fg.Forecast_Group_Name, odfo.Forecast_Group_ID, ac.Accom_Class_Code,at.Accom_Class_ID" +
            " from Occupancy_Demand_FCST_ovr odfo" +
            " inner join Group_Block gb on odfo.Occupancy_DT = gb.Occupancy_DT" +
            " inner join Pace_Group_Master pgm on gb.Group_ID = pgm.Group_ID" +
            " inner join Accom_Type at on gb.Accom_Type_ID = at.Accom_Type_ID and odfo.Accom_Class_ID = at.Accom_Class_ID" +
            " inner join Accom_Class ac on at.Accom_Class_ID = ac.Accom_Class_ID" +
            " inner join Forecast_Group fg on odfo.Forecast_Group_ID = fg.Forecast_Group_ID" +
            " where odfo.Status_ID = 1 and pgm.Group_Status_Code in ('CANCELLED','LOST/REGRET') " +
            " and pgm.End_DT >= :caughtUpDate and pgm.File_Metadata_ID = :fileMetadataId" +
            " order by group_code,occupancy_dt,Forecast_Group_Name,Accom_Class_Code";

    public void setConfigParamService(PacmanConfigParamsService configParamService) {
        this.configParamService = configParamService;
    }

    public void setDecisionService(DecisionService decisionService) {
        this.decisionService = decisionService;
    }

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    public Map<DemandOverride, Set<DemandOverride>> saveOverrides(Set<DemandOverride> overrides, Set<WashOverride> washOverrides) {
        return saveOverrides(overrides, washOverrides, null);
    }

    public Map<DemandOverride, Set<DemandOverride>> saveOverrides(Set<DemandOverride> overrides, Set<WashOverride> washOverrides, Decision decision) {
        saveWashOverrides(washOverrides, decision);
        Map<DemandOverride, Set<DemandOverride>> conflicts = saveOverrides(overrides, decision);
        if (conflicts != null && !conflicts.isEmpty()) {
            return conflicts;
        }
        return null;
    }

    public Map<DemandOverride, Set<DemandOverride>> saveOverrides(Set<DemandOverride> overrides) {
        return saveOverrides(overrides, (Decision) null);
    }

    private Map<DemandOverride, Set<DemandOverride>> saveOverrides(Set<DemandOverride> overrides, Decision decision) {
        if (CollectionUtils.isEmpty(overrides)) {
            return null;
        }
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(new Date());
        calendar.add(Calendar.SECOND, -1);
        Date updateOverrideStartTime = calendar.getTime();
        Map<DemandOverride, Set<DemandOverride>> conflicts = getConflicts(overrides);
        List<DemandOverride> editConflicts = new ArrayList<>();
        List<DemandOverride> keysToRemove = new ArrayList<>();
        conflicts.forEach((key, value) -> {
            if (key instanceof com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride) {
                List<DemandOverride> temp = value.stream()
                        .filter(v -> v instanceof com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride)
                        .collect(Collectors.toList());
                if (!temp.isEmpty()) {
                    keysToRemove.add(key);
                    editConflicts.addAll(temp);
                }
            }
        });
        keysToRemove.forEach(conflicts::remove);

        if (MapUtils.isNotEmpty(conflicts)) {
            return conflicts;
        }
        Set<DemandOverride> removals = getRemovals(overrides);
        // add in conflicting occupancy values
        removals.addAll(editConflicts);
        List<OccupancyDemandForecast> occupancyDemandForecasts = getAllOccupancyDemandForecasts(removals);

        removeOverridesEfficiently(removals, occupancyDemandForecasts);

        Set<DemandOverride> creates = getCreates(overrides);

        //query all the information we need for arrival overrides up front so we are not executing a ton of queries in loops
        //first extract out our arrival overrides
        List<com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride> arrivalOverrides = creates.stream()
                .filter(override -> override instanceof com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride)
                .map(com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride.class::cast)
                .collect(Collectors.toList());

        Map<DemandOverrideKey, List<ArrivalDemandForecast>> arrivalDemandForecastsGrouped = null;
        List<RateUnqualified> rates = null;
        Map<DecisionBAROutputKey, List<DecisionBAROutput>> decisionsGrouped = null;

        //we have arrival overrides, lets execute the queries one time
        if (!arrivalOverrides.isEmpty()) {
            List<Integer> roomClassIds = arrivalOverrides.stream()
                    .map(com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride::getAccomClassId)
                    .distinct()
                    .collect(Collectors.toList());

            List<Integer> forecastGroupIds = arrivalOverrides.stream()
                    .map(com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride::getForecastGroupId)
                    .distinct()
                    .collect(Collectors.toList());

            List<Date> arrivalDates = arrivalOverrides.stream()
                    .map(com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride::getArrivalDate)
                    .distinct()
                    .collect(Collectors.toList());

            List<ArrivalDemandForecast> arrivalDemandForecasts = getArrivalDemandForecasts(roomClassIds, forecastGroupIds, arrivalDates);

            //group the demand forecasts so we can extract specific data for individual arrival demand overrides
            arrivalDemandForecastsGrouped = arrivalDemandForecasts.stream().collect(
                    Collectors.groupingBy(forecast -> new DemandOverrideKey(forecast.getAccomClassID(), forecast.getForecastGroupId(), forecast.getArrivalDate())));

            rates = crudService.findByNamedQuery(RateUnqualified.BY_PROPERTY_ID,
                    QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());

            List<DecisionBAROutput> decisions = crudService.findByNamedQuery(DecisionBAROutput.BY_DATELIST_AND_ACCOMCLASSIDLIST,
                    QueryParameter.with(ACCOM_CLASS_IDS, roomClassIds).
                            and(ARRIVAL_DATES, arrivalDates).
                            and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());

            decisionsGrouped = decisions.stream().collect(
                    Collectors.groupingBy(d -> new DecisionBAROutputKey(d.getAccomClassId(), d.getArrivalDate())));
        }
        List<ForecastGroup> forecastGroupList = crudService.findByNamedQuery(ForecastGroup.BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanThreadLocalContextHolder.getWorkContext().getPropertyId())
                        .parameters());

        saveOverridesEfficiently(decision, creates, arrivalDemandForecastsGrouped, rates, decisionsGrouped, forecastGroupList);

        boolean isOccupancyOverrideRemoved = isOccupancyOverridePresentIn(removals);
        boolean isOccupancyOverrideCreated = isOccupancyOverridePresentIn(creates);
        boolean isArrivalByLOSOverrideRemoved = isArrivalByLOSOverridePresentIn(removals);
        boolean isArrivalByLOSOverrideCreated = isArrivalByLOSOverridePresentIn(creates);
        updateUserRemainingDemandUsing(OccupancyDemandForecast.UPDATE_USER_REMAINING_DEMAND, isOccupancyOverrideRemoved, isOccupancyOverrideCreated, updateOverrideStartTime);
        updateUserRemainingDemandUsing(ArrivalDemandForecast.UPDATE_USER_REMAINING_DEMAND, isArrivalByLOSOverrideRemoved, isArrivalByLOSOverrideCreated, updateOverrideStartTime);

        return null;
    }

    private void updateUserRemainingDemandUsing(String queryName, boolean isOverrideRemoved, boolean isOverrideCreated, Date updateOverrideStartTime) {
        if (isOverrideRemoved) {
            crudService.executeUpdateByNamedQuery(queryName,
                    QueryParameter.with("lastUpdatedDate", updateOverrideStartTime)
                            .and("statusId", "2").parameters());
        }

        if (isOverrideCreated) {
            crudService.executeUpdateByNamedQuery(queryName,
                    QueryParameter.with("lastUpdatedDate", updateOverrideStartTime)
                            .and("statusId", "1").parameters());
        }
    }

    private boolean isOccupancyOverridePresentIn(Set<DemandOverride> removals) {
        return removals.stream().anyMatch(override -> override instanceof com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride);
    }

    private boolean isArrivalByLOSOverridePresentIn(Set<DemandOverride> removals) {
        return removals.stream().anyMatch(override -> override instanceof com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride);
    }

    private void saveOverridesEfficiently(Decision decision, Set<DemandOverride> creates, Map<DemandOverrideKey, List<ArrivalDemandForecast>> arrivalDemandForecastsGrouped, List<RateUnqualified> rates, Map<DecisionBAROutputKey, List<DecisionBAROutput>> decisionsGrouped, List<ForecastGroup> forecastGroupList) {
        boolean isContinuousPricingEnabled = configParamService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value());
        List<Integer> accomClassIds = creates.stream()
                .map(DemandOverride::getAccomClassId)
                .distinct()
                .collect(Collectors.toList());

        List<Integer> forecastGroupIds = creates.stream()
                .map(DemandOverride::getForecastGroupId)
                .distinct()
                .collect(Collectors.toList());

        List<Date> occupancyDates = creates.stream()
                .map(DemandOverride::getOccupancyDate)
                .distinct()
                .collect(Collectors.toList());
        List<OccupancyForecastDetailView> allOccupancyForecastDetailViews = new ArrayList<>();
        Map<LocalDate, Map<Integer, List<CPDecisionBAROutput>>> cpDecisionsByArrivalDateAndAccomClass = new HashMap<>();
        Map<Date, Map<Integer, List<DecisionBAROutput>>> decisionsByArrivalDateAndAccomClass = new HashMap<>();
        List<OccupancyDemandForecast> filteredOccupancyDemands = new ArrayList<>();
        RateUnqualified defaultRateUnqualified = null;
        List<RateUnqualified> rateUnqualifieds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(creates)) {
            allOccupancyForecastDetailViews = getAllOccupancyForecastDetailViews(occupancyDates, forecastGroupIds, accomClassIds);
            Date startDate = creates.stream().map(DemandOverride::getOccupancyDate).min(Date::compareTo).get();
            Date endDate = creates.stream().map(DemandOverride::getOccupancyDate).max(Date::compareTo).get();
            List<OccupancyDemandForecast> occupancyDemandsForDateRange = getOccupancyDemands(startDate, endDate);
            filteredOccupancyDemands = occupancyDemandsForDateRange.stream()
                    .filter(occDemandFcst -> forecastGroupIds.contains(occDemandFcst.getForecastGroupId()))
                    .filter(occDemandFcst -> accomClassIds.contains(occDemandFcst.getAccomClassID()))
                    .collect(Collectors.toList());
            if (isContinuousPricingEnabled) {
                cpDecisionsByArrivalDateAndAccomClass = getCpDecisionsByOccupancyDatesAndAccomClassIds(new HashSet<>(occupancyDates), new HashSet<>(accomClassIds));
            } else {
                decisionsByArrivalDateAndAccomClass = getDecisionsByOccupancyDatesAndAccomClassIds(new HashSet<>(occupancyDates), new HashSet<>(accomClassIds));
                List<Integer> matchingRateUnqualifiedIds = filteredOccupancyDemands.stream()
                        .map(OccupancyDemandForecast::getRateUnqualifiedId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(matchingRateUnqualifiedIds)) {
                    rateUnqualifieds = crudService.findByNamedQuery(RateUnqualified.BY_IDS, QueryParameter.with("ids", matchingRateUnqualifiedIds).parameters());
                }
                defaultRateUnqualified = getDefaultRateUnqualified();
            }
        }
        for (DemandOverride create : creates) {
            if (create instanceof com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride) {
                decision = saveOccupancyOverride((com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride) create, decision, isContinuousPricingEnabled, allOccupancyForecastDetailViews, 1, cpDecisionsByArrivalDateAndAccomClass, decisionsByArrivalDateAndAccomClass, filteredOccupancyDemands, rateUnqualifieds, defaultRateUnqualified);
            } else if (create instanceof com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride) {
                decision = saveArrivalOverrideBeta((com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride) create, decision, arrivalDemandForecastsGrouped, rates, decisionsGrouped, forecastGroupList, cpDecisionsByArrivalDateAndAccomClass);
            }
        }
    }

    private List<ArrivalDemandForecast> getArrivalDemandForecasts(List<Integer> roomClassIds, List<Integer> forecastGroupIds, List<Date> arrivalDates) {
        if (CollectionUtils.isEmpty(roomClassIds) || CollectionUtils.isEmpty(forecastGroupIds) || CollectionUtils.isEmpty(arrivalDates)) {
            return new ArrayList<>();
        }
        return crudService.findByNamedQuery(ArrivalDemandForecast.BY_ARRIVAL_DATES_IN_AND_PROPERTY_ID_AND_FORECAST_GROUP_ID_AND_BY_ACCOM_CLASSES,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and(ARRIVAL_DATES, arrivalDates)
                        .and(FORECAST_GROUP_IDS, forecastGroupIds)
                        .and(ACCOM_CLASS_IDS, roomClassIds).parameters());
    }

    private List<OccupancyDemandForecast> getAllOccupancyDemandForecasts(Set<DemandOverride> removals) {
        List<OccupancyDemandForecast> occupancyDemandForecasts = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(removals)) {
            Date startDate = removals.stream().map(DemandOverride::getOccupancyDate).min(Date::compareTo).get();
            Date endDate = removals.stream().map(DemandOverride::getOccupancyDate).max(Date::compareTo).get();
            occupancyDemandForecasts = getOccupancyDemands(startDate, endDate);
        }
        return occupancyDemandForecasts;
    }

    private Set<DemandOverride> getRemovals(Set<DemandOverride> overrides) {
        Set<DemandOverride> removals = new HashSet<>();
        for (DemandOverride override : overrides) {
            if (!override.getIds().isEmpty()) {
                removals.add(override);
            }
        }
        return removals;
    }

    private Set<DemandOverride> getCreates(Set<DemandOverride> overrides) {
        Set<DemandOverride> creates = new HashSet<>();
        for (DemandOverride override : overrides) {
            if (override.isActive()) {
                creates.add(override);
            }
        }
        return creates;
    }


    private void removeOverride(DemandOverride override, List<OccupancyDemandForecast> occupancyDemandForecasts, Map<Integer, ArrivalDemandOverride> arrivalDemandOverrideById, List<ArrivalDemandForecast> arrivalDemandForecasts) {
        if (override instanceof com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride) {
            OccupancyDemandOverride occupancyOverride =
                    crudService.find(OccupancyDemandOverride.class, ((com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride) override).getId());
            occupancyOverride.remove();
            occupancyOverride.setLastUpdatedByUserId(Integer.parseInt(PacmanWorkContextHelper.getUserId()));
            occupancyOverride.setLastUpdatedDate(new Date());
            crudService.save(occupancyOverride);
        } else if (override instanceof com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride) {
            com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride arrivalOverrideDTO = (com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride) override;
            for (ArrivalOverrideValue value : arrivalOverrideDTO.getValues()) {
                if (value.getId() != null) {
                    ArrivalDemandOverride arrivalOverride = arrivalDemandOverrideById.get(value.getId());
                    if (arrivalOverride != null) {
                        arrivalOverride.remove();
                        arrivalOverride.setLastUpdatedByUserId(Integer.parseInt(PacmanWorkContextHelper.getUserId()));
                        arrivalOverride.setLastUpdatedDate(new Date());
                        crudService.save(arrivalOverride);
                    }
                }
            }
        }
    }

    private <T> List<T> getIds(List<ArrivalDemandOverride> arrivalDemandOverrides, Function<ArrivalDemandOverride, T> getIdFunction) {
        return arrivalDemandOverrides.stream()
                .map(getIdFunction)
                .distinct()
                .collect(Collectors.toList());
    }


    private Decision saveOccupancyOverride(com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride override, Decision decision, boolean isContinuousPricingEnabled, List<OccupancyForecastDetailView> occupancyForecastDetailViews, int lengthOfStay, Map<LocalDate, Map<Integer, List<CPDecisionBAROutput>>> cpDecisionsByArrivalDateAndAccomClass, Map<Date, Map<Integer, List<DecisionBAROutput>>> decisionsByArrivalDateAndAccomClass, List<OccupancyDemandForecast> occupancyDemandForecasts, List<RateUnqualified> rateUnqualifieds, RateUnqualified defaultRateUnqualified) {
        if (decision == null) {
            decision = decisionService.createDemandOverrideDecision();
            decision = crudService.save(decision);
        }
        OccupancyDemandOverride overrideEntity = new OccupancyDemandOverride();
        overrideEntity.setDecisionID(decision.getId());
        overrideEntity.setForecastGroupId(override.getForecastGroupId());
        overrideEntity.setAccomClassID(override.getAccomClassId());
        overrideEntity.setPropertyID(PacmanWorkContextHelper.getPropertyId());
        overrideEntity.setOccupancyDate(override.getOccupancyDate());
        overrideEntity.setRemainingDemand(override.getRemainingDemand());
        overrideEntity.setOverrideValue(override.getValue());
        overrideEntity.setStatusId(Constants.ACTIVE_STATUS_ID);
        overrideEntity.setCreatedByUserId(getCurrentUser());
        overrideEntity.setLastUpdatedByUserId(getCurrentUser());
        overrideEntity.setCreateDate(new Date());
        overrideEntity.setLastUpdatedDate(new Date());
        overrideEntity.setRoomsSold(getRoomsSold(occupancyForecastDetailViews, override.getOccupancyDate(), override.getForecastGroupId(), override.getAccomClassId()));

        OccupancyDemandForecast forecast = updateUserDemand(overrideEntity, occupancyDemandForecasts);
        if (isContinuousPricingEnabled) {
            if (forecast != null && forecast.getRateValue() != null) {
                overrideEntity.setRateValue(forecast.getRateValue());
            } else {
                overrideEntity.setRateValue(getOptimalBar(overrideEntity.getOccupancyDate(), overrideEntity.getAccomClassID(), cpDecisionsByArrivalDateAndAccomClass, lengthOfStay));
            }
        } else {
            overrideEntity.setRateUnqualified(getRateUnqualifiedForOccupancyDemand(overrideEntity, occupancyDemandForecasts, rateUnqualifieds, decisionsByArrivalDateAndAccomClass, defaultRateUnqualified));
        }

        if (forecast != null && forecast.getRateForecast() != null) {
            overrideEntity.setRateForecast(forecast.getRateForecast());
        }

        crudService.save(overrideEntity);
        return decision;
    }

    private BigDecimal getOptimalBar(Date occupancyDate, Integer accomClassID, Map<LocalDate, Map<Integer, List<CPDecisionBAROutput>>> cpDecisionsByArrivalDateAndAccomClass, int lengthOfStay) {
        BigDecimal optimalBar = null;
        Map<Integer, List<CPDecisionBAROutput>> cpDecisionsByAccomClass = cpDecisionsByArrivalDateAndAccomClass.get(LocalDate.fromDateFields(occupancyDate));
        if (null != cpDecisionsByAccomClass) {
            List<CPDecisionBAROutput> cpDecisionBAROutputs = cpDecisionsByAccomClass.get(accomClassID);
            if (null != cpDecisionBAROutputs) {
                optimalBar = cpDecisionBAROutputs.stream()
                        .filter(cpBarOutput -> cpBarOutput.getLengthOfStay().equals(lengthOfStay) || cpBarOutput.getLengthOfStay().equals(-1))
                        .map(CPDecisionBAROutput::getOptimalBAR)
                        .findFirst()
                        .orElse(null);
            }
        }
        return optimalBar;
    }

    private RateUnqualified getRateUnqualifiedForOccupancyDemand(OccupancyDemandOverride overrideEntity, List<OccupancyDemandForecast> occupancyDemandForecasts, List<RateUnqualified> rateUnqualifieds, Map<Date, Map<Integer, List<DecisionBAROutput>>> decisionsByArrivalDateAndAccomClass, RateUnqualified defaultRateUnqualified) {
        RateUnqualified rateUnqualified = getLowestPricePointRateForOccupancyDemand(overrideEntity, occupancyDemandForecasts, rateUnqualifieds);
        if (null == rateUnqualified) {
            rateUnqualified = getBestAvailableRateForOccupancyDemand(overrideEntity, decisionsByArrivalDateAndAccomClass, defaultRateUnqualified);
        }
        return rateUnqualified;
    }

    private RateUnqualified getLowestPricePointRateForOccupancyDemand(OccupancyDemandOverride overrideEntity, List<OccupancyDemandForecast> occupancyDemandForecasts, List<RateUnqualified> rateUnqualifieds) {
        OccupancyDemandForecast occupancyDemandForecast = occupancyDemandForecasts.stream().filter(odf ->
                odf.getOccupancyDate().equals(overrideEntity.getOccupancyDate())
                        && odf.getForecastGroupId().equals(overrideEntity.getForecastGroupId())
                        && odf.getAccomClassID().equals(overrideEntity.getAccomClassID())).findFirst().orElse(null);
        return rateUnqualifieds
                .stream()
                .filter(rateUnqualified -> null != occupancyDemandForecast && rateUnqualified.getId().equals(occupancyDemandForecast.getRateUnqualifiedId()))
                .findFirst()
                .orElse(null);
    }

    private List<OccupancyForecastDetailView> getAllOccupancyForecastDetailViews(List<Date> occupancyDates, List<Integer> forecastGroupIds, List<Integer> accomClassIds) {
        if (CollectionUtils.isEmpty(occupancyDates) || CollectionUtils.isEmpty(forecastGroupIds) || CollectionUtils.isEmpty(accomClassIds)) {
            return new ArrayList<>();
        }
        return crudService.findByNamedQuery(OccupancyForecastDetailView.BY_ROOM_CLASSES_FORECAST_GROUPS_OCCUPANCY_DATES,
                QueryParameter.with(OCCUPANCY_DATES, occupancyDates).
                        and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).
                        and(FORECAST_GROUP_IDS, forecastGroupIds).
                        and(ACCOM_CLASS_IDS, accomClassIds).
                        parameters());
    }

    private BigDecimal getRoomsSold(List<OccupancyForecastDetailView> occupancyForecastDetailViews, Date occupancyDate, Integer forecastGroupId, Integer accomClassId) {
        OccupancyForecastDetailView occupancyForecastDetailView = occupancyForecastDetailViews.stream().filter(detailView -> detailView.getOccupancyDate().equals(occupancyDate)
                && detailView.getForecastGroupId().equals(forecastGroupId)
                && detailView.getAccomClassID().equals(accomClassId)).findFirst().orElse(null);
        if (occupancyForecastDetailView != null && occupancyForecastDetailView.getRoomsSold() != null) {
            return occupancyForecastDetailView.getRoomsSold();
        }
        return BigDecimal.ZERO;
    }

    private OccupancyDemandForecast updateUserDemand(OccupancyDemandOverride overrideEntity, List<OccupancyDemandForecast> occupancyDemandForecasts) {
        OccupancyDemandForecast forecast;
        if (CollectionUtils.isEmpty(occupancyDemandForecasts)) {
            forecast = getOccupancyDemandForecastFromDB(overrideEntity);
        } else {
            forecast = occupancyDemandForecasts.stream()
                    .filter(occDemandForecast -> occDemandForecast.getOccupancyDate().equals(overrideEntity.getOccupancyDate()))
                    .filter(occDemandForecast -> occDemandForecast.getAccomClassID().equals(overrideEntity.getAccomClassID()))
                    .filter(occDemandForecast -> occDemandForecast.getForecastGroupId().equals(overrideEntity.getForecastGroupId()))
                    .findFirst()
                    .orElse(null);
        }
        return forecast;
    }

    private OccupancyDemandForecast getOccupancyDemandForecastFromDB(OccupancyDemandOverride overrideEntity) {
        return crudService.findByNamedQuerySingleResult(OccupancyDemandForecast.BY_OCCUPANCY_DATE_AND_PROPERTY_ID_AND_FORECAST_GROUP_ID_AND_ACCOM_CLASS_ID,
                QueryParameter.with(OCCUPANCY_DATE, overrideEntity.getOccupancyDate()).
                        and(FORECAST_GROUP_ID, overrideEntity.getForecastGroupId()).
                        and(ACCOM_CLASS_ID, overrideEntity.getAccomClassID()).
                        and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).
                        parameters());
    }


    private Decision saveArrivalOverrideBeta(com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride override, Decision decision, Map<DemandOverrideKey, List<ArrivalDemandForecast>> arrivalDemandForecastsGrouped, List<RateUnqualified> rates, Map<DecisionBAROutputKey, List<DecisionBAROutput>> descisions, List<ForecastGroup> forecastGroupList, Map<LocalDate, Map<Integer, List<CPDecisionBAROutput>>> cpDecisionsByArrivalDateAndAccomClass) {
        if (decision == null) {
            decision = decisionService.createDemandOverrideDecision();
            decision = crudService.save(decision);
        }

        //extract out the arrival demand forecasts that represent the passed in override
        List<ArrivalDemandForecast> arrivalDemandForecasts = arrivalDemandForecastsGrouped.get(new DemandOverrideKey(override.getAccomClassId(), override.getForecastGroupId(), override.getArrivalDate()));

        //extract out the decisions that represent the override
        List<DecisionBAROutput> decisionBAROutputs = descisions.get(new DecisionBAROutputKey(override.getAccomClassId(), override.getArrivalDate()));

        int maxLos = Integer.parseInt(configParamService.getParameterValue(MAX_LOS));

        for (ArrivalOverrideValue value : override.getValues()) {
            if (value.isActive()) {
                ArrivalDemandOverride overrideEntity = new ArrivalDemandOverride();
                overrideEntity.setDecisionID(decision.getId());
                overrideEntity.setForecastGroupId(override.getForecastGroupId());
                overrideEntity.setAccomClassID(override.getAccomClassId());
                overrideEntity.setPropertyID(PacmanWorkContextHelper.getPropertyId());
                overrideEntity.setArrivalDate(override.getArrivalDate());
                overrideEntity.setRemainingDemand(value.getRemainingDemand());
                overrideEntity.setLengthOfStay(value.getLengthOfStay());
                if (null == value.getValue()) {
                    overrideEntity.setOverrideValue(BigDecimal.ZERO);
                } else {
                    overrideEntity.setOverrideValue(value.getValue());
                }
                overrideEntity.setStatusId(Constants.ACTIVE_STATUS_ID);
                overrideEntity.setCreatedByUserId(getCurrentUser());
                overrideEntity.setLastUpdatedByUserId(getCurrentUser());
                overrideEntity.setCreateDate(new Date());
                overrideEntity.setLastUpdatedDate(new Date());

                //find the forecast that represents the current LOS value
                ArrivalDemandForecast forecast = arrivalDemandForecasts.stream().filter(arrivalForecast -> arrivalForecast.getLengthOfStay().equals(value.getLengthOfStay())).findFirst().orElse(null);

                setRateValueIn(overrideEntity, rates, forecastGroupList, decisionBAROutputs, maxLos, value, forecast, cpDecisionsByArrivalDateAndAccomClass);

                crudService.save(overrideEntity);
            }
        }
        return decision;
    }

    private void setRateValueIn(ArrivalDemandOverride overrideEntity, List<RateUnqualified> rates, List<ForecastGroup> forecastGroupList, List<DecisionBAROutput> decisionBAROutputs, int maxLos, ArrivalOverrideValue value, ArrivalDemandForecast forecast, Map<LocalDate, Map<Integer, List<CPDecisionBAROutput>>> cpDecisionsByArrivalDateAndAccomClass) {
        if (configParamService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value())) {
            if (forecast != null && forecast.getRateValue() != null) {
                overrideEntity.setRateValue(forecast.getRateValue());
            } else {
                if (MapUtils.isNotEmpty(cpDecisionsByArrivalDateAndAccomClass)) {
                    BigDecimal optimalBar = getOptimalBar(overrideEntity.getArrivalDate(), overrideEntity.getAccomClassID(), cpDecisionsByArrivalDateAndAccomClass, value.getLengthOfStay());
                    overrideEntity.setRateValue(optimalBar);
                } else {
                    overrideEntity.setRateValue(getCPDecisionBAROutput(overrideEntity.getAccomClassID(), overrideEntity.getArrivalDate(), value.getLengthOfStay()));
                }
            }
        } else {
            RateUnqualified rateUnqualified = getLowestPricePointRateForArrivalDemandBeta(forecast, rates);
            if (null == rateUnqualified) {
                rateUnqualified = getBestAvailableRateForArrivalDemandBeta(overrideEntity, decisionBAROutputs, maxLos, isQualifiedForecastGroup(forecastGroupList, overrideEntity));
            }
            overrideEntity.setRateUnqualified(rateUnqualified);
        }

        if (forecast != null && forecast.getRateForecast() != null) {
            overrideEntity.setRateForecast(forecast.getRateForecast());
        }
    }

    protected boolean isQualifiedForecastGroup(List<ForecastGroup> forecastGroupList, ArrivalDemandOverride overrideEntity) {
        if (!forecastGroupList.isEmpty() && null != overrideEntity) {
            ForecastGroup forecastGroup = forecastGroupList.stream().filter(fg -> fg.getId().equals(overrideEntity.getForecastGroupId())).findFirst().orElse(null);
            if (null != forecastGroup) {
                return forecastGroup.getForecastType().getId().equals(ForecastType.QUALIFIED_YIELDABLE_FORECAST_TYPE_ID) ||
                        forecastGroup.getForecastType().getId().equals(ForecastType.QUALIFIED_NON_YIELDABLE_FORECAST_TYPE_ID) ||
                        forecastGroup.getForecastType().getId().equals(ForecastType.QUALIFIED_SEMI_YIELDABLE_FORECAST_TYPE_ID);
            }
        }
        return false;
    }


    private RateUnqualified getLowestPricePointRateForArrivalDemandBeta(ArrivalDemandForecast arrivalDemandForecast, List<RateUnqualified> rates) {
        if (arrivalDemandForecast == null) {
            return null;
        }

        return rates.stream().filter(rate -> rate.getId().equals(arrivalDemandForecast.getRateUnqualifiedId())).findFirst().orElse(null);
    }


    private RateUnqualified getBestAvailableRateForOccupancyDemand(OccupancyDemandOverride overrideEntity, Map<Date, Map<Integer, List<DecisionBAROutput>>> decisionsByArrivalDateAndAccomClass, RateUnqualified defaultRateUnqualified) {
        Map<Integer, List<DecisionBAROutput>> decisionsByAccomClass = decisionsByArrivalDateAndAccomClass.get(overrideEntity.getOccupancyDate());
        RateUnqualified rateUnqualified = null;
        if (null != decisionsByAccomClass) {
            List<DecisionBAROutput> decisionBAROutputs = decisionsByAccomClass.get(overrideEntity.getAccomClassID());
            if (null != decisionBAROutputs) {
                rateUnqualified = decisionBAROutputs.stream()
                        .filter(barOutput -> barOutput.getLengthOfStay().equals(1))
                        .map(DecisionBAROutput::getRateUnqualified)
                        .findFirst()
                        .orElse(null);
                if (rateUnqualified == null) {
                    rateUnqualified = decisionBAROutputs.stream()
                            .filter(barOutput -> barOutput.getLengthOfStay().equals(-1))
                            .map(DecisionBAROutput::getRateUnqualified)
                            .findFirst()
                            .orElse(null);
                }
            }
        }
        return rateUnqualified != null ? rateUnqualified : defaultRateUnqualified;
    }

    private RateUnqualified getDefaultRateUnqualified() {
        return (RateUnqualified) crudService.findByNamedQuerySingleResult(RateUnqualified.DEFAULT_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).
                        parameters());
    }

    protected RateUnqualified getBestAvailableRateForArrivalDemandBeta(ArrivalDemandOverride overrideEntity, List<DecisionBAROutput> decisionBAROutputs, int maxLos, boolean isQualifiedForecastGroup) {
        //first try and find the decision that represents the specific LOS
        DecisionBAROutput bestAvailableRate = decisionBAROutputs.stream().filter(decisionBAROutput -> decisionBAROutput.getLengthOfStay().equals(overrideEntity.getLengthOfStay())).findFirst().orElse(null);

        //if we don't find a decision for that specific LOS, assume its a RateOfDay/BarByDay property meaning the LOS will be represented by -1
        if (bestAvailableRate == null) {
            bestAvailableRate = decisionBAROutputs.stream().filter(decisionBAROutput -> decisionBAROutput.getLengthOfStay().equals(-1)).findFirst().orElse(null);
        }

        //if we don't find a decision and the forecast group is a Qualified Forecast Group and LOS is > MaxLOS; set to Decision Bar Output for MaxLOS
        if (bestAvailableRate == null && isQualifiedForecastGroup && overrideEntity.getLengthOfStay() > maxLos) {
            bestAvailableRate = decisionBAROutputs.stream().filter(decisionBAROutput -> decisionBAROutput.getLengthOfStay().equals(maxLos)).findFirst().orElse(null);
        }

        if (bestAvailableRate != null) {
            return bestAvailableRate.getRateUnqualified();
        }
        return null;
    }

    public BigDecimal getCPDecisionBAROutput(Integer accomClassID, Date arrivalDate, Integer lengthOfStay) {
        Product product = crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
        BigDecimal optimalRate = BigDecimalUtil.convert(crudService.findByNamedQuerySingleResult(CPDecisionBAROutput.GET_OPTIMAL_BAR_FOR_BASE_ROOM_TYPE, CPDecisionBAROutput.params(product, accomClassID, arrivalDate, lengthOfStay)));
        if (optimalRate == null) {
            return BigDecimalUtil.convert(crudService.findByNamedQuerySingleResult(CPDecisionBAROutput.GET_OPTIMAL_BAR_FOR_BASE_ROOM_TYPE, CPDecisionBAROutput.params(product, accomClassID, arrivalDate, -1)));
        }

        return optimalRate;
    }

    public List<CPDecisionBAROutput> getCPDecisionBAROutputs(Set<Integer> accomClassIDs, Set<Date> selectedDates) {
        if (CollectionUtils.isEmpty(accomClassIDs) || CollectionUtils.isEmpty(selectedDates)) {
            return new ArrayList<>();
        }
        Product product = crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
        Set<LocalDate> selectedArrivalDates = selectedDates.stream().map(LocalDate::fromDateFields).collect(Collectors.toSet());
        return crudService.findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_FOR_BASE_ROOM_TYPE_FOR_ACCOMCLASS_LIST,
                QueryParameter.with(PRODUCT, product)
                        .and(ACCOM_CLASS_IDS, accomClassIDs)
                        .and("selectedDates", selectedArrivalDates)
                        .parameters());
    }

    private List<DecisionBAROutput> getDecisionBAROutputs(Set<Integer> accomClassIDs, Set<Date> selectedDates) {
        if (CollectionUtils.isEmpty(accomClassIDs) || CollectionUtils.isEmpty(selectedDates)) {
            return new ArrayList<>();
        }
        return crudService.findByNamedQuery(DecisionBAROutput.BY_DATELIST_AND_ACCOMCLASSIDLIST,
                QueryParameter.with(ACCOM_CLASS_IDS, accomClassIDs).
                        and(ARRIVAL_DATES, selectedDates).
                        and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .parameters());
    }

    public Map<DemandOverride, Set<DemandOverride>> getConflicts(Set<DemandOverride> overrides) {
        Map<DemandOverride, Set<DemandOverride>> conflicts = new HashMap<>();
        conflicts.putAll(getInternalConflicts(overrides));

        findConflictsEfficiently(overrides, conflicts);

        return conflicts;
    }

    private void findConflictsEfficiently(Set<DemandOverride> overrides, Map<DemandOverride, Set<DemandOverride>> conflicts) {
        if (CollectionUtils.isEmpty(overrides)) {
            return;
        }
        Date startDate = overrides.stream().map(DemandOverride::getOccupancyDate).min(Date::compareTo).get();
        Date endDate = overrides.stream().map(DemandOverride::getOccupancyDate).max(Date::compareTo).get();
        int maxLos = Integer.parseInt(configParamService.getParameterValue(MAX_LOS));
        LocalDate startDateMinusMaxLOS = new LocalDate(startDate).minusDays(maxLos);
        LocalDate endDatePlusMaxLOS = new LocalDate(endDate).plusDays(maxLos);
        List<Integer> accomClassIds = overrides.stream().map(DemandOverride::getAccomClassId).distinct().collect(Collectors.toList());
        List<Integer> forecastGroupIds = overrides.stream().map(DemandOverride::getForecastGroupId).distinct().collect(Collectors.toList());
        List<OccupancyDemandOverride> existingOccupancyOverridesForDateRange = getExistingOccupancyOverridesForDateRange(startDateMinusMaxLOS.toDate(), endDatePlusMaxLOS.toDate(), forecastGroupIds, accomClassIds);
        Map<Date, List<OccupancyDemandOverride>> occDemandOverridesByOccupancyDate = existingOccupancyOverridesForDateRange.stream().collect(Collectors.groupingBy(OccupancyDemandOverride::getOccupancyDate, Collectors.toList()));

        List<ArrivalDemandOverride> arrivalDemandOverrides = crudService.findByNamedQuery(ArrivalDemandOverride.BY_ARRIVAL_DATE_RANGE_AND_PROPERTY_ID_AND_FORECAST_GROUP_IDS_AND_ACCOM_CLASS_IDS,
                QueryParameter.with(ACCOM_CLASS_IDS, accomClassIds).
                        and(FORECAST_GROUP_IDS, forecastGroupIds).
                        and(START_DATE, startDateMinusMaxLOS.toDate()).
                        and(END_DATE, endDatePlusMaxLOS.toDate()).
                        and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).
                        parameters());
        List<Integer> userIDs = getIds(arrivalDemandOverrides, ArrivalDemandOverride::getCreatedByUserId);
        List<User> users = new ArrayList<>();
        if (!userIDs.isEmpty()) {
            users = crudService.findByNamedQuery(User.BY_IDS, QueryParameter.with("ids", userIDs).parameters());
        }
        Set<Integer> rateUnqualifiedIds = new HashSet<>();
        arrivalDemandOverrides.stream().forEach(ado -> {
            if (ado.getRateUnqualified() != null && ado.getRateUnqualified().getId() != null) {
                rateUnqualifiedIds.add(ado.getRateUnqualified().getId());
            }
        });
        List<RateUnqualified> rateUnqalifieds = new ArrayList<>();
        if (!rateUnqualifiedIds.isEmpty()) {
            rateUnqalifieds = crudService.findByNamedQuery(RateUnqualified.BY_IDS, QueryParameter.with("ids", rateUnqualifiedIds).parameters());
        }
        for (DemandOverride override : overrides) {
            Set<DemandOverride> externalConflicts = getConflicts(override, occDemandOverridesByOccupancyDate, arrivalDemandOverrides, users, rateUnqalifieds);
            externalConflicts = filterPendingInactiveOverrides(externalConflicts, overrides);
            if (CollectionUtils.isNotEmpty(externalConflicts)) {
                Set<DemandOverride> internalConflicts = conflicts.get(override);
                if (internalConflicts != null) {
                    externalConflicts.addAll(internalConflicts);
                }
                conflicts.put(override, externalConflicts);
            }
        }
    }

    private Set<DemandOverride> filterPendingInactiveOverrides(
            Set<DemandOverride> externalConflicts, Set<DemandOverride> overrides) {
        if (externalConflicts == null) {
            return null;
        }
        Set<DemandOverride> filteredConflicts = new HashSet<>();
        Set<Integer> pendingInactiveOverrideIds = buildPendingInactiveOverrideIds(overrides);
        for (DemandOverride conflict : externalConflicts) {
            if (!isPendingInactive(conflict, pendingInactiveOverrideIds)) {
                filteredConflicts.add(conflict);
            }
        }
        return filteredConflicts;
    }

    private boolean isPendingInactive(DemandOverride conflict,
                                      Set<Integer> pendingInactiveOverrideIds) {
        return pendingInactiveOverrideIds.containsAll(conflict.getIds());
    }

    private Set<Integer> buildPendingInactiveOverrideIds(Set<DemandOverride> overrides) {
        Set<Integer> pendingInactiveOverrideIds = new HashSet<>();
        for (DemandOverride override : overrides) {
            if (!override.getIds().isEmpty()) {
                pendingInactiveOverrideIds.addAll(override.getIds());
            }
        }
        return pendingInactiveOverrideIds;
    }

    private Map<DemandOverride, Set<DemandOverride>> getInternalConflicts(Set<DemandOverride> overrides) {
        Map<DemandOverride, Set<DemandOverride>> allConflicts = new HashMap<>();
        Set<DemandOverride> overridesCopy = new HashSet<>();
        overridesCopy.addAll(overrides);
        for (DemandOverride override : overrides) {
            Set<DemandOverride> conflicts = getConflicts(override, overridesCopy);
            if (CollectionUtils.isNotEmpty(conflicts)) {
                allConflicts.put(override, conflicts);
            }
        }
        return allConflicts;
    }

    protected Set<DemandOverride> getConflicts(DemandOverride override,
                                               Set<DemandOverride> overrides) {
        Set<DemandOverride> conflicts = new HashSet<>();
        for (DemandOverride potentialConflict : overrides) {
            if (override.conflictsWith(potentialConflict)) {
                conflicts.add(potentialConflict);
            }
        }
        return conflicts;
    }


    public Set<DemandOverride> getConflicts(DemandOverride override, Map<Date, List<OccupancyDemandOverride>> existingOccupancyOverridesByOccupancyDate, List<ArrivalDemandOverride> existingArrivalDemandOverrides, List<User> users, List<RateUnqualified> rateUnqualifieds) {
        if (!override.isActive()) {
            return null;
        }
        if (override instanceof com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride) {
            com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride occupancyOverride = (com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride) override;
            List<OccupancyDemandOverride> occupancyDemandOverridesForOccupancyDate = existingOccupancyOverridesByOccupancyDate.get(occupancyOverride.getOccupancyDate());
            return getConflictsForOccupancyOverride(occupancyOverride.getOccupancyDate(), occupancyOverride.getForecastGroupId(), occupancyOverride.getAccomClassId(), occupancyOverride.getId(), occupancyDemandOverridesForOccupancyDate, existingArrivalDemandOverrides, users, rateUnqualifieds);
        } else if (override instanceof com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride) {
            com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride arrivalOverride = (com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride) override;
            Set<DemandOverride> conflicts = new HashSet<>();
            for (ArrivalOverrideValue value : arrivalOverride.getActiveValues()) {

                Set<DemandOverride> lengthOfStayConflicts = getConflictsForArrivalOverride(arrivalOverride, value, existingArrivalDemandOverrides, existingOccupancyOverridesByOccupancyDate);

                addOverridesToSet(lengthOfStayConflicts, conflicts);
            }
            return conflicts;
        }
        return null;
    }

    private void addOverridesToSet(Set<DemandOverride> overrides,
                                   Set<DemandOverride> set) {
        for (DemandOverride override : overrides) {
            if (!setContainsOverride(set, override)) {
                set.add(override);
            }
        }
    }

    private boolean setContainsOverride(Set<DemandOverride> set,
                                        DemandOverride override) {
        for (DemandOverride candidate : set) {
            if (candidate.matches(override)) {
                return true;
            }
        }
        return false;
    }

    private Integer getCurrentUser() {
        return Integer.valueOf(PacmanWorkContextHelper.getWorkContext().getUserId());
    }


    private Set<DemandOverride> getConflictsForOccupancyOverride(Date occupancyDate, Integer forecastGroupId, Integer accomClassId, Integer existingOverrideId, List<OccupancyDemandOverride> existingOccOverridesForDate, List<ArrivalDemandOverride> arrivalDemandOverrides, List<User> users, List<RateUnqualified> rateUnqualifieds) {
        Set<DemandOverride> conflicts = new HashSet<>();
        // get (as an entity) any existing Occupancy Override that conflicts
        OccupancyDemandOverride existingOverride = null;
        if (null != existingOccOverridesForDate) {
            existingOverride = existingOccOverridesForDate.stream()
                    .filter(over -> over.getForecastGroupId().equals(forecastGroupId))
                    .filter(over -> over.getAccomClassID().equals(accomClassId))
                    .findFirst()
                    .orElse(null);
        }

        if (existingOverride != null && (existingOverrideId == null || !existingOverride.getId().equals(existingOverrideId))) {
            // convert the entity to a DTO and add to the list of conflicts
            conflicts.add(new com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride(existingOverride));
        }
        // get (as a list of DTO's) any existing Arrival Overrides that conflict
        Set<com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride> existingOverrides = getExistingArrivalOverrides(occupancyDate, forecastGroupId, accomClassId, arrivalDemandOverrides, users, rateUnqualifieds);
        if (existingOverrides != null) {
            for (com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride override : existingOverrides) {
                conflicts.add(override);
            }
        }
        return conflicts;
    }


    @SuppressWarnings("unchecked")
    private Set<com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride> getExistingArrivalOverrides(
            Date occupancyDate, Integer forecastGroupId, Integer accomClassId, List<ArrivalDemandOverride> arrivalDemandOverrides, List<User> users, List<RateUnqualified> rateUnqualifieds) {

        Set<com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride> conflicts = new HashSet<>();
        if (occupancyDate == null || forecastGroupId == null || accomClassId == null) {
            return conflicts;
        }

        Predicate<ArrivalDemandOverride> arrivalDemandOverridePredicate = override ->
                override.getAccomClassID().equals(accomClassId)
                        && override.getForecastGroupId().equals(forecastGroupId)
                        && (override.getArrivalDate().equals(occupancyDate)
                        || (override.getLengthOfStay() > 0
                        && override.getArrivalDate().before(occupancyDate)
                        && getDateWithLOS(override).after(occupancyDate)
                )
                );
        List<ArrivalDemandOverride> filteredArrivalDemandOverrides = arrivalDemandOverrides.stream().filter(arrivalDemandOverridePredicate)
                .collect(Collectors.toList());

        for (ArrivalDemandOverride arrivalDemandOverride : filteredArrivalDemandOverrides) {
            Date arrivalDate = arrivalDemandOverride.getArrivalDate();

            // call a helper method to determine if we have already encountered
            // another LOS value for this same Override
            com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride override = getExistingArrivalOverride(conflicts, arrivalDate);
            if (override == null) { // this is the first LOS for this override
                override = new com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride();
                override.setAccomClassId(accomClassId);
                override.setForecastGroupId(forecastGroupId);
                override.setArrivalDate(arrivalDate);
                User user = users.stream().filter(u -> u.getId().equals(arrivalDemandOverride.getCreatedByUserId())).findFirst().orElse(null);
                override.setUser(user != null ? user.getName() : "");
                override.setDatePerformed(LocalDateUtils.toDate(arrivalDemandOverride.getCreateDate()));
                conflicts.add(override);
            }

            Integer id = arrivalDemandOverride.getId();
            Integer lengthOfStay = arrivalDemandOverride.getLengthOfStay();
            BigDecimal value = arrivalDemandOverride.getOverrideValue();
            BigDecimal remainingDemand = arrivalDemandOverride.getRemainingDemand();
            RateUnqualified rateUnqualified = rateUnqualifieds.stream().filter(ru -> ru.equals(arrivalDemandOverride.getRateUnqualified())).findFirst().orElse(null);
            String barSnapshot = rateUnqualified != null ? rateUnqualified.getName() : "";
            override.addValue(id, lengthOfStay, value, remainingDemand, barSnapshot);
        }
        return conflicts;
    }

    private Date getDateWithLOS(ArrivalDemandOverride override) {
        LocalDate arrivalDatePlusLOS = new LocalDate(override.getArrivalDate()).plusDays(override.getLengthOfStay());
        return arrivalDatePlusLOS.toDate();
    }

    /*  Iterate through the existing ArrivalDemandOverride DTO's, looking for an
     * arrival date that matches the new override row.  If a match is found, return the DTO.
     *
     */
    private com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride getExistingArrivalOverride(
            Set<com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride> conflicts, Date arrivalDate) {
        if (conflicts == null || arrivalDate == null) {
            return null;
        }
        for (com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride conflict : conflicts) {
            if (arrivalDate.equals(conflict.getArrivalDate())) {
                return conflict;
            }
        }
        return null;
    }


    /*  Given the arrival date, LOS, forecast group, and accommodation class of a
     * proposed new Arrival Override, returns a list of any exiting overrides
     * that conflict with (and therefore prevent the creation of)
     * the proposed new Arrival Override.
     *
     * First, check if there is an existing Arrival Override that conflicts with
     * the new override.  In order for an existing Arrival Override to conflict
     * with a new Arrival Override, it must have the same Property ID,
     * Forecast Group, Accom Class, Arrival Date, and LOS.
     *
     * Next, check if there are any existing Occupancy Overrides that conflict with
     * the new override.  There can be more than one, because of multi-day LOS's.
     * Specifically, in order for an existing Occupancy Override to conflict with
     * a new Arrival Override, they must share the same Property ID, FG, and AC.
     * In addition, the Occupancy Override's Occupancy Date must be greater than or equal
     * to the Arrival Date of the Arrival Override, and must be less than the
     * Arrival Date of the Arrival Override added to the LOS.  For example,
     * an Arrival Override for January 10 with LOS 3 would conflict with
     * Occupancy Overrides for January 10, 11, or 12 (assuming the Property
     * ID's, FG's and AC's matched).
     *
     * Note that this method returns a list of ArrivalDemandOverride and
     * OccupancyDemandOverride DTO's.  Note that these are not the same as
     * ArrivalDemandOverride and OccupancyDemandOverride entities.  Because
     * the entities and DTO's share a common class name, the DTO's include
     * the full package name.
     *
     * Since NamedQueries return entities, these are converted to DTO's using
     * DTO constructors that take as arguments the corresponding entities.
     *
     */
    private Set<DemandOverride> getConflictsForArrivalOverride(com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride arrivalOverride, ArrivalOverrideValue value, List<ArrivalDemandOverride> existingArrivalDemandOverrides, Map<Date, List<OccupancyDemandOverride>> existingOccupancyOverridesByOccupancyDate) {
        Set<DemandOverride> conflicts = getArrivalDemandOverrideConflicts(arrivalOverride, value, existingArrivalDemandOverrides);
        addOccupancyDemandOverrideConflicts(arrivalOverride, value, existingOccupancyOverridesByOccupancyDate, conflicts);
        return conflicts;
    }

    private void addOccupancyDemandOverrideConflicts(com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride arrivalOverride, ArrivalOverrideValue value, Map<Date, List<OccupancyDemandOverride>> existingOccupancyOverridesByOccupancyDate, Set<DemandOverride> conflicts) {
        Date arrivalDate = arrivalOverride.getArrivalDate();
        Integer forecastGroupId = arrivalOverride.getForecastGroupId();
        Integer accomClassId = arrivalOverride.getAccomClassId();
        Integer lengthOfStay = value.getLengthOfStay();

        Date startDate = arrivalDate;
        Calendar endDate = Calendar.getInstance();
        endDate.setTime(startDate);
        endDate.add(Calendar.DAY_OF_MONTH, lengthOfStay);
        List<OccupancyDemandOverride> existingOccupancyOverrides =
                existingOccupancyOverridesByOccupancyDate.entrySet()
                        .stream()
                        .filter(entry -> (entry.getKey().equals(startDate) || entry.getKey().after(startDate))
                                && (entry.getKey().before(endDate.getTime()))
                        )
                        .flatMap(entry -> entry.getValue().stream())
                        .filter(occupancyDemandOverride -> occupancyDemandOverride.getAccomClassID().equals(accomClassId))
                        .filter(occupancyDemandOverride -> occupancyDemandOverride.getForecastGroupId().equals(forecastGroupId))
                        .collect(Collectors.toList());

        for (OccupancyDemandOverride override : existingOccupancyOverrides) {
            conflicts.add(new com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride(override));
        }
    }

    private Set<DemandOverride> getArrivalDemandOverrideConflicts(com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride arrivalOverride, ArrivalOverrideValue value, List<ArrivalDemandOverride> existingArrivalDemandOverrides) {
        Date arrivalDate = arrivalOverride.getArrivalDate();
        Integer forecastGroupId = arrivalOverride.getForecastGroupId();
        Integer accomClassId = arrivalOverride.getAccomClassId();
        Integer lengthOfStay = value.getLengthOfStay();
        Integer existingOverrideId = value.getId();

        Set<DemandOverride> conflicts = new HashSet<>();
        // get (as an entity) any existing Arrival Override that conflicts
        Predicate<ArrivalDemandOverride> accomClassIdForecastGroupIdAndArrivalDatePredicate = override ->
                override.getAccomClassID().equals(accomClassId)
                        && override.getForecastGroupId().equals(forecastGroupId)
                        && override.getArrivalDate().equals(arrivalDate);

        Predicate<ArrivalDemandOverride> losPredicate = override ->
                override.getLengthOfStay().equals(lengthOfStay);

        ArrivalDemandOverride existingOverride = existingArrivalDemandOverrides.stream()
                .filter(accomClassIdForecastGroupIdAndArrivalDatePredicate.and(losPredicate))
                .findFirst()
                .orElse(null);

        if (existingOverride != null && (existingOverrideId == null || !existingOverride.getId().equals(existingOverrideId))) {
            // convert the entity to a DTO and add to the list of conflicts
            com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride arrivalOverrideDto = null;
            List<ArrivalDemandOverride> arrivalDemandOverrides = existingArrivalDemandOverrides.stream()
                    .filter(accomClassIdForecastGroupIdAndArrivalDatePredicate)
                    .collect(Collectors.toList());

            for (ArrivalDemandOverride overrideEntity : arrivalDemandOverrides) {
                if (arrivalOverrideDto == null) {
                    arrivalOverrideDto = new com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride(overrideEntity);
                } else {
                    arrivalOverrideDto.addValue(overrideEntity.getId(),
                            overrideEntity.getLengthOfStay(), overrideEntity.getOverrideValue(), overrideEntity.getRemainingDemand(),
                            overrideEntity.getRateUnqualified() != null ? overrideEntity.getRateUnqualified().getName() : "");
                }
            }
            conflicts.add(arrivalOverrideDto);
        }
        return conflicts;
    }


    public HashMap<DemandOverrideKey, com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride> getArrivalDemandOverrides(Date startDate, Date endDate, List<Integer> forecastGroupIds, List<Integer> roomClassIds) {
        List<ArrivalDemandOverride> arrivalDemandOverrideEntities = crudService.findByNamedQuery(ArrivalDemandOverride.BY_DATE_RANGE_AND_LENGHT_OF_STAYS_AND_PROPERTY_ID_AND_FORECAST_GROUP_IDS_AND_ACCOM_CLASSES,
                QueryParameter.with(START_DATE, startDate).
                        and(END_DATE, endDate).
                        and(FORECAST_GROUP_IDS, forecastGroupIds).
                        and(ACCOM_CLASS_IDS, roomClassIds).
                        and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).
                        parameters());

        Map<DemandOverrideKey, List<ArrivalDemandOverride>> overridesGrouped = new HashMap<>();

        //arrivalDemandOverrideEntities will contain records across multiple dates, room classes and length of stays for the passed in forecast group
        //we need to group them together, so we can convert them to the ArrivalDemandOverride dto objects
        for (ArrivalDemandOverride entity : arrivalDemandOverrideEntities) {
            DemandOverrideKey key = new DemandOverrideKey(entity.getAccomClassID(), entity.getForecastGroupId(), entity.getArrivalDate());
            List<ArrivalDemandOverride> arrivalDemandOverrides = overridesGrouped.get(key);
            if (arrivalDemandOverrides == null) {
                arrivalDemandOverrides = new ArrayList<>();
            }
            arrivalDemandOverrides.add(entity);
            overridesGrouped.put(key, arrivalDemandOverrides);
        }

        //now convert the values in our map to ArrivalDemandOverride dtos
        HashMap<DemandOverrideKey, com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride> arrivalDemandOverrides = new HashMap<>();
        for (Map.Entry<DemandOverrideKey, List<ArrivalDemandOverride>> entry : overridesGrouped.entrySet()) {
            DemandOverrideKey key = entry.getKey();
            List<ArrivalDemandOverride> overrideEntities = entry.getValue();
            com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride arrivalDemandOverride = buildArrivalDemandOverride(overrideEntities);
            if (arrivalDemandOverride != null) {
                arrivalDemandOverrides.put(key, arrivalDemandOverride);
            }
        }
        return arrivalDemandOverrides;
    }

    @SuppressWarnings("unchecked")
    private com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride getArrivalDemandOverride(Date arrivalDate, Integer forecastGroupId, Integer accomClassId) {

        List<ArrivalDemandOverride> arrivalOverrides = crudService.findByNamedQuery(ArrivalDemandOverride.BY_ARRIVAL_DATE_AND_PROPERTY_ID_AND_FORECAST_GROUP_ID_AND_ACCOM_CLASS_ID,
                QueryParameter.with(ARRIVAL_DATE, arrivalDate).
                        and(FORECAST_GROUP_ID, forecastGroupId).
                        and(ACCOM_CLASS_ID, accomClassId).
                        and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).
                        parameters());
        com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride arrivalOverride = null;
        if (CollectionUtils.isNotEmpty(arrivalOverrides)) {
            for (ArrivalDemandOverride overrideEntity : arrivalOverrides) {
                if (arrivalOverride == null) {
                    arrivalOverride = new com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride(overrideEntity);
                } else {
                    arrivalOverride.addValue(overrideEntity.getId(),
                            overrideEntity.getLengthOfStay(), overrideEntity.getOverrideValue(), overrideEntity.getRemainingDemand(),
                            overrideEntity.getRateUnqualified() != null ? overrideEntity.getRateUnqualified().getName() : "");
                }
            }
        }
        return arrivalOverride;
    }

    @SuppressWarnings("unchecked")
    private List<com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride> getArrivalDemandOverrides(Date arrivalDate, Integer forecastGroupId) {
        List<com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride> arrivalDemandOverrides = new ArrayList<>();
        List<ArrivalDemandOverride> arrivalOverrides = crudService.findByNamedQuery(ArrivalDemandOverride.BY_ARRIVAL_DATE_AND_PROPERTY_ID_AND_FORECAST_GROUP_ID,
                QueryParameter.with(ARRIVAL_DATE, arrivalDate).
                        and(FORECAST_GROUP_ID, forecastGroupId).
                        and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).
                        parameters());
        return convertArrivalDemandOverrides(arrivalDemandOverrides, arrivalOverrides);
    }

    private List<com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride> convertArrivalDemandOverrides(List<com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride> arrivalDemandOverrides, List<ArrivalDemandOverride> arrivalOverrides) {
        com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride arrivalOverride = null;
        for (ArrivalDemandOverride overrideEntity : arrivalOverrides) {
            if (arrivalOverride == null) {
                arrivalOverride = new com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride(overrideEntity);
            } else {
                arrivalOverride.addValue(overrideEntity.getId(),
                        overrideEntity.getLengthOfStay(), overrideEntity.getOverrideValue(), overrideEntity.getRemainingDemand(),
                        overrideEntity.getRateUnqualified() != null ? overrideEntity.getRateUnqualified().getName() : "");
            }
            arrivalDemandOverrides.add(arrivalOverride);
        }
        return arrivalDemandOverrides;
    }


    public List<OccupancyDemandOverride> getExistingOccupancyOverridesForDateRange(Date startDate, Date endDate, List<Integer> forecastGroupIds, List<Integer> accomClassIds) {
        if (null == startDate || null == endDate || CollectionUtils.isEmpty(forecastGroupIds) || CollectionUtils.isEmpty(accomClassIds)) {
            return new ArrayList<>();
        }

        return crudService.findByNamedQuery(OccupancyDemandOverride.BY_OCCUPANCY_DATE_RANGE_AND_PROPERTY_ID_AND_FORECAST_GROUP_IDS_AND_ACCOM_CLASS_IDS,
                QueryParameter.with(START_DATE, startDate)
                        .and(END_DATE, endDate)
                        .and(FORECAST_GROUP_IDS, forecastGroupIds)
                        .and(ACCOM_CLASS_IDS, accomClassIds)
                        .and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .parameters());
    }

    public DemandOverride getExistingOverride(Date occupancyDate, Integer forecastGroupId, Integer accomClassId) {
        if (occupancyDate == null || forecastGroupId == null || accomClassId == null) {
            return null;
        }
        OccupancyDemandOverride occupancyOverride = crudService.findByNamedQuerySingleResult(OccupancyDemandOverride.BY_OCCUPANCY_DATE_AND_PROPERTY_ID_AND_FORECAST_GROUP_ID_AND_ACCOM_CLASS_ID,
                QueryParameter.with(OCCUPANCY_DATE, occupancyDate).
                        and(FORECAST_GROUP_ID, forecastGroupId).
                        and(ACCOM_CLASS_ID, accomClassId).
                        and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).
                        parameters());
        if (occupancyOverride != null) {
            return new com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride(occupancyOverride);
        }
        return getArrivalDemandOverride(occupancyDate, forecastGroupId, accomClassId);
    }

    public Integer findActiveOccupancyDemandOverridesCountFor(LocalDate occupancyDate, List<Integer> forecastGroupIds) {
        return crudService.findByNamedQuerySingleResult(OccupancyDemandOverride.FIND_ACTIVE_OVERRIDES_COUNT_BY_OCCUPANCY_DATE_AND_FORECAST_GROUP_IDS,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and(OCCUPANCY_DATE, occupancyDate)
                        .and(FORECAST_GROUP_IDS, forecastGroupIds).parameters());
    }

    public List<OccupancyDemandOverride> findActiveOccupancyDemandOverridesFor(LocalDate occupancyDate, List<Integer> forecastGroupIds) {
        return crudService.findByNamedQuery(OccupancyDemandOverride.FIND_ACTIVE_OVERRIDES_BY_OCCUPANCY_DATE_AND_FORECAST_GROUP_IDS,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and(OCCUPANCY_DATE, occupancyDate)
                        .and(FORECAST_GROUP_IDS, forecastGroupIds).parameters());
    }

    public List<DemandOverride> getExistingOverrides(Date occupancyDate, Integer forecastGroupId) {
        List<DemandOverride> demandOverrides = new ArrayList<>();
        if (occupancyDate == null || forecastGroupId == null) {
            return demandOverrides;
        }
        List<OccupancyDemandOverride> occupancyOverrides = crudService.findByNamedQuery(OccupancyDemandOverride.BY_OCCUPANCY_DATE_AND_PROPERTY_ID_AND_FORECAST_GROUP_ID,
                QueryParameter.with(OCCUPANCY_DATE, occupancyDate).
                        and(FORECAST_GROUP_ID, forecastGroupId).
                        and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).
                        parameters());
        if (isNotEmpty(occupancyOverrides)) {
            for (OccupancyDemandOverride occupancyOverride : occupancyOverrides) {
                demandOverrides.add(new com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride(occupancyOverride));
            }
            return demandOverrides;
        }
        for (com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride arrivalDemandOverride : getArrivalDemandOverrides(occupancyDate, forecastGroupId)) {
            demandOverrides.add(arrivalDemandOverride);
        }
        return demandOverrides;
    }


    /**
     * @param occupancyDate
     * @param forecastGroupId
     * @param accomClassId
     * @return
     * @deprecated
     */
    @Deprecated
    public DemandOverride getExistingOverride(DateParameter occupancyDate, Integer forecastGroupId, Integer accomClassId) {
        return getExistingOverride(occupancyDate.getTime(), forecastGroupId, accomClassId);
    }

    public DemandOverride getExistingOverride(LocalDate occupancyDate, Integer forecastGroupId, Integer accomClassId) {
        return getExistingOverride(occupancyDate.toDate(), forecastGroupId, accomClassId);
    }

    @SuppressWarnings("unchecked")
    public Set<DemandOverride> getExistingOverrides(Set<Date> occupancyDates, Integer forecastGroupId, Integer accomClassId) {
        if (CollectionUtils.isEmpty(occupancyDates) || forecastGroupId == null || forecastGroupId == -1) {
            return null;
        }
        Set<DemandOverride> overrides = new HashSet<>();
        List<OccupancyDemandOverride> occupancyEntities = findOccupancyOverrides(occupancyDates, forecastGroupId, accomClassId);
        if (occupancyEntities != null) {
            for (OccupancyDemandOverride entity : occupancyEntities) {
                overrides.add(new com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride(entity));
            }
        }
        List<ArrivalDemandOverride> arrivalEntities = findArrivalDemandOverrides(occupancyDates, forecastGroupId, accomClassId);
        if (arrivalEntities == null) {
            return overrides;
        }
        Date currentDate = null;
        Integer currentAccomClassId = null;
        com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride arrivalOverride = null;
        for (ArrivalDemandOverride entity : arrivalEntities) {
            Integer newAccomClassId = entity.getAccomClassID();
            Date newDate = entity.getArrivalDate();
            if (currentDate == null || !currentDate.equals(newDate) || !currentAccomClassId.equals(newAccomClassId)) {
                currentDate = newDate;
                currentAccomClassId = newAccomClassId;
                if (arrivalOverride != null) {
                    overrides.add(arrivalOverride);
                }
                arrivalOverride = new com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride(entity);
            } else {
                arrivalOverride.addValue(entity.getId(),
                        entity.getLengthOfStay(), entity.getOverrideValue(), entity.getRemainingDemand(),
                        entity.getRateUnqualified() != null ? entity.getRateUnqualified().getName() : "");
            }
        }
        if (arrivalOverride != null) {
            overrides.add(arrivalOverride);
        }
        return overrides;
    }

    private List<ArrivalDemandOverride> findArrivalDemandOverrides(Set<Date> occupancyDates, Integer forecastGroupId, Integer accomClassId) {
        List<ArrivalDemandOverride> arrivalEntities;
        if (accomClassId == null || accomClassId == -1) {
            arrivalEntities =
                    crudService.findByNamedQuery(ArrivalDemandOverride.BY_ARRIVAL_DATES_AND_PROPERTY_ID_AND_FORECAST_GROUP_ID,
                            QueryParameter.with(ARRIVAL_DATES, occupancyDates).
                                    and(FORECAST_GROUP_ID, forecastGroupId).
                                    and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).
                                    parameters());

        } else {
            arrivalEntities =
                    crudService.findByNamedQuery(ArrivalDemandOverride.BY_ARRIVAL_DATES_AND_PROPERTY_ID_AND_FORECAST_GROUP_ID_AND_ACCOM_CLASS_ID,
                            QueryParameter.with(ARRIVAL_DATES, occupancyDates).
                                    and(FORECAST_GROUP_ID, forecastGroupId).
                                    and(ACCOM_CLASS_ID, accomClassId).
                                    and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).
                                    parameters());
        }
        return arrivalEntities;
    }

    private List<OccupancyDemandOverride> findOccupancyOverrides(Set<Date> occupancyDates, Integer forecastGroupId, Integer accomClassId) {
        List<OccupancyDemandOverride> occupancyEntities;
        if (accomClassId == null || accomClassId == -1) {
            occupancyEntities =
                    crudService.findByNamedQuery(OccupancyDemandOverride.BY_OCCUPANCY_DATES_AND_PROPERTY_ID_AND_FORECAST_GROUP_ID,
                            QueryParameter.with(OCCUPANCY_DATES, occupancyDates).
                                    and(FORECAST_GROUP_ID, forecastGroupId).
                                    and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).
                                    parameters());

        } else {
            occupancyEntities =
                    crudService.findByNamedQuery(OccupancyDemandOverride.BY_OCCUPANCY_DATES_AND_PROPERTY_ID_AND_FORECAST_GROUP_ID_AND_ACCOM_CLASS_ID,
                            QueryParameter.with(OCCUPANCY_DATES, occupancyDates).
                                    and(FORECAST_GROUP_ID, forecastGroupId).
                                    and(ACCOM_CLASS_ID, accomClassId).
                                    and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).
                                    parameters());
        }
        return occupancyEntities;
    }

    public WashOverride getExistingWashOverride(Date occupancyDate, Integer forecastGroupId) {
        if (occupancyDate == null || forecastGroupId == null) {
            return null;
        }
        com.ideas.tetris.pacman.services.demandoverride.entity.WashOverride washOverride =
                crudService.findByNamedQuerySingleResult(com.ideas.tetris.pacman.services.demandoverride.entity.WashOverride.BY_OCCUPANCY_DATE_AND_PROPERTY_ID_AND_FORECAST_GROUP_ID,
                        QueryParameter.with(OCCUPANCY_DATE, occupancyDate).
                                and(FORECAST_GROUP_ID, forecastGroupId).
                                and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).
                                parameters());
        if (washOverride != null) {
            return new WashOverride(washOverride);
        }
        return null;
    }

    /**
     * @param occupancyDate
     * @param forecastGroupId
     * @return
     * @deprecated
     */
    @Deprecated
    public WashOverride getExistingWashOverride(DateParameter occupancyDate, Integer forecastGroupId) {
        return getExistingWashOverride(occupancyDate.getTime(), forecastGroupId);
    }


    public void saveWashOverrides(Set<WashOverride> overrides) {
        saveWashOverrides(overrides, null);
    }

    public void saveWashOverrides(Set<WashOverride> overrides, Decision decision) {
        if (CollectionUtils.isEmpty(overrides)) {
            return;
        }
        Set<WashOverride> removals = getWashRemovals(overrides);
        for (WashOverride removal : removals) {
            removeOverride(removal);
        }
        Set<WashOverride> creates = getWashCreates(overrides);
        for (WashOverride create : creates) {
            decision = saveOverride(create, decision);
        }
    }

    private Set<WashOverride> getWashRemovals(Set<WashOverride> overrides) {
        Set<WashOverride> removals = new HashSet<>();
        for (WashOverride override : overrides) {
            if (override.getId() != null) {
                removals.add(override);
            }
        }
        return removals;
    }

    private Set<WashOverride> getWashCreates(Set<WashOverride> overrides) {
        Set<WashOverride> creates = new HashSet<>();
        for (WashOverride override : overrides) {
            if (override.isActive()) {
                creates.add(override);
            }
        }
        return creates;
    }


    private void removeOverride(WashOverride override) {
        com.ideas.tetris.pacman.services.demandoverride.entity.WashOverride overrideEntity =
                crudService.find(com.ideas.tetris.pacman.services.demandoverride.entity.WashOverride.class, override.getId());
        overrideEntity.remove();
        updateUserWash(overrideEntity);
        overrideEntity.setLastUpdatedByUserId(Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        overrideEntity.setLastUpdatedDate(new Date());
        crudService.save(overrideEntity);
    }

    private Decision saveOverride(WashOverride override, Decision decision) {
        if (override == null) {
            return null;
        }
        if (conflictsWithExistingWashOverride(override)) {
            return null;
        }
        if (decision == null) {
            decision = decisionService.createWashOverrideDecision();
            decision = crudService.save(decision);
        }
        com.ideas.tetris.pacman.services.demandoverride.entity.WashOverride overrideEntity = new com.ideas.tetris.pacman.services.demandoverride.entity.WashOverride();
        overrideEntity.setDecisionID(decision.getId());
        overrideEntity.setForecastGroupId(override.getForecastGroupId());
        overrideEntity.setPropertyID(PacmanWorkContextHelper.getPropertyId());
        overrideEntity.setOccupancyDate(override.getOccupancyDate());
        overrideEntity.setExpirationDate(override.getExpirationDate());
        overrideEntity.setOverrideValue(override.getValue());
        overrideEntity.setSystemWash(override.getSystemWash());
        overrideEntity.setStatusId(Constants.ACTIVE_STATUS_ID);
        overrideEntity.setCreatedByUserId(getCurrentUser());
        overrideEntity.setLastUpdatedByUserId(getCurrentUser());
        overrideEntity.setCreateDate(new Date());
        overrideEntity.setLastUpdatedDate(new Date());
        updateUserWash(overrideEntity);
        crudService.save(overrideEntity);
        return decision;
    }

    private boolean conflictsWithExistingWashOverride(WashOverride override) {
        WashOverride conflict = getExistingWashOverride(override.getOccupancyDate(), override.getForecastGroupId());
        return conflict != null;
    }

    private void updateUserWash(com.ideas.tetris.pacman.services.demandoverride.entity.WashOverride overrideEntity) {
        updateUserWashForecastGroupLevel(overrideEntity);
        updateUserWashAccomClassLevel(overrideEntity);
    }

    private void updateUserWashForecastGroupLevel(com.ideas.tetris.pacman.services.demandoverride.entity.WashOverride overrideEntity) {
        WashForecastForecastGroup forecast =
                crudService.findByNamedQuerySingleResult(WashForecastForecastGroup.BY_OCCUPANCY_DATE_AND_PROPERTY_ID_AND_FORECAST_GROUP_ID,
                        QueryParameter.with(OCCUPANCY_DATE, overrideEntity.getOccupancyDate()).
                                and(FORECAST_GROUP_ID, overrideEntity.getForecastGroupId()).
                                and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).
                                parameters());
        if (forecast == null) {
            return;
        }
        if (overrideEntity.getStatusId().equals(Constants.ACTIVE_STATUS_ID)) {
            forecast.setDecisionID(overrideEntity.getDecisionID());
            forecast.setUserWash(overrideEntity.getOverrideValue());
        } else {
            forecast.setUserWash(forecast.getSystemWash());
        }
        crudService.save(forecast);
    }

    @SuppressWarnings("unchecked")
    private void updateUserWashAccomClassLevel(com.ideas.tetris.pacman.services.demandoverride.entity.WashOverride overrideEntity) {
        List<WashForecast> forecasts =
                crudService.findByNamedQuery(WashForecast.BY_OCCUPANCY_DATE_AND_PROPERTY_ID_AND_FORECAST_GROUP_ID,
                        QueryParameter.with(OCCUPANCY_DATE, overrideEntity.getOccupancyDate()).
                                and(FORECAST_GROUP_ID, overrideEntity.getForecastGroupId()).
                                and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).
                                parameters());
        if (forecasts == null) {
            return;
        }
        for (WashForecast forecast : forecasts) {
            if (overrideEntity.getStatusId().equals(Constants.ACTIVE_STATUS_ID)) {
                forecast.setDecisionID(overrideEntity.getDecisionID());
                forecast.setUserWash(overrideEntity.getOverrideValue());
            } else {
                forecast.setUserWash(forecast.getSystemWash());
            }
            crudService.save(forecast);
        }
    }

    @SuppressWarnings("unchecked")
    public List<ForecastGroupWashOverride> getForecastGroupWashOverrides(Date occupancyDate) {
        if (null == occupancyDate) {
            return null;
        }
        List<ForecastGroupWashOverride> fgWashOverrideList = new ArrayList<>();
        List<Object[]> result = crudService.findByNamedQuery(
                OccupancyForecastDetailViewForCR.BY_OCCUPANCY_DATE_AND_PROPERTY_ID_CR,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).
                        and(OCCUPANCY_DATE, occupancyDate).parameters());
        for (Object[] row : result) {
            ForecastGroupWashOverride fgWashOverride = new ForecastGroupWashOverride();
            Integer forecastGroupId = (Integer) row[0];
            fgWashOverride.setForecastGroupId(forecastGroupId);
            fgWashOverride.setForecastGroupName((String) row[1]);
            fgWashOverride.setSystemWash((BigDecimal) row[2]);
            fgWashOverride.setOccupancyNumber((BigDecimal) row[4]);
            fgWashOverride.setRoomsSold((BigDecimal) row[5]);
            fgWashOverride.setRemainingDemand((BigDecimal) row[6]);
            fgWashOverride.setWashOverride(
                    getExistingWashOverride(occupancyDate, forecastGroupId));
            fgWashOverrideList.add(fgWashOverride);
            Integer forecastTypeId = (Integer) row[7];
            fgWashOverride.setWashOverrideAllowed(isWashOverrideAllowed(forecastTypeId));
        }
        Collections.sort(fgWashOverrideList, new ComparatorForecastGroupWashOverride());
        return fgWashOverrideList;
    }

    /**
     * @param occupancyDate
     * @return
     * @deprecated
     */
    @Deprecated
    public List<ForecastGroupWashOverride> getForecastGroupWashOverrides(DateParameter occupancyDate) {
        return getForecastGroupWashOverrides(occupancyDate.getTime());
    }


    @SuppressWarnings("unchecked")
    public ForecastGroupWashOverride getForecastGroupWashOverride(Date occupancyDate, Integer forecastGroupId) {
        if (null == occupancyDate || null == forecastGroupId) {
            return null;
        }
        List<Object[]> result = crudService.findByNamedQuery(
                OccupancyForecastDetailView.BY_OCCUPANCY_DATE_AND_PROPERTY_ID_AND_FORECAST_GROUP_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).
                        and(OCCUPANCY_DATE, occupancyDate).and(FORECAST_GROUP_ID, forecastGroupId).parameters());
        if (result == null || result.size() != 1) {
            return null;
        }
        Object[] row = result.get(0);
        ForecastGroupWashOverride fgWashOverride = new ForecastGroupWashOverride();
        fgWashOverride.setForecastGroupId(forecastGroupId);
        fgWashOverride.setForecastGroupName((String) row[1]);
        fgWashOverride.setSystemWash((BigDecimal) row[2]);
        fgWashOverride.setOccupancyNumber((BigDecimal) row[4]);
        fgWashOverride.setRoomsSold((BigDecimal) row[5]);
        fgWashOverride.setRemainingDemand((BigDecimal) row[6]);
        fgWashOverride.setWashOverride(
                getExistingWashOverride(occupancyDate, forecastGroupId));
        Integer forecastTypeId = (Integer) row[7];
        fgWashOverride.setWashOverrideAllowed(isWashOverrideAllowed(forecastTypeId));
        return fgWashOverride;
    }

    /**
     * @param occupancyDate
     * @param forecastGroupId
     * @return
     * @deprecated
     */
    @Deprecated
    public ForecastGroupWashOverride getForecastGroupWashOverride(DateParameter occupancyDate, Integer forecastGroupId) {
        return getForecastGroupWashOverride(occupancyDate.getTime(), forecastGroupId);
    }


    protected boolean isWashOverrideAllowed(Integer forecastTypeId) {
        if (null == forecastTypeId) {
            return false;
        }
        return com.ideas.tetris.pacman.services.demandoverride.entity.WashOverride.
                WASH_OVERRIDE_FORECAST_TYPE_IDS.contains(String.valueOf(forecastTypeId));
    }

    public HashMap<DemandOverrideKey, com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride> getOccupancyDemandOverrides(Date startDate, Date endDate, List<Integer> forecastGroupIds, List<Integer> roomClassIds) {

        List<OccupancyDemandOverride> demandOverrideEntities = crudService.findByNamedQuery(OccupancyDemandOverride.BY_OCCUPANCY_DATE_RANGE_AND_PROPERTY_ID_AND_FORECAST_GROUP_IDS_AND_IN_ACCOM_CLASSES,
                QueryParameter.with(START_DATE, startDate).
                        and(END_DATE, endDate).
                        and(FORECAST_GROUP_IDS, forecastGroupIds).
                        and(ACCOM_CLASS_IDS, roomClassIds).
                        and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).
                        parameters());

        HashMap<DemandOverrideKey, com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride> overrideMap = new HashMap<>();
        //lets convert these into our DemandOverrideDtos and add to our map
        for (OccupancyDemandOverride demandOverrideEntity : demandOverrideEntities) {
            //key is room class id + forecast group id + occupancy date
            DemandOverrideKey key = new DemandOverrideKey(demandOverrideEntity.getAccomClassID(), demandOverrideEntity.getForecastGroupId(), demandOverrideEntity.getOccupancyDate());
            com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride occupancyDemandOverride = new com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride(demandOverrideEntity);
            overrideMap.put(key, occupancyDemandOverride);
        }

        return overrideMap;
    }

    public void deleteArrivalDemandFCSTOverride() {
        crudService.executeUpdateByNativeQuery("truncate table Arrival_Demand_FCST_OVR");
    }

    /*
     * Sort the list of ForecastGroupWashOverrides to put all wash overrides, where overrides are allowed, at top.
     */
    private class ComparatorForecastGroupWashOverride implements Comparator<ForecastGroupWashOverride> {
        @Override
        public int compare(ForecastGroupWashOverride override1, ForecastGroupWashOverride override2) {
            boolean washOverrideAllowed1 = override1.isWashOverrideAllowed();
            boolean washOverrideAllowed2 = override2.isWashOverrideAllowed();
            if (washOverrideAllowed1 && washOverrideAllowed2) {
                return 0;
            } else if (washOverrideAllowed1) {
                return -1;
            } else {
                return +1;
            }
        }
    }


    private Map<LocalDate, Map<Integer, List<CPDecisionBAROutput>>> getCpDecisionsByOccupancyDatesAndAccomClassIds(Set<Date> overrideDates, Set<Integer> accomClassIds) {
        List<CPDecisionBAROutput> cpDecisionBAROutputs = getCPDecisionBAROutputs(accomClassIds, overrideDates);
        return cpDecisionBAROutputs.stream().collect(Collectors.groupingBy(
                CPDecisionBAROutput::getArrivalDate, Collectors.groupingBy(cpBarOutput -> cpBarOutput.getAccomType().getAccomClass().getId(), Collectors.toList())
        ));
    }

    private Map<Date, Map<Integer, List<DecisionBAROutput>>> getDecisionsByOccupancyDatesAndAccomClassIds(Set<Date> overrideDates, Set<Integer> accomClassIds) {
        List<DecisionBAROutput> decisionBAROutputs = getDecisionBAROutputs(accomClassIds, overrideDates);
        return decisionBAROutputs.stream().collect(Collectors.groupingBy(
                DecisionBAROutput::getArrivalDate, Collectors.groupingBy(DecisionBAROutput::getAccomClassId, Collectors.toList())
        ));
    }


    private void removeOverridesEfficiently(Set<DemandOverride> removals, List<OccupancyDemandForecast> occupancyDemandForecasts) {
        Map<Integer, ArrivalDemandOverride> arrivalDemandOverrideById;
        List<ArrivalDemandForecast> arrivalDemandForecasts;
        List<Integer> accomClassIDs = new ArrayList<>();
        List<Integer> forecastGroupIds = new ArrayList<>();
        List<Date> arrivalDates = new ArrayList<>();

        List<Integer> arrivalDemandOverrideIds = removals
                .stream()
                .filter(over -> over instanceof com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride)
                .flatMap(over -> ((com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride) over).getValues().stream())
                .map(ArrivalOverrideValue::getId)
                .distinct()
                .collect(Collectors.toList());

        List<ArrivalDemandOverride> arrivalDemandOverrides = getArrivalDemandOverridesUsingChunks(arrivalDemandOverrideIds, BATCH_SIZE);
        if (CollectionUtils.isNotEmpty(arrivalDemandOverrideIds)) {
            accomClassIDs = getIds(arrivalDemandOverrides, ArrivalDemandOverride::getAccomClassID);
            forecastGroupIds = getIds(arrivalDemandOverrides, ArrivalDemandOverride::getForecastGroupId);
            arrivalDates = getIds(arrivalDemandOverrides, ArrivalDemandOverride::getArrivalDate);
        }

        arrivalDemandOverrideById = arrivalDemandOverrides
                .stream()
                .collect(Collectors.toMap(ArrivalDemandOverride::getId,
                        arrivalDemandOverride -> arrivalDemandOverride,
                        (value1, value2) -> value1));
        arrivalDemandForecasts = getArrivalDemandForecasts(accomClassIDs, forecastGroupIds, arrivalDates);

        for (DemandOverride removal : removals) {
            removeOverride(removal, occupancyDemandForecasts, arrivalDemandOverrideById, arrivalDemandForecasts);
        }
    }

    /**
     * We got the below Exception, because we were passing a lot's of ID's
     * Exception : "The incoming request has too many parameters. The server supports a maximum of 2100 parameters. Reduce the number of parameters and resend the request"
     * We will now pass the ID's in the batch of 1000
     */
    @VisibleForTesting
	public
    List<ArrivalDemandOverride> getArrivalDemandOverridesUsingChunks(List<Integer> arrivalDemandOverrideIds, int batchSize) {
        if (CollectionUtils.isEmpty(arrivalDemandOverrideIds)) {
            return new ArrayList<>();
        }
        if (arrivalDemandOverrideIds.size() <= batchSize) {
            return crudService.findByNamedQuery(ArrivalDemandOverride.BY_IDS, QueryParameter.with("ids", arrivalDemandOverrideIds).parameters());
        }

        List<List<Integer>> partitionedList = Lists.partition(arrivalDemandOverrideIds, batchSize);
        return partitionedList
                .stream()
                .map(subList -> crudService.findByNamedQuery(ArrivalDemandOverride.BY_IDS, QueryParameter.with("ids", subList).parameters()))
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toList())
                .stream()
                .filter(obj -> obj instanceof ArrivalDemandOverride)
                .map(obj -> (ArrivalDemandOverride) obj)
                .collect(Collectors.toList());
    }

    /**
     * @param overrides should all have the same arrivalDate, forecastGroupId and accomClassId
     * @return
     */
    public static com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride buildArrivalDemandOverride(List<ArrivalDemandOverride> overrides) {
        if (overrides != null) {
            com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride override = null;
            for (ArrivalDemandOverride overrideEntity : overrides) {
                if (override == null) {
                    override = new com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride(overrideEntity);
                } else {
                    override.addValue(overrideEntity.getId(), overrideEntity.getLengthOfStay(), overrideEntity.getOverrideValue(), overrideEntity.getRemainingDemand(), overrideEntity.getRateUnqualified() != null ? overrideEntity.getRateUnqualified().getName() : "");
                }
            }
            return override;
        }
        return null;
    }

    public List<OccupancyDemandForecast> getOccupancyDemands(Date startDate, Date endDate) {
        return crudService.findByNamedQuery(OccupancyDemandForecast.BY_OCCUPANCY_DATE_RANGE,
                QueryParameter.with(START_DATE, startDate).
                        and(END_DATE, endDate).
                        parameters());
    }

    public List<OccupancyDemandForecast> getOccupancyDemands(Date startDate, Date endDate, List<Integer> forecastGroupIds, List<Integer> accomClassIds) {
        return crudService.findByNamedQuery(OccupancyDemandForecast.BY_OCCUPANCY_DATE_RANGE_FORECAST_GROUPS_ACCOM_CLASSES,
                QueryParameter.with(START_DATE, startDate).
                        and(END_DATE, endDate).
                        and(FORECAST_GROUP_IDS, forecastGroupIds).
                        and(ACCOM_CLASS_IDS, accomClassIds).
                        parameters());
    }

    public List<ArrivalDemandForecast> getArrivalDemands(Date startDate, Date endDate) {
        return crudService.findByNamedQuery(ArrivalDemandForecast.BY_ARRIVAL_DATE_RANGE,
                QueryParameter.with(START_DATE, startDate).
                        and(END_DATE, endDate).
                        parameters());
    }

    /**
     * Gets {@link OccupancyDemandForecast} at the forecast group level
     *
     * @param startDate
     * @param endDate
     * @param forecastGroupNames
     * @param productId
     * @return {@link OccupancyDemandForecast} between the given date range (inclusive), forecast
     * group names, and product id
     */
    public List<OccupancyDemandForecast> getOccupancyDemandForecastsBetweenDatesAndForecastGroupNamesAndProductId(java.time.LocalDate startDate, java.time.LocalDate endDate, Set<String> forecastGroupNames, int productId) {
        return crudService.findByNamedQuery(
                OccupancyDemandForecast.BY_OCCUPANCY_DATE_RANGE_AND_FORECAST_GROUPS_NAMES_AND_PRODUCT_ID,
                QueryParameter.with("startDate", LocalDateUtils.toDate(startDate))
                        .and("endDate", LocalDateUtils.toDate(endDate))
                        .and("forecastGroupNames", forecastGroupNames)
                        .and("productId", productId)
                        .parameters()
        );
    }

    /**
     * Gets {@link OccupancyDemandForecast} at the property level
     *
     * @param startDate
     * @param endDate
     * @param productId
     * @return {@link OccupancyDemandForecast} between the given date range (inclusive) and product id
     */
    public List<OccupancyDemandForecast> getOccupancyDemandForecastsBetweenDatesAndByProductId(java.time.LocalDate startDate, java.time.LocalDate endDate, int productId) {
        return crudService.findByNamedQuery(
                OccupancyDemandForecast.BY_OCCUPANCY_DATE_RANGE_AND_PRODUCT_ID,
                QueryParameter.with("startDate", LocalDateUtils.toDate(startDate))
                        .and("endDate", LocalDateUtils.toDate(endDate))
                        .and("productId", productId)
                        .parameters()
        );
    }

    /**
     * Gets {@link OccupancyDemandOverride} at the forecast group level
     *
     * @param startDate
     * @param endDate
     * @param forecastGroupNames
     * @param productId
     * @return {@link OccupancyDemandOverride} between the given date range (inclusive), forecast
     * group names, and product id
     */
    public List<OccupancyDemandOverride> getOccupancyDemandOverridesBetweenDatesAndForecastGroupNamesAndProductId(java.time.LocalDate startDate, java.time.LocalDate endDate, Set<String> forecastGroupNames, int productId) {
        return crudService.findByNamedQuery(
                OccupancyDemandOverride.FIND_ACTIVE_OVERRIDES_BETWEEN_DATES_AND_FORECAST_GROUP_NAMES_AND_PRODUCT_ID,
                QueryParameter.with("startDate", LocalDateUtils.toDate(startDate))
                        .and("endDate", LocalDateUtils.toDate(endDate))
                        .and("forecastGroupNames", forecastGroupNames)
                        .and("productId", productId)
                        .parameters()
        );
    }

    /**
     * Gets {@link OccupancyDemandOverride} at the property level
     *
     * @param startDate
     * @param endDate
     * @param productId
     * @return {@link OccupancyDemandOverride} between the given date range (inclusive) and product id
     */
    public List<OccupancyDemandOverride> getOccupancyDemandOverridesBetweenDatesAndByProductId(java.time.LocalDate startDate, java.time.LocalDate endDate, int productId) {
        return crudService.findByNamedQuery(
                OccupancyDemandOverride.FIND_ACTIVE_OVERRIDES_BETWEEN_DATES_AND_PRODUCT_ID,
                QueryParameter.with("startDate", LocalDateUtils.toDate(startDate))
                        .and("endDate", LocalDateUtils.toDate(endDate))
                        .and("productId", productId)
                        .parameters()
        );
    }

    public boolean isAlertForCanceledGroupsHavingDemandWashOverrideEnabled() {
        return configParamService.getBooleanParameterValue(FeatureTogglesConfigParamName.ALERT_USER_FOR_CANCELED_GROUPS_HAVING_DEMAND_WASH_OVERRIDE);
    }

    public List<DemandWashOverrideFGACDetailsDto> getFgAcDetailsForCancelledGroupsData() {
        Integer fileMetadataId = crudService.findByNamedQuerySingleResult(FileMetadata.GET_MAX_FILE_METADATA_ID);
        Map<String, Object> params = new HashMap<>();
        params.put("fileMetadataId", fileMetadataId);
        params.put("caughtUpDate", dateService.getCaughtUpDate());
        List<Object[]> result = crudService.findByNativeQuery(FIND_OVERRIDES_FOR_CANCELLED_GROUPS, params);
        return mapToDto(result);
    }

    private List<DemandWashOverrideFGACDetailsDto> mapToDto(List<Object[]> result) {
        List<DemandWashOverrideFGACDetailsDto> list = new ArrayList<>();
        for (Object[] obj : result) {
            DemandWashOverrideFGACDetailsDto dto = new DemandWashOverrideFGACDetailsDto();
            dto.setGroupCode((String) obj[0]);
            dto.setGroupId((Integer) obj[1]);
            dto.setOccupancyDate((Date) obj[2]);
            dto.setFgName((String) obj[3]);
            dto.setFgId((Integer) obj[4]);
            dto.setAcCode((String) obj[5]);
            dto.setAcId((Integer) obj[6]);
            list.add(dto);
        }
        return list;
    }


    /*
     * For testing.
     */
    @ForTesting


    public void deleteArrivalDemandOverride(Integer propertyId) {
        crudService.deleteAll(ArrivalDemandOverride.class);
    }

    @ForTesting


    public void deleteOccupancyDemandOverride(Integer propertyId) {
        crudService.deleteAll(OccupancyDemandOverride.class);
    }

    @ForTesting


    public void deleteWashOverride(Integer propertyId) {
        crudService.deleteAll(WashOverride.class);
        crudService.deleteAll(WashOverrideByGroup.class);
    }

    @ForTesting


    public Map<DemandOverride, Set<DemandOverride>> saveOccupancyDemandOverrides(Set<com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride> overrides) {
        return saveOverrides(new HashSet<>(overrides));
    }

    @ForTesting


    public Map<DemandOverride, Set<DemandOverride>> saveArrivalDemandOverrides(Set<com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride> overrides) {
        return saveOverrides(new HashSet<>(overrides));
    }

    @ForTesting


    public List<Integer> getActiveDemandOverrides(@DateFormat Date occupancyDate) {
        List<OccupancyDemandOverride> list = crudService.findByNamedQuery(OccupancyDemandOverride.BY_OCCUPANCY_DATE_AND_PROPERTY_ID,
                QueryParameter.with(START_DATE, occupancyDate).
                        and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).
                        parameters());
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().map(OccupancyDemandOverride::getId).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @ForTesting


    public void saveWashOverride(Set<WashOverride> overrides) {
        saveWashOverrides(overrides, null);
    }

    public List<WashForecast> getWashForecasts(){
        return crudService.findByNamedQuery(WashForecast.GET_BY_PROPERTY_ID,
                        QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).
                                parameters());
    }

}
