package com.ideas.tetris.pacman.services.bestavailablerate;

import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClassPriceRank;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccomClassPriceRankService;
import com.ideas.tetris.pacman.services.agilerates.configuration.dto.CeilingFloorAndOffsetsHierarchyContainer;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductAccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductHierarchy;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesHierarchyValidationService;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.CPRuleViolation;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.CPValidationDTO;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.CPValidationResultDTO;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.MinPriceDiffDTO;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.PricingAccomClassRankDto;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.Seasons;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomClassMinPriceDiff;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomClassMinPriceDiffSeason;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigMergedCeilingAndFloor;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigMergedCeilingAndFloorPK;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigMergedOffset;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigMergedOffsetPK;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigOffsetAccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OffsetMethod;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingBaseAccomType;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.TransientPricingBaseAccomType;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationOffsetService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.DateTimeConstants;
import org.joda.time.LocalDate;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper.getPropertyId;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class PricingConfigurationValidationService {
    private static final String PROPERTY_ID = "propertyId";
    private static final String PRODUCT_ID = "productID";

    @Autowired
    PricingConfigurationService pricingConfigurationService;

    @Autowired
    PricingConfigurationOffsetService pricingConfigurationOffsetService;

    @Autowired
    AccomClassPriceRankService accomClassPriceRankService;

    @Autowired
    DateService dateService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;

    @Autowired
	protected PacmanConfigParamsService configParamsService;

    @Autowired
	private AgileRatesConfigurationService agileRatesConfigurationService;

    @Autowired
    AgileRatesHierarchyValidationService agileRatesHierarchyValidationService;

    private final BigDecimal minValue = BigDecimal.valueOf(0.01);

    private static final String DEFAULT = "Default";
    private static final String CEILING_FLOOR_FLOW = "CeilingFloorFlow";
    private static final String OFFSET_FLOW = "OffestFlow";


    @SuppressWarnings("unchecked")
    public Set<String> validateSaveDefaultOffsets(List<CPConfigOffsetAccomType> offsetsToSave, List<AccomClassPriceRank> accomClassPriceRank, List<PricingAccomClass> pricingAccomClasses, List<PricingBaseAccomType> ceilingAndFloorConfig, OccupancyType baseOccupancyType) {
        List<PricingAccomClassRankDto> pricingAccomClassRankDtos = getPricingAccomClassesForProperty(accomClassPriceRank, pricingAccomClasses);

        return validateDefaultConfig(pricingAccomClassRankDtos, offsetsToSave, ceilingAndFloorConfig, baseOccupancyType);
    }

    @SuppressWarnings("unchecked")
    public Set<String> validateSaveDefaultOffsetsByPricingRuleType(List<CPConfigOffsetAccomType> offsetsToSave, List<AccomClassPriceRank> accomClassPriceRank, List<PricingAccomClass> pricingAccomClasses, List<PricingBaseAccomType> ceilingAndFloorConfig, OccupancyType baseOccupancyType) {
        List<PricingAccomClassRankDto> pricingAccomClassRankDtos = getPricingAccomClassesForProperty(accomClassPriceRank, pricingAccomClasses);

        return validateDefaultOffsetsByPricingRuleType(pricingAccomClassRankDtos, offsetsToSave, ceilingAndFloorConfig, baseOccupancyType);
    }

    public List<PricingBaseAccomType> getDefaultPricingBaseAccomTypes() {
        return crudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_DEFAULT_BY_PROPERTY_ID, QueryParameter.with(PROPERTY_ID, getPropertyId()).parameters());
    }

    public List<PricingBaseAccomType> getAllPricingBaseAccomTypes() {
        return crudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_ALL_BY_PROPERTY_ID, QueryParameter.with(PROPERTY_ID, getPropertyId()).parameters());
    }

    public List<PricingBaseAccomType> getAllPricingBaseAccomTypesByProductId(int productID) {
        return crudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_ALL_BY_PRODUCT_ID, QueryParameter.with(PRODUCT_ID, productID).parameters());
    }

    @SuppressWarnings("unchecked")
    public Set<String> validateSaveOffsetLowerThanTransientFloor(List<CPConfigOffsetAccomType> offsetsToSave, List<PricingBaseAccomType> transientCeilingAndFloorConfig) {
        Set<String> roomClassesWithOffsetViolation = new HashSet<>();

        transientCeilingAndFloorConfig.forEach(config -> {
            List<CPConfigOffsetAccomType> offsets = offsetsToSave.stream().filter(offset -> offset.getAccomType().getAccomClass().getId().equals(config.getAccomType().getAccomClass().getId())).collect(Collectors.toList());

            //verify floor + offset > 0
            roomClassesWithOffsetViolation.addAll(compareFloorOffsetsToZero(config, offsets));
        });
        return roomClassesWithOffsetViolation;
    }

    @SuppressWarnings("unchecked")
    public Set<String> validateSaveOffsetLowerThanTransientSeasonFloor(List<CPConfigOffsetAccomType> offsetsToSave, List<PricingBaseAccomType> transientSeasonCeilingAndFloorConfig, LocalDate caughtUpDate) {
        Set<String> roomClassesWithOffsetViolation = new HashSet<>();

        transientSeasonCeilingAndFloorConfig.forEach(config -> {

            //only check seasons with end dates after today
            if (config.getEndDate().isAfter(caughtUpDate)) {
                List<CPConfigOffsetAccomType> offsets = offsetsToSave.stream().filter(offset -> offset.getAccomType().getAccomClass().getId().equals(config.getAccomType().getAccomClass().getId())).collect(Collectors.toList());

                //verify floor + offset > 0
                roomClassesWithOffsetViolation.addAll(compareFloorOffsetsToZero(config, offsets));
            }
        });
        return roomClassesWithOffsetViolation;
    }

    @SuppressWarnings("unchecked")
    public Set<String> validateSaveFloorAboveZeroWithOffset(List<PricingBaseAccomType> baseAccomTypes) {
        LocalDate caughtUpDate = dateService.getCaughtUpLocalDate();

        List<CPConfigOffsetAccomType> cpConfigOffsetAccomTypes = crudService.findByNamedQuery(CPConfigOffsetAccomType.GET_OFFSET_CONFIG_FOR_PROPERTY_EXCLUDING_PAST_DATA,
                QueryParameter.with(PROPERTY_ID, getPropertyId())
                        .and("systemDate", caughtUpDate)
                        .and(PRODUCT_ID, baseAccomTypes.get(0).getProductID()).parameters());

        Set<String> roomClassesWithOffsetViolation = new HashSet<>();

        baseAccomTypes.forEach(config -> {

            List<CPConfigOffsetAccomType> offsets = cpConfigOffsetAccomTypes.stream().filter(offset -> offset.getAccomType().getAccomClass().getId().equals(config.getAccomType().getAccomClass().getId())).collect(Collectors.toList());

            //verify floor + offset > 0
            roomClassesWithOffsetViolation.addAll(compareFloorOffsetsToZero(config, offsets));
        });
        return roomClassesWithOffsetViolation;
    }

    public Set<String> validatePriceExcludedOffsets(List<CPConfigOffsetAccomType> offsetsToSave, List<PricingAccomClass> pricingAccomClasses, OccupancyType baseOccupancyType) {
        Set<String> roomClassesWithOffsetViolation = new HashSet<>();

        pricingAccomClasses.forEach(pricingAccomClass -> {
            if (pricingAccomClass.isPriceExcluded()) {

                List<CPConfigOffsetAccomType> offsets = offsetsToSave.stream().filter(offset -> offset.getAccomType().getAccomClass().getId().equals(pricingAccomClass.getAccomClass().getId())).collect(Collectors.toList());

                offsets.forEach(offset -> {
                    if (offset.getOccupancyType().equals(baseOccupancyType) && isPriceExcludedOffsetInvalid(offset)) {
                        roomClassesWithOffsetViolation.add(offset.getAccomType().getName());
                    }
                });
            }
        });

        return roomClassesWithOffsetViolation;
    }

    public boolean offsetValidation(BigDecimal offsetRate, boolean isSeasonal) {
        return (offsetRate == null && !isSeasonal) || (offsetRate != null && offsetRate.compareTo(minValue) < 0);
    }

    private Set<String> compareFloorOffsetsToZero(PricingBaseAccomType config, List<CPConfigOffsetAccomType> offsets) {
        Set<String> roomClassesWithOffsetViolation = new HashSet<>();

        offsets.forEach(offset -> {
            //only verify if fixed offset; ignore percentage
            if (offset.getOffsetMethod().toString().equals(OffsetMethod.FIXED_OFFSET.toString()) && isOffsetInvalid(config, offset)) {
                //do not add if season offsets are NOT within the same date range as the season config
                if ((config.getStartDate() == null) ||
                        (config.getStartDate() != null && offset.getStartDate() == null) ||
                        (offset.getStartDate() != null && (
                                (offset.getStartDate().isAfter(config.getStartDate()) && offset.getStartDate().isBefore(config.getEndDate())) ||
                                        (offset.getEndDate().isAfter(config.getStartDate()) && offset.getEndDate().isBefore(config.getEndDate())) ||
                                        (offset.getStartDate().isBefore(config.getStartDate()) && offset.getEndDate().isAfter(config.getEndDate())) ||
                                        (offset.getStartDate().equals(config.getStartDate())) || (offset.getEndDate().equals(config.getEndDate()))))) {
                    roomClassesWithOffsetViolation.add(offset.getAccomType().getName());
                }
            }
        });

        return roomClassesWithOffsetViolation;
    }

    public boolean floorOffsetCalculation(BigDecimal floorRate, BigDecimal offsetRate) {
        return floorRate != null && offsetRate != null && floorRate.add(offsetRate).compareTo(minValue) < 0;
    }

    @SuppressWarnings("unchecked")
    public Set<String> validateSaveDefaultCeilingAndFloor(List<PricingBaseAccomType> basePricingAccomTypes) {
        List<PricingAccomClassRankDto> pricingAccomClassRankDtos = getPricingAccomClassesForProperty();
        List<CPConfigOffsetAccomType> cpConfigOffsetAccomTypes = pricingConfigurationOffsetService.retrieveOffsetConfig(true, basePricingAccomTypes.get(0).getProductID());

        return validateDefaultConfig(pricingAccomClassRankDtos, cpConfigOffsetAccomTypes, basePricingAccomTypes);
    }

    public Set<String> validateSaveDefaultCeilingAndFloorByPricingRuleType(List<PricingBaseAccomType> basePricingAccomTypes) {
        List<PricingAccomClassRankDto> pricingAccomClassRankDtos = getPricingAccomClassesForProperty();
        List<CPConfigOffsetAccomType> cpConfigOffsetAccomTypes = pricingConfigurationOffsetService.retrieveOffsetConfig(true, basePricingAccomTypes.get(0).getProductID());
        return validateDefaultConfigByPricingRuleType(pricingAccomClassRankDtos, cpConfigOffsetAccomTypes, basePricingAccomTypes);
    }

    private Set<String> validateDefaultConfig(List<PricingAccomClassRankDto> pricingAccomClassRankDtos, List<CPConfigOffsetAccomType> offsetConfig, List<PricingBaseAccomType> ceilingAndFloorConfig) {
        OccupancyType baseOccupancyType = pricingConfigurationService.getBaseOccupancyType();
        return validateDefaultConfig(pricingAccomClassRankDtos, offsetConfig, ceilingAndFloorConfig, baseOccupancyType);
    }

    private Set<String> validateDefaultConfigByPricingRuleType(List<PricingAccomClassRankDto> pricingAccomClassRankDtos, List<CPConfigOffsetAccomType> offsetConfig, List<PricingBaseAccomType> ceilingAndFloorConfig) {
        OccupancyType baseOccupancyType = pricingConfigurationService.getBaseOccupancyType();
        return validateDefaultConfigByPricingRuleType(pricingAccomClassRankDtos, offsetConfig, ceilingAndFloorConfig, baseOccupancyType);
    }

    private Set<String> validateDefaultConfig(List<PricingAccomClassRankDto> pricingAccomClassRankDtos, List<CPConfigOffsetAccomType> offsetConfig, List<PricingBaseAccomType> ceilingAndFloorConfig, OccupancyType baseOccupancyType) {
        Set<String> classesInViolation = new LinkedHashSet<>();

        pricingAccomClassRankDtos.forEach(pricingAccomClassRankDto -> {
            PricingAccomClass lowerAccomClass = pricingAccomClassRankDto.getLowerPricingAccomClass();
            PricingAccomClass higherAccomClass = pricingAccomClassRankDto.getHigherPricingAccomClass();

            Set<CPConfigOffsetAccomType> lowerAccomTypeOffsets = getDefaultSingleOffsetsByAccomClass(offsetConfig, lowerAccomClass, baseOccupancyType);
            Set<CPConfigOffsetAccomType> higherAccomTypeOffsets = getDefaultSingleOffsetsByAccomClass(offsetConfig, higherAccomClass, baseOccupancyType);

            PricingBaseAccomType lowerAccomClassBaseConfig = getTransientPricingBaseAccomType(ceilingAndFloorConfig, lowerAccomClass);
            PricingBaseAccomType higherAccomClassBaseConfig = getTransientPricingBaseAccomType(ceilingAndFloorConfig, higherAccomClass);

            //No offset config for this class
            if (CollectionUtils.isNotEmpty(lowerAccomTypeOffsets) && CollectionUtils.isNotEmpty(higherAccomTypeOffsets) &&
                    !isValidDefaultRoomClassConfig(lowerAccomClassBaseConfig, lowerAccomTypeOffsets, lowerAccomClass.isPriceExcluded(), higherAccomClassBaseConfig, higherAccomTypeOffsets, higherAccomClass.isPriceExcluded())) {
                classesInViolation.add(lowerAccomClass.getAccomClass().getName() + " - " + higherAccomClass.getAccomClass().getName());
            }
        });
        return classesInViolation;
    }

    private Map<String, List<CPConfigOffsetAccomType>> getOffsetMap(List<CPConfigOffsetAccomType> offsetConfig, LocalDate caughtUpDate, OccupancyType baseOccupancyType, Set<Integer> baseRoomTypeIds) {
        Map<String, List<CPConfigOffsetAccomType>> map = new HashMap<>();
        offsetConfig = offsetConfig.stream().filter(x -> !baseRoomTypeIds.contains(x.getAccomType().getId()) && (x.getStartDate() == null || caughtUpDate.compareTo(x.getEndDate()) < 1) && x.getOccupancyType().equals(baseOccupancyType)).collect(Collectors.toList());
        map.put("DEFAULT", offsetConfig.stream().filter(x -> x.getStartDate() == null).collect(Collectors.toList()));
        map.put("ALL", offsetConfig);
        map.put("SEASON", offsetConfig.stream().filter(x -> x.getStartDate() != null).collect(Collectors.toList()));
        return map;
    }

    private Set<String> validateDefaultConfigByPricingRuleType(List<PricingAccomClassRankDto> pricingAccomClassRankDtos, List<CPConfigOffsetAccomType> offsetConfig, List<PricingBaseAccomType> ceilingAndFloorConfig, OccupancyType baseOccupancyType) {
        Set<String> classesInViolation = new LinkedHashSet<>();
        LocalDate caughtUpDate = dateService.getCaughtUpLocalDate();
        Set<Integer> baseRoomTypeIds = getRoomTypeIds(ceilingAndFloorConfig);
        Map<String, List<CPConfigOffsetAccomType>> offsetMap = getOffsetMap(offsetConfig, caughtUpDate, baseOccupancyType, baseRoomTypeIds);

        boolean hasPriceExcludedAccomClass = pricingAccomClassRankDtos.stream().anyMatch(pricingAccomClassRankDto ->
                pricingAccomClassRankDto.getHigherPricingAccomClass().isPriceExcluded() ||
                        pricingAccomClassRankDto.getLowerPricingAccomClass().isPriceExcluded());
        List<CPConfigOffsetAccomType> cpConfigOffsetAccomTypes = new ArrayList<>();
        if (hasPriceExcludedAccomClass) {
            cpConfigOffsetAccomTypes.addAll(offsetMap.get("ALL"));
        }
        validateConfigDetault(pricingAccomClassRankDtos, offsetMap, ceilingAndFloorConfig, baseOccupancyType, classesInViolation, baseRoomTypeIds, caughtUpDate);
        return classesInViolation;
    }

    private void validateConfigDetault(List<PricingAccomClassRankDto> pricingAccomClassRankDtos, Map<String, List<CPConfigOffsetAccomType>> offsetMap, List<PricingBaseAccomType> ceilingAndFloorConfig, OccupancyType baseOccupancyType, Set<String> classesInViolation, Set<Integer> baseRoomTypeIds, LocalDate caughtUpDate) {

        List<AccomClassMinPriceDiffSeason> minPriceDiffSeasons = crudService.findAll(AccomClassMinPriceDiffSeason.class);
        List<AccomClassMinPriceDiff> minPriceDiffDefaults = crudService.findAll(AccomClassMinPriceDiff.class);
        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsetConfigForBaseOccupancyType = getCpConfigMergedOffsetPKCPConfigMergedOffsetMap(offsetMap.get("SEASON"), baseRoomTypeIds);
        List<PricingAccomClassRankDto> sortedPricingAccomClassRankDtos = getSortedPricingAccomClassRankDtos(pricingAccomClassRankDtos);

        List<CPConfigOffsetAccomType> finalOffsetConfig = offsetMap.get("DEFAULT");
        sortedPricingAccomClassRankDtos.forEach(pricingAccomClassRankDto -> {

            CPRuleViolation cpRuleViolation = new CPRuleViolation(CEILING_FLOOR_FLOW);
            List<LocalDate> validationDoneSeasonDate = new ArrayList<>();

            PricingAccomClass lowerAccomClass = pricingAccomClassRankDto.getLowerPricingAccomClass();
            PricingAccomClass higherAccomClass = pricingAccomClassRankDto.getHigherPricingAccomClass();

            Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> lowerAccomClassOffsetConfigForBaseOccupancyType = getCpConfigMergedOffsetPKCPConfigMergedOffsetMap(offsetConfigForBaseOccupancyType, lowerAccomClass);

            Set<CPConfigOffsetAccomType> lowerAccomTypeOffsets = getDefaultOffsetsByAccomClass(finalOffsetConfig, lowerAccomClass, baseOccupancyType);
            Set<CPConfigOffsetAccomType> higherAccomTypeOffsets = getDefaultOffsetsByAccomClass(finalOffsetConfig, higherAccomClass, baseOccupancyType);

            // fetching min price diff for accom class.
            List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = getMinRoomClassPriceDifferenceSeasonsByAccomClass(minPriceDiffSeasons, lowerAccomClass, higherAccomClass);
            AccomClassMinPriceDiff accomClassMinPriceDiffDefault = getMinRoomClassPriceDifferenceDefaultByAccomClass(minPriceDiffDefaults, lowerAccomClass, higherAccomClass);

            PricingBaseAccomType lowerAccomClassBaseConfig = getTransientPricingBaseAccomType(ceilingAndFloorConfig, lowerAccomClass);
            PricingBaseAccomType higherAccomClassBaseConfig = getTransientPricingBaseAccomType(ceilingAndFloorConfig, higherAccomClass);

            CPValidationDTO cpValidationDTO = isValidDefaultRoomClassConfiguration(lowerAccomClassBaseConfig, lowerAccomTypeOffsets, lowerAccomClass.isPriceExcluded(), higherAccomClassBaseConfig, higherAccomTypeOffsets, higherAccomClass.isPriceExcluded(), accomClassMinPriceDiffDefault);
            if (CollectionUtils.isNotEmpty(lowerAccomTypeOffsets) &&
                    cpValidationDTO.getCeilingFloorViolation().contains(Boolean.FALSE)) {

                if (cpValidationDTO.getOffsetViolation().contains(Boolean.FALSE)) {
                    cpRuleViolation.getOffsetsOrTransientViolations().add(DEFAULT);
                }
                if (null != accomClassMinPriceDiffDefault && cpValidationDTO.getMinRoomClassViolation().contains(Boolean.FALSE)) {
                    cpRuleViolation.getMinPriceDiffVoilations().add(DEFAULT);
                }
                if (cpValidationDTO.getCeilingFloorViolation().contains(Boolean.FALSE)) {
                    cpRuleViolation.getViolations().add(Boolean.FALSE);
                }
            }
            validateCeilingFloorDefaultByOffsetSeason(offsetMap, ceilingAndFloorConfig, caughtUpDate, baseOccupancyType, lowerAccomClassOffsetConfigForBaseOccupancyType, cpRuleViolation, validationDoneSeasonDate, lowerAccomClass, higherAccomClass, accomClassMinPriceDiffSeasons, accomClassMinPriceDiffDefault);
            validateCeilingFloorByMinDiffSeasons(caughtUpDate, cpRuleViolation, validationDoneSeasonDate, lowerAccomClass, higherAccomClass, lowerAccomTypeOffsets, accomClassMinPriceDiffSeasons, accomClassMinPriceDiffDefault, lowerAccomClassBaseConfig, higherAccomClassBaseConfig);
            String accomClassformatedViolation = formatViolationsModuleWise(cpRuleViolation, lowerAccomClass, higherAccomClass, true);
            if (StringUtils.isNotEmpty(accomClassformatedViolation)) {
                classesInViolation.add(accomClassformatedViolation);
            }
        });
    }

    private Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> getCpConfigMergedOffsetPKCPConfigMergedOffsetMap(Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> finalOffsetConfigForBaseOccupancyType, PricingAccomClass lowerAccomClass) {
        Set<Integer> lowerAccomClassTypeId = lowerAccomClass.getAccomClass().getAccomTypes().stream().map(x -> x.getId()).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(lowerAccomClassTypeId) && null != finalOffsetConfigForBaseOccupancyType) {
            finalOffsetConfigForBaseOccupancyType.entrySet().stream().filter(x -> lowerAccomClassTypeId.contains(x.getKey().getAccomTypeId())).collect(Collectors.toMap(m -> m.getKey(), m -> m.getValue()));
        }
        return finalOffsetConfigForBaseOccupancyType;
    }

    private void validateCeilingFloorByMinDiffSeasons(LocalDate caughtUpDate, CPRuleViolation cpRuleViolation, List<LocalDate> validationDoneSeasonDate, PricingAccomClass lowerAccomClass, PricingAccomClass higherAccomClass, Set<CPConfigOffsetAccomType> lowerAccomTypeOffsets, List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons, AccomClassMinPriceDiff accomClassMinPriceDiffDefault, PricingBaseAccomType lowerAccomClassBaseConfig, PricingBaseAccomType higherAccomClassBaseConfig) {
        if (CollectionUtils.isNotEmpty(accomClassMinPriceDiffSeasons)) {
            for (AccomClassMinPriceDiffSeason accomClassMinPriceDiffSeason : accomClassMinPriceDiffSeasons) {

                LocalDate minRCSeasonStartDate = accomClassMinPriceDiffSeason.getStartDate();
                LocalDate minRCSeasonEndDate = accomClassMinPriceDiffSeason.getEndDate();

                LocalDate minRCSeasoncurrentDate = new LocalDate(minRCSeasonStartDate);

                while (minRCSeasoncurrentDate.compareTo(minRCSeasonEndDate) < 1) {
                    if (!validationDoneSeasonDate.contains(minRCSeasoncurrentDate) && minRCSeasoncurrentDate.compareTo(caughtUpDate) > -1) {

                        Function<PricingBaseAccomType, BigDecimal> dayOfWeekCeilingFunction = getDayOfWeekCeilingFunction(minRCSeasoncurrentDate);
                        Function<PricingBaseAccomType, BigDecimal> dayOfWeekFloorFunction = getDayOfWeekFloorFunction(minRCSeasoncurrentDate);
                        Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction = getDayOfWeekOffsetFunction(minRCSeasoncurrentDate);

                        MinPriceDiffDTO minPriceDiffDTO = getMinPriceDiff(minRCSeasoncurrentDate, accomClassMinPriceDiffSeasons, accomClassMinPriceDiffDefault);

                        CPValidationResultDTO cpValidationResultDTO = isValidDefaultRoomClassConfigForDayOfWeek(dayOfWeekCeilingFunction, dayOfWeekFloorFunction, dayOfWeekOffsetFunction, lowerAccomClassBaseConfig, lowerAccomTypeOffsets, lowerAccomClass.isPriceExcluded(), higherAccomClassBaseConfig, Collections.emptySet(), higherAccomClass.isPriceExcluded(), minPriceDiffDTO.getMinDiffValue());
                        addViolationBasedOnMinRCDiffSeasonsValidationResult(cpRuleViolation, lowerAccomTypeOffsets, minPriceDiffDTO, cpValidationResultDTO);

                    }
                    minRCSeasoncurrentDate = minRCSeasoncurrentDate.plusDays(1);
                }
            }
        }
    }

    private void addViolationBasedOnMinRCDiffSeasonsValidationResult(CPRuleViolation cpRuleViolation, Set<CPConfigOffsetAccomType> lowerAccomTypeOffsets, MinPriceDiffDTO minPriceDiffDTO, CPValidationResultDTO cpValidationResultDTO) {
        if (null != lowerAccomTypeOffsets && !cpValidationResultDTO.isValidationFlag()) {
            if (cpRuleViolation.getFlowFlag().equals(CEILING_FLOOR_FLOW)) {
                if (BigDecimal.ZERO.compareTo(cpValidationResultDTO.getOffsetValue()) != 0) {
                    cpRuleViolation.getOffsetsOrTransientViolations().add(DEFAULT);
                }
            } else {
                cpRuleViolation.getOffsetsOrTransientViolations().add(DEFAULT);
            }

            if (null != minPriceDiffDTO.getSource() && BigDecimal.ZERO.compareTo(minPriceDiffDTO.getMinDiffValue()) != 0) {
                cpRuleViolation.getMinPriceDiffVoilations().add(minPriceDiffDTO.getSource());
            }
            cpRuleViolation.getViolations().add(Boolean.FALSE);
        }
    }

    private void validateCeilingFloorDefaultByOffsetSeason(Map<String, List<CPConfigOffsetAccomType>> offsetMap, List<PricingBaseAccomType> ceilingAndFloorConfig, LocalDate caughtUpDate, OccupancyType finalBaseOccupancyType, Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> finalOffsetConfigForBaseOccupancyType, CPRuleViolation cpRuleViolation, List<LocalDate> validationDoneSeasonDate, PricingAccomClass lowerAccomClass, PricingAccomClass higherAccomClass, List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons, AccomClassMinPriceDiff accomClassMinPriceDiffDefault) {
        HashSet<Seasons> offsetSeasons = getOffsetSeason(offsetMap, finalBaseOccupancyType, lowerAccomClass);
        if (MapUtils.isNotEmpty(finalOffsetConfigForBaseOccupancyType)) {
            for (Seasons offsetSeason : offsetSeasons) {
                LocalDate startDate = offsetSeason.getStartDate();
                LocalDate endDate = offsetSeason.getEndDate();
                LocalDate currentDate = new LocalDate(startDate);

                PricingBaseAccomType higherClassCeilingConfig = getPricingBaseAccomType(ceilingAndFloorConfig, higherAccomClass);
                while (currentDate.compareTo(endDate) < 1) {
                    if (currentDate.compareTo(caughtUpDate) > -1 && !validationDoneSeasonDate.contains(currentDate)) {
                        validationDoneSeasonDate.add(currentDate);

                        Function<PricingBaseAccomType, BigDecimal> dayOfWeekCeilingFunction = getDayOfWeekCeilingFunction(currentDate);
                        Function<PricingBaseAccomType, BigDecimal> dayOfWeekFloorFunction = getDayOfWeekFloorFunction(currentDate);
                        Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction = getDayOfWeekOffsetFunction(currentDate);

                        List<CPConfigMergedOffset> baseRoomTypeOffsetsForLowerRoomClass = collectBaseRoomTypeOffsetsForDate(finalOffsetConfigForBaseOccupancyType, lowerAccomClass, currentDate);

                        BigDecimal higherRoomClassCeilingRate = getPricingBaseAccomTypeCeilingRate(getDayOfWeek(currentDate), higherClassCeilingConfig);
                        BigDecimal higherRoomClassFloorRate = getPricingBaseAccomTypeFloorRate(getDayOfWeek(currentDate), higherClassCeilingConfig);

                        BigDecimal lowerRoomClassCeilingRate = null;
                        BigDecimal lowerRoomClassFloorRate = null;
                        if (getDailyBarPricingRuleType().equals(3)) {
                            lowerRoomClassCeilingRate = getLowerRoomClassLowestRate(ceilingAndFloorConfig, lowerAccomClass, dayOfWeekCeilingFunction, offsetMap.get("ALL"), dayOfWeekOffsetFunction, finalBaseOccupancyType, baseRoomTypeOffsetsForLowerRoomClass);
                            lowerRoomClassFloorRate = getLowerRoomClassFloorLowestRate(ceilingAndFloorConfig, lowerAccomClass, dayOfWeekFloorFunction, offsetMap.get("ALL"), dayOfWeekOffsetFunction, finalBaseOccupancyType, baseRoomTypeOffsetsForLowerRoomClass);
                        } else {
                            lowerRoomClassCeilingRate = getLowerRoomClassRate(ceilingAndFloorConfig, lowerAccomClass, dayOfWeekCeilingFunction, offsetMap.get("ALL"), dayOfWeekOffsetFunction, finalBaseOccupancyType, baseRoomTypeOffsetsForLowerRoomClass);
                            lowerRoomClassFloorRate = getLowerRoomClassFloorRate(ceilingAndFloorConfig, lowerAccomClass, dayOfWeekFloorFunction, offsetMap.get("ALL"), dayOfWeekOffsetFunction, finalBaseOccupancyType, baseRoomTypeOffsetsForLowerRoomClass);
                        }

                        MinPriceDiffDTO minPriceDiffDTO = getMinPriceDiff(currentDate, accomClassMinPriceDiffSeasons, accomClassMinPriceDiffDefault);
                        lowerRoomClassCeilingRate = lowerRoomClassCeilingRate != null ? lowerRoomClassCeilingRate.add(minPriceDiffDTO.getMinDiffValue()) : lowerRoomClassCeilingRate;
                        lowerRoomClassFloorRate = lowerRoomClassFloorRate != null ? lowerRoomClassFloorRate.add(minPriceDiffDTO.getMinDiffValue()) : lowerRoomClassFloorRate;

                        //These will be null only if offsets are not configured at all
                        addViolationBasedOnOffsetSeasonValidationResult(cpRuleViolation, baseRoomTypeOffsetsForLowerRoomClass, higherRoomClassCeilingRate, higherRoomClassFloorRate, lowerRoomClassCeilingRate, lowerRoomClassFloorRate, minPriceDiffDTO);
                    }
                    currentDate = currentDate.plusDays(1);
                }
            }
        }
    }

    private void addViolationBasedOnOffsetSeasonValidationResult(CPRuleViolation cpRuleViolation, List<CPConfigMergedOffset> baseRoomTypeOffsetsForLowerRoomClass, BigDecimal higherRoomClassCeilingRate, BigDecimal higherRoomClassFloorRate, BigDecimal lowerRoomClassCeilingRate, BigDecimal lowerRoomClassFloorRate, MinPriceDiffDTO minPriceDiffDTO) {
        if (lowerRoomClassCeilingRate != null && higherRoomClassCeilingRate != null &&
                (lowerRoomClassCeilingRate.compareTo(higherRoomClassCeilingRate) > 0 || Objects.requireNonNull(lowerRoomClassFloorRate).compareTo(higherRoomClassFloorRate) > 0)) {
            if (null != minPriceDiffDTO.getSource() && BigDecimal.ZERO.compareTo(minPriceDiffDTO.getMinDiffValue()) != 0) {
                cpRuleViolation.getMinPriceDiffVoilations().add(minPriceDiffDTO.getSource());
            }
            String violationSeasonName = getVoildatedSeasonName(baseRoomTypeOffsetsForLowerRoomClass);
            if (StringUtils.isNotEmpty(violationSeasonName)) {
                cpRuleViolation.getOffsetsOrTransientViolations().add(violationSeasonName);
            }
            cpRuleViolation.getViolations().add(Boolean.FALSE);
        }
    }

    private PricingBaseAccomType getPricingBaseAccomType(List<PricingBaseAccomType> ceilingAndFloorConfig, PricingAccomClass higherAccomClass) {
        return ceilingAndFloorConfig
                .stream()
                .filter(pricingBaseAccomType -> pricingBaseAccomType.getAccomType().equals(higherAccomClass.getAccomType()))
                .filter(pricingBaseAccomType -> pricingBaseAccomType.getStartDate() == null)
                .findFirst()
                .orElse(null);
    }

    private HashSet<Seasons> getOffsetSeason(Map<String, List<CPConfigOffsetAccomType>> offsetMap, OccupancyType finalBaseOccupancyType, PricingAccomClass lowerAccomClass) {
        HashSet<Seasons> offsetSeasons = new HashSet<>();
        Set<CPConfigOffsetAccomType> lowerAccomClassSeasons = getSeasonsSingleOffsetsByAccomClass(offsetMap.get("SEASON"), lowerAccomClass, finalBaseOccupancyType);

        for (CPConfigOffsetAccomType cpConfigOffsetAccomType : lowerAccomClassSeasons) {
            Seasons offsetSeason = new Seasons();
            offsetSeason.setSeasonName(cpConfigOffsetAccomType.getName());
            offsetSeason.setStartDate(cpConfigOffsetAccomType.getStartDate());
            offsetSeason.setEndDate(cpConfigOffsetAccomType.getEndDate());
            offsetSeasons.add(offsetSeason);
        }

        return offsetSeasons;
    }

    private HashSet<Seasons> getTransientSeason(Set<PricingBaseAccomType> trainsentSeasons, PricingAccomClass lowerAccomClass) {
        Set<PricingBaseAccomType> lowerAccomClassTrainsentSeason = trainsentSeasons.stream().filter(pricingBaseAccomType -> pricingBaseAccomType.getAccomType().getAccomClass().equals(lowerAccomClass.getAccomClass())).collect(Collectors.toSet());
        HashSet<Seasons> offsetSeasons = new HashSet<>();
        for (PricingBaseAccomType pricingBaseAccomType : lowerAccomClassTrainsentSeason) {
            Seasons offsetSeason = new Seasons();
            offsetSeason.setSeasonName(pricingBaseAccomType.getSeasonName());
            offsetSeason.setStartDate(pricingBaseAccomType.getStartDate());
            offsetSeason.setEndDate(pricingBaseAccomType.getEndDate());
            offsetSeasons.add(offsetSeason);
        }

        return offsetSeasons;

    }

    private Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> getCpConfigMergedOffsetPKCPConfigMergedOffsetMap(List<CPConfigOffsetAccomType> offsetSeasons, Set<Integer> baseRoomTypeIds) {

        Optional<LocalDate> seasonStartDate = offsetSeasons.stream().map(x -> x.getStartDate()).min(LocalDate::compareTo);
        Optional<LocalDate> seasonEndDate = offsetSeasons.stream().map(x -> x.getEndDate()).max(LocalDate::compareTo);

        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsetConfigForBaseOccupancyType = null;
        if (seasonStartDate.isPresent() && seasonEndDate.isPresent()) {
            offsetConfigForBaseOccupancyType = pricingConfigurationService.findOffsetsForDatesAndBaseOccupancyType(seasonStartDate.get(), seasonEndDate.get());
            offsetConfigForBaseOccupancyType = offsetConfigForBaseOccupancyType.entrySet().stream().filter(x -> !baseRoomTypeIds.contains(x.getKey().getAccomTypeId())).collect(Collectors.toMap(m -> m.getKey(), m -> m.getValue()));
        }
        return offsetConfigForBaseOccupancyType;
    }

    private Set<Integer> getRoomTypeIds(List<PricingBaseAccomType> ceilingAndFloorConfig) {
        return ceilingAndFloorConfig.stream().map(x -> x.getAccomType().getId()).collect(Collectors.toSet());
    }

    private List<AccomClassMinPriceDiffSeason> getMinRoomClassPriceDifferenceSeasonsByAccomClass(List<AccomClassMinPriceDiffSeason> minPriceDiffSeasons, PricingAccomClass lowerAccomClass, PricingAccomClass higherAccomClass) {
        List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = null;
        if (!minPriceDiffSeasons.isEmpty()) {
            accomClassMinPriceDiffSeasons = minPriceDiffSeasons.stream().filter(x -> x.getAccomClassPriceRank().getLowerRankAccomClass().equals(lowerAccomClass.getAccomClass()) && x.getAccomClassPriceRank().getHigherRankAccomClass().equals(higherAccomClass.getAccomClass())).collect(Collectors.toList());
        }
        return accomClassMinPriceDiffSeasons;
    }

    private AccomClassMinPriceDiff getMinRoomClassPriceDifferenceDefaultByAccomClass(List<AccomClassMinPriceDiff> minPriceDiffDefaults, PricingAccomClass lowerAccomClass, PricingAccomClass higherAccomClass) {
        AccomClassMinPriceDiff accomClassMinPriceDiffDefault = null;
        if (!minPriceDiffDefaults.isEmpty()) {
            accomClassMinPriceDiffDefault = minPriceDiffDefaults.stream().filter(x -> x.getAccomClassPriceRank().getLowerRankAccomClass().equals(lowerAccomClass.getAccomClass()) && x.getAccomClassPriceRank().getHigherRankAccomClass().equals(higherAccomClass.getAccomClass())).findAny().orElse(null);
        }
        return accomClassMinPriceDiffDefault;
    }

    private String formatViolationsModuleWise(CPRuleViolation cpRuleViolation, PricingAccomClass lowerAccomClass, PricingAccomClass higherAccomClass, boolean lableFlag) {
        StringBuilder sb = new StringBuilder();
        String lable = lableFlag ? "Offsets" : "Transient Pricing";
        if (CollectionUtils.isNotEmpty(cpRuleViolation.getOffsetsOrTransientViolations()) || CollectionUtils.isNotEmpty(cpRuleViolation.getMinPriceDiffVoilations()) || cpRuleViolation.getViolations().contains(Boolean.FALSE)) {
            sb.append(lowerAccomClass.getAccomClass().getName() + " - " + higherAccomClass.getAccomClass().getName() + "<BR/>");
        }
        formatSeasonViolations(cpRuleViolation.getOffsetsOrTransientViolations(), sb, lable);
        formatMinPricingDiffViolations(cpRuleViolation.getMinPriceDiffVoilations(), sb);
        return sb.toString();
    }

    private void formatMinPricingDiffViolations(Set<String> minPriceDiffViolations, StringBuilder sb) {
        if (CollectionUtils.isNotEmpty(minPriceDiffViolations)) {
            List<String> minPricingDiffViolationSeasonList = minPriceDiffViolations.stream().filter(x -> x != DEFAULT).collect(Collectors.toList());
            boolean isminPriceDiffDefaultPresent = minPriceDiffViolations.contains(DEFAULT);
            sb.append("<b>Minimum Pricing Difference: [</b>");
            if (isminPriceDiffDefaultPresent) {
                sb.append(DEFAULT);
            }

            if (CollectionUtils.isNotEmpty(minPricingDiffViolationSeasonList)) {
                if (isminPriceDiffDefaultPresent) {
                    sb.append(", ");
                }
                sb.append(minPricingDiffViolationSeasonList.stream().collect(Collectors.joining(", ")));
            }
            sb.append("<b>]</b> <BR/>");
        }
    }

    private void formatSeasonViolations(Set<String> violations, StringBuilder sb, String lable) {
        if (CollectionUtils.isNotEmpty(violations)) {
            boolean isDefaultPresent = violations.contains(DEFAULT);
            List<String> violationSeasonList = violations.stream().filter(x -> x != DEFAULT).collect(Collectors.toList());
            sb.append("<b>" + lable + ": [</b>");
            if (isDefaultPresent) {
                sb.append(DEFAULT);
            }
            if (CollectionUtils.isNotEmpty(violationSeasonList)) {
                if (isDefaultPresent) {
                    sb.append(", ");
                }
                sb.append(violationSeasonList.stream().collect(Collectors.joining(", ")));
            }
            sb.append("<b>]</b> <BR/>");
        }
    }

    private Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> getCeilingAndFloorDetailsByDates(Set<PricingBaseAccomType> trainsentSeasons) {
        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates = null;
        Optional<LocalDate> seasonStartDate = trainsentSeasons.stream().map(x -> x.getStartDate()).min(LocalDate::compareTo);
        Optional<LocalDate> seasonEndDate = trainsentSeasons.stream().map(x -> x.getEndDate()).max(LocalDate::compareTo);
        if (seasonStartDate.isPresent() && seasonEndDate.isPresent()) {
            ceilingAndFloorConfigForDates = pricingConfigurationService.findCeilingAndFloorConfigForDates(seasonStartDate.get(), seasonEndDate.get(), false);
        }
        return ceilingAndFloorConfigForDates;
    }

    private Set<String> validateDefaultOffsetsByPricingRuleType(List<PricingAccomClassRankDto> pricingAccomClassRankDtos, List<CPConfigOffsetAccomType> offsetConfig, List<PricingBaseAccomType> ceilingAndFloorConfig, OccupancyType baseOccupancyType) {
        Set<String> classesInViolation = new LinkedHashSet<>();
        Set<Integer> baseRoomTypeIds = getRoomTypeIds(ceilingAndFloorConfig);
        offsetConfig = getCpConfigOffsetForNonBaseAccomTypes(offsetConfig, baseRoomTypeIds);

        boolean hasPriceExcludedAccomClass = pricingAccomClassRankDtos.stream().anyMatch(pricingAccomClassRankDto ->
                pricingAccomClassRankDto.getHigherPricingAccomClass().isPriceExcluded() ||
                        pricingAccomClassRankDto.getLowerPricingAccomClass().isPriceExcluded());
        List<CPConfigOffsetAccomType> allPersistedOffsetConfigs = new ArrayList<>();

        if (hasPriceExcludedAccomClass) {
            allPersistedOffsetConfigs.addAll(offsetConfig);
        }

        validateOffsetDefault(pricingAccomClassRankDtos, offsetConfig, ceilingAndFloorConfig, baseOccupancyType, classesInViolation, allPersistedOffsetConfigs);
        return classesInViolation;
    }

    private void validateOffsetDefault(List<PricingAccomClassRankDto> pricingAccomClassRankDtos, List<CPConfigOffsetAccomType> offsetConfig, List<PricingBaseAccomType> ceilingAndFloorConfig, OccupancyType baseOccupancyType, Set<String> classesInViolation, List<CPConfigOffsetAccomType> allPersistedOffsetConfigs) {

        List<AccomClassMinPriceDiffSeason> minPriceDiffSeasons = crudService.findAll(AccomClassMinPriceDiffSeason.class);
        List<AccomClassMinPriceDiff> minPriceDiffDefaults = crudService.findAll(AccomClassMinPriceDiff.class);
        List<PricingBaseAccomType> ceilingAndFloorConfigDefaults = ceilingAndFloorConfig.stream().filter(pricingBaseAccomType -> pricingBaseAccomType.getStartDate() == null && pricingBaseAccomType.getEndDate() == null).collect(Collectors.toList());
        LocalDate caughtUpDate = dateService.getCaughtUpLocalDate();
        Set<PricingBaseAccomType> transientSeasons = ceilingAndFloorConfig.stream().filter(x -> StringUtils.isNotEmpty(x.getSeasonName()) && caughtUpDate.compareTo(x.getEndDate()) < 1).collect(Collectors.toSet());
        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates = getCeilingAndFloorDetailsByDates(transientSeasons);
        List<PricingAccomClassRankDto> sortedPricingAccomClassRankDtos = getSortedPricingAccomClassRankDtos(pricingAccomClassRankDtos);
        OccupancyType finalBaseOccupancyType = baseOccupancyType;
        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> finalCeilingAndFloorConfigForDates = ceilingAndFloorConfigForDates;
        List<CPConfigOffsetAccomType> finalOffsetConfig = offsetConfig;
        sortedPricingAccomClassRankDtos.forEach(pricingAccomClassRankDto -> {

            CPRuleViolation cpRuleViolation = new CPRuleViolation(OFFSET_FLOW);
            List<LocalDate> validationDoneSeasonDate = new ArrayList<>();

            PricingAccomClass lowerAccomClass = pricingAccomClassRankDto.getLowerPricingAccomClass();
            PricingAccomClass higherAccomClass = pricingAccomClassRankDto.getHigherPricingAccomClass();

            Set<CPConfigOffsetAccomType> lowerAccomTypeOffsets = getDefaultSingleOffsetsByAccomClass(finalOffsetConfig, lowerAccomClass, finalBaseOccupancyType);
            Set<CPConfigOffsetAccomType> higherAccomTypeOffsets = getDefaultSingleOffsetsByAccomClass(finalOffsetConfig, higherAccomClass, finalBaseOccupancyType);


            PricingBaseAccomType lowerAccomClassBaseConfig = getTransientPricingBaseAccomType(ceilingAndFloorConfigDefaults, lowerAccomClass);
            PricingBaseAccomType higherAccomClassBaseConfig = getTransientPricingBaseAccomType(ceilingAndFloorConfigDefaults, higherAccomClass);

            List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = getMinRoomClassPriceDifferenceSeasonsByAccomClass(minPriceDiffSeasons, lowerAccomClass, higherAccomClass);
            AccomClassMinPriceDiff accomClassMinPriceDiffDefault = getMinRoomClassPriceDifferenceDefaultByAccomClass(minPriceDiffDefaults, lowerAccomClass, higherAccomClass);
            CPValidationDTO cpValidationDTO = isValidDefaultRoomClassConfiguration(lowerAccomClassBaseConfig, lowerAccomTypeOffsets, lowerAccomClass.isPriceExcluded(), higherAccomClassBaseConfig, higherAccomTypeOffsets, higherAccomClass.isPriceExcluded(), accomClassMinPriceDiffDefault);
            if (CollectionUtils.isNotEmpty(lowerAccomTypeOffsets) &&
                    cpValidationDTO.getCeilingFloorViolation().contains(Boolean.FALSE)) {
                cpRuleViolation.getOffsetsOrTransientViolations().add(DEFAULT);
                if (null != accomClassMinPriceDiffDefault && cpValidationDTO.getMinRoomClassViolation().contains(Boolean.FALSE)) {
                    cpRuleViolation.getMinPriceDiffVoilations().add(DEFAULT);
                }
            }
            validateOffsetDefaultByTransientSeasons(baseOccupancyType, allPersistedOffsetConfigs, caughtUpDate, transientSeasons, finalCeilingAndFloorConfigForDates, finalOffsetConfig, cpRuleViolation, validationDoneSeasonDate, lowerAccomClass, higherAccomClass, accomClassMinPriceDiffSeasons, accomClassMinPriceDiffDefault);
            validateCeilingFloorByMinDiffSeasons(caughtUpDate, cpRuleViolation, validationDoneSeasonDate, lowerAccomClass, higherAccomClass, lowerAccomTypeOffsets, accomClassMinPriceDiffSeasons, accomClassMinPriceDiffDefault, lowerAccomClassBaseConfig, higherAccomClassBaseConfig);
            String accomClassformatedViolation = formatViolationsModuleWise(cpRuleViolation, lowerAccomClass, higherAccomClass, false);
            if (StringUtils.isNotEmpty(accomClassformatedViolation)) {
                classesInViolation.add(accomClassformatedViolation);
            }
        });
    }

    /**
     * validateOffsetDefaultByTransientSeasons from PricingConfigurationValidationService
     * <p>
     * ---Sonar ignores---
     * <p>
     * S3776: Cognitive Complexity of methods should not be too high
     * We're not going to spend the time to refactor this now.
     */
    @SuppressWarnings({"squid:S3776"})
    private void validateOffsetDefaultByTransientSeasons(OccupancyType baseOccupancyType, List<CPConfigOffsetAccomType> allPersistedOffsetConfigs, LocalDate caughtUpDate, Set<PricingBaseAccomType> transientSeasons, Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> finalCeilingAndFloorConfigForDates, List<CPConfigOffsetAccomType> finalOffsetConfig, CPRuleViolation cpRuleViolation, List<LocalDate> validationDoneSeasonDate, PricingAccomClass lowerAccomClass, PricingAccomClass higherAccomClass, List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons, AccomClassMinPriceDiff accomClassMinPriceDiffDefault) {

        HashSet<Seasons> filteredTransientSeasons = getTransientSeason(transientSeasons, lowerAccomClass);

        if (MapUtils.isNotEmpty(finalCeilingAndFloorConfigForDates)) {
            for (Seasons transientSeason : filteredTransientSeasons) {

                LocalDate startDate = transientSeason.getStartDate();
                LocalDate endDate = transientSeason.getEndDate();

                LocalDate currentDate = new LocalDate(startDate);

                while (currentDate.compareTo(endDate) < 1) {
                    if (currentDate.compareTo(caughtUpDate) > -1 && !validationDoneSeasonDate.contains(currentDate)) {
                        validationDoneSeasonDate.add(currentDate);

                        Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction = getDayOfWeekOffsetFunction(currentDate);

                        Integer productId = transientSeasons
                                .stream()
                                .map(PricingBaseAccomType::getProductID)
                                .findFirst()
                                .orElse(1);

                        Set<CPConfigOffsetAccomType> lowerAccomTypes = getSingleOffsetsByAccomClass(finalOffsetConfig, lowerAccomClass, baseOccupancyType);
                        Set<CPConfigOffsetAccomType> higherAccomTypes = getSingleOffsetsByAccomClass(finalOffsetConfig, higherAccomClass, baseOccupancyType);

                        BigDecimal higherRoomClassCeilingRate = getHigherRoomClassCeilingRate(productId, finalCeilingAndFloorConfigForDates, higherAccomClass, higherAccomTypes, currentDate, dayOfWeekOffsetFunction, baseOccupancyType, allPersistedOffsetConfigs);
                        BigDecimal higherRoomClassFloorRate = getHigherRoomClassFloorRate(productId, finalCeilingAndFloorConfigForDates, higherAccomClass, higherAccomTypes, currentDate, dayOfWeekOffsetFunction, baseOccupancyType, allPersistedOffsetConfigs);

                        BigDecimal lowerRoomClassCeilingRate;
                        BigDecimal lowerRoomClassFloorRate;
                        if (getDailyBarPricingRuleType().equals(3)) {
                            lowerRoomClassCeilingRate = getLowerRoomClassCeilingLowestRate(productId, finalCeilingAndFloorConfigForDates, lowerAccomClass, lowerAccomTypes, currentDate, dayOfWeekOffsetFunction, baseOccupancyType, allPersistedOffsetConfigs);
                            lowerRoomClassFloorRate = getLowerRoomClassFloorLowestRate(productId, finalCeilingAndFloorConfigForDates, lowerAccomClass, lowerAccomTypes, currentDate, dayOfWeekOffsetFunction, baseOccupancyType, allPersistedOffsetConfigs);
                        } else {
                            lowerRoomClassCeilingRate = getLowerRoomClassCeilingRate(productId, finalCeilingAndFloorConfigForDates, lowerAccomClass, lowerAccomTypes, currentDate, dayOfWeekOffsetFunction, baseOccupancyType, allPersistedOffsetConfigs);
                            lowerRoomClassFloorRate = getLowerRoomClassFloorRate(productId, finalCeilingAndFloorConfigForDates, lowerAccomClass, lowerAccomTypes, currentDate, dayOfWeekOffsetFunction, baseOccupancyType, allPersistedOffsetConfigs);
                        }

                        MinPriceDiffDTO minPriceDiffDTO = getMinPriceDiff(currentDate, accomClassMinPriceDiffSeasons, accomClassMinPriceDiffDefault);

                        lowerRoomClassCeilingRate = lowerRoomClassCeilingRate != null ? lowerRoomClassCeilingRate.add(minPriceDiffDTO.getMinDiffValue()) : lowerRoomClassCeilingRate;
                        lowerRoomClassFloorRate = lowerRoomClassFloorRate != null ? lowerRoomClassFloorRate.add(minPriceDiffDTO.getMinDiffValue()) : lowerRoomClassFloorRate;

                        addViolationBasedOnTransientSeasonsValidationResult(finalCeilingAndFloorConfigForDates, cpRuleViolation, lowerAccomClass, currentDate, higherRoomClassCeilingRate, higherRoomClassFloorRate, lowerRoomClassCeilingRate, lowerRoomClassFloorRate, minPriceDiffDTO);
                    }
                    currentDate = currentDate.plusDays(1);
                }
            }
        }
    }

    private void addViolationBasedOnTransientSeasonsValidationResult(Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> finalCeilingAndFloorConfigForDates, CPRuleViolation cpRuleViolation, PricingAccomClass lowerAccomClass, LocalDate currentDate, BigDecimal higherRoomClassCeilingRate, BigDecimal higherRoomClassFloorRate, BigDecimal lowerRoomClassCeilingRate, BigDecimal lowerRoomClassFloorRate, MinPriceDiffDTO minPriceDiffDTO) {
        if (lowerRoomClassCeilingRate != null && higherRoomClassCeilingRate != null &&
                (lowerRoomClassCeilingRate.compareTo(higherRoomClassCeilingRate) > 0 || Objects.requireNonNull(lowerRoomClassFloorRate).compareTo(higherRoomClassFloorRate) > 0)) {
            if (null != minPriceDiffDTO.getSource() && BigDecimal.ZERO.compareTo(minPriceDiffDTO.getMinDiffValue()) != 0) {
                cpRuleViolation.getMinPriceDiffVoilations().add(minPriceDiffDTO.getSource());
            }
            CPConfigMergedCeilingAndFloor lowerClassCeiling = finalCeilingAndFloorConfigForDates.get(new CPConfigMergedCeilingAndFloorPK(1, currentDate, lowerAccomClass.getAccomType().getId()));
            if (null != lowerClassCeiling && lowerClassCeiling.getSeasonName() != null) {
                cpRuleViolation.getOffsetsOrTransientViolations().add(lowerClassCeiling.getSeasonName());
            } else {
                cpRuleViolation.getOffsetsOrTransientViolations().add(DEFAULT);
            }
        }
    }

    private String getVoildatedSeasonName(List<CPConfigMergedOffset> baseRoomTypeOffsetsForLowerRoomClass) {
        Optional<CPConfigMergedOffset> lowerClassOffset;
        String offsetSeasonName = null;
        BigDecimal offsetValue = null;

        if (getDailyBarPricingRuleType().equals(2)) {
            lowerClassOffset = getLowerClassOffset(baseRoomTypeOffsetsForLowerRoomClass, 2);
            offsetSeasonName = getOffsetSeasonName(lowerClassOffset);
            offsetValue = lowerClassOffset.isPresent() ? lowerClassOffset.get().getOffsetValue() : BigDecimal.ZERO;
            return BigDecimal.ZERO.compareTo(offsetValue) >= 0 ? null : offsetSeasonName;
        } else {
            lowerClassOffset = getLowerClassOffset(baseRoomTypeOffsetsForLowerRoomClass, 3);
            offsetSeasonName = getOffsetSeasonName(lowerClassOffset);
            offsetValue = lowerClassOffset.isPresent() ? lowerClassOffset.get().getOffsetValue() : BigDecimal.ZERO;
            return BigDecimal.ZERO.compareTo(offsetValue) <= 0 ? null : offsetSeasonName;
        }
    }

    private String getOffsetSeasonName(Optional<CPConfigMergedOffset> lowerClassOffset) {
        String offsetSeasonName = null;
        offsetSeasonName = lowerClassOffset.isPresent() ? lowerClassOffset.get().getSeasonName() : null;
        offsetSeasonName = StringUtils.isNotEmpty(offsetSeasonName) ? offsetSeasonName : DEFAULT;
        return offsetSeasonName;
    }

    private Optional<CPConfigMergedOffset> getLowerClassOffset(List<CPConfigMergedOffset> baseRoomTypeOffsetsForLowerRoomClass, int ruleType) {
        Optional<CPConfigMergedOffset> lowerClassOffset;
        if (ruleType == 2) {
            lowerClassOffset = baseRoomTypeOffsetsForLowerRoomClass
                    .stream()
                    .max((a, b) -> a.getOffsetValue().compareTo(b.getOffsetValue()));
        } else {
            lowerClassOffset = baseRoomTypeOffsetsForLowerRoomClass
                    .stream()
                    .min((a, b) -> a.getOffsetValue().compareTo(b.getOffsetValue()));
        }
        return lowerClassOffset;
    }

    private PricingBaseAccomType getTransientPricingBaseAccomType(List<PricingBaseAccomType> ceilingAndFloorConfig, PricingAccomClass pricingAccomClass) {
        return ceilingAndFloorConfig.stream()
                .filter(pricingBaseAccomType -> pricingBaseAccomType.getAccomType().getAccomClass().equals(pricingAccomClass.getAccomClass()))
                .findAny()
                .orElse(null);
    }

    private CPConfigOffsetAccomType createRowRepresentingBaseRoomTypeOffset(PricingAccomClass pricingAccomClass, OccupancyType baseOccupancyType) {
        CPConfigOffsetAccomType cpConfigOffsetAccomType = new CPConfigOffsetAccomType();

        cpConfigOffsetAccomType.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        cpConfigOffsetAccomType.setOccupancyType(baseOccupancyType);
        cpConfigOffsetAccomType.setSundayOffsetValue(BigDecimal.ZERO);
        cpConfigOffsetAccomType.setMondayOffsetValue(BigDecimal.ZERO);
        cpConfigOffsetAccomType.setTuesdayOffsetValue(BigDecimal.ZERO);
        cpConfigOffsetAccomType.setWednesdayOffsetValue(BigDecimal.ZERO);
        cpConfigOffsetAccomType.setThursdayOffsetValue(BigDecimal.ZERO);
        cpConfigOffsetAccomType.setFridayOffsetValue(BigDecimal.ZERO);
        cpConfigOffsetAccomType.setSaturdayOffsetValue(BigDecimal.ZERO);

        cpConfigOffsetAccomType.setSundayOffsetValueWithTax(BigDecimal.ZERO);
        cpConfigOffsetAccomType.setMondayOffsetValueWithTax(BigDecimal.ZERO);
        cpConfigOffsetAccomType.setTuesdayOffsetValueWithTax(BigDecimal.ZERO);
        cpConfigOffsetAccomType.setWednesdayOffsetValueWithTax(BigDecimal.ZERO);
        cpConfigOffsetAccomType.setThursdayOffsetValueWithTax(BigDecimal.ZERO);
        cpConfigOffsetAccomType.setFridayOffsetValueWithTax(BigDecimal.ZERO);
        cpConfigOffsetAccomType.setSaturdayOffsetValueWithTax(BigDecimal.ZERO);

        cpConfigOffsetAccomType.setAccomType(pricingAccomClass.getAccomType());
        return cpConfigOffsetAccomType;
    }

    private void updateRowRepresentingBaseRoomTypeOffset(CPConfigOffsetAccomType cpConfigOffsetAccomType) {
        cpConfigOffsetAccomType.setSundayOffsetValue(cpConfigOffsetAccomType.getSundayOffsetValue() != null ? cpConfigOffsetAccomType.getSundayOffsetValue() : BigDecimal.ZERO);
        cpConfigOffsetAccomType.setMondayOffsetValue(cpConfigOffsetAccomType.getMondayOffsetValue() != null ? cpConfigOffsetAccomType.getMondayOffsetValue() : BigDecimal.ZERO);
        cpConfigOffsetAccomType.setTuesdayOffsetValue(cpConfigOffsetAccomType.getTuesdayOffsetValue() != null ? cpConfigOffsetAccomType.getTuesdayOffsetValue() : BigDecimal.ZERO);
        cpConfigOffsetAccomType.setWednesdayOffsetValue(cpConfigOffsetAccomType.getWednesdayOffsetValue() != null ? cpConfigOffsetAccomType.getWednesdayOffsetValue() : BigDecimal.ZERO);
        cpConfigOffsetAccomType.setThursdayOffsetValue(cpConfigOffsetAccomType.getThursdayOffsetValue() != null ? cpConfigOffsetAccomType.getThursdayOffsetValue() : BigDecimal.ZERO);
        cpConfigOffsetAccomType.setFridayOffsetValue(cpConfigOffsetAccomType.getFridayOffsetValue() != null ? cpConfigOffsetAccomType.getFridayOffsetValue() : BigDecimal.ZERO);
        cpConfigOffsetAccomType.setSaturdayOffsetValue(cpConfigOffsetAccomType.getSaturdayOffsetValue() != null ? cpConfigOffsetAccomType.getSaturdayOffsetValue() : BigDecimal.ZERO);

        cpConfigOffsetAccomType.setSundayOffsetValueWithTax(cpConfigOffsetAccomType.getSundayOffsetValueWithTax() != null ? cpConfigOffsetAccomType.getSundayOffsetValueWithTax() : BigDecimal.ZERO);
        cpConfigOffsetAccomType.setMondayOffsetValueWithTax(cpConfigOffsetAccomType.getMondayOffsetValueWithTax() != null ? cpConfigOffsetAccomType.getMondayOffsetValueWithTax() : BigDecimal.ZERO);
        cpConfigOffsetAccomType.setTuesdayOffsetValueWithTax(cpConfigOffsetAccomType.getTuesdayOffsetValueWithTax() != null ? cpConfigOffsetAccomType.getTuesdayOffsetValueWithTax() : BigDecimal.ZERO);
        cpConfigOffsetAccomType.setWednesdayOffsetValueWithTax(cpConfigOffsetAccomType.getWednesdayOffsetValueWithTax() != null ? cpConfigOffsetAccomType.getWednesdayOffsetValueWithTax() : BigDecimal.ZERO);
        cpConfigOffsetAccomType.setThursdayOffsetValueWithTax(cpConfigOffsetAccomType.getThursdayOffsetValueWithTax() != null ? cpConfigOffsetAccomType.getThursdayOffsetValueWithTax() : BigDecimal.ZERO);
        cpConfigOffsetAccomType.setFridayOffsetValueWithTax(cpConfigOffsetAccomType.getFridayOffsetValueWithTax() != null ? cpConfigOffsetAccomType.getFridayOffsetValueWithTax() : BigDecimal.ZERO);
        cpConfigOffsetAccomType.setSaturdayOffsetValueWithTax(cpConfigOffsetAccomType.getSaturdayOffsetValueWithTax() != null ? cpConfigOffsetAccomType.getSaturdayOffsetValueWithTax() : BigDecimal.ZERO);
    }

    public Set<String> validateSaveSeasonOffsets(List<CPConfigOffsetAccomType> offsetsForSeason, LocalDate startDate, LocalDate endDate, List<AccomClassPriceRank> accomClassPriceRank, List<PricingAccomClass> pricingAccomClasses, OccupancyType baseOccupancyType) {
        Set<String> classesInViolation = new HashSet<>();
        int productIDForOffsets = offsetsForSeason.get(0).getProductID();

        List<PricingAccomClassRankDto> pricingAccomClassRankDtos = getPricingAccomClassesForProperty(accomClassPriceRank, pricingAccomClasses);

        //Price excluded classes don't show up in this data
        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates = pricingConfigurationService.findCeilingAndFloorConfigForDates(startDate, endDate, false);
        List<PricingAccomClass> priceExcludedAccomClasses = pricingAccomClasses.stream().filter(PricingAccomClass::isPriceExcluded).collect(Collectors.toList());

        if (MapUtils.isNotEmpty(ceilingAndFloorConfigForDates) || CollectionUtils.isNotEmpty(priceExcludedAccomClasses)) {
            boolean shouldIncludePastData = true;
            LocalDate caughtUpDate = dateService.getCaughtUpLocalDate();
            shouldIncludePastData = startDate.isBefore(caughtUpDate);
            boolean hasPriceExcludedAccomClass = pricingAccomClassRankDtos.stream().anyMatch(pricingAccomClassRankDto ->
                    pricingAccomClassRankDto.getHigherPricingAccomClass().isPriceExcluded() ||
                            pricingAccomClassRankDto.getLowerPricingAccomClass().isPriceExcluded());
            List<CPConfigOffsetAccomType> allPersistedOffsetConfigs = new ArrayList<>();
            if (hasPriceExcludedAccomClass) {
                allPersistedOffsetConfigs.addAll(pricingConfigurationOffsetService.retrieveOffsetConfig(shouldIncludePastData, productIDForOffsets));
            }
            pricingAccomClassRankDtos.forEach(pricingAccomClassRankDto -> {
                PricingAccomClass lowerAccomClass = pricingAccomClassRankDto.getLowerPricingAccomClass();
                PricingAccomClass higherAccomClass = pricingAccomClassRankDto.getHigherPricingAccomClass();

                Set<CPConfigOffsetAccomType> lowerAccomTypes = getSingleOffsetsByAccomClass(offsetsForSeason, lowerAccomClass, baseOccupancyType);
                Set<CPConfigOffsetAccomType> higherAccomTypes = getSingleOffsetsByAccomClass(offsetsForSeason, higherAccomClass, baseOccupancyType);

                LocalDate currentDate = new LocalDate(startDate);
                while (currentDate.compareTo(endDate) < 1) {
                    Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction = getDayOfWeekOffsetFunction(currentDate);

                    BigDecimal lowerRoomClassCeilingRate;
                    BigDecimal higherRoomClassCeilingRate;
                    BigDecimal lowerRoomClassFloorRate;
                    BigDecimal higherRoomClassFloorRate;
                    if (configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)) {
                        List<AccomClass> accomClassesInOffsets = offsetsForSeason.stream()
                                .map(offset -> offset.getAccomType().getAccomClass())
                                .distinct()
                                .collect(Collectors.toList());

                        lowerRoomClassCeilingRate = accomClassesInOffsets.contains(lowerAccomClass.getAccomClass()) ?
                                getLowerRoomClassCeilingAndFloorRate(productIDForOffsets, ceilingAndFloorConfigForDates, lowerAccomClass, lowerAccomTypes, currentDate, dayOfWeekOffsetFunction, baseOccupancyType, allPersistedOffsetConfigs) :
                                null;
                        higherRoomClassCeilingRate = accomClassesInOffsets.contains(higherAccomClass.getAccomClass()) ?
                                getHigherRoomClassCeilingRate(productIDForOffsets, ceilingAndFloorConfigForDates, higherAccomClass, higherAccomTypes, currentDate, dayOfWeekOffsetFunction, baseOccupancyType, allPersistedOffsetConfigs) :
                                null;

                        lowerRoomClassFloorRate = accomClassesInOffsets.contains(lowerAccomClass.getAccomClass()) ?
                                getLowerRoomClassCeilingAndFloorRate(productIDForOffsets, ceilingAndFloorConfigForDates, lowerAccomClass, lowerAccomTypes, currentDate, dayOfWeekOffsetFunction, baseOccupancyType, allPersistedOffsetConfigs) :
                                null;
                        higherRoomClassFloorRate = accomClassesInOffsets.contains(higherAccomClass.getAccomClass()) ?
                                getHigherRoomClassFloorRate(productIDForOffsets, ceilingAndFloorConfigForDates, higherAccomClass, higherAccomTypes, currentDate, dayOfWeekOffsetFunction, baseOccupancyType, allPersistedOffsetConfigs) :
                                null;
                    } else {
                        lowerRoomClassCeilingRate = getLowerRoomClassCeilingAndFloorRate(productIDForOffsets, ceilingAndFloorConfigForDates, lowerAccomClass, lowerAccomTypes, currentDate, dayOfWeekOffsetFunction, baseOccupancyType, allPersistedOffsetConfigs);
                        higherRoomClassCeilingRate = getHigherRoomClassCeilingRate(productIDForOffsets, ceilingAndFloorConfigForDates, higherAccomClass, higherAccomTypes, currentDate, dayOfWeekOffsetFunction, baseOccupancyType, allPersistedOffsetConfigs);

                        lowerRoomClassFloorRate = getLowerRoomClassCeilingAndFloorRate(productIDForOffsets, ceilingAndFloorConfigForDates, lowerAccomClass, lowerAccomTypes, currentDate, dayOfWeekOffsetFunction, baseOccupancyType, allPersistedOffsetConfigs);
                        higherRoomClassFloorRate = getHigherRoomClassFloorRate(productIDForOffsets, ceilingAndFloorConfigForDates, higherAccomClass, higherAccomTypes, currentDate, dayOfWeekOffsetFunction, baseOccupancyType, allPersistedOffsetConfigs);
                    }

                    //These will be null only if ceiling and floor are not configured at all
                    if (lowerRoomClassCeilingRate != null && higherRoomClassCeilingRate != null &&
                            (lowerRoomClassCeilingRate.compareTo(higherRoomClassCeilingRate) > 0 || lowerRoomClassFloorRate.compareTo(higherRoomClassFloorRate) > 0)) {
                        classesInViolation.add(lowerAccomClass.getAccomClass().getName() + " - " + higherAccomClass.getAccomClass().getName());
                    }

                    currentDate = currentDate.plusDays(1);
                }
            });
        }

        return classesInViolation;
    }

    public Set<String> validateSaveSeasonOffsetsByDailyPricingBarRuleType(List<CPConfigOffsetAccomType> offsetsForSeason, LocalDate startDate, LocalDate endDate, List<AccomClassPriceRank> accomClassPriceRank, List<PricingAccomClass> pricingAccomClasses, OccupancyType baseOccupancyType) {
        int productIDForOffsets = offsetsForSeason.get(0).getProductID();
        Set<String> classesInViolation = new LinkedHashSet<>();
        List<PricingAccomClassRankDto> pricingAccomClassRankDtos = getPricingAccomClassesForProperty(accomClassPriceRank, pricingAccomClasses);
        //Price excluded classes don't show up in this data
        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates = pricingConfigurationService.findCeilingAndFloorConfigForDates(startDate, endDate, false);
        Set<Integer> baseRoomTypeIds = getRoomTypeIds(getAllPricingBaseAccomTypes());

        offsetsForSeason = getCpConfigOffsetForNonBaseAccomTypes(offsetsForSeason, baseRoomTypeIds);

        List<PricingAccomClass> priceExcludedAccomClasses = pricingAccomClasses.stream().filter(PricingAccomClass::isPriceExcluded).collect(Collectors.toList());

        if (MapUtils.isNotEmpty(ceilingAndFloorConfigForDates) || CollectionUtils.isNotEmpty(priceExcludedAccomClasses)) {
            boolean shouldIncludePastData = true;
            LocalDate caughtUpDate = dateService.getCaughtUpLocalDate();
            shouldIncludePastData = startDate.isBefore(caughtUpDate);

            boolean hasPriceExcludedAccomClass = pricingAccomClassRankDtos.stream().anyMatch(pricingAccomClassRankDto ->
                    pricingAccomClassRankDto.getHigherPricingAccomClass().isPriceExcluded() ||
                            pricingAccomClassRankDto.getLowerPricingAccomClass().isPriceExcluded());
            List<CPConfigOffsetAccomType> allPersistedOffsetConfigs = new ArrayList<>();

            if (hasPriceExcludedAccomClass) {
                allPersistedOffsetConfigs.addAll(pricingConfigurationOffsetService.retrieveOffsetConfig(shouldIncludePastData, productIDForOffsets));
                allPersistedOffsetConfigs = allPersistedOffsetConfigs.stream().filter(x -> !baseRoomTypeIds.contains(x.getAccomType().getId())).collect(Collectors.toList());
            }

            validateOffsetSeason(offsetsForSeason, startDate, endDate, baseOccupancyType, classesInViolation, pricingAccomClassRankDtos, ceilingAndFloorConfigForDates, allPersistedOffsetConfigs);
        }
        return classesInViolation;
    }

    private void validateOffsetSeason(List<CPConfigOffsetAccomType> offsetsForSeason, LocalDate startDate, LocalDate endDate, OccupancyType baseOccupancyType, Set<String> classesInViolation, List<PricingAccomClassRankDto> pricingAccomClassRankDtos, Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates, List<CPConfigOffsetAccomType> allPersistedOffsetConfigs) {
        List<AccomClassMinPriceDiffSeason> minPriceDiffSeasons = crudService.findAll(AccomClassMinPriceDiffSeason.class);
        List<AccomClassMinPriceDiff> minPriceDiffDefaults = crudService.findAll(AccomClassMinPriceDiff.class);
        List<PricingAccomClassRankDto> sortedPricingAccomClassRankDtos = getSortedPricingAccomClassRankDtos(pricingAccomClassRankDtos);

        List<CPConfigOffsetAccomType> finalOffsetsForSeason = offsetsForSeason;
        List<CPConfigOffsetAccomType> finalAllPersistedOffsetConfigs = allPersistedOffsetConfigs;
        sortedPricingAccomClassRankDtos.forEach(pricingAccomClassRankDto -> {
            CPRuleViolation cpRuleViolation = new CPRuleViolation();
            PricingAccomClass lowerAccomClass = pricingAccomClassRankDto.getLowerPricingAccomClass();
            PricingAccomClass higherAccomClass = pricingAccomClassRankDto.getHigherPricingAccomClass();

            Set<CPConfigOffsetAccomType> lowerAccomTypes = getSingleOffsetsByAccomClass(finalOffsetsForSeason, lowerAccomClass, baseOccupancyType);
            Set<CPConfigOffsetAccomType> higherAccomTypes = getSingleOffsetsByAccomClass(finalOffsetsForSeason, higherAccomClass, baseOccupancyType);

            List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = getMinRoomClassPriceDifferenceSeasonsByAccomClass(minPriceDiffSeasons, lowerAccomClass, higherAccomClass);
            AccomClassMinPriceDiff accomClassMinPriceDiffDefault = getMinRoomClassPriceDifferenceDefaultByAccomClass(minPriceDiffDefaults, lowerAccomClass, higherAccomClass);

            LocalDate currentDate = new LocalDate(startDate);
            while (currentDate.compareTo(endDate) < 1) {
                validateOffsetSeasonByDate(baseOccupancyType, ceilingAndFloorConfigForDates, finalAllPersistedOffsetConfigs, cpRuleViolation, lowerAccomClass, higherAccomClass, lowerAccomTypes, higherAccomTypes, accomClassMinPriceDiffSeasons, accomClassMinPriceDiffDefault, currentDate);
                currentDate = currentDate.plusDays(1);
            }
            String accomClassformatedViolation = formatViolationsModuleWise(cpRuleViolation, lowerAccomClass, higherAccomClass, false);
            if (StringUtils.isNotEmpty(accomClassformatedViolation)) {
                classesInViolation.add(accomClassformatedViolation);
            }
        });
    }

    private void validateOffsetSeasonByDate(OccupancyType baseOccupancyType, Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates, List<CPConfigOffsetAccomType> finalAllPersistedOffsetConfigs, CPRuleViolation cpRuleViolation, PricingAccomClass lowerAccomClass, PricingAccomClass higherAccomClass, Set<CPConfigOffsetAccomType> lowerAccomTypes, Set<CPConfigOffsetAccomType> higherAccomTypes, List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons, AccomClassMinPriceDiff accomClassMinPriceDiffDefault, LocalDate currentDate) {
        Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction = getDayOfWeekOffsetFunction(currentDate);

        Integer productId = finalAllPersistedOffsetConfigs.stream()
                .map(CPConfigOffsetAccomType::getProductID)
                .findFirst()
                .orElse(1);

        BigDecimal higherRoomClassCeilingRate = getHigherRoomClassCeilingRate(productId, ceilingAndFloorConfigForDates, higherAccomClass, higherAccomTypes, currentDate, dayOfWeekOffsetFunction, baseOccupancyType, finalAllPersistedOffsetConfigs);
        BigDecimal higherRoomClassFloorRate = getHigherRoomClassFloorRate(productId, ceilingAndFloorConfigForDates, higherAccomClass, higherAccomTypes, currentDate, dayOfWeekOffsetFunction, baseOccupancyType, finalAllPersistedOffsetConfigs);

        BigDecimal lowerRoomClassCeilingRate = null;
        BigDecimal lowerRoomClassFloorRate = null;

        if (getDailyBarPricingRuleType().equals(3)) {
            lowerRoomClassCeilingRate = getLowerRoomClassCeilingLowestRate(productId, ceilingAndFloorConfigForDates, lowerAccomClass, lowerAccomTypes, currentDate, dayOfWeekOffsetFunction, baseOccupancyType, finalAllPersistedOffsetConfigs);
            lowerRoomClassFloorRate = getLowerRoomClassFloorLowestRate(productId, ceilingAndFloorConfigForDates, lowerAccomClass, lowerAccomTypes, currentDate, dayOfWeekOffsetFunction, baseOccupancyType, finalAllPersistedOffsetConfigs);
        } else {
            lowerRoomClassCeilingRate = getLowerRoomClassCeilingRate(productId, ceilingAndFloorConfigForDates, lowerAccomClass, lowerAccomTypes, currentDate, dayOfWeekOffsetFunction, baseOccupancyType, finalAllPersistedOffsetConfigs);
            lowerRoomClassFloorRate = getLowerRoomClassFloorRate(productId, ceilingAndFloorConfigForDates, lowerAccomClass, lowerAccomTypes, currentDate, dayOfWeekOffsetFunction, baseOccupancyType, finalAllPersistedOffsetConfigs);
        }
        MinPriceDiffDTO minPriceDiffDTO = null;
        minPriceDiffDTO = getMinPriceDiff(currentDate, accomClassMinPriceDiffSeasons, accomClassMinPriceDiffDefault);
        lowerRoomClassCeilingRate = lowerRoomClassCeilingRate != null ? lowerRoomClassCeilingRate.add(minPriceDiffDTO.getMinDiffValue()) : lowerRoomClassCeilingRate;
        lowerRoomClassFloorRate = lowerRoomClassFloorRate != null ? lowerRoomClassFloorRate.add(minPriceDiffDTO.getMinDiffValue()) : lowerRoomClassFloorRate;

        //These will be null only if ceiling and floor are not configured at all
        if (lowerRoomClassCeilingRate != null && higherRoomClassCeilingRate != null &&
                (lowerRoomClassCeilingRate.compareTo(higherRoomClassCeilingRate) > 0 || Objects.requireNonNull(lowerRoomClassFloorRate).compareTo(higherRoomClassFloorRate) > 0)) {
            if (null != minPriceDiffDTO.getSource() && BigDecimal.ZERO.compareTo(minPriceDiffDTO.getMinDiffValue()) != 0) {
                cpRuleViolation.getMinPriceDiffVoilations().add(minPriceDiffDTO.getSource());
            }
            CPConfigMergedCeilingAndFloor lowerClassCeiling = ceilingAndFloorConfigForDates.get(new CPConfigMergedCeilingAndFloorPK(1, currentDate, lowerAccomClass.getAccomType().getId()));
            if (null != lowerClassCeiling && null != lowerClassCeiling.getSeasonName()) {
                cpRuleViolation.getOffsetsOrTransientViolations().add(lowerClassCeiling.getSeasonName());
            } else {
                cpRuleViolation.getOffsetsOrTransientViolations().add(DEFAULT);
            }
        }
    }

    private List<CPConfigOffsetAccomType> getCpConfigOffsetForNonBaseAccomTypes(List<CPConfigOffsetAccomType> offsetsForSeason, Set<Integer> baseRoomTypeIds) {
        offsetsForSeason = offsetsForSeason.stream().filter(x -> !baseRoomTypeIds.contains(x.getAccomType().getId())).collect(Collectors.toList());
        return offsetsForSeason;
    }

    private BigDecimal getHigherRoomClassFloorRate(Integer productId, Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates, PricingAccomClass pricingAccomClass, Set<CPConfigOffsetAccomType> higherAccomTypes, LocalDate currentDate, Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction, OccupancyType baseOccupancyType, List<CPConfigOffsetAccomType> allPersistedOffsetConfigs) {
        if (pricingAccomClass.isPriceExcluded()) {
            List<CPConfigOffsetAccomType> offsetsProvidedForThisDOW = getOffsetProvidedForThisDOW(pricingAccomClass, higherAccomTypes, dayOfWeekOffsetFunction, baseOccupancyType, allPersistedOffsetConfigs);

            return offsetsProvidedForThisDOW
                    .stream()
                    .filter(offset -> dayOfWeekOffsetFunction.apply(offset) != null)
                    .map(dayOfWeekOffsetFunction::apply)
                    .min(BigDecimal::compareTo)
                    .orElse(null);
        }

        CPConfigMergedCeilingAndFloor higherClassFloor = ceilingAndFloorConfigForDates.get(new CPConfigMergedCeilingAndFloorPK(productId, currentDate, pricingAccomClass.getAccomType().getId()));
        return higherClassFloor != null ? getLowestOffset(higherClassFloor.getFloorRate(), dayOfWeekOffsetFunction, higherAccomTypes) : null;
    }

    private BigDecimal getLowerRoomClassFloorRate(Integer productId, Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates, PricingAccomClass pricingAccomClass, Set<CPConfigOffsetAccomType> lowerAccomTypes, LocalDate currentDate, Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction, OccupancyType baseOccupancyType, List<CPConfigOffsetAccomType> allPersistedOffsetConfigs) {
        if (pricingAccomClass.isPriceExcluded()) {
            List<CPConfigOffsetAccomType> offsetsProvidedForThisDOW = getOffsetProvidedForThisDOW(pricingAccomClass, lowerAccomTypes, dayOfWeekOffsetFunction, baseOccupancyType, allPersistedOffsetConfigs);
            return offsetsProvidedForThisDOW
                    .stream()
                    .filter(offset -> dayOfWeekOffsetFunction.apply(offset) != null)
                    .map(dayOfWeekOffsetFunction::apply)
                    .max(BigDecimal::compareTo)
                    .orElse(null);
        }

        CPConfigMergedCeilingAndFloor lowerClassFloor = ceilingAndFloorConfigForDates.get(new CPConfigMergedCeilingAndFloorPK(productId, currentDate, pricingAccomClass.getAccomType().getId()));
        return lowerClassFloor != null ? getHighestOffset(lowerClassFloor.getFloorRate(), dayOfWeekOffsetFunction, lowerAccomTypes) : null;
    }

    private BigDecimal getLowerRoomClassFloorLowestRate(Integer productId, Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates, PricingAccomClass pricingAccomClass, Set<CPConfigOffsetAccomType> lowerAccomTypes, LocalDate currentDate, Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction, OccupancyType baseOccupancyType, List<CPConfigOffsetAccomType> allPersistedOffsetConfigs) {
        if (pricingAccomClass.isPriceExcluded()) {
            List<CPConfigOffsetAccomType> offsetsProvidedForThisDOW = getOffsetProvidedForThisDOW(pricingAccomClass, lowerAccomTypes, dayOfWeekOffsetFunction, baseOccupancyType, allPersistedOffsetConfigs);
            return offsetsProvidedForThisDOW
                    .stream()
                    .filter(offset -> dayOfWeekOffsetFunction.apply(offset) != null)
                    .map(dayOfWeekOffsetFunction::apply)
                    .min(BigDecimal::compareTo)
                    .orElse(null);
        }

        CPConfigMergedCeilingAndFloor lowerClassFloor = ceilingAndFloorConfigForDates.get(new CPConfigMergedCeilingAndFloorPK(productId, currentDate, pricingAccomClass.getAccomType().getId()));
        return lowerClassFloor != null ? getLowestOffsetForLowerClassAccomType(lowerClassFloor.getFloorRate(), dayOfWeekOffsetFunction, lowerAccomTypes) : null;
    }


    private BigDecimal getHigherRoomClassCeilingRate(Integer productId, Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates, PricingAccomClass pricingAccomClass, Set<CPConfigOffsetAccomType> higherAccomTypes, LocalDate currentDate, Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction, OccupancyType baseOccupancyType, List<CPConfigOffsetAccomType> allPersistedOffsetConfigs) {
        if (pricingAccomClass.isPriceExcluded()) {
            List<CPConfigOffsetAccomType> offsetsProvidedForThisDOW = getOffsetProvidedForThisDOW(pricingAccomClass, higherAccomTypes, dayOfWeekOffsetFunction, baseOccupancyType, allPersistedOffsetConfigs);

            return offsetsProvidedForThisDOW
                    .stream()
                    .filter(offset -> dayOfWeekOffsetFunction.apply(offset) != null)
                    .map(dayOfWeekOffsetFunction::apply)
                    .min(BigDecimal::compareTo)
                    .orElse(null);
        }

        CPConfigMergedCeilingAndFloor higherClassCeiling = ceilingAndFloorConfigForDates.get(new CPConfigMergedCeilingAndFloorPK(productId, currentDate, pricingAccomClass.getAccomType().getId()));
        return higherClassCeiling != null ? getLowestOffset(higherClassCeiling.getCeilingRate(), dayOfWeekOffsetFunction, higherAccomTypes) : null;
    }

    private BigDecimal getLowerRoomClassCeilingRate(Integer productId, Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates, PricingAccomClass pricingAccomClass, Set<CPConfigOffsetAccomType> lowerAccomTypes, LocalDate currentDate, Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction, OccupancyType baseOccupancyType, List<CPConfigOffsetAccomType> allPersistedOffsetConfigs) {
        if (pricingAccomClass.isPriceExcluded()) {
            List<CPConfigOffsetAccomType> offsetsProvidedForThisDOW = getOffsetProvidedForThisDOW(pricingAccomClass, lowerAccomTypes, dayOfWeekOffsetFunction, baseOccupancyType, allPersistedOffsetConfigs);
            return offsetsProvidedForThisDOW
                    .stream()
                    .filter(offset -> dayOfWeekOffsetFunction.apply(offset) != null)
                    .map(dayOfWeekOffsetFunction::apply)
                    .max(BigDecimal::compareTo)
                    .orElse(null);
        }

        CPConfigMergedCeilingAndFloor lowerClassCeiling = ceilingAndFloorConfigForDates.get(new CPConfigMergedCeilingAndFloorPK(productId, currentDate, pricingAccomClass.getAccomType().getId()));
        return lowerClassCeiling != null ? getHighestOffset(lowerClassCeiling.getCeilingRate(), dayOfWeekOffsetFunction, lowerAccomTypes) : null;
    }

    private BigDecimal getLowerRoomClassCeilingAndFloorRate(Integer productId, Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates, PricingAccomClass pricingAccomClass, Set<CPConfigOffsetAccomType> lowerAccomTypes, LocalDate currentDate, Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction, OccupancyType baseOccupancyType, List<CPConfigOffsetAccomType> allPersistedOffsetConfigs) {
        if (pricingAccomClass.isPriceExcluded()) {
            List<CPConfigOffsetAccomType> offsetsProvidedForThisDOW = getOffsetProvidedForThisDOW(pricingAccomClass, lowerAccomTypes, dayOfWeekOffsetFunction, baseOccupancyType, allPersistedOffsetConfigs);
            return offsetsProvidedForThisDOW
                    .stream()
                    .filter(offset -> dayOfWeekOffsetFunction.apply(offset) != null)
                    .map(dayOfWeekOffsetFunction::apply)
                    .max(BigDecimal::compareTo)
                    .orElse(null);
        }

        CPConfigMergedCeilingAndFloor lowerClassCeiling = ceilingAndFloorConfigForDates.get(new CPConfigMergedCeilingAndFloorPK(productId, currentDate, pricingAccomClass.getAccomType().getId()));
        return lowerClassCeiling != null ? getHighestOffsetForCeilingAndFloor(lowerClassCeiling.getCeilingRate(), dayOfWeekOffsetFunction, lowerAccomTypes) : null;
    }

    private BigDecimal getLowerRoomClassCeilingLowestRate(Integer productId, Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates, PricingAccomClass pricingAccomClass, Set<CPConfigOffsetAccomType> lowerAccomTypes, LocalDate currentDate, Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction, OccupancyType baseOccupancyType, List<CPConfigOffsetAccomType> allPersistedOffsetConfigs) {
        if (pricingAccomClass.isPriceExcluded()) {
            List<CPConfigOffsetAccomType> offsetsProvidedForThisDOW = getOffsetProvidedForThisDOW(pricingAccomClass, lowerAccomTypes, dayOfWeekOffsetFunction, baseOccupancyType, allPersistedOffsetConfigs);
            return offsetsProvidedForThisDOW
                    .stream()
                    .filter(offset -> dayOfWeekOffsetFunction.apply(offset) != null)
                    .map(dayOfWeekOffsetFunction::apply)
                    .min(BigDecimal::compareTo)
                    .orElse(null);
        }

        CPConfigMergedCeilingAndFloor lowerClassCeiling = ceilingAndFloorConfigForDates.get(new CPConfigMergedCeilingAndFloorPK(productId, currentDate, pricingAccomClass.getAccomType().getId()));
        return lowerClassCeiling != null ? getLowestOffsetForLowerClassAccomType(lowerClassCeiling.getCeilingRate(), dayOfWeekOffsetFunction, lowerAccomTypes) : null;
    }

    //This is just for testing so that we can deterministically have the test run and not fail depending on when the test runs
    protected int getDayOfWeek(LocalDate date) {
        return date.getDayOfWeek();
    }

    public Set<String> validateSaveSeasonCeilingAndFloor(List<PricingBaseAccomType> basePricingAccomTypes, LocalDate startDate, LocalDate endDate) {
        Set<String> classesInViolation = new HashSet<>();
        List<PricingAccomClassRankDto> pricingAccomClassRankDtos = getPricingAccomClassesForProperty();
        boolean hasPriceExcludedAccomClass = pricingAccomClassRankDtos.stream().anyMatch(pricingAccomClassRankDto ->
                pricingAccomClassRankDto.getHigherPricingAccomClass().isPriceExcluded() ||
                        pricingAccomClassRankDto.getLowerPricingAccomClass().isPriceExcluded());
        List<CPConfigOffsetAccomType> cpConfigOffsetAccomTypes = new ArrayList<>();
        OccupancyType baseOccupancyType = null;
        if (hasPriceExcludedAccomClass) {
            cpConfigOffsetAccomTypes = pricingConfigurationOffsetService.retrieveOffsetConfig(true, basePricingAccomTypes.get(0).getProductID());
            baseOccupancyType = pricingConfigurationService.getBaseOccupancyType();
        }

        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsetConfigForBaseOccupancyType = pricingConfigurationService.findOffsetsForDatesAndBaseOccupancyType(startDate, endDate);

        //The only reason these would be empty is that no offsets have been configured and the normal validation should suffice
        if (MapUtils.isNotEmpty(offsetConfigForBaseOccupancyType)) {
            List<CPConfigOffsetAccomType> finalCpConfigOffsetAccomTypes = cpConfigOffsetAccomTypes;
            OccupancyType finalBaseOccupancyType = baseOccupancyType;
            pricingAccomClassRankDtos.forEach(pricingAccomClassRankDto -> {
                PricingAccomClass lowerPricingAccomClass = pricingAccomClassRankDto.getLowerPricingAccomClass();
                PricingAccomClass higherPricingAccomClass = pricingAccomClassRankDto.getHigherPricingAccomClass();

                LocalDate currentDate = new LocalDate(startDate);
                while (currentDate.compareTo(endDate) < 1) {
                    Function<PricingBaseAccomType, BigDecimal> dayOfWeekCeilingFunction = getDayOfWeekCeilingFunction(currentDate);
                    Function<PricingBaseAccomType, BigDecimal> dayOfWeekFloorFunction = getDayOfWeekFloorFunction(currentDate);
                    Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction = getDayOfWeekOffsetFunction(currentDate);

                    List<CPConfigMergedOffset> baseRoomTypeOffsetsForLowerRoomClass = collectBaseRoomTypeOffsetsForDate(offsetConfigForBaseOccupancyType, lowerPricingAccomClass, currentDate);
                    List<CPConfigMergedOffset> baseRoomTypeOffsetsForHigherRoomClass = collectBaseRoomTypeOffsetsForDate(offsetConfigForBaseOccupancyType, higherPricingAccomClass, currentDate);

                    BigDecimal lowerRoomClassCeilingRate;
                    BigDecimal higherRoomClassCeilingRate;
                    BigDecimal lowerRoomClassFloorRate;
                    BigDecimal higherRoomClassFloorRate;

                    if (configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)) {
                        List<AccomType> accomTypesInPricingBaseAccomTypes = basePricingAccomTypes.stream().map(PricingBaseAccomType::getAccomType).collect(Collectors.toList());
                        lowerRoomClassCeilingRate = accomTypesInPricingBaseAccomTypes.contains(lowerPricingAccomClass.getAccomType()) ?
                                getLowerRoomClassRate(basePricingAccomTypes, lowerPricingAccomClass, dayOfWeekCeilingFunction, finalCpConfigOffsetAccomTypes, dayOfWeekOffsetFunction, finalBaseOccupancyType, baseRoomTypeOffsetsForLowerRoomClass) :
                                null;
                        higherRoomClassCeilingRate = accomTypesInPricingBaseAccomTypes.contains(higherPricingAccomClass.getAccomType()) ?
                                getHigherRoomClassCeilingRate(basePricingAccomTypes, higherPricingAccomClass, dayOfWeekCeilingFunction, finalCpConfigOffsetAccomTypes, dayOfWeekOffsetFunction, finalBaseOccupancyType, baseRoomTypeOffsetsForHigherRoomClass) :
                                null;
                        lowerRoomClassFloorRate = accomTypesInPricingBaseAccomTypes.contains(lowerPricingAccomClass.getAccomType()) ?
                                getLowerRoomClassFloorRate(basePricingAccomTypes, lowerPricingAccomClass, dayOfWeekFloorFunction, finalCpConfigOffsetAccomTypes, dayOfWeekOffsetFunction, finalBaseOccupancyType, baseRoomTypeOffsetsForLowerRoomClass) :
                                null;
                        higherRoomClassFloorRate = accomTypesInPricingBaseAccomTypes.contains(higherPricingAccomClass.getAccomType()) ?
                                getHigherRoomClassFloorRate(basePricingAccomTypes, higherPricingAccomClass, dayOfWeekFloorFunction, finalCpConfigOffsetAccomTypes, dayOfWeekOffsetFunction, finalBaseOccupancyType, baseRoomTypeOffsetsForHigherRoomClass) :
                                null;
                    } else {
                        lowerRoomClassCeilingRate = getLowerRoomClassRate(basePricingAccomTypes, lowerPricingAccomClass, dayOfWeekCeilingFunction, finalCpConfigOffsetAccomTypes, dayOfWeekOffsetFunction, finalBaseOccupancyType, baseRoomTypeOffsetsForLowerRoomClass);
                        higherRoomClassCeilingRate = getHigherRoomClassCeilingRate(basePricingAccomTypes, higherPricingAccomClass, dayOfWeekCeilingFunction, finalCpConfigOffsetAccomTypes, dayOfWeekOffsetFunction, finalBaseOccupancyType, baseRoomTypeOffsetsForHigherRoomClass);
                        lowerRoomClassFloorRate = getLowerRoomClassFloorRate(basePricingAccomTypes, lowerPricingAccomClass, dayOfWeekFloorFunction, finalCpConfigOffsetAccomTypes, dayOfWeekOffsetFunction, finalBaseOccupancyType, baseRoomTypeOffsetsForLowerRoomClass);
                        higherRoomClassFloorRate = getHigherRoomClassFloorRate(basePricingAccomTypes, higherPricingAccomClass, dayOfWeekFloorFunction, finalCpConfigOffsetAccomTypes, dayOfWeekOffsetFunction, finalBaseOccupancyType, baseRoomTypeOffsetsForHigherRoomClass);
                    }

                    //These will be null only if offsets are not configured at all
                    if (lowerRoomClassCeilingRate != null && higherRoomClassCeilingRate != null &&
                            (lowerRoomClassCeilingRate.compareTo(higherRoomClassCeilingRate) > 0 || Objects.requireNonNull(lowerRoomClassFloorRate).compareTo(higherRoomClassFloorRate) > 0)) {
                        classesInViolation.add(lowerPricingAccomClass.getAccomClass().getName() + " - " + higherPricingAccomClass.getAccomClass().getName());
                    }

                    currentDate = currentDate.plusDays(1);
                }
            });
        }

        return classesInViolation;
    }

    public Set<String> validateSaveSeasonCeilingAndFloorByDailyBarPricingRuleType(List<PricingBaseAccomType> basePricingAccomTypes, LocalDate startDate, LocalDate endDate) {
        Set<String> classesInViolation = new LinkedHashSet<>();
        Set<Integer> baseRoomTypeIds = getRoomTypeIds(getAllPricingBaseAccomTypes());
        List<PricingAccomClassRankDto> pricingAccomClassRankDtos = getPricingAccomClassesForProperty();

        Integer productId = basePricingAccomTypes.get(0).getProductID();
        Product product = getProductById(productId);
        if(product.isIndependentProduct()){
            List<AccomType> accomTypeList = getAccomTypesForProduct(product);

            pricingAccomClassRankDtos = pricingAccomClassRankDtos.stream().filter(dto -> {
                PricingAccomClass lowerPricingAccomClass = dto.getLowerPricingAccomClass();
                PricingAccomClass higherPricingAccomClass = dto.getHigherPricingAccomClass();
                return accomTypeList.contains(lowerPricingAccomClass.getAccomType()) && accomTypeList.contains(higherPricingAccomClass.getAccomType());
            }).collect(toList());
        }

        boolean hasPriceExcludedAccomClass = pricingAccomClassRankDtos.stream().anyMatch(pricingAccomClassRankDto ->
                pricingAccomClassRankDto.getHigherPricingAccomClass().isPriceExcluded() ||
                        pricingAccomClassRankDto.getLowerPricingAccomClass().isPriceExcluded());
        List<CPConfigOffsetAccomType> cpConfigOffsetAccomTypes = new ArrayList<>();
        OccupancyType baseOccupancyType = null;
        if (hasPriceExcludedAccomClass) {
            cpConfigOffsetAccomTypes = getCpConfigOffsetAccomTypes(baseRoomTypeIds, basePricingAccomTypes.get(0).getProductID());
            baseOccupancyType = pricingConfigurationService.getBaseOccupancyType();
        }
        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsetConfigForBaseOccupancyType = getConfigMergedOffsetMap(startDate, endDate, baseRoomTypeIds);

        //The only reason these would be empty is that no offsets have been configured and the normal validation should suffice
        if (MapUtils.isNotEmpty(offsetConfigForBaseOccupancyType)) {
            List<CPConfigOffsetAccomType> finalCpConfigOffsetAccomTypes = cpConfigOffsetAccomTypes;
            OccupancyType finalBaseOccupancyType = baseOccupancyType;
            // start
            List<AccomClassMinPriceDiffSeason> minPriceDiffSeasons = crudService.findAll(AccomClassMinPriceDiffSeason.class);
            List<AccomClassMinPriceDiff> minPriceDiffDefaults = crudService.findAll(AccomClassMinPriceDiff.class);

            List<PricingAccomClassRankDto> sortedPricingAccomClassRankDtos = getSortedPricingAccomClassRankDtos(pricingAccomClassRankDtos);
            validateCeilingFloorSeason(basePricingAccomTypes, startDate, endDate, classesInViolation, finalCpConfigOffsetAccomTypes, finalBaseOccupancyType, minPriceDiffSeasons, minPriceDiffDefaults, sortedPricingAccomClassRankDtos, offsetConfigForBaseOccupancyType);
        }
        return classesInViolation;
    }

    private void validateCeilingFloorSeason(List<PricingBaseAccomType> basePricingAccomTypes, LocalDate startDate, LocalDate endDate, Set<String> classesInViolation, List<CPConfigOffsetAccomType> finalCpConfigOffsetAccomTypes, OccupancyType finalBaseOccupancyType, List<AccomClassMinPriceDiffSeason> minPriceDiffSeasons, List<AccomClassMinPriceDiff> minPriceDiffDefaults, List<PricingAccomClassRankDto> sortedPricingAccomClassRankDtos, Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> finalOffsetConfigForBaseOccupancyType) {
        sortedPricingAccomClassRankDtos.forEach(pricingAccomClassRankDto -> {
            CPRuleViolation cpRuleViolation = new CPRuleViolation();
            PricingAccomClass lowerPricingAccomClass = pricingAccomClassRankDto.getLowerPricingAccomClass();
            PricingAccomClass higherPricingAccomClass = pricingAccomClassRankDto.getHigherPricingAccomClass();

            List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = getMinRoomClassPriceDifferenceSeasonsByAccomClass(minPriceDiffSeasons, lowerPricingAccomClass, higherPricingAccomClass);
            AccomClassMinPriceDiff accomClassMinPriceDiffDefault = getMinRoomClassPriceDifferenceDefaultByAccomClass(minPriceDiffDefaults, lowerPricingAccomClass, higherPricingAccomClass);

            Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> lowerPricingAccomClassOffsets = getCpConfigMergedOffsetPKCPConfigMergedOffsetMap(finalOffsetConfigForBaseOccupancyType, lowerPricingAccomClass);
            LocalDate currentDate = new LocalDate(startDate);
            while (currentDate.compareTo(endDate) < 1) {
                validateCeilingFloorSeasonByDate(basePricingAccomTypes, finalCpConfigOffsetAccomTypes, finalBaseOccupancyType, lowerPricingAccomClassOffsets, cpRuleViolation, lowerPricingAccomClass, higherPricingAccomClass, accomClassMinPriceDiffSeasons, accomClassMinPriceDiffDefault, currentDate);
                currentDate = currentDate.plusDays(1);
            }
            String accomClassformatedViolation = formatViolationsModuleWise(cpRuleViolation, lowerPricingAccomClass, higherPricingAccomClass, true);
            if (StringUtils.isNotEmpty(accomClassformatedViolation)) {
                classesInViolation.add(accomClassformatedViolation);
            }
        });
    }

    private void validateCeilingFloorSeasonByDate(List<PricingBaseAccomType> basePricingAccomTypes, List<CPConfigOffsetAccomType> finalCpConfigOffsetAccomTypes, OccupancyType finalBaseOccupancyType, Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> finalOffsetConfigForBaseOccupancyType, CPRuleViolation cpRuleViolation, PricingAccomClass lowerPricingAccomClass, PricingAccomClass higherPricingAccomClass, List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons, AccomClassMinPriceDiff accomClassMinPriceDiffDefault, LocalDate currentDate) {
        Function<PricingBaseAccomType, BigDecimal> dayOfWeekCeilingFunction = getDayOfWeekCeilingFunction(currentDate);
        Function<PricingBaseAccomType, BigDecimal> dayOfWeekFloorFunction = getDayOfWeekFloorFunction(currentDate);
        Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction = getDayOfWeekOffsetFunction(currentDate);

        List<CPConfigMergedOffset> baseRoomTypeOffsetsForLowerRoomClass = collectBaseRoomTypeOffsetsForDate(finalOffsetConfigForBaseOccupancyType, lowerPricingAccomClass, currentDate);
        List<CPConfigMergedOffset> baseRoomTypeOffsetsForHigherRoomClass = collectBaseRoomTypeOffsetsForDate(finalOffsetConfigForBaseOccupancyType, higherPricingAccomClass, currentDate);
        BigDecimal higherRoomClassCeilingRate = getHigherRoomClassCeilingRate(basePricingAccomTypes, higherPricingAccomClass, dayOfWeekCeilingFunction, finalCpConfigOffsetAccomTypes, dayOfWeekOffsetFunction, finalBaseOccupancyType, baseRoomTypeOffsetsForHigherRoomClass);
        BigDecimal higherRoomClassFloorRate = getHigherRoomClassFloorRate(basePricingAccomTypes, higherPricingAccomClass, dayOfWeekFloorFunction, finalCpConfigOffsetAccomTypes, dayOfWeekOffsetFunction, finalBaseOccupancyType, baseRoomTypeOffsetsForHigherRoomClass);

        BigDecimal lowerRoomClassCeilingRate = null;
        BigDecimal lowerRoomClassFloorRate = null;
        if (getDailyBarPricingRuleType().equals(3)) {
            lowerRoomClassCeilingRate = getLowerRoomClassLowestRate(basePricingAccomTypes, lowerPricingAccomClass, dayOfWeekCeilingFunction, finalCpConfigOffsetAccomTypes, dayOfWeekOffsetFunction, finalBaseOccupancyType, baseRoomTypeOffsetsForLowerRoomClass);
            lowerRoomClassFloorRate = getLowerRoomClassFloorLowestRate(basePricingAccomTypes, lowerPricingAccomClass, dayOfWeekFloorFunction, finalCpConfigOffsetAccomTypes, dayOfWeekOffsetFunction, finalBaseOccupancyType, baseRoomTypeOffsetsForLowerRoomClass);
        } else {
            lowerRoomClassCeilingRate = getLowerRoomClassRate(basePricingAccomTypes, lowerPricingAccomClass, dayOfWeekCeilingFunction, finalCpConfigOffsetAccomTypes, dayOfWeekOffsetFunction, finalBaseOccupancyType, baseRoomTypeOffsetsForLowerRoomClass);
            lowerRoomClassFloorRate = getLowerRoomClassFloorRate(basePricingAccomTypes, lowerPricingAccomClass, dayOfWeekFloorFunction, finalCpConfigOffsetAccomTypes, dayOfWeekOffsetFunction, finalBaseOccupancyType, baseRoomTypeOffsetsForLowerRoomClass);
        }
        MinPriceDiffDTO minPriceDiffDTO = getMinPriceDiff(currentDate, accomClassMinPriceDiffSeasons, accomClassMinPriceDiffDefault);

        lowerRoomClassCeilingRate = lowerRoomClassCeilingRate != null ? lowerRoomClassCeilingRate.add(minPriceDiffDTO.getMinDiffValue()) : lowerRoomClassCeilingRate;
        lowerRoomClassFloorRate = lowerRoomClassFloorRate != null ? lowerRoomClassFloorRate.add(minPriceDiffDTO.getMinDiffValue()) : lowerRoomClassFloorRate;

        //These will be null only if offsets are not configured at all
        addViolationBasedOnOffsetSeasonValidationResult(cpRuleViolation, baseRoomTypeOffsetsForLowerRoomClass, higherRoomClassCeilingRate, higherRoomClassFloorRate, lowerRoomClassCeilingRate, lowerRoomClassFloorRate, minPriceDiffDTO);
    }

    private Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> getConfigMergedOffsetMap(LocalDate startDate, LocalDate endDate, Set<Integer> baseRoomTypeIds) {
        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsetConfigForBaseOccupancyType = pricingConfigurationService.findOffsetsForDatesAndBaseOccupancyType(startDate, endDate);
        offsetConfigForBaseOccupancyType = offsetConfigForBaseOccupancyType.entrySet().stream().filter(x -> !baseRoomTypeIds.contains(x.getKey().getAccomTypeId())).collect(Collectors.toMap(m -> m.getKey(), m -> m.getValue()));
        return offsetConfigForBaseOccupancyType;
    }

    private List<CPConfigOffsetAccomType> getCpConfigOffsetAccomTypes(Set<Integer> baseRoomTypeIds, int productID) {
        List<CPConfigOffsetAccomType> cpConfigOffsetAccomTypes;
        cpConfigOffsetAccomTypes = pricingConfigurationOffsetService.retrieveOffsetConfig(true, productID);
        cpConfigOffsetAccomTypes = cpConfigOffsetAccomTypes.stream().filter(x -> !baseRoomTypeIds.contains(x.getAccomType().getId())).collect(Collectors.toList());
        return cpConfigOffsetAccomTypes;
    }

    private BigDecimal getLowerRoomClassRate(List<PricingBaseAccomType> basePricingAccomTypes, PricingAccomClass pricingAccomClass,
                                             Function<PricingBaseAccomType, BigDecimal> dayOfWeekCeilingFunction, List<CPConfigOffsetAccomType> cpConfigOffsetAccomTypes,
                                             Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction, OccupancyType baseOccupancyType, List<CPConfigMergedOffset> baseRoomTypeOffsetsForDate) {
        if (pricingAccomClass.isPriceExcluded()) {
            return getLowerRoomClassRateForPriceExcludedRoomClass(pricingAccomClass, cpConfigOffsetAccomTypes, dayOfWeekOffsetFunction, baseOccupancyType);
        }
        Optional<CPConfigMergedOffset> lowerClassOffset = baseRoomTypeOffsetsForDate
                .stream()
                .max((a, b) -> a.getOffsetValue().compareTo(b.getOffsetValue()));

        //Base room Type has no offset
        BigDecimal lowerClassOffsetValue = null;
        if (isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable()) {
            lowerClassOffsetValue = !lowerClassOffset.isPresent() ? BigDecimal.ZERO : lowerClassOffset.get().getOffsetValue();
            lowerClassOffsetValue = BigDecimal.ZERO.compareTo(lowerClassOffsetValue) >= 0 ? BigDecimal.ZERO : lowerClassOffsetValue;
        } else {
            lowerClassOffsetValue = !lowerClassOffset.isPresent() ? BigDecimal.ZERO : BigDecimal.ZERO.compareTo(lowerClassOffset.get().getOffsetValue()) > 0 ? BigDecimal.ZERO :
                    lowerClassOffset.get().getOffsetValue();
        }

        OffsetMethod lowerClassOffsetMethod = lowerClassOffset.isPresent() ? lowerClassOffset.get().getOffsetMethod() : OffsetMethod.FIXED_OFFSET;

        PricingBaseAccomType lowerClassCeilingConfig = basePricingAccomTypes
                .stream()
                .filter(pricingBaseAccomType -> pricingBaseAccomType.getAccomType().equals(pricingAccomClass.getAccomType()))
                .findFirst()
                .orElse(null);
        return applyOffset(dayOfWeekCeilingFunction.apply(lowerClassCeilingConfig), lowerClassOffsetValue, lowerClassOffsetMethod);
    }

    private BigDecimal getLowerRoomClassLowestRate(List<PricingBaseAccomType> basePricingAccomTypes, PricingAccomClass pricingAccomClass,
                                                   Function<PricingBaseAccomType, BigDecimal> dayOfWeekCeilingFunction, List<CPConfigOffsetAccomType> cpConfigOffsetAccomTypes,
                                                   Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction, OccupancyType baseOccupancyType, List<CPConfigMergedOffset> baseRoomTypeOffsetsForDate) {
        if (pricingAccomClass.isPriceExcluded()) {
            return getLowerRoomClassRateForPriceExcludedRoomClass(pricingAccomClass, cpConfigOffsetAccomTypes, dayOfWeekOffsetFunction, baseOccupancyType);
        }

        Optional<CPConfigMergedOffset> lowerClassOffset = baseRoomTypeOffsetsForDate
                .stream()
                .min((a, b) -> a.getOffsetValue().compareTo(b.getOffsetValue()));

        //Base room Type has no offset
        BigDecimal lowerClassOffsetValue = !lowerClassOffset.isPresent() ? BigDecimal.ZERO : lowerClassOffset.get().getOffsetValue();

        lowerClassOffsetValue = BigDecimal.ZERO.compareTo(lowerClassOffsetValue) <= 0 ? BigDecimal.ZERO : lowerClassOffsetValue;

        //Max value is < Base room type of zero
        OffsetMethod lowerClassOffsetMethod = lowerClassOffset.isPresent() ? lowerClassOffset.get().getOffsetMethod() : OffsetMethod.FIXED_OFFSET;

        PricingBaseAccomType lowerClassCeilingConfig = basePricingAccomTypes
                .stream()
                .filter(pricingBaseAccomType -> pricingBaseAccomType.getAccomType().equals(pricingAccomClass.getAccomType()))
                .findFirst()
                .orElse(null);

        return applyOffset(dayOfWeekCeilingFunction.apply(lowerClassCeilingConfig), lowerClassOffsetValue, lowerClassOffsetMethod);
    }

    private BigDecimal getLowerRoomClassRateForPriceExcludedRoomClass(PricingAccomClass pricingAccomClass, List<CPConfigOffsetAccomType> cpConfigOffsetAccomTypes, Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction, OccupancyType baseOccupancyType) {
        return cpConfigOffsetAccomTypes
                .stream()
                .filter(offset -> offset.getAccomType().getAccomClass().equals(pricingAccomClass.getAccomClass()))
                .filter(offset -> offset.getOccupancyType().equals(baseOccupancyType))
                .filter(offset -> dayOfWeekOffsetFunction.apply(offset) != null)
                .map(dayOfWeekOffsetFunction::apply)
                .max(BigDecimal::compareTo)
                .orElse(null);
    }

    private BigDecimal getHigherRoomClassCeilingRate(List<PricingBaseAccomType> basePricingAccomTypes, PricingAccomClass pricingAccomClass,
                                                     Function<PricingBaseAccomType, BigDecimal> dayOfWeekCeilingFunction, List<CPConfigOffsetAccomType> cpConfigOffsetAccomTypes,
                                                     Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction, OccupancyType baseOccupancyType, List<CPConfigMergedOffset> baseRoomTypeOffsetsForDate) {
        return getHigherRoomClassRate(basePricingAccomTypes, pricingAccomClass, dayOfWeekCeilingFunction, cpConfigOffsetAccomTypes, dayOfWeekOffsetFunction, baseOccupancyType, baseRoomTypeOffsetsForDate);
    }

    private BigDecimal getLowerRoomClassFloorRate(List<PricingBaseAccomType> basePricingAccomTypes, PricingAccomClass pricingAccomClass,
                                                  Function<PricingBaseAccomType, BigDecimal> dayOfWeekCeilingFunction, List<CPConfigOffsetAccomType> cpConfigOffsetAccomTypes,
                                                  Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction, OccupancyType baseOccupancyType, List<CPConfigMergedOffset> baseRoomTypeOffsetsForDate) {
        return getLowerRoomClassRate(basePricingAccomTypes, pricingAccomClass, dayOfWeekCeilingFunction, cpConfigOffsetAccomTypes, dayOfWeekOffsetFunction, baseOccupancyType, baseRoomTypeOffsetsForDate);
    }

    private BigDecimal getLowerRoomClassFloorLowestRate(List<PricingBaseAccomType> basePricingAccomTypes, PricingAccomClass pricingAccomClass,
                                                        Function<PricingBaseAccomType, BigDecimal> dayOfWeekCeilingFunction, List<CPConfigOffsetAccomType> cpConfigOffsetAccomTypes,
                                                        Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction, OccupancyType baseOccupancyType, List<CPConfigMergedOffset> baseRoomTypeOffsetsForDate) {
        return getLowerRoomClassLowestRate(basePricingAccomTypes, pricingAccomClass, dayOfWeekCeilingFunction, cpConfigOffsetAccomTypes, dayOfWeekOffsetFunction, baseOccupancyType, baseRoomTypeOffsetsForDate);
    }

    private BigDecimal getHigherRoomClassFloorRate(List<PricingBaseAccomType> basePricingAccomTypes, PricingAccomClass pricingAccomClass,
                                                   Function<PricingBaseAccomType, BigDecimal> dayOfWeekCeilingFunction, List<CPConfigOffsetAccomType> cpConfigOffsetAccomTypes,
                                                   Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction, OccupancyType baseOccupancyType, List<CPConfigMergedOffset> baseRoomTypeOffsetsForDate) {
        return getHigherRoomClassRate(basePricingAccomTypes, pricingAccomClass, dayOfWeekCeilingFunction, cpConfigOffsetAccomTypes, dayOfWeekOffsetFunction, baseOccupancyType, baseRoomTypeOffsetsForDate);
    }

    private BigDecimal getHigherRoomClassRate(List<PricingBaseAccomType> basePricingAccomTypes, PricingAccomClass pricingAccomClass, Function<PricingBaseAccomType, BigDecimal> dayOfWeekCeilingFunction, List<CPConfigOffsetAccomType> cpConfigOffsetAccomTypes, Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction, OccupancyType baseOccupancyType, List<CPConfigMergedOffset> baseRoomTypeOffsetsForDate) {
        if (pricingAccomClass.isPriceExcluded()) {
            return cpConfigOffsetAccomTypes
                    .stream()
                    .filter(offset -> offset.getAccomType().getAccomClass().equals(pricingAccomClass.getAccomClass()))
                    .filter(offset -> offset.getOccupancyType().equals(baseOccupancyType))
                    .filter(offset -> dayOfWeekOffsetFunction.apply(offset) != null)
                    .map(dayOfWeekOffsetFunction::apply)
                    .min(BigDecimal::compareTo)
                    .orElse(null);
        }

        Optional<CPConfigMergedOffset> higherClassOffset = baseRoomTypeOffsetsForDate
                .stream()
                .min((a, b) -> a.getOffsetValue().compareTo(b.getOffsetValue()));

        //Base room Type has offset of zero
        BigDecimal higherClassOffsetValue = !higherClassOffset.isPresent() ? BigDecimal.ZERO :
                //Min value is > Base room type of zero
                BigDecimal.ZERO.compareTo(higherClassOffset.get().getOffsetValue()) < 0 ? BigDecimal.ZERO :
                        higherClassOffset.get().getOffsetValue();

        OffsetMethod higherClassOffsetMethod = higherClassOffset.isPresent() ? higherClassOffset.get().getOffsetMethod() : OffsetMethod.FIXED_OFFSET;

        PricingBaseAccomType higherClassCeilingConfig = basePricingAccomTypes
                .stream()
                .filter(pricingBaseAccomType -> pricingBaseAccomType.getAccomType().equals(pricingAccomClass.getAccomType()))
                .findFirst()
                .orElse(null);

        return applyOffset(dayOfWeekCeilingFunction.apply(higherClassCeilingConfig), higherClassOffsetValue, higherClassOffsetMethod);
    }

    private List<CPConfigMergedOffset> collectBaseRoomTypeOffsetsForDate(Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsetConfigForSingle, PricingAccomClass pricingAccomClass, final LocalDate currentDate) {
        List<Integer> baseAccomTypeIds = pricingAccomClass.getAccomClass().getAccomTypes()
                .stream()
                .map(AccomType::getId)
                .collect(Collectors.toList());

        return offsetConfigForSingle.entrySet()
                .stream()
                .filter(e -> baseAccomTypeIds.contains(e.getKey().getAccomTypeId()))
                .filter(e -> e.getKey().getArrivalDate().compareTo(currentDate) == 0)
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());
    }

    private Function<CPConfigOffsetAccomType, BigDecimal> getDayOfWeekOffsetFunction(final LocalDate currentDate) {
        Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction = CPConfigOffsetAccomType::getSundayOffsetValueWithTax;
        int dayOfWeek = getDayOfWeek(currentDate);

        switch (dayOfWeek) {
            case DateTimeConstants.MONDAY:
                dayOfWeekOffsetFunction = CPConfigOffsetAccomType::getMondayOffsetValueWithTax;
                break;
            case DateTimeConstants.TUESDAY:
                dayOfWeekOffsetFunction = CPConfigOffsetAccomType::getTuesdayOffsetValueWithTax;
                break;
            case DateTimeConstants.WEDNESDAY:
                dayOfWeekOffsetFunction = CPConfigOffsetAccomType::getWednesdayOffsetValueWithTax;
                break;
            case DateTimeConstants.THURSDAY:
                dayOfWeekOffsetFunction = CPConfigOffsetAccomType::getThursdayOffsetValueWithTax;
                break;
            case DateTimeConstants.FRIDAY:
                dayOfWeekOffsetFunction = CPConfigOffsetAccomType::getFridayOffsetValueWithTax;
                break;
            case DateTimeConstants.SATURDAY:
                dayOfWeekOffsetFunction = CPConfigOffsetAccomType::getSaturdayOffsetValueWithTax;
                break;
            default:
                break;
        }

        return dayOfWeekOffsetFunction;
    }

    private Function<PricingBaseAccomType, BigDecimal> getDayOfWeekCeilingFunction(LocalDate currentDate) {
        int dayOfWeek = getDayOfWeek(currentDate);
        Function<PricingBaseAccomType, BigDecimal> dayOfWeekCeilingFunction = PricingBaseAccomType::getSundayCeilingRateWithTax;

        switch (dayOfWeek) {
            case DateTimeConstants.MONDAY:
                dayOfWeekCeilingFunction = PricingBaseAccomType::getMondayCeilingRateWithTax;
                break;
            case DateTimeConstants.TUESDAY:
                dayOfWeekCeilingFunction = PricingBaseAccomType::getTuesdayCeilingRateWithTax;
                break;
            case DateTimeConstants.WEDNESDAY:
                dayOfWeekCeilingFunction = PricingBaseAccomType::getWednesdayCeilingRateWithTax;
                break;
            case DateTimeConstants.THURSDAY:
                dayOfWeekCeilingFunction = PricingBaseAccomType::getThursdayCeilingRateWithTax;
                break;
            case DateTimeConstants.FRIDAY:
                dayOfWeekCeilingFunction = PricingBaseAccomType::getFridayCeilingRateWithTax;
                break;
            case DateTimeConstants.SATURDAY:
                dayOfWeekCeilingFunction = PricingBaseAccomType::getSaturdayCeilingRateWithTax;
                break;
            default:
                break;
        }
        return dayOfWeekCeilingFunction;
    }

    private Function<PricingBaseAccomType, BigDecimal> getDayOfWeekFloorFunction(LocalDate currentDate) {
        int dayOfWeek = getDayOfWeek(currentDate);
        Function<PricingBaseAccomType, BigDecimal> dayOfWeekFloorFunction = PricingBaseAccomType::getSundayFloorRateWithTax;

        switch (dayOfWeek) {
            case DateTimeConstants.MONDAY:
                dayOfWeekFloorFunction = PricingBaseAccomType::getMondayFloorRateWithTax;
                break;
            case DateTimeConstants.TUESDAY:
                dayOfWeekFloorFunction = PricingBaseAccomType::getTuesdayFloorRateWithTax;
                break;
            case DateTimeConstants.WEDNESDAY:
                dayOfWeekFloorFunction = PricingBaseAccomType::getWednesdayFloorRateWithTax;
                break;
            case DateTimeConstants.THURSDAY:
                dayOfWeekFloorFunction = PricingBaseAccomType::getThursdayFloorRateWithTax;
                break;
            case DateTimeConstants.FRIDAY:
                dayOfWeekFloorFunction = PricingBaseAccomType::getFridayFloorRateWithTax;
                break;
            case DateTimeConstants.SATURDAY:
                dayOfWeekFloorFunction = PricingBaseAccomType::getSaturdayFloorRateWithTax;
                break;
            default:
                break;
        }
        return dayOfWeekFloorFunction;
    }

    @SuppressWarnings("unchecked")
    protected List<PricingAccomClassRankDto> getPricingAccomClassesForProperty() {
        // Get all of the AccomClassPriceRank records
        List<AccomClassPriceRank> accomClassPriceRanks = accomClassPriceRankService.getAccomClassPriceRank();
        List<PricingAccomClass> pricingAccomClasses = pricingConfigurationService.getPricingAccomClasses();
        return getPricingAccomClassesForProperty(accomClassPriceRanks, pricingAccomClasses);
    }

    @SuppressWarnings("unchecked")
    public List<PricingAccomClassRankDto> getPricingAccomClassesForProperty(List<AccomClassPriceRank> accomClassPriceRanks, List<PricingAccomClass> pricingAccomClassList) {
        List<PricingAccomClassRankDto> pricingAccomClassRankDtos = new ArrayList<>();

        /**
         * When using the Rooms Configuration, the validating of the price ranking should come from the
         * AccomClassPriceRank instead of the AccomClass rank order.
         */
        // Get the PricingAccomClasses and put into a Map for easier access to build the PricingAccomClassRankDtos
        Map<AccomClass, PricingAccomClass> pricingAccomClasses = pricingAccomClassList.stream().collect(Collectors.toMap(PricingAccomClass::getAccomClass, Function.identity()));

        // Iterate over the accomClassPriceRanks to build pairings containing lower/higher
        if (CollectionUtils.isNotEmpty(accomClassPriceRanks)) {
            accomClassPriceRanks.forEach(accomClassPriceRank -> {
                // Get the lower/higher PricingAccomClasses for the ranks
                PricingAccomClass lowerAccomClass = pricingAccomClasses.get(accomClassPriceRank.getLowerRankAccomClass());
                PricingAccomClass higherAccomClass = pricingAccomClasses.get(accomClassPriceRank.getHigherRankAccomClass());

                // If there are no corresponding PricingAccomClass objects for the rank, do not include them in the validation
                if (lowerAccomClass != null && higherAccomClass != null) {
                    pricingAccomClassRankDtos.add(new PricingAccomClassRankDto(lowerAccomClass, higherAccomClass));
                }
            });
        }

        return pricingAccomClassRankDtos;
    }


    @ForTesting
    protected boolean isValidDefaultRoomClassConfig(PricingBaseAccomType lowerClassBaseConfig, Set<CPConfigOffsetAccomType> lowerClassAccomTypes, boolean lowerClassPriceExcluded, PricingBaseAccomType higherClassBaseConfig, Set<CPConfigOffsetAccomType> higherClassAccomTypes, boolean higherClassPriceExcluded) {
        return isValidDefaultRoomClassConfigForDayOfWeek(PricingBaseAccomType::getSundayCeilingRateWithTax, PricingBaseAccomType::getSundayFloorRateWithTax, CPConfigOffsetAccomType::getSundayOffsetValueWithTax, lowerClassBaseConfig, lowerClassAccomTypes, lowerClassPriceExcluded, higherClassBaseConfig, higherClassAccomTypes, higherClassPriceExcluded)
                && isValidDefaultRoomClassConfigForDayOfWeek(PricingBaseAccomType::getMondayCeilingRateWithTax, PricingBaseAccomType::getMondayFloorRateWithTax, CPConfigOffsetAccomType::getMondayOffsetValueWithTax, lowerClassBaseConfig, lowerClassAccomTypes, lowerClassPriceExcluded, higherClassBaseConfig, higherClassAccomTypes, higherClassPriceExcluded)
                && isValidDefaultRoomClassConfigForDayOfWeek(PricingBaseAccomType::getTuesdayCeilingRateWithTax, PricingBaseAccomType::getTuesdayFloorRateWithTax, CPConfigOffsetAccomType::getTuesdayOffsetValueWithTax, lowerClassBaseConfig, lowerClassAccomTypes, lowerClassPriceExcluded, higherClassBaseConfig, higherClassAccomTypes, higherClassPriceExcluded)
                && isValidDefaultRoomClassConfigForDayOfWeek(PricingBaseAccomType::getWednesdayCeilingRateWithTax, PricingBaseAccomType::getWednesdayFloorRateWithTax, CPConfigOffsetAccomType::getWednesdayOffsetValueWithTax, lowerClassBaseConfig, lowerClassAccomTypes, lowerClassPriceExcluded, higherClassBaseConfig, higherClassAccomTypes, higherClassPriceExcluded)
                && isValidDefaultRoomClassConfigForDayOfWeek(PricingBaseAccomType::getThursdayCeilingRateWithTax, PricingBaseAccomType::getThursdayFloorRateWithTax, CPConfigOffsetAccomType::getThursdayOffsetValueWithTax, lowerClassBaseConfig, lowerClassAccomTypes, lowerClassPriceExcluded, higherClassBaseConfig, higherClassAccomTypes, higherClassPriceExcluded)
                && isValidDefaultRoomClassConfigForDayOfWeek(PricingBaseAccomType::getFridayCeilingRateWithTax, PricingBaseAccomType::getFridayFloorRateWithTax, CPConfigOffsetAccomType::getFridayOffsetValueWithTax, lowerClassBaseConfig, lowerClassAccomTypes, lowerClassPriceExcluded, higherClassBaseConfig, higherClassAccomTypes, higherClassPriceExcluded)
                && isValidDefaultRoomClassConfigForDayOfWeek(PricingBaseAccomType::getSaturdayCeilingRateWithTax, PricingBaseAccomType::getSaturdayFloorRateWithTax, CPConfigOffsetAccomType::getSaturdayOffsetValueWithTax, lowerClassBaseConfig, lowerClassAccomTypes, lowerClassPriceExcluded, higherClassBaseConfig, higherClassAccomTypes, higherClassPriceExcluded);
    }


    @ForTesting
    protected CPValidationDTO isValidDefaultRoomClassConfiguration(PricingBaseAccomType lowerClassBaseConfig, Set<CPConfigOffsetAccomType> lowerClassAccomTypes, boolean lowerClassPriceExcluded, PricingBaseAccomType higherClassBaseConfig, Set<CPConfigOffsetAccomType> higherClassAccomTypes, boolean higherClassPriceExcluded, AccomClassMinPriceDiff accomClassMinRCDiff) {
        CPValidationDTO cpValidationDTO = new CPValidationDTO();
        CPValidationResultDTO cpValidationResultDTO;

        BigDecimal minRoomClassDiffValue = getMinPriceDiffFromDefault(7, accomClassMinRCDiff);
        cpValidationResultDTO = isValidDefaultRoomClassConfigForDayOfWeek(PricingBaseAccomType::getSundayCeilingRateWithTax, PricingBaseAccomType::getSundayFloorRateWithTax, CPConfigOffsetAccomType::getSundayOffsetValueWithTax, lowerClassBaseConfig, lowerClassAccomTypes, lowerClassPriceExcluded, higherClassBaseConfig, higherClassAccomTypes, higherClassPriceExcluded, minRoomClassDiffValue);
        addViolationDetails(cpValidationDTO, cpValidationResultDTO, minRoomClassDiffValue);

        minRoomClassDiffValue = getMinPriceDiffFromDefault(1, accomClassMinRCDiff);
        cpValidationResultDTO = isValidDefaultRoomClassConfigForDayOfWeek(PricingBaseAccomType::getMondayCeilingRateWithTax, PricingBaseAccomType::getMondayFloorRateWithTax, CPConfigOffsetAccomType::getMondayOffsetValueWithTax, lowerClassBaseConfig, lowerClassAccomTypes, lowerClassPriceExcluded, higherClassBaseConfig, higherClassAccomTypes, higherClassPriceExcluded, minRoomClassDiffValue);
        addViolationDetails(cpValidationDTO, cpValidationResultDTO, minRoomClassDiffValue);

        minRoomClassDiffValue = getMinPriceDiffFromDefault(2, accomClassMinRCDiff);
        cpValidationResultDTO = isValidDefaultRoomClassConfigForDayOfWeek(PricingBaseAccomType::getTuesdayCeilingRateWithTax, PricingBaseAccomType::getTuesdayFloorRateWithTax, CPConfigOffsetAccomType::getTuesdayOffsetValueWithTax, lowerClassBaseConfig, lowerClassAccomTypes, lowerClassPriceExcluded, higherClassBaseConfig, higherClassAccomTypes, higherClassPriceExcluded, minRoomClassDiffValue);
        addViolationDetails(cpValidationDTO, cpValidationResultDTO, minRoomClassDiffValue);

        minRoomClassDiffValue = getMinPriceDiffFromDefault(3, accomClassMinRCDiff);
        cpValidationResultDTO = isValidDefaultRoomClassConfigForDayOfWeek(PricingBaseAccomType::getWednesdayCeilingRateWithTax, PricingBaseAccomType::getWednesdayFloorRateWithTax, CPConfigOffsetAccomType::getWednesdayOffsetValueWithTax, lowerClassBaseConfig, lowerClassAccomTypes, lowerClassPriceExcluded, higherClassBaseConfig, higherClassAccomTypes, higherClassPriceExcluded, minRoomClassDiffValue);
        addViolationDetails(cpValidationDTO, cpValidationResultDTO, minRoomClassDiffValue);

        minRoomClassDiffValue = getMinPriceDiffFromDefault(4, accomClassMinRCDiff);
        cpValidationResultDTO = isValidDefaultRoomClassConfigForDayOfWeek(PricingBaseAccomType::getThursdayCeilingRateWithTax, PricingBaseAccomType::getThursdayFloorRateWithTax, CPConfigOffsetAccomType::getThursdayOffsetValueWithTax, lowerClassBaseConfig, lowerClassAccomTypes, lowerClassPriceExcluded, higherClassBaseConfig, higherClassAccomTypes, higherClassPriceExcluded, minRoomClassDiffValue);
        addViolationDetails(cpValidationDTO, cpValidationResultDTO, minRoomClassDiffValue);

        minRoomClassDiffValue = getMinPriceDiffFromDefault(5, accomClassMinRCDiff);
        cpValidationResultDTO = isValidDefaultRoomClassConfigForDayOfWeek(PricingBaseAccomType::getFridayCeilingRateWithTax, PricingBaseAccomType::getFridayFloorRateWithTax, CPConfigOffsetAccomType::getFridayOffsetValueWithTax, lowerClassBaseConfig, lowerClassAccomTypes, lowerClassPriceExcluded, higherClassBaseConfig, higherClassAccomTypes, higherClassPriceExcluded, minRoomClassDiffValue);
        addViolationDetails(cpValidationDTO, cpValidationResultDTO, minRoomClassDiffValue);

        minRoomClassDiffValue = getMinPriceDiffFromDefault(6, accomClassMinRCDiff);
        cpValidationResultDTO = isValidDefaultRoomClassConfigForDayOfWeek(PricingBaseAccomType::getSaturdayCeilingRateWithTax, PricingBaseAccomType::getSaturdayFloorRateWithTax, CPConfigOffsetAccomType::getSaturdayOffsetValueWithTax, lowerClassBaseConfig, lowerClassAccomTypes, lowerClassPriceExcluded, higherClassBaseConfig, higherClassAccomTypes, higherClassPriceExcluded, minRoomClassDiffValue);
        addViolationDetails(cpValidationDTO, cpValidationResultDTO, minRoomClassDiffValue);
        return cpValidationDTO;
    }

    private void addViolationDetails(CPValidationDTO cpValidationDTO, CPValidationResultDTO cpValidationResultDTO, BigDecimal minRoomClassDiffValue) {
        if (!cpValidationResultDTO.isValidationFlag() && BigDecimal.ZERO.compareTo(cpValidationResultDTO.getOffsetValue()) != 0) {
            cpValidationDTO.getOffsetViolation().add(Boolean.valueOf(cpValidationResultDTO.isValidationFlag()));
        }
        if (!cpValidationResultDTO.isValidationFlag() && BigDecimal.ZERO.compareTo(minRoomClassDiffValue) != 0) {
            cpValidationDTO.getMinRoomClassViolation().add(Boolean.valueOf(cpValidationResultDTO.isValidationFlag()));
        }
        cpValidationDTO.getCeilingFloorViolation().add(cpValidationResultDTO.isValidationFlag());
    }

    private boolean isValidDefaultRoomClassConfigForDayOfWeek(Function<PricingBaseAccomType, BigDecimal> dayOfWeekCeilingBaseFunction, Function<PricingBaseAccomType, BigDecimal> dayOfWeekBaseFloorFunction, Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction,
                                                              PricingBaseAccomType lowerClassBaseConfig, Set<CPConfigOffsetAccomType> lowerClassAccomTypes, boolean lowerClassPriceExcluded,
                                                              PricingBaseAccomType higherClassBaseConfig, Set<CPConfigOffsetAccomType> higherClassAccomTypes, boolean higherClassPriceExcluded) {
        if ((lowerClassBaseConfig == null && !lowerClassPriceExcluded) || (higherClassBaseConfig == null && !higherClassPriceExcluded)) {
            //No Base ceilings setup yet
            return true;
        }

        //If a class is price excluded the base ceiling won't be used
        BigDecimal lowerBaseCeiling = lowerClassPriceExcluded ? null : dayOfWeekCeilingBaseFunction.apply(lowerClassBaseConfig);
        BigDecimal higherBaseCeiling = higherClassPriceExcluded ? null : dayOfWeekCeilingBaseFunction.apply(higherClassBaseConfig);

        BigDecimal lowerBaseFloor = lowerClassPriceExcluded ? null : dayOfWeekBaseFloorFunction.apply(lowerClassBaseConfig);
        BigDecimal higherBaseFloor = higherClassPriceExcluded ? null : dayOfWeekBaseFloorFunction.apply(higherClassBaseConfig);

        BigDecimal lowerRoomClassCeilingRate = getHighestOffsetForCeilingAndFloor(lowerBaseCeiling, dayOfWeekOffsetFunction, lowerClassAccomTypes);
        BigDecimal higherRoomClassCeilingRate = getLowestOffset(higherBaseCeiling, dayOfWeekOffsetFunction, higherClassAccomTypes);

        BigDecimal lowerRoomClassFloorRate = getHighestOffsetForCeilingAndFloor(lowerBaseFloor, dayOfWeekOffsetFunction, lowerClassAccomTypes);
        BigDecimal higherRoomClassFloorRate = getLowestOffset(higherBaseFloor, dayOfWeekOffsetFunction, higherClassAccomTypes);

        //If we get this far these are null because offsets are not yet configured
        return lowerRoomClassCeilingRate == null || higherRoomClassCeilingRate == null ||
                (lowerRoomClassCeilingRate.compareTo(higherRoomClassCeilingRate) < 1 && Objects.requireNonNull(lowerRoomClassFloorRate).compareTo(higherRoomClassFloorRate) < 1);
    }

    private CPValidationResultDTO isValidDefaultRoomClassConfigForDayOfWeek(Function<PricingBaseAccomType, BigDecimal> dayOfWeekCeilingBaseFunction, Function<PricingBaseAccomType, BigDecimal> dayOfWeekBaseFloorFunction, Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction,
                                                                            PricingBaseAccomType lowerClassBaseConfig, Set<CPConfigOffsetAccomType> lowerClassAccomTypes, boolean lowerClassPriceExcluded,
                                                                            PricingBaseAccomType higherClassBaseConfig, Set<CPConfigOffsetAccomType> higherClassAccomTypes, boolean higherClassPriceExcluded, BigDecimal priceDiffValue) {
        CPValidationResultDTO cpValidationResultDTO = new CPValidationResultDTO();
        if ((lowerClassBaseConfig == null && !lowerClassPriceExcluded) || (higherClassBaseConfig == null && !higherClassPriceExcluded)) {
            cpValidationResultDTO.setValidationFlag(true);
        }

        //If a class is price excluded the base ceiling won't be used
        BigDecimal lowerBaseCeiling = lowerClassPriceExcluded || lowerClassBaseConfig == null ? null : dayOfWeekCeilingBaseFunction.apply(lowerClassBaseConfig);
        BigDecimal higherBaseCeiling = higherClassPriceExcluded || higherClassBaseConfig == null ? null : dayOfWeekCeilingBaseFunction.apply(higherClassBaseConfig);

        BigDecimal lowerBaseFloor = lowerClassPriceExcluded || lowerClassBaseConfig == null ? null : dayOfWeekBaseFloorFunction.apply(lowerClassBaseConfig);
        BigDecimal higherBaseFloor = higherClassPriceExcluded || higherClassBaseConfig == null ? null : dayOfWeekBaseFloorFunction.apply(higherClassBaseConfig);

        BigDecimal lowerRoomClassCeilingRate = null;
        BigDecimal higherRoomClassCeilingRate = null;
        BigDecimal lowerRoomClassFloorRate = null;
        BigDecimal higherRoomClassFloorRate = null;

        if ((isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable() && getDailyBarPricingRuleType().equals(3))) {
            lowerRoomClassCeilingRate = getLowestOffsetForLowerClassAccomType(lowerBaseCeiling, dayOfWeekOffsetFunction, lowerClassAccomTypes);
            lowerRoomClassFloorRate = getLowestOffsetForLowerClassAccomType(lowerBaseFloor, dayOfWeekOffsetFunction, lowerClassAccomTypes);
        } else {
            lowerRoomClassCeilingRate = getHighestOffset(lowerBaseCeiling, dayOfWeekOffsetFunction, lowerClassAccomTypes);
            lowerRoomClassFloorRate = getHighestOffset(lowerBaseFloor, dayOfWeekOffsetFunction, lowerClassAccomTypes);
        }

        if (lowerRoomClassCeilingRate != null && lowerBaseCeiling != null) {
            cpValidationResultDTO.setOffsetValue(lowerRoomClassCeilingRate.subtract(lowerBaseCeiling));
        }

        higherRoomClassCeilingRate = getLowestOffset(higherBaseCeiling, dayOfWeekOffsetFunction, higherClassAccomTypes);
        higherRoomClassFloorRate = getLowestOffset(higherBaseFloor, dayOfWeekOffsetFunction, higherClassAccomTypes);

        lowerRoomClassCeilingRate = lowerRoomClassCeilingRate != null ? lowerRoomClassCeilingRate.add(priceDiffValue) : lowerRoomClassCeilingRate;
        lowerRoomClassFloorRate = lowerRoomClassFloorRate != null ? lowerRoomClassFloorRate.add(priceDiffValue) : lowerRoomClassFloorRate;

        //If we get this far these are null because offsets are not yet configured
        cpValidationResultDTO.setValidationFlag(lowerRoomClassCeilingRate == null || higherRoomClassCeilingRate == null ||
                (lowerRoomClassCeilingRate.compareTo(higherRoomClassCeilingRate) < 1 && Objects.requireNonNull(lowerRoomClassFloorRate).compareTo(higherRoomClassFloorRate) < 1));
        return cpValidationResultDTO;
    }

    private BigDecimal getHighestOffset(BigDecimal rate, Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction, Set<CPConfigOffsetAccomType> lowerClassAccomTypes) {
        Optional<CPConfigOffsetAccomType> highestOffset = lowerClassAccomTypes.stream()
                .filter(offset -> dayOfWeekOffsetFunction.apply(offset) != null || offset.getOffsetMethod() == OffsetMethod.FIXED_PRICE)
                .max((o1, o2) -> applyOffset(rate, dayOfWeekOffsetFunction.apply(o1), o1.getOffsetMethod()).compareTo(applyOffset(rate, dayOfWeekOffsetFunction.apply(o2), o2.getOffsetMethod())));

        return highestOffset.isPresent() ? applyOffsetByConsideringBaseOffset(rate, dayOfWeekOffsetFunction.apply(highestOffset.get()), highestOffset.get().getOffsetMethod(), 2) : rate;
    }

    private BigDecimal getHighestOffsetForCeilingAndFloor(BigDecimal rate, Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction, Set<CPConfigOffsetAccomType> lowerClassAccomTypes) {
        Optional<CPConfigOffsetAccomType> highestOffset = lowerClassAccomTypes.stream()
                .filter(offset -> dayOfWeekOffsetFunction.apply(offset) != null && BigDecimal.ZERO.compareTo(dayOfWeekOffsetFunction.apply(offset)) < 0 || offset.getOffsetMethod() == OffsetMethod.FIXED_PRICE)
                .max((o1, o2) -> applyOffset(rate, dayOfWeekOffsetFunction.apply(o1), o1.getOffsetMethod()).compareTo(applyOffset(rate, dayOfWeekOffsetFunction.apply(o2), o2.getOffsetMethod())));

        return highestOffset.isPresent() ? applyOffset(rate, dayOfWeekOffsetFunction.apply(highestOffset.get()), highestOffset.get().getOffsetMethod()) : rate;
    }

    private BigDecimal getLowestOffset(BigDecimal rate, Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction, Set<CPConfigOffsetAccomType> higherClassAccomTypes) {
        Optional<CPConfigOffsetAccomType> lowestOffset = higherClassAccomTypes.stream()
                .filter(offset -> dayOfWeekOffsetFunction.apply(offset) != null && (BigDecimal.ZERO.compareTo(dayOfWeekOffsetFunction.apply(offset)) > 0 || offset.getOffsetMethod() == OffsetMethod.FIXED_PRICE))
                .min((o1, o2) -> applyOffset(rate, dayOfWeekOffsetFunction.apply(o1), o1.getOffsetMethod()).compareTo(applyOffset(rate, dayOfWeekOffsetFunction.apply(o2), o2.getOffsetMethod())));

        return lowestOffset.isPresent() ? applyOffset(rate, dayOfWeekOffsetFunction.apply(lowestOffset.get()), lowestOffset.get().getOffsetMethod()) : rate;
    }

    private BigDecimal applyOffset(BigDecimal baseNumber, BigDecimal offsetValue, OffsetMethod offsetMethod) {
        if (OffsetMethod.PERCENTAGE == offsetMethod) {
            if (baseNumber != null) {
                offsetValue = baseNumber.multiply(offsetValue.divide(BigDecimal.valueOf(100))).setScale(2, RoundingMode.HALF_UP);
            }
        } else if (OffsetMethod.FIXED_PRICE == offsetMethod) {
            return offsetValue;
        }

        return offsetValue != null ? (baseNumber != null ? (baseNumber.add(offsetValue)) : offsetValue) : baseNumber;
    }

    private BigDecimal applyOffsetWithPossibleNullValue(BigDecimal baseNumber, BigDecimal offsetValue, OffsetMethod offsetMethod) {
        if (Objects.nonNull(offsetValue) && Objects.nonNull(offsetMethod)) {
            return applyOffset(baseNumber, offsetValue, offsetMethod);
        }
        return baseNumber;
    }

    private Set<CPConfigOffsetAccomType> getDefaultSingleOffsetsByAccomClass(List<CPConfigOffsetAccomType> offsets, PricingAccomClass pricingAccomClass, OccupancyType baseOccupancyType) {
        Set<CPConfigOffsetAccomType> offsetAccomTypes = offsets.stream()
                .filter(offset -> offset.getOccupancyType().equals(baseOccupancyType))
                .filter(offset -> pricingAccomClass.getAccomClass().getAccomTypes().contains(offset.getAccomType()))
                .filter(offset -> offset.getStartDate() == null)
                .collect(Collectors.toSet());
        if (pricingAccomClass.isPriceExcluded()) {
            CPConfigOffsetAccomType priceExcludedBaseRoomTypeOffset = offsetAccomTypes.stream().filter(cpConfigOffsetAccomType -> cpConfigOffsetAccomType.getAccomType().equals(pricingAccomClass.getAccomType())).findFirst().orElse(null);
            if (priceExcludedBaseRoomTypeOffset != null) {
                updateRowRepresentingBaseRoomTypeOffset(priceExcludedBaseRoomTypeOffset);
            }
        }
        if (CollectionUtils.isEmpty(offsetAccomTypes)) {
            offsetAccomTypes.add(createRowRepresentingBaseRoomTypeOffset(pricingAccomClass, baseOccupancyType));
        }
        return offsetAccomTypes;
    }

    private Set<CPConfigOffsetAccomType> getDefaultOffsetsByAccomClass(List<CPConfigOffsetAccomType> offsets, PricingAccomClass pricingAccomClass, OccupancyType baseOccupancyType) {
        Set<CPConfigOffsetAccomType> offsetAccomTypes = offsets.stream()
                //  .filter(offset -> offset.getOccupancyType().equals(baseOccupancyType))
                .filter(offset -> pricingAccomClass.getAccomClass().getAccomTypes().contains(offset.getAccomType()))
                // .filter(offset -> offset.getStartDate() == null)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(offsetAccomTypes)) {
            offsetAccomTypes.add(createRowRepresentingBaseRoomTypeOffset(pricingAccomClass, baseOccupancyType));
        }

        return offsetAccomTypes;
    }

    private Set<CPConfigOffsetAccomType> getSeasonsSingleOffsetsByAccomClass(List<CPConfigOffsetAccomType> offsets, PricingAccomClass pricingAccomClass, OccupancyType baseOccupancyType) {
        return offsets.stream()
                .filter(offset -> offset.getOccupancyType().equals(baseOccupancyType))
                .filter(offset -> pricingAccomClass.getAccomClass().getAccomTypes().contains(offset.getAccomType()))
                .collect(Collectors.toSet());
    }

    private Set<CPConfigOffsetAccomType> getSingleOffsetsByAccomClass(List<CPConfigOffsetAccomType> offsets, PricingAccomClass pricingAccomClass, OccupancyType baseOccupancyType) {
        return offsets.stream()
                .filter(offset -> offset.getOccupancyType().equals(baseOccupancyType))
                .filter(offset -> pricingAccomClass.getAccomClass().getAccomTypes().contains(offset.getAccomType()))
                .collect(Collectors.toSet());
    }

    private BigDecimal getLowestOffsetForLowerClassAccomType(BigDecimal rate, Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction, Set<CPConfigOffsetAccomType> lowerClassAccomTypes) {
        Optional<CPConfigOffsetAccomType> highestOffset = lowerClassAccomTypes.stream()
                .filter(offset -> dayOfWeekOffsetFunction.apply(offset) != null || offset.getOffsetMethod() == OffsetMethod.FIXED_PRICE)
                .min((o1, o2) -> applyOffset(rate, dayOfWeekOffsetFunction.apply(o1), o1.getOffsetMethod()).compareTo(applyOffset(rate, dayOfWeekOffsetFunction.apply(o2), o2.getOffsetMethod())));

        return highestOffset.isPresent() ? applyOffsetByConsideringBaseOffset(rate, dayOfWeekOffsetFunction.apply(highestOffset.get()), highestOffset.get().getOffsetMethod(), 3) : rate;
    }

    private BigDecimal applyOffsetByConsideringBaseOffset(BigDecimal baseNumber, BigDecimal offsetValue, OffsetMethod offsetMethod, int ruleType) {
        if (ruleType == 2) {
            return BigDecimal.ZERO.compareTo(offsetValue) >= 0 ? baseNumber : applyOffset(baseNumber, offsetValue, offsetMethod);
        } else {
            return BigDecimal.ZERO.compareTo(offsetValue) <= 0 ? baseNumber : applyOffset(baseNumber, offsetValue, offsetMethod);
        }
    }

    private List<CPConfigOffsetAccomType> getOffsetProvidedForThisDOW(PricingAccomClass pricingAccomClass, Set<CPConfigOffsetAccomType> configOffsetAccomTypes, Function<CPConfigOffsetAccomType, BigDecimal> dayOfWeekOffsetFunction, OccupancyType baseOccupancyType, List<CPConfigOffsetAccomType> allPersistedOffsetConfigs) {
        List<CPConfigOffsetAccomType> offsetsProvidedForThisDOW = configOffsetAccomTypes
                .stream()
                .filter(offset -> offset.getAccomType().getAccomClass().equals(pricingAccomClass.getAccomClass()))
                .filter(offset -> offset.getOccupancyType().equals(baseOccupancyType))
                .filter(offset -> dayOfWeekOffsetFunction.apply(offset) != null)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(offsetsProvidedForThisDOW)) {
            //This room class was not being saved, so we should grab existing offsets
            offsetsProvidedForThisDOW = allPersistedOffsetConfigs
                    .stream()
                    .filter(offset -> offset.getAccomType().getAccomClass().equals(pricingAccomClass.getAccomClass()))
                    .filter(offset -> offset.getOccupancyType().equals(baseOccupancyType))
                    .collect(Collectors.toList());
        }
        return offsetsProvidedForThisDOW;
    }

    private boolean isOffsetInvalid(PricingBaseAccomType config, CPConfigOffsetAccomType offset) {
        return (floorOffsetCalculation(config.getSundayFloorRateWithTax(), offset.getSundayOffsetValueWithTax())) ||
                (floorOffsetCalculation(config.getMondayFloorRateWithTax(), offset.getMondayOffsetValueWithTax())) ||
                (floorOffsetCalculation(config.getTuesdayFloorRateWithTax(), offset.getTuesdayOffsetValueWithTax())) ||
                (floorOffsetCalculation(config.getWednesdayFloorRateWithTax(), offset.getWednesdayOffsetValueWithTax())) ||
                (floorOffsetCalculation(config.getThursdayFloorRateWithTax(), offset.getThursdayOffsetValueWithTax())) ||
                (floorOffsetCalculation(config.getFridayFloorRateWithTax(), offset.getFridayOffsetValueWithTax())) ||
                (floorOffsetCalculation(config.getSaturdayFloorRateWithTax(), offset.getSaturdayOffsetValueWithTax()));
    }

    private boolean isPriceExcludedOffsetInvalid(CPConfigOffsetAccomType offset) {
        return offsetValidation(offset.getSundayOffsetValueWithTax(), offset.isSeason()) ||
                offsetValidation(offset.getMondayOffsetValueWithTax(), offset.isSeason()) ||
                offsetValidation(offset.getTuesdayOffsetValueWithTax(), offset.isSeason()) ||
                offsetValidation(offset.getWednesdayOffsetValueWithTax(), offset.isSeason()) ||
                offsetValidation(offset.getThursdayOffsetValueWithTax(), offset.isSeason()) ||
                offsetValidation(offset.getFridayOffsetValueWithTax(), offset.isSeason()) ||
                offsetValidation(offset.getSaturdayOffsetValueWithTax(), offset.isSeason());
    }

    public boolean isCPFloorAndCeilingSoftWarningByPricingRuleTypeEnable() {
        return configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_CP_FLOOR_AND_CEILING_SOFT_WARNING_BY_PRICING_RULE_TYPE);
    }

    public Integer getDailyBarPricingRuleType() {
        return configParamsService.getIntegerParameterValue(IPConfigParamName.DAILY_BAR_PRICING_RULE_TYPE.value());
    }

    /**
     * use to get minPriceDiff
     *
     * @param currentDate
     * @param accomClassMinPriceDiffSeasonFilteredList
     * @param accomClassMinPriceDiff
     * @return
     */
    private MinPriceDiffDTO getMinPriceDiff(LocalDate currentDate, List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasonFilteredList, AccomClassMinPriceDiff accomClassMinPriceDiff) {

        String valueCalculatdUsingDefaultORSeason = DEFAULT;
        int dayOfWeek = getDayOfWeek(currentDate);
        BigDecimal minPriceDiff;

        if (null == accomClassMinPriceDiff) {
            valueCalculatdUsingDefaultORSeason = null;
        }
        if (CollectionUtils.isNotEmpty(accomClassMinPriceDiffSeasonFilteredList)) {
            List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons = accomClassMinPriceDiffSeasonFilteredList.stream().filter(x -> currentDate.compareTo(x.getStartDate()) > -1 && currentDate.compareTo(x.getEndDate()) < 1).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(accomClassMinPriceDiffSeasons)) {
                minPriceDiff = getMinPriceDiffFromSeason(dayOfWeek, accomClassMinPriceDiffSeasons.get(0));
                if (null != minPriceDiff) {
                    valueCalculatdUsingDefaultORSeason = "Seasons";
                } else {
                    minPriceDiff = getMinPriceDiffFromDefault(dayOfWeek, accomClassMinPriceDiff);
                }
            } else {
                minPriceDiff = getMinPriceDiffFromDefault(dayOfWeek, accomClassMinPriceDiff);
            }
        } else {
            minPriceDiff = getMinPriceDiffFromDefault(dayOfWeek, accomClassMinPriceDiff);
        }
        return new MinPriceDiffDTO(valueCalculatdUsingDefaultORSeason, minPriceDiff);
    }

    /**
     * get minPriceDiff from default
     *
     * @param dayOfWeek
     * @param accomClassMinPriceDiff
     * @return
     */
    private BigDecimal getMinPriceDiffFromDefault(int dayOfWeek, AccomClassMinPriceDiff accomClassMinPriceDiff) {
        if (null != accomClassMinPriceDiff) {
            switch (dayOfWeek) {
                case DateTimeConstants.MONDAY:
                    return accomClassMinPriceDiff.getMondayDiffWithTax();
                case DateTimeConstants.TUESDAY:
                    return accomClassMinPriceDiff.getTuesdayDiffWithTax();
                case DateTimeConstants.WEDNESDAY:
                    return accomClassMinPriceDiff.getWednesdayDiffWithTax();
                case DateTimeConstants.THURSDAY:
                    return accomClassMinPriceDiff.getThursdayDiffWithTax();
                case DateTimeConstants.FRIDAY:
                    return accomClassMinPriceDiff.getFridayDiffWithTax();
                case DateTimeConstants.SATURDAY:
                    return accomClassMinPriceDiff.getSaturdayDiffWithTax();
                case DateTimeConstants.SUNDAY:
                    return accomClassMinPriceDiff.getSundayDiffWithTax();
                default:
                    return BigDecimal.ZERO;
            }
        } else {
            return BigDecimal.ZERO;
        }
    }

    /**
     * get minPriceDiff from season's
     *
     * @param dayOfWeek
     * @param accomClassMinPriceDiffSeason
     * @return
     */
    private BigDecimal getMinPriceDiffFromSeason(int dayOfWeek, AccomClassMinPriceDiffSeason accomClassMinPriceDiffSeason) {
        switch (dayOfWeek) {
            case DateTimeConstants.MONDAY:
                return accomClassMinPriceDiffSeason.getMondayDiffWithTax();
            case DateTimeConstants.TUESDAY:
                return accomClassMinPriceDiffSeason.getTuesdayDiffWithTax();
            case DateTimeConstants.WEDNESDAY:
                return accomClassMinPriceDiffSeason.getWednesdayDiffWithTax();
            case DateTimeConstants.THURSDAY:
                return accomClassMinPriceDiffSeason.getThursdayDiffWithTax();
            case DateTimeConstants.FRIDAY:
                return accomClassMinPriceDiffSeason.getFridayDiffWithTax();
            case DateTimeConstants.SATURDAY:
                return accomClassMinPriceDiffSeason.getSaturdayDiffWithTax();
            case DateTimeConstants.SUNDAY:
                return accomClassMinPriceDiffSeason.getSundayDiffWithTax();
            default:
                return BigDecimal.ZERO;
        }

    }

    private List<PricingAccomClassRankDto> getSortedPricingAccomClassRankDtos(List<PricingAccomClassRankDto> pricingAccomClassRankDtos) {
        return pricingAccomClassRankDtos.stream().sorted(new Comparator<PricingAccomClassRankDto>() {
            @Override
            public int compare(PricingAccomClassRankDto priceRank1, PricingAccomClassRankDto priceRank2) {
                if (priceRank1.getLowerPricingAccomClass().getAccomClass().getRankOrder() == priceRank2.getLowerPricingAccomClass().getAccomClass().getRankOrder()) {
                    return 0;
                } else if (priceRank1.getLowerPricingAccomClass().getAccomClass().getRankOrder() > priceRank2.getLowerPricingAccomClass().getAccomClass().getRankOrder()) {
                    return 1;
                } else {
                    return -1;
                }
            }
        }).collect(Collectors.toList());
    }

    private BigDecimal getPricingBaseAccomTypeCeilingRate(int dayOfWeek, PricingBaseAccomType pricingBaseAccomType) {
        if (null != pricingBaseAccomType) {
            switch (dayOfWeek) {
                case DateTimeConstants.MONDAY:
                    return pricingBaseAccomType.getMondayCeilingRateWithTax();
                case DateTimeConstants.TUESDAY:
                    return pricingBaseAccomType.getTuesdayCeilingRateWithTax();
                case DateTimeConstants.WEDNESDAY:
                    return pricingBaseAccomType.getWednesdayCeilingRateWithTax();
                case DateTimeConstants.THURSDAY:
                    return pricingBaseAccomType.getThursdayCeilingRateWithTax();
                case DateTimeConstants.FRIDAY:
                    return pricingBaseAccomType.getFridayCeilingRateWithTax();
                case DateTimeConstants.SATURDAY:
                    return pricingBaseAccomType.getSaturdayCeilingRateWithTax();
                case DateTimeConstants.SUNDAY:
                    return pricingBaseAccomType.getSundayCeilingRateWithTax();
                default:
                    return BigDecimal.ZERO;
            }
        } else {
            return BigDecimal.ZERO;
        }
    }

    private BigDecimal getPricingBaseAccomTypeFloorRate(int dayOfWeek, PricingBaseAccomType pricingBaseAccomType) {
        if (null != pricingBaseAccomType) {
            switch (dayOfWeek) {
                case DateTimeConstants.MONDAY:
                    return pricingBaseAccomType.getMondayFloorRateWithTax();
                case DateTimeConstants.TUESDAY:
                    return pricingBaseAccomType.getTuesdayFloorRateWithTax();
                case DateTimeConstants.WEDNESDAY:
                    return pricingBaseAccomType.getWednesdayFloorRateWithTax();
                case DateTimeConstants.THURSDAY:
                    return pricingBaseAccomType.getThursdayFloorRateWithTax();
                case DateTimeConstants.FRIDAY:
                    return pricingBaseAccomType.getFridayFloorRateWithTax();
                case DateTimeConstants.SATURDAY:
                    return pricingBaseAccomType.getSaturdayFloorRateWithTax();
                case DateTimeConstants.SUNDAY:
                    return pricingBaseAccomType.getSundayFloorRateWithTax();
                default:
                    return BigDecimal.ZERO;
            }
        } else {
            return BigDecimal.ZERO;
        }
    }

    public boolean isHierarchyInvalidAccordingToOffsetsValues(List<PricingBaseAccomType> floorCeilingMappingsOfEditingProduct,
                                                              List<PricingBaseAccomType> floorCeilingMappingsOfNonEditingProduct,
                                                              List<CPConfigOffsetAccomType> offsetsOfEditingProduct,
                                                              List<CPConfigOffsetAccomType> offsetsOfNonEditingProduct,
                                                              Set<AccomClass> priceExcludedAccomClasses,
                                                              BigDecimal minimumDifference,
                                                              boolean isEditingProductSelected) {
        return agileRatesHierarchyValidationService.isHierarchyInvalidAccordingToOffsetsValues(
                floorCeilingMappingsOfEditingProduct,
                floorCeilingMappingsOfNonEditingProduct,
                offsetsOfEditingProduct,
                offsetsOfNonEditingProduct,
                priceExcludedAccomClasses,
                minimumDifference,
                isEditingProductSelected,
                new HashSet<>());
    }

    private Map<AccomType, PricingBaseAccomType> groupCeilingFloorValuesByAccomType(List<PricingBaseAccomType> floorCeilingValues) {
        return floorCeilingValues.stream()
                .collect(Collectors.toMap(PricingBaseAccomType::getAccomType, Function.identity()));
    }

    private Map<AccomType, CPConfigOffsetAccomType> groupOffsetsByAccomTypeAndOccupancyType(List<CPConfigOffsetAccomType> offsets) {
        return offsets.stream()
                .collect(Collectors.toMap(CPConfigOffsetAccomType::getAccomType, Function.identity()));
    }

    public List<ProductHierarchy> getInvalidHierarchiesEditingCeilingFloor(Product editingProduct,
                                                                           List<PricingBaseAccomType> editingCeilingFloorValues,
                                                                           List<PricingBaseAccomType> nonEditingCeilingFloorValues) {
        List<ProductHierarchy> impactedHierarchies = agileRatesConfigurationService.findImpactedProductHierarchies(editingProduct.getId());
        if (CollectionUtils.isEmpty(impactedHierarchies)) {
            return Collections.emptyList();
        }
        OccupancyType baseOccupancyType = agileRatesConfigurationService.getBaseOccupancyType();
        Map<Pair<LocalDate, LocalDate>, List<PricingBaseAccomType>> ceilingFloorValuesOfEditingProduct =
                groupCeilingFloorValuesByDates(
                        Stream.of(editingCeilingFloorValues, nonEditingCeilingFloorValues)
                                .flatMap(List::stream)
                                .collect(toList()));
        List<Integer> allImpactedProductIdsWithinHierarchies = getAllImpactedProductsWithinHierarchies(impactedHierarchies, editingProduct);
        Map<Integer, List<PricingBaseAccomType>> ceilingFloorValuesOfNonEditing = groupCeilingFloorValuesByProductId(agileRatesConfigurationService.retrieveFloorCeilingMappingsForProducts(allImpactedProductIdsWithinHierarchies));
        allImpactedProductIdsWithinHierarchies.add(editingProduct.getId());
        Map<Integer, List<CPConfigOffsetAccomType>> offsets = groupOffsetsByProductId(agileRatesConfigurationService.retrieveOffsetsForProducts(allImpactedProductIdsWithinHierarchies),
                offset -> Objects.equals(offset.getOccupancyType(), baseOccupancyType));
        Map<Pair<LocalDate, LocalDate>, List<CPConfigOffsetAccomType>> offsetsOfEditingProduct = groupOffsetsValuesByDates(offsets.getOrDefault(editingProduct.getId(), Collections.emptyList()));
        Set<AccomClass> priceExcludedAccomClasses = agileRatesConfigurationService.findPriceExcludedAccomClasses();
        Pair<LocalDate, LocalDate> editingSeason = getEditingSeasonFromCeilingFloorValues(editingCeilingFloorValues);
        return impactedHierarchies.stream()
                .filter(this::isHierarchyBetweenIndependentProducts)
                .filter(productHierarchy -> {
                    Product selectedProduct = productHierarchy.getFromProduct();
                    Product relatedProduct = productHierarchy.getToProduct();
                    return Objects.equals(editingProduct.getId(), selectedProduct.getId()) ?
                            !agileRatesHierarchyValidationService.isHierarchyBetweenIPsValid(
                                    ceilingFloorValuesOfEditingProduct,
                                    groupCeilingFloorValuesByDates(ceilingFloorValuesOfNonEditing.get(relatedProduct.getId())),
                                    offsetsOfEditingProduct,
                                    groupOffsetsValuesByDates(offsets.getOrDefault(relatedProduct.getId(), Collections.emptyList())),
                                    priceExcludedAccomClasses,
                                    productHierarchy.getMinimumDifference(),
                                    new HashSet<>(),
                                    getSelectedCeilingFloorPredicate(editingSeason, editingProduct.getId())) :
                            !agileRatesHierarchyValidationService.isHierarchyBetweenIPsValid(
                                    groupCeilingFloorValuesByDates(ceilingFloorValuesOfNonEditing.get(selectedProduct.getId())),
                                    ceilingFloorValuesOfEditingProduct,
                                    groupOffsetsValuesByDates(offsets.getOrDefault(relatedProduct.getId(), Collections.emptyList())),
                                    offsetsOfEditingProduct,
                                    priceExcludedAccomClasses,
                                    productHierarchy.getMinimumDifference(),
                                    new HashSet<>(),
                                    getRelatedCeilingFloorPredicate(editingSeason, editingProduct.getId()));
                })
                .collect(toList());
    }

    private Pair<LocalDate, LocalDate> getEditingSeasonFromCeilingFloorValues(List<PricingBaseAccomType> ceilingFloorValues) {
        return ceilingFloorValues.stream()
                .findFirst()
                .map(ceilingFloor -> Pair.of(ceilingFloor.getStartDate(), ceilingFloor.getEndDate()))
                .orElse(Pair.of(null, null));
    }

    private Pair<LocalDate, LocalDate> getEditingSeasonFromOffsets(List<CPConfigOffsetAccomType> offsets) {
        return offsets.stream()
                .findFirst()
                .map(ceilingFloor -> Pair.of(ceilingFloor.getStartDate(), ceilingFloor.getEndDate()))
                .orElse(Pair.of(null, null));
    }

    private Predicate<CeilingFloorAndOffsetsHierarchyContainer> getSelectedCeilingFloorPredicate(
            Pair<LocalDate, LocalDate> season, Integer productId) {
        return container -> {
            PricingBaseAccomType selectedCeilingFloor = container.getSelectedCeilingFloor()
                    .stream()
                    .findFirst()
                    .orElse(null);
            return Objects.nonNull(selectedCeilingFloor) &&
                    Objects.equals(selectedCeilingFloor.getProductID(), productId) &&
                    Objects.equals(selectedCeilingFloor.getStartDate(), season.getLeft()) &&
                    Objects.equals(selectedCeilingFloor.getEndDate(), season.getRight());
        };
    }

    private Predicate<CeilingFloorAndOffsetsHierarchyContainer> getRelatedCeilingFloorPredicate(
            Pair<LocalDate, LocalDate> season, Integer productId) {
        return container -> {
            PricingBaseAccomType relatedCeilingFloor = container.getRelatedCeilingFloor()
                    .stream()
                    .findFirst()
                    .orElse(null);
            return Objects.nonNull(relatedCeilingFloor) &&
                    Objects.equals(relatedCeilingFloor.getProductID(), productId) &&
                    Objects.equals(relatedCeilingFloor.getStartDate(), season.getLeft()) &&
                    Objects.equals(relatedCeilingFloor.getEndDate(), season.getRight());
        };
    }

    private Predicate<CeilingFloorAndOffsetsHierarchyContainer> getSelectedOffsetPredicate(
            Pair<LocalDate, LocalDate> season, Integer productId) {
        return container -> {
            CPConfigOffsetAccomType offset = container.getSelectedOffsets()
                    .stream()
                    .findFirst()
                    .orElse(null);
            return Objects.nonNull(offset) &&
                    Objects.equals(offset.getProductID(), productId) &&
                    Objects.equals(offset.getStartDate(), season.getLeft()) &&
                    Objects.equals(offset.getEndDate(), season.getRight());
        };
    }

    private Predicate<CeilingFloorAndOffsetsHierarchyContainer> getRelatedOffsetPredicate(
            Pair<LocalDate, LocalDate> season, Integer productId) {
        return container -> {
            CPConfigOffsetAccomType offset = container.getRelatedOffsets()
                    .stream()
                    .findFirst()
                    .orElse(null);
            return Objects.nonNull(offset) &&
                    Objects.equals(offset.getProductID(), productId) &&
                    Objects.equals(offset.getStartDate(), season.getLeft()) &&
                    Objects.equals(offset.getEndDate(), season.getRight());
        };
    }

    public List<ProductHierarchy> getInvalidHierarchiesEditingOffsets(Product editingProduct,
                                                                      List<CPConfigOffsetAccomType> editingOffsets,
                                                                      List<CPConfigOffsetAccomType> nonEditingOffsets) {
        List<ProductHierarchy> impactedHierarchies = agileRatesConfigurationService.findImpactedProductHierarchies(editingProduct.getId());
        if (CollectionUtils.isEmpty(impactedHierarchies)) {
            return Collections.emptyList();
        }
        OccupancyType baseOccupancyType = agileRatesConfigurationService.getBaseOccupancyType();
        Map<Pair<LocalDate, LocalDate>, List<CPConfigOffsetAccomType>> offsetsOfEditingProduct = groupOffsetsValuesByDates(Stream.of(editingOffsets, nonEditingOffsets)
                .flatMap(List::stream)
                .filter(offset -> Objects.equals(offset.getOccupancyType(), baseOccupancyType)).collect(toList()));
        List<Integer> allImpactedProductIdsWithinHierarchies = getAllImpactedProductsWithinHierarchies(impactedHierarchies, editingProduct);
        Map<Integer, List<CPConfigOffsetAccomType>> offsets = groupOffsetsByProductId(agileRatesConfigurationService.retrieveOffsetsForProducts(allImpactedProductIdsWithinHierarchies), offset -> Objects.equals(offset.getOccupancyType(), baseOccupancyType));
        allImpactedProductIdsWithinHierarchies.add(editingProduct.getId());
        Map<Integer, List<PricingBaseAccomType>> ceilingFloorValues = groupCeilingFloorValuesByProductId(agileRatesConfigurationService.retrieveFloorCeilingMappingsForProducts(allImpactedProductIdsWithinHierarchies));
        Map<Pair<LocalDate, LocalDate>, List<PricingBaseAccomType>> ceilingFloorValuesOfEditingProduct = groupCeilingFloorValuesByDates(ceilingFloorValues.get(editingProduct.getId()));
        Set<AccomClass> priceExcludedAccomClasses = agileRatesConfigurationService.findPriceExcludedAccomClasses();
        Pair<LocalDate, LocalDate> editingSeason = getEditingSeasonFromOffsets(editingOffsets);
        return impactedHierarchies.stream()
                .filter(this::isHierarchyBetweenIndependentProducts)
                .filter(productHierarchy -> {
                    Product selectedProduct = productHierarchy.getFromProduct();
                    Product relatedProduct = productHierarchy.getToProduct();
                    return Objects.equals(editingProduct.getId(), selectedProduct.getId()) ?
                            !agileRatesHierarchyValidationService.isHierarchyBetweenIPsValid(
                                    ceilingFloorValuesOfEditingProduct,
                                    groupCeilingFloorValuesByDates(ceilingFloorValues.get(relatedProduct.getId())),
                                    offsetsOfEditingProduct,
                                    groupOffsetsValuesByDates(offsets.getOrDefault(relatedProduct.getId(), Collections.emptyList())),
                                    priceExcludedAccomClasses,
                                    productHierarchy.getMinimumDifference(),
                                    new HashSet<>(),
                                    getSelectedOffsetPredicate(editingSeason, editingProduct.getId())) :
                            !agileRatesHierarchyValidationService.isHierarchyBetweenIPsValid(
                                    groupCeilingFloorValuesByDates(ceilingFloorValues.get(relatedProduct.getId())),
                                    ceilingFloorValuesOfEditingProduct,
                                    groupOffsetsValuesByDates(offsets.getOrDefault(relatedProduct.getId(), Collections.emptyList())),
                                    offsetsOfEditingProduct,
                                    priceExcludedAccomClasses,
                                    productHierarchy.getMinimumDifference(),
                                    new HashSet<>(),
                                    getRelatedOffsetPredicate(editingSeason, editingProduct.getId()));
                })
                .collect(toList());
    }

    public List<ProductHierarchy> getInvalidHierarchiesIncludingOffsets(Product product, List<CPConfigOffsetAccomType> offsetsOfEditingProduct) {
        List<ProductHierarchy> impactedHierarchies = agileRatesConfigurationService.findImpactedProductHierarchies(product.getId());
        if (CollectionUtils.isEmpty(impactedHierarchies)) {
            return Collections.emptyList();
        }
        List<Integer> allImpactedProductIdsWithinHierarchies = getAllImpactedProductsWithinHierarchies(impactedHierarchies, product);
        List<PricingBaseAccomType> allFloorCeilingMappings = agileRatesConfigurationService.retrieveAllFloorCeilingMappingsForProperty();
        Map<Integer, List<CPConfigOffsetAccomType>> offsetsForProducts = groupOffsetsByProductId(agileRatesConfigurationService.retrieveOffsetsForProducts(allImpactedProductIdsWithinHierarchies), offset -> true);
        OccupancyType baseOccupancyType = pricingConfigurationService.getBaseOccupancyType();
        Set<AccomClass> priceExcludedAccomClasses = agileRatesConfigurationService.findPriceExcludedAccomClasses();
        return impactedHierarchies.stream()
                .filter(this::isHierarchyBetweenIndependentProducts)
                .filter(productHierarchy -> {
                    Integer nonEditingProductId = agileRatesConfigurationService.getNonEditingProductIdFromHierarchy(productHierarchy, product.getId());
                    Set<AccomType> sharedAccomTypes = agileRatesConfigurationService.findSharedAccomTypes(productHierarchy.getFromProduct(), productHierarchy.getToProduct());
                    if (CollectionUtils.isEmpty(sharedAccomTypes)) {
                        return false;
                    }
                    List<PricingBaseAccomType> floorCeilingMappingsOfEditingProduct =
                            filterFloorCeilingValueForProvidedProduct(
                                    allFloorCeilingMappings, mapping -> mapping.getProductID().equals(product.getId()) &&
                                            sharedAccomTypes.contains(mapping.getAccomType()) && !isPricingBaseAccomTypeSeasonal(mapping));
                    List<PricingBaseAccomType> floorCeilingMappingsOfNonEditingProduct =
                            filterFloorCeilingValueForProvidedProduct(
                                    allFloorCeilingMappings, mapping -> mapping.getProductID().equals(nonEditingProductId) &&
                                            sharedAccomTypes.contains(mapping.getAccomType()) && !isPricingBaseAccomTypeSeasonal(mapping));
                    List<CPConfigOffsetAccomType> filteredOffsetsOfEditingProduct = filterOffsets(
                            offsetsOfEditingProduct, getFilterForOffset(sharedAccomTypes, baseOccupancyType));
                    List<CPConfigOffsetAccomType> offsetsOfNonEditingProduct = filterOffsets(offsetsForProducts.get(nonEditingProductId), getFilterForOffset(sharedAccomTypes, baseOccupancyType));
                    return isHierarchyInvalidAccordingToOffsetsValues(floorCeilingMappingsOfEditingProduct, floorCeilingMappingsOfNonEditingProduct, filteredOffsetsOfEditingProduct, offsetsOfNonEditingProduct,
                            priceExcludedAccomClasses, productHierarchy.getMinimumDifference(), Objects.equals(product.getId(), productHierarchy.getFromProduct().getId()));
                })
                .collect(toList());
    }

    private Map<Pair<LocalDate, LocalDate>, List<PricingBaseAccomType>> groupCeilingFloorValuesByDates(List<PricingBaseAccomType> ceilingFLoorValues) {
        if (ceilingFLoorValues == null) {
            return Collections.emptyMap();
        }
        return ceilingFLoorValues.stream()
                .collect(groupingBy(ceilingFloor -> Pair.of(ceilingFloor.getStartDate(), ceilingFloor.getEndDate())));
    }

    private Map<Pair<LocalDate, LocalDate>, List<CPConfigOffsetAccomType>> groupOffsetsValuesByDates(List<CPConfigOffsetAccomType> offsets) {
        return offsets.stream()
                .collect(groupingBy(ceilingFloor -> Pair.of(ceilingFloor.getStartDate(), ceilingFloor.getEndDate())));
    }


    private List<Integer> getAllImpactedProductsWithinHierarchies(List<ProductHierarchy> impactedHierarchies, Product editingProduct) {
        return agileRatesConfigurationService.getAllImpactedProductsWithinHierarchies(impactedHierarchies, editingProduct)
                .stream()
                .map(Product::getId)
                .collect(Collectors.toList());
    }

    private Map<Integer, List<CPConfigOffsetAccomType>> groupOffsetsByProductId(List<CPConfigOffsetAccomType> offsets, Predicate<CPConfigOffsetAccomType> filter) {
        return offsets.stream()
                .filter(filter)
                .collect(groupingBy(CPConfigOffsetAccomType::getProductID));
    }

    private Map<Integer, List<PricingBaseAccomType>> groupCeilingFloorValuesByProductId(List<PricingBaseAccomType> ceilingFloorValues) {
        return ceilingFloorValues.stream()
                .collect(groupingBy(PricingBaseAccomType::getProductID));
    }


    private boolean isHierarchyBetweenIndependentProducts(ProductHierarchy hierarchy) {
        return hierarchy.getFromProduct().isSystemDefaultOrIndependentProduct() && hierarchy.getToProduct().isSystemDefaultOrIndependentProduct();
    }

    private List<AccomType> findSharedAccomTypes(ProductHierarchy productHierarchy) {
        if (productHierarchy.getFromProduct().isSystemDefault()) {
            return getAccomTypesForProduct(productHierarchy.getToProduct());
        } else if (productHierarchy.getToProduct().isSystemDefault()) {
            return getAccomTypesForProduct(productHierarchy.getFromProduct());
        } else {
            List<AccomType> accomTypesForSelectedProduct = getAccomTypesForProduct(productHierarchy.getFromProduct());
            List<AccomType> accomTypesForRelatedProduct = getAccomTypesForProduct(productHierarchy.getToProduct());
            return accomTypesForSelectedProduct.stream()
                    .filter(accomTypesForRelatedProduct::contains)
                    .collect(Collectors.toList());
        }
    }

    private boolean isPricingBaseAccomTypeSeasonal(PricingBaseAccomType pricingBaseAccomType) {
        return Objects.nonNull(pricingBaseAccomType.getStartDate()) && Objects.nonNull(pricingBaseAccomType.getEndDate());
    }

    private Predicate<CPConfigOffsetAccomType> getFilterForOffset(Set<AccomType> sharedAccomTypes, OccupancyType baseOccupancyType) {
        return offset -> sharedAccomTypes.contains(offset.getAccomType()) &&
                (ObjectUtils.anyNotNull(offset.getMondayOffsetValueWithTax(),
                        offset.getTuesdayOffsetValueWithTax(),
                        offset.getWednesdayOffsetValueWithTax(),
                        offset.getThursdayOffsetValueWithTax(),
                        offset.getFridayOffsetValueWithTax(),
                        offset.getSaturdayOffsetValueWithTax(),
                        offset.getSundayOffsetValueWithTax())) &&
                Objects.isNull(offset.getStartDate()) && Objects.isNull(offset.getEndDate())
                && Objects.equals(offset.getOccupancyType(), baseOccupancyType);
    }

    public List<AccomType> getAccomTypesForProduct(Product product) {
        return agileRatesConfigurationService.findProductRoomTypesByProduct(product).stream()
                .map(ProductAccomType::getAccomType)
                .collect(Collectors.toList());
    }

    public Product getProductById(int productId) {
        return agileRatesConfigurationService.findProductById(productId);
    }

    private List<PricingBaseAccomType> filterFloorCeilingValueForProvidedProduct(
            List<PricingBaseAccomType> allFloorCeilingMappings, Predicate<PricingBaseAccomType> filter) {
        return allFloorCeilingMappings.stream()
                .filter(filter)
                .collect(Collectors.toList());
    }

    private List<CPConfigOffsetAccomType> filterOffsets(List<CPConfigOffsetAccomType> offsets, Predicate<CPConfigOffsetAccomType> filter) {
        return Optional.ofNullable(offsets)
                .orElse(Collections.emptyList())
                .stream()
                .filter(filter)
                .collect(Collectors.toList());
    }
}

