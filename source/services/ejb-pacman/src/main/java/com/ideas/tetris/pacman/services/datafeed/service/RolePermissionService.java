package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.infra.tetris.security.domain.Functionality;
import com.ideas.infra.tetris.security.domain.Page;
import com.ideas.infra.tetris.security.domain.PagesBuilder;
import com.ideas.infra.tetris.security.domain.Role;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.datafeed.dto.RolePermission;
import com.ideas.tetris.pacman.services.rest.PagesBuilderService;
import com.ideas.tetris.pacman.services.scheduledreport.entity.Language;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;
import com.ideas.tetris.pacman.services.security.Access;
import com.ideas.tetris.pacman.services.security.MenuService;
import com.ideas.tetris.pacman.services.security.Permission;
import com.ideas.tetris.pacman.services.security.RoleService;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.util.NullableMethodChainUtil.resolveChainAsNullable;

@Component
@Transactional
public class RolePermissionService {

    private static final Logger LOGGER = Logger.getLogger(RolePermissionService.class.getName());

    @Autowired
	private RoleService roleService;
    @Autowired
	private MenuService menuService;
    @Autowired
	private PagesBuilderService pagesBuilderService;

    private enum Permissions {
        READONLY("Read Only"),
        READWRITE("Read/Write"),
        NOACCESS("No Access");

        private final String permission;

        public String getPermission() {
            return permission;
        }

        Permissions(String permission) {
            this.permission = permission;
        }
    }


    public List<RolePermission> getAllRolesAndPermissions(boolean includeInternal) {
        Set<Role> roles = roleService.getAllRoles(includeInternal);

        List<RolePermission> rolesAndPermissions = generateRolesAndPermissions(roles);

        Collections.sort(rolesAndPermissions);
        return rolesAndPermissions;
    }

    public List<RolePermission> getAccessibleRolePermissions() {
        Predicate<RolePermission> rolePermissionPredicate = rolePermission -> !StringUtils.isEmpty(rolePermission.getPermission());
        return getAllRolesAndPermissions(false).stream().filter(rolePermissionPredicate).collect(Collectors.toList());
    }

    public List<RolePermission> getAccessibleRolePermissions(boolean includeInternal) {
        Predicate<RolePermission> rolePermissionPredicate = rolePermission -> !StringUtils.isEmpty(rolePermission.getPermission());
        return getAllRolesAndPermissions(includeInternal).stream().filter(rolePermissionPredicate).collect(Collectors.toList());
    }

    private List<RolePermission> generateRolesAndPermissions(Set<Role> roles) {
        List<RolePermission> rolePermissions = new ArrayList<>();
        RolePermission rolePermission;
        for (Role role : roles) {
            if (role.isRoleHasFullPermission()) {
                rolePermissions.addAll(populateFullPermissionsFor(role));
            } else {
                for (String moduleAndPermission : role.getPermissions()) {
                    Permission pageCodeAndPermission = new Permission(moduleAndPermission);
                    Optional<String> moduleName = Optional.ofNullable(pageCodeAndPermission.getPageCode());
                    Optional<String> permission = resolveChainAsNullable(() -> pageCodeAndPermission.getPageAccess().getAccessCode());
                    rolePermission = getRolePermission(role, Optional.empty(), moduleName, permission);
                    rolePermissions.add(rolePermission);
                    rolePermissions.addAll(getAllFunctionPermissionsForPermissionAndRole(pageCodeAndPermission, role));
                }
            }
        }
        return rolePermissions;
    }

    private List<RolePermission> getAllFunctionPermissionsForPermissionAndRole(Permission permission, Role role) {
        Map<String, Access> functionMap = permission.getFunctionMap();
        return functionMap.keySet().stream()
                .map(functionName -> getRolePermission(role, Optional.of(permission.getPageCode()), Optional.of(functionName), Optional.of(functionMap.get(functionName).getAccessCode())))
                .collect(Collectors.toList());
    }

    private RolePermission getRolePermission(Role role, Optional<String> parentModuleName, Optional<String> moduleName, Optional<String> permission) {
        RolePermission rolePermission = new RolePermission();
        rolePermission.setRoleName(role.getRoleName());
        rolePermission.setRoleDescription(role.getDescription());
        rolePermission.setCorporateAccess(role.isCorporate() ? Constants
                .YES : Constants.NO);
        rolePermission.setModuleName(appendExternalIdentifier(moduleName, getLocalizedName(moduleName)));
        rolePermission.setModuleImmediateParent(appendExternalIdentifier(parentModuleName, getLocalizedName(parentModuleName)));
        rolePermission.setPermission(getPermission(permission));
        rolePermission.setRank(role.getRoleRanking());
        return rolePermission;
    }

    private String appendExternalIdentifier(Optional<String> moduleName, String localizedName) {
        if (moduleName.isPresent() && localizedName != null) {
            Page page = PagesBuilder.getInstance().getPage(moduleName.get());
            if (page != null) {
                return page.getExternalIdentifier() != null ? page.getExternalIdentifier() + " " + localizedName : localizedName;
            }
        }
        return localizedName;
    }

    private String getLocalizedName(Optional<String> moduleName) {
        return moduleName.isPresent() ? ResourceUtil.getText(moduleName.get(), Language.ENGLISH) : null;
    }

    private String getPermission(Optional<String> permission) {
        return permission.isPresent() ? Permissions.valueOf(permission.get().toUpperCase()).getPermission() : null;
    }

    private List<RolePermission> populateFullPermissionsFor(Role role) {
        List<RolePermission> fullPermissions = new ArrayList<>();
        List<Page> pages = menuService.getRawMenu();
        for (Page page : pages) {
            fullPermissions.addAll(getPagePermissionsRecursive(role, page, null));
        }
        return fullPermissions;
    }

    private List<RolePermission> getPagePermissionsRecursive(Role role, Page page, String parentName) {
        // Base case 1: If the page is internal, return nothing.
        if (page.isInternal()) {
            return Collections.emptyList();
        }

        List<RolePermission> rolePermissions = new ArrayList<>();
        //Base case 2: If there are no child pages, return just the child page and their functions.
        if (page.getPages().isEmpty()) {
            rolePermissions.add(getRolePermission(role, Optional.ofNullable(parentName), Optional.of(getModuleNameKey(page)), Optional.of(Permissions.READWRITE.toString())));
            rolePermissions.addAll(getFunctionPermissions(role, page));
            return rolePermissions;
        }

        // Otherwise, add the current page and recurse down the childPages
        rolePermissions.add(getRolePermission(role, Optional.ofNullable(parentName), Optional.of(getModuleNameKey(page)), Optional.of(Permissions.READWRITE.toString())));
        rolePermissions.addAll(getFunctionPermissions(role, page));
        for (Page childPage : page.getPages()) {
            rolePermissions.addAll(getPagePermissionsRecursive(role, childPage, getModuleNameKey(page)));
        }
        return rolePermissions;
    }

    private List<RolePermission> getFunctionPermissions(Role role, final Page page) {
        List<RolePermission> functionPermissions = new ArrayList<>();
        List<Functionality> accessibleFunctionsList = page.getFunctionalities().stream().filter(functionality -> !functionality.isOnlyVisibleToInternalUsers()).collect(Collectors.toList());
        for (Functionality functionality : accessibleFunctionsList) {
            RolePermission functionPermission =
                    getRolePermission(role, Optional.of(page.getCode()), Optional.of(functionality.getCode()), Optional.of(Permissions.READWRITE.toString()));
            functionPermissions.add(functionPermission);
        }
        return functionPermissions;
    }

    private List<RolePermission> getPagePermissions(Role role, final Page page) {
        List<RolePermission> pagePermissions = new ArrayList<>();
        RolePermission parentPagePermission = getRolePermission(role, Optional.empty(), Optional.of(page.getCode()), Optional.of(Permissions.READWRITE.toString()));
        pagePermissions.add(parentPagePermission);
        final List<Page> accessiblePages = page.getPages().stream().filter(page1 -> !page1.isInternal()).collect(Collectors.toList());
        for (Page childPage : accessiblePages) {
            RolePermission childPagePermission =
                    getRolePermission(role, Optional.of(page.getCode()), Optional.of(childPage.getCode()), Optional.of(Permissions.READWRITE.toString()));
            pagePermissions.add(childPagePermission);
        }
        return pagePermissions;
    }

    //Categories are accessed by a different key, so must get the correct resource key if page is a category.
    private String getModuleNameKey(Page page) {
        if (page.isCategory()) {
            if (page.getMenuSubCategory() != null) {
                return page.getMenuSubCategory().getResourceKey();
            } else {
                return page.getMenuCategory().getResourceKey();
            }
        } else {
            return page.getCode();
        }
    }
}
