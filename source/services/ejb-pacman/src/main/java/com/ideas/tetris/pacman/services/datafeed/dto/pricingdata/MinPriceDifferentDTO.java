package com.ideas.tetris.pacman.services.datafeed.dto.pricingdata;

import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomClassMinPriceDiff;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomClassMinPriceDiffSeason;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class MinPriceDifferentDTO implements Serializable {

    private String roomClassCode;
    private String nextHigherRankRoomClass;
    private String category;
    private BigDecimal sundayPrice;
    private BigDecimal mondayPrice;
    private BigDecimal tuesdayPrice;
    private BigDecimal wednesdayPrice;
    private BigDecimal thursdayPrice;
    private BigDecimal fridayPrice;
    private BigDecimal saturdayPrice;
    private Date seasonStartDate;
    private Date seasonEndDate;

    public MinPriceDifferentDTO(AccomClassMinPriceDiff accomClassMinPriceDiff) {
        this.roomClassCode = accomClassMinPriceDiff.getAccomClassPriceRank().getLowerRankAccomClass().getCode();
        this.nextHigherRankRoomClass = accomClassMinPriceDiff.getAccomClassPriceRank().getHigherRankAccomClass().getCode();
        this.category = "Default";
        this.sundayPrice = accomClassMinPriceDiff.getSundayDiffWithTax();
        this.mondayPrice = accomClassMinPriceDiff.getMondayDiffWithTax();
        this.tuesdayPrice = accomClassMinPriceDiff.getTuesdayDiffWithTax();
        this.wednesdayPrice = accomClassMinPriceDiff.getWednesdayDiffWithTax();
        this.thursdayPrice = accomClassMinPriceDiff.getThursdayDiffWithTax();
        this.fridayPrice = accomClassMinPriceDiff.getFridayDiffWithTax();
        this.saturdayPrice = accomClassMinPriceDiff.getSaturdayDiffWithTax();
    }

    public MinPriceDifferentDTO(AccomClassMinPriceDiffSeason accomClassMinPriceDiffSeason) {
        this.roomClassCode = accomClassMinPriceDiffSeason.getAccomClassPriceRank().getLowerRankAccomClass().getCode();
        this.nextHigherRankRoomClass = accomClassMinPriceDiffSeason.getAccomClassPriceRank().getHigherRankAccomClass().getCode();
        this.category = "Seasonal";
        this.sundayPrice = accomClassMinPriceDiffSeason.getSundayDiffWithTax();
        this.mondayPrice = accomClassMinPriceDiffSeason.getMondayDiffWithTax();
        this.tuesdayPrice = accomClassMinPriceDiffSeason.getTuesdayDiffWithTax();
        this.wednesdayPrice = accomClassMinPriceDiffSeason.getWednesdayDiffWithTax();
        this.thursdayPrice = accomClassMinPriceDiffSeason.getThursdayDiffWithTax();
        this.fridayPrice = accomClassMinPriceDiffSeason.getFridayDiffWithTax();
        this.saturdayPrice = accomClassMinPriceDiffSeason.getSaturdayDiffWithTax();
        this.seasonStartDate = accomClassMinPriceDiffSeason.getStartDate().toDate();
        this.seasonEndDate = accomClassMinPriceDiffSeason.getEndDate().toDate();
    }

    public String getRoomClassCode() {
        return roomClassCode;
    }

    public void setRoomClassCode(String roomClassCode) {
        this.roomClassCode = roomClassCode;
    }

    public String getNextHigherRankRoomClass() {
        return nextHigherRankRoomClass;
    }

    public void setNextHigherRankRoomClass(String nextHigherRankRoomClass) {
        this.nextHigherRankRoomClass = nextHigherRankRoomClass;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public BigDecimal getSundayPrice() {
        return sundayPrice;
    }

    public void setSundayPrice(BigDecimal sundayPrice) {
        this.sundayPrice = sundayPrice;
    }

    public BigDecimal getMondayPrice() {
        return mondayPrice;
    }

    public void setMondayPrice(BigDecimal mondayPrice) {
        this.mondayPrice = mondayPrice;
    }

    public BigDecimal getTuesdayPrice() {
        return tuesdayPrice;
    }

    public void setTuesdayPrice(BigDecimal tuesdayPrice) {
        this.tuesdayPrice = tuesdayPrice;
    }

    public BigDecimal getWednesdayPrice() {
        return wednesdayPrice;
    }

    public void setWednesdayPrice(BigDecimal wednesdayPrice) {
        this.wednesdayPrice = wednesdayPrice;
    }

    public BigDecimal getThursdayPrice() {
        return thursdayPrice;
    }

    public void setThursdayPrice(BigDecimal thursdayPrice) {
        this.thursdayPrice = thursdayPrice;
    }

    public BigDecimal getFridayPrice() {
        return fridayPrice;
    }

    public void setFridayPrice(BigDecimal fridayPrice) {
        this.fridayPrice = fridayPrice;
    }

    public BigDecimal getSaturdayPrice() {
        return saturdayPrice;
    }

    public void setSaturdayPrice(BigDecimal saturdayPrice) {
        this.saturdayPrice = saturdayPrice;
    }

    public Date getSeasonStartDate() {
        return seasonStartDate;
    }

    public void setSeasonStartDate(Date seasonStartDate) {
        this.seasonStartDate = seasonStartDate;
    }

    public Date getSeasonEndDate() {
        return seasonEndDate;
    }

    public void setSeasonEndDate(Date seasonEndDate) {
        this.seasonEndDate = seasonEndDate;
    }

}
