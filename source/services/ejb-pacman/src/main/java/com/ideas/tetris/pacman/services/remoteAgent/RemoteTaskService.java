package com.ideas.tetris.pacman.services.remoteAgent;


import com.ideas.g3.integration.opera.agent.dto.OperaFeedMonitor;
import com.ideas.g3.integration.opera.agent.task.AgentTask;
import com.ideas.g3.integration.opera.agent.task.AgentTaskType;
import com.ideas.g3.integration.opera.agent.task.ManagerTask;
import com.ideas.g3.integration.opera.agent.task.ManagerTaskType;
import com.ideas.g3.integration.opera.agent.task.ParameterType;
import com.ideas.g3.integration.opera.agent.task.PropertyTask;
import com.ideas.g3.integration.opera.agent.task.PropertyTaskType;
import com.ideas.g3.integration.opera.agent.task.RemoteTaskDto;
import com.ideas.g3.integration.opera.agent.task.RemoteTaskStatus;
import com.ideas.g3.integration.opera.agent.task.TaskParameterDto;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.RemoteAgentConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.marketsegment.component.MarketSegmentComponent;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.opera.OperaIncomingFile;
import com.ideas.tetris.pacman.services.opera.ProcessDecisionStatus;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.remoteAgent.entity.AgentProperty;
import com.ideas.tetris.pacman.services.remoteAgent.entity.PropertyRemoteTask;
import com.ideas.tetris.pacman.services.remoteAgent.entity.PropertyTaskParameter;
import com.ideas.tetris.pacman.services.remoteAgent.entity.RemoteAgent;
import com.ideas.tetris.pacman.services.remoteAgent.entity.RemoteTask;
import com.ideas.tetris.pacman.services.remoteAgent.entity.RemoteTaskCriteria;
import com.ideas.tetris.pacman.services.remoteAgent.entity.TaskClass;
import com.ideas.tetris.pacman.services.remoteAgent.entity.TaskParameter;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.platform.common.businessservice.async.AsyncCallbackData;
import com.ideas.tetris.platform.common.businessservice.async.AsyncCallbackDataBuilder;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobCallback;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.joda.time.LocalTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.g3.integration.opera.agent.task.RemoteTaskStatus.*;
import static com.ideas.tetris.platform.common.externalsystem.ExternalSystem.OPERA_AGENT;

@Component
@Transactional
public class RemoteTaskService {

    private static final Logger LOGGER = Logger.getLogger(RemoteTaskService.class);

    private static final Integer NEXT_TASK_DELAY_FOR_UPLOAD_DECISIONS = 10; // seconds
    private static final Integer NEXT_TASK_DELAY_FOR_DOWNLOAD_FEED = 10; // seconds
    private static final String REMOTE_AGENT_ID = "remoteAgentId";
    private static final String BECAUSE_ITS_STATUS_IS = " because its status is: ";
    private static final String UNABLE_TO_COMPLETE_TASK_INVALID = "Unable to complete task. Invalid task id: ";
    private static final String UNABLE_TO_COMPLETE_TASK = "Unable to complete null task.";

    protected static JobCallback JOB_CALLBACK = new JobCallback();
    @GlobalCrudServiceBean.Qualifier
	@Qualifier("globalCrudServiceBean")
    @Autowired
	protected CrudService globalCrudService;
    @Autowired
	protected PacmanConfigParamsService configService;
    @Autowired
    RemoteAgentConfigService remoteAgentConfigService;
    @Autowired
    PropertyService propertyService;
    @Autowired
    ProcessDecisionStatus processDecisionStatus;
    @Autowired
	protected AbstractMultiPropertyCrudService multiPropertyCrudService;
    @Autowired
    MarketSegmentComponent marketSegmentComponent;

    public List<RemoteTask> findRemoteTasks(RemoteTaskCriteria criteria) {
        return globalCrudService.findByCriteria(criteria);
    }

    public List<RemoteTaskDto> getRemoteTasks(RemoteTaskCriteria criteria) {
        List<RemoteTaskDto> dtos = new ArrayList<>();
        List<RemoteTask> entities = findRemoteTasks(criteria);
        for (RemoteTask entity : entities) {
            dtos.add(toDto(entity));
        }
        return dtos;
    }

    public List<PropertyTask> getPropertyRemoteTasks(int agentId, Set<String> statuses) {
        return getPropertyTasks(PacmanWorkContextHelper.getPropertyId(), PropertyRemoteTask.BY_AGENT_AND_STATUS,
                QueryParameter.with(REMOTE_AGENT_ID, agentId).and("statuses", statuses).parameters()).stream()
                .map(task -> toDto((PropertyRemoteTask) task)).collect(Collectors.toList());
    }

    public List<PropertyTask> getPropertyRemoteTasks(Set<String> types, Set<String> statuses) {
        return getPropertyTasks(PacmanWorkContextHelper.getPropertyId(), PropertyRemoteTask.BY_TYPE_AND_STATUS,
                QueryParameter.with("types", types).and("statuses", statuses).parameters()).stream()
                .map(task -> toDto((PropertyRemoteTask) task)).collect(Collectors.toList());
    }

    public List<PropertyTask> getPropertyRemoteTasks(Set<String> types, Set<String> statuses, Set<Integer> agents) {
        if (CollectionUtils.isEmpty(agents)) {
            return getPropertyRemoteTasks(types, statuses);
        }
        return getPropertyTasks(PacmanWorkContextHelper.getPropertyId(), PropertyRemoteTask.BY_TYPE_STATUS_AND_AGENT_IDS,
                QueryParameter.with("types", types).and("statuses", statuses).and("agentIds", agents).parameters()).stream()
                .map(task -> toDto((PropertyRemoteTask) task)).collect(Collectors.toList());
    }

    @SuppressWarnings("unchecked")
    private <T> List<T> getPropertyTasks(Integer propertyId, String namedQuery, Map<String, Object> queryParameters) {
        Property property = propertyService.getPropertyById(propertyId);
        if (property.getStatus() == Status.MAINTENANCE) {
            return Collections.emptyList();
        }
        return (List<T>) multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, namedQuery, queryParameters);
    }

    public RemoteTask getRemoteTask(int taskId) {
        return globalCrudService.find(RemoteTask.class, taskId);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public PropertyTask getPropertyRemoteTask(int taskId) {
        return toDto(multiPropertyCrudService.find(PacmanWorkContextHelper.getPropertyId(), PropertyRemoteTask.class, taskId));
    }

    public int getRemoteTaskCount(RemoteTaskCriteria criteria) {
        criteria.setSortEnabled(false);
        return globalCrudService.findCountByCriteria(criteria);
    }

    public List<RemoteTaskDto> getRemoteTasksForAgent(int agentId) {
        RemoteTaskCriteria criteria = new RemoteTaskCriteria();
        criteria.setAgentId(agentId);
        List<RemoteTaskDto> tasks = getRemoteTasks(criteria);
        RemoteAgent agent = globalCrudService.find(RemoteAgent.class, agentId);
        if (agent != null) {
            tasks.addAll(agent.getAgentProperties().stream().map(AgentProperty::getPropertyId)
                    .map(id -> getPropertyTasks(agent, id)).flatMap(List::stream).collect(Collectors.toList()));
        }
        return tasks;
    }

    private List<RemoteTaskDto> getPropertyTasks(RemoteAgent agent, Integer propertyId) {
        return getPropertyTasks(propertyId, PropertyRemoteTask.BY_AGENT,
                QueryParameter.with(REMOTE_AGENT_ID, agent.getId()).parameters())
                .stream().map(entity -> toDto((PropertyRemoteTask) entity, propertyId))
                .collect(Collectors.toList());

    }

    private RemoteTaskDto toDto(RemoteTask entity) {
        RemoteTaskDto dto = null;
        if (entity.getTaskClass().equals(TaskClass.MANAGER.toString())) {
            dto = new ManagerTask();
            ((ManagerTask) dto).setType(ManagerTaskType.valueOf(entity.getTaskType()));
        } else if (entity.getTaskClass().equals(TaskClass.AGENT.toString())) {
            dto = new AgentTask();
            ((AgentTask) dto).setType(AgentTaskType.valueOf(entity.getTaskType()));
        }
        if (null != entity.getId()) {
            dto.setTaskId(entity.getId());
        }
        if (null != entity.getRemoteAgent()) {
            dto.setAgentId(entity.getRemoteAgent().getId());
        }
        dto.setCompletedDate(entity.getCompletedDate());
        dto.setCreatedDate(entity.getCreatedDate());
        dto.setScheduledDate(entity.getScheduledDate());
        dto.setStatus(RemoteTaskStatus.valueOf(entity.getTaskStatus()));
        for (TaskParameter parameter : entity.getParameters()) {
            try {
                ParameterType type = ParameterType.valueOf(parameter.getParameterType());
                Object value = restoreValue(parameter.getValue(), type.getValueType());
                TaskParameterDto parameterDto = new TaskParameterDto(type, value);
                parameterDto.setId(parameter.getId());
                dto.addParameter(parameterDto);
            } catch (IllegalArgumentException e) {
                LOGGER.debug("the parameter type string stored in the database has been removed from the ParameterType enumeration.  So we will ignore this parameter", e);
            }
        }
        dto.setResults(entity.getResults());
        return dto;
    }

    private PropertyTask toDto(PropertyRemoteTask entity) {
        return toDto(entity, PacmanWorkContextHelper.getPropertyId());
    }

    private PropertyTask toDto(PropertyRemoteTask entity, Integer propertyId) {
        PropertyTask dto = new PropertyTask();
        dto.setType(PropertyTaskType.valueOf(entity.getTaskType()));
        dto.setPropertyId(propertyId);
        dto.setTaskId(entity.getId());
        dto.setAgentId(entity.getRemoteAgentId());
        dto.setCompletedDate(entity.getCompletedDate());
        dto.setCreatedDate(entity.getCreatedDate());
        dto.setScheduledDate(entity.getScheduledDate());
        dto.setStatus(RemoteTaskStatus.valueOf(entity.getTaskStatus()));

        if (configService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGENT_PERFORMANCE_MONITOR_ENABLED)) {
            dto.setOperaFeedMonitor(new OperaFeedMonitor());
        } else {
            dto.setOperaFeedMonitor(null);
        }
        for (PropertyTaskParameter parameter : entity.getParameters()) {
            try {
                ParameterType type = ParameterType.valueOf(parameter.getParameterType());
                Object value = restoreValue(parameter.getValue(), type.getValueType());
                TaskParameterDto parameterDto = new TaskParameterDto(type, value);
                parameterDto.setId(parameter.getId());
                dto.addParameter(parameterDto);
            } catch (IllegalArgumentException e) {
                LOGGER.debug("the parameter type string stored in the database has been removed from the ParameterType enumeration.  So we will ignore this parameter", e);
            }
        }
        dto.setResults(entity.getResults());
        return dto;
    }

    private Object restoreValue(String valueString, Class<?> valueType) {
        if (valueType.equals(String.class)) {
            return valueString;
        } else if (valueType.equals(Integer.class)) {
            return Integer.parseInt(valueString);
        } else if (valueType.equals(LocalDate.class)) {
            return LocalDate.parse(valueString);
        }
        return valueString; // default type is String
    }

    public RemoteTaskDto saveRemoteTask(RemoteTaskDto taskDto) {
        RemoteTask entity = toEntity(taskDto);
        return toDto(saveTask(entity));
    }

    public RemoteTaskDto savePropertyRemoteTask(PropertyTask taskDto) {
        PropertyRemoteTask entity = toTenantEntity(taskDto);
        return toDto(saveTask(entity), taskDto.getPropertyId());
    }

    public void deleteRemoteTasks(RemoteAgent remoteAgent) {
        globalCrudService.executeUpdateByNamedQuery(RemoteTask.DELETE_BY_AGENT,
                QueryParameter.with("remoteAgent", remoteAgent).parameters());
    }

    public void deleteRemoteTasks(int agentId, int propertyId) {
        PacmanWorkContextHelper.setPropertyId(propertyId);
        multiPropertyCrudService.executeNamedUpdateOnSingleProperty(propertyId, PropertyRemoteTask.DELETE_BY_AGENT,
                QueryParameter.with(REMOTE_AGENT_ID, agentId).parameters());
    }

    public int purgeRemoteTasks() {
        int daysToRetain = Integer.parseInt(configService.getValue("pacman", GUIConfigParamName.REMOTE_AGENT_TASKS_DAYS_TO_RETAIN.value()));
        LocalDate oldestDateToRetain = LocalDate.now().minusDays(daysToRetain);
        final Set<String> excludeStatuses = Stream.of(IN_PROGRESS, PENDING, FAILED).map(RemoteTaskStatus::toString).collect(Collectors.toSet());
        return globalCrudService.executeUpdateByNamedQuery(RemoteTask.DELETE_BY_DATE_AND_EXCLUDE_STATUS_LIST,
                QueryParameter.with("retainDate", oldestDateToRetain.toLocalDateTime(LocalTime.MIDNIGHT)).and("statusList", excludeStatuses).parameters());
    }

    public int fetchRemoteTaskCountForFailedAndClosedTaks(Long jobInstanceID, Integer remoteAgentId) {
        return (int) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(PacmanWorkContextHelper.getPropertyId(), PropertyRemoteTask.COUNT_BY_PROPERTY_TASK_TYPE_STATUS_JOB_ID,
                QueryParameter.with(REMOTE_AGENT_ID, remoteAgentId).and("jobInstanceID", jobInstanceID).parameters());
    }

    @SuppressWarnings("unchecked")
    public List<String> fetchUploadFailedDistinctAgileRateCodes(Integer remoteAgentId, List<String> listOfValues, java.time.LocalDateTime createdDttm) {
        return multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanWorkContextHelper.getPropertyId(), PropertyRemoteTask.UPLOAD_FAILED_DISTINCT_AGILE_RATE_CODE,
                QueryParameter.with(REMOTE_AGENT_ID, remoteAgentId).and("listOfValues", listOfValues).and("createdDttm", java.sql.Timestamp.valueOf(createdDttm)).parameters());
    }

    private RemoteTask toEntity(RemoteTaskDto dto) {
        RemoteTask entity = new RemoteTask();
        if (dto instanceof AgentTask) {
            entity.setTaskClass(TaskClass.AGENT.toString());
            entity.setTaskType(((AgentTask) dto).getType().toString());
        } else if (dto instanceof ManagerTask) {
            entity.setTaskClass(TaskClass.MANAGER.toString());
            entity.setTaskType(((ManagerTask) dto).getType().toString());
        }
        if (dto.getTaskId() > 0) {
            entity.setId(dto.getTaskId());
        }
        entity.setTaskStatus(dto.getStatus().toString());
        entity.setCompletedDate(dto.getCompletedDate());
        entity.setCreatedDate(dto.getCreatedDate());
        entity.setScheduledDate(dto.getScheduledDate());
        List<TaskParameter> parameters = new ArrayList<>();
        for (TaskParameterDto parameterDto : dto.getParameters()) {
            TaskParameter parameter = new TaskParameter();
            parameter.setRemoteTask(entity);
            parameter.setParameterType(parameterDto.getType().toString());
            parameter.setValue(parameterDto.getValue().toString());
            if (parameterDto.getId() != null && parameterDto.getId() > 0) {
                parameter.setId(parameterDto.getId());
            }
            parameters.add(parameter);
        }
        entity.setParameters(parameters);
        entity.setResults(dto.getResults());
        entity.setRemoteAgent(globalCrudService.find(RemoteAgent.class, dto.getAgentId()));
        entity.setNotes(dto.getNotes());
        return entity;
    }

    protected PropertyRemoteTask toTenantEntity(PropertyTask dto) {
        PropertyRemoteTask entity = new PropertyRemoteTask();
        entity.setTaskType(dto.getType().toString());
        if (dto.getTaskId() > 0) {
            entity.setId(dto.getTaskId());
        }
        entity.setTaskStatus(dto.getStatus().toString());
        entity.setCompletedDate(dto.getCompletedDate());
        entity.setCreatedDate(dto.getCreatedDate());
        entity.setScheduledDate(dto.getScheduledDate());
        List<PropertyTaskParameter> parameters = new ArrayList<>();
        for (TaskParameterDto parameterDto : dto.getParameters()) {
            PropertyTaskParameter parameter = new PropertyTaskParameter();
            parameter.setRemoteTask(entity);
            parameter.setParameterType(parameterDto.getType().toString());
            parameter.setValue(parameterDto.getValue().toString());
            if (parameterDto.getId() != null && parameterDto.getId() > 0) {
                parameter.setId(parameterDto.getId());
            }
            parameters.add(parameter);
        }
        entity.setParameters(parameters);
        entity.setResults(dto.getResults());
        entity.setRemoteAgentId(dto.getAgentId());
        entity.setNotes(dto.getNotes());
        return entity;
    }

    @Transactional(propagation = Propagation.NEVER)
    public RemoteTaskDto propertyTaskCompleted(int taskId, LocalDateTime completionDate, String results) throws TaskNotInProgressException {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        PropertyTask taskDto = toDto(completePropertyRemoteTask(taskId, completionDate, results, propertyId));
        updateDecisionStatus(taskDto, true);
        completeAsyncJobStep(taskDto);
        return taskDto;
    }

    private PropertyRemoteTask completePropertyRemoteTask(int taskId, LocalDateTime completionDate, String results, Integer propertyId) throws TaskNotInProgressException {
        PropertyRemoteTask remoteTask = multiPropertyCrudService.find(propertyId, PropertyRemoteTask.class, taskId);
        if (remoteTask == null) {
            throw new TetrisException(ErrorCode.REMOTE_TASK_NOT_FOUND, "Unable to complete property task. Invalid task id: " + taskId);
        }
        if (!IN_PROGRESS.toString().equals(remoteTask.getTaskStatus())) {
            throw new TaskNotInProgressException("Unable to complete task " + remoteTask.getId() + BECAUSE_ITS_STATUS_IS + remoteTask.getTaskStatus());
        }
        remoteTask.setCompletedDate(completionDate);
        remoteTask.setTaskStatus(RemoteTaskStatus.COMPLETED.toString());
        remoteTask.setResults(results);
        return saveTask(remoteTask);
    }

    private void updateDecisionStatus(PropertyTask taskDto, boolean success) {
        if (PropertyTaskType.UPLOAD_AGILE_RATES.equals(taskDto.getType()) &&
                Boolean.valueOf((String) taskDto.getParameterValue(ParameterType.IS_LAST_RATE_CODE))) {
            updateDecisionStatusWithUploadDate((String) taskDto.getParameterValue(ParameterType.OPERA_SYS_REQ), taskDto.getResults(), success);
        }
    }

    public RemoteTaskDto taskCompleted(int taskId, LocalDateTime completionDate, String results) throws TaskNotInProgressException {
        RemoteTask remoteTask = getTask(taskId);
        if (remoteTask == null) {
            throw new TetrisException(ErrorCode.REMOTE_TASK_NOT_FOUND, UNABLE_TO_COMPLETE_TASK_INVALID + taskId);
        }
        String taskType = remoteTask.getTaskType();
        RemoteAgent agent = remoteTask.getRemoteAgent();
        if (taskType.equals(AgentTaskType.RESET_AGENT_MANAGER.toString()) && agent.isSuspended()) {
            remoteAgentConfigService.resumeRemoteAgent(agent);
        }
        return toDto(taskCompleted(remoteTask, completionDate, results));
    }

    public RemoteTask getTask(int taskId) {
        RemoteTask remoteTask = globalCrudService.find(RemoteTask.class, taskId);
        return remoteTask;
    }

    public RemoteTask taskCompleted(RemoteTask remoteTask, LocalDateTime completionDate, String results) throws TaskNotInProgressException {
        String responseString = null;
        if (remoteTask == null) {
            throw new IllegalArgumentException(UNABLE_TO_COMPLETE_TASK);
        }
        if (!IN_PROGRESS.toString().equals(remoteTask.getTaskStatus())) {
            throw new TaskNotInProgressException("Unable to complete task " + remoteTask.getId() + BECAUSE_ITS_STATUS_IS + remoteTask.getTaskStatus());
        }
        remoteTask.setCompletedDate(completionDate);
        remoteTask.setTaskStatus(RemoteTaskStatus.COMPLETED.toString());
        remoteTask.setResults(results);
        if (remoteTask.getTaskType().equals(PropertyTaskType.UPLOAD_DECISIONS.toString()) &&
                configService.getBooleanParameterValue(FeatureTogglesConfigParamName.ATTACH_DECISIONSTO_JOB_STEPS.value())) {
            responseString = remoteTask.getNotes();
        }
        if (remoteTask.getTaskType().equals(AgentTaskType.UPDATE_CONFIG.toString())) {
            createResetAgentTask(remoteTask.getRemoteAgent().getId());
        }
        RemoteTask updatedTask = saveTask(remoteTask);

        if (PropertyTaskType.UPLOAD_AGILE_RATES.toString().equalsIgnoreCase(remoteTask.getTaskType()) &&
                Boolean.valueOf(remoteTask.getParameterValue(ParameterType.IS_LAST_RATE_CODE))) {
            updateDecisionStatusWithUploadDate(remoteTask.getParameterValue(ParameterType.OPERA_SYS_REQ), results, true);
        }
        completeAsyncJobStep(updatedTask, true, results, responseString);
        return updatedTask;
    }

    private boolean completeAsyncJobStep(RemoteTask remoteTask, boolean successful, Serializable results) {
        return completeAsyncJobStep(remoteTask, successful, results, null);
    }

    private void completeAsyncJobStep(PropertyRemoteTask remoteTask, boolean successful, Serializable results, String responseString) {
        Long jobInstanceId = getJobInstanceId(remoteTask);
        if (jobInstanceId == null) {
            return;
        }
        AsyncCallbackData callbackData = AsyncCallbackDataBuilder.build();
        callbackData.setWasSuccessful(successful);
        callbackData.setJobInstanceId(jobInstanceId);
        callbackData.setResponseString(responseString);
        callbackData.setResponse(results);
        executeCallback(callbackData);
    }

    private void completeAsyncJobStep(PropertyTask taskDto) {
        String jobInstanceId = (String) taskDto.getParameterValue(ParameterType.JOB_INSTANCE_ID);
        if (jobInstanceId == null) {
            return;
        }
        AsyncCallbackData callbackData = AsyncCallbackDataBuilder.build();
        callbackData.setWasSuccessful(true);
        callbackData.setJobInstanceId(Long.valueOf(jobInstanceId));
        callbackData.setResponseString(taskDto.getNotes());
        callbackData.setResponse(taskDto.getResults());
        executeCallback(callbackData);
    }

    private boolean completeAsyncJobStep(RemoteTask remoteTask, boolean successful, Serializable results, String responseString) {
        Long jobInstanceId = getJobInstanceId(remoteTask);
        if (jobInstanceId == null) {
            return false;
        }
        if (remoteTask.getTaskType().equals(AgentTaskType.DELETE_PROPERTY.toString())) {
            handleDeletePropertyTaskCompleted(remoteTask, successful, results, jobInstanceId);
            return true;
        }
        AsyncCallbackData callbackData = AsyncCallbackDataBuilder.build();
        callbackData.setWasSuccessful(successful);
        callbackData.setJobInstanceId(jobInstanceId);
        callbackData.setResponse(results);
        callbackData.setResponseString(responseString);
        executeCallback(callbackData);
        return true;
    }

    private void handleDeletePropertyTaskCompleted(RemoteTask remoteTask, boolean successful, Serializable results, Long jobInstanceId) {
        if (successful) {
            if (propertyDeletedFromAllAgents(remoteTask)) {
                AsyncCallbackData callbackData = AsyncCallbackDataBuilder.build();
                callbackData.setWasSuccessful(successful);
                callbackData.setJobInstanceId(jobInstanceId);
                callbackData.setResponse(results);
                executeCallback(callbackData);
            }

        } else {
            AsyncCallbackData callbackData = AsyncCallbackDataBuilder.build();
            callbackData.setWasSuccessful(successful);
            callbackData.setJobInstanceId(jobInstanceId);
            callbackData.setResponse(results);
            executeCallback(callbackData);
        }
    }

    private void executeCallback(AsyncCallbackData callbackData) {
        // the thread-local WorkContext is cleared when the job callback is made, so we need to save it before and restore it after
        WorkContextType workContext = PacmanWorkContextHelper.getWorkContext();
        JOB_CALLBACK.execute(callbackData);
        PacmanWorkContextHelper.setWorkContext(workContext);
    }

    private boolean propertyDeletedFromAllAgents(RemoteTask remoteTask) {
        RemoteTaskCriteria criteria = new RemoteTaskCriteria();

        Set<String> statuses = new HashSet<>();
        statuses.add(IN_PROGRESS.toString());
        statuses.add(PENDING.toString());
        criteria.setStatuses(statuses);
        Set<String> taskTypes = new HashSet<>();
        taskTypes.add(AgentTaskType.DELETE_PROPERTY.toString());
        criteria.setTaskTypes(taskTypes);
        List<RemoteTask> pendingTasks = findRemoteTasks(criteria);
        if (CollectionUtils.isEmpty(pendingTasks)) {
            return true;
        }
        String propertyIdString = remoteTask.getParameterValue(ParameterType.PROPERTY_ID);
        for (RemoteTask task : pendingTasks) {
            String candidate = task.getParameterValue(ParameterType.PROPERTY_ID);
            if (candidate != null && candidate.equals(propertyIdString)) {
                return false;
            }
        }
        return true;
    }

    private Long getJobInstanceId(RemoteTask remoteTask) {
        String jobId = remoteTask.getParameterValue(ParameterType.JOB_INSTANCE_ID);
        return jobId == null ? null : Long.valueOf(jobId);
    }

    private Long getJobInstanceId(PropertyRemoteTask remoteTask) {
        String jobId = remoteTask.getParameterValue(ParameterType.JOB_INSTANCE_ID);
        return jobId == null ? null : Long.valueOf(jobId);
    }

    @Transactional(propagation = Propagation.NEVER)
    public RemoteTaskDto propertyTaskFailed(int taskId, LocalDateTime completionDate, String results) throws TaskNotInProgressException {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        PropertyRemoteTask remoteTask = multiPropertyCrudService.find(propertyId, PropertyRemoteTask.class, taskId);
        if (remoteTask == null) {
            throw new TetrisException(ErrorCode.REMOTE_TASK_NOT_FOUND, UNABLE_TO_COMPLETE_TASK_INVALID + taskId);
        }
        PropertyTask taskDto = toDto(propertyTaskFailed(remoteTask, completionDate, results, propertyId));
        updateDecisionStatus(taskDto, false);
        completeAsyncJobStep(remoteTask, StringUtils.equalsIgnoreCase(PropertyTaskType.UPLOAD_AGILE_RATES.toString(), remoteTask.getTaskType()), new TetrisException(results), null);
        return taskDto;
    }


    public RemoteTaskDto taskFailed(int taskId, LocalDateTime completionDate, String results) throws TaskNotInProgressException {
        RemoteTask remoteTask = globalCrudService.find(RemoteTask.class, taskId);
        if (remoteTask == null) {
            throw new TetrisException(ErrorCode.REMOTE_TASK_NOT_FOUND, UNABLE_TO_COMPLETE_TASK_INVALID + taskId);
        }
        return toDto(taskFailed(remoteTask, completionDate, results));
    }

    public RemoteTask taskFailed(RemoteTask remoteTask, LocalDateTime completionDate, String results) throws TaskNotInProgressException {
        if (remoteTask == null) {
            throw new IllegalArgumentException(UNABLE_TO_COMPLETE_TASK);
        }
        if (!IN_PROGRESS.toString().equals(remoteTask.getTaskStatus())) {
            throw new TaskNotInProgressException("Unable to fail task " + remoteTask.getId() + BECAUSE_ITS_STATUS_IS + remoteTask.getTaskStatus());
        }
        remoteTask.setCompletedDate(completionDate);
        remoteTask.setResults(results);
        TetrisException exception = new TetrisException(results);

        if (PropertyTaskType.UPLOAD_AGILE_RATES.toString().equalsIgnoreCase(remoteTask.getTaskType()) &&
                Boolean.valueOf(remoteTask.getParameterValue(ParameterType.IS_LAST_RATE_CODE))) {
            updateDecisionStatusWithUploadDate(remoteTask.getParameterValue(ParameterType.OPERA_SYS_REQ), results, false);
        }

        if (getJobInstanceId(remoteTask) == null) {
            remoteTask.setTaskStatus(RemoteTaskStatus.FAILED.toString());
        } else {
            completeAsyncJobStep(remoteTask, PropertyTaskType.UPLOAD_AGILE_RATES.toString().equalsIgnoreCase(remoteTask.getTaskType()), exception);
            remoteTask.setTaskStatus(RemoteTaskStatus.CLOSED.toString());
        }
        return saveTask(remoteTask);
    }

    protected PropertyRemoteTask propertyTaskFailed(PropertyRemoteTask remoteTask, LocalDateTime completionDate, String results, Integer propertyId) throws TaskNotInProgressException {
        if (remoteTask == null) {
            throw new IllegalArgumentException(UNABLE_TO_COMPLETE_TASK);
        }
        if (!RemoteTaskStatus.IN_PROGRESS.toString().equals(remoteTask.getTaskStatus())) {
            throw new TaskNotInProgressException("Unable to fail task " + remoteTask.getId() + BECAUSE_ITS_STATUS_IS + remoteTask.getTaskStatus());
        }
        remoteTask.setCompletedDate(completionDate);
        remoteTask.setResults(results);
        remoteTask.setTaskStatus(getJobInstanceId(remoteTask) == null ? RemoteTaskStatus.FAILED.toString() : RemoteTaskStatus.CLOSED.toString());
        return saveTask(remoteTask, propertyId);
    }

    private void updateDecisionStatusWithUploadDate(String externalSystemParameterValue, String results, boolean success) {
        final String externalSystem = StringUtils.equalsIgnoreCase(Constants.PMS, externalSystemParameterValue) ? Constants.OPERA : externalSystemParameterValue;
        if (success) {
            processDecisionStatus.success(Constants.AGILE_RATES, externalSystem, results);
        } else {
            processDecisionStatus.fail(Constants.AGILE_RATES, externalSystem, results);
        }
    }

    @SuppressWarnings("squid:S3776")
    public PropertyTask createDataFeedTask(int agentId, int propertyId, OperaIncomingFile feedType, String correlationId, LocalDateTime scheduledDateTime, Integer pastDays, Integer groupPastDays, Integer futureDays, Long jobInstanceId, Boolean overrideBusinessDateTime) {
        PropertyTask task = new PropertyTask(PropertyTaskType.DOWNLOAD_FEED_DATA);
        task.setPropertyId(propertyId);
        task.setAgentId(agentId);
        task.setCreatedDate(LocalDateTime.now());
        task.setStatus(PENDING);
        task.setScheduledDate(scheduledDateTime);
        task.addParameter(new TaskParameterDto(ParameterType.FEED_TYPE, feedType.getFileTypeCode()));
        task.addParameter(new TaskParameterDto(ParameterType.CORRELATION_ID, correlationId));
        // Only add param if value is true to maintain backward compatibility
        if (overrideBusinessDateTime) {
            task.addParameter(new TaskParameterDto(ParameterType.OVRRIDE_BUSINESS_DT, overrideBusinessDateTime));
        }
        // Only add param if value is true to maintain backward compatibility, can be removed when agent is fixed to ignore "extra" parameters because currently agent throws an Exception if the parameter is not supported.
        boolean excludeZeroGroupBlockPickup = configService.getBooleanParameterValue(RemoteAgentConfigParamName.EXCLUDE_ZERO_GROUP_BLOCK_PICKUP.value(Constants.OPERA));
        if (excludeZeroGroupBlockPickup) {
            task.addParameter(new TaskParameterDto(ParameterType.EXCLUDE_ZERO_GROUP, excludeZeroGroupBlockPickup));
        }
        if (needsPastDays(feedType)) {
            if (pastDays == null) {
                throw new TetrisException("pastDays parameter to data feed task cannot be null");
            }
            task.addParameter(new TaskParameterDto(ParameterType.PAST_DAYS, pastDays));
        }
        if (needsGroupPastDays(feedType)) {
            if (groupPastDays == null) {
                throw new TetrisException("groupPastDays parameter to data feed task cannot be null");
            }
            task.addParameter(new TaskParameterDto(ParameterType.PAST_DAYS, groupPastDays));
        }
        if (needsFutureDays(feedType)) {
            if (futureDays == null) {
                throw new TetrisException("futureDays parameter to data feed task cannot be null");
            }
            task.addParameter(new TaskParameterDto(ParameterType.FUTURE_DAYS, futureDays));
        }
        if (needsRoomTypesToInclude(feedType)) {
            String roomTypesToInclude = removeSpaces(getRoomTypesToInclude());
            if (roomTypesToInclude != null && roomTypesToInclude.trim().length() > 0) {
                task.addParameter(new TaskParameterDto(ParameterType.RM_TYPES_TO_INCL, roomTypesToInclude));
            }
        }
        if (needsMarketSegmentsToInclude(feedType)) {
            String marketSegmentsToInclude = getMarketSegmentsToInclude();
            if (marketSegmentsToInclude != null && marketSegmentsToInclude.trim().length() > 0) {
                task.addParameter(new TaskParameterDto(ParameterType.MKT_SEGMENTS_TO_INCL, marketSegmentsToInclude));
            }
        }

        if (needsResTypesToInclude(feedType)) {
            String resTypesToInclude = getResTypesToInclude();
            if (StringUtils.isNotEmpty(resTypesToInclude)) {
                task.addParameter(new TaskParameterDto(ParameterType.RES_TYPES_TO_INCL, resTypesToInclude));
            }
        }

        task.addParameter(new TaskParameterDto(ParameterType.POP_CHUNK_SIZE, getPopulationChunkSize()));
        task.addParameter(new TaskParameterDto(ParameterType.READ_CHUNK_SIZE, getReadChunkSize()));
        task.addParameter(new TaskParameterDto(ParameterType.OPERA_SYS_REQ, "PMS"));
        if (feedType.equals(OperaIncomingFile.INCOMING_METADATA)) {
            task.addParameter(new TaskParameterDto(ParameterType.PROPERTY_TIMEZONE, getPropertyTimeZone()));
        }
        if (jobInstanceId != null) {
            task.addParameter(new TaskParameterDto(ParameterType.JOB_INSTANCE_ID, jobInstanceId));
        }
        task.addParameter(new TaskParameterDto(ParameterType.NEXT_TASK_DELAY, NEXT_TASK_DELAY_FOR_DOWNLOAD_FEED));
        return (PropertyTask) savePropertyRemoteTask(task);
    }

    private String removeSpaces(String input) {
        return input == null ? null : input.replaceAll("\\s", "");
    }

    protected boolean needsPastDays(OperaIncomingFile feedType) {
        return feedType.equals(OperaIncomingFile.INCOMING_METADATA)
                || feedType.equals(OperaIncomingFile.PAST_HOTEL_AND_ROOM_TYPE_SUMMARY) || feedType.equals(OperaIncomingFile.PAST_HOTEL_SUMMARY)
                || feedType.equals(OperaIncomingFile.PAST_SEGMENT_AND_ROOM_TYPE_SUMMARY) || feedType.equals(OperaIncomingFile.PAST_TRANSACTION)
                || feedType.equals(OperaIncomingFile.YIELD_CURRENCY);
    }

    protected boolean needsGroupPastDays(OperaIncomingFile feedType) {
        return feedType.equals(OperaIncomingFile.PAST_GROUP_MASTER);
    }

    protected boolean needsFutureDays(OperaIncomingFile feedType) {
        return feedType.equals(OperaIncomingFile.INCOMING_METADATA) || feedType.equals(OperaIncomingFile.CURRENT_GROUP_MASTER)
                || feedType.equals(OperaIncomingFile.CURRENT_HOTEL_AND_ROOM_TYPE_SUMMARY) || feedType.equals(OperaIncomingFile.CURRENT_HOTEL_SUMMARY)
                || feedType.equals(OperaIncomingFile.CURRENT_SEGMENT_AND_ROOM_TYPE_SUMMARY) || feedType.equals(OperaIncomingFile.CURRENT_TRANSACTION)
                || feedType.equals(OperaIncomingFile.YIELD_CURRENCY);
    }

    protected boolean needsRoomTypesToInclude(OperaIncomingFile feedType) {
        return feedType.equals(OperaIncomingFile.CURRENT_HOTEL_AND_ROOM_TYPE_SUMMARY) || feedType.equals(OperaIncomingFile.CURRENT_HOTEL_SUMMARY)
                || feedType.equals(OperaIncomingFile.CURRENT_SEGMENT_AND_ROOM_TYPE_SUMMARY) || feedType.equals(OperaIncomingFile.PAST_HOTEL_AND_ROOM_TYPE_SUMMARY)
                || feedType.equals(OperaIncomingFile.PAST_HOTEL_SUMMARY) || feedType.equals(OperaIncomingFile.PAST_SEGMENT_AND_ROOM_TYPE_SUMMARY);
    }

    protected boolean needsMarketSegmentsToInclude(OperaIncomingFile feedType) {
        return feedType.equals(OperaIncomingFile.CURRENT_SEGMENT_AND_ROOM_TYPE_SUMMARY)
                && Boolean.valueOf(configService.getParameterValue(IntegrationConfigParamName.FILTER_MARKET_SEGMENT_SUMMARY, OPERA_AGENT).toString())
                && Boolean.valueOf(configService.getParameterValue(GUIConfigParamName.POPULATION_ANALYTICAL_MARKET_SEGMENT_MAPPING_COMPLETE).toString());
    }

    protected boolean needsResTypesToInclude(OperaIncomingFile feedType) {
        return feedType.equals(OperaIncomingFile.CURRENT_HOTEL_AND_ROOM_TYPE_SUMMARY) || feedType.equals(OperaIncomingFile.CURRENT_SEGMENT_AND_ROOM_TYPE_SUMMARY)
                || feedType.equals(OperaIncomingFile.CURRENT_HOTEL_SUMMARY);

    }

    public PropertyTask createUploadDecisionsTask(int agentId, String operaSystemType, String decisionType) {
        return createUploadDecisionsTask(agentId, operaSystemType, decisionType, null);

    }

    public ManagerTask createResetAgentTask(int agentId) {
        ManagerTask task = new ManagerTask(ManagerTaskType.RESET_AGENT);
        task.setAgentId(agentId);
        task.setCreatedDate(LocalDateTime.now());
        task.setStatus(PENDING);
        saveRemoteTask(task);
        return task;

    }

    public ManagerTask createInstallNewAgentTask(int agentId, String version) {
        ManagerTask task = new ManagerTask(ManagerTaskType.INSTALL_NEW_AGENT);
        task.setAgentId(agentId);
        task.setCreatedDate(LocalDateTime.now());
        task.setStatus(PENDING);
        task.addParameter(new TaskParameterDto(ParameterType.AGENT_VERSION, version));
        saveRemoteTask(task);
        return task;

    }

    public PropertyTask createUploadDecisionsTask(int agentId, String operaSystemType, String decisionType, Long jobInstanceId) {
        return createUploadDecisionsTask(agentId, operaSystemType, decisionType, jobInstanceId, null);
    }

    public PropertyTask createUploadDecisionsTask(int agentId, String operaSystemType, String decisionType, Long jobInstanceId, Integer inputProcessingId) {
        String correlationId = UUID.randomUUID().toString();

        List<PropertyTask> tasks = new ArrayList<>();
        PropertyTask task = new PropertyTask(PropertyTaskType.UPLOAD_DECISIONS);
        task.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        task.setAgentId(agentId);
        task.setCreatedDate(LocalDateTime.now());
        task.setStatus(PENDING);
        task.addParameter(new TaskParameterDto(ParameterType.CORRELATION_ID, correlationId));
        task.addParameter(new TaskParameterDto(ParameterType.OPERA_SYS_REQ, operaSystemType));
        task.addParameter(new TaskParameterDto(ParameterType.DECISION_TYPE, decisionType));
        task.addParameter(new TaskParameterDto(ParameterType.NEXT_TASK_DELAY, NEXT_TASK_DELAY_FOR_UPLOAD_DECISIONS));
        if (jobInstanceId != null) {
            task.addParameter(new TaskParameterDto(ParameterType.JOB_INSTANCE_ID, jobInstanceId));
        }
        if (inputProcessingId != null) {
            task.addParameter(new TaskParameterDto(ParameterType.INPUT_PROCESSING_ID, inputProcessingId));
        }
        task.addParameter(new TaskParameterDto(ParameterType.COMMIT_BATCH_SIZE, getDecisionCommitBatchSize()));
        tasks.add((PropertyTask) savePropertyRemoteTask(task));
        return task;

    }

    public PropertyTask createUploadDecisionsTaskAgileRates(int agentId, String operaSystemType, String decisionType, Long jobInstanceId, Integer inputProcessingId,
                                                            String rateCode, Boolean lastRateCode) {
        String correlationId = UUID.randomUUID().toString();

        List<PropertyTask> tasks = new ArrayList<>();
        PropertyTask task = new PropertyTask(PropertyTaskType.UPLOAD_AGILE_RATES);
        task.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        task.setAgentId(agentId);
        task.setCreatedDate(LocalDateTime.now());
        task.setStatus(PENDING);
        task.addParameter(new TaskParameterDto(ParameterType.CORRELATION_ID, correlationId));
        task.addParameter(new TaskParameterDto(ParameterType.OPERA_SYS_REQ, operaSystemType));
        task.addParameter(new TaskParameterDto(ParameterType.DECISION_TYPE, decisionType));
        task.addParameter(new TaskParameterDto(ParameterType.NEXT_TASK_DELAY, NEXT_TASK_DELAY_FOR_UPLOAD_DECISIONS));
        task.addParameter(new TaskParameterDto(ParameterType.IS_LAST_RATE_CODE, lastRateCode));
        if (jobInstanceId != null) {
            task.addParameter(new TaskParameterDto(ParameterType.JOB_INSTANCE_ID, jobInstanceId));
        }
        if (inputProcessingId != null) {
            task.addParameter(new TaskParameterDto(ParameterType.INPUT_PROCESSING_ID, inputProcessingId));
        }
        task.addParameter(new TaskParameterDto(ParameterType.COMMIT_BATCH_SIZE, getDecisionCommitBatchSize()));
        task.addParameter(new TaskParameterDto(ParameterType.RATE_CODE, rateCode));
        tasks.add((PropertyTask) savePropertyRemoteTask(task));
        return task;

    }

    private Integer getDecisionCommitBatchSize() {
        return configService.getIntegerParameterValue(IntegrationConfigParamName.NO_OF_DECISIONS_TO_COMMIT.value(Constants.OPERA));
    }

    private String getPropertyTimeZone() {
        return configService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE);
    }

    private Integer getPopulationChunkSize() {
        return configService.getIntegerParameterValue(RemoteAgentConfigParamName.POPULATION_CHUNK_SIZE.value(Constants.OPERA));
    }

    private String getRoomTypesToInclude() {
        return configService.getParameterValue(IntegrationConfigParamName.ROOM_TYPES_TO_INCLUDE.value(Constants.OPERA));
    }

    private String getResTypesToInclude() {
        return configService.getValue(configService.propertyNode(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode()),
                IntegrationConfigParamName.RES_TYPES_TO_INCLUDE.value(Constants.OPERA), false);
    }

    private String getMarketSegmentsToInclude() {
        return marketSegmentComponent.findStraightMarketSegments().stream().map(MktSeg::getCode).collect(Collectors.joining(","));
    }

    private Integer getReadChunkSize() {
        return configService.getIntegerParameterValue(RemoteAgentConfigParamName.READ_CHUNK_SIZE.value(Constants.OPERA));
    }

    public RemoteTask saveTask(RemoteTask task) {
        return globalCrudService.save(task);
    }

    public PropertyRemoteTask saveTask(PropertyRemoteTask task) {
        return saveTask(task, PacmanWorkContextHelper.getPropertyId());
    }

    public PropertyRemoteTask saveTask(PropertyRemoteTask task, Integer propertyId) {
        return multiPropertyCrudService.save(propertyId, task);
    }

    public void deleteRemoteTasks(Set<Integer> taskIds) {
        if (taskIds == null || taskIds.isEmpty()) {
            return;
        }
        RemoteTaskCriteria criteria = new RemoteTaskCriteria();
        criteria.setIds(taskIds);
        List<RemoteTask> entities = findRemoteTasks(criteria);
        for (RemoteTask entity : entities) {
            deleteRemoteTask(entity);
        }
    }

    public void deletePropertyRemoteTask(Integer propertyId, Integer taskId) {
        Stream.of(multiPropertyCrudService.find(propertyId, PropertyRemoteTask.class, taskId)).filter(Objects::nonNull)
                .filter(entity -> PENDING.toString().equals(entity.getTaskStatus())).forEach(entity -> multiPropertyCrudService.delete(propertyId, entity));
    }

    public void deleteRemoteTask(RemoteTask remoteTask) {
        if (remoteTask != null) {
            globalCrudService.delete(remoteTask);
        }
    }

    public void deletePropertyRemoteTask(PropertyTask task) {
        if (task != null) {
            multiPropertyCrudService.delete(task.getPropertyId(), toTenantEntity(task));
        }
    }

    public void abandonRemoteTasks(Set<Integer> taskIds) {
        if (taskIds == null || taskIds.isEmpty()) {
            return;
        }
        RemoteTaskCriteria criteria = new RemoteTaskCriteria();
        criteria.setIds(taskIds);
        List<RemoteTask> entities = findRemoteTasks(criteria);
        for (RemoteTask entity : entities) {
            abandonRemoteTask(entity);
        }
    }

    public void abandonPropertyRemoteTask(Integer propertyId, Integer taskId) {
        Stream.of(multiPropertyCrudService.find(propertyId, PropertyRemoteTask.class, taskId)).filter(Objects::nonNull)
                .map(entity -> toDto(entity, propertyId)).forEach(task -> abandonPropertyRemoteTask(task, propertyId));
    }

    public RemoteTask abandonRemoteTask(RemoteTask remoteTask) {
        if (remoteTask == null) {
            return null;
        }

        if (!IN_PROGRESS.toString().equals(remoteTask.getTaskStatus())) {
            throw new IllegalStateException("Only " + IN_PROGRESS.toString() + " tasks can be abandoned!");
        }

        remoteTask.setTaskStatus(RemoteTaskStatus.ABANDONED.toString());
        addParameter(remoteTask, ParameterType.ABANDONED_BY, getDisplayName());
        remoteTask.setCompletedDate(LocalDateTime.now());
        return globalCrudService.save(remoteTask);
    }

    public String getDisplayName() {
        return PacmanThreadLocalContextHolder.getPrincipal() != null ? PacmanThreadLocalContextHolder.getPrincipal().getDisplayName() : "System";
    }

    public PropertyTask abandonPropertyRemoteTask(PropertyTask task, Integer propertyId) {
        if (task == null) {
            return null;
        }

        if (!IN_PROGRESS.equals(task.getStatus())) {
            throw new IllegalStateException("Only " + IN_PROGRESS.toString() + " tasks can be abandoned!");
        }

        task.setStatus(RemoteTaskStatus.ABANDONED);
        task.addParameter(new TaskParameterDto(ParameterType.ABANDONED_BY, getDisplayName()));
        task.setCompletedDate(LocalDateTime.now());
        return toDto(multiPropertyCrudService.save(propertyId, toTenantEntity(task)), propertyId);
    }

    /**
     * @deprecated
     */
    @Deprecated
    public void closePropertyRemoteTasks(Set<Integer> taskIds) {
        taskIds.stream().map(id -> multiPropertyCrudService.find(PacmanWorkContextHelper.getPropertyId(), PropertyRemoteTask.class, id))
                .filter(Objects::nonNull).filter(task -> RemoteTaskStatus.FAILED.toString().equals(task.getTaskStatus())).forEach(this::closePropertyTask);
    }

    public void closePropertyRemoteTask(Integer propertyId, Integer taskId) {
        Stream.of(multiPropertyCrudService.find(propertyId, PropertyRemoteTask.class, taskId)).filter(Objects::nonNull)
                .filter(task -> RemoteTaskStatus.FAILED.toString().equals(task.getTaskStatus())).forEach(task -> closePropertyTask(task, propertyId));
    }

    private void closePropertyTask(PropertyRemoteTask entity) {
        closePropertyTask(entity, PacmanWorkContextHelper.getPropertyId());
    }

    private void closePropertyTask(PropertyRemoteTask entity, Integer propertyId) {
        entity.setTaskStatus(RemoteTaskStatus.CLOSED.toString());
        addParameter(entity, ParameterType.CLOSED_BY, getDisplayName(), propertyId);
        addParameter(entity, ParameterType.CLOSED_DATE, LocalDateTime.now().toString("dd-MMM-yyyy HH:mm"), propertyId);
        multiPropertyCrudService.save(propertyId, entity);
    }

    public void closeRemoteTasks(Set<Integer> taskIds) {
        if (CollectionUtils.isEmpty(taskIds)) {
            return;
        }
        RemoteTaskCriteria criteria = new RemoteTaskCriteria();
        criteria.setIds(taskIds);
        List<RemoteTask> entities = findRemoteTasks(criteria);
        for (RemoteTask entity : entities) {
            if (entity.getTaskStatus().equals(FAILED.toString())) {
                entity.setTaskStatus(RemoteTaskStatus.CLOSED.toString());
                addParameter(entity, ParameterType.CLOSED_BY, getDisplayName());
                addParameter(entity, ParameterType.CLOSED_DATE, LocalDateTime.now().toString("dd-MMM-yyyy HH:mm"));
                globalCrudService.save(entity);
            }
        }
    }

    private void addParameter(RemoteTask task, ParameterType type, String value) {
        TaskParameter parameter = new TaskParameter();
        parameter.setRemoteTask(task);
        parameter.setParameterType(type.toString());
        parameter.setValue(value);
        globalCrudService.save(parameter);
    }

    private void addParameter(PropertyRemoteTask task, ParameterType type, String value, Integer propertyId) {
        PropertyTaskParameter parameter = new PropertyTaskParameter();
        parameter.setRemoteTask(task);
        parameter.setParameterType(type.toString());
        parameter.setValue(value);
        multiPropertyCrudService.save(propertyId, parameter);
    }

    public void createAddPropertyTask(RemoteAgent remoteAgent, Property property) {
        RemoteTask addPropertyTask = new RemoteTask();
        addPropertyTask.setRemoteAgent(remoteAgent);
        addPropertyTask.setTaskClass(TaskClass.AGENT.toString());
        addPropertyTask.setTaskType(AgentTaskType.ADD_PROPERTY.toString());
        addPropertyTask.setTaskStatus(PENDING.toString());
        addPropertyTask.setCreatedDate(LocalDateTime.now());

        TaskParameter idParameter = new TaskParameter();
        idParameter.setParameterType(ParameterType.PROPERTY_ID.toString());
        idParameter.setValue(property.getId().toString());
        addPropertyTask.addParameter(idParameter);

        TaskParameter nameParameter = new TaskParameter();
        nameParameter.setParameterType(ParameterType.PROPERTY_NAME.toString());
        nameParameter.setValue(property.getName());
        addPropertyTask.addParameter(nameParameter);

        // Add CLIENT_CODE parameter if this is a multi-client installation
        if (remoteAgent.getClient() == null) {
            TaskParameter clientCodeParameter = new TaskParameter();
            clientCodeParameter.setParameterType(ParameterType.CLIENT_CODE.toString());
            clientCodeParameter.setValue(property.getClient().getCode());
            addPropertyTask.addParameter(clientCodeParameter);
        }

        saveTask(addPropertyTask);
    }

    public AgentTask createDeletePropertyTask(int agentId, int propertyId) {
        AgentTask task = new AgentTask(AgentTaskType.DELETE_PROPERTY);
        task.setAgentId(agentId);
        task.setCreatedDate(LocalDateTime.now()); // this is server timezone, which is probably what we want
        task.setStatus(PENDING);
        task.addParameter(new TaskParameterDto(ParameterType.PROPERTY_ID, propertyId));
        return (AgentTask) saveRemoteTask(task);
    }

    public AgentTask createDeletePropertyTask(int agentId, int propertyId, Long jobInstanceId) {
        AgentTask task = new AgentTask(AgentTaskType.DELETE_PROPERTY);
        task.setAgentId(agentId);
        task.setCreatedDate(LocalDateTime.now()); // this is server timezone, which is probably what we want
        task.setStatus(PENDING);
        task.addParameter(new TaskParameterDto(ParameterType.PROPERTY_ID, propertyId));
        task.addParameter(new TaskParameterDto(ParameterType.JOB_INSTANCE_ID, jobInstanceId));
        return (AgentTask) saveRemoteTask(task);
    }

    @ForTesting
    public void setGlobalCrudService(CrudService globalCrudService) {
        this.globalCrudService = globalCrudService;
    }

    public List<Integer> getRemoteAgents() {
        return globalCrudService.findByNamedQuery(RemoteAgent.SELECT_REMOTE_AGENT_ID);
    }

    public boolean isValidAgentId(Integer agentId) {
        return globalCrudService.find(RemoteAgent.class, agentId) != null;
    }
}
