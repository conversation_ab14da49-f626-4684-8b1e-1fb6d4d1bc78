package com.ideas.tetris.pacman.services.groupmeetingsandevents;

import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceRevenueGroup;
import com.ideas.tetris.pacman.services.functionspace.util.GroupEvaluationFunctionSpacePackageEvalResultsUtil;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations.GroupMeetingAndEventsEvaluationAdjustmentService;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations.dto.EvaluationResultRoomClassRevenueDetail;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations.dto.EvaluationResultRoomTypeRevenueDetail;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations.dto.result.adjustment.*;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.*;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.packaging.GroupEvaluationGroupPricingPackageRevenueByArrivalDate;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.service.GroupEvaluationGroupPricingPackageResultBuilder;
import com.ideas.tetris.pacman.services.tax.util.TaxUtil;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations.dto.result.adjustment.UserAdjustmentOverride.UserAdjustmentType.GUEST_ROOM_ROH;
import static com.ideas.tetris.platform.common.time.JavaLocalDateUtils.toJavaLocalDate;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptySet;
import static java.util.Objects.nonNull;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;

public class GroupEvaluationArrivalDateAdjustmentResultMapper {

    private final GroupEvaluationArrivalDate groupEvaluationArrivalDate;
    private final boolean shouldApplyTax;
    private boolean isResultModified;

    public GroupEvaluationArrivalDateAdjustmentResultMapper(GroupEvaluationArrivalDate groupEvaluationArrivalDate, boolean shouldApplyTax) {
        this.groupEvaluationArrivalDate = groupEvaluationArrivalDate;
        this.shouldApplyTax = shouldApplyTax;
        this.isResultModified = false;
    }

    public void mapEvaluationResultAdjustments(EvaluationResultArrivalDateUserAdjustment evaluationResultUserAdjustment) {
        mapROHResultAdjustment(evaluationResultUserAdjustment);
        mapGuestRoomRatesPerNightResultAdjustment(evaluationResultUserAdjustment);
        mapRoomClassResultAdjustment(evaluationResultUserAdjustment);
        mapRevenueStreamResultAdjustment(evaluationResultUserAdjustment);
        mapPackagePricingResultAdjustment(evaluationResultUserAdjustment);
        mapUserAdjustedContractedRevenue(evaluationResultUserAdjustment);
        mapUserAdjustedNetProfitPercentage(evaluationResultUserAdjustment);
        updateAjustmentState();
    }

    private void updateAjustmentState() {
        Boolean userAdjustedOutput = Optional.ofNullable(groupEvaluationArrivalDate.getUserAdjustedOutput()).orElse(isResultModified);
        groupEvaluationArrivalDate.setUserAdjustedOutput(userAdjustedOutput);
    }

    private void mapUserAdjustedContractedRevenue(EvaluationResultArrivalDateUserAdjustment evaluationResultUserAdjustment) {
        BigDecimal userAdjustedContractedRevenue = evaluationResultUserAdjustment.getUserAdjustedContractedRevenue();
        if (Objects.nonNull(userAdjustedContractedRevenue)) {
            isResultModified = true;
            groupEvaluationArrivalDate.setUserAdjustedContractedRevenue(userAdjustedContractedRevenue);
        }
    }

    private void mapUserAdjustedNetProfitPercentage(EvaluationResultArrivalDateUserAdjustment evaluationResultUserAdjustment) {
        BigDecimal userAdjustedNetProfitPercentage = evaluationResultUserAdjustment.getUserAdjustedNetProfitPercentage();
        if (Objects.nonNull(userAdjustedNetProfitPercentage)) {
            isResultModified = true;
            groupEvaluationArrivalDate.setUserAdjustedTotalProfitPercentage(userAdjustedNetProfitPercentage);
        }
    }

    private void mapROHResultAdjustment(EvaluationResultArrivalDateUserAdjustment evaluationResultUserAdjustment) {
        BigDecimal overrideRate = getROHOverrideRate(evaluationResultUserAdjustment);
        if (Objects.nonNull(overrideRate)) {
            isResultModified = true;
            groupEvaluationArrivalDate.setUserAdjustedRoomRate(overrideRate);
        }
    }

    private BigDecimal getROHOverrideRate(EvaluationResultArrivalDateUserAdjustment evaluationResultUserAdjustment) {
        if (shouldApplyTax) {
            return TaxUtil.applyTaxToRoomRate(groupEvaluationArrivalDate.getGroupEvaluation().getTaxRate(), evaluationResultUserAdjustment.getOverrideRate());
        }

        return evaluationResultUserAdjustment.getOverrideRate();
    }

    private void mapGuestRoomRatesPerNightResultAdjustment(EvaluationResultArrivalDateUserAdjustment evaluationResultUserAdjustment) {
        Map<LocalDate, GroupEvaluationArrivalDateGuestRoomRates> guestRoomRateResultByOccupancyDate = getGuestRoomRateResultByOccupancyDate();
        BigDecimal taxRate = groupEvaluationArrivalDate.getGroupEvaluation().getTaxRate();
        Optional.ofNullable(evaluationResultUserAdjustment.getRatesPerNightUserAdjustments())
                .orElse(emptyList())
                .forEach(guestRoomRateResultUserAdjustment -> {
                    GroupEvaluationArrivalDateGuestRoomRates guestRoomRateResult = guestRoomRateResultByOccupancyDate.get(guestRoomRateResultUserAdjustment.getDate());
                    BigDecimal overrideRate = getRatersPerNightOverrideRate(guestRoomRateResultUserAdjustment, taxRate);
                    mapGuestRoomRateAdjustment(guestRoomRateResult, overrideRate);
                });
    }

    private BigDecimal getRatersPerNightOverrideRate(EvaluationResultRatesPerNight guestRoomRateResultUserAdjustment,
                                                     BigDecimal taxRate) {
        if (shouldApplyTax) {
            return TaxUtil.applyTaxToRoomRate(taxRate, guestRoomRateResultUserAdjustment.getOverrideRate());
        }

        return guestRoomRateResultUserAdjustment.getOverrideRate();
    }

    private Map<LocalDate, GroupEvaluationArrivalDateGuestRoomRates> getGuestRoomRateResultByOccupancyDate() {
        return Optional.ofNullable(groupEvaluationArrivalDate.getGroupEvaluationArrivalDateGuestRoomRates())
                .orElse(emptyList())
                .stream()
                .collect(Collectors.toMap(guestRoomRate -> toJavaLocalDate(guestRoomRate.getOccupancyDate()), Function.identity()));
    }

    private void mapGuestRoomRateAdjustment(GroupEvaluationArrivalDateGuestRoomRates guestRoomRateResult, BigDecimal overrideRate) {
        if (nonNull(guestRoomRateResult) && Objects.nonNull(overrideRate)) {
            this.isResultModified = true;
            guestRoomRateResult.setUserAdjustedRate(overrideRate);
        }
    }

    private void mapRoomClassResultAdjustment(EvaluationResultArrivalDateUserAdjustment evaluationResultUserAdjustment) {
        Map<String, GroupEvaluationArrivalDateAccomClass> arrivalDateAccomClassResultByName = getArrivalDateAccomClassResultByName();
        Optional.ofNullable(evaluationResultUserAdjustment.getRoomClassUserAdjustments())
                .orElse(emptyList())
                .forEach(roomClassResultUserAdjustment -> {
                    GroupEvaluationArrivalDateAccomClass roomClassResult = arrivalDateAccomClassResultByName.get(roomClassResultUserAdjustment.getName());
                    mapRoomTypesResultAdjustment(roomClassResultUserAdjustment, roomClassResult);
                });
    }

    private Map<String, GroupEvaluationArrivalDateAccomClass> getArrivalDateAccomClassResultByName() {
        return Optional.ofNullable(groupEvaluationArrivalDate.getGroupEvaluationArrivalDateAccomClasses())
                .orElse(emptyList())
                .stream()
                .collect(Collectors.toMap(roomClassResult -> roomClassResult.getAccomClass().getName(), Function.identity()));
    }

    private void mapRoomTypesResultAdjustment(EvaluationResultRoomClassRevenueDetail roomClassResultUserAdjustment, GroupEvaluationArrivalDateAccomClass roomClassResult) {
        Map<Integer, GroupEvaluationArrivalDateAccomType> roomTypeResultById = getRoomTypeResultById(roomClassResult);
        Optional.ofNullable(roomClassResultUserAdjustment.getRoomTypes())
                .orElse(emptyList())
                .forEach(roomTypeResultUserAdjustment -> {
                    GroupEvaluationArrivalDateAccomType roomTypeResult = roomTypeResultById.get(roomTypeResultUserAdjustment.getId());
                    BigDecimal overrideRate = getRoomTypeOverrideRate(roomTypeResultUserAdjustment);
                    mapRoomTypeRateAdjustment(roomTypeResult, overrideRate);
                });
    }

    private BigDecimal getRoomTypeOverrideRate(EvaluationResultRoomTypeRevenueDetail roomTypeResultUserAdjustment) {
        if (shouldApplyTax) {
            return TaxUtil.applyTaxToRoomRate(groupEvaluationArrivalDate.getGroupEvaluation().getTaxRate(), roomTypeResultUserAdjustment.getOverrideRate());
        }

        return roomTypeResultUserAdjustment.getOverrideRate();
    }

    private void mapRoomTypeRateAdjustment(GroupEvaluationArrivalDateAccomType roomTypeResult, BigDecimal overrideRate) {
        if (nonNull(roomTypeResult) && Objects.nonNull(overrideRate)) {
            this.isResultModified = true;
            roomTypeResult.setUserAdjustedRate(overrideRate);
        }
    }

    private static Map<Integer, GroupEvaluationArrivalDateAccomType> getRoomTypeResultById(GroupEvaluationArrivalDateAccomClass roomClassResult) {
        return Optional.ofNullable(roomClassResult.getGroupEvaluationArrivalDateAccomTypes())
                .orElse(emptyList())
                .stream()
                .collect(Collectors.toMap(groupEvaluationArrivalDateAccomType -> groupEvaluationArrivalDateAccomType.getAccomType().getId(),
                        Function.identity()));
    }

    private void mapRevenueStreamResultAdjustment(EvaluationResultArrivalDateUserAdjustment evaluationResultUserAdjustment) {
        updateRevenueStreamsResultAdjustment(evaluationResultUserAdjustment);
        List<GroupEvaluationArrivalDateUserAdjustment> revenueStreamResultUserAdjustments = buildRevenueStreamsResultAdjustment(evaluationResultUserAdjustment);
        revenueStreamResultUserAdjustments.forEach(groupEvaluationArrivalDate::addGroupEvaluationArrivalDateUserAdjustment);
    }

    private void updateRevenueStreamsResultAdjustment(EvaluationResultArrivalDateUserAdjustment evaluationResultUserAdjustment) {
        Map<String, EvaluationResultRevenueStreamUserAdjustment> revenueStreamResultUserAdjustmentByName = getRevenueStreamResultAdjustmentByName(evaluationResultUserAdjustment);
        Set<GroupEvaluationFunctionSpaceConfAndBanq> confAndBanquetInputs = groupEvaluationArrivalDate.getGroupEvaluation().getGroupEvaluationFunctionSpaceConfAndBanquets();

        Optional.ofNullable(confAndBanquetInputs)
                .orElse(emptySet())
                .forEach(confAndBanqInput -> {
                    Optional<BigDecimal> overrideRateOptional = getConfAndBanqOverrideRate(revenueStreamResultUserAdjustmentByName, confAndBanqInput);
                    overrideRateOptional.ifPresent(overrideRate -> this.isResultModified = true);
                    BigDecimal userAdjustmentRate = overrideRateOptional.orElse(confAndBanqInput.getRevenue());
                    Optional<GroupEvaluationArrivalDateUserAdjustment> conferenceAndBanquetUserAdjustmentOptional = getGroupEvaluationArrivalDateUserAdjustment(groupEvaluationArrivalDate, confAndBanqInput);
                    updateGroupEvaluationArrivalDateUserAdjustment(userAdjustmentRate, conferenceAndBanquetUserAdjustmentOptional);
                });
    }

    private void updateGroupEvaluationArrivalDateUserAdjustment(BigDecimal userAdjustmentRate,
                                                                Optional<GroupEvaluationArrivalDateUserAdjustment> conferenceAndBanquetUserAdjustmentOptional) {
        conferenceAndBanquetUserAdjustmentOptional
                .ifPresent(conferenceAndBanquetUserAdjustment -> updateConferenceAndBanquetUserAdjustment(conferenceAndBanquetUserAdjustment, userAdjustmentRate));
    }

    private List<GroupEvaluationArrivalDateUserAdjustment> buildRevenueStreamsResultAdjustment(EvaluationResultArrivalDateUserAdjustment evaluationResultUserAdjustment) {
        Map<String, EvaluationResultRevenueStreamUserAdjustment> revenueStreamResultUserAdjustmentByName = getRevenueStreamResultAdjustmentByName(evaluationResultUserAdjustment);
        Set<GroupEvaluationFunctionSpaceConfAndBanq> confAndBanquetInputs = groupEvaluationArrivalDate.getGroupEvaluation().getGroupEvaluationFunctionSpaceConfAndBanquets();

        return Optional.ofNullable(confAndBanquetInputs)
                .orElse(emptySet())
                .stream()
                .map(confAndBanqInput -> {
                    Optional<BigDecimal> overrideRateOptional = getConfAndBanqOverrideRate(revenueStreamResultUserAdjustmentByName, confAndBanqInput);
                    overrideRateOptional.ifPresent(overrideRate -> this.isResultModified = true);
                    BigDecimal userAdjustmentRate = overrideRateOptional.orElse(confAndBanqInput.getRevenue());
                    return prepareGroupEvaluationArrivalDateUserAdjustment(groupEvaluationArrivalDate, confAndBanqInput, userAdjustmentRate);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private Optional<BigDecimal> getConfAndBanqOverrideRate(Map<String, EvaluationResultRevenueStreamUserAdjustment> revenueStreamResultUserAdjustmentByName,
                                                            GroupEvaluationFunctionSpaceConfAndBanq confAndBanqInput) {
        String confAndBanqName = confAndBanqInput.getFunctionSpaceRevenueGroup().getName();
        return Optional.ofNullable(revenueStreamResultUserAdjustmentByName.get(confAndBanqName))
                .map(EvaluationResultRevenueStreamUserAdjustment::getOverrideRate);
    }

    private Map<String, EvaluationResultRevenueStreamUserAdjustment> getRevenueStreamResultAdjustmentByName(EvaluationResultArrivalDateUserAdjustment evaluationResultUserAdjustment) {
        return Optional.ofNullable(evaluationResultUserAdjustment.getRevenueStreamUserAdjustments())
                .orElse(emptyList())
                .stream()
                .filter(evaluationResultRevenueStreamUserAdjustment ->
                        GroupMeetingAndEventsEvaluationAdjustmentService.ALLOWED_REVENUE_STREAM_ADJUSTMENTS.contains(evaluationResultRevenueStreamUserAdjustment.getRevenueStreamType()))
                .collect(Collectors.toMap(EvaluationResultRevenueStreamUserAdjustment::getRevenueStreamName, Function.identity()));
    }

    private GroupEvaluationArrivalDateUserAdjustment prepareGroupEvaluationArrivalDateUserAdjustment(GroupEvaluationArrivalDate groupEvaluationArrivalDate,
                                                                                                     GroupEvaluationFunctionSpaceConfAndBanq confAndBanq,
                                                                                                     BigDecimal userAdjustmentRate) {
        Optional<GroupEvaluationArrivalDateUserAdjustment> conferenceAndBanquetUserAdjustmentOptional = getGroupEvaluationArrivalDateUserAdjustment(groupEvaluationArrivalDate, confAndBanq);
        if (conferenceAndBanquetUserAdjustmentOptional.isEmpty()) {
            return buildGroupEvaluationArrivalDateUserAdjustment(groupEvaluationArrivalDate, confAndBanq, userAdjustmentRate);
        }

        return null;
    }

    private Optional<GroupEvaluationArrivalDateUserAdjustment> getGroupEvaluationArrivalDateUserAdjustment(GroupEvaluationArrivalDate groupEvaluationArrivalDate, GroupEvaluationFunctionSpaceConfAndBanq confAndBanq) {
        List<GroupEvaluationArrivalDateUserAdjustment> groupEvaluationArrivalDateUserAdjustments = groupEvaluationArrivalDate.getGroupEvaluationArrivalDateUserAdjustments();
        return getGroupEvaluationArrivalDateUserAdjustment(confAndBanq.getFunctionSpaceRevenueGroup(), groupEvaluationArrivalDateUserAdjustments);
    }

    private void updateConferenceAndBanquetUserAdjustment(GroupEvaluationArrivalDateUserAdjustment conferenceAndBanquetUserAdjustment,
                                                          BigDecimal userAdjustmentRate) {
        conferenceAndBanquetUserAdjustment.setUserAdjustmentRate(userAdjustmentRate);
    }

    private GroupEvaluationArrivalDateUserAdjustment buildGroupEvaluationArrivalDateUserAdjustment(GroupEvaluationArrivalDate groupEvaluationArrivalDate, GroupEvaluationFunctionSpaceConfAndBanq confAndBanq, BigDecimal userAdjustmentRate) {
        GroupEvaluationArrivalDateUserAdjustment arrivalDateUserAdjustment = new GroupEvaluationArrivalDateUserAdjustment();
        arrivalDateUserAdjustment.setFunctionSpaceRevenueGroup(confAndBanq.getFunctionSpaceRevenueGroup());
        arrivalDateUserAdjustment.setGroupEvaluationArrivalDate(groupEvaluationArrivalDate);
        arrivalDateUserAdjustment.setUserAdjustmentRate(userAdjustmentRate);
        return arrivalDateUserAdjustment;
    }

    private Optional<GroupEvaluationArrivalDateUserAdjustment> getGroupEvaluationArrivalDateUserAdjustment(FunctionSpaceRevenueGroup functionSpaceRevenueGroup,
                                                                                                           List<GroupEvaluationArrivalDateUserAdjustment> groupEvaluationArrivalDateUserAdjustments) {
        for (GroupEvaluationArrivalDateUserAdjustment arrivalDateUserAdjustment : groupEvaluationArrivalDateUserAdjustments) {
            if (arrivalDateUserAdjustment.getFunctionSpaceRevenueGroup().getId().equals(functionSpaceRevenueGroup.getId())) {
                return Optional.of(arrivalDateUserAdjustment);
            }
        }
        return Optional.empty();
    }

    private void mapPackagePricingResultAdjustment(EvaluationResultArrivalDateUserAdjustment evaluationResultUserAdjustment) {
        if (isNotEmpty(groupEvaluationArrivalDate.getGroupEvaluationFunctionSpaceArrivalDatePackages())) {
            mapFSPackagePricingResultAdjustment(evaluationResultUserAdjustment, groupEvaluationArrivalDate.getGroupEvaluationFunctionSpaceArrivalDatePackages());
            return;
        }

        mapGPPackagePricingResultAdjustment(evaluationResultUserAdjustment, groupEvaluationArrivalDate.getGroupEvaluationGroupPricingPackageRevenueByArrivalDates());
    }

    private void mapFSPackagePricingResultAdjustment(EvaluationResultArrivalDateUserAdjustment evaluationResultUserAdjustment,
                                                     List<GroupEvaluationFunctionSpaceArrivalDatePackage> functionSpaceArrivalDatePackages) {
        Optional.ofNullable(functionSpaceArrivalDatePackages)
                .orElse(emptyList())
                .forEach(packageResult -> {
                    String packageName = packageResult.getGroupEvaluationFunctionSpacePackageDetail().getPackageName();
                    String packageCategory = packageResult.getGroupEvaluationFunctionSpacePackageDetail().getPackageCategory();
                    Optional<EvaluationResultPackageUserAdjustment> packageResultUserAdjustment = getMatchingPackageUserAdjustment(packageName,
                            packageCategory, evaluationResultUserAdjustment.getPackagePricingUserAdjustments());

                    packageResultUserAdjustment.ifPresent(userAdjustment -> mapPackageRateResultAdjustment(packageResult, userAdjustment));
                });
    }

    private void mapPackageRateResultAdjustment(GroupEvaluationFunctionSpaceArrivalDatePackage packageResult,
                                                EvaluationResultPackageUserAdjustment userAdjustment) {
        BigDecimal overrideRate = getFSPackageOverrideRate(packageResult, userAdjustment);
        if (Objects.nonNull(overrideRate)) {
            this.isResultModified = true;
            packageResult.setUserAdjustmentPackageRate(overrideRate);
        }
    }

    private BigDecimal getFSPackageOverrideRate(GroupEvaluationFunctionSpaceArrivalDatePackage packageResult,
                                                EvaluationResultPackageUserAdjustment userAdjustment) {
        if (shouldApplyTax) {
            return GroupEvaluationFunctionSpacePackageEvalResultsUtil.calculateUserAdjustedPackageRate(groupEvaluationArrivalDate,
                    packageResult.getGroupEvaluationFunctionSpacePackageDetail(),
                    groupEvaluationArrivalDate.getUserAdjustedRoomRate(), BigDecimal.ZERO);
        }

        return userAdjustment.getOverrideRate();
    }

    private void mapGPPackagePricingResultAdjustment(EvaluationResultArrivalDateUserAdjustment evaluationResultUserAdjustment,
                                                     List<GroupEvaluationGroupPricingPackageRevenueByArrivalDate> groupPricingPackageRevenueByArrivalDates) {
        Optional.ofNullable(groupPricingPackageRevenueByArrivalDates)
                .orElse(emptyList())
                .forEach(packageResult -> {
                    String packageName = packageResult.getGroupEvaluationGroupPricingPackageDetail().getPackageName();
                    String packageCategory = packageResult.getGroupEvaluationGroupPricingPackageDetail().getPackageCategory();
                    Optional<EvaluationResultPackageUserAdjustment> packageResultUserAdjustment = getMatchingPackageUserAdjustment(packageName,
                            packageCategory, evaluationResultUserAdjustment.getPackagePricingUserAdjustments());

                    packageResultUserAdjustment.ifPresent(userAdjustment -> mapPackageRateResultAdjustment(packageResult, userAdjustment));
                });
    }

    private void mapPackageRateResultAdjustment(GroupEvaluationGroupPricingPackageRevenueByArrivalDate packageResult,
                                                EvaluationResultPackageUserAdjustment userAdjustment) {
        BigDecimal overrideRate = getGPPackageOverrideRate(packageResult, userAdjustment);
        if (Objects.nonNull(overrideRate)) {
            this.isResultModified = true;
            packageResult.setUserAdjustmentPackageRate(overrideRate);
        }
    }

    private BigDecimal getGPPackageOverrideRate(GroupEvaluationGroupPricingPackageRevenueByArrivalDate packageResult,
                                                EvaluationResultPackageUserAdjustment userAdjustment) {
        if (shouldApplyTax) {
            new GroupEvaluationGroupPricingPackageResultBuilder().calculateUserAdjustedPackageRate(
                    packageResult.getGroupEvaluationGroupPricingPackageDetail(),
                    groupEvaluationArrivalDate.getUserAdjustedRoomRate());
        }

        return userAdjustment.getOverrideRate();
    }

    private Optional<EvaluationResultPackageUserAdjustment> getMatchingPackageUserAdjustment(String packageName,
                                                                                             String packageCategory,
                                                                                             List<EvaluationResultPackageUserAdjustment> packageResultUserAdjustments) {
        return Optional.ofNullable(packageResultUserAdjustments)
                .orElse(emptyList())
                .stream()
                .filter(packageResultUserAdjustment -> isMatchingPackage(packageName, packageCategory, packageResultUserAdjustment))
                .findFirst();
    }

    private boolean isMatchingPackage(String packageName, String packageCategory,
                                      EvaluationResultPackageUserAdjustment packageResultUserAdjustment) {
        return packageResultUserAdjustment.getName().equals(packageName) &&
                packageResultUserAdjustment.getCategory().getCaptionKey().equals(packageCategory);
    }

    public void mapEvaluationResultAdjustments(UserAdjustmentsRequest userAdjustmentsRequest) {
        if (isNotEmpty(userAdjustmentsRequest.getAdjustments())) {
            groupEvaluationArrivalDate.setUserAdjustedOutput(Boolean.TRUE);
        }
        mapGuestRoomROHAdjustment(userAdjustmentsRequest);
    }

    private void mapGuestRoomROHAdjustment(UserAdjustmentsRequest userAdjustmentsRequest) {
        userAdjustmentsRequest.getAdjustments().stream()
                .filter(override -> GUEST_ROOM_ROH.equals(override.getAdjustmentType())).findFirst()
                .ifPresent(override -> groupEvaluationArrivalDate.setUserAdjustedRoomRate(override.getOverrideRate()));
    }
}
