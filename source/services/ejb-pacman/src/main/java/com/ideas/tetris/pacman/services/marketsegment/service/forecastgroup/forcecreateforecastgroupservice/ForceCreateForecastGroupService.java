package com.ideas.tetris.pacman.services.marketsegment.service.forecastgroup.forcecreateforecastgroupservice;

import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;

import javax.inject.Inject;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class ForceCreateForecastGroupService {

    @Autowired
    JobServiceLocal jobService;


    public void createForecastGroupFor(String propertyCode, String clientCode,
                                       String isChainWide) {

        Map<String, Object> jobParameterMap = new HashMap<String, Object>();
        jobParameterMap.put(JobParameterKey.CLIENT_CODE, clientCode);
        jobParameterMap.put(JobParameterKey.PROPERTY_CODE, propertyCode);
        jobParameterMap.put(JobParameterKey.DATE, new Date());
        jobService.startGuaranteedNewInstance(JobName.ForceCreateForecastGroups, jobParameterMap);
    }
}
