package com.ideas.tetris.pacman.services.groupflooroverride;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.pojo.Pair;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.PricingRule;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutputOverride;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.groupblock.GroupBlockDetail;
import com.ideas.tetris.pacman.services.groupblock.GroupBlockMaster;
import com.ideas.tetris.pacman.services.groupflooroverride.entity.AssumeMFN;
import com.ideas.tetris.pacman.services.groupflooroverride.entity.GroupFloorAccomClass;
import com.ideas.tetris.pacman.services.groupflooroverride.entity.GroupFloorMarketSegment;
import com.ideas.tetris.pacman.services.groupflooroverride.entity.GroupFloorOverride;
import com.ideas.tetris.pacman.services.groupflooroverride.entity.GroupFloorOverrideReason;
import com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentSummary;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingBaseAccomType;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.TransientPricingBaseAccomType;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.CONSIDER_CEILING_OVERRIDE_FOR_GFO_EVALUATION;
import static com.ideas.tetris.pacman.services.groupflooroverride.GroupFloorOverrideConstants.NEW_GROUP_ADDED_REASON_ID;
import static java.util.Collections.emptyList;
import static java.util.Comparator.comparing;
import static java.util.Comparator.comparingInt;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.maxBy;
import static java.util.stream.Collectors.toMap;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Component
@Transactional
public class GroupFloorOverrideService {

    private static final Logger LOGGER = Logger.getLogger(GroupFloorOverrideService.class);

    @Autowired
	private GroupFloorOverrideRepository groupFloorOverrideRepository;
    @Autowired
	private PacmanConfigParamsService configParamsService;

    @Autowired
	private DecisionService decisionService;

    @Autowired
	private GroupFloorDecisionOutputOverrideService cpOverrideService;

    @Autowired
	private GroupFloorOverrideMergingService mergingService;

    @Autowired
	private SyncEventAggregatorService syncEventAggregatorService;

    @Autowired
    private DateService dateService;

    public void evaluateGroupFloorOverrides(LocalDate startDate, LocalDate endDate) {
        Decision groupFloorOverrideDecision = decisionService.createGroupFloorOverrideDecision();
        Decision barOverrideDecision = decisionService.createBAROverrideDecision();
        evaluateGroupFloorOverrides(startDate, endDate, groupFloorOverrideDecision.getId(), barOverrideDecision.getId());
    }

    public void evaluateGroupFloorOverrides(LocalDate startDate, LocalDate endDate, Integer groupFloorOverrideDecisionId, Integer barOverrideDecisionId) {
        LOGGER.info(String.format("GFO: ClientCode : %s  PropertyCode : %s Group Floor Override Evaluation from %s to %s",
                PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode(), startDate, endDate));
        GroupFloorOverridePerformanceCache cache = initCache(startDate, endDate);
        Pair<String, Double> minDifferenceRule = getMinDifferenceRule();
        Product barProduct = groupFloorOverrideRepository.getProduct();
        PricingRule pricingRule = cpOverrideService.getPricingRuleForProduct(barProduct);


        Pair<LocalDate, LocalDate> dateRange = new Pair<>(startDate, endDate);
        Map<LocalDate, List<GroupFloorOverride>> applicableGroupFloorOverridesByDate = determineApplicableGroupFloorOverrides(dateRange, groupFloorOverrideDecisionId, minDifferenceRule, pricingRule, cache);

        Map<LocalDate, List<GroupFloorOverride>> existingOverridesByDate = groupFloorOverrideRepository.fetchExistingGroupFloorOverrides(dateRange.getFirst(), dateRange.getSecond());

        for (LocalDate occupancyDate = startDate; occupancyDate.isBefore(endDate); occupancyDate = occupancyDate.plusDays(1)) {
            List<GroupFloorOverride> applicableGroupFloorOverrides = applicableGroupFloorOverridesByDate.getOrDefault(occupancyDate, emptyList());

            List<GroupFloorOverride> existingOverrides = existingOverridesByDate.getOrDefault(occupancyDate, emptyList());

            List<GroupFloorOverride> groupFloorOverridesByDate = mergingService.merge(existingOverrides, applicableGroupFloorOverrides, groupFloorOverrideDecisionId, cache);

            applyCPOverrides(groupFloorOverridesByDate, cache, occupancyDate, barProduct, barOverrideDecisionId, groupFloorOverrideDecisionId);
        }
    }

    private GroupFloorOverridePerformanceCache initCache(LocalDate startDate, LocalDate endDate) {
        GroupFloorOverridePerformanceCache cache = new GroupFloorOverridePerformanceCache();
        List<AccomType> activeAccomTypes = groupFloorOverrideRepository.findAllActiveAccomTypes();
        cache.addAccomTypes(activeAccomTypes);

        List<PricingAccomClass> baseRoomTypes = groupFloorOverrideRepository.getPricingAccomClasses();
        cache.addBaseRoomTypes(baseRoomTypes);

        List<PricingBaseAccomType> groupFloorAndCeilings = groupFloorOverrideRepository.fetchSeasonalGroupFloorAndCeilings(startDate, endDate);
        cache.addSeasonFloorAndCeilings(groupFloorAndCeilings);

        List<GroupFloorOverrideReason> allOverrideReasons = groupFloorOverrideRepository.findAllOverrideReasons();
        cache.addOverrideReasonCache(allOverrideReasons);

        if (shouldConsiderCeilingOverrideForGFOEvaluation()) {
            cache.addAccomTypeArrivalDateToCeilOverrideMap(getAccomTypeArriDatePairToCeilOverrideMap(startDate, endDate));
        }

        return cache;
    }

    private boolean shouldConsiderCeilingOverrideForGFOEvaluation() {
        return configParamsService.getBooleanParameterValue(CONSIDER_CEILING_OVERRIDE_FOR_GFO_EVALUATION);
    }

    private Map<AccomTypeDatePair, BigDecimal> getAccomTypeArriDatePairToCeilOverrideMap(LocalDate startDate, LocalDate endDate) {
        List<CPDecisionBAROutputOverride> overrides = groupFloorOverrideRepository.fetchLatestCeilCPOverrideForArrivalDateRange(startDate, endDate);
        return Optional.ofNullable(overrides).orElse(emptyList()).stream()
                .collect(toMap(override -> new AccomTypeDatePair(override.getAccomType().getId(), override.getArrivalDate()), CPDecisionBAROutputOverride::getNewCeilingRate));
    }

    public int updatePeakNightForGroupBlocksBetween(LocalDate startDate, LocalDate endDate) {
        Double shoulderPercent = configParamsService.getParameterValue(FeatureTogglesConfigParamName.GROUP_FLOOR_SHOULDER_NIGHT_PERCENT_THRESHOLD);
        LOGGER.info(String.format("GFO: ClientCode : %s  PropertyCode : %s Peak Night Evaluation with threshold percent %s from %s to %s",
                PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode(), shoulderPercent, startDate, endDate));

        return groupFloorOverrideRepository.updatePeakNightStatusForGroupBlocksBetween(startDate, endDate, BigDecimal.valueOf(shoulderPercent).divide(BigDecimal.valueOf(100), RoundingMode.HALF_UP));
    }

    public List<GroupFloorOverride> getGroupFloorOverridesFor(AccomClass accomClass, LocalDate startDate, LocalDate endDate) {
        return groupFloorOverrideRepository.fetchApplicableGroupFloorOverridesFor(accomClass, startDate, endDate);
    }

    public List<AccomClassBlocksDTO> getBlocksFor(List<PricingAccomClass> accomClasses, LocalDate startDate, LocalDate endDate) {
        List<Integer> accomClassesID = accomClasses.stream()
                .map(pricingAccomClass -> pricingAccomClass.getAccomClass().getId())
                .collect(Collectors.toList());
        List<AccomClassBlocksDTO> accomClassBlocks = groupFloorOverrideRepository.fetchBlocksFor(accomClassesID, startDate, endDate);
        return Optional.ofNullable(accomClassBlocks).orElse(Collections.emptyList());
    }

    public List<AccomClassBlocksDTO> getTotalBlocksFor(LocalDate startDate, LocalDate endDate) {
        List<AccomClassBlocksDTO> accomClassBlocksDTOS = groupFloorOverrideRepository.fetchTotalBlockForOccupancyDate(startDate, endDate);
        return Optional.ofNullable(accomClassBlocksDTOS).orElse(Collections.emptyList());
    }

    public List<PricingAccomClass> getPricingAccomClasses() {
        return applyGFMFilterCriteria(groupFloorOverrideRepository.getPricingAccomClasses());
    }

    public List<PricingAccomClass> getGFOApplicablePricingAccomClasses() {
        return applyGFMFilterCriteria(groupFloorOverrideRepository.getGFOApplicableClasses());
    }

    private List<PricingAccomClass> applyGFMFilterCriteria(List<PricingAccomClass> pricingAccomClasses) {
        return pricingAccomClasses.stream().filter(pricingAccomClass -> !pricingAccomClass.isPriceExcluded())
                .sorted(comparing(pricingAccomClass -> pricingAccomClass.getAccomClass().getViewOrder()))
                .collect(Collectors.toList());
    }

    private Collection<GroupFloorOverride> applyCPOverrides(List<GroupFloorOverride> groupFloorOverrides, GroupFloorOverridePerformanceCache cache, LocalDate occupancyDate, Product barProduct, Integer barOverrideDecisionId, Integer groupFloorOverrideDecisionId) {
        cpOverrideService.createCPOverrides(groupFloorOverrides, cache, occupancyDate, barProduct, barOverrideDecisionId, groupFloorOverrideDecisionId);
        return groupFloorOverrideRepository.saveGroupFloorOverrides(groupFloorOverrides);
    }

    private Pair<String, Double> getMinDifferenceRule() {
        Double minDifferenceValue = configParamsService.getParameterValue(FeatureTogglesConfigParamName.GROUP_FLOOR_MIN_DIFFERENCE_VALUE);
        String minDifferenceType = configParamsService.getParameterValue(FeatureTogglesConfigParamName.GROUP_FLOOR_MIN_DIFFERENCE_TYPE);
        return new Pair<>(minDifferenceType, minDifferenceValue);
    }

    private Map<LocalDate, List<GroupFloorOverride>> determineApplicableGroupFloorOverrides(Pair<LocalDate, LocalDate> dateRange, Integer groupFloorOverrideDecisionId, Pair<String, Double> minDifferenceRule, PricingRule pricingRule, GroupFloorOverridePerformanceCache cache) {
        LocalDate occupancyDate = dateRange.getFirst();
        LocalDate endDate = dateRange.getSecond();
        List<GroupBlockDetail> groupBlockDetails;
        Integer daysToArrival = configParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.REMOVE_GROUP_FLOOR_DAYS_TO_ARRIVAL_VALUE.getParameterName());
        LocalDate caughtUpDate = new LocalDate(dateService.getCaughtUpDateFromCache());
        boolean isGroupFloorOverrideDaysToArrivalEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_REMOVE_GROUP_FLOOR_OVERRIDE_DAYS_TO_ARRIVAL_ENABLED) && (daysToArrival != null);
        if (isAssumeMFNToggleSet()) {
            // fetch all,  removing entries present in assumeMFNCfg
            groupBlockDetails = groupFloorOverrideRepository.fetchGroupBlocksUnFilteredWithAssumeMFN(occupancyDate, endDate); // 1 db call
        } else {
            // fetch all entries present in assumeMFNCfg
            groupBlockDetails = groupFloorOverrideRepository.fetchGroupBlocksUnFilteredWithoutAssumeMFN(occupancyDate, endDate); // 1 db call
        }

        List<GroupBlockDetail> groupBlockDetailsOfNonConfiguredMarketSegmentGroups = groupFloorOverrideRepository.getNonConfiguredMarketSegmentGroupsFromConfigTable(occupancyDate, endDate);
        for (GroupBlockDetail groupBlockDetailsOfNonConfiguredMarketSegmentGroup : groupBlockDetailsOfNonConfiguredMarketSegmentGroups) {
            if (isGroupBlockDetailNotPresentIn(groupBlockDetailsOfNonConfiguredMarketSegmentGroup, groupBlockDetails)) {
                groupBlockDetails.add(groupBlockDetailsOfNonConfiguredMarketSegmentGroup);
            }
        }

        Map<LocalDate, List<GroupBlockDetail>> groupBlocksByOccupancyDate = groupBlockDetails.stream().
                collect(groupingBy(GroupBlockDetail::getOccupancyDate));

        Map<LocalDate, List<GroupFloorOverride>> groupFloorOverridesByOccupancyDate = new HashMap<>();

        for (Map.Entry<LocalDate, List<GroupBlockDetail>> groupBlocks : groupBlocksByOccupancyDate.entrySet()) {
            LocalDate date = groupBlocks.getKey();
            if(isGroupFloorOverrideDaysToArrivalEnabled && date.compareTo(caughtUpDate.plusDays(daysToArrival)) <= 0) {
               continue;
            }
            List<GroupBlockDetail> groupBlockDetailsForADate = groupBlocks.getValue();

            List<GroupFloorOverride> groupFloorOverrides = getGroupFloorOverrides(groupFloorOverrideDecisionId, minDifferenceRule, pricingRule, cache, groupBlockDetailsForADate);
            groupFloorOverridesByOccupancyDate.put(date, groupFloorOverrides);
        }

        return groupFloorOverridesByOccupancyDate;
    }

    private boolean isGroupBlockDetailNotPresentIn(GroupBlockDetail groupBlockDetailsOfNonConfiguredMarketSegmentGroup, List<GroupBlockDetail> groupBlockDetails) {
        return groupBlockDetails.stream().noneMatch(groupBlockDetail -> groupBlockDetailsOfNonConfiguredMarketSegmentGroup.getId().equals(groupBlockDetail.getId()));
    }

    private List<GroupFloorOverride> getGroupFloorOverrides(Integer groupFloorOverrideDecisionId, Pair<String, Double> minDifferenceRule, PricingRule pricingRule, GroupFloorOverridePerformanceCache cache, List<GroupBlockDetail> groupBlockDetails) {
        Map<AccomClass, List<GroupBlockDetail>> groupBlocksByRoomClass = groupBlockDetails.stream()
                .collect(groupingBy(groupBlock -> getRoomClass(groupBlock, cache))); // 1  for each unique Accom type db call

        List<GroupBlockDetail> groupBlocksWithMaxRate = fetchGroupBlocksWithMaxRate(groupBlocksByRoomClass, cache); // 1 for each accom class db call

        return groupBlocksWithMaxRate.stream()
                .map(groupBlockDetail -> convertToGroupFloorOverride(groupBlockDetail, groupFloorOverrideDecisionId, minDifferenceRule, pricingRule, cache))
                .filter(gfo -> isRateBetweenFloorAndCeiling(gfo, cache))
                .collect(Collectors.toList());
    }

    private boolean isRateBetweenFloorAndCeiling(GroupFloorOverride applicableGroupFloorOverride, GroupFloorOverridePerformanceCache cache) {
        LocalDate occupancyDate = applicableGroupFloorOverride.getOccupancyDate();

        Integer accomClassId = applicableGroupFloorOverride.getAccomClass().getId();
        List<PricingBaseAccomType> seasonalFloorAndCeilingForAccomClass = cache.getSeasonalFloorAndCeilingCache(accomClassId);
        PricingBaseAccomType pricingBaseAccomType = seasonalFloorAndCeilingForAccomClass
                .stream()
                .filter(pbat -> isOccupancyDateBetweenSeason(occupancyDate, pbat.getStartDate(), pbat.getEndDate()))
                .findFirst()
                .orElse(getPricingBaseAccomTypeForDefaultSeason(seasonalFloorAndCeilingForAccomClass));
        return shouldConsiderCeilingOverrideForGFOEvaluation() ?
                isGroupRateBetweenFloorAndCeilingConsideringCeilingOvrride(applicableGroupFloorOverride, cache, occupancyDate, pricingBaseAccomType)
                : isGroupRateBetweenFloorAndCeiling(occupancyDate, pricingBaseAccomType, applicableGroupFloorOverride.getPrettyGroupRate());
    }

    private boolean isGroupRateBetweenFloorAndCeilingConsideringCeilingOvrride(GroupFloorOverride applicableGroupFloorOverride, GroupFloorOverridePerformanceCache cache, LocalDate occupancyDate, PricingBaseAccomType pricingBaseAccomType) {
        BigDecimal ceilOverride = cache.getAccomTypeArrivalDateToCeilOverrideMap().get(new AccomTypeDatePair(applicableGroupFloorOverride.getBaseAccomType().getId(), occupancyDate));
        return isGroupRateBetweenFloorAndCeiling(occupancyDate, pricingBaseAccomType, applicableGroupFloorOverride.getPrettyGroupRate(), ceilOverride);
    }

    private boolean isGroupRateBetweenFloorAndCeiling(LocalDate occupancyDate, PricingBaseAccomType pricingBaseAccomType, BigDecimal groupRate, BigDecimal ceilOverride) {
        Map<DayOfWeek, BigDecimal> floorValueByDayOfWeekMap = ((TransientPricingBaseAccomType) pricingBaseAccomType).getFloorValueByDayOfWeekMap();
        Map<DayOfWeek, BigDecimal> ceilingValueByDayOfWeekMap = ((TransientPricingBaseAccomType) pricingBaseAccomType).getCeilingValueByDayOfWeekMap();
        DayOfWeek dayOfWeek = LocalDateUtils.getDayOfWeek(occupancyDate);
        boolean groupRateGreaterThanFloorRate = BigDecimalUtil.isGreaterThan(groupRate, floorValueByDayOfWeekMap.get(dayOfWeek));
        BigDecimal ceilRate = Optional.ofNullable(ceilOverride).orElse(ceilingValueByDayOfWeekMap.get(dayOfWeek));
        boolean groupRateLessThanCeilingRate = BigDecimalUtil.isLessThan(groupRate, ceilRate);
        return groupRateGreaterThanFloorRate && groupRateLessThanCeilingRate;
    }

    private boolean isGroupRateBetweenFloorAndCeiling(LocalDate occupancyDate, PricingBaseAccomType pricingBaseAccomType, BigDecimal groupRate) {
        Map<DayOfWeek, BigDecimal> floorValueByDayOfWeekMap = ((TransientPricingBaseAccomType) pricingBaseAccomType).getFloorValueByDayOfWeekMap();
        Map<DayOfWeek, BigDecimal> ceilingValueByDayOfWeekMap = ((TransientPricingBaseAccomType) pricingBaseAccomType).getCeilingValueByDayOfWeekMap();
        DayOfWeek dayOfWeek = LocalDateUtils.getDayOfWeek(occupancyDate);
        boolean groupRateGreaterThanFloorRate = BigDecimalUtil.isGreaterThan(groupRate, floorValueByDayOfWeekMap.get(dayOfWeek));
        boolean groupRateLessThanCeilingRate = BigDecimalUtil.isLessThan(groupRate, ceilingValueByDayOfWeekMap.get(dayOfWeek));
        return groupRateGreaterThanFloorRate && groupRateLessThanCeilingRate;
    }

    private boolean isOccupancyDateBetweenSeason(LocalDate occupancyDate, LocalDate floorAndCeilingStartDate, LocalDate floorAndCeilingEndDate) {
        return floorAndCeilingStartDate != null && floorAndCeilingEndDate != null && !occupancyDate.isBefore(floorAndCeilingStartDate) && !occupancyDate.isAfter(floorAndCeilingEndDate);
    }

    private PricingBaseAccomType getPricingBaseAccomTypeForDefaultSeason(List<PricingBaseAccomType> groupFloorAndCeilings) {

        Optional<PricingBaseAccomType> pricingBaseAccomType = groupFloorAndCeilings.stream().filter(pbat -> pbat.getStartDate() == null && pbat.getEndDate() == null).findFirst();
        if (!pricingBaseAccomType.isPresent()) {
            throw new TetrisException("Default Floor and Ceiling for Base RT not defined.");
        }
        return pricingBaseAccomType.get();
    }

    private AccomClass getRoomClass(GroupBlockDetail groupBlock, GroupFloorOverridePerformanceCache cache) {
        Integer accommodationTypeId = groupBlock.getAccommodationTypeId();
        AccomType accomType = cache.getAccomTypeCache().get(accommodationTypeId);
        return accomType.getAccomClass();
    }

    private GroupFloorOverride convertToGroupFloorOverride(GroupBlockDetail groupBlockDetail, Integer groupFloorOverrideDecisionId, Pair<String, Double> minDifferenceRule, PricingRule pricingRule, GroupFloorOverridePerformanceCache cache) {
        AccomType accomType = groupBlockDetail.getAccommodationType();
        AccomClass accomClass = accomType.getAccomClass();
        Integer baseAccomTypeId = cache.getBaseAccomTypeCache().get(accomClass.getId());
        AccomType baseAccomType = cache.getAccomTypeCache().get(baseAccomTypeId);

        GroupFloorOverride override = new GroupFloorOverride();
        override.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        override.setOccupancyDate(groupBlockDetail.getOccupancyDate());
        override.setAccomType(accomType);
        override.setBaseAccomType(baseAccomType);
        override.setAccomClass(accomClass);
        override.setGroupMaster(groupBlockDetail.getGroupBlockMaster());
        override.setGroupBlockRate(groupBlockDetail.getRate());
        setPrettyGroupRateWithMinDifferenceRule(override, minDifferenceRule, pricingRule, groupBlockDetail.getRate());
        GroupFloorOverrideReason reason = cache.getOverrideReason(NEW_GROUP_ADDED_REASON_ID);
        override.setOverrideReason(reason);
        override.setCreateDate(LocalDateTime.now());
        override.setLastUpdatedDate(LocalDateTime.now());
        override.setDecisionId(groupFloorOverrideDecisionId);
        override.setGFOApplied(true);

        return override;
    }

    private void setPrettyGroupRateWithMinDifferenceRule(GroupFloorOverride override, Pair<String, Double> minDifferenceRule, PricingRule pricingRule, BigDecimal groupBlockRate) {
        BigDecimal minDifferenceGroupRate;
        if (Constants.GROUP_FLOOR_MIN_DIFFERENCE_FIXED.equalsIgnoreCase(minDifferenceRule.getFirst())) {
            minDifferenceGroupRate = BigDecimal.valueOf(minDifferenceRule.getSecond()).add(groupBlockRate);
        } else {
            minDifferenceGroupRate = multiplyPriceByOnePlusValuePercentage(groupBlockRate, BigDecimal.valueOf(minDifferenceRule.getSecond()));
        }
        BigDecimal prettyPrice = pricingRule.calculatePrettyPrice(minDifferenceGroupRate);
        override.setPrettyGroupRate(prettyPrice);
    }

    private BigDecimal multiplyPriceByOnePlusValuePercentage(BigDecimal price, BigDecimal offsetValue) {
        return price.multiply(BigDecimal.ONE.add(offsetValue.setScale(5, RoundingMode.HALF_UP).divide(BigDecimal.valueOf(100), RoundingMode.HALF_UP)));
    }

    private List<GroupBlockDetail> fetchGroupBlocksWithMaxRate(Map<AccomClass, List<GroupBlockDetail>> groupBlocksByRoomClass, GroupFloorOverridePerformanceCache cache) {
        return groupBlocksByRoomClass.entrySet().stream()
                .map(entry -> fetchGroupBlockWithMaxRateForRoomClass(entry.getKey(), entry.getValue(), cache))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());
    }

    @ForTesting
	public
    Optional<GroupBlockDetail> fetchGroupBlockWithMaxRateForRoomClass(AccomClass accomClass, List<GroupBlockDetail> groupBlocks, GroupFloorOverridePerformanceCache cache) {
        Integer baseRoomTypeId = cache.getBaseAccomTypeCache().get(accomClass.getId());
        final Optional<GroupBlockDetail> baseRoomTypeBlock = getApplicableGroupBlockDetailForBaseRoomType(groupBlocks, baseRoomTypeId);
        final Optional<GroupBlockDetail> nonBaseRoomTypeBlock = getApplicableGroupBlockDetailForNonBaseRoomType(groupBlocks, baseRoomTypeId);
        if (baseRoomTypeBlock.isPresent() && nonBaseRoomTypeBlock.isPresent()) {
            return Optional.of(getMaxRateGroupBlockDetail(baseRoomTypeBlock.get(), nonBaseRoomTypeBlock.get()));
        }
        if (baseRoomTypeBlock.isPresent()) {
            return baseRoomTypeBlock;
        }
        return nonBaseRoomTypeBlock;
    }

    private Optional<GroupBlockDetail> getApplicableGroupBlockDetailForBaseRoomType(List<GroupBlockDetail> groupBlocks, Integer baseRoomTypeId) {
        return groupBlocks.stream()
                .filter(groupBlock -> isGroupBlockOf(baseRoomTypeId, groupBlock))
                .max(comparing(GroupBlockDetail::getRate));
    }

    private Optional<GroupBlockDetail> getApplicableGroupBlockDetailForNonBaseRoomType(List<GroupBlockDetail> groupBlocks, Integer baseRoomTypeId) {
        final Set<GroupBlockMaster> groupsWithBaseRoomType = getGroupsWithBaseRoomType(groupBlocks, baseRoomTypeId);
        return groupBlocks.stream()
                .filter(groupBlock -> !groupsWithBaseRoomType.contains(groupBlock.getGroupBlockMaster()))
                .collect(groupingBy(GroupBlockDetail::getGroupBlockMaster, maxBy(getGroupBlockThenRateComparator())))
                .values()
                .stream()
                .map(Optional::get)
                .max(comparing(GroupBlockDetail::getRate));
    }

    public Map<GroupBlockMaster, GroupDetailsDTO> getGroupBlockRateDetailsFor(PricingAccomClass pricingAccomClass, LocalDate startDate, LocalDate endDate) {
        final List<GroupBlockDetail> groupBlockDetails = groupFloorOverrideRepository.getGroupBlockDetailsFor(pricingAccomClass.getAccomClass(), startDate, endDate);
        final Map<GroupBlockMaster, Map<LocalDate, GroupBlockDetail>> groupMasterToDateWiseGroupDetailsMap = groupBlockDetails.stream()
                .collect(groupingBy(GroupBlockDetail::getGroupBlockMaster,
                        Collectors.toMap(GroupBlockDetail::getOccupancyDate, Function.identity(), (oldGroupBlock, newGroupBlock) -> getApplicableGroupBlock(oldGroupBlock, newGroupBlock, pricingAccomClass.getAccomType()))));

        Map<GroupBlockMaster, GroupDetailsDTO> groupToDTOMap = new HashMap<>();
        final List<AssumeMFN> allAssumeMFNGroups = groupFloorOverrideRepository.getAllAssumeMFNGroups();

        groupMasterToDateWiseGroupDetailsMap.forEach((groupBlockMaster, dateToGroupBlockDetailsMap) -> {
            GroupDetailsDTO dto = new GroupDetailsDTO();
            dto.setMFNGroup(isAssumeMFN(groupBlockMaster, allAssumeMFNGroups));
            dto.setOccupancyDateToGroupDetailsMap(dateToGroupBlockDetailsMap);
            groupToDTOMap.put(groupBlockMaster, dto);
        });
        return groupToDTOMap;
    }
    public Map<GroupBlockMaster, GroupDetailsDTO> fetchOptimizedGroupBlockRateDetails(PricingAccomClass pricingAccomClass, LocalDate startDate, LocalDate endDate) {
        LOGGER.info("Fetching group block rate details using optimized approach");
        final List<GroupBlockDetail> groupBlockDetails = groupFloorOverrideRepository.getGroupBlockDetailsUsingJoinFetch(pricingAccomClass.getAccomClass(), startDate, endDate);
        final Map<GroupBlockMaster, Map<LocalDate, GroupBlockDetail>> groupMasterToDateWiseGroupDetailsMap = groupBlockDetails.stream()
                .collect(groupingBy(GroupBlockDetail::getGroupBlockMaster,
                        Collectors.toMap(GroupBlockDetail::getOccupancyDate, Function.identity(), (oldGroupBlock, newGroupBlock) -> getApplicableGroupBlock(oldGroupBlock, newGroupBlock, pricingAccomClass.getAccomType()))));

        Map<GroupBlockMaster, GroupDetailsDTO> groupToDTOMap = new HashMap<>();
        final List<AssumeMFN> allAssumeMFNGroups = groupFloorOverrideRepository.getAllAssumeMFNGroupsEagerFetch();
        List<GroupFloorMarketSegment> configuredGroupFloorMarketSegments = groupFloorOverrideRepository.getConfiguredGroupFloorMarketSegmentsEagerly();
        groupMasterToDateWiseGroupDetailsMap.forEach((groupBlockMaster, dateToGroupBlockDetailsMap) -> {
            GroupDetailsDTO dto = new GroupDetailsDTO();
            dto.setMFNGroup(isAssumeMFNOptimized(groupBlockMaster, allAssumeMFNGroups,configuredGroupFloorMarketSegments));
            dto.setOccupancyDateToGroupDetailsMap(dateToGroupBlockDetailsMap);
            groupToDTOMap.put(groupBlockMaster, dto);
        });
        return groupToDTOMap;
    }
    private Set<GroupBlockMaster> getGroupsWithBaseRoomType(List<GroupBlockDetail> groupBlocks, Integer baseRoomTypeId) {
        return groupBlocks.stream()
                .filter(groupBlock -> isGroupBlockOf(baseRoomTypeId, groupBlock))
                .map(GroupBlockDetail::getGroupBlockMaster)
                .collect(Collectors.toSet());
    }

    private boolean isGroupBlockOf(Integer baseRoomTypeId, GroupBlockDetail groupBlock) {
        return baseRoomTypeId.equals(groupBlock.getAccommodationTypeId());
    }

    private GroupBlockDetail getApplicableGroupBlock(GroupBlockDetail oldGroupBlock, GroupBlockDetail newGroupBlock, AccomType baseAccomType) {
        if (isBaseRoomTypeGroupBlock(oldGroupBlock, baseAccomType) && isBaseRoomTypeGroupBlock(newGroupBlock, baseAccomType)) {
            return getMaxRateGroupBlockDetail(oldGroupBlock, newGroupBlock);
        }

        if (!isBaseRoomTypeGroupBlock(oldGroupBlock, baseAccomType) && !isBaseRoomTypeGroupBlock(newGroupBlock, baseAccomType)) {
            return getGroupBlockThenRateComparator().compare(oldGroupBlock, newGroupBlock) >= 0 ? oldGroupBlock : newGroupBlock;
        }

        return isBaseRoomTypeGroupBlock(oldGroupBlock, baseAccomType) ? oldGroupBlock : newGroupBlock;
    }

    private GroupBlockDetail getMaxRateGroupBlockDetail(GroupBlockDetail oldGroupBlock, GroupBlockDetail newGroupBlock) {
        return oldGroupBlock.getRate().compareTo(newGroupBlock.getRate()) >= 0 ? oldGroupBlock : newGroupBlock;
    }

    private boolean isBaseRoomTypeGroupBlock(GroupBlockDetail groupBlockDetail, AccomType baseAccomType) {
        return baseAccomType.equals(groupBlockDetail.getAccommodationType());
    }

    private Comparator<GroupBlockDetail> getGroupBlockThenRateComparator() {
        return comparingInt(GroupBlockDetail::getBlocks).thenComparing(GroupBlockDetail::getRate);
    }

    private boolean isAssumeMFN(GroupBlockMaster groupMaster, List<AssumeMFN> allAssumeMFNGroups) {
        final Integer groupMasterId = groupMaster.getId();
        final List<GroupFloorMarketSegment> allConfiguredMarketSegments = groupFloorOverrideRepository.getConfiguredGroupFloorMarketSegments();

        Optional<AssumeMFN> groupInAssumeMFNGroups = findGroupInListOf(allAssumeMFNGroups, groupMasterId);
        boolean isGroupOfNonConfiguredMarketSegment = isGroupOfNonConfiguredMarketSegment(groupMaster, allConfiguredMarketSegments);

        if (groupInAssumeMFNGroups.isPresent()) {
            if (groupInAssumeMFNGroups.get().getMfnStatus()) {
                return Boolean.TRUE;
            } else {
                if (isAssumeMFNToggleSet()) {
                    return Boolean.FALSE;
                }
                return Boolean.TRUE;
            }
        } else {
            if (isGroupOfNonConfiguredMarketSegment) {
                return Boolean.FALSE;
            }
            if (isAssumeMFNToggleSet()) {
                return Boolean.TRUE;
            }
            return Boolean.FALSE;
        }
    }
    private boolean isAssumeMFNOptimized(GroupBlockMaster groupMaster, List<AssumeMFN> allAssumeMFNGroups, List<GroupFloorMarketSegment> allConfiguredMarketSegments) {
        final Integer groupMasterId = groupMaster.getId();

        Optional<AssumeMFN> groupInAssumeMFNGroups = findGroupInListOf(allAssumeMFNGroups, groupMasterId);
        boolean isGroupOfNonConfiguredMarketSegment = isGroupOfNonConfiguredMarketSegment(groupMaster, allConfiguredMarketSegments);

        if (groupInAssumeMFNGroups.isPresent()) {
            if (groupInAssumeMFNGroups.get().getMfnStatus()) {
                return Boolean.TRUE;
            } else {
                if (isAssumeMFNToggleSet()) {
                    return Boolean.FALSE;
                }
                return Boolean.TRUE;
            }
        } else {
            if (isGroupOfNonConfiguredMarketSegment) {
                return Boolean.FALSE;
            }
            if (isAssumeMFNToggleSet()) {
                return Boolean.TRUE;
            }
            return Boolean.FALSE;
        }
    }
    private Optional<AssumeMFN> findGroupInListOf(List<AssumeMFN> allAssumeMFNGroups, Integer groupMasterId) {
        return allAssumeMFNGroups.stream()
                .filter(assumeMFN -> groupMasterId.equals(assumeMFN.getGroupMaster().getId()))
                .findFirst();
    }

    private Boolean isAssumeMFNToggleSet() {
        return configParamsService.getParameterValue(FeatureTogglesConfigParamName.GROUP_FLOOR_OVERRIDE_ASSUME_MFN);
    }

    public void updateMFNStatus(Map<GroupBlockMaster, GroupDetailsDTO> groupMasterToGroupDetailsDTOMap) {
        if (MapUtils.isEmpty(groupMasterToGroupDetailsDTOMap)) {
            return;
        }
        List<GroupFloorMarketSegment> allConfiguredMarketSegments = groupFloorOverrideRepository.getConfiguredGroupFloorMarketSegments();
        List<GroupBlockMaster> groupsToBeAdded = new ArrayList<>();
        List<GroupBlockMaster> groupsToBeRemoved = new ArrayList<>();
        Map<GroupBlockMaster, Boolean> groupsOfNotConfiguredMarketSegment = new HashedMap();
        List<AssumeMFN> allAssumeMFNs = groupFloorOverrideRepository.getAllAssumeMFNGroups();
        List<GroupBlockMaster> minGroupTobeConsidered = new ArrayList<>();

        groupMasterToGroupDetailsDTOMap.forEach((groupBlockMaster, dto) -> {
            if (isGroupOfNonConfiguredMarketSegment(groupBlockMaster, allConfiguredMarketSegments) && dto.isMFNGroup()) {
                groupsOfNotConfiguredMarketSegment.put(groupBlockMaster, Boolean.TRUE);
            } else if (isGroupOfNonConfiguredMarketSegment(groupBlockMaster, allConfiguredMarketSegments) && !dto.isMFNGroup()) {
                groupsToBeRemoved.add(groupBlockMaster);
            } else if (findGroupInListOf(allAssumeMFNs, groupBlockMaster.getId()).isPresent() && dto.isMFNGroup()) {
                minGroupTobeConsidered.add(groupBlockMaster);
            } else if (isAssumeMFNToggleSet().equals(dto.isMFNGroup())) {
                groupsToBeRemoved.add(groupBlockMaster);
            } else {
                groupsToBeAdded.add(groupBlockMaster);
            }
        });

        groupFloorOverrideRepository.addNotConfiguredMarketSegmentGroups(groupsOfNotConfiguredMarketSegment);
        groupFloorOverrideRepository.updateMinBlockGroup(minGroupTobeConsidered);
        groupFloorOverrideRepository.addMFNGroups(groupsToBeAdded);
        groupFloorOverrideRepository.removeMFNGroups(groupsToBeRemoved);
        syncEventAggregatorService.registerSyncEvent(SyncEvent.MFN_STATUS_CHANGED);
    }

    private boolean isGroupOfNonConfiguredMarketSegment(GroupBlockMaster groupBlockMaster, List<GroupFloorMarketSegment> configuredGroupFloorMarketSegments) {
        return configuredGroupFloorMarketSegments.stream()
                .filter(GroupFloorMarketSegment::isApplicableForGFO)
                .noneMatch(groupFloorMarketSegment -> groupFloorMarketSegment.getMarketSeg().getId().equals(groupBlockMaster.getMarketSegment().getId()));
    }

    public int configureGFOAccomClasses() {
        Set<Integer> alreadyConfiguredAccomClasses = groupFloorOverrideRepository.getConfigureGroupFloorAccomClasses()
                .stream().map(gfoac -> gfoac.getAccomClass().getId()).collect(Collectors.toSet());

        List<AccomClass> accomClasses = groupFloorOverrideRepository.getAllAccomClasses();

        Set<GroupFloorAccomClass> newGroupFloorAccomClasses = accomClasses.stream()
                .filter(ac -> !alreadyConfiguredAccomClasses.contains(ac.getId()))
                .map(ac -> {
                    GroupFloorAccomClass newGroupFloorAccomClass = new GroupFloorAccomClass();
                    newGroupFloorAccomClass.setAccomClass(ac);
                    newGroupFloorAccomClass.setApplicableForGFO(true);
                    return newGroupFloorAccomClass;
                })
                .collect(Collectors.toSet());
        groupFloorOverrideRepository.saveGroupFloorAccomClass(newGroupFloorAccomClasses);
        return newGroupFloorAccomClasses.size();
    }

    public int configureGFOMarketSegments() {
        Set<Integer> alreadyConfiguredMarketSegments = groupFloorOverrideRepository.getConfiguredGroupFloorMarketSegments()
                .stream().map(gfoac -> gfoac.getMarketSeg().getId()).collect(Collectors.toSet());

        List<MarketSegmentSummary> marketSegments = groupFloorOverrideRepository.getAllMarketSegments();

        Set<GroupFloorMarketSegment> newGroupFloorAccomClasses = marketSegments.stream()
                .filter(ms -> !alreadyConfiguredMarketSegments.contains(ms.getId()))
                .map(mktSeg -> {
                    GroupFloorMarketSegment newGroupFloorAccomClass = new GroupFloorMarketSegment();
                    newGroupFloorAccomClass.setMarketSeg(mktSeg);
                    newGroupFloorAccomClass.setApplicableForGFO(true);
                    return newGroupFloorAccomClass;
                })
                .collect(Collectors.toSet());
        groupFloorOverrideRepository.saveGroupFloorMarketSegments(newGroupFloorAccomClasses);
        return newGroupFloorAccomClasses.size();
    }

    public void removeGroupFloorOverrides(List<Integer> accomClassIds, LocalDate caughtUpDate) {
        groupFloorOverrideRepository.removeGroupFloorOverridesFor(accomClassIds, caughtUpDate);
    }

    public void deleteGroupMFNConfigTable() {
        groupFloorOverrideRepository.truncateGroupMFNConfigTable();
    }

    public void deleteGroupFloorAccomClassConfigTable() {
        groupFloorOverrideRepository.truncateGroupFloorAccomClassConfigTable();
    }

    public void deleteGroupFloorMktSegConfigTable() {
        groupFloorOverrideRepository.truncateGroupFloorMktSegConfigTable();
    }

    public void evaluateMinimumGroupBlocks(LocalDate startDate, LocalDate endDate) {
        Integer minBlockSize = configParamsService.getParameterValue(FeatureTogglesConfigParamName.GROUP_FLOOR_MIN_BLOCK_SIZE);
        List<GroupBlockDetail> allGroupBlocks = groupFloorOverrideRepository.getMinimumBlockGroupDetails(startDate, endDate, minBlockSize);
        Set<GroupBlockMaster> applicableGroups = allGroupBlocks.stream()
                .map(GroupBlockDetail::getGroupBlockMaster)
                .collect(Collectors.toSet());

        groupFloorOverrideRepository.addMinBlockGroup(applicableGroups);

    }
}