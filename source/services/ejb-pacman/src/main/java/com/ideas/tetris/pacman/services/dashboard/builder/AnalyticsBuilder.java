package com.ideas.tetris.pacman.services.dashboard.builder;

import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.bestavailablerate.BarDecisionService;
import com.ideas.tetris.pacman.services.businessview.entity.ClientBusinessGroup;
import com.ideas.tetris.pacman.services.businessview.entity.ClientBusinessView;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dashboard.MetricRequest;
import com.ideas.tetris.pacman.services.dashboard.MetricResponse;
import com.ideas.tetris.pacman.services.dashboard.dto.CapacityByOccupancyDayDto;
import com.ideas.tetris.pacman.services.dashboard.dto.ClientBusinessGroupMktSegCodeDto;
import com.ideas.tetris.pacman.services.dashboard.dto.OccupancyByGroupDto;
import com.ideas.tetris.pacman.services.dashboard.dto.OccupancyPercentByDateAndGroupDto;
import com.ideas.tetris.pacman.services.dashboard.dto.RevenueByMarketSegmentDto;
import com.ideas.tetris.pacman.services.dashboard.rowmapper.CapacityByOccupancyDayDtoRowMapper;
import com.ideas.tetris.pacman.services.dashboard.rowmapper.ClientBusinessGroupMktSegCodeDtoRowMapper;
import com.ideas.tetris.pacman.services.dashboard.rowmapper.OccupancyByBusinessGroupDtoRowMapper;
import com.ideas.tetris.pacman.services.dashboard.rowmapper.OccupancyPercentByArrivalDateAndBusinessGroupDtoRowMapper;
import com.ideas.tetris.pacman.services.dashboard.rowmapper.RevenueByMarketSegmentRowMapper;
import com.ideas.tetris.pacman.services.dashboard.type.GroupByType;
import com.ideas.tetris.pacman.services.dashboard.util.DateFiller;
import com.ideas.tetris.pacman.services.dashboard.util.DateSorter;
import com.ideas.tetris.pacman.services.demandoverride.entity.LastRoomValue;
import com.ideas.tetris.pacman.services.informationmanager.alert.AlertEvaluationException;
import com.ideas.tetris.pacman.services.inventorygroup.entity.InventoryGroup;
import com.ideas.tetris.pacman.services.overbooking.entity.OverbookingPropertyLevel;
import com.ideas.tetris.pacman.services.overbooking.rowmapper.OverbookingPropertyLevelRowMapper;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.multiproperty.aggregation.MultiPropertyAggregationService;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.PHYSICAL_PROPERTY_CODE;
import static java.util.Optional.ofNullable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class AnalyticsBuilder {
    private static final Logger LOGGER = Logger.getLogger(AnalyticsBuilder.class.getName());
    public static final String UNASSIGNED = "Unassigned";
    public static final String TETRIS_EXCEPTION = "TetrisException";
    public static final String PROPERTY_ID = "propertyId";
    public static final String INVENTORY_GROUP_ID = "inventoryGroupId";
    public static final String EXCLUDE_COMP_ROOMS = "excludeCompRooms";
    public static final String MKT_SEG_EXCLUDED_FLAG = "mktSegExcludedFlag";
    public static final String LOS_ID = "losId";
    public static final String PROPERTY_IDS = "propertyIds";
    public static final String CAUGHT_UP_DATE = "caughtUpDate";
    public static final String START_DATE = "startDate";
    public static final String END_DATE = "endDate";

    private static final String EXEC_USP_OCCUPANCY_PERCENT_BY_ARRIVAL_DATE_AND_PROPERTY_BUSINESS_VIEW_SINGLE_PROP = "exec dbo.usp_occupancy_percent_by_arrival_and_property_bv_single_prop :startDate,:endDate,:caughtUpDate,:excludeCompRooms";
    private static final String EXEC_OCCUPANCY_PERCENT_BY_ARRIVAL_DATE_AND_PROPERTY_BUSINESS_VIEW_SINGLE_PROP_BY_INVENTORY_GROUP = "exec dbo.usp_occupancy_percent_by_arrival_and_property_bv_single_prop_by_inventory_grp :startDate,:endDate,:caughtUpDate,:inventoryGroupId,:excludeCompRooms";
    private static final String EXEC_OCCUPANCY_BY_PROPERTY_BUSINESS_VIEW_SINGLE_PROP = "exec dbo.usp_occupancy_by_prop_bv_single_prop :startDate,:endDate,:caughtUpDate";
    private static final String EXEC_OCCUPANCY_BY_PROPERTY_BUSINESS_VIEW_SINGLE_PROP_BY_INVENTORY_GROUP = "exec dbo.usp_occupancy_by_prop_bv_single_prop_by_inventory_grp :startDate,:endDate,:caughtUpDate,:inventoryGroupId,:excludeCompRooms";
    private static final String EXEC_ROOMS_SOLD_BY_MARKET_SEGMENT = "exec dbo.usp_rooms_sold_by_market_segment :startDate,:endDate,:caughtUpDate,:excludeCompRooms";
    @Autowired
	private BarDecisionService barDecisionService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;

    @Autowired
	private PropertyGroupService propertyGroupService;

    @Autowired
	private AbstractMultiPropertyCrudService multiPropertyCrudService;

    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	private CrudService globalCrudService;

    @Autowired
	private PacmanConfigParamsService configParamsService;

    @Autowired
	private MultiPropertyAggregationService multiPropertyAggregationService;

    public PacmanConfigParamsService getConfigParamsService() {
        return configParamsService;
    }

    public void setConfigParamsService(PacmanConfigParamsService configParamsService) {
        this.configParamsService = configParamsService;
    }

    protected MultiPropertyAggregationService getMultiPropertyAggregationService() {
        return multiPropertyAggregationService;
    }

    public void setMultiPropertyAggregationService(MultiPropertyAggregationService multiPropertyAggregationService) {
        this.multiPropertyAggregationService = multiPropertyAggregationService;
    }

    public void setBarDecisionService(BarDecisionService barDecisionService) {
        this.barDecisionService = barDecisionService;
    }

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    public void setPropertyGroupService(PropertyGroupService propertyGroupService) {
        this.propertyGroupService = propertyGroupService;
    }

    public void setMultiPropertyCrudService(AbstractMultiPropertyCrudService multiPropertyCrudService) {
        this.multiPropertyCrudService = multiPropertyCrudService;
    }

    public void setGlobalCrudService(CrudService globalCrudService) {
        this.globalCrudService = globalCrudService;
    }

    public MetricResponse buildMetricForLRVByBaseRCOfInventoryGroup(Date startDate, Date endDate, Integer inventoryGroupId) {
        Map<Date, BigDecimal> decimalResults = null;

        try {
            int accomClassDriverId = getBaseRoomClassOfInventoryGroup(inventoryGroupId);
            decimalResults = barDecisionService.getLastRoomValue(accomClassDriverId, startDate, endDate);
        } catch (TetrisException te) {
            LOGGER.error(te);
        }

        List<BigDecimal> sortedFilledInValues = DateFiller.sortValuesByDateAndFillInEmptyData(startDate, endDate, decimalResults);
        MetricResponse metricResponse = new MetricResponse();
        metricResponse.addMetricValuesForGroupByType("INVENTORY_GROUP_BASE_RC", sortedFilledInValues);

        return metricResponse;
    }

    protected int getBaseRoomClassOfInventoryGroup(Integer inventoryGroupId) {
        InventoryGroup inventoryGroup = crudService.find(InventoryGroup.class, inventoryGroupId);
        return inventoryGroup.getBaseAccomClass().getId();
    }

    public MetricResponse buildHotelLRVMetric(Date startDate, Date endDate) {

        Map<Date, BigDecimal> decimalResults = null;

        try {
            int accomClassDriverId = getMasterAccomClassId();
            decimalResults = barDecisionService.getLastRoomValue(accomClassDriverId, startDate, endDate);
        } catch (TetrisException te) {
            LOGGER.error(te);
        }

        List<BigDecimal> sortedFilledInValues = DateFiller.sortValuesByDateAndFillInEmptyData(startDate, endDate, decimalResults);
        MetricResponse metricResponse = new MetricResponse();
        metricResponse.addMetricValuesForGroupByType("PROPERTY", sortedFilledInValues);

        return metricResponse;
    }

    public MetricResponse buildInventoryGroupBARMetric(Date startDate, Date endDate, Integer inventoryGroupId) {
        int accomClassDriverId = getBaseRoomClassOfInventoryGroup(inventoryGroupId);
        Map<Date, String> type1Results = buildHotelBARResultsMap(startDate, endDate, accomClassDriverId);

        List<String> sortedValues = DateSorter.sortStringValuesByDate(startDate, endDate, type1Results);
        MetricResponse metricResponse = new MetricResponse();
        metricResponse.addMetricValuesForGroupByType("INVENTORY_GROUP_BASE_RC", sortedValues);

        return metricResponse;
    }

    public MetricResponse buildHotelBARMetric(Date startDate, Date endDate) {
        Integer accomClassDriverId = getMasterAccomClassId();
        Map<Date, String> type1Results = buildHotelBARResultsMap(startDate, endDate, accomClassDriverId);

        List<String> sortedValues = DateSorter.sortStringValuesByDate(startDate, endDate, type1Results);
        MetricResponse metricResponse = new MetricResponse();
        metricResponse.addMetricValuesForGroupByType("PROPERTY", sortedValues);

        return metricResponse;
    }


    protected static final String BAR_PRICE_BY_ARRIVAL_DATE_FOR_SINGLE_DAY = "select  dbo.Arrival_DT, dbo.Accom_Class_ID, ac.Accom_Class_Name, rq.Rate_Code_Name, dr.Decision_Reason_Type_ID, "
            + "dr.Decision_Reason_Name, ac.View_Order ,at.Accom_Type_Name,"
            + "	Rate=     case (DATENAME(dw,Arrival_Dt))"
            + "                        when 'Monday' then rqd.Monday"
            + "                        when 'Tuesday' then rqd.Tuesday"
            + "                        when 'Wednesday' then rqd.Wednesday"
            + "                        when 'Thursday' then rqd.Thursday"
            + "                        when 'Friday' then rqd.Friday"
            + "                        when 'Saturday' then rqd.Saturday"
            + "                        when 'Sunday' then rqd.Sunday"
            + "                  end"
            + " from Decision_Reason_Type dr,  Decision_Bar_Output as dbo "
            + " left join Rate_Unqualified rq on dbo.Rate_Unqualified_ID = rq.Rate_Unqualified_ID "
            + " left join Accom_Class ac on ac.Accom_Class_ID = dbo.Accom_Class_ID "
            + "	left join Accom_Type at on dbo.Accom_Class_ID = at.Accom_Class_ID "
            + " left join Rate_Unqualified_Details rqd on at.Accom_Type_ID = rqd.Accom_Type_ID and dbo.Rate_Unqualified_ID = rqd.Rate_Unqualified_ID"
            + " where Arrival_DT >= :startDate and Arrival_DT <= :endDate "
            + " and dbo.LOS = :losId "
            + " and ac.Accom_Class_ID = :accomClassId "
            + " and dr.Decision_Reason_Type_ID = dbo.Decision_Reason_Type_ID "
            + " and :startDate between rqd.Start_Date_DT and rqd.End_Date_DT "
            + " order by ac.View_Order ";

    private static final String BAR_PRICE_BY_ACCOM_CLASS_ARRIVAL_DATE = "select  dbo.Arrival_DT, dbo.Accom_Class_ID, ac.Accom_Class_Name, rq.Rate_Code_Name, dr.Decision_Reason_Type_ID, dr.Decision_Reason_Name, ac.View_Order"
            + " from Decision_Reason_Type dr,  Decision_Bar_Output as dbo "
            + " left join Rate_Unqualified rq on dbo.Rate_Unqualified_ID = rq.Rate_Unqualified_ID "
            + " left join Accom_Class ac on ac.Accom_Class_ID = dbo.Accom_Class_ID "
            + " where Arrival_DT >= :startDate and Arrival_DT <= :endDate "
            + " and dbo.LOS = :losId "
            + " and ac.Accom_Class_ID = :accomClassId "
            + " and dr.Decision_Reason_Type_ID = dbo.Decision_Reason_Type_ID "
            + " order by ac.View_Order ";

    private static final String BAR_CONTINUOUS_PRICE_BY_ACCOM_CLASS_ARRIVAL_DATE = "select  dbo.Arrival_DT, ac.Accom_Class_ID, ac.Accom_Class_Name, dbo.Final_BAR, dr.Decision_Reason_Type_ID, dr.Decision_Reason_Name, ac.View_Order"
            + " from Decision_Reason_Type dr,  CP_Decision_Bar_Output as dbo "
            + " left join Accom_Type at on at.Accom_Type_ID = dbo.Accom_Type_ID "
            + " left join Accom_Class ac on ac.Accom_Class_ID = at.Accom_Class_ID "
            + " left join CP_Cfg_AC cfg on cfg.Accom_Class_ID = ac.Accom_Class_ID "
            + " inner join Product p on dbo.Product_ID = p.Product_ID "
            + " where Arrival_DT >= :startDate and Arrival_DT <= :endDate "
            + " and dbo.LOS = :losId "
            + " and p.System_Default = 1 "
            + " and ac.Accom_Class_ID = :accomClassId "
            + " and dr.Decision_Reason_Type_ID = dbo.Decision_Reason_Type_ID "
            + " and cfg.Accom_Type_ID = at.Accom_Type_ID "
            + " order by ac.View_Order ";

    private static final String GET_TOTAL_CAPACITY_PER_OCCUPANCY_DATE = new StringBuilder()
            .append("SELECT Occupancy_DT, sum(Total_Accom_Capacity - (Rooms_Not_Avail_Maint + Rooms_Not_Avail_Other) ) as AvailableCapacity ,sum(total_accom_capacity) as physicalCapacity  ")
            .append("FROM Total_Activity  ")
            .append("WHERE occupancy_DT BETWEEN :startDate AND :endDate  ")
            .append("	AND Property_ID in (:propertyIds)  ")
            .append("GROUP BY Occupancy_DT").toString();

    public Map<Date, BigDecimal> buildHotelMasterRoomClassBARs(final Date startDate, final Date endDate, final Integer propertyId) {
        return getHotelMasterRoomClassBARs(startDate, endDate, getMasterAccomClassId(propertyId));
    }

    public Map<Date, BigDecimal> buildHotelMasterRoomClassBARsForPhysicalProperty(final Date startDate, final Date endDate, final String physicalPropertyCode) {
        Integer baseAccomClassId = getBaseAccomClassForPhysicalProperty(physicalPropertyCode);
        return getHotelMasterRoomClassBARs(startDate, endDate, baseAccomClassId);
    }

    @SuppressWarnings("unchecked")
    private Map<Date, BigDecimal> getHotelMasterRoomClassBARs(final Date startDate, final Date endDate,
                                                              final Integer accomClassId) {

        final Query q = crudService.getEntityManager().createNativeQuery(BAR_CONTINUOUS_PRICE_BY_ACCOM_CLASS_ARRIVAL_DATE);
        q.setParameter("accomClassId", accomClassId);
        q.setParameter(START_DATE, startDate);
        q.setParameter(END_DATE, endDate);
        q.setParameter(LOS_ID, barDecisionService.getDefaultLOS());

        final List<Object[]> masterRoomClassBARs = q.getResultList();
        return ofNullable(masterRoomClassBARs)
                .orElse(List.of())
                .stream()
                .collect(Collectors.toMap(
                        row -> (Date) row[0],
                        row -> ofNullable(row[3])
                                .map(BigDecimal.class::cast)
                                .map(val -> val.setScale(2, RoundingMode.HALF_UP))
                                .orElse(BigDecimal.ZERO)));
    }

    @SuppressWarnings("unchecked")
    private Map<Date, String> buildHotelBARResultsMap(Date startDate, Date endDate, Integer accomClassDriverId) {
        Map<Date, String> results = new HashMap<Date, String>();

        try {
            if (-1 == accomClassDriverId) {
                throw new TetrisException(ErrorCode.UNEXPECTED_ERROR,
                        "No accommodation classes for property id: " + PacmanWorkContextHelper.getPropertyId() + " code: " + PacmanWorkContextHelper.getPropertyCode());
            }
            boolean isForSingleDay = startDate.equals(endDate) ? true : false;
            boolean isContinuousPricingEnabled = configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value());
            String query = null;
            if (isForSingleDay) {
                query = isContinuousPricingEnabled ? BAR_CONTINUOUS_PRICE_BY_ACCOM_CLASS_ARRIVAL_DATE : BAR_PRICE_BY_ARRIVAL_DATE_FOR_SINGLE_DAY;
            } else {
                query = isContinuousPricingEnabled ? BAR_CONTINUOUS_PRICE_BY_ACCOM_CLASS_ARRIVAL_DATE : BAR_PRICE_BY_ACCOM_CLASS_ARRIVAL_DATE;
            }
            Query q = crudService.getEntityManager().createNativeQuery(query);
            q.setParameter("accomClassId", accomClassDriverId);
            q.setParameter(START_DATE, startDate);
            q.setParameter(END_DATE, endDate);
            q.setParameter(LOS_ID, barDecisionService.getDefaultLOS());

            List<Object[]> resultList = q.getResultList();
            List<Object[]> newResultList = new ArrayList<>();
            if (!isContinuousPricingEnabled && isForSingleDay) {
                String prevAccomClass = "";
                BigDecimal minAmount = BigDecimal.ZERO; // just to make sure this is greater than 0
                Object[] lastRow = null;
                List<Object> newRow = null;
                for (Object[] row : resultList) {
                    if (!prevAccomClass.equalsIgnoreCase((String) row[2])) {
                        if (!"".equals(prevAccomClass)) {
                            newResultList.add(lastRow);
                            minAmount = BigDecimal.ZERO;
                        }
                        newRow = new ArrayList<Object>();
                        newRow.addAll(Arrays.asList(row).subList(0, 8));
                    }

                    if (minAmount.compareTo(BigDecimal.ZERO) == 0) {
                        minAmount = (BigDecimal) row[8];
                    } else {
                        minAmount = minAmount.min((BigDecimal) row[8]);
                    }

                    newRow.remove(7);
                    newRow.add(7, minAmount);
                    newRow.add(newRow.size(), row[7]);
                    newRow.add(newRow.size(), row[8]);

                    prevAccomClass = (String) row[2];

                    lastRow = newRow.toArray();
                }
                if (null != lastRow) {
                    newResultList.add(lastRow);
                }
            } else { // when isContinuousPricingEnabled = true ... doing no changes in this case..
                newResultList = resultList;
            }


            for (Object[] row : newResultList) {

                Object barValue = null == row[3] ? BigDecimal.ZERO : row[3];
                String price = isContinuousPricingEnabled ? ((BigDecimal) barValue).setScale(2).toString() : (barValue instanceof BigDecimal ? ((BigDecimal) barValue).setScale(2).toString() : (String) barValue);

                String responseString = price + "&&" + row[4].toString() + "&&" + row[5];

                if (!isContinuousPricingEnabled && isForSingleDay) {
                    for (int i = 7; i < row.length; i++) {
                        if (row[i] instanceof BigDecimal) {
                            responseString = responseString + "&&" + ((BigDecimal) row[i]).setScale(2, RoundingMode.HALF_UP).toString();
                        } else {
                            responseString = responseString + "&&" + row[i].toString();
                        }
                    }
                }

                results.put((Date) row[0], responseString);
            }

        } catch (TetrisException te) {
            // ignore and return empty integer results if no accom classes
            LOGGER.error(te);
        }
        results = DateFiller.fillInEmptyStringDataWithNulls(startDate, endDate, results);

        return results;
    }

    public MetricResponse buildHotelOverbookingMetric(Date startDate, Date endDate, Date caughtUpDate, MetricRequest metricRequest) {
        MetricResponse metricResponse = new MetricResponse();
        List<Integer> overBookingValues = new ArrayList<Integer>();
        List<OverbookingPropertyLevel> overbookingRecords;
        overbookingRecords = crudService.findByNativeQuery(OverbookingPropertyLevel.BY_PROPERTYID_AND_OCCUPANCYDATE_RANGE_AND_COMP_ROOM_EXCLUSION, QueryParameter
                .with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and(CAUGHT_UP_DATE, caughtUpDate).and(START_DATE, startDate)
                .and(END_DATE, endDate).and(MKT_SEG_EXCLUDED_FLAG, metricRequest.isExcludeCompRooms() ? "0" : "0,1")
                .parameters(), new OverbookingPropertyLevelRowMapper());
        Date currentDate = startDate;
        int rowIndex = 0;
        OverbookingPropertyLevel currentRow;

        while (currentDate.compareTo(endDate) <= 0) {
            if (overbookingRecords != null && rowIndex < overbookingRecords.size()) {
                currentRow = overbookingRecords.get(rowIndex);
                Date currentRowsDate = currentRow.getOccupancyDate();

                if (currentRowsDate.compareTo(currentDate) == 0) {
                    overBookingValues.add(overbookingRecords.get(rowIndex).getOverbookingDecision());
                    rowIndex++;
                } else {
                    // for missing dates, add Integer.MIN_VALUE
                    overBookingValues.add(DateFiller.NO_VALUE);
                }
            } else {
                // for missing dates, add Integer.MIN_VALUE
                overBookingValues.add(DateFiller.NO_VALUE);
            }

            currentDate = DateUtil.addDaysToDate(currentDate, 1);
        }

        metricResponse.addMetricValuesForGroupByType(metricRequest.getGroupByType().name(), overBookingValues);

        return metricResponse;
    }

    private static final String LRV_BY_ARRIVAL_DATE_AND_AC = "SELECT lrv.occupancy_DT, ac.accom_class_name, lrv.lrv "
            + "FROM Decision_LRV lrv " + "INNER JOIN Accom_Class ac On ac.Accom_Class_ID = lrv.Accom_Class_ID "
            + "WHERE lrv.occupancy_DT >= :startDate AND lrv.occupancy_DT <= :endDate " + "AND lrv.Property_ID = :propertyId "
            + "ORDER BY ac.view_order, ac.accom_class_name, lrv.occupancy_dt ";

    @SuppressWarnings({"unchecked", "rawtypes"})
    public MetricResponse buildLRVByACMetric(Date startDate, Date endDate) {
        MetricResponse response = new MetricResponse();

        try {
            Query q = crudService.getEntityManager().createNativeQuery(LRV_BY_ARRIVAL_DATE_AND_AC);
            q.setParameter(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
            q.setParameter(START_DATE, startDate);
            q.setParameter(END_DATE, endDate);

            List<Object[]> resultList = q.getResultList();

            createValuesForAccomClass(response, resultList);
        } catch (TetrisException te) {
            LOGGER.error(te);
        }
        return response;
    }

    public MetricResponse buildLRVByRCMetricForInventoryGroup(Date startDate, Date endDate, Integer inventoryGroupId) {
        MetricResponse response = new MetricResponse();

        try {
            List<Object[]> resultList = crudService.findByNamedQuery(LastRoomValue.LRV_BY_INVENTORY_GROUP,
                    QueryParameter.with(START_DATE, startDate)
                            .and(END_DATE, endDate).and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                            .and(INVENTORY_GROUP_ID, inventoryGroupId).parameters());

            createValuesForAccomClass(response, resultList);
        } catch (TetrisException te) {
            LOGGER.error(te);
        }
        return response;
    }

    private void createValuesForAccomClass(MetricResponse response, List<Object[]> resultList) {
        List<BigDecimal> valuesForAccomClass = new ArrayList<BigDecimal>();
        String currentAccomClass = "";
        for (Object[] row : resultList) {

            if ("".equals(currentAccomClass)) {
                // first record
                currentAccomClass = (String) row[1];
                valuesForAccomClass.add((BigDecimal) row[2]);
            } else if (!currentAccomClass.equals(row[1])) {
                // changing accom classes
                response.addMetricValuesForGroupByType(currentAccomClass, valuesForAccomClass);
                valuesForAccomClass = new ArrayList();
                valuesForAccomClass.add((BigDecimal) row[2]);
                currentAccomClass = (String) row[1];
            } else {
                valuesForAccomClass.add((BigDecimal) row[2]);
            }
        }
        // add the last accom class
        response.addMetricValuesForGroupByType(currentAccomClass, valuesForAccomClass);
    }

    private static final String UNQUALIFIED_PRICE_BY_TYPE_AND_WHATEVER = " SELECT rud.Sunday, rud.Monday, rud.Tuesday, rud.Wednesday, rud.Thursday, rud.Friday, rud.Saturday "
            + " FROM Rate_Unqualified ru, "
            + " Rate_Unqualified_Details rud, "
            + " Accom_Type a"
            + " where rud.Accom_Type_ID = a.Accom_Type_ID "
            + " and rud.Rate_Unqualified_ID = ru.Rate_Unqualified_ID "
            + " and ru.Property_ID = 		:propertyId	"
            + " and ru.Rate_Code_Name = 	:rateCode "
            + " and a.Accom_Type_Code = 	:typeCode "
            + " and rud.Start_Date_DT <= 	:occupancyDate " + " and rud.End_Date_DT >=   	:occupancyDate ";

    public BigDecimal getRate(Date occupancyDate, String rateCode, String accomodationTypeCode) throws AlertEvaluationException {
        try {
            Query q = crudService.getEntityManager().createNativeQuery(UNQUALIFIED_PRICE_BY_TYPE_AND_WHATEVER);
            q.setParameter(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
            q.setParameter("rateCode", rateCode);
            q.setParameter("typeCode", accomodationTypeCode);
            q.setParameter("occupancyDate", occupancyDate);

            Calendar cal = GregorianCalendar.getInstance();
            cal.setTime(occupancyDate);
            int dayIndex = cal.get(Calendar.DAY_OF_WEEK) - 1; // ( SUNDAY == 1 )

            Object[] result = (Object[]) q.getSingleResult();
            BigDecimal rateForDay = (BigDecimal) result[dayIndex];

            return rateForDay;

        } catch (Exception e) {
            throw new AlertEvaluationException("room rate queryfailed", e);
        }
    }

    private static final String BAR_PRICE_BY_ARRIVAL_DATE = "select  dbo.Arrival_DT, dbo.Accom_Class_ID, ac.Accom_Class_Name, rq.Rate_Code_Name, dr.Decision_Reason_Type_ID, "
            + "dr.Decision_Reason_Name, ac.View_Order "
            + " from Decision_Reason_Type dr,  Decision_Bar_Output as dbo "
            + " left join Rate_Unqualified rq on dbo.Rate_Unqualified_ID = rq.Rate_Unqualified_ID "
            + " left join Accom_Class ac on ac.Accom_Class_ID = dbo.Accom_Class_ID "
            + " where Arrival_DT >= :startDate and Arrival_DT <= :endDate "
            + " and dbo.Property_ID = :propertyId "
            + " and dbo.LOS = :losId "
            + " and dr.Decision_Reason_Type_ID = dbo.Decision_Reason_Type_ID "
            + " order by ac.View_Order";

    private static final StringBuilder BAR_PRICE_BY_ARRIVAL_DATE_FOR_INVENTORY_GROUP = new StringBuilder()
            .append("select  dbo.Arrival_DT, dbo.Accom_Class_ID, ac.Accom_Class_Name, rq.Rate_Code_Name, dr.Decision_Reason_Type_ID, ")
            .append("dr.Decision_Reason_Name, ac.View_Order ")
            .append(" from Decision_Reason_Type dr,  Decision_Bar_Output as dbo ")
            .append(" left join Rate_Unqualified rq on dbo.Rate_Unqualified_ID = rq.Rate_Unqualified_ID ")
            .append(" left join Accom_Class ac on ac.Accom_Class_ID = dbo.Accom_Class_ID ")
            .append(" inner join Inventory_Group_Details as igd on igd.Accom_Class_ID = ac.Accom_Class_ID and igd.Inventory_Group_ID = :inventoryGroupId")
            .append(" where Arrival_DT >= :startDate and Arrival_DT <= :endDate ")
            .append(" and dbo.Property_ID = :propertyId ")
            .append(" and dbo.LOS = :losId ")
            .append(" and dr.Decision_Reason_Type_ID = dbo.Decision_Reason_Type_ID ")
            .append(" order by ac.View_Order");

    protected static final String BAR_PRICE_BY_ARRIVAL_DATE_FOR_DECISION_POPUP = "select  dbo.Arrival_DT, dbo.Accom_Class_ID, ac.Accom_Class_Name, rq.Rate_Code_Name, dr.Decision_Reason_Type_ID, "
            + "dr.Decision_Reason_Name, ac.View_Order ,at.Accom_Type_Name,"
            + "	Rate=     case (DATENAME(dw,Arrival_Dt))"
            + "                        when 'Monday' then rqd.Monday"
            + "                        when 'Tuesday' then rqd.Tuesday"
            + "                        when 'Wednesday' then rqd.Wednesday"
            + "                        when 'Thursday' then rqd.Thursday"
            + "                        when 'Friday' then rqd.Friday"
            + "                        when 'Saturday' then rqd.Saturday"
            + "                        when 'Sunday' then rqd.Sunday"
            + "                  end"
            + " from Decision_Reason_Type dr,  Decision_Bar_Output as dbo "
            + " left join Rate_Unqualified rq on dbo.Rate_Unqualified_ID = rq.Rate_Unqualified_ID "
            + " left join Accom_Class ac on ac.Accom_Class_ID = dbo.Accom_Class_ID "
            + "	left join Accom_Type at on dbo.Accom_Class_ID = at.Accom_Class_ID "
            + "left join Rate_Unqualified_Details rqd on at.Accom_Type_ID = rqd.Accom_Type_ID and dbo.Rate_Unqualified_ID = rqd.Rate_Unqualified_ID"
            + " where Arrival_DT >= :startDate and Arrival_DT <= :endDate "
            + " and dbo.Property_ID = :propertyId "
            + " and dbo.LOS = :losId "
            + " and dr.Decision_Reason_Type_ID = dbo.Decision_Reason_Type_ID "
            + " and :startDate between rqd.Start_Date_DT and rqd.End_Date_DT "
            + " order by ac.View_Order";

    protected static final StringBuilder BAR_PRICE_BY_ARRIVAL_DATE_FOR_DECISION_POPUP_FOR_INVENTORY_GROUP = new StringBuilder()
            .append("select  dbo.Arrival_DT, dbo.Accom_Class_ID, ac.Accom_Class_Name, rq.Rate_Code_Name, dr.Decision_Reason_Type_ID, ")
            .append("dr.Decision_Reason_Name, ac.View_Order ,at.Accom_Type_Name,")
            .append("	Rate = case (DATENAME(dw,Arrival_Dt))")
            .append("                when 'Monday' then rqd.Monday")
            .append("                when 'Tuesday' then rqd.Tuesday")
            .append("                when 'Wednesday' then rqd.Wednesday")
            .append("                when 'Thursday' then rqd.Thursday")
            .append("                when 'Friday' then rqd.Friday")
            .append("                when 'Saturday' then rqd.Saturday")
            .append("                when 'Sunday' then rqd.Sunday")
            .append("           end")
            .append(" from Decision_Reason_Type dr,  Decision_Bar_Output as dbo ")
            .append(" left join Rate_Unqualified rq on dbo.Rate_Unqualified_ID = rq.Rate_Unqualified_ID ")
            .append(" left join Accom_Class ac on ac.Accom_Class_ID = dbo.Accom_Class_ID ")
            .append(" inner join Inventory_Group_Details as igd on igd.Accom_Class_ID = ac.Accom_Class_ID and igd.Inventory_Group_ID = :inventoryGroupId")
            .append(" left join Accom_Type at on dbo.Accom_Class_ID = at.Accom_Class_ID ")
            .append(" left join Rate_Unqualified_Details rqd on at.Accom_Type_ID = rqd.Accom_Type_ID and dbo.Rate_Unqualified_ID = rqd.Rate_Unqualified_ID")
            .append(" where Arrival_DT >= :startDate and Arrival_DT <= :endDate ")
            .append(" and dbo.Property_ID = :propertyId ")
            .append(" and dbo.LOS = :losId ")
            .append(" and dr.Decision_Reason_Type_ID = dbo.Decision_Reason_Type_ID ")
            .append(" and :startDate between rqd.Start_Date_DT and rqd.End_Date_DT ")
            .append(" order by ac.View_Order");

    protected static final String BAR_CONTINUOUS_PRICE_BY_ARRIVAL_DATE = "select  dbo.Arrival_DT, ac.Accom_Class_ID, ac.Accom_Class_Name, dbo.Final_BAR, dr.Decision_Reason_Type_ID, dr.Decision_Reason_Name, ac.View_Order "
            + " from Decision_Reason_Type dr,  CP_Decision_Bar_Output as dbo "
            + " left join Accom_Type at on at.Accom_Type_ID = dbo.Accom_Type_ID "
            + " left join Accom_Class ac on ac.Accom_Class_ID = at.Accom_Class_ID "
            + " left join CP_Cfg_AC cfg on cfg.Accom_Class_ID = ac.Accom_Class_ID "
            + " inner join Product p on dbo.Product_ID = p.Product_ID "
            + " where Arrival_DT >= :startDate and Arrival_DT <= :endDate "
            + " and dbo.Property_ID = :propertyId "
            + " and dbo.LOS = :losId "
            + " and p.System_Default = 1 "
            + " and dr.Decision_Reason_Type_ID = dbo.Decision_Reason_Type_ID "
            + " and cfg.Accom_Type_ID = at.Accom_Type_ID "
            + " order by ac.View_Order";

    protected static final StringBuilder BAR_CONTINUOUS_PRICE_BY_ARRIVAL_DATE_FOR_INVENTORY_GROUP = new StringBuilder()
            .append("select  dbo.Arrival_DT, ac.Accom_Class_ID, ac.Accom_Class_Name, dbo.Final_BAR, dr.Decision_Reason_Type_ID, dr.Decision_Reason_Name, ac.View_Order ")
            .append(" from Decision_Reason_Type dr,  CP_Decision_Bar_Output as dbo ")
            .append(" left join Accom_Type at on at.Accom_Type_ID = dbo.Accom_Type_ID ")
            .append(" left join Accom_Class ac on ac.Accom_Class_ID = at.Accom_Class_ID ")
            .append(" inner join Inventory_Group_Details as igd on igd.Accom_Class_ID = ac.Accom_Class_ID and igd.Inventory_Group_ID = :inventoryGroupId")
            .append(" left join CP_Cfg_AC cfg on cfg.Accom_Class_ID = ac.Accom_Class_ID ")
            .append(" inner join Product p on dbo.Product_ID = p.Product_ID ")
            .append(" where Arrival_DT >= :startDate and Arrival_DT <= :endDate ")
            .append(" and dbo.Property_ID = :propertyId ")
            .append(" and dbo.LOS = :losId ")
            .append(" and p.System_Default = 1 ")
            .append(" and dr.Decision_Reason_Type_ID = dbo.Decision_Reason_Type_ID ")
            .append(" and cfg.Accom_Type_ID = at.Accom_Type_ID ")
            .append(" order by ac.View_Order");

    @SuppressWarnings({"unchecked", "rawtypes"})
    public MetricResponse buildBARByACResultsMetric(Date startDate, Date endDate, Integer inventoryGroupId) {
        MetricResponse response = new MetricResponse();

        try {
            boolean isForSingleDay = startDate.equals(endDate) ? true : false;
            boolean isContinuousPricingEnabled = configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value());
            List<Object[]> resultList;
            if (-1 == inventoryGroupId) {
                resultList = executeQueryForBuildByRC(startDate, endDate, isForSingleDay, isContinuousPricingEnabled);
            } else {
                resultList = executeQueryForBuildByRCForInventoryGroup(startDate, endDate, isForSingleDay, isContinuousPricingEnabled, inventoryGroupId);
            }
            List<Object[]> newResultList = new ArrayList<>();
            if (!isContinuousPricingEnabled && isForSingleDay) {
                String prevAccomClass = "";
                BigDecimal minAmount = BigDecimal.ZERO; // just to make sure this is greater than 0
                Object[] lastRow = null;
                List<Object> newRow = null;
                for (Object[] row : resultList) {
                    if (!prevAccomClass.equalsIgnoreCase((String) row[2])) {
                        if (!"".equals(prevAccomClass)) {
                            newResultList.add(lastRow);
                            minAmount = BigDecimal.ZERO;
                        }
                        newRow = new ArrayList<Object>();
                        newRow.addAll(Arrays.asList(row).subList(0, 8));
                    }

                    if (minAmount.compareTo(BigDecimal.ZERO) == 0) {
                        minAmount = (BigDecimal) row[8];
                    } else {
                        minAmount = minAmount.min((BigDecimal) row[8]);
                    }

                    newRow.remove(7);
                    newRow.add(7, minAmount);
                    newRow.add(newRow.size(), row[7]);
                    newRow.add(newRow.size(), row[8]);

                    prevAccomClass = (String) row[2];

                    lastRow = newRow.toArray();
                }
                if (null != lastRow) {
                    newResultList.add(lastRow);
                }
            } else { // when isContinuousPricingEnabled = true ... doing no changes in this case..
                newResultList = resultList;
            }

            List valuesForAccomClass = new ArrayList();
            String currentAccomClass = "";
            for (Object[] row : newResultList) {
                String price = (null != row[3] && isContinuousPricingEnabled) ? ((BigDecimal) row[3]).setScale(2, BigDecimal.ROUND_HALF_UP).toString() : (String) row[3];

                if ("".equals(currentAccomClass)) {
                    // first record
                    currentAccomClass = (String) row[2];
                    valuesForAccomClass.add(price);
                    valuesForAccomClass.add(row[4]);
                    valuesForAccomClass.add(row[5]);
                } else if (!currentAccomClass.equals(row[2])) {
                    // changing accom classes
                    response.addMetricValuesForGroupByType(currentAccomClass, valuesForAccomClass);
                    valuesForAccomClass = new ArrayList();
                    valuesForAccomClass.add(price);
                    valuesForAccomClass.add(row[4]);
                    valuesForAccomClass.add(row[5]);
                    currentAccomClass = (String) row[2];
                } else {
                    valuesForAccomClass.add(price);
                }
                if (!isContinuousPricingEnabled && isForSingleDay) {
                    for (int i = 7; i < row.length; i++) {
                        if (row[i] instanceof BigDecimal) {
                            valuesForAccomClass.add(((BigDecimal) row[i]).setScale(2, RoundingMode.HALF_UP).toString());
                        } else {
                            valuesForAccomClass.add(row[i].toString());
                        }
                    }
                }
            }
            // add the last accom class
            response.addMetricValuesForGroupByType(currentAccomClass, valuesForAccomClass);
        } catch (TetrisException te) {
            LOGGER.error(te);
        }
        return response;
    }

    private List<Object[]> executeQueryForBuildByRCForInventoryGroup(Date startDate, Date endDate, boolean isForSingleDay, boolean isContinuousPricingEnabled, Integer inventoryGroupId) {
        String query = null;
        if (isForSingleDay) {
            query = isContinuousPricingEnabled ? BAR_CONTINUOUS_PRICE_BY_ARRIVAL_DATE_FOR_INVENTORY_GROUP.toString() : BAR_PRICE_BY_ARRIVAL_DATE_FOR_DECISION_POPUP_FOR_INVENTORY_GROUP.toString();
        } else {
            query = isContinuousPricingEnabled ? BAR_CONTINUOUS_PRICE_BY_ARRIVAL_DATE_FOR_INVENTORY_GROUP.toString() : BAR_PRICE_BY_ARRIVAL_DATE_FOR_INVENTORY_GROUP.toString();
        }

        int defaultLengthOfStay = barDecisionService.getDefaultLOS();
        return crudService.findByNativeQuery(query,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and(START_DATE, startDate)
                        .and(END_DATE, endDate)
                        .and(LOS_ID, defaultLengthOfStay)
                        .and(INVENTORY_GROUP_ID, inventoryGroupId).parameters());
    }

    private List<Object[]> executeQueryForBuildByRC(Date startDate, Date endDate, boolean isForSingleDay, boolean isContinuousPricingEnabled) {
        String query = null;
        if (isForSingleDay) {
            query = isContinuousPricingEnabled ? BAR_CONTINUOUS_PRICE_BY_ARRIVAL_DATE : BAR_PRICE_BY_ARRIVAL_DATE_FOR_DECISION_POPUP;
        } else {
            query = isContinuousPricingEnabled ? BAR_CONTINUOUS_PRICE_BY_ARRIVAL_DATE : BAR_PRICE_BY_ARRIVAL_DATE;
        }
        int defaultLengthOfStay = barDecisionService.getDefaultLOS();
        return crudService.findByNativeQuery(query,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and(START_DATE, startDate)
                        .and(END_DATE, endDate)
                        .and(LOS_ID, defaultLengthOfStay).parameters());
    }

    private static final String OCCUPANCY_PERCENT_BY_ARRIVAL_DATE_AND_FG_MULTI_PROP = "SELECT fgdata.Occupancy_DT, Forecast_Group_Code, AvailableCapacity, occupancy_nbr,physicalCapacity "
            + " FROM "
            + " ( "
            + "	SELECT Occupancy_DT,sum(Total_Accom_Capacity - (Rooms_Not_Avail_Maint + Rooms_Not_Avail_Other) ) as AvailableCapacity,sum(total_accom_capacity) as physicalCapacity"
            + "	 FROM Total_Activity "
            + "    WHERE occupancy_DT BETWEEN :startDate AND :endDate "
            + "     AND Property_ID in (:propertyIds) "
            + "    GROUP BY Occupancy_DT "
            + " ) as cap INNER JOIN "
            + " ( "
            + "   SELECT occ.Occupancy_DT, fg.Forecast_Group_Code, SUM(occ.Occupancy_NBR) as occupancy_nbr "
            + "    FROM FN_AT_MS_Occupancy_Exclude_Comp_MS(:caughtUpDate, 0, :excludeCompRooms) as occ "
            + "     INNER JOIN Forecast_Group fg on occ.forecast_group_id = fg.forecast_group_id "
            + " inner join Accom_Type at on occ.Accom_Type_ID = at.Accom_Type_ID and occ.Property_ID = at.Property_ID "
            + " and at.isComponentRoom = 'N' "
            + "    WHERE occ.occupancy_DT BETWEEN :startDate AND :endDate "
            + "     AND occ.Property_ID in (:propertyIds) "
            + "    GROUP BY occ.Occupancy_DT, fg.Forecast_Group_Code "
            + ") as fgdata "
            + " ON cap.Occupancy_DT=fgdata.Occupancy_DT "
            + " ORDER BY Forecast_Group_Code, Occupancy_DT ";

    @SuppressWarnings({"unchecked", "rawtypes"})
    public MetricResponse buildOccupancyPercentByFGMetric(Date startDate, Date endDate, Date caughtUpDate, MetricRequest metricRequest) {
        MetricResponse response = new MetricResponse();

        try {
            List<OccupancyPercentByDateAndGroupDto> resultList = getByOccupancyPercentByFGMetric(startDate, endDate, caughtUpDate, metricRequest.isExcludeCompRooms());

            List<BigDecimal> valuesForFG = new ArrayList<BigDecimal>();
            String currentForecastGroup = "";
            boolean enablePhysicalCapacityConsideration = configParamsService.isEnablePhysicalCapacityConsideration();

            for (OccupancyPercentByDateAndGroupDto dto : resultList) {

                if ("".equals(currentForecastGroup)) {
                    // first record
                    currentForecastGroup = dto.getGroupName();
                    setOccupancyPercent(valuesForFG, dto, enablePhysicalCapacityConsideration);
                } else if (!currentForecastGroup.equals(dto.getGroupName())) {
                    // changing accom classes
                    response.addMetricValuesForGroupByType(currentForecastGroup, valuesForFG);
                    valuesForFG = new ArrayList();
                    setOccupancyPercent(valuesForFG, dto, enablePhysicalCapacityConsideration);
                    currentForecastGroup = dto.getGroupName();
                } else {
                    setOccupancyPercent(valuesForFG, dto, enablePhysicalCapacityConsideration);
                }
            }
            // add the last accom class
            response.addMetricValuesForGroupByType(currentForecastGroup, valuesForFG);
        } catch (TetrisException te) {
            LOGGER.error(te);
        }
        return response;
    }

    private void setOccupancyPercent(List<BigDecimal> valuesForFG, OccupancyPercentByDateAndGroupDto dto, boolean isEnablePhysicalCapacityConsideration) {
        if (isEnablePhysicalCapacityConsideration) {
            BigDecimal occupancyForecastPerc = null;
            if (dto.getOccupancyNumber() == null || dto.getPhysicalCapacity() == null
                    || BigDecimal.valueOf(0).compareTo(dto.getPhysicalCapacity()) == 0) {
                occupancyForecastPerc = BigDecimal.valueOf(0);
            } else {
                occupancyForecastPerc = dto.getOccupancyNumber().divide(dto.getPhysicalCapacity(), 4,
                        RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP)).setScale(2);
            }
            valuesForFG.add(occupancyForecastPerc);
        } else {
            valuesForFG.add(dto.getOccupancyPercentage());
        }
    }

    private static final String CLIENT_BUSINESS_GROUP_NAME_MKT_SEG_CODE = "select cbg.Client_Business_Group_Name, cbgmsc.Mkt_Seg_Code \n"
            + "from  \n" + "	Client_Business_Group_Mkt_Seg_Code cbgmsc, \n" + " 	Client_Business_Group cbg, \n"
            + "	Client_Business_View cbv \n" + "where \n" + "	cbgmsc.Client_Business_Group_ID = cbg.Client_Business_Group_ID \n"
            + "	and cbg.Client_Business_View_ID = cbv.Client_Business_View_ID \n" + "   and cbg.Status_ID = 1 \n"
            + "	and cbv.Default_View = 1 \n" + "   and cbv.Client_ID = :clientId";

    private static final String OCCUPANCY_PERCENT_BY_ARRIVAL_DATE_AND_MKT_SEG = "select cap.Occupancy_DT, Mkt_Seg_Code, AvailableCapacity, OccupancyNumber,physicalCapacity from ( \n"
            + "		SELECT Occupancy_DT, sum(Total_Accom_Capacity - (Rooms_Not_Avail_Maint + Rooms_Not_Avail_Other) ) as AvailableCapacity ,sum(total_accom_capacity) as physicalCapacity \n"
            + "		FROM Total_Activity \n"
            + "		WHERE occupancy_DT BETWEEN :startDate AND :endDate \n"
            + "	    AND Property_ID in (:propertyIds) \n"
            + "	    GROUP BY Occupancy_DT \n"
            + ") cap INNER MERGE JOIN ( \n"
            + "		SELECT Occupancy_DT, Mkt_Seg_Code, SUM(occ.Occupancy_NBR) OccupancyNumber \n"
            + "		FROM FN_AT_MS_Occupancy_Exclude_Comp_MS(:caughtUpDate ,0, :excludeCompRooms) as occ \n"
            + "		inner join Mkt_Seg ms on occ.Mkt_Seg_ID = ms.Mkt_Seg_Id \n"
            + " 	inner join Accom_Type at on occ.Accom_Type_ID = at.Accom_Type_ID and occ.Property_ID = at.Property_ID and at.isComponentRoom = 'N'"
            + "		WHERE occ.occupancy_DT BETWEEN :startDate AND :endDate \n"
            + "    	AND occ.Property_ID in (:propertyIds) "
            + "		and ms.Status_ID = 1 \n"
            + "		group by Occupancy_DT, Mkt_Seg_Code \n"
            + ") mktsegdata on cap.Occupancy_DT = mktsegdata.Occupancy_DT \n" + "order by cap.Occupancy_DT, Mkt_Seg_Code \n";

    /**
     * This method will return metric response for Business Group in the Default
     * corporate business view
     *
     * @param startDate
     * @param endDate
     * @param caughtUpDate
     * @param metricRequest
     * @return {@link MetricResponse}
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public MetricResponse buildOccupancyPercentByBGMetric(Date startDate, Date endDate, Date caughtUpDate, MetricRequest metricRequest) {

        MetricResponse response = new MetricResponse();
        try {
            // Lookup the default client business view - if there isn't one,
            // don't try to gather any additional data
            ClientBusinessView defaultClientBusinessView = getDefaultClientBusinessViewForClient();
            if (defaultClientBusinessView == null) {
                return response;
            }

            // Build a Map of Mkt_Seg_Code -> Business_Group_Names
            Map<String, String> mktSegToBusinessGroup = getMktSegBusinessGroupMapping();

            // Get the Mkt_Seg_Code by Occupancy Date results to then merge with
            // the business group names
            List<OccupancyPercentByDateAndGroupDto> resultList = getByOccupancyPercentByBGMetric(startDate, endDate, caughtUpDate, metricRequest.isExcludeCompRooms());

            // Convert the OccupancyPercentByDateAndGroupDto to
            // OccupancyPercentByDateAndGroupDto's with the business group name
            // replacing the mkt seg code to run Map/Reduce to aggregate values
            List<OccupancyPercentByDateAndGroupDto> occupancyPercentByDateGroupAndCapacityDtos = new ArrayList<OccupancyPercentByDateAndGroupDto>();

            if (resultList != null) {
                for (OccupancyPercentByDateAndGroupDto dto : resultList) {
                    String businessGroup = mktSegToBusinessGroup.get(dto.getGroupName());
                    if (StringUtils.isEmpty(businessGroup)) {
                        businessGroup = UNASSIGNED;
                    }

                    OccupancyPercentByDateAndGroupDto occupancyPercentByDateAndGroupDto = new OccupancyPercentByDateAndGroupDto();
                    occupancyPercentByDateAndGroupDto.setGroupName(businessGroup);
                    occupancyPercentByDateAndGroupDto.setOccupancyDate(dto.getOccupancyDate());
                    occupancyPercentByDateAndGroupDto.setOccupancyNumber(dto.getOccupancyNumber());
                    occupancyPercentByDateGroupAndCapacityDtos.add(occupancyPercentByDateAndGroupDto);
                }
            }

            // Call the MultiPropertyAggregationService to aggregate based on
            // Business Group, Occupancy Date, and Available Capacity
            // NOTE: capacity is included in the key because it will be the same
            // for every business group & occupancy date
            occupancyPercentByDateGroupAndCapacityDtos = getMultiPropertyAggregationService().aggregate(
                    occupancyPercentByDateGroupAndCapacityDtos);
            populateTotalCapacityValues(startDate, endDate, occupancyPercentByDateGroupAndCapacityDtos);

            List<BigDecimal> valuesForBG = new ArrayList<BigDecimal>();
            String currentBusinessGroup = "";
            boolean enablePhysicalCapacityConsideration = configParamsService.isEnablePhysicalCapacityConsideration();

            for (OccupancyPercentByDateAndGroupDto dto : occupancyPercentByDateGroupAndCapacityDtos) {

                if ("".equals(currentBusinessGroup)) {
                    // first record
                    currentBusinessGroup = dto.getGroupName();
                    setOccupancyPercent(valuesForBG, dto, enablePhysicalCapacityConsideration);
                } else if (!currentBusinessGroup.equals(dto.getGroupName())) {
                    // changing accom classes
                    response.addMetricValuesForGroupByType(currentBusinessGroup, valuesForBG);
                    valuesForBG = new ArrayList();
                    setOccupancyPercent(valuesForBG, dto, enablePhysicalCapacityConsideration);
                    currentBusinessGroup = dto.getGroupName();
                } else {
                    setOccupancyPercent(valuesForBG, dto, enablePhysicalCapacityConsideration);
                }
            }
            // add the last accom class
            response.addMetricValuesForGroupByType(currentBusinessGroup, valuesForBG);
        } catch (TetrisException te) {
            LOGGER.error(TETRIS_EXCEPTION, te);
            // ignore and return empty integer results if no accom classes
        }
        return response;
    }

    private void populateTotalCapacityValues(Date startDate, Date endDate, List<OccupancyPercentByDateAndGroupDto> occupancyPercentByDateGroupAndCapacityDtos) {
        List<CapacityByOccupancyDayDto> capacityList = getTotalCapacityPerOccupancyDay(startDate, endDate);
        for (OccupancyPercentByDateAndGroupDto dto : occupancyPercentByDateGroupAndCapacityDtos) {
            Date occupancyDate = dto.getOccupancyDate();
            capacityList.stream().filter(currentDay -> currentDay.getOccupancyDate().equals(occupancyDate)).findFirst().ifPresent(currentDay -> {
                dto.setAvailableCapacity(currentDay.getAvailableCapacity());
                dto.setPhysicalCapacity(currentDay.getPhysicalCapacity());
            });
        }
    }

    private List<CapacityByOccupancyDayDto> getTotalCapacityPerOccupancyDay(Date startDate, Date endDate) {
        return multiPropertyCrudService.findByNativeQueryUnionAcrossProperties(
                propertyGroupService.getPropertyContextAsList(),
                GET_TOTAL_CAPACITY_PER_OCCUPANCY_DATE,
                QueryParameter.with(PROPERTY_IDS, propertyGroupService.getPropertyContextAsList()).and(START_DATE, startDate)
                        .and(END_DATE, endDate).parameters(),
                new CapacityByOccupancyDayDtoRowMapper());
    }

    public Map<Date, BigDecimal> buildRevenueByBGMetric(Date startDate, Date endDate, Date caughtUpDate, List<Integer> selectedBusinessGroupIds, boolean excludeCompRooms) {
        Map<Date, BigDecimal> resultsByDate = new LinkedHashMap<>();

        ClientBusinessView defaultClientBusinessView = getDefaultClientBusinessViewForClient();
        if (defaultClientBusinessView == null) {
            return resultsByDate;
        }

        // Build a Map of Mkt_Seg_Code -> Business_Group_Names
        Map<String, String> mktSegToBusinessGroup = getMktSegBusinessGroupMapping();

        List<RevenueByMarketSegmentDto> resultList = getRevenueByMktSeg(startDate, endDate, caughtUpDate, excludeCompRooms);

        Set<String> selectedBusinessGroups = fetchCorporateBusinessViewsForIds(selectedBusinessGroupIds);

        resultList.stream().forEach(revenueByMarketSegmentDto -> {

            String businessGroup = mktSegToBusinessGroup.get(revenueByMarketSegmentDto.getMarketSegCode());
            if (StringUtils.isEmpty(businessGroup)) {
                businessGroup = UNASSIGNED;
            }

            if (selectedBusinessGroups.contains(businessGroup)) {
                BigDecimal revenue = resultsByDate.get(revenueByMarketSegmentDto.getOccupancyDate());
                if (null == revenue) {
                    revenue = BigDecimal.ZERO;
                }
                BigDecimal revenueSum = revenue.add(revenueByMarketSegmentDto.getRevenue());
                resultsByDate.put(revenueByMarketSegmentDto.getOccupancyDate(), revenueSum);
            }

        });

        return resultsByDate;
    }

    public MetricResponse buildRoomsSoldByBGMetric(Date startDate, Date endDate, Date caughtUpDate, boolean excludeCompRooms) {
        MetricResponse response = new MetricResponse();
        try {
            Map<String, BigDecimal> businessGroupTotal = getRoomSoldsForCorporateBusinessGroup(startDate, endDate, caughtUpDate, excludeCompRooms);
            businessGroupTotal.keySet().stream().forEach(businessGroup -> {
                response.addMetricValuesForGroupByType(businessGroup, Arrays.asList(businessGroupTotal.get(businessGroup)));
            });
        } catch (TetrisException te) {
            LOGGER.error(TETRIS_EXCEPTION, te);
        }
        return response;
    }

    private Map<String, BigDecimal> getRoomSoldsForCorporateBusinessGroup(Date startDate, Date endDate, Date caughtUpDate, boolean excludeCompRooms) {
        Map<String, BigDecimal> businessGroupTotal = new HashMap<>();
        ClientBusinessView defaultClientBusinessView = getDefaultClientBusinessViewForClient();
        if (defaultClientBusinessView == null) {
            return businessGroupTotal;
        }

        // Build a Map of Mkt_Seg_Code -> Business_Group_Names
        Map<String, String> mktSegToBusinessGroup = getMktSegBusinessGroupMapping();
        List<OccupancyByGroupDto> resultList;
        resultList = getRoomSoldsByMktSeg(startDate, endDate, caughtUpDate, excludeCompRooms);

        resultList.stream().forEach(occupancyByGroupDto -> {
            String businessGroup = mktSegToBusinessGroup.get(occupancyByGroupDto.getGroupName());
            if (StringUtils.isEmpty(businessGroup)) {
                businessGroup = UNASSIGNED;
            }
            BigDecimal roomSoldsTotal = businessGroupTotal.get(businessGroup);
            if (null == roomSoldsTotal) {
                roomSoldsTotal = BigDecimal.ZERO;
            }
            BigDecimal total = roomSoldsTotal.add(occupancyByGroupDto.getOccupancyNumber());
            businessGroupTotal.put(businessGroup, total);
        });

        return businessGroupTotal;

    }

    private ClientBusinessView getDefaultClientBusinessViewForClient() {
        return globalCrudService.findByNamedQuerySingleResult(
                ClientBusinessView.GET_DEFAULT_CORP_VIEW_BY_CLIENT,
                QueryParameter.with("clientId", PacmanWorkContextHelper.getClientId()).parameters());
    }

    private List<OccupancyByGroupDto> getRoomSoldsByMktSeg(Date startDate, Date endDate, Date caughtUpDate, boolean excludeCompRooms) {
        String query;
        Map<String, Object> parameters = QueryParameter.with(CAUGHT_UP_DATE, caughtUpDate)
                .and(START_DATE, startDate)
                .and(END_DATE, endDate)
                .and(EXCLUDE_COMP_ROOMS, excludeCompRooms ? "0" : "0,1")
                .parameters();
        if (SystemConfig.useOptimizedStoredProcedure()) {
            query = EXEC_ROOMS_SOLD_BY_MARKET_SEGMENT;
        } else {
            query = ROOM_SOLDS_BY_MARKET_SEGMENT;
        }
        return multiPropertyCrudService.findByNativeQueryUnionAcrossProperties(
                propertyGroupService.getPropertyContextAsList(),
                query,
                parameters,
                new OccupancyByBusinessGroupDtoRowMapper());
    }

    public static final String ROOM_SOLDS_BY_MARKET_SEGMENT = new StringBuilder()
            .append("select mktSeg.Mkt_Seg_Code, SUM(fnAtMs.Occupancy_NBR) as RoomSolds ")
            .append("from FN_AT_MS_Occupancy_Exclude_Comp_MS(:caughtUpDate ,0, :excludeCompRooms) as fnAtMs ")
            .append("inner join Accom_Type as at ")
            .append("ON at.Accom_Type_ID = fnAtMS.Accom_Type_ID and at.isComponentRoom = 'N'")
            .append("inner join Mkt_Seg as mktSeg ")
            .append("on mktSeg.Mkt_Seg_ID = fnAtMs.Mkt_Seg_ID ")
            .append("where Occupancy_DT between :startDate and :endDate ")
            .append("group by fnAtMs.Mkt_Seg_ID, mktSeg.Mkt_Seg_Code ").toString();

    public BigDecimal buildRevenueTotalByBGMetric(Date startDate, Date endDate, Date caughtUpDate, List<Integer> selectedBusinessGroupIds, boolean excludeCompRooms) {

        ClientBusinessView defaultClientBusinessView = getDefaultClientBusinessViewForClient();
        if (defaultClientBusinessView == null) {
            return BigDecimal.ZERO;
        }

        // Build a Map of Mkt_Seg_Code -> Business_Group_Names
        Map<String, String> mktSegToBusinessGroup = getMktSegBusinessGroupMapping();

        List<RevenueByMarketSegmentDto> resultList = getRevenueByMktSeg(startDate, endDate, caughtUpDate, excludeCompRooms);

        Set<String> selectedBusinessGroups = fetchCorporateBusinessViewsForIds(selectedBusinessGroupIds);
        RevenueTotal revenueTotal = new RevenueTotal();
        return revenueTotal.getRevenueTotal(resultList, mktSegToBusinessGroup, selectedBusinessGroups);
    }

    private Map<String, String> getMktSegBusinessGroupMapping() {
        Map<String, String> mktSegToBusinessGroup = new HashMap<String, String>();
        List<ClientBusinessGroupMktSegCodeDto> clientBusinessGroupDtos = globalCrudService.findByNativeQuery(
                CLIENT_BUSINESS_GROUP_NAME_MKT_SEG_CODE, QueryParameter.with("clientId", PacmanWorkContextHelper.getClientId())
                        .parameters(), new ClientBusinessGroupMktSegCodeDtoRowMapper());
        if (clientBusinessGroupDtos != null) {
            for (ClientBusinessGroupMktSegCodeDto dto : clientBusinessGroupDtos) {
                mktSegToBusinessGroup.put(dto.getMktSegCode(), dto.getBusinessGroupName());
            }
        }
        return mktSegToBusinessGroup;
    }

    private class RevenueTotal {
        BigDecimal revenue = BigDecimal.ZERO;

        public BigDecimal getRevenueTotal(List<RevenueByMarketSegmentDto> resultList, Map<String, String> mktSegToBusinessGroup, Set<String> selectedBusinessGroups) {
            resultList.stream().forEach(revenueByMarketSegmentDto -> {
                String businessGroup = mktSegToBusinessGroup.get(revenueByMarketSegmentDto.getMarketSegCode());
                if (StringUtils.isEmpty(businessGroup)) {
                    businessGroup = UNASSIGNED;
                }
                if (selectedBusinessGroups.contains(businessGroup)) {
                    revenue = revenue.add(revenueByMarketSegmentDto.getRevenue());
                }
            });
            return revenue;
        }
    }

    private Set<String> fetchCorporateBusinessViewsForIds(List<Integer> businessViewIds) {
        Set<String> businessGroupCodes = new HashSet<>();
        businessViewIds.forEach(businessViewId -> {
            if (businessViewId == -1) {
                businessGroupCodes.add(UNASSIGNED);
            } else {
                ClientBusinessGroup clientBusinessGroup = globalCrudService.find(ClientBusinessGroup.class, businessViewId);
                if (null != clientBusinessGroup) {
                    businessGroupCodes.add(clientBusinessGroup.getName());
                }
            }
        });
        return businessGroupCodes;
    }

    private List<RevenueByMarketSegmentDto> getRevenueByMktSeg(Date startDate, Date endDate, Date caughtUpDate, boolean excludeCompRooms) {
        Map<String, Object> parameters = QueryParameter.with(CAUGHT_UP_DATE, caughtUpDate)
                .and(START_DATE, startDate)
                .and(END_DATE, endDate)
                .and(EXCLUDE_COMP_ROOMS, excludeCompRooms ? "0" : "0,1")
                .parameters();
        String query = RevenueMetricQuery.REVENUE_BY_MARKET_SEGMENT;
        if (SystemConfig.useOptimizedStoredProcedure()) {
            query = RevenueMetricQuery.EXEC_REVENUE_BY_MARKET_SEGMENT;
        }
        return multiPropertyCrudService.findByNativeQueryUnionAcrossProperties(
                propertyGroupService.getPropertyContextAsList(),
                query,
                parameters,
                new RevenueByMarketSegmentRowMapper()
        );
    }

    public List<OccupancyPercentByDateAndGroupDto> getByOccupancyPercentByBGMetric(Date startDate, Date endDate, Date caughtUpDate, boolean excludeCompRooms) {
        return multiPropertyCrudService.findByNativeQueryUnionAcrossProperties(
                propertyGroupService.getPropertyContextAsList(),
                OCCUPANCY_PERCENT_BY_ARRIVAL_DATE_AND_MKT_SEG,
                QueryParameter.with(PROPERTY_IDS, propertyGroupService.getPropertyContextAsList()).and(START_DATE, startDate)
                        .and(END_DATE, endDate).and(CAUGHT_UP_DATE, caughtUpDate)
                        .and(EXCLUDE_COMP_ROOMS, excludeCompRooms ? "0" : "0,1").parameters(),
                new OccupancyPercentByArrivalDateAndBusinessGroupDtoRowMapper());
    }

    public List<OccupancyPercentByDateAndGroupDto> getByOccupancyPercentByFGMetric(Date startDate, Date endDate, Date caughtUpDate, boolean excludeCompRooms) {
        return multiPropertyCrudService.findByNativeQueryUnionAcrossProperties(
                propertyGroupService.getPropertyContextAsList(),
                OCCUPANCY_PERCENT_BY_ARRIVAL_DATE_AND_FG_MULTI_PROP,
                QueryParameter.with(PROPERTY_IDS, propertyGroupService.getPropertyContextAsList()).and(START_DATE, startDate)
                        .and(END_DATE, endDate).and(CAUGHT_UP_DATE, caughtUpDate)
                        .and(EXCLUDE_COMP_ROOMS, excludeCompRooms ? "0" : "0,1").parameters(),
                new OccupancyPercentByArrivalDateAndBusinessGroupDtoRowMapper());
    }

    private static final StringBuilder OCCUPANCY_PERCENT_BY_ARRIVAL_DATE_AND_PROPERTY_BUSINESS_VIEW_SINGLE_PROP_BY_INVENTORY_GROUP =
            new StringBuilder()
                    .append("SELECT fgdata.Occupancy_DT, Business_Group_name, AvailableCapacity, occupancy_nbr,physicalCapacity ")
                    .append("FROM ")
                    .append("( ")
                    .append("	SELECT Occupancy_DT,")
                    .append("	(SUM(aa.Accom_Capacity) - (SUM(aa.Rooms_Not_Avail_Maint) + SUM(aa.Rooms_Not_Avail_Other))) as AvailableCapacity, ")
                    .append("	SUM(aa.Accom_Capacity) as physicalCapacity ")
                    .append("   FROM Accom_Activity as aa (nolock) ")
                    .append("   inner join Accom_Type at ")
                    .append("   on at.Accom_Type_ID = aa.Accom_Type_ID ")
                    .append("   inner join Inventory_Group_Details as igd ")
                    .append("   on igd.Accom_Class_ID = at.Accom_Class_ID and igd.Inventory_Group_ID = :inventoryGroupId ")
                    .append("   WHERE occupancy_DT BETWEEN :startDate AND :endDate ")
                    .append("	GROUP BY Occupancy_DT ")
                    .append(") as cap ")
                    .append("INNER MERGE JOIN ")
                    .append("( ")
                    .append("	SELECT occ.Occupancy_DT, Business_Group_Name, SUM(occ.Occupancy_NBR) as occupancy_nbr ")
                    .append("	FROM ")
                    .append("	( ")
                    .append("		SELECT Occupancy_DT, fnATMS.Mkt_Seg_ID, ")
                    .append("		Occupancy_NBR as Occupancy_NBR ")
                    .append("		FROM FN_InventoryGroupView_AT_MS_Occupancy(:caughtUpDate, :inventoryGroupId) fnATMS ")
                    .append("		inner join Accom_Type at ON fnATMS.Accom_Type_ID = at.Accom_Type_ID and at.isComponentRoom = 'N' ")
                    .append("		WHERE occupancy_DT BETWEEN :startDate AND :endDate ")
                    .append("	) AS occ ")
                    .append("	INNER MERGE JOIN ")
                    .append("	(")
                    .append("		select ISNULL(b.Business_Group_Name,'Unassigned') as Business_Group_name, a.Mkt_Seg_ID from ")
                    .append("		( ")
                    .append("			select Mkt_Seg.Mkt_Seg_Code, Mkt_Seg.Mkt_Seg_ID from Mkt_Seg where Mkt_Seg.Status_ID = 1 ")
                    .append("				and Mkt_Seg.Exclude_CompHouse_Data_Display IN (select value from varcharToInt(:excludeCompRooms, ',')) ")
                    .append("		) as a ")
                    .append("		left hash join ")
                    .append("		( ")
                    .append("			select Business_Group_name,Mkt_Seg_Business_Group.Mkt_Seg_ID ")
                    .append("			from Business_Group ")
                    .append(" 	    	left join Mkt_Seg_Business_Group (nolock) ")
                    .append(" 	    	on Business_Group.Business_Group_ID=Mkt_Seg_Business_Group.Business_Group_ID ")
                    .append(" 	    	left join Mkt_Seg ")
                    .append(" 		    on Mkt_Seg.Mkt_Seg_ID = Mkt_Seg_Business_Group.Mkt_Seg_ID ")
                    .append(" 		) as b on a.Mkt_Seg_ID=b.Mkt_Seg_ID ")
                    .append(" 		group by a.Mkt_Seg_ID, Business_Group_Name ")
                    .append("	)  as msdata on occ.Mkt_Seg_ID = msdata.Mkt_Seg_ID ")
                    .append("	WHERE occ.occupancy_DT BETWEEN :startDate AND :endDate ")
                    .append("	GROUP BY occ.Occupancy_DT, Business_Group_Name ")
                    .append(") as fgdata ")
                    .append("ON cap.Occupancy_DT=fgdata.Occupancy_DT ")
                    .append("ORDER BY Business_Group_Name,Occupancy_DT ");

    private static final String OCCUPANCY_PERCENT_BY_ARRIVAL_DATE_AND_PROPERTY_BUSINESS_VIEW_SINGLE_PROP =

            "SELECT fgdata.Occupancy_DT, Business_Group_name, AvailableCapacity, occupancy_nbr,physicalCapacity "
                    + " FROM "
                    + " ( "
                    + " 	SELECT Occupancy_DT,sum(Total_Accom_Capacity - (Rooms_Not_Avail_Maint + Rooms_Not_Avail_Other) ) as AvailableCapacity,sum(total_accom_capacity) as physicalCapacity "
                    + " 	 FROM Total_Activity (nolock)"
                    + "   WHERE occupancy_DT BETWEEN :startDate AND :endDate "
                    + "     AND Property_ID in (:propertyIds) "
                    + "    GROUP BY Occupancy_DT "
                    + " ) as cap INNER MERGE JOIN "
                    + " ( "
                    + "   SELECT occ.Occupancy_DT, Business_Group_Name, SUM(occ.Occupancy_NBR) as occupancy_nbr "
                    + " FROM ( " +
                    "  SELECT Occupancy_DT,   Mkt_Seg_ID, fnATMS.Property_ID, " +
                    "      Occupancy_NBR as Occupancy_NBR" +
                    "      FROM FN_AT_MS_Occupancy_Exclude_Comp_MS(:caughtUpDate, 0, :excludeCompRooms) fnATMS inner join Accom_Type at " +
                    "          on fnATMS.Accom_Type_ID = at.Accom_Type_ID " +
                    "          and fnATMS.Property_ID = at.Property_ID" +
                    "          and at.isComponentRoom = 'N' " +
                    "      WHERE occupancy_DT BETWEEN :startDate AND :endDate" +
                    "      AND fnATMS.Property_ID IN (:propertyIds) " +
                    "  ) AS occ INNER MERGE "
                    + "     JOIN ( "
                    + " 		select ISNULL(b.Business_Group_Name,'Unassigned') as Business_Group_name, a.Mkt_Seg_ID from ( "
                    + " 			select Mkt_Seg.Mkt_Seg_Code, Mkt_Seg.Mkt_Seg_ID from Mkt_Seg where Property_ID in (:propertyIds) and Mkt_Seg.Status_ID = 1 "
                    + " 			) as a "
                    + " 			 left hash join "
                    + "  			( "
                    + "  	 			 select Business_Group_name,Mkt_Seg_Business_Group.Mkt_Seg_ID "
                    + " 	 			 from Business_Group "
                    + " 				 left join Mkt_Seg_Business_Group (nolock) "
                    + " 				 on Business_Group.Business_Group_ID=Mkt_Seg_Business_Group.Business_Group_ID "
                    + " 	 			 left join Mkt_Seg "
                    + " 	  			 on Mkt_Seg.Mkt_Seg_ID = Mkt_Seg_Business_Group.Mkt_Seg_ID "
                    + " 			) as b on a.Mkt_Seg_ID=b.Mkt_Seg_ID "
                    + " 			group by a.Mkt_Seg_ID, Business_Group_Name "
                    + "   )  as msdata on occ.Mkt_Seg_ID = msdata.Mkt_Seg_ID "
                    + "    WHERE occ.occupancy_DT BETWEEN :startDate AND :endDate "
                    + "    AND occ.Property_ID in (:propertyIds) "
                    + "    GROUP BY occ.Occupancy_DT, Business_Group_Name "
                    + " ) as fgdata "
                    + " ON cap.Occupancy_DT=fgdata.Occupancy_DT "
                    + " ORDER BY Business_Group_Name,Occupancy_DT ";

    private static final StringBuilder OCCUPANCY_BY__PROPERTY_BUSINESS_VIEW_SINGLE_PROP =
            new StringBuilder().
                    append("SELECT  Business_Group_name, occupancy_nbr")
                    .append(" FROM ")
                    .append("( SELECT  Business_Group_Name, SUM(occ.Occupancy_NBR) as occupancy_nbr ")
                    .append(" FROM ( ")
                    .append(" SELECT Mkt_Seg_ID, fnATMS.Property_ID, Occupancy_NBR as Occupancy_NBR ")
                    .append(" FROM FN_AT_MS_Occupancy(:caughtUpDate, 0) fnATMS inner join Accom_Type at ")
                    .append(" on fnATMS.Accom_Type_ID = at.Accom_Type_ID ")
                    .append(" and fnATMS.Property_ID = at.Property_ID ")
                    .append(" and at.isComponentRoom = 'N' ")
                    .append(" WHERE occupancy_DT BETWEEN :startDate AND :endDate ")
                    .append(" AND fnATMS.Property_ID IN (:propertyIds) ")
                    .append(" ) AS occ INNER MERGE JOIN ")
                    .append(" ( select ISNULL(b.Business_Group_Name,'Unassigned') as Business_Group_name, a.Mkt_Seg_ID from (  ")
                    .append(" select Mkt_Seg.Mkt_Seg_Code, Mkt_Seg.Mkt_Seg_ID from Mkt_Seg where Property_ID in (:propertyIds) and Mkt_Seg.Status_ID = 1 and Mkt_Seg.Exclude_CompHouse_Data_Display IN (select value from varcharToInt(:excludeCompRooms, ','))")
                    .append(" ) as a left hash join (  ")
                    .append(" select Business_Group_name,Mkt_Seg_Business_Group.Mkt_Seg_ID ")
                    .append(" from Business_Group ")
                    .append(" left join Mkt_Seg_Business_Group (nolock) ")
                    .append(" on Business_Group.Business_Group_ID=Mkt_Seg_Business_Group.Business_Group_ID ")
                    .append(" left join Mkt_Seg ")
                    .append(" on Mkt_Seg.Mkt_Seg_ID = Mkt_Seg_Business_Group.Mkt_Seg_ID ")
                    .append(" ) as b on a.Mkt_Seg_ID=b.Mkt_Seg_ID ")
                    .append(" group by a.Mkt_Seg_ID, Business_Group_Name ")
                    .append(")  as msdata on occ.Mkt_Seg_ID = msdata.Mkt_Seg_ID ")
                    .append(" WHERE occ.Property_ID in (:propertyIds) GROUP BY Business_Group_Name ")
                    .append(") as fgdata ");


    private static final StringBuilder OCCUPANCY__BY_PROPERTY_BUSINESS_VIEW_SINGLE_PROP_BY_INVENTORY_GROUP =
            new StringBuilder().
                    append(" SELECT Business_Group_name, occupancy_nbr ")
                    .append(" FROM ( ")
                    .append(" SELECT Business_Group_Name, SUM(occ.Occupancy_NBR) as occupancy_nbr ")
                    .append(" FROM ( ")
                    .append(" SELECT Mkt_Seg_ID, Occupancy_NBR as Occupancy_NBR ")
                    .append(" FROM FN_InventoryGroupView_AT_MS_Occupancy(:caughtUpDate, :inventoryGroupId) fnATMS ")
                    .append(" WHERE occupancy_DT BETWEEN :startDate AND :endDate ")
                    .append(" ) AS occ ")
                    .append(" INNER MERGE JOIN ( ")
                    .append(" select ISNULL(b.Business_Group_Name,'Unassigned') as Business_Group_name, a.Mkt_Seg_ID from ")
                    .append(" ( select Mkt_Seg.Mkt_Seg_Code, Mkt_Seg.Mkt_Seg_ID from Mkt_Seg where Mkt_Seg.Status_ID = 1 and Mkt_Seg.Exclude_CompHouse_Data_Display IN (select value from varcharToInt(:excludeCompRooms, ','))) as a ")
                    .append(" left hash join (  ")
                    .append(" select Business_Group_name,Mkt_Seg_Business_Group.Mkt_Seg_ID ")
                    .append(" from Business_Group ")
                    .append(" left join Mkt_Seg_Business_Group (nolock) ")
                    .append(" on Business_Group.Business_Group_ID=Mkt_Seg_Business_Group.Business_Group_ID ")
                    .append(" left join Mkt_Seg ")
                    .append(" on Mkt_Seg.Mkt_Seg_ID = Mkt_Seg_Business_Group.Mkt_Seg_ID ")
                    .append(" ) as b on a.Mkt_Seg_ID=b.Mkt_Seg_ID ")
                    .append(" group by a.Mkt_Seg_ID, Business_Group_Name ")
                    .append(" )  as msdata on occ.Mkt_Seg_ID = msdata.Mkt_Seg_ID ")
                    .append(" GROUP BY Business_Group_Name ")
                    .append(" ) as fgdata ")
                    .append(" ORDER BY Business_Group_Name ");

    /**
     * This method will return metric response for Property Business View for a
     * selected Property
     *
     * @param startDate
     * @param endDate
     * @param caughtUpDate
     * @return {@link MetricResponse}
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public MetricResponse buildOccupancyPercentByPBVMetric(Date startDate, Date endDate, Date caughtUpDate, MetricRequest metricRequest) {

        MetricResponse response = new MetricResponse();
        try {
            List<OccupancyPercentByDateAndGroupDto> resultList;
            resultList = getOccupancyPercentByDateAndGroupDtos(startDate, endDate, caughtUpDate, metricRequest);

            List<BigDecimal> valuesForPBV = new ArrayList<BigDecimal>();
            String currentBusinessGroup = "";
            boolean enablePhysicalCapacityConsideration = configParamsService.isEnablePhysicalCapacityConsideration();

            for (OccupancyPercentByDateAndGroupDto dto : resultList) {
                if ("".equals(currentBusinessGroup)) {
                    // first record
                    currentBusinessGroup = dto.getGroupName();
                    setOccupancyPercent(valuesForPBV, dto, enablePhysicalCapacityConsideration);
                } else if (!currentBusinessGroup.equals(dto.getGroupName())) {
                    // changing accom classes
                    response.addMetricValuesForGroupByType(currentBusinessGroup, valuesForPBV);
                    valuesForPBV = new ArrayList();
                    setOccupancyPercent(valuesForPBV, dto, enablePhysicalCapacityConsideration);
                    currentBusinessGroup = dto.getGroupName();
                } else {
                    setOccupancyPercent(valuesForPBV, dto, enablePhysicalCapacityConsideration);
                }
            }
            // add the last accom class
            response.addMetricValuesForGroupByType(currentBusinessGroup, valuesForPBV);
        } catch (TetrisException te) {
            LOGGER.error(TETRIS_EXCEPTION, te);
            // ignore and return empty integer results if no accom classes
        }
        return response;
    }

    public MetricResponse buildRoomSoldsByPBVMetric(Date startDate, Date endDate, Date caughtUpDate, MetricRequest metricRequest) {

        MetricResponse response = new MetricResponse();
        try {
            List<OccupancyByGroupDto> resultList;
            resultList = getOccupancyByGroupDtos(startDate, endDate, caughtUpDate, metricRequest);
            for (OccupancyByGroupDto dto : resultList) {
                response.addMetricValuesForGroupByType(dto.getGroupName(), Arrays.asList(dto.getOccupancyNumber()));
            }
        } catch (TetrisException te) {
            LOGGER.error(TETRIS_EXCEPTION, te);
        }
        return response;
    }


    public List<OccupancyByGroupDto> getOccupancyByGroupDtos(Date startDate, Date endDate, Date caughtUpDate, MetricRequest metricRequest) {
        List<OccupancyByGroupDto> resultList;
        if (metricRequest.getGroupByType().equals(GroupByType.INVENTORY_GROUP_WITH_BUSINESS_VIEW)) {
            resultList = getOccupancyByBVSinglePropForInventoryGroup(startDate, endDate, caughtUpDate, metricRequest.getInventoryGroupId(), metricRequest.isExcludeCompRooms());
        } else {
            resultList = getOccupancyByBVSingleProp(startDate, endDate, caughtUpDate, metricRequest.isExcludeCompRooms());
        }
        return resultList;
    }


    public List<OccupancyByGroupDto> getOccupancyByBVSinglePropForInventoryGroup(Date startDate, Date endDate, Date caughtUpDate, Integer inventoryGroupId, boolean excludeCompRooms) {
        String query;
        Map<String, Object> parameters = QueryParameter.with(START_DATE, startDate)
                .and(END_DATE, endDate)
                .and(CAUGHT_UP_DATE, caughtUpDate)
                .and(INVENTORY_GROUP_ID, inventoryGroupId)
                .and(EXCLUDE_COMP_ROOMS, excludeCompRooms ? "0" : "0,1").parameters();
        if (SystemConfig.useOptimizedStoredProcedure()) {
            query = EXEC_OCCUPANCY_BY_PROPERTY_BUSINESS_VIEW_SINGLE_PROP_BY_INVENTORY_GROUP;
        } else {
            query = OCCUPANCY__BY_PROPERTY_BUSINESS_VIEW_SINGLE_PROP_BY_INVENTORY_GROUP.toString();
        }
        List<OccupancyByGroupDto> results = crudService.findByNativeQuery(
                query,
                parameters,
                new OccupancyByBusinessGroupDtoRowMapper());
        return null == results ? Collections.emptyList() : results;
    }


    public List<OccupancyPercentByDateAndGroupDto> getOccupancyPercentByDateAndGroupDtos(Date startDate, Date endDate, Date caughtUpDate, MetricRequest metricRequest) {
        List<OccupancyPercentByDateAndGroupDto> resultList;
        if (metricRequest.getGroupByType().equals(GroupByType.INVENTORY_GROUP_WITH_BUSINESS_VIEW)) {
            resultList = getOccupancyPercentByBVSinglePropForInventoryGroup(startDate, endDate, caughtUpDate, metricRequest.getInventoryGroupId(), metricRequest.isExcludeCompRooms());
        } else {
            resultList = getOccupancyPercentByBVSingleProp(startDate, endDate, caughtUpDate, metricRequest.isExcludeCompRooms());
        }
        return resultList;
    }

    public List<OccupancyPercentByDateAndGroupDto> getOccupancyPercentByBVSinglePropForInventoryGroup(Date startDate, Date endDate,
                                                                                                      Date caughtUpDate, Integer inventoryGroupId, boolean excludeCompRooms) {
        String query;
        Map<String, Object> parameters = QueryParameter.with(START_DATE, startDate)
                .and(END_DATE, endDate).and(CAUGHT_UP_DATE, caughtUpDate)
                .and(INVENTORY_GROUP_ID, inventoryGroupId)
                .and(EXCLUDE_COMP_ROOMS, excludeCompRooms ? "0" : "0,1").parameters();
        if (SystemConfig.useOptimizedStoredProcedure()) {
            query = EXEC_OCCUPANCY_PERCENT_BY_ARRIVAL_DATE_AND_PROPERTY_BUSINESS_VIEW_SINGLE_PROP_BY_INVENTORY_GROUP;
        } else {
            query = OCCUPANCY_PERCENT_BY_ARRIVAL_DATE_AND_PROPERTY_BUSINESS_VIEW_SINGLE_PROP_BY_INVENTORY_GROUP.toString();
        }
        List<OccupancyPercentByDateAndGroupDto> results = crudService.findByNativeQuery(
                query,
                parameters,
                new OccupancyPercentByArrivalDateAndBusinessGroupDtoRowMapper());
        return null == results ? Collections.emptyList() : results;
    }

    public List<OccupancyPercentByDateAndGroupDto> getOccupancyPercentByBVSingleProp(Date startDate, Date endDate, Date caughtUpDate, boolean excludeCompRooms) {

        String query;
        Map<String, Object> parameters;

        if (SystemConfig.useOptimizedStoredProcedure()) {
            query = EXEC_USP_OCCUPANCY_PERCENT_BY_ARRIVAL_DATE_AND_PROPERTY_BUSINESS_VIEW_SINGLE_PROP;
            parameters = QueryParameter.with(START_DATE, startDate)
                    .and(END_DATE, endDate).and(CAUGHT_UP_DATE, caughtUpDate)
                    .and(EXCLUDE_COMP_ROOMS, excludeCompRooms ? "0" : "0,1").parameters();
        } else {
            query = OCCUPANCY_PERCENT_BY_ARRIVAL_DATE_AND_PROPERTY_BUSINESS_VIEW_SINGLE_PROP;
            parameters = QueryParameter.with(PROPERTY_IDS, propertyGroupService.getPropertyContextAsList()).and(START_DATE, startDate)
                    .and(END_DATE, endDate).and(CAUGHT_UP_DATE, caughtUpDate)
                    .and(EXCLUDE_COMP_ROOMS, excludeCompRooms ? "0" : "0,1").parameters();
        }

        return multiPropertyCrudService.findByNativeQueryUnionAcrossProperties(
                propertyGroupService.getPropertyContextAsList(),
                query,
                parameters,
                new OccupancyPercentByArrivalDateAndBusinessGroupDtoRowMapper());
    }

    public List<OccupancyByGroupDto> getOccupancyByBVSingleProp(Date startDate, Date endDate, Date caughtUpDate, boolean excludeCompRooms) {
        String query;
        Map<String, Object> parameters;
        if (SystemConfig.useOptimizedStoredProcedure()) {
            query = EXEC_OCCUPANCY_BY_PROPERTY_BUSINESS_VIEW_SINGLE_PROP;
            parameters = QueryParameter.with(START_DATE, startDate)
                    .and(END_DATE, endDate).and(CAUGHT_UP_DATE, caughtUpDate).parameters();
        } else {
            query = OCCUPANCY_BY__PROPERTY_BUSINESS_VIEW_SINGLE_PROP.toString();
            parameters = QueryParameter.with(PROPERTY_IDS, propertyGroupService.getPropertyContextAsList()).and(START_DATE, startDate)
                    .and(END_DATE, endDate).and(CAUGHT_UP_DATE, caughtUpDate)
                    .and(EXCLUDE_COMP_ROOMS, excludeCompRooms ? "0" : "0,1").parameters();
        }
        return multiPropertyCrudService.findByNativeQueryUnionAcrossProperties(
                propertyGroupService.getPropertyContextAsList(),
                query,
                parameters,
                new OccupancyByBusinessGroupDtoRowMapper());
    }

    private Integer getMasterAccomClassId() {
        return getMasterAccomClassId(PacmanThreadLocalContextHolder.getWorkContext().getPropertyId());
    }

    private Integer getMasterAccomClassId(Integer propertyId) {
        AccomClass masterClass = crudService.findByNamedQuerySingleResult(AccomClass.GET_MASTER_CLASS,
                QueryParameter.with(PROPERTY_ID, propertyId).parameters());
        if (masterClass != null) {
            return masterClass.getId();
        }
        return -1;
    }

    private Integer getBaseAccomClassForPhysicalProperty(String physicalPropertyCode) {
        String query = "SELECT Base_Accom_Class_Id From Inventory_Group WHERE Inventory_Group_Name = :physicalPropertyCode ";

        Integer masterClassId = crudService.findByNativeQuerySingleResult(query,
                QueryParameter.with(PHYSICAL_PROPERTY_CODE, physicalPropertyCode).parameters(), row -> (Integer) row[0]);

        return ofNullable(masterClassId).orElse(-1);
    }

}
