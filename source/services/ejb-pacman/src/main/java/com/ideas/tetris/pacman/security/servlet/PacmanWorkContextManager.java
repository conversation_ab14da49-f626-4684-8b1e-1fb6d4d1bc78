package com.ideas.tetris.pacman.security.servlet;

import com.ideas.infra.tetris.security.domain.LDAPUser;
import com.ideas.infra.tetris.security.jaas.TetrisPrincipal;
import com.ideas.infra.tetris.security.servlet.AuthRequestWrapper;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.PropertyGroup;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.pacman.services.security.AuthorizationService;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.contextholder.PlatformThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.errorhandling.TetrisSecurityException;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.security.servlet.WorkContextManager;
import com.ideas.tetris.platform.services.client.ClientCodePropertyCodeMappingService;
import com.ideas.tetris.platform.services.client.ClientProperty;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.StopWatch;
import org.apache.log4j.Logger;
import org.jfree.util.Log;

import javax.enterprise.context.Dependent;
import javax.inject.Inject;
import javax.servlet.http.HttpServletRequest;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import static org.apache.commons.lang3.math.NumberUtils.isDigits;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

/**
 * Manages work context on thread local for Pacman
 */
@Dependent
@Component
public class PacmanWorkContextManager {

    public static final String CLIENT_CODE = "clientCode";
    public static final String CLIENT_UUID = "clientUUID";
    public static final String PROPERTY_CODE = "propertyCode";
    public static final String PROPERTY_ID = "propertyId";
    public static final String UPS_ID = "upsId";
    public static final String PROPERTY_GROUP_ID = "propertyGroupId";
    public static final String VIRTUAL_PROPERTY_CODE = "virtualPropertyCode";
    private static final Logger log = Logger.getLogger(PacmanWorkContextManager.class);
    @Autowired
    AuthorizationService authService;
    @Autowired
    WorkContextManager workContextManager;
    @Autowired
    PropertyService propertyService;
    @Autowired
    PropertyGroupService propertyGroupService;
    @Autowired
    UserService userService;
    @Autowired
    ClientCodePropertyCodeMappingService clientCodePropertyCodeMappingService;

    @GlobalCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("globalCrudServiceBean")
    CrudService globalCrudService;

    @Autowired
    PacmanConfigParamsService configParamService;

    /**
     * Returns the default property from LDAP. If default property is not available then returns the first available property.
     */
    String getDefaultPropertyOrFirstAvailableProperty(TetrisPrincipal tp, String propertyGroupId) {
        String propertyId = null;
        //take value from LDAP if its null then get the first available authorized property
        propertyId = tp.getAttributes().get("defaultProperty");
        if (propertyId == null) {
            propertyId = getFirstAuthorisedProperty(Integer.parseInt(propertyGroupId));
        }
        return propertyId;
    }

    String getFirstAuthorisedProperty(Integer propertyGroupId) {
        PropertyGroup propertyGroup = globalCrudService.find(PropertyGroup.class, propertyGroupId);
        List<Property> properties = authService.retrieveAuthorizedProperties(propertyGroup.getUserId(), propertyGroup.getClientId());
        if (properties != null) {
            return properties.get(0).getId().toString();
        }
        return null;
    }

    /**
     * Returns validated work context, else will throw SecurityException(s),
     * if user lacks access to work context selections.
     */
    public boolean userHasAccess(TetrisPrincipal principal, WorkContextType target) {
        StopWatch timer = new StopWatch();
        timer.start();

        boolean hasAccess = false;
        if (target.getClientId() != null || target.getClientCode() != null) {
            WorkContextType clientContext =
                    workContextManager.createWorkContext(principal, target.getClientId(), target.getClientCode());
            if (clientContext != null) {
                hasAccess = true;
            }
        }
        if (target.getPropertyId() != null) {
            Property property = verifyPropertyAccess(principal, target.getPropertyId());
            hasAccess = property != null;
        }
        log.info("userHasAccess() - target: " + target + ", result: " + hasAccess);

        if (log.isDebugEnabled()) {
            log.debug("userHasAccess() - time: " + timer.getTime() + "ms");
        }
        return hasAccess;
    }

    public WorkContextType populateThreadLocal(HttpServletRequest request) {
        TetrisPrincipal tp = (TetrisPrincipal) request.getUserPrincipal();
        return populateThreadLocal(request, tp);
    }

    public WorkContextType populateThreadLocal(HttpServletRequest request, TetrisPrincipal tp) {
        if (tp == null) {
            log.warn("populateThreadLocal() - User principal is NULL");
            return null;
        }

        // Check URL params for context settings - if not there, check request headers

        String propertyGroupId = getParamOrHeader(request, PROPERTY_GROUP_ID);
        String propertyId = getRequestedPropertyId(request);
        String upsId = getRequestedUPSId(request);
        String propertyCode = getRequestedPropertyCode(request);
        String clientCode = getRequestedClientCode(request);
        String virtualPropertyCode = getParamOrHeader(request, VIRTUAL_PROPERTY_CODE);

        if (StringUtils.isNotEmpty(virtualPropertyCode)) {
            propertyCode = virtualPropertyCode;
        }

        log.debug("populateThreadLocal() - propertyGroupId: " + propertyGroupId + ", propertyId: " + propertyId);

        if(shouldUseUpsId(propertyId, upsId)){
            Integer propertyIdFromUpsId = globalCrudService.findByNamedQuerySingleResult(Property.GET_ID_BY_UNIFIED_PROPERTY_ID, QueryParameter.with(Property.PARAM_UNIFIED_PROPERTY_ID, upsId).parameters());
            if(null != propertyIdFromUpsId){
                propertyId = propertyIdFromUpsId.toString();
            }
        }

        //validate property group id with the threshold value from global param and if it doesn't validate then check
        //to see if other rest of the property group are valid else set it to NULL
        if (WorkContextManager.isValidParameter(propertyGroupId)) {
            String tempPropertyGroup = propertyGroupId;
            propertyGroupId = propertyGroupService.rectifyPropertyGroupId(propertyGroupId);
            if (propertyGroupId == null && propertyId == null) {
                propertyId = getDefaultPropertyOrFirstAvailableProperty(tp, tempPropertyGroup);
            }
        }

        // If propertyGroupId and propertyId aren't valid, return platform context
        if (!WorkContextManager.isValidParameter(propertyId) && !WorkContextManager.isValidParameter(propertyGroupId)
                && !(WorkContextManager.isValidParameter(clientCode) && WorkContextManager.isValidParameter(propertyCode))) {
            return populateWorkContextByClient(request, tp);
        }

        return createWorkContextFromPropertyGroupOrProperty(tp, propertyGroupId, propertyId, propertyCode, clientCode);
    }

    private static boolean shouldUseUpsId(String propertyId, String upsId) {
        return null == propertyId && null != upsId && WorkContextManager.isValidParameter(upsId);
    }

    public WorkContextType createWorkContextFromPropertyGroupOrProperty(TetrisPrincipal tp, String propertyGroupId, String propertyId, String propertyCode, String clientCode) {
        // We know that either propertyId or propertyGroupId is a valid parameter, therefore
        // we can create a workcontext object to match it
        WorkContextType workContext = new WorkContextType();
        workContext.setUserId(tp.getName());

        Client client = null;
        // If propertyGroupId is a valid parameter, check to see if the user has
        if (WorkContextManager.isValidParameter(propertyGroupId)) {
            client = populateClientFromPropertyGroup(tp, propertyGroupId, workContext, client);
        } else if (WorkContextManager.isValidParameter(propertyId)) {
            // If property is a valid parameter and there isn't a property group on the work context
            Property property = verifyPropertyAccess(tp, Integer.valueOf(propertyId));
            updateWorkContextWithPropertyValues(workContext, property);

            if (property != null) {
                client = property.getClient();
            }
        } else if (WorkContextManager.isValidParameter(clientCode) && WorkContextManager.isValidParameter(propertyCode)) {
            // If the clientCode/propertyCode are valid parameters and there isn't a property group on the work context, then use it to set the property
            Property property = verifyPropertyAccess(tp, clientCode, propertyCode);
            updateWorkContextWithPropertyValues(workContext, property);

            if (property != null) {
                client = property.getClient();
            }
        }
        updateWorkContextWithClientValues(workContext, client);

        // Set the work context on thread-local and return it
        PacmanWorkContextHelper.setWorkContext(workContext);
        return workContext;
    }

    public Client populateClientFromPropertyGroup(TetrisPrincipal tp, String propertyGroupId, WorkContextType workContext, Client client) {
        PropertyGroup propertyGroup = findPropertyGroup(propertyGroupId);

        if (propertyGroup == null) {
            log.warn("Property Group with ID: " + propertyGroupId + " was not found");

        } else {
            if (log.isDebugEnabled()) {
                log.debug("populateThreadLocal() - authService: " + (authService != null ? "NOT NULL" : "NULL")
                        + ", tp.id: " + tp.getId()
                        + ", pGroup.id: " + (propertyGroup.getId()));
            }
            client = populatePropertyGroupOnWorkContext(workContext, propertyGroup);
        }
        return client;
    }

    public WorkContextType populateWorkContextByClient(HttpServletRequest request, TetrisPrincipal tp) {
        log.debug("populateThreadLocal() - No property params supplied - attempt population by client");

        // Create a platform work context and set it on thread-local
        WorkContextType workContextType;
        if (request instanceof AuthRequestWrapper) {
            workContextType = workContextManager.createWorkContext(request);
            PacmanWorkContextHelper.setWorkContext(workContextType);
        } else {
            workContextType = workContextManager.createWorkContext(request, tp);
            PacmanWorkContextHelper.setWorkContext(workContextType);
        }

        return workContextType;
    }

    public String getRequestedPropertyId(HttpServletRequest request) {
        return getParamOrHeader(request, PROPERTY_ID);
    }

    public String getRequestedUPSId(HttpServletRequest request) {
        return getParamOrHeader(request, UPS_ID);
    }

    public String getRequestedClientCode(HttpServletRequest request) {
        return getParamOrHeader(request, CLIENT_CODE);
    }

    public String getRequestedPropertyCode(HttpServletRequest request) {
        return getParamOrHeader(request, PROPERTY_CODE);
    }

    //
    // Update supplied workContext with values found in supplied client
    // @throws
    //      TetrisSecurityException - if no client supplied
    //
    private WorkContextType updateWorkContextWithClientValues(WorkContextType workContext, Client client) {
        if (client != null) {
            workContext.setClientCode(client.getCode());
            workContext.setClientId(client.getId());
        } else {
            throw TetrisSecurityException.USER_LACKS_CLIENT_ACCESS;
        }
        return workContext;
    }

    private WorkContextType updateWorkContextWithPropertyValues(WorkContextType workContext, Property property) {
        if (workContext != null && property != null) {
            workContext.setPropertyId(property.getId());
            workContext.setPropertyCode(property.getCode());
        }
        return workContext;
    }

    //
    // Returns property found by propertyId
    // - if user lacks access, throws TetrisSecurityException
    //
    private Property verifyPropertyAccess(TetrisPrincipal tp, Integer propertyId) {
        Property property = findProperty(propertyId);
        if (property == null) {
            log.warn("Property with ID: " + propertyId + " was not found");
            throw createPropertyAccessException(propertyId);

        } else {
            // Check to see if the user has access - if so, populate client/property
            if (!authService.userHasAccessToProperty(tp, property)) {
                throw createPropertyAccessException(propertyId);
            }
        }
        return property;
    }


    private Property verifyPropertyAccess(TetrisPrincipal tp, String clientCode, String propertyCode) {
        Property property = findProperty(clientCode, propertyCode);
        if (property == null) {
            log.warn("Property with ClientCode: " + clientCode + " and Property Code: " + propertyCode + " was not found");
            log.info("checking in table G3_Ngi_ClientCode_PropertyCode_Mappings if any mapping exists for client_code, property_code combination");
            ClientProperty clientProperty = clientCodePropertyCodeMappingService.translateNGIClientPropertyCodes(clientCode, propertyCode);
            log.info("Mapping found as new clientCode = " + clientProperty.getClientCode() + " new propertyCode = " + clientProperty.getPropertyCode());
            property = findProperty(clientProperty.getClientCode(), clientProperty.getPropertyCode());
        }

        if (property == null) {
            log.warn("Property with ClientCode: " + clientCode + " and Property Code: " + propertyCode + " was not found");
            throw createPropertyAccessException(clientCode, propertyCode);

        } else {
            // Check to see if the user has access - if so, populate client/property
            if (!authService.userHasAccessToProperty(tp, property)) {
                throw createPropertyAccessException(clientCode, propertyCode);
            }
        }
        return property;
    }

    private TetrisSecurityException createPropertyAccessException(Integer propertyId) {
        return new TetrisSecurityException(TetrisSecurityException.USER_LACKS_PROPERTY_ACCESS + " Property ID: " + propertyId);
    }

    private TetrisSecurityException createPropertyAccessException(String clientCode, String propertyCode) {
        return new TetrisSecurityException(TetrisSecurityException.USER_LACKS_PROPERTY_ACCESS + " Client Code: " + clientCode + " Property Code: " + propertyCode);
    }

    /**
     * Check to see if the user has access - if so, populate client/propertyGroup
     *
     * @return Client associated with group, for which user has been authenticated
     */
    public Client populatePropertyGroupOnWorkContext(WorkContextType workContext, PropertyGroup propertyGroup) {
        Client client = null;

        PacmanWorkContextHelper.setWorkContext(workContext);
        if (authService.userHasAccessToPropertyGroup(propertyGroup)) {

            // Need to lookup the client to set the client info in the work context
            client = globalCrudService.find(Client.class, propertyGroup.getClientId());

            workContext.setPropertyGroupId(propertyGroup.getId());
            // NOTE:
            // If group has properties, will set property id to first in group
            // - Was seeing multi-tenant DB connections issues
            // - Also, things like system permissions are at property level - so
            //   some property selection is required till we come up with alternate logic

            List<Property> authorizedGroupProperties = authService.retrieveAuthorizedProperties(Integer.parseInt(workContext.getUserId()), client.getId());
            LDAPUser user = userService.getById(workContext.getUserId());

            String defaultPropertyId = user.getDefaultProperty();

            Property defaultPropertyInPreference = null;
            if (defaultPropertyId != null && !defaultPropertyId.isEmpty()) {
                defaultPropertyInPreference = findProperty(Integer.parseInt(defaultPropertyId));

                if (!authorizedGroupProperties.contains(defaultPropertyInPreference)) {
                    defaultPropertyInPreference = !authorizedGroupProperties.isEmpty() ? authorizedGroupProperties.get(0) : null;
                }

            } else {
                defaultPropertyInPreference = !authorizedGroupProperties.isEmpty() ? authorizedGroupProperties.get(0) : null;
            }
            if (defaultPropertyInPreference != null) {
                workContext.setPropertyId(defaultPropertyInPreference.getId());
                workContext.setPropertyCode(defaultPropertyInPreference.getCode());
            }
        } else {
            throw TetrisSecurityException.USER_LACKS_PROPERTYGROUP_ACCESS;
        }
        return client;
    }

    Property findProperty(Integer propertyId) {
        return propertyService.getPropertyById(propertyId);
    }

    Property findProperty(String clientCode, String propertyCode) {
        return propertyService.getPropertyByCode(clientCode, propertyCode);
    }

    PropertyGroup findPropertyGroup(String propertyGroupId) {
        try {
            return propertyGroupService.getPropertyGroupById(Integer.valueOf(propertyGroupId));
        } catch (NumberFormatException nfe) {
            log.warn("PropertyGroupId: " + propertyGroupId + " is not numeric");
            return null;
        }
    }

    String getParamOrHeader(HttpServletRequest request, String key) {
        String value = request.getParameter(key);
        log.debug("getParamOrHeader() - key: " + key + ", param: " + value);
        if (!WorkContextManager.isValidParameter(value)) {
            value = request.getHeader(key);
            log.debug("getParamOrHeader() - key: " + key + ", header: " + value);
        }
        return value;
    }

    public WorkContextType setDefaultProperty(TetrisPrincipal principal, WorkContextType workcontext) {

        String defaultPropertyId = principal.getAttributes().get("defaultProperty");

        //check to make sure we don't get back the "null" as a string.  If we do, turn into an actual null
        defaultPropertyId = "null".equalsIgnoreCase(defaultPropertyId) ? null : defaultPropertyId;

        Property property = getDefaultProperty(getActiveProperties(), defaultPropertyId);

        if (Log.isDebugEnabled()) {
            log.debug("setDefaultProperty() - principal.attributes.defaultProperty: " + defaultPropertyId);
        }

        if (property != null) {
            workcontext.setPropertyId(property.getId());
            workcontext.setPropertyCode(property.getCode());
            workcontext.setResetToDefaultProperty(false);
            workcontext.setPropertySFDCAccountNumber(property.getSfdcAccountNo());
            workcontext.setPropertyStage(property.getStage());
        } else {
            // You CAN have a user with no properties associated with it...
            log.warn("setDefaultProperty()  - User does not have an active property. User: " + workcontext.getUserId());
        }
        return workcontext;
    }

    Set<Property> getActiveProperties() {
        List<Property> userProperties = authService.retrieveAuthorizedProperties();
        Set<Property> activeProperties = new HashSet<Property>();

        log.debug("getActiveProperties(): userProperties.size()" + (userProperties != null ? userProperties.size() : "0"));

        for (Property property : userProperties) {
            if (property.isActive()) {
                activeProperties.add(property);
            }
        }
        return activeProperties;
    }

    /**
     * Fetch default settings for user, based on their client and preferences
     *
     * @return Default work context for specified Principal
     */
    public WorkContextType getDefaultWorkContext() {

        TetrisPrincipal tp = PacmanThreadLocalContextHolder.getPrincipal();
        if (tp == null) {
            log.warn("getDefaultWorkContext() - User principal is NULL");
            return null;
        }
        log.debug("");
        log.debug("getDefaultWorkContext() - tp.name: " + tp.getName());

        WorkContextType platformContext = workContextManager.getDefaultWorkContext(tp);

        //
        // Look out, folks downstream may still read off thread local, such as auth checks!
        //
        PlatformThreadLocalContextHolder.setWorkContext(platformContext);

        log.debug("getDefaultWorkContext() - default platformWorkContext: " + platformContext);

        if (platformContext == null) {
            log.warn("No platform work context available");
        } else {
            setDefaultProperty(tp, platformContext);
            //set default property group
            log.debug("\n\n ********* Before setDefaultPropertyGroupId \n\n");

            setDefaultPropertyGroup(tp, platformContext);
        }

        log.debug("\n\n ********* End of Default WorkContext");
        return platformContext;
    }

    void setDefaultPropertyGroup(TetrisPrincipal tp, WorkContextType workContext) {
        Integer propertyGroupId = getDefaultPropertyGroupId(tp);
        log.debug("\n\n ==== FRom setDefaultPropertyGroupId  propertyGroupId == " + propertyGroupId);
        if (propertyGroupId != null) {

            //get number of properties for property group
            Long numberOfActualProperties = (Long) globalCrudService.getEntityManager().createQuery("SELECT COUNT(ppg.id) FROM PropertyPropertyGroup ppg WHERE ppg.propertyGroup.id=:id")
                    .setParameter("id", propertyGroupId)
                    .getSingleResult();

            String globalParamValue = configParamService.getParameterValueByClientLevel(GUIConfigParamName.CORE_PROPERTY_MAX_PROPERTY_PER_PROPERTY_GROUP.value(), workContext.getClientCode());
            boolean isValidGroup = false;
            if (globalParamValue != null) {
                Integer maxPropertyPerPropertyGrp = Integer.parseInt(globalParamValue);
                if (numberOfActualProperties <= maxPropertyPerPropertyGrp && numberOfActualProperties > 0) {
                    isValidGroup = true;
                } else {
                    isValidGroup = false;
                }
            }
            log.debug("\n\n ==== FRom setDefaultPropertyGroupId  isValidGroup == " + isValidGroup);
            //code for validating property group as per threshold value set in global param here...
            if (isValidGroup) {
                workContext.setPropertyGroupId(propertyGroupId);
            }
        }
    }


    /**
     * Fetch default settings for user, based on provided client and preferences
     *
     * @return Default work context for specified Principal
     */
    public WorkContextType getDefaultWorkContext(Client client) {

        TetrisPrincipal tp = PacmanThreadLocalContextHolder.getPrincipal();
        if (tp == null) {
            log.warn("getDefaultWorkContext() - User principal is NULL");
            return null;
        }

        log.debug("");
        log.debug("getDefaultWorkContext() - tp.name: " + tp.getName());

        WorkContextType workContext = createPlatformContextPlaceOnThreadLocalAndReturnAsPacmanContext_yeeHaw(tp, client);

        if (workContext == null) {
            log.warn("No platform work context available");
        } else {
            setDefaultProperty(tp, workContext);
        }

        return workContext;
    }

    WorkContextType createPlatformContextPlaceOnThreadLocalAndReturnAsPacmanContext_yeeHaw(
            TetrisPrincipal tp, Client client) {

        WorkContextType platformContext = createDefaultPlatformWorkContextAndPlaceOnThreadLocal(tp, client);

        log.debug("createPlatformContextPlaceOnThreadLocalAndReturnAsPacmanContext_yeeHaw() - default platformWorkContext: " + platformContext);

        return platformContext;
    }

    //
    // Look out, folks downstream may still read off thread local, such as auth checks!
    //
    WorkContextType createDefaultPlatformWorkContextAndPlaceOnThreadLocal(TetrisPrincipal tp, Client client) {

        WorkContextType platformContext = createDefaultPlatformWorkContext(tp, client);

        //
        // Look out, folks downstream may still read off thread local, such as auth checks!
        //
        PlatformThreadLocalContextHolder.setWorkContext(platformContext);
        return platformContext;
    }

    WorkContextType createDefaultPlatformWorkContext(TetrisPrincipal tp, Client client) {

        return workContextManager.getDefaultWorkContext(tp, client.getId(), client.getCode());
    }

    /**
     * Fetch default settings for user, based on provided property
     *
     * @return Default work context for specified Principal, by property
     */
    public WorkContextType getDefaultWorkContext(Property property) {

        TetrisPrincipal tp = PacmanThreadLocalContextHolder.getPrincipal();
        if (tp == null) {
            log.warn("getDefaultWorkContext(ByPropertyId) - User principal is NULL");
            return null;
        }
        Integer propertyId = property.getId();
        log.debug("getDefaultWorkContext(ByProperty) - p.id: " + propertyId);

        if (!tp.isInternalUser() && !tp.getOrganization().equalsIgnoreCase(property.getClient().getCode())) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, "Only internal users can access multiple clients");
        }
        //Must setup thread local before service calls
        createDefaultPlatformWorkContextAndPlaceOnThreadLocal(tp, property.getClient());

        if (authService.userHasAccessToProperty(property.getId())) {
            WorkContextType workContext = PlatformThreadLocalContextHolder.getWorkContext();
            workContext.setPropertyCode(property.getCode());
            workContext.setPropertyId(property.getId());
            workContext.setPropertyStage(property.getStage());
            return workContext;

        } else {
            throw createPropertyAccessException(propertyId);
        }
    }

    Property getDefaultProperty(Set<Property> activeProperties, String defaultProperty) {

        if (log.isDebugEnabled()) {
            log.debug("getDefaultProperty() - activeProperties.size(): " + activeProperties.size() + ", defaultProperty: " + defaultProperty);
        }

        if (activeProperties.isEmpty()) {
            log.warn("getDefaultProperty(): There are no active properties");
            return null;
        }

        if (isDigits(defaultProperty)) {
            int defaultId = Integer.valueOf(defaultProperty);

            for (Property property : activeProperties) {
                if (property.getId() == defaultId) {
                    return property;
                }
            }
            // since the default property is not in the list of active properties:
            log.warn(generateLogMessage(activeProperties, defaultId));
        }

        // (return the first in the list since we don't have a default property - it could be any of them)
        return activeProperties.iterator().next();
    }


    /*
     * if defaultPropertyOrGroup is GROUP - we can set the id, otherwise leave it NULL
     */
    Integer getDefaultPropertyGroupId(TetrisPrincipal principal) {
        StopWatch timer = new StopWatch();
        timer.start();

        /// start work
        Integer id = null;
        LDAPUser user = userService.getById(principal.getId());

        String propertyOrGroup = user.getDefaultPropertyOrGroup();
        boolean usePropertyGroup = StringUtils.equalsIgnoreCase("Group", propertyOrGroup);

        if (log.isDebugEnabled()) {
            log.debug("****** getDefaultPropertyGroupId *** propertyOrGroup: " + propertyOrGroup + " **** usePropertyGroup: " + usePropertyGroup);
        }

        String defaultPropertyGroupId = user.getDefaultPropertyGroup();

        if (log.isDebugEnabled()) {
            log.debug("****** getDefaultPropertyGroupId *** defaultPropertyGroupId: " + defaultPropertyGroupId);
        }

        if (isDigits(defaultPropertyGroupId)) {
            id = Integer.valueOf(defaultPropertyGroupId);
        }

        if (log.isDebugEnabled()) {
            log.debug("getDefaultPropertyGroupId() - found: " + id + ", time: " + timer.getTime() + "ms");
        }

        if (usePropertyGroup) {
            return id;
        }

        return null;
    }

    /*
     * Our default property was not active
     */
    String generateLogMessage(Set<Property> activeProperties, int defaultPropertyId) {
        StringBuilder msg = new StringBuilder("The default property id: ");
        msg.append(defaultPropertyId);
        msg.append(" is not in the list of active properties! [");

        Iterator<Property> active = activeProperties.iterator();
        while (active.hasNext()) {
            Property property = active.next();
            msg.append(property.getId());
            if (active.hasNext()) {
                msg.append(", ");
            }
        }
        msg.append("]");
        return msg.toString();
    }

    public void setWorkContextManager(WorkContextManager workContextManager) {
        this.workContextManager = workContextManager;
    }

    public void setPropertyGroupService(PropertyGroupService propertyGroupService) {
        this.propertyGroupService = propertyGroupService;
    }

    public void setAuthService(AuthorizationService authService) {
        this.authService = authService;
    }

    public void setGlobalCrudService(CrudService globalCrudService) {
        this.globalCrudService = globalCrudService;
    }

    public void setPropertyService(PropertyService propertyService) {
        this.propertyService = propertyService;
    }

    public void setClientCodePropertyCodeMappingService(ClientCodePropertyCodeMappingService clientCodePropertyCodeMappingService) {
        this.clientCodePropertyCodeMappingService = clientCodePropertyCodeMappingService;
    }

    public void setUserServiceLocal(UserService userService) {
        this.userService = userService;
    }

}
