package com.ideas.tetris.pacman.services.saspopulation.service.decisions;

import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.demandoverride.entity.LastRoomValueAccomType;
import com.ideas.tetris.pacman.services.saspopulation.service.dtos.PaceLrvAtBatchDto;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.apache.commons.csv.CSVParser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

import static org.apache.commons.collections.CollectionUtils.isNotEmpty;

@Component
@Transactional
@Qualifier("PaceLrvAtPopulationService")
public class PaceLrvAtPopulationService extends DecisionPopulationService {
    @Autowired
	private AccommodationService accommodationService;

    @Override
    public void populate(CSVParser csvRecords) {
        deleteDecisionsAgainstInactiveAccomTypes();
        Date currentDate = DateUtil.getCurrentDate();
        populate(csvRecords, csvRecord -> PaceLrvAtBatchDto.mapToPaceLrvAtBatchDto(csvRecord, currentDate),
                PaceLrvAtBatchDto.USP_PACE_LRV_AT_UPSERT, PaceLrvAtBatchDto.getBatchSize());
    }

    private void deleteDecisionsAgainstInactiveAccomTypes() {
        List<Integer> inactiveRoomTypeIds = accommodationService.getInactiveRoomTypes();
        if (isNotEmpty(inactiveRoomTypeIds)) {
            tenantCrudService.executeUpdateByNamedQuery(LastRoomValueAccomType.DELETE_BY_AT_IDS,
                    QueryParameter.with("accomTypeIds", inactiveRoomTypeIds).parameters());
        }
    }

}