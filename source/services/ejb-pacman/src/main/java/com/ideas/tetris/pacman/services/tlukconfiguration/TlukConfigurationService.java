package com.ideas.tetris.pacman.services.tlukconfiguration;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.client.service.ClientService;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang.StringUtils;

import javax.inject.Inject;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;


@Component
@Transactional
public class TlukConfigurationService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService tenantCrudService;

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    @Autowired
    ClientService clientService;


    public void configure() {
        addMS();
        pacmanConfigParamsService.addParameterValue(getContext(), FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value(), "true");
    }

    private String getContext() {
        return Constants.CONFIG_PARAMS_NODE_PREFIX + "." + PacmanWorkContextHelper.getClientCode() + "." + PacmanWorkContextHelper.getPropertyCode();
    }


    private void addMS() {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append(addMSQuery("BAR"));
        queryBuilder.append(addMSQuery("CORP_DEF"));
        queryBuilder.append(addMSQuery("CORP_QS"));
        queryBuilder.append(addMSQuery("CORP_QYL"));
        queryBuilder.append(addMSQuery("DISC1"));
        queryBuilder.append(addMSQuery("DISC2"));
        queryBuilder.append(addMSQuery("DISC3"));
        queryBuilder.append(addMSQuery("GRP1"));
        queryBuilder.append(addMSQuery("GRPD"));
        queryBuilder.append(addMSQuery("OTA"));
        queryBuilder.append(addMSQuery("PROM"));
        queryBuilder.append(addMSQuery("SALH"));
        queryBuilder.append(addMSQuery("SALL"));
        queryBuilder.append(addMSQuery("WHOL"));
        queryBuilder.append(addMSQuery("WREQ"));

        tenantCrudService.executeUpdateByNativeQuery(queryBuilder.toString(), QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    private String addMSQuery(String ms) {
        return String.format("if not exists(select 1 from Mkt_Seg where Mkt_Seg_Code = '%s')\n" +
                "INSERT INTO [dbo].[Mkt_Seg]([Property_ID] ,[Mkt_Seg_Code]  ,[Mkt_Seg_Name]  ,[Mkt_Seg_Description] ,[Status_ID],[Last_Updated_DTTM],[Is_Editable],[Created_By_User_ID],[Created_DTTM],[Last_Updated_By_User_ID]) VALUES (:propertyId,'%s','%s','%s',1,GETDATE(),0,11403,GETDATE(),11403)\n ", ms, ms, ms, ms);

    }

    public boolean isPropertyTlukAndLdb(String ldb) {
        return clientService.isTLUK() && ldb != null && StringUtils.equalsIgnoreCase(Constants.TRUE, ldb);
    }
}
