package com.ideas.tetris.pacman.integration.ratchet.services.continuouspricing;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.integration.ratchet.services.continuouspricing.dto.HiltonCPDay;
import com.ideas.tetris.pacman.integration.ratchet.services.continuouspricing.dto.HiltonCPFile;
import com.ideas.tetris.pacman.integration.ratchet.services.continuouspricing.dto.HiltonCPSeason;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesDecisionsSentBy;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesOffsetMethod;
import com.ideas.tetris.pacman.services.configsparam.service.ConfigParameterNameService;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dailybar.constants.DailyBarConstants;
import com.ideas.tetris.pacman.services.datafeed.service.DecisionConfigurationService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.decisiondelivery.entity.DecisionUploadDateToExternalSystemRepository;
import com.ideas.tetris.pacman.services.decisiontranslator.HiltonDecisionService;
import com.ideas.tetris.pacman.services.inventorylimit.VirtualPropertyInventoryLimitDecisionService;
import com.ideas.tetris.pacman.services.ngi.dto.DailyBar;
import com.ideas.tetris.pacman.services.ngi.dto.RateType;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.roomtypemapping.HiltonDailyBARVendorRoomTypeTranslator;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.util.Runner.runIfTrue;
import static com.ideas.tetris.platform.common.externalsystem.ExternalSystem.HILSTAR;
import static com.ideas.tetris.platform.common.externalsystem.ExternalSystem.PCRS;
import static java.math.BigInteger.valueOf;
import static java.util.stream.Collectors.*;

@Component
@Transactional
public class HiltonCpDailyBarDecisionService {
    private static final Logger LOGGER = Logger.getLogger(HiltonCpDailyBarDecisionService.class.getName());
    private static final String MEAL_PLAN_CODE = "N";
    private static final BigDecimal DUMMY_VALUE = BigDecimal.valueOf(99999.00).setScale(2);
    private static final BigDecimal DEFAULT_VALUE = BigDecimal.valueOf(0.00).setScale(2);
    private static final int INDEX_OCCUPANCY_DATE = 0;
    private static final int INDEX_ROOM_TYPE = 1;
    private static final int INDEX_SINGLE_RATE = 2;
    private static final int INDEX_DOUBLE_RATE = 3;
    private static final int INDEX_ADULT_RATE = 4;
    private static final int INDEX_CHILD_RATE = 5;
    private static final int INDEX_CHILD_AGE_RATE_ONE = 6;
    private static final int INDEX_CHILD_AGE_RATE_TWO = 7;
    private static final int INDEX_CHILD_AGE_RATE_THREE = 8;
    private static final int INDEX_PRODUCT_ID = 9;
    private static final int INDEX_TRIPLE_RATE = 10;
    private static final int INDEX_QUAD_RATE = 11;
    private static final String EXTERNAL_SYSTEM_HILSTAR = "HILSTAR";
    private static final String EXTERNAL_SYSTEM_PCRS = "PCRS";
    public static final String DECISION_START_DATE = "decisionStartDate";
    public static final String DECISION_END_DATE = "decisionEndDate";

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService crudService;
    @Autowired
    HiltonDecisionService hiltonDecisionService;
    @Autowired
    PacmanConfigParamsService configParamsService;
    @Autowired
    DateService dateService;
    @Autowired
    DecisionService decisionService;
    @Autowired
    DecisionUploadDateToExternalSystemRepository decisionUploadDateToExternalSystemRepository;
    @Autowired
    HiltonDailyBARVendorRoomTypeTranslator hiltonDailyBARVendorRoomTypeTranslator;
    @Autowired
    ConfigParameterNameService configParameterNameService;
    @Autowired
	private DecisionConfigurationService decisionConfigurationService;
    @Autowired
    private VirtualPropertyInventoryLimitDecisionService virtualPropertyInventoryLimitDecisionService;


    public HiltonCPFile getCpDailyBarDecisionsFile(String clientCode,
                                                   String propertyCode,
                                                   String externalSystem,
                                                   String operationType) {
        LOGGER.info("Generating CP DailyBAR decisions file for client:property = " + clientCode + ":" + propertyCode);
        externalSystem = validateExternalSystem(externalSystem);
        HiltonCPFile hiltonCPFile = initializeCPFile(externalSystem, propertyCode);
        List<Object[]> data = getDailyBarDecisionData(externalSystem, operationType);
        hydrateCPFile(hiltonCPFile, data);
        fillMissingDays(hiltonCPFile);
        return hiltonCPFile;
    }

    @SuppressWarnings("squid:S3776")
    public List<DailyBar> getCpDailyBarDecisions(Date startDate, Date endDate, Date lastUploadDate,
                                                 String operationType, String externalSystem, HiltonNewGenParameters hiltonNewGenParameters) {
        String clientCode = PacmanWorkContextHelper.getClientCode();
        String propertyCode = PacmanWorkContextHelper.getPropertyCode();
        boolean uploadChildAgeBuckets = isIntegrationParameterEnabled(externalSystem, IntegrationConfigParamName.UPLOAD_CHILD_AGE_BUCKETS);
        boolean uploadExtraChild = isIntegrationParameterEnabled(externalSystem, IntegrationConfigParamName.UPLOAD_EXTRA_CHILD);
        boolean perPersonPricingEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED);
        boolean shouldSendAdjustment = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED);
        boolean isHiltonUpdateValuesSentForExtraAdultExtraChild = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.HILTON_OPTION_TO_UPDATE_VALUES_SENT_FOR_EXTRA_ADULT_EXTRA_CHILD);
        List<Object[]> data = getDailyBarDecisionData(startDate, endDate, lastUploadDate, externalSystem, operationType, hiltonNewGenParameters.isStreaming());
        List<DailyBar> dailyBarDecisions = new ArrayList<>();
        final boolean isFreeNightEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_CONSORTIA_FREE_NIGHT_ENABLED);
        final List<Integer> fnProducts = new ArrayList<>();
        if (isFreeNightEnabled) {
            fnProducts.addAll(getActiveFreeNightProducts());
        }
        String srpCode = getConfiguredSrpCode();
        Map<Integer, Product> uploadableProducts = getProductIdNameMapping(srpCode);
        final boolean isVirtualProperty = hiltonNewGenParameters.isVirtualProperty();
        Map<String, String> rateCodeByPropertyCode = getRateCodesByPhysicalPropertyCode(isVirtualProperty);
        for (Object[] row : data) {
            DailyBar decision = new DailyBar();
            Product product = uploadableProducts.get(((BigInteger) row[INDEX_PRODUCT_ID]).intValue());
            setMetadataToDecision(clientCode, propertyCode, row, decision, product, hiltonNewGenParameters.getCorrelationId(), isVirtualProperty, rateCodeByPropertyCode);
            boolean isRateAdjustment = isAdjustmentDecision(shouldSendAdjustment, product);
            boolean isAdultRateAdjustment = isAdjustmentForAdult(isHiltonUpdateValuesSentForExtraAdultExtraChild, product, isRateAdjustment);
            boolean isChildRateAdjustment = isAdjustmentForChild(isHiltonUpdateValuesSentForExtraAdultExtraChild, product, isRateAdjustment);
            BigDecimal singleRate = getBigDecimal(row[INDEX_SINGLE_RATE], isRateAdjustment);
            BigDecimal doubleRate = getBigDecimal(row[INDEX_DOUBLE_RATE], isRateAdjustment);
            BigDecimal tripleRate = getBigDecimal(row[INDEX_TRIPLE_RATE], isRateAdjustment);
            BigDecimal quadRate = getBigDecimal(row[INDEX_QUAD_RATE], isRateAdjustment);
            BigDecimal addAdultRate = getBigDecimal(row[INDEX_ADULT_RATE], isAdultRateAdjustment);
            BigDecimal addChildRate = getBigDecimal(row[INDEX_CHILD_RATE], isChildRateAdjustment);
            if (isRateAdjustment) {
                BigDecimal rateWithSign = getBigDecimal(row[INDEX_SINGLE_RATE]);
                decision.setRateType(findRateType(rateWithSign, product.getOffsetMethod()));
                setAbsoluteRateToDecision(decision, singleRate, doubleRate, tripleRate, quadRate);
            } else {
                decision.setSingleRate(singleRate == null ? DUMMY_VALUE : singleRate);
                decision.setDoubleRate(doubleRate == null ? DUMMY_VALUE : doubleRate);
                setTripleQuadRate(decision, doubleRate, addAdultRate, perPersonPricingEnabled, tripleRate, quadRate,
                        fnProducts.contains(((BigInteger) row[INDEX_PRODUCT_ID]).intValue()));
            }
            setAdditionalRates(externalSystem, hiltonNewGenParameters.isStreaming(), decision, addAdultRate, addChildRate, row, uploadChildAgeBuckets, uploadExtraChild);
            runIfTrue(isVirtualProperty, () -> setVirtualPropertyMetadata(hiltonNewGenParameters.getCorrelationId(), decision, hiltonNewGenParameters.getPhysicalPropertyCodes()));
            dailyBarDecisions.add(decision);
        }
        translateVendorAccomTypeRateCode(externalSystem, dailyBarDecisions, srpCode);
        return dailyBarDecisions;
    }

    private Map<String, String> getRateCodesByPhysicalPropertyCode(boolean isVirtualProperty) {
        return isVirtualProperty && isSmallGroupProductEnabled() ?
                virtualPropertyInventoryLimitDecisionService.getRateCodesByPhysicalPropertyCode() : Collections.emptyMap();
    }

    private BigDecimal getBigDecimal(Object o, boolean isAdjustmentValue) {
        return isAdjustmentValue ? absoluteValue(o) : zeroAdjustedValue(o);
    }

    private BigDecimal zeroAdjustedValue(Object o) {
        BigDecimal value = getBigDecimal(o);
        if (value == null) {
            return null;
        }
        return value.signum() < 0 ? BigDecimal.ZERO : value;
    }

    private BigDecimal absoluteValue(Object o) {
        BigDecimal value = getBigDecimal(o);
        return value != null ? value.abs() : null;
    }

    private boolean isAdjustmentForAdult(boolean isHiltonUpdateValuesSentForExtraAdultExtraChild, Product product, boolean shouldSendAdjustment) {
        return shouldSendAdjustment && isHiltonUpdateValuesSentForExtraAdultExtraChild && !product.isSystemDefault() && product.isOffsetForExtraAdult();
    }

    private boolean isAdjustmentForChild(boolean isHiltonUpdateValuesSentForExtraAdultExtraChild, Product product, boolean shouldSendAdjustment) {
        return shouldSendAdjustment && isHiltonUpdateValuesSentForExtraAdultExtraChild && !product.isSystemDefault() && product.isOffsetForExtraChild();
    }

    private boolean isAdjustmentDecision(boolean shouldSendAdjustment, Product product) {
        return shouldSendAdjustment && !product.isSystemDefault() && AgileRatesDecisionsSentBy.ADJUSTMENT.equals(product.getDecisionsSentBy());
    }

    private void setVirtualPropertyMetadata(String correlationId, DailyBar decision, Set<String> physicalPropertyCodes) {
        try {
            final String[] split = decision.getRoomTypeCode().split("_", 2);
            decision.setPropertyCode(split[0]);
            decision.setRoomTypeCode(split[1]);
            decision.setCorrelationId(String.format("%s_%s", correlationId, split[0]));
            physicalPropertyCodes.add(split[0]);
        } catch (ArrayIndexOutOfBoundsException e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, String.format("Virtual Property Decision :: Room Type %s Does not have inncode", decision.getRoomTypeCode()), e);
        }
    }

    protected void translateVendorAccomTypeRateCode(String externalSystem, List<DailyBar> dailyBarDecisions, String srpCode) {
        Map<String, List<DailyBar>> accomTypeToDailyBARProductMap = dailyBarDecisions.stream().filter(dailyBar -> StringUtils.equalsIgnoreCase(srpCode, dailyBar.getRateCode())).collect(groupingBy(DailyBar::getRoomTypeCode, HashMap::new, toCollection(ArrayList::new)));
        runIfTrue(MapUtils.isNotEmpty(accomTypeToDailyBARProductMap), () -> dailyBarDecisions.addAll(hiltonDailyBARVendorRoomTypeTranslator.translateVendorRoomTypesForHiltonDailyBAR(accomTypeToDailyBARProductMap, externalSystem)));
    }

    private void setMetadataToDecision(String clientCode, String propertyCode, Object[] row, DailyBar decision,
                                       Product product, String correlationId, boolean isVirtualProperty,
                                       Map<String, String> rateCodeByPropertyCode) {
        decision.setClientCode(clientCode);
        decision.setPropertyCode(propertyCode);
        decision.setOccupancyDate((Date) row[INDEX_OCCUPANCY_DATE]);
        decision.setRoomTypeCode((String) row[INDEX_ROOM_TYPE]);
        decision.setRateCode(getRateCodeName(product, isVirtualProperty, decision.getRoomTypeCode(), rateCodeByPropertyCode));
        decision.setCorrelationId(correlationId);
    }

    private String getRateCodeName(Product product, boolean isVirtualProperty, String roomTypeCode, Map<String, String> rateCodeByPropertyCode) {
        if (isVirtualProperty && product.isGroupProduct() && isSmallGroupProductEnabled()) {
            String physicalPropertyCode = getPhysicalPropertyCode(roomTypeCode);
            if (rateCodeByPropertyCode.containsKey(physicalPropertyCode)) {
                return getRateCodeName(rateCodeByPropertyCode, physicalPropertyCode);
            }
        }

        String rateCodeName = product.getName();
        if (isVirtualProperty && StringUtils.contains(rateCodeName, "_")) {
            rateCodeName = rateCodeName.split("_", 2)[1];
        }
        return rateCodeName;
    }

    private boolean isSmallGroupProductEnabled() {
        return configParamsService.getBooleanParameterValue(PreProductionConfigParamName.SMALL_GROUP_PRODUCT_ENABLED);
    }

    private static String getRateCodeName(Map<String, String> rateCodeByPropertyCode,
                                          String physicalPropertyCode) {
        String rateCode = rateCodeByPropertyCode.get(physicalPropertyCode);
        if (rateCode.equals(StringUtils.EMPTY)) {
            throw new TetrisException(String.format("Rate code for Virtual Physical Property Code: %s is Empty", physicalPropertyCode));
        }

        return rateCode;
    }

    private static String getPhysicalPropertyCode(String roomTypeCode) {
        String[] split = roomTypeCode.split("_", 2);
        return split[0];
    }

    private void setAbsoluteRateToDecision(DailyBar decision, BigDecimal singleRate, BigDecimal doubleRate, BigDecimal tripleRate, BigDecimal quadRate) {
        decision.setSingleRate(singleRate);
        decision.setDoubleRate(doubleRate);
        decision.setTripleRate(tripleRate == null ? singleRate : tripleRate);
        decision.setQuadRate(quadRate == null ? singleRate : quadRate);
    }

    private RateType findRateType(BigDecimal rateValue, AgileRatesOffsetMethod offsetMethod) {
        switch (offsetMethod) {
            case FIXED:
                return rateValue.signum() <= 0 ? RateType.AMOUNTOFFRACK : RateType.AMOUNTADDRACK;
            case PERCENTAGE:
                return rateValue.signum() <= 0 ? RateType.PERCENTOFFRACK : RateType.PERCENTADDRACK;
            default:
                return RateType.RACK;
        }
    }

    private void setTripleQuadRate(DailyBar decision, BigDecimal doubleRate, BigDecimal addAdultRate, boolean perPersonPricingEnabled, BigDecimal tipleRate, BigDecimal quadRate, boolean isFreeNightProduct) {
        if (perPersonPricingEnabled || isFreeNightProduct) {
            calculateTripleQuadeRate(decision, doubleRate, addAdultRate, tipleRate, quadRate);
        } else {
            decision.setTripleRate(doubleRate == null || addAdultRate == null ? DUMMY_VALUE : doubleRate.add(addAdultRate));  // Hilton specification: 3 person rate = 2 person + extra person
            decision.setQuadRate(doubleRate == null || addAdultRate == null ? DUMMY_VALUE : doubleRate.add(addAdultRate).add(addAdultRate));  // Hilton specification: 4 person rate = 2 person + (2*extra person)
        }
    }

    private void calculateTripleQuadeRate(DailyBar decision, BigDecimal doubleRate, BigDecimal addAdultRate, BigDecimal tripleRate, BigDecimal quadRate) {
        decision.setTripleRate(tripleRate != null ? tripleRate : (doubleRate == null || addAdultRate == null ? DUMMY_VALUE : doubleRate.add(addAdultRate)));
        decision.setQuadRate(quadRate != null ? quadRate : (doubleRate == null || addAdultRate == null ? DUMMY_VALUE : doubleRate.add(addAdultRate).add(addAdultRate)));
    }

    protected Map<Integer, Product> getProductIdNameMapping(String srpCode) {
        List<Product> products = crudService.findByNamedQuery(Product.GET_SYSTEM_DEFAULT_OR_ACTIVE_UPLOAD_ENABLED_PRODUCTS);
        products.stream().filter(Product::isSystemDefault).findFirst().ifPresent(product -> product.setName(srpCode.trim()));
        return products.stream().collect(Collectors.toMap(Product::getId, Product::new));
    }

    private void setAdditionalRates(String externalSystem, boolean isStreaming, DailyBar decision, BigDecimal addAdultRate, BigDecimal addChildRate,
                                    Object[] row, boolean uploadChildAgeBuckets, boolean uploadExtraChild) {
        if (isStreaming) {
            decision.setAdditionalAdultRate(addAdultRate == null ? DEFAULT_VALUE : addAdultRate);
            setAdditionalChildRate(decision, addChildRate, uploadExtraChild);
            setChildAgeBucketValues(decision, row, uploadChildAgeBuckets);
        } else {
            if ("HCRS".equalsIgnoreCase(externalSystem)) {
                decision.setAdditionalAdultRate(addAdultRate == null ? DUMMY_VALUE : addAdultRate);
                decision.setAdditionalChildRate(addChildRate == null ? DUMMY_VALUE : addChildRate);
            } else if ("PCRS".equalsIgnoreCase(externalSystem)) {
                decision.setAdditionalAdultRate(null);
                decision.setAdditionalChildRate(null);
            }
        }
    }

    private void setAdditionalChildRate(DailyBar decision, BigDecimal addChildRate, boolean uploadExtraChild) {
        if (uploadExtraChild) {
            decision.setAdditionalChildRate(addChildRate == null ? DEFAULT_VALUE : addChildRate);
        }
    }

    private void setChildAgeBucketValues(DailyBar decision, Object[] row, boolean uploadChildAgeBuckets) {
        if (uploadChildAgeBuckets) {
            decision.setChildOneRate(getBigDecimal(row[INDEX_CHILD_AGE_RATE_ONE]));
            decision.setChildTwoRate(getBigDecimal(row[INDEX_CHILD_AGE_RATE_TWO]));
            decision.setChildThreeRate(getBigDecimal(row[INDEX_CHILD_AGE_RATE_THREE]));
        }
    }

    private String validateExternalSystem(String externalSystem) {
        if (HILSTAR.getCode().equalsIgnoreCase(externalSystem)) {
            return EXTERNAL_SYSTEM_HILSTAR;
        }
        if (PCRS.getCode().equalsIgnoreCase(externalSystem)) {
            return EXTERNAL_SYSTEM_PCRS;
        }
        throw new TetrisException(ErrorCode.EXTERNAL_SYSTEM_INVALID, "Required: " + PCRS.getCode() + " or " + HILSTAR.getCode() + ". Found: " + externalSystem + ".");
    }

    private HiltonCPFile initializeCPFile(String externalSystem, String propertyCode) {
        HiltonCPFile hiltonCPFile = new HiltonCPFile();
        hiltonCPFile.setPropCode(propertyCode);
        hiltonCPFile.setCrs(PCRS.getCode().equals(externalSystem) ? "PCRS" : "HCRS");
        hiltonCPFile.setSrpCode(getConfiguredSrpCode());
        hiltonCPFile.setTimeStamp(ZonedDateTime.now().withZoneSameInstant(ZoneId.of("America/Chicago")));
        return hiltonCPFile;
    }

    private String getConfiguredSrpCode() {
        return configParamsService.getParameterValue(IntegrationConfigParamName.HILTON_CP_SRP_NAME);
    }

    private boolean isAgileRatesEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED);
    }

    private boolean isIntegrationParameterEnabled(String externalSystem, IntegrationConfigParamName uploadParameter) {
        return configParamsService.getBooleanParameterValue(uploadParameter.value(externalSystem), PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode());
    }

    private List<Object[]> getDailyBarDecisionData(Date startDate, Date endDate, Date lastUploadDate,
                                                   String externalSystem, String operationType, boolean isStreaming) {
        if (lastUploadDate == null || isForceFullDecisionsEnabled(operationType)) {
            // Full
            return getDecisionDataFull(startDate, endDate, externalSystem);
        } else {
            // Differential
            return getDecisionDataDifferential(startDate, endDate, lastUploadDate, externalSystem, isStreaming);
        }
    }

    private List<Object[]> getDailyBarDecisionData(String externalSystem, String operationType) {
        Date startDate = dateService.getOptimizationWindowStartDate();
        Date endDate = dateService.getDecisionUploadWindowEndDate();
        Date lastUploadDate = getLastUploadDate(externalSystem);
        // as streaming is false I think this can be removed
        return getDailyBarDecisionData(startDate, endDate, lastUploadDate, externalSystem, operationType, false);
    }

    private Date getLastUploadDate(String externalSystem) {
        return hiltonDecisionService.getLastUploadDate(Constants.DAILYBAR, externalSystem, false);
    }

    private boolean isForceFullDecisionsEnabled(String operationType) {
        return StringUtils.equalsIgnoreCase(Constants.BDE, operationType) && decisionConfigurationService.isPropertyScheduledForFullDecisions(PacmanWorkContextHelper.getPropertyId());
    }

    private List<Object[]> getDecisionDataDifferential(Date startDate, Date endDate, Date lastUploadedDate, String externalSystem, boolean isStreaming) {
        if (null == lastUploadedDate || null == startDate || null == endDate) {
            throw new TetrisException(ErrorCode.NULL_NOT_ALLOWED, "Unable to generate differential CP decisions for start=" + startDate + ", end=" + endDate + ", lastUpload=" + lastUploadedDate);
        }
        List<Object[]> data = getDecisionDailyBarOutputDifferential(startDate, endDate, lastUploadedDate, externalSystem);
        if (!isStreaming) {
            // not streaming
            data.addAll(getFillInDataByWeek(startDate, endDate, data, externalSystem));
        }
        data.sort(Comparator.comparing(row -> (Date) row[INDEX_OCCUPANCY_DATE]));
        return data;
    }

    private List<Object[]> getDecisionDataFull(Date startDate, Date endDate, String externalSystem) {
        LOGGER.info("Fetching FULL Dailybar Decisions for date range (" + startDate + ", " + endDate + ")");
        return crudService.findByNativeQuery(DailyBarConstants.getHiltonCPFileDecisionsFull(getRounding(externalSystem)),
                QueryParameter.with(DECISION_START_DATE, startDate).and(DECISION_END_DATE, endDate).parameters());
    }

    private List<Integer> getActiveFreeNightProducts() {
        return crudService.<Product>findByNamedQuery(Product.GET_ALL_FREE_NIGHT_ACTIVE_PRODUCTS).stream().map(Product::getId).collect(toList());
    }

    protected List<Object[]> getDecisionDailyBarOutputDifferential(Date startDate, Date endDate, Date lastUploadedDate, String externalSystem) {
        LOGGER.info("Fetching DIFFERENTIAL Dailybar Decisions for date range (" + startDate + ", " + endDate + ")");
        final BigInteger fullRefreshDecisionToCompare = decisionService.getFullRefreshDecisionToCompare();
        LOGGER.info("Full Refresh Decision_ID to compare : " + fullRefreshDecisionToCompare);
        return crudService.findByNativeQuery(DailyBarConstants.getHiltonCPFileDecisionsDifferential(getRounding(externalSystem), fullRefreshDecisionToCompare),
                QueryParameter.with(DECISION_START_DATE, startDate).and(DECISION_END_DATE, endDate).and("lastUploadedDate", lastUploadedDate).parameters());
    }


    private List<Object[]> getFillInDataByWeek(Date startDate, Date endDate, List<Object[]> differentialData, String externalSystem) {
        LOGGER.info("Getting data to fill differential weeks.");
        List<Object[]> fullData = getDecisionDataFull(startDate, endDate, externalSystem);
        List<Object[]> addDecisionsToDifferential = new ArrayList<>();

        for (Object[] decisionRow : fullData) {
            LocalDate decisionDate = LocalDate.fromDateFields((Date) decisionRow[INDEX_OCCUPANCY_DATE]);
            String roomType = (String) decisionRow[INDEX_ROOM_TYPE];
            List<Object[]> differentialMatchingRoomTypeAndWeek = differentialData.stream()
                    .filter(diffRow -> roomType.equals(diffRow[INDEX_ROOM_TYPE]))
                    .filter(diffRow -> decisionDate.isAfter(getStartOfWeek(LocalDate.fromDateFields((Date) diffRow[INDEX_OCCUPANCY_DATE])).minusDays(1))
                            && decisionDate.isBefore(getStartOfWeek(LocalDate.fromDateFields((Date) diffRow[INDEX_OCCUPANCY_DATE])).plusDays(7)))
                    .collect(Collectors.toList());
            // If differential records exist for the same week and room type as this full decision record
            // but none of them are for the same occupancy date as this full decision record...
            if (!differentialMatchingRoomTypeAndWeek.isEmpty()
                    && differentialMatchingRoomTypeAndWeek.stream()
                    .map(diffRow -> LocalDate.fromDateFields((Date) diffRow[INDEX_OCCUPANCY_DATE])).noneMatch(decisionDate::equals)) {
                addDecisionsToDifferential.add(decisionRow);
            }
        }
        return addDecisionsToDifferential;
    }

    @SuppressWarnings("squid:S3776")
    private void hydrateCPFile(HiltonCPFile hiltonCPFile, List<Object[]> data) {
        hiltonCPFile.setWeeks(new ArrayList<>());
        data.forEach(row -> {
            LocalDate occupancyDate = LocalDate.fromDateFields((Date) row[INDEX_OCCUPANCY_DATE]);
            String roomType = (String) row[INDEX_ROOM_TYPE];
            HiltonCPSeason season = getSeason(hiltonCPFile, occupancyDate, roomType);

            BigDecimal ratePer1 = getBigDecimal(row[INDEX_SINGLE_RATE]);
            BigDecimal ratePer2 = getBigDecimal(row[INDEX_DOUBLE_RATE]);
            BigDecimal ratePerX = getBigDecimal(row[INDEX_ADULT_RATE]);
            BigDecimal rateChildX = getBigDecimal(row[INDEX_CHILD_RATE]);

            HiltonCPDay day = new HiltonCPDay();
            day.setDayOfWeek(DayOfWeek.valueOf(occupancyDate.getDayOfWeek()));
            day.setRatePer1(ratePer1 == null ? DUMMY_VALUE : ratePer1);
            day.setRatePer2(ratePer2 == null ? DUMMY_VALUE : ratePer2);
            day.setRatePer3(ratePer2 == null || ratePerX == null ? DUMMY_VALUE : ratePer2.add(ratePerX));  // Hilton specification: 3 person rate = 2 person + extra person
            day.setRatePer4(ratePer2 == null || ratePerX == null ? DUMMY_VALUE : ratePer2.add(ratePerX).add(ratePerX));  // Hilton specification: 4 person rate = 2 person + (2*extra person)

            if ("HCRS".equals(hiltonCPFile.getCrs())) {
                day.setRatePerX(ratePerX == null ? DUMMY_VALUE : ratePerX);
                day.setRateChildX(rateChildX == null ? DUMMY_VALUE : rateChildX);
                day.setMealPlanCode(MEAL_PLAN_CODE);
            } else if ("PCRS".equals(hiltonCPFile.getCrs())) {
                day.setRatePerX(null);
                day.setRateChildX(null);
                day.setMealPlanCode(null);
            }
            season.getRates().add(day);
        });
    }

    private void fillMissingDays(HiltonCPFile hiltonCPFile) {
        // Add a DUMMY_VALUE HiltonCPDay for any day missing from HiltonCPSeason.
        for (HiltonCPSeason week : hiltonCPFile.getWeeks()) {
            for (DayOfWeek dayOfWeek : DayOfWeek.values()) {
                if (week.getRates().stream().noneMatch(rate -> rate.getDayOfWeek().equals(dayOfWeek))) {
                    HiltonCPDay dummyRate = new HiltonCPDay();
                    dummyRate.setDayOfWeek(dayOfWeek);
                    fillDummyValues(dummyRate);
                    week.getRates().add(dummyRate);
                }
            }
            week.getRates().sort(Comparator.comparing(rate -> rate.getDayOfWeek().getCalendarDayOfWeek() % 7));
        }
    }

    private void fillDummyValues(HiltonCPDay rate) {
        if (rate.getRatePer1() == null) {
            rate.setRatePer1(DUMMY_VALUE);
        }
        if (rate.getRatePer2() == null) {
            rate.setRatePer2(DUMMY_VALUE);
        }
        if (rate.getRatePer3() == null) {
            rate.setRatePer3(DUMMY_VALUE);
        }
        if (rate.getRatePer4() == null) {
            rate.setRatePer4(DUMMY_VALUE);
        }
        if (rate.getRatePerX() == null) {
            rate.setRatePerX(DUMMY_VALUE);
        }
        if (rate.getRateChildX() == null) {
            rate.setRateChildX(DUMMY_VALUE);
        }
        if (rate.getMealPlanCode() == null) {
            rate.setMealPlanCode(MEAL_PLAN_CODE);
        }
    }

    private HiltonCPSeason getSeason(HiltonCPFile hiltonCPFile, LocalDate date, String roomType) {
        for (HiltonCPSeason season : hiltonCPFile.getWeeks()) {
            if (season.getRoomType().equals(roomType)
                    && date.isAfter(season.getStartDate())
                    && date.isBefore(season.getEndDate().plusDays(1))) {
                return season;
            }
        }
        HiltonCPSeason season = new HiltonCPSeason();
        LocalDate startDate = getStartOfWeek(date);
        season.setStartDate(startDate);
        season.setEndDate(startDate.plusDays(6));
        season.setRoomType(roomType);
        season.setRates(new ArrayList<>());
        hiltonCPFile.getWeeks().add(season);
        return season;
    }

    private LocalDate getStartOfWeek(LocalDate date) {
        return date.dayOfWeek().get() == 7 ? date : date.minusWeeks(1).withDayOfWeek(7);
    }

    private BigDecimal getBigDecimal(Object o) {
        if (null == o || (o instanceof BigDecimal)) {
            return (BigDecimal) o;
        }
        return BigDecimal.valueOf((Double) o).setScale(2);
    }

    private int getRounding(String externalSystem) {
        boolean applyRoundOFF = configParamsService.getBooleanParameterValue(configParameterNameService.getIntegrationParameterName(externalSystem, Constants.ROUNDING_OFF));
        return applyRoundOFF ? 0 : 2;
    }

    public void insertRecordForDecisionDeliveryTracking(String externalSystem, List<Integer> DECISION_TYPE_IDS, String decisionType) {
        Integer decisionId = decisionService.getMaxDecisionIdForDecisionTypes(DECISION_TYPE_IDS);
        if (decisionId != null) {
            decisionUploadDateToExternalSystemRepository.save(
                    valueOf(decisionId),
                    decisionType,
                    externalSystem,
                    getUploadType(externalSystem, decisionType)
            );
        }
    }

    private String getUploadType(String externalSystem, String decisionType) {
        String decisionUploadType = configParameterNameService.getIntegrationUploadTypeParameterName(externalSystem, decisionType, Constants.UPLOAD_TYPE);
        return configParamsService.getParameterValue(decisionUploadType);
    }

    public void setConfigParameterNameService(ConfigParameterNameService configParameterNameService) {
        this.configParameterNameService = configParameterNameService;
    }
}