package com.ideas.tetris.pacman.util.jaxb.hilstarpopulation.request;

import com.ideas.tetris.pacman.util.jaxb.hilstarpopulation.request.HilstarPopulationJAXBUtil.HilstarPopulationQualifier;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import org.springframework.stereotype.Component;


@HilstarPopulationQualifier
@Component
public class HilstarPopulationJAXBUtil extends com.ideas.tetris.platform.common.util.jaxb.JAXBUtil {
    private static final String CONTEXT_PATH = "com.ideas.tetris.pacman.common.xml.schema.hilstarpopulation.request.v1";
    private static final String SCHEMA_PATH = "/schema/hilstarpopulation/request/v1/hilstar_population_request.xsd";

    @Override
    public String createContextPath() {
        return CONTEXT_PATH;
    }

    @Override
    public String getSchemaPath() {
        return SCHEMA_PATH;
    }

    @javax.inject.Qualifier
    @Retention(RUNTIME)
    @Target({TYPE, METHOD, FIELD, PARAMETER})
    public @interface HilstarPopulationQualifier {
    }
}
