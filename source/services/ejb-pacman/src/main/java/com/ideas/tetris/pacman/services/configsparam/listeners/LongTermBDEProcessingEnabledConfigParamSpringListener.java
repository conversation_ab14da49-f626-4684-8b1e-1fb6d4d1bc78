package com.ideas.tetris.pacman.services.configsparam.listeners;


import com.ideas.tetris.platform.common.configparams.components.ConfigParamChangeEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

@Component
public class LongTermBDEProcessingEnabledConfigParamSpringListener {

    @Autowired
	private LongTermBDEProcessingEnabledConfigParamListener baseConfigParamListener;

    @EventListener
    public void onParameterChange(ConfigParamChangeEvent event) {
        baseConfigParamListener.onParameterChange(event);
    }
}
