package com.ideas.tetris.pacman.services.informationmanager.exception.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductAccomType;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.informationmanager.dto.*;
import com.ideas.tetris.pacman.services.informationmanager.entity.*;
import com.ideas.tetris.pacman.services.informationmanager.enums.*;
import com.ideas.tetris.pacman.services.informationmanager.service.AbstractAlertService;
import com.ideas.tetris.pacman.services.informationmanager.service.InformationManagerCleanupService;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessType;
import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompetitors;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompetitorsAccomClass;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrAlertConfigEntity.ADDITIONAL_CONDITION_PARAM_ROOM_CLASS_ID;
import static com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrAlertConfigEntity.ADDITIONAL_CONDITION_PARAM_ROOM_CLASS_ID_DEFAULT_VALUE;

@Component
@Transactional
public class ExceptionConfigService extends AbstractAlertService {

    public static final Logger LOGGER = Logger.getLogger(ExceptionConfigService.class.getName());
    public static final String BOOKING_PACE_AS_OF_LAST_NIGHTLY_OPTIMIZATION = "Booking Pace as of Last Nightly Optimization";
    public static final String OCCUPANCY_CHANGE_FROM_LAST_NIGHTLY_OPTIMIZATION = "Occupancy Change from Last Nightly Optimization";
    public static final String FORECAST_CHANGE_FROM_LAST_NIGHTLY_OPTIMIZATION = "Forecast Change from Last Nightly Optimization";
    public static final String COMPETITOR_PRICE_AS_OF_LAST_NIGHTLY_OPTIMIZATION = "Competitor Price as of Last Nightly Optimization";
    public static final String COMPETITOR_PRICE_CHANGE_NOTIFICATION = "Competitor Price Change Notification";
    public static final String DECISION_CHANGE_FROM_LAST_NIGHTLY_OPTIMIZATION = "Decision Change from Last Nightly Optimization";
    public static final String DECISION_CHANGE_FROM_LAST_OPTIMIZATION = "Decision Change from Last Optimization";
    public static final String FORECAST_CHANGE_FROM_LAST_OPTIMIZATION = "Forecast Change from Last Optimization";
    public static final String YEAR_ON_YEAR_PACE_CHANGE = "Year On Year Pace Change";
    public static final String OCCUPANCY_CHANGE_FROM_LAST_OPTIMIZATION = "Occupancy Change from Last Optimization";



    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	public CrudService crudService;

    @Autowired
	protected PacmanConfigParamsService configParamService;

    @Autowired
	protected InformationManagerCleanupService informationManagerCleanUpService;

    public static final String ADDITIONAL_CONDITIONS_SEPARATOR = "##";

    public static final String ADDITIONAL_CONDITIONS_PARAM_VALUE_SEPARATOR = "=";

    /**
     * Get all AlertTypes with Alert_Category as 'Exception'
     *
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<InfoMgrTypeEntity> getAllExceptionTypes() {
        return crudService.findByNamedQuery(
                InfoMgrTypeEntity.BY_CATEGORY, QueryParameter.with("categoryName", Constants.SYSTEM_EXCEPTION_CATEGORY)
                        .parameters());
    }

    @SuppressWarnings("unchecked")
    public List<InfoMgrTypeEntity> getAllNotificationTypes() {
        return crudService.findByNamedQuery(
                InfoMgrTypeEntity.BY_CATEGORY_ORDERED_DISPLAY, QueryParameter.with("categoryName", Constants.EXCEPTION_CATEGORY)
                        .parameters());
    }

    @SuppressWarnings("unchecked")
    public List<InfoMgrStatusEntity> getAllStatus() {
        return crudService.findByNamedQuery(InfoMgrStatusEntity.ALL);
    }

    /**
     * This method will return the List of Exception Sub Types (like LRV,
     * Pricing ,Overbooking etc...)
     */
    @SuppressWarnings("unchecked")

    public List<InformationMgrSubTypeEntity> getExceptionSubTypes(Integer exceptionTypeId) {
        List<String> excludeNotifSubTypes = new ArrayList<String>();
        if (getIsRankingEnabled() && !isAgileRatesEnabled()) {
            //price by rank, exclude price by value
            excludeNotifSubTypes.add(ExceptionSubType.PRICING_BY_VALUE.toString());
            return crudService.findByNamedQuery(InformationMgrTypeSubTypeMappingEntity.BY_EXCEPTION_TYPE_EXCLUDE_NOTIF_SUB_TYPE,
                    QueryParameter.with("exceptionTypeId", exceptionTypeId).and("notifSubType", excludeNotifSubTypes).parameters());

        } else {
            //price by value
            excludeNotifSubTypes.add(ExceptionSubType.PRICING.toString());
            return crudService.findByNamedQuery(InformationMgrTypeSubTypeMappingEntity.BY_EXCEPTION_TYPE_EXCLUDE_NOTIF_SUB_TYPE,
                    QueryParameter.with("exceptionTypeId", exceptionTypeId).and("notifSubType", excludeNotifSubTypes).parameters());
        }
    }

    public boolean getIsRankingEnabled() {
        String context = String.format("%s.%s", "pacman", PacmanWorkContextHelper.getClientCode());
        String isRankingEnabledStr = configParamService.getValue(context, IPConfigParamName.CORE_IS_RANKING_ENABLED.value());
        return "true".equalsIgnoreCase(isRankingEnabledStr);
    }

    /**
     * This Method will return the Exception levels like Room Class, Business
     * Group etc...
     */
    public List<InformationMgrLevelEntity> getExceptionLevels(Integer exceptionTypeId, Integer subTypeId) {
        @SuppressWarnings("unchecked")
        List<InformationMgrLevelEntity> levelsList = crudService.findByNamedQuery(InformationMgrSubTypeLevelMappingEntity.BY_EXCEPTION_SUBTYPE, QueryParameter.with("subTypeId", subTypeId).parameters());
        InfoMgrTypeEntity alertType = crudService.find(InfoMgrTypeEntity.class, exceptionTypeId);


        if (alertType.getName().equals(AlertType.YOYPaceChangeEx.name())) {
            List<InformationMgrLevelEntity> templevelList = new ArrayList<InformationMgrLevelEntity>();
            templevelList.addAll(levelsList);
            for (InformationMgrLevelEntity levelEntity : templevelList) {
                if (levelEntity.getName().equals(LevelType.ROOM_TYPE.getCode()) || levelEntity.getName().equals(LevelType.MARKET_SEGMENT.getCode())) {
                    levelsList.remove(levelEntity);
                }
            }
        }
        return levelsList;
    }

    /**
     * @param subTypeCodes
     * @return
     */
    @SuppressWarnings("unchecked")

    public List<InformationMgrLevelEntity> getExceptionlevelsForSubTypes(List<String> subTypeCodes) {
        return crudService.findByNamedQuery(InformationMgrSubTypeLevelMappingEntity.BY_EXCEPTION_SUBTYPE_CODES, QueryParameter.with("subTypeCodes", subTypeCodes).parameters());
    }

    /**
     * This method will return the List of the Sub levels bases on the selection
     * of the Level and Property Ids passed.
     */

    public List<SubLevelConfigDTO> getExceptionSubLevels(Integer subTypeId, Integer levelId, List<Integer> propertyIds, Product product) {
        List<SubLevelConfigDTO> subLevels = new ArrayList<SubLevelConfigDTO>();

        @SuppressWarnings("unchecked")
        List<InformationMgrSubLevelEntity> subLevelsList = crudService.findByNamedQuery(InformationMgrSubLevelEntity.BY_EXCEPTION_LEVEL, QueryParameter.with("levelId", levelId).parameters());

        List<SubLevelConfigDTO> listSubLevelConfigDTO = covertToSubLevelDTO(subLevelsList);

        InformationMgrSubTypeEntity subTypeEntity = crudService.find(InformationMgrSubTypeEntity.class, subTypeId);
        boolean isROHEnabledForProperties = false;
        InformationMgrLevelEntity levelEntity = crudService.find(InformationMgrLevelEntity.class, levelId);
        if (subTypeEntity.getName().equalsIgnoreCase(ExceptionSubType.OVERBOOKING.getCode())) {
            isROHEnabledForProperties = checkIfROHIsEnabledForProperties(propertyIds);

            if (isROHEnabledForProperties) {
                subLevels.addAll(listSubLevelConfigDTO);
            }
        } else if (!levelEntity.getName().equalsIgnoreCase(LevelType.ROOM_TYPE.getCode())) {
            subLevels.addAll(listSubLevelConfigDTO);
        }
        switch (LevelType.valueOfByCode(levelEntity.getName())) {
            case ROOM_CLASS:
                List<SubLevelConfigDTO> resultList = getCommonRoomClassesForProperties(propertyIds);
                subLevels.addAll(resultList);
                break;
            case ROOM_TYPE:
                addCommomRoomTypes(propertyIds, product, subLevels, isROHEnabledForProperties);
                break;
            case MARKET_SEGMENT:
                subLevels.addAll(getCommonMktSegmentsForProperties(propertyIds));
                break;
            case BUSINESS_TYPE:
                subLevels.addAll(getBusinessTypesForProperty());
                break;
            case FORECAST_GROUP:
                subLevels.addAll(getCommomForecastGroupsForProperties(propertyIds));
                break;
            case PROPERTY_BUSINESS_VIEW:
                subLevels.addAll(getPBVForProperties(propertyIds));
                break;
            case PROPERTY:
                if (subLevels.isEmpty()) {
                    subLevels.addAll(listSubLevelConfigDTO);
                }
                break;
            case COMPETITOR_SELECT:
                subLevels.addAll(getCompetitros(propertyIds, product));
                break;

            default:
                throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, " Unable to get SubLevels.");
        }
        return subLevels;
    }

    private void addCommomRoomTypes(List<Integer> propertyIds, Product product, List<SubLevelConfigDTO> subLevels, boolean isROHEnabledForProperties) {
        if (null == product) {
            subLevels.addAll(getCommonRoomTypesForProperties(propertyIds, isROHEnabledForProperties));
        } else {
            subLevels.addAll(getCommonRoomTypesForProperties(propertyIds, product));
        }
    }

    private List<SubLevelConfigDTO> getCommonRoomTypesForProperties(List<Integer> propertyIds, Product product) {
        List<String> commonRoomTypes = new ArrayList<>();
        Integer propertyId = propertyIds.get(0);
        Product selectedProductDetails = null;
        List<Product> productList = multiPropertyCrudService.findByNamedQueryForSinglePropertyGeneric(propertyId, Product.GET_ALL_BY_NAME, QueryParameter.with("name",
                product.getName()).parameters());
        selectedProductDetails = productList.get(0);
        List<AccomType> accomTypeList = new ArrayList<>();
        List<ProductAccomType> productRoomTypesByProduct = findProductRoomTypesByProduct(selectedProductDetails, propertyId);
        for (ProductAccomType productAccomType : productRoomTypesByProduct) {
            AccomType roomType = productAccomType.getAccomType();
            accomTypeList.add(roomType);
        }
        List<String> result;
        if (getParameterValue(propertyId, FeatureTogglesConfigParamName.ROOM_TYPE_RECODING_UI_ENABLED.value())) {
            result = accomTypeList.stream().filter(accomType -> !(accomType.getDisplayStatusId() == Status.INACTIVE.getId())).map(AccomType::getAccomTypeCode).sorted().collect(Collectors.toList());
        } else {
            result = accomTypeList.stream().map(AccomType::getAccomTypeCode).sorted().collect(Collectors.toList());
        }
        commonRoomTypes.addAll(result);
        List<SubLevelConfigDTO> listSubLevelConfigDTOs = new ArrayList<SubLevelConfigDTO>();
        SubLevelConfigDTO subLevelDTO = null;
        for (String accomTypeCode : commonRoomTypes) {
            subLevelDTO = new SubLevelConfigDTO();
            subLevelDTO.setCode(accomTypeCode);
            listSubLevelConfigDTOs.add(subLevelDTO);
        }

        return listSubLevelConfigDTOs;
    }

    public List<SubLevelConfigDTO> covertToSubLevelDTO(List<InformationMgrSubLevelEntity> subLevels) {
        List<SubLevelConfigDTO> listSubLevelConfigDTO = new ArrayList<SubLevelConfigDTO>();
        SubLevelConfigDTO subLevelConfigDTO = null;

        for (InformationMgrSubLevelEntity subLevel : subLevels) {
            subLevelConfigDTO = new SubLevelConfigDTO();
            subLevelConfigDTO.setName(subLevel.getName());
            subLevelConfigDTO.setCode(subLevel.getDescription());
            listSubLevelConfigDTO.add(subLevelConfigDTO);
        }
        return listSubLevelConfigDTO;
    }


    private Integer getSubLevelIdForCode(InformationMgrLevelEntity levelEntity, Integer propertyId, String code) {
        Integer subLevelId = null;
        switch (LevelType.valueOfByCode(levelEntity.getName())) {
            case ROOM_CLASS:
                if (code.equalsIgnoreCase(SubLevelType.MASTER_CLASS.getCode())) {
                    InformationMgrSubLevelEntity subLevelEntity = (InformationMgrSubLevelEntity) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId, InformationMgrSubLevelEntity.BY_DESC, QueryParameter.with("description", code).parameters());
                    subLevelId = subLevelEntity.getId();
                } else {
                    AccomClass objAccomClass = getAccomClassByCode(propertyId, code);
                    subLevelId = objAccomClass.getId();
                }
                break;
            case ROOM_TYPE:
                if (code.equalsIgnoreCase(SubLevelType.ROH_ROOM_TYPE.getCode())) {
                    InformationMgrSubLevelEntity subLevelEntity = (InformationMgrSubLevelEntity) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId, InformationMgrSubLevelEntity.BY_DESC, QueryParameter.with("description", code).parameters());
                    subLevelId = subLevelEntity.getId();
                } else {
                    AccomType objAccomType = (AccomType) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId, AccomType.BY_CODE, QueryParameter.with("code", code).parameters());
                    subLevelId = objAccomType.getId();
                }
                break;
            case MARKET_SEGMENT:
                MktSeg objMktSeg = (MktSeg) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId, MktSeg.BY_PROPERTY_ID_AND_CODE, QueryParameter.with("code", code).and("propertyId", propertyId).parameters());
                subLevelId = objMktSeg.getId();
                break;
            case BUSINESS_TYPE:
                BusinessType objBusinessType = (BusinessType) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId, BusinessType.BY_NAME, QueryParameter.with("name", code).parameters());
                subLevelId = objBusinessType.getId();
                break;
            case FORECAST_GROUP:
                ForecastGroup objForecastGroup = (ForecastGroup) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId, ForecastGroup.BY_PROPERTY_AND_NAME, QueryParameter.with("name", code).and("propertyId", propertyId).parameters());
                subLevelId = objForecastGroup.getId();
                break;
            case PROPERTY_BUSINESS_VIEW:
                BusinessGroup objBusinessGroup = (BusinessGroup) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId, BusinessGroup.BY_NAME, QueryParameter.with("propertyId", propertyId).and("name", code).parameters());
                subLevelId = objBusinessGroup.getId();
                break;
            case PROPERTY:
                InformationMgrSubLevelEntity subLevelEntity = (InformationMgrSubLevelEntity) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId, InformationMgrSubLevelEntity.BY_EXCEPTION_LEVEL, QueryParameter.with("levelId", levelEntity.getId()).parameters());
                subLevelId = subLevelEntity.getId();
                break;
            case COMPETITOR_SELECT:
                WebrateCompetitors objWebrateCompetitors = (WebrateCompetitors) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId, WebrateCompetitors.BY_PROPERTY_ID_AND_NAME, QueryParameter.with("propertyId", propertyId).and("competitorName", code).parameters());
                subLevelId = objWebrateCompetitors.getId();
                break;

            default:
                throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, " Unable to get SubLevels.");
        }
        return subLevelId;
    }

    /**
     *
     */

    public List<String> getMetricTypes(String exceptionSubType) {
        return MetricType.getMetricTypes(exceptionSubType);
    }

    /**
     * This method returns name and ids of competitors.
     *
     * @param propertyIds
     * @return
     */
    @SuppressWarnings("unchecked")
	public
    List<SubLevelConfigDTO> getCompetitros(List<Integer> propertyIds, Product product) {
        List<String> commonCompetitors = new ArrayList<String>();
        for (int i = 0; i < propertyIds.size(); i++) {

            List<String> result = product == null || product.isSystemDefault() ? multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyIds.get(i), WebrateCompetitors.GET_NAME_BY_PROPERTY_ID, QueryParameter.with("propertyId", propertyIds.get(i)).parameters())
                    : multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyIds.get(i), WebrateCompetitorsAccomClass.COMP_BY_PRODUCT_ID_AND_DEMAND, QueryParameter.with("productID", product.getId()).and("propertyId", propertyIds.get(i)).parameters());

            if (i == 0) {
                commonCompetitors.addAll(result);
            } else {
                commonCompetitors.retainAll(result);
            }
        }

        List<SubLevelConfigDTO> listSubLevelConfigDTO = new ArrayList<SubLevelConfigDTO>();
        SubLevelConfigDTO subLevelConfigDTO = null;

        for (String webrateCompetitorName : commonCompetitors) {
            subLevelConfigDTO = new SubLevelConfigDTO();
            subLevelConfigDTO.setCode(webrateCompetitorName);
            listSubLevelConfigDTO.add(subLevelConfigDTO);
        }

        return listSubLevelConfigDTO;
    }

    /**
     * This method checks if ROH is enabled for properties
     */
    public boolean checkIfROHIsEnabledForProperties(List<Integer> properties) {

        for (Integer propertyId : properties) {
            Property ObjProperty = globalCrudService.find(Property.class, propertyId);
            StringBuilder nodeName = new StringBuilder();
            nodeName.append(Constants.CONFIG_PARAMS_NODE_PREFIX).append(".")
                    .append(ObjProperty.getClient().getCode()).append(".")
                    .append(ObjProperty.getCode());
            String paramValue = configParamService.getValue(nodeName.toString(),
                    IPConfigParamName.ROH_ROHENABLED.value());
            if (null == paramValue || "False".equalsIgnoreCase(paramValue)) {
                return false;
            }
        }
        return true;
    }


    /**
     * This method Creates the Exception configurations.
     *
     * @param objExceptionConfigDTO
     */

    public Map<String, List<InformationMgrAlertConfigEntity>> persistExceptionConfiguration(ExceptionConfigDTO objExceptionConfigDTO) {
        try {
            Map<Integer, List<String>> noOfExceptionsToCreate = identifyNumberOfExceptionsToCreate(objExceptionConfigDTO);
            Set<Integer> objKeySet = noOfExceptionsToCreate.keySet();
            Iterator<Integer> itr = objKeySet.iterator();

            // Result returned with Success and Failure (because of duplicate configuration)
            Map<String, List<InformationMgrAlertConfigEntity>> resultMap = new HashMap<String, List<InformationMgrAlertConfigEntity>>();

            List<InformationMgrAlertConfigEntity> listExceptionRuleSuccess = new ArrayList<InformationMgrAlertConfigEntity>();
            List<InformationMgrAlertConfigEntity> listExceptionRuleFailure = new ArrayList<InformationMgrAlertConfigEntity>();

            InformationMgrSubLevelEntity objSubLevel = null;
            if (objExceptionConfigDTO.isSubLevelHasKeyword()) {
                objSubLevel = (InformationMgrSubLevelEntity) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(objExceptionConfigDTO.getPropertyIds().get(0), InformationMgrSubLevelEntity.BY_DESC, QueryParameter.with("description", objExceptionConfigDTO.getExceptionSubLevel()).parameters());
            }

            if ((isPricingNotfication(objExceptionConfigDTO.getExceptionSubType().getName()) ||
                    isCompetitiveNotification(objExceptionConfigDTO.getExceptionSubType().getName())) && objExceptionConfigDTO.getProduct() == null) {
                Product barProduct = (Product) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(objExceptionConfigDTO.getPropertyIds().get(0), Product.GET_SYSTEM_DEFAULT);
                objExceptionConfigDTO.setProduct(barProduct);
            }

            //For All Competitor selection
            if (null != objSubLevel && objExceptionConfigDTO.isSubLevelHasKeyword()
                    && objSubLevel.getDescription().equalsIgnoreCase(SubLevelType.ALL_COMPETITOR.getCode())) {
                for (Entry<Integer, List<String>> propertyCompMap : noOfExceptionsToCreate.entrySet()) {
                    Integer propertyId = propertyCompMap.getKey();
                    String propertyCode = globalCrudService.find(Property.class, propertyId).getCode();
                    for (String competitor : propertyCompMap.getValue()) {
                        objExceptionConfigDTO.setSubLevelHasKeyword(false);
                        createExceptionConfig(objExceptionConfigDTO,
                                listExceptionRuleSuccess, listExceptionRuleFailure,
                                propertyId, propertyCode, Integer.parseInt(competitor));
                    }
                }
            } else {
                while (itr.hasNext()) {
                    Integer propertyId = itr.next();

                    String propertyCode = globalCrudService.find(Property.class, propertyId).getCode();
                    List<String> listRoomClasses = noOfExceptionsToCreate
                            .get(propertyId);
                    // check if user want to create configuration @property level
                    if (null != listRoomClasses
                            && !objExceptionConfigDTO.getExceptionLevel().getDescription()
                            .equalsIgnoreCase(LevelType.PROPERTY.getCode())) {
                        //create configuration for individual room class
                        for (String roomClass : listRoomClasses) {
                            InformationMgrAlertConfigEntity objExceptionAlertConfigEntity = new InformationMgrAlertConfigEntity();
                            objExceptionAlertConfigEntity.setPropertyId(propertyId);
                            objExceptionAlertConfigEntity
                                    .setPropertyCode(propertyCode);

                            // Storing the Master_class keyword in DB
                            if (null != objSubLevel && objSubLevel.getDescription()
                                    .equalsIgnoreCase(
                                            SubLevelType.MASTER_CLASS
                                                    .getCode())) {
                                objExceptionAlertConfigEntity
                                        .setExceptionSubLevel(objSubLevel.getId());
                                objExceptionAlertConfigEntity.setSubLevelKeywordUsed(true);
                            } else {
                                objExceptionAlertConfigEntity
                                        .setExceptionSubLevel(Integer.parseInt(roomClass));
                                objExceptionAlertConfigEntity.setSubLevelKeywordUsed(false);
                                objExceptionConfigDTO.setSubLevelHasKeyword(false);
                            }

                            objExceptionAlertConfigEntity = populateExceptionConfiguration(
                                    objExceptionAlertConfigEntity,
                                    objExceptionConfigDTO, propertyId);
                            objExceptionAlertConfigEntity.setSubLevelDisplayName(getSubLevelCodeForId(objExceptionAlertConfigEntity));
                            // check if same configuration is already exists
                            if (!checkForDuplicateExceptionConfiguration(objExceptionAlertConfigEntity)) {
                                objExceptionAlertConfigEntity = multiPropertyCrudService.save(propertyId, objExceptionAlertConfigEntity);
                                listExceptionRuleSuccess
                                        .add(objExceptionAlertConfigEntity);
                            } else {
                                listExceptionRuleFailure
                                        .add(objExceptionAlertConfigEntity);
                            }
                        }

                    } else {
                        //configuration at the Property level

                        Integer exceptionSubLevel = null;
                        if (null != objSubLevel && objSubLevel.getDescription().equalsIgnoreCase(SubLevelType.ROH_ROOM_TYPE.getCode())) {
                            objExceptionConfigDTO.setExceptionSubLevel(objExceptionConfigDTO.getExceptionSubLevel());
                            objExceptionConfigDTO.setSubLevelHasKeyword(true);
                            exceptionSubLevel = objSubLevel.getId();

                        } else {
                            exceptionSubLevel = (!isOverbookingChangeAlertType(objExceptionConfigDTO.getAlertTypeEntity().getName()))
                                    ? getSubLevelIdForCode(objExceptionConfigDTO.getExceptionLevel(), propertyId, objExceptionConfigDTO.getExceptionSubLevel())
                                    : null;
                        }
                        createExceptionConfig(objExceptionConfigDTO,
                                listExceptionRuleSuccess, listExceptionRuleFailure,
                                propertyId, propertyCode, exceptionSubLevel);
                    }
                }
            }
            resultMap.put("SUCCESS", listExceptionRuleSuccess);
            resultMap.put("FAILURE", listExceptionRuleFailure);
            return resultMap;
        } catch (Exception e) {
            LOGGER.error("Unable to create Notification Configuration ", e);
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Unable to create Notification Configuration");
        }
    }

    private static boolean isOverbookingChangeAlertType(String subType) {
        return subType.equals(ExceptionType.OverbookingChangeDuetoClosedCompetitors.name());
    }

    public Map<String, List<InformationMgrAlertConfigEntity>> persistExceptionConfigurationForNotification(ExceptionConfigDTO objExceptionConfigDTO) {
        try {
            Map<Integer, List<String>> noOfExceptionsToCreate = identifyNumberOfExceptionsToCreate(objExceptionConfigDTO);
            Set<Integer> objKeySet = noOfExceptionsToCreate.keySet();
            Iterator<Integer> itr = objKeySet.iterator();

            // Result returned with Success and Failure (because of duplicate configuration)
            Map<String, List<InformationMgrAlertConfigEntity>> resultMap = new HashMap<String, List<InformationMgrAlertConfigEntity>>();

            List<InformationMgrAlertConfigEntity> listExceptionRuleSuccess = new ArrayList<InformationMgrAlertConfigEntity>();
            List<InformationMgrAlertConfigEntity> listExceptionRuleFailure = new ArrayList<InformationMgrAlertConfigEntity>();

            InformationMgrSubLevelEntity objSubLevel = null;
            if (objExceptionConfigDTO.isSubLevelHasKeyword()) {
                objSubLevel = (InformationMgrSubLevelEntity) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(objExceptionConfigDTO.getPropertyIds().get(0), InformationMgrSubLevelEntity.BY_DESC, QueryParameter.with("description", objExceptionConfigDTO.getExceptionSubLevel()).parameters());
            }

            if ((isPricingNotfication(objExceptionConfigDTO.getExceptionSubType().getName()) ||
                    isCompetitiveNotification(objExceptionConfigDTO.getExceptionSubType().getName())) && objExceptionConfigDTO.getProduct() == null) {
                Product barProduct = (Product) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(objExceptionConfigDTO.getPropertyIds().get(0), Product.GET_SYSTEM_DEFAULT);
                objExceptionConfigDTO.setProduct(barProduct);
            }

            //For All Competitor selection
            if (null != objSubLevel && objExceptionConfigDTO.isSubLevelHasKeyword()
                    && objSubLevel.getDescription().equalsIgnoreCase(SubLevelType.ALL_COMPETITOR.getCode())) {
                for (Entry<Integer, List<String>> propertyCompMap : noOfExceptionsToCreate.entrySet()) {
                    Integer propertyId = propertyCompMap.getKey();
                    String propertyCode = globalCrudService.find(Property.class, propertyId).getCode();
                    for (String competitor : propertyCompMap.getValue()) {
                        objExceptionConfigDTO.setSubLevelHasKeyword(false);
                        createExceptionConfigForNotification(objExceptionConfigDTO,
                                listExceptionRuleSuccess, listExceptionRuleFailure,
                                propertyId, propertyCode, Integer.parseInt(competitor));
                    }
                }
            } else {
                while (itr.hasNext()) {
                    Integer propertyId = itr.next();

                    String propertyCode = globalCrudService.find(Property.class, propertyId).getCode();
                    List<String> listRoomClasses = noOfExceptionsToCreate
                            .get(propertyId);
                    // check if user want to create configuration @property level
                    if (null != listRoomClasses
                            && !objExceptionConfigDTO.getExceptionLevel().getDescription()
                            .equalsIgnoreCase(LevelType.PROPERTY.getCode())) {
                        //create configuration for individual room class
                        for (String roomClass : listRoomClasses) {
                            InformationMgrAlertConfigEntity objExceptionAlertConfigEntity = new InformationMgrAlertConfigEntity();
                            objExceptionAlertConfigEntity.setPropertyId(propertyId);
                            objExceptionAlertConfigEntity
                                    .setPropertyCode(propertyCode);

                            // Storing the Master_class keyword in DB
                            if (null != objSubLevel && objSubLevel.getDescription()
                                    .equalsIgnoreCase(
                                            SubLevelType.MASTER_CLASS
                                                    .getCode())) {
                                objExceptionAlertConfigEntity
                                        .setExceptionSubLevel(objSubLevel.getId());
                                objExceptionAlertConfigEntity.setSubLevelKeywordUsed(true);
                            } else {
                                objExceptionAlertConfigEntity
                                        .setExceptionSubLevel(Integer.parseInt(roomClass));
                                objExceptionAlertConfigEntity.setSubLevelKeywordUsed(false);
                                objExceptionConfigDTO.setSubLevelHasKeyword(false);
                            }

                            objExceptionAlertConfigEntity = populateExceptionConfiguration(
                                    objExceptionAlertConfigEntity,
                                    objExceptionConfigDTO, propertyId);
                            objExceptionAlertConfigEntity.setSubLevelDisplayName(getSubLevelCodeForId(objExceptionAlertConfigEntity));
                            // check if same configuration is already exists
                            if (!checkForDuplicateExceptionConfigurationForNotification(objExceptionAlertConfigEntity)) {
                                objExceptionAlertConfigEntity = multiPropertyCrudService.save(propertyId, objExceptionAlertConfigEntity);
                                listExceptionRuleSuccess
                                        .add(objExceptionAlertConfigEntity);
                            } else {
                                listExceptionRuleFailure
                                        .add(objExceptionAlertConfigEntity);
                            }
                        }

                    } else {
                        //configuration at the Property level

                        Integer exceptionSubLevel = null;
                        if (null != objSubLevel && objSubLevel.getDescription().equalsIgnoreCase(SubLevelType.ROH_ROOM_TYPE.getCode())) {
                            objExceptionConfigDTO.setExceptionSubLevel(objExceptionConfigDTO.getExceptionSubLevel());
                            objExceptionConfigDTO.setSubLevelHasKeyword(true);
                            exceptionSubLevel = objSubLevel.getId();

                        } else {
                            exceptionSubLevel = getSubLevelIdForCode(objExceptionConfigDTO.getExceptionLevel(),
                                    propertyId, objExceptionConfigDTO.getExceptionSubLevel());
                        }
                        createExceptionConfigForNotification(objExceptionConfigDTO,
                                listExceptionRuleSuccess, listExceptionRuleFailure,
                                propertyId, propertyCode, exceptionSubLevel);
                    }
                }
            }
            resultMap.put("SUCCESS", listExceptionRuleSuccess);
            resultMap.put("FAILURE", listExceptionRuleFailure);
            return resultMap;
        } catch (Exception e) {
            LOGGER.error("Unable to create Notification Configuration ", e);
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Unable to create Notification Configuration");
        }
    }


    private void createExceptionConfig(
            ExceptionConfigDTO objExceptionConfigDTO,
            List<InformationMgrAlertConfigEntity> listExceptionRuleSuccess,
            List<InformationMgrAlertConfigEntity> listExceptionRuleFailure,
            Integer propertyId, String propertyCode, Integer exceptionSubLevel) {
        InformationMgrAlertConfigEntity objExceptionAlertConfigEntity = new InformationMgrAlertConfigEntity();
        objExceptionAlertConfigEntity.setPropertyId(propertyId);
        objExceptionAlertConfigEntity.setPropertyCode(propertyCode);
        objExceptionAlertConfigEntity.setExceptionSubLevel(exceptionSubLevel);
        if (null != objExceptionConfigDTO.getProduct()) {
            List<Product> product = multiPropertyCrudService.findByNamedQueryForSinglePropertyGeneric(propertyId, Product.GET_ALL_BY_NAME, QueryParameter.with("name",
                    objExceptionConfigDTO.getProduct().getName()).parameters());
            objExceptionAlertConfigEntity.setProduct(product.get(0));
        }
        objExceptionAlertConfigEntity = populateExceptionConfiguration(
                objExceptionAlertConfigEntity, objExceptionConfigDTO, propertyId);
        objExceptionAlertConfigEntity.setSubLevelDisplayName(getSubLevelCodeForId(objExceptionAlertConfigEntity));
        StringBuilder additionalConditions = new StringBuilder();
        Map<String, String> additionalConditionsMap = objExceptionConfigDTO.getAdditionalConditions();
        if (MapUtils.isNotEmpty(additionalConditionsMap)) {
            additionalConditionsMap.entrySet().forEach(entry ->
                    additionalConditions.append(entry.getKey()).append(ADDITIONAL_CONDITIONS_PARAM_VALUE_SEPARATOR).append(entry.getValue()).append(ADDITIONAL_CONDITIONS_SEPARATOR));
            objExceptionAlertConfigEntity.setAdditionalConditions(additionalConditions.toString());
        }
        if (!checkForDuplicateExceptionConfiguration(objExceptionAlertConfigEntity)) {
            objExceptionAlertConfigEntity = multiPropertyCrudService.save(propertyId, objExceptionAlertConfigEntity);

            listExceptionRuleSuccess.add(objExceptionAlertConfigEntity);
        } else {
            listExceptionRuleFailure.add(objExceptionAlertConfigEntity);
        }
    }

    private void createExceptionConfigForNotification(
            ExceptionConfigDTO objExceptionConfigDTO,
            List<InformationMgrAlertConfigEntity> listExceptionRuleSuccess,
            List<InformationMgrAlertConfigEntity> listExceptionRuleFailure,
            Integer propertyId, String propertyCode, Integer exceptionSubLevel) {
        InformationMgrAlertConfigEntity objExceptionAlertConfigEntity = new InformationMgrAlertConfigEntity();
        objExceptionAlertConfigEntity.setPropertyId(propertyId);
        objExceptionAlertConfigEntity.setPropertyCode(propertyCode);
        objExceptionAlertConfigEntity.setExceptionSubLevel(exceptionSubLevel);
        if (null != objExceptionConfigDTO.getProduct()) {
            List<Product> product = multiPropertyCrudService.findByNamedQueryForSinglePropertyGeneric(propertyId, Product.GET_ALL_BY_NAME, QueryParameter.with("name",
                    objExceptionConfigDTO.getProduct().getName()).parameters());
            objExceptionAlertConfigEntity.setProduct(product.get(0));
        }
        objExceptionAlertConfigEntity = populateExceptionConfiguration(
                objExceptionAlertConfigEntity, objExceptionConfigDTO, propertyId);
        objExceptionAlertConfigEntity.setSubLevelDisplayName(getSubLevelCodeForId(objExceptionAlertConfigEntity));
        StringBuilder additionalConditions = new StringBuilder();
        Map<String, String> additionalConditionsMap = objExceptionConfigDTO.getAdditionalConditions();
        if (MapUtils.isNotEmpty(additionalConditionsMap)) {
            additionalConditionsMap.entrySet().forEach(entry ->
                    additionalConditions.append(entry.getKey()).append(ADDITIONAL_CONDITIONS_PARAM_VALUE_SEPARATOR).append(entry.getValue()).append(ADDITIONAL_CONDITIONS_SEPARATOR));
            objExceptionAlertConfigEntity.setAdditionalConditions(additionalConditions.toString());
        }
        if (!checkForDuplicateExceptionConfigurationForNotification(objExceptionAlertConfigEntity)) {
            objExceptionAlertConfigEntity = multiPropertyCrudService.save(propertyId, objExceptionAlertConfigEntity);

            listExceptionRuleSuccess.add(objExceptionAlertConfigEntity);
        } else {
            listExceptionRuleFailure.add(objExceptionAlertConfigEntity);
        }
    }


    /**
     * Helper method to populate the InformationMgrAlertConfigEntity from DTO
     *
     * @param objExceptionAlertConfigEntity
     * @param objExceptionConfigDTO
     * @param propertyId
     * @return
     */
    private InformationMgrAlertConfigEntity populateExceptionConfiguration(
            InformationMgrAlertConfigEntity objExceptionAlertConfigEntity,
            ExceptionConfigDTO objExceptionConfigDTO, Integer propertyId) {
        objExceptionAlertConfigEntity
                .setAlertTypeEntity(objExceptionConfigDTO.getAlertTypeEntity());
        objExceptionAlertConfigEntity.setExceptionSubType(objExceptionConfigDTO.getExceptionSubType());
        objExceptionAlertConfigEntity.setDisabled(objExceptionConfigDTO.isDisabled());
        objExceptionAlertConfigEntity.setExceptionLevel(objExceptionConfigDTO.getExceptionLevel());
        objExceptionAlertConfigEntity.setFrequency(objExceptionConfigDTO.getFrequency());
        objExceptionAlertConfigEntity.setStartDate(objExceptionConfigDTO.getStartDate());
        objExceptionAlertConfigEntity.setEndDate(objExceptionConfigDTO.getEndDate());
        objExceptionAlertConfigEntity.setStatusId(objExceptionConfigDTO.getStatusId());
        String operatorString = objExceptionConfigDTO.getThresholdConstraint();
        objExceptionAlertConfigEntity.setThresholdOperator(operatorString);
        objExceptionAlertConfigEntity.setThresholdMetricType(objExceptionConfigDTO.getMetricType());
        objExceptionAlertConfigEntity.setThresholdValue(objExceptionConfigDTO.getThresholdValue());
        objExceptionAlertConfigEntity.setCreateDate(DateUtil.getCurrentDate());
        objExceptionAlertConfigEntity.setCreatedByUserId(Integer.parseInt(PacmanThreadLocalContextHolder.getWorkContext().getUserId()));
        objExceptionAlertConfigEntity.setSubLevelKeywordUsed(objExceptionConfigDTO.isSubLevelHasKeyword());
        if (null != objExceptionConfigDTO.getProduct()) {
            List<Product> product = multiPropertyCrudService.findByNamedQueryForSinglePropertyGeneric(propertyId, Product.GET_ALL_BY_NAME, QueryParameter.with("name",
                    objExceptionConfigDTO.getProduct().getName()).parameters());
            objExceptionAlertConfigEntity.setProduct(product.get(0));
        }
        return objExceptionAlertConfigEntity;
    }


    /**
     * This method returns the Common Room Classes for properties
     *
     * @param propertyIds
     * @return
     */
    @SuppressWarnings("unchecked")
    protected List<SubLevelConfigDTO> getCommonRoomClassesForProperties(List<Integer> propertyIds) {
        List<String> commonRoomClasses = new ArrayList<String>();
        for (int i = 0; i < propertyIds.size(); i++) {
            List<String> result = multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyIds.get(i), AccomClass.GET_CODE_FOR_ALL_ACTIVE_NON_DEFAULT, QueryParameter.with("propertyId", propertyIds.get(i)).parameters());
            if (i == 0) {
                commonRoomClasses.addAll(result);
            } else {
                commonRoomClasses.retainAll(result);
            }
        }
        List<SubLevelConfigDTO> listSubLevelConfigDTOs = new ArrayList<SubLevelConfigDTO>();
        SubLevelConfigDTO subLevelDTO = null;
        for (String accomClassCode : commonRoomClasses) {
            subLevelDTO = new SubLevelConfigDTO();
            subLevelDTO.setCode(accomClassCode);
            listSubLevelConfigDTOs.add(subLevelDTO);
        }
        return listSubLevelConfigDTOs;
    }

    /**
     * This method fetches common Room Types for multiple properties.
     *
     * @param propertyIds
     * @return
     */
    @SuppressWarnings("unchecked")
    private List<SubLevelConfigDTO> getCommonRoomTypesForProperties(List<Integer> propertyIds, boolean isROHEnabledForProperties) {
        List<String> commonRoomTypes = new ArrayList<>();

        if (!isROHEnabledForProperties) {
            for (int i = 0; i < propertyIds.size(); i++) {
                List<AccomType> accomTypeList = multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyIds.get(i), AccomType.BY_PROPERTY_ID, QueryParameter.with("propertyId", propertyIds.get(i)).parameters());

                List<String> result;

                if (getParameterValue(propertyIds.get(i), FeatureTogglesConfigParamName.ROOM_TYPE_RECODING_UI_ENABLED.value())) {
                    result = accomTypeList.stream().filter(accomType -> !(accomType.getDisplayStatusId() == Status.INACTIVE.getId())).map(AccomType::getAccomTypeCode).sorted().collect(Collectors.toList());
                } else {
                    result = accomTypeList.stream().map(AccomType::getAccomTypeCode).sorted().collect(Collectors.toList());
                }

                if (i == 0) {
                    commonRoomTypes.addAll(result);
                } else {
                    commonRoomTypes.retainAll(result);
                }
            }
        } else {
            //add only if Configured ROH Room Type is common for selected properties
            for (int i = 0; i < propertyIds.size(); i++) {
                AccomType existingROHAccomType = (AccomType) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyIds.get(i), AccomType.GET_ROH_TYPE, QueryParameter.with("propertyId", propertyIds.get(i)).parameters());

                if (i == 0) {
                    commonRoomTypes.add(existingROHAccomType.getAccomTypeCode());
                } else {
                    if (!commonRoomTypes.contains(existingROHAccomType.getAccomTypeCode())) {
                        commonRoomTypes.remove(0);
                    }
                }
            }
        }

        List<SubLevelConfigDTO> listSubLevelConfigDTOs = new ArrayList<SubLevelConfigDTO>();
        SubLevelConfigDTO subLevelDTO = null;
        for (String accomTypeCode : commonRoomTypes) {
            subLevelDTO = new SubLevelConfigDTO();
            subLevelDTO.setCode(accomTypeCode);
            listSubLevelConfigDTOs.add(subLevelDTO);
        }

        return listSubLevelConfigDTOs;
    }


    @SuppressWarnings("unchecked")
    private List<AccomClass> getRoomClassesForProperty(Integer propertyId) {
        return multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId,
                AccomClass.ALL_ACTIVE_NON_DEFAULT_NONEMPTY, QueryParameter.with("propertyId", propertyId)
                        .parameters());
    }


    /**
     * This method returns the Market segments for multiple properties
     */
    @SuppressWarnings("unchecked")
    private List<SubLevelConfigDTO> getCommonMktSegmentsForProperties(List<Integer> propertyIds) {
        List<String> commonMktSegments = new ArrayList<String>();
        for (int i = 0; i < propertyIds.size(); i++) {
            List<String> result = multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyIds.get(i), MktSeg.CODES_BY_PROPERTY_ID, QueryParameter.with("propertyId", propertyIds.get(i)).parameters());
            if (i == 0) {
                commonMktSegments.addAll(result);
            } else {
                commonMktSegments.retainAll(result);
            }
        }

        List<SubLevelConfigDTO> listSubLevelConfigDTOs = new ArrayList<SubLevelConfigDTO>();
        SubLevelConfigDTO subLevelDTO = null;
        for (String code : commonMktSegments) {
            subLevelDTO = new SubLevelConfigDTO();
            subLevelDTO.setCode(code);
            listSubLevelConfigDTOs.add(subLevelDTO);
        }

        return listSubLevelConfigDTOs;
    }

    /**
     * This method returns the Business Type for Property
     *
     * @return
     */
    private List<SubLevelConfigDTO> getBusinessTypesForProperty() {
        List<BusinessType> listBusinessType = crudService.findAll(BusinessType.class);
        List<SubLevelConfigDTO> list = new ArrayList<SubLevelConfigDTO>();

        SubLevelConfigDTO subLevelDTO = null;
        for (BusinessType objBusinessType : listBusinessType) {
            subLevelDTO = new SubLevelConfigDTO();
            subLevelDTO.setCode(objBusinessType.getName());
            list.add(subLevelDTO);
        }
        return list;
    }

    /**
     * @param propertyIds
     * @return
     */
    private List<SubLevelConfigDTO> getCommomForecastGroupsForProperties(List<Integer> propertyIds) {
        List<String> commonFGs = new ArrayList<String>();
        for (int i = 0; i < propertyIds.size(); i++) {
            @SuppressWarnings("unchecked")
            List<String> result = multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyIds.get(i), ForecastGroup.NAME_BY_PROPERTY_ID, QueryParameter.with("propertyId", propertyIds.get(i)).parameters());
            if (i == 0) {
                commonFGs.addAll(result);
            } else {
                commonFGs.retainAll(result);
            }
        }
        List<SubLevelConfigDTO> list = new ArrayList<SubLevelConfigDTO>();

        SubLevelConfigDTO subLevelDTO = null;
        for (String code : commonFGs) {
            subLevelDTO = new SubLevelConfigDTO();
            subLevelDTO.setCode(code);
            list.add(subLevelDTO);
        }

        return list;
    }

    /**
     * @param propertyIds
     * @return
     */
    private List<SubLevelConfigDTO> getPBVForProperties(List<Integer> propertyIds) {
        List<String> commonBGs = new ArrayList<String>();
        for (int i = 0; i < propertyIds.size(); i++) {
            @SuppressWarnings("unchecked")
            List<String> result = multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyIds.get(i), BusinessGroup.NAME_BY_PROPERTY, QueryParameter.with("propertyId", propertyIds.get(i)).parameters());
            if (i == 0) {
                commonBGs.addAll(result);
            } else {
                commonBGs.retainAll(result);
            }
        }
        List<SubLevelConfigDTO> list = new ArrayList<SubLevelConfigDTO>();

        SubLevelConfigDTO subLevelDTO = null;
        for (String bgName : commonBGs) {
            subLevelDTO = new SubLevelConfigDTO();
            subLevelDTO.setCode(bgName);
            list.add(subLevelDTO);
        }

        return list;
    }


    /**
     * This method will identify the no of exception_configurations to be
     * created.
     * for e.g - Single property selected with Master Class or All Room classes Or All Competitors then
     * Map return <propertyId, List<Master Class code/ individual room classes, Individual competitors>>
     * same logic applied to the multiple property selection
     *
     * @param objExceptionConfigDTO
     * @return
     */
    private Map<Integer, List<String>> identifyNumberOfExceptionsToCreate(
            ExceptionConfigDTO objExceptionConfigDTO) {

        InformationMgrSubLevelEntity objSubLevel = null;
        if (objExceptionConfigDTO.isSubLevelHasKeyword()) {
            objSubLevel = (InformationMgrSubLevelEntity) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(objExceptionConfigDTO.getPropertyIds().get(0), InformationMgrSubLevelEntity.BY_DESC, QueryParameter.with("description", objExceptionConfigDTO.getExceptionSubLevel()).parameters());
        }
        if (objExceptionConfigDTO.getExceptionLevel().getName().equals(LevelType.COMPETITOR_SELECT.getCode())
                && null != objSubLevel && objSubLevel.getDescription()
                .equalsIgnoreCase(SubLevelType.ALL_COMPETITOR.getCode())) {
            return handlePropertyLevelCompetitors(objExceptionConfigDTO.getPropertyIds(), objExceptionConfigDTO.getProduct());

        }
        if (objExceptionConfigDTO.getExceptionLevel().getName().equals(LevelType.ROOM_CLASS.toString())) {
            return handleRoomClass(objExceptionConfigDTO.getPropertyIds(), objExceptionConfigDTO.getExceptionSubLevel(), objExceptionConfigDTO.isSubLevelHasKeyword());
        } else {
            // There is no Sub level when PROPERTY level is selected (when Configuration created for Overbooking)

            return handlePropertyLevelConfiguration(objExceptionConfigDTO.getPropertyIds());
        }
    }

    /**
     * This method has not been optimized for multi-property feature as it is going away.
     * To optimize it, a new query will be required with GROUP BY clause.
     *
     * @param propertyIds
     * @return
     */
    private Map<Integer, List<String>> handlePropertyLevelCompetitors(
            List<Integer> propertyIds, Product product) {
        Map<Integer, List<String>> propertyCompetitorsMap = new LinkedHashMap<Integer, List<String>>(propertyIds.size());
        List<Integer> tempList = new ArrayList<Integer>(1);
        tempList.add(0, propertyIds.get(0));
        for (Integer propertyId : propertyIds) {
            tempList.set(0, propertyId);
            List<Integer> property = new ArrayList<Integer>();
            property.add(propertyId);

            List<SubLevelConfigDTO> competitors = getCompetitros(property, product);

            List<Property> properties = globalCrudService.findByNamedQuery(Property.BY_IDS,
                    QueryParameter.with(Property.PARAM_PROPERTY_LIST_IDS, property).parameters());

            String webRateHotelId = configParamService.getParameterValue(properties.get(0).getClient().getCode(), properties.get(0).getCode(), IPConfigParamName.BAR_WEB_RATE_ALIAS);
            List<String> compIds = new ArrayList<String>();
            for (SubLevelConfigDTO dto : competitors) {
                WebrateCompetitors wc = getCompetitorByName(propertyId, dto.getCode());
                if (null != wc && webRateHotelId != null && !webRateHotelId.equalsIgnoreCase(wc.getWebrateHotelID())) {
                    compIds.add(Integer.toString(wc.getId()));
                } else {
                    LOGGER.error("Unable to get WebRate competitor by name for Property -" + propertyId);
                }
            }

            propertyCompetitorsMap.put(propertyId, compIds);
        }
        return propertyCompetitorsMap;
    }

    @SuppressWarnings("unchecked")
    private WebrateCompetitors getCompetitorByName(Integer propertyId, String name) {
        List<WebrateCompetitors> listCompetitors = multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, WebrateCompetitors.BY_PROPERTY_ID_AND_NAME, QueryParameter.with("competitorName", name)
                .and("propertyId", propertyId).parameters());
        if (CollectionUtils.isNotEmpty(listCompetitors)) {
            return listCompetitors.get(0);
        } else {
            return null;
        }
    }


    /**
     * In case of the Property Level Configuration the Sublevel would be Blank
     *
     * @param propertyIds
     * @return
     */
    private Map<Integer, List<String>> handlePropertyLevelConfiguration(List<Integer> propertyIds) {

        Map<Integer, List<String>> propertyRoomClassMap = new HashMap<Integer, List<String>>();
        for (Integer propertyId : propertyIds) {
            propertyRoomClassMap.put(propertyId, null);
        }
        return propertyRoomClassMap;
    }

    /**
     * This method identifies the No fo Room classes for which configuration has to be configured and returns the Map<property , List<Room Classes>>.
     *
     * @param propertyIds
     * @param exceptionSubLevel
     * @param subLevelAsKeyword
     * @return
     */
    private Map<Integer, List<String>> handleRoomClass(List<Integer> propertyIds, String exceptionSubLevel, boolean subLevelAsKeyword) {
        Map<Integer, List<String>> propertyRoomClassMap = new HashMap<Integer, List<String>>();
        List<String> roomClassList = null;

        InformationMgrSubLevelEntity objSubLevel = null;
        if (subLevelAsKeyword) {
            objSubLevel = (InformationMgrSubLevelEntity) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyIds.get(0), InformationMgrSubLevelEntity.BY_DESC, QueryParameter.with("description", exceptionSubLevel).parameters());
        }

        if (null != objSubLevel && objSubLevel.getDescription().equalsIgnoreCase(
                SubLevelType.ALL_ROOM_CLASSES.getCode())) {

            for (Integer propertyId : propertyIds) {
                roomClassList = new ArrayList<String>();
                List<AccomClass> lisAccomClasses = getRoomClassesForProperty(propertyId);

                for (AccomClass objAccomClass : lisAccomClasses) {
                    roomClassList.add(Integer.toString(objAccomClass.getId()));
                }
                propertyRoomClassMap.put(propertyId, roomClassList);
            }
        } else if (null != objSubLevel && objSubLevel.getDescription().equalsIgnoreCase(
                SubLevelType.MASTER_CLASS.getCode())) {
            // find master class

            for (Integer propertyId : propertyIds) {
                roomClassList = new ArrayList<String>();
                AccomClass masterClass = (AccomClass) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId,
                        AccomClass.GET_MASTER_CLASS,
                        QueryParameter.with("propertyId", propertyId).parameters());
                if (null != masterClass) {
                    roomClassList.add(Integer.toString(masterClass.getId()));
                } else {
                    roomClassList.add(SubLevelType.MASTER_CLASS.toString());
                }
                propertyRoomClassMap.put(propertyId, roomClassList);
            }
        } else {

            for (Integer propertyId : propertyIds) {
                roomClassList = new ArrayList<String>();
                // for Selected room class , This is when user select any
                // perticular room class/Type for creation
                AccomClass objAccomClass = getAccomClassByCode(propertyId, exceptionSubLevel);
                if (null != objAccomClass) {
                    roomClassList.add(objAccomClass.getId().toString());
                } else {
                    LOGGER.error("Unable to get Accom Class by code for property " + propertyId);
                }
                propertyRoomClassMap.put(propertyId, roomClassList);
            }
        }
        return propertyRoomClassMap;
    }

    @SuppressWarnings("unchecked")
    private AccomClass getAccomClassByCode(Integer propertyId, String code) {
        List<AccomClass> listAccomClasses = multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, AccomClass.BY_CODE, QueryParameter.with("code", code).and("propertyId", propertyId).parameters());
        if (CollectionUtils.isNotEmpty(listAccomClasses)) {
            return listAccomClasses.get(0);
        } else {
            return null;
        }
    }


    /**
     * This method will fetch the Exceptions created by logged in user has
     * access
     *
     * @return
     */
    @SuppressWarnings("unchecked")

    public List<InformationMgrAlertConfigEntity> findAllExceptions() {
        List<Integer> propertyIds = getPropertyIds();
        return findAllExceptionsConfiguredForProperties(propertyIds);
    }

    public List<InformationMgrAlertConfigEntity> findAllExceptionsConfiguredForProperties(List<Integer> propertyIds) {
        List<InformationMgrAlertConfigEntity> listAllException = multiPropertyCrudService.findByNamedQueryUnionAcrossProperties(propertyIds,
                InformationMgrAlertConfigEntity.FIND_BY_PROPERTY_ID, QueryParameter.with("propertyId", propertyIds).and("category", Constants.SYSTEM_EXCEPTION_CATEGORY).parameters());
        if (CollectionUtils.isEmpty(listAllException)) {
            return listAllException;
        }

        // Adding Property Code for Displaying on UI
        Map<Integer, Property> propertyMap = new HashMap<Integer, Property>();

        String displayPropertyCodeOrName = findDisplayPropertyCodeOrNameForClientContext();

        for (InformationMgrAlertConfigEntity objExceptionAlertConfigEntity : listAllException) {

            Property property = propertyMap.get(objExceptionAlertConfigEntity.getPropertyId());
            if (null != property) {
                setPropertyParamToExceAlertConfigEntity(displayPropertyCodeOrName, objExceptionAlertConfigEntity, property);
            } else {
                Property objTenantProperty = globalCrudService.find(Property.class, objExceptionAlertConfigEntity.getPropertyId());
                setPropertyParamToExceAlertConfigEntity(displayPropertyCodeOrName, objExceptionAlertConfigEntity, objTenantProperty);

                propertyMap.put(objExceptionAlertConfigEntity.getPropertyId(), objTenantProperty);
            }
            objExceptionAlertConfigEntity.setSubLevelDisplayName(getSubLevelCodeForId(objExceptionAlertConfigEntity));
        }
        return listAllException;
    }

    private void setPropertyParamToExceAlertConfigEntity(String codeOrName,
                                                         InformationMgrAlertConfigEntity objExceptionAlertConfigEntity,
                                                         Property objTenantProperty) {
        objExceptionAlertConfigEntity.setPropertyCode(objTenantProperty.getCode());
        //set name and display label field
        objExceptionAlertConfigEntity.setPropertyName(objTenantProperty.getName());
        String displayLabel = objTenantProperty.evaluateDisplayLabel(codeOrName);
        objExceptionAlertConfigEntity.setPropertyDisplayLabelField(displayLabel);
    }


    private String findDisplayPropertyCodeOrNameForClientContext() {
        String clientCode = PacmanWorkContextHelper.getClientCode();
        String clientContext = "pacman." + clientCode;
        return configParamService.getValue(clientContext,
                GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value());
    }

    /**
     * This method will update the Already configured exception.
     */

    public void updateExceptionConfiguration(InformationMgrAlertConfigEntity objExceptionAlertConfigEntity) {
        try {
            if (null != objExceptionAlertConfigEntity.getId()) {
                Integer subLevelIdForCode = (!isOverbookingChangeAlertType(objExceptionAlertConfigEntity.getAlertTypeEntity().getName()))
                        ? getSubLevelIdForCode(objExceptionAlertConfigEntity.getExceptionLevel(),
                        objExceptionAlertConfigEntity.getPropertyId(), objExceptionAlertConfigEntity.getSubLevelDisplayName())
                        : null;
                objExceptionAlertConfigEntity.setExceptionSubLevel(subLevelIdForCode);

                if (!checkForDuplicateExceptionConfiguration(objExceptionAlertConfigEntity, true)) {

                    InformationMgrAlertConfigEntity objDBExceptionAlertConfigEntity = multiPropertyCrudService.find(objExceptionAlertConfigEntity.getPropertyId(),
                            InformationMgrAlertConfigEntity.class, objExceptionAlertConfigEntity.getId());

                    objDBExceptionAlertConfigEntity.setAlertTypeEntity(objExceptionAlertConfigEntity
                            .getAlertTypeEntity());
                    objDBExceptionAlertConfigEntity.setDisabled(objExceptionAlertConfigEntity.isDisabled());
                    objDBExceptionAlertConfigEntity.setEndDate(objExceptionAlertConfigEntity.getEndDate());
                    objDBExceptionAlertConfigEntity.setExceptionLevel(objExceptionAlertConfigEntity.getExceptionLevel());
                    objDBExceptionAlertConfigEntity.setExceptionSubLevel(subLevelIdForCode);
                    objDBExceptionAlertConfigEntity.setExceptionSubType(objExceptionAlertConfigEntity
                            .getExceptionSubType());
                    objDBExceptionAlertConfigEntity.setFrequency(objExceptionAlertConfigEntity
                            .getFrequency());
                    objDBExceptionAlertConfigEntity.setPropertyId(objExceptionAlertConfigEntity
                            .getPropertyId());
                    objDBExceptionAlertConfigEntity.setStartDate(objExceptionAlertConfigEntity
                            .getStartDate());
                    objDBExceptionAlertConfigEntity
                            .setStatusId(objExceptionAlertConfigEntity.getStatusId());
                    objDBExceptionAlertConfigEntity.setThresholdMetricType(objExceptionAlertConfigEntity
                            .getThresholdMetricType());
                    objDBExceptionAlertConfigEntity.setThresholdOperator(objExceptionAlertConfigEntity
                            .getThresholdOperator());
                    objDBExceptionAlertConfigEntity.setThresholdValue(objExceptionAlertConfigEntity
                            .getThresholdValue());
                    objDBExceptionAlertConfigEntity.setSubLevelKeywordUsed(objExceptionAlertConfigEntity.isSubLevelKeywordUsed());
                    if (StringUtils.isNotEmpty(objExceptionAlertConfigEntity.getAdditionalConditions())) {
                        objDBExceptionAlertConfigEntity.setAdditionalConditions(objExceptionAlertConfigEntity.getAdditionalConditions());
                    }
                    multiPropertyCrudService.save(objExceptionAlertConfigEntity.getPropertyId(), objDBExceptionAlertConfigEntity);

                    informationManagerCleanUpService.deleteRaisedExceptionsForConfiguration(objExceptionAlertConfigEntity.getPropertyId(), objDBExceptionAlertConfigEntity.getId());

                } else {
                    throw new TetrisException(ErrorCode.DUPLICATE_DATA, "Duplicate entry found for Exeception Configuration.");
                }
            }
        } catch (TetrisException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("Unable to Update Notification Configuration", e);
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Unable to Update Notification Configuration");
        }
    }

    public void updateExceptionConfigurationForNotification(InformationMgrAlertConfigEntity objExceptionAlertConfigEntity) {
        try {
            if (null != objExceptionAlertConfigEntity.getId()) {
                objExceptionAlertConfigEntity.setExceptionSubLevel(getSubLevelIdForCode(objExceptionAlertConfigEntity.getExceptionLevel(),
                        objExceptionAlertConfigEntity.getPropertyId(), objExceptionAlertConfigEntity.getSubLevelDisplayName()));

                if (!checkForDuplicateExceptionConfigurationForNotification(objExceptionAlertConfigEntity, true)) {

                    InformationMgrAlertConfigEntity objDBExceptionAlertConfigEntity = multiPropertyCrudService.find(objExceptionAlertConfigEntity.getPropertyId(),
                            InformationMgrAlertConfigEntity.class, objExceptionAlertConfigEntity.getId());

                    objDBExceptionAlertConfigEntity.setAlertTypeEntity(objExceptionAlertConfigEntity
                            .getAlertTypeEntity());
                    objDBExceptionAlertConfigEntity.setDisabled(objExceptionAlertConfigEntity.isDisabled());
                    objDBExceptionAlertConfigEntity.setEndDate(objExceptionAlertConfigEntity.getEndDate());
                    objDBExceptionAlertConfigEntity.setExceptionLevel(objExceptionAlertConfigEntity.getExceptionLevel());
                    objDBExceptionAlertConfigEntity.setExceptionSubLevel(getSubLevelIdForCode(objExceptionAlertConfigEntity.getExceptionLevel(),
                            objExceptionAlertConfigEntity.getPropertyId(), objExceptionAlertConfigEntity.getSubLevelDisplayName()));
                    objDBExceptionAlertConfigEntity.setExceptionSubType(objExceptionAlertConfigEntity
                            .getExceptionSubType());
                    objDBExceptionAlertConfigEntity.setFrequency(objExceptionAlertConfigEntity
                            .getFrequency());
                    objDBExceptionAlertConfigEntity.setPropertyId(objExceptionAlertConfigEntity
                            .getPropertyId());
                    objDBExceptionAlertConfigEntity.setStartDate(objExceptionAlertConfigEntity
                            .getStartDate());
                    objDBExceptionAlertConfigEntity
                            .setStatusId(objExceptionAlertConfigEntity.getStatusId());
                    objDBExceptionAlertConfigEntity.setThresholdMetricType(objExceptionAlertConfigEntity
                            .getThresholdMetricType());
                    objDBExceptionAlertConfigEntity.setThresholdOperator(objExceptionAlertConfigEntity
                            .getThresholdOperator());
                    objDBExceptionAlertConfigEntity.setThresholdValue(objExceptionAlertConfigEntity
                            .getThresholdValue());
                    if(objDBExceptionAlertConfigEntity.getProduct() !=null) {
                        objDBExceptionAlertConfigEntity.setProduct(objExceptionAlertConfigEntity
                                .getProduct());
                    }
                    objDBExceptionAlertConfigEntity.setSubLevelKeywordUsed(objExceptionAlertConfigEntity.isSubLevelKeywordUsed());
                    if (StringUtils.isNotEmpty(objExceptionAlertConfigEntity.getAdditionalConditions())) {
                        objDBExceptionAlertConfigEntity.setAdditionalConditions(objExceptionAlertConfigEntity.getAdditionalConditions());
                    }
                    multiPropertyCrudService.save(objExceptionAlertConfigEntity.getPropertyId(), objDBExceptionAlertConfigEntity);

                    informationManagerCleanUpService.deleteRaisedExceptionsForConfiguration(objExceptionAlertConfigEntity.getPropertyId(), objDBExceptionAlertConfigEntity.getId());

                } else {
                    throw new TetrisException(ErrorCode.DUPLICATE_DATA, "Duplicate entry found for Exeception Configuration.");
                }
            }
        } catch (TetrisException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("Unable to Update Notification Configuration", e);
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Unable to Update Notification Configuration");
        }
    }


    /**
     * This method disables the Exception configuration and Deletes the raised
     * exception based on the User input (deleteExceptionsRaised).
     *
     * @param exceptionConfigId
     */

    public void disableExceptionConfiguration(Integer propertyId, Integer exceptionConfigId,
                                              boolean deleteExceptionsRaised, boolean disbledState) {
        InformationMgrAlertConfigEntity objExceptionAlertConfigEntity = multiPropertyCrudService.find(propertyId,
                InformationMgrAlertConfigEntity.class, exceptionConfigId);
        objExceptionAlertConfigEntity.setDisabled(disbledState);
        multiPropertyCrudService.save(propertyId, objExceptionAlertConfigEntity);

        //If user seleted to erase the raised exceptions.
        if (deleteExceptionsRaised && disbledState) {
            informationManagerCleanUpService.deleteRaisedExceptionsForConfiguration(propertyId, exceptionConfigId);
        }
    }

    protected boolean checkForDuplicateExceptionConfiguration(InformationMgrAlertConfigEntity objExceptionAlertConfigEntity) {
        return checkForDuplicateExceptionConfiguration(objExceptionAlertConfigEntity, false);
    }
    protected boolean checkForDuplicateExceptionConfigurationForNotification(InformationMgrAlertConfigEntity objExceptionAlertConfigEntity) {
        return checkForDuplicateExceptionConfigurationForNotification(objExceptionAlertConfigEntity, false);
    }

    /**
     * This method will check for the duplication of the Exception rule being created
     *
     * @param objExceptionAlertConfigEntity
     * @return
     */
    @SuppressWarnings("unchecked")
    protected boolean checkForDuplicateExceptionConfiguration(InformationMgrAlertConfigEntity objExceptionAlertConfigEntity, boolean isEdit) {

        // Check for the existing rules with type, subtype, level and sub level
        boolean isDuplicate = false;

        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("alertTypeId", objExceptionAlertConfigEntity.getAlertTypeEntity().getId());
        parameters.put("exceptionSubTypeId", objExceptionAlertConfigEntity.getExceptionSubType().getId());
        parameters.put("exceptionLevelId", objExceptionAlertConfigEntity.getExceptionLevel().getId());
        parameters.put("exceptionSubLevel", objExceptionAlertConfigEntity.getExceptionSubLevel());
        parameters.put("thresholdValue", objExceptionAlertConfigEntity.getThresholdValue());
        parameters.put("thresholdOperator", objExceptionAlertConfigEntity.getThresholdOperator());
        parameters.put("thresholdMetricType", objExceptionAlertConfigEntity.getThresholdMetricType());
        parameters.put("startDate", objExceptionAlertConfigEntity.getStartDate());
        parameters.put("endDate", objExceptionAlertConfigEntity.getEndDate());
        parameters.put("propertyId", objExceptionAlertConfigEntity.getPropertyId());
        parameters.put("frequency", objExceptionAlertConfigEntity.getFrequency());
        parameters.put("subLevelKeywordUsed", objExceptionAlertConfigEntity.isSubLevelKeywordUsed());

        List<InformationMgrAlertConfigEntity> listExistingRulesInDB = multiPropertyCrudService.findByNamedQueryForSingleProperty(objExceptionAlertConfigEntity.getPropertyId(), InformationMgrAlertConfigEntity.FIND_WITH_CRITERIA, parameters);

        if (!listExistingRulesInDB.isEmpty()) {
            if (isCPCNotificationByRoomClassEnabled(objExceptionAlertConfigEntity.getPropertyCode())) {
                isDuplicate = checkDuplicateExceptionConfigurationForAdditionalConditions(objExceptionAlertConfigEntity, listExistingRulesInDB, isEdit);
            } else {
                isDuplicate = true;
            }
        }

        return isDuplicate;
    }

    @SuppressWarnings("unchecked")
    protected boolean checkForDuplicateExceptionConfigurationForNotification(InformationMgrAlertConfigEntity objExceptionAlertConfigEntity, boolean isEdit) {

        // Check for the existing rules with type, subtype, level and sub level
        boolean isDuplicate = false;
        List<InformationMgrAlertConfigEntity> listExistingRulesInDB = null;
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("alertTypeId", objExceptionAlertConfigEntity.getAlertTypeEntity().getId());
        parameters.put("exceptionSubTypeId", objExceptionAlertConfigEntity.getExceptionSubType().getId());
        parameters.put("exceptionLevelId", objExceptionAlertConfigEntity.getExceptionLevel().getId());
        parameters.put("exceptionSubLevel", objExceptionAlertConfigEntity.getExceptionSubLevel());
        parameters.put("thresholdValue", objExceptionAlertConfigEntity.getThresholdValue());
        parameters.put("thresholdOperator", objExceptionAlertConfigEntity.getThresholdOperator());
        parameters.put("thresholdMetricType", objExceptionAlertConfigEntity.getThresholdMetricType());
        parameters.put("startDate", objExceptionAlertConfigEntity.getStartDate());
        parameters.put("endDate", objExceptionAlertConfigEntity.getEndDate());
        parameters.put("propertyId", objExceptionAlertConfigEntity.getPropertyId());
        parameters.put("frequency", objExceptionAlertConfigEntity.getFrequency());
        parameters.put("subLevelKeywordUsed", objExceptionAlertConfigEntity.isSubLevelKeywordUsed());
        Optional<Integer> productId = Optional.ofNullable(objExceptionAlertConfigEntity.getProduct())
                .map(Product::getId);

        if (productId.isPresent()) {
            parameters.put("product", productId.get());
            listExistingRulesInDB = multiPropertyCrudService.findByNamedQueryForSingleProperty(
                    objExceptionAlertConfigEntity.getPropertyId(),
                    InformationMgrAlertConfigEntity.FIND_WITH_PRODUCT_CRITERIA,
                    parameters
            );
        } else {
            listExistingRulesInDB = multiPropertyCrudService.findByNamedQueryForSingleProperty(
                    objExceptionAlertConfigEntity.getPropertyId(),
                    InformationMgrAlertConfigEntity.FIND_WITH_CRITERIA,
                    parameters
            );
        }
        if (!listExistingRulesInDB.isEmpty()) {
            if (isCPCNotificationByRoomClassEnabled(objExceptionAlertConfigEntity.getPropertyCode())) {
                isDuplicate = checkDuplicateExceptionConfigurationForAdditionalConditions(objExceptionAlertConfigEntity, listExistingRulesInDB, isEdit);
            } else {
                isDuplicate = true;
            }
        }

        return isDuplicate;
    }
    private boolean checkDuplicateExceptionConfigurationForAdditionalConditions(InformationMgrAlertConfigEntity objExceptionAlertConfigEntity, List<InformationMgrAlertConfigEntity> listExistingRulesInDB, boolean isEdit) {
        boolean isDuplicate;
        List<InformationMgrAlertConfigEntity> existingExceptionWithAdditionalConditions = listExistingRulesInDB.stream()
                .filter(alertConfig -> StringUtils.isNotEmpty(alertConfig.getAdditionalConditions()))
                .collect(Collectors.toList());
        if (StringUtils.isEmpty(objExceptionAlertConfigEntity.getAdditionalConditions()) &&
                (existingExceptionWithAdditionalConditions.isEmpty() ||
                        existingExceptionWithAdditionalConditions.size() < listExistingRulesInDB.size())) {
            isDuplicate = true;
        } else if (StringUtils.isNotEmpty(objExceptionAlertConfigEntity.getAdditionalConditions()) &&
                existingExceptionWithAdditionalConditions.isEmpty()) {
            isDuplicate = false;
        } else {
            isDuplicate = compareAdditionalConditions(objExceptionAlertConfigEntity, isEdit, existingExceptionWithAdditionalConditions);
        }
        return isDuplicate;
    }

    private boolean compareAdditionalConditions(InformationMgrAlertConfigEntity objExceptionAlertConfigEntity, boolean isEdit, List<InformationMgrAlertConfigEntity> existingExceptionWithAdditionalConditions) {
        Map<String, String> newExceptionAdditionalConditions = getAdditionalConditionsMap(objExceptionAlertConfigEntity.getAdditionalConditions());
        for (InformationMgrAlertConfigEntity alertConfig : existingExceptionWithAdditionalConditions) {
            Map<String, String> additionalConditionsMap = getAdditionalConditionsMap(alertConfig.getAdditionalConditions());
            if (newExceptionAdditionalConditions.size() == additionalConditionsMap.size()) {
                if (newExceptionAdditionalConditions.entrySet().stream()
                        .allMatch(e -> e.getValue().trim().equals(additionalConditionsMap.get(e.getKey()).trim())) ||
                        (checkAllRoomClassVsSpecificRoomClass(newExceptionAdditionalConditions, additionalConditionsMap) &&
                                (!isEdit || !alertConfig.getId().equals(objExceptionAlertConfigEntity.getId())))) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean checkAllRoomClassVsSpecificRoomClass(Map<String, String> newExceptionAdditionalConditions, Map<String, String> existingExceptionAdditionalConditionsMap) {
        String newExceptionRoomClassId = newExceptionAdditionalConditions.get(ADDITIONAL_CONDITION_PARAM_ROOM_CLASS_ID);
        String existingExceptionRoomClassId = existingExceptionAdditionalConditionsMap.get(ADDITIONAL_CONDITION_PARAM_ROOM_CLASS_ID);
        return StringUtils.isNotEmpty(newExceptionRoomClassId) && StringUtils.isNotEmpty(existingExceptionRoomClassId) &&
                (Integer.parseInt(newExceptionRoomClassId) == -1 ^ Integer.parseInt(existingExceptionRoomClassId) == -1);
    }

    public boolean isCPCNotificationByRoomClassEnabled(String propertyCode) {
        if (StringUtils.isNotEmpty(propertyCode)) {
            return getConfigParamService().getBooleanParameterValue(
                    PreProductionConfigParamName.ENABLE_CPC_NOTIFICATION_BY_ROOM_CLASS.getParameterName(),
                    PacmanWorkContextHelper.getClientCode(), propertyCode);
        }
        return Boolean.FALSE;
    }

    private Map<String, String> getAdditionalConditionsMap(String additionalConditions) {
        Map<String, String> map = new TreeMap<>();
        if (StringUtils.isNotEmpty(additionalConditions)) {
            List<String> additionalConditionsArray = Arrays.asList(additionalConditions.split(ADDITIONAL_CONDITIONS_SEPARATOR));
            if (CollectionUtils.isNotEmpty(additionalConditionsArray)) {
                additionalConditionsArray.forEach(condition -> {
                    String[] conditionArray = (condition.split(ADDITIONAL_CONDITIONS_PARAM_VALUE_SEPARATOR));
                    if (conditionArray.length == 2) {
                        map.put(conditionArray[0], conditionArray[1]);
                    }
                });
            }
        }
        return map;
    }

    /**
     * This function returns the Matched Exception configuration(s) based on the Search String.
     * input search String can be - PropertyCode, Exception Type, Exception Sub Type, Exception Level, Exception Sub level
     */
    @SuppressWarnings("unchecked")

    public List<InformationMgrAlertConfigEntity> searchExceptionConfiguration(String searchString) {
        //Find propertyid based on the search String
        String srh = "%".concat(searchString.toUpperCase()).concat("%");
        List<Property> listProperties = globalCrudService.findByNamedQuery(Property.BY_CODE_LIKE, QueryParameter.with("code", srh).parameters());

        List<Integer> propertyIds = new ArrayList<Integer>();
        for (Property objTenantProperty : listProperties) {
            propertyIds.add(objTenantProperty.getId());
        }

        List<InformationMgrAlertConfigEntity> foundExceptionConfigrations = crudService.findByNamedQuery(InformationMgrAlertConfigEntity.SEARCH_BY_STRING,
                QueryParameter.with("searchString", "%".concat(searchString).concat("%")).and("propertyId", (propertyIds.isEmpty()) ? null : propertyIds).parameters());

        //Adding propertyCode into search result
        for (InformationMgrAlertConfigEntity objExceptionAlertConfigEntity : foundExceptionConfigrations) {
            Property property = globalCrudService.find(Property.class, objExceptionAlertConfigEntity.getPropertyId());
            objExceptionAlertConfigEntity.setPropertyCode(property.getCode());
        }
        return foundExceptionConfigrations;
    }

    private static String BASE_QUERY = "select * from Info_Mgr_Excep_Notif_Config config inner join Info_Mgr_Type imt on config.Info_Mgr_Type_ID = imt.Info_Mgr_Type_ID " +
            " where imt.Name not in (:systemExceptionTypes) and config.status_id = 1 ";
    private static String WITH_PROPERTIES = " and config.property_id in (:propertyId)";
    private static String WITH_EXCEPTION_TYPES = " and config.Info_Mgr_Type_Id in (:exceptionTypes)";
    private static String WITH_EXCEPTION_SUB_TYPES = " and config.Info_Mgr_Excep_Notif_Sub_Type_ID in (:exceptionSubTypes)";
    private static String WITH_EXCEPTION_LEVEL = " and config.Info_Mgr_Excep_Notif_Level_ID in (:exceptionLevel)";
    private static String WITH_EXCEPTION_SUBLEVELS = " and config.Info_Mgr_Excep_Notif_Sub_Level in (:exceptionSubLevels)";

    /**
     * This function returns the Matched Exception configuration(s) based on the Search Criteria selected by User.
     * Search Criteria contains - Properties, Exception Type, Exception subType and .......
     *
     * @return
     */
    @SuppressWarnings("unchecked")

    public List<InformationMgrAlertConfigEntity> searchExceptionConfigurationWithCriteria(SearchCriteriaDTO searchCriteria) {

        List<InformationMgrAlertConfigEntity> listFoundExceptionConfigurations = null;

        StringBuilder queryStr = new StringBuilder(BASE_QUERY);
        queryStr.append(WITH_PROPERTIES);
        if (CollectionUtils.isNotEmpty(searchCriteria.getNotificationTypes())) {
            queryStr.append(WITH_EXCEPTION_TYPES);

        }
        if (CollectionUtils.isNotEmpty(searchCriteria.getSubTypes())) {
            queryStr.append(WITH_EXCEPTION_SUB_TYPES);

        }
        if (CollectionUtils.isNotEmpty(searchCriteria.getLevel())) {
            queryStr.append(WITH_EXCEPTION_LEVEL);

        }
        if (CollectionUtils.isNotEmpty(searchCriteria.getSubLevels())) {
            queryStr.append(WITH_EXCEPTION_SUBLEVELS);

        }

        List<Integer> propertyIds = new ArrayList<Integer>();

        if (CollectionUtils.isNotEmpty(searchCriteria.getPropertyIds())) {
            propertyIds.addAll(searchCriteria.getPropertyIds());
        } else {
            propertyIds = getPropertyIds();
        }

        listFoundExceptionConfigurations = multiPropertyCrudService.findByNativeQueryUnionAcrossProperties(propertyIds, queryStr.toString(), populateParameters(searchCriteria, propertyIds), InformationMgrAlertConfigEntity.class);

        listFoundExceptionConfigurations = addPropertyCodeToResult(listFoundExceptionConfigurations);

        return listFoundExceptionConfigurations;
    }

    /**
     * This method adds the property code to the configuration
     *
     * @param listFoundExceptionConfigurations
     * @return
     */
    public List<InformationMgrAlertConfigEntity> addPropertyCodeToResult(List<InformationMgrAlertConfigEntity> listFoundExceptionConfigurations) {
        //Adding propertyCode into search result
        Map<Integer, Property> propertyMap = new HashMap<Integer, Property>();
        String displayPropertyCodeOrName = findDisplayPropertyCodeOrNameForClientContext();
        for (InformationMgrAlertConfigEntity objExceptionAlertConfigEntity : listFoundExceptionConfigurations) {

            Property objProperty = propertyMap.get(objExceptionAlertConfigEntity.getPropertyId());
            if (null != objProperty) {
                objExceptionAlertConfigEntity.setPropertyCode(objProperty.getCode());
                objExceptionAlertConfigEntity.setPropertyDisplayLabelField(objProperty.evaluateDisplayLabel(displayPropertyCodeOrName));
                objExceptionAlertConfigEntity.setPropertyName(objProperty.getName());
            } else {
                Property objTenantProperty = globalCrudService.find(Property.class, objExceptionAlertConfigEntity.getPropertyId());
                objExceptionAlertConfigEntity.setPropertyCode(objTenantProperty.getCode());
                String displayLabel = objTenantProperty.evaluateDisplayLabel(displayPropertyCodeOrName);
                objExceptionAlertConfigEntity.setPropertyDisplayLabelField(displayLabel);
                objTenantProperty.setDisplayLabelField(displayLabel);
                propertyMap.put(objExceptionAlertConfigEntity.getPropertyId(), objTenantProperty);
                objExceptionAlertConfigEntity.setPropertyName(objTenantProperty.getName());
            }
            objExceptionAlertConfigEntity.setSubLevelDisplayName(getSubLevelForId(objExceptionAlertConfigEntity));
        }
        return listFoundExceptionConfigurations;
    }

    /**
     * This method returns the List of Authorized properties for logged in User
     *
     * @return
     */
    public List<Integer> getPropertyIds() {
        List<Integer> propertyIds = new ArrayList<Integer>();
        List<Property> properties = authorizationService.retrieveAuthorizedProperties();

        if (properties != null) {
            for (Property property : properties) {
                propertyIds.add(property.getId());
            }
        }
        return propertyIds;
    }


    private Map<String, Object> populateParameters(SearchCriteriaDTO searchCriteria, List<Integer> propertyIds) {

        Map<String, Object> parameters = new HashMap<String, Object>();
        List<String> systemExceptionsTypes = new ArrayList<String>();
        systemExceptionsTypes.add(AlertType.LRVExceedingUserDefinedBAR.toString());
        systemExceptionsTypes.add(AlertType.LRVExceedingCeilingBAR.toString());
        systemExceptionsTypes.add(AlertType.OOORoomsAffectingDemand.toString());
        systemExceptionsTypes.add(AlertType.StraightLineAvailabilityEx.toString());
        systemExceptionsTypes.add(AlertType.CPCeilingOverrideBelowLRV.toString());
        systemExceptionsTypes.add(AlertType.CPUserOverrideBelowLRV.toString());
        systemExceptionsTypes.add(AlertType.InsufficientCompetitorRatesForCompetitiveMarket.toString());
        parameters.put("systemExceptionTypes", systemExceptionsTypes);
        parameters.put("propertyId", propertyIds);

        if (CollectionUtils.isNotEmpty(searchCriteria.getNotificationTypes())) {
            parameters.put("exceptionTypes", searchCriteria.getNotificationTypes());
        }
        if (CollectionUtils.isNotEmpty(searchCriteria.getSubTypes())) {
            parameters.put("exceptionSubTypes", searchCriteria.getSubTypes());
        }
        if (CollectionUtils.isNotEmpty(searchCriteria.getLevel())) {
            parameters.put("exceptionLevel", searchCriteria.getLevel());
        }
        if (CollectionUtils.isNotEmpty(searchCriteria.getSubLevels())) {
            parameters.put("exceptionSubLevels", searchCriteria.getSubLevels());
        }
        return parameters;
    }

    /**
     * This method returns max threshold value which can be entered by user to
     * create exception configuration. Typically, it is different for different
     * properties. But according to current decision, the max value will be
     * selected. When UI is enabled to enter different threshold for each
     * property, then Map<PropertyId,MaxThresholdValue> should be returned.
     *
     * @param propertyIds
     * @return Threshold for PricePoint exception
     */
    @SuppressWarnings("unchecked")

    public RateLevelThresholdDTO getPricePointThreshold(List<Integer> propertyIds) {
        List<Object[]> resultList = multiPropertyCrudService
                .findByNamedQueryUnionAcrossProperties(propertyIds,
                        RateUnqualified.GET_MAX_RANKING_LEVEL, QueryParameter
                                .with("propertyId", propertyIds).parameters());

        if (resultList == null) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Unable to find PricePoint rank.");
        }

        RateLevelThresholdDTO dto = new RateLevelThresholdDTO();
        if (resultList.isEmpty()) {
            dto.getNoRateProperties().addAll(propertyIds);
            dto.setThreshold(new BigDecimal(-1));
        } else {
            // Query result returns max_ranking_level of property with least number
            // of ranking levels. So the threshold = query result - 1.
            Integer threshold = ((Integer) (resultList.get(0)[1])).intValue() - 1;
            dto.setThreshold(new BigDecimal(threshold));

            for (Integer propertyId : propertyIds) {
                boolean propertyNotInResults = true;
                for (Object[] result : resultList) {
                    if (propertyId.equals(result[0])) {
                        propertyNotInResults = false;
                    }
                }
                if (propertyNotInResults) {
                    dto.getNoRateProperties().add(propertyId);
                }
            }
        }
        return dto;
    }

    /**
     * This method deletes the instances and respective configuration which are impacted ROH changes/remove
     *
     * @param isFirstTimeOrChangeInROHRT
     * @param removeROH
     */
    public void clearConfigAndInstancesAfterROHChanges(boolean isFirstTimeOrChangeInROHRT, boolean removeROH) {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        try {
            InformationMgrSubTypeEntity subType = (InformationMgrSubTypeEntity) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId,
                    InformationMgrSubTypeEntity.BY_NAME, QueryParameter.with("name", ExceptionSubType.OVERBOOKING.getCode()).parameters());

            InformationMgrLevelEntity level = (InformationMgrLevelEntity) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId,
                    InformationMgrLevelEntity.BY_NAME, QueryParameter.with("name", LevelType.ROOM_TYPE.getCode()).parameters());

            // find the configuration @overbooking and @room Type level
            @SuppressWarnings("unchecked")
            List<InformationMgrAlertConfigEntity> listConfig = multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, InformationMgrAlertConfigEntity.BY_PROPERTY_SUBTYPE_LEVEL,
                    QueryParameter.with("propertyId", propertyId).and("subTypeId", subType.getId()).and("levelId", level.getId()).parameters());

            if (isFirstTimeOrChangeInROHRT) {

                for (InformationMgrAlertConfigEntity config : listConfig) {
                    String subLevel = getSubLevelForId(config);
                    if (!subLevel.equalsIgnoreCase(SubLevelType.ROH_ROOM_TYPE.getCode())) {
                        LOGGER.debug("Setting First Time or Change in ROH, Scenario - deleting the instances and configuration for " + config.getId() + " and SubLevel " + config.getExceptionSubLevel());
                        informationManagerCleanUpService.deleteExceptionConfiguration(propertyId, config.getId());

                    } else {
                        LOGGER.debug("Setting First Time or Change in ROH, Scenario - Configuration not qualified for after ROH changes clean up - " + config.getId() + " sublevel - " + config.getExceptionSubLevel());
                    }
                }
            }
            if (removeROH) {
                //delete configuration and instances for ROH Room Type keyword and actual ROH room type removed
                for (InformationMgrAlertConfigEntity config : listConfig) {
                    LOGGER.debug("Remove ROH Scenario - deleting the instances and configuration for " + config.getId() + " and SubLevel " + config.getExceptionSubLevel());
                    informationManagerCleanUpService.deleteExceptionConfiguration(propertyId, config.getId());
                }
            }
        } catch (Exception e) {
            LOGGER.error("Unable to clear configurations and instances after ROH configuration changes", e);
        }
    }

    @Override
    public void setGlobalCrudService(CrudService globalCrudService) {
        this.globalCrudService = globalCrudService;
    }


    public List<Status> getGlobalStatus() {
        List<Status> activeInactiveList = new ArrayList<Status>();
        activeInactiveList.add(Status.ACTIVE);
        activeInactiveList.add(Status.INACTIVE);
        return activeInactiveList;
    }

    public List<DayOfWeek> getDows() {
        List<DayOfWeek> dayOfWeeks = new ArrayList<>();
        dayOfWeeks.add(DayOfWeek.SUNDAY);
        dayOfWeeks.add(DayOfWeek.THURSDAY);
        dayOfWeeks.add(DayOfWeek.MONDAY);
        dayOfWeeks.add(DayOfWeek.FRIDAY);
        dayOfWeeks.add(DayOfWeek.TUESDAY);
        dayOfWeeks.add(DayOfWeek.SATURDAY);
        dayOfWeeks.add(DayOfWeek.WEDNESDAY);
        return dayOfWeeks;
    }

    public InformationMgrAlertConfigEntity getConfigById(Integer configId, Integer propertyId) {
        InformationMgrAlertConfigEntity configEntity = multiPropertyCrudService.find(propertyId, InformationMgrAlertConfigEntity.class, configId);
        if (null != configEntity) {
            return configEntity;
        }
        return null;
    }

    /**
     * This method returns the Valid Start date and End date for PACE YoY configuration
     */

    public Map<String, DateParameter> getDatesForMonitoringWindow(List<Integer> propertyIds, String alertType) {
        // max optimization window end date
        Map<String, DateParameter> mwDates = new HashMap<String, DateParameter>();
        try {
            Date maxCaughtUpDate = dateService.getMaxCaughtUpDate(propertyIds);
            List<Integer> optWindowSizes = new ArrayList<Integer>();
            List<Date> datesList = new ArrayList<Date>();

            for (Integer propertyId : propertyIds) {
                Property objProperty = globalCrudService.find(Property.class, propertyId);

                StringBuilder nodeName = new StringBuilder();
                nodeName.append(Constants.CONFIG_PARAMS_NODE_PREFIX).append(".")
                        .append(objProperty.getClient().getCode()).append(".")
                        .append(objProperty.getCode());
                optWindowSizes.add(Integer.parseInt(configParamService.getValue(nodeName.toString(), IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE.value())));

                List<Object[]> objFileMetadata = multiPropertyCrudService.findByNativeQueryForSingleProperty(propertyId, "select top 1 * from File_Metadata where Record_Type_ID =:recordTypeId and IsBDE = 1 " +
                        " and Process_Status_ID = 13 order by SnapShot_DT", QueryParameter.with("recordTypeId", "3").parameters());
                if (CollectionUtils.isNotEmpty(objFileMetadata)) {
                    try {
                        datesList.add(DateUtil.parseDate(objFileMetadata.get(0)[10].toString(), "yyyy-MM-dd"));
                    } catch (ParseException e) {
                        LOGGER.error("Unable to parse date: " + objFileMetadata.get(0)[10].toString(), e);
                    }
                }
            }

            Integer max = Collections.max(optWindowSizes);
            Date dateFinal = DateUtil.addDaysToDate(maxCaughtUpDate, max);
            mwDates.put("maxEndDate", new DateParameter(dateFinal));

            // successful first bde population date
            Date maxDate = null;
            for (Date date : datesList) {
                Date currentCaughtUpDate = date;
                if (maxDate == null || currentCaughtUpDate.after(maxDate)) {
                    maxDate = currentCaughtUpDate;
                }
            }
            if (maxDate == null) {
                maxDate = dateService.getCurrentDate();
            }

            if (alertType.equalsIgnoreCase(AlertType.YOYPaceChangeEx.toString())) {
                maxDate = DateUtil.addDaysToDate(maxDate, 365);

                if (maxDate.before(maxCaughtUpDate)) {
                    mwDates.put("maxStartDate", new DateParameter(maxCaughtUpDate));
                } else {
                    mwDates.put("maxStartDate", new DateParameter(maxDate));
                }
            } else {
                if (null != maxCaughtUpDate) {
                    mwDates.put("maxStartDate", new DateParameter(maxCaughtUpDate));
                }
            }
            return mwDates;
        } catch (Exception e) {
            LOGGER.error("Unable to get Monitoring dates for Notification configuration" + e);
            return mwDates;
        }
    }


    public void deleteExceptionConfiguration(Integer propertyId, Integer exceptionConfigId) {
        informationManagerCleanUpService.deleteExceptionConfiguration(propertyId, exceptionConfigId);
    }


    public void deleteRaisedExceptionsForConfiguration(Integer propertyId, Integer exceptionConfigId) {
        informationManagerCleanUpService.deleteRaisedExceptionsForConfiguration(propertyId, exceptionConfigId);

    }

    public PacmanConfigParamsService getConfigParamService() {
        return configParamService;
    }

    public void setConfigParamService(
            PacmanConfigParamsService configParamService) {
        this.configParamService = configParamService;
    }

    public boolean shouldUnexpectedDemandNotificationBeDisplayed(List<Integer> selectedPropertyIds) {
        for (Integer propertyId : selectedPropertyIds) {
            if (shouldUnexpectedDemandNotificationBeDisplayed(propertyId)) {
                return true;
            }
        }
        return false;
    }

    public boolean shouldUnexpectedDemandNotificationBeDisplayed(Integer propertyId) {
        boolean isUnexpectedDemandNotificationEnabled = getParameterValue(propertyId, FeatureTogglesConfigParamName.UNEXPECTED_DEMAND_NOTIFICATION_ENABLED.value());
        boolean isLDBEnabled = getParameterValue(propertyId, IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED.value());
        return isUnexpectedDemandNotificationEnabled && !isLDBEnabled;
    }

    public boolean shouldBookingPaceNotificationBeDisplayed(List<Integer> selectedPropertyIds) {
        for (Integer propertyId : selectedPropertyIds) {
            if (shouldBookingPaceNotificationBeDisplayed(propertyId)) {
                return true;
            }
        }
        return false;
    }

    public boolean shouldBookingPaceNotificationBeDisplayed(Integer propertyId) {
        boolean isLDBEnabled = getParameterValue(propertyId, IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED.value());
        return !isLDBEnabled;
    }

    public boolean getParameterValue(Integer propertyId, String paramName) {
        String clientCode = PacmanWorkContextHelper.getClientCode();
        String propertyCode = getPropertyCode(propertyId);
        return configParamService.getBooleanParameterValue(paramName, clientCode, propertyCode);
    }

    public List<ProductAccomType> findProductRoomTypesByProduct(Product product, Integer propertyId) {
        return findAllByProduct(product, ProductAccomType.BY_PRODUCT, propertyId);
    }

    private <T> List<T> findAllByProduct(Product product, String namedQuery, Integer propertyId) {
        return multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId,
                namedQuery,
                QueryParameter.with("product", product).parameters());
    }

    public List<InfoMgrTypeEntity> getAllNotificationTypesByNamesAndCategory(Set<String> names, String category) {
        return crudService.findByNamedQuery(
                InfoMgrTypeEntity.BY_NAMES_AND_CATEGORY_ORDERED_DISPLAY, QueryParameter.with("categoryName", category).and("names", names)
                        .parameters());
    }

    public InfoMgrTypeEntity getNotificationTypesByName(String notificationType) {
        return tenantCrudService.findByNamedQuerySingleResult(InfoMgrTypeEntity.BY_NAME, QueryParameter.with("name", notificationType).parameters());
    }

    public InformationMgrSubTypeEntity getNotificationSubTypeByName(String notificationSubType) {
        return tenantCrudService.findByNamedQuerySingleResult(InformationMgrSubTypeEntity.BY_NAME, QueryParameter.with("name", notificationSubType).parameters());
    }

    public InformationMgrLevelEntity getNotificationLevelsByName(String notificationLevel) {
        return tenantCrudService.findByNamedQuerySingleResult(InformationMgrLevelEntity.BY_NAME, QueryParameter.with("name", notificationLevel).parameters());
    }

    public InformationMgrSubLevelEntity getNotificationSubLevelByName(String notificationSubLevel) {
        return crudService.findByNamedQuerySingleResult(InformationMgrSubLevelEntity.BY_NAME, QueryParameter.with("name", notificationSubLevel).parameters());
    }

    public Product getProductByName(String productName) {
        return crudService.findByNamedQuerySingleResult(Product.GET_BY_NAME, QueryParameter.with("name", productName).parameters());
    }

    public InformationMgrAlertConfigEntity getNotificationByIDAndPropertyId(Integer notificationId, Integer propertyId) {
        return crudService.findByNamedQuerySingleResult(InformationMgrAlertConfigEntity.FIND_BY_ID_AND_PROPERTY_ID, QueryParameter.with("id", notificationId).and("propertyId", propertyId).parameters());
    }

    public AccomType getAccomTypeUsingPropertyIdAndAccomTypeCode(Integer propertyId, String code){
        return (AccomType) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId, AccomType.BY_CODE, QueryParameter.with("code", code).parameters());
    }

    public Object createNotification(NotificationConfigDTO notificationConfigDTO) {
        Product product = null;
        InformationMgrSubLevelEntity subLevelEntity = null;
        InfoMgrTypeEntity notificationType = getNotificationTypesByName(getType(notificationConfigDTO.getInfoMgrType()));
        InformationMgrSubTypeEntity subTypeEntity = getNotificationSubTypeByName(notificationConfigDTO.getInfoMgrSubType());
        InformationMgrLevelEntity levelEntity = getNotificationLevelsByName(notificationConfigDTO.getInfoMgrLevel().getCode());
        if (notificationConfigDTO.getInfoMgrSubLevel().equals(SubLevelTypeAPI.ALL_COMPETITOR)) {
            subLevelEntity = getNotificationSubLevelByName("ALL_COMPETITORS");
        } else {
            subLevelEntity = getNotificationSubLevelByName(notificationConfigDTO.getInfoMgrSubLevel().name());
        }
        if (notificationConfigDTO.getProduct() != null) {
            product = getProductByName(notificationConfigDTO.getProduct());
        }
        String validationResult = validate(product, subLevelEntity, notificationType, subTypeEntity, levelEntity, notificationConfigDTO);
        if (validationResult != null) {
            return validationResult;
        }
        return updateNotificationDTO(notificationType, subTypeEntity, levelEntity, subLevelEntity, product, notificationConfigDTO);
    }

    private String validate(Product product, InformationMgrSubLevelEntity subLevelEntity, InfoMgrTypeEntity notificationType, InformationMgrSubTypeEntity subTypeEntity,
                            InformationMgrLevelEntity levelEntity, NotificationConfigDTO notificationConfigDTO) {
        Map<Object, String> validationMap = new HashMap<>();
        validationMap.put(notificationType, "Please provide valid Notification Type");
        validationMap.put(subTypeEntity, "Please provide valid Notification Sub Type");
        validationMap.put(levelEntity, "Please provide valid Notification Level");

        if (notificationType != null && subTypeEntity != null && levelEntity != null) {

            if (subTypeEntity.getName().equalsIgnoreCase("BOOKING_PACE")) {
                validationMap.put(notificationConfigDTO.getInfoMgrSubLevel().equals(SubLevelTypeAPI.TRANSIENT) ?
                        SubLevelTypeAPI.TRANSIENT : notificationConfigDTO.getInfoMgrSubLevel().equals(SubLevelTypeAPI.GROUP) ? SubLevelTypeAPI.GROUP : null, "Sub Level should be " + SubLevelTypeAPI.TRANSIENT);
            } else {
                validationMap.put(subLevelEntity, "Please provide valid Notification Sub Level");
            }

            if (subTypeEntity.getName().equalsIgnoreCase("PRICING_BY_VALUE")) {
                validationMap.put(product, "Please provide valid Product");
            }

            if (subTypeEntity.getName().equalsIgnoreCase("PRICING_BY_VALUE")
                    && levelEntity.getName().equalsIgnoreCase(LevelType.ROOM_TYPE.name())) {
                String baseRoomType = notificationConfigDTO.getBaseRoomType();
                AccomType accomType = null;
                if(baseRoomType != null){
                     accomType = getAccomTypeUsingPropertyIdAndAccomTypeCode(PacmanWorkContextHelper.getPropertyId(), baseRoomType);
                }
                validationMap.put(accomType != null ?
                        accomType : null, "Please provide valid Base Room Type");
            }

            validateMetricTypes(notificationConfigDTO, subTypeEntity, validationMap);
            validateThresholdValue(notificationConfigDTO, validationMap);
            validateThresholdConstraint(notificationConfigDTO, validationMap);
            validationMap.put(notificationConfigDTO.getStartDate(), "Start date cannot be empty");
            validationMap.put(notificationConfigDTO.getEndDate(), "End date cannot be empty");
            validationMap.put(notificationConfigDTO.getThresholdValue(), "Threshold value cannot be empty");
        }
        for (Entry<Object, String> entry : validationMap.entrySet()) {
            if (entry.getKey() == null || entry.getKey().equals("")) {
                return entry.getValue();
            }
        }
        return null;
    }

    public ExceptionConfigDTO updateNotificationDTO
            (InfoMgrTypeEntity typeEntity, InformationMgrSubTypeEntity subTypeEntity, InformationMgrLevelEntity levelEntity,
             InformationMgrSubLevelEntity subLevelEntity, Product product, NotificationConfigDTO notificationConfigDTO) {
        ExceptionConfigDTO dto = new ExceptionConfigDTO();
        String subLevelName = null;
        String subLevelCode = null;
        dto.setAlertTypeEntity(typeEntity);
        dto.setExceptionSubType(subTypeEntity);
        dto.setExceptionLevel(levelEntity);
        if(notificationConfigDTO.getInfoMgrSubLevel().equals(SubLevelTypeAPI.TRANSIENT)){
            subLevelCode = "Transient";
            dto.setSubLevelId(0);
        }else if(notificationConfigDTO.getInfoMgrSubLevel().equals(SubLevelTypeAPI.GROUP)) {
            subLevelCode = "Group";
            dto.setSubLevelId(0);
        }
        else if(levelEntity.getName().equalsIgnoreCase(LevelType.ROOM_TYPE.name())){
            subLevelCode = notificationConfigDTO.getBaseRoomType();
            dto.setSubLevelId(0);
        }else if(notificationConfigDTO.getBaseRoomClass()!=null){
            Integer baseRoomClassID = getRoomClassID(PacmanWorkContextHelper.getPropertyId(), notificationConfigDTO.getBaseRoomClass());
            if(null != baseRoomClassID){
                subLevelCode = notificationConfigDTO.getBaseRoomClass();
                dto.setSubLevelId(baseRoomClassID);
            }
        }else{
            subLevelName = subLevelEntity.getName();
            subLevelCode = subLevelEntity.getDescription();
            dto.setSubLevelId(subLevelEntity.getId());
        }
        dto.setExceptionSubLevel(subLevelCode);
        dto.setSubLevelHasKeyword(subLevelName != null);
        dto.setStartDate(notificationConfigDTO.getStartDate());
        dto.setEndDate(notificationConfigDTO.getEndDate());
        dto.setPropertyIds(notificationConfigDTO.getPropertyIds());
        dto.setThresholdConstraint(notificationConfigDTO.getThresholdConstraint().getCode());
        dto.setThresholdValue(notificationConfigDTO.getThresholdValue());
        dto.setMetricType(notificationConfigDTO.getMetricType());
        dto.setDisabled(false);
        dto.setFrequency("1");
        dto.setStatusId(1);
        dto.setProduct(product);
        updateAdditionalCondition(notificationConfigDTO, dto);
        return dto;
    }

    private void updateAdditionalCondition(NotificationConfigDTO notificationConfigDTO, ExceptionConfigDTO dto) {
        String infoMgrType = notificationConfigDTO.getInfoMgrType();
        boolean isCPNotificationByRoomClassEnabled = getConfigParamService().getBooleanParameterValue(PreProductionConfigParamName.ENABLE_CPC_NOTIFICATION_BY_ROOM_CLASS.getParameterName(),
                PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode());

        if (infoMgrType.equalsIgnoreCase(COMPETITOR_PRICE_AS_OF_LAST_NIGHTLY_OPTIMIZATION) ||
                infoMgrType.equalsIgnoreCase(COMPETITOR_PRICE_CHANGE_NOTIFICATION)) {
            dto.addAdditionalCondition(ADDITIONAL_CONDITION_PARAM_ROOM_CLASS_ID, ADDITIONAL_CONDITION_PARAM_ROOM_CLASS_ID_DEFAULT_VALUE);
            if (isCPNotificationByRoomClassEnabled && notificationConfigDTO.getRoomClass() != null) {
                Integer roomClassID = getRoomClassID(PacmanWorkContextHelper.getPropertyId(), notificationConfigDTO.getRoomClass());
                dto.addAdditionalCondition(ADDITIONAL_CONDITION_PARAM_ROOM_CLASS_ID,(roomClassID!=null)?roomClassID.toString():ADDITIONAL_CONDITION_PARAM_ROOM_CLASS_ID_DEFAULT_VALUE);
            }
        }
    }

    private String getType(String level) {
        switch(level) {
            case DECISION_CHANGE_FROM_LAST_NIGHTLY_OPTIMIZATION:
                return "DecisionChangeEx";
            case COMPETITOR_PRICE_AS_OF_LAST_NIGHTLY_OPTIMIZATION:
                return "CompetitorPriceAsOfLastNightlyOptimization";
            case FORECAST_CHANGE_FROM_LAST_NIGHTLY_OPTIMIZATION:
                return "ForecastChangeEx";
            case YEAR_ON_YEAR_PACE_CHANGE:
                return "YOYPaceChangeEx";
            case OCCUPANCY_CHANGE_FROM_LAST_NIGHTLY_OPTIMIZATION:
                return "OccupancyChangeEx";
            case BOOKING_PACE_AS_OF_LAST_NIGHTLY_OPTIMIZATION:
                return "BookingPaceEx";
            case DECISION_CHANGE_FROM_LAST_OPTIMIZATION:
                return "DecisionChangeExAsOfLastOptimization";
            case FORECAST_CHANGE_FROM_LAST_OPTIMIZATION:
                return "ForecastChangeFromLastOptimization";
            case COMPETITOR_PRICE_CHANGE_NOTIFICATION:
                return "CompetitorPriceChange";
            case OCCUPANCY_CHANGE_FROM_LAST_OPTIMIZATION:
                return "OccupancyChangeAsOfLastOptimization";
            default:
                return "";
        }
    }

    private static void validateThresholdValue(NotificationConfigDTO notificationConfigDTO, Map<Object, String> validationMap) {
        if (notificationConfigDTO.getThresholdValue() != null) {
            BigDecimal thresholdValue = notificationConfigDTO.getThresholdValue();

            if (thresholdValue.compareTo(BigDecimal.ZERO) <= 0) {
                validationMap.put(null, "Threshold value must be greater than zero");
            }

            if (notificationConfigDTO.getInfoMgrType().equalsIgnoreCase(BOOKING_PACE_AS_OF_LAST_NIGHTLY_OPTIMIZATION)
                    && thresholdValue.compareTo(new BigDecimal(3)) > 0) {
                validationMap.put(null, "The Threshold Value should be 1, 2, or 3. Here, 1 represents small, 2 represents medium, and 3 represents large");
            }
        }
    }

    private static void validateThresholdConstraint(NotificationConfigDTO notificationConfigDTO, Map<Object, String> validationMap) {
        List<RelationalOperatorAPI> thresholdConstraint = getThresholdConstraints(notificationConfigDTO.getInfoMgrType());

        String thresholdConstraintStr = thresholdConstraint.stream()
                .map(Enum::name)
                .collect(Collectors.joining(", "));

        validationMap.put(
                thresholdConstraint.contains(notificationConfigDTO.getThresholdConstraint()) ?
                        notificationConfigDTO.getThresholdConstraint() :
                        null, "The ThresholdConstraint should be " + thresholdConstraintStr
        );
    }

    private static List<RelationalOperatorAPI> getThresholdConstraints(String infoMgrType) {
        if (infoMgrType.equalsIgnoreCase(BOOKING_PACE_AS_OF_LAST_NIGHTLY_OPTIMIZATION)) {
            return List.of(
                    RelationalOperatorAPI.FASTER_OR_SLOWER_THAN_EXPECTED_BOOKING_PACE,
                    RelationalOperatorAPI.SLOWER_THAN_EXPECTED_BOOKING_PACE,
                    RelationalOperatorAPI.FASTER_THAN_EXPECTED_BOOKING_PACE
            );
        } else if (infoMgrType.equalsIgnoreCase(COMPETITOR_PRICE_AS_OF_LAST_NIGHTLY_OPTIMIZATION)) {
            return List.of(
                    RelationalOperatorAPI.GREATER_OR_EQUAL,
                    RelationalOperatorAPI.LESS_THAN_OR_EQUAL
            );
        } else {
            return List.of(
                    RelationalOperatorAPI.INCREASED_BY,
                    RelationalOperatorAPI.DECREASED_BY,
                    RelationalOperatorAPI.INCREASED_OR_DECREASED_BY
            );
        }
    }

    private void validateMetricTypes(NotificationConfigDTO notificationConfigDTO, InformationMgrSubTypeEntity subTypeEntity, Map<Object, String> validationMap) {
        List<MetricType> metricTypes = determineMetricTypes(notificationConfigDTO.getInfoMgrType(), subTypeEntity);
        String metricTypeStr = metricTypes.stream()
                .map(Enum::name)
                .collect(Collectors.joining(", "));

        validationMap.put(
                metricTypes.contains(notificationConfigDTO.getMetricType()) ?
                        notificationConfigDTO.getMetricType() : null,
                "The ThresholdConstraint should be " + metricTypeStr
        );
    }

    public List<MetricType> determineMetricTypes(String infoMgrType, InformationMgrSubTypeEntity subTypeEntity) {
        String subTypeName = subTypeEntity.getName();

        if (isBookingPace(infoMgrType)) {
            return List.of(MetricType.BOOKING_PACE);
        }

        if (isCurrencyMetric(infoMgrType, subTypeName)) {
            return List.of(MetricType.CURRENCY);
        }

        if (isRoomAndPercentMetric(infoMgrType, subTypeName)) {
            return List.of(MetricType.ROOMS, MetricType.PERCENT);
        }

        return List.of(MetricType.CURRENCY, MetricType.PERCENT);
    }

    private boolean isBookingPace(String infoMgrType) {
        return infoMgrType.equalsIgnoreCase(BOOKING_PACE_AS_OF_LAST_NIGHTLY_OPTIMIZATION);
    }

    private boolean isCurrencyMetric(String infoMgrType, String subTypeName) {
        return infoMgrType.equalsIgnoreCase(COMPETITOR_PRICE_AS_OF_LAST_NIGHTLY_OPTIMIZATION) ||
                (infoMgrType.equalsIgnoreCase(DECISION_CHANGE_FROM_LAST_NIGHTLY_OPTIMIZATION) &&
                        !subTypeName.equalsIgnoreCase("Overbooking")) ||
                (infoMgrType.equalsIgnoreCase(DECISION_CHANGE_FROM_LAST_OPTIMIZATION) &&
                        (subTypeName.equalsIgnoreCase("LRV") || subTypeName.equalsIgnoreCase("PRICING_BY_VALUE")));
    }

    private boolean isRoomAndPercentMetric(String infoMgrType, String subTypeName) {
        return infoMgrType.equalsIgnoreCase(OCCUPANCY_CHANGE_FROM_LAST_NIGHTLY_OPTIMIZATION) ||
                infoMgrType.equalsIgnoreCase(OCCUPANCY_CHANGE_FROM_LAST_OPTIMIZATION) ||
                (infoMgrType.equalsIgnoreCase(DECISION_CHANGE_FROM_LAST_OPTIMIZATION) &&
                        !subTypeName.equalsIgnoreCase("LRV") && !subTypeName.equalsIgnoreCase("PRICING_BY_VALUE")) ||
                (infoMgrType.equalsIgnoreCase(DECISION_CHANGE_FROM_LAST_NIGHTLY_OPTIMIZATION) &&
                        subTypeName.equalsIgnoreCase("Overbooking"));
    }



    private Integer getRoomClassID(Integer propertyId, String accomClassCode) {
        List<AccomClass> lisAccomClasses = getRoomClassesForProperty(propertyId);
        Map<String, Integer> collect = lisAccomClasses.stream()
                .collect(Collectors.toMap(AccomClass::getCode, AccomClass::getId));
        return collect.getOrDefault(accomClassCode, null);
    }

    public void populateNotificationSubLevel(NotificationConfigDTO notificationConfigDTO, InformationMgrAlertConfigEntity notificationByID) {
        SubLevelConfigDTO exceptionSubLevels = getExceptionSubLevels(notificationByID.getExceptionSubType().getId(), notificationByID.getExceptionLevel().getId(),
                notificationConfigDTO.getPropertyIds(), notificationByID.getProduct()).get(0);
        if (exceptionSubLevels.getName() != null) {
            notificationByID.setSubLevelKeywordUsed(true);
            notificationByID.setExceptionSubLevel(exceptionSubLevels.getId());
            notificationByID.setSubLevelDisplayName(exceptionSubLevels.getCode());
        } else {
            notificationByID.setSubLevelKeywordUsed(false);
            notificationByID.setExceptionSubLevel(exceptionSubLevels.getId());
            notificationByID.setSubLevelDisplayName(exceptionSubLevels.getCode());
        }
    }

}
