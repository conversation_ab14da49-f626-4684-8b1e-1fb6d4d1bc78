package com.ideas.tetris.pacman.services.agilerates.configuration.service;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.dto.CeilingFloorAndOffsetsHierarchyContainer;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.CeilingFloor;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigOffsetAccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OffsetMethod;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingBaseAccomType;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.common.constants.Constants.PROPERTY_ID;
import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

@Component
@Transactional
public class AgileRatesHierarchyValidationService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService tenantCrudService;

    public boolean isHierarchyBetweenIPsValid(Map<Pair<LocalDate, LocalDate>, List<PricingBaseAccomType>> ceilingFloorValuesForSelectedProduct,
                                              Map<Pair<LocalDate, LocalDate>, List<PricingBaseAccomType>> ceilingFloorValuesForRelatedProduct,
                                              Map<Pair<LocalDate, LocalDate>, List<CPConfigOffsetAccomType>> offsetsForSelectedProduct,
                                              Map<Pair<LocalDate, LocalDate>, List<CPConfigOffsetAccomType>> offsetsForRelatedProduct,
                                              Set<AccomClass> priceExcludedAccomClasses,
                                              BigDecimal minDifference, Set<String> warnings,
                                              Predicate<CeilingFloorAndOffsetsHierarchyContainer> filter) {

        List<CeilingFloorAndOffsetsHierarchyContainer> bucketsToBeValidated =
                createContainerForCeilingFloorAndOffsetsGroupedByDateBuckets(ceilingFloorValuesForSelectedProduct, ceilingFloorValuesForRelatedProduct, offsetsForSelectedProduct, offsetsForRelatedProduct);
        return bucketsToBeValidated.stream()
                .filter(filter)
                .noneMatch(bucket ->
                        isHierarchyInvalidAccordingToOffsetsValues(bucket.getSelectedCeilingFloor(), bucket.getRelatedCeilingFloor(),
                                bucket.getSelectedOffsets(), bucket.getRelatedOffsets(), priceExcludedAccomClasses,
                                minDifference, true, warnings));
    }

    private List<CeilingFloorAndOffsetsHierarchyContainer> createContainerForCeilingFloorAndOffsetsGroupedByDateBuckets(
            Map<Pair<LocalDate, LocalDate>, List<PricingBaseAccomType>> selectedCeilingFloor,
            Map<Pair<LocalDate, LocalDate>, List<PricingBaseAccomType>> relatedCeilingFloor,
            Map<Pair<LocalDate, LocalDate>, List<CPConfigOffsetAccomType>> selectedOffsets,
            Map<Pair<LocalDate, LocalDate>, List<CPConfigOffsetAccomType>> relatedOffsets) {
        Set<Pair<LocalDate, LocalDate>> selectedCeilingFloorDateRanges = selectedCeilingFloor.keySet();
        Set<Pair<LocalDate, LocalDate>> selectedOffsetDateRanges = selectedOffsets.keySet();
        Set<Pair<LocalDate, LocalDate>> relatedCeilingFloorDateRanges = relatedCeilingFloor.keySet();
        Set<Pair<LocalDate, LocalDate>> relatedOffsetDateRanges = relatedOffsets.keySet();

        List<Pair<LocalDate, Boolean>> dateBuckets = createDateBuckets(
                selectedCeilingFloorDateRanges, selectedOffsetDateRanges, relatedCeilingFloorDateRanges, relatedOffsetDateRanges);

        return createContainerToBeValidated(dateBuckets, selectedCeilingFloor, relatedCeilingFloor, selectedOffsets, relatedOffsets);
    }

    private List<Pair<LocalDate, Boolean>> createDateBuckets(Set<Pair<LocalDate, LocalDate>> selectedCeilingFloorDateRanges,
                                                             Set<Pair<LocalDate, LocalDate>> selectedOffsetDateRanges,
                                                             Set<Pair<LocalDate, LocalDate>> relatedCeilingFloorDateRanges,
                                                             Set<Pair<LocalDate, LocalDate>> relatedOffsetDateRanges) {
        return Stream.of(selectedCeilingFloorDateRanges, selectedOffsetDateRanges, relatedCeilingFloorDateRanges, relatedOffsetDateRanges)
                .flatMap(Set::stream)
                .filter(pair -> nonNull(pair.getLeft()))
                .map(pair -> {
                    LocalDate start = pair.getLeft();
                    LocalDate end = pair.getRight();
                    Pair<LocalDate, Boolean> isStartDateEndOfSeason = Pair.of(start, false);
                    Pair<LocalDate, Boolean> isEndDateEndOfSeason = Pair.of(end, true);
                    return Set.of(isStartDateEndOfSeason, isEndDateEndOfSeason);
                })
                .flatMap(Set::stream)
                .distinct()
                .sorted((pair1, pair2) -> {
                    if (pair1.getLeft().equals(pair2.getLeft())) {
                        return pair1.getRight().compareTo(pair2.getRight());
                    }
                    return pair1.getLeft().compareTo(pair2.getLeft());
                })
                .collect(toList());
    }

    private List<CeilingFloorAndOffsetsHierarchyContainer> createContainerToBeValidated(
            List<Pair<LocalDate, Boolean>> dateBuckets,
            Map<Pair<LocalDate, LocalDate>, List<PricingBaseAccomType>> selectedCeilingFloor,
            Map<Pair<LocalDate, LocalDate>, List<PricingBaseAccomType>> relatedCeilingFloor,
            Map<Pair<LocalDate, LocalDate>, List<CPConfigOffsetAccomType>> selectedOffsets,
            Map<Pair<LocalDate, LocalDate>, List<CPConfigOffsetAccomType>> relatedOffsets) {

        List<CeilingFloorAndOffsetsHierarchyContainer> containerToBeValidated = new ArrayList<>();
        for (int i = 0; i < dateBuckets.size() - 1; i++) {
            LocalDate currentDate = dateBuckets.get(i).getLeft();
            LocalDate nextDate = dateBuckets.get(i + 1).getLeft();
            LocalDate startBucketDate, endBucketDate;
            boolean isCurrentDateEndSeasonDate = dateBuckets.get(i).getRight();
            boolean isNextDateEndSeasonDate = dateBuckets.get(i + 1).getRight();
            if (!isCurrentDateEndSeasonDate) {
                startBucketDate = currentDate;
            } else {
                startBucketDate = currentDate.plusDays(1);
            }
            if (!isNextDateEndSeasonDate) {
                endBucketDate = nextDate.minusDays(1);
            } else {
                endBucketDate = nextDate;
            }
            if (startBucketDate.isBefore(endBucketDate)) {
                containerToBeValidated.add(CeilingFloorAndOffsetsHierarchyContainer.builder()
                        .relatedOffsets(findOverlappingOffsets(startBucketDate, endBucketDate, relatedOffsets))
                        .selectedOffsets(findOverlappingOffsets(startBucketDate, endBucketDate, selectedOffsets))
                        .relatedCeilingFloor(findOverlappingCeilingFloorValues(startBucketDate, endBucketDate, relatedCeilingFloor))
                        .selectedCeilingFloor(findOverlappingCeilingFloorValues(startBucketDate, endBucketDate, selectedCeilingFloor))
                        .build()
                );
            }
        }
        boolean wasDefaultsToDefaultsAdded = containerToBeValidated.stream()
                .anyMatch(this::isCeilingFloorAndOffsetsHierarchyContainerContainingDefaultsToDefaults);
        if (!wasDefaultsToDefaultsAdded) {
            containerToBeValidated.add(CeilingFloorAndOffsetsHierarchyContainer.builder()
                    .relatedOffsets(relatedOffsets.getOrDefault(Pair.of(null, null), Collections.emptyList()))
                    .selectedOffsets(selectedOffsets.getOrDefault(Pair.of(null, null), Collections.emptyList()))
                    .relatedCeilingFloor(relatedCeilingFloor.get(Pair.of(null, null)))
                    .selectedCeilingFloor(selectedCeilingFloor.get(Pair.of(null, null)))
                    .build());
        }

        return containerToBeValidated;
    }

    private List<PricingBaseAccomType> findOverlappingCeilingFloorValues(LocalDate startDate, LocalDate endDate,
                                                                         Map<Pair<LocalDate, LocalDate>, List<PricingBaseAccomType>> ceilingFloor) {
        List<PricingBaseAccomType> filteredCeilingFLoor = ceilingFloor.entrySet()
                .stream()
                .filter(entry -> Objects.nonNull(entry.getKey().getLeft()))
                .filter(entry -> areDatesOverlapped(startDate, endDate, entry.getKey().getLeft(), entry.getKey().getRight()))
                .map(Map.Entry::getValue)
                .findFirst()
                .orElse(Collections.emptyList());
        return CollectionUtils.isNotEmpty(filteredCeilingFLoor) ?
                filteredCeilingFLoor :
                ceilingFloor.get(Pair.of(null, null));
    }

    private List<CPConfigOffsetAccomType> findOverlappingOffsets(LocalDate startDate, LocalDate endDate,
                                                                 Map<Pair<LocalDate, LocalDate>, List<CPConfigOffsetAccomType>> offsets) {
        List<CPConfigOffsetAccomType> filteredOffsets = offsets.entrySet()
                .stream()
                .filter(entry -> Objects.nonNull(entry.getKey().getLeft()))
                .filter(entry -> areDatesOverlapped(startDate, endDate, entry.getKey().getLeft(), entry.getKey().getRight()))
                .map(Map.Entry::getValue)
                .findFirst()
                .orElse(Collections.emptyList());
        return CollectionUtils.isNotEmpty(filteredOffsets) ?
                filteredOffsets :
                CollectionUtils.isEmpty(offsets.get(Pair.of(null, null))) ? Collections.emptyList()
                        : offsets.get(Pair.of(null, null));
    }

    public boolean areDatesOverlapped(LocalDate startDateOfEditingSeason, LocalDate endDateOfEditingSeason,
                                      LocalDate startDateOfNonEditingSeason, LocalDate endDateOfNonEditingSeason) {
        return ((startDateOfEditingSeason.isBefore(startDateOfNonEditingSeason) || startDateOfEditingSeason.isEqual(startDateOfNonEditingSeason)) && (endDateOfEditingSeason.isAfter(startDateOfNonEditingSeason) || endDateOfEditingSeason.isEqual(startDateOfNonEditingSeason))) ||
                ((startDateOfEditingSeason.isBefore(endDateOfNonEditingSeason) || startDateOfEditingSeason.isEqual(endDateOfNonEditingSeason)) && (endDateOfEditingSeason.isAfter(endDateOfNonEditingSeason) || endDateOfEditingSeason.isEqual(endDateOfNonEditingSeason))) ||
                ((startDateOfEditingSeason.isBefore(startDateOfNonEditingSeason) || startDateOfEditingSeason.isEqual(startDateOfNonEditingSeason)) && (endDateOfEditingSeason.isAfter(endDateOfNonEditingSeason) || endDateOfEditingSeason.isEqual(endDateOfNonEditingSeason))) ||
                ((startDateOfEditingSeason.isAfter(startDateOfNonEditingSeason) || startDateOfEditingSeason.isEqual(startDateOfNonEditingSeason)) && (endDateOfEditingSeason.isBefore(endDateOfNonEditingSeason) || endDateOfEditingSeason.isEqual(endDateOfNonEditingSeason)));
    }

    private boolean isCeilingFloorAndOffsetsHierarchyContainerContainingDefaultsToDefaults(CeilingFloorAndOffsetsHierarchyContainer container) {
        return (CollectionUtils.isEmpty(container.getRelatedOffsets()) || Objects.isNull(container.getRelatedOffsets().get(0).getStartDate())) &&
                (CollectionUtils.isEmpty(container.getSelectedOffsets()) || Objects.isNull(container.getSelectedOffsets().get(0).getStartDate())) &&
                Objects.isNull(container.getRelatedCeilingFloor().get(0).getStartDate()) &&
                Objects.isNull(container.getSelectedCeilingFloor().get(0).getStartDate());
    }

    public boolean isHierarchyInvalidAccordingToOffsetsValues(List<PricingBaseAccomType> floorCeilingMappingsOfEditingProduct,
                                                              List<PricingBaseAccomType> floorCeilingMappingsOfNonEditingProduct,
                                                              List<CPConfigOffsetAccomType> offsetsOfEditingProduct,
                                                              List<CPConfigOffsetAccomType> offsetsOfNonEditingProduct,
                                                              Set<AccomClass> priceExcludedAccomClasses,
                                                              BigDecimal minimumDifference,
                                                              boolean isEditingProductSelected,
                                                              Set<String> warningMessages) {
        Map<AccomClass, AccomType> baseRoomTypes = getAccomClassToBaseAccomType();
        Map<AccomType, PricingBaseAccomType> floorCeilingValuesOfEditingProduct = groupCeilingFloorValuesByAccomType(floorCeilingMappingsOfEditingProduct);
        Map<AccomType, PricingBaseAccomType> floorCeilingValuesOfNonEditingProduct = groupCeilingFloorValuesByAccomType(floorCeilingMappingsOfNonEditingProduct);
        Map<AccomType, CPConfigOffsetAccomType> groupedOffsetsOfEditingProduct = groupOffsetsByAccomType(offsetsOfEditingProduct);
        Map<AccomType, CPConfigOffsetAccomType> groupedOffsetsOfNonEditingProduct = groupOffsetsByAccomType(offsetsOfNonEditingProduct);
        Map<AccomType, Map<DayOfWeek, CeilingFloor>> calculatedCeilingFloorWithOffsetsValuesForEditingProduct = new HashMap<>();
        Map<AccomType, Map<DayOfWeek, CeilingFloor>> calculatedCeilingFloorWithOffsetsValuesForNonEditingProduct = new HashMap<>();
        return floorCeilingValuesOfEditingProduct.entrySet()
                .stream()
                .anyMatch(ceilingFloor -> {
                    boolean isPriceExcluded = priceExcludedAccomClasses.contains(ceilingFloor.getKey().getAccomClass());
                    AccomType baseAccomType = ceilingFloor.getKey();
                    Map<AccomType, CPConfigOffsetAccomType> offsetsWithinRoomClassOfEditingProduct =
                            findOffsetsWithinRoomClass(groupedOffsetsOfEditingProduct, ceilingFloor.getKey());
                    Map<AccomType, CPConfigOffsetAccomType> offsetsWithinRoomClassOfNonEditingProduct =
                            findOffsetsWithinRoomClass(groupedOffsetsOfNonEditingProduct, ceilingFloor.getKey());
                    if (MapUtils.isEmpty(offsetsWithinRoomClassOfEditingProduct)) {
                        if (MapUtils.isEmpty(offsetsWithinRoomClassOfNonEditingProduct)) {
                            return validateParticularAccomType(ceilingFloor.getValue(), floorCeilingValuesOfNonEditingProduct.get(baseAccomType),
                                    Collections.emptyMap(), Collections.emptyMap(), calculatedCeilingFloorWithOffsetsValuesForEditingProduct,
                                    calculatedCeilingFloorWithOffsetsValuesForNonEditingProduct, ceilingFloor.getKey(), minimumDifference, baseRoomTypes,
                                    isPriceExcluded, isEditingProductSelected, warningMessages);
                        }
                        return offsetsWithinRoomClassOfNonEditingProduct.entrySet()
                                .stream()
                                .anyMatch(offset -> {
                                    AccomType accomType = offset.getKey();
                                    return validateParticularAccomType(ceilingFloor.getValue(), floorCeilingValuesOfNonEditingProduct.get(baseAccomType),
                                            Collections.emptyMap(), offsetsWithinRoomClassOfNonEditingProduct, calculatedCeilingFloorWithOffsetsValuesForEditingProduct,
                                            calculatedCeilingFloorWithOffsetsValuesForNonEditingProduct, accomType, minimumDifference, baseRoomTypes,
                                            isPriceExcluded, isEditingProductSelected, warningMessages);
                                }) ||
                                validateParticularAccomType(ceilingFloor.getValue(), floorCeilingValuesOfNonEditingProduct.get(baseAccomType),
                                        Collections.emptyMap(), Collections.emptyMap(), calculatedCeilingFloorWithOffsetsValuesForEditingProduct,
                                        calculatedCeilingFloorWithOffsetsValuesForNonEditingProduct, ceilingFloor.getKey(), minimumDifference, baseRoomTypes,
                                        isPriceExcluded, isEditingProductSelected, warningMessages);
                    }
                    return offsetsWithinRoomClassOfEditingProduct.entrySet()
                            .stream()
                            .anyMatch(offset -> {
                                AccomType accomType = offset.getKey();
                                return validateParticularAccomType(ceilingFloor.getValue(), floorCeilingValuesOfNonEditingProduct.get(baseAccomType),
                                        offsetsWithinRoomClassOfEditingProduct, offsetsWithinRoomClassOfNonEditingProduct, calculatedCeilingFloorWithOffsetsValuesForEditingProduct,
                                        calculatedCeilingFloorWithOffsetsValuesForNonEditingProduct, accomType, minimumDifference, baseRoomTypes,
                                        isPriceExcluded, isEditingProductSelected, warningMessages);
                            }) ||
                            validateParticularAccomType(ceilingFloor.getValue(), floorCeilingValuesOfNonEditingProduct.get(baseAccomType),
                                    Collections.emptyMap(), Collections.emptyMap(), calculatedCeilingFloorWithOffsetsValuesForEditingProduct,
                                    calculatedCeilingFloorWithOffsetsValuesForNonEditingProduct, ceilingFloor.getKey(), minimumDifference, baseRoomTypes,
                                    isPriceExcluded, isEditingProductSelected, warningMessages);
                });
    }

    private boolean validateParticularAccomType(PricingBaseAccomType ceilingFloorValueOfEditingProduct, PricingBaseAccomType ceilingFloorValueOfNonEditingProduct,
                                                Map<AccomType, CPConfigOffsetAccomType> groupedOffsetsOfEditingProduct,
                                                Map<AccomType, CPConfigOffsetAccomType> groupedOffsetsOfNonEditingProduct,
                                                Map<AccomType, Map<DayOfWeek, CeilingFloor>> calculatedCeilingFloorWithOffsetsValuesForEditingProduct,
                                                Map<AccomType, Map<DayOfWeek, CeilingFloor>> calculatedCeilingFloorWithOffsetsValuesForNonEditingProduct,
                                                AccomType accomType, BigDecimal minimumDifference, Map<AccomClass, AccomType> baseRoomTypes,
                                                boolean isPriceExcluded, boolean isEditingProductSelected, Set<String> warningMessages) {
        Map<DayOfWeek, CeilingFloor> calculatedValuesOfEditingProduct =
                resolveOffsetValuesForBaseOccupancyType(accomType, ceilingFloorValueOfEditingProduct, calculatedCeilingFloorWithOffsetsValuesForEditingProduct, groupedOffsetsOfEditingProduct, baseRoomTypes, isPriceExcluded);
        Map<DayOfWeek, CeilingFloor> calculatedValuesOfNonEditingProduct =
                resolveOffsetValuesForBaseOccupancyType(accomType, ceilingFloorValueOfNonEditingProduct, calculatedCeilingFloorWithOffsetsValuesForNonEditingProduct, groupedOffsetsOfNonEditingProduct, baseRoomTypes, isPriceExcluded);
        return areCalculatedValuesInValid(calculatedValuesOfEditingProduct, calculatedValuesOfNonEditingProduct, minimumDifference, isEditingProductSelected, isPriceExcluded, warningMessages);
    }

    private Map<AccomType, PricingBaseAccomType> groupCeilingFloorValuesByAccomType(List<PricingBaseAccomType> floorCeilingValues) {
        if (Objects.isNull(floorCeilingValues)) {
            return Collections.emptyMap();
        }
        return floorCeilingValues.stream()
                .collect(Collectors.toMap(PricingBaseAccomType::getAccomType, Function.identity()));
    }

    private Map<AccomType, CPConfigOffsetAccomType> groupOffsetsByAccomType(List<CPConfigOffsetAccomType> offsets) {
        return offsets.stream()
                .collect(Collectors.toMap(CPConfigOffsetAccomType::getAccomType, Function.identity()));
    }

    private Map<AccomType, CPConfigOffsetAccomType> findOffsetsWithinRoomClass(Map<AccomType, CPConfigOffsetAccomType> offsets, AccomType baseAccomType) {
        return offsets.entrySet()
                .stream()
                .filter(entry -> baseAccomType.getAccomClass().getAccomTypes().contains(entry.getKey()))
                .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    private Map<DayOfWeek, CeilingFloor> resolveOffsetValuesForBaseOccupancyType(AccomType editingAccomType, PricingBaseAccomType ceilingFloor,
                                                                                 Map<AccomType, Map<DayOfWeek, CeilingFloor>> calculatedCeilingFloorWithOffsetsValues,
                                                                                 Map<AccomType, CPConfigOffsetAccomType> groupedOffsetsOfEditingProduct,
                                                                                 Map<AccomClass, AccomType> baseRoomTypes, boolean isPriceExcluded) {
        if (isPriceExcluded) {
            return calculateBaseValuesForPriceExcluded(groupedOffsetsOfEditingProduct.getOrDefault(editingAccomType, new CPConfigOffsetAccomType()), ceilingFloor);
        }
        Map<DayOfWeek, CeilingFloor> calculatedWithOffsetsCeilingFloorValues =
                calculatedCeilingFloorWithOffsetsValues.get(editingAccomType);
        if (Objects.nonNull(calculatedWithOffsetsCeilingFloorValues)) {
            return calculatedWithOffsetsCeilingFloorValues;
        }
        AccomType baseAccomType = getBaseRoomTypeForNonBaseRoomTypeWithinRoomClass(editingAccomType, baseRoomTypes);
        calculatedCeilingFloorWithOffsetsValues.putIfAbsent(baseAccomType, calculateBaseValues(ceilingFloor));
        Map<DayOfWeek, CeilingFloor> ceilingFloorForBaseAccomType = calculatedCeilingFloorWithOffsetsValues.get(baseAccomType);
        if (Objects.equals(baseAccomType, editingAccomType)) {
            return ceilingFloorForBaseAccomType;
        }
        calculatedCeilingFloorWithOffsetsValues.putIfAbsent(editingAccomType, applyOffsetsToAlreadyCalculated(ceilingFloorForBaseAccomType, groupedOffsetsOfEditingProduct.getOrDefault(editingAccomType, new CPConfigOffsetAccomType())));
        return calculatedCeilingFloorWithOffsetsValues.get(editingAccomType);
    }

    private boolean areCalculatedValuesInValid(
            Map<DayOfWeek, CeilingFloor> calculatedValuesOfEditingProduct,
            Map<DayOfWeek, CeilingFloor> calculatedValuesOfNonEditingProduct,
            BigDecimal minimumDifference,
            boolean isEditingProductSelected,
            boolean isPriceExcluded,
            Set<String> validationMessages) {
        return calculatedValuesOfEditingProduct.entrySet()
                .stream()
                .filter(entry -> Objects.nonNull(entry.getValue()))
                .anyMatch(calculatedValueOfEditingProduct -> {
                    if (isEditingProductSelected) {
                        return !validateCalculatedValues(
                                calculatedValueOfEditingProduct.getValue().getCeiling(),
                                calculatedValuesOfNonEditingProduct.get(calculatedValueOfEditingProduct.getKey()).getCeiling(),
                                calculatedValueOfEditingProduct.getValue().getFloor(),
                                calculatedValuesOfNonEditingProduct.get(calculatedValueOfEditingProduct.getKey()).getFloor(),
                                minimumDifference,
                                isPriceExcluded,
                                validationMessages);
                    }
                    return !validateCalculatedValues(
                            calculatedValuesOfNonEditingProduct.get(calculatedValueOfEditingProduct.getKey()).getCeiling(),
                            calculatedValueOfEditingProduct.getValue().getCeiling(),
                            calculatedValuesOfNonEditingProduct.get(calculatedValueOfEditingProduct.getKey()).getFloor(),
                            calculatedValueOfEditingProduct.getValue().getFloor(),
                            minimumDifference,
                            isPriceExcluded,
                            validationMessages);
                });
    }

    public boolean validateCalculatedValues(BigDecimal selectedCeiling, BigDecimal relatedCeiling,
                                            BigDecimal selectedFloor, BigDecimal relatedFloor,
                                            BigDecimal minDifference,
                                            boolean isPriceExcluded, Set<String> warnings) {
        double overlap = relatedCeiling.doubleValue() - selectedFloor.doubleValue();
        if (isPriceExcluded && selectedFloor.compareTo(relatedFloor) > 0) {
            warnings.add("invalid.hierarchy.excluded.value");
        }
        if (!isPriceExcluded && selectedCeiling.compareTo(relatedCeiling) > 0) {
            warnings.add("invalid.hierarchy.ceiling.value");
        }
        if (!isPriceExcluded && selectedFloor.compareTo(relatedFloor) > 0) {
            warnings.add("invalid.hierarchy.floor.value");
        }
        if (!isPriceExcluded && overlap < minDifference.doubleValue()) {
            warnings.add("invalid.hierarchy.mindiff.value");
        }
        if (!isPriceExcluded &&
                isOverlapNotExist(selectedFloor.doubleValue(), selectedCeiling.doubleValue(), relatedFloor.doubleValue(), relatedCeiling.doubleValue())) {
            warnings.add("invalid.hierarchy.overlap.value");
        }
        if (!isPriceExcluded && overlap < 0 || CollectionUtils.isNotEmpty(warnings)) {
            return false;
        }
        return true;
    }

    private boolean isOverlapNotExist(double selectedFloorValue, double selectedCeilingValue, double relatedFloorValue, double relatedCeilingValue) {
        return (!isValueBetween(relatedFloorValue, selectedFloorValue, selectedCeilingValue)
                && !isValueBetween(relatedCeilingValue, selectedFloorValue, selectedCeilingValue))
                && (!isValueBetween(selectedFloorValue, relatedFloorValue, relatedCeilingValue)
                && !isValueBetween(selectedCeilingValue, relatedFloorValue, relatedCeilingValue));
    }

    public boolean isValueBetween(double value, double lowerBound, double upperBound) {
        return lowerBound <= value && value <= upperBound;
    }

    public Map<DayOfWeek, CeilingFloor> applyOffsetsToAlreadyCalculated(Map<DayOfWeek, CeilingFloor> calculatedCeilingFloor, CPConfigOffsetAccomType offset) {
        CeilingFloor mondayCeilingFloor = new CeilingFloor();
        mondayCeilingFloor.setCeiling(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.MONDAY).getCeiling(), offset.getMondayOffsetValueWithTax(), offset.getOffsetMethod()));
        mondayCeilingFloor.setFloor(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.MONDAY).getFloor(), offset.getMondayOffsetValueWithTax(), offset.getOffsetMethod()));
        CeilingFloor tuesdayCeilingFloor = new CeilingFloor();
        tuesdayCeilingFloor.setCeiling(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.TUESDAY).getCeiling(), offset.getTuesdayOffsetValueWithTax(), offset.getOffsetMethod()));
        tuesdayCeilingFloor.setFloor(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.TUESDAY).getFloor(), offset.getTuesdayOffsetValueWithTax(), offset.getOffsetMethod()));
        CeilingFloor wednesdayCeilingFloor = new CeilingFloor();
        wednesdayCeilingFloor.setCeiling(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.WEDNESDAY).getCeiling(), offset.getWednesdayOffsetValueWithTax(), offset.getOffsetMethod()));
        wednesdayCeilingFloor.setFloor(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.WEDNESDAY).getFloor(), offset.getWednesdayOffsetValueWithTax(), offset.getOffsetMethod()));
        CeilingFloor thursdayCeilingFloor = new CeilingFloor();
        thursdayCeilingFloor.setCeiling(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.THURSDAY).getCeiling(), offset.getThursdayOffsetValueWithTax(), offset.getOffsetMethod()));
        thursdayCeilingFloor.setFloor(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.THURSDAY).getFloor(), offset.getThursdayOffsetValueWithTax(), offset.getOffsetMethod()));
        CeilingFloor fridayCeilingFloor = new CeilingFloor();
        fridayCeilingFloor.setCeiling(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.FRIDAY).getCeiling(), offset.getFridayOffsetValueWithTax(), offset.getOffsetMethod()));
        fridayCeilingFloor.setFloor(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.FRIDAY).getFloor(), offset.getFridayOffsetValueWithTax(), offset.getOffsetMethod()));
        CeilingFloor saturdayCeilingFloor = new CeilingFloor();
        saturdayCeilingFloor.setCeiling(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.SATURDAY).getCeiling(), offset.getSaturdayOffsetValueWithTax(), offset.getOffsetMethod()));
        saturdayCeilingFloor.setFloor(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.SATURDAY).getFloor(), offset.getSaturdayOffsetValueWithTax(), offset.getOffsetMethod()));
        CeilingFloor sundayCeilingFloor = new CeilingFloor();
        sundayCeilingFloor.setCeiling(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.SUNDAY).getCeiling(), offset.getSundayOffsetValueWithTax(), offset.getOffsetMethod()));
        sundayCeilingFloor.setFloor(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.SUNDAY).getFloor(), offset.getSundayOffsetValueWithTax(), offset.getOffsetMethod()));

        return Map.of(DayOfWeek.MONDAY, mondayCeilingFloor,
                DayOfWeek.TUESDAY, tuesdayCeilingFloor,
                DayOfWeek.WEDNESDAY, wednesdayCeilingFloor,
                DayOfWeek.THURSDAY, thursdayCeilingFloor,
                DayOfWeek.FRIDAY, fridayCeilingFloor,
                DayOfWeek.SATURDAY, saturdayCeilingFloor,
                DayOfWeek.SUNDAY, sundayCeilingFloor
        );
    }

    private Map<DayOfWeek, CeilingFloor> calculateBaseValuesForPriceExcluded(CPConfigOffsetAccomType offset, PricingBaseAccomType ceilingFloor) {
        CeilingFloor mondayCeilingFloor = new CeilingFloor();
        mondayCeilingFloor.setFloor(offset.getMondayOffsetValueWithTax() != null ? offset.getMondayOffsetValueWithTax() : ceilingFloor.getMondayFloorRateWithTax());
        mondayCeilingFloor.setCeiling(offset.getMondayOffsetValueWithTax() != null ? offset.getMondayOffsetValueWithTax() : ceilingFloor.getMondayFloorRateWithTax());
        CeilingFloor tuesdayCeilingFloor = new CeilingFloor();
        tuesdayCeilingFloor.setFloor(offset.getTuesdayOffsetValueWithTax() != null ? offset.getTuesdayOffsetValueWithTax() : ceilingFloor.getTuesdayFloorRateWithTax());
        tuesdayCeilingFloor.setCeiling(offset.getTuesdayOffsetValueWithTax() != null ? offset.getTuesdayOffsetValueWithTax() : ceilingFloor.getTuesdayFloorRateWithTax());
        CeilingFloor wednesdayCeilingFloor = new CeilingFloor();
        wednesdayCeilingFloor.setFloor(offset.getWednesdayOffsetValueWithTax() != null ? offset.getWednesdayOffsetValueWithTax() : ceilingFloor.getWednesdayFloorRateWithTax());
        wednesdayCeilingFloor.setCeiling(offset.getWednesdayOffsetValueWithTax() != null ? offset.getWednesdayOffsetValueWithTax() : ceilingFloor.getWednesdayFloorRateWithTax());
        CeilingFloor thursdayCeilingFloor = new CeilingFloor();
        thursdayCeilingFloor.setFloor(offset.getThursdayOffsetValueWithTax() != null ? offset.getThursdayOffsetValueWithTax() : ceilingFloor.getThursdayFloorRateWithTax());
        thursdayCeilingFloor.setCeiling(offset.getThursdayOffsetValueWithTax() != null ? offset.getThursdayOffsetValueWithTax() : ceilingFloor.getThursdayFloorRateWithTax());
        CeilingFloor fridayCeilingFloor = new CeilingFloor();
        fridayCeilingFloor.setFloor(offset.getFridayOffsetValueWithTax() != null ? offset.getFridayOffsetValueWithTax() : ceilingFloor.getFridayFloorRateWithTax());
        fridayCeilingFloor.setCeiling(offset.getFridayOffsetValueWithTax() != null ? offset.getFridayOffsetValueWithTax() : ceilingFloor.getFridayFloorRateWithTax());
        CeilingFloor saturdayCeilingFloor = new CeilingFloor();
        saturdayCeilingFloor.setFloor(offset.getSaturdayOffsetValueWithTax() != null ? offset.getSaturdayOffsetValueWithTax() : ceilingFloor.getSaturdayFloorRateWithTax());
        saturdayCeilingFloor.setCeiling(offset.getSaturdayOffsetValueWithTax() != null ? offset.getSaturdayOffsetValueWithTax() : ceilingFloor.getSaturdayFloorRateWithTax());
        CeilingFloor sundayCeilingFloor = new CeilingFloor();
        sundayCeilingFloor.setFloor(offset.getSundayOffsetValueWithTax() != null ? offset.getSundayOffsetValueWithTax() : ceilingFloor.getSundayFloorRateWithTax());
        sundayCeilingFloor.setCeiling(offset.getSundayOffsetValueWithTax() != null ? offset.getSundayOffsetValueWithTax() : ceilingFloor.getSundayFloorRateWithTax());

        return Map.of(DayOfWeek.MONDAY, mondayCeilingFloor,
                DayOfWeek.TUESDAY, tuesdayCeilingFloor,
                DayOfWeek.WEDNESDAY, wednesdayCeilingFloor,
                DayOfWeek.THURSDAY, thursdayCeilingFloor,
                DayOfWeek.FRIDAY, fridayCeilingFloor,
                DayOfWeek.SATURDAY, saturdayCeilingFloor,
                DayOfWeek.SUNDAY, sundayCeilingFloor
        );
    }

    public BigDecimal applyOffsetWithPossibleNullValue(BigDecimal baseNumber, BigDecimal offsetValue, OffsetMethod offsetMethod) {
        if (Objects.nonNull(offsetValue) && Objects.nonNull(offsetMethod)) {
            return applyOffset(baseNumber, offsetValue, offsetMethod);
        }
        return baseNumber;
    }

    private Map<DayOfWeek, CeilingFloor> calculateBaseValues(PricingBaseAccomType ceilingFloor) {
        CeilingFloor mondayCeilingFloor = new CeilingFloor();
        mondayCeilingFloor.setCeiling(ceilingFloor.getMondayCeilingRateWithTax());
        mondayCeilingFloor.setFloor(ceilingFloor.getMondayFloorRateWithTax());
        CeilingFloor tuesdayCeilingFloor = new CeilingFloor();
        tuesdayCeilingFloor.setCeiling(ceilingFloor.getTuesdayCeilingRateWithTax());
        tuesdayCeilingFloor.setFloor(ceilingFloor.getTuesdayFloorRateWithTax());
        CeilingFloor wednesdayCeilingFloor = new CeilingFloor();
        wednesdayCeilingFloor.setCeiling(ceilingFloor.getWednesdayCeilingRateWithTax());
        wednesdayCeilingFloor.setFloor(ceilingFloor.getWednesdayFloorRateWithTax());
        CeilingFloor thursdayCeilingFloor = new CeilingFloor();
        thursdayCeilingFloor.setCeiling(ceilingFloor.getThursdayCeilingRateWithTax());
        thursdayCeilingFloor.setFloor(ceilingFloor.getThursdayFloorRateWithTax());
        CeilingFloor fridayCeilingFloor = new CeilingFloor();
        fridayCeilingFloor.setCeiling(ceilingFloor.getFridayCeilingRateWithTax());
        fridayCeilingFloor.setFloor(ceilingFloor.getFridayFloorRateWithTax());
        CeilingFloor saturdayCeilingFloor = new CeilingFloor();
        saturdayCeilingFloor.setCeiling(ceilingFloor.getSaturdayCeilingRateWithTax());
        saturdayCeilingFloor.setFloor(ceilingFloor.getSaturdayFloorRateWithTax());
        CeilingFloor sundayCeilingFloor = new CeilingFloor();
        sundayCeilingFloor.setCeiling(ceilingFloor.getSundayCeilingRateWithTax());
        sundayCeilingFloor.setFloor(ceilingFloor.getSundayFloorRateWithTax());

        return Map.of(DayOfWeek.MONDAY, mondayCeilingFloor,
                DayOfWeek.TUESDAY, tuesdayCeilingFloor,
                DayOfWeek.WEDNESDAY, wednesdayCeilingFloor,
                DayOfWeek.THURSDAY, thursdayCeilingFloor,
                DayOfWeek.FRIDAY, fridayCeilingFloor,
                DayOfWeek.SATURDAY, saturdayCeilingFloor,
                DayOfWeek.SUNDAY, sundayCeilingFloor
        );
    }

    private AccomType getBaseRoomTypeForNonBaseRoomTypeWithinRoomClass(AccomType accomType, Map<AccomClass, AccomType> baseRoomTypes) {
        return baseRoomTypes.get(accomType.getAccomClass());
    }

    private BigDecimal applyOffset(BigDecimal baseNumber, BigDecimal offsetValue, OffsetMethod offsetMethod) {
        if (OffsetMethod.PERCENTAGE == offsetMethod) {
            if (baseNumber != null) {
                offsetValue = baseNumber.multiply(offsetValue.divide(BigDecimal.valueOf(100))).setScale(2, RoundingMode.HALF_UP);
            }
        } else if (OffsetMethod.FIXED_PRICE == offsetMethod) {
            return offsetValue;
        }

        return offsetValue != null ? (baseNumber != null ? (baseNumber.add(offsetValue)) : offsetValue) : baseNumber;
    }

    private Map<AccomClass, AccomType> getAccomClassToBaseAccomType() {
        return getPricingAccomClasses().stream()
                .collect(toMap(PricingAccomClass::getAccomClass, PricingAccomClass::getAccomType));
    }

    private List<PricingAccomClass> getPricingAccomClasses() {
        return tenantCrudService.findByNamedQuery(PricingAccomClass.FIND_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }
}
