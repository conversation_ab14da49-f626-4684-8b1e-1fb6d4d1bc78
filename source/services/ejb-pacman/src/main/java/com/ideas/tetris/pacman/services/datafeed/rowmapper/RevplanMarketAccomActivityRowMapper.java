package com.ideas.tetris.pacman.services.datafeed.rowmapper;

import com.ideas.tetris.pacman.services.datafeed.dto.RevplanMarketAccomActivity;
import com.ideas.tetris.platform.common.crudservice.RowMapper;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class RevplanMarketAccomActivityRowMapper implements RowMapper<RevplanMarketAccomActivity> {
    @Override
    public RevplanMarketAccomActivity mapRow(Object[] row) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date occupancyDate;
        try {
            occupancyDate = dateFormat.parse(row[0].toString());
        } catch (ParseException e) {
            occupancyDate = null;
        }
        return new RevplanMarketAccomActivity(occupancyDate, row[1].toString(), row[2].toString(), ((BigDecimal) row[3]).intValue(), (BigDecimal) row[4]);
    }
}
