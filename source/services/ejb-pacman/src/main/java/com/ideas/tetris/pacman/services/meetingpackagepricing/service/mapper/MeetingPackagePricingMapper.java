package com.ideas.tetris.pacman.services.meetingpackagepricing.service.mapper;

import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesOffsetMethod;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceFunctionRoom;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceRevenueGroup;
import com.ideas.tetris.pacman.services.functionspace.configuration.packaging.dto.RevenueGroupDto;
import com.ideas.tetris.pacman.services.meetingpackagepricing.dto.*;
import com.ideas.tetris.pacman.services.meetingpackagepricing.entity.*;
import com.ideas.tetris.pacman.services.meetingpackagepricing.service.PricingDifferentialGroupKey;
import com.ideas.tetris.pacman.services.meetingpackagepricing.service.ProductOccupancyKey;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.MinimumIncrementMethod;
import com.ideas.tetris.pacman.services.product.FloorType;
import com.ideas.tetris.pacman.services.product.InvalidReason;
import com.ideas.tetris.pacman.services.product.OverridableProductEnum;
import com.ideas.tetris.pacman.services.product.RoundingRule;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEvent;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class MeetingPackagePricingMapper {

    public static List<MeetingPackagePricingPerDay> toPricingPerDay(List<MeetingPackageBarDecision> decisions,
                                                                    BaseMeetingRoomDTO baseMeetingRoom,
                                                                    Map<String, List<PropertySpecialEvent>> specialEventsMap,
                                                                    List<MeetingPackageBarDecisionOverride> meetingPackageBarDecisionOverrides,
                                                                    Map<ProductOccupancyKey, MeetingPackageProductRateOffsetOverride> productRateOffsetOverrideMap,
                                                                    Map<ProductOccupancyKey, BigDecimal> adjustmentMap,
                                                                    Map<PricingDifferentialGroupKey, MeetingPackagePaceBarDecision> paceBarDecisionMap,
                                                                    List<MeetingPackageBarDecision> parentProductDecisions)
     {
        Map<Date, List<MeetingPackageBarDecision>> decisionByOccupancyDate = decisions.stream()
                .collect(Collectors.groupingBy(MeetingPackageBarDecision::getOccupancyDate, Collectors.toList()));
        return decisionByOccupancyDate.keySet().stream()
                .map(occupancyDate -> {
                    List<MeetingPackageBarDecision> meetingPackageBarDecisions = decisionByOccupancyDate.get(occupancyDate);
                    List<MeetingPackagePriceDetail> meetingPackagePriceDetails = createMeetingPackagePriceDetails(
                            meetingPackageBarDecisions,
                            baseMeetingRoom,
                            meetingPackageBarDecisionOverrides,
                            productRateOffsetOverrideMap,
                            adjustmentMap,
                            paceBarDecisionMap,
                            parentProductDecisions);
                    return createMeetingPackagePricingPerDay(occupancyDate, specialEventsMap, meetingPackagePriceDetails);
                })
                .collect(Collectors.toList());
    }

    static MeetingPackagePricingPerDay createMeetingPackagePricingPerDay(Date occupancyDate,
                                                                         Map<String, List<PropertySpecialEvent>> specialEventsMap,
                                                                         List<MeetingPackagePriceDetail> meetingPackagePriceDetails) {
        MeetingPackagePricingPerDay meetingPackagePricingPerDay = new MeetingPackagePricingPerDay();
        meetingPackagePricingPerDay.setDate(occupancyDate);
        meetingPackagePricingPerDay.setPriceDetails(new ArrayList<>());
        meetingPackagePricingPerDay.setSpecialEvents(toSpecialEventDetails(specialEventsMap, LocalDateUtils.toLocalDate(occupancyDate)));
        meetingPackagePricingPerDay.setPriceDetails(meetingPackagePriceDetails);
        return meetingPackagePricingPerDay;
    }

    private static List<MeetingPackagePriceDetail> createMeetingPackagePriceDetails(List<MeetingPackageBarDecision> meetingPackageBarDecisions,
                                                                                    BaseMeetingRoomDTO baseMeetingRoom,
                                                                                    List<MeetingPackageBarDecisionOverride> meetingPackageBarDecisionOverrides,
                                                                                    Map<ProductOccupancyKey, MeetingPackageProductRateOffsetOverride> productRateOffsetOverrideMap,
                                                                                    Map<ProductOccupancyKey, BigDecimal> adjustmentMap,
                                                                                    Map<PricingDifferentialGroupKey, MeetingPackagePaceBarDecision> paceBarDecisionMap,
                                                                                    List<MeetingPackageBarDecision> parentProductDecisions) {
        Map<Integer, List<MeetingPackageBarDecision>> decisionsByProductId = meetingPackageBarDecisions.stream()
                .collect(Collectors.groupingBy(meetingPackageBarDecision -> meetingPackageBarDecision.getMeetingPackageProduct().getId(), Collectors.toList()));
        return decisionsByProductId.keySet().stream()
                .map(productId -> {
                    List<MeetingPackageBarDecision> decisions = decisionsByProductId.get(productId);
                    MeetingPackageProduct meetingPackageProduct = decisions.get(0).getMeetingPackageProduct();
                    return createMeetingPackagePriceDetail(
                            meetingPackageProduct,
                            baseMeetingRoom,
                            decisions,
                            meetingPackageBarDecisionOverrides,
                            productRateOffsetOverrideMap,
                            adjustmentMap,
                            paceBarDecisionMap,
                            parentProductDecisions);
                })
                .collect(Collectors.toList());
    }

    private static MeetingPackagePriceDetail createMeetingPackagePriceDetail(MeetingPackageProduct meetingPackageProduct,
                                                                             BaseMeetingRoomDTO baseMeetingRoom,
                                                                             List<MeetingPackageBarDecision> decisions,
                                                                             List<MeetingPackageBarDecisionOverride> meetingPackageBarDecisionOverrides,
                                                                             Map<ProductOccupancyKey, MeetingPackageProductRateOffsetOverride> productRateOffsetOverrideMap,
                                                                             Map<ProductOccupancyKey, BigDecimal> adjustmentMap,
                                                                             Map<PricingDifferentialGroupKey, MeetingPackagePaceBarDecision> paceBarDecisionMap,
                                                                             List<MeetingPackageBarDecision> parentProductDecisions) {
        List<MeetingRoomPrice> meetingRoomPrices = decisions.stream()
                .map(decision -> toMeetingRoomPrice(decision, baseMeetingRoom, meetingPackageBarDecisionOverrides, productRateOffsetOverrideMap, paceBarDecisionMap, parentProductDecisions))
                .collect(Collectors.toList());
        MeetingPackagePriceDetail priceDetail = new MeetingPackagePriceDetail();
        priceDetail.setProductId(meetingPackageProduct.getId());
        priceDetail.setProductName(meetingPackageProduct.getName());
        priceDetail.setLinkedProduct(MeetingPackageProduct.PRODUCT_TYPE_LINKED.equals(meetingPackageProduct.getType()));
        priceDetail.setMeetingRoomPrices(meetingRoomPrices);
        if ("LINKED".equals(meetingPackageProduct.getType())) {
            decisions.stream()
                    .map(decision -> new ProductOccupancyKey(meetingPackageProduct.getId(), decision.getOccupancyDate()))
                    .map(adjustmentMap::get)
                    .filter(Objects::nonNull)
                    .findFirst()
                    .ifPresent(priceDetail::setAdjustment);
        }
        return priceDetail;
    }

    protected static MeetingRoomPrice toMeetingRoomPrice(MeetingPackageBarDecision decision,
                                                         BaseMeetingRoomDTO baseMeetingRoom,
                                                         List<MeetingPackageBarDecisionOverride> overrides,
                                                         Map<ProductOccupancyKey, MeetingPackageProductRateOffsetOverride> productRateOffsetOverrideMap,
                                                         Map<PricingDifferentialGroupKey, MeetingPackagePaceBarDecision> paceBarDecisionMap,
                                                         List<MeetingPackageBarDecision> parentProductDecisions) {
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = decision.getFunctionSpaceFunctionRoom();
        MeetingRoomPrice meetingRoom = new MeetingRoomPrice();
        meetingRoom.setMeetingRoomId(functionSpaceFunctionRoom.getId());
        meetingRoom.setMeetingRoomName(functionSpaceFunctionRoom.getName());
        meetingRoom.setMeetingRoomPrice(decision.getFinalBar());
        meetingRoom.setPreviousBar(decision.getPreviousBar());
        meetingRoom.setSpecificOverride(decision.getUserSpecifiedRate());
        meetingRoom.setFloorOverride(decision.getFloorRate());
        meetingRoom.setCeilingOverride(decision.getCeilRate());
        meetingRoom.setPriceHigh(true);
        meetingRoom.setTotalPackagePrice(decision.getTotalPackagePrice());
        meetingRoom.setBaseMeetingRoom(isBaseMeetingRoom(functionSpaceFunctionRoom, baseMeetingRoom));
        PricingDifferentialGroupKey searchKey = new PricingDifferentialGroupKey(decision.getMeetingPackageProduct().getId(), decision.getFunctionSpaceFunctionRoom().getId(), decision.getOccupancyDate());
        Optional <MeetingPackagePaceBarDecision> paceBarDecision = Optional.ofNullable(paceBarDecisionMap.get(searchKey));
        paceBarDecision.ifPresent(mpOverride -> meetingRoom.setOldBar(mpOverride.getFinalBar()));
        if ("LINKED".equalsIgnoreCase(decision.getMeetingPackageProduct().getType()) && isBaseMeetingRoom(functionSpaceFunctionRoom, baseMeetingRoom)) {
            ProductOccupancyKey key = new ProductOccupancyKey(
                    decision.getMeetingPackageProduct().getId(),
                    decision.getOccupancyDate()
            );
            MeetingPackageProductRateOffsetOverride offsetOverride = productRateOffsetOverrideMap.get(key);
            if (Objects.nonNull(offsetOverride)) {
                meetingRoom.setAdjustmentValue(offsetOverride.getOffsetValue());
            }
            Optional<MeetingPackageBarDecision> parentDecision = getParentDecision(
                    decision.getOccupancyDate(),
                    decision.getMeetingPackageProduct().getDependentProductId(),
                    decision.getFunctionSpaceFunctionRoom().getId(), parentProductDecisions);
            parentDecision.ifPresent(pd -> {
                meetingRoom.setBaseProduct(pd.getMeetingPackageProduct().getName());
                meetingRoom.setBaseProductPrice(pd.getFinalBar());
                meetingRoom.setBaseProductSpecificOverride(pd.getUserSpecifiedRate());
                meetingRoom.setBaseProductFloorOverride(pd.getFloorRate());
                meetingRoom.setBaseProductCeilingOverride(pd.getCeilRate());
            });
        }
        return meetingRoom;
    }

    private static Optional<MeetingPackageBarDecision> getParentDecision(Date occupancyDate, Integer dependentProductId, Integer id, List<MeetingPackageBarDecision> parentProductDecisions) {
        return parentProductDecisions.stream().filter(decision-> decision.getMeetingPackageProduct().getId().equals(dependentProductId)
        && decision.getOccupancyDate().equals(occupancyDate)
        && decision.getFunctionSpaceFunctionRoom().getId().equals(id)).findFirst();
    }

    private static boolean isBaseMeetingRoom(FunctionSpaceFunctionRoom meetingRoom,
                                             BaseMeetingRoomDTO baseMeetingRoom) {
        return baseMeetingRoom.getBaseMeetingRoom().getId().equals(meetingRoom.getId());
    }


    private static Optional<MeetingPackageBarDecisionOverride> getOverrideForMeetingPackagePriceDecision(
            MeetingPackageBarDecision decision, List<MeetingPackageBarDecisionOverride> overrides) {

        return overrides.stream()
                .filter(override ->
                        decision.getOccupancyDate().equals(override.getOccupancyDate()) &&
                                decision.getMeetingPackageProduct().getId().equals(override.getMeetingPackageProduct().getId()) &&
                                decision.getFunctionSpaceFunctionRoom().getId().equals(override.getFunctionSpaceFunctionRoom().getId()))
                .findFirst();
    }

    protected static List<SpecialEventDetails> toSpecialEventDetails(Map<String, List<PropertySpecialEvent>> specialEventsMap, LocalDate occupancyDate) {
        List<PropertySpecialEvent> specialEvents = getOverlappingSpecialEvents(occupancyDate, specialEventsMap);
        return specialEvents.stream()
                .map(event -> {
                    SpecialEventDetails details = new SpecialEventDetails();
                    details.setId(event.getId());
                    details.setName(event.getName());
                    details.setInformationOnly(event.isInfoOnly());
                    details.setStartDate(event.getStartDate());
                    details.setEndDate(event.getEndDate());
                    return details;
                })
                .collect(Collectors.toList());
    }

    protected static List<PropertySpecialEvent> getOverlappingSpecialEvents(LocalDate occupancyDate, Map<String, List<PropertySpecialEvent>> specialEventsMap) {
        List<PropertySpecialEvent> overlappingSpecialEvents = new ArrayList<>();

        for (List<PropertySpecialEvent> events : specialEventsMap.values()) {
            for (PropertySpecialEvent event : events) {
                LocalDate eventStart = DateUtil.convertJavaUtilDateToLocalDate(event.getStartDate());
                LocalDate eventEnd = DateUtil.convertJavaUtilDateToLocalDate(event.getEndDate());

                if (!occupancyDate.isBefore(eventStart) && !occupancyDate.isAfter(eventEnd)) {
                    overlappingSpecialEvents.add(event);
                }
            }
        }
        return overlappingSpecialEvents;
    }

    public static List<MeetingPackageProductDTO> toMeetingPackageProductDTOS(List<MeetingPackageProduct> meetingPackageProducts){
        List<MeetingPackageProductDTO> meetingPackageProductDTOS = new ArrayList<>();
        if(ObjectUtils.isNotEmpty(meetingPackageProducts)){
            List<MeetingPackageProductDTO> packageProductDTOS = meetingPackageProducts.stream().map(MeetingPackagePricingMapper::getMeetingPackageProductDTO).collect(Collectors.toList());
            meetingPackageProductDTOS.addAll(packageProductDTOS);
        }
        return meetingPackageProductDTOS;
    }

    private static MeetingPackageProductDTO getMeetingPackageProductDTO(MeetingPackageProduct meetingPackageProduct) {
        MeetingPackageProductDTO meetingPackageProductDTO = new MeetingPackageProductDTO();
        meetingPackageProductDTO.setId(meetingPackageProduct.getId());
        meetingPackageProductDTO.setName(meetingPackageProduct.getName());
        meetingPackageProductDTO.setCode(meetingPackageProduct.getCode());
        meetingPackageProductDTO.setDescription(meetingPackageProduct.getDescription());
        meetingPackageProductDTO.setType(meetingPackageProduct.getType());
        meetingPackageProductDTO.setDisplayOrder(meetingPackageProduct.getDisplayOrder());
        meetingPackageProductDTO.setDependentProductId(meetingPackageProduct.getDependentProductId());
        meetingPackageProductDTO.setSystemDefault(meetingPackageProduct.isSystemDefault());
        meetingPackageProductDTO.setStatusId(meetingPackageProduct.getStatus().getId());
        meetingPackageProductDTO.setUpload(meetingPackageProduct.isUpload());
        meetingPackageProductDTO.setDefaultInactive(meetingPackageProduct.isDefaultInactive());
        meetingPackageProductDTO.setOptimized(meetingPackageProduct.isOptimized());
        meetingPackageProductDTO.setCentrallyManaged(meetingPackageProduct.isCentrallyManaged());
        meetingPackageProductDTO.setOverridable(!OverridableProductEnum.NO_OVERRIDES.equals(meetingPackageProduct.getOverridable()));
        meetingPackageProductDTO.setFloorType(meetingPackageProduct.getFloorType().getId());
        meetingPackageProductDTO.setFloor(meetingPackageProduct.getFloor());
        meetingPackageProductDTO.setOffsetMethod(meetingPackageProduct.getOffsetMethod().getId());
        meetingPackageProductDTO.setFloorPercentage(meetingPackageProduct.getFloorPercentage());
        meetingPackageProductDTO.setRoundingRule(meetingPackageProduct.getRoundingRule().getId());
        meetingPackageProductDTO.setPackageId(meetingPackageProduct.getPackageId());
        InvalidReason invalidReason = meetingPackageProduct.getInvalidReason();
        meetingPackageProductDTO.setInvalidReasonId(invalidReason == null ? null : invalidReason.getId());
        return meetingPackageProductDTO;
    }

    public static void mapToMeetingRooomDTO(List<MeetingRoomDTO> meetingRoomDTOS, List<MeetingPackageProductMeetingRoom> meetingRooms) {
        meetingRooms.forEach(mr -> {
            MeetingRoomDTO dto = new MeetingRoomDTO();
            dto.setId(mr.getId());
            dto.setName(mr.getFunctionSpaceFunctionRoom().getName());
            dto.setBase(false);
            meetingRoomDTOS.add(dto);
        });
    }

    public static void mapToMeetingRoomFromBaseDTO(List<MeetingRoomDTO> meetingRoomDTOS, List<BaseMeetingRoom> baseMeetingRooms) {
        baseMeetingRooms.forEach(mr -> {
            MeetingRoomDTO dto = new MeetingRoomDTO();
            dto.setId(mr.getId());
            dto.setName(mr.getFunctionSpaceFunctionRoom().getName());
            dto.setBase(true);
            meetingRoomDTOS.add(dto);
        });
    }

    public static MeetingPackageOffsetsResponseDTO mapToProductOffsetDTO(List<MeetingPackageProductOffset> mpProductOffsets) {
        MeetingPackageOffsetsResponseDTO offsetsDto = new MeetingPackageOffsetsResponseDTO();
        List<MeetingPackageProductOffsetsDTO> dtos = new ArrayList<>();
        List<SeasonDTO> seasonDTOS = new ArrayList<>();
        Set<String> seasons = new HashSet<>();

        mpProductOffsets.forEach(offset -> {
            addToOffsetDtoList(dtos, offset);
            if(seasons.add(offset.getSeasonName())) {
                addToSeasonDtoList(seasonDTOS, offset);
            }
        });
        offsetsDto.setOffsets(dtos);
        offsetsDto.setSeasons(seasonDTOS);
        return offsetsDto;
    }

    private static void addToSeasonDtoList(List<SeasonDTO> dtos, MeetingPackageProductOffset offset) {
        if(StringUtils.isEmpty(offset.getSeasonName())) {
            return;
        }
        SeasonDTO dto = new SeasonDTO();
        dto.setId(offset.getId());
        dto.setSeasonName(offset.getSeasonName());
        dto.setStatus(offset.getStatus() == TenantStatusEnum.ACTIVE);
        dto.setStartDate(offset.getStartDate());
        dto.setEndDate(offset.getEndDate());
        dto.setNumberOfDays(LocalDateUtils.daysBetween(offset.getStartDate(), offset.getEndDate()) + 1);
        dtos.add(dto);
    }
    private static void addToOffsetDtoList(List<MeetingPackageProductOffsetsDTO> dtos, MeetingPackageProductOffset offset) {
        MeetingPackageProductOffsetsDTO dto = new MeetingPackageProductOffsetsDTO();
        dto.setId(offset.getId());
        dto.setMeetingRoomId(offset.getProductMeetingRoom().getId());
        dto.setMeetingRoomName(offset.getProductMeetingRoom().getFunctionSpaceFunctionRoom().getName());
        dto.setSeasonName(offset.getSeasonName());
        dto.setStatus(offset.getStatus() == TenantStatusEnum.ACTIVE);
        dto.setStartDate(offset.getStartDate());
        dto.setEndDate(offset.getEndDate());
        dto.setSundayOffsetValue(offset.getSundayOffsetValue());
        dto.setMondayOffsetValue(offset.getMondayOffsetValue());
        dto.setTuesdayOffsetValue(offset.getTuesdayOffsetValue());
        dto.setWednesdayOffsetValue(offset.getWednesdayOffsetValue());
        dto.setThursdayOffsetValue(offset.getThursdayOffsetValue());
        dto.setFridayOffsetValue(offset.getFridayOffsetValue());
        dto.setSaturdayOffsetValue(offset.getSaturdayOffsetValue());
        dto.setOffsetMethod(offset.getOffsetMethod().name());
        dtos.add(dto);
    }

    public static List<MeetingPackageProductOffset> mapToProductOffsetEntity(MeetingPackageProduct product, Map<Integer, MeetingPackageProductMeetingRoom> rooms, List<MeetingPackageProductOffsetsDTO> mpProductOffsets) {
        List<MeetingPackageProductOffset> dtos = new ArrayList<>();

        mpProductOffsets.forEach(offset -> {
            MeetingPackageProductOffset dto = new MeetingPackageProductOffset();
            dto.setId(offset.getId());
            dto.setMeetingPackageProduct(product);
            dto.setProductMeetingRoom(rooms.get(offset.getMeetingRoomId()));
            dto.setSeasonName(offset.getSeasonName());
            dto.setStatus(offset.isStatus() ? TenantStatusEnum.ACTIVE: TenantStatusEnum.INACTIVE);
            dto.setStartDate(offset.getStartDate());
            dto.setEndDate(offset.getEndDate());
            dto.setSundayOffsetValue(offset.getSundayOffsetValue());
            dto.setMondayOffsetValue(offset.getMondayOffsetValue());
            dto.setTuesdayOffsetValue(offset.getTuesdayOffsetValue());
            dto.setWednesdayOffsetValue(offset.getWednesdayOffsetValue());
            dto.setThursdayOffsetValue(offset.getThursdayOffsetValue());
            dto.setFridayOffsetValue(offset.getFridayOffsetValue());
            dto.setSaturdayOffsetValue(offset.getSaturdayOffsetValue());
            dto.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
            dtos.add(dto);
        });
        return dtos;
    }

    public static MeetingPackageProductCeilingFloor toMeetingPackageProductCeilingFloorEntity(MeetingPackageProduct product,BaseMeetingRoom baseMeetingRoom, MeetingPackageProductBaseRoomCeilingFloorDTO meetingPackageProductBaseRoomCeilingFloorDTO){
        MeetingPackageProductCeilingFloor meetingPackageProductCeilingFloor = new MeetingPackageProductCeilingFloor();
        meetingPackageProductCeilingFloor.setId(meetingPackageProductBaseRoomCeilingFloorDTO.getId());
        meetingPackageProductCeilingFloor.setStatus(meetingPackageProductBaseRoomCeilingFloorDTO.isStatus() ? Status.ACTIVE : Status.INACTIVE);
        meetingPackageProductCeilingFloor.setSeasonName(meetingPackageProductBaseRoomCeilingFloorDTO.getSeasonName());

        meetingPackageProductCeilingFloor.setBaseMeetingRoom(baseMeetingRoom);

        meetingPackageProductCeilingFloor.setMeetingPackageProduct(product);

        meetingPackageProductCeilingFloor.setStartDate(meetingPackageProductBaseRoomCeilingFloorDTO.getStartDate());
        meetingPackageProductCeilingFloor.setEndDate(meetingPackageProductBaseRoomCeilingFloorDTO.getEndDate());

        meetingPackageProductCeilingFloor.setSundayFloorRate(meetingPackageProductBaseRoomCeilingFloorDTO.getSundayFloorRate());
        meetingPackageProductCeilingFloor.setSundayCeilingRate(meetingPackageProductBaseRoomCeilingFloorDTO.getSundayCeilRate());

        meetingPackageProductCeilingFloor.setMondayFloorRate(meetingPackageProductBaseRoomCeilingFloorDTO.getMondayFloorRate());
        meetingPackageProductCeilingFloor.setMondayCeilingRate(meetingPackageProductBaseRoomCeilingFloorDTO.getMondayCeilRate());

        meetingPackageProductCeilingFloor.setTuesdayFloorRate(meetingPackageProductBaseRoomCeilingFloorDTO.getTuesdayFloorRate());
        meetingPackageProductCeilingFloor.setTuesdayCeilingRate(meetingPackageProductBaseRoomCeilingFloorDTO.getTuesdayCeilRate());

        meetingPackageProductCeilingFloor.setWednesdayFloorRate(meetingPackageProductBaseRoomCeilingFloorDTO.getWednesdayFloorRate());
        meetingPackageProductCeilingFloor.setWednesdayCeilingRate(meetingPackageProductBaseRoomCeilingFloorDTO.getWednesdayCeilRate());

        meetingPackageProductCeilingFloor.setThursdayFloorRate(meetingPackageProductBaseRoomCeilingFloorDTO.getThursdayFloorRate());
        meetingPackageProductCeilingFloor.setThursdayCeilingRate(meetingPackageProductBaseRoomCeilingFloorDTO.getThursdayCeilRate());

        meetingPackageProductCeilingFloor.setFridayFloorRate(meetingPackageProductBaseRoomCeilingFloorDTO.getFridayFloorRate());
        meetingPackageProductCeilingFloor.setFridayCeilingRate(meetingPackageProductBaseRoomCeilingFloorDTO.getFridayCeilRate());

        meetingPackageProductCeilingFloor.setSaturdayFloorRate(meetingPackageProductBaseRoomCeilingFloorDTO.getSaturdayFloorRate());
        meetingPackageProductCeilingFloor.setSaturdayCeilingRate(meetingPackageProductBaseRoomCeilingFloorDTO.getSaturdayCeilRate());

        return meetingPackageProductCeilingFloor;
    }

    public static MeetingPackageProductCeilingFloorResponseDTO mapToMeetingProductCeilingFloorResponseDTO(List<MeetingPackageProductCeilingFloor> ceilingFloorDTOS) {
        MeetingPackageProductCeilingFloorResponseDTO productCeilingFloorResponseDTO  = new MeetingPackageProductCeilingFloorResponseDTO();
        List<MeetingPackageProductBaseRoomCeilingFloorDTO> baseRoomCeilingFloorDTOS = new ArrayList<>();
        List<SeasonDTO> seasonDTOS = new ArrayList<>();

        ceilingFloorDTOS.forEach(ceilingFloorDTO -> {
            addToCeilingFloorDtoList(baseRoomCeilingFloorDTOS, ceilingFloorDTO);
            addToCeilingFloorSeasonDtoList(seasonDTOS, ceilingFloorDTO);
        });
        productCeilingFloorResponseDTO.setCeilingFloor(baseRoomCeilingFloorDTOS);
        productCeilingFloorResponseDTO.setSeasons(seasonDTOS);
        return productCeilingFloorResponseDTO;
    }

    private static void addToCeilingFloorSeasonDtoList(List<SeasonDTO> seasonDTOS, MeetingPackageProductCeilingFloor meetingPackageProductCeilingFloor) {
        if(StringUtils.isEmpty(meetingPackageProductCeilingFloor.getSeasonName())) {
            return;
        }
        SeasonDTO dto = new SeasonDTO();
        dto.setId(meetingPackageProductCeilingFloor.getId());
        dto.setSeasonName(meetingPackageProductCeilingFloor.getSeasonName());
        dto.setStatus(meetingPackageProductCeilingFloor.getStatus() == Status.ACTIVE);
        dto.setStartDate(meetingPackageProductCeilingFloor.getStartDate());
        dto.setEndDate(meetingPackageProductCeilingFloor.getEndDate());
        dto.setNumberOfDays(LocalDateUtils.daysBetween(meetingPackageProductCeilingFloor.getStartDate(), meetingPackageProductCeilingFloor.getEndDate()) + 1);
        seasonDTOS.add(dto);
    }

    private static void addToCeilingFloorDtoList(List<MeetingPackageProductBaseRoomCeilingFloorDTO> ceilingFloorDTOS, MeetingPackageProductCeilingFloor productCeilingFloor) {
        MeetingPackageProductBaseRoomCeilingFloorDTO dto = new MeetingPackageProductBaseRoomCeilingFloorDTO();
        dto.setId(productCeilingFloor.getId());
        dto.setBaseMeetingRoomId(productCeilingFloor.getBaseMeetingRoom().getId());
        dto.setSeasonName(productCeilingFloor.getSeasonName());
        dto.setStatus(productCeilingFloor.getStatus() == Status.ACTIVE);
        dto.setStartDate(productCeilingFloor.getStartDate());
        dto.setEndDate(productCeilingFloor.getEndDate());
        dto.setSundayFloorRate(productCeilingFloor.getSundayFloorRate());
        dto.setSundayCeilRate(productCeilingFloor.getSundayCeilingRate());
        dto.setMondayFloorRate(productCeilingFloor.getMondayFloorRate());
        dto.setMondayCeilRate(productCeilingFloor.getMondayCeilingRate());
        dto.setTuesdayFloorRate(productCeilingFloor.getTuesdayFloorRate());
        dto.setTuesdayCeilRate(productCeilingFloor.getTuesdayCeilingRate());
        dto.setWednesdayFloorRate(productCeilingFloor.getWednesdayFloorRate());
        dto.setWednesdayCeilRate(productCeilingFloor.getWednesdayCeilingRate());
        dto.setThursdayFloorRate(productCeilingFloor.getThursdayFloorRate());
        dto.setThursdayCeilRate(productCeilingFloor.getThursdayCeilingRate());
        dto.setFridayFloorRate(productCeilingFloor.getFridayFloorRate());
        dto.setFridayCeilRate(productCeilingFloor.getFridayCeilingRate());
        dto.setSaturdayFloorRate(productCeilingFloor.getSaturdayFloorRate());
        dto.setSaturdayCeilRate(productCeilingFloor.getSaturdayCeilingRate());
        ceilingFloorDTOS.add(dto);
    }


    public static List<MeetingRoomDTO> toMeetingRoomDTOS(List<FunctionSpaceFunctionRoom> meetingRooms){
        List<MeetingRoomDTO> meetingRoomDTOS = new ArrayList<>();
        if(ObjectUtils.isNotEmpty(meetingRooms)){
            List<MeetingRoomDTO> roomDTOS = meetingRooms.stream().map(functionSpaceFunctionRoom -> {
                MeetingRoomDTO meetingRoomDTO = new MeetingRoomDTO();
                meetingRoomDTO.setId(functionSpaceFunctionRoom.getId());
                meetingRoomDTO.setName(functionSpaceFunctionRoom.getName());
                return meetingRoomDTO;
            }).collect(Collectors.toList());
            meetingRoomDTOS.addAll(roomDTOS);
        }
        return meetingRoomDTOS;
    }

    public static <T> List<MeetingRoomDTO> toMeetingRoomDTOS(List<T> meetingRooms, Function<T, MeetingRoomDTO> mapper) {
        return (meetingRooms != null && !meetingRooms.isEmpty()) ?
                meetingRooms.stream().map(mapper).collect(Collectors.toList()) :
                new ArrayList<>();
    }

    public static MeetingRoomDTO mapFunctionSpaceFunctionRoomToMeetingRoomDTO(FunctionSpaceFunctionRoom functionSpaceFunctionRoom) {
        MeetingRoomDTO meetingRoomDTO = new MeetingRoomDTO();
        meetingRoomDTO.setId(functionSpaceFunctionRoom.getId());
        meetingRoomDTO.setName(functionSpaceFunctionRoom.getName());
        return meetingRoomDTO;
    }

    public static MeetingRoomDTO mapMeetingPackageProductMeetingRoomToMeetingRoomDTO(MeetingPackageProductMeetingRoom meetingPackageProductMeetingRoom) {
        MeetingRoomDTO meetingRoomDTO = new MeetingRoomDTO();
        meetingRoomDTO.setId(meetingPackageProductMeetingRoom.getFunctionSpaceFunctionRoom().getId());
        meetingRoomDTO.setName(meetingPackageProductMeetingRoom.getFunctionSpaceFunctionRoom().getName());
        return meetingRoomDTO;
    }

    public static BaseMeetingRoomDTO toBaseMeetingRoomDTO(BaseMeetingRoom baseMeetingRoom){
        BaseMeetingRoomDTO baseMeetingRoomDTO = new BaseMeetingRoomDTO();
        if(Objects.nonNull(baseMeetingRoom)){
            baseMeetingRoomDTO.setId(baseMeetingRoom.getId());
            baseMeetingRoomDTO.setPriceExcluded(baseMeetingRoom.isPriceExcluded());
            baseMeetingRoomDTO.setMinimumChangeMethod(mapToMinChangeMethodDTO(baseMeetingRoom.getMinimumIncrementMethod().name()));
            baseMeetingRoomDTO.setMinimumChangeValue(baseMeetingRoom.getMinimumIncrementValue());
            baseMeetingRoomDTO.setBaseMeetingRoom(mapToMeetingRoomDTO(baseMeetingRoom.getFunctionSpaceFunctionRoom(), true));
        }
        return baseMeetingRoomDTO;
    }

    public static MeetingRoomDTO mapToMeetingRoomDTO(FunctionSpaceFunctionRoom functionSpaceFunctionRoom, boolean isBase){
        MeetingRoomDTO meetingRoomDTO = new MeetingRoomDTO();
        meetingRoomDTO.setId(functionSpaceFunctionRoom.getId());
        meetingRoomDTO.setName(functionSpaceFunctionRoom.getName());
        meetingRoomDTO.setBase(isBase);
        return meetingRoomDTO;
    }

    public static MinChangeMethodDTO mapToMinChangeMethodDTO(String enumName){
        MinChangeMethodDTO minChangeMethodDTO = new MinChangeMethodDTO();
        minChangeMethodDTO.setName(MinimumIncrementMethod.valueOf(enumName).equals(MinimumIncrementMethod.FIXED_OFFSET) ? "Fixed" : "Percentage");
        minChangeMethodDTO.setValue(enumName);
        return minChangeMethodDTO;
    }

    public static BaseMeetingRoom mapToBaseMeetingRoomEntity(FunctionSpaceFunctionRoom functionSpaceFunctionRoom, BaseMeetingRoomDTO baseMeetingRoomDTO){
        BaseMeetingRoom baseMeetingRoom = new BaseMeetingRoom();
        baseMeetingRoom.setId(baseMeetingRoomDTO.getId());
        baseMeetingRoom.setFunctionSpaceFunctionRoom(functionSpaceFunctionRoom);
        baseMeetingRoom.setPriceExcluded(baseMeetingRoomDTO.isPriceExcluded());
        baseMeetingRoom.setMinimumIncrementMethod(MinimumIncrementMethod.valueOf(baseMeetingRoomDTO.getMinimumChangeMethod().getValue()));
        baseMeetingRoom.setMinimumIncrementValue(baseMeetingRoomDTO.getMinimumChangeValue());
        return baseMeetingRoom;
    }

    public static MeetingPackageProductDefinitionDTO mapToMeetingPackageProductDefinitionDTO(MeetingPackageProduct meetingPackageProduct, List<MeetingRoomDTO> availableMeetingRooms, List<MeetingRoomDTO> selectedMeetingRooms){
        MeetingPackageProductDefinitionDTO meetingPackageProductDefinitionDTO = new MeetingPackageProductDefinitionDTO();
        meetingPackageProductDefinitionDTO.setProduct(getMeetingPackageProductDTO(meetingPackageProduct));
        meetingPackageProductDefinitionDTO.setDayPart(mapToMeetingProductDayPartDTO(meetingPackageProduct.getMeetingProductDayPart(),new HashSet<>()));
        meetingPackageProductDefinitionDTO.setAvailableMeetingRooms(availableMeetingRooms);
        meetingPackageProductDefinitionDTO.setSelectedMeetingRooms(selectedMeetingRooms);
        return meetingPackageProductDefinitionDTO;
    }

    public static MeetingPackageProductDefinitionDTO mapToPackageElementProductDefinitionDTO(MeetingPackageProductDefinitionDTO meetingPackageProductDefinitionDTO, List<MeetingPackageElementDTO> availableMeetingPackageElements, List<MeetingPackageElementDTO> selectedMeetingPackageElements){
        meetingPackageProductDefinitionDTO.setAvailablePackageElements(availableMeetingPackageElements);
        meetingPackageProductDefinitionDTO.setSelectedPackageElements(selectedMeetingPackageElements);
        return meetingPackageProductDefinitionDTO;
    }


    public static MeetingProductDayPartDTO mapToMeetingProductDayPartDTO(MeetingProductDayPart meetingProductDayPart,Set<Integer> useDayPartIds){
        MeetingProductDayPartDTO meetingProductDayPartDTO = new MeetingProductDayPartDTO();
        meetingProductDayPartDTO.setId(meetingProductDayPart.getId());
        meetingProductDayPartDTO.setName(meetingProductDayPart.getName());
        meetingProductDayPartDTO.setStatus(Status.ACTIVE.equals(meetingProductDayPart.getStatus()));
        meetingProductDayPartDTO.setStartTime(meetingProductDayPart.getStartTime());
        meetingProductDayPartDTO.setEndTime(meetingProductDayPart.getEndTime());
        meetingProductDayPartDTO.setUsedInProduct(!useDayPartIds.isEmpty() && useDayPartIds.contains(meetingProductDayPart.getId()));
        return  meetingProductDayPartDTO;
    }

    public static List<MeetingProductDayPartDTO> mapToMeetingProductDayPartDTOS(List<MeetingProductDayPart> meetingProductDayParts, Set<Integer> useDayPartIds){
        List<MeetingProductDayPartDTO> meetingProductDayPartDTOS = new ArrayList<>();
        if(ObjectUtils.isNotEmpty(meetingProductDayParts)){
            List<MeetingProductDayPartDTO> productDayPartDTOS = meetingProductDayParts.stream().map(productDayPart -> mapToMeetingProductDayPartDTO(productDayPart, useDayPartIds)).collect(Collectors.toList());
            meetingProductDayPartDTOS.addAll(productDayPartDTOS);
        }
        return meetingProductDayPartDTOS;
    }

    public static MeetingPackageProduct mapToMeetingPackageProductEntity(MeetingPackageProductDTO meetingPackageProductDTO, MeetingProductDayPart meetingProductDayPart){
        MeetingPackageProduct meetingPackageProduct = new MeetingPackageProduct();
        meetingPackageProduct.setId(meetingPackageProductDTO.getId());
        meetingPackageProduct.setName(meetingPackageProductDTO.getName());
        meetingPackageProduct.setDependentProductId(meetingPackageProductDTO.getDependentProductId());
        meetingPackageProduct.setDescription(meetingPackageProductDTO.getDescription());
        meetingPackageProduct.setCode(meetingPackageProductDTO.getCode());
        meetingPackageProduct.setType(meetingPackageProductDTO.getType());
        meetingPackageProduct.setStatus(TenantStatusEnum.valueOfId(meetingPackageProductDTO.getStatusId()));
        meetingPackageProduct.setOptimized(meetingPackageProductDTO.isOptimized());
        meetingPackageProduct.setCentrallyManaged(meetingPackageProductDTO.isCentrallyManaged());
        meetingPackageProduct.setUpload(meetingPackageProductDTO.isUpload());
        meetingPackageProduct.setSystemDefault(meetingPackageProductDTO.isSystemDefault());
        meetingPackageProduct.setDefaultInactive(meetingPackageProduct.isDefaultInactive());
        meetingPackageProduct.setOverridable(meetingPackageProductDTO.isOverridable() ? OverridableProductEnum.ALLOW_OVERRIDES: OverridableProductEnum.NO_OVERRIDES);
        meetingPackageProduct.setFloorType(FloorType.valueOfId(meetingPackageProductDTO.getFloorType()));
        meetingPackageProduct.setFloor(meetingPackageProductDTO.getFloor());
        meetingPackageProduct.setFloorPercentage(meetingPackageProductDTO.getFloorPercentage());
        meetingPackageProduct.setOffsetMethod(AgileRatesOffsetMethod.valueOfId(meetingPackageProductDTO.getOffsetMethod()));
        meetingPackageProduct.setRoundingRule(RoundingRule.valueOfId(meetingPackageProductDTO.getRoundingRule()));
        meetingPackageProduct.setMeetingProductDayPart(meetingProductDayPart);
        meetingPackageProduct.setPackageId(meetingPackageProductDTO.getPackageId());
        Integer invalidReasonId = meetingPackageProductDTO.getInvalidReasonId();
        meetingPackageProduct.setInvalidReason(invalidReasonId == null ? null : InvalidReason.valueOfId(invalidReasonId));
        return meetingPackageProduct;
    }

    public static List<MeetingPackageElementDTO> mapToMeetingPackageElementsDTOS(List<MeetingPackageElement> meetingPackageElements, Set<Integer> elementsUseInProduct){
        List<MeetingPackageElementDTO> meetingPackageElementDTO = new ArrayList<>();
        if(ObjectUtils.isNotEmpty(meetingPackageElements)){
            List<MeetingPackageElementDTO> meetingPackageElementDTOS = meetingPackageElements.stream().map(element->mapToMeetingPackageElementDTO(element,elementsUseInProduct)).collect(Collectors.toList());
            meetingPackageElementDTO.addAll(meetingPackageElementDTOS);
        }
        return meetingPackageElementDTO;
    }

    public static MeetingPackageElementDTO mapToMeetingPackageElementDTO(MeetingPackageElement meetingPackageElement, Set<Integer> elementsUseInProduct) {
        MeetingPackageElementDTO packageElementDTO = new MeetingPackageElementDTO();
        packageElementDTO.setId(meetingPackageElement.getId());
        packageElementDTO.setName(meetingPackageElement.getName());

        FunctionSpaceRevenueGroup functionSpaceRevenueGroup = meetingPackageElement.getFunctionSpaceRevenueGroup();
        RevenueGroupDto revenueGroupDto = new RevenueGroupDto();
        revenueGroupDto.setId(functionSpaceRevenueGroup.getId());
        revenueGroupDto.setName(functionSpaceRevenueGroup.getName());
        revenueGroupDto.setProfitPercent(functionSpaceRevenueGroup.getProfitPercent());
        packageElementDTO.setRevenueStream(revenueGroupDto);

        packageElementDTO.setOffsetMethod(meetingPackageElement.getOffsetMethod().name());
        packageElementDTO.setOffsetValue(meetingPackageElement.getOffsetValue());
        packageElementDTO.setUsedInProduct(!elementsUseInProduct.isEmpty() && elementsUseInProduct.contains(meetingPackageElement.getId()));
        return packageElementDTO;
    }

    public static List<MeetingProductMeetingPackageElement> mapToMeetingPackageElementRoomEntity(MeetingPackageProduct meetingPackageProduct,
                                                                                                 List<MeetingPackageElement> packageElements) {
        List<MeetingProductMeetingPackageElement> mpPackageElements = new ArrayList<>();
        packageElements.forEach(elm -> {
            MeetingProductMeetingPackageElement meetingPackageElement = new MeetingProductMeetingPackageElement();
            meetingPackageElement.setMeetingPackageProduct(meetingPackageProduct);
            meetingPackageElement.setMeetingPackageElement(elm);
            mpPackageElements.add(meetingPackageElement);
        });
        return mpPackageElements;
    }

    public static List<MeetingPackageProductMeetingRoom> mapToMeetingPackageProductMeetingRoomEntity(MeetingPackageProduct meetingPackageProduct, List<FunctionSpaceFunctionRoom> selectedMeetingRooms){
        List<MeetingPackageProductMeetingRoom> meetingPackageProductMeetingRooms = new ArrayList<>();
        selectedMeetingRooms.forEach(functionSpaceFunctionRoom -> {
            MeetingPackageProductMeetingRoom meetingPackageProductMeetingRoom = new MeetingPackageProductMeetingRoom();
            meetingPackageProductMeetingRoom.setMeetingPackageProduct(meetingPackageProduct);
            meetingPackageProductMeetingRoom.setFunctionSpaceFunctionRoom(functionSpaceFunctionRoom);
            meetingPackageProductMeetingRooms.add(meetingPackageProductMeetingRoom);
        });
        return meetingPackageProductMeetingRooms;
    }

    public static List<MeetingProductDayPart> mapToMeetingProductDayPartEntity(List<MeetingProductDayPart> dayPartsFromDB,
                                                                               List<MeetingProductDayPartDTO> meetingProductDayPartDTO) {
        Map<Integer, MeetingProductDayPart> meetingProductDayPartMap = dayPartsFromDB.stream()
                .collect(Collectors.toMap(MeetingProductDayPart::getId, dayPart -> dayPart));
        List<MeetingProductDayPart> dayPartsToBeSaved = new ArrayList<>();
        meetingProductDayPartDTO.forEach(dto -> {
            MeetingProductDayPart dayPart = meetingProductDayPartMap.getOrDefault(dto.getId(), new MeetingProductDayPart());
            getMeetingProductDayPart(dayPart, dto);
            dayPartsToBeSaved.add(dayPart);
        });
        return dayPartsToBeSaved;
    }

    private static MeetingProductDayPart getMeetingProductDayPart(MeetingProductDayPart meetingProductDayPart, MeetingProductDayPartDTO dto) {
        meetingProductDayPart.setId(dto.getId());
        meetingProductDayPart.setName(dto.getName());
        meetingProductDayPart.setStatus(Status.ACTIVE);
        meetingProductDayPart.setStartTime(dto.getStartTime());
        meetingProductDayPart.setEndTime(dto.getEndTime());
        return meetingProductDayPart;
    }

    public static  List<MeetingPackageElement> mapToMeetingPackageElementEntity(List<MeetingPackageElement> packageElementsFromDB, List<MeetingPackageElementDTO> meetingPackageElementDTOS) {
        Map<Integer, MeetingPackageElement> meetingPackageElementMap = packageElementsFromDB.stream().collect(Collectors.toMap(MeetingPackageElement::getId, x -> x));
        List<MeetingPackageElement> packageElementsToBeSaved = new ArrayList<>();
        meetingPackageElementDTOS.forEach(dto -> {
            MeetingPackageElement meetingPackageElement = meetingPackageElementMap.getOrDefault(dto.getId(), new MeetingPackageElement());
            mapMeetingPkgElmDTOToEntity(dto, meetingPackageElement);
            packageElementsToBeSaved.add(meetingPackageElement);
        });
        return packageElementsToBeSaved;
    }

    private static void mapMeetingPkgElmDTOToEntity(MeetingPackageElementDTO dto, MeetingPackageElement meetingPackageElement) {
        meetingPackageElement.setId(dto.getId());
        meetingPackageElement.setName(dto.getName());
        meetingPackageElement.setOffsetValue(dto.getOffsetValue());
        meetingPackageElement.setOffsetMethod(AgileRatesOffsetMethod.FIXED);

        RevenueGroupDto revenueGroupDto = dto.getRevenueStream();
        FunctionSpaceRevenueGroup functionSpaceRevenueGroup = new FunctionSpaceRevenueGroup();
        functionSpaceRevenueGroup.setId(revenueGroupDto.getId());
        functionSpaceRevenueGroup.setName(revenueGroupDto.getName());
        functionSpaceRevenueGroup.setProfitPercent(revenueGroupDto.getProfitPercent());
        meetingPackageElement.setFunctionSpaceRevenueGroup(functionSpaceRevenueGroup);
    }


    public static List<MeetingPackageProductRateOffset> toMeetingPackageProductRateOffsetEntity(MeetingPackageProduct product,List<MeetingPackageProductRateOffsetDTO> productRateOffsetDTOS){
        List<MeetingPackageProductRateOffset> productRateOffsets = new ArrayList<>();
        productRateOffsetDTOS.forEach(adjustment->{
            MeetingPackageProductRateOffset productRateOffset = new MeetingPackageProductRateOffset();

            productRateOffset.setId(adjustment.getId());
            productRateOffset.setSeasonName(adjustment.getSeasonName());
            productRateOffset.setOffsetMethod(AgileRatesOffsetMethod.valueOfId(adjustment.getOffsetMethod()));

            productRateOffset.setMeetingPackageProduct(product);

            productRateOffset.setStartDate(adjustment.getStartDate());
            productRateOffset.setEndDate(adjustment.getEndDate());

            productRateOffset.setSundayOffsetValueFloor(adjustment.getSundayOffsetFloorRate());
            productRateOffset.setSundayOffsetValueCeiling(adjustment.getSundayOffsetCeilRate());

            productRateOffset.setMondayOffsetValueFloor(adjustment.getMondayOffsetFloorRate());
            productRateOffset.setMondayOffsetValueCeiling(adjustment.getMondayOffsetCeilRate());

            productRateOffset.setTuesdayOffsetValueFloor(adjustment.getTuesdayOffsetFloorRate());
            productRateOffset.setTuesdayOffsetValueCeiling(adjustment.getTuesdayOffsetCeilRate());

            productRateOffset.setWednesdayOffsetValueFloor(adjustment.getWednesdayOffsetFloorRate());
            productRateOffset.setWednesdayOffsetValueCeiling(adjustment.getWednesdayOffsetCeilRate());

            productRateOffset.setThursdayOffsetValueFloor(adjustment.getThursdayOffsetFloorRate());
            productRateOffset.setThursdayOffsetValueCeiling(adjustment.getThursdayOffsetCeilRate());

            productRateOffset.setFridayOffsetValueFloor(adjustment.getFridayOffsetFloorRate());
            productRateOffset.setFridayOffsetValueCeiling(adjustment.getFridayOffsetCeilRate());

            productRateOffset.setSaturdayOffsetValueFloor(adjustment.getSaturdayOffsetFloorRate());
            productRateOffset.setSaturdayOffsetValueCeiling(adjustment.getSaturdayOffsetCeilRate());

            productRateOffsets.add(productRateOffset);
        });


        return productRateOffsets;
    }

    public static MeetingPackageProductRateOffsetResponseDTO mapToMeetingPackageProductRateOffsetResponseDTO(List<MeetingPackageProductRateOffset> meetingPackageProductRateOffsets) {
        MeetingPackageProductRateOffsetResponseDTO meetingPackageProductRateOffsetResponseDTO  = new MeetingPackageProductRateOffsetResponseDTO();
        List<MeetingPackageProductRateOffsetDTO> productRateOffsetDTOS = new ArrayList<>();
        List<SeasonDTO> seasonDTOS = new ArrayList<>();
        Set<String> seasons = new HashSet<>();

        meetingPackageProductRateOffsets.forEach(rateOffset -> {
            addToProductRateOffset(productRateOffsetDTOS, rateOffset);
            if(seasons.add(rateOffset.getSeasonName())) {
                addToSeasonDtoList(seasonDTOS, rateOffset);
            }
        });
        meetingPackageProductRateOffsetResponseDTO.setOffsets(productRateOffsetDTOS);
        meetingPackageProductRateOffsetResponseDTO.setSeasons(seasonDTOS);
        return meetingPackageProductRateOffsetResponseDTO;
    }

    private static void addToProductRateOffset(List<MeetingPackageProductRateOffsetDTO> productRateOffsetDTOS, MeetingPackageProductRateOffset rateOffset) {
        MeetingPackageProductRateOffsetDTO productRateOffsetDTO = new MeetingPackageProductRateOffsetDTO();
        productRateOffsetDTO.setId(rateOffset.getId());
        productRateOffsetDTO.setStartDate(rateOffset.getStartDate());
        productRateOffsetDTO.setEndDate(rateOffset.getEndDate());
        productRateOffsetDTO.setOffsetMethod(rateOffset.getOffsetMethod().getId());
        productRateOffsetDTO.setSeasonName(rateOffset.getSeasonName());
        productRateOffsetDTO.setSeasonDowOffset(rateOffset.isSeasonDowOffset());
        productRateOffsetDTO.setSundayOffsetCeilRate(rateOffset.getSundayOffsetValueCeiling());
        productRateOffsetDTO.setSundayOffsetFloorRate(rateOffset.getSundayOffsetValueFloor());

        productRateOffsetDTO.setMondayOffsetCeilRate(rateOffset.getMondayOffsetValueCeiling());
        productRateOffsetDTO.setMondayOffsetFloorRate(rateOffset.getMondayOffsetValueFloor());

        productRateOffsetDTO.setTuesdayOffsetCeilRate(rateOffset.getTuesdayOffsetValueCeiling());
        productRateOffsetDTO.setTuesdayOffsetFloorRate(rateOffset.getTuesdayOffsetValueFloor());

        productRateOffsetDTO.setWednesdayOffsetCeilRate(rateOffset.getWednesdayOffsetValueCeiling());
        productRateOffsetDTO.setWednesdayOffsetFloorRate(rateOffset.getWednesdayOffsetValueFloor());

        productRateOffsetDTO.setThursdayOffsetCeilRate(rateOffset.getThursdayOffsetValueCeiling());
        productRateOffsetDTO.setThursdayOffsetFloorRate(rateOffset.getThursdayOffsetValueFloor());

        productRateOffsetDTO.setFridayOffsetCeilRate(rateOffset.getFridayOffsetValueCeiling());
        productRateOffsetDTO.setFridayOffsetFloorRate(rateOffset.getFridayOffsetValueFloor());

        productRateOffsetDTO.setSaturdayOffsetCeilRate(rateOffset.getSaturdayOffsetValueCeiling());
        productRateOffsetDTO.setSaturdayOffsetFloorRate(rateOffset.getSaturdayOffsetValueFloor());

        productRateOffsetDTOS.add(productRateOffsetDTO);
    }

    private static void addToSeasonDtoList(List<SeasonDTO> dtos, MeetingPackageProductRateOffset offset) {
        if(StringUtils.isEmpty(offset.getSeasonName())) {
            return;
        }
        SeasonDTO dto = new SeasonDTO();
        dto.setId(offset.getId());
        dto.setSeasonName(offset.getSeasonName());
        dto.setStartDate(offset.getStartDate());
        dto.setEndDate(offset.getEndDate());
        dto.setNumberOfDays(LocalDateUtils.daysBetween(offset.getStartDate(), offset.getEndDate()) + 1);
        dtos.add(dto);
    }
    public static List<RevenueGroupDto> mapToRevenueGroupDTOS(List<FunctionSpaceRevenueGroup> functionSpaceRevenueGroups) {
        List<RevenueGroupDto> revenueGroupDtos = new ArrayList<>();
        functionSpaceRevenueGroups.forEach(functionSpaceRevenueGroup -> {
            RevenueGroupDto revenueGroupDto = new RevenueGroupDto();
            revenueGroupDto.setId(functionSpaceRevenueGroup.getId());
            revenueGroupDto.setName(functionSpaceRevenueGroup.getName());
            revenueGroupDto.setProfitPercent(functionSpaceRevenueGroup.getProfitPercent());

            revenueGroupDtos.add(revenueGroupDto);
        });
        return revenueGroupDtos;
    }

    public static List<MeetingPackagePricingPerDay> toPricingPerDayFromPace(List<MeetingPackagePaceBarDecision> decisions,
                                                                    BaseMeetingRoomDTO baseMeetingRoom,
                                                                    Map<String, List<PropertySpecialEvent>> specialEventsMap,
                                                                    List<MeetingPackageBarDecisionOverride> meetingPackageBarDecisionOverrides,
                                                                    Map<ProductOccupancyKey, MeetingPackageProductRateOffsetOverride> productRateOffsetOverrideMap,
                                                                    Map<ProductOccupancyKey, BigDecimal> adjustmentMap) {
        Map<Date, List<MeetingPackagePaceBarDecision>> decisionByOccupancyDate = decisions.stream()
                .collect(Collectors.groupingBy(MeetingPackagePaceBarDecision::getOccupancyDate, Collectors.toList()));
        return decisionByOccupancyDate.keySet().stream()
                .map(occupancyDate -> {
                    List<MeetingPackagePaceBarDecision> paceDecisions = decisionByOccupancyDate.get(occupancyDate);
                    List<MeetingPackagePriceDetail> meetingPackagePriceDetails = createMeetingPackagePriceDetailsFromPace(
                            paceDecisions,
                            baseMeetingRoom,
                            meetingPackageBarDecisionOverrides,
                            productRateOffsetOverrideMap,
                            adjustmentMap);
                    return createMeetingPackagePricingPerDay(occupancyDate, specialEventsMap, meetingPackagePriceDetails);
                })
                .collect(Collectors.toList());
    }

    private static List<MeetingPackagePriceDetail> createMeetingPackagePriceDetailsFromPace(List<MeetingPackagePaceBarDecision> meetingPackageBarDecisions,
                                                                                    BaseMeetingRoomDTO baseMeetingRoom,
                                                                                    List<MeetingPackageBarDecisionOverride> meetingPackageBarDecisionOverrides,
                                                                                    Map<ProductOccupancyKey, MeetingPackageProductRateOffsetOverride> productRateOffsetOverrideMap,
                                                                                    Map<ProductOccupancyKey, BigDecimal> adjustmentMap) {
        Map<Integer, List<MeetingPackagePaceBarDecision>> decisionsByProductId = meetingPackageBarDecisions.stream()
                .collect(Collectors.groupingBy(paceDecision -> paceDecision.getMeetingPackageProduct().getId(), Collectors.toList()));
        return decisionsByProductId.keySet().stream()
                .map(productId -> {
                    List<MeetingPackagePaceBarDecision> decisions = decisionsByProductId.get(productId);
                    MeetingPackageProduct meetingPackageProduct = decisions.get(0).getMeetingPackageProduct();
                    return createMeetingPackagePriceDetailFromPace(
                            meetingPackageProduct,
                            baseMeetingRoom,
                            decisions,
                            meetingPackageBarDecisionOverrides,
                            productRateOffsetOverrideMap,
                            adjustmentMap);
                })
                .collect(Collectors.toList());
    }

    private static MeetingPackagePriceDetail createMeetingPackagePriceDetailFromPace(MeetingPackageProduct meetingPackageProduct,
                                                                                     BaseMeetingRoomDTO baseMeetingRoom,
                                                                                     List<MeetingPackagePaceBarDecision> decisions,
                                                                                     List<MeetingPackageBarDecisionOverride> meetingPackageBarDecisionOverrides,
                                                                                     Map<ProductOccupancyKey, MeetingPackageProductRateOffsetOverride> productRateOffsetOverrideMap,
                                                                                     Map<ProductOccupancyKey, BigDecimal> adjustmentMap) {
        List<MeetingRoomPrice> meetingRoomPrices = decisions.stream()
                .map(decision -> toMeetingRoomPriceFromPace(decision, baseMeetingRoom, meetingPackageBarDecisionOverrides, productRateOffsetOverrideMap))
                .collect(Collectors.toList());
        MeetingPackagePriceDetail priceDetail = new MeetingPackagePriceDetail();
        priceDetail.setProductId(meetingPackageProduct.getId());
        priceDetail.setProductName(meetingPackageProduct.getName());
        priceDetail.setLinkedProduct(MeetingPackageProduct.PRODUCT_TYPE_LINKED.equals(meetingPackageProduct.getType()));
        priceDetail.setMeetingRoomPrices(meetingRoomPrices);
        if ("LINKED".equals(meetingPackageProduct.getType())) {
            decisions.stream()
                    .map(decision -> new ProductOccupancyKey(meetingPackageProduct.getId(), decision.getOccupancyDate()))
                    .map(adjustmentMap::get)
                    .filter(Objects::nonNull)
                    .findFirst()
                    .ifPresent(priceDetail::setAdjustment);
        }
        return priceDetail;
    }

    protected static MeetingRoomPrice toMeetingRoomPriceFromPace(MeetingPackagePaceBarDecision decision,
                                                         BaseMeetingRoomDTO baseMeetingRoom,
                                                         List<MeetingPackageBarDecisionOverride> overrides,
                                                         Map<ProductOccupancyKey, MeetingPackageProductRateOffsetOverride> productRateOffsetOverrideMap) {
        FunctionSpaceFunctionRoom functionSpaceFunctionRoom = decision.getFunctionSpaceFunctionRoom();
        MeetingRoomPrice meetingRoom = new MeetingRoomPrice();
        meetingRoom.setMeetingRoomId(functionSpaceFunctionRoom.getId());
        meetingRoom.setMeetingRoomName(functionSpaceFunctionRoom.getName());
        meetingRoom.setMeetingRoomPrice(decision.getFinalBar());
        meetingRoom.setPreviousBar(decision.getPreviousBar());
        meetingRoom.setSpecificOverride(decision.getUserSpecifiedRate());
        meetingRoom.setFloorOverride(decision.getFloorRate());
        meetingRoom.setCeilingOverride(decision.getCeilRate());
        meetingRoom.setPriceHigh(true);
        meetingRoom.setTotalPackagePrice(decision.getTotalPackagePrice());
        meetingRoom.setBaseMeetingRoom(isBaseMeetingRoom(functionSpaceFunctionRoom, baseMeetingRoom));
        Optional<MeetingPackageBarDecisionOverride> override = getOverrideForPaceDecision(decision, overrides);
        override.ifPresent(mpOverride -> meetingRoom.setOldBar(mpOverride.getOldBar()));
        if ("LINKED".equalsIgnoreCase(decision.getMeetingPackageProduct().getType()) && isBaseMeetingRoom(functionSpaceFunctionRoom, baseMeetingRoom)) {
            ProductOccupancyKey key = new ProductOccupancyKey(
                    decision.getMeetingPackageProduct().getId(),
                    decision.getOccupancyDate()
            );
            MeetingPackageProductRateOffsetOverride offsetOverride = productRateOffsetOverrideMap.get(key);
            if (Objects.nonNull(offsetOverride)) {
                meetingRoom.setAdjustmentValue(offsetOverride.getOffsetValue());
            }
        }
        return meetingRoom;
    }

    private static Optional<MeetingPackageBarDecisionOverride> getOverrideForPaceDecision(
            MeetingPackagePaceBarDecision decision, List<MeetingPackageBarDecisionOverride> overrides) {

        return overrides.stream()
                .filter(override ->
                        decision.getOccupancyDate().equals(override.getOccupancyDate()) &&
                                decision.getMeetingPackageProduct().getId().equals(override.getMeetingPackageProduct().getId()) &&
                                decision.getFunctionSpaceFunctionRoom().getId().equals(override.getFunctionSpaceFunctionRoom().getId()))
                .findFirst();
    }
}
