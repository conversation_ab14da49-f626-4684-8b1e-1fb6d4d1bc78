package com.ideas.tetris.pacman.services.rules;

import com.ideas.infra.tetris.security.domain.LDAPUser;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ClientAttributeValue;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.PropertyGroup;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.PropertyGroupOneTimeCleanUpDto;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.PropertyPropertyGroup;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.customattributeservice.entity.CustomAttributeSearchCriteria;
import com.ideas.tetris.pacman.services.customattributeservice.entity.SearchJoinCondition;
import com.ideas.tetris.pacman.services.customattributeservice.entity.SearchOperator;
import com.ideas.tetris.pacman.services.customattributeservice.service.CustomAttributeSearchBean;
import com.ideas.tetris.pacman.services.security.AsyncUserService;
import com.ideas.tetris.pacman.services.security.AuthorizationService;
import com.ideas.tetris.pacman.services.security.UserAuthorizedPropertyCache;
import com.ideas.tetris.pacman.util.Pagination;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.TransactionTimestampHelper;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.daoandentities.entity.AuthorizationGroup;
import com.ideas.tetris.platform.services.daoandentities.entity.AuthorizationGroupPropertyMapping;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import com.ideas.tetris.platform.services.globalproperty.service.ClientPropertyCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static org.apache.commons.collections.CollectionUtils.isEmpty;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;

@Component
@Transactional
public class RulesService {
    @GlobalCrudServiceBean.Qualifier
    @Autowired
    @Qualifier("globalCrudServiceBean")
    private CrudService globalCrudService;
    @Autowired
    private CustomAttributeSearchBean searcher;
    @Autowired
    private UserAuthorizedPropertyCache userAuthorizedPropertyCache;
    @Autowired
    protected ClientPropertyCacheService clientPropertyCacheService;
    @Autowired
    private AsyncUserService asyncUserService;
    @Autowired
    private AuthorizationService authorizationService;

    // Several test classes - not moving all to Mockito just yet
    public void setGlobalCrudService(CrudService globalCrudService) {
        this.globalCrudService = globalCrudService;
    }

    public void setSearcher(CustomAttributeSearchBean searcher) {
        this.searcher = searcher;
    }

    public boolean deleteRule(Integer ruleId) {
        globalCrudService.delete(Rule.class, ruleId);
        return true;
    }

    public Rule getRule(Integer ruleId) {
        return globalCrudService.find(Rule.class, ruleId);
    }

    public List<CustomAttributeSearchCriteria> getSearchCriteria(Integer ruleId) {
        Rule rule = globalCrudService.find(Rule.class, ruleId);
        if (rule == null) {
            return Collections.emptyList();
        }

        List<CustomAttributeSearchCriteria> result = new ArrayList<CustomAttributeSearchCriteria>();

        for (RuleAttributeValueMapping mapping : rule.getRuleAttributeValueMappings()) {
            CustomAttributeSearchCriteria clause = new CustomAttributeSearchCriteria();
            clause.setClientAttributeValueId(mapping.getClientAttributeValue().getId());
            clause.setOperator(SearchOperator.withId(mapping.getConditionType().getId()));
            if (mapping.getConjunctionType() != null
                    && mapping.getConjunctionType().getId().intValue() != SearchJoinCondition.NONE.getId()) {
                clause.setJoinCondition(SearchJoinCondition.withId(mapping.getConjunctionType().getId()));
            }
            result.add(clause);
        }
        return result;
    }

    public Integer saveSearchCriteria(List<CustomAttributeSearchCriteria> searchCriteria) {
        Rule rule = new Rule();
        rule.setClientId(PacmanThreadLocalContextHolder.getWorkContext().getClientId());
        rule.setRuleAttributeValueMappings(new LinkedHashSet<RuleAttributeValueMapping>());

        // TODO: next two lines to disappear when Rule extends BaseEntity
        rule.setCreateDate(TransactionTimestampHelper.getCurrentTransactionTimestamp());

        WorkContextType wc = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
        Integer userId = Integer.valueOf(wc.getUserId());
        rule.setCreatedByUserId(userId);

        convertSearchCriteriaToRuleAttributeValueMappings(searchCriteria, rule);
        return globalCrudService.save(rule).getId();
    }

    public boolean updateSearchCriteria(Integer ruleId, List<CustomAttributeSearchCriteria> searchCriteria) {
        Rule rule = globalCrudService.find(Rule.class, ruleId);
        if (rule == null) {
            return false;
        }

        for (RuleAttributeValueMapping mapping : rule.getRuleAttributeValueMappings()) {
            globalCrudService.delete(RuleAttributeValueMapping.class, mapping.getId());
        }
        rule.getRuleAttributeValueMappings().clear();
        convertSearchCriteriaToRuleAttributeValueMappings(searchCriteria, rule);

        globalCrudService.save(rule);
        return true;
    }

    private void convertSearchCriteriaToRuleAttributeValueMappings(List<CustomAttributeSearchCriteria> searchCriteria,
                                                                   Rule rule) {
        int i = 0;
        for (CustomAttributeSearchCriteria clause : searchCriteria) {
            RuleAttributeValueMapping mapping = new RuleAttributeValueMapping();

            mapping.setClientAttributeValue(globalCrudService.find(ClientAttributeValue.class,
                    clause.getClientAttributeValueId()));
            mapping.setConditionType(globalCrudService.find(ConditionType.class, clause.getOperator().getId()));

            if (clause.getJoinCondition() != null) {
                mapping.setConjunctionType(globalCrudService.find(ConjunctionType.class, clause.getJoinCondition()
                        .getId()));
            } else {
                mapping.setConjunctionType(globalCrudService.find(ConjunctionType.class,
                        SearchJoinCondition.NONE.getId()));
            }

            mapping.setRanking(i++);

            // TODO: next line to disappear when we can extend BaseEntity
            mapping.setCreateDate(LocalDateTime.now());

            mapping.setRule(rule);
            rule.getRuleAttributeValueMappings().add(mapping);
        }
    }

    public List<Integer> recomputeAllRuleBasedGroups() {
        List<Integer> authGroupIDsForPropertyGroupUpdate = new ArrayList<Integer>();
        for (AuthorizationGroup authGroup : getAllRuleBasedAuthGroups()) {
            List<AuthorizationGroupPropertyMapping> authorizationGroupPropertyMappingRemovalList = updatePropertySetFromAuthGroup(authGroup);
            if (null != authorizationGroupPropertyMappingRemovalList
                    && isNotEmpty(authorizationGroupPropertyMappingRemovalList)) {
                authGroupIDsForPropertyGroupUpdate.add(authGroup.getId());
            }
        }

        for (PropertyGroup propertyGroup : getAllRuleBasedPropertyGroups()) {
            updatePropertySet(propertyGroup);
        }

        return authGroupIDsForPropertyGroupUpdate;
    }

    public void recomputeRuleBasedGroupsForNewProperty(Integer propertyId) {
        List<Integer> propertyIds = null;
        // Can we actually disable individual props within group and not just the group?
        for (AuthorizationGroup authGroup : getAllRuleBasedAuthGroups()) {
            propertyIds = searcher.searchForPropertiesByCustomAttributeValue(getSearchCriteria(authGroup.getRuleId()));
            if (propertyIds.contains(propertyId) && !propertyIsInAuthGroup(propertyId, authGroup)) {
                // new property matches, add mapping
                AuthorizationGroupPropertyMapping authMapping = new AuthorizationGroupPropertyMapping();
                authMapping.setAuthorizationGroup(authGroup);
                authMapping.setPropertyId(propertyId);
                authMapping.setStatusId(Status.ACTIVE.getId());
                globalCrudService.save(authMapping);
                globalCrudService.getEntityManager().flush();
            }
        }

        for (PropertyGroup propertyGroup : getAllRuleBasedPropertyGroups()) {
            propertyIds = searcher.searchForPropertiesByCustomAttributeValue(getSearchCriteria(propertyGroup
                    .getRuleId()));
            if (propertyIds.contains(propertyId) && !propertyIsInPropertyGroup(propertyId, propertyGroup)) {
                // new property matches, add mapping
                PropertyPropertyGroup propertyMapping = new PropertyPropertyGroup();
                propertyMapping.setPropertyGroup(propertyGroup);
                propertyMapping.setProperty(globalCrudService.find(Property.class, propertyId));
                propertyMapping.setStatus(Status.ACTIVE);
                globalCrudService.save(propertyMapping);
            }
        }
    }

    @SuppressWarnings("unchecked")
    private boolean propertyIsInPropertyGroup(Integer propertyId, PropertyGroup propertyGroup) {
        List<PropertyPropertyGroup> mappings = globalCrudService.findByNamedQuery(
                PropertyPropertyGroup.BY_PROPERTY_AND_PROPERTY_GROUP,
                QueryParameter.with(PropertyPropertyGroup.PARAM_PROPERTY_ID, propertyId)
                        .and(PropertyPropertyGroup.PARAM_PROPERTY_GROUP_ID, propertyGroup.getId()).parameters());
        return mappings != null && !mappings.isEmpty();
    }

    @SuppressWarnings("unchecked")
    private boolean propertyIsInAuthGroup(Integer propertyId, AuthorizationGroup authGroup) {
        List<AuthorizationGroupPropertyMapping> mappings = globalCrudService
                .findByNamedQuery(
                        AuthorizationGroupPropertyMapping.BY_PROPERTY_AND_AUTH_GROUP,
                        QueryParameter.with(AuthorizationGroupPropertyMapping.PARAM_PROPERTY_ID, propertyId)
                                .and(AuthorizationGroupPropertyMapping.PARAM_AUTH_GROUP_ID, authGroup.getId())
                                .parameters());
        return mappings != null && !mappings.isEmpty();
    }

    @SuppressWarnings("unchecked")
    private List<AuthorizationGroup> getAllRuleBasedAuthGroups() {
        return globalCrudService.findByNamedQuery(
                AuthorizationGroup.RULE_BASED,
                QueryParameter.with(AuthorizationGroup.PARAM_CLIENT_ID,
                        PacmanThreadLocalContextHolder.getWorkContext().getClientId()).parameters());
    }

    @SuppressWarnings("unchecked")
    private List<PropertyGroup> getAllRuleBasedPropertyGroups() {
        return globalCrudService.findByNamedQuery(
                PropertyGroup.RULE_BASED,
                QueryParameter.with(PropertyGroup.PARAM_CLIENT_ID,
                        PacmanThreadLocalContextHolder.getWorkContext().getClientId()).parameters());
    }

    public void updatePropertySet(AuthorizationGroup authGroup) {
        updatePropertySetFromAuthGroup(authGroup);
    }

    // Helper to handle bulk delete from PropertyPropertyGroup
    public void removePropertiesMappingsWithPropertyGroupByPagination(List<Integer> propertyIds, Integer propertyGroupId) {
        int totalNoOfRecords = propertyIds.size();
        int pageNo = 0;
        while (totalNoOfRecords > 0) {
            pageNo++;
            removePropertiesMappingWithPropertyGroupPageWise(pageNo, propertyIds, propertyGroupId);
            totalNoOfRecords = totalNoOfRecords - Constants.PAGE_SIZE;
        }
    }

    @SuppressWarnings("unchecked")
    private void removePropertiesMappingWithPropertyGroupPageWise(int pageNo, List<Integer> propertyIds,
                                                                  Integer propertyGroupId) {
        List<Integer> propertyIdsBatch = Pagination.paginateSublistForPageNoByPageSize(pageNo, propertyIds,
                Constants.PAGE_SIZE);
        removePropertiesMappingWithPropertyGroup(propertyIdsBatch, propertyGroupId);
    }

    private void removePropertiesMappingWithPropertyGroup(List<Integer> propertyIds, Integer propertyGroupId) {
        globalCrudService.executeUpdateByNamedQuery(
                PropertyPropertyGroup.DELETE_PROPERTY_MAPPINGS_FROM_GROUP,
                QueryParameter.with(PropertyPropertyGroup.PARAM_PROPERTY_GROUP_ID, propertyGroupId)
                        .and(PropertyPropertyGroup.PARAM_PROPERTY_LIST_ID, propertyIds).parameters());
    }

    // Helper to handle bulk delete from AuthorizationGroupPropertyMapping
    public void removeMappingsFromAuthorizationGroupPropertyMapping(List<Integer> propertyIds, Integer authGroupId) {
        Query query = globalCrudService.getEntityManager().createNativeQuery(
                "delete from Auth_Group_Property where Auth_Group_Id = :authGroupId and Property_Id in (:propertyIds)");
        query.setParameter("authGroupId", authGroupId);
        query.setParameter("propertyIds", propertyIds);
        query.executeUpdate();
    }

    public void updatePropertySet(PropertyGroup propertyGroup) {
        List<Integer> propertyIds = searcher.searchForPropertiesByCustomAttributeValue(getSearchCriteria(propertyGroup
                .getRuleId()));
        List<Integer> removedMappingsPropertyIds = new ArrayList<Integer>();
        List<Integer> currentlyMappedPropertyIdsToPG = new ArrayList<Integer>();
        // Remove all existing property mappings that are no longer in the search set.

        if (null != propertyGroup.getPropertyPropertyGroups()) {
            Iterator<PropertyPropertyGroup> iter = propertyGroup.getPropertyPropertyGroups().iterator();
            while (iter.hasNext()) {
                PropertyPropertyGroup mapping = iter.next();
                if (!propertyIds.contains(mapping.getProperty().getId())) {
                    iter.remove();
                    removedMappingsPropertyIds.add(mapping.getProperty().getId());
                } else {
                    currentlyMappedPropertyIdsToPG.add(mapping.getProperty().getId());
                }
            }
        }
        // Perform Bulk delete for Mappings from PropertyPropertyGroup
        if (isNotEmpty(removedMappingsPropertyIds)) {
            removePropertiesMappingsWithPropertyGroupByPagination(removedMappingsPropertyIds, propertyGroup.getId());
        }

        // Create new mapping between this auth group and each property matching the search criteria

        List<Property> authorizedPropertyListOfTheUser = authorizationService.retrieveAuthorizedProperties(
                propertyGroup.getUserId(), PacmanThreadLocalContextHolder.getWorkContext().getClientId());

        List<Integer> authorizedPropertyIntegerListOfTheUser = authorizedPropertyListOfTheUser.stream()
                .map(Property::getId).collect(Collectors.toList());

        Map<Integer, Property> propertyMap = new HashMap<>();
        propertyIds.stream()
                .filter(propertyId -> !currentlyMappedPropertyIdsToPG.contains(propertyId))
                .filter(authorizedPropertyIntegerListOfTheUser::contains)
                .forEach(propertyId -> addPropertyToPropertyPropertyGroup(propertyGroup, propertyMap, propertyId));
    }

    public void updatePropertySetForAuthGroupManagement(PropertyGroup propertyGroup, List<Integer> authorizedPropertyIntegerListOfTheUser) {
        List<Integer> propertyIds = searcher.searchForPropertiesByCustomAttributeValue(getSearchCriteria(propertyGroup
                .getRuleId()));
        List<Integer> currentlyMappedPropertyIdsToPG = new ArrayList<>();

        if (null != propertyGroup.getPropertyPropertyGroups()) {
            Iterator<PropertyPropertyGroup> iter = propertyGroup.getPropertyPropertyGroups().iterator();
            while (iter.hasNext()) {
                PropertyPropertyGroup mapping = iter.next();
                if (!propertyIds.contains(mapping.getProperty().getId())) {
                    iter.remove();
                } else {
                    currentlyMappedPropertyIdsToPG.add(mapping.getProperty().getId());
                }
            }
        }
        Map<Integer, Property> propertyMap = new HashMap<>();
        propertyIds.stream()
                .filter(propertyId -> !currentlyMappedPropertyIdsToPG.contains(propertyId))
                .filter(authorizedPropertyIntegerListOfTheUser::contains)
                .forEach(propertyId -> addPropertyToPropertyPropertyGroup(propertyGroup, propertyMap, propertyId));
    }

    private void addPropertyToPropertyPropertyGroup(PropertyGroup propertyGroup, Map<Integer, Property> propertyMap, Integer propertyId) {
        PropertyPropertyGroup mapping = new PropertyPropertyGroup();
        mapping.setPropertyGroup(propertyGroup);
        mapping.setProperty(propertyMap.computeIfAbsent(propertyId, clientPropertyCacheService::getProperty));
        mapping.setStatus(Status.ACTIVE);
        globalCrudService.save(mapping);
        if (null == propertyGroup.getPropertyPropertyGroups()) {
            propertyGroup.setPropertyPropertyGroups(new HashSet<>());
        }
        propertyGroup.getPropertyPropertyGroups().add(mapping);
    }

    public void identifyPropertyPGMappingsEligibleForAddition(PropertyGroup propertyGroup,
                                                              List<PropertyGroupOneTimeCleanUpDto> propertyGroupDtoList) {
        List<Integer> propertyIds = searcher.searchForPropertiesByCustomAttributeValue(getSearchCriteria(propertyGroup
                .getRuleId()));
        List<Integer> removedMappingsPropertyIds = new ArrayList<Integer>();
        List<Integer> currentlyMappedPropertyIdsToPG = new ArrayList<Integer>();
        // Remove all existing property mappings that are no longer in the search set.
        List<PropertyPropertyGroup> removals = new ArrayList<PropertyPropertyGroup>();
        for (PropertyPropertyGroup mapping : propertyGroup.getPropertyPropertyGroups()) {
            if (!propertyIds.contains(mapping.getProperty().getId())) {
                removedMappingsPropertyIds.add(mapping.getProperty().getId());
                removals.add(mapping);
            }
            currentlyMappedPropertyIdsToPG.add(mapping.getProperty().getId());
        }
        // Perform Bulk delete for Mappings from PropertyPropertyGroup
        if (null != removedMappingsPropertyIds && isNotEmpty(removedMappingsPropertyIds)) {
            removePropertiesMappingsWithPropertyGroupByPagination(removedMappingsPropertyIds, propertyGroup.getId());
        }

        propertyGroup.getPropertyPropertyGroups().removeAll(removals);
        currentlyMappedPropertyIdsToPG.removeAll(removedMappingsPropertyIds);

        Map<Integer, Property> propertyMap = new HashMap<Integer, Property>();
        // Create a new mapping between this auth group and each property matching the search criteria.

        List<Integer> authorizedPropertyIntegerListOfTheUser = null;

        PropertyGroupOneTimeCleanUpDto propertyGroupDto = new PropertyGroupOneTimeCleanUpDto();
        propertyGroupDto.setPropertiesHavingAccessTo(authorizedPropertyIntegerListOfTheUser);
        propertyGroupDto.setClientId(propertyGroup.getClientId());
        propertyGroupDto.setUserId(propertyGroup.getUserId());
        propertyGroupDto.setId(propertyGroup.getId());
        propertyGroupDto.setName(propertyGroup.getName());
        propertyGroupDto.setRuleId(propertyGroup.getRuleId());

        for (Integer propertyId : propertyIds) {
            if (!currentlyMappedPropertyIdsToPG.contains(propertyId)) {
                if (null == authorizedPropertyIntegerListOfTheUser) {
                    authorizedPropertyIntegerListOfTheUser = new ArrayList<Integer>();
                    List<Property> authorizedPropertyListOfTheUser = authorizationService.retrieveAuthorizedProperties(
                            propertyGroup.getUserId(), PacmanThreadLocalContextHolder.getWorkContext().getClientId());
                    if (null != authorizedPropertyListOfTheUser) {
                        for (Property property : authorizedPropertyListOfTheUser) {
                            authorizedPropertyIntegerListOfTheUser.add(property.getId());
                        }
                    }
                }
                if (authorizedPropertyIntegerListOfTheUser.contains(propertyId)) {
                    PropertyPropertyGroup mapping = new PropertyPropertyGroup();
                    mapping.setPropertyGroup(propertyGroup);
                    Property objProperty = propertyMap.get(propertyId);
                    if (null != objProperty) {
                        mapping.setProperty(objProperty);
                    } else {
                        objProperty = globalCrudService.find(Property.class, propertyId);
                        mapping.setProperty(objProperty);
                        propertyMap.put(propertyId, objProperty);
                    }

                    mapping.setStatus(Status.ACTIVE);
                    propertyGroupDto.getPropertyIdsFromMappingsMarkedForAddition().add(propertyId);
                    propertyGroup.getPropertyPropertyGroups().add(mapping);
                }
            }
        }
        propertyGroupDtoList.add(propertyGroupDto);
    }

    public List<AuthorizationGroupPropertyMapping> updatePropertySetFromAuthGroup(AuthorizationGroup authGroup) {
        List<Integer> propertyIds = searcher.searchForPropertiesByCustomAttributeValue(getSearchCriteria(authGroup
                .getRuleId()));
        List<Integer> removedMappingsPropertyIds = new ArrayList<Integer>();
        List<Integer> currentlyMappedPropertyIdsToPG = new ArrayList<Integer>();

        // Remove all existing property mappings that are no longer in the search set
        List<AuthorizationGroupPropertyMapping> removals = new ArrayList<AuthorizationGroupPropertyMapping>();
        for (AuthorizationGroupPropertyMapping mapping : authGroup.getAuthGroupPropertyMappings()) {
            if (!propertyIds.contains(mapping.getPropertyId())) {
                removedMappingsPropertyIds.add(mapping.getPropertyId());
                removals.add(mapping);
                updatePropertyToCacheForAllUsersWhoBelongsToAuthGroup(authGroup, mapping.getPropertyId());
            }
            currentlyMappedPropertyIdsToPG.add(mapping.getPropertyId());
        }

        authGroup.getAuthGroupPropertyMappings().removeAll(removals);
        currentlyMappedPropertyIdsToPG.removeAll(removedMappingsPropertyIds);
        // Create new mapping between this auth group & each property matching the search criteria
        for (Integer propertyId : propertyIds) {
            if (!currentlyMappedPropertyIdsToPG.contains(propertyId)) {
                AuthorizationGroupPropertyMapping mapping = new AuthorizationGroupPropertyMapping();
                mapping.setAuthorizationGroup(authGroup);
                mapping.setPropertyId(propertyId);
                mapping.setStatusId(Constants.ACTIVE_STATUS_ID);
                globalCrudService.save(mapping);
                authGroup.getAuthGroupPropertyMappings().add(mapping);
                // add this property to cache also
                updatePropertyToCacheForAllUsersWhoBelongsToAuthGroup(authGroup, propertyId);
            }
        }
        return removals;
    }

    private void updatePropertyToCacheForAllUsersWhoBelongsToAuthGroup(AuthorizationGroup authGroup, Integer propertyId) {
        Set<LDAPUser> users = authorizationService.getUserForAuthGroup(authGroup.getId(),
                PacmanWorkContextHelper.getClientCode());
        for (LDAPUser ldapUser : users) {
            List<Integer> existingAuthPropertyIds = userAuthorizedPropertyCache.get(
                    PacmanWorkContextHelper.getClientId(), ldapUser.getUserId());
            if (isNotEmpty(existingAuthPropertyIds)) {
                if (!existingAuthPropertyIds.contains(propertyId)) {
                    existingAuthPropertyIds.add(propertyId);
                } else {
                    existingAuthPropertyIds.remove(propertyId);
                }
                asyncUserService.clearUnathourizedDefaultPropertyFromLdap(ldapUser.getUserId(), existingAuthPropertyIds,
                        PacmanWorkContextHelper.getWorkContext(), PacmanThreadLocalContextHolder.getPrincipal());
            }
            if (isEmpty(existingAuthPropertyIds)) {
                userAuthorizedPropertyCache.remove(PacmanWorkContextHelper.getClientId(), ldapUser.getUserId());
            } else {
                userAuthorizedPropertyCache.put(PacmanWorkContextHelper.getClientId(), ldapUser.getUserId(),
                        existingAuthPropertyIds);
            }
        }
    }
}
