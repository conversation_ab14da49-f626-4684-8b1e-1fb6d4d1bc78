package com.ideas.tetris.pacman.services.bestavailablerate;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClassPriceRank;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomTypeVendorMapping;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.accommodation.service.RoomTypeVendorMappingService;
import com.ideas.tetris.pacman.services.agilerates.configuration.dto.AgileRatesProductTypeEnum;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesOffsetMethod;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductAccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.ComponentRoomData;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomTypeSupplementValue;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfiguration;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionDailybarOutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionDailybarOutputKey;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionDailybarOutputNonHiltonCRS;
import com.ideas.tetris.pacman.services.componentrooms.entity.CRAccomTypeMapping;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.hospitalityrooms.entity.HospitalityRoomsConfigUI;
import com.ideas.tetris.pacman.services.hospitalityrooms.service.HospitalityRoomsService;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.Supplement;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.TableBatch;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystem;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystemHelper;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.joda.time.LocalDate;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.NON_HILTON_CRS_SEND_PRICE_ADJUSTMENT_ENABLED;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.transaction.annotation.Transactional;

@Component
@Transactional
public abstract class CPBarDecisionService {

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService tenantCrudService;

    @Autowired
	protected DecisionService decisionService;

    @Autowired
	protected DateService dateService;

    @Autowired
	protected HospitalityRoomsService hospitalityRoomsService;

    @Autowired
	protected AccommodationService accommodationService;

    @Autowired
	protected AgileRatesConfigurationService agileRatesConfigurationService;

    @Autowired
	protected PricingConfigurationService pricingConfigurationService;

    @Autowired
	protected CPManagementService cpManagementService;

    @Autowired
	protected PacmanConfigParamsService pacmanConfigParamsService;

    @Autowired
	protected ExternalSystemHelper externalSystemHelper;
    @Autowired
	protected RoomTypeVendorMappingService roomTypeVendorMappingService;

    public static final String PROPERTY_ID = "propertyId";
    public static final String DECISION_ID = "decisionId";
    public static final String START_DATE = "startDate";
    public static final String END_DATE = "endDate";
    public static final String BUSINESS_DATE = "businessDate";
    private static final int OVERRIDE_PRECISION = 2;
    public static final int HUNDRED_PERCENT_ADJUSTMENT = -100;

    protected abstract TableBatch buildTableBatch();

    protected abstract void handleCPBarDecisionChange(Integer decisionId, RateUnqualified defaultRateUnqualified, CPDecisionContext cpDecisionContext, TableBatch tableBatch, TableBatch tableBatchNonHiltonCRS, CPBarDecisionChange cpBarDecisionChange, List<CPDecisionBAROutput> productOutputsForComparison, List<DecisionDailybarOutput> barDecisionDailybarOutputs,
                                                      Map<DecisionDailybarOutputKey, DecisionDailybarOutput> previousDecisionDailybarOutputs, Product primaryProduct);

    protected abstract void setAgilePrettyBarToNullToRecalculateTheRates(Product product, List<CPDecisionBAROutput> productOutputs, Set<AccomType> crAccomTypes);

    public void handleDecisions(LocalDate startDate, LocalDate endDate) {
        handleDecisions(startDate, endDate, null, null);
    }

    public void handleDecisions(LocalDate startDate, LocalDate endDate, Integer decisionId, RateUnqualified defaultRateUnqualified) {
        // Get the CPDecisionContext to help perform the rate calculations
        boolean componentRoomsPriceAsSumOfPartEnabled = pricingConfigurationService.isComponentRoomsPriceAsSumOfPartEnabled();
        CPDecisionContext cpDecisionContext = pricingConfigurationService.getCPDecisionContext(startDate, endDate);
        ComponentRoomData componentRoomData = null;
        if (componentRoomsPriceAsSumOfPartEnabled) {
            componentRoomData = getComponentRoomData(cpDecisionContext);
        }
        // Find the CPDecisionBAROutputs for the start/end dates
        TreeMap<LocalDate, Map<Product, List<CPDecisionBAROutput>>> cpDecisionBarOutputs = findCPDecisionBAROutputs(startDate, endDate);
        List<CPBarDecisionChange> componentRoomRelatedDecisionChanges = processDecisions(startDate, endDate, decisionId, defaultRateUnqualified, cpDecisionContext, cpDecisionBarOutputs, false, componentRoomData, new ArrayList<>());

        if (componentRoomsPriceAsSumOfPartEnabled && null != componentRoomData) {
            Map<AccomType, Map<AccomType, Integer>> crAndCPMappingWithQuantities = componentRoomData.getCRAndCPMappingWithQuantities();
            componentRoomRelatedDecisionChanges.stream()
                    .filter(cPBarDecisionChange -> !cPBarDecisionChange.isUpdateRequired())
                    .forEach(CPBarDecisionChange::resetToPreviousDailyBarRates);
            if (MapUtils.isNotEmpty(crAndCPMappingWithQuantities)) {
                TreeMap<LocalDate, Map<Product, List<CPDecisionBAROutput>>> cpDecisionBAROutputsForRoomTypes = findCPDecisionBAROutputsForRoomTypes(startDate, endDate, crAndCPMappingWithQuantities.keySet());
                processDecisions(startDate, endDate, decisionId, defaultRateUnqualified, cpDecisionContext, cpDecisionBAROutputsForRoomTypes, true, componentRoomData, componentRoomRelatedDecisionChanges);
            }
        }
    }

    private ComponentRoomData getComponentRoomData(CPDecisionContext cpDecisionContext) {
        ComponentRoomData componentRoomData = null;
        List<PricingAccomClass> sumOfPriceEnabledRoomClasses = cpDecisionContext.getPricingAccomClasses().values()
                .stream()
                .filter(PricingAccomClass::isPriceAsSumOfParts)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(sumOfPriceEnabledRoomClasses)) {
            List<AccomType> sumOfPriceEnabledRoomTypes = sumOfPriceEnabledRoomClasses.stream()
                    .flatMap(pac -> pac.getAccomClass().getAccomTypes().stream())
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(sumOfPriceEnabledRoomTypes)) {
                List<CRAccomTypeMapping> allComponentRoomTypeMappings = tenantCrudService.findAll(CRAccomTypeMapping.class);
                List<HospitalityRoomsConfigUI> hrMappings = hospitalityRoomsService.getHospitalityRoomsConfigUIWithoutPseudoRooms();
                List<AccomType> allAccomTypes = accommodationService.getAllAccomTypes();
                componentRoomData = new ComponentRoomData(allComponentRoomTypeMappings, hrMappings, sumOfPriceEnabledRoomTypes, allAccomTypes);
            }
        }
        return componentRoomData;
    }

    private List<CPBarDecisionChange> processDecisions(LocalDate startDate, LocalDate endDate, Integer decisionId, RateUnqualified defaultRateUnqualified, CPDecisionContext cpDecisionContext, TreeMap<LocalDate, Map<Product, List<CPDecisionBAROutput>>> cpDecisionBarOutputs, boolean componentRoomSumOfPriceFlow, ComponentRoomData componentRoomData, List<CPBarDecisionChange> componentRoomsRelatedDecisionChanges) {
        boolean isComponentRoomsPriceAsSumOfPartEnabled = pricingConfigurationService.isComponentRoomsPriceAsSumOfPartEnabled();

        List<CPBarDecisionChange> componentRoomRelatedBarDecisionChanges = new ArrayList<>();
        // Get the AccomClassPriceRanks
        List<AccomClassPriceRank> accomClassPriceRanks = tenantCrudService.findByNamedQuery(AccomClassPriceRank.GET_PRICE_RANKS_WITH_ACCOM_CLASS);

        // Get a Map of DecisionDailybarOutput records for the date range
        Map<DecisionDailybarOutputKey, DecisionDailybarOutput> decisionDailybarOutputs = findUniqueDecisionDailybarOutputs(startDate, endDate);

        Map<DecisionDailybarOutputKey, DecisionDailybarOutputNonHiltonCRS> nonHiltonCRSDecisionDailybarOutputs =
                isNonHiltonCrsSendPriceAdjustmentEnabled() ?
                        findUniqueNonHiltonCRSDecisionDailybarOutputs(startDate, endDate)
                        : new HashMap<>();

        // Gets the list of Products in hierarchical order
        final Map<Product, List<Product>> products = cpDecisionContext.getProductsInHierarchicalOrder();

        List<ProductAccomType> allProductAccomTypes = new ArrayList<>();
        if (SystemConfig.isIndependentProductPriceRankValidationEnabled() && cpDecisionContext.hasIndependentProductsConfigured()) {
            allProductAccomTypes = agileRatesConfigurationService.findAllProductAccomType();
        }

        // Get the tableVariableBatcher if it's configured to use it
        TableBatch tableBatch = buildTableBatch();
        TableBatch tableBatchNonHiltonCRS = buildTableBatchNonHiltonCRS();

        //get the CR AccomType with mapped CP accomTypes and quantities
        final Map<AccomType, Map<AccomType, Integer>> crAndCPMappingWithQuantities = getCRAndCPMappingWithQuantities(componentRoomSumOfPriceFlow, componentRoomData);
        // For each date, loop thru the CPDecisionBAROutputs
        for (Map.Entry<LocalDate, Map<Product, List<CPDecisionBAROutput>>> outputsByDateAndProduct : cpDecisionBarOutputs.entrySet()) {

            // Get the outputs for the day by product
            Map<Product, List<CPDecisionBAROutput>> outputsByProduct = outputsByDateAndProduct.getValue();

            // Keep track of the changes between products
            Map<CPBarDecisionChangeKey, CPBarDecisionChange> allProductsDecisionChanges = new HashMap<>();

            // Store newly created BAR Decision Daily Bar Outputs
            List<DecisionDailybarOutput> barDecisionDailybarOutputs = new ArrayList<>();

            List<ProductAccomType> finalAllProductAccomTypes = allProductAccomTypes;
            products.forEach((primaryProduct, productsList) -> {
                List<CPDecisionBAROutput> primaryProductOutput = outputsByProduct.get(primaryProduct);

                productsList.forEach(product -> {
                    // Get the outputs for the product
                    List<CPDecisionBAROutput> productOutputs = outputsByProduct.get(product);

                    // If no outputs exist for the Product - return
                    if (CollectionUtils.isEmpty(productOutputs)) {
                        return;
                    }

                    Map<AccomType, List<CPBarDecisionChange>> relatedBarOutputsForCR = new HashMap<>();
                    if (componentRoomSumOfPriceFlow && product.isSystemDefaultOrIndependentProduct()) {
                        relatedBarOutputsForCR = getCPDecisionsBarChangesRelatedToComponentRooms(crAndCPMappingWithQuantities, componentRoomsRelatedDecisionChanges, outputsByDateAndProduct.getKey());
                    }

                    if (componentRoomSumOfPriceFlow) {
                        setAgilePrettyBarToNullToRecalculateTheRates(product, productOutputs, crAndCPMappingWithQuantities.keySet());
                    }

                    // Sort the list to process BaseRT's first
                    productOutputs.sort((d1, d2) -> Boolean.compare(cpDecisionContext.isBaseRoomType(d1), cpDecisionContext.isBaseRoomType(d2)) * -1);

                    // Handle the CPDecisionBAROutputs for each date
                    List<CPBarDecisionChange> cpBarDecisionChanges = handleProductDecisionsForDate(cpDecisionContext, decisionDailybarOutputs, nonHiltonCRSDecisionDailybarOutputs, allProductsDecisionChanges, productOutputs, componentRoomSumOfPriceFlow, relatedBarOutputsForCR);
                    cpDecisionContext.setAllProductsDecisionChanges(allProductsDecisionChanges);
                    // If the Product is the system default or independent product - validate it against the accom class price ranking
                    if (product.isSystemDefault() || (product.isIndependentProduct() && !SystemConfig.isIndependentProductPriceRankValidationEnabled())) {
                        validateAccomClassPriceRank(accomClassPriceRanks, cpBarDecisionChanges);
                    } else if (product.isIndependentProduct() && SystemConfig.isIndependentProductPriceRankValidationEnabled()) {
                        // If the Product is an Independent product - validate accom class price ranking for selected accom classes
                        List<AccomType> productAccomTypes = finalAllProductAccomTypes.stream()
                                .filter(productAccomType -> productAccomType.getProduct().getId().equals(product.getId()))
                                .collect(Collectors.toList())
                                .stream().map(ProductAccomType::getAccomType)
                                .collect(Collectors.toList());
                        validateAccomClassPriceRankForIndependentProduct(accomClassPriceRanks, cpBarDecisionChanges, productAccomTypes);
                    } else {
                        checkNonDefaultProductRequiresUpdate(cpDecisionContext, cpBarDecisionChanges);
                    }

                    boolean sendNonBarProductOutputs = sendNonPrimaryProductOutputs(product, primaryProduct);
                    List<CPDecisionBAROutput> productOutputsForComparison = sendNonBarProductOutputs ? getParentProductOutputs(product, productsList, outputsByProduct) : primaryProductOutput;

                    // For each of the cpBarDecisionChanges, we need to call the Call the decision handler - it is responsible for the final states of the objects and saving the outputs
                    cpBarDecisionChanges.forEach(cpBarDecisionChange -> handleCPBarDecisionChange(decisionId, defaultRateUnqualified, cpDecisionContext, tableBatch, tableBatchNonHiltonCRS, cpBarDecisionChange, productOutputsForComparison, barDecisionDailybarOutputs
                            , decisionDailybarOutputs, primaryProduct));

                    boolean shouldPopulateRelatedDecision = shouldPopulateRelatedDecision(componentRoomData, product, isComponentRoomsPriceAsSumOfPartEnabled, componentRoomSumOfPriceFlow);
                    populateComponentRoomRelatedBarDecisionChanges(componentRoomData, componentRoomRelatedBarDecisionChanges, cpBarDecisionChanges, shouldPopulateRelatedDecision);
                });
            });
        }

        if (tableBatchNonHiltonCRS != null) {
            tenantCrudService.execute(tableBatchNonHiltonCRS);
        } else {
            tenantCrudService.flush();
        }

        // Use the tableBatcher if it's been configured to do so
        if (tableBatch != null) {
            tenantCrudService.execute(tableBatch);
        } else {
            // Flush the EntityManager to be able to execute insert statement based on records
            tenantCrudService.flush();
        }

        return componentRoomRelatedBarDecisionChanges;
    }


    protected TableBatch buildTableBatchNonHiltonCRS() {
        TableBatch tableBatchDecisionDailyBarNonHiltonCRS = null;
        if (SystemConfig.isTableBatchForDecisionDailyBARForNonHiltonCRSEnabled()) {
            tableBatchDecisionDailyBarNonHiltonCRS = new TableBatch("usp_Decision_Dailybar_Output_NonHiltonCRS_Update", "Decision_Dailybar_Output_NonHiltonCRS_Update");
            tableBatchDecisionDailyBarNonHiltonCRS.addColumn("Decision_Dailybar_Output_NonHiltonCRS_ID", Types.BIGINT);
            tableBatchDecisionDailyBarNonHiltonCRS.addColumn("Decision_ID", Types.BIGINT);
            tableBatchDecisionDailyBarNonHiltonCRS.addColumn("Occupancy_Date", Types.DATE);
            tableBatchDecisionDailyBarNonHiltonCRS.addColumn("Accom_Type_ID", Types.INTEGER);
            tableBatchDecisionDailyBarNonHiltonCRS.addColumn("Rate_Unqualified_ID", Types.INTEGER);
            tableBatchDecisionDailyBarNonHiltonCRS.addColumn("Single_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBarNonHiltonCRS.addColumn("Double_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBarNonHiltonCRS.addColumn("Triple_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBarNonHiltonCRS.addColumn("Quad_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBarNonHiltonCRS.addColumn("Quint_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBarNonHiltonCRS.addColumn("Adult_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBarNonHiltonCRS.addColumn("Child_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBarNonHiltonCRS.addColumn("Product_ID", Types.BIGINT);
            tableBatchDecisionDailyBarNonHiltonCRS.addColumn("One_Child_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBarNonHiltonCRS.addColumn("Two_Child_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBarNonHiltonCRS.addColumn("Three_Child_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBarNonHiltonCRS.addColumn("Four_Child_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBarNonHiltonCRS.addColumn("Five_Child_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBarNonHiltonCRS.addColumn("Child_Age_1_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBarNonHiltonCRS.addColumn("Child_Age_2_Rate", Types.DECIMAL);
            tableBatchDecisionDailyBarNonHiltonCRS.addColumn("Child_Age_3_Rate", Types.DECIMAL);
        }
        return tableBatchDecisionDailyBarNonHiltonCRS;
    }

    private List<CPDecisionBAROutput> getParentProductOutputs(Product product, List<Product> products, Map<Product, List<CPDecisionBAROutput>> outputsByProduct) {
        Product parentProduct = products.stream()
                .filter(p -> p.getId().equals(product.getDependentProductId()))
                .findFirst()
                .orElse(null);
        return outputsByProduct.get(parentProduct);
    }

    private boolean sendNonPrimaryProductOutputs(Product product, Product barProduct) {
        return !product.isSystemDefaultOrIndependentProduct() &&
                !product.getDependentProductId().equals(barProduct.getId()) &&
                !PacmanWorkContextHelper.isHiltonClientCode();
    }

    private void populateComponentRoomRelatedBarDecisionChanges(ComponentRoomData componentRoomData, List<CPBarDecisionChange> componentRoomRelatedBarDecisionChanges, List<CPBarDecisionChange> cpBarDecisionChanges, boolean shouldPopulateRelatedDecision) {
        if (shouldPopulateRelatedDecision) {
            Set<AccomType> allCRAndRelatedCPRTs = componentRoomData.getAllCRAndCPRoomTypesWherePriceAsSumOfPartsIsEnabled();
            List<CPBarDecisionChange> crAndCPDecisionChanges = cpBarDecisionChanges.stream().filter(cpBarDecisionChange -> allCRAndRelatedCPRTs.contains(cpBarDecisionChange.getCPDecisionBAROutput().getAccomType()))
                    .collect(Collectors.toList());
            componentRoomRelatedBarDecisionChanges.addAll(crAndCPDecisionChanges);
        }
    }

    private boolean shouldPopulateRelatedDecision(ComponentRoomData componentRoomData, Product product, boolean isComponentRoomsPriceAsSumOfPartEnabled, boolean componentRoomSumOfPriceFlow) {
        return isComponentRoomsPriceAsSumOfPartEnabled
                && !componentRoomSumOfPriceFlow
                && null != componentRoomData
                && product.isSystemDefaultOrIndependentProduct();
    }

    private Map<AccomType, Map<AccomType, Integer>> getCRAndCPMappingWithQuantities(boolean componentRoomSumOfPriceFlow, ComponentRoomData componentRoomData) {
        Map<AccomType, Map<AccomType, Integer>> crAndCPMappingWithQuantities = new HashMap<>();
        if (componentRoomSumOfPriceFlow && null != componentRoomData) {
            crAndCPMappingWithQuantities = componentRoomData.getCRAndCPMappingWithQuantities();
        }
        return crAndCPMappingWithQuantities;
    }

    @VisibleForTesting
	public
    TreeMap<LocalDate, Map<Product, List<CPDecisionBAROutput>>> findCPDecisionBAROutputsForRoomTypes(LocalDate startDate, LocalDate endDate, Set<AccomType> roomTypesRelatedToComponentRoom) {
        // Using a TreeMap for ordering by the LocalDate
        TreeMap<LocalDate, Map<Product, List<CPDecisionBAROutput>>> cpDecisionBAROutputsByDateAndProduct = new TreeMap<>();

        // Query for all CPDecisionBAROutputs between start/end dates with capacity
        List<CPDecisionBAROutput> cpDecisionBAROutputs = cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate);

        cpDecisionBAROutputs = cpDecisionBAROutputs.stream().filter(cpDecisionBAROutput -> roomTypesRelatedToComponentRoom.contains(cpDecisionBAROutput.getAccomType()))
                .collect(Collectors.toList());
        // Since we need to process the CPDecisionBAROutput by date groupings
        // create a map by date and a list of the CPDecisionBAROutput for that date
        if (CollectionUtils.isNotEmpty(cpDecisionBAROutputs)) {
            for (CPDecisionBAROutput cpDecisionBAROutput : cpDecisionBAROutputs) {
                Map<Product, List<CPDecisionBAROutput>> dateProductOutputs = cpDecisionBAROutputsByDateAndProduct.computeIfAbsent(cpDecisionBAROutput.getArrivalDate(), v -> new LinkedHashMap<>());
                List<CPDecisionBAROutput> productOutputs = dateProductOutputs.computeIfAbsent(cpDecisionBAROutput.getProduct(), v -> new ArrayList<>());
                productOutputs.add(cpDecisionBAROutput);
            }
        }

        return cpDecisionBAROutputsByDateAndProduct;
    }

    private Map<AccomType, List<CPBarDecisionChange>> getCPDecisionsBarChangesRelatedToComponentRooms(Map<AccomType, Map<AccomType, Integer>> crAndCPMappingWithQuantities, List<CPBarDecisionChange> barDecisionChanges, LocalDate arrivalDate) {
        Map<AccomType, List<CPBarDecisionChange>> relatedBarOutputsForCR = new HashMap<>();
        for (Map.Entry<AccomType, Map<AccomType, Integer>> entry : crAndCPMappingWithQuantities.entrySet()) {
            Set<AccomType> cpAccomTypes = entry.getValue().keySet();
            Map<AccomType, List<CPBarDecisionChange>> decisionsForCPAccomType = barDecisionChanges.stream()
                    .filter(barDecisionChange -> arrivalDate.equals(barDecisionChange.getCPDecisionBAROutput().getArrivalDate()))
                    .filter(barDecisionChange -> cpAccomTypes.contains(barDecisionChange.getCPDecisionBAROutput().getAccomType()))
                    .collect(Collectors.groupingBy(barDecisionChange -> barDecisionChange.getCPDecisionBAROutput().getAccomType()));

            for (Map.Entry<AccomType, Integer> cpAccomTypeAndQuantity : entry.getValue().entrySet()) {
                if (decisionsForCPAccomType.containsKey(cpAccomTypeAndQuantity.getKey())) {
                    List<CPBarDecisionChange> cpDecisionChangesForQuantity = new ArrayList<>();
                    List<CPBarDecisionChange> cpBarDecisionChanges = decisionsForCPAccomType.get(cpAccomTypeAndQuantity.getKey());
                    for (int i = 0; i < cpAccomTypeAndQuantity.getValue(); i++) {
                        cpDecisionChangesForQuantity.addAll(cpBarDecisionChanges);
                    }
                    if (relatedBarOutputsForCR.containsKey(entry.getKey())) {
                        List<CPBarDecisionChange> barDecisionChanges1 = relatedBarOutputsForCR.get(entry.getKey());
                        cpDecisionChangesForQuantity.addAll(barDecisionChanges1);
                    }
                    relatedBarOutputsForCR.put(entry.getKey(), cpDecisionChangesForQuantity);
                }
            }
        }
        return relatedBarOutputsForCR;
    }

    protected BigDecimal getSupplementToBeAdded(CPDecisionContext cpDecisionContext, CPDecisionBAROutput cpDecisionBAROutput,
                                                BigDecimal rateWithoutSupplement) {
        Optional<AccomTypeSupplementValue> accomTypeSupplementValue = cpDecisionContext.getSupplementFor(cpDecisionBAROutput);
        if (accomTypeSupplementValue.isEmpty()) {
            return BigDecimal.ZERO;
        }
        return Supplement.getSupplementToAdd(rateWithoutSupplement, accomTypeSupplementValue.get());
    }

    // for every product and arrival date, this method is called with list of accom types
    private List<CPBarDecisionChange> handleProductDecisionsForDate(CPDecisionContext cpDecisionContext, Map<DecisionDailybarOutputKey, DecisionDailybarOutput> decisionDailybarOutputs, Map<DecisionDailybarOutputKey, DecisionDailybarOutputNonHiltonCRS> nonHiltonCRSDecisionDailybarOutputs, Map<CPBarDecisionChangeKey, CPBarDecisionChange> allProductsDecisionChanges, List<CPDecisionBAROutput> cpDecisionBAROutputs, boolean componentRoomSumOfPriceFlow, Map<AccomType, List<CPBarDecisionChange>> crRelatedBarDecisionChanges) {
        List<CPBarDecisionChange> productDecisionChanges = new ArrayList<>();
        for (CPDecisionBAROutput cpDecisionBAROutput : cpDecisionBAROutputs) {
            // From the roundOptimalBARS flow, the PrettyBAR field will always be null.  This was done by analytics
            // to inform the services layer that we needed to round the Optimal BAR value.  With the inception of
            // supplements being done as a 'post-process', we need to add the supplement value back onto the
            // specific/floor/ceiling override(s) should the override exist because analytics re-wrote those values
            // not knowing about them.
            BigDecimal prettyBAR = cpDecisionBAROutput.getPrettyBAR();
            if (prettyBAR == null) {
                // Saving the values from analytics with a float data type allows for precision errors when loaded
                // into BigDecimal objects.  The float is valuable as it shows the most precise value when querying
                // the database, but we need to protect the java side as BigDecimal has it's own precision tricks.
                // The optimal bar will be rounded to the 5th decimal place to hold it's integrity down to the same
                // level as it did before.  The overrides will respect the precision of '2' as that is what can be
                // entered on the UI.
                cpDecisionBAROutput.setOptimalBAR(BigDecimalUtil.roundReturnsNull(cpDecisionBAROutput.getOptimalBAR(), BigDecimalUtil.DEFAULT_PRECISION));
                cpDecisionBAROutput.setSpecificOverride(BigDecimalUtil.roundReturnsNull(cpDecisionBAROutput.getSpecificOverride(), OVERRIDE_PRECISION));
                cpDecisionBAROutput.setFloorOverride(BigDecimalUtil.roundReturnsNull(cpDecisionBAROutput.getFloorOverride(), OVERRIDE_PRECISION));
                cpDecisionBAROutput.setCeilingOverride(BigDecimalUtil.roundReturnsNull(cpDecisionBAROutput.getCeilingOverride(), OVERRIDE_PRECISION));

                // Add the supplement onto the override - if it exists.
                // Note that the absolute supplement value in this particular case is calculated on base override value
                if (cpDecisionContext.isSupplementPercentEnabled()) {
                    BigDecimal supplementToAdd;
                    supplementToAdd = getSupplementToBeAdded(cpDecisionContext, cpDecisionBAROutput, cpDecisionBAROutput.getSpecificOverride());
                    cpDecisionBAROutput.setSpecificOverride(BigDecimalUtil.addReturnsNull(cpDecisionBAROutput.getSpecificOverride(), supplementToAdd));
                    supplementToAdd = getSupplementToBeAdded(cpDecisionContext, cpDecisionBAROutput, cpDecisionBAROutput.getFloorOverride());
                    cpDecisionBAROutput.setFloorOverride(BigDecimalUtil.addReturnsNull(cpDecisionBAROutput.getFloorOverride(), supplementToAdd));
                    supplementToAdd = getSupplementToBeAdded(cpDecisionContext, cpDecisionBAROutput, cpDecisionBAROutput.getCeilingOverride());
                    cpDecisionBAROutput.setCeilingOverride(BigDecimalUtil.addReturnsNull(cpDecisionBAROutput.getCeilingOverride(), supplementToAdd));
                } else {
                    BigDecimal supplement = cpDecisionContext.getSupplement(cpDecisionBAROutput);
                    cpDecisionBAROutput.setSpecificOverride(BigDecimalUtil.addReturnsNull(cpDecisionBAROutput.getSpecificOverride(), supplement));
                    cpDecisionBAROutput.setFloorOverride(BigDecimalUtil.addReturnsNull(cpDecisionBAROutput.getFloorOverride(), supplement));
                    cpDecisionBAROutput.setCeilingOverride(BigDecimalUtil.addReturnsNull(cpDecisionBAROutput.getCeilingOverride(), supplement));
                }
            }

            // Get the PricingAccomClass
            PricingAccomClass pricingAccomClass = cpDecisionContext.getPricingAccomClass(cpDecisionBAROutput);

            DecisionDailybarOutputKey decisionDailybarOutputKey = new DecisionDailybarOutputKey(cpDecisionBAROutput);

            // Get the previously created DecisionDailybarOutput - used to default RoomsOnlyBAR and FinalBAR and for comparison purposes
            DecisionDailybarOutput previousDailybarOutput = decisionDailybarOutputs.get(decisionDailybarOutputKey);

            DecisionDailybarOutputNonHiltonCRS previousNonHiltonCRSDecisionDailybarOutput = nonHiltonCRSDecisionDailybarOutputs.get(decisionDailybarOutputKey);

            // Get the change for the product's dependent product - if there is one
            CPBarDecisionChange baseProductChange = allProductsDecisionChanges.get(new CPBarDecisionChangeKey(cpDecisionBAROutput, cpDecisionBAROutput.getProduct().getDependentProductId()));

            // If CPDecisionBAROutput is for a non-base room type, get the base room type change
            boolean isBaseRoomType = cpDecisionContext.isBaseRoomType(cpDecisionBAROutput);
            if (isBaseRoomType) {
                // Create a CPBarDecisionChange object and set whether an update will be required on it
                CPBarDecisionChange cpBarDecisionChange = new CPBarDecisionChange(cpDecisionContext, cpDecisionBAROutput, previousDailybarOutput,
                        previousNonHiltonCRSDecisionDailybarOutput, null, baseProductChange, componentRoomSumOfPriceFlow, crRelatedBarDecisionChanges);

                allProductsDecisionChanges.put(cpBarDecisionChange.uniqueKey(), cpBarDecisionChange);
                productDecisionChanges.add(cpBarDecisionChange);
            } else {
                // Get the baseRT change
                CPBarDecisionChange baseRoomTypeChange = allProductsDecisionChanges.get(new CPBarDecisionChangeKey(cpDecisionBAROutput, pricingAccomClass.getAccomType()));

                // If BaseRT Decisions only is enabled, we want to set all non-base RT decisions to be the same
                if (cpDecisionContext.isBaseRoomTypeOnlyEnabled() && baseRoomTypeChange != null) {

                    // Update the CPDecisionBAROutput to match the representation of the baseRoomType
                    cpDecisionBAROutput.setOptimalBAR(BigDecimalUtil.roundReturnsNull(cpDecisionBAROutput.getOptimalBAR(), BigDecimalUtil.DEFAULT_PRECISION));
                    cpDecisionBAROutput.setSpecificOverride(baseRoomTypeChange.getCPDecisionBAROutput().getSpecificOverride());
                    cpDecisionBAROutput.setRoomsOnlyBAR(baseRoomTypeChange.getCPDecisionBAROutput().getRoomsOnlyBAR());
                    cpDecisionBAROutput.setFinalBAR(baseRoomTypeChange.getCPDecisionBAROutput().getFinalBAR());
                }

                // Build a CPBarDecisionChange for the non-baseRT and set the values of the BaseRT
                CPBarDecisionChange nonBaseRoomTypeChange = new CPBarDecisionChange(cpDecisionContext, cpDecisionBAROutput, previousDailybarOutput, previousNonHiltonCRSDecisionDailybarOutput,
                        baseRoomTypeChange, baseProductChange, componentRoomSumOfPriceFlow, crRelatedBarDecisionChanges);
                allProductsDecisionChanges.put(nonBaseRoomTypeChange.uniqueKey(), nonBaseRoomTypeChange);
                productDecisionChanges.add(nonBaseRoomTypeChange);
            }
        }
        return productDecisionChanges;
    }

    public void populateAdjustmentValueForAgileProducts(List<CPDecisionBAROutput> productOutputsForComparison, CPDecisionBAROutput cpDecisionBAROutput) {
        //If it is not the BAR Product or Agile Product with Packages, use the Product Outputs for Comparison to find the adjustment rate as required
        if (!cpDecisionBAROutput.getProduct().isSystemDefaultOrIndependentProduct()
                && !cpDecisionBAROutput.getProduct().getType().equalsIgnoreCase(AgileRatesProductTypeEnum.FENCED_AND_PACKAGED.getValue())
                && !cpDecisionBAROutput.getProduct().getType().equalsIgnoreCase(AgileRatesProductTypeEnum.UNFENCED_AND_PACKAGED.getValue())) {
            BigDecimal currentFinalBar = cpDecisionBAROutput.getFinalBAR();
            //Grab the Final BAR from the Product of the corresponding Accom Type
            CPDecisionBAROutput comparisonProductOutput = productOutputsForComparison.stream()
                    .filter(barItem -> barItem.getAccomType().equals(cpDecisionBAROutput.getAccomType()))
                    .findFirst()
                    .orElse(null);
            BigDecimal comparisonProductOutputFinalBar = null;
            if (comparisonProductOutput != null) {
                comparisonProductOutputFinalBar = comparisonProductOutput.getFinalBAR();
            }
            if (currentFinalBar != null && comparisonProductOutputFinalBar != null && comparisonProductOutputFinalBar.compareTo(BigDecimal.ZERO) > 0) {
                if (cpDecisionBAROutput.getProduct().getOffsetMethod().equals(AgileRatesOffsetMethod.PERCENTAGE)) {
                    //Percentage Adjustment Value = ((currentFinalBAR - comparisonProductOutputFinalBAR) / comparisonProductOutputFinalBAR)) * 100
                    BigDecimal adjustmentValue = currentFinalBar
                            .subtract(comparisonProductOutputFinalBar)
                            .multiply(BigDecimal.valueOf(100))
                            .divide(comparisonProductOutputFinalBar, 2, RoundingMode.HALF_UP);
                    cpDecisionBAROutput.setAdjustmentValue(adjustmentValue);
                } else {
                    BigDecimal adjustmentValue = currentFinalBar
                            .subtract(comparisonProductOutputFinalBar)
                            .setScale(2, RoundingMode.HALF_UP);
                    cpDecisionBAROutput.setAdjustmentValue(adjustmentValue);
                }
            }
        }
    }

    private void checkNonDefaultProductRequiresUpdate(CPDecisionContext cpDecisionContext, List<CPBarDecisionChange> cpBarDecisionChanges) {

        // Check to see if the numberOfDaysToArrival is the max of a DTA range - if it is, check to see if the baseOccupancyType rate has changed
        boolean baseOccupancyRateChangedDueToDTA =
                cpBarDecisionChanges.stream()
                        .anyMatch(change -> cpDecisionContext.hasRateChangeDueToDTA(change.getCPDecisionBAROutput()) && !BigDecimalUtil.equals(change.getBaseOccupancyRate(), change.getPreviousDailybarOutputBaseOccupancyTypeRate()));

        // Check to see if there is a rate offset override and if that override has caused a new rate value
        boolean changedRateOffsetOverride =
                cpBarDecisionChanges.stream()
                        .anyMatch(change -> cpDecisionContext.hasProductRateOffsetOverride(change.getCPDecisionBAROutput())
                                && !BigDecimalUtil.equals(change.getBaseOccupancyRate(), change.getPreviousDailybarOutputBaseOccupancyTypeRate()));

        if (baseOccupancyRateChangedDueToDTA || changedRateOffsetOverride) {
            cpBarDecisionChanges.forEach(change -> change.setUpdateRequired(true));
        }
    }

    private void validateAccomClassPriceRank(List<AccomClassPriceRank> accomClassPriceRanks, List<CPBarDecisionChange> cpBarDecisionChanges) {
        // Put all the changes in a map to make them easier to access
        Map<AccomType, CPBarDecisionChange> cpBarDecisionChangeHashMap = buildCPBarDecisionChangeByAccomType(cpBarDecisionChanges);

        // Set the changeRequiredRankUpdate = true so that at least once pass through the paths occurs
        boolean changeRequiredRankUpdate = true;
        while (changeRequiredRankUpdate) {

            // Now that you are within the loop - set the flag to false to fall out of this logic if no paths require an update
            changeRequiredRankUpdate = false;

            //Iterate through the list of price rank entities and ensure that neither the lower nor higher change violates the price hierarchy.
            for (AccomClassPriceRank accomClassPriceRank : accomClassPriceRanks) {
                Set<AccomType> lowerAccomTypes = accomClassPriceRank.getLowerRankAccomClass().getAccomTypes();
                Set<AccomType> higherAccomTypes = accomClassPriceRank.getHigherRankAccomClass().getAccomTypes();

                // Using the lower/higher accom types - check to see if a rank update is required
                changeRequiredRankUpdate = isRankUpdateRequired(cpBarDecisionChangeHashMap, changeRequiredRankUpdate, lowerAccomTypes, higherAccomTypes);
            }
        }
    }

    private void validateAccomClassPriceRankForIndependentProduct(List<AccomClassPriceRank> accomClassPriceRanks, List<CPBarDecisionChange> cpBarDecisionChanges, List<AccomType> productAccomTypes) {
        // Put all the changes in a map to make them easier to access
        Map<AccomType, CPBarDecisionChange> cpBarDecisionChangeHashMap = buildCPBarDecisionChangeByAccomType(cpBarDecisionChanges);

        // Set the changeRequiredRankUpdate = true so that at least once pass through the paths occurs
        boolean changeRequiredRankUpdate = true;
        while (changeRequiredRankUpdate) {

            // Now that you are within the loop - set the flag to false to fall out of this logic if no paths require an update
            changeRequiredRankUpdate = false;

            //Iterate through the list of price rank entities and ensure that neither the lower nor higher change violates the price hierarchy.
            for (AccomClassPriceRank accomClassPriceRank : accomClassPriceRanks) {
                Set<AccomType> lowerAccomTypes = filterAccomTypesForIndependentProduct(accomClassPriceRank.getLowerRankAccomClass().getAccomTypes(), productAccomTypes);
                Set<AccomType> higherAccomTypes = filterAccomTypesForIndependentProduct(accomClassPriceRank.getHigherRankAccomClass().getAccomTypes(), productAccomTypes);

                if (CollectionUtils.isNotEmpty(lowerAccomTypes) || CollectionUtils.isNotEmpty(higherAccomTypes)) {
                    if (CollectionUtils.isEmpty(lowerAccomTypes)) {
                        lowerAccomTypes = findAccomTypesForNextLowestPriceRankAccomClass(accomClassPriceRanks, accomClassPriceRank.getLowerRankAccomClass(), productAccomTypes);
                        //get next lower accom type and its RTs
                    } else if (CollectionUtils.isEmpty(higherAccomTypes)) {
                        //get next higher accom class and its RTs
                        higherAccomTypes = findAccomTypesForNextHighestPriceRankAccomClass(accomClassPriceRanks, accomClassPriceRank.getHigherRankAccomClass(), productAccomTypes);
                    }

                    if (CollectionUtils.isNotEmpty(lowerAccomTypes) && CollectionUtils.isNotEmpty(higherAccomTypes)) {
                        // Using the lower/higher accom types - check to see if a rank update is required
                        changeRequiredRankUpdate = isRankUpdateRequired(cpBarDecisionChangeHashMap, changeRequiredRankUpdate, lowerAccomTypes, higherAccomTypes);
                    }
                }
            }
        }
    }

    @VisibleForTesting
	public
    Set<AccomType> findAccomTypesForNextLowestPriceRankAccomClass(List<AccomClassPriceRank> accomClassPriceRanks, AccomClass lowerRankAccomClass, List<AccomType> productAccomTypes) {
        AccomClassPriceRank accomClassPriceRank = accomClassPriceRanks.stream().filter(acpr -> acpr.getHigherRankAccomClass().equals(lowerRankAccomClass)).findFirst().orElse(null);
        if (accomClassPriceRank != null) {
            Set<AccomType> accomTypes = filterAccomTypesForIndependentProduct(accomClassPriceRank.getLowerRankAccomClass().getAccomTypes(), productAccomTypes);
            if (CollectionUtils.isEmpty(accomTypes)) {
                return findAccomTypesForNextLowestPriceRankAccomClass(accomClassPriceRanks, accomClassPriceRank.getLowerRankAccomClass(), productAccomTypes);
            } else {
                return accomTypes;
            }
        }
        return Set.of();
    }

    @VisibleForTesting
	public
    Set<AccomType> findAccomTypesForNextHighestPriceRankAccomClass(List<AccomClassPriceRank> accomClassPriceRanks, AccomClass higherRankAccomClass, List<AccomType> productAccomTypes) {
        AccomClassPriceRank accomClassPriceRank = accomClassPriceRanks.stream().filter(acpr -> acpr.getLowerRankAccomClass().equals(higherRankAccomClass)).findFirst().orElse(null);
        if (accomClassPriceRank != null) {
            Set<AccomType> accomTypes = filterAccomTypesForIndependentProduct(accomClassPriceRank.getHigherRankAccomClass().getAccomTypes(), productAccomTypes);
            if (CollectionUtils.isEmpty(accomTypes)) {
                return findAccomTypesForNextHighestPriceRankAccomClass(accomClassPriceRanks, accomClassPriceRank.getHigherRankAccomClass(), productAccomTypes);
            } else {
                return accomTypes;
            }
        }
        return Set.of();
    }

//    private AccomClassPriceRank findNextPriceRankAccomClass(List<AccomClassPriceRank> accomClassPriceRanks, AccomClass lowerRankAccomClass) {
//        AccomClassPriceRank accomClassPriceRank = accomClassPriceRanks.stream().filter(acpr -> acpr.getHigherRankAccomClass().equals(lowerRankAccomClass)).findFirst().orElse(null);
//        if (accomClassPriceRank != null) {
//            return accomClassPriceRank
//        }
//    }

    private Set<AccomType> filterAccomTypesForIndependentProduct(Set<AccomType> accomTypes, List<AccomType> productAccomTypes) {
        return accomTypes.stream().filter(accomType -> productAccomTypes.contains(accomType)).collect(Collectors.toSet());
    }

    private boolean isRankUpdateRequired(Map<AccomType, CPBarDecisionChange> cpBarDecisionChangeHashMap, boolean changeRequiredRankUpdate, Set<AccomType> lowerAccomTypes, Set<AccomType> higherAccomTypes) {
        for (AccomType lowerAccomType : lowerAccomTypes) {
            for (AccomType higherAccomType : higherAccomTypes) {
                // Get the decisions for the lower/higher ranked changes to do a comparison against
                CPBarDecisionChange lowerRankedCpBarDecisionChange = cpBarDecisionChangeHashMap.get(lowerAccomType);
                CPBarDecisionChange higherRankedCpBarDecisionChange = cpBarDecisionChangeHashMap.get(higherAccomType);

                // If we have a lower/higher change and has an invalid accom class price ranking,
                // set the changeRequiredRankUpdate flag to true to make another pass through the ranks
                if (lowerRankedCpBarDecisionChange != null && higherRankedCpBarDecisionChange != null && hasInvalidAccomClassPriceRanking(lowerRankedCpBarDecisionChange, higherRankedCpBarDecisionChange)) {
                    changeRequiredRankUpdate = true;
                }
            }
        }
        return changeRequiredRankUpdate;
    }

    private Map<AccomType, CPBarDecisionChange> buildCPBarDecisionChangeByAccomType(List<CPBarDecisionChange> cpBarDecisionChanges) {
        Map<AccomType, CPBarDecisionChange> cpBarDecisionChangeHashMap = new HashMap<>();
        for (CPBarDecisionChange cpBarDecisionChange : cpBarDecisionChanges) {
            cpBarDecisionChangeHashMap.put(cpBarDecisionChange.getCPDecisionBAROutput().getAccomType(), cpBarDecisionChange);
        }
        return cpBarDecisionChangeHashMap;
    }

    private boolean hasInvalidAccomClassPriceRanking(CPBarDecisionChange lowerAccomClassCpChange, CPBarDecisionChange higherAccomClassCpChange) {
        boolean lowerAccomClassUpdateRequired = lowerAccomClassCpChange.isUpdateRequired();
        boolean higherAccomClassUpdateRequired = higherAccomClassCpChange.isUpdateRequired();

        BigDecimal lowerPreviousBaseOccupancyRate = lowerAccomClassCpChange.getPreviousDailybarOutputBaseOccupancyTypeRate();
        BigDecimal higherPreviousBaseOccupancyRate = higherAccomClassCpChange.getPreviousDailybarOutputBaseOccupancyTypeRate();

        BigDecimal lowerBaseOccupancyRate = lowerAccomClassCpChange.getBaseOccupancyRate();
        BigDecimal higherBaseOccupancyRate = higherAccomClassCpChange.getBaseOccupancyRate();

        //If the old decision of the lower ranked accom class is higher than the new decision of the higher ranked accom class we must update.
        if (!lowerAccomClassUpdateRequired && higherAccomClassUpdateRequired && (lowerPreviousBaseOccupancyRate == null || lowerPreviousBaseOccupancyRate.compareTo(higherBaseOccupancyRate) > 0)) {
            lowerAccomClassCpChange.setUpdateRequired(true);
            return true;
        }

        //If the old decision of the higher ranked accom class is lower than the new decision of the lower ranked accom class we must update.
        if (!higherAccomClassUpdateRequired && lowerAccomClassUpdateRequired && (higherPreviousBaseOccupancyRate == null || higherPreviousBaseOccupancyRate.compareTo(lowerBaseOccupancyRate) < 0)) {
            higherAccomClassCpChange.setUpdateRequired(true);
            return true;
        }

        return false;
    }

    private Map<DecisionDailybarOutputKey, DecisionDailybarOutput> findUniqueDecisionDailybarOutputs(LocalDate startDate, LocalDate endDate) {
        // Query for all DecisionDailybarOutputs between start/end dates
        List<DecisionDailybarOutput> decisionDailybarOutputs = tenantCrudService.findByNamedQuery(DecisionDailybarOutput.FIND_BY_OCCUPANCY_DATE_BETWEEN, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());

        // Create Map of DecisionDailybarOutput by the unique record key (product/occupancyDate/accomType)
        return decisionDailybarOutputs.stream().collect(Collectors.toMap(DecisionDailybarOutput::uniqueKey, Function.identity()));
    }

    private Map<DecisionDailybarOutputKey, DecisionDailybarOutputNonHiltonCRS> findUniqueNonHiltonCRSDecisionDailybarOutputs(LocalDate startDate, LocalDate endDate) {
        // Query for all NonHiltonCRSDecisionDailybarOutput between start/end dates
        List<DecisionDailybarOutputNonHiltonCRS> nonHiltonCRSDecisionDailybarOutputs = tenantCrudService.findByNamedQuery(DecisionDailybarOutputNonHiltonCRS.FIND_BY_OCCUPANCY_DATE_BETWEEN, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());

        // Create Map of NonHiltonCRSDecisionDailybarOutput by the unique record key (product/occupancyDate/accomType)
        return nonHiltonCRSDecisionDailybarOutputs.stream().collect(Collectors.toMap(DecisionDailybarOutputNonHiltonCRS::uniqueKey, Function.identity()));
    }

    public TreeMap<LocalDate, Map<Product, List<CPDecisionBAROutput>>> findCPDecisionBAROutputs(LocalDate startDate, LocalDate endDate) {
        // Using a TreeMap for ordering by the LocalDate
        TreeMap<LocalDate, Map<Product, List<CPDecisionBAROutput>>> cpDecisionBAROutputsByDateAndProduct = new TreeMap<>();

        // Query for all CPDecisionBAROutputs between start/end dates with capacity
        List<CPDecisionBAROutput> cpDecisionBAROutputs = cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate);

        // Since we need to process the CPDecisionBAROutput by date groupings
        // create a map by date and a list of the CPDecisionBAROutput for that date
        if (CollectionUtils.isNotEmpty(cpDecisionBAROutputs)) {
            for (CPDecisionBAROutput cpDecisionBAROutput : cpDecisionBAROutputs) {
                Map<Product, List<CPDecisionBAROutput>> dateProductOutputs = cpDecisionBAROutputsByDateAndProduct.computeIfAbsent(cpDecisionBAROutput.getArrivalDate(), v -> new LinkedHashMap<>());
                List<CPDecisionBAROutput> productOutputs = dateProductOutputs.computeIfAbsent(cpDecisionBAROutput.getProduct(), v -> new ArrayList<>());
                productOutputs.add(cpDecisionBAROutput);
            }
        }

        return cpDecisionBAROutputsByDateAndProduct;
    }

    public void updateEnableFullRefresh(boolean enableFullRefresh) {
        CPConfiguration cpConfiguration = tenantCrudService.findOne(CPConfiguration.class);
        cpConfiguration.setEnableFullRefresh(enableFullRefresh);
        tenantCrudService.save(cpConfiguration);
    }

    public boolean isNonHiltonCrsSendPriceAdjustmentEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(NON_HILTON_CRS_SEND_PRICE_ADJUSTMENT_ENABLED);
    }

    public boolean isFreeNightEligibleForCurrentFreeNightDecision(CPDecisionBAROutput cpDecisionBAROutput) {
        return cpDecisionBAROutput.getOptimalBAR().intValue() == HUNDRED_PERCENT_ADJUSTMENT;
    }

    public boolean isFreeNightProduct(CPDecisionBAROutput cpDecisionBAROutput, CPDecisionContext cpDecisionContext) {
        return cpDecisionContext.isConsortiaFreeNightEnabled() && cpDecisionBAROutput.getProduct().isFreeNightEnabled();
    }

    protected Set<Product> getFreeUpgradeProducts(CPDecisionContext cpDecisionContext) {
        return cpDecisionContext.getProductsInHierarchicalOrder().values().stream()
                .flatMap(Collection::stream).filter(Product::isFreeUpgradeEnabled).collect(Collectors.toSet());
    }

    protected ExternalSystem getExternalSystem() {
        if (externalSystemHelper.isHilstar()) {
            return ExternalSystem.HILSTAR;
        }

        if (externalSystemHelper.isPCRS()) {
            return ExternalSystem.PCRS;
        }
        // fail since we need to have a vendor mapping configured for free upgrade, either with PCRS or with HCRS
        throw new TetrisException("External system must be HCRS/PCRS since there is a free upgrade product present");
    }

    protected List<AccomTypeVendorMapping> getAccomTypeVendorMappings(ExternalSystem externalSystem) {
        List<AccomTypeVendorMapping> accomTypeMappings = roomTypeVendorMappingService.getRoomTypeVendorMappings(externalSystem.getCode())
                .stream()
                .filter(a -> !a.getAccomTypeCode().equalsIgnoreCase(a.getVendorAccomTypeCode())).collect(Collectors.toList());
        if (accomTypeMappings.isEmpty()) {
            // fail since we need to have a vendor mapping configured for at least one accom type
            throw new TetrisException("Vendor mapping should be configured for at least one accom type for free upgrade to work. None found for "
                    + externalSystem.getCode());
        }
        return accomTypeMappings;
    }

    // returns a map where key is accom type after upgrade and value is accom type before upgrade
    protected Map<String, String> getUpgradePaths() {
        List<AccomTypeVendorMapping> accomTypeMappings = getAccomTypeVendorMappings(getExternalSystem());
        return accomTypeMappings.stream()
                .collect(Collectors.toMap(AccomTypeVendorMapping::getVendorAccomTypeCode, AccomTypeVendorMapping::getAccomTypeCode));
    }


}
