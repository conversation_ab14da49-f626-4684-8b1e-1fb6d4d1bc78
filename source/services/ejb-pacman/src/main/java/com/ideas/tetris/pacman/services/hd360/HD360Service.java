package com.ideas.tetris.pacman.services.hd360;

import com.ideas.tetris.pacman.services.bestavailablerate.entity.TotalActivity;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.demand360.Demand360Service;
import com.ideas.tetris.pacman.services.hd360.dto.HD360BookingSummaryPaceDTO;
import com.ideas.tetris.pacman.services.hd360.dto.HD360MktSegDemandDTO;
import com.ideas.tetris.pacman.services.hd360.entity.HD360MarketSegmentHistoryCapacity;
import com.ideas.tetris.pacman.services.hd360.entity.HD360TransitoryBookingSummaryPace;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.TableBatchAware;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.joda.time.LocalDate;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceMktSegActivity.GET_BY_BUSINESS_DAY_END_AND_OCCUPANCYDATE;
import static com.ideas.tetris.pacman.services.bestavailablerate.entity.TotalActivity.BY_OCCUPANCY_DATERANGE;
import static com.ideas.tetris.pacman.services.hd360.dto.HD360BookingSummaryPaceDTO.USP_HD360_BOOKING_SUMMARY_PACE_BATCH_LOAD;
import static com.ideas.tetris.pacman.services.hd360.entity.HD360BookingSummaryPace.GET_HD360DEMAND_COUNT_BY_CAPTURE_DATE;
import static com.ideas.tetris.pacman.services.hd360.entity.HD360MarketSegmentHistoryCapacity.DELETE_BY_OCCUPANCY_DATE;
import static com.ideas.tetris.pacman.services.hd360.entity.HD360TransitoryBookingSummaryPace.GET_HD360DEMAND_TRANSITORY_BY_CAPTURE_DATE;
import static java.util.stream.Collectors.toList;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class HD360Service {

    private static final Logger LOGGER = Logger.getLogger(Demand360Service.class.getName());

    private static final String BUSINESS_DAY_END_KEY = "businessDayEnd";
    private static final String OCCUPANCY_DATE_END_KEY = "occupancyDateEnd";
    private static final String OCCUPANCY_DATE_START_KEY = "occupancyDateStart";
    private static final String COMP_PROPERTY_ID_KEY = "compPropertyId";
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	public CrudService tenantCrudService;

    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	public CrudService crudService;

    @Autowired
	public AbstractMultiPropertyCrudService multiPropertyCrudService;

    @Autowired
	public JobServiceLocal jobService;

    @Autowired
	public HD360CompSetService compSetService;

    public List<HD360BookingSummaryPaceDTO> calculateAndPersistDemandForCompSet(List<Integer> propertyIds, Integer subscriber,
                                                                                Date businessDayEnd, Date occupancyStartDate) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(BUSINESS_DAY_END_KEY, DateUtil.formatDate(businessDayEnd, DateUtil.DEFAULT_DATE_FORMAT));
        parameters.put(OCCUPANCY_DATE_START_KEY, DateUtil.formatDate(occupancyStartDate, DateUtil.DEFAULT_DATE_FORMAT));
        parameters.put(OCCUPANCY_DATE_END_KEY, LocalDate.parse(new SimpleDateFormat(DateUtil.DEFAULT_DATE_FORMAT).format(businessDayEnd))
                .plusDays(compSetService.findMinimumForecastWindow(propertyIds)));

        List<List<HD360MktSegDemandDTO>> hD360MktSegDemandDTOMultiTenantList = multiPropertyCrudService
                .findByNamedQuery(propertyIds, GET_BY_BUSINESS_DAY_END_AND_OCCUPANCYDATE, parameters);

        List<HD360MktSegDemandDTO> demands = hD360MktSegDemandDTOMultiTenantList.stream()
                .flatMap(List::stream)
                .collect(toList());

        Map<String, HD360MktSegDemandDTO> subscriberDemandMap = demands.stream()
                .filter(a -> a.getPropertyId() == subscriber)
                .collect(Collectors.toMap(HD360MktSegDemandDTO::getKey, Function.identity()));

        Function<HD360BookingSummaryPaceDTO, List> classifier =
                (item) -> List.of(
                        item.getOccupancyDate(),
                        item.getDemand360MarketSegmentDetailId());

        List<HD360BookingSummaryPaceDTO> hD360 = hD360MktSegDemandDTOMultiTenantList.stream()
                .flatMap(List::stream)
                .map(s -> {
                    return new HD360BookingSummaryPaceDTO
                            (s.getPropertyId(), s.getArrivals().intValue(), s.getRoomsSold().intValue(), s.getRoomRevenue()
                                    , s.getArrivals().intValue(), s.getRoomsSold().intValue(), s.getRoomRevenue()
                                    , DateUtil.convertDateToLocalDate(s.getOccupancyDate()), DateUtil.convertDateToLocalDate(businessDayEnd), s.getMarketSegDetailId());
                })
                .collect(Collectors.groupingBy(classifier))
                .entrySet().stream()
                .map(e -> e.getValue().stream()
                        .reduce((f1, f2) -> {
                            return new HD360BookingSummaryPaceDTO(f1.getPropertyId(), f1.getArrivals(), f1.getRoomsSold(), f1.getRoomRevenue(),
                                    (f1.getMktArrivals() + f2.getMktArrivals()), (f1.getMktRoomSold() + f2.getMktRoomSold()), f1.getMktRoomRevenue().add(f2.getMktRoomRevenue()),
                                    f1.getOccupancyDate(), f1.getCaptureDate(), f1.getDemand360MarketSegmentDetailId()
                            );
                        })
                )
                .map(f -> {
                    HD360BookingSummaryPaceDTO dto = f.get();
                    HD360MktSegDemandDTO subscriberDemand = subscriberDemandMap.get(dto.getGroupingKey());
                    dto.setPropertyId(subscriber);
                    dto.setArrivals(subscriberDemand == null ? 0 : subscriberDemand.getArrivals().intValue());
                    dto.setRoomsSold(subscriberDemand == null ? 0 : subscriberDemand.getRoomsSold().intValue());
                    dto.setRoomRevenue(subscriberDemand == null ? BigDecimal.ZERO : subscriberDemand.getRoomRevenue());

                    return dto;
                })
                .collect(toList());

        if (!hD360.isEmpty() && !this.isHD360DemandAlreadyExist(parameters, GET_HD360DEMAND_COUNT_BY_CAPTURE_DATE)) {
            LOGGER.info("Creating HD360 Demand for property id " + propertyIds.get(0) + " and CompSet Count " +
                    propertyIds.size() + " , HD360BookingSummary for demand count " + demands.size() +
                    " for businessDayEnd " + DateUtil.formatDate(businessDayEnd, DateUtil.DEFAULT_DATE_FORMAT));
            tenantCrudService.execute(USP_HD360_BOOKING_SUMMARY_PACE_BATCH_LOAD, hD360);
        } else {
            LOGGER.info("HD360 Demand not found or already exists for property id : " + propertyIds.get(0) +
                    " for businessDayEnd : " + businessDayEnd);
        }
        return hD360;
    }

    public List<HD360BookingSummaryPaceDTO> calculateAndPersistDemandUsingTransitoryRoute(Integer propertyId, Date businessDayEnd) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(BUSINESS_DAY_END_KEY, LocalDate.parse(new SimpleDateFormat(DateUtil.DEFAULT_DATE_FORMAT).format(businessDayEnd)));
        parameters.put(OCCUPANCY_DATE_END_KEY, LocalDate.parse(new SimpleDateFormat(DateUtil.DEFAULT_DATE_FORMAT).format(businessDayEnd))
                .plusDays(compSetService.findMinimumForecastWindow(propertyId)));

        List<HD360TransitoryBookingSummaryPace> transitoryDemand = tenantCrudService.findByNamedQuery(GET_HD360DEMAND_TRANSITORY_BY_CAPTURE_DATE, parameters);

        Map<String, HD360TransitoryBookingSummaryPace> subscriberDemandMap = transitoryDemand.stream()
                .filter(a -> a.getCompPropertyId() == propertyId)
                .collect(Collectors.toMap(HD360TransitoryBookingSummaryPace::getGroupingKey, Function.identity()));

        Function<HD360BookingSummaryPaceDTO, List> classifier =
                (item) -> List.of(
                        item.getOccupancyDate(),
                        item.getDemand360MarketSegmentDetailId());

        List<HD360BookingSummaryPaceDTO> hD360 = transitoryDemand.stream()
                .map(s -> {
                    return new HD360BookingSummaryPaceDTO
                            (s.getCompPropertyId(), s.getArrivals(), s.getRoomsSold(), s.getRoomRevenue()
                                    , s.getArrivals(), s.getRoomsSold(), s.getRoomRevenue()
                                    , s.getOccupancyDate(), DateUtil.convertDateToLocalDate(businessDayEnd), s.getDemand360MarketSegmentDetail().getId());
                })
                .collect(Collectors.groupingBy(classifier))
                .entrySet().stream()
                .map(e -> e.getValue().stream()
                        .reduce((f1, f2) -> {
                            return new HD360BookingSummaryPaceDTO(f1.getPropertyId(), f1.getArrivals(), f1.getRoomsSold(), f1.getRoomRevenue(),
                                    (f1.getMktArrivals() + f2.getMktArrivals()), (f1.getMktRoomSold() + f2.getMktRoomSold()), f1.getMktRoomRevenue().add(f2.getMktRoomRevenue()),
                                    f1.getOccupancyDate(), f1.getCaptureDate(), f1.getDemand360MarketSegmentDetailId()
                            );
                        })
                )
                .map(f -> {
                    HD360BookingSummaryPaceDTO dto = f.get();
                    HD360TransitoryBookingSummaryPace subscriberDemand = subscriberDemandMap.get(dto.getGroupingKey());
                    dto.setPropertyId(propertyId);
                    dto.setArrivals(subscriberDemand == null ? 0 : subscriberDemand.getArrivals());
                    dto.setRoomsSold(subscriberDemand == null ? 0 : subscriberDemand.getRoomsSold());
                    dto.setRoomRevenue(subscriberDemand == null ? BigDecimal.ZERO : subscriberDemand.getRoomRevenue());
                    return dto;
                })
                .collect(Collectors.toList());

        if (!hD360.isEmpty() && !this.isHD360DemandAlreadyExist(parameters, GET_HD360DEMAND_COUNT_BY_CAPTURE_DATE)) {
            LOGGER.info("Creating HD360 Demand from transitory table for property id " + propertyId +
                    ",  count: " + transitoryDemand.size() + " for businessDayEnd : " + businessDayEnd);
            tenantCrudService.execute(USP_HD360_BOOKING_SUMMARY_PACE_BATCH_LOAD, hD360);
        } else {
            LOGGER.info("HD360 transitory demand not found or already exists for property id : " + propertyId +
                    " for businessDayEnd : " + businessDayEnd);
        }

        return hD360;
    }

    public List<HD360MarketSegmentHistoryCapacity> calculateAndPersistTotalCapacityForCompSet(List<Integer> propertyIds, Date startDate, Date systemDate) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("startDate", startDate);
        parameters.put("endDate", LocalDate.parse(new SimpleDateFormat(DateUtil.DEFAULT_DATE_FORMAT).format(systemDate))
                .plusDays(compSetService.findMinimumForecastWindow(propertyIds)).toDate());

        List<List<TotalActivity>> paceTotalActivities = multiPropertyCrudService.findByNamedQuery(propertyIds, BY_OCCUPANCY_DATERANGE, parameters);

        List<HD360MarketSegmentHistoryCapacity> capacities = paceTotalActivities.stream()
                .flatMap(List::stream)
                .map(s -> {
                    return new HD360MarketSegmentHistoryCapacity
                            (s.getPropertyId(), s.getTotalAccomCapacity().intValue(), DateUtil.convertDateToLocalDate(s.getOccupancyDate()));
                })
                .collect(Collectors.groupingBy(activity -> activity.getOccupancyDate()))
                .entrySet().stream()
                .map(e -> e.getValue().stream()
                        .reduce((f1, f2) -> new HD360MarketSegmentHistoryCapacity(propertyIds.get(0), (f1.getCapacity() + (f2.getCapacity())), f1.getOccupancyDate())))
                .map(f -> f.get())
                .collect(toList());

        if (!capacities.isEmpty()) {
            tenantCrudService.executeUpdateByNamedQuery(DELETE_BY_OCCUPANCY_DATE,
                    QueryParameter.with("systemDate", DateUtil.convertDateToLocalDate(systemDate)).parameters());
            tenantCrudService.save(capacities);
        }
        return capacities;
    }

    public int truncateTransitoryBookingPaces() {
        LOGGER.info("Deleting all records form HD360_Transitory_Booking_Summary_Pace table");
        return tenantCrudService.executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[HD360_Transitory_Booking_Summary_Pace];");
    }

    public int truncateBookingPaces() {
        LOGGER.info("Deleting all records form HD360_Booking_Summary_Pace table");
        return tenantCrudService.executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[HD360_Booking_Summary_Pace];");
    }

    public int truncateMktHistCapacity() {
        LOGGER.info("Deleting all records form HD360_MKT_Hist_Capacity table");
        return tenantCrudService.executeUpdateByNativeQuery("TRUNCATE TABLE [dbo].[HD360_MKT_Hist_Capacity];");
    }

    private boolean isHD360DemandAlreadyExist(Map<String, Object> parameters, String query) {
        Long count = tenantCrudService.findByNamedQuerySingleResult(query,
                QueryParameter.with(BUSINESS_DAY_END_KEY, new LocalDate(parameters.get(BUSINESS_DAY_END_KEY))).parameters());

        return count > 0;
    }

    public boolean isHD360DemandAlreadyExistInTransitory(Integer propertyId, Map<String, Object> parameters, String query) {
        List result = multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, query,
                QueryParameter.with(BUSINESS_DAY_END_KEY, new LocalDate(parameters.get(BUSINESS_DAY_END_KEY))).
                        and(COMP_PROPERTY_ID_KEY, parameters.get(COMP_PROPERTY_ID_KEY)).parameters());

        return (!result.isEmpty() && (Long) result.get(0) > 0);
    }

    public List<HD360MktSegDemandDTO> getHD360MktSegDemand(String query, Map<String, Object> parameters) {
        return tenantCrudService.findByNamedQuery(query, parameters);
    }

    public void executeBatchInsertOnSingleProperty(Integer propertyId, String storedProcedure, List<? extends TableBatchAware> table) {
        multiPropertyCrudService.executeBatchInsertOnSingleProperty(propertyId, storedProcedure, table);
    }
}