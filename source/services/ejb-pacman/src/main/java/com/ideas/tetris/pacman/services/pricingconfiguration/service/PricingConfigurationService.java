package com.ideas.tetris.pacman.services.pricingconfiguration.service;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.threadpool.TetrisDataServiceThreadPool;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.integration.ratchet.services.currencyexchange.CurrencyExchangeService;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClassPriceRank;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.*;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.bestavailablerate.*;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.CeilingFloor;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.*;
import com.ideas.tetris.pacman.services.centralrms.CentralRmsService;
import com.ideas.tetris.pacman.services.centralrms.SyncCentralRmsService;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.BrandCodeServiceTypeMapping;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.configautomation.dto.AccomClassMinPriceDiffResponse;
import com.ideas.tetris.pacman.services.configautomation.dto.GPSyncBARCeilingFloorDTO;
import com.ideas.tetris.pacman.services.continuouspricing.CPTaxInclusiveMigrationService;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.currency.CurrencyService;
import com.ideas.tetris.pacman.services.datafeed.dto.pricingdata.MinPriceDifferentDTO;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfiguration;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingEvaluationMethod;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.GroupPricingConfigurationService;
import com.ideas.tetris.pacman.services.independentproducts.service.IndependentProductsService;
import com.ideas.tetris.pacman.services.override.InvalidateOverridesService;
import com.ideas.tetris.pacman.services.perpersonpricing.MaximumOccupantsEntity;
import com.ideas.tetris.pacman.services.perpersonpricing.OccupantBucketEntity;
import com.ideas.tetris.pacman.services.perpersonpricing.PerPersonPricingService;
import com.ideas.tetris.pacman.services.pricingconfiguration.dto.PricingConfigurationDTO;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.*;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.product.ProductType;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.pacman.services.sas.entity.SASMacrosParameters;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.services.tax.service.TaxService;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompetitors;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.externalsystem.ReservationSystem;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.log4j.Logger;
import org.hibernate.Hibernate;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.CENTRAL_RMS_AVAILABLE;
import static com.ideas.tetris.pacman.services.sas.entity.SASMacrosParameters.RM_FLOOR_CEILING_PARAMS;
import static com.ideas.tetris.pacman.services.sas.entity.SASMacrosParameters.createParameterInsertQuery;
import static com.ideas.tetris.pacman.util.Streams.stream;
import static com.ideas.tetris.platform.common.time.JavaLocalDateUtils.toJavaLocalDate;
import static com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig.useOptimizedStoredProcedurePricing;
import static java.util.Objects.isNull;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;

@Slf4j
@Component
@Transactional
public class PricingConfigurationService {

    public static final String QUERY_TO_COPY_DRAFT_CONFIGURATIONS_TO_CURRENT_CONFIGURATIONS =
            " insert into [dbo].[CP_Cfg_Base_AT] (product_Id, property_Id, Accom_Type_ID, Start_Date, End_Date, Sunday_Floor_Rate, Sunday_Ceil_Rate, Monday_Floor_Rate,Monday_Ceil_Rate, Tuesday_Floor_Rate, Tuesday_Ceil_Rate, " +
            "   Wednesday_Floor_Rate, Wednesday_Ceil_Rate, Thursday_Floor_Rate, Thursday_Ceil_Rate, Friday_Floor_Rate, Friday_Ceil_Rate, Saturday_Floor_Rate, Saturday_Ceil_Rate, " +
            "   Status_ID, Created_by_User_ID, Last_Updated_by_User_ID, Season_Name, Sunday_Floor_Rate_w_Tax, Sunday_Ceil_Rate_w_Tax, Monday_Floor_Rate_w_Tax, Monday_Ceil_Rate_w_Tax, " +
            "   Tuesday_Floor_Rate_w_Tax, Tuesday_Ceil_Rate_w_Tax, Wednesday_Floor_Rate_w_Tax, Wednesday_Ceil_Rate_w_Tax,  Thursday_Floor_Rate_w_Tax, Thursday_Ceil_Rate_w_Tax, Friday_Floor_Rate_w_Tax, Friday_Ceil_Rate_w_Tax, Saturday_Floor_Rate_w_Tax, Saturday_Ceil_Rate_w_Tax) " +
            "   select product_Id, property_Id, Accom_Type_ID, Start_Date, End_Date, Sunday_Floor_Rate, Sunday_Ceil_Rate, Monday_Floor_Rate,Monday_Ceil_Rate, Tuesday_Floor_Rate, Tuesday_Ceil_Rate, " +
            "   Wednesday_Floor_Rate, Wednesday_Ceil_Rate, Thursday_Floor_Rate, Thursday_Ceil_Rate, Friday_Floor_Rate, Friday_Ceil_Rate, Saturday_Floor_Rate, Saturday_Ceil_Rate, " +
            "   Status_ID, Created_by_User_ID, Last_Updated_by_User_ID, Season_Name, Sunday_Floor_Rate_w_Tax, Sunday_Ceil_Rate_w_Tax, Monday_Floor_Rate_w_Tax, Monday_Ceil_Rate_w_Tax, " +
            "   Tuesday_Floor_Rate_w_Tax, Tuesday_Ceil_Rate_w_Tax, Wednesday_Floor_Rate_w_Tax, Wednesday_Ceil_Rate_w_Tax,  Thursday_Floor_Rate_w_Tax, Thursday_Ceil_Rate_w_Tax, Friday_Floor_Rate_w_Tax, Friday_Ceil_Rate_w_Tax, Saturday_Floor_Rate_w_Tax, Saturday_Ceil_Rate_w_Tax " +
            "   from [dbo].[CP_Cfg_Base_AT_Draft] where product_Id = :productId ;" +
            "delete from [dbo].[CP_Cfg_Base_AT_Draft] where product_Id = :productId ;";
    private static final String DELETE_CEIL_FLOOR = " delete from [dbo].[CP_Cfg_Base_AT] where Product_ID = :productId";
    public static final String DELETE_FLOOR_CEILING_DRAFT_VALUES_FOR_ACCOM_CLASS = "delete draft from CP_Cfg_Base_AT_Draft as draft inner join accom_type as accomType " +
            " on draft.accom_type_id = accomType.accom_type_id " +
            "   where accomType.accom_class_id = :accomClassId ";

    public static final String DELETE_GP_BASE_ACCOMTYPE_WITH_PRICE_EXCLUDED = "DELETE CCBA FROM grp_prc_cfg_base_at CCBA  INNER JOIN cp_cfg_ac PAC " +
            "  ON PAC.accom_type_id = CCBA.accom_type_id AND Product_ID = :productId";
    private static final String PROPERTY_ID = "propertyId";
    private static final String PRODUCT_ID = "productId";
    private static final String ACCOM_CLASS_ID = "accomClassId";
    private static final String ACCOM_TYPES = "accomTypes";
    private static final String OCCUPANCY_TYPE_ID = "occupancyTypeID";
    private static final String START_DATE = "startDate";
    private static final String END_DATE = "endDate";
    private static final String DATE = "date";
    private static final String OCCUPANCY_TYPE = "occupancyType";
    private static final String USE_OVERRIDES = "useOverrides";
    private static final String REMOVED_OVERRIDES = "removedOverrides";
    private static final String UPDATE_FC_DEFAULT_MACRO_VALUE = "UPDATE Sas_Macros_Parameters " +
            " SET Parameter_Value = :paramValue " +
            " WHERE Macro_Name = :macroName AND Parameter_Name = :paramName";
    private static final String PARAM_NAME = "paramName";
    private static final String PARAM_VALUE = "paramValue";
    private static final String MACRO_NAME = "macroName";
    private static final String MACRO_RM_CONF_FLOOR_CEIL = "rm_conf_floor_ceil";

    private static final String ACCOM_TYPE_ID = "accomTypeId";

    private static final String ID = "id";
    @Autowired
    PacmanConfigParamsService configParamsService;
    @Autowired
    GroupPricingConfigurationService groupPricingConfigurationService;
    @Autowired
    SyncEventAggregatorService syncEventAggregatorService;
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;
    @Autowired
	private AbstractMultiPropertyCrudService multiPropertyCrudService;
    @Autowired
	private InvalidateOverridesService invalidateOverridesService;
    @Autowired
	private IndependentProductsService independentProductsService;

    @Autowired
	private TaxService taxService;

    @Autowired
	private AccomTypeSupplementService accomTypeSupplementService;

    @Autowired
	private PerPersonPricingService perPersonPricingService;

    @Autowired
	private PrettyPricingService prettyPricingService;

    @Autowired
	private CPTaxInclusiveMigrationService taxInclusiveMigrationService;

    @Autowired
	private DateService dateService;

    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	private CrudService globalCrudService;

    @Autowired
	private CurrencyExchangeService currencyExchangeService;

    @Autowired
	private AgileRatesConfigurationService agileRatesConfigurationService;

    @Autowired
	private JobServiceLocal jobService;

    @Autowired
	private SyncCentralRmsService syncCentralRmsService;

    @Autowired
	private CurrencyService currencyService;

    @Autowired
    PricingConfigurationLTBDEService pricingConfigurationLTBDEService;

    @Autowired
    private CentralRmsService centralRmsService;

    private static final Logger LOGGER = Logger.getLogger(PricingConfigurationService.class);

    public CPDecisionContext getCPDecisionContext(LocalDate date) {
        return getCPDecisionContext(date, date);
    }

    public CPDecisionContext getCPDecisionContext(LocalDate startDate, LocalDate endDate) {
        return getCPDecisionContext(startDate, endDate, true);
    }

    public CPDecisionContext getCPDecisionContext(LocalDate startDate, LocalDate endDate, boolean useOverrides) {
        // Get the base occupancy type
        OccupancyType baseOccupancyType = getBaseOccupancyType();
        return getCPDecisionContext(startDate, endDate, useOverrides, baseOccupancyType);
    }

    public CPDecisionContext getCPDecisionContext(LocalDate startDate, LocalDate endDate, boolean useOverrides, OccupancyType baseOccupancyType) {
        // Get the base occupancy type
        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates =
                findCeilingAndFloorConfigForDates(startDate, endDate, useOverrides);
        return getCPDecisionContext(startDate, endDate, baseOccupancyType, ceilingAndFloorConfigForDates);
    }

    public CPDecisionContext getCPDecisionContext(LocalDate startDate, LocalDate endDate, boolean useOverrides, String removeOverridesDates) {
        // Get the base occupancy type
        OccupancyType baseOccupancyType = getBaseOccupancyType();
        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigForDates =
                findCeilingAndFloorConfigForDates(startDate, endDate, useOverrides, removeOverridesDates);
        return getCPDecisionContext(startDate, endDate, baseOccupancyType, ceilingAndFloorConfigForDates);
    }


    public List<CPConfigMergedCeilingAndFloor> getCeilingAndFloorConfig(LocalDate startDate, LocalDate
            endDate, OccupancyType baseOccupancy, boolean useOverrides, String removeOverridesDates) {
        // Query for all CPConfigMergedCeilingAndFloor between start/end dates
        return tenantCrudService.findByNamedQuery(CPConfigMergedCeilingAndFloor.FIND_CEILING_AND_FLOOR_FOR_PROPERTY_BY_START_DATE_AND_END_DATE_WITH_TAX_WHAT_IF,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and(START_DATE, startDate)
                        .and(END_DATE, endDate)
                        .and(OCCUPANCY_TYPE, baseOccupancy.getId())
                        .and(USE_OVERRIDES, useOverrides ? 1 : 0)
                        .and(REMOVED_OVERRIDES, removeOverridesDates).parameters());
    }

    public Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> findCeilingAndFloorConfigForDates
            (LocalDate startDate, LocalDate endDate, boolean useOverrides, String removeOverridesDates) {
        List<CPConfigMergedCeilingAndFloor> defaultConfig = getCeilingAndFloorConfig(startDate, endDate, getBaseOccupancyType(), useOverrides, removeOverridesDates);

        // Create a Map based on the ID
        return defaultConfig.stream().collect(Collectors.toMap(CPConfigMergedCeilingAndFloor::getId, Function.identity(), (id, ceilingAndFloor) -> id));
    }

    public CPDecisionContext getCPDecisionContext(LocalDate startDate, LocalDate endDate,
                                                  OccupancyType baseOccupancyType,
                                                  Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigs) {

        if (SystemConfig.isMultiThreadingEnabledOnPricingConfigService()) {
            return getCPDecisionContextUsingThreads(startDate, endDate,
                    baseOccupancyType,
                    ceilingAndFloorConfigs);
        }

        CPConfiguration cpConfiguration = tenantCrudService.findOne(CPConfiguration.class);

        // Is base room type only
        boolean isBaseRoomTypeOnlyEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value());

        // Get the PricingRules
        Map<Integer, PricingRule> pricingRules = getPrettyPricingRules();

        // Get the Tax
        Tax tax = taxService.findTax();

        // Get the offsets for the start/end dates
        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsets = findOffsetsForDates(startDate, endDate);

        // Get the supplements for the start/end dates
        Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> supplements = accomTypeSupplementService.getSupplementValueMap(startDate, endDate);

        // Get the minimum increment
        List<PricingAccomClass> pricingAccomClasses = getPricingAccomClasses();

        // Get the OccupancyBucketEntites
        List<OccupantBucketEntity> occupantBucketEntities = perPersonPricingService.getOccupantBuckets();

        // Get the maximum occupants allowed in each room type
        Map<Integer, MaximumOccupantsEntity> maximumOccupantsEntities = new HashMap<>();
        List<MaximumOccupantsEntity> maximumOccupantsEntitiesList = getMaximumEntitiesList();
        if (CollectionUtils.isNotEmpty(maximumOccupantsEntitiesList)) {
            maximumOccupantsEntities = maximumOccupantsEntitiesList.stream().collect(Collectors.toMap(MaximumOccupantsEntity::getAccomTypeId, Function.identity(), (accomTypeId, maximumOccupantsEntity) -> accomTypeId));
        }

        // If the property is per-person - get the max adult/childQuantity OccupancyType
        OccupancyType maxAdultOccupancyType = null;
        OccupancyType maxChildQuantityOccupancyType = null;
        boolean isPerPersonPricingEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED.value());
        if (isPerPersonPricingEnabled) {
            maxAdultOccupancyType = occupantBucketEntities.stream().map(OccupantBucketEntity::getOccupancyType).filter(OccupancyType::isAdultBucket).findFirst().orElse(null);
            maxChildQuantityOccupancyType = occupantBucketEntities.stream().map(OccupantBucketEntity::getOccupancyType).filter(OccupancyType::isChildQuantityBucket).findFirst().orElse(null);
        }

        // Get the max child age bucket occupancy type
        OccupancyType maxChildAgeBucketOccupancyType = null;
        boolean isChildAgeBucketsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.CP_CHILD_AGE_BUCKETS_ENABLED.value());
        if (isChildAgeBucketsEnabled) {
            maxChildAgeBucketOccupancyType = findMaxChildAgeBucketOccupancyType(occupantBucketEntities);
        }

        boolean isChildAgeBucketPackagesEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.CHILD_AGE_BUCKET_PACKAGES_ENABLED.value());
        // Handle the Product-related components
        Map<Product, List<AgileRatesPackage>> productPackages = null;
        Map<ProductRateOffsetNonDTAKey, List<ProductRateOffsetByDate>> productRateOffsets = null;
        boolean isAgileRatesEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED.value());
        boolean isIndependentProductsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
        Map<Product, List<Product>> products = findAllActiveProductsInHierarchyOrder(isAgileRatesEnabled, isIndependentProductsEnabled);
        LocalDate caughtUpDate = dateService.getCaughtUpLocalDate();
        if (isAgileRatesEnabled) {
            productPackages = groupPackagesByProduct(getActiveProductPackages());
            productRateOffsets = findAllProductRateOffsetsForActiveProducts(startDate, endDate).stream().collect(Collectors.groupingBy(ProductRateOffsetByDate::nonDTAKey));
        }

        boolean isHiltonSendAdjustmentEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED);
        boolean isHiltonUpdateValuesSentForExtraAdultExtraChild = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.HILTON_OPTION_TO_UPDATE_VALUES_SENT_FOR_EXTRA_ADULT_EXTRA_CHILD);
        boolean isConsortiaFreeNightEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_CONSORTIA_FREE_NIGHT_ENABLED);
        boolean isUseBaseRTPriceForNegativeOffset = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_BASE_RT_PRICE_FOR_NEGATIVE_OFFSET);
        boolean isSupplementPercentEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT);
        boolean isProductFloorAdjustmentForNonBaseRT= configParamsService.getBooleanParameterValue(PreProductionConfigParamName.PRODUCT_FLOOR_ADJUSTMENT_FOR_NON_BASE_RT);
        // Return the CPDecisionContext

        return new CPDecisionContext(
                cpConfiguration,
                isBaseRoomTypeOnlyEnabled,
                baseOccupancyType,
                maxAdultOccupancyType,
                maxChildQuantityOccupancyType,
                maxChildAgeBucketOccupancyType,
                maximumOccupantsEntities,
                tax,
                pricingRules,
                pricingAccomClasses,
                ceilingAndFloorConfigs,
                offsets,
                supplements,
                products,
                caughtUpDate,
                productPackages,
                productRateOffsets,
                isHiltonSendAdjustmentEnabled,
                isHiltonUpdateValuesSentForExtraAdultExtraChild,
                isPerPersonPricingEnabled,
                isChildAgeBucketsEnabled,
                isChildAgeBucketPackagesEnabled,
                startDate,
                endDate,
                isConsortiaFreeNightEnabled,
                isUseBaseRTPriceForNegativeOffset,
                isSupplementPercentEnabled,
                isProductFloorAdjustmentForNonBaseRT);
    }

    public List<ProductPackage> getActiveProductPackages() {
        return tenantCrudService.findByNamedQuery(ProductPackage.BY_ACTIVE_PRODUCTS);
    }

    public CPDecisionContext getCPDecisionContext(LocalDate startDate, LocalDate endDate, boolean useOverrides,
                                                  CPConfiguration cpConfiguration, OccupancyType baseOccupancyType,
                                                  boolean isBaseRoomTypeOnlyEnabled, Map<Integer, PricingRule> pricingRules,
                                                  Tax tax, List<PricingAccomClass> pricingAccomClasses,
                                                  List<OccupantBucketEntity> occupantBucketEntities,
                                                  List<MaximumOccupantsEntity> maximumOccupantsEntitiesList,
                                                  boolean isPerPersonPricingEnabled, boolean isChildAgeBucketsEnabled,
                                                  boolean isChildAgeBucketPackagesEnabled,
                                                  boolean isAgileRatesEnabled, LocalDate caughtUpDate,
                                                  List<ProductPackage> ungroupedProductPackages) {
        // Get a Map of the Floor/Ceiling Values for the Date Range
        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigs = findCeilingAndFloorConfigForDates(startDate, endDate, useOverrides);

        // Get the offsets for the start/end dates
        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsets = findOffsetsForDates(startDate, endDate);

        // Get the supplements for the start/end dates
        Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> supplements = accomTypeSupplementService.getSupplementValueMap(startDate, endDate, isBaseRoomTypeOnlyEnabled);

        // Get the maximum occupants allowed in each room type
        Map<Integer, MaximumOccupantsEntity> maximumOccupantsEntities = new HashMap<>();

        if (CollectionUtils.isNotEmpty(maximumOccupantsEntitiesList)) {
            maximumOccupantsEntities = maximumOccupantsEntitiesList.stream().collect(Collectors.toMap(MaximumOccupantsEntity::getAccomTypeId, Function.identity(), (accomTypeId, maximumOccupantsEntity) -> accomTypeId));
        }

        // If the property is per-person - get the max adult/childQuantity OccupancyType
        OccupancyType maxAdultOccupancyType = null;
        OccupancyType maxChildQuantityOccupancyType = null;
        if (isPerPersonPricingEnabled) {
            maxAdultOccupancyType = occupantBucketEntities.stream().map(OccupantBucketEntity::getOccupancyType).filter(OccupancyType::isAdultBucket).findFirst().orElse(null);
            maxChildQuantityOccupancyType = occupantBucketEntities.stream().map(OccupantBucketEntity::getOccupancyType).filter(OccupancyType::isChildQuantityBucket).findFirst().orElse(null);
        }

        // Get the max child age bucket occupancy type
        OccupancyType maxChildAgeBucketOccupancyType = null;
        if (isChildAgeBucketsEnabled) {
            maxChildAgeBucketOccupancyType = findMaxChildAgeBucketOccupancyType(occupantBucketEntities);
        }

        // Handle the Product-related components
        Map<Product, List<AgileRatesPackage>> productPackages = null;
        Map<ProductRateOffsetNonDTAKey, List<ProductRateOffsetByDate>> productRateOffsets = null;
        boolean isIndependentProductsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
        Map<Product, List<Product>> products = findAllActiveProductsInHierarchyOrder(true, isIndependentProductsEnabled);
        if (isAgileRatesEnabled) {
            productPackages = groupPackagesByProduct(ungroupedProductPackages);
            productRateOffsets = findAllProductRateOffsetsForActiveProducts(startDate, endDate).stream().collect(Collectors.groupingBy(ProductRateOffsetByDate::nonDTAKey));
        }

        boolean isSendAdjustmentEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED);
        boolean isHiltonUpdateValuesSentForExtraAdultExtraChild = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.HILTON_OPTION_TO_UPDATE_VALUES_SENT_FOR_EXTRA_ADULT_EXTRA_CHILD);
        boolean isConsortiaFreeNightEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_CONSORTIA_FREE_NIGHT_ENABLED);
        boolean isUseBaseRTPriceForNegativeOffset = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_BASE_RT_PRICE_FOR_NEGATIVE_OFFSET);
        boolean isSupplementPercentEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT);
        boolean isProductFloorAdjustmentForNonBaseRT = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.PRODUCT_FLOOR_ADJUSTMENT_FOR_NON_BASE_RT);
        // Return the CPDecisionContext

        return new CPDecisionContext(
                cpConfiguration,
                isBaseRoomTypeOnlyEnabled,
                baseOccupancyType,
                maxAdultOccupancyType,
                maxChildQuantityOccupancyType,
                maxChildAgeBucketOccupancyType,
                maximumOccupantsEntities,
                tax,
                pricingRules,
                pricingAccomClasses,
                ceilingAndFloorConfigs,
                offsets,
                supplements,
                products,
                caughtUpDate,
                productPackages,
                productRateOffsets,
                isSendAdjustmentEnabled,
                isHiltonUpdateValuesSentForExtraAdultExtraChild,
                isPerPersonPricingEnabled,
                isChildAgeBucketsEnabled,
                isChildAgeBucketPackagesEnabled,
                startDate,
                endDate,
                isConsortiaFreeNightEnabled,
                isUseBaseRTPriceForNegativeOffset,
                isSupplementPercentEnabled,
                isProductFloorAdjustmentForNonBaseRT);
    }

    public List<MaximumOccupantsEntity> getMaximumEntitiesList() {
        return tenantCrudService.findByNamedQuery(MaximumOccupantsEntity.ALL_ACTIVE);
    }

    private CPDecisionContext getCPDecisionContextUsingThreads(LocalDate startDate, LocalDate endDate,
                                                               OccupancyType baseOccupancyType,
                                                               Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloorConfigs) {
        Map<String, Supplier<Object>> requestMap = new HashMap<>();
        LocalDate caughtUpDate = dateService.getCaughtUpLocalDate();
        boolean isAgileRatesEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED.value());
        boolean isIndependentProductsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
        requestMap.put("products", () -> findAllActiveProductsInHierarchyOrder(isAgileRatesEnabled, isIndependentProductsEnabled));
        if (isAgileRatesEnabled) {
            requestMap.put("productPackages", () -> groupPackagesByProduct(getActiveProductPackages()));
            requestMap.put("productRateOffsets", () -> findAllProductRateOffsetsForActiveProducts(startDate, endDate).stream().collect(Collectors.groupingBy(ProductRateOffsetByDate::nonDTAKey)));
        }

        requestMap.put("cpConfiguration", () -> tenantCrudService.findOne(CPConfiguration.class));
        requestMap.put("pricingRules", () -> prettyPricingService.getPricingRules());
        requestMap.put("tax", () -> taxService.findTax());
        requestMap.put("offsets", () -> findOffsetsForDates(startDate, endDate));
        requestMap.put("supplements", () -> accomTypeSupplementService.getSupplementValueMap(startDate, endDate));
        requestMap.put("pricingAccomClasses", () -> getPricingAccomClasses());
        requestMap.put("occupantBucketEntities", () -> perPersonPricingService.getOccupantBuckets());
        List<MaximumOccupantsEntity> maximumOccupantsEntitiesList = getMaximumEntitiesList();
        if (CollectionUtils.isNotEmpty(maximumOccupantsEntitiesList)) {
            requestMap.put("maximumOccupantsEntities", () -> maximumOccupantsEntitiesList.stream().collect(
                    Collectors.toMap(MaximumOccupantsEntity::getAccomTypeId, Function.identity(),
                            (accomTypeId, maximumOccupantsEntity) -> accomTypeId)));
        }


        TetrisDataServiceThreadPool tetrisDataThreadPool = new TetrisDataServiceThreadPool(
                TetrisDataServiceThreadPool.getThreadPoolExecutor(requestMap.size(), "cpdecctx"));
        Map<String, Object> results = tetrisDataThreadPool.executeSupplierMap(requestMap);

        CPConfiguration cpConfiguration = results.get("cpConfiguration") != null ? (CPConfiguration) results.get("cpConfiguration") : null;
        Map<Integer, PricingRule> pricingRules = results.get("pricingRules") != null ? (Map<Integer, PricingRule>) results.get("pricingRules") : null;
        Tax tax = results.get("tax") != null ? (Tax) results.get("tax") : null;
        Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> offsets = results.get("offsets") != null ? (Map<CPConfigMergedOffsetPK, CPConfigMergedOffset>) results.get("offsets") : null;
        Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> supplements = results.get("supplements") != null ? (Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue>) results.get("supplements") : null;
        List<PricingAccomClass> pricingAccomClasses = results.get("pricingAccomClasses") != null ? (List<PricingAccomClass>) results.get("pricingAccomClasses") : null;
        List<OccupantBucketEntity> occupantBucketEntities = results.get("occupantBucketEntities") != null ? (List<OccupantBucketEntity>) results.get("occupantBucketEntities") : null;
        Map<Integer, MaximumOccupantsEntity> maximumOccupantsEntities = results.get("maximumOccupantsEntities") != null ? (Map<Integer, MaximumOccupantsEntity>) results.get("maximumOccupantsEntities") : null;
        Map<Product, List<Product>> products = results.get("products") != null ? (Map<Product, List<Product>>) results.get("products") : null;
        Map<Product, List<AgileRatesPackage>> productPackages = results.get("productPackages") != null ? (Map<Product, List<AgileRatesPackage>>) results.get("productPackages") : null;
        Map<ProductRateOffsetNonDTAKey, List<ProductRateOffsetByDate>> productRateOffsets = results.get("productRateOffsets") != null ? (Map<ProductRateOffsetNonDTAKey, List<ProductRateOffsetByDate>>) results.get("productRateOffsets") : null;


        boolean isBaseRoomTypeOnlyEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value());

        // If the property is per-person - get the max adult/childQuantity OccupancyType
        OccupancyType maxAdultOccupancyType = null;
        OccupancyType maxChildQuantityOccupancyType = null;
        boolean isPerPersonPricingEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED.value());
        if (isPerPersonPricingEnabled && occupantBucketEntities != null) {
            maxAdultOccupancyType = occupantBucketEntities.stream().map(OccupantBucketEntity::getOccupancyType).filter(OccupancyType::isAdultBucket).findFirst().orElse(null);
            maxChildQuantityOccupancyType = occupantBucketEntities.stream().map(OccupantBucketEntity::getOccupancyType).filter(OccupancyType::isChildQuantityBucket).findFirst().orElse(null);
        }

        // Get the max child age bucket occupancy type
        OccupancyType maxChildAgeBucketOccupancyType = null;
        boolean isChildAgeBucketsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.CP_CHILD_AGE_BUCKETS_ENABLED.value());
        if (isChildAgeBucketsEnabled && occupantBucketEntities != null) {
            maxChildAgeBucketOccupancyType = findMaxChildAgeBucketOccupancyType(occupantBucketEntities);
        }

        boolean isHiltonSendAdjustmentEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED);
        boolean isHiltonUpdateValuesSentForExtraAdultExtraChild = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.HILTON_OPTION_TO_UPDATE_VALUES_SENT_FOR_EXTRA_ADULT_EXTRA_CHILD);
        boolean isChildAgeBucketPackagesEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.CHILD_AGE_BUCKET_PACKAGES_ENABLED.value());
        boolean isConsortiaFreeNightEnabled = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_CONSORTIA_FREE_NIGHT_ENABLED);
        boolean isUseBaseRTPriceForNegativeOffset = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_BASE_RT_PRICE_FOR_NEGATIVE_OFFSET);
        boolean isSupplementPercentEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT);
        boolean isProductFloorAdjustmentForNonBaseRT = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.PRODUCT_FLOOR_ADJUSTMENT_FOR_NON_BASE_RT);

        return new CPDecisionContext(
                cpConfiguration,
                isBaseRoomTypeOnlyEnabled,
                baseOccupancyType,
                maxAdultOccupancyType,
                maxChildQuantityOccupancyType,
                maxChildAgeBucketOccupancyType,
                maximumOccupantsEntities,
                tax,
                pricingRules,
                pricingAccomClasses,
                ceilingAndFloorConfigs,
                offsets,
                supplements,
                products,
                caughtUpDate,
                productPackages,
                productRateOffsets,
                isHiltonSendAdjustmentEnabled,
                isHiltonUpdateValuesSentForExtraAdultExtraChild,
                isPerPersonPricingEnabled,
                isChildAgeBucketsEnabled,
                isChildAgeBucketPackagesEnabled,
                startDate,
                endDate,
                isConsortiaFreeNightEnabled,
                isUseBaseRTPriceForNegativeOffset,
                isSupplementPercentEnabled,
                isProductFloorAdjustmentForNonBaseRT);

    }

    private List<ProductRateOffsetByDate> findAllProductRateOffsetsForActiveProducts(LocalDate startDate, LocalDate endDate) {
        if(SystemConfig.usePerformanceImprovementChangesOnPricingScreen()) {
            return tenantCrudService.findByNamedQuery(ProductRateOffsetByDate.FIND_OFFSETS_BY_START_DATE_AND_END_DATE_USING_PROC, ProductRateOffsetByDate.params(startDate, endDate));
        } else {
            return tenantCrudService.findByNamedQuery(ProductRateOffsetByDate.FIND_OFFSETS_BY_START_DATE_AND_END_DATE, ProductRateOffsetByDate.params(startDate, endDate));
        }
    }

    private OccupancyType findMaxChildAgeBucketOccupancyType(List<OccupantBucketEntity> occupantBucketEntities) {
        OccupancyType maxChildAgeBucketOccupancyType = null;
        for (OccupantBucketEntity bucket : occupantBucketEntities) {
            OccupancyType bucketOccupancyType = bucket.getOccupancyType();

            if (maxChildAgeBucketOccupancyType == null) {
                maxChildAgeBucketOccupancyType = bucketOccupancyType;
            } else {
                int maxChildAgeBucketIndex = OccupancyType.getChildBuckets().indexOf(maxChildAgeBucketOccupancyType);
                int bucketTypeIndex = OccupancyType.getChildBuckets().indexOf(bucketOccupancyType);

                if (bucketTypeIndex > maxChildAgeBucketIndex) {
                    maxChildAgeBucketOccupancyType = bucketOccupancyType;
                }
            }
        }

        return maxChildAgeBucketOccupancyType;
    }

    private Map<Product, List<AgileRatesPackage>> groupPackagesByProduct(List<ProductPackage> packages) {
        Map<Product, List<AgileRatesPackage>> packagesGroupedByProduct = new HashMap<>();
        if (CollectionUtils.isNotEmpty(packages)) {
            packages.forEach(productPackage -> {
                List<AgileRatesPackage> agileRatesPackages = packagesGroupedByProduct.computeIfAbsent(productPackage.getProduct(), v -> new ArrayList<>());
                agileRatesPackages.add(productPackage.getAgileRatesPackage());
            });
        }

        return packagesGroupedByProduct;
    }

    public Map<Product, List<Product>> findAllActiveProductsInHierarchyOrder(boolean agileRatesEnabled, boolean independentProductsEnabled) {
        boolean smallGroupProductsEnabled = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.SMALL_GROUP_PRODUCT_ENABLED);

        if (independentProductsEnabled && smallGroupProductsEnabled) {
            final List<Product> activeAgileRatesProducts = tenantCrudService.findByNamedQuery(
                    Product.GET_SYSTEM_DEFAULT_AGILE_RATES_INDEPENDENT_SMALL_GROUP_PRODUCTS_BY_STATUS,
                    QueryParameter.with("status", TenantStatusEnum.ACTIVE).parameters());
            return sortProductsMapInHierarchicalOrder(activeAgileRatesProducts);
        } else if (independentProductsEnabled) {
            final List<Product> activeAgileRatesProducts = tenantCrudService.findByNamedQuery(
                    Product.GET_SYSTEM_DEFAULT_AGILE_RATES_INDEPENDENT_PRODUCTS_BY_STATUS,
                    QueryParameter.with("status", TenantStatusEnum.ACTIVE).parameters());
            return sortProductsMapInHierarchicalOrder(activeAgileRatesProducts);
        } else if (smallGroupProductsEnabled && agileRatesEnabled) {
            final List<Product> activeAgileRatesProducts = tenantCrudService.findByNamedQuery(
                    Product.GET_SYSTEM_DEFAULT_AGILE_RATES_SMALL_GROUP_PRODUCTS_BY_STATUS,
                    QueryParameter.with("status", TenantStatusEnum.ACTIVE).parameters());
            return sortProductsMapInHierarchicalOrder(activeAgileRatesProducts);
        } else if (agileRatesEnabled) {
            final List<Product> activeAgileRatesProducts = tenantCrudService.findByNamedQuery(
                    Product.GET_ACTIVE_SYSTEM_DEFAULT_OR_AGILE_RATES,
                    QueryParameter.with("status", TenantStatusEnum.ACTIVE).parameters());
            return sortProductsMapInHierarchicalOrder(activeAgileRatesProducts);
        } else {
            List<Product> products = tenantCrudService.findByNamedQuery(Product.GET_SYSTEM_DEFAULT);
            Map<Product, List<Product>> barProductMap = new HashMap<>();
            barProductMap.put(products.get(0), products);
            return barProductMap;
        }
    }

    private Map<Integer, List<Product>> createDependencyMap(Map<Integer, Product> productMap) {
        final Map<Integer, List<Product>> dependencyMap = new HashMap<>();
        productMap.forEach((id, product) -> {
            final List<Product> children = productMap.values().stream()
                    .filter(p -> id.equals(p.getDependentProductId()))
                    .collect(toList());
            dependencyMap.put(id, children);
        });
        return dependencyMap;
    }

    public Map<Product, List<Product>> sortProductsMapInHierarchicalOrder(List<Product> products) {
        Map<Integer, Product> productIdProductMap = createProductIdProductMap(products);
        return sortDependencyMapFromRootProductsMap(productIdProductMap, createDependencyMap(productIdProductMap));
    }

    private Map<Integer, Product> createProductIdProductMap(List<Product> products) {
        return products.stream()
                .collect(Collectors.toMap(Product::getId, Function.identity()));
    }

    private Map<Product, List<Product>> sortDependencyMapFromRootProductsMap(Map<Integer, Product> productMap, Map<Integer, List<Product>> dependencyMap) {
        Map<Product, List<Product>> productsMap = new HashMap<>();
        List<Product> primaryProducts = productMap.values().stream()
                .filter(product -> product.getDependentProductId() == null)
                .collect(toList());
        primaryProducts.forEach(primaryProduct -> {
            final ArrayList<Product> sorted = new ArrayList<>();
            List<Product> allProductsFromTree = new ArrayList<>();
            allProductsFromTree.add(primaryProduct);
            sortByDependency(primaryProduct, sorted, dependencyMap);
            productsMap.put(primaryProduct, sorted);
        });

        return productsMap;
    }

    private List<Product> sortByDependency(Product parent, ArrayList<Product> sorted, Map<Integer, List<Product>> dependencyMap) {
        sorted.add(parent);
        final List<Product> children = new ArrayList<>(dependencyMap.get(parent.getId()));
        children.forEach(child -> {
            sortByDependency(child, sorted, dependencyMap);
        });
        return sorted;
    }

    @SuppressWarnings("unchecked")
    public List<PricingAccomClass> getPricingAccomClasses() {
        return getPricingAccomClasses(PacmanWorkContextHelper.getPropertyId());
    }

    public List<AccomType> getActiveAccomTypes(){
        return tenantCrudService.findByNamedQuery(AccomType.ALL_ACTIVE,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public List<PricingAccomClass> getPricingAccomClasses(Integer propertyId) {
        List<PricingAccomClass> pricingAccomClasses = (List<PricingAccomClass>) multiPropertyCrudService.findByNamedQueryForSingleProperty(
                propertyId,
                PricingAccomClass.FIND_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, propertyId).parameters());

        return CollectionUtils.isNotEmpty(pricingAccomClasses) ? pricingAccomClasses : new ArrayList<>();
    }

    public List<PricingAccomClass> getPricingAccomClassesForProduct(Product product) {
        List<PricingAccomClass> allPricingAccomClasses = getPricingAccomClasses();
        List<AccomType> productAccomTypesForProduct = agileRatesConfigurationService
                .findProductRoomTypeByProducts(new HashSet<>(Collections.singletonList(product)))
                .stream().map(ProductAccomType::getAccomType)
                .collect(toList());

        allPricingAccomClasses.removeIf(pac -> !productAccomTypesForProduct.contains(pac.getAccomType()));
        return allPricingAccomClasses;
    }

    public List<PricingAccomClass> getPricingAccomClassesForProductRoomType(Product product) {
        List<PricingAccomClass> allPricingAccomClasses = getPricingAccomClasses();
        List<AccomType> productAccomTypesForProduct = agileRatesConfigurationService
                .findProductRoomTypeByProducts(new HashSet<>(Collections.singletonList(product)))
                .stream().map(ProductAccomType::getAccomType)
                .collect(toList());
        allPricingAccomClasses.removeIf(pac -> !CollectionUtils.containsAny(pac.getAccomClass().getAccomTypes(), productAccomTypesForProduct));
        return allPricingAccomClasses;
    }

    public List<PricingAccomClass> getPriceExcludedPricingAccomClasses() {
        return getPriceExcludedPricingAccomClasses(PacmanWorkContextHelper.getPropertyId());
    }

    private List<PricingAccomClass> getPriceExcludedPricingAccomClasses(Integer propertyId) {
        List<PricingAccomClass> pricingAccomClasses = (List<PricingAccomClass>) multiPropertyCrudService.findByNamedQueryForSingleProperty(
                propertyId,
                PricingAccomClass.FIND_BY_PROPERTY_ID_AND_PRICE_EXCLUDED,
                QueryParameter.with(PROPERTY_ID, propertyId).parameters());

        return CollectionUtils.isNotEmpty(pricingAccomClasses) ? pricingAccomClasses : new ArrayList<>();
    }

    public Map<String, AccomType> getPricingAccomClassBaseRoomTypeMapping() {
        List<PricingAccomClass> pricingAccomClasses = getPricingAccomClasses();
        return pricingAccomClasses.stream().collect(Collectors.toMap(p -> p.getAccomClass().getName().toUpperCase(), p -> p.getAccomType()));
    }

    public List<PricingAccomClass> getNonExcludedPricingAccomClasses(Integer propertyId) {
        List<PricingAccomClass> pricingAccomClasses = getPricingAccomClasses(propertyId);

        return pricingAccomClasses.stream()
                .filter(pricingAccomClass -> !pricingAccomClass.isPriceExcluded())
                .collect(toList());
    }

    public List<PricingAccomClass> getNonExcludedPricingAccomClassesTransient(Integer propertyId, Product product) {
        List<PricingAccomClass> pricingAccomClasses;
        if (configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED) && product.isIndependentProduct()) {
            pricingAccomClasses = getPricingAccomClassesForProduct(product);
        } else {
            pricingAccomClasses = getPricingAccomClasses(propertyId);
        }

        return pricingAccomClasses.stream()
                .filter(pricingAccomClass -> !pricingAccomClass.isPriceExcluded())
                .collect(toList());
    }

    public boolean isAccomClassPriceExcluded(AccomClass accomClass) {
        List<PricingAccomClass> pricingAccomClasses = getPricingAccomClasses();

        return pricingAccomClasses
                .stream()
                .filter(pricingAccomClass -> pricingAccomClass.getAccomClass().equals(accomClass))
                .anyMatch(PricingAccomClass::isPriceExcluded);
    }

    public void savePricingAccomClasses(List<PricingAccomClass> pricingAccomClasses) {
        // if default room type was changed, or price excluded was changed, then blow away downstream room type configurations, overrides,
        // and seasons for room class
        pricingAccomClasses.stream()
                .filter(pricingAccomClass -> pricingAccomClass.isAccomTypeChanged() || pricingAccomClass.isPriceExcludedChanged())
                .forEach(pricingAccomClass -> {
                    deleteGroupPricingBaseAccomTypeForAccomClass(pricingAccomClass.getAccomClass().getId());
                    deleteTransientPricingBaseAccomTypeForAccomClass(pricingAccomClass.getAccomClass().getId());
                    deletePricingOffsetAccomTypes(pricingAccomClass.getAccomClass().getId());
                    deletePricingOffsetAccomTypesDraft(pricingAccomClass.getAccomClass().getId());
                    deleteTransientPricingBaseAccomTypeDraft(pricingAccomClass.getAccomClass().getId());

                    // Only when we are continuous pricing should we invalidate the overrides
                    if (configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)) {
                        invalidateOverridesService.invalidateCPOverrides(Collections.singletonList(pricingAccomClass.getAccomClass().getId()));
                    }
                });
        ensureSeasonRowsForAlteredAccomClasses(pricingAccomClasses);

        // To avoid the potential for duplicate entry issues, just delete the accommodation classes before save
        deleteAllPricincAccomClasses();
        tenantCrudService.save(pricingAccomClasses);

        syncEventAggregatorService.registerSyncEvent(SyncEvent.PRICING_CONFIG_CHANGED);
    }

    @SuppressWarnings("unchecked")
    public List<PricingBaseAccomType> getAllGroupPricingBaseAccomTypes() {
        return getAllGroupPricingBaseAccomTypes(PacmanWorkContextHelper.getPropertyId());
    }

    public List<PricingBaseAccomType> getAllGroupPricingBaseAccomTypesWithPriceExcluded() {
        return getAllGroupPricingBaseAccomTypesWithPriceExcluded(PacmanWorkContextHelper.getPropertyId());
    }

    public List<PricingBaseAccomType> getAllGroupPricingBaseAccomTypes(Integer propertyId) {
        List<PricingBaseAccomType> pricingBaseAccomTypes = (List<PricingBaseAccomType>) multiPropertyCrudService
                .findByNamedQueryForSingleProperty(propertyId, GroupPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC,
                        QueryParameter.with(PROPERTY_ID, propertyId).parameters());

        return CollectionUtils.isNotEmpty(pricingBaseAccomTypes) ? pricingBaseAccomTypes : new ArrayList<>();
    }

    public List<PricingBaseAccomType> getAllGroupPricingBaseAccomTypesWithPriceExcluded(Integer propertyId) {
        List<PricingBaseAccomType> pricingBaseAccomTypes = (List<PricingBaseAccomType>) multiPropertyCrudService
                .findByNamedQueryForSingleProperty(propertyId, GroupPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_WITH_PRICE_EXCLUDED,
                        QueryParameter.with(PROPERTY_ID, propertyId).parameters());

        return CollectionUtils.isNotEmpty(pricingBaseAccomTypes) ? pricingBaseAccomTypes : new ArrayList<>();
    }

    @SuppressWarnings("unchecked")
    public List<PricingBaseAccomType> getAllTransientPricingBaseAccomTypes() {
        List<PricingBaseAccomType> pricingBaseAccomTypes = tenantCrudService
                .findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC,
                        QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());

        if (CollectionUtils.isNotEmpty(pricingBaseAccomTypes)) {
            for (PricingBaseAccomType pricingBaseAccomType : pricingBaseAccomTypes) {
                Hibernate.initialize(pricingBaseAccomType.getAccomType());
            }
        }

        return pricingBaseAccomTypes;
    }

    public List<PricingBaseAccomType> getAllTransientPricingBaseAccomTypesWithJoinFetch() {
        return tenantCrudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_WITH_JOIN_FETCH,
                        QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public List<PricingBaseAccomType> getAllTransientPricingBaseAccomTypesForAllAccomTypes() {
        List<PricingBaseAccomType> pricingBaseAccomTypes = tenantCrudService
                .findByNamedQuery(TransientPricingBaseAccomType.FIND_ALL_BY_PROPERTY_ID,
                        QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());

        if (CollectionUtils.isNotEmpty(pricingBaseAccomTypes)) {
            for (PricingBaseAccomType pricingBaseAccomType : pricingBaseAccomTypes) {
                Hibernate.initialize(pricingBaseAccomType.getAccomType());
            }
        }
        return pricingBaseAccomTypes;
    }

    public List<PricingBaseAccomType> getAllTransientPricingBaseAccomTypesWithPriceExcluded(Product product) {
        List<PricingBaseAccomType> pricingBaseAccomTypes = tenantCrudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_WITH_PRICE_EXCLUDED,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and(PRODUCT_ID, product.getId()).parameters());

        if (CollectionUtils.isNotEmpty(pricingBaseAccomTypes)) {
            for (PricingBaseAccomType pricingBaseAccomType : pricingBaseAccomTypes) {
                Hibernate.initialize(pricingBaseAccomType.getAccomType());
            }
        }

        return pricingBaseAccomTypes;
    }

    public List<PricingBaseAccomType> getAllTransientPricingBaseAccomTypesWithPriceExcludedForGPSync() {
        return getAllTransientPricingBaseAccomTypesWithPriceExcluded(tenantCrudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT));
    }

    public List<PricingBaseAccomType> getAllPriceExcludedBaseAccomTypes() {
        List<PricingBaseAccomType> pricingBaseAccomTypes = tenantCrudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_IN_NON_PRICEABLE_AC,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());

        if (CollectionUtils.isNotEmpty(pricingBaseAccomTypes)) {
            pricingBaseAccomTypes.forEach(pricingBaseAccomType -> Hibernate.initialize(pricingBaseAccomType.getAccomType()));
        }
        return pricingBaseAccomTypes;
    }

    public List<PricingBaseAccomType> getAllBaseRoomTypes() {
        List<PricingBaseAccomType> pricingBaseAccomTypes = tenantCrudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_ALL_BY_PROPERTY_ID, QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());

        if (CollectionUtils.isNotEmpty(pricingBaseAccomTypes)) {
            for (PricingBaseAccomType pricingBaseAccomType : pricingBaseAccomTypes) {
                Hibernate.initialize(pricingBaseAccomType.getAccomType());
            }
        }

        return pricingBaseAccomTypes;
    }

    private List<AccomType> allBaseRoomTypes() {
        return tenantCrudService.findByNamedQuery(PricingAccomClass.DISTINCT_BASE_ROOM_TYPES_BY_PROPERTY_ID, QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public List<PricingBaseAccomType> getGroupPricingBaseAccomTypeDefaults(Integer propertyId) {
        List<PricingBaseAccomType> groupPricingBaseAccomTypeDefaults = new ArrayList<>();
        List<PricingBaseAccomType> allPricingBaseAccomTypes = getAllGroupPricingBaseAccomTypes(propertyId);

        if (CollectionUtils.isNotEmpty(allPricingBaseAccomTypes)) {
            for (PricingBaseAccomType pricingBaseAccomType : allPricingBaseAccomTypes) {
                if (isDefault(pricingBaseAccomType)) {
                    groupPricingBaseAccomTypeDefaults.add(pricingBaseAccomType);
                }
            }
        }

        return groupPricingBaseAccomTypeDefaults;
    }

    public List<PricingBaseAccomType> getGroupPricingBaseAccomTypeDefaultsWithPriceExcluded(Integer propertyId) {
        List<PricingBaseAccomType> groupPricingBaseAccomTypeDefaults = new ArrayList<>();
        List<PricingBaseAccomType> allPricingBaseAccomTypes = getAllGroupPricingBaseAccomTypesWithPriceExcluded(propertyId);

        if (CollectionUtils.isNotEmpty(allPricingBaseAccomTypes)) {
            for (PricingBaseAccomType pricingBaseAccomType : allPricingBaseAccomTypes) {
                if (isDefault(pricingBaseAccomType)) {
                    groupPricingBaseAccomTypeDefaults.add(pricingBaseAccomType);
                }
            }
        }

        return groupPricingBaseAccomTypeDefaults;
    }

    public List<PricingBaseAccomType> getGroupPricingBaseAccomTypeSeasons() {
        List<PricingBaseAccomType> pricingBaseAccomTypeSeasons = new ArrayList<>();
        List<PricingBaseAccomType> allPricingBaseAccomTypes = getAllGroupPricingBaseAccomTypes();

        if (CollectionUtils.isNotEmpty(allPricingBaseAccomTypes)) {
            for (PricingBaseAccomType pricingBaseAccomType : allPricingBaseAccomTypes) {
                if (!isDefault(pricingBaseAccomType)) {
                    pricingBaseAccomTypeSeasons.add(pricingBaseAccomType);
                }
            }
        }

        return pricingBaseAccomTypeSeasons;
    }

    public List<PricingBaseAccomType> getGroupPricingBaseAccomTypeSeasonsWithPriceExcluded() {
        List<PricingBaseAccomType> pricingBaseAccomTypeSeasons = new ArrayList<>();
        List<PricingBaseAccomType> allPricingBaseAccomTypes = getAllGroupPricingBaseAccomTypesWithPriceExcluded();

        if (CollectionUtils.isNotEmpty(allPricingBaseAccomTypes)) {
            for (PricingBaseAccomType pricingBaseAccomType : allPricingBaseAccomTypes) {
                if (!isDefault(pricingBaseAccomType)) {
                    pricingBaseAccomTypeSeasons.add(pricingBaseAccomType);
                }
            }
        }

        return pricingBaseAccomTypeSeasons;
    }

    public CeilingFloor getNotOverriddenCeilingFloorValues(Integer productId, Integer accomTypeId, LocalDate date) {
        List<TransientPricingBaseAccomType> seasonAndDefaultCeilingFloorValues = tenantCrudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_DEFAULT_AND_SEASON_FOR_PRODUCT_AND_ACCOM_TYPE,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and(PRODUCT_ID, productId)
                        .and(ACCOM_TYPE_ID, accomTypeId)
                        .and(DATE, date)
                        .parameters());

        TransientPricingBaseAccomType transientPricingBaseAccomType = CollectionUtils.size(seasonAndDefaultCeilingFloorValues) == 1 ?
                getTransientPricingBaseAccomTypeWithFilter(seasonAndDefaultCeilingFloorValues, value -> true) :
                getTransientPricingBaseAccomTypeWithFilter(seasonAndDefaultCeilingFloorValues, value -> Objects.nonNull(value.getStartDate()));

        return agileRatesConfigurationService.retrieveCeilingFloorForParticularDate(transientPricingBaseAccomType, date);
    }

    public AccomType getBaseAccomTypeForProvidedAccomType(AccomType accomType) {
        return getAllBaseRoomTypes()
                .stream()
                .map(PricingBaseAccomType::getAccomType)
                .filter(type -> type.getAccomClass().getId().equals(accomType.getAccomClass().getId()))
                .findFirst()
                .get();
    }

    private TransientPricingBaseAccomType getTransientPricingBaseAccomTypeWithFilter(List<TransientPricingBaseAccomType> transientPricingBaseAccomTypes,
                                                                                     Predicate<TransientPricingBaseAccomType> filter) {
        return transientPricingBaseAccomTypes.stream()
                .filter(filter)
                .findFirst()
                .orElse(new TransientPricingBaseAccomType());
    }

    public void saveGroupPricingBaseAccomTypes(List<PricingBaseAccomType> pricingBaseAccomTypes) {
        pricingBaseAccomTypes.forEach(PricingBaseAccomType::removeNonDateRangeDayOfWeekValues);

        applyTax(pricingBaseAccomTypes);

        tenantCrudService.save(pricingBaseAccomTypes);
        taxInclusiveMigrationService.handleGroupPriceExcludedBaseRoomTypeSupplementOnSave();
        syncEventAggregatorService.registerSyncEvent(SyncEvent.PRICING_CONFIG_CHANGED);
    }

    private void ignorePastDateRangeOfASeason(PricingBaseAccomType pricingBaseAccomType, LocalDate systemDate) {
        if (isCurrentSeason(systemDate, pricingBaseAccomType)) {
            pricingBaseAccomType.setStartDate(systemDate);
        }
    }

    public void saveGroupPricingBaseAccomTypesForExcelUpload(List<PricingBaseAccomType> uploadedAccomTypes) {
        truncateCurrentSeasonsInDatabaseToRetainOnlyPastDateRange();
        deleteGroupPricingBaseAccomTypes();
        ignorePastDateRangeOfCurrentSeasons(uploadedAccomTypes);
        saveGroupPricingBaseAccomTypes(uploadedAccomTypes);
    }

    private void ignorePastDateRangeOfCurrentSeasons(List<PricingBaseAccomType> pricingBaseAccomTypes) {
        LocalDate systemDate = new LocalDate(dateService.getCaughtUpDate());
        pricingBaseAccomTypes.forEach(pricingBaseAccomType -> ignorePastDateRangeOfASeason(pricingBaseAccomType, systemDate));
    }

    private void truncateCurrentSeasonsInDatabaseToRetainOnlyPastDateRange() {
        LocalDate systemDate = new LocalDate(dateService.getCaughtUpDate());
        List<PricingBaseAccomType> savedSeasons = getGroupPricingBaseAccomTypeSeasons();
        List<PricingBaseAccomType> currentSeasonsWithEndDatesTruncated = savedSeasons.stream()
                .filter(season -> isCurrentSeason(systemDate, season))
                .map(season -> seasonWithTruncatedEndDateToPastDate(season, systemDate))
                .collect(toList());

        tenantCrudService.save(currentSeasonsWithEndDatesTruncated);
    }

    private PricingBaseAccomType seasonWithTruncatedEndDateToPastDate(PricingBaseAccomType season, LocalDate systemDate) {
        season.setEndDate(systemDate.minusDays(1));
        return season;
    }

    private boolean isCurrentSeason(LocalDate systemDate, PricingBaseAccomType season) {
        LocalDate startDate = season.getStartDate();
        LocalDate endDate = season.getEndDate();
        return startDate != null && endDate != null && startDate.isBefore(systemDate) && !endDate.isBefore(systemDate);
    }

    public void deletePricingBaseAccomTypes(List<PricingBaseAccomType> pricingBaseAccomTypesToDelete) {
        if (!pricingBaseAccomTypesToDelete.isEmpty()) {
            pricingConfigurationLTBDEService.enabledLTBDEIfApplicable(toJavaLocalDate(pricingBaseAccomTypesToDelete.get(0).getEndDate()), dateService.getCaughtUpJavaLocalDate());
        }
        tenantCrudService.delete(pricingBaseAccomTypesToDelete);
        if (isSyncGPCeilingFloorWithPrimaryPricedProductEnabled() && CollectionUtils.isNotEmpty(pricingBaseAccomTypesToDelete)) {
            deleteGPCeilingFloorForPrimaryPricedProduct(pricingBaseAccomTypesToDelete);
        }
    }

    public void deletePricingAccomClass(AccomClass accomClass) {
        final PricingAccomClass pricingAccomClass = tenantCrudService.findByNamedQuerySingleResult(
                PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_CLASS,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and("accomClassId", accomClass.getId()).parameters());

        if (pricingAccomClass != null) {
            tenantCrudService.delete(PricingAccomClass.class, pricingAccomClass.getId());
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void saveImpactedGroupPricingBaseAccomTypes(List<PricingBaseAccomType> pricingBaseAccomTypesToPersist) {
        pricingBaseAccomTypesToPersist.forEach(PricingBaseAccomType::removeNonDateRangeDayOfWeekValues);

        applyTax(pricingBaseAccomTypesToPersist);

        tenantCrudService.save(pricingBaseAccomTypesToPersist);
        taxInclusiveMigrationService.handleGroupPriceExcludedBaseRoomTypeSupplementOnSave();
        syncEventAggregatorService.registerSyncEvent(SyncEvent.PRICING_CONFIG_CHANGED);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void saveImpactedTransientPricingBaseAccomTypes(List<PricingBaseAccomType> pricingBaseAccomTypesToPersist) {
        List<PricingBaseAccomType> defaultPricingAccomTypes = new ArrayList<>();
        List<PricingBaseAccomType> seasonsPricingAccomTypes = new ArrayList<>();
        List<PricingBaseAccomType> toUpdateGroupPricingSeasons = new ArrayList<>();
        List<PricingBaseAccomType> toCreateGroupPricingSeasons = new ArrayList<>();
        List<PricingBaseAccomType> oldPricingBaseAccomTypesSeasons = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(pricingBaseAccomTypesToPersist)) {
            pricingBaseAccomTypesToPersist.forEach(PricingBaseAccomType::removeNonDateRangeDayOfWeekValues);

            Product product = tenantCrudService.findByNamedQuerySingleResult(Product.GET_BY_ID,
                    QueryParameter.with("productId", pricingBaseAccomTypesToPersist.get(0).getProductID()).parameters());
            applySupplementsAndTax(pricingBaseAccomTypesToPersist, product);
            pricingConfigurationLTBDEService.enabledLTBDEIfApplicableForCeilingFloorChange(pricingBaseAccomTypesToPersist, dateService.getCaughtUpJavaLocalDate());

            if (isSyncGPCeilingFloorWithPrimaryPricedProductEnabled()) {
                defaultPricingAccomTypes = populateDefaultsList(pricingBaseAccomTypesToPersist);
                seasonsPricingAccomTypes = populateSeasonList(pricingBaseAccomTypesToPersist);

                if (CollectionUtils.isNotEmpty(seasonsPricingAccomTypes)) {
                    toCreateGroupPricingSeasons = getPricingSeasonsToCreateGPSeasons(seasonsPricingAccomTypes);
                    toUpdateGroupPricingSeasons = getPricingSeasonsToUpdateGPSeasons(seasonsPricingAccomTypes);
                    if (CollectionUtils.isNotEmpty(toUpdateGroupPricingSeasons)) {
                        oldPricingBaseAccomTypesSeasons = populateGroupPricingSeasonsFromPricingSeasons(toUpdateGroupPricingSeasons);
                    }
                }
            }
            tenantCrudService.save(pricingBaseAccomTypesToPersist);

            if (isPrimaryPricedProductCeilingFloorToBeSyncedInGP(product)) {
                updateGroupPricingDefaultsFromPricing(defaultPricingAccomTypes);

                updateGroupPricingSeasonsFromPricing(toUpdateGroupPricingSeasons, toCreateGroupPricingSeasons, oldPricingBaseAccomTypesSeasons);
            }
        }
        taxInclusiveMigrationService.handleTransientPriceExcludedBaseRoomTypeOnSupplementChange();
        syncEventAggregatorService.registerSyncEvent(SyncEvent.PRICING_CONFIG_CHANGED);
    }


    private void updateGroupPricingSeasonsFromPricing(List<PricingBaseAccomType> seasonsToUpdate, List<PricingBaseAccomType> seasonsToCreate, List<PricingBaseAccomType> oldTransientPricingBaseAccomTypesSeasons) {
        if (CollectionUtils.isNotEmpty(oldTransientPricingBaseAccomTypesSeasons)) {
            oldTransientPricingBaseAccomTypesSeasons.forEach(oldPricingAccomTypeSeason ->
                    deleteExistingGPCeilingFloorSeasonFromPricingCeilingFloorSeason(oldPricingAccomTypeSeason)
            );
            syncGPCeilingFloorWithPrimaryPricedProductSeason(seasonsToUpdate);
        }
        if (CollectionUtils.isNotEmpty(seasonsToCreate)) {
            syncGPCeilingFloorWithPrimaryPricedProductSeason(seasonsToCreate);
        }
    }

    private void updateGroupPricingDefaultsFromPricing(List<PricingBaseAccomType> defaultPricingAccomTypes) {
        if (CollectionUtils.isNotEmpty(defaultPricingAccomTypes)) {
            syncDefaultGPCeilingFloorWithPrimaryPricedProduct(defaultPricingAccomTypes);
        }
    }

    private boolean isPrimaryPricedProductCeilingFloorToBeSyncedInGP(Product product) {
        return isPrimaryPricedProduct(product) && isSyncGPCeilingFloorWithPrimaryPricedProductEnabled();
    }

    private List<PricingBaseAccomType> getPricingSeasonsToCreateGPSeasons(List<PricingBaseAccomType> seasonsPricingBaseAccomTypes) {
        return seasonsPricingBaseAccomTypes.stream()
                .filter(pricingBaseAccomType -> pricingBaseAccomType.getId() == null).collect(Collectors.toList());

    }


    private List<PricingBaseAccomType> getPricingSeasonsToUpdateGPSeasons(List<PricingBaseAccomType> seasonsPricingBaseAccomTypes) {
        return seasonsPricingBaseAccomTypes.stream()
                .filter(pricingBaseAccomType -> pricingBaseAccomType.getId() != null).collect(Collectors.toList());
    }

    private List<PricingBaseAccomType> populateGroupPricingSeasonsFromPricingSeasons(List<PricingBaseAccomType> seasonsToUpdate) {
        Map<String, List<PricingBaseAccomType>> existingPricingAccomTypeMapBySeason = seasonsToUpdate.stream().collect(Collectors.groupingBy(PricingBaseAccomType::getSeasonName));

        List<PricingBaseAccomType> oldTransientPricingBaseAccomTypesSeasons = new ArrayList<>();
        if (!existingPricingAccomTypeMapBySeason.isEmpty()) {
            for (String seasonName : existingPricingAccomTypeMapBySeason.keySet()) {
                List<PricingBaseAccomType> pricingBaseAccomTypes = existingPricingAccomTypeMapBySeason.get(seasonName);
                if (CollectionUtils.isNotEmpty(pricingBaseAccomTypes)) {
                    PricingBaseAccomType oldTransientPricingBaseAccomTypeSeason = populateOldSeasonPricingCeilingFloorBeforePersistNewSeasonPricingCeilingFloor(pricingBaseAccomTypes.get(0));
                    oldTransientPricingBaseAccomTypesSeasons.add(oldTransientPricingBaseAccomTypeSeason);
                }
            }
        }
        return oldTransientPricingBaseAccomTypesSeasons;
    }

    private PricingBaseAccomType populateOldSeasonPricingCeilingFloorBeforePersistNewSeasonPricingCeilingFloor(PricingBaseAccomType pricingBaseAccomType) {
        if (Objects.nonNull(pricingBaseAccomType)) {
            QueryParameter queryParameter = QueryParameter.with(ID, pricingBaseAccomType.getId());
            queryParameter.and(ACCOM_TYPE_ID, pricingBaseAccomType.getAccomType().getId());
            TransientPricingBaseAccomType transientPricingBaseAccomType = tenantCrudService.findByNamedQuerySingleResult(TransientPricingBaseAccomType.FIND_BY_ID_AND_ACCOM_TYPE,
                    queryParameter.parameters());
            return transientPricingBaseAccomType.clone();
        }
        return null;
    }


    public void saveTransientPricingBaseAccomTypes(List<PricingBaseAccomType> pricingBaseAccomTypes) {
        if(CollectionUtils.isEmpty(pricingBaseAccomTypes)){
            return;
        }
        pricingBaseAccomTypes.forEach(PricingBaseAccomType::removeNonDateRangeDayOfWeekValues);

        //We will need to remove tax and supplements for defaults
        Product product = tenantCrudService.findByNamedQuerySingleResult(Product.GET_BY_ID,
                QueryParameter.with("productId", pricingBaseAccomTypes.get(0).getProductID()).parameters());
        applySupplementsAndTax(pricingBaseAccomTypes, product);

        pricingConfigurationLTBDEService.enabledLTBDEIfApplicableForCeilingFloorChange(pricingBaseAccomTypes, dateService.getCaughtUpJavaLocalDate());

        tenantCrudService.save(pricingBaseAccomTypes);
        taxInclusiveMigrationService.handleTransientPriceExcludedBaseRoomTypeOnSupplementChange();

        if (configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED)) {
            if (product.isIndependentProduct()) {
                agileRatesConfigurationService.validateIndependentProducts();
            }
        }

        if (isPrimaryPricedProduct(product) && isSyncGPCeilingFloorWithPrimaryPricedProductEnabled()
                && isInstanceOfTransientPricingBaseAccomType(pricingBaseAccomTypes)) {
            syncPrimaryPricedProductGPCeilingFloorWithPricing(pricingBaseAccomTypes);
        }
        syncEventAggregatorService.registerSyncEvent(SyncEvent.PRICING_CONFIG_CHANGED);
    }

    public Boolean isPrimaryPricedProduct(Product product) {
        return product != null && product.isSystemDefault();
    }

    public Boolean isInstanceOfTransientPricingBaseAccomType(List<PricingBaseAccomType> pricingBaseAccomTypes) {
        return CollectionUtils.isNotEmpty(pricingBaseAccomTypes)
                && pricingBaseAccomTypes.get(0).getClass().equals(TransientPricingBaseAccomType.class);
    }

    public void syncPrimaryPricedProductGPCeilingFloorWithPricing(List<PricingBaseAccomType> pricingBaseAccomTypes) {
        List<PricingBaseAccomType> baseAccomTypes = new ArrayList<>();
        List<PricingBaseAccomType> seasonBaseAccomTypes = new ArrayList<>();
        populateDefaultsAndSeasonsList(pricingBaseAccomTypes, baseAccomTypes, seasonBaseAccomTypes);

        if (CollectionUtils.isNotEmpty(baseAccomTypes)) {
            syncDefaultGPCeilingFloorWithPrimaryPricedProduct(baseAccomTypes);
        }
        if (CollectionUtils.isNotEmpty(seasonBaseAccomTypes)) {
            syncGPCeilingFloorWithPrimaryPricedProductSeason(seasonBaseAccomTypes);
        }
    }

    private void populateDefaultsAndSeasonsList(List<PricingBaseAccomType> pricingBaseAccomTypes, List<PricingBaseAccomType> baseAccomTypes, List<PricingBaseAccomType> seasonBaseAccomTypes) {
        if (CollectionUtils.isNotEmpty(pricingBaseAccomTypes)) {
            pricingBaseAccomTypes.forEach(pricingBaseAccomType -> {
                if (isDefault(pricingBaseAccomType)) {
                    baseAccomTypes.add(pricingBaseAccomType);
                } else {
                    seasonBaseAccomTypes.add(pricingBaseAccomType);
                }
            });
        }
    }

    private List<PricingBaseAccomType> populateDefaultsList(List<PricingBaseAccomType> pricingBaseAccomTypes) {
        List<PricingBaseAccomType> defaultPricingBaseAccomTypeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pricingBaseAccomTypes)) {
            pricingBaseAccomTypes.forEach(pricingBaseAccomType -> {
                if (isDefault(pricingBaseAccomType)) {
                    defaultPricingBaseAccomTypeList.add(pricingBaseAccomType);
                }
            });
        }
        return defaultPricingBaseAccomTypeList;
    }

    private List<PricingBaseAccomType> populateSeasonList(List<PricingBaseAccomType> pricingBaseAccomTypes) {
        List<PricingBaseAccomType> seasonPricingBaseAccomTypeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pricingBaseAccomTypes)) {
            pricingBaseAccomTypes.forEach(pricingBaseAccomType -> {
                if (!isDefault(pricingBaseAccomType)) {
                    seasonPricingBaseAccomTypeList.add(pricingBaseAccomType);
                }
            });
        }
        return seasonPricingBaseAccomTypeList;
    }

    public List<PricingBaseAccomType> getTransientPricingBaseAccomTypeDefaultsWithPriceExcluded(Product product) {
        List<PricingBaseAccomType> allPricingBaseAccomTypes = getAllTransientPricingBaseAccomTypesWithPriceExcluded(product);
        return getPricingBaseAccomTypesDefaults(allPricingBaseAccomTypes);
    }

    public List<PricingBaseAccomType> getTransientPricingBaseAccomTypeDefaults() {
        List<PricingBaseAccomType> allPricingBaseAccomTypes = getAllTransientPricingBaseAccomTypes();
        return getPricingBaseAccomTypesDefaults(allPricingBaseAccomTypes);
    }

    public List<PricingBaseAccomType> getPricingBaseAccomTypesDefaults(List<PricingBaseAccomType> allPricingBaseAccomTypes) {
        List<PricingBaseAccomType> groupPricingBaseAccomTypeDefaults = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(allPricingBaseAccomTypes)) {
            for (PricingBaseAccomType pricingBaseAccomType : allPricingBaseAccomTypes) {
                if (isDefault(pricingBaseAccomType)) {
                    groupPricingBaseAccomTypeDefaults.add(pricingBaseAccomType);
                }
            }
        }
        return groupPricingBaseAccomTypeDefaults;
    }

    public List<PricingBaseAccomType> getTransientPricingBaseAccomTypeDefaultsInDraft(Integer productId) {
        List<PricingBaseAccomType> allPricingBaseAccomTypesInDraft = getTransientPricingBaseAccomTypesInDraftByProduct(productId);
        return getPricingBaseAccomTypesDefaults(allPricingBaseAccomTypesInDraft);
    }

    public List<PricingBaseAccomType> getTransientPricingBaseAccomTypeSeasons() {
        List<PricingBaseAccomType> allPricingBaseAccomTypes = getAllTransientPricingBaseAccomTypes();
        return getPricingBaseAccomTypesSeasons(allPricingBaseAccomTypes);
    }

    public List<PricingBaseAccomType> getTransientPricingBaseAccomTypeSeasonsWithPriceExcluded(Product product) {
        List<PricingBaseAccomType> allPricingBaseAccomTypes = getAllTransientPricingBaseAccomTypesWithPriceExcluded(product);
        return getPricingBaseAccomTypesSeasons(allPricingBaseAccomTypes);
    }

    public List<PricingBaseAccomType> getPricingBaseAccomTypesSeasons(List<PricingBaseAccomType> allPricingBaseAccomTypes) {
        List<PricingBaseAccomType> transientPricingBaseAccomTypeSeasons = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(allPricingBaseAccomTypes)) {
            for (PricingBaseAccomType pricingBaseAccomType : allPricingBaseAccomTypes) {
                if (!isDefault(pricingBaseAccomType)) {
                    transientPricingBaseAccomTypeSeasons.add(pricingBaseAccomType);
                }
            }
        }
        return transientPricingBaseAccomTypeSeasons;
    }

    public List<PricingBaseAccomType> getTransientPricingBaseAccomTypeSeasonsInDraft(Integer productId) {
        List<PricingBaseAccomType> allPricingBaseAccomTypesInDraft = getTransientPricingBaseAccomTypesInDraftByProduct(productId);
        return getPricingBaseAccomTypesSeasons(allPricingBaseAccomTypesInDraft);
    }


    public boolean getIsPricingConfigurationEnabled() {
        // Note: Needed to add a check here for Room Class Group Pricing, as the configurations have been moved under
        //       the Group Pricing Configuration tab, if continuous pricing is not turned on.
        return configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_PRICING_CONFIGURATION_ENABLED.value()) ||
                configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED.value());
    }

    public List<PricingBaseAccomType> getTransientPricingBaseAccomTypesInDraftByProduct(Integer productId) {
        return tenantCrudService.findByNamedQuery(
                TransientPricingBaseAccomTypeDraft.GET_BY_PRODUCT,QueryParameter.with("productId",productId).parameters());
    }

    public boolean isBaseAccomTypeSetupComplete() {
        List<AccomClass> roomClasses = groupPricingConfigurationService.getAllActiveNonDefaultAccomClassByViewOrder();

        return isBaseAccomTypeSetupComplete(PacmanWorkContextHelper.getPropertyId(), roomClasses);
    }

    public boolean isBaseAccomTypeSetupComplete(Integer propertyId, List<AccomClass> activeRoomClasses) {
        List<PricingAccomClass> pricingAccomClasses = getPricingAccomClasses(propertyId);
        List<AccomClass> setupCompletedList = new ArrayList<>();
        getSetupCompletedAccomClassList(setupCompletedList, pricingAccomClasses, activeRoomClasses);

        return pricingAccomClasses.size() == setupCompletedList.size() && setupCompletedList.size() == activeRoomClasses.size();
    }

    public boolean isCeilingFloorGroupComplete(Integer propertyId) {
        List<PricingAccomClass> pricingAccomClasses = getNonExcludedPricingAccomClasses(propertyId);
        List<PricingBaseAccomType> groupPricingBaseAccomTypeSeasons = getGroupPricingBaseAccomTypeDefaults(propertyId);

        return pricingAccomClasses.size() == groupPricingBaseAccomTypeSeasons.size();
    }

    public void getSetupCompletedAccomClassList(List<AccomClass> baseAccomTypeSetupCompleteList,
                                                List<PricingAccomClass> pricingAccomClasses, List<AccomClass> roomClasses) {
        for (PricingAccomClass pricingAccomClass : pricingAccomClasses) {
            if (pricingAccomClass.getAccomType() != null) { // set up complete
                for (AccomClass roomClass : roomClasses) {
                    if (pricingAccomClass.getAccomClass().equals(roomClass)) { // exists in room class
                        baseAccomTypeSetupCompleteList.add(roomClass);
                        break;
                    }
                }
            }
        }
    }

    public void deletePricingConfigurationsForRoomType(Set<AccomType> accomTypes) {
        accomTypes.forEach(accomType -> {
            AccomType existingAccomType = tenantCrudService.find(AccomType.class, accomType.getId());

            final PricingAccomClass pricingAccomClass = tenantCrudService.findByNamedQuerySingleResult(
                    PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_CLASS,
                    QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                            .and("accomClassId", existingAccomType.getAccomClass().getId()).parameters());

            //Clean up all types within class of a base room type or just clean up the room type alone
            Set<AccomType> accomTypesToCleanUp = Collections.singleton(existingAccomType);

            boolean isBaseRoomType = pricingAccomClass != null && accomType.getId().equals(pricingAccomClass.getAccomType().getId());
            boolean accomClassWasDeleted = accomType.getAccomClass().getStatusId().equals(Constants.INACTIVE_STATUS_ID);

            if (isBaseRoomType || accomClassWasDeleted) {
                //Delete all config for room class without base type
                if (pricingAccomClass != null) {
                    tenantCrudService.delete(PricingAccomClass.class, pricingAccomClass.getId());
                }
                //Delete defaults and seasons for the base accom type
                deleteTransientPricingConfigurationsByAccomType(existingAccomType);
                deleteGroupPricingConfigurationsByAccomType(existingAccomType);

                if (isCentralRmsSyncAvailable()) {
                    centralRmsService.syncPricingDataOnRoomClassChange();
                }
            }

            deletePricingOffsetAccomTypesByAccomTypes(accomTypesToCleanUp);

            deletePricingConfigurationSeasonsForAccomTypes(accomTypesToCleanUp);

            deletePricingConfigurationSupplementsForAccomTypes(accomTypesToCleanUp);
        });
    }

    public void deletePricingConfigurationsByRoomTypes(Set<AccomType> accomTypes) {
        accomTypes.forEach(accomType -> {
            final PricingAccomClass pricingAccomClass = tenantCrudService.findByNamedQuerySingleResult(
                    PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_TYPE,
                    QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                            .and("accomTypeId", accomType.getId()).parameters());

            if (pricingAccomClass != null) {
                // this was base accom-type
                tenantCrudService.delete(PricingAccomClass.class, pricingAccomClass.getId());
                deleteTransientPricingConfigurationsByAccomType(accomType);
                deleteGroupPricingConfigurationsByAccomType(accomType);
            }

            Set<AccomType> accomTypesToCleanUp = Collections.singleton(accomType);

            deletePricingOffsetAccomTypesByAccomTypes(accomTypesToCleanUp);

            deletePricingConfigurationSeasonsForAccomTypes(accomTypesToCleanUp);

            deletePricingConfigurationSupplementsForAccomTypes(accomTypesToCleanUp);
        });
    }

    public List<CPConfigOffsetAccomType> findAllOffsets() {
        return tenantCrudService.findAll(CPConfigOffsetAccomType.class);
    }

    public void deleteOffsets(List<CPConfigOffsetAccomType> toDelete) {
        tenantCrudService.delete(toDelete);
    }

    public void deletePricingConfigurationSupplementsForAccomTypes(Set<AccomType> accomTypesToCleanUp) {
        tenantCrudService.executeUpdateByNamedQuery(AccomTypeSupplement.DELETE_SUPPLEMENT_BY_ACCOM_TYPE,
                QueryParameter.with(ACCOM_TYPES, accomTypesToCleanUp).parameters());
    }

    private void deletePricingConfigurationSeasonsForAccomTypes(Set<AccomType> accomTypesToCleanUp) {
        tenantCrudService.executeUpdateByNamedQuery(CPConfigOffsetAccomType.DELETE_SEASONS_DATE_FOR_ACCOM_TYPE,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and(ACCOM_TYPES, accomTypesToCleanUp).parameters());
    }

    public void deleteAllPricincAccomClasses() {
        tenantCrudService.executeUpdateByNamedQuery(PricingAccomClass.DELETE_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());

    }

    public void deletePricingOffsetAccomTypesByAccomTypes(Set<AccomType> accomTypes) {
        tenantCrudService.executeUpdateByNamedQuery(CPConfigOffsetAccomType.DELETE_BY_ACCOM_TYPES,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and(ACCOM_TYPES, accomTypes).parameters());
    }

    private void deleteTransientPricingConfigurationsByAccomType(AccomType accomType) {
        tenantCrudService.executeUpdateByNamedQuery(TransientPricingBaseAccomType.DELETE_BY_ACCOM_TYPE,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and("accomType", accomType).parameters());
    }

    private void deleteGroupPricingConfigurationsByAccomType(AccomType accomType) {
        tenantCrudService.executeUpdateByNamedQuery(GroupPricingBaseAccomType.DELETE_BY_ACCOM_TYPE,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and("accomType", accomType).parameters());
    }

    private void deleteGroupPricingBaseAccomTypeForAccomClass(Integer accomClassId) {
        //Delete rows for this accom class in default and seasons
        deleteGroupPricingBaseAccomType(accomClassId);
    }

    public void deleteTransientPricingConfigurationsByAccomTypes(Set<AccomType> accomTypes) {
        tenantCrudService.executeUpdateByNamedQuery(TransientPricingBaseAccomType.DELETE_BY_ACCOM_TYPES,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and(ACCOM_TYPES, accomTypes).parameters());
    }

    public void deleteGroupPricingConfigurationsByAccomTypes(Set<AccomType> accomTypes) {
        tenantCrudService.executeUpdateByNamedQuery(GroupPricingBaseAccomType.DELETE_BY_ACCOM_TYPES,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and(ACCOM_TYPES, accomTypes).parameters());
    }


    @SuppressWarnings("unchecked")
    public void deleteGroupPricingBaseAccomType(Integer accomClassId) {
        List<PricingBaseAccomType> groupPricingBaseAccomTypes = tenantCrudService.findByNamedQuery(
                GroupPricingBaseAccomType.FIND_BY_PROPERTY_AND_ACCOM_CLASS,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and(ACCOM_CLASS_ID, accomClassId).parameters());

        if (CollectionUtils.isNotEmpty(groupPricingBaseAccomTypes)) {
            tenantCrudService.delete(groupPricingBaseAccomTypes);
        }
    }

    private void deleteTransientPricingBaseAccomTypeForAccomClass(Integer accomClassId) {
        //Delete rows for this accom class in default and seasons
        deleteTransientPricingBaseAccomType(accomClassId);
    }


    public void deleteTransientPricingBaseAccomType(Integer accomClassId) {
        List<PricingBaseAccomType> transientPricingBaseAccomTypes = getTransientPricingBaseAccomTypesForAccomClass(accomClassId);

        if (CollectionUtils.isNotEmpty(transientPricingBaseAccomTypes)) {
            tenantCrudService.delete(transientPricingBaseAccomTypes);
        }
    }

    public void deleteTransientPricingBaseAccomTypeDraft(Integer accomClassId) {
        tenantCrudService.executeUpdateByNativeQuery(DELETE_FLOOR_CEILING_DRAFT_VALUES_FOR_ACCOM_CLASS, QueryParameter.with("accomClassId", accomClassId).parameters());
    }

    @SuppressWarnings("unchecked")
    public List<PricingBaseAccomType> getTransientPricingBaseAccomTypesForAccomClass(Integer accomClassId) {
        return tenantCrudService.findByNamedQuery(
                TransientPricingBaseAccomType.FIND_BY_PROPERTY_AND_ACCOM_CLASS,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and(ACCOM_CLASS_ID, accomClassId).parameters());
    }

    public List<PricingBaseAccomType> getTransientPricingBaseAccomTypesDraftForAccomClass(Integer accomClassId) {
        return tenantCrudService.findByNamedQuery(
                TransientPricingBaseAccomTypeDraft.FIND_BY_PROPERTY_AND_ACCOM_CLASS,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and(ACCOM_CLASS_ID, accomClassId).parameters());
    }

    public List<PricingBaseAccomType> getGroupPricingBaseAccomTypesForAccomClass(Integer accomClassId) {
        return tenantCrudService.findByNamedQuery(GroupPricingBaseAccomType.FIND_BY_PROPERTY_AND_ACCOM_CLASS, QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                .and(ACCOM_CLASS_ID, accomClassId).parameters());
    }

    @SuppressWarnings("unchecked")
    public void ensureSeasonRowsForAlteredAccomClasses(List<PricingAccomClass> classesToSave) {

        List<PricingBaseAccomType> transientPricingBaseAccomTypeSeasons = tenantCrudService.findByNamedQuery(
                TransientPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());

        List<PricingBaseAccomType> groupPricingBaseAccomTypeSeasons = tenantCrudService.findByNamedQuery(
                GroupPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());

        //Find the changed room classes, but don't include price excluded room classes when we ensure the season rows down below
        List<PricingAccomClass> alteredAccomClasses = classesToSave.stream()
                .filter(pricingAccomClass -> pricingAccomClass.isAccomTypeChanged() || pricingAccomClass.isPriceExcludedChanged() && !pricingAccomClass.isPriceExcluded())
                .collect(toList());
        List<PricingAccomClass> alteredAccomClassesForTransientPricing = classesToSave.stream()
                .filter(pricingAccomClass -> pricingAccomClass.isAccomTypeChanged() || pricingAccomClass.isPriceExcludedChanged())
                .collect(toList());

        ensureSeasonRows(groupPricingBaseAccomTypeSeasons, alteredAccomClasses, false);
        ensureSeasonRows(transientPricingBaseAccomTypeSeasons, alteredAccomClassesForTransientPricing, true);
    }

    private void ensureSeasonRows
            (List<PricingBaseAccomType> pricingBaseAccomTypeSeasons, List<PricingAccomClass> alteredAccomClasses,
             boolean isTransient) {
        //Find all distinct seasons where the accom class is not price excluded
        //See if the season contains a row for the altered accom class
        //If no, then create an empty row
        if (CollectionUtils.isNotEmpty(pricingBaseAccomTypeSeasons) && CollectionUtils.isNotEmpty(alteredAccomClasses)) {
            Map<String, List<PricingBaseAccomType>> seasonNameMap = pricingBaseAccomTypeSeasons.stream()
                    .collect(Collectors.groupingBy(PricingBaseAccomType::getSeasonName));

            if (MapUtils.isNotEmpty(seasonNameMap)) {
                seasonNameMap.forEach((seasonName, seasonRowsForSeasonName) -> {
                    alteredAccomClasses.forEach(alteredAccomClass -> {
                        Optional<PricingBaseAccomType> seasonRowForAlteredClass = pricingBaseAccomTypeSeasons.stream()
                                .filter(seasonRow -> seasonName.equals(seasonRow.getSeasonName()))
                                .filter(seasonRow -> seasonRow.getAccomType().equals(alteredAccomClass.getAccomType()))
                                .findAny();

                        if (!seasonRowForAlteredClass.isPresent() && CollectionUtils.isNotEmpty(seasonRowsForSeasonName)) {
                            PricingBaseAccomType defaultSeasonRow = isTransient ? new TransientPricingBaseAccomType() : new GroupPricingBaseAccomType();
                            defaultSeasonRow.setAccomType(alteredAccomClass.getAccomType());
                            defaultSeasonRow.setPropertyId(alteredAccomClass.getPropertyId());
                            defaultSeasonRow.setProductID(seasonRowsForSeasonName.get(0).getProductID());
                            defaultSeasonRow.setStartDate(seasonRowsForSeasonName.get(0).getStartDate());
                            defaultSeasonRow.setEndDate(seasonRowsForSeasonName.get(0).getEndDate());
                            defaultSeasonRow.setStatus(Status.ACTIVE);
                            defaultSeasonRow.setSeasonName(seasonName);

                            tenantCrudService.save(defaultSeasonRow);
                        }
                    });
                });
            }
        }
    }


    @SuppressWarnings("unchecked")
    public void deleteTransientSeasons() {
        List<PricingBaseAccomType> transientPricingBaseAccomTypeSeasons = tenantCrudService.findByNamedQuery(
                TransientPricingBaseAccomType.FIND_SEASONS_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());

        if (CollectionUtils.isNotEmpty(transientPricingBaseAccomTypeSeasons)) {
            tenantCrudService.delete(transientPricingBaseAccomTypeSeasons);
        }
    }

    @SuppressWarnings("unchecked")
    private void deleteTransientPriceableSeasons() {
        List<PricingBaseAccomType> transientPricingBaseAccomTypeSeasons = tenantCrudService.findByNamedQuery(
                TransientPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());

        if (CollectionUtils.isNotEmpty(transientPricingBaseAccomTypeSeasons)) {
            tenantCrudService.delete(transientPricingBaseAccomTypeSeasons);
        }
    }

    @SuppressWarnings("unchecked")
    private void deleteGroupPriceableSeasons() {
        List<PricingBaseAccomType> groupPricingBaseAccomTypeSeasons = tenantCrudService.findByNamedQuery(
                GroupPricingBaseAccomType.FIND_PRICEABLE_SEASONS_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());

        if (CollectionUtils.isNotEmpty(groupPricingBaseAccomTypeSeasons)) {
            tenantCrudService.delete(groupPricingBaseAccomTypeSeasons);
        }
    }


    @SuppressWarnings("unchecked")
    public void deleteGroupSeasons() {
        List<PricingBaseAccomType> groupPricingBaseAccomTypeSeasons = tenantCrudService.findByNamedQuery(
                GroupPricingBaseAccomType.FIND_SEASONS_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());

        if (CollectionUtils.isNotEmpty(groupPricingBaseAccomTypeSeasons)) {
            tenantCrudService.delete(groupPricingBaseAccomTypeSeasons);
        }
    }


    @SuppressWarnings("unchecked")
    public void deleteOffsetSeasons() {
        List<CPConfigOffsetAccomType> cpConfigOffsetAccomTypesSeasons = tenantCrudService.findByNamedQuery(
                CPConfigOffsetAccomType.FIND_SEASONS_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());

        if (CollectionUtils.isNotEmpty(cpConfigOffsetAccomTypesSeasons)) {
            tenantCrudService.delete(cpConfigOffsetAccomTypesSeasons);
        }
    }


    @SuppressWarnings("unchecked")
    public void deleteSupplementsSeasons() {
        List<AccomTypeSupplement> accomTypeSupplementSeasons = tenantCrudService.findByNamedQuery(
                AccomTypeSupplement.FIND_SEASON_SUPPLEMENTS);

        if (CollectionUtils.isNotEmpty(accomTypeSupplementSeasons)) {
            tenantCrudService.delete(accomTypeSupplementSeasons);
        }
    }

    @SuppressWarnings("unchecked")
    public Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> findOffsetsForDatesAndBaseOccupancyType(LocalDate startDate,
                                                                                                     LocalDate endDate) {
        List<CPConfigMergedOffset> offsets = tenantCrudService.findByNamedQuery(CPConfigMergedOffset.FIND_OFFSETS_WITH_TAX_FOR_PROPERTY_BY_START_DATE_AND_END_DATE_AND_OCCUPANCY_TYPE,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and(START_DATE, startDate).and(END_DATE, endDate).and(OCCUPANCY_TYPE, getBaseOccupancyType().getId()).parameters());

        // Create a Map based on the ID
        return offsets.stream().distinct().collect(Collectors.toMap(CPConfigMergedOffset::getId, value -> value));
    }

    public List<CPConfigOffsetAccomType> findAllOffsetSeasons() {
        return tenantCrudService.findByNamedQuery(
                CPConfigOffsetAccomType.FIND_SEASONS_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @SuppressWarnings("unchecked")
    public Map<CPConfigMergedOffsetPK, CPConfigMergedOffset> findOffsetsForDates(LocalDate startDate, LocalDate
            endDate) {
        // Query for all CPConfigMergedOffset between start/end dates
        List<CPConfigMergedOffset> offsets = tenantCrudService.findByNamedQuery(CPConfigMergedOffset.FIND_OFFSETS_WITH_TAX_FOR_PROPERTY_BY_START_DATE_AND_END_DATE, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and(START_DATE, startDate).and(END_DATE, endDate).parameters());

        // Create a Map based on the ID
        return offsets.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(CPConfigMergedOffset::getId, Function.identity()));
    }

    public CPConfigMergedOffset findMergedOffset(Integer productId, LocalDate arrivalDate, Integer accomTypeId) {
        return findOffsetsForDates(arrivalDate, arrivalDate).get(new CPConfigMergedOffsetPK(arrivalDate, productId, accomTypeId, getBaseOccupancyType()));
    }

    public Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> findCeilingAndFloorConfigForDates
            (LocalDate startDate, LocalDate endDate, boolean useOverrides) {
        List<CPConfigMergedCeilingAndFloor> defaultConfig = getCeilingAndFloorConfig(startDate, endDate, getBaseOccupancyType(), useOverrides);

        // Create a Map based on the ID
        return defaultConfig.stream().collect(Collectors.toMap(CPConfigMergedCeilingAndFloor::getId, Function.identity(), (id, ceilingAndFloor) -> id));
    }

    public List<CPConfigMergedCeilingAndFloor> getCeilingAndFloorConfig(LocalDate startDate, LocalDate
            endDate, OccupancyType baseOccupancy, boolean useOverrides) {
        // Query for all CPConfigMergedCeilingAndFloor between start/end dates
        if (useOptimizedStoredProcedurePricing()) {
            return tenantCrudService.findByNativeQuery(CPConfigMergedCeilingAndFloor.USP_FIND_CEILING_AND_FLOOR_FOR_PROPERTY_BY_START_DATE_AND_END_DATE_WITH_TAX, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and(START_DATE, startDate.toDate()).and(END_DATE, endDate.toDate()).and(OCCUPANCY_TYPE, baseOccupancy.getId()).and(USE_OVERRIDES, useOverrides ? 1 : 0).parameters(), CPConfigMergedCeilingAndFloor.class);
        }
        return tenantCrudService.findByNamedQuery(CPConfigMergedCeilingAndFloor.FIND_CEILING_AND_FLOOR_FOR_PROPERTY_BY_START_DATE_AND_END_DATE_WITH_TAX, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and(START_DATE, startDate).and(END_DATE, endDate).and(OCCUPANCY_TYPE, baseOccupancy.getId()).and(USE_OVERRIDES, useOverrides ? 1 : 0).parameters());
    }

    public List<AccomClassMinPriceDiff> getDefaultAccomClassMinPriceDiff() {
        return tenantCrudService.findAll(AccomClassMinPriceDiff.class);
    }

    public List<AccomClassMinPriceDiffSeason> getSeasonAccomClassMinPriceDiffs() {
        return tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class);
    }

    public void saveAccomClassMinPriceDiff(AccomClassMinPriceDiff accomClassMinPriceDiff) {
        tenantCrudService.save(accomClassMinPriceDiff);
    }

    public void saveAccomClassMinPriceDiffs(List<AccomClassMinPriceDiff> accomClassMinPriceDiffs) {
        tenantCrudService.save(accomClassMinPriceDiffs);
    }

    public void saveAccomClassMinPriceDiffSeasons
            (List<AccomClassMinPriceDiffSeason> accomClassMinPriceDiffSeasons) {
        // Look up the existing AccomClassMinPriceDiffSeason records and put them into a Map by ID
        List<AccomClassMinPriceDiffSeason> existingSeasonList = tenantCrudService.findAll(AccomClassMinPriceDiffSeason.class);
        Map<Integer, AccomClassMinPriceDiffSeason> existingTypeSeasons = CollectionUtils.isEmpty(existingSeasonList) ? new HashMap<>() : existingSeasonList.stream().collect(Collectors.toMap(AccomClassMinPriceDiffSeason::getId, Function.identity()));
        if (CollectionUtils.isNotEmpty(accomClassMinPriceDiffSeasons)) {
            java.time.LocalDate caughtUpDate = dateService.getCaughtUpJavaLocalDate();
            accomClassMinPriceDiffSeasons.forEach(season -> {

                if (pricingConfigurationLTBDEService.isLTBDEOnIndirectConfigChangedEnabled()) {
                    if (season.getId() == null) {
                        if (pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(season.getEndDate()),
                                caughtUpDate)) {
                            pricingConfigurationLTBDEService.enableLTBDEForPricing(true);
                        }
                    } else {
                        AccomClassMinPriceDiffSeason existingSeason = existingTypeSeasons.get(season.getId());
                        boolean isPriceDiffChanged = isSeasonChanged(season, existingSeason);

                        if (isPriceDiffChanged && pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(season.getEndDate()),
                                caughtUpDate)) {
                            pricingConfigurationLTBDEService.enableLTBDEForPricing(true);
                        }

                        if (isSeasonEndDateChangedFromExtendedToWithinOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(season.getEndDate()),
                                JavaLocalDateUtils.toJavaLocalDate(existingSeason.getEndDate()),
                                caughtUpDate)) {
                            pricingConfigurationLTBDEService.enableLTBDEForPricing(true);
                        }
                    }
                }

                // Remove existing seasons from the Map as the records left in the Map will be deleted later
                existingTypeSeasons.remove(season.getId());

                // Save the season
                tenantCrudService.save(season);
            });
        }

        // Delete any values remaining in the Map of existing CPConfigAccomClassMinPriceDiffSeasons
        if (MapUtils.isNotEmpty(existingTypeSeasons)) {
            tenantCrudService.delete(existingTypeSeasons.values());
            if (pricingConfigurationLTBDEService.isLTBDEOnIndirectConfigChangedEnabled()) {
                existingTypeSeasons.values().stream().forEach(
                        accomClassMinPriceDiffSeason ->
                        {
                            if (pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(JavaLocalDateUtils.toJavaLocalDate(accomClassMinPriceDiffSeason.getEndDate()),
                                    dateService.getCaughtUpJavaLocalDate())) {
                                pricingConfigurationLTBDEService.enableLTBDEForPricing(true);
                            }

                        });
            }
        }
    }

    private boolean isSeasonChanged(AccomClassMinPriceDiffSeason newPriceDiffSeason, AccomClassMinPriceDiffSeason existingPriceDiffSeason) {
        return !Objects.equals(newPriceDiffSeason.getMondayDiffWithTax(), existingPriceDiffSeason.getMondayDiffWithTax()) ||
                !Objects.equals(newPriceDiffSeason.getTuesdayDiffWithTax(), existingPriceDiffSeason.getTuesdayDiffWithTax()) ||
                !Objects.equals(newPriceDiffSeason.getWednesdayDiffWithTax(), existingPriceDiffSeason.getWednesdayDiffWithTax()) ||
                !Objects.equals(newPriceDiffSeason.getThursdayDiffWithTax(), existingPriceDiffSeason.getThursdayDiffWithTax()) ||
                !Objects.equals(newPriceDiffSeason.getFridayDiffWithTax(), existingPriceDiffSeason.getFridayDiffWithTax()) ||
                !Objects.equals(newPriceDiffSeason.getSaturdayDiffWithTax(), existingPriceDiffSeason.getSaturdayDiffWithTax()) ||
                !Objects.equals(newPriceDiffSeason.getSundayDiffWithTax(), existingPriceDiffSeason.getSundayDiffWithTax()) ||
                !Objects.equals(newPriceDiffSeason.getStartDate(), existingPriceDiffSeason.getStartDate()) ||
                !Objects.equals(newPriceDiffSeason.getEndDate(), existingPriceDiffSeason.getEndDate());
    }

    private boolean isSeasonEndDateChangedFromExtendedToWithinOptimizationWindow(java.time.LocalDate updateSeasonEndDate, java.time.LocalDate existingSeasonEndDate, java.time.LocalDate caughtupDate) {
        boolean isNewSeasonEndDateIsWithinExtendedWindow = !pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(updateSeasonEndDate, caughtupDate);
        boolean isExistingSeasonEndDateIsInExtendedWindow = pricingConfigurationLTBDEService.isDateBeyondOptimizationWindow(existingSeasonEndDate, caughtupDate);

        return isNewSeasonEndDateIsWithinExtendedWindow && isExistingSeasonEndDateIsInExtendedWindow;
    }

    public void deletePriceDiffDataByPriceRank(AccomClassPriceRank priceRank) {
        if (priceRank.isPersisted()) {
            tenantCrudService.executeUpdateByNamedQuery(AccomClassMinPriceDiff.DELETE_BY_PRICE_RANK, QueryParameter.with("priceRank", priceRank).parameters());
            tenantCrudService.executeUpdateByNamedQuery(AccomClassMinPriceDiffSeason.DELETE_BY_PRICE_RANK, QueryParameter.with("priceRank", priceRank).parameters());
        }
    }

    public void deletePriceDiffDataByPriceRanks(List<AccomClassPriceRank> priceRanks) {
        if (CollectionUtils.isNotEmpty(priceRanks)) {
            priceRanks.forEach(this::deletePriceDiffDataByPriceRank);
        }
    }

    public List<MinPriceDifferentDTO> getDefaultAndSeasonMinPriceDiff(Date currentSystemDate) {

        List<MinPriceDifferentDTO> minPriceDifferentDTOList = getDefaultAccomClassMinPriceDiff().stream()
                .map(MinPriceDifferentDTO::new).collect(toList());
        minPriceDifferentDTOList.addAll(getAccomClassMinPriceDiffCurrentSeason(currentSystemDate).stream().map(MinPriceDifferentDTO::new).collect(toList()));
        return minPriceDifferentDTOList;
    }

    private List<AccomClassMinPriceDiffSeason> getAccomClassMinPriceDiffCurrentSeason(Date currentSystemDate) {
        return tenantCrudService.findByNamedQuery(
                AccomClassMinPriceDiffSeason.GET_CURRENT_SEASON_BY_DATE, QueryParameter.with("currentSystemDate", new LocalDate(currentSystemDate)).parameters());
    }


    public boolean isPricingConfigurationComplete() {
        return isBaseRoomTypeConfigurationComplete() && isBaseRoomTypeFloorAndCeilingConfigurationComplete() && isFixedPriceRoomTypeConfigurationComplete();
    }

    public boolean isBaseRoomTypeConfigurationComplete() {
        return isBaseRoomTypeConfigurationComplete(PacmanWorkContextHelper.getPropertyId());
    }

    public boolean isBaseRoomTypeConfigurationComplete(Integer propertyId) {
        return (Integer) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId,
                PricingAccomClass.FIND_COUNT_OF_AC_WITHOUT_BASE_AT) == 0;
    }

    public boolean isBaseRoomTypeFloorAndCeilingConfigurationComplete(Integer propertyId, Integer productId, String query) {
        return (Integer) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId,
                query, QueryParameter.with("productId", productId).parameters()) == 0;
    }

    public boolean isBaseRoomTypeFloorAndCeilingConfigurationComplete() {
        return isBaseRoomTypeFloorAndCeilingConfigurationComplete(PacmanWorkContextHelper.getPropertyId(), 1,
                CPConfigMergedCeilingAndFloor.FIND_COUNT_OF_AC_WITHOUT_DEFAULT_FLOOR_AND_CEILING_FOR_BAR);
    }

    public boolean isBaseRoomTypeFloorAndCeilingConfigurationCompleteForAllIndependentProducts() {
        return stream(independentProductsService.getAllIndependentProductsIncludingPrimaryPricedProduct())
                .filter(product -> !product.isSystemDefault())
                .filter(product -> product.isActive())
                .allMatch(product -> isBaseRoomTypeFloorAndCeilingConfigurationComplete(PacmanWorkContextHelper.getPropertyId(), product.getId(), CPConfigMergedCeilingAndFloor.FIND_COUNT_OF_AC_WITHOUT_DEFAULT_FLOOR_AND_CEILING));
    }

    public boolean isFixedPriceRoomTypeConfigurationComplete() {
        return isFixedPriceRoomTypeConfigurationComplete(PacmanWorkContextHelper.getPropertyId(), 1, CPConfigMergedOffset.FIND_COUNT_OF_UNCONFIGURED_BASE_AT_FIXED_PRICE_OFFSETS_BAR, CPConfigMergedOffset.FIND_COUNT_OF_UNCONFIGURED_FIXED_PRICE_OFFSETS_BAR);
    }

    public boolean isFixedPriceRoomTypeConfigurationCompleteForAllIndependentProducts() {
        return stream(independentProductsService.getAllIndependentProductsIncludingPrimaryPricedProduct())
                .filter(product -> !product.isSystemDefault())
                .filter(product -> product.isActive())
                .allMatch(product -> isFixedPriceRoomTypeConfigurationComplete(PacmanWorkContextHelper.getPropertyId(), product.getId(), CPConfigMergedOffset.FIND_COUNT_OF_UNCONFIGURED_BASE_AT_FIXED_PRICE_OFFSETS, CPConfigMergedOffset.FIND_COUNT_OF_UNCONFIGURED_FIXED_PRICE_OFFSETS));
    }

    public boolean isFixedPriceRoomTypeConfigurationComplete(Integer propertyId, Integer productId, String priceExcludeCheckQueryAtRTLevel, String priceExcludeCheckQueryATRCLevel) {
        if (isCPBaseRoomTypeEnabled()) {
            return (Integer) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(
                    propertyId, priceExcludeCheckQueryAtRTLevel,
                    QueryParameter.with(OCCUPANCY_TYPE_ID, getBaseOccupancyType().getId()).and(PRODUCT_ID, productId).parameters()
            ) == 0;
        } else {
            return (Integer) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(
                    propertyId, priceExcludeCheckQueryATRCLevel,
                    QueryParameter.with(OCCUPANCY_TYPE_ID, getBaseOccupancyType().getId()).and(PRODUCT_ID, productId).parameters()
            ) == 0;
        }
    }

    @SuppressWarnings("unchecked")
    private void deletePricingOffsetAccomTypes(Integer accomClassId) {
        List<CPConfigOffsetAccomType> pricingOffsetAccomTypes = tenantCrudService.findByNamedQuery(
                CPConfigOffsetAccomType.FIND_BY_PROPERTY_AND_ACCOM_CLASS,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and(ACCOM_CLASS_ID, accomClassId).parameters());

        if (CollectionUtils.isNotEmpty(pricingOffsetAccomTypes)) {
            tenantCrudService.delete(pricingOffsetAccomTypes);
        }
    }
    private void deletePricingOffsetAccomTypesDraft(Integer accomClassId) {
        List<CPConfigOffsetAccomTypeDraft> draftOffsets = tenantCrudService.findByNamedQuery(
                CPConfigOffsetAccomTypeDraft.FIND_BY_ACCOM_CLASS, QueryParameter.with(ACCOM_CLASS_ID, accomClassId).parameters());
        if (CollectionUtils.isNotEmpty(draftOffsets)) {
            tenantCrudService.delete(draftOffsets);
        }
    }

    public CPConfiguration findCPConfiguration() {
        return tenantCrudService.findOne(CPConfiguration.class);
    }

    public void save(CPConfiguration cpConfiguration) {
        tenantCrudService.save(cpConfiguration);
    }

    private boolean isDefault(PricingBaseAccomType pricingBaseAccomType) {
        return pricingBaseAccomType.getStartDate() == null && pricingBaseAccomType.getEndDate() == null;
    }

    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }

    public void setMultiPropertyCrudService(AbstractMultiPropertyCrudService abstractMultiPropertyCrudService) {
        this.multiPropertyCrudService = abstractMultiPropertyCrudService;
    }

    public PricingAccomClass getPricingAccomClass(AccomClass accomClass) {
        return tenantCrudService
                .findByNamedQuerySingleResult(PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_CLASS,
                        QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                                .and("accomClassId", accomClass.getId()).parameters());
    }

    public Map<Integer, BigDecimal> getContinuesPricingApportionedAdr() {
        List<Object[]> rows = tenantCrudService.findByNativeQuery(TransientPricingBaseAccomType.FIND_CEILING_AND_FLOOR_ADR_FOR_DEFAULT_SEASONAL_RATES);
        return convertDbRowsToApportionedAdrMap(rows);
    }

    private Map<Integer, BigDecimal> convertDbRowsToApportionedAdrMap(List<Object[]> rows) {
        Map<Integer, BigDecimal> apportionedAdrs = new HashMap<>();
        if (isNotEmpty(rows)) {
            rows.stream().forEach(row -> {
                apportionedAdrs.put((Integer) row[0], ((BigDecimal) row[1]));
            });
        }
        return apportionedAdrs;
    }

    public Map<Integer, BigDecimal> getContinuesPricingApportionedAdrForAccomTypes(List<Integer> accomTypeIds) {
        List<Object[]> rows = tenantCrudService.findByNativeQuery(TransientPricingBaseAccomType.FIND_CEILING_AND_FLOOR_ADR_FOR_DEFAULT_SEASONAL_RATES_FOR_ACCOM_TYPES, QueryParameter.with("accomTypeIds", accomTypeIds).parameters());
        return convertDbRowsToApportionedAdrMap(rows);

    }

    //Group Eval fails in SAS when per room servicing cost for all RC is not present.
    public boolean isPerRoomServicingCostAvailableForAllRoomClass() {
        List<AccomClass> byNativeQuery = tenantCrudService.findByNamedQuery(AccomClass.ALL_ACTIVE_NON_DEFAULT_NONEMPTY_BY_RANK_ORDER, QueryParameter.with("propertyId", PacmanThreadLocalContextHolder.getWorkContext().getPropertyId()).parameters());
        List<GroupPricingConfiguration> defaultEvaluationMethod = tenantCrudService.findByNamedQuery(GroupPricingConfiguration.FIND_BY_EVALUATION_METHOD,
                QueryParameter.with("defaultEvaluationMethod", GroupPricingEvaluationMethod.RC)
                        .and("propertyId", PacmanThreadLocalContextHolder.getWorkContext().getPropertyId()).parameters());
        List<Integer> collect = defaultEvaluationMethod.stream().map(groupPricingConfiguration -> groupPricingConfiguration.getAccomClass().getId()).collect(toList());
        return !byNativeQuery.stream().filter(accomClass -> !collect.contains(accomClass.getId())).findFirst().isPresent();
    }

    public OccupancyType getBaseOccupancyType() {
        boolean isPerPersonEnabled = configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED);
        return isPerPersonEnabled ? OccupancyType.DOUBLE : OccupancyType.SINGLE;
    }

    public boolean isCPBaseRoomTypeEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value());
    }
    public boolean isSuggestFeatureEnabledForIndependentProducts() {
        return configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCT_SUGGEST_ENABLED.value());
    }
    public boolean isPPPOffsetSuggestEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PPP_OFFSET_SUGGEST_ENABLED.value());
    }

    public List<CPConfigOffsetAccomType> getDefaultOffsetsForExcludedRoomTypesSingleOccupancy
            (List<AccomType> accomTypes) {
        //accomTypes cannot be empty
        OccupancyType occupancyType = isPerPersonPricingEnabled() ? OccupancyType.DOUBLE : OccupancyType.SINGLE;
        return tenantCrudService.findByNamedQuery(CPConfigOffsetAccomType.FIND_BY_PROPERTY_AND_LIST_OF_ACCOM_TYPES_FOR_DEFAULT,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).and("accomTypes",
                        accomTypes).and(OCCUPANCY_TYPE, occupancyType).and("offsetMethod", OffsetMethod.FIXED_PRICE).parameters());
    }

    public List<CPConfigOffsetAccomType> findDefaultOffsets() {
        return tenantCrudService.findByNamedQuery(com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigOffsetAccomType.FIND_DEFAULT_OFFSETS);
    }

    public List<PricingAccomClass> getExcludedPricingAccomClass() {
        return tenantCrudService.findByNamedQuery(PricingAccomClass.FIND_BY_PRICE_EXCLUDED);
    }

    public void applySupplementsAndTax(List<PricingBaseAccomType> pricingBaseAccomTypes, Product product) {
        Tax defaultTax = taxService.findTax();
        final Map<LocalDate, Tax> taxForDateRange = new HashMap<>();
        List<PricingBaseAccomType> seasons = pricingBaseAccomTypes
                .stream()
                .filter(PricingBaseAccomType::isSeason)
                .collect(toList());
        if (!seasons.isEmpty()) {
            LocalDate startDate = seasons.stream().map(PricingBaseAccomType::getStartDate).min(LocalDate::compareTo).orElse(null);
            LocalDate endDate = seasons.stream().map(PricingBaseAccomType::getEndDate).max(LocalDate::compareTo).orElse(null);
            taxForDateRange.putAll(taxService.findTaxesForDateRange(startDate, endDate));
        }
        OccupancyType occupancyType = isPerPersonPricingEnabled() ? OccupancyType.DOUBLE : OccupancyType.SINGLE;
        pricingBaseAccomTypes.forEach(pricingBaseAccomType -> {
            if (!pricingBaseAccomType.isSeason()) {
                // Defaults
                updateDefaultAccomTypesForTaxAndSupplement(defaultTax, occupancyType, pricingBaseAccomType, product);
            } else {
                // Season
                updatePricingBaseAccomTypeSeasonsForTaxAndSupplement(pricingBaseAccomType.getProductID(), pricingBaseAccomType, occupancyType, taxForDateRange);
            }
        });
    }

    public void applySupplementsAndTaxGroupPricingBaseAccomTypes(List<PricingBaseAccomType> pricingBaseAccomTypes) {
        Tax defaultTax = taxService.findTax();
        final Map<LocalDate, Tax> taxForDateRange = new HashMap<>();
        List<PricingBaseAccomType> seasons = pricingBaseAccomTypes
                .stream()
                .filter(PricingBaseAccomType::isSeason)
                .collect(toList());
        if (!seasons.isEmpty()) {
            LocalDate startDate = seasons.stream().map(PricingBaseAccomType::getStartDate).min(LocalDate::compareTo).orElse(null);
            LocalDate endDate = seasons.stream().map(PricingBaseAccomType::getEndDate).max(LocalDate::compareTo).orElse(null);
            taxForDateRange.putAll(taxService.findTaxesForDateRange(startDate, endDate));
        }
        OccupancyType occupancyType = isPerPersonPricingEnabled() ? OccupancyType.DOUBLE : OccupancyType.SINGLE;
        List<PricingBaseAccomType> allPriceExcludedBaseAccomTypes = getAllPriceExcludedBaseAccomTypes();
        pricingBaseAccomTypes.forEach(pricingBaseAccomType -> {
            if (allPriceExcludedBaseAccomTypes.contains(pricingBaseAccomType)) {
                if (!pricingBaseAccomType.isSeason()) {
                    // Defaults
                    Product systemDefaultProduct = tenantCrudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
                    updateDefaultAccomTypesForTaxAndSupplement(defaultTax, occupancyType, pricingBaseAccomType, systemDefaultProduct);
                } else {
                    // Season
                    updatePricingBaseAccomTypeSeasonsForTaxAndSupplement(pricingBaseAccomType.getProductID(), pricingBaseAccomType, occupancyType, taxForDateRange);
                }
            } else {
                if (!pricingBaseAccomType.isSeason()) {
                    // Defaults
                    updateDefaultAccomTypesForTax(defaultTax, pricingBaseAccomType);
                } else {
                    // Season
                    updatePricingBaseAccomTypeSeasonsForTax(pricingBaseAccomType, taxForDateRange);
                }
            }
        });
    }

    private void updatePricingBaseAccomTypeSeasonsForTaxAndSupplement(Integer productId, PricingBaseAccomType pricingBaseAccomType, OccupancyType occupancyType, Map<LocalDate, Tax> taxForDateRange) {
        LocalDate startDate = pricingBaseAccomType.getStartDate();
        LocalDate endDate = pricingBaseAccomType.getEndDate();
        Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> supplementMap = accomTypeSupplementService.getSupplementValueMap(startDate, endDate);
        List<LocalDate> seasonDates = DateUtil.datesBetweenInclusive(startDate, endDate);
        for (LocalDate date : seasonDates) {
            Tax tax = taxForDateRange.get(date);
            AccomTypeSupplementValue supplement = supplementMap.get(new AccomTypeSupplementValuePK(productId, date, pricingBaseAccomType.getAccomType().getId(), occupancyType));
            BigDecimal supplementValue = BigDecimal.ZERO;
            boolean isPercentage = false;
            if (supplement != null) {
                supplementValue = supplement.getValue();
                isPercentage = OffsetMethod.PERCENTAGE.equals(supplement.getOffsetMethod());
            }

            int dayOfWeek = date.getDayOfWeek();

            if (DayOfWeek.FRIDAY.getCalendarDayOfWeek() == dayOfWeek) {
                pricingBaseAccomType.setFridayFloorRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getFridayFloorRateWithTax(), tax, nullToZero(supplementValue), isPercentage));
                pricingBaseAccomType.setFridayCeilingRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getFridayCeilingRateWithTax(), tax, nullToZero(supplementValue), isPercentage));

            } else if (DayOfWeek.SATURDAY.getCalendarDayOfWeek() == dayOfWeek) {
                pricingBaseAccomType.setSaturdayFloorRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getSaturdayFloorRateWithTax(), tax, nullToZero(supplementValue), isPercentage));
                pricingBaseAccomType.setSaturdayCeilingRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getSaturdayCeilingRateWithTax(), tax, nullToZero(supplementValue), isPercentage));

            } else if (DayOfWeek.SUNDAY.getCalendarDayOfWeek() == dayOfWeek) {
                pricingBaseAccomType.setSundayFloorRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getSundayFloorRateWithTax(), tax, nullToZero(supplementValue), isPercentage));
                pricingBaseAccomType.setSundayCeilingRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getSundayCeilingRateWithTax(), tax, nullToZero(supplementValue), isPercentage));

            } else if (DayOfWeek.MONDAY.getCalendarDayOfWeek() == dayOfWeek) {
                pricingBaseAccomType.setMondayFloorRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getMondayFloorRateWithTax(), tax, nullToZero(supplementValue), isPercentage));
                pricingBaseAccomType.setMondayCeilingRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getMondayCeilingRateWithTax(), tax, nullToZero(supplementValue), isPercentage));

            } else if (DayOfWeek.TUESDAY.getCalendarDayOfWeek() == dayOfWeek) {
                pricingBaseAccomType.setTuesdayFloorRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getTuesdayFloorRateWithTax(), tax, nullToZero(supplementValue), isPercentage));
                pricingBaseAccomType.setTuesdayCeilingRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getTuesdayCeilingRateWithTax(), tax, nullToZero(supplementValue), isPercentage));

            } else if (DayOfWeek.WEDNESDAY.getCalendarDayOfWeek() == dayOfWeek) {
                pricingBaseAccomType.setWednesdayFloorRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getWednesdayFloorRateWithTax(), tax, nullToZero(supplementValue), isPercentage));
                pricingBaseAccomType.setWednesdayCeilingRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getWednesdayCeilingRateWithTax(), tax, nullToZero(supplementValue), isPercentage));

            } else if (DayOfWeek.THURSDAY.getCalendarDayOfWeek() == dayOfWeek) {
                pricingBaseAccomType.setThursdayFloorRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getThursdayFloorRateWithTax(), tax, nullToZero(supplementValue), isPercentage));
                pricingBaseAccomType.setThursdayCeilingRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getThursdayCeilingRateWithTax(), tax, nullToZero(supplementValue), isPercentage));
            }
        }
    }

    private void updatePricingBaseAccomTypeSeasonsForTax(PricingBaseAccomType pricingBaseAccomType, Map<LocalDate, Tax> taxForDateRange) {
        LocalDate startDate = pricingBaseAccomType.getStartDate();
        LocalDate endDate = pricingBaseAccomType.getEndDate();
        List<LocalDate> seasonDates = DateUtil.datesBetweenInclusive(startDate, endDate);
        for (LocalDate date : seasonDates) {
            Tax tax = taxForDateRange.get(date);
            int dayOfWeek = date.getDayOfWeek();

            if (DayOfWeek.FRIDAY.getCalendarDayOfWeek() == dayOfWeek) {
                pricingBaseAccomType.setFridayFloorRate(removeTaxFrom(pricingBaseAccomType.getFridayFloorRateWithTax(), tax));
                pricingBaseAccomType.setFridayCeilingRate(removeTaxFrom(pricingBaseAccomType.getFridayCeilingRateWithTax(), tax));

            } else if (DayOfWeek.SATURDAY.getCalendarDayOfWeek() == dayOfWeek) {
                pricingBaseAccomType.setSaturdayFloorRate(removeTaxFrom(pricingBaseAccomType.getSaturdayFloorRateWithTax(), tax));
                pricingBaseAccomType.setSaturdayCeilingRate(removeTaxFrom(pricingBaseAccomType.getSaturdayCeilingRateWithTax(), tax));

            } else if (DayOfWeek.SUNDAY.getCalendarDayOfWeek() == dayOfWeek) {
                pricingBaseAccomType.setSundayFloorRate(removeTaxFrom(pricingBaseAccomType.getSundayFloorRateWithTax(), tax));
                pricingBaseAccomType.setSundayCeilingRate(removeTaxFrom(pricingBaseAccomType.getSundayCeilingRateWithTax(), tax));

            } else if (DayOfWeek.MONDAY.getCalendarDayOfWeek() == dayOfWeek) {
                pricingBaseAccomType.setMondayFloorRate(removeTaxFrom(pricingBaseAccomType.getMondayFloorRateWithTax(), tax));
                pricingBaseAccomType.setMondayCeilingRate(removeTaxFrom(pricingBaseAccomType.getMondayCeilingRateWithTax(), tax));

            } else if (DayOfWeek.TUESDAY.getCalendarDayOfWeek() == dayOfWeek) {
                pricingBaseAccomType.setTuesdayFloorRate(removeTaxFrom(pricingBaseAccomType.getTuesdayFloorRateWithTax(), tax));
                pricingBaseAccomType.setTuesdayCeilingRate(removeTaxFrom(pricingBaseAccomType.getTuesdayCeilingRateWithTax(), tax));

            } else if (DayOfWeek.WEDNESDAY.getCalendarDayOfWeek() == dayOfWeek) {
                pricingBaseAccomType.setWednesdayFloorRate(removeTaxFrom(pricingBaseAccomType.getWednesdayFloorRateWithTax(), tax));
                pricingBaseAccomType.setWednesdayCeilingRate(removeTaxFrom(pricingBaseAccomType.getWednesdayCeilingRateWithTax(), tax));

            } else if (DayOfWeek.THURSDAY.getCalendarDayOfWeek() == dayOfWeek) {
                pricingBaseAccomType.setThursdayFloorRate(removeTaxFrom(pricingBaseAccomType.getThursdayFloorRateWithTax(), tax));
                pricingBaseAccomType.setThursdayCeilingRate(removeTaxFrom(pricingBaseAccomType.getThursdayCeilingRateWithTax(), tax));
            }
        }
    }

    private BigDecimal removeTaxAndSupplementFrom(BigDecimal withTaxRate, Tax tax, BigDecimal supplement, boolean isPercentage) {
        return calculateTax(Supplement.removeSupplementFrom(withTaxRate, supplement, isPercentage), tax);
    }

    private BigDecimal calculateTax(BigDecimal withTaxRate, Tax tax) {
        return null != withTaxRate ? tax.removeRoomTaxRate(nullToZero(withTaxRate)) : null;
    }

    public void applyTax(List<PricingBaseAccomType> pricingBaseAccomTypes) {
        Tax defaultTax = taxService.findTax();
        if (CollectionUtils.isNotEmpty(pricingBaseAccomTypes)) {
            List<PricingBaseAccomType> defaultGroupPricingAccomTypes = pricingBaseAccomTypes
                    .stream()
                    .filter(pbat -> null == pbat.getStartDate())
                    .collect(toList());
            applyTax(defaultGroupPricingAccomTypes, defaultTax);

            List<PricingBaseAccomType> groupPricingAccomTypeSeasons = pricingBaseAccomTypes
                    .stream()
                    .filter(PricingBaseAccomType::isSeason)
                    .collect(toList());
            if (CollectionUtils.isNotEmpty(groupPricingAccomTypeSeasons)) {
                LocalDate startDate = groupPricingAccomTypeSeasons.stream().map(PricingBaseAccomType::getStartDate).min(LocalDate::compareTo).orElse(null);
                LocalDate endDate = groupPricingAccomTypeSeasons.stream().map(PricingBaseAccomType::getEndDate).max(LocalDate::compareTo).orElse(null);
                Map<LocalDate, Tax> taxesByDates = taxService.findTaxesForDateRange(startDate, endDate);
                groupPricingAccomTypeSeasons.forEach(groupPricingAccomTypeSeason
                        -> updateGroupAccomTypeSeasonsForTax(groupPricingAccomTypeSeason, taxesByDates));
            }
        }
    }

    private void applyTax(List<PricingBaseAccomType> pricingBaseAccomTypes, Tax tax) {
        for (PricingBaseAccomType pbat : pricingBaseAccomTypes) {
            pbat.setSundayFloorRate(removeTaxFrom(pbat.getSundayFloorRateWithTax(), tax));
            pbat.setSundayCeilingRate(removeTaxFrom(pbat.getSundayCeilingRateWithTax(), tax));

            pbat.setMondayFloorRate(removeTaxFrom(pbat.getMondayFloorRateWithTax(), tax));
            pbat.setMondayCeilingRate(removeTaxFrom(pbat.getMondayCeilingRateWithTax(), tax));

            pbat.setTuesdayFloorRate(removeTaxFrom(pbat.getTuesdayFloorRateWithTax(), tax));
            pbat.setTuesdayCeilingRate(tax.removeRoomTaxRate(pbat.getTuesdayCeilingRateWithTax()));

            pbat.setWednesdayFloorRate(removeTaxFrom(pbat.getWednesdayFloorRateWithTax(), tax));
            pbat.setWednesdayCeilingRate(removeTaxFrom(pbat.getWednesdayCeilingRateWithTax(), tax));

            pbat.setThursdayFloorRate(removeTaxFrom(pbat.getThursdayFloorRateWithTax(), tax));
            pbat.setThursdayCeilingRate(removeTaxFrom(pbat.getThursdayCeilingRateWithTax(), tax));

            pbat.setFridayFloorRate(removeTaxFrom(pbat.getFridayFloorRateWithTax(), tax));
            pbat.setFridayCeilingRate(removeTaxFrom(pbat.getFridayCeilingRateWithTax(), tax));

            pbat.setSaturdayFloorRate(removeTaxFrom(pbat.getSaturdayFloorRateWithTax(), tax));
            pbat.setSaturdayCeilingRate(removeTaxFrom(pbat.getSaturdayCeilingRateWithTax(), tax));
        }
    }

    private void updateGroupAccomTypeSeasonsForTax(PricingBaseAccomType groupPricingAccomTypeSeason, Map<LocalDate, Tax> taxForDateRange) {
        LocalDate startDate = groupPricingAccomTypeSeason.getStartDate();
        LocalDate endDate = groupPricingAccomTypeSeason.getEndDate();
        List<LocalDate> seasonDates = DateUtil.datesBetweenInclusive(startDate, endDate);
        for (LocalDate date : seasonDates) {
            Tax tax = taxForDateRange.get(date);
            int dayOfWeek = date.getDayOfWeek();
            if (DayOfWeek.FRIDAY.getCalendarDayOfWeek() == dayOfWeek) {
                groupPricingAccomTypeSeason.setFridayFloorRate(removeTaxFrom(groupPricingAccomTypeSeason.getFridayFloorRateWithTax(), tax));
                groupPricingAccomTypeSeason.setFridayCeilingRate(removeTaxFrom(groupPricingAccomTypeSeason.getFridayCeilingRateWithTax(), tax));
            } else if (DayOfWeek.SATURDAY.getCalendarDayOfWeek() == dayOfWeek) {
                groupPricingAccomTypeSeason.setSaturdayFloorRate(removeTaxFrom(groupPricingAccomTypeSeason.getSaturdayFloorRateWithTax(), tax));
                groupPricingAccomTypeSeason.setSaturdayCeilingRate(removeTaxFrom(groupPricingAccomTypeSeason.getSaturdayCeilingRateWithTax(), tax));
            } else if (DayOfWeek.SUNDAY.getCalendarDayOfWeek() == dayOfWeek) {
                groupPricingAccomTypeSeason.setSundayFloorRate(removeTaxFrom(groupPricingAccomTypeSeason.getSundayFloorRateWithTax(), tax));
                groupPricingAccomTypeSeason.setSundayCeilingRate(removeTaxFrom(groupPricingAccomTypeSeason.getSundayCeilingRateWithTax(), tax));
            } else if (DayOfWeek.MONDAY.getCalendarDayOfWeek() == dayOfWeek) {
                groupPricingAccomTypeSeason.setMondayFloorRate(removeTaxFrom(groupPricingAccomTypeSeason.getMondayFloorRateWithTax(), tax));
                groupPricingAccomTypeSeason.setMondayCeilingRate(removeTaxFrom(groupPricingAccomTypeSeason.getMondayCeilingRateWithTax(), tax));
            } else if (DayOfWeek.TUESDAY.getCalendarDayOfWeek() == dayOfWeek) {
                groupPricingAccomTypeSeason.setTuesdayFloorRate(removeTaxFrom(groupPricingAccomTypeSeason.getTuesdayFloorRateWithTax(), tax));
                groupPricingAccomTypeSeason.setTuesdayCeilingRate(removeTaxFrom(groupPricingAccomTypeSeason.getTuesdayCeilingRateWithTax(), tax));
            } else if (DayOfWeek.WEDNESDAY.getCalendarDayOfWeek() == dayOfWeek) {
                groupPricingAccomTypeSeason.setWednesdayFloorRate(removeTaxFrom(groupPricingAccomTypeSeason.getWednesdayFloorRateWithTax(), tax));
                groupPricingAccomTypeSeason.setWednesdayCeilingRate(removeTaxFrom(groupPricingAccomTypeSeason.getWednesdayCeilingRateWithTax(), tax));
            } else if (DayOfWeek.THURSDAY.getCalendarDayOfWeek() == dayOfWeek) {
                groupPricingAccomTypeSeason.setThursdayFloorRate(removeTaxFrom(groupPricingAccomTypeSeason.getThursdayFloorRateWithTax(), tax));
                groupPricingAccomTypeSeason.setThursdayCeilingRate(removeTaxFrom(groupPricingAccomTypeSeason.getThursdayCeilingRateWithTax(), tax));
            }
        }
    }

    private BigDecimal removeTaxFrom(BigDecimal withTaxValue, Tax tax) {
        return null != withTaxValue ? tax.removeRoomTaxRate(withTaxValue) : null;
    }

    private void updateDefaultAccomTypesForTaxAndSupplement(Tax tax, OccupancyType occupancyType, PricingBaseAccomType pricingBaseAccomType, Product product) {
        AccomTypeSupplement defaultSupp = accomTypeSupplementService.findSupplementByAccomTypeAndOccupancyTypeAndProduct(pricingBaseAccomType.getAccomType(), occupancyType, product);
        boolean isPercentage = false;
        if (ObjectUtils.isNotEmpty(defaultSupp) && OffsetMethod.PERCENTAGE.equals(defaultSupp.getOffsetMethod())) {
            isPercentage = true;
        }

        pricingBaseAccomType.setSundayFloorRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getSundayFloorRateWithTax(), tax, defaultSupp != null ? defaultSupp.getSundaySupplementValue() : BigDecimal.ZERO, isPercentage));
        pricingBaseAccomType.setSundayCeilingRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getSundayCeilingRateWithTax(), tax, defaultSupp != null ? defaultSupp.getSundaySupplementValue() : BigDecimal.ZERO, isPercentage));

        pricingBaseAccomType.setMondayFloorRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getMondayFloorRateWithTax(), tax, defaultSupp != null ? defaultSupp.getMondaySupplementValue() : BigDecimal.ZERO, isPercentage));
        pricingBaseAccomType.setMondayCeilingRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getMondayCeilingRateWithTax(), tax, defaultSupp != null ? defaultSupp.getMondaySupplementValue() : BigDecimal.ZERO, isPercentage));

        pricingBaseAccomType.setTuesdayFloorRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getTuesdayFloorRateWithTax(), tax, defaultSupp != null ? defaultSupp.getTuesdaySupplementValue() : BigDecimal.ZERO, isPercentage));
        pricingBaseAccomType.setTuesdayCeilingRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getTuesdayCeilingRateWithTax(), tax, defaultSupp != null ? defaultSupp.getTuesdaySupplementValue() : BigDecimal.ZERO, isPercentage));

        pricingBaseAccomType.setWednesdayFloorRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getWednesdayFloorRateWithTax(), tax, defaultSupp != null ? defaultSupp.getWednesdaySupplementValue() : BigDecimal.ZERO, isPercentage));
        pricingBaseAccomType.setWednesdayCeilingRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getWednesdayCeilingRateWithTax(), tax, defaultSupp != null ? defaultSupp.getWednesdaySupplementValue() : BigDecimal.ZERO, isPercentage));

        pricingBaseAccomType.setThursdayFloorRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getThursdayFloorRateWithTax(), tax, defaultSupp != null ? defaultSupp.getThursdaySupplementValue() : BigDecimal.ZERO, isPercentage));
        pricingBaseAccomType.setThursdayCeilingRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getThursdayCeilingRateWithTax(), tax, defaultSupp != null ? defaultSupp.getThursdaySupplementValue() : BigDecimal.ZERO, isPercentage));

        pricingBaseAccomType.setFridayFloorRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getFridayFloorRateWithTax(), tax, defaultSupp != null ? defaultSupp.getFridaySupplementValue() : BigDecimal.ZERO, isPercentage));
        pricingBaseAccomType.setFridayCeilingRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getFridayCeilingRateWithTax(), tax, defaultSupp != null ? defaultSupp.getFridaySupplementValue() : BigDecimal.ZERO, isPercentage));

        pricingBaseAccomType.setSaturdayFloorRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getSaturdayFloorRateWithTax(), tax, defaultSupp != null ? defaultSupp.getSaturdaySupplementValue() : BigDecimal.ZERO, isPercentage));
        pricingBaseAccomType.setSaturdayCeilingRate(removeTaxAndSupplementFrom(pricingBaseAccomType.getSaturdayCeilingRateWithTax(), tax, defaultSupp != null ? defaultSupp.getSaturdaySupplementValue() : BigDecimal.ZERO, isPercentage));
    }

    private void updateDefaultAccomTypesForTax(Tax tax, PricingBaseAccomType pricingBaseAccomType) {
        pricingBaseAccomType.setSundayFloorRate(removeTaxFrom(pricingBaseAccomType.getSundayFloorRateWithTax(), tax));
        pricingBaseAccomType.setSundayCeilingRate(removeTaxFrom(pricingBaseAccomType.getSundayCeilingRateWithTax(), tax));

        pricingBaseAccomType.setMondayFloorRate(removeTaxFrom(pricingBaseAccomType.getMondayFloorRateWithTax(), tax));
        pricingBaseAccomType.setMondayCeilingRate(removeTaxFrom(pricingBaseAccomType.getMondayCeilingRateWithTax(), tax));

        pricingBaseAccomType.setTuesdayFloorRate(removeTaxFrom(pricingBaseAccomType.getTuesdayFloorRateWithTax(), tax));
        pricingBaseAccomType.setTuesdayCeilingRate(removeTaxFrom(pricingBaseAccomType.getTuesdayCeilingRateWithTax(), tax));

        pricingBaseAccomType.setWednesdayFloorRate(removeTaxFrom(pricingBaseAccomType.getWednesdayFloorRateWithTax(), tax));
        pricingBaseAccomType.setWednesdayCeilingRate(removeTaxFrom(pricingBaseAccomType.getWednesdayCeilingRateWithTax(), tax));

        pricingBaseAccomType.setThursdayFloorRate(removeTaxFrom(pricingBaseAccomType.getThursdayFloorRateWithTax(), tax));
        pricingBaseAccomType.setThursdayCeilingRate(removeTaxFrom(pricingBaseAccomType.getThursdayCeilingRateWithTax(), tax));

        pricingBaseAccomType.setFridayFloorRate(removeTaxFrom(pricingBaseAccomType.getFridayFloorRateWithTax(), tax));
        pricingBaseAccomType.setFridayCeilingRate(removeTaxFrom(pricingBaseAccomType.getFridayCeilingRateWithTax(), tax));

        pricingBaseAccomType.setSaturdayFloorRate(removeTaxFrom(pricingBaseAccomType.getSaturdayFloorRateWithTax(), tax));
        pricingBaseAccomType.setSaturdayCeilingRate(removeTaxFrom(pricingBaseAccomType.getSaturdayCeilingRateWithTax(), tax));
    }

    public boolean isPerPersonPricingEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED.value());
    }

    public boolean isComponentRoomsPriceAsSumOfPartEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_PRICE_AS_SUM_OF_PARTS);
    }

    private BigDecimal nullToZero(BigDecimal value) {
        return value != null ? value : BigDecimal.ZERO;
    }

    private BigDecimal nullToOne(BigDecimal value) {
        return value != null ? value : BigDecimal.ONE;
    }

    private BigDecimal valueOrNull(BigDecimal value) {
        return value != null ? value : null;
    }

    public void updateTransientBaseAccomTypesForSupplementChange() {
        List<PricingBaseAccomType> transientPricingBaseAccomTypes = getAllTransientPricingBaseAccomTypes();

        if (CollectionUtils.isNotEmpty(transientPricingBaseAccomTypes)) {
            Product product = tenantCrudService.findByNamedQuerySingleResult(Product.GET_BY_ID,
                    QueryParameter.with("productId", transientPricingBaseAccomTypes.get(0).getProductID()).parameters());
            applySupplementsAndTax(transientPricingBaseAccomTypes, product);
            tenantCrudService.save(transientPricingBaseAccomTypes);
        }

        taxInclusiveMigrationService.handleTransientPriceExcludedBaseRoomTypeOnSupplementChange();
    }

    public void updateGroupBaseAccomTypesForSupplementChange() {
        List<PricingBaseAccomType> groupPricingBaseAccomTypes = getAllGroupPricingBaseAccomTypes();

        if (CollectionUtils.isNotEmpty(groupPricingBaseAccomTypes)) {
            applySupplementsAndTaxGroupPricingBaseAccomTypes(groupPricingBaseAccomTypes);
            tenantCrudService.save(groupPricingBaseAccomTypes);
        }

        taxInclusiveMigrationService.handleGroupPriceExcludedBaseRoomTypeOnSupplementChangeToggle();
    }

    /**
     * @param referencedPricingBaseAccomType
     * @param nonBaseRoomTypes               -> in this flow we are expecting only non-base accomTypes
     * @return
     */
    public List<PricingBaseAccomType> getPricingNonBaseAccomTypesUsing(List<PricingBaseAccomType> referencedPricingBaseAccomType, List<AccomType> nonBaseRoomTypes) {
        Map<AccomClass, PricingBaseAccomType> pricingBaseAccomTypesByAccomClass = referencedPricingBaseAccomType
                .stream()
                .collect(Collectors.toMap(pricingBaseAccomType -> pricingBaseAccomType.getAccomType().getAccomClass()
                        , pricingBaseAccomType -> pricingBaseAccomType,
                        (value1, value2) -> value2));
        List<AccomTypeSupplement> accomTypeSupplements = findDefaultSupplementsForBaseOccupancyTypes();
        Map<AccomType, AccomTypeSupplement> supplementsByAccomType = accomTypeSupplements
                .stream()
                .collect(Collectors.toMap(AccomTypeSupplement::getAccomType, accomTypeSupplement -> accomTypeSupplement, (value1, value2) -> value2));

        List<CPConfigOffsetAccomType> defaultOffsetsForBaseOccupancyTypes = findDefaultOffsetsForBaseOccupancyTypes();
        Map<AccomType, CPConfigOffsetAccomType> offsetsByAccomTypes = defaultOffsetsForBaseOccupancyTypes
                .stream()
                .collect(Collectors.toMap(CPConfigOffsetAccomType::getAccomType, offsetAccomType -> offsetAccomType, (value1, value2) -> value2));

        List<AccomClass> priceExcludeAccomClasses = getExcludedPricingAccomClass().stream()
                .map(PricingAccomClass::getAccomClass)
                .collect(toList());

        List<PricingBaseAccomType> relatedNonBaseCeilingFloors = new ArrayList<>();
        for (AccomType nonBaseRoomType : nonBaseRoomTypes) {
            PricingBaseAccomType pricingBaseAccomTypes = pricingBaseAccomTypesByAccomClass.get(nonBaseRoomType.getAccomClass());
            if (pricingBaseAccomTypes != null) {
                AccomType baseAccomType = pricingBaseAccomTypes.getAccomType();
                PricingBaseAccomType nonBaseAccomTypePricing = pricingBaseAccomTypes.clone();
                nonBaseAccomTypePricing.setAccomType(nonBaseRoomType);
                CPConfigOffsetAccomType nonBaseRTSingleOffset = offsetsByAccomTypes.getOrDefault(nonBaseRoomType, new CPConfigOffsetAccomType());
                if (priceExcludeAccomClasses.contains(nonBaseRoomType.getAccomClass())) {
                    updateCeilingFloorsForPriceExcludedRC(nonBaseAccomTypePricing, nonBaseRTSingleOffset);
                } else {
                    updateCeilingFloorsForNonPriceExcludedRC(nonBaseAccomTypePricing, nonBaseRTSingleOffset, supplementsByAccomType.getOrDefault(baseAccomType, new AccomTypeSupplement()), supplementsByAccomType.getOrDefault(nonBaseRoomType, new AccomTypeSupplement()));
                }
                relatedNonBaseCeilingFloors.add(nonBaseAccomTypePricing);
            }
        }
        return relatedNonBaseCeilingFloors;
    }

    private List<AccomTypeSupplement> findDefaultSupplementsForBaseOccupancyTypes() {
        OccupancyType baseOccupancyType = getBaseOccupancyType();
        List<AccomTypeSupplement> defaultSupplments = accomTypeSupplementService.findDefaultSupplements();
        if (CollectionUtils.isNotEmpty(defaultSupplments)) {
            return defaultSupplments.stream()
                    .filter(supplement -> baseOccupancyType.equals(supplement.getOccupancyType()))
                    .collect(toList());
        }
        return new ArrayList<>();
    }

    private List<CPConfigOffsetAccomType> findDefaultOffsetsForBaseOccupancyTypes() {
        OccupancyType baseOccupancyType = getBaseOccupancyType();
        List<CPConfigOffsetAccomType> defaultOffsets = findDefaultOffsets();
        if (CollectionUtils.isNotEmpty(defaultOffsets)) {
            return defaultOffsets.stream()
                    .filter(offsetAccomType -> baseOccupancyType.equals(offsetAccomType.getOccupancyType()))
                    .collect(toList());
        }
        return new ArrayList<>();
    }

    private void updateCeilingFloorsForPriceExcludedRC(PricingBaseAccomType nonBaseAccomTypePricing, CPConfigOffsetAccomType nonBaseRTSingleOffset) {
        nonBaseAccomTypePricing.setMondayFloorRateWithTax(nonBaseRTSingleOffset.getMondayOffsetValueWithTax());
        nonBaseAccomTypePricing.setMondayCeilingRateWithTax(nonBaseRTSingleOffset.getMondayOffsetValueWithTax());
        nonBaseAccomTypePricing.setTuesdayFloorRateWithTax(nonBaseRTSingleOffset.getTuesdayOffsetValueWithTax());
        nonBaseAccomTypePricing.setTuesdayCeilingRateWithTax(nonBaseRTSingleOffset.getTuesdayOffsetValueWithTax());
        nonBaseAccomTypePricing.setWednesdayFloorRateWithTax(nonBaseRTSingleOffset.getWednesdayOffsetValueWithTax());
        nonBaseAccomTypePricing.setWednesdayCeilingRateWithTax(nonBaseRTSingleOffset.getWednesdayOffsetValueWithTax());
        nonBaseAccomTypePricing.setThursdayFloorRateWithTax(nonBaseRTSingleOffset.getThursdayOffsetValueWithTax());
        nonBaseAccomTypePricing.setThursdayCeilingRateWithTax(nonBaseRTSingleOffset.getThursdayOffsetValueWithTax());
        nonBaseAccomTypePricing.setFridayFloorRateWithTax(nonBaseRTSingleOffset.getFridayOffsetValueWithTax());
        nonBaseAccomTypePricing.setFridayCeilingRateWithTax(nonBaseRTSingleOffset.getFridayOffsetValueWithTax());
        nonBaseAccomTypePricing.setSaturdayFloorRateWithTax(nonBaseRTSingleOffset.getSaturdayOffsetValueWithTax());
        nonBaseAccomTypePricing.setSaturdayCeilingRateWithTax(nonBaseRTSingleOffset.getSaturdayOffsetValueWithTax());
        nonBaseAccomTypePricing.setSundayFloorRateWithTax(nonBaseRTSingleOffset.getSundayOffsetValueWithTax());
        nonBaseAccomTypePricing.setSundayCeilingRateWithTax(nonBaseRTSingleOffset.getSundayOffsetValueWithTax());
    }

    private void updateCeilingFloorsForNonPriceExcludedRC(PricingBaseAccomType nonBaseAccomTypePricing, CPConfigOffsetAccomType nonBaseRTSingleOffset, AccomTypeSupplement baseRTSingleSupplement, AccomTypeSupplement nonBaseRTSingleSupplement) {
        addSupplement(nonBaseAccomTypePricing, baseRTSingleSupplement, nonBaseRTSingleSupplement);
        addOffset(nonBaseAccomTypePricing, nonBaseRTSingleOffset);
    }

    private void addOffset(PricingBaseAccomType nonBaseAccomTypePricing, CPConfigOffsetAccomType nonBaseRTSingleOffset) {
        OffsetMethod offsetMethod = nonBaseRTSingleOffset.getOffsetMethod();
        nonBaseAccomTypePricing.setMondayFloorRateWithTax(addOffsetValueTo(nonBaseAccomTypePricing.getMondayFloorRateWithTax(), nonBaseRTSingleOffset.getMondayOffsetValueWithTax(), offsetMethod));
        nonBaseAccomTypePricing.setMondayCeilingRateWithTax(addOffsetValueTo(nonBaseAccomTypePricing.getMondayCeilingRateWithTax(), nonBaseRTSingleOffset.getMondayOffsetValueWithTax(), offsetMethod));
        nonBaseAccomTypePricing.setTuesdayFloorRateWithTax(addOffsetValueTo(nonBaseAccomTypePricing.getTuesdayFloorRateWithTax(), nonBaseRTSingleOffset.getTuesdayOffsetValueWithTax(), offsetMethod));
        nonBaseAccomTypePricing.setTuesdayCeilingRateWithTax(addOffsetValueTo(nonBaseAccomTypePricing.getTuesdayCeilingRateWithTax(), nonBaseRTSingleOffset.getTuesdayOffsetValueWithTax(), offsetMethod));
        nonBaseAccomTypePricing.setWednesdayFloorRateWithTax(addOffsetValueTo(nonBaseAccomTypePricing.getWednesdayFloorRateWithTax(), nonBaseRTSingleOffset.getWednesdayOffsetValueWithTax(), offsetMethod));
        nonBaseAccomTypePricing.setWednesdayCeilingRateWithTax(addOffsetValueTo(nonBaseAccomTypePricing.getWednesdayCeilingRateWithTax(), nonBaseRTSingleOffset.getWednesdayOffsetValueWithTax(), offsetMethod));
        nonBaseAccomTypePricing.setThursdayFloorRateWithTax(addOffsetValueTo(nonBaseAccomTypePricing.getThursdayFloorRateWithTax(), nonBaseRTSingleOffset.getThursdayOffsetValueWithTax(), offsetMethod));
        nonBaseAccomTypePricing.setThursdayCeilingRateWithTax(addOffsetValueTo(nonBaseAccomTypePricing.getThursdayCeilingRateWithTax(), nonBaseRTSingleOffset.getThursdayOffsetValueWithTax(), offsetMethod));
        nonBaseAccomTypePricing.setFridayFloorRateWithTax(addOffsetValueTo(nonBaseAccomTypePricing.getFridayFloorRateWithTax(), nonBaseRTSingleOffset.getFridayOffsetValueWithTax(), offsetMethod));
        nonBaseAccomTypePricing.setFridayCeilingRateWithTax(addOffsetValueTo(nonBaseAccomTypePricing.getFridayCeilingRateWithTax(), nonBaseRTSingleOffset.getFridayOffsetValueWithTax(), offsetMethod));
        nonBaseAccomTypePricing.setSaturdayFloorRateWithTax(addOffsetValueTo(nonBaseAccomTypePricing.getSaturdayFloorRateWithTax(), nonBaseRTSingleOffset.getSaturdayOffsetValueWithTax(), offsetMethod));
        nonBaseAccomTypePricing.setSaturdayCeilingRateWithTax(addOffsetValueTo(nonBaseAccomTypePricing.getSaturdayCeilingRateWithTax(), nonBaseRTSingleOffset.getSaturdayOffsetValueWithTax(), offsetMethod));
        nonBaseAccomTypePricing.setSundayFloorRateWithTax(addOffsetValueTo(nonBaseAccomTypePricing.getSundayFloorRateWithTax(), nonBaseRTSingleOffset.getSundayOffsetValueWithTax(), offsetMethod));
        nonBaseAccomTypePricing.setSundayCeilingRateWithTax(addOffsetValueTo(nonBaseAccomTypePricing.getSundayCeilingRateWithTax(), nonBaseRTSingleOffset.getSundayOffsetValueWithTax(), offsetMethod));
    }

    private void addSupplement(PricingBaseAccomType nonBaseAccomTypePricing, AccomTypeSupplement baseRTSingleSupplement, AccomTypeSupplement nonBaseRTSingleSupplement) {
        nonBaseAccomTypePricing.setMondayFloorRateWithTax(BigDecimalUtil.add(nonBaseAccomTypePricing.getMondayFloorRateWithTax(), BigDecimalUtil.subtract(nonBaseRTSingleSupplement.getMondaySupplementValue(), baseRTSingleSupplement.getMondaySupplementValue())));
        nonBaseAccomTypePricing.setMondayCeilingRateWithTax(BigDecimalUtil.add(nonBaseAccomTypePricing.getMondayCeilingRateWithTax(), BigDecimalUtil.subtract(nonBaseRTSingleSupplement.getMondaySupplementValue(), baseRTSingleSupplement.getMondaySupplementValue())));
        nonBaseAccomTypePricing.setTuesdayFloorRateWithTax(BigDecimalUtil.add(nonBaseAccomTypePricing.getTuesdayFloorRateWithTax(), BigDecimalUtil.subtract(nonBaseRTSingleSupplement.getTuesdaySupplementValue(), baseRTSingleSupplement.getTuesdaySupplementValue())));
        nonBaseAccomTypePricing.setTuesdayCeilingRateWithTax(BigDecimalUtil.add(nonBaseAccomTypePricing.getTuesdayCeilingRateWithTax(), BigDecimalUtil.subtract(nonBaseRTSingleSupplement.getTuesdaySupplementValue(), baseRTSingleSupplement.getTuesdaySupplementValue())));
        nonBaseAccomTypePricing.setWednesdayFloorRateWithTax(BigDecimalUtil.add(nonBaseAccomTypePricing.getWednesdayFloorRateWithTax(), BigDecimalUtil.subtract(nonBaseRTSingleSupplement.getWednesdaySupplementValue(), baseRTSingleSupplement.getWednesdaySupplementValue())));
        nonBaseAccomTypePricing.setWednesdayCeilingRateWithTax(BigDecimalUtil.add(nonBaseAccomTypePricing.getWednesdayCeilingRateWithTax(), BigDecimalUtil.subtract(nonBaseRTSingleSupplement.getWednesdaySupplementValue(), baseRTSingleSupplement.getWednesdaySupplementValue())));
        nonBaseAccomTypePricing.setThursdayFloorRateWithTax(BigDecimalUtil.add(nonBaseAccomTypePricing.getThursdayFloorRateWithTax(), BigDecimalUtil.subtract(nonBaseRTSingleSupplement.getThursdaySupplementValue(), baseRTSingleSupplement.getThursdaySupplementValue())));
        nonBaseAccomTypePricing.setThursdayCeilingRateWithTax(BigDecimalUtil.add(nonBaseAccomTypePricing.getThursdayCeilingRateWithTax(), BigDecimalUtil.subtract(nonBaseRTSingleSupplement.getThursdaySupplementValue(), baseRTSingleSupplement.getThursdaySupplementValue())));
        nonBaseAccomTypePricing.setFridayFloorRateWithTax(BigDecimalUtil.add(nonBaseAccomTypePricing.getFridayFloorRateWithTax(), BigDecimalUtil.subtract(nonBaseRTSingleSupplement.getFridaySupplementValue(), baseRTSingleSupplement.getFridaySupplementValue())));
        nonBaseAccomTypePricing.setFridayCeilingRateWithTax(BigDecimalUtil.add(nonBaseAccomTypePricing.getFridayCeilingRateWithTax(), BigDecimalUtil.subtract(nonBaseRTSingleSupplement.getFridaySupplementValue(), baseRTSingleSupplement.getFridaySupplementValue())));
        nonBaseAccomTypePricing.setSaturdayFloorRateWithTax(BigDecimalUtil.add(nonBaseAccomTypePricing.getSaturdayFloorRateWithTax(), BigDecimalUtil.subtract(nonBaseRTSingleSupplement.getSaturdaySupplementValue(), baseRTSingleSupplement.getSaturdaySupplementValue())));
        nonBaseAccomTypePricing.setSaturdayCeilingRateWithTax(BigDecimalUtil.add(nonBaseAccomTypePricing.getSaturdayCeilingRateWithTax(), BigDecimalUtil.subtract(nonBaseRTSingleSupplement.getSaturdaySupplementValue(), baseRTSingleSupplement.getSaturdaySupplementValue())));
        nonBaseAccomTypePricing.setSundayFloorRateWithTax(BigDecimalUtil.add(nonBaseAccomTypePricing.getSundayFloorRateWithTax(), BigDecimalUtil.subtract(nonBaseRTSingleSupplement.getSundaySupplementValue(), baseRTSingleSupplement.getSundaySupplementValue())));
        nonBaseAccomTypePricing.setSundayCeilingRateWithTax(BigDecimalUtil.add(nonBaseAccomTypePricing.getSundayCeilingRateWithTax(), BigDecimalUtil.subtract(nonBaseRTSingleSupplement.getSundaySupplementValue(), baseRTSingleSupplement.getSundaySupplementValue())));
    }

    public List<WebrateCompetitors> getWebrateCompetitors() {
        return tenantCrudService.findAll(WebrateCompetitors.class);
    }

    @VisibleForTesting
	public
    BigDecimal addOffsetValueTo(BigDecimal price, BigDecimal offsetValue, OffsetMethod offsetMethod) {
        if (null != price && offsetValue != null) {
            if (OffsetMethod.FIXED_OFFSET.equals(offsetMethod)) {
                return BigDecimalUtil.add(price, offsetValue);
            } else if (OffsetMethod.PERCENTAGE.equals(offsetMethod)) {
                BigDecimal offsetPercentageMultiplier = BigDecimalUtil.divide(offsetValue, BigDecimalUtil.ONE_HUNDRED, 5);
                return BigDecimalUtil.multiply(price, BigDecimal.ONE.add(offsetPercentageMultiplier), 5);
            }
        }
        return price;
    }

    @ForTesting


    @SuppressWarnings("unchecked")
    public void deleteChildAgeGroups() {
        tenantCrudService.deleteAll(OccupantBucketEntity.class);
    }

    @ForTesting


    @SuppressWarnings("unchecked")
    public void deleteMaximumOccupants() {
        tenantCrudService.deleteAll(MaximumOccupantsEntity.class);
    }

    public boolean isSupplementEnabled() {
        return configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)
                && configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_SUPPLEMENTAL_ENABLED)
                && findCPConfiguration().isEnableSupplements();
    }

    public void configureCurrentConfigurationsUsingDraftValues(PricingConfigurationDTO pricingConfigurationDTO) {
        if(isNull(pricingConfigurationDTO.getIndependentProductDTO()) || isNull(pricingConfigurationDTO.getIndependentProductDTO().getProduct())){
            return;
        }
        boolean isCentralRmsProperty = isCentralRmsProperty();
        List<PricingBaseAccomType> configsBeforeDraftCopy = Collections.emptyList();
        if (isCentralRmsProperty) {
            configsBeforeDraftCopy = getAllTransientPricingBaseAccomTypes();
        }

        Integer productId = pricingConfigurationDTO.getProduct().getId();
        Product product = tenantCrudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
        tenantCrudService.executeUpdateByNativeQuery(DELETE_CEIL_FLOOR, QueryParameter.with("productId", productId).parameters());
        tenantCrudService.executeUpdateByNativeQuery(QUERY_TO_COPY_DRAFT_CONFIGURATIONS_TO_CURRENT_CONFIGURATIONS, QueryParameter.with("productId", productId).parameters());

        pricingConfigurationLTBDEService.enabledLTBDEIfApplicable();
        if (Objects.equals(product.getId(),productId) && Boolean.TRUE.equals(isSyncGPCeilingFloorWithPrimaryPricedProductEnabled())) {
            copyFromPricingCeilingFloorToGPCeilingFloor();
        }

        // tl;dr - native queries bypass all entity listeners, e.g., auditing and custom ones.
        // Central RMS depends on things not bypassing entity listeners
        if (isCentralRmsProperty) {
            List<PricingBaseAccomType> toDelete = configsBeforeDraftCopy;
            List<PricingBaseAccomType> toSync = getAllTransientPricingBaseAccomTypes();
            String clientCode = PacmanWorkContextHelper.getClientCode();
            String propertyCode = PacmanWorkContextHelper.getPropertyCode();

            try {
                resyncPricingConfigsToCentralRMSWorkAround(toDelete, toSync);
            } catch (Exception e) {
                log.error("Central RMS: Failed to sync persisted draft data for client: {} property: {}", clientCode, propertyCode, e);
            }
        }
    }

    public boolean isTransientFloorCeilingDraftAvailableByProduct(Integer productId) {
        long countOfDraftValues = tenantCrudService.findByNamedQuerySingleResult(TransientPricingBaseAccomTypeDraft.GET_COUNT_BY_PRODUCT,
                QueryParameter.with("productId", productId).parameters());
        return countOfDraftValues > 0;
    }

    public boolean isTransientFloorCeilingDraftAvailable() {
        long countOfDraftValues = tenantCrudService.findByNamedQuerySingleResult(TransientPricingBaseAccomTypeDraft.GET_COUNT);
        return countOfDraftValues > 0;
    }

    public void deleteDraftValuesByProduct(Integer productId) {
        tenantCrudService.executeUpdateByNamedQuery(TransientPricingBaseAccomTypeDraft.DELETE_BY_PRODUCT_ID, QueryParameter.with("productId", productId).parameters());
    }

    @ForTesting


    @SuppressWarnings("unchecked")
    public void setStatusForPriceAsSumOfPartRoomTypes(String status,
                                                      String accomType) {
        tenantCrudService.executeUpdateByNativeQuery("update [dbo].[Accom_Type] set isComponentRoom = '" + status + "' where Accom_Type_Name = '" + accomType + "'");
    }

    public PricingBaseAccomType saveEmptyPricingBaseAccomTypeDraft(PricingAccomClass pricingAccomClass, PricingBaseAccomType existingSeason) {
        PricingBaseAccomType pricingBaseAccomType = new TransientPricingBaseAccomTypeDraft();
        pricingBaseAccomType.setProductID(existingSeason.getProductID());
        pricingBaseAccomType.setSeasonName(existingSeason.getSeasonName());
        pricingBaseAccomType.setStartDate(existingSeason.getStartDate());
        pricingBaseAccomType.setEndDate(existingSeason.getEndDate());
        pricingBaseAccomType.setPropertyId(pricingAccomClass.getPropertyId());
        pricingBaseAccomType.setAccomType(pricingAccomClass.getAccomType());
        pricingBaseAccomType.setStatus(Status.ACTIVE);
        return tenantCrudService.save(pricingBaseAccomType);
    }

    public List<PricingBaseAccomType> getFutureSeasonsInDraft(Integer productId) {
        return filterPastSeasons(getTransientPricingBaseAccomTypeSeasonsInDraft(productId));
    }

    private List<PricingBaseAccomType> filterPastSeasons(List<PricingBaseAccomType> seasonsInDraft) {
        LocalDate businessDate = new LocalDate(dateService.getCaughtUpDate());
        return seasonsInDraft.stream().filter(p -> !p.getEndDate().isBefore(businessDate)).collect(toList());
    }

    public List<PricingBaseAccomType> getFutureTransientPricingAccomTypes(Product product) {
        return filterPastSeasons(getTransientPricingBaseAccomTypeSeasonsWithPriceExcluded(product));
    }


    public void updateDataForHiltonCpMigration() {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        CurrencyExchangeService.ExchangeRate exchangeRate = currencyExchangeService.getExchangeRateWrtUSD();
        updateMinPriceDifferential(BigDecimal.TEN, Integer.valueOf(Objects.requireNonNull(PacmanWorkContextHelper.getUserId())), exchangeRate);
        updateMinimumChangeMethodAndValue(propertyId, exchangeRate);
        updateBarRoundingRules(propertyId);
    }

    @VisibleForTesting
	public
    void updateMinPriceDifferential(BigDecimal withTaxValue, Integer userId, CurrencyExchangeService.ExchangeRate exchangeRate) {
        BigDecimal convertedValue = exchangeRate.apply(withTaxValue);
        BigDecimal withoutTaxValue = getTaxExclusiveValue(convertedValue);
        tenantCrudService.executeUpdateByNativeQuery("delete from Accom_Class_MinDiff");
        tenantCrudService.executeUpdateByNativeQuery("INSERT INTO [dbo].[Accom_Class_MinDiff] SELECT [Accom_Class_PriceRank_Path_ID], :withoutTaxValue, " +
                ":withoutTaxValue, :withoutTaxValue, :withoutTaxValue, :withoutTaxValue, :withoutTaxValue, :withoutTaxValue," + userId + ", GETDATE()," + userId + ", GETDATE(), " +
                "NULL, NULL, NULL, NULL, NULL, NULL, NULL" +
                " FROM [dbo].[Accom_Class_PriceRank_Path]", QueryParameter.with("withoutTaxValue", withoutTaxValue).parameters());
    }

    private BigDecimal getTaxExclusiveValue(BigDecimal withTaxValue) {
        Tax tax = taxService.findTax();
        return tax.removeRoomTaxRate(withTaxValue);
    }

    @VisibleForTesting
	public
    void updateMinimumChangeMethodAndValue(Integer propertyId, CurrencyExchangeService.ExchangeRate exchangeRate) {
        BigDecimal defaultMinChangeValue = globalCrudService.findByNamedQuerySingleResult(BrandCodeServiceTypeMapping.GET_DEFAULT_MIN_CHANGE_VALUE_BY_PROPERTY_ID
                , QueryParameter.with("propertyId", propertyId).parameters());

        if (Objects.isNull(defaultMinChangeValue)) {
            throw new TetrisException("No Brand Code Mapping found for propertyId: " + propertyId);
        }

        BigDecimal minIncrementValue = exchangeRate.apply(defaultMinChangeValue);
        minIncrementValue = roundOffToNearestWholeNumber(minIncrementValue);
        updateMinimumChangeMethodAndValueByPropertyId(propertyId, MinimumIncrementMethod.FIXED_OFFSET, minIncrementValue);
    }


    private BigDecimal roundOffToNearestWholeNumber(BigDecimal value) {
        value = value.setScale(0, RoundingMode.HALF_UP);
        return value.setScale(2, RoundingMode.HALF_UP);
    }


    //use this rest call when you want to populate accom_class_mindiff separately
    public void updateMinPriceDifferential(BigDecimal defaultMinPriceDiff, Integer userId) {
        updateMinPriceDifferential(defaultMinPriceDiff, userId, currencyExchangeService.getExchangeRateWrtUSD());
    }


    //use this rest call when you want to populate cp_cfg_ac separately
    public void updateMinimumChangeMethodAndValue(Integer propertyId) {
        updateMinimumChangeMethodAndValue(propertyId, currencyExchangeService.getExchangeRateWrtUSD());
    }

    private void updateMinimumChangeMethodAndValueByPropertyId(Integer propertyId, MinimumIncrementMethod minimumIncrementMethod, BigDecimal minimumIncrementValue) {
        tenantCrudService.executeUpdateByNamedQuery(PricingAccomClass.UPDATE_MIN_INC_METHOD_AND_VALUE, QueryParameter.with("incrementMethod", minimumIncrementMethod)
                .and("incrementValue", minimumIncrementValue).and("propertyId", propertyId).and("userId", Integer.valueOf(Objects.requireNonNull(PacmanWorkContextHelper.getUserId()))).and("currentTimeStamp", LocalDateTime.now()).parameters());
    }


    public void updateBarRoundingRules(Integer propertyId) {
        tenantCrudService.executeUpdateByNamedQuery(PrettyPricingRuleRow.DELETE_PRETTY_PRICE_RULES_FOR_PROPERTY, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
        PrettyPricingRuleRow prettyPricingRuleTenth = createPrettyPricingRule(propertyId, PricingDigit.TENTHS);
        PrettyPricingRuleRow prettyPricingRuleHundredth = createPrettyPricingRule(propertyId, PricingDigit.HUNDREDTHS);
        tenantCrudService.save(prettyPricingRuleTenth);
        tenantCrudService.save(prettyPricingRuleHundredth);
    }

    private PrettyPricingRuleRow createPrettyPricingRule(Integer propertyId, PricingDigit pricingDigit) {
        PrettyPricingRuleRow prettyPricingRuleRow = new PrettyPricingRuleRow();
        prettyPricingRuleRow.setRuleNumbers("0");
        prettyPricingRuleRow.setPropertyId(propertyId);
        //Hard coded to BAR as this is only used for CP Migration
        prettyPricingRuleRow.setProductId(1);
        prettyPricingRuleRow.setDigit(pricingDigit);
        return prettyPricingRuleRow;
    }

    public boolean isAnyPricingAccomClassConfigured() {
        long pricingAccomClasses = tenantCrudService.findByNamedQuerySingleResult(PricingAccomClass.FIND_COUNT_PRICING_ACCOM_CLASSES);
        return pricingAccomClasses > 0;
    }

    public void handleMigrationForPerPersonPricing() {
        Tax tax = taxService.findTax();
        List<CPConfigOffsetAccomType> allOffsets = tenantCrudService.findAll(CPConfigOffsetAccomType.class);
        List<CPConfigOffsetAccomType> defaultOffsets = allOffsets.stream()
                .filter(offset -> offset.getStartDate() == null)
                .collect(toList());
        Map<Integer, PricingRule> pricingRules = getPrettyPricingRules();

        List<TransientPricingBaseAccomType> allTransientFloorCeiling = tenantCrudService.findAll(TransientPricingBaseAccomType.class);

        //Adjust Default Floor and Ceiling Values based on Default Offsets
        adjustDefaultFloorAndCeilingValues(tax, defaultOffsets, allTransientFloorCeiling, pricingRules);

        //Adjust Default Offsets based on Base/Non-Base Room Type
        adjustDefaultAndSeasonOffsets(tax, allOffsets, pricingRules);
    }

    public void adjustDefaultFloorAndCeilingValues(Tax tax, List<CPConfigOffsetAccomType> defaultOffsets, List<TransientPricingBaseAccomType> allTransientFloorCeiling, Map<Integer, PricingRule> pricingRules) {
        //Iterate over all default CP_Cfg_Base_AT values (since Price Excluded for the Base AT is stored here as well it will cover Price Excluded as well)
        allTransientFloorCeiling.stream()
                .forEach(tpbat -> {
                    //Find the offset corresponding to the AccomType and the Occupancy Type Double for the offset adjustment
                    CPConfigOffsetAccomType cpConfigOffsetAccomType = defaultOffsets.stream()
                            .filter(defaultOffset -> defaultOffset.getStatusId().equals(Status.ACTIVE.getId()))
                            .filter(defaultOffset -> defaultOffset.getAccomType().equals(tpbat.getAccomType()))
                            .filter(defaultOffset -> defaultOffset.getOccupancyType().equals(OccupancyType.DOUBLE))
                            .findFirst()
                            .orElse(null);
                    PricingRule pricingRule = pricingRules.get(tpbat.getProductID());
                    updateNewPPPRatesForDOW(tax, tpbat, cpConfigOffsetAccomType, pricingRule);
                });

        tenantCrudService.save(allTransientFloorCeiling);
    }

    public void updateNewPPPRatesForDOW(Tax tax, TransientPricingBaseAccomType tpbat, CPConfigOffsetAccomType offset, PricingRule pricingRule) {
        //Sunday Floor/Ceiling Rate Adjustment
        calculateNewPPPRate(tax, tpbat.getSundayFloorRateWithTax(), offset != null ? offset.getSundayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setSundayFloorRate, TransientPricingBaseAccomType::setSundayFloorRateWithTax, pricingRule);
        calculateNewPPPRate(tax, tpbat.getSundayCeilingRateWithTax(), offset != null ? offset.getSundayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setSundayCeilingRate, TransientPricingBaseAccomType::setSundayCeilingRateWithTax, pricingRule);

        //Monday Floor/Ceiling Rate Adjustment
        calculateNewPPPRate(tax, tpbat.getMondayFloorRateWithTax(), offset != null ? offset.getMondayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setMondayFloorRate, TransientPricingBaseAccomType::setMondayFloorRateWithTax, pricingRule);
        calculateNewPPPRate(tax, tpbat.getMondayCeilingRateWithTax(), offset != null ? offset.getMondayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setMondayCeilingRate, TransientPricingBaseAccomType::setMondayCeilingRateWithTax, pricingRule);

        //Tuesday Floor/Ceiling Rate Adjustment
        calculateNewPPPRate(tax, tpbat.getTuesdayFloorRateWithTax(), offset != null ? offset.getTuesdayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setTuesdayFloorRate, TransientPricingBaseAccomType::setTuesdayFloorRateWithTax, pricingRule);
        calculateNewPPPRate(tax, tpbat.getTuesdayCeilingRateWithTax(), offset != null ? offset.getTuesdayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setTuesdayCeilingRate, TransientPricingBaseAccomType::setTuesdayCeilingRateWithTax, pricingRule);

        //Wednesday Floor/Ceiling Rate Adjustment
        calculateNewPPPRate(tax, tpbat.getWednesdayFloorRateWithTax(), offset != null ? offset.getWednesdayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setWednesdayFloorRate, TransientPricingBaseAccomType::setWednesdayFloorRateWithTax, pricingRule);
        calculateNewPPPRate(tax, tpbat.getWednesdayCeilingRateWithTax(), offset != null ? offset.getWednesdayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setWednesdayCeilingRate, TransientPricingBaseAccomType::setWednesdayCeilingRateWithTax, pricingRule);

        //Thursday Floor/Ceiling Rate Adjustment
        calculateNewPPPRate(tax, tpbat.getThursdayFloorRateWithTax(), offset != null ? offset.getThursdayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setThursdayFloorRate, TransientPricingBaseAccomType::setThursdayFloorRateWithTax, pricingRule);
        calculateNewPPPRate(tax, tpbat.getThursdayCeilingRateWithTax(), offset != null ? offset.getThursdayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setThursdayCeilingRate, TransientPricingBaseAccomType::setThursdayCeilingRateWithTax, pricingRule);

        //Friday Floor/Ceiling Rate Adjustment
        calculateNewPPPRate(tax, tpbat.getFridayFloorRateWithTax(), offset != null ? offset.getFridayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setFridayFloorRate, TransientPricingBaseAccomType::setFridayFloorRateWithTax, pricingRule);
        calculateNewPPPRate(tax, tpbat.getFridayCeilingRateWithTax(), offset != null ? offset.getFridayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setFridayCeilingRate, TransientPricingBaseAccomType::setFridayCeilingRateWithTax, pricingRule);

        //Saturday Floor/Ceiling Rate Adjustment
        calculateNewPPPRate(tax, tpbat.getSaturdayFloorRateWithTax(), offset != null ? offset.getSaturdayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setSaturdayFloorRate, TransientPricingBaseAccomType::setSaturdayFloorRateWithTax, pricingRule);
        calculateNewPPPRate(tax, tpbat.getSaturdayCeilingRateWithTax(), offset != null ? offset.getSaturdayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setSaturdayCeilingRate, TransientPricingBaseAccomType::setSaturdayCeilingRateWithTax, pricingRule);
    }

    //Function will adjust all rates based on a value with no supplements and adjusted by the double offset
    public void calculateNewPPPRate(Tax tax, BigDecimal rateWithTax, BigDecimal offset, OffsetMethod offsetMethod, TransientPricingBaseAccomType tpbat,
                                    BiConsumer<TransientPricingBaseAccomType, BigDecimal> setterFunction,
                                    BiConsumer<TransientPricingBaseAccomType, BigDecimal> setterFunctionWithTax, PricingRule pricingRule) {
        if (pricingRule == null) {
            pricingRule = new PricingRule();
        }
        //This calculates the new floor/ceiling value with the adjusted offset
        if (rateWithTax != null) {
            if (offsetMethod != null) {
                if (offsetMethod.equals(OffsetMethod.FIXED_OFFSET)) {
                    BigDecimal finalAdjustment = pricingRule.calculatePrettyPrice(rateWithTax.add(nullToZero(offset)));

                    setterFunctionWithTax.accept(tpbat, finalAdjustment);
                    setterFunction.accept(tpbat, tax.removeRoomTaxRate(finalAdjustment));
                } else if (offsetMethod.equals(OffsetMethod.PERCENTAGE)) {
                    BigDecimal calculatedRate = rateWithTax.multiply(BigDecimal.ONE.add(nullToZero(offset).movePointLeft(2)));
                    BigDecimal finalAdjustment = pricingRule.calculatePrettyPrice(calculatedRate);

                    setterFunctionWithTax.accept(tpbat, finalAdjustment);
                    setterFunction.accept(tpbat, tax.removeRoomTaxRate(finalAdjustment));
                }
            } else {
                BigDecimal finalAdjustment = pricingRule.calculatePrettyPrice(rateWithTax.add(nullToZero(offset)));

                setterFunctionWithTax.accept(tpbat, finalAdjustment);
                setterFunction.accept(tpbat, tax.removeRoomTaxRate(finalAdjustment));
            }
        }
    }

    public void adjustDefaultAndSeasonOffsets(Tax tax, List<CPConfigOffsetAccomType> offsets, Map<Integer, PricingRule> pricingRules) {
        List<AccomType> allBaseRoomTypes = allBaseRoomTypes();
        List<CPConfigOffsetAccomType> completedMigrationAccomTypes = new ArrayList<>();
        List<CPConfigOffsetAccomType> newlyGeneratedOffsets = new ArrayList<>();
        offsets.stream()
                .filter(offset -> offset.getStatusId().equals(Status.ACTIVE.getId()))
                .filter(offset -> !offset.getOffsetMethod().equals(OffsetMethod.FIXED_PRICE))
                .filter(offset -> offset.getOccupancyType().equals(OccupancyType.DOUBLE))
                .forEach(offset -> {
                    boolean isBaseRoomType = allBaseRoomTypes.contains(offset.getAccomType());
                    PricingRule pricingRule = pricingRules.get(offset.getProductID());
                    if (isBaseRoomType) {
                        adjustOffsetRecords(offset, null, isBaseRoomType, tax, pricingRule);
                    } else {
                        CPConfigOffsetAccomType singleOffset = offsets.stream()
                                .filter(off -> off.getStatusId().equals(Status.ACTIVE.getId()))
                                .filter(off -> off.getOccupancyType().equals(OccupancyType.SINGLE))
                                .filter(off -> areDatesMatching(off.getStartDate(), offset.getStartDate()))
                                .filter(off -> areDatesMatching(off.getEndDate(), offset.getEndDate()))
                                .filter(off -> off.getAccomType().equals(offset.getAccomType()))
                                .findFirst()
                                .orElse(null);
                        if (singleOffset == null) {
                            singleOffset = new CPConfigOffsetAccomType();
                            singleOffset.setPropertyId(offset.getPropertyId());
                            singleOffset.setProductID(offset.getProductID());
                            singleOffset.setOccupancyType(OccupancyType.SINGLE);
                            singleOffset.setStartDate(offset.getStartDate());
                            singleOffset.setEndDate(offset.getEndDate());
                            singleOffset.setAccomType(offset.getAccomType());
                            singleOffset.setOffsetMethod(offset.getOffsetMethod());
                            singleOffset.setStatusId(Status.ACTIVE.getId());
                            newlyGeneratedOffsets.add(singleOffset);
                        }
                        adjustOffsetRecords(offset, singleOffset, isBaseRoomType, tax, pricingRule);
                        completedMigrationAccomTypes.add(singleOffset);
                    }
                    completedMigrationAccomTypes.add(offset);
                });

        //Handle any offsets that are straggling and don't have a corresponding double to adjust with it
        offsets.stream()
                .filter(offset -> offset.getStatusId().equals(Status.ACTIVE.getId()))
                .filter(offset -> offset.getOccupancyType().equals(OccupancyType.SINGLE))
                .filter(offset -> !completedMigrationAccomTypes.contains(offset))
                .forEach(offset -> {
                    boolean isBaseRoomType = allBaseRoomTypes.contains(offset.getAccomType());
                    PricingRule pricingRule = pricingRules.get(offset.getProductID());
                    CPConfigOffsetAccomType doubleOffset = offsets.stream()
                            .filter(off -> off.getOccupancyType().equals(OccupancyType.DOUBLE))
                            .filter(off -> areDatesMatching(off.getStartDate(), offset.getStartDate()))
                            .filter(off -> areDatesMatching(off.getEndDate(), offset.getEndDate()))
                            .filter(off -> off.getAccomType().equals(offset.getAccomType()))
                            .findFirst()
                            .orElse(null);
                    if (doubleOffset == null && (!isBaseRoomType || offset.getOffsetMethod().equals(OffsetMethod.FIXED_PRICE))) {
                        doubleOffset = new CPConfigOffsetAccomType();
                        doubleOffset.setPropertyId(offset.getPropertyId());
                        doubleOffset.setProductID(offset.getProductID());
                        doubleOffset.setOccupancyType(OccupancyType.DOUBLE);
                        doubleOffset.setStartDate(offset.getStartDate());
                        doubleOffset.setEndDate(offset.getEndDate());
                        doubleOffset.setAccomType(offset.getAccomType());
                        if (offset.getOffsetMethod().equals(OffsetMethod.FIXED_PRICE)) {
                            doubleOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
                        } else {
                            doubleOffset.setOffsetMethod(offset.getOffsetMethod());
                        }
                        doubleOffset.setStatusId(Status.ACTIVE.getId());
                        newlyGeneratedOffsets.add(doubleOffset);
                        adjustOffsetRecords(doubleOffset, offset, isBaseRoomType, tax, pricingRule);
                    }
                });

        offsets.addAll(newlyGeneratedOffsets);
        tenantCrudService.save(offsets);
    }

    public void adjustOffsetRecords(CPConfigOffsetAccomType doubleOffset, CPConfigOffsetAccomType singleOffset, boolean isBaseRoomType, Tax tax, PricingRule pricingRule) {
        if (isBaseRoomType) {
            if (doubleOffset != null) {
                updateDoubleOffsetValuesBaseRoomType(doubleOffset, tax);
            }
        } else {
            updateSingleAndDoubleOffsetValuesNonBase(valueOrNull(singleOffset.getSundayOffsetValueWithTax()),
                    valueOrNull(doubleOffset.getSundayOffsetValueWithTax()),
                    singleOffset.getOffsetMethod().equals(OffsetMethod.FIXED_PRICE),
                    pricingRule,
                    tax, doubleOffset.getOffsetMethod(), singleOffset, doubleOffset,
                    singleOffset.getOffsetMethod().equals(doubleOffset.getOffsetMethod()),
                    CPConfigOffsetAccomType::setSundayOffsetValue, CPConfigOffsetAccomType::setSundayOffsetValueWithTax);

            updateSingleAndDoubleOffsetValuesNonBase(valueOrNull(singleOffset.getMondayOffsetValueWithTax()),
                    valueOrNull(doubleOffset.getMondayOffsetValueWithTax()),
                    singleOffset.getOffsetMethod().equals(OffsetMethod.FIXED_PRICE),
                    pricingRule,
                    tax, doubleOffset.getOffsetMethod(), singleOffset, doubleOffset,
                    singleOffset.getOffsetMethod().equals(doubleOffset.getOffsetMethod()),
                    CPConfigOffsetAccomType::setMondayOffsetValue, CPConfigOffsetAccomType::setMondayOffsetValueWithTax);

            updateSingleAndDoubleOffsetValuesNonBase(valueOrNull(singleOffset.getTuesdayOffsetValueWithTax()),
                    valueOrNull(doubleOffset.getTuesdayOffsetValueWithTax()),
                    singleOffset.getOffsetMethod().equals(OffsetMethod.FIXED_PRICE),
                    pricingRule,
                    tax, doubleOffset.getOffsetMethod(), singleOffset, doubleOffset,
                    singleOffset.getOffsetMethod().equals(doubleOffset.getOffsetMethod()),
                    CPConfigOffsetAccomType::setTuesdayOffsetValue, CPConfigOffsetAccomType::setTuesdayOffsetValueWithTax);

            updateSingleAndDoubleOffsetValuesNonBase(valueOrNull(singleOffset.getWednesdayOffsetValueWithTax()),
                    valueOrNull(doubleOffset.getWednesdayOffsetValueWithTax()),
                    singleOffset.getOffsetMethod().equals(OffsetMethod.FIXED_PRICE),
                    pricingRule,
                    tax, doubleOffset.getOffsetMethod(), singleOffset, doubleOffset,
                    singleOffset.getOffsetMethod().equals(doubleOffset.getOffsetMethod()),
                    CPConfigOffsetAccomType::setWednesdayOffsetValue, CPConfigOffsetAccomType::setWednesdayOffsetValueWithTax);

            updateSingleAndDoubleOffsetValuesNonBase(valueOrNull(singleOffset.getThursdayOffsetValueWithTax()),
                    valueOrNull(doubleOffset.getThursdayOffsetValueWithTax()),
                    singleOffset.getOffsetMethod().equals(OffsetMethod.FIXED_PRICE),
                    pricingRule,
                    tax, doubleOffset.getOffsetMethod(), singleOffset, doubleOffset,
                    singleOffset.getOffsetMethod().equals(doubleOffset.getOffsetMethod()),
                    CPConfigOffsetAccomType::setThursdayOffsetValue, CPConfigOffsetAccomType::setThursdayOffsetValueWithTax);

            updateSingleAndDoubleOffsetValuesNonBase(valueOrNull(singleOffset.getFridayOffsetValueWithTax()),
                    valueOrNull(doubleOffset.getFridayOffsetValueWithTax()),
                    singleOffset.getOffsetMethod().equals(OffsetMethod.FIXED_PRICE),
                    pricingRule,
                    tax, doubleOffset.getOffsetMethod(), singleOffset, doubleOffset,
                    singleOffset.getOffsetMethod().equals(doubleOffset.getOffsetMethod()),
                    CPConfigOffsetAccomType::setFridayOffsetValue, CPConfigOffsetAccomType::setFridayOffsetValueWithTax);

            updateSingleAndDoubleOffsetValuesNonBase(valueOrNull(singleOffset.getSaturdayOffsetValueWithTax()),
                    valueOrNull(doubleOffset.getSaturdayOffsetValueWithTax()),
                    singleOffset.getOffsetMethod().equals(OffsetMethod.FIXED_PRICE),
                    pricingRule,
                    tax, doubleOffset.getOffsetMethod(), singleOffset, doubleOffset,
                    singleOffset.getOffsetMethod().equals(doubleOffset.getOffsetMethod()),
                    CPConfigOffsetAccomType::setSaturdayOffsetValue, CPConfigOffsetAccomType::setSaturdayOffsetValueWithTax);
        }
    }

    public void updateDoubleOffsetValuesBaseRoomType(CPConfigOffsetAccomType doubleOffset, Tax tax) {
        doubleOffset.setOccupancyType(OccupancyType.SINGLE);

        if (doubleOffset.getOffsetMethod().equals(OffsetMethod.PERCENTAGE)) {
            updateOffsetValueForPercentageBaseRoomType(doubleOffset, tax,
                    doubleOffset.getSundayOffsetValueWithTax(),
                    CPConfigOffsetAccomType::setSundayOffsetValue, CPConfigOffsetAccomType::setSundayOffsetValueWithTax);

            updateOffsetValueForPercentageBaseRoomType(doubleOffset, tax,
                    doubleOffset.getMondayOffsetValueWithTax(),
                    CPConfigOffsetAccomType::setMondayOffsetValue, CPConfigOffsetAccomType::setMondayOffsetValueWithTax);

            updateOffsetValueForPercentageBaseRoomType(doubleOffset, tax,
                    doubleOffset.getTuesdayOffsetValueWithTax(),
                    CPConfigOffsetAccomType::setTuesdayOffsetValue, CPConfigOffsetAccomType::setTuesdayOffsetValueWithTax);

            updateOffsetValueForPercentageBaseRoomType(doubleOffset, tax,
                    doubleOffset.getWednesdayOffsetValueWithTax(),
                    CPConfigOffsetAccomType::setWednesdayOffsetValue, CPConfigOffsetAccomType::setWednesdayOffsetValueWithTax);

            updateOffsetValueForPercentageBaseRoomType(doubleOffset, tax,
                    doubleOffset.getThursdayOffsetValueWithTax(),
                    CPConfigOffsetAccomType::setThursdayOffsetValue, CPConfigOffsetAccomType::setThursdayOffsetValueWithTax);

            updateOffsetValueForPercentageBaseRoomType(doubleOffset, tax,
                    doubleOffset.getFridayOffsetValueWithTax(),
                    CPConfigOffsetAccomType::setFridayOffsetValue, CPConfigOffsetAccomType::setFridayOffsetValueWithTax);

            updateOffsetValueForPercentageBaseRoomType(doubleOffset, tax,
                    doubleOffset.getSaturdayOffsetValueWithTax(),
                    CPConfigOffsetAccomType::setSaturdayOffsetValue, CPConfigOffsetAccomType::setSaturdayOffsetValueWithTax);
        } else {
            updateDoubleOffsetForFixedOffset(doubleOffset);
        }
    }

    private void updateDoubleOffsetForFixedOffset(CPConfigOffsetAccomType doubleOffset) {
        doubleOffset.setSundayOffsetValue(doubleOffset.getSundayOffsetValue() != null ? doubleOffset.getSundayOffsetValue().negate() : null);
        doubleOffset.setSundayOffsetValueWithTax(doubleOffset.getSundayOffsetValueWithTax() != null ? doubleOffset.getSundayOffsetValueWithTax().negate() : null);

        doubleOffset.setMondayOffsetValue(doubleOffset.getMondayOffsetValue() != null ? doubleOffset.getMondayOffsetValue().negate() : null);
        doubleOffset.setMondayOffsetValueWithTax(doubleOffset.getMondayOffsetValueWithTax() != null ? doubleOffset.getMondayOffsetValueWithTax().negate() : null);

        doubleOffset.setTuesdayOffsetValue(doubleOffset.getTuesdayOffsetValue() != null ? doubleOffset.getTuesdayOffsetValue().negate() : null);
        doubleOffset.setTuesdayOffsetValueWithTax(doubleOffset.getTuesdayOffsetValueWithTax() != null ? doubleOffset.getTuesdayOffsetValueWithTax().negate() : null);

        doubleOffset.setWednesdayOffsetValue(doubleOffset.getWednesdayOffsetValue() != null ? doubleOffset.getWednesdayOffsetValue().negate() : null);
        doubleOffset.setWednesdayOffsetValueWithTax(doubleOffset.getWednesdayOffsetValueWithTax() != null ? doubleOffset.getWednesdayOffsetValueWithTax().negate() : null);

        doubleOffset.setThursdayOffsetValue(doubleOffset.getThursdayOffsetValue() != null ? doubleOffset.getThursdayOffsetValue().negate() : null);
        doubleOffset.setThursdayOffsetValueWithTax(doubleOffset.getThursdayOffsetValueWithTax() != null ? doubleOffset.getThursdayOffsetValueWithTax().negate() : null);

        doubleOffset.setFridayOffsetValue(doubleOffset.getFridayOffsetValue() != null ? doubleOffset.getFridayOffsetValue().negate() : null);
        doubleOffset.setFridayOffsetValueWithTax(doubleOffset.getFridayOffsetValueWithTax() != null ? doubleOffset.getFridayOffsetValueWithTax().negate() : null);

        doubleOffset.setSaturdayOffsetValue(doubleOffset.getSaturdayOffsetValue() != null ? doubleOffset.getSaturdayOffsetValue().negate() : null);
        doubleOffset.setSaturdayOffsetValueWithTax(doubleOffset.getSaturdayOffsetValueWithTax() != null ? doubleOffset.getSaturdayOffsetValueWithTax().negate() : null);
    }

    public void updateOffsetValueForPercentageBaseRoomType(CPConfigOffsetAccomType doubleOffset, Tax tax,
                                                           BigDecimal offsetValueWithTax,
                                                           BiConsumer<CPConfigOffsetAccomType, BigDecimal> offsetSetterFunction,
                                                           BiConsumer<CPConfigOffsetAccomType, BigDecimal> offsetSetterFunctionWithTax) {
        if (offsetValueWithTax != null) {
            offsetSetterFunctionWithTax.accept(doubleOffset, offsetValueWithTax.negate());
            offsetSetterFunction.accept(doubleOffset, tax.removeRoomTaxRate(offsetValueWithTax.negate()));
        }
    }

    //Non-base room type flow for adjusting single and double offsets
    public void updateSingleAndDoubleOffsetValuesNonBase(BigDecimal singleOffsetWithTax, BigDecimal doubleOffsetWithTax,
                                                         boolean isPriceExcluded,
                                                         PricingRule pricingRule,
                                                         Tax tax,
                                                         OffsetMethod offsetMethod,
                                                         CPConfigOffsetAccomType singleOffsetObject,
                                                         CPConfigOffsetAccomType doubleOffsetObject,
                                                         boolean isSingleAndDoubleOffsetMatching,
                                                         BiConsumer<CPConfigOffsetAccomType, BigDecimal> offsetSetterFunction,
                                                         BiConsumer<CPConfigOffsetAccomType, BigDecimal> offsetSetterFunctionWithTax) {
        if (isPriceExcluded) {
            if (pricingRule == null) {
                pricingRule = new PricingRule();
            }
            singleOffsetObject.setOccupancyType(OccupancyType.DOUBLE);
            doubleOffsetObject.setOccupancyType(OccupancyType.SINGLE);

            if (offsetMethod.equals(OffsetMethod.FIXED_OFFSET)) {
                BigDecimal finalAdjustment = getAdjustmentForUpdatingOffsets(singleOffsetWithTax, doubleOffsetWithTax, pricingRule);

                offsetSetterFunctionWithTax.accept(singleOffsetObject, finalAdjustment);
                offsetSetterFunction.accept(singleOffsetObject, tax.removeRoomTaxRate(finalAdjustment));

                offsetSetterFunctionWithTax.accept(doubleOffsetObject, doubleOffsetWithTax != null ? doubleOffsetWithTax.negate() : null);
                offsetSetterFunction.accept(doubleOffsetObject, doubleOffsetWithTax != null ? tax.removeRoomTaxRate(doubleOffsetWithTax.negate()) : null);
            } else if (offsetMethod.equals(OffsetMethod.PERCENTAGE)) {
                BigDecimal finalOffsetWithTax = doubleOffsetWithTax != null ?
                        doubleOffsetWithTax.setScale(5).divide(BigDecimal.ONE.add(doubleOffsetWithTax.movePointLeft(2)), 5).negate() :
                        null;

                offsetSetterFunctionWithTax.accept(doubleOffsetObject, finalOffsetWithTax);
                offsetSetterFunction.accept(doubleOffsetObject, tax.removeRoomTaxRate(finalOffsetWithTax));

                BigDecimal finalAdjustment = null;
                if (Objects.nonNull(singleOffsetWithTax)) {
                    BigDecimal calculatedRate = singleOffsetWithTax.multiply(BigDecimal.ONE.add(nullToZero(doubleOffsetWithTax).movePointLeft(2)));
                    finalAdjustment = pricingRule.calculatePrettyPrice(calculatedRate);
                }

                offsetSetterFunctionWithTax.accept(singleOffsetObject, finalAdjustment);
                offsetSetterFunction.accept(singleOffsetObject, tax.removeRoomTaxRate(finalAdjustment));
            }
        } else if (isSingleAndDoubleOffsetMatching) {
            //Single offsets will be derived from the negated double offset
            //Double offsets will remain the same
            offsetSetterFunctionWithTax.accept(singleOffsetObject, doubleOffsetWithTax != null ? doubleOffsetWithTax.negate() : null);
            offsetSetterFunction.accept(singleOffsetObject, doubleOffsetWithTax != null ? tax.removeRoomTaxRate(doubleOffsetWithTax.negate()) : null);
        } else {
            if (singleOffsetObject.getStartDate() != null || doubleOffsetObject.getStartDate() != null) {
                offsetSetterFunction.accept(singleOffsetObject, null);
                offsetSetterFunctionWithTax.accept(singleOffsetObject, null);
                offsetSetterFunction.accept(doubleOffsetObject, null);
                offsetSetterFunctionWithTax.accept(doubleOffsetObject, null);
            } else {
                offsetSetterFunction.accept(singleOffsetObject, BigDecimal.ZERO);
                offsetSetterFunctionWithTax.accept(singleOffsetObject, BigDecimal.ZERO);
                offsetSetterFunction.accept(doubleOffsetObject, BigDecimal.ZERO);
                offsetSetterFunctionWithTax.accept(doubleOffsetObject, BigDecimal.ZERO);
            }
        }
    }

    public void handleRevertFromPerPersonPricing() {
        Tax tax = taxService.findTax();
        List<CPConfigOffsetAccomType> allOffsets = tenantCrudService.findAll(CPConfigOffsetAccomType.class);
        List<CPConfigOffsetAccomType> defaultOffsets = allOffsets.stream()
                .filter(offset -> offset.getStartDate() == null)
                .collect(toList());
        Map<Integer, PricingRule> pricingRules = getPrettyPricingRules();

        List<TransientPricingBaseAccomType> allTransientFloorCeiling = tenantCrudService.findAll(TransientPricingBaseAccomType.class);

        //Adjust Default Floor and Ceiling Values based on Default Offsets
        revertDefaultFloorAndCeilingValuesFromPerPersonPricing(tax, defaultOffsets, allTransientFloorCeiling, pricingRules);

        //Adjust Default Offsets based on Base/Non-Base Room Type
        revertDefaultAndSeasonOffsets(tax, allOffsets, pricingRules);
    }

    public Map<Integer, PricingRule> getPrettyPricingRules() {
        Map<Integer, PricingRule> pricingRules = prettyPricingService.getPricingRules();
        return pricingRules;
    }

    public void revertDefaultFloorAndCeilingValuesFromPerPersonPricing(Tax tax, List<CPConfigOffsetAccomType> defaultOffsets, List<TransientPricingBaseAccomType> allTransientFloorCeiling, Map<Integer, PricingRule> pricingRules) {
        //Iterate over all default CP_Cfg_Base_AT values (since Price Excluded for the Base AT is stored here as well it will cover Price Excluded as well)
        //Filter out seasons as those will have a different logic to them
        allTransientFloorCeiling.stream()
                .forEach(tpbat -> {
                    //Find the offset corresponding to the AccomType and the Occupancy Type Double for the offset adjustment
                    CPConfigOffsetAccomType cpConfigOffsetAccomType = defaultOffsets.stream()
                            .filter(defaultOffset -> defaultOffset.getAccomType().equals(tpbat.getAccomType()))
                            .filter(defaultOffset -> defaultOffset.getOccupancyType().equals(OccupancyType.SINGLE))
                            .findFirst()
                            .orElse(null);
                    PricingRule pricingRule = pricingRules.get(tpbat.getProductID());
                    revertPPPRatesForDOW(tax, tpbat, cpConfigOffsetAccomType, pricingRule);
                });

        tenantCrudService.save(allTransientFloorCeiling);
    }

    public void revertPPPRatesForDOW(Tax tax, TransientPricingBaseAccomType tpbat, CPConfigOffsetAccomType offset, PricingRule pricingRule) {
        //Sunday Floor/Ceiling Rate Adjustment
        calculateNewPPPRate(tax, tpbat.getSundayFloorRateWithTax(), offset != null ? offset.getSundayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setSundayFloorRate, TransientPricingBaseAccomType::setSundayFloorRateWithTax, pricingRule);
        calculateNewPPPRate(tax, tpbat.getSundayCeilingRateWithTax(), offset != null ? offset.getSundayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setSundayCeilingRate, TransientPricingBaseAccomType::setSundayCeilingRateWithTax, pricingRule);

        //Monday Floor/Ceiling Rate Adjustment
        calculateNewPPPRate(tax, tpbat.getMondayFloorRateWithTax(), offset != null ? offset.getMondayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setMondayFloorRate, TransientPricingBaseAccomType::setMondayFloorRateWithTax, pricingRule);
        calculateNewPPPRate(tax, tpbat.getMondayCeilingRateWithTax(), offset != null ? offset.getMondayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setMondayCeilingRate, TransientPricingBaseAccomType::setMondayCeilingRateWithTax, pricingRule);

        //Tuesday Floor/Ceiling Rate Adjustment
        calculateNewPPPRate(tax, tpbat.getTuesdayFloorRateWithTax(), offset != null ? offset.getTuesdayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setTuesdayFloorRate, TransientPricingBaseAccomType::setTuesdayFloorRateWithTax, pricingRule);
        calculateNewPPPRate(tax, tpbat.getTuesdayCeilingRateWithTax(), offset != null ? offset.getTuesdayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setTuesdayCeilingRate, TransientPricingBaseAccomType::setTuesdayCeilingRateWithTax, pricingRule);

        //Wednesday Floor/Ceiling Rate Adjustment
        calculateNewPPPRate(tax, tpbat.getWednesdayFloorRateWithTax(), offset != null ? offset.getWednesdayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setWednesdayFloorRate, TransientPricingBaseAccomType::setWednesdayFloorRateWithTax, pricingRule);
        calculateNewPPPRate(tax, tpbat.getWednesdayCeilingRateWithTax(), offset != null ? offset.getWednesdayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setWednesdayCeilingRate, TransientPricingBaseAccomType::setWednesdayCeilingRateWithTax, pricingRule);

        //Thursday Floor/Ceiling Rate Adjustment
        calculateNewPPPRate(tax, tpbat.getThursdayFloorRateWithTax(), offset != null ? offset.getThursdayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setThursdayFloorRate, TransientPricingBaseAccomType::setThursdayFloorRateWithTax, pricingRule);
        calculateNewPPPRate(tax, tpbat.getThursdayCeilingRateWithTax(), offset != null ? offset.getThursdayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setThursdayCeilingRate, TransientPricingBaseAccomType::setThursdayCeilingRateWithTax, pricingRule);

        //Friday Floor/Ceiling Rate Adjustment
        calculateNewPPPRate(tax, tpbat.getFridayFloorRateWithTax(), offset != null ? offset.getFridayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setFridayFloorRate, TransientPricingBaseAccomType::setFridayFloorRateWithTax, pricingRule);
        calculateNewPPPRate(tax, tpbat.getFridayCeilingRateWithTax(), offset != null ? offset.getFridayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setFridayCeilingRate, TransientPricingBaseAccomType::setFridayCeilingRateWithTax, pricingRule);

        //Saturday Floor/Ceiling Rate Adjustment
        calculateNewPPPRate(tax, tpbat.getSaturdayFloorRateWithTax(), offset != null ? offset.getSaturdayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setSaturdayFloorRate, TransientPricingBaseAccomType::setSaturdayFloorRateWithTax, pricingRule);
        calculateNewPPPRate(tax, tpbat.getSaturdayCeilingRateWithTax(), offset != null ? offset.getSaturdayOffsetValueWithTax() : null,
                offset != null ? offset.getOffsetMethod() : null,
                tpbat, TransientPricingBaseAccomType::setSaturdayCeilingRate, TransientPricingBaseAccomType::setSaturdayCeilingRateWithTax, pricingRule);
    }

    public void revertDefaultAndSeasonOffsets(Tax tax, List<CPConfigOffsetAccomType> offsets, Map<Integer, PricingRule> pricingRules) {
        //Filter out fixed price offsets (aka Price Excluded)
        List<AccomType> allBaseRoomTypes = getAllBaseRoomTypes().stream().map(PricingBaseAccomType::getAccomType).distinct().collect(toList());
        List<CPConfigOffsetAccomType> completedMigrationAccomTypes = new ArrayList<>();
        List<CPConfigOffsetAccomType> newlyGeneratedOffsets = new ArrayList<>();
        offsets.stream()
                .filter(offset -> offset.getStatusId().equals(Status.ACTIVE.getId()))
                .filter(offset -> !offset.getOffsetMethod().equals(OffsetMethod.FIXED_PRICE))
                .filter(offset -> offset.getOccupancyType().equals(OccupancyType.SINGLE))
                .forEach(offset -> {
                    boolean isBaseRoomType = allBaseRoomTypes.contains(offset.getAccomType());
                    PricingRule pricingRule = pricingRules.get(offset.getProductID());
                    if (isBaseRoomType) {
                        revertOffsetRecords(null, offset, isBaseRoomType, tax, pricingRule);
                    } else {
                        CPConfigOffsetAccomType doubleOffset = offsets.stream()
                                .filter(off -> off.getStatusId().equals(Status.ACTIVE.getId()))
                                .filter(off -> off.getOccupancyType().equals(OccupancyType.DOUBLE))
                                .filter(off -> areDatesMatching(off.getStartDate(), offset.getStartDate()))
                                .filter(off -> areDatesMatching(off.getEndDate(), offset.getEndDate()))
                                .filter(off -> off.getAccomType().equals(offset.getAccomType()))
                                .findFirst()
                                .orElse(null);
                        if (doubleOffset == null) {
                            doubleOffset = new CPConfigOffsetAccomType();
                            doubleOffset.setPropertyId(offset.getPropertyId());
                            doubleOffset.setProductID(offset.getProductID());
                            doubleOffset.setOccupancyType(OccupancyType.DOUBLE);
                            doubleOffset.setStartDate(offset.getStartDate());
                            doubleOffset.setEndDate(offset.getEndDate());
                            doubleOffset.setAccomType(offset.getAccomType());
                            doubleOffset.setOffsetMethod(offset.getOffsetMethod());
                            doubleOffset.setStatusId(offset.getStatusId());
                            newlyGeneratedOffsets.add(doubleOffset);
                        }
                        revertOffsetRecords(doubleOffset, offset, isBaseRoomType, tax, pricingRule);
                        completedMigrationAccomTypes.add(doubleOffset);
                    }
                    completedMigrationAccomTypes.add(offset);
                });

        //Handle any offsets that are straggling and don't have a corresponding single to adjust with it
        offsets.stream()
                .filter(offset -> offset.getStatusId().equals(Status.ACTIVE.getId()))
                .filter(offset -> offset.getOccupancyType().equals(OccupancyType.DOUBLE))
                .filter(offset -> !completedMigrationAccomTypes.contains(offset))
                .forEach(offset -> {
                    boolean isBaseRoomType = allBaseRoomTypes.contains(offset.getAccomType());
                    PricingRule pricingRule = pricingRules.get(offset.getProductID());
                    CPConfigOffsetAccomType singleOffset = offsets.stream()
                            .filter(off -> off.getOccupancyType().equals(OccupancyType.SINGLE))
                            .filter(off -> areDatesMatching(off.getStartDate(), offset.getStartDate()))
                            .filter(off -> areDatesMatching(off.getEndDate(), offset.getEndDate()))
                            .filter(off -> off.getAccomType().equals(offset.getAccomType()))
                            .findFirst()
                            .orElse(null);
                    if (singleOffset == null && !isBaseRoomType) {
                        singleOffset = new CPConfigOffsetAccomType();
                        singleOffset.setPropertyId(offset.getPropertyId());
                        singleOffset.setProductID(offset.getProductID());
                        singleOffset.setOccupancyType(OccupancyType.SINGLE);
                        singleOffset.setStartDate(offset.getStartDate());
                        singleOffset.setEndDate(offset.getEndDate());
                        singleOffset.setAccomType(offset.getAccomType());
                        if (offset.getOffsetMethod().equals(OffsetMethod.FIXED_PRICE)) {
                            singleOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
                        } else {
                            singleOffset.setOffsetMethod(offset.getOffsetMethod());
                        }
                        singleOffset.setStatusId(Status.ACTIVE.getId());
                        newlyGeneratedOffsets.add(singleOffset);
                        revertOffsetRecords(offset, singleOffset, isBaseRoomType, tax, pricingRule);
                    }
                });

        offsets.addAll(newlyGeneratedOffsets);
        tenantCrudService.save(offsets);
    }

    public void revertOffsetRecords(CPConfigOffsetAccomType doubleOffset, CPConfigOffsetAccomType singleOffset, boolean isBaseRoomType, Tax tax, PricingRule pricingRule) {
        if (isBaseRoomType) {
            if (singleOffset != null) {
                revertSingleOffsetValueBaseRoomType(singleOffset);
            }
        } else {
            revertSingleAndDoubleOffsetValuesNonBase(valueOrNull(singleOffset.getSundayOffsetValueWithTax()),
                    valueOrNull(doubleOffset.getSundayOffsetValueWithTax()),
                    doubleOffset.getOffsetMethod().equals(OffsetMethod.FIXED_PRICE),
                    pricingRule,
                    tax, singleOffset.getOffsetMethod(), singleOffset, doubleOffset,
                    singleOffset.getOffsetMethod().equals(doubleOffset.getOffsetMethod()),
                    CPConfigOffsetAccomType::setSundayOffsetValue, CPConfigOffsetAccomType::setSundayOffsetValueWithTax);

            revertSingleAndDoubleOffsetValuesNonBase(valueOrNull(singleOffset.getMondayOffsetValueWithTax()),
                    valueOrNull(doubleOffset.getMondayOffsetValueWithTax()),
                    doubleOffset.getOffsetMethod().equals(OffsetMethod.FIXED_PRICE),
                    pricingRule,
                    tax, singleOffset.getOffsetMethod(), singleOffset, doubleOffset,
                    singleOffset.getOffsetMethod().equals(doubleOffset.getOffsetMethod()),
                    CPConfigOffsetAccomType::setMondayOffsetValue, CPConfigOffsetAccomType::setMondayOffsetValueWithTax);

            revertSingleAndDoubleOffsetValuesNonBase(valueOrNull(singleOffset.getTuesdayOffsetValueWithTax()),
                    valueOrNull(doubleOffset.getTuesdayOffsetValueWithTax()),
                    doubleOffset.getOffsetMethod().equals(OffsetMethod.FIXED_PRICE),
                    pricingRule,
                    tax, singleOffset.getOffsetMethod(), singleOffset, doubleOffset,
                    singleOffset.getOffsetMethod().equals(doubleOffset.getOffsetMethod()),
                    CPConfigOffsetAccomType::setTuesdayOffsetValue, CPConfigOffsetAccomType::setTuesdayOffsetValueWithTax);

            revertSingleAndDoubleOffsetValuesNonBase(valueOrNull(singleOffset.getWednesdayOffsetValueWithTax()),
                    valueOrNull(doubleOffset.getWednesdayOffsetValueWithTax()),
                    doubleOffset.getOffsetMethod().equals(OffsetMethod.FIXED_PRICE),
                    pricingRule,
                    tax, singleOffset.getOffsetMethod(), singleOffset, doubleOffset,
                    singleOffset.getOffsetMethod().equals(doubleOffset.getOffsetMethod()),
                    CPConfigOffsetAccomType::setWednesdayOffsetValue, CPConfigOffsetAccomType::setWednesdayOffsetValueWithTax);

            revertSingleAndDoubleOffsetValuesNonBase(valueOrNull(singleOffset.getThursdayOffsetValueWithTax()),
                    valueOrNull(doubleOffset.getThursdayOffsetValueWithTax()),
                    doubleOffset.getOffsetMethod().equals(OffsetMethod.FIXED_PRICE),
                    pricingRule,
                    tax, singleOffset.getOffsetMethod(), singleOffset, doubleOffset,
                    singleOffset.getOffsetMethod().equals(doubleOffset.getOffsetMethod()),
                    CPConfigOffsetAccomType::setThursdayOffsetValue, CPConfigOffsetAccomType::setThursdayOffsetValueWithTax);

            revertSingleAndDoubleOffsetValuesNonBase(valueOrNull(singleOffset.getFridayOffsetValueWithTax()),
                    valueOrNull(doubleOffset.getFridayOffsetValueWithTax()),
                    doubleOffset.getOffsetMethod().equals(OffsetMethod.FIXED_PRICE),
                    pricingRule,
                    tax, singleOffset.getOffsetMethod(), singleOffset, doubleOffset,
                    singleOffset.getOffsetMethod().equals(doubleOffset.getOffsetMethod()),
                    CPConfigOffsetAccomType::setFridayOffsetValue, CPConfigOffsetAccomType::setFridayOffsetValueWithTax);

            revertSingleAndDoubleOffsetValuesNonBase(valueOrNull(singleOffset.getSaturdayOffsetValueWithTax()),
                    valueOrNull(doubleOffset.getSaturdayOffsetValueWithTax()),
                    doubleOffset.getOffsetMethod().equals(OffsetMethod.FIXED_PRICE),
                    pricingRule,
                    tax, singleOffset.getOffsetMethod(), singleOffset, doubleOffset,
                    singleOffset.getOffsetMethod().equals(doubleOffset.getOffsetMethod()),
                    CPConfigOffsetAccomType::setSaturdayOffsetValue, CPConfigOffsetAccomType::setSaturdayOffsetValueWithTax);
        }
    }

    public void revertSingleOffsetValueBaseRoomType(CPConfigOffsetAccomType singleOffset) {
        singleOffset.setOccupancyType(OccupancyType.DOUBLE);
        updateDoubleOffsetForFixedOffset(singleOffset);
    }

    //Non-base room type flow for adjusting single and double offsets
    public void revertSingleAndDoubleOffsetValuesNonBase(BigDecimal singleOffsetWithTax, BigDecimal doubleOffsetWithTax,
                                                         boolean isPriceExcluded,
                                                         PricingRule pricingRule,
                                                         Tax tax,
                                                         OffsetMethod offsetMethod,
                                                         CPConfigOffsetAccomType singleOffsetObject,
                                                         CPConfigOffsetAccomType doubleOffsetObject,
                                                         boolean isSingleAndDoubleOffsetMatching,
                                                         BiConsumer<CPConfigOffsetAccomType, BigDecimal> offsetSetterFunction,
                                                         BiConsumer<CPConfigOffsetAccomType, BigDecimal> offsetSetterFunctionWithTax) {
        if (isPriceExcluded) {
            singleOffsetObject.setOccupancyType(OccupancyType.DOUBLE);
            doubleOffsetObject.setOccupancyType(OccupancyType.SINGLE);

            if (offsetMethod.equals(OffsetMethod.FIXED_OFFSET)) {

                BigDecimal finalAdjustment = getAdjustmentForRevertingOffsets(singleOffsetWithTax, doubleOffsetWithTax, pricingRule);
                offsetSetterFunctionWithTax.accept(doubleOffsetObject, finalAdjustment);
                offsetSetterFunction.accept(doubleOffsetObject, tax.removeRoomTaxRate(finalAdjustment));
            } else if (offsetMethod.equals(OffsetMethod.PERCENTAGE)) {

                BigDecimal finalAdjustment = null;
                if (Objects.nonNull(doubleOffsetWithTax)) {
                    BigDecimal offsetValueFromRate = doubleOffsetWithTax.multiply(nullToZero(singleOffsetWithTax).movePointLeft(2));
                    finalAdjustment = pricingRule.calculatePrettyPrice(doubleOffsetWithTax.add(offsetValueFromRate));
                }
                offsetSetterFunctionWithTax.accept(doubleOffsetObject, finalAdjustment);
                offsetSetterFunction.accept(doubleOffsetObject, tax.removeRoomTaxRate(finalAdjustment));
            }

            offsetSetterFunctionWithTax.accept(singleOffsetObject, singleOffsetWithTax != null ? singleOffsetWithTax.negate() : null);
            offsetSetterFunction.accept(singleOffsetObject, singleOffsetWithTax != null ? tax.removeRoomTaxRate(singleOffsetWithTax.negate()) : null);
        } else if (isSingleAndDoubleOffsetMatching) {
            //Double offsets will be derived from the negated single offset
            //Single offsets will remain the same
            offsetSetterFunctionWithTax.accept(doubleOffsetObject, singleOffsetWithTax != null ? singleOffsetWithTax.negate() : null);
            offsetSetterFunction.accept(doubleOffsetObject, singleOffsetWithTax != null ? tax.removeRoomTaxRate(singleOffsetWithTax.negate()) : null);
        } else {
            if (singleOffsetObject.getStartDate() != null || doubleOffsetObject.getStartDate() != null) {
                offsetSetterFunction.accept(singleOffsetObject, null);
                offsetSetterFunctionWithTax.accept(singleOffsetObject, null);
                offsetSetterFunction.accept(doubleOffsetObject, null);
                offsetSetterFunctionWithTax.accept(doubleOffsetObject, null);
            } else {
                offsetSetterFunction.accept(singleOffsetObject, BigDecimal.ZERO);
                offsetSetterFunctionWithTax.accept(singleOffsetObject, BigDecimal.ZERO);
                offsetSetterFunction.accept(doubleOffsetObject, BigDecimal.ZERO);
                offsetSetterFunctionWithTax.accept(doubleOffsetObject, BigDecimal.ZERO);
            }
        }
    }

    private BigDecimal getAdjustmentForRevertingOffsets(BigDecimal singleOffsetWithTax, BigDecimal doubleOffsetWithTax, PricingRule pricingRule) {
        BigDecimal finalAdjustment = null;
        if (Objects.isNull(pricingRule)) {
            pricingRule = new PricingRule();
        }
        if (Objects.nonNull(doubleOffsetWithTax) && Objects.nonNull(singleOffsetWithTax)) {
            finalAdjustment = pricingRule.calculatePrettyPrice(singleOffsetWithTax.add(doubleOffsetWithTax));
        } else if (Objects.isNull(singleOffsetWithTax) && Objects.nonNull(doubleOffsetWithTax)) {
            finalAdjustment = pricingRule.calculatePrettyPrice(doubleOffsetWithTax);
        } else if (Objects.isNull(doubleOffsetWithTax) && Objects.nonNull(singleOffsetWithTax)) {
            finalAdjustment = pricingRule.calculatePrettyPrice(singleOffsetWithTax);
        }
        return finalAdjustment;
    }

    private BigDecimal getAdjustmentForUpdatingOffsets(BigDecimal singleOffsetWithTax, BigDecimal doubleOffsetWithTax, PricingRule pricingRule) {
        BigDecimal finalAdjustment = null;
        if (Objects.isNull(pricingRule)) {
            pricingRule = new PricingRule();
        }
        if (Objects.nonNull(singleOffsetWithTax) && Objects.nonNull(doubleOffsetWithTax)) {
            finalAdjustment = pricingRule.calculatePrettyPrice(singleOffsetWithTax.add(doubleOffsetWithTax));
        } else if (Objects.isNull(doubleOffsetWithTax) && Objects.nonNull(singleOffsetWithTax)) {
            finalAdjustment = pricingRule.calculatePrettyPrice(singleOffsetWithTax);
        } else if (Objects.isNull(singleOffsetWithTax) && Objects.nonNull(doubleOffsetWithTax)) {
            finalAdjustment = pricingRule.calculatePrettyPrice(doubleOffsetWithTax);
        }
        return finalAdjustment;
    }

    public boolean areDatesMatching(LocalDate startDate, LocalDate compareDate) {
        if (startDate == null && compareDate == null) {
            return true;
        } else if (startDate != null && compareDate != null) {
            return startDate.equals(compareDate);
        } else {
            return false;
        }
    }

    public List<AccomClassPriceRank> getAllAccomClassPriceRanks() {
        return tenantCrudService.findAll(AccomClassPriceRank.class);
    }

    public List<CPDecisionBAROutput> getCpDecisionBAROutputForAccomTypes(Product product, List<AccomType> accomTypes, LocalDate startDate, LocalDate endDate) {
        return tenantCrudService.findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN_FOR_ACCOM_TYPES,
                (QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("product", product).and(START_DATE, startDate).and(END_DATE, endDate)
                        .and("accomTypes", accomTypes)).parameters());
    }

    public List<ProductAccomType> getProductAccomTypesForProduct(Product product) {
        return tenantCrudService.findByNamedQuery(ProductAccomType.BY_PRODUCT, (QueryParameter.with("product", product).parameters()));
    }

    public List<ProductAccomType> getBaseProductAccomTypesForProduct(Product product) {
        List<AccomType> baseAccomTypes = getPricingAccomClasses().stream()
                .map(PricingAccomClass::getAccomType)
                .collect(toList());
        return getProductAccomTypesForProduct(product).stream()
                .filter(pat -> baseAccomTypes.contains(pat.getAccomType()))
                .collect(toList());
    }

    public List<AccomType> getRoomTypesForProduct(Product product) {
        return getProductAccomTypesForProduct(product).stream()
                .map(ProductAccomType::getAccomType)
                .collect(toList());
    }

    public List<PricingBaseAccomType> getAllPricingBaseAccomTypes() {
        return tenantCrudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_ALL_BY_PROPERTY_ID, QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }


    public Long startAutomatePricingConfigurationJob(Integer propertyId) {
        HashMap<String, Object> parameters = new HashMap<>();
        parameters.put(JobParameterKey.PROPERTY_ID, propertyId);
        parameters.put(JobParameterKey.TIMESTAMP, System.currentTimeMillis());
        return jobService.startGuaranteedNewInstance(JobName.AutomatePricingConfigurationsJob, parameters);
    }

    public int updateFloorCeilingMacroValue(String parameterName, String parameterValue) {
        return tenantCrudService.executeUpdateByNativeQuery(UPDATE_FC_DEFAULT_MACRO_VALUE,
                QueryParameter.with(PARAM_VALUE, parameterValue)
                        .and(MACRO_NAME, MACRO_RM_CONF_FLOOR_CEIL)
                        .and(PARAM_NAME, parameterName).parameters());
    }

    public String insertFloorCeilingMacroValues() {
        final Long countOfFloorCeilingParams = tenantCrudService.findByNamedQuerySingleResult(SASMacrosParameters.COUNT_BY_MACRO_NAME,
                QueryParameter.with(MACRO_NAME, MACRO_RM_CONF_FLOOR_CEIL).parameters());
        final StringBuilder response = new StringBuilder().append(String.format("::%s::\nExpected Number of params : %s" + " But found %s\n",
                MACRO_RM_CONF_FLOOR_CEIL, RM_FLOOR_CEILING_PARAMS, countOfFloorCeilingParams));
        if (countOfFloorCeilingParams < RM_FLOOR_CEILING_PARAMS) {
            final int rows = tenantCrudService.executeUpdateByNativeQuery(createParameterInsertQuery());
            LOGGER.info(response.append("Now ").append(rows).append(" parameters inserted").toString());
        }
        return response.toString();
    }

    /***********************************************************************************************
     * Central RMS stuff
     **********************************************************************************************/
    /**
     * There's an entity listener on TransientBaseAccomType that will sync data to Central RMS
     * whenever there are any CRUD operations on it, but that gets bypassed because there's a
     * query in here that directly writes to Cp_Cfg_Base_AT from the draft table (it also bypasses
     * any auditing too). So, without refactoring how G3 works, we're going to patch in our own
     * workaround until that query gets removed in favor of the hibernate approach.
     * <p>
     * yes, this is package protected, but only because of a hall monitor test :)
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
	public
    void resyncPricingConfigsToCentralRMSWorkAround(List<PricingBaseAccomType> configsToDelete, List<PricingBaseAccomType> configsToSync) {
        syncCentralRmsService.syncTransientPricingConfigurationData(configsToDelete, true);
        syncCentralRmsService.syncTransientPricingConfigurationData(configsToSync, false);
    }

    /**
     * More native query workarounds :) This should be used when you don't have a handle on <b>any</b>
     * of the configs that are being deleted, updated, or persisted. Specifically, all the CRUD
     * operations are done via a native query. So effectively, what happens is that this
     * <ol>
     *     <li>Truncates all of the data from central rms to avoid stale data</li>
     *     <li>Pulls all of the persisted data from CP_Cfg_Base_AT into memory to sync them to central rms</li>
     * </ol>
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void forceSyncPricingConfigsToCentralRMSWorkAround() {
        syncCentralRmsService.truncateTransientPricingConfigurationData();
        List<PricingBaseAccomType> snapshot = getAllTransientPricingBaseAccomTypesForBAR();
        syncCentralRmsService.syncTransientPricingConfigurationData(snapshot, false);
    }

    public List<PricingBaseAccomType> getAllTransientPricingBaseAccomTypesForBAR() {
        List<PricingBaseAccomType> pricingBaseAccomTypes = tenantCrudService.findByNamedQuery(TransientPricingBaseAccomType.FIND_BASE_AT_FOR_PRICEABLE_AC_BAR,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());

        if (CollectionUtils.isNotEmpty(pricingBaseAccomTypes)) {
            for (PricingBaseAccomType pricingBaseAccomType : pricingBaseAccomTypes) {
                Hibernate.initialize(pricingBaseAccomType.getAccomType());
            }
        }

        return pricingBaseAccomTypes;
    }

    public List<PricingBaseAccomType> getTransientPricingBaseAccomTypeSeasonsForBAR() {
        List<PricingBaseAccomType> allPricingBaseAccomTypes = getAllTransientPricingBaseAccomTypesForBAR();
        return getPricingBaseAccomTypesSeasons(allPricingBaseAccomTypes);
    }

    public boolean isCentralRmsProperty() {
        return configParamsService.getBooleanParameterValue(CENTRAL_RMS_AVAILABLE) &&
                configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED) &&
                com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig.isCentralRmsDataSyncEnabled();
    }

    public void saveMinimumPriceDifferentialData(BigDecimal minDifferentialValue, String corporateCurrency) {
        removeAllAccommodationClassMinPriceDiff();
        Tax defaultTax = taxService.findTax();
        BigDecimal minimumDifferentialValueWithoutRounding = getMinimumDifferentialValueAsPerCurrencyExchangeRate(minDifferentialValue, corporateCurrency);
        BigDecimal minimumDifferentialValueAsPerCurrencyExchangeRateWithTax = roundOffToNearestWholeNumber(minimumDifferentialValueWithoutRounding);
        BigDecimal minimumPricedDifferentialValueWithoutTax = roundOffToNearestWholeNumber(getMinimumPricedDifferentialValueWithTax(minimumDifferentialValueWithoutRounding, defaultTax.getRoomTaxRate()));
        List<AccomClassPriceRank> existingAccommodationClassPriceRank = getAllAccommodationClassPriceRank();
        List<AccomClassMinPriceDiff> accomClassMinPriceDiffToBeSaved = existingAccommodationClassPriceRank.stream()
                .map(accommodationClassPriceRank -> mapToAccommodationClassMinDiff(accommodationClassPriceRank, minimumPricedDifferentialValueWithoutTax, minimumDifferentialValueAsPerCurrencyExchangeRateWithTax))
                .collect(toList());
        tenantCrudService.save(accomClassMinPriceDiffToBeSaved);
    }

    private AccomClassMinPriceDiff mapToAccommodationClassMinDiff(AccomClassPriceRank accomClassPriceRank, BigDecimal minimumPricedDifferentialValueWithoutTax, BigDecimal minimumDifferentialValueAsPerCurrencyExchangeRateWithTax) {
        AccomClassMinPriceDiff accomClassMinPriceDiff = new AccomClassMinPriceDiff();
        accomClassMinPriceDiff.setAccomClassPriceRank(accomClassPriceRank);
        accomClassMinPriceDiff.setSundayDiff(minimumPricedDifferentialValueWithoutTax);
        accomClassMinPriceDiff.setMondayDiff(minimumPricedDifferentialValueWithoutTax);
        accomClassMinPriceDiff.setTuesdayDiff(minimumPricedDifferentialValueWithoutTax);
        accomClassMinPriceDiff.setWednesdayDiff(minimumPricedDifferentialValueWithoutTax);
        accomClassMinPriceDiff.setThursdayDiff(minimumPricedDifferentialValueWithoutTax);
        accomClassMinPriceDiff.setFridayDiff(minimumPricedDifferentialValueWithoutTax);
        accomClassMinPriceDiff.setSaturdayDiff(minimumPricedDifferentialValueWithoutTax);
        accomClassMinPriceDiff.setSundayDiffWithTax(minimumDifferentialValueAsPerCurrencyExchangeRateWithTax);
        accomClassMinPriceDiff.setMondayDiffWithTax(minimumDifferentialValueAsPerCurrencyExchangeRateWithTax);
        accomClassMinPriceDiff.setTuesdayDiffWithTax(minimumDifferentialValueAsPerCurrencyExchangeRateWithTax);
        accomClassMinPriceDiff.setWednesdayDiffWithTax(minimumDifferentialValueAsPerCurrencyExchangeRateWithTax);
        accomClassMinPriceDiff.setThursdayDiffWithTax(minimumDifferentialValueAsPerCurrencyExchangeRateWithTax);
        accomClassMinPriceDiff.setFridayDiffWithTax(minimumDifferentialValueAsPerCurrencyExchangeRateWithTax);
        accomClassMinPriceDiff.setSaturdayDiffWithTax(minimumDifferentialValueAsPerCurrencyExchangeRateWithTax);
        return accomClassMinPriceDiff;
    }

    private BigDecimal getMinimumPricedDifferentialValueWithTax(BigDecimal minDiffValue, BigDecimal roomTaxRate) {
        return minDiffValue.divide(roomTaxRate.divide(BigDecimal.valueOf(100.00), 2)
                .add(BigDecimal.ONE), 2, RoundingMode.DOWN);
    }

    public void removeAllAccommodationClassMinPriceDiff() {
        tenantCrudService.deleteAll(AccomClassMinPriceDiff.class);
    }

    private List<AccomClassPriceRank> getAllAccommodationClassPriceRank() {
        return tenantCrudService.findAll(AccomClassPriceRank.class);
    }

    public List<AccomClassMinPriceDiffResponse> getAccomClassMinPriceDiffResponse() {
        List<AccomClassMinPriceDiff> accomClassMinPriceDiffs = getDefaultAccomClassMinPriceDiff();
        return accomClassMinPriceDiffs.stream()
                .map(this::mapToAccomClassMinPriceDiffResponse)
                .collect(toList());
    }

    public void prepareAndSavePricingAccomClasses(BigDecimal value, MinimumIncrementMethod method, String corporateCurrency) {
        if (StringUtils.isNotEmpty(corporateCurrency) && null != value) {
            double conversionFactor = currencyService.toYieldConversionFactor(corporateCurrency);
            value = roundOffToNearestWholeNumber(value.multiply(BigDecimal.valueOf(conversionFactor)));
        }

        Map<Integer, Integer> existingAccomClassToBaseAccomTypeMapping = getPricingAccomClasses().stream()
                .collect(toMap(header -> header.getAccomClass().getId(), header -> header.getAccomType().getId()));
        List<PricingAccomClass> pricingAccomClasses = new ArrayList<>();
        List<Object[]> results = tenantCrudService.findByNamedQuery(AccomType.ACCOM_CLASS_WITH_HIGHEST_CAPACITY_ACCOM_TYPE);
        PricingAccomClass pricingAccomClass = null;
        for (Object[] dataRow : results) {
            int accomClassId = (Integer) dataRow[0];
            int accomTypeId = (Integer) dataRow[1];
            AccomType accomType = tenantCrudService.find(AccomType.class, accomTypeId);
            AccomClass accomClass = tenantCrudService.find(AccomClass.class, accomClassId);
            pricingAccomClass = new PricingAccomClass();
            pricingAccomClass.setPropertyId(PacmanWorkContextHelper.getPropertyId());
            pricingAccomClass.setAccomClass(accomClass);
            Integer existingBaseAccomTypeId = existingAccomClassToBaseAccomTypeMapping.get(accomClassId);
            if (null == existingBaseAccomTypeId || existingBaseAccomTypeId != accomTypeId) {
                pricingAccomClass.setAccomTypeChanged(true);
            }
            pricingAccomClass.setAccomType(accomType);
            pricingAccomClass.setPriceExcluded(false);
            pricingAccomClass.setMinimumIncrementMethod(method);
            pricingAccomClass.setMinimumIncrementValue(value);
            pricingAccomClass.setCreatedByUserId(
                    Integer.valueOf(PacmanThreadLocalContextHolder.getWorkContext().getUserId()));
            pricingAccomClass.setCreateDate(DateUtil.getCurrentDate());
            pricingAccomClass.setLastUpdatedByUserId(
                    Integer.valueOf(PacmanThreadLocalContextHolder.getWorkContext().getUserId()));
            pricingAccomClass.setLastUpdatedDate(DateUtil.getCurrentDate());
            pricingAccomClasses.add(pricingAccomClass);
        }
        this.savePricingAccomClasses(pricingAccomClasses);
    }

    public void updateBarProduct(String oldName, String newName, String description) {
        Predicate<Product> updatePredicate = p -> ObjectUtils.isNotEmpty(p) && oldName.equalsIgnoreCase(p.getName())
                && ProductType.DAILY.name().equals(p.getType());
        Product product = tenantCrudService.findByNamedQuerySingleResult(Product.GET_BY_ID,
                QueryParameter.with("productId", 1).parameters());
        updateProduct(oldName, newName, description, updatePredicate, product);
    }

    private void updateProduct(String oldName, String newName, String description, Predicate<Product> updatePredicate,
                               Product product) {
        if (updatePredicate.test(product)) {
            product.setName(newName);
            product.setDescription(description);
        } else {
            throw new TetrisException(
                    "No Product with ProductID : 1 and Name : " + oldName + " found for propertyId: " +
                            PacmanWorkContextHelper.getPropertyId());
        }
    }

    public void updateProductName(String oldName, String newName, String description) {
        Predicate<Product> updatePredicate = p -> ObjectUtils.isNotEmpty(p) && oldName.equalsIgnoreCase(p.getName());
        Product product = tenantCrudService.findByNamedQuerySingleResult(Product.GET_BY_NAME,
                QueryParameter.with("name", oldName).parameters());
        updateProduct(oldName, newName, description, updatePredicate, product);
    }

    private BigDecimal getMinimumDifferentialValueAsPerCurrencyExchangeRate(BigDecimal minDiffValue,
                                                                            String corporateCurrency) {
        double conversionFactor = StringUtils.isEmpty(corporateCurrency) ? 1.0 :
                currencyService.toYieldConversionFactor(corporateCurrency);
        return minDiffValue.multiply(BigDecimal.valueOf(conversionFactor));
    }

    private AccomClassMinPriceDiffResponse mapToAccomClassMinPriceDiffResponse(
            AccomClassMinPriceDiff accomClassMinPriceDiff) {
        AccomClassMinPriceDiffResponse response = new AccomClassMinPriceDiffResponse();
        response.setId(accomClassMinPriceDiff.getId());
        response.setAccomClassPriceRankPathId(accomClassMinPriceDiff.getAccomClassPriceRank().getId());
        response.setMondayDiff(accomClassMinPriceDiff.getMondayDiff());
        response.setTuesdayDiff(accomClassMinPriceDiff.getTuesdayDiff());
        response.setWednesdayDiff(accomClassMinPriceDiff.getWednesdayDiff());
        response.setThursdayDiff(accomClassMinPriceDiff.getThursdayDiff());
        response.setFridayDiff(accomClassMinPriceDiff.getFridayDiff());
        response.setSaturdayDiff(accomClassMinPriceDiff.getSaturdayDiff());
        response.setSundayDiff(accomClassMinPriceDiff.getSundayDiff());
        response.setMondayDiffWithTax(accomClassMinPriceDiff.getMondayDiffWithTax());
        response.setTuesdayDiffWithTax(accomClassMinPriceDiff.getTuesdayDiffWithTax());
        response.setWednesdayDiffWithTax(accomClassMinPriceDiff.getWednesdayDiffWithTax());
        response.setThursdayDiffWithTax(accomClassMinPriceDiff.getThursdayDiffWithTax());
        response.setFridayDiffWithTax(accomClassMinPriceDiff.getFridayDiffWithTax());
        response.setSaturdayDiffWithTax(accomClassMinPriceDiff.getSaturdayDiffWithTax());
        response.setSundayDiffWithTax(accomClassMinPriceDiff.getSundayDiffWithTax());
        response.setCreateDate(accomClassMinPriceDiff.getCreateDate());
        response.setCreatedByUserId(accomClassMinPriceDiff.getCreatedByUserId());
        response.setLastUpdatedDate(accomClassMinPriceDiff.getLastUpdatedDate());
        response.setLastUpdatedByUserId(accomClassMinPriceDiff.getLastUpdatedByUserId());
        return response;
    }

    public void removeHierarchies(List<ProductHierarchy> hierarchies) {
        agileRatesConfigurationService.removeHierarchies(hierarchies);
    }

    private void deleteGroupPricingBaseAccomTypes() {
        Date systemDate = dateService.getCaughtUpDate();
        tenantCrudService.executeUpdateByNamedQuery(GroupPricingBaseAccomType.DELETE_ALL_EXCEPT_PAST_SEASONS, Map.of(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId(), DATE, new LocalDate(systemDate)));
    }

    public GroupPricingSyncBARCeilingFloorDetails getUseDefaultBarCeilingFloorSyncWithPricingConfig() {
        GroupPricingSyncBARCeilingFloorDetails groupPricingSyncBARCeilingFloorDetails = tenantCrudService.findByNamedQuerySingleResult(GroupPricingSyncBARCeilingFloorDetails.FIND_DEFAULT_BAR_CEILING_FLOOR);
        return groupPricingSyncBARCeilingFloorDetails != null ? groupPricingSyncBARCeilingFloorDetails : new GroupPricingSyncBARCeilingFloorDetails();
    }

    public void saveSyncCeilingFloorValuesForGroupPricingInfo(GroupPricingSyncBARCeilingFloorDetails defaultBARCeilingFloor) {
        if(Objects.isNull(defaultBARCeilingFloor.getCeilingAdjustmentValue())){
            defaultBARCeilingFloor.setCeilingAdjustmentValue(BigDecimal.ZERO.setScale(2));
        }
        if(Objects.isNull(defaultBARCeilingFloor.getFloorAdjustmentValue())){
            defaultBARCeilingFloor.setFloorAdjustmentValue(BigDecimal.ZERO.setScale(2));
        }
        tenantCrudService.save(defaultBARCeilingFloor);
    }

    public Boolean isSyncGPCeilingFloorWithPrimaryPricedProductEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYNC_GP_CEILING_FLOOR_WITH_PRIMARY_PRICED_PRODUCT);
    }

    public void syncDefaultGPCeilingFloorWithPrimaryPricedProduct(List<PricingBaseAccomType> pricingBaseAccomTypes) {
        List<PricingBaseAccomType> groupPricingBaseAccomTypes = new ArrayList<>();

        List<PricingBaseAccomType> groupPricing = getGroupPricingBaseAccomTypeDefaultsWithPriceExcluded(PacmanWorkContextHelper.getPropertyId());
        GroupPricingSyncBARCeilingFloorDetails ceilingFloorDetails = getUseDefaultBarCeilingFloorSyncWithPricingConfig();
        Tax tax = taxService.findTax();

        createGPCeilingFloorUsingPricingCeilingFloor(pricingBaseAccomTypes, groupPricingBaseAccomTypes, groupPricing, ceilingFloorDetails, tax);

        if (CollectionUtils.isNotEmpty(groupPricing))
            updateGPCeilingFloorUsingPricingCeilingFloor(pricingBaseAccomTypes, groupPricingBaseAccomTypes, groupPricing, ceilingFloorDetails, tax);

        tenantCrudService.save(groupPricingBaseAccomTypes);
        taxInclusiveMigrationService.handleGroupPriceExcludedBaseRoomTypeSupplementOnSave();
    }

    private void updateGPCeilingFloorUsingPricingCeilingFloor(List<PricingBaseAccomType> pricingBaseAccomTypes, List<PricingBaseAccomType> groupPricingBaseAccomTypes, List<PricingBaseAccomType> groupPricing, GroupPricingSyncBARCeilingFloorDetails ceilingFloorDetails, Tax tax) {
        Map<AccomType, PricingBaseAccomType> transientAccomTypesMap = pricingBaseAccomTypes.stream().collect(Collectors.toMap(PricingBaseAccomType::getAccomType, PricingBaseAccomType::clone));

        if (Objects.nonNull(transientAccomTypesMap)) {
            groupPricing.forEach(group -> {
                if (Objects.nonNull(transientAccomTypesMap.get(group.getAccomType()))) {
                    GroupPricingBaseAccomType groupPricingBaseAccomType = populateGPCeilingFloorFromPricingCeilingFloor(ceilingFloorDetails, tax, transientAccomTypesMap.get(group.getAccomType()), (GroupPricingBaseAccomType) group);
                    groupPricingBaseAccomTypes.add(groupPricingBaseAccomType);
                }
            });
        }
    }

    private void createGPCeilingFloorUsingPricingCeilingFloor(List<PricingBaseAccomType> pricingBaseAccomTypes, List<PricingBaseAccomType> groupPricingBaseAccomTypes, List<PricingBaseAccomType> groupPricing, GroupPricingSyncBARCeilingFloorDetails ceilingFloorDetails, Tax tax) {
        List<PricingBaseAccomType> nonMatachPricingBaseAccomTypeList = pricingBaseAccomTypes.stream().filter(k -> groupPricing.stream()
                .noneMatch(g -> g.getAccomType().equals(k.getAccomType()))).collect(Collectors.toList());

        for (PricingBaseAccomType type : nonMatachPricingBaseAccomTypeList) {
            GroupPricingBaseAccomType groupPricingBaseAccomType = populateGPCeilingFloorFromPricingCeilingFloor(ceilingFloorDetails, tax, type, new GroupPricingBaseAccomType());
            groupPricingBaseAccomTypes.add(groupPricingBaseAccomType);
        }
    }


    public void syncGPCeilingFloorWithPrimaryPricedProductSeason(List<PricingBaseAccomType> pricingBaseAccomTypes) {
        List<PricingBaseAccomType> groupPricingBaseAccomTypes = new ArrayList<>();

        createGPCeilingFloorSeasonFromPricingCeilingFloor(pricingBaseAccomTypes, groupPricingBaseAccomTypes);

        tenantCrudService.save(groupPricingBaseAccomTypes);
        taxInclusiveMigrationService.handleGroupPriceExcludedBaseRoomTypeSupplementOnSave();
    }

    private void deleteExistingGPCeilingFloorSeasonFromPricingCeilingFloorSeason(PricingBaseAccomType pricingBaseAccomType) {
        Map<String, Object> queryParameters = new HashMap<>();
        if (Objects.nonNull(pricingBaseAccomType)) {
            queryParameters.put("seasonName", pricingBaseAccomType.getSeasonName());
            queryParameters.put("startDate", pricingBaseAccomType.getStartDate());
            queryParameters.put("endDate", pricingBaseAccomType.getEndDate());
            List<PricingBaseAccomType> seasonToBeDeleted = tenantCrudService.findByNamedQuery(GroupPricingBaseAccomType.FIND_SEASONS_BY_SEASON_NAME_START_DATE_END_DATE, queryParameters);
            tenantCrudService.delete(seasonToBeDeleted);
        }
    }

    private void createGPCeilingFloorSeasonFromPricingCeilingFloor(List<PricingBaseAccomType> pricingBaseAccomTypes, List<PricingBaseAccomType> groupPricingBaseAccomTypes) {
        GroupPricingSyncBARCeilingFloorDetails ceilingFloorDetails = getUseDefaultBarCeilingFloorSyncWithPricingConfig();
        Tax tax = taxService.findTax();

        for (PricingBaseAccomType transientAccomType : pricingBaseAccomTypes) {
            GroupPricingBaseAccomType newGroupSeason = populateGPCeilingFloorFromPricingCeilingFloor(ceilingFloorDetails, tax, transientAccomType, new GroupPricingBaseAccomType());
            Map<String, Object> queryParameters = new HashMap<>();
            queryParameters.put("accomTypeId", newGroupSeason.getAccomType().getId());
            queryParameters.put("startDate", newGroupSeason.getStartDate());
            queryParameters.put("endDate", newGroupSeason.getEndDate());
            List<PricingBaseAccomType> list = tenantCrudService.findByNamedQuery(GroupPricingBaseAccomType.FIND_SEASONS_BY_ACCOM_TYPE_START_DATE_END_DATE_PRODUCT, queryParameters);
            if (CollectionUtils.isNotEmpty(list)) {
                //  Requires changes immediately to sync with database so that subsequent insert works without  duplication
                tenantCrudService.executeUpdateByNamedQuery(GroupPricingBaseAccomType.DELETE_BY_ID, QueryParameter.with("id", list.get(0).getId()).parameters());
            }
            groupPricingBaseAccomTypes.add(newGroupSeason);
        }
    }

    private GroupPricingBaseAccomType populateGPCeilingFloorFromPricingCeilingFloor(GroupPricingSyncBARCeilingFloorDetails ceilingFloorDetails, Tax tax, PricingBaseAccomType transPricingBaseAccomType, GroupPricingBaseAccomType groupPricingBaseAccomType) {
        if (Objects.nonNull(transPricingBaseAccomType)) {
            groupPricingBaseAccomType.mapTransientDataToGroup(Optional.of(transPricingBaseAccomType));
            if (ceilingFloorDetails.isAdjustCeilingFloorValues()) {
                adjustGroupPricingCeilingFloorValue(groupPricingBaseAccomType, ceilingFloorDetails, tax);
            }
            return groupPricingBaseAccomType;
        }
        return null;
    }

    public void syncGPCeilingFloorWithPricingCeilingFloor() {
        if (isSyncGPCeilingFloorWithPrimaryPricedProductEnabled()) {
            // When feature toggle is enabled then we need to enable pre prod toggle to show checkbox enabled which is required for GP sync.
            updateGroupFloorCeilingDetails();
            copyFromPricingCeilingFloorToGPCeilingFloor();
        }
    }

    public void syncGPCeilingFloorWithPricingCeilingFloor(GPSyncBARCeilingFloorDTO  gpSyncBARCeilingFloorDTO) {
        if (isSyncGPCeilingFloorWithPrimaryPricedProductEnabled()) {
            // When feature toggle is enabled then we need to enable pre prod toggle to show checkbox enabled which is required for GP sync.
            updateUseBarCeilingFloorValue(gpSyncBARCeilingFloorDTO);
            copyFromPricingCeilingFloorToGPCeilingFloor();
        }
    }

    public void copyFromPricingCeilingFloorToGPCeilingFloor() {
        Product barProduct = tenantCrudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
        tenantCrudService.executeUpdateByNamedQuery(GroupPricingBaseAccomType.DELETE_NON_PRICE_EXCLUDED_BY_PRODUCT_ID, QueryParameter.with("productId", barProduct.getId()).parameters());
        List<PricingBaseAccomType> pricingBaseAccomTypes = getAllTransientPricingBaseAccomTypesWithPriceExcluded(barProduct);
        List<PricingBaseAccomType> baseAccomTypes = new ArrayList<>();
        List<PricingBaseAccomType> seasonBaseAccomTypes = new ArrayList<>();
        populateDefaultsAndSeasonsList(pricingBaseAccomTypes, baseAccomTypes, seasonBaseAccomTypes);

        if (CollectionUtils.isNotEmpty(baseAccomTypes)) {
            syncDefaultGPCeilingFloorWithPrimaryPricedProduct(baseAccomTypes);
        }
        if (CollectionUtils.isNotEmpty(seasonBaseAccomTypes)) {
            syncGPCeilingFloorWithPrimaryPricedProductSeason(seasonBaseAccomTypes);
        }
    }

    private void updateGroupFloorCeilingDetails() {
            GroupPricingSyncBARCeilingFloorDetails groupPricingSyncBARCeilingFloorDetails = getUseDefaultBarCeilingFloorSyncWithPricingConfig();
            groupPricingSyncBARCeilingFloorDetails.setUseBARCeilingAndFloor(true);
            saveSyncCeilingFloorValuesForGroupPricingInfo(groupPricingSyncBARCeilingFloorDetails);
    }


    public void adjustGroupPricingCeilingFloorValue(GroupPricingBaseAccomType pricingBaseAccomType,
                                                    GroupPricingSyncBARCeilingFloorDetails ceilingFloorDetails, Tax tax) {
        PricingAccomClass pricingAccomClass = tenantCrudService.findByNamedQuerySingleResult(
                PricingAccomClass.FIND_BY_PROPERTY_AND_ACCOM_TYPE,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and("accomTypeId", pricingBaseAccomType.getAccomType().getId()).parameters());
        if (pricingAccomClass != null && pricingAccomClass.isPriceExcluded()) {
            adjustFloorValues(pricingBaseAccomType, ceilingFloorDetails.getCeilingAdjustmentType(),
                    ceilingFloorDetails.getCeilingAdjustmentValue(), tax);
        } else {
            adjustFloorValues(pricingBaseAccomType, ceilingFloorDetails.getFloorAdjustmentType(),
                    ceilingFloorDetails.getFloorAdjustmentValue(), tax);
        }
        adjustCeilingValues(pricingBaseAccomType, ceilingFloorDetails.getCeilingAdjustmentType(),
                ceilingFloorDetails.getCeilingAdjustmentValue(), tax);
    }

    private void adjustFloorValues(GroupPricingBaseAccomType pricingBaseAccomType, AdjustmentType adjustmentType, BigDecimal floorValue, Tax tax) {
        pricingBaseAccomType.setSundayFloorRateWithTax(getAdjustedValue(pricingBaseAccomType.getSundayFloorRateWithTax(), adjustmentType, floorValue));
        pricingBaseAccomType.setMondayFloorRateWithTax(getAdjustedValue(pricingBaseAccomType.getMondayFloorRateWithTax(), adjustmentType, floorValue));
        pricingBaseAccomType.setTuesdayFloorRateWithTax(getAdjustedValue(pricingBaseAccomType.getTuesdayFloorRateWithTax(), adjustmentType, floorValue));
        pricingBaseAccomType.setWednesdayFloorRateWithTax(getAdjustedValue(pricingBaseAccomType.getWednesdayFloorRateWithTax(), adjustmentType, floorValue));
        pricingBaseAccomType.setThursdayFloorRateWithTax(getAdjustedValue(pricingBaseAccomType.getThursdayFloorRateWithTax(), adjustmentType, floorValue));
        pricingBaseAccomType.setFridayFloorRateWithTax(getAdjustedValue(pricingBaseAccomType.getFridayFloorRateWithTax(), adjustmentType, floorValue));
        pricingBaseAccomType.setSaturdayFloorRateWithTax(getAdjustedValue(pricingBaseAccomType.getSaturdayFloorRateWithTax(), adjustmentType, floorValue));

        pricingBaseAccomType.setSundayFloorRate(removeTaxFrom(pricingBaseAccomType.getSundayFloorRateWithTax(), tax));
        pricingBaseAccomType.setMondayFloorRate(removeTaxFrom(pricingBaseAccomType.getMondayFloorRateWithTax(), tax));
        pricingBaseAccomType.setTuesdayFloorRate(removeTaxFrom(pricingBaseAccomType.getTuesdayFloorRateWithTax(), tax));
        pricingBaseAccomType.setWednesdayFloorRate(removeTaxFrom(pricingBaseAccomType.getWednesdayFloorRateWithTax(), tax));
        pricingBaseAccomType.setThursdayFloorRate(removeTaxFrom(pricingBaseAccomType.getThursdayFloorRateWithTax(), tax));
        pricingBaseAccomType.setFridayFloorRate(removeTaxFrom(pricingBaseAccomType.getFridayFloorRateWithTax(), tax));
        pricingBaseAccomType.setSaturdayFloorRate(removeTaxFrom(pricingBaseAccomType.getSaturdayFloorRateWithTax(), tax));
    }

    private void adjustCeilingValues(GroupPricingBaseAccomType pricingBaseAccomType, AdjustmentType adjustmentType, BigDecimal ceilingValue, Tax tax) {
        pricingBaseAccomType.setSundayCeilingRateWithTax(getAdjustedValue(pricingBaseAccomType.getSundayCeilingRateWithTax(), adjustmentType, ceilingValue));
        pricingBaseAccomType.setMondayCeilingRateWithTax(getAdjustedValue(pricingBaseAccomType.getMondayCeilingRateWithTax(), adjustmentType, ceilingValue));
        pricingBaseAccomType.setTuesdayCeilingRateWithTax(getAdjustedValue(pricingBaseAccomType.getTuesdayCeilingRateWithTax(), adjustmentType, ceilingValue));
        pricingBaseAccomType.setWednesdayCeilingRateWithTax(getAdjustedValue(pricingBaseAccomType.getWednesdayCeilingRateWithTax(), adjustmentType, ceilingValue));
        pricingBaseAccomType.setThursdayCeilingRateWithTax(getAdjustedValue(pricingBaseAccomType.getThursdayCeilingRateWithTax(), adjustmentType, ceilingValue));
        pricingBaseAccomType.setFridayCeilingRateWithTax(getAdjustedValue(pricingBaseAccomType.getFridayCeilingRateWithTax(), adjustmentType, ceilingValue));
        pricingBaseAccomType.setSaturdayCeilingRateWithTax(getAdjustedValue(pricingBaseAccomType.getSaturdayCeilingRateWithTax(), adjustmentType, ceilingValue));

        pricingBaseAccomType.setSundayCeilingRate(removeTaxFrom(pricingBaseAccomType.getSundayCeilingRateWithTax(), tax));
        pricingBaseAccomType.setMondayCeilingRate(removeTaxFrom(pricingBaseAccomType.getMondayCeilingRateWithTax(), tax));
        pricingBaseAccomType.setTuesdayCeilingRate(removeTaxFrom(pricingBaseAccomType.getTuesdayCeilingRateWithTax(), tax));
        pricingBaseAccomType.setWednesdayCeilingRate(removeTaxFrom(pricingBaseAccomType.getWednesdayCeilingRateWithTax(), tax));
        pricingBaseAccomType.setThursdayCeilingRate(removeTaxFrom(pricingBaseAccomType.getThursdayCeilingRateWithTax(), tax));
        pricingBaseAccomType.setFridayCeilingRate(removeTaxFrom(pricingBaseAccomType.getFridayCeilingRateWithTax(), tax));
        pricingBaseAccomType.setSaturdayCeilingRate(removeTaxFrom(pricingBaseAccomType.getSaturdayCeilingRateWithTax(), tax));
    }


    public BigDecimal getAdjustedValue(BigDecimal rate, AdjustmentType adjustmentType, BigDecimal adjustmentValue) {
        if (rate != null) {
            if (adjustmentType.equals(AdjustmentType.FIXED)) {
                return rate.add(adjustmentValue);
            } else {
                return rate.add(rate.multiply(adjustmentValue).divide(new BigDecimal(100)));
            }
        }
        return null;
    }

    public List<PricingBaseAccomType> getTransientPricingBaseAccomTypeSeasonsWithPriceExcludedForBAR() {
        Product product = tenantCrudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
        return getTransientPricingBaseAccomTypeSeasonsWithPriceExcluded(product);
    }

    public void deleteGPCeilingFloorForPrimaryPricedProduct(List<PricingBaseAccomType> pricingBaseAccomTypes) {
        List<PricingBaseAccomType> seasonBaseAccomTypes = getGroupPricingBaseAccomTypeSeasonsWithPriceExcluded();
        List<PricingBaseAccomType> seasonBaseAccomTypesForBar = seasonBaseAccomTypes.stream().filter(k -> k.getProductID().equals(1)).collect(Collectors.toList());

        List<PricingBaseAccomType> deleteSeasonAccomTypes = seasonBaseAccomTypesForBar.stream().filter(grp -> pricingBaseAccomTypes.stream().
                        anyMatch(trans -> trans.getSeasonName().equals(grp.getSeasonName())))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteSeasonAccomTypes))
            tenantCrudService.delete(deleteSeasonAccomTypes);
    }

    public String getSystemDefaultProductName() {
        Product product = tenantCrudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
        return product.getName();
    }


    public void syncGPCeilingFloorFromPricingCeilingFloorUploadJob() {
        Product barProduct = tenantCrudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
        tenantCrudService.executeUpdateByNativeQuery(DELETE_GP_BASE_ACCOMTYPE_WITH_PRICE_EXCLUDED, QueryParameter.with("productId", barProduct.getId()).parameters());
        List<PricingBaseAccomType> pricingBaseAccomTypes = getAllTransientPricingBaseAccomTypesWithPriceExcluded(barProduct);
        List<PricingBaseAccomType> baseAccomTypes = new ArrayList<>();
        List<PricingBaseAccomType> seasonBaseAccomTypes = new ArrayList<>();
        populateDefaultsAndSeasonsList(pricingBaseAccomTypes, baseAccomTypes, seasonBaseAccomTypes);

        if (CollectionUtils.isNotEmpty(baseAccomTypes))
            syncDefaultGPCeilingFloorWithPrimaryPricedProduct(baseAccomTypes);

        if (CollectionUtils.isNotEmpty(seasonBaseAccomTypes))
            syncGPCeilingFloorWithPrimaryPricedProductSeason(seasonBaseAccomTypes);
    }



    public List<CPDecisionBAROutput> fetchCPDecisionBAROutputs(Set<Product> products, Set<LocalDate> arrivalDates, Set<AccomType> accomTypes) {
        try {
            // Extract product IDs from the set of products
            List<Integer> productIds = products.stream()
                    .map(Product::getId)
                    .collect(Collectors.toList());

            // Extract accommodation type IDs from the set of accommodation types
            List<Integer> accomTypeIds = accomTypes.stream()
                    .map(AccomType::getId)
                    .collect(Collectors.toList());

            // Convert Set<LocalDate> to List<String> of formatted dates
            List<String> formattedDates = arrivalDates.stream()
                    .map(LocalDate::toString) // Assuming Joda LocalDate's toString method returns a compatible format
                    .collect(Collectors.toList());

            String sql = "SELECT * " +
                    "FROM CP_Decision_Bar_Output " +
                    "WHERE property_id = :propertyId AND product_id IN (:productIds) AND Arrival_DT  IN (:arrivalDates)  AND los = -1 AND accom_type_id IN (:accomTypeIds) " +
                    "ORDER BY property_id";
            // Execute the query and map the results using a row mapper
            return tenantCrudService.findByNativeQuery(sql,
                    QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                            .and("productIds", productIds)
                            .and("arrivalDates", formattedDates)
                            .and("accomTypeIds", accomTypeIds)
                            .parameters(), row -> getCpDecisionBAROutput(row, products, accomTypes));
        } catch (Exception exception) {
            LOGGER.error("Exception occurred while fetching CPDecisionBarOutput for pricing in advance : " + exception + " Products:" + products + " dates:" + arrivalDates + " accomTypeList:" + accomTypes);
            throw exception;
        }
    }

    private static CPDecisionBAROutput getCpDecisionBAROutput(Object[] row, Set<Product> products, Set<AccomType> accomTypes) {
        CPDecisionBAROutput decision = new CPDecisionBAROutput();
        BigInteger bigIntegerId = (BigInteger) row[0];
        decision.setId(bigIntegerId.longValue());
        decision.setPropertyId((Integer) row[1]);
        decision.setDecisionId(((BigInteger) row[2]).intValue());

        // Filter Product from products based on product_id in the row
        Integer productId = ((BigInteger) row[3]).intValue();
        Product product = products.stream()
                .filter(p -> p.getId().equals(productId))
                .findFirst()
                .orElse(null);
        decision.setProduct(product);
        decision.setDecisionReasonTypeId((Integer) row[4]);
        // Filter AccomType from accomTypes based on accomType_id in the row
        Integer accomTypeId = (Integer) row[5];
        AccomType accomType = accomTypes.stream()
                .filter(a -> a.getId().equals(accomTypeId))
                .findFirst()
                .orElse(null);
        decision.setAccomType(accomType);
        java.sql.Date sqlDate = (java.sql.Date) row[6];
        decision.setArrivalDate(new DateTime(sqlDate).toLocalDate());
        decision.setLengthOfStay((Integer) row[7]);
        decision.setOptimalBAR(BigDecimal.valueOf((Double) row[8]));
        decision.setPrettyBAR((BigDecimal) row[9]);
        decision.setOverrideType(DecisionOverrideType.valueOf((String) row[10]));
        Double floorRate = (Double) row[11];
        if (floorRate != null) {
            decision.setFloorOverride(BigDecimal.valueOf(floorRate));
        }
        Double ceilRate = (Double) row[12];
        if (ceilRate != null) {
            decision.setCeilingOverride(BigDecimal.valueOf(ceilRate));
        }
        Double specificOverride = (Double) row[13];
        if (specificOverride != null) {
            decision.setSpecificOverride(BigDecimal.valueOf(specificOverride));
        }
        decision.setFinalBAR((BigDecimal) row[15]);
        decision.setRoomsOnlyBAR((BigDecimal) row[16]);
        decision.setPreviousBAR((BigDecimal) row[17]);
        Integer optimalBar = (Integer) row[18];
        if (optimalBar != null) {
            decision.setOptimalBarType(OptimalBarType.get(optimalBar));
        }
        decision.setAdjustmentValue((BigDecimal) row[19]);
        return decision;
    }

    public List<PricingBaseAccomType> getAllGroupPricingAccomTypeByProperty() {
        List<PricingBaseAccomType> groupPricingBaseAccomTypes = tenantCrudService.findByNamedQuery(GroupPricingBaseAccomType.FIND_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
        if (CollectionUtils.isNotEmpty(groupPricingBaseAccomTypes)) {
            return groupPricingBaseAccomTypes;
        }
        return Collections.emptyList();
    }

    public Boolean syncGroupPricingCeilingFloorWithPricingCeilingFloor(GPSyncBARCeilingFloorDTO gpSyncBARCeilingFloorDTO) {
        if (CollectionUtils.isNotEmpty(getAllGroupPricingAccomTypeByProperty())) {
            LOGGER.info("AUTOMATION GROUP PRICING CONFIGURATION: GP pricing configuration already present");
            return Boolean.FALSE;
        } else {
            enableSyncGPCeilingFloorWithPrimaryPricedProduct();
            updateUseBarCeilingFloorValue(gpSyncBARCeilingFloorDTO);
            copyFromPricingCeilingFloorToGPCeilingFloor();
             return Boolean.TRUE;
        }
    }


    public void enableSyncGPCeilingFloorWithPrimaryPricedProduct() {
        configParamsService.updateParameterValue(FeatureTogglesConfigParamName.SYNC_GP_CEILING_FLOOR_WITH_PRIMARY_PRICED_PRODUCT.getParameterName(),Boolean.TRUE);
    }

    public int deleteGroupPricingConfigurationByProperty() {
        return tenantCrudService.executeUpdateByNamedQuery(GroupPricingBaseAccomType.DELETE_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public void updateUseBarCeilingFloorValue(GPSyncBARCeilingFloorDTO gpSyncBARCeilingFloorDTO) {
        GroupPricingSyncBARCeilingFloorDetails groupPricingSyncBARCeilingFloorDetails = getUseDefaultBarCeilingFloorSyncWithPricingConfig();
        groupPricingSyncBARCeilingFloorDetails.setUseBARCeilingAndFloor(true);
        if(!Objects.isNull(gpSyncBARCeilingFloorDTO.getCeilingAdjustmentValue()) && !Objects.isNull(gpSyncBARCeilingFloorDTO.getFloorAdjustmentValue())
              && !Objects.isNull(gpSyncBARCeilingFloorDTO.getCeilingAdjustmentType()) && !Objects.isNull(gpSyncBARCeilingFloorDTO.getFloorAdjustmentType())  ){
            groupPricingSyncBARCeilingFloorDetails.setAdjustCeilingFloorValues(true);
            groupPricingSyncBARCeilingFloorDetails.setCeilingAdjustmentValue(gpSyncBARCeilingFloorDTO.getCeilingAdjustmentValue());
            groupPricingSyncBARCeilingFloorDetails.setCeilingAdjustmentType(gpSyncBARCeilingFloorDTO.getCeilingAdjustmentType());
            groupPricingSyncBARCeilingFloorDetails.setFloorAdjustmentValue(gpSyncBARCeilingFloorDTO.getFloorAdjustmentValue());
            groupPricingSyncBARCeilingFloorDetails.setFloorAdjustmentType(gpSyncBARCeilingFloorDTO.getFloorAdjustmentType());
        }
        saveSyncCeilingFloorValuesForGroupPricingInfo(groupPricingSyncBARCeilingFloorDetails);
    }

    boolean isCentralRmsSyncAvailable() {
        return SystemConfig.isCentralRmsRoomClassSyncEnabled() && configParamsService.getBooleanParameterValue(CENTRAL_RMS_AVAILABLE);
    }

    public boolean isHilton(ReservationSystem reservationSystem) {
        return ReservationSystem.HILSTAR.equals(reservationSystem) || ReservationSystem.PCRS.equals(reservationSystem);
    }
    public boolean hasOneYearHistoricalData() {
        java.time.LocalDate caughtUpDate = dateService.getCaughtUpJavaLocalDate();
        Date oneYearAgo = Date.from(caughtUpDate.minusYears(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date caughtUpAsDate = Date.from(caughtUpDate.atStartOfDay(ZoneId.systemDefault()).toInstant());

        List<Integer> fileMetadataResult = tenantCrudService.findByNamedQuery(
                FileMetadata.HAS_ONE_YEAR_HISTORY,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("fromDate", oneYearAgo)
                        .and("caughtUpDate", caughtUpAsDate)
                        .parameters()
        );
        if (!fileMetadataResult.isEmpty() && fileMetadataResult.get(0) > 0) {
            return true;
        }
        List<Integer> accomActivityResult = tenantCrudService.findByNamedQuery(
                AccomActivity.HAS_ONE_YEAR_HISTORY,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("fromDate", oneYearAgo)
                        .and("caughtUpDate", caughtUpAsDate)
                        .parameters()
        );
        return !accomActivityResult.isEmpty() && accomActivityResult.get(0) > 0;
    }
    public int getSuggestPollingInterval() {
        return SystemConfig.getSuggestPollingInterval();
    }

    @ForTesting
    public void setCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }

    public List<Product> getIndependentAndSystemDefaultProducts() {
        return tenantCrudService.findByNamedQuery(Product.GET_SYSTEM_DEFAULT_INDEPENDENT_PRODUCTS);
    }
}
