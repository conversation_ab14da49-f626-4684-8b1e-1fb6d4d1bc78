package com.ideas.tetris.pacman.services.informationmanager.alert.service;

import com.google.gson.Gson;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ClientPropertyView;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.discontinuedmarketsegment.entity.ProbableDiscontinuedMktSeg;
import com.ideas.tetris.pacman.services.discontinuedmarketsegment.services.DiscontinuedMarketSegmentsService;
import com.ideas.tetris.pacman.services.fplos.constants.StatisticalOutlierAlertConstants;
import com.ideas.tetris.pacman.services.functionspace.service.FunctionSpaceService;
import com.ideas.tetris.pacman.services.informationmanager.cache.InformationManagerPropertyCountCache;
import com.ideas.tetris.pacman.services.informationmanager.dto.Alert;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertHistory;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.informationmanager.dto.ScoreRange;
import com.ideas.tetris.pacman.services.informationmanager.dto.SearchCriteriaDTO;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrExcepNotifEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrHistoryEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrInstanceEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrInstanceStepStateEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrStatusEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrStepsEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrTypeEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrAlertConfigEntity;
import com.ideas.tetris.pacman.services.informationmanager.service.AbstractAlertService;
import com.ideas.tetris.pacman.services.job.JobMonitorService;
import com.ideas.tetris.pacman.services.job.entity.ExecutionStatus;
import com.ideas.tetris.pacman.services.job.entity.JobExecution;
import com.ideas.tetris.pacman.services.mktsegrecoding.entity.MktSegRecodingConfig;
import com.ideas.tetris.pacman.services.mktsegrecoding.service.MktSegRecodingService;
import com.ideas.tetris.pacman.services.pmsmigration.alert.PMSMigrationAlertActionHelper;
import com.ideas.tetris.pacman.services.pmsmigration.entity.PMSMigrationConfig;
import com.ideas.tetris.pacman.services.problem.JobCrudServiceBean;
import com.ideas.tetris.pacman.services.problem.ProblemService;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.pacman.services.roomtyperecoding.services.RoomTypeRecodingService;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;
import com.ideas.tetris.pacman.services.tars.TARSDecisionService;
import com.ideas.tetris.pacman.services.virtualproperty.service.VirtualPropertyMappingService;
import com.ideas.tetris.platform.common.Justification;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystem;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.license.LicenseService;
import com.ideas.tetris.platform.common.license.util.LicenseFeatureConstants;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.daoandentities.entity.VirtualPropertyMapping;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.FEATURE_LICENSING_ENABLED;
import static com.ideas.tetris.pacman.common.constants.Constants.*;
import static com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper.getPropertyId;
import static com.ideas.tetris.pacman.services.informationmanager.dto.AlertType.*;
import static com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrInstanceStepStateEntity.BY_ALERT_INSTANCE;
import static com.ideas.tetris.platform.common.externalsystem.ExternalSystem.TARS;
import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.toList;

@Component
@Transactional
public class AlertService extends AbstractAlertService {
    private static final Logger LOGGER = Logger.getLogger(AlertService.class.getName());
    private static final String DATE_FORMAT = "EEE dd-MMM-yyyy HH:mm:ss";
    private static int UNQUALIFIED_RATE_STEP_ID = 150;
    public static final String USER_ACTION_VALUE_FOR_LOWEST_OPTIMIZATION_DECISION_CHECK_ALERT = "lowestalertActionOnUi";
    public static final String USER_ACTION_VALUE_FOR_HIGHEST_OPTIMIZATION_DECISION_CHECK_ALERT = "highestalertActionOnUi";

    @JobCrudServiceBean.Qualifier
	@Qualifier("jobCrudServiceBean")
    @Autowired
	protected CrudService jobCrudService;
    @TenantCrudServiceBean.Qualifier
    @Autowired
	protected CrudService crudService;
    @Autowired
	private FunctionSpaceService service;
    @Autowired
    private ProblemService problemService;
    @Autowired
	private InformationManagerPropertyCountCache informationManagerPropertyCountCache;
    @Autowired
	private PacmanConfigParamsService configParamsService;
    @Autowired
	private JobServiceLocal jobService;
    @Autowired
	private JobMonitorService jobMonitorService;
    @Autowired
	private TARSDecisionService tarsDecisionService;
    @Autowired
	private RoomTypeRecodingService roomTypeRecodingService;
    @Autowired
	private PMSMigrationAlertActionHelper pmsMigrationAlertActionHelper;
    @Autowired
	private DiscontinuedMarketSegmentsService discontinuedMarketSegmentsService;

    @Autowired
	private VirtualPropertyMappingService virtualPropertyMappingService;

    @Autowired
	protected TentativeGroupForecastAlertRepository tentativeGroupForecastAlertRepository;

    @Autowired
	private MktSegRecodingService mktSegRecodingService;
    @Autowired
    private LicenseService licenseService;

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Justification("The caller of this method throws Runtime Exception after creating Alert. This method also needs to update some data before generating alert. we want to persist the Alert even after throwing Exception. Hence this method needs new Transaction")
    public Alert updateMigrationStateAndCreateAlertWithNewTransaction(WorkContextType workContext, InfoMgrTypeEntity alertTypeEntity,
                                                                      String description, String details, AlertType alertType, PMSMigrationConfig pmsMigrationConfig) {
        globalCrudService.save(pmsMigrationConfig);
        return createAlert(workContext, alertTypeEntity, description, details, alertType);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Justification("The caller of this method throws Runtime Exception. This method needs to update some data and generate an alert. Hence this method needs new Transaction")
    public Alert generateAlertAndUpdateRecodingState(WorkContextType workContext, InfoMgrTypeEntity alertTypeEntity, String description, String details, AlertType alertType, MktSegRecodingConfig mktSegRecodingConfig) {
        globalCrudService.save(mktSegRecodingConfig);
        return createAlert(workContext, alertTypeEntity, description, details, alertType);
    }

    public boolean isDataNotPopulatedToday() {
        int latestBdeCdpFileMetadata = crudService.findByNamedQuerySingleResult(FileMetadata.GET_ID_FOR_LAST_BDE_CPD_EXTRACT);
        final List<FileMetadata> fileMetadatIds = crudService.findByNamedQuery(FileMetadata.GET_NOT_PROCESSED_RSS_EXTRACTS,
                QueryParameter.with("fileMetadatId", latestBdeCdpFileMetadata).parameters());
        return CollectionUtils.isEmpty(fileMetadatIds);
    }

    public boolean isSkipCompetitorPriceChangeEnabled() {
        return configParamsService.getParameterValue(PreProductionConfigParamName.SKIP_COMPETITOR_PRICE_CHANGE_NOTIFICATION_WHEN_NO_DATA);
    }

    public boolean isRdlGeneratePrivateLabelCompetitorAlertEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RDL_GENERATE_PRIVATE_LABEL_COMPETITOR_ALERT);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Justification("The caller of this method throws Runtime Exception after creating Alert, we want to persist the Alert even after throwing Exception. Hence this method needs new Transaction")
    public Alert createAlertWithNewTransaction(WorkContextType workContext, InfoMgrTypeEntity alertTypeEntity,
                                               String description, String details, AlertType alertType) {
        return createAlert(workContext, alertTypeEntity, description, details, alertType);
    }

    public Alert createAlert(WorkContextType workContext, InfoMgrTypeEntity alertTypeEntity,
                             String description, String details, AlertType alertType) {
        String propertyStage = getPropertyStage();
        Integer propertyStageValue = Stage.valueForCode(propertyStage).getOrder();
        Alert alert = new Alert();
        populate(alert, workContext, alertTypeEntity, description, details, alertType);

        /*
         *  FIXME the check for property stage should be done by the caller because we now have
         *  alert types that need to be created even if the stage is below one way.
         */
        if ((Constants.IM_EQUAL_AND_ABOVE_ONE_WAY_THRESHOLD <= propertyStageValue)
                || alertType.equals(AlertType.NewRateCodeForMarketSegment)
                || (alertType.equals(AlertType.UnassignedMarketSegment)
                && configService.getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value())
        )
                || (alertType.equals(AlertType.UnmappedGroupStatusCodes)
                || (alertType.equals(AlertType.G3AgentCommunicationIssue))
        )
                || alertType.equals(AlertType.ValidateCostOfWalk)
                || alertType.equals(AlertType.NewCRFound)
                || alertType.equals(AlertType.CROOONotUpdated)
                || alertType.equals(AlertType.ScheduledDecisionDeliveryDateCancelled)
                || alertType.equals(AlertType.MissingRoomTypesInHotelData)
                || alertType.equals(AlertType.InactivatedRoomType)
                || pmsMigrationAlertActionHelper.isPMSMigrationAlert(alertType)
                || alertType.equals(AlertType.MissingMarketSegmentOnReservations)
                || alertType.equals(AlertType.UnmappedRateCodesFound)
                || alertType.equals(AlertType.NewCompetitiveRoomTypeFound)
                || alertType.equals(AlertType.NewCompetitorRoomTypeDescriptionsFound)
                || alertType.equals(AlertType.NewVendorCompetitorRoomTypeDescriptionsFound)
                || alertType.equals(AlertType.NewRDLCompetitorFound)
                || alertType.equals(AlertType.NewCompetitorRoomTypeDescriptionsRecommendations)
                || alertType.equals(AverageRateThresholdsTooLow)
                || alertType.equals(FullClosureOfRatesIntoTheFuture)
                || alertType.equals(NewShortDescriptions)
        ) {
            LOGGER.debug("Creating Alert for property - " + workContext.getPropertyId() + " of type" + alertType.getDescription());
            return createAlert(alert);
        } else {
            LOGGER.debug("Unable to create Alert for Property with Stage - " + propertyStage + " and " + alertTypeEntity.getDescription());
            return null;
        }
    }

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    public void createAlert(String name, String description, String details) {
        InfoMgrTypeEntity alertTypeEntity = getAlertType(name);
        if (alertTypeEntity.isEnabled()) {
            WorkContextType workContext = PacmanWorkContextHelper.getWorkContext();
            AlertType alertType = AlertType.valueOf(alertTypeEntity.getName());
            createAlert(workContext, alertTypeEntity, description, details, alertType);
        }
    }

    private Alert createAlert(Alert alert) {
        if (isAlertCreationEnabled()) {
            InfoMgrInstanceEntity existingAlert = findExistingAlert(alert);
            if (existingAlert != null) {
                existingAlert.incrementScore();
                InfoMgrStatusEntity status = multiPropertyCrudService.find(alert.getPropertyId(), InfoMgrStatusEntity.class, Constants.ALERT_STATUS_NEW_ID);
                existingAlert.setAlertStatus(status);
                existingAlert.setDescription(alert.getDescription());
                existingAlert.setLastModificationDate(new Date());
                existingAlert.setDetails(alert.getDetails());
                existingAlert = multiPropertyCrudService.save(existingAlert);

                InfoMgrHistoryEntity history = createHistory(Constants.ALERT_HISTORY_SCORE_INCREASED_ID, existingAlert);
                clearStepActionedState(alert.getPropertyId(), existingAlert.getId());
                multiPropertyCrudService.save(alert.getPropertyId(), history);
                alert = new Alert(existingAlert);
                alert.setSteps(getSteps(existingAlert));
                return alert;
            } else {
                InfoMgrInstanceEntity entity = buildAlertEntity(alert);
                InfoMgrStatusEntity status = multiPropertyCrudService.find(alert.getPropertyId(), InfoMgrStatusEntity.class, Constants.ALERT_STATUS_NEW_ID);
                entity.setAlertStatus(status);
                entity.setInfoMgrInstanceStepStateEntities(createSteps(entity));
                entity = multiPropertyCrudService.save(entity);

                InfoMgrHistoryEntity history = createHistory(Constants.ALERT_HISTORY_CREATED_ID, entity);
                multiPropertyCrudService.save(alert.getPropertyId(), history);
                return new Alert(entity, entity.getInfoMgrInstanceStepStateEntities());
            }
        }
        return null;
    }

    public List<InfoMgrExcepNotifEntity> findActiveUnresolvedNotifications(String notificationType) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("type", notificationType);
        paramMap.put("propertyId", getPropertyId());
        return crudService.findByNamedQuery(InfoMgrExcepNotifEntity.ALL_ACTIVE_UNRESOLVED_BY_TYPE, paramMap);
    }

    public List<InfoMgrExcepNotifEntity> findSuspendedNotifications(String notificationType) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("type", notificationType);
        paramMap.put("propertyId", getPropertyId());
        return crudService.findByNamedQuery(InfoMgrExcepNotifEntity.ALL_SUSPENDED, paramMap);
    }

    @SuppressWarnings("unchecked")
    public InfoMgrInstanceEntity findExistingAlert(Alert alert) {
        if (alert.getType().equals(FloorHigherThanProjectedADR)) {
            return findExistingFloorHigherThanADRAlert(alert);
        } else if (alert.getType().equals(NoRateAvailableForCompetitor)) {
            return findExistingAlertWithDetails(alert, alert.getDetails());
        }
        List<InfoMgrInstanceEntity> entities = (List<InfoMgrInstanceEntity>) multiPropertyCrudService.findByNamedQueryForSingleProperty(alert.getPropertyId(), InfoMgrInstanceEntity.BY_TYPE_AND_PROPERTY_ID,
                QueryParameter.with("propertyId", alert.getPropertyId()).and("type", alert.getType().toString()).and("category", Constants.ALERT_CATEGORY).parameters());
        if (entities == null || entities.isEmpty()) {
            return null;
        }
        return entities.get(0);
    }

    private InfoMgrInstanceEntity findExistingFloorHigherThanADRAlert(Alert alert) {
        Map<String, String> metadata = extractMetadata(alert.getDetails());
        String monthYear = "%" + metadata.get(MONTH_YEAR) + "%";
        return findExistingAlertWithDetails(alert, monthYear);
    }

    private InfoMgrInstanceEntity findExistingAlertWithDetails(Alert alert, String details) {
        List<InfoMgrInstanceEntity> entities = (List<InfoMgrInstanceEntity>) multiPropertyCrudService.findByNamedQueryForSingleProperty(alert.getPropertyId(), InfoMgrInstanceEntity.BY_TYPE_AND_PROPERTY_ID_DESC,
                QueryParameter.with("propertyId", alert.getPropertyId()).and("type", alert.getType().toString()).and("category", Constants.ALERT_CATEGORY).and("description", details).parameters());
        if (entities == null || entities.isEmpty()) {
            return null;
        }
        return entities.get(0);
    }

    public Map<String, String> extractMetadata(String alertDescription) {
        Gson jsonParser = new Gson();
        return jsonParser.fromJson(alertDescription, Map.class);
    }

    public InfoMgrInstanceEntity findLastModifiedAlert(Alert alert) {

        List<InfoMgrInstanceEntity> entities = tenantCrudService.findByNamedQuery(InfoMgrInstanceEntity.FIND_BY_Type_AND_ORDERED_BY_LAST_MODIFICATION, QueryParameter.with("type", alert.getType().toString()).parameters(), 1);
        if (entities == null || entities.isEmpty()) {
            return null;
        }
        return entities.get(0);
    }

    @SuppressWarnings("unchecked")
    private InfoMgrInstanceEntity buildAlertEntity(Alert alert) {
        InfoMgrInstanceEntity entity = new InfoMgrInstanceEntity();
        entity.setPropertyId(alert.getPropertyId());
        entity.setDescription(alert.getDescription());
        entity.setDetails(alert.getDetails());
        if (alert.isActive()) {
            entity.setStatusId(Constants.ACTIVE_STATUS_ID);
        } else {
            entity.setStatusId(Constants.INACTIVE_STATUS_ID);
        }
        List<InfoMgrTypeEntity> types = multiPropertyCrudService.findByNamedQueryForSingleProperty(alert.getPropertyId(), InfoMgrTypeEntity.BY_NAME,
                QueryParameter.with("name", alert.getType().toString()).parameters());
        if (types != null && !types.isEmpty()) {
            entity.setAlertType(types.get(0));
        }
        entity.setScore(alert.getScore());
        entity.setCreatedBy(getUserName(alert.getPropertyId(), alert.getCreatedBy()));
        entity.setLastModificationDate(new Date());
        return entity;
    }

    public Alert decreaseAlertScore(Alert alert) {
        InfoMgrInstanceEntity existingAlert = findExistingAlert(alert);
        if (existingAlert != null) {
            existingAlert.decrementScore();
            InfoMgrStatusEntity status = multiPropertyCrudService.find(alert.getPropertyId(), InfoMgrStatusEntity.class, Constants.ALERT_STATUS_ACTIONED_ID);
            existingAlert.setAlertStatus(status);
            existingAlert.setLastModificationDate(new Date());
            existingAlert.setDetails(alert.getDetails());
            existingAlert = multiPropertyCrudService.save(existingAlert);

            InfoMgrHistoryEntity history = createHistory(Constants.ALERT_HISTORY_SCORE_DECREASED_ID, existingAlert);
            clearStepActionedState(alert.getPropertyId(), existingAlert.getId());
            multiPropertyCrudService.save(alert.getPropertyId(), history);
            alert = new Alert(existingAlert);
            alert.setSteps(getSteps(existingAlert));
            return alert;
        }
        return alert;
    }

    @SuppressWarnings("unchecked")
    public List<AlertHistory> getHistory(int alertId, int propertyId) {
        List<AlertHistory> historyList = new ArrayList<>();
        List<InfoMgrHistoryEntity> entities =
                (List<InfoMgrHistoryEntity>) multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, InfoMgrHistoryEntity.BY_ALERT_INSTANCE_ID,
                        QueryParameter.with("alertInstanceId", alertId).parameters());
        if (entities == null || entities.isEmpty()) {
            return historyList;
        }
        Property objProperty = globalCrudService.find(Property.class, propertyId);
        TimeZone pTimeZone = getPropertyTimeZone(objProperty);

        DateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT);

        for (InfoMgrHistoryEntity entity : entities) {

            try {
                Date date = dateFormat.parse(dateService.formatDate(entity.getCreateDate(), true, pTimeZone));
                boolean areWeDaylightSavingsNow = pTimeZone.inDaylightTime(entity.getCreateDate());
                entity.setCreatedDateTimeZone(pTimeZone.getDisplayName(areWeDaylightSavingsNow, 0));
                entity.setCreateDate(date);
            } catch (ParseException e) {
                LOGGER.error("Unable to parse created Date as per Property TimeZone.", e);
            }
            historyList.add(new AlertHistory(entity));
        }
        return historyList;
    }

    public Integer getOpenAlertCount() {
        //UI will not be sending in propertyId so add
        return getOpenCategoryCount(Constants.ALERT_CATEGORY);
    }

    public Integer getNewAlertCount() {
        return getNewCategoryCount(Constants.ALERT_CATEGORY);
    }

    public Alert getAlertById(int alertId, int propertyId) {
        InfoMgrInstanceEntity entity = (InfoMgrInstanceEntity) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId, InfoMgrInstanceEntity.FIND_BY_ID, QueryParameter.with("id", alertId).parameters());
        Alert alert = null;
        if (entity != null) {
            alert = new Alert(entity);
            Property property = globalCrudService.find(Property.class, propertyId);
            alert.setPropertyCode(property.getCode());
            alert.setPropertyName(property.getName());
            alert.setSteps(getSteps(entity));
        }
        return alert;
    }

    @ForTesting
    public Alert getAlertByTypeAlertStatus(String alertTypeByName, String alertStatusName, int propertyId) {
        InfoMgrInstanceEntity entity;
        entity = (InfoMgrInstanceEntity) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId, InfoMgrInstanceEntity.FIND_BY_ALERT_TYPE_STATUS_NAME,
                QueryParameter.with("alertTypeByName", alertTypeByName).and("alertStatusName", alertStatusName).parameters());
        return new Alert(entity);

    }

    public Alert getAlertById(int alertId) {
        Integer propertyId = getPropertyId();
        InfoMgrInstanceEntity entity = (InfoMgrInstanceEntity) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId, InfoMgrInstanceEntity.FIND_BY_ID, QueryParameter.with("id", alertId).parameters());
        updateTimeAsPerPropertyTimeZone(entity);
        Alert alert = new Alert(entity);
        if (entity != null) {
            alert.setSteps(getSteps(entity));
        }
        return alert;
    }

    @SuppressWarnings("unchecked")
    public List<Alert> getOpenAlerts() {
        List<Alert> alerts = new ArrayList<>();

        Map<Integer, Property> propertiesById = getAuthorizedProperties();
        List<Integer> propertyIds = new ArrayList<>(propertiesById.keySet());

        if (propertyIds.isEmpty()) {
            return alerts;
        }

        List<InfoMgrInstanceEntity> entities = (List<InfoMgrInstanceEntity>) multiPropertyCrudService.findByNamedQueryUnionAcrossProperties(propertyIds, InfoMgrInstanceEntity.BY_PROPERTY_ID,
                QueryParameter.with("propertyId", propertyIds).and("category", Constants.ALERT_CATEGORY).parameters());

        if (entities != null) {
            getAlertsFromInfoMgrInstanceEntityList(alerts, propertiesById, entities);
        }
        return alerts;
    }

    /**
     * Adding a new method to get Alerts for the "System" user, who calls this from a scheduled quartz job.  The other Alert calls involve using the Authorization framework,
     * which depends on the userId being Integer-izable.  "System" does not convert to an Integer, at least not in any number base we're using here at IDeaS.  Perhaps
     * they use Base 46 at "alternate Universe" IDeaS.
     */
    @SuppressWarnings("unchecked")
    public List<Alert> getOpenAlertsForSystemUser(List<Integer> propertyIds, AlertType alertType) {
        List<Alert> alerts = new ArrayList<>();

        if (propertyIds.isEmpty()) {
            return alerts;
        }

        List<InfoMgrInstanceEntity> entities = (List<InfoMgrInstanceEntity>) multiPropertyCrudService.findByNamedQueryUnionAcrossProperties(propertyIds, InfoMgrInstanceEntity.BY_TYPE_AND_PROPERTY_ID,
                QueryParameter.with("propertyId", propertyIds).and("type", alertType.toString()).and("category", Constants.ALERT_CATEGORY).parameters());

        if (entities != null) {
            for (InfoMgrInstanceEntity entity : entities) {
                Alert alert = new Alert(entity);
                alert.setSteps(getSteps(entity));
                alerts.add(alert);
            }
        }
        return alerts;
    }

    @SuppressWarnings("unchecked")
    public List<Alert> getNewAlerts() {
        List<Alert> alerts = new ArrayList<>();

        Map<Integer, Property> propertiesById = getAuthorizedProperties();
        List<Integer> propertyIds = new ArrayList<>(propertiesById.keySet());

        if (propertyIds.isEmpty()) {
            return alerts;
        }

        List<InfoMgrInstanceEntity> entities = (List<InfoMgrInstanceEntity>) multiPropertyCrudService.findByNamedQueryUnionAcrossProperties(propertyIds, InfoMgrInstanceEntity.NEW_ALERTS,
                QueryParameter.with("propertyId", propertyIds).and("category", Constants.ALERT_CATEGORY).parameters());

        if (entities == null) {
            return alerts;
        }

        getAlertsFromInfoMgrInstanceEntityList(alerts, propertiesById, entities);
        return alerts;
    }

    private void getAlertsFromInfoMgrInstanceEntityList(List<Alert> alerts, Map<Integer, Property> propertiesById, List<InfoMgrInstanceEntity> entities) {
        for (InfoMgrInstanceEntity entity : entities) {
            Alert alert = new Alert(entity);
            alert.setPropertyCode(propertiesById.get(alert.getPropertyId()).getCode());
            alert.setSteps(getSteps(entity));
            alerts.add(alert);
        }
    }

    public void viewAlert(int alertId, int propertyId) {
        InfoMgrInstanceEntity entity = multiPropertyCrudService.find(propertyId, InfoMgrInstanceEntity.class, alertId);
        if (entity == null || entity.getAlertStatus().getId() == Constants.ALERT_STATUS_RESOLVED_ID) {
            return;
        }
        if (entity.getAlertStatus().getId() == Constants.ALERT_STATUS_NEW_ID) {
            InfoMgrStatusEntity status = multiPropertyCrudService.find(propertyId, InfoMgrStatusEntity.class, Constants.ALERT_STATUS_VIEWED_ID);
            entity.setAlertStatus(status);
            entity.setLastModificationDate(new Date());
            entity = multiPropertyCrudService.save(entity);
        }

        InfoMgrHistoryEntity history = createHistory(Constants.ALERT_HISTORY_VIEWED_ID, entity);
        multiPropertyCrudService.save(propertyId, history);
    }

    public void actionAlert(int alertId, int propertyId) {
        InfoMgrInstanceEntity entity = multiPropertyCrudService.find(propertyId, InfoMgrInstanceEntity.class, alertId);
        if (entity == null || entity.getAlertStatus().getId() == Constants.ALERT_STATUS_RESOLVED_ID) {
            return;
        }
        InfoMgrStatusEntity status = multiPropertyCrudService.find(propertyId, InfoMgrStatusEntity.class, Constants.ALERT_STATUS_ACTIONED_ID);
        entity.setAlertStatus(status);
        entity.setLastModificationDate(new Date());
        entity = multiPropertyCrudService.save(entity);

        InfoMgrHistoryEntity history = createHistory(Constants.ALERT_HISTORY_ACTIONED_ID, entity);
        multiPropertyCrudService.save(propertyId, history);
    }

    // use only for test.
    public void resolveAlert(int alertId, int propertyId) {
        InfoMgrInstanceEntity instance = multiPropertyCrudService.find(propertyId, InfoMgrInstanceEntity.class, alertId);
        if (instance == null || instance.getAlertStatus().getId() == Constants.ALERT_STATUS_RESOLVED_ID) {
            return;
        }
        markResolvedIfActionStepClicked(instance, propertyId);
    }

    public List<Alert> resolveAllAlertTypes(List<AlertType> alertTypes, Integer propertyId) {
        List<Alert> alerts = new ArrayList<>();
        for (AlertType alertType : alertTypes) {
            alerts.addAll(resolveAllAlerts(alertType, propertyId));
        }
        return alerts;
    }

    @SuppressWarnings("unchecked")
    public List<Alert> resolveAllAlerts(AlertType alertType, Integer propertyId) {
        List<Alert> alerts = new ArrayList<>();
        List<InfoMgrInstanceEntity> entities = (List<InfoMgrInstanceEntity>) multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, InfoMgrInstanceEntity.BY_TYPE_AND_PROPERTY_ID,
                QueryParameter.with("propertyId", propertyId).and("type", alertType.toString()).and("category", Constants.ALERT_CATEGORY).parameters());
        if (entities == null || entities.isEmpty()) {
            return alerts;
        }
        return resolveAlert(propertyId, entities);
    }

    public List<Alert> resolveAlert(Integer propertyId, List<InfoMgrInstanceEntity> entities) {
        List<Alert> alerts = new ArrayList<>();
        InfoMgrStatusEntity status = multiPropertyCrudService.find(propertyId, InfoMgrStatusEntity.class, Constants.ALERT_STATUS_RESOLVED_ID);
        for (InfoMgrInstanceEntity entity : entities) {
            entity.setLastModificationDate(new Date());
            entity.setAlertStatus(status);
            entity.setScore(0);
            entity = multiPropertyCrudService.save(entity);

            InfoMgrHistoryEntity history = createHistory(Constants.ALERT_HISTORY_RESOLVED_ID, entity);
            multiPropertyCrudService.save(entity.getPropertyId(), history);
            alerts.add(new Alert(entity));
        }
        return alerts;
    }

    public List<Alert> resolveOpenLDBAlerts(){
        List<Alert> alerts = new ArrayList<>();
        List<InfoMgrInstanceEntity> openLDBAlertInstances = getOpenAlertInstances(List.of(IncompleteLDBConfiguration.name(), LDBConfigurationUnlocked.name(), ApproachingSoftOpeningDate.name()), List.of(NEW_STATUS, VIEWED_STATUS, ACTIONED_STATUS, SUSPENDED_STATUS), ALERT_CATEGORY);
        if (openLDBAlertInstances == null || openLDBAlertInstances.isEmpty()) {
            return alerts;
        }
        return resolveAlert(PacmanWorkContextHelper.getPropertyId(), openLDBAlertInstances);
    }

    // use only for test.
    public void resolveUnassignedRoomTypeAlerts() {
        resolveUnassignedCapacityRoomTypeAlerts();
        resolveUnassignedZeroCapacityRoomTypeAlerts();
    }

    public void resolveUnassignedCapacityRoomTypeAlerts() {
        Integer propertyId = getPropertyId();
        List<AccomType> unassignedRoomTypes = crudService.findByNamedQuery(AccomType.ALL_UNASSIGNED_BY_PROPERTY_ID, QueryParameter.with("propertyId", propertyId).parameters());
        boolean ratePlanConfigEnabled = configService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value());

        boolean isAlertValidForCapacity = false;
        if (!unassignedRoomTypes.isEmpty()) {
            //We are checking to see if these alerts are still valid by finding at least one accom type that is still unassigned.
            isAlertValidForCapacity = unassignedRoomTypes.stream().anyMatch(accomType -> accomType.getAccomTypeCapacity() > 0);
        }

        resolveUnassignedRoomTypeAlerts(AlertType.UnassignedRoomType, isAlertValidForCapacity, ratePlanConfigEnabled, propertyId);
    }

    public void resolveUnassignedZeroCapacityRoomTypeAlerts() {
        Integer propertyId = getPropertyId();
        List<AccomType> unassignedRoomTypes = crudService.findByNamedQuery(AccomType.ALL_UNASSIGNED_BY_PROPERTY_ID, QueryParameter.with("propertyId", propertyId).parameters());
        boolean ratePlanConfigEnabled = configService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value());

        boolean isAlertValidForZeroCapacity = false;
        if (!unassignedRoomTypes.isEmpty()) {
            //We are checking to see if these alerts are still valid by finding at least one accom type that is still unassigned.
            isAlertValidForZeroCapacity = unassignedRoomTypes.stream().anyMatch(accomType -> accomType.getAccomTypeCapacity() == 0);
        }

        resolveUnassignedRoomTypeAlerts(AlertType.UnassignedZeroCapacityRoomType, isAlertValidForZeroCapacity, ratePlanConfigEnabled, propertyId);

    }

    private void resolveUnassignedRoomTypeAlerts(AlertType alertType, boolean isAlertValid, boolean ratePlanConfigEnabled, int propertyId) {
        List<InfoMgrInstanceEntity> unresolvedUnassignedRoomTypeAlerts = crudService.findByNamedQuery(InfoMgrInstanceEntity.FIND_UNRESOLVED_BY_TYPE,
                QueryParameter.with("propertyId", propertyId).and("type", alertType.name())
                        .and("category", Constants.ALERT_CATEGORY)
                        .and("alertStatus", "Resolved").parameters());

        List<InfoMgrInstanceEntity> alerts = new ArrayList<>();
        alerts.addAll(unresolvedUnassignedRoomTypeAlerts);

        InfoMgrStatusEntity resolvedStatus = crudService.find(InfoMgrStatusEntity.class, Constants.ALERT_STATUS_RESOLVED_ID);

        //Here we will stream through each unresolved alert and if all the steps have been actioned, it will mark the alert as resolved. If there is
        //an Unqualified Rate step, we will ignore it if they don't have Rate Plan Configuration enabled.
        alerts.stream().forEach(alert -> {
            Optional<InfoMgrInstanceStepStateEntity> unresolvedStateOptional = alert.getInfoMgrInstanceStepStateEntities().stream().filter(step ->
                    !step.getActioned() && !(step.getStep().getId().equals(UNQUALIFIED_RATE_STEP_ID) && !ratePlanConfigEnabled)
            ).findAny();
            if (!unresolvedStateOptional.isPresent() || !isAlertValid) {
                alert.setAlertStatus(resolvedStatus);
                alert.setScore(0);
                alert.setLastModificationDate(new Date());
                crudService.save(alert);
            }
        });

    }

    @SuppressWarnings("unchecked")
    public void doAlertStep(int alertId, int propertyId, int stepId, String label) {
        InfoMgrInstanceEntity entity = multiPropertyCrudService.find(propertyId, InfoMgrInstanceEntity.class, alertId);
        InfoMgrStepsEntity stepEntity = multiPropertyCrudService.find(propertyId, InfoMgrStepsEntity.class, stepId);
        boolean wasSuspended = false;
        if (entity == null || entity.getAlertStatus().getId() == Constants.ALERT_STATUS_RESOLVED_ID) {
            return;
        }
        if (entity.getAlertStatus().getId() == Constants.ALERT_STATUS_SUSPENDED_ID || entity.getAlertStatus().getId() == Constants.ALERT_STATUS_SNOOZED_ID) {
            wasSuspended = true;
        }
        InfoMgrStatusEntity status = multiPropertyCrudService.find(propertyId, InfoMgrStatusEntity.class, Constants.ALERT_STATUS_ACTIONED_ID);
        InfoMgrStatusEntity statusInvestigated = multiPropertyCrudService.find(propertyId, InfoMgrStatusEntity.class, Constants.ALERT_STATUS_INVESTIGATED_ID);
        if (!wasSuspended) {
            if (stepEntity.getStepType() == Constants.IM_INVESTIGATION_STEP_TYPE) {
                entity.setAlertStatus(statusInvestigated);
            } else {
                entity.setAlertStatus(status);
            }
        }
        entity.setLastModificationDate(new Date());
        List<InfoMgrInstanceStepStateEntity> steps = (List<InfoMgrInstanceStepStateEntity>) multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, InfoMgrInstanceStepStateEntity.BY_ALERT_AND_STEP,
                QueryParameter.with("alertId", alertId).and("stepId", stepId).parameters());
        if (steps != null && steps.size() == 1) {
            InfoMgrInstanceStepStateEntity step = steps.get(0);
            if (!wasSuspended) {
                step.setActioned(true);
            }
            multiPropertyCrudService.save(propertyId, step);
        }
        entity = multiPropertyCrudService.save(entity);

        InfoMgrHistoryEntity history = createHistory(Constants.ALERT_HISTORY_ACTIONED_ID, entity);
        history.setDescription(label + " executed");
        multiPropertyCrudService.save(propertyId, history);

        // mark  resolved if the Action  step clicked by user
        if (null != stepEntity && stepEntity.getStepType() == Constants.IM_ACTION_STEP_TYPE && !wasSuspended) {
            markResolvedIfActionStepClicked(entity, propertyId);
        }
        actionSpecificAlerts(entity, stepEntity);
    }

    private void actionSpecificAlerts(InfoMgrInstanceEntity entity, InfoMgrStepsEntity stepEntity) {
        actionIfStatisticalOutlierAlert(entity, stepEntity);
        actionIfDecisionUploadFailedAlert(entity, stepEntity);
        actionIfMissingRoomTypesInHotelDataAlert(entity, stepEntity);
        actionIfInactivatedRoomTypeAlert(entity, stepEntity);
        actionIfUnmappedRateCodesFoundAlert(entity, stepEntity);
        actionSufficientBookedDataAvailableAlert(entity);
        pmsMigrationAlertActionHelper.actionAlert(entity, stepEntity);
        actionInconsistentBARRatesAlert(entity, stepEntity);
        actionDiscontinuedMktSegFoundAlert(entity, stepEntity);
        actionUnassignedRoomTypeAlert(entity, stepEntity);
    }

    private void actionUnassignedRoomTypeAlert(InfoMgrInstanceEntity entity, InfoMgrStepsEntity stepEntity) {
        if ((AlertType.UnassignedRoomType.name().equals(entity.getAlertType().getName()) || AlertType.UnassignedZeroCapacityRoomType.name().equals(entity.getAlertType().getName())) && RESOLVE_ALERT_UNASSIGNED_RT.equals(stepEntity.getText())) {

            boolean isUnAssignedRoomTypeAlert = AlertType.UnassignedRoomType.name().equals(entity.getAlertType().getName());

            Integer propertyId = getPropertyId();
            List<AccomType> unassignedRoomTypes = null;
            if (isUnAssignedRoomTypeAlert) {
                unassignedRoomTypes = crudService.findByNamedQuery(AccomType.ALL_UNASSIGNED_BY_PROPERTY_ID, QueryParameter.with("propertyId", propertyId).parameters());
            } else {
                unassignedRoomTypes = crudService.findByNamedQuery(AccomType.ALL_UNASSIGNED_ZERO_CAPACITY_BY_PROPERTY_ID, QueryParameter.with("propertyId", propertyId).parameters());
            }
            if (unassignedRoomTypes.isEmpty()) {
                resolveUnassignedRoomTypeAlerts();
            } else {
                InfoMgrInstanceStepStateEntity step = (InfoMgrInstanceStepStateEntity) multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, InfoMgrInstanceStepStateEntity.BY_ALERT_AND_STEP,
                        QueryParameter.with("alertId", entity.getId()).and("stepId", stepEntity.getId()).parameters());
                if (step != null) {
                    step.setActioned(false);
                    multiPropertyCrudService.save(propertyId, step);
                }

                List<InfoMgrInstanceStepStateEntity> steps = (List<InfoMgrInstanceStepStateEntity>) multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, BY_ALERT_INSTANCE,
                        QueryParameter.with("instanceId", entity.getId()).parameters());
                if (!steps.isEmpty()) {
                    InfoMgrInstanceStepStateEntity first_step = steps.get(0);
                    if (first_step.getActioned()) {
                        first_step.setActioned(false);
                        multiPropertyCrudService.save(propertyId, first_step);
                    }
                }
            }
        }
    }

    @SuppressWarnings("unchecked")
    public boolean isThereAnyUnAssignedRT(int propertyId) {
        List<AccomType> unassignedRoomTypes = crudService.findByNamedQuery(AccomType.ALL_UNASSIGNED_BY_PROPERTY_ID, QueryParameter.with("propertyId", propertyId).parameters());
        return !unassignedRoomTypes.isEmpty();
    }

    public boolean isThereAnyUnAssignedZeroCapacityRT(Integer propertyId) {
        List<AccomType> unassignedRoomTypes = crudService.findByNamedQuery(AccomType.ALL_UNASSIGNED_ZERO_CAPACITY_BY_PROPERTY_ID, QueryParameter.with("propertyId", propertyId).parameters());
        return !unassignedRoomTypes.isEmpty();
    }

    private void actionDiscontinuedMktSegFoundAlert(InfoMgrInstanceEntity entity, InfoMgrStepsEntity stepEntity) {
        if (AlertType.DiscontinuedMarketSegmentsFound.getName().equals(entity.getAlertType().getName())) {
            if (MARK_PROBABLE_DISCONTINUE_MKT_SEG.equals(stepEntity.getText())) {
                List<ProbableDiscontinuedMktSeg> list = convertToProbableDiscontinuedMktSegEntityList(entity);
                crudService.save(list);
            }
        }
    }

    protected List<ProbableDiscontinuedMktSeg> convertToProbableDiscontinuedMktSegEntityList(InfoMgrInstanceEntity entity) {
        String periodInfo = String.format(ResourceUtil.getText("mktSegAreNotPresentPeriod", Locale.US),
                configParamsService.getIntegerParameterValue(IntegrationConfigParamName.FUTURE_DAYS.value()));
        String[] discontinuedMktSegments = StringUtils.stripAll(entity.getDetails()
                .replace(periodInfo, "")
                .split(","));
        Date date = new Date();
        List<ProbableDiscontinuedMktSeg> list = new ArrayList<>();
        List<String> existingDiscontinuedProbableMs = discontinuedMarketSegmentsService.getIdentifiedProbableDiscontinuedMktSegCodes();
        for (String mktSegCode : discontinuedMktSegments) {
            if (!existingDiscontinuedProbableMs.contains(mktSegCode)) {
                ProbableDiscontinuedMktSeg pdms = new ProbableDiscontinuedMktSeg();
                pdms.setMktSegCode(mktSegCode);
                pdms.setCreateDate(date);
                pdms.setLastUpdatedDate(date);
                list.add(pdms);
            }
        }
        return list;
    }

    public void actionInconsistentBARRatesAlert(InfoMgrInstanceEntity entity, InfoMgrStepsEntity stepEntity) {
        if (InconsistentBarRates.getName().equals(entity.getAlertType().getName()) && DISABLE_THE_ALERT.equals(stepEntity.getText())) {
            crudService.executeUpdateByNativeQuery("update info_mgr_type set Enabled = 0 where Name = 'InconsistentBarRates'");
            resolveAllAlerts(InconsistentBarRates, getPropertyId());
        }
    }

    private void actionSufficientBookedDataAvailableAlert(InfoMgrInstanceEntity entity) {
        if (AlertType.SufficientBookedRTDataAvailable.getName().equals(entity.getAlertType().getName()) && Stage.ONE_WAY.getCode().equals(getPropertyStage())) {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("instanceId", entity.getId());
            List<InfoMgrInstanceStepStateEntity> stepsEntityList = crudService.findByNamedQuery(BY_ALERT_INSTANCE, paramMap);
            for (InfoMgrInstanceStepStateEntity stepsEntity : stepsEntityList) {
                if (!stepsEntity.getActioned()) {
                    crudService.delete(stepsEntity);
                }
            }
        }
    }

    private void actionIfMissingRoomTypesInHotelDataAlert(InfoMgrInstanceEntity entity, InfoMgrStepsEntity stepEntity) {
        if (AlertType.MissingRoomTypesInHotelData.getName().equals(entity.getAlertType().getName())
                && Constants.RT_RECODING_VALIDATE_AND_RESOLVE_ALERT_STEP.equals(stepEntity.getText())) {
            roomTypeRecodingService.resolveAlertAndResumeFailedStep();
        }
    }

    protected void actionIfInactivatedRoomTypeAlert(InfoMgrInstanceEntity entity, InfoMgrStepsEntity stepEntity) {
        if (InactivatedRoomType.getName().equals(entity.getAlertType().getName())
                && Constants.REACTIVATE_AND_RESOLVE_ALERT_STEP.equals(stepEntity.getText())) {
            String alertMetadata = extractAlertMetadata(entity.getDescription(), Constants.INACTIVATED_ROOM_TYPES, true);
            if (StringUtils.isBlank(alertMetadata)) {
                return;
            }
            List<String> inactiveRoomTypes = List.of(alertMetadata.split(","));
            roomTypeRecodingService.reactivateInactiveRTsAndTriggerSyncJob(inactiveRoomTypes);
            resolveAllAlerts(InactivatedRoomType, PacmanWorkContextHelper.getPropertyId());
        }
    }

    private void actionIfUnmappedRateCodesFoundAlert(InfoMgrInstanceEntity entity, InfoMgrStepsEntity stepEntity) {
        if (AlertType.UnmappedRateCodesFound.getName().equals(entity.getAlertType().getName())
                && Constants.UNMAPPED_RATE_CODES_ALERT_RESOLVE_STEP.equals(stepEntity.getText())) {
            mktSegRecodingService.resolveAlertAndResumeFailedStep();
        }
    }

    private void actionIfDecisionUploadFailedAlert(InfoMgrInstanceEntity entity, InfoMgrStepsEntity stepEntity) {
        if (AlertType.DecisionUploadFailed.getName().equals(entity.getAlertType().getName()) && Constants.RETRIGGER_TARS_DECISION_UPLOAD.equals(stepEntity.getText())) {
            WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
            Property property = propertyService.getPropertyById(entity.getPropertyId());
            Map<String, Object> jobParameters = new HashedMap();
            jobParameters.put(JobParameterKey.PROPERTY_CODE, property.getCode());
            jobParameters.put(JobParameterKey.CLIENT_CODE, property.getClient().getCode());
            jobParameters.put(JobParameterKey.DATE_START, new Date());
            jobParameters.put(JobParameterKey.USER_ID, workContext.getUserId());
            jobParameters.put(JobParameterKey.EXTERNAL_SYSTEM_CODE, TARS.getCode());
            tarsDecisionService.startJob(jobParameters);
        }
    }

    private void actionIfStatisticalOutlierAlert(InfoMgrInstanceEntity entity, InfoMgrStepsEntity stepEntity) {
        //FPLOS Alert Specific Updates
        if (entity.getAlertType().getName().equals(AlertType.LowestBarFplosCheckFail.getName())
                || entity.getAlertType().getName().equals(AlertType.HighestBarFplosCheckFail.getName())
                || entity.getAlertType().getName().equals(AlertType.LowestOptimizationDecisionCheckFail.getName())
                || entity.getAlertType().getName().equals(AlertType.HighestOptimizationDecisionCheckFail.getName())) {
            performStatisticalOutlierAlertActions(entity, stepEntity);
        }
    }

    protected void performStatisticalOutlierAlertActions(InfoMgrInstanceEntity alertEntity, InfoMgrStepsEntity stepEntity) {
        if (!stepEntity.getText().equals(StatisticalOutlierAlertConstants.I_WOULD_OPEN_SUPPORT_CASE)) {
            String userAlertActionFromUi = StringUtils.EMPTY;
            String userLowestAlertActionFromUi = null;
            String userHighestAlertActionFromUi = null;
            Long decisionJobExecutionId = extractJobId(alertEntity.getDetails(), StatisticalOutlierAlertConstants.DECISION_JOB_ID_KEY);
            switch (stepEntity.getText()) {
                case StatisticalOutlierAlertConstants.IGNORE_ONCE_UPLOAD_DECISIONS:
                case StatisticalOutlierAlertConstants.IGNORE_ONCE_CONTINUE_PROCESSING:
                    updateConfigParamForStatisticalOutlierAlert(alertEntity, StatisticalOutlierAlertConstants.ALERT_CONFIG_VALUE_SKIP_ONCE);
                    userAlertActionFromUi = StatisticalOutlierAlertConstants.ALERT_CONFIG_VALUE_SKIP_ONCE;
                    break;
                case StatisticalOutlierAlertConstants.IGNORE_ALWAYS_UPLOAD_DECISIONS:
                case StatisticalOutlierAlertConstants.IGNORE_ALWAYS_CONTINUE_PROCESSING:
                    updateConfigParamForStatisticalOutlierAlert(alertEntity, StatisticalOutlierAlertConstants.ALERT_CONFIG_VALUE_SKIP_ALWAYS);
                    userAlertActionFromUi = StatisticalOutlierAlertConstants.ALERT_CONFIG_VALUE_SKIP_ALWAYS;
                    break;
                default:
                    break;
            }
            switch (AlertType.valueOf(alertEntity.getAlertType().getName())) {
                case LowestOptimizationDecisionCheckFail:
                    userLowestAlertActionFromUi = userAlertActionFromUi;
                    LOGGER.info("Resume failed job for LowestOptimizationDecisionCheckFail with " +
                            "decisionJobExecutionId:"+ decisionJobExecutionId + " userLowestAlertActionFromUi:" + userLowestAlertActionFromUi
                            + "userHighestAlertActionFromUi:" + userHighestAlertActionFromUi);
                    resumeFailedJob(decisionJobExecutionId, userLowestAlertActionFromUi, userHighestAlertActionFromUi);
                    break;
                case HighestOptimizationDecisionCheckFail:
                    userHighestAlertActionFromUi = userAlertActionFromUi;
                    resumeFailedJob(decisionJobExecutionId, userLowestAlertActionFromUi, userHighestAlertActionFromUi);
                    break;
                default:
                    resumeFailedJob(decisionJobExecutionId, userAlertActionFromUi, null);
                    break;
            }
        }
    }

    private void resumeFailedJob(Long failedJobExecutionId, String userLowestAlertActionFromUi, String userHighestAlertActionFromUi) {
        if (failedJobExecutionId != null) {
            Long jobInstanceId = jobMonitorService.getJobInstanceId(failedJobExecutionId);
            if (null != jobInstanceId) {
                JobExecution lastExecution = jobMonitorService.getJobDetail(jobInstanceId).getLastExecution();
                if (!ExecutionStatus.isEndState(lastExecution.getExecutionStatus())) {
                    LOGGER.info("resumefailedJob() - Closing active problem and resuming failed job. JobExecutionId: " + lastExecution.getJobExecutionId());
                    if (nonNull(userLowestAlertActionFromUi)) {
                        jobMonitorService.insertAdditionalJobExecutionParam(lastExecution.getJobExecutionId(), USER_ACTION_VALUE_FOR_LOWEST_OPTIMIZATION_DECISION_CHECK_ALERT, userLowestAlertActionFromUi);
                    }
                    if (nonNull(userHighestAlertActionFromUi)) {
                        jobMonitorService.insertAdditionalJobExecutionParam(lastExecution.getJobExecutionId(), USER_ACTION_VALUE_FOR_HIGHEST_OPTIMIZATION_DECISION_CHECK_ALERT, userHighestAlertActionFromUi);
                    }
                    jobService.restartJob(lastExecution.getJobExecutionId());
                    problemService.closeActiveProblemsIn(lastExecution);
                }
            }
        }
    }

    private void updateConfigParamForStatisticalOutlierAlert(InfoMgrInstanceEntity alertEntity, String newAlertConfigValue) {
        switch (AlertType.valueOf(alertEntity.getAlertType().getName())) {
            case LowestBarFplosCheckFail:
                configParamsService.addParameterValue(FeatureTogglesConfigParamName.LOWEST_BAR_FPLOS_CHECKS.value(), newAlertConfigValue);
                break;
            case HighestBarFplosCheckFail:
                configParamsService.addParameterValue(FeatureTogglesConfigParamName.HIGHEST_BAR_FPLOS_CHECKS.value(), newAlertConfigValue);
                break;
            case LowestOptimizationDecisionCheckFail:
                WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
                String userName = getUserName(alertEntity.getPropertyId(), workContext.getUserId());
                String existingValue = configParamsService.getParameterValue(FeatureTogglesConfigParamName.LOWEST_OPTIMIZATION_DECISION_CHECK.value());
                LOGGER.info(FeatureTogglesConfigParamName.LOWEST_OPTIMIZATION_DECISION_CHECK.value() + "Old_Value=" + existingValue + " New_Value = " + newAlertConfigValue + " Updated by User=" + userName);
                configParamsService.addParameterValue(FeatureTogglesConfigParamName.LOWEST_OPTIMIZATION_DECISION_CHECK.value(), newAlertConfigValue);
                break;
            case HighestOptimizationDecisionCheckFail:
                configParamsService.addParameterValue(FeatureTogglesConfigParamName.HIGHEST_OPTIMIZATION_DECISION_CHECK.value(), newAlertConfigValue);
                break;
            default:
                break;
        }
    }

    public Long extractJobId(String details, String jobExecutionKeyInDetails) {
        final String jobIdStr = extractAlertMetadata(details, jobExecutionKeyInDetails, false);
        if (jobIdStr != null) {
            return Long.valueOf(jobIdStr);
        }
        return null;
    }

    public String extractAlertMetadata(String alertDescription, String param, boolean isMetadataInJson) {
        int index = alertDescription.indexOf(param);
        if (index != -1) {
            int indexOfNextColon = alertDescription.indexOf(":", index) + 1;
            int indexOfNextQuama = isMetadataInJson ? alertDescription.length() : alertDescription.indexOf(",", index);
            if (indexOfNextQuama == -1) {
                indexOfNextQuama = alertDescription.length();
            }
            if ("-1".equals(alertDescription.substring(indexOfNextColon, indexOfNextQuama))) {
                return null;
            } else {
                return alertDescription.substring(indexOfNextColon, indexOfNextQuama);
            }
        } else {
            return null;
        }
    }

    public void resumeJobFailedByUnmappedRateCodesAlert(String description) {
        Long failedJobExecutionId = extractJobId(description, UNMAPPED_RATE_CODES_ALERT_METADATA);
        resumeFailedJob(failedJobExecutionId, null, null);
    }

    public void resumeJobFailedByPMSMigrationSteps(String description) {
        Long failedJobExecutionId = extractJobId(description, PMS_MIGRATION_FAILED_JOB_EXECUTION_ID);
        resumeFailedJob(failedJobExecutionId, null, null);
    }

    public void resumeJobFailedByRoomTypeRecodingStep(String details) {
        String alertMetadataJson = extractAlertMetadata(details, RT_RECODING_ALERT_METADATA, true);
        Gson jsonParser = new Gson();
        Map<String, String> alertMetadataMap = jsonParser.fromJson(alertMetadataJson, Map.class);
        final Long roomTypeRecodingIdentifiedJobExecutionId = Long.valueOf(alertMetadataMap.get(RT_RECODING_JOB_EXECUTION_ID));
        resumeFailedJob(roomTypeRecodingIdentifiedJobExecutionId, null, null);
    }

    public void markResolvedIfActionStepClicked(InfoMgrInstanceEntity instance, int propertyId) {
        InfoMgrStatusEntity status = multiPropertyCrudService.find(propertyId, InfoMgrStatusEntity.class, Constants.ALERT_STATUS_RESOLVED_ID);
        instance.setAlertStatus(status);
        instance.setScore(0);
        instance.setLastModificationDate(new Date());
        instance = multiPropertyCrudService.save(instance);

        InfoMgrHistoryEntity history = createHistory(Constants.ALERT_HISTORY_RESOLVED_ID, instance);
        multiPropertyCrudService.save(propertyId, history);
    }

    /**
     * This method get the Count of Open Alerts, Exceptions and Notifications
     */
    @SuppressWarnings("unchecked")
    public List<Integer> getOpenAllCountForClient() {
        Map<Integer, Property> propertiesById = getAuthorizedProperties();
        List<Integer> propertyIds = new ArrayList<>(propertiesById.keySet());
        return getOpenAllCount(propertyIds);
    }

    public List<Integer> getOpenAllCount() {
        LOGGER.warn("getOpenAllCount called from granite, does it really need to be? Calling new getOpenAllCountForWorkContext");
        return getOpenAllCountForWorkContext();
    }

    public List<Integer> getOpenAllCountForWorkContext() {
        List<Integer> validPropertyIds;
        if (SystemConfig.isInfoMgrPropertyAndPropertyGroupEnabled()) {
            validPropertyIds = new ArrayList<>(validatePropertyIds(null).keySet());
        } else {
            validPropertyIds = new ArrayList<>(getAuthorizedProperties().keySet());
        }

        return getOpenAllCount(validPropertyIds);
    }

    @SuppressWarnings("unchecked")
    private List<Integer> getOpenAllCount(List<Integer> propertyIds) {
        // If there are no property ids, return an empty list of counts
        if (propertyIds.isEmpty()) {
            return new ArrayList<>();
        }

        // Need to determine list of property ids to query for.
        // This list should include the properties in the context
        // and properties that are not already in the cache in
        // hopes to make the result more accurate.
        List<Integer> propertyIdsToQuery = new ArrayList<Integer>();
        propertyIdsToQuery.addAll(propertyIds);

        // Get the propertyIds in the work context and those
        // already in the cache.  Since we want to query for
        // property ids in the context, we should remove those
        // from the propertyIds already in the cache
        List<Integer> propertyIdsInContext = propertyGroupService.getPropertyContextAsList();
        List<Integer> propertyIdsInCache = informationManagerPropertyCountCache.getPropertyIds();

        // Remove the property ids that are cached, except for the ones in the work context
        // so those will be queried for
        propertyIdsToQuery.removeAll(ListUtils.removeAll(propertyIdsInCache, propertyIdsInContext));

        // For each property, get the counts of each exception type
        List<Object[]> allPropertyCategoryCounts = (List<Object[]>) multiPropertyCrudService.findByNamedQueryUnionAcrossProperties(propertyIdsToQuery, InfoMgrInstanceEntity.NEW_ALERTS_NOTIFICATIONS_EXCEPTIONS_COUNTS, QueryParameter.with("propertyId", propertyIdsToQuery).parameters());

        Map<Integer, HashMap<String, Integer>> propertyCategoryCountMap = convertPropertyCategoryCountsToMap(allPropertyCategoryCounts);
        updateInfoMgrPropertyCountCacheFromAMap(propertyIdsToQuery, propertyCategoryCountMap);
        // Get the total category counts from the cache
        Map<String, Integer> categoryCounts = informationManagerPropertyCountCache.getCategoryCounts(propertyIds, Constants.ALERT_CATEGORY, Constants.SYSTEM_EXCEPTION_CATEGORY, Constants.EXCEPTION_CATEGORY);

        // Need to return a list of the counts in a specific order
        List<Integer> counts = new ArrayList<>();

        // Put the Alert Category Count on the Return List
        Integer alertCategoryCount = categoryCounts.get(Constants.ALERT_CATEGORY);
        counts.add(alertCategoryCount != null ? alertCategoryCount : 0);

        // Put the System Exception Category Count on the Return List
        Integer systemExceptionCount = categoryCounts.getOrDefault(Constants.SYSTEM_EXCEPTION_CATEGORY, 0);
        systemExceptionCount = updateExceptionCountAsPerTheLicense(propertyIdsToQuery, systemExceptionCount, Constants.SYSTEM_EXCEPTION_CATEGORY);
        counts.add(systemExceptionCount);

        // Put the Exception Category Count on the Return List
        Integer exceptionCount = categoryCounts.get(Constants.EXCEPTION_CATEGORY);
        counts.add(exceptionCount != null ? exceptionCount : 0);

        return counts;
    }

    private Integer updateExceptionCountAsPerTheLicense(List<Integer> propertyIdsToQuery, Integer systemExceptionCount, String category) {
        for (Integer propertyId : propertyIdsToQuery) {
            if (isExceptionTabAvailableInLicensePackage(propertyId) && systemExceptionCount != 0) {
                Map<String, Integer> categoryCountsOfProperty = informationManagerPropertyCountCache.get(propertyId);
                int count = categoryCountsOfProperty != null ? categoryCountsOfProperty.getOrDefault(category, 0) : 0;
                systemExceptionCount -= count;
            }
        }
        return systemExceptionCount;
    }

    public void updateInfoMgrPropertyCountCacheFromAMap(List<Integer> propertyIdsToQuery, Map<Integer, HashMap<String, Integer>> propertyCategoryCountMap) {
        for (Integer propertyID : propertyIdsToQuery) {
            if (!propertyCategoryCountMap.containsKey(propertyID)) {
                informationManagerPropertyCountCache.putPropertyCategoryCount(propertyID, Constants.ALERT_CATEGORY, 0);
                informationManagerPropertyCountCache.putPropertyCategoryCount(propertyID, Constants.SYSTEM_EXCEPTION_CATEGORY, 0);
                informationManagerPropertyCountCache.putPropertyCategoryCount(propertyID, Constants.EXCEPTION_CATEGORY, 0);
            } else {
                HashMap<String, Integer> categoryMapForThisProperty = propertyCategoryCountMap.get(propertyID);
                updateInformationManagerPropertyCountCacheForACategory(propertyID, Constants.ALERT_CATEGORY, categoryMapForThisProperty);
                updateInformationManagerPropertyCountCacheForACategory(propertyID, Constants.SYSTEM_EXCEPTION_CATEGORY, categoryMapForThisProperty);
                updateInformationManagerPropertyCountCacheForACategory(propertyID, Constants.EXCEPTION_CATEGORY, categoryMapForThisProperty);
            }
        }
    }

    public void updateInformationManagerPropertyCountCacheForACategory(Integer propertyID, String categoryName, HashMap<String, Integer> categoryMapForThisProperty) {
        if (categoryMapForThisProperty.containsKey(categoryName)) {
            informationManagerPropertyCountCache.putPropertyCategoryCount(propertyID, categoryName, categoryMapForThisProperty.get(categoryName));
        } else {
            informationManagerPropertyCountCache.putPropertyCategoryCount(propertyID, categoryName, 0);
        }
    }

    public Map<Integer, HashMap<String, Integer>> convertPropertyCategoryCountsToMap(List<Object[]> allPropertyCategoryCounts) {
        Map<Integer, HashMap<String, Integer>> retMap = new HashMap<>();
        if (null != allPropertyCategoryCounts) {
            HashMap<String, Integer> categoryMapForThisProperty;
            for (Object[] propertyCategoryCounts : allPropertyCategoryCounts) {
                Integer propertyID = (Integer) propertyCategoryCounts[0];
                if (!retMap.containsKey(propertyID)) {
                    categoryMapForThisProperty = new HashMap<>();
                    retMap.put(propertyID, categoryMapForThisProperty);
                } else {
                    categoryMapForThisProperty = retMap.get(propertyID);
                }
                categoryMapForThisProperty.put((String) propertyCategoryCounts[1], ((Long) propertyCategoryCounts[2]).intValue());
            }
        }
        return retMap;
    }

    @SuppressWarnings("unchecked")
    public List<InfoMgrTypeEntity> getAllAlertTypes() {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("categoryName", Constants.ALERT_CATEGORY);
        return multiPropertyCrudService.findByNamedQueryForSingleProperty(PacmanThreadLocalContextHolder.getWorkContext().getPropertyId(),
                InfoMgrTypeEntity.BY_CATEGORY, paramMap);
    }

    /**
     * This method search for Alerts with criteria, this only returns alerts that the current user has permissions to see.  Which will
     * bork if a system job is running this, because the user Id for system user is "System", and not a number.  Nice.
     */
    public List<Alert> searchAlertsWithCriteria(SearchCriteriaDTO searchCriteria) {
        try {
            List<Alert> alerts = new ArrayList<>();
            List<InfoMgrExcepNotifEntity> listFoundExceptionsRaised = searchWithCriteria(searchCriteria, Constants.ALERT_CATEGORY);
            Map<Integer, Set<String>> permissionPerProperty = getPermissionPerProperty(searchCriteria.getPropertyIds());

            //Adding propertyCode into search result
            for (InfoMgrExcepNotifEntity objNotificationEntity : listFoundExceptionsRaised) {
                Alert objAlert = new Alert(objNotificationEntity);
                objAlert.setPropertyCode(objNotificationEntity.getPropertyCode());
                objAlert.setPropertyName(objNotificationEntity.getPropertyName());
                objAlert.setPropertyDisplayLabelField(objNotificationEntity.getPropertyDisplayLabelField());
                objAlert.setSteps(getSteps(objNotificationEntity.getId(), objNotificationEntity.getPropertyId()));
                applyPermissionsToSteps(objAlert.getSteps(), permissionPerProperty.get(objAlert.getPropertyId()));
                alerts.add(objAlert);
            }

            return getCRSDataDidNotArriveAlertOnTop(alerts);
        } catch (Exception e) {
            LOGGER.error("Search alert with criteria failed." + e);
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Search alert with criteria failed." + e);
        }
    }

    public List<TentativeGroupForecastDataMismatchAlertData> getMismatchDataForPmsCrsMismatchAlert() {
        List<Integer> fsStatusIds = service.getFunctionSpaceStatusesToConsiderForTentativeGroupForecasting();
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();

        Integer pmsCrsWindow = getPMSCRSWindow();
        Integer aseamWindow = getGroupPricingExtendedWindowDays();
        boolean isVirtualProperty = propertyService.isPropertyVirtual(propertyId);
        List<String> pcrsPhysicalPropertyCodes = getPcrsPhysicalPropertyCodes(isVirtualProperty, propertyId);

        ClientPropertyView clientPropertyView = globalCrudService.findByNamedQuerySingleResult(ClientPropertyView.BY_PROPERTY_ID, QueryParameter.with(PROPERTY_ID, propertyId).parameters());
        boolean isPCRSProperty = ExternalSystem.PCRS.name().equals(clientPropertyView.getExternalSystem());

        return transform(tentativeGroupForecastAlertRepository.getTentativeGroupForecastAlertDetails(pmsCrsWindow, aseamWindow, fsStatusIds, isVirtualProperty, isPCRSProperty, pcrsPhysicalPropertyCodes));
    }

    private List<String> getPcrsPhysicalPropertyCodes(boolean isVirtualProperty, Integer propertyId) {
        List<String> pcrsPhysicalPropertyCodes = List.of();
        if (isVirtualProperty) {
            List<VirtualPropertyMapping> mappingsForVirtualProperty = virtualPropertyMappingService.getMappingsForVirtualProperty(propertyId);
            pcrsPhysicalPropertyCodes = mappingsForVirtualProperty.stream()
                    .filter(mapping -> mapping.getExternalSystem().equals(ExternalSystem.PCRS.name()))
                    .map(VirtualPropertyMapping::getPhysicalPropertyCode)
                    .collect(toList());
        }
        return pcrsPhysicalPropertyCodes;
    }

    private Integer getPMSCRSWindow() {
        return configParamsService.getIntegerParameterValue(IPConfigParamName.FORECASTING_FORECAST_WINDOW_BDE.value());
    }

    private Integer getGroupPricingExtendedWindowDays() {
        return configParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_EXTENDED_WINDOW_DAYS.value());
    }

    private List<TentativeGroupForecastDataMismatchAlertData> transform(List<Object[]> mismatchData) {
        return mismatchData
                .stream()
                .map(this::transform)
                .collect(Collectors.toList());
    }

    private TentativeGroupForecastDataMismatchAlertData transform(Object[] mismatchData) {
        TentativeGroupForecastDataMismatchAlertData alertData = new TentativeGroupForecastDataMismatchAlertData();
        alertData.setGroupArrivalDate(LocalDate.parse(mismatchData[0].toString()));
        alertData.setGroupName((String) mismatchData[1]);
        alertData.setWithinPmsCrsWindow((String) mismatchData[2]);
        alertData.setPmsCrsId((String) mismatchData[3]);
        alertData.setSncId((String) mismatchData[4]);
        alertData.setPmsCrsIdInSnc((String) mismatchData[5]);
        alertData.setImportance((String) mismatchData[6]);

        return alertData;
    }

    /**
     * This method search for Alerts with criteria, this only returns alerts that the current user has permissions to see.  Which will
     * bork if a system job is running this, because the user Id for system user is "system", and not a number.  Nice.
     */
    public List<Alert> getAlertWithSearchCriteria(SearchCriteriaDTO searchCriteria) {
        try {
            List<InfoMgrExcepNotifEntity> listFoundExceptionsRaised = getInstancesSortedOnScore(searchCriteria, Constants.ALERT_CATEGORY);
            List<Alert> alerts = new ArrayList<>();
            for (InfoMgrExcepNotifEntity objNotificationEntity : listFoundExceptionsRaised) {
                Alert objAlert = new Alert(objNotificationEntity);
                objAlert.setPropertyCode(objNotificationEntity.getPropertyCode());
                objAlert.setPropertyName(objNotificationEntity.getPropertyName());
                objAlert.setPropertyDisplayLabelField(objNotificationEntity.getPropertyDisplayLabelField());
                alerts.add(objAlert);
            }
            return getCRSDataDidNotArriveAlertOnTop(alerts);
        } catch (Exception e) {
            LOGGER.error("Search alert with criteria failed." + e);
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Search alert with criteria failed." + e);
        }
    }

    public Alert getExtendedDetailsForInstance(Alert objAlert) {
        return getExtendedDetailsForAlertInstance(objAlert);
    }

    public List<ScoreRange> getScores() {
        List<ScoreRange> scoreList = new ArrayList<>();
        for (ScoreRange scoreRange : ScoreRange.values()) {
            scoreList.add(scoreRange);
        }
        return scoreList;
    }

    /**
     * This method puts the CRS data did not arrive alerts on top.
     */
    private List<Alert> getCRSDataDidNotArriveAlertOnTop(List<Alert> alertsList) {
        Comparator<Alert> scoreComparator = (alert1, alert2) -> Integer.compare(alert1.getScore(), alert2.getScore());
        Comparator<Alert> alertTypeComparator = (alert1, alert2) ->
                AlertType.CRSDataDidNotArrive.toString().equalsIgnoreCase(alert1.getType().toString()) &&
                        !(AlertType.CRSDataDidNotArrive.toString().equalsIgnoreCase(alert2.getType().toString())) ? -1 : 0;

        //Sort by score reversed first so we have the correct order of highest score to lowest
        alertsList.sort(scoreComparator.reversed());
        //Now sort so that all the crsDataDidNotArrive alerts are at the top reguardless of if their score is the highest
        alertsList.sort(alertTypeComparator);
        return alertsList;
    }

    public String getCategory(Integer alertId, Integer propertyId) {
        InfoMgrInstanceEntity objInstance = multiPropertyCrudService.find(propertyId, InfoMgrInstanceEntity.class, alertId);
        if (null != objInstance) {
            return objInstance.getAlertType().getAlertCategory();
        }
        return null;
    }

    public Date getCaughtUpDateForPropertyFromPopoutURL(Integer propertyId) {
        return getCaughtUpDateForProperty(propertyId);
    }

    /**
     * This method return the Business date for the property.
     */
    public Date getBusinessDateForPropertyFromPopoutURL(Integer propertyId) {
        return getBusinessDateForProperty(propertyId);
    }

    public AccomClass getMasterClassForProperty(Integer propertyId) {
        return (AccomClass) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId, AccomClass.GET_MASTER_CLASS, QueryParameter.with("propertyId", propertyId).parameters());
    }

    public AccomType getRohRoomTypeForProperty(Integer propertyId) {
        return (AccomType) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId, AccomType.GET_ROH_TYPE, QueryParameter.with("propertyId", propertyId).parameters());
    }

    public InfoMgrStepsEntity getStepById(Integer propertyId, Integer stepId) {
        return multiPropertyCrudService.find(propertyId, InfoMgrStepsEntity.class, stepId);
    }

    public String getSubLevelForConfig(InformationMgrAlertConfigEntity config) {
        return getSubLevelForId(config);
    }

    /**
     * This method returns the Count of records for selected criteria
     */
    public Integer alertsCountByCriteria(SearchCriteriaDTO searchCriteria) {
        return searchWithCriteria(searchCriteria, Constants.ALERT_CATEGORY).size();
    }

    public InformationManagerPropertyCountCache getInformationManagerPropertyCountCache() {
        return informationManagerPropertyCountCache;
    }

    public void setInformationManagerPropertyCountCache(InformationManagerPropertyCountCache informationManagerPropertyCountCache) {
        this.informationManagerPropertyCountCache = informationManagerPropertyCountCache;
    }

    public List<InfoMgrInstanceEntity> getByCreatedLastModifiedCategory(int propertyId, String alertCategory, Date beginDate, Date endDate, int alertStatus) {

        return (List<InfoMgrInstanceEntity>) multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId,
                InfoMgrInstanceEntity.FIND_BY_LASTMODIFIED_AND_BY_CATEGORY,
                QueryParameter.with("propertyId", propertyId).and("category", alertCategory).and("beginDate", beginDate).and("endDate", endDate).and("id", alertStatus).parameters());

    }

    public boolean isAlertEnable(AlertType alertType){
        boolean isEnabled;
        try{
            WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
            InfoMgrTypeEntity alertTypeEntity = getAlertType(workContext.getPropertyId(), alertType.getName());
            isEnabled =  alertTypeEntity.isEnabled();
        }catch (Exception e){
            LOGGER.error("Error while fetching InfoMgrTypeEntity. Error : ", e);
            isEnabled = true;
        }
        return isEnabled;
    }

    public int disableBarRatesNotLogicalAlertForAProperty(Integer propertyId) {
        return multiPropertyCrudService.executeNamedUpdateOnSingleProperty(propertyId, InfoMgrTypeEntity.DISABLE_BAR_RATES_NOT_LOGICAL_ALERT, null);
    }

    public void suspendOrRevertRaisedAlert(Integer alertId, Integer propertyId, Integer stepId, boolean suspended) {

        InfoMgrInstanceEntity objAlert = multiPropertyCrudService.find(propertyId, InfoMgrInstanceEntity.class, alertId);

        if (!suspended) {
            //This is strictly used for UI if the user unchecks the suspend option.
            InfoMgrStatusEntity statusActioned = multiPropertyCrudService.find(objAlert.getPropertyId(),
                    InfoMgrStatusEntity.class, Constants.ALERT_STATUS_VIEWED_ID);
            objAlert.incrementScore();
            objAlert.setAlertStatus(statusActioned);
            objAlert.setLastModificationDate(new Date());
            objAlert = multiPropertyCrudService.save(objAlert);

            doStepAction(propertyId, alertId, stepId, false);
            InfoMgrHistoryEntity history = createHistory(
                    Constants.ALERT_HISTORY_REVERTED_ID, objAlert);
            multiPropertyCrudService.save(propertyId, history);
        } else {
            InfoMgrStatusEntity statusSuspended = multiPropertyCrudService.find(objAlert.getPropertyId(),
                    InfoMgrStatusEntity.class, Constants.ALERT_STATUS_SUSPENDED_ID);
            objAlert.setAlertStatus(statusSuspended);
            objAlert.setScore(0);
            objAlert.setLastModificationDate(new Date());
            objAlert = multiPropertyCrudService.save(objAlert);

            doStepAction(propertyId, alertId, stepId, true);
            InfoMgrHistoryEntity history = createHistory(
                    Constants.ALERT_HISTORY_SUSPENDED_ID, objAlert);
            multiPropertyCrudService.save(propertyId, history);
        }
    }

    public void doStepAction(Integer propertyId, Integer alertId, Integer stepId, boolean action) {
        @SuppressWarnings("unchecked")
        List<InfoMgrInstanceStepStateEntity> steps = (List<InfoMgrInstanceStepStateEntity>) multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, InfoMgrInstanceStepStateEntity.BY_ALERT_AND_STEP,
                QueryParameter.with("alertId", alertId).and("stepId", stepId).parameters());
        if (steps != null && steps.size() == 1) {
            InfoMgrInstanceStepStateEntity step = steps.get(0);
            step.setActioned(action);
            multiPropertyCrudService.save(propertyId, step);
        }
    }

    public List<InfoMgrInstanceEntity>  getOpenAlertInstances(List<String> alertTypeNames, List<String> statuses, String alertCategory){
        return crudService.findByNamedQuery
                (InfoMgrInstanceEntity.FIND_BY_TYPES_AND_STATUSES, QueryParameter.with("types", alertTypeNames)
                        .and("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("statuses", statuses)
                        .and("category", alertCategory)
                        .parameters());
    }

    private boolean isExceptionTabAvailableInLicensePackage(Integer propertyId) {
        return configParamsService.getBooleanParameterValue(FEATURE_LICENSING_ENABLED) && !licenseService.isLicenseEnabledFor(propertyId, LicenseFeatureConstants.INFO_MGR_EXCEPTIONS);
    }
}