package com.ideas.tetris.pacman.services.scheduledreport.entity.criteria;


import com.ideas.tetris.pacman.services.reports.dataextraction.entity.DataExtractionHotel;
import com.ideas.tetris.pacman.services.scheduledreport.entity.Language;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.annotations.ColumnHeader;
import com.ideas.tetris.platform.common.util.FieldDefinition;
import org.apache.commons.collections.CollectionUtils;
import org.joda.time.LocalDate;

import java.util.List;

public class DataExtractionReportCriteria extends BaseReportCriteria {

    private boolean hotel;
    private boolean hotelADR;
    private boolean hotelArrivals;
    private boolean hotelBAR;
    private boolean hotelBARByDay;
    private boolean hotelCapacity;
    private boolean hotelCancellations;
    private boolean hotelDepartures;
    private boolean hotelLRV;
    private boolean hotelNoShow;
    private boolean hotelOccupancyForecast;
    private boolean hotelOutOfOrder;
    private boolean hotelOverbooking;
    private boolean hotelRevenue;
    private boolean hotelRevenueSTLY;
    private boolean hotelRevenueST2Y;
    private boolean hotelRevenueST19;
    private boolean hotelRevPAR;
    private boolean hotelRoomsNotAvailableOutOfOrder;
    private boolean hotelRoomsSold;
    private boolean hotelRoomsSoldSTLY;
    private boolean hotelRoomsSoldST2Y;
    private boolean hotelRoomsSoldST19;
    private boolean hotelSpecialEvent;
    private boolean hotelSystemUnconstrainedDemand;
    private boolean hotelUserUnconstrainedDemand;
    private boolean hotelWash;
    private boolean hotelProfit;
    private boolean hotelProfitSTLY;
    private boolean hotelProPAR;
    private boolean hotelProPARSTLY;
    private boolean hotelProPOR;
    private boolean hotelProPORSTLY;
    private boolean hotelInventoryLimit;

    private boolean roomClass;
    private boolean roomClassArrivals;
    private boolean roomClassADR;
    private boolean roomClassBAR;
    private boolean roomClassCancellations;
    private boolean roomClassCapacity;
    private boolean roomClassDepartures;
    private boolean roomClassOccupancyForecast;
    private boolean roomClassLRV;
    private boolean roomClassNoShow;
    private boolean roomClassOutOfOrder;
    private boolean roomClassRevenue;
    private boolean roomClassRevenueSTLY;
    private boolean roomClassRevenueST2Y;
    private boolean roomClassRevenueST19;
    private boolean roomClassRevPAR;
    private boolean roomClassRoomsNotAvailableOutOfOrder;
    private boolean roomClassRoomsSold;
    private boolean roomClassRoomsSoldSTLY;
    private boolean roomClassRoomsSoldST2Y;
    private boolean roomClassRoomsSoldST19;
    private boolean roomClassSystemUnconstrainedDemand;
    private boolean roomClassUserUnconstrainedDemand;
    private boolean roomClassProfit;
    private boolean roomClassProfitSTLY;
    private boolean roomClassProPAR;
    private boolean roomClassProPARSTLY;
    private boolean roomClassProPOR;
    private boolean roomClassProPORSTLY;

    private boolean roomType;
    private boolean roomTypeADR;
    private boolean roomTypeArrivals;
    private boolean roomTypeBAR;
    private boolean roomTypeCancellations;
    private boolean roomTypeCapacity;
    private boolean roomTypeDepartures;
    private boolean roomTypeNoShow;
    private boolean roomTypeOutOfOrder;
    private boolean roomTypeOverbooking;
    private boolean roomTypeRevenue;
    private boolean roomTypeRevenueSTLY;
    private boolean roomTypeRevenueST2Y;
    private boolean roomTypeRevenueST19;
    private boolean roomTypeRevPAR;
    private boolean roomTypeRoomsNotAvailableOutOfOrder;
    private boolean roomTypeRoomsSold;
    private boolean roomTypeRoomsSoldSTLY;
    private boolean roomTypeRoomsSoldST2Y;
    private boolean roomTypeRoomsSoldST19;

    private boolean marketSegment;
    private boolean marketSegmentArrivals;
    private boolean marketSegmentADR;
    private boolean marketSegmentCancellations;
    private boolean marketSegmentDepartures;
    private boolean marketSegmentNoShow;
    private boolean marketSegmentOccupancyForecast;
    private boolean marketSegmentRevenue;
    private boolean marketSegmentRevenueSTLY;
    private boolean marketSegmentRevenueST2Y;
    private boolean marketSegmentRevenueST19;
    private boolean marketSegmentRoomsSold;
    private boolean marketSegmentRoomsSoldSTLY;
    private boolean marketSegmentRoomsSoldST2Y;
    private boolean marketSegmentRoomsSoldST19;

    private boolean forecastGroup;
    private boolean forecastGroupADR;
    private boolean forecastGroupArrivals;
    private boolean forecastGroupCancellations;
    private boolean forecastGroupDepartures;
    private boolean forecastGroupNoShow;
    private boolean forecastGroupOccupancyForecast;
    private boolean forecastGroupRevenue;
    private boolean forecastGroupRevenueSTLY;
    private boolean forecastGroupRevenueST2Y;
    private boolean forecastGroupRevenueST19;
    private boolean forecastGroupRoomsSold;
    private boolean forecastGroupRoomsSoldSTLY;
    private boolean forecastGroupRoomsSoldST2Y;
    private boolean forecastGroupRoomsSoldST19;
    private boolean forecastGroupSystemGroupWashPerFG;
    private boolean forecastGroupSystemUnconstrainedDemand;
    private boolean forecastGroupUserUnconstrainedDemand;
    private boolean forecastGroupProfit;
    private boolean forecastGroupProfitSTLY;
    private boolean forecastGroupProPOR;
    private boolean forecastGroupProPORSTLY;

    private boolean businessView;
    private boolean businessViewADR;
    private boolean businessViewArrivals;
    private boolean businessViewCancellations;
    private boolean businessViewDepartures;
    private boolean businessViewNoShow;
    private boolean businessViewOccupancyForecast;
    private boolean businessViewRevenue;
    private boolean businessViewRoomsSold;
    private boolean businessViewRevenueSTLY;
    private boolean businessViewRevenueST2Y;
    private boolean businessViewRevenueST19;
    private boolean businessViewRoomsSoldSTLY;
    private boolean businessViewRoomsSoldST2Y;
    private boolean businessViewRoomsSoldST19;

    private boolean los;
    private int maxLOS = -1;

    private boolean competitor;
    private int comp1 = -1;
    private int comp2 = -1;
    private int comp3 = -1;
    private int comp4 = -1;
    private int comp5 = -1;

    private Integer propertyId;
    private String jndiName;
    @ColumnHeader(titleKey = "startDate", order = 105)
    private LocalDate startDate;
    @ColumnHeader(titleKey = "endDate", order = 106)
    private LocalDate endDate;
    private int isRollingDate = -1;
    private boolean showLastYearData;
    private boolean showLast2YearsData;
    private boolean showYear2019Data;
    private int userId;
    private boolean sheetPerCriteria;
    private Language language;
    private String rollingStartDate;
    private String rollingEndDate;
    private boolean continuousPricingEnabled;
    private boolean ignorePagination;
    private int usePhysicalCapacity = -1;

    public boolean isHotel() {
        return hotel;
    }

    public void setHotel(boolean hotel) {
        this.hotel = hotel;
    }

    public boolean isHotelADR() {
        return hotelADR;
    }

    public void setHotelADR(boolean hotelADR) {
        this.hotelADR = hotelADR;
    }

    public boolean isHotelArrivals() {
        return hotelArrivals;
    }

    public void setHotelArrivals(boolean hotelArrivals) {
        this.hotelArrivals = hotelArrivals;
    }

    public boolean isHotelBAR() {
        return hotelBAR;
    }

    public void setHotelBAR(boolean hotelBAR) {
        this.hotelBAR = hotelBAR;
    }

    public boolean isHotelBARByDay() {
        return hotelBARByDay;
    }

    public void setHotelBARByDay(boolean hotelBARByDay) {
        this.hotelBARByDay = hotelBARByDay;
    }

    public boolean isHotelCapacity() {
        return hotelCapacity;
    }

    public void setHotelCapacity(boolean hotelCapacity) {
        this.hotelCapacity = hotelCapacity;
    }

    public boolean isHotelCancellations() {
        return hotelCancellations;
    }

    public void setHotelCancellations(boolean hotelCancellations) {
        this.hotelCancellations = hotelCancellations;
    }

    public boolean isHotelDepartures() {
        return hotelDepartures;
    }

    public void setHotelDepartures(boolean hotelDepartures) {
        this.hotelDepartures = hotelDepartures;
    }

    public boolean isHotelLRV() {
        return hotelLRV;
    }

    public void setHotelLRV(boolean hotelLRV) {
        this.hotelLRV = hotelLRV;
    }

    public boolean isHotelNoShow() {
        return hotelNoShow;
    }

    public void setHotelNoShow(boolean hotelNoShow) {
        this.hotelNoShow = hotelNoShow;
    }

    public boolean isHotelOccupancyForecast() {
        return hotelOccupancyForecast;
    }

    public void setHotelOccupancyForecast(boolean hotelOccupancyForecast) {
        this.hotelOccupancyForecast = hotelOccupancyForecast;
    }

    public boolean isHotelOutOfOrder() {
        return hotelOutOfOrder;
    }

    public void setHotelOutOfOrder(boolean hotelOutOfOrder) {
        this.hotelOutOfOrder = hotelOutOfOrder;
    }

    public boolean isHotelOverbooking() {
        return hotelOverbooking;
    }

    public void setHotelOverbooking(boolean hotelOverbooking) {
        this.hotelOverbooking = hotelOverbooking;
    }

    public boolean isHotelRevenue() {
        return hotelRevenue;
    }

    public void setHotelRevenue(boolean hotelRevenue) {
        this.hotelRevenue = hotelRevenue;
    }

    public boolean isHotelRevPAR() {
        return hotelRevPAR;
    }

    public void setHotelRevPAR(boolean hotelRevPAR) {
        this.hotelRevPAR = hotelRevPAR;
    }

    public boolean isHotelRoomsNotAvailableOutOfOrder() {
        return hotelRoomsNotAvailableOutOfOrder;
    }

    public void setHotelRoomsNotAvailableOutOfOrder(boolean hotelRoomsNotAvailableOutOfOrder) {
        this.hotelRoomsNotAvailableOutOfOrder = hotelRoomsNotAvailableOutOfOrder;
    }

    public boolean isHotelRoomsSold() {
        return hotelRoomsSold;
    }

    public void setHotelRoomsSold(boolean hotelRoomsSold) {
        this.hotelRoomsSold = hotelRoomsSold;
    }

    public boolean isHotelSpecialEvent() {
        return hotelSpecialEvent;
    }

    public void setHotelSpecialEvent(boolean hotelSpecialEvent) {
        this.hotelSpecialEvent = hotelSpecialEvent;
    }

    public boolean isHotelSystemUnconstrainedDemand() {
        return hotelSystemUnconstrainedDemand;
    }

    public void setHotelSystemUnconstrainedDemand(boolean hotelSystemUnconstrainedDemand) {
        this.hotelSystemUnconstrainedDemand = hotelSystemUnconstrainedDemand;
    }

    public boolean isHotelUserUnconstrainedDemand() {
        return hotelUserUnconstrainedDemand;
    }

    public void setHotelUserUnconstrainedDemand(boolean hotelUserUnconstrainedDemand) {
        this.hotelUserUnconstrainedDemand = hotelUserUnconstrainedDemand;
    }

    public boolean isHotelWash() {
        return hotelWash;
    }

    public void setHotelWash(boolean hotelWash) {
        this.hotelWash = hotelWash;
    }

    public boolean isHotelInventoryLimit() {
        return hotelInventoryLimit;
    }

    public void setHotelInventoryLimit(boolean hotelInventoryLimit) {
        this.hotelInventoryLimit = hotelInventoryLimit;
    }

    public boolean isRoomClass() {
        return roomClass;
    }

    public void setRoomClass(boolean roomClass) {
        this.roomClass = roomClass;
    }

    public boolean isRoomClassArrivals() {
        return roomClassArrivals;
    }

    public void setRoomClassArrivals(boolean roomClassArrivals) {
        this.roomClassArrivals = roomClassArrivals;
    }

    public boolean isRoomClassADR() {
        return roomClassADR;
    }

    public void setRoomClassADR(boolean roomClassADR) {
        this.roomClassADR = roomClassADR;
    }

    public boolean isRoomClassBAR() {
        return roomClassBAR;
    }

    public void setRoomClassBAR(boolean roomClassBAR) {
        this.roomClassBAR = roomClassBAR;
    }

    public boolean isRoomClassCancellations() {
        return roomClassCancellations;
    }

    public void setRoomClassCancellations(boolean roomClassCancellations) {
        this.roomClassCancellations = roomClassCancellations;
    }

    public boolean isRoomClassCapacity() {
        return roomClassCapacity;
    }

    public void setRoomClassCapacity(boolean roomClassCapacity) {
        this.roomClassCapacity = roomClassCapacity;
    }

    public boolean isRoomClassDepartures() {
        return roomClassDepartures;
    }

    public void setRoomClassDepartures(boolean roomClassDepartures) {
        this.roomClassDepartures = roomClassDepartures;
    }

    public boolean isRoomClassOccupancyForecast() {
        return roomClassOccupancyForecast;
    }

    public void setRoomClassOccupancyForecast(boolean roomClassOccupancyForecast) {
        this.roomClassOccupancyForecast = roomClassOccupancyForecast;
    }

    public boolean isRoomClassLRV() {
        return roomClassLRV;
    }

    public void setRoomClassLRV(boolean roomClassLRV) {
        this.roomClassLRV = roomClassLRV;
    }

    public boolean isRoomClassNoShow() {
        return roomClassNoShow;
    }

    public void setRoomClassNoShow(boolean roomClassNoShow) {
        this.roomClassNoShow = roomClassNoShow;
    }

    public boolean isRoomClassOutOfOrder() {
        return roomClassOutOfOrder;
    }

    public void setRoomClassOutOfOrder(boolean roomClassOutOfOrder) {
        this.roomClassOutOfOrder = roomClassOutOfOrder;
    }

    public boolean isRoomClassRevenue() {
        return roomClassRevenue;
    }

    public void setRoomClassRevenue(boolean roomClassRevenue) {
        this.roomClassRevenue = roomClassRevenue;
    }

    public boolean isRoomClassRevPAR() {
        return roomClassRevPAR;
    }

    public void setRoomClassRevPAR(boolean roomClassRevPAR) {
        this.roomClassRevPAR = roomClassRevPAR;
    }

    public boolean isRoomClassRoomsNotAvailableOutOfOrder() {
        return roomClassRoomsNotAvailableOutOfOrder;
    }

    public void setRoomClassRoomsNotAvailableOutOfOrder(boolean roomClassRoomsNotAvailableOutOfOrder) {
        this.roomClassRoomsNotAvailableOutOfOrder = roomClassRoomsNotAvailableOutOfOrder;
    }

    public boolean isRoomClassRoomsSold() {
        return roomClassRoomsSold;
    }

    public void setRoomClassRoomsSold(boolean roomClassRoomsSold) {
        this.roomClassRoomsSold = roomClassRoomsSold;
    }

    public boolean isRoomClassSystemUnconstrainedDemand() {
        return roomClassSystemUnconstrainedDemand;
    }

    public void setRoomClassSystemUnconstrainedDemand(boolean roomClassSystemUnconstrainedDemand) {
        this.roomClassSystemUnconstrainedDemand = roomClassSystemUnconstrainedDemand;
    }

    public boolean isRoomClassUserUnconstrainedDemand() {
        return roomClassUserUnconstrainedDemand;
    }

    public void setRoomClassUserUnconstrainedDemand(boolean roomClassUserUnconstrainedDemand) {
        this.roomClassUserUnconstrainedDemand = roomClassUserUnconstrainedDemand;
    }

    public boolean isRoomType() {
        return roomType;
    }

    public void setRoomType(boolean roomType) {
        this.roomType = roomType;
    }

    public boolean isRoomTypeADR() {
        return roomTypeADR;
    }

    public void setRoomTypeADR(boolean roomTypeADR) {
        this.roomTypeADR = roomTypeADR;
    }

    public boolean isRoomTypeArrivals() {
        return roomTypeArrivals;
    }

    public void setRoomTypeArrivals(boolean roomTypeArrivals) {
        this.roomTypeArrivals = roomTypeArrivals;
    }

    public boolean isRoomTypeBAR() {
        return roomTypeBAR;
    }

    public void setRoomTypeBAR(boolean roomTypeBAR) {
        this.roomTypeBAR = roomTypeBAR;
    }

    public boolean isRoomTypeCancellations() {
        return roomTypeCancellations;
    }

    public void setRoomTypeCancellations(boolean roomTypeCancellations) {
        this.roomTypeCancellations = roomTypeCancellations;
    }

    public boolean isRoomTypeCapacity() {
        return roomTypeCapacity;
    }

    public void setRoomTypeCapacity(boolean roomTypeCapacity) {
        this.roomTypeCapacity = roomTypeCapacity;
    }

    public boolean isRoomTypeDepartures() {
        return roomTypeDepartures;
    }

    public void setRoomTypeDepartures(boolean roomTypeDepartures) {
        this.roomTypeDepartures = roomTypeDepartures;
    }

    public boolean isRoomTypeNoShow() {
        return roomTypeNoShow;
    }

    public void setRoomTypeNoShow(boolean roomTypeNoShow) {
        this.roomTypeNoShow = roomTypeNoShow;
    }

    public boolean isRoomTypeOutOfOrder() {
        return roomTypeOutOfOrder;
    }

    public void setRoomTypeOutOfOrder(boolean roomTypeOutOfOrder) {
        this.roomTypeOutOfOrder = roomTypeOutOfOrder;
    }

    public boolean isRoomTypeOverbooking() {
        return roomTypeOverbooking;
    }

    public void setRoomTypeOverbooking(boolean roomTypeOverbooking) {
        this.roomTypeOverbooking = roomTypeOverbooking;
    }

    public boolean isRoomTypeRevenue() {
        return roomTypeRevenue;
    }

    public void setRoomTypeRevenue(boolean roomTypeRevenue) {
        this.roomTypeRevenue = roomTypeRevenue;
    }

    public boolean isRoomTypeRevPAR() {
        return roomTypeRevPAR;
    }

    public void setRoomTypeRevPAR(boolean roomTypeRevPAR) {
        this.roomTypeRevPAR = roomTypeRevPAR;
    }

    public boolean isRoomTypeRoomsNotAvailableOutOfOrder() {
        return roomTypeRoomsNotAvailableOutOfOrder;
    }

    public void setRoomTypeRoomsNotAvailableOutOfOrder(boolean roomTypeRoomsNotAvailableOutOfOrder) {
        this.roomTypeRoomsNotAvailableOutOfOrder = roomTypeRoomsNotAvailableOutOfOrder;
    }

    public boolean isRoomTypeRoomsSold() {
        return roomTypeRoomsSold;
    }

    public void setRoomTypeRoomsSold(boolean roomTypeRoomsSold) {
        this.roomTypeRoomsSold = roomTypeRoomsSold;
    }

    public boolean isMarketSegment() {
        return marketSegment;
    }

    public void setMarketSegment(boolean marketSegment) {
        this.marketSegment = marketSegment;
    }

    public boolean isMarketSegmentArrivals() {
        return marketSegmentArrivals;
    }

    public void setMarketSegmentArrivals(boolean marketSegmentArrivals) {
        this.marketSegmentArrivals = marketSegmentArrivals;
    }

    public boolean isMarketSegmentADR() {
        return marketSegmentADR;
    }

    public void setMarketSegmentADR(boolean marketSegmentADR) {
        this.marketSegmentADR = marketSegmentADR;
    }

    public boolean isMarketSegmentCancellations() {
        return marketSegmentCancellations;
    }

    public void setMarketSegmentCancellations(boolean marketSegmentCancellations) {
        this.marketSegmentCancellations = marketSegmentCancellations;
    }

    public boolean isMarketSegmentDepartures() {
        return marketSegmentDepartures;
    }

    public void setMarketSegmentDepartures(boolean marketSegmentDepartures) {
        this.marketSegmentDepartures = marketSegmentDepartures;
    }

    public boolean isMarketSegmentNoShow() {
        return marketSegmentNoShow;
    }

    public void setMarketSegmentNoShow(boolean marketSegmentNoShow) {
        this.marketSegmentNoShow = marketSegmentNoShow;
    }

    public boolean isMarketSegmentOccupancyForecast() {
        return marketSegmentOccupancyForecast;
    }

    public void setMarketSegmentOccupancyForecast(boolean marketSegmentOccupancyForecast) {
        this.marketSegmentOccupancyForecast = marketSegmentOccupancyForecast;
    }

    public boolean isMarketSegmentRevenue() {
        return marketSegmentRevenue;
    }

    public void setMarketSegmentRevenue(boolean marketSegmentRevenue) {
        this.marketSegmentRevenue = marketSegmentRevenue;
    }

    public boolean isMarketSegmentRoomsSold() {
        return marketSegmentRoomsSold;
    }

    public void setMarketSegmentRoomsSold(boolean marketSegmentRoomsSold) {
        this.marketSegmentRoomsSold = marketSegmentRoomsSold;
    }

    public boolean isForecastGroup() {
        return forecastGroup;
    }

    public void setForecastGroup(boolean forecastGroup) {
        this.forecastGroup = forecastGroup;
    }

    public boolean isForecastGroupADR() {
        return forecastGroupADR;
    }

    public void setForecastGroupADR(boolean forecastGroupADR) {
        this.forecastGroupADR = forecastGroupADR;
    }

    public boolean isForecastGroupArrivals() {
        return forecastGroupArrivals;
    }

    public void setForecastGroupArrivals(boolean forecastGroupArrivals) {
        this.forecastGroupArrivals = forecastGroupArrivals;
    }

    public boolean isForecastGroupCancellations() {
        return forecastGroupCancellations;
    }

    public void setForecastGroupCancellations(boolean forecastGroupCancellations) {
        this.forecastGroupCancellations = forecastGroupCancellations;
    }

    public boolean isForecastGroupDepartures() {
        return forecastGroupDepartures;
    }

    public void setForecastGroupDepartures(boolean forecastGroupDepartures) {
        this.forecastGroupDepartures = forecastGroupDepartures;
    }

    public boolean isForecastGroupNoShow() {
        return forecastGroupNoShow;
    }

    public void setForecastGroupNoShow(boolean forecastGroupNoShow) {
        this.forecastGroupNoShow = forecastGroupNoShow;
    }

    public boolean isForecastGroupOccupancyForecast() {
        return forecastGroupOccupancyForecast;
    }

    public void setForecastGroupOccupancyForecast(boolean forecastGroupOccupancyForecast) {
        this.forecastGroupOccupancyForecast = forecastGroupOccupancyForecast;
    }

    public boolean isForecastGroupRevenue() {
        return forecastGroupRevenue;
    }

    public void setForecastGroupRevenue(boolean forecastGroupRevenue) {
        this.forecastGroupRevenue = forecastGroupRevenue;
    }

    public boolean isForecastGroupRoomsSold() {
        return forecastGroupRoomsSold;
    }

    public void setForecastGroupRoomsSold(boolean forecastGroupRoomsSold) {
        this.forecastGroupRoomsSold = forecastGroupRoomsSold;
    }

    public boolean isForecastGroupSystemGroupWashPerFG() {
        return forecastGroupSystemGroupWashPerFG;
    }

    public void setForecastGroupSystemGroupWashPerFG(boolean forecastGroupSystemGroupWashPerFG) {
        this.forecastGroupSystemGroupWashPerFG = forecastGroupSystemGroupWashPerFG;
    }

    public boolean isForecastGroupSystemUnconstrainedDemand() {
        return forecastGroupSystemUnconstrainedDemand;
    }

    public void setForecastGroupSystemUnconstrainedDemand(boolean forecastGroupSystemUnconstrainedDemand) {
        this.forecastGroupSystemUnconstrainedDemand = forecastGroupSystemUnconstrainedDemand;
    }

    public boolean isForecastGroupUserUnconstrainedDemand() {
        return forecastGroupUserUnconstrainedDemand;
    }

    public void setForecastGroupUserUnconstrainedDemand(boolean forecastGroupUserUnconstrainedDemand) {
        this.forecastGroupUserUnconstrainedDemand = forecastGroupUserUnconstrainedDemand;
    }

    public boolean isBusinessView() {
        return businessView;
    }

    public void setBusinessView(boolean businessView) {
        this.businessView = businessView;
    }

    public boolean isBusinessViewADR() {
        return businessViewADR;
    }

    public void setBusinessViewADR(boolean businessViewADR) {
        this.businessViewADR = businessViewADR;
    }

    public boolean isBusinessViewArrivals() {
        return businessViewArrivals;
    }

    public void setBusinessViewArrivals(boolean businessViewArrivals) {
        this.businessViewArrivals = businessViewArrivals;
    }

    public boolean isBusinessViewCancellations() {
        return businessViewCancellations;
    }

    public void setBusinessViewCancellations(boolean businessViewCancellations) {
        this.businessViewCancellations = businessViewCancellations;
    }

    public boolean isBusinessViewDepartures() {
        return businessViewDepartures;
    }

    public void setBusinessViewDepartures(boolean businessViewDepartures) {
        this.businessViewDepartures = businessViewDepartures;
    }

    public boolean isBusinessViewNoShow() {
        return businessViewNoShow;
    }

    public void setBusinessViewNoShow(boolean businessViewNoShow) {
        this.businessViewNoShow = businessViewNoShow;
    }

    public boolean isBusinessViewOccupancyForecast() {
        return businessViewOccupancyForecast;
    }

    public void setBusinessViewOccupancyForecast(boolean businessViewOccupancyForecast) {
        this.businessViewOccupancyForecast = businessViewOccupancyForecast;
    }

    public boolean isBusinessViewRevenue() {
        return businessViewRevenue;
    }

    public void setBusinessViewRevenue(boolean businessViewRevenue) {
        this.businessViewRevenue = businessViewRevenue;
    }

    public boolean isBusinessViewRoomsSold() {
        return businessViewRoomsSold;
    }

    public void setBusinessViewRoomsSold(boolean businessViewRoomsSold) {
        this.businessViewRoomsSold = businessViewRoomsSold;
    }

    public boolean isHotelProfit() {
        return hotelProfit;
    }

    public void setHotelProfit(boolean hotelProfit) {
        this.hotelProfit = hotelProfit;
    }

    public boolean isHotelProfitSTLY() {
        return hotelProfitSTLY;
    }

    public void setHotelProfitSTLY(boolean hotelProfitSTLY) {
        this.hotelProfitSTLY = hotelProfitSTLY;
    }

    public boolean isHotelProPAR() {
        return hotelProPAR;
    }

    public void setHotelProPAR(boolean hotelProPAR) {
        this.hotelProPAR = hotelProPAR;
    }

    public boolean isHotelProPARSTLY() {
        return hotelProPARSTLY;
    }

    public void setHotelProPARSTLY(boolean hotelProPARSTLY) {
        this.hotelProPARSTLY = hotelProPARSTLY;
    }

    public boolean isHotelProPOR() {
        return hotelProPOR;
    }

    public void setHotelProPOR(boolean hotelProPOR) {
        this.hotelProPOR = hotelProPOR;
    }

    public boolean isHotelProPORSTLY() {
        return hotelProPORSTLY;
    }

    public void setHotelProPORSTLY(boolean hotelProPORSTLY) {
        this.hotelProPORSTLY = hotelProPORSTLY;
    }

    public boolean isRoomClassProfit() {
        return roomClassProfit;
    }

    public void setRoomClassProfit(boolean roomClassProfit) {
        this.roomClassProfit = roomClassProfit;
    }

    public boolean isRoomClassProfitSTLY() {
        return roomClassProfitSTLY;
    }

    public void setRoomClassProfitSTLY(boolean roomClassProfitSTLY) {
        this.roomClassProfitSTLY = roomClassProfitSTLY;
    }

    public boolean isRoomClassProPAR() {
        return roomClassProPAR;
    }

    public void setRoomClassProPAR(boolean roomClassProPAR) {
        this.roomClassProPAR = roomClassProPAR;
    }

    public boolean isRoomClassProPARSTLY() {
        return roomClassProPARSTLY;
    }

    public void setRoomClassProPARSTLY(boolean roomClassProPARSTLY) {
        this.roomClassProPARSTLY = roomClassProPARSTLY;
    }

    public boolean isRoomClassProPOR() {
        return roomClassProPOR;
    }

    public void setRoomClassProPOR(boolean roomClassProPOR) {
        this.roomClassProPOR = roomClassProPOR;
    }

    public boolean isRoomClassProPORSTLY() {
        return roomClassProPORSTLY;
    }

    public void setRoomClassProPORSTLY(boolean roomClassProPORSTLY) {
        this.roomClassProPORSTLY = roomClassProPORSTLY;
    }

    public boolean isForecastGroupProfit() {
        return forecastGroupProfit;
    }

    public void setForecastGroupProfit(boolean forecastGroupProfit) {
        this.forecastGroupProfit = forecastGroupProfit;
    }

    public boolean isForecastGroupProfitSTLY() {
        return forecastGroupProfitSTLY;
    }

    public void setForecastGroupProfitSTLY(boolean forecastGroupProfitSTLY) {
        this.forecastGroupProfitSTLY = forecastGroupProfitSTLY;
    }

    public boolean isForecastGroupProPOR() {
        return forecastGroupProPOR;
    }

    public void setForecastGroupProPOR(boolean forecastGroupProPOR) {
        this.forecastGroupProPOR = forecastGroupProPOR;
    }

    public boolean isForecastGroupProPORSTLY() {
        return forecastGroupProPORSTLY;
    }

    public void setForecastGroupProPORSTLY(boolean forecastGroupProPORSTLY) {
        this.forecastGroupProPORSTLY = forecastGroupProPORSTLY;
    }

    public boolean isLos() {
        return los;
    }

    public void setLos(boolean los) {
        this.los = los;
    }

    public int getMaxLOS() {
        return maxLOS;
    }

    public void setMaxLOS(int maxLOS) {
        this.maxLOS = maxLOS;
    }

    public boolean isCompetitor() {
        return competitor;
    }

    public void setCompetitor(boolean competitor) {
        this.competitor = competitor;
    }

    public int getComp1() {
        return comp1;
    }

    public void setComp1(int comp1) {
        this.comp1 = comp1;
    }

    public int getComp2() {
        return comp2;
    }

    public void setComp2(int comp2) {
        this.comp2 = comp2;
    }

    public int getComp3() {
        return comp3;
    }

    public void setComp3(int comp3) {
        this.comp3 = comp3;
    }

    public int getComp4() {
        return comp4;
    }

    public void setComp4(int comp4) {
        this.comp4 = comp4;
    }

    public int getComp5() {
        return comp5;
    }

    public void setComp5(int comp5) {
        this.comp5 = comp5;
    }

    public Integer getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(Integer propertyId) {
        this.propertyId = propertyId;
    }

    public String getJndiName() {
        return jndiName;
    }

    public void setJndiName(String jndiName) {
        this.jndiName = jndiName;
    }

    @FieldDefinition(name = "roomtype.table.header.startdate", priority = 201)
    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    @FieldDefinition(name = "roomtype.table.header.enddate", priority = 202)
    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public int getIsRollingDate() {
        return isRollingDate;
    }

    public void setIsRollingDate(int isRollingDate) {
        this.isRollingDate = isRollingDate;
    }

    public boolean isShowLastYearData() {
        return showLastYearData;
    }

    public void setShowLastYearData(boolean showLastYearData) {
        this.showLastYearData = showLastYearData;
    }

    public boolean isShowLast2YearsData() {
        return showLast2YearsData;
    }

    public void setShowLast2YearsData(boolean showLast2YearsData) {
        this.showLast2YearsData = showLast2YearsData;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public boolean isSheetPerCriteria() {
        return sheetPerCriteria;
    }

    public void setSheetPerCriteria(boolean sheetPerCriteria) {
        this.sheetPerCriteria = sheetPerCriteria;
    }

    @Override
    public Language getLanguage() {
        return language;
    }

    public void setLanguage(Language language) {
        this.language = language;
    }

    public String getRollingStartDate() {
        return rollingStartDate;
    }

    public void setRollingStartDate(String rollingStartDate) {
        this.rollingStartDate = rollingStartDate;
    }

    public String getRollingEndDate() {
        return rollingEndDate;
    }

    public void setRollingEndDate(String rollingEndDate) {
        this.rollingEndDate = rollingEndDate;
    }

    public boolean isContinuousPricingEnabled() {
        return continuousPricingEnabled;
    }

    public void setContinuousPricingEnabled(boolean continuousPricingEnabled) {
        this.continuousPricingEnabled = continuousPricingEnabled;
    }

    public boolean isIgnorePagination() {
        return ignorePagination;
    }

    public void setIgnorePagination(boolean ignorePagination) {
        this.ignorePagination = ignorePagination;
    }

    public boolean isHotelRevenueSTLY() {
        return hotelRevenueSTLY;
    }

    public void setHotelRevenueSTLY(boolean hotelRevenueSTLY) {
        this.hotelRevenueSTLY = hotelRevenueSTLY;
    }

    public boolean isHotelRoomsSoldSTLY() {
        return hotelRoomsSoldSTLY;
    }

    public void setHotelRoomsSoldSTLY(boolean hotelRoomsSoldSTLY) {
        this.hotelRoomsSoldSTLY = hotelRoomsSoldSTLY;
    }

    public boolean isRoomClassRevenueSTLY() {
        return roomClassRevenueSTLY;
    }

    public void setRoomClassRevenueSTLY(boolean roomClassRevenueSTLY) {
        this.roomClassRevenueSTLY = roomClassRevenueSTLY;
    }

    public boolean isRoomClassRoomsSoldSTLY() {
        return roomClassRoomsSoldSTLY;
    }

    public void setRoomClassRoomsSoldSTLY(boolean roomClassRoomsSoldSTLY) {
        this.roomClassRoomsSoldSTLY = roomClassRoomsSoldSTLY;
    }

    public boolean isRoomTypeRevenueSTLY() {
        return roomTypeRevenueSTLY;
    }

    public void setRoomTypeRevenueSTLY(boolean roomTypeRevenueSTLY) {
        this.roomTypeRevenueSTLY = roomTypeRevenueSTLY;
    }

    public boolean isRoomTypeRoomsSoldSTLY() {
        return roomTypeRoomsSoldSTLY;
    }

    public void setRoomTypeRoomsSoldSTLY(boolean roomTypeRoomsSoldSTLY) {
        this.roomTypeRoomsSoldSTLY = roomTypeRoomsSoldSTLY;
    }

    public boolean isMarketSegmentRevenueSTLY() {
        return marketSegmentRevenueSTLY;
    }

    public void setMarketSegmentRevenueSTLY(boolean marketSegmentRevenueSTLY) {
        this.marketSegmentRevenueSTLY = marketSegmentRevenueSTLY;
    }

    public boolean isMarketSegmentRoomsSoldSTLY() {
        return marketSegmentRoomsSoldSTLY;
    }

    public void setMarketSegmentRoomsSoldSTLY(boolean marketSegmentRoomsSoldSTLY) {
        this.marketSegmentRoomsSoldSTLY = marketSegmentRoomsSoldSTLY;
    }

    public boolean isForecastGroupRevenueSTLY() {
        return forecastGroupRevenueSTLY;
    }

    public void setForecastGroupRevenueSTLY(boolean forecastGroupRevenueSTLY) {
        this.forecastGroupRevenueSTLY = forecastGroupRevenueSTLY;
    }

    public boolean isForecastGroupRoomsSoldSTLY() {
        return forecastGroupRoomsSoldSTLY;
    }

    public void setForecastGroupRoomsSoldSTLY(boolean forecastGroupRoomsSoldSTLY) {
        this.forecastGroupRoomsSoldSTLY = forecastGroupRoomsSoldSTLY;
    }

    public boolean isBusinessViewRevenueSTLY() {
        return businessViewRevenueSTLY;
    }

    public void setBusinessViewRevenueSTLY(boolean businessViewRevenueSTLY) {
        this.businessViewRevenueSTLY = businessViewRevenueSTLY;
    }

    public boolean isBusinessViewRoomsSoldSTLY() {
        return businessViewRoomsSoldSTLY;
    }

    public void setBusinessViewRoomsSoldSTLY(boolean businessViewRoomsSoldSTLY) {
        this.businessViewRoomsSoldSTLY = businessViewRoomsSoldSTLY;
    }

    public boolean showComp1Data() {
        return isCompetitor() && getComp1() != -1;
    }

    public String getCompetitor1Name(List<DataExtractionHotel> dataList) {
        if (CollectionUtils.isEmpty(dataList)) return "";
        return dataList.get(0).getComp1Name();
    }

    public boolean showComp2Data() {
        return isCompetitor() && getComp2() != -1;
    }

    public String getCompetitor2Name(List<DataExtractionHotel> dataList) {
        if (CollectionUtils.isEmpty(dataList)) return "";
        return dataList.get(0).getComp2Name();
    }

    public boolean showComp3Data() {
        return isCompetitor() && getComp3() != -1;
    }

    public String getCompetitor3Name(List<DataExtractionHotel> dataList) {
        if (CollectionUtils.isEmpty(dataList)) return "";
        return dataList.get(0).getComp3Name();
    }

    public boolean showComp4Data() {
        return isCompetitor() && getComp4() != -1;
    }

    public String getCompetitor4Name(List<DataExtractionHotel> dataList) {
        if (CollectionUtils.isEmpty(dataList)) return "";
        return dataList.get(0).getComp4Name();
    }

    public boolean showComp5Data() {
        return isCompetitor() && getComp5() != -1;
    }

    public String getCompetitor5Name(List<DataExtractionHotel> dataList) {
        if (CollectionUtils.isEmpty(dataList)) return "";
        return dataList.get(0).getComp5Name();
    }

    public int getUsePhysicalCapacity() {
        return usePhysicalCapacity;
    }

    public void setUsePhysicalCapacity(int usePhysicalCapacity) {
        this.usePhysicalCapacity = usePhysicalCapacity;
    }

    public boolean isHotelRevenueST2Y() {
        return hotelRevenueST2Y;
    }

    public void setHotelRevenueST2Y(boolean hotelRevenueST2Y) {
        this.hotelRevenueST2Y = hotelRevenueST2Y;
    }

    public boolean isHotelRoomsSoldST2Y() {
        return hotelRoomsSoldST2Y;
    }

    public void setHotelRoomsSoldST2Y(boolean hotelRoomsSoldST2Y) {
        this.hotelRoomsSoldST2Y = hotelRoomsSoldST2Y;
    }

    public boolean isRoomClassRevenueST2Y() {
        return roomClassRevenueST2Y;
    }

    public void setRoomClassRevenueST2Y(boolean roomClassRevenueST2Y) {
        this.roomClassRevenueST2Y = roomClassRevenueST2Y;
    }

    public boolean isRoomClassRoomsSoldST2Y() {
        return roomClassRoomsSoldST2Y;
    }

    public void setRoomClassRoomsSoldST2Y(boolean roomClassRoomsSoldST2Y) {
        this.roomClassRoomsSoldST2Y = roomClassRoomsSoldST2Y;
    }

    public boolean isRoomTypeRevenueST2Y() {
        return roomTypeRevenueST2Y;
    }

    public void setRoomTypeRevenueST2Y(boolean roomTypeRevenueST2Y) {
        this.roomTypeRevenueST2Y = roomTypeRevenueST2Y;
    }

    public boolean isRoomTypeRoomsSoldST2Y() {
        return roomTypeRoomsSoldST2Y;
    }

    public void setRoomTypeRoomsSoldST2Y(boolean roomTypeRoomsSoldST2Y) {
        this.roomTypeRoomsSoldST2Y = roomTypeRoomsSoldST2Y;
    }

    public boolean isMarketSegmentRevenueST2Y() {
        return marketSegmentRevenueST2Y;
    }

    public void setMarketSegmentRevenueST2Y(boolean marketSegmentRevenueST2Y) {
        this.marketSegmentRevenueST2Y = marketSegmentRevenueST2Y;
    }

    public boolean isMarketSegmentRoomsSoldST2Y() {
        return marketSegmentRoomsSoldST2Y;
    }

    public void setMarketSegmentRoomsSoldST2Y(boolean marketSegmentRoomsSoldST2Y) {
        this.marketSegmentRoomsSoldST2Y = marketSegmentRoomsSoldST2Y;
    }

    public boolean isForecastGroupRevenueST2Y() {
        return forecastGroupRevenueST2Y;
    }

    public void setForecastGroupRevenueST2Y(boolean forecastGroupRevenueST2Y) {
        this.forecastGroupRevenueST2Y = forecastGroupRevenueST2Y;
    }

    public boolean isForecastGroupRoomsSoldST2Y() {
        return forecastGroupRoomsSoldST2Y;
    }

    public void setForecastGroupRoomsSoldST2Y(boolean forecastGroupRoomsSoldST2Y) {
        this.forecastGroupRoomsSoldST2Y = forecastGroupRoomsSoldST2Y;
    }

    public boolean isBusinessViewRevenueST2Y() {
        return businessViewRevenueST2Y;
    }

    public void setBusinessViewRevenueST2Y(boolean businessViewRevenueST2Y) {
        this.businessViewRevenueST2Y = businessViewRevenueST2Y;
    }

    public boolean isBusinessViewRoomsSoldST2Y() {
        return businessViewRoomsSoldST2Y;
    }

    public void setBusinessViewRoomsSoldST2Y(boolean businessViewRoomsSoldST2Y) {
        this.businessViewRoomsSoldST2Y = businessViewRoomsSoldST2Y;
    }

    public boolean isHotelRevenueST19() {
        return hotelRevenueST19;
    }

    public void setHotelRevenueST19(boolean hotelRevenueST19) {
        this.hotelRevenueST19 = hotelRevenueST19;
    }

    public boolean isHotelRoomsSoldST19() {
        return hotelRoomsSoldST19;
    }

    public void setHotelRoomsSoldST19(boolean hotelRoomsSoldST19) {
        this.hotelRoomsSoldST19 = hotelRoomsSoldST19;
    }

    public boolean isShowYear2019Data() {
        return showYear2019Data;
    }

    public void setShowYear2019Data(boolean showYear2019Data) {
        this.showYear2019Data = showYear2019Data;
    }

    public boolean isRoomTypeRevenueST19() {
        return roomTypeRevenueST19;
    }

    public void setRoomTypeRevenueST19(boolean roomTypeRevenueST19) {
        this.roomTypeRevenueST19 = roomTypeRevenueST19;
    }

    public boolean isRoomTypeRoomsSoldST19() {
        return roomTypeRoomsSoldST19;
    }

    public void setRoomTypeRoomsSoldST19(boolean roomTypeRoomsSoldST19) {
        this.roomTypeRoomsSoldST19 = roomTypeRoomsSoldST19;
    }

    public boolean isMarketSegmentRevenueST19() {
        return marketSegmentRevenueST19;
    }

    public void setMarketSegmentRevenueST19(boolean marketSegmentRevenueST19) {
        this.marketSegmentRevenueST19 = marketSegmentRevenueST19;
    }

    public boolean isMarketSegmentRoomsSoldST19() {
        return marketSegmentRoomsSoldST19;
    }

    public void setMarketSegmentRoomsSoldST19(boolean marketSegmentRoomsSoldST19) {
        this.marketSegmentRoomsSoldST19 = marketSegmentRoomsSoldST19;
    }

    public boolean isRoomClassRevenueST19() {
        return roomClassRevenueST19;
    }

    public void setRoomClassRevenueST19(boolean roomClassRevenueST19) {
        this.roomClassRevenueST19 = roomClassRevenueST19;
    }

    public boolean isRoomClassRoomsSoldST19() {
        return roomClassRoomsSoldST19;
    }

    public void setRoomClassRoomsSoldST19(boolean roomClassRoomsSoldST19) {
        this.roomClassRoomsSoldST19 = roomClassRoomsSoldST19;
    }

    public boolean isForecastGroupRevenueST19() {
        return forecastGroupRevenueST19;
    }

    public void setForecastGroupRevenueST19(boolean forecastGroupRevenueST19) {
        this.forecastGroupRevenueST19 = forecastGroupRevenueST19;
    }

    public boolean isForecastGroupRoomsSoldST19() {
        return forecastGroupRoomsSoldST19;
    }

    public void setForecastGroupRoomsSoldST19(boolean forecastGroupRoomsSoldST19) {
        this.forecastGroupRoomsSoldST19 = forecastGroupRoomsSoldST19;
    }

    public boolean isBusinessViewRevenueST19() {
        return businessViewRevenueST19;
    }

    public void setBusinessViewRevenueST19(boolean businessViewRevenueST19) {
        this.businessViewRevenueST19 = businessViewRevenueST19;
    }

    public boolean isBusinessViewRoomsSoldST19() {
        return businessViewRoomsSoldST19;
    }

    public void setBusinessViewRoomsSoldST19(boolean businessViewRoomsSoldST19) {
        this.businessViewRoomsSoldST19 = businessViewRoomsSoldST19;
    }
}
