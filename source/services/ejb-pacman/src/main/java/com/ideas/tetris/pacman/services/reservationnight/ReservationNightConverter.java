package com.ideas.tetris.pacman.services.reservationnight;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.individualtransactions.RoomStayRevenue;
import com.ideas.tetris.pacman.services.marketsegment.repository.MarketSegmentRepository;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentService;
import com.ideas.tetris.pacman.services.marketsegment.service.HiltonIppMktSegService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.roomtyperecoding.services.RoomTypeRecodingService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.ngi.NGIConvertUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.TreeSet;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.INCLUDE_NO_SHOW_CANCELLATION_REVENUE;
import static com.ideas.tetris.pacman.services.reservationnight.ReservationNightConstants.*;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.removeTimeFromDate;

/**
 * Convert all reservation in incoming Map from NGI to Reservation Nights
 */
@SuppressWarnings({"squid:S3776", "unchecked"})
@Slf4j
@Component
@Transactional
public class ReservationNightConverter {

    private static final String DASH = "-";
    public static final String DEF_FUT = "DEF_FUT";
    @Autowired
    protected PropertyService propertyService;
    @Autowired
    protected DateService dateService;
    @Autowired
    protected PacmanConfigParamsService configParamsService;
    @Autowired
    protected AccommodationService accommodationService;
    @Autowired
    protected RoomTypeRecodingService roomTypeRecodingService;
    @Autowired
    protected HiltonIppMktSegService hiltonIppMktSegService;
    @TenantCrudServiceBean.Qualifier
    @Autowired
    @Qualifier("tenantCrudServiceBean")
    protected CrudService tenantCrudService;
    @Autowired
	private MarketSegmentRepository marketSegmentRepository;
    @Autowired
	private MarketSegmentResolutionService marketSegmentResolutionService;
    @Autowired
    private AnalyticalMarketSegmentService amsService;

    /**
     * Convert all reservation in incoming Map to Reservation Night
     *
     * @param reservations   inbound reservations from NGI
     * @param fileMetadataId inbound fileMetadataId
     * @param pseudoRooms    list of Pseudo Room ids
     * @return list of reservation Nights
     */
    public List<ReservationNight> convert(
            List<? extends Map<String, Object>> reservations,
            Integer fileMetadataId,
            List<String> pseudoRooms) {

        if (reservations == null) {
            return new ArrayList<>();
        }

        Map<String, TreeMap<Date, String>> roomTypeRecodingMappingsForRename = getRoomTypeRecordingMapping();

        AMSResolution amsResolution = marketSegmentResolutionService.getAMSResolution();
        ExtendedStayRateLevelResolution extendedStayRateLevelResolution = marketSegmentResolutionService.getExtendedStayRateLevelResolution();

        ReservationNightHiltonIppConverterHelper hiltonIppTxnConverterHelper = getHiltonIppTxnConverterHelper(reservations);
        return reservations
                .stream()
                .map(reservation -> convertRoomStays(reservation,
                        new ReservationNightConverterHelper(fileMetadataId,
                                roomTypeRecodingMappingsForRename, true, pseudoRooms, amsResolution,
                                extendedStayRateLevelResolution, hiltonIppTxnConverterHelper)))
                .peek((ignore) -> log.debug(" Expanded Combined to Reservation Nights"))
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    ReservationNightHiltonIppConverterHelper getHiltonIppTxnConverterHelper(List<? extends Map<String, Object>> reservations) {
        if (amsService.shouldProcessForIpp() && !amsService.isPropertyVirtual(PacmanWorkContextHelper.getPropertyId())) {
            List<String> transactionIds = reservations.stream().map(entry -> entry.get(RESERVATION_ID).toString()).collect(Collectors.toList());
            List<Product> independentProducts = tenantCrudService.findByNamedQuery(Product.GET_ACTIVE_INDEPENDENT_PRODUCTS);
            final TreeMap<Integer, String> minLosToIpProductMapping = independentProducts
                    .stream().collect(Collectors.toMap(Product::getMinLOS, Product::getName,
                            (oldProduct, newProduct) -> newProduct, TreeMap::new));
            final List<String> validMsForIpp = hiltonIppMktSegService.getAllOriginalMktSegCodes();
            log.info("Invalid Market Codes for Hilton IPP : {}", validMsForIpp);

            Map<Integer, String> validMsIdsForIpp = hiltonIppMktSegService.getAllSplitMktSegIdsToCodes();
            java.time.LocalDate caughtUpDate = dateService.getCaughtUpJavaLocalDate().plusDays(1);
            List<ReservationNight> dbReservationsList = tenantCrudService.findByNamedQuery(ReservationNight.GET_LATEST_INHOUSE_CHECKOUT_TRXNS_FOR_IPP,
                    QueryParameter.with("txnIds", transactionIds)
                            .and("msIds", validMsIdsForIpp.keySet())
                            .and("caughtUpDate", caughtUpDate).parameters());
            Map<String, ReservationNight> dbReservations = dbReservationsList
                    .stream().map(res -> res.setAnalyticalMarketSegmentCode(validMsIdsForIpp.get(res.getMarketSegId())))
                    .collect(Collectors.toMap(ReservationNight::getReservationIdentifier, Function.identity()));

            return new ReservationNightHiltonIppConverterHelper(true, minLosToIpProductMapping,
                    validMsForIpp, dbReservations, caughtUpDate);
        }
        return new ReservationNightHiltonIppConverterHelper(false, new TreeMap<>(), new ArrayList<>(), new HashMap<>(), null);
    }

    /**
     * Converts all reservation in incoming Map to Reservation Night. The method uses existing
     * reservation conversion functionality as a mere transformation of input data. It DOES NOT
     * expect any DML operations to be carried out as part of this conversion. For instance,
     * creation of new accom-types and mkt-segs. (that's what implied by the slang WYGIWYG=What u
     * give is what u get).
     */
    public List<ReservationNight> convertRoomStaysWYGIWYGStyle(Map<String, Object> reservation) {

        return convertRoomStays(reservation,
                new ReservationNightConverterHelper(null, Collections.emptyMap(),
                        false, Collections.emptyList(), new AMSResolution(),
                        new ExtendedStayRateLevelResolution(List.of()), getHiltonIppTxnConverterHelper(new ArrayList<>())));
    }

    /**
     * Convert each room stay in a reservation into reservation nights
     *
     * @param reservation                     reservation to convert
     * @param reservationNightConverterHelper converstion helper
     * @return list of reservation Nights
     */
    private List<ReservationNight> convertRoomStays(
            Map<String, Object> reservation,
            ReservationNightConverterHelper reservationNightConverterHelper) {

        List<Map<String, Object>> roomStays = (List<Map<String, Object>>) reservation.get(
                ROOM_STAYS);
        Map<Date, RoomStayRevenue> stayRevenues = getStayRevenuePerOccupancy(
                (Map<String, Object>) reservation.get(STAY_REVENUE));

        if (CollectionUtils.isEmpty(roomStays)) {
            return new ArrayList<>();
        }
        log.debug("Conversion of Room Stay to Reservations started");

        TreeSet<ReservationNight> reservationNightsFromRoomStays = roomStays
                .stream()
                .map(roomStay -> createReservationNight(reservation,
                        roomStay, stayRevenues, reservationNightConverterHelper))
                .collect(Collectors.toCollection(() -> new TreeSet<>(
                        Comparator.comparing(ReservationNight::getArrivalDate))));

        log.debug("Converted Room Stays to Reservation Nights");

        boolean includePseudos = configParamsService.getBooleanParameterValue(PreProductionConfigParamName.INCLUDE_PSEUDO_IN_REVENUE);
        TreeSet<ReservationNight> filtered = filterPartialPseudoRooms(
                reservationNightsFromRoomStays,
                includePseudos ? Collections.emptyList() : reservationNightConverterHelper.getPseudoRooms());
        log.debug("Filtered Pseudo Rooms, assigning accom_type_id if not already done");
        setAccomTypeIfNull(filtered, reservationNightConverterHelper.getPseudoRooms());
        Map<Date, String> rateCodesByOccupancyDate = new HashMap<>();
        List<ReservationNight> prototypes = getRoomStayPrototypes(filtered,
                rateCodesByOccupancyDate);
        log.debug("Combined Room Stays");
        final List<ReservationNight> reservationNightsByOccupancyDate = getReservationNightsByOccupancyDate(
                prototypes, rateCodesByOccupancyDate);
        reservationNightsByOccupancyDate.sort(
                Comparator.comparing(ReservationNight::getOccupancyDate));
        return reservationNightsByOccupancyDate;
    }

    private Map<Date, RoomStayRevenue> getStayRevenuePerOccupancy(
            final Map<String, Object> stayRevenueMap) {
        if (MapUtils.isEmpty(stayRevenueMap)) {
            return Collections.EMPTY_MAP;
        }
        List<Map<String, Object>> dailyRevenues = (List<Map<String, Object>>) stayRevenueMap.get(
                DAILY);
        if (CollectionUtils.isEmpty(dailyRevenues)) {
            return Collections.EMPTY_MAP;
        }
        Function<Map<String, Object>, Date> getTransactionDate = m -> NGIConvertUtils.convert(
                m.get(TRANSACTION_DT), Date.class);
        return dailyRevenues.stream().filter(m -> m.get(TRANSACTION_DT) != null)
                .collect(Collectors.toMap(getTransactionDate, this::convertRevenue));

    }


    private void setAccomTypeIfNull(final TreeSet<ReservationNight> filtered,
                                    final List<String> pseudoRooms) {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        filtered.stream().filter(r -> r.getAccomTypeId() == null)
                .peek(r -> log.info("Abnormal Reservation {} with InvTypeCode {}", r.getReservationIdentifier(), r.getInvTypeCode()))
                .forEach(r -> r.setAccomTypeId(
                        findOrCreateAccomodationId(propertyId, r.getInvTypeCode(), pseudoRooms)));
    }

    /**
     * Build collection of room stay prototypes
     *
     * @param reservationNights        converted room stays
     * @param rateCodesByOccupancyDate rate codes
     * @return list of reservation Nights
     */
    public List<ReservationNight> getRoomStayPrototypes(TreeSet<ReservationNight> reservationNights,
                                                 Map<Date, String> rateCodesByOccupancyDate) {
        final Map<String, List<ReservationNight>> prototypes =
                reservationNights.stream().collect(Collectors.groupingBy(this::getKey));
        return resolvePrototypes(prototypes, rateCodesByOccupancyDate);
    }

    /**
     * Build marketCode - inventoryTypeCode key
     *
     * @param reservationNight room stay
     * @return the generated key
     */
    private String getKey(ReservationNight reservationNight) {
        return reservationNight.getAnalyticalMarketSegmentCode() == null ?
                reservationNight.getMarketCode() :
                reservationNight.getAnalyticalMarketSegmentCode() + DASH
                        + reservationNight.getInvTypeCode();
    }

    /**
     * Combine room stays that only differ by rate
     *
     * @param prototypes               room stay prototypes
     * @param rateCodesByOccupancyDate rate codes
     * @return the list of combined room stays
     */
    private List<ReservationNight> resolvePrototypes(Map<String, List<ReservationNight>> prototypes,
                                                     Map<Date, String> rateCodesByOccupancyDate) {
        List<ReservationNight> resolved = new ArrayList<>();
        prototypes.values().forEach(l -> {
            if (l.size() == 1) {
                resolved.add(l.get(0));
            } else {
                rateCodesByOccupancyDate.putAll(getRateCodesByOccupancyDate(l));
                resolved.addAll(resolveList(l));
            }
        });
        return resolved;
    }

    /**
     * Combine room stays where departure date matches arrival date
     *
     * @param list list of room stays
     * @return the list of combined room stays
     */
    private List<ReservationNight> resolveList(List<ReservationNight> list) {
        List<ReservationNight> removes = new ArrayList<>();
        int skipCount;
        for (int i = 0; i < list.size() - 1; i = i + skipCount) {
            ReservationNight rn1 = list.get(i);
            skipCount = 1;
            while (i + skipCount < list.size() && rn1.getDepartureDate()
                    .equals(list.get(i + skipCount).getArrivalDate())) {
                ReservationNight rn2 = list.get(i + skipCount);
                rn1.setDepartureDate(rn2.getDepartureDate());
                rn1.addRates(rn2.getRates());
                rn1.addRevenueByOccupancyDates(rn2.getRevenueByOccupancyDates());
                removes.add(rn2);
                skipCount++;
            }
        }
        list.removeAll(removes);
        return list;
    }

    protected void assignRateCodes(Map<Date, String> rateCodesByOccupancyDate,
                                   ReservationNight reservationNight) {
        reservationNight.setRateCode(
                rateCodesByOccupancyDate.get(reservationNight.getOccupancyDate()));
    }

    protected Map<Date, String> getRateCodesByOccupancyDate(
            List<ReservationNight> reservationNights) {
        Map<Date, String> rateCodes = new HashMap<>();
        for (ReservationNight rn : reservationNights) {
            String rateValue = rn.getRateCode();
            LocalDate startDate = NGIConvertUtils.convert(rn.getArrivalDate(), LocalDate.class);
            LocalDate endDate = NGIConvertUtils.convert(rn.getDepartureDate(), LocalDate.class);
            DateUtil.datesBetweenInclusive(startDate.toDate(), endDate.minusDays(1).toDate())
                    .forEach(d -> rateCodes.put(d, rateValue));
        }
        return rateCodes;
    }

    /**
     * Expand room stay list by occupancy date
     *
     * @param prototypes               the room stays
     * @param rateCodesByOccupancyDate rate codes
     * @return the list of expanded room stays
     */
    public List<ReservationNight> getReservationNightsByOccupancyDate(
            Collection<ReservationNight> prototypes, Map<Date, String> rateCodesByOccupancyDate) {
        return prototypes.stream()
                .flatMap(rn -> createReservationNights(rn, rateCodesByOccupancyDate).stream())
                .filter(this::isComplete)
                .collect(Collectors.toList());
    }

    /**
     * Loop on date ranges to create multiple room stays
     *
     * @param reservationNight         in reservation night
     * @param rateCodesByOccupancyDate rate codes
     * @return new reservation night objects
     */
    protected List<ReservationNight> createReservationNights(ReservationNight reservationNight,
                                                             Map<Date, String> rateCodesByOccupancyDate) {
        List<ReservationNight> reservationNights = new ArrayList<>();
        Date arrivalDate = reservationNight.getArrivalDate();
        Date departureDate = reservationNight.getDepartureDate();
        Map<Date, Float> rateValues = extractRateValuesByOccupancyDate(reservationNight);
        for (Date occupancyDate = arrivalDate; occupancyDate.before(departureDate); occupancyDate =
                DateUtils.addDays(occupancyDate, 1)) {
            ReservationNight r = new ReservationNight(reservationNight, occupancyDate);
            assignRateAndRevenue(r, rateValues, reservationNight.getRevenue(occupancyDate));
            if (!rateCodesByOccupancyDate.isEmpty() && rateCodesByOccupancyDate.containsKey(
                    occupancyDate)) {
                assignRateCodes(rateCodesByOccupancyDate, r);
            }
            reservationNights.add(r);
        }
        return reservationNights;
    }

    protected Map<Date, Float> extractRateValuesByOccupancyDate(
            final ReservationNight reservationNight) {

        Map<Date, Float> rateValues = new HashMap<>();
        if (CollectionUtils.isEmpty(reservationNight.getRates())) {
            return new HashMap<>();
        }
        for (Map<String, Object> rate : reservationNight.getRates()) {
            if (MapUtils.isEmpty(rate)) {
                continue;
            }
            boolean totalRateEnabled = configParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.TOTAL_RATE_ENABLED.value()) == 1;

            String rateValue;
            if (totalRateEnabled && rate.get(GROSS_RATE_VALUE) != null) {
                rateValue = rate.get(GROSS_RATE_VALUE).toString();
            } else {
                rateValue = rate.get(RATE_VALUE) == null ? ZERO : rate.get(RATE_VALUE).toString();
            }
            Float floatRate = Float.parseFloat(rateValue);

            LocalDate startDate = NGIConvertUtils.convert(rate.get(START_DATE), LocalDate.class);
            LocalDate endDate = NGIConvertUtils.convert(rate.get(END_DATE), LocalDate.class);
            DateUtil.datesBetweenInclusive(startDate.toDate(), endDate.minusDays(1).toDate())
                    .forEach(d -> rateValues.put(d, floatRate));
        }
        return rateValues;
    }

    /**
     * Convert one room stay into a reservation night
     *
     * @param reservationMap one room stay
     * @param roomstay       one room stay
     * @param stayRevenues stay revenues
     * @param helper conversion helper
     * @return a converted reservation night
     */
    protected ReservationNight createReservationNight(Map<String, Object> reservationMap,
                                                      Map<String, Object> roomstay,
                                                      final Map<Date, RoomStayRevenue> stayRevenues,
                                                      ReservationNightConverterHelper helper) {

        String bookingDate = (String) reservationMap.get(BOOKING_DATE);
        if (StringUtils.isBlank(bookingDate)) {
            throw new TetrisException(ErrorCode.INVALID_INPUT,
                    "Booking Date of the reservation is empty");
        }
        final Date bookingDateByTimeZone = getDateByPropertyTimeZone(bookingDate);
        String cancellationDate = (String) reservationMap.get(CANCELLATION_DATE);
        String nationality = getNationality(reservationMap);
        String channel = (String) reservationMap.get(CHANNEL);
        String confirmationNumber = (String) reservationMap.get(CONFIRMATION_NUMBER);
        String invTypeCode = (String) roomstay.get(INV_TYPE_CODE);
        String marketCode = (String) roomstay.get(MARKET_CODE);
        Integer los = Math.toIntExact(DateUtil.daysBetween(NGIConvertUtils.convert(roomstay.get(ARRIVAL_DATE), Date.class), NGIConvertUtils.convert(roomstay.get(DEPARTURE_DATE), Date.class)));
        final String rateCode = roomstay.get(RATE_CODE) == null ? "" : helper.resolveRateCode(roomstay, los);
        String analyticalMarketSegmentCode = (String) roomstay.get(ANALYTICAL_MARKET_SEGMENT_CODE);
        if (helper.shouldResolveAMS()) {
            analyticalMarketSegmentCode = helper.resolveStraightMSForIPP(analyticalMarketSegmentCode, los);
            marketCode = getMarketCode(analyticalMarketSegmentCode, marketCode);
            final String esMarketCode = helper.resolveESMarketCode(analyticalMarketSegmentCode, rateCode,
                    removeTimeFromDate(bookingDateByTimeZone));
            analyticalMarketSegmentCode = helper.resolveResAMS(esMarketCode, rateCode);
            if (analyticalMarketSegmentCode.contains(DEF_FUT)){
                String baseMktSegCode = analyticalMarketSegmentCode.split("_")[0];
                String originalMktSeg = baseMktSegCode + "_" + DEF_FUT;
                analyticalMarketSegmentCode = helper.resolveDefaultMS(originalMktSeg,los);
            }
        }

        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        ReservationNight reservationNight = new ReservationNight()
                .setArrivalDate(NGIConvertUtils.convert(roomstay.get(ARRIVAL_DATE), Date.class))
                .setDepartureDate(NGIConvertUtils.convert(roomstay.get(DEPARTURE_DATE), Date.class))
                .setInvBlockCode((String) roomstay.get(INV_BLOCK_CODE))
                .setMarketCode(marketCode)
                .setSourceBooking((String) roomstay.get(SOURCE_BOOKING_CODE))
                .setNumberChildren((Integer) roomstay.get(NUMBER_OF_CHILDREN))
                .setNumberAdults((Integer) roomstay.get(NUMBER_OF_ADULTS))
                .setBookingType((String) roomstay.get(BOOKING_TYPE))
                .setInvTypeCode(invTypeCode)
                .setBookedAccomTypeCode(roomstay.get(BOOKED_ACCOM_TYPE_CODE) == null ? invTypeCode
                        : (String) roomstay.get(BOOKED_ACCOM_TYPE_CODE))
                .setRoomNumber((String) roomstay.get(ROOM_NUMBER))
                .setRates((List<Map<String, Object>>) roomstay.get(RATES))
                .setRevenue(convertRevenue((Map<String, Object>) roomstay.get(REVENUE)))
                .setReservationIdentifier((String) reservationMap.get(RESERVATION_ID))
                .setIndividualStatus((String) reservationMap.get(STATUS))
                .setNationality(nationality)
                .setChannel(channel)
                .setConfirmationNo(confirmationNumber)
                .setCreateDate(DateUtil.getCurrentDate())
                .setRoomRevenue(BigDecimal.ZERO)
                .setTotalRevenue(BigDecimal.ZERO)
                .setSharers((List<String>) reservationMap.get(SHARERS))
                .setPreviousSharers((List<String>) reservationMap.get(PREVIOUS_SHARERS))
                .setRateCode(rateCode)
                .setStayRevenueByOccupancyDates(stayRevenues);

        reservationNight.setPropertyId(propertyId);
        reservationNight.setFileMetadataId(helper.getFileMetadataId());
        reservationNight.setBookingDate(removeTimeFromDate(bookingDateByTimeZone));
        reservationNight.setBookingTm(bookingDateByTimeZone);

        if (StringUtils.isNotBlank(cancellationDate)) {
            Date cancelDateByTimeZone = getDateByPropertyTimeZone(cancellationDate);
            reservationNight.setCancellationDate(removeTimeFromDate(cancelDateByTimeZone));
        }
        reservationNight.setAccomTypeId(helper.shouldCreateAccomType(invTypeCode) ?
                findOrCreateAccomodationId(propertyId, invTypeCode,
                        helper.getRoomTypeRecodingMappings(),
                        reservationNight) : null);
        applyRoomTypeRecodingTransformation(helper.getRoomTypeRecodingMappings(),
                reservationNight);
        if (Boolean.TRUE.equals(helper.shouldRevertToOldIppMarketCode(reservationNight))) {
            ReservationNight oldReservationNight = helper.fetchExistingReservationsById(reservationNight.getReservationIdentifier());
            reservationNight.setRateCode(oldReservationNight.getRateCode());
            reservationNight.setMarketCode(oldReservationNight.getMarketCode());
            analyticalMarketSegmentCode = helper.resolveResAMS(reservationNight.getMarketCode(), reservationNight.getRateCode());
            if (analyticalMarketSegmentCode.contains(DEF_FUT)){
                analyticalMarketSegmentCode = oldReservationNight.getAnalyticalMarketSegmentCode();
            }
            log.debug("REVERTED TRANSACTION : {} MC: {} RateCode: {}", reservationNight.getReservationIdentifier(),
                    reservationNight.getMarketCode(), reservationNight.getRateCode());
        }

        reservationNight.setAnalyticalMarketSegmentCode(analyticalMarketSegmentCode)
                .setMarketSegId(helper.isCreateMktAccomsIfMissing()
                ? findOrCreateMarketSegmentId(propertyId,
                getMarketCode(analyticalMarketSegmentCode, marketCode)) : null);

        if (NO_SHOW.equalsIgnoreCase(reservationNight.getIndividualStatus())
                || CANCELLED.equalsIgnoreCase(reservationNight.getIndividualStatus())) {
            reservationNight.setRoomRevenue(BigDecimal.ZERO);
            reservationNight.setTotalRevenue(BigDecimal.ZERO);
        }

        return reservationNight;
    }

    /**
     * get Room Type Recording Mapping
     *
     * @return map of room type recording
     */
    private Map<String, TreeMap<Date, String>> getRoomTypeRecordingMapping() {
        return configParamsService.getBooleanParameterValue(
                FeatureTogglesConfigParamName.ROOM_TYPE_RECODING_ENABLED)
                ? roomTypeRecodingService.fetchRenameConfigMappingsUptilNow()
                : Collections.emptyMap();
    }

    /**
     * @param roomTypeRecodingMappingsForRename mappings
     * @param reservationNight the reservation night to transform
     */
    public void applyRoomTypeRecodingTransformation(
            Map<String, TreeMap<Date, String>> roomTypeRecodingMappingsForRename,
            ReservationNight reservationNight) {
        if (roomTypeRecodingMappingsForRename.containsKey(
                reservationNight.getBookedAccomTypeCode())) {
            TreeMap<Date, String> effectiveDateNewRTMapping =
                    roomTypeRecodingMappingsForRename.get(
                            reservationNight.getBookedAccomTypeCode());
            final Map.Entry<Date, String> newAccomTypeCode =
                    effectiveDateNewRTMapping.ceilingEntry(reservationNight.getBookingDate());
            if (newAccomTypeCode != null) {
                reservationNight.setBookedAccomTypeCode(newAccomTypeCode.getValue());
            }
        }
    }

    /**
     * get nationality with max length
     *
     * @param roomstay the raw room stay object
     * @return nationality
     */
    private String getNationality(Map<String, Object> roomstay) {
        String nationality = (String) roomstay.get(NATIONALITY);
        if (StringUtils.isNotEmpty(nationality) && nationality.length() <= MAX_NATIONALITY_LENGTH) {
            return nationality;
        }
        return null;
    }

    /**
     * Check to see if resevation night is complete
     *
     * @param reservationNight the reservation night to inspect
     * @return complete or not
     */
    private boolean isComplete(ReservationNight reservationNight) {
        return StringUtils.isNotBlank(reservationNight.getMarketCode())
                && StringUtils.isNotBlank(reservationNight.getInvTypeCode())
                && reservationNight.getArrivalDate() != null
                && reservationNight.getDepartureDate() != null;
    }

    /**
     * Populate Revenue
     *
     * @param revenueMap the revenue map
     * @return Room Stay Revenue
     */
    private RoomStayRevenue convertRevenue(Map<String, Object> revenueMap) {
        if (MapUtils.isEmpty(revenueMap)) {
            return null;
        }

        RoomStayRevenue revenue = new RoomStayRevenue();
        revenue.setTotalRevenue(
                NGIConvertUtils.convert(revenueMap.get(TOTAL_REVENUE), BigDecimal.class));
        revenue.setRoomRevenue(
                NGIConvertUtils.convert(revenueMap.get(ROOM_REVENUE), BigDecimal.class));
        revenue.setFoodRevenue(
                NGIConvertUtils.convert(revenueMap.get(FOOD_REVENUE), BigDecimal.class));
        revenue.setOtherRevenue(
                NGIConvertUtils.convert(revenueMap.get(OTHER_REVENUE), BigDecimal.class));

        return revenue;
    }

    /**
     * Check which market code to use
     *
     * @param ams ams
     * @param ms  ns
     * @return ms/ams
     */
    private String getMarketCode(String ams, String ms) {
        return ams == null ? ms : ams;
    }

    /**
     * Convert Rate for one Reservation Night
     *
     * @param reservationNight incoming reservation night
     */
    protected void assignRateAndRevenue(ReservationNight reservationNight,
                                        Map<Date, Float> rateValues, RoomStayRevenue revenue) {

        float rateNumeric = rateValues.getOrDefault(reservationNight.getOccupancyDate(), 0.0f);
        reservationNight.setRateValue(BigDecimal.valueOf(rateNumeric));

        boolean includeNsCxRevenue = configParamsService.getBooleanParameterValue(INCLUDE_NO_SHOW_CANCELLATION_REVENUE);
        if ((NO_SHOW.equalsIgnoreCase(reservationNight.getIndividualStatus())
                || CANCELLED.equalsIgnoreCase(reservationNight.getIndividualStatus()) &&
                !includeNsCxRevenue)) {
            return;
        }

        // If Revenue is populated, these values are coming from OXI's Stay Messages, hence these take precedence over
        // the value calculated using Daily Rate...., however the Daily Rates method would still be used for most
        // scenarios where Stay Messages are not sent..
        if (revenue != null) {
            reservationNight
                    .setTotalRevenue(revenue.getTotalRevenue())
                    .setRoomRevenue(revenue.getRoomRevenue())
                    .setFoodRevenue(revenue.getFoodRevenue())
                    .setOtherRevenue(revenue.getOtherRevenue());
        } else {
            reservationNight
                    .setRoomRevenue(BigDecimal.valueOf(rateNumeric))
                    .setTotalRevenue(BigDecimal.valueOf(rateNumeric));
        }
    }

    /**
     * @param date the date string
     * @return the date in property timezone
     */
    private Date getDateByPropertyTimeZone(String date) {
        return DateUtil.hasOffset(date)
                ? DateUtil.getDateTimeByTimeZone(DateUtil.convertISODate(date),
                dateService.getPropertyTimeZone())
                : NGIConvertUtils.convert(date, Date.class);
    }

    /**
     * @param propertyId the property id
     * @param marketCode market code to find
     * @return market segment summary id
     */
    private Integer findOrCreateMarketSegmentId(final Integer propertyId, String marketCode) {
        if (marketCode == null) {
            return null;
        }
        // Build a new MktSeg with default values if one doesn't exist
        Integer marketSegmentId = marketSegmentRepository.findOrCreateMarketSegmentForCode(
                propertyId, marketCode);
        return marketSegmentId != null ? marketSegmentId : -1;
    }

    /**
     * @param propertyId    property id
     * @param accomTypeCode code
     * @return room type code id
     */
    private Integer findOrCreateAccomodationId(Integer propertyId, String accomTypeCode) {
        return accommodationService.findOrCreateRoomTypeForCode(propertyId, accomTypeCode).getId();
    }

    protected Integer findOrCreateAccomodationId(Integer propertyId, String accomTypeCode,
                                                 Map<String, TreeMap<Date, String>> roomTypeRecodingMappingsForRename,
                                                 ReservationNight reservationNight) {
        if (roomTypeRecodingMappingsForRename.containsKey(accomTypeCode)) {
            TreeMap<Date, String> effectiveDateNewRTMapping = roomTypeRecodingMappingsForRename.get(
                    accomTypeCode);
            final Map.Entry<Date, String> newAccomTypeCode = effectiveDateNewRTMapping.ceilingEntry(
                    reservationNight.getBookingDate());
            if (newAccomTypeCode != null) {
                return findOrCreateAccomodationId(propertyId, newAccomTypeCode.getValue());
            }
        }
        return findOrCreateAccomodationId(propertyId, accomTypeCode);
    }

    private Integer findOrCreateAccomodationId(Integer propertyId, String accomTypeCode,
                                               List<String> pseudoRooms) {
        if (pseudoRooms.contains(accomTypeCode)) {
            return accommodationService.findOrCreatePseudoRoomTypeForCode(propertyId, accomTypeCode)
                    .getId();
        }
        return findOrCreateAccomodationId(propertyId, accomTypeCode);
    }

    /**
     * Filter reservations that have pseudo rooms at the beginning or end of the reservation
     *
     * @param inReservationNights collection of converted reservation nights
     * @param pseudoRooms         list of pseudo room ids
     * @return the filtered reservation nights
     */
    public TreeSet<ReservationNight> filterPartialPseudoRooms(
            TreeSet<ReservationNight> inReservationNights,
            List<String> pseudoRooms) {

        //filter forwards
        TreeSet<ReservationNight> filteredReservationNights = new TreeSet<>(
                Comparator.comparing(ReservationNight::getArrivalDate));

        for (ReservationNight roomStayDto : inReservationNights) {
            if (!pseudoRooms.contains(roomStayDto.getInvTypeCode()) || CollectionUtils.isNotEmpty(
                    filteredReservationNights)) {
                filteredReservationNights.add(roomStayDto);
            }
        }
        //reverse and remove pseudo room types from the back
        for (ReservationNight roomStayDto : inReservationNights.descendingSet()) {
            if (pseudoRooms.contains(roomStayDto.getInvTypeCode())) {
                filteredReservationNights.remove(roomStayDto);
            } else {
                break;
            }
        }

        // massage remaining pseudo room types to recognized room types
        String previousRoomType = null;
        for (ReservationNight validReservationNight : filteredReservationNights) {
            String currentRoomType = validReservationNight.getInvTypeCode();
            boolean isAPseudoRoom = pseudoRooms.contains(currentRoomType);
            if (isAPseudoRoom && previousRoomType != null) {
                validReservationNight.setInvTypeCode(previousRoomType);
                validReservationNight.setAccomTypeId(
                        findOrCreateAccomodationId(validReservationNight.getPropertyId(),
                                validReservationNight.getInvTypeCode()));
            } else {
                previousRoomType = currentRoomType;
            }
        }

        // we might have roomstays now that match and are contiguous in dates, so they need to be combined again
        return filteredReservationNights;
    }

}
