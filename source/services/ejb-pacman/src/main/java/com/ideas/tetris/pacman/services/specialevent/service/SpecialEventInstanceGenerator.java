package com.ideas.tetris.pacman.services.specialevent.service;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.xml.schema.repeatpattern.v1.RepeatPattern;
import com.ideas.tetris.pacman.common.xml.schema.repeatpattern.v1.RepeatPatternEndEnum;
import com.ideas.tetris.pacman.common.xml.schema.repeatpattern.v1.RepeatPatternStartEnum;
import com.ideas.tetris.pacman.common.xml.schema.repeatpattern.v1.RepeatPatternTypeEnum;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEventInstance;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;

import java.util.Calendar;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.Set;

class SpecialEventInstanceGenerator {

    static boolean checkOccurenceCount(RepeatPattern repeatPattern,
                                       int patternEndValue, int noOfInstances) {
        return repeatPattern.getPatterEnd().getMode().value()
                .equals(Constants.OCCURENCES)
                && noOfInstances < patternEndValue;
    }

    static void createInstanceForQuaterlyOnDow(
            Set<PropertySpecialEventInstance> propertySpecialEventInstanceSet,
            Date startDate, int startYear, int startDowPosition,
            String startDowDay, Date endDate, long dateDiffInMillis, int year,
            int quarterStart, int quarter) {
        PropertySpecialEventInstance propertySpecialEventInstance;
        if (year == startYear && quarter == quarterStart) {
            propertySpecialEventInstanceSet.add(createSpecialEventInstance(
                    startDate, endDate));
        } else {
            propertySpecialEventInstance = new PropertySpecialEventInstance();
            propertySpecialEventInstance.setStartDate(DateUtil.getDate(
                    startDowDay, startDowPosition, year, quarter));
            propertySpecialEventInstance.setEndDate(DateUtil.getDate(
                    propertySpecialEventInstance.getStartDate(),
                    dateDiffInMillis));
            propertySpecialEventInstanceSet.add(propertySpecialEventInstance);
        }
    }

    static PropertySpecialEventInstance createSpecialEventInstance(
            Date startDate, Date endDate) {
        PropertySpecialEventInstance propertySpecialEventInstance;
        propertySpecialEventInstance = new PropertySpecialEventInstance();
        propertySpecialEventInstance.setStartDate(startDate);
        propertySpecialEventInstance.setEndDate(endDate);
        return propertySpecialEventInstance;
    }

    private boolean inconsistent;

    private DateParameter endDateParameter;

    private Integer impactOnForcast;

    private RepeatPattern repeatPattern;

    private DateParameter startDateParameter;

    public SpecialEventInstanceGenerator(Integer impactOnForcast,
                                         RepeatPattern repeatPattern, DateParameter startDateParameter,
                                         DateParameter endDateParameter) {
        this.repeatPattern = repeatPattern;
        this.startDateParameter = startDateParameter;
        this.endDateParameter = endDateParameter;
        this.impactOnForcast = impactOnForcast;
    }

    int getWeekOfMonthForDOWPosInMonth(int dayOfWeek, int dayOfWeekPositionInMonth, int year, int month) {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.YEAR, year);
        c.set(Calendar.MONTH, month);
        c.set(Calendar.DAY_OF_WEEK, dayOfWeek);
        c.set(Calendar.DAY_OF_WEEK_IN_MONTH, dayOfWeekPositionInMonth);

        int weekOfMonth = c.get(Calendar.WEEK_OF_MONTH);
        int startDowOfMonth = getFirstDowOfMonthOfSpecifiedCalendar(c);
        if (startDowOfMonth > dayOfWeek) {
            weekOfMonth = weekOfMonth - 1;
        }

        boolean isValid = (year == c.get(Calendar.YEAR)
                && month == c.get(Calendar.MONTH)
                && dayOfWeek == c.get(Calendar.DAY_OF_WEEK)
        );
        if (isValid) {
            return weekOfMonth;
        } else {
            return -1;
        }
    }

    private int getFirstDowOfMonthOfSpecifiedCalendar(Calendar cal) {
        Calendar c = (Calendar) cal.clone();
        c.set(Calendar.DAY_OF_MONTH, 1);
        return c.get(Calendar.DAY_OF_WEEK);
    }

    public Set<PropertySpecialEventInstance> generateInstancesFromRepeatPattern() {
        Set<PropertySpecialEventInstance> propertySpecialEventInstanceSet = new LinkedHashSet<PropertySpecialEventInstance>();

        // Getting the pattern AlternateYear, Year
        String pattern = repeatPattern.getPattern().value();
        long startDateMs = repeatPattern.getStartDate();
        Date startDate = startDateParameter.getTime();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDateParameter.getTime());
        startDateMs = calendar.getTimeInMillis();
        int startYear = calendar.get(Calendar.YEAR);
        int startDow = calendar.get(Calendar.DAY_OF_MONTH);
        int weekOfMonthOrDayOfYear = calendar.get(Calendar.WEEK_OF_MONTH);
        int startMonth = calendar.get(Calendar.MONTH);
        RepeatPatternStartEnum patternStartMode = repeatPattern.getPatterStart().getMode();
        if (RepeatPatternStartEnum.ON_DOW == patternStartMode) {
            startDow = Integer.parseInt(DateUtil.daysOfWeek.get(repeatPattern.getPatterStart().getDow()));
            weekOfMonthOrDayOfYear = repeatPattern.getPatterStart().getDowPosition();
            startMonth = Integer.parseInt(repeatPattern.getPatterStart().getMonth());
        }
        calendar.setTime(endDateParameter.getTime());
        long endDateMs = calendar.getTimeInMillis();
        // calculating the difference between start date and end date
        // milliseconds
        long dateDiffInMillis = DateUtil.getDateDiff(endDateMs, startDateMs);
        // Getting the patternEnd mode value
        int patternEndValue = repeatPattern.getPatterEnd().getValue(); // year/quarter

        // for alternate year the skipping would ensure half occurrences
        int occurences = getOccurenceCount(repeatPattern, startYear, patternEndValue);
        int patternEndYear = 0;
        if (RepeatPatternEndEnum.NO_END.equals(repeatPattern.getPatterEnd().getMode())) {
            patternEndYear = (patternEndYear < DateUtil.getCurrentYear()) ? startYear + 20 : patternEndYear;
        } else {
            patternEndYear = getPatternEndYear(repeatPattern, pattern, startYear, patternEndValue, occurences);
        }

        // Calculating the increment value for looping through the years
        int increment = (pattern.equals(Constants.ALTERNATE_YEAR) ? 2 : 1);

        // creating the global variable for keeping track of occurrence
        int patternOccur = occurences;
        for (int year = startYear; year <= patternEndYear; year = year + increment) {
            if (propertySpecialEventInstanceSet.size() < occurences) {
                patternOccur = 0;
                propertySpecialEventInstanceSet = getInstancesForNonQuarterly(
                        propertySpecialEventInstanceSet, patternEndValue,
                        patternOccur, startDow, startDate, startMonth, year,
                        weekOfMonthOrDayOfYear, dateDiffInMillis);
            }
        }
        for (PropertySpecialEventInstance psei : propertySpecialEventInstanceSet) {
            psei.setRepeatable(this.repeatPattern.getPattern() == RepeatPatternTypeEnum.NONE ? 0
                    : 1);
            psei.setPreEventDays(this.repeatPattern.getPreDays());
            psei.setPostEventDays(this.repeatPattern.getPostDays());
            if (impactOnForcast.equals(Constants.SPECIAL_EVENT_DO_NOT_USE_DATES_FOR_FORECASTING)
                    || impactOnForcast.equals(Constants.SPECIAL_EVENT_INFORMATION_ONLY)) {
                psei.setEnableForecast(0);
            } else {
                psei.setEnableForecast(1);
            }
        }
        return propertySpecialEventInstanceSet;
    }

    public Set<PropertySpecialEventInstance> generateInstancesFromRepeatPatternBySkipingInconsistentSpecialEvent() {
        Set<PropertySpecialEventInstance> propertySpecialEventInstanceSet = new LinkedHashSet<PropertySpecialEventInstance>();

        // Getting the pattern AlternateYear, Year
        String pattern = repeatPattern.getPattern().value();
        long startDateMs = repeatPattern.getStartDate();
        Date startDate = startDateParameter.getTime();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDateParameter.getTime());
        startDateMs = calendar.getTimeInMillis();
        int startYear = calendar.get(Calendar.YEAR);
        int startDow = calendar.get(Calendar.DAY_OF_MONTH);
        int weekOfMonthOrDayOfYear = calendar.get(Calendar.WEEK_OF_MONTH);
        int startMonth = calendar.get(Calendar.MONTH);
        RepeatPatternStartEnum patternStartMode = repeatPattern.getPatterStart().getMode();
        if (RepeatPatternStartEnum.ON_DOW == patternStartMode) {
            startDow = Integer.parseInt(DateUtil.daysOfWeek.get(repeatPattern
                    .getPatterStart().getDow()));
            weekOfMonthOrDayOfYear = repeatPattern.getPatterStart()
                    .getDowPosition();
            startMonth = Integer.parseInt(repeatPattern.getPatterStart()
                    .getMonth());
        }
        calendar.setTime(endDateParameter.getTime());
        long endDateMs = calendar.getTimeInMillis();
        // calculating the difference between start date and end date
        // milliseconds
        long dateDiffInMillis = DateUtil.getDateDiff(endDateMs, startDateMs);
        // Getting the patternEnd mode value
        int patternEndValue = repeatPattern.getPatterEnd().getValue(); // year/quarter

        // for alternate year the skipping would ensure half occurrences
        int occurences = getOccurenceCount(repeatPattern, startYear,
                patternEndValue);
        int patternEndYear = 0;
        if (RepeatPatternEndEnum.NO_END.equals(repeatPattern.getPatterEnd()
                .getMode()) && patternEndYear < DateUtil.getCurrentYear()) {
            patternEndYear = startYear + 20;
        } else {
            patternEndYear = getPatternEndYear(repeatPattern, pattern,
                    startYear, patternEndValue, occurences);
        }

        // Calculating the increment value for looping through the years
        int increment = (pattern.equals(Constants.ALTERNATE_YEAR) ? 2 : 1);

        // creating the global variable for keeping track of occurrence
        int patternOccur = occurences;
        for (int year = startYear; year <= patternEndYear; year = year
                + increment) {
            if (propertySpecialEventInstanceSet.size() < occurences) {
                patternOccur = 0;
                propertySpecialEventInstanceSet = getInstancesForNonQuarterlyBySkipingInconistentEvent(
                        propertySpecialEventInstanceSet, patternEndValue,
                        patternOccur, startDow, startDate, startMonth, year,
                        weekOfMonthOrDayOfYear, dateDiffInMillis);
            }
        }
        for (PropertySpecialEventInstance psei : propertySpecialEventInstanceSet) {
            psei.setRepeatable(this.repeatPattern.getPattern() == RepeatPatternTypeEnum.NONE ? 0
                    : 1);
            psei.setPreEventDays(this.repeatPattern.getPreDays());
            psei.setPostEventDays(this.repeatPattern.getPostDays());
            if (impactOnForcast
                    .equals(Constants.SPECIAL_EVENT_DO_NOT_USE_DATES_FOR_FORECASTING)
                    || impactOnForcast
                    .equals(Constants.SPECIAL_EVENT_INFORMATION_ONLY)) {
                psei.setEnableForecast(0);
            } else {
                psei.setEnableForecast(1);
            }
        }
        return propertySpecialEventInstanceSet;
    }

    /**
     * Returns true if dates may not repeat accurately based on repeat pattern.
     *
     * @return boolean consistent
     */
    public boolean isInconsistent() {
        return this.inconsistent;
    }

    Set<PropertySpecialEventInstance> getInstancesForNonQuarterly(
            Set<PropertySpecialEventInstance> propertySpecialEventInstanceSet,
            int patternEndValue, int patternOccur, int startDow,
            Date startDate, int startMonth, int year, int weekOfMonth,
            long dateDiffInMillis) {
        String patternStartMode = repeatPattern.getPatterStart().getMode().value();
        PropertySpecialEventInstance propertySpecialEventInstance = new PropertySpecialEventInstance();
        Date defaultStartDate = DateUtil.getDate(startDow, weekOfMonth, startMonth, year);
        if (!DateUtil.isDateHasExpectedYear(year, defaultStartDate)) {
            weekOfMonth = weekOfMonth - 1;
            defaultStartDate = DateUtil.getDate(startDow, weekOfMonth, startMonth, year);
        }
        if (checkOccurenceCount(repeatPattern, patternEndValue, patternOccur)) {
            // case3&4 year, alternate year for same start date and dow cases
            if (patternStartMode.equals(Constants.ON_SAME_START_DATE)) {
                int origStartDow = startDow;
                // handles feb 29th
                startDow = DateUtil.getMinDayOfMonth(startDow, startDate, year);
                if (!this.inconsistent) {
                    this.inconsistent = origStartDow != startDow;
                }
                Calendar calendar = Calendar.getInstance();
                calendar.clear();
                calendar.setTime(startDate);
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                int lastDayOfMonth = calendar
                        .getActualMaximum(Calendar.DAY_OF_MONTH);
                if (lastDayOfMonth < startDow) {
                    startDow = lastDayOfMonth;
                }
                propertySpecialEventInstance.setStartDate(DateUtil.getDate(
                        startDow, startMonth, year));
            } else {
                if (!this.inconsistent) {
                    this.inconsistent = !DateUtil
                            .nthOccuranceOfDayInMonthMatchesForOccurance(
                                    startDate, startMonth, year, weekOfMonth,
                                    startDow);
                }
                propertySpecialEventInstance.setStartDate(defaultStartDate);
            }

            propertySpecialEventInstance.setEndDate(DateUtil.getDate(
                    propertySpecialEventInstance.getStartDate(),
                    dateDiffInMillis));
            propertySpecialEventInstanceSet.add(propertySpecialEventInstance);
        } else if (!repeatPattern.getPatterEnd().getMode().value()
                .equals(Constants.OCCURENCES)) {
            if (patternStartMode.equals(Constants.ON_SAME_START_DATE)) {
                int origStartDow = startDow;
                // handles feb 29th
                startDow = DateUtil
                        .getMinDayOfMonth(startDow, startDate, year);
                if (!this.inconsistent) {
                    this.inconsistent = origStartDow != startDow;
                }
                propertySpecialEventInstance.setStartDate(DateUtil.getDate(
                        startDow, startMonth, year));
            } else {
                if (!this.inconsistent) {
                    this.inconsistent = !DateUtil
                            .nthOccuranceOfDayInMonthMatchesForOccurance(
                                    startDate, startMonth, year, weekOfMonth,
                                    startDow);
                }
                propertySpecialEventInstance.setStartDate(defaultStartDate);
            }

            propertySpecialEventInstance.setEndDate(DateUtil.getDate(
                    propertySpecialEventInstance.getStartDate(),
                    dateDiffInMillis));
            propertySpecialEventInstanceSet.add(propertySpecialEventInstance);
        }
        return propertySpecialEventInstanceSet;
    }

    Set<PropertySpecialEventInstance> getInstancesForNonQuarterlyBySkipingInconistentEvent(
            Set<PropertySpecialEventInstance> propertySpecialEventInstanceSet,
            int patternEndValue, int patternOccur, int startDow,
            Date startDate, int startMonth, int year, int weekOfMonth,
            long dateDiffInMillis) {
        boolean isInvalid = false;
        String patternStartMode = repeatPattern.getPatterStart().getMode().value();
        PropertySpecialEventInstance propertySpecialEventInstance = new PropertySpecialEventInstance();

        if (checkOccurenceCount(repeatPattern, patternEndValue, patternOccur)) {
            // case3&4 year, alternate year for same start date and dow cases
            if (patternStartMode.equals(Constants.ON_SAME_START_DATE)) {
                int origStartDow = startDow;
                // handles feb 29th
                startDow = DateUtil.getMinDayOfMonth(startDow, startDate, year);

                isInvalid = origStartDow != startDow;
                if (!this.inconsistent) {
                    this.inconsistent = isInvalid;
                }
                Calendar calendar = Calendar.getInstance();
                calendar.clear();
                calendar.setTime(startDate);
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                int lastDayOfMonth = calendar
                        .getActualMaximum(Calendar.DAY_OF_MONTH);
                if (lastDayOfMonth < startDow) {
                    startDow = lastDayOfMonth;
                }
                propertySpecialEventInstance.setStartDate(DateUtil.getDate(startDow, startMonth, year));
            } else {
                isInvalid = setSpecialEventStartDateForOccurencesPattern(startDow, startMonth,
                        year, propertySpecialEventInstance, repeatPattern.getPatterStart()
                                .getDowPosition());

                if (!this.inconsistent) {
                    this.inconsistent = isInvalid;
                }
            }

            if (!isInvalid) {
                propertySpecialEventInstance.setEndDate(DateUtil.getDate(
                        propertySpecialEventInstance.getStartDate(),
                        dateDiffInMillis));
                propertySpecialEventInstanceSet.add(propertySpecialEventInstance);
            }
        } else if (!repeatPattern.getPatterEnd().getMode().value()
                .equals(Constants.OCCURENCES)) {
            if (patternStartMode.equals(Constants.ON_SAME_START_DATE)) {
                int origStartDow = startDow;
                // handles feb 29th
                startDow = DateUtil.getMinDayOfMonth(startDow, startDate, year);
                isInvalid = origStartDow != startDow;
                if (!this.inconsistent) {
                    this.inconsistent = isInvalid;
                }
                propertySpecialEventInstance.setStartDate(DateUtil.getDate(
                        startDow, startMonth, year));
            } else {
                isInvalid = setSpecialEventStartDateForOccurencesPattern(startDow, startMonth,
                        year, propertySpecialEventInstance, repeatPattern.getPatterStart()
                                .getDowPosition());

                if (!this.inconsistent) {
                    this.inconsistent = isInvalid;
                }
            }

            if (!isInvalid) {
                propertySpecialEventInstance.setEndDate(DateUtil.getDate(
                        propertySpecialEventInstance.getStartDate(),
                        dateDiffInMillis));
                propertySpecialEventInstanceSet.add(propertySpecialEventInstance);
            }
        }
        return propertySpecialEventInstanceSet;
    }

    boolean setSpecialEventStartDateForOccurencesPattern(int dow, int month,
                                                         int year, PropertySpecialEventInstance propertySpecialEventInstance, int dowPositionInMonth) {
        int weekOfMonth = getWeekOfMonthForDOWPosInMonth(dow, dowPositionInMonth, year, month);
        Date specialEventStartDate = null;
        if (weekOfMonth != -1) {
            specialEventStartDate = DateUtil.getDate(dow, weekOfMonth, month, year);
            propertySpecialEventInstance.setStartDate(specialEventStartDate);
            return false;
        } else {
            return true;
        }
    }

    int getOccurenceCount(RepeatPattern repeatPattern, int startYear,
                          int patternEndValue) {
        return (repeatPattern.getPatterEnd().getMode().value()
                .equals(Constants.END_BY) ? repeatPattern.getPatterEnd()
                .getValue() - startYear + 1
                : (repeatPattern.getPatterEnd().getMode().value()
                .equals(Constants.NO_END) ? Constants.NOENDOCCURENCES
                : patternEndValue));
    }

    int getPatternEndYear(RepeatPattern repeatPattern, String pattern,
                          int startYear, int patternEndValue, int occurences) {
        int yearAdd = Constants.ALTERNATE_YEAR.equals(pattern) ? 50 : 20;
        int occIncr = Constants.ALTERNATE_YEAR.equals(pattern) ? (occurences * 2)
                : (occurences - 1);
        return (repeatPattern.getPatterEnd().getMode().value()
                .equals(Constants.OCCURENCES) ? startYear + occIncr
                : (repeatPattern.getPatterEnd().getMode().value()
                .equals(Constants.NO_END) ? startYear + yearAdd
                : patternEndValue));
    }

    int getQuaterlyStartDate(int startYear, int startMonth, int year,
                             int quarterStart) {
        if (year == startYear) {
            quarterStart = DateUtil.getQuarterByMonth(startMonth);
        }
        return quarterStart;
    }

}
