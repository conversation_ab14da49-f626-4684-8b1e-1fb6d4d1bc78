package com.ideas.tetris.pacman.services.accommodation.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.AccomTypeADR;
import com.ideas.tetris.pacman.services.accommodation.AccomTypeDto;
import com.ideas.tetris.pacman.services.accommodation.RoomTypeCache;
import com.ideas.tetris.pacman.services.accommodation.dto.*;
import com.ideas.tetris.pacman.services.accommodation.entity.*;
import com.ideas.tetris.pacman.services.accommodation.purge.AccomTypePurgable;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductAccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.CloseHighestBarService;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.OldRoomClassConfigDto;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomClassMinPriceDiff;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomClassMinPriceDiffSeason;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomTypeSupplement;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigOffsetAccomType;
import com.ideas.tetris.pacman.services.centralrms.CentralRmsService;
import com.ideas.tetris.pacman.services.centralrms.repository.rooms.CentralRMSRoomTypeRepository;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatus;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.componentrooms.entity.CRAccomTypeMapping;
import com.ideas.tetris.pacman.services.configautomation.dto.AccomClassDTO;
import com.ideas.tetris.pacman.services.configautomation.dto.AccomClassDetailsDTO;
import com.ideas.tetris.pacman.services.configautomation.dto.AccomClassResponse;
import com.ideas.tetris.pacman.services.configautomation.dto.AccomTypeDTO;
import com.ideas.tetris.pacman.services.costofwalk.entity.CostofWalkDefault;
import com.ideas.tetris.pacman.services.costofwalk.entity.CostofWalkSeason;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.database.index.entity.CommandLog;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.eventaggregator.AccomConfigAlertComponent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.extendedstay.common.entity.ExtendedStayProductDefinition;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.GroupPricingConfigurationService;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertCreationService;
import com.ideas.tetris.pacman.services.informationmanager.service.AsyncInformationManagerCleanupService;
import com.ideas.tetris.pacman.services.inventorygroup.entity.InventoryGroup;
import com.ideas.tetris.pacman.services.inventorygroup.entity.InventoryGroupDetails;
import com.ideas.tetris.pacman.services.limiteddatabuild.LDBService;
import com.ideas.tetris.pacman.services.opera.entity.DailyBarConfig;
import com.ideas.tetris.pacman.services.outoforderoverride.entity.OutOfOrderOverride;
import com.ideas.tetris.pacman.services.overbooking.entity.OverbookingAccom;
import com.ideas.tetris.pacman.services.overbooking.entity.OverbookingAccomSeason;
import com.ideas.tetris.pacman.services.overbooking.service.OverbookingOverrideService;
import com.ideas.tetris.pacman.services.override.InvalidateOverridesService;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingBaseAccomType;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.dto.impl.RoomClassPropertyConfigurationDto;
import com.ideas.tetris.pacman.services.property.configuration.dto.impl.RoomTypePropertyConfigurationDto;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualifiedDetails;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.pacman.services.revenuestreams.entity.RevenueStreamDetail;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedAccomClass;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedDefaults;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualifiedDetails;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompetitorsAccomClass;
import com.ideas.tetris.pacman.services.webrate.service.AccommodationMappingService;
import com.ideas.tetris.platform.common.Justification;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import javax.persistence.StoredProcedureQuery;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.CENTRAL_RMS_AVAILABLE;
import static com.ideas.tetris.pacman.common.constants.Constants.EMPTY_STRING;
import static com.ideas.tetris.pacman.common.constants.Constants.INACTIVE_DISPLAY_STATUS_ID;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.collections.CollectionUtils.isEmpty;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;

@Component
@Transactional
public class AccommodationService {

    public static final String DEFAULT_MIN_PRICE_DIFFERENTIALS = "INSERT INTO [dbo].[Accom_Class_MinDiff] SELECT [Accom_Class_PriceRank_Path_ID], :defaultMinPriceDiff, :defaultMinPriceDiff, :defaultMinPriceDiff, :defaultMinPriceDiff, :defaultMinPriceDiff, :defaultMinPriceDiff, :defaultMinPriceDiff, 1, GETDATE(), 1, GETDATE() FROM [dbo].[Accom_Class_PriceRank_Path]";
    public static final String UNASSIGNED = "Unassigned";
    public static final String ACCOM_CLASS_ID = "accomClassId";
    public static final String ROOM_TYPE = "room type = ";
    public static final String DELETED = "deleted ";
    public static final String CODE = "code";
    public static final String CODES = "codes";
    private static final String FIND_MISSING_ACCOMMODATION_TYPES_SQL = "SELECT DISTINCT Room_Type as Room_Type FROM "
            + "                        ({selectQuery}) rt "
            + "	WHERE NOT EXISTS (SELECT at.Accom_Type_ID from Accom_Type at WHERE at.Accom_Type_Code = rt.Room_Type)";
    private static final String CREATE_NEW_ACCOMMODATION_TYPES_SQL = "INSERT INTO Accom_Type"
            + "           (Property_ID" + "           ,Accom_Type_Name" + "           ,Accom_Type_Code"
            + "           ,Accom_Type_Description" + "           ,Accom_Type_Capacity" + "           ,Accom_Class_ID"
            + "           ,System_Default" + "           ,Status_ID" + "           ,Last_Updated_DTTM"
            + "           ,Created_DTTM" + "           ,Roh_Type)" + " (SELECT :propertyId, "
            + "             drt.Room_Type, drt.Room_Type, drt.Room_Type," + "             0, "
            + "            (SELECT Accom_Class_ID FROM Accom_Class WHERE Accom_Class_Code = 'Unassigned'),"
            + "            0," + "            1," + "            GETDATE()," + "            GETDATE(),"
            + "            0 FROM (" + FIND_MISSING_ACCOMMODATION_TYPES_SQL + ") drt )";
    private static final String PROPERTY_ID = "propertyId";
    private static final Logger LOGGER = Logger.getLogger(AccommodationService.class);
    private static final String ACCOM_TYPE_ID = "accomTypeId";
    private static final String SELECT_QUERY = "{selectQuery}";

    @Lazy
    @Autowired
    private AsyncInformationManagerCleanupService asyncInformationManagerCleanupService;
    @Autowired
    RoomTypeCache roomTypeCache;
    @Autowired
    AbstractMultiPropertyCrudService multiPropertyCrudService;
    @TenantCrudServiceBean.Qualifier
    @Qualifier("tenantCrudServiceBean")
    @Autowired
    private CrudService tenantCrudService;
    @Autowired
    private InvalidateOverridesService invalidateOverridesService;
    @Autowired
    private OverbookingOverrideService overbookingOverrideService;
    @Autowired
    private PacmanConfigParamsService configParamsService;
    @Autowired
    private SyncEventAggregatorService syncEventAggregatorService;
    @Autowired
    private AccomConfigAlertComponent accomConfigSyncComponent;
    @Autowired
    private DateService dateService;
    @Autowired
    @Qualifier("ldbService")
    private LDBService ldbService;
    @Autowired
    private AlertCreationService alertCreationService;
    @Autowired
    private PricingConfigurationService pricingConfigurationService;
    @Autowired
    private GroupPricingConfigurationService groupPricingConfigurationService;
    @Autowired
    private AccomClassPriceRankService accomClassPriceRankService;
    @Autowired
    private CloseHighestBarService closeHighestBarService;
    @Autowired
    private AccommodationMappingService accommodationMappingService;
    @Autowired
    private CentralRmsService centralRmsService;
    @Autowired
    private CentralRMSRoomTypeRepository centralRMSRoomTypeRepository;

    public InventorySharingRank addInventorySharingRank(List<InventorySharingRank> inventorySharingRanks,
                                                 AccomClassSharingGroup accomClassSharingGroup, AccomClass accomClass, int rank) {
        InventorySharingRank inventorySharingRank = findExistingInventorySharingRankByAccomClass(inventorySharingRanks,
                accomClass);
        if (inventorySharingRank == null) {
            inventorySharingRank = new InventorySharingRank();
            inventorySharingRank.setAccomClassSharingGrp(accomClassSharingGroup);
            inventorySharingRank.setAccomClass(accomClass);
            inventorySharingRanks.add(inventorySharingRank);
        }

        inventorySharingRank.setRank(rank);
        return inventorySharingRank;
    }

    public void addRateUnqualifiedsForAccomClass(AccomClass accomClass) {

        List<RateUnqualified> rateUnqualifieds = findRateUnqualifiedForProperty(
                PacmanWorkContextHelper.getPropertyId());
        if (rateUnqualifieds != null) {

            Integer maxLOS = 8;
            String maxLOSStr = configParamsService.getParameterValue(IPConfigParamName.BAR_MAX_LOS.value());
            if (maxLOSStr != null && maxLOSStr.length() > 0) {
                maxLOS = Integer.parseInt(maxLOSStr);
            }

            for (RateUnqualified rateUnqualified : rateUnqualifieds) {

                RateUnqualifiedAccomClass rateUnqualifiedAccomClass = new RateUnqualifiedAccomClass();
                rateUnqualifiedAccomClass.setRateUnqualifiedId(rateUnqualified.getId());
                rateUnqualifiedAccomClass.setAccomClassId(accomClass.getId());
                rateUnqualifiedAccomClass = tenantCrudService.save(rateUnqualifiedAccomClass);

                RateUnqualifiedDefaults rateUnqualifiedDefaults = new RateUnqualifiedDefaults();
                rateUnqualifiedDefaults.setRateUnqualifiedAccomClass(rateUnqualifiedAccomClass);
                rateUnqualifiedDefaults.setSundayAvailable(1);
                rateUnqualifiedDefaults.setMondayAvailable(1);
                rateUnqualifiedDefaults.setTuesdayAvailable(1);
                rateUnqualifiedDefaults.setWednesdayAvailable(1);
                rateUnqualifiedDefaults.setThursdayAvailable(1);
                rateUnqualifiedDefaults.setFridayAvailable(1);
                rateUnqualifiedDefaults.setSaturdayAvailable(1);
                rateUnqualifiedDefaults.setSundayMinLos(1f);
                rateUnqualifiedDefaults.setMondayMinLos(1f);
                rateUnqualifiedDefaults.setTuesdayMinLos(1f);
                rateUnqualifiedDefaults.setWednesdayMinLos(1f);
                rateUnqualifiedDefaults.setThursdayMinLos(1f);
                rateUnqualifiedDefaults.setFridayMinLos(1f);
                rateUnqualifiedDefaults.setSaturdayMinLos(1f);
                rateUnqualifiedDefaults.setSundayMaxLos(maxLOS.floatValue());
                rateUnqualifiedDefaults.setMondayMaxLos(maxLOS.floatValue());
                rateUnqualifiedDefaults.setTuesdayMaxLos(maxLOS.floatValue());
                rateUnqualifiedDefaults.setWednesdayMaxLos(maxLOS.floatValue());
                rateUnqualifiedDefaults.setThursdayMaxLos(maxLOS.floatValue());
                rateUnqualifiedDefaults.setFridayMaxLos(maxLOS.floatValue());
                rateUnqualifiedDefaults.setSaturdayMaxLos(maxLOS.floatValue());
                rateUnqualifiedDefaults.setUserOverrideOnly(0);
                tenantCrudService.save(rateUnqualifiedDefaults);
            }
        }
    }

    @SuppressWarnings("unchecked")
    public boolean areAllRoomTypesMappedToRoomClassesExcludingZeroCapacity() {
        boolean retVal = true;
        List<AccomType> unassignedRoomTypeList =
                getActiveAccomTypes(AccomType.ALL_UNASSIGNED_BY_PROPERTY_ID,
                        PacmanThreadLocalContextHolder.getWorkContext().getPropertyId());
        if (null != unassignedRoomTypeList && !unassignedRoomTypeList.isEmpty()) {
            retVal = unassignedRoomTypeList.stream().noneMatch(accomType -> accomType.getAccomTypeCapacity() > 0);
        }
        return retVal;
    }

    public List<Integer> getInactiveRoomTypes() {
        return tenantCrudService.findByNamedQuery(AccomType.ALL_BY_STATUS_ID_AND_DISPLAY_STATUS,
                QueryParameter.with("statusId", Status.INACTIVE.getId()).and("displayStatusId", INACTIVE_DISPLAY_STATUS_ID).parameters());
    }

    @SuppressWarnings("unchecked")
	public
    boolean checkIfAllRCsParticipateInInvShare(List<InventorySharingRank> inventorySharingRanks) {
        if (isEmpty(inventorySharingRanks)) {
            return false;
        }

        List<AccomClass> accomClasses = tenantCrudService.findByNamedQuery(
                AccomClass.ALL_ACS_BY_SYSTEMDEFAULT_PROPERTY_ID_STATUS_ID, QueryParameter.with("systemDefault", 0)
                        .and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and("statusId", 1).parameters());
        for (AccomClass accomClass : accomClasses) {
            if (accomClass.getAccomTypes().isEmpty() || accomClass.hasZeroCapacity()) {
                // ignoring the empty RCs
                continue;
            }

            if (!accomClass.isUsedInInventorySharing(inventorySharingRanks)) {
                return false;
            }
        }
        return true;
    }

    public List<AccomClass> findAll() {
        return tenantCrudService.findAll(AccomClass.class);
    }

    public boolean checkIfAllUnitsOfRCSharedInInvShare(List<AccomClassInventorySharing> accomClassInventorySharings) {
        for (AccomClassInventorySharing accomClassInvSharing : accomClassInventorySharings) {
            if (!accomClassInvSharing.isUseMax()) {
                return false;
            }
        }
        return true;
    }

    @SuppressWarnings("unchecked")
    public void clearRohType() {
        // Get the PropertyId from the WorkContext
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();

        // Get all AccomTypes for a Property
        List<AccomType> accomTypes = getActiveAccomTypes(AccomType.BY_PROPERTY_ID, propertyId);
        if (accomTypes != null) {

            // Update each AccomType setting the ROH Type to '0'
            for (AccomType accomType : accomTypes) {
                accomType.setRohType(0);
                tenantCrudService.save(accomType);
            }
        }
    }

    public String convertRtsWithOrderIntoPipeSeparatorFormat(Map<Integer, Set<Integer>> rtsWithOrder) {
        if (rtsWithOrder.isEmpty()) {
            return null;
        }
        StringBuilder rtsWithOrderFinal = new StringBuilder("");
        Set<Integer> keys = rtsWithOrder.keySet();
        for (Integer order : keys) {
            String roomTypeIds = rtsWithOrder.get(order).toString().replace("[", "").replace("]", "");
            rtsWithOrderFinal.append(roomTypeIds).append(":").append(order).append("|");
        }
        rtsWithOrderFinal = new StringBuilder(rtsWithOrderFinal.substring(0, rtsWithOrderFinal.length() - 1));
        return rtsWithOrderFinal.toString();
    }

    @SuppressWarnings("unchecked")
    public void createMissingAccomTypes(String selectQuery) {
        String insertSql = CREATE_NEW_ACCOMMODATION_TYPES_SQL.replace(SELECT_QUERY, selectQuery);
        tenantCrudService.executeUpdateByNativeQuery(insertSql, QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public void deleteAccomClass(Integer accomClassId) {
        // If an AccomClass ID isn't passed in or is set to 0, nothing to delete
        if (accomClassId == null || accomClassId == 0) {
            return;
        }

        // Find the AccomClass for the ID
        AccomClass accomClass = tenantCrudService.find(AccomClass.class, accomClassId);
        if (accomClass == null) {
            LOGGER.warn("Unable to find AccomClass with ID: " + accomClassId);
            return;
        }

        setAssociatedRoomTypesToUnassignedRoomClass(accomClass);

        softDeleteRoomClass(accomClass);

        invalidateOverrides(Collections.singletonList(accomClass));

        Map<String, List<Integer>> changes = new HashMap<>();

        changes.put(Constants.IM_CLEANUP_ACCOM_CONFIG_CHANGED, Collections.singletonList(accomClassId));

        asyncInformationManagerCleanupService.identifyConfigForCleanUp(PacmanWorkContextHelper.getPropertyId(), changes);
    }

    private void setAssociatedRoomTypesToUnassignedRoomClass(AccomClass accomClass) {
        Set<AccomType> accomTypes = accomClass.getAccomTypes();
        if (accomTypes != null) {

            // Look up the Unassigned AccomClass
            AccomClass unassignedAccomClass = findUnassignedAccomClass(accomClass.getPropertyId());

            // For all AccomTypes for the AccomClass, remap them to the
            // unassigned AccomClass
            for (AccomType accomType : accomTypes) {
                accomType.setAccomClass(unassignedAccomClass);
                tenantCrudService.save(accomType);
            }
        }
    }

    private void softDeleteRoomClass(AccomClass accomClass) {
        accomClass.setStatusId(Constants.INACTIVE_STATUS_ID);
        accomClass.setMasterClass(0);
        tenantCrudService.save(accomClass);
    }

    public void deleteAccomClassSharingGroup(Integer accomClassSharingGroupId) {
        // Nothing to delete
        if (accomClassSharingGroupId == null) {
            return;
        }

        // If there is InventorySharing when deleting the
        // AccomClassSharingGroup,
        // a sync is required.
        if (hasInventorySharingWhenBeingDeleted(accomClassSharingGroupId)) {
            syncEventAggregatorService.registerSyncEvent(SyncEvent.INVENTORY_SHARING_CONFIG_CHANGED);
        }

        tenantCrudService.delete(AccomClassSharingGroup.class, accomClassSharingGroupId);

    }

    public void deleteCloseHighestBarForAccomTypes(List<OldRoomClassConfigDto> listOfMovedAccomTypes) {
        Map<Integer, Set<Integer>> oldRoomClassConfigMap = new HashMap<>();
        for (OldRoomClassConfigDto oldRoomClassConfigDto : listOfMovedAccomTypes) {
            if (oldRoomClassConfigMap.containsKey(oldRoomClassConfigDto.getAccomClassId())) {
                oldRoomClassConfigMap.get(oldRoomClassConfigDto.getAccomClassId()).add(oldRoomClassConfigDto.getAccomTypeId());
            } else {
                Set<Integer> movedAccomtypes = new HashSet<>();
                movedAccomtypes.add(oldRoomClassConfigDto.getAccomTypeId());
                oldRoomClassConfigMap.put(oldRoomClassConfigDto.getAccomClassId(), movedAccomtypes);
            }
        }
        closeHighestBarService.deleteCloseHighestBarForAccomClasses(oldRoomClassConfigMap);
    }

    public Map<Integer, Set<Integer>> extractRtsForOrder(List<AccomClass> roomClasses) {
        HashMap<Integer, Set<Integer>> rtsWithOrder = new HashMap<>();
        // key - rank order , value - room types
        for (AccomClass roomClass : roomClasses) {
            Integer rankOrder = roomClass.getRankOrder();
            Set<Integer> roomTypeIdsForRoomClass = getRoomTypeIdsForRoomClass(roomClass);
            if (isNotEmpty(roomTypeIdsForRoomClass)) {
                if (rtsWithOrder.get(rankOrder) != null) {
                    rtsWithOrder.get(rankOrder).addAll(getRoomTypeIdsForRoomClass(roomClass));
                } else {
                    rtsWithOrder.put(rankOrder, getRoomTypeIdsForRoomClass(roomClass));
                }
            }
        }
        return rtsWithOrder;
    }

    @SuppressWarnings("unchecked")
	public
    Set<AccomClass> findAccomClassInventorySharedAccomClasses(AccomClass accomClass) {
        Set<AccomClass> sharedAccomClasses = new HashSet<>();

        List<AccomClassInventorySharing> accomClassInventorySharings = tenantCrudService.findByNamedQuery(
                AccomClassInventorySharing.BY_ACCOM_CLASS_ID_OR_SHARED_ID,
                QueryParameter.with("accomClass", accomClass).and("sharedAccomClass", accomClass).parameters());
        if (accomClassInventorySharings != null) {

            for (AccomClassInventorySharing accomClassInventorySharing : accomClassInventorySharings) {

                if (!accomClassInventorySharing.getAccomClass().equals(accomClass)) {
                    sharedAccomClasses.add(accomClassInventorySharing.getAccomClass());
                } else if (!accomClassInventorySharing.getSharedAccomClass().equals(accomClass)) {
                    sharedAccomClasses.add(accomClassInventorySharing.getSharedAccomClass());
                }
            }
        }

        return sharedAccomClasses;
    }

    /**
     * Return the AccomClassInventorySharing for an AccomClass
     */
    public AccomClassInventorySharing findAccomClassInventorySharing(AccomClass accomClass) {
        return tenantCrudService.findByNamedQuerySingleResult(
                AccomClassInventorySharing.BY_ACCOM_CLASS_ID,
                QueryParameter.with("id", accomClass.getId()).parameters());
    }

    /**
     * Looks up an AccomClassSharingGroup by name
     */
    public AccomClassSharingGroup findExistingAccomClassSharingGroupByName(String name) {
        return tenantCrudService.findByNamedQuerySingleResult(AccomClassSharingGroup.BY_NAME,
                QueryParameter.with("name", name).and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .parameters());
    }

    /**
     * Finds an AccomClass by Name
     */
    public AccomClass findExistingAccomClassWithName(Integer propertyId, String name) {
        return tenantCrudService.findByNamedQuerySingleResult(AccomClass.BY_NAME,
                QueryParameter.with("name", name).and(PROPERTY_ID, propertyId).parameters());
    }

    /**
     * Returns an InventorySharingRank for a particular AccomClass (assumes it's within the same AccomClassSharingGroup
     */
    public InventorySharingRank findExistingInventorySharingRankByAccomClass(List<InventorySharingRank> inventorySharingRanks,
                                                                      AccomClass accomClass) {
        if (inventorySharingRanks != null) {

            for (InventorySharingRank inventorySharingRank : inventorySharingRanks) {
                if (inventorySharingRank.getAccomClass().equals(accomClass)) {
                    return inventorySharingRank;
                }
            }
        }
        return null;
    }

    /**
     * The highest ranked AccomClassInventorySharing is based on which AccomClass is sharing inventory that isn't asking for any in return.
     */
    public AccomClassInventorySharing findHighestRankAccomClassInventorySharing(
            List<AccomClassInventorySharing> accomClassInventorySharings) {
        for (AccomClassInventorySharing accomClassInventorySharing : accomClassInventorySharings) {
            AccomClass accomClass = accomClassInventorySharing.getAccomClass();

            boolean found = false;
            for (AccomClassInventorySharing innerAccomClassInventorySharing : accomClassInventorySharings) {
                AccomClass sharedWithAccomClass = innerAccomClassInventorySharing.getSharedAccomClass();

                if (sharedWithAccomClass != null && accomClass.getId().equals(sharedWithAccomClass.getId())) {
                    found = true;
                    break;
                }
            }

            if (!found) {
                return accomClassInventorySharing;
            }
        }
        return null;
    }

    /**
     * Finds the MasterClass for the Property
     */
    public AccomClass findMasterClass(Integer propertyId) {
        return tenantCrudService.findByNamedQuerySingleResult(AccomClass.GET_MASTER_CLASS,
                QueryParameter.with(PROPERTY_ID, propertyId).parameters());
    }

    public AccomClass findMasterClassForProperty(Integer propertyId) {
        return (AccomClass) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId, AccomClass.GET_MASTER_CLASS,
                QueryParameter.with(PROPERTY_ID, propertyId).parameters());
    }

    public Map<Integer, String> findAccomTypeIdToAccomTypeCodeMapping() {
        List<AccomType> accomTypes =
                tenantCrudService.findByNamedQuery(AccomType.ALL);
        return accomTypes.stream().collect(toMap(AccomType::getId, AccomType::getAccomTypeCode));
    }

    private List<AccomType> getActiveAccomTypes(String allActive, Integer propertyId) {
        return tenantCrudService.findByNamedQuery(allActive,
                QueryParameter.with(AccommodationService.PROPERTY_ID, propertyId).parameters());
    }

    private AccomType findOrCreateRoomTypeForCodeWithAccomClass(Integer propertyId, String roomTypeCode, AccomClass accomClass) {
        AccomType accomType = tenantCrudService.findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", roomTypeCode).parameters());

        // Build a new AccomType with default values if one doesn't exist
        if (accomType == null) {
            LOGGER.info("Unable to find Room Type: " + roomTypeCode + ", creating new RoomType");

            accomType = new AccomType();
            accomType.setPropertyId(propertyId);
            accomType.setAccomClass(accomClass);
            accomType.setAccomTypeCode(roomTypeCode);
            accomType.setName(roomTypeCode);
            accomType.setDescription(roomTypeCode);
            accomType.setRohType(0);
            accomType.setStatusId(Constants.ACTIVE_STATUS_ID);
            accomType.setSystemDefault(0);
            accomType.setAccomTypeCapacity(100);

            // Save the new AccomType
            accomType = tenantCrudService.save(accomType);
        } else {
            accomType.setAccomClass(accomClass);
            accomType = tenantCrudService.save(accomType);
        }

        return accomType;
    }

    private AccomClass findOrCreateAccomClass(String accomClassCode, Integer propertyId) {
        AccomClass accomClass = tenantCrudService.findByNamedQuerySingleResult(AccomClass.BY_CODE, QueryParameter.with(PROPERTY_ID, propertyId).and("code", accomClassCode).parameters());
        if (accomClass == null) {
            accomClass = new AccomClass();
            accomClass.setCode(accomClassCode);
            accomClass.setName(accomClassCode);
            accomClass.setPropertyId(propertyId);
            accomClass.setSystemDefault(0);
            accomClass.setStatusId(1);
            accomClass.setDescription(accomClassCode);
            accomClass.setMasterClassBoolean(true);
            tenantCrudService.save(accomClass);
        }
        return accomClass;
    }

    @ForTesting
    public void createAccomTypes(Integer propertyId, List<String> accomTypeCodes) {
        AccomClass accomClass = findOrCreateAccomClass("MasterAccomClass", propertyId);
        accomTypeCodes.stream().forEach(accomTypeCode -> findOrCreateRoomTypeForCodeWithAccomClass(propertyId, accomTypeCode, accomClass));
    }

    public AccomType findOrCreateRoomTypeForCode(Integer propertyId, String roomTypeCode) {
        AccomType accomType = roomTypeCache.get(propertyId, roomTypeCode);

        // Build a new AccomType with default values if one doesn't exist
        if (accomType == null) {
            LOGGER.info("Unable to find Room Type: " + roomTypeCode + ", creating new RoomType");
            accomType = getAccomType(propertyId, roomTypeCode, Constants.ACTIVE_STATUS_ID, Constants.ACTIVE_DISPLAY_STATUS_ID);
            // Save the new AccomType
            accomType = tenantCrudService.save(accomType);
            ldbService.roomTypeMappingChanged(propertyId);
        }
        return accomType;
    }

    public AccomType findOrCreatePseudoRoomTypeForCode(Integer propertyId, String roomTypeCode) {
        AccomType accomType = roomTypeCache.get(propertyId, roomTypeCode);

        // Build a new AccomType with default values if one doesn't exist
        if (accomType == null) {
            LOGGER.info("Unable to find Room Type: " + roomTypeCode + ", creating new Pseudo RoomType");
            accomType = getAccomType(propertyId, roomTypeCode, Constants.PSEUDO_ROOM_STATUS_ID, Constants.PSEUDO_ROOM_DISPLAY_STATUS_ID);
            // Save the new AccomType
            accomType = tenantCrudService.save(accomType);
            ldbService.roomTypeMappingChanged(propertyId);
        }
        return accomType;
    }

    private AccomType getAccomType(final Integer propertyId, final String roomTypeCode, final Integer activeStatusId,
                                   final Integer activeDisplayStatusId) {
        AccomType accomType;
        accomType = new AccomType();
        accomType.setPropertyId(propertyId);
        accomType.setAccomClass(tenantCrudService.findByNamedQuerySingleResult(AccomClass.BY_CODE,
                QueryParameter.with(PROPERTY_ID, propertyId).and("code", Constants.UNASSIGNED)
                        .parameters()));
        accomType.setAccomTypeCode(roomTypeCode);
        accomType.setName(roomTypeCode);
        accomType.setDescription(roomTypeCode);
        accomType.setRohType(0);
        accomType.setStatusId(activeStatusId);
        accomType.setSystemDefault(0);
        accomType.setDisplayStatusId(activeDisplayStatusId);
        return accomType;
    }

    //so we batch the save, and don't query for accom class for each accom type. really only helpful if there's new accom types
    //or if there's no accom types in the cache
    public List<AccomType> findOrCreateRoomTypeForCodes(Integer propertyId, Set<String> roomTypeCodes) {
        List<AccomType> accomTypes = new ArrayList<>();
        List<AccomType> createdAccomTypes = new ArrayList<>();
        AccomClass accomClass = null;

        for (String code : roomTypeCodes) {
            AccomType accomType = roomTypeCache.get(propertyId, code);

            //not in cache, probably because we just created it
            if (accomType == null) {
                if (accomClass == null) {
                    accomClass = tenantCrudService.findByNamedQuerySingleResult(AccomClass.BY_CODE,
                            QueryParameter.with(PROPERTY_ID, propertyId).and("code", Constants.UNASSIGNED).parameters());
                }
                LOGGER.info("Unable to find Room Type: " + code + ", creating new RoomType");

                accomType = new AccomType();
                accomType.setPropertyId(propertyId);
                accomType.setAccomClass(accomClass);
                accomType.setAccomTypeCode(code);
                accomType.setName(code);
                accomType.setDescription(code);
                accomType.setRohType(0);
                accomType.setStatusId(Constants.ACTIVE_STATUS_ID);
                accomType.setSystemDefault(0);

                createdAccomTypes.add(accomType);
            }

            accomTypes.add(accomType);
        }

        if (!createdAccomTypes.isEmpty()) {
            // Save the new AccomTypes
            tenantCrudService.save(createdAccomTypes);
            ldbService.roomTypeMappingChanged(propertyId);
        }

        return accomTypes;
    }

    @SuppressWarnings("unchecked")
	public
    List<RateUnqualified> findRateUnqualifiedForProperty(Integer propertyId) {
        return tenantCrudService.findByNamedQuery(RateUnqualified.BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, propertyId).parameters());
    }

    /**
     * Finds the Unassigned Room Class for a property
     *
     * @param propertyId
     * @return Unassigned AccomClass
     */
    public AccomClass findUnassignedAccomClass(Integer propertyId) {
        return tenantCrudService.findByNamedQuerySingleResult(
                AccomClass.BY_SYSTEMDEFAULT_AND_PROPERTY_ID_ACCOM_CLASS,
                QueryParameter.with(PROPERTY_ID, propertyId).and("systemDefault", 1).parameters());
    }

    public AccomClass getAccomClassById(Integer id) {
        return tenantCrudService.find(AccomClass.class, id);
    }

    @SuppressWarnings("unchecked")
    public List<AccomClass> getAccomClassesBy(String query) {
        return tenantCrudService.findByNamedQuery(query,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }


    public List<AccomClass> getAccomClassesByRankOrder() {
        return getAccomClassesBy(AccomClass.ALL_ACTIVE_BY_RANK_ORDER);
    }

    public List<AccomClass> getAssignedAccomClassesByRankOrder() {
        return getAccomClassesBy(AccomClass.ALL_ASSIGNED_ACTIVE_BY_RANK_ORDER);
    }

    public List<AccomClass> getAccomClassesByViewOrder() {
        return getAccomClassesBy(AccomClass.ALL_BY_VIEW_ORDER);
    }

    public List<AccomClass> getAccomClassesByViewOrder(Integer propertyId) {
        return getAccomClassesBy(AccomClass.ALL_BY_VIEW_ORDER, propertyId);
    }

    public List<AccomClass> getAssignedAccomClassesByViewOrder(Integer propertyId) {
        return getAccomClassesBy(AccomClass.ALL_ASSIGNED_BY_VIEW_ORDER, propertyId);
    }

    public List<AccomClass> getAssignedAccomClassesByViewOrder() {
        return getAccomClassesBy(AccomClass.ALL_ASSIGNED_BY_VIEW_ORDER);
    }

    public List<AccomClass> getAssignedAccomClassesByViewOrderWithAccomTypes() {
        return getAccomClassesBy(AccomClass.ALL_ASSIGNED_BY_VIEW_ORDER_WITH_ACCOM_TYPE);
    }

    public List<AccomClass> getAccomClassesBy(String query, Integer propertyId) {
        return (List<AccomClass>) multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, query,
                QueryParameter.with(PROPERTY_ID, propertyId).parameters());
    }
    public List<AccomClassResponse> getAccomClasses() {
        return tenantCrudService.findByNamedQuery(AccomClass.GET_FLOOR_CEILING_OF_ALL_ACCOM_CLASSES);
    }

    public List<AccomClass> getActiveNonDefaultAccomClasses() {
        return getAccomClassesBy(AccomClass.ALL_ACTIVE_NON_DEFAULT);
    }

    public List<AccomClass> getActiveNonDefaultAccomClasses(Integer propertyId) {
        return getAccomClassesBy(AccomClass.ALL_ACTIVE_NON_DEFAULT, propertyId);
    }

    public List<AccomClass> getActiveNonDefaultNonEmptyAccomClasses() {
        return tenantCrudService.findByNamedQuery(AccomClass.ALL_ACTIVE_NON_DEFAULT_NONEMPTY,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public List<AccomClass> getInactiveAccomClasses() {
        return tenantCrudService.findByNamedQuery(AccomClass.ALL_INACTIVE_NON_DEFAULT,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @SuppressWarnings("unchecked")
    public List<AccomClassSharingGroup> getAccomClassSharingGroups() {
        List<AccomClassSharingGroup> accomClassSharingGroups = tenantCrudService.findByNamedQuery(
                AccomClassSharingGroup.BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
        if (accomClassSharingGroups != null) {

            // Need to initialize lazy fetched lists
            for (AccomClassSharingGroup accomClassSharingGroup : accomClassSharingGroups) {

                // Initialize AccomClassInventorySharing
                List<AccomClassInventorySharing> accomClassInventorySharings = accomClassSharingGroup
                        .getAccomClassInventorySharings();
                if (accomClassInventorySharings != null) {
                    accomClassInventorySharings.size();
                }

                // Initialize InventorySharingRank
                List<InventorySharingRank> inventorySharingRanks = accomClassSharingGroup.getInventorySharingRanks();
                if (inventorySharingRanks != null) {
                    inventorySharingRanks.size();
                }
            }
        }

        return accomClassSharingGroups;
    }


    public AlertCreationService getAlertCreationService() {
        return alertCreationService;
    }

    public void setAlertCreationService(AlertCreationService alertCreationService) {
        this.alertCreationService = alertCreationService;
    }

    public List<AccomClass> getAllAccomClassDetails() {
        return getAccomClassesByViewOrder();
    }

    public List<AccomClassSharingGroup> getAllAccomClassSharingGroupDetails() {
        // Get the PropertyId from the WorkContext
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        return getAllAccomClassSharingGroupDetails(propertyId);
    }

    @SuppressWarnings("unchecked")
    public List<AccomClassSharingGroup> getAllAccomClassSharingGroupDetails(Integer propertyId) {
        return tenantCrudService.findByNamedQuery(AccomClassSharingGroup.BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, propertyId).parameters());
    }

    public List<AccomClassSummary> getAllAccomClassSummaries() {
        List<AccomClass> accomClasses = tenantCrudService.findByNamedQuery(AccomClass.ALL_BY_VIEW_ORDER,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
        return getAccomClassSummaryFromAccomClass(accomClasses);
    }

    public List<AccomClassSummary> getAllAccomClassSummariesForInvGrp(Integer invGrpId) {
        InventoryGroup ig = new InventoryGroup();
        ig.setId(invGrpId);
        List<AccomClass> accomClasses = tenantCrudService.findByNamedQuery(InventoryGroupDetails.ALL_ACCOM_CLASSES_BY_VIEW_ORDER,
                QueryParameter.with("inventoryGroup", ig).parameters());
        return getAccomClassSummaryFromAccomClass(accomClasses);
    }

    public List<AccomClassSummary> getAllAccomClassSummariesForProduct(Integer productId) {
        List<AccomClass> accomClasses = tenantCrudService.findByNamedQuery(AccomClass.ALL_ROOM_CLASS_FOR_PRODUCT,
                QueryParameter.with("productId", productId).parameters());
        List<AccomClass> accomClassList = accomClasses.stream().distinct().collect(Collectors.toList());
        return getAccomClassSummaryFromAccomClass(accomClassList);
    }

    private List<AccomClassSummary> getAccomClassSummaryFromAccomClass(List<AccomClass> accomClasses) {
        return accomClasses.stream()
                .filter(this::isNonSystemDefaultAccomClass)
                .map(this::createAccomClassSummaryUsing)
                .collect(Collectors.toList());
    }

    private AccomClassSummary createAccomClassSummaryUsing(AccomClass accomClass) {
        AccomClassSummary summary = new AccomClassSummary(accomClass);
        Set<AccomType> accomTypes = accomClass.getAccomTypes();
        summary.setAccomTypesAssigned(CollectionUtils.isNotEmpty(accomTypes));
        return summary;
    }

    private boolean isNonSystemDefaultAccomClass(AccomClass accomClass) {
        return accomClass.getSystemDefault() == null || accomClass.getSystemDefault() == 0;
    }

    @SuppressWarnings("unchecked")
    public Map<AccomClassSummary, List<AccomTypeSummary>> getAllAccomClassWithTypeSummaries() {

        List<AccomClassSummary> accomClassSummaries = tenantCrudService.findByNamedQuery(
                AccomClass.ALL_ACTIVE_ACCOM_CLASS_SUMMARY,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());

        LinkedHashMap<AccomClassSummary, List<AccomTypeSummary>> accomClassSummaryWithType = new LinkedHashMap<>();
        for (AccomClassSummary accomClassSummary : accomClassSummaries) {
            List<AccomTypeSummary> accomTypeSummaries = tenantCrudService.findByNamedQuery(
                    AccomType.ALL_ACTIVE_ACCOM_TYPES_BY_ACCOM_CLASS_ID,
                    QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                            .and(ACCOM_CLASS_ID, accomClassSummary.getId()).parameters());
            if (isNotEmpty(accomTypeSummaries)) {
                accomClassSummary.setAccomTypesAssigned(true);
                accomClassSummaryWithType.put(accomClassSummary, accomTypeSummaries);
            }
        }

        return accomClassSummaryWithType;
    }

    @SuppressWarnings("unchecked")
    public List<AccomType> getAllAccomTypeDetails() {
        return tenantCrudService.findByNamedQuery(AccomType.ALL);
    }

    public List<AccomType> getAllAccomTypeDetailsIncludingPseudoRooms() {
        return tenantCrudService.findByNamedQuery(AccomType.ALL_INCLUDING_PSEUDO_ROOMS);
    }

    @SuppressWarnings("unchecked")
    public List<AccomType> getAllAccomTypesByViewOrderAndName() {
        return tenantCrudService.findByNamedQuery(AccomType.ALL_ACTIVE_ORDERED_BY_VIEW_ORDER_AND_NAME,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @SuppressWarnings("unchecked")
    public List<AccomType> getAllAssignedAccomTypes() {
        return tenantCrudService.findByNamedQuery(AccomType.ALL_ASSIGNED_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @SuppressWarnings("unchecked")
    public List<AccomType> getAllAssignedAccomTypesIncludingInactive() {
        return tenantCrudService.findByNamedQuery(AccomType.ALL_ASSIGNED_BY_PROPERTY_ID_INCLUDING_INACTIVE,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public List<AccomType> getAllAssignedAccomTypesWithCapacity() {
        return tenantCrudService.findByNamedQuery(AccomType.ALL_ASSIGNED_BY_PROPERTY_ID_VALID_CAPACITY,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public String getContextAtPropertyLevel() {
        return String.format("%s.%s.%s", Constants.CONFIG_PARAMS_NODE_PREFIX, PacmanWorkContextHelper.getClientCode(),
                PacmanWorkContextHelper.getPropertyCode());
    }

    @SuppressWarnings("unchecked")
    public List<Object[]> getFailingScenariosForRtMappingChangeOrRcOrderChange(String roomTypeIdsToMove) {
        String procedure = "exec dbo.usp_get_conflicted_rates_for_changed_rc_rt_order :changedRoomTypeIds, :systemDate";

        String systemDateForQuery = getFormattedSystemDateForQuery();
        return tenantCrudService.findByNativeQuery(procedure, QueryParameter
                .with("changedRoomTypeIds", roomTypeIdsToMove).and("systemDate", systemDateForQuery).parameters());
    }

    private String getFormattedSystemDateForQuery() {
        Date businessDate = dateService.getBusinessDate();
        Date systemDate = DateUtil.addDaysToDate(businessDate, 1);
        return DateUtil.formatDate(systemDate, "yyyy-MM-dd");
    }

    @SuppressWarnings("all")
    public AccomClass getLowestRankAccomClassForInventoryShare() {
        // Get the PropertyId from the WorkContext
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();

        List<AccomClassSharingGroup> accomClassSharingGroups = getAllAccomClassSharingGroupDetails(propertyId);
        if (accomClassSharingGroups != null && accomClassSharingGroups.size() == 1) {
            AccomClassSharingGroup accomClassSharingGroup = accomClassSharingGroups.get(0);

            Query query = tenantCrudService.getEntityManager().createNativeQuery(
                    "select Top 1  accom_class_id, rank from Inventory_Sharing_Rank  where Accom_Class_Sharing_Group_ID =:sharingGroupId order by Rank desc");
            query.setParameter("sharingGroupId", accomClassSharingGroup.getId());
            List<Object[]> resultList = query.getResultList();

            Integer accomClassId = (Integer) resultList.get(0)[0];
            return tenantCrudService.find(AccomClass.class, accomClassId);
        }

        return null;
    }

    @SuppressWarnings("unchecked")
    public List<AccomClassInventorySharing> getPropertyAccomClassInventorySharingDetails(Integer propertyId) {
        return tenantCrudService.findByNamedQuery(
                AccomClassInventorySharing.ALL_BY_PROPERTY, QueryParameter.with(PROPERTY_ID, propertyId).parameters());
    }

    private Set<Integer> getRoomTypeIdsForRoomClass(AccomClass roomClass) {
        if (null != roomClass.getAccomTypes() && !roomClass.getAccomTypes().isEmpty()) {
            Set<Integer> roomTypeIds = new HashSet<>();
            for (AccomType roomType : roomClass.getAccomTypes()) {
                roomTypeIds.add(roomType.getId());
            }
            return roomTypeIds;
        } else {
            return null;
        }
    }

    @SuppressWarnings("unchecked")
    public List<Object[]> getTheFailingScenarios(String roomTypeIdsToMove, Integer proposedRCRankForRTmove,
                                                 Integer firstRoomClassIdToChangeRankOrder, Integer proposedRankOrderForFirstRoomClassId,
                                                 Integer secondRoomClassIdToChangeRankOrder, Integer proposedRankOrderForSecondRoomClassId,
                                                 boolean isRoomClassRankOrderChanged) {
        String procedure = "exec dbo.usp_get_conflicted_rates_for_changed_rc_rt_order :changedRoomTypeIds, :proposedRankForRoomTypeIds, :systemDate,"
                + ":firstRoomClassIdForChangingRankOrder, :proposedRankOrderForFirstRoomClass, :secondRoomClassIdForChangingRankOrder, :proposedRankOrderForSecondRoomClass, "
                + ":isRoomClassRankingChanged";

        return tenantCrudService.findByNativeQuery(procedure,
                QueryParameter.with("changedRoomTypeIds", roomTypeIdsToMove)
                        .and("proposedRankForRoomTypeIds", proposedRCRankForRTmove)
                        .and("systemDate", DateUtil.formatDate(dateService.getBusinessDate(), "yyyy-MM-dd"))
                        .and("firstRoomClassIdForChangingRankOrder", firstRoomClassIdToChangeRankOrder)
                        .and("proposedRankOrderForFirstRoomClass", proposedRankOrderForFirstRoomClassId)
                        .and("secondRoomClassIdForChangingRankOrder", secondRoomClassIdToChangeRankOrder)
                        .and("proposedRankOrderForSecondRoomClass", proposedRankOrderForSecondRoomClassId)
                        .and("isRoomClassRankingChanged", isRoomClassRankOrderChanged).parameters());
    }

    /**
     * Invalidates overrides when AccomTypes have been moved between AccomClasses.
     */
    /**
     * ---Sonar ignores---
     * 3AS3776: Cognitive Complexity of methods should not be too high
     * This should be refactored to reduce complexity but this is too much work and too risky to take on right now. This would require significant regression.
     */
    @SuppressWarnings({"squid:S3776"})
    private void handleInvalidatingOverridesForAccomClasses(List<AccomClass> accomClasses, List<AccomClass> allExistingAccomClasses, List<AccomType> allExistingAccomTypes) {
        List<AccomClass> accomClassesWithChanges = new ArrayList<>();
        List<AccomClass> accomClassesWithUnassignedRTs = new ArrayList<>();
        List<AccomType> accomTypesMoved = new ArrayList<>();

        boolean accomTypeWasMoved = false;

        boolean accomTypeWasMovedToClassOtherThanUnassigned = false;
        boolean addedRateUnqualified = false;
        boolean changedMasterClass = false;
        boolean noMasterClassDefinedInDb = true;
        boolean unassignedRoomTypeInDb = false;
        boolean roomClassRankingChanged = false;
        boolean rohChanged = false;

        // Get the PropertyId from the WorkContext
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        AccomClass masterAccomClass = allExistingAccomClasses.stream().filter(ac -> ac.getMasterClassBoolean()).findFirst()
                .orElseGet(() -> findMasterClass(propertyId));

        for (AccomClass accomClass : accomClasses) {
            if (null != masterAccomClass) {
                // If there is a different master class, we the changedMasterClass flag
                // If the existing master class isn't the master class any more, a sync is required.
                if ((accomClass.isMstClsDefined() && (!masterAccomClass.equals(accomClass))) || (accomClass.getId().equals(masterAccomClass.getId()) && !accomClass.isMstClsDefined())) {
                    changedMasterClass = true;
                }
            } else if (accomClass.isMstClsDefined()) {
                noMasterClassDefinedInDb = false;
            }

            AccomClass existingAccomClassinDb = allExistingAccomClasses.stream().filter(ac -> ac.getId().equals(accomClass.getId())).findFirst()
                    .orElseGet(() -> tenantCrudService.find(AccomClass.class, accomClass.getId()));

            if (!Objects.equals(existingAccomClassinDb.getRankOrder(), accomClass.getRankOrder())) {
                roomClassRankingChanged = true;
            }

            // Iterate over the AccomTypes to see if any have been remapped
            Set<AccomType> accomTypes = accomClass.getAccomTypes();
            if (accomTypes != null) {

                for (AccomType accomType : accomTypes) {

                    AccomType existingAccomType = allExistingAccomTypes.stream().filter(at -> at.getId().equals(accomType.getId())).findFirst()
                            .orElseGet(() -> tenantCrudService.find(AccomType.class, accomType.getId()));

                    if (!Objects.equals(existingAccomType.getRohType(), accomType.getRohType())) {
                        rohChanged = true;
                    }

                    AccomClass existingAccomClass = existingAccomType.getAccomClass();
                    if (!existingAccomClass.getId().equals(accomClass.getId())) {
                        accomTypeWasMoved = true;

                        // If AccomType's AccomClass is not unassigned, we should delete any overrides
                        if (!StringUtils.equalsIgnoreCase(existingAccomClass.getName(), UNASSIGNED)) {
                            accomTypeWasMovedToClassOtherThanUnassigned = true;

                            // If the AccomClass isn't in the remapped list yet, add it
                            if (!accomClassesWithChanges.contains(accomClass)) {
                                accomClassesWithChanges.add(accomClass);
                            }

                            // If the existing AccomClass isn't in the remapped list yet, add it
                            if (!accomClassesWithChanges.contains(existingAccomClass)) {
                                accomClassesWithChanges.add(existingAccomClass);
                            }

                            if (!accomTypesMoved.contains(accomType)) {
                                accomTypesMoved.add(accomType);
                            }
                        } else {
                            unassignedRoomTypeInDb = true;
                            if (configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_UNASSIGNED_ROOM_TYPE_NO_OVERRIDE_REMOVAL_ENABLED)) {
                                accomClassesWithUnassignedRTs.add(accomClass);
                            }
                        }

                        // If the AccomClass that the AccomType is being moved
                        // to doesn't have RateUnqualifieds
                        // they need to be added
                        if (!hasRateUnqualifiedForAccomClass(accomClass)) {
                            addRateUnqualifiedsForAccomClass(accomClass);

                            // If we added rate unqualifieds for the AccomClass,
                            // we need to invalidate the overrides.
                            addedRateUnqualified = true;

                            // If the AccomClass isn't in the remapped list yet,
                            // add it
                            if (configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_UNASSIGNED_ROOM_TYPE_NO_OVERRIDE_REMOVAL_ENABLED)) {
                                if (!accomClassesWithChanges.contains(accomClass) && !accomClassesWithUnassignedRTs.contains(accomClass)) {
                                    accomClassesWithChanges.add(accomClass);
                                }
                            } else {
                                if (!accomClassesWithChanges.contains(accomClass)) {
                                    accomClassesWithChanges.add(accomClass);
                                }
                            }
                        }
                    }
                }
            }
        }

        if (roomClassRankingChanged) {
            SyncEvent syncEvent = SyncEvent.ACCOMMODATION_CONFIG_CHANGED_NO_CALIBRATION;
            // Dirty the Accommodation Configuration Changed Flag
            syncEventAggregatorService.registerSyncEvent(syncEvent);
        }

        // If an AccomType was moved to a different AccomClass or master class
        // changed
        if (accomTypeWasMoved || changedMasterClass) {

            // Invalidate any overrides on the affected AccomClasses
            SyncEvent syncEvent = SyncEvent.ACCOMMODATION_CONFIG_CHANGED_NO_LOCK_DOWN;
            if (accomTypeWasMovedToClassOtherThanUnassigned || addedRateUnqualified || changedMasterClass) {
                invalidateOverrides(accomClassesWithChanges);

                if (accomTypeWasMovedToClassOtherThanUnassigned || changedMasterClass) {
                    syncEvent = SyncEvent.ACCOMMODATION_CONFIG_CHANGED;
                }
            }

            // Dirty the Accommodation Configuration Changed Flag
            syncEventAggregatorService.registerSyncEvent(syncEvent);
        }

        if (!noMasterClassDefinedInDb || unassignedRoomTypeInDb) {
            SyncEvent syncEvent = SyncEvent.ACCOMMODATION_CONFIG_CHANGED;
            // Dirty the Accommodation Configuration Changed Flag
            syncEventAggregatorService.registerSyncEvent(syncEvent);
        }

        if (rohChanged) {
            syncEventAggregatorService.registerSyncEvent(SyncEvent.OVERBOOKING_CONFIG_CHANGED);
        }

        if (accomTypeWasMoved) {
            // If AccomTypes were remapped, then need to delete Pricing Configurations - if enabled
            deletePricingConfigurations(accomTypesMoved);
        }
    }

    public boolean hasInventoryShareRequiringSync(AccomClassSharingGroup accomClassSharingGroup) {
        // New AccomClassSharingGroup, existence of any sharing requires sync
        if (accomClassSharingGroup.getId() == null) {

            // If there are any AccomClassInventorySharing, a sync is required
            List<AccomClassInventorySharing> accomClassInventorySharings = accomClassSharingGroup
                    .getAccomClassInventorySharings();
            if (accomClassInventorySharings != null && !accomClassInventorySharings.isEmpty()) {
                return true;
            }

            // If there are any InventorySharingRank, a sync is required
            List<InventorySharingRank> inventorySharingRanks = accomClassSharingGroup.getInventorySharingRanks();
            return inventorySharingRanks != null && !inventorySharingRanks.isEmpty();
        }

        /*
         * Lookup each existing AccomClassInventorySharing to see if any rankings are different
         */
        List<AccomClassInventorySharing> accomClassInventorySharings = accomClassSharingGroup
                .getAccomClassInventorySharings();
        if (accomClassInventorySharings != null) {

            for (AccomClassInventorySharing accomClassInventorySharing : accomClassInventorySharings) {

                // If 'new' share, a sync is required
                if (accomClassInventorySharing.getId() == null) {
                    return true;
                }

                // If existing, check to see that the share is different
                AccomClassInventorySharing existingAccomClassInventorySharing = tenantCrudService
                        .find(AccomClassInventorySharing.class, accomClassInventorySharing.getId());
                // If any of the fields are different, a sync is required
                if (existingAccomClassInventorySharing != null && !EqualsBuilder
                        .reflectionEquals(accomClassInventorySharing, existingAccomClassInventorySharing)) {
                    return true;
                }
            }
        }

        /*
         * Lookup each existing InventorySharingRank to see if any rankings are different
         */
        List<InventorySharingRank> inventorySharingRanks = accomClassSharingGroup.getInventorySharingRanks();
        if (inventorySharingRanks != null) {

            for (InventorySharingRank inventorySharingRank : inventorySharingRanks) {

                // If 'new' share, a sync is required
                if (inventorySharingRank.getId() == null) {
                    return true;
                }

                // If existing, check to see that the share is different
                InventorySharingRank existingInventorySharingRank = tenantCrudService.find(InventorySharingRank.class,
                        inventorySharingRank.getId());

                // If any of the fields are different, a sync is required
                if (existingInventorySharingRank != null
                        && !EqualsBuilder.reflectionEquals(inventorySharingRank, existingInventorySharingRank)) {
                    return true;
                }
            }
        }

        /*
         * If there were no new or changed sharing values, check to see if anything was deleted
         */
        // Get the existing values
        AccomClassSharingGroup existingAccomClassSharingGroup = tenantCrudService.find(AccomClassSharingGroup.class,
                accomClassSharingGroup.getId());

        // Check to see if any previous AccomClassInventorySharing were deleted
        List<AccomClassInventorySharing> existingAccomClassInventorySharings = existingAccomClassSharingGroup
                .getAccomClassInventorySharings();
        if (CollectionUtils.size(accomClassInventorySharings) != CollectionUtils
                .size(existingAccomClassInventorySharings)) {
            return true;
        }

        // Check to see if any previous InventorySharingRank were deleted
        List<InventorySharingRank> existingInventorySharingRanks = existingAccomClassSharingGroup
                .getInventorySharingRanks();
        return CollectionUtils.size(inventorySharingRanks) != CollectionUtils.size(existingInventorySharingRanks);
    }

    public boolean hasInventorySharingWhenBeingDeleted(Integer accomClassSharingGroupId) {
        // Get the existing values
        AccomClassSharingGroup existingAccomClassSharingGroup = tenantCrudService.find(AccomClassSharingGroup.class,
                accomClassSharingGroupId);

        // Check to see if any previous AccomClassInventorySharing were deleted
        List<AccomClassInventorySharing> existingAccomClassInventorySharings = existingAccomClassSharingGroup
                .getAccomClassInventorySharings();
        if (CollectionUtils.size(existingAccomClassInventorySharings) > 0) {
            return true;
        }

        // Check to see if any previous InventorySharingRank were deleted
        List<InventorySharingRank> existingInventorySharingRanks = existingAccomClassSharingGroup
                .getInventorySharingRanks();
        return CollectionUtils.size(existingInventorySharingRanks) > 0;

    }

    @SuppressWarnings("unchecked")
	public
    boolean hasRateUnqualifiedForAccomClass(AccomClass accomClass) {
        List<RateUnqualified> rateUnqualifieds = tenantCrudService.findByNamedQuery(RateUnqualified.BY_ACCOM_CLASS_ID,
                QueryParameter.with(ACCOM_CLASS_ID, accomClass.getId()).parameters());
        return rateUnqualifieds != null && !rateUnqualifieds.isEmpty();
    }

    @SuppressWarnings("unchecked")
	public
    boolean hasWebRatesForAccomClass(AccomClass accomClass) {
        List<Integer> webrateIds = tenantCrudService.findByNamedQuery(
                WebrateCompetitorsAccomClass.BY_ACCOM_IDS,
                QueryParameter.with("accomIdList", Arrays.asList(accomClass.getId())).parameters());
        return webrateIds != null && !webrateIds.isEmpty();

    }

    public void invalidateOverrides(List<AccomClass> accomClasses) {
        invalidateOverridesService.invalidateOverrides(accomClasses);
    }

    public boolean isBarDecisionRateOfDayEnabled() {
        String paramValue = configParamsService.getValue(getContextAtPropertyLevel(), IPConfigParamName.BAR_BAR_DECISION.value());
        return null != paramValue && paramValue.equalsIgnoreCase(Constants.BAR_DECISION_VALUE_RATEOFDAY);
    }

    public boolean isEnableSingleBarDecision() {
        String value = configParamsService.getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value());
        return StringUtils.equalsIgnoreCase("true", value);

    }

    public void persistAccomClassSharingGroup(AccomClassSharingGroup accomClassSharingGroup) {
        // Nothing to persist
        if (accomClassSharingGroup == null) {
            return;
        }

        // Check to see if the name is already in use
        validateAccomClassSharingGroupNames(Arrays.asList(accomClassSharingGroup));

        // Save the AccomClassSharingGroup
        saveAccomClassSharingGroup(accomClassSharingGroup);

    }

    public void defaultMinimimumPriceDifferentials() {
        // If there are no AccomClassMinPriceDiff records, then we want to create default records
        if (tenantCrudService.findOne(AccomClassMinPriceDiff.class) == null) {
            // Determine what the min price diff should be
            BigDecimal defaultMinPriceDiff = configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value()) ? BigDecimal.ZERO : new BigDecimal("0.01");

            // Create minimum price differential records
            tenantCrudService.executeUpdateByNativeQuery(DEFAULT_MIN_PRICE_DIFFERENTIALS, QueryParameter.with("defaultMinPriceDiff", defaultMinPriceDiff).parameters());
        }
    }

    public void deleteAccomClassInventorySharing() {
        tenantCrudService.deleteAll(AccomClassInventorySharing.class);
        tenantCrudService.deleteAll(InventorySharingRank.class);
        tenantCrudService.deleteAll(AccomClassSharingGroup.class);
    }

    public void persistAccomClassWithViewOrder(AccomClass accomClass) {
        // Can't persist a null AccomClass
        if (accomClass == null) {
            return;
        }

        // If there is an existing AccomClass with that name, we can't allow
        // this to be saved.
        AccomClass existingAccomClass = findExistingAccomClassWithName(accomClass.getPropertyId(),
                accomClass.getName());
        if (existingAccomClass != null) {
            throw new TetrisException(ErrorCode.DUPLICATE_ACCOM_CLASS_NAME, "AccomdatonClass with name "
                    + accomClass.getName() + " already exsist. Accomdation Class name should be unique", null);
        }

        // Set the defaults for an AccomClass
        setAccomClassDefaults(accomClass);

        // Save the AccomClass
        saveAccomClass(accomClass);
    }

    /**
     * Saves an AccomClass, determines whether to call create or update based on the existence of an ID.
     *
     * @param accomClass AccomClass
     */
    public void saveAccomClass(AccomClass accomClass) {
        // No ID means that the AccomClass needs to be created
        tenantCrudService.save(accomClass);
    }

    /**
     * Saves a List of AccomClasses
     */
    public void saveAccomClass(List<AccomClass> accomClasses) {
        // Sort the List of AccomClasses so that the MasterClass AccomClass is
        // saved last
        accomClasses.sort(Comparator.comparing(AccomClass::getMasterClass));

        // Save the AccomClasses
        for (AccomClass accomClass : accomClasses) {
            saveAccomClass(accomClass);

            // The order of the AccomClasses being persisted causes the need to
            // flush
            tenantCrudService.getEntityManager().flush();
            accomConfigSyncComponent.setDirty();
            accomConfigSyncComponent.sync();
        }

        handleGroupPricingRoomServiceCostForAccomClasses();

        ldbService.roomTypeMappingChanged(PacmanWorkContextHelper.getPropertyId());
    }

    /**
     * Saves an AccomClassSharingGroup
     */
    public void saveAccomClassSharingGroup(AccomClassSharingGroup accomClassSharingGroup) {
        // Default any values for AccomClassSharingGroup
        setAccomClassSharingGroupDefaults(accomClassSharingGroup);

        // Sets the Rankings for the AccomClassInventorySharing and
        // InventorySharingRanks
        setRankings(accomClassSharingGroup);

        // Checks to see if there is a sync required due to inventory sharing
        // changes
        if (hasInventoryShareRequiringSync(accomClassSharingGroup)) {
            syncEventAggregatorService.registerSyncEvent(SyncEvent.INVENTORY_SHARING_CONFIG_CHANGED);
        }
        tenantCrudService.save(accomClassSharingGroup);
    }

    /**
     * Defaults values in the AccomClass
     */
    public void setAccomClassDefaults(AccomClass accomClass) {
        // Set PropertyId from WorkContext
        if (accomClass.getPropertyId() == null) {
            accomClass.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        }

        // Default Status
        if (accomClass.getStatusId() == null) {
            accomClass.setStatusId(Constants.ACTIVE_STATUS_ID);
        }

        // Default SystemDefault
        if (accomClass.getSystemDefault() == null) {
            accomClass.setSystemDefault(0);
        }

        accomClass.setCode(accomClass.getName());

        // Default MasterClass
        if (accomClass.getMasterClass() == null) {
            accomClass.setMasterClass(0);
        }
    }

    /**
     * Defaults the values in the AccomClassSharingGroup
     */
    public void setAccomClassSharingGroupDefaults(AccomClassSharingGroup accomClassSharingGroup) {
        // Set PropertyId from WorkContext
        if (accomClassSharingGroup.getPropertyId() == null) {
            accomClassSharingGroup.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        }

        // Make sure the AccomClassSharingGroup is set on the
        // AccomClassInventorySharing
        List<AccomClassInventorySharing> accomClassInventorySharings = accomClassSharingGroup
                .getAccomClassInventorySharings();
        if (accomClassInventorySharings != null) {

            List<AccomClassInventorySharing> toBeDeletedInventorySharings = new ArrayList<>();

            for (AccomClassInventorySharing accomClassInventorySharing : accomClassInventorySharings) {

                if (accomClassInventorySharing.getSharedAccomClass() == null) {
                    toBeDeletedInventorySharings.add(accomClassInventorySharing);
                } else {
                    // Set the AccomClassSharingGroup
                    accomClassInventorySharing.setAccomClassSharingGrp(accomClassSharingGroup);
                    accomClassInventorySharing.setRank(null);
                }
            }

            accomClassInventorySharings.removeAll(toBeDeletedInventorySharings);
        }

        // Make sure the AccomClassSharingGroup is set on the
        // InventorySharingRank
        List<InventorySharingRank> inventorySharingRanks = accomClassSharingGroup.getInventorySharingRanks();
        if (inventorySharingRanks != null) {

            for (InventorySharingRank inventorySharingRank : inventorySharingRanks) {
                inventorySharingRank.setAccomClassSharingGrp(accomClassSharingGroup);
                inventorySharingRank.setRank(null);
            }
        }
    }

    public void setAccomConfigSyncComponent(AccomConfigAlertComponent accomConfigSyncComponent) {
        this.accomConfigSyncComponent = accomConfigSyncComponent;
    }

    /**
     * @deprecated (" Should not be used once we migrate to Rooms Configuration as it supports two ROH types ")
     */
    @Deprecated
    @Justification("Should not be used once we migrate to Rooms Configuration as it supports two ROH types")
    public boolean setAccomTypeAsRoh(Integer accomTypeId, Boolean isRohRoomType) {
        // Get the PropertyId from the WorkContext
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();

        // Look up AccomType that was passed in
        AccomType accomType = tenantCrudService.find(AccomType.class, accomTypeId);

        // Get the existing ROH AccomType for the property
        AccomType existingROHAccomType = tenantCrudService.findByNamedQuerySingleResult(
                AccomType.GET_ROH_TYPE, QueryParameter.with(PROPERTY_ID, propertyId).parameters());

        // If passed in AccomType is ROH and there is an existing AccomType that
        // is already ROH, remove it
        if (existingROHAccomType != null && !existingROHAccomType.getId().equals(accomType.getId()) && isRohRoomType) {
            existingROHAccomType.setRohType(0);
            tenantCrudService.save(existingROHAccomType);
            tenantCrudService.getEntityManager().flush();
        }

        // Update AccomType
        accomType.setRohType(isRohRoomType ? 1 : 0);
        tenantCrudService.save(accomType);

        // Cleanup any ROH configurations
        overbookingOverrideService.afterROHConfigurationCleanup();

        return true;
    }

    public List<AccomType> getROHAccomTypes() {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        return tenantCrudService.findByNamedQuery(
                AccomType.GET_ROH_TYPE, QueryParameter.with(PROPERTY_ID, propertyId).parameters());
    }

    public List<AccomType> getROHAccomTypesValidCapacity() {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        return tenantCrudService.findByNamedQuery(
                AccomType.GET_ROH_TYPE_VALID_CAPACITY, QueryParameter.with(PROPERTY_ID, propertyId).parameters());
    }

    public List<AccomType> getROHAccomTypesWithValidCapacityAndDisplayStatus() {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        return tenantCrudService.findByNamedQuery(
                AccomType.GET_ROH_TYPE_VALID_CAPACITY_AND_DISPLAY_STATUS, QueryParameter.with(PROPERTY_ID, propertyId).parameters());
    }

    public void setConfigParamsService(PacmanConfigParamsService configParamsService) {
        this.configParamsService = configParamsService;
    }

    public void setDateService(DateService dateService) {
        this.dateService = dateService;
    }

    public void setLDBService(LDBService ldbService) {
        this.ldbService = ldbService;
    }

    public void setInvalidateOverridesService(InvalidateOverridesService invalidateOverridesService) {
        this.invalidateOverridesService = invalidateOverridesService;
    }

    public void setOverbookingOverrideService(OverbookingOverrideService overbookingOverrideService) {
        this.overbookingOverrideService = overbookingOverrideService;
    }

    public void setPricingConfigurationService(PricingConfigurationService pricingConfigurationService) {
        this.pricingConfigurationService = pricingConfigurationService;
    }

    public void setGroupPricingCOnfigurationService(GroupPricingConfigurationService groupPricingConfigurationService) {
        this.groupPricingConfigurationService = groupPricingConfigurationService;
    }


    /**
     * Adds/updates/removes any InventorySharingRanks in relation to the AccomClassSharingGroup being added/updated/deleted.
     */
    public void setRankings(AccomClassSharingGroup accomClassSharingGroup) {
        List<AccomClassInventorySharing> accomClassInventorySharings = accomClassSharingGroup
                .getAccomClassInventorySharings();

        if (accomClassInventorySharings == null) {
            accomClassSharingGroup.setInventorySharingRanks(null);
            return;
        }

        // Get the InventorySharingRank list, if there are none, set them on the
        // AccomClassSharingGroup
        List<InventorySharingRank> inventorySharingRanks = accomClassSharingGroup.getInventorySharingRanks();
        if (inventorySharingRanks == null) {
            inventorySharingRanks = new ArrayList<>();
            accomClassSharingGroup.setInventorySharingRanks(inventorySharingRanks);
        }

        int nextAccomClassInventorySharingRank = 1;
        int nextInventorySharingRank = 1;

        // Find the Highest Ranked AccomClassInventorySharing and use it as the
        // driving object to chain
        // through the list.
        AccomClassInventorySharing highestRankedAccomClassInventorySharing = findHighestRankAccomClassInventorySharing(
                accomClassInventorySharings);
        if (highestRankedAccomClassInventorySharing != null) {

            highestRankedAccomClassInventorySharing.setRank(nextAccomClassInventorySharingRank);
            nextAccomClassInventorySharingRank++;

            addInventorySharingRank(inventorySharingRanks, accomClassSharingGroup,
                    highestRankedAccomClassInventorySharing.getAccomClass(), nextInventorySharingRank);
            nextInventorySharingRank++;

            boolean foundNextAccomClass = true;
            AccomClass nextAccomClass = highestRankedAccomClassInventorySharing.getSharedAccomClass();
            while (foundNextAccomClass) {

                // Add to InventorySharingRank
                addInventorySharingRank(inventorySharingRanks, accomClassSharingGroup, nextAccomClass,
                        nextInventorySharingRank);
                nextInventorySharingRank++;

                // Need to look for next AccomClassInventorySharing, set the
                // found property to false
                foundNextAccomClass = false;
                for (AccomClassInventorySharing accomClassInventorySharing : accomClassInventorySharings) {
                    AccomClass accomClass = accomClassInventorySharing.getAccomClass();

                    if (nextAccomClass.getId().equals(accomClass.getId())) {
                        accomClassInventorySharing.setRank(nextAccomClassInventorySharingRank);
                        nextAccomClassInventorySharingRank++;

                        nextAccomClass = accomClassInventorySharing.getSharedAccomClass();
                        foundNextAccomClass = true;
                        break;
                    }
                }
            }
        }

        // Reset rankings for InventorySharingRanks that are in list but no
        // longer in chain
        List<InventorySharingRank> toBeDeletedInventorySharingRanks = new ArrayList<>();
        for (InventorySharingRank inventorySharingRank : inventorySharingRanks) {
            if (inventorySharingRank.getRank() == null) {
                toBeDeletedInventorySharingRanks.add(inventorySharingRank);
            }
        }

        inventorySharingRanks.removeAll(toBeDeletedInventorySharingRanks);
    }

    public void setSyncEventAggregatorService(SyncEventAggregatorService syncEventAggregatorService) {
        this.syncEventAggregatorService = syncEventAggregatorService;
    }

    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }

    public boolean singleInventoryShareExist(List<AccomClassSharingGroup> accomClassSharingGroups) {
        return accomClassSharingGroups != null && accomClassSharingGroups.size() == 1;
    }

    /**
     * This method will check if a Single share exists for the given property. If yes it returns true.
     *
     * @return singleInvShareExists Boolean flag to indicate the response
     */
    public boolean singleInventoryShareExistForProperty() {
        // Get the PropertyId from the WorkContext
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();

        List<AccomClassSharingGroup> accomClassSharingGroups = getAllAccomClassSharingGroupDetails(propertyId);
        return singleInventoryShareExist(accomClassSharingGroups);
    }

    public void updateAccomClasses(List<AccomClass> accomClasses) {
        updateAccomClasses(accomClasses, new ArrayList<>(), new ArrayList<>());
    }

    public void updateAccomClasses(List<AccomClass> accomClasses, List<AccomClass> allExistingAccomClasses, List<AccomType> allExistingAccomTypes) {
        // If no AccomClasses were passed in, nothing to do
        if (accomClasses == null || accomClasses.isEmpty()) {
            return;
        }

        // Validate AccomClass doesn't have a duplicate name
        validateAccomClassNames(accomClasses);

        // If AccomTypes were remapped to other AccomClasses, we need to
        // invalidate their overrides and dirty a staleness flag.
        handleInvalidatingOverridesForAccomClasses(accomClasses, allExistingAccomClasses, allExistingAccomTypes);

        // Save the AccomClasses
        saveAccomClass(accomClasses);

        //Update Webrate mapping data for Accom Classes
        accommodationMappingService.deleteInactiveWebrateAccomClassMapping(PacmanWorkContextHelper.getPropertyId());

        // If Central Rms Available and Room Class Name has changed, then re-sync pricing configuration
        if (isCentralRmsAvailable() && hasRoomClassNameChange(accomClasses, allExistingAccomClasses)) {
            centralRmsService.syncPricingDataOnRoomClassChange();
        }
    }

    public AccomType createAccomType(String name,
                                     String code,
                                     String description,
                                     Integer accomTypeCapacity,
                                     Integer accomClassId,
                                     Integer statusId,
                                     Integer systemDefault,
                                     Integer rohType,
                                     String isComponentRoom
    ) {
        AccomType accomType = new AccomType();
        accomType.setName(name);
        accomType.setAccomTypeCode(code);
        accomType.setPropertyId(PacmanThreadLocalContextHolder.getWorkContext().getPropertyId());
        accomType.setDescription(description);
        accomType.setAccomTypeCode(code);
        accomType.setAccomClass(tenantCrudService.find(AccomClass.class, accomClassId));
        accomType.setStatusId(statusId);
        accomType.setSystemDefault(systemDefault);
        accomType.setRohType(rohType);
        accomType.setIsComponentRoom(isComponentRoom);


        tenantCrudService.save(accomType);
        ldbService.roomTypeMappingChanged(PacmanWorkContextHelper.getPropertyId());
        return accomType;
    }

    public List<RoomType> getAllRoomTypes(){
        List<AccomType> allAccomTypes = tenantCrudService.findAll(AccomType.class);
        List<TenantStatus> allStatus = tenantCrudService.findAll(TenantStatus.class);
        Map<Integer, TenantStatus> statusById = allStatus.stream().collect(toMap(TenantStatus::getId, Function.identity()));
        Set<AccomType> baseRoomTypes = getBaseRoomTypes();
        Map<Integer, AccomType> baseRoomTypeIdsById = baseRoomTypes.stream()
                .collect(Collectors.toMap(AccomType::getId, Function.identity()));
        return allAccomTypes.stream().map(accomType -> createRoomTypeFrom(accomType, statusById, baseRoomTypeIdsById)).collect(Collectors.toList());
    }

    private Set<AccomType> getBaseRoomTypes() {
        return pricingConfigurationService.getAllBaseRoomTypes().stream()
                .map(PricingBaseAccomType::getAccomType)
                .collect(Collectors.toSet());
    }

    private RoomType createRoomTypeFrom(AccomType accomType, Map<Integer, TenantStatus> statusById, Map<Integer, AccomType> baseRoomTypeIdsById) {
        return new RoomType(accomType.getId(), accomType.getName(), accomType.getAccomTypeCode(), accomType.getDescription(),
                accomType.getAccomTypeCapacity(), accomType.getAccomClass().getCode(), statusById.get(accomType.getStatusId()).getName().toUpperCase(),
                getSystemDefault(accomType), baseRoomTypeIdsById.containsKey(accomType.getId()), getRohType(accomType), accomType.isComponentRoom(),
                getDisplayStatus(accomType), getExcludedFromSoldout(accomType), accomType.getSupplementOverbookingType());
    }

    private static String getDisplayStatus(AccomType accomType) {
        return Objects.nonNull(accomType.getDisplayStatusId()) && accomType.getDisplayStatusId() == 1 ? "SHOW" : "HIDE";
    }

    private static boolean getExcludedFromSoldout(AccomType accomType) {
        return Objects.nonNull(accomType.getExcludedFromSoldout()) && accomType.getExcludedFromSoldout() == 1;
    }

    private static boolean getSystemDefault(AccomType accomType) {
        return Objects.nonNull(accomType.getSystemDefault()) && accomType.getSystemDefault() == 1;
    }

    private static boolean getRohType(AccomType accomType) {
        return Objects.nonNull(accomType.getRohType()) && accomType.getRohType() == 1;
    }

    public List<AccomType> getAllAccomTypes() {
        return getAllActiveAccomTypes();
    }

    public List<RoomTypeDto> getAllAssignedValidCapacityNonComponentNonPseudoAccomTypesByRankOrder() {
        String pseudoRoomTypesParam = configParamsService.getParameterValue(IntegrationConfigParamName.PSEUDO_ROOM_TYPE_CODES);
        List<String> pseudoRoomTypes = Arrays.asList(pseudoRoomTypesParam.split(","));
        List<Object[]> results = tenantCrudService.findByNamedQuery(AccomType.ALL_ASSIGNED_ACTIVE_VALID_CAPACITY_NON_COMPONENT);
        List<RoomTypeDto> validRoomTypes = results.stream()
                .map(row -> new RoomTypeDto((Integer) row[0], (String) row[1], (String) row[2]))
                .collect(Collectors.toList());
        return validRoomTypes.stream().filter(at -> !pseudoRoomTypes.contains(at.getAccomTypeCode())).collect(Collectors.toList());
    }

    public void updateAccomClassSharingGroups(List<AccomClassSharingGroup> accomClassSharingGroups) {
        // Nothing to do if the List is null or has no AccomClassSharingGroups
        if (accomClassSharingGroups == null || accomClassSharingGroups.isEmpty()) {
            return;
        }

        // Check to see if the name is already in use
        validateAccomClassSharingGroupNames(accomClassSharingGroups);

        // Save the AccomClassSharingGroups
        for (AccomClassSharingGroup accomClassSharingGroup : accomClassSharingGroups) {
            // Save the AccomClassSharingGroups
            saveAccomClassSharingGroup(accomClassSharingGroup);
            tenantCrudService.getEntityManager().flush();
        }

    }

    /**
     * Validates that we don't have a duplicate class name.
     */
    public void validateAccomClassNames(List<AccomClass> accomClasses) {
        for (AccomClass accomClass : accomClasses) {
            // Check the database to see if an AccomClass exists for the
            // specified name
            AccomClass existingAccomClass = findExistingAccomClassWithName(accomClass.getPropertyId(),
                    accomClass.getName());

            // If there is an existing AccomClass with that name, and the ids
            // aren't the same - we can't allow this to be saved.
            if (existingAccomClass != null && accomClass.getId() != null
                    && !existingAccomClass.getId().equals(accomClass.getId())) {
                throw new TetrisException(ErrorCode.DUPLICATE_ACCOM_CLASS_NAME, "AccommodationClass with name "
                        + accomClass.getName() + " already exsist. Accomdation Class name should be unique", null);
            }
        }
    }

    /**
     * Validates that we don't have a duplicate class name.
     */
    public void validateAccomClassSharingGroupNames(List<AccomClassSharingGroup> accomClassSharingGroups) {
        for (AccomClassSharingGroup accomClassSharingGroup : accomClassSharingGroups) {

            // Lookup existing AccomClassSharingGroup
            AccomClassSharingGroup existingAccomClassSharingGroup = findExistingAccomClassSharingGroupByName(
                    accomClassSharingGroup.getName());

            // If there is an existing AccomClass with that name, and the ids
            // aren't the same - we can't allow this to be saved.
            if (existingAccomClassSharingGroup != null && (accomClassSharingGroup.getId() == null || !existingAccomClassSharingGroup.getId().equals(accomClassSharingGroup.getId()))) {
                throw new TetrisException(ErrorCode.DUPLICATE_SHARING_GROUP_NAME,
                        "AccomClassSharingGroup with name " + accomClassSharingGroup.getName()
                                + " already exists. AccomClassSharingGroup name should be unique",
                        null);
            }
        }
    }

    public boolean validateRatesAsPerOrder(List<AccomClass> accomClasses) {
        // validate rates as per order
        // use feature toggle here
        if (isBarDecisionRateOfDayEnabled() && !validateRcRtRatesAsPerOrder(accomClasses)) {
            throw new TetrisException(ErrorCode.INVALID_RC_RT_RATE_AS_PER_ORDER,
                    "Invalid room type rates as per room class order", null);
        }
        return true;
    }

    public boolean validateRatesWhenRcRtMappingChangedOrRCOrderChanged(String roomTypeIdsToMove,
                                                                       Integer proposedRCRankForRTmove, Integer firstRoomClassIdToChangeRankOrder,
                                                                       Integer proposedRankOrderForFirstRoomClassId, Integer secondRoomClassIdToChangeRankOrder,
                                                                       Integer proposedRankOrderForSecondRoomClassId, boolean isRoomClassRankOrderChanged) {

        List<Object[]> objects = getTheFailingScenarios(roomTypeIdsToMove, proposedRCRankForRTmove,
                firstRoomClassIdToChangeRankOrder, proposedRankOrderForFirstRoomClassId,
                secondRoomClassIdToChangeRankOrder, proposedRankOrderForSecondRoomClassId, isRoomClassRankOrderChanged);

        return objects.isEmpty();
    }

    public boolean validateRatesWhenRoomClassOrderChanged(Integer firstRoomClassIdToChangeRankOrder,
                                                          Integer proposedRankOrderForFirstRoomClassId, Integer secondRoomClassIdToChangeRankOrder,
                                                          Integer proposedRankOrderForSecondRoomClassId) {
        return validateRatesWhenRcRtMappingChangedOrRCOrderChanged(null, null, firstRoomClassIdToChangeRankOrder,
                proposedRankOrderForFirstRoomClassId, secondRoomClassIdToChangeRankOrder,
                proposedRankOrderForSecondRoomClassId, true);
    }

    public boolean validateRatesWhenRoomTypeMappingChanged(List<Integer> roomTypeIds, Integer destinationRoomClassId) {
        AccomClass proposedRCForRTMove = tenantCrudService.find(AccomClass.class, destinationRoomClassId);
        Integer proposedRCRankForRTmove = proposedRCForRTMove.getRankOrder();
        String roomTypeIdsToMoveStr = roomTypeIds.toString().replace("[", "").replace("]", "");

        return validateRatesWhenRcRtMappingChangedOrRCOrderChanged(roomTypeIdsToMoveStr, proposedRCRankForRTmove, null,
                null, null, null, false);
    }

    public boolean validateRcRtRatesAsPerOrder(List<AccomClass> roomClasses) {
        Map<Integer, Set<Integer>> rtsWithOrder = extractRtsForOrder(roomClasses);
        if (rtsWithOrder.isEmpty()) {
            // no need to go ahead as there are no RTs available for any RCs but
            // should go ahead so returning true
            return true;
        }
        String rtsWithOrderFinal = convertRtsWithOrderIntoPipeSeparatorFormat(rtsWithOrder);
        List<Object[]> objects = getFailingScenariosForRtMappingChangeOrRcOrderChange(rtsWithOrderFinal);
        return objects.isEmpty();
    }

    public void deletePricingConfigurations(List<AccomType> accomTypesMoved) {
        LOGGER.info("Deleting Pricing Configurations for Continuous Pricing and Group Pricing");
        pricingConfigurationService.deletePricingConfigurationsForRoomType(new HashSet<>(accomTypesMoved));
    }

    public void deletePricingConfigurationsByAccomTypes(List<AccomType> accomTypesMoved) {
        LOGGER.info("Deleting Pricing Configurations By AccomTYpes for Continuous Pricing and Group Pricing");
        pricingConfigurationService.deletePricingConfigurationsByRoomTypes(new HashSet<>(accomTypesMoved));
    }

    public void handleGroupPricingRoomServiceCostForAccomClasses() {
        if (configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED.value())) {
            LOGGER.info("Updating Group Pricing Service Room Service Cost for Changed Accom Classes");
            groupPricingConfigurationService.handleModifiedRoomClasses();
        }
    }

    @SuppressWarnings("unchecked")
    public List<AccomType> getAllActiveAccomTypes() {
        return tenantCrudService.findByNamedQuery(AccomType.ALL_ACTIVE_NON_DEFAULT_BY_PROPERTY_ID, QueryParameter.with(PROPERTY_ID,
                PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public List<AccomType> getAllActiveAccomTypes(Integer propertyId) {
        return multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, AccomType.ALL_ACTIVE_NON_DEFAULT_BY_PROPERTY_ID, QueryParameter.with(PROPERTY_ID,
                propertyId).parameters());
    }

    public List<AccomType> getAllDiscontinuedRoomTypes() {
        Map<String, Object> map = new HashMap<>();
        map.put(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
        map.put("displayStatusId", 2);
        return tenantCrudService.findByNamedQuery(AccomType.ALL_BY_DISPLAY_STATUS, map);
    }

    public List<AccomType> getAllUnassignedAccomTypes() {
        return tenantCrudService.findByNamedQuery(AccomType.ALL_UNASSIGNED_BY_PROPERTY_ID, QueryParameter.with(PROPERTY_ID,
                PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public List<AccomType> getAllUnassignedRoomTypesIncludingZeroCapacity() {
        return tenantCrudService.findByNamedQuery(AccomType.ALL_UNASSIGNED_BY_PROPERTY_ID_INCLUDING_ZERO_CAPACITY, QueryParameter.with(PROPERTY_ID,
                PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public List<CRAccomTypeMapping> getComponentRoomsMapping(Integer crAccomTypeId) {
        return tenantCrudService.findByNamedQuery(CRAccomTypeMapping.GET_COMPONENT_ROOM_MAPPINGS, QueryParameter.with("crAccomTypeId", crAccomTypeId).parameters());
    }

    public List<AccomTypeADR> getAllActiveAccomTypeADRs() {
        List<AccomType> accomTypes = getAllActiveAccomTypes();

        return getAllActiveAccomTypeADRs(accomTypes);
    }

    public List<AccomTypeADR> getUnassignedAccomTypeADRs() {
        List<AccomType> accomTypes = getAllUnassignedAccomTypes();

        return getAllActiveAccomTypeADRs(accomTypes);
    }

    @SuppressWarnings("unchecked")
    public List<AccomTypeADR> getAllActiveAccomTypeADRs(List<AccomType> accomTypes) {
        return getAccomTypeADRSForGivenPastYear(accomTypes, 1);
    }

    public Map<AccomClass, BigDecimal> getAccomClassAdr() {
        Map<AccomClass, BigDecimal> accomClassAdrMap = new HashMap<>();
        List<AccomTypeADR> accomTypeADRSForGivenPast2Year = getAccomTypeADRSForGivenPastYear(getAllActiveAccomTypes(), 2);
        getActiveNonDefaultAccomClasses().stream().forEach(accomClass -> {
            BigDecimal accomClassAdr = getAccomClassAdrFromAccomTypeAdr(accomTypeADRSForGivenPast2Year, accomClass);
            accomClassAdrMap.put(accomClass, accomClassAdr);
        });
        return accomClassAdrMap;
    }

    private BigDecimal getAccomClassAdrFromAccomTypeAdr(List<AccomTypeADR> accomTypeADRSForGivenPast2Year, AccomClass accomClass) {
        BigDecimal accomClassAdr = BigDecimal.ZERO;
        List<BigDecimal> roomTypesRoomsSold = accomTypeADRSForGivenPast2Year.stream()
                .filter(adr -> adr.getRoomsSold() != null)
                .filter(adr -> accomClass.getAccomTypes() != null)
                .filter(adr -> accomClass.getAccomTypes().contains(adr.getAccomType()))
                .map(AccomTypeADR::getRoomsSold)
                .collect(Collectors.toList());

        BigDecimal totalRoomsSold = roomTypesRoomsSold.isEmpty() ? BigDecimal.ZERO : roomTypesRoomsSold.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        if (totalRoomsSold.compareTo(BigDecimal.ZERO) > 0) {

            List<BigDecimal> roomTypesRoomRevenue = accomTypeADRSForGivenPast2Year.stream()
                    .filter(adr -> adr.getRoomRevenue() != null)
                    .filter(adr -> accomClass.getAccomTypes() != null)
                    .filter(adr -> accomClass.getAccomTypes().contains(adr.getAccomType()))
                    .map(AccomTypeADR::getRoomRevenue)
                    .collect(Collectors.toList());

            BigDecimal totalRoomRevenue = roomTypesRoomRevenue.isEmpty() ? BigDecimal.ZERO : roomTypesRoomRevenue.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            accomClassAdr = totalRoomRevenue.divide(totalRoomsSold, 2, RoundingMode.HALF_UP);
        }
        return accomClassAdr;
    }

    private List<AccomTypeADR> getAccomTypeADRSForGivenPastYear(List<AccomType> accomTypes, int pastYear) {
        // Build a list of AccomTypeADRs
        List<AccomTypeADR> accomTypeADRs = new ArrayList<>();

        // Get all the active AccomTypes
        if (CollectionUtils.isEmpty(accomTypes)) {
            return accomTypeADRs;
        }

        // Get the AccomTypeCodeADRs
        Map<String, AccomTypeCodeADR> accomTypeCodeADRs = getADRsOfAccomTypesOverPastYear(accomTypes, pastYear);

        // Set the ADR for each AccomType
        accomTypes.forEach(accomType -> {
            // Get the ADR, if one was returned - otherwise default '0.0'
            BigDecimal roomsSold = BigDecimal.ZERO;
            BigDecimal roomRevenue = BigDecimal.ZERO;
            BigDecimal adr = BigDecimal.ZERO;
            AccomTypeCodeADR accomTypeCodeADR = accomTypeCodeADRs.get(accomType.getAccomTypeCode());
            if (accomTypeCodeADR != null) {
                roomsSold = accomTypeCodeADR.getRoomsSold();
                roomRevenue = accomTypeCodeADR.getRoomRevenue();
                adr = accomTypeCodeADR.getAdr();
            }

            // Add the AccomTypeADR to the return list
            accomTypeADRs.add(new AccomTypeADR(accomType, roomsSold, roomRevenue, adr));
        });

        Collections.sort(accomTypeADRs);
        return accomTypeADRs;
    }

    private Map<String, AccomTypeCodeADR> getADRsOfAccomTypesOverPastYear(List<AccomType> accomTypes, int pastYear) {
        List<AccomTypeCodeADR> accomTypeCodeADRResults = tenantCrudService.findByNamedQuery(AccomTypeCodeADR.FIND_ACCOM_TYPE_ADR_FROM_POPULATED_TABLE);
        if (CollectionUtils.isEmpty(accomTypeCodeADRResults)) {
            return getAccomTypeADRByQueryingReservationNight(accomTypes, pastYear);
        }

        List<String> accomTypeCodes = accomTypes.stream().map(AccomType::getAccomTypeCode).collect(Collectors.toList());
        return accomTypeCodeADRResults.stream()
                .filter(accomTypeAdr -> accomTypeCodes.contains(accomTypeAdr.getAccomTypeCode()))
                .collect(toMap(AccomTypeCodeADR::getAccomTypeCode, Function.identity()));
    }

    /* This is an attempt to find the ADR using query on reservation_night just in case this table is empty.
       It can happen when table is created but No BDE is ran yet on this property
    */
    private Map<String, AccomTypeCodeADR> getAccomTypeADRByQueryingReservationNight(List<AccomType> accomTypes, int pastYear) {
        Map<String, AccomTypeCodeADR> accomTypeCodeADRs = new HashMap<>();
        Date caughtUpDate = dateService.getCaughtUpDate();
        if (caughtUpDate != null) {
            String accomTypeCodes = accomTypes.stream().map(AccomType::getAccomTypeCode).collect(Collectors.joining(","));

            // Look up the ADRs of all the AccomTypes over the past year
            List<AccomTypeCodeADR> accomTypeCodeADRList = tenantCrudService.findByNamedQuery(AccomTypeCodeADR.FIND_ACCOM_TYPE_ADR, QueryParameter.with(AccomTypeCodeADR.PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and(AccomTypeCodeADR.START_DATE, DateUtil.addYearsToDate(caughtUpDate, -pastYear))
                    .and(AccomTypeCodeADR.END_DATE, caughtUpDate).and(AccomTypeCodeADR.ACCOM_TYPE_CODES, accomTypeCodes).and(AccomTypeCodeADR.TOTAL_RATE_ENABLED, getTotalRateEnabledValue()).parameters());
            if (CollectionUtils.isNotEmpty(accomTypeCodeADRList)) {
                accomTypeCodeADRs.putAll(accomTypeCodeADRList.stream().collect(
                        toMap(AccomTypeCodeADR::getAccomTypeCode, Function.identity())));
            }
        }
        return accomTypeCodeADRs;
    }

    private Integer getTotalRateEnabledValue() {
        Integer totalRateEnabled = configParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.TOTAL_RATE_ENABLED.value());
        return (totalRateEnabled == null) ? 0 : totalRateEnabled;
    }

    @SuppressWarnings("unchecked")
    public List<AccomType> getAllAccomTypesWithCapacity() {
        return tenantCrudService.findByNamedQuery(AccomType.ALL_ACTIVE_CAPACITY, QueryParameter.with(PROPERTY_ID,
                PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @SuppressWarnings("unchecked")
    public List<AccomType> getAllActiveAccomTypesWithCapacity() {
        return tenantCrudService.findByNamedQuery(AccomType.ALL_ACTIVE_VALID_CAPACITY, QueryParameter.with(PROPERTY_ID,
                PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public Integer getTotalAccomCapacity() {
        return getAllActiveAccomTypesWithCapacity().stream().mapToInt(type -> type.getAccomTypeCapacity()).sum();
    }

    public List<String> getAllAccomTypesWithZeroCapacity() {
        return tenantCrudService.findByNamedQuery(AccomType.GET_ZERO_CAPACITY_ROOMTYPES);
    }

    public List<AccomType> getActiveAccomTypesWithValidCapacityAndDisplayStatus() {
        return tenantCrudService.findByNamedQuery(AccomType.ALL_ACTIVE_VALID_CAPACITY_AND_DISPLAY_STATUS, QueryParameter.with(PROPERTY_ID,
                PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public List<AccomType> getActiveAccomTypesWithValidCapacityAndDisplayStatusOptimisedFetch() {
        return tenantCrudService.findByNamedQuery(AccomType.ALL_ACTIVE_VALID_CAPACITY_AND_DISPLAY_STATUS_OPTIMISED, QueryParameter.with(PROPERTY_ID,
                PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public List<AccomType> getActiveAccomTypesWithValidCapacityAndDisplayStatusWithAccomTypeCodeLike(String code) {
        return tenantCrudService.findByNamedQuery(AccomType.ALL_ACTIVE_VALID_CAPACITY_AND_DISPLAY_STATUS_WHERE_ACCOM_CODE_LIKE, QueryParameter.with(PROPERTY_ID,
                PacmanWorkContextHelper.getPropertyId()).and(CODE, code + "%").parameters());
    }

    public void saveOrEditAccomType(AccomType accomType) {
        if (null != accomType.getId()) {
            accomType = editEntityObject(accomType);
        }
        saveAccomType(accomType);
    }
    public void saveAccomTypesForLDB(AccomType accomType) {
        saveAccomType(accomType);
    }
    public AccomType convertToEntityObject(AccomTypeDTO accomTypeDTO) {
        AccomType accomType = new AccomType();
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        accomType.setAccomTypeCode(accomTypeDTO.getAccomTypCode());
        accomType.setName(accomTypeDTO.getAccomTypeName());
        accomType.setDescription(accomTypeDTO.getAccomTypeDescription());
        accomType.setAccomTypeCapacity(accomTypeDTO.getAccomTypeCapacity());
        accomType.setPropertyId(propertyId);
        AccomClass unassignedAccomClass = findUnassignedAccomClass(propertyId);
        accomType.setAccomClass(unassignedAccomClass);
        accomType.setStatusId(1);
        accomType.setSystemDefault(0);
        return accomType;
    }

    private AccomType editEntityObject(AccomType accomTypeNew) {
        AccomType accomType = getAccomTypeById(accomTypeNew.getId());
        accomType.setId(accomTypeNew.getId());
        accomType.setAccomTypeCode(accomTypeNew.getAccomTypeCode());
        accomType.setName(accomTypeNew.getName());
        accomType.setDescription(accomTypeNew.getDescription());
        accomType.setAccomTypeCapacity(accomTypeNew.getAccomTypeCapacity());
        return accomType;
    }

    public void saveAccomType(AccomType accomType) {
        ldbService.roomTypeMappingChanged(PacmanWorkContextHelper.getPropertyId());
        tenantCrudService.save(accomType);
    }

    public void saveAccomTypes(Set<AccomType> accomTypes) {
        ldbService.roomTypeMappingChanged(PacmanWorkContextHelper.getPropertyId());
        tenantCrudService.save(accomTypes);
    }

    @SuppressWarnings("unchecked")
    public List<AccomClass> getAllActiveNonDefaultAccomClassByViewOrder() {
        return tenantCrudService.findByNamedQuery(AccomClass.ALL_ACTIVE_NON_DEFAULT_NONEMPTY_BY_RANK_ORDER,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public boolean isAdvancedPriceRankingEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_ADVANCED_PRICE_RANKING_ENABLED.value());
    }

    protected void setAccomClassPriceRankService(AccomClassPriceRankService accomClassPriceRankService) {
        this.accomClassPriceRankService = accomClassPriceRankService;
    }

    public List<AccomClass> getAllActiveAccomClasses() {
        return tenantCrudService.findByNamedQuery(AccomClass.ALL,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public List<AccomClass> getAllActiveAccomClassesWithAccomTypes() {
        return tenantCrudService.findByNamedQuery(AccomClass.ALL_WITH_ACCOM_TYPE,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public List<AccomClass> getAllActiveAccomClasses(Integer propertyId) {
        return multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, AccomClass.ALL,
                QueryParameter.with(PROPERTY_ID, propertyId).parameters());
    }

    public Map<Integer, String> getAccomClassNameById() {
        return getAllActiveAccomClasses().stream().collect(toMap(AccomClass::getId, AccomClass::getName));
    }

    public String getAccomTypeCodeById(int id) {
        return tenantCrudService.findByNamedQuerySingleResult(AccomType.ACCOM_TYPE_CODE_BY_ACCOM_TYPE_ID,
                QueryParameter.with("id", id).parameters());
    }

    public String getAccomTypeNameById(int accomTypeId) {
        return tenantCrudService.findByNamedQuerySingleResult(AccomType.GET_ACCOM_TYPE_NAME,
                QueryParameter.with("id", accomTypeId).parameters());
    }

    public List<AccomClass> getRoomClassCapacityRatio() {
        List<Object[]> rows = tenantCrudService.findByNativeQuery(AccomClass.GET_ROOM_CLASS_CAPACITY_RATIO);
        return mapRowsToAccomClasses(rows);
    }

    @Transactional
    public void deleteAccomTypeData(int accomTypeId) {
        Arrays.stream(AccomTypePurgable.values())
                .forEachOrdered(accomTypePurgable -> purgeOneTableWithStoredProcedure(accomTypePurgable, accomTypeId));
    }

    private void purgeOneTableWithStoredProcedure(AccomTypePurgable accomTypePurgable, int accomTypeId) {
        if (accomTypePurgable == null) {
            return;
        }
        long start = System.currentTimeMillis();
        StoredProcedureQuery spQuery = tenantCrudService.getEntityManager().createNamedStoredProcedureQuery(CommandLog.DELETE_DATA_WITH_SP)
                .setParameter("DryRun", false)
                .setParameter("BatchSize", 5000)
                .setParameter("SchemaName", "dbo")
                .setParameter("TableName", accomTypePurgable.getTableName())
                .setParameter("WhereClause", accomTypePurgable.getWhereClause(accomTypeId))
                .setParameter("UseRowLockHint", false);

        spQuery.execute();

        Object result = null;
        for (Object o : spQuery.getResultList()) {
            if (o instanceof Object[]) {
                Object[] array = (Object[]) o;
                result = array[0];
            }
        }
        long elapsed = System.currentTimeMillis() - start;

        String debugInfo = String.format(
                "BatchSize %s for %s.%s with where clause of %s deleted in %s ms with a result of %s",
                5000,
                "dbo",
                accomTypePurgable.getTableName(),
                accomTypePurgable.getWhereClause(accomTypeId),
                elapsed,
                result);

        LOGGER.info(debugInfo);

        if (!"COMPLETE".equals(result)) {
            throw new TetrisException(String.format("Purge for %s failed where clause %s", accomTypePurgable.getTableName(), accomTypePurgable.getWhereClause(accomTypeId)));
        }
    }

    @ForTesting
    public List<String> getAllAccomTypeReferencesListedInAccomTypePurgable() {
        return Arrays.stream(AccomTypePurgable.values()).map(AccomTypePurgable::getTableName).collect(Collectors.toList());
    }

    /**
     * This is only for NGI testing purposes as this depends on other conditions to be true before this can run
     */
    public void deleteAllAccomTypes() {
        tenantCrudService.deleteAll(CostofWalkDefault.class);
        tenantCrudService.deleteAll(OverbookingAccom.class);
        tenantCrudService.deleteAll(AccomType.class);
    }

    @ForTesting
    @Transactional
    public void deleteAllAccomClasses() {
        List<AccomType> accomTypes = tenantCrudService.findAll(AccomType.class);

        for (AccomType accomType : accomTypes) {
            deleteAccomTypeData(accomType.getId());
        }

        tenantCrudService.deleteAll(AccomClassMinPriceDiffSeason.class);
        tenantCrudService.deleteAll(AccomClassMinPriceDiff.class);
        tenantCrudService.deleteAll(AccomClassPriceRankNetworkArrow.class);
        tenantCrudService.deleteAll(AccomClassPriceRank.class);
        tenantCrudService.deleteAll(AccomClass.class);
    }

    public void insertAccomTypeTestData() {
        AccomClass unassigned = new AccomClass();
        unassigned.setName(UNASSIGNED);
        unassigned.setCode(UNASSIGNED);
        unassigned.setDescription(UNASSIGNED);
        unassigned.setSystemDefault(1);
        unassigned.setStatusId(1);
        unassigned.setMasterClass(0);
        unassigned.setIsPriceRankConfigured(AccomClassPriceRankStatus.INCOMPLETE);
        unassigned.setExcludedForGroupEvaluation(false);
        unassigned.setPropertyId(10757);

        AccomType alpha = new AccomType();
        alpha.setName("Alpha");
        alpha.setAccomClass(unassigned);
        alpha.setAccomTypeCode("alpha");
        alpha.setSystemDefault(0);
        alpha.setRohType(0);
        alpha.setPropertyId(10757);
        alpha.setStatusId(1);
        alpha.setAccomTypeCapacity(50);

        AccomType bravo = new AccomType();
        bravo.setName("Bravo");
        bravo.setAccomClass(unassigned);
        bravo.setAccomTypeCode("bravo");
        bravo.setSystemDefault(0);
        bravo.setRohType(0);
        bravo.setPropertyId(10757);
        bravo.setStatusId(1);
        bravo.setAccomTypeCapacity(100);

        AccomType charlie = new AccomType();
        charlie.setName("Charlie");
        charlie.setAccomClass(unassigned);
        charlie.setAccomTypeCode("charlie");
        charlie.setSystemDefault(0);
        charlie.setRohType(0);
        charlie.setPropertyId(10757);
        charlie.setStatusId(1);
        charlie.setAccomTypeCapacity(150);

        AccomType delta = new AccomType();
        delta.setName("Delta");
        delta.setAccomClass(unassigned);
        delta.setAccomTypeCode("delta");
        delta.setSystemDefault(0);
        delta.setRohType(0);
        delta.setPropertyId(10757);
        delta.setStatusId(1);
        delta.setAccomTypeCapacity(200);

        tenantCrudService.save(unassigned);

        tenantCrudService.save(Arrays.asList(alpha, bravo, charlie, delta));
    }

    @ForTesting
    public void terraneaRoomClassConfiguration(int propertyId) {
        tenantCrudService.executeUpdateByNativeQuery("update Accom_Type set Accom_Class_ID=(Select Accom_Class_ID from Accom_Class where Accom_Class_Name='componentROR') where Accom_type_Name = 'C0RK'");
        tenantCrudService.executeUpdateByNativeQuery("update Accom_Type set display_Status_ID = 2 where Accom_Type_Name = 'C0RK'");
        tenantCrudService.executeUpdateByNativeQuery("update Accom_Type set Accom_Class_ID=(Select Accom_Class_ID from Accom_Class where Accom_Class_Name='componentROR') where Accom_type_Name = 'ROR'");
        tenantCrudService.executeUpdateByNativeQuery("update Accom_Type set Accom_Class_ID=(Select Accom_Class_ID from Accom_Class where Accom_Class_Name='componentSPA') where Accom_type_Name = 'SPA'");
        tenantCrudService.executeUpdateByNativeQuery("update Accom_Type set Accom_Class_ID=(Select Accom_Class_ID from Accom_Class where Accom_Class_Name='componentB2VKD') where Accom_type_Name = 'B2VKD'");
        tenantCrudService.executeUpdateByNativeQuery("update Accom_Type set Accom_Class_ID=(Select Accom_Class_ID from Accom_Class where Accom_Class_Name='componentC2SKD') where Accom_type_Name = 'C2SKD'");
        tenantCrudService.executeUpdateByNativeQuery("update Accom_Type set Accom_Class_ID=(Select Accom_Class_ID from Accom_Class where Accom_Class_Name='componentC3SKM') where Accom_type_Name = 'C3SKM'");
        tenantCrudService.executeUpdateByNativeQuery("update Accom_Type set Accom_Class_ID=(Select Accom_Class_ID from Accom_Class where Accom_Class_Name='componentC2RKD') where Accom_type_Name = 'C2RKD'");
        tenantCrudService.executeUpdateByNativeQuery("update Accom_Type set Accom_Class_ID=(Select Accom_Class_ID from Accom_Class where Accom_Class_Name='componentB2VKK') where Accom_type_Name = 'B2VKK'");
        tenantCrudService.executeUpdateByNativeQuery("update Accom_Type set Accom_Class_ID=(Select Accom_Class_ID from Accom_Class where Accom_Class_Name='componentC2VKD') where Accom_type_Name = 'C2VKD'");
        tenantCrudService.executeUpdateByNativeQuery("update Accom_Type set Accom_Class_ID=(Select Accom_Class_ID from Accom_Class where Accom_Class_Name='componentC3VKM') where Accom_type_Name = 'C3VKM'");
        tenantCrudService.executeUpdateByNativeQuery("update Accom_Type set Accom_Class_ID=(Select Accom_Class_ID from Accom_Class where Accom_Class_Name='componentC3RKM') where Accom_type_Name = 'C3RKM'");
        tenantCrudService.executeUpdateByNativeQuery("update Accom_Type set Accom_Class_ID=(Select Accom_Class_ID from Accom_Class where Accom_Class_Name='noncomponentRC') where Accom_type_Name = 'C1SKS'");
        tenantCrudService.executeUpdateByNativeQuery("update Accom_Type set Accom_Class_ID=(Select Accom_Class_ID from Accom_Class where Accom_Class_Name='noncomponentRC') where Accom_type_Name = 'C0SD'");
        tenantCrudService.executeUpdateByNativeQuery("update Accom_Type set Accom_Class_ID=(Select Accom_Class_ID from Accom_Class where Accom_Class_Name='noncomponentRC') where Accom_type_Name = 'L0SD'");
        tenantCrudService.executeUpdateByNativeQuery("update Accom_Type set Accom_Class_ID=(Select Accom_Class_ID from Accom_Class where Accom_Class_Name='noncomponentRC') where Accom_type_Name = 'C0RD'");
    }

    public List<AccomType> getAllActiveAccomTypesWithCapacityForBudget() {
        return tenantCrudService.findByNamedQuery(AccomType.ALL_ACTIVE_VALID_CAPACITY_WITHOUT_ACCOM_CLASS, QueryParameter.with(PROPERTY_ID,
                PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public void deleteAccomTypeById(Integer accomTypeId) {
        AccomType accomType = tenantCrudService.findByNamedQuerySingleResult(AccomType.ALL_BY_ACCOM_TYPE_ID, QueryParameter.with("id", accomTypeId).parameters());
        Set<AccomType> accomTypes = new HashSet<>();
        accomTypes.add(accomType);

        tenantCrudService.executeUpdateByNativeQuery("delete from Accom_Type_AUD where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from Post_departure_revenue where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from Accom_Type_Supplement_AUD where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from CostofWalk_Default_AUD where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from CostofWalk_Season_AUD where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from CP_Cfg_AC_AUD where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from CP_Cfg_Base_AT_AUD where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from CP_Cfg_Offset_AT_AUD where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from CR_Out_Of_Order_AUD where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from Daily_Bar_Config_AUD where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from Overbooking_Accom_AUD where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from Overbooking_Accom_Season_AUD where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from Rate_Qualified_Details_AUD where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from Rate_Unqualified_Details_AUD where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from CP_Cfg_AC where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from CP_Cfg_Base_AT where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from CP_Cfg_Base_AT_Draft where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from CR_Out_Of_Order where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from Max_Allowed_Occupancy_AT where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from Max_Allowed_Occupancy_AT_AUD where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from Product_AT where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from Product_AT_AUD where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from LDB_Hybrid_Accom_Type where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from CP_Recommended_Floor_Ceiling where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from ES_LongLos_Price_Cfg where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from ES_LongLos_Price_Cfg_AUD where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from ES_Recommended_Price where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from PACE_Manual_Restriction_Accom_OVR where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from Manual_Restriction_Accom_OVR where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from Accom_Class_Proposed where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from PROFIT_ADJ_FCST where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from MVCR_RATE_AT where Accom_Type_ID = " + accomTypeId);
        tenantCrudService.executeUpdateByNativeQuery("delete from MVCR_RATE_AT_AUD where Accom_Type_ID = " + accomTypeId);

        tenantCrudService.executeUpdateByNamedQuery(RevenueStreamDetail.DELETE_BY_ACCOM_TYPE_ID, QueryParameter.with("accomTypeId", accomTypeId).parameters());

        tenantCrudService.executeUpdateByNamedQuery(AccomTypeSupplement.DELETE_BY_ACCOM_TYPE_ID, QueryParameter.with("accomType", accomType).parameters());
        tenantCrudService.executeUpdateByNamedQuery(CPConfigOffsetAccomType.DELETE_BY_ACCOM_TYPES, QueryParameter.with("accomTypes", accomTypes).and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
        tenantCrudService.executeUpdateByNamedQuery(CRAccomTypeMapping.DELETE_BY_ACCOM_TYPE_ID, QueryParameter.with(ACCOM_TYPE_ID, accomTypeId).parameters());
        tenantCrudService.executeUpdateByNamedQuery(DailyBarConfig.DELETE_BY_ACCOM_TYPE_ID, QueryParameter.with(ACCOM_TYPE_ID, accomTypeId).parameters());
        tenantCrudService.executeUpdateByNamedQuery(ExtendedStayProductDefinition.DELETE_BY_ACCOM_TYPE_ID, QueryParameter.with(ACCOM_TYPE_ID, accomTypeId).parameters());
        tenantCrudService.executeUpdateByNamedQuery(RateUnqualifiedDetails.DELETE_BY_ACCOM_TYPE_ID, QueryParameter.with(ACCOM_TYPE_ID, accomTypeId).parameters());
        tenantCrudService.executeUpdateByNamedQuery(RateQualifiedDetails.DELETE_BY_ACCOM_TYPE_ID, QueryParameter.with(ACCOM_TYPE_ID, accomTypeId).parameters());
        tenantCrudService.executeUpdateByNamedQuery(CostofWalkSeason.DELETE_BY_ACCOM_TYPE_ID, QueryParameter.with(ACCOM_TYPE_ID, accomTypeId).parameters());
        tenantCrudService.executeUpdateByNamedQuery(CostofWalkDefault.DELETE_BY_ACCOM_TYPE_ID, QueryParameter.with(ACCOM_TYPE_ID, accomTypeId).parameters());
        tenantCrudService.executeUpdateByNamedQuery(OverbookingAccomSeason.DELETE_BY_ACCOM_TYPE_ID, QueryParameter.with(ACCOM_TYPE_ID, accomTypeId).parameters());
        tenantCrudService.executeUpdateByNamedQuery(OverbookingAccom.DELETE_BY_ACCOM_TYPE_ID, QueryParameter.with(ACCOM_TYPE_ID, accomTypeId).parameters());
        tenantCrudService.executeUpdateByNamedQuery(AccomType.DELETE_ACCOM_TYPE_BY_ID, QueryParameter.with("id", accomTypeId).parameters());
        tenantCrudService.executeUpdateByNamedQuery(OutOfOrderOverride.DELETE_BY_ACCOM_TYPE_ID, QueryParameter.with("accomType", accomType).parameters());
        ldbService.roomTypeMappingChanged(PacmanWorkContextHelper.getPropertyId());
    }

    public AccomType getAccomTypeById(Integer id) {
        return (AccomType) tenantCrudService.findByNamedQuery(AccomType.ALL_BY_ACCOM_TYPE_ID, QueryParameter.with("id", id).parameters()).get(0);
    }

    public void testSetupForTestBenchCRUpgradeTest(int propertyId) {
        tenantCrudService.executeUpdateByNativeQuery("update Accom_Type set Accom_Class_ID = 5 where Accom_Type_ID in(1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,20,21,22,23,24,25,26,27,28,35,37,38,39,40,41,42,43) " +
                " update Accom_Type set Accom_Class_ID = 14,Accom_Type_Name='SPA',Accom_Type_Code='SPA',Accom_Type_Description='SPA' where Accom_Type_ID in(17) " +
                " update Accom_Type set Accom_Class_ID = 13 where Accom_Type_ID in(18) " +
                " update Accom_Type set Accom_Class_ID = 9 where Accom_Type_ID in(19) " +
                " update Accom_Type set Accom_Class_ID = 10 where Accom_Type_ID in(29) " +
                " update Accom_Type set Accom_Class_ID = 12 where Accom_Type_ID in(30) " +
                " update Accom_Type set Accom_Class_ID = 8 where Accom_Type_ID in(31) " +
                " update Accom_Type set Accom_Class_ID = 6 where Accom_Type_ID in(32) " +
                " update Accom_Type set Accom_Class_ID = 11 where Accom_Type_ID in(33) " +
                " update Accom_Type set Accom_Class_ID = 7 where Accom_Type_ID in(34) " +
                " update Accom_Type set Accom_Class_ID = 15 where Accom_Type_ID in(36) " +
                " update Accom_Class set status_id =2 where Accom_Class_ID > 15 " +
                " update Accom_Class set View_Order = 1,Accom_Class_Name = 'STD', Accom_Class_Code ='STD', Accom_Class_Description ='STD',status_id =2, Master_Class=0, Price_Rank_Is_Complete =0, Rank_Order = 1 where Accom_Class_ID = 2 " +
                " update Accom_Class set View_Order = 2,Accom_Class_Name = 'DLX', Accom_Class_Code ='DLX', Accom_Class_Description ='DLX',status_id =2, Master_Class=0, Price_Rank_Is_Complete =0, Rank_Order = 2 where Accom_Class_ID = 3 " +
                " update Accom_Class set View_Order = 1,Accom_Class_Name = 'componentRC', Accom_Class_Code ='componentRC', Accom_Class_Description ='componentRC',status_id =2, Master_Class=0, Price_Rank_Is_Complete =0, Rank_Order = 1 where Accom_Class_ID = 4 " +
                " update Accom_Class set View_Order = 1,Accom_Class_Name = 'noncomponentRC', Accom_Class_Code ='noncomponentRC', Accom_Class_Description ='noncomponentRC', Master_Class=1, Price_Rank_Is_Complete =1, Rank_Order = 2 where Accom_Class_ID = 5 " +
                " update Accom_Class set View_Order = 2,Accom_Class_Name = 'componentC3RKM', Accom_Class_Code ='componentC3RKM', Accom_Class_Description ='componentC3RKM', Master_Class=0, Price_Rank_Is_Complete =1, Rank_Order = 2 where Accom_Class_ID = 6 " +
                " update Accom_Class set View_Order = 3,Accom_Class_Name = 'componentC3VKM', Accom_Class_Code ='componentC3VKM', Accom_Class_Description ='componentC3VKM', Master_Class=0, Price_Rank_Is_Complete =1, Rank_Order = 3 where Accom_Class_ID = 7 " +
                " update Accom_Class set View_Order = 4,Accom_Class_Name = 'componentC2VKD', Accom_Class_Code ='componentC2VKD', Accom_Class_Description ='componentC2VKD', Master_Class=0, Price_Rank_Is_Complete =1, Rank_Order = 4 where Accom_Class_ID = 8 " +
                " update Accom_Class set View_Order = 5,Accom_Class_Name = 'componentB2VKK', Accom_Class_Code ='componentB2VKK', Accom_Class_Description ='componentB2VKK', Master_Class=0, Price_Rank_Is_Complete =1, Rank_Order = 5 where Accom_Class_ID = 9 " +
                " update Accom_Class set View_Order = 6,Accom_Class_Name = 'componentC2RKD', Accom_Class_Code ='componentC2RKD', Accom_Class_Description ='componentC2RKD', Master_Class=0, Price_Rank_Is_Complete =1, Rank_Order = 6 where Accom_Class_ID = 10 " +
                " update Accom_Class set View_Order = 7,Accom_Class_Name = 'componentC3SKM', Accom_Class_Code ='componentC3SKM', Accom_Class_Description ='componentC3SKM', Master_Class=0, Price_Rank_Is_Complete =1, Rank_Order = 7 where Accom_Class_ID = 11 " +
                " update Accom_Class set View_Order = 8,Accom_Class_Name = 'componentC2SKD', Accom_Class_Code ='componentC2SKD', Accom_Class_Description ='componentC2SKD', Master_Class=0, Price_Rank_Is_Complete =1, Rank_Order = 8 where Accom_Class_ID = 12 " +
                " update Accom_Class set View_Order = 9,Accom_Class_Name = 'componentB2VKD', Accom_Class_Code ='componentB2VKD', Accom_Class_Description ='componentB2VKD', Master_Class=0, Price_Rank_Is_Complete =1, Rank_Order = 9 where Accom_Class_ID = 13 " +
                " update Accom_Class set View_Order = 10,Accom_Class_Name = 'componentSPA', Accom_Class_Code ='componentSPA', Accom_Class_Description ='componentSPA', Master_Class=0, Price_Rank_Is_Complete =1, Rank_Order = 10 where Accom_Class_ID = 14 " +
                " update Accom_Class set View_Order = 11,Accom_Class_Name = 'componentROR', Accom_Class_Code ='componentROR', Accom_Class_Description ='componentROR', Master_Class=0, Price_Rank_Is_Complete =1, Rank_Order = 11 where Accom_Class_ID = 15 " +
                " update CR_Accom_Type_Mapping set CP_Accom_Type_Quantity=2 where CR_Accom_Type_ID = 30 and CP_Accom_Type_ID = 22 " +
                " update CR_Accom_Type_Mapping set CP_Accom_Type_Quantity=2 where CR_Accom_Type_ID = 30 and CP_Accom_Type_ID = 27 " +
                " update CR_Accom_Type_Mapping set CP_Accom_Type_ID = 22,CP_Accom_Type_Quantity=2 where CR_Accom_Type_ID = 34 and CP_Accom_Type_ID = 25 " +
                " update CR_Accom_Type_Mapping set CP_Accom_Type_ID = 23,CP_Accom_Type_Quantity=2 where CR_Accom_Type_ID = 34 and CP_Accom_Type_ID = 31 " +
                " delete from CR_Accom_Type_Mapping where CR_Accom_Type_ID = 34 and CP_Accom_Type_ID = 28 " +
                " insert into CR_Accom_Type_Mapping values(11007,34,28,2) " +
                " update Accom_Type set accom_type_capacity =1 where Accom_Type_Name='ROR' ");
    }

    @ForTesting
    public void setAccommodationMappingService(AccommodationMappingService accommodationMappingService) {
        this.accommodationMappingService = accommodationMappingService;
    }

    public Map<Integer, List<AccomType>> getAccomTypesByAccomClass(List<Integer> accomClassIds) {
        Map<Integer, List<AccomType>> accomTypeMap = new HashMap<>();
        for (Integer accomClassId : accomClassIds) {
            List<AccomType> accomTypeList = getAccomTypes(accomClassId);
            accomTypeMap.put(accomClassId, accomTypeList);
        }
        return accomTypeMap;
    }

    public Map<Integer, List<AccomType>> getDisplayableAccomTypesByAccomClass(List<Integer> accomClassIds, Integer displayStatusId) {
        Map<Integer, List<AccomType>> accomTypeMap = new HashMap<>();
        for (Integer accomClassId : accomClassIds) {
            List<AccomType> accomTypeList = getAccomTypesBy(accomClassId, displayStatusId);
            accomTypeMap.put(accomClassId, accomTypeList);
        }
        return accomTypeMap;
    }

    public List<AccomType> getAccomTypes(Integer accomClassId) {
        return tenantCrudService.findByNamedQuery(AccomType.BY_ACCOM_CLASS_ID,
                QueryParameter.with("id", accomClassId).parameters());
    }

    public List<AccomTypeDto> getAccomTypeDtos(Integer propertyId, String pseudoRooms) {
        List<String> roomTypes = Arrays.stream(pseudoRooms.split(",")).map(String::trim).collect(Collectors.toList());
        List<Object[]> accomTypes = getAccomCapacities(propertyId, roomTypes);
        List<AccomTypeDto> accomTypesToBeDisplayed = new ArrayList<>();

        addAccomTypesPresentInDbToBeDisplayed(accomTypes, accomTypesToBeDisplayed);
        return accomTypesToBeDisplayed;
    }

    private void addAccomTypesPresentInDbToBeDisplayed(List<Object[]> accomTypes, List<AccomTypeDto> accomTypesToBeDisplayed) {
        for (Object[] accomType : accomTypes) {
            AccomTypeDto dto = new AccomTypeDto(accomType[0].toString(), (BigDecimal) accomType[1]);
            accomTypesToBeDisplayed.add(dto);
        }
    }

    private List<Object[]> getAccomCapacities(Integer propertyId, List<String> pseudoRooms) {
        return multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, AccomType.GET_ROOM_TYPE_CAPACITY_BY_CODE,
                QueryParameter.with("accomTypeCodes", pseudoRooms).parameters());
    }

    public Set findRealPseudoRoomTypes(Set ngiPseudoRoomType) {
        final List<AccomType> accomTypeList = tenantCrudService.findByNamedQuery(AccomType.ALL_BY_CODES, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                .and("accomTypeList", ngiPseudoRoomType).parameters());
        final List<String> accomTypeCodes = accomTypeList.stream().map(accomType -> accomType.getAccomTypeCode()).collect(Collectors.toList());
        ngiPseudoRoomType.removeAll(accomTypeCodes);
        return ngiPseudoRoomType;
    }

    public List<AccomType> getAccomTypesBy(Integer accomClassId, Integer displayStatusId) {
        return tenantCrudService.findByNamedQuery(AccomType.BY_ACCOM_CLASS_ID_AND_DISPLAY_STATUS,
                QueryParameter.with(ACCOM_CLASS_ID, accomClassId).and("displayStatusId", displayStatusId).parameters());
    }

    public List<AccomClass> getRoomClassCapacityRatioForAccomTypes(List<Integer> accomTypeIds) {
        List<Object[]> rows = tenantCrudService.findByNativeQuery(AccomClass.GET_ROOM_CLASS_CAPACITY_RATIO_FOR_ACCOM_TYPES, QueryParameter.with("accomTypeIds", accomTypeIds).parameters());
        return mapRowsToAccomClasses(rows);
    }

    private List<AccomClass> mapRowsToAccomClasses(List<Object[]> rows) {
        List<AccomClass> accomClassCapacityRatio = new ArrayList<>();
        if (isNotEmpty(rows)) {
            rows.stream().forEach(row -> {
                AccomClass accomClass = new AccomClass();
                accomClass.setPropertyId((Integer) row[0]);
                accomClass.setId((Integer) row[1]);
                accomClass.setCapacity(((BigDecimal) row[2]).intValueExact());
                accomClass.setCapacityRatio((BigDecimal) row[4]);
                accomClassCapacityRatio.add(accomClass);
            });
        }
        return accomClassCapacityRatio;
    }

    public List<Integer> getAccomTypesForAccomClass(Integer accomClassId) {
        return tenantCrudService
                .findByNamedQuery(AccomType.ACTIVE_ACCOM_TYPES_BY_ACCOM_CLASS_ID,
                        QueryParameter.with("accomClassId", accomClassId).parameters());
    }

    public void populateAccomTypeADR() {
        tenantCrudService.executeUpdateByNamedQuery(AccomTypeCodeADR.POPULATE_ACCOM_TYPE_ADR_USING_RESERVATION_NIGHT,
                QueryParameter.with(AccomTypeCodeADR.PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and(AccomTypeCodeADR.TOTAL_RATE_ENABLED, getTotalRateEnabledValue()).parameters());
    }

    public AccomType getAccomTypeByCode(String roomTypeCode) {
        return tenantCrudService.findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with(CODE, roomTypeCode).parameters());
    }

    public List<AccomType> getAccomTypeByCodes(Set<String> roomTypeCodes) {
        return tenantCrudService.findByNamedQuery(AccomType.BY_CODE_IN, QueryParameter.with(CODES,
                roomTypeCodes).parameters());
    }

    public AccomClass getAccomClassByCode(String code) {
        return tenantCrudService.findByNamedQuerySingleResult(AccomClass.BY_CODE, QueryParameter.with(CODE, code).and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public AccomClass findAccomClass(Integer propertyId, String name) {
        return (AccomClass) tenantCrudService.findByNamedQuerySingleResult(AccomClass.BY_NAME, QueryParameter.with(PROPERTY_ID, propertyId).and("name", name).parameters());
    }

    public Map<Integer, BigDecimal> getCapacityByRoomClass() {
        List<Object[]> resultsList = tenantCrudService.findByNamedQuery(AccomType.CAPACITY_BY_ROOM_CLASS);
        return resultsList.stream().collect(toMap(a -> (Integer) a[0], a -> (BigDecimal) a[1]));
    }

    public void updateROHGroupEvaluationExclusionFlagFor(AccomClass accomClass) {
        AccomClass accomClassToUpdate = tenantCrudService.find(AccomClass.class, accomClass.getId());
        accomClassToUpdate.setExcludedForGroupEvaluation(accomClass.isExcludedForGroupEvaluation());
        saveAccomClass(accomClassToUpdate);
    }

    public List<CapacityByAccomClassDto> getCapacityByAccomClass() {
        List<Object[]> result = tenantCrudService.findByNamedQuery(AccomClass.GET_CAPACITY_BY_ACCOM_CLASS);
        return result.stream().map(o -> new CapacityByAccomClassDto((Integer) o[0], (String) o[1], (BigDecimal) o[2])).collect(Collectors.toList());
    }

    public List<Product> getAllProductsExcludingBar() {
        return tenantCrudService.findByNamedQuery(Product.GET_AGILE_RATES_INDEPENDENT_GROUP_PRODUCTS_BY_STATUS,
                QueryParameter.with("status", TenantStatusEnum.ACTIVE).parameters());
    }

    public List<ProductAccomType> getProductAccomTypes() {
        return tenantCrudService.findAll(ProductAccomType.class);
    }

    public void saveProductAccomTypes(List<ProductAccomType> productAccomTypes) {
        List<ProductAccomType> existingProductAccomTypes = tenantCrudService.findAll(ProductAccomType.class);
        productAccomTypes = productAccomTypes.stream().filter(productAccomType -> existingProductAccomTypes.stream()
                        .noneMatch(productAccomType1 -> productAccomType.getAccomType().getId()
                                .equals(productAccomType1.getAccomType().getId())
                                && productAccomType.getProduct().getId()
                                .equals(productAccomType1.getProduct().getId())))
                .collect(Collectors.toList());
        tenantCrudService.save(productAccomTypes);
    }

    public Product getBARProduct() {
        return tenantCrudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
    }

    public Collection<AccomType> getNewAccomTypes(Set<String> notExistingAccomTypesCodes, int propertyId,
                                                  Integer statusId, Integer displayStatusId) {
        Set<AccomType> newAccomTypes = notExistingAccomTypesCodes.stream()
                .map(ac -> getAccomType(propertyId, ac, statusId, displayStatusId))
                .collect(Collectors.toSet());
        return tenantCrudService.save(newAccomTypes);
    }

    public List<AccomType> getOrCreateAccomTypesByCodes(Set<String> accomCodes, int propertyId, List<String> pseudoRoomsTypes) {
        List<AccomType> accomTypes = getAccomTypeByCodes(accomCodes);
        Set<String> notExistingRegularAccomCodes = new HashSet<>();
        Set<String> notExistingPseudoAccomCodes = new HashSet<>();
        checkAndSortNotExistingAccomCodes(accomCodes, accomTypes, notExistingRegularAccomCodes,
                notExistingPseudoAccomCodes, pseudoRoomsTypes);
        if (CollectionUtils.isNotEmpty(notExistingRegularAccomCodes)) {
            accomTypes.addAll(getNewAccomTypes(notExistingRegularAccomCodes, propertyId, Constants.ACTIVE_STATUS_ID,
                    Constants.ACTIVE_DISPLAY_STATUS_ID));
        }
        if (CollectionUtils.isNotEmpty(notExistingPseudoAccomCodes)) {
            accomTypes.addAll(getNewAccomTypes(notExistingPseudoAccomCodes, propertyId, Constants.PSEUDO_ROOM_STATUS_ID,
                    Constants.PSEUDO_ROOM_DISPLAY_STATUS_ID));
        }
        return accomTypes;
    }

    protected boolean hasRoomClassNameChange(List<AccomClass> accomClasses, List<AccomClass> allExistingAccomClasses) {
        // Skip if no existing room classes or if room classes have been added or removed
        if (allExistingAccomClasses == null || allExistingAccomClasses.isEmpty() || accomClasses.size() != allExistingAccomClasses.size()) {
            return false;
        }

        Set<String> existingRoomClassNames = allExistingAccomClasses.stream()
                .map(AccomClass::getName)
                .collect(Collectors.toSet());

        // Check if any updated accommodation class name is not in the set of existing names
        return accomClasses.stream()
                .map(AccomClass::getName)
                .anyMatch(name -> !existingRoomClassNames.contains(name));
    }

    private void checkAndSortNotExistingAccomCodes(Set<String> accomCodes, List<AccomType> accomTypes, Set<String> notExistingRegularAccomCodes, Set<String> notExistingPseudoAccomCodes, List<String> pseudoRoomsTypes) {
        // checking if some of accomCodes not existing in the system and split regular and pseudo ones
        accomCodes.stream()
                .filter(ac -> !accomTypes.stream()
                        .map(AccomType::getAccomTypeCode)
                        .collect(Collectors.toSet()).contains(ac))
                .forEach(notExistingAccomCode -> {
                    if (pseudoRoomsTypes.contains(notExistingAccomCode)) {
                        notExistingPseudoAccomCodes.add(notExistingAccomCode);
                    } else {
                        notExistingRegularAccomCodes.add(notExistingAccomCode);
                    }
                });
    }

    public boolean isCentralRmsAvailable() {
        return SystemConfig.isCentralRmsRoomClassSyncEnabled() && configParamsService.getBooleanParameterValue(CENTRAL_RMS_AVAILABLE);
    }

    @ForTesting
    protected void setCentralRmsService(CentralRmsService centralRmsService) {
        this.centralRmsService = centralRmsService;
    }

    public int recodeAccomTypeForProperty() {
        String updateQuery =
                "UPDATE at " +
                        "SET at.Accom_Type_Code        = map.New_Equivalent_Code, " +
                        "    at.Accom_Type_Name        = map.New_Equivalent_Code, " +
                        "    at.Accom_Type_Description = map.New_Equivalent_Code " +
                        "FROM accom_type at " +
                        "INNER JOIN PMS_Migration_Mapping map " +
                        "ON at.Accom_Type_Code = map.Current_Code";
        return tenantCrudService.executeUpdateByNativeQuery(updateQuery);
    }

    public void updateAccomType(String roomName, PropertyConfigurationDto propertyConfig) {
        RoomTypePropertyConfigurationDto roomType = (RoomTypePropertyConfigurationDto) propertyConfig;
        AccomType accomType = getAccomTypeByCode(roomType.getRoomTypeCode());
        AccomClass accomClass = findAccomClass(PacmanWorkContextHelper.getPropertyId(), roomType.getRoomClassName());

        if (Objects.nonNull(accomClass) && Objects.nonNull(accomType)) {
            accomType.setAccomClass(accomClass);
            accomType.setName(roomName);
            accomType.setDescription(roomType.getRoomTypeDescription());
            tenantCrudService.save(accomType);
        }
    }

    public List<AccomType> mapAccomTypeToUnassignedAccomClass(List<AccomType> roomTypes) {
        if (isEmpty(roomTypes)) {
            return Collections.emptyList();
        }
        List<AccomType> accomTypesToSave = new ArrayList<>();
        AccomClass accomClass = getAccomClassByCode(PacmanWorkContextHelper.getPropertyId(), Constants.UNASSIGNED);
        for (AccomType accomType : roomTypes) {
            accomType.setAccomClass(accomClass);
            accomTypesToSave.add(accomType);
        }
        Collection<AccomType> savedAccomTypes = tenantCrudService.save(accomTypesToSave);
        return new ArrayList<>(savedAccomTypes);
    }

    public AccomClass getAccomClassByCode(Integer propertyId, String code) {
        return tenantCrudService.findByNamedQuerySingleResult(AccomClass.BY_CODE,
                QueryParameter.with(PROPERTY_ID, propertyId).and("code", code)
                        .parameters());
    }

    public List<AccomClass> getAccomClassesByNames(Set<String> names) {
        return tenantCrudService.findByNamedQuery(AccomClass.BY_NAMES_IN,
                QueryParameter.with("names", names).and("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }
    public List<AccomType> getAccomTypesByNames(Set<String> names) {
        return tenantCrudService.findByNamedQuery(AccomType.BY_NAMES_IN,
                QueryParameter.with("names", names).and("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public void createAccomClass(PropertyConfigurationDto propertyConfigurationDto, Integer propertyId) {
        RoomClassPropertyConfigurationDto roomClassPropertyConfigDto = (RoomClassPropertyConfigurationDto) propertyConfigurationDto;
        final int maxViewOrder = findAll().stream()
                .filter(accomClass -> accomClass.getViewOrder() != null)
                .filter(accomClass -> !Objects.equals(accomClass.getStatusId(),Status.INACTIVE.getId()))
                .mapToInt(AccomClass::getViewOrder)
                .max()
                .orElse(0);
        AccomClass accomClass = new AccomClass();
        accomClass.setPropertyId(propertyId);
        accomClass.setCode(roomClassPropertyConfigDto.getRoomClassName());
        accomClass.setName(roomClassPropertyConfigDto.getRoomClassName());
        accomClass.setStatusId(Constants.ACTIVE_STATUS_ID);
        accomClass.setSystemDefault(0);
        accomClass.setDescription(roomClassPropertyConfigDto.getRoomClassDescription());
        accomClass.setMasterClass(BooleanUtils.toIntegerObject(roomClassPropertyConfigDto.isMasterAccommodationClass()));
        accomClass.setViewOrder(maxViewOrder + 1);
        accomClass.setRankOrder(maxViewOrder + 1);
        LOGGER.info("Creating AccomClass: " + roomClassPropertyConfigDto.getRoomClassName() + " for Property: " + propertyConfigurationDto.getPropertyCode());
        tenantCrudService.save(accomClass);
    }

    public void save(List<AccomTypeDTO> accomTypeDTO) {
        for(AccomTypeDTO accomTypeDTOS: accomTypeDTO) {
            saveAccomTypesForLDB(convertToEntityObject(accomTypeDTOS));
        }
    }

    @FunctionalInterface
    public interface NameExtractor<T> {
        String getName(T item);
    }

    public List<AccomClass> getActiveAccomClasses() {
        return tenantCrudService.findByNamedQuery(
                AccomClass.ALL_ACS_BY_SYSTEMDEFAULT_PROPERTY_ID_STATUS_ID, QueryParameter.with("systemDefault", 0)
                        .and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and("statusId", 1).parameters());
    }

    public List<AccomClassDTO> getActiveAccomClassesDTO() {
        List<AccomClass> accomClasses = getActiveAccomClasses();
        return accomClasses.stream().map(this::mapToAccomClassDTO).collect(Collectors.toList());
    }

    public List<AccomClassDetailsDTO> getActiveAccomClassesDetails() {
        List<AccomType> roomTypesList = centralRMSRoomTypeRepository.getActiveRoomTypesWithBaseRoomTypeInfoWithActiveDisplayStatusAndActiveRoomClass();
        Set<AccomClass> accomClasses = roomTypesList.stream()
                .map(AccomType::getAccomClass)
                .collect(Collectors.toSet());

        return accomClasses.stream()
                .map(this::mapToAccomClassDetailsDTO)
                .collect(Collectors.toList());
    }

    private AccomClassDTO mapToAccomClassDTO(AccomClass accomClass) {
        AccomClassDTO accomClassDTO = new AccomClassDTO();
        accomClassDTO.setId(accomClass.getId());
        accomClassDTO.setName(accomClass.getName());
        accomClassDTO.setCode(accomClass.getCode());
        accomClassDTO.setDescription(accomClass.getDescription());
        accomClassDTO.setExcludedForGroupEvaluation(accomClass.isExcludedForGroupEvaluation());
        accomClassDTO.setIsPriceRankConfigured(accomClass.getIsPriceRankConfigured());
        accomClassDTO.setMappedAccomTypes(accomClass.getAccomTypes().stream().map(AccomType::getName).collect(Collectors.toList()));
        return accomClassDTO;
    }

    public List<AccomType> getBaseAccomTypesForActiveAccomClasses() {
        return tenantCrudService.findByNamedQuery(PricingAccomClass.FIND_BASE_ACCOM_TYPE_FOR_ACCOM_CLASSES);
    }

    private AccomClassDetailsDTO mapToAccomClassDetailsDTO(AccomClass accomClass) {
        AccomClassDetailsDTO accomClassDetailsDTO = new AccomClassDetailsDTO();
        Set<AccomType> accomTypes = accomClass.getAccomTypes();
        accomClassDetailsDTO.setId(accomClass.getId());
        accomClassDetailsDTO.setMasterRoomClass(accomClass.getMasterClassBoolean());
        accomClassDetailsDTO.setBaseRoomType(accomTypes.stream().filter(x->x.isBaseRoomType()).map(AccomType::getName).findFirst().orElseGet(()->EMPTY_STRING));
        accomClassDetailsDTO.setName(accomClass.getName());
        accomClassDetailsDTO.setCode(accomClass.getCode());
        accomClassDetailsDTO.setDescription(accomClass.getDescription());
        accomClassDetailsDTO.setExcludedForGroupEvaluation(accomClass.isExcludedForGroupEvaluation());
        accomClassDetailsDTO.setIsPriceRankConfigured(accomClass.getIsPriceRankConfigured());
        accomClassDetailsDTO.setMappedAccomTypes(accomTypes.stream().map(AccomType::getName).collect(Collectors.toList()));
        return accomClassDetailsDTO;
    }

    public BigDecimal getOccupancyPercentage(LocalDate occupancyDate){
        return tenantCrudService.findByNativeQuerySingleResult("exec dbo.usp_getOccupancy_Fcst_byOccupancyDate :occupancyDate",
                QueryParameter.with("occupancyDate", occupancyDate).parameters());
    }

    public List<Product> getActiveGroupProducts() {
        return tenantCrudService.findByNamedQuery(Product.GET_SMALL_GROUP_PRODUCTS_BY_STATUS, QueryParameter.with("status", TenantStatusEnum.ACTIVE).parameters());
    }
}
