package com.ideas.tetris.pacman.services.grouppricing.evaluation.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.configparams.SyncConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.auditreader.AuditReaderService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.MktSegAccomActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyDateCapacity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.RoomTypeOccupancyDateCapacityDetails;
import com.ideas.tetris.pacman.services.bestavailablerate.rowmapper.OccupancyDateCapacityRowMapper;
import com.ideas.tetris.pacman.services.bestavailablerate.rowmapper.RoomTypeOccupancyDateCapacityDetailsRowMapper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.expose.external.IntegrationType;
import com.ideas.tetris.pacman.services.functionspace.activity.entity.FunctionSpaceBookingGuestRoom;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.*;
import com.ideas.tetris.pacman.services.functionspace.configuration.service.FunctionSpaceConfigurationService;
import com.ideas.tetris.pacman.services.groupblock.GroupBlockMaster;
import com.ideas.tetris.pacman.services.groupmeetingsandevents.evaluations.dto.EvaluationFilter;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfiguration;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingEvaluationMethod;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.ConferenceAndBanquetService;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.GroupPricingConfigurationService;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.dto.*;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.*;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.packaging.GroupEvaluationGroupPricingPackageDetail;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.packaging.GroupEvaluationGroupPricingPackageRevenueByArrivalDate;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.packaging.GroupEvaluationGroupPricingPackageRevenueByRevenueGroup;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.service.mapper.BookingEvaluationResultMapperService;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.service.mapper.EvaluationResultMapperService;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.service.mapper.GroupEvaluationArrivalDateMapperService;
import com.ideas.tetris.pacman.services.grouppricing.ngi.dto.EvaluationResult;
import com.ideas.tetris.pacman.services.grouppricing.ngi.dto.salesandcatering.BookingEvaluationResultTracker;
import com.ideas.tetris.pacman.services.grouppricing.search.GroupEvaluationSearchCriteria;
import com.ideas.tetris.pacman.services.job.JobMonitorService;
import com.ideas.tetris.pacman.services.job.entity.ExecutionStatus;
import com.ideas.tetris.pacman.services.job.entity.JobView;
import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastType;
import com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentSummary;
import com.ideas.tetris.pacman.services.overbooking.entity.OverbookingAccomLevelView;
import com.ideas.tetris.pacman.services.overbooking.service.OverbookingOverrideService;
import com.ideas.tetris.pacman.services.problem.ProblemService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.security.GlobalUser;
import com.ideas.tetris.pacman.services.security.GlobalUserCriteria;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.pacman.services.servicingcostbylos.service.ServicingCostByLOSService;
import com.ideas.tetris.pacman.services.syncflags.service.SyncFlagService;
import com.ideas.tetris.pacman.services.useractivity.ActivityType;
import com.ideas.tetris.pacman.services.useractivity.UserActivityService;
import com.ideas.tetris.pacman.services.useractivity.entity.UserActivityPageCodeEnum;
import com.ideas.tetris.platform.common.Justification;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameter;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameterValue;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.regulator.service.RegulatorService;
import com.ideas.tetris.platform.services.regulator.service.spring.RegulatorSpringService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.builder.ReflectionToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;
import org.apache.log4j.Logger;
import org.hibernate.Hibernate;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.ws.rs.QueryParam;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.ENABLE_RUNNING_GROUP_EVALUATION_DURING_BDE;
import static com.ideas.tetris.pacman.services.grouppricing.evaluation.dto.GroupEvaluationSmallGroupPricingStatus.FULL_EVALUATION_NEEDED;
import static com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluation.FIND_GE_BY_IDS;
import static com.ideas.tetris.platform.common.mock.ServiceMockModeBean.isServiceMockModeEnabled;
import static com.ideas.tetris.platform.common.xstream.SerializationUtil.serialize;
import static java.util.Objects.nonNull;
import static org.apache.commons.collections.CollectionUtils.isEmpty;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;


@Justification("In some cases it is taking SAS longer than 5 minutes to process the evaluation. Relaxing the default tx timeout to accomadate this")
@Component
@Transactional(timeout = 420)
public class GroupEvaluationService {
    private static final Logger LOGGER = Logger.getLogger(GroupEvaluationService.class);
    private static final String FUNCTION_SPACE_EVALUATION_WINDOW_SIZE = "select value from  ip_cfg_property_attribute where Attribute_Name = 'GP_FS_FCST_WINDOW_SIZE'";
    private static final long JOB_TIMEOUT = 415000;
    private static final String GROUP_EVALUATION_FAILED = "Group Evaluation Failed";

    public static final String GROUP_EVALUATION_SAS_INVOCATION_STEP_NAME = "GroupEvaluationSasInvocationStep";
    public static final String PROPERTY_ID = "propertyId";
    public static final String START_DATE = "startDate";
    public static final String END_DATE = "endDate";
    private static final String GET_DECISIONS_BY_BASE_ROOM_TYPE_WITH_DATES_BETWEEN =
            " SELECT DISTINCT CPDecision.Arrival_DT as occupancyDate, AC.Accom_Class_ID as accomClassId, AC.Accom_Class_Code as accomClassCode, " +
                    "   AC.Rank_Order as accomClassRankOrder, CPDecision.Final_Bar as currentBar" +
                    " FROM CP_Decision_Bar_Output CPDecision INNER JOIN Grp_Prc_Cfg_Base_AT GrpPrcBaseAT " +
                    " ON CPDecision.Accom_Type_ID = GrpPrcBaseAT.Accom_Type_ID " +
                    " INNER JOIN Accom_Type AT ON CPDecision.Accom_Type_ID = AT.Accom_Type_ID and GrpPrcBaseAT.Accom_Type_ID = AT.Accom_Type_ID " +
                    " INNER JOIN Accom_Class AC ON AC.Accom_Class_ID = AT.Accom_Class_ID " +
                    " WHERE CPDecision.Product_ID=1 and CPDecision.Arrival_DT between :startDate and :endDate ORDER BY CPDecision.Arrival_DT, AC.Rank_Order";

    private static final String GET_FINAL_PRICE_FOR_ROH_BASED_ON_ARRIVAL_DATE = "select MIN(final_BAR) from (select * from CP_Decision_Bar_Output where Accom_Type_ID in(select Accom_Type_ID from Accom_Type where\n" +
            "Accom_Class_ID=(select Top 1 Accom_Class_ID from Accom_Class where Master_Class=1 AND Status_ID=1 AND Accom_Class_Code != 'Unassigned')) and Arrival_DT=:arrivalDate and Product_ID=1) as FinalBar";

    private static final String GET_FINAL_PRICE_FOR_ROH_FOR_ARRIVAL_DATE_RANGE = "select distinct Arrival_DT, MIN(final_BAR) from " +
            "(" +
            "   select * from CP_Decision_Bar_Output where Accom_Type_ID in " +
            "   (   " +
            "       select Accom_Type_ID from Accom_Type where\n" +
            "       Accom_Class_ID = (select Top 1 Accom_Class_ID from Accom_Class where Master_Class=1 AND Status_ID=1 AND Accom_Class_Code != 'Unassigned')" +
            "   ) " +
            "   and Arrival_DT >= :startDate and Arrival_DT <= :endDate  and Product_ID=1" +
            ") as FinalBar group by Arrival_DT";

    private static final String GET_FINAL_PRICE_FOR_RC_FOR_ARRIVAL_DATE_RANGE = "select Arrival_DT, Accom_Type_ID, final_BAR from " +
            "CP_Decision_Bar_Output where Arrival_DT >= :startDate and Arrival_DT <= :endDate and Accom_Type_ID in (:accomTypeIds) and Product_ID=1";

    private static final String GET_FINAL_PRICE_FOR_RC_BASED_ON_ARRIVAL_DATE_AND_ACCOMID = "select final_BAR from CP_Decision_Bar_Output where Arrival_DT=:arrivalDate and Accom_Type_ID=:accomId and Product_ID=1";

    @Autowired
	private RegulatorService regulatorService;

    @Autowired
    private RegulatorSpringService regulatorSpringService;


    @TenantCrudServiceBean.Qualifier
	@Qualifier("tenantCrudServiceBean")
    @Autowired
	private CrudService tenantCrudService;

    @Autowired
	private JobMonitorService jobMonitorService;

    @Autowired
	private DateService dateService;

    @Autowired
	private JobServiceLocal jobService;

    @Autowired
	private UserService userService;

    @Autowired
	private GroupPricingConfigurationService groupPricingConfigurationService;

    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;

    @Autowired
	private FunctionSpaceConfigurationService functionSpaceConfigurationService;

    @Autowired
    GroupEvaluationSasInvocationService groupEvaluationSasInvocationService;

    @Autowired
	private GroupEvaluationSmallGroupPricingService groupEvaluationSmallGroupPricingService;

    @Autowired
	private UserActivityService userActivityService;

    @Autowired
	private AuditReaderService auditReaderService;

    @Autowired
    OverbookingOverrideService overbookingService;

    @Autowired
    ServicingCostByLOSService servicingCostByLOSService;

    @Autowired
    PropertyService propertyService;

    @Autowired
	private EvaluationResultMapperService evaluationResultMapperService;

    @Autowired
	private BookingEvaluationResultMapperService bookingEvaluationResultMapperService;

    @Autowired
	private SyncFlagService syncFlagService;

    @Autowired
    private ProblemService problemService;

    @Autowired
    private ConferenceAndBanquetService conferenceAndBanquetService;

    @Autowired
    GroupEvaluationArrivalDateMapperService groupEvaluationArrivalDateMapperService;

    public List<MarketSegmentSummary> getCommonPropertyMarketSegmentsForGroupForecastType(List<Integer> propertyIds) {
        return getMarketSegmentsForGroupForecastType(propertyIds);
    }

    public void initOnBooksIfNeeded(List<GroupEvaluationArrivalDateDisplacementAndForecastDetail> dayOfStayGroupEvaluationArrivalDateDisplacementAndForecastDetails) {
        boolean isOnBooksApplied = true;
        List<GroupEvaluationArrivalDateDisplacementAndForecastDetail> excludePreAndPost = new ArrayList<>();
        for (GroupEvaluationArrivalDateDisplacementAndForecastDetail detail : dayOfStayGroupEvaluationArrivalDateDisplacementAndForecastDetails) {
            if (detail.getOccupancyDate() != null) {
                excludePreAndPost.add(detail);
            }
            if (!detail.isOnBooksApplied() && isOnBooksApplied) {
                isOnBooksApplied = false;
            }
        }

        if (!isOnBooksApplied) {
            applyOnBooks(excludePreAndPost);
        }
    }

    public void applyOnBooks(List<GroupEvaluationArrivalDateDisplacementAndForecastDetail> details) {
        if (CollectionUtils.isEmpty(details)) {
            return;
        }

        // Put the details in a map for easier access
        Map<LocalDate, GroupEvaluationArrivalDateDisplacementAndForecastDetail> detailsMap = new HashMap<>();
        for (GroupEvaluationArrivalDateDisplacementAndForecastDetail detail : details) {
            detail.setOnBooksApplied(true);
            detailsMap.put(detail.getOccupancyDate(), detail);
        }

        // Set the Rooms Sold value per the appropriate type of Forecast Group
        List<OccupancyDateBusinessTypeRoomsSold> roomsSold = findRoomsSoldForDatesByBusinessType(detailsMap.keySet());
        if (roomsSold != null) {

            for (OccupancyDateBusinessTypeRoomsSold occupancyDateBusinessTypeRoomsSold : roomsSold) {
                GroupEvaluationArrivalDateDisplacementAndForecastDetail detail = detailsMap
                        .get(occupancyDateBusinessTypeRoomsSold.getOccupancyDate());
                if (detail != null) {
                    if (ForecastType.GROUP_FORECAST_TYPE_ID
                            .equals(occupancyDateBusinessTypeRoomsSold.getBusinessTypeId())) {
                        detail.setGroupOnBooks(occupancyDateBusinessTypeRoomsSold.getRoomsSold());
                    } else {
                        detail.setTransientOnBooks(occupancyDateBusinessTypeRoomsSold.getRoomsSold());
                    }
                }
            }
        }
    }

    protected MarketSegmentSummary getMarketSegmentForProperty(GroupEvaluation groupEvaluation) {
        Integer propertyId = groupEvaluation.getPropertyId();
        String marketSegmentCode = groupEvaluation.getMarketSegment().getCode();
        LOGGER.info("Finding Market Segment for Property: " + propertyId + " And Market Segment Code: " + marketSegmentCode);

        return getMarketSegmentSummaryByCode(marketSegmentCode);
    }

    public MarketSegmentSummary getMarketSegmentSummaryByCode(String marketSegmentCode) {
        return tenantCrudService.findByNamedQuerySingleResult(MarketSegmentSummary.BY_CODE,
                QueryParameter.with("code", marketSegmentCode).parameters());
    }

    public GroupEvaluation generateEvaluation(GroupEvaluation groupEvaluation) {
        groupEvaluation.setEvaluationCategory(getEvaluationCategory());
        return evaluate(groupEvaluation);
    }

    public EvaluationCategory getEvaluationCategory() {
        if (!conferenceAndBanquetService.shouldUseFSRevenueStreams()) {
            return EvaluationCategory.UNKNOWN;
        }

        return isGroupPricingEnabled() ? EvaluationCategory.GP : EvaluationCategory.FS;
    }

    public GroupEvaluation evaluate(GroupEvaluation groupEvaluation) {
        LOGGER.debug("Starting to evaluate: " + ReflectionToStringBuilder.toString(groupEvaluation, ToStringStyle.MULTI_LINE_STYLE));

        // Check to see if Sync is required due to Room Class Configuration Changes for Room Class Evaluations
        final boolean accommodationConfigSyncRequired = syncFlagService.isSyncEnabledFor(SyncConfigParamName.ACCOMMODATION_CONFIGURATION_CHANGED);
        if (groupEvaluation.isRoomTypeEvaluation() && accommodationConfigSyncRequired) {
            throw new TetrisException("groupEvaluation.error.room.class.sync.required");
        }

        // tell user activity tracking that the user attempted to run an evaluation
        userActivityService.startActivity(UserActivityPageCodeEnum.GROUP_PRICING_EVALUATION.getPageCode(),
                ActivityType.RUN_EVALUATION, groupEvaluation.getEvaluationType().name(), null);

        // Reset all of the arrival date result codes
        resetResultCode(groupEvaluation);
        groupEvaluation.setMarketSegment(getMarketSegmentForProperty(groupEvaluation));

        if (isServiceMockModeEnabled()) {
            return mock_evaluate(groupEvaluation);
        }

        if (shouldCheckRegulatorForGroupEvaluations()) {
            // Check the regulator to see if a GroupEvaluation can be run
            if (regulatorService.isSpringTXEnableRegulatorService()) {
                if (regulatorSpringService.isPropertyReadOnly()) {
                    throw new TetrisException(ErrorCode.DENIED_BY_REGULATOR_SERVICE, "Another process is currently running.");
                }
            } else {
                if (regulatorService.isPropertyReadOnly()) {
                    throw new TetrisException(ErrorCode.DENIED_BY_REGULATOR_SERVICE, "Another process is currently running.");
                }
            }
        }

        GroupEvaluationSmallGroupPricingResult smallGroupEvaluationResult = groupEvaluationSmallGroupPricingService.evaluate(groupEvaluation);
        if (smallGroupEvaluationResult.getStatus() == FULL_EVALUATION_NEEDED) {
            // Execute GroupEvaluationJob
            LOGGER.info("Invoking Group Evaluation Job for doing SAS group evaluation for group: " + groupEvaluation.getGroupName());
            groupEvaluation = (GroupEvaluation) executeJob(groupEvaluation);
        } else {
            groupEvaluation = smallGroupEvaluationResult.getGroupEvaluation();
        }

        LOGGER.info("Evaluated: " + ReflectionToStringBuilder.toString(groupEvaluation, ToStringStyle.MULTI_LINE_STYLE));

        groupEvaluation.setPerRoomServicingCost(getPerRoomServicingCost(groupEvaluation));
        groupEvaluation.setContractedRate(null);

        if (GroupPricingEvaluationMethod.RC.equals(groupEvaluation.getEvaluationMethod())) {
            groupEvaluation.setPerRoomServingCostByRoomType(getPerRoomServicingCostByRoomType());
        }

        return groupEvaluation;
    }

    private boolean shouldCheckRegulatorForGroupEvaluations() {
        try {
            return !pacmanConfigParamsService.getBooleanParameterValue(ENABLE_RUNNING_GROUP_EVALUATION_DURING_BDE);
        } catch (Exception e) {
            LOGGER.error("Error reading value for ENABLE_RUNNING_GROUP_EVALUATION_DURING_BDE", e);
            return true;
        }
    }


    private Object executeJob(GroupEvaluation groupEvaluation) {
        // Pass in the groupEvaluation as a parameter
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, groupEvaluation);
        // Start the job
        Long jobExecutionId = jobService.startGuaranteedNewInstance(JobName.GroupEvaluation, parameters);
        // Need to get the Job Instance Id
        Long jobInstanceId = jobMonitorService.getJobInstanceId(jobExecutionId);
        // Wait for the job to complete or abandon
        JobView jobView = jobMonitorService.getJobViewWaitUntilCompletedOrAbandoned(jobInstanceId, false, JOB_TIMEOUT);
        // If the response was a TetrisException, bring it to the UI
        Object response = jobView.getResponseFromStep(GROUP_EVALUATION_SAS_INVOCATION_STEP_NAME);

        if (!(response instanceof GroupEvaluation)) {
            createProblem(jobExecutionId, jobInstanceId, jobView.getStepExecutionId(), groupEvaluation.getPropertyId());
            handleException(response);
        }

        return response;
    }

    private void handleException(Object response) {
        if (response instanceof TetrisException) {
            throw (TetrisException) response;
        } else if (!(response instanceof GroupEvaluation)) {
            throw new TetrisException(ErrorCode.GROUP_EVALUATION_FAILED, "Unable to perform Group Evaluation");
        }
    }

    private void createProblem(Long jobExecutionId, Long jobInstanceId, Long stepExecutionId, Integer propertyId) {
        problemService.createClosedProblem(jobInstanceId, jobExecutionId, stepExecutionId, GROUP_EVALUATION_FAILED, ErrorCode.GROUP_EVALUATION_FAILED, propertyId);
    }

    public Map<AccomClass, BigDecimal> getPerRoomServicingCostByRoomType() {
        Map<AccomClass, BigDecimal> accomClassBigDecimalMap = new HashMap<>();

        List<GroupPricingConfiguration> groupPricingConfigurations = servicingCostByLOSService.isProfitOptimizationEnabled() ?
                servicingCostByLOSService.getGroupPricingConfigDetails(PacmanWorkContextHelper.getPropertyId())
                : groupPricingConfigurationService.getGroupPricingConfigurationsPerRoomClass();

        if (groupPricingConfigurations != null) {
            groupPricingConfigurations.forEach(groupPricingConfiguration -> accomClassBigDecimalMap.put(groupPricingConfiguration.getAccomClass(), groupPricingConfiguration.getPerRoomServicingCost()));
        }

        return accomClassBigDecimalMap;
    }

    public List<OccupancyDateBusinessTypeRoomsSold> findRoomsSoldForDatesByBusinessType(Set<LocalDate> occupancyDates) {
        List<Date> dates = new ArrayList<>();
        for (LocalDate localDate : occupancyDates) {
            dates.add(localDate.toDate());
        }

        StringBuilder query = new StringBuilder();
        query.append("select Occupancy_DT, Business_Type, SUM(Rooms_Sold) Rooms_Sold from ( \n");
        query.append(
                "    select maa.Occupancy_DT, case when fg.Forecast_Type_ID = 6 then 6 else 0 end Business_Type, SUM(maa.Rooms_Sold) Rooms_Sold \n");
        query.append(
                "    from Mkt_Accom_Activity maa inner join Mkt_Seg_Forecast_Group msfg on maa.Mkt_Seg_ID = msfg.Mkt_Seg_ID and msfg.Status_ID = 1 \n");
        query.append(
                "    inner join Forecast_Group fg on msfg.Forecast_Group_ID = fg.Forecast_Group_ID and fg.Status_ID = 1 \n");
        query.append("    where maa.Property_ID = :propertyId \n");
        query.append("    and maa.Occupancy_DT in (:occupancyDates) \n");
        query.append("    group by maa.Occupancy_DT, fg.Forecast_Type_ID \n");
        query.append(") roomSold \n");
        query.append("group by Occupancy_DT, Business_Type \n");
        return tenantCrudService
                .findByNativeQuery(query.toString(),
                        QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                                .and("occupancyDates", dates).parameters(),
                        new OccupancyDateBusinessTypeRoomsSoldRowMapper());
    }

    public void fullyHydrateAudittedGroupEvaluation(GroupEvaluation groupEvaluation) {
        // Need to hydrate the market segments
        groupEvaluation.getMarketSegment().getName();
        Hibernate.initialize(groupEvaluation);
        // Load the day of stays
        Set<GroupEvaluationDayOfStay> groupEvaluationDayOfStays = groupEvaluation.getGroupEvaluationDayOfStays();
        if (groupEvaluationDayOfStays != null) {
            groupEvaluationDayOfStays.size();
        }

        // Load the costs
        Set<GroupEvaluationCost> groupEvaluationCosts = groupEvaluation.getGroupEvaluationCosts();
        if (groupEvaluationCosts != null) {
            groupEvaluationCosts.size();
        }

        for (GroupEvaluationConferenceAndBanquet groupEvaluationConferenceAndBanquet : groupEvaluation
                .getGroupEvaluationConferenceAndBanquets()) {
            Hibernate
                    .initialize(groupEvaluationConferenceAndBanquet.getGroupPricingConfigurationConferenceAndBanquet());
        }
        for (GroupEvaluationAncillary groupEvaluationAncillary : groupEvaluation.getGroupEvaluationAncillaries()) {
            Hibernate.initialize(groupEvaluationAncillary.getGroupPricingConfigurationAncillaryStream());
        }

        Set<GroupEvaluationArrivalDate> groupEvaluationArrivalDates = groupEvaluation.getGroupEvaluationArrivalDates();
        if (groupEvaluationArrivalDates != null) {
            for (GroupEvaluationArrivalDate groupEvaluationArrivalDate : groupEvaluationArrivalDates) {
                Hibernate.initialize(groupEvaluationArrivalDate);
                List<GroupEvaluationArrivalDateForecastGroup> groupEvaluationArrivalDateForecastGroups = groupEvaluationArrivalDate
                        .getGroupEvaluationArrivalDateForecastGroups();
                if (groupEvaluationArrivalDateForecastGroups != null) {
                    for (GroupEvaluationArrivalDateForecastGroup groupEvaluationArrivalDateForecastGroup : groupEvaluationArrivalDateForecastGroups) {
                        // Need to get the forecast group
                        groupEvaluationArrivalDateForecastGroup.getForecastGroup().getName();

                        // Load up the forecast group dates
                        List<GroupEvaluationArrivalDateForecastGroupDateROH> groupEvaluationArrivalDateForecastGroupDates = groupEvaluationArrivalDateForecastGroup
                                .getGroupEvaluationArrivalDateForecastGroupDatesROH();
                        if (groupEvaluationArrivalDateForecastGroupDates != null) {
                            groupEvaluationArrivalDateForecastGroupDates.size();
                        }

                        // Load up the forecast group dates per Room Class
                        List<GroupEvaluationArrivalDateForecastGroupDateAC> groupEvaluationArrivalDateForecastGroupDateACs = groupEvaluationArrivalDateForecastGroup
                                .getGroupEvaluationArrivalDateForecastGroupDateACs();
                        if (groupEvaluationArrivalDateForecastGroupDateACs != null) {
                            Hibernate.initialize(groupEvaluationArrivalDateForecastGroupDateACs);
                            for (GroupEvaluationArrivalDateForecastGroupDateAC ac : groupEvaluationArrivalDateForecastGroupDateACs) {
                                Hibernate.initialize(ac);
                                Hibernate.initialize(ac.getAccomClass());
                            }
                        }
                    }
                }
                // User adjustment
                List<GroupEvaluationArrivalDateUserAdjustment> groupEvaluationArrivalDateUserAdjustments = groupEvaluationArrivalDate
                        .getGroupEvaluationArrivalDateUserAdjustments();
                Hibernate.initialize(groupEvaluationArrivalDateUserAdjustments);
                for (GroupEvaluationArrivalDateUserAdjustment groupEvaluationArrivalDateUserAdjustment : groupEvaluationArrivalDateUserAdjustments) {
                    Hibernate.initialize(groupEvaluationArrivalDateUserAdjustment);
                    Hibernate.initialize(groupEvaluationArrivalDateUserAdjustment.getFunctionSpaceRevenueGroup());
                }

                loadGuestRoomRatesByOccupancyDateDetails(groupEvaluationArrivalDate);

                // function spaces
                List<GroupEvaluationFunctionSpaceArrivalDate> groupEvaluationFuctionSpaceArrivalDates = groupEvaluationArrivalDate
                        .getGroupEvaluationFunctionSpaceArrivalDates();
                Hibernate.initialize(groupEvaluationFuctionSpaceArrivalDates);

                List<GroupEvaluationArrivalDateAccomClass> groupEvaluationArrivalDateAccomClasses = groupEvaluationArrivalDate
                        .getGroupEvaluationArrivalDateAccomClasses();
                Hibernate.initialize(groupEvaluationArrivalDateAccomClasses);
                for (GroupEvaluationArrivalDateAccomClass groupEvaluationArrivalDateAccomClass : groupEvaluationArrivalDateAccomClasses) {
                    Hibernate.initialize(groupEvaluationArrivalDateAccomClass.getAccomClass());
                    List<GroupEvaluationArrivalDateAccomType> arrivalDateAccomTypes = groupEvaluationArrivalDateAccomClass
                            .getGroupEvaluationArrivalDateAccomTypes();
                    Hibernate
                            .initialize(groupEvaluationArrivalDateAccomClass.getGroupEvaluationArrivalDateAccomTypes());

                    for (GroupEvaluationArrivalDateAccomType accomType : arrivalDateAccomTypes) {
                        Hibernate.initialize(accomType.getAccomType());
                    }
                }

                for (GroupEvaluationFunctionSpaceArrivalDate groupEvaluationFuctionSpaceArrivalDate : groupEvaluationFuctionSpaceArrivalDates) {
                    Hibernate.initialize(groupEvaluationFuctionSpaceArrivalDate
                            .getGroupEvaluationFunctionSpaceArrivalDateDayParts());
                    List<GroupEvaluationFunctionSpaceArrivalDateDayPart> fsArrivalDateDayParts = groupEvaluationFuctionSpaceArrivalDate
                            .getGroupEvaluationFunctionSpaceArrivalDateDayParts();

                    if (fsArrivalDateDayParts != null) {
                        Hibernate.initialize(fsArrivalDateDayParts);
                    }

                    for (GroupEvaluationFunctionSpaceArrivalDateDayPart fsArrivalDateDayPart : fsArrivalDateDayParts) {
                        // Not audited - fetch it this way for now
                        Integer dayPartId = fsArrivalDateDayPart.getFunctionSpaceDayPart().getId();
                        fsArrivalDateDayPart
                                .setFunctionSpaceDayPart(functionSpaceConfigurationService.findDayPartById(dayPartId));
                    }
                }

            }
        }

        Set<GroupEvaluationFunctionSpace> groupEvaluationFunctionSpaces = groupEvaluation
                .getGroupEvaluationFunctionSpaces();
        Hibernate.initialize(groupEvaluationFunctionSpaces);

        for (GroupEvaluationFunctionSpace functionSpace : groupEvaluationFunctionSpaces) {
            Set<GroupEvaluationFunctionSpaceFunctionRoom> functionRooms = functionSpace
                    .getGroupEvaluationFunctionSpaceFunctionRooms();

            if (functionRooms != null) {
                Hibernate.initialize(functionRooms);
            }

            for (GroupEvaluationFunctionSpaceFunctionRoom functionRoom : functionRooms) {

                if (functionRoom.getFunctionSpaceFunctionRoom() != null) {
                    // Not audited - fetch it this way for now
                    Integer functionRoomId = functionRoom.getFunctionSpaceFunctionRoom().getId();
                    functionRoom.setFunctionSpaceFunctionRoom(
                            functionSpaceConfigurationService.findFunctionRoomById(functionRoomId));
                }
                // handle combo room and its indivisible parts
                FunctionSpaceFunctionRoom functionSpaceRoom = functionRoom.getFunctionSpaceFunctionRoom();
                if (functionSpaceRoom.isCombo()) {
                    FunctionSpaceCombinationFunctionRoom comboRoom = getCombinationRoom(functionSpaceRoom.getId());
                    Hibernate.initialize(comboRoom.getIndivisibleFunctionRoomParts());
                    functionRoom.setFunctionSpaceFunctionRoom(comboRoom);

                    for (FunctionSpaceFunctionRoom indivisibleRoom : comboRoom.getIndivisibleFunctionRoomParts()) {
                        Hibernate.initialize(indivisibleRoom.getFunctionSpaceFunctionRoomMARSeasons());
                    }
                } else {
                    Hibernate.initialize(
                            functionRoom.getFunctionSpaceFunctionRoom().getFunctionSpaceFunctionRoomMARSeasons());
                }

                Set<FunctionSpaceFunctionRoomMARSeason> marSeasons = functionRoom.getFunctionSpaceFunctionRoom()
                        .getFunctionSpaceFunctionRoomMARSeasons();
                Hibernate.initialize(marSeasons);
                for (FunctionSpaceFunctionRoomMARSeason season : marSeasons) {
                    Hibernate.initialize(season);
                }
            }
        }

        // function space conference and banquets
        Set<GroupEvaluationFunctionSpaceConfAndBanq> confAndBaqs = groupEvaluation
                .getGroupEvaluationFunctionSpaceConfAndBanquets();

        if (confAndBaqs != null) {
            Hibernate.initialize(confAndBaqs);
        }

        for (GroupEvaluationFunctionSpaceConfAndBanq groupEvaluationFunctionSpaceConfAndBanq : groupEvaluation
                .getGroupEvaluationFunctionSpaceConfAndBanquets()) {
            // Not audited - fetch it this way for now
            Integer revenueGroupId = groupEvaluationFunctionSpaceConfAndBanq.getFunctionSpaceRevenueGroup().getId();
            List<FunctionSpaceRevenueGroup> revenueGroups = functionSpaceConfigurationService.getActiveRevenueGroups();

            for (FunctionSpaceRevenueGroup revenueGroup : revenueGroups) {
                if (revenueGroupId.equals(revenueGroup.getId())) {
                    groupEvaluationFunctionSpaceConfAndBanq.setFunctionSpaceRevenueGroup(revenueGroup);
                    break;
                }
            }
        }

        // Load Room Types
        loadRoomTypes(groupEvaluation);

        hydratePackageDetails(groupEvaluation);

        hydrateEvaluationApprovals(groupEvaluation);
    }

    private boolean groupEvaluationResultsByOccupancyDateEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_EVALUATION_RESULTS_BY_OCCUPANCY_DATE);
    }

    public boolean groupPricingUseExtendedWindowEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(IPConfigParamName.GP_USE_EXTENDED_WND);
    }

    @SuppressWarnings("unchecked")
    public List<MarketSegmentSummary> getAllMarketSegmentsAssignedToForecastGroups() {
        return tenantCrudService.findByNamedQuery(MarketSegmentSummary.ASSIGNED_TO_FORECAST_GROUP);
    }

    public List<OccupancyDateCapacity> getCapacitiesFromActivityForDateRange(LocalDate startDate, LocalDate endDate) {
        return getCapacities(startDate.toDate(), endDate.toDate());
    }

    public List<OccupancyDateCapacity> getCapacities(Date startDate, Date endDate) {
        return tenantCrudService.findByNativeQuery(
                "SELECT Occupancy_DT, Available_Capacity, Physical_Capacity from dbo.ufn_get_capacities_by_occupancy_dt(:propertyId, :startDate, :endDate)",
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and(START_DATE, startDate).and(END_DATE, endDate).parameters(),
                new OccupancyDateCapacityRowMapper());
    }

    public List<OccupancyDateCapacity> getCapacitiesForDateRange(LocalDate startDate, LocalDate endDate) {
        if (groupPricingUseExtendedWindowEnabled()) {
            return getRoomsCapacitiesForExtendedDates(startDate, endDate);
        } else {
            return getCapacitiesFromActivityForDateRange(startDate, endDate);
        }
    }

    public List<OccupancyDateCapacity> getRoomsCapacitiesForExtendedDates(LocalDate startDate, LocalDate endDate) {
        LocalDate maxOccupancyDateFromAccomActivity = LocalDateUtils.fromDate((Date) getAccomActivityMaxOccupancyDate());
        if (maxOccupancyDateFromAccomActivity.isBefore(endDate)) {
            List<OccupancyDateCapacity> activityDatesCapacities = getCapacitiesFromActivityForDateRange(
                    LocalDateUtils.minDate(maxOccupancyDateFromAccomActivity, startDate),
                    maxOccupancyDateFromAccomActivity);
            Optional<OccupancyDateCapacity> maxActivityDateCapacity = activityDatesCapacities.stream()
                    .max(Comparator.comparing(OccupancyDateCapacity::getOccupancyDate));
            Map<LocalDate, Integer> extendedWindowRoomsSoldMap = getRoomsCapacitiesFromFunctionSpaceBooking(
                    LocalDateUtils.maxDate(startDate, maxOccupancyDateFromAccomActivity.plusDays(1)), endDate);

            List<OccupancyDateCapacity> occupancyDateCapacities = new ArrayList<>();
            if (!startDate.isAfter(maxOccupancyDateFromAccomActivity)) {
                occupancyDateCapacities.addAll(activityDatesCapacities);
            }
            if (maxActivityDateCapacity.isPresent()) {
                for (LocalDate date = LocalDateUtils.maxDate(startDate, maxOccupancyDateFromAccomActivity.plusDays(1));
                     !date.isAfter(endDate); date = date.plusDays(1)) {
                    OccupancyDateCapacity dateCapacity = new OccupancyDateCapacity();
                    dateCapacity.setOccupancyDate(date);
                    dateCapacity.setAvailableCapacity(maxActivityDateCapacity.get().getPhysicalCapacity() - extendedWindowRoomsSoldMap.getOrDefault(date, 0));
                    dateCapacity.setPhysicalCapacity(maxActivityDateCapacity.get().getPhysicalCapacity());
                    occupancyDateCapacities.add(dateCapacity);
                }
            }
            return occupancyDateCapacities;
        } else {
            return getCapacitiesFromActivityForDateRange(startDate, endDate);
        }
    }

    public Map<AccomType, List<OverbookingAccomLevelView>> getCapacitiesForDateRangeByAccomType(LocalDate startDate,
                                                                                                LocalDate endDate) {
        Map<AccomType, List<OverbookingAccomLevelView>> accomTypeCapacityMap = new HashMap<>();

        // Get list of configured room types for group pricing - will change to get configured gp room types
        List<AccomType> accomTypes = groupPricingConfigurationService.getAccomTypesConfiguredForRoomClassEvaluations();

        for (AccomType accomType : accomTypes) {
            List<OverbookingAccomLevelView> accomLevels = overbookingService
                    .executeQueryAccomTypeLevel(accomType.getId(), startDate.toDate(), endDate.toDate());

            accomTypeCapacityMap.put(accomType, accomLevels);
        }

        return accomTypeCapacityMap;
    }

    /**
     * Calculates the total physical capacity for a property by summing up all the active accom_type_capacitys from the accom_type table.
     *
     * @return
     */
    public Integer getRohPhysicalCapacity() {
        return getRohPhysicalCapacityForProperty(PacmanWorkContextHelper.getPropertyId());
    }

    public Map<AccomType, List<RoomTypeOccupancyDateCapacityDetails>> getRoomTypeCapacitiesForDateRangeDetails(
            List<AccomType> accomTypes, LocalDate startDate, LocalDate endDate) {
        if (groupPricingUseExtendedWindowEnabled()) {
            return getRoomTypeCapacitiesForExtendedDateDetails(accomTypes, startDate, endDate);
        } else {
            return getRoomTypeCapacitiesFromActivityForDateRange(accomTypes, startDate, endDate);
        }
    }

    public BigDecimal getTotalRoomClassCapacityForOccupancyDate(Integer accomClassId, Date startDate) {
        if (groupPricingUseExtendedWindowEnabled()) {
            Date startDateForExtendedWindow = getStartDateForExtendedWindow();
            if (startDate.compareTo(startDateForExtendedWindow) > 0) {
                startDate = startDateForExtendedWindow;
            }
        }
        List<BigDecimal> resultsList = tenantCrudService.findByNamedQuery(AccomType.CAPACITY_BY_ROOM_CLASS_BY_DATE,
                QueryParameter.with("accomClassId", accomClassId)
                        .and(START_DATE, startDate).parameters());
        return resultsList != null ? resultsList.iterator().next() : BigDecimal.ZERO;
    }

    private Date getStartDateForExtendedWindow() {
        return (Date) tenantCrudService.findByNamedQuerySingleResult(AccomActivity.MAX_OCCUPANCY_DATE);
    }

    protected Map<AccomType, List<RoomTypeOccupancyDateCapacityDetails>> getRoomTypeCapacitiesForExtendedDateDetails(
            List<AccomType> accomTypes, LocalDate startDate, LocalDate endDate) {
        LocalDate maxOccupancyDateFromActivity = LocalDateUtils.fromDate(
                (Date) tenantCrudService.findByNamedQuerySingleResult(AccomActivity.MAX_OCCUPANCY_DATE));
        if (maxOccupancyDateFromActivity.isBefore(endDate)) {
            Map<AccomType, List<RoomTypeOccupancyDateCapacityDetails>> accomCapacityFromActivityMap =
                    getRoomTypeCapacitiesFromActivityForDateRange(
                            accomTypes,
                            LocalDateUtils.minDate(startDate, maxOccupancyDateFromActivity),
                            maxOccupancyDateFromActivity);
            Map<AccomType, RoomTypeOccupancyDateCapacityDetails> maxOccupancyDateCapacityMap =
                    getRoomTypeCapacitiesForMaxOccupancyDate(accomCapacityFromActivityMap);

            List<FunctionSpaceBookingRoomTypeForecastDetail> extendedWindowRoomsSoldByType = getRoomTypeCapacitiesFromFunctionSpaceBooking(
                    LocalDateUtils.maxDate(startDate, maxOccupancyDateFromActivity.plusDays(1)), endDate);

            Map<AccomType, List<RoomTypeOccupancyDateCapacityDetails>> accomTypeExtendedCapacityMap =
                    createAccomTypeCapacityDetailsBeyondForecast(maxOccupancyDateCapacityMap, extendedWindowRoomsSoldByType,
                            LocalDateUtils.maxDate(startDate, maxOccupancyDateFromActivity.plusDays(1)), endDate);

            if (startDate.isBefore(maxOccupancyDateFromActivity)) {
                accomTypeExtendedCapacityMap.forEach((accomType, roomTypeOccupancyDateCapacityDetails) -> {
                    roomTypeOccupancyDateCapacityDetails.addAll(accomCapacityFromActivityMap.get(accomType));
                    roomTypeOccupancyDateCapacityDetails.sort(Comparator.comparing(RoomTypeOccupancyDateCapacityDetails::getOccupancyDate));
                });
            }
            return accomTypeExtendedCapacityMap;
        } else {
            return getRoomTypeCapacitiesFromActivityForDateRange(accomTypes, startDate, endDate);
        }
    }

    protected List<FunctionSpaceBookingRoomTypeForecastDetail> getRoomTypeCapacitiesFromFunctionSpaceBooking(LocalDate startDate, LocalDate endDate) {
        List<Object[]> results = tenantCrudService.findByNamedQuery(FunctionSpaceBookingGuestRoom.GET_FORECASTED_ROOMS_SOLD_BY_ROOM_TYPE_AND_DATE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("startDate", startDate.toDate()).and("endDate", endDate.toDate()).parameters());
        return Optional.ofNullable(results)
                .orElse(Collections.emptyList()).stream()
                .map(FunctionSpaceBookingRoomTypeForecastDetail::new)
                .collect(Collectors.toList());
    }

    protected Map<AccomType, List<RoomTypeOccupancyDateCapacityDetails>> createAccomTypeCapacityDetailsBeyondForecast(
            Map<AccomType, RoomTypeOccupancyDateCapacityDetails> maxOccupancyDateCapacityMap,
            List<FunctionSpaceBookingRoomTypeForecastDetail> extendedWindowRoomsSoldForecast,
            LocalDate startDate, LocalDate endDate) {
        Map<AccomType, List<RoomTypeOccupancyDateCapacityDetails>> accomTypeExtendedCapacityMap = new HashMap<>();
        for (Map.Entry<AccomType, RoomTypeOccupancyDateCapacityDetails>
                accomTypeCapacities : maxOccupancyDateCapacityMap.entrySet()) {
            accomTypeExtendedCapacityMap.put(accomTypeCapacities.getKey(), new ArrayList<>());

            for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                var roomTypeOccupancyDateCapacity = new RoomTypeOccupancyDateCapacityDetails();
                roomTypeOccupancyDateCapacity.setAccomTypeId(accomTypeCapacities.getKey().getId());
                roomTypeOccupancyDateCapacity.setOccupancyDate(date);
                roomTypeOccupancyDateCapacity.setCapacity(accomTypeCapacities.getValue().getCapacity());
                roomTypeOccupancyDateCapacity.setRemainingCapacity(accomTypeCapacities.getValue().getCapacity()
                        - getForecastedRoomsSoldOrZero(extendedWindowRoomsSoldForecast, accomTypeCapacities.getKey(), date));
                accomTypeExtendedCapacityMap.get(accomTypeCapacities.getKey()).add(roomTypeOccupancyDateCapacity);
            }
        }
        return accomTypeExtendedCapacityMap;
    }

    private Integer getForecastedRoomsSoldOrZero(List<FunctionSpaceBookingRoomTypeForecastDetail> extendedWindowRoomsSoldForecast,
                                                 AccomType accomType, LocalDate occupancyDate) {
        return extendedWindowRoomsSoldForecast.stream()
                .filter(forecastDetail -> forecastDetail.getAccomTypeId().equals(accomType.getId()))
                .filter(forecastDetail -> LocalDateUtils.toJavaTimeLocalDate(occupancyDate).isEqual(forecastDetail.getOccupancyDate()))
                .map(FunctionSpaceBookingRoomTypeForecastDetail::getRoomsSold).findFirst().orElse(0);
    }

    protected Map<AccomType, RoomTypeOccupancyDateCapacityDetails> getRoomTypeCapacitiesForMaxOccupancyDate(
            Map<AccomType, List<RoomTypeOccupancyDateCapacityDetails>> activityDatesCapacitiesMap) {
        Map<AccomType, RoomTypeOccupancyDateCapacityDetails> accomCapacityForMaxOccupancyDateMap = new HashMap<>();
        for (Map.Entry<AccomType, List<RoomTypeOccupancyDateCapacityDetails>> accomTypeDetail :
                activityDatesCapacitiesMap.entrySet()) {
            Optional<RoomTypeOccupancyDateCapacityDetails> roomTypeCapacityForMaxOccupancyDate =
                    accomTypeDetail.getValue().stream()
                            .max(Comparator.comparing(RoomTypeOccupancyDateCapacityDetails::getOccupancyDate));
            roomTypeCapacityForMaxOccupancyDate.ifPresent(
                    maxDetail -> accomCapacityForMaxOccupancyDateMap.put(accomTypeDetail.getKey(), maxDetail));

        }
        return accomCapacityForMaxOccupancyDateMap;
    }

    protected Map<LocalDate, Integer> getRoomsCapacitiesFromFunctionSpaceBooking(LocalDate startDate, LocalDate endDate) {
        List<Object[]> results = tenantCrudService.findByNamedQuery(FunctionSpaceBookingGuestRoom.GET_FORECASTED_ROOMS_SOLD_BY_DATE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("startDate", startDate.toDate()).and("endDate", endDate.toDate()).parameters());
        return Optional.ofNullable(results)
                .orElse(Collections.emptyList()).stream()
                .collect(Collectors.toMap(obj -> new LocalDate(obj[0].toString()), obj -> Integer.valueOf(obj[1].toString())));
    }

    public Map<AccomType, List<RoomTypeOccupancyDateCapacityDetails>> getRoomTypeCapacitiesFromActivityForDateRange(List<AccomType> accomTypes, LocalDate startDate, LocalDate endDate) {
        Map<AccomType, List<RoomTypeOccupancyDateCapacityDetails>> roomTypeCapacityMap = new HashMap<>();

        for (AccomType accomType : accomTypes) {
            List<RoomTypeOccupancyDateCapacityDetails> roomTypeCapacities = getCapacitiesForDateRangeDetailsForAccomType(startDate,
                    endDate, PacmanWorkContextHelper.getPropertyId(), accomType.getId());
            roomTypeCapacityMap.put(accomType, roomTypeCapacities);
        }

        return roomTypeCapacityMap;
    }

    private List<RoomTypeOccupancyDateCapacityDetails> getCapacitiesForDateRangeDetailsForAccomType(LocalDate startDate,
                                                                                                    LocalDate endDate, Integer propertyId, Integer accomTypeId) {
        return tenantCrudService.findByNativeQuery(
                "SELECT occupancy_dt, accom_type, physical_capacity, remaining_capacity from dbo.ufn_get_capacities_by_room_type_dt(:propertyId, :accomType, :startDate, :endDate)",
                QueryParameter.with(PROPERTY_ID, propertyId).and("accomType", accomTypeId)
                        .and(START_DATE, startDate.toDate()).and(END_DATE, endDate.toDate()).parameters(),
                new RoomTypeOccupancyDateCapacityDetailsRowMapper());
    }

    protected FunctionSpaceCombinationFunctionRoom getCombinationRoom(Integer roomId) {
        return tenantCrudService.find(FunctionSpaceCombinationFunctionRoom.class,
                roomId);
    }

    @SuppressWarnings("unchecked")
    public List<GlobalUser> getEvaluationUsers(Integer propertyId) {
        List<Integer> evaluationUserIds = tenantCrudService.findByNamedQuery(GroupEvaluation.ALL_USER_IDS,
                QueryParameter.with(PROPERTY_ID, propertyId).parameters());
        if (evaluationUserIds.isEmpty()) {
            return new ArrayList<>();
        }
        return getGlobalUsersByIds(evaluationUserIds);
    }

    public LocalDate getEvaluationWindowEndDate() {
        // Subtract the window size from the forecast window to get the last
        // date that can be selected for an evaluation
        return LocalDate.fromDateFields(dateService.getForecastWindowEndDateBDE())
                .minusDays(SystemConfig.getGroupPricingWindowSize());
    }

    public LocalDate getFunctionSpaceEvaluationWindowEndDate() {
        int windowSize = Integer.parseInt(
                tenantCrudService.findByNativeQuerySingleResult(FUNCTION_SPACE_EVALUATION_WINDOW_SIZE, null));
        return new LocalDate(dateService.getCaughtUpDate()).plusDays(windowSize)
                .minusDays(SystemConfig.getGroupPricingWindowSize());
    }

    public GroupEvaluation getGroupEvaluationById(Integer groupEvaluationId) {
        GroupEvaluation groupEvaluation = tenantCrudService.find(GroupEvaluation.class, groupEvaluationId);
        if (Objects.nonNull(groupEvaluation)) {
            hydrateGroupEvaluations(List.of(groupEvaluation), true);
        }
        return groupEvaluation;
    }

    public GroupEvaluation getGroupEvaluationAtRevision(Integer revision) {
        GroupEvaluation groupEvaluation = (GroupEvaluation) auditReaderService
                .getEntityAtRevision(GroupEvaluation.class, revision);
        if (groupEvaluation != null) {
            if (GroupPricingEvaluationMethod.RC.equals(groupEvaluation.getEvaluationMethod())) {
                groupEvaluation.setPerRoomServingCostByRoomType(getPerRoomServicingCostByRoomType());
            }
            try {
                fullyHydrateAudittedGroupEvaluation(groupEvaluation);
            } catch (Exception exception) {
                throw new TetrisException(ErrorCode.GROUP_EVALUATION_FAILED, "Unable to find audit table details for this evaluation", exception);
            }
        }
        return groupEvaluation;
    }

    public List<FunctionSpaceFunctionRoom> getGroupEvaluationFunctionSpaceRooms() {
        List<FunctionSpaceFunctionRoom> groupEvalFunctionSpaceRooms = new ArrayList<>();
        List<FunctionSpaceFunctionRoom> allActiveAndPricedRooms = functionSpaceConfigurationService
                .getAllActiveRoomsIncludedForPricing();

        for (FunctionSpaceFunctionRoom room : allActiveAndPricedRooms) {
            Hibernate.initialize(room);
            Hibernate.initialize(room.getFunctionSpaceFunctionRoomMARSeasons());
            if (room.isCombo()) {
                FunctionSpaceCombinationFunctionRoom comboRoom = (FunctionSpaceCombinationFunctionRoom) room;
                Hibernate.initialize(comboRoom.getIncludeForPricingParts());
            }
            groupEvalFunctionSpaceRooms.add(room);
        }

        if (groupEvalFunctionSpaceRooms.isEmpty()) {
            LOGGER.warn("No Include For Pricing function space rooms configured");
        }

        return groupEvalFunctionSpaceRooms;
    }

    public List<GroupEvaluationRevision> getGroupEvaluationRevisions(Integer groupEvaluationId) {
        List<GroupEvaluationRevision> revisions = tenantCrudService.findByNativeQuery(
                "select Evaluation_Date,  max(REV) from Grp_Evl_AUD where Grp_Evl_ID = :groupEvaluationId group by Evaluation_Date order by Evaluation_Date desc",
                QueryParameter.with("groupEvaluationId", groupEvaluationId).parameters(),
                new GroupEvaluationRevision());
        if (revisions == null) {
            revisions = new ArrayList<>();
        }
        return revisions;
    }

    public Integer getRevisionIdFromEvaluationIdAndDate(Integer evaluationId, java.time.LocalDateTime evaluatedOnDate) {
        return tenantCrudService.findByNativeQuerySingleResult(
                "SELECT max(REV) FROM Grp_Evl_AUD WHERE Grp_Evl_ID = :groupEvaluationId AND Evaluation_Date = :evaluatedOn",
                QueryParameter.with("groupEvaluationId", evaluationId).and("evaluatedOn", evaluatedOnDate.toString()).parameters());
    }

    public List<MarketSegmentSummary> getMarketSegmentsForGroupForecastType() {
        return getMarketSegmentsForGroupForecastType(Collections.singletonList(PacmanWorkContextHelper.getPropertyId()));
    }

    public Long save(GroupEvaluation groupEvaluation, boolean isEvaluationRequestFromFDC, String evaluationId) {
        boolean isMultiGroupEval = groupEvaluation.isPartOfMultiGroupEvaluation();
        LOGGER.info("Saving: " + ReflectionToStringBuilder.toString(groupEvaluation, ToStringStyle.MULTI_LINE_STYLE));
        saveGroupEvaluation(groupEvaluation, isMultiGroupEval);

        return sendEvaluationResultToNGI(groupEvaluation, isEvaluationRequestFromFDC, evaluationId, isMultiGroupEval);
    }

    public Long sendEvaluationResultToNGI(GroupEvaluation groupEvaluation, boolean isEvaluationRequestFromFDC, String evaluationId, boolean isMultiGroupEval) {
        tenantCrudService.flushAndClear();
        if (genericSalesAndCateringEnabled()) {
            return sendGenericSCEvaluationResultToNGI(groupEvaluation, isEvaluationRequestFromFDC, isMultiGroupEval, evaluationId);
        } else {
            sendEvaluationResultsBackToClient(groupEvaluation, isEvaluationRequestFromFDC, isMultiGroupEval, evaluationId);
            return null;
        }
    }

    protected Long sendGenericSCEvaluationResultToNGI(GroupEvaluation groupEvaluation, boolean isEvaluationRequestFromFDC, boolean isMultiGroupEval, String evaluationId) {
        if (shouldSendGenericSCEvaluationResultToNGI(groupEvaluation, isEvaluationRequestFromFDC, isMultiGroupEval, evaluationId)) {
            Long jobExecutionId = startNGIBookingEvaluationResultJob(groupEvaluation.getSalesCateringBookingId(), groupEvaluation.getPropertyId(), evaluationId);
            return jobMonitorService.getJobInstanceId(jobExecutionId);
        }
        return null;
    }

    public String waitForJobStatus(long jobInstanceId) {
        JobView jobView = jobMonitorService.getJobViewWaitUntilStatus(jobInstanceId, false, 60000L, Arrays.asList(ExecutionStatus.COMPLETED, ExecutionStatus.FAILED));
        return jobView.getExecutionStatus().name();
    }

    private boolean shouldSendGenericSCEvaluationResultToNGI(GroupEvaluation groupEvaluation, boolean isEvaluationRequestFromFDC, boolean isMultiGroupEval, String evaluationId) {
        return !isMultiGroupEval
                && isEvaluationRequestFromFDC
                && StringUtils.isNotBlank(groupEvaluation.getSalesCateringBookingId())
                && StringUtils.isNotBlank(evaluationId)
                && isSendEvalResToNGIEnabled();
    }

    private boolean genericSalesAndCateringEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GENERIC_SALES_AND_CATERING_ENABLED);
    }

    private boolean isOnBooksGroupEvaluation(GroupEvaluation groupEvaluation) {
        return groupEvaluation.getGroupEvaluationDayOfStays().stream().anyMatch(evaluationDayOfStay -> evaluationDayOfStay.getCurrentRate() != null);
    }

    public void saveGroupEvaluation(GroupEvaluation groupEvaluation) {
        boolean isMultiGroupEval = groupEvaluation.isPartOfMultiGroupEvaluation();
        LOGGER.info("Saving: " + ReflectionToStringBuilder.toString(groupEvaluation, ToStringStyle.MULTI_LINE_STYLE));
        saveGroupEvaluation(groupEvaluation, isMultiGroupEval);
    }

    private void saveGroupEvaluation(GroupEvaluation groupEvaluation, boolean isMultiGroupEval) {
        groupEvaluation.setPerRoomServicingCost(getPerRoomServicingCost(groupEvaluation));
        // force an update on grp evl - needed for revisions to stay in sync if user makes adjustments on arr date slider
        groupEvaluation.setEvaluationDate(groupEvaluation.getEvaluationDate().plusMillis(1));

        if (isMultiGroupEval) {
            tenantCrudService.save(groupEvaluation.cloneFromMultiGroupEvaluation());
        } else {
            tenantCrudService.save(groupEvaluation);
        }
    }

    private void sendEvaluationResultsBackToClient(GroupEvaluation groupEvaluation, boolean isEvaluationRequestFromFDC, boolean isMultiGroupEval, String evaluationId) {
        //Client can not do multi group evaluation
        if (!isMultiGroupEval && shouldSendToClient(groupEvaluation, isEvaluationRequestFromFDC)) {
            startNGIAhwsGroupEvalResultsJob(groupEvaluation.getSalesCateringBookingId(), groupEvaluation.getPropertyId(), evaluationId, groupEvaluation.getCurrencyExchangeRate());
        }
    }

    private boolean isSendEvalResToNGIEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(IPConfigParamName.SEND_EVALUATION_RESULTS_TO_NGI.value());
    }

    private boolean shouldSendToClient(GroupEvaluation groupEvaluation, boolean isEvaluationRequestFromFDC) {
        //Checks if SALES_AND_CATERING_FUNCTION_SPACE_CLIENT_ALIAS parameter is set and SalesCateringBookingId is present then send the results to NGI
        //When request comes from client SalesCateringBookingId is present, in ui he can not insert SalesCateringBookingId
        String salesCateringAlias = pacmanConfigParamsService.getParameterValue(IPConfigParamName.SALES_AND_CATERING_FUNCTION_SPACE_CLIENT_ALIAS.value());
        return isSendEvalResToNGIEnabled() && isEvaluationRequestFromFDC && StringUtils.isNotEmpty(salesCateringAlias) && groupEvaluation.getSalesCateringBookingId() != null;
    }

    public Long startNGIAhwsGroupEvalResultsJob(String salesCateringBookingId, int propertyId, String evaluationId, BigDecimal currencyExchangeRate) {
        // Determine the IntegrationType to which job to start
        IntegrationType integrationType = IntegrationType.valueOf("EVALUATION_RESULTS");

        Map<String, Object> jobParams = new HashMap<>();
        jobParams.put(JobParameterKey.PROPERTY_ID, propertyId);
        jobParams.put(JobParameterKey.SALES_CATERING_BOOKING_ID, salesCateringBookingId);
        jobParams.put(JobParameterKey.CURRENCY_EXCHANGE_RATE, currencyExchangeRate);
        if (StringUtils.isNotBlank(evaluationId)) {
            jobParams.put(JobParameterKey.AHWS_EVALUATION_ID, evaluationId);
        }

        return jobService.startGuaranteedNewInstance(integrationType.getG3JobName(), jobParams);
    }

    public Long startNGIBookingEvaluationResultJob(String salesCateringBookingId, int propertyId, String evaluationId) {
        Map<String, Object> jobParams = new HashMap<>();
        jobParams.put(JobParameterKey.PROPERTY_ID, propertyId);
        jobParams.put(JobParameterKey.SALES_CATERING_BOOKING_ID, salesCateringBookingId);
        jobParams.put(JobParameterKey.BOOKING_EVALUATION_ID, evaluationId);

        return jobService.startGuaranteedNewInstance(JobName.NGIBookingEvaluationResultJob, jobParams);
    }

    public EvaluationResult getGroupEvalResults(String salesCateringBookingId, BigDecimal currencyExchangeRate) {
        /*
        Setting some basic values. As what need to be sent to NGI is still under discussion
        once that is finalized correct values needs to be set and passed.
         */
        GroupEvaluation existingEvaluation = fetchGroupEvaluation(salesCateringBookingId);
        setPerRoomServicingCostTo(existingEvaluation);

        Property property = propertyService.getPropertyById(existingEvaluation.getPropertyId());
        return evaluationResultMapperService.buildEvaluationResult(existingEvaluation, property, currencyExchangeRate);
    }

    public BookingEvaluationResultTracker getBookingEvaluationResultTracker(String salesCateringBookingId, String evaluationRequestId) {
        GroupEvaluation existingEvaluation = fetchGroupEvaluation(salesCateringBookingId);
        setPerRoomServicingCostTo(existingEvaluation);
        existingEvaluation.setEvaluationRequestId(evaluationRequestId);

        Property property = propertyService.getPropertyById(existingEvaluation.getPropertyId());
        return bookingEvaluationResultMapperService.buildBookingEvaluationResultTracker(existingEvaluation, property);
    }

    protected GroupEvaluation fetchGroupEvaluation(@QueryParam("salesCateringBookingId") String salesCateringBookingId) {
        return tenantCrudService.findByNamedQuerySingleResult(GroupEvaluation.FIND_BY_SALES_CATERING_BOOKING_ID,
                QueryParameter.with("salesCateringBookingId", salesCateringBookingId).parameters());
    }

    private void setPerRoomServicingCostTo(GroupEvaluation existingEvaluation) {
        existingEvaluation.setPerRoomServicingCost(getPerRoomServicingCostUsingTenant(existingEvaluation));

        if (GroupPricingEvaluationMethod.RC.equals(existingEvaluation.getEvaluationMethod())) {
            existingEvaluation.setPerRoomServingCostByRoomType(getPerRoomServicingCostByRoomType());
        }
    }

    public void saveAndSetParentReferences(GroupEvaluation groupEvaluation) {
        // Assigning the parent object to make it easier for the rest endpoint to perform saves
        setParentReference(groupEvaluation);

        save(groupEvaluation, false, null);
    }

    public void setParentReference(GroupEvaluation groupEvaluation) {
        Set<GroupEvaluationArrivalDate> groupEvaluationArrivalDates = groupEvaluation.getGroupEvaluationArrivalDates();
        if (groupEvaluationArrivalDates != null) {
            for (GroupEvaluationArrivalDate groupEvaluationArrivalDate : groupEvaluationArrivalDates) {
                groupEvaluationArrivalDate.setGroupEvaluation(groupEvaluation);

                List<GroupEvaluationArrivalDateForecastGroup> groupEvaluationArrivalDateForecastGroups = groupEvaluationArrivalDate
                        .getGroupEvaluationArrivalDateForecastGroups();
                if (groupEvaluationArrivalDateForecastGroups != null) {
                    for (GroupEvaluationArrivalDateForecastGroup groupEvaluationArrivalDateForecastGroup : groupEvaluationArrivalDateForecastGroups) {
                        groupEvaluationArrivalDateForecastGroup
                                .setGroupEvaluationArrivalDate(groupEvaluationArrivalDate);

                        List<GroupEvaluationArrivalDateForecastGroupDateROH> groupEvaluationArrivalDateForecastGroupDates = groupEvaluationArrivalDateForecastGroup
                                .getGroupEvaluationArrivalDateForecastGroupDatesROH();
                        if (groupEvaluationArrivalDateForecastGroupDates != null) {
                            for (GroupEvaluationArrivalDateForecastGroupDateROH groupEvaluationArrivalDateForecastGroupDate : groupEvaluationArrivalDateForecastGroupDates) {
                                groupEvaluationArrivalDateForecastGroupDate.setGroupEvaluationArrivalDateForecastGroup(
                                        groupEvaluationArrivalDateForecastGroup);
                            }
                        }

                        List<GroupEvaluationArrivalDateForecastGroupDateAC> groupEvaluationArrivalDateForecastGroupDateACs = groupEvaluationArrivalDateForecastGroup
                                .getGroupEvaluationArrivalDateForecastGroupDateACs();
                        if (groupEvaluationArrivalDateForecastGroupDateACs != null) {
                            for (GroupEvaluationArrivalDateForecastGroupDateAC groupEvaluationArrivalDateForecastGroupDateAC : groupEvaluationArrivalDateForecastGroupDateACs) {
                                groupEvaluationArrivalDateForecastGroupDateAC
                                        .setGroupEvaluationArrivalDateForecastGroup(
                                                groupEvaluationArrivalDateForecastGroup);
                            }
                        }
                    }
                }

                List<GroupEvaluationArrivalDateUserAdjustment> groupEvaluationArrivalDateUserAdjustments = groupEvaluationArrivalDate
                        .getGroupEvaluationArrivalDateUserAdjustments();
                if (groupEvaluationArrivalDateUserAdjustments != null) {
                    for (GroupEvaluationArrivalDateUserAdjustment groupEvaluationArrivalDateUserAdjustment : groupEvaluationArrivalDateUserAdjustments) {
                        groupEvaluationArrivalDateUserAdjustment
                                .setGroupEvaluationArrivalDate(groupEvaluationArrivalDate);
                    }
                }

                if (groupEvaluationResultsByOccupancyDateEnabled()) {
                    List<GroupEvaluationArrivalDateGuestRoomRates> groupEvaluationArrivalDateGuestRoomRates = groupEvaluationArrivalDate
                            .getGroupEvaluationArrivalDateGuestRoomRates();
                    if (groupEvaluationArrivalDateGuestRoomRates != null) {
                        for (GroupEvaluationArrivalDateGuestRoomRates groupEvaluationArrivalDateGuestRoomRate : groupEvaluationArrivalDateGuestRoomRates) {
                            groupEvaluationArrivalDateGuestRoomRate.
                                    setGroupEvaluationArrivalDate(groupEvaluationArrivalDate);
                        }
                    }
                }

                List<GroupEvaluationArrivalDateAccomClass> groupEvaluationArrivalDateAccomClasses = groupEvaluationArrivalDate.getGroupEvaluationArrivalDateAccomClasses();
                if (isNotEmpty(groupEvaluationArrivalDateAccomClasses)) {
                    for (GroupEvaluationArrivalDateAccomClass accomClass : groupEvaluationArrivalDateAccomClasses) {
                        accomClass.setGroupEvaluationArrivalDate(groupEvaluationArrivalDate);

                        List<GroupEvaluationArrivalDateAccomType> accomTypes = accomClass.getGroupEvaluationArrivalDateAccomTypes();
                        if (isNotEmpty(accomTypes)) {
                            for (GroupEvaluationArrivalDateAccomType accomType : accomTypes) {
                                accomType.setGroupEvaluationArrivalDateAccomClass(accomClass);
                            }
                        }
                    }
                }
            }
        }

        Set<GroupEvaluationDayOfStay> groupEvaluationDayOfStays = groupEvaluation.getGroupEvaluationDayOfStays();
        if (groupEvaluationDayOfStays != null) {
            for (GroupEvaluationDayOfStay groupEvaluationDayOfStay : groupEvaluationDayOfStays) {
                groupEvaluationDayOfStay.setGroupEvaluation(groupEvaluation);
            }
        }
    }

    public List<GroupEvaluation> search(GroupEvaluationSearchCriteria groupEvaluationSearchCriteria) {
        return getGroupEvaluations(groupEvaluationSearchCriteria);
    }

    public List<GroupEvaluation> search(GroupEvaluationSearchCriteria groupEvaluationSearchCriteria,
                                        boolean hydratePackageDetails) {
        List<GroupEvaluation> groupEvaluations = getGroupEvaluations(groupEvaluationSearchCriteria);
        if (arePackageDetailsUninitialized(groupEvaluationSearchCriteria) && hydratePackageDetails) {
            groupEvaluations.forEach(this::hydratePackageDetails);
        }
        return groupEvaluations;
    }

    public Integer getTotalNumberOfEvaluations(EvaluationFilter evaluationFilter) {
        GroupEvaluationQueryBuilder groupEvaluationQueryBuilder = new GroupEvaluationQueryBuilder(evaluationFilter);
        Integer total = tenantCrudService.findByNativeQuerySingleResult(groupEvaluationQueryBuilder.build(true),
                groupEvaluationQueryBuilder.parameters());
        return Objects.nonNull(total) ? total : 0;
    }

    private boolean arePackageDetailsUninitialized(GroupEvaluationSearchCriteria groupEvaluationSearchCriteria) {
        return !groupEvaluationSearchCriteria.isHydrateDeepResults();
    }

    private List<GroupEvaluation> getGroupEvaluations(GroupEvaluationSearchCriteria groupEvaluationSearchCriteria) {
        List<GroupEvaluation> list = tenantCrudService.findByCriteria(groupEvaluationSearchCriteria);

        if (groupEvaluationSearchCriteria.isHydrateDeepResults()) {
            hydrateGroupEvaluations(list, groupEvaluationSearchCriteria.isHydrateForecastGroups());
        }

        return list;
    }

    public List<GroupEvaluation> getPaginatedGroupEvaluations(EvaluationFilter evaluationFilter) {
        int offset = (evaluationFilter.getIndex() - 1) * evaluationFilter.getPageSize();

        GroupEvaluationQueryBuilder groupEvaluationQueryBuilder = new GroupEvaluationQueryBuilder(evaluationFilter);
        List<Integer> evaluationIds = tenantCrudService.findByNativeQuery(groupEvaluationQueryBuilder.build(false),
                groupEvaluationQueryBuilder.parameters(), offset, evaluationFilter.getPageSize(), row -> (Integer) row[0]);

        //get attached entities
        List<GroupEvaluation> evaluationsList = new ArrayList<>();
        if(isNotEmpty(evaluationIds)){
            evaluationsList = getGroupEvaluationByIds(new ArrayList<>(evaluationIds));
            if(!com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig.optimizeGetEvaluationsBySkippingHydrate()){
                hydrateGroupEvaluations(evaluationsList, true);
            }
            evaluationsList.forEach(evaluation -> Hibernate.initialize(evaluation.getGroupEvaluationCosts()));
        }
        return getSortedEvaluationsList(evaluationIds, evaluationsList);
    }

    private static List<GroupEvaluation> getSortedEvaluationsList(List<Integer> evaluationIds, List<GroupEvaluation> evaluationsList) {
        Map<Integer, GroupEvaluation> grpEvl = evaluationsList.stream()
                .collect(Collectors.toMap(groupEvaluation -> groupEvaluation.getId(), Function.identity()));
        List<GroupEvaluation> sortedEvaluationsList = new ArrayList<>();
        evaluationIds.forEach(e -> sortedEvaluationsList.add(grpEvl.get(e)));
        return sortedEvaluationsList;
    }


    private List<GroupEvaluation> getGroupEvaluationByIds(List<Integer> groupEvaluationIds) {
        return tenantCrudService.findByNamedQuery(FIND_GE_BY_IDS, QueryParameter.with("groupEvaluationIds", groupEvaluationIds).parameters());
    }

    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }

    public BigDecimal getPerRoomServicingCost(GroupEvaluation groupEvaluation) {
        // Set the per-room servicing cost
        BigDecimal perRoomServicingCost = null;
        if (GroupPricingEvaluationMethod.ROH.equals(groupEvaluation.getEvaluationMethod())) {
            perRoomServicingCost = servicingCostByLOSService.isProfitOptimizationEnabled() ?
                    servicingCostByLOSService.getROHServicingCostConfiguration(groupEvaluation.getPropertyId()).getFullTurnServicingCost()
                    : groupPricingConfigurationService.getROHGroupPricingConfigurationForProperty(groupEvaluation.getPropertyId()).getPerRoomServicingCost();

        }
        return perRoomServicingCost;
    }

    public BigDecimal getPerRoomServicingCostUsingTenant(GroupEvaluation groupEvaluation) {
        // Set the per-room servicing cost
        BigDecimal perRoomServicingCost = null;
        if (GroupPricingEvaluationMethod.ROH.equals(groupEvaluation.getEvaluationMethod())) {
            perRoomServicingCost = servicingCostByLOSService.isProfitOptimizationEnabled() ?
                    servicingCostByLOSService.getROHServicingCostConfiguration(groupEvaluation.getPropertyId()).getFullTurnServicingCost()
                    : groupPricingConfigurationService.getROHGroupPricingConfigurationForPropertyUsingTenant(groupEvaluation.getPropertyId()).getPerRoomServicingCost();

        }
        return perRoomServicingCost;
    }

    public GroupEvaluation mock_evaluate(GroupEvaluation groupEvaluation) {
        groupEvaluationSasInvocationService.evaluateSynchronous(groupEvaluation, true);
        setPerRoomServicingCostTo(groupEvaluation);

        return groupEvaluation;
    }

    private void resetResultCode(GroupEvaluation groupEvaluation) {
        // Iterate over all of the arrival dates. First set the result code to null, as we are doing a new evaluation
        // and there are some pre-evaluation checks that need to be run that depend on result code.
        for (GroupEvaluationArrivalDate groupEvaluationArrivalDate : groupEvaluation.getGroupEvaluationArrivalDates()) {

            // Set all the result codes to null
            groupEvaluationArrivalDate.setResultCode(null);

            // Get the day of stay objects
            groupEvaluation.getGroupEvaluationDayOfStays();
        }
    }

    private Integer getRohPhysicalCapacityForProperty(Integer propertyId) {
        String sql = "select dbo.ufn_get_roh_physical_capacity(:propertyId)";
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(PROPERTY_ID, propertyId);

        return tenantCrudService.findByNativeQuerySingleResult(sql, parameters);
    }

    private List<MarketSegmentSummary> getMarketSegmentsForGroupForecastType(List<Integer> propertyIds) {
        MarketSegmentSummaryCriteria marketSegmentSummaryCriteria = new MarketSegmentSummaryCriteria();
        marketSegmentSummaryCriteria.setPropertyIds(propertyIds);
        marketSegmentSummaryCriteria.setForecastGroupTypeId(ForecastType.GROUP_FORECAST_TYPE_ID);

        List<String> projections = new ArrayList<>();
        projections.add("id");
        projections.add("name");
        projections.add("code");

        return tenantCrudService.findByCriteria(marketSegmentSummaryCriteria, projections);
    }

    public List<MarketSegmentSummary> getGroupMarketSegmentsInDescOrderByVolume(Integer propertyId) {
        return tenantCrudService.findByNamedQuery(MarketSegmentSummary.FIND_GROUP_MARKET_SEGMENTS_IN_DESC_ORDER, QueryParameter.with("propertyId", propertyId).parameters())
                .stream()
                .map(o -> {
                    Object[] obj = (Object[]) o; // cast it here
                    MarketSegmentSummary mss = new MarketSegmentSummary();
                    mss.setId(((Number) obj[0]).intValue());
                    mss.setCode(obj[1].toString());
                    return mss;
                })
                .collect(Collectors.toList());
    }

    public List<MarketSegmentSummary> getMarketSegmentsForGroupForecastTypeAndStatus(List<Integer> propertyIds, boolean onlyActiveMktSeg) {
        MarketSegmentSummaryCriteria marketSegmentSummaryCriteria = new MarketSegmentSummaryCriteria();
        marketSegmentSummaryCriteria.setPropertyIds(propertyIds);
        marketSegmentSummaryCriteria.setForecastGroupTypeId(ForecastType.GROUP_FORECAST_TYPE_ID);
        if (onlyActiveMktSeg) {
            marketSegmentSummaryCriteria.setStatusId(Constants.ACTIVE_STATUS_ID);
        }

        List<String> projections = new ArrayList<>();
        projections.add("id");
        projections.add("name");
        projections.add("code");

        return tenantCrudService.findByCriteria(marketSegmentSummaryCriteria, projections);
    }

    public void hydrateGroupEvaluations(List<GroupEvaluation> list, boolean hydrateForecastGroups) {
        long startTime = System.currentTimeMillis();
        boolean performanceImprovementFunctionSpaceLandingPageIsEnabled = isPerformanceImprovementFunctionSpaceLandingPageIsEnabled();

        for (GroupEvaluation groupEvaluation : list) {

            Set<GroupEvaluationArrivalDate> groupEvaluationArrivalDates = groupEvaluation
                    .getGroupEvaluationArrivalDates();
            Hibernate.initialize(groupEvaluationArrivalDates);
            Set<GroupEvaluationDayOfStay> groupEvaluationDayOfStays = groupEvaluation.getGroupEvaluationDayOfStays();
            Hibernate.initialize(groupEvaluationDayOfStays);

            loadGroupEvaluationOnBooks(groupEvaluation);
            loadGroupEvaluationOnBooksCurrentBARList(groupEvaluation);

            long arrivalDateStartTime = System.currentTimeMillis();
            for (GroupEvaluationArrivalDate groupEvaluationArrivalDate : groupEvaluationArrivalDates) {
                // only hydrate on loading single group evaluation - i.e. navigating to details
                if (hydrateForecastGroups) {
                    loadForecastGroupDetails(groupEvaluationArrivalDate);
                }
                loadGuestRoomRatesByOccupancyDateDetails(groupEvaluationArrivalDate);

                if (performanceImprovementFunctionSpaceLandingPageIsEnabled) {
                    // function space arrival date
                    if (hydrateForecastGroups) {
                        loadFunctionSpaceArrivalDate(groupEvaluationArrivalDate);
                    }
                } else {
                    loadFunctionSpaceArrivalDate(groupEvaluationArrivalDate);
                }

                // Hydrate Arrival Date Accomodation Classes
                List<GroupEvaluationArrivalDateAccomClass> groupEvaluationArrivalDateAccomClasses = groupEvaluationArrivalDate
                        .getGroupEvaluationArrivalDateAccomClasses();

                if (isNotEmpty(groupEvaluationArrivalDateAccomClasses)) {
                    Hibernate.initialize(groupEvaluationArrivalDateAccomClasses);

                    for (GroupEvaluationArrivalDateAccomClass groupEvaluationArrivalDateAccomClass : groupEvaluationArrivalDateAccomClasses) {
                        Hibernate.initialize(groupEvaluationArrivalDateAccomClass.getAccomClass());
                        Hibernate.initialize(groupEvaluationArrivalDateAccomClass.getGroupEvaluationArrivalDateAccomTypes());
                    }
                }

                // User adjustment
                List<GroupEvaluationArrivalDateUserAdjustment> groupEvaluationArrivalDateUserAdjustments = groupEvaluationArrivalDate
                        .getGroupEvaluationArrivalDateUserAdjustments();
                Hibernate.initialize(groupEvaluationArrivalDateUserAdjustments);
                if (groupEvaluationArrivalDateUserAdjustments != null) {
                    for (GroupEvaluationArrivalDateUserAdjustment groupEvaluationArrivalDateUserAdjustment : groupEvaluationArrivalDateUserAdjustments) {
                        Hibernate.initialize(groupEvaluationArrivalDateUserAdjustment);
                    }
                }
            }
            long arrivalDateEndTime = System.currentTimeMillis();
            LOGGER.info("Group Evaluation ArrivalDate for: "+groupEvaluation.getGroupName()+" "+(arrivalDateEndTime-arrivalDateStartTime));

            Set<GroupEvaluationFunctionSpace> groupEvaluationFunctionSpaces = groupEvaluation
                    .getGroupEvaluationFunctionSpaces();
            Hibernate.initialize(groupEvaluationFunctionSpaces);
            if (performanceImprovementFunctionSpaceLandingPageIsEnabled) {
                if (hydrateForecastGroups) {
                    // function space saved evaluations
                    loadFunctionSpaceFunctionRoom(groupEvaluationFunctionSpaces);
                }
            } else {
                // function space saved evaluations
                loadFunctionSpaceFunctionRoom(groupEvaluationFunctionSpaces);
            }

            // function space conference and banquets
            Set<GroupEvaluationFunctionSpaceConfAndBanq> groupEvaluationFunctionSpaceConfAndBanquets = groupEvaluation
                    .getGroupEvaluationFunctionSpaceConfAndBanquets();
            Hibernate.initialize(groupEvaluationFunctionSpaceConfAndBanquets);
            if (groupEvaluationFunctionSpaceConfAndBanquets != null) {
                for (GroupEvaluationFunctionSpaceConfAndBanq groupEvaluationFunctionSpaceConfAndBanquet : groupEvaluationFunctionSpaceConfAndBanquets) {
                    if (groupEvaluationFunctionSpaceConfAndBanquet.getFunctionSpaceRevenueGroup() != null) {
                        Hibernate.initialize(groupEvaluationFunctionSpaceConfAndBanquet.getFunctionSpaceRevenueGroup()
                                .getResourceType());
                    }
                }
            }

            loadRoomTypes(groupEvaluation);

            hydratePackageDetails(groupEvaluation);

            hydrateEvaluationApprovals(groupEvaluation);
        }

        setRoomServiceCosts(list);
        long endTime = System.currentTimeMillis();

        LOGGER.info("Total hydration time: " +(endTime-startTime));
    }

    private void hydratePackageDetails(GroupEvaluation groupEvaluation) {
        Set<GroupEvaluationArrivalDate> groupEvaluationArrivalDates = groupEvaluation
                .getGroupEvaluationArrivalDates();
        Hibernate.initialize(groupEvaluationArrivalDates);

        for (GroupEvaluationArrivalDate groupEvaluationArrivalDate : groupEvaluationArrivalDates) {
            loadGroupEvaluationFunctionSpaceArrivalDatePackage(groupEvaluationArrivalDate);
            loadGroupEvaluationGroupPricingRevenueByArrivalDate(groupEvaluationArrivalDate);
        }

        loadFunctionSpaceEvalPackagePricing(groupEvaluation);
        loadGroupPricingEvalPackagePricing(groupEvaluation);
        loadGroupEvaluationFunctionSpacePackageDetail(groupEvaluation);
        loadGroupEvaluationGroupPricingPackageEntities(groupEvaluation);
    }

    private void hydrateEvaluationApprovals(GroupEvaluation groupEvaluation) {
        Set<GroupEvaluationApproval> groupEvaluationApprovals = groupEvaluation.getGroupEvaluationApprovals();
        Hibernate.initialize(groupEvaluationApprovals);
    }

    private boolean isPerformanceImprovementFunctionSpaceLandingPageIsEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.PERFORMANCE_IMPROVEMENT_FUNCTION_SPACE_LANDING_PAGE);
    }

    private void loadFunctionSpaceFunctionRoom(Set<GroupEvaluationFunctionSpace> groupEvaluationFunctionSpaces) {
        long startTime = System.currentTimeMillis();
        for (GroupEvaluationFunctionSpace functionSpace : groupEvaluationFunctionSpaces) {

            Hibernate.initialize(functionSpace);
            Set<GroupEvaluationFunctionSpaceFunctionRoom> functionRooms = functionSpace
                    .getGroupEvaluationFunctionSpaceFunctionRooms();
            Hibernate.initialize(functionRooms);

            for (GroupEvaluationFunctionSpaceFunctionRoom functionRoom : functionRooms) {
                Hibernate.initialize(functionRoom.getFunctionSpaceFunctionRoom());

                FunctionSpaceFunctionRoom functionSpaceRoom = functionRoom.getFunctionSpaceFunctionRoom();
                // handle combo room and its indivisible parts
                if (functionSpaceRoom.isCombo()) {
                    FunctionSpaceCombinationFunctionRoom comboRoom = getCombinationRoom(functionSpaceRoom.getId());
                    Hibernate.initialize(comboRoom.getIndivisibleFunctionRoomParts());
                    functionRoom.setFunctionSpaceFunctionRoom(comboRoom);

                    for (FunctionSpaceFunctionRoom indivisibleRoom : comboRoom.getIndivisibleFunctionRoomParts()) {
                        Hibernate.initialize(indivisibleRoom.getFunctionSpaceFunctionRoomMARSeasons());
                    }
                } else {
                    Hibernate.initialize(
                            functionRoom.getFunctionSpaceFunctionRoom().getFunctionSpaceFunctionRoomMARSeasons());
                }
            }
        }
        long endTime = System.currentTimeMillis();
        LOGGER.info("FunctionSpace FunctionRoom time for: " + (endTime - startTime));
    }

    private static void loadFunctionSpaceArrivalDate(GroupEvaluationArrivalDate groupEvaluationArrivalDate) {
        long startTime = System.currentTimeMillis();
        List<GroupEvaluationFunctionSpaceArrivalDate> groupEvaluationFuctionSpaceArrivalDates = groupEvaluationArrivalDate
                .getGroupEvaluationFunctionSpaceArrivalDates();
        Hibernate.initialize(groupEvaluationFuctionSpaceArrivalDates);

        for (GroupEvaluationFunctionSpaceArrivalDate groupEvaluationFuctionSpaceArrivalDate : groupEvaluationFuctionSpaceArrivalDates) {
            Hibernate.initialize(groupEvaluationFuctionSpaceArrivalDate
                    .getGroupEvaluationFunctionSpaceArrivalDateDayParts());
            List<GroupEvaluationFunctionSpaceArrivalDateDayPart> dayParts = groupEvaluationFuctionSpaceArrivalDate
                    .getGroupEvaluationFunctionSpaceArrivalDateDayParts();
            for (GroupEvaluationFunctionSpaceArrivalDateDayPart dayPart : dayParts) {
                Hibernate.initialize(dayPart.getFunctionSpaceDayPart());
            }
        }
        long endTime = System.currentTimeMillis();
        LOGGER.info("FunctionSpaceArrivalDate time for: "+(endTime-startTime));
    }


    private void loadGroupEvaluationGroupPricingPackageEntities(GroupEvaluation groupEvaluation) {
        Set<GroupEvaluationGroupPricingPackageDetail> groupPricingPackageDetails = groupEvaluation.getGroupEvaluationGroupPricingPackageDetails();
        if (nonNull(groupPricingPackageDetails)) {
            Hibernate.initialize(groupPricingPackageDetails);
            for (GroupEvaluationGroupPricingPackageDetail groupPricingPackageDetail : groupPricingPackageDetails) {
                Hibernate.initialize(groupPricingPackageDetail);
                Hibernate.initialize(groupPricingPackageDetail.getGroupPricingEvalPackagePricing());
                initializeGroupPricingPackageRevenueByArrivalDate(groupPricingPackageDetail.getPackageRevenueByArrivalDates());
                initializeGroupPricingPackageRevenueByRevenueGroup(groupPricingPackageDetail.getRevenueByRevenueGroups());
            }
        }
    }

    private void initializeGroupPricingPackageRevenueByRevenueGroup(Set<GroupEvaluationGroupPricingPackageRevenueByRevenueGroup> revenueByRevenueGroups) {
        if (nonNull(revenueByRevenueGroups)) {
            Hibernate.initialize(revenueByRevenueGroups.size());
        }
    }

    private void initializeGroupPricingPackageRevenueByArrivalDate(Set<GroupEvaluationGroupPricingPackageRevenueByArrivalDate> packageRevenueByArrivalDates) {
        if (nonNull(packageRevenueByArrivalDates)) {
            Hibernate.initialize(packageRevenueByArrivalDates.size());
        }
    }

    private void loadGroupEvaluationFunctionSpacePackageDetail(GroupEvaluation groupEvaluation) {
        Set<GroupEvaluationFunctionSpacePackageDetail> groupEvaluationFunctionSpacePackageDetails = groupEvaluation.getGroupEvaluationFunctionSpacePackageDetails();
        if (groupEvaluationFunctionSpacePackageDetails != null) {
            Hibernate.initialize(groupEvaluationFunctionSpacePackageDetails);
            for (GroupEvaluationFunctionSpacePackageDetail groupEvaluationFunctionSpacePackageDetail : groupEvaluationFunctionSpacePackageDetails) {
                Hibernate.initialize(groupEvaluationFunctionSpacePackageDetail);
                Hibernate.initialize(groupEvaluationFunctionSpacePackageDetail.getFunctionSpacePackage());
                Set<GroupEvaluationFunctionSpaceArrivalDatePackage> groupEvaluationFunctionSpaceArrivalDatePackages =
                        groupEvaluationFunctionSpacePackageDetail.getGroupEvaluationFunctionSpaceArrivalDatePackages();
                if (groupEvaluationFunctionSpaceArrivalDatePackages != null) {
                    Hibernate.initialize(groupEvaluationFunctionSpaceArrivalDatePackages.size());
                }
                Set<GroupEvaluationFunctionSpacePackageDayOfStay> groupEvaluationFunctionSpacePackageDayOfStaySet =
                        groupEvaluationFunctionSpacePackageDetail.getGroupEvaluationFunctionSpacePackageDayOfStays();
                if (groupEvaluationFunctionSpacePackageDayOfStaySet != null) {
                    Hibernate.initialize(groupEvaluationFunctionSpacePackageDayOfStaySet.size());
                }
                Set<GroupEvaluationFunctionSpacePackageRevenueGroup> groupEvaluationFunctionSpacePackageRevenueGroupSet =
                        groupEvaluationFunctionSpacePackageDetail.getGroupEvaluationFunctionSpacePackageRevenueGroups();
                if (groupEvaluationFunctionSpacePackageRevenueGroupSet != null) {
                    Hibernate.initialize(groupEvaluationFunctionSpacePackageRevenueGroupSet.size());
                }
            }
        }
    }

    private void loadGroupEvaluationFunctionSpaceArrivalDatePackage(GroupEvaluationArrivalDate groupEvaluationArrivalDate) {
        List<GroupEvaluationFunctionSpaceArrivalDatePackage> groupEvaluationFunctionSpaceArrivalDatePackages =
                groupEvaluationArrivalDate.getGroupEvaluationFunctionSpaceArrivalDatePackages();

        if (groupEvaluationFunctionSpaceArrivalDatePackages != null) {
            Hibernate.initialize(groupEvaluationFunctionSpaceArrivalDatePackages);
            for (GroupEvaluationFunctionSpaceArrivalDatePackage groupEvaluationFunctionSpaceArrivalDatePackage : groupEvaluationFunctionSpaceArrivalDatePackages) {
                Hibernate.initialize(groupEvaluationFunctionSpaceArrivalDatePackage);
            }
        }
    }

    private void loadGroupEvaluationGroupPricingRevenueByArrivalDate(GroupEvaluationArrivalDate groupEvaluationArrivalDate) {
        List<GroupEvaluationGroupPricingPackageRevenueByArrivalDate> groupPricingPackageRevenueByArrivalDates =
                groupEvaluationArrivalDate.getGroupEvaluationGroupPricingPackageRevenueByArrivalDates();

        if (groupPricingPackageRevenueByArrivalDates != null) {
            Hibernate.initialize(groupPricingPackageRevenueByArrivalDates);
            for (GroupEvaluationGroupPricingPackageRevenueByArrivalDate groupPricingPackageRevenueByArrivalDate : groupPricingPackageRevenueByArrivalDates) {
                Hibernate.initialize(groupPricingPackageRevenueByArrivalDate);
            }
        }
    }

    private void loadFunctionSpaceEvalPackagePricing(GroupEvaluation groupEvaluation) {
        Set<FunctionSpaceEvalPackagePricing> functionSpaceEvalPackagePricings = groupEvaluation.getFunctionSpaceEvalPackagePricings();

        if (functionSpaceEvalPackagePricings != null) {
            Hibernate.initialize(functionSpaceEvalPackagePricings);
            for (FunctionSpaceEvalPackagePricing functionSpaceEvalPackagePricing : functionSpaceEvalPackagePricings) {
                Hibernate.initialize(functionSpaceEvalPackagePricing);
                Hibernate.initialize(functionSpaceEvalPackagePricing.getFunctionSpacePackageType());
                Hibernate.initialize(functionSpaceEvalPackagePricing.getFunctionSpacePackage());
                Hibernate.initialize(functionSpaceEvalPackagePricing.getFunctionSpacePackage().getPackageType());
                Set<FunctionSpaceEvalPackagePricingDOS> packagePricingDOSList = functionSpaceEvalPackagePricing.getPackagePricingDOSList();
                if (packagePricingDOSList != null) {
                    Hibernate.initialize(packagePricingDOSList);
                    for (FunctionSpaceEvalPackagePricingDOS functionSpaceEvalPackagePricingDOS : packagePricingDOSList) {
                        Hibernate.initialize(functionSpaceEvalPackagePricingDOS);
                    }
                }
            }
        }
    }

    private void loadGroupPricingEvalPackagePricing(GroupEvaluation groupEvaluation) {
        Set<GroupPricingEvalPackagePricing> groupPricingEvalPackagePricings = groupEvaluation.getGroupPricingEvalPackagePricings();

        if (groupPricingEvalPackagePricings != null) {
            Hibernate.initialize(groupPricingEvalPackagePricings);
            for (GroupPricingEvalPackagePricing groupPricingEvalPackagePricing : groupPricingEvalPackagePricings) {
                Hibernate.initialize(groupPricingEvalPackagePricing);
                Hibernate.initialize(groupPricingEvalPackagePricing.getFunctionSpacePackageType());
                Hibernate.initialize(groupPricingEvalPackagePricing.getGroupPricingConfigurationPackage());
                Hibernate.initialize(groupPricingEvalPackagePricing.getGroupPricingConfigurationPackage().getPackageType());
                Set<GroupPricingEvalPackagePricingDOS> packagePricingDOSList = groupPricingEvalPackagePricing.getPackagePricingDOSList();
                if (packagePricingDOSList != null) {
                    Hibernate.initialize(packagePricingDOSList);
                    for (GroupPricingEvalPackagePricingDOS groupPricingEvalPackagePricingDOS : packagePricingDOSList) {
                        Hibernate.initialize(groupPricingEvalPackagePricingDOS);
                    }
                }
            }
        }
    }

    protected void loadGroupEvaluationOnBooks(GroupEvaluation groupEvaluation) {
        Set<GroupEvaluationOnBooks> groupEvaluationOnBooksList = groupEvaluation.getGroupEvaluationOnBooks();
        if (isNotEmpty(groupEvaluationOnBooksList)) {
            Hibernate.initialize(groupEvaluationOnBooksList);
            for (GroupEvaluationOnBooks groupEvaluationOnBooks : groupEvaluationOnBooksList) {
                Hibernate.initialize(groupEvaluationOnBooks);
            }
        }
    }

    protected void loadGroupEvaluationOnBooksCurrentBARList(GroupEvaluation groupEvaluation) {
        final Set<GroupEvaluationOnBooksCurrentBAR> groupEvaluationOnBooksCurrentBARSet = groupEvaluation.getGroupEvaluationOnBooksCurrentBARSet();
        if (isNotEmpty(groupEvaluationOnBooksCurrentBARSet)) {
            Hibernate.initialize(groupEvaluationOnBooksCurrentBARSet);
            for (GroupEvaluationOnBooksCurrentBAR groupEvaluationOnBooksCurrentBAR : groupEvaluationOnBooksCurrentBARSet) {
                Hibernate.initialize(groupEvaluationOnBooksCurrentBAR);
            }
        }
    }

    protected void loadGuestRoomRatesByOccupancyDateDetails(GroupEvaluationArrivalDate groupEvaluationArrivalDate) {
        List<GroupEvaluationArrivalDateGuestRoomRates> arrivalDateGuestRoomRates = groupEvaluationArrivalDate.getGroupEvaluationArrivalDateGuestRoomRates();
        if (!arrivalDateGuestRoomRates.isEmpty()) {
            Hibernate.initialize(arrivalDateGuestRoomRates);
            for (GroupEvaluationArrivalDateGuestRoomRates guestRoomRates : arrivalDateGuestRoomRates) {
                Hibernate.initialize(guestRoomRates);
            }
        }
    }

    private void setRoomServiceCosts(List<GroupEvaluation> groupEvaluations) {
        Map<AccomClass, BigDecimal> perRoomServicingCostByRoomTypes = new HashMap<>();

        for (GroupEvaluation groupEvaluation : groupEvaluations) {
            if (GroupPricingEvaluationMethod.RC.equals(groupEvaluation.getEvaluationMethod())) {
                if (MapUtils.isEmpty(perRoomServicingCostByRoomTypes)) {
                    perRoomServicingCostByRoomTypes = getPerRoomServicingCostByRoomType();
                }
                groupEvaluation.setPerRoomServingCostByRoomType(perRoomServicingCostByRoomTypes);
            }
        }
    }

    protected void loadForecastGroupDetails(GroupEvaluationArrivalDate groupEvaluationArrivalDate) {
        List<GroupEvaluationArrivalDateForecastGroup> groupEvaluationArrivalDateForecastGroups = groupEvaluationArrivalDate
                .getGroupEvaluationArrivalDateForecastGroups();
        Hibernate.initialize(groupEvaluationArrivalDateForecastGroups);

        for (GroupEvaluationArrivalDateForecastGroup groupEvaluationArrivalDateForecastGroup : groupEvaluationArrivalDateForecastGroups) {
            Hibernate.initialize(groupEvaluationArrivalDateForecastGroup.getForecastGroup());
            Hibernate.initialize(groupEvaluationArrivalDateForecastGroup.getGroupEvaluationArrivalDateForecastGroupDatesROH());
            List<GroupEvaluationArrivalDateForecastGroupDateAC> forecastGroupDateACS = groupEvaluationArrivalDateForecastGroup.getGroupEvaluationArrivalDateForecastGroupDateACs();
            if (isNotEmpty(forecastGroupDateACS)) {
                forecastGroupDateACS.forEach(ac -> ac.getAccomClass().getName());
            }
        }
    }

    private void loadRoomTypes(GroupEvaluation groupEvaluation) {
        Set<GroupEvaluationRoomType> groupEvaluationRoomTypes = groupEvaluation.getGroupEvaluationRoomTypes();
        if (groupEvaluationRoomTypes != null) {
            Hibernate.initialize(groupEvaluationRoomTypes);

            for (GroupEvaluationRoomType groupEvaluationRoomType : groupEvaluationRoomTypes) {
                Hibernate.initialize(groupEvaluationRoomType.getRoomType());
                Hibernate.initialize(groupEvaluationRoomType.getRoomType().getAccomClass());
                List<GroupEvaluationRoomTypeDayOfStay> dayOfStays = groupEvaluationRoomType
                        .getGroupEvaluationRoomTypeDayOfStays();
                if (isNotEmpty(dayOfStays)) {
                    Hibernate.initialize(dayOfStays);
                }
            }
        }
    }

    public boolean isPropertyOneWayOrTwoWay() {
        Property property = propertyService.getPropertyById(PacmanThreadLocalContextHolder.getWorkContext().getPropertyId());
        return property.getStage() != null && (property.getStage().equals(Stage.ONE_WAY) ||
                property.getStage().equals(Stage.TWO_WAY));
    }

    public List<GroupEvaluation> getSavedGroupEvaluationsForDateRange(Date startDate, int startPosition, int size) {
        if (startDate == null) {
            throw new TetrisException("startDate cannot be null");
        }
        Map<String, Object> parameters = getQueryParametersForSavedGroupEvaluations(startDate);
        List<GroupEvaluation> groupEvaluations = tenantCrudService.
                findByNamedQuery(GroupEvaluation.FIND_BY_DATE_RANGE, parameters, startPosition, size);

        if (CollectionUtils.isEmpty(groupEvaluations)) {
            return Collections.emptyList();
        }
        List<GroupEvaluation> groupEvaluationsWithArrivalDatesAC = tenantCrudService.
                findByNamedQuery(GroupEvaluation.FIND_GE_ARRIVAL_DATES_ACCOM_CLASS,
                        QueryParameter.with("groupEvaluations", groupEvaluations)
                                .parameters(), startPosition, size);

        List<GroupEvaluation> groupEvaluationsWithForecastGroup = tenantCrudService.
                findByNamedQuery(GroupEvaluation.FIND_GE_ARRIVAL_DATES_FORECAST_GROUP,
                        QueryParameter.with("groupEvaluations", groupEvaluationsWithArrivalDatesAC)
                                .parameters(), startPosition, size);

        Map<AccomClass, BigDecimal> perRoomServicingCostByRoomType = getPerRoomServicingCostByRoomType();
        groupEvaluationsWithForecastGroup.stream().
                filter(groupEvaluation -> GroupPricingEvaluationMethod.RC.equals(groupEvaluation.getEvaluationMethod())).
                forEach(groupEvaluation -> groupEvaluation.setPerRoomServingCostByRoomType(perRoomServicingCostByRoomType));
        return groupEvaluationsWithForecastGroup;
    }

    private Map<String, Object> getQueryParametersForSavedGroupEvaluations(Date startDate) {
        LocalDateTime startDateTime = LocalDateTime.fromDateFields(startDate);
        return QueryParameter.with(START_DATE, startDateTime).parameters();
    }

    public Map<Integer, String> getUserIdToFullnameMapForGroupEvaluations(List<GroupEvaluation> groupEvaluations) {
        List<Integer> evaluationUserIds = groupEvaluations.stream().map(GroupEvaluation::getLastUpdatedByUserId).collect(Collectors.toList());
        if (evaluationUserIds.isEmpty()) {
            return Collections.emptyMap();
        }
        return getUserIdToFullNameMapForGroupEvaluationIDs(evaluationUserIds);
    }

    public Map<Integer, String> getUserIdToFullNameMapForGroupEvaluationIDs(List<Integer> evaluationUserIds) {
        GlobalUserCriteria globalUserCriteria = new GlobalUserCriteria();
        globalUserCriteria.setIds(evaluationUserIds);
        List<GlobalUser> propertyUsers = userService.getGlobalUsers(globalUserCriteria);
        return propertyUsers.stream().collect(Collectors.toMap(GlobalUser::getId
                , user -> user.getFirstName() + " " + user.getLastName()));
    }

    public List<GroupBlockMaster> getGroupMasterDataByDate(LocalDate date, LocalDate caughtUpDate) {
        return tenantCrudService.findByNamedQuery(GroupBlockMaster.BY_DATE,
                QueryParameter
                        .with("date", date.toDate())
                        .and("caughtUpDate", caughtUpDate.toDate())
                        .parameters()
        );
    }

    public List<GroupBlockMaster> getGroupMasterDataByCode(String groupCode, LocalDate caughtUpDate) {
        String groupCodeString = "%" + groupCode + "%";
        return tenantCrudService.findByNamedQuery(GroupBlockMaster.BY_CODE_USING_LKE,
                QueryParameter
                        .with("code", groupCodeString)
                        .and("caughtUpDate", caughtUpDate.toDate())
                        .parameters());
    }

    public List<GroupBlockMaster> getGroupMasterDataByName(String groupName, LocalDate caughtUpDate) {
        String groupNameString = "%" + groupName + "%";

        return tenantCrudService.findByNamedQuery(GroupBlockMaster.BY_NAME,
                QueryParameter
                        .with("name", groupNameString)
                        .and("caughtUpDate", caughtUpDate.toDate())
                        .parameters());
    }


    public List<GroupEvaluationOnBooksCurrentBAR> populateAndSaveCurrentBarInformation(GroupEvaluation groupEvaluation) {
        LocalDate startDate = groupEvaluation.getEarliestArrivalDate();
        LocalDate endDate = startDate.plusDays(groupEvaluation.getNumberOfNights());
        final List<Object[]> resultList = getCPDecisionBarOutputData(startDate, endDate);
        Map<Integer, AccomClass> accomClassCache = new HashMap();
        List<GroupEvaluationOnBooksCurrentBAR> groupEvaluationOnBooksCurrentBARList = new ArrayList<>();
        if(isNotEmpty(resultList)) {
            resultList.forEach(objects -> {
                GroupEvaluationOnBooksCurrentBAR groupEvaluationOnBooksCurrentBAR = new GroupEvaluationOnBooksCurrentBAR();
                final Integer accomClassId = (Integer) objects[1];
                AccomClass accomClass = accomClassCache.get(accomClassId);

                if (Objects.isNull(accomClass)) {
                    accomClass = tenantCrudService.find(AccomClass.class, accomClassId);
                    accomClassCache.put(accomClassId, accomClass);
                }

                groupEvaluationOnBooksCurrentBAR.setOccupancyDate(LocalDate.fromDateFields((Date) objects[0]));
                groupEvaluationOnBooksCurrentBAR.setCurrentBAR(Objects.isNull(objects[4]) ? null : ((BigDecimal) objects[4]).setScale(2, RoundingMode.HALF_UP));
                groupEvaluationOnBooksCurrentBAR.setGroupEvaluation(groupEvaluation);
                groupEvaluationOnBooksCurrentBAR.setAccomClass(accomClass);
                groupEvaluationOnBooksCurrentBARList.add(groupEvaluationOnBooksCurrentBAR);

            });
            return groupEvaluationOnBooksCurrentBARList;
        }
        return Collections.emptyList();
    }

    public void addCurrentBarDataToGroupEvaluation(List<CurrentBarDTO> currentBarDTOList, GroupEvaluation groupEvaluation) {
        if(isNotEmpty(currentBarDTOList)) {
            Map<Integer, AccomClass> accomClassCache = new HashMap<>();
            List<GroupEvaluationOnBooksCurrentBAR> groupEvaluationOnBooksCurrentBARList = new ArrayList<>();
            for (CurrentBarDTO currentBarDTO : currentBarDTOList) {
                final Integer accomClassId = currentBarDTO.getAccomClassId();
                AccomClass accomClass = accomClassCache.get(accomClassId);

                if (Objects.isNull(accomClass)) {
                    accomClass = tenantCrudService.find(AccomClass.class, accomClassId);
                    accomClassCache.put(accomClassId, accomClass);
                }

                for (CurrentBarDetails currentBarDetails : currentBarDTO.getCurrentBarDetailsList()) {
                    GroupEvaluationOnBooksCurrentBAR groupEvaluationOnBooksCurrentBAR = new GroupEvaluationOnBooksCurrentBAR();
                    groupEvaluationOnBooksCurrentBAR.setAccomClass(accomClass);
                    groupEvaluationOnBooksCurrentBAR.setGroupEvaluation(groupEvaluation);
                    groupEvaluationOnBooksCurrentBAR.setCurrentBAR(currentBarDetails.getCurrentBar());
                    groupEvaluationOnBooksCurrentBAR.setOccupancyDate(currentBarDetails.getOccupancyDate());
                    groupEvaluationOnBooksCurrentBARList.add(groupEvaluationOnBooksCurrentBAR);
                }
            }
            groupEvaluation.setGroupEvaluationOnBooksCurrentBARSet(new LinkedHashSet<>(groupEvaluationOnBooksCurrentBARList));
        }
    }

    public List<CurrentBarDTO> populateCurrentBarDTOListUsingCPDecisionBAROutputByDateRange(LocalDate startDate, LocalDate endDate) {
        Map<Integer, CurrentBarDTO> currentBarDTOByAccomClassCodeMap = new HashMap<>();

        final List<Object[]> resultList = getCPDecisionBarOutputData(startDate, endDate);

        if(isNotEmpty(resultList)) {
            resultList.forEach(objects -> {
                final Integer accomClassId = (Integer) objects[1];
                CurrentBarDTO currentBarDTO = currentBarDTOByAccomClassCodeMap.get(accomClassId);
                CurrentBarDetails currentBarDetails =
                        new CurrentBarDetails(
                                LocalDate.fromDateFields((Date) objects[0]),
                                Objects.isNull(objects[4]) ? null : ((BigDecimal) objects[4]).setScale(2, RoundingMode.HALF_UP)
                        );
                if(nonNull(currentBarDTO)) {
                    currentBarDTO.getCurrentBarDetailsList().add(currentBarDetails);
                } else {
                    List<CurrentBarDetails> currentBarDetailsList = new ArrayList<>();
                    currentBarDetailsList.add(currentBarDetails);
                    currentBarDTO = new CurrentBarDTO((String) objects[2], (Integer) objects[1], (Integer) objects[3], currentBarDetailsList);
                    currentBarDTOByAccomClassCodeMap.put(accomClassId, currentBarDTO);
                }
            });
            return new ArrayList<>(currentBarDTOByAccomClassCodeMap.values());
        }
        return Collections.emptyList();
    }

    private List<Object[]> getCPDecisionBarOutputData(LocalDate startDate, LocalDate endDate) {
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("startDate", DateUtil.formatDate(startDate.toDate(), DateUtil.DEFAULT_DATE_FORMAT));
        parameters.put("endDate", DateUtil.formatDate(endDate.toDate(), DateUtil.DEFAULT_DATE_FORMAT));
        return tenantCrudService.findByNativeQuery(GET_DECISIONS_BY_BASE_ROOM_TYPE_WITH_DATES_BETWEEN,
                parameters);
    }

    public Date getAccomActivityMaxOccupancyDate() {
        return tenantCrudService.findByNamedQuerySingleResult(AccomActivity.MAX_OCCUPANCY_DATE);
    }

    public Date getMktSegAccomActivityMaxOccupancyDate() {
        return tenantCrudService.findByNamedQuerySingleResult(MktSegAccomActivity.MAX_OCCUPANCY_DATE);
    }

    public Integer getExtendedGPEMktAccomInventoryPopulationNumDays(Date mktSegAccomActivityMaxOccupancyDate) {
        int daysDiff = (int) DateUtil.getTotalNoOfDays(dateService.getCaughtUpDate(), mktSegAccomActivityMaxOccupancyDate);
        return getExtendedGPEPopulationDays() - daysDiff;
    }

    public Integer getExtendedGPEAccomInventoryPopulationNumDays(Date accomActivityMaxOccupancyDate) {
        int daysDiff = (int) DateUtil.getTotalNoOfDays(dateService.getCaughtUpDate(), accomActivityMaxOccupancyDate);
        return getExtendedGPEPopulationDays() - daysDiff;
    }

    private Integer getExtendedGPEPopulationDays() {
        int daysToCompensateForPopulationJobNotRunningFor10Days = 10;
        return getGroupPricingExtendedWindowDays() + daysToCompensateForPopulationJobNotRunningFor10Days;
    }

    public Integer getGroupPricingExtendedWindowDays() {
        return pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_EXTENDED_WINDOW_DAYS.value());
    }

    public List<BigDecimal> getFinalPriceForROH(LocalDate arrivalDate) {
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("arrivalDate", DateUtil.formatDate(arrivalDate.toDate(), DateUtil.DEFAULT_DATE_FORMAT));
        return tenantCrudService.findByNativeQuery(GET_FINAL_PRICE_FOR_ROH_BASED_ON_ARRIVAL_DATE,
                parameters);
    }

    public Map<Date, BigDecimal> getFinalPriceForROHForDateRange(Date startDate, Date endDate) {
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("startDate", DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT));
        parameters.put("endDate", DateUtil.formatDate(endDate, DateUtil.DEFAULT_DATE_FORMAT));
        Map<Date, BigDecimal> finalBarByDate = new HashMap<>();
        List<Object[]> results = tenantCrudService.findByNativeQuery(GET_FINAL_PRICE_FOR_ROH_FOR_ARRIVAL_DATE_RANGE,
                parameters);
        finalBarByDate = results.stream().collect(Collectors.toMap(row -> (Date) row[0], row -> (BigDecimal) row[1]));
        return finalBarByDate;
    }

    public List<BigDecimal> getFinalPriceForRC(LocalDate arrivalDate, Integer accomId) {
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("arrivalDate", DateUtil.formatDate(arrivalDate.toDate(), DateUtil.DEFAULT_DATE_FORMAT));
        parameters.put("accomId", accomId);
        return tenantCrudService.findByNativeQuery(GET_FINAL_PRICE_FOR_RC_BASED_ON_ARRIVAL_DATE_AND_ACCOMID,
                parameters);
    }

    public List<BarRate> getFinalPriceForRoomTypesForDateRange(Date startDate, Date endDate, List<Integer> accomTypeIds) {
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("startDate", DateUtil.formatDate(startDate, DateUtil.DEFAULT_DATE_FORMAT));
        parameters.put("endDate", DateUtil.formatDate(endDate, DateUtil.DEFAULT_DATE_FORMAT));
        parameters.put("accomTypeIds", accomTypeIds);
        List<BarRate> barRates = tenantCrudService.findByNativeQuery(GET_FINAL_PRICE_FOR_RC_FOR_ARRIVAL_DATE_RANGE,
                parameters, row -> {
                    Date date = (Date) row[0];
                    Integer roomTypeId = (Integer) row[1];
                    BigDecimal barRate = (BigDecimal) row[2];
                    return new BarRate(date, roomTypeId, barRate);
                });
        return isEmpty(barRates) ? Collections.emptyList() : barRates;
    }

    public boolean isAccomActivityOrMktAccomActivityTableNonEmpty() {
        return nonNull(getAccomActivityMaxOccupancyDate()) || nonNull(getMktSegAccomActivityMaxOccupancyDate());
    }

    public void disableExtendedGroupEvaluationAtPropertyLevel() {
        String context = pacmanConfigParamsService.propertyNode(PacmanWorkContextHelper.getWorkContext());
        ConfigParameter parameter = pacmanConfigParamsService.getParameter(IPConfigParamName.GP_USE_EXTENDED_WND.value());
        ConfigParameterValue parameterValue = pacmanConfigParamsService.getParameterValue(context, parameter);
        if (nonNull(parameterValue)) {
            parameterValue.setValue("false");
            pacmanConfigParamsService.updateParameterValue(parameterValue, false);
        }
    }

    public boolean isGroupPricingEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED);
    }

    public boolean isComponentRoomsEnabledEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED);
    }

    public Integer saveEvaluationRequest(GroupEvaluation groupEvaluation) {
        GroupEvaluationRequest groupEvaluationRequest = new GroupEvaluationRequest(serialize(groupEvaluation).getBytes(), Integer.valueOf(PacmanWorkContextHelper.getUserId()));
        GroupEvaluationRequest savedRequest = tenantCrudService.save(groupEvaluationRequest);
        return savedRequest.getId();
    }

    public GroupEvaluation getGroupEvaluationRequest(Integer evaluationRequestId) {
        GroupEvaluationRequest groupEvaluationRequest = tenantCrudService.findByNativeQuerySingleResult(
                "SELECT * FROM Group_Evaluation_Request WHERE Group_Evaluation_Request_ID = :grpEvalRequestID ",
                Map.of("grpEvalRequestID", evaluationRequestId),
                groupEvaluationRequestRowMapper());

        if (Objects.nonNull(groupEvaluationRequest)) {
            return groupEvaluationRequest.getGroupEvaluation();
        }

        return null;
    }

    private RowMapper<GroupEvaluationRequest> groupEvaluationRequestRowMapper() {
        return row -> {
            GroupEvaluationRequest evaluationRequest = new GroupEvaluationRequest();
            evaluationRequest.setId((Integer) row[0]);
            evaluationRequest.setEvaluationContent((byte[]) row[1]);
            return evaluationRequest;
        };
    }

    public void deleteGroupEvaluationRequest(Integer evaluationRequestId) {
        tenantCrudService.executeUpdateByNativeQuery("DELETE FROM Group_Evaluation_Request WHERE Group_Evaluation_Request_ID = :grpEvlRequestID",
                Map.of("grpEvlRequestID", evaluationRequestId));
    }

    public List<GroupEvaluationArrivalDate> getAllGroupEvaluationArrivalDatesToBeUpdated(Integer propertyId) {
        List<GroupEvaluationArrivalDate> groupEvaluationArrivalDateList = tenantCrudService.findByNamedQuery(GroupEvaluationArrivalDate.FIND_ALL_BY_PROPERTY_ID_TOTAL_ROOMS_NUMBER_OF_NIGHTS_CONTRACTED_REVENUE_TOTAL_PROFIT_TOTAL_PROFIT_PERCENTAGE, QueryParameter.with(PROPERTY_ID, propertyId).parameters());
        if (CollectionUtils.isNotEmpty(groupEvaluationArrivalDateList)) {
            hydrateGroupEvaluationsArrivalDate(groupEvaluationArrivalDateList);
            return groupEvaluationArrivalDateList;
        }
        return Collections.emptyList();
    }

    public void updateExistingGPAndFSEvaluationFields(List<GroupEvaluationArrivalDate> groupEvaluationArrivalDates) {
        if (CollectionUtils.isNotEmpty(groupEvaluationArrivalDates)) {
            groupEvaluationArrivalDateMapperService.mapGroupEvaluationArrivalDate(groupEvaluationArrivalDates);
            tenantCrudService.save(groupEvaluationArrivalDates);
        }
    }

    private void hydrateGroupEvaluationsArrivalDate(List<GroupEvaluationArrivalDate> groupEvaluationArrivalDates) {
        List<GroupEvaluation> groupEvaluations = new ArrayList<>();
        for (GroupEvaluationArrivalDate groupEvaluationArrivalDate : groupEvaluationArrivalDates) {

            loadGuestRoomRatesByOccupancyDateDetails(groupEvaluationArrivalDate);

            // Hydrate Arrival Date Accommodation Classes
            List<GroupEvaluationArrivalDateAccomClass> groupEvaluationArrivalDateAccomClasses = groupEvaluationArrivalDate
                    .getGroupEvaluationArrivalDateAccomClasses();

            if (isNotEmpty(groupEvaluationArrivalDateAccomClasses)) {
                Hibernate.initialize(groupEvaluationArrivalDateAccomClasses);

                for (GroupEvaluationArrivalDateAccomClass groupEvaluationArrivalDateAccomClass : groupEvaluationArrivalDateAccomClasses) {
                    Hibernate.initialize(groupEvaluationArrivalDateAccomClass.getAccomClass());
                    Hibernate.initialize(groupEvaluationArrivalDateAccomClass.getGroupEvaluationArrivalDateAccomTypes());
                }
            }

            // User adjustment
            List<GroupEvaluationArrivalDateUserAdjustment> groupEvaluationArrivalDateUserAdjustments = groupEvaluationArrivalDate
                    .getGroupEvaluationArrivalDateUserAdjustments();
            Hibernate.initialize(groupEvaluationArrivalDateUserAdjustments);
            if (groupEvaluationArrivalDateUserAdjustments != null) {

                for (GroupEvaluationArrivalDateUserAdjustment groupEvaluationArrivalDateUserAdjustment : groupEvaluationArrivalDateUserAdjustments) {
                    Hibernate.initialize(groupEvaluationArrivalDateUserAdjustment);
                }
            }

            GroupEvaluation groupEvaluation = hydrateGroupEvaluationUsingGroupEvaluationArrivalDate(groupEvaluationArrivalDate);
            groupEvaluations.add(groupEvaluation);

        }
        setRoomServiceCosts(groupEvaluations);
    }

    private GroupEvaluation hydrateGroupEvaluationUsingGroupEvaluationArrivalDate(GroupEvaluationArrivalDate groupEvaluationArrivalDate) {
        GroupEvaluation groupEvaluation = groupEvaluationArrivalDate.getGroupEvaluation();
        Hibernate.initialize(groupEvaluation);

        Set<GroupEvaluationCost> groupEvaluationCosts = groupEvaluation.getGroupEvaluationCosts();
        Hibernate.initialize(groupEvaluationCosts);

        Set<GroupEvaluationDayOfStay> groupEvaluationDayOfStays = groupEvaluation.getGroupEvaluationDayOfStays();
        Hibernate.initialize(groupEvaluationDayOfStays);

        // function space saved evaluations
        Set<GroupEvaluationFunctionSpace> groupEvaluationFunctionSpaces = groupEvaluation
                .getGroupEvaluationFunctionSpaces();
        Hibernate.initialize(groupEvaluationFunctionSpaces);
        for (GroupEvaluationFunctionSpace functionSpace : groupEvaluationFunctionSpaces) {
            Hibernate.initialize(functionSpace);
        }

        // function space conference and banquets
        Set<GroupEvaluationFunctionSpaceConfAndBanq> groupEvaluationFunctionSpaceConfAndBanquets = groupEvaluation
                .getGroupEvaluationFunctionSpaceConfAndBanquets();
        Hibernate.initialize(groupEvaluationFunctionSpaceConfAndBanquets);
        if (groupEvaluationFunctionSpaceConfAndBanquets != null) {

            for (GroupEvaluationFunctionSpaceConfAndBanq groupEvaluationFunctionSpaceConfAndBanquet : groupEvaluationFunctionSpaceConfAndBanquets) {
                if (groupEvaluationFunctionSpaceConfAndBanquet.getFunctionSpaceRevenueGroup() != null) {
                    Hibernate.initialize(groupEvaluationFunctionSpaceConfAndBanquet.getFunctionSpaceRevenueGroup()
                            .getResourceType());
                }
            }
        }

        loadRoomTypes(groupEvaluation);
        loadGroupEvaluationFunctionSpacePackageDetail(groupEvaluation);
        loadGroupEvaluationGroupPricingPackageEntities(groupEvaluation);
        return groupEvaluation;
    }


    public Long triggerFSAndGPEvaluationMigrationJob(Integer propertyId) {
        Map<String, Object> jobParams = new HashMap<>();
        jobParams.put(JobParameterKey.PROPERTY_ID, propertyId);
        return jobService.startGuaranteedNewInstance(JobName.MigrationGPAndFunctionSpaceEvaluationsJob, jobParams);
    }

    public boolean salesAndCateringUrlRedirectEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SALES_AND_CATERING_URL_REDIRECT_ENABLED);
    }

    public boolean disAllowNegativeValueForUserAdjustedFSEval() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.DISALLOW_NEGATIVE_VALUES_USER_ADJUSTED_EVAL);
    }

    public List<GlobalUser> getGlobalUsersByIds(List<Integer> evaluationUserIds) {
        GlobalUserCriteria globalUserCriteria = new GlobalUserCriteria();
        globalUserCriteria.setIds(evaluationUserIds);
        return userService.getGlobalUsers(globalUserCriteria);
    }

    public boolean isGroupPricingEvaluationForFSRMGuestRoomOnlyRequestEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(
                FeatureTogglesConfigParamName.USE_GROUP_PRICING_EVALUATION_FOR_FSRM_GUEST_ROOM_ONLY_REQUEST);
    }
}

