package com.ideas.tetris.pacman.services.reports.rateplanproduction;

import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.reports.rateplanproduction.dto.RatePlanProductionReportDTO;
import com.ideas.tetris.pacman.services.scheduledreport.ScheduledReportUtils;
import com.ideas.tetris.pacman.services.scheduledreport.domain.converter.JasperReportDataConverter;
import com.ideas.tetris.pacman.services.scheduledreport.domain.converter.RatePlanProductionReportConverter;
import com.ideas.tetris.pacman.services.scheduledreport.entity.Language;
import com.ideas.tetris.pacman.services.scheduledreport.entity.ScheduledReport;
import com.ideas.tetris.pacman.services.scheduledreport.entity.criteria.RatePlanProductionReportCriteria;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;
import com.ideas.tetris.pacman.services.scheduledreport.service.JasperReportService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class RatePlanProductionReportService extends JasperReportService<List<RatePlanProductionReportDTO>, RatePlanProductionReportCriteria> {

    @Autowired
	private PacmanConfigParamsService configParamsService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    @RatePlanProductionReportConverter.Qualifier
    @Autowired
	@Qualifier("ratePlanProductionReportConverter")
	private JasperReportDataConverter<List<RatePlanProductionReportDTO>, RatePlanProductionReportCriteria> ratePlanProductionReportConverter;

    private List<RatePlanProductionReportDTO> getRatePlanProductionPickupReportData(RatePlanProductionReportCriteria reportCriteria) {
        QueryParameter queryParameters = getQueryParameter(reportCriteria, true);
        String query = "exec  usp_get_pickup_report_comparative_view_srp :property_id,:srp1,:srp2,:srp3,:srp4," +
                ":srp5,:srp6,:srp7,:srp8,:srp9,:srp10,:srp11,:srp12,:srp13,:srp14,:srp15,:srp16,:srp17,:srp18,:srp19,:srp20,:srp21," +
                ":srp22,:srp23,:srp24,:srp25,:businessStartDate,:businessEndDate,:start_date,:end_date,:isRollingDate," +
                ":rollingBusinessStartDate,:rollingBusinessEndDate,:rolling_start_date,:rolling_end_date ";
        List<Object[]> reportDataByStaticDates = tenantCrudService.findByNativeQuery(query, queryParameters.parameters());
        populateReportCriteria(reportCriteria, true);
        return prepareReportData(reportDataByStaticDates);
    }

    private List<RatePlanProductionReportDTO> getRatePlanProductionChangeReportData(RatePlanProductionReportCriteria reportCriteria) {
        QueryParameter queryParameters = getQueryParameter(reportCriteria, false);
        String query = "exec usp_get_change_report_comparative_view_srp :property_id,:srp1,:srp2,:srp3,:srp4," +
                ":srp5,:srp6,:srp7,:srp8,:srp9,:srp10,:srp11,:srp12,:srp13,:srp14,:srp15,:srp16,:srp17,:srp18,:srp19,:srp20,:srp21," +
                ":srp22,:srp23,:srp24,:srp25,:businessStartDate,:start_date,:end_date,:isRollingDate," +
                ":rollingBusinessStartDate,:rolling_start_date,:rolling_end_date";
        List<Object[]> reportDataByStaticDates = tenantCrudService.findByNativeQuery(query, queryParameters.parameters());
        populateReportCriteria(reportCriteria, false);
        return prepareReportData(reportDataByStaticDates);
    }

    private QueryParameter getQueryParameter(RatePlanProductionReportCriteria reportCriteria, boolean isPickupReport) {
        QueryParameter queryParameters = QueryParameter.with("property_id", reportCriteria.getPropertyId())
                .and("srp1", reportCriteria.getSrp1())
                .and("srp2", reportCriteria.getSrp2())
                .and("srp3", reportCriteria.getSrp3())
                .and("srp4", reportCriteria.getSrp4())
                .and("srp5", reportCriteria.getSrp5())
                .and("srp6", reportCriteria.getSrp6())
                .and("srp7", reportCriteria.getSrp7())
                .and("srp8", reportCriteria.getSrp8())
                .and("srp9", reportCriteria.getSrp9())
                .and("srp10", reportCriteria.getSrp10())
                .and("srp11", reportCriteria.getSrp11())
                .and("srp12", reportCriteria.getSrp12())
                .and("srp13", reportCriteria.getSrp13())
                .and("srp14", reportCriteria.getSrp14())
                .and("srp15", reportCriteria.getSrp15())
                .and("srp16", reportCriteria.getSrp16())
                .and("srp17", reportCriteria.getSrp17())
                .and("srp18", reportCriteria.getSrp18())
                .and("srp19", reportCriteria.getSrp19())
                .and("srp20", reportCriteria.getSrp20())
                .and("srp21", reportCriteria.getSrp21())
                .and("srp22", reportCriteria.getSrp22())
                .and("srp23", reportCriteria.getSrp23())
                .and("srp24", reportCriteria.getSrp24())
                .and("srp25", reportCriteria.getSrp25())
                .and("businessStartDate", new java.sql.Date(DateUtil.convertLocalDateToJavaUtilDate(reportCriteria.getBusinessStartDate()).getTime()))
                .and("start_date", new java.sql.Date(DateUtil.convertLocalDateToJavaUtilDate(reportCriteria.getStartDate()).getTime()))
                .and("end_date", new java.sql.Date(DateUtil.convertLocalDateToJavaUtilDate(reportCriteria.getEndDate()).getTime()))
                .and("isRollingDate", reportCriteria.getIsRollingDate())
                .and("rollingBusinessStartDate", reportCriteria.getRollingBusinessStartDate())
                .and("rolling_start_date", reportCriteria.getRollingStartDate())
                .and("rolling_end_date", reportCriteria.getRollingEndDate());

        if (isPickupReport) {
            queryParameters.and("businessEndDate", new java.sql.Date(DateUtil.convertLocalDateToJavaUtilDate(reportCriteria.getBusinessEndDate()).getTime()))
                    .and("rollingBusinessEndDate", reportCriteria.getRollingBusinessEndDate());
        }
        return queryParameters;
    }

    public void populateReportCriteria(RatePlanProductionReportCriteria reportCriteria, boolean isPickupReport) {

        String propertyId = reportCriteria.getPropertyId().toString();
        String userId = "''";
        String baseCurrency = reportCriteria.getCurrency();

        LocalDate startDate = reportCriteria.getStartDate();
        LocalDate endDate = reportCriteria.getEndDate();
        Integer rolling = reportCriteria.getIsRollingDate();
        String rollingStartDate = reportCriteria.getRollingStartDate();
        String rollingEndDate = reportCriteria.getRollingEndDate();
        //LocalDate businessStartDate = reportCriteria.getBusinessStartDate();
        //LocalDate businessEndDate = reportCriteria.getBusinessEndDate();
        String rollingBuisnessStartDate = reportCriteria.getRollingBusinessStartDate();
        String rollingBuisnessEndDate = reportCriteria.getRollingBusinessEndDate();

        String sql = " select * from dbo.ufn_get_filter_selection " + "('" + propertyId + "'," + userId + ",'" +
                baseCurrency + "'," + "'" + rolling + "'," + "'" + startDate + "'," + "'" + endDate + "'," +
                "''," + "''," + "'','','',''," +
                "'" + rollingStartDate + "'," + "'" + rollingEndDate + "'," +
                "'" + rollingBuisnessStartDate + "'," + "'" + rollingBuisnessEndDate + "'," +
                "'','','','' )";

        List<Object[]> resultList = tenantCrudService.findByNativeQuery(sql);
        if (resultList != null) {
            Object[] result = resultList.get(0);
            reportCriteria.setPropertyName((String) result[0]);
            LocalDate localStartDate = DateUtil.convertJavaUtilDateToLocalDate((Date) result[3], true);
            reportCriteria.setStartDate(localStartDate);
            LocalDate localEndDate = DateUtil.convertJavaUtilDateToLocalDate((Date) result[4], true);
            reportCriteria.setEndDate(localEndDate);
            LocalDate localBusinessStartDate = DateUtil.convertJavaUtilDateToLocalDate((Date) result[5], true);
            reportCriteria.setBusinessStartDate(localBusinessStartDate);
            if (isPickupReport) {
                LocalDate localBusinessEndDate = DateUtil.convertJavaUtilDateToLocalDate((Date) result[6], true);
                reportCriteria.setBusinessEndDate(localBusinessEndDate);
            } else {
                reportCriteria.setBusinessEndDate(null);
            }
        }
        reportCriteria.setCreatedOn(ScheduledReportUtils.convertDateTimeToTimeZone(reportCriteria.getCreatedOn(), TimeZone.getTimeZone
                (configParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE.value()))));
    }


    private List<RatePlanProductionReportDTO> prepareReportData(List<Object[]> reportDataByStaticDates) {
        List<RatePlanProductionReportDTO> ratePlanProductionReportDTOs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(reportDataByStaticDates)) {
            for (Object[] row : reportDataByStaticDates) {
                RatePlanProductionReportDTO ratePlanProductionReportDTO = new RatePlanProductionReportDTO();
                ratePlanProductionReportDTO.setArrivalDate((Date) row[0]);
                ratePlanProductionReportDTO.setDayOfWeek((String) row[1]);
                ratePlanProductionReportDTO.setRoomsOnBooksSRP1((Integer) row[2]);
                ratePlanProductionReportDTO.setRoomsOnBooksDiffSRP1((Integer) row[3]);
                ratePlanProductionReportDTO.setRoomsOnBooksSRP2((Integer) row[4]);
                ratePlanProductionReportDTO.setRoomsOnBooksDiffSRP2((Integer) row[5]);
                ratePlanProductionReportDTO.setRoomsOnBooksSRP3((Integer) row[6]);
                ratePlanProductionReportDTO.setRoomsOnBooksDiffSRP3((Integer) row[7]);
                ratePlanProductionReportDTO.setRoomsOnBooksSRP4((Integer) row[8]);
                ratePlanProductionReportDTO.setRoomsOnBooksDiffSRP4((Integer) row[9]);
                ratePlanProductionReportDTO.setRoomsOnBooksSRP5((Integer) row[10]);
                ratePlanProductionReportDTO.setRoomsOnBooksDiffSRP5((Integer) row[11]);
                ratePlanProductionReportDTO.setRoomsOnBooksSRP6((Integer) row[12]);
                ratePlanProductionReportDTO.setRoomsOnBooksDiffSRP6((Integer) row[13]);
                ratePlanProductionReportDTO.setRoomsOnBooksSRP7((Integer) row[14]);
                ratePlanProductionReportDTO.setRoomsOnBooksDiffSRP7((Integer) row[15]);
                ratePlanProductionReportDTO.setRoomsOnBooksSRP8((Integer) row[16]);
                ratePlanProductionReportDTO.setRoomsOnBooksDiffSRP8((Integer) row[17]);
                ratePlanProductionReportDTO.setRoomsOnBooksSRP9((Integer) row[18]);
                ratePlanProductionReportDTO.setRoomsOnBooksDiffSRP9((Integer) row[19]);
                ratePlanProductionReportDTO.setRoomsOnBooksSRP10((Integer) row[20]);
                ratePlanProductionReportDTO.setRoomsOnBooksDiffSRP10((Integer) row[21]);
                ratePlanProductionReportDTO.setRoomsOnBooksSRP11((Integer) row[22]);
                ratePlanProductionReportDTO.setRoomsOnBooksDiffSRP11((Integer) row[23]);
                ratePlanProductionReportDTO.setRoomsOnBooksSRP12((Integer) row[24]);
                ratePlanProductionReportDTO.setRoomsOnBooksDiffSRP12((Integer) row[25]);
                ratePlanProductionReportDTO.setRoomsOnBooksSRP13((Integer) row[26]);
                ratePlanProductionReportDTO.setRoomsOnBooksDiffSRP13((Integer) row[27]);
                ratePlanProductionReportDTO.setRoomsOnBooksSRP14((Integer) row[28]);
                ratePlanProductionReportDTO.setRoomsOnBooksDiffSRP14((Integer) row[29]);
                ratePlanProductionReportDTO.setRoomsOnBooksSRP15((Integer) row[30]);
                ratePlanProductionReportDTO.setRoomsOnBooksDiffSRP15((Integer) row[31]);
                ratePlanProductionReportDTO.setRoomsOnBooksSRP16((Integer) row[32]);
                ratePlanProductionReportDTO.setRoomsOnBooksDiffSRP16((Integer) row[33]);
                ratePlanProductionReportDTO.setRoomsOnBooksSRP17((Integer) row[34]);
                ratePlanProductionReportDTO.setRoomsOnBooksDiffSRP17((Integer) row[35]);
                ratePlanProductionReportDTO.setRoomsOnBooksSRP18((Integer) row[36]);
                ratePlanProductionReportDTO.setRoomsOnBooksDiffSRP18((Integer) row[37]);
                ratePlanProductionReportDTO.setRoomsOnBooksSRP19((Integer) row[38]);
                ratePlanProductionReportDTO.setRoomsOnBooksDiffSRP19((Integer) row[39]);
                ratePlanProductionReportDTO.setRoomsOnBooksSRP20((Integer) row[40]);
                ratePlanProductionReportDTO.setRoomsOnBooksDiffSRP20((Integer) row[41]);
                ratePlanProductionReportDTO.setRoomsOnBooksSRP21((Integer) row[42]);
                ratePlanProductionReportDTO.setRoomsOnBooksDiffSRP21((Integer) row[43]);
                ratePlanProductionReportDTO.setRoomsOnBooksSRP22((Integer) row[44]);
                ratePlanProductionReportDTO.setRoomsOnBooksDiffSRP22((Integer) row[45]);
                ratePlanProductionReportDTO.setRoomsOnBooksSRP23((Integer) row[46]);
                ratePlanProductionReportDTO.setRoomsOnBooksDiffSRP23((Integer) row[47]);
                ratePlanProductionReportDTO.setRoomsOnBooksSRP24((Integer) row[48]);
                ratePlanProductionReportDTO.setRoomsOnBooksDiffSRP24((Integer) row[49]);
                ratePlanProductionReportDTO.setRoomsOnBooksSRP25((Integer) row[50]);
                ratePlanProductionReportDTO.setRoomsOnBooksDiffSRP25((Integer) row[51]);

                ratePlanProductionReportDTO.setRevenueSRP1((BigDecimal) row[52]);
                ratePlanProductionReportDTO.setRevenueDiffSRP1((BigDecimal) row[53]);
                ratePlanProductionReportDTO.setRevenueSRP2((BigDecimal) row[54]);
                ratePlanProductionReportDTO.setRevenueDiffSRP2((BigDecimal) row[55]);
                ratePlanProductionReportDTO.setRevenueSRP3((BigDecimal) row[56]);
                ratePlanProductionReportDTO.setRevenueDiffSRP3((BigDecimal) row[57]);
                ratePlanProductionReportDTO.setRevenueSRP4((BigDecimal) row[58]);
                ratePlanProductionReportDTO.setRevenueDiffSRP4((BigDecimal) row[59]);
                ratePlanProductionReportDTO.setRevenueSRP5((BigDecimal) row[60]);
                ratePlanProductionReportDTO.setRevenueDiffSRP5((BigDecimal) row[61]);
                ratePlanProductionReportDTO.setRevenueSRP6((BigDecimal) row[62]);
                ratePlanProductionReportDTO.setRevenueDiffSRP6((BigDecimal) row[63]);
                ratePlanProductionReportDTO.setRevenueSRP7((BigDecimal) row[64]);
                ratePlanProductionReportDTO.setRevenueDiffSRP7((BigDecimal) row[65]);
                ratePlanProductionReportDTO.setRevenueSRP8((BigDecimal) row[66]);
                ratePlanProductionReportDTO.setRevenueDiffSRP8((BigDecimal) row[67]);
                ratePlanProductionReportDTO.setRevenueSRP9((BigDecimal) row[68]);
                ratePlanProductionReportDTO.setRevenueDiffSRP9((BigDecimal) row[69]);
                ratePlanProductionReportDTO.setRevenueSRP10((BigDecimal) row[70]);
                ratePlanProductionReportDTO.setRevenueDiffSRP10((BigDecimal) row[71]);
                ratePlanProductionReportDTO.setRevenueSRP11((BigDecimal) row[72]);
                ratePlanProductionReportDTO.setRevenueDiffSRP11((BigDecimal) row[73]);
                ratePlanProductionReportDTO.setRevenueSRP12((BigDecimal) row[74]);
                ratePlanProductionReportDTO.setRevenueDiffSRP12((BigDecimal) row[75]);
                ratePlanProductionReportDTO.setRevenueSRP13((BigDecimal) row[76]);
                ratePlanProductionReportDTO.setRevenueDiffSRP13((BigDecimal) row[77]);
                ratePlanProductionReportDTO.setRevenueSRP14((BigDecimal) row[78]);
                ratePlanProductionReportDTO.setRevenueDiffSRP14((BigDecimal) row[79]);
                ratePlanProductionReportDTO.setRevenueSRP15((BigDecimal) row[80]);
                ratePlanProductionReportDTO.setRevenueDiffSRP15((BigDecimal) row[81]);
                ratePlanProductionReportDTO.setRevenueSRP16((BigDecimal) row[82]);
                ratePlanProductionReportDTO.setRevenueDiffSRP16((BigDecimal) row[83]);
                ratePlanProductionReportDTO.setRevenueSRP17((BigDecimal) row[84]);
                ratePlanProductionReportDTO.setRevenueDiffSRP17((BigDecimal) row[85]);
                ratePlanProductionReportDTO.setRevenueSRP18((BigDecimal) row[86]);
                ratePlanProductionReportDTO.setRevenueDiffSRP18((BigDecimal) row[87]);
                ratePlanProductionReportDTO.setRevenueSRP19((BigDecimal) row[88]);
                ratePlanProductionReportDTO.setRevenueDiffSRP19((BigDecimal) row[89]);
                ratePlanProductionReportDTO.setRevenueSRP20((BigDecimal) row[90]);
                ratePlanProductionReportDTO.setRevenueDiffSRP20((BigDecimal) row[91]);
                ratePlanProductionReportDTO.setRevenueSRP21((BigDecimal) row[92]);
                ratePlanProductionReportDTO.setRevenueDiffSRP21((BigDecimal) row[93]);
                ratePlanProductionReportDTO.setRevenueSRP22((BigDecimal) row[94]);
                ratePlanProductionReportDTO.setRevenueDiffSRP22((BigDecimal) row[95]);
                ratePlanProductionReportDTO.setRevenueSRP23((BigDecimal) row[96]);
                ratePlanProductionReportDTO.setRevenueDiffSRP23((BigDecimal) row[97]);
                ratePlanProductionReportDTO.setRevenueSRP24((BigDecimal) row[98]);
                ratePlanProductionReportDTO.setRevenueDiffSRP24((BigDecimal) row[99]);
                ratePlanProductionReportDTO.setRevenueSRP25((BigDecimal) row[100]);
                ratePlanProductionReportDTO.setRevenueDiffSRP25((BigDecimal) row[101]);

                ratePlanProductionReportDTO.setAdrSRP1((BigDecimal) row[102]);
                ratePlanProductionReportDTO.setAdrDiffSRP1((BigDecimal) row[103]);
                ratePlanProductionReportDTO.setAdrSRP2((BigDecimal) row[104]);
                ratePlanProductionReportDTO.setAdrDiffSRP2((BigDecimal) row[105]);
                ratePlanProductionReportDTO.setAdrSRP3((BigDecimal) row[106]);
                ratePlanProductionReportDTO.setAdrDiffSRP3((BigDecimal) row[107]);
                ratePlanProductionReportDTO.setAdrSRP4((BigDecimal) row[108]);
                ratePlanProductionReportDTO.setAdrDiffSRP4((BigDecimal) row[109]);
                ratePlanProductionReportDTO.setAdrSRP5((BigDecimal) row[110]);
                ratePlanProductionReportDTO.setAdrDiffSRP5((BigDecimal) row[111]);
                ratePlanProductionReportDTO.setAdrSRP6((BigDecimal) row[112]);
                ratePlanProductionReportDTO.setAdrDiffSRP6((BigDecimal) row[113]);
                ratePlanProductionReportDTO.setAdrSRP7((BigDecimal) row[114]);
                ratePlanProductionReportDTO.setAdrDiffSRP7((BigDecimal) row[115]);
                ratePlanProductionReportDTO.setAdrSRP8((BigDecimal) row[116]);
                ratePlanProductionReportDTO.setAdrDiffSRP8((BigDecimal) row[117]);
                ratePlanProductionReportDTO.setAdrSRP9((BigDecimal) row[118]);
                ratePlanProductionReportDTO.setAdrDiffSRP9((BigDecimal) row[119]);
                ratePlanProductionReportDTO.setAdrSRP10((BigDecimal) row[120]);
                ratePlanProductionReportDTO.setAdrDiffSRP10((BigDecimal) row[121]);
                ratePlanProductionReportDTO.setAdrSRP11((BigDecimal) row[122]);
                ratePlanProductionReportDTO.setAdrDiffSRP11((BigDecimal) row[123]);
                ratePlanProductionReportDTO.setAdrSRP12((BigDecimal) row[124]);
                ratePlanProductionReportDTO.setAdrDiffSRP12((BigDecimal) row[125]);
                ratePlanProductionReportDTO.setAdrSRP13((BigDecimal) row[126]);
                ratePlanProductionReportDTO.setAdrDiffSRP13((BigDecimal) row[127]);
                ratePlanProductionReportDTO.setAdrSRP14((BigDecimal) row[128]);
                ratePlanProductionReportDTO.setAdrDiffSRP14((BigDecimal) row[129]);
                ratePlanProductionReportDTO.setAdrSRP15((BigDecimal) row[130]);
                ratePlanProductionReportDTO.setAdrDiffSRP15((BigDecimal) row[131]);
                ratePlanProductionReportDTO.setAdrSRP16((BigDecimal) row[132]);
                ratePlanProductionReportDTO.setAdrDiffSRP16((BigDecimal) row[133]);
                ratePlanProductionReportDTO.setAdrSRP17((BigDecimal) row[134]);
                ratePlanProductionReportDTO.setAdrDiffSRP17((BigDecimal) row[135]);
                ratePlanProductionReportDTO.setAdrSRP18((BigDecimal) row[136]);
                ratePlanProductionReportDTO.setAdrDiffSRP18((BigDecimal) row[137]);
                ratePlanProductionReportDTO.setAdrSRP19((BigDecimal) row[138]);
                ratePlanProductionReportDTO.setAdrDiffSRP19((BigDecimal) row[139]);
                ratePlanProductionReportDTO.setAdrSRP20((BigDecimal) row[140]);
                ratePlanProductionReportDTO.setAdrDiffSRP20((BigDecimal) row[141]);
                ratePlanProductionReportDTO.setAdrSRP21((BigDecimal) row[142]);
                ratePlanProductionReportDTO.setAdrDiffSRP21((BigDecimal) row[143]);
                ratePlanProductionReportDTO.setAdrSRP22((BigDecimal) row[144]);
                ratePlanProductionReportDTO.setAdrDiffSRP22((BigDecimal) row[145]);
                ratePlanProductionReportDTO.setAdrSRP23((BigDecimal) row[146]);
                ratePlanProductionReportDTO.setAdrDiffSRP23((BigDecimal) row[147]);
                ratePlanProductionReportDTO.setAdrSRP24((BigDecimal) row[148]);
                ratePlanProductionReportDTO.setAdrDiffSRP24((BigDecimal) row[149]);
                ratePlanProductionReportDTO.setAdrSRP25((BigDecimal) row[150]);
                ratePlanProductionReportDTO.setAdrDiffSRP25((BigDecimal) row[151]);

                ratePlanProductionReportDTOs.add(ratePlanProductionReportDTO);
            }
        }

        return ratePlanProductionReportDTOs;
    }

    @Override
    protected JasperReportDataConverter<List<RatePlanProductionReportDTO>, RatePlanProductionReportCriteria> getJasperReportDataConverter() {
        return ratePlanProductionReportConverter;
    }

    @Override
    public String getReportTitle(ScheduledReport<RatePlanProductionReportCriteria> scheduledReport) {
        final RatePlanProductionReportCriteria ratePlanProductionReportCriteria = scheduledReport.getReportCriteria();
        Language lang = ratePlanProductionReportCriteria.getLanguage();
        if (ratePlanProductionReportCriteria.getReportType().equals(Constants.REPORT_TYPE_CHANGE)) {
            return ResourceUtil.getText("change.report.at.special.rate.plan.level", lang) + " " + ResourceUtil.getText("side.by.side.view", lang);
        }
        return ResourceUtil.getText("pick.up.report.at.special.rate.plan.level", lang) + " " + ResourceUtil.getText("side.by.side.view", lang);
    }


    @Override
    protected List<RatePlanProductionReportDTO> retrieveData(ScheduledReport<RatePlanProductionReportCriteria> scheduledReport) {
        final RatePlanProductionReportCriteria ratePlanProductionReportCriteria = scheduledReport.getReportCriteria();
        if (ratePlanProductionReportCriteria.getReportType().equals(Constants.REPORT_TYPE_CHANGE)) {
            return getRatePlanProductionChangeReportData(ratePlanProductionReportCriteria);
        }
        return getRatePlanProductionPickupReportData(ratePlanProductionReportCriteria);
    }
}
