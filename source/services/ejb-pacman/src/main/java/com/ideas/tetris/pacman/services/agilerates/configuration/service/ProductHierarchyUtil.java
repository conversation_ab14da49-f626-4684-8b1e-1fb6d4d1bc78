package com.ideas.tetris.pacman.services.agilerates.configuration.service;

import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.dto.ProductHierarchyBucket;
import com.ideas.tetris.pacman.services.agilerates.configuration.dto.ProductHierarchyContext;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesDTARange;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductRateOffset;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductRateOffsetOverride;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.CeilingFloor;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigOffsetAccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OffsetMethod;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingBaseAccomType;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.LocalDate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

public final class ProductHierarchyUtil {

    static List<ProductHierarchyBucket> createBucketsWhenBothIndependentProducts(ProductHierarchyContext context, Set<LocalDate> datesToConsiderForBucketing) {

        Set<Pair<LocalDate, LocalDate>> dateRanges = createDateBucketsForOverrides(datesToConsiderForBucketing);
        List<Pair<LocalDate, Boolean>> dateBuckets = createDateBuckets(dateRanges);

        List<ProductHierarchyBucket> productHierarchyBuckets = createEmptyHierarchyBuckets(dateBuckets);
        productHierarchyBuckets.forEach(bucket -> addDataToBucketWhenBothProductsAreIndependent(bucket, context));

        boolean wasDefaultsToDefaultsAdded = productHierarchyBuckets.stream().anyMatch(ProductHierarchyUtil::isBucketHavingDefaultValueForIP);
        if (!wasDefaultsToDefaultsAdded) {
            productHierarchyBuckets.add(createDefaultBucketWhenBothProductsAreIndependent(context));
        }
        return productHierarchyBuckets;
    }

    private static Set<Pair<LocalDate, LocalDate>> createDateBucketsForOverrides(Set<LocalDate> datesToConsiderForBucketing) {
        Set<Pair<LocalDate, LocalDate>> bucketDates = new HashSet<>();
        for (LocalDate date : datesToConsiderForBucketing) {
            bucketDates.add(Pair.of(date, date));
        }
        return bucketDates;
    }

    static List<ProductHierarchyBucket> createBucketsWhenBothIndependentProducts(ProductHierarchyContext context) {
        Set<Pair<LocalDate, LocalDate>> dateSet = getDatesToBeConsideredForBuckets(
                context.getSelectedIPCeilingFloor() != null ? context.getSelectedIPCeilingFloor().keySet() : null,
                context.getRelatedIPCeilingFloor() != null ? context.getRelatedIPCeilingFloor().keySet() : null,
                context.getSelectedIPOffsets() != null ? context.getSelectedIPOffsets().keySet() : null,
                context.getRelatedIPOffsets() != null ? context.getRelatedIPOffsets().keySet() : null,
                context.getSelectedCeilingFloorOverrides() != null ? context.getSelectedCeilingFloorOverrides().keySet() : null,
                context.getRelatedCeilingFloorOverrides() != null ? context.getRelatedCeilingFloorOverrides().keySet() : null,
                context.getSelectedSpecificOverrides() != null ? context.getSelectedSpecificOverrides().keySet() : null,
                context.getRelatedSpecificOverrides() != null ? context.getRelatedSpecificOverrides().keySet() : null);

        List<Pair<LocalDate, Boolean>> dateBuckets = createDateBuckets(dateSet);

        List<ProductHierarchyBucket> productHierarchyBuckets = createEmptyHierarchyBuckets(dateBuckets);
        productHierarchyBuckets.forEach(bucket -> addDataToBucketWhenBothProductsAreIndependent(bucket, context));

        boolean wasDefaultsToDefaultsAdded = productHierarchyBuckets.stream().anyMatch(ProductHierarchyUtil::isBucketHavingDefaultValueForIP);
        if (!wasDefaultsToDefaultsAdded) {
            productHierarchyBuckets.add(createDefaultBucketWhenBothProductsAreIndependent(context));
        }
        return productHierarchyBuckets;
    }

    public static List<ProductHierarchyBucket> createBucketsWhenIndirectRelations(ProductHierarchyContext context) {

        Set<Pair<LocalDate, LocalDate>> datePairs = new HashSet<>();
        Set<Pair<LocalDate, LocalDate>> seasonalProductSeasons = new HashSet<>();
        List<ProductHierarchyBucket> productHierarchyBuckets;
        List<Product> selectedProductChain = context.getSelectedProductChain();
        List<Product> relatedProductChain = context.getRelatedProductChain();

        context.getAllOffsetsByProductAndSeasonDates().forEach((k, v) -> datePairs.addAll(v.keySet()));
        context.getAllOverridesByProductAndSeasonDates().forEach((k, v) -> datePairs.addAll(v.keySet()));
        Set<Pair<LocalDate, LocalDate>> dateSet = getDatesToBeConsideredForBuckets(datePairs);
        List<Pair<LocalDate, Boolean>> dateBuckets = createDateBuckets(dateSet);

        selectedProductChain.forEach(product -> {
            Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>> offsets = context.getAllOffsetsByProductAndSeasonDates().get(product);
            seasonalProductSeasons.addAll(getSeasonalProductSeasons(offsets, Collections.emptyMap()));
        });

        relatedProductChain.forEach(product -> {
            Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>> offsets = context.getAllOffsetsByProductAndSeasonDates().get(product);
            seasonalProductSeasons.addAll(getSeasonalProductSeasons(Collections.emptyMap(), offsets));
        });

        if (CollectionUtils.isNotEmpty(seasonalProductSeasons)) {
            productHierarchyBuckets = createEmptyHierarchyBuckets(dateBuckets).stream()
                    .filter(bucket -> isBucketApplicableForSeasonProductSeasons(bucket, seasonalProductSeasons)).collect(Collectors.toList());
        } else {
            productHierarchyBuckets = createEmptyHierarchyBuckets(dateBuckets);
        }
        productHierarchyBuckets.forEach(bucket -> addDataToBucketWhenIndirectRelations(bucket, context));

        boolean isSelectedSeasonal = isProductChainSeasonal(context, context.getSelectedProductChain());
        boolean isRelatedSeasonal = isProductChainSeasonal(context, context.getRelatedProductChain());
        if (!isSelectedSeasonal && !isRelatedSeasonal) {
            boolean wasDefaultsToDefaultsAdded = productHierarchyBuckets.stream().anyMatch(ProductHierarchyUtil::isBucketHavingDefaultValueForIndirectLPRelation);
            if (!wasDefaultsToDefaultsAdded) {
                productHierarchyBuckets.add(createDefaultBucketWhenIndirectRelations(context));
            }
        }
        return productHierarchyBuckets;
    }

    private static boolean isProductChainSeasonal(ProductHierarchyContext context, List<Product> productChain) {
        boolean isSeasonal = false;
        Map<Product, List<ProductRateOffset>> offsetList = new HashMap<>();
        for (Product product : productChain) {
            Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>> productOffsets = context.getAllOffsetsByProductAndSeasonDates().get(product);
            List<ProductRateOffset> offsets = productOffsets.values().stream().flatMap(List::stream).collect(Collectors.toList());
            offsetList.put(product, offsets);
        }
        for (Map.Entry<Product, List<ProductRateOffset>> entry : offsetList.entrySet()) {
            isSeasonal = isSeasonalProduct(entry.getValue());
            if (isSeasonal) {
                break;
            }
        }
        return isSeasonal;
    }

    public static List<ProductHierarchyBucket> createBucketsWhenIndirectRelations(ProductHierarchyContext context, LocalDate overrideDate) {

        Set<LocalDate> overrideDates = new HashSet<>(Set.of(overrideDate));
        Set<Pair<LocalDate, LocalDate>> seasonalProductSeasons = new HashSet<>();
        List<ProductHierarchyBucket> productHierarchyBuckets;
        List<Product> selectedProductChain = context.getSelectedProductChain();
        List<Product> relatedProductChain = context.getRelatedProductChain();

        Set<Pair<LocalDate, LocalDate>> dateRanges = createDateBucketsForOverrides(overrideDates);
        Set<Pair<LocalDate, LocalDate>> dateSet = getDatesToBeConsideredForBuckets(dateRanges);
        List<Pair<LocalDate, Boolean>> dateBuckets = createDateBuckets(dateSet);

        selectedProductChain.forEach(product -> {
            Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>> offsets = context.getAllOffsetsByProductAndSeasonDates().get(product);
            seasonalProductSeasons.addAll(getSeasonalProductSeasons(offsets, Collections.emptyMap()));
        });

        relatedProductChain.forEach(product -> {
            Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>> offsets = context.getAllOffsetsByProductAndSeasonDates().get(product);
            seasonalProductSeasons.addAll(getSeasonalProductSeasons(Collections.emptyMap(), offsets));
        });

        if (CollectionUtils.isNotEmpty(seasonalProductSeasons)) {
            productHierarchyBuckets = createEmptyHierarchyBuckets(dateBuckets).stream()
                    .filter(bucket -> isBucketApplicableForSeasonProductSeasons(bucket, seasonalProductSeasons)).collect(Collectors.toList());
        } else {
            productHierarchyBuckets = createEmptyHierarchyBuckets(dateBuckets);
        }
        productHierarchyBuckets.forEach(bucket -> addDataToBucketWhenIndirectRelations(bucket, context));
        return productHierarchyBuckets;
    }

    private static ProductHierarchyBucket createDefaultBucketWhenIndirectRelations(ProductHierarchyContext context) {

        ProductHierarchyBucket defaultValueBucket = new ProductHierarchyBucket();

        Map<Product, Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>>> selectedProductOffsets = new HashMap<>();
        Map<Product, Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>>> relatedProductOffsets = new HashMap<>();

        Map<Product, Map<Pair<LocalDate, LocalDate>, List<ProductRateOffsetOverride>>> selectedProductOffsetOverrides = new HashMap<>();
        Map<Product, Map<Pair<LocalDate, LocalDate>, List<ProductRateOffsetOverride>>> relatedProductOffsetOverrides = new HashMap<>();

        for (Product product : context.getSelectedProductChain()) {
            selectedProductOffsets.put(product, context.getAllOffsetsByProductAndSeasonDates().get(product));
            selectedProductOffsetOverrides.put(product, context.getAllOverridesByProductAndSeasonDates().get(product));
        }

        selectedProductOffsetOverrides.keySet().removeIf(product -> {
            Map<Pair<LocalDate, LocalDate>, List<ProductRateOffsetOverride>> rateOffsetMap = selectedProductOffsetOverrides.get(product);
            return rateOffsetMap == null || rateOffsetMap.isEmpty();
        });

        for (Product product : context.getRelatedProductChain()) {
            relatedProductOffsets.put(product, context.getAllOffsetsByProductAndSeasonDates().get(product));
            relatedProductOffsetOverrides.put(product, context.getAllOverridesByProductAndSeasonDates().get(product));
        }

        relatedProductOffsetOverrides.keySet().removeIf(product -> {
            Map<Pair<LocalDate, LocalDate>, List<ProductRateOffsetOverride>> rateOffsetMap = relatedProductOffsetOverrides.get(product);
            return rateOffsetMap == null || rateOffsetMap.isEmpty();
        });

        defaultValueBucket.setSelectedOffsetsByLinkedProduct(
                Optional.of(selectedProductOffsets)
                        .map(allOffsets -> allOffsets.entrySet().stream()
                                .filter(entry -> entry.getValue().keySet().stream().anyMatch(pair -> pair.getLeft() == null))
                                .collect(Collectors.toMap(
                                        Map.Entry::getKey,
                                        entry -> entry.getValue().values().stream().flatMap(List::stream).collect(Collectors.toList())
                                )))
                        .orElse(Collections.emptyMap())
        );

        defaultValueBucket.setRelatedOffsetsByLinkedProduct(
                Optional.of(relatedProductOffsets)
                        .map(allOffsets -> allOffsets.entrySet().stream()
                                .filter(entry -> entry.getValue().keySet().stream().anyMatch(pair -> pair.getLeft() == null))
                                .collect(Collectors.toMap(
                                        Map.Entry::getKey,
                                        entry -> entry.getValue().values().stream().flatMap(List::stream).collect(Collectors.toList())
                                )))
                        .orElse(Collections.emptyMap())
        );

        defaultValueBucket.setSelectedOverridesByLinkedProduct(MapUtils.isNotEmpty(selectedProductOffsetOverrides) ?
                Optional.of(selectedProductOffsetOverrides)
                        .map(allOverrides -> allOverrides.entrySet().stream()
                                .filter(entry -> entry.getValue().keySet().stream().anyMatch(pair -> pair.getLeft() == null))
                                .collect(Collectors.toMap(
                                        Map.Entry::getKey,
                                        entry -> entry.getValue().values().stream()
                                                .flatMap(List::stream)
                                                .collect(Collectors.toList())
                                )))
                        .orElse(Collections.emptyMap()) : Collections.emptyMap()
        );

        defaultValueBucket.setRelatedOverridesByLinkedProduct(MapUtils.isNotEmpty(relatedProductOffsetOverrides) ?
                Optional.of(relatedProductOffsetOverrides)
                        .map(allOverrides -> allOverrides.entrySet().stream()
                                .filter(entry -> entry.getValue().keySet().stream().anyMatch(pair -> pair.getLeft() == null))
                                .collect(Collectors.toMap(
                                        Map.Entry::getKey,
                                        entry -> entry.getValue().values().stream()
                                                .flatMap(List::stream)
                                                .collect(Collectors.toList())
                                )))
                        .orElse(Collections.emptyMap()) : Collections.emptyMap()
        );
        return defaultValueBucket;
    }

    private static boolean isBucketHavingDefaultValueForIndirectLPRelation(ProductHierarchyBucket bucket) {
        return (MapUtils.isEmpty(bucket.getRelatedOffsetsByLinkedProduct()) || bucket.getRelatedOffsetsByLinkedProduct()
                .values().stream()
                .flatMap(List::stream)
                .collect(groupingBy(off -> Pair.of(off.getStartDate(), off.getEndDate())))
                .keySet().stream()
                .allMatch(pair -> pair.getLeft() == null)) &&
                (MapUtils.isEmpty(bucket.getSelectedOffsetsByLinkedProduct()) || bucket.getSelectedOffsetsByLinkedProduct()
                        .values().stream()
                        .flatMap(List::stream)
                        .collect(groupingBy(off -> Pair.of(off.getStartDate(), off.getEndDate())))
                        .keySet().stream()
                        .allMatch(pair -> pair.getLeft() == null));
    }

    private static void addDataToBucketWhenIndirectRelations(ProductHierarchyBucket bucket, ProductHierarchyContext context) {

        LocalDate startBucketDate = bucket.getStartDate();
        LocalDate endBucketDate = bucket.getEndDate();

        List<Product> selectedProductChain = context.getSelectedProductChain();
        List<Product> relatedProductChain = context.getRelatedProductChain();

        Map<Product, List<ProductRateOffset>> selectedProductOffset = new HashMap<>();
        for (Product product : selectedProductChain) {
            Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>> productOffsets = context.getAllOffsetsByProductAndSeasonDates().get(product);
            Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>> overlappingOffsetsForLP =
                    findOverlappingOffsetsForLP(startBucketDate, endBucketDate, productOffsets, context.getSharedAccomTypes());
            List<ProductRateOffset> offsets = overlappingOffsetsForLP.entrySet().stream().findFirst().get().getValue();
            selectedProductOffset.put(product, offsets);
        }

        Map<Product, List<ProductRateOffset>> relatedProductOffset = new HashMap<>();
        for (Product product : relatedProductChain) {
            Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>> productOffsets = context.getAllOffsetsByProductAndSeasonDates().get(product);
            Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>> overlappingOffsetsForLP =
                    findOverlappingOffsetsForLP(startBucketDate, endBucketDate, productOffsets, context.getSharedAccomTypes());
            List<ProductRateOffset> offsets = overlappingOffsetsForLP.entrySet().stream().findFirst().get().getValue();
            relatedProductOffset.put(product, offsets);
        }

        Map<Product, List<ProductRateOffsetOverride>> selectedOverrides = new HashMap<>();
        for (Product product : selectedProductChain) {
            Map<Pair<LocalDate, LocalDate>, List<ProductRateOffsetOverride>> productOverrides = context.getAllOverridesByProductAndSeasonDates().get(product);
            if (MapUtils.isNotEmpty(productOverrides) && CollectionUtils.isNotEmpty(productOverrides.values())) {
                List<ProductRateOffsetOverride> overlappingOverridesForLP = findOverlappingOverridesForLP(startBucketDate, endBucketDate, productOverrides);
                selectedOverrides.put(product, overlappingOverridesForLP);
            }
        }

        Map<Product, List<ProductRateOffsetOverride>> relatedOverrides = new HashMap<>();
        for (Product product : relatedProductChain) {
            Map<Pair<LocalDate, LocalDate>, List<ProductRateOffsetOverride>> productOverrides = context.getAllOverridesByProductAndSeasonDates().get(product);
            if (MapUtils.isNotEmpty(productOverrides) && CollectionUtils.isNotEmpty(productOverrides.values())) {
                List<ProductRateOffsetOverride> overlappingOverridesForLP = findOverlappingOverridesForLP(startBucketDate, endBucketDate, productOverrides);
                relatedOverrides.put(product, overlappingOverridesForLP);
            }
        }

        bucket.setSelectedOffsetsByLinkedProduct(selectedProductOffset);
        bucket.setRelatedOffsetsByLinkedProduct(relatedProductOffset);

        bucket.setSelectedOverridesByLinkedProduct(selectedOverrides);
        bucket.setRelatedOverridesByLinkedProduct(relatedOverrides);
    }

    @SafeVarargs
    private static Set<Pair<LocalDate, LocalDate>> getDatesToBeConsideredForBuckets(Set<Pair<LocalDate, LocalDate>>... dateRanges) {
        Set<Pair<LocalDate, LocalDate>> datesToConsiderForBucket = new HashSet<>();
        for (Set<Pair<LocalDate, LocalDate>> dateSet : dateRanges) {
            if (Objects.nonNull(dateSet)) {
                dateSet.forEach(date -> {
                    if (date.getLeft() != null && date.getRight() != null) {
                        datesToConsiderForBucket.add(date);
                    }
                });
            }
        }
        return datesToConsiderForBucket;
    }

    static List<Product> getProductBranch(Product product, List<Product> allProducts) {
        //This method returns the selected product's branch from its position up to the primary product
        if (product.isSystemDefaultOrIndependentProduct()) {
            return List.of(product);
        }

        //Add selected product first
        List<Product> productBranch = new ArrayList<>();
        productBranch.add(0, product);

        //Add dependent product above it
        Product dependentProduct = getDependentProduct(product, allProducts);
        productBranch.add(0, dependentProduct);

        //Add remaining dependent products up the branch
        while (dependentProduct != null && !dependentProduct.isSystemDefaultOrIndependentProduct()) {
            dependentProduct = getDependentProduct(dependentProduct, allProducts);
            productBranch.add(0, dependentProduct);
        }

        return productBranch;
    }

    private static Product getDependentProduct(Product product, List<Product> allProducts) {
        return allProducts.stream().filter(p -> p.getId().equals(product.getDependentProductId())).findFirst().orElse(null);
    }

    static List<ProductRateOffset> getProductBranchOffsets(List<Product> productBranch, List<ProductRateOffset> allProductRateOffsets) {
        List<Product> branchWithoutPrimaryProduct = productBranch.stream().filter(product -> !product.isSystemDefaultOrIndependentProduct()).collect(Collectors.toList());
        return allProductRateOffsets.stream().filter(productRateOffset -> branchWithoutPrimaryProduct.contains(productRateOffset.getProduct())).collect(Collectors.toList());
    }

    @SafeVarargs
    static List<Pair<LocalDate, Boolean>> createDateBuckets(Set<Pair<LocalDate, LocalDate>>... dateRanges) {

        if (isArrayOfSetsEmpty(dateRanges)) {
            return Collections.emptyList();
        }
        if (Arrays.stream(dateRanges).filter(Objects::nonNull).flatMap(Collection::stream).allMatch(pair -> pair.getLeft() == null && pair.getRight() == null)) {
            return List.of(Pair.of(null, false), Pair.of(null, true));
        }

        Set<Pair<LocalDate, Boolean>> pairSet =
                Arrays.stream(dateRanges)
                        .flatMap(Set::stream)
                        .filter(pair -> nonNull(pair) && nonNull(pair.getLeft()))
                        .flatMap(pair -> {
                            LocalDate start = pair.getLeft();
                            LocalDate end = pair.getRight();
                            Pair<LocalDate, Boolean> isStartDateEndOfSeason = Pair.of(start, false);
                            Pair<LocalDate, Boolean> isEndDateEndOfSeason = Pair.of(end, true);
                            return Stream.of(isStartDateEndOfSeason, isEndDateEndOfSeason);//10-Aug-false,20-Aug-true 15-Aug-false,25-Aug-true
                        })
                        .collect(Collectors.toSet());

        return pairSet.stream().distinct()
                .sorted((pair1, pair2) -> {
                    if (pair1.getLeft().equals(pair2.getLeft())) {
                        return pair1.getRight().compareTo(pair2.getRight());
                    }
                    return pair1.getLeft().compareTo(pair2.getLeft());
                })
                .collect(Collectors.toList());
    }

    @SafeVarargs
    public static boolean isArrayOfSetsEmpty(Set<Pair<LocalDate, LocalDate>>... sets) {
        for (Set<Pair<LocalDate, LocalDate>> set : sets) {
            if (set != null && !set.isEmpty()) {
                return false;
            }
        }
        return true;
    }

    static List<ProductHierarchyBucket> createEmptyHierarchyBuckets(List<Pair<LocalDate, Boolean>> dateBuckets) {
        List<ProductHierarchyBucket> bucketsToValidate = new ArrayList<>();
        for (int i = 0; i < dateBuckets.size() - 1; i++) {
            LocalDate currentDate = dateBuckets.get(i).getLeft();
            LocalDate nextDate = dateBuckets.get(i + 1).getLeft();
            LocalDate startBucketDate, endBucketDate;
            boolean isCurrentDateEndSeasonDate = dateBuckets.get(i).getRight();
            boolean isNextDateEndSeasonDate = dateBuckets.get(i + 1).getRight();
            if (!isCurrentDateEndSeasonDate) {
                startBucketDate = currentDate;
            } else {
                startBucketDate = currentDate.plusDays(1);
            }
            if (!isNextDateEndSeasonDate) {
                endBucketDate = nextDate.minusDays(1);
            } else {
                endBucketDate = nextDate;
            }
            if (startBucketDate.isBefore(endBucketDate) || startBucketDate.equals(endBucketDate)) {
                ProductHierarchyBucket bucket = new ProductHierarchyBucket();
                bucket.setStartDate(startBucketDate);
                bucket.setEndDate(endBucketDate);
                bucketsToValidate.add(bucket);
            }
        }
        return bucketsToValidate;
    }

    static void addDataToBucketWhenBothProductsAreIndependent(ProductHierarchyBucket bucket, ProductHierarchyContext context) {
        LocalDate startBucketDate = bucket.getStartDate();
        LocalDate endBucketDate = bucket.getEndDate();
        bucket.setSelectedIPCeilingFloor(findOverlappingCeilingFloorValues(startBucketDate, endBucketDate, context.getSelectedIPCeilingFloor()));
        bucket.setRelatedIPCeilingFloor(findOverlappingCeilingFloorValues(startBucketDate, endBucketDate, context.getRelatedIPCeilingFloor()));
        bucket.setSelectedIPOffsets(findOverlappingOffsets(startBucketDate, endBucketDate, context.getSelectedIPOffsets()));
        bucket.setRelatedIPOffsets(findOverlappingOffsets(startBucketDate, endBucketDate, context.getRelatedIPOffsets()));
        bucket.setSelectedCFOverrides(findOverlappingOverrides(startBucketDate, endBucketDate, context.getSelectedCeilingFloorOverrides()));
        bucket.setRelatedCFOverrides(findOverlappingOverrides(startBucketDate, endBucketDate, context.getRelatedCeilingFloorOverrides()));
        bucket.setSelectedSpecificOverrides(findOverlappingOverrides(startBucketDate, endBucketDate, context.getSelectedSpecificOverrides()));
        bucket.setRelatedSpecificOverrides(findOverlappingOverrides(startBucketDate, endBucketDate, context.getRelatedSpecificOverrides()));
        bucket.setSelectedDecisions(context.getSelectedDecisions());
        bucket.setRelatedDecisions(context.getRelatedDecisions());
        if (!bucket.getSelectedSpecificOverrides().isEmpty() || !bucket.getRelatedSpecificOverrides().isEmpty()) {
            bucket.setSpecificOverridePresent(true);
        }

        if (!bucket.getSelectedCFOverrides().isEmpty() || !bucket.getRelatedCFOverrides().isEmpty()) {
            bucket.setCeilingFloorOverridePresent(true);
        }
    }

    static boolean isBucketHavingDefaultValueForIP(ProductHierarchyBucket bucket) {
        return (CollectionUtils.isEmpty(bucket.getRelatedIPOffsets()) || Objects.isNull(bucket.getRelatedIPOffsets().get(0).getStartDate())) &&
                (CollectionUtils.isEmpty(bucket.getSelectedIPOffsets()) || Objects.isNull(bucket.getSelectedIPOffsets().get(0).getStartDate())) &&
                Objects.isNull(bucket.getRelatedIPCeilingFloor().get(0).getStartDate()) &&
                Objects.isNull(bucket.getSelectedIPCeilingFloor().get(0).getStartDate());
    }


    static ProductHierarchyBucket createDefaultBucketWhenBothProductsAreIndependent(ProductHierarchyContext context) {
        ProductHierarchyBucket defaultValueBucket = new ProductHierarchyBucket();
        defaultValueBucket.setSelectedIPCeilingFloor(context.getSelectedIPCeilingFloor() != null ?
                context.getSelectedIPCeilingFloor().getOrDefault(Pair.of(null, null), Collections.emptyList()) : new ArrayList<>());
        defaultValueBucket.setRelatedIPCeilingFloor(context.getRelatedIPCeilingFloor() != null ?
                context.getRelatedIPCeilingFloor().getOrDefault(Pair.of(null, null), Collections.emptyList()) : new ArrayList<>());
        defaultValueBucket.setSelectedIPOffsets(context.getSelectedIPOffsets() != null ?
                context.getSelectedIPOffsets().getOrDefault(Pair.of(null, null), Collections.emptyList()) : new ArrayList<>());
        defaultValueBucket.setRelatedIPOffsets(context.getRelatedIPOffsets() != null ?
                context.getRelatedIPOffsets().getOrDefault(Pair.of(null, null), Collections.emptyList()) : new ArrayList<>());
        defaultValueBucket.setSelectedCFOverrides(context.getSelectedCeilingFloorOverrides() != null ?
                context.getSelectedCeilingFloorOverrides().getOrDefault(Pair.of(null, null), Collections.emptyList()) : new ArrayList<>());
        defaultValueBucket.setRelatedCFOverrides(context.getRelatedCeilingFloorOverrides() != null ?
                context.getRelatedCeilingFloorOverrides().getOrDefault(Pair.of(null, null), Collections.emptyList()) : new ArrayList<>());
        defaultValueBucket.setSelectedSpecificOverrides(context.getSelectedSpecificOverrides() != null ?
                context.getSelectedSpecificOverrides().getOrDefault(Pair.of(null, null), Collections.emptyList()) : new ArrayList<>());
        defaultValueBucket.setRelatedSpecificOverrides(context.getRelatedSpecificOverrides() != null ?
                context.getRelatedSpecificOverrides().getOrDefault(Pair.of(null, null), Collections.emptyList()) : new ArrayList<>());
        return defaultValueBucket;
    }

    public static List<ProductHierarchyBucket> createBucketsWhenBothLinkedProductsAreDirectChildrenOfParentIP(ProductHierarchyContext context) {

        Set<Pair<LocalDate, LocalDate>> dateToBeConsideredForBuckets = getDatesToBeConsideredForBuckets(
                context.getSelectedLPOffsets() != null ? context.getSelectedLPOffsets().keySet() : null,
                context.getRelatedLPOffsets() != null ? context.getRelatedLPOffsets().keySet() : null,
                context.getSelectedLPOffsetOverrides() != null ? context.getSelectedLPOffsetOverrides().keySet() : null,
                context.getRelatedLPOffsetOverrides() != null ? context.getRelatedLPOffsetOverrides().keySet() : null);

        List<Pair<LocalDate, Boolean>> dateBuckets = createDateBuckets(dateToBeConsideredForBuckets);

        List<ProductHierarchyBucket> productHierarchyBuckets;
        Set<Pair<LocalDate, LocalDate>> seasonalProductSeasons = getSeasonalProductSeasons(context.getSelectedLPOffsets(), context.getRelatedLPOffsets());
        if (CollectionUtils.isNotEmpty(seasonalProductSeasons)) {
            productHierarchyBuckets = createEmptyHierarchyBuckets(dateBuckets).stream()
                    .filter(bucket -> isBucketApplicableForSeasonProductSeasons(bucket, seasonalProductSeasons)).collect(Collectors.toList());
        } else {
            productHierarchyBuckets = createEmptyHierarchyBuckets(dateBuckets);
        }
        productHierarchyBuckets.forEach(bucket -> addDataToBucketWhenBothLinkedProductsAreDirectChildrenOfParentIP(bucket, context));
        List<ProductRateOffset> selectedOffsets = context.getSelectedLPOffsets().values().stream().flatMap(List::stream).collect(Collectors.toList());
        List<ProductRateOffset> relatedOffsets = context.getRelatedLPOffsets().values().stream().flatMap(List::stream).collect(Collectors.toList());
        if (!isSeasonalProduct(selectedOffsets) && !isSeasonalProduct(relatedOffsets)) {
            boolean wasDefaultsToDefaultsAdded = productHierarchyBuckets.stream().anyMatch(ProductHierarchyUtil::isBucketHavingDefaultValueForLP);
            if (!wasDefaultsToDefaultsAdded) {
                productHierarchyBuckets.add(createDefaultBucketWhenBothLinkedProductsAreDirectChildrenOfParentIP(context));
            }
        }
        return productHierarchyBuckets;
    }

    private static Set<Pair<LocalDate, LocalDate>> getSeasonalProductSeasons(Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>> selectedLPOffsets,
                                                                             Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>> relatedLPOffsets) {

        Set<Pair<LocalDate, LocalDate>> seasonDates = new HashSet<>();
        if (MapUtils.isNotEmpty(selectedLPOffsets)) {
            List<ProductRateOffset> selectedOffsets = selectedLPOffsets.values().stream().flatMap(List::stream).collect(Collectors.toList());
            if (isSeasonalProduct(selectedOffsets)) {
                getSeasonDates(selectedOffsets, seasonDates);
            }
        }
        if (MapUtils.isNotEmpty(relatedLPOffsets)) {
            List<ProductRateOffset> relatedOffsets = relatedLPOffsets.values().stream().flatMap(List::stream).collect(Collectors.toList());
            if (isSeasonalProduct(relatedOffsets)) {
                getSeasonDates(relatedOffsets, seasonDates);
            }
        }
        return seasonDates;
    }

    private static void getSeasonDates(List<ProductRateOffset> offsets, Set<Pair<LocalDate, LocalDate>> seasonDates) {
        if (isSeasonalProduct(offsets)) {
            Set<Pair<LocalDate, LocalDate>> datePair = offsets.stream()
                    .collect(groupingBy(offset -> Pair.of(offset.getStartDate(), offset.getEndDate()))).keySet();
            seasonDates.addAll(datePair);
        }
    }

    private static boolean isBucketApplicableForSeasonProductSeasons(ProductHierarchyBucket bucket, Set<Pair<LocalDate, LocalDate>> seasonalProductSeasons) {
        if (CollectionUtils.isEmpty(seasonalProductSeasons)) {
            return false;
        }
        LocalDate startDate = bucket.getStartDate();
        LocalDate endDate = bucket.getEndDate();
        for (Pair<LocalDate, LocalDate> season : seasonalProductSeasons) {
            if ((startDate.isAfter(season.getLeft()) || startDate.isEqual(season.getLeft())) &&
                    (endDate.isBefore(season.getRight()) || endDate.isEqual(season.getRight()))) {
                return true;
            }
        }
        return false;
    }

    public static boolean isSeasonalProduct(List<ProductRateOffset> offsets) {
        return offsets
                .stream()
                .filter(offset -> offset.getStartDate() != null)
                .filter(offset -> offset.getEndDate() != null).count() == offsets.size();
    }

    public static List<ProductHierarchyBucket> createBucketsWhenBothLinkedProductsAreDirectChildrenOfParentIP(ProductHierarchyContext context, LocalDate overrideDate) {

        Set<LocalDate> overrideDates = new HashSet<>(List.of(overrideDate));
        Set<Pair<LocalDate, LocalDate>> dateRanges = createDateBucketsForOverrides(overrideDates);
        List<Pair<LocalDate, Boolean>> dateBuckets = createDateBuckets(dateRanges);
        List<ProductHierarchyBucket> productHierarchyBuckets;
        Set<Pair<LocalDate, LocalDate>> seasonalProductSeasons = getSeasonalProductSeasons(context.getSelectedLPOffsets(), context.getRelatedLPOffsets());
        if (CollectionUtils.isNotEmpty(seasonalProductSeasons)) {
            productHierarchyBuckets = createEmptyHierarchyBuckets(dateBuckets).stream()
                    .filter(bucket -> isBucketApplicableForSeasonProductSeasons(bucket, seasonalProductSeasons)).collect(Collectors.toList());
        } else {
            productHierarchyBuckets = createEmptyHierarchyBuckets(dateBuckets);
        }
        productHierarchyBuckets.forEach(bucket -> addDataToBucketWhenBothLinkedProductsAreDirectChildrenOfParentIP(bucket, context));
        List<ProductRateOffset> selectedOffsets = context.getSelectedLPOffsets().values().stream().flatMap(List::stream).collect(Collectors.toList());
        List<ProductRateOffset> relatedOffsets = context.getRelatedLPOffsets().values().stream().flatMap(List::stream).collect(Collectors.toList());
        if (!isSeasonalProduct(selectedOffsets) && !isSeasonalProduct(relatedOffsets)) {
            boolean wasDefaultsToDefaultsAdded = productHierarchyBuckets.stream().anyMatch(ProductHierarchyUtil::isBucketHavingDefaultValueForLP);
            if (!wasDefaultsToDefaultsAdded) {
                productHierarchyBuckets.add(createDefaultBucketWhenBothLinkedProductsAreDirectChildrenOfParentIP(context));
            }
        }
        return productHierarchyBuckets;
    }

    private static ProductHierarchyBucket createDefaultBucketWhenBothLinkedProductsAreDirectChildrenOfParentIP(ProductHierarchyContext context) {
        ProductHierarchyBucket defaultValueBucket = new ProductHierarchyBucket();

        defaultValueBucket.setSelectedLPOffsets(
                Optional.ofNullable(context.getSelectedLPOffsets())
                        .map(selectedLPOffsets -> selectedLPOffsets.entrySet().stream()
                                .filter(entry -> entry.getKey().getLeft() == null)
                                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)))
                        .orElse(Collections.emptyMap())
        );
        defaultValueBucket.setRelatedLPOffsets(
                Optional.ofNullable(context.getRelatedLPOffsets())
                        .map(relatedLPOffsets -> relatedLPOffsets.entrySet().stream()
                                .filter(entry -> entry.getKey().getLeft() == null)
                                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)))
                        .orElse(Collections.emptyMap())
        );
        defaultValueBucket.setSelectedLPOffsetOverrides(context.getSelectedLPOffsetOverrides() != null ?
                context.getSelectedLPOffsetOverrides().getOrDefault(Pair.of(null, null), Collections.emptyList()) : new ArrayList<>());
        defaultValueBucket.setRelatedLPOffsetOverrides(context.getRelatedLPOffsetOverrides() != null ?
                context.getRelatedLPOffsetOverrides().getOrDefault(Pair.of(null, null), Collections.emptyList()) : new ArrayList<>());
        return defaultValueBucket;
    }

    private static boolean isBucketHavingDefaultValueForLP(ProductHierarchyBucket bucket) {
        return (MapUtils.isEmpty(bucket.getRelatedLPOffsets()) || bucket.getRelatedLPOffsets().keySet().stream().allMatch(pair -> pair.getLeft() == null) &&
                (MapUtils.isEmpty(bucket.getSelectedLPOffsets()) || bucket.getSelectedLPOffsets().keySet().stream().allMatch(pair -> pair.getLeft() == null)));
    }

    static void addDataToBucketWhenBothLinkedProductsAreDirectChildrenOfParentIP(ProductHierarchyBucket bucket, ProductHierarchyContext context) {

        LocalDate startBucketDate = bucket.getStartDate();
        LocalDate endBucketDate = bucket.getEndDate();
        bucket.setSelectedLPOffsets(findOverlappingOffsetsForLP(startBucketDate, endBucketDate, context.getSelectedLPOffsets(), context.getSharedAccomTypes()));
        bucket.setRelatedLPOffsets(findOverlappingOffsetsForLP(startBucketDate, endBucketDate, context.getRelatedLPOffsets(), context.getSharedAccomTypes()));
        bucket.setSelectedLPOffsetOverrides(findOverlappingOverridesForLP(startBucketDate, endBucketDate, context.getSelectedLPOffsetOverrides()));
        bucket.setRelatedLPOffsetOverrides(findOverlappingOverridesForLP(startBucketDate, endBucketDate, context.getRelatedLPOffsetOverrides()));
    }

    public static void updateWithOffsetOverridesForLP(List<ProductRateOffset> offsetOverrides, List<ProductRateOffset> offsets, LocalDate overrideDate) {

        if (CollectionUtils.isEmpty(offsetOverrides)) {
            return;
        }
        boolean isOffsetUpdated = false;

        ProductRateOffset override = offsetOverrides.stream().findFirst().orElse(new ProductRateOffset());
        List<ProductRateOffset> defaultOffsets = offsets.stream().filter(off -> off.getStartDate() == null).collect(Collectors.toList());
        Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>> seasonOffsets = offsets.stream()
                .filter(off -> off.getStartDate() != null)
                .collect(groupingBy(off -> Pair.of(off.getStartDate(), off.getEndDate())));

        for (Map.Entry<Pair<LocalDate, LocalDate>, List<ProductRateOffset>> offset : seasonOffsets.entrySet()) {
            boolean isOverrideBetweenSeasonRange = isDateBetweenDateRange(offset.getKey().getLeft(), offset.getKey().getRight(), overrideDate);
            if (isOverrideBetweenSeasonRange) {
                ProductRateOffset offsetToUpdate = offset.getValue().stream()
                        .filter(off -> off.getAccomClass().equals(override.getAccomClass()) &&
                                ((Objects.isNull(off.getAgileRatesDTARange()) && Objects.isNull(override.getAgileRatesDTARange())) ||
                                        off.getAgileRatesDTARange().equals(override.getAgileRatesDTARange())))
                        .findAny().orElse(null);
                if (Objects.nonNull(offsetToUpdate)) {
                    updateOffsetWithOverride(offsetToUpdate, override, overrideDate, isOffsetUpdated);
                    return;
                }
            }
        }
        ProductRateOffset offsetToUpdate = defaultOffsets.stream()
                .filter(off -> off.getAccomClass().equals(override.getAccomClass()) &&
                        ((Objects.isNull(off.getAgileRatesDTARange()) && Objects.isNull(override.getAgileRatesDTARange())) ||
                                off.getAgileRatesDTARange().equals(override.getAgileRatesDTARange())))
                .findAny().orElse(null);
        updateOffsetWithOverride(offsetToUpdate, override, overrideDate, isOffsetUpdated);
    }

    private static void updateOffsetWithOverride(ProductRateOffset offsetToUpdate, ProductRateOffset override, LocalDate overrideDate, boolean isOffsetUpdated) {

        if (Objects.isNull(offsetToUpdate) || Objects.isNull(override)) {
            return;
        }
        DayOfWeek dayOfWeek = DayOfWeek.valueOf(overrideDate.getDayOfWeek());
        switch (dayOfWeek) {
            case SUNDAY:
                if (override.getSundayOffsetValueFloor() != null)
                    offsetToUpdate.setSundayOffsetValueFloor(override.getSundayOffsetValueFloor());
                if (override.getSundayOffsetValueCeiling() != null)
                    offsetToUpdate.setSundayOffsetValueCeiling(override.getSundayOffsetValueCeiling());
                break;
            case MONDAY:
                if (override.getMondayOffsetValueFloor() != null)
                    offsetToUpdate.setMondayOffsetValueFloor(override.getMondayOffsetValueFloor());
                if (override.getMondayOffsetValueCeiling() != null)
                    offsetToUpdate.setMondayOffsetValueCeiling(override.getMondayOffsetValueCeiling());
                break;
            case TUESDAY:
                if (override.getTuesdayOffsetValueFloor() != null)
                    offsetToUpdate.setTuesdayOffsetValueFloor(override.getTuesdayOffsetValueFloor());
                if (override.getTuesdayOffsetValueCeiling() != null)
                    offsetToUpdate.setTuesdayOffsetValueCeiling(override.getTuesdayOffsetValueCeiling());
                break;
            case WEDNESDAY:
                if (override.getWednesdayOffsetValueFloor() != null)
                    offsetToUpdate.setWednesdayOffsetValueFloor(override.getWednesdayOffsetValueFloor());
                if (override.getWednesdayOffsetValueCeiling() != null)
                    offsetToUpdate.setWednesdayOffsetValueCeiling(override.getWednesdayOffsetValueCeiling());
                break;
            case THURSDAY:
                if (override.getThursdayOffsetValueFloor() != null)
                    offsetToUpdate.setThursdayOffsetValueFloor(override.getThursdayOffsetValueFloor());
                if (override.getThursdayOffsetValueCeiling() != null)
                    offsetToUpdate.setThursdayOffsetValueCeiling(override.getThursdayOffsetValueCeiling());
                break;
            case FRIDAY:
                if (override.getFridayOffsetValueFloor() != null)
                    offsetToUpdate.setFridayOffsetValueFloor(override.getFridayOffsetValueFloor());
                if (override.getFridayOffsetValueCeiling() != null)
                    offsetToUpdate.setFridayOffsetValueCeiling(override.getFridayOffsetValueCeiling());
                break;
            case SATURDAY:
                if (override.getSaturdayOffsetValueFloor() != null)
                    offsetToUpdate.setSaturdayOffsetValueFloor(override.getSaturdayOffsetValueFloor());
                if (override.getSaturdayOffsetValueCeiling() != null)
                    offsetToUpdate.setSaturdayOffsetValueCeiling(override.getSaturdayOffsetValueCeiling());
                break;
        }
    }

    public static boolean isDateBetweenDateRange(LocalDate startDate, LocalDate endDate, LocalDate date) {
        return (date.isEqual(startDate) || date.isAfter(startDate)) && (date.isEqual(endDate) || date.isBefore(endDate));
    }

    public static ProductRateOffset createProductRateOffset(AccomClass accomClass, ProductRateOffset productRateOffset) {
        ProductRateOffset offset = new ProductRateOffset();
        offset.setId(productRateOffset.getId());
        offset.setProduct(productRateOffset.getProduct());
        offset.setAccomClass(accomClass);
        offset.setAgileRatesDTARange(productRateOffset.getAgileRatesDTARange());
        offset.setStartDate(productRateOffset.getStartDate());
        offset.setEndDate(productRateOffset.getEndDate());
        offset.setOffsetMethod(productRateOffset.getOffsetMethod());
        offset.setSundayOffsetValueFloor(productRateOffset.getSundayOffsetValueFloor());
        offset.setSundayOffsetValueCeiling(productRateOffset.getSundayOffsetValueCeiling());
        offset.setMondayOffsetValueFloor(productRateOffset.getMondayOffsetValueFloor());
        offset.setMondayOffsetValueCeiling(productRateOffset.getMondayOffsetValueCeiling());
        offset.setTuesdayOffsetValueFloor(productRateOffset.getTuesdayOffsetValueFloor());
        offset.setTuesdayOffsetValueCeiling(productRateOffset.getTuesdayOffsetValueCeiling());
        offset.setWednesdayOffsetValueFloor(productRateOffset.getWednesdayOffsetValueFloor());
        offset.setWednesdayOffsetValueCeiling(productRateOffset.getWednesdayOffsetValueCeiling());
        offset.setThursdayOffsetValueFloor(productRateOffset.getThursdayOffsetValueFloor());
        offset.setThursdayOffsetValueCeiling(productRateOffset.getThursdayOffsetValueCeiling());
        offset.setFridayOffsetValueFloor(productRateOffset.getFridayOffsetValueFloor());
        offset.setFridayOffsetValueCeiling(productRateOffset.getFridayOffsetValueCeiling());
        offset.setSaturdayOffsetValueFloor(productRateOffset.getSaturdayOffsetValueFloor());
        offset.setSaturdayOffsetValueCeiling(productRateOffset.getSaturdayOffsetValueCeiling());
        offset.setSeasonName(productRateOffset.getSeasonName());
        offset.setSeasonDowOffset(productRateOffset.isSeasonDowOffset());
        return offset;
    }


    static Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>> getLPOffsets(List<ProductRateOffset> offsetList, Product product) {
        return offsetList.stream().filter(off -> product.getId().equals(off.getProduct().getId()))
                .collect(groupingBy(ovr -> Pair.of(ovr.getStartDate(), ovr.getEndDate())));
    }

    static List<ProductRateOffset> updateProductRateOffsetsByDTARange(List<ProductRateOffset> selectedProductRateOffsets,
                                                                      List<ProductRateOffset> relatedProductRateOffsets,
                                                                      List<AgileRatesDTARange> agileRatesDTARange) {
        List<ProductRateOffset> resultOffsets = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(selectedProductRateOffsets)) {
            List<ProductRateOffset> defaultOffsetsForSelected = selectedProductRateOffsets.stream()
                    .filter(off -> off.getStartDate() == null)
                    .collect(Collectors.toList());
            List<ProductRateOffset> seasonOffsetsForSelected = selectedProductRateOffsets.stream()
                    .filter(off -> off.getStartDate() != null)
                    .collect(Collectors.toList());
            getOffsetsByDTARange(agileRatesDTARange, resultOffsets, defaultOffsetsForSelected, seasonOffsetsForSelected);
        }
        if (CollectionUtils.isNotEmpty(relatedProductRateOffsets)) {
            List<ProductRateOffset> defaultOffsetsForRelated = relatedProductRateOffsets.stream()
                    .filter(off -> off.getStartDate() == null)
                    .collect(Collectors.toList());
            List<ProductRateOffset> seasonOffsetsForRelated = relatedProductRateOffsets.stream()
                    .filter(off -> off.getStartDate() != null)
                    .collect(Collectors.toList());
            getOffsetsByDTARange(agileRatesDTARange, resultOffsets, defaultOffsetsForRelated, seasonOffsetsForRelated);
        }

        return resultOffsets;
    }

    private static void getOffsetsByDTARange(List<AgileRatesDTARange> agileRatesDTARange,
                                             List<ProductRateOffset> resultOffsets,
                                             List<ProductRateOffset> defaultOffsets,
                                             List<ProductRateOffset> seasonOffsets) {

        Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>> seasonsByDate = seasonOffsets.stream()
                .collect(groupingBy(off -> Pair.of(off.getStartDate(), off.getEndDate())));

        if (CollectionUtils.isNotEmpty(defaultOffsets) && defaultOffsets.get(0).getAgileRatesDTARange() == null &&
                CollectionUtils.isNotEmpty(agileRatesDTARange)) {
            createOffsetsForDTARange(defaultOffsets, resultOffsets, agileRatesDTARange);
        } else {
            resultOffsets.addAll(defaultOffsets);
        }
        for (Map.Entry<Pair<LocalDate, LocalDate>, List<ProductRateOffset>> season : seasonsByDate.entrySet()) {
            if (CollectionUtils.isNotEmpty(season.getValue()) && season.getValue().stream().allMatch(off -> off.getAgileRatesDTARange() == null) &&
                    CollectionUtils.isNotEmpty(agileRatesDTARange)) {
                createOffsetsForDTARange(seasonOffsets, resultOffsets, agileRatesDTARange);
            } else {
                resultOffsets.addAll(season.getValue());
            }
        }
    }

    static public void createOffsetsForDTARange(List<ProductRateOffset> nonDTAProductRateOffsets, List<ProductRateOffset> offsetsByDTARange,
                                                List<AgileRatesDTARange> agileRatesDTARange) {
        if (CollectionUtils.isEmpty(agileRatesDTARange)) {
            return;
        }
        for (AgileRatesDTARange dta : agileRatesDTARange) {
            for (ProductRateOffset offset : nonDTAProductRateOffsets) {
                ProductRateOffset newOffset = offset.copy();
                newOffset.setAgileRatesDTARange(dta);
                offsetsByDTARange.add(newOffset);
            }
        }
    }

    private static List<ProductRateOffsetOverride> findOverlappingOverridesForLP(LocalDate startDate, LocalDate endDate,
                                                                                 Map<Pair<LocalDate, LocalDate>, List<ProductRateOffsetOverride>> overrides) {
        if (MapUtils.isEmpty(overrides)) {
            return Collections.emptyList();
        }
        List<ProductRateOffsetOverride> filteredOffsets = overrides.entrySet()
                .stream()
                .filter(entry -> Objects.nonNull(entry.getKey().getLeft()))
                .filter(entry -> areDatesOverlapped(startDate, endDate, entry.getKey().getLeft(), entry.getKey().getRight()))
                .map(Map.Entry::getValue)
                .findFirst()
                .orElse(Collections.emptyList());
        return CollectionUtils.isNotEmpty(filteredOffsets) ?
                filteredOffsets :
                CollectionUtils.isEmpty(overrides.get(Pair.of(null, null))) ? Collections.emptyList()
                        : overrides.get(Pair.of(null, null));

    }

    private static Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>> findOverlappingOffsetsForLP(LocalDate startDate, LocalDate endDate,
                                                                                                        Map<Pair<LocalDate, LocalDate>, List<ProductRateOffset>> offsets,
                                                                                                        Set<AccomType> sharedAccomTypes) {
        List<ProductRateOffset> filteredOffsets = offsets.entrySet()
                .stream()
                .filter(entry -> Objects.nonNull(entry.getKey().getLeft()))
                .filter(entry -> areDatesOverlapped(startDate, endDate, entry.getKey().getLeft(), entry.getKey().getRight()))
                .map(Map.Entry::getValue)
                .findFirst()
                .orElse(Collections.emptyList());
        return CollectionUtils.isNotEmpty(filteredOffsets) ?
                filteredOffsets.stream().collect(groupingBy(offset -> Pair.of(offset.getStartDate(), offset.getEndDate()))) :
                offsets;
    }

    static List<PricingBaseAccomType> findOverlappingCeilingFloorValues(LocalDate startDate, LocalDate endDate,
                                                                        Map<Pair<LocalDate, LocalDate>, List<PricingBaseAccomType>> ceilingFloor) {
        List<PricingBaseAccomType> filteredCeilingFLoor = ceilingFloor.entrySet()
                .stream()
                .filter(entry -> Objects.nonNull(entry.getKey().getLeft()))
                .filter(entry -> areDatesOverlapped(startDate, endDate, entry.getKey().getLeft(), entry.getKey().getRight()))
                .map(Map.Entry::getValue)
                .findFirst()
                .orElse(Collections.emptyList());
        return CollectionUtils.isNotEmpty(filteredCeilingFLoor) ?
                filteredCeilingFLoor :
                ceilingFloor.get(Pair.of(null, null));
    }

    static List<CPConfigOffsetAccomType> findOverlappingOffsets(LocalDate startDate, LocalDate endDate,
                                                                Map<Pair<LocalDate, LocalDate>, List<CPConfigOffsetAccomType>> offsets) {
        List<CPConfigOffsetAccomType> filteredOffsets = offsets.entrySet()
                .stream()
                .filter(entry -> Objects.nonNull(entry.getKey().getLeft()))
                .filter(entry -> areDatesOverlapped(startDate, endDate, entry.getKey().getLeft(), entry.getKey().getRight()))
                .map(Map.Entry::getValue)
                .findFirst()
                .orElse(Collections.emptyList());
        return CollectionUtils.isNotEmpty(filteredOffsets) ?
                filteredOffsets :
                CollectionUtils.isEmpty(offsets.get(Pair.of(null, null))) ? Collections.emptyList()
                        : offsets.get(Pair.of(null, null));
    }

    static List<CPDecisionBAROutput> findOverlappingOverrides(LocalDate startDate, LocalDate endDate,
                                                              Map<Pair<LocalDate, LocalDate>, List<CPDecisionBAROutput>> productOverrides) {
        if (MapUtils.isEmpty(productOverrides)) {
            return Collections.emptyList();
        }
        return productOverrides.entrySet()
                .stream()
                .filter(entry -> Objects.nonNull(entry.getKey().getLeft()))
                .filter(entry -> areDatesOverlapped(startDate, endDate, entry.getKey().getLeft(), entry.getKey().getRight()))
                .map(Map.Entry::getValue)
                .findFirst()
                .orElse(Collections.emptyList());

    }

    static boolean areDatesOverlapped(LocalDate startDateOfEditingSeason, LocalDate endDateOfEditingSeason,
                                      LocalDate startDateOfNonEditingSeason, LocalDate endDateOfNonEditingSeason) {
        return ((startDateOfEditingSeason.isBefore(startDateOfNonEditingSeason) || startDateOfEditingSeason.isEqual(startDateOfNonEditingSeason)) && (endDateOfEditingSeason.isAfter(startDateOfNonEditingSeason) || endDateOfEditingSeason.isEqual(startDateOfNonEditingSeason))) ||
                ((startDateOfEditingSeason.isBefore(endDateOfNonEditingSeason) || startDateOfEditingSeason.isEqual(endDateOfNonEditingSeason)) && (endDateOfEditingSeason.isAfter(endDateOfNonEditingSeason) || endDateOfEditingSeason.isEqual(endDateOfNonEditingSeason))) ||
                ((startDateOfEditingSeason.isBefore(startDateOfNonEditingSeason) || startDateOfEditingSeason.isEqual(startDateOfNonEditingSeason)) && (endDateOfEditingSeason.isAfter(endDateOfNonEditingSeason) || endDateOfEditingSeason.isEqual(endDateOfNonEditingSeason))) ||
                ((startDateOfEditingSeason.isAfter(startDateOfNonEditingSeason) || startDateOfEditingSeason.isEqual(startDateOfNonEditingSeason)) && (endDateOfEditingSeason.isBefore(endDateOfNonEditingSeason) || endDateOfEditingSeason.isEqual(endDateOfNonEditingSeason)));
    }

    static boolean isAnyInputNull(Object... inputs) {
        return !ObjectUtils.allNotNull(inputs);
    }

    static BigDecimal getOverrideOrFinalBar(Map<AccomType, CPDecisionBAROutput> specificOverridesByAccomType, AccomType accomType,
                                            ProductHierarchyBucket bucket) {
        if (specificOverridesByAccomType.containsKey(accomType)) {
            return specificOverridesByAccomType.get(accomType).getSpecificOverride();
        } else {
            CPDecisionBAROutput decisionForCurrentDateAndAccomType = bucket.getSelectedDecisions().get(bucket.getStartDate())
                    .stream()
                    .filter(d -> d.getAccomType().equals(accomType))
                    .findFirst().orElse(new CPDecisionBAROutput());
            return decisionForCurrentDateAndAccomType.getFinalBAR();
        }
    }

    static void addCFOverridesToPricingBaseAccomType(PricingBaseAccomType pricingBaseAccomType, BigDecimal ceilOverride,
                                                     BigDecimal floorOverride, DayOfWeek dayOfWeek) {
        switch (dayOfWeek) {
            case SUNDAY:
                if (ceilOverride != null) pricingBaseAccomType.setSundayCeilingRateWithTax(ceilOverride);
                if (floorOverride != null) pricingBaseAccomType.setSundayFloorRateWithTax(floorOverride);
                break;
            case MONDAY:
                if (ceilOverride != null) pricingBaseAccomType.setMondayCeilingRateWithTax(ceilOverride);
                if (floorOverride != null) pricingBaseAccomType.setMondayFloorRateWithTax(floorOverride);
                break;
            case TUESDAY:
                if (ceilOverride != null) pricingBaseAccomType.setTuesdayCeilingRateWithTax(ceilOverride);
                if (floorOverride != null) pricingBaseAccomType.setTuesdayFloorRateWithTax(floorOverride);
                break;
            case WEDNESDAY:
                if (ceilOverride != null) pricingBaseAccomType.setWednesdayCeilingRateWithTax(ceilOverride);
                if (floorOverride != null) pricingBaseAccomType.setWednesdayFloorRateWithTax(floorOverride);
                break;
            case THURSDAY:
                if (ceilOverride != null) pricingBaseAccomType.setThursdayCeilingRateWithTax(ceilOverride);
                if (floorOverride != null) pricingBaseAccomType.setThursdayFloorRateWithTax(floorOverride);
                break;
            case FRIDAY:
                if (ceilOverride != null) pricingBaseAccomType.setFridayCeilingRateWithTax(ceilOverride);
                if (floorOverride != null) pricingBaseAccomType.setFridayFloorRateWithTax(floorOverride);
                break;
            case SATURDAY:
                if (ceilOverride != null) pricingBaseAccomType.setSaturdayCeilingRateWithTax(ceilOverride);
                if (floorOverride != null) pricingBaseAccomType.setSaturdayFloorRateWithTax(floorOverride);
                break;
        }
    }

    static boolean verifyOffsetsOverlapGivenOptimizedProducts(ProductRateOffset selectedOffset, ProductRateOffset relatedOffset,
                                                              BigDecimal minimumDifference, List<String> validationMessages) {
        List<BigDecimal> selectedFloorOffsetVals = getFloorOffsetValues(Collections.singletonList(selectedOffset));
        List<BigDecimal> relatedFloorOffsetVals = getFloorOffsetValues(Collections.singletonList(relatedOffset));
        List<BigDecimal> selectedCeilOffsetVals = getCeilingOffsetValues(Collections.singletonList(selectedOffset));
        List<BigDecimal> relatedCeilOffsetVals = getCeilingOffsetValues(Collections.singletonList(relatedOffset));

        boolean hasMatchingDOW = false;
        for (int i = 0; i < selectedFloorOffsetVals.size(); i++) {
            if (selectedFloorOffsetVals.get(i) != null && relatedFloorOffsetVals.get(i) != null) {
                hasMatchingDOW = true;
                // 1) floor of selected  <= floor of related
                // 2) ceiling of selected <= ceiling of related
                // 3) overlap >= minimum difference
                //overlapAmount = Ceiling Of Related Product (Greater than product) - Floor of Selected Product (Less than product)
                double overlapAmount = relatedCeilOffsetVals.get(i).doubleValue() - selectedFloorOffsetVals.get(i).doubleValue();
                if (selectedFloorOffsetVals.get(i).doubleValue() > relatedFloorOffsetVals.get(i).doubleValue()) {
                    validationMessages.add("invalid.hierarchy.min.adjustment.value.linked.product");
                }
                if (selectedCeilOffsetVals.get(i).doubleValue() > relatedCeilOffsetVals.get(i).doubleValue()) {
                    validationMessages.add("invalid.hierarchy.max.adjustment.value.linked.product");
                }
                if (isOverlapNotExist(selectedFloorOffsetVals.get(i).doubleValue(), selectedCeilOffsetVals.get(i).doubleValue(),
                        relatedFloorOffsetVals.get(i).doubleValue(), relatedCeilOffsetVals.get(i).doubleValue())) {
                    validationMessages.add("invalid.hierarchy.overlap.adjustment.value.linked.product");
                }
                if (overlapAmount < minimumDifference.doubleValue()) {
                    validationMessages.add("invalid.hierarchy.mindiff.value.linked.product");
                }
                if (overlapAmount < 0 || CollectionUtils.isNotEmpty(validationMessages)) {
                    return false;
                }
            }
        }
        return hasMatchingDOW;
    }

    static boolean verifyOffsetsOverlapWithLinkedProduct(ProductRateOffset linkedProductOffset, ProductRateOffset optimizedProductOffset,
                                                         BigDecimal minimumDifference, List<String> validationMessages) {
        List<BigDecimal> linkedFloorOffsetVals = getFloorOffsetValues(Collections.singletonList(linkedProductOffset));
        List<BigDecimal> optimizedFloorOffsetVals = getFloorOffsetValues(Collections.singletonList(optimizedProductOffset));
        List<BigDecimal> optimizedCeilOffsetVals = getCeilingOffsetValues(Collections.singletonList(optimizedProductOffset));

        boolean hasMatchingDOW = false;
        for (int i = 0; i < linkedFloorOffsetVals.size(); i++) {
            if (linkedFloorOffsetVals.get(i) != null && optimizedFloorOffsetVals.get(i) != null) {
                hasMatchingDOW = true;
                // 1) the linked product's value must have some overlap of the floor and ceiling of the optimized product's floor and ceiling
                if (!(linkedFloorOffsetVals.get(i).doubleValue() >= optimizedFloorOffsetVals.get(i).doubleValue()//28>=35 && 28<=30
                        && linkedFloorOffsetVals.get(i).doubleValue() <= optimizedCeilOffsetVals.get(i).doubleValue())) {
                    validationMessages.add("invalid.hierarchy.overlap.adjustment.value.linked.product");
                    return false;
                }
                if ((Math.abs(optimizedCeilOffsetVals.get(i).doubleValue() - linkedFloorOffsetVals.get(i).doubleValue())
                        < minimumDifference.doubleValue())) {
                    // 2) if the relationship is linked < optimized there must be at least a difference = minimum difference
                    // between the linked product's offset and optimized product's floor
                    validationMessages.add("invalid.hierarchy.mindiff.value.linked.product");
                    return false;
                }
            }
        }
        return hasMatchingDOW;
    }

    static List<BigDecimal> getFloorOffsetValues(List<ProductRateOffset> offsetValues) {
        List<BigDecimal> floorOffsetValues = new ArrayList<>();
        for (ProductRateOffset offset : offsetValues) {
            floorOffsetValues.add(offset.getSundayOffsetValueFloor());
            floorOffsetValues.add(offset.getMondayOffsetValueFloor());
            floorOffsetValues.add(offset.getTuesdayOffsetValueFloor());
            floorOffsetValues.add(offset.getWednesdayOffsetValueFloor());
            floorOffsetValues.add(offset.getThursdayOffsetValueFloor());
            floorOffsetValues.add(offset.getFridayOffsetValueFloor());
            floorOffsetValues.add(offset.getSaturdayOffsetValueFloor());
        }
        return floorOffsetValues;
    }

    static List<BigDecimal> getCeilingOffsetValues(List<ProductRateOffset> offsetValues) {
        List<BigDecimal> ceilOffsetValues = new ArrayList<>();
        for (ProductRateOffset offset : offsetValues) {
            ceilOffsetValues.add(offset.getSundayOffsetValueCeiling());
            ceilOffsetValues.add(offset.getMondayOffsetValueCeiling());
            ceilOffsetValues.add(offset.getTuesdayOffsetValueCeiling());
            ceilOffsetValues.add(offset.getWednesdayOffsetValueCeiling());
            ceilOffsetValues.add(offset.getThursdayOffsetValueCeiling());
            ceilOffsetValues.add(offset.getFridayOffsetValueCeiling());
            ceilOffsetValues.add(offset.getSaturdayOffsetValueCeiling());
        }
        return ceilOffsetValues;
    }

    static void addOffsetOverridesToProductRateOffset(List<ProductRateOffset> offsets, BigDecimal ceilOverride, BigDecimal floorOverride, DayOfWeek dayOfWeek) {

        for (ProductRateOffset offset : offsets) {
            switch (dayOfWeek) {
                case SUNDAY:
                    if (ceilOverride != null) offset.setSundayOffsetValueCeiling(ceilOverride);
                    if (floorOverride != null) offset.setSundayOffsetValueFloor(floorOverride);
                    break;
                case MONDAY:
                    if (ceilOverride != null) offset.setMondayOffsetValueCeiling(ceilOverride);
                    if (floorOverride != null) offset.setMondayOffsetValueFloor(floorOverride);
                    break;
                case TUESDAY:
                    if (ceilOverride != null) offset.setTuesdayOffsetValueCeiling(ceilOverride);
                    if (floorOverride != null) offset.setTuesdayOffsetValueFloor(floorOverride);
                    break;
                case WEDNESDAY:
                    if (ceilOverride != null) offset.setWednesdayOffsetValueCeiling(ceilOverride);
                    if (floorOverride != null) offset.setWednesdayOffsetValueFloor(floorOverride);
                    break;
                case THURSDAY:
                    if (ceilOverride != null) offset.setThursdayOffsetValueCeiling(ceilOverride);
                    if (floorOverride != null) offset.setThursdayOffsetValueFloor(floorOverride);
                    break;
                case FRIDAY:
                    if (ceilOverride != null) offset.setFridayOffsetValueCeiling(ceilOverride);
                    if (floorOverride != null) offset.setFridayOffsetValueFloor(floorOverride);
                    break;
                case SATURDAY:
                    if (ceilOverride != null) offset.setSaturdayOffsetValueCeiling(ceilOverride);
                    if (floorOverride != null) offset.setSaturdayOffsetValueFloor(floorOverride);
                    break;
            }
        }
    }

    static boolean isOverlapNotExist(double selectedFloorValue, double selectedCeilingValue, double relatedFloorValue, double relatedCeilingValue) {
        return (!isValueBetween(relatedFloorValue, selectedFloorValue, selectedCeilingValue)
                && !isValueBetween(relatedCeilingValue, selectedFloorValue, selectedCeilingValue))
                && (!isValueBetween(selectedFloorValue, relatedFloorValue, relatedCeilingValue)
                && !isValueBetween(selectedCeilingValue, relatedFloorValue, relatedCeilingValue));
    }

    static ProductRateOffset calculateLowestCeilingHighestFloorOffset(List<ProductRateOffset> existingCalculatedOffsetsForParent) {
        ProductRateOffset copy = existingCalculatedOffsetsForParent.get(0).copy();

        copy.setSundayOffsetValueFloor(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getSundayOffsetValueFloor).max(Comparator.nullsLast(Comparator.reverseOrder())).orElse(null));
        copy.setSundayOffsetValueCeiling(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getSundayOffsetValueCeiling).min(Comparator.nullsLast(Comparator.naturalOrder())).orElse(null));
        copy.setMondayOffsetValueFloor(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getMondayOffsetValueFloor).max(Comparator.nullsLast(Comparator.reverseOrder())).orElse(null));
        copy.setMondayOffsetValueCeiling(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getMondayOffsetValueCeiling).min(Comparator.nullsLast(Comparator.naturalOrder())).orElse(null));
        copy.setTuesdayOffsetValueFloor(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getTuesdayOffsetValueFloor).max(Comparator.nullsLast(Comparator.reverseOrder())).orElse(null));
        copy.setTuesdayOffsetValueCeiling(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getTuesdayOffsetValueCeiling).min(Comparator.nullsLast(Comparator.naturalOrder())).orElse(null));
        copy.setWednesdayOffsetValueFloor(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getWednesdayOffsetValueFloor).max(Comparator.nullsLast(Comparator.reverseOrder())).orElse(null));
        copy.setWednesdayOffsetValueCeiling(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getWednesdayOffsetValueCeiling).min(Comparator.nullsLast(Comparator.naturalOrder())).orElse(null));
        copy.setThursdayOffsetValueFloor(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getThursdayOffsetValueFloor).max(Comparator.nullsLast(Comparator.reverseOrder())).orElse(null));
        copy.setThursdayOffsetValueCeiling(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getThursdayOffsetValueCeiling).min(Comparator.nullsLast(Comparator.naturalOrder())).orElse(null));
        copy.setFridayOffsetValueFloor(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getFridayOffsetValueFloor).max(Comparator.nullsLast(Comparator.reverseOrder())).orElse(null));
        copy.setFridayOffsetValueCeiling(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getFridayOffsetValueCeiling).min(Comparator.nullsLast(Comparator.naturalOrder())).orElse(null));
        copy.setSaturdayOffsetValueFloor(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getSaturdayOffsetValueFloor).max(Comparator.nullsLast(Comparator.reverseOrder())).orElse(null));
        copy.setSaturdayOffsetValueCeiling(existingCalculatedOffsetsForParent.stream().map(ProductRateOffset::getSaturdayOffsetValueCeiling).min(Comparator.nullsLast(Comparator.naturalOrder())).orElse(null));

        return copy;
    }

    static void updateCalculatedOffsets(List<ProductRateOffset> calculatedOffsets, ProductRateOffset offset, ProductRateOffset existingCalculatedOffset) {
        if (existingCalculatedOffset != null) {
            existingCalculatedOffset.setProduct(offset.getProduct());
            existingCalculatedOffset.setAgileRatesDTARange(offset.getAgileRatesDTARange());
            existingCalculatedOffset.setSundayOffsetValueCeiling(calculateNewOffset(existingCalculatedOffset.getSundayOffsetValueCeiling(), offset.getSundayOffsetValueCeiling()));
            existingCalculatedOffset.setSundayOffsetValueFloor(calculateNewOffset(existingCalculatedOffset.getSundayOffsetValueFloor(), offset.getSundayOffsetValueFloor()));
            existingCalculatedOffset.setMondayOffsetValueCeiling(calculateNewOffset(existingCalculatedOffset.getMondayOffsetValueCeiling(), offset.getMondayOffsetValueCeiling()));
            existingCalculatedOffset.setMondayOffsetValueFloor(calculateNewOffset(existingCalculatedOffset.getMondayOffsetValueFloor(), offset.getMondayOffsetValueFloor()));
            existingCalculatedOffset.setTuesdayOffsetValueCeiling(calculateNewOffset(existingCalculatedOffset.getTuesdayOffsetValueCeiling(), offset.getTuesdayOffsetValueCeiling()));
            existingCalculatedOffset.setTuesdayOffsetValueFloor(calculateNewOffset(existingCalculatedOffset.getTuesdayOffsetValueFloor(), offset.getTuesdayOffsetValueFloor()));
            existingCalculatedOffset.setWednesdayOffsetValueCeiling(calculateNewOffset(existingCalculatedOffset.getWednesdayOffsetValueCeiling(), offset.getWednesdayOffsetValueCeiling()));
            existingCalculatedOffset.setWednesdayOffsetValueFloor(calculateNewOffset(existingCalculatedOffset.getWednesdayOffsetValueFloor(), offset.getWednesdayOffsetValueFloor()));
            existingCalculatedOffset.setThursdayOffsetValueCeiling(calculateNewOffset(existingCalculatedOffset.getThursdayOffsetValueCeiling(), offset.getThursdayOffsetValueCeiling()));
            existingCalculatedOffset.setThursdayOffsetValueFloor(calculateNewOffset(existingCalculatedOffset.getThursdayOffsetValueFloor(), offset.getThursdayOffsetValueFloor()));
            existingCalculatedOffset.setFridayOffsetValueCeiling(calculateNewOffset(existingCalculatedOffset.getFridayOffsetValueCeiling(), offset.getFridayOffsetValueCeiling()));
            existingCalculatedOffset.setFridayOffsetValueFloor(calculateNewOffset(existingCalculatedOffset.getFridayOffsetValueFloor(), offset.getFridayOffsetValueFloor()));
            existingCalculatedOffset.setSaturdayOffsetValueCeiling(calculateNewOffset(existingCalculatedOffset.getSaturdayOffsetValueCeiling(), offset.getSaturdayOffsetValueCeiling()));
            existingCalculatedOffset.setSaturdayOffsetValueFloor(calculateNewOffset(existingCalculatedOffset.getSaturdayOffsetValueFloor(), offset.getSaturdayOffsetValueFloor()));
            calculatedOffsets.add(existingCalculatedOffset);
        } else {
            calculatedOffsets.add(offset.copy());
        }
    }

    static boolean verifyOffsetsOverlap(ProductRateOffset selectedOffset, ProductRateOffset relatedOffset,
                                        BigDecimal minimumDifference, List<String> validationMessages) {
        ProductRateOffset linkedProductOffset = null;
        ProductRateOffset optimizedProductOffset = null;
        if (selectedOffset.getProduct().isNonOptimizedProduct() || relatedOffset.getProduct().isNonOptimizedProduct()) {
            linkedProductOffset = selectedOffset.getProduct().isNonOptimizedProduct() ? selectedOffset : relatedOffset;
            optimizedProductOffset = selectedOffset.getProduct().isOptimized() ? selectedOffset : relatedOffset;
        }

        if (linkedProductOffset != null && !isFloorCeilingARange(linkedProductOffset)) {
            return verifyOffsetsOverlapWithLinkedProduct(linkedProductOffset, optimizedProductOffset, minimumDifference, validationMessages);
        } else {
            return verifyOffsetsOverlapGivenOptimizedProducts(selectedOffset, relatedOffset, minimumDifference, validationMessages);
        }
    }

    static boolean isFloorCeilingARange(ProductRateOffset rates) {
        List<BigDecimal> floorOffsetValues = getFloorOffsetValues(Collections.singletonList(rates));
        List<BigDecimal> ceilOffsetValues = getCeilingOffsetValues(Collections.singletonList(rates));

        return IntStream.range(0, floorOffsetValues.size())
                .anyMatch((idx) -> !floorOffsetValues.get(idx).equals(ceilOffsetValues.get(idx)));
    }

    static BigDecimal calculateNewOffset(BigDecimal existingOffset, BigDecimal offset) {
        BigDecimal existingValue = BigDecimal.ONE.add(BigDecimalUtil.divide(existingOffset, BigDecimal.valueOf(100)));
        BigDecimal offsetValue = BigDecimal.ONE.add(BigDecimalUtil.divide(offset, BigDecimal.valueOf(100)));
        return BigDecimalUtil.multiply(BigDecimal.ONE.subtract(existingValue.multiply(offsetValue)), new BigDecimal(-100));
    }

    static boolean isValueBetween(double value, double lowerBound, double upperBound) {
        return lowerBound <= value && value <= upperBound;
    }

    static Map<AccomType, CPConfigOffsetAccomType> findOffsetsWithinRoomClass(Map<AccomType, CPConfigOffsetAccomType> offsets, AccomType baseAccomType) {
        return offsets.entrySet()
                .stream()
                .filter(entry -> baseAccomType.getAccomClass().getAccomTypes().contains(entry.getKey()))
                .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    public static AccomType getBaseRoomTypeForNonBaseRoomTypeWithinRoomClass(AccomType accomType, Map<AccomClass, AccomType> baseRoomTypes) {
        return baseRoomTypes.get(accomType.getAccomClass());
    }

    public static Map<DayOfWeek, CeilingFloor> calculateBaseValuesForPriceExcluded(CPConfigOffsetAccomType offset, PricingBaseAccomType ceilingFloor) {
        CeilingFloor mondayCeilingFloor = new CeilingFloor();
        mondayCeilingFloor.setFloor(offset.getMondayOffsetValueWithTax() != null ? offset.getMondayOffsetValueWithTax() : ceilingFloor.getMondayFloorRateWithTax());
        mondayCeilingFloor.setCeiling(offset.getMondayOffsetValueWithTax() != null ? offset.getMondayOffsetValueWithTax() : ceilingFloor.getMondayFloorRateWithTax());
        CeilingFloor tuesdayCeilingFloor = new CeilingFloor();
        tuesdayCeilingFloor.setFloor(offset.getTuesdayOffsetValueWithTax() != null ? offset.getTuesdayOffsetValueWithTax() : ceilingFloor.getTuesdayFloorRateWithTax());
        tuesdayCeilingFloor.setCeiling(offset.getTuesdayOffsetValueWithTax() != null ? offset.getTuesdayOffsetValueWithTax() : ceilingFloor.getTuesdayFloorRateWithTax());
        CeilingFloor wednesdayCeilingFloor = new CeilingFloor();
        wednesdayCeilingFloor.setFloor(offset.getWednesdayOffsetValueWithTax() != null ? offset.getWednesdayOffsetValueWithTax() : ceilingFloor.getWednesdayFloorRateWithTax());
        wednesdayCeilingFloor.setCeiling(offset.getWednesdayOffsetValueWithTax() != null ? offset.getWednesdayOffsetValueWithTax() : ceilingFloor.getWednesdayFloorRateWithTax());
        CeilingFloor thursdayCeilingFloor = new CeilingFloor();
        thursdayCeilingFloor.setFloor(offset.getThursdayOffsetValueWithTax() != null ? offset.getThursdayOffsetValueWithTax() : ceilingFloor.getThursdayFloorRateWithTax());
        thursdayCeilingFloor.setCeiling(offset.getThursdayOffsetValueWithTax() != null ? offset.getThursdayOffsetValueWithTax() : ceilingFloor.getThursdayFloorRateWithTax());
        CeilingFloor fridayCeilingFloor = new CeilingFloor();
        fridayCeilingFloor.setFloor(offset.getFridayOffsetValueWithTax() != null ? offset.getFridayOffsetValueWithTax() : ceilingFloor.getFridayFloorRateWithTax());
        fridayCeilingFloor.setCeiling(offset.getFridayOffsetValueWithTax() != null ? offset.getFridayOffsetValueWithTax() : ceilingFloor.getFridayFloorRateWithTax());
        CeilingFloor saturdayCeilingFloor = new CeilingFloor();
        saturdayCeilingFloor.setFloor(offset.getSaturdayOffsetValueWithTax() != null ? offset.getSaturdayOffsetValueWithTax() : ceilingFloor.getSaturdayFloorRateWithTax());
        saturdayCeilingFloor.setCeiling(offset.getSaturdayOffsetValueWithTax() != null ? offset.getSaturdayOffsetValueWithTax() : ceilingFloor.getSaturdayFloorRateWithTax());
        CeilingFloor sundayCeilingFloor = new CeilingFloor();
        sundayCeilingFloor.setFloor(offset.getSundayOffsetValueWithTax() != null ? offset.getSundayOffsetValueWithTax() : ceilingFloor.getSundayFloorRateWithTax());
        sundayCeilingFloor.setCeiling(offset.getSundayOffsetValueWithTax() != null ? offset.getSundayOffsetValueWithTax() : ceilingFloor.getSundayFloorRateWithTax());

        return Map.of(DayOfWeek.MONDAY, mondayCeilingFloor,
                DayOfWeek.TUESDAY, tuesdayCeilingFloor,
                DayOfWeek.WEDNESDAY, wednesdayCeilingFloor,
                DayOfWeek.THURSDAY, thursdayCeilingFloor,
                DayOfWeek.FRIDAY, fridayCeilingFloor,
                DayOfWeek.SATURDAY, saturdayCeilingFloor,
                DayOfWeek.SUNDAY, sundayCeilingFloor
        );
    }

    public static Map<DayOfWeek, CeilingFloor> calculateBaseValues(PricingBaseAccomType ceilingFloor) {
        CeilingFloor mondayCeilingFloor = new CeilingFloor();
        mondayCeilingFloor.setCeiling(ceilingFloor.getMondayCeilingRateWithTax());
        mondayCeilingFloor.setFloor(ceilingFloor.getMondayFloorRateWithTax());
        CeilingFloor tuesdayCeilingFloor = new CeilingFloor();
        tuesdayCeilingFloor.setCeiling(ceilingFloor.getTuesdayCeilingRateWithTax());
        tuesdayCeilingFloor.setFloor(ceilingFloor.getTuesdayFloorRateWithTax());
        CeilingFloor wednesdayCeilingFloor = new CeilingFloor();
        wednesdayCeilingFloor.setCeiling(ceilingFloor.getWednesdayCeilingRateWithTax());
        wednesdayCeilingFloor.setFloor(ceilingFloor.getWednesdayFloorRateWithTax());
        CeilingFloor thursdayCeilingFloor = new CeilingFloor();
        thursdayCeilingFloor.setCeiling(ceilingFloor.getThursdayCeilingRateWithTax());
        thursdayCeilingFloor.setFloor(ceilingFloor.getThursdayFloorRateWithTax());
        CeilingFloor fridayCeilingFloor = new CeilingFloor();
        fridayCeilingFloor.setCeiling(ceilingFloor.getFridayCeilingRateWithTax());
        fridayCeilingFloor.setFloor(ceilingFloor.getFridayFloorRateWithTax());
        CeilingFloor saturdayCeilingFloor = new CeilingFloor();
        saturdayCeilingFloor.setCeiling(ceilingFloor.getSaturdayCeilingRateWithTax());
        saturdayCeilingFloor.setFloor(ceilingFloor.getSaturdayFloorRateWithTax());
        CeilingFloor sundayCeilingFloor = new CeilingFloor();
        sundayCeilingFloor.setCeiling(ceilingFloor.getSundayCeilingRateWithTax());
        sundayCeilingFloor.setFloor(ceilingFloor.getSundayFloorRateWithTax());

        return Map.of(DayOfWeek.MONDAY, mondayCeilingFloor,
                DayOfWeek.TUESDAY, tuesdayCeilingFloor,
                DayOfWeek.WEDNESDAY, wednesdayCeilingFloor,
                DayOfWeek.THURSDAY, thursdayCeilingFloor,
                DayOfWeek.FRIDAY, fridayCeilingFloor,
                DayOfWeek.SATURDAY, saturdayCeilingFloor,
                DayOfWeek.SUNDAY, sundayCeilingFloor
        );
    }

    public static Map<DayOfWeek, CeilingFloor> applyOffsetsToAlreadyCalculated(Map<DayOfWeek, CeilingFloor> calculatedCeilingFloor, CPConfigOffsetAccomType offset) {
        CeilingFloor mondayCeilingFloor = new CeilingFloor();
        mondayCeilingFloor.setCeiling(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.MONDAY).getCeiling(), offset.getMondayOffsetValueWithTax(), offset.getOffsetMethod()));
        mondayCeilingFloor.setFloor(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.MONDAY).getFloor(), offset.getMondayOffsetValueWithTax(), offset.getOffsetMethod()));
        CeilingFloor tuesdayCeilingFloor = new CeilingFloor();
        tuesdayCeilingFloor.setCeiling(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.TUESDAY).getCeiling(), offset.getTuesdayOffsetValueWithTax(), offset.getOffsetMethod()));
        tuesdayCeilingFloor.setFloor(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.TUESDAY).getFloor(), offset.getTuesdayOffsetValueWithTax(), offset.getOffsetMethod()));
        CeilingFloor wednesdayCeilingFloor = new CeilingFloor();
        wednesdayCeilingFloor.setCeiling(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.WEDNESDAY).getCeiling(), offset.getWednesdayOffsetValueWithTax(), offset.getOffsetMethod()));
        wednesdayCeilingFloor.setFloor(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.WEDNESDAY).getFloor(), offset.getWednesdayOffsetValueWithTax(), offset.getOffsetMethod()));
        CeilingFloor thursdayCeilingFloor = new CeilingFloor();
        thursdayCeilingFloor.setCeiling(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.THURSDAY).getCeiling(), offset.getThursdayOffsetValueWithTax(), offset.getOffsetMethod()));
        thursdayCeilingFloor.setFloor(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.THURSDAY).getFloor(), offset.getThursdayOffsetValueWithTax(), offset.getOffsetMethod()));
        CeilingFloor fridayCeilingFloor = new CeilingFloor();
        fridayCeilingFloor.setCeiling(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.FRIDAY).getCeiling(), offset.getFridayOffsetValueWithTax(), offset.getOffsetMethod()));
        fridayCeilingFloor.setFloor(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.FRIDAY).getFloor(), offset.getFridayOffsetValueWithTax(), offset.getOffsetMethod()));
        CeilingFloor saturdayCeilingFloor = new CeilingFloor();
        saturdayCeilingFloor.setCeiling(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.SATURDAY).getCeiling(), offset.getSaturdayOffsetValueWithTax(), offset.getOffsetMethod()));
        saturdayCeilingFloor.setFloor(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.SATURDAY).getFloor(), offset.getSaturdayOffsetValueWithTax(), offset.getOffsetMethod()));
        CeilingFloor sundayCeilingFloor = new CeilingFloor();
        sundayCeilingFloor.setCeiling(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.SUNDAY).getCeiling(), offset.getSundayOffsetValueWithTax(), offset.getOffsetMethod()));
        sundayCeilingFloor.setFloor(applyOffsetWithPossibleNullValue(calculatedCeilingFloor.get(DayOfWeek.SUNDAY).getFloor(), offset.getSundayOffsetValueWithTax(), offset.getOffsetMethod()));

        return Map.of(DayOfWeek.MONDAY, mondayCeilingFloor,
                DayOfWeek.TUESDAY, tuesdayCeilingFloor,
                DayOfWeek.WEDNESDAY, wednesdayCeilingFloor,
                DayOfWeek.THURSDAY, thursdayCeilingFloor,
                DayOfWeek.FRIDAY, fridayCeilingFloor,
                DayOfWeek.SATURDAY, saturdayCeilingFloor,
                DayOfWeek.SUNDAY, sundayCeilingFloor
        );
    }

    static BigDecimal applyOffsetWithPossibleNullValue(BigDecimal baseNumber, BigDecimal offsetValue, OffsetMethod offsetMethod) {
        if (isNonNullAndNonZero(baseNumber) && Objects.nonNull(offsetValue) && Objects.nonNull(offsetMethod)) {
            return applyOffset(baseNumber, offsetValue, offsetMethod);
        }
        return baseNumber;
    }

    private static boolean isNonNullAndNonZero(BigDecimal rate) {
        return Objects.nonNull(rate) && !Objects.equals(BigDecimal.ZERO, rate);
    }

    static BigDecimal applyOffset(BigDecimal baseNumber, BigDecimal offsetValue, OffsetMethod offsetMethod) {
        if (OffsetMethod.PERCENTAGE == offsetMethod) {
            if (baseNumber != null) {
                offsetValue = baseNumber.multiply(offsetValue.divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP)).setScale(2, RoundingMode.HALF_UP);
            }
        } else if (OffsetMethod.FIXED_PRICE == offsetMethod) {
            return offsetValue;
        }

        return offsetValue != null ? (baseNumber != null ? (baseNumber.add(offsetValue)) : offsetValue) : baseNumber;
    }

    static boolean isSpecificOverrideBreakingHierarchy(ProductHierarchyBucket bucket, ProductHierarchyContext context,
                                                       BigDecimal minimumDifference, Map<AccomType, CPDecisionBAROutput> selectedSpecificOverridesByAccomType,
                                                       Map<AccomType, CPDecisionBAROutput> relatedSpecificOverridesByAccomType) {
        for (AccomType accomType : context.getSharedAccomTypes()) {
            if (bucket.isSpecificOverridePresent()) {
                BigDecimal selected = getOverrideOrFinalBar(selectedSpecificOverridesByAccomType, accomType, bucket);
                BigDecimal related = getOverrideOrFinalBar(relatedSpecificOverridesByAccomType, accomType, bucket);
                if (BigDecimalUtil.add(selected, minimumDifference).compareTo(related) > 0) {
                    return true;
                }
            }
        }
        return false;
    }

    static boolean validateCalculatedValues(BigDecimal selectedCeiling, BigDecimal relatedCeiling,
                                            BigDecimal selectedFloor, BigDecimal relatedFloor,
                                            BigDecimal minDifference,
                                            boolean isPriceExcluded, Set<String> warnings) {
        double overlap = relatedCeiling.doubleValue() - selectedFloor.doubleValue();
        if (isPriceExcluded && selectedFloor.compareTo(relatedFloor) > 0) {
            warnings.add("invalid.hierarchy.excluded.value");
        }
        if (!isPriceExcluded && selectedCeiling.compareTo(relatedCeiling) > 0) {
            warnings.add("invalid.hierarchy.ceiling.value");
        }
        if (!isPriceExcluded && selectedFloor.compareTo(relatedFloor) > 0) {
            warnings.add("invalid.hierarchy.floor.value");
        }
        if (!isPriceExcluded && overlap < minDifference.doubleValue()) {
            warnings.add("invalid.hierarchy.mindiff.value");
        }
        if (!isPriceExcluded &&
                isOverlapNotExist(selectedFloor.doubleValue(), selectedCeiling.doubleValue(), relatedFloor.doubleValue(), relatedCeiling.doubleValue())) {
            warnings.add("invalid.hierarchy.overlap.value");
        }
        if (!isPriceExcluded && overlap < 0 || CollectionUtils.isNotEmpty(warnings)) {
            return false;
        }
        return true;
    }

    public static boolean areCalculatedValuesInValid(
            Map<DayOfWeek, CeilingFloor> calculatedValuesOfSelectedProduct,
            Map<DayOfWeek, CeilingFloor> calculatedValuesOfRelatedProduct,
            BigDecimal minimumDifference,
            boolean isPriceExcluded,
            Set<String> validationMessages) {

        for (DayOfWeek dayOfWeek : DayOfWeek.values()) {
            BigDecimal selectedCeiling = calculatedValuesOfSelectedProduct.get(dayOfWeek).getCeiling();
            BigDecimal relatedCeiling = calculatedValuesOfRelatedProduct.get(dayOfWeek).getCeiling();
            BigDecimal selectedFloor = calculatedValuesOfSelectedProduct.get(dayOfWeek).getFloor();
            BigDecimal relatedFloor = calculatedValuesOfRelatedProduct.get(dayOfWeek).getFloor();

            if (!isAnyInputNull(selectedCeiling, selectedFloor, relatedCeiling, relatedFloor)) {
                boolean isValid = validateCalculatedValues(selectedCeiling, relatedCeiling, selectedFloor, relatedFloor, minimumDifference, isPriceExcluded, validationMessages);
                if (!isValid) {
                    return true;
                }
            }
        }
        return false;
    }

    public static List<CPConfigOffsetAccomType> getCpConfigOffsetForProduct(
            Map<Integer, List<CPConfigOffsetAccomType>> offsetsByProduct, Product product) {
        if (MapUtils.isNotEmpty(offsetsByProduct) && com.ideas.tetris.pacman.util.CollectionUtils.isNotEmpty(offsetsByProduct.get(product.getId()))) {
            return offsetsByProduct.get(product.getId());
        }
        return Collections.emptyList();
    }

    public static Map<DayOfWeek, CeilingFloor> resolveOffsetValuesForBaseOccupancyType(AccomType accomType, PricingBaseAccomType ceilingFloor,
                                                                                       Map<AccomType, Map<DayOfWeek, CeilingFloor>> cfHolder,
                                                                                       Map<AccomType, CPConfigOffsetAccomType> offsetsByAccomType,
                                                                                       Map<AccomClass, AccomType> baseRoomTypes, boolean isPriceExcluded) {
        if (isPriceExcluded) {
            return calculateBaseValuesForPriceExcluded(offsetsByAccomType.getOrDefault(accomType, new CPConfigOffsetAccomType()), ceilingFloor);
        }
        Map<DayOfWeek, CeilingFloor> calculatedWithOffsetsCeilingFloorValues =
                cfHolder.get(accomType);
        if (Objects.nonNull(calculatedWithOffsetsCeilingFloorValues)) {
            return calculatedWithOffsetsCeilingFloorValues;
        }
        AccomType baseAccomType = getBaseRoomTypeForNonBaseRoomTypeWithinRoomClass(accomType, baseRoomTypes);
        cfHolder.putIfAbsent(baseAccomType, calculateBaseValues(ceilingFloor));
        Map<DayOfWeek, CeilingFloor> ceilingFloorForBaseAccomType = cfHolder.get(baseAccomType);
        if (Objects.equals(baseAccomType, accomType)) {
            return ceilingFloorForBaseAccomType;
        }
        cfHolder.putIfAbsent(accomType, applyOffsetsToAlreadyCalculated(ceilingFloorForBaseAccomType, offsetsByAccomType.getOrDefault(accomType, new CPConfigOffsetAccomType())));
        return cfHolder.get(accomType);
    }

    public static boolean validateParticularAccomType(PricingBaseAccomType selectedCF, PricingBaseAccomType relatedCF,
                                                      Map<AccomType, CPConfigOffsetAccomType> selectedOffsetsByAccomType,
                                                      Map<AccomType, CPConfigOffsetAccomType> relatedOffsetsByAccomType,
                                                      Map<AccomType, Map<DayOfWeek, CeilingFloor>> selectedCFHolder,
                                                      Map<AccomType, Map<DayOfWeek, CeilingFloor>> relatedCFHolder,
                                                      AccomType accomType, BigDecimal minimumDifference, Map<AccomClass, AccomType> baseRoomTypes,
                                                      boolean isPriceExcluded, Set<String> warningMessages) {
        Map<DayOfWeek, CeilingFloor> calculatedValuesOfSelectedProduct =
                resolveOffsetValuesForBaseOccupancyType(accomType, selectedCF, selectedCFHolder, selectedOffsetsByAccomType, baseRoomTypes, isPriceExcluded);
        Map<DayOfWeek, CeilingFloor> calculatedValuesOfRelatedProduct =
                resolveOffsetValuesForBaseOccupancyType(accomType, relatedCF, relatedCFHolder, relatedOffsetsByAccomType, baseRoomTypes, isPriceExcluded);
        return areCalculatedValuesInValid(calculatedValuesOfSelectedProduct, calculatedValuesOfRelatedProduct, minimumDifference, isPriceExcluded, warningMessages);
    }
}
