package com.ideas.tetris.pacman.services.decisiondelivery;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.decisiondelivery.dto.BarByLOSDecision;
import com.ideas.tetris.pacman.services.decisiondelivery.entity.BarByLOSDecisionEntity;
import com.ideas.tetris.pacman.services.hospitalityrooms.service.HospitalityRoomsService;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.rest.annotation.DateFormat;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class BARDecisionService {
    public static final String FULL_BAR_LOS_BY_RT = "dbo.Decision_Bar_Output";
    public static final String NON_PACE_FOR_DIFFERENTIAL_QUERY = "NON_PACE_FOR_DIFFERENTIAL_QUERY";
    public static final String BAR_LOS_MASTER_ROOM_CLASS_DIFFERENTIAL_UPLOAD_QUERY = "BAR_LOS_MASTER_ROOM_CLASS_DIFFERENTIAL_UPLOAD_QUERY";
    public static final String BAR_LOS_MASTER_ROOM_CLASS_DIFFERENTIAL_UPLOAD_USING_DECISIONID_QUERY = "BAR_LOS_MASTER_ROOM_CLASS_DIFFERENTIAL_UPLOAD_USING_DECISIONID_QUERY";
    public static final String RATE_OF_DAY_DECISIONS_MASTER_ROOM_CLASS_FULL_QUERY_NAME = "PRICING_DECISIONS_MASTER_ROOM_CLASS_FULL";
    private static final String DECISION_START_DATE = "decisionStartDate";
    private static final String DECISION_END_DATE = "decisionEndDate";
    private static final String LAST_UPLOADED_DATE = "lastUploadedDate";
    private static final String TABLE_NAME_KEY = "tableName";
    private static final String PACE_TABLE_NAME = "PACE_Bar_Output_Upload";
    private static final String NON_PACE_TABLE_NAME = "Decision_Bar_Output";
    private static final String PACE_TEMP_TABLE_NAME = "#DifferentialBarLosAtMasterClassWithAllLos";
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService crudService;

    @Autowired
	public PacmanConfigParamsService configParamsService;

    @Autowired
	private HospitalityRoomsService hospitalityRoomsService;

    public void setHospitalityRoomService(HospitalityRoomsService hospitalityRoomsService) {
        this.hospitalityRoomsService = hospitalityRoomsService;
    }

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    private static Logger LOGGER = Logger.getLogger(BARDecisionService.class.getName());

    public static final String RATE_OF_DAY_DECISIONS_MASTER_ROOM_CLASS_FULL_QUERY = " select ruq.Rate_Code_Name, DBO.Accom_Class_ID, DBO.Arrival_DT, DBO.LOS, DBO.Decision_ID, DBO.Rate_Unqualified_ID " +
            " from tableName as DBO " +
            " INNER JOIN Accom_Class AC ON AC.Accom_Class_ID = DBO.Accom_Class_ID " +
            " INNER JOIN Rate_Unqualified AS ruq ON DBO.Rate_Unqualified_ID = ruq.Rate_Unqualified_ID " +
            " WHERE Arrival_DT BETWEEN :decisionStartDate AND :decisionEndDate " +
            " AND AC.Master_Class = 1 " +
            " AND AC.Status_ID = (select Status_ID from dbo.Status where Status_Name='Active') " +
            " AND ruq.Status_ID = (select Status_ID from dbo.Status where Status_Name='Active') " +
            " AND DBO.LOS = -1; ";

    public static final String BAR_LOS_MASTER_ROOM_CLASS_FULL_QUERY = " SELECT     Rate_Code_Name, Accom_Class_ID, Arrival_DT, LOS, MIN(Rate_value) AS Rate_value " +
            " FROM         (SELECT     rq.Rate_Code_Name, AC.Accom_Class_ID, D.Arrival_DT, D.LOS, AC.accom_type_id, CASE (DATEPART(WEEKDAY, D .Arrival_DT)) WHEN 1 THEN (Sunday) " +
            " WHEN 2 THEN (Monday) WHEN 3 THEN (Tuesday) WHEN 4 THEN (Wednesday) WHEN 5 THEN (Thursday) WHEN 6 THEN (Friday) " +
            " WHEN 7 THEN (Saturday) END AS Rate_value " +
            " FROM          (SELECT     Accom_Class_ID, Arrival_DT, Rate_Unqualified_ID, LOS " +
            " FROM          Decision_Bar_Output " +
            " WHERE      (Arrival_DT BETWEEN :decisionStartDate AND :decisionEndDate )) AS D INNER JOIN  " +
            " (SELECT     ACT.Accom_Class_ID, AT.Accom_Type_ID " +
            " FROM          (SELECT     Accom_Class_ID " +
            " FROM          Accom_Class " +
            " where master_class = 1 and  Status_ID = (select Status_ID from dbo.Status where Status_Name='Active')) AS ACT INNER JOIN " +
            " Accom_Type AS AT ON ACT.Accom_Class_ID = AT.Accom_Class_ID) AS AC ON AC.Accom_Class_ID = D.Accom_Class_ID INNER JOIN " +
            " Rate_Unqualified AS rq ON D.Rate_Unqualified_ID = rq.Rate_Unqualified_ID INNER JOIN " +
            " Rate_Unqualified_Details AS rqd ON D.Rate_Unqualified_ID = rqd.Rate_Unqualified_ID AND AC.Accom_Type_ID = rqd.Accom_Type_ID " +
            " WHERE      (D.Arrival_DT BETWEEN rqd.Start_Date_DT AND rqd.End_Date_DT)) AS baserate " +
            " GROUP BY Rate_Code_Name, Accom_Class_ID, Arrival_DT, LOS order by Arrival_DT, LOS; ";

    public static final String NON_PACE_FOR_DIFFERENTIAL_UPLOAD = "SELECT PBO.Accom_Class_ID,PBO.Arrival_DT,PBO.LOS,PBO.Rate_Unqualified_ID, PBO.Decision_id  " +
            "INTO #NonPaceForDifferntial " +
            "FROM   ( " +
            "select Arrival_DT, Accom_Class_ID, D.Decision_id, LOS, Rate_Unqualified_ID " +
            ", row_number() over (partition by Arrival_DT, Accom_Class_ID, LOS ORDER BY D.Decision_id DESC) as Rownum " +
            "FROM PACE_Bar_Output_Upload PBOU " +
            "INNER JOIN Decision D on PBOU.Decision_ID=D.Decision_ID and  Arrival_DT between :decisionStartDate AND :decisionEndDate " +
            ") as PBO " +
            "WHERE Rownum = 1; ";

    public static final String BAR_LOS_MASTER_ROOM_CLASS_DIFFERENTIAL_UPLOAD = "SELECT NonPace.Arrival_DT,NonPace.Accom_Class_ID,NonPace.Rate_Unqualified_ID, NonPace.Decision_id, NonPace.LOS  " +
            "into #DifferentialBarLosAtMasterClass " +
            "FROM   ( " +
            "SELECT Accom_Class_ID,Arrival_DT,LOS,Rate_Unqualified_ID, Decision_id   " +
            "FROM #NonPaceForDifferntial ) as NonPace " +
            "LEFT JOIN  (  " +
            "SELECT PBO.Accom_Class_ID,PBO.Arrival_DT,PBO.LOS,PBO.Rate_Unqualified_ID, PBO.Decision_id   " +
            "FROM   ( " +
            "select Arrival_DT, Accom_Class_ID, D.Decision_id, LOS, Rate_Unqualified_ID  " +
            ", row_number() over (partition by Arrival_DT, Accom_Class_ID, LOS ORDER BY D.Decision_id DESC) as Rownum " +
            "FROM PACE_Bar_Output_Upload PBOU " +
            "INNER JOIN Decision D on PBOU.Decision_ID=D.Decision_ID   and  Arrival_DT between :decisionStartDate AND :decisionEndDate   and D.End_DTTM <= :lastUploadedDate  " +
            ") as PBO " +
            "WHERE Rownum = 1 " +
            ") as Pace  on NonPace.Accom_Class_ID=Pace.Accom_Class_ID  and NonPace.Arrival_DT=pace.Arrival_DT  and NonPace.LOS = Pace.LOS  " +
            "WHERE NonPace.Rate_Unqualified_ID <> Pace.Rate_Unqualified_ID OR pace.Accom_Class_ID is null; ";

    public static final String BAR_LOS_MASTER_ROOM_CLASS_DIFFERENTIAL_UPLOAD_USING_DECISIONID = " select NonPace.Arrival_DT,NonPace.Accom_Class_ID,NonPace.Rate_Unqualified_ID, Decision_id, NonPace.LOS into #DifferentialBarLosAtMasterClass from  " +
            " ( select Accom_Class_ID,Arrival_DT,LOS,Rate_Unqualified_ID, Decision_id" +
            "   from #NonPaceForDifferntial" +
            " ) as NonPace Left join " +
            " ( " +
            " select pace1.Accom_Class_ID,pace1.Arrival_DT,pace1.LOS,pace1.Rate_Unqualified_ID from " +
            " (select Arrival_DT,Accom_Class_ID,MAX(D.Decision_id)as Decision_id ,Los " +
            " from tableName PBO inner join Decision D on PBO.Decision_ID=D.Decision_ID " +
            " and  Arrival_DT between :decisionStartDate AND :decisionEndDate  " +
            " and D.Decision_ID <= (select MAX(Decision_id) from Decision_Upload_Date_To_External_System where  " +
            " External_system_name= :externalSystem and decision_name= :decisionName and last_upload_dttm is not null) " +
            " group by Arrival_DT,Accom_Class_ID,LOS) as pacegroup inner join " +
            " (select Arrival_DT,Accom_Class_ID,Los,Rate_Unqualified_ID,Decision_ID from  tableName) as Pace1 " +
            " on pacegroup.Accom_Class_ID=pace1.Accom_Class_ID " +
            " and pacegroup.Arrival_DT=pace1.Arrival_DT " +
            " and pacegroup.LOS=pace1.LOS " +
            " and pacegroup.Decision_id=pace1.Decision_ID " +
            " ) as Pace " +
            " on NonPace.Accom_Class_ID=Pace.Accom_Class_ID " +
            " and NonPace.Arrival_DT=pace.Arrival_DT " +
            " and NonPace.LOS = Pace.LOS " +
            " where NonPace.Rate_Unqualified_ID <> Pace.Rate_Unqualified_ID OR pace.Accom_Class_ID is null; ";

    public static final String BAR_LOS_GET_ALL_LOS_FOR_DIFFERENTIAL = "	 select dbo.Arrival_DT,dbo.Accom_Class_ID,dbo.Rate_Unqualified_ID, dbo.Decision_id, dbo.LOS  " +
            " into #DifferentialBarLosAtMasterClassWithAllLos  from #NonPaceForDifferntial dbo inner join ( " +
            "		 select distinct Arrival_DT from #DifferentialBarLosAtMasterClass where Accom_Class_id= " +
            "		 (SELECT     Accom_Class_ID   " +
            "		 FROM          Accom_Class   " +
            "		 where master_class = 1  " +
            "		 AND Status_ID = (select Status_ID from dbo.Status where Status_Name='Active'))" +
            ") as tempdbo on  dbo.Arrival_DT=tempdbo.Arrival_DT; ";

    public static final String RATE_OF_DAY_GET_ALL_LOS_FOR_DIFFERENTIAL = " select dbo.Arrival_DT,dbo.Accom_Class_ID,dbo.Rate_Unqualified_ID, dbo.Decision_id, dbo.LOS  " +
            " into #DifferentialBarLosAtMasterClassWithAllLos  " +
            " from #NonPaceForDifferntial dbo " +
            " inner join " +
            " ( " +
            "   select distinct Arrival_DT " +
            "   from #DifferentialBarLosAtMasterClass " +
            "   where Accom_Class_id = ( SELECT Accom_Class_ID FROM Accom_Class where Master_Class = 1 AND Status_ID = (select Status_ID from dbo.Status where Status_Name='Active')) " +
            " ) as tempdbo on  dbo.Arrival_DT=tempdbo.Arrival_DT; ";

    public static final String BAR_LOS_BY_RT_GET_ALL_LOS_FOR_DIFFERENTIAL = " SELECT dbo.Arrival_DT,dbo.Accom_Class_ID,dbo.Rate_Unqualified_ID,dbo.Decision_ID,dbo.LOS " +
            "INTO #DifferentialBarLosByRTWithAllLos FROM #NonPaceForDifferntial dbo " +
            "INNER JOIN " +
            "(SELECT DISTINCT Arrival_DT, Accom_Class_ID FROM #DifferentialBarLosAtMasterClass WHERE Accom_Class_ID IN " +
            "(SELECT Accom_Class_ID FROM Accom_Class WHERE Status_ID = " +
            "(SELECT Status_ID FROM dbo.Status WHERE Status_Name = 'Active'))) AS tempdbo ON dbo.Arrival_DT = tempdbo.Arrival_DT and dbo.Accom_Class_ID = tempdbo.Accom_Class_ID; ";


    public static final String BAR_LOS_MASTER_ROOM_CLASS_QUERY = " SELECT     Rate_Code_Name, Accom_Class_ID, Arrival_DT, LOS, MIN(Rate_value) AS Rate_value " +
            " FROM         (SELECT     rq.Rate_Code_Name, AC.Accom_Class_ID, D.Arrival_DT, D.LOS, AC.accom_type_id, CASE (DATEPART(WEEKDAY, D .Arrival_DT)) WHEN 1 THEN (Sunday) " +
            " WHEN 2 THEN (Monday) WHEN 3 THEN (Tuesday) WHEN 4 THEN (Wednesday) WHEN 5 THEN (Thursday) WHEN 6 THEN (Friday)  " +
            " WHEN 7 THEN (Saturday) END AS Rate_value  " +
            " FROM          (SELECT     Accom_Class_ID, Arrival_DT, Rate_Unqualified_ID, LOS " +
            " FROM         #DifferentialBarLosAtMasterClassWithAllLos  " +
            " ) AS D INNER JOIN   " +
            " (SELECT     ACT.Accom_Class_ID, AT.Accom_Type_ID " +
            " FROM          (SELECT     Accom_Class_ID  " +
            " FROM          Accom_Class  " +
            " where master_class = 1 and  Status_ID = (select Status_ID from dbo.Status where Status_Name='Active')) AS ACT INNER JOIN " +
            " Accom_Type AS AT ON ACT.Accom_Class_ID = AT.Accom_Class_ID) AS AC ON AC.Accom_Class_ID = D.Accom_Class_ID INNER JOIN " +
            " Rate_Unqualified AS rq ON D.Rate_Unqualified_ID = rq.Rate_Unqualified_ID INNER JOIN  " +
            " Rate_Unqualified_Details AS rqd ON D.Rate_Unqualified_ID = rqd.Rate_Unqualified_ID AND AC.Accom_Type_ID = rqd.Accom_Type_ID " +
            " WHERE      (D.Arrival_DT BETWEEN rqd.Start_Date_DT AND rqd.End_Date_DT)) AS baserate  " +
            " GROUP BY Rate_Code_Name, Accom_Class_ID, Arrival_DT, LOS order by Arrival_DT, LOS ";

    protected static final String QUERY_FOR_GETTING_LAST_UPLOAD_DECISION_DATE_WITH_EXTERNAL_SYSTEM = "select max(Last_Upload_DTTM) from dbo.Decision_Upload_Date_To_External_System "
            + "where Decision_Name= :decisionName and External_System_Name= :externalSystem";


    @SuppressWarnings("unchecked")
    public List<BarByLOSDecision> getBARByLOSRoomTypeDecisions(Date startDate,
                                                               Date endDate, Date lastUploadedDate) {
        List<BarByLOSDecision> barByLOSRTDecisionsList = new ArrayList<>();
        List<Object[]> resultList = getFullOrDifferentialBarLOSAtRoomType(startDate, endDate, lastUploadedDate);
        for (Object[] singleDecision : resultList) {
            BarByLOSDecision barLOSDecision = new BarByLOSDecision();
            barLOSDecision.setRoomType((String) singleDecision[0]);
            barLOSDecision.setRateDate((Date) singleDecision[1]);
            barLOSDecision.setRateCode((String) singleDecision[2]);
            barLOSDecision.setLos((Integer) singleDecision[3]);
            barByLOSRTDecisionsList.add(barLOSDecision);
        }

        return barByLOSRTDecisionsList;
    }

    // used by NGI


    public BarByLOSDecisionEntity createBarByLos(Integer accomClassId,
                                                 Integer decisionId,
                                                 @DateFormat Date arrivalDate,
                                                 String unqualifiedRateName,
                                                 Integer los,
                                                 Integer month,
                                                 Integer year,
                                                 Integer decisionReasonTypeId,
                                                 String override) {
        BarByLOSDecisionEntity barByLOSDecision = new BarByLOSDecisionEntity();
        barByLOSDecision.setPropertyId(PacmanThreadLocalContextHolder.getWorkContext().getPropertyId());
        barByLOSDecision.setAccomClassId(accomClassId);
        barByLOSDecision.setArrivalDate(arrivalDate);
        barByLOSDecision.setDecisionId(decisionId);
        barByLOSDecision.setLos(los);
        barByLOSDecision.setMonth(month);
        barByLOSDecision.setYear(year);
        barByLOSDecision.setRateUnqualifiedId(getRateUnqualified(unqualifiedRateName).getId());
        barByLOSDecision.setDecisionReasonTypeId(decisionReasonTypeId);
        barByLOSDecision.setOverride(override);
        barByLOSDecision.setCreateDate(new Date());
        crudService.save(barByLOSDecision);
        return barByLOSDecision;
    }

    // used by NGI


    public List<BarByLOSDecisionEntity> getAllBarByLOS() {
        return crudService.findAll(BarByLOSDecisionEntity.class);
    }

    // used by NGI

    @ForTesting


    public boolean removeBarByLOSDecision(Integer id) {
        return crudService.delete(BarByLOSDecisionEntity.class, id);
    }


    @ForTesting

    public int removeAllBarByLOSDecision() {
        return crudService.deleteAll(BarByLOSDecisionEntity.class);
    }

    private RateUnqualified getRateUnqualified(String name) {
        return (RateUnqualified) crudService.findByNamedQuerySingleResult(RateUnqualified.GET_RATE_UNQUALIFIED_BY_NAMES,
                QueryParameter.with("names", Collections.singletonList(name)).parameters());
    }

    public List<BarByLOSDecision> getBARByLOSRoomClassDecisions(Date startDate, Date endDate, Date lastUploadedDate) {
        List<BarByLOSDecision> barByLOSRCDecisionsList = new ArrayList<>();
        List<Object[]> resultList = getFullOrDifferentialBarLOSAtRoomClass(startDate, endDate, lastUploadedDate);
        for (Object[] singleDecision : resultList) {
            BarByLOSDecision barLOSDecision = new BarByLOSDecision();
            barLOSDecision.setRateDate((Date) singleDecision[2]);
            barLOSDecision.setRateCode((String) singleDecision[0]);
            barLOSDecision.setLos((Integer) singleDecision[3]);
            barLOSDecision.setRateValue(new BigDecimal(singleDecision[4].toString()));
            barByLOSRCDecisionsList.add(barLOSDecision);
        }
        return barByLOSRCDecisionsList;
    }

    public List<BarByLOSDecision> getRateOfDayBARDecisionsForMasterClass(Date startDate, Date endDate, Date lastUploadedDate, String externalSystem) {
        List<BarByLOSDecision> barDecisionsMasterClassList;
        List<Object[]> resultList = getFullOrDifferentialRateOfDayBARDecisionsForMasterClass(startDate, endDate, lastUploadedDate, externalSystem);
        barDecisionsMasterClassList = resultList.stream().map(result -> new BarByLOSDecision((String) result[0], (Date) result[2], (Integer) result[3])).collect(Collectors.toList());
        return barDecisionsMasterClassList;
    }

    @SuppressWarnings("unchecked")
    private List<Object[]> getFullOrDifferentialRateOfDayBARDecisionsForMasterClass(Date startDate, Date endDate, Date lastUploadedDate, String externalSystem) {
        List<Object[]> resultList;
        if (null == lastUploadedDate) {
            resultList = crudService.findByNativeQuery(prepareQueryFor(RATE_OF_DAY_DECISIONS_MASTER_ROOM_CLASS_FULL_QUERY_NAME, NON_PACE_TABLE_NAME), QueryParameter.with(DECISION_START_DATE, startDate)
                    .and(DECISION_END_DATE, endDate).parameters());
        } else {
            resultList = getDifferentialRateOfDayBARDecisionsForMasterClass(startDate, endDate, externalSystem);

        }
        return resultList;
    }

    @SuppressWarnings("unchecked")
    private List<Object[]> getDifferentialRateOfDayBARDecisionsForMasterClass(Date startDate, Date endDate, String externalSystem) {
        return crudService.findByNativeQuery(getDifferentialQueryForRateOfDayForMasterClass(),
                QueryParameter.with(DECISION_START_DATE, startDate)
                        .and(DECISION_END_DATE, endDate)
                        .and("externalSystem", externalSystem)
                        .and("decisionName", Constants.BAR_DECISION_VALUE_RATEOFDAY).parameters());
    }

    public String getDifferentialQueryForRateOfDayForMasterClass() {
        return prepareQueryFor(NON_PACE_FOR_DIFFERENTIAL_QUERY, PACE_TABLE_NAME) +
                prepareQueryFor(BAR_LOS_MASTER_ROOM_CLASS_DIFFERENTIAL_UPLOAD_USING_DECISIONID_QUERY, PACE_TABLE_NAME) +
                RATE_OF_DAY_GET_ALL_LOS_FOR_DIFFERENTIAL +
                prepareQueryFor(RATE_OF_DAY_DECISIONS_MASTER_ROOM_CLASS_FULL_QUERY_NAME, PACE_TEMP_TABLE_NAME);
    }

    @SuppressWarnings("unchecked")
    public Date getLastUpdateDateToExternalSystem(String decisionName, String partner, boolean isDifferentialDecisionUpload) {
        Date singleResult = null;
        try {
            if (isDifferentialDecisionUpload) {

                List<Object> resultList = crudService.findByNativeQuery(QUERY_FOR_GETTING_LAST_UPLOAD_DECISION_DATE_WITH_EXTERNAL_SYSTEM,
                        QueryParameter.with("decisionName", decisionName).and("externalSystem", partner).parameters());
                if (!resultList.isEmpty()) {
                    singleResult = formatDate(resultList);
                }
            }
        } catch (Exception e) {
            LOGGER.warn("ERROR occurred while getting last update date for decisionName: " + decisionName + " from system: " + partner, e);
        }
        return singleResult;
    }

    private Date formatDate(List<Object> resultList) {
        Date source = (Date) resultList.get(0);
        if (source != null) {
            return source;
        }
        return null;
    }

    @SuppressWarnings("unchecked")
    private List<Object[]> getFullOrDifferentialBarLOSAtRoomClass(Date startDate, Date endDate, Date lastUploadedDate) {
        List<Object[]> resultList;
        if (null == lastUploadedDate) {
            resultList = crudService.findByNativeQuery(BAR_LOS_MASTER_ROOM_CLASS_FULL_QUERY, QueryParameter.with(DECISION_START_DATE, startDate)
                    .and(DECISION_END_DATE, endDate).parameters());
        } else {
            resultList = getDifferentialBarLOSAtRoomClass(startDate, endDate, lastUploadedDate);

        }
        return resultList;
    }

    //need change here
    private List<Object[]> getDifferentialBarLOSAtRoomClass(Date startDate, Date endDate, Date lastUploadedDate) {
        return crudService.findByNativeQuery(getDifferentialQueryForBarByLOSAtRoomClass(),
                QueryParameter.with(DECISION_START_DATE, startDate)
                        .and(DECISION_END_DATE, endDate).and(LAST_UPLOADED_DATE, lastUploadedDate).parameters());
    }

    public String getDifferentialQueryForBarByLOSAtRoomClass() {
        return prepareQueryFor(NON_PACE_FOR_DIFFERENTIAL_QUERY, PACE_TABLE_NAME) +
                prepareQueryFor(BAR_LOS_MASTER_ROOM_CLASS_DIFFERENTIAL_UPLOAD_QUERY, PACE_TABLE_NAME) +
                BAR_LOS_GET_ALL_LOS_FOR_DIFFERENTIAL +
                BAR_LOS_MASTER_ROOM_CLASS_QUERY;
    }

    private String prepareQueryFor(String queryName, String tableToUse) {

        if (queryName.equalsIgnoreCase(NON_PACE_FOR_DIFFERENTIAL_QUERY)) {
            return NON_PACE_FOR_DIFFERENTIAL_UPLOAD.replaceAll(TABLE_NAME_KEY, tableToUse);
        } else if (queryName.equalsIgnoreCase(BAR_LOS_MASTER_ROOM_CLASS_DIFFERENTIAL_UPLOAD_QUERY)) {
            return BAR_LOS_MASTER_ROOM_CLASS_DIFFERENTIAL_UPLOAD.replaceAll(TABLE_NAME_KEY, tableToUse);
        } else if (queryName.equalsIgnoreCase(BAR_LOS_MASTER_ROOM_CLASS_DIFFERENTIAL_UPLOAD_USING_DECISIONID_QUERY)) {
            return BAR_LOS_MASTER_ROOM_CLASS_DIFFERENTIAL_UPLOAD_USING_DECISIONID.replaceAll(TABLE_NAME_KEY, tableToUse);
        } else if (queryName.equalsIgnoreCase(RATE_OF_DAY_DECISIONS_MASTER_ROOM_CLASS_FULL_QUERY_NAME)) {
            return RATE_OF_DAY_DECISIONS_MASTER_ROOM_CLASS_FULL_QUERY.replaceAll(TABLE_NAME_KEY, tableToUse);
        }
        return "";
    }

    private List<Object[]> getFullOrDifferentialBarLOSAtRoomType(Date startDate, Date endDate, Date lastUploadedDate) {
        List<Object[]> resultList;
        if (null == lastUploadedDate) {
            resultList = crudService.findByNativeQuery(getBarLOSByRTQuery(FULL_BAR_LOS_BY_RT), QueryParameter.with(DECISION_START_DATE, startDate).and(DECISION_END_DATE, endDate).parameters());
        } else {
            resultList = getDifferentialBarLOSAtRoomType(startDate, endDate, lastUploadedDate);
        }
        return resultList;
    }

    private List<Object[]> getDifferentialBarLOSAtRoomType(Date startDate, Date endDate, Date lastUploadedDate) {
        List<Object[]> resultList;
        resultList = crudService.findByNativeQuery(getDifferentialQueryForBarLOSByRoomType(), QueryParameter.with(DECISION_START_DATE, startDate)
                .and(DECISION_END_DATE, endDate).and(LAST_UPLOADED_DATE, lastUploadedDate).parameters());
        return resultList;
    }

    public String getDifferentialQueryForBarLOSByRoomType() {
        return prepareQueryFor(NON_PACE_FOR_DIFFERENTIAL_QUERY, PACE_TABLE_NAME) +
                prepareQueryFor(BAR_LOS_MASTER_ROOM_CLASS_DIFFERENTIAL_UPLOAD_QUERY, PACE_TABLE_NAME) +
                BAR_LOS_BY_RT_GET_ALL_LOS_FOR_DIFFERENTIAL +
                getBarLOSByRTQuery("#DifferentialBarLosByRTWithAllLos");
    }

    protected String getBarLOSByRTQuery(String tableName) {
        String zeroCapacityRTClauseToReplace = hospitalityRoomsService.getZeroCapacityRTClauseToReplace("accomType");
        String sql = "SELECT accomType.Accom_Type_Code ,barOutput.Arrival_DT ,rateUQ.Rate_Code_Name ,barOutput.LOS " +
                "FROM " + tableName + " as barOutput " +
                "join dbo.Rate_Unqualified as rateUQ " +
                "on barOutput.Rate_Unqualified_ID = rateUQ.Rate_Unqualified_ID and barOutput.Arrival_DT between :decisionStartDate and :decisionEndDate " +
                "join dbo.Accom_Type as accomType " +
                "on barOutput.Accom_Class_ID = accomType.Accom_Class_ID and barOutput.Arrival_DT between :decisionStartDate and :decisionEndDate " +
                "where ( accomType.Accom_Type_Capacity > 0 zeroCapacityRTClause ) " +
                " order by barOutput.Arrival_DT, accomType.Accom_Type_Code, barOutput.LOS";
        return sql.replace("zeroCapacityRTClause", zeroCapacityRTClauseToReplace);
    }

}
