package com.ideas.tetris.pacman.services.license.feature;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.roa.attribute.entity.PropertyAttributeOverride;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.license.entities.LicensePackage;
import com.ideas.tetris.platform.common.license.migration.actions.LicenseFeatureUpgradable;
import com.ideas.tetris.platform.common.license.util.LicenseFeatureConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.ideas.tetris.pacman.services.roa.attribute.entity.PropertyAttributeEnum.*;

@Component
public class OptimisationSettingFeature extends LicenseFeatureUpgradable {

    @Autowired
    private AbstractMultiPropertyCrudService multiPropertyCrudService;

    @Override
    public String getFeatureCode() {
        return LicenseFeatureConstants.OPTIMIZATION_SETTINGS;
    }

    @Override
    protected void cleanUp(int propertyId, LicensePackage currentLicensePackage, LicensePackage newLicensePackage, Map<String, Object> featuresInputMapToDowngrade) {
        List<String> propertyAttributes = Arrays.asList(PRICE_DROP_MIN_REV_GAIN.getAttributeName(),
                PRICE_DROP_MAX_VALUE.getAttributeName(),
                PRICE_DROP_MIN_DTA.getAttributeName(),
                CANCEL_REBOOK_PCT.getAttributeName(),
                OPT_DYNAMIC_PRICE.getAttributeName(),
                LRV_DROP_MIN_DTA.getAttributeName());
        List<PropertyAttributeOverride> overrides = multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, PropertyAttributeOverride.FIND_ACTIVE_BY_ATTRIBUTE_NAMES,
                QueryParameter.with("attributeNames", propertyAttributes).parameters());
        overrides.forEach(override -> override.setStatusId(Constants.INACTIVE_STATUS_ID));
        multiPropertyCrudService.save(propertyId, overrides);
    }
}
