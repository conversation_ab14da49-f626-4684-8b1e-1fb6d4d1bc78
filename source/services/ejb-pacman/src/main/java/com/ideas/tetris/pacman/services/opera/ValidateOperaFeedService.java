package com.ideas.tetris.pacman.services.opera;

import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

/**
 * Created by idnrbk on 5/4/2015.
 */
@Component
@Transactional
public class ValidateOperaFeedService {
    @Autowired
    PropertyService propertyService;
    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
    OperaUtilityService operaUtilityService;
    @TenantCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("tenantCrudServiceBean")
    CrudService crudService;

    @OperaGroupBlockFeedValidationService.Qualifier
	@Autowired
	@Qualifier("operaGroupBlockFeedValidationService")
    OperaFeedValidationService operaGroupBlockFeedValidationService;
    @OperaGroupMasterFeedValidationService.Qualifier
	@Autowired
	@Qualifier("operaGroupMasterFeedValidationService")
    OperaFeedValidationService operaGroupMasterFeedValidationService;
    @OperaMSRTSummaryFeedValidationService.Qualifier
	@Autowired
	@Qualifier("operaMSRTSummaryFeedValidationService")
    OperaFeedValidationService operaMSRTSummaryFeedValidationService;
    @OperaRTSummaryFeedValidationService.Qualifier
	@Autowired
	@Qualifier("operaRTSummaryFeedValidationService")
    OperaFeedValidationService operaRTSummaryFeedValidationService;
    @OperaTotalSummaryFeedValidationService.Qualifier
	@Autowired
	@Qualifier("operaTotalSummaryFeedValidationService")
    OperaFeedValidationService operaTotalSummaryFeedValidationService;
    @OperaTransactionFeedValidationService.Qualifier
	@Autowired
	@Qualifier("operaTransactionFeedValidationService")
    OperaFeedValidationService operaTransactionFeedValidationService;

    private static final Logger LOGGER = Logger.getLogger(ValidateOperaDataService.class.getName());


    public void validateOperaFeed(String correlationId) {
        long startTime = System.currentTimeMillis();
        LOGGER.info("Validating feed : " + correlationId);
        StringBuffer validationMessage = new StringBuffer();
        Map<String, Integer> metadataIDMap = operaUtilityService.getDataLoadMetadataIDMap(correlationId);
        validationMessage.append(performValidation(correlationId, operaGroupBlockFeedValidationService, metadataIDMap));
        validationMessage.append(performValidation(correlationId, operaGroupMasterFeedValidationService, metadataIDMap));
        validationMessage.append(performValidation(correlationId, operaMSRTSummaryFeedValidationService, metadataIDMap));
        validationMessage.append(performValidation(correlationId, operaRTSummaryFeedValidationService, metadataIDMap));
        validationMessage.append(performValidation(correlationId, operaTotalSummaryFeedValidationService, metadataIDMap));
        validationMessage.append(performValidation(correlationId, operaTransactionFeedValidationService, metadataIDMap));

        if (StringUtils.isNotBlank(validationMessage.toString())) {
            throw new TetrisException(new StringBuilder(
                    " Spring batch job processing failed because there was a feed validation error. \n").append(validationMessage)
                    .toString());
        }

        //check if stage is one way and above and turn OFF validation flag for next feed
        turnOffValidationWhenStageAboveOneWay();

        long endTime = System.currentTimeMillis();

        LOGGER.info("****Validate OperaFeed took " + (endTime - startTime) + " milliseconds");
        LOGGER.info("Completed validating feed : " + correlationId);
    }

    private String performValidation(String correlationId, OperaFeedValidationService operaFeedValidationService, Map<String, Integer> metadataIDMap) {
        List<Integer> dataLoadMedataIDs = new ArrayList<Integer>();
        for (String dataLoadType : operaFeedValidationService.getDataLoadTypes()) {
            dataLoadMedataIDs.add(metadataIDMap.get(dataLoadType));
        }
        return operaFeedValidationService.validateFeed(correlationId, dataLoadMedataIDs);
    }

    public void turnOffValidationWhenStageAboveOneWay() {
        Stage propertyStage = propertyService.getPropertyStage(PacmanWorkContextHelper.getPropertyId());
        if (null != propertyStage && propertyStage.getOrder() >= Stage.ONE_WAY.getOrder()
                && pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.VALIDATE_DATA_FEED.value(Constants.OPERA))) {

            String context = new StringBuilder(Constants.CONFIG_PARAMS_NODE_PREFIX)
                    .append('.')
                    .append(PacmanWorkContextHelper.getClientCode())
                    .append('.').append(PacmanWorkContextHelper.getPropertyCode())
                    .toString();
            pacmanConfigParamsService.updateParameterValue(context, IntegrationConfigParamName.VALIDATE_DATA_FEED.value(Constants.OPERA), Boolean.FALSE.toString());
            LOGGER.info("Turning off feed validation as property is now One Way.");
        }
    }


}
