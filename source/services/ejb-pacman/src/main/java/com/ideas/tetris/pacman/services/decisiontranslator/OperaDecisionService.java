package com.ideas.tetris.pacman.services.decisiontranslator;

import com.ideas.g3.integration.opera.dto.AccomOverbookingDecision;
import com.ideas.g3.integration.opera.dto.AccomTypeManualRestrictionDecisionForOpera;
import com.ideas.g3.integration.opera.dto.BarByLOSDecision;
import com.ideas.g3.integration.opera.dto.DailyBarDecision;
import com.ideas.g3.integration.opera.dto.HotelOverbookingDecision;
import com.ideas.g3.integration.opera.dto.LastRoomValueDecision;
import com.ideas.g3.integration.opera.dto.OperaFplosQualifiedDecision;
import com.ideas.g3.integration.opera.dto.OperaMinLOSDecision;
import com.ideas.g3.integration.opera.dto.PropertyManualRestrictionDecisionForOpera;
import com.ideas.g3.integration.opera.services.OperaDecisionServiceLocal;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.rateCodeVendorMapping.RateCodeVendorMapping;
import com.ideas.tetris.pacman.services.accommodation.rateCodeVendorMapping.RateCodeVendorMappingService;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dailybar.DailyBarDecisionService;
import com.ideas.tetris.pacman.services.dailybar.PacmanAgileRatesDecisionService;
import com.ideas.tetris.pacman.services.dailybar.entity.DailyBarDecisions;
import com.ideas.tetris.pacman.services.decisiondelivery.BARDecisionService;
import com.ideas.tetris.pacman.services.decisiondelivery.LastRoomValueDecisionService;
import com.ideas.tetris.pacman.services.decisiondelivery.PacmanOverbookingDecisionService;
import com.ideas.tetris.pacman.services.decisiondelivery.dto.PropertyOverbookingDecision;
import com.ideas.tetris.pacman.services.decisiondelivery.entity.DecisionUploadDateToExternalSystemRepository;
import com.ideas.tetris.pacman.services.demandoverride.entity.LastRoomValue;
import com.ideas.tetris.pacman.services.demandoverride.entity.LastRoomValueAccomType;
import com.ideas.tetris.pacman.services.fplos.FPLOSByHierarchyByRoomClassDecisionService;
import com.ideas.tetris.pacman.services.fplos.FPLOSByHierarchyDecisionService;
import com.ideas.tetris.pacman.services.fplos.FPLOSByRoomClassDecisionService;
import com.ideas.tetris.pacman.services.fplos.FPLOSByRoomTypeDecisionService;
import com.ideas.tetris.pacman.services.fplos.FPLOSQualifiedDecisionService;
import com.ideas.tetris.pacman.services.fplos.entity.FPLOSDecisions;
import com.ideas.tetris.pacman.services.lra.LRADecisionService;
import com.ideas.tetris.pacman.services.lra.dto.LRARestrictionType;
import com.ideas.tetris.pacman.services.lra.entity.DecisionLRAFPLOS;
import com.ideas.tetris.pacman.services.lra.entity.DecisionLRAMINLOS;
import com.ideas.tetris.pacman.services.manualrestrictions.upload.ManualRestrictionDecisionService;
import com.ideas.tetris.pacman.services.manualrestrictions.upload.decision.AccomTypeManualRestrictionDecision;
import com.ideas.tetris.pacman.services.manualrestrictions.upload.decision.PropertyManualRestrictionDecision;
import com.ideas.tetris.pacman.services.minlos.MinlosDecisionService;
import com.ideas.tetris.pacman.services.minlos.entity.MinlosDecisions;
import com.ideas.tetris.pacman.services.ngi.decision.decisiondelivery.DecisionType;
import com.ideas.tetris.pacman.services.ngi.dto.CurrencyExchange;
import com.ideas.tetris.pacman.services.ngi.dto.CurrencyExchangeResultsMapper;
import com.ideas.tetris.pacman.services.ngi.dto.ProfitAdjustment;
import com.ideas.tetris.pacman.services.overbooking.entity.DecisionOvrbkProperty;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.profitadjustment.service.ProfitAdjustmentService;
import com.ideas.tetris.pacman.services.profitadjustment.service.ProfitAdjustmentService;
import com.ideas.tetris.pacman.services.roomtypemapping.DailyBarVendorRoomTypeTranslator;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.externalsystem.ExternalSubSystem;
import com.ideas.tetris.platform.common.rest.mapper.RestClient;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.math3.util.Precision;
import org.apache.log4j.Logger;

import javax.ws.rs.FormParam;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.AGILE_RATES;
import static com.ideas.tetris.pacman.common.constants.Constants.DECISION_TYPE_DAILYBAR_FULL_REFRESH;
import static com.ideas.tetris.pacman.common.constants.Constants.DECISION_TYPE_LRA_FPLOS;
import static com.ideas.tetris.pacman.common.constants.Constants.DECISION_TYPE_LRA_MINLOS;
import static com.ideas.tetris.pacman.common.constants.Constants.LRA_CONTROL_FPLOS;
import static com.ideas.tetris.pacman.common.constants.Constants.LRA_CONTROL_FPLOS_BY_RATE_CATEGORY;
import static com.ideas.tetris.pacman.common.constants.Constants.LRA_CONTROL_FPLOS_BY_RATE_CODE;
import static com.ideas.tetris.pacman.common.constants.Constants.LRA_CONTROL_MINLOS;
import static com.ideas.tetris.pacman.common.constants.Constants.LRA_CONTROL_MINLOS_BY_RATE_CATEGORY;
import static com.ideas.tetris.pacman.common.constants.Constants.LRA_CONTROL_MINLOS_BY_RATE_CODE;
import static com.ideas.tetris.pacman.common.constants.Constants.TAX_ADJUSTMENT_VALUE;
import static com.ideas.tetris.platform.common.rest.mapper.RestEndpoints.CURRENCY_EXCHANGE;
import static java.util.Objects.isNull;
import static org.apache.commons.collections.CollectionUtils.isEmpty;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Slf4j
@Component
@Transactional
public class OperaDecisionService extends AbstractDecisionConfigurationsFetcher implements OperaDecisionServiceLocal {

    static final String QUERY_TO_GET_MAX_DECISION_ID_FOR_DECISION_TYPE = "select MAX(Decision_ID) from dbo.Decision where Decision_Type_ID in (:decisionTypeIDs)";
    private static final String LAST_UPDATE_DATE_FOR_DECISION_TYPE = "Last Update date for decision type ";
    private static final Logger LOGGER = Logger.getLogger(OperaDecisionService.class.getName());
    private static final String PROPERTY_ID = "propertyId";
    @Autowired
    DecisionUploadDateToExternalSystemRepository decisionUploadDateToExternalSystemRepository;
    @Autowired
	private AccommodationService accommodationService;
    @Autowired
	private PacmanOverbookingDecisionService pacmanOverbookingDecisionService;
    @Autowired
	private LastRoomValueDecisionService lastRoomValueDecisionService;
    @Autowired
	private MinlosDecisionService minimumLengthOfStayDecisionService;
    @Autowired
	private FPLOSQualifiedDecisionService fplosQualifiedDecisionService;
    @Autowired
	private DailyBarDecisionService dailyBarDecisionService;
    @Autowired
	private PacmanAgileRatesDecisionService pacmanAgileRatesDecisionService;
    @Autowired
	private FPLOSByHierarchyDecisionService fplosByHierarchyDecisionService;
    @Autowired
	private FPLOSByHierarchyByRoomClassDecisionService fplosByHierarchyByRoomClassDecisionService;
    @Autowired
	private FPLOSByRoomTypeDecisionService fplosByRoomTypeDecisionService;
    @Autowired
	private FPLOSByRoomClassDecisionService fplosByRoomClassDecisionService;
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;
    @Autowired
	private PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
	private BARDecisionService barDecisionService;
    @Autowired
    private ProfitAdjustmentService profitAdjustmentService;
    @Autowired
	private LRADecisionService lraDecisionService;
    @Autowired
	private DailyBarVendorRoomTypeTranslator dailyBarVendorRoomTypeTranslator;
    @Autowired
	private RateCodeVendorMappingService rateCodeVendorMappingService;
    @Autowired
	private RestClient restClient;
    @Autowired
	private ManualRestrictionDecisionService manualRestrictionDecisionService;

    @Override
    public List<HotelOverbookingDecision> getHotelOverbookingDecisions(@FormParam("propertyId") String propertyId) {
        String externalSystem = getExternalSystem();
        return fetchHotelOverbookingDecisions(externalSystem);
    }

    public List<HotelOverbookingDecision> getHotelOverbookingDecisions(String propertyId, String externalSystem) {
        return fetchHotelOverbookingDecisions(externalSystem);
    }

    @Override
    public boolean hasHotelOverbookingDecisions(String externalSystem) {
        Date lastUpdateDate = queryForLastUpdateDateToExternalSystem(Constants.OPERA_HOTEL_OVERBOOKING, externalSystem);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(LAST_UPDATE_DATE_FOR_DECISION_TYPE + Constants.OPERA_HOTEL_OVERBOOKING + " is " + lastUpdateDate);
        }

        Date endDate = dateService.getDecisionUploadWindowEndDate();
        List<PropertyOverbookingDecision> propertyOverbookingDecisions =
                pacmanOverbookingDecisionService.getPropertyOverbookingDecisions(null, endDate, lastUpdateDate);

        if (propertyOverbookingDecisions != null) {
            propertyOverbookingDecisions = filterEmptyAuthorizedCapacity(externalSystem, propertyOverbookingDecisions);
        }

        return propertyOverbookingDecisions != null && !propertyOverbookingDecisions.isEmpty();
    }

    private List<HotelOverbookingDecision> fetchHotelOverbookingDecisions(
            String externalSystem) {
        Date lastUpdateDate = queryForLastUpdateDateToExternalSystem(Constants.OPERA_HOTEL_OVERBOOKING, externalSystem);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(LAST_UPDATE_DATE_FOR_DECISION_TYPE + Constants.OPERA_HOTEL_OVERBOOKING + " is " + lastUpdateDate);
        }

        Date endDate = dateService.getDecisionUploadWindowEndDate();
        List<PropertyOverbookingDecision> propertyOverbookingDecisions =
                pacmanOverbookingDecisionService.getPropertyOverbookingDecisions(null, endDate, lastUpdateDate);

        List<HotelOverbookingDecision> operaDecisions = new ArrayList<>();
        if (propertyOverbookingDecisions != null) {
            propertyOverbookingDecisions = filterEmptyAuthorizedCapacity(externalSystem, propertyOverbookingDecisions);
            for (PropertyOverbookingDecision propertyOverbookingDecision : propertyOverbookingDecisions) {
                HotelOverbookingDecision decision = new HotelOverbookingDecision();
                decision.setAuthorizedCapacity(propertyOverbookingDecision.getAuthorizedCapacity());
                decision.setExpectedWalks(propertyOverbookingDecision.getExpectedWalks());
                decision.setOccupancyDate(propertyOverbookingDecision.getOccupancyDate());
                decision.setOverbooking(propertyOverbookingDecision.getOverbooking());
                operaDecisions.add(decision);
            }
        }

        insertRecordForDecisionDeliveryTracking(Constants.OPERA_HOTEL_OVERBOOKING,
                getParameterValue(configParameterNameService.getIntegrationUploadTypeParameterName(externalSystem, Constants.OPERA_HOTEL_OVERBOOKING, Constants.UPLOAD_TYPE)),
                Arrays.asList(Constants.DECISION_TYPE_BDE, Constants.DECISION_TYPE_CDP), externalSystem);
        return operaDecisions;
    }

    private List<PropertyOverbookingDecision> filterEmptyAuthorizedCapacity(String externalSystem,
                                                                            List<PropertyOverbookingDecision> propertyOverbookingDecisions) {
        if (ExternalSubSystem.SIHOT.toString().equals(externalSystem)) {
            return propertyOverbookingDecisions
                    .stream()
                    .filter(propertyOverbookingDecision -> propertyOverbookingDecision.getAuthorizedCapacity() != null)
                    .collect(Collectors.toList());
        }
        return propertyOverbookingDecisions;
    }

    @Override
    public List<AccomOverbookingDecision> getAccomOverbookingDecisions(@FormParam("propertyId") String propertyId) {
        String externalSystem = getExternalSystem();
        return fetchAccomOverbookingDecisions(externalSystem);
    }

    public List<AccomOverbookingDecision> getAccomOverbookingDecisions(
            String propertyId,
            String externalSystem) {
        return fetchAccomOverbookingDecisions(externalSystem);
    }

    @Override
    public boolean hasAccomOverbookingDecisions(String propertyId, String externalSystem) {
        Date lastUpdateDate = queryForLastUpdateDateToExternalSystem(Constants.OPERA_ROOM_TYPE_OVERBOOKING, externalSystem);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(LAST_UPDATE_DATE_FOR_DECISION_TYPE + Constants.OPERA_ROOM_TYPE_OVERBOOKING + " is " + lastUpdateDate);
        }

        Date endDate = dateService.getDecisionUploadWindowEndDate();
        List<com.ideas.tetris.pacman.services.decisiondelivery.dto.AccomOverbookingDecision> accomTypeOverbookingDecisions =
                pacmanOverbookingDecisionService.getAccomTypeOverbookingDecisions(null, endDate, lastUpdateDate);

        return accomTypeOverbookingDecisions != null && !accomTypeOverbookingDecisions.isEmpty();
    }

    private List<AccomOverbookingDecision> fetchAccomOverbookingDecisions(String externalSystem) {
        Date lastUpdateDate = queryForLastUpdateDateToExternalSystem(Constants.OPERA_ROOM_TYPE_OVERBOOKING, externalSystem);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(LAST_UPDATE_DATE_FOR_DECISION_TYPE + Constants.OPERA_ROOM_TYPE_OVERBOOKING + " is " + lastUpdateDate);
        }

        Date endDate = dateService.getDecisionUploadWindowEndDate();
        List<com.ideas.tetris.pacman.services.decisiondelivery.dto.AccomOverbookingDecision> accomTypeOverbookingDecisions =
                pacmanOverbookingDecisionService.getAccomTypeOverbookingDecisions(null, endDate, lastUpdateDate);

        List<AccomOverbookingDecision> operaDecisions = new ArrayList<>();
        if (null != accomTypeOverbookingDecisions) {
            for (com.ideas.tetris.pacman.services.decisiondelivery.dto.AccomOverbookingDecision accomTypeOverbookingDecision : accomTypeOverbookingDecisions) {
                AccomOverbookingDecision accomOverbookingDecision = new AccomOverbookingDecision();
                accomOverbookingDecision.setAccomType(accomTypeOverbookingDecision.getAccomType());
                accomOverbookingDecision.setAuthorizedCapacity(accomTypeOverbookingDecision.getAuthorizedCapacity());
                accomOverbookingDecision.setOccupancyDate(accomTypeOverbookingDecision.getOccupancyDate());
                accomOverbookingDecision.setOverbooking(accomTypeOverbookingDecision.getOverbooking());
                operaDecisions.add(accomOverbookingDecision);
            }
        }

        insertRecordForDecisionDeliveryTracking(Constants.OPERA_ROOM_TYPE_OVERBOOKING,
                getParameterValue(configParameterNameService.getIntegrationUploadTypeParameterName(externalSystem,
                        Constants.OPERA_ROOM_TYPE_OVERBOOKING, Constants.UPLOAD_TYPE)),
                Arrays.asList(Constants.DECISION_TYPE_BDE, Constants.DECISION_TYPE_CDP), externalSystem);
        return operaDecisions;
    }

    @Override
    public List<LastRoomValueDecision> getLastRoomValueDecisions(final String propertyId) {
        return getLastRoomValueDecisions(propertyId, getExternalSystem());
    }

    @Override
    public List<LastRoomValueDecision> getLastRoomValueDecisions(final String propertyId, final String externalSystem) {
        return getLastRoomValueDecisions(propertyId, externalSystem, Constants.BDE);
    }

    @Override
    public List<LastRoomValueDecision> getLastRoomValueDecisions(final String propertyId, final String externalSystem,
                                                                 final String operationType) {
        List<LastRoomValueDecision> decisions;
        final List<LastRoomValue> lastRoomValues = getLastRoomValues(externalSystem, operationType);
        boolean sendRoomTypeAsRoomClassLRV = pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.SEND_ROOM_TYPES_AS_ROOM_CLASS_LRV);
        if (sendRoomTypeAsRoomClassLRV) {
            // Send LRV_at_Accom_Type as LRV_at_Accom_Class (because LRV at Accom Type is not supported by Opera PMS)
            // Here we are replacing Accom class with its respective Accom Types
            decisions = retrieveLRVDecisionsByRoomType(lastRoomValues, externalSystem, operationType);
        } else {
            decisions = retrieveLRVDecisionsByRoomClass(lastRoomValues);
        }


        boolean htngOutboundSpecificToggle = pacmanConfigParamsService
                .getBooleanParameterValue(IntegrationConfigParamName.DECISION_DELIVERY_SERVICE_ENABLED.value(externalSystem, PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode()));

        String lrvUploadType = List.of("OHD", "OCP", "RESERVE", "DecisionDelivery1").contains(externalSystem) || htngOutboundSpecificToggle
                ? configParamsService.getParameterValue(DecisionType.LAST_ROOM_VALUE_BY_ROOM_CLASS.getIntegrationConfigParamName(), externalSystem)
                : getParameterValue(configParameterNameService.getIntegrationUploadTypeParameterName(externalSystem, Constants.LAST_ROOM_VALUE_BY_ROOM_CLASS, Constants.UPLOAD_TYPE));

        insertRecordForDecisionDeliveryTracking(Constants.LAST_ROOM_VALUE_BY_ROOM_CLASS, lrvUploadType,
                Arrays.asList(Constants.DECISION_TYPE_BDE, Constants.DECISION_TYPE_CDP), externalSystem);

        return decisions;
    }

    @Override
    public List<LastRoomValueDecision> getLastRoomValueByRoomTypeDecisions(final String propertyId, final String externalSystem,
                                                                           final String operationType) {
        final List<LastRoomValue> lastRoomValues = getLastRoomValues(externalSystem, operationType);
        final List<LastRoomValueDecision> decisions = retrieveLRVDecisionsByRoomType(lastRoomValues, externalSystem, operationType);

        String lrvUploadType = ExternalSubSystem.SIHOT.toString().equals(externalSystem)
                ? configParamsService.getParameterValue(DecisionType.LAST_ROOM_VALUE_BY_ROOM_TYPE.getIntegrationConfigParamName(), externalSystem)
                : getParameterValue(configParameterNameService.getIntegrationUploadTypeParameterName(externalSystem, Constants.LAST_ROOM_VALUE_BY_ROOM_TYPE, Constants.UPLOAD_TYPE));

        insertRecordForDecisionDeliveryTracking(Constants.LAST_ROOM_VALUE_BY_ROOM_TYPE, lrvUploadType,
                Arrays.asList(Constants.DECISION_TYPE_BDE, Constants.DECISION_TYPE_CDP), externalSystem);

        return decisions;
    }

    private List<LastRoomValueDecision> retrieveLRVDecisionsByRoomType(final List<LastRoomValue> lastRoomValues,
                                                                       final String externalSystem,
                                                                       final String operationType) {
        final List<LastRoomValueAccomType> lastRoomValuesAtAccomType = getLastRoomValuesAtAccomType(externalSystem, operationType);
        return createLRVDecisionsAtAccomType(lastRoomValuesAtAccomType, lastRoomValues);
    }

    private Map<Date, Integer> getOccupancyDateMaxSoldMap(List<LastRoomValue> lastRoomValues) {
        // Sorting the list based on occupancy date to fetch start and end dates.
        lastRoomValues.sort(Comparator.comparing(LastRoomValue::getOccupancyDate));
        Date startDate = lastRoomValues.get(0).getOccupancyDate();
        Date endDate = lastRoomValues.get(lastRoomValues.size() - 1).getOccupancyDate();
        LOGGER.info("Start Date = " + startDate + "      End Date = " + endDate);

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
        paramMap.put("startDate", startDate);
        paramMap.put("endDate", endDate);
        @SuppressWarnings("unchecked")
        List<DecisionOvrbkProperty> decisionOvrbkPropertyList = crudService.findByNamedQuery(DecisionOvrbkProperty.BY_PROPERTYID_AND_OCCUPANCY_DATE_RANGE, paramMap);

        Map<Date, Integer> occupancyDateMaxSoldMap = new HashMap<>();
        for (DecisionOvrbkProperty decisionOvrbkProperty : decisionOvrbkPropertyList) {
            occupancyDateMaxSoldMap.put(decisionOvrbkProperty.getOccupancyDate(), decisionOvrbkProperty.getMaxSolds());
        }

        return occupancyDateMaxSoldMap;
    }

    private Map<Integer, String> getaccomClassIdCodeMap() {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
        List<AccomClass> accomClassList = crudService.findByNamedQuery(AccomClass.ALL, paramMap);
        Map<Integer, String> accomClassIdCodeMap = new HashMap<>();
        for (AccomClass accomClass : accomClassList) {
            accomClassIdCodeMap.put(accomClass.getId(), accomClass.getCode());
        }
        return accomClassIdCodeMap;
    }

    private void populateMaps(Map<Integer, String> accomTypeIdCodeMap, Map<Integer, List<AccomType>> accomClassToAccomTypeMap) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
        List<AccomType> accomTypeList = crudService.findByNamedQuery(AccomType.ALL_ACTIVE_VALID_CAPACITY_WITHOUT_ACCOM_CLASS, paramMap);
        for (AccomType accomType : accomTypeList) {
            accomTypeIdCodeMap.put(accomType.getId(), accomType.getAccomTypeCode());
            Integer accomClassId = accomType.getAccomClass().getId();
            List<AccomType> accomTypes;
            if (accomClassToAccomTypeMap.get(accomClassId) == null) {
                accomTypes = new ArrayList<>();
            } else {
                accomTypes = accomClassToAccomTypeMap.get(accomClassId);
            }
            accomTypes.add(accomType);
            accomClassToAccomTypeMap.put(accomClassId, accomTypes);
        }
    }

    private List<LastRoomValueDecision> createLRVDecisionsAtAccomType(final List<LastRoomValueAccomType> lastRoomValueAccomType,
                                                                      final List<LastRoomValue> lastRoomValues) {
        final List<LastRoomValueDecision> decisions = new ArrayList<>();

        Double exchangeRate = getExchangeRate();
        if (CollectionUtils.isNotEmpty(lastRoomValues)) {
            Map<Integer, String> accomTypeIdCodeMap = new HashMap<>();
            Map<Integer, List<AccomType>> accomClassToAccomTypeMap = new HashMap<>();
            populateMaps(accomTypeIdCodeMap, accomClassToAccomTypeMap);
            Map<Date, Integer> occupancyDateMaxSoldMap = getOccupancyDateMaxSoldMap(lastRoomValues);

            //Distrbuting AccomClass level data to corresponding AccomType level data
            distributeAccomClassData(decisions, exchangeRate, lastRoomValues, accomClassToAccomTypeMap, accomTypeIdCodeMap,
                    occupancyDateMaxSoldMap);

            // Overwriting decision data with data from Decision_LRV_AT table for LRV@AcomType
            overwriteLrvDecisionData(decisions, exchangeRate, lastRoomValueAccomType, accomTypeIdCodeMap);

            LOGGER.info("Decisions At Accom Type Records: " + decisions.size());
        }

        return decisions;
    }

    private void distributeAccomClassData(List<LastRoomValueDecision> decisions, Double exchangeRate,
                                          List<LastRoomValue> lastRoomValues, Map<Integer, List<AccomType>> accomClassToAccomTypeMap,
                                          Map<Integer, String> accomTypeIdCodeMap, Map<Date, Integer> occupancyDateMaxSoldMap) {
        for (LastRoomValue lastRoomValue : lastRoomValues) {
            Integer accomClassId = lastRoomValue.getAccomClassID();
            List<AccomType> accomTypeList = accomClassToAccomTypeMap.get(accomClassId);
            addLrvDecisionsForAccomTypes(decisions, exchangeRate, accomTypeList, lastRoomValue, accomTypeIdCodeMap, occupancyDateMaxSoldMap);
        }
    }

    private void addLrvDecisionsForAccomTypes(List<LastRoomValueDecision> decisions, Double exchangeRate,
                                              List<AccomType> accomTypeList, LastRoomValue lastRoomValue,
                                              Map<Integer, String> accomTypeIdCodeMap, Map<Date, Integer> occupancyDateMaxSoldMap) {
        if (CollectionUtils.isNotEmpty(accomTypeList)) {
            for (AccomType accomType : accomTypeList) {
                LastRoomValueDecision decision = new LastRoomValueDecision();
                decision.setCeilingValue(lastRoomValue.getCeilingValue());
                decision.setDeltaValue(getConvertedRate(lastRoomValue.getDeltaValue(), exchangeRate));
                decision.setOccupancyDate(lastRoomValue.getOccupancyDate());
                decision.setOpportunityCost(getConvertedRate(lastRoomValue.getValue(), exchangeRate));
                // Accom_Type to be sent as Accom_Class
                decision.setInventoryCode(accomTypeIdCodeMap.get(accomType.getId()));
                Integer maxSolds = occupancyDateMaxSoldMap.get(lastRoomValue.getOccupancyDate());
                if (maxSolds != null) {
                    decision.setMaxSolds(new BigDecimal(maxSolds));
                }
                decisions.add(decision);
            }
        }
    }

    private void overwriteLrvDecisionData(List<LastRoomValueDecision> decisions, Double exchangeRate,
                                          List<LastRoomValueAccomType> lastRoomValueAccomType, Map<Integer, String> accomTypeIdCodeMap) {
        if (isEmpty(lastRoomValueAccomType)) {
            return;
        }
        int i = 0;
        for (LastRoomValueDecision newDecisions : decisions) {
            for (LastRoomValueAccomType newlrvAccomType : lastRoomValueAccomType) {
                if (newDecisions.getOccupancyDate().equals(newlrvAccomType.getOccupancyDate()) &&
                        newDecisions.getInventoryCode().equals(accomTypeIdCodeMap.get(newlrvAccomType.getAccomTypeID()))) {
                    newDecisions.setOpportunityCost(getConvertedRate(newlrvAccomType.getValue(), exchangeRate));
                    i++;
                }
            }
        }
        LOGGER.info("LRV@AccomType Overwritten using Decision_LRV_AT count : " + i);
    }

    private List<LastRoomValueDecision> retrieveLRVDecisionsByRoomClass(final List<LastRoomValue> lastRoomValues) {
        final List<LastRoomValueDecision> decisions = new ArrayList<>();

        Double exchangeRate = getExchangeRate();
        if (CollectionUtils.isNotEmpty(lastRoomValues)) {
            Map<Integer, String> accomClassIdCodeMap = getaccomClassIdCodeMap();
            Map<Date, Integer> occupancyDateMaxSoldMap = getOccupancyDateMaxSoldMap(lastRoomValues);

            for (LastRoomValue lastRoomValue : lastRoomValues) {
                LastRoomValueDecision decision = new LastRoomValueDecision();
                decision.setCeilingValue(lastRoomValue.getCeilingValue());
                decision.setDeltaValue(getConvertedRate(lastRoomValue.getDeltaValue(), exchangeRate));
                decision.setOccupancyDate(lastRoomValue.getOccupancyDate());
                decision.setOpportunityCost(getConvertedRate(lastRoomValue.getValue(), exchangeRate));
                decision.setInventoryCode(accomClassIdCodeMap.get(lastRoomValue.getAccomClassID()));
                Integer maxSolds = occupancyDateMaxSoldMap.get(lastRoomValue.getOccupancyDate());
                if (maxSolds != null) {
                    decision.setMaxSolds(new BigDecimal(maxSolds));
                }

                decisions.add(decision);
            }
        }

        return decisions;
    }

    @Override
    public boolean hasLRVDecisions(String propertyId, String externalSystem) {
        return hasLRVDecisions(propertyId, externalSystem, Constants.BDE);
    }

    @Override
    public boolean hasLRVDecisions(String propertyId, String externalSystem, String operationType) {
        return lastRoomValueDecisionService.getLastRoomValueDecisionCount(
                dateService.getOptimizationWindowStartDate(),
                dateService.getOperationTypeDecisionUploadEndDate(operationType)) > 0;
    }

    private List<LastRoomValue> getLastRoomValues(String externalSystem, String operationType) {
        return lastRoomValueDecisionService.getLastRoomValueDecisions(
                externalSystem,
                dateService.getOptimizationWindowStartDate(),
                dateService.getOperationTypeDecisionUploadEndDate(operationType),
                isApplyTax(externalSystem));
    }

    private List<LastRoomValueAccomType> getLastRoomValuesAtAccomType(String externalSystem, String operationType) {
        return lastRoomValueDecisionService.getLastRoomValueAtAccomTypeDecisions(
                externalSystem,
                dateService.getOptimizationWindowStartDate(),
                dateService.getOperationTypeDecisionUploadEndDate(operationType),
                isApplyTax(externalSystem));
    }

    private boolean isApplyTax(String externalSystem) {
        if (ExternalSubSystem.SIHOT.toString().equals(externalSystem)) {
            return isApplyTaxByTaxAdjustmentValue(externalSystem);
        } else {
            String shouldApplyTaxParameterName = configParameterNameService.getIntegrationParameterName(externalSystem, Constants.APPLY_TAX);
            return Boolean.parseBoolean(pacmanConfigParamsService.getParameterValue(shouldApplyTaxParameterName));
        }
    }

    private boolean isApplyTaxByTaxAdjustmentValue(String externalSystem) {
        String taxAdjustmentValueParameterName = configParameterNameService.getIntegrationParameterName(externalSystem, TAX_ADJUSTMENT_VALUE);
        String taxAdjustmentValueParameterValue = pacmanConfigParamsService.getParameterValue(taxAdjustmentValueParameterName);

        return StringUtils.isNotEmpty(taxAdjustmentValueParameterValue) &&
                !Precision.equals(Double.parseDouble(taxAdjustmentValueParameterValue), 0, Precision.EPSILON);
    }


    @Override
    public List<DailyBarDecision> getDailyBarDecisions(
            @FormParam("propertyId") String propertyId) {

        String externalSystem = getExternalSystem();
        return fetchDailyBarDecisions(externalSystem);
    }

    public List<DailyBarDecision> getDailyBarDecisions(
            String propertyId,
            String externalSystem) {
        return fetchDailyBarDecisions(externalSystem);
    }

    @Override
    public List<DailyBarDecision> getDailyBarDecisionsForAgileRates(String propertyId, String externalSystem, String rateCode) {
        return fetchDailyBarDecisionsForAgileRates(externalSystem, rateCode);
    }

    @Override
    public List<DailyBarDecision> getAgileRateDecisions(String propertyId, String externalSystem, Date uploadWindowStartDate, Date uploadWindowEndDate) {
        Date lastUploadedDate = queryForLastUpdateDateToExternalSystem(Constants.AGILE_RATES, externalSystem);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(LAST_UPDATE_DATE_FOR_DECISION_TYPE + Constants.AGILE_RATES + " is " + lastUploadedDate);
        }

        boolean applyTax = isApplyTax(externalSystem);

        List<DailyBarDecisions> agileRatesDecisions = pacmanAgileRatesDecisionService
                .getAgileRatesDecision(applyTax, lastUploadedDate, externalSystem, uploadWindowStartDate, uploadWindowEndDate);

        if (!agileRatesDecisions.isEmpty() && shouldIncludeAllRoomTypes(lastUploadedDate, externalSystem, Constants.AGILE_RATES)) {
            final List<Date> dates = agileRatesDecisions
                    .stream()
                    .map(DailyBarDecisions::getOccupancyDate)
                    .distinct()
                    .collect(Collectors.toList());
            agileRatesDecisions = pacmanAgileRatesDecisionService.getAllAgileRatesDecision(applyTax, externalSystem, dates);
        }

        Map<String, List<DailyBarDecisions>> rateCodeToDecisionMap = new HashMap<>();
        agileRatesDecisions.forEach(decision -> {
            rateCodeToDecisionMap
                    .computeIfAbsent(decision.getRatePlan(), ratePlan -> new ArrayList<>())
                    .add(decision);
        });
        List<DailyBarDecision> decisions = new ArrayList<>();
        rateCodeToDecisionMap.keySet().forEach(rateCode -> {
            List<DailyBarDecisions> decisionsForTargetedRateCode = rateCodeToDecisionMap.get(rateCode);
            List<DailyBarDecisions> decisionsAfterRoomTypeTranslation = dailyBarVendorRoomTypeTranslator.translateVendorRoomTypes(decisionsForTargetedRateCode, StringUtils.lowerCase(externalSystem));
            List<RateCodeVendorMapping> vendorRateCodeMappingForTargetedRateCode = rateCodeVendorMappingService.getForVendorAndRateCodeName(externalSystem, rateCode);
            List<DailyBarDecision> decisionsAfterVendorRateCodeMapping;
            if (!vendorRateCodeMappingForTargetedRateCode.isEmpty()) {
                decisionsAfterVendorRateCodeMapping = mapPmsRateCodes(decisionsAfterRoomTypeTranslation, vendorRateCodeMappingForTargetedRateCode);
                decisionsAfterVendorRateCodeMapping.addAll(createDecisionsIfAgileRateCodeNotMappedToItself(externalSystem, decisionsForTargetedRateCode, vendorRateCodeMappingForTargetedRateCode));
            } else {
                decisionsAfterVendorRateCodeMapping = mapPmsRateCodes(decisionsAfterRoomTypeTranslation, rateCode);
            }
            decisions.addAll(decisionsAfterVendorRateCodeMapping);
        });

        insertRecordForDecisionDeliveryTracking(Constants.AGILE_RATES,
                getParameterValue(configParameterNameService.getIntegrationUploadTypeParameterName(externalSystem, Constants.AGILE_RATES, Constants.UPLOAD_TYPE)),
                Arrays.asList(Constants.DECISION_TYPE_DAILYBAR, DECISION_TYPE_DAILYBAR_FULL_REFRESH), externalSystem);
        return decisions;
    }

    @Override
    public boolean hasDailyBarDecisions(String externalSystem) {
        boolean isApplyTax = isApplyTax(externalSystem);
        Date lastUploadedDate = queryForLastUpdateDateToExternalSystem(Constants.DAILYBAR, externalSystem);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(LAST_UPDATE_DATE_FOR_DECISION_TYPE + Constants.DAILYBAR + " is " + lastUploadedDate);
        }
        List<DailyBarDecisions> dailyBarDecisions = dailyBarDecisionService.getDailyBarDecisions(isApplyTax, lastUploadedDate, externalSystem);

        return dailyBarDecisions != null && !dailyBarDecisions.isEmpty();
    }

    @Override
    public boolean hasAgileRatesDecisions(String externalSystem, String vendorRateCode) {
        Date lastUploadedDate = queryForLastUpdateDateToExternalSystem(Constants.AGILE_RATES, externalSystem);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(LAST_UPDATE_DATE_FOR_DECISION_TYPE + Constants.AGILE_RATES + " is " + lastUploadedDate);
        }
        List<RateCodeVendorMapping> vendorRateCodeMapping = getVendorRateCodeMapping(externalSystem, vendorRateCode);
        List<DailyBarDecisions> dailyBarDecisions = dailyBarDecisionService.getDailyBarDecisionsForAgileRates(getMappedRateCodes(vendorRateCode, vendorRateCodeMapping), isApplyTax(externalSystem),
                lastUploadedDate, externalSystem, true);

        return dailyBarDecisions != null && !dailyBarDecisions.isEmpty();
    }

    @Override
    public boolean hasAgileRatesDecisions(String externalSystem) {
        if (!pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED)) {
            return false;
        }
        boolean isApplyTax = isApplyTax(externalSystem);
        Date lastUploadedDate = queryForLastUpdateDateToExternalSystem(AGILE_RATES, externalSystem);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(LAST_UPDATE_DATE_FOR_DECISION_TYPE + Constants.AGILE_RATES + " is " + lastUploadedDate);
        }
        List<DailyBarDecisions> agileRatesDecisions = pacmanAgileRatesDecisionService.getAgileRatesDecision(isApplyTax, lastUploadedDate, externalSystem, null, null);
        return agileRatesDecisions != null && !agileRatesDecisions.isEmpty();
    }

    private List<DailyBarDecision> fetchDailyBarDecisions(String externalSystem) {
        Date lastUploadedDate = queryForLastUpdateDateToExternalSystem(Constants.DAILYBAR, externalSystem);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(LAST_UPDATE_DATE_FOR_DECISION_TYPE + Constants.DAILYBAR + " is " + lastUploadedDate);
        }

        boolean applyTax = isApplyTax(externalSystem);

        List<DailyBarDecisions> dailyBarDecisions = dailyBarDecisionService
                .getDailyBarDecisions(applyTax, lastUploadedDate, externalSystem);

        if (!dailyBarDecisions.isEmpty() && shouldIncludeAllRoomTypes(lastUploadedDate, externalSystem, Constants.DAILYBAR)) {
            final List<Date> dates = dailyBarDecisions
                    .stream()
                    .map(DailyBarDecisions::getOccupancyDate)
                    .distinct()
                    .collect(Collectors.toList());
            dailyBarDecisions = dailyBarDecisionService.getAllDecisionForDates(applyTax, lastUploadedDate, externalSystem, dates);
        }

        List<DailyBarDecisions> decisionsWithRoomTypeMapped = dailyBarVendorRoomTypeTranslator.translateVendorRoomTypes(dailyBarDecisions,
                StringUtils.lowerCase(externalSystem));

        List<RateCodeVendorMapping> mappedPmsRateCodes = getVendorMappedRateCodes(externalSystem, Collections.singletonList(getOperaDailyBarRateCode()));
        List<DailyBarDecision> decisions = mapPmsRateCodes(decisionsWithRoomTypeMapped, mappedPmsRateCodes);
        decisions.addAll(createDecisionsForOperaDailyRateCodeIfNotMappedToItSelf(externalSystem, dailyBarDecisions, mappedPmsRateCodes));

        insertRecordForDecisionDeliveryTracking(Constants.DAILYBAR,
                getParameterValue(configParameterNameService.getIntegrationUploadTypeParameterName(externalSystem, Constants.DAILYBAR, Constants.UPLOAD_TYPE)),
                Arrays.asList(Constants.DECISION_TYPE_DAILYBAR, DECISION_TYPE_DAILYBAR_FULL_REFRESH), externalSystem);

        return decisions;
    }

    private boolean shouldIncludeAllRoomTypes(final Date lastUploadedDate,
                                              final String externalSystem,
                                              final String decisionType) {

        return ExternalSubSystem.SIHOT.toString().equals(externalSystem)
                && isDifferentialDecisionUpload(externalSystem, decisionType)
                && lastUploadedDate != null;
    }

    private List<DailyBarDecision> fetchDailyBarDecisionsForAgileRates(String externalSystem, String vendorRateCode) {
        Date lastUploadedDate = queryForLastUpdateDateToExternalSystem(Constants.AGILE_RATES, externalSystem);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(LAST_UPDATE_DATE_FOR_DECISION_TYPE + Constants.AGILE_RATES + " is " + lastUploadedDate);
        }

        List<RateCodeVendorMapping> vendorRateCodeMapping = getVendorRateCodeMapping(externalSystem, vendorRateCode);
        List<DailyBarDecisions> dailyBarDecisions = dailyBarDecisionService.getDailyBarDecisionsForAgileRates(getMappedRateCodes(vendorRateCode, vendorRateCodeMapping), isApplyTax(externalSystem),
                lastUploadedDate, externalSystem, false);

        List<DailyBarDecisions> decisionsWithRoomTypeMapped = dailyBarVendorRoomTypeTranslator.translateVendorRoomTypes(dailyBarDecisions,
                StringUtils.lowerCase(externalSystem));

        List<DailyBarDecision> decisions = mapPmsRateCodes(decisionsWithRoomTypeMapped, vendorRateCode);
        decisions.addAll(createDecisionsIfAgileRateCodeNotMappedToItself(externalSystem, dailyBarDecisions, vendorRateCodeMapping));


        if (!isDecisionDeliveryTrackingRecordInsertedForAgileRates(externalSystem)) {
            insertRecordForDecisionDeliveryTracking(Constants.AGILE_RATES,
                    getParameterValue(configParameterNameService.getIntegrationUploadTypeParameterName(externalSystem, Constants.AGILE_RATES, Constants.UPLOAD_TYPE)),
                    Arrays.asList(Constants.DECISION_TYPE_DAILYBAR, DECISION_TYPE_DAILYBAR_FULL_REFRESH), externalSystem);
        }
        return decisions;

    }

    private boolean isDecisionDeliveryTrackingRecordInsertedForAgileRates(String externalSystem) {
        return decisionUploadDateToExternalSystemRepository.findByDecisionNameExternalName(Constants.AGILE_RATES, externalSystem) != null;
    }


    private List<RateCodeVendorMapping> getVendorRateCodeMapping(String externalSystem, String vendorRateCode) {
        return new ArrayList<>(rateCodeVendorMappingService.getVendorRateCodeMappingForVendorRateCode(externalSystem, vendorRateCode));
    }

    private String getMappedRateCodes(String rateCode, List<RateCodeVendorMapping> mappedPmsRateCodes) {
        return mappedPmsRateCodes.isEmpty() ? rateCode : mappedPmsRateCodes.get(0).getRateCodeName();
    }

    private List<DailyBarDecision> createDecisionsForOperaDailyRateCodeIfNotMappedToItSelf(String externalSystem, List<DailyBarDecisions> dailyBarDecisions,
                                                                                           List<RateCodeVendorMapping> vendorMappedRateCodes) {
        final String operaDailyBarRateCode = getOperaDailyBarRateCode();
        if (isOperaPmsDailyRateCodeMappedToItself(operaDailyBarRateCode, vendorMappedRateCodes)) {
            return (mapPmsRateCodes(dailyBarDecisions,
                    Collections.singletonList(new RateCodeVendorMapping(operaDailyBarRateCode, externalSystem, operaDailyBarRateCode))));
        }

        return Collections.emptyList();
    }

    private List<DailyBarDecision> createDecisionsIfAgileRateCodeNotMappedToItself(String externalSystem, List<DailyBarDecisions> dailyBarDecisions,
                                                                                   List<RateCodeVendorMapping> vendorMappedRateCodes) {
        if (!vendorMappedRateCodes.isEmpty()) {
            String rateCodeName = vendorMappedRateCodes.get(0).getRateCodeName();
            if (isOperaPmsDailyRateCodeMappedToItself(rateCodeName, vendorMappedRateCodes)) {
                return (mapPmsRateCodes(dailyBarDecisions,
                        Collections.singletonList(new RateCodeVendorMapping(rateCodeName, externalSystem, rateCodeName))));
            }
        }
        return Collections.emptyList();
    }

    private boolean isOperaPmsDailyRateCodeMappedToItself(String operaDailyBarRateCode, List<RateCodeVendorMapping> vendorMappedRateCodes) {
        return vendorMappedRateCodes.stream().noneMatch(rateCodeVendorMapping -> operaDailyBarRateCode.equals(rateCodeVendorMapping.getVendorRateCode()));
    }

    private List<DailyBarDecision> mapPmsRateCodes(List<DailyBarDecisions> mappedDecisions,
                                                   List<RateCodeVendorMapping> mappedPmsRateCodes) {
        Double exchangeRate = getExchangeRateForYieldCurrency();
        List<DailyBarDecision> dailyBarDecisions = new ArrayList<>();

        mappedPmsRateCodes.forEach(rateCodeVendorMapping -> dailyBarDecisions.addAll(
                createDecisionWithRateCode(
                        rateCodeVendorMapping.getVendorRateCode(),
                        mappedDecisions,
                        exchangeRate)));

        return dailyBarDecisions;
    }

    private List<DailyBarDecision> mapPmsRateCodes(List<DailyBarDecisions> mappedDecisions, String vendorRateCode) {
        Double exchangeRate = getExchangeRateForYieldCurrency();
        List<DailyBarDecision> dailyBarDecisions = new ArrayList<>();
        dailyBarDecisions.addAll(createDecisionWithRateCode(vendorRateCode, mappedDecisions, exchangeRate));
        return dailyBarDecisions;
    }

    private List<DailyBarDecision> createDecisionWithRateCode(String rateCode, List<DailyBarDecisions> mappedDecisions,
                                                              Double exchangeRate) {
        return mappedDecisions.stream()
                .map(dailyDecision -> mapDailyBarDecision(rateCode, exchangeRate, dailyDecision))
                .collect(Collectors.toList());
    }

    private DailyBarDecision mapDailyBarDecision(String rateCode, Double exchangeRate, DailyBarDecisions dailyDecision) {
        return new DailyBarDecision(rateCode, dailyDecision.getRoomType(), dailyDecision.getOccupancyDate(),
                getConvertedRate(dailyDecision.getSingleRate(), exchangeRate),
                getConvertedRate(dailyDecision.getDoubleRate(), exchangeRate),
                getConvertedRate(dailyDecision.getTripleRate(), exchangeRate),
                getConvertedRate(dailyDecision.getQuadRate(), exchangeRate),
                getConvertedRate(dailyDecision.getQuintRate(), exchangeRate),
                getConvertedRate(dailyDecision.getAdultRate(), exchangeRate),
                getConvertedRate(dailyDecision.getChildRate(), exchangeRate),
                getConvertedRate(dailyDecision.getChildAgeOneRate(), exchangeRate),
                getConvertedRate(dailyDecision.getChildAgeTwoRate(), exchangeRate),
                getConvertedRate(dailyDecision.getChildAgeThreeRate(), exchangeRate),
                getConvertedRate(dailyDecision.getChildAgeFourRate(), exchangeRate),
                getConvertedRate(dailyDecision.getOneChildRate(), exchangeRate),
                getConvertedRate(dailyDecision.getTwoChildRate(), exchangeRate),
                getConvertedRate(dailyDecision.getThreeChildRate(), exchangeRate),
                getConvertedRate(dailyDecision.getFourChildRate(), exchangeRate),
                getConvertedRate(dailyDecision.getFiveChildRate(), exchangeRate));
    }

    private Double getExchangeRateForYieldCurrency() {
        Double exchangeRate = 1.0;
        if (!pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.USE_YIELD_CURRENCY_FOR_DAILY_BAR.value(Constants.OPERA))) {
            exchangeRate = getExchangeRate();
        }
        return exchangeRate;
    }

    private List<RateCodeVendorMapping> getVendorMappedRateCodes(String externalSystem, List<String> rateCodeNames) {
        return new ArrayList<>(rateCodeVendorMappingService.getRateCodeNameVendorMappings(externalSystem,
                rateCodeNames));
    }

    public List<String> fetchVendorMappedRateCodesForRatesProductName(String externalSystem) {
        final List<String> uploadEnabledActiveRateProductNames = crudService.findByNamedQuery(Product.GET_UPLOAD_ENABLED_ACTIVE_AGILE_RATES_PRODUCT_NAME,
                QueryParameter.with("status", TenantStatusEnum.ACTIVE).and("isUpload", true).parameters());

        LOGGER.info("Group Product Fetch Flag : " + pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GET_ENABLED_ACTIVE_RATE_GROUP_PRODUCT_CODE) + " and "
                + "Independent Product Fetch Flag : " + pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GET_ENABLED_ACTIVE_RATE_INDEPENDENT_PRODUCT_CODE));

        if (pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GET_ENABLED_ACTIVE_RATE_GROUP_PRODUCT_CODE)) {
            uploadEnabledActiveRateProductNames.addAll(fetchRatesCodeForProduct(Product.GROUP_PRODUCT_CODE));
        }
        if (pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GET_ENABLED_ACTIVE_RATE_INDEPENDENT_PRODUCT_CODE)) {
            uploadEnabledActiveRateProductNames.addAll(fetchRatesCodeForProduct(Product.INDEPENDENT_PRODUCT_CODE));
        }
        final List<RateCodeVendorMapping> vendorMappedRateCodes = getVendorMappedRateCodes(externalSystem, uploadEnabledActiveRateProductNames);
        return isEmpty(vendorMappedRateCodes) ? uploadEnabledActiveRateProductNames : vendorMappedRateCodes.stream().map(RateCodeVendorMapping::getVendorRateCode).collect(Collectors.toList());
    }

    private List<String> fetchRatesCodeForProduct(String productCode) {
        return crudService.findByNamedQuery(Product.GET_UPLOAD_ENABLED_ACTIVE_PRODUCT_NAME_BY_CODE,
                QueryParameter.with("productCode", productCode).and("status", TenantStatusEnum.ACTIVE).and("isUpload", true).parameters());
    }

    private String getOperaDailyBarRateCode() {
        return pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.DAILYBAR_PMSRATE_CODE.value(Constants.OPERA));
    }

    public BigDecimal getConvertedRate(BigDecimal rate, Double exchangeRate) {
        if (rate != null && Double.compare(exchangeRate, 1.0) != 0) {
            return rate.multiply(BigDecimal.valueOf(exchangeRate)).setScale(10, BigDecimal.ROUND_CEILING);
        }
        return rate;
    }

    @Override
    public List<OperaMinLOSDecision> getMinLOSDecisions(@FormParam("propertyId") String propertyId, String decisionType) {
        String externalSystem = getExternalSystem();
        return addMinLOSDecisions(externalSystem, decisionType);
    }

    public List<OperaMinLOSDecision> getMinLOSDecisions(String propertyId, String externalSystem, String decisionType) {
        return addMinLOSDecisions(externalSystem, decisionType);
    }

    private List<OperaMinLOSDecision> addMinLOSDecisions(String externalSystem, String decisionType) {
        Date lastUploadedDate = queryForLastUpdateDateToExternalSystem(decisionType, externalSystem);
        List<MinlosDecisions> g3Decisions = minimumLengthOfStayDecisionService.getMinLOSDecisions(lastUploadedDate);

        List<OperaMinLOSDecision> operaDecisions = new ArrayList<>();
        for (MinlosDecisions g3Decision : g3Decisions) {
            OperaMinLOSDecision operaDecision = new OperaMinLOSDecision();
            operaDecision.setArrivalDate(g3Decision.getArrivalDate());
            operaDecision.setMinimumLengthOfStay(BigDecimal.valueOf(g3Decision.getMinlos()));
            operaDecision.setRateCode(g3Decision.getRatePlan());
            operaDecision.setRoomType(g3Decision.getRoomType());
            operaDecisions.add(operaDecision);
        }
        insertRecordForDecisionDeliveryTracking(decisionType,
                getParameterValue(configParameterNameService.getIntegrationUploadTypeParameterName(externalSystem, decisionType, Constants.UPLOAD_TYPE)),
                Collections.singletonList(Constants.DECISION_TYPE_MINLOS), externalSystem);
        return operaDecisions;
    }

    @Override
    public boolean hasMinLOSDecisions(String externalSystem, String decisionType) {
        Date lastUploadedDate = queryForLastUpdateDateToExternalSystem(decisionType, externalSystem);
        List<MinlosDecisions> g3Decisions = minimumLengthOfStayDecisionService.getMinLOSDecisions(lastUploadedDate);

        return g3Decisions != null && !g3Decisions.isEmpty();
    }

    @Override
    public List<OperaFplosQualifiedDecision> getFplosQualifiedDecisions(@FormParam("propertyId") String propertyId, String decisionType) {
        // Use this when FPLOS decisions are configured for OPER with upload type in place
        String externalSystem = getExternalSystem();
        return fetchFplosQualifiedDecisions(externalSystem, decisionType);
    }

    public List<OperaFplosQualifiedDecision> getFplosQualifiedDecisions(String propertyId, String externalSystem, String decisionType) {
        return fetchFplosQualifiedDecisions(externalSystem, decisionType);
    }

    private List<OperaFplosQualifiedDecision> fetchFplosQualifiedDecisions(String externalSystem, String decisionType) {
        int decisionTypeID = Constants.DECISION_TYPE_QUALIFIED_FPLOS;
        List<FPLOSDecisions> g3Decisions = fplosQualifiedDecisionService.getFPLOSDecisionData(dateService.getOptimizationWindowStartDate(),
                dateService.getDecisionUploadWindowEndDate(),
                queryForLastUpdateDateToExternalSystem(decisionType, externalSystem));

        return getOperaDecisions(g3Decisions, decisionType, externalSystem, decisionTypeID);
    }

    @Override
    public boolean hasFplosQualifiedDecisions(String externalSystem, String decisionType) {
        List<FPLOSDecisions> g3Decisions = fplosQualifiedDecisionService.getFPLOSDecisionData(dateService.getOptimizationWindowStartDate(),
                dateService.getDecisionUploadWindowEndDate(),
                queryForLastUpdateDateToExternalSystem(decisionType, externalSystem));

        return g3Decisions != null && !g3Decisions.isEmpty();
    }

    @Override
    public boolean hasManualRestrictionDecisions(String externalSystem, String decisionName) {
        return manualRestrictionDecisionService.hasDecisions(decisionName, externalSystem);
    }

    @Override
    public List<OperaFplosQualifiedDecision> getBarFplosByHierarchyDecisions(String propertyId) {
        String externalSystem = getExternalSystem();
        return fetchBarFplosByHierarchyDecisions(externalSystem, Constants.OPERA_BAR_FPLOS_BY_HIERARCHY, Constants.DECISION_TYPE_BAR_FLOS_BY_HIERARCHY);

    }

    public List<OperaFplosQualifiedDecision> getBarFplosByHierarchyDecisions(String propertyId, String externalSystem) {
        return fetchBarFplosByHierarchyDecisions(externalSystem, Constants.OPERA_BAR_FPLOS_BY_HIERARCHY, Constants.DECISION_TYPE_BAR_FLOS_BY_HIERARCHY);
    }

    @Override
    public List<OperaFplosQualifiedDecision> getBarFplosByHierarchyByRoomClassDecisions(String propertyId, String externalSystem) {
        return fetchBarFplosByHierarchyByRoomClassDecisions(externalSystem, Constants.OPERA_BAR_FPLOS_BY_HIERARCHY_BY_ROOM_CLASS, Constants.DECISION_TYPE_BAR_FLOS_BY_HIERARCHY_BY_ROOM_CLASS);
    }

    private List<OperaFplosQualifiedDecision> fetchBarFplosByHierarchyDecisions(String externalSystem, String decisionType, Integer decisionTypeID) {
        List<FPLOSDecisions> fplosByHierarchyDecisionData = fplosByHierarchyDecisionService.getFPLOSByHierarchyDecisionData
                (dateService.getOptimizationWindowStartDate(), dateService.getDecisionUploadWindowEndDate(),
                        queryForLastUpdateDateToExternalSystem(decisionType, externalSystem));
        return getOperaDecisions(fplosByHierarchyDecisionData, decisionType, externalSystem, decisionTypeID);
    }

    private List<OperaFplosQualifiedDecision> fetchBarFplosByHierarchyByRoomClassDecisions(String externalSystem, String decisionType, Integer decisionTypeID) {
        List<FPLOSDecisions> fplosByHierarchyDecisionData = fplosByHierarchyByRoomClassDecisionService.getFPLOSByHierarchyByRoomClassDecisionData
                (dateService.getOptimizationWindowStartDate(), dateService.getDecisionUploadWindowEndDate(),
                        queryForLastUpdateDateToExternalSystem(decisionType, externalSystem));
        return getOperaDecisions(fplosByHierarchyDecisionData, decisionType, externalSystem, decisionTypeID);
    }

    @Override
    public boolean hasBarFplosByHierarchyDecisions(String externalSystem) {
        List<FPLOSDecisions> fplosByHierarchyDecisionData = fplosByHierarchyDecisionService.getFPLOSByHierarchyDecisionData
                (dateService.getOptimizationWindowStartDate(), dateService.getDecisionUploadWindowEndDate(),
                        queryForLastUpdateDateToExternalSystem(Constants.OPERA_BAR_FPLOS_BY_HIERARCHY, externalSystem));

        return fplosByHierarchyDecisionData != null && !fplosByHierarchyDecisionData.isEmpty();
    }

    @Override
    public boolean hasBarFplosByHierarchyByRoomClassDecisions(String externalSystem) {
        List<FPLOSDecisions> fplosByHierarchyDecisionData = fplosByHierarchyByRoomClassDecisionService.getFPLOSByHierarchyByRoomClassDecisionData
                (dateService.getOptimizationWindowStartDate(), dateService.getDecisionUploadWindowEndDate(),
                        queryForLastUpdateDateToExternalSystem(Constants.OPERA_BAR_FPLOS_BY_HIERARCHY_BY_ROOM_CLASS, externalSystem));

        return fplosByHierarchyDecisionData != null && !fplosByHierarchyDecisionData.isEmpty();
    }

    private List<OperaFplosQualifiedDecision> fetchBarFplosByRoomTypeDecisions(String externalSystem, String decisionType, Integer decisionTypeID) {
        List<FPLOSDecisions> fplosByRoomTypeDecisionData = fplosByRoomTypeDecisionService.getFPLOSByRoomTypeDecisionData
                (dateService.getOptimizationWindowStartDate(), dateService.getDecisionUploadWindowEndDate(),
                        queryForLastUpdateDateToExternalSystem(decisionType, externalSystem));
        return getOperaDecisions(fplosByRoomTypeDecisionData, decisionType, externalSystem, decisionTypeID);
    }

    private List<OperaFplosQualifiedDecision> fetchBarFplosByRoomClassDecisions(String externalSystem, String decisionType, Integer decisionTypeID) {
        List<FPLOSDecisions> fplosByRoomClassDecisionData = fplosByRoomClassDecisionService.getFPLOSByRoomClassDecisionData
                (dateService.getOptimizationWindowStartDate(), dateService.getDecisionUploadWindowEndDate(),
                        queryForLastUpdateDateToExternalSystem(decisionType, externalSystem));
        return getOperaDecisions(fplosByRoomClassDecisionData, decisionType, externalSystem, decisionTypeID);
    }

    private List<OperaFplosQualifiedDecision> getOperaDecisions(List<FPLOSDecisions> fplosDecisionData, String decisionType, String externalSystem, Integer decisionTypeID) {

        List<OperaFplosQualifiedDecision> operaDecisions = new ArrayList<>();
        for (FPLOSDecisions fplosDecisions : fplosDecisionData) {
            OperaFplosQualifiedDecision operaDecision = new OperaFplosQualifiedDecision();
            operaDecision.setArrivalDate(fplosDecisions.getArrivalDate());
            operaDecision.setFplos(fplosDecisions.getFplos());
            operaDecision.setRateCode(fplosDecisions.getRateCodeName());
            operaDecision.setRoomType(fplosDecisions.getAccomTypeName());
            operaDecision.setRoomClass(fplosDecisions.getAccomClassName());
            operaDecisions.add(operaDecision);
        }

        insertRecordForDecisionDeliveryTracking(decisionType,
                getParameterValue(configParameterNameService.getIntegrationUploadTypeParameterName(externalSystem,
                        decisionType, Constants.UPLOAD_TYPE)),
                Collections.singletonList(decisionTypeID), externalSystem);

        return operaDecisions;
    }

    @Override
    public boolean hasBarFplosByRoomTypeDecisions(String externalSystem) {
        List<FPLOSDecisions> fplosByRoomTypeDecisionData = fplosByRoomTypeDecisionService.getFPLOSByRoomTypeDecisionData
                (dateService.getOptimizationWindowStartDate(), dateService.getDecisionUploadWindowEndDate(),
                        queryForLastUpdateDateToExternalSystem(Constants.OPERA_BAR_FPLOS_BY_ROOM_TYPE, externalSystem));

        return fplosByRoomTypeDecisionData != null && !fplosByRoomTypeDecisionData.isEmpty();
    }

    @Override
    public boolean hasBarFplosByRoomClassDecisions(String externalSystem) {
        List<FPLOSDecisions> fplosByRoomClassDecisionData = fplosByRoomClassDecisionService.getFPLOSByRoomClassDecisionData
                (dateService.getOptimizationWindowStartDate(), dateService.getDecisionUploadWindowEndDate(),
                        queryForLastUpdateDateToExternalSystem(Constants.OPERA_BAR_FPLOS_BY_ROOM_CLASS, externalSystem));

        return fplosByRoomClassDecisionData != null && !fplosByRoomClassDecisionData.isEmpty();
    }

    @SuppressWarnings("unchecked")
    public int insertRecordForDecisionDeliveryTracking(String decisionName, String uploadType, List<Integer> decisionTypeIDs, String externalSystem) {
        int numberOfRecordsInserted = 0;
        String decisionUploadType = isForceFullUpload(uploadType);
        List<Object> decisionIDList = crudService.findByNativeQuery
                (QUERY_TO_GET_MAX_DECISION_ID_FOR_DECISION_TYPE, QueryParameter.with("decisionTypeIDs", decisionTypeIDs).parameters());
        if (CollectionUtils.isNotEmpty(decisionIDList) && decisionIDList.get(0) != null) {
            BigInteger decisionId = (BigInteger) decisionIDList.get(0);
            decisionUploadDateToExternalSystemRepository.save(decisionId, decisionName, externalSystem, decisionUploadType);
            return 1;
        } else {
            LOGGER.error("Decision For " + decisionName + " are not generated");
        }
        return numberOfRecordsInserted;
    }

    @Override
    public List<BarByLOSDecision> getBarByLOSDecisions(String propertyId) {
        String externalSystem = getExternalSystem();
        return fetchBarByLOSDecisions(externalSystem);
    }

    public List<BarByLOSDecision> getBarByLOSDecisions(String propertyId, String externalSystem) {
        return fetchBarByLOSDecisions(externalSystem);
    }

    private List<BarByLOSDecision> fetchBarByLOSDecisions(String externalSystem) {
        Date startDate = dateService.getOptimizationWindowStartDate();
        Date endDate = dateService.getDecisionUploadWindowEndDate();
        Date lastUploadedDate = queryForLastUpdateDateToExternalSystem(Constants.OPERA_BAR_BY_LOS, externalSystem);
        List<BarByLOSDecision> operaDecisions = getBarByLOSDecisions(startDate, endDate, lastUploadedDate);

        insertRecordForDecisionDeliveryTracking(Constants.OPERA_BAR_BY_LOS,
                getParameterValue(configParameterNameService.getIntegrationUploadTypeParameterName(externalSystem,
                        Constants.OPERA_BAR_BY_LOS, Constants.UPLOAD_TYPE)),
                Arrays.asList(Constants.DECISION_TYPE_BDE, Constants.DECISION_TYPE_CDP), externalSystem);

        return operaDecisions;
    }

    private List<BarByLOSDecision> getBarByLOSDecisions(Date startDate, Date endDate, Date lastUploadedDate) {

        List<com.ideas.tetris.pacman.services.decisiondelivery.dto.BarByLOSDecision> g3BarByLOSRCDecisionList =
                barDecisionService.getBARByLOSRoomClassDecisions(startDate, endDate, lastUploadedDate);
        return convert(g3BarByLOSRCDecisionList);
    }

    @Override
    public boolean hasBarByLOSDecisions(String externalSystem) {
        Date startDate = dateService.getOptimizationWindowStartDate();
        Date endDate = dateService.getDecisionUploadWindowEndDate();
        Date lastUploadedDate = queryForLastUpdateDateToExternalSystem(Constants.OPERA_BAR_BY_LOS, externalSystem);
        List<com.ideas.tetris.pacman.services.decisiondelivery.dto.BarByLOSDecision> g3BarByLOSRCDecisionList =
                barDecisionService.getBARByLOSRoomClassDecisions(startDate, endDate, lastUploadedDate);

        return g3BarByLOSRCDecisionList != null && !g3BarByLOSRCDecisionList.isEmpty();
    }

    @Override
    public boolean hasBarByLOSByRoomTypeDecisions(String externalSystems) {
        Date startDate = dateService.getOptimizationWindowStartDate();
        Date endDate = dateService.getDecisionUploadWindowEndDate();
        Date lastUploadedDate = queryForLastUpdateDateToExternalSystem(Constants.BAR_DECISION_VALUE_LOS_BY_RT, externalSystems);
        List<com.ideas.tetris.pacman.services.decisiondelivery.dto.BarByLOSDecision> g3BarByLOSByRoomTypeDecisionList =
                barDecisionService.getBARByLOSRoomTypeDecisions(startDate, endDate, lastUploadedDate);

        return g3BarByLOSByRoomTypeDecisionList != null && !g3BarByLOSByRoomTypeDecisionList.isEmpty();
    }


    @SuppressWarnings("unchecked")
	public
    Date queryForLastUpdateDateToExternalSystem(String decisionName, String externalSystem) {
        return queryForLastUpdateDateToExternalSystem(decisionName, decisionName, externalSystem);
    }

    private Date queryForLastUpdateDateToExternalSystem(String decisionName, String decisionType, String externalSystem) {
        try {
            if (isDifferentialDecisionUpload(externalSystem, decisionType)) {
                return decisionUploadDateToExternalSystemRepository.findMaxLastUpdateByDecisionAndExtSystem(decisionName, externalSystem);
            }
        } catch (Exception e) {
            LOGGER.error("ERROR occurred while getting last update date for decision: " + decisionName + " for external system: " + externalSystem, e);
        }
        return null;
    }

    private String getUploadParameterName(String decisionName) {
        String decisionType;
        if (LRA_CONTROL_MINLOS_BY_RATE_CODE.equals(decisionName) || LRA_CONTROL_MINLOS_BY_RATE_CATEGORY.equals(decisionName)) {
            decisionType = LRA_CONTROL_MINLOS;
        } else if (LRA_CONTROL_FPLOS_BY_RATE_CODE.equals(decisionName) || LRA_CONTROL_FPLOS_BY_RATE_CATEGORY.equals(decisionName)) {
            decisionType = LRA_CONTROL_FPLOS;
        } else {
            decisionType = decisionName;
        }
        return decisionType;
    }

    private List<BarByLOSDecision> convert(List<com.ideas.tetris.pacman.services.decisiondelivery.dto.BarByLOSDecision> g3BarByLOSRCDecisionList) {
        List<BarByLOSDecision> results = new ArrayList<>();
        for (com.ideas.tetris.pacman.services.decisiondelivery.dto.BarByLOSDecision g3BarByLOSRTDecision : g3BarByLOSRCDecisionList) {
            BarByLOSDecision operaDecision = new BarByLOSDecision();
            operaDecision.setLos(g3BarByLOSRTDecision.getLos());
            operaDecision.setRateCode(g3BarByLOSRTDecision.getRateCode());
            operaDecision.setRateDate(g3BarByLOSRTDecision.getRateDate());
            results.add(operaDecision);
        }
        return results;
    }


    public List<BarByLOSDecision> getDBarDecisions(
            String propertyId) {
        String externalSystem = getExternalSystem();
        return fetchDBarDecisions(externalSystem);
    }

    public List<BarByLOSDecision> getDBarDecisions(
            String propertyId,
            String externalSystem) {

        return fetchDBarDecisions(externalSystem);
    }

    @Override
    public boolean hasDBarDecisions(String externalSystem) {
        Date startDate = dateService.getOptimizationWindowStartDate();
        Date endDate = dateService.getDecisionUploadWindowEndDate();
        Date lastUploadedDate = queryForLastUpdateDateToExternalSystem(Constants.DBAR, externalSystem);

        List<com.ideas.tetris.pacman.services.decisiondelivery.dto.BarByLOSDecision> g3BarByLOSRCDecisionList =
                barDecisionService.getBARByLOSRoomClassDecisions(startDate, endDate, lastUploadedDate);

        return g3BarByLOSRCDecisionList != null && !g3BarByLOSRCDecisionList.isEmpty();
    }

    @Override
    public List<OperaMinLOSDecision> getLRAMinlosDecisions(String propertyId, String decisionName) {
        String externalSystem = getExternalSystem();
        return getLRAMinlosDecisionsWithExternalSystem(propertyId, externalSystem, decisionName);
    }

    @Override
    public boolean hasLRAMinlosDecisions(String externalSystem, String restrictionType) {
        String decisionName = LRARestrictionType.valueOf(restrictionType) == LRARestrictionType.RATE_CATEGORY_LEVEL ? LRA_CONTROL_MINLOS_BY_RATE_CATEGORY
                : LRA_CONTROL_MINLOS_BY_RATE_CODE;
        String decisionType = getUploadParameterName(decisionName);
        Date lastUploadedDate = queryForLastUpdateDateToExternalSystem(decisionName, decisionType, externalSystem);
        List<DecisionLRAMINLOS> g3Decisions = lraDecisionService.getLRAMinlosDecisions(lastUploadedDate, decisionName, externalSystem);
        return g3Decisions != null && !g3Decisions.isEmpty();
    }

    @Override
    public List<OperaMinLOSDecision> getLRAMinlosDecisionsWithExternalSystem(@FormParam("propertyId") String propertyId, @FormParam("externalSystem") String externalSystem, @FormParam("decisionType") String decisionName) {
        String decisionType = getUploadParameterName(decisionName);
        Date lastUploadedDate = queryForLastUpdateDateToExternalSystem(decisionName, decisionType, externalSystem);
        List<DecisionLRAMINLOS> g3Decisions = lraDecisionService.getLRAMinlosDecisions(lastUploadedDate, decisionName, externalSystem);
        List<OperaMinLOSDecision> operaDecisions = new ArrayList<>();
        for (DecisionLRAMINLOS g3Decision : g3Decisions) {
            OperaMinLOSDecision operaDecision = new OperaMinLOSDecision();
            operaDecision.setArrivalDate(g3Decision.getArrivalDate());
            operaDecision.setMinimumLengthOfStay(BigDecimal.valueOf(g3Decision.getDecision()));
            operaDecision.setRateCode(g3Decision.getRestrictionName());
            operaDecision.setRoomType(g3Decision.getAccomTypeCode());
            operaDecisions.add(operaDecision);
        }

        insertRecordForDecisionDeliveryTracking(decisionName,
                getParameterValue(configParameterNameService.getIntegrationUploadTypeParameterName(externalSystem, LRA_CONTROL_MINLOS, Constants.UPLOAD_TYPE)),
                Collections.singletonList(DECISION_TYPE_LRA_MINLOS), externalSystem);
        return operaDecisions;
    }

    @Override
    public List<OperaFplosQualifiedDecision> getLRAFPLOSByRateCodeDecision(@FormParam("propertyId") String propertyId, @FormParam("decisionType") String decisionName) {
        String externalSystem = getExternalSystem();
        return getLRAFPLOSDecisionWithExternalSystem(propertyId, externalSystem, decisionName);
    }

    @Override
    public boolean hasLRAFplosDecision(String externalSystem, String restrictionType) {
        String decisionName = LRARestrictionType.valueOf(restrictionType) == LRARestrictionType.RATE_CATEGORY_LEVEL ? Constants.LRA_CONTROL_FPLOS_BY_RATE_CATEGORY
                : Constants.LRA_CONTROL_FPLOS_BY_RATE_CODE;
        String decisionType = getUploadParameterName(decisionName);
        Date lastUploadedDate = queryForLastUpdateDateToExternalSystem(decisionName, decisionType, externalSystem);
        List<DecisionLRAFPLOS> g3Decisions = lraDecisionService.getLRAFPLOSDecisions(lastUploadedDate, decisionName, externalSystem);
        return g3Decisions != null && !g3Decisions.isEmpty();
    }

    @Override
    public List<OperaFplosQualifiedDecision> getLRAFPLOSDecisionWithExternalSystem(@FormParam("propertyId") String propertyId, @FormParam("externalSystem") String externalSystem, @FormParam("decisionType") String decisionName) {
        String decisionType = getUploadParameterName(decisionName);
        Date lastUploadedDate = queryForLastUpdateDateToExternalSystem(decisionName, decisionType, externalSystem);
        List<DecisionLRAFPLOS> g3Decisions = lraDecisionService.getLRAFPLOSDecisions(lastUploadedDate, decisionName, externalSystem);
        List<OperaFplosQualifiedDecision> operaDecisions = new ArrayList<>();
        for (DecisionLRAFPLOS g3Decision : g3Decisions) {
            OperaFplosQualifiedDecision operaDecision = new OperaFplosQualifiedDecision();
            operaDecision.setArrivalDate(g3Decision.getArrivalDate());
            operaDecision.setFplos(g3Decision.getDecision());
            operaDecision.setRateCode(g3Decision.getRestrictionName());
            operaDecision.setRoomType(g3Decision.getAccomTypeCode());
            operaDecisions.add(operaDecision);
        }

        insertRecordForDecisionDeliveryTracking(decisionName,
                getParameterValue(configParameterNameService.getIntegrationUploadTypeParameterName(externalSystem, LRA_CONTROL_FPLOS, Constants.UPLOAD_TYPE)),
                Collections.singletonList(DECISION_TYPE_LRA_FPLOS), externalSystem);
        return operaDecisions;
    }

    @Override
    public List<PropertyManualRestrictionDecisionForOpera> getPropertyLevelManualRestrictions(String propertyId,
                                                                                              String externalSystem,
                                                                                              String decisionName) {
        List<PropertyManualRestrictionDecision> g3Decisions
                = manualRestrictionDecisionService.fetchPropertyLevelRestrictionDecisions(decisionName, externalSystem);

        return g3Decisions
                .stream()
                .map(OperaSpecManualRestrictionDecisionConverter::convertToOperaSpec)
                .collect(Collectors.toList());
    }

    @Override
    public List<AccomTypeManualRestrictionDecisionForOpera> getRoomTypeLevelManualRestrictions(String propertyId,
                                                                                               String externalSystem,
                                                                                               String decisionName) {
        List<AccomTypeManualRestrictionDecision> g3Decisions
                = manualRestrictionDecisionService.fetchAccomTypeLevelRestrictionDecisions(decisionName, externalSystem);

        return g3Decisions
                .stream()
                .map(OperaSpecManualRestrictionDecisionConverter::convertToOperaSpec)
                .collect(Collectors.toList());
    }

    private List<BarByLOSDecision> fetchDBarDecisions(String externalSystem) {
        Date startDate = dateService.getOptimizationWindowStartDate();
        Date endDate = dateService.getDecisionUploadWindowEndDate();
        Date lastUploadedDate = queryForLastUpdateDateToExternalSystem(Constants.DBAR, externalSystem);

        List<com.ideas.tetris.pacman.services.decisiondelivery.dto.BarByLOSDecision> g3BarByLOSRCDecisionList =
                barDecisionService.getBARByLOSRoomClassDecisions(startDate, endDate, lastUploadedDate);
        List<BarByLOSDecision> operaDecisions = convert(g3BarByLOSRCDecisionList);

        insertRecordForDecisionDeliveryTracking(Constants.DBAR,
                getParameterValue(configParameterNameService.getIntegrationUploadTypeParameterName(externalSystem, Constants.DBAR, Constants.UPLOAD_TYPE)),
                Arrays.asList(Constants.DECISION_TYPE_BDE, Constants.DECISION_TYPE_CDP), externalSystem);
        return operaDecisions;
    }

    @SuppressWarnings("rawtypes")
    public Double getExchangeRate() {
        if (pacmanConfigParamsService.getBooleanParameterValue(IntegrationConfigParamName.CORE_PROPERTY_APPLY_YIELD_CURRENCY.value())
                && !pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SKIP_LRV_CURRENCY_CONVERSION)) {
            String configuredYC = pacmanConfigParamsService.getParameterValue(GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE.value());
            String baseCurrency = pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_BASE_CURRENCY_CODE.value());
            if (!isYCConversionRequired(configuredYC, baseCurrency)) {
                return 1.0;
            }

            // Get exchange rate from database
            Double exchangeRate = getExchangeRateFromCrud(configuredYC);

            if (exchangeRate == null) {
                // Get exchange rate from NGI
                exchangeRate = getExchangeRateFromRestClient(baseCurrency, configuredYC);
            }

            if (exchangeRate == null) {
                LOGGER.error("It is expected to Apply Yield Currency to decision data, Yield Currency Code configured: " + configuredYC + " is invalid.");
                throw new TetrisException(ErrorCode.OPERA_DECISION_CURRENCY_CONVERSION_FAILED, "ERROR:Error validating Yield Currency Code Configuration for Decisions ");
            }

            return exchangeRate;
        }
        return 1.0;
    }

    private Double getExchangeRateFromCrud(String configuredYC) {
        List exchangeRateList = crudService.findByNativeQuery("select top 1 Exchange_Rate from opera.Stage_Yield_Currency where Currency_Code = :yc order by Begin_DT desc",
                QueryParameter.with("yc", configuredYC).parameters());
        return CollectionUtils.isNotEmpty(exchangeRateList) ? 1.0 / ((BigDecimal) exchangeRateList.get(0)).doubleValue() : null;
    }

    private Double getExchangeRateFromRestClient(String baseCurrency, String configuredYC) {
        Map<String, String> parameters = new HashMap<>();
        parameters.put("date", LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE));
        parameters.put("fromCurrencyCode", baseCurrency);
        parameters.put("toCurrencyCode", configuredYC);

        CurrencyExchange currencyExchange = restClient.getSingleResultFromEndpoint(CURRENCY_EXCHANGE, parameters, new CurrencyExchangeResultsMapper());
        return currencyExchange != null ? 1.0 / currencyExchange.getExchangeRate().doubleValue() : null;
    }

    @Override
    public List<OperaFplosQualifiedDecision> getBarFplosByRoomTypeDecisions(String propertyId) {
        String externalSystem = getExternalSystem();
        return fetchBarFplosByRoomTypeDecisions(externalSystem, Constants.OPERA_BAR_FPLOS_BY_ROOM_TYPE, Constants.DECISION_TYPE_BAR_FPLOS_BY_ROOM_TYPE);

    }

    private String getExternalSystem() {
        return getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value());
    }

    @Override
    public List<OperaFplosQualifiedDecision> getBarFplosByRoomTypeDecisions(@FormParam("propertyId") String propertyId, @FormParam("externalSystem") String externalSystem) {
        return fetchBarFplosByRoomTypeDecisions(externalSystem, Constants.OPERA_BAR_FPLOS_BY_ROOM_TYPE, Constants.DECISION_TYPE_BAR_FPLOS_BY_ROOM_TYPE);
    }

    @Override
    public List<OperaFplosQualifiedDecision> getBarFplosByRoomClassDecisions(@FormParam("propertyId") String propertyId, @FormParam("externalSystem") String externalSystem) {
        return fetchBarFplosByRoomClassDecisions(externalSystem, Constants.OPERA_BAR_FPLOS_BY_ROOM_CLASS, Constants.DECISION_TYPE_BAR_FPLOS_BY_ROOM_CLASS);
    }

    @Override
    public List<OperaFplosQualifiedDecision> getBarFplosByHotelDecisions(@FormParam("propertyId") String propertyId, @FormParam("externalSystem") String externalSystem) {
        List<OperaFplosQualifiedDecision> results = new ArrayList<>();
        String masterClassRoomTypeCode = getMasterClassRoomTypeCode(Integer.valueOf(propertyId));
        List<OperaFplosQualifiedDecision> roomTypeDecisions = fetchBarFplosByRoomTypeDecisions(externalSystem, Constants.OPERA_BAR_FPLOS_BY_HOTEL, Constants.DECISION_TYPE_BAR_FPLOS_BY_ROOM_TYPE);
        for (OperaFplosQualifiedDecision roomTypeDecision : roomTypeDecisions) {
            if (roomTypeDecision.getRoomType().equals(masterClassRoomTypeCode)) {
                results.add(roomTypeDecision);
            }
        }
        return results;
    }

    @Override
    public boolean hasBarFplosByHotelDecisions(String externalSystem) {
        List<FPLOSDecisions> fplosByRoomTypeDecisionData = fplosByRoomTypeDecisionService.getFPLOSByRoomTypeDecisionData
                (dateService.getOptimizationWindowStartDate(), dateService.getDecisionUploadWindowEndDate(),
                        queryForLastUpdateDateToExternalSystem(Constants.OPERA_BAR_FPLOS_BY_HOTEL, externalSystem));
        return fplosByRoomTypeDecisionData != null && !fplosByRoomTypeDecisionData.isEmpty();
    }

    public boolean hasProfitAdjustmentDecisions(String externalSystem) {
         List<ProfitAdjustment> profitAdjustments = getProfitAdjustmentDecisions(externalSystem);
         return !isEmpty(profitAdjustments);
    }

    public List<ProfitAdjustment> getProfitAdjustmentDecisions(String externalSystem) {
        Date lastUploadDate = queryForLastUpdateDateToExternalSystem(Constants.DECISION_DELIVERY_PROFIT_ADJUSTMENT, externalSystem);
        LocalDateTime lastSuccessfulUploadDate = isNull(lastUploadDate) ? null: LocalDateTime.ofInstant(lastUploadDate.toInstant(), ZoneId.systemDefault());

        List<ProfitAdjustment> decisions = profitAdjustmentService.getDifferentialProfitAdjustments(lastSuccessfulUploadDate);

        insertRecordForDecisionDeliveryTracking(
                Constants.DECISION_DELIVERY_PROFIT_ADJUSTMENT,
                getParameterValue(configParameterNameService.getIntegrationUploadTypeParameterName(externalSystem, Constants.DECISION_DELIVERY_PROFIT_ADJUSTMENT, Constants.UPLOAD_TYPE)),
                Arrays.asList(Constants.DECISION_TYPE_BDE, Constants.DECISION_TYPE_CDP),
                externalSystem);
        return decisions;
    }

    private String getMasterClassRoomTypeCode(Integer propertyId) {
        AccomClass masterClass = accommodationService.findMasterClass(propertyId);
        if (masterClass == null) {
            return null;
        }

        Set<AccomType> accomTypes = masterClass.getAccomTypes();
        if (accomTypes == null || accomTypes.isEmpty()) {
            return null;
        }
        return accomTypes.stream().filter(at -> at.getAccomTypeCapacity() > 0).map(AccomType::getAccomTypeCode).findFirst().orElse(null);
    }

    private boolean isYCConversionRequired(String configuredYC, String baseCurrency) {
        return !(configuredYC != null && baseCurrency != null && StringUtils.equalsIgnoreCase(baseCurrency, configuredYC));
    }

    public void setAccommodationService(AccommodationService accommodationService) {
        this.accommodationService = accommodationService;
    }

    public void setFplosByRoomTypeDecisionService(FPLOSByRoomTypeDecisionService fplosByRoomTypeDecisionService) {
        this.fplosByRoomTypeDecisionService = fplosByRoomTypeDecisionService;
    }

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    private String isForceFullUpload(String uploadType) {
        return decisionConfigurationService.shouldSendFullDecisions(PacmanWorkContextHelper.getPropertyId()) ? Constants.FULL : uploadType;
    }
}
