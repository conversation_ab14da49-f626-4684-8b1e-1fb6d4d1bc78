package com.ideas.tetris.pacman.services.informationmanager.notification.querybuilder;

import org.springframework.aop.SpringProxy;
public interface ForecastChangeQueryNotificationsConstants extends SpringProxy {
    String COMMON_SELECTION = new StringBuilder()
            .append("select Property_ID,'NA' as DummyCoulmn ")
            .append(" ,Occupancy_DT ")
            .append(" ,cast(SUBSTRING([1],0,51) as int) as CurrentDec ")
            .append(" ,cast( SUBSTRING([1],52,LEN([1])) as numeric(35,2) ) as COVAL ")
            .append(" ,cast(SUBSTRING([2],0,51) as int)as LastDec ")
            .append(" ,cast( SUBSTRING([2],52,LEN([2])) as numeric(35,2)  ) as LOVAL ")
            .append(" ,cast( SUBSTRING([1],52,<PERSON><PERSON>([1])) as numeric(35,2) ) - cast( SUBSTRING([2],52,<PERSON><PERSON>([2])) as numeric(35,2) ) as diff ")
            .append(" from ( ").toString();

    String COMMON_SELECT_QUERY = new StringBuilder()
            .append("select ")
            .append("Property_ID")
            .append(", Occupancy_DT")
            .append(",pos ")
            .append(" ,right('000000000000000000000000000000000000000000000000000000000000' + ").toString();

    String COMMON_SELECT_FOR_OCCUPANCY = new StringBuilder()
            .append(COMMON_SELECT_QUERY)
            .append(" cast(decision_id as varchar(50)), 50) + '|' + cast( Occupancy as varchar(50)) as Occupancy ")
            .append(" from ( ").toString();

    String COMMON_SELECT_FOR_REVENUE = new StringBuilder()
            .append(COMMON_SELECT_QUERY)
            .append(" cast(decision_id as varchar(50)), 50) + '|' + cast( Revenue as varchar(50)) as Revenue ")
            .append(" from ( ").toString();

    String COMMON_SELECT_FOR_ADR = new StringBuilder()
            .append(COMMON_SELECT_QUERY)
            .append(" cast(decision_id as varchar(50)), 50) + '|' + cast( ADR as varchar(50)) as ADR ")
            .append(" from ( ").toString();

    String COMMON_SELECT_FOR_REV_PAR = new StringBuilder()
            .append(COMMON_SELECT_QUERY)
            .append(" cast(decision_id as varchar(50)), 50) + '|' + cast( RevPar as varchar(50)) as RevPar ")
            .append(" from ( ").toString();

    String COMMON_SELECT_FOR_REV_PAR_LAST_OPT = new StringBuilder()
            .append(COMMON_SELECT_QUERY)
            .append(" cast(decision_id as varchar(50)), 50) + '|' + case when pos = 1 then cast( RevParLatest as varchar(50)) else cast( RevParPrevious as varchar(50)) end as RevPar ")
            .append(" from ( ").toString();

    String QUERY_FOR_PROPERTY = new StringBuilder()
            .append(" select  TA.Property_ID, TA.occupancy_dt, Decision_ID,pos,SUM(TotalOccupancy) as Occupancy,")
            .append(" SUM(TotalRevenue)  as Revenue,case(SUM(TotalOccupancy))    when 0 then 0    else ROUND(SUM(TotalRevenue) / SUM(TotalOccupancy), 2)    end as ADR  ,")
            .append(" (select revpar from dbo.ufn_calculate_revpar(sum(TotalRevenue), sum(ta.Total_Accom_Capacity), sum(ta.Rooms_Not_Avail_Maint + ta.Rooms_Not_Avail_Other), :is_physical_capacity_enabled)) as RevPar ")
            .append(" from")
            .append(" (")
            .append("    select p.decision_id,p.property_id,p.occupancy_dt,business_dt,sum(revenue) as TotalRevenue,SUM(occupancy_Nbr) as TotalOccupancy,")
            .append("    rank() over (PARTITION by p.[Property_ID], p.occupancy_DT order by p.[Property_ID], p.decision_id desc ) as pos")
            .append(" 	 from")
            .append("	 (")
            .append("		select decision_id,dec.property_id,mkt_seg_id,occupancy_dt,business_dt")
            .append("		from	(")
            .append("	select calendar_date as occupancy_dt from calendar_dim where calendar_date between")
            .append("		:startDate and :endDate")
            .append("	) as occ cross join(")
            .append("	select distinct top 2 fcst.property_id,fcst.decision_id,business_dt   from PACE_Mkt_Occupancy_FCST_NOTIFICATION fcst")
            .append(" 	inner join decision on fcst.decision_id = decision.decision_id")
            .append(" where")
            .append(" Decision_Type_ID = 1")
            .append(" and")
            .append(" fcst.property_id= :propertyId")
            .append(" and")
            .append(" fcst.occupancy_dt= ")
            .append("( select max(SnapShot_DT) snapshotDateTime from File_Metadata where Record_Type_ID = 3 and Process_Status_ID = 13 and Property_ID =:propertyId )")
            .append(" order by decision_id desc				")
            .append("	) as dec cross join mkt_seg")
            .append(" where	mkt_seg.property_id=:propertyId ")
            .append("		) y inner join PACE_Mkt_Occupancy_FCST_NOTIFICATION p on p.[Property_ID] = y.[Property_ID] and p.occupancy_dt = y.occupancy_dt")
            .append("       and p.decision_id = y.decision_id and p.mkt_seg_id=y.mkt_seg_id")
            .append("       group by p.[Property_ID], p.occupancy_dt, p.decision_id, business_dt")
            .append(" )r inner join Pace_Total_Activity TA on (TA.Occupancy_DT = r.Occupancy_DT")
            .append(" and TA.Property_ID=r.Property_ID and TA.Business_Day_End_DT = r.Business_DT )")
            .append(" group by ta.Property_ID, ta.occupancy_dt, decision_id, business_dt, pos").toString();

    String QUERY_FOR_PROPERTY_LAST_OPT_REVPAR = new StringBuilder()
            .append(" select  TA.Property_ID, TA.occupancy_dt, Decision_ID,pos,SUM(TotalOccupancy) as Occupancy,")
            .append(" SUM(TotalRevenue)  as Revenue,case(SUM(TotalOccupancy))    when 0 then 0    else ROUND(SUM(TotalRevenue) / SUM(TotalOccupancy), 2)    end as ADR  ,")
            .append(" (select revpar from dbo.ufn_calculate_revpar(sum(TotalRevenue), sum(ta.Total_Accom_Capacity), sum(ta.Rooms_Not_Avail_Maint + ta.Rooms_Not_Avail_Other), :is_physical_capacity_enabled)) as RevParLatest, ")
            .append(" (select revpar from dbo.ufn_calculate_revpar(sum(TotalRevenue), sum(LoTA.Total_Accom_Capacity), sum(LoTA.Rooms_Not_Avail_Maint + LoTA.Rooms_Not_Avail_Other), :is_physical_capacity_enabled)) as RevParPrevious ")
            .append(" from")
            .append(" (")
            .append("    select p.decision_id,p.property_id,p.occupancy_dt,business_dt,sum(revenue) as TotalRevenue,SUM(occupancy_Nbr) as TotalOccupancy,")
            .append("    rank() over (PARTITION by p.[Property_ID], p.occupancy_DT order by p.[Property_ID], p.decision_id desc ) as pos")
            .append(" 	 from")
            .append("	 (")
            .append("		select decision_id,dec.property_id,mkt_seg_id,occupancy_dt,business_dt")
            .append("		from	(")
            .append("	select calendar_date as occupancy_dt from calendar_dim where calendar_date between")
            .append("		:startDate and :endDate")
            .append("	) as occ cross join(")
            .append("	select distinct top 2 fcst.property_id,fcst.decision_id,business_dt   from PACE_Mkt_Occupancy_FCST_NOTIFICATION fcst")
            .append(" 	inner join decision on fcst.decision_id = decision.decision_id")
            .append(" where")
            .append(" fcst.property_id= :propertyId")
            .append(" and")
            .append(" fcst.occupancy_dt= ")
            .append(" (select max(SnapShot_DT) snapshotDateTime from File_Metadata where Record_Type_ID = 3 and Process_Status_ID = 13 and Property_ID =:propertyId )")
            .append(" order by decision_id desc				")
            .append("	) as dec cross join mkt_seg")
            .append(" where	mkt_seg.property_id=:propertyId ")
            .append("		) y inner join PACE_Mkt_Occupancy_FCST_NOTIFICATION p on p.[Property_ID] = y.[Property_ID] and p.occupancy_dt = y.occupancy_dt")
            .append("       and p.decision_id = y.decision_id and p.mkt_seg_id=y.mkt_seg_id")
            .append("       group by p.[Property_ID], p.occupancy_dt, p.decision_id, business_dt")
            .append(" )r inner join Total_Activity TA on (TA.Occupancy_DT = r.Occupancy_DT and TA.Property_ID=r.Property_ID)")
            .append(" inner join Last_Optimization_Total_Activity LoTA on (LoTA.Occupancy_DT = r.Occupancy_DT and LoTA.Property_ID=r.Property_ID ) ")
            .append(" group by ta.Property_ID, ta.occupancy_dt, decision_id, business_dt, pos").toString();

    String QUERY_FOR_PROPERTY_LAST_OPT = new StringBuilder()
            .append(" select  r.Property_ID, r.occupancy_dt, Decision_ID,pos,SUM(TotalOccupancy) as Occupancy,")
            .append(" SUM(TotalRevenue)  as Revenue,case(SUM(TotalOccupancy))    when 0 then 0    else ROUND(SUM(TotalRevenue) / SUM(TotalOccupancy), 2)    end as ADR ,")
            .append(" :is_physical_capacity_enabled as dummyAsRevParNotRequired ")
            .append(" from")
            .append(" (")
            .append("    select p.decision_id,p.property_id,p.occupancy_dt,business_dt,sum(revenue) as TotalRevenue,SUM(occupancy_Nbr) as TotalOccupancy,")
            .append("    rank() over (PARTITION by p.[Property_ID], p.occupancy_DT order by p.[Property_ID], p.decision_id desc ) as pos")
            .append(" 	 from")
            .append("	 (")
            .append("		select decision_id,dec.property_id,mkt_seg_id,occupancy_dt,business_dt")
            .append("		from	(")
            .append("	select calendar_date as occupancy_dt from calendar_dim where calendar_date between")
            .append("		:startDate and :endDate")
            .append("	) as occ cross join(")
            .append("	select distinct top 2 fcst.property_id,fcst.decision_id,business_dt   from PACE_Mkt_Occupancy_FCST_NOTIFICATION fcst")
            .append(" 	inner join decision on fcst.decision_id = decision.decision_id")
            .append(" where")
            .append(" fcst.property_id= :propertyId")
            .append(" and")
            .append(" fcst.occupancy_dt= ")
            .append(" (select max(SnapShot_DT) snapshotDateTime from File_Metadata where Record_Type_ID = 3 and Process_Status_ID = 13 and Property_ID =:propertyId )")
            .append(" order by decision_id desc				")
            .append("	) as dec cross join mkt_seg")
            .append(" where	mkt_seg.property_id=:propertyId ")
            .append("		) y inner join PACE_Mkt_Occupancy_FCST_NOTIFICATION p on p.[Property_ID] = y.[Property_ID] and p.occupancy_dt = y.occupancy_dt")
            .append("       and p.decision_id = y.decision_id and p.mkt_seg_id=y.mkt_seg_id")
            .append("       group by p.[Property_ID], p.occupancy_dt, p.decision_id, business_dt")
            .append(" )r group by r.Property_ID, r.occupancy_dt, decision_id, business_dt, pos").toString();

    String COMMON_PIVOT_PREFIX = new StringBuilder()
            .append(") as XX where XX.pos < 3")
            .append("      )  xxx ")
            .append("   pivot  ")
            .append(" ( ").toString();

    String COMMON_PIVOT_POSTFIX = new StringBuilder()
            .append("      for xxx.pos in ([1],[2]) ")
            .append(" )p ").toString();

    String PIVOT_FOR_OCCUPANCY = new StringBuilder()
            .append(COMMON_PIVOT_PREFIX)
            .append("       max(Occupancy) ")
            .append(COMMON_PIVOT_POSTFIX).toString();

    String PIVOT_FOR_REVENUE = new StringBuilder()
            .append(COMMON_PIVOT_PREFIX)
            .append("       max(Revenue) ")
            .append(COMMON_PIVOT_POSTFIX).toString();

    String PIVOT_FOR_ADR = new StringBuilder()
            .append(COMMON_PIVOT_PREFIX)
            .append("       max(ADR) ")
            .append(COMMON_PIVOT_POSTFIX).toString();

    String PIVOT_FOR_REV_PAR = new StringBuilder()
            .append(COMMON_PIVOT_PREFIX)
            .append("       max(RevPar) ")
            .append(COMMON_PIVOT_POSTFIX).toString();

    String QUERY_FOR_ACCOM_CLASS = new StringBuilder()
            .append("select  ")
            .append(" pof.Property_ID as 'Property_ID' ")
            .append(" ,pof.Occupancy_DT as 'Occupancy_DT' ")
            .append(" ,business_DT  ")
            .append(" ,D.Decision_ID ")
            .append(" ,SUM(Revenue) as Revenue ")
            .append(" ,SUM(occupancy_Nbr) as Occupancy ")
            .append(" ,case(sum(occupancy_nbr))  ")
            .append("  when 0 then 0 ")
            .append("  else ROUND(SUM(revenue) / sum(occupancy_nbr), 2) ")
            .append("  end as ADR ")
            .append(" ,rank() over (PARTITION by pof.[Property_ID], pof.occupancy_DT order by pof.[Property_ID],Business_DT desc ) as pos ")
            .append("                  ,(select revpar from dbo.ufn_calculate_revpar(sum(pof.Revenue), sum(aa.Accom_Capacity), sum(aa.Rooms_Not_Avail_Maint + aa.Rooms_Not_Avail_Other), :is_physical_capacity_enabled)) as RevPar ")
            .append(" from PACE_Accom_Occupancy_FCST_NOTIFICATION pof ")
            .append(" inner join Decision D on (D.Decision_ID = pof.Decision_ID) ")
            .append(" inner join pace_accom_Activity AA on (AA.Occupancy_DT = pof.Occupancy_DT and AA.Accom_Type_ID=pof.Accom_Type_ID and AA.Property_ID = pof.Property_ID and aa.Business_Day_End_DT = d.Business_DT ) ")
            .append(" inner join Accom_Type AT on (AT.Accom_Type_ID = AA.Accom_Type_ID AND AT.Property_ID = AA.Property_ID) ")
            .append(" inner join Accom_Class AC on (AT.Accom_Class_ID=AC.Accom_Class_ID and AT.Property_ID = AC.Property_ID) ")
            .append(" inner join Vw_Active_Room_Class VAC on (VAC.accom_class_ID = AC.Accom_Class_ID) ")
            .append(" where pof.Occupancy_DT between :startDate and :endDate ")
            .append(" and D.Decision_Type_ID = 1 ")
            .append(" and AC.Accom_Class_ID =:inp_accom_class_id ")
            .append(" group by pof.Occupancy_DT,D.Decision_ID,business_DT,pof.Property_ID,AA.Occupancy_DT").toString();

    String QUERY_FOR_ACCOM_CLASS_LAST_OPT_REVPAR = new StringBuilder()
            .append("select  ")
            .append(" pof.Property_ID as 'Property_ID' ")
            .append(" ,pof.Occupancy_DT as 'Occupancy_DT' ")
            .append(" ,business_DT  ")
            .append(" ,D.Decision_ID ")
            .append(" ,SUM(Revenue) as Revenue ")
            .append(" ,SUM(occupancy_Nbr) as Occupancy ")
            .append(" ,case(sum(occupancy_nbr))  ")
            .append("  when 0 then 0 ")
            .append("  else ROUND(SUM(revenue) / sum(occupancy_nbr), 2) ")
            .append("  end as ADR ")
            .append(" ,rank() over (PARTITION by pof.[Property_ID], pof.occupancy_DT order by pof.[Property_ID],D.Decision_ID desc ) as pos ")
            .append(" ,(select revpar from dbo.ufn_calculate_revpar(sum(pof.Revenue), sum(AA.Accom_Capacity), sum(AA.Rooms_Not_Avail_Maint + AA.Rooms_Not_Avail_Other), :is_physical_capacity_enabled)) as RevParLatest ")
            .append(" ,(select revpar from dbo.ufn_calculate_revpar(sum(pof.Revenue), sum(LOAA.Accom_Capacity), sum(LOAA.Rooms_Not_Avail_Maint + LOAA.Rooms_Not_Avail_Other), :is_physical_capacity_enabled)) as RevParPrevious ")
            .append(" from PACE_Accom_Occupancy_FCST_NOTIFICATION pof ")
            .append(" inner join Decision D on (D.Decision_ID = pof.Decision_ID) ")
            .append(" inner join Accom_Activity AA on (AA.Occupancy_DT = pof.Occupancy_DT and AA.Accom_Type_ID=pof.Accom_Type_ID and AA.Property_ID = pof.Property_ID ) ")
            .append(" inner join Last_Optimization_Accom_Activity LOAA on (LOAA.Occupancy_DT = pof.Occupancy_DT and LOAA.Accom_Type_ID=pof.Accom_Type_ID and LOAA.Property_ID = pof.Property_ID ) ")
            .append(" inner join Accom_Type AT on (AT.Accom_Type_ID = AA.Accom_Type_ID AND AT.Property_ID = AA.Property_ID) ")
            .append(" inner join Accom_Class AC on (AT.Accom_Class_ID=AC.Accom_Class_ID and AT.Property_ID = AC.Property_ID) ")
            .append(" inner join Vw_Active_Room_Class VAC on (VAC.accom_class_ID = AC.Accom_Class_ID) ")
            .append(" where pof.Occupancy_DT between :startDate and :endDate ")
            .append(" and AC.Accom_Class_ID =:inp_accom_class_id ")
            .append(" and pof.Decision_ID in (select distinct top 2 Decision_Id from PACE_Accom_Occupancy_FCST_NOTIFICATION order by Decision_ID desc) ")
            .append(" group by pof.Occupancy_DT,D.Decision_ID,business_DT,pof.Property_ID,AA.Occupancy_DT").toString();

    String QUERY_FOR_ACCOM_CLASS_LAST_OPT = new StringBuilder()
            .append("select  ")
            .append(" pof.Property_ID as 'Property_ID' ")
            .append(" ,pof.Occupancy_DT as 'Occupancy_DT' ")
            .append(" ,business_DT  ")
            .append(" ,D.Decision_ID ")
            .append(" ,SUM(Revenue) as Revenue ")
            .append(" ,SUM(occupancy_Nbr) as Occupancy ")
            .append(" ,case(sum(occupancy_nbr))  ")
            .append("  when 0 then 0 ")
            .append("  else ROUND(SUM(revenue) / sum(occupancy_nbr), 2) ")
            .append("  end as ADR ")
            .append(" ,rank() over (PARTITION by pof.[Property_ID], pof.occupancy_DT order by pof.[Property_ID],D.Decision_ID desc ) as pos ")
            .append(" ,:is_physical_capacity_enabled as dummyAsRevParNotRequired ")
            .append(" from PACE_Accom_Occupancy_FCST_NOTIFICATION pof ")
            .append(" inner join Decision D on (D.Decision_ID = pof.Decision_ID) ")
            .append(" inner join Accom_Type AT on (AT.Accom_Type_ID = pof.Accom_Type_ID AND AT.Property_ID = pof.Property_ID) ")
            .append(" inner join Accom_Class AC on (AT.Accom_Class_ID=AC.Accom_Class_ID and AT.Property_ID = AC.Property_ID) ")
            .append(" inner join Vw_Active_Room_Class VAC on (VAC.accom_class_ID = AC.Accom_Class_ID) ")
            .append(" where pof.Occupancy_DT between :startDate and :endDate ")
            .append(" and AC.Accom_Class_ID =:inp_accom_class_id ")
            .append(" and pof.Decision_ID in (select distinct top 2 Decision_Id from PACE_Accom_Occupancy_FCST_NOTIFICATION order by Decision_ID desc) ")
            .append(" group by pof.Occupancy_DT,D.Decision_ID,business_DT,pof.Property_ID,pof.Occupancy_DT").toString();

    String QUERY_FOR_ACCOM_TYPE = new StringBuilder()
            .append("select ")
            .append(" pof.Property_ID as 'Property_ID' ")
            .append(" ,pof.Occupancy_DT as 'Occupancy_DT' ")
            .append(" ,business_DT ")
            .append(" ,D.Decision_ID ")
            .append(" ,SUM(Revenue) as Revenue ")
            .append(" ,SUM(occupancy_Nbr) as Occupancy ")
            .append(" ,case(sum(occupancy_nbr))  ")
            .append("  when 0 then 0  ")
            .append("  else ROUND(SUM(revenue) / sum(occupancy_nbr), 2) ")
            .append("  end as ADR ")
            .append(" ,rank() over (PARTITION by pof.[Property_ID], pof.occupancy_DT order by pof.[Property_ID],Business_DT desc ) as pos ")
            .append(" ,(select revpar from dbo.ufn_calculate_revpar(sum(pof.Revenue), sum(AA.Accom_Capacity), sum(AA.Rooms_Not_Avail_Maint + AA.Rooms_Not_Avail_Other), :is_physical_capacity_enabled)) as RevPar ")
            .append(" from PACE_Accom_Occupancy_FCST_NOTIFICATION pof ")
            .append(" inner join Decision D on (D.Decision_ID = pof.Decision_ID) ")
            .append(" inner join pace_accom_Activity AA on (AA.Occupancy_DT = pof.Occupancy_DT and AA.Accom_Type_ID=pof.Accom_Type_ID and AA.Property_ID = pof.Property_ID and aa.Business_Day_End_DT = d.Business_DT ) ")
            .append(" where pof.Occupancy_DT between :startDate and :endDate ")
            .append(" and D.Decision_Type_ID = 1 ")
            .append(" and pof.Accom_Type_ID =:inp_accom_type_id ")
            .append(" group by pof.Occupancy_DT,D.Decision_ID,business_DT,pof.Property_ID,AA.Occupancy_DT,AA.Accom_Capacity,AA.Rooms_Not_Avail_Other,AA.Rooms_Not_Avail_Maint").toString();

    String QUERY_FOR_ACCOM_TYPE_LAST_OPT_REVPAR = new StringBuilder()
            .append("select ")
            .append(" pof.Property_ID as 'Property_ID' ")
            .append(" ,pof.Occupancy_DT as 'Occupancy_DT' ")
            .append(" ,business_DT ")
            .append(" ,D.Decision_ID ")
            .append(" ,SUM(Revenue) as Revenue ")
            .append(" ,SUM(occupancy_Nbr) as Occupancy ")
            .append(" ,case(sum(occupancy_nbr))  ")
            .append("  when 0 then 0  ")
            .append("  else ROUND(SUM(revenue) / sum(occupancy_nbr), 2) ")
            .append("  end as ADR ")
            .append(" ,rank() over (PARTITION by pof.[Property_ID], pof.occupancy_DT order by pof.[Property_ID],D.Decision_ID desc ) as pos ")
            .append(" ,(select revpar from dbo.ufn_calculate_revpar(sum(pof.Revenue), sum(AA.Accom_Capacity), sum(AA.Rooms_Not_Avail_Maint + AA.Rooms_Not_Avail_Other), :is_physical_capacity_enabled)) as RevParLatest ")
            .append(" ,(select revpar from dbo.ufn_calculate_revpar(sum(pof.Revenue), sum(LOAA.Accom_Capacity), sum(LOAA.Rooms_Not_Avail_Maint + LOAA.Rooms_Not_Avail_Other), :is_physical_capacity_enabled)) as RevParPrevious ")
            .append(" from PACE_Accom_Occupancy_FCST_NOTIFICATION pof ")
            .append(" inner join Decision D on (D.Decision_ID = pof.Decision_ID) ")
            .append(" inner join Accom_Activity AA on (AA.Occupancy_DT = pof.Occupancy_DT and AA.Accom_Type_ID=pof.Accom_Type_ID and AA.Property_ID = pof.Property_ID) ")
            .append(" inner join Last_Optimization_Accom_Activity LOAA on (LOAA.Occupancy_DT = pof.Occupancy_DT and LOAA.Accom_Type_ID=pof.Accom_Type_ID and LOAA.Property_ID = pof.Property_ID) ")
            .append(" where pof.Occupancy_DT between :startDate and :endDate ")
            .append(" and pof.Accom_Type_ID =:inp_accom_type_id ")
            .append(" and pof.Decision_ID in (select distinct top 2 Decision_Id from PACE_Accom_Occupancy_FCST_NOTIFICATION order by Decision_ID desc) ")
            .append(" group by pof.Occupancy_DT,D.Decision_ID,business_DT,pof.Property_ID,AA.Occupancy_DT,AA.Accom_Capacity,AA.Rooms_Not_Avail_Other,AA.Rooms_Not_Avail_Maint").toString();

    String QUERY_FOR_ACCOM_TYPE_LAST_OPT = new StringBuilder()
            .append("select ")
            .append(" pof.Property_ID as 'Property_ID' ")
            .append(" ,pof.Occupancy_DT as 'Occupancy_DT' ")
            .append(" ,business_DT ")
            .append(" ,D.Decision_ID ")
            .append(" ,SUM(Revenue) as Revenue ")
            .append(" ,SUM(occupancy_Nbr) as Occupancy ")
            .append(" ,case(sum(occupancy_nbr))  ")
            .append("  when 0 then 0  ")
            .append("  else ROUND(SUM(revenue) / sum(occupancy_nbr), 2) ")
            .append("  end as ADR ")
            .append(" ,rank() over (PARTITION by pof.[Property_ID], pof.occupancy_DT order by pof.[Property_ID],D.Decision_ID desc ) as pos ")
            .append(" ,:is_physical_capacity_enabled as dummyAsRevParNotRequired ")
            .append(" from PACE_Accom_Occupancy_FCST_NOTIFICATION pof ")
            .append(" inner join Decision D on (D.Decision_ID = pof.Decision_ID) ")
            .append(" where pof.Occupancy_DT between :startDate and :endDate ")
            .append(" and pof.Accom_Type_ID =:inp_accom_type_id ")
            .append(" and pof.Decision_ID in (select distinct top 2 Decision_Id from PACE_Accom_Occupancy_FCST_NOTIFICATION order by Decision_ID desc) ")
            .append(" group by pof.Occupancy_DT,D.Decision_ID,business_DT,pof.Property_ID,pof.Occupancy_DT").toString();


    String QUERY_FOR_BUSINESS_TYPE = new StringBuilder()
            .append("SELECT fcstData.Property_ID AS 'Property_ID', ")
            .append("       fcstData.Occupancy_DT AS 'Occupancy_DT', ")
            .append("       business_DT, ")
            .append("       fcstData.Decision_ID, ")
            .append("       Revenue AS Revenue, ")
            .append("       Occupancy AS Occupancy, ")
            .append("       CASE(Occupancy) ")
            .append("           WHEN 0 THEN 0 ")
            .append("           ELSE ROUND(fcstData.revenue / Occupancy, 2) ")
            .append("       END AS ADR, ")
            .append("       (select revpar from dbo.ufn_calculate_revpar(fcstData.Revenue, ta.Total_Accom_Capacity, ta.Rooms_Not_Avail_Maint + ta.Rooms_Not_Avail_Other, :is_physical_capacity_enabled)) as RevPar, ")
            .append("       pos from ")
            .append("  ( SELECT occupancy_dt, decision_id,Property_ID,Occupancy,Revenue,pos,Business_dt ").append("   FROM ")
            .append("     ( SELECT pmof.occupancy_dt,pmof.Property_ID,pmof.decision_id,Business_dt,pos,sum(Occupancy_Nbr) AS Occupancy,sum(Revenue) AS revenue ").append("      FROM ")
            .append("        ( SELECT Mkt_Seg_id,Occupancy_DT,Decision_id,Property_ID, Business_dt,pos ").append("         FROM ")
            .append("           ( SELECT pof.Mkt_Seg_ID,occupancy_dt,pof.Decision_ID,d.business_dt, pof.Property_ID,rank() over (PARTITION BY pof.Mkt_Seg_ID,occupancy_dt ")
            .append("                                                                                            ORDER BY pof.Mkt_Seg_ID,occupancy_dt,pof.Decision_ID DESC) AS pos ")
            .append("            FROM PACE_Mkt_Occupancy_FCST_NOTIFICATION pof ")
            .append("            INNER JOIN Decision D ON ( D.Decision_ID = pof.Decision_ID ")
            .append("                                      AND pof.Property_ID = d.Property_ID ")
            .append("                                      AND D.Decision_Type_ID = 1 ")
            .append("                                      AND D.Process_Status_ID = 13 ) ")
            .append("            INNER JOIN Mkt_Seg ms ON (ms.Property_ID = pof.Property_ID ")
            .append("                                      AND ms.Mkt_Seg_ID = pof.MKT_SEG_ID ) ")
            .append("            INNER JOIN Mkt_Seg_Details msd ON msd.Mkt_Seg_ID = ms.Mkt_Seg_ID ")
            .append("            WHERE pof.Occupancy_DT BETWEEN :startDate AND :endDate ")
            .append("              AND D.Decision_Type_ID = 1 ")
            .append("              AND Business_Type_ID = :businessTypeId ")
            .append("              AND D.Process_Status_ID = 13 ) AS a ")
            .append("         WHERE pos<=2) AS selectedDecisions ")
            .append("      INNER JOIN PACE_Mkt_Occupancy_FCST_NOTIFICATION pmof ON selectedDecisions.Decision_ID = pmof.Decision_ID " +
                    " AND selectedDecisions.Property_ID = pmof.Property_ID ")
            .append("      AND selectedDecisions.MKT_SEG_ID = pmof.MKT_SEG_ID ")
            .append("      AND selectedDecisions.Occupancy_DT = pmof.Occupancy_DT ")
            .append("      GROUP BY pmof.Property_ID,pmof.occupancy_dt,pmof.Decision_id,Business_DT,pos ) AS innerQueryData ) AS fcstData ")
            .append("INNER JOIN Total_Activity ta ON fcstData.Occupancy_DT = ta.Occupancy_DT ")
            .append("AND fcstData.Property_ID = ta.Property_ID ").toString();

    String QUERY_FOR_BUSINESS_TYPE_LAST_OPT = new StringBuilder()
            .append("SELECT fcstData.Property_ID AS 'Property_ID', ")
            .append("fcstData.Occupancy_DT AS 'Occupancy_DT', ")
            .append("business_DT, ")
            .append("fcstData.Decision_ID, ")
            .append("Revenue AS Revenue, ")
            .append("Occupancy AS Occupancy, ")
            .append("CASE(Occupancy) ")
            .append("    WHEN 0 THEN 0 ")
            .append("    ELSE ROUND(fcstData.revenue / Occupancy, 2) ")
            .append("    END AS ADR, ")
            .append(":is_physical_capacity_enabled as dummyAsRevParNotRequired, ")
            .append("pos from ")
            .append("( SELECT occupancy_dt, decision_id,Property_ID,Occupancy,Revenue,pos,Business_dt ")
            .append(" FROM ")
            .append(" ( SELECT pmof.occupancy_dt,pmof.Property_ID,pmof.decision_id,Business_dt,pos,sum(Occupancy_Nbr) AS Occupancy,sum(Revenue) AS revenue ")
            .append(" FROM ")
            .append(" ( SELECT Mkt_Seg_id,Occupancy_DT,Decision_id,Property_ID, Business_dt,pos ")
            .append("  FROM ")
            .append("  ( SELECT pof.Mkt_Seg_ID,occupancy_dt,pof.Decision_ID,d.business_dt, pof.Property_ID,rank() over (PARTITION BY pof.Mkt_Seg_ID,occupancy_dt ")
            .append("   ORDER BY pof.Mkt_Seg_ID,occupancy_dt,pof.Decision_ID DESC) AS pos ")
            .append("   FROM PACE_Mkt_Occupancy_FCST_NOTIFICATION pof ")
            .append("   INNER JOIN Decision D ON ( D.Decision_ID = pof.Decision_ID AND pof.Property_ID = d.Property_ID AND D.Process_Status_ID = 13 ) ")
            .append("   INNER JOIN Mkt_Seg_Details msd ON msd.Mkt_Seg_ID = pof.Mkt_Seg_ID ")
            .append("   WHERE pof.Occupancy_DT BETWEEN :startDate AND :endDate AND Business_Type_ID = :businessTypeId ")
            .append("  AND pof.Decision_ID in (select distinct top 2 Decision_Id from PACE_Mkt_Occupancy_FCST_NOTIFICATION order by Decision_ID desc)")
            .append("  AND D.Process_Status_ID = 13 ) AS a ")
            .append(" WHERE pos<=2) AS selectedDecisions ")
            .append(" INNER JOIN PACE_Mkt_Occupancy_FCST_NOTIFICATION pmof ON selectedDecisions.Decision_ID = pmof.Decision_ID ")
            .append(" AND selectedDecisions.Property_ID = pmof.Property_ID ")
            .append(" AND selectedDecisions.MKT_SEG_ID = pmof.MKT_SEG_ID ")
            .append(" AND selectedDecisions.Occupancy_DT = pmof.Occupancy_DT ")
            .append(" GROUP BY pmof.Property_ID,pmof.occupancy_dt,pmof.Decision_id,Business_DT,pos ) AS innerQueryData ) AS fcstData ").toString();

    String QUERY_FOR_FORECAST_GROUP = new StringBuilder()
            .append("select ")
            .append(" pof.Property_ID as 'Property_ID' ")
            .append(" ,pof.Occupancy_DT as 'Occupancy_DT' ")
            .append(" ,business_DT  ")
            .append(" ,D.Decision_ID ")
            .append(" ,SUM(Revenue) as Revenue ")
            .append(" ,SUM(occupancy_Nbr) as Occupancy ")
            .append(" ,case(sum(occupancy_nbr))  ")
            .append("  when 0 then 0  ")
            .append("  else ROUND(SUM(revenue) / sum(occupancy_nbr), 2) ")
            .append("  end as ADR ")
            .append(" ,rank() over (PARTITION by pof.[Property_ID], pof.occupancy_DT order by pof.[Property_ID],Business_DT desc ) as pos ")
            .append("        ,(select revpar from dbo.ufn_calculate_revpar(sum(pof.Revenue), sum(ta.Total_Accom_Capacity), sum(ta.Rooms_Not_Avail_Maint + ta.Rooms_Not_Avail_Other), :is_physical_capacity_enabled)) as RevPar ")
            .append("        from PACE_Mkt_Occupancy_FCST_NOTIFICATION pof  ")
            .append(" inner join Decision D on (D.Decision_ID = pof.Decision_ID) ")
            .append(" inner join Total_Activity TA on (TA.Occupancy_DT = pof.Occupancy_DT and TA.Property_ID=pof.Property_ID) ")
            .append(" inner join Mkt_Seg ms on (ms.Property_ID = pof.Property_ID  and ms.Mkt_Seg_ID = pof.MKT_SEG_ID ) ")
            .append(" inner join Mkt_Seg_Forecast_Group msfg on msfg.Mkt_Seg_ID = ms.Mkt_Seg_ID  ")
            .append(" where pof.Occupancy_DT between :startDate and :endDate ")
            .append(" and D.Decision_Type_ID = 1 ")
            .append(" and msfg.Forecast_Group_ID  =:forecastGroupId ")
            .append(" group by pof.Occupancy_DT,D.Decision_ID,business_DT,pof.Property_ID,ta.Occupancy_DT,ta.Total_Accom_Capacity,ta.Rooms_Not_Avail_Other,ta.Rooms_Not_Avail_Maint").toString();

    String QUERY_FOR_MKT_SEG = new StringBuilder()
            .append("select ")
            .append(" pof.Property_ID as 'Property_ID' ")
            .append(" ,pof.Occupancy_DT as 'Occupancy_DT' ")
            .append(" ,business_DT  ")
            .append(" ,D.Decision_ID ")
            .append(" ,SUM(Revenue) as Revenue ")
            .append(" ,SUM(occupancy_Nbr) as Occupancy ")
            .append(" ,case(sum(occupancy_nbr))  ")
            .append("  when 0 then 0  ")
            .append("  else ROUND(SUM(revenue) / sum(occupancy_nbr), 2) ")
            .append("  end as ADR ")
            .append(" ,rank() over (PARTITION by pof.[Property_ID], pof.occupancy_DT order by pof.[Property_ID],Business_DT desc ) as pos ")
            .append("        ,(select revpar from dbo.ufn_calculate_revpar(sum(pof.Revenue), sum(ta.Total_Accom_Capacity), sum(ta.Rooms_Not_Avail_Maint + ta.Rooms_Not_Avail_Other), :is_physical_capacity_enabled)) as RevPar ")
            .append("        from PACE_Mkt_Occupancy_FCST_NOTIFICATION pof  ")
            .append(" inner join Decision D on (D.Decision_ID = pof.Decision_ID) ")
            .append(" inner join Total_Activity TA on (TA.Occupancy_DT = pof.Occupancy_DT and TA.Property_ID=pof.Property_ID) ")
            .append(" inner join Mkt_Seg ms on (ms.Property_ID = pof.Property_ID  and ms.Mkt_Seg_ID = pof.MKT_SEG_ID ) ")
            .append(" where pof.Occupancy_DT between :startDate and :endDate ")
            .append(" and D.Decision_Type_ID = 1 ")
            .append(" and ms.mkt_seg_id  =:mktSegId ")
            .append(" group by pof.Occupancy_DT,D.Decision_ID,business_DT,pof.Property_ID,ta.Occupancy_DT,ta.Total_Accom_Capacity,ta.Rooms_Not_Avail_Other,ta.Rooms_Not_Avail_Maint").toString();

    String QUERY_FOR_BUSINESS_VIEW = new StringBuilder()
            .append("select ")
            .append(" pof.Property_ID as 'Property_ID' ")
            .append(" ,pof.Occupancy_DT as 'Occupancy_DT' ")
            .append(" ,business_DT  ")
            .append(" ,D.Decision_ID ")
            .append(" ,SUM(Revenue) as Revenue ")
            .append(" ,SUM(occupancy_Nbr) as Occupancy ")
            .append(" ,case(sum(occupancy_nbr))  ")
            .append("  when 0 then 0  ")
            .append("  else ROUND(SUM(revenue) / sum(occupancy_nbr), 2) ")
            .append("  end as ADR ")
            .append(" ,rank() over (PARTITION by pof.[Property_ID], pof.occupancy_DT order by pof.[Property_ID],Business_DT desc ) as pos ")
            .append("        ,(select revpar from dbo.ufn_calculate_revpar(sum(pof.Revenue), sum(ta.Total_Accom_Capacity), sum(ta.Rooms_Not_Avail_Maint + ta.Rooms_Not_Avail_Other), :is_physical_capacity_enabled)) as RevPar ")
            .append("        from PACE_Mkt_Occupancy_FCST_NOTIFICATION pof  ")
            .append(" inner join Decision D on (D.Decision_ID = pof.Decision_ID) ")
            .append(" inner join Total_Activity TA on (TA.Occupancy_DT = pof.Occupancy_DT and TA.Property_ID=pof.Property_ID) ")
            .append(" inner join Mkt_Seg ms on (ms.Property_ID = pof.Property_ID  and ms.Mkt_Seg_ID = pof.MKT_SEG_ID ) ")
            .append(" inner join Mkt_Seg_Business_Group msbg on msbg.Mkt_Seg_ID = pof.Mkt_Seg_ID ")
            .append(" where pof.Occupancy_DT between :startDate and :endDate ")
            .append(" and msbg.Business_Group_ID =:inp_Business_Group_id ")
            .append(" and D.Decision_Type_ID = 1 ")
            .append(" group by pof.Occupancy_DT,D.Decision_ID,business_DT,pof.Property_ID,ta.Occupancy_DT,ta.Total_Accom_Capacity,ta.Rooms_Not_Avail_Other,ta.Rooms_Not_Avail_Maint").toString();

    String QUERY_FOR_BUSINESS_VIEW_LAST_OPT = new StringBuilder()
            .append("select ")
            .append(" pof.Property_ID as 'Property_ID' ")
            .append(" ,pof.Occupancy_DT as 'Occupancy_DT' ")
            .append(" ,business_DT  ")
            .append(" ,D.Decision_ID ")
            .append(" ,SUM(Revenue) as Revenue ")
            .append(" ,SUM(occupancy_Nbr) as Occupancy ")
            .append(" ,case(sum(occupancy_nbr))  ")
            .append("  when 0 then 0  ")
            .append("  else ROUND(SUM(revenue) / sum(occupancy_nbr), 2) ")
            .append("  end as ADR ")
            .append(" ,rank() over (PARTITION by pof.[Property_ID], pof.occupancy_DT order by pof.[Property_ID],D.Decision_ID desc ) as pos ")
            .append(" ,:is_physical_capacity_enabled as dummyAsRevParNotRequired ")
            .append("  from PACE_Mkt_Occupancy_FCST_NOTIFICATION pof  ")
            .append(" inner join Decision D on (D.Decision_ID = pof.Decision_ID) ")
            .append(" inner join Mkt_Seg_Business_Group msbg on msbg.Mkt_Seg_ID = pof.Mkt_Seg_ID ")
            .append(" where pof.Occupancy_DT between :startDate and :endDate ")
            .append(" and msbg.Business_Group_ID =:inp_Business_Group_id ")
            .append(" and pof.Decision_ID in (select distinct top 2 Decision_Id from PACE_Mkt_Occupancy_FCST_NOTIFICATION order by Decision_ID desc) ")
            .append(" group by pof.Occupancy_DT,D.Decision_ID,business_DT,pof.Property_ID").toString();

    String QUERY_FOR_FORECAST_GROUP_LAST_OPT = new StringBuilder()
            .append("select ")
            .append(" pof.Property_ID as 'Property_ID' ")
            .append(" ,pof.Occupancy_DT as 'Occupancy_DT' ")
            .append(" ,business_DT  ")
            .append(" ,D.Decision_ID ")
            .append(" ,SUM(Revenue) as Revenue ")
            .append(" ,SUM(occupancy_Nbr) as Occupancy ")
            .append(" ,case(sum(occupancy_nbr))  ")
            .append("  when 0 then 0  ")
            .append("  else ROUND(SUM(revenue) / sum(occupancy_nbr), 2) ")
            .append("  end as ADR ")
            .append(" ,rank() over (PARTITION by pof.[Property_ID], pof.occupancy_DT order by pof.[Property_ID],D.Decision_ID desc ) as pos ")
            .append(" ,:is_physical_capacity_enabled as dummyAsRevParNotRequired ")
            .append("  from PACE_Mkt_Occupancy_FCST_NOTIFICATION pof  ")
            .append(" inner join Decision D on (D.Decision_ID = pof.Decision_ID) ")
            .append(" inner join Mkt_Seg_Forecast_Group msfg on msfg.Mkt_Seg_ID = pof.Mkt_Seg_ID ")
            .append(" where pof.Occupancy_DT between :startDate and :endDate ")
            .append(" and msfg.status_id = 1 and msfg.Forecast_Group_ID =:forecastGroupId ")
            .append(" and pof.Decision_ID in (select distinct top 2 Decision_Id from PACE_Mkt_Occupancy_FCST_NOTIFICATION order by Decision_ID desc) ")
            .append(" group by pof.Occupancy_DT,D.Decision_ID,business_DT,pof.Property_ID").toString();

    String QUERY_FOR_MKT_SEG_LAST_OPT = new StringBuilder()
            .append("select ")
            .append(" pof.Property_ID as 'Property_ID' ")
            .append(" ,pof.Occupancy_DT as 'Occupancy_DT' ")
            .append(" ,business_DT  ")
            .append(" ,D.Decision_ID ")
            .append(" ,SUM(Revenue) as Revenue ")
            .append(" ,SUM(occupancy_Nbr) as Occupancy ")
            .append(" ,case(sum(occupancy_nbr))  ")
            .append("  when 0 then 0  ")
            .append("  else ROUND(SUM(revenue) / sum(occupancy_nbr), 2) ")
            .append("  end as ADR ")
            .append(" ,rank() over (PARTITION by pof.[Property_ID], pof.occupancy_DT order by pof.[Property_ID],D.Decision_ID desc ) as pos ")
            .append(" ,:is_physical_capacity_enabled as dummyAsRevParNotRequired ")
            .append("  from PACE_Mkt_Occupancy_FCST_NOTIFICATION pof  ")
            .append(" inner join Decision D on (D.Decision_ID = pof.Decision_ID) ")
            .append(" inner join Mkt_Seg ms on ms.Mkt_Seg_ID = pof.Mkt_Seg_ID ")
            .append(" where pof.Occupancy_DT between :startDate and :endDate ")
            .append(" and ms.status_id = 1 and ms.Mkt_Seg_ID =:mktSegId ")
            .append(" and pof.Decision_ID in (select distinct top 2 Decision_Id from PACE_Mkt_Occupancy_FCST_NOTIFICATION order by Decision_ID desc) ")
            .append(" group by pof.Occupancy_DT,D.Decision_ID,business_DT,pof.Property_ID").toString();

}
