package com.ideas.tetris.pacman.services.pmsmigration.alert;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.pmsmigration.PMSMigrationUtils;
import com.ideas.tetris.pacman.services.pmsmigration.entity.PMSMigrationConfig;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Transactional
public class PMSMigrationAlertDetailsBuilder {
    public static final String REPLACE_UI_SPECIFIC_PARAMS = "REPLACE_UI_SPECIFIC_PARAMS";
    private static final String POST_BACKFILL_DATA_VALIDATION_REPORT_REST_ENDPOINT = "_HOST_URL_/solutions/rest/pmsMigrationTools/postBackFillDataValidationReport?propertyId=propId";
    private static final String SYSTEM_VALIDATION_RESERVATIONS_COMPARE_REPORT_ENDPOINT = "_HOST_URL_/solutions/rest/pmsMigrationTools/postSystemValidationReservationsReport?propertyId=propId";
    private static final String CONFIG_COMPARE_REPORT_REST_ENDPOINT = "_HOST_URL_/solutions/rest/pmsMigrationTools/newSystemConfigCompareReport?propertyId=propId";
    private static final String RESERVATION_DISCREPANCY_REPORT_REST_ENDPOINT = "_HOST_URL_/solutions/rest/pmsMigrationTools/newSystemReservationsDiscrepancyReport?propertyId=propId";
    private static final String GENERIC_REPORT_URL = "_flowId=viewReportFlow&standAlone=true&ParentFolderUri=/public/Reports&reportUnit=%s&decorate=no";
    private static final String DATA_EXTRACTION_REPORT_UNIT = "/public/Reports/DataExtraction";
    private static final String DATA_EXTRACTION_REPORT_COMMON_PARAMS = "&Comp1=-1&Comp2=-1&Comp3=-1&Comp4=-1&Comp5=-1&Competitor=false&IS_IGNORE_PAGINATION=true&IsADR_BV=false&IsADR_FG=false&IsADR_Hotel=false&IsADR_MS=false&IsADR_RC=false&IsADR_RT=false&IsArr_BV=false&IsArr_FG=false&IsArr_Hotel=true&IsArr_MS=true&IsArr_RC=true&IsArr_RT=false&IsBAR_Hotel=false&IsBAR_RC=false&IsBAR_RT=false&IsBV=false&IsBudgetHotel=false&IsCap_Hotel=true&IsCap_RC=true&IsCap_RT=false&IsCncel_BV=false&IsCncel_FG=false&IsCncel_Hotel=true&IsCncel_MS=true&IsCncel_RC=true&IsCncel_RT=false&IsDep_BV=false&IsDep_FG=false&IsDep_Hotel=true&IsDep_MS=true&IsDep_RC=true&IsDep_RT=false&IsFG=false&IsHotel=true&IsLRV_Hotel=false&IsLRV_RC=false&IsLRV_RT=false&IsMS=true&IsNS_BV=false&IsNS_FG=false&IsNS_Hotel=true&IsNS_MS=true&IsNS_RC=true&IsNS_RT=false&IsOOO_Hotel=true&IsOOO_RC=true&IsOOO_RT=false&IsOcFcst_BV=false&IsOcFcst_FG=false&IsOcFcst_Hotel=false&IsOcFcst_MS=false&IsOcFcst_RC=false&IsOvbk_Hotel=false&IsOvbk_RT=false&IsRC=true&IsRNAO_Hotel=true&IsRNAO_RC=true&IsRNAO_RT=false&IsRS_BV=false&IsRS_FG=false&IsRS_Hotel=true&IsRS_MS=true&IsRS_RC=true&IsRS_RT=false&IsRS_STLY_BV=false&IsRS_STLY_FG=false&IsRS_STLY_Hotel=false&IsRS_STLY_MS=false&IsRS_STLY_RC=false&IsRS_STLY_RT=false&IsRT=false&IsRemainingCapacityHotel=false&IsRemainingCapacityRC=false&IsRemainingCapacityRT=false&IsRevPar_Hotel=false&IsRevPar_RC=false&IsRevPar_RT=false&IsRev_BV=false&IsRev_FG=false&IsRev_Hotel=true&IsRev_MS=true&IsRev_RC=true&IsRev_RT=false&IsRev_STLY_BV=false&IsRev_STLY_FG=false&IsRev_STLY_Hotel=false&IsRev_STLY_MS=false&IsRev_STLY_RC=false&IsRev_STLY_RT=false&IsSplEvt_Hotel=false&IsSysGrpWashPer_FG=false&IsSysUnConDmdTtl_Hotel=false&IsSysUnConDmd_RC=false&IsSysUnDmd_FG=false&IsUserProjected_FG=false&IsUserProjected_Hotel=false&IsUserProjected_MS=false&IsUserUnConDmdTotal_Hotel=false&IsUserUnConDmd_RC=false&IsUserUnDmd_FG=false&includeInactiveRT=0&param_IsShowLastYearDataChecked=false&param_IsWashAtHotelLevel=false&param_Rolling_End_Date=null&param_Rolling_Start_Date=null&param_isRollingDate=0&IsSTR=false&isMarketPerformanceChecked=false";
    private static final String URL_DATE_FORMAT = "yyyyMMdd";
    protected static final int REPORT_DURATION = 364;
    private static final String VOID = "";
    private static final String PROP_ID = "propId";

    public String buildNewSysDataValidationAlertDetails(PMSMigrationConfig pmsMigrationConfig) {
        StringBuilder urls = new StringBuilder()
                .append("<ul>")
                .append(li(buildOldSysNewSysConfigCompareReportUrl(pmsMigrationConfig)))
                .append(li(buildReservationDiscrepancyReportUrl()))
                .append("</ul>");
        return urls.toString();
    }

    private String buildReservationDiscrepancyReportUrl() {
        String url = RESERVATION_DISCREPANCY_REPORT_REST_ENDPOINT
                .replace(PROP_ID, PacmanWorkContextHelper.getPropertyId().toString());
        return href(url, "view.reservation.discrepancy.report");
    }

    private String buildOldSysNewSysConfigCompareReportUrl(PMSMigrationConfig pmsMigrationConfig) {
        if (PMSMigrationUtils.hasConfigChanged(pmsMigrationConfig)) {
            String url = CONFIG_COMPARE_REPORT_REST_ENDPOINT
                    .replace(PROP_ID, PacmanWorkContextHelper.getPropertyId().toString());
            return href(url, "view.config.compare.report");
        }
        return VOID;
    }

    public String buildPostBackfillAlertDetails() {
        String url = POST_BACKFILL_DATA_VALIDATION_REPORT_REST_ENDPOINT
                .replace(PROP_ID, PacmanWorkContextHelper.getPropertyId().toString());
        return href(url, "view.report");
    }

    public String buildSystemDataValidationAlertDetails(Date caughtUpDate, boolean ifBackupExists) {
        DateFormat df = new SimpleDateFormat(URL_DATE_FORMAT);
        String startDateString = df.format(caughtUpDate);
        String endDateString = df.format(DateUtil.addDaysToDate(caughtUpDate, REPORT_DURATION));
        StringBuilder dataExtractionUrl = new StringBuilder(String.format(GENERIC_REPORT_URL, DATA_EXTRACTION_REPORT_UNIT));
        dataExtractionUrl.append(DATA_EXTRACTION_REPORT_COMMON_PARAMS);
        dataExtractionUrl.append(String.format("&StartDate=%s&EndDate=%s", startDateString, endDateString));
        dataExtractionUrl.append(REPLACE_UI_SPECIFIC_PARAMS);
        String reservationsCompareUrl = SYSTEM_VALIDATION_RESERVATIONS_COMPARE_REPORT_ENDPOINT
                .replace(PROP_ID, PacmanWorkContextHelper.getPropertyId().toString());
        StringBuilder hrefUrls = new StringBuilder();
        return hrefUrls.append("<ul>")
                .append(ifBackupExists ? li(href(reservationsCompareUrl, "view.reservations.compare.report")) : "")
                .append(li(href(dataExtractionUrl.toString(), "view.report")))
                .append("</ul>")
                .toString();
    }

    private String href(String url, String urlText) {
        return "<a target=\"_blank\" href=\"" + url + "\">" + urlText + "</a>";
    }

    private String li(String innerHtml) {
        if (VOID.equals(innerHtml)) {
            return VOID;
        }
        return "<li>" + innerHtml + "</li>";
    }
}
