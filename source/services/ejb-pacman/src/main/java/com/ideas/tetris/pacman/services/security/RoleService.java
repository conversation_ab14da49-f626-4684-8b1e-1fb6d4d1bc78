package com.ideas.tetris.pacman.services.security;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.infra.tetris.security.AuthGroupExploder;
import com.ideas.infra.tetris.security.ExplosionException;
import com.ideas.infra.tetris.security.LDAPException;
import com.ideas.infra.tetris.security.domain.*;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.common.workcontext.WorkContextHelper;
import com.ideas.tetris.pacman.services.client.service.ClientAgentConfigService;
import com.ideas.tetris.pacman.services.client.service.ClientService;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.fds.uas.PermissionDefinition;
import com.ideas.tetris.pacman.services.fds.uas.UASService;
import com.ideas.tetris.pacman.services.fds.uas.model.UASAuthGroup;
import com.ideas.tetris.pacman.services.fds.uas.model.UASRole;
import com.ideas.tetris.pacman.services.fds.uis.model.entitlement.EntitlementV2;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.platform.common.contextholder.PlatformThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.TransactionTimestampHelper;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.AuthorizationGroup;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.globalproperty.service.ClientPropertyCacheService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.opends.sdk.EntryNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.naming.InvalidNameException;
import javax.naming.ldap.LdapName;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Transactional
public class RoleService {
    private static final Logger LOGGER = Logger.getLogger(RoleService.class);
    public static final String CLIENT_CODE_KEY = "clientCode";
    public static final String FAILED_TO_GET_ROLES = "Failed to get roles";
    public static final String ROLE_NAME = "roleName";

    public static final String ACCESS = "&access=";
    public static final String PAGE_CODE = "pageCode=";
    public static final String FUNCTIONS = "&functions={";
    public static final String END_OF_FUNCTION_STR = "}";
    public static final String COMMA = ",";
    public static final String COLON = ":";
    public static final String GET_REQUIRED_ROLES_TO_UPDATE_FOR_ALL_CLIENTS =
            "SELECT Role_ID, Role_Name FROM Roles \n" +
                    "WHERE CHARINDEX(:roomsConfig, CAST([Permissions] AS NVARCHAR(MAX))) <> 0 AND CHARINDEX(:minPriceDiff, CAST([Permissions] AS NVARCHAR(MAX))) = 0";
    public static final String GET_REQUIRED_ROLES_TO_UPDATE_FOR_CLIENT =
            "SELECT Role_ID, Role_Name FROM Roles \n" +
                    "WHERE CHARINDEX(:roomsConfig, CAST([Permissions] AS NVARCHAR(MAX))) <> 0 AND CHARINDEX(:minPriceDiff, CAST([Permissions] AS NVARCHAR(MAX))) = 0 AND Client_Code = :clientCode";
    public static final String ROOMS_CONFIG_SUBMODULE_RW_PERMISSION = TetrisPermissionKey.ROOMS_CONFIGURATION_SUBMODULE_PERMISSION.concat(COLON).concat(ActionKey.readWrite.name());
    public static final String MIN_PRICE_DIFF_RW_PERMISSION = TetrisPermissionKey.ROOMS_CONFIGURATION_MIN_PRICE_DIFFERENTIAL.concat(COLON).concat(ActionKey.readWrite.name());
    public static final String MIN_PRICE_DIFF_PERMISSION_KEY = TetrisPermissionKey.ROOMS_CONFIGURATION_MIN_PRICE_DIFFERENTIAL.concat(COLON);
    public static final String ALL_CLIENTS = "-1";
    public static final String FDS_ALL_PERMISSIONS_ROLE_UUID = "fc69a60f-0418-453e-873f-e266ed05d61f";

    @Autowired
	private GlobalRoleCache globalRoleCache;
    @Autowired
	private UserService userService;
    @Autowired
	private UserGlobalDBService userGlobalDBService;
    @GlobalCrudServiceBean.Qualifier
	@Qualifier("globalCrudServiceBean")
    @Autowired
	protected CrudService globalCrudService;
    @Autowired
    JobServiceLocal jobService;
    @Autowired
    ClientService clientService;
    @Autowired
    UASService uasService;

    @Autowired
    ClientPropertyCacheService clientPropertyCacheService;

    private static final Integer ALL_PROP_ID = -666;
    private static final String ATTR_RULE_GRANT = "grant";

    @Autowired
	protected PacmanConfigParamsService pacmanConfigParamsService;

    public void setUserGlobalDBService(UserGlobalDBService userGlobalDBService) {
        this.userGlobalDBService = userGlobalDBService;
    }

    public void setUserService(UserService userService) {
        this.userService = userService;
    }

    public void setClientService(ClientService clientService) {
        this.clientService = clientService;
    }

    public void setJobService(JobServiceLocal jobService) {
        this.jobService = jobService;
    }

    @ForTesting
    public void setUasService(UASService uasService) {
        this.uasService = uasService;
    }

    private String getClient(boolean isInternal) {
        return isInternal ? Constants.CLIENT_INTERNAL : WorkContextHelper.getCurrent().getClientCode();
    }


    public Set<Role> getAllRoles(  boolean isInternal) {
        Set<Role> roles;
        try {
            String clientCode = getClient(isInternal);
            roles = getAllRoles(clientCode);
        } catch (LDAPException le) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, FAILED_TO_GET_ROLES, le);
        }
        return roles;
    }

    public Role getPropertyUserRole(String userId, Integer propertyId) {
        Integer userIdentifier = Integer.valueOf(userId);
        List<UserIndividualPropertyRole> individualPropertyRoles =
                globalCrudService.findByNamedQuery(UserIndividualPropertyRole.FIND_Role_BY_USER_ID_AND_PROPERTY, QueryParameter.with("userId", userIdentifier).and("propertyId", propertyId).parameters());
        if (CollectionUtils.isNotEmpty(individualPropertyRoles)) {
            return getRole(individualPropertyRoles.get(0).getRoleId());
        }
        List<UserAuthGroupRole> authGroupRoles = globalCrudService.findByNamedQuery(UserAuthGroupRole.FIND_ROLE_FOR_USER_ID, QueryParameter.with("userId", userIdentifier).parameters());
        if (CollectionUtils.isNotEmpty(authGroupRoles)) {
            return getRole(authGroupRoles.get(0).getRoleId());
        }
        return null;
    }

    public Role getPropertyUserRoleForAll(String userId, Integer propertyId) {
        Integer userIdentifier = Integer.valueOf(userId);
        List<UserIndividualPropertyRole> individualPropertyRoles =
                globalCrudService.findByNamedQuery(UserIndividualPropertyRole.FIND_Role_BY_USER_ID_AND_PROPERTY, QueryParameter.with("userId", userIdentifier).and("propertyId", propertyId).parameters());
        if (CollectionUtils.isNotEmpty(individualPropertyRoles)) {
            return getRoleForAll(individualPropertyRoles.get(0).getRoleId());
        }
        List<UserAuthGroupRole> authGroupRoles = globalCrudService.findByNamedQuery(UserAuthGroupRole.FIND_ROLE_FOR_USER_ID, QueryParameter.with("userId", userIdentifier).parameters());
        if (CollectionUtils.isNotEmpty(authGroupRoles)) {
            return getRoleForAll(authGroupRoles.get(0).getRoleId());
        }
        return null;
    }

    public List<String> getUserRoles(String userId) {
        List<String> roles = new ArrayList<>();
        try {
            List<UserAuthGroupRole> authGroupRoles = globalCrudService.findByNamedQuery(UserAuthGroupRole.FIND_ROLE_FOR_USER_ID, QueryParameter.with("userId", Integer.valueOf(userId)).parameters());
            roles.addAll(authGroupRoles.stream().map(UserAuthGroupRole::toString).collect(Collectors.toList()));
            List<UserIndividualPropertyRole> individualPropertyRoles = globalCrudService.findByNamedQuery(UserIndividualPropertyRole.FIND_Role_FOR_USER_ID, QueryParameter.with("userId", Integer.valueOf(userId)).parameters());
            roles.addAll(individualPropertyRoles.stream().map(UserIndividualPropertyRole::toString).collect(Collectors.toList()));
        } catch (LDAPException le) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, FAILED_TO_GET_ROLES, le);
        }

        return roles;
    }

    public List<EntitlementV2> getUserRolesForFDS(Integer userId, String upsClientUUID) {
        List<EntitlementV2> roles = new ArrayList<>();
        try {
            LOGGER.info("User migration has been for userId: " + userId);
            List<UserIndividualPropertyRole> individualPropertyRoles = globalCrudService.findByNamedQuery(UserIndividualPropertyRole.FIND_Role_FOR_USER_ID, QueryParameter.with("userId", userId).parameters());
            List<String> individualRoleIds = individualPropertyRoles
                    .stream()
                    .map(UserIndividualPropertyRole::getRoleId)
                    .distinct()
                    .collect(Collectors.toList());

            LOGGER.info("User: " + userId + "\nIndividual property roles: " + individualRoleIds);
            Map<Integer, String> propertyToUUIDMap = individualPropertyRoles
                    .stream()
                    .collect(Collectors.toMap(individualPropertyRole -> individualPropertyRole.getPropertyId(),
                            individualPropertyRole -> {
                                Property property = clientPropertyCacheService.getProperty(individualPropertyRole.getPropertyId());
                                return property.getUpsId();
                            }));

            List<UserAuthGroupRole> authGroupRoles = globalCrudService.findByNamedQuery(
                    UserAuthGroupRole.FIND_ROLE_FOR_USER_ID,
                    QueryParameter.with("userId", userId).parameters());

            //Find the UAS Auth Group UUIDs to associate them with the proper values later
            List<Integer> authGroupIds = authGroupRoles
                    .stream()
                    .filter(authGroup -> authGroup.getAuthGroupId() != -666)
                    .map(UserAuthGroupRole::getAuthGroupId)
                    .collect(Collectors.toList());

            LOGGER.info("User: " + userId + "\nAuth group ids: " + authGroupIds);

            List<AuthorizationGroup> authorizationGroups = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(authGroupIds)) {
                authorizationGroups = globalCrudService.findByNamedQuery(
                        AuthorizationGroup.BY_IDS,
                        QueryParameter.with("ids", authGroupIds).parameters());
            }

            //Grab all auth groups and add the all properties authGroup as that is the only one G3 does not manage locally
            List<UASAuthGroup> authGroupsFromFDS = uasService.getAuthGroupsFromFDSByClient(upsClientUUID);
            LOGGER.info("Auth Group list for user: " + userId + "\nAuth Groups: " + authGroupsFromFDS.stream().map(item -> item.getName()).collect(Collectors.joining(",")));
            UASAuthGroup allPropertiesAuthGroup = authGroupsFromFDS
                    .stream()
                    .filter(authGroup -> authGroup.getName().equals("All Properties"))
                    .findFirst()
                    .orElse(null);

            if (allPropertiesAuthGroup != null) {
                AuthorizationGroup allPropertiesGlobalRoleDto = new AuthorizationGroup();
                allPropertiesGlobalRoleDto.setId(-666);
                allPropertiesGlobalRoleDto.setUasAuthGroupUuid(allPropertiesAuthGroup.getAuthGroupId().toString());
                authorizationGroups.add(allPropertiesGlobalRoleDto);
            }

            Map<Integer, String> authGroupToUUIDMap = authorizationGroups
                    .stream()
                    .collect(Collectors.toMap(authGroup -> authGroup.getId(), authGroup -> authGroup.getUasAuthGroupUuid()));

            //Grab all roleIds associated with Auth Groups and combine with the individual property roles to obtain all GlobalRoles
            List<String> roleIds = authGroupRoles
                    .stream()
                    .map(UserAuthGroupRole::getRoleId)
                    .collect(Collectors.toList());
            individualRoleIds.addAll(roleIds);
            List<String> combinedRoleIds = individualRoleIds
                    .stream()
                    .distinct()
                    .collect(Collectors.toList());

            //Exclude -666 as that will not exist as a data entry in the GlobalRoles table
            List<GlobalRole> globalRoles = combinedRoleIds
                    .stream()
                    .filter(roleId -> !roleId.equals("-666"))
                    .map(roleId -> globalRoleCache.get(Integer.parseInt(roleId)))
                    .collect(Collectors.toList());

            //Map all GlobalRoles we have saved to their respective UASRoleDictionary UUID
            Map<Integer, String> rolesToUUIDMap = globalRoles
                    .stream()
                    .collect(Collectors.toMap(role -> role.getId(), role -> role.getUasRoleDictionaryUuid()));

            LOGGER.info("User: " + userId + "\n rolesMap:" + rolesToUUIDMap);

            boolean allPermissionsForInternalUser = combinedRoleIds
                    .stream()
                    .anyMatch(roleId -> roleId.equals("-666"));
            if (upsClientUUID.equalsIgnoreCase(Constants.CLIENT_INTERNAL_UUID) && allPermissionsForInternalUser) {
                rolesToUUIDMap.put(-666, FDS_ALL_PERMISSIONS_ROLE_UUID);
            }

            rolesToUUIDMap.keySet().forEach(roleId -> {
                //Creating only one UASUserRole entry per role as that is the current restriction
                EntitlementV2 userRole = new EntitlementV2(rolesToUUIDMap.get(roleId));
                LOGGER.info("Building entitlement for role: " + roleId + "\n For userId: " + userId);

                //Take the auth group assigned to the user and add it to the role string if it matches the role's UUID
                authGroupRoles
                        .stream()
                        .filter(authGroupRole -> authGroupRole.getRoleId().equals(roleId.toString()))
                        .forEach(authGroupRole -> userRole.setAuthGroupId(authGroupToUUIDMap.get(authGroupRole.getAuthGroupId())));

                //Loop through individual property roles and populate the list of properties with access for this role
                List<String> currentIndividualPropertyRoles = individualPropertyRoles
                        .stream()
                        .filter(individualPropertyRole -> individualPropertyRole.getRoleId().equals(roleId.toString())) //Only add properties for the current role
                        .map(individualPropertyRole -> propertyToUUIDMap.get(individualPropertyRole.getPropertyId()))
                        .collect(Collectors.toList());
                userRole.setPropertyIds(currentIndividualPropertyRoles);
                roles.add(userRole);
            });
        } catch (LDAPException le) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, FAILED_TO_GET_ROLES, le);
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "An unexpected error has occurred while populating roles", e);
        }

        return roles;
    }

    public List<String> getUserRoleNames(String userId) {
        return globalCrudService.findByNativeQuery("select r.Role_Name from Roles r\n" +
                "inner join User_Auth_Group_Role ua\n" +
                "on r.Role_ID = ua.Role_ID\n" +
                "inner join Users u\n" +
                "on u.User_ID = ua.User_ID\n" +
                "where u.User_ID =:userId", QueryParameter.with("userId", userId).parameters());
    }

    public Set<Role> getAllRoles(String clientCode) {
        Set<Role> roles;
        try {
            roles = getRoleFromGlobal(clientCode);
        } catch (LDAPException le) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, FAILED_TO_GET_ROLES, le);
        }
        filterG3AgentRole(roles);
        return roles;
    }

    public Set<Role> getRoleFromGlobal(String clientCode) {
        List<GlobalRole> globalRoles = globalCrudService.findByNamedQuery(GlobalRole.BY_CLIENT_CODE,
                QueryParameter.with(CLIENT_CODE_KEY, clientCode).parameters());
        return globalRoles.stream().map(GlobalRole::toRole).collect(Collectors.toSet());
    }

    public void clearRolesUasUuid(String clientCode) {
        List<GlobalRole> globalRoles = globalCrudService.findByNamedQuery(GlobalRole.BY_CLIENT_CODE,
                QueryParameter.with(CLIENT_CODE_KEY, clientCode).parameters());

        if (CollectionUtils.isNotEmpty(globalRoles)) {
            globalRoles.stream().forEach(globalRole -> globalRole.setUasRoleDictionaryUuid(null));
            globalCrudService.save(globalRoles);
        }
    }

    public Set<Role> getAllRolesSuppressesEntryNotFoundException(String clientCode) {
        try {
            return getAllRoles(clientCode);
        } catch (TetrisException te) {
            // Not thrilled with this solution - the class that wraps EntryNotFoundException
            // is a final class and re-creating that specific type of exception in a test scenario
            // turned out to be a very difficult task
            Throwable cause = ExceptionUtils.getRootCause(te);
            if (cause instanceof EntryNotFoundException) {
                LOGGER.warn("Encountered EntryNotFoundException during role search", cause);
                return new HashSet<>();
            }

            throw te;
        }
    }



    public Set<Role> getAllAssignableRoles(  boolean isInternal) {
        boolean isCorporate;
        Set<Role> roles;
        try {
            GlobalUser globalUser = globalCrudService.find(GlobalUser.class, Integer.parseInt(PlatformThreadLocalContextHolder.getWorkContext().getUserId()));
            isCorporate = globalUser.isCorporate() || globalUser.getClientCode().equals(Constants.CLIENT_INTERNAL);
            roles = getRoleFromGlobal(getClient(isInternal));
            if (!isCorporate && SystemConfig.hasFeatureCorporateUsers()) {
                filterOutCorporate(roles);
            }
        } catch (LDAPException le) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, "Failed to get assignable roles", le);
        }

        // We do not explode user's perms to check that target roles are a subset
        // If user has role mgmt perm, they can manipulate all perms within roles they can access

        if (isCorporate || !SystemConfig.hasFeatureCorporateUsers()) {
            // Keep for backwards compatibility for a while
            roles.add((isInternal || SystemConfig.hasFeatureRoleAllPermissions()) ?
                    Role.ALL_PERMS_ROLE_ASSIGNABLE :
                    Role.ALL_PERMS_ROLE_UNASSIGNABLE);
        }
        filterG3AgentRole(roles);
        return roles;
    }

    private void filterOutCorporate(Set<Role> roles) {
        CollectionUtils.filter(roles, object -> !((Role) object).isCorporate());
    }

    public Set<Role> search(String searchString, boolean isInternal) {
        Set<Role> roles;
        String client = getClient(isInternal);
        List<GlobalRole> globalRoles = globalCrudService.findByNamedQuery(GlobalRole.BY_CLIENT_AND_NAME_LIKE, QueryParameter.with(CLIENT_CODE_KEY, client).and(ROLE_NAME, searchString).parameters());
        roles = globalRoles.stream().map(GlobalRole::toRole).collect(Collectors.toSet());
        filterG3AgentRole(roles);
        return roles;
    }



    public Role create(Role role,   boolean isInternal) {
        role.setClientCode(getClient(isInternal));
        return createRoleWithProvidedClient(role, isInternal);
    }

    public Role createRoleWithProvidedClient(Role role, boolean isInternal) {
        setPermissionsIfEmpty(role);
        try {
            verifyThatTheRoleDoesNotAlreadyExist(role, role.getClientCode(), true);
            return saveRole(role);
        } catch (LDAPException le) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, "Failed to create role", le);
        }
    }

    public Role createRoleIfNotExists(Role role) {
        setPermissionsIfEmpty(role);
        try {
            Role roleFromDB = findRoleByClientAndName(role.getClientCode(), role.getRoleName());
            return roleFromDB != null ? roleFromDB : saveRole(role);
        } catch (Exception le) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, "Failed to create role", le);
        }
    }

    public void updateRoleRanking(Set<GlobalRole> globalRoleSet) {
        if (CollectionUtils.isNotEmpty(globalRoleSet)) {
            globalCrudService.save(globalRoleSet);
            if (Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName()))) {
                //We need to get the client code from one of the roles. It should be the same for all.
                GlobalRole globalRole = globalRoleSet.stream().findFirst().orElse(null);
                startFDSRolesMigrationJob(globalRole.getClientCode(), false);
            }
        }
    }

    public Role saveRole(Role role) {
        GlobalRole globalRole = new GlobalRole(role, role.getClientCode());
        globalRole = globalCrudService.save(globalRole);
        if ((!globalRole.isInternal() && Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName()))) ||
                (globalRole.isInternal() && Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())))) {
            uasService.saveRoleInFDS(globalRole);
        }
        return globalRole.toRole();
    }

    public void persistRole(Role role) {
        GlobalRole globalRole = new GlobalRole(role, role.getClientCode());
        globalCrudService.save(globalRole);
    }

    private void setPermissionsIfEmpty(Role role) {
        if (role.getPermissions() == null || role.getPermissions().isEmpty()) {
            role.getPermissions().add(Role.NO_PERMISSIONS);
        }
    }

    public Role update(String roleId, Role role) {
        return updateGlobalRole(roleId, role);
    }

    private Role updateGlobalRole(String roleId, Role role) {
        try {
            GlobalRole existingRole = globalCrudService.find(GlobalRole.class, Integer.valueOf(roleId));
            GlobalRole globalRole = new GlobalRole(role, role.getClientCode());
            globalRole.setCreatedByUserId(existingRole.getCreatedByUserId());
            globalRole.setCreateDate(existingRole.getCreateDate());
            globalRole.setLastUpdatedByUserId(existingRole.getLastUpdatedByUserId());
            globalRole.setLastUpdatedDate(existingRole.getLastUpdatedDate());
            boolean isCorporateAccessUpdated = existingRole.isCorporate() != role.isCorporate();
            globalCrudService.save(globalRole);
            if ((!globalRole.isInternal() && Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName()))) ||
                    (globalRole.isInternal() && Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())))) {
                uasService.saveRoleInFDS(globalRole);
            }
            globalRoleCache.remove(globalRole.getId());
            if (isCorporateAccessUpdated) {
                updateIsCorporate(role);
            }
            return globalRole.toRole();
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, "failed to update role", e);
        }
    }

    private void updateIsCorporate(Role role) {
        // If 'isCorporate' changed, we need to blast through users and update isCorporate
        Set<LDAPUser> users = getUsersForRole(role.getUniqueIdentifier());
        LOGGER.info("Role isCorporate changed. Recalculate isCorporate for affected users. Qnty: " +
                users.size());
        for (LDAPUser user : users) {
            // Just updating the user should force isCorporate to be recalculated
            // Do we want to wrap this call and provide different message if fails (similar to delete)
            userService.update(user.getUid(), user);
        }
    }




    public String delete(String roleId) {

        // make sure role exists
        Role role = getRole(roleId);
        if (null == role) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, "Could not find role");
        }

        // update the users that currently have the role
        Set<LDAPUser> users = getUsersForRole(role.getUniqueIdentifier());
        removeRoleIdFromUsers(users, role.getUniqueIdentifier());

        // delete from global and ldap as necessary
        try {
            updateAuditDetailsForRoleDeletion(Integer.parseInt(roleId));
            GlobalRole globalRole = globalCrudService.find(GlobalRole.class, Integer.parseInt(roleId));
            if ((!globalRole.isInternal() && Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName()))) ||
                    (globalRole.isInternal() && Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLES_ENABLED.getParameterName())))) {
                uasService.deleteRoleInFDS(globalRole);
            }
            globalCrudService.delete(globalRole);
            globalRoleCache.remove(Integer.parseInt(roleId));
        } catch (LDAPException e) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, "failed to delete role", e);
        }

        return roleId;
    }

    public void processRoleCreateFromFDS(Client client, String roleID) {
        processRoleCreateFromFDS(client.getUpsClientUuid(), client.getCode(), roleID);
    }

    public void processRoleCreateFromFDS(String upsClientUuid, String clientCode, String roleID) {
        GlobalRole globalRole = getRoleByUasRoleDictionaryUuid(roleID);
        if (globalRole == null) {
            createRoleFromFDS(upsClientUuid, clientCode, roleID);
        } else {
            LOGGER.info("Updating role as it already exists with this UasRoleDictionaryUuid: " + roleID);
            updateGlobalRoleFromFDS(upsClientUuid, clientCode, roleID, globalRole);
        }
    }

    private void createRoleFromFDS(String upsClientUuid, String clientCode, String roleID) {
        UASRole roleFromFDS = uasService.getRoleFromFDS(roleID);

        if (roleFromFDS == null) {
            LOGGER.error("Error creating roleId: " + roleID + " for clientId: " + upsClientUuid + ", as the role doesn't exist in FDS");
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error creating roleId: " + roleID + " for clientId: " + upsClientUuid + ", as the role doesn't exist in FDS");
        }

        if (roleFromFDS.getName() == null || !roleFromFDS.getName().equalsIgnoreCase("ORG_ADMIN")) {
            createRoleFromFDS(clientCode, roleFromFDS);
        }
    }

    private void createRoleFromFDS(String clientCode, UASRole uasRole) {
        Role role = new Role();
        role.setClientCode(clientCode);
        role.setRoleName(uasRole.getName());
        role.setDescription(uasRole.getDescription());
        role.setCorporate(uasRole.isCorporateUser());
        role.setViewAnnouncements(uasRole.isViewAnnouncement());
        role.setPermissions(convertFDSPermissions(uasRole.getPermissions(), uasRole.isInternalUser()));
        role.setRoleRanking(uasRole.getRoleRanking());
        role.setUasRoleDictionaryUuid(uasRole.getRoleId().toString());

        persistRole(role);
    }

    @VisibleForTesting
    protected List<String> convertFDSPermissions(Map<String, Object> permissions, boolean internalUser) {
        List<String> finalPermissions = new ArrayList<>();
        //get the G3, Optix and Navigator permissions; all other products can be ignored
        Map<String, String> g3Permissions = getG3Permissions(permissions, internalUser);
        Map<String, String> universalAdminPermissions = permissions != null ? getPermissionsFromProductEnvironmentId(permissions, SystemConfig.getUniversalAdminApplicationEnvironmentId()) : null;
        Map<String, String> optixPermissions = permissions != null ? getPermissionsFromProductEnvironmentId(permissions, SystemConfig.getOptixApplicationEnvironmentId()) : null;
        Map<String, String> navigatorPermissions = permissions != null ? getPermissionsFromProductEnvironmentId(permissions, SystemConfig.getNavigatorApplicationEnvironmentId()) : null;
        //This will attempt to get special event permissions even if the pacman.fds.specialevents.product.enabled = false
        Map<String, String> specialEventsPermissions = permissions != null ? getPermissionsFromProductEnvironmentId(permissions, SystemConfig.getSpecialEventsApplicationEnvironmentId()) : null;
        //If all the products have no permissions return NO_PERMISSIONS
        if ((g3Permissions == null || g3Permissions.isEmpty()) &&
                (optixPermissions == null || optixPermissions.isEmpty()) &&
                (navigatorPermissions == null || navigatorPermissions.isEmpty()) &&
                (universalAdminPermissions == null || universalAdminPermissions.isEmpty()) &&
                (specialEventsPermissions == null || specialEventsPermissions.isEmpty())) {
            finalPermissions.add(Role.NO_PERMISSIONS);
            return finalPermissions;
        }
        //ALL_PERMISSIONS is tightly coupled to G3 for now
        //we shouldn't expect to only have ALL_PERMISSIONS for Optix or Navigator until we start pulling permissions directly from FDS and don't need this code
        if (g3Permissions != null && g3Permissions.size() == 1) {
            if (g3Permissions.get(Role.ALL_PERMISSIONS) != null) {
                finalPermissions.add(Role.ALL_PERMS_ID);
                return finalPermissions;
            } else if (g3Permissions.get(Role.NO_PERMISSIONS) != null) {
                finalPermissions.add(Role.NO_PERMISSIONS);
                return finalPermissions;
            }
        }
        //get permissions definitions from FDS
        List<PermissionDefinition> allPermissionDefinitions = uasService.getPermissionDefinitions();
        //find all functions and take out of list
        List<PermissionDefinition> functionsPermissionDefinitions = findAllFunctionPermissionDefinitions(allPermissionDefinitions);
        //final all parents of functions and then do those first
        List<PermissionDefinition> parentsOfFunctionsPermissionDefinitions = findAllParentsOfFunctionsPermissionDefinitions(allPermissionDefinitions, functionsPermissionDefinitions);
        //get all permissions definitions that are not in the functions or parentsOfFunctions lists
        List<PermissionDefinition> remainingPermissionDefinitions = new ArrayList<>();
        remainingPermissionDefinitions.addAll(allPermissionDefinitions);
        remainingPermissionDefinitions.removeAll(functionsPermissionDefinitions);
        remainingPermissionDefinitions.removeAll(parentsOfFunctionsPermissionDefinitions);

        //loop over parents of functions, grab functions and create string
        for (PermissionDefinition parentOfFunctionPermissionDefinition : parentsOfFunctionsPermissionDefinitions) {
            //find all functions of the parent
            List<PermissionDefinition> functionsOfParent = functionsPermissionDefinitions.stream().filter(function -> function.getParentId().equals(parentOfFunctionPermissionDefinition.getId())).collect(Collectors.toList());
            //create G3 permission string
            String g3PageCode = createPageCodeString(parentOfFunctionPermissionDefinition, g3Permissions);
            if (g3PageCode != null) {
                String pageCodeWithFunctions = addFunctionsToPageCode(g3PageCode, functionsOfParent, g3Permissions);
                finalPermissions.add(pageCodeWithFunctions);
            }
            //create UAD permission string
            String uadPageCode = createPageCodeString(parentOfFunctionPermissionDefinition, universalAdminPermissions);
            if (uadPageCode != null) {
                String pageCodeWithFunctions = addFunctionsToPageCode(uadPageCode, functionsOfParent, universalAdminPermissions);
                finalPermissions.add(pageCodeWithFunctions);
            }
            //create Optix permission string
            String optixPageCode = createPageCodeString(parentOfFunctionPermissionDefinition, optixPermissions);
            if (optixPageCode != null) {
                String pageCodeWithFunctions = addFunctionsToPageCode(optixPageCode, functionsOfParent, optixPermissions);
                finalPermissions.add(pageCodeWithFunctions);
            }
            //create Navigator permission string
            String navigatorPageCode = createPageCodeString(parentOfFunctionPermissionDefinition, navigatorPermissions);
            if (navigatorPageCode != null) {
                String pageCodeWithFunctions = addFunctionsToPageCode(navigatorPageCode, functionsOfParent, navigatorPermissions);
                finalPermissions.add(pageCodeWithFunctions);
            }
            //create Special Events permission string
            String specialEventsPageCode = createPageCodeString(parentOfFunctionPermissionDefinition, specialEventsPermissions);
            if (specialEventsPageCode != null) {
                String pageCodeWithFunctions = addFunctionsToPageCode(specialEventsPageCode, functionsOfParent, specialEventsPermissions);
                finalPermissions.add(pageCodeWithFunctions);
            }
        }

        //loop over all remaining pages and create string
        for (PermissionDefinition remainingPermissionDefinition : remainingPermissionDefinitions) {
            //create permission string
            String g3PageCode = createPageCodeString(remainingPermissionDefinition, g3Permissions);
            if (g3PageCode != null) {
                finalPermissions.add(g3PageCode);
            }
            String uadPageCode = createPageCodeString(remainingPermissionDefinition, universalAdminPermissions);
            if (uadPageCode != null) {
                finalPermissions.add(uadPageCode);
            }
            String optixPageCode = createPageCodeString(remainingPermissionDefinition, optixPermissions);
            if (optixPageCode != null) {
                finalPermissions.add(optixPageCode);
            }
            String navigatorPageCode = createPageCodeString(remainingPermissionDefinition, navigatorPermissions);
            if (navigatorPageCode != null) {
                finalPermissions.add(navigatorPageCode);
            }
            String specialEventsPageCode = createPageCodeString(remainingPermissionDefinition, specialEventsPermissions);
            if (specialEventsPageCode != null) {
                finalPermissions.add(specialEventsPageCode);
            }
        }

        return finalPermissions;
    }

    private Map<String, String> getG3Permissions(Map<String, Object> permissions, boolean internalUser) {
        if (internalUser) {
            List<String> g3ApplicationEnvironmentIds = Arrays.asList(com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig.getG3ApplicationEnvironmentIds().split(","));
            for (String g3ApplicationEnvironmentId : g3ApplicationEnvironmentIds) {
                if (permissions.containsKey(g3ApplicationEnvironmentId)) {
                    return getPermissionsFromProductEnvironmentId(permissions, g3ApplicationEnvironmentId);
                }
            }
        } else {
            return permissions != null ? getPermissionsFromProductEnvironmentId(permissions, SystemConfig.getG3ApplicationEnvironmentId()) : null;
        }
        return null;
    }

    private Map<String, String> getPermissionsFromProductEnvironmentId(Map<String, Object> permissions, String productEnvironmentId) {
        Map<String, String> appPermissions = (Map<String, String>) permissions.get(productEnvironmentId);
        Map<String, String> finalMap = new HashMap<>();
        if (appPermissions != null) {
            for (Map.Entry<String, String> entry : appPermissions.entrySet()) {
                if (StringUtils.isNotBlank(entry.getValue())) {
                    finalMap.put(entry.getKey(), entry.getValue());
                }
            }
        }
        return finalMap;
    }

    private String createPageCodeString(PermissionDefinition permissionDefinition, Map<String, String> g3Permissions) {
        String accessKey = getPageCodePermissionAccess(permissionDefinition.getId().toString(), g3Permissions);
        if (accessKey.equalsIgnoreCase(ActionKey.noAccess.toString())) {
            return null;
        }
        return PAGE_CODE + permissionDefinition.getPageCode() + ACCESS + accessKey;
    }

    private String addFunctionsToPageCode(String pageCode, List<PermissionDefinition> functions, Map<String, String> g3Permissions) {
        String functionsString = "";
        for (PermissionDefinition function : functions) {
            String accessKey = getPageCodePermissionAccess(function.getId().toString(), g3Permissions);
            if (!accessKey.equalsIgnoreCase(ActionKey.noAccess.toString())) {
                if (functionsString != null && functionsString.length() > 0) {
                    functionsString = functionsString + COMMA + function.getPageCode() + COLON + accessKey;
                } else {
                    functionsString = functionsString + function.getPageCode() + COLON + accessKey;
                }
            }
        }
        //return just the page code if there are no functions that have readOnly or readWrite access
        if (functionsString != null && functionsString.length() > 0) {
            return pageCode + FUNCTIONS + functionsString + END_OF_FUNCTION_STR;
        }
        return pageCode;
    }

    private String getPageCodePermissionAccess(String pageCode, Map<String, String> permissions) {
        String accessKey = permissions != null ? permissions.get(pageCode) : null;
        if (accessKey != null) {
            return accessKey;
        }
        return ActionKey.noAccess.toString();
    }

    private List<PermissionDefinition> findAllFunctionPermissionDefinitions(List<PermissionDefinition> allPermissionDefinitions) {
        return allPermissionDefinitions.stream().filter(permissionDefinition -> permissionDefinition.isFunction()).collect(Collectors.toList());
    }

    private List<PermissionDefinition> findAllParentsOfFunctionsPermissionDefinitions(List<PermissionDefinition> allPermissionDefinitions, List<PermissionDefinition> functionsPermissionDefinitions) {
        //get all the parent UUIDs of the functions
        Set<UUID> parentsOfFunctionsUUIDs = functionsPermissionDefinitions.stream().map(PermissionDefinition::getParentId).collect(Collectors.toSet());
        //find all the Permission Definitions of the parents of functions
        return allPermissionDefinitions.stream().filter(permissionDefinition -> parentsOfFunctionsUUIDs.contains(permissionDefinition.getId())).collect(Collectors.toList());
    }

    public void processRoleUpdateFromFDS(Client client, String roleID) {
        processRoleUpdateFromFDS(client.getUpsClientUuid(), client.getCode(), roleID);
    }

    public void processRoleUpdateFromFDS(String upsClientUuid, String clientCode, String roleID) {
        GlobalRole globalRole = getRoleByUasRoleDictionaryUuid(roleID);
        if (globalRole != null) {
            updateGlobalRoleFromFDS(upsClientUuid, clientCode, roleID, globalRole);
        } else {
            //Create role if it doesn't already exist
            LOGGER.info("Creating role as it does not exist with this UasRoleDictionaryUuid: " + roleID);
            createRoleFromFDS(upsClientUuid, clientCode, roleID);
        }
    }

    public void updateGlobalRolesFromFDS(String upsClientUuid) {
        String clientCode;
        if (upsClientUuid != null && Constants.CLIENT_INTERNAL_UUID.equals(upsClientUuid)) {
            clientCode = Constants.CLIENT_INTERNAL;
        } else {
            Client client = clientService.findClientByUpsClientUuid(upsClientUuid);
            clientCode = client != null ? client.getCode() : null;
        }
        LOGGER.info("Updating G3 Global Roles from FDS for ClientId: " + upsClientUuid + " and ClientCode:" + clientCode);
        if (clientCode != null && upsClientUuid != null) {
            List<UASRole> allUASRoles = uasService.getRolesFromFDS(upsClientUuid);
            for (UASRole uasRole : allUASRoles) {
                String uasRoleDictionaryUuid = uasRole.getRoleId().toString();
                //skip over roles that don't have g3 permissions
                Map<String, String> g3Permissions = getG3Permissions(uasRole.getPermissions(), uasRole.isInternalUser());
                if (g3Permissions != null) {
                    GlobalRole globalRole = getRoleByUasRoleDictionaryUuid(uasRoleDictionaryUuid);
                    if (globalRole == null) {
                        LOGGER.info("Creating role as it does not exist with this UasRoleDictionaryUuid: " + uasRoleDictionaryUuid);
                        createRoleFromFDS(clientCode, uasRole);
                    } else {
                        LOGGER.info("Updating role as it already exists with this UasRoleDictionaryUuid: " + uasRoleDictionaryUuid);
                        updateGlobalRoleFromFDS(clientCode, globalRole, uasRole);
                    }
                }  else {
                    LOGGER.info("Skipping role as it does not have the G3 Product Environment Id defined in the Permissions: " + uasRoleDictionaryUuid);
                }
            }
        } else {
            LOGGER.error("Error updating roles as the client doesn't exist with clientId: " + upsClientUuid);
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error updating roles as the client doesn't exist with clientId: " + upsClientUuid);
        }
    }

    public void updateGlobalRoleFromFDS(String upsClientUuid, String uasRoleDictionaryUuid) {
        String clientCode;
        if (upsClientUuid != null && Constants.CLIENT_INTERNAL_UUID.equals(upsClientUuid)) {
            clientCode = Constants.CLIENT_INTERNAL;
        } else {
            Client client = clientService.findClientByUpsClientUuid(upsClientUuid);
            clientCode = client != null ? client.getCode() : null;
        }

        if (clientCode != null && upsClientUuid != null) {
            GlobalRole globalRole = getRoleByUasRoleDictionaryUuid(uasRoleDictionaryUuid);
            if (globalRole == null) {
                LOGGER.info("Creating role as it does not exist with this UasRoleDictionaryUuid: " + uasRoleDictionaryUuid);
                createRoleFromFDS(upsClientUuid, clientCode, uasRoleDictionaryUuid);
            } else {
                LOGGER.info("Updating role as it already exists with this UasRoleDictionaryUuid: " + uasRoleDictionaryUuid);
                updateGlobalRoleFromFDS(upsClientUuid, clientCode, uasRoleDictionaryUuid, globalRole);
            }
        } else {
            LOGGER.error("Error updating roleId: " + uasRoleDictionaryUuid + ", as the client doesn't exist with clientId: " + upsClientUuid);
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error updating roleId: " + uasRoleDictionaryUuid + ", as the client doesn't exist with clientId: " + upsClientUuid);
        }
    }

    private void updateGlobalRoleFromFDS(String UpsClientUuid, String clientCode, String roleID, GlobalRole globalRole) {
        UASRole roleFromFDS = uasService.getRoleFromFDS(roleID);

        if (roleFromFDS == null) {
            LOGGER.error("Error updating roleId: " + roleID + " for clientId: " + UpsClientUuid + ", as the role doesn't exist in FDS");
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error updating roleId: " + roleID + " for clientId: " + UpsClientUuid + ", as the role doesn't exist in FDS");
        }

        if (roleFromFDS.getName() == null || !roleFromFDS.getName().equalsIgnoreCase("ORG_ADMIN")) {
            updateGlobalRoleFromFDS(clientCode, globalRole, roleFromFDS);
        }
    }

    private void updateGlobalRoleFromFDS(String clientCode, GlobalRole globalRole, UASRole uasRole) {
        globalRole.setClientCode(clientCode);
        globalRole.setRoleName(uasRole.getName());
        globalRole.setDescription(uasRole.getDescription());
        globalRole.setCorporate(uasRole.isCorporateUser());
        globalRole.setViewAnnouncements(uasRole.isViewAnnouncement());
        List<String> permissions = convertFDSPermissions(uasRole.getPermissions(), uasRole.isInternalUser());
        globalRole.setPermissions(CollectionUtils.isEmpty(permissions) ? null : StringUtils.join(permissions, "\n"));
        globalRole.setRoleRanking(uasRole.getRoleRanking());
        globalRole.setUasRoleDictionaryUuid(uasRole.getRoleId().toString());

        globalCrudService.save(globalRole);
    }

    public void processRoleDeleteFromFDS(String roleUuid) {
        GlobalRole globalRole = getRoleByUasRoleDictionaryUuid(roleUuid);
        if (globalRole != null) {
            List<UserIndividualPropertyRole> userIndividualPropertyRoles = globalCrudService.findByNamedQuery(
                    UserIndividualPropertyRole.FIND_ROLES_BY_ROLE_IDS,
                    QueryParameter.with("roleIds", Arrays.asList(globalRole.getId().toString()))
                            .parameters());
            if (CollectionUtils.isNotEmpty(userIndividualPropertyRoles)) {
                globalCrudService.delete(userIndividualPropertyRoles);
            }
            List<UserAuthGroupRole> userAuthGroupRoles = globalCrudService.findByNamedQuery(
                    UserAuthGroupRole.FIND_BY_ROLE_IDS,
                    QueryParameter.with("roleIds", Arrays.asList(globalRole.getId().toString()))
                            .parameters());
            if (CollectionUtils.isNotEmpty(userAuthGroupRoles)) {
                globalCrudService.delete(userAuthGroupRoles);
            }
            globalCrudService.delete(globalRole);
        } else {
            LOGGER.info("Role does not exist with this UasRoleDictionaryUuid: " + roleUuid);
        }
    }

    @VisibleForTesting
    protected GlobalRole getRoleByUasRoleDictionaryUuid(String roleID) {
        return globalCrudService.findByNamedQuerySingleResult(GlobalRole.BY_UAS_UUID,
                QueryParameter.with("roleId", roleID).parameters());
    }

    private void updateAuditDetailsForRoleDeletion(int roleId) {
        GlobalRole existingRole = globalCrudService.find(GlobalRole.class, roleId);
        updateLastUpdatedByUser(existingRole);
        existingRole.setLastUpdatedDate(TransactionTimestampHelper.getCurrentTransactionTimestamp());
        globalCrudService.save(existingRole);
        globalRoleCache.remove(existingRole.getId());
        globalCrudService.flush();
    }

    private void updateLastUpdatedByUser(GlobalRole existingRole) {
        WorkContextType workContext = PlatformThreadLocalContextHolder.getWorkContext();
        Integer userId = 1;

        if (workContext != null && StringUtils.isNumeric(workContext.getUserId())) {
            userId = Integer.valueOf(workContext.getUserId());
        }
        existingRole.setLastUpdatedByUserId(userId);
    }

    public void deleteForClient(String clientCode) {
        LOGGER.info("Deleting all roles for client " + clientCode);
        List<GlobalRole> globalRoles = globalCrudService.findByNamedQuery(GlobalRole.BY_CLIENT_CODE,
                QueryParameter.with(CLIENT_CODE_KEY, clientCode).parameters());
        globalCrudService.delete(globalRoles);
        if (Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLES_ENABLED.getParameterName()))) {
            startFDSRolesMigrationJob(clientCode, true);
        }
        globalRoles.stream().forEach(globalRole -> globalRoleCache.remove(globalRole.getId()));
        LOGGER.info("Deleted " + globalRoles.size() + " roles");
    }

    private Set<LDAPUser> getUsersForRole(String roleId) {
        return userGlobalDBService.listUsersForRole(roleId);
    }

    private void removeRoleIdFromUsers(Set<LDAPUser> users, String roleId) {
        LOGGER.info("Deleting role. Recalculate isCorporate for affected users. Qnty: " + users.size());
        for (LDAPUser user : users) {
            user.removeRole(roleId);
            forceIsCorporateToBeRecalculated(user);
        }
    }

    private void forceIsCorporateToBeRecalculated(LDAPUser user) {
        try {
            // Just updating the user should force isCorporate to be recalculated
            userService.update(user.getUid(), user);
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR,
                    "Error when removing role from client from ldapUser: " + user.getUid(), e);
        }
    }

    private void verifyThatTheRoleDoesNotAlreadyExist(Role role, String client, Boolean isCreating) {
        String errorPrepend = MessageFormat.format("Failed to {0} role. ", isCreating ? "create" : "update");
        if (role.getRoleName().equalsIgnoreCase(Role.ALL_PERMS_ROLE_ASSIGNABLE.getRoleName())) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, errorPrepend + "Role name is reserved.");
        }

        try {
            if (doesRoleExist(client, role, isCreating)) {
                throw new TetrisException(ErrorCode.SECURITY_ERROR, errorPrepend + "It already exists.");
            }
        } catch (LDAPException e) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, errorPrepend + "Query for existing role failed: " + e);
        }
    }

    public boolean doesRoleExist(String client, Role role, boolean isCreating) {
        Role existingRole;
        GlobalRole globalRole = globalCrudService.findByNamedQuerySingleResult(GlobalRole.BY_CLIENT_AND_NAME, QueryParameter.with(CLIENT_CODE_KEY, client).and(ROLE_NAME, role.getRoleName()).parameters());
        existingRole = null != globalRole ? globalRole.toRole() : null;
        Boolean doesRoleExistWhenCreating = isCreating && existingRole != null;
        Boolean doesRoleExistWhenUpdating = !isCreating && existingRole != null &&
                !existingRole.getUniqueIdentifier().equals(role.getUniqueIdentifier());
        return doesRoleExistWhenCreating || doesRoleExistWhenUpdating;
    }

    public Role findRoleByClientAndName(String client, String roleName) {
        GlobalRole globalRole = globalCrudService.findByNamedQuerySingleResult(
                GlobalRole.BY_CLIENT_AND_NAME,
                QueryParameter.with(CLIENT_CODE_KEY, client).and(ROLE_NAME, roleName).parameters());
        return globalRole == null ? null : globalRole.toRole();

    }

    public Role getRoleForAll(String roleId) {
        try {
            GlobalRole globalRole = null;
            if (StringUtils.isNumeric(roleId)) {
                globalRole = globalRoleCache.get(Integer.parseInt(roleId));
            }
            if ("-666".equals(roleId)) {
                Role systemCEORole = findRoleByClientAndName(PacmanWorkContextHelper.getClientCode(), Role.SYSTEM_CEO);
                return  systemCEORole;
            }
            return globalRole == null ? null : globalRole.toRole();
        } catch (LDAPException e) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, "Failed to get role " + roleId, e);
        }
    }

    public Role getRole(String roleId) {
        try {
            GlobalRole globalRole = null;
            if (StringUtils.isNumeric(roleId)) {
                globalRole = globalRoleCache.get(Integer.parseInt(roleId));
            }
            return globalRole == null ? null : globalRole.toRole();
        } catch (LDAPException e) {
            throw new TetrisException(ErrorCode.SECURITY_ERROR, "Failed to get role " + roleId, e);
        }
    }

    public void filterG3AgentRole(Set<Role> allRoles) {
        String userId = PlatformThreadLocalContextHolder.getWorkContext().getUserId();
        if (!NumberUtils.isNumber(userId)) {
            return;
        }

        GlobalUser user = userGlobalDBService.getGlobalUserById(Integer.parseInt(userId));
        if (null != user && !user.isInternal()) {
            CollectionUtils.filter(allRoles, object -> {
                final Role role = (Role) object;
                String roleName = role.getRoleName();
                roleName = roleName.replace(" ", "");
                return !roleName.startsWith(ClientAgentConfigService.AGENT_ROLE);
            });
        }
    }

    public Set<String> getPropertiesForUser(String userDN, AuthGroupExploder exploder) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("getPropertiesForUser(String userDN: " + userDN);
        }

        Set<String> properties = new HashSet<>();
        Map<String, Map<String, List<String>>> rules = getAggregateGrantRevoke(userDN);

        for (String src : rules.keySet()) {
            for (String rule : rules.get(src).get(ATTR_RULE_GRANT)) {
                Map<String, String> rolesByProperty = parseRule(rule, exploder);
                addPropertiesForRoles(rolesByProperty, properties);
            }
        }

        return properties;
    }

    public void addPropertiesForRoles(Map<String, String> rolesByProperty, Set<String> properties) {
        for (String property : rolesByProperty.keySet()) {
            if (property.length() > 0) {
                properties.add(property);
            }
        }
    }

    public Map<String, String> parseRule(String ruleLine, AuthGroupExploder exploder) {
        Map<String, String> rolesByProperty = new HashMap<>();

        // if this is a single rule?
        if (ruleLine.indexOf(' ') == -1) {
            rolesByProperty.put("", ruleLine.split("=")[1]);
            return rolesByProperty;
        }

        // split by whitespace (the regular expression for whitespace is \s)
        String[] tokens = ruleLine.split("\\s");
        if (tokens.length < RoleMapping.ENTRY_PARTS) {
            // ignore it, apparently
            return rolesByProperty;
        }

        String roleId = tokens[RoleMapping.ENTRY_PART_ID].split("=")[1];
        String propOrGroup = tokens[RoleMapping.ENTRY_PART_TYPE];
        String propOrAuthGroupIdentifier = tokens[RoleMapping.ENTRY_PART_TYPE_ID].split("=")[1];

        if (propOrGroup.equalsIgnoreCase(PropertyRoleMapping.TYPE_IDENTIFIER)) {
            rolesByProperty.put(propOrAuthGroupIdentifier, roleId);
        } else if (propOrGroup.equalsIgnoreCase(AuthGroupRoleMapping.TYPE_IDENTIFIER)) {
            explodeAuthGroupForRule(rolesByProperty, propOrAuthGroupIdentifier, roleId, exploder);
        }
        return rolesByProperty;
    }

    private void explodeAuthGroupForRule(Map<String, String> rolesByProperty, String authGroupId, String roleId,
                                         AuthGroupExploder exploder) {
        if (exploder == null) {
            // No way to explode this auth group, give up and return nothing (empty list)
            LOGGER.warn("No auth group exploder for auth group id: " + authGroupId + " and role: " + roleId);
        } else {
            try {
                List<Integer> properties = exploder.getAuthGroupPropertyIDs(Integer.parseInt(authGroupId));
                for (Integer prop : properties) {
                    rolesByProperty.put(Integer.toString(prop), roleId);
                }
            } catch (ExplosionException e) {
                LOGGER.error("Error exploding auth group id: " + authGroupId + ", role: " + roleId, e);
                throw getAuthGroupExlodeException(authGroupId, e);
            }
        }
    }

    public Map<String, Map<String, List<String>>> getAggregateGrantRevoke(String userDN) {
        Map<String, Map<String, List<String>>> rules = new HashMap<>();
        rules.put(userDN, getGrants(userDN));
        return rules;
    }

    public Map<String, List<String>> getGrants(String dn) {
        String[] attributes = new String[]{"tetrisRoleGrant"};
        LDAPUser entry = read(dn, attributes);
        Map<String, List<String>> rules = new HashMap<>();
        if (entry != null) {
            List<String> tetrisRoleGrant = entry.getRoles();
            List<String> grant = new ArrayList<>();
            if (tetrisRoleGrant != null) {
                for (String val : tetrisRoleGrant) {
                    grant.add(val.toString());
                }
            }
            rules.put(ATTR_RULE_GRANT, grant);
        }
        return rules;
    }

    public LDAPUser read(String dn, String[] attributes) {
        LDAPUser user = null;
        try {
            LdapName ldapName = new LdapName(dn);
            String uid = ldapName.get(4).split("=")[1];
            user = userService.getById(Integer.parseInt(uid));
        } catch (InvalidNameException e) {
            LOGGER.error("Exception while reading from LDAP ", e);
        }
        return user;
    }

    public Set<String> getPermsForUser(String userDN, String propertyId, AuthGroupExploder exploder) {
        LDAPUser ldapUser = read(userDN, null);
        List<String> rules = ldapUser.getRoles();
        return getPermissionsFromRules(rules, propertyId, exploder);
    }

    public Set<String> getPermissionsFromRules(List<String> rules, String propertyId, AuthGroupExploder exploder) {
        String propertyRole = parsePropertyRole(rules, propertyId);
        if (propertyRole != null) {
            return new HashSet<>(getRolePermissions(propertyRole));
        }

        String authGroupRole = parseAuthGroupRole(rules, propertyId, exploder);
        if (authGroupRole != null) {
            return new HashSet<>(getRolePermissions(authGroupRole));
        }

        String applicationLevelRole = parseApplicationRole(rules);
        if (applicationLevelRole != null) {
            return new HashSet<>(getRolePermissions(applicationLevelRole));
        }

        return new HashSet<>();
    }

    public List<String> getRolePermissions(String roleId) {
        if (roleId.equals(Role.ALL_PERMS_ID)) {
            List<String> permissions = new ArrayList<>();
            permissions.add(Role.ALL_PERMS_ID);
            return permissions;
        } else {
            return getRole(roleId).getPermissions();
        }
    }

    public String parseApplicationRole(List<String> rules) {
        for (String rule : rules) {
            if (rule.contains(RoleMapping.ENTRY_CLAUSE)) {
                // (an auth group or property role)
                continue;
            }
            return rule.split("=")[1].trim();
        }
        return null;
    }

    public String parsePropertyRole(List<String> rules, String propertyId) {
        for (String rule : rules) {
            if (rule.contains(PropertyRoleMapping.ID_IDENTIFIER)) {
                String id = rule.split(PropertyRoleMapping.ID_IDENTIFIER)[1].trim();

                if (id.equals(propertyId)) {
                    String role = rule.split("=")[1].trim();
                    role = StringUtils.substringBefore(role, " " + RoleMapping.ENTRY_CLAUSE);
                    return role;
                }
            }
        }
        return null;
    }

    public String parseAuthGroupRole(List<String> rules, String propertyId, AuthGroupExploder exploder) {
        // If we had access to AuthorizationGroup, we could loop through and check for "All Properties"
        // as we don't need an exploder for that - just give 'em
        for (String rule : rules) {
            if (rule.contains(AuthGroupRoleMapping.ID_IDENTIFIER)) {
                String authGroupId = rule.split(AuthGroupRoleMapping.ID_IDENTIFIER)[1].trim();
                String roleId = rule.split("=")[1];
                roleId = StringUtils.substringBefore(roleId, " " + RoleMapping.ENTRY_CLAUSE);
                if (authGroupId.equals(ALL_PROP_ID.toString())) {
                    return roleId;
                } else if (exploder != null && doesAuthGroupHaveProperty(exploder, authGroupId, propertyId)) {
                    return roleId;
                }
            }
        }
        return null;
    }

    private boolean doesAuthGroupHaveProperty(AuthGroupExploder exploder, String authGroupId, String propertyId) {
        boolean hasRole = false;
        try {
            // explode! aieeee!
            List<Integer> propertyIds =
                    exploder.getAuthGroupPropertyIDs(Integer.parseInt(authGroupId));
            for (Integer id : propertyIds) {
                if (propertyId.equals(id.toString())) {
                    hasRole = true;
                    break;
                }
            }
        } catch (ExplosionException ee) {
            throw getAuthGroupExlodeException(authGroupId, ee);
        }
        return hasRole;
    }

    private LDAPException getAuthGroupExlodeException(String id, ExplosionException exception) {
        return new LDAPException("Failed to explode authorization group: " + id, exception);
    }

    public void setGlobalCrudService(CrudService globalCrudService) {
        this.globalCrudService = globalCrudService;
    }

    @ForTesting
    public void setPacmanConfigParamsService(PacmanConfigParamsService pacmanConfigParamsService) {
        this.pacmanConfigParamsService = pacmanConfigParamsService;
    }

    public void deleteUsersFromUserAuthGroupRolesForClient(String clientCode) {
        globalCrudService.executeUpdateByNativeQuery("DELETE FROM User_Auth_Group_Role WHERE USER_ID IN (SELECT USER_ID FROM Users Where Client_Code = '" + clientCode + "')");
    }

    public List<GlobalRole> findAllGlobalRoles() {
        return globalCrudService.findAll(GlobalRole.class);
    }

    public void setGlobalRoleCache(GlobalRoleCache globalRoleCache) {
        this.globalRoleCache = globalRoleCache;
    }



    public void setPermissionForMinPriceDiff( String clientCode) {
        List<Object[]> rolesToBeUpdated = getRequiredRolesToUpdate(clientCode);
        if (rolesToBeUpdated == null || rolesToBeUpdated.isEmpty()) {
            LOGGER.warn("No roles updated.");
            return;
        }

        for (Object[] roleObject : rolesToBeUpdated) {
            Role role = getRole(roleObject[0].toString());
            List<String> permissionList = role.getPermissions();
            role.setPermissions(permissionList.stream().map(this::setRWPermissionForMinPriceDiff).collect(Collectors.toList()));
            if (updateGlobalRole(role.getUniqueIdentifier(), role) != null) {
                LOGGER.info("Role: '" + role.getRoleName() + "' updated successfully for client: " + role.getClientCode());
            } else {
                LOGGER.error("Error while updating role: '" + role.getRoleName() + "' for client: " + role.getClientCode());
            }
        }

    }

    private List<Object[]> getRequiredRolesToUpdate(String clientCode) {
        List<Object[]> rolesToBeUpdated;
        if (clientCode != null && clientCode.equals(ALL_CLIENTS)) {
            rolesToBeUpdated = globalCrudService.findByNativeQuery(GET_REQUIRED_ROLES_TO_UPDATE_FOR_ALL_CLIENTS,
                    QueryParameter.with("roomsConfig", ROOMS_CONFIG_SUBMODULE_RW_PERMISSION)
                            .and("minPriceDiff", MIN_PRICE_DIFF_PERMISSION_KEY).parameters());
        } else if (clientCode != null) {
            rolesToBeUpdated = globalCrudService.findByNativeQuery(GET_REQUIRED_ROLES_TO_UPDATE_FOR_CLIENT,
                    QueryParameter.with("roomsConfig", ROOMS_CONFIG_SUBMODULE_RW_PERMISSION).and("minPriceDiff", MIN_PRICE_DIFF_PERMISSION_KEY)
                            .and("clientCode", clientCode).parameters());
        } else {
            LOGGER.error("Client code not supplied. To update for all clients pass -1");
            return Collections.emptyList();
        }
        return rolesToBeUpdated;
    }

    @VisibleForTesting
	public
    String setRWPermissionForMinPriceDiff(String permission) {
        if (permission.contains(TetrisPermissionKey.ROOMS_CONFIGURATION) && permission.contains(ROOMS_CONFIG_SUBMODULE_RW_PERMISSION)) {
            permission = permission.replace(ROOMS_CONFIG_SUBMODULE_RW_PERMISSION, ROOMS_CONFIG_SUBMODULE_RW_PERMISSION.concat(COMMA).concat(MIN_PRICE_DIFF_RW_PERMISSION));
        }
        return permission;
    }

    private void startFDSRolesMigrationJob(String clientCode, boolean deleteAllRoles) {
        if (StringUtils.isEmpty(clientCode)) {
            LOGGER.warn("FDS Roles Migration Job did not start because the client code supplied is empty");
        } else {
            Map<String, Object> jobParameters = new HashMap<String, Object>();
            jobParameters.put(JobParameterKey.DATE, DateUtil.formatDate(DateUtil.getCurrentDate(), DateUtil.DATE_TIME_MILLIS_FORMAT));
            jobParameters.put(JobParameterKey.USER_ID, String.valueOf(PacmanWorkContextHelper.getUserId()));
            jobParameters.put(JobParameterKey.CLIENT_CODE, clientCode);
            Client clientByCode = clientService.getClientByCode(clientCode);
            jobParameters.put(JobParameterKey.CLIENT_ID, clientByCode.getId());
            jobParameters.put("deleteAll", String.valueOf(deleteAllRoles));

            jobService.startJob(JobName.FDSRolesMigrationJob, jobParameters);
        }
    }

    public void userManagementAPISyncToFDS(LDAPUser user) {
        boolean isAPISyncEnabled = Boolean.parseBoolean(System.getProperty("pacman.fds.user.management.api.sync.enabled", "true"));
        if (isAPISyncEnabled) {
            try {
                boolean isRoleAssignmentsEnabledAtClientLevel = !user.isInternal() && Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_ROLE_ASSIGNMENTS_ENABLED.value(), user.getClient()));
                boolean isInternalRolesEnabled = user.isInternal() && Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByGlobalLevel(FeatureTogglesConfigParamName.FDS_INTERNAL_ROLE_ASSIGNMENTS_ENABLED.value()));
                if (isInternalRolesEnabled || isRoleAssignmentsEnabledAtClientLevel) {
                    startFDSRoleAssignmentJob(user, user.getClient());
                }
            } catch (Exception e) {
                LOGGER.error("An error has occurred in starting the role assignment job for user: " + user.getMail(), e);
            }
        } else {
            LOGGER.info("User Management API Sync disabled");
        }
    }

    public void startFDSRoleAssignmentJob(LDAPUser user, String clientCode) {
        if (StringUtils.isEmpty(clientCode)) {
            LOGGER.warn("FDS Roles Migration Job did not start because the client code supplied is empty");
        } else {
            Map<String, Object> jobParameters = new HashMap<String, Object>();
            //Indicate this is updating the singular user
            jobParameters.put(JobParameterKey.CLIENT_CODE, clientCode);
            GlobalUser globalUser = userService.getGlobalUser(Integer.parseInt(user.getUid()), false);
            jobParameters.put("cognitoUserId", globalUser.getCognitoUserId());
            jobService.startGuaranteedNewInstance(JobName.FDSRoleAssignmentsMigrationJob, jobParameters);
        }
    }

    public void migrateAllRolesForClientToFDS(String clientCode) {
        uasService.migrateRolesToFDSForClientCode(clientCode);
    }
}
