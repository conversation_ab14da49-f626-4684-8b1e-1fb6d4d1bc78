package com.ideas.tetris.pacman.services.linkedsrp.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.fplos.RateQualifiedFixedDetailResolver;
import com.ideas.tetris.pacman.services.fplos.RateQualifiedOffsetRateResolver;
import com.ideas.tetris.pacman.services.linkedsrp.dto.LinkedSrpMappingsDto;
import com.ideas.tetris.pacman.services.linkedsrp.repository.LinkedSRPMappingsRepository;
import com.ideas.tetris.pacman.services.linkedsrp.repository.RateQualifiedFixedRepository;
import com.ideas.tetris.pacman.services.linkedsrp.repository.SemiYieldableRateDetailsRepository;
import com.ideas.tetris.pacman.services.linkedsrp.util.LinkedSRPRateCalculationForStreamServiceUtil;
import com.ideas.tetris.pacman.services.qualifiedrate.dto.RateQualifiedEntityDto;
import com.ideas.tetris.pacman.services.qualifiedrate.dto.RateQualifiedFixedDetailsBatchDto;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualified;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualifiedAdjustment;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualifiedDetails;
import com.ideas.tetris.pacman.services.qualifiedrate.repository.RateQualifiedFixedDetailsStageRepository;
import com.ideas.tetris.pacman.services.qualifiedrate.service.RateQualifiedAdjustmentService;
import com.ideas.tetris.pacman.services.qualifiedrate.service.RateQualifiedService;
import com.ideas.tetris.pacman.services.ratepopulation.RateDetails;
import com.ideas.tetris.pacman.services.ratepopulation.entity.RateQualifiedFixed;
import com.ideas.tetris.pacman.services.ratepopulation.entity.RateQualifiedFixedDetails;
import com.ideas.tetris.pacman.services.ratepopulation.entity.RateQualifiedFixedDetailsStg;
import com.ideas.tetris.pacman.util.CollectionUtils;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import lombok.extern.log4j.Log4j;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.linkedsrp.service.LinkedSrpForStreamingRateResolver.LV0;

@Log4j
@Component
public class LinkedSRPRateCalculationForStreamService {

    private static final Logger LOGGER = Logger.getLogger(LinkedSRPRateCalculationForStreamService.class);
    private static final String MESSAGE_FAILING_BECAUSE_OF_OVERLAPPING_SEASONS_FOUND_IN_BELOW_DATA = "Failing because of overlapping seasons found in below data:- ";
    static final int INSERT_CHUNK_SIZE = 10000;
    @Autowired
    private RateQualifiedService rateQualifiedService;

    @Autowired
	private RateQualifiedFixedRepository repository;

    @Autowired
	private DateService dateService;

    @Autowired
	private LinkedSRPLv0RateCalculationForStreamService lv0Service;

    @Autowired
	private PacmanConfigParamsService configParamsService;

    @Autowired
    RateQualifiedFixedDetailsStageRepository rateQualifiedFixedDetailsStageRepository;
    @Autowired
    LinkedSRPMappingsRepository linkedSRPMappingsRepository;
    @Autowired
    RateQualifiedAdjustmentService rateQualifiedAdjustmentService;
    @Autowired
    SemiYieldableRateDetailsRepository semiYieldableRateDetailsRepository;

    @TenantCrudServiceBean.Qualifier
    @Autowired
    private CrudService crudService;

    public Map<String, RateQualifiedEntityDto> loadRateQualifiedData(Date startDate, Date endDate,List<String> rateCodes) {
        return rateCodes.isEmpty()?rateQualifiedService.getActiveRateQualifiedWithDetails(startDate, endDate)
                :rateQualifiedService.getActiveRateQualifiedWithDetailsByRateNames(startDate, endDate, rateCodes);
    }

    public Map<String, RateQualifiedEntityDto> loadRateQualifiedDataWithLv0Rates() {
        Date startDate = dateService.getOptimizationWindowStartDate();
        Date endDate = dateService.getOptimizationWindowEndDateBDE();
        return loadRateQualifiedDataWithLv0RatesForGivenRates(List.of(), startDate, endDate);
    }

    private Map<String, RateQualifiedEntityDto> loadRateQualifiedDataWithLv0RatesForGivenRates(List<String> rateCodes, Date startDate, Date endDate) {
        Map<String, RateQualifiedEntityDto> activeRateQualifiedWithDetails = loadRateQualifiedData(startDate, endDate,rateCodes);
        failIfOverLappingSeason(activeRateQualifiedWithDetails.values());
        activeRateQualifiedWithDetails.computeIfPresent(LV0, (key, lv0) -> {
            List<RateDetails> lv0Rates = getLv0Rates(DateUtil.convertJavaUtilDateToLocalDate(startDate), DateUtil.convertJavaUtilDateToLocalDate(endDate));
            return LinkedSRPRateCalculationForStreamServiceUtil.buildRateQualifiedForLv0(activeRateQualifiedWithDetails.get(LV0), lv0Rates);
        });
        return activeRateQualifiedWithDetails;
    }

    public List<RateDetails> getLv0Rates(LocalDate startDate, LocalDate endDate) {
        return lv0Service.buildLv0RatesFromCPDecisionBarOutput(startDate, endDate);
    }

    public List<RateDetails> getRatesBySrpName(String srpName, Map<String, RateQualifiedEntityDto> rateQualifiedMap) {
        LinkedSrpForStreamingRateResolver resolver = getLinkedSrpForStreamingRateResolver(rateQualifiedMap);
        return resolver.getRatesBySrpName(srpName);
    }

    private LinkedSrpForStreamingRateResolver getLinkedSrpForStreamingRateResolver(Map<String, RateQualifiedEntityDto> rateQualifiedMap) {
        LinkedSrpForStreamingRateResolver resolver;
        if (isHiltonYieldAsRefinementEnabled()) {
            LocalDate startDate = dateService.getCaughtUpJavaLocalDate();
            LocalDate endDate = getOptimizationWindowEndDateFromCaughtUpDate(startDate);
            Map<String, List<RateDetails>> semiYieldRateCodeToDetails = semiYieldableRateDetailsRepository.fetchActiveSemiYieldableRatesByDateRange(startDate, endDate, new ArrayList<>(rateQualifiedMap.keySet()));
            resolver = new LinkedSrpForStreamingRateResolver(rateQualifiedMap, new HashMap<>(), semiYieldRateCodeToDetails, true);
        } else {
            resolver = new LinkedSrpForStreamingRateResolver(rateQualifiedMap, false);
        }
        return resolver;
    }

    public Map<String, List<RateDetails>> getAllRates(Map<String, RateQualifiedEntityDto> rateQualifiedMap) {
        LinkedSrpForStreamingRateResolver resolver = getLinkedSrpForStreamingRateResolver(rateQualifiedMap);
        return resolver.getAllRates();
    }

    public Map<String,List<RateDetails>> getAllRatesUsingExistingRates(Map<String, RateQualifiedEntityDto> rateQualifiedMap,Map<String, List<RateDetails>> calculatedRates, Map<String, List<RateDetails>> semiYieldRateCodeToDetails) {
        LinkedSrpForStreamingRateResolver resolver = new LinkedSrpForStreamingRateResolver(rateQualifiedMap, calculatedRates, semiYieldRateCodeToDetails, isHiltonYieldAsRefinementEnabled());
        return resolver.getAllRates();
    }

    private LocalDate getOptimizationWindowEndDateFromCaughtUpDate(LocalDate startDate) {
        Integer optimizationWindow = configParamsService.getParameterValue(IPConfigParamName.OPTIMIZATION_OPTIMIZATION_WINDOW_BDE);
        return startDate.plusDays(optimizationWindow - 1L);
    }

    public List<RateQualifiedFixed> buildRates(Map<String, RateQualifiedEntityDto> rateQualifiedMap) {
        Map<String, List<RateDetails>> calculatedRates = getAllRates(rateQualifiedMap);
        List<RateQualifiedFixed> fixedRates = buildRateQualifiedFixedEntitiesFromDtos(rateQualifiedMap,calculatedRates);
        deleteRateQualifiedFixedData();
        return fixedRates;
    }

    public void deleteRateQualifiedFixedData() {
        repository.deleteAll();
    }

    public List<RateQualifiedFixed> buildRatesForGivenRatesHeaders(Map<String, RateQualifiedEntityDto> rateQualifiedMap,Map<String, List<RateDetails>> calculatedExistingRates, Map<String, List<RateDetails>> semiYieldRateCodeToDetails) {
        Map<String, List<RateDetails>> calculatedRates = getAllRatesUsingExistingRates(rateQualifiedMap,calculatedExistingRates, semiYieldRateCodeToDetails);
        return buildRateQualifiedFixedEntitiesFromDtos(rateQualifiedMap,calculatedRates);
    }

    public List<RateQualifiedFixedDetails> buildRatesAndSaveRateQualifiedFixed(Map<String, RateQualifiedEntityDto> rateQualifiedMap) {
        List<RateQualifiedFixed> fixedRates = buildRates(rateQualifiedMap);
        return saveHeaderAndGetDetails(fixedRates);
    }


    public int buildRatesAndSaveRateQualifiedFixedForGivenRates(List<String> rateCodes, Date startDate, Date endDate) {

        if (rateCodes.isEmpty()) {
            LOGGER.info("No rate codes provided for processing in the batch");
            return 0;
        }
        LOGGER.debug("Processing " + rateCodes.size() + " rate codes.");

        LOGGER.debug("Rate codes: " + String.join(", ", rateCodes));

        Map<String, RateQualifiedEntityDto> rateQualifiedMap = loadRateQualifiedDataWithLv0RatesForGivenRates(rateCodes, startDate, endDate);
        if (rateQualifiedMap.isEmpty()) {
            LOGGER.info("No rate details present for the given rate codes.");
            return 0;
        }

        // Fetch  already created rates
        List<RateQualifiedFixedDetailsStg> existingRateCodes = rateQualifiedFixedDetailsStageRepository.findRateQualifiedDetailsByRateCodeNames(rateCodes);
        // Fetch RateQualified data

        Map<String, List<RateDetails>> calculatedExistingRates = buildRateDetailsForExistingRates(existingRateCodes, rateQualifiedMap);
        calculatedExistingRates.remove(LV0); // Remove LV0 since its rates come from CP_DECISION_BAR_OUTPUT table


        Map<String, List<RateDetails>> semiYieldRateCodeToDetails = isHiltonYieldAsRefinementEnabled() ?
                semiYieldableRateDetailsRepository.fetchActiveSemiYieldableRatesByDateRange(
                    DateUtil.convertJavaUtilDateToLocalDate(startDate), DateUtil.convertJavaUtilDateToLocalDate(endDate), rateCodes) : Map.of();

        List<RateQualifiedFixed> fixedRates = buildRatesForGivenRatesHeaders(rateQualifiedMap, calculatedExistingRates, semiYieldRateCodeToDetails);

        //Remove rates which are already present in the existing rates
        Set<Integer> existingRateIds = existingRateCodes.stream()
                .map(RateQualifiedFixedDetailsStg::getRateQualifiedId)
                .collect(Collectors.toSet());

        List<RateQualifiedFixed> newRates = fixedRates.stream()
                .filter(rateQualifiedFixed -> !existingRateIds.contains(rateQualifiedFixed.getRateQualifiedId()))
                .collect(Collectors.toList());

        if(newRates.isEmpty()){
            LOGGER.info("No new rates to save for the given rate codes");
            return 0;
        }

        List<RateQualifiedFixedDetails> rateQualifiedFixedDetails = saveHeaderAndGetDetails(newRates);

        saveRateDetailsInChunk(rateQualifiedFixedDetails);
        LOGGER.debug("Saved " + rateQualifiedFixedDetails.size() + " new rate qualified fixed details stg records.");
        LOGGER.debug("New rates saved for rate codes: " +
                newRates.stream().map(RateQualifiedFixed::getName).collect(Collectors.joining(", ")));
        return rateQualifiedFixedDetails.size();
    }

     void saveRateDetailsInChunk(List<RateQualifiedFixedDetails> rateQualifiedFixedDetails) {
        com.ideas.tetris.pacman.util.CollectionUtils.chunk(rateQualifiedFixedDetails, INSERT_CHUNK_SIZE)
                .forEach(this::saveRateDetailsAsBatch);
    }

    private Map<String, List<RateDetails>> buildRateDetailsForExistingRates(List<RateQualifiedFixedDetailsStg> rateQualifiedDetailsByRateCodeNames, Map<String, RateQualifiedEntityDto> rateQualifiedMap) {
        Map<Integer,String> rateQualifiedIdToRateCode = rateQualifiedMap.entrySet().stream().collect(Collectors.toMap(e->e.getValue().getId(),Map.Entry::getKey));
        Map<Integer, List<RateQualifiedFixedDetailsStg>> stageDetailsByRateQualifiedId = rateQualifiedDetailsByRateCodeNames.stream().collect(Collectors.groupingBy(RateQualifiedFixedDetailsStg::getRateQualifiedId));
        return stageDetailsByRateQualifiedId.entrySet().stream()
                .collect(Collectors.toMap(
                        entry -> rateQualifiedIdToRateCode.get(entry.getKey()),
                        entry -> entry.getValue().stream()
                                .map(rateQualifiedFixedDetailsStg -> {
                                    RateDetails rateDetails = new RateDetails(rateQualifiedFixedDetailsStg.getRateQualifiedId(), rateQualifiedFixedDetailsStg.getAccomTypeId());
                                    rateDetails.setStartDate(rateQualifiedFixedDetailsStg.getStartDate());
                                    rateDetails.setEndDate(rateQualifiedFixedDetailsStg.getEndDate());
                                    rateDetails.setSunday(rateQualifiedFixedDetailsStg.getSunday());
                                    rateDetails.setMonday(rateQualifiedFixedDetailsStg.getMonday());
                                    rateDetails.setTuesday(rateQualifiedFixedDetailsStg.getTuesday());
                                    rateDetails.setWednesday(rateQualifiedFixedDetailsStg.getWednesday());
                                    rateDetails.setThursday(rateQualifiedFixedDetailsStg.getThursday());
                                    rateDetails.setFriday(rateQualifiedFixedDetailsStg.getFriday());
                                    rateDetails.setSaturday(rateQualifiedFixedDetailsStg.getSaturday());
                                    return rateDetails;
                                })
                                .collect(Collectors.toList())
                ));
    }

    private List<RateQualifiedFixedDetails> saveHeaderAndGetDetails(List<RateQualifiedFixed> fixedRates) {
        Map<String, List<RateQualifiedFixedDetails>> rateQualifiedFixedDetailsMap = fixedRates.stream().collect(Collectors.toMap(RateQualifiedFixed::getName, RateQualifiedFixed::getDetails));
        Collection<RateQualifiedFixed> rateQualifiedFixedList = saveRatesWithoutDetails(fixedRates);
        for (RateQualifiedFixed rateQualifiedFixed : rateQualifiedFixedList) {
            if (CollectionUtils.isNotEmpty(rateQualifiedFixedDetailsMap.get(rateQualifiedFixed.getName()))) {
                for (RateQualifiedFixedDetails fixedDetails : rateQualifiedFixedDetailsMap.get(rateQualifiedFixed.getName())) {
                    fixedDetails.setRateQualifiedFixed(rateQualifiedFixed);
                }
            }
        }
        return rateQualifiedFixedDetailsMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
    }

    public Collection<RateQualifiedFixed> saveRatesWithoutDetails(List<RateQualifiedFixed> fixedRates) {
        fixedRates.forEach(rateQualifiedFixed -> rateQualifiedFixed.setDetails(List.of()));
        return repository.saveAll(fixedRates);
    }

    public void saveRateDetailsAsBatch(List<RateQualifiedFixedDetails> fixedRatesEntities) {
        saveRateDetailsAsBatch(fixedRatesEntities, true);
    }

    public void saveRateDetailsAsBatch(List<RateQualifiedFixedDetails> fixedRatesEntities, boolean saveIntoStageTable) {
        List<RateQualifiedFixedDetailsBatchDto> rateQualifiedFixedDetailsBatchDtos = fixedRatesEntities.stream().map(rateQualifiedFixedDetails ->
                RateQualifiedFixedDetailsBatchDto.builder()
                        .rateQualifiedFixedId(rateQualifiedFixedDetails.getRateQualifiedFixed().getId())
                        .startDate(rateQualifiedFixedDetails.getStartDate())
                        .endDate(rateQualifiedFixedDetails.getEndDate())
                        .rateQualifiedId(rateQualifiedFixedDetails.getRateQualifiedId())
                        .accomTypeId(rateQualifiedFixedDetails.getAccomTypeId())
                        .sunday(rateQualifiedFixedDetails.getSunday())
                        .monday(rateQualifiedFixedDetails.getMonday())
                        .tuesday(rateQualifiedFixedDetails.getTuesday())
                        .wednesday(rateQualifiedFixedDetails.getWednesday())
                        .thursday(rateQualifiedFixedDetails.getThursday())
                        .friday(rateQualifiedFixedDetails.getFriday())
                        .saturday(rateQualifiedFixedDetails.getSaturday())
                        .build()).collect(Collectors.toList());
        if (saveIntoStageTable) {
            repository.saveDetailsStgAsBatch(rateQualifiedFixedDetailsBatchDtos);
        } else {
            repository.saveDetailsAsBatch(rateQualifiedFixedDetailsBatchDtos);
        }
    }

    private List<RateQualifiedFixed> buildRateQualifiedFixedEntitiesFromDtos(Map<String, RateQualifiedEntityDto> rateQualifiedMap, Map<String,
            List<RateDetails>> calculatedRates) {
        return calculatedRates.keySet().stream().filter(srpName -> CollectionUtils.isNotEmpty(calculatedRates.get(srpName))
                && rateQualifiedMap.get(srpName) != null).map(srp -> {
            RateQualifiedEntityDto rateQualifiedEntityDto = rateQualifiedMap.get(srp);
            return rateQualifiedEntityDto.getName().equals(LV0) ? getOriginalLv0Rates()
                    : rateQualifiedEntityDto.buildRateQualifiedFixedEntity(calculatedRates.get(srp));
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private RateQualifiedFixed getOriginalLv0Rates() {
        return rateQualifiedService.getLv0RateQualifiedWithDetails().buildRateQualifiedFixedEntityForLv0();
    }


    private void failIfOverLappingSeason(Collection<RateQualifiedEntityDto> values) {
        List<String> overLappingSeasonSrps = new ArrayList<>();
        values.forEach(rateQualifiedEntityDto -> {
            Set<Integer> accomTypesWithOverLappingSeasons = rateQualifiedEntityDto.getAccomTypesWithOverLappingSeasons();
            if (CollectionUtils.isNotEmpty(accomTypesWithOverLappingSeasons)) {
                overLappingSeasonSrps.add("SRP " + rateQualifiedEntityDto.getName() + " and accomTypes " + accomTypesWithOverLappingSeasons);
            }
        });
        if (CollectionUtils.isNotEmpty(overLappingSeasonSrps)) {
            throw new TetrisException(MESSAGE_FAILING_BECAUSE_OF_OVERLAPPING_SEASONS_FOUND_IN_BELOW_DATA + overLappingSeasonSrps);
        }

    }

    public List<RateQualifiedFixedDetails> resolveRateQualifiedFixedDetails(List<? extends Integer> rateQualifiedIds, Optional<LocalDate> snapshotDate) {
        log.debug("Starting resolution of fixed details stg to fixed details " + rateQualifiedIds);
        // stg records for which fixed details need to write
        List<RateQualifiedFixedDetailsStg> rateQualifiedDetailsStage = rateQualifiedFixedDetailsStageRepository.findRateQualifiedDetailsByRateQualifiedIds(rateQualifiedIds); // get child
        List<RateQualified> rateQualifieds = rateQualifiedService.getRateQualifiedsByIds(rateQualifiedIds);
        List<String> rateCodeNames = rateQualifieds.stream().map(RateQualified::getName).distinct().collect(Collectors.toList());

        // from ratchet tables get the linked rate codes
        Map<String, String> srpToLinkedSrp = getSrpToLinkedSrpMappings();
        Set<String> parentRateCodes = new HashSet<>(srpToLinkedSrp.values());
        List<String> parentAndChildRateCodes = com.ideas.tetris.pacman.util.CollectionUtils.mergeLists(rateCodeNames, parentRateCodes);
        // child + parent rate_qualified
        Map<Integer, RateQualified> rateQualifiedById = rateQualifiedService.getRateQualifiedById();
        Map<String, RateQualified> rateQualifiedByName = rateQualifiedById.entrySet().stream().collect(Collectors.toMap(e->e.getValue().getName(), Map.Entry::getValue));

        // get the rate qualified details for the linkedRate codes
        List<RateQualifiedFixedDetailsStg> parentRateQualifiedDetailsStg = rateQualifiedFixedDetailsStageRepository.findRateQualifiedDetailsByRateCodeNames(parentRateCodes);
        Map<String, List<RateQualifiedFixedDetailsStg>> srpToParentDetails = parentRateQualifiedDetailsStg.stream().collect(Collectors.groupingBy(r->rateQualifiedById.get(r.getRateQualifiedId()).getName()));
        // only childs
        Map<String, List<RateQualifiedFixedDetailsStg>> srpToChildDetails = rateQualifiedDetailsStage.stream().collect(Collectors.groupingBy(r -> rateQualifiedById.get(r.getRateQualifiedId()).getName()));

        // get the adjustments (for both parent and child ratecodenames only for yieldable value)
        Map<String, RateQualifiedAdjustment> rateCodeToAdjustment = configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_ENABLE_YIELD_AS_ADJUSTMENT) ? Map.of() :
                rateQualifiedAdjustmentService.getYieldableValueAdjustments(parentAndChildRateCodes);
        log.debug("Retrieved child rates : "+rateQualifiedDetailsStage.size()+" parentRates: "+srpToParentDetails.size());
        if(srpToParentDetails.containsKey(LV0)) {

            List<RateDetails> lv0RateDetails = lv0Service.buildLv0RatesFuture();
            log.debug("Retrieved lv0 rates : "+lv0RateDetails.size());
            srpToParentDetails.put(LV0,LinkedSRPRateCalculationForStreamServiceUtil.buildRateQualifiedForLv0(srpToParentDetails.get(LV0),lv0RateDetails));
        }

        final java.time.LocalDate caughtUpDate = snapshotDate.orElse(dateService.getCaughtUpJavaLocalDate());
        Map<String, List<RateDetails>> semiYieldableRates = replaceSemiYieldableParentsByOriginalRates(parentRateCodes, srpToParentDetails, caughtUpDate);

        List<Integer> offsetResolvableRateQualifiedIds = getOffsetResolvableRateQualifiedIds();
        Map<Boolean, List<Integer>> rateQualifiedIdsByIsOffsetResolvable = rateQualifiedIds.stream().collect(Collectors.partitioningBy(offsetResolvableRateQualifiedIds::contains));
        childrenOfSemiYieldableShouldBeFixed(semiYieldableRates, rateQualifiedIdsByIsOffsetResolvable.get(true), rateQualifiedByName, srpToLinkedSrp);
        removeFromOverQualifiedIfBothHaveDifferentRateQualifiedType(rateQualifiedIdsByIsOffsetResolvable.get(true), rateQualifiedByName, srpToLinkedSrp);

        Map<Integer, List<RateQualifiedDetails>> rateOverqualifiedForOffsetResolvable = rateQualifiedService.getRateQualifiedDetailsByIdsChunked(rateQualifiedIdsByIsOffsetResolvable.get(true),caughtUpDate);
        Map<Integer, List<RateQualifiedDetails>> parentRateQualifiedDetails = getParentRateQualifiedDetails(caughtUpDate, rateOverqualifiedForOffsetResolvable, rateQualifiedById, rateQualifiedByName,srpToLinkedSrp);
        rateOverqualifiedForOffsetResolvable.putAll(parentRateQualifiedDetails);
        RateQualifiedOffsetRateResolver offsetResolvableResolver = new RateQualifiedOffsetRateResolver(srpToChildDetails, rateQualifiedById, srpToLinkedSrp, rateOverqualifiedForOffsetResolvable, rateQualifiedByName, isHiltonYieldAsRefinementEnabled());
        List<RateQualifiedFixedDetails> offsetResolvedDetails = offsetResolvableResolver.resolve(rateQualifiedIdsByIsOffsetResolvable.get(true));
        Map<Integer, List<RateQualifiedFixedDetails>> offsetResolvedDetailsByRateQualifiedId = offsetResolvedDetails.stream().collect(Collectors.groupingBy(RateQualifiedFixedDetails::getRateQualifiedId));
        // use above 3 data points to resove the data
        RateQualifiedFixedDetailResolver resolver = new RateQualifiedFixedDetailResolver(srpToChildDetails, srpToParentDetails, rateCodeToAdjustment, rateQualifiedById, srpToLinkedSrp, isHiltonYieldAsRefinementEnabled());
        List<? extends Integer> rateQualifiedIdsForFixedRateResolution = rateQualifiedIds.stream().filter(r -> !offsetResolvedDetailsByRateQualifiedId.containsKey(r)).collect(Collectors.toList());
        List<RateQualifiedFixedDetails> rateQualifiedFixedDetails = resolver.resolve(rateQualifiedIdsForFixedRateResolution);

        // resolve the offset based rates
        List<RateQualifiedFixedDetails> mergedRateQualifiedFixedList =
                CollectionUtils.mergeLists(offsetResolvedDetails,rateQualifiedFixedDetails);


        // save to the database
        saveRateDetailsAsBatch(mergedRateQualifiedFixedList, false);
        repository.updateRateQualifiedTypeByIdsChunked(offsetResolvedDetailsByRateQualifiedId.keySet());
        log.debug("Completed of resolution of fixed details stg to fixed details " + rateQualifiedIds);
        return mergedRateQualifiedFixedList;
    }

    private void childrenOfSemiYieldableShouldBeFixed(Map<String, List<RateDetails>> semiYieldableRates, List<Integer> offsetResolvableRateQualifiedIds, Map<String, RateQualified> rateQualifiedByName, Map<String, String> srpToLinkedSrp) {
        if (!semiYieldableRates.isEmpty() && !offsetResolvableRateQualifiedIds.isEmpty()) {
            offsetResolvableRateQualifiedIds.removeAll(
                    srpToLinkedSrp.entrySet().stream().filter(entry -> semiYieldableRates.containsKey(entry.getValue()))
                            .map(entry -> rateQualifiedByName.getOrDefault(entry.getKey(), new RateQualified()).getId()).collect(Collectors.toList()));
        }
    }

    private void removeFromOverQualifiedIfBothHaveDifferentRateQualifiedType(List<Integer> offsetResolvableRateQualifiedIds, Map<String, RateQualified> rateQualifiedByName, Map<String, String> srpToLinkedSrp) {
        if (isHiltonYieldAsRefinementEnabled() && !offsetResolvableRateQualifiedIds.isEmpty()) {
            srpToLinkedSrp.entrySet().stream()
                    .filter(entry -> !List.of("LV0", "NONE").contains(entry.getValue()))
                    .forEach(entry -> {
                        RateQualified srp = rateQualifiedByName.get(entry.getKey());
                        RateQualified yieldAsSRP = rateQualifiedByName.get(entry.getValue());
                        if (srp != null && yieldAsSRP != null && !srp.getRateQualifiedTypeId().equals(yieldAsSRP.getRateQualifiedTypeId())) {
                            offsetResolvableRateQualifiedIds.remove(srp.getId());
                        }
                    });
        }
    }

    private Map<String, List<RateDetails>> replaceSemiYieldableParentsByOriginalRates(Set<String> parentRateCodes, Map<String, List<RateQualifiedFixedDetailsStg>> srpToParentDetails, LocalDate caughtUpDate) {
        if (isHiltonYieldAsRefinementEnabled() && !parentRateCodes.isEmpty()) {
            LocalDate optimizationEndDate = getOptimizationWindowEndDateFromCaughtUpDate(caughtUpDate);
            Map<String, List<RateDetails>> semiYieldableRates = semiYieldableRateDetailsRepository.fetchActiveSemiYieldableRatesByDateRange(caughtUpDate, optimizationEndDate, new ArrayList<>(parentRateCodes));
            Map<String, List<RateQualifiedFixedDetailsStg>> semiYieldRatesToStg = semiYieldableRates.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> LinkedSRPRateCalculationForStreamServiceUtil.buildRateQualifiedForLv0(srpToParentDetails.get(e.getKey()), e.getValue())));
            srpToParentDetails.putAll(semiYieldRatesToStg);
            return semiYieldableRates;
        }
        return Map.of();
    }

    private boolean isHiltonYieldAsRefinementEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_YIELD_AS_REFINEMENT_ENABLED);
    }

    private Map<Integer, List<RateQualifiedDetails>> getParentRateQualifiedDetails(LocalDate snapshotDate,
                                                                                   Map<Integer, List<RateQualifiedDetails>> rateOverqualifiedForOffsetResolvable,
                                                                                   Map<Integer, RateQualified> rateQualifiedById, Map<String, RateQualified> rateQualifiedByName, Map<String, String> srpToLinkedSrp) {
        Set<RateQualified> rateQualifiedSet = rateOverqualifiedForOffsetResolvable.keySet().stream()
                .map(rateQualifiedById::get).filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Set<String> parentKeys = new HashSet<>();
        rateQualifiedSet.stream().map(RateQualified::getReferenceRateCode).filter(Objects::nonNull).forEach(parentKeys::add);
        rateQualifiedSet.stream().map(RateQualified::getName).map(srpToLinkedSrp::get).filter(Objects::nonNull).forEach(parentKeys::add);

        Set<Integer> parentRateQualifiedIds = parentKeys.stream()
                .map(rateQualifiedByName::get)
                .filter(rq -> rq != null && !rateOverqualifiedForOffsetResolvable.containsKey(rq.getId()))
                .map(RateQualified::getId)
                .collect(Collectors.toSet());

        if (!parentRateQualifiedIds.isEmpty()) {
            return rateQualifiedService.getRateQualifiedDetailsByIdsChunked(parentRateQualifiedIds, snapshotDate);
        }
        return Map.of();
    }

    private List<Integer> getOffsetResolvableRateQualifiedIds() {
        String offsetBasedResolutionLevel =  configParamsService.getParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_CALCULATE_OFFSET_BASED_RATES_FOR);
        if (OffsetBasedResolutionLevel.valueOf(offsetBasedResolutionLevel) == OffsetBasedResolutionLevel.LV0) {
            return linkedSRPMappingsRepository.getLinkedToLv0RateQualifiedIds();
        }
        return List.of();
    }

    public enum OffsetBasedResolutionLevel {
        NONE, LV0, ALL;


    }

    private Map<String, String> getSrpToActiveLinkedSrpMappings(Map<String, LinkedSrpMappingsDto> linkedMappings) {
        Set<String> activeParents = new HashSet<>(crudService.findByNamedQuery(RateQualified.ALL_ACTIVE_RATES));
        // Filter the linked mappings
        return linkedMappings.entrySet().stream()
                .filter(entry -> entry.getValue().getSource().isYieldAsParent())
                .filter(entry -> activeParents.contains(entry.getValue().getSource().getLinkedSrpCode()))
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getSource().getLinkedSrpCode()));
    }

    public Map<String, String> getSrpToLinkedSrpMappings() {
        var linkedMappings = linkedSRPMappingsRepository.getMappings();
        if (isHiltonYieldAsRefinementEnabled()) {
            return getSrpToActiveLinkedSrpMappings(linkedMappings);
        }
        return linkedMappings.entrySet().stream()
                .filter(e -> e.getValue().getSource().isYieldAsParent())
                .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().getSource().getLinkedSrpCode()));
    }

    public List<RateQualifiedFixedDetails> resolveRateQualifiedFixedDetails() {
        return resolveRateQualifiedFixedDetails(rateQualifiedFixedDetailsStageRepository.findDistinctRateQualifiedIds(), Optional.empty());
    }
}
