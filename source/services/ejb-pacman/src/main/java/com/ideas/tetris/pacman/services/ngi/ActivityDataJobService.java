package com.ideas.tetris.pacman.services.ngi;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.api.client.activityStats.model.InputType;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.expose.external.IntegrationType;
import com.ideas.tetris.pacman.services.job.JobMonitorService;
import com.ideas.tetris.pacman.services.job.JobViewCriteria;
import com.ideas.tetris.pacman.services.job.entity.ExecutionStatus;
import com.ideas.tetris.pacman.services.job.entity.JobView;
import com.ideas.tetris.pacman.services.ngi.dto.StatisticsCorrelation;
import com.ideas.tetris.pacman.services.ngi.dto.StatisticsCorrelationJsonMapper;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.systemconfig.PacmanSystemConfigService;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystem;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystemHelper;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.rest.mapper.NGIStatisticsStatus;
import com.ideas.tetris.platform.common.rest.mapper.RestClient;
import com.ideas.tetris.platform.common.rest.mapper.RestEndpoints;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xstream.SerializationUtil;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.ideas.tetris.pacman.common.constants.Constants.SYSTEM_USER_ID;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class ActivityDataJobService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ActivityDataJobService.class);
    private static final String RECEIVED_ACTIVITY_REQUEST = "Received request for client: {} property: {} " +
            "for integration type: {}. Notification origin {}";
    private static final String SUBMITTING_REQUEST_TO_START_JOB = "submitting request to start job: {}";
    private static final String UNABLE_TO_START_JOB = "Did not start job: {}";

    @Autowired
	private PropertyService propertyService;
    @Autowired
	private HiltonValidationService hiltonValidationService;
    @Autowired
	private PacmanSystemConfigService systemConfigService;
    @Autowired
	private RestClient ngiRestClient;
    @Autowired
	private JobServiceLocal jobService;
    @Autowired
	private ExternalSystemHelper externalSystemHelper;
    @Autowired
	private PacmanConfigParamsService configParamsService;
    @Autowired
	private JobMonitorService jobMonitorService;

    public List<Long> loadActivityDataFromNGI(String integrationTypeStr,
                                              String sendingSystemPropertyCode,
                                              String sendingSystemClientCode,
                                              String correlationId,
                                              String timestamp,
                                              String fiscalDate,
                                              String inputType,
                                              String calculateActivityStarted,
                                              String calculateActivityCompleted,
                                              String processId,
                                              NotificationOrigin notificationOrigin) {
        LOGGER.info(RECEIVED_ACTIVITY_REQUEST,
                sendingSystemClientCode,
                sendingSystemPropertyCode,
                integrationTypeStr,
                notificationOrigin);

        List<Long> jobExecutionIds = new ArrayList<>();

        var property = propertyService.translateAndGetNGIProperty(sendingSystemClientCode, sendingSystemPropertyCode);
        if (property == null) {
            LOGGER.info("Property for NGI client code {} and NGI property code {} not found. Ignoring activity data notification from {}",
                    sendingSystemClientCode,
                    sendingSystemPropertyCode,
                    notificationOrigin);

            return jobExecutionIds;
        }

        var shouldAcceptNotification = shouldAcceptNotification(property, notificationOrigin);
        if (!shouldAcceptNotification) {
            LOGGER.info("Activity data notification from {} for client code {} and property code {} is not accepted",
                    notificationOrigin,
                    property.getClient().getCode(),
                    property.getCode());

            return jobExecutionIds;
        }

        LOGGER.info("Processing activity data notification. Client code {}. Property code {}. Notification origin {}",
                property.getClient().getCode(),
                property.getCode(),
                notificationOrigin);

        IntegrationType integrationType = IntegrationType.valueOf(integrationTypeStr);
        String propertyCode = property.getCode();
        String clientCode = property.getClient().getCode();

        if (hiltonValidationService.isRolloutStatusValidation(property)) {
            if (systemConfigService.shouldFetchFiscalDateFromStatsCorrelation()) {
                fiscalDate = getFiscalDateFromNGI(clientCode, propertyCode, correlationId, integrationType.getG3JobName(), false).getFiscalDate();
            }

            hiltonValidationService.processPropertyInValidationRolloutStatus(clientCode, propertyCode, integrationType, correlationId, fiscalDate);

            return jobExecutionIds;
        }

        Stage stage = property.getStage();
        JobName g3JobName = integrationType.getG3JobName();
        if (stageSetForProcessing(stage)) {
            FiscalDateAndStatus fiscalDateAndStatus = null;

            if (shouldFetchFiscalDateFromNGI(property, g3JobName)) {
                fiscalDateAndStatus = getFiscalDateFromNGI(clientCode, propertyCode, correlationId, g3JobName, true);
                fiscalDate = fiscalDateAndStatus.getFiscalDate();
            }

            if (shouldSkipNgiDeferredDeliveryJobCreation(g3JobName, fiscalDateAndStatus, property, correlationId, notificationOrigin)) {
                return jobExecutionIds;
            }

            Map<String, Object> jobParams = new HashMap<>();
            jobParams.put(JobParameterKey.PROPERTY_CODE, propertyCode);
            jobParams.put(JobParameterKey.PROPERTY_ID, property.getId());
            jobParams.put(JobParameterKey.CLIENT_CODE, clientCode);
            jobParams.put(JobParameterKey.CORRELATION_ID, correlationId);
            jobParams.put(JobParameterKey.INTEGRATION_TYPE, integrationTypeStr);
            jobParams.put(JobParameterKey.TIMESTAMP, timestamp);
            jobParams.put(JobParameterKey.FISCAL_DATE, fiscalDate);
            jobParams.put(JobParameterKey.INPUT_TYPE, inputType);
            jobParams.put(JobParameterKey.CALCULATE_ACTIVITY_STARTED_TIME, calculateActivityStarted);
            jobParams.put(JobParameterKey.CALCULATE_ACTIVITY_COMPLETED_TIME, calculateActivityCompleted);
            if (processId != null) {
                jobParams.put(JobParameterKey.INPUT_PROCESSING_ID, processId);
            }
            jobParams.put(JobParameterKey.USER_ID, PacmanWorkContextHelper.getUserId() == null
                    ?
                    String.valueOf(SYSTEM_USER_ID) :
                    PacmanWorkContextHelper.getUserId());

            setExternalSystem(jobParams, clientCode, propertyCode);
            setActivityJobTypeParam(integrationType, jobParams);

            LOGGER.info(SUBMITTING_REQUEST_TO_START_JOB, g3JobName.getDisplayName());
            jobExecutionIds.add(jobService.startGuaranteedNewInstance(g3JobName, jobParams));
        } else {
            //FIXME:COMPLETE INPUT PROCESSING
            LOGGER.info(UNABLE_TO_START_JOB, g3JobName.getDisplayName());
        }

        return jobExecutionIds;
    }

    /**
     * At the moment where the feature flag ACTIVITY_DATA_NOTIFICATION_EVENTS_SQS_ONLY is switched,
     * there is a slight chance that HTTP notification AND SQS notification from PMS inbound are both accepted, which can trigger job creation twice.
     * Here, we make sure to not duplicate jobs by verifying in db whether there is a NGI(Cdp)DeferredDeliveryJob job with same correlation id.
     * This check can be removed once Activity data HTTP notifications from PMS inbound are totally disabled for all properties.
     */
    private boolean shouldSkipNgiDeferredDeliveryJobCreation(JobName g3JobName,
                                                             @Nullable FiscalDateAndStatus fiscalDateAndStatus,
                                                             Property property,
                                                             String correlationId,
                                                             NotificationOrigin notificationOrigin) {
        var localFiscalDateAndStatus = fiscalDateAndStatus;
        if (!isNgiDeferredDeliveryJobOrNGICdpDeferredDeliveryJob(g3JobName)) {
            return false;
        }

        if (!systemConfigService.shouldVerifyStatisticsCorrelationToPreventDuplicateJobs()) {
            return false;
        }

        // in most cases, fiscalDateAndStatus will not be null but better to make sure we don't get an NPE further.
        if (localFiscalDateAndStatus == null) {
            localFiscalDateAndStatus = getFiscalDateFromNGI(property.getClient().getCode(), property.getCode(), correlationId, g3JobName, true);
        }

        // If status in PMS inbound is not SENT_FOR_PROCESSING, then we don't even have to look up for an existing job in database.
        // We can skip job creation.
        if (!NGIStatisticsStatus.SENT_FOR_PROCESSING.toString().equals(localFiscalDateAndStatus.getStatus())) {
            LOGGER.info("NGI statistics status is {} for correlation id {}. Skipping job creation. " +
                            "Client code {}. Property code {}. Notification origin {}",
                    localFiscalDateAndStatus.getStatus(),
                    correlationId,
                    property.getClient().getCode(),
                    property.getCode(),
                    notificationOrigin);

            return true;
        }

        var existingNgiDeferredDeliveryJobWithCorrelationId = findNgiDeferredDeliveryOrNGICdpDeferredDeliveryJobWithCorrelationId(correlationId, property);

        if (existingNgiDeferredDeliveryJobWithCorrelationId.isPresent()) {
            var existingJobView = existingNgiDeferredDeliveryJobWithCorrelationId.get();
            LOGGER.info("Job {} with execution id {} with status {} for correlation id {} found in db. Skipping job creation. " +
                            "Client code {}. Property code {}. Notification origin {}",
                    existingJobView.getJobName(),
                    existingJobView.getJobExecutionId(),
                    existingJobView.getStatus(),
                    correlationId,
                    property.getClient().getCode(),
                    property.getCode(),
                    notificationOrigin);

            return true;
        }

        return false;
    }

    private Optional<JobView> findNgiDeferredDeliveryOrNGICdpDeferredDeliveryJobWithCorrelationId(String correlationId,
                                                                                                  Property property) {
        JobViewCriteria jobViewCriteria = new JobViewCriteria();
        jobViewCriteria.setPropertyId(property.getId());
        jobViewCriteria.setJobNames(List.of(JobName.NGIDeferredDeliveryJob.name(), JobName.NGICdpDeferredDeliveryJob.name()));
        jobViewCriteria.setStatuses(List.of(ExecutionStatus.FAILED, ExecutionStatus.STOPPED, ExecutionStatus.RUNNING));

        var existingJob = jobMonitorService.getJobs(jobViewCriteria);

        return existingJob.stream()
                .map(job -> new JobAndCorrelationId(job, (String) SerializationUtil.deserializeMap(job.getSerializedContext()).get(JobParameterKey.CORRELATION_ID)))
                .filter(jobAndCorrelationId -> correlationId.equals(jobAndCorrelationId.getCorrelationId()))
                .map(JobAndCorrelationId::getJobView)
                .findAny();
    }

    private boolean shouldAcceptNotification(Property property, NotificationOrigin notificationOrigin) {
        boolean acceptActivityDataFromSqsOnly = Boolean.TRUE.equals(configParamsService.<Boolean>getParameterValue(
                property.getClient().getCode(),
                property.getCode(),
                FeatureTogglesConfigParamName.ACTIVITY_DATA_NOTIFICATION_EVENTS_SQS_ONLY));
        switch (notificationOrigin) {
            case HTTP:
                return !acceptActivityDataFromSqsOnly;
            case SQS:
                return acceptActivityDataFromSqsOnly;
            default:
                throw new IllegalArgumentException(String.format("ActivityData Notification from %s is not accepted", notificationOrigin));
        }
    }

    @VisibleForTesting
    boolean shouldFetchFiscalDateFromNGI(Property property, JobName g3JobName) {
        return systemConfigService.shouldFetchFiscalDateFromStatsCorrelation()
                && isMultipleNgiUrlEnabled(property)
                && isNgiDeferredDeliveryJobOrNGICdpDeferredDeliveryJob(g3JobName);
    }

    private static boolean isNgiDeferredDeliveryJobOrNGICdpDeferredDeliveryJob(JobName g3JobName) {
        return JobName.NGIDeferredDeliveryJob == g3JobName || JobName.NGICdpDeferredDeliveryJob == g3JobName;
    }

    private void setExternalSystem(Map<String, Object> jobParams, String clientCode, String propertyCode) {
        if (externalSystemHelper.isOXI(clientCode, propertyCode)) {
            jobParams.put(JobParameterKey.EXTERNAL_SYSTEM_CODE, ExternalSystem.OXI.getCode());
        }
    }

    private Boolean isMultipleNgiUrlEnabled(Property property) {
        return configParamsService.getParameterValue(property.getClient().getCode(), property.getCode(), PreProductionConfigParamName.ENABLE_MULTIPLE_NGI_URL_CONFIGURATIONS);
    }

    private FiscalDateAndStatus getFiscalDateFromNGI(String clientCode, String propertyCode, String correlationId, JobName g3JobName, boolean shouldFormatDate) {
        try {
            LOGGER.info("loadActivityDataFromNGI::Fetching fiscalDate from NGI for clientCode:{}, propertyCode:{},statsCorrelationId:{}", clientCode, propertyCode, correlationId);
            Map<String, String> clientPropertyParameters = getParameters(clientCode, propertyCode, correlationId);
            StatisticsCorrelation statisticsCorrelation = ngiRestClient.getSingleResultFromEndpoint(RestEndpoints.GET_STATISTICS_CORRELATION_V2API, clientPropertyParameters, new StatisticsCorrelationJsonMapper());
            String fiscalDate = shouldFormatDate ? getFormattedDate(statisticsCorrelation.getFiscalDate()) : statisticsCorrelation.getFiscalDate();
            String status = statisticsCorrelation.getStatus();
            LOGGER.info("loadActivityDataFromNGI::Got fiscalDate={} from NGI for clientCode:{}, propertyCode:{},statsCorrelationId:{}", fiscalDate, clientCode, propertyCode, correlationId);
            return new FiscalDateAndStatus(fiscalDate, status);
        } catch (Exception ex) {
            String message = String.format("Error in loadActivityDataFromNGI for clientCode:%s, propertyCode:%s, g3JobName:%s ", clientCode, propertyCode, g3JobName);
            LOGGER.error(message, ex);
            throw new TetrisException(message);
        }
    }

    private Map<String, String> getParameters(String clientCode, String propertyCode, String correlationId) {
        Map<String, String> clientPropertyParameters = new HashMap<>();
        clientPropertyParameters.put("clientCode", clientCode);
        clientPropertyParameters.put("propertyCode", propertyCode);
        clientPropertyParameters.put("statsCorrelationId", correlationId);
        return clientPropertyParameters;
    }

    private boolean stageSetForProcessing(Stage stage) {
        return !(stage == Stage.CATCHUP || stage == Stage.DATA_CAPTURE || stage == Stage.PAUSED || stage == Stage.DORMANT);
    }

    @VisibleForTesting
    String getFormattedDate(String fiscalDateStr) throws ParseException {
        Date fiscalDate = DateUtil.parseDate(fiscalDateStr, "yyyy-MM-dd");
        SimpleDateFormat dateFormat = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy");
        return dateFormat.format(fiscalDate);
    }

    private void setActivityJobTypeParam(IntegrationType integrationType, Map<String, Object> jobParams) {
        if (integrationType.equals(IntegrationType.ACTIVITY_DATA_CDP)) {
            jobParams.put(JobParameterKey.OPERATION_TYPE, Constants.CDP);
        } else if (integrationType.equals(IntegrationType.ACTIVITY_DATA)) {
            jobParams.put(JobParameterKey.OPERATION_TYPE, Constants.BDE);
        }
    }

    @lombok.Value
    private static class FiscalDateAndStatus {
        String fiscalDate;
        String status;
    }

    @lombok.Value
    private static class JobAndCorrelationId {
        JobView jobView;
        String correlationId;
    }
}