package com.ideas.tetris.pacman.services.security;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableMap;
import com.ideas.infra.tetris.security.AuthGroupExploder;
import com.ideas.infra.tetris.security.ExplosionException;
import com.ideas.infra.tetris.security.LDAPConstants;
import com.ideas.infra.tetris.security.LDAPException;
import com.ideas.infra.tetris.security.domain.LDAPUser;
import com.ideas.infra.tetris.security.domain.PropertyRoleMapping;
import com.ideas.infra.tetris.security.domain.Role;
import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.infra.tetris.security.jaas.TetrisPrincipal;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.common.workcontext.WorkContextHelper;
import com.ideas.tetris.pacman.services.authgroup.services.AuthGroupManagementService;
import com.ideas.tetris.pacman.services.client.service.ClientAgentConfigService;
import com.ideas.tetris.pacman.services.client.service.ClientConfigService;
import com.ideas.tetris.pacman.services.client.service.ClientService;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.*;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.customattributeservice.entity.CustomAttributeSearchCriteria;
import com.ideas.tetris.pacman.services.customattributeservice.service.CustomAttributeSearchBean;
import com.ideas.tetris.pacman.services.customattributeservice.service.CustomAttributeService;
import com.ideas.tetris.pacman.services.fds.uas.UASService;
import com.ideas.tetris.pacman.services.fds.uas.model.UASAuthGroup;
import com.ideas.tetris.pacman.services.fds.uas.model.UASRuleDetails;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.pacman.services.rules.*;
import com.ideas.tetris.platform.common.Justification;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.contextholder.PlatformThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.errorhandling.TetrisSecurityException;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.*;
import com.ideas.tetris.platform.services.globalproperty.service.ClientPropertyCacheService;
import com.ideas.tetris.platform.services.util.bean.BeanLocator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.SSO_USER_PRINCIPAL;
import static com.ideas.tetris.platform.common.errorhandling.ErrorCode.UNEXPECTED_ERROR;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;

@Component
@Transactional
public class AuthorizationService implements AuthGroupExploder {

    private static final Logger LOGGER = Logger.getLogger(AuthorizationService.class.getName());

    // This needs some work. Permissions are now defined in grails and here.
    // Need single source of truth
    static final String DECISION_CONFIGURATION_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE
            + TetrisPermissionKey.DECISION_CONFIGURATION;
    static final String CLIENT_QUESTIONNAIRE_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE
            + TetrisPermissionKey.CLIENT_QUESTIONNAIRE;
    static final String INSTALLATION_STATUS_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.INSTALLATION_STATUS;
    static final String GROUP_PRICING_CONFIGURATION_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE
            + TetrisPermissionKey.GROUP_PRICING_CONFIGURATION;
    static final String GROUP_PRICING_EVALUATION_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE
            + TetrisPermissionKey.GROUP_PRICING_EVALUATION;

    static final String FUNCTION_SPACE_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.FUNCTION_SPACE;
    static final String FUNCTION_SPACE_EVALUATION_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.FUNCTION_SPACE_EVALUATION;
    static final String FUNCTION_SPACE_PERFORMANCE_TRENDS_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.FUNCTION_SPACE_PERFORMANCE_TRENDS;
    static final String FUNCTION_SPACE_DEMAND_CALENDAR_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.FUNCTION_SPACE_DEMAND_CALENDAR;
    static final String FUNCTION_SPACE_FORECAST_REVIEW_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.FUNCTION_SPACE_FORECAST_REVIEW;
    static final String FUNCTION_SPACE_CONFIGURATION_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.FUNCTION_SPACE_CONFIGURATION;

    static final String GROUP_WASH_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.GROUP_WASH;
    static final String BUSINESS_INSIGHTS_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.BUSINESS_INSIGHTS;
    static final String SAVED_REPORTS_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.SAVED_REPORT;
    static final String RATE_PLAN_CONFIG_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.RATE_PLAN_CONFIGURATION;
    static final String RATE_PLAN_REPORT_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.RATE_PLAN_REPORT;
    static final String INVENTORY_HISTORY_REPORT_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE
            + TetrisPermissionKey.INVENTORY_HISTORY_REPORT;
    static final String FORECAST_VALIDATION_REPORT_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE
            + TetrisPermissionKey.FORECAST_VALIDATION_REPORT;
    static final String INDIVIDUAL_GROUP_WASH_REPORT_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE
            + TetrisPermissionKey.INDIVIDUAL_GROUP_WASH_REPORT;
    static final String SCHEDULE_REPORT_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.SCHEDULE_REPORT;
    static final String LIMITED_DATA_BUILD_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.LIMITED_DATA_BUILD;
    static final String CLIENT_LIMITED_DATA_BUILD_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.CLIENT_LIMITED_DATA_BUILD;
    static final String CLIENT_BUDGET_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.CLIENT_BUDGET;
    static final String CHANNEL_COSTS_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.CHANNEL_COSTS;
    static final String QUALIFIED_RATE_PLAN_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE
            + TetrisPermissionKey.QUALIFIED_RATE_PLAN_CONFIGURATION;
    static final String MASS_RESTRICTION_CONFIGURATION_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE
            + TetrisPermissionKey.MASS_RESTRICTION_UPLOAD;
    static final String SERVICING_COST_BY_LOS_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE
            + TetrisPermissionKey.SERVICING_COST_BY_LOS_CONFIGURATION;
    static final String SPECIAL_EVENT_UPLOAD_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.SPECIAL_EVENT_UPLOAD;
    static final String RESTRICTION_LEVEL_REPORT_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE
            + TetrisPermissionKey.RESTRICTION_LEVEL_REPORT;
    static final String EXTENDED_STAY_RATE_CONFIGURATION_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.EXTENDED_STAY_RATE_CONFIGURATION;
    static final String EXTENDED_STAY_RATE_MANAGEMENT_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.EXTENDED_STAY_RATE_MANAGEMENT;

    static final String DAILY_BAR_CONFIG_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.DAILY_BAR_CONFIG;
    static final String GROUP_STATUS_CODE_MAPPING_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.GROUP_STATUS_CODE_MAPPING;

    static final String PRICING_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.PRICING;
    static final String CONTINUOUS_PRICING_CONFIGURATION_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.PRICING_CONFIG;
    static final String PRICING_MANAGEMENT_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.PRICING_MANAGEMENT;
    static final String MASS_BAR_CONFIGURATION_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.MASS_BAR_CONFIGURATION;
    static final String COMPONENT_ROOMS_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.COMPONENT_ROOMS;
    static final String OUT_OF_ORDER_COMPONENT_ROOMS_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.OUT_OF_ORDER_COMPONENT_ROOMS;
    static final String OUT_OF_ORDER_OVERRIDES_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.OUT_OF_ORDER_OVERRIDES;
    static final String PMS_MIGRATION_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.PMS_MIGRATION;
    static final String RDL_MANAGEMENT_NEW_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.RDL_MANAGEMENT_NEW;
    static final String FISCAL_CALENDAR_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.FISCAL_CALENDAR;
    static final String DEMAND360_DASHBOARD_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.DEMAND_360;
    static final String ARRIVAL_BY_LOS_REMAINING_DEMAND_REPORT_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.ARRIVAL_BY_LOS_REMAINING_DEMAND_REPORT;
    static final String INVENTORY_GROUP_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.INVENTORY_GROUP;
    static final String INVESTIGATOR_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.INVESTIGATOR;
    static final String LRA_RESTRICTIONS_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.LRA_RESTRICTIONS;
    static final String REPUTATION_MANAGEMENT_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.REPUTATION_MANAGEMENT;
    static final String AGILE_RATES_CONFIGURATION_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.AGILE_RATES_CONFIGURATION;
    static final String OPTIX_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.OPTIX;
    static final String OPTIX_MULTI_PROPERTY_RATE_PLAN_REPORT_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.MULTI_PROPERTY_RATE_PLAN_REPORT;
    static final String OPTIX_MULTI_PROPERTY_BOOKING_PACE_REPORT_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.MULTI_PROPERTY_BOOKING_PACE_REPORT;
    static final String CLIENT_DASHBOARD_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE
            + TetrisPermissionKey.CLIENT_DASHBOARD;
    static final String PMS_MIGRATION_MAPPING_IMPORT_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.PMS_MIGRATION_MAPPING_IMPORT;
    static final String GROUP_FLOOR_MANAGEMENT_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.GROUP_FLOOR_MANAGEMENT;
    static final String GROUP_FLOOR_CONFIGURATION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.GROUP_FLOOR_CONFIGURATION;
    static final String FUNCTION_SPACE_DASHBOARD_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.FUNCTION_SPACE_DASHBOARD;
    static final String HOSPITALITY_ROOMS_CONFIGURATION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.HOSPITALITY_ROOMS_CONFIGURATION;
    static final String MANUAL_RESTRICTION_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.MANUAL_RESTRICTION;
    static final String IDEASHARE_PERMISSION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.IDEASHARE;
    static final String CONFIGURATION_MANAGER_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.CONFIGURATION_MANAGER;
    static final String HILTON_CPMIGRATION_RCRTMAPPING_IMPORT_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.HILTON_CPMIGRATION_RCRTMAPPING_IMPORT;
    static final String HILTON_RECOVERY_STATE_TRANSITION_IMPORT_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.HILTON_RECOVERY_STATE_TRANSITION_IMPORT;
    static final String HILTON_CPMIGRATION_EXTENDED_STAY_IMPORT_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.HILTON_CPMIGRATION_EXTENDED_STAY_IMPORT;
    static final String COMPLIMENTARY_CROSS_CHARGE_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.COMPLIMENTARY_CROSS_CHARGE;
    static final String MEETING_PACKAGE_PRICING_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.MEETING_PACKAGE_PRICING;
    static final String MEETING_PACKAGE_PRICING_CONFIGURATION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.MEETING_PACKAGE_CONFIGURATION;
    static final String MEETING_PACKAGE_PRICING_REPORT_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.MEETING_PACKAGE_PRICING_REPORT;
    static final String MEETING_PACKAGE_INVENTORY_HISTORY_REPORT_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.MEETING_PACKAGE_INVENTORY_HISTORY_REPORT;

    static final String PORTFOLIO_NAVIGATOR_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.PORTFOLIO_NAVIGATOR;
    static final String PORTFOLIO_NAVIGATOR_OVERBOOKING_CONFIGURATION_CONSTRAINTS_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.PORTFOLIO_NAVIGATOR_OVERBOOKING_CONFIGURATION_CONSTRAINTS;
    static final String PORTFOLIO_NAVIGATOR_OVERBOOKING_OVERRIDES_CONSTRAINTS_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.PORTFOLIO_NAVIGATOR_OVERBOOKING_OVERRIDES_CONSTRAINTS;
    static final String RDL_CONFIGURATION_MANAGE_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.RDL_CONFIGURATION;
    static final String COMPETITOR_CUSTOM_ROOM_MAPPING_NEW_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.COMPETITOR_CUSTOM_ROOM_MAPPING_NEW;
    static final String COMPETITOR_SET_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.COMPETITOR_SET;
    static final String RDL_HOME_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.RDL_HOME;
    static final String ON_DEMAND_OPTIMIZATION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.ON_DEMAND_OPTIMIZATION;
    static final String GRO_AGGREGATE_DATA_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.GRO_AGGREGATE_DATA;
    static final String GROUP_PRODUCTS_DEFINITION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.GROUP_PRODUCTS_DEFINITION;
    static final String GROUP_PRODUCTS_DEFAULTS_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.GROUP_PRODUCTS_DEFAULTS;
    static final String GROUP_PRODUCTS_SEASONS_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.GROUP_PRODUCTS_SEASONS;
    static final String FUNCTION_MANAGE_NATIONAL_HOLIDAY_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.FUNCTION_MANAGE_NATIONAL_HOLIDAY;
    static final String FUNCTION_GROUP_PRICING_PRICE_ACCELERATOR_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.FUNCTION_GROUP_PRICING_PRICE_ACCELERATOR;
    static final String PRICING_CONFIGURATION_OFFSETS_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.PRICING_CONFIGURATION_OFFSETS;
    static final String PRICING_CONFIGURATION_RESTRICTIONS_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.PRICING_CONFIGURATION_RESTRICTIONS;
    static final String PRICING_CONFIGURATION_SUPPLEMENTS_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.PRICING_CONFIGURATION_SUPPLEMENTS;
    static final String GROUP_PRODUCTS_CONFIGURATION_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.GROUP_PRODUCTS_CONFIGURATION;
    static final String CHANNEL_FORECAST_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.CHANNEL_FORECAST;
    static final String CHANNEL_RESTRICTION_ADJUSTMENTS_PROPERTY_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.CHANNEL_RESTRICTION_ADJUSTMENTS_PROPERTY;
    static final String CHANNEL_RESTRICTION_ADJUSTMENTS_CLIENT_KEY = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.CHANNEL_RESTRICTION_ADJUSTMENTS_CLIENT;

    private static final java.lang.String AT_LEAST_ONE_PROPERTY_HAVING_FEATURE_ENABLED = "select value.* from Config_Parameter_Value value " +
            " inner join Config_Parameter cp " +
            " on value.Config_Parameter_ID = cp.Config_Parameter_ID " +
            " and Context like :clientContext  " +
            " and cp.Name = :featureName " +
            " and ( Config_Parameter_Predefined_Value_ID = 1000 " +
            " or ( Config_Parameter_Predefined_Value_ID is null and FixedValue = 'true' ) " +
            " ) ";

    public static final java.lang.String GET_VALUES_WITH_PARAM_ENABLED_AT_PROPERTY_CONTEXTS_FOR_CLIENT = "select cp.Name, cpv.Context, cpv.Config_Parameter_Predefined_Value_ID, cpv.FixedValue from Config_Parameter_Value cpv " +
            " inner join Config_Parameter cp " +
            " on cpv.Config_Parameter_ID = cp.Config_Parameter_ID " +
            " and cpv.Context like :clientContext " +
            " and cp.Name in :paramNames " +
            " and ( cpv.Config_Parameter_Predefined_Value_ID = 1000 " +
            " or ( cpv.Config_Parameter_Predefined_Value_ID is null and cpv.FixedValue = 'true' ) " +
            " ) ";

    private static final java.lang.String AT_LEAST_ONE_PROPERTY_HAVING_FEATURE_DISABLED = "select value.* from Config_Parameter_Value value " +
            " inner join Config_Parameter cp " +
            " on value.Config_Parameter_ID = cp.Config_Parameter_ID " +
            " and Context like :clientContext  " +
            " and cp.Name = :featureName " +
            " and Config_Parameter_Predefined_Value_ID = 1001 ";

    public static final java.lang.String GET_USER_AUTH_GROUP_IDS_FOR_SELECTED_USERS = " With loggedInUserAuthGroup as( " +
            " select Auth_Group_ID  from User_Auth_Group_Role where user_id = :loggedInUserId " +
            "  ), editedUserAuthGroup as (" +
            " select Auth_Group_ID  from User_Auth_Group_Role where user_id in( :usersToBeEdited) " +
            " ) select loggedInUserAuthGroup.Auth_Group_ID as loggedInAuthGroup, editedUserAuthGroup.Auth_Group_ID as editedAuthGroup " +
            "  from loggedInUserAuthGroup full outer join editedUserAuthGroup on 1=1";


    public static final java.lang.String IS_EDITED_USERS_HAVING_MORE_ASSIGNED_PROPERTIES = " With propertiesOfLoggedInUser as (  " +
            "       select  group_property.property_id as propID from User_Auth_Group_Role as group_role inner join Auth_Group_Property as group_property on group_role.Auth_Group_ID = group_property.Auth_Group_ID  " +
            "       where user_id = :loggedInUserId  " +
            "       union  " +
            "       select  Property_id from User_Individual_Property_Role where User_ID = :loggedInUserId  " +
            "   )," +
            " propertiesOfEditedUser as (  " +
            "           select   group_property.property_id as propId from User_Auth_Group_Role as group_role inner join Auth_Group_Property as group_property on group_role.Auth_Group_ID = group_property.Auth_Group_ID  " +
            "           where user_id in( :usersToBeEdited)  " +
            "           union  " +
            "           select  Property_id from User_Individual_Property_Role where User_ID in (:usersToBeEdited)  " +
            "   )   " +
            "  select count(*) from propertiesOfLoggedInUser full outer join propertiesOfEditedUser " +
            "  on propertiesOfLoggedInUser.propID = propertiesOfEditedUser.propId  " +
            "  where propertiesOfLoggedInUser.propID is null";


    private static final Map<String, String> PERM_FEATURE_MAPPING = ImmutableMap.<String, String>builder()
            .put(GROUP_PRICING_CONFIGURATION_PERMISSION_KEY, FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED.value())
            .put(GROUP_PRICING_EVALUATION_PERMISSION_KEY, FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED.value())
            .put(GROUP_WASH_PERMISSION_KEY, FeatureTogglesConfigParamName.GROUP_WASH_BY_GROUP_ENABLED.value())
            .put(BUSINESS_INSIGHTS_PERMISSION_KEY, PreProductionConfigParamName.BUSINESS_INSIGHTS_ENABLED.value())
            .put(RATE_PLAN_CONFIG_PERMISSION_KEY, FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())
            .put(INVENTORY_HISTORY_REPORT_PERMISSION_KEY, FeatureTogglesConfigParamName.INVENTORY_HISTORY_REPORT_ENABLED.value())
            .put(FORECAST_VALIDATION_REPORT_PERMISSION_KEY, FeatureTogglesConfigParamName.FORECAST_VALIDATION_REPORT_ENABLED.value())
            .put(INDIVIDUAL_GROUP_WASH_REPORT_PERMISSION_KEY, FeatureTogglesConfigParamName.INDIVIDUAL_GROUP_WASH_REPORT_ENABLED.value())
            .put(SCHEDULE_REPORT_PERMISSION_KEY, FeatureTogglesConfigParamName.SCHEDULE_REPORT_ENABLED.value())
            .put(RATE_PLAN_REPORT_PERMISSION_KEY, FeatureTogglesConfigParamName.ALL_SRPREPORT_ENABLED.value())
            .put(DECISION_CONFIGURATION_PERMISSION_KEY, FeatureTogglesConfigParamName.DECISION_CONFIGURATION_ENABLED.value())
            .put(CLIENT_QUESTIONNAIRE_PERMISSION_KEY, FeatureTogglesConfigParamName.ENABLE_CLIENT_QUESTIONNAIRE.value())
            .put(INSTALLATION_STATUS_PERMISSION_KEY, FeatureTogglesConfigParamName.INSTALLATION_STATUS_ENABLED.value())
            .put(MASS_RESTRICTION_CONFIGURATION_PERMISSION_KEY, FeatureTogglesConfigParamName.MASS_RESTRICTION_CONFIGURATION_CONFIGURATION_ENABLED.value())
            .put(SERVICING_COST_BY_LOS_PERMISSION_KEY, FeatureTogglesConfigParamName.PROFIT_OPTIMIZATION_ENABLED.value())
            .put(RESTRICTION_LEVEL_REPORT_PERMISSION_KEY, FeatureTogglesConfigParamName.ENABLE_RESTRICTION_LEVEL_REPORT.value())
            .put(FUNCTION_SPACE_PERMISSION_KEY, FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value())
            .put(FUNCTION_SPACE_DEMAND_CALENDAR_PERMISSION_KEY, FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value())
            .put(FUNCTION_SPACE_FORECAST_REVIEW_PERMISSION_KEY, FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value())
            .put(FUNCTION_SPACE_CONFIGURATION_PERMISSION_KEY, FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value())
            .put(FUNCTION_SPACE_EVALUATION_PERMISSION_KEY, FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value())
            .put(FUNCTION_SPACE_PERFORMANCE_TRENDS_PERMISSION_KEY, FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value())
            .put(EXTENDED_STAY_RATE_CONFIGURATION_PERMISSION_KEY, FeatureTogglesConfigParamName.EXTENDED_STAY_UNQUALIFIED_RATE_MANAGEMENT_ENABLED.value())
            .put(DAILY_BAR_CONFIG_PERMISSION_KEY, FeatureTogglesConfigParamName.DAILY_BAR_CONFIGURATION_ENABLED.value())
            .put(GROUP_STATUS_CODE_MAPPING_PERMISSION_KEY, FeatureTogglesConfigParamName.GROUP_STATUS_CODE_MAPPING.value())
            .put(EXTENDED_STAY_RATE_MANAGEMENT_PERMISSION_KEY, FeatureTogglesConfigParamName.EXTENDED_STAY_UNQUALIFIED_RATE_MANAGEMENT_ENABLED.value())
            .put(PRICING_PERMISSION_KEY, GUIConfigParamName.IS_PRICING_REDESIGN_ENABLED.value())
            .put(PRICING_MANAGEMENT_PERMISSION_KEY, GUIConfigParamName.IS_PRICING_MANAGEMENT_ENABLED.value())
            .put(CONTINUOUS_PRICING_CONFIGURATION_PERMISSION_KEY, GUIConfigParamName.IS_PRICING_CONFIGURATION_ENABLED.value())
            .put(LIMITED_DATA_BUILD_PERMISSION_KEY, IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED.value())
            .put(CLIENT_LIMITED_DATA_BUILD_PERMISSION_KEY, IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED.value())
            .put(CHANNEL_COSTS_PERMISSION_KEY, FeatureTogglesConfigParamName.CHANNEL_COST_ENABLED.value())
            .put(MASS_BAR_CONFIGURATION_PERMISSION_KEY, FeatureTogglesConfigParamName.MASS_BAR_CONFIGURATION_ENABLED.value())
            .put(COMPONENT_ROOMS_PERMISSION_KEY, FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED.value())
            .put(OUT_OF_ORDER_COMPONENT_ROOMS_PERMISSION_KEY, FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED.value())
            .put(OUT_OF_ORDER_OVERRIDES_PERMISSION_KEY, FeatureTogglesConfigParamName.OUT_OF_ORDER_OVERRIDES_ENABLED.value())
            .put(PMS_MIGRATION_PERMISSION_KEY, FeatureTogglesConfigParamName.PMS_MIGRATION_ENABLED.value())
            .put(RDL_MANAGEMENT_NEW_PERMISSION_KEY, FeatureTogglesConfigParamName.IS_RDL_ENABLED.value())
            .put(DEMAND360_DASHBOARD_PERMISSION_KEY, FeatureTogglesConfigParamName.DEMAND360_DEMAND360DASHBOARD_ENABLED.value())
            .put(ARRIVAL_BY_LOS_REMAINING_DEMAND_REPORT_PERMISSION_KEY, FeatureTogglesConfigParamName.DEMAND_AND_WASH_MANAGEMENT_EXTENDED_STAY_ENABLED.value())
            .put(LRA_RESTRICTIONS_PERMISSION_KEY, FeatureTogglesConfigParamName.LRA_RESTRICTIONS_ENABLED.value())
            .put(REPUTATION_MANAGEMENT_PERMISSION_KEY, FeatureTogglesConfigParamName.RRAENABLED.value())
            .put(AGILE_RATES_CONFIGURATION_PERMISSION_KEY, FeatureTogglesConfigParamName.AGILE_RATES_ENABLED.value())
            .put(OPTIX_PERMISSION_KEY, FeatureTogglesConfigParamName.OPTIX_ENABLED.value())
            .put(OPTIX_MULTI_PROPERTY_BOOKING_PACE_REPORT_KEY, FeatureTogglesConfigParamName.OPTIX_ENABLED.value())
            .put(OPTIX_MULTI_PROPERTY_RATE_PLAN_REPORT_KEY, FeatureTogglesConfigParamName.OPTIX_ENABLED.value())
            .put(SPECIAL_EVENT_UPLOAD_PERMISSION_KEY, FeatureTogglesConfigParamName.SPECIAL_EVENT_UPLOAD_ENABLED.value())
            .put(CLIENT_DASHBOARD_PERMISSION_KEY, FeatureTogglesConfigParamName.CLIENT_PROCESSING_DASHBOARD_ENABLED.value())
            .put(PMS_MIGRATION_MAPPING_IMPORT_PERMISSION_KEY, FeatureTogglesConfigParamName.PMS_MIGRATION_ENABLED.value())
            .put(GROUP_FLOOR_MANAGEMENT_KEY, FeatureTogglesConfigParamName.IS_GROUP_FLOOR_OVERRIDE_ENABLED.value())
            .put(GROUP_FLOOR_CONFIGURATION_KEY, FeatureTogglesConfigParamName.IS_GROUP_FLOOR_OVERRIDE_ENABLED.value())
            .put(FUNCTION_SPACE_DASHBOARD_KEY, FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value())
            .put(HOSPITALITY_ROOMS_CONFIGURATION_KEY, FeatureTogglesConfigParamName.HOSPITALITY_ROOMS_ENABLED.value())
            .put(MANUAL_RESTRICTION_PERMISSION_KEY, FeatureTogglesConfigParamName.MANUAL_RESTRICTION_ENABLED.value())
            .put(RDL_CONFIGURATION_MANAGE_KEY, FeatureTogglesConfigParamName.IS_RDL_ENABLED.value())
            .put(COMPETITOR_CUSTOM_ROOM_MAPPING_NEW_KEY, FeatureTogglesConfigParamName.NEW_COMPETITOR_CUSTOM_ROOM_TYPE_MAPPING_UI_ENABLED.value())
            .put(RDL_HOME_KEY, FeatureTogglesConfigParamName.IS_RDL_HOME_UI_ENABLED.value())
            .put(IDEASHARE_PERMISSION_KEY, GUIConfigParamName.IDEASHARE_ENABLED.value())
            .put(CONFIGURATION_MANAGER_KEY, PreProductionConfigParamName.CONFIGURATION_MANAGER_ENABLED.value())
            .put(HILTON_CPMIGRATION_RCRTMAPPING_IMPORT_KEY, GUIConfigParamName.ENABLE_HILTON_CP_MIGRATION_RCRT_MAPPING_SCREEN.value())
            .put(HILTON_CPMIGRATION_EXTENDED_STAY_IMPORT_KEY, GUIConfigParamName.ENABLE_HILTON_CP_MIGRATION_RCRT_MAPPING_SCREEN.value())
            .put(HILTON_RECOVERY_STATE_TRANSITION_IMPORT_KEY, GUIConfigParamName.ENABLE_HILTON_CP_MIGRATION_RCRT_MAPPING_SCREEN.value())
            .put(COMPLIMENTARY_CROSS_CHARGE_KEY, PreProductionConfigParamName.COMPLIMENTARY_CROSS_CHARGE_CONFIGURATION_ENABLED.value())
            .put(ON_DEMAND_OPTIMIZATION_KEY, FeatureTogglesConfigParamName.ON_DEMAND_IDP_ENABLED.value())
            .put(GRO_AGGREGATE_DATA_KEY, FeatureTogglesConfigParamName.IS_INTERNAL_AGGREGATE_DATA_ENABLED.value())
            .put(SAVED_REPORTS_PERMISSION_KEY, FeatureTogglesConfigParamName.ENABLE_SAVE_CONFIG_IN_REPORTS.value())

            .put(PORTFOLIO_NAVIGATOR_KEY, FeatureTogglesConfigParamName.CENTRAL_RMS_G3_PERMISSIONS_ENABLED.value())
            .put(PORTFOLIO_NAVIGATOR_OVERBOOKING_CONFIGURATION_CONSTRAINTS_KEY, FeatureTogglesConfigParamName.CENTRAL_RMS_OVERBOOKING_SCORING_ALERTS_ENABLED.value())
            .put(PORTFOLIO_NAVIGATOR_OVERBOOKING_OVERRIDES_CONSTRAINTS_KEY, FeatureTogglesConfigParamName.CENTRAL_RMS_OVERBOOKING_SCORING_ALERTS_ENABLED.value())

            .put(GROUP_PRODUCTS_DEFINITION_KEY, PreProductionConfigParamName.SMALL_GROUP_PRODUCT_ENABLED.value())
            .put(GROUP_PRODUCTS_DEFAULTS_KEY, PreProductionConfigParamName.SMALL_GROUP_PRODUCT_ENABLED.value())
            .put(GROUP_PRODUCTS_SEASONS_KEY, PreProductionConfigParamName.SMALL_GROUP_PRODUCT_ENABLED.value())
            .put(FUNCTION_MANAGE_NATIONAL_HOLIDAY_KEY, FeatureTogglesConfigParamName.ENABLE_MANAGE_NATIONAL_HOLIDAY.value())
            .put(FUNCTION_GROUP_PRICING_PRICE_ACCELERATOR_KEY, PreProductionConfigParamName.SHOW_PRICE_ACCELERATOR_PERMISSION_ON_UI.value())
            .put(PRICING_CONFIGURATION_OFFSETS_KEY, FeatureTogglesConfigParamName.PRICING_CONFIGURATION_PERMISSIONS_ENABLED.value())
            .put(PRICING_CONFIGURATION_RESTRICTIONS_KEY, FeatureTogglesConfigParamName.PRICING_CONFIGURATION_PERMISSIONS_ENABLED.value())
            .put(PRICING_CONFIGURATION_SUPPLEMENTS_KEY, FeatureTogglesConfigParamName.PRICING_CONFIGURATION_PERMISSIONS_ENABLED.value())
            .put(GROUP_PRODUCTS_CONFIGURATION_KEY, PreProductionConfigParamName.SMALL_GROUP_PRODUCT_ENABLED.value())
            .put(CHANNEL_FORECAST_KEY, FeatureTogglesConfigParamName.FORECAST_DASHBOARD_ENABLED.value())
            .put(CHANNEL_RESTRICTION_ADJUSTMENTS_PROPERTY_KEY, PreProductionConfigParamName.CHANNEL_RESTRICTIONS_ADJUSTMENT.value())
            .put(MEETING_PACKAGE_PRICING_KEY, FeatureTogglesConfigParamName.MEETING_PACKAGE_PRICING_ENABLED.value())
            .put(MEETING_PACKAGE_PRICING_CONFIGURATION_KEY, FeatureTogglesConfigParamName.MEETING_PACKAGE_PRICING_ENABLED.value())
            .put(MEETING_PACKAGE_PRICING_REPORT_KEY, FeatureTogglesConfigParamName.MEETING_PACKAGE_REPORT_ENABLED.value())
            .put(MEETING_PACKAGE_INVENTORY_HISTORY_REPORT_KEY, FeatureTogglesConfigParamName.MEETING_PACKAGE_REPORT_ENABLED.value())
            .put(CHANNEL_RESTRICTION_ADJUSTMENTS_CLIENT_KEY, FeatureTogglesConfigParamName.HILTON_CHANNEL_RESTRICTION_GLOBAL_ADJUSTMENTS.value())
            .build();
    private static final int LOGGED_USER_AUTH_GROUP_ID_QUERY_INDEX = 0;
    private static final int SELECTED_USERS_AUTH_GROUP_ID_QUERY_INDEX = 1;
    private static final String ACCESS_READ_WRITE = "&access=readWrite";

    @GlobalCrudServiceBean.Qualifier
    @Qualifier("globalCrudServiceBean")
    @Autowired
    private CrudService globalCrudService;
    @Autowired
    private PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
    private RulesService rulesService;
    @Autowired
    private CustomAttributeSearchBean searcher;

    @TenantCrudServiceBean.Qualifier
    @Qualifier("tenantCrudServiceBean")
    @Autowired
    private CrudService crudService;
    @Autowired
    private PropertyGroupService propertyGroupService;
    @Autowired
    private ClientConfigService clientConfigService;
    @Autowired
    private CustomAttributeService customAttributeService;
    @Autowired
    private PropertyCapacityCache propertyCapacityCache;
    @Autowired
    private UserAuthorizedPropertyCache userAuthorizedPropertyCache;
    @Autowired
    private ClientPropertyCacheService clientPropertyCacheService;
    @Autowired
    private UserGlobalDBService userGlobalDBService;
    @Autowired
    private UserService userService;
    @Autowired
    private AsyncUserService asyncUserService;
    @Autowired
    private RoleService roleService;
    @Autowired
    AuthGroupManagementService authGroupManagementService;
    @Autowired
	private UASService uasService;
    @Autowired
	private ClientService clientService;

    // These mutators are solely for testing purposes - outside namespace -
    // Mockito me
    // Some need to be public - yuck
    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    public void setGlobalCrudService(CrudService globalCrudService) {
        this.globalCrudService = globalCrudService;
    }

    public void setUserGlobalDBService(UserGlobalDBService userGlobalDBService) {
        this.userGlobalDBService = userGlobalDBService;
    }

    public void setRulesService(RulesService rulesService) {
        this.rulesService = rulesService;
    }

    public void setSearcher(CustomAttributeSearchBean searcher) {
        this.searcher = searcher;
    }

    public void setPacmanConfigParamsService(PacmanConfigParamsService pacmanConfigParamsService) {
        this.pacmanConfigParamsService = pacmanConfigParamsService;
    }

    public void setUserAuthorizedPropertyCache(UserAuthorizedPropertyCache userAuthorizedPropertyCache) {
        this.userAuthorizedPropertyCache = userAuthorizedPropertyCache;
    }

    public void setClientPropertyCacheService(ClientPropertyCacheService clientPropertyCacheService) {
        this.clientPropertyCacheService = clientPropertyCacheService;
    }

    public void setPropertyCapacityCache(PropertyCapacityCache propertyCapacityCache) {
        this.propertyCapacityCache = propertyCapacityCache;
    }

    public void setClientConfigService(ClientConfigService clientConfigService) {
        this.clientConfigService = clientConfigService;
    }

    public void setCustomAttributeService(CustomAttributeService customAttributeService) {
        this.customAttributeService = customAttributeService;
    }

    public void setPropertyGroupService(PropertyGroupService propertyGroupService) {
        this.propertyGroupService = propertyGroupService;
    }

    public void setAuthGroupManagementService(AuthGroupManagementService authGroupManagementService) {
        this.authGroupManagementService = authGroupManagementService;
    }

    public void setUasService(UASService uasService) {
        this.uasService = uasService;
    }

    public BeanLocator getBeanLocator() {
        return new BeanLocator();
    }

    /**
     * Requires work context on thread local!!
     */
    public boolean userHasAccessToProperty(Integer propertyId) {
        return retrieveAuthorizedProperty(propertyId) != null;
    }

    public boolean userHasAccessToProperty(Property property) {
        if (property == null) {
            LOGGER.warn("userHasAccessToProperty() - Property parameter is NULL");
            return false;
        }

        Integer userId = Integer.valueOf(PlatformThreadLocalContextHolder.getWorkContext().getUserId());
        Integer clientId = PacmanWorkContextHelper.getClientId();

        List<Integer> authorizedProperties = retrieveAuthorizedPropertyIds(userId, clientId);
        return authorizedProperties.contains(property.getId());
    }

    /**
     * Does NOT require work context on thread local
     */
    public boolean userHasAccessToProperty(TetrisPrincipal tp, Property property) {
        if (tp == null || property == null) {
            LOGGER.warn("userHasAccessToProperty() - Either principal or property parameter are NULL");
            return false;
        }
        if (!tp.isInternalUser()) {

            String userOrg = tp.getOrganization();
            String clientOrg = property.getClient() != null ? property.getClient().getCode() : "";

            LOGGER.debug("userHasAccessToProperty(tp,property) - NOT internal user");
            LOGGER.debug("userHasAccessToProperty(tp,property) - userOrg: " + userOrg + ", clientOrg: " + clientOrg);

            // external can NOT access other clients
            if (!userOrg.equalsIgnoreCase(clientOrg)) {
                LOGGER.warn("External users can only access properties within their organization");
                throw TetrisSecurityException.USER_LACKS_CLIENT_ACCESS;
            }
        }
        List<Integer> authorizedProperties = retrieveAuthorizedPropertyIds(tp.getId(), property.getClient().getId());

        return authorizedProperties.contains(property.getId());
    }

    /**
     * Property group must belong to user Furthermore, user must still have
     * access to each property as defined in grouping.
     */
    public boolean userHasAccessToPropertyGroup(PropertyGroup propertyGroup) {
        return userHasAccessToPropertyGroup(PacmanThreadLocalContextHolder.getPrincipal(), propertyGroup);
    }

    public boolean userHasAccessToPropertyGroup(TetrisPrincipal tp, PropertyGroup propertyGroup) {
        if (tp == null || propertyGroup == null) {
            LOGGER.warn("userHasAccessToProperty() - Either principal or propertyGroup parameter are NULL");
            return false;
        }
        // Loop through the individual properties to see if the user has access
        // to all of them
        // If they don't have access to a property, then don't show the group

        Set<PropertyPropertyGroup> propertyPropertyGroups = propertyGroup.getPropertyPropertyGroups();
        if (propertyPropertyGroups != null) {
            List<Property> authorizedProperties = retrieveAuthorizedProperties(propertyGroup.getUserId(), propertyGroup.getClientId());
            for (Property property : getAuthorizedProperties(propertyGroup)) {
                if (!authorizedProperties.contains(property)) {
                    return false;
                }
            }
        }
        return true;
    }

    public List<Property> getAuthorizedProperties(PropertyGroup propertyGroup) {
        List<Property> authorizedGroupProperties = new ArrayList<Property>();
        if (propertyGroup.getPropertyPropertyGroups() != null) {
            Property property = null;
            List<Property> authorizedProperties = retrieveAuthorizedProperties(propertyGroup.getUserId(), propertyGroup.getClientId());
            for (PropertyPropertyGroup ppg : propertyGroup.getPropertyPropertyGroups()) {
                property = ppg.getProperty();
                if (authorizedProperties.contains(property)) {
                    authorizedGroupProperties.add(property);
                }
            }
        }
        return authorizedGroupProperties;
    }

    public List<User> retrieveAllUsers() {
        return crudService.findByNamedQuery(User.ALL);
    }

    public List<User> retriveAllUsersBasedOnLoggedInUser(Integer statusId) {
        GlobalUser loggedInUser = globalCrudService.find(GlobalUser.class, Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        List<Integer> statusList = new ArrayList<>();
        switch (statusId) {
            case -1:
                statusList.add(1);
                statusList.add(2);
                break;
            case 1:
                statusList.add(1);
                break;
            case 2:
                statusList.add(2);
                break;
            default:
                statusList.add(2);
                break;
        }
        if (loggedInUser.isInternal()) {
            return crudService.findByNamedQuery(User.BY_USER_STATUS, QueryParameter.with("status", statusList).parameters());
        } else {
            List<GlobalUser> globalUserList = globalCrudService.findByNamedQuery(
                    GlobalUser.INTERNAL_OR_EXTERNAL_USERS_BY_USER_STATUS,
                    QueryParameter.with("isInternal", Boolean.FALSE).and("clientCode", PacmanWorkContextHelper.getClientCode())
                            .and("status", statusList).parameters());
            ArrayList<User> usersList = new ArrayList<>();
            for (GlobalUser globalUser : globalUserList) {
                User user = new User();
                user.setId(globalUser.getId());
                user.setEmail(globalUser.getEmail());
                user.setName(globalUser.getFullName());
                user.setScreenName(globalUser.getScreenName());
                user.setStatusId(globalUser.getStatusId());
                if (isNotG3Agent(user)) {
                    usersList.add(user);
                }
            }
            return usersList;
        }
    }

    private boolean isNotG3Agent(User user) {
        return ((user.getEmail() != null) && (!user.getEmail().equals(getAgentUserEmail(PacmanWorkContextHelper.getClientCode()))));
    }

    private String getAgentUserEmail(String clientCode) {
        return MessageFormat.format(ClientAgentConfigService.AGENT_EMAIL_TMPL, clientCode);
    }

    public boolean deleteAuthorizationGroup(int authGroupId) {
        Set<LDAPUser> users = loadUsersForAuthGroup(authGroupId, getClientCodeFromWorkContext());
        synchronizePropertyGroupsForTheseUserForAuthGroupDeletion(users, getClientCodeFromWorkContext());
        deleteUserAndAuthGroupAssociation(authGroupId);

        AuthorizationGroup authGroup = globalCrudService.find(AuthorizationGroup.class, authGroupId);
        Integer ruleId = authGroup.getRuleId();
        String uasAuthGroupUuid = authGroup.getUasAuthGroupUuid();

        globalCrudService.delete(AuthorizationGroup.class, authGroupId);
        LOGGER.debug("AuthGroup : " + authGroupId + " deleted");

        deleteRuleAndAuthGroupAssociation(ruleId);

        if (isFDSAuthGroupEnabled()) {
            uasService.deleteAuthGroupInFDS(uasAuthGroupUuid);
            LOGGER.debug("AuthGroup UUID : " + uasAuthGroupUuid + " deleted from UAS");
        }
        return true;
    }

    public void deleteAuthorizationGroup(int authGroupId, String clientCode) {
        Set<LDAPUser> users = loadUsersForAuthGroup(authGroupId, clientCode);
        synchronizePropertyGroupsForTheseUserForAuthGroupDeletion(users, clientCode);
        deleteUserAndAuthGroupAssociation(authGroupId);

        AuthorizationGroup authGroup = globalCrudService.find(AuthorizationGroup.class, authGroupId);
        Integer ruleId = authGroup.getRuleId();

        globalCrudService.delete(AuthorizationGroup.class, authGroupId);
        LOGGER.debug("AuthGroup : " + authGroupId + " deleted");

        deleteRuleAndAuthGroupAssociation(ruleId);
    }

    public void clearAuthorizationGroupsUasUuid(Integer clientId) {
        List<AuthorizationGroup> authGroups = getAuthGroupsByClientId(clientId);

        if (CollectionUtils.isNotEmpty(authGroups)) {
            authGroups.stream().forEach(authorizationGroup -> authorizationGroup.setUasAuthGroupUuid(null));
            globalCrudService.save(authGroups);
        }
    }

    public void deleteRuleAndAuthGroupAssociation(Integer ruleId) {
        if (ruleId != null) {
            List<Integer> ruleIds = Collections.singletonList(ruleId);
            customAttributeService.deleteRulesAndValueMappings(ruleIds, false, null);
        }
    }

    protected int deleteUserAndAuthGroupAssociation(Integer authGroupId) {
        return globalCrudService.executeUpdateByNamedQuery(UserAuthGroupRole.DELETE_BY_AUTH_GROUP_ID,
                QueryParameter.with("authGroupId", authGroupId).parameters());
    }

    public void deletePropertyFromAuthorizationGroups(int propertyId) {
        Query query = globalCrudService.getEntityManager().createNamedQuery(AuthorizationGroupPropertyMapping.DELETE_BY_PROPERTY);
        query.setParameter(AuthorizationGroupPropertyMapping.PARAM_PROPERTY_ID, propertyId);
        int deaths = query.executeUpdate();
        LOGGER.debug(MessageFormat.format(
                "While deleting property {0} we found {1} occurences in authorization groups that must be deleted", propertyId, deaths));
    }

    public void deletePropertyFromPermissions(Integer propertyId, String clientCode) {
        LOGGER.debug("Deleting property " + propertyId + " from user property role mappings");
        Set<LDAPUser> ldapUsers = listAllUsersForClientAndAssociatedProperty(clientCode, propertyId);

        LOGGER.debug("Removing permission for total users " + ldapUsers.size());
        for (LDAPUser ldapUser : ldapUsers) {
            LOGGER.debug("Removing permission for user " + ldapUser.getUserId());

            List<PropertyRoleMapping> propertyRoles = ldapUser.getPropertyRoles();
            Iterator<PropertyRoleMapping> propertyRoleMappings = propertyRoles.iterator();
            while (propertyRoleMappings.hasNext()) {
                PropertyRoleMapping propertyRoleMapping = propertyRoleMappings.next();
                String roleId = propertyRoleMapping.getPropertyId();
                if (roleId.equals(propertyId.toString())) {
                    propertyRoleMappings.remove();
                }
            }
            removeDefaultPropertyFromLdap(propertyId, ldapUser);
            userService.updateUserPreferencesToGlobal(ldapUser);

        }
    }

    private void removeDefaultPropertyFromLdap(Integer propertyId, LDAPUser ldapUser) {
        if (propertyId.toString().equals(ldapUser.getDefaultProperty())) {
            ldapUser.setDefaultProperty("");
        }
    }

    private Set<LDAPUser> listAllUsersForClient(String client) {
        Set<LDAPUser> ldapUsers = new HashSet<>();
        try {
            ldapUsers = userService.listLdapUsersForClient(client);
        } catch (LDAPException e) {
            // Apparently we still don't want to fail hard
            LOGGER.error("LDAP exception thrown getting all users for client: " + client, e);
        }
        return ldapUsers;
    }

    private Set<LDAPUser> listAllUsersForClientAndAssociatedProperty(String client, int propertyId) {
        Set<LDAPUser> ldapUsers = new HashSet<>();
        try {
            ldapUsers = userService.listAllUsersForClientAndAssociatedProperty(client, propertyId);
        } catch (LDAPException e) {
            // Apparently we still don't want to fail hard
            LOGGER.error("LDAP exception thrown getting all users for client: " + client, e);
        }
        return ldapUsers;
    }

    @SuppressWarnings("unchecked")
    public List<AuthorizationGroup> getAllAuthGroupDetails() {
        List<AuthorizationGroup> authorizationGroups = loadAuthGroupsByLoggedInUsersClientId();
        List<AuthorizationGroup> assignableAuthGroups = new ArrayList<>();
        // the the properties this user has access to
        List<Property> usersProperties = retrieveAuthorizedProperties();
        List<Integer> userPropertyIds = new ArrayList<>();
        populateUserPropertyIds(usersProperties, userPropertyIds);
        for (AuthorizationGroup authorizationGroup : authorizationGroups) {
            if (authorizationGroup.getRuleId() != null) {
                List<CustomAttributeSearchCriteria> customAttributeSearchCriteria = rulesService.getSearchCriteria(authorizationGroup
                        .getRuleId());
                if (customAttributeSearchCriteria != null && !customAttributeSearchCriteria.isEmpty()) {
                    List<Integer> propertyIds = searcher.searchForPropertiesByCustomAttributeValue(customAttributeSearchCriteria);

                    authorizationGroup.setMappedProperties(removeDuplicates(propertyIds));
                }
            } else {
                List<Integer> propertyIdswithoutRank = new ArrayList<>();
                Set<AuthorizationGroupPropertyMapping> authMappping = authorizationGroup.getAuthGroupPropertyMappings();
                Iterator<AuthorizationGroupPropertyMapping> itMap = authMappping.iterator();
                while (itMap.hasNext()) {
                    AuthorizationGroupPropertyMapping objMapping = itMap.next();
                    int iPropId = objMapping.getPropertyId();
                    propertyIdswithoutRank.add(iPropId);
                }
                authorizationGroup.setMappedProperties(removeDuplicates(propertyIdswithoutRank));
            }

            // mark all the auth groups that this user has access to
            List<Integer> mappedProperties = authorizationGroup.getMappedProperties();
            if (usersProperties != null && userPropertyIds.containsAll(mappedProperties)) {
                assignableAuthGroups.add(authorizationGroup);
            }
        }
        return assignableAuthGroups;
    }

    private void populateUserPropertyIds(List<Property> usersProperties, List<Integer> userPropertyIds) {
        for (Property objProperty : usersProperties) {
            userPropertyIds.add(objProperty.getId());
        }
    }

    public List<AuthorizationGroupSummary> getAllAuthorizationGroupSummaries(boolean includeAllPropertiesEntry) {
        List<AuthorizationGroupSummary> authorizationGroups = globalCrudService.findByNamedQuery(
                AuthorizationGroupSummary.BY_CLIENT_ID,
                QueryParameter.with(AuthorizationGroupSummary.PARAM_CLIENT_ID,
                        PlatformThreadLocalContextHolder.getWorkContext().getClientId()).parameters());
        for (AuthorizationGroupSummary authorizationGroup : authorizationGroups) {
            List<Integer> propertyIds = new ArrayList<>();
            Set<Property> properties = authorizationGroup.getProperties();
            for (Property property : properties) {
                propertyIds.add(property.getId());
            }
            authorizationGroup.setMappedPropertyIds(new LinkedHashSet<>(propertyIds));
        }
        if (includeAllPropertiesEntry) {
            authorizationGroups.add(0, AuthorizationGroupSummary.ALL_PROPS_AUTH_GROUP);
        }
        return authorizationGroups;
    }

    public List<Integer> getAuthGroupPropertyIDs(Integer authGroupID) throws ExplosionException {
        return explodeAuthGroup(authGroupID);
    }

    public List<Integer> explodeAuthGroup(Integer authGroupID) {
        if (AuthorizationGroup.ALL_PROP_ID.intValue() == authGroupID) {
            List<Property> properties = getAllClientProperties(PlatformThreadLocalContextHolder.getWorkContext().getClientId());
            return null != properties ? properties.stream().map(Property::getId).collect(Collectors.toList()) : Collections.EMPTY_LIST;
        }
        return globalCrudService.findByNamedQuery(AuthorizationGroupPropertyMapping.GET_PROPERTIES_IN_AUTH_GROUP,
                QueryParameter.with("authGroupId", authGroupID).parameters());
    }

    public Set<String> getPermsForContext(boolean atLeastOnePropertyHasFeatureToggleOn) {
        return getPermsForContext(atLeastOnePropertyHasFeatureToggleOn, PacmanWorkContextHelper.getWorkContext());
    }

    public Set<String> getPermsForContext(boolean atLeastOnePropertyHasFeatureToggleOn, WorkContextType wc) {
        Set<String> perms = new HashSet<>();

        TetrisPrincipal principal = PacmanThreadLocalContextHolder.getPrincipal();

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("getPermsForContext() - wc.propertyId: " + (wc != null ? wc.getPropertyId() : "NULL"));
        }
        if (principal == null) {
            LOGGER.error("Cannot determine permissions for current user - the principal in the pacmanThreadLocalContext is NULL!");
        } else if (wc == null) {
            LOGGER.error("Cannot determine permissions for current user - work context in the pacmanThreadLocalContext is NULL!");
        } else if (wc.getPropertyId() == null) {
            getPermissions(wc, perms, principal);
        } else {
            String propertyId = Integer.toString(wc.getPropertyId());
            perms = getPermsForUserAndProperty(principal.getDN(), propertyId, atLeastOnePropertyHasFeatureToggleOn);
        }
        if (LOGGER.isDebugEnabled()) {
            if (wc != null) {
                LOGGER.debug("getPermsForContext() - wc.propertyId: " + wc.getPropertyId());
            }
            LOGGER.debug("getPermsForContext() - user perms.size(): " + (perms != null ? perms.size() : "NULL"));
            LOGGER.debug("getPermsForContext() - user perms: " + perms);
        }
        return perms;
    }

    private void getPermissions(WorkContextType wc, Set<String> perms, TetrisPrincipal principal) {
        Long numberOfClientProperties = globalCrudService.findByNamedQuerySingleResult(Property.NUMBER_OF_PROPERTIES_FOR_CLIENT,
                QueryParameter.with(Property.PARAM_CLIENT_ID, wc.getClientId()).parameters());

        if (numberOfClientProperties != null && numberOfClientProperties == 0) {
            LOGGER.debug("Special case. Client has no properties. Sending flag to client.");
            perms.add(Role.NO_CLIENT_PROPS_ID);
        } else {
            // A user that has yet to be assigned any auth group / prop
            // roles is valid use case
            LOGGER.warn("User does not have a property id. User: " + principal.getName());
        }
    }

    public String getPermsForRequestedPage(String pageCode) {
        Set<String> allPermissions = getPermsForContext(false);
        Iterator<String> permissionsIterator = allPermissions.iterator();
        while (permissionsIterator.hasNext()) {
            String pageCodePermission = permissionsIterator.next();
            if (pageCodePermission.contains(Role.ALL_PERMS_ID)) {
                return PermissionKeys.READ_WRITE_ACCESS.getLabel();
            } else if (pageCodePermission.contains(pageCode)) {
                return StringUtils.substringAfterLast(pageCodePermission, LDAPConstants.SEPERATOR);
            }
        }
        return null;
    }

    /**
     * Retrieve permissions - also filtered by global config params, for some
     * permissions
     */
    public Set<String> getPermsForUserAndProperty(String dn, String propertyId, boolean atLeastOnePropertyHasFeatureToggleOn) {
        Set<String> perms = new HashSet<>();


        // Check to see if user has been switched
        String userSwitch = PlatformThreadLocalContextHolder.getUserSwitch();
        if (userSwitch != null) {
            dn = userSwitch;
        }

        // If the MockRoleTokenStore is enabled, then we should check it to see
        // if
        // a mock role exists for this principal's token.
        if (MockRoleTokenStore.isEnabled()) {
            // Get the LDAPRole for the Principal
            Role role = MockRoleTokenStore.getMockRole();
            if (role != null) {
                // If the LDAPRole has permissions, add them to the perms
                // variable.
                perms.addAll(role.getPermissions());
            }
        }

        // If the perms Set is empty, that means that we are either not enabled
        // for mock roles
        // or no mock role is set up for the principal. If that is the case,
        // load the perms
        // as you normally would.
        if (perms.isEmpty()) {
            try {
                perms = roleService.getPermsForUser(dn, propertyId, this);
            } catch (LDAPException lde) {
                LOGGER.error("LDAPError finding permissions for: " + dn + " and propertyID: " + propertyId, lde);
            }
        }
        if (userIsExternal() || userSwitch != null) {
            removeFeaturePermissions(perms, atLeastOnePropertyHasFeatureToggleOn);
            removePermissionsWhenPropertyIsNotReadyToHandover(perms);
        }
        return perms;
    }

    private boolean userIsExternal() {
        GlobalUser user = userGlobalDBService.getGlobalUserById(Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        return !user.isInternal();
    }

    public Map<String, Boolean> getPermsFeatureMappingForContext(boolean atLeastOnePropertyHasFeatureToggleOn) {
        Map<String, Boolean> permissionFeatureMapping = new HashMap<>();
        Set<String> featurePermissions = PERM_FEATURE_MAPPING.keySet();

        for (String featurePermission : featurePermissions) {
            String featureName = PERM_FEATURE_MAPPING.get(featurePermission);
            boolean isEnabled = isFeatureEnabled(featureName, atLeastOnePropertyHasFeatureToggleOn);
            permissionFeatureMapping.put(featurePermission, isEnabled);
        }
        return permissionFeatureMapping;
    }

    public boolean checkFeatureToggleEnabledForClientOrAtLeastOneProperty(String featureName) {
        return atLeastOnePropertyHasAccessToFeature(featureName);
    }

    public boolean checkFeatureToggleDisabledForAtLeastOneProperty(String featureName) {
        return atLeastOnePropertyHasFeatureDisabled(featureName);
    }

    private boolean getBooleanConfigurationValue(String featureName) {
        return pacmanConfigParamsService.getBooleanParameterValue(featureName);
    }

    private void removeFeaturePermissions(Set<String> permissions, boolean atLeastOnePropertyHasFeatureToggleOn) {
        Set<String> revokedPermissions = new HashSet<>();
        Set<String> featurePermissions = PERM_FEATURE_MAPPING.keySet();

        for (String permission : permissions) {
            //convert the permission string into an actual permission that will parse out its parts
            Permission userPermission = new Permission(permission);

            String featureName = "";
            for (String featurePermission : featurePermissions) {
                if (new Permission(featurePermission).getPageCode().equalsIgnoreCase(userPermission.getPageCode())) {
                    featureName = PERM_FEATURE_MAPPING.get(featurePermission);
                    break;
                }
            }
            if (StringUtils.isNotEmpty(featureName)) {
                if (featureName.equalsIgnoreCase(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value())
                        && userPermission.getPageCode().equalsIgnoreCase(TetrisPermissionKey.FUNCTION_SPACE_CONFIGURATION)) {
                    if (!pacmanConfigParamsService.getBooleanParameterValue(featureName)) {
                        featureName = FeatureTogglesConfigParamName.ALLOW_FUNCTION_SPACE_CONFIGURATION.value();
                    }
                }

                removePermissionIfConfigDisabled(revokedPermissions, permission, featureName, atLeastOnePropertyHasFeatureToggleOn);
            }
        }
        if (!revokedPermissions.isEmpty()) {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("Will remove perms: " + revokedPermissions);
            }
            permissions.removeAll(revokedPermissions);
        }

    }

    private void removePermissionsWhenPropertyIsNotReadyToHandover(Set<String> permissions) {
        if (pacmanConfigParamsService.getParameterValue(GUIConfigParamName.IS_PROPERTY_READY_FOR_EXTERNAL_USER)) {
            return;
        }
        LOGGER.info("System access for external users is disabled.");
        Set<String> allowedPermissions = createDefaultListOfPermissionsBeforePropertyHandover();
        if (!permissions.contains(Role.ALL_PERMS_ID)) {
            final Set<String> allowedPages = new HashSet<>(allowedPermissions);
            allowedPermissions = permissions.stream()
                    .filter(permission -> isAccessToPageAllowed(permission, allowedPages))
                    .collect(Collectors.toSet());
        }
        LOGGER.info("Restricting user access to " + allowedPermissions + " from " + permissions);
        permissions.clear();
        permissions.addAll(allowedPermissions);
    }

    private Set<String> createDefaultListOfPermissionsBeforePropertyHandover() {
        Set<String> defaultAllowedPermissions = new HashSet<>();
        defaultAllowedPermissions.add(createPermissionString(TetrisPermissionKey.CONFIGURE));
        defaultAllowedPermissions.add(createPermissionString(TetrisPermissionKey.AUTHORIZATIONS));
        defaultAllowedPermissions.add(createPermissionString(TetrisPermissionKey.ROLE_MANAGEMENT));
        defaultAllowedPermissions.add(createPermissionString(TetrisPermissionKey.USER_MANAGEMENT));
        defaultAllowedPermissions.add(createPermissionString(TetrisPermissionKey.AUTHORIZATION_GROUP_MANAGEMENT));
        if (pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_CLIENT_QUESTIONNAIRE)) {
            defaultAllowedPermissions.add(createPermissionString(TetrisPermissionKey.CLIENT_QUESTIONNAIRE));
        }
        return defaultAllowedPermissions;
    }

    private String createPermissionString(String allowedPage) {
        return LDAPConstants.PAGE_CODE_KEY_VALUE +
                allowedPage +
                ACCESS_READ_WRITE;
    }

    private boolean isAccessToPageAllowed(final String permission, final Set<String> allowedPages) {
        return allowedPages.stream()
                .anyMatch(allowedPermission -> new Permission(allowedPermission).getPageCode().equalsIgnoreCase(new Permission(permission).getPageCode()));
    }

    private void removePermissionIfConfigDisabled(Set<String> remove, String perm, String featureName,
                                                  boolean atLeastOnePropertyHasFeatureToggleOn) {
        boolean enabled = false;
        enabled = isFeatureEnabled(featureName, atLeastOnePropertyHasFeatureToggleOn);

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("removePermissionIfConfigDisabled() _ featureName: " + featureName + ", enabled: " + enabled);
        }
        if (!enabled) {
            remove.add(perm);
        }
    }

    private boolean isFeatureEnabled(String featureName, boolean atLeastOnePropertyHasFeatureToggleOn) {
        boolean enabled;
        if (atLeastOnePropertyHasFeatureToggleOn) {
            enabled = atLeastOnePropertyHasAccessToFeature(featureName);
        } else {
            enabled = getBooleanConfigurationValue(featureName);
        }
        return enabled;
    }

    public boolean isAuthGroupOptimizationEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IMPROVE_PERFORMANCE_FOR_AUTHORIZATION_GROUP);
    }

    public boolean isFDSAuthGroupEnabled() {
        return Boolean.parseBoolean(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.getParameterName()));
    }

    /*
     * isAssignableByUser is marked true or false depending on if user has
     * authorization to the auth group. needed for user management when user A
     * with auth group AG1 is viewing user B with AG2. DE2506
     */
    @SuppressWarnings("unchecked")
    public List<AuthorizationGroup> getAuthorizationGroupsWithAssignableFlag(boolean isInternal) {
        List<AuthorizationGroup> authorizationGroups = loadAuthGroupsByLoggedInUsersClientId();
        return getAssignableGroups(isInternal, authorizationGroups);
    }

    public List<UserAuthGroupRole> getAuthorizationGroupForUser(Integer userId) {

        return globalCrudService.findByNamedQuery(UserAuthGroupRole.FIND_AUTH_GROUP_ID_FOR_USER,
                QueryParameter.with("globaluserId", userId).parameters());
    }

    public List<GlobalRole> getRoles(List<Integer> roleIds) {
        return globalCrudService.findByNamedQuery(GlobalRole.BY_IDS,
                QueryParameter.with("ids", roleIds).parameters());
    }

    public List<UserIndividualPropertyRole> getRoleIdAndPropoertIdForUser(Integer userId) {
        return globalCrudService.findByNamedQuery(UserIndividualPropertyRole.FIND_Role_FOR_USER_ID,
                QueryParameter.with("userId", userId).parameters());
    }

    private List loadAuthGroupsByLoggedInUsersClientId() {
        return globalCrudService.findByNamedQuery(AuthorizationGroup.BY_CLIENT_ID, QueryParameter
                .with(AuthorizationGroup.PARAM_CLIENT_ID, PlatformThreadLocalContextHolder.getWorkContext().getClientId()).parameters());
    }

    public List<AuthorizationGroup> getAuthGroupsByClientId(Integer clientId) {
        return globalCrudService.findByNamedQuery(AuthorizationGroup.BY_CLIENT_ID, QueryParameter
                .with(AuthorizationGroup.PARAM_CLIENT_ID, clientId).parameters());
    }

    public List<AuthorizationGroup> getAllAssignableAuthGroupDetails(boolean isInternal) {
        return getAssignableGroups(isInternal, getAllAuthGroupDetails());
    }

    public AuthorizationGroup persistAuthorizationGroup(AuthorizationGroup authGroup, List<CustomAttributeSearchCriteria> searchCriteria) {
        List<Integer> authorizedPropertyIds = new ArrayList<>();
        AuthorizationGroup authorizationGroup = null;
        if (authGroup.getId() == null) {
            return saveNewAuthGroup(authGroup, searchCriteria);
        }
        AuthorizationGroup dbAuthGroup = loadAuthGroup(authGroup);
        modifyBasicInfoOfAuthGroup(authGroup, dbAuthGroup);

        List<AuthorizationGroupPropertyMapping> removedMappings = new ArrayList<>();
        if (null == searchCriteria && null == authGroup.getRuleId()) {
            deleteAuthGroupRuleAndMapping(authGroup, authorizedPropertyIds, dbAuthGroup, removedMappings);
        } else {
            updateRuleAndSearchCriteriaMapping(searchCriteria, dbAuthGroup);
        }
        authorizationGroup = globalCrudService.save(dbAuthGroup);
        if (isAuthGroupOptimizationEnabled()) {
            LOGGER.debug("This is the optimized code getting executed");
            List<Integer> affectedUsers = authGroupManagementService.save(authorizationGroup);

            Integer clientId = PacmanWorkContextHelper.getClientId();
            updatePropertySet(clientId, affectedUsers);

            if (null == authGroup.getRuleId()) {
                LOGGER.debug("Update cache and cleanup LDAP for auth group users starts here. " + DateUtil.getCurrentDate().toString());
                updateCacheAndClearDefaultPropertyFromLdapForAuthGroup(clientId, affectedUsers);
                LOGGER.debug("Update cache and cleanup LDAP for auth group users ends here. " + DateUtil.getCurrentDate().toString());
            }
        } else {
            LOGGER.debug("This is the non-optimized code getting executed");
            syncPropertyGroupAuthGroupOfAllUser(authGroup, removedMappings);
            updatePropertySetForAllUsersOfThisAuthGroupID(authGroup.getId(), getClientCodeFromWorkContext());

            if (null == authGroup.getRuleId()) {
                LOGGER.debug("Update cache and cleanup LDAP for auth group users starts here. " + DateUtil.getCurrentDate().toString());
                updateCacheAndClearDefaultPropertyFromLdapForAuthGroup(authGroup, authorizedPropertyIds);
                LOGGER.debug("Update cache and cleanup LDAP for auth group users ends here. " + DateUtil.getCurrentDate().toString());
            }
        }
        if (isFDSAuthGroupEnabled()) {
            LOGGER.debug("Update FDS for auth group starts here. " + DateUtil.getCurrentDate().toString());
            uasService.migrateAuthGroupToFDS(authorizationGroup);
            LOGGER.debug("Update FDS for auth group ends here. " + DateUtil.getCurrentDate().toString());
        }
        return authorizationGroup;
    }

    public void updateCacheAndClearDefaultPropertyFromLdapForAuthGroup(AuthorizationGroup authGroup, List<Integer> authorizedPropertyIds) {
        Set<LDAPUser> users = getUserForAuthGroup(authGroup.getId(), getClientCodeFromWorkContext());
        for (LDAPUser ldapUser : users) {
            userAuthorizedPropertyCache.put(PacmanWorkContextHelper.getClientId(), ldapUser.getUserId(), authorizedPropertyIds);

            // check if authorizedPropertyIds does not contain default property
            // then remove default property entry for that user
            // Why didn't we just inject UserServiceLocal?
            // UserService userServiceLocal = getBeanLocator().getPacmanBean(UserService.class, "UserService");
            Integer userId = ldapUser.getUserId();
            WorkContextType workContextType = PacmanWorkContextHelper.getWorkContext();
            TetrisPrincipal tetrisPrincipal = PacmanThreadLocalContextHolder.getPrincipal();
            asyncUserService.clearUnathourizedDefaultPropertyFromLdap(userId, authorizedPropertyIds, workContextType, tetrisPrincipal);
        }
    }

    public void updatePropertySetForAllUsersOfThisAuthGroupID(int authGroupId, String clientCode) {
        Integer clientId = loadClientIdByCode(clientCode);
        Set<LDAPUser> users = getUserForAuthGroup(authGroupId, clientCode);

        if (checkValidClientAndUsers(users, clientId)) {
            updatePropertySet(clientId, users);
        }
    }

    private void updatePropertySet(int clientId, Set<LDAPUser> users) {
        for (LDAPUser ldapUser : users) {
            List<PropertyGroup> ruleBasedPropertyGroupList = propertyGroupService.getAllRuleBasedPropertyGroupsForUser(
                    ldapUser.getUserId(), clientId);
            if (null != ruleBasedPropertyGroupList && !ruleBasedPropertyGroupList.isEmpty()) {
                for (PropertyGroup propertyGroup : ruleBasedPropertyGroupList) {
                    rulesService.updatePropertySet(propertyGroup);
                }
            }
        }
    }

    private void syncPropertyGroupAuthGroupOfAllUser(AuthorizationGroup authGroup, List<AuthorizationGroupPropertyMapping> removedMappings) {
        if (CollectionUtils.isNotEmpty(removedMappings)) {
            synchronizePropertyGroupsForAllUsersOfThisAuthorizationGroupOfThisClient(authGroup.getId(), getClientCodeFromWorkContext());
        }
    }

    private void updateRuleAndSearchCriteriaMapping(List<CustomAttributeSearchCriteria> searchCriteria, AuthorizationGroup dbAuthGroup) {
        if (dbAuthGroup.getRuleId() != null) {
            rulesService.updateSearchCriteria(dbAuthGroup.getRuleId(), searchCriteria);
        } else {
            dbAuthGroup.setRuleId(rulesService.saveSearchCriteria(searchCriteria));
        }
        rulesService.updatePropertySetFromAuthGroup(dbAuthGroup);
    }

    private String getClientCodeFromWorkContext() {
        return WorkContextHelper.getCurrent().getClientCode();
    }

    private void deleteAuthGroupRuleAndMapping(AuthorizationGroup authGroup, List<Integer> authorizedPropertyIds, AuthorizationGroup dbAuthGroup, List<AuthorizationGroupPropertyMapping> removedMappings) {
        if (dbAuthGroup.getRuleId() != null) {
            rulesService.deleteRule(dbAuthGroup.getRuleId());
        }
        dbAuthGroup.setRuleId(null);

        for (AuthorizationGroupPropertyMapping mapping : dbAuthGroup.getAuthGroupPropertyMappings()) {
            if (!isPropertyInMappings(authGroup, mapping.getPropertyId())) {
                removedMappings.add(mapping);
                globalCrudService.delete(AuthorizationGroupPropertyMapping.class, mapping.getId());
            }
        }
        dbAuthGroup.getAuthGroupPropertyMappings().removeAll(removedMappings);
        addAuthGroupMappings(authorizedPropertyIds, authGroup, dbAuthGroup);
    }

    private AuthorizationGroup loadAuthGroup(AuthorizationGroup authGroup) {
        return globalCrudService.findByNamedQuerySingleResult(AuthorizationGroup.BY_ID,
                QueryParameter.with("id", authGroup.getId()).parameters());
    }

    private void modifyBasicInfoOfAuthGroup(AuthorizationGroup authGroup, AuthorizationGroup dbAuthGroup) {
        dbAuthGroup.setName(authGroup.getName());
        dbAuthGroup.setDescription(authGroup.getDescription());
        dbAuthGroup.setStatusId(authGroup.getStatusId());
    }

    private AuthorizationGroup saveNewAuthGroup(AuthorizationGroup authGroup, List<CustomAttributeSearchCriteria> searchCriteria) {
        if (authGroup.getClientId() == null) {
            authGroup.setClientId(PlatformThreadLocalContextHolder.getWorkContext().getClientId());
        }
        authGroup.setStatusId(Constants.ACTIVE_STATUS_ID);
        if (hasSearchCriteria(searchCriteria)) {
            authGroup.setRuleId(rulesService.saveSearchCriteria(searchCriteria));
        }
        AuthorizationGroup ag = globalCrudService.save(authGroup);

        if (hasSearchCriteria(searchCriteria)) {
            rulesService.updatePropertySet(ag);
        }

        if (isFDSAuthGroupEnabled()) {
            LOGGER.debug("Update FDS for auth group starts here. " + DateUtil.getCurrentDate().toString());
            uasService.migrateAuthGroupToFDS(ag);
            LOGGER.debug("Update FDS for auth group ends here. " + DateUtil.getCurrentDate().toString());
        }

        return ag;
    }

    private boolean hasSearchCriteria(List<CustomAttributeSearchCriteria> searchCriteria) {
        return null != searchCriteria && !searchCriteria.isEmpty();
    }

    private List<AuthorizationGroup> getAssignableGroups(boolean isInternal, List<AuthorizationGroup> userAuthGroups) {
        // all the existing auth groups
        List<AuthorizationGroup> assignableAuthGroups = new ArrayList<>();

        // Note: Technically we could remove all auth groups if isInternal as
        // internal users
        // are relegated to a single "All Properties" auth group
        if (isInternal) {
            // add that crazy ALL PROP auth group if user has all props
            assignableAuthGroups.add(AuthorizationGroup.ALL_PROPS_AUTH_GROUP_ASSIGNABLE);
            if (!userAuthGroups.isEmpty()) {
                // Internal users should be only "All Properties" auth group
                LOGGER.debug("Not including " + userAuthGroups.size() + " assignable auth group options from internal user mgmt");
            }
        } else {
            // the the properties this user has access to
            List<Property> userProperties = retrieveAuthorizedProperties();

            Long numberOfClientProperties = globalCrudService.findByNamedQuerySingleResult(Property.NUMBER_OF_PROPERTIES_FOR_CLIENT,
                    QueryParameter.with(Property.PARAM_CLIENT_ID, PlatformThreadLocalContextHolder.getWorkContext().getClientId())
                            .parameters());
            // Note: Currently retrieveAuthorizedProperties is NOT filtering out
            // Inactive properties so that
            // UI can still resolve roles applied to inactive properties
            if (userProperties.size() == numberOfClientProperties.intValue()) {
                // add that crazy ALL PROP auth group if user has all props
                assignableAuthGroups.add(AuthorizationGroup.ALL_PROPS_AUTH_GROUP_ASSIGNABLE);
            } else {
                assignableAuthGroups.add(AuthorizationGroup.ALL_PROPS_AUTH_GROUP_UNASSIGNABLE);
            }
            Set<AuthorizationGroupPropertyMapping> authGroupPropertyMap;
            boolean isAssignableByUser = false;
            // mark all the auth groups that this user has access to
            for (AuthorizationGroup authorizationGroup : userAuthGroups) {
                authGroupPropertyMap = authorizationGroup.getAuthGroupPropertyMappings();
                // No properties in auth group is NOT assignable (TTRS-3116)
                isAssignableByUser = !authGroupPropertyMap.isEmpty() && areAllPropsInAuthGroupAvailableToThisUser(userProperties,
                        authGroupPropertyMap);
                authorizationGroup.setAssignableByTheCurrentUser(isAssignableByUser);
                assignableAuthGroups.add(authorizationGroup);
            }
        }
        return assignableAuthGroups;
    }

    public Optional<AuthorizationGroup> getAuthorizationGroupByName(String authGroupName) {
        if (AuthorizationGroup.ALL_PROP_NAME.equals(authGroupName)) {
            return Optional.of(AuthorizationGroup.ALL_PROPS_AUTH_GROUP_ASSIGNABLE);
        }

        List<AuthorizationGroup> authGroups = globalCrudService.findByNamedQuery(AuthorizationGroup.BY_NAME,
                QueryParameter.with("name", authGroupName).and("clientId", PacmanWorkContextHelper.getClientId()).parameters());
        return authGroups.stream().findFirst();
    }

    /*
     * are all the properties contained in the authGroup available to the
     * current user?
     */
    public boolean areAllPropsInAuthGroupAvailableToThisUser(List<Property> usersProperties,
                                                      Set<AuthorizationGroupPropertyMapping> authGroupMappingSet) {
        final Set<Integer> userPropertyIds = usersProperties.stream()
                .map(Property::getId)
                .collect(Collectors.toSet());

        for (AuthorizationGroupPropertyMapping mapping : authGroupMappingSet) {
            Integer property = mapping.getPropertyId();
            if (usersProperties != null && !userPropertyIds.contains(property)) {
                return false;
            }
        }
        return true;
    }

    public boolean isAuthGroupInUse(int authGroupId) {
        // There is no "internal" context for authorization groups. Internal
        // users only have "All Properties" auth group
        String client = getClientCodeFromWorkContext();
        return isAuthGroupInUse(authGroupId, client);
    }

    @SuppressWarnings("all")
    private List removeDuplicates(List items) {
        Set set = new LinkedHashSet();
        set.addAll(items);
        return new ArrayList(set);
    }

    public boolean isAuthGroupInUse(int authGroupId, String client) {
        Set<LDAPUser> users = loadUsersForAuthGroup(authGroupId, client);
        return isNotEmpty(users);
    }

    private void addAuthGroupMappings(List<Integer> authorizedPropertyIds, AuthorizationGroup authGroup, AuthorizationGroup dbAuthGroup) {
        // Add any new mappings that are present in the user modified instance
        for (AuthorizationGroupPropertyMapping mapping : authGroup.getAuthGroupPropertyMappings()) {
            if (!isPropertyInMappings(dbAuthGroup, mapping.getPropertyId())) {
                mapping.setPropertyId(mapping.getPropertyId());
                mapping.setAuthorizationGroup(dbAuthGroup);
                mapping.setStatusId(Constants.ACTIVE_STATUS_ID);
                dbAuthGroup.getAuthGroupPropertyMappings().add(mapping);
                mapping = globalCrudService.save(mapping);
            }
            authorizedPropertyIds.add(mapping.getPropertyId());
        }
    }

    public void updateCacheAndClearDefaultPropertyFromLdapForAuthGroup(Integer clientId, List<Integer> usersAuthPropertiesMap) {

        usersAuthPropertiesMap.forEach((user) ->
                userAuthorizedPropertyCache.remove(clientId, user)
        );
    }

    private boolean isPropertyInMappings(AuthorizationGroup authGroup, Integer propertyId) {
        boolean isPresent = false;
        for (AuthorizationGroupPropertyMapping mapping : authGroup.getAuthGroupPropertyMappings()) {
            if (mapping.getPropertyId().equals(propertyId)) {
                isPresent = true;
                break;
            }
        }
        return isPresent;
    }

    public boolean isActivateDeactivateAllowedForSelectedUsers(List<Object> selectedUsers) {
        String loggedInUserId = PacmanWorkContextHelper.getUserId();
        List<Integer> userIdsToBeEdited = selectedUsers.stream().map(user -> ((LDAPUser) user).getUserId()).collect(Collectors.toList());

        List<Object[]> authGroupIds = globalCrudService.findByNativeQuery(GET_USER_AUTH_GROUP_IDS_FOR_SELECTED_USERS,
                QueryParameter.with("loggedInUserId", loggedInUserId)
                        .and("usersToBeEdited", userIdsToBeEdited).parameters());

        if (!authGroupIds.isEmpty()) {
            if (isSelectedUserAssignedToAllProperties(authGroupIds, LOGGED_USER_AUTH_GROUP_ID_QUERY_INDEX)) {
                return true;
            } else if (isSelectedUserAssignedToAllProperties(authGroupIds, SELECTED_USERS_AUTH_GROUP_ID_QUERY_INDEX)) {
                return false;
            }
        }
        return !isEditedUsersHavingMoreAssignedProperties(loggedInUserId, userIdsToBeEdited);
    }

    private boolean isEditedUsersHavingMoreAssignedProperties(String loggedInUserId, List<Integer> userIdsToBeEdited) {
        int countOfAdditionalProperties = globalCrudService.findByNativeQuerySingleResult(IS_EDITED_USERS_HAVING_MORE_ASSIGNED_PROPERTIES, QueryParameter.with("loggedInUserId", loggedInUserId)
                .and("usersToBeEdited", userIdsToBeEdited).parameters());
        return countOfAdditionalProperties > 0;

    }

    private boolean isSelectedUserAssignedToAllProperties(List<Object[]> authGroupIds, int index) {
        for (int count = 0; count < authGroupIds.size(); count++) {
            Object[] row = authGroupIds.get(count);
            if (row[index] != null && (Integer) row[index] == -666) {
                return true;
            }
        }
        return false;
    }

    public void saveAuthGroup(AuthorizationGroup authGroup) {
        globalCrudService.save(authGroup);
    }

    /**
     * Provide an exploder that will cache found properties Can this be done
     * better (as in, without loading every property object, returning the IDs,
     * then getting the properties based on the IDs?)
     */
    class PropertyAuthGroupExploder implements AuthGroupExploder {
        private Integer clientId;

        public PropertyAuthGroupExploder(final Integer clientId) {
            this.clientId = clientId;
        }

        @Override
        public List<Integer> getAuthGroupPropertyIDs(Integer authGroupID) {
            // Skip explodeAuthGroup logic if authgroup is ALL PROPERTIES group
            if (AuthorizationGroup.ALL_PROP_ID.intValue() == authGroupID) {
                LOGGER.debug("This is an internal user - clientId: " + clientId);
                List<Property> properties = getAllClientProperties(clientId);
                LOGGER.debug("retrieved # properties: " + (properties != null ? properties.size() : 0));
                return null != properties ? properties.stream().map(Property::getId).collect(Collectors.toList()) : Collections.EMPTY_LIST;
            }
            LOGGER.debug("calling the EXPLODE auth group");
            return explodeAuthGroup(authGroupID);
        }
    }

    public List<Property> getAllClientProperties(Integer clientId) {
        return clientPropertyCacheService.getClientPropertiesByClientId(clientId);
    }

    public List<Property> retrieveAuthorizedProperties() {
        Integer userId = Integer.valueOf(PlatformThreadLocalContextHolder.getWorkContext().getUserId());
        Integer clientId = PlatformThreadLocalContextHolder.getWorkContext().getClientId();

        return retrieveAuthorizedProperties(userId, clientId);
    }

    public List<Property> retrieveActiveAuthorizedProperties() {
        List<Property> authorizedProperties = retrieveAuthorizedProperties();
        return authorizedProperties.stream().filter(Property::isActive).collect(Collectors.toList());
    }

    public List<Property> retrieveAuthorizedProperties(Integer userId, Integer clientId) {
        return retrieveAuthorizedProperties(userId, clientId, true);
    }

    /*
     * Could look up client from LDAPUser but is this always accurate with
     * internal users Keep passing in for now
     */
    public List<Property> retrieveAuthorizedProperties(Integer userId, Integer clientId, Boolean attemptToUseCache) {
        boolean debugEnabled = LOGGER.isDebugEnabled();
        LOGGER.debug("retrieveAuthorizedProperties( userId: " + userId + ", clientId: " + clientId + ")");
        List<Property> properties = new ArrayList<>();

        try {
            StopWatch stop1 = new StopWatch();
            stop1.start();
            LDAPUser user = userService.getById(userId.toString());
            stop1.stop();
            if (debugEnabled) {
                LOGGER.debug("userService.getById() time:" + stop1.toString());
            }
            // Might be better if we did not catch this. We are hiding serious
            // issues.
            if (user == null) {
                LOGGER.error("Could not find user: " + userId);
                return properties;
            }
            PropertyAuthGroupExploder exploder = createPropertyAuthGroupExploder(clientId);

            StopWatch stop2 = new StopWatch();
            stop2.start();
            Set<Integer> propertyIdsSet = new HashSet<>();
            List<Integer> propertyIdsList = new ArrayList<>();
            if (attemptToUseCache) {
                propertyIdsList = userAuthorizedPropertyCache.get(clientId, userId);
            }
            if (CollectionUtils.isEmpty(propertyIdsList)) {
                Set<String> propertyIdsStringsFromDB = roleService.getPropertiesForUser(user.getDN(), exploder);
                Set<Integer> propertyIdsIntegersFromDB = propertyIdsStringsFromDB.stream()
                        .map(Integer::parseInt)
                        .collect(Collectors.toSet());
                propertyIdsSet.addAll(propertyIdsIntegersFromDB);
            } else {
                propertyIdsSet.addAll(propertyIdsList);
            }

            stop2.stop();
            if (debugEnabled) {
                LOGGER.debug("roleService.getPropertiesForUser() time:" + stop2.toString());
            }

            if (isNotEmpty(propertyIdsSet)) {
                StopWatch stop4 = new StopWatch();
                stop4.start();
                properties = addAllPropertiesUsedByClient(propertyIdsSet, clientId);
                stop4.stop();
                if (debugEnabled) {
                    LOGGER.debug("addAllPropertiesUsedByClient() time:" + stop4.toString());
                }
            }
        } catch (LDAPException lde) {
            LOGGER.error("Failed to get authorized properties for user ", lde);
        }
        StopWatch stop5 = new StopWatch();
        stop5.start();
        sortByDisplayLabelField(properties);
        stop5.stop();
        if (debugEnabled) {
            LOGGER.debug("sortByDisplayLabelField() time:" + stop5.toString());
        }

        return properties;
    }

    protected PropertyAuthGroupExploder createPropertyAuthGroupExploder(Integer clientId) {
        return new PropertyAuthGroupExploder(clientId);
    }

    public void sortByDisplayLabelField(List<Property> properties) {
        properties.sort((p1, p2) -> p1.getDisplayLabelField().compareToIgnoreCase(p2.getDisplayLabelField()));
    }

    @SuppressWarnings("squid:S3776")
    @Justification("The cognitive complexity is high due to a temporary system config toggle. The dead code will eventually be removed")
    private List<Property> addAllPropertiesUsedByClient(Set<Integer> propertyIds, Integer clientId) {
        List<Property> properties = new ArrayList<>();
        Client client = getClient(clientId);
        String codeOrName = displayLabel("pacman." + client.getCode());

        if (SystemConfig.isAuthServiceFewerCacheHitsEnabled()) {
            // Get all of the properties for the Client
            Map<Integer, Property> clientProperties = getAllClientProperties(clientId).stream().collect(Collectors.toMap(Property::getId, Function.identity()));

            for (Integer propertyId : propertyIds) {
                Property property = clientProperties.get(propertyId);

                if (property != null) {
                    property.setDisplayLabelField(property.evaluateDisplayLabel(codeOrName));
                    properties.add(property);
                }
            }
        } else {
            Set<Property> allClientProperties = new HashSet<>(getAllClientProperties(clientId));

            boolean notEmpty = isNotEmpty(allClientProperties);

            for (Integer propertyId : propertyIds) {
                Property property = clientPropertyCacheService.getProperty(propertyId);

                if (property != null) {
                    String displayLabel = property.evaluateDisplayLabel(codeOrName);
                    property.setDisplayLabelField(displayLabel);
                    if (notEmpty && allClientProperties.contains(property)) {
                        properties.add(property);
                    }
                }
            }
        }

        return properties;
    }

    private String displayLabel(String context) {
        return pacmanConfigParamsService.getValue(context, GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value());
    }

    public Property retrieveAuthorizedProperty(Integer propertyId) {
        Property target = clientPropertyCacheService.getProperty(propertyId);
        if (target == null) {
            target = globalCrudService.find(Property.class, propertyId);
        }
        return retrieveAuthorizedProperty(PacmanThreadLocalContextHolder.getPrincipal(), target);
    }

    public Property retrieveAuthorizedProperty(TetrisPrincipal tp, Property target) {
        Client client = target != null ? target.getClient() : null;

        if (tp == null || client == null) {
            LOGGER.warn("retrieveAuthorizedProperty() - principal or client supplied is NULL");
            return null;
        }
        Property result = null;

        // Principal name is user uid
        Integer userId = Integer.valueOf(tp.getName());
        List<Integer> authorizedProperties = retrieveAuthorizedPropertyIds(userId, client.getId());
        if (authorizedProperties.contains(target.getId())) {
            result = target;
        }
        LOGGER.debug("retrieveAuthorizedProperty(tp, target) - pid: " + target.getId() + ", found: "
                + (result != null ? result.getName() : "NULL"));
        return result;
    }

    public List<Integer> retrieveAuthorizedPropertyIds(Integer userId, Integer clientId) {
        List<Integer> propertyIds = userAuthorizedPropertyCache.get(clientId, userId);
        if (CollectionUtils.isEmpty(propertyIds)) {
            propertyIds = new ArrayList<>();

            List<Property> properties = retrieveAuthorizedProperties(userId, clientId, false);

            populateUserPropertyIds(properties, propertyIds);

            if (CollectionUtils.isNotEmpty(propertyIds)) {
                userAuthorizedPropertyCache.put(clientId, userId, propertyIds);
            }
        }
        return propertyIds;
    }

    @Transactional(propagation = Propagation.NEVER)
    public List<Client> retrieveClientByUser() {
        try {
            if (isInternalUser()) {
                return clientConfigService.getAllClientDetails();
            } else {
                // there should only be one for an external user
                Client client = clientPropertyCacheService.getClient(PlatformThreadLocalContextHolder.getWorkContext().getClientId());
                return Collections.singletonList(client);
            }
        } catch (Exception e) {
            throw new TetrisException(ErrorCode.SERVICE_ERROR, "Failed to find client", e);
        }
    }

    public Client getClient(Integer clientId) {
        return clientPropertyCacheService.getClient(clientId);
    }

    public boolean isInternalUser() {
        TetrisPrincipal principal = PacmanThreadLocalContextHolder.getPrincipal();
        return principal != null && principal.isInternalUser();
    }

    public List<Map<String, Object>> getClients() {
        List<Client> clients = retrieveClientByUser();
        List<Map<String, Object>> result = new ArrayList<>();
        Map<String, Object> client;
        for (Client c : clients) {
            client = new HashMap<>();
            client.put("code", c.getCode());
            client.put("id", c.getId());
            client.put("name", c.getName());
            result.add(client);
        }
        return result;
    }

    public List<Map<String, Object>> getProperties() {
        List<Property> properties = retrieveAuthorizedProperties();
        List<Map<String, Object>> result = new ArrayList<>();
        Map<String, Object> property;
        for (Property p : properties) {
            property = new HashMap<>();
            property.put("id", p.getId());
            property.put("statusId", p.getStatus().getId());
            property.put("displayLabelField", p.getDisplayLabelField());
            property.put("propertyCode", p.getCode());
            property.put("clientCode", p.getClient().getCode());
            result.add(property);
        }
        return result;
    }

    public List<PropertyGroup> retrieveAuthorizedPropertyGroups() {
        // Currently not checking that user still has access to properties in
        // their groups
        List<PropertyGroup> groups = propertyGroupService.getAllPropertyGroupsForCurrentUser();
        // Sort by name, for display
        groups.sort((p1, p2) -> p1.getName().compareToIgnoreCase(p2.getName()));
        return groups;
    }

    public PropertyGroup retrieveAuthorizedPropertyGroup(int propertyGroupId) {
        List<PropertyGroup> authorizedGroups = retrieveAuthorizedPropertyGroups();
        for (PropertyGroup group : authorizedGroups) {
            if (group.getId().equals(propertyGroupId)) {
                return group;
            }
        }
        return null;
    }

    public List<Map<String, Object>> getPropertyGroups() {
        List<PropertyGroup> groups = retrieveAuthorizedPropertyGroups();
        List<Map<String, Object>> result = new ArrayList<>();
        Map<String, Object> group;
        for (PropertyGroup pg : groups) {
            group = new HashMap<>();
            group.put("id", pg.getId());
            group.put("name", pg.getName());
            group.put("isDefaultPropertyGroup", pg.getIsDefaultPropertyGroup());
            group.put("isValidGroup", pg.isValidGroup());
            result.add(group);
        }
        return result;
    }

    public Map<Integer, BigDecimal> getCapacityForAuthorizedProperties() {
        return getCapacityForAuthorizedProperties(retrieveAuthorizedProperties());
    }

    public Map<Integer, BigDecimal> getCapacityForAuthorizedProperties(List<Property> propertyList) {
        return propertyCapacityCache.getCapacitiesForProperties(propertyList);
    }

    /**
     * Sums up capacity value across property list
     */
    public BigDecimal getCapacitySumForProperties(List<Property> properties) {
        LOGGER.debug("getCapacitySumForProperties() - #properties: " + (properties != null ? properties.size() : "NULL"));

        // gather capacities from group
        BigDecimal result = BigDecimal.ZERO;
        Map<Integer, BigDecimal> propertyCapacities = propertyCapacityCache.getCapacitiesForProperties(properties);
        if (null != propertyCapacities) {
            for (BigDecimal value : propertyCapacities.values()) {
                if (value.intValue() > 0) { // We store empty as INT.MIN
                    result = result.add(value);
                }
            }
        }
        return result;
    }

    public Map<Integer, BigDecimal> getCapacityForAuthorizedPropertyGroups() {
        Map<Integer, BigDecimal> results = new HashMap<>();
        List<PropertyGroup> groups = retrieveAuthorizedPropertyGroups();
        List<Property> authorizedProperties = this.retrieveAuthorizedProperties(Integer.parseInt(PacmanWorkContextHelper.getWorkContext()
                .getUserId()), PacmanWorkContextHelper.getWorkContext().getClientId());
        for (PropertyGroup group : groups) {
            results.put(group.getId(), getPropertyGroupCapacity(group.getId(), group, authorizedProperties));
        }
        return results;
    }

    public BigDecimal getCapacityForPropertyGroup(PropertyGroup propertyGroup, List<Property> properties) {
        if (propertyGroup == null) {
            throw TetrisSecurityException.USER_LACKS_PROPERTYGROUP_ACCESS;
        }
        return getPropertyGroupCapacity(propertyGroup.getId(), propertyGroup, properties);
    }

    /**
     * Fetch capacity for group selected in work context
     *
     * @return BigDecimal representing sum of capacity for properties in group
     */
    public BigDecimal getPropertyGroupCapacity() {
        return getPropertyGroupCapacity(PacmanWorkContextHelper.getPropertyGroupId());
    }

    public BigDecimal getPropertyGroupCapacity(int propertyGroupId) {
        LOGGER.debug("getPropertyGroupCapacity() - pgid: " + propertyGroupId);

        PropertyGroup target = retrieveAuthorizedPropertyGroup(propertyGroupId);

        if (target == null) {
            throw TetrisSecurityException.USER_LACKS_PROPERTYGROUP_ACCESS;
        }
        return getCapacitySumForProperties(getAuthorizedProperties(target));
    }

    public BigDecimal getPropertyGroupCapacity(int propertyGroupId, PropertyGroup
            target, List<Property> authorizedProperties) {
        LOGGER.debug("getPropertyGroupCapacity() - pgid: " + propertyGroupId);
        if (target == null) {
            throw TetrisSecurityException.USER_LACKS_PROPERTYGROUP_ACCESS;
        }
        List<Property> authorizedGroupProperties = new ArrayList<>();
        Set<PropertyPropertyGroup> propertyPropertyGroups = target.getPropertyPropertyGroups();
        if (null != propertyPropertyGroups) {
            Property property = null;
            for (PropertyPropertyGroup ppg : propertyPropertyGroups) {
                property = ppg.getProperty();
                if (authorizedProperties.contains(property)) {
                    authorizedGroupProperties.add(property);
                }
            }
        }
        return getCapacitySumForProperties(authorizedGroupProperties);
    }

    // Rest interface - Used for one time sync up - Remove Mappings
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<PropertyGroupOneTimeCleanUpDto> synchronizePropertyGroupsForAllUsers(String clientCode) {
        return syncRetrivePropertyGroupsForAllUsers(clientCode, false);
    }

    private List<PropertyGroupOneTimeCleanUpDto> syncRetrivePropertyGroupsForAllUsers(String clientCode,
                                                                                      boolean retrive) {
        List<PropertyGroupOneTimeCleanUpDto> propertyGroupDtoList = new ArrayList<>();
        int clientId = loadClientIdByCode(clientCode);
        Set<LDAPUser> users = listAllUsersForClient(clientCode);
        if (isNotEmpty(users)) {
            for (LDAPUser ldapUser : users) {
                List<PropertyGroup> propertyGroupList = propertyGroupService.getAllPropertyGroupsForUser(ldapUser.getUserId(), clientId);
                if (isNotEmpty(propertyGroupList)) {
                    List<Property> authorizedPropertyList = retrieveAuthorizedProperties(ldapUser.getUserId(), clientId);
                    if (retrive) {
                        propertyGroupService.retrievePropertyGroupMappingForUser(propertyGroupList, authorizedPropertyList,
                                propertyGroupDtoList);
                    } else {
                        propertyGroupService.updatePropertyGroupMappingForUser(propertyGroupList, authorizedPropertyList, propertyGroupDtoList);
                    }
                }
            }
        }
        return propertyGroupDtoList;
    }

    private void updatePropertySet(int clientId, List<Integer> users) {
        for (Integer user : users) {
            List<PropertyGroup> ruleBasedPropertyGroupList = propertyGroupService.getAllRuleBasedPropertyGroupsForUser(user, clientId);
            List<Integer> authorizedPropertyIntegerListOfTheUser = retrieveAuthorizedProperties(
                    user, clientId).stream()
                    .map(Property::getId).collect(Collectors.toList());
            ruleBasedPropertyGroupList.forEach(propertyGroup -> rulesService.updatePropertySetForAuthGroupManagement(propertyGroup, authorizedPropertyIntegerListOfTheUser));
        }
    }

    // Rest interface - Used for Impact Analysis for one time sync up activity -
    // Mappings eligible for Removal
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<PropertyGroupOneTimeCleanUpDto> retrieveSynchronizationDataForPropertyGroupsForAllUsers(String clientCode) {
        return syncRetrivePropertyGroupsForAllUsers(clientCode, true);
    }

    // Rest interface - Used for Impact Analysis for one time sync up activity -
    // Mappings eligible for Addition
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<PropertyGroupOneTimeCleanUpDto> synchronizePropertyGroupsForAllUsersADDAnalysis(String clientCode) {
        List<PropertyGroupOneTimeCleanUpDto> propertyGroupDtoList = new ArrayList<>();
        Set<LDAPUser> users = listAllUsersForClient(clientCode);
        if (isNotEmpty(users)) {
            for (LDAPUser ldapUser : users) {
                List<PropertyGroup> ruleBasedPropertyGroupList = propertyGroupService.getAllRuleBasedPropertyGroupsForUser(ldapUser.getUserId(), loadClientIdByCode(clientCode));
                if (null != ruleBasedPropertyGroupList && !ruleBasedPropertyGroupList.isEmpty()) {
                    for (PropertyGroup propertyGroup : ruleBasedPropertyGroupList) {
                        rulesService.identifyPropertyPGMappingsEligibleForAddition(propertyGroup, propertyGroupDtoList);
                    }
                }
            }
        }
        return propertyGroupDtoList;
    }

    public void synchronizePropertyGroupsForAllUsersOfThisAuthorizationGroupOfThisClient(int authGroupId, String
            clientCode) {
        Integer clientId = loadClientIdByCode(clientCode);
        Set<LDAPUser> users = getUserForAuthGroup(authGroupId, clientCode);
        updatePropertyGroupsForUsers(users, clientId);
    }

    public void synchronizePropertyGroupsForTheseUserForAuthGroupDeletion(Set<LDAPUser> users, String clientCode) {
        Integer clientId = loadClientIdByCode(clientCode);
        updatePropertyGroupsForUsers(users, clientId);
    }

    private void updatePropertyGroupsForUsers(Set<LDAPUser> users, Integer clientId) {
        if (checkValidClientAndUsers(users, clientId)) {
            for (LDAPUser ldapUser : users) {
                List<PropertyGroup> propertyGroupList = propertyGroupService.getAllPropertyGroupsForUser(ldapUser.getUserId(), clientId);
                if (isNotEmpty(propertyGroupList)) {
                    List<Property> authorizedPropertyList = retrieveAuthorizedProperties(ldapUser.getUserId(), clientId);
                    propertyGroupService.updatePropertyGroupsForUser(propertyGroupList, authorizedPropertyList);
                }
            }
        }
    }

    private Integer loadClientIdByCode(String clientCode) {
        return clientConfigService.getClientByCode(clientCode).getId();
    }

    private boolean checkValidClientAndUsers(Set<LDAPUser> users, Integer clientId) {
        return isNotEmpty(users) && null != clientId;
    }

    public Set<LDAPUser> getUserForAuthGroup(int authGroupId, String clientCode) {
        return loadUsersForAuthGroup(authGroupId, clientCode);
    }

    private Set<LDAPUser> loadUsersForAuthGroup(int authGroupId, String clientCode) {
        return userGlobalDBService.listUsersForAuthGroup(authGroupId, clientCode);
    }

    public AuthorizationGroupSummary getAuthorizationGroupSummary(int authGroupId) {
        if (AuthorizationGroup.ALL_PROP_ID == authGroupId) {
            return AuthorizationGroupSummary.ALL_PROPS_AUTH_GROUP;
        }
        return globalCrudService.find(AuthorizationGroupSummary.class, authGroupId);
    }

    public List<Property> subsetCompare(int parentAuthGroupId, int childAuthGroupId) {
        // if not pacmanThreadLocalService isCurrentUserInternalUser
        // throw new TetrisException ErrorCode SECURITY_ERROR - Must be internal
        // to view the internals
        List<Property> missingProperties = new ArrayList<>();
        Set<Property> parentProperties = getAuthorizationGroupSummary(parentAuthGroupId).getProperties();
        AuthorizationGroupSummary childAuthGroup = getAuthorizationGroupSummary(childAuthGroupId);
        for (Property property : childAuthGroup.getProperties()) {
            if (!parentProperties.contains(property)) {
                missingProperties.add(property);
            }
        }
        return missingProperties;
    }

    public Map<String, String> visitThePrincipalsOffice() {
        TetrisPrincipal principle = (TetrisPrincipal) PacmanThreadLocalContextHolder.get(SSO_USER_PRINCIPAL);
        return null != principle ? principle.getAttributes() : new HashMap<>();
    }

    public List<Property> findClientProperties(final Client client) {
        return globalCrudService
                .findByNativeQuery(
                        "select p.property_id, p.property_code, p.property_name, p.status_id, p.stage from Property as p where p.client_id=:clientId",
                        QueryParameter.with(Property.PARAM_CLIENT_ID, client.getId()).parameters(), row -> {
                            Property property = buildPropertyFromDataRow(row);
                            property.setClient(client);
                            return property;
                        });
    }

    private Property buildPropertyFromDataRow(Object[] row) {
        Property property = new Property();
        property.setId((Integer) row[0]);
        property.setCode((String) row[1]);
        property.setName((String) row[2]);
        property.setStatus(Status.valueOfId((Integer) row[3]));

        if (row[4] != null) {
            property.setStage(Stage.valueOf((String) row[4]));
        }
        return property;
    }

    public void cleanUpEmptyAuthGroups(Integer clientId) {
        List<Integer> emptyAuthGroupIds = globalCrudService.findByNamedQuery(AuthorizationGroup.FIND_EMPTY_AUTH_GROUPS_BY_CLIENT,
                QueryParameter.with(AuthorizationGroup.PARAM_CLIENT_ID, clientId).parameters());

        if (isNotEmpty(emptyAuthGroupIds)) {
            List<Integer> userBelongsToEmptyAuthGroups = globalCrudService.findByNamedQuery(
                    UserAuthGroupRole.FIND_USERS_FOR_AUTH_GROUP_IDS, QueryParameter.with("authGroupIds", emptyAuthGroupIds).parameters());
            globalCrudService.executeUpdateByNamedQuery(UserAuthGroupRole.DELETE_BY_AUTH_GROUP_IDS,
                    QueryParameter.with("authGroupIds", emptyAuthGroupIds).parameters());
            // clear permissions from ldap for userBelongsToEmptyAuthGroups
            clearPermissionsForUsersWhoHasEmptyAuthGroups(emptyAuthGroupIds, userBelongsToEmptyAuthGroups);
        }
    }

    private void clearPermissionsForUsersWhoHasEmptyAuthGroups
            (List<Integer> emptyAuthGroupIds, List<Integer> userBelongsToEmptyAuthGroups) {
        userService.cleanEmptyAuthGroupsFromUserPermissions(userBelongsToEmptyAuthGroups, emptyAuthGroupIds);
    }

    public boolean atLeastOnePropertyHasAccessToFeature(String featureName) {
        List hasAccessToAnyProperty = globalCrudService.findByNativeQuery(AT_LEAST_ONE_PROPERTY_HAVING_FEATURE_ENABLED,
                QueryParameter.with("clientContext", "pacman." + PacmanThreadLocalContextHolder.getWorkContext().getClientCode() + ".%")
                        .and("featureName", featureName).parameters());
        boolean trueAtAnyPropertyLevel = CollectionUtils.isNotEmpty(hasAccessToAnyProperty);
        if (trueAtAnyPropertyLevel) {
            return trueAtAnyPropertyLevel;
        }
        String trueAtClientOrPacmanLevel = pacmanConfigParamsService.getParameterValueByClientLevel(featureName);
        return Boolean.valueOf(trueAtClientOrPacmanLevel);
    }

    public boolean atLeastOnePropertyHasFeatureDisabled(String featureName) {
        List disabledForAnyProperty = globalCrudService.findByNativeQuery(AT_LEAST_ONE_PROPERTY_HAVING_FEATURE_DISABLED,
                QueryParameter.with("clientContext", "pacman." + PacmanThreadLocalContextHolder.getWorkContext().getClientCode() + ".%")
                        .and("featureName", featureName).parameters());
        boolean falseAtAnyPropertyLevel = CollectionUtils.isNotEmpty(disabledForAnyProperty);
        if (falseAtAnyPropertyLevel) {
            return falseAtAnyPropertyLevel;
        }

        int totalPropertiesOfClient = globalCrudService.findByNamedQuery(Property.BY_CLIENT_CODE_ACTIVE, QueryParameter.with("clientCode", PacmanWorkContextHelper.getClientCode()).parameters()).size();
        List hasAccessToAnyProperty = globalCrudService.findByNativeQuery(AT_LEAST_ONE_PROPERTY_HAVING_FEATURE_ENABLED,
                QueryParameter.with("clientContext", "pacman." + PacmanThreadLocalContextHolder.getWorkContext().getClientCode() + ".%")
                        .and("featureName", featureName).parameters());
        return hasAccessToAnyProperty.size() != totalPropertiesOfClient;
    }

    public List<GlobalUser> getUsersForAuthGroupNameAndRoleId(String authGroupName, String roleId) {
        Optional<AuthorizationGroup> authorizationGroup = getAuthorizationGroupByName(authGroupName);
        int authGroupId;
        if (authorizationGroup.isPresent()) {
            authGroupId = authorizationGroup.get().getId();
        } else {
            throw new TetrisException(ErrorCode.INVALID_INPUT, String.format("Authorization group %s is not valid.", authGroupName));
        }
        return userGlobalDBService.listUsersForAuthGroupIdAndRoleId(authGroupId, roleId);
    }

    public void processAuthGroupCreateUpdateFromFDS(Client client, String authGroupUuid) {
        AuthorizationGroup authorizationGroup = getAuthGroupByUasAuthGroupUuid(authGroupUuid, client.getId());
        if (authorizationGroup == null) {
            createAuthGroupFromFDS(client, authGroupUuid);
        } else {
            LOGGER.info("Updating role as it already exists with this UasAuthGroupUuid: " + authGroupUuid);
            updateAuthGroupFromFDS(client, authGroupUuid, authorizationGroup);
        }
    }

    public void processAuthGroupDeleteFromFDS(Client client, String authGroupUuid) {
        AuthorizationGroup authorizationGroup = getAuthGroupByUasAuthGroupUuid(authGroupUuid, client.getId());
        if (authorizationGroup != null) {
            //We don't need to see if the Auth Group is assigned to a user as the UI allows it to be deleted without un-assigning
            deleteAuthorizationGroup(authorizationGroup.getId(), client.getCode());
        } else {
            LOGGER.info("Auth Group does not exist with this UasAuthGroupUuid: " + authGroupUuid);
        }
    }

    public AuthorizationGroup getAuthGroupByUasAuthGroupUuid(String authGroupId, Integer clientId) {
        return globalCrudService.findByNamedQuerySingleResult(AuthorizationGroup.BY_UAS_UUID,
                QueryParameter.with("authGroupId", authGroupId)
                        .and("clientId", clientId).parameters());
    }

    @VisibleForTesting
    public void createAuthGroupFromFDS(Client client, String authGroupUuid) {
        AuthorizationGroup authorizationGroup = new AuthorizationGroup();
        //Get Auth Group from UAS
        UASAuthGroup authGroupFromFDS = uasService.getAuthGroupFromFDS(authGroupUuid);

        if (authGroupFromFDS == null) {
            LOGGER.error("Error creating authGroupId: " + authGroupUuid + " for clientId: " + client.getUpsClientUuid() + ", as the auth group doesn't exist in FDS");
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error creating authGroupId: " + authGroupUuid + " for clientId: " + client.getUpsClientUuid() + ", as the auth group doesn't exist in FDS");
        }

        if (!authGroupFromFDS.getName().equalsIgnoreCase("All Properties")) {
            //Get all properties for client
            Map<String, Property> clientPropertiesMap = getClientUPSPropertiesMapByClientId(client.getId());

            //Create Rule
            Integer ruleId = null;
            if (authGroupFromFDS.getRule() != null && !authGroupFromFDS.getRule().isEmpty()) {
                Rule rule = createRule(client.getId(), authGroupFromFDS.getRule());
                ruleId = rule.getId();
            }

            //Create Property Mappings
            Set<AuthorizationGroupPropertyMapping> authorizationGroupPropertyMappings = new HashSet<>();
            List<Integer> mappedProperties = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(authGroupFromFDS.getPropertyList())) {
                authorizationGroupPropertyMappings.addAll(createAuthorizationGroupPropertyMappings(authorizationGroup, clientPropertiesMap, authGroupFromFDS.getPropertyList()));
            }

            //Create Auth Group
            authorizationGroup.setClientId(client.getId());
            authorizationGroup.setName(authGroupFromFDS.getName());
            authorizationGroup.setDescription(authGroupFromFDS.getDescription());
            authorizationGroup.setRuleId(ruleId);
            authorizationGroup.setAuthGroupPropertyMappings(authorizationGroupPropertyMappings);
            if (CollectionUtils.isNotEmpty(authorizationGroupPropertyMappings)) {
                mappedProperties = authorizationGroupPropertyMappings.stream().map(AuthorizationGroupPropertyMapping::getPropertyId).collect(Collectors.toList());
            }
            authorizationGroup.setMappedProperties(removeDuplicates(mappedProperties));
            authorizationGroup.setUasAuthGroupUuid(authGroupFromFDS.getAuthGroupId().toString());
            authorizationGroup.setStatusId(authGroupFromFDS.getStatus());

            saveAuthGroup(authorizationGroup);
        }
    }

    @VisibleForTesting
    public Map<String, Property> getClientUPSPropertiesMapByClientId(Integer clientId) {
        List<Property> clientProperties = clientPropertyCacheService.getClientPropertiesByClientId(clientId);
        return getClientUPSPropertiesMapByClientId(clientId, clientProperties);
    }

    private Map<String, Property> getClientUPSPropertiesMapByClientId(Integer clientId, List<Property> clientProperties) {
        //convert list to map
        Map<String, Property> clientPropertiesMap = new HashMap<>();
        clientProperties.forEach(property -> {
            if (property.getUpsId() != null) {
                clientPropertiesMap.put(property.getUpsId(), property);
            }
        });
        return clientPropertiesMap;
    }

    private Map<Integer, Property> getClientPropertiesMapByClientId(Integer clientId, List<Property> clientProperties) {
        //convert list to map
        Map<Integer, Property> clientPropertiesMap = new HashMap<>();
        clientProperties.forEach(property -> {
            if (property.getId() != null) {
                clientPropertiesMap.put(property.getId(), property);
            }
        });
        return clientPropertiesMap;
    }

    @VisibleForTesting
    public Set<AuthorizationGroupPropertyMapping> createAuthorizationGroupPropertyMappings(AuthorizationGroup authorizationGroup, Map<String, Property> clientPropertiesMap, List<String> propertyList) {
        Set<AuthorizationGroupPropertyMapping> authorizationGroupPropertyMappings = new HashSet<>();

        propertyList.forEach(upsId -> {
            //Only add properties that exist in this environment as the auth group may have properties that only exist for other products
            Property property = clientPropertiesMap.get(upsId);
            if (property != null) {
                AuthorizationGroupPropertyMapping authorizationGroupPropertyMapping = new AuthorizationGroupPropertyMapping();
                authorizationGroupPropertyMapping.setPropertyId(property.getId());
                authorizationGroupPropertyMapping.setAuthorizationGroup(authorizationGroup);
                authorizationGroupPropertyMapping.setStatusId(Status.ACTIVE.getId());
                authorizationGroupPropertyMappings.add(authorizationGroupPropertyMapping);
            }
        });

        return authorizationGroupPropertyMappings;
    }

    @VisibleForTesting
    public Rule createRule(Integer clientId, Map<Integer, UASRuleDetails> ruleFromUAS) {
        List<ConjunctionType> conjunctionTypes = globalCrudService.findAll(ConjunctionType.class);
        List<ConditionType> conditionTypes = globalCrudService.findAll(ConditionType.class);
        List<ClientAttribute> clientAttributes = customAttributeService.getClientAttributeListByClientId(clientId);
        List<ClientAttributeValue> clientAttributeValues = customAttributeService.getClientAttributeValuesListByClientId(clientId);

        Rule rule = new Rule();
        rule.setClientId(clientId);

        Set<RuleAttributeValueMapping> ruleAttributeValueMappings = new HashSet<>();
        ruleFromUAS.forEach((rank, uasRuleDetails) -> {
            RuleAttributeValueMapping ruleAttributeValueMapping = new RuleAttributeValueMapping();
            ruleAttributeValueMapping.setRule(rule);
            ruleAttributeValueMapping.setRanking(rank);
            ruleAttributeValueMapping.setClientAttributeValue(getClientAttributeValue(clientAttributes, clientAttributeValues, uasRuleDetails));
            ruleAttributeValueMapping.setConjunctionType(getConjunctionType(conjunctionTypes, uasRuleDetails.getConjunctionType()));
            ruleAttributeValueMapping.setConditionType(getConditionType(conditionTypes, uasRuleDetails.getConditionType()));
            ruleAttributeValueMapping.setCreateDate(LocalDateTime.now());

            ruleAttributeValueMappings.add(ruleAttributeValueMapping);
        });
        rule.setRuleAttributeValueMappings(ruleAttributeValueMappings);
        rule.setCreateDate(LocalDateTime.now());
        rule.setCreatedByUserId(Constants.SYSTEM_USER_ID);

        globalCrudService.save(rule);

        return rule;
    }

    @VisibleForTesting
    public void deleteRule(Integer ruleId) {
        rulesService.deleteRule(ruleId);
    }

    @VisibleForTesting
    public ClientAttributeValue getClientAttributeValue(List<ClientAttribute> clientAttributes, List<ClientAttributeValue> clientAttributesValues, UASRuleDetails uasRuleDetails) {
        //find client attribute from attribute UUID
        ClientAttribute clientAttribute = clientAttributes.stream().filter(ca -> ca.getUpsCustomAttributeUuid() != null && ca.getUpsCustomAttributeUuid().equalsIgnoreCase(uasRuleDetails.getCustomAttributeId().toString())).findFirst()
                .orElseThrow(() -> new TetrisException(UNEXPECTED_ERROR, "Unable to find ClientAttribute for UPS Custom Attribute UUID: " + uasRuleDetails.getCustomAttributeId().toString()));

        //find client attribute value; we should only be sending 1 value instead of a list otherwise the rank will get out of order
        ClientAttributeValue clientAttributeValue = clientAttributesValues.stream().filter(cav -> cav.getClientAttribute().equals(clientAttribute) && cav.getClientAttributeValue().equalsIgnoreCase(uasRuleDetails.getCustomAttributeValues().get(0))).findFirst()
                .orElseThrow(() -> new TetrisException(UNEXPECTED_ERROR, "Unable to find ClientAttributeValue for UPS Custom Attribute UUID: " + uasRuleDetails.getCustomAttributeId().toString() + " and value: " + uasRuleDetails.getCustomAttributeValues().get(0)));

        return clientAttributeValue;
    }

    @VisibleForTesting
    public ConditionType getConditionType(List<ConditionType> conditionTypes, String conditionType) {
        //Convert FDS condition type to G3 string
        String convertedConditionType = conditionType.replaceAll("_", " ");
        return conditionTypes.stream().filter(ct -> ct.getName().equalsIgnoreCase(convertedConditionType)).findFirst()
                .orElseThrow(() -> new TetrisException(UNEXPECTED_ERROR, "Unable to find ConditionType for: " + convertedConditionType));
    }

    @VisibleForTesting
    public ConjunctionType getConjunctionType(List<ConjunctionType> conjunctionTypes, String conjunctionType) {
        return conjunctionTypes.stream().filter(ct -> ct.getName().equalsIgnoreCase(conjunctionType)).findFirst()
                .orElseThrow(() -> new TetrisException(UNEXPECTED_ERROR, "Unable to find ConjunctionType for: " + conjunctionType));
    }

    public void updateAuthGroupFromFDS(String clientUuid, String authGroupUuid) {
        Client client = clientService.findClientByUpsClientUuid(clientUuid);
        if (client != null) {
            AuthorizationGroup authorizationGroup = getAuthGroupByUasAuthGroupUuid(authGroupUuid, client.getId());
            if (authorizationGroup != null) {
                updateAuthGroupFromFDS(client, authGroupUuid, authorizationGroup);
            } else {
                createAuthGroupFromFDS(client, authGroupUuid);
            }
        } else {
            LOGGER.error("Error updating authGroupId: " + authGroupUuid + ", as the client doesn't exist with clientId: " + clientUuid);
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error updating authGroupId: " + authGroupUuid + ", as the client doesn't exist with clientId: " + clientUuid);
        }
    }

    @VisibleForTesting
    public void updateAuthGroupFromFDS(Client client, String authGroupUuid, AuthorizationGroup authorizationGroup) {
        //Get Auth Group from UAS
        UASAuthGroup authGroupFromFDS = uasService.getAuthGroupFromFDS(authGroupUuid);

        if (authGroupFromFDS == null) {
            LOGGER.error("Error updating authGroupId: " + authGroupUuid + " for clientId: " + client.getUpsClientUuid() + ", as the auth group doesn't exist in FDS");
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Error updating authGroupId: " + authGroupUuid + " for clientId: " + client.getUpsClientUuid() + ", as the auth group doesn't exist in FDS");
        }

        //Get all properties for client
        List<Property> clientProperties = clientPropertyCacheService.getClientPropertiesByClientId(client.getId());
        Map<String, Property> clientUPSPropertiesMap = getClientUPSPropertiesMapByClientId(client.getId(), clientProperties);
        Map<Integer, Property> clientG3PropertiesMap = getClientPropertiesMapByClientId(client.getId(), clientProperties);

        //Update Rule
        Integer ruleId = authorizationGroup.getRuleId();
        if (ruleId != null) {
            //Delete old rule if defined and recreate down below
            //We do not check if other Auth Groups or Property groups use the rule as the UI always creates new rules
            deleteRule(ruleId);
            ruleId = null;
        }

        if (authGroupFromFDS.getRule() != null && !authGroupFromFDS.getRule().isEmpty()) {
            //Create rule if defined
            Rule rule = createRule(client.getId(), authGroupFromFDS.getRule());
            ruleId = rule.getId();
        }

        //Create/Delete Auth Group property mappings as needed
        Set<AuthorizationGroupPropertyMapping> existingAuthGroupPropertyMappings = authorizationGroup.getAuthGroupPropertyMappings();
        Map<Integer, AuthorizationGroupPropertyMapping> existingAuthGroupPropertyMappingsMap = existingAuthGroupPropertyMappings.stream().collect(
                Collectors.toMap(AuthorizationGroupPropertyMapping::getPropertyId, mapping -> mapping));
        Set<AuthorizationGroupPropertyMapping> updatedAuthGroupPropertyMappings = new HashSet<>();
        List<AuthorizationGroupPropertyMapping> authGroupPropertyMappingsToBeDeleted = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(authGroupFromFDS.getPropertyList())) {
            existingAuthGroupPropertyMappingsMap.forEach((integer, authorizationGroupPropertyMapping) -> {
                //find G3 Property, so I can get the Ups ID
                Property property = clientG3PropertiesMap.get(integer);
                if (property != null) {
                    //find mappings to remove
                    if (property.getUpsId() != null && !authGroupFromFDS.getPropertyList().contains(property.getUpsId())) {
                        authGroupPropertyMappingsToBeDeleted.add(authorizationGroupPropertyMapping);
                    } else {
                        updatedAuthGroupPropertyMappings.add(authorizationGroupPropertyMapping);
                    }
                }
            });

            //Delete existing mappings that are no longer in the property list
            if (CollectionUtils.isNotEmpty(authGroupPropertyMappingsToBeDeleted)) {
                authorizationGroup.getAuthGroupPropertyMappings().removeAll(authGroupPropertyMappingsToBeDeleted);
                List<Integer> propertiesToDelete = authGroupPropertyMappingsToBeDeleted.stream().map(AuthorizationGroupPropertyMapping::getPropertyId).collect(Collectors.toList());
                rulesService.removeMappingsFromAuthorizationGroupPropertyMapping(propertiesToDelete, authorizationGroup.getId());
            }

            authGroupFromFDS.getPropertyList().forEach(entry -> {
                //find mappings to add
                Property property = clientUPSPropertiesMap.get(entry);
                if (property != null && (existingAuthGroupPropertyMappingsMap == null || existingAuthGroupPropertyMappingsMap.get(property.getId()) == null)) {
                    AuthorizationGroupPropertyMapping authorizationGroupPropertyMapping = new AuthorizationGroupPropertyMapping();
                    authorizationGroupPropertyMapping.setPropertyId(property.getId());
                    authorizationGroupPropertyMapping.setAuthorizationGroup(authorizationGroup);
                    authorizationGroupPropertyMapping.setStatusId(Status.ACTIVE.getId());
                    authorizationGroup.getAuthGroupPropertyMappings().add(authorizationGroupPropertyMapping);
                    updatedAuthGroupPropertyMappings.add(authorizationGroupPropertyMapping);
                }
            });
        } else {
            //Delete all existing mappings if there are none in FDS
            if (CollectionUtils.isNotEmpty(existingAuthGroupPropertyMappings)) {
                crudService.delete(existingAuthGroupPropertyMappings);
            }
        }

        //Update Auth Group
        authorizationGroup.setClientId(client.getId());
        authorizationGroup.setName(authGroupFromFDS.getName());
        authorizationGroup.setDescription(authGroupFromFDS.getDescription());
        authorizationGroup.setRuleId(ruleId);
        authorizationGroup.setUasAuthGroupUuid(authGroupFromFDS.getAuthGroupId().toString());
        authorizationGroup.setStatusId(authGroupFromFDS.getStatus());

        saveAuthGroup(authorizationGroup);
    }

    public String getConfigParamForFeature(String permissionString) {
        String permissionKey = LDAPConstants.PAGE_CODE_KEY_VALUE + permissionString;
        return PERM_FEATURE_MAPPING.get(permissionKey);
    }
}
