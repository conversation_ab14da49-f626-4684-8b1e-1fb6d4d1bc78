package com.ideas.tetris.pacman.services.discontinuedroomtypes;

import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.common.xml.schema.ISASRequest;
import com.ideas.tetris.pacman.common.xml.schema.discontinuedroomtypes.request.v1.DiscontinuedRoomTypesRequestType;
import com.ideas.tetris.pacman.common.xml.schema.discontinuedroomtypes.request.v1.DiscontinuedRoomTypesSASRequest;
import com.ideas.tetris.pacman.common.xml.schema.discontinuedroomtypes.request.v1.RequestHeaderType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.sasinvocationbase.AbstractSasInvocation;
import com.ideas.tetris.pacman.util.jaxb.discontinuedroomtypes.request.DiscontinuedRoomTypesRequestJAXBUtil;
import com.ideas.tetris.pacman.util.jaxb.discontinuedroomtypes.response.DiscontinuedRoomTypesResponseJAXBUtilResponse;
import com.ideas.tetris.platform.common.util.jaxb.JAXBUtilLocal;
import com.ideas.tetris.platform.common.utils.systemconfig.SASSettings;

import javax.inject.Inject;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class DiscontinuedRoomTypesService extends AbstractSasInvocation<DiscontinuedRoomTypesRequestType> {

    private static final String OPERATION_NAME = "discontinuedroomtypes";
    @DiscontinuedRoomTypesRequestJAXBUtil.DiscontinuedRoomTypesRequestQualifier
    @Autowired
	@Qualifier("discontinuedRoomTypesRequestJAXBUtil")
	private JAXBUtilLocal requestJaxbUtil;
    @DiscontinuedRoomTypesResponseJAXBUtilResponse.DiscontinuedRoomTypesResponseQualifier
    @Autowired
	@Qualifier("discontinuedRoomTypesResponseJAXBUtilResponse")
	private JAXBUtilLocal responseJaxbUtil;

    public void execute() {
        DiscontinuedRoomTypesRequestType discontinuedRoomTypesRequestDataType = new DiscontinuedRoomTypesRequestType();
        execute(discontinuedRoomTypesRequestDataType);
    }


    @Override
    protected String getOperationName() {
        return OPERATION_NAME;
    }

    @Override
    protected JAXBUtilLocal getJaxbUtil() {
        return requestJaxbUtil;
    }

    public void setJaxbUtil(DiscontinuedRoomTypesRequestJAXBUtil discontinuedRoomTypesRequestJaxbUtil) {
        this.requestJaxbUtil = discontinuedRoomTypesRequestJaxbUtil;
    }

    @Override
    protected JAXBUtilLocal getResponseJaxbUtil() {
        return responseJaxbUtil;
    }

    @Override
    protected String getStoredProc() {
        return SASSettings.getDefaultStoredProcName();
    }

    @Override
    protected String getRequestMap() {
        return SASSettings.getMapFileLocation() + "discontinuedRoomTypes.map";
    }

    @SuppressWarnings("rawtypes")
    @Override
    protected ISASRequest getSASRequest(DiscontinuedRoomTypesRequestType requestType, Decision decision) {
        DiscontinuedRoomTypesSASRequest sasRequest = new DiscontinuedRoomTypesSASRequest();
        sasRequest.setRequestHeader(new RequestHeaderType());
        requestType.setClientCode(PacmanWorkContextHelper.getClientCode());
        requestType.setPropertyCode(PacmanWorkContextHelper.getPropertyCode());
        requestType.setSasDataSetPath(SystemConfig.getSasRatchetDataSetPath());
        sasRequest.setDiscontinuedRoomTypesRequestType(requestType);
        return sasRequest;
    }

}
