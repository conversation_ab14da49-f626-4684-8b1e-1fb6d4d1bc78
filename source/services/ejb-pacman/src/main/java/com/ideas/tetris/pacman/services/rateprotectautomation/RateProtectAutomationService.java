package com.ideas.tetris.pacman.services.rateprotectautomation;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.Service;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.configparams.SyncConfigParamName;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.marketsegment.dto.FixedAboveBarConfigurationDTO;
import com.ideas.tetris.pacman.services.marketsegment.service.FixedAboveBarConfigurationService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.product.ProductCode;
import com.ideas.tetris.pacman.services.product.RoundingRule;
import com.ideas.tetris.pacman.services.syncflags.service.SyncFlagService;
import com.ideas.tetris.pacman.util.JavaLocalDateRangeIterator;
import com.ideas.tetris.pacman.util.csv.CsvFileReader;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.SortedSet;
import java.util.TreeMap;
import java.util.TreeSet;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesOffsetMethod.PERCENTAGE;
import static com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesOffsetMethod.SET;
import static com.ideas.tetris.pacman.util.Executor.executeIfTrue;
import static com.ideas.tetris.pacman.util.Runner.runIfTrue;
import static com.ideas.tetris.platform.common.time.JavaLocalDateUtils.isDateBetween;
import static com.ideas.tetris.platform.common.time.JavaLocalDateUtils.maxDate;
import static com.ideas.tetris.platform.common.time.JavaLocalDateUtils.minDate;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

@Service
@Component
public class RateProtectAutomationService {

    private static final Logger LOGGER = Logger.getLogger(RateProtectAutomationService.class);

    @Autowired
	private RateProtectAutomationRepository repository;

    @Autowired
	private FixedAboveBarConfigurationService fixedAboveBarConfigurationService;

    @Autowired
	private AccommodationService accommodationService;

    @Autowired
	private SyncFlagService syncFlagService;

    @Autowired
	private PacmanConfigParamsService configParamsService;

    private static final Integer RUN_FREQ_IN_DAYS_ZERO = 0;
    private static final Integer PRODUCT_WEIGHT_CALIB_TASK_ID = 5017;
    private static final String PRODUCT_WEIGHT_CALIB_TASK_NAME = "PRODUCT_WEIGHT_CALIB";


    public String execute(String filePath, LocalDate resolutionStartDate, LocalDate resolutionEndDate) {
        Map<String, Map<String, SortedSet<Season>>> incomingSeasons = readPSVFile(filePath);
        return execute(resolutionStartDate, resolutionEndDate, incomingSeasons);
    }

    public String execute(LocalDate resolutionStartDate, LocalDate resolutionEndDate, Map<String, Map<String,
            SortedSet<Season>>> incomingSeasons) {
        if (MapUtils.isEmpty(incomingSeasons)) {
            filterAndDeactivateProducts(Map.of());
            return "Could not read any season from incoming file.";
        }
        Map<String, Map<String, List<Season>>> finalSeasons = new HashMap<>();
        incomingSeasons.entrySet().stream()
                .forEach(rateCodeEntry ->
                        rateCodeEntry.getValue().forEach((roomTypeCode, unresolvedSeasons) -> {
                                    LinkedList<Season> resolvedSeasons = lastOneWins(resolutionStartDate, resolutionEndDate, unresolvedSeasons);
                                    finalSeasons
                                            .computeIfAbsent(rateCodeEntry.getKey(), s -> new HashMap<>())
                                            .put(roomTypeCode, resolvedSeasons);
                                }
                        ));

        if (fixedAboveBarConfigurationService.findFixedAboveBarProductsIncludeInactive().isEmpty()) {
            syncFlagService.enableSyncFor(SyncConfigParamName.SPECIAL_EVENTS_CHANGED);
        }
        long start = System.currentTimeMillis();
        String rateProtectProducts;
        if (isEnabledOptimizedHiltonRateProtectAutoStep()) {
            rateProtectProducts = createRateProtectProductsOptimized(finalSeasons, resolutionStartDate, resolutionEndDate);
        } else {
            rateProtectProducts = createRateProtectProducts(finalSeasons, resolutionStartDate, resolutionEndDate);
        }
        long end = System.currentTimeMillis();
        LOGGER.info((isEnabledOptimizedHiltonRateProtectAutoStep() ? "optimized" : " ") + "createRateProtectProducts time = " + (end - start));
        updateCfgPropertyTask(RUN_FREQ_IN_DAYS_ZERO, PRODUCT_WEIGHT_CALIB_TASK_ID, PRODUCT_WEIGHT_CALIB_TASK_NAME);
        return rateProtectProducts;
    }

    private String createRateProtectProducts(Map<String, Map<String, List<Season>>> rateCodeSeasons, LocalDate startDate, LocalDate endDate) {
        StringBuilder processedRates = new StringBuilder();
        Map<String, Product> currentProducts = filterAndDeactivateProducts(rateCodeSeasons);
        Map<Integer, String> baseRoomTypes = repository.getAccomClassToBaseRoomTypeMap();
        Map<String, AccomType> accomTypes = repository.getAllActiveAccomTypes();
        Map<Integer, AccomClass> roomClasses = accommodationService.getAllActiveAccomClasses().stream().collect(Collectors.toMap(rc -> rc.getId(), rc -> rc));
        rateCodeSeasons.forEach((rateCodeName, roomTypeSeasons) -> {
            FixedAboveBarConfigurationDTO dto = createRateProtectProtectionProduct(rateCodeName, currentProducts);
            LinkedList<AccomType> selectedAccomTypes = processForRTSeasons(dto, roomTypeSeasons, baseRoomTypes, accomTypes, roomClasses, startDate, endDate);
            dto.setRoomTypes(filterWithBaseProductRoomTypes(dto.getBaseProduct(), selectedAccomTypes));
            executeIfTrue(dto.getProduct().isPersisted(), repository::deleteOffsetsForProductId, dto.getProductId());
            fixedAboveBarConfigurationService.justSaveFixedAboveBarProductConfiguration(dto);
            processedRates.append(dto.getProductName()).append(",");
        });
        return processedRates.substring(0, processedRates.length() - 1);
    }

    protected Collection<AccomType> filterWithBaseProductRoomTypes(Product baseProduct, LinkedList<AccomType> selectedAccomTypes) {
        Set<AccomType> baseProductRoomTypes = fixedAboveBarConfigurationService.findAllProductAccomTypes(baseProduct);
        List<AccomType> filteredList = new LinkedList<>();
        for (AccomType selectedAccomType : selectedAccomTypes) {
            if (baseProductRoomTypes.contains(selectedAccomType)) {
                filteredList.add(selectedAccomType);
            }
        }
        return filteredList;
    }

    protected void populateSeasonsForNonBaseRoomTypes(FixedAboveBarConfigurationDTO dto, AccomClass roomClass,
                                                      Map<String, List<Season>> roomTypeSeasons, LocalDate startDate, LocalDate endDate, Season defaultSeason) {

        JavaLocalDateRangeIterator window = new JavaLocalDateRangeIterator(startDate, endDate);
        TreeMap<LocalDate, Season> dateWiseSeasons = new TreeMap<>();
        window.forEachRemaining(date -> roomTypeSeasons.entrySet().stream()
                .map(e -> e.getValue().stream().filter(season -> season.doesDateFallInSeason(date)).findFirst().orElse(null))
                .filter(Objects::nonNull)
                .filter(target -> !dateWiseSeasons.containsKey(date) || target.getMonday().compareTo(dateWiseSeasons.get(date).getMonday()) < 0)
                .forEach(season -> dateWiseSeasons.put(date, season)));
        LinkedList<Season> seasons = mergeDateWiseSeasons(dateWiseSeasons);
        int size = seasons.size();
        for (int i = 0; i < size; i++) {
            Season season = seasons.get(i);
            dto.getSetOffsets().add(season.createRateProtectSeason("Season_" + i, dto.getProduct(), roomClass, SET));
            dto.getSetOffsets().add(season.createRateProtectSeason("Season_" + i, dto.getProduct(), roomClass, PERCENTAGE));
            setDefaultValues(defaultSeason, season);
        }
    }

    private void setDefaultValues(Season defaultSeason, Season season) {
        defaultSeason.setSunday(defaultSeason.getSunday().min(season.getSunday()));
        defaultSeason.setMonday(defaultSeason.getMonday().min(season.getMonday()));
        defaultSeason.setTuesday(defaultSeason.getTuesday().min(season.getTuesday()));
        defaultSeason.setWednesday(defaultSeason.getWednesday().min(season.getWednesday()));
        defaultSeason.setThursday(defaultSeason.getThursday().min(season.getThursday()));
        defaultSeason.setFriday(defaultSeason.getFriday().min(season.getFriday()));
        defaultSeason.setSaturday(defaultSeason.getSaturday().min(season.getSaturday()));
    }

    private void populateSeasonsForBaseRoomTypes(FixedAboveBarConfigurationDTO dto, String baseRoomType, AccomClass roomClass,
                                                 List<Season> seasons, Season defaultSeason) {
        int size = seasons.size();
        for (int i = 0; i < size; i++) {
            Season season = seasons.get(i);
            dto.getSetOffsets().add(season.createRateProtectSeason(dto.getProductName() + "_" + baseRoomType + i, dto.getProduct(), roomClass, SET));
            dto.getSetOffsets().add(season.createRateProtectSeason(dto.getProductName() + "_" + baseRoomType + i, dto.getProduct(), roomClass, PERCENTAGE));
            setDefaultValues(defaultSeason, season);
        }
    }

    private FixedAboveBarConfigurationDTO createRateProtectProtectionProduct(String rateCodeName, Map<String, Product> currentProducts) {
        FixedAboveBarConfigurationDTO dto =
                currentProducts.containsKey(rateCodeName) ?
                        fixedAboveBarConfigurationService.loadProductConfigurationByProductId(currentProducts.get(rateCodeName).getId()) : new FixedAboveBarConfigurationDTO();
        dto.setProductName(rateCodeName);
        dto.setBaseProduct(repository.getBaseProduct());
        dto.setRoundingRule(RoundingRule.NONE);
        dto.setFloor(BigDecimal.ONE);
        dto.setRateCodes(Arrays.asList(rateCodeName));
        dto.setSetOffsets(new LinkedList<>());
        dto.setDefaultOffsets(new LinkedList<>());
        dto.getProduct().setStatus(TenantStatusEnum.ACTIVE);
        dto.getProduct().setDowOffset(true);
        dto.getProduct().setRoomClassOffset(true);
        return dto;
    }

    protected LinkedList<Season> lastOneWins(LocalDate resolutionStartDate, LocalDate resolutionEndDate, SortedSet<Season> in) {
        Map<LocalDate, Season> dateWiseSeasons = getDateWiseSeasons(resolutionStartDate, resolutionEndDate, in);
        return mergeDateWiseSeasons(dateWiseSeasons);
    }

    private LinkedList<Season> mergeDateWiseSeasons(Map<LocalDate, Season> dateWiseSeasons) {
        LinkedList<Season> results = new LinkedList<>();
        if (MapUtils.isEmpty(dateWiseSeasons)) {
            return results;
        }
        LocalDate startDate = null;
        LocalDate endDate = null;
        Season season = null;
        Set<LocalDate> dates = dateWiseSeasons.keySet();

        for (LocalDate date : dates) {
            if (isNull(season)) {
                startDate = endDate = date;
                season = dateWiseSeasons.get(date);
                continue;
            }

            if (season.isSame(dateWiseSeasons.get(date)) && endDate.plusDays(1).equals(date)) {
                endDate = date;
                continue;
            }

            results.addLast(Season.builder()
                    .startDate(startDate).endDate(endDate)
                    .sunday(season.getSunday()).monday(season.getMonday()).tuesday(season.getTuesday())
                    .wednesday(season.getWednesday()).thursday(season.getThursday())
                    .friday(season.getFriday()).saturday(season.getSaturday()).build());

            startDate = endDate = date;
            season = dateWiseSeasons.get(date);
        }
        // The last one
        if (nonNull(season)) {
            results.addLast(Season.builder()
                    .startDate(startDate).endDate(endDate)
                    .sunday(season.getSunday()).monday(season.getMonday()).tuesday(season.getTuesday())
                    .wednesday(season.getWednesday()).thursday(season.getThursday())
                    .friday(season.getFriday()).saturday(season.getSaturday()).build());
        }
        return results;
    }

    private Map<LocalDate, Season> getDateWiseSeasons(LocalDate startDate, LocalDate endDate, Set<Season> in) {
        Map<LocalDate, Season> dateWiseSeasons = new TreeMap<>();
        JavaLocalDateRangeIterator iterator = new JavaLocalDateRangeIterator(startDate, endDate);
        iterator.forEachRemaining(date ->
                in.stream()
                        .filter(season -> isDateBetween(date, maxDate(startDate, season.getStartDate()), minDate(endDate, season.getEndDate())))
                        .findFirst()
                        .ifPresent(season -> dateWiseSeasons.put(date, season)));
        return dateWiseSeasons;
    }

    public Map<String, Map<String, SortedSet<Season>>> readPSVFile(String filePath) {
        Set<String> activeRateCodes = repository.getActiveRateCodes();
        return readPSVFile(filePath, activeRateCodes);
    }

    private Map<String, Map<String, SortedSet<Season>>> readPSVFile(String filePath, Set<String> activeRateCodes) {
        Map<String, Map<String, SortedSet<Season>>> result = new HashMap<>();
        new CsvFileReader().read(filePath, "[|]", columns -> {
            runIfTrue(activeRateCodes.contains(columns[0]),
                    () -> result.computeIfAbsent(columns[0], s -> new HashMap<>())
                            .computeIfAbsent(columns[1], s -> new TreeSet<>())
                            .add(createSeason(columns))
            );
            return StringUtils.EMPTY;
        });
        return result;
    }

    private Season createSeason(String[] columns) {
        return Season.builder()
                .startDate(LocalDate.parse(columns[2]))
                .endDate(LocalDate.parse(columns[3]))
                .sunday(new BigDecimal(columns[4]))
                .monday(new BigDecimal(columns[5]))
                .tuesday(new BigDecimal(columns[6]))
                .wednesday(new BigDecimal(columns[7]))
                .thursday(new BigDecimal(columns[8]))
                .friday(new BigDecimal(columns[9]))
                .saturday(new BigDecimal(columns[10]))
                .recordSeq(Integer.parseInt(columns[11]))
                .build();
    }


    public boolean isAnyRoomClassNotConfiguredWithBaseRoomType() {
        Integer numberOfRoomClassesBaseRoomTypesAreNotDefinedFor = repository.getNumberOfRoomClassesBaseRoomTypesAreNotDefinedFor();
        return numberOfRoomClassesBaseRoomTypesAreNotDefinedFor != null && numberOfRoomClassesBaseRoomTypesAreNotDefinedFor > 0;
    }

    @VisibleForTesting
    void updateCfgPropertyTask(Integer runFreqInDays, Integer propTaskId, String taskName) {
        repository.updateCfgPropertyTask(runFreqInDays, propTaskId, taskName);
    }

    private String createRateProtectProductsOptimized(Map<String, Map<String, List<Season>>> rateCodeSeasons, LocalDate startDate, LocalDate endDate) {
        Map<String, Product> currentProducts = filterAndDeactivateProducts(rateCodeSeasons);
        Map<Integer, String> baseRoomTypes = repository.getAccomClassToBaseRoomTypeMap();
        Map<String, AccomType> accomTypes = repository.getAllActiveAccomTypes();
        Map<Integer, AccomClass> roomClasses = accommodationService.getAllActiveAccomClasses().stream().collect(Collectors.toMap(rc -> rc.getId(), rc -> rc));
        StringBuilder processedRates = new StringBuilder();
        ProductCode pc = repository.findProductCodeByName(Product.FIXED_ABOVE_BAR_CODE);
        rateCodeSeasons.forEach((rateCodeName, roomTypeSeasons) -> {
            FixedAboveBarConfigurationDTO dto = createRateProtectProtectionProductOptimized(rateCodeName, currentProducts);
            LinkedList<AccomType> selectedAccomTypes = processForRTSeasons(dto, roomTypeSeasons, baseRoomTypes, accomTypes, roomClasses, startDate, endDate);
            dto.setRoomTypes(filterWithBaseProductRoomTypes(dto.getBaseProduct(), selectedAccomTypes));
            executeIfTrue(dto.getProduct().isPersisted(), repository::deleteOffsetsForProductId, dto.getProductId());
            fixedAboveBarConfigurationService.justSaveFixedAboveBarProductConfigurationOptimized(dto, pc);
            processedRates.append(dto.getProductName()).append(",");
        });
        return processedRates.substring(0, processedRates.length() - 1);
    }

    @VisibleForTesting
    protected Map<String, Product> filterAndDeactivateProducts(Map<String, Map<String, List<Season>>> rateCodeSeasons) {
        List<Integer> productIdsToDelete = new LinkedList<>();
        Map<String, Product> currentProducts = fixedAboveBarConfigurationService.findFixedAboveBarProductsIncludeInactive().stream()
                .filter(product -> {
                    if (product.isActive() && !rateCodeSeasons.containsKey(product.getName())) {
                        productIdsToDelete.add(product.getId());
                        return false;
                    }
                    return true;
                })
                .collect(Collectors.toMap(Product::getName, p -> p));
        LOGGER.info(CollectionUtils.isEmpty(productIdsToDelete) ? "No products to deactivate." : "Products to deactivate: " + productIdsToDelete);
        if (!CollectionUtils.isEmpty(productIdsToDelete)) {
            repository.deactivateProducts(productIdsToDelete);
        }
        return currentProducts;
    }

    @VisibleForTesting
    protected FixedAboveBarConfigurationDTO createRateProtectProtectionProductOptimized(String rateCodeName, Map<String, Product> currentProducts) {
        FixedAboveBarConfigurationDTO dto = null;
        if (currentProducts.containsKey(rateCodeName)) {
            dto = fixedAboveBarConfigurationService.loadProductConfigurationByProductOptimized(currentProducts.get(rateCodeName));
        } else {
            dto = new FixedAboveBarConfigurationDTO();
        }

        dto.setBaseProduct(repository.getBaseProductOptimized());
        dto.setProductName(rateCodeName);
        dto.setRoundingRule(RoundingRule.NONE);
        dto.setFloor(BigDecimal.ONE);
        dto.setRateCodes(Arrays.asList(rateCodeName));
        dto.setSetOffsets(new LinkedList<>());
        dto.setDefaultOffsets(new LinkedList<>());
        dto.getProduct().setStatus(TenantStatusEnum.ACTIVE);
        dto.getProduct().setDowOffset(true);
        dto.getProduct().setRoomClassOffset(true);
        return dto;
    }

    private LinkedList<AccomType> processForRTSeasons(FixedAboveBarConfigurationDTO dto, Map<String, List<Season>> roomTypeSeasons,
                                                      Map<Integer, String> baseRoomTypes, Map<String, AccomType> accomTypes, Map<Integer, AccomClass> roomClasses,
                                                      LocalDate startDate, LocalDate endDate) {
        LinkedList<AccomType> selectedAccomTypes = new LinkedList<>();
        baseRoomTypes.forEach((accomClassId, baseRoomType) -> {
            Season defaultValue = Season.createDefaultValueSeason();
            final AccomClass roomClass = roomClasses.get(accomClassId);
            // For base room Types
            if (roomTypeSeasons.containsKey(baseRoomType) && !roomTypeSeasons.get(baseRoomType).isEmpty()) {
                selectedAccomTypes.add(accomTypes.get(baseRoomType));
                List<Season> seasons = roomTypeSeasons.get(baseRoomType);
                populateSeasonsForBaseRoomTypes(dto, baseRoomType, roomClass, seasons, defaultValue);
            } else {
                selectedAccomTypes.addAll(accomTypes.values().stream()
                        .filter(roomType -> roomType.getAccomClass().getId() == accomClassId)
                        .filter(roomTypeCode -> roomTypeSeasons.containsKey(roomTypeCode.getAccomTypeCode()))
                        .collect(Collectors.toList())
                );
                populateSeasonsForNonBaseRoomTypes(dto, roomClass, roomTypeSeasons, startDate, endDate, defaultValue);
            }
            dto.getDefaultOffsets().add(defaultValue.createRateProtectSeason(null, dto.getProduct(), roomClass, SET));
            dto.getDefaultOffsets().add(defaultValue.createRateProtectSeason(null, dto.getProduct(), roomClass, PERCENTAGE));
        });
        return selectedAccomTypes;
    }

    private boolean isEnabledOptimizedHiltonRateProtectAutoStep() {
        return configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_OPTIMIZED_HILTON_RATE_PROTECT_AUTO_STEP);
    }
}
