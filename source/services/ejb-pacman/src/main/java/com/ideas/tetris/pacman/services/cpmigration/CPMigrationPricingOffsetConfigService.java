package com.ideas.tetris.pacman.services.cpmigration;

import com.codepoetics.protonpack.StreamUtils;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.integration.ratchet.services.currencyexchange.CurrencyExchangeService;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfigOffsetAccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OffsetMethod;
import com.ideas.tetris.pacman.services.cprecommendedfloorceiling.service.CPRecommendedFloorCeilingService;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.services.tax.service.TaxService;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.sas.log.SasDbQueryResult;
import com.ideas.tetris.platform.services.sas.log.SasDbToolService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Slf4j


@Component
@Transactional
public class CPMigrationPricingOffsetConfigService {
    private static final String SELECT_LATEST_LV0_SEASON_FROM_RATCHET_RATES =
            "select * from ratchet.rates where rtlvl=:rateLevel and ':caughtupDate'd between startDate and endDate";
    private static final int LV_2 = 2;
    private static final int LV_0 = 0;

    @Autowired
	private DateService dateService;
    @Autowired
	private SasDbToolService sasDbToolService;
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;
    @Autowired
	private AccommodationService accommodationService;
    @Autowired
	private CurrencyExchangeService currencyExchangeService;
    @Autowired
	private TaxService taxService;
    @Autowired
	private PricingConfigurationService pricingConfigurationService;
    @Autowired
    CPRecommendedFloorCeilingService cpRecommendedFloorCeilingService;


    public String populateDefaultOffsets() {
        Collection<AccomType> baseAccomTypes = findBaseAccomTypes();
        OffsetProduction offsetProduction = new OffsetProduction();
        List<AccomType> allActiveAccomTypes = accommodationService.getAllAssignedAccomTypes();
        List<CPConfigOffsetAccomType> generatedOffsets = allActiveAccomTypes
                .stream()
                .flatMap(accomType -> Stream.of(OccupancyType.SINGLE, OccupancyType.DOUBLE, OccupancyType.EXTRA_ADULT, OccupancyType.EXTRA_CHILD)
                        .filter(occupancyType -> !(occupancyType == OccupancyType.SINGLE && baseAccomTypes.contains(accomType)))
                        .map(occupancyType -> offsetProduction.apply(accomType, occupancyType)))
                .collect(Collectors.toList());

        tenantCrudService.deleteAll(CPConfigOffsetAccomType.class);
        tenantCrudService.save(generatedOffsets);

        log.info("Generated {} offsets for {} accom-types.", generatedOffsets.size(), allActiveAccomTypes.size());
        return MessageFormat.format("Generated {0} offsets for {1} accom-types.", generatedOffsets.size(), allActiveAccomTypes.size());
    }

    public void reConfigureOffsetsForPerPersonProperty() {
        tenantCrudService.executeUpdateByNamedQuery(CPConfigOffsetAccomType.DELETE_BY_PROPERTY_ID_AND_OCCUPANCY_TYPE,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("occupancyType", OccupancyType.DOUBLE).parameters());
    }

    private Collection<AccomType> findBaseAccomTypes() {
        return tenantCrudService.findAll(PricingAccomClass.class)
                .stream()
                .map(PricingAccomClass::getAccomType)
                .collect(Collectors.toList());
    }


    public Map<AccomClass, AccomType> configureBaseRoomTypes() {
        List<PricingAccomClass> existingAccomClasses = pricingConfigurationService.getPricingAccomClasses();
        if (CollectionUtils.isNotEmpty(existingAccomClasses)) {
            tenantCrudService.deleteAll(PricingAccomClass.class);
        }
        SasDbQueryResult ratesFromRatchet = getRatesFromRatchet();
        IndexedColumns columns = IndexedColumns.from(ratesFromRatchet.getColumns());
        Map<String, List<Object>> lv2Rates = dedupRates(ratesFromRatchet.getData(), columns)
                .stream()
                .collect(Collectors.toMap(row -> getStringValue("roomType", row, columns), Function.identity()));
        List<AccomType> accomTypes = accommodationService.getAllAssignedAccomTypes().stream()
                .filter(at -> at.getAccomTypeCapacity() > 0)
                .filter(at -> lv2Rates.containsKey(at.getAccomTypeCode()))
                .collect(Collectors.toList());
        Map<String, BigDecimal> roomTypeToRateMap = getRoomTypeRatesMapping(columns, lv2Rates, accomTypes);
        Map<AccomClass, List<AccomType>> rcRtsMap = accomTypes.stream().collect(Collectors.groupingBy(AccomType::getAccomClass));
        Map<AccomClass, AccomType> rcBaseRtMap = getRoomClassToBaseRoomTypeMapping(roomTypeToRateMap, rcRtsMap);
        List<PricingAccomClass> newPricingAccomClasses = getPricingAccomClasses(rcBaseRtMap);
        tenantCrudService.save(newPricingAccomClasses);
        if (CollectionUtils.isNotEmpty(existingAccomClasses)) {
            cpRecommendedFloorCeilingService.populateRecommendedBaseRTForHiltonCpMigration(existingAccomClasses, newPricingAccomClasses);
        }
        return rcBaseRtMap;
    }

    private Map<String, BigDecimal> getRoomTypeRatesMapping(IndexedColumns columns, Map<String, List<Object>> lv2Rates, List<AccomType> accomTypes) {
        Map<String, BigDecimal> roomTypeToRateMap = new HashMap<>();
        List<String> accomTypeCodes = accomTypes.stream().map(AccomType::getAccomTypeCode).collect(Collectors.toList());
        lv2Rates.forEach((rtCode, rate) -> {
            if (!accomTypeCodes.contains(rtCode)) {
                return;
            }
            roomTypeToRateMap.put(rtCode, getBigDecimalValue("Sunday_1", rate, columns));
        });
        return roomTypeToRateMap;
    }

    private Map<AccomClass, AccomType> getRoomClassToBaseRoomTypeMapping(Map<String, BigDecimal> roomTypeToRateMap, Map<AccomClass, List<AccomType>> rcRtsMap) {
        Map<AccomClass, AccomType> rcBaseRtMap = new HashMap<>();
        rcRtsMap.forEach((rc, rts) -> {
            for (AccomType rt : rts) {
                if (rcBaseRtMap.containsKey(rc)) {
                    AccomType at = rcBaseRtMap.get(rc);
                    if ((roomTypeToRateMap.get(rt.getAccomTypeCode()).doubleValue() < roomTypeToRateMap.get(at.getAccomTypeCode()).doubleValue()) ||
                            (BigDecimalUtil.equals(roomTypeToRateMap.get(rt.getAccomTypeCode()), roomTypeToRateMap.get(at.getAccomTypeCode())) &&
                                    (rt.getAccomTypeCapacity() > at.getAccomTypeCapacity()))) {
                        rcBaseRtMap.put(rc, rt);
                    }
                } else {
                    rcBaseRtMap.put(rc, rt);
                }
            }
        });
        return rcBaseRtMap;
    }

    private List<PricingAccomClass> getPricingAccomClasses(Map<AccomClass, AccomType> rcBaseRtMap) {
        List<PricingAccomClass> pricingAccomClasses = new ArrayList<>();
        rcBaseRtMap.forEach((rc, rt) -> {
            PricingAccomClass pac = new PricingAccomClass();
            pac.setPropertyId(PacmanWorkContextHelper.getPropertyId());
            pac.setAccomClass(rc);
            pac.setAccomType(rt);
            pricingAccomClasses.add(pac);
        });
        return pricingAccomClasses;
    }


    public String populateSingleOccupancyOffsets() {
        Offsets offsets = new Offsets().add(OccupancyType.SINGLE, findExistingOffsetsByType(OccupancyType.SINGLE));

        SasDbQueryResult ratesFromRatchet = getRatesFromRatchet();
        populateSingleOccupancyOffsets(ratesFromRatchet, offsets);

        tenantCrudService.save(offsets.all());

        log.info("Updated {} offsets of single-occupancy type.", offsets.all().size());
        return MessageFormat.format("Updated {0} offsets of single-occupancy type.", offsets.all().size());
    }

    private SasDbQueryResult getRatesFromRatchet() {
        SasDbQueryResult ratesFromRatchet = sasDbToolService.executeQuery(PacmanWorkContextHelper.getClientCode(),
                PacmanWorkContextHelper.getPropertyId(), PacmanWorkContextHelper.getPropertyCode(),
                prepareRateQuery(LV_2));
        log.info("Fetched {} LV2 rates from ratchet.", ratesFromRatchet.getData().size());
        return ratesFromRatchet;
    }

    private String prepareRateQuery(int rateLevel) {
        return SELECT_LATEST_LV0_SEASON_FROM_RATCHET_RATES.replace(":caughtupDate", DateUtil.formatDate(dateService.getCaughtUpDate(), DateUtil.DATE_FORMAT_SAS))
                .replace(":rateLevel", String.valueOf(rateLevel));
    }

    private void populateSingleOccupancyOffsets(SasDbQueryResult queryResult, Offsets offsets) {
        IndexedColumns columns = IndexedColumns.from(queryResult.getColumns());
        Tax tax = taxService.findTax();

        Map<AccomClass, PricingAccomClass> baseACATMapping = tenantCrudService.findAll(PricingAccomClass.class).stream()
                .collect(Collectors.toMap(PricingAccomClass::getAccomClass, Function.identity()));

        Map<String, AccomType> accomTypes = accommodationService.getAllAssignedAccomTypes().stream()
                .collect(Collectors.toMap(AccomType::getAccomTypeCode, Function.identity()));

        Map<String, List<Object>> lv2Rates = dedupRates(queryResult.getData(), columns)
                .stream()
                .collect(Collectors.toMap(row -> getStringValue("roomType", row, columns), Function.identity()));

        lv2Rates.forEach((roomType, rate) -> {
            if (!accomTypes.containsKey(roomType)) {
                return;
            }
            populateDOWOffsets(offsets.get(OccupancyType.SINGLE, roomType),
                    dow -> tax.removeRoomTaxRate(deduceSingleOffset(dow.getCaption(), rate, columns, roomType, lv2Rates, baseACATMapping, accomTypes)));
        });
    }

    private BigDecimal deduceSingleOffset(String dow, List<Object> lv2Rate, IndexedColumns columns, String roomType, Map<String, List<Object>> lv0Rates, Map<AccomClass, PricingAccomClass> baseACATMapping, Map<String, AccomType> accomTypes) {
        AccomType baseAccomType = baseACATMapping.get(accomTypes.get(roomType).getAccomClass()).getAccomType();
        List<Object> lv2RateOfBaseAT = lv0Rates.get(baseAccomType.getAccomTypeCode());
        return getBigDecimalValue(dow + "_1", lv2Rate, columns).subtract(getBigDecimalValue(dow + "_1", lv2RateOfBaseAT, columns));
    }


    public String populateExtraChildAndExtraAdultOffsets() {
        Offsets offsets = new Offsets()
                .add(OccupancyType.EXTRA_ADULT, findExistingOffsetsByType(OccupancyType.EXTRA_ADULT))
                .add(OccupancyType.EXTRA_CHILD, findExistingOffsetsByType(OccupancyType.EXTRA_CHILD));

        SasDbQueryResult ratesFromRatchet = sasDbToolService.executeQuery(PacmanWorkContextHelper.getClientCode(),
                PacmanWorkContextHelper.getPropertyId(), PacmanWorkContextHelper.getPropertyCode(),
                prepareRateQuery(LV_0));
        log.info("Fetched {} LV0 rates from ratchet.", ratesFromRatchet.getData().size());

        populateExtraAdultExtraChildOffsets(ratesFromRatchet, offsets);

        tenantCrudService.save(offsets.all());

        return MessageFormat.format("Updated {0} offset-records of types- extra-adult and extra-child.", offsets.all().size());
    }

    private Collection<CPConfigOffsetAccomType> findExistingOffsetsByType(OccupancyType type) {
        CPConfigOffsetAccomType templateOffset = new CPConfigOffsetAccomType();
        templateOffset.setOccupancyType(type);
        return tenantCrudService.findByExample(templateOffset);
    }

    private void populateExtraAdultExtraChildOffsets(SasDbQueryResult queryResult, Offsets offsets) {
        IndexedColumns columns = IndexedColumns.from(queryResult.getColumns());
        CurrencyExchangeService.ExchangeRate exchangeRate = currencyExchangeService.getYieldCurrencyConversionFactor();
        Tax tax = taxService.findTax();

        dedupRates(queryResult.getData(), columns)
                .forEach(row -> {
                    String roomType = getStringValue("roomType", row, columns);
                    populateDOWOffsets(offsets.get(OccupancyType.EXTRA_ADULT, roomType),
                            dow -> tax.removeRoomTaxRate(deduceExtraAdultOffset(dow.getCaption(), row, columns, exchangeRate)));
                    populateDOWOffsets(offsets.get(OccupancyType.EXTRA_CHILD, roomType),
                            dow -> tax.removeRoomTaxRate(deduceExtraChildOffset(dow.getCaption(), row, columns, exchangeRate)));
                });
    }

    private BigDecimal deduceExtraChildOffset(final String dow, List<Object> rate, IndexedColumns columns, CurrencyExchangeService.ExchangeRate exchangeRate) {
        return exchangeRate.apply(getBigDecimalValue(dow + "_xc", rate, columns));
    }

    private BigDecimal deduceExtraAdultOffset(final String dow, List<Object> row, IndexedColumns columns, CurrencyExchangeService.ExchangeRate exchangeRate) {
        return exchangeRate.apply(getBigDecimalValue(dow + "_3", row, columns)).subtract(getBigDecimalValue(dow + "_2", row, columns));
    }

    private void populateDOWOffsets(CPConfigOffsetAccomType offset, Function<DayOfWeek, BigDecimal> dowOffsetProvider) {
        if (offset == null) {
            return;
        }
        offset.setSundayOffsetValue(dowOffsetProvider.apply(DayOfWeek.SUNDAY));
        offset.setMondayOffsetValue(dowOffsetProvider.apply(DayOfWeek.MONDAY));
        offset.setTuesdayOffsetValue(dowOffsetProvider.apply(DayOfWeek.TUESDAY));
        offset.setWednesdayOffsetValue(dowOffsetProvider.apply(DayOfWeek.WEDNESDAY));
        offset.setThursdayOffsetValue(dowOffsetProvider.apply(DayOfWeek.THURSDAY));
        offset.setFridayOffsetValue(dowOffsetProvider.apply(DayOfWeek.FRIDAY));
        offset.setSaturdayOffsetValue(dowOffsetProvider.apply(DayOfWeek.SATURDAY));
    }

    private List<List<Object>> dedupRates(List<ArrayList<Object>> allRates, IndexedColumns columns) {
        Map<String, List<List<Object>>> accomTypeWiseRateRecords = allRates
                .stream()
                .collect(Collectors.groupingBy(row -> getStringValue("roomType", row, columns)));

        return accomTypeWiseRateRecords.values()
                .stream()
                .map(duplicatedRates -> duplicatedRates.stream().max(Comparator.comparing(row ->
                        Integer.valueOf(getStringValue("recordseq", row, columns)))).orElse(null))
                .collect(Collectors.toList());
    }

    private String getStringValue(String columnName, List<Object> row, IndexedColumns columns) {
        return ((String) row.get(columns.get(columnName))).trim();
    }

    private BigDecimal getBigDecimalValue(String columnName, List<Object> row, IndexedColumns columns) {
        return BigDecimalUtil.valueOf(row.get(columns.get(columnName)), false, true);
    }


    // for easy lookup of existing offsets
    private static class Offsets {
        private final Map<OccupancyType, Map<String, CPConfigOffsetAccomType>> lookup = new HashMap<>();

        public Offsets add(OccupancyType occupancyType, Collection<CPConfigOffsetAccomType> offsets) {
            lookup.put(occupancyType, buildLookup(offsets));
            return this;
        }

        private Map<String, CPConfigOffsetAccomType> buildLookup(Collection<CPConfigOffsetAccomType> offsets) {
            return offsets.stream()
                    .collect(Collectors.toMap(offset -> offset.getAccomType().getAccomTypeCode(), Function.identity()));
        }

        public CPConfigOffsetAccomType get(OccupancyType occupancyType, String accomType) {
            return lookup.get(occupancyType).get(accomType);
        }

        private List<CPConfigOffsetAccomType> all() {
            return lookup.values()
                    .stream()
                    .flatMap(offsets -> offsets.values().stream())
                    .collect(Collectors.toList());
        }
    }

    // for case insensitive column lookup
    @AllArgsConstructor
    private static class IndexedColumns {
        private final Map<String, Integer> columnNameToIndexMapping;

        public static IndexedColumns from(List<SasDbQueryResult.SasDbColumnInfo> columns) {
            Map<String, Integer> columnNameToIndexMapping = StreamUtils.zipWithIndex(columns.stream())
                    .collect(Collectors.toMap(idxColumnPair -> idxColumnPair.getValue().getName().toLowerCase(),
                            idxColumnPair -> (int) idxColumnPair.getIndex()));
            return new IndexedColumns(columnNameToIndexMapping);
        }

        public Integer get(String columnName) {
            return this.columnNameToIndexMapping.get(columnName.toLowerCase());
        }
    }

    private class OffsetProduction implements BiFunction<AccomType, OccupancyType, CPConfigOffsetAccomType> {

        @Override
        public CPConfigOffsetAccomType apply(AccomType accomType, OccupancyType occupancyType) {
            CPConfigOffsetAccomType offset = new CPConfigOffsetAccomType();
            offset.setPropertyId(PacmanWorkContextHelper.getPropertyId());
            //Hard Coded to System Default product
            offset.setProductID(1);
            offset.setAccomType(accomType);
            offset.setOccupancyType(occupancyType);
            offset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
            populateDOWOffsets(offset, dow -> BigDecimal.ZERO);
            return offset;
        }
    }
}
