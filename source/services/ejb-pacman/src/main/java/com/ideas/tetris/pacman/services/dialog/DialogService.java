package com.ideas.tetris.pacman.services.dialog;

import com.google.common.base.Strings;
import com.ideas.g3.dialog.dto.DialogIntentRequest;
import com.ideas.g3.dialog.dto.DialogIntentResponse;
import com.ideas.g3.dialog.dto.DialogLaunchRequest;
import com.ideas.g3.dialog.dto.DialogLaunchResponse;
import com.ideas.g3.dialog.dto.DialogRequest;
import com.ideas.g3.dialog.dto.DialogSession;
import com.ideas.g3.dialog.dto.DialogSessionEndedRequest;
import com.ideas.g3.dialog.dto.DialogSessionEndedResponse;
import com.ideas.g3.dialog.dto.DialogSessionStartedRequest;
import com.ideas.g3.dialog.dto.DialogSessionStartedResponse;
import com.ideas.g3.dialog.dto.IntentType;
import com.ideas.g3.dialog.dto.NotificationType;
import com.ideas.g3.dialog.dto.Property;
import com.ideas.g3.dialog.dto.StatisticType;
import com.ideas.infra.tetris.security.domain.LDAPUser;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.dialog.entity.DailyBriefing;
import com.ideas.tetris.pacman.services.dialog.entity.DialogUserRequest;
import com.ideas.tetris.pacman.services.dialog.entity.DialogUserSession;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertService;
import com.ideas.tetris.pacman.services.informationmanager.dto.SearchCriteriaDTO;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrExcepNotifEntity;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.security.AuthorizationService;
import com.ideas.tetris.pacman.services.security.GlobalUser;
import com.ideas.tetris.pacman.services.security.UserGlobalDBService;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.pacman.services.webrate.dto.CompetitorRateInfo;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystem;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang.LocaleUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.LocalDateTime;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class DialogService {
    private static final Logger LOGGER = Logger.getLogger(DialogService.class);
    protected static final int RECENT_SESSION_THRESHOLD = 60; // minutes
    protected static final String RESOURCE_BUNDLE_NAME = "dialog";
    private static final String REPROMPT_MESSAGE_LABEL = "repromptMessage";
    private static final String DATE_FORMAT = "yyyy-MM-dd";
    private static final String TEXT_DATE_FORMAT = "EEEE MMMM dd";
    private static final String NOT_AVAILABLE_MESSAGE_LABEL = "notAvailable";
    private static final String AHEAD_BEHIND = "aheadBehind";
    private static final String ABOVE_BELOW = "aboveBelow";
    private static final String UP_DOWN = "upDown";
    private static final String STLY = "stly";
    private static final String LAST_YEAR = "lastYear";
    private static final String COMMA_WITH_SPACE = ", ";
    private static final String BLANK_SPACE = " ";
    private static final String FULL_STOP = ". ";
    private static final String ROOMS = "rooms";
    private static final String ROOM_TYPE = "roomtype";
    private static final int MAX_LEVENSHTEIN_DISTANCE = 4;

    @Autowired
    OperationsDataService operationsDataService;
    @Autowired
    RevenueDataService revenueDataService;
    @Autowired
    CompetitorDataService competitorDataService;
    @Autowired
    DecisionDataService decisionDataService;
    @Autowired
	private UserGlobalDBService userGlobalDBService;
    @Autowired
	private UserService userService;
    @Autowired
	private PropertyService propertyService;
    @Autowired
    DailyBriefingDataService dailyBriefingDataService;
    @Autowired
	private AlertService alertService;
    @GlobalCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("globalCrudServiceBean")
    CrudService globalCrudService;

    @Autowired
	private CapacityDataService capacityDataService;
    @Autowired
	private PacmanConfigParamsService pacmanConfigParamsService;

    @Autowired
	private AuthorizationService authorizationService;

    @Autowired
	private CurrencySymbol currencySymbol;


    /*
    This method is intended to be used to generate the JSON for an IntentRequest.  For example, Postman could be used to invoke this method using REST, then the output JSON could be used as the body
    of a subsequent Postman POST request to /onIntent.  This method exists solely as a convenience for local testing, and is not required or used for any Production functionality.
     */
    public DialogIntentRequest createRequestForTesting() {
        DialogIntentRequest request = new DialogIntentRequest();
        request.setLocale(("en-US"));
        DialogSession session = new DialogSession();
        session.setNew(true);
        session.setAttribute(DialogSession.REQUEST_COUNT, Integer.valueOf(0));
        session.setAttribute(DialogSession.PROPERTY, new Property(9, "London Metropolitan"));
        session.setAttribute(DialogSession.USER_WELCOMED, false);
        session.setAttribute(DialogSession.PROPERTY_ANNOUNCED, false);
        request.setSession(session);
        request.setVendorIntent(IntentType.ARRIVALS.getVendorIntent());
        request.setParameter(DialogIntentRequest.STATISTIC_TYPE_PARAMETER, StatisticType.FORECASTED.toString());
        request.setParameter(DialogIntentRequest.START_DATE_PARAMETER, "2018-01-01");
        request.setParameter(DialogIntentRequest.END_DATE_PARAMETER, "2018-02-01");
        return request;
    }


    public DialogSessionStartedResponse onSessionStarted(DialogSessionStartedRequest request) {
        DialogSession session = getOrCreateSession(request);
        setWorkContextPropertyForNewSession(session);
        findOrCreateUserSession(session);
        session.setAttribute(DialogSession.PROPERTY_ANNOUNCED, false);
        session.setAttribute(DialogSession.USER_WELCOMED, false);
        DialogSessionStartedResponse response = new DialogSessionStartedResponse();
        response.setSession(session);
        return response;
    }


    public DialogSessionEndedResponse onSessionEnded(DialogSessionEndedRequest request) {
        DialogSession session = request.getSession();
        setWorkContextPropertyForNewSession(session);
        endUserSession(session);
        DialogSessionEndedResponse response = new DialogSessionEndedResponse();
        response.setSession(session);
        return response;
    }


    public DialogLaunchResponse onLaunch(DialogLaunchRequest request) {
        DialogSession session = getOrCreateSession(request);
        setWorkContextPropertyForNewSession(session);
        findOrCreateUserSession(session);
        session.setAttribute(DialogSession.USER_WELCOMED, true);
        DialogLaunchResponse response = new DialogLaunchResponse();
        response.setSession(session);
        ResourceBundle bundle = getBundle(request.getLocale());
        response.setText(cleanText(getWelcomeText(bundle)));
        response.setRepromptText(cleanText(getText(bundle, REPROMPT_MESSAGE_LABEL)));
        response.setCloseSession(false);
        return response;
    }

    private String getWelcomeText(ResourceBundle bundle) {
        if (userHasRecentSession()) {
            return String.format(getText(bundle, "conciseWelcomeMessage"), getUserFirstName());
        } else {
            return String.format(getText(bundle, "welcomeMessage"), getUserFirstName());
        }
    }

    private String getGoodbyeText(ResourceBundle bundle) {
        if (userHasRecentSession()) {
            return String.format(getText(bundle, "conciseGoodbyeMessage"), getUserFirstName());
        } else {
            return String.format(getText(bundle, "goodbyeMessage"), getUserFirstName());
        }
    }


    public DialogIntentResponse onIntent(DialogIntentRequest request) {
        DialogSession session = getOrCreateSession(request);
        setWorkContextPropertyForNewSession(session);
        String vendorIntent = request.getVendorIntent();
        IntentType intentType = IntentType.fromVendorIntent(vendorIntent);
        DialogIntentResponse response = new DialogIntentResponse();
        if (intentType == null) {
            return response;
        }
        if (IntentType.CHANGE_DATE.equals(intentType)) {
            intentType = changeDate(request);
        } else if (IntentType.CHANGE_STATISTIC_TYPE.equals(intentType)) {
            intentType = changeStatisticType(request);
        } else {
            session.setAttribute(DialogSession.INTENT, vendorIntent);
        }
        switch (intentType) {
            case ARRIVALS:
            case DEPARTURES:
            case OCCUPANCY:
            case ROOM_REVENUE:
            case AVERAGE_COMPETITOR_RATE:
            case LAST_ROOM_VALUE:
            case BAR_DECISION:
            case CAPACITY:
            case ADR:
            case AVAILABLE_CAPACITY:
            case OUT_OF_ORDER:
            case REV_PAR:
            case OVERBOOKING:
                response = announceStatisticData(request);
                break;
            case HIGHEST_COMPETITOR_RATE:
            case LOWEST_COMPETITOR_RATE:
            case COMPETITOR_RATE:
                response = announceCompetitorData(request);
                break;
            case FINISHED:
                response = processFinishedIntent(request);
                break;
            case NOTIFICATION_COUNT:
                response = announceNotificationCounts(request);
                break;
            case NOTIFICATION_TYPE_COUNT:
                response = announceNotificationTypeCounts(request);
                break;
            case DAILY_BRIEFING:
                if (pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.ENABLED_DIALOG_DAILY_BRIEFING)) {
                    response = announceDailyBriefing(request);
                } else {
                    response = processDailyBriefingIntent(request);
                }
                break;
            case ROOM_TYPE_OVERBOOKING:
                response = announceRoomTypeOverbooking(request);
                break;
            case ROOM_TYPE_BAR_DECISION:
                response = announceRoomTypeBarDecision(request);
                break;
            case ROOM_CLASS_LAST_ROOM_VALUE:
                response = announceRoomClassLastRoomValueDecision(request);
                break;
            case SWITCH_PROPERTY:
                response = switchProperty(request);
                break;
            default:
                // use default response
        }
        addRequestToUserSession(session, intentType, request.getParameters(), response.getText());
        return response;
    }

    private DialogIntentResponse announceRoomClassLastRoomValueDecision(DialogIntentRequest request) {
        ResourceBundle bundle = getBundle(request.getLocale());
        DialogSession session = request.getSession();
        IntentType intentType = IntentType.fromVendorIntent(request.getVendorIntent());
        StringBuilder buffer = new StringBuilder();
        buffer.append(welcomeUser(session, bundle));
        buffer.append(getText(bundle, "the"));
        buffer.append(getText(bundle, intentType.getLabel())).append(getText(bundle, "for"));
        buffer.append(announceProperty(session, bundle));
        LocalDate startDate = getStartDate(request);
        session.setAttribute(DialogSession.START_DATE, startDate.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        LocalDate endDate = getEndDate(request);
        session.setAttribute(DialogSession.END_DATE, endDate.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        buffer.append(" ").append(toSpeech(startDate, endDate, bundle, request.getLocale()));
        String roomClass = (String) request.getParameter(DialogIntentRequest.ROOM_CLASS_PARAMETER);
        if ("All".equalsIgnoreCase(roomClass)) {
            buffer.append(buildLastRoomValueDecisionTextForAllRoomClasses(startDate, intentType, bundle));
        } else {
            buffer.append(getText(bundle, "for"));
            buffer.append(getText(bundle, "roomclass")).append(roomClass).append(" ").append(getText(bundle, "is"));
            BigDecimal lastRoomValue = decisionDataService.getLastRoomValueForRoomClass(startDate, roomClass);
            if (lastRoomValue == null) {
                buffer.append(getText(bundle, NOT_AVAILABLE_MESSAGE_LABEL));
            } else {
                buffer.append(getValueText(lastRoomValue, intentType, bundle));
            }
        }
        buffer.append(".");
        DialogIntentResponse response = new DialogIntentResponse();
        response.setSession(session);
        response.setText(buffer.toString());
        response.setRepromptText(getText(bundle, REPROMPT_MESSAGE_LABEL));
        response.setCloseSession(false);
        return response;
    }

    private String buildLastRoomValueDecisionTextForAllRoomClasses(LocalDate occupancyDate, IntentType intentType, ResourceBundle bundle) {
        StringBuilder buffer = new StringBuilder();
        Map<String, BigDecimal> decisions = decisionDataService.getLastRoomValueForAllRoomClasses(occupancyDate);
        int total = decisions.size();
        int count = 0;
        if (total == 0) {
            buffer.append(getText(bundle, "is")).append(getText(bundle, NOT_AVAILABLE_MESSAGE_LABEL));
        } else {
            buffer.append(getText(bundle, "for"));
            for (Map.Entry<String, BigDecimal> entry : decisions.entrySet()) {
                if (total > 1 && count == total - 1) {
                    buffer.append(getText(bundle, "and"));
                }
                buffer.append(getText(bundle, "roomclass")).append(entry.getKey()).append(" ").append(getText(bundle, "is")).append(getValueText(entry.getValue(), intentType, bundle));
                if (count < total - 2) {
                    buffer.append(", ");
                }
                count++;
            }
        }
        return buffer.toString();
    }

    private DialogIntentResponse announceRoomTypeBarDecision(DialogIntentRequest request) {
        ResourceBundle bundle = getBundle(request.getLocale());
        DialogSession session = request.getSession();
        IntentType intentType = IntentType.fromVendorIntent(request.getVendorIntent());
        StringBuilder buffer = new StringBuilder();
        buffer.append(welcomeUser(session, bundle));
        buffer.append(getText(bundle, "the"));
        buffer.append(getText(bundle, intentType.getLabel())).append(getText(bundle, "for"));
        buffer.append(announceProperty(session, bundle));
        LocalDate startDate = getStartDate(request);
        session.setAttribute(DialogSession.START_DATE, startDate.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        LocalDate endDate = getEndDate(request);
        session.setAttribute(DialogSession.END_DATE, endDate.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        buffer.append(" ").append(toSpeech(startDate, endDate, bundle, request.getLocale()));
        String roomType = (String) request.getParameter(DialogIntentRequest.ROOM_TYPE_PARAMETER);
        if ("All".equalsIgnoreCase(roomType)) {
            buffer.append(buildBarDecisionTextForAllRoomTypes(startDate, intentType, bundle));
        } else {
            buffer.append(getText(bundle, "for"));
            buffer.append(getText(bundle, ROOM_TYPE)).append(roomType).append(" ").append(getText(bundle, "is"));
            BigDecimal decision = decisionDataService.getBarDecisionByRoomType(startDate, roomType);
            if (decision == null) {
                buffer.append(getText(bundle, NOT_AVAILABLE_MESSAGE_LABEL));
            } else {
                buffer.append(getValueText(decision.setScale(2, BigDecimal.ROUND_HALF_UP), intentType, bundle));
            }
        }
        buffer.append(".");
        DialogIntentResponse response = new DialogIntentResponse();
        response.setSession(session);
        response.setText(buffer.toString());
        response.setRepromptText(getText(bundle, REPROMPT_MESSAGE_LABEL));
        response.setCloseSession(false);
        return response;
    }

    private String buildBarDecisionTextForAllRoomTypes(LocalDate occupancyDate, IntentType intentType, ResourceBundle bundle) {
        StringBuilder buffer = new StringBuilder();
        Map<String, BigDecimal> decisions = decisionDataService.getBarDecisionsAllRoomTypes(occupancyDate);
        int total = decisions.size();
        int count = 0;
        if (total == 0) {
            buffer.append(getText(bundle, "is")).append(getText(bundle, NOT_AVAILABLE_MESSAGE_LABEL));
        } else {
            buffer.append(getText(bundle, "for"));
            for (Map.Entry<String, BigDecimal> entry : decisions.entrySet()) {
                if (total > 1 && count == total - 1) {
                    buffer.append(getText(bundle, "and"));
                }
                buffer.append(getText(bundle, ROOM_TYPE)).append(entry.getKey()).append(" ").append(getText(bundle, "is")).append(getValueText(entry.getValue(), intentType, bundle));
                if (count < total - 2) {
                    buffer.append(", ");
                }
                count++;
            }
        }
        return buffer.toString();
    }

    private IntentType changeDate(DialogIntentRequest request) {
        DialogSession session = request.getSession();
        String previousValue = (String) session.getAttribute(DialogSession.STATISTIC_TYPE);
        if (previousValue != null) {
            request.setParameter(DialogIntentRequest.STATISTIC_TYPE_PARAMETER, previousValue);
        }
        previousValue = (String) session.getAttribute(DialogSession.COMPETITOR_NAME);
        if (previousValue != null) {
            request.setParameter(DialogIntentRequest.COMPETITOR_NAME_PARAMETER, previousValue);
        }
        previousValue = (String) session.getAttribute(DialogSession.INTENT);
        if (previousValue == null) {
            // if user has not previously specified an intent, default to Daily Briefing
            return IntentType.DAILY_BRIEFING;
        }
        IntentType previousIntent = IntentType.fromVendorIntent(previousValue);
        if (previousIntent == null) {
            previousIntent = IntentType.DAILY_BRIEFING;
        }
        request.setVendorIntent(previousIntent.getVendorIntent());
        return previousIntent;
    }

    private IntentType changeStatisticType(DialogIntentRequest request) {
        DialogSession session = request.getSession();
        String previousValue = (String) session.getAttribute(DialogSession.START_DATE);
        if (previousValue != null) {
            request.setParameter(DialogIntentRequest.START_DATE_PARAMETER, previousValue);
        }
        previousValue = (String) session.getAttribute(DialogSession.END_DATE);
        if (previousValue != null) {
            request.setParameter(DialogIntentRequest.END_DATE_PARAMETER, previousValue);
        }
        previousValue = (String) session.getAttribute(DialogSession.COMPETITOR_NAME);
        if (previousValue != null) {
            request.setParameter(DialogIntentRequest.COMPETITOR_NAME_PARAMETER, previousValue);
        }
        previousValue = (String) session.getAttribute(DialogSession.INTENT);
        if (previousValue == null) {
            // if user has not previously specified an intent, default to Daily Briefing
            return IntentType.DAILY_BRIEFING;
        }
        IntentType previousIntent = IntentType.fromVendorIntent(previousValue);
        if (previousIntent == null) {
            previousIntent = IntentType.DAILY_BRIEFING;
        }
        request.setVendorIntent(previousIntent.getVendorIntent());
        return previousIntent;
    }

    private DialogIntentResponse processDailyBriefingIntent(DialogIntentRequest request) {
        ResourceBundle bundle = getBundle(request.getLocale());
        DialogIntentResponse response = new DialogIntentResponse();
        response.setSession(request.getSession());
        if (getStartDate(request).equals(getEndDate(request))) {
            response.setText(cleanText(getText(bundle, "dailyBriefing")));
        } else {
            response.setText(cleanText(getText(bundle, "dailyBriefingForMonth")));
        }
        response.setRepromptText(cleanText(getText(bundle, REPROMPT_MESSAGE_LABEL)));
        response.setCloseSession(false);
        return response;
    }

    private DialogIntentResponse processFinishedIntent(DialogIntentRequest request) {
        ResourceBundle bundle = getBundle(request.getLocale());
        DialogIntentResponse response = new DialogIntentResponse();
        response.setSession(request.getSession());
        response.setText(cleanText(getGoodbyeText(bundle)));
        response.setRepromptText(cleanText(getText(bundle, REPROMPT_MESSAGE_LABEL)));
        response.setCloseSession(true);
        return response;
    }

    private String getUserFirstName() {
        GlobalUser loggedInUser = userGlobalDBService.getGlobalUserById(Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        return loggedInUser == null || loggedInUser.getFirstName() == null ? "" : loggedInUser.getFirstName();
    }

    private String getText(ResourceBundle bundle, String label) {
        return bundle.getString(label) + " ";
    }

    private DialogSession getOrCreateSession(DialogRequest request) {
        DialogSession session = request.getSession();
        if (session == null) {
            session = new DialogSession();
            request.setSession(session);
        }
        return session;
    }

    private DialogIntentResponse announceStatisticData(DialogIntentRequest request) {
        ResourceBundle bundle = getBundle(request.getLocale());
        DialogSession session = getOrCreateSession(request);
        IntentType intentType = IntentType.fromVendorIntent(request.getVendorIntent());
        Boolean isForecast = isForecast(request);
        StringBuilder buffer = new StringBuilder();
        buffer.append(welcomeUser(session, bundle));
        buffer.append(getText(bundle, "the"));
        buffer.append(getStatisticTypeText(intentType, bundle, isForecast));
        buffer.append(getText(bundle, intentType.getLabel())).append(getText(bundle, "for"));
        buffer.append(announceProperty(session, bundle));
        LocalDate startDate = getStartDate(request);
        session.setAttribute(DialogSession.START_DATE, startDate.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        LocalDate endDate = getEndDate(request);
        session.setAttribute(DialogSession.END_DATE, endDate.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        buffer.append(" ").append(toSpeech(startDate, endDate, bundle, request.getLocale()));
        buffer.append(getVerb(intentType, bundle));
        BigDecimal value = getValueForStatistic(intentType, isForecast, startDate, endDate);
        buffer.append(getValueText(value, intentType, bundle));
        buffer.append(".");
        DialogIntentResponse response = new DialogIntentResponse();
        response.setSession(session);
        response.setText(cleanText(buffer.toString()));
        response.setRepromptText(cleanText(getText(bundle, REPROMPT_MESSAGE_LABEL)));
        response.setCloseSession(false);
        return response;
    }

    private DialogIntentResponse announceRoomTypeOverbooking(DialogIntentRequest request) {
        ResourceBundle bundle = getBundle(request.getLocale());
        DialogSession session = request.getSession();
        IntentType intentType = IntentType.fromVendorIntent(request.getVendorIntent());
        StringBuilder buffer = new StringBuilder();
        buffer.append(welcomeUser(session, bundle));
        buffer.append(getText(bundle, "the"));
        buffer.append(getText(bundle, intentType.getLabel())).append(getText(bundle, "for"));
        buffer.append(announceProperty(session, bundle));
        LocalDate startDate = getStartDate(request);
        session.setAttribute(DialogSession.START_DATE, startDate.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        LocalDate endDate = getEndDate(request);
        session.setAttribute(DialogSession.END_DATE, endDate.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        buffer.append(" ").append(toSpeech(startDate, endDate, bundle, request.getLocale()));
        String roomType = (String) request.getParameter(DialogIntentRequest.ROOM_TYPE_PARAMETER);
        if ("All".equalsIgnoreCase(roomType)) {
            buffer.append(buildOverbookingTextForAllRoomTypes(startDate, bundle));
        } else {
            buffer.append(getText(bundle, "for"));
            buffer.append(getText(bundle, ROOM_TYPE)).append(roomType).append(" ").append(getText(bundle, "is"));
            Integer decision = decisionDataService.getOverbookingDecision(startDate, roomType);
            if (decision == null) {
                buffer.append(getText(bundle, NOT_AVAILABLE_MESSAGE_LABEL));
            } else {
                buffer.append(decision).append(" ").append(getText(bundle, ROOMS));
            }
        }
        buffer.append(".");
        DialogIntentResponse response = new DialogIntentResponse();
        response.setSession(session);
        response.setText(cleanText(buffer.toString()));
        response.setRepromptText(cleanText(getText(bundle, REPROMPT_MESSAGE_LABEL)));
        response.setCloseSession(false);
        return response;
    }

    private String buildOverbookingTextForAllRoomTypes(LocalDate occupancyDate, ResourceBundle bundle) {
        StringBuilder buffer = new StringBuilder();
        Map<String, Integer> decisions = decisionDataService.getOverbookingDecisionsAllRoomTypes(occupancyDate);
        int total = decisions.size();
        int count = 0;
        if (total == 0) {
            buffer.append(getText(bundle, "is")).append(getText(bundle, NOT_AVAILABLE_MESSAGE_LABEL));
        } else {
            buffer.append(getText(bundle, "for"));
            for (Map.Entry<String, Integer> entry : decisions.entrySet()) {
                if (total > 1 && count == total - 1) {
                    buffer.append(getText(bundle, "and"));
                }
                buffer.append(getText(bundle, ROOM_TYPE)).append(entry.getKey()).append(" ").append(getText(bundle, "is")).append(entry.getValue()).append(" ").append(getText(bundle, ROOMS));
                if (count < total - 2) {
                    buffer.append(", ");
                }
                count++;
            }
        }
        return buffer.toString();
    }

    private String welcomeUser(DialogSession session, ResourceBundle bundle) {
        if (session.getAttribute(DialogSession.USER_WELCOMED) == null || !((Boolean) session.getAttribute(DialogSession.USER_WELCOMED))) {
            session.setAttribute(DialogSession.USER_WELCOMED, true);
            return getText(bundle, "hello") + getUserFirstName() + " , ";
        }
        return "";
    }

    private String announceProperty(DialogSession session, ResourceBundle bundle) {
        if (session.getAttribute(DialogSession.PROPERTY_ANNOUNCED) == null || !((Boolean) session.getAttribute(DialogSession.PROPERTY_ANNOUNCED))) {
            Property property = Property.fromSession(session.getAttribute(DialogSession.PROPERTY));
            if (property != null && property.getPropertyName() != null) {
                session.setAttribute(DialogSession.PROPERTY_ANNOUNCED, true);
                return property.getPropertyName() + " " + getText(bundle, "for");
            }
        }
        return "";
    }

    private String getValueText(BigDecimal value, IntentType intentType, ResourceBundle bundle) {
        if (value == null) {
            return getText(bundle, NOT_AVAILABLE_MESSAGE_LABEL);
        }
        StringBuilder buffer = new StringBuilder();
        if (intentType.isCurrency()) {
            buffer.append(BLANK_SPACE);
            buffer.append(currencySymbol.getCurrencySymbol(getYieldCurrencyName()));
        }
        buffer.append(setScaleToTwo(value)).append(" ");
        if (!intentType.isCurrency()) {
            buffer.append(getText(bundle, ROOMS));
        }
        return buffer.toString();
    }

    private String getYieldCurrencyName() {
        final List<String> hiltonExternalSystem = Arrays.asList(ExternalSystem.HILSTAR.getExternalSystemName(), ExternalSystem.PCRS.getExternalSystemName());
        if (hiltonExternalSystem.contains(pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value()))) {
            return pacmanConfigParamsService.getParameterValue("pacman.integration.ratchet.yieldCurrencyCode");
        }
        return pacmanConfigParamsService.getParameterValue(GUIConfigParamName.CORE_PROPERTY_YIELD_CURRENCY_CODE.value());
    }

    private String getStatisticTypeText(IntentType intentType, ResourceBundle bundle, Boolean isForecast) {
        if (intentType.isForecasted()) {
            if (isForecast) {
                return getText(bundle, "forecasted");
            } else {
                return getText(bundle, "onBooks");
            }
        } else {
            return "";
        }
    }

    private String getVerb(IntentType intentType, ResourceBundle bundle) {
        if (IntentType.ARRIVALS.equals(intentType) || IntentType.DEPARTURES.equals(intentType)) {
            return getText(bundle, "are");
        } else {
            return getText(bundle, "is");
        }
    }

    private ResourceBundle getBundle(String locale) {
        if (locale == null) {
            return ResourceBundle.getBundle(RESOURCE_BUNDLE_NAME);
        }
        try {
            Locale localeObj = LocaleUtils.toLocale(locale);
            return ResourceBundle.getBundle(RESOURCE_BUNDLE_NAME, localeObj);
        } catch (IllegalArgumentException e) {
            LOGGER.info(e);
            return ResourceBundle.getBundle(RESOURCE_BUNDLE_NAME);
        }
    }

    private DialogIntentResponse announceCompetitorData(DialogIntentRequest request) {
        ResourceBundle bundle = getBundle(request.getLocale());
        DialogSession session = getOrCreateSession(request);
        IntentType intentType = IntentType.fromVendorIntent(request.getVendorIntent());
        StringBuilder buffer = new StringBuilder();
        buffer.append(welcomeUser(session, bundle));
        buffer.append(getText(bundle, "the"));
        buffer.append(getText(bundle, intentType.getLabel())).append(getText(bundle, "for"));
        LocalDate startDate = getStartDate(request);
        session.setAttribute(DialogSession.START_DATE, startDate.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        LocalDate endDate = getEndDate(request);
        session.setAttribute(DialogSession.END_DATE, endDate.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        buffer.append(" ").append(toSpeech(startDate, endDate, bundle, request.getLocale()));
        buffer.append(getText(bundle, "is"));
        CompetitorRateInfo rateInfo = getCompetitorData(request, startDate);
        if (rateInfo == null) {
            buffer.append(getText(bundle, NOT_AVAILABLE_MESSAGE_LABEL));
        } else {
            buffer.append(getValueText(rateInfo.getRate(), intentType, bundle));
            buffer.append(getText(bundle, "forTheCompetitor")).append(rateInfo.getCompetitorName());
        }
        buffer.append(".");
        DialogIntentResponse response = new DialogIntentResponse();
        response.setSession(session);
        response.setText(cleanText(buffer.toString()));
        response.setRepromptText(cleanText(getText(bundle, REPROMPT_MESSAGE_LABEL)));
        response.setCloseSession(false);
        return response;
    }

    private DialogIntentResponse announceNotificationTypeCounts(DialogIntentRequest request) {
        ResourceBundle bundle = getBundle(request.getLocale());
        DialogSession session = getOrCreateSession(request);
        // use ALERT as default notification type
        NotificationType notificationType = NotificationType.ALERT;
        String typeValue = (String) request.getParameter(DialogIntentRequest.NOTIFICATION_TYPE_PARAMETER);
        if (typeValue != null && NotificationType.fromTypeString(typeValue) != null) {
            notificationType = NotificationType.fromTypeString(typeValue);
        }
        StringBuilder buffer = new StringBuilder();
        buffer.append(welcomeUser(session, bundle));
        Map<String, Integer> map = getNotificationTypeCounts(notificationType);
        if (map.size() == 0) {
            buffer.append(getText(bundle, "youHaveNo")).append(getText(bundle, "active")).append(getText(bundle, notificationType.getLabel()));
        } else {
            buffer.append(getText(bundle, "youHave"));
            int total = map.size();
            int current = 0;
            for (Map.Entry<String, Integer> entry : map.entrySet()) {
                if (total > 1 && current == total - 1) {
                    buffer.append(getText(bundle, "and"));
                }
                buffer.append(entry.getValue()).append(" ").append(getText(bundle, entry.getKey())).
                        append(getText(bundle, notificationType.getLabel()));
                if (current != total - 1) {
                    buffer.append(", ");
                }
                current++;
            }
        }
        buffer.append(".");
        DialogIntentResponse response = new DialogIntentResponse();
        response.setSession(session);
        response.setText(cleanText(buffer.toString()));
        response.setRepromptText(cleanText(getText(bundle, REPROMPT_MESSAGE_LABEL)));
        response.setCloseSession(false);
        return response;
    }

    private Map<String, Integer> getNotificationTypeCounts(NotificationType notificationType) {
        List<InfoMgrExcepNotifEntity> notifications = getNotifications(notificationType);
        Map<String, Integer> map = new HashMap<>();
        for (InfoMgrExcepNotifEntity entity : notifications) {
            String type = entity.getAlertType().getDescription();
            Integer count = map.get(type);
            if (count == null) {
                map.put(type, 1);
            } else {
                map.put(type, count + 1);
            }
        }
        return map;
    }

    private DialogIntentResponse announceNotificationCounts(DialogIntentRequest request) {
        ResourceBundle bundle = getBundle(request.getLocale());
        DialogSession session = getOrCreateSession(request);
        // use ALERT as default notification type
        NotificationType notificationType = NotificationType.ALERT;
        String typeValue = (String) request.getParameter(DialogIntentRequest.NOTIFICATION_TYPE_PARAMETER);
        if (typeValue != null && NotificationType.fromTypeString(typeValue) != null) {
            notificationType = NotificationType.fromTypeString(typeValue);
        }
        StringBuilder buffer = new StringBuilder();
        buffer.append(welcomeUser(session, bundle));
        int count = getNotificationCount(notificationType);
        if (count == 0) {
            buffer.append(getText(bundle, "youHaveNo"));
        } else {
            buffer.append(getText(bundle, "youHave"));
            buffer.append(count).append(" ");
        }
        buffer.append(getText(bundle, "active")).append(getText(bundle, notificationType.getLabel()));
        buffer.append(".");
        DialogIntentResponse response = new DialogIntentResponse();
        response.setSession(session);
        response.setText(cleanText(buffer.toString()));
        response.setRepromptText(cleanText(getText(bundle, REPROMPT_MESSAGE_LABEL)));
        response.setCloseSession(false);
        return response;
    }

    private int getNotificationCount(NotificationType notificationType) {
        List<InfoMgrExcepNotifEntity> notifications = getNotifications(notificationType);
        return notifications == null ? 0 : notifications.size();
    }

    private List<InfoMgrExcepNotifEntity> getNotifications(NotificationType notificationType) {
        List<InfoMgrExcepNotifEntity> notifications = null;
        SearchCriteriaDTO searchCriteriaDTO = new SearchCriteriaDTO();
        searchCriteriaDTO.setPropertyIds(Arrays.asList(PacmanWorkContextHelper.getPropertyId()));
        switch (notificationType) {
            case ALERT:
                notifications = alertService.searchWithCriteria(searchCriteriaDTO, Constants.ALERT_CATEGORY);
                break;
            case NOTIFICATION:
                notifications = alertService.searchWithCriteria(searchCriteriaDTO, Constants.EXCEPTION_CATEGORY);
                break;
            case EXCEPTION:
                notifications = alertService.searchWithCriteria(searchCriteriaDTO, Constants.SYSTEM_EXCEPTION_CATEGORY);
                break;
            default:
                break;
        }
        return notifications;
    }

    private Boolean isForecast(DialogIntentRequest request) {
        String value = (String) request.getParameter(DialogIntentRequest.STATISTIC_TYPE_PARAMETER);
        if (Strings.isNullOrEmpty(value)) {
            return false;
        }
        request.getSession().setAttribute(DialogSession.STATISTIC_TYPE, value);
        StatisticType statisticType = StatisticType.fromTypeString(value);
        return StatisticType.FORECASTED.equals(statisticType);
    }

    private LocalDate getStartDate(DialogIntentRequest request) {
        String requestDate = (String) request.getParameter(DialogIntentRequest.START_DATE_PARAMETER);
        if (!Strings.isNullOrEmpty(requestDate)) {
            return LocalDate.parse(requestDate);
        }
        String sessionDate = (String) request.getSession().getAttribute(DialogSession.START_DATE);
        if (!Strings.isNullOrEmpty(sessionDate)) {
            return LocalDate.parse(sessionDate);
        }
        return LocalDate.now();
    }

    private LocalDate getEndDate(DialogIntentRequest request) {
        String requestDate = (String) request.getParameter(DialogIntentRequest.END_DATE_PARAMETER);
        if (!Strings.isNullOrEmpty(requestDate)) {
            return LocalDate.parse(requestDate);
        }
        String sessionDate = (String) request.getSession().getAttribute(DialogSession.END_DATE);
        if (!Strings.isNullOrEmpty(sessionDate)) {
            return LocalDate.parse(sessionDate);
        }
        return LocalDate.now();
    }

    private String toSpeech(LocalDate startDate, LocalDate endDate, ResourceBundle bundle, String localeString) {
        Locale locale = null;
        try {
            locale = LocaleUtils.toLocale(localeString);
        } catch (IllegalArgumentException e) {
            // use default locale
            LOGGER.info(e);
        }
        StringBuilder buffer = new StringBuilder(locale == null ? startDate.format(DateTimeFormatter.ofPattern(TEXT_DATE_FORMAT)) : startDate.format(DateTimeFormatter.ofPattern(TEXT_DATE_FORMAT, locale)));
        buffer.append(" ");
        if (endDate.isAfter(startDate)) {
            buffer.append(getText(bundle, "through")).append(locale == null ? endDate.format(DateTimeFormatter.ofPattern(TEXT_DATE_FORMAT)) : endDate.format(DateTimeFormatter.ofPattern(TEXT_DATE_FORMAT, locale))).append(" ");
        }
        return buffer.toString();

    }

    private BigDecimal getValueForStatistic(IntentType intentType, Boolean isForecast, LocalDate startDate, LocalDate endDate) {
        BigDecimal value = null;
        switch (intentType) {
            case ARRIVALS:
            case DEPARTURES:
            case OCCUPANCY:
                value = operationsDataService.getStatisticValue(intentType, isForecast, startDate, endDate);
                break;
            case ROOM_REVENUE:
                value = revenueDataService.getStatisticValue(intentType, isForecast, startDate, endDate);
                break;
            case LAST_ROOM_VALUE:
                value = decisionDataService.getLastRoomValue(startDate);
                break;
            case BAR_DECISION:
                value = decisionDataService.getBarDecision(startDate);
                break;
            case OVERBOOKING:
                value = decisionDataService.getOverbookingDecision(startDate);
                break;
            case AVERAGE_COMPETITOR_RATE:
                value = competitorDataService.getAverageCompetitorRate(startDate);
                break;
            case CAPACITY:
                value = capacityDataService.getCapacity(startDate, endDate);
                break;
            case AVAILABLE_CAPACITY:
                BigDecimal capacity = capacityDataService.getCapacity(startDate, endDate);
                BigDecimal ooo = capacityDataService.getOOO(startDate, endDate);
                value = capacity != null && ooo != null ? capacity.subtract(ooo) : null;
                break;
            case OUT_OF_ORDER:
                value = capacityDataService.getOOO(startDate, endDate);
                break;
            case REV_PAR:
                value = computeRevPAR(isForecast, startDate, endDate);
                break;
            case ADR:
                value = computeADR(isForecast, startDate, endDate);
                break;
            default:
                // use default value
                break;
        }
        return value;
    }

    private BigDecimal computeADR(Boolean isForecast, LocalDate startDate, LocalDate endDate) {
        BigDecimal revenue = revenueDataService.getStatisticValue(IntentType.ROOM_REVENUE, isForecast, startDate, endDate);
        BigDecimal roomsSold = operationsDataService.getStatisticValue(IntentType.OCCUPANCY, isForecast, startDate, endDate);
        if (revenue == null || roomsSold == null) {
            return null;
        }
        return roomsSold.compareTo(BigDecimal.ZERO) > 0 ? revenue.divide(roomsSold, RoundingMode.HALF_EVEN) : BigDecimal.ZERO;
    }

    private BigDecimal computeRevPAR(Boolean isForecast, LocalDate startDate, LocalDate endDate) {
        BigDecimal revenue = revenueDataService.getStatisticValue(IntentType.ROOM_REVENUE, isForecast, startDate, endDate);
        BigDecimal capacityValue = capacityDataService.getCapacity(startDate, endDate);
        if (revenue == null || capacityValue == null) {
            return null;
        }
        return capacityValue.compareTo(BigDecimal.ZERO) > 0 ? revenue.divide(capacityValue, RoundingMode.HALF_EVEN) : BigDecimal.ZERO;
    }

    private CompetitorRateInfo getCompetitorData(DialogIntentRequest request, LocalDate occupancyDate) {
        String vendorIntent = request.getVendorIntent();
        IntentType intentType = IntentType.fromVendorIntent(vendorIntent);
        CompetitorRateInfo data = null;
        if (IntentType.HIGHEST_COMPETITOR_RATE.equals(intentType)) {
            data = competitorDataService.getHighestCompetitorRate(occupancyDate);
        } else if (IntentType.LOWEST_COMPETITOR_RATE.equals(intentType)) {
            data = competitorDataService.getLowestCompetitorRate(occupancyDate);
        } else if (IntentType.COMPETITOR_RATE.equals(intentType)) {
            String competitorName = getCompetitorName(request);
            request.getSession().setAttribute(DialogSession.COMPETITOR_NAME, competitorName);
            data = competitorDataService.getRateForCompetitor(occupancyDate, competitorName);
        }
        return data;
    }

    private DialogIntentResponse switchProperty(DialogIntentRequest request) {
        ResourceBundle bundle = getBundle(request.getLocale());
        DialogSession session = request.getSession();
        StringBuilder buffer = new StringBuilder();
        buffer.append(welcomeUser(session, bundle));
        Integer currentPropertyId = Property.fromSession(session.getAttribute(DialogSession.PROPERTY)).getPropertyId();
        final String requestedPropertyName = (String) request.getParameter(DialogIntentRequest.PROPERTY_NAME_PARAMETER);
        List<Property> matchingProperties = findMatchingAuthorizedProperties(requestedPropertyName);
        if (matchingProperties.isEmpty()) {
            buffer.append(requestedPropertyName);
            buffer.append(BLANK_SPACE);
            buffer.append(getText(bundle, "property.not.found.prompt"));
        } else if (matchingProperties.size() == 1) {
            if (matchingProperties.get(0).getPropertyId().equals(currentPropertyId)) {
                buffer.append(getText(bundle, "property.not.change.prompt"));
            } else {
                request.getSession().setAttribute(DialogSession.PROPERTY, matchingProperties.get(0));
                buffer.append(getText(bundle, "property.change.successfully"));
                buffer.append(matchingProperties.get(0).getPropertyName());
            }
        } else {
            final String propertiesString = String.join(",", matchingProperties.stream().map(property -> property.getPropertyName()).collect(Collectors.toList()));
            buffer.append(getText(bundle, "multiple.properties.prompt"));
            buffer.append(propertiesString);
            buffer.append(FULL_STOP);
            buffer.append(getText(bundle, "please.select.one"));
        }
        buffer.append(".");
        DialogIntentResponse response = new DialogIntentResponse();
        session.setAttribute(DialogSession.PROPERTY_ANNOUNCED, false); // since the property has changed, announce it as part of the next intent request
        response.setSession(session);
        response.setText(cleanText(buffer.toString()));
        response.setRepromptText(cleanText(getText(bundle, REPROMPT_MESSAGE_LABEL)));
        response.setCloseSession(false);
        return response;
    }

    public List<Property> findMatchingAuthorizedProperties(String requestedPropertyName) {
        if (requestedPropertyName == null) {
            return new ArrayList<>();
        }
        List<Property> authorizedProperties = authorizationService.retrieveActiveAuthorizedProperties().stream().map(entity -> fromEntity(entity)).collect(Collectors.toList());
        if (authorizedProperties.isEmpty()) {
            return new ArrayList<>();
        }
        Optional<Property> exactMatch = authorizedProperties.stream().filter(prop -> normalize(prop.getPropertyCode()).equals(normalize(requestedPropertyName)) ||
                normalize(prop.getPropertyName()).equals(normalize(requestedPropertyName))).findAny();
        if (exactMatch.isPresent()) {
            return Arrays.asList(exactMatch.get());
        }
        return authorizedProperties.stream().filter(prop ->
                StringUtils.getLevenshteinDistance(normalize(prop.getPropertyName()), normalize(requestedPropertyName)) <= MAX_LEVENSHTEIN_DISTANCE).collect(Collectors.toList());
    }

    private String normalize(String input) {
        return input.replaceAll("[^a-zA-Z0-9]", "").toLowerCase();
    }

    private Property fromEntity(com.ideas.tetris.platform.services.daoandentities.entity.Property entity) {
        Property prop = new Property();
        prop.setPropertyId(entity.getId());
        prop.setPropertyCode(entity.getCode());
        prop.setClientId(entity.getClient().getId());
        prop.setClientCode(entity.getClient().getCode());
        prop.setPropertyName(entity.getName());
        return prop;
    }

    private String getCompetitorName(DialogIntentRequest request) {
        return (String) request.getParameter(DialogIntentRequest.COMPETITOR_NAME_PARAMETER);
    }

    private BigDecimal setScaleToTwo(BigDecimal amount) {
        if (amount.scale() > 2) {
            amount = amount.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        return amount;
    }

    private DialogUserSession findOrCreateUserSession(DialogSession session) {
        DialogUserSession userSession = null;
        if (session.getAttribute(DialogSession.USER_SESSION_ID) != null) {
            userSession = globalCrudService.find(DialogUserSession.class, (Integer) session.getAttribute(DialogSession.USER_SESSION_ID));
        }
        if (userSession == null) {
            userSession = new DialogUserSession();
            userSession.setStartDate(LocalDateTime.now());
            userSession.setUserId(Integer.parseInt(PacmanWorkContextHelper.getUserId()));
            userSession.setClientId(PacmanWorkContextHelper.getClientId());
            userSession = globalCrudService.save(userSession);
            session.setAttribute(DialogSession.USER_SESSION_ID, userSession.getId());
        }
        return userSession;
    }

    private void addRequestToUserSession(DialogSession session, IntentType intentType, Map<String, Object> parameters, String response) {
        DialogUserSession userSession = findOrCreateUserSession(session);
        DialogUserRequest userRequest = new DialogUserRequest();
        userRequest.setSession(userSession);
        userRequest.setIntent(intentType.getVendorIntent());
        userRequest.setRequestDate(LocalDateTime.now());
        userRequest.setParameters(toString(parameters));
        userRequest.setResponse(response);
        userSession.addRequest(userRequest);
        globalCrudService.save(userSession);
    }

    private String toString(Map<String, Object> map) {
        StringBuilder buffer = new StringBuilder();
        map.keySet().stream().map(key -> key + " = " + map.get(key).toString()).forEach(text -> buffer.append(text).append(","));
        return buffer.toString();
    }

    private void endUserSession(DialogSession session) {
        Integer userSessionId = (Integer) session.getAttribute(DialogSession.USER_SESSION_ID);
        if (userSessionId != null) {
            DialogUserSession userSession = globalCrudService.find(DialogUserSession.class, userSessionId);
            if (userSession != null) {

                userSession.setEndDate(LocalDateTime.now());
                globalCrudService.save(userSession);
            }
        }
    }

    private boolean userHasRecentSession() {
        DialogUserSession mostRecentSession = getMostRecentClosedSession();
        if (mostRecentSession == null) {
            return false;
        }
        return mostRecentSession.getEndDate().plusMinutes(RECENT_SESSION_THRESHOLD).compareTo(LocalDateTime.now()) > 0;
    }

    private DialogUserSession getMostRecentClosedSession() {
        List<DialogUserSession> sessions = globalCrudService.findByNamedQuery(DialogUserSession.FIND_CLOSED_SESSIONS_BY_USER, QueryParameter.with("userId", Integer.parseInt(PacmanWorkContextHelper.getUserId())).parameters());
        return sessions.isEmpty() ? null : sessions.get(0);
    }

    protected String cleanText(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        input = input.replaceAll("ä", "ae");
        input = input.replaceAll("Ä", "Ae");
        input = input.replaceAll("ü", "ue");
        input = input.replaceAll("Ü", "Ue");
        input = input.replaceAll("ö", "oe");
        input = input.replaceAll("Ö", "Oe");
        input = input.replaceAll("ẞ", "Ss");
        input = input.replaceAll("ß", "ss");
        return input;
    }

    protected void setWorkContextPropertyForNewSession(DialogSession session) {
        Property property = Property.fromSession(session.getAttribute(DialogSession.PROPERTY));
        if (property == null || property.getPropertyId() == null || property.getPropertyCode() == null
                || property.getClientId() == null || property.getClientCode() == null) {
            LOGGER.info("property not already in session, finding user's default property...");
            property = getUsersDefaultProperty();
        }
        if (property != null) {
            session.setAttribute(DialogSession.PROPERTY, property.toMap());
            PacmanWorkContextHelper.setPropertyId(property.getPropertyId());
            PacmanWorkContextHelper.setPropertyCode(property.getPropertyCode());
            PacmanWorkContextHelper.setClientId(property.getClientId());
            PacmanWorkContextHelper.setClientCode(property.getClientCode());
        }
    }

    private Property getUsersDefaultProperty() {
        LDAPUser user = userService.getById(Integer.valueOf(PacmanWorkContextHelper.getUserId()));
        if (user != null && user.getDefaultProperty() != null) {
            Integer propertyId = Integer.valueOf(user.getDefaultProperty());
            com.ideas.tetris.platform.services.daoandentities.entity.Property propertyEntity = propertyService.getPropertyById(propertyId);
            Property property = new Property();
            property.setPropertyId(propertyId);
            property.setPropertyName(propertyEntity.getName());
            property.setPropertyCode(propertyEntity.getCode());
            property.setClientCode(propertyEntity.getClient().getCode());
            property.setClientId(propertyEntity.getClient().getId());
            LOGGER.info("set default property Id = " + propertyId + ", propertyName = " + propertyEntity.getName());
            return property;
        } else {
            LOGGER.error("unable to find default property for user " + PacmanWorkContextHelper.getUserId());
            return null;
        }
    }

    private DialogIntentResponse announceDailyBriefing(DialogIntentRequest request) {
        ResourceBundle bundle = getBundle(request.getLocale());
        DialogSession session = request.getSession();
        StringBuilder buffer = new StringBuilder();
        buffer.append(welcomeUser(session, bundle));
        buffer.append(announceProperty(session, bundle));
        LocalDate startDate = getStartDate(request);
        LocalDate firstDayOfMonth = startDate.withDayOfMonth(1);
        session.setAttribute(DialogSession.START_DATE, startDate.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        LocalDate endDate = getEndDate(request);
        session.setAttribute(DialogSession.END_DATE, endDate.format(DateTimeFormatter.ofPattern(DATE_FORMAT)));
        buffer.append(getText(bundle, "here.is.the.overview.for"));
        buffer.append(getMonthName(startDate)).append(FULL_STOP);
        DailyBriefing dailyBriefingMonthlySummary = dailyBriefingDataService.getDailyBriefingMonthlySummary(firstDayOfMonth, firstDayOfMonth.plusMonths(1).plusDays(-1));
        if (null != dailyBriefingMonthlySummary && null != dailyBriefingMonthlySummary.getOnBooksRoomSold() && null != dailyBriefingMonthlySummary.getOnBooksRoomRevenue()) {
            appendSentenceForDailyBriefing(dailyBriefingMonthlySummary, request, buffer, bundle);
        } else {
            buffer.append(getText(bundle, "is")).append(getText(bundle, NOT_AVAILABLE_MESSAGE_LABEL));
        }
        DialogIntentResponse response = new DialogIntentResponse();
        response.setSession(session);
        response.setText(cleanText(buffer.toString()));
        response.setRepromptText(cleanText(getText(bundle, REPROMPT_MESSAGE_LABEL)));
        response.setCloseSession(false);
        return response;
    }

    private void appendSentenceForDailyBriefing(DailyBriefing dailyBriefing, DialogIntentRequest request, StringBuilder buffer, ResourceBundle bundle) {
        IntentType intentType = IntentType.fromVendorIntent(request.getVendorIntent());
        appendOnBooksAndStylSentence(dailyBriefing, intentType, bundle, buffer);
        appendSpecialEventSentence(dailyBriefing, request, buffer);
        appendForecastedAndBudgetSentence(dailyBriefing, intentType, buffer, bundle);
    }

    private void appendOnBooksAndStylSentence(DailyBriefing dailyBriefing, IntentType intentType, ResourceBundle bundle, StringBuilder buffer) {
        buffer.append(getText(bundle, "there.are.currently"));
        buffer.append(dailyBriefing.getOnBooksRoomSold()).append(BLANK_SPACE);
        buffer.append(getText(bundle, "rooms.sold")).append(FULL_STOP);
        if (dailyBriefing.hasStlyDataAvailable()) {
            buffer.append(getText(bundle, "this.is"));
            appendOnBooksAndStlyPercDiff(dailyBriefing.compareOnBooksRoomSoldWithStlyRoomSold(), bundle, buffer);
            buffer.append(FULL_STOP);
        }
        appendPickUpSentence(dailyBriefing, buffer, bundle);
        buffer.append(getText(bundle, "revenue.on.the.books.is"));
        buffer.append(getValueText(dailyBriefing.getOnBooksRoomRevenue(), intentType, bundle));
        if (dailyBriefing.hasStlyDataAvailable()) {
            buffer.append(COMMA_WITH_SPACE);
            appendOnBooksAndStlyPercDiff(dailyBriefing.compareOnBooksRoomRevenueWithStlyRoomRevenue(), bundle, buffer);
        }
        buffer.append(getText(bundle, "and"));
        appendOnBooksAdrSentence(dailyBriefing, intentType, buffer, bundle);
        buffer.append(getText(bundle, "making"));
        appendOnBooksRevParSentence(dailyBriefing, intentType, buffer, bundle);
        buffer.append(FULL_STOP);
    }

    private void appendOnBooksAdrSentence(DailyBriefing dailyBriefing, IntentType intentType, StringBuilder buffer, ResourceBundle bundle) {
        BigDecimal onBooksAdr = dailyBriefing.fetchOnBooksADR();
        buffer.append(getText(bundle, "adr.on.the.books.is"));
        buffer.append(getValueText(onBooksAdr, intentType, bundle));
        if (dailyBriefing.hasStlyDataAvailable()) {
            buffer.append(COMMA_WITH_SPACE);
            appendOnBooksAndStlyPercDiff(dailyBriefing.compareOnBooksADRWithStlyADR(), bundle, buffer);
        }
    }

    private void appendOnBooksRevParSentence(DailyBriefing dailyBriefing, IntentType intentType, StringBuilder buffer, ResourceBundle bundle) {
        BigDecimal onBooksRevPar = dailyBriefing.fetchOnBooksRevPar();
        buffer.append(getText(bundle, "revPAR"));
        buffer.append(getValueText(onBooksRevPar, intentType, bundle));
        if (dailyBriefing.hasStlyDataAvailable()) {
            buffer.append(COMMA_WITH_SPACE);
            appendOnBooksAndStlyPercDiff(dailyBriefing.compareOnBooksRevParWithStlyRevPar(), bundle, buffer);
        }
    }

    private void appendForecastedAndBudgetSentence(DailyBriefing dailyBriefing, IntentType intentType, StringBuilder buffer, ResourceBundle bundle) {
        boolean isBudgetEnabled = (dailyBriefingDataService.isBudgetEnabled() && null != dailyBriefing.getBudgetRoomSold() && null != dailyBriefing.getBudgetRoomRevenue()) ? true : false;
        if ((null != dailyBriefing.getForecastedRoomSold() && null != dailyBriefing.getForecastedRoomRevenue())) {
            appendForecastedRoomSoldSentence(dailyBriefing, isBudgetEnabled, buffer, bundle);
            appendForecastedAdrSentence(dailyBriefing, intentType, isBudgetEnabled, buffer, bundle);
            appendForecastedRevParSentence(dailyBriefing, intentType, isBudgetEnabled, buffer, bundle);
        }
    }

    private void appendForecastedRoomSoldSentence(DailyBriefing dailyBriefing, boolean isBudgetEnabled, StringBuilder buffer, ResourceBundle bundle) {
        buffer.append(getText(bundle, "forecasted.rooms.sold.are"));
        buffer.append(dailyBriefing.getForecastedRoomSold());
        if (isBudgetEnabled || dailyBriefing.hasLastYearDataAvailable()) {
            buffer.append(", ");
            buffer.append(getText(bundle, "this.is"));
        }
        if (isBudgetEnabled) {
            appendForecastAndBudgetPercDiff(dailyBriefing.compareForecastedRoomSoldWithBudgetedRoomSold(), buffer, bundle);
        }
        if (isBudgetEnabled && dailyBriefing.hasLastYearDataAvailable()) {
            buffer.append(getText(bundle, "and"));
        }
        if (dailyBriefing.hasLastYearDataAvailable()) {
            appendForecastAndLastYearPercDiff(dailyBriefing.compareForecastedRoomSoldWithLastYearRoomSold(), buffer, bundle);
        }
        buffer.append(FULL_STOP);
    }

    private void appendForecastedAdrSentence(DailyBriefing dailyBriefing, IntentType intentType, boolean isBudgetEnabled, StringBuilder buffer, ResourceBundle bundle) {
        BigDecimal forecastedAdr = dailyBriefing.fetchForecastedADR();
        buffer.append(getText(bundle, "forecasted.adr.is"));
        buffer.append(getValueText(forecastedAdr, intentType, bundle));
        buffer.append(COMMA_WITH_SPACE);
        if (isBudgetEnabled || dailyBriefing.hasLastYearDataAvailable()) {
            buffer.append(getText(bundle, "which.is"));
        }
        if (isBudgetEnabled) {
            appendForecastAndBudgetPercDiff(dailyBriefing.compareForecastedADRWithBudgetADR(), buffer, bundle);
        }
        if (isBudgetEnabled && dailyBriefing.hasLastYearDataAvailable()) {
            buffer.append(getText(bundle, "and"));
        }
        if (dailyBriefing.hasLastYearDataAvailable()) {
            appendForecastAndLastYearPercDiff(dailyBriefing.compareForecastedADRWithLastYearADR(), buffer, bundle);
        }
        if (isBudgetEnabled || dailyBriefing.hasLastYearDataAvailable()) {
            buffer.append(FULL_STOP);
        }
    }

    private void appendForecastedRevParSentence(DailyBriefing dailyBriefing, IntentType intentType, boolean isBudgetEnabled, StringBuilder buffer, ResourceBundle bundle) {
        BigDecimal forecastedRevPar = dailyBriefing.fetchForecastedRevPar();
        buffer.append(getText(bundle, "forecasted.revpar.is"));
        buffer.append(getValueText(forecastedRevPar, intentType, bundle));
        if (isBudgetEnabled || dailyBriefing.hasLastYearDataAvailable()) {
            buffer.append(COMMA_WITH_SPACE);
            buffer.append(getText(bundle, "which.is"));
        }
        if (isBudgetEnabled) {
            appendForecastAndBudgetPercDiff(dailyBriefing.compareForecastedRevParWithBudgetRevPar(), buffer, bundle);
        }
        if (isBudgetEnabled && dailyBriefing.hasLastYearDataAvailable()) {
            buffer.append(getText(bundle, "and"));
        }
        if (dailyBriefing.hasLastYearDataAvailable()) {
            appendForecastAndLastYearPercDiff(dailyBriefing.compareForecastedRevParWithLastYearRevPar(), buffer, bundle);
        }
        buffer.append(FULL_STOP);
    }

    private void appendOnBooksAndStlyPercDiff(BigDecimal onBooksStylPercDiff, ResourceBundle bundle, StringBuilder buffer) {
        appendPercentageSentence(onBooksStylPercDiff, AHEAD_BEHIND, buffer, bundle);
        appendTypeofSentence(STLY, buffer, bundle);
    }

    private void appendForecastAndBudgetPercDiff(BigDecimal forecastBudgetPercDiff, StringBuilder buffer, ResourceBundle bundle) {
        appendPercentageSentence(forecastBudgetPercDiff, ABOVE_BELOW, buffer, bundle);
        appendTypeofSentence("budget", buffer, bundle);
    }

    private void appendForecastAndLastYearPercDiff(BigDecimal forecastLastYearPercDiff, StringBuilder buffer, ResourceBundle bundle) {
        appendPercentageSentence(forecastLastYearPercDiff, UP_DOWN, buffer, bundle);
        appendTypeofSentence(LAST_YEAR, buffer, bundle);
    }

    private void appendPickUpSentence(DailyBriefing dailyBriefingVO, StringBuilder buffer, ResourceBundle bundle) {
        if (null != dailyBriefingVO.getTransientRoomSold() && null != dailyBriefingVO.getGroupRoomSold()) {
            buffer.append(getText(bundle, "we.have.picked.up"));
            final int pickupValue = Math.addExact(dailyBriefingVO.getTransientRoomSold(), dailyBriefingVO.getGroupRoomSold());
            buffer.append(pickupValue).append(BLANK_SPACE);
            buffer.append(getText(bundle, "rooms.in.the.last.week"));
            if (pickupValue > 0) {
                buffer.append(COMMA_WITH_SPACE);
                buffer.append(dailyBriefingVO.getTransientRoomSold()).append(BLANK_SPACE);
                buffer.append(getText(bundle, "of.these.were.transient.room.nights.and"));
                buffer.append(dailyBriefingVO.getGroupRoomSold()).append(BLANK_SPACE);
                buffer.append(getText(bundle, "of.these.were.group.room.nights"));
            } else {
                buffer.append(FULL_STOP);
            }
        }
    }

    private void appendSpecialEventSentence(DailyBriefing dailyBriefingVO, DialogIntentRequest request, StringBuilder buffer) {
        if (null != dailyBriefingVO.getSpecialEvents()) {
            String[] split = dailyBriefingVO.getSpecialEvents().split(",");
            final long count = Arrays.stream(split).count();
            ResourceBundle bundle = getBundle(request.getLocale());
            buffer.append(getText(bundle, "we.have"));
            buffer.append(count).append(BLANK_SPACE);
            if (count == 1) {
                buffer.append(getText(bundle, "special.event.in"));
            } else {
                buffer.append(getText(bundle, "special.events.in"));
            }
            buffer.append(getMonthName(getStartDate(request))).append(COMMA_WITH_SPACE);
            buffer.append(dailyBriefingVO.getSpecialEvents()).append(FULL_STOP);
        }
    }

    private void appendPercentageSentence(BigDecimal percentage, String messageType, StringBuilder buffer, ResourceBundle bundle) {
        if (null != percentage) {
            buffer.append(Math.abs(percentage.doubleValue()));
            buffer.append("% ");
            buffer.append(comparisonValue(percentage, messageType, bundle));
        }
    }

    private String comparisonValue(BigDecimal value, String messageType, ResourceBundle bundle) {
        if (messageType.equals(AHEAD_BEHIND)) {
            return (value.compareTo(BigDecimal.ZERO) > 0) ? getText(bundle, "ahead") : getText(bundle, "behind");
        } else if (messageType.equals(ABOVE_BELOW)) {
            return (value.compareTo(BigDecimal.ZERO) > 0) ? getText(bundle, "above") : getText(bundle, "below");
        } else {
            return (value.compareTo(BigDecimal.ZERO) > 0) ? getText(bundle, "up") : getText(bundle, "down");
        }
    }

    private void appendTypeofSentence(String messageType, StringBuilder buffer, ResourceBundle bundle) {
        if (messageType.equals(STLY)) {
            buffer.append(getText(bundle, "of.the.same.time.last.year"));
        } else if (messageType.equals(LAST_YEAR)) {
            buffer.append(getText(bundle, "from.last.Year"));
        } else {
            buffer.append(getText(bundle, "budget"));
        }
    }

    private String getMonthName(LocalDate startDate) {
        return startDate.format(DateTimeFormatter.ofPattern("MMMM"));
    }
}
