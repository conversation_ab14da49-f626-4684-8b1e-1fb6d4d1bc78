package com.ideas.tetris.pacman.services.mktsegrecoding.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.activity.service.RoomTypeMarketSegmentActivityService;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductRateCode;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesUtils;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.filemetadata.FileMetadataService;
import com.ideas.tetris.pacman.services.independentproducts.repository.IndependentProductsRepository;
import com.ideas.tetris.pacman.services.independentproducts.service.IndependentProductsService;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertService;
import com.ideas.tetris.pacman.services.informationmanager.dto.Alert;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrInstanceEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrTypeEntity;
import com.ideas.tetris.pacman.services.marketsegment.component.MarketSegmentComponent;
import com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentMaster;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.marketsegment.entity.YieldCategoryRule;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegment;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentService;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentSummary;
import com.ideas.tetris.pacman.services.marketsegment.service.MarketSegmentService;
import com.ideas.tetris.pacman.services.marketsegment.service.RateCodeTypeEnum;
import com.ideas.tetris.pacman.services.marketsegment.service.YieldCategoryRuleService;
import com.ideas.tetris.pacman.services.mktsegrecoding.dto.MarketCodeRateCodeCombination;
import com.ideas.tetris.pacman.services.mktsegrecoding.dto.MarketCodeRateCodeReservationVolume;
import com.ideas.tetris.pacman.services.mktsegrecoding.dto.MktSegProductDetailsDTO;
import com.ideas.tetris.pacman.services.mktsegrecoding.dto.MktSegRecodingAMSSummary;
import com.ideas.tetris.pacman.services.mktsegrecoding.dto.MktSegRecodingConfigDto;
import com.ideas.tetris.pacman.services.mktsegrecoding.dto.ProductRateCodeAmsRule;
import com.ideas.tetris.pacman.services.mktsegrecoding.dto.RateCodeAssociation;
import com.ideas.tetris.pacman.services.mktsegrecoding.entity.MktSegRecodingBusinessTypeShift;
import com.ideas.tetris.pacman.services.mktsegrecoding.entity.MktSegRecodingConfig;
import com.ideas.tetris.pacman.services.mktsegrecoding.entity.PMSMigrationTemporaryMapping;
import com.ideas.tetris.pacman.services.mktsegrecoding.entity.RateCodeMarketSegmentMapping;
import com.ideas.tetris.pacman.services.mktsegrecoding.entity.RateCodeShiftAssociation;
import com.ideas.tetris.pacman.services.mktsegrecoding.entity.RateCodeShiftAssociationForTier;
import com.ideas.tetris.pacman.services.mktsegrecoding.entity.TierMarketSegmentMapping;
import com.ideas.tetris.pacman.services.mktsegrecoding.enums.MktSegRecodingState;
import com.ideas.tetris.pacman.services.opera.DataLoadSummaryService;
import com.ideas.tetris.pacman.services.opera.OperaTransactionLoadService;
import com.ideas.tetris.pacman.services.pacebackfill.BackFillService;
import com.ideas.tetris.pacman.services.pmsmigration.alert.PMSMigrationAlertDetailsBuilder;
import com.ideas.tetris.pacman.services.pmsmigration.amsresolution.IgnoredRateCode;
import com.ideas.tetris.pacman.services.pmsmigration.amsresolution.NewAMSRule;
import com.ideas.tetris.pacman.services.pmsmigration.amsresolution.PMSRevampAMSDataService;
import com.ideas.tetris.pacman.services.pmsmigration.entity.PMSMigrationConfig;
import com.ideas.tetris.pacman.services.pmsmigration.entity.PMSMigrationMapping;
import com.ideas.tetris.pacman.services.pmsmigration.entity.PMSMigrationRateCodeMktSegMapping;
import com.ideas.tetris.pacman.services.pmsmigration.enums.PMSMigrationMappingType;
import com.ideas.tetris.pacman.services.pmsmigration.purge.PMSMigrationUpdateTempCodeTable;
import com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationMappingService;
import com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationService;
import com.ideas.tetris.pacman.services.pmsmigration.services.PMSRevampNonPaceUpdationService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyStageChangeService;
import com.ideas.tetris.pacman.services.reservationnight.ReservationNightService;
import com.ideas.tetris.pacman.services.saspopulation.service.OperaPopulationService;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.rest.mapper.HttpResponseException;
import com.ideas.tetris.platform.common.rest.mapper.RestClient;
import com.ideas.tetris.platform.common.rest.mapper.RestEndpoints;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.utils.map.MapBuilder;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.PredicateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.ws.rs.client.Entity;
import javax.ws.rs.core.MediaType;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.PMS_MIGRATION_FAILED_JOB_EXECUTION_ID;
import static com.ideas.tetris.pacman.common.constants.Constants.UNMAPPED_RATE_CODES_ALERT_METADATA;
import static com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.UPDATE_MARKET_CODE_BY_NEW_FOR_GIVEN_MARKET_CODE;
import static com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.UPDATE_MARKET_SEGMENT_ID_FOR_SHARED_RATE_CODE_BETWEEN_TIER_MS;
import static com.ideas.tetris.pacman.services.marketsegment.service.RateCodeTypeEnum.CONTAINS;
import static com.ideas.tetris.pacman.services.marketsegment.service.RateCodeTypeEnum.ENDS_WITH;
import static com.ideas.tetris.pacman.services.marketsegment.service.RateCodeTypeEnum.STARTS_WITH;
import static com.ideas.tetris.pacman.services.mktsegrecoding.enums.MktSegRecodingState.MKT_SEG_RECODING_COMMIT_FG_REQUIRED;
import static com.ideas.tetris.pacman.services.mktsegrecoding.enums.MktSegRecodingState.MKT_SEG_RECODING_MAPPINGS_VALIDATION_DONE;
import static com.ideas.tetris.pacman.services.mktsegrecoding.enums.MktSegRecodingState.MKT_SEG_RECODING_READY;
import static com.ideas.tetris.pacman.services.pmsmigration.enums.PMSMigrationMappingType.MARKET_SEGMENT;
import static com.ideas.tetris.pacman.services.pmsmigration.enums.PMSMigrationMappingType.MARKET_SEGMENT_NON_GROUP;
import static com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationService.OVERALL_THRESHOLD_PERCENT;
import static com.ideas.tetris.pacman.services.pmsmigration.services.PMSMigrationService.THRESHOLD_PERCENT;
import static com.ideas.tetris.pacman.services.revenuestreams.entity.RevenueStreamDetail.UPDATE_MARKET_SEGMENT_ID_BY_PREFERRED_MS_FOR_SHARED_RATE_CODE_BETWEEN_TIER_MS;
import static java.lang.String.format;
import static org.apache.commons.collections.CollectionUtils.isEmpty;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;

@Component
@Transactional
public class MktSegRecodingService {
    private static final Logger LOGGER = Logger.getLogger(MktSegRecodingService.class);
    public static final String MKT_SEG_RECODING_MKT_SEG_MAPPING_NOT_PROVIDED_ERROR = "mkt.seg.recoding.mapping.mkt.seg.mapping.not.provided.error";
    public static final String MKT_SEG_RECODING_NEW_AMS_ATTR_MAPPING_NOT_PROVIDED_ERROR = "mkt.seg.recoding.new.ams.attr.mapping.not.provided.error";
    public static final String MKT_SEG_RECODING_MAPPING_TEMPLATE_VALIDATION_ERROR = "mkt.seg.recoding.mapping.template.validation.error";
    public static final String MKT_SEG_RECODING_NEW_AMS_ATTR_MISSING_ERROR = "mkt.seg.recoding.new.ams.attr.missing.error";
    public static final String MKT_SEG_RECODING_CCFG_ALERT_CREATION = "mkt.seg.recoding.complete.ccfg";
    private static final String CLIENT_ID_KEY = "clientId";
    private static final String PROPERTY_ID_KEY = "propertyId";
    private static final String MKT_SEG_RECODING_STATE_KEY = "mktSegRecodingState";
    private static final String TRUE_CONDITION = "1";
    public static final String DEFAULT = "DEFAULT";
    public static final String START_DATE = "startDate";
    public static final String END_DATE = "endDate";
    public static final String PMS_MIGRATION_MAPPING_TABLE = "PMS_Migration_Mapping";
    public static final String TIER_MARKET_SEGMENT_MAPPING_TABLE = "Tier_Market_Segment_Mapping";
    public static final String NEW_TEMPORARY_CODE = "New_Temporary_Code";
    public static final String NEW_ORIGINAL_CODE = "New_Original_Code";

    @OperaPopulationService.Qualifier
	@Qualifier("operaPopulationService")
    @Autowired
    OperaPopulationService operaPopulationService;
    @Autowired
    OperaTransactionLoadService operaTransactionLoadService;
    @GlobalCrudServiceBean.Qualifier
	@Qualifier("globalCrudServiceBean")
    @Autowired
	private CrudService globalCrudService;
    @TenantCrudServiceBean.Qualifier
    @Autowired
	private CrudService tenantCrudService;
    @Autowired
	private JobServiceLocal jobService;
    @Autowired
	private DateService dateService;
    @Autowired
	private PropertyStageChangeService propertyStageChangeService;
    @Autowired
    private PMSMigrationService pmsMigrationService;
    @Autowired
	private PMSRevampAMSDataService pmsRevampAMSDataService;
    @Autowired
	private PMSRevampNonPaceUpdationService pmsRevampNonPaceUpdationService;
    @Autowired
	private RoomTypeMarketSegmentActivityService roomTypeMarketSegmentActivityService;
    @Autowired
	private FileMetadataService fileMetadataService;
    @Autowired
    private DataLoadSummaryService dataLoadSummaryService;
    @Autowired
	private MktSegRecodingBackupService mktSegRecodingBackupService;
    @Autowired
	private MarketSegmentComponent marketSegmentComponent;
    @Autowired
	private AlertService alertService;
    @Autowired
	private PMSMigrationMappingService pmsMigrationMappingService;
    @Autowired
	private PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
    @Qualifier("backFillService")
	private BackFillService backfillService;
    @Autowired
	private ReservationNightService reservationNightService;
    @Autowired
	private AnalyticalMarketSegmentService analyticalMarketSegmentService;
    @Autowired
	private YieldCategoryRuleService yieldCategoryRuleService;
    @Autowired
	private PMSMigrationAlertDetailsBuilder pmsMigrationAlertDetailsBuilder;
    @Autowired
	private MarketSegmentService marketSegmentService;
    @Autowired
	private RestClient restClient;
    @Autowired
	private IndependentProductsRepository independentProductsRepository;
    @Autowired
	private IndependentProductsService independentProductsService;
    private static final char UNDERSCORE = '_';

    private static final String existingMarketCodeRateCodeAssociationInDB =
            "with MarketCode_RateCode_ReservationNight as  " +
                    "( " +
                    "   select  " +
                    "      distinct isnull(rn.Market_Code, originalMktCodeFromID.Market_Code) as Market_Code, Rate_Code " +
                    "   from  " +
                    "      Reservation_Night rn " +
                    "       inner join " +
                    "      ( " +
                    "           SELECT " +
                    "               ana_mkt_id as Mkt_Seg_ID, ori_mkt_code as Market_Code " +
                    "           FROM " +
                    "               dbo.ufn_get_generic_analyticalmarketsegid_originalmarketsegment_mapping() " +
                    "      ) as originalMktCodeFromID " +
                    "      on rn.Mkt_Seg_ID = originalMktCodeFromID.Mkt_Seg_ID " +
                    "      and originalMktCodeFromID.market_code not in (select Market_Code from Analytical_Mkt_Seg where Rate_Code_Type = 'ALL' and Rank = 1) " +
                    "), " +
                    "MarketCode_RateCode_Analytical_Mkt_Seg as  " +
                    "( " +
                    "   select  " +
                    "      distinct Market_Code, Rate_Code " +
                    "   from  " +
                    "      Analytical_Mkt_Seg  " +
                    "   where Rate_Code_Type not in ('CONTAINS', 'ENDS_WITH', 'STARTS_WITH') and Rate_Code is not null and Rate_Code <> '' " +
                    "), " +
                    "MarketCode_RateCode_Final as " +
                    "( " +
                    "   select  " +
                    "      distinct Market_Code, Rate_Code " +
                    "   from  " +
                    "      MarketCode_RateCode_ReservationNight  " +
                    "   union  " +
                    "      select Market_Code, Rate_Code from MarketCode_RateCode_Analytical_Mkt_Seg " +
                    "), " +
                    "Expected_MarketCode_RateCode_From_Mapping as " +
                    "(" +
                    "select " +
                    "   distinct " +
                    "   rn.*, " +
                    "   isnull(mc.New_Equivalent_Code, rn.Market_Code) as New_Market_Code, " +
                    "   isNull(rc.New_Equivalent_Code, rn.Rate_Code) as New_Rate_Code, " +
                    "   mc.Is_Primary_Code_For_One_To_Many_Splits as Is_MC_Primary, " +
                    "   rc.Is_Primary_Code_For_One_To_Many_Splits as Is_RC_Primary " +
                    "from " +
                    "   MarketCode_RateCode_Final rn " +
                    "   left join " +
                    "   PMS_Migration_Mapping mc " +
                    "   on rn.Market_Code = mc.Current_Code and mc.Code_Type = 'MARKET_SEGMENT_NON_GROUP' " +
                    "   left join " +
                    "   PMS_Migration_Mapping rc " +
                    "   on (rn.Rate_Code = rc.Current_Code and rc.Code_Type = 'RATE_CODE') " +
                    "   where mc.Discontinued = 0 " +
                    ") ";


    private static final String exceptionMessage = "Found Unmapped Rate codes in hotel data. New Alert is raised for the same. Please provide more information to G3 System by resolving the alert. Unmapped Rate codes : ";


    public static List<String> getValidationErrorsList() {
        return new ArrayList<>(Arrays.asList(MKT_SEG_RECODING_MKT_SEG_MAPPING_NOT_PROVIDED_ERROR, MKT_SEG_RECODING_NEW_AMS_ATTR_MAPPING_NOT_PROVIDED_ERROR,
                MKT_SEG_RECODING_MAPPING_TEMPLATE_VALIDATION_ERROR, MKT_SEG_RECODING_NEW_AMS_ATTR_MISSING_ERROR));
    }

    public String ycbrToAMS() {
        List<Integer> ranksOfAssignmentRules = Arrays.asList(CONTAINS.getRank(), STARTS_WITH.getRank(), ENDS_WITH.getRank());
        List<String> changedMarketCodes = pmsMigrationMappingService.getChangedCurrentNonGroupMktSegCodes();
        List<String> changedRateCodes = pmsMigrationMappingService.getChangedRateCodes();
        if (changedMarketCodes.isEmpty() && changedRateCodes.isEmpty()) {
            return "No market codes or Rate Codes are changed. So skipping";
        }
        List<String> allChangedMktCodes = yieldCategoryRuleService.getMktSegCodes(changedMarketCodes, changedRateCodes);
        List<String> marketSegmentsWithAssignmentRules = analyticalMarketSegmentService.getMarketCodes(ranksOfAssignmentRules, allChangedMktCodes);
        if (marketSegmentsWithAssignmentRules.isEmpty()) {
            return "No AMS Rules currently exists with Attribute Assignment Rules. Skipping.";
        }
        int noOfMarketSegmentsAffected = analyticalMarketSegmentService.deleteNonDefaultAMSRulesWithMktSegsCodes(marketSegmentsWithAssignmentRules);
        List<YieldCategoryRule> ycbrWithAssignmentRulesForChangedAmsRules = yieldCategoryRuleService.getYCBRRules(marketSegmentsWithAssignmentRules);
        List<AnalyticalMarketSegment> amsFromYcbr = getAmsFromYCBR(ycbrWithAssignmentRulesForChangedAmsRules);

        tenantCrudService.save(amsFromYcbr);

        return "Market codes: " + marketSegmentsWithAssignmentRules + " deleted. No of rows deleted = " + noOfMarketSegmentsAffected;
    }

    private AnalyticalMarketSegmentAttribute getAttributeByMappedMarketCode(String mappedMarketCode) {
        for (AnalyticalMarketSegmentAttribute analyticalMarketSegmentAttribute : AnalyticalMarketSegmentAttribute.values()) {
            if (mappedMarketCode.endsWith(UNDERSCORE + analyticalMarketSegmentAttribute.getSuffix())) {
                return analyticalMarketSegmentAttribute;
            }
        }
        LOGGER.info("No relevant AnalyticalMarketSegmentAttribute for mappedMarketCode:=" + mappedMarketCode);
        return null;
    }

    private List<AnalyticalMarketSegment> getAmsFromYCBR(List<YieldCategoryRule> ycbrRulesForAMSWithAssignmentRules) {
        List<AnalyticalMarketSegment> amsFromYcbr = new ArrayList<>();
        ycbrRulesForAMSWithAssignmentRules.forEach(ycbr -> {
            AnalyticalMarketSegment ams = new AnalyticalMarketSegment();
            ams.setMarketCode(ycbr.getMarketCode());
            ams.setRateCode(ycbr.getRateCode());
            ams.setMappedMarketCode(ycbr.getAnalyticalMarketCode());

            ams.setAttribute(getAttributeByMappedMarketCode(ycbr.getAnalyticalMarketCode()));
            RateCodeTypeEnum rateCodeType = RateCodeTypeEnum.findByRank(ycbr.getRank());
            ams.setRank(ycbr.getRank());
            ams.setRateCodeType(rateCodeType);
            amsFromYcbr.add(ams);
        });
        return amsFromYcbr;
    }

    public String activateMktSegRecodingConfiguration() {
        MktSegRecodingConfig mktSegRecodingConfig = getMktSegRecodingConfiguration(MktSegRecodingState.MKT_SEG_RECODING_NOT_STARTED);
        if (Objects.nonNull(mktSegRecodingConfig)) {
            updateMktSegRecodingState(mktSegRecodingConfig, MktSegRecodingState.MKT_SEG_RECODING_READY);
            return "Market segment recoding configuration activated for " + mktSegRecodingConfig.getProperty().getCode();
        }
        return "No market segment recoding configuration found to activate for " + PacmanWorkContextHelper.getPropertyCode();
    }

    public void changePropertyStageToDormant(Integer propertyId) {
        propertyStageChangeService.changeStage(propertyId, Stage.DORMANT, null, "Changing stage to Dormant as part of Market Segment Recoding start step.");
    }

    public void updateMktSegRecodingState(MktSegRecodingConfig mktSegRecodingConfig, MktSegRecodingState newMktSegRecodingState) {
        LOGGER.info("Updating state of config for property : " + mktSegRecodingConfig.getProperty().getCode()
                + " from : " + mktSegRecodingConfig.getMktSegRecodingState() + " to : " + newMktSegRecodingState);
        mktSegRecodingConfig.setMktSegRecodingState(newMktSegRecodingState);
        globalCrudService.save(mktSegRecodingConfig);
    }

    public boolean isValidMktSegRecodingConfigurationAvailable() {
        final MktSegRecodingConfig mktSegRecodingConfig = getMktSegRecodingConfiguration(MktSegRecodingState.MKT_SEG_RECODING_NOT_STARTED);
        return hasCaughtupDatePassedOldSysDateOf(mktSegRecodingConfig);
    }

    public MktSegRecodingConfig getMktSegRecodingConfiguration(MktSegRecodingState state) {
        Map<String, Object> params = getMapWithClientIdAndPropertyIdParams();
        params.put(MKT_SEG_RECODING_STATE_KEY, state);
        return globalCrudService.findByNamedQuerySingleResult(MktSegRecodingConfig.BY_CLIENT_ID_PROPERTY_ID_RECODING_STATE, params);
    }

    private Map<String, Object> getMapWithClientIdAndPropertyIdParams() {
        return MapBuilder.with(CLIENT_ID_KEY, PacmanWorkContextHelper.getClientId()).and(PROPERTY_ID_KEY, PacmanWorkContextHelper.getPropertyId()).get();
    }

    public List<MktSegRecodingConfig> fetchAllMSRecodingConfigsForAllPropertiesOfClient(Integer clientId) {
        return globalCrudService.findByNamedQuery(MktSegRecodingConfig.ALL_BY_CLIENT_ID, QueryParameter.with("clientId", clientId).parameters());
    }

    public List<MktSegRecodingConfig> fetchAllMSRecodingConfigsForAllClients() {
        return globalCrudService.findByNamedQuery(MktSegRecodingConfig.ALL_BY_AUTHENTICATED_PROPERTIES);
    }

    public void saveMktSegRecodingConfig(MktSegRecodingConfigDto bean) {
        MktSegRecodingConfig mktSegRecodingConfig = new MktSegRecodingConfig();
        mktSegRecodingConfig.setClient(bean.getClient());
        mktSegRecodingConfig.setProperty(bean.getProperty());
        LocalDate lastExtractDate = new LocalDate(
                bean.getLastExtractDate().getYear(),
                bean.getLastExtractDate().getMonthValue(),
                bean.getLastExtractDate().getDayOfMonth()
        );
        mktSegRecodingConfig.setMktSegRecodingState(MktSegRecodingState.MKT_SEG_RECODING_NOT_STARTED);
        mktSegRecodingConfig.setOldSysDate(lastExtractDate.toDate());
        globalCrudService.save(mktSegRecodingConfig);
    }

    public void deleteMktSegRecodingConfig(MktSegRecodingConfigDto bean) {
        MktSegRecodingConfig mktSegRecodingConfig = globalCrudService.findByNamedQuerySingleResult(MktSegRecodingConfig.BY_CLIENT_ID_PROPERTY_ID_ACTIVE,
                QueryParameter.with("clientId", bean.getClient().getId())
                        .and("propertyId", bean.getProperty().getId()).parameters());
        globalCrudService.delete(mktSegRecodingConfig);
    }

    public boolean hasCaughtupDatePassedOldSysDateOf(MktSegRecodingConfig mktSegRecodingConfig) {
        return mktSegRecodingConfig != null && !dateService.getCaughtUpDate().before(mktSegRecodingConfig.getOldSysDate());
    }

    public Long startMktSegRecodingJob() {
        Map<String, Object> jobParams = new HashMap<>();
        jobParams.put(PROPERTY_ID_KEY, PacmanWorkContextHelper.getPropertyId());
        jobParams.put(JobParameterKey.TIMESTAMP, LocalDateTime.now());
        return this.jobService.startJob(JobName.MktSegRecodingJob, jobParams);
    }

    public Long startMktSegRecodingClientSpecificFlowJob() {
        Map<String, Object> jobParams = new HashMap<>();
        jobParams.put(PROPERTY_ID_KEY, PacmanWorkContextHelper.getPropertyId());
        jobParams.put(JobParameterKey.TIMESTAMP, LocalDateTime.now());
        return this.jobService.startJob(JobName.MktSegRecodingClientSpecificFlowJob, jobParams);
    }

    public MktSegRecodingConfig fetchInProgressRecodingConfiguration() {
        return globalCrudService.findByNamedQuerySingleResult(MktSegRecodingConfig.BY_CLIENT_ID_PROPERTY_ID_ACTIVE, getMapWithClientIdAndPropertyIdParams());
    }

    public Boolean validateRequiredMktSegRecodingConfiguration(Long jobExecutionId) {
        MktSegRecodingConfig mktSegRecodingConfig = fetchInProgressRecodingConfiguration();
        if (Objects.nonNull(mktSegRecodingConfig) && mktSegRecodingConfig.getMktSegRecodingState() == MKT_SEG_RECODING_READY) {
            Optional<String> validationError = validateInconsistentMappingsAndGetError();
            if (validationError.isPresent()) {

                /*
                  We are reusing the alert generation flow of PMS migration. The job resuming logic on alert action is dependent on that particular key.
                   Also key cannot be renamed to a generic name, for the sake of keeping it backward compatible in PMS migration.
                 */
                String description = PMS_MIGRATION_FAILED_JOB_EXECUTION_ID + ":" + jobExecutionId;
                generateAlertAndHaltProcessing(description, validationError.get(), AlertType.PMSMigrationInconsistentMappingsValidation, mktSegRecodingConfig, MktSegRecodingState.MKT_SEG_RECODING_MAPPINGS_VALIDATION_REQUIRED);
                return Boolean.FALSE;
            } else {
                updateMktSegRecodingState(mktSegRecodingConfig, MKT_SEG_RECODING_MAPPINGS_VALIDATION_DONE);
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    private void generateAlertAndHaltProcessing(String description, String details, AlertType alertType, MktSegRecodingConfig mktSegRecodingConfig, MktSegRecodingState newState) {
        WorkContextType workContext = PacmanWorkContextHelper.getWorkContext();
        InfoMgrTypeEntity alertTypeEntity = alertService.getAlertType(alertType.getName());
        if (alertTypeEntity.isEnabled()) {
            LOGGER.info("Updating recoding state to " + newState + " and creating " + alertType.getName() + " alert");
            mktSegRecodingConfig.setMktSegRecodingState(newState);
            alertService.generateAlertAndUpdateRecodingState(workContext, alertTypeEntity, description, details, alertType, mktSegRecodingConfig);
            throw new TetrisException(ErrorCode.USER_ACTION_REQUIRED_EXCEPTION, "");
        }
    }

    public void validatePostBackFillData(MktSegRecodingConfig mktSegRecodingConfig, Long jobExecutionId) {
        LOGGER.info("Threshold Percent => " + THRESHOLD_PERCENT);
        LOGGER.info("Overall Threshold Percent => " + OVERALL_THRESHOLD_PERCENT);
        if (pmsMigrationService.isActivityDeltaExceedsOverallThreshold()) {
            String details = pmsMigrationAlertDetailsBuilder.buildPostBackfillAlertDetails();
            String description = PMS_MIGRATION_FAILED_JOB_EXECUTION_ID + ":" + jobExecutionId;
            generateAlertAndHaltProcessing(description, details, AlertType.PMSMigrationPostBackFillDataValidation, mktSegRecodingConfig,
                    MktSegRecodingState.MKT_SEG_RECODING_MAPPINGS_VALIDATION_REQUIRED);
        }
        LOGGER.info("Activity Delta does not exceed Overall Threshold.");
    }

    public Optional<String> validateInconsistentMappingsAndGetError() {
        List<Supplier<Pair<Boolean, String>>> validators = Arrays.asList(
                () -> Pair.of(pmsMigrationService.getAllExistingMappings().isEmpty(), MKT_SEG_RECODING_MKT_SEG_MAPPING_NOT_PROVIDED_ERROR),
                () -> Pair.of(pmsRevampAMSDataService.getNewAMSRules().isEmpty(), MKT_SEG_RECODING_NEW_AMS_ATTR_MAPPING_NOT_PROVIDED_ERROR),
                () -> Pair.of(!pmsMigrationService.validateExtraMappingsForMSRecoding(pmsMigrationService.getAllExistingMappings()).isEmpty(), MKT_SEG_RECODING_MAPPING_TEMPLATE_VALIDATION_ERROR),
                () -> Pair.of(!isAMSAttributionPresentForAllNewMSAndRCInMSRecoding(), MKT_SEG_RECODING_NEW_AMS_ATTR_MISSING_ERROR));

        Optional<Pair<Boolean, String>> validationError = validators.stream().map(Supplier::get).filter(Pair::getLeft).findFirst();
        return validationError.map(Pair::getValue);
    }

    public Boolean isAMSAttributionPresentForAllNewMSAndRCInMSRecoding() {
        Map<String, Set<String>> mktWiseRateCodesFromUpload = pmsRevampAMSDataService.getUserUploadedAMSRules();
        Map<String, Set<String>> missingRateCodesFromSystem = calculateMissingRateCodesFromSystem(mktWiseRateCodesFromUpload);
        Map<String, Set<String>> userApprovedMissingRateCodes = pmsRevampAMSDataService.getMktSegWiseIgnoredRateCodesOfMismatchType(IgnoredRateCode.MismatchType.MISSING);
        return missingRateCodesFromSystem.equals(userApprovedMissingRateCodes);
    }

    public String setInProgressConfigIsBackedUpAs(Boolean isBackedUp) {
        MktSegRecodingConfig inProgressConfig = fetchInProgressRecodingConfiguration();
        if (inProgressConfig != null) {
            inProgressConfig.setBackupCreated(isBackedUp);
            globalCrudService.save(inProgressConfig);
            return "In progress market segment recoding configuration isBackedUp is set as " + isBackedUp;
        }
        return "No market segment recoding configuration found for " + PacmanWorkContextHelper.getPropertyCode();
    }

    public FileMetadata updateLatestFileMetadataAndGet() {
        return pmsRevampNonPaceUpdationService.updateLatestFileMetadataAndGet();
    }

    public void populateNonPaceAndLastPacePointFromMktAccomActivity(Integer fileMetadataId) {
        FileMetadata fileMetadata = fileMetadataService.getFileMetadataById(fileMetadataId);
        pmsRevampNonPaceUpdationService.populateNonPaceAndLastPacePointFromMktAccomActivity(fileMetadata);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public int updateLastPacePointFromMktAccomActivity(Integer fileMetadataId) {
        FileMetadata fileMetadata = fileMetadataService.getFileMetadataById(fileMetadataId);
        Date minOccupancyDate = DateUtil.addDaysToDate(fileMetadata.getSnapshotDt(), -fileMetadata.getPastWindowSize());
        Date maxOccupancyDate = DateUtil.addDaysToDate(fileMetadata.getSnapshotDt(), fileMetadata.getFutureWindowSize());
        return roomTypeMarketSegmentActivityService.updatePace(String.valueOf(PacmanWorkContextHelper.getPropertyId()), minOccupancyDate, maxOccupancyDate, fileMetadata);
    }

    public List<Integer> getNewMktSegIds(List<Integer> oldMktSegSnapshot) {
        final List<Integer> allMarketSegmentIds = getAllMarketSegments();
        return ListUtils.subtract(allMarketSegmentIds, oldMktSegSnapshot);
    }

    public String createBackup() {
        setInProgressConfigIsBackedUpAs(Boolean.FALSE);
        final String backupResult = mktSegRecodingBackupService.createBackup();
        setInProgressConfigIsBackedUpAs(Boolean.TRUE);
        return backupResult;
    }

    public String dropBackup() {
        final String dropResult = mktSegRecodingBackupService.dropBackup();
        setInProgressConfigIsBackedUpAs(Boolean.FALSE);
        return dropResult;
    }

    private void handleRenamedMktCodesWithNoAttributeChange() {
        Map<PMSMigrationMappingType, Map<String, String>> renamedNewOldMktCodesByCodeType = pmsMigrationMappingService.getRenamedNewOldCodesByCodeType(
                Arrays.asList(MARKET_SEGMENT, MARKET_SEGMENT_NON_GROUP));
        Map<String, String> oldNewMappedMarkteCodesToBeUpdated = new HashMap<>();
        Map<String, Integer> mappedMarketCodeAndProduct = new HashMap<>();
        filterBusinessTypeShiftMarketSegments(renamedNewOldMktCodesByCodeType);
        if (renamedNewOldMktCodesByCodeType.get(MARKET_SEGMENT) != null) {
            oldNewMappedMarkteCodesToBeUpdated.putAll(getOldNewRenamedGroupMktCodesWithNoAttributeChange(renamedNewOldMktCodesByCodeType.get(MARKET_SEGMENT)));
        }

        if (renamedNewOldMktCodesByCodeType.get(MARKET_SEGMENT_NON_GROUP) != null) {
            Map<String, String> nonSplitOldNewRateCodeMappings = pmsMigrationMappingService.getOldNewNonSplitRateCodes();

            List<NewAMSRule> allNewAMSRules = pmsRevampAMSDataService.getNewAMSRulesOf(renamedNewOldMktCodesByCodeType.get(MARKET_SEGMENT_NON_GROUP).keySet());
            List<NewAMSRule> allNewAMSRulesForIgnoredRateCodes = pmsRevampAMSDataService.getIgnoreRateCodeNewAMSRules(renamedNewOldMktCodesByCodeType.get(MARKET_SEGMENT_NON_GROUP).keySet());
            Map<String, MktSegRecodingAMSSummary> newAMSRulesSummary = getMktSegRecodingAMSSummaryByMappedMarketCodeForNewAMSRules(allNewAMSRules);
            allNewAMSRulesForIgnoredRateCodes.forEach(newAMSRule -> newAMSRulesSummary.get(getDefaultMappedMarketCode(newAMSRule.getMarketSegment())).addRateCode(newAMSRule.getRateCode()));

            List<AnalyticalMarketSegment> existingAMSRules = analyticalMarketSegmentService.getAMSRulesWithMarketCodes(new HashSet<>(renamedNewOldMktCodesByCodeType.get(MARKET_SEGMENT_NON_GROUP).values()));

            List<AnalyticalMarketSegment> existingAMSRulesWithoutBlankRateCodes = existingAMSRules.stream()
                    .filter(analyticalMarketSegment -> !"".equals(analyticalMarketSegment.getRateCode()))
                    .collect(Collectors.toList());

            if (isMSRecodingSupportedForIndependentProduct() && isIndependentProductEnabled()) {
                mappedMarketCodeAndProduct = getMarketCodeAndProductAssociation(existingAMSRulesWithoutBlankRateCodes);
            }
            Map<String, MktSegRecodingAMSSummary> existingAMSRulesSummaryByMktCode = getMktSegRecodingAMSSummaryByMappedMktCodeOfExistingAMSRules(existingAMSRulesWithoutBlankRateCodes);

            oldNewMappedMarkteCodesToBeUpdated.putAll(getOldNewRenamedMktCodesWithNoAttributeChange(renamedNewOldMktCodesByCodeType.get(MARKET_SEGMENT_NON_GROUP),
                    nonSplitOldNewRateCodeMappings, newAMSRulesSummary, existingAMSRulesSummaryByMktCode, mappedMarketCodeAndProduct));
        }
        oldNewMappedMarkteCodesToBeUpdated.forEach(this::handleRenamedMappedMarketCode);
        if (isMSRecodingSupportedForIndependentProduct() && isIndependentProductEnabled()) {
            deleteProductMarketSegmentMappingForOldMktCodes(renamedNewOldMktCodesByCodeType);
        }
    }

    private void deleteProductMarketSegmentMappingForOldMktCodes(Map<PMSMigrationMappingType, Map<String, String>> renamedNewOldMktCodesByCodeType) {
        Set<String> oldMarketCodes = getOldMarketCodes(renamedNewOldMktCodesByCodeType);
        Set<String> oldMappedMarketCodes = getMappedMarketCodeForMarketCodes(oldMarketCodes);
        independentProductsRepository.deleteMarketSegmentProductMappingFor(oldMappedMarketCodes);
    }

    private static Set<String> getOldMarketCodes(Map<PMSMigrationMappingType, Map<String, String>> renamedNewOldMktCodesByCodeType) {
        Set<String> oldMarketCodes = new HashSet<>();
        if (Objects.nonNull(renamedNewOldMktCodesByCodeType.get(MARKET_SEGMENT))) {
            Map<String, String> newOldMktCode = renamedNewOldMktCodesByCodeType.get(MARKET_SEGMENT);
            if (CollectionUtils.isNotEmpty(newOldMktCode.values())) {
                oldMarketCodes.addAll(newOldMktCode.values());
            }
        }

        if (Objects.nonNull(renamedNewOldMktCodesByCodeType.get(MARKET_SEGMENT_NON_GROUP))) {
            Map<String, String> newOldMktCode = renamedNewOldMktCodesByCodeType.get(MARKET_SEGMENT_NON_GROUP);
            if (CollectionUtils.isNotEmpty(newOldMktCode.values())) {
                oldMarketCodes.addAll(newOldMktCode.values());
            }
        }

        return oldMarketCodes;
    }

    private Set<String> getMappedMarketCodeForMarketCodes(Set<String> oldMarketCodes) {
        return analyticalMarketSegmentService.getAmsByMarketCodes(oldMarketCodes).stream()
                .map(AnalyticalMarketSegment::getMappedMarketCode)
                .collect(Collectors.toSet());
    }

    private Map<String, Integer> getMarketCodeAndProductAssociation(List<AnalyticalMarketSegment> existingAMSRules) {
        Map<String, Integer> mappedMarketCodeProductMap = new HashMap<>();
        existingAMSRules.forEach(rule -> {
            String mappedMarketCode = rule.getMappedMarketCode();
            Optional<Product> product = independentProductsService.getProductMappedToMarketSegment(mappedMarketCode);
            product.ifPresent(value -> mappedMarketCodeProductMap.put(mappedMarketCode, value.getId()));
        });
        return mappedMarketCodeProductMap;
    }

    private List<AnalyticalMarketSegment> filterTierMarketSegmentsFromAMSRules(List<AnalyticalMarketSegment> existingAMSRules) {
        List<Pair<Integer, String>> tierMarketSegments = getTierMarketSegments();
        List<String> tierMarketSegmentCodes = tierMarketSegments.stream().map(Pair::getRight).collect(Collectors.toList());
        return existingAMSRules.stream()
                .filter(amsRule -> !tierMarketSegmentCodes.contains(amsRule.getMappedMarketCode()))
                .collect(Collectors.toList());
    }

    private List<Pair<Integer, String>> getTierMarketSegments() {
        return tenantCrudService.findByNamedQuery(
                AnalyticalMarketSegment.GET_TIER_MARKET_SEGMENTS);
    }

    private void filterBusinessTypeShiftMarketSegments(Map<PMSMigrationMappingType, Map<String, String>> renamedNewOldMktCodesByCodeType) {
        List<String> mktSegsWithBusinessTypeShift = getAllMktSegCodesFromMktSegRecodingBusinessTypeShift();
        if (!renamedNewOldMktCodesByCodeType.isEmpty()) {
            mktSegsWithBusinessTypeShift.forEach(mktSeg -> {
                if (null != renamedNewOldMktCodesByCodeType.get(MARKET_SEGMENT)) {
                    renamedNewOldMktCodesByCodeType.get(MARKET_SEGMENT).remove(mktSeg);
                }
            });
        }
    }

    private Map<String, String> getOldNewRenamedMktCodesWithNoAttributeChange(Map<String, String> renamedNewOldMktCodesByCodeType, Map<String, String> nonSplitOldNewRateCodeMappings,
                                                                              Map<String, MktSegRecodingAMSSummary> newAMSRulesSummary, Map<String, MktSegRecodingAMSSummary> existingAMSRulesSummaryByMktCode,
                                                                              Map<String, Integer> mappedMarketCodeAndProduct) {
        Map<String, String> oldNewMappedMarkteCodesToBeUpdated = new HashMap<>();
        newAMSRulesSummary.forEach((newMappedMarketCode, newSummary) -> {
            if (isMSRecodingSupportedForIndependentProduct() && isIndependentProductEnabled()) {
                List<String> rateCodes = newSummary.getRateCodes();
                String oldMarketCode = renamedNewOldMktCodesByCodeType.get(newSummary.getMarketCode());
                String oldMappedMarketCode = getMappedMarketCodeForRateCodesAndOldMarketCode(existingAMSRulesSummaryByMktCode, rateCodes, oldMarketCode);
                if (null != oldMappedMarketCode && isProductSameForOldAndNewMarketCode(mappedMarketCodeAndProduct, newSummary, oldMappedMarketCode)) {
                    MktSegRecodingAMSSummary oldSummary = existingAMSRulesSummaryByMktCode.get(oldMappedMarketCode);
                    if (oldSummary != null && isOldNewSummaryEqual(oldSummary, newSummary, nonSplitOldNewRateCodeMappings, true)) {
                        oldNewMappedMarkteCodesToBeUpdated.put(oldMappedMarketCode, newMappedMarketCode);
                    }
                }
            } else {
                String oldMappedMktCode = renamedNewOldMktCodesByCodeType.get(getMarketCodeFromMappedMarketCode(newMappedMarketCode)) + getSuffixFromSplitMappedMarketCode(newMappedMarketCode);
                MktSegRecodingAMSSummary oldSummary = existingAMSRulesSummaryByMktCode.get(oldMappedMktCode);
                if (oldSummary != null && isOldNewSummaryEqual(oldSummary, newSummary, nonSplitOldNewRateCodeMappings, true)) {
                    oldNewMappedMarkteCodesToBeUpdated.put(oldMappedMktCode, newMappedMarketCode);
                }
            }
        });
        return oldNewMappedMarkteCodesToBeUpdated;
    }

    private boolean isProductSameForOldAndNewMarketCode(Map<String, Integer> mappedMarketCodeAndProduct, MktSegRecodingAMSSummary newSummary, String oldMappedMarketCode) {
        if (null != mappedMarketCodeAndProduct.get(oldMappedMarketCode))
            return mappedMarketCodeAndProduct.get(oldMappedMarketCode).equals(newSummary.getProductId());
        return false;
    }

    private String getMappedMarketCodeForRateCodesAndOldMarketCode(Map<String, MktSegRecodingAMSSummary> existingAMSRulesSummaryByMktCode, List<String> rateCodes, String marketCode) {
        for (Map.Entry<String, MktSegRecodingAMSSummary> entry : existingAMSRulesSummaryByMktCode.entrySet()) {
            MktSegRecodingAMSSummary summary = entry.getValue();
            if (summary.getMarketCode().equals(marketCode) && summary.getRateCodes().containsAll(rateCodes) && rateCodes.containsAll(summary.getRateCodes())) {
                return entry.getKey();
            }
        }
        return null;
    }

    protected Map<String, MktSegRecodingAMSSummary> getMktSegRecodingAMSSummaryByMappedMarketCodeForNewAMSRules(List<NewAMSRule> allNewAMSRules) {
        Map<String, MktSegRecodingAMSSummary> newAMSRulesSummary = new HashMap<>();
        allNewAMSRules.forEach(rule -> {
            String mappedMarketCodeForNewAMSRule = getMappedMarketCodeForNewAMSRule(rule);
            if (newAMSRulesSummary.get(mappedMarketCodeForNewAMSRule) == null) {
                MktSegRecodingAMSSummary summary = new MktSegRecodingAMSSummary();
                summary.setAttribute(rule.getAttribute());
                summary.setMappedMarketCode(mappedMarketCodeForNewAMSRule);
                summary.setMarketCode(rule.getMarketSegment());
                List<String> rateCodes = new ArrayList<>();
                if (rule.getRateCode() != null && !rule.getRateCode().equalsIgnoreCase(DEFAULT)) {
                    rateCodes.add(rule.getRateCode());
                }
                summary.setRateCodes(rateCodes);
                if (isMSRecodingSupportedForIndependentProduct() && isIndependentProductEnabled() && null != rule.getProduct())
                    summary.setProductId(rule.getProduct().getId());
                newAMSRulesSummary.put(mappedMarketCodeForNewAMSRule, summary);
            } else {
                newAMSRulesSummary.get(mappedMarketCodeForNewAMSRule).addRateCode(rule.getRateCode());
            }
        });
        return newAMSRulesSummary;
    }

    protected Map<String, MktSegRecodingAMSSummary> getMktSegRecodingAMSSummaryByMappedMktCodeOfExistingAMSRules(List<AnalyticalMarketSegment> existingAMSRules) {
        Map<String, MktSegRecodingAMSSummary> existingAMSRulesSummaryByMktCode = new HashMap<>();
        existingAMSRules.forEach(rule -> {
            if (existingAMSRulesSummaryByMktCode.get(rule.getMappedMarketCode()) == null) {
                MktSegRecodingAMSSummary summary = new MktSegRecodingAMSSummary();
                summary.setAttribute(rule.getAttribute());
                summary.setMappedMarketCode(rule.getMappedMarketCode());
                summary.setMarketCode(rule.getMarketCode());
                List<String> rateCodes = new ArrayList<>();
                if (rule.getRateCode() != null) {
                    rateCodes.add(rule.getRateCode());
                }
                summary.setRateCodes(rateCodes);
                existingAMSRulesSummaryByMktCode.put(rule.getMappedMarketCode(), summary);
            } else {
                existingAMSRulesSummaryByMktCode.get(rule.getMappedMarketCode()).addRateCode(rule.getRateCode());
            }
        });
        return existingAMSRulesSummaryByMktCode;
    }

    protected void handleRenamedMappedMarketCode(String oldMktSegCode, String newMktSegCode) {
        if (StringUtils.isBlank(oldMktSegCode) || StringUtils.isBlank(newMktSegCode)) {
            return;
        }

        final int updatedRecordCount = updateMktSeg(oldMktSegCode, newMktSegCode);
        handleMarketSegmentMaster(oldMktSegCode, newMktSegCode, updatedRecordCount == 1);
    }

    private void handleMarketSegmentMaster(String oldMktSegCode, String newMktSegCode, boolean mktSegExists) {
        if (mktSegExists) {
            updateMarketSegmentMaster(oldMktSegCode, newMktSegCode);
        } else {
            deleteMarketSegmentMaster(oldMktSegCode);
        }
    }

    private void deleteMarketSegmentMaster(String mktSegCode) {
        tenantCrudService.executeUpdateByNamedQuery(MarketSegmentMaster.DELETE_BY_CODE, QueryParameter.with("code", mktSegCode).parameters());
    }

    private void updateMarketSegmentMaster(String oldMktSegCode, String newMktSegCode) {
        tenantCrudService.executeUpdateByNamedQuery(MarketSegmentMaster.UPDATE_CODE, QueryParameter.with("oldCode", oldMktSegCode).and("newCode", newMktSegCode).parameters());
    }

    private int updateMktSeg(String oldMktSegCode, String newMktSegCode) {
        return tenantCrudService.executeUpdateByNamedQuery(MktSeg.UPDATE_MKT_SEG, QueryParameter.with("oldCode", oldMktSegCode).and("newCode", newMktSegCode).parameters());
    }

    private boolean isOldNewSummaryEqual(MktSegRecodingAMSSummary oldSummary, MktSegRecodingAMSSummary newSummary, Map<String, String> nonSplitOldNewRateCodeMappings, boolean checkAttribute) {
        if (checkAttribute && !isAttributeSameForSummaries(oldSummary, newSummary)) {
            return false;
        }
        if (oldSummary.getRateCodes().isEmpty() && newSummary.getRateCodes().isEmpty()) {
            return true;
        }
        return areAllExpectedRateCodesInNewRules(oldSummary, newSummary, nonSplitOldNewRateCodeMappings);
    }

    private boolean areAllExpectedRateCodesInNewRules(MktSegRecodingAMSSummary oldSummary, MktSegRecodingAMSSummary newSummary, Map<String, String> nonSplitOldNewRateCodeMappings) {
        Set<String> distinctNewRateCodesExpectedFromOldRateCodes = new HashSet<>();
        oldSummary.getRateCodes().forEach(oldRateCode -> {
            if (nonSplitOldNewRateCodeMappings.get(oldRateCode) != null) {
                distinctNewRateCodesExpectedFromOldRateCodes.add(nonSplitOldNewRateCodeMappings.get(oldRateCode));
            }
        });
        if (distinctNewRateCodesExpectedFromOldRateCodes.size() != newSummary.getRateCodes().size()) {
            return false;
        }
        return oldSummary.getRateCodes().stream().allMatch(oldRate -> nonSplitOldNewRateCodeMappings.get(oldRate) != null &&
                newSummary.getRateCodes().contains(nonSplitOldNewRateCodeMappings.get(oldRate)));
    }

    private boolean isAttributeSameForSummaries(MktSegRecodingAMSSummary oldSummary, MktSegRecodingAMSSummary newSummary) {
        return oldSummary.getAttribute().equals(newSummary.getAttribute());
    }

    private String getMappedMarketCodeForNewAMSRule(NewAMSRule rule) {
        if (rule.getRateCode() == null) {
            return rule.getMarketSegment();
        }
        if (rule.getRateCode().equalsIgnoreCase(DEFAULT)) {
            return getDefaultMappedMarketCode(rule.getMarketSegment());
        }
        return getMappedMarketCode(rule);
    }

    private String getMappedMarketCode(NewAMSRule rule) {
        if (isMSRecodingSupportedForIndependentProduct() && isIndependentProductEnabled() && null != rule.getProduct()) {
            return rule.getMarketSegment() + '_' + "IP" + rule.getProduct().getId() + '_' + rule.getAttribute().getSuffix();
        }
        return rule.getMarketSegment() + '_' + rule.getAttribute().getSuffix();
    }

    private String getDefaultMappedMarketCode(String marketCode) {
        return marketCode + UNDERSCORE + "DEF";
    }

    private String getMarketCodeFromMappedMarketCode(String mappedMarketCode) {
        int suffixIndex = getLastIndexOfSuffix(mappedMarketCode);
        if (suffixIndex < 0) {
            return mappedMarketCode;
        }
        return mappedMarketCode.substring(0, suffixIndex);
    }

    private String getSuffixFromSplitMappedMarketCode(String splitMappedMarketCode) {
        int suffixIndex = getLastIndexOfSuffix(splitMappedMarketCode);
        if (suffixIndex < 0) {
            return "";
        }
        return splitMappedMarketCode.substring(getLastIndexOfSuffix(splitMappedMarketCode));
    }

    private int getLastIndexOfSuffix(String splitMappedMarketCode) {
        return splitMappedMarketCode.lastIndexOf(UNDERSCORE);
    }

    private Map<String, String> getOldNewRenamedGroupMktCodesWithNoAttributeChange(Map<String, String> newOldRenamedCodeMap) {
        if (newOldRenamedCodeMap.isEmpty()) {
            return Collections.emptyMap();
        }
        List<AnalyticalMarketSegment> existingGroupAMSRules = analyticalMarketSegmentService.getAMSRulesWithMarketCodes(new HashSet<>(newOldRenamedCodeMap.values()));
        List<NewAMSRule> newAMSRules = pmsRevampAMSDataService.getNewAMSRulesOf(newOldRenamedCodeMap.keySet());

        List<AnalyticalMarketSegment> existingStraightGroupAMSRules = existingGroupAMSRules.stream().filter(ams -> ams.getMarketCode().equalsIgnoreCase(ams.getMappedMarketCode())).collect(Collectors.toList());

        Map<String, AnalyticalMarketSegmentAttribute> oldMktCodeAttributeMap = existingStraightGroupAMSRules.stream()
                .collect(Collectors.toMap(AnalyticalMarketSegment::getMarketCode, AnalyticalMarketSegment::getAttribute));
        Map<String, AnalyticalMarketSegmentAttribute> newMktCodeAttributeMap = newAMSRules.stream().collect(Collectors.toMap(NewAMSRule::getMarketSegment, NewAMSRule::getAttribute));

        return newOldRenamedCodeMap.entrySet().stream()
                .filter(entry -> {
                    if (oldMktCodeAttributeMap.get(entry.getValue()) == null) {
                        return false;
                    }
                    return oldMktCodeAttributeMap.get(entry.getValue()).equals(newMktCodeAttributeMap.get(entry.getKey()));
                })
                .collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey));
    }

    public List<Integer> assignMktSegToNewAMSRules() {
        handleRenamedMktCodesWithNoAttributeChange();
        handleRenamedRateCodesWithNoMarketCodeOrAttributeChange();
        List<NewAMSRule> rulesForRateCodeChangeInStraightToAMS = handleStraightToAMSConversions();
        handleAMSToStraightConversions();
        final List<Integer> mktSegIdsForPaceBackFill = getMktSegIdsEligibleForPaceBackFill();
        pmsRevampAMSDataService.assignOrUpdateMarketSegmentsForNewAMSRules();
        updateAsPreservedForStraightToAMSRuleInCaseOfRateCodeChange(rulesForRateCodeChangeInStraightToAMS);

        int noOfMktSegsInvalidated = invalidatePropertyIdsOfMktSegMarketForDeletion();
        LOGGER.info("Number of MktSeg records invalidated: " + noOfMktSegsInvalidated);
        return mktSegIdsForPaceBackFill;
    }

    protected void handleRenamedRateCodesWithNoMarketCodeOrAttributeChange() {
        Map<String, String> oldNewRenamedRateCodeMap = pmsMigrationMappingService.getRenamedNewRateCodeByCurrentRateCode();
        List<String> unchangedMktCodes = pmsMigrationMappingService.getUnchangedNonGroupMktCodes();

        List<NewAMSRule> newAMSRulesWithRenamedRateCodesAndUnchangedMktCodes = pmsRevampAMSDataService.getNewAMSRulesWith(oldNewRenamedRateCodeMap.values(), unchangedMktCodes);
        if (newAMSRulesWithRenamedRateCodesAndUnchangedMktCodes.isEmpty()) {
            return;
        }
        List<String> mktCodes = newAMSRulesWithRenamedRateCodesAndUnchangedMktCodes.stream().map(NewAMSRule::getMarketSegment).collect(Collectors.toList());
        List<AnalyticalMarketSegment> amsRulesWithUnchangedMktCodesAndRenamedRateCodes = analyticalMarketSegmentService.getAMSRulesWith(mktCodes, oldNewRenamedRateCodeMap.keySet());
        Map<String, Map<String, AnalyticalMarketSegmentAttribute>> newAttributesMktCodeMapByRateCode = newAMSRulesWithRenamedRateCodesAndUnchangedMktCodes.stream()
                .collect(Collectors.groupingBy(NewAMSRule::getRateCode, Collectors.toMap(NewAMSRule::getMarketSegment, NewAMSRule::getAttribute)));
        filterOnlyRenamedRateCodesAndMarkAsPreserved(oldNewRenamedRateCodeMap, amsRulesWithUnchangedMktCodesAndRenamedRateCodes, newAttributesMktCodeMapByRateCode);
        if (isMSRecodingSupportedForIndependentProduct() && isIndependentProductEnabled()) {
            handleRenamedProductRateCodeMapping(oldNewRenamedRateCodeMap);
        }
    }

    private void handleRenamedProductRateCodeMapping(Map<String, String> oldNewRenamedRateCodeMap) {
        Set<ProductRateCode> productRateCodesForOldRateCodes = new HashSet<>(getProductRateCodes(oldNewRenamedRateCodeMap.keySet()));
        Map<Product, Set<String>> rateCodesByProductForNewRateCodes = getRateCodesByProduct(new HashSet<>(oldNewRenamedRateCodeMap.values()));
        Set<ProductRateCode> productRateCodesToDelete = identifyProductRateCodesToDeleteWhichAreRenamed(productRateCodesForOldRateCodes, oldNewRenamedRateCodeMap, rateCodesByProductForNewRateCodes);
        Set<ProductRateCode> productRateCodesToUpdate = productRateCodesForOldRateCodes.stream()
                .filter(productRateCode -> !productRateCodesToDelete.contains(productRateCode))
                .collect(Collectors.toSet());
        deleteProductRateCodeMappings(new ArrayList<>(productRateCodesToDelete));
        updateProductRateCodeMappings(productRateCodesToUpdate, oldNewRenamedRateCodeMap);
    }

    private void updateProductRateCodeMappings(Set<ProductRateCode> productRateCodes,
                                               Map<String, String> oldNewRenamedRateCodeMap) {
        productRateCodes.forEach(productRateCode -> productRateCode.setRateCode(oldNewRenamedRateCodeMap.get(productRateCode.getRateCode())));
        tenantCrudService.save(productRateCodes);
    }

    private Set<ProductRateCode> identifyProductRateCodesToDeleteWhichAreRenamed(Set<ProductRateCode> productRateCodesForOldRateCodes,
                                                                                 Map<String, String> oldNewRenamedRateCodeMap,
                                                                                 Map<Product, Set<String>> rateCodesByProductForNewRateCodes) {
        return productRateCodesForOldRateCodes.stream()
                .filter(productRateCode -> {
                    Set<String> existingRateCodes = rateCodesByProductForNewRateCodes.get(productRateCode.getProduct());
                    String newRateCode = oldNewRenamedRateCodeMap.get(productRateCode.getRateCode());
                    return Objects.nonNull(existingRateCodes) && existingRateCodes.contains(newRateCode);
                })
                .collect(Collectors.toSet());
    }

    private List<ProductRateCode> getProductRateCodes(Set<String> rateCodes) {
        return independentProductsRepository.getProductRateCodeMappingsForGivenRateCodes(rateCodes);
    }

    private Map<Product, Set<String>> getRateCodesByProduct(Set<String> rateCodes) {
        List<ProductRateCode> productRateCodeMappings = getProductRateCodes(rateCodes);
        return productRateCodeMappings.stream()
                .collect(Collectors.groupingBy(ProductRateCode::getProduct, Collectors.mapping(ProductRateCode::getRateCode, Collectors.toSet())));
    }

    private void deleteProductRateCodeMappings(List<ProductRateCode> productRateCodes) {
        Map<Product, Set<String>> productRateCodesToBeDeleted = productRateCodes.stream()
                .collect(Collectors.groupingBy(ProductRateCode::getProduct, Collectors.mapping(ProductRateCode::getRateCode, Collectors.toSet())));
        productRateCodesToBeDeleted.forEach((product, rateCodes) -> independentProductsService.transferRateCodes(product, null, rateCodes));
    }

    private void filterOnlyRenamedRateCodesAndMarkAsPreserved(Map<String, String> oldNewRenamedRateCodeMap,
                                                              List<AnalyticalMarketSegment> amsRulesWithUnchangedMktCodesAndRenamedRateCodes,
                                                              Map<String, Map<String, AnalyticalMarketSegmentAttribute>> newAttributesMktCodeMapByRateCode) {
        amsRulesWithUnchangedMktCodesAndRenamedRateCodes.stream()
                .filter(amsRule -> amsRule.getAttribute().equals(newAttributesMktCodeMapByRateCode.getOrDefault(oldNewRenamedRateCodeMap.get(amsRule.getRateCode()), new HashMap<>()).get(amsRule.getMarketCode())))
                .forEach(amsRule -> tenantCrudService.executeUpdateByNamedQuery(AnalyticalMarketSegment.UPDATE_PRESERVED_BY_MARKET_CODE_RATE_CODE,
                        QueryParameter.with("marketCode", amsRule.getMarketCode())
                                .and("rateCode", amsRule.getRateCode())
                                .and("preserved", true)
                                .parameters()));
    }

    private void updateAsPreservedForStraightToAMSRuleInCaseOfRateCodeChange(List<NewAMSRule> rulesForRateCodeChangeInStraightToAMS) {
        rulesForRateCodeChangeInStraightToAMS.forEach(rule -> tenantCrudService.executeUpdateByNamedQuery(AnalyticalMarketSegment.UPDATE_PRESERVED_BY_MARKET_CODE_RATE_CODE,
                QueryParameter.with("marketCode", rule.getMarketSegment())
                        .and("rateCode", rule.getRateCode())
                        .and("preserved", rule.getPreserved())
                        .parameters()));
    }

    protected List<Integer> getMktSegIdsEligibleForPaceBackFill() {
        final List<NewAMSRule> newAMSRules = pmsRevampAMSDataService.getNewAMSRulesByMktCodeExistsInAMS();

        final Map<String, MktSegRecodingAMSSummary> oldAMSRulesSummaryByMktCode = getExistingAMSRuleSummaryByMktCode(newAMSRules);

        final Map<String, MktSegRecodingAMSSummary> newAMSRulesSummaryByMktCode = getNewAMSRuleSummaryByMarketCode(newAMSRules);

        final Set<String> mktSegCodesEligibleForPaceBackFill = getMktSegCodesEligibleForPaceBackFill(oldAMSRulesSummaryByMktCode, newAMSRulesSummaryByMktCode);

        mktSegCodesEligibleForPaceBackFill.addAll(getAllMktSegCodesFromMktSegRecodingBusinessTypeShift());

        mktSegCodesEligibleForPaceBackFill.addAll(getMktSegCodesRenamedWithNoAttributeChangeButInvolvedInSplit());

        return getMktSegIds(mktSegCodesEligibleForPaceBackFill);
    }

    private List<String> getAllMktSegCodesFromMktSegRecodingBusinessTypeShift() {
        if (isBusinessTypeShiftAssociationNotExists()) {
            return Collections.emptyList();
        }
        return tenantCrudService.findByNamedQuery(MktSegRecodingBusinessTypeShift.GET_ALL_MKT_SEG_CODES);
    }

    private List<String> getMktSegCodesRenamedWithNoAttributeChangeButInvolvedInSplit() {
        List<String> mktCodeExistsInAnalyticalMktSegRenameSplit = tenantCrudService.findByNativeQuery(NewAMSRule.MKT_CODE_EXISTS_IN_ANALYTICAL_MKT_SEG_RENAME_SPLIT, Collections.emptyMap());
        if (CollectionUtils.isEmpty(mktCodeExistsInAnalyticalMktSegRenameSplit)) {
            return Collections.emptyList();
        }
        return mktCodeExistsInAnalyticalMktSegRenameSplit;
    }

    private Map<String, MktSegRecodingAMSSummary> getNewAMSRuleSummaryByMarketCode(List<NewAMSRule> newAMSRules) {
        final Set<String> marketCodes = newAMSRules.stream()
                .map(NewAMSRule::getMarketSegment)
                .collect(Collectors.toSet());

        List<NewAMSRule> allNewAMSRulesForIgnoredRateCodes = pmsRevampAMSDataService.getIgnoreRateCodeNewAMSRules(marketCodes);

        Map<String, MktSegRecodingAMSSummary> newAMSRulesSummary = getMktSegRecodingAMSSummaryByMappedMarketCodeForNewAMSRules(newAMSRules);

        allNewAMSRulesForIgnoredRateCodes.forEach(newAMSRule -> newAMSRulesSummary.get(getDefaultMappedMarketCode(newAMSRule.getMarketSegment())).addRateCode(newAMSRule.getRateCode()));

        return newAMSRulesSummary;
    }

    private Set<String> getMktSegCodesEligibleForPaceBackFill(Map<String, MktSegRecodingAMSSummary> oldAMSRulesSummaryByMktCode, Map<String, MktSegRecodingAMSSummary> newAMSRulesSummaryByMktCode) {
        Set<String> mktSegCodes = new HashSet<>();

        final Set<String> mappedMarketCodesCommonInOldAndNew = new HashSet<>(newAMSRulesSummaryByMktCode.keySet());
        mappedMarketCodesCommonInOldAndNew.retainAll(oldAMSRulesSummaryByMktCode.keySet());

        final Set<String> oldMappedMarketCodesNotInNew = new HashSet<>(oldAMSRulesSummaryByMktCode.keySet());
        oldMappedMarketCodesNotInNew.removeAll(newAMSRulesSummaryByMktCode.keySet());

        final Set<String> newMappedMarketCodesNotInOld = new HashSet<>(newAMSRulesSummaryByMktCode.keySet());
        newMappedMarketCodesNotInOld.removeAll(oldAMSRulesSummaryByMktCode.keySet());

        identifyMktSegCodesForPaceBackFillWithNoChangeInAttribution(oldAMSRulesSummaryByMktCode, newAMSRulesSummaryByMktCode, mktSegCodes, mappedMarketCodesCommonInOldAndNew);

        identifyMktSegCodesForPaceBackFillWithChangeInAttribution(oldAMSRulesSummaryByMktCode, newAMSRulesSummaryByMktCode, mktSegCodes, oldMappedMarketCodesNotInNew, newMappedMarketCodesNotInOld);

        return mktSegCodes;
    }

    /**
     * Identify whether the market segment is eligible of pace back fill in case only the attribution is changed while specifying new AMS rule.
     * This will check if the rate codes added partially to the new AMS from the existing AMS and select that market segment for pace back fill.
     */
    private void identifyMktSegCodesForPaceBackFillWithChangeInAttribution(Map<String, MktSegRecodingAMSSummary> oldAMSRulesSummaryByMktCode, Map<String, MktSegRecodingAMSSummary> newAMSRulesSummaryByMktCode, Set<String> mktSegCodes, Set<String> oldMappedMarketCodesNotInNew, Set<String> newMappedMarketCodesNotInOld) {
        if (isEmpty(oldMappedMarketCodesNotInNew) || isEmpty(newMappedMarketCodesNotInOld)) {
            return;
        }

        newMappedMarketCodesNotInOld.forEach(newMappedMarketCode -> {
            final MktSegRecodingAMSSummary newAMSSummary = newAMSRulesSummaryByMktCode.get(newMappedMarketCode);
            if (isNotEmpty(newAMSSummary.getRateCodes())) {
                oldAMSRulesSummaryByMktCode.forEach((oldMappedMarketCode, oldAMSSummary) -> {
                    if (isNotEmpty(oldAMSSummary.getRateCodes())
                            && newAMSSummary.getRateCodes().containsAll(oldAMSSummary.getRateCodes())
                            && newAMSSummary.getRateCodes().size() != oldAMSSummary.getRateCodes().size()) {
                        mktSegCodes.add(newMappedMarketCode);
                    }
                });
            }
        });
    }

    /**
     * Identify whether the market segment is eligible of pace back fill in case the attribution not changed while specifying new AMS rule.
     * This will check if the rate code added or removed partially to/from the existing AMS and select that market segment for pace back fill.
     * This will also check if the existing straight AMS is merged to another existing straight AMS and select that market segment for pace back fill.
     */
    private void identifyMktSegCodesForPaceBackFillWithNoChangeInAttribution(Map<String, MktSegRecodingAMSSummary> oldAMSRulesSummaryByMktCode, Map<String, MktSegRecodingAMSSummary> newAMSRulesSummaryByMktCode, Set<String> mktSegCodes, Set<String> mappedMarketCodesCommonInOldAndNew) {
        if (isEmpty(mappedMarketCodesCommonInOldAndNew)) {
            return;
        }

        Map<String, String> nonSplitOldNewRateCodeMappings = pmsMigrationMappingService.getOldNewNonSplitRateCodes();
        mappedMarketCodesCommonInOldAndNew.forEach(mappedMarketCode -> {
            if (!isOldNewSummaryEqual(oldAMSRulesSummaryByMktCode.get(mappedMarketCode), newAMSRulesSummaryByMktCode.get(mappedMarketCode), nonSplitOldNewRateCodeMappings, false)) {
                mktSegCodes.add(mappedMarketCode);
            } else if (isStraightToStraightAMSMerge(newAMSRulesSummaryByMktCode.get(mappedMarketCode))) {
                mktSegCodes.add(mappedMarketCode);
            }
        });
    }

    private boolean isStraightToStraightAMSMerge(MktSegRecodingAMSSummary mktSegRecodingNewAMSSummary) {
        if (Objects.equals(mktSegRecodingNewAMSSummary.getMarketCode(), mktSegRecodingNewAMSSummary.getMappedMarketCode())) {
            final Long validMappingCountForNewEquivalentMktCode = tenantCrudService.findByNamedQuerySingleResult(
                    PMSMigrationMapping.GET_COUNT_BY_NEW_EQUIVALENT_CODE_AND_TYPES,
                    QueryParameter.with("newEquivalentCode", mktSegRecodingNewAMSSummary.getMarketCode())
                            .and("codeTypes", Arrays.asList(MARKET_SEGMENT, MARKET_SEGMENT_NON_GROUP)).parameters()
            );

            return validMappingCountForNewEquivalentMktCode > 1;
        }

        return false;
    }

    private List<Integer> getMktSegIds(Set<String> mktSegCodes) {
        if (isNotEmpty(mktSegCodes)) {
            return tenantCrudService.findByNamedQuery(MktSeg.GET_IDS_BY_MKT_SEG_CODES, QueryParameter.with("mktSegCodes", mktSegCodes).parameters());
        }
        return Collections.emptyList();
    }

    protected Map<String, MktSegRecodingAMSSummary> getExistingAMSRuleSummaryByMktCode(List<NewAMSRule> newAMSRules) {
        final Set<String> marketCodes = newAMSRules.stream()
                .map(NewAMSRule::getMarketSegment)
                .collect(Collectors.toSet());
        return getMktSegRecodingAMSSummaryByMappedMktCodeOfExistingAMSRules(analyticalMarketSegmentService.getAMSRulesWithMarketCodes(marketCodes));
    }

    private List<NewAMSRule> handleStraightToAMSConversions() {
        List<NewAMSRule> straightToAmsRules = findStraightToAMSRules();
        if (isNotEmpty(straightToAmsRules)) {
            addRulesForOldMarketCodeIfMarketCodeIsChanged(straightToAmsRules);
            List<NewAMSRule> rulesAddedForRateCodeChangeStraightToAMS = addRulesForOldRateCodeIfRateCodeIsChanged(straightToAmsRules);
            deleteAnalyticalMarketSegmentsRuleFor(straightToAmsRules);
            return rulesAddedForRateCodeChangeStraightToAMS;
        }
        return Collections.EMPTY_LIST;
    }

    private List<NewAMSRule> addRulesForOldRateCodeIfRateCodeIsChanged(List<NewAMSRule> straightToAmsRules) {
        List<NewAMSRule> rulesForChangeInRateCode = straightToAmsRules.stream()
                .filter(newAMSRule -> newAMSRule.getOldRateCode() != null && !newAMSRule.getRateCode().equals(newAMSRule.getOldRateCode()))
                .peek(this::updateRuleWithOldRateCode)
                .collect(Collectors.toList());
        pmsRevampAMSDataService.insertNewAMSRules(rulesForChangeInRateCode);
        return rulesForChangeInRateCode;
    }

    private void updateRuleWithOldRateCode(NewAMSRule newAMSRule) {
        newAMSRule.setRateCode(newAMSRule.getOldRateCode());
        newAMSRule.setPreserved(true);
    }

    private void addRulesForOldMarketCodeIfMarketCodeIsChanged(List<NewAMSRule> straightToAmsRules) {
        Set<String> oldMarketCodesWhichIsChanged = straightToAmsRules.stream()
                .filter(newAMSRule -> !newAMSRule.getMarketSegment().equals(newAMSRule.getOldMarketSegment()))
                .map(NewAMSRule::getOldMarketSegment)
                .collect(Collectors.toSet());
        if (isNotEmpty(oldMarketCodesWhichIsChanged)) {
            List<NewAMSRule> rulesForOldMarketCodeWhichIsChanged = createRulesForChangedOldMarketCodes(oldMarketCodesWhichIsChanged);
            if (isNotEmpty(rulesForOldMarketCodeWhichIsChanged)) {
                addDefaultRule(oldMarketCodesWhichIsChanged, rulesForOldMarketCodeWhichIsChanged);
                pmsRevampAMSDataService.insertNewAMSRules(rulesForOldMarketCodeWhichIsChanged);
            }
        }
    }

    private void addDefaultRule(Set<String> oldMarketCodesWhichIsChanged, List<NewAMSRule> rulesForOldMarketCodeWhichIsChanged) {
        oldMarketCodesWhichIsChanged.forEach(oldMarketCode -> {
            NewAMSRule newAMSRule = new NewAMSRule();
            newAMSRule.setMarketSegment(oldMarketCode);
            newAMSRule.setRateCode("DEFAULT");
            newAMSRule.setAttribute(AnalyticalMarketSegmentAttribute.FENCED_AND_PACKAGED);//dummy attribute it will be replaced in preservation
            newAMSRule.setForecastActivityTypeId(1);//dummy attribute it will be replaced in preservation
            rulesForOldMarketCodeWhichIsChanged.add(newAMSRule);
        });
    }

    private List<NewAMSRule> createRulesForChangedOldMarketCodes(Set<String> oldMarketCodesWhichIsChanged) {
        return tenantCrudService.findByNativeQuery
                (ReservationNight.GET_MARKET_CODE_RATE_CODE_BY_MARKET_CODES,
                        QueryParameter.with("marketCodes", oldMarketCodesWhichIsChanged).parameters(), row -> {
                            NewAMSRule newAMSRule = new NewAMSRule();
                            newAMSRule.setMarketSegment((String) row[0]);
                            newAMSRule.setRateCode((String) row[1]);
                            newAMSRule.setAttribute(AnalyticalMarketSegmentAttribute.valueOf((String) row[2]));
                            newAMSRule.setForecastActivityTypeId(1);
                            return newAMSRule;
                        });
    }

    private void handleAMSToStraightConversions() {
        List<NewAMSRule> newAMSRulesOfAMSToStraightConverted = findMarketCodeOfAMSToStraightRules();
        if (newAMSRulesOfAMSToStraightConverted == null || newAMSRulesOfAMSToStraightConverted.isEmpty()) {
            return;
        }
        Set<String> oldMarketCodes = newAMSRulesOfAMSToStraightConverted.stream().map(NewAMSRule::getOldMarketSegment).collect(Collectors.toSet());
        Set<String> oldMarketCodesToDeletedFromMktSeg = newAMSRulesOfAMSToStraightConverted.stream().filter(newAMSRule -> newAMSRule.getMarketSegment().equals(newAMSRule.getOldMarketSegment()))
                .map(NewAMSRule::getOldMarketSegment).collect(Collectors.toSet());
        invalidateMarketSegmentsByMarketCode(oldMarketCodesToDeletedFromMktSeg);
        analyticalMarketSegmentService.deleteAMSRulesWithMktCodes(new ArrayList(oldMarketCodes));
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        marketSegmentService.updatePropertyCode(oldMarketCodesToDeletedFromMktSeg, propertyId);
        List<NewAMSRule> newAMSRulesForOldMktCodesThatAreRenamed = newAMSRulesOfAMSToStraightConverted.stream().filter(newAMSRule -> !newAMSRule.getMarketSegment().equals(newAMSRule.getOldMarketSegment()))
                .peek(newAMSRule -> newAMSRule.setMarketSegment(newAMSRule.getOldMarketSegment())).collect(Collectors.toList());
        pmsRevampAMSDataService.insertNewAMSRules(newAMSRulesForOldMktCodesThatAreRenamed);
    }

    private List<NewAMSRule> findStraightToAMSRules() {
        return tenantCrudService.findByNativeQuery(NewAMSRule.FIND_STRAIGHT_TO_AMS_RULE,
                QueryParameter.with("codeType", Arrays.asList("MARKET_SEGMENT_NON_GROUP", "MARKET_SEGMENT")).parameters(), this::getNewAMSRuleFromRow);
    }

    private List<NewAMSRule> findMarketCodeOfAMSToStraightRules() {
        return tenantCrudService.findByNativeQuery(NewAMSRule.FIND_AMS_TO_STRAIGHT_CURRENT_MARKET_CODE,
                QueryParameter.with("codeType", "MARKET_SEGMENT_NON_GROUP").parameters(),
                this::getNewAMSRuleFromRow);
    }

    private NewAMSRule getNewAMSRuleFromRow(Object[] row) {
        NewAMSRule newAMSRule = new NewAMSRule();
        newAMSRule.setMarketSegment((String) row[1]);
        newAMSRule.setRateCode((String) row[2]);
        newAMSRule.setAttribute(AnalyticalMarketSegmentAttribute.valueOf((String) row[3]));
        newAMSRule.setForecastActivityTypeId((Integer) row[4]);
        newAMSRule.setProduct(getProductFromRow(row));
        newAMSRule.setOldMarketSegment((String) row[6]);
        if (row.length > 7) {
            newAMSRule.setOldRateCode((String) row[7]);
        }
        return newAMSRule;

    }

    private Product getProductFromRow(Object[] row) {
        if (null != row[5]) {
            return tenantCrudService.find(Product.class, (Integer) row[5]);
        }
        return null;
    }

    private void deleteAnalyticalMarketSegmentsRuleFor(List<NewAMSRule> straightToAmsRules) {
        Set<String> marketSegmentsForDeletion = straightToAmsRules.stream().map(NewAMSRule::getOldMarketSegment).collect(Collectors.toSet());
        invalidateMarketSegmentsByMarketCode(marketSegmentsForDeletion);
        analyticalMarketSegmentService.deleteAMSRulesWithMktCodes(new ArrayList(marketSegmentsForDeletion));
    }

    private void invalidateMarketSegmentsByMarketCode(Set<String> marketSegmentsForDeletion) {
        if (marketSegmentsForDeletion.isEmpty()) {
            return;
        }
        tenantCrudService.executeUpdateByNamedQuery(MktSeg.INVALIDATE_PROPERTY_ID_BY_MKT_SEG,
                QueryParameter.with("mktSegCodes", marketSegmentsForDeletion).parameters());
    }

    public String invalidateRedundantMktSeg() {
        List<String> changedMktSegCodes = pmsMigrationMappingService.getChangedCurrentMktSegCodes();
        List<String> newEquivalentMktSegCodes = pmsMigrationMappingService.getDistinctNewEquivalentMktSegCodes();
        changedMktSegCodes.removeAll(newEquivalentMktSegCodes);
        if (!changedMktSegCodes.isEmpty()) {
            tenantCrudService.executeUpdateByNamedQuery(MktSeg.INVALIDATE_PROPERTY_ID_BY_MKT_SEG,
                    QueryParameter.with("mktSegCodes", changedMktSegCodes).parameters());
        }
        return changedMktSegCodes + " MktSegs invalidated";
    }

    private int invalidatePropertyIdsOfMktSegMarketForDeletion() {
        return tenantCrudService.executeUpdateByNamedQuery(MktSeg.INVALIDATE_DISCONTINUED_MKT_SEG_PROPERTY_ID);
    }

    public List<Integer> getMarketSegmentIdsDiscontinuedByClient() {
        return tenantCrudService.findByNamedQuery(MktSeg.GET_MKT_SEG_IDS_DISCONTINUED_BY_CLIENT);
    }

    public int syncSASDataSetsWithMktAccomActivityData(Integer fileMetadataId) {
        final FileMetadata fileMetadata = fileMetadataService.getFileMetadataById(fileMetadataId);
        // Transaction table population
        int numberOfRows = operaTransactionLoadService.loadOperaAnalyticsTransData(fileMetadata);
        LOGGER.info("Loaded Reservation Data for Analytics : " + numberOfRows);
        operaPopulationService.executeWithOverrideLosOffset(fileMetadataId, Boolean.FALSE,
                pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.AMS_REBUILD_SUMMARY_FOR_ALL_DATA.getParameterName()));
        return numberOfRows;
    }

    public String validateCCFGProcessCompletion(Long jobExecutionId) {
        MktSegRecodingConfig inProgressRecodingConfig = fetchInProgressRecodingConfiguration();
        if (isConfigStateAfterValidationsDone(inProgressRecodingConfig)) {
            boolean ccfgProcessCompleted = pmsMigrationService.isCCFGProcessCompleted(jobExecutionId);
            if (!ccfgProcessCompleted) {
                String description = PMS_MIGRATION_FAILED_JOB_EXECUTION_ID + ":" + jobExecutionId;
                generateAlertAndHaltProcessing(description, MKT_SEG_RECODING_CCFG_ALERT_CREATION, AlertType.PMSMigrationCCFGValidation, inProgressRecodingConfig, MKT_SEG_RECODING_COMMIT_FG_REQUIRED);
                LOGGER.info("CCFG is required.");
                return "CCFG Required";
            } else {
                updateMktSegRecodingState(inProgressRecodingConfig, MktSegRecodingState.MKT_SEG_RECODING_COMMIT_FG_DONE);
                LOGGER.info("CCFG is Completed.");
                return "CCFG Completed";
            }
        }
        LOGGER.info("No config found, with recoding state >= Post Backfill.");
        return "No config found, with recoding state >= Post Backfill.";
    }

    public List<Integer> getPaceBackFillMarketSegmentIdsForMSRecoding() {
        return getMktSegIdsEligibleForPaceBackFill();
    }

    public boolean isConfigStateAfterValidationsDone(MktSegRecodingConfig config) {
        return Objects.nonNull(config) && !config.getMktSegRecodingState().isBefore(MKT_SEG_RECODING_MAPPINGS_VALIDATION_DONE);
    }

    public List<Integer> getAllMarketSegments() {
        return marketSegmentComponent.findAllMarketSegments().stream().map(MktSeg::getId).collect(Collectors.toList());
    }

    public MktSegRecodingConfig getMSRecodingConfigForClientAndProperty(Integer clientId, Integer propertyId) {
        return globalCrudService.findByNamedQuerySingleResult(MktSegRecodingConfig.BY_CLIENT_ID_PROPERTY_ID,
                QueryParameter.with("clientId", clientId).and("propertyId", propertyId).parameters());

    }

    public void updateMktSegRecodingStateTo(MktSegRecodingState newRecodingState) {
        final MktSegRecodingConfig mktSegRecodingConfig = fetchInProgressRecodingConfiguration();
        if (Objects.nonNull(mktSegRecodingConfig)) {
            updateMktSegRecodingState(mktSegRecodingConfig, newRecodingState);
        } else {
            LOGGER.info("No recoding configuration with required recoding status found in db.");
        }
    }

    public void executeAndSyncTenantMS(List<Integer> marketSegmentIds) {
        backfillService.updateFilenameForCorrectionBackfill();
        backfillService.executeAndSyncTenantMS(marketSegmentIds);
    }

    public List<RateCodeAssociation> findRateCodeIsShiftedFromPreviousMarketSegmentToOther(Map<String, Set<String>> mktSegWiseRateCodesFromUpload) {
        List<MarketCodeRateCodeCombination> marketCodeRateCodeCombinationsByUser =
                extractMarketCodeRateCodeCombinationByUserFromMap(mktSegWiseRateCodesFromUpload);
        marketCodeRateCodeCombinationsByUser = sortedByMarketCode(marketCodeRateCodeCombinationsByUser);
        Set<String> userUploadedRateCodes = getUserUploadedRateCodesFromMap(mktSegWiseRateCodesFromUpload);
        if (isEmpty(userUploadedRateCodes)) {
            return Collections.emptyList();
        }
        List<MarketCodeRateCodeCombination> possibleCombinationOfMktCodeRateCode =
                getPossibleCombinationOfMktCodeRateCodeForRateCodes(userUploadedRateCodes);

        List<RateCodeAssociation> rateCodeAssociations = new ArrayList<>();
        List<String> marketCodeRateCodeCombinationByUser = marketCodeRateCodeCombinationsByUser.stream()
                .map(userCombi -> userCombi.getMarketCodeFromMapping() + "="
                        + userCombi.getRateCodeFromMapping())
                .collect(Collectors.toList());
        marketCodeRateCodeCombinationsByUser.forEach(mktCodeRtCodeByUser ->
                rateCodeAssociations.addAll(getRateCodeAssociationFromPossibleCombination(
                        possibleCombinationOfMktCodeRateCode, mktCodeRtCodeByUser, marketCodeRateCodeCombinationByUser)
                )
        );
        return rateCodeAssociations;
    }

    private List<MarketCodeRateCodeCombination> sortedByMarketCode(List<MarketCodeRateCodeCombination> marketCodeRateCodeCombinations) {
        return marketCodeRateCodeCombinations.stream()
                .sorted(Comparator.comparing(MarketCodeRateCodeCombination::getMarketCodeFromMapping))
                .collect(Collectors.toList());
    }

    private Set<RateCodeAssociation> getRateCodeAssociationFromPossibleCombination(
            List<MarketCodeRateCodeCombination> possibleCombinationOfMktCodeRateCode,
            MarketCodeRateCodeCombination mktCodeRtCodeByUser,
            List<String> marketCodeRateCodeCombinationByUser) {
        return possibleCombinationOfMktCodeRateCode.stream()
                .filter(mktCodeRtCodePossibility ->
                        mktCodeRtCodePossibility.isRateFromMappingEqualsWith(mktCodeRtCodeByUser)
                                && mktCodeRtCodePossibility.isMarketCodeFromMappingNotEqualsWith(mktCodeRtCodeByUser)
                                && !marketCodeRateCodeCombinationByUser.contains(mktCodeRtCodePossibility.getMarketCodeFromMapping() + "=" +
                                mktCodeRtCodeByUser.getRateCodeFromMapping())
                )
                .map(mktCodeRtCodePossibility ->
                        new RateCodeAssociation(
                                mktCodeRtCodeByUser.getRateCodeFromMapping(),
                                mktCodeRtCodePossibility.getMarketCodeFromMapping(),
                                mktCodeRtCodeByUser.getMarketCodeFromMapping())
                )
                .collect(Collectors.toSet());
    }

    private Set<String> getUserUploadedRateCodesFromMap(Map<String, Set<String>> mktSegWiseRateCodesFromUpload) {
        return mktSegWiseRateCodesFromUpload.values()
                .stream()
                .flatMap(Set::stream)
                .collect(Collectors.toSet());
    }

    private List<MarketCodeRateCodeCombination> extractMarketCodeRateCodeCombinationByUserFromMap(Map<String, Set<String>> mktSegWiseRateCodesFromUpload) {
        return mktSegWiseRateCodesFromUpload.entrySet()
                .stream()
                .map(this::convertToList)
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    private List<MarketCodeRateCodeCombination> convertToList(Map.Entry<String, Set<String>> map) {
        String marketCode = map.getKey();
        return map.getValue()
                .stream()
                .map(rateCode -> new MarketCodeRateCodeCombination(marketCode, rateCode))
                .collect(Collectors.toList());
    }

    private List<MarketCodeRateCodeCombination> getPossibleCombinationOfMktCodeRateCodeForRateCodes(Set<String> rateCodesWithNewName) {
        List<Object[]> rows = tenantCrudService.findByNativeQuery(
                StringUtils.join(existingMarketCodeRateCodeAssociationInDB,
                        " select distinct New_Market_Code, New_Rate_Code from Expected_MarketCode_RateCode_From_Mapping " +
                                "where New_Rate_Code in (:rateCodes) and Is_MC_Primary is null"),
                QueryParameter.with("rateCodes", new ArrayList<>(rateCodesWithNewName)).parameters());
        List<MarketCodeRateCodeCombination> mcRcCombinations = new ArrayList<>();
        rows.forEach(row -> mcRcCombinations.add(new MarketCodeRateCodeCombination(
                (String) row[0],
                (String) row[1])
        ));
        return sortedByMarketCode(mcRcCombinations);
    }

    public List<MarketCodeRateCodeCombination> getOldNewMarketCodeRateCodeCombinationFromReservationAndMappings() {
        List<Object[]> rows = tenantCrudService.findByNativeQuery(
                existingMarketCodeRateCodeAssociationInDB +
                        "select * from Expected_MarketCode_RateCode_From_Mapping where (Market_Code COLLATE SQL_Latin1_General_CP1_CS_AS <> New_Market_Code COLLATE SQL_Latin1_General_CP1_CS_AS) " +
                        "or (Rate_Code COLLATE SQL_Latin1_General_CP1_CS_AS <> New_Rate_Code COLLATE SQL_Latin1_General_CP1_CS_AS) ");
        List<MarketCodeRateCodeCombination> mcRcCombinations = new ArrayList<>();
        rows.forEach(row -> mcRcCombinations.add(new MarketCodeRateCodeCombination(
                (String) row[0],
                (String) row[1],
                (String) row[2],
                (String) row[3],
                getBooleanValue(4, row),
                getBooleanValue(5, row))
        ));
        return mcRcCombinations;
    }

    private boolean getBooleanValue(int i, Object[] row) {
        if (null == row[i]) {
            return false;
        }
        return TRUE_CONDITION.equals(String.valueOf(row[i]));
    }

    public Map<String, List<String>> calculateMissingRateCodes(Map<String, Set<String>> mktSegWiseRateCodesFromUpload) {
        Map<String, Set<String>> missingRateCodes = calculateMissingRateCodesFromSystem(mktSegWiseRateCodesFromUpload);
        return missingRateCodes.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> new ArrayList(e.getValue())
                ));
    }

    private Map<String, Set<String>> calculateMissingRateCodesFromSystem(Map<String, Set<String>> mktSegWiseRateCodesFromUpload) {
        Map<String, Set<String>> missingRateCodesForNonStraightMS = calculateMissingRateCodesForNonStraightMS(mktSegWiseRateCodesFromUpload);
        Map<String, Set<String>> userUploadedMktSegRateCodeMapFilteringStraightMS = filterStraightMSFromUserUploadedAMSRule(mktSegWiseRateCodesFromUpload);
        Map<String, Set<String>> missingRateCodesForStraightMS = calculateMissingRateCodesForStraightMS(userUploadedMktSegRateCodeMapFilteringStraightMS);
        missingRateCodesForNonStraightMS.putAll(missingRateCodesForStraightMS);
        filterRateCodeShiftAssociationFromMissingRateCodes(missingRateCodesForNonStraightMS);
        return filterIfAlreadyExistInAMS(missingRateCodesForNonStraightMS);
    }

    private Map<String, Set<String>> filterIfAlreadyExistInAMS(Map<String, Set<String>> missingRateCodesForNonStraightMS) {
        Map<String, Set<String>> missingMarketCodeRateCodes = new HashMap<>();
        Map<String, Set<String>> marketCodeRateCodesFromAMS = getMarketCodeRateCodesFromAMS();
        if (!marketCodeRateCodesFromAMS.isEmpty()) {
            missingRateCodesForNonStraightMS.entrySet().forEach(missingEntry -> {
                String missingMarketCode = missingEntry.getKey();
                Set<String> missingRateCodes = missingEntry.getValue();
                Set<String> rateCodesInAMS = marketCodeRateCodesFromAMS.get(missingMarketCode);
                if (isNotEmpty(rateCodesInAMS)) {
                    missingRateCodes.removeAll(rateCodesInAMS);
                }
                missingRateCodes.removeIf(String::isEmpty);
                if (!missingRateCodes.isEmpty()) {
                    missingMarketCodeRateCodes.put(missingMarketCode, missingRateCodes);
                }
            });
            return missingMarketCodeRateCodes;
        }
        return missingRateCodesForNonStraightMS;
    }

    private Map<String, Set<String>> getMarketCodeRateCodesFromAMS() {
        List<Object[]> rows = tenantCrudService.findByNamedQuery(AnalyticalMarketSegment.GET_MARKET_CODE_RATE_CODE);
        Map<String, Set<String>> marketCodeRateCodesFromAMS = new HashMap<>();
        if (isNotEmpty(rows)) {
            rows.forEach(row -> {
                String marketCode = (String) row[0];
                Set<String> rateCodes = marketCodeRateCodesFromAMS.get(marketCode);
                if (isEmpty(rateCodes)) {
                    rateCodes = new HashSet<>();
                }
                rateCodes.add((String) row[1]);
                marketCodeRateCodesFromAMS.put(marketCode, rateCodes);
            });
        }
        return marketCodeRateCodesFromAMS;
    }

    private void filterRateCodeShiftAssociationFromMissingRateCodes(Map<String, Set<String>> missingRateCodes) {
        if (MapUtils.isNotEmpty(missingRateCodes)) {
            List<RateCodeShiftAssociation> rateCodeShiftAssociations = getRateCodeShiftAssociations();
            rateCodeShiftAssociations.forEach(association -> {
                Set<String> rateCodes = missingRateCodes.get(association.getPreviousMarketSegment());
                if (isNotEmpty(rateCodes)) {
                    rateCodes.remove(association.getRateCode());
                    missingRateCodes.put(association.getPreviousMarketSegment(), rateCodes);
                }
                if (isEmpty(rateCodes)) {
                    missingRateCodes.remove(association.getPreviousMarketSegment());
                }
            });
        }
    }

    public boolean isRateCodeShiftAssociationNotExists() {
        int tableExists = tenantCrudService.findByNativeQuerySingleResult(RateCodeShiftAssociation.TABLE_EXISTS, Collections.emptyMap());
        return tableExists == 0;
    }

    public boolean isPMSTempMappingTableNotExists() {
        int tableExists = tenantCrudService.findByNativeQuerySingleResult(PMSMigrationTemporaryMapping.TABLE_EXISTS, Collections.emptyMap());
        return tableExists == 0;
    }

    public List<RateCodeShiftAssociation> getRateCodeShiftAssociations() {
        if (isRateCodeShiftAssociationNotExists()) {
            return Collections.emptyList();
        }
        return tenantCrudService.findByNamedQuery(RateCodeShiftAssociation.ALL);
    }

    public boolean isBusinessTypeShiftAssociationNotExists() {
        int tableExists = tenantCrudService.findByNativeQuerySingleResult(MktSegRecodingBusinessTypeShift.TABLE_EXISTS, Collections.emptyMap());
        return tableExists == 0;
    }

    public List<MktSegRecodingBusinessTypeShift> getBusinessTypeShiftAssociationsForGroupToTransient() {
        if (isBusinessTypeShiftAssociationNotExists()) {
            return Collections.emptyList();
        }
        return tenantCrudService.findByNamedQuery(MktSegRecodingBusinessTypeShift.BY_TYPE,
                QueryParameter.with("businessType", Constants.BUSINESS_TYPE.GROUP.getCode()).parameters());
    }

    protected Map<String, Set<String>> filterStraightMSFromUserUploadedAMSRule(Map<String, Set<String>> mktSegWiseRateCodesFromUpload) {
        return mktSegWiseRateCodesFromUpload.entrySet()
                .stream()
                .filter(this::isNotEmptyByRemovingNull)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    private boolean isNotEmptyByRemovingNull(Map.Entry<String, Set<String>> map) {
        Set<String> rateCodes = new HashSet<>(map.getValue());
        CollectionUtils.filter(rateCodes, PredicateUtils.notNullPredicate());
        return isNotEmpty(rateCodes);
    }

    private Map<String, Set<String>> calculateMissingRateCodesForStraightMS(Map<String, Set<String>> mktSegWiseRateCodesFromUpload) {
        List<MarketCodeRateCodeCombination> possibleMktCodeRateCodeCombinationForStraightMS =
                getExpectedMarketCodeRateCodeCombinationFromReservationAndMappings();
        Set<String> originalMarketCodesForSplits = possibleMktCodeRateCodeCombinationForStraightMS.stream()
                .filter(MarketCodeRateCodeCombination::isMarketCodePrimary)
                .map(MarketCodeRateCodeCombination::getMarketCodeFromReservation)
                .collect(Collectors.toSet());
        Map<String, Set<String>> missingRateCodesForSplits =
                calculateMissingRateCodesForSplits(mktSegWiseRateCodesFromUpload, possibleMktCodeRateCodeCombinationForStraightMS, originalMarketCodesForSplits);
        Map<String, Set<String>> missingRateCodesForNonSplits =
                calculateMissingRateCodesForNonSplits(mktSegWiseRateCodesFromUpload, possibleMktCodeRateCodeCombinationForStraightMS, originalMarketCodesForSplits);
        missingRateCodesForNonSplits.putAll(missingRateCodesForSplits);
        return missingRateCodesForNonSplits;
    }

    private Map<String, Set<String>> calculateMissingRateCodesForSplits(Map<String, Set<String>> mktSegWiseRateCodesFromUpload,
                                                                        List<MarketCodeRateCodeCombination> expectedMarketCodeRateCodeCombinationForStraightMS,
                                                                        Set<String> originalMarketCodesForSplits) {
        List<MarketCodeRateCodeCombination> mktCodeRtCodeCombiForSplits = expectedMarketCodeRateCodeCombinationForStraightMS.stream()
                .filter(mktCodeRateCode -> originalMarketCodesForSplits.contains(mktCodeRateCode.getMarketCodeFromReservation()))
                .collect(Collectors.toList());
        if (isEmpty(mktCodeRtCodeCombiForSplits)) {
            return MapUtils.EMPTY_MAP;
        }
        Map<String, Set<String>> userUploadedRateCodesForOriginalMktCode =
                getUserUploadedRateCodesForOriginalMarketCode(mktSegWiseRateCodesFromUpload);
        return mktCodeRtCodeCombiForSplits.stream()
                .filter(resAndMappDto ->
                        mktSegWiseRateCodesFromUpload.containsKey(resAndMappDto.getMarketCodeFromMapping())
                                && isRateCodeNotPresentInUserUploadedRules(userUploadedRateCodesForOriginalMktCode, resAndMappDto)
                                && resAndMappDto.isMarketCodePrimary()
                ).collect(Collectors.groupingBy(
                        MarketCodeRateCodeCombination::getMarketCodeFromMapping,
                        Collectors.mapping(MarketCodeRateCodeCombination::getRateCodeFromMapping, Collectors.toSet())
                ));
    }

    private Map<String, Set<String>> getUserUploadedRateCodesForOriginalMarketCode(Map<String, Set<String>> mktSegWiseRateCodesFromUpload) {
        List<PMSMigrationMapping> mappingConfigForSplits = tenantCrudService.findByNamedQuery(PMSMigrationMapping.GET_ONLY_SPLIT_MAPPINGS);
        Map<String, Set<String>> userUploadedRateCodesForOriginalMktCode = new HashMap<>();
        mktSegWiseRateCodesFromUpload.keySet().forEach(marketCode -> {
            String originalMarketCode = findOriginalMarketCode(mappingConfigForSplits, marketCode);
            if (!originalMarketCode.isEmpty()) {
                userUploadedRateCodesForOriginalMktCode.computeIfPresent(originalMarketCode,
                        (key, value) -> {
                            value.addAll(mktSegWiseRateCodesFromUpload.get(marketCode));
                            return value;
                        });
                userUploadedRateCodesForOriginalMktCode.computeIfAbsent(originalMarketCode,
                        key -> mktSegWiseRateCodesFromUpload.get(marketCode));
            }
        });
        return userUploadedRateCodesForOriginalMktCode;
    }

    private boolean isRateCodeNotPresentInUserUploadedRules(Map<String, Set<String>> userUploadedRateCodesForOriginalMktCode, MarketCodeRateCodeCombination resAndMappDto) {
        return userUploadedRateCodesForOriginalMktCode.containsKey(resAndMappDto.getMarketCodeFromReservation())
                && !userUploadedRateCodesForOriginalMktCode.get(resAndMappDto.getMarketCodeFromReservation()).contains(resAndMappDto.getRateCodeFromMapping());
    }

    private String findOriginalMarketCode(List<PMSMigrationMapping> mappingConfigForSplits, String marketCode) {
        Optional<String> first = mappingConfigForSplits.stream()
                .filter(mapping -> mapping.getNewEquivalentCode().equals(marketCode))
                .map(PMSMigrationMapping::getCurrentCode)
                .findFirst();
        return first.orElse(StringUtils.EMPTY);
    }

    private Map<String, Set<String>> calculateMissingRateCodesForNonSplits(Map<String, Set<String>> mktSegWiseRateCodesFromUpload, List<MarketCodeRateCodeCombination> expectedMarketCodeRateCodeCombinationForStraightMS, Set<String> originalMarketCodesForSplits) {
        List<MarketCodeRateCodeCombination> mktCodeRtCodeCombiForNonSplits = expectedMarketCodeRateCodeCombinationForStraightMS.stream()
                .filter(mktCodeRateCode -> !originalMarketCodesForSplits.contains(mktCodeRateCode.getMarketCodeFromReservation()))
                .collect(Collectors.toList());
        return mktCodeRtCodeCombiForNonSplits.stream()
                .filter(resAndMappDto -> mktSegWiseRateCodesFromUpload.containsKey(resAndMappDto.getMarketCodeFromMapping())
                        && isMktSegWiseRateCodeIsNotEmpty(mktSegWiseRateCodesFromUpload, resAndMappDto)
                        && !mktSegWiseRateCodesFromUpload.get(resAndMappDto.getMarketCodeFromMapping())
                        .contains(resAndMappDto.getRateCodeFromMapping()))
                .collect(Collectors.groupingBy(
                        MarketCodeRateCodeCombination::getMarketCodeFromMapping,
                        Collectors.mapping(MarketCodeRateCodeCombination::getRateCodeFromMapping, Collectors.toSet())
                ));
    }

    private boolean isMktSegWiseRateCodeIsNotEmpty(Map<String, Set<String>> mktSegWiseRateCodesFromUpload, MarketCodeRateCodeCombination resAndMappDto) {
        mktSegWiseRateCodesFromUpload.get(resAndMappDto.getMarketCodeFromMapping()).removeAll(Collections.singletonList(null));
        return isNotEmpty(mktSegWiseRateCodesFromUpload.get(resAndMappDto.getMarketCodeFromMapping()));
    }

    private List<MarketCodeRateCodeCombination> getExpectedMarketCodeRateCodeCombinationFromReservationAndMappings() {
        List<Object[]> rows = tenantCrudService.findByNativeQuery(
                "with MarketCode_RateCode_From_System as   " +
                        "(  " +
                        "   select   " +
                        "      distinct isnull(rn.Market_Code, originalMktCodeFromID.Market_Code) as Market_Code, rn.Rate_Code " +
                        "   from   " +
                        "      Reservation_Night rn  " +
                        "       inner join  " +
                        "      (  " +
                        "           SELECT  " +
                        "               ana_mkt_id as Mkt_Seg_ID, ori_mkt_code as Market_Code  " +
                        "           FROM  " +
                        "               dbo.ufn_get_generic_analyticalmarketsegid_originalmarketsegment_mapping()  " +
                        "      ) as originalMktCodeFromID  " +
                        "      on rn.Mkt_Seg_ID = originalMktCodeFromID.Mkt_Seg_ID  " +
                        "     and originalMktCodeFromID.market_code in (select Market_Code from Analytical_Mkt_Seg where Rate_Code_Type = 'ALL' and Rank = 1)  " +
                        "), " +
                        "Expected_MarketCode_RateCode_From_Mapping as  " +
                        "( " +
                        "select  " +
                        "   distinct  " +
                        "   rn.Market_Code as Market_Code, " +
                        "   rn.Rate_Code as Rate_Code, " +
                        "   isnull(mc.New_Equivalent_Code, rn.Market_Code) as New_Market_Code,  " +
                        "   isNull(rc.New_Equivalent_Code, rn.Rate_Code) as New_Rate_Code,  " +
                        "   mc.Is_Primary_Code_For_One_To_Many_Splits as Is_MC_Primary,  " +
                        "   rc.Is_Primary_Code_For_One_To_Many_Splits as Is_RC_Primary  " +
                        "from  " +
                        "   MarketCode_RateCode_From_System rn  " +
                        "   left join  " +
                        "   PMS_Migration_Mapping mc  " +
                        "   on rn.Market_Code = mc.Current_Code and mc.Code_Type in ('MARKET_SEGMENT_NON_GROUP', 'MARKET_SEGMENT') " +
                        "   left join  " +
                        "   PMS_Migration_Mapping rc  " +
                        "   on (rn.Rate_Code = rc.Current_Code and rc.Code_Type = 'RATE_CODE')  " +
                        "   where mc.Discontinued = 0  " +
                        ")  " +
                        "select distinct * from Expected_MarketCode_RateCode_From_Mapping ");
        List<MarketCodeRateCodeCombination> mcRcCombinations = new ArrayList<>();
        rows.forEach(row -> mcRcCombinations.add(new MarketCodeRateCodeCombination(
                (String) row[0],
                (String) row[1],
                (String) row[2],
                (String) row[3],
                getBooleanValue(4, row),
                getBooleanValue(5, row))
        ));
        return mcRcCombinations;
    }

    private Map<String, Set<String>> calculateMissingRateCodesForNonStraightMS(Map<String, Set<String>> mktSegWiseRateCodesFromUpload) {
        Set<String> totalRateCodesUsedInAmsRule = new HashSet<>();
        mktSegWiseRateCodesFromUpload.values().forEach(totalRateCodesUsedInAmsRule::addAll);

        List<MarketCodeRateCodeCombination> expectedMarketCodeRateCodeCombinationFromReservationAndMappings = getOldNewMarketCodeRateCodeCombinationFromReservationAndMappings();
        Set<String> marketCodeWithPrimary = expectedMarketCodeRateCodeCombinationFromReservationAndMappings.stream()
                .filter(MarketCodeRateCodeCombination::isMarketCodePrimary)
                .map(MarketCodeRateCodeCombination::getMarketCodeFromReservation)
                .collect(Collectors.toSet());
        List<PMSMigrationMapping> splittMappings = tenantCrudService.findByNamedQuery(PMSMigrationMapping.GET_ONLY_SPLIT_MAPPINGS);
        return expectedMarketCodeRateCodeCombinationFromReservationAndMappings
                .stream()
                .filter(resAndMappDto -> filterForMissingRateCodes(totalRateCodesUsedInAmsRule, marketCodeWithPrimary, resAndMappDto, mktSegWiseRateCodesFromUpload, splittMappings))
                .collect(Collectors.groupingBy(
                        MarketCodeRateCodeCombination::getMarketCodeFromMapping,
                        Collectors.mapping(MarketCodeRateCodeCombination::getRateCodeFromMapping, Collectors.toSet())
                ));
    }

    private boolean filterForMissingRateCodes(Set<String> totalRateCodesUsedInAmsRule, Set<String> marketCodeWithPrimary,
                                              MarketCodeRateCodeCombination resAndMappDto,
                                              Map<String, Set<String>> mktSegWiseRateCodesFromUpload, List<PMSMigrationMapping> mappingForSplits) {
        //Skip Straight MS Check
        if (mktSegWiseRateCodesFromUpload.containsKey(resAndMappDto.getMarketCodeFromMapping()) &&
                (mktSegWiseRateCodesFromUpload.get(resAndMappDto.getMarketCodeFromMapping()) == null
                        || mktSegWiseRateCodesFromUpload.get(resAndMappDto.getMarketCodeFromMapping()).contains(null)
                        || mktSegWiseRateCodesFromUpload.get(resAndMappDto.getMarketCodeFromMapping()).isEmpty())) {
            return false;
        }
        //New Rate Code present in AMS rule but missing in Rate Codes of any other Market Code
        if (totalRateCodesUsedInAmsRule.contains(resAndMappDto.getRateCodeFromMapping())) {
            Set<String> rateCodesGivenInUpload = mktSegWiseRateCodesFromUpload.get(resAndMappDto.getMarketCodeFromMapping());
            List<String> marketCodesInUploadForRateCode = mktSegWiseRateCodesFromUpload.entrySet().stream()
                    .filter(entry -> entry.getValue().contains(resAndMappDto.getRateCodeFromMapping()))
                    .map(entry -> entry.getKey()).collect(Collectors.toList());
            boolean isRateCodeExistInSameSplit = marketCodesInUploadForRateCode.stream().anyMatch(mktCode ->
                    mappingForSplits.stream()
                            .filter(map -> map.getNewEquivalentCode().equals(mktCode))
                            .map(map -> map.getCurrentCode())
                            .anyMatch(currentCode -> resAndMappDto.getMarketCodeFromReservation().equals(currentCode))
            );
            return isEmpty(rateCodesGivenInUpload) || (!rateCodesGivenInUpload.contains(resAndMappDto.getRateCodeFromMapping()) && !isRateCodeExistInSameSplit);
        }
        //Rate Code not present in AMS Rule and Market COde is primary. So this will be shown as missing for primary
        return !totalRateCodesUsedInAmsRule.contains(resAndMappDto.getRateCodeFromMapping())
                && marketCodePrimaryCheck(marketCodeWithPrimary, resAndMappDto);
    }

    private boolean marketCodePrimaryCheck(Set<String> marketCodeWithPrimary, MarketCodeRateCodeCombination resAndMappDto) {
        if (marketCodeWithPrimary.contains(resAndMappDto.getMarketCodeFromReservation())) {
            return resAndMappDto.isMarketCodePrimary();
        }
        return true;
    }

    public void saveRateCodeShiftAssociations(List<RateCodeAssociation> rateCodeAssociations) {
        List<RateCodeShiftAssociation> rateCodeShiftAssociations = rateCodeAssociations.stream()
                .map(this::createRateCodeShiftAssociationEntity)
                .collect(Collectors.toList());
        tenantCrudService.executeUpdateByNativeQuery(RateCodeShiftAssociation.CREATE_RATE_CODE_SHIFT_TABLE_DDL);
        removeRateCodeShiftAssociations();
        pmsRevampAMSDataService.executeBatchInsert(new ArrayList<>(rateCodeShiftAssociations),
                obj -> ((RateCodeShiftAssociation) obj).toInsertQueryFormat(), RateCodeShiftAssociation.INSERT_RATE_CODE_SHIFT_RULES_QUERY_PREFIX);
    }

    public void removeRateCodeShiftAssociations() {
        tenantCrudService.executeUpdateByNativeQuery(RateCodeShiftAssociation.CLEAN_RATE_CODE_SHIFT_TABLE_QUERY);
    }

    public void removeIgnoredRateCodesAssociations() {
        tenantCrudService.executeUpdateByNativeQuery(IgnoredRateCode.CLEAN_IGNORED_RATE_CODE_TABLE_QUERY);
    }

    public void saveBusinessTypeShiftAssociations(List<MktSegRecodingBusinessTypeShift> businessTypeShiftAssociations) {
        tenantCrudService.executeUpdateByNativeQuery(MktSegRecodingBusinessTypeShift.CREATE_MKT_SEG_RECODING_BUSINESS_TYPE_SHIFT_TABLE_DDL);
        removeBusinessTypeShiftAssociations();
        pmsRevampAMSDataService.executeBatchInsert(new ArrayList<>(businessTypeShiftAssociations),
                obj -> ((MktSegRecodingBusinessTypeShift) obj).toInsertQueryFormat(), MktSegRecodingBusinessTypeShift.INSERT_BUSINESS_TYPE_SHIFT_RULES_QUERY_PREFIX);
    }

    public void removeBusinessTypeShiftAssociations() {
        tenantCrudService.executeUpdateByNativeQuery(MktSegRecodingBusinessTypeShift.CLEAN_BUSINESS_TYPE_SHIFT_TABLE_QUERY);
    }

    private RateCodeShiftAssociation createRateCodeShiftAssociationEntity(RateCodeAssociation rateCodeAssociation) {
        return new RateCodeShiftAssociation(rateCodeAssociation.getRateCodeName(),
                rateCodeAssociation.getPreviousAssociatedMarketSegment(),
                rateCodeAssociation.getNewAssociatedMarketSegment());
    }

    public int updateAMSForRateCodeShiftAssociation() {
        return tenantCrudService.executeUpdateByNativeQuery(RateCodeShiftAssociation.UPDATE_AMS_FOR_RATE_CODE_SHIFT_ASSOCIATION);
    }

    public List<Integer> getMarketSegmentIdsFromRateCodeShiftAssociation() {
        return tenantCrudService.findByNativeQuery(RateCodeShiftAssociation.GET_MKT_SEG_IDS_FOR_FROM_RATE_CODE_SHIFT_ASSOCIATION);
    }

    public int updateTierMarketSegmentInMktSeg() {
        List<String> tierMarketCodes = getTierMarketCodes();
        LOGGER.info("Updating Mkt_Seg for tier Market Segments : " + tierMarketCodes);
        return tenantCrudService.executeUpdateByNamedQuery(MktSeg.UPDATE_MARKET_CODE_BY_NEW_FOR_GIVEN_MARKET_CODE,
                QueryParameter.with("marketCodes", tierMarketCodes).parameters());
    }

    private List<String> getTierMarketCodes() {
        List<TierMarketSegmentMapping> allMappings = tenantCrudService.findByNamedQuery(TierMarketSegmentMapping.ALL);
        return allMappings.stream().map(TierMarketSegmentMapping::getCurrentCode).collect(Collectors.toList());
    }

    public boolean isTierMarketSegmentMappingTableExists() {
        int tableExists = tenantCrudService.findByNativeQuerySingleResult(TierMarketSegmentMapping.TABLE_EXISTS,
                Collections.emptyMap());
        return tableExists == 1;
    }

    public int assignTierMarketSegmentsToAMS() {
        List<String> tierMarketCodes = tenantCrudService.findByNamedQuery(AnalyticalMarketSegment.GET_MARKET_CODE_OF_TIER_MARKET_SEGMENTS);
        List<AnalyticalMarketSegment> tierAMS = tenantCrudService.findByNamedQuery(AnalyticalMarketSegment.BY_MARKET_CODES,
                QueryParameter.with("marketCodes", tierMarketCodes).parameters());
        List<TierMarketSegmentMapping> mappings = tenantCrudService.findByNamedQuery(TierMarketSegmentMapping.GET_MAPPING);
        List<RateCodeShiftAssociationForTier> preferredMappedMarketCodes = getRateCodeShiftAssociationForTiers();
        preferredMappedMarketCodes.forEach(prefMappedMarketCode ->
                assignPreferredConfigurations(tierAMS, prefMappedMarketCode));
        List<AnalyticalMarketSegment> amsEntitiesForSave = tierAMS.stream()
                .map(ams -> createNewAMSWith(mappings, preferredMappedMarketCodes, ams))
                .collect(Collectors.toList());
        Set<AnalyticalMarketSegment> amsEntitiesForSaveUnique = getUniqueAnalyticalMarketSegments(amsEntitiesForSave);
        LOGGER.info("Creating New Analytical Market Segments for tier Market Segments : " + tierMarketCodes);
        tenantCrudService.save(amsEntitiesForSaveUnique);
        LOGGER.info("Updating Old records of tier market segment in Analytical Market Segments as Preserved : " + tierMarketCodes);
        int updatedRows = tenantCrudService.executeUpdateByNamedQuery(AnalyticalMarketSegment.UPDATE_AS_PRESERVED_FOR_GIVEN_MARKET_CODES,
                QueryParameter.with("marketCodes", tierMarketCodes).parameters());
        LOGGER.info("Update count for making Tier Analytical Market Segments as Preserved is: " + updatedRows);
        updatePreferredMappedMarketCodeForPreserved(tierMarketCodes);
        return updatedRows;
    }

    private void assignPreferredConfigurations(List<AnalyticalMarketSegment> tierAMS, RateCodeShiftAssociationForTier prefMappedMarketCode) {
        Optional<AnalyticalMarketSegment> prefAttributeOptional = tierAMS.stream().filter(tierAms ->
                        tierAms.getMappedMarketCode().equals(prefMappedMarketCode.getPreferredMappedMarketCode()) &&
                                tierAms.getRateCode().equals(prefMappedMarketCode.getRateCode()))
                .findFirst();
        if (prefAttributeOptional.isPresent()) {
            prefMappedMarketCode.setPreferredAttribute(prefAttributeOptional.get().getAttribute());
            prefMappedMarketCode.setPreferredRateCodeType(prefAttributeOptional.get().getRateCodeType());
        }
    }

    private void updatePreferredMappedMarketCodeForPreserved(List<String> tierMarketCodes) {
        if (isRateCodeShiftAssociationForTierAvailable()) {
            LOGGER.info("Updating mapped market code with preferred for preserved tier market segments: " + tierMarketCodes);
            int updateCountForPreferred = tenantCrudService.executeUpdateByNamedQuery(AnalyticalMarketSegment.UPDATE_PREFERRED_MAPPED_MARKET_CODE_FOR_TIER_MS,
                    QueryParameter.with("tierMarketCodes", tierMarketCodes).parameters());
            LOGGER.info("Update count for preferred market segments is: " + updateCountForPreferred);
            LOGGER.info("Updating attribute with preferred for preserved tier market segments: " + tierMarketCodes);
            int updateCountForPreferredAttribute = tenantCrudService.executeUpdateByNamedQuery(AnalyticalMarketSegment.UPDATE_PREFERRED_ATTRIBUTE_FOR_TIER_MS,
                    QueryParameter.with("tierMarketCodes", tierMarketCodes).parameters());
            LOGGER.info("Update count for updating preferred attribute is: " + updateCountForPreferredAttribute);
        }
    }

    private Set<AnalyticalMarketSegment> getUniqueAnalyticalMarketSegments(List<AnalyticalMarketSegment> amsEntitiesForSave) {
        Comparator<AnalyticalMarketSegment> analyticalMarketSegmentComparator =
                Comparator.comparing(AnalyticalMarketSegment::getMarketCode)
                        .thenComparing(a -> Objects.requireNonNullElse(a.getRateCode(), StringUtils.EMPTY))
                        .thenComparing(AnalyticalMarketSegment::getMappedMarketCode);
        Set<AnalyticalMarketSegment> amsEntitiesForSaveUnique =
                new TreeSet<>(analyticalMarketSegmentComparator);
        amsEntitiesForSaveUnique.addAll(amsEntitiesForSave);
        return amsEntitiesForSaveUnique;
    }

    private AnalyticalMarketSegment createNewAMSWith(List<TierMarketSegmentMapping> mappings,
                                                     List<RateCodeShiftAssociationForTier> preferredMappedMarketCodes,
                                                     AnalyticalMarketSegment ams) {
        AnalyticalMarketSegment analyticalMarketSegment = new AnalyticalMarketSegment();
        String marketCode = getMarketCodeNewName(mappings, ams);
        Optional<RateCodeShiftAssociationForTier> preferredAmsOptional = getPreferredAms(preferredMappedMarketCodes, ams, marketCode);
        analyticalMarketSegment.setMarketCode(marketCode);
        analyticalMarketSegment.setRateCode(ams.getRateCode());
        analyticalMarketSegment.setAttribute(getPreferredAttribute(ams, preferredAmsOptional));
        analyticalMarketSegment.setMappedMarketCode(getPreferredMappedMarketCode(ams, preferredAmsOptional));
        RateCodeTypeEnum preferredRateCodeType = getPreferredRateCodeType(ams, preferredAmsOptional);
        analyticalMarketSegment.setRateCodeType(preferredRateCodeType);
        analyticalMarketSegment.setRank(preferredRateCodeType.getRank());
        analyticalMarketSegment.setComplimentary(ams.isComplimentary());
        analyticalMarketSegment.setPreserved(ams.isPreserved());
        analyticalMarketSegment.setForecastActivityType(ams.getForecastActivityType());
        return analyticalMarketSegment;
    }

    private Optional<RateCodeShiftAssociationForTier> getPreferredAms(List<RateCodeShiftAssociationForTier> preferredMappedMarketCodes, AnalyticalMarketSegment ams, String newMarketCode) {
        Optional<RateCodeShiftAssociationForTier> preferredOptional = preferredMappedMarketCodes.stream()
                .filter(preferred -> preferred.getRateCode().equals(ams.getRateCode())
                        && preferred.getMarketCode().equals(newMarketCode))
                .findFirst();
        return preferredOptional;
    }

    private String getPreferredMappedMarketCode(AnalyticalMarketSegment ams, Optional<RateCodeShiftAssociationForTier> preferredOptional) {
        if (preferredOptional.isPresent()) {
            return preferredOptional.get().getPreferredMappedMarketCode();
        }
        return ams.getMappedMarketCode();
    }

    private AnalyticalMarketSegmentAttribute getPreferredAttribute(AnalyticalMarketSegment ams, Optional<RateCodeShiftAssociationForTier> preferredOptional) {
        if (preferredOptional.isPresent()) {
            return preferredOptional.get().getPreferredAttribute();
        }
        return ams.getAttribute();
    }

    private RateCodeTypeEnum getPreferredRateCodeType(AnalyticalMarketSegment ams, Optional<RateCodeShiftAssociationForTier> preferredOptional) {
        if (preferredOptional.isPresent()) {
            return preferredOptional.get().getPreferredRateCodeType();
        }
        return ams.getRateCodeType();
    }

    private String getMarketCodeNewName(List<TierMarketSegmentMapping> mappings, AnalyticalMarketSegment ams) {
        return mappings.stream()
                .filter(mapping -> mapping.getCurrentCode().equals(ams.getMarketCode()))
                .findFirst().get().getNewEquivalentCode();
    }

    public Integer updateMarketCodeOfTierMarketSegmentsInReservation(String tableName, Date startDate, Date endDate) {
        List<String> tierMarketCodes = getTierMarketCodes();
        LOGGER.info("Updating tier market codes " + tierMarketCodes + " for table " + tableName);
        int numberOfRows = 0;
        if (isNotEmpty(tierMarketCodes)) {
            numberOfRows = tenantCrudService.executeUpdateByNativeQuery(
                    format(
                            UPDATE_MARKET_CODE_BY_NEW_FOR_GIVEN_MARKET_CODE,
                            tableName
                    ),
                    QueryParameter.with("marketCodes", tierMarketCodes)
                            .and(START_DATE, startDate)
                            .and(END_DATE, endDate).parameters()
            );
        }
        LOGGER.info("Update count of reservations for tier market segments in table " + tableName + " is: " + numberOfRows);
        return numberOfRows;
    }

    public Integer updateMarketSegmentIdOfTierMarketSegmentsInReservation(String tableName, Date startDate, Date endDate) {
        List<String> tierMarketCodes = getTierMarketCodes();
        LOGGER.info("Updating market segment id for tier market codes  " + tierMarketCodes + " for table " + tableName);
        int numberOfRows = 0;
        if (isNotEmpty(tierMarketCodes)) {
            numberOfRows = tenantCrudService.executeUpdateByNativeQuery(
                    format(
                            UPDATE_MARKET_SEGMENT_ID_FOR_SHARED_RATE_CODE_BETWEEN_TIER_MS,
                            tableName
                    ),
                    QueryParameter.with(START_DATE, startDate)
                            .and(END_DATE, endDate).parameters()
            );
        }
        LOGGER.info("Update count of reservations to update Mkt_Seg_ID for tier market segments in table " + tableName + " is: " + numberOfRows);
        return numberOfRows;
    }

    public int updateMarketSegmentIdOfTierMarketSegmentsInRevenueStreamDetails(Date startDate, Date endDate) {
        List<String> tierMarketCodes = getTierMarketCodes();
        LOGGER.info("Updating market segment id for tier market codes  " + tierMarketCodes + " for table Revenue_Stream_Detail. ");
        int numberOfRows = 0;
        if (isNotEmpty(tierMarketCodes)) {
            numberOfRows = tenantCrudService.executeUpdateByNativeQuery(
                    UPDATE_MARKET_SEGMENT_ID_BY_PREFERRED_MS_FOR_SHARED_RATE_CODE_BETWEEN_TIER_MS,
                    QueryParameter.with(START_DATE, startDate)
                            .and(END_DATE, endDate).parameters()
            );
        }
        LOGGER.info("Update count of revenue stream details to update Mkt_Seg_ID for tier market segments is: " + numberOfRows);
        return numberOfRows;
    }

    public void createTierMarketSegmentMappingTable() {
        tenantCrudService.executeUpdateByNativeQuery(TierMarketSegmentMapping.CREATE_TIER_MARKET_SEGMENT_MAPPING_TABLE_DDL);
    }

    public List<TierMarketSegmentMapping> getTierMarketSegmentMappingRecordCount() {
        final List<TierMarketSegmentMapping> result = tenantCrudService.findByNamedQuery(TierMarketSegmentMapping.ALL);
        return CollectionUtils.isNotEmpty(result) ? result : Collections.emptyList();
    }

    public boolean isRateCodeShiftAssociationForTierAvailable() {
        return isNotEmpty(getRateCodeShiftAssociationForTiers());
    }

    private List<RateCodeShiftAssociationForTier> getRateCodeShiftAssociationForTiers() {
        int tableExists = tenantCrudService.findByNativeQuerySingleResult(RateCodeShiftAssociationForTier.TABLE_EXISTS, Collections.emptyMap());
        if (1 == tableExists) {
            return tenantCrudService.findByNamedQuery(RateCodeShiftAssociationForTier.ALL);
        }
        return Collections.emptyList();
    }

    public void filterTierMarketSegmentFromNonTierMSRecodingConfiguration() {
        List<String> tierMarketCodes = getTierMarketCodes();
        filterTierMarketSegmentsFromPMSMigrationMapping(tierMarketCodes);
        filterTierMarketSegmentsFromRateCodeShift(tierMarketCodes);
        filterTierMarketSegmentsFromBusinessTypeShift(tierMarketCodes);
        filterTierMarketSegmentsFromIgnoreMissingRateCodes(tierMarketCodes);
    }

    protected void filterTierMarketSegmentsFromPMSMigrationMapping(List<String> tierMarketCodes) {
        LOGGER.info("Filtering tier market segments from PMS_Migration_Mapping : " + tierMarketCodes);
        int deletedRecordsCount = tenantCrudService.executeUpdateByNamedQuery(PMSMigrationMapping.DELETE_BY_TIER_MARKET_SEGMENTS,
                QueryParameter.with("tierMarketCodes", tierMarketCodes).parameters());
        LOGGER.info("No of records deleted from PMS_Migration_Mapping:=" + deletedRecordsCount);
    }

    private void filterTierMarketSegmentsFromIgnoreMissingRateCodes(List<String> tierMarketCodes) {
        if (ignoreMissingRateCodesExist()) {
            LOGGER.info("Filtering tier market segments from PMS_Revamp_Ignored_Rate_Code : " + tierMarketCodes);
            tenantCrudService.executeUpdateByNamedQuery(IgnoredRateCode.DELETE_BY_TIER_MARKET_SEGMENTS,
                    QueryParameter.with("tierMarketCodes", tierMarketCodes).parameters());
        }
    }

    private boolean ignoreMissingRateCodesExist() {
        int tableExists = tenantCrudService.findByNativeQuerySingleResult(IgnoredRateCode.TABLE_EXISTS, Collections.emptyMap());
        return tableExists == 1;
    }

    private void filterTierMarketSegmentsFromBusinessTypeShift(List<String> tierMarketCodes) {
        if (!isBusinessTypeShiftAssociationNotExists()) {
            LOGGER.info("Filtering tier market segments from MS_Recoding_Business_Type_Shift : " + tierMarketCodes);
            tenantCrudService.executeUpdateByNamedQuery(MktSegRecodingBusinessTypeShift.DELETE_BY_TIER_MS,
                    QueryParameter.with("tierMarketSegments", tierMarketCodes).parameters());
        }
    }

    private void filterTierMarketSegmentsFromRateCodeShift(List<String> tierMarketCodes) {
        if (!isRateCodeShiftAssociationNotExists()) {
            LOGGER.info("Filtering tier market segments from MS_Recoding_Rate_Code_Shift : " + tierMarketCodes);
            tenantCrudService.executeUpdateByNativeQuery(RateCodeShiftAssociation.DELETE_TIER_MS_FROM_RATE_CODE_SHIFT,
                    QueryParameter.with("tierMarketCodes", tierMarketCodes).parameters());
        }
    }

    public List<String> verifyAllConflictingRateCodesAreMentionedWithPreferredMappedMarketCode() {
        List<String> tierMarketCodes = getTierMarketCodes();
        List<String> sharedRateCodes = tenantCrudService.findByNativeQuery(
                RateCodeShiftAssociationForTier.GET_SHARED_RATE_CODES_BETWEEN_TIER_MARKET_SEGMENTS_HAVING_DIFFERENT_MAPPED_MARKET_CODE,
                QueryParameter.with("tierMarketCodes", tierMarketCodes).parameters());
        List<RateCodeShiftAssociationForTier> rateCodeShiftAssociationForTiers = getRateCodeShiftAssociationForTiers();
        List<String> rateCodesWithPreference = rateCodeShiftAssociationForTiers.stream()
                .map(RateCodeShiftAssociationForTier::getRateCode)
                .collect(Collectors.toList());
        sharedRateCodes.removeAll(rateCodesWithPreference);
        return sharedRateCodes;
    }

    public int getUnAttributedMarketSegments() {
        return getUnassignedGroupMarketSegmentsCount() + getUnassignedIndividualMarketSegmentsCount() + getUnassignedSharedMarketSegmentsCount();
    }

    private int getUnassignedSharedMarketSegmentsCount() {
        List<AnalyticalMarketSegmentSummary> unassignedSharedMarketSegments = analyticalMarketSegmentService.getUnassignedSharedMarketSegments();
        return CollectionUtils.isEmpty(unassignedSharedMarketSegments) ? 0 : unassignedSharedMarketSegments.size();
    }

    private int getUnassignedIndividualMarketSegmentsCount() {
        List<AnalyticalMarketSegmentSummary> unassignedIndividualMarketSegments = analyticalMarketSegmentService.getUnassignedIndividualMarketSegments();
        return CollectionUtils.isEmpty(unassignedIndividualMarketSegments) ? 0 : unassignedIndividualMarketSegments.size();
    }

    private int getUnassignedGroupMarketSegmentsCount() {
        List<AnalyticalMarketSegmentSummary> unassignedGroupMarketSegments = analyticalMarketSegmentService.getUnassignedGroupMarketSegments();
        return CollectionUtils.isEmpty(unassignedGroupMarketSegments) ? 0 : unassignedGroupMarketSegments.size();
    }


    public boolean isMktSegRecodingConfigInProgress() {
        MktSegRecodingConfig mktSegRecodingConfigInProgress = fetchInProgressRecodingConfiguration();
        return Objects.nonNull(mktSegRecodingConfigInProgress);
    }

    public boolean isInvolvedInMsRecoding(String mappedCode) {
        return pmsMigrationService.isMsRecodingEnabled() && isMappedCodeInvolvedInRecoding(mappedCode);
    }

    private boolean isMappedCodeInvolvedInRecoding(String mappedCode) {
        List<String> marketCodes = tenantCrudService.findByNamedQuery(AnalyticalMarketSegment.GET_MARKET_CODE_INVOLVED_IN_MS_RECODING);
        return isNotEmpty(marketCodes) && marketCodes.contains(mappedCode);
    }

    public List<String> findUnmappedRateCodes(Map<String, String> parameterMap) {
        LOGGER.info("Hitting NGI with params => " + parameterMap);
        String unmappedRateCodesStr = restClient.getJsonFromEndpoint(RestEndpoints.GET_UNMAPPED_RATE_CODES_V2, parameterMap, 0);
        if ("[]".equals(unmappedRateCodesStr)) {
            return Collections.EMPTY_LIST;
        }
        LOGGER.info("Response received from NGI as String : " + unmappedRateCodesStr);
        unmappedRateCodesStr = unmappedRateCodesStr.replace("\"", "");
        unmappedRateCodesStr = StringUtils.substring(unmappedRateCodesStr, 1, unmappedRateCodesStr.length() - 1);
        List<String> unmappedRateCodes = List.of(unmappedRateCodesStr.split(","));
        LOGGER.info("Rate codes with missing MS mapping : " + String.join(", ", unmappedRateCodes));
        return unmappedRateCodes;
    }

    public void generateAlertAndHaltProcessing(List<String> unmappedRateCodes, Long jobExecutionId, String statsCorrelationId) {
        LOGGER.info("jobExecutionId => : " + jobExecutionId);
        AlertType alertType = AlertType.UnmappedRateCodesFound;
        WorkContextType workContext = PacmanWorkContextHelper.getWorkContext();
        InfoMgrTypeEntity alertTypeEntity = alertService.getAlertType(alertType.getName());
        String description = statsCorrelationId + "##" + UNMAPPED_RATE_CODES_ALERT_METADATA + ":" + jobExecutionId;
        String details = String.join(", ", unmappedRateCodes);
        if (alertTypeEntity.isEnabled()) {
            alertService.createAlertWithNewTransaction(workContext, alertTypeEntity, description, details, alertType);
            throw new TetrisException(ErrorCode.USER_ACTION_REQUIRED_EXCEPTION, exceptionMessage + String.join(", ", unmappedRateCodes));
        }
    }

    public void resolveAlertAndResumeFailedStep() {
        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
        InfoMgrInstanceEntity existingAlert = getExistingUnmappedRateCodesFoundAlert();
        String description = existingAlert.getDescription();
        String statsCorrelationId = description.split("##")[0];
        if (existingAlert != null) {
            List<String> unmappedRateCodes = findUnmappedRateCodes(getParameters(workContext, statsCorrelationId));
            if (CollectionUtils.isEmpty(unmappedRateCodes)) {
                LOGGER.info("Attempting to resolve UnmappedRateCodesFound Alert and resume processing");
                alertService.resolveAlert(existingAlert.getId(), workContext.getPropertyId());
                alertService.resumeJobFailedByUnmappedRateCodesAlert(description);
                LOGGER.info("Processing has been resumed");
            } else {
                throw new TetrisException(ErrorCode.USER_ACTION_REQUIRED_EXCEPTION, exceptionMessage + String.join(", ", unmappedRateCodes));
            }
        }
    }

    private Map<String, String> getParameters(WorkContextType workContext, String statsCorrelationId) {
        Map<String, String> params = new HashMap<>();
        params.put("clientCode", workContext.getClientCode());
        params.put("propertyCode", workContext.getPropertyCode());
        params.put("statisticsCorrelationId", statsCorrelationId);
        return params;
    }

    private InfoMgrInstanceEntity getExistingUnmappedRateCodesFoundAlert() {
        Alert alert = new Alert();
        alert.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        alert.setType(AlertType.UnmappedRateCodesFound);
        return alertService.findExistingAlert(alert);
    }

    public boolean isThisFirstExtract() {
        Long fileMetadataIdCount = tenantCrudService.findByNamedQuerySingleResult(FileMetadata.GET_COUNT_FILE_METADATA_ID);
        return fileMetadataIdCount == 1L;
    }

    public List<String> getUnmappedRateCodes() {

        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
        InfoMgrInstanceEntity existingAlert = getExistingUnmappedRateCodesFoundAlert();
        if (workContext != null && existingAlert != null) {
            String description = existingAlert.getDescription();
            String statsCorrelationId = description.split("##")[0];
            return findUnmappedRateCodes(getParameters(workContext, statsCorrelationId));
        } else {
            return Collections.emptyList();
        }

    }


    public List<String> getMarketSegmentList(List<String> rateCodes) {

        if (rateCodes.isEmpty()) {
            return Collections.emptyList();
        }

        String msCodes = pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.MS_RECODING_MARKET_SEGMENT_CODES.value());
        if (StringUtils.isEmpty(msCodes)) {
            return Collections.emptyList();
        } else {
            List<String> msList = new ArrayList<>(Arrays.asList(msCodes.split(",")));
            Collections.sort(msList);
            return msList;
        }
    }

    public String saveRateCodeMapping(List<RateCodeMarketSegmentMapping> rateCodeMapping) {

        boolean isErrorOccurred = false;

        try {
            //make a API call to NGI to add rate code mapping
            WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
            InfoMgrInstanceEntity existingAlert = getExistingUnmappedRateCodesFoundAlert();

            if (workContext != null && existingAlert != null) {

                ObjectMapper objectMapper = new ObjectMapper();
                String jsonData = objectMapper.writeValueAsString(rateCodeMapping);
                String clientCode = workContext.getClientCode();
                String propertyCode = workContext.getPropertyCode();

                restClient.post(RestEndpoints.POST_UNMAPPED_RATE_CODE_V2, Entity.entity(jsonData, MediaType.APPLICATION_JSON_TYPE), clientCode, propertyCode);
            } else {
                isErrorOccurred = true;
            }

        } catch (HttpResponseException hre) {
            LOGGER.error("Received HttpResponseException. Error: " + hre.getResponseBody(), hre);
            isErrorOccurred = true;
        } catch (Exception e) {
            LOGGER.error("exception while preparing JSON data: " + e.getMessage(), e);
            isErrorOccurred = true;
        } finally {
            if (!isErrorOccurred) {
                // data has been posted successfully now we can store this data at g3 side.
                tenantCrudService.save(rateCodeMapping);
            }
        }

        return isErrorOccurred ? "failed" : "Success";
    }

    public boolean codesHavingSimilarSpellingWithDifferentCasesPresentInMappings() {
        List<PMSMigrationMapping> codesHavingSimilarSpellingWithDifferentCases = identifyCodesHavingSimilarSpellingWithDifferentCases();
        return isNotEmpty(codesHavingSimilarSpellingWithDifferentCases);
    }

    public List<PMSMigrationMapping> identifyCodesHavingSimilarSpellingWithDifferentCases() {
        return tenantCrudService.findByNamedQuery(PMSMigrationMapping.GET_BY_SIMILAR_SPELLING_WITH_DIFFERENT_CASE);
    }

    public void replaceOriginalCodeWithTemporaryCode() {
        List<PMSMigrationMapping> pmsMigrationMappings = identifyCodesHavingSimilarSpellingWithDifferentCases();
        tenantCrudService.executeUpdateByNativeQuery(PMSMigrationTemporaryMapping.CREATE_PMS_MIGRATION_TEMPORARY_MAPPING_TABLE_DDL);
        addPmsMigrationTemporaryMappingFrom(pmsMigrationMappings);
        updateTableReplaceFirstCodeWithSecondCode(PMSMigrationUpdateTempCodeTable.PMS_MIGRATION_MAPPING, NEW_ORIGINAL_CODE, NEW_TEMPORARY_CODE);
        updateTableReplaceFirstCodeWithSecondCode(PMSMigrationUpdateTempCodeTable.PMS_REVAMP_NEW_AMS_RULE, NEW_ORIGINAL_CODE, NEW_TEMPORARY_CODE);
        updateTableReplaceFirstCodeWithSecondCode(PMSMigrationUpdateTempCodeTable.MS_RECODING_RATE_CODE_SHIFT, NEW_ORIGINAL_CODE, NEW_TEMPORARY_CODE);
        updateTableReplaceFirstCodeWithSecondCode(PMSMigrationUpdateTempCodeTable.MS_RECODING_BUSINESS_TYPE_SHIFT, NEW_ORIGINAL_CODE, NEW_TEMPORARY_CODE);
        updateTableReplaceFirstCodeWithSecondCode(PMSMigrationUpdateTempCodeTable.PMS_REVAMP_IGNORED_RATE_CODE, NEW_ORIGINAL_CODE, NEW_TEMPORARY_CODE);
        updateTableReplaceFirstCodeWithSecondCode(PMSMigrationUpdateTempCodeTable.PMS_MIGRATION_RATE_CODE_MKT_SEG_MAPPING, NEW_ORIGINAL_CODE, NEW_TEMPORARY_CODE);
    }

    public void replaceTempCodeWithOriginalCode() {
        updateTableReplaceFirstCodeWithSecondCode(PMSMigrationUpdateTempCodeTable.PMS_MIGRATION_MAPPING, NEW_TEMPORARY_CODE, NEW_ORIGINAL_CODE);
        updateTableReplaceFirstCodeWithSecondCode(PMSMigrationUpdateTempCodeTable.PMS_REVAMP_NEW_AMS_RULE, NEW_TEMPORARY_CODE, NEW_ORIGINAL_CODE);
        updateTableReplaceFirstCodeWithSecondCode(PMSMigrationUpdateTempCodeTable.MS_RECODING_RATE_CODE_SHIFT, NEW_TEMPORARY_CODE, NEW_ORIGINAL_CODE);
        updateTableReplaceFirstCodeWithSecondCode(PMSMigrationUpdateTempCodeTable.MS_RECODING_BUSINESS_TYPE_SHIFT, NEW_TEMPORARY_CODE, NEW_ORIGINAL_CODE);
        updateTableReplaceFirstCodeWithSecondCode(PMSMigrationUpdateTempCodeTable.PMS_REVAMP_IGNORED_RATE_CODE, NEW_TEMPORARY_CODE, NEW_ORIGINAL_CODE);
        updateTableReplaceFirstCodeWithSecondCode(PMSMigrationUpdateTempCodeTable.PMS_MIGRATION_RATE_CODE_MKT_SEG_MAPPING, NEW_TEMPORARY_CODE, NEW_ORIGINAL_CODE);
        updateTableReplaceFirstCodeWithSecondCode(PMSMigrationUpdateTempCodeTable.ANALYTICAL_MKT_SEG, NEW_TEMPORARY_CODE, NEW_ORIGINAL_CODE);
        updateTableReplaceFirstCodeWithSecondCode(PMSMigrationUpdateTempCodeTable.ANALYTICAL_MKT_SEG_AUD, NEW_TEMPORARY_CODE, NEW_ORIGINAL_CODE);
        updateTableReplaceFirstCodeWithSecondCode(PMSMigrationUpdateTempCodeTable.YIELD_CATEGORY_RULE, NEW_TEMPORARY_CODE, NEW_ORIGINAL_CODE);
        updateTableReplaceFirstCodeWithSecondCode(PMSMigrationUpdateTempCodeTable.MKT_SEG, NEW_TEMPORARY_CODE, NEW_ORIGINAL_CODE);
        tenantCrudService.executeUpdateByNativeQuery(MarketSegmentMaster.DELETE_EXISTING_RECORDS_HAVING_SAME_SPELLING_DIFFERENT_CASE);
        updateTableReplaceFirstCodeWithSecondCode(PMSMigrationUpdateTempCodeTable.MKT_SEG_MASTER, NEW_TEMPORARY_CODE, NEW_ORIGINAL_CODE);
        if (isMSRecodingSupportedForIndependentProduct() && isIndependentProductEnabled()) {
            updateIndependentProductMappingTables();
        }
    }

    private void updateIndependentProductMappingTables() {
        updateTableReplaceFirstCodeWithSecondCode(PMSMigrationUpdateTempCodeTable.MKT_SEG_PRODUCT_MAPPING, NEW_TEMPORARY_CODE, NEW_ORIGINAL_CODE);
        updateTableReplaceFirstCodeWithSecondCode(PMSMigrationUpdateTempCodeTable.PRODUCT_RATE_CODE, NEW_TEMPORARY_CODE, NEW_ORIGINAL_CODE);
    }

    private boolean isIndependentProductEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
    }

    private boolean isMSRecodingSupportedForIndependentProduct() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MS_RECODING_SUPPORT_FOR_INDEPENDENT_PRODUCT);
    }

    private void updateTableReplaceFirstCodeWithSecondCode(PMSMigrationUpdateTempCodeTable migrationTable, String codeToBeReplaced, String codeToReplaceWith) {
        tenantCrudService.executeUpdateByNativeQuery(migrationTable.buildUpdateQuery(codeToBeReplaced, codeToReplaceWith));
    }

    private void addPmsMigrationTemporaryMappingFrom(List<PMSMigrationMapping> pmsMigrationMappings) {
        List<PMSMigrationTemporaryMapping> pmsMigrationTemporaryMappings = pmsMigrationMappings.stream()
                .map(mapping -> new PMSMigrationTemporaryMapping(mapping.getCodeType().name(), mapping.getCurrentCode(), mapping.getNewEquivalentCode(), mapping.getNewEquivalentCode() + "-CS"))
                .collect(Collectors.toList());
        pmsRevampAMSDataService.executeBatchInsert(new ArrayList<>(pmsMigrationTemporaryMappings),
                obj -> ((PMSMigrationTemporaryMapping) obj).toInsertQueryFormat(), PMSMigrationTemporaryMapping.INSERT_PMS_MIGRATION_TEMPORARY_MAPPING_QUERY_PREFIX);
        PMSMigrationConfig pmsMigrationConfig = pmsMigrationService.fetchOngoingMigrationConfiguration();
        if ((pmsMigrationConfig != null) && Boolean.FALSE.equals(pmsMigrationConfig.isUseGenericTemplateForMigration())) {
            List<PMSMigrationRateCodeMktSegMapping> rateCodeMktSegMappings = tenantCrudService.findByNamedQuery(PMSMigrationRateCodeMktSegMapping.GET_BY_SIMILAR_SPELLING_WITH_DIFFERENT_CASE);
            final List<PMSMigrationTemporaryMapping> temporaryMappings = rateCodeMktSegMappings.stream()
                    .map(mappping -> new PMSMigrationTemporaryMapping(PMSMigrationMappingType.RATE_CODE.name(), mappping.getCurrentRateCode(), mappping.getNewRateCode(), mappping.getNewRateCode() + "-CS"))
                    .collect(Collectors.toList());
            pmsRevampAMSDataService.executeBatchInsert(new ArrayList<>(temporaryMappings),
                    obj -> ((PMSMigrationTemporaryMapping) obj).toInsertQueryFormat(), PMSMigrationTemporaryMapping.INSERT_PMS_MIGRATION_TEMPORARY_MAPPING_QUERY_PREFIX);

        }
    }

    public List<MktSegProductDetailsDTO> getMktSegProductDetailsWithEqualToBarAttribute(List<String> productNames) {
        String query = "SELECT DISTINCT p.Name, ams.Market_Code, ams.Mapped_Market_Code, pm.New_Equivalent_Code, pm.Discontinued" +
                " FROM Product p JOIN Mkt_Seg_Product_Mapping mp ON(p.Product_ID = mp.Product_ID)" +
                " JOIN Analytical_Mkt_Seg ams ON (ams.Mapped_Market_Code = MP.Mkt_Seg_Code)" +
                " JOIN PMS_Migration_Mapping pm ON (pm.Current_Code = ams.Market_Code AND pm.Code_Type in ('MARKET_SEGMENT_NON_GROUP', 'MARKET_SEGMENT'))" +
                " WHERE ams.Attribute = 'EQUAL_TO_BAR' AND p.name IN(:productNames) AND p.status_id = 1";

        List<Object[]> rows = tenantCrudService.findByNativeQuery(query,
                QueryParameter.with("productNames", productNames).parameters());
        List<MktSegProductDetailsDTO> mktSegProductDetailDTOS = new ArrayList<>();
        rows.forEach(row -> mktSegProductDetailDTOS.add(
                new MktSegProductDetailsDTO(
                        (String) row[0],
                        (String) row[1],
                        (String) row[2],
                        (String) row[3],
                        getBooleanValue(4, row)
                )
        ));
        return mktSegProductDetailDTOS;

    }

    public List<ProductRateCodeAmsRule> getExistingProductRateCodeRulesForRateCodes(Set<String> rateCodesWithOldNames) {
        if (isEmpty(rateCodesWithOldNames)) {
            return Collections.emptyList();
        }
        Set<ProductRateCodeAmsRule> productRateCodeMarketCodeFromAms = new HashSet<>();
        productRateCodeMarketCodeFromAms.addAll(getProductRateCodeForMarketCodeFromAms(rateCodesWithOldNames));
        Set<ProductRateCodeAmsRule> productRateCodeMarketCodeFromReservations = new HashSet<>();
        productRateCodeMarketCodeFromReservations.addAll(getProductRateCodeForMarketCodeFromReservationNight(rateCodesWithOldNames));
        productRateCodeMarketCodeFromReservations.addAll(getProductRateCodeForMarketCodeFromReservationNightForBarProducts(rateCodesWithOldNames));
        productRateCodeMarketCodeFromReservations.addAll(getProductRateCodeForMarketCodeForBarProduct(rateCodesWithOldNames));
        productRateCodeMarketCodeFromAms.addAll(productRateCodeMarketCodeFromReservations);
        Integer percentageValueToSkipMarketCodeRateCodeReservations = pacmanConfigParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.PERCENTAGE_TO_SKIP_RESERVATIONS_HAVING_LESS_VOLUME_IN_PRODUCT_RATE_VALIDATION_IN_MS_RECODING.getParameterName());
        if (null != percentageValueToSkipMarketCodeRateCodeReservations && percentageValueToSkipMarketCodeRateCodeReservations > 0) {
            Set<MarketCodeRateCodeReservationVolume> marketCodeRateCodeVolumeInPercentageFor = getMarketCodeRateCodeVolumeInPercentageFor(rateCodesWithOldNames);
            Set<ProductRateCodeAmsRule> productRateCodeAmsRules = removeProductRateCodeMarketCodeForLessVolume(productRateCodeMarketCodeFromAms, marketCodeRateCodeVolumeInPercentageFor, new BigDecimal(percentageValueToSkipMarketCodeRateCodeReservations));
            return productRateCodeAmsRules.stream().collect(Collectors.toList());
        }
        return productRateCodeMarketCodeFromAms.stream().collect(Collectors.toList());
    }

    private Set<ProductRateCodeAmsRule> removeProductRateCodeMarketCodeForLessVolume(Set<ProductRateCodeAmsRule> productRateCodeMarketCodeFromAms, Set<MarketCodeRateCodeReservationVolume> marketCodeRateCodeVolumeInPercentageFor, BigDecimal percentageValue) {
        return productRateCodeMarketCodeFromAms.stream()
                .filter(productRateCodeAmsRule -> skipLessVolumeMarketCodes(marketCodeRateCodeVolumeInPercentageFor, percentageValue, productRateCodeAmsRule))
                .collect(Collectors.toSet());
    }

    private boolean skipLessVolumeMarketCodes(Set<MarketCodeRateCodeReservationVolume> marketCodeRateCodeVolumeInPercentageFor,
                                              BigDecimal percentageValue, ProductRateCodeAmsRule productRateCodeAmsRule) {
        Set<String> sharedRateCodes = marketCodeRateCodeVolumeInPercentageFor.stream()
                .filter(t -> t.getPercentage().compareTo(new BigDecimal("100")) < 0).map(MarketCodeRateCodeReservationVolume::getRateCode)
                .collect(Collectors.toSet());
        if (!sharedRateCodes.contains(productRateCodeAmsRule.getRateCode())) {
            return true;
        }
        String marketCode = productRateCodeAmsRule.getMarketCode();
        String rateCode = productRateCodeAmsRule.getRateCode();
        return marketCodeRateCodeVolumeInPercentageFor.stream().anyMatch(mcRcPerc -> mcRcPerc.getMarketCode().equals(marketCode) &&
                mcRcPerc.getRateCode().equals(rateCode) && mcRcPerc.getPercentage().compareTo(percentageValue) >= 0);
    }

    private Set<MarketCodeRateCodeReservationVolume> getMarketCodeRateCodeVolumeInPercentageFor(Set<String> rateCodesWithOldNames) {
        List<Object[]> results = tenantCrudService.findByNamedQuery(ReservationNight.GET_MARKET_CODE_RATE_CODE_VOLUME_BY_PERCENTAGE_FOR_RATE_CODES,
                QueryParameter.with("rateCodes", rateCodesWithOldNames).parameters());
        if (isNotEmpty(results)) {
            return results.stream().map(row -> new MarketCodeRateCodeReservationVolume((String) row[0], (String) row[1], new BigDecimal((Integer) row[2]), (BigDecimal) row[3]))
                    .collect(Collectors.toSet());
        }
        return Collections.emptySet();
    }

    private Set<ProductRateCodeAmsRule> getProductRateCodeForMarketCodeForBarProduct(Set<String> rateCodesWithOldNames) {
        Product primaryPricedProduct = independentProductsService.getPrimaryPricedProduct();
        List<Object[]> results = tenantCrudService.findByNamedQuery(AnalyticalMarketSegment.GET_MARKET_CODE_RATE_CODE_PRODUCT_NAME_FOR_BAR_PRODUCT,
                QueryParameter.with("rateCodes", rateCodesWithOldNames).and("productId", primaryPricedProduct.getId()).parameters());
        if (isNotEmpty(results)) {
            return results.stream().map(row -> new ProductRateCodeAmsRule((String) row[0], (String) row[1], (String) row[2]))
                    .collect(Collectors.toSet());
        }
        return Collections.emptySet();
    }

    private Set<ProductRateCodeAmsRule> getProductRateCodeForMarketCodeFromReservationNightForBarProducts(Set<String> rateCodesWithOldNames) {
        Product primaryPricedProduct = independentProductsService.getPrimaryPricedProduct();
        List<Product> childProductsOfPrimaryProduct = AgileRatesUtils.getChildren(primaryPricedProduct, independentProductsService.getAllProducts());
        List<Integer> barLinkedProductIds = childProductsOfPrimaryProduct.stream().map(Product::getId).collect(Collectors.toList());
        if (isNotEmpty(barLinkedProductIds)) {
            List<Object[]> results = tenantCrudService.findByNamedQuery(AnalyticalMarketSegment.GET_MARKET_CODE_RATE_CODE_PRODUCT_NAME_FROM_RESERVATION_NIGHT_FOR_BAR_PRODUCT,
                    QueryParameter.with("rateCodes", rateCodesWithOldNames).and("productIds", barLinkedProductIds).parameters());
            if (isNotEmpty(results)) {
                return results.stream().map(row -> new ProductRateCodeAmsRule((String) row[0], (String) row[1], primaryPricedProduct.getName()))
                        .collect(Collectors.toSet());
            }
        }
        return Collections.emptySet();
    }

    private Set<ProductRateCodeAmsRule> getProductRateCodeForMarketCodeFromReservationNight(Set<String> rateCodesWithOldNames) {
        List<Object[]> results = tenantCrudService.findByNamedQuery(AnalyticalMarketSegment.GET_MARKET_CODE_RATE_CODE_PRODUCT_NAME_FROM_RESERVATION_NIGHT,
                QueryParameter.with("rateCodes", rateCodesWithOldNames).parameters());
        if (isNotEmpty(results)) {
            return results.stream().map(row -> new ProductRateCodeAmsRule((String) row[0], (String) row[1], (String) row[2]))
                    .collect(Collectors.toSet());
        }
        return Collections.emptySet();
    }

    private Set<ProductRateCodeAmsRule> getProductRateCodeForMarketCodeFromAms(Set<String> rateCodesWithOldNames) {
        List<Object[]> results = tenantCrudService.findByNamedQuery(AnalyticalMarketSegment.GET_MARKET_CODE_RATE_CODE_PRODUCT_NAME,
                QueryParameter.with("rateCodes", rateCodesWithOldNames).parameters());
        if (isNotEmpty(results)) {
            return results.stream().map(row -> new ProductRateCodeAmsRule((String) row[0], (String) row[1], (String) row[2]))
                    .collect(Collectors.toSet());
        }
        return Collections.emptySet();
    }

    public void deleteIPProductMappings() {
        deleteOrphanMarketSegmentProductMappings();
        deleteOrphanProductRateCodeMapping();
    }

    private void deleteOrphanMarketSegmentProductMappings() {
        independentProductsRepository.deleteOrphanMarketSegmentProductMappings();
    }

    private void deleteOrphanProductRateCodeMapping() {
        List<ProductRateCode> orphanProductRateCodes = independentProductsRepository.getOrphanProductRateCodesForIPProducts();
        if (CollectionUtils.isNotEmpty(orphanProductRateCodes)) {
            deleteProductRateCodeMappings(orphanProductRateCodes);
        }
    }

    public Map<String, Set<String>> getNewMarketCodeRateCodeForStraightMS(Set<String> newMarketCodes) {
        if (isNotEmpty(newMarketCodes)) {
            List<Object[]> results = tenantCrudService.findByNamedQuery(AnalyticalMarketSegment.GET_NEW_MARKET_CODE_RATE_CODE_FOR_MARKET_CODE,
                    QueryParameter.with("newMarketCodes", newMarketCodes).parameters());
            if (isNotEmpty(results)) {
                return results.stream()
                        .collect(Collectors.groupingBy(row -> (String) row[0], Collectors.mapping(row -> (String) row[1], Collectors.toSet())));
            }
        }
        return Collections.emptyMap();
    }

    public List<String> getSharedRateCodesWithMultipleProductsInProductRateCode() {
        Product primaryPricedProduct = independentProductsService.getPrimaryPricedProduct();
        List<Product> childProductsOfPrimaryProduct = AgileRatesUtils.getChildren(primaryPricedProduct, independentProductsService.getAllProducts());
        List<Integer> barLinkedProductIds = childProductsOfPrimaryProduct.stream().map(Product::getId).collect(Collectors.toList());
        if (isEmpty(barLinkedProductIds)) {
            barLinkedProductIds = new ArrayList<>();
            barLinkedProductIds.add(-1);
        }
        return tenantCrudService.findByNamedQuery(ProductRateCode.GET_RATE_CODES_HAVING_MULTIPLE_PRODUCTS,
                QueryParameter.with("barLinkedProducts", barLinkedProductIds).parameters());
    }

}
