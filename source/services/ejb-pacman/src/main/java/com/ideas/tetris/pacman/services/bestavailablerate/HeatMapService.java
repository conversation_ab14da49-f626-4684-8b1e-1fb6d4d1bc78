package com.ideas.tetris.pacman.services.bestavailablerate;

import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.OccupancyDateRevenueOccupancyDto;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.rowmapper.OccupancyDateRevenueOccupancyDtoRowMapper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.heatmap.entity.HeatMapRangeAndColors;
import com.ideas.tetris.pacman.services.heatmap.entity.HeatMapRangeAndColorsConfig;
import com.ideas.tetris.pacman.services.heatmap.entity.HeatMapRangeAndColorsConfigType;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.BAR_DECISION_VALUE_RATEOFDAY;

@Component
@Transactional
@Slf4j
public class HeatMapService {

    private static final String RATE_NAMES_ORDERED_BY_RATE_VALUE_FOR_ACCOM_CLASS = new StringBuilder()
            .append("select ru.Rate_Unqualified_ID as rateUnqualifiedId, ru.Rate_Code_Name as rateCodeName, atype.Accom_Class_ID as accomClassId, MAX(rud.#columnName) as rateValue ")
            .append(" from ")
            .append("    Rate_Unqualified ru, ")
            .append("    Rate_Unqualified_Details rud, ")
            .append("    Accom_Type atype ")
            .append(" where ")
            .append("    ru.Rate_Unqualified_ID = rud.Rate_Unqualified_ID ")
            .append("    and rud.Accom_Type_ID = atype.Accom_Type_ID ")
            .append("    and ru.System_Default != 1")
            .append("    and ru.Status_ID = 1 ")
            .append("    and atype.Accom_Class_ID = :accomClassId ")
            .append("    and rud.Start_Date_DT <= :startDate ")
            .append("    and rud.End_Date_DT >= :endDate ")
            .append(" group by ru.Rate_Unqualified_ID, ru.Rate_Code_Name, atype.Accom_Class_ID ")
            .append(" order by rateValue desc").toString();
    private static final String QUERY_PEAK_DEMAND_ACTUAL_CAPACITY = "select Occupancy_DT, User_Peak_Demand_Percentage, User_Peak_Demand_Percentage_ActualCapacity from Vw_Peak_Demand where Occupancy_DT >= :startDate and Occupancy_DT <= :endDate and Property_ID =:propertyId";
    private static final String QUERY_PEAK_DEMAND_ACTUAL_CAPACITY_BY_INVENTORY_GROUP = new StringBuilder()
            .append("select Occupancy_DT, User_Peak_Demand_Percentage, User_Peak_Demand_Percentage_ActualCapacity ")
            .append("from Vw_Peak_Demand_Inventory_Group ")
            .append("where Occupancy_DT >= :startDate ")
            .append("and Occupancy_DT <= :endDate ")
            .append("and Inventory_Group_ID = :inventoryGroupId").toString();

    private static final String QUERY_PEAK_DEMAND_ACTUAL_CAPACITY_BY_PRODUCT = new StringBuilder()
            .append("select Occupancy_DT, User_Peak_Demand_Percentage, User_Peak_Demand_Percentage_ActualCapacity ")
            .append("from ufn_Peak_Demand_Product(:startDate , :endDate, :productId, :accomClassIds, :forecastGroupIds) ")
            .toString();

    private static final String QUERY_OCCUPANCY_FORECAST = "select Occupancy_DT, sum(Revenue) as Revenue, sum(total_Accom_Capacity) - sum(OOO) as AvailableCapacity, sum(Occupancy_NBR), pofv.Occupancy_Percent as Occupancy_Percent_Per_Property, sum(total_Accom_Capacity) as physicalCapacity from FN_Occupancy_Forecast_Zero_Diet(:caughtUpDate, 0) as pofv where Occupancy_DT >= :startDate and Occupancy_DT <= :endDate and Property_ID in(:propertyIds) group by occupancy_dt,pofv.Occupancy_Percent";
    private static final String QUERY_OCCUPANCY_FORECAST_FOR_INVENTORY_GROUP = new StringBuilder()
            .append("select Occupancy_DT, Revenue, Accom_Capacity - OOO as AvailableCapacity, ")
            .append("Occupancy_NBR, pofv.Occupancy_Percent as Occupancy_Percent_Per_Property, Accom_Capacity as physicalCapacity ")
            .append("from FN_Occupancy_Forecast_Inventory_Group_View(:caughtUpDate, 0, :inventoryGroupId, 2) as pofv ")
            .append("where Occupancy_DT >= :startDate and Occupancy_DT <= :endDate ")
            .toString();

    private static final String QUERY_OCCUPANCY_FORECAST_FOR_INVENTORY_GROUP_DATES = new StringBuilder()
            .append("select Occupancy_DT, Revenue, Accom_Capacity - OOO as AvailableCapacity, ")
            .append("Occupancy_NBR, pofv.Occupancy_Percent as Occupancy_Percent_Per_Property, Accom_Capacity as physicalCapacity ")
            .append("from FN_Occupancy_Forecast_Inventory_Group_View_dates(:caughtUpDate, 0, :inventoryGroupId, 2, :startDate, :endDate) as pofv ")
            .append("where Occupancy_DT >= :startDate and Occupancy_DT <= :endDate ")
            .toString();

    private static final String QUERY_OCCUPANCY_FORECAST_FOR_PRODUCT = new StringBuilder()
            .append("select Occupancy_DT, Revenue, Accom_Capacity - OOO as AvailableCapacity, ")
            .append("Occupancy_NBR, pofv.Occupancy_Percent as Occupancy_Percent_Per_Property, Accom_Capacity as physicalCapacity ")
            .append("from ufn_Occupancy_Forecast_Product(:caughtUpDate, 0, :productId, 2) as pofv ")
            .append("where Occupancy_DT >= :startDate and Occupancy_DT <= :endDate ")
            .toString();

    private static final String QUERY_OCCUPANCY_FORECAST_FOR_PRODUCT_USE_MKT_ACCOM_ACTIVITY = new StringBuilder()
            .append("select Occupancy_DT, Revenue, Accom_Capacity - OOO as AvailableCapacity, ")
            .append("Occupancy_NBR, pofv.Occupancy_Percent as Occupancy_Percent_Per_Property, Accom_Capacity as physicalCapacity ")
            .append("from ufn_Occupancy_Forecast_Product_MktAccomActivity(:caughtUpDate, 0, :productId, 2, :startDate, :endDate) as pofv ")
            .append("where Occupancy_DT >= :startDate and Occupancy_DT <= :endDate ")
            .toString();

    private static final String QUERY_ACCOM_REVENUE = "select Occupancy_DT, Revenue, ADR, REVPAR from FN_AC_Occupancy_FCST(:caughtUpDate, :is_physical_capacity_enabled)  where Occupancy_DT >= :startDate and Occupancy_DT <= :endDate and Property_ID =:propertyId and Accom_Class_ID =:accomClassId";

    private static final String QUERY_OVERBOOKING = "select Occupancy_DT, Overbooking_Decision_Percentage from FN_Overbooking_Property_Level(:caughtUpDate) where Occupancy_DT >= :startDate and Occupancy_DT <= :endDate and Property_ID = :propertyId";

    private static final String QUERY_OVERBOOKING_ENABLE_PHYSICAL_CAPACITY = "select Occupancy_DT, Overbooking_Decision_Percentage, Overbooking_Decision, Total_Accom_Capacity " +
            " from FN_Overbooking_Property_Level(:caughtUpDate) where Occupancy_DT >= :startDate and Occupancy_DT <= :endDate and Property_ID = :propertyId";

    private static final String QUERY_OVERBOOKING_IN_RANGE = "EXEC dbo.usp_get_overbooking_property_level :caughtUpDate, :propertyId, :startDate, :endDate";

    private static final String QUERY_GET_CONFIG_BETWEEN_DATES = "With Config_Count(recordCount) as " +
            "(select " +
            "   count(*) " +
            "from " +
            "   Heat_Map_Range_and_Colors_Config_Type as configType, Heat_Map_Range_and_Colors_Config as config " +
            "where " +
            "   (configType.Heat_Map_Config_Type_Id = config.Heat_Map_Config_Type_Id) AND " +
            "   (config.Start_Date IS NULL AND config.End_Date IS NULL) AND " +
            "   (configType.Config_Type = :defaultType OR configType.Config_Type = :customizeType)) " +
            "select " +
            "   config.Heat_Map_Config_Id, config.Start_Date, config.End_Date, ct.Heat_Map_Config_Type_Id, ct.Config_Type, rac.Range_From, rac.Range_To, " +
            "   rac.Occupancy_Forecast_Color_Code, rac.Peak_Demand_Color_Code, rac.Heat_Map_Range_and_Colors_Id  " +
            "from " +
            "   Heat_Map_Range_and_Colors_Config as config, Heat_Map_Range_and_Colors as rac, Heat_Map_Range_and_Colors_Config_Type as ct  " +
            "where  " +
            "   ((config.Start_Date between :startDate and :endDate) OR (config.End_Date between :startDate and :endDate) OR " +
            "   (config.Start_Date <= :startDate AND config.End_Date >= :endDate))" +
            "   AND config.Heat_Map_Config_Id = rac.Heat_Map_Config_Id " +
            "   AND config.Heat_Map_Config_Type_Id = ct.Heat_Map_Config_Type_Id " +
            "Union all " +
            "select " +
            "   config.Heat_Map_Config_Id, config.Start_Date, config.End_Date, ct.Heat_Map_Config_Type_Id, ct.Config_Type, rac.Range_From, rac.Range_To, " +
            "   rac.Occupancy_Forecast_Color_Code, rac.Peak_Demand_Color_Code, rac.Heat_Map_Range_and_Colors_Id  " +
            "from " +
            "   Heat_Map_Range_and_Colors_Config as config, Heat_Map_Range_and_Colors as rac, Heat_Map_Range_and_Colors_Config_Type as ct  " +
            "where " +
            "   (config.Heat_Map_Config_Id = rac.Heat_Map_Config_Id) " +
            "   AND config.Heat_Map_Config_Type_Id = ct.Heat_Map_Config_Type_Id " +
            "   AND config.Heat_Map_Config_Type_Id = (select configType.Heat_Map_Config_Type_Id from Heat_Map_Range_and_Colors_Config_Type as configType " +
            "where " +
            "   (configType.Config_Type = :customizeType AND 2 =(select recordCount from Config_Count)) " +
            "   OR (configType.Config_Type = :defaultType AND 1 =(select recordCount from Config_Count))) " +
            "order by config.start_date ASC, rac.Range_From DESC";
    private static final String QUERY_GET_CONFIG_BETWEEN_FISCAL_DATES = "With Config_Count(recordCount) as " +
            "(select " +
            "   count(*) " +
            "from " +
            "   Heat_Map_Range_and_Colors_Config_Type as configType, Heat_Map_Range_and_Colors_Config as config " +
            "where " +
            "   (configType.Heat_Map_Config_Type_Id = config.Heat_Map_Config_Type_Id) AND " +
            "   (config.Start_Date IS NULL AND config.End_Date IS NULL) AND " +
            "   (configType.Config_Type = :defaultType OR configType.Config_Type = :customizeType)) " +
            "select " +
            "   config.Heat_Map_Config_Id, config.Start_Date, config.End_Date, ct.Heat_Map_Config_Type_Id, ct.Config_Type, rac.Range_From, rac.Range_To, " +
            "   rac.Occupancy_Forecast_Color_Code, rac.Peak_Demand_Color_Code, rac.Heat_Map_Range_and_Colors_Id,config.Fiscal_Start_Date,config.Fiscal_End_Date " +
            "from " +
            "   Heat_Map_Range_and_Colors_Config as config, Heat_Map_Range_and_Colors as rac, Heat_Map_Range_and_Colors_Config_Type as ct  " +
            "where  " +
            "   ((config.Fiscal_Start_Date between :startDate and :endDate) OR (config.Fiscal_End_Date between :startDate and :endDate) OR " +
            "   (config.Fiscal_Start_Date <= :startDate AND config.Fiscal_End_Date >= :endDate))" +
            "   AND config.Heat_Map_Config_Id = rac.Heat_Map_Config_Id " +
            "   AND config.Heat_Map_Config_Type_Id = ct.Heat_Map_Config_Type_Id " +
            "Union all " +
            "select " +
            "   config.Heat_Map_Config_Id, config.Start_Date, config.End_Date, ct.Heat_Map_Config_Type_Id, ct.Config_Type, rac.Range_From, rac.Range_To, " +
            "   rac.Occupancy_Forecast_Color_Code, rac.Peak_Demand_Color_Code, rac.Heat_Map_Range_and_Colors_Id,config.Fiscal_Start_Date,config.Fiscal_End_Date " +
            "from " +
            "   Heat_Map_Range_and_Colors_Config as config, Heat_Map_Range_and_Colors as rac, Heat_Map_Range_and_Colors_Config_Type as ct  " +
            "where " +
            "   (config.Heat_Map_Config_Id = rac.Heat_Map_Config_Id) " +
            "   AND config.Heat_Map_Config_Type_Id = ct.Heat_Map_Config_Type_Id " +
            "   AND config.Heat_Map_Config_Type_Id = (select configType.Heat_Map_Config_Type_Id from Heat_Map_Range_and_Colors_Config_Type as configType " +
            "where " +
            "   (configType.Config_Type = :customizeType AND 2 =(select recordCount from Config_Count)) " +
            "   OR (configType.Config_Type = :defaultType AND 1 =(select recordCount from Config_Count))) " +
            "order by config.start_date ASC, rac.Range_From DESC";

    private static final String IS_PHYSICAL_CAPACITY_ENABLED = "is_physical_capacity_enabled";
    private static final String CAUGHT_UP_DATE = "caughtUpDate";
    private static final String START_DATE = "startDate";
    private static final String END_DATE = "endDate";
    private static final String PROPERTY_ID = "propertyId";
    private static final String ACCOM_CLASS_ID = "accomClassId";
    private static final String PROPERTY_IDS = "propertyIds";
    private static final String INVENTORY_GROUP_ID = "inventoryGroupId";
    public static final String COLUMN_NAME_PLACE_HOLDER = "#columnName";

    public static final String CUSTOMIZE_TYPE = "customizeType";
    public static final String DEFAULT_TYPE = "defaultType";
    public static final String CUSTOMIZE = "CUSTOMIZE";
    public static final String DEFAULT = "DEFAULT";
    private static final String PRODUCT_ID = "productId";
    private static final String ACCOM_CLASS_IDS = "accomClassIds";
    private static final String FORECAST_GROUP_IDS = "forecastGroupIds";

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService crudService;
    @Autowired
	private DateService dateService;

    @Autowired
	private PacmanConfigParamsService configService;
    @Autowired
	private RateDeterminator rateDeterminator;
    @Autowired
	private PropertyGroupService propertyGroupService;
    @Autowired
	private AbstractMultiPropertyCrudService multiPropertyCrudService;
    @Autowired
	private AccommodationService accommodationService;

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    public void setDateService(DateService dateService) {
        this.dateService = dateService;
    }

    public void setConfigService(PacmanConfigParamsService configService) {
        this.configService = configService;
    }

    public void setRateDeterminator(RateDeterminator rateDeterminator) {
        this.rateDeterminator = rateDeterminator;
    }

    public void setPropertyGroupService(PropertyGroupService propertyGroupService) {
        this.propertyGroupService = propertyGroupService;
    }

    public void setMultiPropertyCrudService(AbstractMultiPropertyCrudService multiPropertyCrudService) {
        this.multiPropertyCrudService = multiPropertyCrudService;
    }


    @SuppressWarnings("unchecked")
    //TODO why need to call every time on heat map arrows?
    public List<String> getOrderedRatePlanNames(final int accomClassId) {
        final Date caughtUpDate = DateUtil.removeTimeFromDate(dateService.getCaughtUpDate());
        String queryWithExpectedColumnName = RATE_NAMES_ORDERED_BY_RATE_VALUE_FOR_ACCOM_CLASS
                .replace(COLUMN_NAME_PLACE_HOLDER, new SimpleDateFormat("EEEE", Locale.ENGLISH).format(caughtUpDate));
        Query nativeQuery = crudService.getEntityManager().createNativeQuery(queryWithExpectedColumnName);
        nativeQuery.setParameter(START_DATE, caughtUpDate);
        nativeQuery.setParameter(END_DATE, caughtUpDate);
        nativeQuery.setParameter(ACCOM_CLASS_ID, accomClassId);

        List<Object[]> resultList = nativeQuery.getResultList();
        return resultList.stream().map(row -> String.valueOf(row[1])).collect(Collectors.toList());
    }

    public Map<Date, BigDecimal> getOccupancyForecastHeatMapData() {
        Date startDate = dateService.getHeatMapDisplayWindowStartDate();
        Date endDate = dateService.getHeatMapDisplayWindowEndDate();

        return getOccupancyForecastHeatMapData(startDate, endDate);
    }

    public Map<Date, BigDecimal> getOccupancyForecastHeatMapData(Date startDate, Date endDate) {
        List<OccupancyDateRevenueOccupancyDto> results = multiPropertyCrudService.findByNativeQueryUnionAcrossProperties(
                propertyGroupService.getPropertyContextAsList(),
                QUERY_OCCUPANCY_FORECAST,
                QueryParameter.with(START_DATE, startDate).and(END_DATE, endDate).and(CAUGHT_UP_DATE, dateService.getCaughtUpDate()).and(PROPERTY_IDS, propertyGroupService.getPropertyContextAsList()).parameters(),
                new OccupancyDateRevenueOccupancyDtoRowMapper());
        return processOccupancyForecastHeatMapData(results);
    }

    public Map<Date, BigDecimal> getOccupancyForecastHeatMapDataByInventoryGroup(Date startDate, Date endDate, Integer inventoryGroupId) {
        String queryOccupancyForecastForInventoryGroup = QUERY_OCCUPANCY_FORECAST_FOR_INVENTORY_GROUP;
        if (configService.getBooleanParameterValue(PreProductionConfigParamName.DATE_FILTERS_FOR_STORED_PROC_BAD_AAG)) {
            queryOccupancyForecastForInventoryGroup = QUERY_OCCUPANCY_FORECAST_FOR_INVENTORY_GROUP_DATES;
            log.info(this.getClass().getName()+" using date filters for SQL functions for BAD and AAG screens "+queryOccupancyForecastForInventoryGroup);
        }

        return getOccupancyForecastForHeatMap(startDate, endDate, inventoryGroupId, queryOccupancyForecastForInventoryGroup, INVENTORY_GROUP_ID);
    }

    private Map<Date, BigDecimal> processOccupancyForecastHeatMapData(List<OccupancyDateRevenueOccupancyDto> results) {
        Map<Date, BigDecimal> result = new HashMap<Date, BigDecimal>();
        boolean enablePhysicalCapacityConsideration = configService.isEnablePhysicalCapacityConsideration();
        for (OccupancyDateRevenueOccupancyDto dto : results) {
            if (enablePhysicalCapacityConsideration) {
                BigDecimal occupancyNumber = dto.getOccupancy();
                BigDecimal totalAccomCapacity = dto.getPhysicalCapacity();
                if (occupancyNumber == null || totalAccomCapacity == null || totalAccomCapacity.intValue() == 0) {
                    dto.setOccupancyPercentPerProperty(BigDecimal.ZERO);
                } else {
                    BigDecimal occupancyPercent = occupancyNumber.divide(totalAccomCapacity, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100).setScale(2));
                    dto.setOccupancyPercentPerProperty(occupancyPercent);
                }
                result.put(dto.getOccupancyDate(), dto.getOccupancyPercentPerProperty());
            } else {
                result.put(dto.getOccupancyDate(), dto.getOccupancyPercentPerProperty());
            }
        }
        return result;
    }

    /**
     * @deprecated
     */
    @Deprecated
    public Map<Date, BigDecimal> getOccupancyForecastHeatMapData_DateParameter(DateParameter startDate, DateParameter endDate) {
        return getOccupancyForecastHeatMapData(startDate.getTime(), endDate.getTime());
    }

    public Map<Date, BigDecimal> getOccupancyForecastHeatMapData_LocalDate(LocalDate startDate, LocalDate endDate) {
        return getOccupancyForecastHeatMapData(startDate.toDate(), endDate.toDate());
    }

    public Map<Date, BigDecimal> getOccupancyForecastValueHeatMapData(Date startDate, Date endDate) {
        Map<Date, BigDecimal> result = new HashMap<Date, BigDecimal>();

        List<OccupancyDateRevenueOccupancyDto> results = multiPropertyCrudService.findByNativeQueryUnionAcrossProperties(
                propertyGroupService.getPropertyContextAsList(),
                QUERY_OCCUPANCY_FORECAST,
                QueryParameter.with(START_DATE, startDate).and(END_DATE, endDate).and(CAUGHT_UP_DATE, dateService.getCaughtUpDate()).and(PROPERTY_IDS, propertyGroupService.getPropertyContextAsList()).parameters(),
                new OccupancyDateRevenueOccupancyDtoRowMapper());

        for (OccupancyDateRevenueOccupancyDto dto : results) {
            result.put(dto.getOccupancyDate(), dto.getOccupancy());
        }

        return result;
    }

    public Map<Date, BigDecimal> getPeakDemandHeatMapData() {
        Query q = crudService.getEntityManager().createNativeQuery(QUERY_PEAK_DEMAND_ACTUAL_CAPACITY);
        Date startDate = dateService.getHeatMapDisplayWindowStartDate();
        Date endDate = dateService.getHeatMapDisplayWindowEndDate();

        return executeNativeQueryForPeakDemand(q, startDate, endDate);
    }

    public Map<Date, BigDecimal> getPeakDemandHeatMapData(Date startDate, Date endDate) {
        Query q = crudService.getEntityManager().createNativeQuery(QUERY_PEAK_DEMAND_ACTUAL_CAPACITY);
        return executeNativeQueryForPeakDemand(q, startDate, endDate);
    }

    public Map<Date, BigDecimal> getPeakDemandHeatMapDataByInventoryGroup(Date startDate, Date endDate, Integer inventoryGroupSelection) {
        Query q = crudService.getEntityManager().createNativeQuery(QUERY_PEAK_DEMAND_ACTUAL_CAPACITY_BY_INVENTORY_GROUP);
        return executeNativeQueryForPeakDemand(q, startDate, endDate, inventoryGroupSelection);
    }

    public Map<Date, BigDecimal> getPeakDemandHeatMapDataByProduct(Date startDate, Date endDate, Integer productId,
                                                                   List<Integer> accomClassIds, List<Integer> forecastGroupIds) {
        Query q = crudService.getEntityManager().createNativeQuery(QUERY_PEAK_DEMAND_ACTUAL_CAPACITY_BY_PRODUCT);
        Map<Date, BigDecimal> result = new HashMap<Date, BigDecimal>();
        q.setParameter(START_DATE, startDate);
        q.setParameter(END_DATE, endDate);
        q.setParameter(PRODUCT_ID, productId);
        q.setParameter(ACCOM_CLASS_IDS, StringUtils.join(accomClassIds, ","));
        q.setParameter(FORECAST_GROUP_IDS, StringUtils.join(forecastGroupIds, ","));

        return processPeakDemandHeatMapData(q, result);
    }

    /**
     * @deprecated
     */
    @Deprecated
    public Map<Date, BigDecimal> getPeakDemandHeatMapData_DateParameter(DateParameter startDate, DateParameter endDate) {
        return getPeakDemandHeatMapData(startDate.getTime(), endDate.getTime());
    }

    public Map<Date, BigDecimal> getPeakDemandHeatMapData_LocalDate(LocalDate startDate, LocalDate endDate) {
        return getPeakDemandHeatMapData(startDate.toDate(), endDate.toDate());
    }

    public Map<Date, String> getUnqualifiedRateHeatMapData(int accomClassId) {
        return getUnqualifiedRateHeatMapDataByLos(accomClassId);
    }

    public Map<Date, String> getUnqualifiedRateHeatMapData(int accomClassId, Date startDate, Date endDate) {
        return getUnqualifiedRateHeatMapData(accomClassId, getDefaultLOS(), startDate, endDate);
    }

    /**
     * @deprecated
     */
    @Deprecated
    public Map<Date, String> getUnqualifiedRateHeatMapData_DateParameter(int accomClassId, DateParameter startDate, DateParameter endDate) {
        return getUnqualifiedRateHeatMapData(accomClassId, startDate.getTime(), endDate.getTime());
    }

    public Map<Date, String> getUnqualifiedRateHeatMapData_LocalDate(int accomClassId, LocalDate startDate, LocalDate endDate) {
        return getUnqualifiedRateHeatMapData(accomClassId, startDate.toDate(), endDate.toDate());
    }

    public Map<Date, String> getUnqualifiedRateHeatMapDataByLos(int accomClassId) {
        return getUnqualifiedRateHeatMapData(accomClassId, getDefaultLOS(), dateService.getHeatMapDisplayWindowStartDate(), dateService.getHeatMapDisplayWindowEndDate());
    }

    public boolean isLraEnabled() {
        return configService.isLRAEnabled();
    }


    @SuppressWarnings("unchecked")
    public Map<Date, String> getUnqualifiedRateHeatMapData(int accomClassId, int lengthOfStay, Date startDate, Date endDate) {
        Map<Date, String> result = new HashMap<Date, String>();
        List<DecisionBAROutput> decisions = crudService.findByNamedQuery(DecisionBAROutput.BY_ARRIVALDATERANGE_AND_ACCOMCLASSID_AND_LOS, QueryParameter.with(START_DATE, startDate).and(END_DATE, endDate).and(ACCOM_CLASS_ID, accomClassId).and("lengthOfStay", lengthOfStay).parameters());
        boolean enableSingleBarDecision = isEnableSingleBarDecision();
        boolean lraEnabled = isLraEnabled();
        for (DecisionBAROutput d : decisions) {
            // If the DecisionBAROutput is NO DECISION, don't include it in the Heat Map
            if (!d.getRateUnqualified().thisIsDefaultRate()) {
                //if decision reason type id is 6 then its LRA impacted decision
                if (enableSingleBarDecision && lraEnabled && d.getReasonTypeId() == 6) {
                    result.put(d.getArrivalDate(), Constants.LRA_IMPACTED_DECISION);
                } else if (!enableSingleBarDecision && !lraEnabled && d.getReasonTypeId() == 6) {
                    result.put(d.getArrivalDate(), Constants.LRA_IMPACTED_DECISION);
                } else if (!enableSingleBarDecision && d.getReasonTypeId() == 6) {
                    result.put(d.getArrivalDate(), Constants.LRA_IMPACTED_DECISION);
                } else {
                    result.put(d.getArrivalDate(), d.getRateUnqualified().getName());
                }
            } else {
                result.put(d.getArrivalDate(), Constants.BARDECISIONOVERRIDE_NONE);
            }
        }

        return result;
    }

    private boolean isEnableSingleBarDecision() {
        return configService.getBooleanParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value());
    }

    /**
     * @deprecated
     */
    @Deprecated
    public Map<Date, String> getUnqualifiedRateHeatMapData_DateParameter(int accomClassId, int lengthOfStay, DateParameter startDate, DateParameter endDate) {
        return getUnqualifiedRateHeatMapData(accomClassId, lengthOfStay, startDate.getTime(), endDate.getTime());
    }

    public Map<Date, String> getUnqualifiedRateHeatMapData_LocalDate(int accomClassId, int lengthOfStay, LocalDate startDate, LocalDate endDate) {
        return getUnqualifiedRateHeatMapData(accomClassId, lengthOfStay, startDate.toDate(), endDate.toDate());
    }


    public Map<Date, BigDecimal> getHotelRevenueHeatMapData() {
        Date startDate = dateService.getHeatMapDisplayWindowStartDate();
        Date endDate = dateService.getHeatMapDisplayWindowEndDate();

        return getHotelRevenueHeatMapData(startDate, endDate);
    }

    public Map<Date, BigDecimal> getHotelRevenueHeatMapData(Date startDate, Date endDate) {
        Map<Date, BigDecimal> result = new HashMap<Date, BigDecimal>();

        List<OccupancyDateRevenueOccupancyDto> results = multiPropertyCrudService.findByNativeQueryUnionAcrossProperties(
                propertyGroupService.getPropertyContextAsList(),
                QUERY_OCCUPANCY_FORECAST,
                QueryParameter.with(START_DATE, startDate).and(END_DATE, endDate).and(CAUGHT_UP_DATE, dateService.getCaughtUpDate()).and(PROPERTY_IDS, propertyGroupService.getPropertyContextAsList()).parameters(),
                new OccupancyDateRevenueOccupancyDtoRowMapper());

        for (OccupancyDateRevenueOccupancyDto dto : results) {
            result.put(dto.getOccupancyDate(), dto.getRevenue());
        }

        return result;
    }

    @SuppressWarnings("unchecked")
    public Map<Date, BigDecimal> getAccomClassRevenueHeatMapData(final int accomClassId) {
        Map<Date, BigDecimal> result = new HashMap<Date, BigDecimal>();

        Query q = crudService.getEntityManager().createNativeQuery(QUERY_ACCOM_REVENUE);
        q.setParameter(CAUGHT_UP_DATE, dateService.getCaughtUpDate());
        q.setParameter(START_DATE, dateService.getHeatMapDisplayWindowStartDate());
        q.setParameter(END_DATE, dateService.getHeatMapDisplayWindowEndDate());
        q.setParameter(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
        q.setParameter(ACCOM_CLASS_ID, accomClassId);
        q.setParameter(IS_PHYSICAL_CAPACITY_ENABLED, 0);
        List<Object[]> resultList = q.getResultList();
        for (Object[] row : resultList) {
            result.put((Date) row[0], (BigDecimal) row[1]);
        }

        return result;
    }

    public Map<Date, BigDecimal> getHotelADRHeatMapData() {

        Date startDate = dateService.getHeatMapDisplayWindowStartDate();
        Date endDate = dateService.getHeatMapDisplayWindowEndDate();

        return getHotelADRHeatMapData(startDate, endDate);
    }

    public Map<Date, BigDecimal> getHotelADRHeatMapData(Date startDate, Date endDate) {
        Map<Date, BigDecimal> result = new HashMap<Date, BigDecimal>();

        List<OccupancyDateRevenueOccupancyDto> results = multiPropertyCrudService.findByNativeQueryUnionAcrossProperties(
                propertyGroupService.getPropertyContextAsList(),
                QUERY_OCCUPANCY_FORECAST,
                QueryParameter.with(START_DATE, startDate).and(END_DATE, endDate).and(CAUGHT_UP_DATE, dateService.getCaughtUpDate()).and(PROPERTY_IDS, propertyGroupService.getPropertyContextAsList()).parameters(),
                new OccupancyDateRevenueOccupancyDtoRowMapper());

        for (OccupancyDateRevenueOccupancyDto dto : results) {
            result.put(dto.getOccupancyDate(), dto.getADR());
        }

        return result;
    }

    @SuppressWarnings("unchecked")
    public Map<Date, BigDecimal> getAccomClassADRHeatMapData(final int accomClassId) {
        Map<Date, BigDecimal> result = new HashMap<Date, BigDecimal>();

        Query q = crudService.getEntityManager().createNativeQuery(QUERY_ACCOM_REVENUE);
        q.setParameter(CAUGHT_UP_DATE, dateService.getCaughtUpDate());
        q.setParameter(START_DATE, dateService.getHeatMapDisplayWindowStartDate());
        q.setParameter(END_DATE, dateService.getHeatMapDisplayWindowEndDate());
        q.setParameter(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
        q.setParameter(ACCOM_CLASS_ID, accomClassId);
        q.setParameter(IS_PHYSICAL_CAPACITY_ENABLED, 0);
        List<Object[]> resultList = q.getResultList();
        for (Object[] row : resultList) {
            result.put((Date) row[0], (BigDecimal) row[2]);
        }

        return result;
    }

    public Map<Date, BigDecimal> getHotelREVPARHeatMapData() {

        Date startDate = dateService.getHeatMapDisplayWindowStartDate();
        Date endDate = dateService.getHeatMapDisplayWindowEndDate();

        return getHotelREVPARHeatMapData(startDate, endDate);
    }

    public Map<Date, BigDecimal> getHotelREVPARHeatMapData(Date startDate, Date endDate) {
        Map<Date, BigDecimal> result = new HashMap<Date, BigDecimal>();

        List<OccupancyDateRevenueOccupancyDto> results = multiPropertyCrudService.findByNativeQueryUnionAcrossProperties(
                propertyGroupService.getPropertyContextAsList(),
                QUERY_OCCUPANCY_FORECAST,
                QueryParameter.with(START_DATE, startDate).and(END_DATE, endDate).and(CAUGHT_UP_DATE, dateService.getCaughtUpDate()).and(PROPERTY_IDS, propertyGroupService.getPropertyContextAsList()).parameters(),
                new OccupancyDateRevenueOccupancyDtoRowMapper());

        boolean physicalCapacityConsideration = configService.isEnablePhysicalCapacityConsideration();
        for (OccupancyDateRevenueOccupancyDto dto : results) {
            result.put(dto.getOccupancyDate(), dto.getREVPAR(physicalCapacityConsideration));
        }

        return result;
    }

    @SuppressWarnings("unchecked")
    public Map<Date, BigDecimal> getAccomClassREVPARHeatMapData(final int accomClassId) {
        Map<Date, BigDecimal> result = new HashMap<Date, BigDecimal>();
        int isPhysicalCapacityEnabled = configService.isEnablePhysicalCapacityConsideration() ? 1 : 0;

        Query q = crudService.getEntityManager().createNativeQuery(QUERY_ACCOM_REVENUE);
        q.setParameter(CAUGHT_UP_DATE, dateService.getCaughtUpDate());
        q.setParameter(START_DATE, dateService.getHeatMapDisplayWindowStartDate());
        q.setParameter(END_DATE, dateService.getHeatMapDisplayWindowEndDate());
        q.setParameter(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
        q.setParameter(ACCOM_CLASS_ID, accomClassId);
        q.setParameter(IS_PHYSICAL_CAPACITY_ENABLED, isPhysicalCapacityEnabled);
        List<Object[]> resultList = q.getResultList();
        for (Object[] row : resultList) {
            result.put((Date) row[0], (BigDecimal) row[3]);
        }

        return result;
    }

    public Map<Date, BigDecimal> getOverbookingHeatMapData() {
        Query q = crudService.getEntityManager().createNativeQuery(QUERY_OVERBOOKING);
        q.setParameter(CAUGHT_UP_DATE, dateService.getCaughtUpDate());
        return executeNativeQuery(q);
    }

    public Map<Date, BigDecimal> getOverbookingHeatMapData(Date startDate, Date endDate, boolean isOvbkOptimizationEnabled, Date caughtUpDate) {
        String query = isOvbkOptimizationEnabled ? QUERY_OVERBOOKING_IN_RANGE : QUERY_OVERBOOKING_ENABLE_PHYSICAL_CAPACITY;
        Query q = crudService.getEntityManager().createNativeQuery(query);
        if(isOvbkOptimizationEnabled) {
            q.setParameter(END_DATE, endDate);
            q.setParameter(CAUGHT_UP_DATE, caughtUpDate);
        }
        else {
            q.setParameter(CAUGHT_UP_DATE, dateService.getCaughtUpDate());
        }
        return executeNativeQuery(q, startDate, endDate);
    }

    private Map<Date, BigDecimal> executeNativeQuery(Query q) {
        Date startDate = dateService.getHeatMapDisplayWindowStartDate();
        Date endDate = dateService.getHeatMapDisplayWindowEndDate();

        return executeNativeQuery(q, startDate, endDate);
    }

    @SuppressWarnings("unchecked")
    private Map<Date, BigDecimal> executeNativeQuery(Query q, Date startDate, Date endDate) {

        Map<Date, BigDecimal> result = new HashMap<Date, BigDecimal>();
        q.setParameter(START_DATE, startDate);
        q.setParameter(END_DATE, endDate);
        q.setParameter(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
        boolean enablePhysicalCapacityConsideration = configService.isEnablePhysicalCapacityConsideration();
        List<Object[]> resultList = q.getResultList();
        for (Object[] row : resultList) {
            if (enablePhysicalCapacityConsideration) {
                BigDecimal overbookingDecision = BigDecimal.ZERO;
                BigDecimal totalAccomCapacity = BigDecimal.ZERO;
                if (null != row[2]) {
                    overbookingDecision = (BigDecimal) row[2];
                }
                if (null != row[3]) {
                    totalAccomCapacity = (BigDecimal) row[3];
                }
                if (totalAccomCapacity.intValue() == 0) {
                    result.put((Date) row[0], BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                } else {
                    BigDecimal overbookingPercentage = (overbookingDecision.add(totalAccomCapacity)).divide(totalAccomCapacity, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100).setScale(2));
                    result.put((Date) row[0], overbookingPercentage);
                }
            } else {
                result.put((Date) row[0], ((BigDecimal) row[1]).setScale(2, RoundingMode.HALF_UP));
            }
        }
        return result;
    }

    private Map<Date, BigDecimal> executeNativeQueryForPeakDemand(Query q, Date startDate, Date endDate) {

        Map<Date, BigDecimal> result = new HashMap<Date, BigDecimal>();
        q.setParameter(START_DATE, startDate);
        q.setParameter(END_DATE, endDate);
        q.setParameter(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
        return processPeakDemandHeatMapData(q, result);
    }

    private Map<Date, BigDecimal> executeNativeQueryForPeakDemand(Query q, Date startDate, Date endDate,
                                                                  Integer inventoryGroupId) {

        Map<Date, BigDecimal> result = new HashMap<Date, BigDecimal>();
        q.setParameter(START_DATE, startDate);
        q.setParameter(END_DATE, endDate);
        q.setParameter(INVENTORY_GROUP_ID, inventoryGroupId);
        return processPeakDemandHeatMapData(q, result);
    }

    private Map<Date, BigDecimal> processPeakDemandHeatMapData(Query q, Map<Date, BigDecimal> result) {
        boolean enablePhysicalCapacityConsideration = configService.isEnablePhysicalCapacityConsideration();
        List<Object[]> resultList = q.getResultList();
        for (Object[] row : resultList) {
            if (enablePhysicalCapacityConsideration) {
                result.put((Date) row[0], (BigDecimal) row[1]);
            } else {
                result.put((Date) row[0], (BigDecimal) row[2]);
            }
        }
        return result;
    }


    private int getDefaultLOS() {
        if (configService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value()).equals(BAR_DECISION_VALUE_RATEOFDAY)) {
            return -1;
        } else {
            return 1;
        }
    }

    private BigDecimal getRatePlanValue(int accomClassId, final Date caughtUpDate, RateUnqualified ratePlan) {
        return rateDeterminator.getMaxRateForRateScheduleAndDate(accomClassId, caughtUpDate, ratePlan.getId());
    }

    public Map<AccomClass, List<String>> getOrderedRatePlanByAccomClass() {
        HashMap<AccomClass, List<String>> ratePlanAccomClassMap = new HashMap<>();
        for (AccomClass accomClass : accommodationService.getAccomClassesByRankOrder()) {
            ratePlanAccomClassMap.put(accomClass, getOrderedRatePlanNames(accomClass.getId()));
        }
        return ratePlanAccomClassMap;
    }

    public Integer getConfigTypeIdByConfigType(String configType) {
        return crudService.findByNamedQuerySingleResult(HeatMapRangeAndColorsConfigType.GET_ID_BY_TYPE,
                QueryParameter.with("configType", configType).parameters());
    }

    public boolean saveOrUpdateHeatMapRangeAndColors(List<HeatMapRangeAndColors> heatMapRangeAndColors) {
        return CollectionUtils.isNotEmpty(crudService.save(heatMapRangeAndColors));
    }

    public boolean saveHeatMapRangeAndColorsConfig(Set<HeatMapRangeAndColors> heatMapRangeAndColors, int typeId) {
        HeatMapRangeAndColorsConfigType heatMapRangeAndColorsConfigType = new HeatMapRangeAndColorsConfigType();
        heatMapRangeAndColorsConfigType.setId(typeId);
        HeatMapRangeAndColorsConfig heatMapRangeAndColorsConfig = new HeatMapRangeAndColorsConfig();
        heatMapRangeAndColorsConfig.setHeatMapRangeAndColorsConfigType(heatMapRangeAndColorsConfigType);
        heatMapRangeAndColorsConfig.setCreateDate(LocalDateTime.now());
        heatMapRangeAndColorsConfig.setCreatedByUserId(Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        heatMapRangeAndColorsConfig.setLastUpdatedDate(LocalDateTime.now());
        heatMapRangeAndColorsConfig.setLastUpdatedByUserId(Integer.parseInt(PacmanWorkContextHelper.getUserId()));
        heatMapRangeAndColorsConfig = crudService.save(heatMapRangeAndColorsConfig);
        boolean isSaved = false;
        if (heatMapRangeAndColorsConfig != null) {
            isSaved = true;
            for (HeatMapRangeAndColors heatMapRangeAndColor : heatMapRangeAndColors) {
                heatMapRangeAndColor.setHeatMapRangeAndColorsConfigId(heatMapRangeAndColorsConfig.getId());
                isSaved = isSaved && (crudService.save(heatMapRangeAndColor) != null);
            }
        }
        return isSaved;
    }

    public List<HeatMapRangeAndColorsConfig> getAllHeatMapRangeAndColorsConfig(List<String> configTypesToLoadRangeAndColorsData) {
        List<HeatMapRangeAndColorsConfig> heatMapRangeAndColorsConfigList = crudService.findAll(HeatMapRangeAndColorsConfig.class);
        HashMap<String, Integer> heatMapRangeAndColorsConfigTypeMap = (HashMap<String, Integer>)
                heatMapRangeAndColorsConfigList.stream()
                        .map(HeatMapRangeAndColorsConfig::getHeatMapRangeAndColorsConfigType)
                        .collect(Collectors.toMap(HeatMapRangeAndColorsConfigType::getConfigType, HeatMapRangeAndColorsConfigType::getId,
                                (existing, replacement) -> existing));
        if (CollectionUtils.isNotEmpty(configTypesToLoadRangeAndColorsData)) {
            initializeRangeAndColors(configTypesToLoadRangeAndColorsData, heatMapRangeAndColorsConfigList,
                    heatMapRangeAndColorsConfigTypeMap);
        }
        return heatMapRangeAndColorsConfigList;
    }

    private void initializeRangeAndColors(List<String> configTypesToLoadRangeAndColorsData,
                                          List<HeatMapRangeAndColorsConfig> heatMapRangeAndColorsConfigList,
                                          HashMap<String, Integer> heatMapRangeAndColorsConfigTypeMap) {
        configTypesToLoadRangeAndColorsData.forEach(configType -> {
            if (heatMapRangeAndColorsConfigTypeMap.keySet().contains(configType)) {
                List<HeatMapRangeAndColorsConfig> rangeAndColorsConfigList = heatMapRangeAndColorsConfigList.stream()
                        .filter(config -> config.getHeatMapRangeAndColorsConfigType().getId() == heatMapRangeAndColorsConfigTypeMap.get(configType))
                        .collect(Collectors.toList());
                rangeAndColorsConfigList.forEach(config -> {
                    Hibernate.initialize(config.getHeatMapRangeAndColors());
                    config.setHeatMapRangeAndColorsInitialized(true);
                });
            }
        });
    }


    public HeatMapRangeAndColorsConfigType getHeatMapRangeAndColorsConfigType(String configType) {
        return (HeatMapRangeAndColorsConfigType) crudService.findByNamedQuerySingleResult(HeatMapRangeAndColorsConfigType.BY_CONFIG_TYPE,
                QueryParameter.with("configType", configType).parameters());
    }

    public List<HeatMapRangeAndColors> getHeatMapRangeAndColorsByConfigId(Integer id) {
        return crudService.findByNamedQuery(HeatMapRangeAndColors.BY_HEAT_MAP_CONFIG_ID,
                QueryParameter.with("heatMapConfigId", id).parameters());
    }

    public void saveHeatmapRangeAndColorsForSeason(Set<HeatMapRangeAndColors> heatMapRangeAndColorsList) {
        crudService.save(heatMapRangeAndColorsList);
    }

    public HeatMapRangeAndColorsConfig saveHeatMapRangeandColorsConfig(HeatMapRangeAndColorsConfig heatMapRangeAndColorsConfig) {
        return crudService.save(heatMapRangeAndColorsConfig);
    }

    public int removeSeasonalData(int id) {
        int deletedcount = 0;
        deletedcount = crudService.executeUpdateByNamedQuery(HeatMapRangeAndColors.DELETE_BY_HEAT_MAP_CONFIG_ID,
                QueryParameter.with("heatMapRangeAndColorsConfigId", id).parameters());
        crudService.executeUpdateByNamedQuery(HeatMapRangeAndColorsConfig.DELETE_BY_ID,
                QueryParameter.with("id", id).parameters());
        return deletedcount;

    }

    public List<HeatMapRangeAndColorsConfig> getSeasonHeatMapRangeAndColorsConfig(LocalDate startDate, LocalDate endDate, boolean fiscalCalendarEnabled) {
        String currentMonthValue = "";
        String nextMonthValue = "";
        List<HeatMapRangeAndColorsConfig> rangeAndColorsConfigList = null;
        if (fiscalCalendarEnabled) {
            currentMonthValue = startDate.toString();
            nextMonthValue = endDate.toString();
            rangeAndColorsConfigList = crudService.findByNamedQuery(HeatMapRangeAndColorsConfig.FIND_BY_FISCAL_DATE_RANGE,
                    QueryParameter.with(START_DATE, currentMonthValue).and(END_DATE, nextMonthValue).parameters());
        } else {
            currentMonthValue = LocalDate.parse(startDate.toString()).dayOfMonth().withMinimumValue().toString();
            nextMonthValue = LocalDate.parse(endDate.toString()).dayOfMonth().withMaximumValue().toString();
            rangeAndColorsConfigList = crudService.findByNamedQuery(HeatMapRangeAndColorsConfig.FIND_BY_DATE_RANGE,
                    QueryParameter.with(START_DATE, currentMonthValue).and(END_DATE, nextMonthValue).parameters());
        }
        rangeAndColorsConfigList.forEach(config -> {
            Hibernate.initialize(config.getHeatMapRangeAndColors());
            config.setHeatMapRangeAndColorsInitialized(true);
        });
        return rangeAndColorsConfigList;
    }

    public Map<HeatMapRangeAndColorsConfig, List<HeatMapRangeAndColors>> getHeatMapConfigWithRangeAndColors(Date startDate, Date endDate, boolean fiscalCalendarEnabled) {
        Query query = fiscalCalendarEnabled ? crudService.getEntityManager().createNativeQuery(QUERY_GET_CONFIG_BETWEEN_FISCAL_DATES) :
                crudService.getEntityManager().createNativeQuery(QUERY_GET_CONFIG_BETWEEN_DATES);
        query.setParameter(START_DATE, startDate);
        query.setParameter(END_DATE, endDate);
        query.setParameter(CUSTOMIZE_TYPE, CUSTOMIZE);
        query.setParameter(DEFAULT_TYPE, DEFAULT);
        List<Object[]> result = query.getResultList();
        Map<Integer, HeatMapRangeAndColorsConfig> configIdMap = new HashMap<>();
        Map<Integer, HeatMapRangeAndColorsConfigType> configTypeIdMap = new HashMap<>();
        Map<HeatMapRangeAndColorsConfig, List<HeatMapRangeAndColors>> map = new LinkedHashMap<>();
        result.forEach(row -> {
            List<HeatMapRangeAndColors> heatMapRangeAndColorsList;
            if (!configIdMap.keySet().contains(row[0])) {
                HeatMapRangeAndColorsConfig config = new HeatMapRangeAndColorsConfig();
                config.setId((Integer) row[0]);
                if (row[1] != null) {
                    config.setStartDate(DateUtil.formatDate(DateUtil.toDate(String.valueOf(row[1])), "dd-MMM-yyyy"));
                }
                if (row[2] != null) {
                    config.setEndDate(DateUtil.formatDate(DateUtil.toDate(String.valueOf(row[2])), "dd-MMM-yyyy"));
                }
                setFiscalStartAndEndDate(fiscalCalendarEnabled, row, config);
                if (!configTypeIdMap.containsKey(row[3])) {
                    HeatMapRangeAndColorsConfigType configType = new HeatMapRangeAndColorsConfigType();
                    configType.setId((Integer) row[3]);
                    configType.setConfigType(String.valueOf(row[4]));
                    configTypeIdMap.put(configType.getId(), configType);
                    config.setHeatMapRangeAndColorsConfigType(configType);
                } else {
                    config.setHeatMapRangeAndColorsConfigType(configTypeIdMap.get(row[3]));
                }

                heatMapRangeAndColorsList = new ArrayList<>();
                configIdMap.put((Integer) row[0], config);
                map.put(config, heatMapRangeAndColorsList);
            } else {
                heatMapRangeAndColorsList = map.get(configIdMap.get(row[0]));
            }

            HeatMapRangeAndColors heatMapRangeAndColors = new HeatMapRangeAndColors();
            heatMapRangeAndColors.setRangeFrom(((Byte) row[5]).intValue());
            heatMapRangeAndColors.setRangeTo(((Byte) row[6]).intValue());
            heatMapRangeAndColors.setOccupancyForecastColorCode(String.valueOf(row[7]));
            heatMapRangeAndColors.setPeakDemandColorCode(String.valueOf(row[8]));
            heatMapRangeAndColors.setId((Integer) row[9]);
            heatMapRangeAndColorsList.add(heatMapRangeAndColors);
        });
        return map;
    }

    private void setFiscalStartAndEndDate(boolean fiscalCalendarEnabled, Object[] row, HeatMapRangeAndColorsConfig config) {
        if (fiscalCalendarEnabled && row[10] != null && row[11] != null) {
            config.setFiscalStartDate(DateUtil.formatDate(DateUtil.toDate(String.valueOf(row[10])), "dd-MMM-yyyy"));
            config.setFiscalEndDate(DateUtil.formatDate(DateUtil.toDate(String.valueOf(row[11])), "dd-MMM-yyyy"));
        }
    }

    public Map<Date, BigDecimal> getOccupancyForecastHeatMapDataByProduct(Date startDate, Date endDate, Integer productId) {
        String query = QUERY_OCCUPANCY_FORECAST_FOR_PRODUCT;
        if (configService.getBooleanParameterValue(PreProductionConfigParamName.USE_MKT_ACCOM_ACTIVITY_FOR_PAST_DATA)) {
            query = QUERY_OCCUPANCY_FORECAST_FOR_PRODUCT_USE_MKT_ACCOM_ACTIVITY;
        }
        return getOccupancyForecastForHeatMap(startDate, endDate, productId, query, PRODUCT_ID);
    }

    public Map<Date, BigDecimal> getOccupancyForecastForHeatMap(Date startDate, Date endDate, Integer id, String queryOccupancyForecastForProduct,
                                                         String Source) {
        List<OccupancyDateRevenueOccupancyDto> results = crudService.findByNativeQuery(
                queryOccupancyForecastForProduct,
                QueryParameter.with(START_DATE, startDate)
                        .and(END_DATE, endDate)
                        .and(CAUGHT_UP_DATE, dateService.getCaughtUpDate())
                        .and(Source, id).parameters(),
                new OccupancyDateRevenueOccupancyDtoRowMapper());
        if (null == results || results.isEmpty()) {
            return Collections.EMPTY_MAP;
        }
        return processOccupancyForecastHeatMapData(results);
    }
}