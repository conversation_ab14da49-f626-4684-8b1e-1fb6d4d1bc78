package com.ideas.tetris.pacman.services.componentrooms.services;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.*;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.componentrooms.dto.CROrphanMappingDto;
import com.ideas.tetris.pacman.services.componentrooms.dto.CRoooNotUpdatedAlertDto;
import com.ideas.tetris.pacman.services.componentrooms.dto.ComponentRoomsConfiguration;
import com.ideas.tetris.pacman.services.componentrooms.entity.*;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.filemetadata.FileMetadataService;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertService;
import com.ideas.tetris.pacman.services.informationmanager.dto.Alert;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrInstanceEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrTypeEntity;
import com.ideas.tetris.pacman.services.marketsegment.entity.YieldCategoryRule;
import com.ideas.tetris.pacman.services.opera.OperaOccupancySummary;
import com.ideas.tetris.pacman.services.opera.PreProcessPseudoRoomTransactionsService;
import com.ideas.tetris.pacman.services.pacebackfill.entity.PaceBackfillLog;
import com.ideas.tetris.pacman.services.pmsmigration.services.PMSRevampNonPaceUpdationService;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.pacman.services.roomtyperecoding.entity.RoomTypeRecodingConfig;
import com.ideas.tetris.pacman.services.roomtyperecoding.services.RoomTypeRecodingService;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.sas.log.SasDbQueryResult;
import com.ideas.tetris.platform.services.sas.log.SasDbToolService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.common.constants.Constants.CONTEXT_KEY;
import static com.ideas.tetris.pacman.services.pacebackfill.BackFillService.ALL;


@Component
@Transactional
public class ComponentRoomService {
    private static final Logger LOGGER = Logger.getLogger(ComponentRoomService.class);
    public static final String SELECTIVE_MKT_SEG_WHERE_CLAUSE = "SELECTIVE_MKT_SEG_WHERE_CLAUSE";
    private static final String NEW_CR_DETECTED = "new.cr.detected";
    public static final String INVALID_CR_CONFIGURATION_DESCRIPTION = "InvalidCRConfiguration.description";
    public static final String ROOM_NUMBERS_REQUIRED_FOR_CR_CONFIG_DESCRIPTION = "RoomNumbersRequiredForCRConfig.description";
    private static final String ALERT_TYPE_NAME = "NewCRFound";
    private static final String CR_OOO_NOT_UPDATED_DESCRIPTION = "followingcrooonotupdated";
    public static final String CR_OOO_NOT_UPDATED_ALERT = "CROOONotUpdated";
    private static final String OOO_ADJUSTED_FOR = "alert.ooo.adjusted";
    private static final String FROM = "alert.from";
    private static final String TO = "alert.to";
    public static final String OCCUPANCY_DATE = "alert.occupancy.date";
    public static final String BLANK_SPACE = " ";
    public static final String START_DATE = "startDate";
    public static final String END_DATE = "endDate";
    public static final String FILE_META_DATA_ID = "fileMetaDataId";
    public static final String PROPERTY_ID = "propertyId";
    public static final String TABLE_HEADER_CR_ACCOM_TYPE = "CR Accom Type";
    public static final String TABLE_HEADER_CP_ACCOM_TYPE = "CP Accom Type";
    public static final String TABLE_HEADER_CP_ACCOM_TYPE_QUANTITY = "CP Accom Type Quantity";
    public static final String TABLE_DATA_NO_ORPHAN_MAPPINGS = "No mappings found.";
    public static final String SELECTIVE_BACKFILL = "Selective Backfill";
    public static final String LAST_UPDATED_DTTM = "lastUpdatedDttm";

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;
    @Autowired
	private PreProcessPseudoRoomTransactionsService pseudoRoomTransactionsService;
    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	private CrudService globalCrudService;
    @Autowired
	private PacmanConfigParamsService configParamsService;
    @Autowired
	private FileMetadataService fileMetadataService;
    @Autowired
    AlertService alertService;
    @Autowired
	private SasDbToolService sasDbToolService;
    @Autowired
    RoomTypeRecodingService roomTypeRecodingService;
    @Autowired
    DateService dateService;
    @Autowired
    AccommodationService accommodationService;
    @Autowired
    ComponentRoomPhysicalRoomResolver componentRoomPhysicalRoomResolver;
    @Autowired
	private AbstractMultiPropertyCrudService multiPropertyCrudService;
    @Autowired
	private PMSRevampNonPaceUpdationService pmsRevampNonPaceUpdationService;

    public static final String SAVE_CR_ACCOM_TYPE_MAPPING_FROM_HISTORY = new StringBuilder()
            .append(" SET IDENTITY_INSERT CR_Accom_Type_Mapping ON ")
            .append(" insert into CR_Accom_Type_Mapping (cr_accom_type_mapping_id,property_id,cr_accom_type_id,cp_accom_type_id,cp_accom_type_quantity) ")
            .append(" select cr_accom_type_mapping_id,property_id,cr_accom_type_id,cp_accom_type_id,cp_accom_type_quantity from CR_Accom_Type_Mapping_History ")
            .append(" where snapshot_dttm = (select min(snapshot_dttm) from CR_Accom_Type_Mapping_History where snapshot_dttm >= :caughtupDate) ")
            .append(" SET IDENTITY_INSERT CR_Accom_Type_Mapping OFF ").toString();

    public static final String SAVE_CR_MAPPING_ROOM_NUMBERS_FROM_HISTORY = new StringBuilder()
            .append(" SET IDENTITY_INSERT CR_Mapping_Room_Numbers ON ")
            .append(" insert into CR_Mapping_Room_Numbers (CR_Mapping_Room_Numbers_ID,property_id,CR_Accom_Type_Mapping_ID,CP_Room_Number) ")
            .append(" select CR_Mapping_Room_Numbers_ID,property_id,CR_Accom_Type_Mapping_ID,CP_Room_Number from CR_Mapping_Room_Numbers_history ")
            .append(" where snapshot_dttm = (select min(snapshot_dttm) from CR_Accom_Type_Mapping_History where snapshot_dttm >= :caughtupDate) ")
            .append(" SET IDENTITY_INSERT CR_Mapping_Room_Numbers OFF").toString();

    private static String GET_ALL_ROOM_NUMBERS = new StringBuilder()
            .append("SELECT DISTINCT(Room_Number) From Individual_Trans where Accom_Type_ID = :accomTypeId AND room_number <> '' AND Room_Number IS NOT NULL")
            .append(" UNION ")
            .append(" SELECT DISTINCT(CP_Room_Number) from CR_Accom_Type_Mapping catm ")
            .append(" INNER JOIN CR_Mapping_Room_Numbers cmrn ON (catm.CR_Accom_Type_Mapping_ID = cmrn.CR_Accom_Type_Mapping_ID) ")
            .append(" WHERE catm.CP_Accom_Type_ID= :accomTypeId ")
            .append(" ORDER by Room_Number ").toString();

    private static final String CROOO_VALUES = "{{crooo}}";
    private static final String POPULATE_CR_OUT_OF_ORDER_ACCOM_ACTIVITY = new StringBuilder()
            .append("CREATE TABLE #crooo ")
            .append("  ( ")
            .append("     [property_id]           [INT] NOT NULL, ")
            .append("     [occupancy_dt]          [DATE] NOT NULL, ")
            .append("     [accom_type_id]         [INT] NOT NULL, ")
            .append("     [rooms_not_avail_other] [NUMERIC](18, 0) NOT NULL ")
            .append("  ) ")
            .append("INSERT INTO #crooo ")
            .append("VALUES     ")
            .append(CROOO_VALUES)
            .append("UPDATE aa ")
            .append("SET    aa.[rooms_not_avail_other] = co.[rooms_not_avail_other], ")
            .append("       aa.last_updated_dttm = Getdate() ")
            .append("FROM   accom_activity aa ")
            .append("       INNER JOIN #crooo co ")
            .append("               ON aa.property_id = co.property_id ")
            .append("                  AND aa.occupancy_dt = co.occupancy_dt ")
            .append("                  AND aa.accom_type_id = co.accom_type_id ")
            .toString();
    private static final String POPULATE_CR_OUT_OF_ORDER_ACCOM_ACTIVITY_TEMPLATE = "(%d, '%s', %d, %d),";

    protected static final String UPDATE_MKT_ACCOM_ACTIVITY_ADD_COMPONENT_ROOMS_SOLD_AND_REVENUE_POST_BACKFILL =
                    " DECLARE @File_Metadata_id int\n" +
                    " DECLARE @SnapShot_DTTM datetime\n" +
                    " SET @File_Metadata_id = (select max(file_metadata_id)  from Mkt_Accom_Activity)\n" +
                    " SET @SnapShot_DTTM = (select max(snapshot_dttm) from Mkt_Accom_Activity)\n" +
                    " Merge mkt_Accom_Activity as target \n" +
                    "using \n" +
                    "(\n" +
                    "select part_2.property_id,part_2.Occupancy_DT,part_2.CP_Accom_Type_ID as accom_type_id, @File_Metadata_id as file_metadata_id,@SnapShot_DTTM as snapshot_dttm,part_2.mkt_seg_id,\n" +
                    "\t\tisnull(Rooms_Sold,0) + part_1.CR_Part_Solds new_rooms_sold,\n" +
                    "        isnull(Room_Revenue ,0) + part_2.additional_Room_revenue new_room_revenue,  \n" +
                    "        isnull(food_revenue,0) + part_2.additional_Food_revenue new_food_revenue,  \n" +
                    "        isnull(total_revenue,0) + part_2.additional_Total_revenue  new_total_revenue\n" +
                    "            FROM   \n" +
                    "\t\t\t(  \n" +
                    "                select property_id,Occupancy_DT,Mkt_Seg_ID, temp_cr_mapping.CP_Accom_Type_ID , sum((Room_Revenue * CP_Accom_Type_Quantity) /multiplier) additional_Room_revenue,  \n" +
                    "                sum((Food_Revenue * CP_Accom_Type_Quantity) /multiplier) additional_Food_revenue, sum((Total_Revenue * CP_Accom_Type_Quantity) /multiplier) additional_Total_revenue  \n" +
                    "                from  \n" +
                    "                (  \n" +
                    "                SELECT property_id,maa.Occupancy_DT, cratm.CR_Accom_Type_ID, maa.Mkt_Seg_ID, maa.Room_Revenue, maa.Food_Revenue, maa.Total_Revenue,SUM(CP_Accom_Type_Quantity) AS multiplier   \n" +
                    "                FROM Mkt_Accom_Activity maa  \n" +
                    "                JOIN Temp_CR_Accom_Type_Mapping cratm   \n" +
                    "                ON maa.Accom_Type_ID = cratm.CR_Accom_Type_ID   \n" +
                    "                WHERE Occupancy_DT BETWEEN :startDt AND :endDt \n" +
                    "                GROUP BY maa.Occupancy_DT, maa.Mkt_Seg_ID, cratm.CR_Accom_Type_ID, maa.Room_Revenue ,  maa.Food_Revenue, maa.Total_Revenue ,property_id\n" +
                    "                ) temp  \n" +
                    "                inner join Temp_CR_Accom_Type_Mapping temp_cr_mapping  \n" +
                    "                on temp.CR_Accom_Type_ID = temp_cr_mapping.CR_Accom_Type_ID  \n" +
                    "                group by property_id,Occupancy_DT,Mkt_Seg_ID, CP_Accom_Type_ID   \n" +
                    "            )part_2  \n" +
                    "            INNER JOIN  \n" +
                    "            (  \n" +
                    "                SELECT maa.Occupancy_DT, maa.Mkt_Seg_ID, cratm.CP_Accom_Type_ID,  \n" +
                    "                    SUM(CP_Accom_Type_Quantity * maa.rooms_sold) AS CR_Part_Solds  \n" +
                    "                FROM Mkt_Accom_Activity maa  \n" +
                    "                JOIN Temp_CR_Accom_Type_Mapping cratm  \n" +
                    "                ON maa.Accom_Type_ID = cratm.CR_Accom_Type_ID  \n" +
                    "                WHERE maa.Rooms_Sold > 0 and Occupancy_DT BETWEEN :startDt AND :endDt \n" +
                    "                GROUP BY maa.Occupancy_DT, maa.Mkt_Seg_ID, cratm.CP_Accom_Type_ID  \n" +
                    "            ) part_1 \n" +
                    "            ON part_1.Occupancy_DT = part_2.Occupancy_DT  \n" +
                    "            AND part_1.Mkt_Seg_ID = part_2.Mkt_Seg_ID  \n" +
                    "            AND part_1.CP_Accom_Type_ID = part_2.CP_Accom_Type_ID  \n" +
                    "            SELECTIVE_MKT_SEG_WHERE_CLAUSE \n" +
                    "\n" +
                    "\t\t\tLEFT JOIN Mkt_Accom_Activity maa\n" +
                    "            ON part_2.Occupancy_DT = maa.Occupancy_DT   \n" +
                    "            AND part_2.CP_Accom_Type_ID = maa.Accom_Type_ID   \n" +
                    "            AND part_2.Mkt_Seg_ID = maa.Mkt_Seg_ID  \n" +
                    "            SELECTIVE_MKT_SEG_WHERE_CLAUSE \n" +
                    "            WHERE maa.Occupancy_DT BETWEEN :startDt AND :endDt \n" +
                    ")\n" +
                    "as source\n" +
                    "on target.Occupancy_DT = source.Occupancy_DT\n" +
                    "and target.accom_type_id = source.accom_type_id\n" +
                    "and target.Mkt_Seg_ID = source.Mkt_Seg_ID\n" +
                    "when not matched then\n" +
                    "\tinsert (property_id,occupancy_dt,snapshot_dttm,mkt_seg_id,accom_type_id,rooms_sold,arrivals,departures, cancellations,no_shows,room_revenue,food_revenue,total_revenue,file_metadata_id,last_updated_dttm,createDate,pseudo_room_revenue,total_profit,no_show_revenue)\n" +
                    "\tvalues (source.property_id,source.occupancy_dt,source.snapshot_dttm,source.mkt_seg_id,source.accom_type_id,source.new_rooms_sold,0,0,0,0,source.new_room_revenue, source.new_food_revenue, source.new_total_revenue, source.file_metadata_id,\n" +
                    "\tgetdate(),getdate(),0,null,0)\n" +
                    "when matched then\n" +
                    "\tupdate set rooms_sold = source.new_rooms_sold, room_revenue = source.new_room_revenue, food_revenue = source.new_food_revenue, total_revenue = source.new_total_revenue;";
    protected static final String UPDATE_MKT_ACCOM_ACTIVITY_ADD_COMPONENT_ROOMS_SOLD = "" +
            " UPDATE maa " +
            " SET Rooms_Sold = Rooms_Sold + part.CR_Part_Solds " +
            " FROM Mkt_Accom_Activity maa " +
            " JOIN ( " +
            "     SELECT maa.Occupancy_DT, maa.Mkt_Seg_ID, cratm.CP_Accom_Type_ID, " +
            "         SUM(CP_Accom_Type_Quantity * maa.rooms_sold) AS CR_Part_Solds " +
            "     FROM Mkt_Accom_Activity maa " +
            "     JOIN Temp_CR_Accom_Type_Mapping cratm " +
            "     ON maa.Accom_Type_ID = cratm.CR_Accom_Type_ID " +
            "     WHERE maa.Rooms_Sold > 0 " +
            "     AND maa.Mkt_Seg_ID NOT IN (SELECT DISTINCT ms.Mkt_Seg_ID FROM Mkt_Seg ms \n" +
            "                                JOIN Hotel_Mkt_Accom_Activity hmaa" +
            "                                ON ms.Mkt_Seg_Code = hmaa.Mkt_Seg_Code)" +
            "     GROUP BY maa.Occupancy_DT, maa.Mkt_Seg_ID, cratm.CP_Accom_Type_ID " +
            " ) part " +
            " ON part.Occupancy_DT = maa.Occupancy_DT " +
            " AND part.Mkt_Seg_ID = maa.Mkt_Seg_ID " +
            " SELECTIVE_MKT_SEG_WHERE_CLAUSE  " +
            " AND part.CP_Accom_Type_ID = maa.Accom_Type_ID " +
            " WHERE maa.Occupancy_DT BETWEEN :startDt AND :endDt ; ";
    protected static final String UPDATE_ACCOM_ACTIVITY_ADD_COMPONENT_ROOMS_SOLD = "" +
            " DECLARE @File_Metadata_id int\n" +
            " DECLARE @SnapShot_DTTM datetime\n" +
            " SET @File_Metadata_id = (select max(file_metadata_id)  from Accom_Activity)\n" +
            " SET @SnapShot_DTTM = (select max(snapshot_dttm) from Accom_Activity)" +
            " Merge Accom_Activity as target \n" +
            "using \n" +
            "(\n" +
            "select part_2.property_id,part_2.Occupancy_DT,part_2.CP_Accom_Type_ID as accom_type_id,@File_Metadata_id as file_metadata_id,@SnapShot_DTTM as snapshot_dttm,at.accom_type_capacity,\n" +
            "        isnull(Rooms_Sold,0) + part_1.CR_Part_Solds new_rooms_sold,\n" +
            "        isnull(Room_Revenue ,0) + part_2.additional_Room_revenue new_room_revenue,  \n" +
            "        isnull(food_revenue,0) + part_2.additional_Food_revenue new_food_revenue,  \n" +
            "        isnull(total_revenue,0) + part_2.additional_Total_revenue  new_total_revenue\n" +
            "            FROM \n" +
            "            ( \n" +
            "\t\t\tselect property_id,Occupancy_DT, temp_cr_mapping.CP_Accom_Type_ID,\n" +
            "             sum((Room_Revenue * CP_Accom_Type_Quantity) /multiplier) additional_Room_revenue, \n" +
            "             sum((Food_Revenue * CP_Accom_Type_Quantity) /multiplier) additional_Food_revenue, sum((Total_Revenue * CP_Accom_Type_Quantity) /multiplier) additional_Total_revenue\n" +
            "             from ( \n" +
            "              SELECT aa.property_id,aa.Occupancy_DT, cratm.CR_Accom_Type_ID, aa.Room_Revenue, aa.Food_Revenue,aa.Total_Revenue,SUM(CP_Accom_Type_Quantity) AS multiplier  \n" +
            "              FROM Accom_Activity aa   \n" +
            "              JOIN Temp_CR_Accom_Type_Mapping cratm   \n" +
            "              ON aa.Accom_Type_ID = cratm.CR_Accom_Type_ID  \n" +
            "              WHERE Occupancy_DT BETWEEN :startDt AND :endDt  \n" +
            "              GROUP BY aa.Occupancy_DT, cratm.CR_Accom_Type_ID,aa.Room_Revenue,  aa.Food_Revenue, aa.Total_Revenue,aa.property_id\n" +
            "             ) temp \n" +
            "             inner join Temp_CR_Accom_Type_Mapping temp_cr_mapping  \n" +
            "             on temp.CR_Accom_Type_ID = temp_cr_mapping.CR_Accom_Type_ID \n" +
            "             GROUP BY Occupancy_DT, CP_Accom_Type_ID, property_id\n" +
            "            )part_2 \n" +
            " \n" +
            "            INNER JOIN  \n" +
            "            (  \n" +
            "                SELECT aa.Occupancy_DT, cratm.CP_Accom_Type_ID,  \n" +
            "                    SUM(CP_Accom_Type_Quantity * aa.rooms_sold) AS CR_Part_Solds  \n" +
            "                FROM Accom_Activity aa   \n" +
            "                inner JOIN Temp_CR_Accom_Type_Mapping cratm   \n" +
            "                ON aa.Accom_Type_ID = cratm.CR_Accom_Type_ID  \n" +
            "                WHERE aa.Rooms_Sold > 0 and Occupancy_DT BETWEEN :startDt AND :endDt  \n" +
            "                GROUP BY aa.Occupancy_DT, cratm.CP_Accom_Type_ID  \n" +
            "            ) part_1 \n" +
            "            ON part_1.Occupancy_DT = part_2.Occupancy_DT  \n" +
            "            AND part_1.CP_Accom_Type_ID = part_2.cp_Accom_Type_ID  \n" +
            "\n" +
            "            LEFT JOIN Accom_Activity aa   \n" +
            "\t\t\tON part_2.Occupancy_DT = aa.Occupancy_DT  \n" +
            "            AND part_2.CP_Accom_Type_ID = aa.Accom_Type_ID \n" +
            "\n" +
            "\t\t\tINNER JOIN Accom_type at on\n" +
            "\t\t\tpart_2.CP_Accom_Type_ID = at.accom_type_id\n" +
            "\n" +
            "            WHERE part_2.Occupancy_DT  BETWEEN :startDt AND :endDt \n" +
            ")\n" +
            "as source\n" +
            "on target.Occupancy_DT = source.Occupancy_DT\n" +
            "and target.accom_type_id = source.accom_type_id\n" +
            "when not matched then\n" +
            "\tinsert (property_id,occupancy_dt,snapshot_dttm,accom_type_id,accom_capacity,rooms_sold,rooms_not_avail_maint, rooms_not_avail_other,arrivals,departures,\n" +
            "\t\tcancellations,no_shows,room_revenue,food_revenue,total_revenue,file_metadata_id,last_updated_dttm,total_profit)\n" +
            "\tvalues (source.property_id,source.occupancy_dt,source.snapshot_dttm,source.accom_type_id,accom_type_capacity,source.new_rooms_sold,0,0,0,0,0,0,source.new_room_revenue,\n" +
            "\t\t source.new_food_revenue, source.new_total_revenue, source.file_metadata_id,getdate(),null)\n" +
            "when matched then\n" +
            "\tupdate set rooms_sold = source.new_rooms_sold, room_revenue = source.new_room_revenue, food_revenue = source.new_food_revenue, total_revenue = source.new_total_revenue; ";
    protected static final String FIND_MIN_MAX_BUSINESS_DAY_END_DT = "" +
            " select min(Business_Day_End_DT) mindate, max(Business_Day_End_DT) maxdate" +
            " from ( select distinct top 730  Business_Day_End_DT from PACE_Accom_Activity order by Business_Day_End_DT desc)aa";
    protected static final String UPDATE_PACE_ACCOM_ACTIVITY_ADD_COMPONENT_ROOMS_SOLD =
                    " DECLARE @File_Metadata_id int\n" +
                    " DECLARE @SnapShot_DTTM datetime\n" +
                    " SET @File_Metadata_id = (select max(file_metadata_id)  from Accom_Activity)\n" +
                    " SET @SnapShot_DTTM = (select max(snapshot_dttm) from Accom_Activity)\n" +
                    " Merge Pace_Accom_Activity as target \n" +
                    "using \n" +
                    "(\n" +
                    "\tselect part_2.property_id,part_2.Business_Day_End_DT, part_2.Occupancy_DT,part_2.CP_Accom_Type_ID as accom_type_id,@File_Metadata_id as file_metadata_id,@SnapShot_DTTM as snapshot_dttm,\n" +
                    "\t\t at.accom_type_capacity,\n" +
                    "\t\tisnull(Rooms_Sold,0) + part_1.CR_Part_Solds new_rooms_sold,\n" +
                    "        isnull(Room_Revenue ,0) + part_2.additional_Room_revenue new_room_revenue,  \n" +
                    "        isnull(food_revenue,0) + part_2.additional_Food_revenue new_food_revenue,  \n" +
                    "        isnull(total_revenue,0) + part_2.additional_Total_revenue  new_total_revenue\n" +
                    "         FROM  (  \n" +
                    "             select property_id,Occupancy_DT, temp_cr_mapping.CP_Accom_Type_ID, Business_Day_End_DT, sum((Room_Revenue * CP_Accom_Type_Quantity) /multiplier) additional_Room_revenue, \n" +
                    "             sum((Food_Revenue * CP_Accom_Type_Quantity) /multiplier) additional_Food_revenue, sum((Total_Revenue * CP_Accom_Type_Quantity) /multiplier) additional_Total_revenue\n" +
                    "              from (  \n" +
                    "               SELECT paa.Property_ID,paa.Occupancy_DT, cratm.CR_Accom_Type_ID,  paa.Room_Revenue, paa.Food_Revenue,paa.Total_Revenue,  paa.Business_Day_End_DT, SUM(CP_Accom_Type_Quantity) AS multiplier   \n" +
                    "               FROM Pace_Accom_Activity paa  \n" +
                    "               JOIN Temp_CR_Accom_Type_Mapping cratm  \n" +
                    "               ON paa.Accom_Type_ID = cratm.CR_Accom_Type_ID   \n" +
                    "               WHERE  Occupancy_DT BETWEEN :startDt AND :endDt    \n" +
                    "               and paa.Business_Day_End_DT <= :maxBusinessDayEndDt   \n" +
                    "               GROUP BY paa.Property_ID,paa.Occupancy_DT, cratm.CR_Accom_Type_ID,paa.Room_Revenue,paa.Food_Revenue,paa.Total_Revenue,  paa.Business_Day_End_DT \n" +
                    "              ) temp  \n" +
                    "              inner join Temp_CR_Accom_Type_Mapping temp_cr_mapping  \n" +
                    "              on temp.CR_Accom_Type_ID = temp_cr_mapping.CR_Accom_Type_ID  \n" +
                    "             GROUP BY property_id,Occupancy_DT, CP_Accom_Type_ID, Business_Day_End_DT \n" +
                    "             )part_2\n" +
                    "\n" +
                    "\t\t\t INNER JOIN   \n" +
                    "             (   \n" +
                    "              SELECT paa.Occupancy_DT, cratm.CP_Accom_Type_ID, paa.Business_Day_End_DT,  \n" +
                    "               SUM(CP_Accom_Type_Quantity * paa.rooms_sold) AS CR_Part_Solds   \n" +
                    "              FROM Pace_Accom_Activity paa   \n" +
                    "              JOIN Temp_CR_Accom_Type_Mapping cratm   \n" +
                    "              ON paa.Accom_Type_ID = cratm.CR_Accom_Type_ID   \n" +
                    "              WHERE paa.Rooms_Sold > 0 and Occupancy_DT BETWEEN :startDt AND :endDt    \n" +
                    "              and paa.Business_Day_End_DT <= :maxBusinessDayEndDt  \n" +
                    "              GROUP BY paa.Occupancy_DT, cratm.CP_Accom_Type_ID, paa.Business_Day_End_DT   \n" +
                    "             )part_1   \n" +
                    "            ON part_2.Occupancy_DT = part_1.Occupancy_DT   \n" +
                    "            AND part_2.CP_Accom_Type_ID = part_1.CP_Accom_Type_ID   \n" +
                    "            and part_2.Business_Day_End_DT = part_1.Business_Day_End_DT  \n" +
                    "\n" +
                    "\t\t\tLEFT JOIN Pace_Accom_Activity paa   \n" +
                    "            ON part_2.Occupancy_DT = paa.Occupancy_DT   \n" +
                    "            AND part_2.CP_Accom_Type_ID = paa.Accom_Type_ID   \n" +
                    "            and part_2.Business_Day_End_DT = paa.Business_Day_End_DT  \n" +
                    "\n" +
                    "\t\t\tINNER JOIN Accom_type at on\n" +
                    "\t\t\tpart_2.CP_Accom_Type_ID = at.accom_type_id\n" +
                    "\n" +
                    "            WHERE part_2.Occupancy_DT  BETWEEN :startDt AND :endDt \n" +
                    "            and part_2.Business_Day_End_DT  <= :maxBusinessDayEndDt\n" +
                    "              PRESERVE_NON_SELECTIVE_PACE_FROM_BACKFILL_CLAUSE  \n" +
                    ")\n" +
                    "as source\n" +
                    "on target.Business_Day_End_DT = source.Business_Day_End_DT\n" +
                    "and target.Occupancy_DT = source.Occupancy_DT\n" +
                    "and target.accom_type_id = source.accom_type_id\n" +
                    "when not matched then\n" +
                    "\tinsert (property_id,occupancy_dt,snapshot_dttm,business_day_end_dt,accom_type_id,accom_capacity,rooms_sold,rooms_not_avail_maint, rooms_not_avail_other,arrivals,departures,\n" +
                    "\t\tcancellations,no_shows,room_revenue,food_revenue,total_revenue,file_metadata_id,month_id,year_id,last_updated_dttm,total_profit)\n" +
                    "\tvalues (source.property_id,source.occupancy_dt,source.snapshot_dttm,source.business_day_end_dt,source.accom_type_id,accom_type_capacity,source.new_rooms_sold,0,0,0,0,0,0,source.new_room_revenue,\n" +
                    "\t\t source.new_food_revenue, source.new_total_revenue, source.file_metadata_id,null,null,getdate(),null)\n" +
                    "when matched then\n" +
                    "\tupdate set rooms_sold = source.new_rooms_sold, room_revenue = source.new_room_revenue, food_revenue = source.new_food_revenue, total_revenue = source.new_total_revenue; ";
    public static final int ROOM_SOLDS_STARTS_COLUMN_SEQUENCE_ACCOM_TYPE = 3;
    public static final int ROOM_SOLDS_STARTS_COLUMN_SEQUENCE_MKT_ACCOM_TYPE = 4;

    protected static final String PRESERVE_NON_SELECTIVE_PACE_FROM_BACKFILL = " and paa.Last_Updated_Dttm >= :lastUpdatedDttm";
    protected static final String PRESERVE_NON_SELECTIVE_PACE_FROM_BACKFILL_CLAUSE = "PRESERVE_NON_SELECTIVE_PACE_FROM_BACKFILL_CLAUSE";

    public static final String FIND_MAX_BUSINESS_DAY_END_DT = "select max (business_day_end_dt) from PACE_Accom_Activity";
    public static final String UPDATE_PROFIT_IN_ACCOM_ACTIVITY_FOR_CR =
            " Update aa set total_profit = isnull(total_profit,0) + additional_total_profit \n" +
                    " from tableName aa \n" +
                    " inner join \n" +
                    " (select property_id, Occupancy_DT, temp_cr_mapping.CP_Accom_Type_ID, sum((Total_profit * CP_Accom_Type_Quantity) /multiplier) additional_Total_profit\n" +
                    "\tfrom ( \n" +
                    "\t\tSELECT aa.property_id,aa.Occupancy_DT, cratm.CR_Accom_Type_ID, aa.total_profit,SUM(CP_Accom_Type_Quantity) AS multiplier  \n" +
                    "\t\tFROM tableName aa   \n" +
                    "\t\tJOIN Temp_CR_Accom_Type_Mapping cratm   \n" +
                    "\t\tON aa.Accom_Type_ID = cratm.CR_Accom_Type_ID  \n" +
                    "\t\tWHERE Occupancy_DT BETWEEN :startDate AND :endDate  \n" +
                    "\t\tGROUP BY aa.Occupancy_DT, cratm.CR_Accom_Type_ID,aa.property_id,aa.total_profit\n" +
                    "\t) temp \n" +
                    "\tinner join Temp_CR_Accom_Type_Mapping temp_cr_mapping  \n" +
                    "\ton temp.CR_Accom_Type_ID = temp_cr_mapping.CR_Accom_Type_ID \n" +
                    "\tGROUP BY Occupancy_DT, CP_Accom_Type_ID, property_id \n" +
                    " )part_2 \n" +
                    " on aa.Accom_Type_ID = part_2.CP_Accom_Type_ID and aa.Occupancy_DT = part_2.Occupancy_DT and aa.Property_ID = part_2.Property_ID";
    public static final String UPDATE_PROFIT_IN_MKT_ACCOM_ACTIVITY_FOR_CR =
            " Update aa set total_profit = isnull(total_profit,0) + additional_total_profit \n" +
                    " from tableName aa \n" +
                    " inner join \n" +
                    " (select property_id, mkt_seg_id,Occupancy_DT, temp_cr_mapping.CP_Accom_Type_ID, sum((Total_profit * CP_Accom_Type_Quantity) /multiplier) additional_Total_profit\n" +
                    "\tfrom ( \n" +
                    "\t\tSELECT aa.property_id,aa.mkt_seg_id,aa.Occupancy_DT, cratm.CR_Accom_Type_ID, aa.total_profit,SUM(CP_Accom_Type_Quantity) AS multiplier  \n" +
                    "\t\tFROM tableName aa   \n" +
                    "\t\tJOIN Temp_CR_Accom_Type_Mapping cratm   \n" +
                    "\t\tON aa.Accom_Type_ID = cratm.CR_Accom_Type_ID  \n" +
                    "\t\tWHERE Occupancy_DT BETWEEN :startDate AND :endDate  \n" +
                    "\t\tGROUP BY aa.Occupancy_DT, aa.mkt_seg_id, cratm.CR_Accom_Type_ID,aa.property_id,aa.total_profit\n" +
                    "\t) temp \n" +
                    "\tinner join Temp_CR_Accom_Type_Mapping temp_cr_mapping  \n" +
                    "\ton temp.CR_Accom_Type_ID = temp_cr_mapping.CR_Accom_Type_ID \n" +
                    "\tGROUP BY Occupancy_DT, mkt_seg_id, CP_Accom_Type_ID, property_id \n" +
                    " )part_2 \n" +
                    " on aa.Accom_Type_ID = part_2.CP_Accom_Type_ID and aa.Occupancy_DT = part_2.Occupancy_DT and aa.Property_ID = part_2.Property_ID and aa.Mkt_Seg_ID = part_2.Mkt_Seg_ID";
    public static final String UPDATE_PROFIT_IN_PACE_ACCOM_ACTIVITY_FOR_CR =
            "Update aa set total_profit = isnull(total_profit,0) +  additional_total_profit  \n" +
                    "from pace_accom_activity aa  \n" +
                    "inner join  \n" +
                    "(select property_id, Occupancy_DT, temp_cr_mapping.CP_Accom_Type_ID, sum((Total_profit * CP_Accom_Type_Quantity) /multiplier) additional_Total_profit \n" +
                    "from (  \n" +
                    "\t\tSELECT aa.property_id,aa.Occupancy_DT, cratm.CR_Accom_Type_ID, aa.total_profit,SUM(CP_Accom_Type_Quantity) AS multiplier   \n" +
                    "\t\tFROM pace_accom_activity aa    \n" +
                    "\t\tJOIN Temp_CR_Accom_Type_Mapping cratm    \n" +
                    "\t\tON aa.Accom_Type_ID = cratm.CR_Accom_Type_ID   \n" +
                    "\t\tWHERE Occupancy_DT BETWEEN :startDate AND :endDate  and Business_Day_End_DT = :businessDayEndDt\n" +
                    "\t\tGROUP BY aa.Occupancy_DT, cratm.CR_Accom_Type_ID,aa.property_id,aa.total_profit \n" +
                    "\t) temp  \n" +
                    " inner join Temp_CR_Accom_Type_Mapping temp_cr_mapping   \n" +
                    " on temp.CR_Accom_Type_ID = temp_cr_mapping.CR_Accom_Type_ID  \n" +
                    " GROUP BY Occupancy_DT, CP_Accom_Type_ID, property_id  \n" +
                    " )part_2  \n" +
                    " on aa.Accom_Type_ID = part_2.CP_Accom_Type_ID and aa.Occupancy_DT = part_2.Occupancy_DT and aa.Property_ID = part_2.Property_ID and Business_Day_End_DT = :businessDayEndDt";

    public List<ProbableAccomTypesCR> getProbableComponentRooms() {
        return tenantCrudService.findByNamedQuery(ProbableAccomTypesCR.ALL);
    }

    public void saveComponentRooms(List<ProbableAccomTypesCR> componentRooms) {
        if (componentRooms != null) {
            tenantCrudService.save(componentRooms);
        }
    }

    public void deleteUnselectedMappings(List<Integer> deleteMappingList) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("crAccomTypeMappingId", deleteMappingList);

        if (deleteMappingList != null && !deleteMappingList.isEmpty()) {
            tenantCrudService.executeUpdateByNativeQuery(CRMappingRoomNumbers.DELETE_UNSELECTED_MAPPINGS_ROOM_NUMBERS, paramMap);
            tenantCrudService.executeUpdateByNativeQuery(CRAccomTypeMapping.DELETE_UNSELECTED_MAPPINGS, paramMap);
        }
    }

    public boolean saveCRMappedRoomNumbers(List<CRMappingRoomNumbers> roomNumbers) {
        for (CRMappingRoomNumbers roomNumber : roomNumbers) {
            if (roomNumber.isRemoved()) {
                tenantCrudService.delete(roomNumber);
            } else if (roomNumber.isAdded()) {
                tenantCrudService.save(roomNumber);
            }
        }
        return true;
    }

    public List<CRMappingRoomNumbers> getAllMappedRoomNumbers(Integer accomTypeMappingId) {
        return tenantCrudService.findByNamedQuery(CRMappingRoomNumbers.GET_ALL_FOR_ACCOM_TYPE_MAPPING_ID,
                QueryParameter.with("accomTypeMappingId", accomTypeMappingId).parameters());
    }

    public List<String> getDiscontinuedAccomTypes() {
        List<String> discontinuedAccomTypes = accommodationService.getAllDiscontinuedRoomTypes().stream()
                .map(accomType -> accomType.getAccomTypeCode()).collect(Collectors.toList());
        InfoMgrInstanceEntity alert = roomTypeRecodingService.getExistingMissingRoomTypeAlert();
        if (alert != null) {
            final List<String> roomTypeCodesOtherThanRename = getMissingRTCodesOtherThanRename(alert);
            discontinuedAccomTypes.addAll(roomTypeCodesOtherThanRename);
        }
        return discontinuedAccomTypes;
    }

    private List<String> getMissingRTCodesOtherThanRename(InfoMgrInstanceEntity alert) {
        final List<String> missingRTs = roomTypeRecodingService.extractMissingRTsFromAlert(alert);
        List<RoomTypeRecodingConfig> roomTypeRecodingConfigs = roomTypeRecodingService.getActiveRoomTypeRecodingConfigurationsFor(missingRTs);
        return roomTypeRecodingService.getOldRoomTypeCodesFromNonRenameConfigs(roomTypeRecodingConfigs);
    }

    public List<CRAccomTypeMapping> getComponentRoomsMappings() {
        return tenantCrudService.findByNamedQuery(CRAccomTypeMapping.ALL);
    }

    public void populateCRCapacityToAccomActivity(boolean isBDE) {
        tenantCrudService.executeUpdateByNamedQuery(AccomActivity.POPULATE_CR_CAPACITY_TO_ACCOM_ACTIVITY);
        if (isBDE) {
            tenantCrudService.executeUpdateByNamedQuery(PaceAccomActivity.POPULATE_CR_CAPACITY_TO_PACE_ACCOM_ACTIVITY);
        }
    }

    public void saveComponentAccomActivity(List<CRAccomActivity> accomActivities) {
        int batchSize = 1000;
        int childCount = 0;
        if (accomActivities != null && !accomActivities.isEmpty()) {
            StringBuilder sourceActivitiesSql = new StringBuilder();
            final Integer propertyId = PacmanWorkContextHelper.getPropertyId();
            int accomActivitiesLength = accomActivities.size();
            for (CRAccomActivity accomActivity : accomActivities) {
                sourceActivitiesSql.append(
                        String.format(CRAccomActivity.MERGE_CR_ACCOM_ACTIVITY_SOURCE_TEMPLATE, propertyId, accomActivity.getAccomTypeId().getId(), DateUtil.formatDate(accomActivity.getOccupancyDate(), DateUtil.DEFAULT_DATE_FORMAT),
                                accomActivity.getRemainingCapacity(), accomActivity.getFileMetadataId(), DateUtil.formatDate(accomActivity.getSnapShotDate(), DateUtil.DATE_TIME_FORMAT), accomActivity.getRoomsSold())
                );

                if (++childCount % batchSize == 0 || accomActivitiesLength == childCount) {
                    sourceActivitiesSql.deleteCharAt(sourceActivitiesSql.length() - 1); //delete trailing comma
                    tenantCrudService.executeUpdateByNativeQuery(CRAccomActivity.MERGE_CR_ACCOM_ACTIVITY.replace("{{sourceTable}}", sourceActivitiesSql.toString()));
                    LOGGER.info("Added " + childCount + " so far in CR acccom activity");
                    sourceActivitiesSql = new StringBuilder();
                }
            }
        }
    }

    private SasDbQueryResult getBackFillLog() {
        return sasDbToolService.executeQuery(PacmanWorkContextHelper.getClientCode(),
                PacmanWorkContextHelper.getPropertyId(), PacmanWorkContextHelper.getPropertyCode(),
                "select * from tenant.backfill_log order by createDt desc, createTime desc ");
    }

    public Map<String, Date> getPaceBackfillRelatedDates() {
        Map<String, Date> minMaxOccupancyDateFromTotalActivity = pmsRevampNonPaceUpdationService.getMinMaxOccupancyDateFromTotalActivity();
        Date nonPaceMinOccupancyDate = minMaxOccupancyDateFromTotalActivity.get("MIN_OCCUPANCY_DATE");
        Date nonPaceMaxOccupancyDate = minMaxOccupancyDateFromTotalActivity.get("MAX_OCCUPANCY_DATE");
        Map<String, Date> datesMap = new HashMap<>();
        datesMap.put("nonPaceMinOccupancyDate", nonPaceMinOccupancyDate);
        datesMap.put("nonPaceMaxOccupancyDate", nonPaceMaxOccupancyDate);
        LOGGER.info("datesMap for Non Pace Activity => " + datesMap);
        return datesMap;
    }


    public Map<String, Date> getPaceBackfillRelatedDates(int paceDaysAdjustment) {
        Date maxOccupancyDate;
        Date minOccupancyDate;
        SasDbQueryResult sasDbQueryResult = getBackFillLog();
        ArrayList<ArrayList<Object>> data = sasDbQueryResult.getData();
        if (Objects.isNull(sasDbQueryResult.getData())) {
            LOGGER.info("No records found in SAS dataset backfill_log.");
            return null;
        }
        ArrayList<Object> row = data.get(0);
        SimpleDateFormat sdf = new SimpleDateFormat(getSASSimpleDateFormat());
        final Date paceLastSnapshotDate;
        final Date paceLastBusinessDayEndDt;
        final Date paceLastUpadatedDttm;
        try {
            paceLastSnapshotDate = sdf.parse((String) row.get(5));
            paceLastUpadatedDttm = sdf.parse((String) row.get(27));
            paceLastBusinessDayEndDt = DateUtil.addDaysToDate(paceLastSnapshotDate, -1 * paceDaysAdjustment);
        } catch (ParseException e) {
            LOGGER.error("Exception occurred when parsing date", e);
            throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Exception occurred when parsing date", e);
        }
        Map<String, Date> datesMap = new HashMap<>();
        datesMap.put("paceLastBusinessDayEndDt", paceLastBusinessDayEndDt);

        if (((String) row.get(10)).equalsIgnoreCase(SELECTIVE_BACKFILL)) {
            try {
                maxOccupancyDate = sdf.parse((String) row.get(6));
                minOccupancyDate = sdf.parse((String) row.get(19));
            } catch (ParseException e) {
                LOGGER.error("Exception occurred when parsing date", e);
                throw new TetrisException(ErrorCode.UNEXPECTED_ERROR, "Exception occurred when parsing date", e);
            }
            datesMap.put(LAST_UPDATED_DTTM, paceLastUpadatedDttm);
        } else {
            FileMetadata fileMetaData = fileMetadataService.getLatestFileMetaDataFor(PacmanWorkContextHelper.getPropertyId(), true);
            Integer futureDays = fileMetaData.getFutureWindowSize();
            Integer paceBuildPastDays = configParamsService.getIntegerParameterValue(FeatureTogglesConfigParamName.PACE_BUILD_PAST_DAYS.value());
            paceBuildPastDays = Math.max(paceBuildPastDays, fileMetaData.getPastWindowSize());
            maxOccupancyDate = DateUtil.addDaysToDate(paceLastBusinessDayEndDt, (futureDays + 1));
            minOccupancyDate = DateUtil.addDaysToDate(paceLastBusinessDayEndDt, (-1 * paceBuildPastDays) + 1);
        }
        datesMap.put("minOccupancyDate", minOccupancyDate);
        datesMap.put("maxOccupancyDate", maxOccupancyDate);

        LOGGER.info("datesMap => " + datesMap);

        return datesMap;
    }

    public Map<String, Date> getPaceBackfillRelatedDatesFromTenantDB(int paceDaysAdjustment) {
        PaceBackfillLog paceBackfillLog = tenantCrudService.findByNamedQuerySingleResult(PaceBackfillLog.GET_LATEST_PACE_BACKFILL_LOG);
        if (Objects.isNull(paceBackfillLog)) {
            LOGGER.info("No records found in DB table Pace_Backfill_Log.");
            return null;
        }
        final Date paceLastSnapshotDate;
        final Date paceLastBusinessDayEndDt;

        paceLastSnapshotDate = DateUtil.addDaysToDate(paceBackfillLog.getFirstSnapshotDate(), -1);
        paceLastBusinessDayEndDt = DateUtil.addDaysToDate(paceLastSnapshotDate, -1 * paceDaysAdjustment);

        Map<String, Date> datesMap = new HashMap<>();
        datesMap.put("paceLastBusinessDayEndDt", paceLastBusinessDayEndDt);
        datesMap.put("minOccupancyDate", paceBackfillLog.getStartDate());
        datesMap.put("maxOccupancyDate", paceBackfillLog.getEndDate());

        LOGGER.info("datesMap => " + datesMap);

        return datesMap;
    }

    public String getSASSimpleDateFormat() {
        return SystemConfig.isSASRestClientDatasetEnabled() ? "ddMMMyy" : "yyyy-MM-dd";
    }


    public void correctAccomActivityDataPostBackFill() {
        Map<String, Date> datesMap = getPaceBackfillRelatedDates();
        if (datesMap != null) {
            LOGGER.info("Correcting Accom_Activity");
            Date startDate = datesMap.get("nonPaceMinOccupancyDate");
            Date endDate = datesMap.get("nonPaceMaxOccupancyDate");
            Map<String, Object> paramMap = getStartDtEndDtMap(startDate, endDate);
            if (updateTempCrAccomTypeMapping()) {
                updateAccomActivityAddComponentRoomsSold(paramMap);
            }
        }
    }

    protected void updateAccomActivityAddComponentRoomsSold(Map<String, Object> paramMap) {
        tenantCrudService.executeUpdateByNativeQuery(UPDATE_ACCOM_ACTIVITY_ADD_COMPONENT_ROOMS_SOLD, paramMap);
    }


    public void correctPaceAccomActivityDataPostBackFill(int paceDaysAdjustment) {
        Map<String, Date> datesMap = (useSqlForBackfill()) ? getPaceBackfillRelatedDatesFromTenantDB(paceDaysAdjustment)
                : getPaceBackfillRelatedDates(paceDaysAdjustment);
        if (datesMap != null) {
            LOGGER.info("Correcting Pace_Accom_Activity");
            Date startDate = datesMap.get("minOccupancyDate");
            Date endDate = datesMap.get("maxOccupancyDate");
            Map<String, Object> paramMap = getStartDtEndDtMap(startDate, endDate);
            paramMap.put("maxBusinessDayEndDt", datesMap.get("paceLastBusinessDayEndDt"));
            if (datesMap.get(LAST_UPDATED_DTTM) != null) {
                paramMap.put(LAST_UPDATED_DTTM, datesMap.get(LAST_UPDATED_DTTM));
            }
            if (updateTempCrAccomTypeMapping()) {
                updatePaceAccomActivityAddComponentRoomsSold(paramMap);
            }
        }
    }

    protected void updatePaceAccomActivityAddComponentRoomsSold(Map<String, Object> paramMap) {
        String sqlQueryToUpdatePaceAccom = UPDATE_PACE_ACCOM_ACTIVITY_ADD_COMPONENT_ROOMS_SOLD;
        if (paramMap.get(LAST_UPDATED_DTTM) != null) {
            sqlQueryToUpdatePaceAccom = sqlQueryToUpdatePaceAccom.replace(PRESERVE_NON_SELECTIVE_PACE_FROM_BACKFILL_CLAUSE, PRESERVE_NON_SELECTIVE_PACE_FROM_BACKFILL);
        }else{
            sqlQueryToUpdatePaceAccom = sqlQueryToUpdatePaceAccom.replace(PRESERVE_NON_SELECTIVE_PACE_FROM_BACKFILL_CLAUSE, "");
        }
        int numberOfUpdates = tenantCrudService.executeUpdateByNativeQuery(sqlQueryToUpdatePaceAccom, paramMap);
        LOGGER.info("Number of updates in Pace Accom Activity: " + numberOfUpdates);
    }


    public void correctMktActivityDataPostBackFill(String mktSegIds) {
        List<Integer> mktSegIdsList = null;
        if (!ALL.equalsIgnoreCase(mktSegIds) && mktSegIds != null) {
            mktSegIdsList = Stream.of(mktSegIds.split("\\|")).map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
        }
        correctMktActivityDataPostBackFill(mktSegIdsList);
    }

    public void correctMktActivityDataPostBackFill(List<Integer> mktSegIds) {
        LOGGER.info("Executing with parameters mktSegIds:   " + mktSegIds);
        Map<String, Date> datesMap = getPaceBackfillRelatedDates();
        if (datesMap != null) {
            LOGGER.info("Executing correction for mkt_accom_activity post backfill : ");
            Date startDate = datesMap.get("nonPaceMinOccupancyDate");
            Date endDate = datesMap.get("nonPaceMaxOccupancyDate");

            LOGGER.info("Start Date : " + startDate + " End Date : " + endDate);
            if (CollectionUtils.isEmpty(mktSegIds)) {
                mktSegIds = null;
            }
            updateMktAccomActivityAddComponentRoomsSoldAndRevenuePostBackFill(startDate, endDate, mktSegIds);
        }
    }
    public void updateMktAccomActivityAddComponentRoomsSoldAndRevenuePostBackFill(Date startDate, Date endDate, List<Integer> mktSegIds) {
        if(!updateTempCrAccomTypeMapping()) {
            return;
        }
        Map<String, Object> paramMap = getStartDtEndDtMap(startDate, endDate);
        String queryString = UPDATE_MKT_ACCOM_ACTIVITY_ADD_COMPONENT_ROOMS_SOLD_AND_REVENUE_POST_BACKFILL;
        if (CollectionUtils.isNotEmpty(mktSegIds)) {
            queryString = queryString.replace(SELECTIVE_MKT_SEG_WHERE_CLAUSE, " and part_2.Mkt_Seg_ID in (:mktSegIds) ");
            paramMap.put("mktSegIds", mktSegIds);
        } else {
            queryString = queryString.replace(SELECTIVE_MKT_SEG_WHERE_CLAUSE, " ");
        }
        tenantCrudService.executeUpdateByNativeQuery(queryString, paramMap);
        LOGGER.info("Updated component rooms to mkt_accom_activity rooms_sold");
    }

    public void updateMktAccomActivityAddComponentRoomsSold(LocalDate startDate, LocalDate endDate, List<Integer> mktSegIds) {
        if (!updateTempCrAccomTypeMapping()) {
            return;
        }
        Map<String, Object> paramMap = getStartDtEndDtMap(startDate.toDate(), endDate.toDate());
        String queryString = UPDATE_MKT_ACCOM_ACTIVITY_ADD_COMPONENT_ROOMS_SOLD;
        if (CollectionUtils.isNotEmpty(mktSegIds)) {
            queryString = queryString.replace(SELECTIVE_MKT_SEG_WHERE_CLAUSE, " and maa.Mkt_Seg_ID in (:mktSegIds) ");
            paramMap.put("mktSegIds", mktSegIds);
        } else {
            queryString = queryString.replace(SELECTIVE_MKT_SEG_WHERE_CLAUSE, " ");
        }
        tenantCrudService.executeUpdateByNativeQuery(queryString, paramMap);
        LOGGER.info("Updated component rooms to mkt_accom_activity rooms_sold");
    }

    public boolean updateTempCrAccomTypeMapping() {
        final Map<Integer, List<Pair<Integer, Integer>>> componentRoomPhysicalRoomRelation =
                componentRoomPhysicalRoomResolver.getComponentRoomPhysicalRoomRelation();
        if (MapUtils.isEmpty(componentRoomPhysicalRoomRelation)) {
            LOGGER.info("Could not identify componentRoomPhysicalRoomRelation");
            return false;
        }
        componentRoomPhysicalRoomResolver.createTempCrPhysicalRoomMapping(componentRoomPhysicalRoomRelation);
        LOGGER.info("Updated Temp_CR_accom_type_mapping");
        return true;
    }

    protected Map<String, Object> getStartDtEndDtMap(Date startDate, Date endDate) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("startDt", startDate);
        paramMap.put("endDt", endDate);
        return paramMap;
    }

    public void saveTotalActivity() {
        tenantCrudService.executeUpdateByNamedQuery(CRTotalActivity.DeleteAll);
        tenantCrudService.executeUpdateByNamedQuery(CRTotalActivity.POPULATE_CR_TOTAL_ACTIVITY);
        LOGGER.info("Added CR Total activity");
    }

    public void saveMktComponentAccomActivity(List<MktSegAccomActivity> accomActivities) {
        int batchSize = 100, childCount = 0;
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        String pattern = "yyyy-MM-dd HH:mm:ss";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        String baseQuery = "INSERT INTO CR_Mkt_Accom_Activity (Property_ID, mkt_seg_id, Accom_Type_ID, Occupancy_DT,  File_Metadata_ID, Last_Updated_DTTM, CreateDate, SnapShot_DTTM, Rooms_Sold) ";
        StringBuilder insertQuery = new StringBuilder();
        if (accomActivities != null && !accomActivities.isEmpty()) {
            int accomActivitiesLength = accomActivities.size();
            for (MktSegAccomActivity accomActivity : accomActivities) {
                Integer mktSegId = accomActivity.getMktSegId();
                String occDtStr = simpleDateFormat.format(accomActivity.getOccupancyDate());
                String snapShotDateStr = simpleDateFormat.format(accomActivity.getSnapShotDate());
                Integer accomTypeID = accomActivity.getAccomTypeId();
                if (childCount == 0 || childCount % batchSize == 0) {
                    insertQuery = new StringBuilder(baseQuery);
                    insertQuery.append(" VALUES (" + propertyId + ", " + mktSegId + ", " + accomTypeID + ", '" + occDtStr + "', " + accomActivity.getFileMetadataId() + ", CURRENT_TIMESTAMP, GETDATE(), '" + snapShotDateStr + "', " + accomActivity.getRoomsSold() + ")");
                } else {
                    insertQuery.append(" ,(" + propertyId + ", " + mktSegId + ", " + accomTypeID + ", '" + occDtStr + "', " + accomActivity.getFileMetadataId() + ", CURRENT_TIMESTAMP, GETDATE(), '" + snapShotDateStr + "', " + accomActivity.getRoomsSold() + ")");
                }
                if (++childCount % batchSize == 0 || accomActivitiesLength == childCount) {
                    tenantCrudService.executeUpdateByNativeQuery(insertQuery.toString());
                    tenantCrudService.flushAndClear();
                    LOGGER.info("Added " + childCount + " so far in CR_Mkt_Accom_Activity");
                }
            }
            LOGGER.info("Added " + childCount + " so far in CR_Mkt_Accom_Activity");
        }
    }

    public void savePaceComponentAccomActivity() {

        Integer fileMetaDataId = getMaxFileMetaDataIdFromCRAccomAcitvity();
        if (fileMetaDataId == null || fileMetaDataId == 0) {
            LOGGER.info(" No file meta Id data found in CR acccom activity");
            return;
        }
        FileMetadata fileMetadata = tenantCrudService.findByNamedQuerySingleResult(FileMetadata.BY_ID, QueryParameter.with(FILE_META_DATA_ID, fileMetaDataId).parameters());

        String businessDate = DateUtil.formatDate(fileMetadata.getSnapshotDt(), DateUtil.DEFAULT_DATE_FORMAT);
        // This is to handle a corner case, where a data feed requested more than once in a day and operadataload runs with same business_day_end_dt
        tenantCrudService.executeUpdateByNamedQuery(PaceCRAccomActivity.DELETE_DATA_IF_ALREADY_EXISTS_FOR_BUSINESS_DT,
                QueryParameter.with("businessDate", businessDate).parameters());

        tenantCrudService.executeUpdateByNamedQuery(PaceCRAccomActivity.MERGE_PACE_CR_ACCOM_ACTIVITY,
                QueryParameter.with(FILE_META_DATA_ID, fileMetadata.getId())
                        .and("SnapShot_DTTM", fileMetadata.getSnapshotDtTm())
                        .and("businessDate", businessDate)
                        .parameters());
    }

    public List<CRMappingRoomNumbers> getRoomNumbers(int accomTypeId) {

        AccomType accomType = tenantCrudService.findByNamedQuerySingleResult(AccomType.ALL_BY_ACCOM_TYPE_ID, QueryParameter.with("id", accomTypeId).parameters());

        List<CRMappingRoomNumbers> crRoomNumbers = tenantCrudService.findByNativeQuery(GET_ALL_ROOM_NUMBERS,
                QueryParameter.with("accomTypeId", accomTypeId).parameters(), getRoomNumberRowMapper(accomType));

        if (null == crRoomNumbers) {
            crRoomNumbers = new ArrayList<CRMappingRoomNumbers>();
        }
        return crRoomNumbers;
    }

    private RowMapper<CRMappingRoomNumbers> getRoomNumberRowMapper(AccomType accomType) {
        return row -> {
            CRMappingRoomNumbers data = new CRMappingRoomNumbers();
            CRAccomTypeMapping crAccomTypeMapping = new CRAccomTypeMapping();
            crAccomTypeMapping.setCpAccomType(accomType);
            data.setCrAccomTypeMapping(crAccomTypeMapping);
            data.setCpRoomNumber((String) row[0]);
            return data;
        };
    }

    public void setTenantCrudService(CrudService tenantCrudService) {
        this.tenantCrudService = tenantCrudService;
    }

    public void setAlertService(AlertService alertService) {
        this.alertService = alertService;
    }

    public List<AccomType> getConfiguredAsCRAccomTypes() {
        return tenantCrudService.findByNamedQuery(AccomType.ALL_COMPONENT_ROOMS_ACCOM_TYPE,
                QueryParameter.with("isComponentRoom", "Y").parameters());
    }

    public List<CRAccomTypeMapping> getMappedComponentRooms() {
        return tenantCrudService.findByNamedQuery(CRAccomTypeMapping.ALL);
    }

    public void deleteOrphanRoomsForHospitalityRooms(Set<String> hrCodes) {
        Map<String, Object> params = new HashMap<>();
        params.put("hrCodes", hrCodes);
        tenantCrudService.executeUpdateByNamedQuery(CROrphanMapping.DELETE_MAPPINGS, params);
    }

    public void saveComponentRoomsAndATMapping(List<AccomType> listATToUpdateCapacity,
                                               List<CRAccomTypeMapping> uiListofCRATMapping,
                                               List<CRAccomTypeMapping> deleteListOfCRATMapping) {
        List<Integer> crMappingIds = new ArrayList<>();
        deleteListOfCRATMapping.forEach(m -> crMappingIds.add(m.getId()));
        if (!crMappingIds.isEmpty()) {
            Map<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("accomTypeMappingIdList", crMappingIds);
            List byNamedQuery = tenantCrudService.findByNamedQuery(CRMappingRoomNumbers.GET_ALL_FOR_MULTIPLE_ACCOM_TYPE_MAPPING_ID, paramMap);
            if (!byNamedQuery.isEmpty()) {
                tenantCrudService.delete(byNamedQuery);
            }
        }
        tenantCrudService.delete(deleteListOfCRATMapping);
        tenantCrudService.flush();//user is adding same unique combination after deleting one.
        tenantCrudService.save(listATToUpdateCapacity);
        tenantCrudService.save(uiListofCRATMapping);
    }

    public boolean isComponentRoomEnabledWithValidConfiguration(Integer propertyId) {
        Property property = globalCrudService.find(Property.class, propertyId);
        String context = Constants.CONFIG_PARAMS_NODE_PREFIX + "." + property.getClient().getCode() + "." + property.getCode();
        Boolean isComponentRoomEnabled = configParamsService.getParameterValue(context, FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED);
        Boolean isComponentRoomConfigurationCompleted = configParamsService.getParameterValue(context, FeatureTogglesConfigParamName.COMPONENT_ROOMS_CONFIGURATION_COMPLETED);
        return isComponentRoomEnabled ? isComponentRoomConfigurationCompleted : true;

    }

    public void saveComponentRoomsConfigurationCompleted(String value) {
        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(CONTEXT_KEY);
        String context = "pacman." + workContext.getClientCode() + "." + workContext.getPropertyCode();
        configParamsService.addParameterValue(context, FeatureTogglesConfigParamName.COMPONENT_ROOMS_CONFIGURATION_COMPLETED.value(), value);
    }

    @ForTesting
    // use only for vaadin UI test.

    public void insertTestDataForProbableAccomTypes(int propertyId) {

        tenantCrudService.executeUpdateByNativeQuery("delete from CR_Mapping_Room_Numbers");
        tenantCrudService.executeUpdateByNativeQuery("delete from CR_Accom_Type_Mapping");
        tenantCrudService.executeUpdateByNativeQuery("delete from CR_PROBABLE_ACCOM_TYPES");
        tenantCrudService.executeUpdateByNativeQuery("delete from dbo.CR_Accom_Type_Mapping_History");
        tenantCrudService.executeUpdateByNativeQuery("delete from dbo.CR_Mapping_Room_Numbers_History");
        tenantCrudService.executeUpdateByNativeQuery("delete from Mkt_Accom_Activity where accom_type_id in (select accom_type_id from Accom_Type where accom_type_code in ('PM1','PM2','CR1','CR2','CR3','CR4'))");
        tenantCrudService.executeUpdateByNativeQuery("delete from CostofWalk_Default where accom_type_id in (select accom_type_id from Accom_Type where accom_type_code in ('PM1','PM2','CR1','CR2','CR3','CR4'))");
        tenantCrudService.executeUpdateByNativeQuery("delete from Overbooking_Accom where accom_type_id in (select accom_type_id from Accom_Type where accom_type_code in ('PM1','PM2','CR1','CR2','CR3','CR4'))");
        tenantCrudService.executeUpdateByNativeQuery("delete from Accom_Activity where accom_type_id in (select accom_type_id from Accom_Type where accom_type_code in ('PM1','PM2','CR1','CR2','CR3','CR4'))");
        tenantCrudService.executeUpdateByNativeQuery("delete from PACE_Accom_Activity where accom_type_id in (select accom_type_id from Accom_Type where accom_type_code in ('PM1','PM2','CR1','CR2','CR3','CR4'))");
        tenantCrudService.executeUpdateByNativeQuery("delete from Accom_Type where accom_type_code in ('PM1','PM2','CR1','CR2','CR3','CR4') ");

        List<ProbableAccomTypesCR> list = new ArrayList<ProbableAccomTypesCR>();
        ProbableAccomTypesCR pact = new ProbableAccomTypesCR();
        pact.setPropertyId(propertyId);
        pact.setAccomTypeCode("PM1");
        list.add(pact);
        pact = new ProbableAccomTypesCR();
        pact.setPropertyId(propertyId);
        pact.setAccomTypeCode("PM2");
        list.add(pact);
        pact = new ProbableAccomTypesCR();
        pact.setPropertyId(propertyId);
        pact.setAccomTypeCode("CR1");
        list.add(pact);
        pact = new ProbableAccomTypesCR();
        pact.setPropertyId(propertyId);
        pact.setAccomTypeCode("CR2");
        list.add(pact);
        pact = new ProbableAccomTypesCR();
        pact.setPropertyId(propertyId);
        pact.setAccomTypeCode("CR3");
        list.add(pact);
        pact = new ProbableAccomTypesCR();
        pact.setPropertyId(propertyId);
        pact.setAccomTypeCode("CR4");
        list.add(pact);

        tenantCrudService.save(list);
    }

    public Map<String, Set<String>> getRoomNumberSoldsAsPerStay(LinkedHashSet<AccomType> cpAccomTypes) {
        List<String> accomIds = new ArrayList<>();
        cpAccomTypes.forEach(accomType -> {
            accomIds.add(accomType.getAccomTypeCode());
        });

        Map<String, String> fileMetaDataDateMap = getStartEndDateForMaxFileMetaDataFromAccomActivity();
        String startDt = fileMetaDataDateMap.get(Constants.START_DATE);
        String endDt = fileMetaDataDateMap.get(Constants.END_DATE);

        Date startDtDate = null, endDtDate = null;
        try {
            startDtDate = DateUtil.parseDate(startDt, DateUtil.DEFAULT_DATE_FORMAT);
            endDtDate = DateUtil.parseDate(endDt, DateUtil.DEFAULT_DATE_FORMAT);
        } catch (ParseException e) {
            LOGGER.error(e);
        }

        Map<String, Set<String>> roomNumberMap = createMapForDateRange(startDtDate, endDtDate);
        StringBuilder sqlBuilder = new StringBuilder(" SELECT Arrival_DT, Departure_DT,  at.Accom_Type_Code,  Room_Number ")
                .append(" FROM Individual_Trans it ")
                .append(" INNER JOIN Accom_Type at ON (it.Accom_Type_ID = at.Accom_Type_ID and at.Status_ID = 1) ")
                .append(" WHERE Arrival_DT <= :endDt ")
                .append(" AND Departure_DT > :startDt")
                .append(" AND at.Accom_Type_Code IN (:accomTypeCode) AND Room_Number <> '' AND it.Individual_Status NOT IN( 'XX','NS') ");

        tenantCrudService.findByNativeQuery(sqlBuilder.toString(),
                QueryParameter.with("startDt", startDt).and("endDt", endDt).and("accomTypeCode", accomIds).parameters(),
                getRoomNumberSoldsAsPerStayRowMapper(startDtDate, endDtDate, roomNumberMap));

        return roomNumberMap;
    }

    private RowMapper<Object> getRoomNumberSoldsAsPerStayRowMapper(Date startDt, Date endDt, Map<String, Set<String>> roomNumberMap) {
        return row -> {
            Date arrivalDate = (Date) row[0];
            Date departureDate = (Date) row[1];
            String roomNumber = (String) row[3];

            Date arrivalDateWithinRange = arrivalDate.before(startDt) ? startDt : arrivalDate;
            Date departureDateWithinRange = departureDate.after(endDt) ? endDt : departureDate;

            List<Date> dateList = DateUtil.datesBetweenInclusive(arrivalDateWithinRange, departureDateWithinRange);

            for (Date date : dateList) {
                String dateStr = DateUtil.formatDate(date, DateUtil.DEFAULT_DATE_FORMAT);
                if (!StringUtils.isEmpty(roomNumber)) {
                    roomNumberMap.get(dateStr).add(roomNumber.toUpperCase());
                }
            }
            return null;
        };
    }

    private Map<String, Set<String>> createMapForDateRange(Date startDt, Date endDt) {

        Map<String, Set<String>> roomNumberMap = new HashMap<>();
        List<Date> dateList = DateUtil.datesBetweenInclusive(startDt, endDt);

        for (Date date : dateList) {
            roomNumberMap.put(DateUtil.formatDate(date, DateUtil.DEFAULT_DATE_FORMAT), new HashSet<>());
        }
        return roomNumberMap;
    }

    public Map<String, List<AccomActivity>> getAccomActivityByOccupancyDate(LinkedHashSet<AccomType> cpAccomTypes, String startDate, String endDate) {
        List<String> accomTypeCode = new ArrayList<String>();
        cpAccomTypes.forEach(accomType -> {
            accomTypeCode.add(accomType.getAccomTypeCode());

        });
        return getAccomActivities(accomTypeCode, startDate, endDate);
    }

    public Map<String, List<AccomActivity>> getAccomActivityByOccupancyDate(LinkedHashSet<AccomType> cpAccomTypes) {
        List<String> accomCode = new ArrayList<String>();
        cpAccomTypes.forEach(accomType -> {
            accomCode.add(accomType.getAccomTypeCode());
        });

        Map<String, String> fileMetaDataDateMap = getStartEndDateForMaxFileMetaDataFromAccomActivity();
        String startDt = fileMetaDataDateMap.get(Constants.START_DATE);
        String endDt = fileMetaDataDateMap.get(Constants.END_DATE);

        return getAccomActivities(accomCode, startDt, endDt);
    }

    private Map<String, List<AccomActivity>> getAccomActivities(List<String> accomCode, String startDt, String endDt) {
        List<AccomActivity> accomActivities = tenantCrudService.findByNamedQuery(AccomActivity.ACTIVE_BY_DATE_RANGE_AND_ACCOM_TYPE_CODE,
                QueryParameter.with(START_DATE, startDt).and(END_DATE, endDt).and("accomTypeCode", accomCode).and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());

        Map<Date, List<AccomActivity>> occupancyAccomActivitiesMap =
                accomActivities.stream().collect(Collectors.groupingBy(AccomActivity::getOccupancyDate));

        Map<String, List<AccomActivity>> occupancyDateToAccomActvityMap = new LinkedHashMap<String, List<AccomActivity>>();
        occupancyAccomActivitiesMap.forEach((date, accomActivities1) -> {
            String occupancyDt = DateUtil.formatDate(date, DateUtil.DEFAULT_DATE_FORMAT);
            occupancyDateToAccomActvityMap.put(occupancyDt, accomActivities1);
        });
        return occupancyDateToAccomActvityMap;
    }

    public Integer getMaxFileMetaDataIdFromCRAccomAcitvity() {
        return tenantCrudService.findByNamedQuerySingleResult(CRAccomActivity.GET_MAX_FILE_METADATA, null);
    }

    public Map<String, Map<String, List<OperaOccupancySummary>>> getOperaOccupancySummaryPerOccupancy(HashSet<AccomType> cpAccomTypes, List<String> analyticalMarketSegments) {
        Collection<String> accomTypeCodes = cpAccomTypes.stream().collect(Collectors.toMap(AccomType::getAccomTypeCode, AccomType::getAccomTypeCode)).values();
        List<OperaOccupancySummary> summaries = tenantCrudService.findByNamedQuery(OperaOccupancySummary.FIND_LATEST_AMS_BY_ROOM_TYPE_BY_AMS,
                QueryParameter.with("roomTypes", accomTypeCodes).and("analyticalMarketSegments", analyticalMarketSegments).parameters());
        return summaries.stream().collect(Collectors.groupingBy(summary -> summary.getOccupancyDate().toString(),
                Collectors.groupingBy(so -> so.getMarketCode())));
    }

    public Map<String, Map<Integer, List<MktSegAccomActivity>>> getMktRoomSoldAsPerMktPerOccupancyDates(LinkedHashSet<AccomType> cpAccomTypes, Date startDate, Date endDate) {
        Collection<Integer> accomTypeIds = cpAccomTypes.stream().collect(Collectors.toMap(AccomType::getId, AccomType::getId)).values();
        List<MktSegAccomActivity> summaries = tenantCrudService.findByNamedQuery(MktSegAccomActivity.FIND_BY_DATE_RANGE_BY_ACCOM_TYPE_ID_AND_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and(START_DATE, startDate).and(END_DATE, endDate).and("accomTypeIds", accomTypeIds).parameters());
        return summaries.stream().collect(Collectors.groupingBy(summary -> LocalDate.fromDateFields(summary.getOccupancyDate()).toString(),
                Collectors.groupingBy(so -> so.getMktSegId())));
    }

    public Map<String, String> getStartEndDateForMaxFileMetaDataFromAccomActivity() {
        FileMetadata fileMetadata = getFileMetadata();
        return getStartEndDateForFileMetaData(fileMetadata);
    }

    private Map<String, String> getStartEndDateForFileMetaData(FileMetadata fileMetadata) {
        String startDt = DateUtil.formatDate(DateUtil.addDaysToDate(fileMetadata.getSnapshotDt(), (-1 * fileMetadata.getPastWindowSize())), DateUtil.DEFAULT_DATE_FORMAT);
        String endDt = DateUtil.formatDate(DateUtil.addDaysToDate(fileMetadata.getSnapshotDt(), fileMetadata.getFutureWindowSize()), DateUtil.DEFAULT_DATE_FORMAT);

        Map<String, String> fileMetaDataDateMap = new HashMap<String, String>();

        fileMetaDataDateMap.put(Constants.START_DATE, startDt);
        fileMetaDataDateMap.put(Constants.END_DATE, endDt);
        fileMetaDataDateMap.put(Constants.CAUGHT_UP_DATE, LocalDate.fromDateFields(fileMetadata.getSnapshotDt()).toString());

        return fileMetaDataDateMap;
    }

    public Map<String, String> getStartEndDateForMaxFileMetaDataFromMktAccomActivity() {
        int fileMetaDataId = tenantCrudService.findByNamedQuerySingleResult(MktSegAccomActivity.GET_MAX_FILE_METADATA_ID);
        FileMetadata fileMetadata = tenantCrudService.findByNamedQuerySingleResult(FileMetadata.BY_ID, QueryParameter.with(FILE_META_DATA_ID, fileMetaDataId).parameters());
        return getStartEndDateForFileMetaData(fileMetadata);
    }

    protected FileMetadata getFileMetadata() {
        int fileMetaDataId = tenantCrudService.findByNamedQuerySingleResult(AccomActivity.GET_MAX_FILE_METADATA_ID);
        return (FileMetadata) tenantCrudService.findByNamedQuerySingleResult(FileMetadata.BY_ID, QueryParameter.with(FILE_META_DATA_ID, fileMetaDataId).parameters());
    }


    public void updateComponentOutOfOrder(Map<String, Map<Integer, Integer>> correctedOutOfOrderMap) {
        if (correctedOutOfOrderMap == null || correctedOutOfOrderMap.isEmpty()) {
            return;
        }
        int batchSize = 1000;
        int childCount = 0;
        List<AccomType> componentAccomTypes = getConfiguredAsCRAccomTypes();
        LinkedHashSet<Integer> accomTypeIds = new LinkedHashSet<Integer>();
        componentAccomTypes.forEach(accomType -> accomTypeIds.add(accomType.getId()));

        StringBuilder crOOOAccomActivities = new StringBuilder();
        final Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        for (Map.Entry<String, Map<Integer, Integer>> entry : correctedOutOfOrderMap.entrySet()) {
            String occupancyDate = entry.getKey();
            Map<Integer, Integer> outOfOrderMap = entry.getValue();
            for (Map.Entry<Integer, Integer> entry1 : outOfOrderMap.entrySet()) {
                Integer accomTypeId = entry1.getKey();
                if (accomTypeIds.contains(accomTypeId)) {
                    crOOOAccomActivities.append(String.format(POPULATE_CR_OUT_OF_ORDER_ACCOM_ACTIVITY_TEMPLATE,
                            propertyId,
                            occupancyDate,
                            accomTypeId,
                            entry1.getValue())
                    );
                    if (++childCount % batchSize == 0) {
                        crOOOAccomActivities.deleteCharAt(crOOOAccomActivities.length() - 1);
                        tenantCrudService.executeUpdateByNativeQuery(POPULATE_CR_OUT_OF_ORDER_ACCOM_ACTIVITY.replace(CROOO_VALUES, crOOOAccomActivities.toString()));
                        LOGGER.info("Updated Out Of Order " + childCount + " so far in Acccom activity");
                        crOOOAccomActivities = new StringBuilder();
                    }
                }
            }
        }
        if (childCount % batchSize != 0) {
            crOOOAccomActivities.deleteCharAt(crOOOAccomActivities.length() - 1);
            tenantCrudService.executeUpdateByNativeQuery(POPULATE_CR_OUT_OF_ORDER_ACCOM_ACTIVITY.replace(CROOO_VALUES, crOOOAccomActivities.toString()));
            LOGGER.info("Updated Out Of Order " + childCount + " so far in Acccom activity");
        }
    }

    public void updatePaceAccomActivityForComponentOOO(Integer propertyId) {
        int fileMetaDataId = tenantCrudService.findByNamedQuerySingleResult(AccomActivity.GET_MAX_FILE_METADATA_ID);
        tenantCrudService.executeUpdateByNamedQuery(PaceAccomActivity.POPULATE_CR_OUT_OF_ORDER_TO_PACE_ACCOM_ACTIVITY,
                QueryParameter.with(PROPERTY_ID, propertyId).and(FILE_META_DATA_ID, fileMetaDataId).parameters());
    }

    public void updateAccomActivityForComponentOOO(String startDt, String endDt) {
        tenantCrudService.executeUpdateByNamedQuery(CROutOfOrder.POPULATE_CR_OOO_TO_PACE_ACCOM_ACTIVITY, QueryParameter.with(START_DATE, startDt).and(END_DATE, endDt).parameters());
    }

    public Map<String, Map<Integer, Integer>> getCROutOfOrderForDateRange(String startDate, String endDate) {

        try {
            Date startDt = DateUtil.parseDate(startDate, DateUtil.DEFAULT_DATE_FORMAT);
            Date endDt = DateUtil.parseDate(endDate, DateUtil.DEFAULT_DATE_FORMAT);

            List<CROutOfOrder> outOfOrderList = getCROutOfOrderForDateRange(startDt, endDt);
            Map<Date, List<CROutOfOrder>> occupancyOutOfOrderMap = outOfOrderList.stream().collect(Collectors.groupingBy(CROutOfOrder::getOccupancyDate));

            Map<String, Map<Integer, Integer>> occupancyDateToOutOfOrderMap = new LinkedHashMap<String, Map<Integer, Integer>>();
            occupancyOutOfOrderMap.forEach((date, outOfOrders) -> {
                String occupancyDt = DateUtil.formatDate(date, DateUtil.DEFAULT_DATE_FORMAT);
                Map<Integer, Integer> outOfOrderMap = new HashMap<Integer, Integer>();
                for (CROutOfOrder outOfOrder : outOfOrders) {
                    outOfOrderMap.put(outOfOrder.getAccomType().getId(), outOfOrder.getOutOfOrder());
                }
                occupancyDateToOutOfOrderMap.put(occupancyDt, outOfOrderMap);
            });
            return occupancyDateToOutOfOrderMap;
        } catch (Exception e) {
            LOGGER.error(e);
        }
        return null;
    }

    public List<CROutOfOrder> getCROutOfOrderForDateRange(Date startDate, Date endDate) {
        return tenantCrudService.findByNamedQuery(CROutOfOrder.BY_OCCUPANCY_DATE_AND_PROPERTY_ID,
                QueryParameter.with(START_DATE, startDate).and(END_DATE, endDate).and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public void saveOutOfOrderConf(List<CROutOfOrder> crOutOfOrders) {
        tenantCrudService.save(crOutOfOrders);
    }

    public void deleteOutOfOrderConf(List<CROutOfOrder> crOutOfOrdersDelete) {
        tenantCrudService.delete(crOutOfOrdersDelete);
    }

    public List<AccomType> getPartialSharingComponentRooms() {
        return tenantCrudService.findByNamedQuery(CRMappingRoomNumbers.GET_PARTIAL_SHARING_COMPONENT_ROOMS);
    }

    /**************REST calls for Vaadin UI test *************/
    @ForTesting
    // use only for vaadin UI test.

    public void insertTestDataConfigurationForOutOfOrder(int propertyId) {

        tenantCrudService.executeUpdateByNativeQuery("delete from CR_Out_Of_Order");
        tenantCrudService.executeUpdateByNativeQuery("delete from CR_Mapping_Room_Numbers");
        tenantCrudService.executeUpdateByNativeQuery("delete from CR_Accom_Type_Mapping");
        tenantCrudService.executeUpdateByNativeQuery("delete from CR_PROBABLE_ACCOM_TYPES");
        tenantCrudService.executeUpdateByNativeQuery("delete from Mkt_Accom_Activity where accom_type_id in (select accom_type_id from Accom_Type where accom_type_code in ('PM1','PM2','CR1','CR2','CR3','CR4'))");
        tenantCrudService.executeUpdateByNativeQuery("delete from CostofWalk_Default where accom_type_id in (select accom_type_id from Accom_Type where accom_type_code in ('PM1','PM2','CR1','CR2','CR3','CR4'))");
        tenantCrudService.executeUpdateByNativeQuery("delete from Overbooking_Accom where accom_type_id in (select accom_type_id from Accom_Type where accom_type_code in ('PM1','PM2','CR1','CR2','CR3','CR4'))");
        tenantCrudService.executeUpdateByNativeQuery("delete from Accom_Activity where accom_type_id in (select accom_type_id from Accom_Type where accom_type_code in ('PM1','PM2','CR1','CR2','CR3','CR4'))");
        tenantCrudService.executeUpdateByNativeQuery("delete from PACE_Accom_Activity where accom_type_id in (select accom_type_id from Accom_Type where accom_type_code in ('PM1','PM2','CR1','CR2','CR3','CR4'))");
        tenantCrudService.executeUpdateByNativeQuery("delete from Accom_Activity where Occupancy_DT in('2017-02-24','2017-02-25','2017-02-26')");
        tenantCrudService.executeUpdateByNativeQuery("delete from Accom_Type where accom_type_code in ('PM1','PM2','CR1','CR2','CR3','CR4') ");

        List<AccomType> accomTypeList = new ArrayList<AccomType>();
        accomTypeList.add(createAccomType(propertyId, "CR1", 4, "Y"));
        accomTypeList.add(createAccomType(propertyId, "CR2", 4, "Y"));
        accomTypeList.add(createAccomType(propertyId, "CR3", 4, "Y"));
        tenantCrudService.save(accomTypeList);

        List<CRAccomTypeMapping> crMappinglist = new ArrayList<CRAccomTypeMapping>();
        crMappinglist.add(createCRAccomTypeMapping(propertyId, "CR1", "QR", 2));
        crMappinglist.add(createCRAccomTypeMapping(propertyId, "CR1", "TU", 1));
        crMappinglist.add(createCRAccomTypeMapping(propertyId, "CR2", "QS", 1));
        crMappinglist.add(createCRAccomTypeMapping(propertyId, "CR2", "ZJ", 1));
        crMappinglist.add(createCRAccomTypeMapping(propertyId, "CR3", "CR1", 1));
        crMappinglist.add(createCRAccomTypeMapping(propertyId, "CR3", "CR2", 1));
        tenantCrudService.save(crMappinglist);

        List<CRMappingRoomNumbers> crRoomNumberslist = new ArrayList<CRMappingRoomNumbers>();
        List crMappingList = tenantCrudService.findByNamedQuery(CRAccomTypeMapping.ALL);
        crRoomNumberslist.add(setCrMappingRoomNumbers(propertyId, crMappingList.get(0), "317"));
        crRoomNumberslist.add(setCrMappingRoomNumbers(propertyId, crMappingList.get(0), "318"));
        crRoomNumberslist.add(setCrMappingRoomNumbers(propertyId, crMappingList.get(0), "319"));
        crRoomNumberslist.add(setCrMappingRoomNumbers(propertyId, crMappingList.get(0), "320"));
        crRoomNumberslist.add(setCrMappingRoomNumbers(propertyId, crMappingList.get(0), "321"));
        crRoomNumberslist.add(setCrMappingRoomNumbers(propertyId, crMappingList.get(0), "322"));
        crRoomNumberslist.add(setCrMappingRoomNumbers(propertyId, crMappingList.get(0), "323"));
        crRoomNumberslist.add(setCrMappingRoomNumbers(propertyId, crMappingList.get(0), "324"));

        crRoomNumberslist.add(setCrMappingRoomNumbers(propertyId, crMappingList.get(1), "545"));
        crRoomNumberslist.add(setCrMappingRoomNumbers(propertyId, crMappingList.get(1), "546"));
        crRoomNumberslist.add(setCrMappingRoomNumbers(propertyId, crMappingList.get(1), "547"));
        crRoomNumberslist.add(setCrMappingRoomNumbers(propertyId, crMappingList.get(1), "548"));
        tenantCrudService.save(crRoomNumberslist);

        List<CROutOfOrder> crOutOfOrderList = new ArrayList<CROutOfOrder>();
        crOutOfOrderList.add(insertOutOfOrder(propertyId, new LocalDate("2017-02-20").toDate(), "CR2", 1));
        crOutOfOrderList.add(insertOutOfOrder(propertyId, new LocalDate("2017-02-21").toDate(), "CR1", 1));
        crOutOfOrderList.add(insertOutOfOrder(propertyId, new LocalDate("2017-02-22").toDate(), "CR3", 1));
        crOutOfOrderList.add(insertOutOfOrder(propertyId, new LocalDate("2017-02-23").toDate(), "CR2", 1));
        crOutOfOrderList.add(insertOutOfOrder(propertyId, new LocalDate("2017-02-23").toDate(), "CR1", 1));
        crOutOfOrderList.add(insertOutOfOrder(propertyId, new LocalDate("2017-02-24").toDate(), "QS", 3));
        crOutOfOrderList.add(insertOutOfOrder(propertyId, new LocalDate("2017-02-25").toDate(), "QR", 3));
        crOutOfOrderList.add(insertOutOfOrder(propertyId, new LocalDate("2017-02-26").toDate(), "QS", 2));
        crOutOfOrderList.add(insertOutOfOrder(propertyId, new LocalDate("2017-02-26").toDate(), "ZJ", 4));

        tenantCrudService.save(crOutOfOrderList);

        List<AccomActivity> accomActivityList = new ArrayList<AccomActivity>();
        accomActivityList.add(insertCRInAccomActivity(propertyId, new LocalDate("2017-02-20").toDate(), "CR2", 1));
        accomActivityList.add(insertCRInAccomActivity(propertyId, new LocalDate("2017-02-21").toDate(), "CR1", 1));
        accomActivityList.add(insertCRInAccomActivity(propertyId, new LocalDate("2017-02-22").toDate(), "CR3", 1));
        accomActivityList.add(insertCRInAccomActivity(propertyId, new LocalDate("2017-02-23").toDate(), "CR2", 1));
        accomActivityList.add(insertCRInAccomActivity(propertyId, new LocalDate("2017-02-23").toDate(), "CR1", 1));
        accomActivityList.add(insertCRInAccomActivity(propertyId, new LocalDate("2017-02-24").toDate(), "QS", 3));
        accomActivityList.add(insertCRInAccomActivity(propertyId, new LocalDate("2017-02-25").toDate(), "QR", 3));
        accomActivityList.add(insertCRInAccomActivity(propertyId, new LocalDate("2017-02-26").toDate(), "QS", 2));
        accomActivityList.add(insertCRInAccomActivity(propertyId, new LocalDate("2017-02-26").toDate(), "ZJ", 4));

        tenantCrudService.save(accomActivityList);
    }

    @ForTesting
    // use only for vaadin UI test.

    public void syncOutOfOrderData(int propertyId) {
        List<AccomActivity> accomActivityList = new ArrayList<AccomActivity>();
        accomActivityList.add(insertCRInAccomActivity(propertyId, new LocalDate("2016-12-2").toDate(), "CR2", 1));
        accomActivityList.add(insertCRInAccomActivity(propertyId, new LocalDate("2016-12-3").toDate(), "CR3", 1));
        accomActivityList.add(insertCRInAccomActivity(propertyId, new LocalDate("2016-12-4").toDate(), "CR1", 3));
        accomActivityList.add(insertCRInAccomActivity(propertyId, new LocalDate("2016-12-4").toDate(), "CR2", 2));

        tenantCrudService.save(accomActivityList);
    }

    @ForTesting
    // use only for vaadin UI test.

    public void markRoomTypeAsComponentRoom(int propertyId, int roomTypeId, String isComponentRoom) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("roomTypeId", roomTypeId);
        paramMap.put("isComponentRoom", isComponentRoom);
        tenantCrudService.executeUpdateByNativeQuery("update Accom_Type set isComponentRoom = :isComponentRoom where Accom_Type_ID = :roomTypeId", paramMap);
    }

    private CRMappingRoomNumbers setCrMappingRoomNumbers(int propertyId, Object crMappingListObject, String roomNumber) {
        CRMappingRoomNumbers crMappingRoomNumbers = new CRMappingRoomNumbers();
        crMappingRoomNumbers.setPropertyId(propertyId);
        crMappingRoomNumbers.setCrAccomTypeMapping((CRAccomTypeMapping) crMappingListObject);
        crMappingRoomNumbers.setCpRoomNumber(roomNumber);
        return crMappingRoomNumbers;
    }

    private CRAccomTypeMapping createCRAccomTypeMapping(int propertyId, String crAccomTypeCode, String cpAccomTypeCode, int cpAccomTypeQuantity) {
        CRAccomTypeMapping crAccomTypeMapping = new CRAccomTypeMapping();
        crAccomTypeMapping.setPropertyId(propertyId);
        AccomType crAccomType = tenantCrudService.findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", crAccomTypeCode).parameters());
        crAccomTypeMapping.setCrAccomType(crAccomType);
        AccomType cpAccomType = tenantCrudService.findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", cpAccomTypeCode).parameters());
        crAccomTypeMapping.setCpAccomType(cpAccomType);
        crAccomTypeMapping.setCpAccomTypeQuantity(cpAccomTypeQuantity);
        return crAccomTypeMapping;
    }

    private AccomType createAccomType(int propertyId, String accomTypeCode, int accomTypeCapacity, String isComponentRoom) {
        AccomType accomType = new AccomType();
        accomType.setPropertyId(propertyId);
        accomType.setAccomTypeCode(accomTypeCode);
        accomType.setAccomTypeCapacity(accomTypeCapacity);
        AccomClass accomClass = tenantCrudService.findByNamedQuerySingleResult(AccomClass.BY_CODE, QueryParameter
                .with(PROPERTY_ID, propertyId).and("code", "S").parameters());
        accomType.setAccomClass(accomClass);
        accomType.setSystemDefault(0);
        accomType.setIsComponentRoom(isComponentRoom);
        accomType.setStatusId(1);
        return accomType;
    }

    private CROutOfOrder insertOutOfOrder(int propertyId, Date occupancyDate, String accomTypeCode, int outOfOrder) {
        CROutOfOrder crOutOfOrder = new CROutOfOrder();
        crOutOfOrder.setPropertyId(propertyId);
        crOutOfOrder.setOccupancyDate(occupancyDate);
        AccomType accomTypeId = tenantCrudService.findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", accomTypeCode).parameters());
        crOutOfOrder.setAccomType(accomTypeId);
        crOutOfOrder.setOutOfOrder(outOfOrder);
        return crOutOfOrder;
    }

    private AccomActivity insertCRInAccomActivity(int propertyId, Date occupancyDate, String accomTypeCode, int roomsNotAvailableOther) {
        AccomActivity accomActivity = new AccomActivity();
        accomActivity.setPropertyId(propertyId);
        accomActivity.setOccupancyDate(occupancyDate);
        accomActivity.setSnapShotDate(new Date());
        List<AccomType> accomTypeList = tenantCrudService.findByNamedQuery(AccomType.BY_CODE, QueryParameter.with("code", accomTypeCode).parameters());
        accomActivity.setAccomTypeId(accomTypeList.get(0).getId());
        accomActivity.setAccomCapacity(new BigDecimal(accomTypeList.get(0).getAccomTypeCapacity()));
        accomActivity.setRoomsSold(new BigDecimal(0));
        accomActivity.setArrivals(new BigDecimal(0));
        accomActivity.setDepartures(new BigDecimal(0));
        accomActivity.setCancellations(new BigDecimal(0));
        accomActivity.setFoodRevenue(new BigDecimal(0));
        accomActivity.setNoShows(new BigDecimal(0));
        accomActivity.setRoomRevenue(new BigDecimal(0));
        accomActivity.setFileMetadataId(3);
        accomActivity.setTotalRevenue(new BigDecimal(0));
        accomActivity.setRoomsNotAvailableMaintenance(new BigDecimal(0));
        accomActivity.setRoomsNotAvailableOther(new BigDecimal(roomsNotAvailableOther));
        return accomActivity;
    }

    public List<ProbableAccomTypesCR> getNewProbableCRDetected() {
        return tenantCrudService.findByNamedQuery(ProbableAccomTypesCR.NEW_COMPONENT_ROOMS);
    }

    public Collection<OperaOccupancySummary> updateOperaOccupancySummary(List<OperaOccupancySummary> operaOccupancySummaries) {
        return tenantCrudService.save(operaOccupancySummaries);
    }

    public void evaluateNewComponentRoomTypeAlert() {
        final List<ProbableAccomTypesCR> newCRDetected = getNewProbableCRDetected();
        if (!newCRDetected.isEmpty()) {
            WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
            InfoMgrTypeEntity alertTypeEntity = alertService.getAlertType(ALERT_TYPE_NAME);
            StringBuilder details = new StringBuilder();
            newCRDetected.stream().forEach(rt -> {
                details.append(rt.getAccomTypeCode());
                details.append(" , ");
            });
            details.deleteCharAt(details.lastIndexOf(","));
            AlertType alertType = AlertType.valueOf(alertTypeEntity.getName());
            if (alertTypeEntity.isEnabled()) {
                alertService.createAlert(workContext, alertTypeEntity, NEW_CR_DETECTED, details.toString(), alertType);
            }
        }
    }

    private Map<String, List<CRoooNotUpdatedAlertDto>> getAlertDetailsAsMap(String details) {
        String[] strArr1 = details.split(OCCUPANCY_DATE + BLANK_SPACE);
        Map<String, List<CRoooNotUpdatedAlertDto>> detailsMap = new HashMap<>();

        for (String str1 : strArr1) {
            if (!"".equals(str1)) {
                String occupancyDate = str1.split("<\\/B>")[0];
                String[] strArr2 = str1.split("<BR\\/>");
                getDetailsMap(detailsMap, occupancyDate, strArr2);
            }
        }

        return detailsMap;
    }

    private void getDetailsMap(Map<String, List<CRoooNotUpdatedAlertDto>> detailsMap, String occupancyDate, String[] strArr2) {
        for (String str2 : strArr2) {
            if (str2.contains(FROM)) {
                String accomType = str2.split(OOO_ADJUSTED_FOR + BLANK_SPACE)[1].split(BLANK_SPACE + FROM)[0];
                int oldValue = Integer.parseInt(str2.split(FROM + BLANK_SPACE)[1].split("&nbsp;")[0]);
                int newValue = Integer.parseInt(str2.split(TO + BLANK_SPACE)[1].split("&nbsp;")[0]);
                CRoooNotUpdatedAlertDto dto = new CRoooNotUpdatedAlertDto(accomType, oldValue, newValue);
                List<CRoooNotUpdatedAlertDto> dtoList = detailsMap.get(occupancyDate);
                if (dtoList == null) {
                    dtoList = new ArrayList<CRoooNotUpdatedAlertDto>();
                }
                dtoList.add(dto);
                detailsMap.put(occupancyDate, dtoList);
            }
        }
    }

    public void evaluateCRoooNotUpdatedAlert(Map<String, List<CRoooNotUpdatedAlertDto>> crOooNotUpdatedAlertMap) {
        if (!crOooNotUpdatedAlertMap.isEmpty()) {
            WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
            InfoMgrTypeEntity alertTypeEntity = alertService.getAlertType(CR_OOO_NOT_UPDATED_ALERT);
            Alert alert = new Alert();
            AlertType alertType = AlertType.valueOf(alertTypeEntity.getName());
            alert.setPropertyId(workContext.getPropertyId());
            alert.setType(alertType);
            InfoMgrInstanceEntity existingAlert = alertService.findExistingAlert(alert);
            Map<String, List<CRoooNotUpdatedAlertDto>> existingDetailsMap = null;
            Map<String, Set<CRoooNotUpdatedAlertDto>> finalDetailsMap = new TreeMap<>();
            if (existingAlert != null) {
                String existingDetailsStr = existingAlert.getDetails();
                try {
                    existingDetailsMap = getAlertDetailsAsMap(existingDetailsStr);
                } catch (Exception e) {
                    LOGGER.error("Failed while parsing the existing alert details. Ignored for now...", e);
                }
            }
            populateDetailsMap(finalDetailsMap, crOooNotUpdatedAlertMap);
            populateDetailsMap(finalDetailsMap, existingDetailsMap);
            StringBuilder details = new StringBuilder();
            finalDetailsMap.forEach((key, value) -> {
                details.append(OCCUPANCY_DATE + BLANK_SPACE + key + "</B><BR/>");
                value.stream().forEach(dto -> {
                    details.append(" " + OOO_ADJUSTED_FOR + BLANK_SPACE + dto.getAccomTypeCode() + BLANK_SPACE + FROM + BLANK_SPACE + dto.getExistingOOO() + "&nbsp; " + TO + BLANK_SPACE + dto.getAdjustedOOO() + "&nbsp;<BR/> ");
                });
            });

            if (alertTypeEntity.isEnabled()) {
                alertService.createAlert(workContext, alertTypeEntity, CR_OOO_NOT_UPDATED_DESCRIPTION, details.toString().replace("  ", BLANK_SPACE), alertType);
            }
        }
    }

    private void populateDetailsMap(Map<String, Set<CRoooNotUpdatedAlertDto>> finalDetailsMap, Map<String, List<CRoooNotUpdatedAlertDto>> map) {
        if (map != null) {
            map.forEach((key, value) -> {
                Set<CRoooNotUpdatedAlertDto> finalSet = finalDetailsMap.get(key);
                if (finalSet == null) {
                    finalSet = new TreeSet<>();
                }
                finalSet.addAll(value);
                finalDetailsMap.put(key, finalSet);
            });
        }
    }

    public String getComponentRoomName(Integer roomTypeID) {
        AccomType accomType = tenantCrudService.findByNamedQuerySingleResult(AccomType.ALL_BY_ACCOM_TYPE_ID, QueryParameter.with("id", roomTypeID).parameters());
        if (accomType.isComponentRoom()) {
            return accomType.getName();
        }
        return "";
    }

    public List<ComponentRoomsConfiguration> getComponentRoomsConfigurations() {
        List<CRAccomTypeMapping> componentRoomsMappings = getComponentRoomsMappings();

        if (noRecords(componentRoomsMappings)) {
            LOGGER.info("No component Rooms configuration exist");
            return null;
        }

        return getTreeComponent(createCRConfigsWithOneLevelChildren(componentRoomsMappings));
    }

    public ArrayList<AccomType> getAccomTypesInvolved() {
        List<ComponentRoomsConfiguration> componentRoomsConfigurations = getComponentRoomsConfigurations();
        if (componentRoomsConfigurations == null || componentRoomsConfigurations.isEmpty()) {
            return new ArrayList<>();
        }
        LinkedHashSet<AccomType> accomTypesInvolved = new LinkedHashSet<>();
        componentRoomsConfigurations.forEach(config -> accomTypesInvolved.addAll(config.getAllAccomTypesInvolved()));
        return new ArrayList(accomTypesInvolved);
    }

    public List<Integer> getAccomTypeIdsInvolved() {
        return getAccomTypesInvolved().stream().map(AccomType::getId).collect(Collectors.toList());
    }

    private List<ComponentRoomsConfiguration> createCRConfigsWithOneLevelChildren(List<CRAccomTypeMapping> crAccomTypeMappings) {
        Map<AccomType, List<CRAccomTypeMapping>> parentChildMapping =
                crAccomTypeMappings.stream().collect(Collectors.groupingBy(CRAccomTypeMapping::getCrAccomType));

        ArrayList<ComponentRoomsConfiguration> configurationUIWrappers = new ArrayList<>();
        parentChildMapping.forEach((componentRoomType, crAccomTypeMappingList) -> {
            ComponentRoomsConfiguration parent = new ComponentRoomsConfiguration(componentRoomType);

            for (CRAccomTypeMapping cpAccomTypeMapping : crAccomTypeMappingList) {
                AccomType physicalRoomType = cpAccomTypeMapping.getCpAccomType();
                int accomTypeQuantity = cpAccomTypeMapping.getCpAccomTypeQuantity();
                Set<CRMappingRoomNumbers> roomNumbers = cpAccomTypeMapping.getCrMappingRoomNumberses();
                ComponentRoomsConfiguration child = new ComponentRoomsConfiguration(parent, physicalRoomType, accomTypeQuantity);
                child.setRoomNumbers(roomNumbers);
                parent.getChildren().add(child);
            }
            configurationUIWrappers.add(parent);
        });
        return configurationUIWrappers;
    }

    private List<ComponentRoomsConfiguration> getTreeComponent(List<ComponentRoomsConfiguration> crConfigsWithOneLevelChildren) {
        Set<AccomType> objectsToRemove = new HashSet<>();
        List<ComponentRoomsConfiguration> newConfigurations = new ArrayList<>();

        Map<AccomType, ComponentRoomsConfiguration> accomTypeCRConfigMap = crConfigsWithOneLevelChildren.stream()
                .collect(Collectors.toMap(ComponentRoomsConfiguration::getAccomType, Function.identity()));

        crConfigsWithOneLevelChildren.stream().forEach(crConfiguration -> {
            ComponentRoomsConfiguration newCRConfig = populateAllChildConfigs(objectsToRemove, accomTypeCRConfigMap, crConfiguration);
            newConfigurations.add(newCRConfig);
        });
        return filterUnwantedCrConfigsWhichAreChild(newConfigurations, objectsToRemove);
    }

    private ComponentRoomsConfiguration populateAllChildConfigs(Set<AccomType> objectsToRemove, Map<AccomType, ComponentRoomsConfiguration> accomTypeCRConfigMap, ComponentRoomsConfiguration componentRoomsConfiguration) {
        ComponentRoomsConfiguration newConfig = componentRoomsConfiguration.getDeepCopy();
        setAllRecursiveChilds(newConfig, accomTypeCRConfigMap);
        markAllComponentChildConfigsToDelete(objectsToRemove, newConfig);
        return newConfig;
    }

    private void markAllComponentChildConfigsToDelete(Set<AccomType> objectsToRemove, ComponentRoomsConfiguration newConfig) {
        objectsToRemove.addAll(newConfig.getChildren().stream()
                .filter(ComponentRoomsConfiguration::isComponent)
                .map(ComponentRoomsConfiguration::getComponentRoomType)
                .collect(Collectors.toList()));
    }

    private void setAllRecursiveChilds(ComponentRoomsConfiguration parentConfig, Map<AccomType, ComponentRoomsConfiguration> accomTypeCRConfigMap) {
        List<ComponentRoomsConfiguration> oldChildren = parentConfig.getChildren();
        List<ComponentRoomsConfiguration> newChildren = new ArrayList<>();
        oldChildren.stream()
                .forEach(oldChild -> {
                    if (isComponent(oldChild)) {
                        ComponentRoomsConfiguration newChild = createNewChildConfig(parentConfig, accomTypeCRConfigMap, oldChild);
                        newChildren.add(newChild);
                        setAllRecursiveChilds(newChild, accomTypeCRConfigMap);
                    } else {
                        newChildren.add(oldChild);
                    }
                });
        parentConfig.setChildren(newChildren);
    }

    private ComponentRoomsConfiguration createNewChildConfig(ComponentRoomsConfiguration parentConfig, Map<AccomType, ComponentRoomsConfiguration> accomTypeCRConfigMap, ComponentRoomsConfiguration oldConfig) {
        AccomType crAccomType = oldConfig.getPhysicalRoomType();
        ComponentRoomsConfiguration newChild = accomTypeCRConfigMap.get(crAccomType).getDeepCopy();
        newChild.setComponentRoomType(crAccomType);
        newChild.setPhysicalRoomType(null);
        newChild.setQuantity(oldConfig.getQuantity());
        newChild.setRoomNumbers(oldConfig.getRoomNumbers());
        newChild.setParent(parentConfig);
        return newChild;
    }

    private List<ComponentRoomsConfiguration> filterUnwantedCrConfigsWhichAreChild(List<ComponentRoomsConfiguration> componentRoomsConfigurations, Set<AccomType> objectsToRemove) {
        return componentRoomsConfigurations.stream()
                .filter(conf -> !(objectsToRemove.contains(conf.getComponentRoomType())))
                .collect(Collectors.toList());
    }

    private boolean isComponent(ComponentRoomsConfiguration child) {
        return child.getPhysicalRoomType() != null && child.getPhysicalRoomType().isComponentRoom();
    }

    public Map<Integer, ComponentRoomMultiplier> computeComponentRoomMultiplier(List<ComponentRoomsConfiguration> componentRoomsConfigurations) {
        Map<Integer, ComponentRoomMultiplier> componentRoomMultipliers = new HashMap<>();

        if (noRecords(componentRoomsConfigurations)) {
            return componentRoomMultipliers;
        }

        componentRoomsConfigurations.stream().forEach(componentRoomsConfiguration -> populateMultiplier(componentRoomMultipliers, componentRoomsConfiguration));
        return componentRoomMultipliers;
    }

    private void populateMultiplier(Map<Integer, ComponentRoomMultiplier> componentRoomMultipliers, ComponentRoomsConfiguration componentRoomsConfiguration) {
        if (componentRoomsConfiguration.hasChildren()) {
            Map<Integer, Integer> parentComponentPhysicalList = new HashMap<>();
            componentRoomsConfiguration.getChildren().forEach(child -> {
                if (child.isComponent()) {
                    if (isMultiplierNotComputed(componentRoomMultipliers, child)) {
                        populateMultiplier(componentRoomMultipliers, child);
                    }
                    copyChildComponentPhysicalRoomsToParentComponent(parentComponentPhysicalList, child, componentRoomMultipliers);
                } else {
                    parentComponentPhysicalList.merge(child.getAccomType().getId(), child.getQuantity(), Integer::sum);
                }
            });
            componentRoomMultipliers.put(componentRoomsConfiguration.getComponentRoomType().getId(), new ComponentRoomMultiplier(parentComponentPhysicalList));
        }
    }

    private void copyChildComponentPhysicalRoomsToParentComponent(Map<Integer, Integer> componentPhysicalList, ComponentRoomsConfiguration child, Map<Integer, ComponentRoomMultiplier> componentRoomMultipliers) {
        ComponentRoomMultiplier componentRoomMultiplier = getMultiplier(componentRoomMultipliers, child);
        Map<Integer, Integer> componentPhysicalListOfChild = componentRoomMultiplier.getPhysicalList();
        componentPhysicalListOfChild.forEach((accomTypeId, multiplier) -> componentPhysicalList.merge(accomTypeId, child.getQuantity() * multiplier, Integer::sum));
    }

    private ComponentRoomMultiplier getMultiplier(Map<Integer, ComponentRoomMultiplier> componentRoomMultipliers, ComponentRoomsConfiguration child) {
        return componentRoomMultipliers.get(child.getComponentRoomType().getId());
    }

    private boolean isMultiplierNotComputed(Map<Integer, ComponentRoomMultiplier> componentRoomMultipliers, ComponentRoomsConfiguration child) {
        return getMultiplier(componentRoomMultipliers, child) == null;
    }

    private boolean noRecords(Collection collection) {
        return collection == null || collection.isEmpty();
    }

    public List<String> getDistinctAnalyticalMarketSegment() {
        List<YieldCategoryRule> yieldCategoryRules = tenantCrudService.findByNamedQuery(YieldCategoryRule.ALL);
        List<String> ams = yieldCategoryRules.stream().map(YieldCategoryRule::getAnalyticalMarketCode).distinct().collect(Collectors.toList());
        return new ArrayList<>(ams);
    }

    public int updatePaceMktWithCorrection() {
        return tenantCrudService.executeUpdateByNamedQuery(PaceMktSegActivity.UPDATE_WITH_PHYSICAL_SOLDS_ONLY);
    }

    public void updatePaceAccomRevenueFromNonPace() {
        tenantCrudService.executeUpdateByNamedQuery(PaceAccomActivity.UPDATE_REVENUE_SPLIT_FROM_NON_PACE,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public Map<String, Set<ComponentRoomsConfiguration>> getConfigurationImediateParents(List<ComponentRoomsConfiguration> confs) {
        Map<String, Set<ComponentRoomsConfiguration>> confMap = new HashMap<>();
        confs.forEach(conf -> {
            populateConfParents(confMap, conf);
        });
        return confMap;
    }

    private void populateConfParents(Map<String, Set<ComponentRoomsConfiguration>> confMap, ComponentRoomsConfiguration conf) {
        conf.getChildren().forEach(child -> {
            if (child.isComponent()) {
                populateConfParents(confMap, child);
            }
            String accomTypeCode = child.getAccomType().getAccomTypeCode();
            if (confMap.containsKey(accomTypeCode)) {
                if (confMap.get(accomTypeCode).stream().noneMatch(tempConf -> tempConf.getAccomType().getAccomTypeCode().equalsIgnoreCase(conf.getAccomType().getAccomTypeCode()))) {
                    confMap.get(accomTypeCode).add(conf);
                }
            } else {
                Set<ComponentRoomsConfiguration> parents = new HashSet<ComponentRoomsConfiguration>() {{
                    add(conf);
                }};
                confMap.put(accomTypeCode, parents);
            }
        });
    }

    public CROutOfOrder getCROutOfOrderForAccomTypeOfDate(Integer propertyId, Date occupancyDate, Integer accomTypeId) {
        return tenantCrudService.findByNamedQuerySingleResult(CROutOfOrder.BY_OCCUPANCY_DATE_ACCOM_TYPE_ID_AND_PROPERTY_ID,
                QueryParameter.with("occupancyDate", occupancyDate).and("accomTypeId", accomTypeId).and(PROPERTY_ID, propertyId).parameters());
    }

    public void correctTotalActivityData(boolean isBDE) {
        tenantCrudService.executeUpdateByNamedQuery(TotalActivity.CORRECT_TOTAL_ACTIVITY_FOR_CR);
        if (isBDE) {
            tenantCrudService.executeUpdateByNamedQuery(PaceTotalActivity.CORRECT_PACE_TOTAL_ACTIVITY_FOR_CR);
        }
    }

    public List<String> getCompRoomAndParticipantRTCodes() {
        return tenantCrudService.findByNamedQuery(AccomType.GET_COMP_RT_AND_PARTICIPANT_RT_CODE);
    }

    public void populateCRConfigurationHistory() {
        Date caughtUpDate = getCaughtUpDate();

        saveCrAccomTypeMappingHistoriesFromMainTable(caughtUpDate);
        saveCrMappingRoomNumbersHistoriesFromMainTable(caughtUpDate);
    }

    private void saveCrMappingRoomNumbersHistoriesFromMainTable(Date caughtUpDate) {
        List<CRMappingRoomNumbers> existingRoomNumbersConfig = tenantCrudService.findAll(CRMappingRoomNumbers.class);
        List<CRMappingRoomNumbersHistory> crMappingRoomNumbersHistories = new ArrayList<>();

        for (CRMappingRoomNumbers crMappingRoomNumbers : existingRoomNumbersConfig) {
            CRMappingRoomNumbersHistory crMappingRoomNumbersHistory = new CRMappingRoomNumbersHistory();
            crMappingRoomNumbersHistory.setCrMappingRoomNumbersId(crMappingRoomNumbers.getId());
            crMappingRoomNumbersHistory.setPropertyId(crMappingRoomNumbers.getPropertyId());
            crMappingRoomNumbersHistory.setCrAccomTypeMapping(crMappingRoomNumbers.getCrAccomTypeMapping());
            crMappingRoomNumbersHistory.setCpRoomNumber(crMappingRoomNumbers.getCpRoomNumber());
            crMappingRoomNumbersHistory.setSnapshotDateTime(caughtUpDate);
            crMappingRoomNumbersHistories.add(crMappingRoomNumbersHistory);
        }
        if (!crMappingRoomNumbersHistories.isEmpty()) {
            tenantCrudService.save(crMappingRoomNumbersHistories);
        }
    }

    private void saveCrAccomTypeMappingHistoriesFromMainTable(Date caughtUpDate) {
        List<CRAccomTypeMapping> existingMappedCRConfigs = getMappedComponentRooms();
        List<CRAccomTypeMappingHistory> crAccomTypeMappingHistories = new ArrayList<>();

        for (CRAccomTypeMapping crAccomTypeMapping : existingMappedCRConfigs) {
            CRAccomTypeMappingHistory crAccomTypeMappingHistory = new CRAccomTypeMappingHistory();
            crAccomTypeMappingHistory.setCrAccomTypeMappingId(crAccomTypeMapping.getId());
            crAccomTypeMappingHistory.setPropertyId(crAccomTypeMapping.getPropertyId());
            crAccomTypeMappingHistory.setCrAccomType(crAccomTypeMapping.getCrAccomType());
            crAccomTypeMappingHistory.setCpAccomType(crAccomTypeMapping.getCpAccomType());
            crAccomTypeMappingHistory.setCpAccomTypeQuantity(crAccomTypeMapping.getCpAccomTypeQuantity());
            crAccomTypeMappingHistory.setCrAccomTypeCapacity(crAccomTypeMapping.getCrAccomType().getAccomTypeCapacity());
            crAccomTypeMappingHistory.setSnapshotDateTime(caughtUpDate);
            crAccomTypeMappingHistories.add(crAccomTypeMappingHistory);
        }
        if (!crAccomTypeMappingHistories.isEmpty()) {
            tenantCrudService.save(crAccomTypeMappingHistories);
        }
    }

    public Date getCaughtUpDate() {
        return dateService.getCaughtUpDate();
    }

    private Date getCaughtUpDateFromStage() {
        return roomTypeRecodingService.getCaughtUpDate();
    }

    public boolean isConfigurationAlreadySavedToHistory() {
        Date caughtUpDate = getCaughtUpDate();
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("caughtupDate", caughtUpDate);
        Long count = tenantCrudService.findByNamedQuerySingleResult(CRAccomTypeMappingHistory.GET_COUNT_CONFIG_FOR_SNAPSHOT_DATE, paramMap);
        return count > 0;
    }

    public void restoreOriginalComponentRoomConfiguration() {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("caughtupDate", getCaughtUpDateFromStage());
        List<CRAccomTypeMappingHistory> crAccomTypeMappingHistories =
                tenantCrudService.findByNamedQuery(CRAccomTypeMappingHistory.GET_CONFIGURATION_FOR_CAUGHTUP_DATE, paramMap);

        if (!crAccomTypeMappingHistories.isEmpty()) {
            tenantCrudService.deleteAll(CRMappingRoomNumbers.class);
            tenantCrudService.deleteAll(CRAccomTypeMapping.class);
            tenantCrudService.executeUpdateByNamedQuery(AccomType.UPDATE_ACCOM_TYPE_RESET_ISCOMPONENTROOM);
            tenantCrudService.flushAndClear();
            restoreCapacitiesOfComponentRooms(crAccomTypeMappingHistories);
            tenantCrudService.executeUpdateByNativeQuery(SAVE_CR_ACCOM_TYPE_MAPPING_FROM_HISTORY, paramMap);
            tenantCrudService.executeUpdateByNativeQuery(SAVE_CR_MAPPING_ROOM_NUMBERS_FROM_HISTORY, paramMap);
        }
    }

    private void restoreCapacitiesOfComponentRooms(List<CRAccomTypeMappingHistory> crAccomTypeMappingHistories) {
        Set<AccomType> accomTypes = new HashSet<>();
        for (CRAccomTypeMappingHistory configHistory : crAccomTypeMappingHistories) {
            AccomType at = configHistory.getCrAccomType();
            at.setAccomTypeCapacity(configHistory.getCrAccomTypeCapacity());
            at.setIsComponentRoom("Y");
            accomTypes.add(at);
        }
        tenantCrudService.save(accomTypes);
    }

    public Date getConfigurationSnapshotDate() {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("caughtupDate", getCaughtUpDateFromStage());
        return tenantCrudService.findByNamedQuerySingleResult(CRAccomTypeMappingHistory.GET_CONFIGURATION_SNAPSHOT_DATE_FOR_CAUGHTUP_DATE, paramMap);
    }

    public void zeroOutRemainingCapacityForDiscontinuedRTs(boolean isBDE) {
        LOGGER.info("Round off Remaining Capacity For Discontinued RTs");
        int fileMetaDataId = tenantCrudService.findByNamedQuerySingleResult(AccomActivity.GET_MAX_FILE_METADATA_ID);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("fileMetadataId", fileMetaDataId);
        tenantCrudService.executeUpdateByNamedQuery(CRAccomActivity.ROUNDOFF_REMAINING_CAPACITY_FOR_DISCONTINUED_RT, paramMap);
        if (isBDE) {
            tenantCrudService.executeUpdateByNamedQuery(PaceCRAccomActivity.ROUNDOFF_REMAINING_CAPACITY_FOR_DISCONTINUED_RT, paramMap);
        }
    }

    public void deleteNonCRConfigPaticipants() {
        List<Integer> accomTypeIdList = tenantCrudService.findByNamedQuery(CRAccomTypeMapping.GET_COMP_ROOM_AND_PARTICIANTS);
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("accomTypeIds", accomTypeIdList);
        tenantCrudService.executeUpdateByNamedQuery(CRAccomActivity.DELETE_NON_CR_AND_NON_PARTICIPANTS, paramMap);
    }

    public boolean isNotificationEnabledForOrphanRTsMappingChange() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.NOTIFY_ORPHAN_ROOM_TYPES_MAPPING_CHANGE.value());
    }


    public List<String> findAccomTypesWithSingleCountedNegativeCapacity() {
        List<CRAccomTypeMapping> componentRoomsMappings = getComponentRoomsMappings();
        return findAccomTypesWithSingleCountedNegativeCapacity(componentRoomsMappings);
    }

    public List<String> findAccomTypesWithSingleCountedNegativeCapacity(List<CRAccomTypeMapping> componentRoomsMappings) {
        Map<Integer, Integer> atToAvailableCapacityMap = new HashMap<>();
        Map<Integer, String> atToAccomTypeCode = new HashMap<>();
        componentRoomsMappings.stream().forEach(mapping -> {
            atToAvailableCapacityMap.putIfAbsent(mapping.getCpAccomType().getId(), mapping.getCpAccomType().getAccomTypeCapacity());
            atToAccomTypeCode.putIfAbsent(mapping.getCrAccomType().getId(), mapping.getCrAccomType().getAccomTypeCode());
            atToAccomTypeCode.putIfAbsent(mapping.getCpAccomType().getId(), mapping.getCpAccomType().getAccomTypeCode());
        });
        LOGGER.info("Accom Type Map " + atToAccomTypeCode);
        LOGGER.info("Physical Capacity Map " + atToAvailableCapacityMap);
        Map<Integer, List<CRAccomTypeMapping>> cpToMapping = componentRoomsMappings.stream().collect(Collectors.groupingBy(crMapping -> crMapping.getCpAccomType().getId()));
        Map<Integer, Integer> atToUsedCapacityMap = new HashMap<>();
        cpToMapping.entrySet().forEach(entry -> atToUsedCapacityMap.putIfAbsent(entry.getKey(), getSumOfCapacity(entry)));
        LOGGER.info("Used Capacity Map " + atToUsedCapacityMap);
        List<String> msgList = new ArrayList<>();
        atToUsedCapacityMap.entrySet().forEach(entry -> {
            Integer accomTypeId = entry.getKey();
            if (atToAvailableCapacityMap.containsKey(accomTypeId)) {
                if (atToUsedCapacityMap.get(accomTypeId) > atToAvailableCapacityMap.get(accomTypeId)) {
                    msgList.add(atToAccomTypeCode.get(accomTypeId));
                    LOGGER.info("AccomTypeCode " + atToAccomTypeCode.get(accomTypeId) + " has single counted negative capacity");
                }
            }
        });
        return msgList;
    }

    private Integer getSumOfCapacity(Map.Entry<Integer, List<CRAccomTypeMapping>> entry) {
        return entry.getValue().stream()
                .map(mapping -> mapping.getCrAccomType().getAccomTypeCapacity() * mapping.getCpAccomTypeQuantity())
                .reduce(0, Integer::sum);
    }


    public List<CROrphanMappingDto> findOrphanMappingsFromSavedCRAccomTypeMappings() {
        List<CROrphanMappingDto> orphanMappings = new ArrayList<>();
        List<ComponentRoomsConfiguration> componentRoomsConfigurations = createCRConfigsWithOneLevelChildren(getComponentRoomsMappings());

        Map<Integer, Map<Integer, Long>> computedOrphanMappings = findOrphanRoomTypesWithQuantityForComponentRooms(componentRoomsConfigurations);
        computedOrphanMappings.forEach((crAccomTypeId, orphanAndQuantity) ->
                orphanAndQuantity.forEach((cpAccomTypeId, cpAccomTypeQuantity) -> {
                    AccomType crAccomType = tenantCrudService.find(AccomType.class, crAccomTypeId);
                    AccomType cpAccomType = tenantCrudService.find(AccomType.class, cpAccomTypeId);
                    orphanMappings.add(new CROrphanMappingDto(crAccomType.getAccomTypeCode(), cpAccomType.getAccomTypeCode(), cpAccomTypeQuantity));
                })
        );

        return orphanMappings;
    }


    public List<String> saveOrphanMappingsWithoutComputation(List<CROrphanMappingDto> orphanMappings) {
        deleteAllOrphanMappings();

        if (CollectionUtils.isEmpty(orphanMappings)) {
            return Collections.emptyList();
        }

        List<String> responseMessages = new ArrayList<>();

        for (CROrphanMappingDto orphanMapping : orphanMappings) {
            AccomType crAccomType = tenantCrudService.findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", orphanMapping.getCrAccomTypeCode()).parameters());
            AccomType cpAccomType = tenantCrudService.findByNamedQuerySingleResult(AccomType.BY_CODE, QueryParameter.with("code", orphanMapping.getCpAccomTypeCode()).parameters());
            if (crAccomType != null && cpAccomType != null) {
                CROrphanMapping crOrphanMapping = createCROrphanMapping(crAccomType.getId(), cpAccomType.getId(), orphanMapping.getCpAccomTypeQuantity());
                tenantCrudService.save(crOrphanMapping);
            } else {
                if (crAccomType == null) {
                    responseMessages.add("Accom Type not found: " + orphanMapping.getCrAccomTypeCode());
                }
                if (cpAccomType == null) {
                    responseMessages.add("Accom Type not found: " + orphanMapping.getCpAccomTypeCode());
                }
            }
        }

        return responseMessages;
    }

    public void saveOrphanMappingsForComponentRooms(List<ComponentRoomsConfiguration> componentRoomsConfigurations) {
        Map<Integer, Map<Integer, Long>> computedOrphanMappings = findOrphanRoomTypesWithQuantityForComponentRooms(componentRoomsConfigurations);

        deleteAllOrphanMappings();

        computedOrphanMappings.forEach((crAccomTypeId, orphanAndQuantityMappings) -> {
            List<CROrphanMapping> crOrphanMappings = createOrphanMappings(crAccomTypeId, orphanAndQuantityMappings);
            tenantCrudService.save(crOrphanMappings);
        });
    }

    public NavigableMap<Integer, List<ComponentRoomsConfiguration>> findLevelsOfMappings(List<ComponentRoomsConfiguration> configurations) {
        NavigableMap<Integer, List<ComponentRoomsConfiguration>> levels = new TreeMap<>();

        Map<AccomType, List<ComponentRoomsConfiguration>> groupedParentMappings = groupParentMappingsByChildren(configurations);

        configurations.stream().filter(ComponentRoomsConfiguration::isParent).forEach(mapping -> {
            int level = levelOf(mapping, groupedParentMappings);

            List<ComponentRoomsConfiguration> mappings = levels.getOrDefault(level, new ArrayList<>());
            mappings.add(mapping);
            levels.put(level, mappings);
        });

        return levels;
    }

    public Map<Integer, Integer> findLevelOfEachComponentRoomType(List<ComponentRoomsConfiguration> configurations) {
        Map<AccomType, List<ComponentRoomsConfiguration>> groupedParentMappings = groupParentMappingsByChildren(configurations);

        Map<Integer, Integer> levels = new HashMap<>();
        configurations.stream().filter(ComponentRoomsConfiguration::isParent).forEach(mapping -> {
            int level = levelOf(mapping, groupedParentMappings);
            levels.put(mapping.getComponentRoomType().getId(), level);
        });

        return levels;
    }

    public Map<Integer, Map<Integer, Long>> findCompositionsAndOccurrencesConsideringQuantity(List<ComponentRoomsConfiguration> configurations) {
        Map<Integer, Map<Integer, Long>> compositions = new HashMap<>();

        Map<AccomType, List<ComponentRoomsConfiguration>> groupedParentMappings = groupParentMappingsByChildren(configurations);

        configurations.stream().filter(ComponentRoomsConfiguration::isParent).forEach(mapping -> {
            List<AccomType> physicalRoomTypes = new ArrayList<>();
            mapping.getChildren().forEach(m -> findPhysicalAccomTypesConsideringQuantity(m, m.getQuantity(), groupedParentMappings, physicalRoomTypes));

            if (!physicalRoomTypes.isEmpty()) {
                Map<Integer, Long> occurrences = physicalRoomTypes.stream().collect(Collectors.groupingBy(AccomType::getId, Collectors.counting()));
                compositions.put(mapping.getComponentRoomType().getId(), occurrences);
            }
        });

        return compositions;
    }

    public void deleteAllOrphanMappings() {
        tenantCrudService.executeUpdateByNamedQuery(CROrphanMapping.DELETE_ALL_MAPPINGS);
    }

    public List<CROrphanMapping> findOrphanMappingsOf(Integer crAccomTypeId) {
        return tenantCrudService.findByNamedQuery(CROrphanMapping.FIND_BY_CR_ACCOM_TYPE_ID, QueryParameter.with("crAccomTypeId", crAccomTypeId).parameters());
    }

    public boolean isCompositionSubset(Map<Integer, Long> parentComposition, Map<Integer, Long> probableSubsetComposition) {
        if (probableSubsetComposition.size() > parentComposition.size()) {
            return false;
        }

        for (Map.Entry<Integer, Long> occurrenceEntry : probableSubsetComposition.entrySet()) {
            if (!parentComposition.containsKey(occurrenceEntry.getKey()) || occurrenceEntry.getValue().compareTo(parentComposition.get(occurrenceEntry.getKey())) > 0) {
                return false;
            }
        }

        return true;
    }

    public CROrphanMapping createCROrphanMapping(Integer crAccomTypeId, Integer orphanAccomTypeId, Long quantity) {
        AccomType crAccomType = tenantCrudService.find(AccomType.class, crAccomTypeId);
        AccomType orphanAccomType = tenantCrudService.find(AccomType.class, orphanAccomTypeId);

        CROrphanMapping crOrphanMapping = new CROrphanMapping();
        crOrphanMapping.setCrAccomType(crAccomType);
        crOrphanMapping.setCpAccomType(orphanAccomType);
        crOrphanMapping.setCpAccomTypeQuantity(quantity);
        crOrphanMapping.setPropertyId(PacmanWorkContextHelper.getPropertyId());

        return crOrphanMapping;
    }

    private int levelOf(ComponentRoomsConfiguration mapping, Map<AccomType, List<ComponentRoomsConfiguration>> groupedParentMappings) {
        List<ComponentRoomsConfiguration> childHierarchy = groupedParentMappings.get(mapping.getPhysicalRoomType() != null ? mapping.getPhysicalRoomType() : mapping.getComponentRoomType());
        if (childHierarchy == null) {
            return 1;
        }

        int max = 1;
        for (ComponentRoomsConfiguration it : childHierarchy) {
            if (it.getPhysicalRoomType().isComponentRoom()) {
                max = Math.max(max, levelOf(it, groupedParentMappings));
            }
        }

        return max + 1;
    }

    private Map<AccomType, List<ComponentRoomsConfiguration>> groupParentMappingsByChildren(List<ComponentRoomsConfiguration> items) {
        return items.stream()
                .filter(ComponentRoomsConfiguration::isParent)
                .collect(Collectors.toMap(ComponentRoomsConfiguration::getComponentRoomType, ComponentRoomsConfiguration::getChildren));
    }

    private List<Integer> findOrphanRoomTypes(Map<AccomType, List<ComponentRoomsConfiguration>> groupedParentMappings, Map<Integer, Integer> levelOfEachComponentRoomType, Integer maxLevel) {
        List<AccomType> mappedAccomTypes = groupedParentMappings.values().stream().flatMap(List::stream).map(ComponentRoomsConfiguration::getPhysicalRoomType).collect(Collectors.toList());

        // Level Predicate - Orphan CR's level will always be less than CR with max level. And CR with max level will not be orphan in any case.
        Predicate<AccomType> levelPredicate = at -> levelOfEachComponentRoomType.get(at.getId()) < maxLevel;

        // Orphan Predicate - Not a part of any other CR i.e. no other CR exists having current CR as its constituent RT
        Predicate<AccomType> orphanPredicate = at -> !mappedAccomTypes.contains(at);

        Predicate<AccomType> orphanPredicateCriteria = levelPredicate.and(orphanPredicate);

        return groupedParentMappings.keySet().stream().filter(orphanPredicateCriteria).map(AccomType::getId).collect(Collectors.toList());
    }

    private void findPhysicalAccomTypesConsideringQuantity(ComponentRoomsConfiguration mapping, int composedQuantity, Map<AccomType, List<ComponentRoomsConfiguration>> groupedParentMappings, List<AccomType> physicalATs) {
        List<ComponentRoomsConfiguration> children = groupedParentMappings.get(mapping.getPhysicalRoomType());
        if (children == null) {
            IntStream.rangeClosed(1, composedQuantity).forEach(i -> physicalATs.add(mapping.getPhysicalRoomType()));
            return;
        }

        for (ComponentRoomsConfiguration child : children) {
            if (!child.getPhysicalRoomType().isComponentRoom()) {
                IntStream.rangeClosed(1, composedQuantity * child.getQuantity()).forEach(i -> physicalATs.add(child.getPhysicalRoomType()));
            } else {
                findPhysicalAccomTypesConsideringQuantity(child, composedQuantity * child.getQuantity(), groupedParentMappings, physicalATs);
            }
        }
    }

    private Map<Integer, List<Integer>> findOrphanRoomTypesForComponentRooms(List<ComponentRoomsConfiguration> configurations) {
        Map<Integer, List<Integer>> orphanParents = new HashMap<>();

        Map<AccomType, List<ComponentRoomsConfiguration>> groupedParentMappings = groupParentMappingsByChildren(configurations);

        Map<Integer, Integer> levelOfEachComponentRoomType = findLevelOfEachComponentRoomType(configurations);
        NavigableMap<Integer, List<ComponentRoomsConfiguration>> levelsOfMappings = findLevelsOfMappings(configurations);
        Integer maxLevel = levelsOfMappings.lastKey();

        Map<Integer, Map<Integer, Long>> compositions = findCompositionsAndOccurrencesConsideringQuantity(configurations);

        List<Integer> orphanAccomTypeIds = findOrphanRoomTypes(groupedParentMappings, levelOfEachComponentRoomType, maxLevel);

        orphanAccomTypeIds.forEach(orphanAccomTypeId -> {
            Integer orphanAccomTypeLevel = levelOfEachComponentRoomType.get(orphanAccomTypeId);
            List<Integer> parentAccomTypeIds = findParentsOfOrphanRoomType(orphanAccomTypeId, orphanAccomTypeLevel, levelsOfMappings, compositions);

            populateParentAndOrphansMapping(orphanAccomTypeId, parentAccomTypeIds, orphanParents);
        });

        return orphanParents;
    }

    private void populateParentAndOrphansMapping(Integer orphanAccomTypeId, List<Integer> parentAccomTypeIds, Map<Integer, List<Integer>> orphanParents) {
        parentAccomTypeIds.forEach(parentAccomTypeId -> {
            List<Integer> existingOrphanRoomTypeIds = orphanParents.getOrDefault(parentAccomTypeId, new ArrayList<>());
            existingOrphanRoomTypeIds.add(orphanAccomTypeId);
            orphanParents.put(parentAccomTypeId, existingOrphanRoomTypeIds);
        });
    }

    private List<Integer> findParentsOfOrphanRoomType(Integer orphanAccomTypeId,
                                                      Integer orphanAccomTypeLevel,
                                                      NavigableMap<Integer, List<ComponentRoomsConfiguration>> levelsOfMappings,
                                                      Map<Integer, Map<Integer, Long>> compositions) {
        List<Integer> parentAccomTypeIds = new ArrayList<>();
        SortedMap<Integer, List<ComponentRoomsConfiguration>> filteredLevels = levelsOfMappings.tailMap(orphanAccomTypeLevel, false);
        Map<Integer, Long> orphanAccomTypeComposition = compositions.get(orphanAccomTypeId);

        for (SortedMap.Entry<Integer, List<ComponentRoomsConfiguration>> filteredLevel : filteredLevels.entrySet()) {
            if (!parentAccomTypeIds.isEmpty()) {
                break;
            }

            parentAccomTypeIds.addAll(
                    filteredLevel
                            .getValue()
                            .stream()
                            .filter(
                                    mapping ->
                                            !mapping.getComponentRoomType().getId().equals(orphanAccomTypeId)
                                                    && isCompositionSubset(compositions.get(mapping.getComponentRoomType().getId()), orphanAccomTypeComposition))
                            .map(mapping -> mapping.getComponentRoomType().getId())
                            .collect(Collectors.toList())
            );
        }

        return parentAccomTypeIds;
    }

    private Map<Integer, Map<Integer, Long>> findOrphanRoomTypesWithQuantityForComponentRooms(List<ComponentRoomsConfiguration> configurations) {
        Map<Integer, Map<Integer, Long>> orphanMappingsWithQuantity = new HashMap<>();

        if (CollectionUtils.isEmpty(configurations)) {
            return orphanMappingsWithQuantity;
        }

        Map<Integer, List<Integer>> orphanMappings = findOrphanRoomTypesForComponentRooms(configurations);
        Map<Integer, Map<Integer, Long>> crCompositions = findCompositionsAndOccurrencesConsideringQuantity(configurations);

        orphanMappings.forEach((crAccomTypeId, orphanAccomTypeIds) -> {
            Map<Integer, Long> orphanToQuantity = new HashMap<>();
            orphanAccomTypeIds.forEach(orphanAccomTypeId -> {
                long quantity = findQuantityOfOrphanRTRequiredToFormParentCR(crCompositions.get(crAccomTypeId), crCompositions.get(orphanAccomTypeId));
                orphanToQuantity.put(orphanAccomTypeId, quantity);
            });
            orphanMappingsWithQuantity.put(crAccomTypeId, orphanToQuantity);
        });

        return orphanMappingsWithQuantity;
    }

    private long findQuantityOfOrphanRTRequiredToFormParentCR(Map<Integer, Long> parentCRComposition, Map<Integer, Long> orphanCRComposition) {
        // Orphan CR composition will always be a subset of parent CR composition
        // Orphan RT quantity will be min(For each RT -> parent CR composition divided by orphan CR composition)
        // But actual floor plan may be different, which cannot be taken under consideration. In that case, quantity computation will be incorrect and needs to be updated via REST call.

        long minQuantity = Integer.MAX_VALUE;
        for (Map.Entry<Integer, Long> orphan : orphanCRComposition.entrySet()) {
            Long parentOccurrence = parentCRComposition.get(orphan.getKey());
            long quantityRequired = parentOccurrence / orphan.getValue();
            if (minQuantity > quantityRequired) {
                minQuantity = quantityRequired;
            }
        }

        return minQuantity;
    }

    private List<CROrphanMapping> createOrphanMappings(Integer crAccomTypeId, Map<Integer, Long> orphanAndQuantityMappings) {
        List<CROrphanMapping> crOrphanMappings = new ArrayList<>();
        orphanAndQuantityMappings.forEach((orphanAccomTypeId, quantity) -> crOrphanMappings.add(createCROrphanMapping(crAccomTypeId, orphanAccomTypeId, quantity)));

        return crOrphanMappings;
    }

    public void saveTempCrAccomTypeMapping(List<TempCRAccomTypeMapping> list) {
        tenantCrudService.executeUpdateByNativeQuery("truncate table temp_CR_Accom_Type_Mapping");
        tenantCrudService.save(list);
    }

    public void truncateFcstOrgTablesAndCRAccomActivity(Integer propertyId) {
        multiPropertyCrudService.executeNamedUpdateOnSingleProperty(propertyId, OccupancyForecastOrg.TRUNCATE_OCC_FCST_ORG, Collections.emptyMap());
        multiPropertyCrudService.executeNamedUpdateOnSingleProperty(propertyId, CRAccomActivity.TRUNCATE_CR_ACCOM_ACTIVITY, Collections.emptyMap());
    }

    public boolean componentRoomsEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED) &&
                configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_CONFIGURATION_COMPLETED);
    }


    public void generateAlertForInvalidComponentRoomConfiguration() {
        List<CRAccomTypeMapping> componentRoomsMappings = getComponentRoomsMappings();
        if (componentRoomsMappings != null) {
            List<CRAccomTypeMapping> invalidConfigDueToReducedCapacityList = new ArrayList<>();
            List<CRAccomTypeMapping> roomNumbersRequiredForPartialSharesList = new ArrayList<>();
            for (CRAccomTypeMapping mapping : componentRoomsMappings) {
                int cpCapacity = mapping.getCpAccomType().getAccomTypeCapacity();
                int crCapacity = mapping.getCrAccomType().getAccomTypeCapacity();
                int minCPCapacity = crCapacity * mapping.getCpAccomTypeQuantity();
                if (cpCapacity < minCPCapacity) {
                    invalidConfigDueToReducedCapacityList.add(mapping);
                }
                int roomNumbersCount = mapping.getCrMappingRoomNumberses().size();
                if (cpCapacity > minCPCapacity && roomNumbersCount != minCPCapacity) {
                    roomNumbersRequiredForPartialSharesList.add(mapping);
                }
            }
            generateAlertForInvalidConfigDueToReducedCapacity(invalidConfigDueToReducedCapacityList);
            generateAlertForRoomNumbersRequiredForPartialSharesList(roomNumbersRequiredForPartialSharesList);
        }
    }

    private void generateAlertForInvalidConfigDueToReducedCapacity(List<CRAccomTypeMapping> invalidConfigDueToReducedCapacityList) {
        if (!invalidConfigDueToReducedCapacityList.isEmpty()) {
            WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
            InfoMgrTypeEntity alertTypeEntity = alertService.getAlertType("InvalidCRConfiguration");
            AlertType alertType = AlertType.InvalidCRConfiguration;
            StringBuilder details = new StringBuilder();
            for (CRAccomTypeMapping mapping : invalidConfigDueToReducedCapacityList) {
                details.append("<BR> CR Parent : " + mapping.getCrAccomType() + "  -->  CR Child : " + mapping.getCpAccomType());
            }
            alertService.createAlert(workContext, alertTypeEntity, INVALID_CR_CONFIGURATION_DESCRIPTION, details.toString(), alertType);
        } else {
            alertService.resolveAllAlerts(AlertType.InvalidCRConfiguration, PacmanWorkContextHelper.getPropertyId());
        }
    }

    private void generateAlertForRoomNumbersRequiredForPartialSharesList(List<CRAccomTypeMapping> roomNumbersRequiredForPartialSharesList) {
        if (!roomNumbersRequiredForPartialSharesList.isEmpty()) {
            WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
            InfoMgrTypeEntity alertTypeEntity = alertService.getAlertType("RoomNumbersRequiredForCRConfig");
            AlertType alertType = AlertType.RoomNumbersRequiredForCRConfig;
            StringBuilder details = new StringBuilder();
            for (CRAccomTypeMapping mapping : roomNumbersRequiredForPartialSharesList) {
                details.append("<BR> CR Parent : " + mapping.getCrAccomType() + "  -->  CR Child : " + mapping.getCpAccomType());
            }
            alertService.createAlert(workContext, alertTypeEntity, ROOM_NUMBERS_REQUIRED_FOR_CR_CONFIG_DESCRIPTION, details.toString(), alertType);
        } else {
            alertService.resolveAllAlerts(AlertType.RoomNumbersRequiredForCRConfig, PacmanWorkContextHelper.getPropertyId());
        }
    }

    public void updateProfitForPaceAccomActivity(Map<String, Object> params) {
        Date maxBusinessDayEndDt = tenantCrudService.findByNativeQuerySingleResult(FIND_MAX_BUSINESS_DAY_END_DT,null, objects -> (Date) objects[0]);
        params.put("businessDayEndDt",maxBusinessDayEndDt);
        tenantCrudService.executeUpdateByNativeQuery(UPDATE_PROFIT_IN_PACE_ACCOM_ACTIVITY_FOR_CR,params);
    }

    public void updateProfitForMktAccomActivity(String tableName, Map<String, Object> params) {
        tenantCrudService.executeUpdateByNativeQuery(UPDATE_PROFIT_IN_MKT_ACCOM_ACTIVITY_FOR_CR.replace("tableName",tableName),params);
    }

    public void updateProfitForAccomActivity(String tableName, Map<String, Object> params) {
        tenantCrudService.executeUpdateByNativeQuery(UPDATE_PROFIT_IN_ACCOM_ACTIVITY_FOR_CR.replace("tableName",tableName),params);
    }

    public boolean useSqlForBackfill() {
        return configParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_SQL_FOR_PACE_BACKFILL);
    }
}
