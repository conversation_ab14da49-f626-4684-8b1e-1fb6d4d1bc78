package com.ideas.tetris.pacman.services.rollback;

import com.google.common.collect.Lists;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.integration.ratchet.services.pcrs.entity.RatchetClient;
import com.ideas.tetris.pacman.integration.ratchet.services.pcrs.entity.RatchetProperty;
import com.ideas.tetris.pacman.integration.ratchet.services.pcrs.entity.RatchetSrpAttributes;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.RecordType;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.RatchetCrudServiceBean;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrLevelEntity;
import com.ideas.tetris.pacman.services.informationmanager.enums.LevelType;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.property.configuration.dto.PropertyConfigurationRecordType;
import com.ideas.tetris.pacman.services.property.configuration.entity.ConfigurationFileRecord;
import com.ideas.tetris.pacman.services.property.configuration.entity.ConfigurationFileRecordStatus;
import com.ideas.tetris.pacman.services.sas.entity.ProcessGroup;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.daoandentities.entity.Status;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.*;

import static com.ideas.tetris.pacman.services.rollback.RollbackServiceConstants.*;

/*
 * Little tricky following transaction mgmt. I think original intent was not to wrap the iteration of many tables but
 * enforce the transaction at the per table level. Should be in fine state if any particular one fails. APIs that do
 * not iterate take the default. Just be case not to miss the two (cross the streams).
 *
 * Cannot truncate a table with foreign key constraints. That avenue is closed off for construction.
 */
@Component
@Transactional
public class RollbackService {
    static final String[] RESERVATION_TABLES = new String[]{TABLE_RESERVATION_NIGHT,
            TABLE_RESERVATION_NIGHT_CHANGE, TABLE_POST_DEPARTURE_REVENUE, TABLE_RESTORED_NO_SHOW_RESERVATION};
    private static final Logger LOGGER = Logger.getLogger(RollbackService.class.getName());

    @GlobalCrudServiceBean.Qualifier
	@Qualifier("globalCrudServiceBean")
    @Autowired
	private CrudService globalCrudService;
    @Autowired
	private AbstractMultiPropertyCrudService multiPropertyCrudService;
    @RatchetCrudServiceBean.Qualifier
    @Autowired
	private CrudService ratchetCrudService;
    @Autowired
	private PropertyService propertyService;
    @Autowired
	private AgileRatesConfigurationService agileRatesConfigurationService;
    @Autowired
	private JobServiceLocal jobService;
    @Autowired
	private PacmanConfigParamsService configParamsService;

    public boolean isEligibleForRollback(Property property) {
        if (property != null && property.getStage() != null) {
            return property.getStage().getOrder() < Stage.ONE_WAY.getOrder();
        }
        return false;
    }

    public void rollback(Integer propertyId) {
        rollback(propertyId, true);
    }

    public void rollback(Integer propertyId, boolean includeRateShopping) {
        rollback(propertyId, includeRateShopping, false);
    }

    public void rollback(Integer propertyId, boolean includeRateShopping, boolean forceFullFailStep) {
        Property property = propertyService.getPropertyById(propertyId);
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("clientCode", property.getClient().getCode());
        parameters.put("propertyCode", property.getCode());
        parameters.put("forceFullFailStep", forceFullFailStep);
        parameters.put("userId", PacmanThreadLocalContextHolder.getPrincipal().getId().toString());
        parameters.put(JobParameterKey.INCLUDE_RATE_SHOPPING, String.valueOf(includeRateShopping));
        parameters.put("date", new Date()); // uniquify
        jobService.startJob(JobName.Rollback, parameters);
    }

    public void rollback(Integer propertyId, String userId, boolean includeRateShopping, boolean forceFullFailStep) {
        Property property = propertyService.getPropertyById(propertyId);
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("userId", userId);
        parameters.put("clientCode", property.getClient().getCode());
        parameters.put("propertyCode", property.getCode());
        parameters.put("forceFullFailStep", forceFullFailStep);
        parameters.put(JobParameterKey.INCLUDE_RATE_SHOPPING, String.valueOf(includeRateShopping));
        parameters.put("date", new Date()); // uniquify
        jobService.startJob(JobName.Rollback, parameters);
    }

    public void startRollback(Integer propertyId) {
        Property property = propertyService.getPropertyById(propertyId);
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("clientCode", property.getClient().getCode());
        parameters.put("propertyCode", property.getCode());
        parameters.put("userId", PacmanThreadLocalContextHolder.getPrincipal().getId().toString());
        parameters.put("date", new Date()); // uniquify
        parameters.put("forceFullFailStep", true);
        jobService.startJob(JobName.RollbackHilton, parameters);
    }

    public List<String> truncateTenantGeneralDatabaseTables(Integer propertyId) {
        List<String> results = truncateTableList(SCHEMA_DEFAULT, TABLES_TENANT_GENERAL, propertyId);

        results.addAll(truncateTableList(SCHEMA_DEFAULT, PRESERVE_TABLES_AMS_REBUILD, propertyId));
        results.addAll(truncateReservationDataTables(propertyId));
        // Only 1 result implies db lookup failed...miserably
        if (results.size() > 1) {
            String databaseName = propertyService.getDatabaseName(propertyId);
            results.add(deleteRowsFromIpConfigProcessGroup(databaseName, propertyId));
            results.add(deleteRowsFromIpConfigHorizonGroup(databaseName, propertyId));
            results.add(deleteRowsFromInfoMgrExceptionNotificationConfig(propertyId));
        }
        return results;
    }

    public List<String> truncateTenantRateShoppingTables(Integer propertyId) {
        final String[] rateShoppingTables = getShoppingTables();
        return truncateTableList(SCHEMA_DEFAULT, rateShoppingTables, propertyId);
    }

    public String[] getShoppingTables() {
        return isRDLEnabled() ? TABLES_RDL_RATE_SHOPPING : TABLES_RATE_SHOPPING;
    }

    private boolean isRDLEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED);
    }

    public boolean isCloudEnabled(Property property) {
        return configParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_S3_FOR_ARCHIVE_EXTRACTS.getParameterName(), property.getClient().getCode(), property.getCode());
    }

    public List<String> truncateTenantCoreDatabaseTables(final Integer propertyId, final boolean isHilton,
                                                         final boolean isNgi) {
        String databaseName = propertyService.getDatabaseName(propertyId);
        List<String> results = new ArrayList<>();
        deleteAllRowsFromTable(SCHEMA_DEFAULT, TABLE_PACE_GROUP_MASTER, propertyId);
        results.add(deleteRowsFromMarketSegment(databaseName, propertyId));
        results.add(deleteRowsFromFileMetaData(propertyId, isHilton, isNgi));
        return results;
    }

    public List<String> truncateRateQualifiedDatabaseTables(Integer propertyId) {
        //these tables are dependent on Rate_Qualified tables
        agileRatesConfigurationService.backupProductAssociation();
        List<String> truncateTableList = truncateAgileProductQualifiedFPLOSDatabaseTables(propertyId);
        truncateTableList.addAll(truncateTableList(SCHEMA_DEFAULT, TABLES_RATE_QUALIFIED, propertyId));
        return truncateTableList;
    }

    private List<String> truncateAgileProductQualifiedFPLOSDatabaseTables(Integer propertyId) {
        return truncateTableList(SCHEMA_DEFAULT, TABLES_AGILE_PRODUCT_QUALIFIED_FPLOS_ASSOCIATION, propertyId);
    }

    public List<String> truncateRateUnqualifiedDatabaseTables(Integer propertyId) {
        List<String> results = truncateTableList(SCHEMA_DEFAULT, TABLES_RATE_UNQUALIFIED, propertyId);
        // Need to leave entry added by 'Add Property' - maybe
        int count = deleteAllRowsFromTable(SCHEMA_DEFAULT, TABLE_RATE_UNQUALIFIED,
                "where Rate_Code_Name <> ''None''", propertyId);

        //Need to point last 'No Rate Plan'  record to the 'DUMMY_FILE_LOCATION' record in the file_metadata table
        multiPropertyCrudService.executeNamedUpdateOnSingleProperty(propertyId, RateUnqualified.UPDATE_NO_RATE_PLAN, null);

        String databaseName = propertyService.getDatabaseName(propertyId);
        // Not sure where the '3' came from - not changing though
        resetIdentityValue(databaseName, SCHEMA_DEFAULT, TABLE_RATE_UNQUALIFIED, 3, propertyId);
        results.add(TABLE_RATE_UNQUALIFIED + " : " + count);
        return results;
    }

    public List<String> truncateOperaDatabaseTables(final Integer propertyId) {
        List<String> tablesToTruncate = Lists.newArrayList(TABLES_OPERA);

        if (amsDisabled()) {
            tablesToTruncate.add(YIELD_CATEGORY_RULE);
        }

        return truncateTableList(SCHEMA_OPERA, tablesToTruncate.toArray(new String[]{}), propertyId);
    }

    public boolean rollbackYieldCategoryRule(List<Integer> propertyIds) {

        if (CollectionUtils.isEmpty(propertyIds)) {
            return false;
        }

        List<String> successfulRollbacks = new ArrayList<>();
        String[] table = {YIELD_CATEGORY_RULE};

        propertyIds.forEach(propertyId -> successfulRollbacks.addAll(truncateTableList(SCHEMA_OPERA, table, propertyId)));

        return successfulRollbacks.size() == propertyIds.size();
    }

    private boolean amsDisabled() {
        return !configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED);
    }

    public List<String> truncateReservationDataTables(Integer propertyId) {
        List<String> results = new ArrayList<>();
        String databaseName = propertyService.getDatabaseName(propertyId);
        if (databaseName == null) {
            results.add("Unable to retrieve database name for property " + propertyId);
        } else {
            truncateTables(databaseName, SCHEMA_DEFAULT, RESERVATION_TABLES, propertyId, results);
            ArrayList<Integer> recordType = new ArrayList<>();
            recordType.add(RecordType.INDIVIDUAL_TRANS_RECORD_TYPE_ID);
            results.add(deleteFromFileMetaData(propertyId, recordType));
        }
        return results;
    }

    public List<String> resetSpecificValuesDatabaseTables(Integer propertyId) {
        List<String> results = new ArrayList<>();
        String databaseName = propertyService.getDatabaseName(propertyId);
        if (databaseName != null) {
            multiPropertyCrudService.executeNativeUpdateOnSingleProperty(propertyId,
                    MessageFormat.format(SQL_TABLE_UPDATE, SCHEMA_DEFAULT + "." + TABLE_IP_CFG_PROPERTY_ATTRIBUTE), QueryParameter.with(ATTRIBUTE_NAME, PROP_BK_HISTORY_START_DT).parameters());
        } else {
            recordNoDatabaseFound(propertyId, results);
        }
        return results;
    }

    private List<String> truncateTableList(String schema, String[] tables, Integer propertyId) {
        List<String> results = new ArrayList<>();
        String databaseName = propertyService.getDatabaseName(propertyId);
        if (databaseName != null) {
            truncateTables(databaseName, schema, tables, propertyId, results);
        } else {
            recordNoDatabaseFound(propertyId, results);
        }
        return results;
    }

    private void truncateTables(String databaseName, String schema, String[] tables, Integer propertyId,
                                List<String> results) {
        List<Object[]> queryResults;
        String qualifiedTable;
        int rowCount;
        for (String table : tables) {
            qualifiedTable = schema + "." + table;
            queryResults = multiPropertyCrudService.findByNativeQueryForSingleProperty(propertyId,
                    SQL_FK_CHECK, QueryParameter.with(PARAM_TABLE_NAME, table).parameters());
            if (!queryResults.isEmpty()) {
                LOGGER.info("Table " + qualifiedTable + " has foreign key constraint on it. Deleting entries.");
                results.add(table + " : " + deleteAllRowsFromTable(schema, table, propertyId) + " : Delete");
                resetIdentityValue(databaseName, schema, table, propertyId);
            } else {
                LOGGER.info("Table " + qualifiedTable + " has no foreign key constraint on it. Truncating table.");
                rowCount = (Integer) multiPropertyCrudService.findByNativeQueryForSingleProperty(propertyId,
                        MessageFormat.format(SQL_TABLE_ROW_COUNT, qualifiedTable), null).get(0)[0];
                multiPropertyCrudService.executeNativeUpdateOnSingleProperty(propertyId,
                        MessageFormat.format(SQL_TABLE_TRUNCATE, qualifiedTable), null);
                results.add(table + " : " + rowCount + " : Truncate");
            }
        }
    }

    private void recordNoDatabaseFound(Integer propertyId, List<String> results) {
        LOGGER.warn("Could not locate db for property: " + propertyId);
        results.add("Unable to retrieve database name for property " + propertyId);
    }

    private String deleteRowsFromIpConfigProcessGroup(String databaseName, Integer propertyId) {
        int count = deleteAllRowsFromTable(SCHEMA_DEFAULT, TABLE_IP_CONFIG_PROCESS_GROUP,
                "where IP_Cfg_Process_Group_ID > 2", propertyId);
        // Not sure where the '2' came from - not changing though
        resetIdentityValue(databaseName, SCHEMA_DEFAULT, TABLE_IP_CONFIG_PROCESS_GROUP, 2, propertyId);
        // Apparently we need to set these back to active
        multiPropertyCrudService.executeNamedUpdateOnSingleProperty(propertyId,
                ProcessGroup.UPDATE_STATUS_FOR_PROPERTY_GROUP,
                QueryParameter.with(ProcessGroup.PARAM_PROCESS_GROUP_ID, -1)
                        .and(ProcessGroup.PARAM_STATUS_ID, Status.ACTIVE.getId()).parameters());
        return TABLE_IP_CONFIG_PROCESS_GROUP + " : " + count;
    }

    private String deleteRowsFromIpConfigHorizonGroup(String databaseName, Integer propertyId) {
        int count = deleteAllRowsFromTable(SCHEMA_DEFAULT, TABLE_IP_CONFIG_HORIZON_GROUP_DETAIL,
                "where IP_Cfg_Horizon_Group_ID > 1", propertyId);
        count += deleteAllRowsFromTable(SCHEMA_DEFAULT, TABLE_IP_CONFIG_HORIZON_GROUP,
                "where IP_Cfg_Horizon_Group_ID > 1", propertyId);
        // Not sure where the '1' came from - not changing though
        resetIdentityValue(databaseName, SCHEMA_DEFAULT, TABLE_IP_CONFIG_HORIZON_GROUP, 1, propertyId);
        return TABLE_IP_CONFIG_HORIZON_GROUP + " : " + count;
    }

    private String deleteRowsFromInfoMgrExceptionNotificationConfig(Integer propertyId) {
        // no reseed table
        // should lookup forecast group & market segment level ids as not defined in enum
        InformationMgrLevelEntity forecastGroupLevel = (InformationMgrLevelEntity) multiPropertyCrudService
                .findByNamedQuerySingleResultForSingleProperty(propertyId, InformationMgrLevelEntity.BY_NAME,
                        QueryParameter.with(InformationMgrLevelEntity.PARAM_NAME, LevelType.FORECAST_GROUP.getCode()).parameters());
        InformationMgrLevelEntity marketSegmentLevel = (InformationMgrLevelEntity) multiPropertyCrudService
                .findByNamedQuerySingleResultForSingleProperty(propertyId, InformationMgrLevelEntity.BY_NAME,
                        QueryParameter.with(InformationMgrLevelEntity.PARAM_NAME, LevelType.MARKET_SEGMENT.getCode()).parameters());
        InformationMgrLevelEntity competitorsLevel = (InformationMgrLevelEntity) multiPropertyCrudService
                .findByNamedQuerySingleResultForSingleProperty(propertyId, InformationMgrLevelEntity.BY_NAME,
                        QueryParameter.with(InformationMgrLevelEntity.PARAM_NAME, LevelType.COMPETITOR_SELECT.getCode()).parameters());
        return TABLE_INFO_MGR_EXCEPTION_NOTIFICATION_CONFIG + " : " + deleteAllRowsFromTable(SCHEMA_DEFAULT,
                TABLE_INFO_MGR_EXCEPTION_NOTIFICATION_CONFIG,
                MessageFormat.format("where Info_Mgr_Excep_Notif_Level_ID in ({0},{1},{2})",
                        forecastGroupLevel.getId(), marketSegmentLevel.getId(), competitorsLevel.getId()),
                propertyId);
    }

    private String deleteRowsFromMarketSegment(String databaseName, Integer propertyId) {
        MktSeg seededMarketSegment = getSeededMarketSegment(propertyId);
        int count = deleteAllRowsFromTable(SCHEMA_DEFAULT, TABLE_MARKET_SEGMENT,
                "where Mkt_Seg_ID <> " + seededMarketSegment.getId(), propertyId);
        resetIdentityValue(databaseName, SCHEMA_DEFAULT, TABLE_MARKET_SEGMENT, seededMarketSegment.getId(), propertyId);
        return TABLE_MARKET_SEGMENT + " : " + count;
    }

    private MktSeg getSeededMarketSegment(Integer propertyId) {
        return (MktSeg) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId,
                MktSeg.BY_CODE, QueryParameter.with(MktSeg.PARAM_CODE, MktSeg.VALUE_CODE_SEED).parameters());
    }

    private String deleteRowsFromFileMetaData(Integer propertyId, boolean includeRateUnqualified, boolean excludeRateQualified) {
        ArrayList<Integer> recordTypes = new ArrayList<>(Arrays.asList(RecordType.T2SNAP_RECORD_TYPE_ID,
                RecordType.WEBRATE_RECORD_TYPE_ID, RecordType.INDIVIDUAL_TRANS_RECORD_TYPE_ID,
                RecordType.GROUP_MASTER, RecordType.GROUP_BLOCK,
                RecordType.MARKET_SEGMENT_ROOM_TYPE_SUMMARY, RecordType.ROOM_TYPE_SUMMARY,
                RecordType.TOTAL_HOTEL_SUMMARY));
        if (!excludeRateQualified) {
            recordTypes.add(RecordType.QUALIFIED_RATE_RECORD_TYPE_ID);
        }
        if (includeRateUnqualified) {
            recordTypes.add(RecordType.UNQUALIFIED_RATE_RECORD_TYPE_ID);
        }
        return deleteFromFileMetaData(propertyId, recordTypes);
    }

    private String deleteFromFileMetaData(Integer propertyId, List<Integer> recordTypes) {
        String recordTypesInClause = recordTypes.toString().replace('[', '(').replace(']', ')');
        int count = deleteAllRowsFromTable(SCHEMA_DEFAULT, TABLE_FILE_METADATA,
                "where Record_Type_ID in " + recordTypesInClause, propertyId);
        return TABLE_FILE_METADATA + " : " + count;
    }

    private int deleteAllRowsFromTable(String schema, String table, Integer propertyId) {
        return deleteAllRowsFromTable(schema, table, null, propertyId);
    }

    private int deleteAllRowsFromTable(String schema, String table, String optionalClause, Integer propertyId) {
        String sql = SQL_TABLE_DELETE_ALL;
        if (null != optionalClause) {
            sql += " " + optionalClause;
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("RollBackService, deleteAllRowsFromTable method, for propertyId: " + propertyId + " about to run sql: " + MessageFormat.format(sql, schema + "." + table));
        }
        return multiPropertyCrudService.executeNativeUpdateOnSingleProperty(propertyId,
                MessageFormat.format(sql, schema + "." + table), null);
    }

    private void resetIdentityValue(String database, String schema, String table, Integer propertyId) {
        resetIdentityValue(database, schema, table, SEED_START_DEFAULT, propertyId);
    }

    private void resetIdentityValue(String database, String schema, String table, Integer seedStart, Integer propertyId) {
        multiPropertyCrudService.executeNativeUpdateOnSingleProperty(propertyId, SQL_TABLE_RESEED,
                QueryParameter.with(PARAM_TABLE_NAME, database + "." + schema + "." + table)
                        .and(PARAM_SEED_START, seedStart).parameters());
    }

    public int setOutOfOrderFileConfigRecordsToPending(Integer propertyId) {
        return setFileConfigRecordsToPending(propertyId, PropertyConfigurationRecordType.OO);
    }

    public int setSpecialRatePlanFileConfigRecordsToPending(Integer propertyId) {
        return setFileConfigRecordsToPending(propertyId, PropertyConfigurationRecordType.SRP);
    }

    public int setDefaultRateChannelFileConfigRecordsToPending(Integer propertyId) {
        return setFileConfigRecordsToPending(propertyId, PropertyConfigurationRecordType.DRC);
    }

    private int setFileConfigRecordsToPending(Integer propertyId, PropertyConfigurationRecordType recordType) {
        Property property = propertyService.getPropertyById(propertyId);
        return globalCrudService.executeUpdateByNamedQuery(ConfigurationFileRecord.UPDATE_STATUS_FOR_PROPERTY_AND_RECORD_TYPE,
                QueryParameter.with(ConfigurationFileRecord.PARAM_CLIENT_ID, property.getClient().getId())
                        .and(ConfigurationFileRecord.PARAM_PROPERTY_CODE, property.getCode())
                        .and(ConfigurationFileRecord.PARAM_STATUS, ConfigurationFileRecordStatus.PENDING)
                        .and(ConfigurationFileRecord.PARAM_RECORD_TYPE, recordType.toString()).parameters());
    }

    public int deleteRatchetSpecialRatePlanRecords(Integer propertyId) {
        Property property = propertyService.getPropertyById(propertyId);
        // It appears ratchet client code = g3 client code but ratchet client id != g3 client id
        // It appears ratchet property code = g3 client code but ratchet property id != g3 property id
        RatchetClient ratchetClient = ratchetCrudService.findByNamedQuerySingleResult(
                RatchetClient.BY_CLIENT_CODE,
                QueryParameter.with(RatchetClient.PARAM_CLIENT_CODE, property.getClient().getCode()).parameters());
        RatchetProperty ratchetProperty = ratchetCrudService.findByNamedQuerySingleResult(
                RatchetProperty.BY_RATCHET_PROPERTY_CODE_AND_CLIENT_ID,
                QueryParameter.with(RatchetProperty.PARAM_PROP_CODE, property.getCode())
                        .and(RatchetProperty.PARAM_CLIENT_ID, ratchetClient.getId()).parameters());
        return ratchetCrudService.executeUpdateByNamedQuery(RatchetSrpAttributes.DELETE_BY_RATCHET_PROPERTY,
                QueryParameter.with(RatchetSrpAttributes.PARAM_PROPERTY, ratchetProperty).parameters());
    }

    public List<String> clearFunctionSpaceTablesAssociations(Integer propertyId, Map<String, String> tableQueryMapping) {
        List<String> result = new ArrayList<>();
        if (MapUtils.isEmpty(tableQueryMapping)) {
            return result;
        }
        if (null != propertyService.getDatabaseName(propertyId)) {
            tableQueryMapping.forEach((tableName, query) -> {
                int rowCount = multiPropertyCrudService.executeNativeUpdateOnSingleProperty(propertyId, query, null);
                result.add(String.format("association cleared for table: %s and rowCount: %d", tableName, rowCount));
            });
        } else {
            recordNoDatabaseFound(propertyId, result);
        }
        return result;
    }
}
