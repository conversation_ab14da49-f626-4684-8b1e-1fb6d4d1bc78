package com.ideas.tetris.pacman.services.webrate.service;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.decisionsupport.DecisionSupportService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.rateshopping.entity.CompetitorRates;
import com.ideas.tetris.pacman.services.webrate.dto.CompetitorRateInfo;
import com.ideas.tetris.pacman.services.webrate.dto.DCMPCGenericValuesDTO;
import com.ideas.tetris.pacman.services.webrate.dto.WebRateCompetitorMappingsDto;
import com.ideas.tetris.pacman.services.webrate.entity.*;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.ENABLE_IGNORE_WEBRATE_CHANNEL;
import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.IS_MAP_ACCOM_TYPE_TO_MULTIPLE_ACCOM_CLASSES_ENABLED;
import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED;
import static com.ideas.tetris.pacman.common.constants.Constants.*;
import static com.ideas.tetris.pacman.services.rateshopping.entity.CompetitorRates.GET_COMPETITOR_RATES_NON_RDL;
import static com.ideas.tetris.pacman.services.rateshopping.entity.CompetitorRates.GET_COMPETITOR_RATES_RDL;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.convertJavaUtilDateToLocalDate;
import static com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig.useOptimizedStoredProcedureInvestigator;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Component
@Transactional
public class CompetitorRateInfoService {

    private static final String ACCOM_ID = "accomId";
    static final Integer DISABLE_WEBRATE_COMPETITOR_CLASS_CODE = 0;
    static final Integer ENABLE_WEBRATE_COMPETITOR_CLASS_CODE = 1;
    public static final String ignoreCompetitorIds = "ignoreCompetitorIds";
    public static final String systemDate = "systemDate";
    public static final String START_DATE = "startDate";
    public static final String END_DATE = "endDate";
    public static final String CHANNEL_IDS = "channelIds";
    public static final String COMPETITOR_IDS = "competitorIds";
    public static final String ACCOM_TYPE_IDS = "accomTypeIds";
    public static final String PRODUCT_IDS = "productIds";
    public static final String KEY_SEPARATOR = "::";

    @TenantCrudServiceBean.Qualifier
    @Autowired
    @Qualifier("tenantCrudServiceBean")
    private CrudService crudService;
    @Autowired
    private PacmanConfigParamsService configService;
    @Autowired
    private WebrateShoppingDataService webrateShoppingDataService;

    @Autowired
    PacmanConfigParamsService configParamsService;

    @Autowired
    DecisionSupportService decisionSupportService;
    @Autowired
    private DateService dateService;

    @Autowired
    private DynamicCMPCService dynamicCMPCService;

    @Autowired
    private AccommodationService accommodationService;

    @Autowired
    WebrateChannelIgnoreService webrateChannelIgnoreService;

    private static final String EXEC_FIND_COMPETITOR_RATES_FOR_DATE_AND_CHANNEL = "EXEC dbo.usp_find_competitor_rates_for_date_and_channel :date, :accomClassId, :channelId, :selfCompetitorId, :ignoreCompetitorIds, :systemDate, :los";
    private static final String EXEC_FIND_COMPETITOR_RATES_FOR_DATE_AND_CHANNEL_RANKING_ENABLED = "EXEC dbo.usp_find_competitor_rates_for_date_and_channel_ranking_enabled :date, :accomClassId, :channelId, :selfCompetitorId, :ignoreCompetitorIds, :systemDate, :los";

    private static final String EXEC_FIND_COMPETITOR_RATES_FOR_DATE_AND_ALL_CHANNEL_FOR_COMPETITOR_CHART = "EXEC dbo.usp_find_competitor_rates_for_date_and_all_channel_competior_chart :date, :accomClassId, :selfCompetitorId,:ignoreCompetitorIds, :systemDate, :los";
    private static final String EXEC_FIND_COMPETITOR_RATES_FOR_DATE_AND_ALL_CHANNEL_RANKING_ENABLED_FOR_COMPETITOR_CHART = "EXEC usp_find_competitor_rates_for_date_and_all_channel_ranking_enabled_competitor_chart :date, :accomClassId, :selfCompetitorId,:ignoreCompetitorIds, :systemDate, :los";
    private static final String EXEC_FIND_COMPETITOR_RATES_FOR_DATE_AND_ALL_CHANNEL = "EXEC dbo.usp_find_competitor_rates_for_date_and_all_channel :date, :accomClassId, :selfCompetitorId,:ignoreCompetitorIds, :systemDate, :los";
    private static final String EXEC_FIND_COMPETITOR_RATES_FOR_DATE_AND_ALL_CHANNEL_RANKING_ENABLED = "EXEC usp_find_competitor_rates_for_date_and_all_channel_ranking_enabled :date, :accomClassId, :selfCompetitorId,:ignoreCompetitorIds, :systemDate, :los";
    private static final String query = "select Webrate_ID,Webrate_Source_Property_ID,Webrate_Competitors_ID,Webrate_Channel_ID,Webrate_Accom_Type_ID,\n" +
            "                    LOS,webRate_remark,Webrate_Status,Webrate_Currency,Webrate_RateValue , webrate_generationdate, Webrate_Occupancy_Date,Product_ID from( \n" +
            "                     SELECT Isnull(wb.webrate_id, 0)                 AS Webrate_ID,  \n" +
            "                       Isnull(wb.webrate_source_property_id, 0) AS Webrate_Source_Property_ID,  \n" +
            "                       x.webrate_competitors_id,  \n" +
            "                       Isnull(wb.webrate_channel_id, 0)         AS Webrate_Channel_ID,  \n" +
            "                       Isnull(wb.webrate_accom_type_id, 0)      AS Webrate_Accom_Type_ID,  \n" +
            "                       Isnull(wb.los, 1)                        AS LOS,  \n" +
            "                       Isnull(wb.webrate_remark, 'NA')          AS Webrate_Remark,  \n" +
            "                       Isnull(wb.webrate_status, 'NA')          AS Webrate_Status,  \n" +
            "                       Isnull(wb.webrate_currency, 'NA')        AS Webrate_Currency,  \n" +
            "                       Isnull(wb.Original_Webrate_RateValue, 0) AS Webrate_RateValue,  \n" +
            "                       wb.webrate_generationdate , ROW_NUMBER() over (Partition by Product_Id, x.webrate_competitors_id,webrate_channel_id, Webrate_Accom_Type_ID, LOS order by Product_Id, x.webrate_competitors_id,webrate_channel_id, Webrate_Accom_Type_ID, LOS desc,webRate_generationDate desc) as rowNUm, \n" +
            "                       wb.occupancy_dt                          AS Webrate_Occupancy_Date  ,\n" +
            "                       wb.Product_ID                            AS Product_ID  \n" +
            "                    FROM   Vw_Webrate_full AS wb  \n" +
            "                       RIGHT JOIN (SELECT *  \n" +
            "                       FROM   webrate_competitors wc  \n" +
            "                       WHERE  webrate_competitors_id IN (:competitorIds)) AS x  \n" +
            "                       ON x.webrate_competitors_id = wb.webrate_competitors_id  \n" +
            "                      AND wb.occupancy_dt >=:startDate  " +
            "                      AND wb.occupancy_dt <= :endDate  " +
            "                      AND wb.webrate_channel_id IN(:channelIds)  \n" +
            "                      AND wb.webrate_accom_type_id IN(:accomTypeIds)\n" +
            "\t\t\t\t\t  and wb.Product_ID in (:productIds)  \n" +
            "                    ) AS finalData \n" +
            "                    WHERE rowNUm = 1 and Webrate_ID > 0";

    private static final String QUERY_FOR_RDL_PROPERTIES = "select Webrate_ID, Webrate_Source_Property_ID, Webrate_Competitors_ID, Webrate_Channel_ID, Webrate_Accom_Type_ID, " +
            "LOS, webRate_remark, Webrate_Status, Webrate_Currency, Webrate_RateValue, " +
            "webrate_generationdate, Webrate_Occupancy_Date,Product_ID, rate_type " +
            "from( " +
            "SELECT Isnull(wb.webrate_id, 0)                 AS Webrate_ID, " +
            "Isnull(wb.webrate_source_property_id, 0) AS Webrate_Source_Property_ID, " +
            "x.webrate_competitors_id, " +
            "Isnull(wb.webrate_channel_id, 0)         AS Webrate_Channel_ID, " +
            "Isnull(wb.webrate_accom_type_id, 0)      AS Webrate_Accom_Type_ID, " +
            "Isnull(wb.los, 1)                        AS LOS, " +
            "Isnull(wb.webrate_remark, 'NA')          AS Webrate_Remark, " +
            "Isnull(wb.webrate_status, 'NA')          AS Webrate_Status, " +
            "Isnull(wb.webrate_currency, 'NA')        AS Webrate_Currency, " +
            "Isnull(wb.Original_Webrate_RateValue, 0) AS Webrate_RateValue, " +
            "wb.webrate_generationdate , ROW_NUMBER() over (Partition by wb.Product_Id, x.webrate_competitors_id,webrate_channel_id, Webrate_Accom_Type_ID, wb.LOS order by wb.Product_Id, x.webrate_competitors_id,webrate_channel_id, Webrate_Accom_Type_ID, wb.LOS desc,webRate_generationDate desc) as rowNUm, " +
            "wb.occupancy_dt                          AS Webrate_Occupancy_Date  ," +
            "wb.Product_ID                            AS Product_ID , webrate_rating as rate_type " +
            "FROM   Vw_Webrate_v2_full AS wb " +
            "RIGHT JOIN (SELECT * " +
            "FROM   webrate_competitors wc " +
            "                       WHERE  webrate_competitors_id IN (:competitorIds)) AS x  \n" +
            "                       ON x.webrate_competitors_id = wb.webrate_competitors_id  \n" +
            "                      AND wb.occupancy_dt >=:startDate  " +
            "                      AND wb.occupancy_dt <= :endDate  " +
            "                      AND wb.webrate_channel_id IN(:channelIds)  \n" +
            "                      AND wb.webrate_accom_type_id IN(:accomTypeIds)\n" +
            "\t\t\t\t\t  and wb.Product_ID in (:productIds)  \n" +
            "                    ) AS finalData \n" +
            "                    WHERE rowNUm = 1 and Webrate_ID > 0";

    public void setWebrateShoppingDataService(WebrateShoppingDataService webrateShoppingDataService) {
        this.webrateShoppingDataService = webrateShoppingDataService;
    }

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    public void setConfigService(PacmanConfigParamsService configService) {
        this.configService = configService;
    }

    @VisibleForTesting
    protected PacmanConfigParamsService getConfigService() {
        return configService;
    }

    public void setDecisionSupportService(DecisionSupportService decisionSupportService) {
        this.decisionSupportService = decisionSupportService;
    }

    @VisibleForTesting
    protected void setAccommodationService(AccommodationService accommodationService) {
        this.accommodationService = accommodationService;
    }

    public void setDateService(DateService dateService) {
        this.dateService = dateService;
    }

    public List<CompetitorRateInfo> getCompetitorRates(Date occupancyDate, List<Integer> channelIds, List<Integer> competitorIds, List<Integer> accomTypeIds) {
        return getCompetitorRates(occupancyDate, occupancyDate, channelIds, competitorIds, accomTypeIds, false);
    }

    /**
     * getCompetitorRates from CompetitorRateInfoService
     * <p>
     * ---Sonar ignores---
     * <p>
     * S3776: Cognitive Complexity of methods should not be too high
     * We're not going to spend the time to refactor this now.
     */
    @SuppressWarnings({"squid:S3776"})
    public List<CompetitorRateInfo> getCompetitorRates(Date fromDate, Date toDate, List<Integer> channelIds, List<Integer> competitorIds, List<Integer> accomTypeIds, boolean isCompetitiveMarketPositioningEnabled) {
        String displayNameEnabled = configService.getParameterValue(GUIConfigParamName.DISPLAY_NAME_DISPLAY_NAME_ENABLED.value());
        List<CompetitorRateInfo> finalResult = new ArrayList<>();
        WebrateRanking defaultWebrateRanking = crudService.findByNamedQuerySingleResult(WebrateRanking.FIND_BY_NAME, QueryParameter.with("webrateRankingName", "None").parameters());
        List<WebrateRankingAccomClass> webrateRankingAccomClasses = crudService.findAll(WebrateRankingAccomClass.class);
        List<WebrateRankingAccomClassOverride> webrateRankingAccomClassOverrides = getWebrateRankingAccomClassOverrides(fromDate, toDate);
        Map<DayOfWeek, List<String>> dayOfWeekWiseIgnoredChannels = webrateChannelIgnoreService.getDayOfWeekWiseIgnoredChannels();
        List<WebrateCompChannelMapping> webrateCompChannelMappings = webrateShoppingDataService.getWebrateCompChannelMappings();

        for (Integer channelIdReq : channelIds) {

            String query = "select Webrate_ID,Webrate_Source_Property_ID,Webrate_Competitors_ID,Webrate_Channel_ID,Webrate_Accom_Type_ID," +
                    "Webrate_Type_ID,LOS,webRate_remark,Webrate_Status,Webrate_Currency,Webrate_RateValue , webrate_generationdate, Webrate_Occupancy_Date from(\n" +
                    " SELECT Isnull(wb.webrate_id, 0)                 AS Webrate_ID, \n" +
                    "   Isnull(wb.webrate_source_property_id, 0) AS Webrate_Source_Property_ID, \n" +
                    "   x.webrate_competitors_id, \n" +
                    "   Isnull(wb.webrate_channel_id, 0)         AS Webrate_Channel_ID, \n" +
                    "   Isnull(wb.webrate_accom_type_id, 0)      AS Webrate_Accom_Type_ID, \n" +
                    "   Isnull(wb.webrate_type_id, 0)            AS Webrate_Type_ID, \n" +
                    "   Isnull(wb.los, 1)                        AS LOS, \n" +
                    "   Isnull(wb.webrate_remark, 'NA')          AS Webrate_Remark, \n" +
                    "   Isnull(wb.webrate_status, 'NA')          AS Webrate_Status, \n" +
                    "   Isnull(wb.webrate_currency, 'NA')        AS Webrate_Currency, \n" +
                    "   Isnull(wb.webrate_ratevalue_display, 0)  AS Webrate_RateValue, \n" +
                    "   wb.webrate_generationdate , ROW_NUMBER() over (Partition by x.webrate_competitors_id, Webrate_Accom_Type_ID, LOS order by x.webrate_competitors_id, Webrate_Accom_Type_ID, LOS desc,webRate_generationDate desc) as rowNUm,\n" +
                    "   wb.occupancy_dt                          AS Webrate_Occupancy_Date \n" +
                    "FROM   webrate AS wb \n" +
                    "   RIGHT JOIN (SELECT * \n" +
                    "   FROM   webrate_competitors wc \n" +
                    "   WHERE  webrate_competitors_id IN ( :competitorIds )) AS x \n" +
                    "   ON x.webrate_competitors_id = wb.webrate_competitors_id \n" +
                    "  AND wb.occupancy_dt >= :startDate \n" +
                    "  AND wb.occupancy_dt <= :endDate \n" +
                    "  AND wb.webrate_channel_id IN( :channelIds ) \n" +
                    "  AND wb.webrate_accom_type_id IN( :accomTypeIds ) \n" +
                    ") AS finalData\n" +
                    "WHERE rowNUm = 1";

            Query q = crudService.getEntityManager().createNativeQuery(query);
            q.setParameter("startDate", new SimpleDateFormat("yyyy-MM-dd").format(fromDate));
            q.setParameter("endDate", new SimpleDateFormat("yyyy-MM-dd").format(toDate));
            q.setParameter("channelIds", channelIdReq);
            q.setParameter("competitorIds", competitorIds);
            q.setParameter("accomTypeIds", accomTypeIds);
            List<Object[]> resultList = q.getResultList();

            WebrateChannel objWebrateChannel = crudService.find(WebrateChannel.class, channelIdReq);

            Object[] currentRow;
            List<CompetitorRateInfo> result = new ArrayList<>();
            for (int i = 0; i < resultList.size(); i++) {

                currentRow = resultList.get(i);

                Integer webRateId = (Integer) currentRow[0];
                Integer competitorId = (Integer) currentRow[2];
                Integer channelId = (Integer) currentRow[3];
                Integer accomTypeId = (Integer) currentRow[4];
                Integer los = ((BigDecimal) currentRow[6]).toBigInteger().intValue();
                String webrateRemark = (String) currentRow[7];
                String webrateStatus = (String) currentRow[8];
                BigDecimal webrateRatevalue = (BigDecimal) currentRow[10];
                Date webrate_occupancyDate = (Date) currentRow[12];

                Date webrateGenerationDate = (null != currentRow[10]) ? ((Date) currentRow[11]) : null;

                if (i == 0 || checkIfCreateNewCompetitorRateInfo(currentRow, resultList, i)) {

                    List<WebrateAccomClassMapping> objMapping = crudService.findByNamedQuery(WebrateAccomClassMapping.BY_ACCOMTYPE_ID, QueryParameter.with("accomTypeId", accomTypeId).parameters());
                    AccomClass accomClass = null;

                    if (isNotEmpty(objMapping)) {
                        accomClass = objMapping.get(0).getAccomClass();
                    }

                    CompetitorRateInfo dto = new CompetitorRateInfo();
                    dto.setWebRateId(webRateId);
                    dto.setOccupancyDate(webrate_occupancyDate);

                    if (null != objWebrateChannel) {
                        if ("true".equalsIgnoreCase(displayNameEnabled)) {
                            dto.setChannelName(objWebrateChannel.getWebrateChannelAlias());
                        } else {
                            dto.setChannelName(objWebrateChannel.getWebrateChannelName());
                        }
                    }
                    WebrateCompetitors objWebrateComp = crudService.find(WebrateCompetitors.class, competitorId);
                    if (null != objWebrateComp) {
                        dto.setCompetitorName(objWebrateComp.getWebrateCompetitorsAlias());
                    }
                    setAccomClassName(accomClass, dto);

                    WebrateAccomType objWebrateAccomType = crudService.find(WebrateAccomType.class, accomTypeId);
                    if (null != objWebrateAccomType) {
                        if ("true".equalsIgnoreCase(displayNameEnabled)) {
                            dto.setRoomTypeName(objWebrateAccomType.getWebrateAccomAlias());
                        } else {
                            dto.setRoomTypeName(objWebrateAccomType.getWebrateAccomName());
                        }
                    }
                    setWebRateValueOrRemarkInLOS(los, webrateRemark, webrateStatus, webrateRatevalue, dto);

                    dto.setRate(webrateRatevalue);

                    if (accomClass != null) {
                        WebrateCompetitorsAccomClass mapping = crudService.findByNamedQuerySingleResult(WebrateCompetitorsAccomClass.BY_ACCOM_ID_AND_COMP_ID,
                                QueryParameter.with(ACCOM_ID, accomClass.getId()).and("compId", competitorId).parameters());

                        if (mapping != null) {
                            if (isCompetitiveMarketPositioningEnabled) {
                                if (isRankDisabledFor(mapping)) {
                                    dto.setIncludedInMarketConstraints(defaultWebrateRanking);
                                    setRankingEnabledForDCMPC(dto, false);
                                } else {
                                    WebrateRanking webrateRanking = getWebrateRanking(webrateRankingAccomClasses, webrateRankingAccomClassOverrides, webrate_occupancyDate, mapping.getProductID(), accomClass);
                                    dto.setIncludedInMarketConstraints(webrateRanking != null ? webrateRanking : defaultWebrateRanking);
                                    setRankingEnabledForDCMPC(dto, true);
                                }
                                if (isDemandDisabledFor(mapping)) {
                                    dto.setIncludedInDemand(false);
                                } else {
                                    WebrateOverrideCompetitor webrateOverrideCompetitor = getWebrateOverrideCompetitor(webrate_occupancyDate, mapping);
                                    boolean includedInDemand = checkIfSelectedDateWithinDTARange(mapping.getDaysToArrival(), webrate_occupancyDate) &&
                                            shouldIncludeInDemand(dto, webrate_occupancyDate, webrateOverrideCompetitor, dayOfWeekWiseIgnoredChannels, webrateCompChannelMappings);
                                    dto.setIncludedInDemand(includedInDemand);
                                }
                            } else {
                                if (isDemandDisabledFor(mapping)) {
                                    dto.setIncludedInDemand(false);
                                    dto.setIncludedInMarketConstraints(defaultWebrateRanking);
                                } else {
                                    WebrateOverrideCompetitor webrateOverrideCompetitor = getWebrateOverrideCompetitor(webrate_occupancyDate, mapping);
                                    boolean includedInDemand = checkIfSelectedDateWithinDTARange(mapping.getDaysToArrival(), webrate_occupancyDate) &&
                                            shouldIncludeInDemand(dto, webrate_occupancyDate, webrateOverrideCompetitor, dayOfWeekWiseIgnoredChannels, webrateCompChannelMappings);
                                    dto.setIncludedInDemand(includedInDemand);
                                    WebrateRanking webrateRanking = getWebrateRanking(webrateRankingAccomClasses, webrateRankingAccomClassOverrides, webrate_occupancyDate, mapping.getProductID(), accomClass);
                                    dto.setIncludedInMarketConstraints(webrateRanking != null ? webrateRanking : defaultWebrateRanking);
                                }
                            }
                        }
                    }
                    setWebRateGenerationDate(webrateGenerationDate, dto);

                    result.add(dto);

                } else {
                    // Add info to previous DTO
                    CompetitorRateInfo dto = result.get(result.size() - 1);
                    if (webrateStatus.equalsIgnoreCase(WEBRATE_ACTIVE_STATUS_CODE)) {
                        dto.getRateByLengthOfStay().put(los, webrateRatevalue.setScale(2, RoundingMode.HALF_UP).toPlainString());
                    } else if (webrateStatus.equalsIgnoreCase(WEBRATE_CLOSED_STATUS_CODE)) {
                        dto.getRateByLengthOfStay().put(los, WEBRATE_CLOSED_STATUS);
                    } else if (webrateStatus.equalsIgnoreCase(WEBRATE_FENCED_STATUS_CODE)) {
                        dto.getRateByLengthOfStay().put(los, WEBRATE_RATE_RESTRICTED_STATUS);
                    } else {
                        dto.getRateByLengthOfStay().put(los, webrateStatus.toUpperCase());
                    }

                    setMaxWebrateGenerationDate(dto, webrateGenerationDate);
                }
            }
            for (CompetitorRateInfo obj : result) {
                finalResult.add(obj);
            }
        }
        return finalResult;
    }

    private boolean shouldIncludeInDemand(CompetitorRateInfo dto, Date webrate_occupancyDate, WebrateOverrideCompetitor webrateOverrideCompetitor, Map<DayOfWeek, List<String>> dayOfWeekWiseIgnoredChannels, List<WebrateCompChannelMapping> webrateCompChannelMappings) {
        return !(isChannelIgnored(dto, dayOfWeekWiseIgnoredChannels) || isCompetitorAndChannelIgnored(dto, webrate_occupancyDate, webrateOverrideCompetitor, webrateCompChannelMappings));
    }

    private boolean isCompetitorAndChannelIgnored(CompetitorRateInfo dto, Date webrate_occupancyDate, WebrateOverrideCompetitor webrateOverrideCompetitor, List<WebrateCompChannelMapping> webrateCompChannelMappings) {
        if (isChannelWiseIgnoreCompetitorEnabled()) {
            return webrateCompChannelMappings.stream()
                    .anyMatch(getWebrateCompChannelMappingPredicate(dto, dto.getOccupancyDate(), getOfWeekFrom(dto.getOccupancyDate())));
        }
        return !(isNull(webrateOverrideCompetitor) || webrateOverrideCompetitor.shouldIncludeInDemandForDate(webrate_occupancyDate));
    }

    private DayOfWeek getOfWeekFrom(Date occupancyDate) {
        int dow = getDayOfWeek(occupancyDate);
        return DayOfWeek.valueOf(dow == 1 ? 7 : dow - 1);
    }

    private boolean isIgnoreWebrateChannelEnabled() {
        return configService.getBooleanParameterValue(ENABLE_IGNORE_WEBRATE_CHANNEL);
    }

    private boolean isChannelWiseIgnoreCompetitorEnabled() {
        return configService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_NEW_UI_IGNORE_COMPETITOR);
    }


    private Predicate<WebrateCompChannelMapping> getWebrateCompChannelMappingPredicate(CompetitorRateInfo dto, Date occupancyDate, DayOfWeek dayOfWeek) {
        return isOccupancyDateBetweenSeason(occupancyDate)
                .and(isCompetitprNameSame(dto.getCompetitorName()))
                .and(isAccomClassNameSame(dto.getAccomClassName()))
                .and(isWebrateChannelNameSame(dto.getChannelName()))
                .and(isDowMatched(dayOfWeek));
    }

    private Predicate<WebrateCompChannelMapping> isDowMatched(DayOfWeek dayOfWeek) {
        return webrateCompChannelMapping -> {
            switch (Objects.requireNonNull(dayOfWeek)) {
                case MONDAY:
                    return webrateCompChannelMapping.getWebrateOverrideCompetitorDetails().getWebrateOverrideCompetitor().getIgnoreCompetitorDataOnMonday() == 1;
                case TUESDAY:
                    return webrateCompChannelMapping.getWebrateOverrideCompetitorDetails().getWebrateOverrideCompetitor().getIgnoreCompetitorDataOnTuesday() == 1;
                case WEDNESDAY:
                    return webrateCompChannelMapping.getWebrateOverrideCompetitorDetails().getWebrateOverrideCompetitor().getIgnoreCompetitorDataOnWednesday() == 1;
                case THURSDAY:
                    return webrateCompChannelMapping.getWebrateOverrideCompetitorDetails().getWebrateOverrideCompetitor().getIgnoreCompetitorDataOnThursday() == 1;
                case FRIDAY:
                    return webrateCompChannelMapping.getWebrateOverrideCompetitorDetails().getWebrateOverrideCompetitor().getIgnoreCompetitorDataOnFriday() == 1;
                case SATURDAY:
                    return webrateCompChannelMapping.getWebrateOverrideCompetitorDetails().getWebrateOverrideCompetitor().getIgnoreCompetitorDataOnSaturday() == 1;
                case SUNDAY:
                    return webrateCompChannelMapping.getWebrateOverrideCompetitorDetails().getWebrateOverrideCompetitor().getIgnoreCompetitorDataOnSunday() == 1;
                default:
                    return false;
            }
        };
    }

    private static Predicate<WebrateCompChannelMapping> isWebrateChannelNameSame(String channelName) {
        return webrateCompChannelMapping -> channelName.equalsIgnoreCase(webrateCompChannelMapping.getWebrateChannel().getWebrateChannelName());
    }

    private static Predicate<WebrateCompChannelMapping> isAccomClassNameSame(String accomClassName) {
        return webrateCompChannelMapping -> accomClassName.equalsIgnoreCase(webrateCompChannelMapping.getWebrateOverrideCompetitorDetails().getWebrateCompetitorsAccomClass().getAccomClass().getName());
    }

    private static Predicate<WebrateCompChannelMapping> isCompetitprNameSame(String competitorName) {
        return webrateCompChannelMapping -> competitorName.equalsIgnoreCase(webrateCompChannelMapping.getWebrateOverrideCompetitorDetails().getWebrateCompetitorsAccomClass().getWebrateCompetitor().getWebrateCompetitorsName());
    }

    private Predicate<WebrateCompChannelMapping> isOccupancyDateBetweenSeason(Date occupancyDate) {
        return webrateCompChannelMapping -> {
            WebrateOverrideCompetitor webrateOverrideCompetitor = webrateCompChannelMapping.getWebrateOverrideCompetitorDetails().getWebrateOverrideCompetitor();
            Date startDate = webrateOverrideCompetitor.getStartDate().getTime();
            Date endDate = webrateOverrideCompetitor.getEndDate().getTime();
            return occupancyDate.equals(startDate) || occupancyDate.equals(endDate) || (occupancyDate.before(endDate) && occupancyDate.after(startDate));
        };
    }

    private boolean isChannelIgnored(CompetitorRateInfo dto, Map<DayOfWeek, List<String>> dayOfWeekWiseIgnoredChannels) {
        if(!isIgnoreWebrateChannelEnabled()){
            return false;
        }
        DayOfWeek dayOfWeek = getOfWeekFrom(dto.getOccupancyDate());
        return nonNull(dayOfWeekWiseIgnoredChannels.get(dayOfWeek)) && dayOfWeekWiseIgnoredChannels.get(dayOfWeek).contains(dto.getChannelName());
    }


    public List<CompetitorRateInfo> getCompetitorRatesIndProductToggleoff(Date fromDate, Date toDate, List<Integer> channelIds, List<Integer> competitorIds, List<Integer> accomTypeIds, boolean isCompetitiveMarketPositioningEnabled) {
        String displayNameEnabled = configService.getParameterValue(GUIConfigParamName.DISPLAY_NAME_DISPLAY_NAME_ENABLED.value());
        List<CompetitorRateInfo> finalResult = new ArrayList<>();
        WebrateRanking defaultWebrateRanking = crudService.findByNamedQuerySingleResult(WebrateRanking.FIND_BY_NAME, QueryParameter.with("webrateRankingName", "None").parameters());
        List<WebrateRankingAccomClass> webrateRankingAccomClasses = crudService.findAll(WebrateRankingAccomClass.class);
        List<WebrateRankingAccomClassOverride> webrateRankingAccomClassOverrides = getWebrateRankingAccomClassOverrides(fromDate, toDate);
        Map<DayOfWeek, List<String>> dayOfWeekWiseIgnoredChannels = webrateChannelIgnoreService.getDayOfWeekWiseIgnoredChannels();
        List<WebrateCompChannelMapping> webrateCompChannelMappings = webrateShoppingDataService.getWebrateCompChannelMappings();

        for (Integer channelIdReq : channelIds) {

            String query = "select Webrate_ID,Webrate_Source_Property_ID,Webrate_Competitors_ID,Webrate_Channel_ID,Webrate_Accom_Type_ID," +
                    "LOS,webRate_remark,Webrate_Status,Webrate_Currency,Webrate_RateValue , webrate_generationdate, Webrate_Occupancy_Date from(\n" +
                    " SELECT Isnull(wb.webrate_id, 0)                 AS Webrate_ID, \n" +
                    "   Isnull(wb.webrate_source_property_id, 0) AS Webrate_Source_Property_ID, \n" +
                    "   x.webrate_competitors_id, \n" +
                    "   Isnull(wb.webrate_channel_id, 0)         AS Webrate_Channel_ID, \n" +
                    "   Isnull(wb.webrate_accom_type_id, 0)      AS Webrate_Accom_Type_ID, \n" +
                    "   Isnull(wb.los, 1)                        AS LOS, \n" +
                    "   Isnull(wb.webrate_remark, 'NA')          AS Webrate_Remark, \n" +
                    "   Isnull(wb.webrate_status, 'NA')          AS Webrate_Status, \n" +
                    "   Isnull(wb.webrate_currency, 'NA')        AS Webrate_Currency, \n" +
                    "   Isnull(wb.Original_Webrate_RateValue, 0)  AS Webrate_RateValue, \n" +
                    "   wb.webrate_generationdate , ROW_NUMBER() over (Partition by x.webrate_competitors_id, Webrate_Accom_Type_ID, LOS order by x.webrate_competitors_id, Webrate_Accom_Type_ID, LOS desc,webRate_generationDate desc) as rowNUm,\n" +
                    "   wb.occupancy_dt                          AS Webrate_Occupancy_Date \n" +
                    "FROM   Vw_Webrate_full AS wb \n" +
                    "   RIGHT JOIN (SELECT * \n" +
                    "   FROM   webrate_competitors wc \n" +
                    "   WHERE  webrate_competitors_id IN ( :competitorIds )) AS x \n" +
                    "   ON x.webrate_competitors_id = wb.webrate_competitors_id \n" +
                    "  AND wb.occupancy_dt >= :startDate \n" +
                    "  AND wb.occupancy_dt <= :endDate \n" +
                    "  AND wb.webrate_channel_id IN( :channelIds ) \n" +
                    "  AND wb.webrate_accom_type_id IN( :accomTypeIds ) \n" +
                    " AND wb.Product_ID = 1" +
                    ") AS finalData\n" +
                    "WHERE rowNUm = 1";

            Query q = crudService.getEntityManager().createNativeQuery(query);
            q.setParameter("startDate", new SimpleDateFormat("yyyy-MM-dd").format(fromDate));
            q.setParameter("endDate", new SimpleDateFormat("yyyy-MM-dd").format(toDate));
            q.setParameter("channelIds", channelIdReq);
            q.setParameter("competitorIds", competitorIds);
            q.setParameter("accomTypeIds", accomTypeIds);
            List<Object[]> resultList = q.getResultList();

            WebrateChannel objWebrateChannel = crudService.find(WebrateChannel.class, channelIdReq);

            Object[] currentRow;
            List<CompetitorRateInfo> result = new ArrayList<>();
            for (int i = 0; i < resultList.size(); i++) {

                currentRow = resultList.get(i);

                Integer webRateId = (Integer) currentRow[0];
                Integer competitorId = (Integer) currentRow[2];
                Integer channelId = (Integer) currentRow[3];
                Integer accomTypeId = (Integer) currentRow[4];
                Integer los = ((BigDecimal) currentRow[5]).toBigInteger().intValue();
                String webrateRemark = (String) currentRow[6];
                String webrateStatus = (String) currentRow[7];
                BigDecimal webrateRatevalue = (BigDecimal) currentRow[9];
                Date webrate_occupancyDate = (Date) currentRow[11];

                Date webrateGenerationDate = (null != currentRow[9]) ? ((Date) currentRow[10]) : null;

                if (i == 0 || checkIfCreateNewCompetitorRateInfoIndProductToggleOff(currentRow, resultList, i)) {

                    List<WebrateAccomClassMapping> objMapping = crudService.findByNamedQuery(WebrateAccomClassMapping.BY_ACCOMTYPE_ID, QueryParameter.with("accomTypeId", accomTypeId).parameters());
                    AccomClass accomClass = null;

                    if (isNotEmpty(objMapping)) {
                        accomClass = objMapping.get(0).getAccomClass();
                    }

                    CompetitorRateInfo dto = new CompetitorRateInfo();
                    dto.setWebRateId(webRateId);
                    dto.setOccupancyDate(webrate_occupancyDate);

                    if (null != objWebrateChannel) {
                        if ("true".equalsIgnoreCase(displayNameEnabled)) {
                            dto.setChannelName(objWebrateChannel.getWebrateChannelAlias());
                        } else {
                            dto.setChannelName(objWebrateChannel.getWebrateChannelName());
                        }
                    }
                    WebrateCompetitors objWebrateComp = crudService.find(WebrateCompetitors.class, competitorId);
                    if (null != objWebrateComp) {
                        dto.setCompetitorName(objWebrateComp.getWebrateCompetitorsAlias());
                    }
                    setAccomClassName(accomClass, dto);

                    WebrateAccomType objWebrateAccomType = crudService.find(WebrateAccomType.class, accomTypeId);
                    if (null != objWebrateAccomType) {
                        if ("true".equalsIgnoreCase(displayNameEnabled)) {
                            dto.setRoomTypeName(objWebrateAccomType.getWebrateAccomAlias());
                        } else {
                            dto.setRoomTypeName(objWebrateAccomType.getWebrateAccomName());
                        }
                    }
                    setWebRateValueOrRemarkInLOS(los, webrateRemark, webrateStatus, webrateRatevalue, dto);

                    dto.setRate(webrateRatevalue);

                    if (accomClass != null) {
                        WebrateCompetitorsAccomClass mapping = crudService.findByNamedQuerySingleResult(WebrateCompetitorsAccomClass.BY_ACCOM_ID_AND_COMP_ID,
                                QueryParameter.with(ACCOM_ID, accomClass.getId()).and("compId", competitorId).parameters());

                        if (mapping != null) {
                            if (isCompetitiveMarketPositioningEnabled) {
                                if (isRankDisabledFor(mapping)) {
                                    dto.setIncludedInMarketConstraints(defaultWebrateRanking);
                                    setRankingEnabledForDCMPC(dto, false);
                                } else {
                                    WebrateRanking webrateRanking = getWebrateRanking(webrateRankingAccomClasses, webrateRankingAccomClassOverrides, webrate_occupancyDate, mapping.getProductID(), accomClass);
                                    dto.setIncludedInMarketConstraints(webrateRanking != null ? webrateRanking : defaultWebrateRanking);
                                    setRankingEnabledForDCMPC(dto, true);
                                }
                                if (isDemandDisabledFor(mapping)) {
                                    dto.setIncludedInDemand(false);
                                } else {
                                    WebrateOverrideCompetitor webrateOverrideCompetitor = getWebrateOverrideCompetitor(webrate_occupancyDate, mapping);
                                    boolean includedInDemand = checkIfSelectedDateWithinDTARange(mapping.getDaysToArrival(), webrate_occupancyDate) &&
                                            shouldIncludeInDemand(dto, webrate_occupancyDate, webrateOverrideCompetitor, dayOfWeekWiseIgnoredChannels, webrateCompChannelMappings);
                                    dto.setIncludedInDemand(includedInDemand);
                                }
                            } else {
                                if (isDemandDisabledFor(mapping)) {
                                    dto.setIncludedInDemand(false);
                                    dto.setIncludedInMarketConstraints(defaultWebrateRanking);
                                } else {
                                    WebrateOverrideCompetitor webrateOverrideCompetitor = getWebrateOverrideCompetitor(webrate_occupancyDate, mapping);
                                    boolean includedInDemand = checkIfSelectedDateWithinDTARange(mapping.getDaysToArrival(), webrate_occupancyDate) &&
                                            shouldIncludeInDemand(dto, webrate_occupancyDate, webrateOverrideCompetitor, dayOfWeekWiseIgnoredChannels, webrateCompChannelMappings);
                                    dto.setIncludedInDemand(includedInDemand);
                                    WebrateRanking webrateRanking = getWebrateRanking(webrateRankingAccomClasses, webrateRankingAccomClassOverrides, webrate_occupancyDate, mapping.getProductID(), accomClass);
                                    dto.setIncludedInMarketConstraints(webrateRanking != null ? webrateRanking : defaultWebrateRanking);
                                }
                            }
                        }
                    }
                    setWebRateGenerationDate(webrateGenerationDate, dto);
                    result.add(dto);

                } else {
                    // Add info to previous DTO
                    CompetitorRateInfo dto = result.get(result.size() - 1);
                    if (webrateStatus.equalsIgnoreCase(WEBRATE_ACTIVE_STATUS_CODE)) {
                        dto.getRateByLengthOfStay().put(los, webrateRatevalue.setScale(2, RoundingMode.HALF_UP).toPlainString());
                    } else if (webrateStatus.equalsIgnoreCase(WEBRATE_CLOSED_STATUS_CODE)) {
                        dto.getRateByLengthOfStay().put(los, WEBRATE_CLOSED_STATUS);
                    } else if (webrateStatus.equalsIgnoreCase(WEBRATE_FENCED_STATUS_CODE)) {
                        dto.getRateByLengthOfStay().put(los, WEBRATE_RATE_RESTRICTED_STATUS);
                    } else {
                        dto.getRateByLengthOfStay().put(los, webrateStatus.toUpperCase());
                    }

                    setMaxWebrateGenerationDate(dto, webrateGenerationDate);
                }
            }
            for (CompetitorRateInfo obj : result) {
                finalResult.add(obj);
            }
        }
        return finalResult;
    }

    private boolean checkIfCreateNewCompetitorRateInfo(Object[] currentRow, List<Object[]> resultList, int i) {
        Integer competitorId = (Integer) currentRow[2];
        Integer channelId = (Integer) currentRow[3];
        Integer accomTypeId = (Integer) currentRow[4];
        String webrateRemark = (String) currentRow[7];
        BigDecimal webrateRatevalue = (BigDecimal) currentRow[10];
        Date webrateGenerationDate = (null != currentRow[10]) ? ((Date) currentRow[11]) : null;

        Object[] previousRow = resultList.get(i - 1);

        if (channelId.intValue() != ((Integer) previousRow[3]).intValue()) {
            return true;
        }
        if (competitorId.intValue() != ((Integer) previousRow[2]).intValue()) {
            return true;
        }
        if (accomTypeId.intValue() != ((Integer) previousRow[4]).intValue()) {
            return true;
        }
        if (!webrateRemark.equals(String.valueOf(previousRow[7]))) {
            return true;
        }
        if (webrateRatevalue.compareTo((BigDecimal) previousRow[10]) != 0) {
            return true;
        }
        if (webrateGenerationDate.compareTo((Date) previousRow[11]) != 0) {
            return true;
        }
        return false;
    }

    private boolean checkIfCreateNewCompetitorRateInfoIndProductToggleOff(Object[] currentRow, List<Object[]> resultList, int i) {
        Integer competitorId = (Integer) currentRow[2];
        Integer channelId = (Integer) currentRow[3];
        Integer accomTypeId = (Integer) currentRow[4];
        String webrateRemark = (String) currentRow[6];
        BigDecimal webrateRatevalue = (BigDecimal) currentRow[9];
        Date webrateGenerationDate = (null != currentRow[10]) ? ((Date) currentRow[11]) : null;

        Object[] previousRow = resultList.get(i - 1);

        if (channelId.intValue() != ((Integer) previousRow[3]).intValue()) {
            return true;
        }
        if (competitorId.intValue() != ((Integer) previousRow[2]).intValue()) {
            return true;
        }
        if (accomTypeId.intValue() != ((Integer) previousRow[4]).intValue()) {
            return true;
        }
        if (!webrateRemark.equals(String.valueOf(previousRow[7]))) {
            return true;
        }
        if (webrateRatevalue.compareTo((BigDecimal) previousRow[10]) != 0) {
            return true;
        }
        if (webrateGenerationDate.compareTo((Date) previousRow[11]) != 0) {
            return true;
        }
        return false;
    }

    /**
     * This is replica of above getCompetitorRates method, with below changes
     * - Used Vw_Webrate_full instead of webRate table,to get product_name for row.
     * - Changed query to improvise and to remove unnecessary looping of channel_ids and added product_id in query
     * - crudServices were getting called in loop so removed that , read all values before loop
     * - Removed one unnecessary loop which was at the end of method
     * - This is getting called only independentToggle is true
     */

    public List<CompetitorRateInfo> getCompetitorRatesView(Date fromDate, Date toDate, List<Integer> channelIds, List<Integer> competitorIds,
                                                           List<Integer> accomTypeIds, boolean isCompetitiveMarketPositioningEnabled,
                                                           List<Integer> productIds) {
        Query q;
        boolean displayNameEnabled = configService.getBooleanParameterValue(GUIConfigParamName.DISPLAY_NAME_DISPLAY_NAME_ENABLED.value());
        boolean isRDLToggleEnabled = isRdlEnabled();
        if (isRDLToggleEnabled) {
            q = crudService.getEntityManager().createNativeQuery(QUERY_FOR_RDL_PROPERTIES);
        } else {
            q = crudService.getEntityManager().createNativeQuery(query);
        }
        q.setParameter("startDate", new SimpleDateFormat("yyyy-MM-dd").format(fromDate));
        q.setParameter("endDate", new SimpleDateFormat("yyyy-MM-dd").format(toDate));
        q.setParameter("channelIds", channelIds);
        q.setParameter("competitorIds", competitorIds);
        q.setParameter("accomTypeIds", accomTypeIds);
        q.setParameter("productIds", productIds);
        List<Object[]> resultList = q.getResultList();

        Object[] currentRow;
        List<CompetitorRateInfo> result = new ArrayList<>();
        Map<Integer, WebrateChannel> webRateChannelMap = new HashMap<>();
        Map<Integer, WebrateCompetitors> webrateCompetitorsMap = new HashMap<>();
        Map<Integer, WebrateAccomType> webRateAccomTypeMap = new HashMap<>();
        Map<String, WebrateCompetitorsAccomClass> webRateCompetitorsAccomClassMap = new HashMap<>();
        List<WebrateRankingAccomClass> webrateRankingAccomClasses = new ArrayList<>();
        List<WebrateRankingAccomClassOverride> webrateRankingAccomClassOverrides = new ArrayList<>();
        Map<Integer, Product> productMap = new HashMap<>();
        WebrateRanking defaultWebrateRanking = null;
        if (CollectionUtils.isNotEmpty(resultList)) {
            defaultWebrateRanking = crudService.findByNamedQuerySingleResult(WebrateRanking.FIND_BY_NAME, QueryParameter.with("webrateRankingName", "None").parameters());
            webrateRankingAccomClasses = crudService.findAll(WebrateRankingAccomClass.class);
            webrateRankingAccomClassOverrides = getWebrateRankingAccomClassOverrides(fromDate, toDate);

            webRateChannelMap = getWebrateChannelMap();

            webrateCompetitorsMap = getWebrateCompetitorsMap();

            webRateAccomTypeMap = getWebrateAccomTypeMap();

            webRateCompetitorsAccomClassMap = getWebrateCompetitorsAccomClassMap();

            productMap = getProductMap();
        }

        for (int i = 0; i < resultList.size(); i++) {

            currentRow = resultList.get(i);

            Integer webRateId = (Integer) currentRow[0];
            Integer competitorId = (Integer) currentRow[2];
            Integer channelId = (Integer) currentRow[3];
            Integer accomTypeId = (Integer) currentRow[4];
            Integer los = ((BigDecimal) currentRow[5]).toBigInteger().intValue();
            String webrateRemark = (String) currentRow[6];
            String webrateStatus = (String) currentRow[7];
            BigDecimal webrateRatevalue = (BigDecimal) currentRow[9];
            Date webrate_occupancyDate = (Date) currentRow[11];
            Date webrateGenerationDate = (null != currentRow[9]) ? ((Date) currentRow[10]) : null;
            Integer productId = ((BigInteger) currentRow[12]).intValue();


            List<WebrateAccomClassMapping> objMapping = crudService.findByNamedQuery(WebrateAccomClassMapping.BY_ACCOMTYPE_ID, QueryParameter.with("accomTypeId", accomTypeId).parameters());
            AccomClass accomClass = null;

            if (isNotEmpty(objMapping)) {
                accomClass = objMapping.get(0).getAccomClass();
            }

            CompetitorRateInfo dto = new CompetitorRateInfo();
            dto.setWebRateId(webRateId);
            dto.setOccupancyDate(webrate_occupancyDate);

            setWebRateChannel(displayNameEnabled, webRateChannelMap, channelId, dto);
            setCompetitorName(webrateCompetitorsMap, competitorId, dto);
            setAccomClassName(accomClass, dto);

            setWebRateAccomType(displayNameEnabled, webRateAccomTypeMap, accomTypeId, dto);
            setWebRateValueOrRemarkInLOS(los, webrateRemark, webrateStatus, webrateRatevalue, dto);

            if (los.intValue() == 1) {
                dto.setRate(webrateRatevalue);
            }

            setMarketConstraintAndIncludedInDemand(isCompetitiveMarketPositioningEnabled, webRateCompetitorsAccomClassMap, webrateRankingAccomClasses, webrateRankingAccomClassOverrides, defaultWebrateRanking, competitorId, webrate_occupancyDate, productId, accomClass, dto);
            setWebRateGenerationDate(webrateGenerationDate, dto);
            dto.setProductName(productMap.get(productId).getName());
            if (isRDLToggleEnabled) {
                dto.setWebrateTypeName((String) currentRow[13]);
            }
            result.add(dto);
        }
        return result;
    }

    public List<CompetitorRates> getCompetitorRates(Date startDate, Date endDate, boolean includeTax, int page, int size) {
        boolean isRDLToggleEnabled = isRdlEnabled();
        String competitorRatesQuery = isRDLToggleEnabled ? GET_COMPETITOR_RATES_RDL :GET_COMPETITOR_RATES_NON_RDL;

        Map<String, Object> parameters = QueryParameter.with("startDate", startDate)
                .and("endDate", endDate).and("includeTax", includeTax).parameters();
        return crudService.findByNamedQuery(competitorRatesQuery, parameters, page * size, size);
    }

    private AccomClass getAccomClass(Map<Integer, WebrateAccomClassMapping> webRateAccomClassMappingMap, Integer accomTypeId, AccomClass accomClass) {
        WebrateAccomClassMapping webrateAccomClassMapping = webRateAccomClassMappingMap.get(accomTypeId);
        if (null != webrateAccomClassMapping) {
            accomClass = webrateAccomClassMapping.getAccomClass();
        }
        return accomClass;
    }

    private void setAccomClassName(AccomClass accomClass, CompetitorRateInfo dto) {
        if (accomClass != null) {
            dto.setAccomClassName(accomClass.getName());
        }
    }

    private void setCompetitorName(Map<Integer, WebrateCompetitors> webrateCompetitorsMap, Integer competitorId, CompetitorRateInfo dto) {
        WebrateCompetitors webrateCompetitors = webrateCompetitorsMap.get(competitorId);
        if (null != webrateCompetitors) {
            dto.setCompetitorName(webrateCompetitors.getWebrateCompetitorsAlias());
        }
    }

    private void setWebRateChannel(boolean displayNameEnabled, Map<Integer, WebrateChannel> webRateChannelMap, Integer channelId, CompetitorRateInfo dto) {
        WebrateChannel webrateChannel = webRateChannelMap.get(channelId);
        if (null != webrateChannel) {
            if (displayNameEnabled) {
                dto.setChannelName(webrateChannel.getWebrateChannelAlias());
            } else {
                dto.setChannelName(webrateChannel.getWebrateChannelName());
            }
        }
    }

    private void setWebRateAccomType(boolean displayNameEnabled, Map<Integer, WebrateAccomType> webRateAccomTypeMap, Integer accomTypeId, CompetitorRateInfo dto) {
        WebrateAccomType webrateAccomType = webRateAccomTypeMap.get(accomTypeId);
        if (null != webrateAccomType) {
            if (displayNameEnabled) {
                dto.setRoomTypeName(webrateAccomType.getWebrateAccomAlias());
            } else {
                dto.setRoomTypeName(webrateAccomType.getWebrateAccomName());
            }
        }
    }

    private void setWebRateValueOrRemarkInLOS(Integer los, String webrateRemark, String webrateStatus, BigDecimal webrateRatevalue, CompetitorRateInfo dto) {
        dto.setRemarks(webrateRemark);
        if (!"NA".equalsIgnoreCase(webrateStatus)) {
            if (webrateStatus.equalsIgnoreCase(WEBRATE_ACTIVE_STATUS_CODE)) {
                dto.getRateByLengthOfStay().put(los, webrateRatevalue.setScale(2, RoundingMode.HALF_UP).toPlainString());
            } else if (webrateStatus.equalsIgnoreCase(WEBRATE_CLOSED_STATUS_CODE)) {
                dto.getRateByLengthOfStay().put(los, WEBRATE_CLOSED_STATUS);
            } else if (webrateStatus.equalsIgnoreCase(WEBRATE_FENCED_STATUS_CODE)) {
                dto.getRateByLengthOfStay().put(los, WEBRATE_RATE_RESTRICTED_STATUS);
            } else {
                dto.getRateByLengthOfStay().put(los, webrateStatus.toUpperCase());
            }
        }
    }

    /**
     * Extracted from above method, it's existing code
     */
    @SuppressWarnings({"squid:S3776"})
    private void setMarketConstraintAndIncludedInDemand(boolean isCompetitiveMarketPositioningEnabled, Map<String, WebrateCompetitorsAccomClass> webRateCompetitorsAccomClassMap, List<WebrateRankingAccomClass> webrateRankingAccomClasses, List<WebrateRankingAccomClassOverride> webrateRankingAccomClassOverrides, WebrateRanking defaultWebrateRanking, Integer competitorId, Date webrate_occupancyDate, Integer productId, AccomClass accomClass, CompetitorRateInfo dto) {
        Map<DayOfWeek, List<String>> dayOfWeekWiseIgnoredChannels = webrateChannelIgnoreService.getDayOfWeekWiseIgnoredChannels();
        List<WebrateCompChannelMapping> webrateCompChannelMappings = webrateShoppingDataService.getWebrateCompChannelMappings();

        if (accomClass != null) {
            WebrateCompetitorsAccomClass mapping = webRateCompetitorsAccomClassMap.get(productId + "-" + accomClass.getId() + "-" + competitorId);

            if (mapping != null) {
                if (isCompetitiveMarketPositioningEnabled) {
                    if (isRankDisabledFor(mapping)) {
                        dto.setIncludedInMarketConstraints(defaultWebrateRanking);
                        setRankingEnabledForDCMPC(dto, false);
                    } else {
                        WebrateRanking webrateRanking = getWebrateRanking(webrateRankingAccomClasses, webrateRankingAccomClassOverrides, webrate_occupancyDate, productId, accomClass);
                        dto.setIncludedInMarketConstraints(webrateRanking != null ? webrateRanking : defaultWebrateRanking);
                        setRankingEnabledForDCMPC(dto, true);
                    }
                    if (isDemandDisabledFor(mapping)) {
                        dto.setIncludedInDemand(false);
                    } else {
                        WebrateOverrideCompetitor webrateOverrideCompetitor = getWebrateOverrideCompetitor(webrate_occupancyDate, mapping);
                        dto.setIncludedInDemand(checkIfSelectedDateWithinDTARange(mapping.getDaysToArrival(), webrate_occupancyDate) &&
                                shouldIncludeInDemand(dto, webrate_occupancyDate, webrateOverrideCompetitor, dayOfWeekWiseIgnoredChannels, webrateCompChannelMappings));
                    }
                } else {
                    if (isDemandDisabledFor(mapping)) {
                        dto.setIncludedInDemand(false);
                        dto.setIncludedInMarketConstraints(defaultWebrateRanking);
                    } else {
                        WebrateOverrideCompetitor webrateOverrideCompetitor = getWebrateOverrideCompetitor(webrate_occupancyDate, mapping);
                        dto.setIncludedInDemand(checkIfSelectedDateWithinDTARange(mapping.getDaysToArrival(), webrate_occupancyDate) &&
                                shouldIncludeInDemand(dto, webrate_occupancyDate, webrateOverrideCompetitor, dayOfWeekWiseIgnoredChannels, webrateCompChannelMappings));

                        WebrateRanking webrateRanking = getWebrateRanking(webrateRankingAccomClasses, webrateRankingAccomClassOverrides, webrate_occupancyDate, productId, accomClass);
                        dto.setIncludedInMarketConstraints(webrateRanking != null ? webrateRanking : defaultWebrateRanking);
                    }
                }
            }
        }
    }

    private void setWebRateGenerationDate(Date webrateGenerationDate, CompetitorRateInfo dto) {
        if (webrateGenerationDate != null) {
            dto.setWebrateGenerationDate(new DateParameter(webrateGenerationDate));
        } else {
            dto.setWebrateGenerationDate(new DateParameter());
        }
    }

    private Map<Integer, Product> getProductMap() {
        List<Product> productList = crudService.findAll(Product.class);
        return productList.stream().collect(Collectors.toMap(Product::getId, Function.identity()));
    }

    private Map<String, WebrateCompetitorsAccomClass> getWebrateCompetitorsAccomClassMap() {
        List<WebrateCompetitorsAccomClass> webRateCompetitorsAccomClasses = crudService.findAll(WebrateCompetitorsAccomClass.class);
        return webRateCompetitorsAccomClasses.stream().collect(Collectors.toMap(WebrateCompetitorsAccomClass::getUniqueKey, Function.identity()));
    }

    private Map<Integer, WebrateAccomType> getWebrateAccomTypeMap() {
        List<WebrateAccomType> webRateAccomTypeList = crudService.findAll(WebrateAccomType.class);
        return webRateAccomTypeList.stream().collect(Collectors.toMap(WebrateAccomType::getId, Function.identity()));
    }

    private Map<Integer, WebrateCompetitors> getWebrateCompetitorsMap() {
        List<WebrateCompetitors> webRateCompetitorsList = getWebrateCompetitors();
        return webRateCompetitorsList.stream().collect(Collectors.toMap(WebrateCompetitors::getId, Function.identity()));
    }

    private Map<Integer, WebrateChannel> getWebrateChannelMap() {
        List<WebrateChannel> webrateChannelList = getWebrateChannels();
        return webrateChannelList.stream().collect(Collectors.toMap(WebrateChannel::getId, Function.identity()));
    }

    private List<WebrateRankingAccomClassOverride> getWebrateRankingAccomClassOverrides(Date fromDate, Date toDate) {
        return crudService.findByNamedQuery(WebrateRankingAccomClassOverride.BY_DATE_RANGE, QueryParameter.with("fromDate", fromDate).and("toDate", toDate).parameters());
    }

    protected WebrateRanking getWebrateRanking(List<WebrateRankingAccomClass> webrateRankingAccomClasses, List<WebrateRankingAccomClassOverride> webrateRankingAccomClassOverrides, Date occupancyDate, Integer productId, AccomClass accomClass) {
        if (CollectionUtils.isNotEmpty(webrateRankingAccomClassOverrides)) {
            WebrateRankingAccomClassOverride webrateRankingAccomClassOverride = webrateRankingAccomClassOverrides.stream().filter(wraco -> wraco.getProductID().equals(productId) && wraco.getAccomClass().getId().equals(accomClass.getId())).findFirst().orElse(null);
            if (webrateRankingAccomClassOverride != null) {
                return getValueForDayOfWeek(webrateRankingAccomClassOverride, occupancyDate);
            }
        }
        WebrateRankingAccomClass webrateRankingAccomClass = webrateRankingAccomClasses.stream().filter(wrac -> wrac.getProductID().equals(productId) && wrac.getAccomClass().getId().equals(accomClass.getId())).findFirst().orElse(null);
        return getValueForDayOfWeek(webrateRankingAccomClass, occupancyDate);
    }

    protected WebrateRanking getValueForDayOfWeek(WebrateRankingAccomClass webrateRankingAccomClass, Date occupancyDate) {
        if (webrateRankingAccomClass != null && occupancyDate != null) {
            switch (DateUtil.getDayOfWeekShortName(occupancyDate)) {
                case "Sun":
                    return webrateRankingAccomClass.getWebrateRankingSunday();
                case "Mon":
                    return webrateRankingAccomClass.getWebrateRankingMonday();
                case "Tue":
                    return webrateRankingAccomClass.getWebrateRankingTuesday();
                case "Wed":
                    return webrateRankingAccomClass.getWebrateRankingWednesday();
                case "Thu":
                    return webrateRankingAccomClass.getWebrateRankingThursday();
                case "Fri":
                    return webrateRankingAccomClass.getWebrateRankingFriday();
                case "Sat":
                    return webrateRankingAccomClass.getWebrateRankingSaturday();
                default:
                    return null;
            }
        }
        return null;
    }

    protected WebrateRanking getValueForDayOfWeek(WebrateRankingAccomClassOverride webrateRankingAccomClassOverride, Date occupancyDate) {
        if (webrateRankingAccomClassOverride != null && occupancyDate != null) {
            switch (DateUtil.getDayOfWeekShortName(occupancyDate)) {
                case "Sun":
                    return webrateRankingAccomClassOverride.getWebrateRankingOvrSunday();
                case "Mon":
                    return webrateRankingAccomClassOverride.getWebrateRankingOvrMonday();
                case "Tue":
                    return webrateRankingAccomClassOverride.getWebrateRankingOvrTuesday();
                case "Wed":
                    return webrateRankingAccomClassOverride.getWebrateRankingOvrWednesday();
                case "Thu":
                    return webrateRankingAccomClassOverride.getWebrateRankingOvrThursday();
                case "Fri":
                    return webrateRankingAccomClassOverride.getWebrateRankingOvrFriday();
                case "Sat":
                    return webrateRankingAccomClassOverride.getWebrateRankingOvrSaturday();
                default:
                    return null;
            }
        }
        return null;
    }

    private boolean isDemandDisabledFor(WebrateCompetitorsAccomClass webrateCompetitorsAccomClass) {
        return DISABLE_WEBRATE_COMPETITOR_CLASS_CODE.equals(webrateCompetitorsAccomClass.getDemandEnabled());
    }

    protected boolean isRankDisabledFor(WebrateCompetitorsAccomClass webrateCompetitorsAccomClass) {
        return DISABLE_WEBRATE_COMPETITOR_CLASS_CODE.equals(webrateCompetitorsAccomClass.getRankingEnabled());
    }

    public WebrateOverrideCompetitor getWebrateOverrideCompetitor(Date occupancyDate, WebrateCompetitorsAccomClass mapping) {
        List<WebrateOverrideCompetitorDetails> webrateOverrideCompetitorDetailsList = crudService
                .findByNamedQuery(WebrateOverrideCompetitorDetails.BY_ACCOMMAPPING_IDS,
                        QueryParameter.with("compAccomClassIds", Arrays.asList(mapping.getId())).parameters());

        return getWebrateOverrideCompetitor(occupancyDate, webrateOverrideCompetitorDetailsList);
    }

    public WebrateOverrideCompetitor getWebrateOverrideCompetitorForOptimisedPricingScreen(Date occupancyDate, WebrateCompetitorsAccomClass mapping, WebRateCompetitorMappingsDto webRateCompetitorMappingsDto) {
        List<WebrateOverrideCompetitorDetails> webrateOverrideCompetitorDetailsList = getWebrateOverrideCompetitorDetails(mapping, webRateCompetitorMappingsDto);
        return getWebrateOverrideCompetitor(occupancyDate, webrateOverrideCompetitorDetailsList);
    }

    private static WebrateOverrideCompetitor getWebrateOverrideCompetitor(Date occupancyDate, List<WebrateOverrideCompetitorDetails> webrateOverrideCompetitorDetailsList) {
        for (WebrateOverrideCompetitorDetails webrateOverrideCompetitorDetails : webrateOverrideCompetitorDetailsList) {
            WebrateOverrideCompetitor webrateOverrideCompetitor = webrateOverrideCompetitorDetails.getWebrateOverrideCompetitor();
            if (DateUtil.isDateBetween(webrateOverrideCompetitor.getCompetitorOverrideStartDT(), webrateOverrideCompetitor.getCompetitorOverrideEndDT(), occupancyDate)) {
                return webrateOverrideCompetitor;
            }
        }
        return null;
    }

    private List<WebrateOverrideCompetitorDetails> getWebrateOverrideCompetitorDetails(WebrateCompetitorsAccomClass mapping, WebRateCompetitorMappingsDto webRateCompetitorMappingsDto) {
        if(webRateCompetitorMappingsDto.isPricingScreenOptimizationEnabled()) {
            return webRateCompetitorMappingsDto.getWebrateOvrdCompDtls().getOrDefault(mapping.getId(), Collections.emptyList());
        } else {
            return crudService.findByNamedQuery(WebrateOverrideCompetitorDetails.BY_ACCOMMAPPING_IDS,
                    QueryParameter.with("compAccomClassIds", Arrays.asList(mapping.getId())).parameters());
        }
    }

    private void setMaxWebrateGenerationDate(CompetitorRateInfo dto, Date webrateGenerationDate) {
        if (webrateGenerationDate != null && (dto.getWebrateGenerationDate().getTime() == null
                || dto.getWebrateGenerationDate().getTime().before(webrateGenerationDate))) {

            dto.setWebrateGenerationDate(new DateParameter(webrateGenerationDate));

        }
    }

    @Deprecated
    public WebrateChannel getDefaultChannel(DateParameter occupancyDate) {
        return getDefaultChannel(occupancyDate.getTime());
    }

    public WebrateChannel getDefaultChannel(LocalDate occupancyDate) {
        return getDefaultChannel(occupancyDate.toDate());
    }

    public WebrateChannel getDefaultChannel(Date occupancyDate) {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();

        WebrateOverrideChannel woc = crudService.findByNamedQuerySingleResult(WebrateOverrideChannel.BY_PROPERTY_ID_AND_DATE, QueryParameter.with(PROPERTY_ID, propertyId).and(DATE, occupancyDate).parameters());
        if (woc != null) {
            return getWebrateChannelForDayOfWeek(woc, occupancyDate);
        }

        //Defaulting to BAR for now; this will need to be updated once we start supporting Independent products
        WebrateDefaultChannel wdc = crudService.findByNamedQuerySingleResult(WebrateDefaultChannel.BY_PROPERTY_ID_FOR_BAR_ONLY, QueryParameter.with(PROPERTY_ID, propertyId).parameters());
        if (wdc != null) {
            return getWebrateChannelForDayOfWeek(wdc, occupancyDate);
        }

        throw new TetrisException(ErrorCode.NO_WEBRATE_DEFAULT_CHANNEL, "Could not find a WebrateDefaultChannel for propertyID " + propertyId);
    }

    @SuppressWarnings("unchecked")
    public List<AccomClass> getAccomClassesHavingWebrateAccomTypes() {
        return crudService.findByNamedQuery(WebrateAccomClassMapping.BY_PROPERTY_ID, QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @SuppressWarnings("unchecked")
    public List<WebrateAccomType> getRoomTypes(int accomClassId) {
        return crudService.findByNamedQuery(WebrateAccomClassMapping.BY_ACCOM_CLASS_ID, QueryParameter.with(ACCOM_CLASS_ID, accomClassId).parameters());
    }

    @SuppressWarnings("unchecked")
    public List<WebrateAccomType> getRoomTypes(List<Integer> accomClassIds) {
        if (accomClassIds == null || accomClassIds.isEmpty()) {
            return new ArrayList<WebrateAccomType>();
        }
        return crudService.findByNamedQuery(WebrateAccomClassMapping.GET_WEBRATE_ACCOM_TYPE_BY_ACCOM_CLASS_IDS, QueryParameter.with("accomClassIds", accomClassIds).parameters());
    }

    private WebrateChannel getWebrateChannelForDayOfWeek(WebrateDefaultChannel wdc, Date date) {
        int dayOfWeek = getDayOfWeek(date);
        switch (dayOfWeek) {
            case Calendar.SUNDAY:
                return wdc.getWebrateChannelSun();
            case Calendar.MONDAY:
                return wdc.getWebrateChannelMon();
            case Calendar.TUESDAY:
                return wdc.getWebrateChannelTues();
            case Calendar.WEDNESDAY:
                return wdc.getWebrateChannelWed();
            case Calendar.THURSDAY:
                return wdc.getWebrateChannelThurs();
            case Calendar.FRIDAY:
                return wdc.getWebrateChannelFri();
            case Calendar.SATURDAY:
                return wdc.getWebrateChannelSat();
            default:
                return null;
        }

    }

    private WebrateChannel getWebrateChannelForDayOfWeek(WebrateOverrideChannel woc, Date date) {
        int dayOfWeek = getDayOfWeek(date);
        switch (dayOfWeek) {
            case Calendar.SUNDAY:
                return woc.getWebrateChannelSun();
            case Calendar.MONDAY:
                return woc.getWebrateChannelMon();
            case Calendar.TUESDAY:
                return woc.getWebrateChannelTues();
            case Calendar.WEDNESDAY:
                return woc.getWebrateChannelWed();
            case Calendar.THURSDAY:
                return woc.getWebrateChannelThurs();
            case Calendar.FRIDAY:
                return woc.getWebrateChannelFri();
            case Calendar.SATURDAY:
                return woc.getWebrateChannelSat();
            default:
                return null;
        }

    }

    private int getDayOfWeek(Date date) {
        Calendar c = new GregorianCalendar();
        c.setTime(date);
        return c.get(Calendar.DAY_OF_WEEK);
    }

    @Deprecated
    public List<CompetitorRateInfo> getCompetitorRates(
            DateParameter occupancyDate, List<Integer> channelIds,
            List<Integer> competitorIds, List<Integer> accomTypeIds) {

        return getCompetitorRates(occupancyDate.getTime(), channelIds, competitorIds, accomTypeIds);
    }

    public List<CompetitorRateInfo> getCompetitorRates(
            LocalDate occupancyDate, List<Integer> channelIds,
            List<Integer> competitorIds, List<Integer> accomTypeIds) {

        return getCompetitorRates(occupancyDate.toDate(), channelIds, competitorIds, accomTypeIds);
    }

    public List<CompetitorRateInfo> getCompetitorRatesForAccomClassList(Date occupancyDate, List<Integer> accomClasses, List<Integer> productIds) {
        return getCompetitorRatesForAccomClassList(occupancyDate, accomClasses, false, productIds);
    }

    public List<CompetitorRateInfo> getCompetitorRatesForAccomClassList(Date occupancyDate, List<Integer> accomClasses,
                                                                        boolean isCompetitiveMarketPositioningEnabled,
                                                                        List<Integer> productIds) {
        List<WebrateAccomType> roomTypes = getRoomTypes(accomClasses);
        List<Integer> accomTypeIds = roomTypes.stream().map(WebrateAccomType::getId).collect(Collectors.toList());
        if (accomTypeIds.isEmpty()) {
            return Collections.emptyList();
        }

        boolean independentProductOrRDLEnabled = configParamsService.getBooleanParameterValue(INDEPENDENT_PRODUCTS_ENABLED) || configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED);
        boolean isMapRoomTypeToMultipleRoomClassesEnabled = configParamsService.getBooleanParameterValue(IS_MAP_ACCOM_TYPE_TO_MULTIPLE_ACCOM_CLASSES_ENABLED);

        if (isMapRoomTypeToMultipleRoomClassesEnabled) {
            if (independentProductOrRDLEnabled) {
                return getCompetitorRatesView(occupancyDate, occupancyDate, getAllChannelIds(), getCompetitorIds(),
                        accomTypeIds, accomClasses, isCompetitiveMarketPositioningEnabled, productIds);
            }
            return getCompetitorRates(occupancyDate, occupancyDate, getAllChannelIds(), getCompetitorIds(), accomTypeIds,
                    accomClasses, isCompetitiveMarketPositioningEnabled);
        }

        if (independentProductOrRDLEnabled) {
            return getCompetitorRatesView(occupancyDate, occupancyDate, getAllChannelIds(), getCompetitorIds(),
                    accomTypeIds, isCompetitiveMarketPositioningEnabled, productIds);
        }

        return getCompetitorRates(occupancyDate, occupancyDate, getAllChannelIds(), getCompetitorIds(), accomTypeIds, isCompetitiveMarketPositioningEnabled);
    }

    public List<CompetitorRateInfo> getCompetitorRatesForAccomClassListForDateRange(Date fromDate, Date toDate, List<Integer> accomClasses, boolean isCompetitiveMarketPositioningEnabled) {
        List<WebrateAccomType> roomTypes = getRoomTypes(accomClasses);
        List<Integer> accomTypeIds = roomTypes.stream().map(WebrateAccomType::getId).collect(Collectors.toList());
        return accomTypeIds.isEmpty() ? Collections.emptyList() : getCompetitorRates(fromDate, toDate, getAllChannelIds(), getCompetitorIds(), accomTypeIds, isCompetitiveMarketPositioningEnabled);
    }

    public List<CompetitorRateInfo> getCompetitorRates(Date occupancyDate, AccomClass accomClass) {
        List<WebrateAccomType> roomTypes = getRoomTypes(accomClass.getId());
        List<Integer> accomTypeIds = roomTypes.stream().map(WebrateAccomType::getId).collect(Collectors.toList());
        return getCompetitorRates(occupancyDate, getAllChannelIds(), getCompetitorIds(), accomTypeIds);
    }

    public List<Integer> getAllChannelIds() {
        List<WebrateChannel> allChannelsByProperty = getWebrateChannels();
        return allChannelsByProperty.stream().map(WebrateChannel::getId).collect(Collectors.toList());
    }

    private List<WebrateChannel> getWebrateChannels() {
        return webrateShoppingDataService.getAllChannelsByProperty();
    }

    public List<Integer> getCompetitorIds() {
        List<WebrateCompetitors> allCompetitorsByProperty = getWebrateCompetitors();
        return allCompetitorsByProperty.stream().map(WebrateCompetitors::getId).collect(Collectors.toList());
    }

    private List<WebrateCompetitors> getWebrateCompetitors() {
        return webrateShoppingDataService.getAllCompetitorsByProperty();
    }

    public List<CompetitorRateInfo> getCompetitorsRateForDate(LocalDate date, Integer accomClassId, int lengthOfStay) {
        if (lengthOfStay == -1) {
            lengthOfStay = 1;
        }
        Integer channelId = findChannelIdForTheDay(date);
        List<CompetitorRateInfo> competitorRateFor = findCompetitorRateFor(date, accomClassId, channelId, lengthOfStay);
        if (!isPricingInvestigatorFetchCompetitorPricesFromAllChannelsEnabled()) {
            return getCompetitorRateInfos(competitorRateFor);
        }
        return competitorRateFor != null ? new ArrayList<>(competitorRateFor) : new ArrayList<>();

    }

    public List<CompetitorRateInfo> getCompetitorsRateForDateAndCompetiorChart(java.time.LocalDate date, Integer accomClassId, int lengthOfStay) {
        if (lengthOfStay == -1) {
            lengthOfStay = 1;
        }
        Integer channelId = findChannelIdForTheDay(LocalDateUtils.toJodaLocalDate(date));
        List<CompetitorRateInfo> competitorRateFor = findCompetitorRateForChart(date, accomClassId, channelId, lengthOfStay);
        return getCompetitorRateInfos(competitorRateFor);
    }

    private List<CompetitorRateInfo> getCompetitorRateInfos(List<CompetitorRateInfo> competitorRateFor) {
        Map<String, CompetitorRateInfo> uniqueCompetitors = new HashMap<>();
        if (isNotEmpty(competitorRateFor)) {
            competitorRateFor.forEach(competitorRateInfo -> {
                uniqueCompetitors.put(competitorRateInfo.getCompetitorName(), competitorRateInfo);
            });
        }
        Collection<CompetitorRateInfo> values = uniqueCompetitors.values();
        return new ArrayList<>(values);
    }

    private List<CompetitorRateInfo> findCompetitorRateFor(LocalDate date, Integer accomClassId, Integer channelId, Integer lengthOfStay) {
        List<Integer> ignoreCompetitorIdList = getIgnoreCompetitorData(DateUtil.convertJodaToJavaLocalDate(date), accomClassId);
        //Defaulting to BAR for now; this will need to be updated if we start supporting Independent products in Investigator
        boolean isCompetitiveMarketPositionConstraintApplicable = isCompetitiveMarketPositionConstraintApplicable(accomClassId, date.toDate(), 1);
        Boolean isPricingInvestigatorFetchCompetitorPricesFromAllChannelsEnabled = isPricingInvestigatorFetchCompetitorPricesFromAllChannelsEnabled();
        String findCompetitorRatesForDateAndChannel;
        QueryParameter queryParameter = QueryParameter.with(DATE, date)
                .and(ACCOM_CLASS_ID, accomClassId)
                .and("selfCompetitorId", getSelfCompetitorId())
                .and(ignoreCompetitorIds, StringUtils.join(ignoreCompetitorIdList, ","))
                .and(systemDate, getSystemDate());

        if (isRdlEnabled() || configService.getBooleanParameterValue(INDEPENDENT_PRODUCTS_ENABLED) ) {
            if (isPricingInvestigatorFetchCompetitorPricesFromAllChannelsEnabled) {
                Boolean isInsufficientCompetitiors = isInsufficientCompetitiorsAvailable(accomClassId, date.toDate(), 1);
                if (isCompetitiveMarketPositionConstraintApplicable && !isInsufficientCompetitiors)
                    queryParameter.and(CHANNEL_ID, channelId);
                findCompetitorRatesForDateAndChannel = getRateShoppingRateTypeForPricingInvestigatorAllChannel
                        (isCompetitiveMarketPositionConstraintApplicable, isInsufficientCompetitiors);
            } else {
                queryParameter.and(CHANNEL_ID, channelId);
                findCompetitorRatesForDateAndChannel = getRateShoppingRateTypeForPricingInvestigator
                        (isCompetitiveMarketPositionConstraintApplicable);
            }
        } else {
            findCompetitorRatesForDateAndChannel = getQueryString(date.toDate(), accomClassId, channelId, lengthOfStay, isCompetitiveMarketPositionConstraintApplicable, isPricingInvestigatorFetchCompetitorPricesFromAllChannelsEnabled, queryParameter, EXEC_FIND_COMPETITOR_RATES_FOR_DATE_AND_ALL_CHANNEL_RANKING_ENABLED, EXEC_FIND_COMPETITOR_RATES_FOR_DATE_AND_ALL_CHANNEL);
        }

        return getCompetitorsResult(findCompetitorRatesForDateAndChannel, queryParameter);
    }

    private List<CompetitorRateInfo> findCompetitorRateForChart(java.time.LocalDate date, Integer accomClassId, Integer channelId, Integer lengthOfStay) {
        List<Integer> ignoreCompetitorIdList = getIgnoreCompetitorData(date, accomClassId);
        //Defaulting to BAR for now; this will need to be updated if we start supporting Independent products in Investigator
        boolean isCompetitiveMarketPositionConstraintApplicable = isCompetitiveMarketPositionConstraintApplicable(accomClassId, LocalDateUtils.toDate(date), 1);
        Boolean isPricingInvestigatorFetchCompetitorPricesFromAllChannelsEnabled = isPricingInvestigatorFetchCompetitorPricesFromAllChannelsEnabled();
        String findCompetitorRatesForDateAndChannel;
        QueryParameter queryParameter = QueryParameter.with(DATE, date)
                .and(ACCOM_CLASS_ID, accomClassId)
                .and("selfCompetitorId", getSelfCompetitorId())
                .and(ignoreCompetitorIds, StringUtils.join(ignoreCompetitorIdList, ","))
                .and(systemDate, getSystemDate());

        if (isRdlEnabled() || configService.getBooleanParameterValue(INDEPENDENT_PRODUCTS_ENABLED) ) {
            if (isPricingInvestigatorFetchCompetitorPricesFromAllChannelsEnabled) {
                Boolean isInsufficientCompetitiors = isInsufficientCompetitiorsAvailable(accomClassId, LocalDateUtils.toDate(date), 1);
                if (isCompetitiveMarketPositionConstraintApplicable && !isInsufficientCompetitiors)
                    queryParameter.and(CHANNEL_ID, channelId);
                findCompetitorRatesForDateAndChannel = getRateShoppingRateTypeForPricingInvestigatorWithAllChannelAndMinWebrateValue
                        (isCompetitiveMarketPositionConstraintApplicable, isInsufficientCompetitiors);
            } else {
                queryParameter.and(CHANNEL_ID, channelId);
                findCompetitorRatesForDateAndChannel = getRateShoppingRateTypeForPricingInvestigator
                        (isCompetitiveMarketPositionConstraintApplicable);
            }
        } else
            findCompetitorRatesForDateAndChannel = getQueryString(LocalDateUtils.toDate(date), accomClassId, channelId, lengthOfStay,
                    isCompetitiveMarketPositionConstraintApplicable, isPricingInvestigatorFetchCompetitorPricesFromAllChannelsEnabled, queryParameter,
                    EXEC_FIND_COMPETITOR_RATES_FOR_DATE_AND_ALL_CHANNEL_RANKING_ENABLED_FOR_COMPETITOR_CHART, EXEC_FIND_COMPETITOR_RATES_FOR_DATE_AND_ALL_CHANNEL_FOR_COMPETITOR_CHART);

        return getCompetitorsResult(findCompetitorRatesForDateAndChannel, queryParameter);
    }

    private String getQueryString(Date date, Integer accomClassId, Integer channelId, Integer lengthOfStay, boolean isCompetitiveMarketPositionConstraintApplicable, Boolean isPricingInvestigatorFetchCompetitorPricesFromAllChannelsEnabled, QueryParameter queryParameter, String execFindCompetitorRatesForDateAndAllChannelRankingEnabled, String execFindCompetitorRatesForDateAndAllChannel) {
        String findCompetitorRatesForDateAndChannel;
        if (useOptimizedStoredProcedureInvestigator()) {
            queryParameter.and("los", lengthOfStay);
            if (isPricingInvestigatorFetchCompetitorPricesFromAllChannelsEnabled) {
                if (isCompetitiveMarketPositionConstraintApplicable) {
                    if (!isInsufficientCompetitiorsAvailable(accomClassId, date, 1)) {
                        queryParameter.and(CHANNEL_ID, channelId);
                        findCompetitorRatesForDateAndChannel = EXEC_FIND_COMPETITOR_RATES_FOR_DATE_AND_CHANNEL_RANKING_ENABLED;
                    } else {
                        findCompetitorRatesForDateAndChannel = execFindCompetitorRatesForDateAndAllChannelRankingEnabled;
                    }
                } else {
                    findCompetitorRatesForDateAndChannel = execFindCompetitorRatesForDateAndAllChannel;
                }

            } else {
                queryParameter.and(CHANNEL_ID, channelId);
                findCompetitorRatesForDateAndChannel = isCompetitiveMarketPositionConstraintApplicable
                        ? EXEC_FIND_COMPETITOR_RATES_FOR_DATE_AND_CHANNEL_RANKING_ENABLED
                        : EXEC_FIND_COMPETITOR_RATES_FOR_DATE_AND_CHANNEL;
            }
        } else {
            queryParameter.and("los", lengthOfStay);
            queryParameter.and(CHANNEL_ID, channelId);
            StringBuilder builder = new StringBuilder()
                    .append("select t1.*, t2.RoomTypeName from ")
                    .append("( ")
                    .append("   Select ")
                    .append("   webrate.Webrate_Competitors_ID, comp.Webrate_Competitors_Name, Occupancy_DT, MIN(Webrate_RateValue_Display) as Rate ")
                    .append("   from Webrate as webrate ")
                    .append("   inner join ")
                    .append("   Webrate_Accom_Class_Mapping webAccom ")
                    .append("   on webAccom.Webrate_Accom_Type_ID = webrate.Webrate_Accom_Type_ID and webAccom.Accom_Class_ID = :accomClassId ")
                    .append("   inner join ")
                    .append("   Webrate_Competitors as comp ")
                    .append("   on comp.Webrate_Competitors_ID = webrate.Webrate_Competitors_ID ")
                    .append("   inner join ")
                    .append("   Webrate_Competitors_Class as wca ")
                    .append("   on wca.Webrate_Competitors_ID = comp.Webrate_Competitors_ID and wca.Accom_Class_ID = :accomClassId ")
                    .append("   where Occupancy_DT = :date  and Webrate_Channel_ID = :channelId and webrate.Webrate_Status = 'A' ")
                    //Defaulting to BAR for now; this will need to be updated once we start supporting Independent products
                    .append("   and LOS = :los and wca.Demand_Enabled = 1 and wca.Product_ID = 1")
                    .append("   and ((wca.DTA is not null and :date between :systemDate and DATEADD(DAY,wca.DTA,:systemDate)) OR wca.DTA is null)")
                    .append("   and webrate.Webrate_Competitors_ID != :selfCompetitorId")
                    .append("   and webrate.Webrate_Competitors_ID NOT IN (select value from dbo.varcharToInt(:ignoreCompetitorIds, ','))");
            if (isCompetitiveMarketPositionConstraintApplicable) {
                builder.append(" and wca.Ranking_Enabled = 1 ");
            }
            builder.append("   group by webrate.Webrate_Competitors_ID,comp.Webrate_Competitors_Name, Occupancy_DT ")
                    .append(") as t1 ")
                    .append("left join ")
                    .append("( ")
                    .append("   Select ")
                    .append("   webrate.Webrate_Competitors_ID, comp.Webrate_Competitors_Name, Occupancy_DT, MIN(Webrate_RateValue_Display) as Rate, wat.Webrate_Accom_Name as RoomTypeName ")
                    .append("   from Webrate as webrate ")
                    .append("   inner join ")
                    .append("   Webrate_Accom_Class_Mapping webAccom ")
                    .append("   on webAccom.Webrate_Accom_Type_ID = webrate.Webrate_Accom_Type_ID and webAccom.Accom_Class_ID = :accomClassId ")
                    .append("   inner join ")
                    .append("   Webrate_Accom_Type wat ")
                    .append("   on wat.Webrate_Accom_Type_ID = webAccom.Webrate_Accom_Type_ID ")
                    .append("   inner join ")
                    .append("   Webrate_Competitors as comp ")
                    .append("   on comp.Webrate_Competitors_ID = webrate.Webrate_Competitors_ID ")
                    .append("   inner join ")
                    .append("   Webrate_Competitors_Class as wca ")
                    .append("   on wca.Webrate_Competitors_ID = comp.Webrate_Competitors_ID and wca.Accom_Class_ID = :accomClassId ")
                    .append("   where Occupancy_DT = :date and Webrate_Channel_ID = :channelId and webrate.Webrate_Status = 'A' ")
                    //Defaulting to BAR for now; this will need to be updated once we start supporting Independent products
                    .append("   and ((wca.DTA is not null and :date between :systemDate and DATEADD(DAY,wca.DTA,:systemDate)) OR wca.DTA is null)")
                    .append("   and LOS = :los and wca.Demand_Enabled = 1 and wca.Product_ID = 1 ")
                    .append("   and webrate.Webrate_Competitors_ID != :selfCompetitorId")
                    .append("   and webrate.Webrate_Competitors_ID NOT IN (select value from dbo.varcharToInt(:ignoreCompetitorIds, ','))");
            if (isCompetitiveMarketPositionConstraintApplicable) {
                builder.append(" and wca.Ranking_Enabled = 1 ");
            }
            builder.append("   group by webrate.Webrate_Competitors_ID,comp.Webrate_Competitors_Name, Occupancy_DT, wat.Webrate_Accom_Name ")
                    .append(") as t2 ")
                    .append("on t1.Webrate_Competitors_ID = t2.Webrate_Competitors_ID and t1.Rate = t2.Rate ");
            findCompetitorRatesForDateAndChannel = builder.toString();
        }
        return findCompetitorRatesForDateAndChannel;
    }

    private List<CompetitorRateInfo> getCompetitorsResult(String query, QueryParameter queryParameter) {
        return crudService.findByNativeQuery(query,
                queryParameter
                        .parameters(), row1 -> {
                    CompetitorRateInfo competitorRateInfo = new CompetitorRateInfo();
                    competitorRateInfo.setCompetitorName((String) row1[1]);
                    competitorRateInfo.setOccupancyDate((Date) row1[2]);
                    competitorRateInfo.setRate(BigDecimalUtil.zeroIfNull((BigDecimal) row1[3]));
                    competitorRateInfo.setRoomTypeName((String) row1[4]);
                    return competitorRateInfo;
                });
    }

    private Boolean isCompetitiveMarketPositionConstraintApplicable(Integer accomClassId, Date date, Integer productId) {
        return webrateShoppingDataService.competitiveMarketPositionConstraintsConfigured(productId) &&
                (isWebrateRankingOvrEnabled(accomClassId, date) || isDefaultWebrateRankingEnabled(accomClassId, date) ||
                        isDynamicCMPCEnabled(accomClassId, date, productId));
    }

    private boolean isDynamicCMPCEnabled(Integer accomClassId, Date date, Integer productId) {
        List<DCMPCGenericValuesDTO> defaultAndSeasonValues = dynamicCMPCService.fetchDefaultAndSeasonValues(List.of(productId), List.of(accomClassId), convertJavaUtilDateToLocalDate(date));
        return defaultAndSeasonValues != null && !defaultAndSeasonValues.isEmpty();
    }

    private boolean isWebrateRankingOvrEnabled(Integer accomClassId, Date date) {
        WebrateRankingAccomClassOverride wracOvr = crudService.findByNamedQuerySingleResult(
                WebrateRankingAccomClassOverride.BY_ACCOMCLASS_ID_PRODUCT_ID_BETWEEN_DATE,
                QueryParameter.with(ACCOM_ID, accomClassId).and("productId", 1)
                        .and("occupancyDate", date).parameters());
        return isRankingEnabled(getValueForDayOfWeek(wracOvr, date));
    }

    private boolean isDefaultWebrateRankingEnabled(Integer accomClassId, Date date) {
        WebrateRankingAccomClass wrac = crudService.findByNamedQuerySingleResult(
                WebrateRankingAccomClass.BY_ACCOMCLASS_ID_PRODUCT_ID,
                QueryParameter.with(ACCOM_ID, accomClassId).and("productId", 1).parameters());
        return isRankingEnabled(getValueForDayOfWeek(wrac, date));
    }

    private boolean isRankingEnabled(WebrateRanking valueForDayOfWeek) {
        return valueForDayOfWeek != null && valueForDayOfWeek.getId() != 1;
    }

    private Integer getSelfCompetitorId() {
        WebrateCompetitors selfCompetitor = null;
        String webRateHotelId = configService.getParameterValue(IPConfigParamName.BAR_WEB_RATE_ALIAS);
        if (isNotBlank(webRateHotelId)) {
            selfCompetitor = crudService.findByNamedQuerySingleResult(
                    WebrateCompetitors.IDS_PROPERTY_ID_AND_WRHOTEL_ID, QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                            .and("webrateHotelID", webRateHotelId).parameters());
        }
        return null != selfCompetitor ? selfCompetitor.getId() : -1;
    }

    public Integer findChannelIdForTheDay(LocalDate date) {
        String dayOfWeek = date.dayOfWeek().getAsText();
        String findChannelIdForTheDay = new StringBuilder()
                .append("select calDim.calendar_date, calDim.dow_name, ")
                .append("CASE WHEN channelOvr." + dayOfWeek + "_Override_Channel_ID IS NULL ")
                //Defaulting to BAR for now; this will need to be updated once we start supporting Independent products
                .append("     THEN (select " + dayOfWeek + "_Channel_ID from Webrate_Default_Channel WHERE Product_ID = 1) ")
                .append("     ELSE channelOvr." + dayOfWeek + "_Override_Channel_ID  ")
                .append("END as Channel_ID ")
                .append("FROM ")
                .append("calendar_dim as calDim ")
                .append("LEFT JOIN ")
                .append("Webrate_Override_Channel as channelOvr ")
                .append("ON calDim.calendar_date >= Channel_Override_Start_DT AND calDim.calendar_date <= Channel_Override_End_DT AND channelOvr.Product_ID = 1")
                .append("where calDim.calendar_date = :date").toString();
        Object[] row = crudService.findByNativeQuerySingleResult(findChannelIdForTheDay,
                QueryParameter.with(DATE, date).parameters());
        return (Integer) row[2];
    }

    public String getCompetitiveMarketPositionConstraints(LocalDate date, Integer accomClassId) {
        return getStandardCompetitiveMarketPositionConstraints(JavaLocalDateUtils.toJavaLocalDate(date), accomClassId, 1);
    }

    public String getCompetitiveMarketPositionConstraints(java.time.LocalDate date, Integer accomClassId, Integer productId) {
        if(isDCMPCEnabled() && isShowOccupancyBasedSettingsOnPricingGFMInvestigatorEnabled()){
            return getDCMPCBasedCompetitiveMarketPositionConstraint(date, accomClassId, productId);
        }
        return getStandardCompetitiveMarketPositionConstraints(date, accomClassId, productId);
    }

    public String getDCMPCBasedCompetitiveMarketPositionConstraint(java.time.LocalDate date, Integer accomClassId, Integer productId){
        BigDecimal occupancyPercentage = accommodationService.getOccupancyPercentage(date);
        List<DCMPCGenericValuesDTO> dcmpcGenericValuesDTOList = dynamicCMPCService.fetchDefaultAndSeasonValuesFirstHigherToOccupancyPercentage(Arrays.asList(productId), Arrays.asList(accomClassId), date, occupancyPercentage);
        int dow =  JavaLocalDateUtils.getDayOfWeek(JavaLocalDateUtils.toDate(date));

        Optional<DCMPCGenericValuesDTO> dCMPCValue = Optional.empty();
        if(dcmpcGenericValuesDTOList != null){
            dCMPCValue = dcmpcGenericValuesDTOList.stream().filter(dto -> dto.getDowId().equals(Byte.valueOf(dow+""))).findFirst();
        }

        if (occupancyPercentage != null && dCMPCValue.isPresent() &&  dCMPCValue.get().getOnBooksThresholdPercent() != null && occupancyPercentage.compareTo(dCMPCValue.get().getOnBooksThresholdPercent()) <= 0) {
            return dCMPCValue.get().getOnBooksThresholdPercent()+ Constants.REGULATOR_CONTEXT_DELIMITER +dCMPCValue.get().getMaxPercentile();
        }
        return getStandardCompetitiveMarketPositionConstraints(date, accomClassId, productId);
    }

    private String getStandardCompetitiveMarketPositionConstraints(java.time.LocalDate date, Integer accomClassId, Integer productId){
        String dow = date.getDayOfWeek().toString();
        String webrateRankingQuery =
                "declare @ranking_id int = (Select " + dow + "_Webrate_Ranking_ID from Webrate_Ranking_Accom_Class where Accom_Class_ID = :accomClassId and Product_ID = :productId);" +
                        "SELECT ranking.Webrate_Ranking_Name " +
                        "   FROM " +
                        "       (SELECT " +
                        "       isnull(min(" +
                        "           CASE WHEN " + dow + "_Override_Rank_ID IS NULL " +
                        "                   then @ranking_id " +
                        "               ELSE " + dow + "_Override_Rank_ID " +
                        "           END)," +
                        "       @ranking_id) as Ranking_ID " +
                        "       FROM " +
                        "       Webrate_Ranking_AC_OVR as ovr " +
                        "           where :date between Webrate_Rank_Override_Start_DT AND Webrate_Rank_Override_End_DT " +
                        "           AND Accom_Class_ID = :accomClassId and ovr.Product_ID = :productId ) as t " +
                        "       INNER JOIN Webrate_Ranking as ranking ON ranking.Webrate_Ranking_ID = t.Ranking_ID";
        Map<String, Object> parameters = QueryParameter.with(ACCOM_CLASS_ID, accomClassId)
                .and(DATE, date).and(PRODUCT_ID, productId).parameters();
        return crudService.findByNativeQuerySingleResult(webrateRankingQuery, parameters, row -> (String) row[0]);
    }

    private  boolean isDCMPCEnabled(){
        return configService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_OCCUPANCY_BASED_DCMPC_TAB);
    }

    private  boolean isShowOccupancyBasedSettingsOnPricingGFMInvestigatorEnabled(){
        return configService.getBooleanParameterValue(PreProductionConfigParamName.SHOW_OCCUPANCY_BASED_SETTINGS_ON_PRICING_GFM_INVESTIGATOR_SCREEN);
    }

    public String getRateShoppingRateTypeForPricingInvestigator(Boolean rankedEnabled) {
        StringBuilder query = new StringBuilder();
        query.append("   Select a.* from ( ");
        query.append("   Select webrate.Webrate_Competitors_ID,webrate.Webrate_Competitors_Name,Occupancy_DT,MIN(webrate.Original_Webrate_RateValue) as Rate,wat.Webrate_Accom_Name as RoomTypeName ,los ,");
        query.append("   ROW_NUMBER() over (partition by  webrate.Webrate_Competitors_ID,webrate.Webrate_Competitors_Name,  Occupancy_DT, wat.Webrate_Accom_Name order by webrate.LOS) rank");
        if(isRdlEnabled()) {
            query.append("   from Vw_Webrate_v2_full webrate");
        } else {
            query.append("   from Vw_Webrate_full webrate");
        }
        query.append("   LEFT JOIN");
        query.append("   Webrate_Accom_Type wat");
        query.append("   ON wat.Webrate_Accom_Type_ID = webrate.Webrate_Accom_Type_ID");
        query.append("   WHERE");
        query.append("   Occupancy_DT = :date  AND webrate.Webrate_Status = 'A'");
        query.append("   AND webrate.Demand_Enabled = 1");
        query.append("   AND ((webrate.DTA is not null and :date between :systemDate and DATEADD(DAY,webrate.DTA,:systemDate)) OR webrate.DTA is null)");
        query.append("   AND webrate.Product_ID = 1 AND webrate.Webrate_Competitors_ID != :selfCompetitorId");
        query.append("   AND webrate.Webrate_Competitors_ID NOT IN (select value from dbo.varcharToInt(:ignoreCompetitorIds, ','))");
        query.append("   AND webrate.Accom_Class_ID=:accomClassId ");
        query.append("AND Webrate_Channel_ID = :channelId");
        if (rankedEnabled) {
            query.append(" and webrate.Ranking_Enabled = 1");
        }
        query.append("   Group By webrate.Webrate_Competitors_ID, webrate.Webrate_Competitors_Name, Occupancy_DT, wat.Webrate_Accom_Name,webrate.LOS");
        query.append("   ) as a  where a.rank=1");
        return query.toString();
    }

    public String getRateShoppingRateTypeForPricingInvestigatorAllChannel(Boolean rankedEnabled, Boolean isInsufficientCompetitors) {
        StringBuilder query = new StringBuilder();
        query.append("   Select a.* from ( ");
        query.append("   Select webrate.Webrate_Competitors_ID,webrate.Webrate_Competitors_Name,Occupancy_DT,webrate.Original_Webrate_RateValue as Rate,wat.Webrate_Accom_Name as RoomTypeName ,los ,");
        query.append("    MIN(los) over (partition by  webrate.Webrate_Competitors_ID,webrate.Webrate_Competitors_Name,  Occupancy_DT, wat.Webrate_Accom_Name order by webrate.LOS) rank");
        if(isRdlEnabled()) {
            query.append("   from Vw_Webrate_v2_full webrate");
        } else {
            query.append("   from Vw_Webrate_full webrate");
        }
        query.append("   LEFT JOIN");
        query.append("   Webrate_Accom_Type wat");
        query.append("   ON wat.Webrate_Accom_Type_ID = webrate.Webrate_Accom_Type_ID");
        query.append("   WHERE");
        query.append("   Occupancy_DT = :date  AND webrate.Webrate_Status = 'A'");
        query.append("   AND webrate.Demand_Enabled = 1");
        query.append("   AND ((webrate.DTA is not null and :date between :systemDate and DATEADD(DAY,webrate.DTA,:systemDate)) OR webrate.DTA is null)");
        query.append("   AND webrate.Product_ID = 1 AND webrate.Webrate_Competitors_ID != :selfCompetitorId");
        query.append("   AND webrate.Webrate_Competitors_ID NOT IN (select value from dbo.varcharToInt(:ignoreCompetitorIds, ','))");
        query.append("   AND webrate.Accom_Class_ID=:accomClassId ");
        if (rankedEnabled) {
            if (!isInsufficientCompetitors) {
                query.append(" and webrate.Ranking_Enabled = 1 AND Webrate_Channel_ID = :channelId");
            } else {
                query.append(" and webrate.Ranking_Enabled = 1");
            }
        }
        query.append("   ) as a  where a.los=rank");
        return query.toString();
    }

    public String getRateShoppingRateTypeForPricingInvestigatorWithAllChannelAndMinWebrateValue(Boolean rankedEnabled, Boolean isInsufficientCompetitors) {
        StringBuilder query = new StringBuilder();
        query.append("   Select a.* from ( ");
        query.append("   Select webrate.Webrate_Competitors_ID,webrate.Webrate_Competitors_Name,Occupancy_DT,MIN(webrate.Original_Webrate_RateValue) as Rate,wat.Webrate_Accom_Name as RoomTypeName ,los ,");
        query.append("   ROW_NUMBER() over (partition by  webrate.Webrate_Competitors_ID,webrate.Webrate_Competitors_Name,  Occupancy_DT, wat.Webrate_Accom_Name order by webrate.LOS) rank");
        if(isRdlEnabled()) {
            query.append("   from Vw_Webrate_v2_full webrate");
        } else {
            query.append("   from Vw_Webrate_full webrate");
        }
        query.append("   LEFT JOIN");
        query.append("   Webrate_Accom_Type wat");
        query.append("   ON wat.Webrate_Accom_Type_ID = webrate.Webrate_Accom_Type_ID");
        query.append("   WHERE");
        query.append("   Occupancy_DT = :date  AND webrate.Webrate_Status = 'A'");
        query.append("   AND webrate.Demand_Enabled = 1");
        query.append("   AND ((webrate.DTA is not null and :date between :systemDate and DATEADD(DAY,webrate.DTA,:systemDate)) OR webrate.DTA is null)");
        query.append("   AND webrate.Product_ID = 1 AND webrate.Webrate_Competitors_ID != :selfCompetitorId");
        query.append("   AND webrate.Webrate_Competitors_ID NOT IN (select value from dbo.varcharToInt(:ignoreCompetitorIds, ','))");
        query.append("   AND webrate.Accom_Class_ID=:accomClassId ");
        if (rankedEnabled) {
            if (!isInsufficientCompetitors) {
                query.append(" and webrate.Ranking_Enabled = 1 AND Webrate_Channel_ID = :channelId");
            } else {
                query.append(" and webrate.Ranking_Enabled = 1");
            }
        }
        query.append("   Group By webrate.Webrate_Competitors_ID, webrate.Webrate_Competitors_Name, Occupancy_DT, wat.Webrate_Accom_Name,webrate.LOS");
        query.append("   ) as a  where a.rank=1");
        return query.toString();
    }

    public List<Integer> getIgnoreCompetitorData(java.time.LocalDate date, Integer accomClassId) {
        String dow = date.getDayOfWeek().toString();
        //Defaulting to BAR for now; this will need to be updated once we start supporting Independent products
        StringBuilder ignoreCompetitorDataQuery = new StringBuilder();
        ignoreCompetitorDataQuery.append("select wcc.Webrate_Competitors_ID\n");
        ignoreCompetitorDataQuery.append("from Webrate_OVR_Competitor_DTLS wocd \n");
        ignoreCompetitorDataQuery.append("join Webrate_Override_Competitor woc on wocd.Webrate_Override_Competitor_ID=woc.Webrate_Override_Competitor_ID\n");
        ignoreCompetitorDataQuery.append("join Webrate_Competitors_Class wcc on wocd.Webrate_Competitors_Class_ID=wcc.Webrate_Competitors_Class_ID\n");
        ignoreCompetitorDataQuery.append("where \n");
        ignoreCompetitorDataQuery.append(":date between woc.Competitor_Override_Start_DT and woc.Competitor_Override_End_DT\n");
        ignoreCompetitorDataQuery.append("and wcc.Accom_Class_ID=:accomClassId\n");
        ignoreCompetitorDataQuery.append("and woc." + dow + "_Override_Competitor=1");
        Map<String, Object> parameters = QueryParameter.with(ACCOM_CLASS_ID, accomClassId)
                .and(DATE, date).parameters();
        return crudService.findByNativeQuery(ignoreCompetitorDataQuery.toString(), parameters);
    }

    public Boolean isRdlEnabled() {
        return configService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_RDL_ENABLED);
    }

    public Boolean isInsufficientCompetitiorsAvailable(Integer accomClassId, Date occupancyDate, Integer productId) {
        return decisionSupportService.getDecisionSupportForAnyProduct(accomClassId, occupancyDate, 1, productId) != null ? true : false;
    }

    public Boolean isPricingInvestigatorFetchCompetitorPricesFromAllChannelsEnabled() {
        return configService.getBooleanParameterValue(PreProductionConfigParamName.PRICING_INVESTIGATOR_FETCH_COMPETITOR_PRICES_FROM_ALL_CHANNELS);
    }

    public boolean checkIfSelectedDateWithinDTARange(Integer daysToArrival, Date selectedDate) {
        final java.time.LocalDate systemDate = getSystemDate();
        return daysToArrival == null || !convertJavaUtilDateToLocalDate(selectedDate).isAfter(systemDate.plusDays(daysToArrival - 1));
    }

    public boolean checkIfSelectedDateWithinDTARangeWithSavedSystemDate(Integer daysToArrival, Date selectedDate, java.time.LocalDate sysDate) {
        return daysToArrival == null || !convertJavaUtilDateToLocalDate(selectedDate).isAfter(sysDate.plusDays(daysToArrival - 1));
    }

    protected java.time.LocalDate getSystemDate() {
        return JavaLocalDateUtils.fromDate(dateService.getCaughtUpDate());
    }

    public List<CompetitorRateInfo> getCompetitorRates(Date fromDate, Date toDate, List<Integer> channelIds, List<Integer> competitorIds, List<Integer> accomTypeIds, List<Integer> accomClassIds, boolean isCompetitiveMarketPositioningEnabled) {
        boolean displayNameEnabled = configService.getBooleanParameterValue(GUIConfigParamName.DISPLAY_NAME_DISPLAY_NAME_ENABLED);
        List<CompetitorRateInfo> finalResult = new ArrayList<>();
        boolean isPricingScreenOptimizationEnabled = isPricingScreenOptimizationEnabled();
        WebRateCompetitorMappingsDto webRateCompetitorMappingsDto = getWebRateCompetitorMappingsDto(accomClassIds, isPricingScreenOptimizationEnabled);
        WebrateRanking defaultWebrateRanking = crudService.findByNamedQuerySingleResult(WebrateRanking.FIND_BY_NAME, QueryParameter.with("webrateRankingName", "None").parameters());
        List<WebrateRankingAccomClass> webrateRankingAccomClasses = crudService.findAll(WebrateRankingAccomClass.class);
        List<WebrateRankingAccomClassOverride> webrateRankingAccomClassOverrides = getWebrateRankingAccomClassOverrides(fromDate, toDate);
        Map<DayOfWeek, List<String>> dayOfWeekWiseIgnoredChannels = webrateChannelIgnoreService.getDayOfWeekWiseIgnoredChannels();
        List<WebrateCompChannelMapping> webrateCompChannelMappings = webrateShoppingDataService.getWebrateCompChannelMappings();

        for (Integer channelIdReq : channelIds) {

            String queryWebrate = "select Webrate_ID,Webrate_Source_Property_ID,Webrate_Competitors_ID,Webrate_Channel_ID,Webrate_Accom_Type_ID," +
                    "Webrate_Type_ID,LOS,webRate_remark,Webrate_Status,Webrate_Currency,Webrate_RateValue , webrate_generationdate, Webrate_Occupancy_Date from(\n" +
                    " SELECT Isnull(wb.webrate_id, 0)                 AS Webrate_ID, \n" +
                    "   Isnull(wb.webrate_source_property_id, 0) AS Webrate_Source_Property_ID, \n" +
                    "   x.webrate_competitors_id, \n" +
                    "   Isnull(wb.webrate_channel_id, 0)         AS Webrate_Channel_ID, \n" +
                    "   Isnull(wb.webrate_accom_type_id, 0)      AS Webrate_Accom_Type_ID, \n" +
                    "   Isnull(wb.webrate_type_id, 0)            AS Webrate_Type_ID, \n" +
                    "   Isnull(wb.los, 1)                        AS LOS, \n" +
                    "   Isnull(wb.webrate_remark, 'NA')          AS Webrate_Remark, \n" +
                    "   Isnull(wb.webrate_status, 'NA')          AS Webrate_Status, \n" +
                    "   Isnull(wb.webrate_currency, 'NA')        AS Webrate_Currency, \n" +
                    "   Isnull(wb.webrate_ratevalue_display, 0)  AS Webrate_RateValue, \n" +
                    "   wb.webrate_generationdate , ROW_NUMBER() over (Partition by x.webrate_competitors_id, Webrate_Accom_Type_ID, LOS order by x.webrate_competitors_id, Webrate_Accom_Type_ID, LOS desc,webRate_generationDate desc) as rowNUm,\n" +
                    "   wb.occupancy_dt                          AS Webrate_Occupancy_Date \n" +
                    "FROM   webrate AS wb \n" +
                    "   RIGHT JOIN (SELECT * \n" +
                    "   FROM   webrate_competitors wc \n" +
                    "   WHERE  webrate_competitors_id IN ( :competitorIds )) AS x \n" +
                    "   ON x.webrate_competitors_id = wb.webrate_competitors_id \n" +
                    "  AND wb.occupancy_dt >= :startDate \n" +
                    "  AND wb.occupancy_dt <= :endDate \n" +
                    "  AND wb.webrate_channel_id IN( :channelIds ) \n" +
                    "  AND wb.webrate_accom_type_id IN( :accomTypeIds ) \n" +
                    ") AS finalData\n" +
                    "WHERE rowNUm = 1";

            Query q = crudService.getEntityManager().createNativeQuery(queryWebrate);
            q.setParameter(START_DATE, new SimpleDateFormat(DateUtil.DEFAULT_DATE_FORMAT).format(fromDate));
            q.setParameter(END_DATE, new SimpleDateFormat(DateUtil.DEFAULT_DATE_FORMAT).format(toDate));
            q.setParameter(CHANNEL_IDS, channelIdReq);
            q.setParameter(COMPETITOR_IDS, competitorIds);
            q.setParameter(ACCOM_TYPE_IDS, accomTypeIds);
            List<Object[]> resultList = q.getResultList();

            WebrateChannel objWebrateChannel = crudService.find(WebrateChannel.class, channelIdReq);

            Object[] currentRow;
            List<CompetitorRateInfo> result = new ArrayList<>();
            for (int i = 0; i < resultList.size(); i++) {

                currentRow = resultList.get(i);

                Integer webRateId = (Integer) currentRow[0];
                Integer competitorId = (Integer) currentRow[2];
                Integer accomTypeId = (Integer) currentRow[4];
                Integer los = ((BigDecimal) currentRow[6]).toBigInteger().intValue();
                String webrateRemark = (String) currentRow[7];
                String webrateStatus = (String) currentRow[8];
                BigDecimal webrateRatevalue = (BigDecimal) currentRow[10];
                Date webrateOccupancyDate = (Date) currentRow[12];

                Date webrateGenerationDate = (null != currentRow[10]) ? ((Date) currentRow[11]) : null;

                if (i == 0 || checkIfCreateNewCompetitorRateInfo(currentRow, resultList, i)) {
                    List<WebrateAccomClassMapping> objMapping = getWebrateAccomClassMappingsForAccomTypeId(accomClassIds, accomTypeId, webRateCompetitorMappingsDto);

                    Set<AccomClass> accomClasses = null;

                    if (isNotEmpty(objMapping)) {
                        accomClasses = objMapping.stream().map(WebrateAccomClassMapping::getAccomClass).collect(Collectors.toSet());
                    }

                    if (accomClasses != null) {
                        for (AccomClass accomClass : accomClasses) {
                            CompetitorRateInfo dto = new CompetitorRateInfo();
                            dto.setWebRateId(webRateId);
                            dto.setOccupancyDate(webrateOccupancyDate);

                            setDtoChannelName(objWebrateChannel, displayNameEnabled, dto);
                            setDtoCompetitorNameAndAccomClassName(competitorId, accomClass, dto, webRateCompetitorMappingsDto);
                            setDtoRoomTypeName(displayNameEnabled, accomTypeId, dto, webRateCompetitorMappingsDto);
                            setWebRateValueOrRemarkInLOS(los, webrateRemark, webrateStatus, webrateRatevalue, dto);

                            dto.setRate(webrateRatevalue);

                            if (accomClass != null) {
                                WebrateCompetitorsAccomClass mapping = getWebrateCompetitorsAccomClass(accomClass, competitorId, webRateCompetitorMappingsDto);

                                if (mapping != null) {
                                    if (isCompetitiveMarketPositioningEnabled) {
                                        setDtoForIncludedInMarketConstraints(defaultWebrateRanking, webrateRankingAccomClasses, webrateRankingAccomClassOverrides,
                                                webrateOccupancyDate, accomClass, dto, mapping);
                                        setDtoForIncludedInDemand(webrateOccupancyDate, dto, mapping, dayOfWeekWiseIgnoredChannels, webrateCompChannelMappings, webRateCompetitorMappingsDto);
                                    } else {
                                        setDtoForIncludedInDemandAndIncludedInMarketConstraints(defaultWebrateRanking, webrateRankingAccomClasses, webrateRankingAccomClassOverrides,
                                                webrateOccupancyDate, accomClass, dto, mapping, dayOfWeekWiseIgnoredChannels, webrateCompChannelMappings , webRateCompetitorMappingsDto);
                                    }
                                }
                            }
                            setWebRateGenerationDate(webrateGenerationDate, dto);
                            result.add(dto);
                        }
                    }
                } else {
                    // Add info to previous DTO
                    CompetitorRateInfo dto = result.get(result.size() - 1);
                    setDtoForRateByLengthOfStay(los, webrateStatus, webrateRatevalue, dto);
                    setMaxWebrateGenerationDate(dto, webrateGenerationDate);
                }
            }

            finalResult.addAll(result);
        }
        return finalResult;
    }

    private WebRateCompetitorMappingsDto getWebRateCompetitorMappingsDto(List<Integer> accomClassIds, boolean isPricingScreenOptimizationEnabled) {
        if(isPricingScreenOptimizationEnabled) {
            return initializeWebRateCompetitorsDataMappingsDto(accomClassIds, isPricingScreenOptimizationEnabled);
        }
        return new WebRateCompetitorMappingsDto();
    }

    private WebRateCompetitorMappingsDto initializeWebRateCompetitorsDataMappingsDto(List<Integer> accomClassIds, boolean isPricingScreenOptimizationEnabled) {
        Map<Integer, List<WebrateAccomClassMapping>> webrateAccomClassMappings = getWebrateAccomClassMap(accomClassIds);
        Map<String, WebrateCompetitorsAccomClass> webrateCompetitorsAccomMapping = getWebrateCompetitorsAccomClassMap(accomClassIds);
        Map<Integer, WebrateCompetitors> webrateCompetitorsIdMap = getWebrateCompetitorsMap();
        Map<Integer, WebrateAccomType> webrateAccomTypeIdMap = getWebrateAccomTypeMap();
        Map<Integer, List<WebrateOverrideCompetitorDetails>> webrateOvrdCompDtls = getWebrateOverrideCompetitorDetails();
        java.time.LocalDate sysDate = getSystemDate();
        return new WebRateCompetitorMappingsDto(isPricingScreenOptimizationEnabled, webrateAccomClassMappings, webrateCompetitorsAccomMapping, webrateCompetitorsIdMap, webrateAccomTypeIdMap, webrateOvrdCompDtls, sysDate);
    }

    private Map<String, WebrateCompetitorsAccomClass> getWebrateCompetitorsAccomClassMap(List<Integer> accomClassIds) {
        List<WebrateCompetitorsAccomClass> webrateCompetitorsAccomList = crudService.findByNamedQuery(WebrateCompetitorsAccomClass.BY_ACCOM_IDS_IN,
                QueryParameter.with("accomClassIds", accomClassIds).parameters());
        Map<String, WebrateCompetitorsAccomClass> webrateCompetitorsAccomMapping = webrateCompetitorsAccomList.stream()
                .collect(Collectors.toMap(mapping -> mapping.getWebrateCompetitor().getId() + KEY_SEPARATOR + mapping.getAccomClass().getId(), mapping -> mapping));
        return webrateCompetitorsAccomMapping;
    }

    private Map<Integer, List<WebrateAccomClassMapping>> getWebrateAccomClassMap(List<Integer> accomClassIds) {
        List<WebrateAccomClassMapping> webrateAccomClassMappingsList = crudService.findByNamedQuery(WebrateAccomClassMapping.BY_ACCOM_CLASS_IDS_IN,
                QueryParameter.with("accomClassIds", accomClassIds).parameters());
        Map<Integer, List<WebrateAccomClassMapping>> webrateAccomClassMappings = webrateAccomClassMappingsList.stream()
                .collect(Collectors.groupingBy(mapping -> mapping.getWebrateAccomType().getId()));
        return webrateAccomClassMappings;
    }

    private boolean isPricingScreenOptimizationEnabled(){
        return configService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRICING_SCREEN_OPTIMIZATION);
    }

    private WebrateCompetitorsAccomClass getWebrateCompetitorsAccomClass(AccomClass accomClass, Integer competitorId, WebRateCompetitorMappingsDto webRateCompetitorMappingsDto) {
        if(webRateCompetitorMappingsDto.isPricingScreenOptimizationEnabled()){
            return webRateCompetitorMappingsDto.getWebrateCompetitorsAccomMapping().get(competitorId + KEY_SEPARATOR + accomClass.getId());
        } else {
            return crudService.findByNamedQuerySingleResult(WebrateCompetitorsAccomClass.BY_ACCOM_ID_AND_COMP_ID,
                    QueryParameter.with(ACCOM_ID, accomClass.getId()).and("compId", competitorId).parameters());
        }
    }

    private List<WebrateAccomClassMapping> getWebrateAccomClassMappingsForAccomTypeId(List<Integer> accomClassIds, Integer accomTypeId, WebRateCompetitorMappingsDto webRateCompetitorMappingsDto) {
        if(webRateCompetitorMappingsDto.isPricingScreenOptimizationEnabled()){
            return webRateCompetitorMappingsDto.getWebrateAccomClassMappings().get(accomTypeId);
        } else {
            return crudService.findByNamedQuery(WebrateAccomClassMapping.BY_ACCOM_CLASS_IDS_ACCOM_TYPE,
                    QueryParameter.with("accomTypeId", accomTypeId).and("accomClassIds", accomClassIds).parameters());
        }
    }

    private Map<Integer, List<WebrateOverrideCompetitorDetails>> getWebrateOverrideCompetitorDetails() {
        List<WebrateOverrideCompetitorDetails> webrateOverrideCompetitorDetails = crudService.findAll(WebrateOverrideCompetitorDetails.class);
        return webrateOverrideCompetitorDetails.stream().collect(Collectors.groupingBy(wocd -> wocd.getWebrateCompetitorsAccomClass().getId()));
    }

    private void setDtoForIncludedInDemandAndIncludedInMarketConstraints(WebrateRanking defaultWebrateRanking, List<WebrateRankingAccomClass> webrateRankingAccomClasses,
                                                                         List<WebrateRankingAccomClassOverride> webrateRankingAccomClassOverrides, Date webrateOccupancyDate, AccomClass accomClass, CompetitorRateInfo dto, WebrateCompetitorsAccomClass mapping, Map<DayOfWeek, List<String>> dayOfWeekWiseIgnoredChannels, List<WebrateCompChannelMapping> webrateCompChannelMappings, WebRateCompetitorMappingsDto webRateCompetitorMappingsDto) {
        if (isDemandDisabledFor(mapping)) {
            dto.setIncludedInDemand(false);
            dto.setIncludedInMarketConstraints(defaultWebrateRanking);
        } else {
            WebrateOverrideCompetitor webrateOverrideCompetitor = getWebrateOverrideCompetitorForOptimisedPricingScreen(webrateOccupancyDate, mapping, webRateCompetitorMappingsDto);
            if(webRateCompetitorMappingsDto.isPricingScreenOptimizationEnabled()) {
                dto.setIncludedInDemand(checkIfSelectedDateWithinDTARangeWithSavedSystemDate(mapping.getDaysToArrival(), webrateOccupancyDate, webRateCompetitorMappingsDto.getSysDate()) &&
                        shouldIncludeInDemand(dto, webrateOccupancyDate, webrateOverrideCompetitor, dayOfWeekWiseIgnoredChannels, webrateCompChannelMappings));
            } else {
                dto.setIncludedInDemand(checkIfSelectedDateWithinDTARange(mapping.getDaysToArrival(), webrateOccupancyDate) &&
                        shouldIncludeInDemand(dto, webrateOccupancyDate, webrateOverrideCompetitor, dayOfWeekWiseIgnoredChannels, webrateCompChannelMappings));
            }

            WebrateRanking webrateRanking = getWebrateRanking(webrateRankingAccomClasses, webrateRankingAccomClassOverrides, webrateOccupancyDate, mapping.getProductID(), accomClass);
            dto.setIncludedInMarketConstraints(webrateRanking != null ? webrateRanking : defaultWebrateRanking);
        }
    }

    private void setDtoForIncludedInDemand(Date webrateOccupancyDate, CompetitorRateInfo dto, WebrateCompetitorsAccomClass mapping, Map<DayOfWeek, List<String>> dayOfWeekWiseIgnoredChannels, List<WebrateCompChannelMapping> webrateCompChannelMappings, WebRateCompetitorMappingsDto webRateCompetitorMappingsDto) {
        if (isDemandDisabledFor(mapping)) {
            dto.setIncludedInDemand(false);
        } else {
            WebrateOverrideCompetitor webrateOverrideCompetitor = getWebrateOverrideCompetitorForOptimisedPricingScreen(webrateOccupancyDate, mapping, webRateCompetitorMappingsDto);
            if(webRateCompetitorMappingsDto.isPricingScreenOptimizationEnabled()) {
                dto.setIncludedInDemand(checkIfSelectedDateWithinDTARangeWithSavedSystemDate(mapping.getDaysToArrival(), webrateOccupancyDate, webRateCompetitorMappingsDto.getSysDate()) &&
                        shouldIncludeInDemand(dto, webrateOccupancyDate, webrateOverrideCompetitor, dayOfWeekWiseIgnoredChannels, webrateCompChannelMappings));
            } else {
                dto.setIncludedInDemand(checkIfSelectedDateWithinDTARange(mapping.getDaysToArrival(), webrateOccupancyDate) &&
                        shouldIncludeInDemand(dto, webrateOccupancyDate, webrateOverrideCompetitor, dayOfWeekWiseIgnoredChannels, webrateCompChannelMappings));
            }
        }
    }

    private void setDtoForIncludedInMarketConstraints(WebrateRanking defaultWebrateRanking, List<WebrateRankingAccomClass> webrateRankingAccomClasses, List<WebrateRankingAccomClassOverride> webrateRankingAccomClassOverrides, Date webrateOccupancyDate, AccomClass accomClass, CompetitorRateInfo dto, WebrateCompetitorsAccomClass mapping) {
        if (isRankDisabledFor(mapping)) {
            dto.setIncludedInMarketConstraints(defaultWebrateRanking);
            setRankingEnabledForDCMPC(dto, false);
        } else {
            WebrateRanking webrateRanking = getWebrateRanking(webrateRankingAccomClasses, webrateRankingAccomClassOverrides, webrateOccupancyDate, mapping.getProductID(), accomClass);
            dto.setIncludedInMarketConstraints(webrateRanking != null ? webrateRanking : defaultWebrateRanking);
            setRankingEnabledForDCMPC(dto, true);
        }
    }

    private void setDtoCompetitorNameAndAccomClassName(Integer competitorId, AccomClass accomClass, CompetitorRateInfo dto, WebRateCompetitorMappingsDto webRateCompetitorMappingsDto) {
        WebrateCompetitors objWebrateComp;
        if(webRateCompetitorMappingsDto.isPricingScreenOptimizationEnabled()) {
            objWebrateComp = webRateCompetitorMappingsDto.getWebrateCompetitorsIdMap().get(competitorId);
        } else {
            objWebrateComp = crudService.find(WebrateCompetitors.class, competitorId);
        }
        if (null != objWebrateComp) {
            dto.setCompetitorName(objWebrateComp.getWebrateCompetitorsAlias());
        }
        setAccomClassName(accomClass, dto);
    }

    private static void setDtoForRateByLengthOfStay(Integer los, String webrateStatus, BigDecimal webrateRatevalue, CompetitorRateInfo dto) {
        if (webrateStatus.equalsIgnoreCase(WEBRATE_ACTIVE_STATUS_CODE)) {
            dto.getRateByLengthOfStay().put(los, webrateRatevalue.setScale(2, RoundingMode.HALF_UP).toPlainString());
        } else if (webrateStatus.equalsIgnoreCase(WEBRATE_CLOSED_STATUS_CODE)) {
            dto.getRateByLengthOfStay().put(los, WEBRATE_CLOSED_STATUS);
        } else if (webrateStatus.equalsIgnoreCase(WEBRATE_FENCED_STATUS_CODE)) {
            dto.getRateByLengthOfStay().put(los, WEBRATE_RATE_RESTRICTED_STATUS);
        } else {
            dto.getRateByLengthOfStay().put(los, webrateStatus.toUpperCase());
        }
    }

    private void setDtoRoomTypeName(boolean displayNameEnabled, Integer accomTypeId, CompetitorRateInfo dto, WebRateCompetitorMappingsDto webRateCompetitorMappingsDto) {
        WebrateAccomType objWebrateAccomType;
        if(webRateCompetitorMappingsDto.isPricingScreenOptimizationEnabled()) {
            objWebrateAccomType = webRateCompetitorMappingsDto.getWebrateAccomTypeIdMap().get(accomTypeId);
        } else {
            objWebrateAccomType = crudService.find(WebrateAccomType.class, accomTypeId);
        }
        if (null != objWebrateAccomType) {
            if (displayNameEnabled) {
                dto.setRoomTypeName(objWebrateAccomType.getWebrateAccomAlias());
            } else {
                dto.setRoomTypeName(objWebrateAccomType.getWebrateAccomName());
            }
        }
    }

    private static void setDtoChannelName(WebrateChannel objWebrateChannel, boolean displayNameEnabled, CompetitorRateInfo dto) {
        if (null != objWebrateChannel) {
            if (displayNameEnabled) {
                dto.setChannelName(objWebrateChannel.getWebrateChannelAlias());
            } else {
                dto.setChannelName(objWebrateChannel.getWebrateChannelName());
            }
        }
    }

    public List<CompetitorRateInfo> getCompetitorRatesView(Date fromDate, Date toDate, List<Integer> channelIds, List<Integer> competitorIds,
                                                           List<Integer> accomTypeIds, List<Integer> accomClassIds, boolean isCompetitiveMarketPositioningEnabled,
                                                           List<Integer> productIds) {
        Query q;
        boolean displayNameEnabled = configService.getBooleanParameterValue(GUIConfigParamName.DISPLAY_NAME_DISPLAY_NAME_ENABLED);
        boolean isRDLToggleEnabled = isRdlEnabled();
        q = getQueryBasedOnRDLToggle(isRDLToggleEnabled);

        q.setParameter(START_DATE, new SimpleDateFormat(DateUtil.DEFAULT_DATE_FORMAT).format(fromDate));
        q.setParameter(END_DATE, new SimpleDateFormat(DateUtil.DEFAULT_DATE_FORMAT).format(toDate));
        q.setParameter(CHANNEL_IDS, channelIds);
        q.setParameter(COMPETITOR_IDS, competitorIds);
        q.setParameter(ACCOM_TYPE_IDS, accomTypeIds);
        q.setParameter(PRODUCT_IDS, productIds);
        List<Object[]> resultList = q.getResultList();

        Object[] currentRow;
        List<CompetitorRateInfo> result = new ArrayList<>();
        Map<Integer, WebrateChannel> webRateChannelMap = new HashMap<>();
        Map<Integer, WebrateCompetitors> webrateCompetitorsMap = new HashMap<>();
        Map<Integer, WebrateAccomType> webRateAccomTypeMap = new HashMap<>();
        Map<String, WebrateCompetitorsAccomClass> webRateCompetitorsAccomClassMap = new HashMap<>();
        List<WebrateRankingAccomClass> webrateRankingAccomClasses = new ArrayList<>();
        List<WebrateRankingAccomClassOverride> webrateRankingAccomClassOverrides = new ArrayList<>();
        Map<Integer, Product> productMap = new HashMap<>();
        WebrateRanking defaultWebrateRanking = null;
        if (CollectionUtils.isNotEmpty(resultList)) {
            defaultWebrateRanking = crudService.findByNamedQuerySingleResult(WebrateRanking.FIND_BY_NAME, QueryParameter.with("webrateRankingName", "None").parameters());
            webrateRankingAccomClasses = crudService.findAll(WebrateRankingAccomClass.class);
            webrateRankingAccomClassOverrides = getWebrateRankingAccomClassOverrides(fromDate, toDate);

            webRateChannelMap = getWebrateChannelMap();

            webrateCompetitorsMap = getWebrateCompetitorsMap();

            webRateAccomTypeMap = getWebrateAccomTypeMap();

            webRateCompetitorsAccomClassMap = getWebrateCompetitorsAccomClassMap();

            productMap = getProductMap();
        }

        for (Object[] currentRowResult : resultList) {

            currentRow = currentRowResult;

            Integer webRateId = (Integer) currentRow[0];
            Integer competitorId = (Integer) currentRow[2];
            Integer channelId = (Integer) currentRow[3];
            Integer accomTypeId = (Integer) currentRow[4];
            Integer los = ((BigDecimal) currentRow[5]).toBigInteger().intValue();
            String webrateRemark = (String) currentRow[6];
            String webrateStatus = (String) currentRow[7];
            BigDecimal webrateRatevalue = (BigDecimal) currentRow[9];
            Date webrateOccupancyDate = (Date) currentRow[11];
            Date webrateGenerationDate = (null != currentRow[9]) ? ((Date) currentRow[10]) : null;
            Integer productId = ((BigInteger) currentRow[12]).intValue();

            List<WebrateAccomClassMapping> objMapping = crudService.findByNamedQuery(WebrateAccomClassMapping.BY_ACCOM_CLASS_IDS_ACCOM_TYPE,
                    QueryParameter.with("accomTypeId", accomTypeId).and("accomClassIds", accomClassIds).parameters());

            Set<AccomClass> accomClasses = null;

            if (isNotEmpty(objMapping)) {
                accomClasses = objMapping.stream().map(WebrateAccomClassMapping::getAccomClass).collect(Collectors.toSet());
            }

            if (accomClasses != null) {
                for (AccomClass accomClass : accomClasses) {
                    CompetitorRateInfo dto = new CompetitorRateInfo();
                    dto.setWebRateId(webRateId);
                    dto.setOccupancyDate(webrateOccupancyDate);

                    setWebRateChannel(displayNameEnabled, webRateChannelMap, channelId, dto);
                    setCompetitorName(webrateCompetitorsMap, competitorId, dto);
                    setAccomClassName(accomClass, dto);

                    setWebRateAccomType(displayNameEnabled, webRateAccomTypeMap, accomTypeId, dto);
                    setWebRateValueOrRemarkInLOS(los, webrateRemark, webrateStatus, webrateRatevalue, dto);
                    setDtoWebrateVale(los, webrateRatevalue, dto);

                    setMarketConstraintAndIncludedInDemand(isCompetitiveMarketPositioningEnabled, webRateCompetitorsAccomClassMap, webrateRankingAccomClasses, webrateRankingAccomClassOverrides, defaultWebrateRanking, competitorId, webrateOccupancyDate, productId, accomClass, dto);
                    setWebRateGenerationDate(webrateGenerationDate, dto);
                    dto.setProductName(productMap.get(productId).getName());
                    setDtoWebrateTypeName(isRDLToggleEnabled, currentRow, dto);

                    result.add(dto);
                }

            }
        }
        return result;
    }

    private void setRankingEnabledForDCMPC(CompetitorRateInfo dto, boolean value){
        dto.setRankingEnabledForDCMPC(value);
    }

    private static void setDtoWebrateTypeName(boolean isRDLToggleEnabled, Object[] currentRow, CompetitorRateInfo dto) {
        if (isRDLToggleEnabled) {
            dto.setWebrateTypeName((String) currentRow[13]);
        }
    }

    private static void setDtoWebrateVale(Integer los, BigDecimal webrateRatevalue, CompetitorRateInfo dto) {
        if (los == 1) {
            dto.setRate(webrateRatevalue);
        }
    }

    private Query getQueryBasedOnRDLToggle(boolean isRDLToggleEnabled) {
        Query q;
        if (isRDLToggleEnabled) {
            q = crudService.getEntityManager().createNativeQuery(QUERY_FOR_RDL_PROPERTIES);
        } else {
            q = crudService.getEntityManager().createNativeQuery(query);
        }
        return q;
    }

    @VisibleForTesting
    protected void setDynamicCMPCService(DynamicCMPCService dynamicCMPCService) {
        this.dynamicCMPCService = dynamicCMPCService;
    }
}
