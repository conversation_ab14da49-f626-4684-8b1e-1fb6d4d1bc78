package com.ideas.tetris.pacman.services.monitoring.clientProcessing.dto;

import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.DailyProcessingStatus;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.DecisionDelivery;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.ExtractStatus;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.InputProcessing;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.InputProcessingJob;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.InputProcessingStatus;
import com.ideas.tetris.pacman.services.monitoring.dailyProcessing.entity.PropertyDailyProcessing;
import com.ideas.tetris.pacman.services.problem.JobCrudServiceBean;
import com.ideas.tetris.pacman.services.problem.entity.Problem;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.platform.common.Justification;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.services.globalproperty.service.ClientPropertyCacheService;
import org.apache.commons.lang.RandomStringUtils;

import javax.inject.Inject;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static com.ideas.tetris.pacman.common.constants.Constants.BDE;
import static com.ideas.tetris.pacman.common.constants.Constants.CDP;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@SuppressWarnings("all")
@Justification("This class will be used for testing purpose only")
@Component
@Transactional
public class ClientDashboardDummyDataPopulator {

    public static final String COMPLETED = "COMPLETED";
    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	private CrudService globalCrudService;
    @JobCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("jobCrudServiceBean")
	private CrudService jobCrudService;
    @Autowired
    AbstractMultiPropertyCrudService multiPropertyCrudService;
    @Autowired
	private ClientPropertyCacheService clientPropertyCacheService;
    public static final int PDP_FOR_TARS_DECISION_DELIVERY_FAILED_INDEX = 7;
    public static final int PDP_FOR_HTNG_DECISION_DELIVERY_FAILED_INDEX = 8;
    public static final int PDP_FOR_PROCESSING_FAILED_INDEX = 9;
    private static final int milliToNanoConst = 1000000;

    @ForTesting


    public void doRun(String clientCode, String propertyCodes) {
        List<String> propertyList = Arrays.asList(propertyCodes.split("\\s*,\\s*"));
        List<PropertyDailyProcessing> dailyProcessings = new ArrayList<>();
        LocalDateTime processingDate = LocalDateTime.now();

        for (int i = 0; i < 30; i++) {
            if (null != propertyList.get(0)) {
                dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate, propertyList.get(0), "BDE_NOT_RECEIVED", "NOT_RECEIVED", ExtractStatus.INCOMPLETE, 3, 3, -1, null, -1, "TwoWay", BDE));
            }
            if (null != propertyList.get(1)) {
                dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate, propertyList.get(1), "BDE_IN_PROGRESS", "IN_PROGRESS", ExtractStatus.COMPLETE, 3, 3, 7, null, -1, "TwoWay", BDE));
            }
            if (null != propertyList.get(2)) {
                dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate, propertyList.get(2), "BDE_DECISIONS_IN_PROGRESS", "IN_PROGRESS", ExtractStatus.COMPLETE, 2, 2, 9, null, -1, "OneWay", BDE));
            }
            if (null != propertyList.get(3)) {
                dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate, propertyList.get(3), "BDE_COMPLETED", COMPLETED, ExtractStatus.COMPLETE, 3, 3, 3, COMPLETED, 7, "OneWay", BDE));
            }
            if (null != propertyList.get(4)) {
                dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate, propertyList.get(4), "BDE_COMPLETED", COMPLETED, ExtractStatus.COMPLETE, 5, 5, 5, COMPLETED, 10, "TwoWay", BDE));
            }
            if (null != propertyList.get(5)) {
                dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate, propertyList.get(5), "BDE_COMPLETED", COMPLETED, ExtractStatus.COMPLETE, 6, 6, 6, COMPLETED, 7, "Population", BDE));
            }
            if (null != propertyList.get(6)) {
                dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate, propertyList.get(6), "BDE_COMPLETED", COMPLETED, ExtractStatus.NOT_RECEIVED, 6, 6, 6, COMPLETED, 10, "Population", BDE));
            }
            if (null != propertyList.get(7)) {
                dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate, propertyList.get(7), "BDE_COMPLETED", COMPLETED, ExtractStatus.COMPLETE, 6, 6, 6, COMPLETED, 10, "TwoWay", BDE));
            }
            if (null != propertyList.get(8)) {
                dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate, propertyList.get(8), "BDE_DECISIONS_IN_PROGRESS", "IN_PROGRESS", ExtractStatus.COMPLETE, 2, 2, 9, null, -1, "TwoWay", BDE));
            }
            if (null != propertyList.get(9)) {
                dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate, propertyList.get(9), "BDE_DECISIONS_IN_PROGRESS", "IN_PROGRESS", ExtractStatus.COMPLETE, 2, 2, 9, null, -1, "TwoWay", BDE));
            }
            processingDate = processingDate.minusDays(1);
        }
        globalCrudService.save(dailyProcessings);
        populateJobDetails(dailyProcessings);
    }

    @ForTesting


    public void saveDataIDP(String clientCode, String propertyCodes) {
        List<String> propertyList = Arrays.asList(propertyCodes.split("\\s*,\\s*"));
        List<PropertyDailyProcessing> dailyProcessings = new ArrayList<>();
        LocalDateTime processingDate = LocalDateTime.now();

        if (propertyList.size() > 0) {
            final PropertyDailyProcessing pdp0 = addPropertyDailyProcessing(clientCode, propertyList.get(0), processingDate, "CDP_DECISIONS_IN_PROGRESS");
            populateFailedDecisionDeliveryJobDataCDP(pdp0.getInputProcessings().get(2));
            populateFailedDecisionDeliveryJobDataCDP(pdp0.getInputProcessings().get(3));
        }
        if (propertyList.size() > 1) {
            final PropertyDailyProcessing pdp1 = addPropertyDailyProcessing(clientCode, propertyList.get(1), processingDate, "CDP_IN_PROGRESS");
            populateProcessingFailedJobDataCDP(pdp1.getInputProcessings().get(2));
        }
        if (propertyList.size() > 2) {
            final PropertyDailyProcessing pdp2 = addPropertyDailyProcessing(clientCode, propertyList.get(2), processingDate, "CDP_IN_PROGRESS");
            populateUserActionRequiredJobDataCDP(pdp2.getInputProcessings().get(3), pdp2.getClientCode(), pdp2.getPropertyCode());
        }
        if (propertyList.size() > 3) {
            final PropertyDailyProcessing pdp3 = addPropertyDailyProcessing(clientCode, propertyList.get(3), processingDate, "CDP_DECISIONS_IN_PROGRESS");
            populateFailedDecisionDeliveryJobDataCDP(pdp3.getInputProcessings().get(2));
        }
        if (propertyList.size() > 4) {
            final PropertyDailyProcessing pdp4 = addPropertyDailyProcessing(clientCode, propertyList.get(4), processingDate, "CDP_IN_PROGRESS");
            populateProcessingFailedJobDataCDP(pdp4.getInputProcessings().get(2));
            populateProcessingFailedJobDataCDP(pdp4.getInputProcessings().get(3));
            populateProcessingFailedJobDataCDP(pdp4.getInputProcessings().get(4));
        }
        if (propertyList.size() > 5) {
            addPropertyDailyProcessing(clientCode, propertyList.get(5), processingDate, "CDP_DECISIONS_IN_PROGRESS");
        }
        if (propertyList.size() > 6) {
            addPropertyDailyProcessing(clientCode, propertyList.get(6), processingDate, "CDP_DECISIONS_IN_PROGRESS");
        }
        if (propertyList.size() > 7) {
            final PropertyDailyProcessing pdp7 = addPropertyDailyProcessing(clientCode, propertyList.get(7), processingDate, "CDP_IN_PROGRESS");
            populateUserActionRequiredJobDataCDP(pdp7.getInputProcessings().get(3), pdp7.getClientCode(), pdp7.getPropertyCode());
        }
        if (propertyList.size() > 8) {
            addPropertyDailyProcessing(clientCode, propertyList.get(8), processingDate, "CDP_IN_PROGRESS");
        }
        if (propertyList.size() > 9) {
            addPropertyDailyProcessing(clientCode, propertyList.get(9), processingDate, "CDP_IN_PROGRESS");
        }

    }

    private void populateProcessingFailedJobDataCDP(InputProcessing inputProcessing) {
        inputProcessing.setJobs(Arrays.asList(createInputProcessingJob(inputProcessing, "NGICdpDeferredDeliveryJob", "NGICdpDeferredDeliveryJob" + inputProcessing.getId() + RandomStringUtils.randomAlphabetic(1), "Problem Description", "9DBFDE12-D066-4A4B-A6EB-0F1B4951F8CB", "FAILED")));
    }

    private void populateUserActionRequiredJobDataCDP(InputProcessing inputProcessing, String clientCode, String propertyCode) {
        inputProcessing.setJobs(Arrays.asList(createInputProcessingJob(inputProcessing, "OperaCdpDataLoad", "OperaCdpDataLoad" + inputProcessing.getId() + RandomStringUtils.randomAlphabetic(1), "Problem Description USER_ACTION_REQUIRED_EXCEPTION", "D9AC1F4C-A533-40E0-B191-0A917F12B99C", "FAILED")));

        seedAlertDataForProperty(clientPropertyCacheService.getProperty(clientCode, propertyCode).getId());
    }

    private void seedAlertDataForProperty(Integer propertyId) {
        String query = "insert into Info_Mgr_Instance values (" +
                " (select Info_Mgr_Type_ID from Info_Mgr_Type where Name = 'MissingRoomTypesInHotelData'), 'test Alert1', 'test Alert1', " + propertyId + ", 1, 'testUser', '2019-02-14 02:36:00.000', 100, getdate(), (select Info_Mgr_Status_ID from Info_Mgr_Status where Name = 'New'), 'Alert', null, null, null, 0, null, null)," +
                " ((select Info_Mgr_Type_ID from Info_Mgr_Type where Name = 'HighestBarFplosCheckFail'), 'test Alert1', 'test Alert1', " + propertyId + ", 1, 'testUser', '2019-02-14 00:20:14.356', 100, getdate(), (select Info_Mgr_Status_ID from Info_Mgr_Status where Name = 'Viewed'), 'Alert', null, null, null, 0, null, null)";
        multiPropertyCrudService.executeNativeUpdateOnSingleProperty(propertyId, query, null);
    }

    private void populateFailedDecisionDeliveryJobDataCDP(InputProcessing inputProcessing) {
        inputProcessing.setJobs(Arrays.asList(createInputProcessingJob(inputProcessing, "NGITARSDecisionDeliveryJob", "NGITARSDecisionDeliveryJob" + inputProcessing.getId() + RandomStringUtils.randomAlphabetic(1), "ForDecisionUploadFailed", "BE82AD95-4E40-4553-9194-1136A1C79F9A", "FAILED")));
    }

    private PropertyDailyProcessing addPropertyDailyProcessing(String clientCode, String propertyCode, LocalDateTime processingDate, String pdpStatus) {
        propertyCode = propertyCode.toUpperCase();
        PropertyDailyProcessing pdp0 = buildPropertyDailyProcessingRecord(clientCode, processingDate, propertyCode, pdpStatus, "TwoWay");

        final InputProcessing bde0 = getInputProcessing(true, COMPLETED, processingDate, ExtractStatus.COMPLETE, 3, 3, 3, COMPLETED, pdp0, 10, BDE);
        bde0.setReceivedDate(withTime(processingDate, 9, 30, 58, 999));
        bde0.setCompletedDate(withTime(processingDate, 12, 01, 0, 0));
        final InputProcessing cdp0_1 = getInputProcessing(true, COMPLETED, processingDate, ExtractStatus.COMPLETE, 3, 3, 3, COMPLETED, pdp0, 10, CDP);
        cdp0_1.setReceivedDate(withTime(processingDate, 12, 30, 58, 999));
        cdp0_1.setCompletedDate(withTime(processingDate, 15, 02, 0, 0));

        final InputProcessing cdp0_2 = getInputProcessing(true, COMPLETED, processingDate, ExtractStatus.COMPLETE, 3, 3, 3, null, pdp0, 10, CDP);
        cdp0_2.setReceivedDate(withTime(processingDate, 15, 30, 58, 999));
        cdp0_2.setCompletedDate(withTime(processingDate, 20, 02, 0, 0));

        final InputProcessing cdp0_3 = getInputProcessing(true, "IN_PROGRESS", processingDate, ExtractStatus.COMPLETE, 3, 3, 3, null
                , pdp0, 10, CDP);
        cdp0_3.setReceivedDate(withTime(processingDate, 18, 30, 58, 999));
        cdp0_3.setCompletedDate(withTime(processingDate, 22, 02, 0, 0));

        final InputProcessing cdp0_4 = getInputProcessing(true, "IN_PROGRESS", processingDate, ExtractStatus.COMPLETE, 3, 3, 3, null
                , pdp0, -1, CDP);
        cdp0_3.setReceivedDate(withTime(processingDate, 18, 30, 58, 999));
        cdp0_3.setCompletedDate(withTime(processingDate, 9, 02, 0, 0));

        pdp0.setInputProcessings(Arrays.asList(bde0, cdp0_1, cdp0_2, cdp0_3, cdp0_4));

        return globalCrudService.save(pdp0);
    }

    private org.joda.time.LocalDateTime withTime(LocalDateTime localDateTime, int hour, int minute, int second, int millisecond) {
        return JavaLocalDateUtils.toJodaLocalDateTime(localDateTime.withHour(hour).withMinute(minute).withSecond(second).withNano(milliToNanoConst * millisecond));
    }

    @ForTesting
    public void populateJobDetails(List<PropertyDailyProcessing> propertyDailyProcessings) {
        populateFailedDecisionDeliveryJobData(propertyDailyProcessings);
        populateUserActionRequiredJobData(propertyDailyProcessings);
        populateProcessingFailedJobData(propertyDailyProcessings);
    }

    private void populateUserActionRequiredJobData(List<PropertyDailyProcessing> propertyDailyProcessings) {
        PropertyDailyProcessing pdpForProcessingFailed = propertyDailyProcessings.get(propertyDailyProcessings.size() - 1);
        pdpForProcessingFailed.getInputProcessings().get(0).setJobs(Arrays.asList(createInputProcessingJob(pdpForProcessingFailed.getInputProcessings().get(0), "OperaDataLoadJob", "OperaDataLoadJob" + RandomStringUtils.randomAlphabetic(1), "Problem Description USER_ACTION_REQUIRED_EXCEPTION", "D9AC1F4C-A533-40E0-B191-0A917F12B99C", "FAILED")));
    }

    private void populateProcessingFailedJobData(List<PropertyDailyProcessing> propertyDailyProcessings) {
        PropertyDailyProcessing pdpForProcessingFailed = propertyDailyProcessings.get(PDP_FOR_PROCESSING_FAILED_INDEX);
        pdpForProcessingFailed.getInputProcessings().get(0).setJobs(Arrays.asList(createInputProcessingJob(pdpForProcessingFailed.getInputProcessings().get(0), "NGIDeferredDeliveryJob", "NGIDeferredDeliveryJob" + RandomStringUtils.randomAlphabetic(1), "Problem Description", "9DBFDE12-D066-4A4B-A6EB-0F1B4951F8CB", "FAILED")));
    }

    private void populateFailedDecisionDeliveryJobData(List<PropertyDailyProcessing> propertyDailyProcessings) {
        PropertyDailyProcessing pdpForDecisionUploadFailedOne = propertyDailyProcessings.get(PDP_FOR_TARS_DECISION_DELIVERY_FAILED_INDEX);
        pdpForDecisionUploadFailedOne.getInputProcessings().get(0).setJobs(Arrays.asList(createInputProcessingJob(pdpForDecisionUploadFailedOne.getInputProcessings().get(0), "NGITARSDecisionDeliveryJob", "NGITARSDecisionDeliveryJob" + RandomStringUtils.randomAlphabetic(1), "ForDecisionUploadFailed", "BE82AD95-4E40-4553-9194-1136A1C79F9A", "FAILED")));
        PropertyDailyProcessing pdpForDecisionUploadFailedTwo = propertyDailyProcessings.get(PDP_FOR_HTNG_DECISION_DELIVERY_FAILED_INDEX);
        pdpForDecisionUploadFailedTwo.getInputProcessings().get(0).setJobs(Arrays.asList(createInputProcessingJob(pdpForDecisionUploadFailedTwo.getInputProcessings().get(0), "HtngDecisionDeliveryJob", "HtngDecisionDeliveryJob" + RandomStringUtils.randomAlphabetic(1), "ForDecisionUploadFailed", "CC82AD95-4E40-4553-9194-1136A1C79FCC", "ABANDONED")));
    }


    private InputProcessingJob createInputProcessingJob(InputProcessing inputProcessing, String jobName, String jobKey, String problemDescription, String jobHash, String executionStatus) {
        Long jobInstanceId = createJobInstance(jobName, jobKey);
        createJobInstanceWorkContext(jobInstanceId);
        Long jobExecutionId = createJobExecution(jobInstanceId);
        createJobExecutionContext(jobExecutionId);
        Long stepExecutionId = createStepExecution(jobExecutionId);
        createJobExecutionParam(jobExecutionId, "TARS");
        createProblem(jobInstanceId, jobExecutionId, stepExecutionId, problemDescription);
        createJobState(jobInstanceId, jobExecutionId, stepExecutionId, jobName, executionStatus, 1, jobHash);
        InputProcessingJob inputProcessingJob = new InputProcessingJob();
        inputProcessingJob.setJobInstanceId(jobInstanceId);
        inputProcessingJob.setInputProcessing(inputProcessing);
        globalCrudService.save(inputProcessingJob);
        return inputProcessingJob;
    }

    private void createJobState(Long jobInstanceId, Long jobExecutionId, Long stepExecutionId, String jobName, String executionStatus, int problemCount, String hash) {
        jobCrudService.executeUpdateByNativeQuery(
                "insert into job_state (job_instance_id, job_execution_id, JOB_NAME, EXECUTION_STATUS, PROBLEM_COUNT, JOB_HASH, STEP_EXECUTION_ID) values " +
                        "(:jobInstanceId, :jobExecutionId, :jobName, :executionStatus, :problemCount, :jobHash, :stepExecutionId)",
                QueryParameter.with("jobInstanceId", jobInstanceId)
                        .and("jobExecutionId", jobExecutionId)
                        .and("jobName", jobName)
                        .and("executionStatus", executionStatus)
                        .and("problemCount", problemCount)
                        .and("jobHash", hash)
                        .and("stepExecutionId", stepExecutionId)
                        .parameters());
    }

    private void createJobExecutionParam(Long jobExecutionId, final String externalSystemCode) {
        jobCrudService.executeUpdateByNativeQuery(
                "insert into job_execution_params (job_execution_id, TYPE_CD, KEY_NAME, STRING_VAL, DATE_VAL, IDENTIFYING) values (:jobExecutionId, 'STRING', 'externalSystemCode', :externalSystemCode, :dateVal, 'Y')",
                QueryParameter.with("jobExecutionId", jobExecutionId)
                        .and("jobExecutionId", jobExecutionId)
                        .and("externalSystemCode", externalSystemCode)
                        .and("dateVal", new Date())
                        .parameters());
    }

    private void createJobExecutionContext(Long jobexecutionID) {
        jobCrudService.executeUpdateByNativeQuery("insert into job_execution_context (job_execution_id, short_context, serialized_context) values (:jobExecutionId, 'test','NULL')",
                QueryParameter.with("jobExecutionId", jobexecutionID).parameters());
    }

    private void createJobInstanceWorkContext(Long jobInstanceID) {
        jobCrudService.executeUpdateByNativeQuery("insert into job_instance_work_context (job_Instance_ID, client_id, client_code, property_id, property_code, property_stage, user_id, hostname, sas_server_name, external_system_code, classifier," +
                        "db_server_name, event_date) values (:jobInstanceID, null, null, null, null, null, null, null, null, null, null, null, null )",
                QueryParameter.with("jobInstanceID", jobInstanceID).parameters());
    }

    private Long createJobExecution(Long jobInstanceId) {
        // first get an available id
        Long jobExecutionId = getId(
                "select count(*) from dbo.job_execution where job_execution_id = :jobExecutionId",
                "jobExecutionId");

        // now create the instance
        jobCrudService.executeUpdateByNativeQuery(
                "insert into job_execution (job_execution_id, job_instance_id, create_time) values (:jobExecutionId, :jobInstanceId, :createTime)",
                QueryParameter.with("jobExecutionId", jobExecutionId)
                        .and("jobInstanceId", jobInstanceId)
                        .and("createTime", new Date())
                        .parameters());

        return jobExecutionId;
    }

    private Long createStepExecution(Long jobExecutionId) {
        // first get an available id
        Long stepExecutionId = getId(
                "select count(*) from dbo.step_execution where step_execution_id = :stepExecutionId",
                "stepExecutionId");

        // now create the instance
        jobCrudService.executeUpdateByNativeQuery(
                "insert into step_execution (step_execution_id, version, step_name, job_execution_id, start_time) " +
                        "values (:stepExecutionId, :version, :stepName, :jobExecutionId, :startTime)",
                QueryParameter.with("stepExecutionId", stepExecutionId)
                        .and("version", 1)
                        .and("stepName", "step123")
                        .and("jobExecutionId", jobExecutionId)
                        .and("startTime", new Date())
                        .parameters());

        return stepExecutionId;
    }

    private void createProblem(Long jobInstanceId, Long jobExecutionId, Long stepExecutionId, String problemDescription) {
        Problem problem = new Problem();
        problem.setJobInstanceId(jobInstanceId);
        problem.setJobExecutionId(jobExecutionId);
        problem.setStepExecutionId(stepExecutionId);
        problem.setActive(true);
        problem.setCreationDate(new Date());
        problem.setDescription(problemDescription);
        problem.setScore(100);
        problem.setErrorCode(141);
        problem.setVersion(0);
        jobCrudService.save(problem);

    }

    private Long createJobInstance(String jobName, String jobKey) {
        // first get an available id
        Long jobInstanceId = getId(
                "select count(*) from dbo.job_instance where job_instance_id = :jobInstanceId",
                "jobInstanceId");

        // now create the instance
        jobCrudService.executeUpdateByNativeQuery(
                "insert into job_instance (job_instance_id, job_name, job_key) values (:jobInstanceId, :jobName, :jobKey)",
                QueryParameter.with("jobInstanceId", jobInstanceId)
                        .and("jobName", jobKey)
                        .and("jobKey", jobName)
                        .parameters());

        return jobInstanceId;
    }


    private Long getId(String query, String idName) {
        Long id = 100000L;
        int count;
        int attempts = 0;
        do {
            count = jobCrudService.findByNativeQuerySingleResult(query,
                    QueryParameter.with(idName, id).parameters());
            if (count != 0) {
                id += 10000;
            }
        } while (count != 0 && attempts++ < 100);
        return id;
    }

    @ForTesting
    public List<PropertyDailyProcessing> doPopulateTestData(String clientCode, String propertyCodes, LocalDateTime processingDate, String inputType) {
        List<String> propertyList = Arrays.asList(propertyCodes.split("\\s*,\\s*"));
        List<PropertyDailyProcessing> dailyProcessings = new ArrayList<>();

        if (null != propertyList.get(0)) {
            dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate, propertyList.get(0), "BDE_NOT_RECEIVED", "NOT_RECEIVED", ExtractStatus.INCOMPLETE, 3, 3, -1, null, -1, "TwoWay", inputType));
            dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate.minusDays(1), propertyList.get(0), "BDE_IN_PROGRESS", "IN_PROGRESS", ExtractStatus.COMPLETE, 3, 3, 7, null, -1, "TwoWay", inputType));
        }
        if (null != propertyList.get(1)) {
            dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate, propertyList.get(1), "BDE_DECISIONS_IN_PROGRESS", "IN_PROGRESS", ExtractStatus.COMPLETE, 2, 2, 9, null, -1, "TwoWay", inputType));
            PropertyDailyProcessing bde_completed = getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate.minusDays(1), propertyList.get(1), "BDE_COMPLETED", COMPLETED, ExtractStatus.COMPLETE, 3, 3, 3, COMPLETED, 7, "TwoWay", inputType);
            //simulate scenario where extract was received yesterday for todays BDE
            bde_completed.getInputProcessings().get(0).setExtractStartedDateTime(bde_completed.getInputProcessings().get(0).getExtractCompletedDateTime().minusDays(1));
            bde_completed.getInputProcessings().get(0).setExtractCompletedDateTime(bde_completed.getInputProcessings().get(0).getExtractStartedDateTime());
            dailyProcessings.add(bde_completed);
        }
        if (null != propertyList.get(2)) {
            dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate, propertyList.get(2), "BDE_COMPLETED", COMPLETED, ExtractStatus.COMPLETE, 3, 3, 3, COMPLETED, 10, "TwoWay", inputType));
            dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate.minusDays(1), propertyList.get(2), "BDE_COMPLETED", COMPLETED, ExtractStatus.COMPLETE, 6, 6, 6, COMPLETED, 7, "TwoWay", inputType));
        }
        if (null != propertyList.get(3)) {
            dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate, propertyList.get(3), "BDE_COMPLETED", COMPLETED, ExtractStatus.COMPLETE, 6, 6, 6, COMPLETED, 10, "TwoWay", inputType));
            dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate.minusDays(1), propertyList.get(3), "BDE_DECISIONS_IN_PROGRESS", "IN_PROGRESS", ExtractStatus.COMPLETE, 2, 2, 9, null, -1, "TwoWay", inputType));
            dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate.minusDays(2), propertyList.get(3), "BDE_DECISIONS_IN_PROGRESS", "IN_PROGRESS", ExtractStatus.COMPLETE, 2, 2, 9, null, -1, "TwoWay", inputType));
        }
        if (null != propertyList.get(4)) {
            dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate.minusDays(1), propertyList.get(4), "BDE_IN_PROGRESS", "IN_PROGRESS", ExtractStatus.COMPLETE, 3, 3, 7, null, -1, "TwoWay", inputType));
            dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate, propertyList.get(4), "BDE_IN_PROGRESS", "IN_PROGRESS", ExtractStatus.COMPLETE, 3, 3, 7, null, -1, "TwoWay", inputType));
        }

        globalCrudService.save(dailyProcessings);
        return dailyProcessings;
    }

    @ForTesting
    public List<PropertyDailyProcessing> doPopulateTestDataForReceivedDTTMColumn(String clientCode, String propertyCodes, LocalDateTime processingDate) {
        List<String> propertyList = Arrays.asList(propertyCodes.split("\\s*,\\s*"));
        List<PropertyDailyProcessing> dailyProcessings = new ArrayList<>();

        if (null != propertyList.get(0)) {
            dailyProcessings.add(getPDPWithEReceivedDTTMColumnPopulated(clientCode, processingDate, propertyList.get(0), "BDE_NOT_RECEIVED", "NOT_RECEIVED", ExtractStatus.INCOMPLETE, 3, 3, -1, null, -1, "TwoWay", BDE));
            dailyProcessings.add(getPDPWithEReceivedDTTMColumnPopulated(clientCode, processingDate.minusDays(1), propertyList.get(0), "BDE_IN_PROGRESS", "IN_PROGRESS", ExtractStatus.COMPLETE, 3, 3, 7, null, -1, "TwoWay", BDE));
        }
        if (null != propertyList.get(1)) {
            dailyProcessings.add(getPDPWithEReceivedDTTMColumnPopulated(clientCode, processingDate, propertyList.get(1), "BDE_DECISIONS_IN_PROGRESS", "IN_PROGRESS", ExtractStatus.COMPLETE, 2, 2, 9, null, -1, "TwoWay", BDE));
            PropertyDailyProcessing bde_completed = getPDPWithEReceivedDTTMColumnPopulated(clientCode, processingDate.minusDays(1), propertyList.get(1), "BDE_COMPLETED", COMPLETED, ExtractStatus.COMPLETE, 3, 3, 3, COMPLETED, 7, "TwoWay", BDE);
            //simulate scenario where extract was received yesterday for todays BDE
            bde_completed.getInputProcessings().get(0).setReceivedDate(bde_completed.getInputProcessings().get(0).getReceivedDate().minusDays(1));
            dailyProcessings.add(bde_completed);
        }
        if (null != propertyList.get(2)) {
            dailyProcessings.add(getPDPWithEReceivedDTTMColumnPopulated(clientCode, processingDate, propertyList.get(2), "BDE_COMPLETED", COMPLETED, ExtractStatus.COMPLETE, 3, 3, 3, COMPLETED, 10, "TwoWay", BDE));
            dailyProcessings.add(getPDPWithEReceivedDTTMColumnPopulated(clientCode, processingDate.minusDays(1), propertyList.get(2), "BDE_COMPLETED", COMPLETED, ExtractStatus.COMPLETE, 6, 6, 6, COMPLETED, 7, "TwoWay", BDE));
        }
        if (null != propertyList.get(3)) {
            dailyProcessings.add(getPDPWithEReceivedDTTMColumnPopulated(clientCode, processingDate, propertyList.get(3), "BDE_COMPLETED", COMPLETED, ExtractStatus.COMPLETE, 6, 6, 6, COMPLETED, 10, "TwoWay", BDE));
            dailyProcessings.add(getPDPWithEReceivedDTTMColumnPopulated(clientCode, processingDate.minusDays(1), propertyList.get(3), "BDE_DECISIONS_IN_PROGRESS", "IN_PROGRESS", ExtractStatus.COMPLETE, 2, 2, 9, null, -1, "TwoWay", BDE));
            dailyProcessings.add(getPDPWithEReceivedDTTMColumnPopulated(clientCode, processingDate.minusDays(2), propertyList.get(3), "BDE_DECISIONS_IN_PROGRESS", "IN_PROGRESS", ExtractStatus.COMPLETE, 2, 2, 9, null, -1, "TwoWay", BDE));
        }
        if (null != propertyList.get(4)) {
            dailyProcessings.add(getPDPWithEReceivedDTTMColumnPopulated(clientCode, processingDate.minusDays(1), propertyList.get(4), "BDE_IN_PROGRESS", "IN_PROGRESS", ExtractStatus.COMPLETE, 3, 3, 7, null, -1, "TwoWay", BDE));
            dailyProcessings.add(getPDPWithEReceivedDTTMColumnPopulated(clientCode, processingDate, propertyList.get(4), "BDE_IN_PROGRESS", "IN_PROGRESS", ExtractStatus.COMPLETE, 3, 3, 7, null, -1, "TwoWay", BDE));
        }

        globalCrudService.save(dailyProcessings);
        return dailyProcessings;
    }

    public List<PropertyDailyProcessing> doPopulateYesterdayUserActionRequiredData(String clientCode, String propertyCode) {
        List<PropertyDailyProcessing> dailyProcessings = new ArrayList<>();
        LocalDateTime processingDate = LocalDateTime.now();
        dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate, propertyCode, "BDE_NOT_RECEIVED", "NOT_RECEIVED", ExtractStatus.INCOMPLETE, 3, 3, -1, null, -1, "TwoWay", BDE));
        dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate.minusDays(1), propertyCode, "BDE_IN_PROGRESS", "IN_PROGRESS", ExtractStatus.COMPLETE, 3, 3, 7, null, -1, "TwoWay", BDE));
        globalCrudService.save(dailyProcessings);
        return dailyProcessings;
    }

    public List<PropertyDailyProcessing> doPopulateYesterdayIncompleteDataWithTodaysProcessingCompleted(String clientCode, String propertyCode) {
        List<PropertyDailyProcessing> dailyProcessings = new ArrayList<>();
        LocalDateTime processingDate = LocalDateTime.now();
        dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate, propertyCode, DailyProcessingStatus.DAILY_PROCESSING_COMPLETED.toString(), InputProcessingStatus.COMPLETED.toString(), ExtractStatus.COMPLETE, 3, 3, 4, "COMPLETED", 6, "TwoWay", BDE));
        dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate.minusDays(1), propertyCode, "BDE_IN_PROGRESS", "IN_PROGRESS", ExtractStatus.COMPLETE, 3, 3, 7, null, -1, "TwoWay", BDE));
        globalCrudService.save(dailyProcessings);
        return dailyProcessings;
    }

    private PropertyDailyProcessing getPDPWithExtractCompletedDTTMColumnPopulated(String clientCode, LocalDateTime processingDate, String propertyCode, String pdpStatus, String ipStatus, ExtractStatus extractStatus, int receivedTimeHour, int extractReceivedTimeHour, int completedTimeHr, String decDelStatus, int processingCompletedDTTM, String stage, String inputType) {
        PropertyDailyProcessing pdp = buildPropertyDailyProcessingRecord(clientCode, processingDate, propertyCode, pdpStatus, stage);
        pdp.setInputProcessings(Arrays.asList(getInputProcessing(true, ipStatus, processingDate, extractStatus, receivedTimeHour, extractReceivedTimeHour, completedTimeHr, decDelStatus, pdp, processingCompletedDTTM, inputType)));
        return pdp;
    }

    private PropertyDailyProcessing getPDPWithEReceivedDTTMColumnPopulated(String clientCode, LocalDateTime processingDate, String propertyCode, String pdpStatus, String ipStatus, ExtractStatus extractStatus, int receivedTimeHour, int extractReceivedTimeHour, int completedTimeHr, String decDelStatus, int processingCompletedDTTM, String stage, String inputType) {
        PropertyDailyProcessing pdp = buildPropertyDailyProcessingRecord(clientCode, processingDate, propertyCode, pdpStatus, stage);
        pdp.setInputProcessings(Arrays.asList(getInputProcessing(false, ipStatus, processingDate, extractStatus, receivedTimeHour, extractReceivedTimeHour, completedTimeHr, decDelStatus, pdp, processingCompletedDTTM, inputType)));
        return pdp;
    }

    private PropertyDailyProcessing buildPropertyDailyProcessingRecord(String clientCode, LocalDateTime processingDate, String propertyCode, String pdpStatus, String stage) {
        PropertyDailyProcessing pdp = new PropertyDailyProcessing();
        pdp.setClientCode(clientCode);
        pdp.setClientName(clientCode);
        pdp.setPropertyCode(propertyCode);
        pdp.setProcessingDate(JavaLocalDateUtils.toJodaLocalDate(processingDate.toLocalDate()));
        pdp.setStage(stage);
        pdp.setPropertyTimeZone("Europe/Helsinki");
        pdp.setExpectedCdpCount(0);
        pdp.setCompletedCdpCount(0);
        pdp.setStatus(pdpStatus);
        pdp.setCompletedRssCount(0);
        pdp.setPropertyName(propertyCode);
        pdp.setClientCode(clientCode);
        pdp.setCompletedFunctionSpaceCount(0);
        pdp.setCompletedScheduledReportsCount(0);
        return pdp;
    }

    private InputProcessing getInputProcessing(boolean shouldPopulateExtractCompletedDTTMColumn, String ipStatus, LocalDateTime date, ExtractStatus extractStatus, int receivedTimeHr, int extractReceivedTimeHour, int extractCompletedTimeHr, String decDelStatus, PropertyDailyProcessing pdp, int processingCompletedDTTM, String inputType) {
        InputProcessing inputProcessing = new InputProcessing();
        inputProcessing.setInputType(inputType);
        inputProcessing.setInputId("inputId");
        inputProcessing.setStatus(ipStatus);
        inputProcessing.setExtractStatus(extractStatus);
        inputProcessing.setDecisionDeliveries(Arrays.asList(buildDecisionDelivery(decDelStatus, inputProcessing)));
        inputProcessing.setPropertyDailyProcessing(pdp);

        if (-1 != receivedTimeHr) {
            //populate received DTTM always, which will never be null as comapred to ExtractCompletedDTTM
            inputProcessing.setReceivedDate(withTime(date, receivedTimeHr, 0, 0, 0));
        }
        if (-1 != processingCompletedDTTM) {
            inputProcessing.setCompletedDate(withTime(date, processingCompletedDTTM, 0, 0, 0));
        }
        //This is to simulate that we will refer to ReceivedDTTM when ExtractCompletedDTTM is null
        if (shouldPopulateExtractCompletedDTTMColumn) {
            if (-1 != extractReceivedTimeHour) {
                inputProcessing.setExtractStartedDateTime(withTime(date, extractReceivedTimeHour, 0, 0, 0));
            }
            if (-1 != extractCompletedTimeHr) {
                inputProcessing.setExtractCompletedDateTime(withTime(date, extractCompletedTimeHr, 0, 0, 0));
            }
        }
        return inputProcessing;
    }

    private DecisionDelivery buildDecisionDelivery(String decDelStatus, InputProcessing inputProcessing) {
        DecisionDelivery decisionDelivery = new DecisionDelivery();
        decisionDelivery.setResult(decDelStatus);//*
        decisionDelivery.setInputProcessing(inputProcessing);
        decisionDelivery.setDestinationId("destId");
        decisionDelivery.setDecisionId("decisionId");
        decisionDelivery.setResult("result");
        return decisionDelivery;
    }

    public List<PropertyDailyProcessing> populateDataForPropertiesWithProcessingCompleted(String clientCode, String testProperties, List<Integer> extractReceivedTimeHour, LocalDateTime processingDate, String inputType) {
        List<String> properties = Arrays.asList(testProperties.split(","));
        List<PropertyDailyProcessing> propertyDailyProcessing = new ArrayList<>();
        propertyDailyProcessing.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate.minusDays(1), properties.get(0), "DAILY_PROCESSING_COMPLETED", "COMPLETED", ExtractStatus.COMPLETE, 3, 3, extractReceivedTimeHour.get(0), null, 10, "TwoWay", inputType));
        propertyDailyProcessing.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate.minusDays(2), properties.get(0), "DAILY_PROCESSING_COMPLETED", "COMPLETED", ExtractStatus.COMPLETE, 3, 3, extractReceivedTimeHour.get(1), null, 10, "TwoWay", inputType));
        propertyDailyProcessing.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate.minusDays(1), properties.get(1), "DAILY_PROCESSING_COMPLETED", "COMPLETED", ExtractStatus.COMPLETE, 3, 3, extractReceivedTimeHour.get(0), null, 1, "TwoWay", inputType));
        propertyDailyProcessing.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate.minusDays(2), properties.get(1), "DAILY_PROCESSING_COMPLETED", "COMPLETED", ExtractStatus.COMPLETE, 3, 3, extractReceivedTimeHour.get(1), null, 1, "TwoWay", inputType));
        globalCrudService.save(propertyDailyProcessing);
        return propertyDailyProcessing;
    }

    public List<PropertyDailyProcessing> doPopulateTestDataForIDP(String clientCode, String propertyCodes, LocalDateTime processingDate) {
        List<String> propertyList = Arrays.asList(propertyCodes.split("\\s*,\\s*"));
        List<PropertyDailyProcessing> dailyProcessings = new ArrayList<>();

        if (null != propertyList.get(0)) {
            dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate, propertyList.get(0), "CDP_NOT_RECEIVED", "NOT_RECEIVED", ExtractStatus.INCOMPLETE, 3, 3, -1, null, -1, "TwoWay", CDP));
            dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate.minusDays(1), propertyList.get(0), "CDP_IN_PROGRESS", "IN_PROGRESS", ExtractStatus.COMPLETE, 3, 3, 7, null, -1, "TwoWay", CDP));
        }
        if (null != propertyList.get(1)) {
            dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate, propertyList.get(1), "CDP_DECISIONS_IN_PROGRESS", "IN_PROGRESS", ExtractStatus.COMPLETE, 2, 2, 9, null, -1, "TwoWay", CDP));
            PropertyDailyProcessing cdpCompletedWithin = getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate.minusDays(1), propertyList.get(1), "CDP_COMPLETED", COMPLETED, ExtractStatus.COMPLETE, 3, 3, 3, COMPLETED, 7, "TwoWay", CDP);

            cdpCompletedWithin.getInputProcessings().get(0).setReceivedDate(withTime(processingDate.minusDays(1), 6, 0, 58, 999));
            cdpCompletedWithin.getInputProcessings().get(0).setCompletedDate(withTime(processingDate.minusDays(1), 9, 0, 0, 0));
            dailyProcessings.add(cdpCompletedWithin);

        }
        if (null != propertyList.get(2)) {
            PropertyDailyProcessing cdpCompletedAfter = getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate, propertyList.get(2), "CDP_COMPLETED", COMPLETED, ExtractStatus.COMPLETE, 3, 3, 3, COMPLETED, 10, "TwoWay", CDP);
            cdpCompletedAfter.getInputProcessings().get(0).setReceivedDate(withTime(processingDate, 5, 59, 58, 999));
            cdpCompletedAfter.getInputProcessings().get(0).setCompletedDate(withTime(processingDate, 9, 0, 0, 0));

            dailyProcessings.add(cdpCompletedAfter);
            dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate.minusDays(1), propertyList.get(2), "CDP_COMPLETED", COMPLETED, ExtractStatus.COMPLETE, 6, 6, 6, COMPLETED, 7, "TwoWay", CDP));

        }
        if (null != propertyList.get(3)) {
            dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate, propertyList.get(3), "CDP_COMPLETED", COMPLETED, ExtractStatus.COMPLETE, 6, 6, 6, COMPLETED, 10, "TwoWay", CDP));
            dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate.minusDays(1), propertyList.get(3), "CDP_DECISIONS_IN_PROGRESS", "IN_PROGRESS", ExtractStatus.COMPLETE, 2, 2, 9, null, -1, "TwoWay", CDP));
            dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate.minusDays(2), propertyList.get(3), "CDP_DECISIONS_IN_PROGRESS", "IN_PROGRESS", ExtractStatus.COMPLETE, 2, 2, 9, null, -1, "TwoWay", CDP));
        }
        if (null != propertyList.get(4)) {
            dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate.minusDays(1), propertyList.get(4), "CDP_IN_PROGRESS", "IN_PROGRESS", ExtractStatus.COMPLETE, 3, 3, 7, null, -1, "TwoWay", CDP));
            dailyProcessings.add(getPDPWithExtractCompletedDTTMColumnPopulated(clientCode, processingDate, propertyList.get(4), "CDP_IN_PROGRESS", "IN_PROGRESS", ExtractStatus.COMPLETE, 3, 3, 7, null, -1, "TwoWay", CDP));
        }

        globalCrudService.save(dailyProcessings);
        return dailyProcessings;
    }
}
