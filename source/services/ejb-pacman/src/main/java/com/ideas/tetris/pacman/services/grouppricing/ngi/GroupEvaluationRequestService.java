package com.ideas.tetris.pacman.services.grouppricing.ngi;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.budget.currency.SalesAndCateringCurrencyExchangeService;
import com.ideas.tetris.pacman.services.budget.dto.CurrencyExchangeDTO;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceFunctionRoom;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceGuestRoomCategory;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceMarketSegment;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.FunctionSpaceRevenueGroup;
import com.ideas.tetris.pacman.services.functionspace.configuration.service.FunctionSpaceConfigurationService;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigurationConferenceAndBanquet;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingEvaluationMethod;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.ConferenceAndBanquetService;
import com.ideas.tetris.pacman.services.grouppricing.configuration.service.GroupPricingConfigurationConferenceAndBanquetService;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluation;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationArrivalDate;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationConferenceAndBanquet;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationCost;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationCostType;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationDayOfStay;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationFunctionSpace;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationFunctionSpaceConfAndBanq;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationFunctionSpaceFunctionRoom;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationRoomType;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationRoomTypeDayOfStay;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.service.GroupEvaluationService;
import com.ideas.tetris.pacman.services.grouppricing.ngi.dto.EvaluationCost;
import com.ideas.tetris.pacman.services.grouppricing.ngi.dto.EvaluationRequest;
import com.ideas.tetris.pacman.services.grouppricing.ngi.dto.EvaluationRevenue;
import com.ideas.tetris.pacman.services.grouppricing.ngi.dto.EvaluationRoomNight;
import com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentSummary;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.pacman.util.CollectionUtils;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.ngi.NGIConvertUtils;
import com.ideas.tetris.platform.common.rest.mapper.RestClient;
import com.ideas.tetris.platform.common.rest.mapper.RestEndpoints;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED;
import static com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupPricingMaterializationStatus.SCENARIO;
import static com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupPricingMaterializationStatus.values;
import static com.ideas.tetris.platform.common.errorhandling.ErrorCode.GROUP_EVALUATION_SALES_AND_CATERING_POPULATION_FAILED;
import static com.ideas.tetris.platform.common.errorhandling.ErrorCode.GROUP_EVALUATION_SALES_AND_CATERING_POPULATION_FAILED_NO_CORRESPONDING_ROOM_TYPE_FOUND;
import static java.lang.Thread.sleep;
import static java.util.Arrays.stream;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Component
@Transactional
public class GroupEvaluationRequestService {
    private static final Logger LOGGER = Logger.getLogger(GroupEvaluationRequestService.class);

    private static final String RC_EVALUATION = "ROOM_CLASS";
    private static final String ROH_EVALUATION = "RUN_OF_HOUSE";

    private static final int MILLIS_BETWEEN_RETRIES = 5000;
    private static final int TIMEOUT_MILLIS = 60000;

    private static final String CLIENT_CODE = "clientCode";
    private static final String PROPERTY_CODE = "propertyCode";
    private static final String EVALUATION_ID = "evaluationId";
    private static final String PROPERTY_ID = "propertyId";

    @Autowired
    RestClient restClient;

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;

    @Autowired
    FunctionSpaceConfigurationService functionSpaceConfigurationService;

    @Autowired
    GroupPricingConfigurationConferenceAndBanquetService groupPricingConfigurationConferenceAndBanquetService;

    @Autowired
    GroupEvaluationService groupEvaluationService;

    @Autowired
	private PacmanConfigParamsService configParamsService;

    @Autowired
    private SalesAndCateringCurrencyExchangeService salesAndCateringCurrencyExchangeService;

    @Autowired
    private ConferenceAndBanquetService conferenceAndBanquetService;

    public GroupEvaluation getGroupEvaluationForRequest(String clientCode, String propertyCode, String evaluationId) {
        LOGGER.info("Processing Evaluation Request for Client Code: " + clientCode + " Property Code: " + propertyCode + " Evaluation ID: " + evaluationId);
        EvaluationRequest evaluationRequest = retrieveEvaluationRequest(clientCode, propertyCode, evaluationId);

        if (checkIfEvaluationRoomNightsNeedsToBeRemoved(evaluationRequest)) {
            removeEvaluationRoomNightsFromEvaluationHavingZeroRoomsForAllOccupancyDates(evaluationRequest);
        }

        return evaluationRequest != null ? buildGroupEvaluation(evaluationRequest) : null;
    }

    private boolean checkIfEvaluationRoomNightsNeedsToBeRemoved(EvaluationRequest evaluationRequest) {
        return evaluationRequest != null && RC_EVALUATION.equalsIgnoreCase(evaluationRequest.getEvaluationType())
                && evaluationRequest.getEvaluationRoomNights().size() > 0;
    }

    private void removeEvaluationRoomNightsFromEvaluationHavingZeroRoomsForAllOccupancyDates(EvaluationRequest evaluationRequest) {
        Map<String, List<EvaluationRoomNight>> roomNightsByGuestRoomTypeName =
                evaluationRequest.getEvaluationRoomNights()
                        .stream()
                        .collect(Collectors.groupingBy(EvaluationRoomNight::getRoomType));

        List<EvaluationRoomNight> roomNightsList = new ArrayList<>();
        roomNightsByGuestRoomTypeName.forEach((roomType, evaluationRoomNights) -> {
            if (ifRoomTypeHasNumberOfRoomsGreaterThanZero(evaluationRoomNights)) {
                roomNightsList.addAll(evaluationRoomNights);
            }
        });
        if (roomNightsList.size() > 0) {
            evaluationRequest.getEvaluationRoomNights().removeIf(roomNight -> roomNightsList.contains(roomNight));
        }
    }

    private boolean ifRoomTypeHasNumberOfRoomsGreaterThanZero(List<EvaluationRoomNight> evaluationRoomNights) {
        return evaluationRoomNights.stream().allMatch(e -> e.getNumberOfRooms() == 0);

    }

    private GroupEvaluation buildGroupEvaluation(EvaluationRequest evaluationRequest) {
        GroupEvaluation existingEvaluation = tenantCrudService.findByNamedQuerySingleResult(GroupEvaluation.FIND_BY_SALES_CATERING_BOOKING_ID, QueryParameter.with("salesCateringBookingId", evaluationRequest.getBookingId()).parameters());
        boolean isFunctionSpaceEnabled = isFunctionSpaceEnabled();

        GroupEvaluation groupEvaluation = new GroupEvaluation();
        groupEvaluation.setFromSalesAndCatering(true);
        groupEvaluation.setSalesCateringBookingId(evaluationRequest.getBookingId());
        groupEvaluation.setGroupName(evaluationRequest.getGroupName());
        groupEvaluation.setNotes(evaluationRequest.getDescription());
        groupEvaluation.setMarketSegment(getMarketSegment(evaluationRequest.getMarketSegment()));
        groupEvaluation.setEvaluationRequestId(evaluationRequest.getId());
        groupEvaluation.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        groupEvaluation.setMaterializationStatus(stream(values()).filter(s -> s.getCaption().equalsIgnoreCase(evaluationRequest.getBookingStatus())).findFirst().orElse(SCENARIO));
        groupEvaluation.setCallbackUrl(evaluationRequest.getCallbackURL());

        if (shouldApplyCurrencyConversion(evaluationRequest)) {
            CurrencyExchangeDTO currencyExchangeDTO = salesAndCateringCurrencyExchangeService.getCurrencyExchangeDTO(evaluationRequest.getClientCode(), evaluationRequest.getPropertyCode(), evaluationRequest.getSalesAndCateringCurrencyCode());
            groupEvaluation.setCurrencyExchangeRate(currencyExchangeDTO.getExchangeRate());
            handleSalesAndCateringCurrencyConversion(evaluationRequest, currencyExchangeDTO.getExchangeRate());
        }

        groupEvaluation.setEvaluationType(evaluationRequest.identifyEvaluationType(isFunctionSpaceEnabled));
        groupEvaluation.setGroupEvaluationArrivalDates(addArrivalDate(groupEvaluation, evaluationRequest, isFunctionSpaceEnabled));

        // Check for Function Space License
        if (isFunctionSpaceEnabled) {
            groupEvaluation.setGroupEvaluationFunctionSpaces(addFunctionSpaceEvents(groupEvaluation, evaluationRequest));
        }

        if (shouldPopulateFSEvaluationConfBanq(isFunctionSpaceEnabled)) {
            groupEvaluation.setGroupEvaluationFunctionSpaceConfAndBanquets(addGroupEvaluationConferenceAndBanquets(groupEvaluation, evaluationRequest));
        } else {
            groupEvaluation.setGroupEvaluationConferenceAndBanquets(addConferenceAndBanquets(groupEvaluation, evaluationRequest));
        }

        groupEvaluation.setGroupEvaluationCosts(addCosts(groupEvaluation, evaluationRequest.getEvaluationCost()));
        groupEvaluation.setGroupEvaluationRoomTypes(RC_EVALUATION.equalsIgnoreCase(evaluationRequest.getEvaluationType()) ? addRoomTypeNights(groupEvaluation, evaluationRequest, isFunctionSpaceEnabled) : new LinkedHashSet<>());
        groupEvaluation.setGroupEvaluationDayOfStays(ROH_EVALUATION.equals(evaluationRequest.getEvaluationType()) ? addDayOfStaysForROH(groupEvaluation, evaluationRequest, isFunctionSpaceEnabled) : new LinkedHashSet<>());

        groupEvaluation.setId(existingEvaluation != null ? existingEvaluation.getId() : null);

        groupEvaluation.setEvaluationRequestErrors(evaluationRequest.getErrors());

        groupEvaluationService.hydrateGroupEvaluations(Collections.singletonList(groupEvaluation), true);

        return groupEvaluation;
    }

    private boolean shouldPopulateFSEvaluationConfBanq(boolean isFunctionSpaceEnabled) {
        return isFunctionSpaceEnabled || conferenceAndBanquetService.shouldUseFSRevenueStreams();
    }

    private boolean shouldApplyCurrencyConversion(EvaluationRequest evaluationRequest) {
        return isSalesAndCateringRMSCurrencyConversionEnabled() && isCurrencyConversionRequired(evaluationRequest);
    }

    private boolean isCurrencyConversionRequired(EvaluationRequest evaluationRequest) {
        return salesAndCateringCurrencyExchangeService.isCurrencyConversionRequired(evaluationRequest.getSalesAndCateringCurrencyCode());
    }

    private boolean isSalesAndCateringRMSCurrencyConversionEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SALES_AND_CATERING_RMS_CURRENCY_CONVERSION_ENABLED);
    }

    private void handleSalesAndCateringCurrencyConversion(EvaluationRequest evaluationRequest, BigDecimal exchangeRate) {
        applyCurrencyConversionOnSalesAndCateringEventRevenue(evaluationRequest, exchangeRate);
        applyCurrencyConversionOnSalesAndCateringEvaluationRevenue(evaluationRequest, exchangeRate);
    }

    private void applyCurrencyConversionOnSalesAndCateringEvaluationRevenue(EvaluationRequest evaluationRequest, BigDecimal exchangeRate) {
        if (CollectionUtils.isNotEmpty(evaluationRequest.getEvaluationRevenues())) {
            evaluationRequest.getEvaluationRevenues().forEach(evaluationRevenue -> evaluationRevenue.setRevenue(salesAndCateringCurrencyExchangeService.getConvertedValueUsingExchangeRate(BigDecimal.valueOf(evaluationRevenue.getRevenue()), exchangeRate).doubleValue()));
        }
    }

    private void applyCurrencyConversionOnSalesAndCateringEventRevenue(EvaluationRequest evaluationRequest, BigDecimal exchangeRate) {
        List<EvaluationRevenue> evaluationRevenues = evaluationRequest.getEvaluationEvents().stream().flatMap(event -> event.getRevenues().stream()).collect(Collectors.toList());
        evaluationRevenues.forEach(evaluationRevenue -> evaluationRevenue.setRevenue(salesAndCateringCurrencyExchangeService.getConvertedValueUsingExchangeRate(BigDecimal.valueOf(evaluationRevenue.getRevenue()), exchangeRate).doubleValue()));
    }

    private Set<GroupEvaluationArrivalDate> addArrivalDate(GroupEvaluation groupEvaluation, EvaluationRequest evaluationRequest, boolean isFunctionSpaceEnabled) {
        // set any existing arrival dates to non-preferred date
        Set<GroupEvaluationArrivalDate> arrivalDates = new LinkedHashSet<>();
        GroupEvaluationArrivalDate arrivalDate = new GroupEvaluationArrivalDate();
        arrivalDate.setGroupEvaluation(groupEvaluation);
        arrivalDate.setArrivalDate(evaluationRequest.getArrivalDate(isFunctionSpaceEnabled));
        arrivalDate.setPreferredDate(true);
        arrivalDates.add(arrivalDate);

        return arrivalDates;
    }

    private Set<GroupEvaluationFunctionSpace> addFunctionSpaceEvents(GroupEvaluation groupEvaluation, EvaluationRequest evaluationRequest) {
        Set<GroupEvaluationFunctionSpace> groupEvaluationFunctionSpaces = new HashSet<>();
        Map<String, FunctionSpaceFunctionRoom> functionSpaceFunctionsRoomsMap = getFunctionSpaceFunctionRoomsMap();
        evaluationRequest.getEvaluationEvents().forEach(event -> {
            FunctionSpaceFunctionRoom functionRoom = functionSpaceFunctionsRoomsMap.get(event.getFunctionRoomId());
            // Only create events if the function room associated with the event has been marked as price inclusive
            if (functionRoom != null) {
                GroupEvaluationFunctionSpace functionSpace = new GroupEvaluationFunctionSpace();
                functionSpace.setGroupEvaluation(groupEvaluation);
                functionSpace.setStartTime(NGIConvertUtils.convert(event.getStartDate(), LocalDateTime.class));
                functionSpace.setEndTime(NGIConvertUtils.convert(event.getEndDate(), LocalDateTime.class));
                functionSpace.addGroupEvaluationFunctionSpaceFunctionRoom(addFunctionRoom(functionRoom, functionSpace));
                groupEvaluationFunctionSpaces.add(functionSpace);
            }
        });

        return groupEvaluationFunctionSpaces;
    }

    protected Map<String, FunctionSpaceFunctionRoom> getFunctionSpaceFunctionRoomsMap() {
        return getTbaAndNonIncludedForPricingRooms();
    }

    private Map<String, FunctionSpaceFunctionRoom> getPriceIncludedRoms() {
        List<FunctionSpaceFunctionRoom> priceIncludedFunctionRooms = functionSpaceConfigurationService.getAllActiveRoomsIncludedForPricing();
        return priceIncludedFunctionRooms.stream().collect(Collectors.toMap(FunctionSpaceFunctionRoom::getSalesCateringIdentifier, fr -> fr));
    }

    private Map<String, FunctionSpaceFunctionRoom> getTbaAndNonIncludedForPricingRooms() {
        List<FunctionSpaceFunctionRoom> priceIncludedFunctionRooms = functionSpaceConfigurationService.getAllTbaAndNonActiveRoomsIncludedForPricing();
        return priceIncludedFunctionRooms.stream().collect(Collectors.toMap(FunctionSpaceFunctionRoom::getSalesCateringIdentifier, fr -> fr));
    }

    private Set<GroupEvaluationFunctionSpaceConfAndBanq> addGroupEvaluationConferenceAndBanquets(GroupEvaluation groupEvaluation,
                                                                                                 EvaluationRequest evaluationRequest) {
        List<EvaluationRevenue> evaluationRevenues = getEvaluationRevenues(evaluationRequest);

        // Only create one GroupEvaluationFunctionSpaceConfAndBanq per configured revenue group i.e. Beverage, Food, etc.
        Map<String, GroupEvaluationFunctionSpaceConfAndBanq> confAndBanqs = buildFunctionSpaceConfAndBanqs(groupEvaluation);
        boolean shouldUseFSRevenueStreams = conferenceAndBanquetService.shouldUseFSRevenueStreams();
        if (isGroupPricingEnabled() && shouldUseFSRevenueStreams) {
            confAndBanqs = filterConfBanqIncludedInEvaluationRevenue(confAndBanqs, evaluationRevenues);
        }

        // For each revenue, update the total revenue for the revenue group
        if (genericSalesAndCateringEnabled()) {
            // make change to only return the revenue streams which are included in evaluation revenue
            populateFunctionSpaceConfAndBanquetRevenueAndCommissionPercentage(evaluationRevenues, confAndBanqs, groupEvaluation.getPropertyId());
        } else {
            for (EvaluationRevenue revenue : evaluationRevenues) {
                GroupEvaluationFunctionSpaceConfAndBanq confAndBanq = confAndBanqs.get(revenue.getRevenueGroup());
                if (confAndBanq == null) {
                    LOGGER.warn("Evaluation Request: No Revenue Group found for: " + revenue.getRevenueGroup() + " Property Id: " + groupEvaluation.getPropertyId());
                } else {
                    updateGroupEvaluationConfBanq(shouldUseFSRevenueStreams, revenue, confAndBanq);
                }
            }
        }

        return new HashSet<>(confAndBanqs.values());
    }

    private void updateGroupEvaluationConfBanq(boolean shouldUseFSRevenueStreams, EvaluationRevenue revenue, GroupEvaluationFunctionSpaceConfAndBanq confAndBanq) {
        if (isGroupPricingEnabled() && shouldUseFSRevenueStreams) {
            BigDecimal commissionPercentage = NGIConvertUtils.convert(revenue.getCommissionPct(), BigDecimal.class, BigDecimal.ZERO);
            confAndBanq.setCommissionPercentage(BigDecimalUtil.divide(commissionPercentage, BigDecimalUtil.ONE_HUNDRED));
        }
        confAndBanq.addRevenue(new BigDecimal(revenue.getRevenue()));
    }

    private List<EvaluationRevenue> getEvaluationRevenues(EvaluationRequest evaluationRequest) {
        List<EvaluationRevenue> evaluationRevenues = evaluationRequest.getEvaluationEvents().stream().flatMap(event -> event.getRevenues().stream()).collect(Collectors.toList());

        // Check for Other Booking Income Revenue - These will only be present from old Delphi
        if (CollectionUtils.isNotEmpty(evaluationRequest.getEvaluationRevenues())) {
            evaluationRevenues.addAll(evaluationRequest.getEvaluationRevenues());
        }
        return evaluationRevenues;
    }

    private Map<String, GroupEvaluationFunctionSpaceConfAndBanq> filterConfBanqIncludedInEvaluationRevenue(Map<String, GroupEvaluationFunctionSpaceConfAndBanq> confAndBanqs,
                                                                                                           List<EvaluationRevenue> evaluationRevenues) {
        Map<String, GroupEvaluationFunctionSpaceConfAndBanq> evaluationConferenceAndBanquetMap = new HashMap<>();
        evaluationRevenues.forEach(revenue -> {
            GroupEvaluationFunctionSpaceConfAndBanq confAndBanq = confAndBanqs.get(revenue.getRevenueGroup());
            if (Objects.nonNull(confAndBanq)) {
                evaluationConferenceAndBanquetMap.put(revenue.getRevenueGroup(), confAndBanq);
            }
        });

        return evaluationConferenceAndBanquetMap;
    }

    private boolean isGroupPricingEnabled() {
        return configParamsService.getBooleanParameterValue(GROUP_PRICING_ENABLED);
    }

    private void populateFunctionSpaceConfAndBanquetRevenueAndCommissionPercentage(List<EvaluationRevenue> evaluationRevenues, Map<String, GroupEvaluationFunctionSpaceConfAndBanq> confAndBanquetByRevenueGroup, Integer propertyId) {
        Map<String, BigDecimal> totalCommissionByRevenueGroup = new HashMap<>();

        evaluationRevenues.forEach(evaluationRevenue -> {
            final String revenueGroup = evaluationRevenue.getRevenueGroup();
            GroupEvaluationFunctionSpaceConfAndBanq confAndBanquet = confAndBanquetByRevenueGroup.get(revenueGroup);

            if (confAndBanquet == null) {
                LOGGER.warn(String.format("Evaluation Request: No Revenue Group found for: %s for Property Id: %s", revenueGroup, propertyId));
            } else {
                final BigDecimal revenue = NGIConvertUtils.convert(evaluationRevenue.getRevenue(), BigDecimal.class, BigDecimal.ZERO);
                confAndBanquet.addRevenue(revenue);

                totalCommissionByRevenueGroup.compute(revenueGroup, (key, commission) -> calculateAndAddCommission(evaluationRevenue.getCommissionPct(), revenue, commission));
            }
        });

        setFunctionSpaceConfAndBanquetCommissionPercentage(confAndBanquetByRevenueGroup, totalCommissionByRevenueGroup);
    }


    /**
     * The commission percentage value will be the weighted average value if the evaluation request has multiple revenues with same revenue group name.
     */
    private void setFunctionSpaceConfAndBanquetCommissionPercentage(Map<String, GroupEvaluationFunctionSpaceConfAndBanq> confAndBanquetByRevenueGroup, Map<String, BigDecimal> totalCommissionByRevenueGroup) {
        totalCommissionByRevenueGroup.forEach((revenueGroupName, totalCommission) -> {
            GroupEvaluationFunctionSpaceConfAndBanq functionSpaceConfAndBanquet = confAndBanquetByRevenueGroup.get(revenueGroupName);
            if (nonNull(functionSpaceConfAndBanquet)) {
                //Setting commission percentage in decimal form as per the logic written at view side to convert it into percentage. Example (15% as 0.15).
                functionSpaceConfAndBanquet.setCommissionPercentage(calculateCommissionPercentageInDecimal(totalCommission, functionSpaceConfAndBanquet.getRevenue()));
            }
        });
    }

    /**
     * Calculate commission percentage in decimal form. Kept the scale value as 4 to avoid rounding of the value upto 2 decimal places.
     */
    private BigDecimal calculateCommissionPercentageInDecimal(BigDecimal commission, BigDecimal revenue) {
        return BigDecimalUtil.divide(commission, revenue, 4);
    }

    private BigDecimal calculateAndAddCommission(Double commissionPercentage, BigDecimal revenue, BigDecimal commission) {
        if (isNull(commission)) {
            commission = BigDecimal.ZERO;
        }
        final BigDecimal commissionPctInDecimal = convertPercentageToDecimal(commissionPercentage);
        final BigDecimal commissionToAdd = BigDecimalUtil.multiply(revenue, commissionPctInDecimal);
        return BigDecimalUtil.add(commission, commissionToAdd);
    }

    private BigDecimal convertPercentageToDecimal(Double commissionPercentage) {
        final BigDecimal commissionPct = NGIConvertUtils.convert(commissionPercentage, BigDecimal.class, BigDecimal.ZERO);
        return BigDecimalUtil.equals(BigDecimal.ZERO, commissionPct) ? commissionPct : BigDecimalUtil.divide(commissionPct, new BigDecimal(100));
    }

    private Map<String, GroupEvaluationFunctionSpaceConfAndBanq> buildFunctionSpaceConfAndBanqs(GroupEvaluation groupEvaluation) {
        List<FunctionSpaceRevenueGroup> revenueGroups = functionSpaceConfigurationService.getActiveRevenueGroups();
        return revenueGroups.stream().collect(Collectors.toMap(FunctionSpaceRevenueGroup::getName, revenueGroup -> {
            GroupEvaluationFunctionSpaceConfAndBanq confAndBanq = new GroupEvaluationFunctionSpaceConfAndBanq();
            confAndBanq.setGroupEvaluation(groupEvaluation);
            confAndBanq.setFunctionSpaceRevenueGroup(revenueGroup);
            confAndBanq.setRevenue(BigDecimal.ZERO);
            confAndBanq.setProfitPercentage(BigDecimalUtil.multiply(revenueGroup.getProfitPercent(), BigDecimalUtil.ONE_HUNDRED));
            return confAndBanq;
        }));
    }

    private Set<GroupEvaluationConferenceAndBanquet> addConferenceAndBanquets(GroupEvaluation groupEvaluation, EvaluationRequest evaluationRequest) {
        List<EvaluationRevenue> evaluationRevenues = getEvaluationRevenues(evaluationRequest);

        // Only create one GroupPricingConfigurationConferenceAndBanquet per configured revenue group i.e. Beverage, Food, etc.
        Map<String, GroupPricingConfigurationConferenceAndBanquet> configurationConferenceAndBanquetMap = buildGroupEvaluationConferenceAndBanquet();
        Map<String, GroupEvaluationConferenceAndBanquet> evaluationConferenceAndBanquetMap = new HashMap<>();

        // For each revenue, update the total revenue for the revenue group
        if (genericSalesAndCateringEnabled()) {
            populateConfAndBanquetRevenueAndCommissionPercentage(evaluationRevenues, configurationConferenceAndBanquetMap, evaluationConferenceAndBanquetMap, groupEvaluation.getPropertyId());
        } else {
            evaluationRevenues.forEach(revenue -> {
                GroupPricingConfigurationConferenceAndBanquet confAndBanq = configurationConferenceAndBanquetMap.get(revenue.getRevenueGroup());
                if (confAndBanq == null) {
                    LOGGER.warn("Evaluation Request: No Revenue Group found for: " + revenue.getRevenueGroup() + " Property Id: " + groupEvaluation.getPropertyId());
                } else {
                    GroupEvaluationConferenceAndBanquet evaluationConferenceAndBanquet = evaluationConferenceAndBanquetMap.computeIfAbsent(revenue.getRevenueGroup(), evalConfAnBanq -> new GroupEvaluationConferenceAndBanquet());
                    evaluationConferenceAndBanquet.setGroupPricingConfigurationConferenceAndBanquet(configurationConferenceAndBanquetMap.get(revenue.getRevenueGroup()));
                    evaluationConferenceAndBanquet.setCommissionPercentage(NGIConvertUtils.convert(revenue.getCommissionPct(), BigDecimal.class, BigDecimal.ZERO));
                    evaluationConferenceAndBanquet.addRevenue(NGIConvertUtils.convert(revenue.getRevenue(), BigDecimal.class, BigDecimal.ZERO));
                    evaluationConferenceAndBanquetMap.put(revenue.getRevenueGroup(), evaluationConferenceAndBanquet);
                }
            });
        }

        return new HashSet<>(evaluationConferenceAndBanquetMap.values());
    }

    private void populateConfAndBanquetRevenueAndCommissionPercentage(List<EvaluationRevenue> evaluationRevenues, Map<String, GroupPricingConfigurationConferenceAndBanquet> configurationConfAndBanquetByRevenueGroup, Map<String, GroupEvaluationConferenceAndBanquet> evaluationConfAndBanquetByRevenueGroup, Integer propertyId) {
        Map<String, BigDecimal> totalCommissionByRevenueGroup = new HashMap<>();
        evaluationRevenues.forEach(evaluationRevenue -> {
            final String revenueGroup = evaluationRevenue.getRevenueGroup();
            GroupPricingConfigurationConferenceAndBanquet configurationConfAndBanquet = configurationConfAndBanquetByRevenueGroup.get(revenueGroup);

            if (isNull(configurationConfAndBanquet)) {
                LOGGER.warn(String.format("Evaluation Request: No Revenue Group found for: %s for Property Id: %s", revenueGroup, propertyId));
            } else {
                GroupEvaluationConferenceAndBanquet evaluationConfAndBanquet = evaluationConfAndBanquetByRevenueGroup.computeIfAbsent(revenueGroup, evalConfAndBanquet -> new GroupEvaluationConferenceAndBanquet());
                evaluationConfAndBanquet.setGroupPricingConfigurationConferenceAndBanquet(configurationConfAndBanquet);

                final BigDecimal revenue = NGIConvertUtils.convert(evaluationRevenue.getRevenue(), BigDecimal.class, BigDecimal.ZERO);
                evaluationConfAndBanquet.addRevenue(revenue);

                totalCommissionByRevenueGroup.compute(revenueGroup, (key, commission) -> calculateAndAddCommission(evaluationRevenue.getCommissionPct(), revenue, commission));
                evaluationConfAndBanquet.setProfitPercentage(configurationConfAndBanquet.getProfitPercentage());
            }
        });

        setConfAndBanquetCommissionPercentage(evaluationConfAndBanquetByRevenueGroup, totalCommissionByRevenueGroup);
    }

    /**
     * The commission percentage value will be the weighted average value if the evaluation request has multiple revenues with same revenue group name.
     */
    private void setConfAndBanquetCommissionPercentage(Map<String, GroupEvaluationConferenceAndBanquet> confAndBanquetByRevenueGroup, Map<String, BigDecimal> totalCommissionByRevenueGroup) {
        totalCommissionByRevenueGroup.forEach((revenueGroupName, totalCommission) -> {
            GroupEvaluationConferenceAndBanquet groupEvaluationConferenceAndBanquet = confAndBanquetByRevenueGroup.get(revenueGroupName);
            if (nonNull(groupEvaluationConferenceAndBanquet)) {
                final BigDecimal commissionPercentage = calculateCommissionPercentage(totalCommission, groupEvaluationConferenceAndBanquet.getRevenue());
                groupEvaluationConferenceAndBanquet.setCommissionPercentage(commissionPercentage);
            }
        });
    }

    private BigDecimal calculateCommissionPercentage(BigDecimal commission, BigDecimal revenue) {
        final BigDecimal commissionPctInDecimal = calculateCommissionPercentageInDecimal(commission, revenue);
        return BigDecimalUtil.multiply(commissionPctInDecimal, new BigDecimal(100));
    }

    private Map<String, GroupPricingConfigurationConferenceAndBanquet> buildGroupEvaluationConferenceAndBanquet() {
        List<GroupPricingConfigurationConferenceAndBanquet> cbConfigs = groupPricingConfigurationConferenceAndBanquetService.getGroupPricingConfigurationConferenceAndBanquets();

        Map<String, GroupPricingConfigurationConferenceAndBanquet> evaluationConferenceAndBanquetMap = new HashMap<>();

        cbConfigs.forEach(config -> {
            GroupEvaluationConferenceAndBanquet groupEvaluationConferenceAndBanquet = new GroupEvaluationConferenceAndBanquet();
            groupEvaluationConferenceAndBanquet.setGroupPricingConfigurationConferenceAndBanquet(config);

            evaluationConferenceAndBanquetMap.put(config.getRevenueStream(), config);
        });

        return evaluationConferenceAndBanquetMap;
    }

    private Set<GroupEvaluationDayOfStay> addDayOfStaysForROH(GroupEvaluation groupEvaluation, EvaluationRequest
            evaluationRequest, boolean isFunctionSpaceEnabled) {
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.ROH);
        Map<LocalDate, GroupEvaluationDayOfStay> uniqueDayOfStays = new HashMap<>();
        LocalDate arrivalDate = groupEvaluation.getPreferredDate();
        Set<GroupEvaluationDayOfStay> groupEvaluationDayOfStays = evaluationRequest.getRoomNightsSortedByDate(false).stream().map(roomNight -> {
            GroupEvaluationDayOfStay dayOfStay;
            if (uniqueDayOfStays.containsKey(roomNight.getLocalDateForOccupancyDate())) {
                dayOfStay = uniqueDayOfStays.get(roomNight.getLocalDateForOccupancyDate());
                dayOfStay.setNumberOfRooms(dayOfStay.getNumberOfRooms() + roomNight.getNumberOfRooms());
            } else {
                dayOfStay = new GroupEvaluationDayOfStay();
                populateGroupEvaluationDayOfStay(groupEvaluation, dayOfStay, arrivalDate, roomNight.getLocalDateForOccupancyDate(), roomNight.getNumberOfRooms());
                uniqueDayOfStays.put(roomNight.getLocalDateForOccupancyDate(), dayOfStay);
            }
            return dayOfStay;
        }).collect(Collectors.toSet());

        if (!uniqueDayOfStays.isEmpty()) {
            LocalDate departureDate = evaluationRequest.getDepartureDate(isFunctionSpaceEnabled);
            // adding 1 to since the api is not inclusive
            int numberOfNights = Days.daysBetween(arrivalDate, departureDate).getDays() + 1;
            for (int i = 0; i < numberOfNights; i++) {
                //create GroupEvaluationDayOfStay record with 0 numberOfRooms to fill groupEvaluationDayOfStays with missing dates
                LocalDate occupancyDate = arrivalDate.plusDays(i);
                if (!uniqueDayOfStays.containsKey(occupancyDate)) {
                    GroupEvaluationDayOfStay groupEvaluationDayOfStay = new GroupEvaluationDayOfStay();
                    populateGroupEvaluationDayOfStay(groupEvaluation, groupEvaluationDayOfStay, arrivalDate, occupancyDate, 0);
                    groupEvaluationDayOfStays.add(groupEvaluationDayOfStay);
                }
            }
        }
        return groupEvaluationDayOfStays;
    }

    private void populateGroupEvaluationDayOfStay(GroupEvaluation groupEvaluation, GroupEvaluationDayOfStay groupEvaluationDayOfStay, LocalDate arrivalDate, LocalDate occupancyDate, Integer numberOfRooms) {
        groupEvaluationDayOfStay.setDayOfStay(Days.daysBetween(arrivalDate, occupancyDate).getDays() + 1);
        groupEvaluationDayOfStay.setGroupEvaluation(groupEvaluation);
        groupEvaluationDayOfStay.setNumberOfRooms(numberOfRooms);
    }

    private Set<GroupEvaluationCost> addCosts(GroupEvaluation groupEvaluation, EvaluationCost evaluationCost) {
        Set<GroupEvaluationCost> evaluationCosts = new HashSet<>();

        if (evaluationCost != null) {
            addComplimentaryCost(groupEvaluation, evaluationCost, evaluationCosts);
            addCommissionCost(groupEvaluation, evaluationCost, evaluationCosts);

            if (genericSalesAndCateringEnabled()) {
                addDiscountedCost(groupEvaluation, evaluationCost, evaluationCosts);
                addRebatesCost(groupEvaluation, evaluationCost, evaluationCosts);
                addOtherConcessionsCost(groupEvaluation, evaluationCost, evaluationCosts);
            }
        }

        return evaluationCosts;
    }

    private void addComplimentaryCost(GroupEvaluation groupEvaluation, EvaluationCost evaluationCost, Set<GroupEvaluationCost> evaluationCosts) {
        if (shouldAddComplimentaryCost(evaluationCost)) {
            GroupEvaluationCost compedRooms = new GroupEvaluationCost();
            compedRooms.setGroupEvaluation(groupEvaluation);
            compedRooms.setGroupEvaluationCostType(GroupEvaluationCostType.COMPLIMENTARY_FIXED);
            compedRooms.setTotal(evaluationCost.getCompedRooms());
            evaluationCosts.add(compedRooms);
        }
    }

    private boolean shouldAddComplimentaryCost(EvaluationCost evaluationCost) {
        return nonNull(evaluationCost.getCompedRooms()) && evaluationCost.getCompedRooms() > 0;
    }

    private void addCommissionCost(GroupEvaluation groupEvaluation, EvaluationCost evaluationCost, Set<GroupEvaluationCost> evaluationCosts) {
        if (shouldAddCommissionCost(evaluationCost)) {
            GroupEvaluationCost commission = new GroupEvaluationCost();
            commission.setGroupEvaluation(groupEvaluation);
            commission.setGroupEvaluationCostType(GroupEvaluationCostType.COMMISSION_FIXED);
            commission.setPercentage(NGIConvertUtils.convert(evaluationCost.getCommissionPct(), BigDecimal.class));
            evaluationCosts.add(commission);
        }
    }

    private boolean shouldAddCommissionCost(EvaluationCost evaluationCost) {
        return nonNull(evaluationCost.getCommissionPct()) && evaluationCost.getCommissionPct() > 0;
    }

    private void addDiscountedCost(GroupEvaluation groupEvaluation, EvaluationCost evaluationCost, Set<GroupEvaluationCost> evaluationCosts) {
        if (shouldAddDiscountedCost(evaluationCost)) {
            GroupEvaluationCost discountedCost = new GroupEvaluationCost();
            discountedCost.setGroupEvaluation(groupEvaluation);
            discountedCost.setGroupEvaluationCostType(GroupEvaluationCostType.DISCOUNTED_FIXED);
            discountedCost.setTotal(evaluationCost.getNumberOfDiscountedRooms());
            if (isValueTypePercentage(evaluationCost.getDiscountValueType())) {
                discountedCost.setPercentage(NGIConvertUtils.convert(evaluationCost.getDiscountPerRoom(), BigDecimal.class));
            } else {
                discountedCost.setCost(NGIConvertUtils.convert(evaluationCost.getDiscountPerRoom(), BigDecimal.class));
            }

            evaluationCosts.add(discountedCost);
        }
    }

    private boolean shouldAddDiscountedCost(EvaluationCost evaluationCost) {
        return nonNull(evaluationCost.getNumberOfDiscountedRooms()) && nonNull(evaluationCost.getDiscountPerRoom()) && nonNull(evaluationCost.getDiscountValueType());
    }

    private void addRebatesCost(GroupEvaluation groupEvaluation, EvaluationCost evaluationCost, Set<GroupEvaluationCost> evaluationCosts) {
        if (shouldAddRebatesCost(evaluationCost)) {
            GroupEvaluationCost rebatesCost = new GroupEvaluationCost();
            rebatesCost.setGroupEvaluation(groupEvaluation);
            rebatesCost.setGroupEvaluationCostType(GroupEvaluationCostType.REBATES_FIXED);
            rebatesCost.setTotal(evaluationCost.getNumberOfRoomsWithRebate());
            if (isValueTypePercentage(evaluationCost.getRebateValueType())) {
                rebatesCost.setPercentage(NGIConvertUtils.convert(evaluationCost.getRebatePerRoom(), BigDecimal.class));
            } else {
                rebatesCost.setCost(NGIConvertUtils.convert(evaluationCost.getRebatePerRoom(), BigDecimal.class));
            }

            evaluationCosts.add(rebatesCost);

        }
    }

    private boolean shouldAddRebatesCost(EvaluationCost evaluationCost) {
        return nonNull(evaluationCost.getNumberOfRoomsWithRebate()) && nonNull(evaluationCost.getRebatePerRoom()) && nonNull(evaluationCost.getRebateValueType());
    }

    private boolean isValueTypePercentage(EvaluationCost.ValueTypeEnum rebateValueType) {
        return EvaluationCost.ValueTypeEnum.PERCENTAGE.equals(rebateValueType);
    }

    private void addOtherConcessionsCost(GroupEvaluation groupEvaluation, EvaluationCost evaluationCost, Set<GroupEvaluationCost> evaluationCosts) {
        if (shouldAddOtherConcessionsCost(evaluationCost)) {
            GroupEvaluationCost otherConcessionsCost = new GroupEvaluationCost();
            otherConcessionsCost.setGroupEvaluation(groupEvaluation);
            otherConcessionsCost.setGroupEvaluationCostType(GroupEvaluationCostType.OTHER_CONCESSIONS_FIXED);
            otherConcessionsCost.setTotal(evaluationCost.getNumberOfRoomsWithOtherConcession());
            otherConcessionsCost.setCost(NGIConvertUtils.convert(evaluationCost.getOtherConcessionPerRoom(), BigDecimal.class));

            evaluationCosts.add(otherConcessionsCost);

        }
    }

    private boolean shouldAddOtherConcessionsCost(EvaluationCost evaluationCost) {
        return nonNull(evaluationCost.getNumberOfRoomsWithOtherConcession()) && nonNull(evaluationCost.getOtherConcessionPerRoom());
    }

    private Set<GroupEvaluationRoomType> addRoomTypeNights(GroupEvaluation groupEvaluation, EvaluationRequest
            evaluationRequest, boolean isFunctionSpaceEnabled) {
        groupEvaluation.setEvaluationMethod(GroupPricingEvaluationMethod.RC);
        LocalDate arrivalDate = groupEvaluation.getPreferredDate();
        LocalDate departureDate = evaluationRequest.getDepartureDate(isFunctionSpaceEnabled);
        // adding 1 to since the api is not inclusive
        int numberOfNights = Days.daysBetween(arrivalDate, departureDate).getDays() + 1;
        final Map<Integer, GroupEvaluationRoomType> accomTypeIdGroupEvaluationRoomTypeMap = new HashMap<>();
        Map<Integer, AccomType> accomTypeIdAccomTypeMapping = new HashMap<>();

        final Set<GroupEvaluationRoomType> groupEvaluationRoomTypes = evaluationRequest.buildUniqueListByRoomType().stream()
                .map(roomType -> getGroupEvaluationRoomType(groupEvaluation, evaluationRequest, accomTypeIdGroupEvaluationRoomTypeMap, accomTypeIdAccomTypeMapping, roomType))
                .filter(Objects::nonNull).collect(Collectors.toSet());

        getMissingGroupEvaluationRoomTypeDayOfStays(arrivalDate, numberOfNights, groupEvaluationRoomTypes);
        return groupEvaluationRoomTypes;
    }

    private void getMissingGroupEvaluationRoomTypeDayOfStays(LocalDate arrivalDate, int numberOfNights, Set<GroupEvaluationRoomType> groupEvaluationRoomTypes) {
        groupEvaluationRoomTypes.forEach(groupEvaluationRoomType -> {
            List<GroupEvaluationRoomTypeDayOfStay> missingGroupEvaluationRoomTypeDayOfStays = new ArrayList<>();
            groupEvaluationRoomType.getGroupEvaluationRoomTypeDayOfStays().forEach(groupEvaluationRoomTypeDayOfStay -> {
                for (int i = 0; i < numberOfNights; i++) {
                    final LocalDate occupancyDate = arrivalDate.plusDays(i);
                    if (!Objects.equals(groupEvaluationRoomTypeDayOfStay.getDateForDayOfStay(), occupancyDate)) {
                        GroupEvaluationRoomTypeDayOfStay groupEvaluationRoomTypeDayOfStay1 = new GroupEvaluationRoomTypeDayOfStay();
                        groupEvaluationRoomTypeDayOfStay1.setDayOfStay(i + 1);
                        groupEvaluationRoomTypeDayOfStay1.setNumberOfRooms(0);
                        groupEvaluationRoomTypeDayOfStay1.setGroupEvaluationRoomType(groupEvaluationRoomType);
                        missingGroupEvaluationRoomTypeDayOfStays.add(groupEvaluationRoomTypeDayOfStay1);
                    }
                }
            });
            groupEvaluationRoomType.getGroupEvaluationRoomTypeDayOfStays().addAll(missingGroupEvaluationRoomTypeDayOfStays);
        });
    }

    private GroupEvaluationRoomType getGroupEvaluationRoomType(GroupEvaluation groupEvaluation, EvaluationRequest evaluationRequest, Map<Integer, GroupEvaluationRoomType> accomTypeIdGroupEvaluationRoomTypeMap, Map<Integer, AccomType> accomTypeIdAccomTypeMapping, EvaluationRoomNight roomType) {
        GroupEvaluationRoomType groupEvalRoomType = findOrCreateGroupEvaluationRoomType(groupEvaluation, accomTypeIdGroupEvaluationRoomTypeMap, accomTypeIdAccomTypeMapping, roomType);
        getRoomNightsForRoomType(roomType.getRoomType(), evaluationRequest.getRoomNightsSortedByDate(false)).forEach(evaluationRoomNight -> {
            Integer dayOfStay = Days.daysBetween(groupEvalRoomType.getGroupEvaluation().getPreferredDate(), evaluationRoomNight.getLocalDateForOccupancyDate()).getDays() + 1;
            groupEvalRoomType.getGroupEvaluationRoomTypeDayOfStays().stream()
                    .filter(groupEvaluationRoomTypeDayOfStay -> Objects.equals(groupEvaluationRoomTypeDayOfStay.getDayOfStay(), dayOfStay))
                    .findFirst().map(groupEvaluationRoomTypeDayOfStay -> updateGroupEvaluationRoomTypeDayOfStayRoomNights(evaluationRoomNight, groupEvaluationRoomTypeDayOfStay)).orElseGet(() -> getGroupEvaluationRoomTypeDayOfStay(groupEvalRoomType, evaluationRoomNight, dayOfStay));
        });
        return groupEvalRoomType;
    }

    private GroupEvaluationRoomTypeDayOfStay updateGroupEvaluationRoomTypeDayOfStayRoomNights(EvaluationRoomNight evaluationRoomNight, GroupEvaluationRoomTypeDayOfStay groupEvaluationRoomTypeDayOfStay) {
        groupEvaluationRoomTypeDayOfStay.setNumberOfRooms(groupEvaluationRoomTypeDayOfStay.getNumberOfRooms() + evaluationRoomNight.getNumberOfRooms());
        return groupEvaluationRoomTypeDayOfStay;
    }

    private GroupEvaluationRoomTypeDayOfStay getGroupEvaluationRoomTypeDayOfStay(GroupEvaluationRoomType groupEvalRoomType, EvaluationRoomNight evaluationRoomNight, Integer dayOfStay) {
        GroupEvaluationRoomTypeDayOfStay groupEvaluationRoomTypeDayOfStay = new GroupEvaluationRoomTypeDayOfStay();
        groupEvaluationRoomTypeDayOfStay.setNumberOfRooms(evaluationRoomNight.getNumberOfRooms());
        groupEvaluationRoomTypeDayOfStay.setDayOfStay(dayOfStay);
        groupEvalRoomType.addGroupEvaluationRoomTypeDayOfStay(groupEvaluationRoomTypeDayOfStay);
        return groupEvaluationRoomTypeDayOfStay;
    }

    private GroupEvaluationRoomType findOrCreateGroupEvaluationRoomType(GroupEvaluation groupEvaluation, Map<Integer, GroupEvaluationRoomType> accomTypeIdGroupEvaluationRoomTypeMap, Map<Integer, AccomType> accomTypeIdAccomTypeMapping, EvaluationRoomNight roomType) {
        AccomType roomTypeFromDatabase = getRoomType(roomType.getRoomType(), accomTypeIdAccomTypeMapping);
        return accomTypeIdGroupEvaluationRoomTypeMap.computeIfAbsent(roomTypeFromDatabase.getId(), accomTypeId -> {
            GroupEvaluationRoomType groupEvaluationRoomType = new GroupEvaluationRoomType();
            groupEvaluationRoomType.setGroupEvaluation(groupEvaluation);
            groupEvaluationRoomType.setRoomType(roomTypeFromDatabase);
            return groupEvaluationRoomType;
        });
    }

    private GroupEvaluationFunctionSpaceFunctionRoom addFunctionRoom(FunctionSpaceFunctionRoom functionRoom, GroupEvaluationFunctionSpace functionSpace) {
        GroupEvaluationFunctionSpaceFunctionRoom functionSpaceFunctionRoom = new GroupEvaluationFunctionSpaceFunctionRoom();
        functionSpaceFunctionRoom.setGroupEvaluationFunctionSpace(functionSpace);
        functionSpaceFunctionRoom.setFunctionSpaceFunctionRoom(functionRoom);
        return functionSpaceFunctionRoom;
    }

    private List<EvaluationRoomNight> getRoomNightsForRoomType(String roomType, List<EvaluationRoomNight> evaluationRoomNights) {
        return evaluationRoomNights.stream().filter(roomNight -> roomNight.getRoomType().equals(roomType)).collect(Collectors.toList());
    }

    public AccomType getRoomType(String roomTypeCode, Map<Integer, AccomType> accomTypeIdAccomTypeMapping) {
        FunctionSpaceGuestRoomCategory fsRoomCategory = tenantCrudService.findByNamedQuerySingleResult(FunctionSpaceGuestRoomCategory.FIND_BY_ROOM_CATEGORY, QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and("roomCategory", roomTypeCode).parameters());
        AccomType accomType = fsRoomCategory != null ? accomTypeIdAccomTypeMapping.computeIfAbsent(fsRoomCategory.getAccomTypeId(), accomTypeId -> getAccomType(fsRoomCategory.getAccomTypeId())) : null;

        if (accomType != null) {
            return accomType;
        } else {
            LOGGER.error("Evaluation Request: No corresponding Room Type found for: " + roomTypeCode);
            throw new TetrisException(GROUP_EVALUATION_SALES_AND_CATERING_POPULATION_FAILED_NO_CORRESPONDING_ROOM_TYPE_FOUND, "No corresponding Room Type found for: " + roomTypeCode, null, roomTypeCode);
        }
    }

    private AccomType getAccomType(Integer accomTypeId) {
        return tenantCrudService.find(AccomType.class, accomTypeId);
    }

    private MarketSegmentSummary getMarketSegment(String marketSegment) {
        FunctionSpaceMarketSegment fsMktSeg = tenantCrudService.findByNamedQuerySingleResult(FunctionSpaceMarketSegment.FIND_BY_ABBREVIATION, QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and("abbreviation", marketSegment).parameters());
        return fsMktSeg != null ? tenantCrudService.find(MarketSegmentSummary.class, fsMktSeg.getMarketSegmentId()) : null;
    }

    public EvaluationRequest retrieveEvaluationRequest(String clientCode, String propertyCode, String evaluationId) {
        Map<String, String> parameters = new HashMap<>();
        parameters.put(CLIENT_CODE, clientCode);
        parameters.put(PROPERTY_CODE, propertyCode);
        parameters.put(EVALUATION_ID, evaluationId);
        String evaluationJson = null;
        try {
            evaluationJson = getEvaluationRequestData(parameters);
            long startMillis = System.currentTimeMillis();
            long elapsedMillis;
            // If no data received, retry for up to a minute
            while (StringUtils.isEmpty(evaluationJson)) {
                elapsedMillis = System.currentTimeMillis() - startMillis;
                if (elapsedMillis > TIMEOUT_MILLIS) {
                    break;
                }
                sleep(MILLIS_BETWEEN_RETRIES);
                evaluationJson = getEvaluationRequestData(parameters);
            }
            return new ObjectMapper().readValue(evaluationJson, EvaluationRequest.class);
        } catch (Exception e) {
            LOGGER.error(String.format("An Error occurred while loading the evaluation request for evaluation id: %s. The evaluationJson is:  %s", evaluationId, evaluationJson) + evaluationId, e);
            throw new TetrisException(GROUP_EVALUATION_SALES_AND_CATERING_POPULATION_FAILED, "An unexpected error has occurred trying to load evaluation data:", e, evaluationJson);
        }
    }

    protected String getEvaluationRequestData(Map<String, String> parameters) {
        if (genericSalesAndCateringEnabled()) {
            return getSalesAndCateringEvaluationRequestData(parameters);
        }
        return getAHWSEvaluationRequestData(parameters);
    }

    private String getAHWSEvaluationRequestData(Map<String, String> parameters) {
        LOGGER.info("Get AHWS Evaluation Request Data for: " + parameters);
        return restClient.getJsonFromEndpoint(RestEndpoints.AHWS_BOOKING_FOR_EVALUATION, parameters, 0);
    }

    private String getSalesAndCateringEvaluationRequestData(Map<String, String> parameters) {
        LOGGER.info("Get Sales And Catering Evaluation Request Data for: " + parameters);
        boolean shouldUpdateClientCodePropertyCodeBeforeCallingNgi = !configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SKIP_CLIENT_PROPERTY_CODE_TRANSLATION_FOR_TEST_PROPERTY);
        return restClient.getJsonFromEndpoint(RestEndpoints.SALES_AND_CATERING_NGI_GET_BOOKING_EVALUATION, parameters, 0, shouldUpdateClientCodePropertyCodeBeforeCallingNgi);
    }

    private boolean genericSalesAndCateringEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GENERIC_SALES_AND_CATERING_ENABLED);
    }

    private boolean isFunctionSpaceEnabled() {
        return configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value());
    }

    public LocalDate getEvaluationRequestArrivalDate(EvaluationRequest evaluationRequest) {
        return evaluationRequest.getArrivalDate(isFunctionSpaceEnabled());
    }
}