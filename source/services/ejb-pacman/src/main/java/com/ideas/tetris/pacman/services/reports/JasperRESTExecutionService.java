package com.ideas.tetris.pacman.services.reports;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.infra.tetris.security.domain.LDAPUser;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.email.EmailService;
import com.ideas.tetris.pacman.services.ftp.FtpService;
import com.ideas.tetris.pacman.services.reports.entity.ScheduleReport;
import com.ideas.tetris.pacman.services.reports.userreport.ScheduledReportAuditExecutionType;
import com.ideas.tetris.pacman.services.scheduledreport.resource.ResourceUtil;
import com.ideas.tetris.pacman.services.security.AuthorizationService;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.pacman.services.sftp.SFTPDetails;
import com.ideas.tetris.pacman.services.sftp.SSHJSftpService;
import com.ideas.tetris.pacman.services.sftp.SftpService;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompetitors;
import com.ideas.tetris.pacman.services.webrate.service.WebrateShoppingDataService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.ScheduleReportExceptionStepMessage;
import com.ideas.tetris.platform.common.errorhandling.ScheduledReportException;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.logging.LoggingContext;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.reports.ReportSource;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.core.Response;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.function.Predicate;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName.USE_SSHJ_SFTP_LIBRARY_FOR_SCHEDULED_REPORT;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.DEFAULT_DATE_FORMAT_FOR_SCHEDULED_REPORTS;
import static com.ideas.tetris.platform.common.utils.systemconfig.SystemConfig.getReportSchedulerMailSenderFrom;
import static com.ideas.tetris.platform.reports.jasperreports.constants.ReportsConstants.IS_IGNORE_PAGINATION;
import static com.ideas.tetris.platform.reports.jasperreports.constants.ReportsConstants.OUTPUT;
import static com.ideas.tetris.platform.reports.jasperreports.constants.ReportsConstants.REPORT_UNIT;
import static com.ideas.tetris.platform.reports.jasperreports.constants.ReportsConstants.SCHEDULE_PARAM_SEPARATOR;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class JasperRESTExecutionService {
    @Autowired
    ReportService reportService;

    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	private CrudService globalCrudService;

    @Autowired
	private EmailService emailService;

    @Autowired
	private FtpService ftpService;

    @Autowired
	private SftpService sftpService;

    @Autowired
	private SSHJSftpService sshjSftpService;

    @Autowired
    DateService dateService;

    @Autowired
    ScheduledReportAuditService scheduledReportAuditService;

    @Autowired
    PacmanConfigParamsService configParamsService;

    @Autowired
    ReportsG3Service reportsG3Service;

    @Autowired
    WebrateShoppingDataService webrateShoppingDataService;

    @Autowired
    AuthorizationService authorizationService;

    @Autowired
    UserService userService;

    private static final Logger LOGGER = Logger.getLogger(JasperRESTExecutionService.class.getName());
    private static final String PATTERN = "yyyyMMddHHmm";
    private static final String DEFAULT_DATE = "1970010100000";
    private static final String DEFAULT_DATE_FOR_REPORTS = "19700101";
    private static final String FTP_TYPE = "FTP";
    private static final String SFTP_TYPE = "SFTP";
    private static final String EMAIL = "EMAIL";
    private static final String YYYY_MM_DD = "yyyyMMdd";
    private static final String COMP = "Comp";
    private static final String COMP_LIST = "compList";
    private static final String TRUE = "true";
    private static final String REPORT_PARAM_VALUE_SEPERATOR = "=";
    private static final String INACTIVE_COMP_REPLACEMENT_VALUE = "-1";
    private static final String REPORT_UNIT_BASE = "/public/Reports/";
    private static final String EXECUTION_STATE_STARTED = "Started execution for report: %s";
    private static final String EXECUTION_STATE_COMPLETED = "Successfully completed execution for report: %s";
    private static final String EXECUTION_STATE_ERROR = "Error in execution for report: %s";
    private static final String UNAUTHORIZED_USER_EMAIL_REMOVED_MESSGAE = "Unauthorized User emails : %s removed from report : %s ";
    private static final String NEW_LINE_SEPARATOR = System.lineSeparator();

    private static final List<String> DATE_PARAM_NAMES = Arrays.asList(new String[]{
            "param_EndDate", "param_StartDate", "EndDate", "StartDate", "param_Business_StartDate", "param_Business_EndDate",
            "param_ComparisonStartDate", "param_ComparisonAsOfDate", "param_ComparisonEndDate",
            "param_AnalysisAsOfDate", "param_AnalysisStartDate", "param_AnalysisEndDate"});

    private static final List<String> COMP_PARAM_NAMES = Arrays.asList("Comp1", "Comp2", "Comp3", "Comp4", "Comp5");

    public void handleExceptionStates(Date businessDate, ScheduleReport scheduleReport, ScheduledReportException sre, ScheduledJobExceptionState systemConfiguredExceptionState) {
        LOGGER.error("Exception details : " + sre.getMessage() + " " + sre.getErrorCode() + " " + sre);
        if (systemConfiguredExceptionState.equals(ScheduledJobExceptionState.ALL)) {
            throw new TetrisException(sre.getErrorCode(), sre.getMessage(), sre);
        }
        if (systemConfiguredExceptionState.equals(ScheduledJobExceptionState.REPORT_GENERATION)) {
            if (ErrorCode.REPORT_DELIVERY_FAILED == sre.getErrorCode()) {
                scheduledReportAuditService.createAuditEntry(scheduleReport.getId(), ScheduledReportAuditStatus.DELIVERY_FAILED, scheduleReport.getActualReportName(), businessDate, sre.getMessage(), ScheduledReportAuditExecutionType.POST_BDE);
            } else {
                throw new TetrisException(sre.getErrorCode(), sre.getMessage(), sre);
            }
        }
        if (systemConfiguredExceptionState.equals(ScheduledJobExceptionState.REPORT_DELIVERY)) {
            if (ErrorCode.REPORT_GENERATION_FAILED == sre.getErrorCode()) {
                scheduledReportAuditService.createAuditEntry(scheduleReport.getId(), ScheduledReportAuditStatus.GENERATION_FAILED, scheduleReport.getActualReportName(), businessDate, sre.getMessage(), ScheduledReportAuditExecutionType.POST_BDE);
            } else {
                throw new TetrisException(sre.getErrorCode(), sre.getMessage(), sre);
            }
        }
        if (systemConfiguredExceptionState.equals(ScheduledJobExceptionState.NONE)) {
            scheduledReportAuditService.createAuditEntry(scheduleReport.getId(), getStatusFromErrorCode(sre.getErrorCode()), scheduleReport.getActualReportName(), businessDate, sre.getMessage(), ScheduledReportAuditExecutionType.POST_BDE);
        }
    }

    public ScheduledReportAuditStatus getStatusFromErrorCode(ErrorCode errorCode) {
        if (ErrorCode.REPORT_GENERATION_FAILED.equals(errorCode)) {
            return ScheduledReportAuditStatus.GENERATION_FAILED;
        } else {
            return ScheduledReportAuditStatus.DELIVERY_FAILED;
        }
    }

    private boolean isEmailDelivery(ScheduleReport scheduleReport) {
        String emailRecipients = scheduleReport.getEmailRecipients();
        return emailRecipients != null && emailRecipients.length() > 0;
    }

    private void buildLoggerForReport(ScheduleReport scheduleReport, Date businessDate,
                                      String logMessage, ScheduledReportException exception, ReportSource reportSource) {
        Map<LoggingContext.LoggingContextKey, Object> context = new HashMap<>();
        context.put(LoggingContext.LoggingContextKey.REPORT_SOURCE, reportSource.name());
        context.put(LoggingContext.LoggingContextKey.REPORT_NAME, scheduleReport.getName());
        context.put(LoggingContext.LoggingContextKey.REPORT_TYPE, scheduleReport.getActualReportName());
        context.put(LoggingContext.LoggingContextKey.BUSINESS_DATE, businessDate);
        context.put(LoggingContext.LoggingContextKey.REPORT_FORMAT, scheduleReport.getOutputFormat());
        if (isEmailDelivery(scheduleReport)) {
            context.put(LoggingContext.LoggingContextKey.REPORT_DELIVERY_TYPE, EMAIL);
        } else {
            context.put(LoggingContext.LoggingContextKey.REPORT_DELIVERY_TYPE, scheduleReport.getFtpType());
            context.put(LoggingContext.LoggingContextKey.FTP_DESTINATION, scheduleReport.getFtpServer());
            context.put(LoggingContext.LoggingContextKey.FTP_FOLDER_PATH, scheduleReport.getFtpFolderPath());
        }

        if (exception != null) {
            context.put(LoggingContext.LoggingContextKey.ERROR_MESSAGE, exception.getMessage());
            context.put(LoggingContext.LoggingContextKey.ERROR_STEP_NAME,
                    exception.getScheduleReportExceptionStepMessage().getExecutionStep());
            context.put(LoggingContext.LoggingContextKey.STEP_ERROR_DESCRIPTION,
                    exception.getScheduleReportExceptionStepMessage().getDescription());
            context.put(LoggingContext.LoggingContextKey.ERROR_TYPE, exception.getErrorCode().name());
            LoggingContext.wrapWithContext(context, v -> LOGGER.error(createErrorMessage(exception, logMessage)));
        } else {
            LoggingContext.wrapWithContext(context, v -> LOGGER.info(logMessage));
        }
    }

    private String createErrorMessage(ScheduledReportException exception, String logMessage) {
        StringBuilder errorMessage = new StringBuilder(logMessage);
        errorMessage.append(NEW_LINE_SEPARATOR).append("Error Type: ").append(exception.getErrorCode().name());
        errorMessage.append(NEW_LINE_SEPARATOR).append("Step Name: ")
                .append(exception.getScheduleReportExceptionStepMessage().getExecutionStep());
        errorMessage.append(NEW_LINE_SEPARATOR).append("Error Description: ")
                .append(exception.getScheduleReportExceptionStepMessage().getDescription());
        if ("".equals(exception.getMessage())) {
            errorMessage.append(NEW_LINE_SEPARATOR).append("Error Message: ").append(exception.getCause());
        } else {
            errorMessage.append(NEW_LINE_SEPARATOR).append("Error Message: ").append(exception.getMessage());
        }
        errorMessage.append(NEW_LINE_SEPARATOR).append("Stack Trace: ").append(getExceptionStackTrace(exception));
        return errorMessage.toString();
    }

    private String getExceptionStackTrace(Throwable t) {
        StringWriter stringWriter = new StringWriter();
        t.printStackTrace(new PrintWriter((stringWriter)));
        return stringWriter.toString();
    }

    public boolean callJasper(ScheduleReport scheduleReport, Date businessDate, ReportSource reportSource) {
        String reportName = scheduleReport.getName();
        buildLoggerForReport(scheduleReport, businessDate, String.format(EXECUTION_STATE_STARTED, reportName), null, reportSource);

        ScheduledJobExceptionState systemConfiguredExceptionState =
                ScheduledJobExceptionState.forExceptionState(SystemConfig.getPostBDEScheduleReportExceptionHandlingMechanism());
        boolean isReportDeliverySuccessful = true;
        try {
            String fileName = getReportFileName(scheduleReport);
            reportsG3Service.generateScheduledReport(createReportParameterMap(
                            scheduleReport.getRportParameters(), scheduleReport.getJasperReportURI(), scheduleReport.getOutputFormat()),
                    getLocalFileSourceFolderPath(scheduleReport.getPropertyId(), reportSource),
                    fileName);
            deliverReport(scheduleReport, fileName, reportSource);
            scheduledReportAuditService.createAuditEntry(scheduleReport.getId(), ScheduledReportAuditStatus.SUCCESS,
                    scheduleReport.getActualReportName(), businessDate, null,
                    getScheduledReportAuditExecutionType(reportSource));
            buildLoggerForReport(scheduleReport, businessDate, String.format(EXECUTION_STATE_COMPLETED, reportName), null, reportSource);
        } catch (ScheduledReportException te) {
            isReportDeliverySuccessful = false;
            buildLoggerForReport(scheduleReport, businessDate, String.format(EXECUTION_STATE_ERROR, reportName), te, reportSource);
            handleExceptionStates(businessDate, scheduleReport, te, systemConfiguredExceptionState);
        }

        return isReportDeliverySuccessful;
    }

    private ScheduledReportAuditExecutionType getScheduledReportAuditExecutionType(ReportSource reportSource) {
        if (ReportSource.AFTER_CDP == reportSource) {
            return ScheduledReportAuditExecutionType.POST_IDP;
        }
        return ScheduledReportAuditExecutionType.POST_BDE;
    }


    public Map<String, String> createReportParameterMap(String reportParameters, String reportUnit, String outputFormat) throws ScheduledReportException {
        String[] parameterArray = reportParameters.split(SCHEDULE_PARAM_SEPARATOR);
        Map<String, String> parameterMap = new HashMap<>();
        List<Integer> activeCompetitors = populateActiveCompetitors(reportParameters);
        for (String reportParameter : parameterArray) {
            String[] parameterNameAndValue = reportParameter.split(REPORT_PARAM_VALUE_SEPERATOR);
            if (isDateParam(parameterNameAndValue[0])) {
                parameterMap.put(parameterNameAndValue[0], getDateInGivenFormat(parameterNameAndValue[1], YYYY_MM_DD));
            } else if (COMP_LIST.equalsIgnoreCase(parameterNameAndValue[0])) {
                addCompetitorsInMap(parameterMap, parameterNameAndValue[1]);
            } else if (isCompParam(parameterNameAndValue[0]) && Integer.valueOf(parameterNameAndValue[1]) > -1) {
                parameterMap.put(parameterNameAndValue[0], getCompId(parameterNameAndValue[1], activeCompetitors));
            } else {
                parameterMap.put(parameterNameAndValue[0], parameterNameAndValue.length > 1 ? parameterNameAndValue[1] : StringUtils.EMPTY);
            }
        }
        parameterMap.put(REPORT_UNIT, REPORT_UNIT_BASE + reportUnit);
        parameterMap.put(OUTPUT, outputFormat);
        parameterMap.put(IS_IGNORE_PAGINATION, TRUE);

        return parameterMap;
    }

    private void addCompetitorsInMap(Map<String, String> parameterMap, String competitorsAsStr) {
        String[] compList = getCompList(competitorsAsStr);
        for (int index = 1; index <= compList.length; index++) {
            parameterMap.put(COMP + index, compList[index - 1].trim());
        }
    }

    public void deliverReport(ScheduleReport scheduleReport, String fileName, ReportSource reportSource) throws ScheduledReportException {
        deliverReport(scheduleReport, getLocalFilePath(fileName, scheduleReport.getPropertyId(), reportSource), fileName);
    }

    public String getReportFileName(ScheduleReport scheduleReport) {
        String fileName;
        String currentTimeStr = dateService.getPropertyCurrentTime().toString(PATTERN);
        fileName = scheduleReport.getName() + "-" + currentTimeStr + "." + scheduleReport.getOutputFormat().toLowerCase();
        return fileName;
    }

    public String getLocalFilePath(String fileName, int propertyId, ReportSource reportSource) {
        String sourceFolder = getLocalFileSourceFolderPath(propertyId, reportSource);
        return sourceFolder + File.separator + fileName;
    }

    public String getLocalFileSourceFolderPath(int propertyId, ReportSource reportSource) {
        Property property = globalCrudService.findByNamedQuerySingleResult(Property.BY_ID_WITH_CLIENT,
                QueryParameter.with(Property.PARAM_PROPERTY_ID, propertyId).parameters());
        String tempFolder = SystemConfig.getTempFolderBasePathForReports();
        if (ReportSource.AFTER_CDP == reportSource) {
            tempFolder = SystemConfig.getTempFolderBasePathForReportsForIDP();
        }
        String sourceFolder = tempFolder + File.separator + property.getClient().getId() + File.separator + propertyId;
        File sourceDir = new File(sourceFolder);
        if (!sourceDir.exists()) {
            sourceDir.mkdirs();
        }
        return sourceFolder;
    }

    public void deliverReport(ScheduleReport scheduleReport, String filePath, String fileName) throws ScheduledReportException {
        if (isEmailDelivery(scheduleReport)) {
            reportEmailDelivery(scheduleReport, new File(filePath));
            return;
        }

        FileInputStream generateFileStream = null;
        try {
            generateFileStream = getFileInputStream(filePath);
        } catch (FileNotFoundException e) {
            throw new ScheduledReportException(ErrorCode.REPORT_DELIVERY_FAILED, ScheduleReportExceptionStepMessage.GENERATED_FILE_NOT_FOUND, e);
        }

        if (FTP_TYPE.equalsIgnoreCase(scheduleReport.getFtpType())) {
            ftpService.uploadFileToFTP(scheduleReport.getFtpServer(), scheduleReport.getFtpPort(), scheduleReport.getFtpUserName(), scheduleReport.getFtpPassword(), scheduleReport.getFtpFolderPath(), generateFileStream, fileName);
        }
        if (SFTP_TYPE.equalsIgnoreCase(scheduleReport.getFtpType())) {
            uploadFileToSftp(scheduleReport, fileName, generateFileStream);
        }
    }

    private void uploadFileToSftp(ScheduleReport scheduleReport, String fileName, FileInputStream generateFileStream) throws ScheduledReportException {
        if (configParamsService.getBooleanParameterValue(USE_SSHJ_SFTP_LIBRARY_FOR_SCHEDULED_REPORT)) {
            SFTPDetails sftpDetails = new SFTPDetails(scheduleReport.getFtpServer(), scheduleReport.getFtpPort(), scheduleReport.getFtpUserName(), scheduleReport.getFtpPassword(), scheduleReport.getFtpFolderPath());
            sshjSftpService.uploadFileToSFTP(sftpDetails, fileName, generateFileStream);
        } else {
            sftpService.uploadFileToSFTP(scheduleReport.getFtpServer(), scheduleReport.getFtpPort(), scheduleReport.getFtpUserName(), scheduleReport.getFtpPassword(), scheduleReport.getFtpFolderPath(), generateFileStream, fileName);
        }
    }

    public FileInputStream getFileInputStream(String filePath) throws FileNotFoundException {
        return new FileInputStream(new File(filePath));
    }

    private String[] getCompList(String competitorsAsStr) {
        String[] competitorsArray = competitorsAsStr.replace("[", "")
                .replace("]", "").split(",");
        if (Integer.valueOf(competitorsArray[0]) > 0) {
            List<Integer> activeCompetitorIds = getActiveCompIds();
            for (int index = 0; index < competitorsArray.length; index++) {
                if (!competitorsArray[index].equals(INACTIVE_COMP_REPLACEMENT_VALUE)) {
                    competitorsArray[index] = getCompId(competitorsArray[index], activeCompetitorIds);
                }
            }
        }
        return competitorsArray;
    }

    private List<Integer> getActiveCompIds() {
        List<WebrateCompetitors> webrateCompetitors = webrateShoppingDataService.getAllCompetitorsByProperty();
        return webrateCompetitors.stream().map(WebrateCompetitors::getId).collect(Collectors.toList());
    }

    private String getCompId(String competitor, List<Integer> activeCompIds) {
        return activeCompIds.contains(Integer.valueOf(competitor)) ? competitor : INACTIVE_COMP_REPLACEMENT_VALUE;
    }

    private boolean isCompParam(String paramName) {
        return COMP_PARAM_NAMES.contains(paramName);
    }

    private List<Integer> populateActiveCompetitors(String reportParameters) {
        List<Integer> activeCompetitorIds = new ArrayList<>();
        String regex = "Comp1=[1-9]";
        Pattern pattern = Pattern.compile(regex);
        Predicate<String> predicate = pattern.asPredicate();
        if (predicate.test(reportParameters)) {
            activeCompetitorIds = getActiveCompIds();
        }
        return activeCompetitorIds;
    }

    private boolean isDateParam(String paramName) {
        return DATE_PARAM_NAMES.contains(paramName);
    }

    private String getDateInGivenFormat(String dateStr, String dateFormat) throws ScheduledReportException {
        try {
            if (DEFAULT_DATE.equals(dateStr)) {
                return DEFAULT_DATE_FOR_REPORTS;
            }
            return DateUtil.formatDate(DateUtil.parseDate(dateStr, DEFAULT_DATE_FORMAT_FOR_SCHEDULED_REPORTS), dateFormat);
        } catch (ParseException e) {
            throw new ScheduledReportException(ErrorCode.REPORT_GENERATION_FAILED, ScheduleReportExceptionStepMessage.GENERATE_REQUEST_ID_DATE_FORMAT_ERROR, e);
        }
    }

    @VisibleForTesting


    public Response downloadReport(@HeaderParam("baseReportFolder") String baseReportFolder, @HeaderParam("reportName") String reportName, ScheduleReport scheduleReport) {
        try {
            Map<String, String> parameterMap = createReportParameterMap(scheduleReport.getRportParameters(), scheduleReport.getJasperReportURI(), scheduleReport.getOutputFormat());
            reportsG3Service.generateReport(parameterMap, baseReportFolder, reportName, () -> "false");
            return Response.ok().build();
        } catch (Exception e) {
            return Response.serverError().entity(ExceptionUtils.getMessage(e)).build();
        }
    }


    public void reportEmailDelivery(ScheduleReport scheduledReport, File report) {
        List<String> toAddresses = new ArrayList<>(Arrays.asList(scheduledReport.getEmailRecipients().split("\\s*,\\s*")));
        removeUpdateAuthorizedEmailRecipients(scheduledReport, toAddresses);
        if (toAddresses.isEmpty()) {
            LOGGER.info("Email report will not be sent for report : " + scheduledReport.getName() + " because no authorized recipient are present");
            return;
        }
        int index1 = scheduledReport.getRportParameters().indexOf("userLocale");
        String locale = scheduledReport.getRportParameters().substring(index1 + 11, index1 + 13);
        String subject = scheduledReport.getActualReportName() + " - " + scheduledReport.getName();
        String body = ResourceUtil.getText("mailContent", new Locale(locale));
        LOGGER.info("Sending email for report  '" + scheduledReport.getName() + "', with file as '" + report + "'.");
        emailService.sendEmail(getReportSchedulerMailSenderFrom(), toAddresses, subject, body, Collections.singletonList(report), false);
        LOGGER.info("Sent email for report  '" + scheduledReport.getName() + "', with file as '" + report + "'.");
    }

    private void removeUpdateAuthorizedEmailRecipients(ScheduleReport scheduledReport, List<String> toAddresses) {
        if (isRemoveUnauthorizedEmailRecipientForReportEnabled()) {
            List<String> unauthorizedEmails = removeUnauthorizedUsersEmail(toAddresses, scheduledReport.getPropertyId());
            LOGGER.info(String.format(UNAUTHORIZED_USER_EMAIL_REMOVED_MESSGAE, unauthorizedEmails.toString(), scheduledReport.getName()));
            toAddresses.removeIf(a -> (unauthorizedEmails.stream().filter(d -> d.equalsIgnoreCase(a)).count()) == 1);
            if (toAddresses.isEmpty()) {
                deleteScheduleReport(scheduledReport);
            } else if (!unauthorizedEmails.isEmpty()) {
                updateEmailRecipient(scheduledReport, toAddresses);
            }
        }
    }

    private void deleteScheduleReport(ScheduleReport scheduledReport) {
        LOGGER.info("Schedule doesn't have any authorized Email recipient,Schedule Report : " + scheduledReport.getName() + " will be removed");
        reportService.deleteSchedule(scheduledReport);
        LOGGER.info("Schedule report : " + scheduledReport.getName() + " removed successfully");
    }

    private void updateEmailRecipient(ScheduleReport scheduledReport, List<String> toAddresses) {
        String validEmails = String.join(",", toAddresses);
        LOGGER.info("Updating schedule report Email-Recipients for report : " + scheduledReport.getName());
        reportService.updateScheduleReportEmail(scheduledReport, validEmails);
        LOGGER.info("Schedule report Email-Recipients updated successfully for report : " + scheduledReport.getName());
    }

    private List<String> removeUnauthorizedUsersEmail(List<String> toAddresses, Integer propertyId) {
        Property property = globalCrudService.findByNamedQuerySingleResult(Property.BY_ID_WITH_CLIENT,
                QueryParameter.with(Property.PARAM_PROPERTY_ID, propertyId).parameters());
        Set<LDAPUser> users = userService.getGlobalUsersByEmailIds(toAddresses);
        return users.stream().filter(a -> !filterUnauthorizedPropertiesAccess(a.getUserId(), property)).map(LDAPUser::getMail).collect(Collectors.toList());
    }

    private boolean filterUnauthorizedPropertiesAccess(Integer userId, Property property) {
        return authorizationService.retrieveAuthorizedProperties(userId, property.getClient().getId()).contains(property);
    }

    private boolean isRemoveUnauthorizedEmailRecipientForReportEnabled() {
        return configParamsService.getParameterValue(FeatureTogglesConfigParamName.REMOVE_UNAUTHORIZED_EMAIL_RECIPIENTS_FOR_REPORTS_ENABLED);
    }
}