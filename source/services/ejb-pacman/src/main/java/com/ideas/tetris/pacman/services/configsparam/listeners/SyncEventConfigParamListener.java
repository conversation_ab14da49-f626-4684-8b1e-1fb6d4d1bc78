package com.ideas.tetris.pacman.services.configsparam.listeners;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.syncflags.service.SyncDisplayNameService;
import com.ideas.tetris.platform.common.cache.ConfigParameterNameValueCache;
import com.ideas.tetris.platform.common.configparams.components.ConfigParamChangeEvent;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

@Component
public class SyncEventConfigParamListener extends BaseConfigParamListener {
    private static final Logger LOGGER = Logger.getLogger(SyncEventConfigParamListener.class);

    @Autowired
	private SyncDisplayNameService syncDisplayNameService;

    @Autowired
    private ConfigParameterNameValueCache configParameterNameValueCache;
    @Override
    void parameterChange(ConfigParamChangeEvent event) {
        SyncEvent syncEvent = getSynEventForParam(event.getName());
        if (syncEvent == null) {
            return;
        }

        if(SystemConfig.getIsLoggerInfoEnabledForSpringListeners()) {     
        String oldValue = configParameterNameValueCache.get(event.getContext(), event.getName(), event.getContext());
        String newValue = event.getValue();

        
        String scopeLevel = (event.getContext().split("\\.").length == 1 ? "global" :
                             event.getContext().split("\\.").length == 2 ? "client" :
                             event.getContext().split("\\.").length == 3 ? "property" : "unknown");

        
        String oldValueDisplay = (oldValue == null || oldValue.isEmpty()) ? "N/A" : oldValue;

        LOGGER.info("Config parameter value changed from " + oldValueDisplay + " to " + newValue + " at " + scopeLevel + " level.");
      }
        LOGGER.info("Received event - '" + event.getName() + "' for context - " + event.getContext());
        if (isNotAPropertyContext(event.getContext())) {
            LOGGER.info("Ignoring deletion of display name of sync parameter for context: " + event.getContext());
            return;
        }

        if (Constants.FALSE.equalsIgnoreCase(event.getValue())) {
            final WorkContextType workContextOld = getWorkContext();
            try {
                setCurrentWorkContext(event.getContext());
                syncDisplayNameService.deleteDisplayNamesForSyncEvent(syncEvent);
                LOGGER.info("Deletion of display name of sync parameter successful for Property: " + event.getContext());
            } finally {
                setWorkContext(workContextOld);
            }
        }
    }

    private SyncEvent getSynEventForParam(String name) {
        return SyncEvent.valueOfGlobalParameterName(name);
    }

    private boolean isNotAPropertyContext(String context) {
        return context != null && context.split("\\.").length != 3;
    }
}