package com.ideas.tetris.pacman.services.opera;

import com.ideas.g3.integration.opera.dto.OperaDataLoadTypeCode;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.componentrooms.services.ComponentRoomService;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.marketsegment.entity.YieldCategoryRule;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentAttribute;
import com.ideas.tetris.pacman.services.opera.entity.DataLoadMetadata;
import com.ideas.tetris.pacman.services.opera.entity.RawOccupancySummary;
import com.ideas.tetris.pacman.services.property.PropertyConfigParamService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.time.JavaLocalDateInterval;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.opera.OperaReservationStatus.CANCELLED;
import static com.ideas.tetris.pacman.services.opera.OperaReservationStatus.CHECKED_IN;
import static com.ideas.tetris.pacman.services.opera.OperaReservationStatus.CHECKED_OUT;
import static com.ideas.tetris.pacman.services.opera.OperaReservationStatus.NO_SHOW;
import static com.ideas.tetris.pacman.services.opera.OperaReservationStatus.RESERVED;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class YCByRuleBuildSummaryService {
    private static final Logger LOGGER = Logger.getLogger(YCByRuleBuildSummaryService.class.getName());
    public static final String CORRELATION_ID = "correlationId";


    @TenantCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("tenantCrudServiceBean")
    CrudService crudService;
    @Autowired
    OperaUtilityService operaUtilityService;
    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
	private ComponentRoomService componentRoomService;
    @Autowired
	private PropertyConfigParamService propertyConfigParamService;

    private String createJoinConditionForTrans(String firstTable, String secondTable) {
        return new StringBuilder().
                append(" ON ").
                append(firstTable).
                append(".Transaction_DT = ").
                append(secondTable).
                append(".Transaction_DT").
                append(" AND ").
                append(firstTable).
                append(".Market_Code = ").
                append(secondTable).
                append(".Market_Code").
                append(" AND ").
                append(firstTable).
                append(".Room_Type = ").
                append(secondTable).
                append(".Room_Type").toString();
    }

    private String createJoinConditionForTransandGroup(String firstTable, String secondTable) {
        return new StringBuilder().
                append(" ON ").
                append(firstTable).
                append(".Occupancy_DT = ").
                append(secondTable).
                append(".Occupancy_DT").
                append(" AND ").
                append(firstTable).
                append(".Market_Code = ").
                append(secondTable).
                append(".Market_Code").
                append(" AND ").
                append(firstTable).
                append(".Room_Type = ").
                append(secondTable).
                append(".Room_Type").toString();
    }

    // This is base table if MS BY Rule is ONf
    private static final StringBuilder SQL_GET_ALL_MS_PARTICIPATING_IN_MS_BY_RULE = new StringBuilder()
            .append("SELECT DISTINCT umc.Market_Code into opera.ycbyrulems FROM")
            .append("(SELECT Analytical_Market_Code as Market_Code FROM opera.Yield_Category_Rule")
            .append(" UNION")
            .append(" SELECT Market_Code as Market_Code FROM opera.Yield_Category_Rule")
            .append(") as umc;");

    private static final StringBuilder SQL_GET_TRANSACTION_DATA_REQUIRED_FOR_SUMMARY_BUILD = new StringBuilder()
            .append(" select t.* into opera.trans from opera.stage_transaction as t join opera.ycbyrulems as m on t.market_code = m.market_code and t.arrival_dt <> t.departure_DT order by transaction_dt,market_code,room_type; ")
            .append(" create index IDX_MSBR_TRANS on opera.trans (transaction_dt,market_code,room_type);");

    private static final StringBuilder SQL_GET_TRANSACTION_DATA_REQUIRED_FOR_SUMMARY_BUILD_WITH_DAY_USE = new StringBuilder()
            .append(" select t.* into opera.trans from opera.stage_transaction as t join opera.ycbyrulems as m on t.market_code = m.market_code order by transaction_dt,market_code,room_type; ")
            .append(" create index IDX_MSBR_TRANS on opera.trans (transaction_dt,market_code,room_type);");

    private static final StringBuilder SQL_DELETE_DAY_USE_TRANSACTIONS = new StringBuilder()
            .append(" delete from opera.trans where arrival_dt = departure_DT; ");

    private static final StringBuilder SQL_ALL_RESERVATION_PER_MS_BY_RULE = new StringBuilder().
            append(" select st.Transaction_DT, st.Market_Code, st.Room_Type ").
            append(" INTO ").
            append(" opera.allReservations").
            append("	FROM opera.trans st ").
            append("	GROUP BY st.Transaction_DT, st.Market_Code, st.Room_Type").
            append("    Having st.Transaction_DT between :businessStartDate and :businessEndDate")
            .append(" create index IDX_ALL_RERSV on opera.allReservations (transaction_dt,market_code,room_type);");


    // Base tables common to Rate Category and MS BY Rule. Use to Join with base table
    private static final StringBuilder SQL_ACTIVE_TRANS_DATA = new StringBuilder().
            append(" select st.Transaction_DT, st.Market_Code, st.Room_Type").
            append("		, SUM(st.Room_Revenue) AS Room_Revenue").
            append("		, SUM(st.Food_Beverage_Revenue) AS Food_Revenue").
            append("		, SUM(st.Total_Revenue) AS Total_Revenue").
            append(" INTO ").
            append(" opera.active").
            append("	FROM opera.trans st WHERE Reservation_Status in :individualStatusTypesForRevenue").
            append("	GROUP BY st.Transaction_DT, st.Market_Code, st.Room_Type ;")
            .append(" create index IDX_ACTIVE_RERSV on opera.active (transaction_dt,market_code,room_type);");

    public static final String EXCLUDE_PSEUDO_ROOM_TYPES_QUERY_CLAUSE = " and st.Room_type not in (:pseudoRoomTypes) ";
    public static final String EXCLUDE_PSEUDO_ROOM_TYPES_PLACEHOLDER = "%%excludePseudoRTsInThisQuery%%";
    private static final StringBuilder SQL_ACTIVE_SOLDS_TRANS_DATA = new StringBuilder().
            append("select st.Transaction_DT, st.Market_Code, st.Room_Type, COUNT(*) AS Solds").
            append(" INTO ").
            append(" opera.activeSolds").
            append("	FROM opera.trans st WHERE Reservation_Status in :activeStatusTypes").
            append("	and st.Transaction_Dt < st.Departure_Dt ").
            append(EXCLUDE_PSEUDO_ROOM_TYPES_PLACEHOLDER).
            append("	GROUP BY st.Transaction_DT, st.Market_Code, st.Room_Type ;")
            .append(" create index IDX_SOLDS_RERSV on opera.activeSolds (transaction_dt,market_code,room_type);");


    private static final StringBuilder SQL_CANCELLED_TRANS_DATA = new StringBuilder().
            append(" select st.Transaction_DT, st.Market_Code, st.Room_Type, COUNT(*) AS Cancellations ").
            append("	INTO ").append(" opera.cancelled").
            append("  FROM opera.trans st WHERE Reservation_Status = :cancelledStatusType").
            append("	 and st.Transaction_Dt = st.Arrival_DT  ").
            append(EXCLUDE_PSEUDO_ROOM_TYPES_PLACEHOLDER).
            append("  GROUP BY st.Transaction_DT, st.Market_Code, st.Room_Type ;")
            .append(" create index IDX_CNX_RERSV on opera.cancelled (transaction_dt,market_code,room_type);");


    private static final StringBuilder SQL_NO_SHOW_TRANS_DATA = new StringBuilder().
            append(" select st.Transaction_DT, st.Market_Code, st.Room_Type, COUNT(*) AS No_Shows ").
            append(" INTO ").
            append(" opera.noShow").
            append(" FROM opera.trans st WHERE Reservation_Status = :noShowStatusType").
            append(" and st.Transaction_Dt = st.Arrival_DT ").
            append(EXCLUDE_PSEUDO_ROOM_TYPES_PLACEHOLDER).
            append(" GROUP BY st.Transaction_DT, st.Market_Code, st.Room_Type ;")
            .append(" create index IDX_NS_RERSV on opera.noShow (transaction_dt,market_code,room_type);");


    private static final StringBuilder SQL_ARRIVALS_TRANS_DATA = new StringBuilder().
            append(" select st.Transaction_DT, st.Market_Code, st.Room_Type, COUNT(*) AS Arrivals").
            append(" INTO ").
            append(" opera.arrivals").
            append(" FROM opera.trans st WHERE st.Arrival_DT = st.Transaction_DT").
            append(" AND Reservation_Status in :activeStatusTypes ").
            append(EXCLUDE_PSEUDO_ROOM_TYPES_PLACEHOLDER).
            append(" GROUP BY st.Transaction_DT, st.Market_Code, st.Room_Type ;")
            .append(" create index IDX_ARRIVALS_RERSV on opera.arrivals (transaction_dt,market_code,room_type);");

    private static final StringBuilder SQL_DEPARTURE_DT_TRANS_DT_DATA_FOR_MS_BY_RULE = new StringBuilder().
            append("select st.Departure_DT as Transaction_DT,max(st.market_code) as market_code ,max(st.room_type) as room_type ").
            append(" INTO ").
            append(" opera.departureDtToTransactionDtData").
            append("  FROM opera.trans st ").
            append(" WHERE Reservation_Status in :activeStatusTypes").
            append(" and st.Transaction_Dt < st.departure_dt").
            append("  GROUP BY st.Departure_DT, st.Reservation_Name_ID ;");


    private static final StringBuilder SQL_DEPARTURE_FROM_TRANS_DATA = new StringBuilder().
            append(" select st.transaction_dt,st.market_code,st.room_type,COUNT(*) as departures ").
            append(" INTO ").
            append(" opera.departureFromAllTrans ").
            append(" FROM ").
            append(" opera.departureDtToTransactionDtData st").
            append(" WHERE 1=1 ").
            append(EXCLUDE_PSEUDO_ROOM_TYPES_PLACEHOLDER).
            append(" GROUP BY st.Transaction_dt, st.Market_Code, st.Room_Type").
            append(" HAVING st.Transaction_DT between :businessStartDate and :businessEndDate ;")
            .append(" create index IDX_DEPT_RERSV on opera.departureFromAllTrans (transaction_dt,market_code,room_type);");

    // past departures - from transaction
    private static final StringBuilder SQL_RESERVATION_WITHOUT_DEPT = new StringBuilder().
            append(" select allReservations.Transaction_DT").
            append("			, allReservations.Market_Code ").
            append("			, COALESCE(activeSolds.Solds, 0) as rooms_sold").
            append("			, COALESCE(active.Room_Revenue, 0) as room_revenue").
            append("			, COALESCE(arrivals.Arrivals, 0) as room_arrivals").
            append("			, COALESCE(active.Total_Revenue, 0) as total_revenue").
            append("			, COALESCE(active.Food_Revenue, 0) as food_revenue").
            append("			, COALESCE(cancelled.Cancellations, 0) as cancellations").
            append("			, COALESCE(noShow.No_Shows, 0) as noShows").
            append("			, allReservations.Room_Type").
            append(" INTO ").
            append(" opera.allTransWithoutDept").
            append(" FROM ");

    private final StringBuilder sqlReservationWithoutDeptBuff =
            new StringBuilder(SQL_RESERVATION_WITHOUT_DEPT)
                    .append(" opera.allReservations")
                    .append(" as ")
                    .append(" allReservations")
                    .append(" LEFT JOIN ")
                    .append(" opera.active")
                    .append(" as active ")
                    .append(createJoinConditionForTrans("active", "allReservations"))
                    .append(" LEFT JOIN ")
                    .append(" opera.activeSolds")
                    .append(" as activeSolds ")
                    .append(createJoinConditionForTrans("activeSolds", "allReservations"))
                    .append(" LEFT JOIN ")
                    .append(" opera.cancelled")
                    .append(" as cancelled ")
                    .append(createJoinConditionForTrans("cancelled", "allReservations"))
                    .append(" LEFT JOIN ")
                    .append(" opera.noShow")
                    .append(" as noShow ")
                    .append(createJoinConditionForTrans("noShow", "allReservations"))
                    .append(" LEFT JOIN ")
                    .append(" opera.arrivals")
                    .append(" as arrivals ")
                    .append(createJoinConditionForTrans("arrivals", "allReservations"))
                    .append(";");

    private static final StringBuilder SQL_RESERVATIONS_WITH_DEPARTURE = new StringBuilder().
            append(" SELECT (select distinct resort from opera.Stage_Occupancy_Summary ) as Resort ").
            append("		, COALESCE( allTransWithoutDept.transaction_dt, departures.transaction_dt) as Occupancy_DT").
            append("		, COALESCE( allTransWithoutDept.market_code, departures.market_code) as market_code").
            append("		, 0 as Physical_Rooms").
            append("		, 0 as Out_Of_Order_Rooms").
            append("		, 0 as Out_Of_Service_Rooms").
            append("		, COALESCE( allTransWithoutDept.rooms_sold, 0) as rooms_sold").
            append("		, COALESCE( allTransWithoutDept.room_revenue, 0) as room_revenue").
            append("		, COALESCE( allTransWithoutDept.room_arrivals, 0) as room_arrivals ").
            append("		, COALESCE( departures.Departures, 0)as Room_Departures").
            append("		, COALESCE( allTransWithoutDept.total_revenue, 0) as Total_Revenue").
            append("		, COALESCE( allTransWithoutDept.food_revenue, 0) as Food_Revenue").
            append("		, COALESCE( allTransWithoutDept.cancellations, 0) as Cancelled_Rooms	").
            append("		, COALESCE( allTransWithoutDept.noShows, 0) as No_Show_Rooms").
            append("		, NULL as Reservation_Type").
            append("		, COALESCE( allTransWithoutDept.room_type,departures.room_type) as Room_Type").
            append("		, NULL as Mkt_Seg_ID").
            append("		, NULL as Accom_Type_ID").
            append("		, Data_Load_Metadata_ID = CASE  ").
            append("                    WHEN COALESCE( allTransWithoutDept.transaction_dt ,departures.transaction_dt) < (select CAST(business_dt AS date) from opera.Stage_Incoming_Metadata)").
            append("				    THEN :psatDataLoadMetadata ELSE :csatDataLoadMetadata").
            append("				  END ").
            append(" INTO ").
            append(" opera.finalMSRTSummaryForTransData").
            append(" FROM ");


    private final StringBuilder sqlReservationWithDeptMSByRuleBuff =
            new StringBuilder(SQL_RESERVATIONS_WITH_DEPARTURE)
                    .append(" opera.allTransWithoutDept")
                    .append(" as ")
                    .append(" allTransWithoutDept")
                    .append(" FULL OUTER JOIN ")
                    .append(" opera.departureFromAllTrans")
                    .append(" as ")
                    .append(" departures")
                    .append(createJoinConditionForTrans("departures", "allTransWithoutDept"))
                    .append(";");

    // Get all solds from GB/GM
    protected static final StringBuilder SQL_GET_GROUP_SOLDS_FROM_GROUP_DATA = new StringBuilder().
            append(" select sgb.Group_ID,block_dt as transaction_dt,room_type,market_segment as market_code").
            append("             ,block =  case when block >= pickup then block else pickup end ").
            append("             ,pickup").
            append("             ,nonPickUpRooms= case when block >= pickup then (block - pickup) else 0 end   ").
            append("             ,Single_Rate").
            append("             ,nonPickedUpRevenue  = case when block >= pickup then ((block - pickup)* Single_Rate) else 0 end").
            append("             INTO ").
            append("             opera.allFutureGroupDataFromGroupForNonPickUpData").
            append("             from  (select * from opera.Stage_Group_Block  where Block_DT >= :businessDt ) as sgb ").
            append("             join ( select * from opera.Stage_Group_Master where G3_Group_Status_Code = 'DEFINITE' and Market_Segment in (select market_code from opera.ycbyrulems)) as sgm ").
            append("             on sgb.Group_ID = sgm.Group_ID  ;");

    protected static final StringBuilder SQL_IGNORE_GROUP_SOLDS_FROM_GROUP_DATA = new StringBuilder().
            append(" select sgb.Group_ID,block_dt as transaction_dt,room_type,market_segment as market_code").
            append("             ,block =  case when block >= pickup then block else pickup end ").
            append("             ,pickup").
            append("             ,nonPickUpRooms= case when block >= pickup then (block - pickup) else 0 end   ").
            append("             ,Single_Rate").
            append("             ,nonPickedUpRevenue  = case when block >= pickup then ((block - pickup)* Single_Rate) else 0 end").
            append("             INTO ").
            append("             opera.allFutureGroupDataFromGroupForNonPickUpData").
            append("             from  (select * from opera.Stage_Group_Block  where Block_DT >= :businessDt and Block_DT < '1970-01-01' ) as sgb ").
            append("             join ( select * from opera.Stage_Group_Master where G3_Group_Status_Code = 'DEFINITE' and Market_Segment in (select market_code from opera.ycbyrulems)) as sgm ").
            append("             on sgb.Group_ID = sgm.Group_ID  ;");

    // Solds ,Arrivals and Revenue for NON Picked UP Group - GM GB Data
    private final StringBuilder SQL_GET_GROUP_SUMMARY_FOR_NON_PICKUP_WITHOUT_DEPARTURES = new StringBuilder().
            append(" select COALESCE(yy.transaction_dt,xx.transaction_dt) as transaction_dt ").
            append(" 	,COALESCE(yy.market_code,xx.market_code) as market_code ").
            append(" 	,COALESCE(yy.room_type,xx.room_type) as room_type ").
            append(" 	,COALESCE(yy.rooms_sold,0) as rooms_sold ").
            append(" 	,COALESCE(yy.room_arrivals,0) as room_arrivals ").
            append(" 	,COALESCE(yy.room_revenue,0) as room_revenue ").
            append(" 	,COALESCE(yy.room_revenue,0) as total_revenue ").
            append(" 	into ").
            append(" opera.allFutureNonPickUpSummarizedGroupDataWithoutDept").
            append("   from ").
            append("   ( ").
            append("    (select DATEADD(day,1,a.transaction_dt) as transaction_dt ,a.market_code,a.Room_Type from  ").
            append(" 	( ").
            append(" 		select transaction_dt,room_type,market_code, SUM(nonPickUpRooms) as rooms_sold,SUM(nonPickUpRooms) as room_arrivals, SUM(nonPickedUpRevenue) as Room_Revenue  ").
            append(" 		FROM ").
            append(" opera.allFutureGroupDataFromGroupForNonPickUpData").
            append(" 		GROUP BY transaction_dt, market_code, Room_Type   ").
            append(" 	) as a ").
            append(" 	where a.room_arrivals > 0) as xx ").
            append(" 	full outer join ").
            append("    ( ").
            append(" 		select transaction_dt,room_type,market_code, SUM(nonPickUpRooms) as rooms_sold,SUM(nonPickUpRooms) as room_arrivals, SUM(nonPickedUpRevenue) as Room_Revenue ").
            append(" 		FROM ").
            append(" opera.allFutureGroupDataFromGroupForNonPickUpData").
            append(" 		GROUP BY transaction_dt, market_code, Room_Type   ").
            append(" 	) as yy ").
            append("    on  ").
            append("    xx.transaction_dt = yy.transaction_dt ").
            append("    and xx.market_code =yy.market_code ").
            append("    and xx.room_type = yy.room_type ").
            append("    ) ; ");

    // Departures for NON Picked up Group - GM GB Data
    private final StringBuilder SQL_GET_GROUP_SUMMARY_FOR_NON_PICKUP_WITH_DEPARTURES = new StringBuilder().
            append("select (select distinct resort from opera.Stage_Occupancy_Summary where resort is not null) as Resort ,").
            append(" a.transaction_dt as Occupancy_DT, ").
            append(" a.market_code  as market_code, ").
            append(" 0 as Physical_Rooms, ").
            append(" 0 as Out_Of_Order_Rooms, ").
            append(" 0 as Out_Of_Service_Rooms, ").
            append(" a.rooms_sold as rooms_sold, ").
            append(" a.room_revenue as room_revenue, ").
            append(" a.room_arrivals as room_arrivals, ").
            append(" COALESCE(b.room_arrivals,0) as room_departures, ").
            append(" a.total_revenue as Total_Revenue, ").
            append(" 0 as Food_Revenue, ").
            append(" 0 as Cancelled_Rooms, ").
            append(" 0 as No_Show_Rooms, ").
            append(" NULL as Reservation_Type, ").
            append(" a.room_type as Room_Type, ").
            append(" NULL as Mkt_Seg_ID, ").
            append(" NULL as Accom_Type_ID, ").
            append(" Data_Load_Metadata_ID =  :csatDataLoadMetadata").
            append(" into ").
            append(" opera.finalMSRTSummaryForGroupData ").
            append(" from opera.allFutureNonPickUpSummarizedGroupDataWithoutDept as a ").
            append(" left outer join ").
            append(" opera.allFutureNonPickUpSummarizedGroupDataWithoutDept as b ").
            append(" on ").
            append(" a.transaction_dt = DATEADD(day,1,b.transaction_dt) ").
            append(" and a.market_code = b.market_code and a.room_type = b.room_type; ");

    private static final StringBuilder SQL_TRANS_AND_GROUP_SUMMARY_WITH_DEPARTURE = new StringBuilder().
            append(" SELECT COALESCE(MSRTSummaryforTrans.Resort, MSRTSummaryforGroup.Resort) as Resort").
            append("		, COALESCE(MSRTSummaryforTrans.Occupancy_DT, MSRTSummaryforGroup.Occupancy_DT) as Occupancy_DT").
            append("		, COALESCE(MSRTSummaryforTrans.market_code,MSRTSummaryforGroup.market_code) as market_code ").
            append("		, 0 as Physical_Rooms").
            append("		, 0 as Out_Of_Order_Rooms").
            append("		, 0 as Out_Of_Service_Rooms").
            append("		, (COALESCE(MSRTSummaryforTrans.rooms_sold,0) + COALESCE(MSRTSummaryforGroup.rooms_sold,0)) as rooms_sold").
            append("		, (COALESCE(MSRTSummaryforTrans.room_revenue,0) + COALESCE(MSRTSummaryforGroup.room_revenue,0)) as room_revenue").
            append("		, (COALESCE(MSRTSummaryforTrans.room_arrivals,0) + COALESCE(MSRTSummaryforGroup.room_arrivals,0)) as room_arrivals ").
            append("		, (COALESCE(MSRTSummaryforTrans.Room_Departures,0) + COALESCE(MSRTSummaryforGroup.Room_Departures,0)) as Room_Departures").
            append("		, (COALESCE(MSRTSummaryforTrans.total_revenue,0) + COALESCE(MSRTSummaryforGroup.total_revenue,0)) as Total_Revenue").
            append("		, COALESCE(MSRTSummaryforTrans.food_revenue,0)  as Food_Revenue").
            append("		, COALESCE(MSRTSummaryforTrans.Cancelled_Rooms,0) as Cancelled_Rooms	").
            append("		, COALESCE(MSRTSummaryforTrans.No_Show_Rooms,0) as No_Show_Rooms").
            append("		, NULL as Reservation_Type").
            append("		, COALESCE(MSRTSummaryforTrans.room_type,MSRTSummaryforGroup.room_type) as Room_Type").
            append("		, NULL as Mkt_Seg_ID").
            append("		, NULL as Accom_Type_ID").
            append("		, COALESCE(MSRTSummaryforTrans.Data_Load_Metadata_ID,MSRTSummaryforGroup.Data_Load_Metadata_ID) as Data_Load_Metadata_ID ").
            append(" INTO ").
            append(" opera.finalMSRTSummaryForGroupandTransData").
            append(" FROM ");

    private final StringBuilder sqlTransandGroupWithDeptMSByRuleBuff =
            new StringBuilder(SQL_TRANS_AND_GROUP_SUMMARY_WITH_DEPARTURE)
                    .append(" opera.finalMSRTSummaryForTransData")
                    .append(" as ")
                    .append(" MSRTSummaryforTrans ")
                    .append(" FULL OUTER JOIN ")
                    .append(" opera.finalMSRTSummaryForGroupData")
                    .append(" as ")
                    .append(" MSRTSummaryforGroup")
                    .append(createJoinConditionForTransandGroup("MSRTSummaryforGroup", "MSRTSummaryforTrans"))
                    .append(";");

    private static final StringBuilder SQL_INSERT_MSRTS_SUMMARY_TO_OCCUPANCY_SUMMARY_BASE_BUFF = new StringBuilder().
            append("INSERT INTO opera.Stage_Occupancy_Summary").
            append("           (Resort,Occupancy_DT,Market_Code,Physical_Rooms,Out_Of_Order_Rooms").
            append("           ,Out_Of_Service_Rooms,Rooms_Sold,Room_Revenue,Room_Arrivals,Room_Departures").
            append("           ,Total_Revenue,Food_Revenue,Cancelled_Rooms,No_Show_Rooms,Reservation_Type").
            append("           ,Room_Type,Mkt_Seg_ID,Accom_Type_ID,Data_Load_Metadata_ID)").
            append(" SELECT Resort , Occupancy_DT,Market_Code,Physical_Rooms,Out_Of_Order_Rooms").
            append("           ,Out_Of_Service_Rooms,Rooms_Sold,Room_Revenue,Room_Arrivals,Room_Departures").
            append("           ,Total_Revenue,Food_Revenue,Cancelled_Rooms,No_Show_Rooms,Reservation_Type").
            append("           ,Room_Type,Mkt_Seg_ID,Accom_Type_ID,Data_Load_Metadata_ID ").
            append(" FROM 	");

    private static final StringBuilder SQL_INSERT_MSRT_SUMMARY_TO_OCCUPANCY_SUMMARY_FOR_TRANS_AND_GROUP_OR_WITHOUT_RC_MAPPING = new StringBuilder(
            SQL_INSERT_MSRTS_SUMMARY_TO_OCCUPANCY_SUMMARY_BASE_BUFF).
            append(" opera.finalMSRTSummaryForGroupandTransData").
            append(";");

    public int aggregateAnalyticalMarketSegmentReservations(String correlationId) {
        int numRows = 0;
        if (verifyIfAnyMSIsConfiguredForMSByRule()) {
            // Rate Category not used BUT MS BY Rule USED
            // ONLY Mapped MS for MS BY RULE will be flushed from Summary and Rebuild using Trans will occur
            LOGGER.info("Using YC By Rule Mapping to build RT-MS Summary from transaction");
            LOGGER.info("DELETING PSAT AND CSAT ONLY FOR ANALYTIC MS ");
            deleteRecordsContainingAnalyticalMarketSegments(correlationId);
            dropTempTables();
            numRows += insertAggregatedRecordsForYCBR(correlationId);
            dropTempTables();
        }
        return numRows;
    }

    private void dropTempTables() {
        dropTempTable("opera.ycbyrulems");
        dropTempTable("opera.trans");
        dropTempTable("opera.allReservations");
        dropTempTable("opera.active");
        dropTempTable("opera.activeSolds");
        dropTempTable("opera.cancelled");
        dropTempTable("opera.noShow");
        dropTempTable("opera.arrivals");
        dropTempTable("opera.departureDtToTransactionDtData");
        dropTempTable("opera.departures");
        dropTempTable("opera.departureFromAllTrans");
        dropTempTable("opera.allTransWithoutDept");
        dropTempTable("opera.finalMSRTSummaryForTransData");
        dropTempTable("opera.allFutureGroupDataFromGroupForNonPickUpData");
        dropTempTable("opera.allFutureNonPickUpSummarizedGroupDataWithoutDept");
        dropTempTable("opera.finalMSRTSummaryForGroupData");
        dropTempTable("opera.finalMSRTSummaryForGroupandTransData");
        LOGGER.info("DROPPED ALL TEMP TABLES ");
    }

    private void dropTempTable(String tableName) {
        String queryStr = new StringBuilder().
                append(" if object_id('").
                append(tableName).
                append("') is not NULL DROP TABLE ").
                append(tableName).
                append(";").toString();
        crudService.executeUpdateByNativeQuery(queryStr);
    }

    private boolean verifyIfAnyMSIsConfiguredForMSByRule() {
        List isPresent = crudService.findByNativeQuery("SELECT 1 FROM opera.Yield_Category_Rule");
        if (null != isPresent && !isPresent.isEmpty() && isPresent.get(0).equals(1)) {
            LOGGER.info("GOT A LOT OF WORK TO DO - THANKS FOR CONFIGURING MS BY RULE!!!");
            return true;
        } else {
            LOGGER.info("SAVED ME FROM A MASSIVE TASK!!");
            return false;
        }
    }

    protected int deleteRecordsContainingAnalyticalMarketSegments(String correlationId) {
        int numRowsDeleted = crudService.executeUpdateByNativeQuery("DELETE soc FROM opera.Stage_Occupancy_Summary soc inner join opera.Yield_Category_Rule oyc on soc.market_code = oyc.market_code " +
                "inner join opera.Data_Load_Metadata dlm " +
                "on dlm.Data_Load_Metadata_ID = soc.Data_Load_Metadata_ID where dlm.Incoming_File_Type_Code = 'PSAT' OR dlm.Incoming_File_Type_Code = 'CSAT'");
        LOGGER.info("ROWS DELETED FROM SUMMARY TO USE TRX AS SUMMARY = " + numRowsDeleted);
        return numRowsDeleted;
    }

    @SuppressWarnings("unchecked")
    private int insertAggregatedRecordsForYCBR(String correlationId) {
        String pseudoRoomTypes = pacmanConfigParamsService.getParameterValue(IntegrationConfigParamName.PSEUDO_ROOM_TYPE_CODES);
        String queryString = createQueryStringForYCBRProcessing(pseudoRoomTypes);
        Map parameters = createQueryParametersForBuildingMSRTSummaryForYCBR(correlationId, pseudoRoomTypes);
        int numRowsInserted = crudService.executeUpdateByNativeQuery(queryString, parameters);
        LOGGER.info("ROWS INSERTED INTO SUMMARY TO USE TRX AS SUMMARY = " + numRowsInserted);
        return numRowsInserted;
    }

    public String createQueryStringForYCBRProcessing(String pseudoRoomTypes) {
        String createNonPickupDataSQL = null;

        if (pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.AMS_CALCULATE_NON_PICKED_UP_BLOCKS_USING_SUMMARY_DATA.value())) {
            createNonPickupDataSQL = SQL_IGNORE_GROUP_SOLDS_FROM_GROUP_DATA.toString();
        } else {
            createNonPickupDataSQL = SQL_GET_GROUP_SOLDS_FROM_GROUP_DATA.toString();
        }

        boolean isDayUseTransactionsEnabled = propertyConfigParamService.isDayUsePopulationEnabled();
        String createTransactionDataSQL = isDayUseTransactionsEnabled ? SQL_GET_TRANSACTION_DATA_REQUIRED_FOR_SUMMARY_BUILD_WITH_DAY_USE.toString() : SQL_GET_TRANSACTION_DATA_REQUIRED_FOR_SUMMARY_BUILD.toString();
        String deleteDayUseTransactionsSQL = isDayUseTransactionsEnabled ? SQL_DELETE_DAY_USE_TRANSACTIONS.toString() : StringUtils.EMPTY;
        StringBuilder completeMSRTSummaryQuery = new StringBuilder(StringUtils.EMPTY);

        // Queries for storing All Stage Transaction Data in smaller tables.
        // Create Data for arrivals, departures , NS, Cancellations and Revenue
        // This data is required for MS By Rule as well as Rate Category MSRT Summary creation
        completeMSRTSummaryQuery
                .append(SQL_GET_ALL_MS_PARTICIPATING_IN_MS_BY_RULE)
                .append(createTransactionDataSQL)
                .append(SQL_ACTIVE_TRANS_DATA)
                .append(SQL_ALL_RESERVATION_PER_MS_BY_RULE)
                .append(deleteDayUseTransactionsSQL)
                .append(SQL_ACTIVE_SOLDS_TRANS_DATA)
                .append(SQL_CANCELLED_TRANS_DATA)
                .append(SQL_NO_SHOW_TRANS_DATA)
                .append(SQL_ARRIVALS_TRANS_DATA)
                // departures data is calculated differently for rate code and MS by rule
                .append(SQL_DEPARTURE_DT_TRANS_DT_DATA_FOR_MS_BY_RULE)
                .append(SQL_DEPARTURE_FROM_TRANS_DATA)

                .append(sqlReservationWithoutDeptBuff)
                .append(sqlReservationWithDeptMSByRuleBuff)
                .append(createNonPickupDataSQL)
                .append(SQL_GET_GROUP_SUMMARY_FOR_NON_PICKUP_WITHOUT_DEPARTURES)
                .append(SQL_GET_GROUP_SUMMARY_FOR_NON_PICKUP_WITH_DEPARTURES)
                .append(sqlTransandGroupWithDeptMSByRuleBuff)
                .append(SQL_INSERT_MSRT_SUMMARY_TO_OCCUPANCY_SUMMARY_FOR_TRANS_AND_GROUP_OR_WITHOUT_RC_MAPPING);

        if (StringUtils.isBlank(pseudoRoomTypes)) {
            return completeMSRTSummaryQuery.toString().replace(EXCLUDE_PSEUDO_ROOM_TYPES_PLACEHOLDER, " ");
        }

        return completeMSRTSummaryQuery.toString().replace(EXCLUDE_PSEUDO_ROOM_TYPES_PLACEHOLDER, EXCLUDE_PSEUDO_ROOM_TYPES_QUERY_CLAUSE);


    }

    private Map createQueryParametersForBuildingMSRTSummaryForYCBR(String correlationId, String pseudoRoomTypes) {
        Map<String, Object> map = new HashMap<String, Object>();
        JavaLocalDateInterval businessDateRange = operaUtilityService.getBusinessDateRange();
        java.time.LocalDate businessStartDate = businessDateRange.getStartDate();
        java.time.LocalDate businessEndDate = businessDateRange.getEndDate();
        LocalDate businessDt = operaUtilityService.getBusinessDateFromRawFormatted();

        List<Integer> csatDataLoadMetadataList = operaUtilityService.getDataLoadMetadataForFileType(correlationId, Collections.singletonList(OperaDataLoadTypeCode.CSAT.name()));
        List<Integer> psatDataLoadMetadataList = operaUtilityService.getDataLoadMetadataForFileType(correlationId, Collections.singletonList(OperaDataLoadTypeCode.PSAT.name()));
        map.put("csatDataLoadMetadata", csatDataLoadMetadataList);
        map.put("activeStatusTypes"
                , Arrays.asList(CHECKED_IN.getCode(), RESERVED.getCode(), CHECKED_OUT.getCode()));
        List<String> statusTypesForRevenue = new ArrayList<>(Arrays.asList(CHECKED_IN.getCode(), RESERVED.getCode(), CHECKED_OUT.getCode()));
        map.put("individualStatusTypesForRevenue"
                , statusTypesForRevenue);
        if (propertyConfigParamService.isIncludeCancellationNoShowRevenueEnabled()) {
            statusTypesForRevenue.addAll(Arrays.asList(CANCELLED.getCode(), NO_SHOW.getCode()));
        }
        map.put("cancelledStatusType", CANCELLED.getCode());
        map.put("noShowStatusType", NO_SHOW.getCode());
        map.put("psatDataLoadMetadata", psatDataLoadMetadataList);
        map.put("businessStartDate", new java.sql.Date(businessStartDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()));
        map.put("businessEndDate", new java.sql.Date(businessEndDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()));
        map.put("businessDt", new java.sql.Date(businessDt.toDate().getTime()));
        if (!StringUtils.isBlank(pseudoRoomTypes)) {
            map.put("pseudoRoomTypes", Arrays.stream(pseudoRoomTypes.split(",")).map(String::trim).collect(Collectors.toList()));
        }

        return map;
    }

    public String addNonPickedUpGroupsToDefaultOccupancySummaries(String correlationId) {
        List<YieldCategoryRule> yieldCategoryRules = crudService.findByNamedQuery(YieldCategoryRule.ALL);
        if (yieldCategoryRules.isEmpty()) {
            return "no split market segments, no Stage_Occupancy_Summary records updated";
        }
        List<String> splitMarketSegments = yieldCategoryRules.stream().map(YieldCategoryRule::getMarketCode).distinct().collect(Collectors.toList());
        List<String> analyticalMarketSegments = yieldCategoryRules.stream().map(YieldCategoryRule::getAnalyticalMarketCode).distinct().collect(Collectors.toList());
        List<OperaOccupancySummary> occupancySummariesForSplitMarketSegments = getOccupancySummariesForSplitMarketSegments(analyticalMarketSegments);
        String resortCode = occupancySummariesForSplitMarketSegments.isEmpty() ? "" : occupancySummariesForSplitMarketSegments.get(0).getResort();
        DataLoadMetadata dataLoadMetadata = occupancySummariesForSplitMarketSegments.isEmpty() ? null : occupancySummariesForSplitMarketSegments.get(0).getOperaDataLoadMetadata();
        Map<String, ActivityTotals> transactionTotalsMap = buildTransactionTotalsMap(occupancySummariesForSplitMarketSegments);
        List<RawOccupancySummary> rawOccupancySummaries = getRawOccupancySummariesForCurrentFeed(splitMarketSegments, correlationId);
        List<ActivityTotals> nonPickedUpTotals = buildNonPickedUpTotals(rawOccupancySummaries, transactionTotalsMap);
        int count = 0;
        if (!nonPickedUpTotals.isEmpty()) { // first, try to apply non-picked up blocks to Group AMS (if any)
            Map<String, OperaOccupancySummary> groupOccupancySummaryMap = buildGroupOccupancySummaryMap(occupancySummariesForSplitMarketSegments);
            count += applyNonPickedUpTotalsToGroupSummaries(nonPickedUpTotals, groupOccupancySummaryMap, resortCode, dataLoadMetadata, analyticalMarketSegments);
            if (count > 0) { // some of the non-picked up totals for been applied to Group MS's, must recalculate remaining non-picked up totals
                occupancySummariesForSplitMarketSegments = getOccupancySummariesForSplitMarketSegments(analyticalMarketSegments);
                transactionTotalsMap = buildTransactionTotalsMap(occupancySummariesForSplitMarketSegments);
                nonPickedUpTotals = buildNonPickedUpTotals(rawOccupancySummaries, transactionTotalsMap);
            }
        }
        if (!nonPickedUpTotals.isEmpty()) { // if there are any remaining non-picked up blocks, apply them to Default AMS
            Map<String, OperaOccupancySummary> defaultOccupancySummaryMap = buildDefaultOccupancySummaryMap(occupancySummariesForSplitMarketSegments);
            count += applyNonPickedUpTotalsToDefaultSummaries(nonPickedUpTotals, defaultOccupancySummaryMap, resortCode, dataLoadMetadata);
        }
        return "updated non-picked up totals for " + count + " Stage_Occupancy_Summary records";
    }

    private int applyNonPickedUpTotalsToGroupSummaries(List<ActivityTotals> nonPickedUpTotals, Map<String, OperaOccupancySummary> groupOccupancySummaryMap,
                                                       String resortCode, DataLoadMetadata dataLoadMetadata, List<String> analyticalMarketSegments) {
        List<OperaOccupancySummary> updatedSummaries = new ArrayList<>();
        Map<String, Integer> nonPickedUpFromGroupBlockMap = buildNonPickedUpFromGroupBlockMap();
        for (ActivityTotals total : nonPickedUpTotals) {
            String key = createKey(total.getOccupancyDate(), total.getMarketSegment(), total.getRoomType());
            Integer nonPickedUpRoomsFromGroupBlock = nonPickedUpFromGroupBlockMap.get(key);
            if (nonPickedUpRoomsFromGroupBlock != null) {
                OperaOccupancySummary summary = groupOccupancySummaryMap.get(key);
                if (summary == null) {
                    if (analyticalMarketSegments.contains(total.getMarketSegment() + "_G")) {
                        summary = createOccupancySummary(total.getOccupancyDate(), total.getMarketSegment() + "_G", total.getRoomType(), resortCode, dataLoadMetadata);
                        groupOccupancySummaryMap.put(key, summary);
                    } else {
                        continue; // even though there are group blocks, there is no group AMS, so ignore this discrepancy (it will be applied to *_DEF ANS)
                    }
                }
                summary.setRoomsSold(summary.getRoomsSold() + nonPickedUpRoomsFromGroupBlock);
                summary.setRoomRevenue(summary.getRoomRevenue().add(total.getADR().multiply(new BigDecimal(nonPickedUpRoomsFromGroupBlock))));
                updatedSummaries.add(summary);
            }
        }
        if (!updatedSummaries.isEmpty()) {
            crudService.save(updatedSummaries);
        }
        return updatedSummaries.size();
    }

    private Map<String, Integer> buildNonPickedUpFromGroupBlockMap() {
        Map<Integer, String> groupMarketSegmentMap = createGroupMarketSegmentMap();
        List<OperaGroupBlock> groupBlocks = crudService.findAll(OperaGroupBlock.class);
        Map<String, Integer> nonPickedUpMap = new HashMap();
        groupBlocks.forEach(groupBlock -> addToMap(groupBlock, groupMarketSegmentMap, nonPickedUpMap));
        return nonPickedUpMap;
    }

    private void addToMap(OperaGroupBlock groupBlock, Map<Integer, String> groupMarketSegmentMap, Map<String, Integer> nonPickedUpMap) {
        String key = createKey(groupBlock, groupMarketSegmentMap);
        Integer nonPickedUpBlocks = nonPickedUpMap.get(key);
        if (nonPickedUpBlocks == null) {
            nonPickedUpBlocks = 0;
        }
        nonPickedUpBlocks += groupBlock.getBlock() - groupBlock.getPickup();
        if (nonPickedUpBlocks < 0) { // if pickup > block, assume pickup == block
            nonPickedUpBlocks = 0;
        }
        nonPickedUpMap.put(key, nonPickedUpBlocks);
    }

    private Map<Integer, String> createGroupMarketSegmentMap() {
        List<OperaGroupMaster> groupMasters = crudService.findAll(OperaGroupMaster.class);
        return groupMasters.stream().collect(Collectors.toMap(groupMaster -> groupMaster.getGroupId(),
                groupMaster -> groupMaster.getMarketSegment()));
    }

    private String createKey(OperaGroupBlock groupBlock, Map<Integer, String> groupMarketSegmentMap) {
        String marketSegment = groupMarketSegmentMap.get(groupBlock.getGroupId());
        if (marketSegment == null) {
            marketSegment = "";
        }
        return createKey(groupBlock.getBlockDate(), toHotelMarketSegment(marketSegment), groupBlock.getRoomType());
    }

    private int applyNonPickedUpTotalsToDefaultSummaries(List<ActivityTotals> nonPickedUpTotals, Map<String, OperaOccupancySummary> defaultOccupancySummaryMap,
                                                         String resortCode, DataLoadMetadata dataLoadMetadata) {
        List<OperaOccupancySummary> updatedSummaries = new ArrayList<>();
        for (ActivityTotals total : nonPickedUpTotals) {
            String key = createKey(total.getOccupancyDate(), total.getMarketSegment(), total.getRoomType());
            OperaOccupancySummary summary = defaultOccupancySummaryMap.get(key);
            if (summary == null) {
                summary = createOccupancySummary(total.getOccupancyDate(), total.getMarketSegment() + "_DEF", total.getRoomType(), resortCode, dataLoadMetadata);
                defaultOccupancySummaryMap.put(key, summary);
            }
            int adjustedSolds = summary.getRoomsSold() + total.getRoomsSold();
            if (adjustedSolds < 0) {
                adjustedSolds = 0;
            }
            summary.setRoomsSold(adjustedSolds);
            BigDecimal adjustedRevenue = summary.getRoomRevenue().add(total.getRoomRevenue());
            if (adjustedRevenue.intValue() < 0) {
                adjustedRevenue = BigDecimal.ZERO;
            }
            summary.setRoomRevenue(adjustedRevenue);
            updatedSummaries.add(summary);
        }
        if (!updatedSummaries.isEmpty()) {
            crudService.save(updatedSummaries);
        }
        return updatedSummaries.size();
    }

    private OperaOccupancySummary createOccupancySummary(LocalDate occupancyDate, String marketSegment, String roomType, String resortCode, DataLoadMetadata dataLoadMetadata) {
        OperaOccupancySummary summary = new OperaOccupancySummary();
        summary.setOccupancyDate(occupancyDate);
        summary.setMarketCode(marketSegment);
        summary.setRoomType(roomType);
        summary.setRoomRevenue(BigDecimal.ZERO);
        summary.setFoodRevenue(BigDecimal.ZERO);
        summary.setTotalRevenue(BigDecimal.ZERO);
        summary.setResort(resortCode);
        summary.setRoomsSold(0);
        summary.setMarketSegmentId(getMarketSegmentId(marketSegment));
        summary.setAccomTypeId(getAccomTypeId(roomType));
        summary.setCancelledRooms(0);
        summary.setNoShowRooms(0);
        summary.setOperaDataLoadMetadata(dataLoadMetadata);
        summary.setOutOfOrderRooms(0);
        summary.setOutOfServiceRooms(0);
        summary.setPhysicalRooms(0);
        summary.setReservationType(null);
        summary.setRoomArrivals(0);
        summary.setRoomDepartures(0);
        return summary;
    }

    private Integer getMarketSegmentId(String marketSegmentCode) {
        MktSeg entity = crudService.findByNamedQuerySingleResult(MktSeg.BY_CODE, QueryParameter.with("code", marketSegmentCode).parameters());
        if (entity == null) {
            entity = new MktSeg();
            entity.setCode(marketSegmentCode);
            entity.setPropertyId(PacmanWorkContextHelper.getPropertyId());
            entity.setDescription(marketSegmentCode);
            entity.setName(marketSegmentCode);
            entity.setEditable(1);
            entity.setStatusId(Constants.ACTIVE_STATUS_ID);
            entity.setCreatedByUserId(1);
            entity.setLastUpdatedByUserId(1);
            entity.setCreateDate(new Date());
            entity.setLastUpdatedDate(new Date());
            entity = crudService.save(entity);
        }
        return entity.getId();
    }

    private Integer getAccomTypeId(String roomType) {
        return crudService.findByNamedQuerySingleResult(AccomType.ID_BY_ACCOM_TYPE_CODE, QueryParameter.with("accomTypeCode", roomType).parameters());
    }

    private Map<Integer, String> buildDefaultMarketSegmentMap() {
        Map<Integer, String> map = new HashMap<Integer, String>();
        List<Integer> mktSegIds = crudService.findByNamedQuery(MktSeg.GET_DEFAULT_MKT_SEG_IDS);
        mktSegIds.forEach(mktSegId -> map.put(mktSegId, "AMS Default"));
        return map;
    }

    private Map<Integer, String> buildGroupMarketSegmentMap() {
        Map<Integer, String> map = new HashMap<Integer, String>();
        List<Integer> mktSegIds = crudService.findByNamedQuery(MktSeg.GET_GROUP_MKT_SEG_IDS);
        mktSegIds.forEach(mktSegId -> map.put(mktSegId, "AMS Group"));
        return map;
    }

    private Map<String, OperaOccupancySummary> buildDefaultOccupancySummaryMap(List<OperaOccupancySummary> occupancySummaries) {
        Map<Integer, String> defaultMarketSegmentMap = buildDefaultMarketSegmentMap();
        Map<String, OperaOccupancySummary> map = new HashMap<>();
        if (defaultMarketSegmentMap.isEmpty()) {
            return map;
        }
        occupancySummaries.forEach(summary -> updateMap(summary, map, defaultMarketSegmentMap));
        return map;
    }

    private Map<String, OperaOccupancySummary> buildGroupOccupancySummaryMap(List<OperaOccupancySummary> occupancySummaries) {
        Map<Integer, String> groupMarketSegmentMap = buildGroupMarketSegmentMap();
        Map<String, OperaOccupancySummary> map = new HashMap<>();
        if (groupMarketSegmentMap.isEmpty()) {
            return map;
        }
        occupancySummaries.forEach(summary -> updateMap(summary, map, groupMarketSegmentMap));
        return map;
    }

    private void updateMap(OperaOccupancySummary summary, Map<String, OperaOccupancySummary> map, Map<Integer, String> marketSegmentMap) {
        if (marketSegmentMap.get(summary.getMarketSegmentId()) == null) {
            return;
        }
        LocalDate occupancyDate = summary.getOccupancyDate();
        String marketSegment = summary.getMarketCode();
        String roomType = summary.getRoomType();
        String key = createKey(occupancyDate, toHotelMarketSegment(marketSegment), roomType);
        map.put(key, summary);
    }

    private List<ActivityTotals> buildNonPickedUpTotals(List<RawOccupancySummary> rawOccupancySummaries, Map<String, ActivityTotals> transactionTotalsMap) {
        return rawOccupancySummaries.stream()
                .map(rawSummary -> createNonPickedUpTotals(rawSummary, transactionTotalsMap))
                .filter(total -> total.getRoomsSold() != 0 || total.getRoomRevenue().compareTo(BigDecimal.ZERO) != 0)
                .collect(Collectors.toList());
    }

    private ActivityTotals createNonPickedUpTotals(RawOccupancySummary rawSummary, Map<String, ActivityTotals> transactionTotalsMap) {
        ActivityTotals results = new ActivityTotals();
        LocalDate occupancyDate = new LocalDate(rawSummary.getOccupancyDate());
        results.setOccupancyDate(occupancyDate);
        results.setRoomType(rawSummary.getRoomType());
        results.setMarketSegment(rawSummary.getMarketCode());
        String key = createKey(occupancyDate, rawSummary.getMarketCode(), rawSummary.getRoomType());
        ActivityTotals transactionTotals = transactionTotalsMap.get(key);
        if (transactionTotals == null) {
            return results;
        }
        int roomsSold = Integer.parseInt(rawSummary.getRoomsSold());
        BigDecimal roomRevenue = new BigDecimal(rawSummary.getRoomRevenue());
        results.setRoomsSold(roomsSold - transactionTotals.getRoomsSold());
        results.setRoomRevenue(roomRevenue.subtract(transactionTotals.getRoomRevenue()));
        return results;
    }

    private List<RawOccupancySummary> getRawOccupancySummariesForCurrentFeed(List<String> splitMarketSegments, String correlationId) {
        List<Integer> csatDataLoadMetadataList = operaUtilityService.getDataLoadMetadataForFileType(correlationId, Arrays.asList(OperaDataLoadTypeCode.CSAT.name()));
        List<RawOccupancySummary> summaries = crudService.findByNamedQuery(RawOccupancySummary.FIND_LATEST_AMS,
                QueryParameter.with("analyticalMarketSegments", splitMarketSegments)
                        .and("dataLoadMetadataIds", csatDataLoadMetadataList).parameters());
        return summaries;
    }

    private Map<String, ActivityTotals> buildTransactionTotalsMap(List<OperaOccupancySummary> occupancySummaries) {
        Map<String, ActivityTotals> map = new HashMap<String, ActivityTotals>();
        occupancySummaries.forEach(summary -> addToTransactionsTotalMap(map, summary));
        return map;
    }

    private void addToTransactionsTotalMap(Map<String, ActivityTotals> map, OperaOccupancySummary summary) {
        String key = createKey(summary.getOccupancyDate(), toHotelMarketSegment(summary.getMarketCode()), summary.getRoomType());
        ActivityTotals totals = map.get(key);
        if (totals == null) {
            totals = new ActivityTotals();
            map.put(key, totals);
        }
        totals.addSummary(summary);
    }

    private String createKey(LocalDate occupancyDate, String marketCode, String roomType) {
        StringBuilder buffer = new StringBuilder();
        buffer.append(DateUtil.formatDate(occupancyDate.toDate(), DateUtil.DEFAULT_DATE_FORMAT)).append("^")
                .append(marketCode).append("^").append(roomType);
        return buffer.toString();
    }

    private String toHotelMarketSegment(String marketSegment) {
        List<String> suffixes = Arrays.asList(AnalyticalMarketSegmentAttribute.values()).stream()
                .map(AnalyticalMarketSegmentAttribute::getSuffix)
                .collect(Collectors.toList());
        suffixes.add("DEF");
        for (String suffix : suffixes) {
            if (marketSegment.endsWith("_" + suffix)) {
                return marketSegment.substring(0, marketSegment.lastIndexOf("_"));
            }
        }
        return marketSegment;
    }

    private List<OperaOccupancySummary> getOccupancySummariesForSplitMarketSegments(List<String> analyticalMarketSegments) {
        List<OperaOccupancySummary> summaries = crudService.findByNamedQuery(OperaOccupancySummary.FIND_LATEST_AMS,
                QueryParameter.with("analyticalMarketSegments", analyticalMarketSegments).parameters());
        return summaries;
    }

    class ActivityTotals {
        private LocalDate occupancyDate;
        private String roomType;
        private String marketSegment;
        private int roomsSold = 0;
        private BigDecimal roomRevenue = BigDecimal.ZERO;

        ActivityTotals() {

        }

        public LocalDate getOccupancyDate() {
            return occupancyDate;
        }

        public void setOccupancyDate(LocalDate occupancyDate) {
            this.occupancyDate = occupancyDate;
        }

        public String getRoomType() {
            return roomType;
        }

        public void setRoomType(String roomType) {
            this.roomType = roomType;
        }

        public String getMarketSegment() {
            return marketSegment;
        }

        public void setMarketSegment(String marketSegment) {
            this.marketSegment = marketSegment;
        }

        public int getRoomsSold() {
            return roomsSold;
        }

        public void setRoomsSold(int roomsSold) {
            this.roomsSold = roomsSold;
        }

        public BigDecimal getRoomRevenue() {
            return roomRevenue;
        }

        public void setRoomRevenue(BigDecimal roomRevenue) {
            this.roomRevenue = roomRevenue;
        }

        public BigDecimal getADR() {
            return roomRevenue.divide(new BigDecimal(roomsSold), 4, RoundingMode.HALF_EVEN);
        }

        void addSummary(OperaOccupancySummary summary) {
            occupancyDate = summary.getOccupancyDate();
            roomType = summary.getRoomType();
            marketSegment = toHotelMarketSegment(summary.getMarketCode());
            roomsSold += summary.getRoomsSold();
            roomRevenue = roomRevenue.add(summary.getRoomRevenue());
        }

    }
}
