package com.ideas.tetris.pacman.services.overbooking.service;

import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decisiondelivery.entity.PaceOverbookingAccomUpload;
import com.ideas.tetris.pacman.services.decisiondelivery.entity.PaceOverbookingPropertyUpload;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.util.Date;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class OverbookingDataCopyForUploadWindowService {

    private static final Logger LOGGER = Logger.getLogger(OverbookingDataCopyForUploadWindowService.class);

    public static final String START_DATE = "startDate";
    public static final String END_DATE = "endDate";
    public static final String PROPERTY_ID = "propertyId";
    @TenantCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("tenantCrudServiceBean")
    CrudService crudService;

    @Autowired
	private DateService dateService;

    @Autowired
	private PacmanConfigParamsService configService;


    public void getDataForUpload(boolean isCDP) {
        log("Overbooking data copy started here.");
        Date startDate = dateService.getOptimizationWindowStartDate();
        String operationType = Constants.BDE;
        if (isCDP) {
            operationType = Constants.CDP;
        }
        Date endDate = dateService.getDecisionUploadEndDate(operationType);
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        saveOverbookingAtProperty(startDate, endDate, propertyId);
        saveOverbookingAtRoomType(startDate, endDate, propertyId);
        log("Overbooking data copy ends here.");
    }

    private void saveOverbookingAtProperty(Date startDate, Date endDate, Integer propertyId) {
        log("Property Level data copy started here.");
        int paceOvrbkPropertyUploadCount = crudService.executeUpdateByNativeQuery(PaceOverbookingPropertyUpload.propertyDataCopyQuery.toString(),
                QueryParameter.with(PROPERTY_ID, propertyId).and(START_DATE, startDate).and(END_DATE, endDate).parameters());
        log("Number of records inserted into PACE_Ovrbk_Property_Upload table are " + paceOvrbkPropertyUploadCount +
                " and Property Level data copy ends here.");
    }

    private void saveOverbookingAtRoomType(Date startDate, Date endDate, Integer propertyId) {
        log("Room type Level data copy started here.");
        int paceOvrkAccomUploadCount = crudService.executeUpdateByNativeQuery(PaceOverbookingAccomUpload.roomTypeDataCopyQuery.toString(),
                QueryParameter.with(PROPERTY_ID, propertyId).and(START_DATE, startDate).and(END_DATE, endDate).parameters());
        log("Number of records inserted into o PACE_Ovrbk_Accom_Upload table are " + paceOvrkAccomUploadCount +
                " and Room type Level data copy ends here.");
    }

    private void log(String msg) {
        LOGGER.info(msg + " - " + new Date());
    }
}