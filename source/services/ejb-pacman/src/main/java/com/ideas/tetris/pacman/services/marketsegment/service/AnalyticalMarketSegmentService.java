package com.ideas.tetris.pacman.services.marketsegment.service;


import com.ideas.tetris.pacman.common.configparams.*;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.threadpool.NamedDefaultThreadFactory;
import com.ideas.tetris.pacman.common.utils.pojo.Pair;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductRateCode;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesUtils;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.MktSegAccomActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight;
import com.ideas.tetris.pacman.services.businessgroup.service.BusinessGroupService;
import com.ideas.tetris.pacman.services.configautomation.dto.MarketSegmentIdPair;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.groupblock.GroupBlockMaster;
import com.ideas.tetris.pacman.services.independentproducts.repository.IndependentProductsRepository;
import com.ideas.tetris.pacman.services.independentproducts.service.IndependentProductsService;
import com.ideas.tetris.pacman.services.marketsegment.MarketSegmentCasinoCategoryType;
import com.ideas.tetris.pacman.services.marketsegment.MktSegChangeType;
import com.ideas.tetris.pacman.services.marketsegment.dto.YieldCategoryRuleDTO;
import com.ideas.tetris.pacman.services.marketsegment.entity.*;
import com.ideas.tetris.pacman.services.marketsegment.repository.MarketSegmentRepository;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.virtualproperty.service.VirtualPropertyMappingService;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.rest.mapper.RestClient;
import com.ideas.tetris.platform.common.rest.mapper.RestEndpoints;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import com.ideas.tetris.platform.services.daoandentities.entity.VirtualPropertyMapping;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import javax.ws.rs.QueryParam;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED;
import static com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight.*;
import static com.ideas.tetris.pacman.services.marketsegment.entity.MarketSegmentProductMapping.BY_PRODUCT;
import static com.ideas.tetris.pacman.util.Executor.executeIfFalse;
import static com.ideas.tetris.pacman.util.Runner.runIfFalse;
import static com.ideas.tetris.pacman.util.Streams.stream;
import static com.ideas.tetris.platform.common.crudservice.QueryParameter.with;
import static java.util.Collections.*;
import static java.util.Objects.isNull;
import static java.util.function.Predicate.not;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang.StringUtils.EMPTY;

@Component
@Transactional
public class AnalyticalMarketSegmentService {
    public static final int EDITABLE = 1;
    public static final String ANALYTICAL_MARKET_SEGMENT_MAPPING_COMPLETE = GUIConfigParamName.
            POPULATION_ANALYTICAL_MARKET_SEGMENT_MAPPING_COMPLETE.getParameterName();
    public static final String NO_AMS_MAPPINGS_FOUND = "No AMS mappings found";
    public static final String TIER1 = "Tier1";
    public static final String TIER2 = "Tier2";
    public static final String TIER3 = "Tier3";
    private static final String LOYALTY = "LOYALTY";
    private static final String BAR = "BAR";
    public static final List<String> SPECIAL_IPP_MARKET_CODES = List.of(TIER1, TIER2, TIER3, LOYALTY, BAR);
    static final String TOTAL_ROOM_SOLD_SQL = "SELECT sum(Rooms_Sold) FROM Ams_Occupancy_Summary WHERE Rate_Code not like '%share%'";
    private static final String DEFAULT_AFFIX = "_DEF";
    private static final Logger LOGGER = Logger.getLogger(AnalyticalMarketSegmentService.class);
    private static final BigDecimal ZERO_SCALE_2 = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
    private static final Integer BOOKING_BLOCK_PCT_DEFAULT = 0;
    private static final Integer BOOKING_BLOCK_PCT_IS_BLOCK = 100;
    private static final String MARKET_CODE = "marketCode";
    private static final String RATE_CODE = "rateCode";
    protected static final String MAPPED_MARKET_CODE = "mappedMarketCode";
    protected static final String MAPPED_MARKET_CODES = "mappedMarketCodes";
    private static final String MARKET_CODES = "marketCodes";
    private static final String PROPERTY_ID = "propertyId";
    private static final String OCCUPANCY_DATE = "occupancyDate";
    private static final String TOTAL_RATE_ENABLED = "totalRateEnabled";
    private static final Set<Integer> STRAIGHT_OR_DEFAULT_RANKS = Stream.of(RateCodeTypeEnum.DEFAULT, RateCodeTypeEnum.ALL)
            .map(RateCodeTypeEnum::getRank).collect(Collectors.toSet());
    @TenantCrudServiceBean.Qualifier
	@Qualifier("tenantCrudServiceBean")
    @Autowired
    CrudService crudService;

    @Autowired
    PacmanConfigParamsService configParamsServiceLocal;

    @Autowired
    MarketSegmentPopulationResource populationResource;

    @Autowired
    RestClient restClient;

    @Autowired
    PropertyService propertyService;

    @Autowired
    private BusinessGroupService businessGroupService;

    @Autowired
    IndependentProductsService independentProductsService;

    @Autowired
	private DateService dateService;
    @Autowired
	private MarketSegmentRepository marketSegmentRepository;
    @Autowired
	private MarketSegmentService marketSegmentService;
    @Autowired
    IndependentProductsRepository independentProductsRepository;
    @Autowired
	private AnalyticalMarketSegmentRepository amsRepository;
    @Autowired
    VirtualPropertyMappingService virtualPropertyMappingService;

    public String clearAssignments(String propertyId) {
        crudService.executeUpdateByNamedQuery(AnalyticalMarketSegment.DELETE_ALL);
        crudService.executeUpdateByNamedQuery(AnalyticalMarketSegmentAudit.DELETE_ALL);
        crudService.executeUpdateByNamedQuery(MarketSegmentMaster.DELETE_ALL);
        return propertyId;
    }

    @SuppressWarnings("unchecked")
    public List<AnalyticalMarketSegmentSummary> getUnassignedGroupMarketSegments() {
        final long start = System.currentTimeMillis();
        Map<String, AnalyticalMarketSegmentSummary> nonGroupSummaries =
                crudService.findByNamedQuery(AnalyticalMarketSegmentSummary.ALL_NON_GROUP)
                        .stream().map(o -> (AnalyticalMarketSegmentSummary) o)
                        .collect(Collectors.toMap(AnalyticalMarketSegmentSummary::getMarketCode, value -> value));

        List<AnalyticalMarketSegmentSummary> group = crudService.findByNamedQuery(
                AnalyticalMarketSegmentSummary.GROUP);

        for (AnalyticalMarketSegmentSummary groupSummary : group) {
            crudService.getEntityManager().detach(groupSummary); // detach to avoid updates
            AnalyticalMarketSegmentSummary nonGroupSummary = nonGroupSummaries.get(groupSummary.getMarketCode());
            if (nonGroupSummary == null) {
                groupSummary.setHotelPercent(1.0);
            } else if (nonGroupSummary.getRoomsSold() > 0.0) {
                groupSummary.setHotelPercent((double) groupSummary.getPickup() / (double) nonGroupSummary.getRoomsSold());
            } else {
                groupSummary.setHotelPercent(0.0);
            }
        }

        group.sort(new SummaryPercentComparator());
        LOGGER.info(String.format("Found %d unassigned group level summaries in %dms", group.size(),
                (System.currentTimeMillis() - start)));
        return group;
    }

    @SuppressWarnings("unchecked")
    public List<AnalyticalMarketSegmentSummary> getUnassignedSharedMarketSegments() {
        final long start = System.currentTimeMillis();

        List<AnalyticalMarketSegmentSummary> list = new ArrayList<>();

        List<AnalyticalMarketSegmentSummary> found = crudService.findByNamedQuery(
                AnalyticalMarketSegmentSummary.SHARED_NON_GROUP);

        if (!CollectionUtils.isEmpty(found)) {
            List<String> marketCodes = found.stream().map(AnalyticalMarketSegmentSummary::getMarketCode).collect(Collectors.toList());
            Integer totalRoomsSold = getTotalRoomsSold();

            Map<String, List<RateCodeSummary>> rateCodesByMarketCode = getRateCodes(marketCodes).stream().collect(Collectors.groupingBy(RateCodeSummary::getMarketCode));

            for (AnalyticalMarketSegmentSummary summary : found) {
                List<RateCodeSummary> rateCodes = rateCodesByMarketCode.get(summary.getMarketCode()) == null ? new ArrayList<>() : rateCodesByMarketCode.get(summary.getMarketCode());

                if (hasNonEmptyRateCode(rateCodes)) {
                    crudService.getEntityManager().detach(summary); // detach to avoid updates
                    if (summary.getRoomsSold() != 0) {
                        summary.setAverageDailyRate(summary.getRoomRevenue() / summary.getRoomsSold());
                    }
                    if (totalRoomsSold > 0) {
                        summary.setHotelPercent((double) summary.getRoomsSold() / (double) totalRoomsSold);
                    } else {
                        summary.setHotelPercent(0.0);
                    }
                    list.add(summary);
                }
            }
            list.sort(new SummaryPercentComparator());
        }

        LOGGER.info(String.format("Found %d unassigned shared summaries in %dms", list.size(),
                (System.currentTimeMillis() - start)));
        return list;
    }

    private boolean hasNonEmptyRateCode(List<RateCodeSummary> rateCodes) {
        for (RateCodeSummary rateCodeSummary : rateCodes) {
            if (StringUtils.isNotBlank(rateCodeSummary.getRateCode())) {
                return true;
            }
        }
        return false;
    }

    @SuppressWarnings("unchecked")
    public List<AnalyticalMarketSegmentSummary> getUnassignedIndividualMarketSegments() {
        final long start = System.currentTimeMillis();
        List<AnalyticalMarketSegmentSummary> list = crudService.findByNamedQuery(
                AnalyticalMarketSegmentSummary.INDIVIDUAL_NON_GROUP);

        if (!CollectionUtils.isEmpty(list)) {
            Integer totalRoomsSold = getTotalRoomsSold();

            for (AnalyticalMarketSegmentSummary summary : list) {
                crudService.getEntityManager().detach(summary); // detach to avoid updates
                if (summary.getRoomsSold() != 0) {
                    summary.setAverageDailyRate(summary.getRoomRevenue() / summary.getRoomsSold());
                }
                if (totalRoomsSold > 0) {
                    summary.setHotelPercent((double) summary.getRoomsSold() / (double) totalRoomsSold);
                } else {
                    summary.setHotelPercent(0.0);
                }
            }

            list.sort(new SummaryPercentComparator());
        }

        LOGGER.info(String.format("Found %d unassigned individual summaries in %dms", (null != list) ? list.size() : 0,
                (System.currentTimeMillis() - start)));
        return list;
    }

    @SuppressWarnings("unchecked")
    public List<AnalyticalMarketSegment> getAssignedMarketSegments() {
        List<AnalyticalMarketSegment> list = getAllSortedByRank();
        final Map<Integer, ForecastActivityType> activityTypeById = getForecastActivityTypesById();

        final Map<String, List<MarketSegmentMaster>> marketSegmentMastersByMarketCode =
                getMarketSegmentMastersByMarketCode();

        final Map<String, MktSeg> mktSegMap = getMktSegByCode();

        for (AnalyticalMarketSegment ams : list) {
            Collection<MarketSegmentMaster> collection = marketSegmentMastersByMarketCode.get(ams.getMappedMarketCode());
            if (!CollectionUtils.isEmpty(collection)) {
                MarketSegmentMaster msm = collection.iterator().next();
                ams.setBlockPercent(msm.getBookingBlockPc());
                if (msm.getForecastActivityTypeId() != null) {
                    ams.setForecastActivityType(activityTypeById.get(msm.getForecastActivityTypeId()));
                }
            }
            MktSeg mktSeg = mktSegMap.get(ams.getMappedMarketCode());
            if (ams.getForecastActivityType() == null && ams.getMappedMarketCode().endsWith(DEFAULT_AFFIX)) {
                if (mktSeg != null && mktSeg.getMktSegDetailsProposed() != null && mktSeg.getMktSegDetailsProposed().getForecastActivityType() != null) {
                    ams.setForecastActivityType(mktSeg.getMktSegDetailsProposed().getForecastActivityType());
                } else if (mktSeg != null && mktSeg.getMktSegDetails() != null && mktSeg.getMktSegDetails().getForecastActivityType() != null) {
                    ams.setForecastActivityType(mktSeg.getMktSegDetails().getForecastActivityType());
                }
            }
            if (ams.getAttribute() == null && ams.getMappedMarketCode().endsWith(DEFAULT_AFFIX)) {
                if (mktSeg != null && mktSeg.getMktSegDetailsProposed() != null) {
                    ams.setAttribute(AnalyticalMarketSegmentAttribute.findFromMarketSegmentDetailProposed(mktSeg.getMktSegDetailsProposed()));
                } else if (mktSeg != null && mktSeg.getMktSegDetails() != null) {
                    ams.setAttribute(AnalyticalMarketSegmentAttribute.findFromMarketSegmentDetail(mktSeg.getMktSegDetails()));
                } else if (collection != null && collection.size() == 1) {
                    MarketSegmentMaster msm = collection.iterator().next();
                    ams.setAttribute(AnalyticalMarketSegmentAttribute.findFromMarketSegmentMaster(msm));
                }
                crudService.save(ams);
            }
        }
        return list;
    }

    public List<AnalyticalMarketSegment> getAssignedMarketSegmentsOptimized() {
        WorkContextType wc = PacmanWorkContextHelper.getWorkContext();
        ThreadPoolExecutor executor = new ThreadPoolExecutor(4, 8, 0L, TimeUnit.MILLISECONDS,
                new SynchronousQueue<>(), new NamedDefaultThreadFactory("market-segment-screen-optimization"));

        CompletableFuture<List<AnalyticalMarketSegment>> listFuture = supplyWithContext(wc, this::getAllSortedByRank, executor);
        CompletableFuture<Map<Integer, ForecastActivityType>> activityTypeByIdFuture = supplyWithContext(wc, this::getForecastActivityTypesById, executor);
        CompletableFuture<Map<String, List<MarketSegmentMaster>>> marketSegmentMastersByMarketCodeFuture = supplyWithContext(wc, this::getMarketSegmentMastersByMarketCode, executor);
        CompletableFuture<Map<String, MktSeg>> getMktSegByCodeFuture = supplyWithContext(wc, this::getMktSegByCode, executor);

        CompletableFuture.allOf(
                listFuture, activityTypeByIdFuture, marketSegmentMastersByMarketCodeFuture, getMktSegByCodeFuture
        ).join();

        try {
            List<AnalyticalMarketSegment> list = listFuture.get();
            Map<Integer, ForecastActivityType> activityTypeById = activityTypeByIdFuture.get();
            Map<String, List<MarketSegmentMaster>> marketSegmentMastersByMarketCode = marketSegmentMastersByMarketCodeFuture.get();
            Map<String, MktSeg> mktSegMap = getMktSegByCodeFuture.get();
            List<AnalyticalMarketSegment> amsToSave = Collections.synchronizedList(new ArrayList<>());
            list.parallelStream().forEach(ams -> {
                Collection<MarketSegmentMaster> collection = marketSegmentMastersByMarketCode.get(ams.getMappedMarketCode());
                if (!CollectionUtils.isEmpty(collection)) {
                    MarketSegmentMaster msm = collection.iterator().next();
                    ams.setBlockPercent(msm.getBookingBlockPc());
                    if (msm.getForecastActivityTypeId() != null) {
                        ams.setForecastActivityType(activityTypeById.get(msm.getForecastActivityTypeId()));
                    }
                }
                MktSeg mktSeg = mktSegMap.get(ams.getMappedMarketCode());
                if (ams.getForecastActivityType() == null && ams.getMappedMarketCode().endsWith(DEFAULT_AFFIX)) {
                    if (mktSeg != null && mktSeg.getMktSegDetailsProposed() != null && mktSeg.getMktSegDetailsProposed().getForecastActivityType() != null) {
                        ams.setForecastActivityType(mktSeg.getMktSegDetailsProposed().getForecastActivityType());
                    } else if (mktSeg != null && mktSeg.getMktSegDetails() != null && mktSeg.getMktSegDetails().getForecastActivityType() != null) {
                        ams.setForecastActivityType(mktSeg.getMktSegDetails().getForecastActivityType());
                    }
                }
                if (ams.getAttribute() == null && ams.getMappedMarketCode().endsWith(DEFAULT_AFFIX)) {
                    if (mktSeg != null && mktSeg.getMktSegDetailsProposed() != null) {
                        ams.setAttribute(AnalyticalMarketSegmentAttribute.findFromMarketSegmentDetailProposed(mktSeg.getMktSegDetailsProposed()));
                    } else if (mktSeg != null && mktSeg.getMktSegDetails() != null) {
                        ams.setAttribute(AnalyticalMarketSegmentAttribute.findFromMarketSegmentDetail(mktSeg.getMktSegDetails()));
                    } else if (collection != null && collection.size() == 1) {
                        MarketSegmentMaster msm = collection.iterator().next();
                        ams.setAttribute(AnalyticalMarketSegmentAttribute.findFromMarketSegmentMaster(msm));
                    }
                    amsToSave.add(ams);
                }
            });
            crudService.save(amsToSave);
            return list;

        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException("Error fetching data in parallel", e);
        } finally {
            executor.shutdown();
        }
    }

    private <T> CompletableFuture<T> supplyWithContext(WorkContextType wc, Supplier<T> supplier, ThreadPoolExecutor executor) {
        return CompletableFuture.supplyAsync(() -> {
            PacmanWorkContextHelper.setWorkContext(wc);
            return supplier.get();
        }, executor);
    }

    public List<AnalyticalMarketSegment> getAllSortedByRank() {
        return crudService.findByNamedQuery(AnalyticalMarketSegment.ALL_SORTED_BY_RANK);
    }

    private Map<String, MktSeg> getMktSegByCode() {
        final List<MktSeg> mktSegList = crudService.findAll(MktSeg.class);
        return mktSegList.stream().collect(Collectors.toMap(MktSeg::getCode, Function
                .identity()));
    }

    private Map<String, List<MarketSegmentMaster>> getMarketSegmentMastersByMarketCode() {
        final List<MarketSegmentMaster> marketSegmentMasters = crudService.findAll(MarketSegmentMaster.class);
        return marketSegmentMasters.stream().collect(Collectors.groupingBy(MarketSegmentMaster::getCode));
    }

    public Map<Integer, ForecastActivityType> getForecastActivityTypesById() {
        final List<ForecastActivityType> forecastActivityTypes = crudService.findAll(ForecastActivityType.class);
        return forecastActivityTypes.stream().collect(Collectors.toMap
                (ForecastActivityType::getId, Function.identity()));
    }

    public int assignMarketSegments(List<AnalyticalMarketSegmentSummary> marketSegments,
                                    AnalyticalMarketSegmentAttribute attribute, int forecastActivityTypeId) {
        return assignMarketSegments(marketSegments, attribute, forecastActivityTypeId, false, null);
    }


    public int assignMarketSegments(List<AnalyticalMarketSegmentSummary> marketSegments,
                                    AnalyticalMarketSegmentAttribute attribute, int forecastActivityTypeId, boolean complimentary, Product baseProduct) {
        LOGGER.info("received " + marketSegments.size() + " marketSegments to save");
        Map<Integer, ForecastActivityType> forecastActivityTypesById = getForecastActivityTypesById();
        ForecastActivityType forecastActivityType = forecastActivityTypesById.get(forecastActivityTypeId);
        Optional<Product> product = independentProductsService.createOrUpdateIndependentProduct(marketSegments, forecastActivityTypeId, baseProduct);
        List<String> mappedMktCodes = marketSegments.stream()
                .map(summary -> getMappedMarketCode(summary, attribute, product))
                .distinct()
                .collect(Collectors.toList());
        Map<String, Set<Boolean>> complimentaryValuesByMappedMarketCodes = getComplimentaryValuesByMappedMarketCodes(mappedMktCodes);
        Set<String> currentlyMappedMarketSegments = marketSegmentRepository.getMarketSegmentProductMappings().keySet();
        Map<String, MarketSegmentProductMapping> mappings = new HashMap<>();
        for (AnalyticalMarketSegmentSummary summary : marketSegments) {
            String mappedMarketCode = getMappedMarketCode(summary, attribute, product);
            if (product.isPresent() && !currentlyMappedMarketSegments.contains(mappedMarketCode) && !mappings.containsKey(mappedMarketCode)) {
                mappings.put(mappedMarketCode, independentProductsService.createNewMapping(mappedMarketCode, product.get(), AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR.equals(attribute)));
            }
            Set<Boolean> complimentaryValuesForMappedCode = complimentaryValuesByMappedMarketCodes.get(mappedMarketCode);
            boolean isComplimentaryChanged = null != complimentaryValuesForMappedCode && !complimentaryValuesForMappedCode.contains(complimentary);
            assignMarketSegments(summary, attribute, false, complimentary, forecastActivityType, isComplimentaryChanged, product);
        }
        executeIfFalse(mappings.isEmpty(), independentProductsService::saveMappings, mappings.values());
        marketSegments.stream()
                .map(AnalyticalMarketSegmentSummary::getMappedCode)
                .distinct()
                .forEach(mappedMarketCode -> updateComplimentaryStatus(complimentary, mappedMarketCode));

        return marketSegments.size();
    }

    public Map<String, Set<Boolean>> getComplimentaryValuesByMappedMarketCodes(List<String> mappedMktCodes) {
        return getAnalyticalMarketSegmentByMappedMarketCodes(mappedMktCodes)
                .stream()
                .collect(Collectors.groupingBy(AnalyticalMarketSegment::getMappedMarketCode,
                        Collectors.mapping(AnalyticalMarketSegment::isComplimentary, Collectors.toSet())));
    }

    private String getMappedMarketCode(AnalyticalMarketSegmentSummary summary, AnalyticalMarketSegmentAttribute attribute, Optional<Product> product) {
        if (summary.getMappedCode() == null) {
            if (summary.getRateCode() == null) {
                return summary.getMarketCode();
            } else {
                return buildMappedMarketCode(summary.getMarketCode(), attribute, product);
            }
        }
        return summary.getMappedCode();
    }

    private void updateComplimentaryStatus(boolean complimentary, String mappedCode) {
        List<AnalyticalMarketSegment> amsRecordsByMappedMarketCodes = getAnalyticalMarketSegmentByMappedMarketCodes(Collections.singletonList(mappedCode));
        if (multipleComplimentaryValuesFound(amsRecordsByMappedMarketCodes)) {
            amsRecordsByMappedMarketCodes.stream().forEach(ams -> {
                ams.setComplimentary(complimentary);
                crudService.save(ams);
            });
        }
    }

    private boolean multipleComplimentaryValuesFound(List<AnalyticalMarketSegment> amsRecordsByMappedMarketCodes) {
        return amsRecordsByMappedMarketCodes.stream().map(AnalyticalMarketSegment::isComplimentary).distinct().count() > 1;
    }

    public void assignMarketSegments(AnalyticalMarketSegmentSummary summary,
                                     AnalyticalMarketSegmentAttribute attribute, Integer forecastActivityTypeId,
                                     boolean complimentary, String productName, Set<String> rateCodes) {
        Product product = null;
        if (isIndependentProductEnabled() && StringUtils.isNotEmpty(productName)) {
            product = getProduct(productName);
        }

        ForecastActivityType forecastActivityType = getForecastActivityType(forecastActivityTypeId);
        assignMarketSegments(summary, attribute, true, complimentary, forecastActivityType, false, Optional.ofNullable(product));
        if (Objects.nonNull(product)) {
            createProductRateCodeMapping(summary, forecastActivityTypeId, rateCodes, product);
            createMarketSegmentProductMapping(summary, attribute, product);
        }
    }

    private void createProductRateCodeMapping(AnalyticalMarketSegmentSummary summary, Integer forecastActivityTypeId, Set<String> rateCodes, Product product) {
        if (Objects.isNull(summary.getRateCode())) {
            independentProductsService.transferRateCodes(null, product, Optional.ofNullable(rateCodes).orElse(Set.of()));
        } else {
            independentProductsService.createOrUpdateIndependentProduct(List.of(summary), forecastActivityTypeId, product);
        }
    }

    private void createMarketSegmentProductMapping(AnalyticalMarketSegmentSummary summary, AnalyticalMarketSegmentAttribute attribute, Product product) {
        Set<String> currentlyMappedMarketSegments = marketSegmentRepository.getMarketSegmentProductMappings().keySet();
        String mappedMarketCode = summary.getMappedCode();
        if (Objects.nonNull(mappedMarketCode) && !currentlyMappedMarketSegments.contains(mappedMarketCode)) {
            MarketSegmentProductMapping marketSegmentProductMapping = independentProductsService.createNewMapping(mappedMarketCode, product, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR.equals(attribute));
            independentProductsService.saveMappings(List.of(marketSegmentProductMapping));
        }
    }

    public void assignMarketSegments(AnalyticalMarketSegmentSummary summary,
                                     AnalyticalMarketSegmentAttribute attribute, Integer forecastActivityTypeId,
                                     boolean createMktSegIfNotFound, boolean complimentary, String productName) {
        Product product = null;
        if (configParamsServiceLocal.getBooleanParameterValue(INDEPENDENT_PRODUCTS_ENABLED) && StringUtils.isNotEmpty(productName)) {
            product = getProduct(productName);
        }

        ForecastActivityType forecastActivityType = getForecastActivityType(forecastActivityTypeId);
        assignMarketSegments(summary, attribute, createMktSegIfNotFound, complimentary, forecastActivityType, false, null != product ? Optional.ofNullable(product) : Optional.ofNullable(null));
    }

    private ForecastActivityType getForecastActivityType(Integer forecastActivityTypeId) {
        return crudService.findByNamedQuerySingleResult(ForecastActivityType.BY_ID, QueryParameter.with("id", forecastActivityTypeId).parameters());
    }

    public Product getProduct(String productName) {
        Product product = independentProductsRepository.getProduct(productName);
        if (null == product) {
            product = new Product();
            product.setName(productName);
            product.setCode(Product.INDEPENDENT_PRODUCT_CODE);
            product = independentProductsService.createProductIfNotPersisted(product);
        }
        return product;
    }


    private void assignMarketSegments(AnalyticalMarketSegmentSummary summary, AnalyticalMarketSegmentAttribute attribute, boolean createMktSegIfNotFound,
                                      boolean complimentary, ForecastActivityType forecastActivityType, boolean isComplimentaryValueChanged, Optional<Product> product) {
        if (summary.getMappedCode() == null) {
            //Any summary at the Group or Market Segment Level, set the mapped code to the Market Segment Code
            if (summary.getRateCode() == null) {
                summary.setMappedCode(summary.getMarketCode());
            } else {
                //This summary is at the rate code level, so the mapped code becomes MarketSegmentCode_AttributeSuffix
                summary.setMappedCode(buildMappedMarketCode(summary.getMarketCode(), attribute, product));
            }
        }

        if (summary.getRateCodeType() == null) {
            if (summary.getRateCode() != null) {
                // this specific market segment/rate code combination
                summary.setRateCodeType(RateCodeTypeEnum.EQUALS.name());
            } else {
                // all rate codes for this market segment
                summary.setRateCodeType(RateCodeTypeEnum.ALL.name());
            }
        }


        AnalyticalMarketSegment ams = createAnalyticalMarketSegment(summary, attribute, forecastActivityType, complimentary);

        //if mapped code exists in marketSegment master we do not need to trigger create commit
        boolean isUpdateDetailsProposed = isComplimentaryValueChanged ||
                null == summary.getRateCode() ||
                null == findMarketSegmentMaster(ams.getMappedMarketCode());

        // Update MktSegs with changes
        handleMarketSegmentChanges(ams.getMappedMarketCode(), ams, attribute, isUpdateDetailsProposed, createMktSegIfNotFound, product.orElse(null));
    }

    private void addEntryForCompositionChange(Map<String, AnalyticalMarketSegment> beforeAMSMap, AnalyticalMarketSegment analyticalMarketSegment, List<PreviewMarketSegment> previewBeforeChange, final boolean updateHistory) {
        if (analyticalMarketSegment != null) {
            AnalyticalMarketSegment ams = beforeAMSMap.get(analyticalMarketSegment.getMappedMarketCode());
            if (ams != null) {
                MktSeg mktSeg = crudService.findByNamedQuerySingleResult(MktSeg.BY_CODE, QueryParameter.with("code", analyticalMarketSegment.getMappedMarketCode()).parameters());
                if (mktSeg != null) {
                    createAMSCompositionChangeRecord(ams, mktSeg, previewBeforeChange, updateHistory);
                    updateMktSegDetailsProposed(analyticalMarketSegment, analyticalMarketSegment.getAttribute(), mktSeg, null);
                }
            }
        }
    }

    private void createAMSCompositionChangeRecord(AnalyticalMarketSegment amsBeforeChange, MktSeg mktSeg, List<PreviewMarketSegment> previewBeforeChange, final boolean updateHistory) {
        PreviewMarketSegment preview = previewBeforeChange.stream().filter(candidate -> candidate.getMappedMarketCode().equals(amsBeforeChange.getMappedMarketCode())).findFirst().get();
        createAMSCompositionChangeRecord(amsBeforeChange.getId(), mktSeg.getId(), preview.getRoomsSold(), preview.getHotelPercent(), updateHistory);
    }

    public void createAMSCompositionChangeRecord(String code, String mappedCode) {
        // Track when a straight MS becomes split.
        MktSeg straightMS = findMktSegByCode(code);
        AnalyticalMarketSegment splitMS = crudService.findByNamedQuerySingleResult(AnalyticalMarketSegment.BY_MAPPED_MARKET_CODE,
                QueryParameter.with(MAPPED_MARKET_CODE, mappedCode).parameters());
        if (straightMS == null || splitMS == null) {
            return;
        }
        LOGGER.info("Creating AMS_Composition_Change record for " + straightMS.getCode() + " -> " + splitMS.getMappedMarketCode());
        createAMSCompositionChangeRecord(splitMS.getId(), straightMS.getId(), 0, BigDecimal.ZERO, true);
    }

    private void createAMSCompositionChangeRecord(Integer amsId, Integer mktSegId, Integer roomsSold, BigDecimal hotelPercent, boolean applyToHistory) {
        AMSCompositionChange entity = new AMSCompositionChange();
        entity.setAMSId(amsId);
        entity.setMarketSegmentId(mktSegId);
        entity.setChangeDate(LocalDateTime.now());
        entity.setAMSRoomsSold(roomsSold);
        entity.setHotelPercent(hotelPercent);
        entity.setApplyToHistory(applyToHistory);
        crudService.save(entity);
    }

    public boolean groupChangesHaveSufficientData() {
        return !groupMsChangeExists() || groupDataAvailable() || groupChangesWithinThreshold();
    }

    private boolean groupChangesWithinThreshold() {
        return computeGroupCompositionChangeImpact().compareTo(BigDecimal.valueOf(SystemConfig.groupChangeThreshold())) < 0;
    }

    private boolean groupDataAvailable() {
        return availableGroupPastDays() >= DateUtil.ONE_YEAR;
    }

    public int availableGroupPastDays() {
        return crudService.findByNamedQuerySingleResult(GroupBlockMaster.COUNT_AVAILABLE_GROUP_PAST_DAYS);
    }

    private boolean groupMsChangeExists() {
        return modifiedGroupCount() > 0;
    }

    public int modifiedGroupCount() {
        return crudService.findByNamedQuerySingleResult(AMSCompositionChange.GET_MODIFIED_GROUP_COUNT);
    }

    public BigDecimal computeCompositionChangeImpact(@QueryParam("ignoreApplyHistory") boolean ignoreApplyHistory) {
        List<AMSCompositionChange> entities = crudService.findAll(AMSCompositionChange.class);
        java.util.function.Predicate<AMSCompositionChange> amsCompositionFilter =
                ignoreApplyHistory ? getAmsCompositionChangeApplyHistoryPredicate(entities) : t -> true;
        Map<Integer, BigDecimal> hotelPercentMap = new HashMap<>();
        entities.stream().filter(amsCompositionFilter).forEach(entity -> updateHotelPercentMap(hotelPercentMap, entity));
        return hotelPercentMap.values().stream()
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }

    public BigDecimal computeGroupCompositionChangeImpact() {
        List<AMSCompositionChange> entities = crudService.findAll(AMSCompositionChange.class);
        List<Integer> groupChangeIds = crudService.findByNamedQuery(AMSCompositionChange.DISTINCT_GROUP_CHANGE);
        java.util.function.Predicate<AMSCompositionChange> amsCompositionFilter =
                acc -> groupChangeIds.contains(acc.getId());
        Map<Integer, BigDecimal> hotelPercentMap = new HashMap<>();
        entities.stream().filter(amsCompositionFilter).forEach(entity -> updateHotelPercentMap(hotelPercentMap, entity));
        return hotelPercentMap.values().stream()
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }


    public java.util.function.Predicate<AMSCompositionChange> getAmsCompositionChangeApplyHistoryPredicate(List<AMSCompositionChange> entities) {
        final Map<Integer, List<String>> mktSegIdHotelMarketCodes = marketSegmentService.findMktSegIdHotelMarketCodeMap();
        Set<String> applyHistoryMarketCodes =
                entities.stream().filter(AMSCompositionChange::isApplyToHistory)
                        .map(AMSCompositionChange::getMarketSegmentId)
                        .map(mktSegIdHotelMarketCodes::get)
                        .filter(Objects::nonNull)
                        .flatMap(List::stream)
                        .collect(Collectors.toSet());
        LOGGER.info("Hotel Market Codes with apply_history = 1" + applyHistoryMarketCodes);

        final java.util.function.Predicate<List<String>> msNotApplyToHistory = marketCodes -> marketCodes.stream()
                .noneMatch(applyHistoryMarketCodes::contains);
        java.util.function.Predicate<AMSCompositionChange> notAppliedToHistory =
                e -> Optional.of(e.getMarketSegmentId())
                        .map(mktSegIdHotelMarketCodes::get)
                        .filter(msNotApplyToHistory)
                        .isPresent();
        return notAppliedToHistory;
    }

    private void updateHotelPercentMap(Map<Integer, BigDecimal> hotelPercentMap, AMSCompositionChange entity) {
        Integer amsId = entity.getAMSId();
        BigDecimal value = entity.getHotelPercent();
        BigDecimal highValue = hotelPercentMap.get(amsId);
        if (highValue == null || highValue.compareTo(value) < 0) {
            hotelPercentMap.put(amsId, value);
        }
    }

    public int clearAMSCompositionChanges() {
        return crudService.deleteAll(AMSCompositionChange.class);
    }

    public int unassignMarketSegments(List<AnalyticalMarketSegment> analyticalMarketSegments, boolean updateHistory) {
        return unAssignMsAndPopulateAmsCompositionChange(analyticalMarketSegments, updateHistory, true);
    }

    public int unAssignMsAndPopulateAmsCompositionChange(List<AnalyticalMarketSegment> analyticalMarketSegments,
                                                         boolean updateHistory, boolean unassignMs) {
        List<PreviewMarketSegment> previewBeforeChange = preview();
        //get list of existing ams entries
        List<AnalyticalMarketSegment> beforeChangeAMSList = getAllSortedByRank();
        Map<String, AnalyticalMarketSegment> beforeAMSMap = new HashMap<>();
        beforeChangeAMSList.stream().forEach(analyticalMarketSegment -> beforeAMSMap.put(analyticalMarketSegment.getMappedMarketCode(), analyticalMarketSegment));

        for (AnalyticalMarketSegment analyticalMarketSegment : analyticalMarketSegments) {
            if (unassignMs) {
                unassignMarketSegment(analyticalMarketSegment);
            }
            addEntryForCompositionChange(beforeAMSMap, analyticalMarketSegment, previewBeforeChange, updateHistory);
        }
        return analyticalMarketSegments.size();
    }

    public int unassignMarketSegmentsByRateCodes(List<ProductRateCode> productRateCodes) {
        if (CollectionUtils.isEmpty(productRateCodes)) {
            return 0;
        }
        List<AnalyticalMarketSegment> analyticalMarketSegments = getAnalyticalMarketSegmentByRateCodes(productRateCodes);

        return unassignMarketSegments(analyticalMarketSegments, true);
    }

    public void unassignMarketSegmentProductMappingByProduct(Product product) {
        List<MarketSegmentProductMapping> mappings = crudService.findByNamedQuery(BY_PRODUCT, QueryParameter.with("productIds", singletonList(product.getId())).parameters());
        crudService.delete(mappings);
    }

    private List<AnalyticalMarketSegment> getAnalyticalMarketSegmentByRateCodes(List<ProductRateCode> productRateCodes) {
        if (CollectionUtils.isNotEmpty(productRateCodes)) {
            return new ArrayList<>();
        }

        Set<String> rateCodes = productRateCodes.stream().map(ProductRateCode::getRateCode).collect(Collectors.toSet());
        return getAmsByRateCodes(rateCodes);
    }

    public List<AnalyticalMarketSegment> getAmsByRateCodes(Collection<String> rateCodes) {
        if (rateCodes.isEmpty()) {
            return List.of();
        }
        return crudService.findByNamedQuery(AnalyticalMarketSegment.BY_RATE_CODES,
                QueryParameter.with("rateCodes", rateCodes).parameters());
    }

    /*
    when the feature of separable Identical MS attributes is enabled, instead of all rate codes with same MS_attribute combo going to the same
    AMS for e.g : MS_QYL, different rate codes with same MS-Attribute combination can have different AMS of pattern MS_<Index>_AttributeSuffix like
    MS_QYL, MS_1_QYL, MS_2_QYL etc.

    If the user mentions the mapped market code to be used for assignment, for e.g. : MS_2_QYL, the user selected mapped
    code will be applied

    In case user hasn't provided the mapped market Code, we want to assign a new higher index for new AMS mapped market code creation.
    I.e., If MS_1_QYL and MS_2_QYL is already present, and the user wants a new AMS with same MS and QYL attribute, a new AMS
    with mapped market code MS_3_QYL will be created

     */
    public void updateAssignedMarketSegments(List<AnalyticalMarketSegment> analyticalMarketSegmentList, AnalyticalMarketSegmentAttribute attribute, String mappedMarketCode, Product product) {
        List<AnalyticalMarketSegment> analyticalMarketSegments = analyticalMarketSegmentList.stream()
                .filter(analyticalMarketSegment -> !analyticalMarketSegment.isPreserved())
                .collect(Collectors.toList());
        String finalMappedMarketCode = StringUtils.isEmpty(mappedMarketCode) ? getNewMappedMarketCode(analyticalMarketSegments, attribute, product) : mappedMarketCode;
        boolean isDefaultEdit = analyticalMarketSegments.stream().anyMatch(this::isDefault);

        if (isIndependentProductsEnabled() && isIdenticalMsAttributeForIPEnabled()) {
            updateAssignedMarketSegments(analyticalMarketSegmentList, attribute, false, product, mappedMarketCode);
        } else {
            for (AnalyticalMarketSegment analyticalMarketSegment : analyticalMarketSegments) {
                String originalMappedCode = analyticalMarketSegment.getMappedMarketCode();
                analyticalMarketSegment.setAttribute(attribute);
                if (!isDefaultEdit) {
                    if (StringUtils.isNotEmpty(analyticalMarketSegment.getMarketCode()) && analyticalMarketSegment.getMarketCode().equalsIgnoreCase(analyticalMarketSegment.getMappedMarketCode())) {
                        analyticalMarketSegment.setMappedMarketCode(analyticalMarketSegment.getMarketCode());
                    } else {
                        analyticalMarketSegment.setMappedMarketCode(finalMappedMarketCode);
                    }
                }
                crudService.save(analyticalMarketSegment);
                handleMarketSegmentChanges(originalMappedCode, analyticalMarketSegment, attribute, true, false, null);
            }
            List<AnalyticalMarketSegment> preservedAnalyticalMarketSegments = analyticalMarketSegmentList.stream()
                    .filter(AnalyticalMarketSegment::isPreserved)
                    .collect(Collectors.toList());
            updatePreservedAnalyticalMarketSegments(preservedAnalyticalMarketSegments, analyticalMarketSegments);
        }
    }

    private boolean isIdenticalMsAttributeForIPEnabled() {
        return isSeparableIdenticalMsAttributeEnabled() && configParamsServiceLocal.getBooleanParameterValue(FeatureTogglesConfigParamName.IDENTICAL_MS_ATTRIBUTE_IP);
    }


    public String getNewMappedMarketCode(List<AnalyticalMarketSegment> analyticalMarketSegmentList, final AnalyticalMarketSegmentAttribute attribute, Product product) {
        if (CollectionUtils.isEmpty(analyticalMarketSegmentList)) {
            /* When the mapped market Code is null, the default logic of deriving mapped market code from
             * market segment and attribute will be applied
             * */
            return null;
        }
        final String hotelMs = analyticalMarketSegmentList.get(analyticalMarketSegmentList.size() - 1).getMarketCode();
        return getNewMappedMarketCode(attribute, hotelMs, MarketSegmentCasinoCategoryType.NONE, product);
    }

    public String getNewMappedMarketCode(final AnalyticalMarketSegmentAttribute attribute, final String hotelMs,
                                         final MarketSegmentCasinoCategoryType casinoCategory, Product product) {
        final Set<String> analyticalMarketSegments =
                getNonDefaultAnalyticalMarketSegmentCodesByMsAttribute(hotelMs, attribute);
        final String msCategory = hotelMs + (casinoCategory.equals(MarketSegmentCasinoCategoryType.NONE) ? "" : "_" + casinoCategory.getName());
        if (Objects.nonNull(product) && product.isIndependentProduct() && isIndependentProductsEnabled() && isIdenticalMsAttributeForIPEnabled()) {
            Product savedProduct = independentProductsService.createProductIfNotPersisted(product);
            final Integer nextIndex = analyticalMarketSegments.stream().map(a -> getIndex(a, msCategory,
                            attribute.getSuffix(), savedProduct))
                    .map(i -> i + 1)
                    .max(Integer::compareTo).orElse(1);
            if (savedProduct != null && savedProduct.isIndependentProduct()) {
                return msCategory + "_" + nextIndex + "_" + "IP" + savedProduct.getId() + "_" + attribute.getSuffix();
            }
            return msCategory + "_" + nextIndex + "_" + attribute.getSuffix();
        } else {
            final Integer nextIndex = analyticalMarketSegments.stream().map(a -> getIndex(a, msCategory,
                            attribute.getSuffix(), null))
                    .map(i -> i + 1)
                    .max(Integer::compareTo).orElse(1);
            return msCategory + "_" + nextIndex + "_" + attribute.getSuffix();
        }
    }

    private int getIndex(final String amsMappedMktCode, final String origMktCode, final String attributeSuffix, Product product) {
        boolean isSeparableIdenticalMsEnabledForIP = isIndependentProductsEnabled() && isIdenticalMsAttributeForIPEnabled();
        String identicalAmsNamePattern = origMktCode + "_" + "(\\d+)" + "_" + attributeSuffix;
        if (isSeparableIdenticalMsEnabledForIP) {
            if (product != null) {
                identicalAmsNamePattern = origMktCode + "_" + "(\\d+)" + "_IP" + product.getId() + "_" + attributeSuffix;
            } else {
                identicalAmsNamePattern = origMktCode + "_" + "(\\d+)" + "_IP_" + attributeSuffix;
            }
        }
        final Pattern compiledPattern = Pattern.compile(identicalAmsNamePattern);
        final Matcher matcher = compiledPattern.matcher(amsMappedMktCode);
        if (matcher.matches()) {
            return Integer.parseInt(matcher.group(1));
        }
        return 0;
    }

    private boolean isSeparableIdenticalMsAttributeEnabled() {
        return configParamsServiceLocal.getBooleanParameterValue(GUIConfigParamName.SEPARABLE_IDENTICAL_MS_ATTRIB);
    }

    public void updateAssignedMarketSegments(List<AnalyticalMarketSegment> analyticalMarketSegmentList,
                                             AnalyticalMarketSegmentAttribute attribute, Product product) {
        updateAssignedMarketSegments(analyticalMarketSegmentList, attribute, false, product, "");
    }

    public void updateAssignedMarketSegments(List<AnalyticalMarketSegment> analyticalMarketSegmentList, AnalyticalMarketSegmentAttribute attribute,
                                             boolean createMktSegIfNotFound, Product product, String mappedMarketCode) {
        updateAssignedMarketSegments(analyticalMarketSegmentList, attribute, createMktSegIfNotFound, product, mappedMarketCode, null);
    }

    public void updateAssignedMarketSegments(List<AnalyticalMarketSegment> analyticalMarketSegmentList, AnalyticalMarketSegmentAttribute attribute,
                                             boolean createMktSegIfNotFound, Product product, String mappedMarketCode,
                                             Map<AnalyticalMarketSegment, List<String>> rateCodesToAssign) {
        List<AnalyticalMarketSegment> analyticalMarketSegments = analyticalMarketSegmentList.stream()
                .filter(analyticalMarketSegment -> !analyticalMarketSegment.isPreserved())
                .collect(Collectors.toList());

        boolean isDefaultEdit = analyticalMarketSegments.stream().anyMatch(this::isDefault);
        Set<String> marketSegmentsMappedWithProduct = new HashSet<>();
        Set<String> rateCodes = new HashSet<>();
        Product originallyMappedProduct = independentProductsService.getProductMappedToMarketSegment(analyticalMarketSegments.get(0).getMappedMarketCode()).orElse(null);
        Product baseProduct = product == null ? null : independentProductsService.getOrCreateProduct(product);
        final Set<String> amsMappedCode = getNonDefaultAnalyticalMarketSegmentCodesByMsAttribute(analyticalMarketSegments.get(0).getMarketCode(), attribute);
        for (AnalyticalMarketSegment analyticalMarketSegment : analyticalMarketSegments) {
            String originalMappedCode = persistAnalyticalMarketSegment(mappedMarketCode, amsMappedCode, attribute, isDefaultEdit, analyticalMarketSegment, Optional.ofNullable(baseProduct));
            String newMappedMarketCode = analyticalMarketSegment.getMappedMarketCode();
            if (Objects.nonNull(rateCodesToAssign) && Objects.nonNull(rateCodesToAssign.get(analyticalMarketSegment))) {
                rateCodes.addAll(rateCodesToAssign.get(analyticalMarketSegment));
            } else if (analyticalMarketSegment.getRateCodeType() == RateCodeTypeEnum.ALL) {
                rateCodes.addAll(stream(marketSegmentRepository.getRateCodesForMarketSegments(Arrays.asList(analyticalMarketSegment.getMappedMarketCode())))
                        .map(RateCodeSummary::getRateCode)
                        .collect(Collectors.toList()));
            } else if (StringUtils.isNotBlank(analyticalMarketSegment.getRateCode())) {
                rateCodes.add(analyticalMarketSegment.getRateCode());
            }
            handleMarketSegmentChanges(originalMappedCode, analyticalMarketSegment, attribute, true, createMktSegIfNotFound, baseProduct);
            if (!marketSegmentsMappedWithProduct.contains(newMappedMarketCode)) {
                runIfFalse(
                        StringUtils.equalsIgnoreCase(originalMappedCode, newMappedMarketCode),
                        () -> independentProductsService.deleteMappingForMarketSegment(originalMappedCode)
                );
                independentProductsService.updateMarketSegmentProductMapping(newMappedMarketCode, baseProduct, attribute);
                marketSegmentsMappedWithProduct.add(newMappedMarketCode);
            }
        }
        independentProductsService.transferRateCodes(originallyMappedProduct, baseProduct, rateCodes);
        List<AnalyticalMarketSegment> preservedAnalyticalMarketSegments = analyticalMarketSegmentList.stream()
                .filter(AnalyticalMarketSegment::isPreserved)
                .collect(Collectors.toList());
        updatePreservedAnalyticalMarketSegments(preservedAnalyticalMarketSegments, analyticalMarketSegments);
    }

    public void updateNonDefaultNonStraightAMSMappedMarketCode(List<AnalyticalMarketSegment> analyticalMarketSegmentList, AnalyticalMarketSegmentAttribute attribute,
                                                               Product product, String mappedMarketCode) {
        Product baseProduct = product == null ? null : independentProductsService.getOrCreateProduct(product);
        Set<String> amsMappedCode = getNonDefaultAnalyticalMarketSegmentCodesByMsAttribute(analyticalMarketSegmentList.get(0).getMarketCode(), attribute);
        for (AnalyticalMarketSegment analyticalMarketSegment : analyticalMarketSegmentList) {
            updateMappedMarketCode(mappedMarketCode, amsMappedCode, attribute, false, analyticalMarketSegment, Optional.ofNullable(baseProduct));
        }
    }

    private void updatePreservedAnalyticalMarketSegments(List<AnalyticalMarketSegment> preservedAnalyticalMarketSegments, List<AnalyticalMarketSegment> analyticalMarketSegments) {
        if (CollectionUtils.isEmpty(preservedAnalyticalMarketSegments) || CollectionUtils.isEmpty(analyticalMarketSegments)) {
            return;
        }
        final String newMappedMarketCode = analyticalMarketSegments.get(0).getMappedMarketCode();
        final AnalyticalMarketSegmentAttribute newAttribute = analyticalMarketSegments.get(0).getAttribute();
        preservedAnalyticalMarketSegments.stream()
                .forEach(analyticalMarketSegment -> {
                    analyticalMarketSegment.setMappedMarketCode(newMappedMarketCode);
                    analyticalMarketSegment.setAttribute(newAttribute);
                });
        crudService.save(preservedAnalyticalMarketSegments);
    }

    public void updateAnalyticalMarketSegment(AnalyticalMarketSegment analyticalMarketSegment, AnalyticalMarketSegmentAttribute attribute,
                                              boolean isDefaultEdit, boolean createMktSegIfNotFound, Optional<Product> product) {
        String originalMappedCode = persistAnalyticalMarketSegment(attribute, isDefaultEdit, analyticalMarketSegment, product);
        String newMappedMarketCode = analyticalMarketSegment.getMappedMarketCode();
        handleMarketSegmentChanges(newMappedMarketCode, analyticalMarketSegment, attribute, true, createMktSegIfNotFound, product.orElse(null));
        if (!analyticalMarketSegmentsExistsFor(originalMappedCode)) {
            deleteMarketSegmentMasterFor(originalMappedCode);
        }

        if (isMSRecodingSupportedForIndependentProduct() && isIndependentProductEnabled()) {
            Product originallyMappedProduct = independentProductsService.getProductMappedToMarketSegment(originalMappedCode).orElse(null);
            Product assignedProduct = product.map(value -> independentProductsService.getOrCreateProduct(value)).orElse(null);
            independentProductsService.updateMarketSegmentProductMapping(newMappedMarketCode, assignedProduct, attribute);
            independentProductsService.transferRateCodes(originallyMappedProduct, assignedProduct, Set.of(analyticalMarketSegment.getRateCode()));
        }
    }

    private void deleteMarketSegmentMasterFor(String originalMappedCode) {
        final MarketSegmentMaster marketSegmentMaster = findMarketSegmentMaster(originalMappedCode);
        if (Objects.nonNull(marketSegmentMaster)) {
            crudService.delete(marketSegmentMaster);
        }
    }

    private boolean analyticalMarketSegmentsExistsFor(String originalMappedCode) {
        final Long numberOfAms = crudService.findByNamedQuerySingleResult(AnalyticalMarketSegment.COUNT_BY_MAPPED_MARKET_CODE,
                QueryParameter.with(MAPPED_MARKET_CODE, originalMappedCode).parameters());
        return numberOfAms > 0;
    }

    private String persistAnalyticalMarketSegment(AnalyticalMarketSegmentAttribute attribute, boolean isDefaultEdit,
                                                  AnalyticalMarketSegment analyticalMarketSegment, Optional<Product> product) {
        String originalMappedCode = analyticalMarketSegment.getMappedMarketCode();

        analyticalMarketSegment.setAttribute(attribute);
        if (!isDefaultEdit) {
            if (analyticalMarketSegment.getMarketCode().equalsIgnoreCase(analyticalMarketSegment.getMappedMarketCode())) {
                analyticalMarketSegment.setMappedMarketCode(analyticalMarketSegment.getMarketCode());
            } else {
                analyticalMarketSegment.setMappedMarketCode(buildMappedMarketCode(analyticalMarketSegment.getMarketCode(), attribute, product));
            }
        }

        crudService.save(analyticalMarketSegment);
        updateComplimentaryStatus(analyticalMarketSegment.isComplimentary(), analyticalMarketSegment.getMappedMarketCode());
        return originalMappedCode;
    }

    private String persistAnalyticalMarketSegment(String mappedMarketCode, Set<String> amsMappedCode, AnalyticalMarketSegmentAttribute attribute, boolean isDefaultEdit,
                                                  AnalyticalMarketSegment analyticalMarketSegment, Optional<Product> product) {
        String originalMappedCode = analyticalMarketSegment.getMappedMarketCode();
        analyticalMarketSegment.setAttribute(attribute);
        updateMappedMarketCode(mappedMarketCode, amsMappedCode, attribute, isDefaultEdit, analyticalMarketSegment, product);

        crudService.save(analyticalMarketSegment);
        updateComplimentaryStatus(analyticalMarketSegment.isComplimentary(), analyticalMarketSegment.getMappedMarketCode());
        return originalMappedCode;
    }

    private void updateMappedMarketCode(String newMappedMarketCode, Set<String> amsMappedCode, AnalyticalMarketSegmentAttribute attribute, boolean isDefaultEdit, AnalyticalMarketSegment analyticalMarketSegment, Optional<Product> product) {
        if (!isDefaultEdit) {
            if (StringUtils.isNotEmpty(newMappedMarketCode)) {
                analyticalMarketSegment.setMappedMarketCode(newMappedMarketCode);
            } else {
                if (StringUtils.isNotEmpty(analyticalMarketSegment.getMarketCode()) &&
                        analyticalMarketSegment.getMarketCode().equalsIgnoreCase(analyticalMarketSegment.getMappedMarketCode())) {
                    analyticalMarketSegment.setMappedMarketCode(analyticalMarketSegment.getMarketCode());
                } else {
                    if (product.isPresent() && product.get().isIndependentProduct() && isIndependentProductsEnabled() && isIdenticalMsAttributeForIPEnabled()) {
                        analyticalMarketSegment.setMappedMarketCode(buildMappedMarketCode(amsMappedCode, analyticalMarketSegment, attribute, product.get()));
                    } else {
                        analyticalMarketSegment.setMappedMarketCode(buildMappedMarketCode(analyticalMarketSegment.getMarketCode(), attribute, product));
                    }
                }
            }
        }
    }

    private String buildMappedMarketCode(Set<String> amsMappedCode, AnalyticalMarketSegment analyticalMarketSegment, AnalyticalMarketSegmentAttribute attribute, Product product) {
        final Integer nextIndex = amsMappedCode.stream().map(a -> getIndex(a, analyticalMarketSegment.getMarketCode(),
                        attribute.getSuffix(), product))
                .map(i -> i + 1)
                .max(Integer::compareTo).orElse(1);
        String productPrefix = "IP" + product.getId();
        return analyticalMarketSegment.getMarketCode() + "_" + nextIndex + "_" + productPrefix + "_" + attribute.getSuffix();
    }

    private boolean isDefault(AnalyticalMarketSegment analyticalMarketSegment) {
        return analyticalMarketSegment.getMappedMarketCode().endsWith(DEFAULT_AFFIX);
    }

    public void accept() {
        configParamsServiceLocal.addParameterValue(ANALYTICAL_MARKET_SEGMENT_MAPPING_COMPLETE, Boolean.TRUE.toString());
    }

    public boolean isAccepted() {
        return configParamsServiceLocal.getBooleanParameterValue(ANALYTICAL_MARKET_SEGMENT_MAPPING_COMPLETE);
    }

    /*
        Is the market segment mapping complete?  Check the system setting and check that there
        are no unassigned market segments at the group and market segment level
    */
    public boolean hasAllGroupAndIndividualMarketSegmentsMapped() {
        return this.getUnassignedGroupAndIndividualMktSegmentCount() == 0;
    }

    public String rebuild() {
        Integer propertyId = PacmanThreadLocalContextHolder.getWorkContext().getPropertyId();
        populationResource.startDataLoad(propertyId.toString());
        return "Starting data load";
    }

    @SuppressWarnings("unchecked")
    public List<RateCodeSummary> getRateCodes(String marketCode) {
        final long start = System.currentTimeMillis();

        // get whole list so we can populate market segments for each rate code
        List<RateCodeSummary> all = crudService.findByNamedQuery(RateCodeSummary.ALL);

        Map<String, List<String>> marketSegmentToRateCode = all.stream().sorted(Comparator.comparing(RateCodeSummary::getMarketCode))
                .collect(Collectors.groupingBy(RateCodeSummary::getRateCode,
                        Collectors.mapping(RateCodeSummary::getMarketCode, Collectors.toList())));

        // now filter out the ones not for this market code
        List<RateCodeSummary> list = (List<RateCodeSummary>) CollectionUtils.select(all,
                rateCodeSummary -> ((RateCodeSummary) rateCodeSummary).getMarketCode().equals(marketCode));

        // apply the rules
        List<AnalyticalMarketSegment> rules = getAllSortedByRank();
        Map<String, List<AnalyticalMarketSegment>> marketCodeForFilter = rules.stream().collect(Collectors.groupingBy(AnalyticalMarketSegment::getMarketCode));
        java.util.function.Predicate<RateCodeSummary> predicate = (summary) -> {
            List<AnalyticalMarketSegment> ruleList = marketCodeForFilter.get(summary.getMarketCode());
            if (ruleList != null) {
                for (AnalyticalMarketSegment rule : ruleList) {
                    if (matches(summary.getRateCode(), rule.getRateCode(), rule.getRateCodeType())) {
                        return false;
                    }
                }
            }
            return true;
        };
        list = list.stream().filter(predicate).collect(Collectors.toList());

        Integer totalRoomsSold = getTotalRoomsSold();

        // populate the market segments for what's left
        for (RateCodeSummary rateCodeSummary : list) {
            rateCodeSummary.setMarketSegments(marketSegmentToRateCode.get(rateCodeSummary.getRateCode()));

            if (!totalRoomsSold.equals(Integer.valueOf(0))) {
                rateCodeSummary.setPercent((double) rateCodeSummary.getRoomsSold() / (double) totalRoomsSold);
            } else {
                rateCodeSummary.setPercent(Double.valueOf(0));
            }
        }

        LOGGER.info(String.format("Found %d rate codes in %dms", list.size(),
                (System.currentTimeMillis() - start)));
        return list;
    }

    @SuppressWarnings("unchecked")
    public List<PreviewMarketSegment> preview() {
        // pre-populate the results with the preview objects we will update later
        Map<String, PreviewMarketSegment> results = new HashMap<>();
        Map<String, MarketSegmentProductMapping> marketSegmentProductMappings = independentProductsService.getMarketSegmentProductMappings();
        for (AnalyticalMarketSegment current : crudService.findAll(AnalyticalMarketSegment.class)) {
            if (!results.containsKey(current.getMappedMarketCode())) {
                PreviewMarketSegment segment = createPreviewMarketSegment(results, current.getMappedMarketCode(), current);
                segment.setBaseProduct(marketSegmentProductMappings.getOrDefault(current.getMappedMarketCode(), MarketSegmentProductMapping.NULL_MAPPING).getProduct());
                results.put(segment.getMappedMarketCode(), segment);
            }
        }

        // get rules grouped by market code
        Map<String, List<YieldCategoryRule>> rulesByMarketCode = getYieldCategoryByRulesForPreview()
                .stream().collect(Collectors.groupingBy(YieldCategoryRule::getMarketCode));


        // get all occupancy summaries
        List<AmsOccupancySummary> summaryList = crudService.findByNamedQuery(AmsOccupancySummary.FIND_ALL);

        // determine total rooms sold
        double totalRoomsSold = summaryList.stream().map(listitem -> listitem.getRoomsSold()).reduce(0, (carry, curr) -> carry + curr);

        for (AmsOccupancySummary summary : summaryList) {

            List<YieldCategoryRule> rules = null;
            // get the rules for the market code sorted by rank
            if (rulesByMarketCode.get(summary.getMarketCode()) != null) {
                rules = rulesByMarketCode.get(summary.getMarketCode())
                        .stream().sorted(Comparator.comparing(YieldCategoryRule::getRank)).collect(Collectors.toList());
            }


            YieldCategoryRule rule = null;

            // find the first rule that applies
            if (rules != null) {
                for (YieldCategoryRule current : rules) {
                    if ((current.getRateCode() == null) || current.getRateCode().equals(summary.getRateCode())) {
                        rule = current;
                        break;
                    }
                }
            }


            PreviewMarketSegment segment;

            if (rule == null) {
                // assume straight MS mapping
                segment = results.get(summary.getMarketCode());

                if (segment == null) {
                    segment = createPreviewMarketSegment(results, summary.getMarketCode(), null);
                    results.put(segment.getMappedMarketCode(), segment);
                }
            } else {
                // these are prepopulated
                segment = results.get(rule.getAnalyticalMarketCode());
            }

            segment.setRoomsSold(summary.getRoomsSold() + segment.getRoomsSold());

            BigDecimal roomRevenue = BigDecimal.valueOf(summary.getRoomRevenue()).add(segment.getRoomRevenue());
            segment.setRoomRevenue(roomRevenue.setScale(2, RoundingMode.HALF_UP));

            if (segment.getRoomsSold() > 0) {
                BigDecimal avgDailyRate = BigDecimal.valueOf(segment.getRoomRevenue().doubleValue() / segment.getRoomsSold());
                segment.setAdr(avgDailyRate.setScale(2, RoundingMode.HALF_UP));
                BigDecimal hotelPercent = BigDecimal.valueOf((double) segment.getRoomsSold() / totalRoomsSold * 100.0);
                segment.setHotelPercent(hotelPercent.setScale(2, RoundingMode.HALF_UP));
            } else {
                segment.setAdr(ZERO_SCALE_2);
                segment.setHotelPercent(ZERO_SCALE_2);
            }
        }

        return new ArrayList<>(results.values());
    }

    public List<AnalyticalMarketSegmentAuditMapping> getAMSAuditReport() {
        return crudService.findByNamedQuery(AnalyticalMarketSegmentAuditMapping.FIND_ALL_AUDITS);
    }

    public boolean isAnalyticalMarketSegmentChanged() {
        if (isAmsFlagEnabled()) {
            List<MktSegDetailsProposed> proposedList = crudService.findAll(MktSegDetailsProposed.class);
            for (MktSegDetailsProposed proposed : proposedList) {
                if (isNotEmpty(getAnalyticalMarketSegmentsByMarketSegmentProposed(proposed.getMktSeg().getCode()))) {
                    return true;
                }
            }
        }
        return false;
    }

    public List<AnalyticalMarketSegment> getAnalyticalMarketSegmentsByMarketSegmentProposed(String marketSegmentCode) {
        List<AnalyticalMarketSegment> ams = crudService.findByNamedQuery(AnalyticalMarketSegment.BY_MAPPED_MARKET_CODE,
                QueryParameter.with(MAPPED_MARKET_CODE, marketSegmentCode).parameters());
        // return any AMS records that are not "Straight"

        return ams.stream().filter(a ->
                !(a.getRank().equals(1) && a.getRateCodeType().equals(RateCodeTypeEnum.ALL))).collect(Collectors.toList());
    }

    public boolean isAnalyticalMarketSegmentStraightMarketSegment(String marketSegmentCode) {
        return (CollectionUtils.isEmpty(getAnalyticalMarketSegmentsByMarketSegmentProposed(marketSegmentCode)));
    }

    private PreviewMarketSegment createPreviewMarketSegment(Map<String, PreviewMarketSegment> results,
                                                            String mappedCode, AnalyticalMarketSegment ams) {
        PreviewMarketSegment segment = new PreviewMarketSegment();
        segment.setId(results.size());
        segment.setMappedMarketCode(mappedCode);
        segment.setAttributeDescription(ams != null ? ams.getAttribute().getDescription() : "");
        segment.setComplimentary(null != ams && ams.isComplimentary());
        segment.setRoomsSold(0);
        segment.setRoomRevenue(ZERO_SCALE_2);
        segment.setAdr(ZERO_SCALE_2);
        segment.setHotelPercent(ZERO_SCALE_2);
        return segment;
    }

    public int deleteNonDefaultAMSRulesWithMktSegsCodes(List<String> mktSegCodes) {
        if (mktSegCodes.isEmpty()) {
            return 0;
        }
        return crudService.executeUpdateByNamedQuery(AnalyticalMarketSegment.DELETE_EXCEPT_DEFAULT_BY_MARKET_CODES, QueryParameter.with(MARKET_CODES, mktSegCodes).parameters());
    }

    public int deleteAMSRulesWithMktCodes(List<String> mktCodes) {
        if (mktCodes.isEmpty()) {
            return 0;
        }
        return crudService.executeUpdateByNamedQuery(AnalyticalMarketSegment.DELETE_BY_MARKET_CODES, QueryParameter.with(MARKET_CODES, mktCodes).parameters());
    }

    public List<String> getMarketCodes(List<Integer> ranks, List<String> marketCodes) {
        if (ranks.isEmpty() || marketCodes.isEmpty()) {
            return new ArrayList<>();
        }
        return crudService.findByNamedQuery(AnalyticalMarketSegment.GET_MARKET_CODES_BY_RANKS_AND_MARKET_CODES,
                QueryParameter.with("ranks", ranks).and(MARKET_CODES, marketCodes).parameters());
    }

    @SuppressWarnings("unchecked")
    public int populateYieldCategoryByRule() {
        return populateYieldCategoryByRule(false);
    }

    public int populateYieldCategoryByRule(boolean forPreview) {
        // remove all existing rules
        crudService.executeUpdateByNativeQuery("TRUNCATE TABLE opera.Yield_Category_Rule ");

        List<AnalyticalMarketSegment> rules = getAllSortedByRank();
        int ycbrRules = 0;

        Map<String, YieldCategoryRule> marketCodeRateCodeCombination = new HashMap<>();
        List<YieldCategoryRule> yieldCategoryRules = new ArrayList<>();

        boolean isBulkEnabled = configParamsServiceLocal.getBooleanParameterValue(FeatureTogglesConfigParamName.AMS_YIELD_CATEGORY_RULE_BULK_ENABLED);

        long s = System.currentTimeMillis();

        for (AnalyticalMarketSegment rule : rules) {
            if (rule.getRateCodeType().equals(RateCodeTypeEnum.DEFAULT)) {
                YieldCategoryRule yieldCategoryByRule = createYieldCategoryByRule(rule, null);
                if (isBulkEnabled) {
                    yieldCategoryRules.add(yieldCategoryByRule);
                } else {
                    crudService.save(yieldCategoryByRule);
                }
                ycbrRules++;
            } else if (!rule.getRateCodeType().equals(RateCodeTypeEnum.ALL)) {
                for (String rateCode : findRateCodes(rule, forPreview)) {
                    String mktRateCodeKey = String.format("%s%s%s", rule.getMarketCode(), "_", rateCode);
                    YieldCategoryRule matches = marketCodeRateCodeCombination.get(mktRateCodeKey);
                    if (null == matches) {
                        YieldCategoryRule yieldCategoryByRule = createYieldCategoryByRule(rule, rateCode);
                        marketCodeRateCodeCombination.put(mktRateCodeKey, yieldCategoryByRule);
                        ycbrRules++;
                    }
                }
            }
        }

        if (isBulkEnabled && CollectionUtils.isNotEmpty(yieldCategoryRules)) {
            saveYieldCategoryRulesInBatch(yieldCategoryRules);
        }

        if (!marketCodeRateCodeCombination.isEmpty()) {
            LOGGER.info("No Match YCR count is " + marketCodeRateCodeCombination.size());
            if (isBulkEnabled) {
                saveYieldCategoryRulesInBatch(marketCodeRateCodeCombination.values());
            } else {
                crudService.save(marketCodeRateCodeCombination.values());
            }
        }

        LOGGER.info("Overall Time taken for Yield category Rule with type enabled/disabled :[ "
                + isBulkEnabled + "] is " + (System.currentTimeMillis() - s) + " ms");

        return ycbrRules;
    }


    private void saveYieldCategoryRulesInBatch(Collection<YieldCategoryRule> yieldCategoryRules) {
        List<YieldCategoryRuleDTO> yieldCategoryRuleDTOS = convertYieldCategoryToYieldCategoryDTOs(yieldCategoryRules);
        crudService.execute(yieldCategoryRuleDTOS.get(0).getInsertStoredProcedureName(), yieldCategoryRuleDTOS);
    }

    private List<YieldCategoryRuleDTO> convertYieldCategoryToYieldCategoryDTOs(Collection<YieldCategoryRule> rules) {
        return rules.stream().map(yieldCategoryRule -> YieldCategoryRuleDTO.builder()
                        .id(yieldCategoryRule.getId())
                        .marketCode(yieldCategoryRule.getMarketCode())
                        .rateCode(yieldCategoryRule.getRateCode())
                        .analyticalMarketCode(yieldCategoryRule.getAnalyticalMarketCode())
                        .bookingStartDate(yieldCategoryRule.getBookingStartDate())
                        .bookingEndDate(yieldCategoryRule.getBookingEndDate())
                        .rank(yieldCategoryRule.getRank()).build()).
                collect(Collectors.toList());
    }

    public long populateYieldCategoryByRuleForStraightAndGroupMS() {
        List<AnalyticalMarketSegment> rules = crudService.findByNamedQuery(AnalyticalMarketSegment.GET_STRAIGHT_AND_GROUP_AMS_RULES);
        Set<String> straightPreservedMappedMarketCodes = rules.stream()
                .filter(AnalyticalMarketSegment::isPreserved)
                .map(AnalyticalMarketSegment::getMappedMarketCode)
                .collect(Collectors.toSet());
        return rules.stream()
                .filter(rule -> straightPreservedMappedMarketCodes.contains(rule.getMappedMarketCode()))
                .peek(rule -> crudService.save(createYieldCategoryByRule(rule, null))).count();
    }

    public boolean isStraightPreservedEntriesAvailableInAMS() {
        List<AnalyticalMarketSegment> straightPreservedAmsRules = crudService.findByNamedQuery(AnalyticalMarketSegment.GET_STRAIGHT_AND_GROUP_PRESERVED_AMS_RULES);
        return isNotEmpty(straightPreservedAmsRules);
    }

    public void assignMarketSegmentWiseRateCodesToDefault(Map<String, Set<String>> marketSegWiseRateCodes) {
        Map<Integer, ForecastActivityType> forecastActivityTypesById = getForecastActivityTypesById();
        marketSegWiseRateCodes.forEach((marketCode, rateCodes) -> {
            AnalyticalMarketSegment defaultAms = getSingleAssignedMarketSegmentsByMappedCodeLike(marketCode + "_DEF");
            if (defaultAms == null) {
                return;
            }
            MarketSegmentMaster masterDefault = findMarketSegmentMaster(defaultAms.getMappedMarketCode());
            Integer forecastActivityTypeId = masterDefault.getForecastActivityTypeId();
            ForecastActivityType forecastActivityType = forecastActivityTypesById.get(forecastActivityTypeId);

            if (isMSRecodingSupportedForIndependentProduct() && isIndependentProductEnabled()) {
                independentProductsService.assignRateCodesFromMarketSegmentToTheMappedIndependentProduct(marketCode + "_DEF", rateCodes);
            }
            for (String rateCode : rateCodes) {
                assignRateCodeToDefault(rateCode, marketCode, defaultAms, forecastActivityType);
            }
            updateRateCodeTypeAndRankForDefaultAmsAssignments(marketCode + "_DEF");
        });
    }

    private boolean isMSRecodingSupportedForIndependentProduct() {
        return configParamsServiceLocal.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_MS_RECODING_SUPPORT_FOR_INDEPENDENT_PRODUCT);
    }

    public void assignRateCodeToDefault(String rateCode, String marketCode, AnalyticalMarketSegment currentDefault, ForecastActivityType forecastActivityType) {

        AnalyticalMarketSegmentSummary summary = new AnalyticalMarketSegmentSummary();
        summary.setRateCode(rateCode);
        summary.setMarketCode(marketCode);
        summary.setMappedCode(currentDefault.getMappedMarketCode());
        summary.setRateCodeType(currentDefault.getRateCodeType().name());

        createAnalyticalMarketSegment(summary, currentDefault.getAttribute(), forecastActivityType, currentDefault.isComplimentary());
    }

    @SuppressWarnings("unchecked")
    public List<AnalyticalMarketSegment> getAssignedMarketSegmentsByMappedCodeLike(String like) {
        return crudService.findByNamedQuery(AnalyticalMarketSegment.LIKE_MAPPED_MARKET_CODE, QueryParameter.with(MAPPED_MARKET_CODE, like).parameters());
    }

    public AnalyticalMarketSegment getSingleAssignedMarketSegmentsByMappedCodeLike(String like) {
        return crudService.findByNamedQuerySingleResult(AnalyticalMarketSegment.LIKE_MAPPED_MARKET_CODE, QueryParameter.with(MAPPED_MARKET_CODE, like).parameters());
    }

    public void updateRateCodeTypeAndRankForDefaultAmsAssignments(String mappedCode) {
        List<AnalyticalMarketSegment> defaults = getAssignedMarketSegmentsByMappedCodeLike(mappedCode);
        defaults.stream().filter(ams -> ams.getRateCode() != null).forEach(ams -> {
            ams.setRateCodeType(RateCodeTypeEnum.EQUALS);
            ams.setRank(RateCodeTypeEnum.EQUALS.getRank());
        });
        crudService.save(defaults);
    }

    @SuppressWarnings("unchecked")
    private List<YieldCategoryRule> getYieldCategoryByRulesForPreview() {
        List<YieldCategoryRule> results = new ArrayList<>();
        List<AnalyticalMarketSegment> rules = getAllSortedByRank();

        for (AnalyticalMarketSegment rule : rules) {
            if (rule.getRateCodeType().equals(RateCodeTypeEnum.DEFAULT)) {
                results.add(createYieldCategoryByRule(rule, null));
            } else if (!rule.getRateCodeType().equals(RateCodeTypeEnum.ALL)) {
                for (String rateCode : findRateCodes(rule, true)) {
                    results.add(createYieldCategoryByRule(rule, rateCode));
                }
            } else {
                results.add(createYieldCategoryByRule(rule, null));
            }
        }

        return results;
    }

    @SuppressWarnings("unchecked")
    public List<RateCodeSummary> getRateCodes(List<String> marketCodes) {
        final long start = System.currentTimeMillis();
        List<RateCodeSummary> list = crudService.findByNamedQuery(RateCodeSummary.BY_MARKET_CODES,
                QueryParameter.with(MARKET_CODES, marketCodes).parameters());
        List<AnalyticalMarketSegment> rules = crudService.findByNamedQuery(
                AnalyticalMarketSegment.BY_MARKET_CODES,
                QueryParameter.with(MARKET_CODES, marketCodes).parameters());

        Map<String, List<AnalyticalMarketSegment>> marketCodeForFilter = rules.stream().collect(Collectors.groupingBy(AnalyticalMarketSegment::getMarketCode));

        list = list.stream().filter(getRateCodeSummaryPredicate(marketCodeForFilter)).collect(Collectors.toList());


        LOGGER.info(String.format("Found %d rate codes in %dms", list.size(),
                (System.currentTimeMillis() - start)));
        return list;
    }

    private java.util.function.Predicate<RateCodeSummary> getRateCodeSummaryPredicate(Map<String, List<AnalyticalMarketSegment>> marketCodeForFilter) {
        return (summary) -> {
            List<AnalyticalMarketSegment> ruleList = marketCodeForFilter.get(summary.getMarketCode());
            if (ruleList != null) {
                for (AnalyticalMarketSegment rule : ruleList) {
                    if (matches(summary.getRateCode(), rule.getRateCode(), rule.getRateCodeType())) {
                        return false;
                    }
                }
            }
            return true;
        };
    }

    private Integer getTotalRoomsSold() {
        String sql = TOTAL_ROOM_SOLD_SQL;
        Query query = crudService.getEntityManager().createNativeQuery(sql);
        return (Integer) query.getSingleResult();
    }

    private AnalyticalMarketSegment createAnalyticalMarketSegment(AnalyticalMarketSegmentSummary summary,
                                                                  AnalyticalMarketSegmentAttribute attribute, ForecastActivityType forecastActivityType, boolean complimentary) {
        AnalyticalMarketSegment ams = new AnalyticalMarketSegment();
        ams.setMarketCode(summary.getMarketCode());
        ams.setRateCode(summary.getRateCode());
        ams.setMappedMarketCode(summary.getMappedCode());
        // If this is a block attribute, set the booking block percent to block (100%)
        ams.setBlockPercent(attribute.getBlock() ? BOOKING_BLOCK_PCT_IS_BLOCK : BOOKING_BLOCK_PCT_DEFAULT);
        ams.setForecastActivityType(forecastActivityType);
        ams.setComplimentary(complimentary);
        if (summary.getRateCodeType() != null) {
            ams.setRateCodeType(Enum.valueOf(RateCodeTypeEnum.class, summary.getRateCodeType()));
            ams.setRank(Enum.valueOf(RateCodeTypeEnum.class, summary.getRateCodeType()).getRank());
        }

        if (shouldCreateNewAnalyticalMarketSegment(ams)) {
            ams.setAttribute(attribute);
            crudService.save(ams);
        }
        return ams;
    }

    private boolean shouldCreateNewAnalyticalMarketSegment(AnalyticalMarketSegment ams) {
        return crudService.findByExampleSingleResult(ams) == null;
    }

    public void createDefaultOrStraightMSForAMS(final AnalyticalMarketSegment ams) {
        final MarketSegmentMaster marketSegmentMaster = findMarketSegmentMaster(ams.getMappedMarketCode());
        if (marketSegmentMaster != null && marketSegmentMaster.getForecastActivityTypeId() != null && ams.getForecastActivityType() == null) {
            ams.setForecastActivityType(crudService.find(ForecastActivityType.class, marketSegmentMaster.getForecastActivityTypeId()));
        }
        handleMarketSegmentChanges(ams.getMappedMarketCode(), ams, ams.getAttribute(), true, true, null);
    }

    private void handleMarketSegmentChanges(String originalMappedCode, AnalyticalMarketSegment analyticalMarketSegment,
                                            AnalyticalMarketSegmentAttribute attribute, boolean isUpdateDetailsProposed,
                                            boolean createMktSegIfNotFound, Product product) {

        // Update the MarketSegmentMaster for the new attribution
        updateMarketSegmentMaster(originalMappedCode, analyticalMarketSegment, attribute, EDITABLE, product);

        // Update an existing MktSeg if the attribution changed - otherwise just look up the MktSeg
        MktSeg mktSeg = updateMktSeg(originalMappedCode, analyticalMarketSegment, createMktSegIfNotFound);

        // Attribution changes require a MktSegDetailsProposed to be created/updated
        if (mktSeg == null) {
            LOGGER.warn("MktSeg not found for code: " + analyticalMarketSegment.getMappedMarketCode());
        } else if (isUpdateDetailsProposed) {
            updateMktSegDetailsProposed(analyticalMarketSegment, attribute, mktSeg, product);
        }


        // If this is the default AMS ("_DEF"), it will have a RateCodeTypeEnum of DEFAULT.
        // We will create a MarketSegmentMaster with the same attributes.  The process will also
        // create a MktSegmentDetailsProposed with those attributes.  The attributes are then proposed
        // for the straight MarketSegment.
        if (analyticalMarketSegment.getRateCodeType() == RateCodeTypeEnum.DEFAULT) {
            AnalyticalMarketSegment ams = new AnalyticalMarketSegment();
            ams.setAttribute(attribute);
            ams.setBlockPercent(analyticalMarketSegment.getBlockPercent());
            // We will use the basic market code without the suffix
            ams.setMappedMarketCode(analyticalMarketSegment.getMarketCode());
            ams.setMarketCode(analyticalMarketSegment.getMarketCode());
            // Use the rank on the RateCodeTypeEnum
            ams.setRank(RateCodeTypeEnum.ALL.getRank());
            ams.setRateCode(analyticalMarketSegment.getRateCode());
            ams.setRateCodeType(RateCodeTypeEnum.ALL);
            ams.setForecastActivityType(analyticalMarketSegment.getForecastActivityType());
            ams.setComplimentary(analyticalMarketSegment.isComplimentary());
            // Update the straight MktSeg as well
            handleMarketSegmentChanges(analyticalMarketSegment.getMarketCode(), ams, attribute, isUpdateDetailsProposed, false, product);
        }
    }

    protected MktSegDetailsProposed updateMktSegDetailsProposed(AnalyticalMarketSegment analyticalMarketSegment, AnalyticalMarketSegmentAttribute attribute, MktSeg mktSeg, Product product) {
        // If a MktSegDetailsProposed doesn't exist create it
        MktSegDetailsProposed mktSegDetailsProposed = mktSeg.getMktSegDetailsProposed();
        if (mktSegDetailsProposed == null) {
            return createMktSegDetailsProposed(analyticalMarketSegment, attribute, mktSeg, product);
        }

        // If the MktSegDetailsProposed already exists, update it with the new attributes
        setStdValues(mktSegDetailsProposed, analyticalMarketSegment, attribute, product);
        return crudService.save(mktSegDetailsProposed);
    }

    public MktSeg updateMktSeg(String originalMappedCode, AnalyticalMarketSegment analyticalMarketSegment, boolean createMktSegIfNotFound) {

        // Look up the MktSeg - it could be for a code that has been changed
        MktSeg existingMappedMktSeg = findMktSegByCode(originalMappedCode);

        if (existingMappedMktSeg == null && createMktSegIfNotFound) {
            return createMarketSegmentForCode(originalMappedCode, analyticalMarketSegment.isComplimentary());
        }

        // If the old/new mapped codes are different, we should update the existing one
        String newMappedMarketCode = analyticalMarketSegment.getMappedMarketCode();
        MktSeg newMappedMktSeg = findMktSegByCode(newMappedMarketCode);

        if (existingMappedMktSeg != null && newMappedMktSeg == null && !StringUtils.equals(originalMappedCode, newMappedMarketCode)) {
            existingMappedMktSeg.setCode(newMappedMarketCode);
            existingMappedMktSeg.setName(newMappedMarketCode);
            existingMappedMktSeg.setDescription(newMappedMarketCode);
            existingMappedMktSeg.setComplimentary(analyticalMarketSegment.isComplimentary());
            return crudService.save(existingMappedMktSeg);
        }

        // If there is no MktSeg for the originalMapped code, check to see if there is one for the new mappedCode

        MktSeg updatedMktSeg = existingMappedMktSeg != null ? existingMappedMktSeg : newMappedMktSeg;
        if (null != updatedMktSeg) {
            updatedMktSeg.setComplimentary(analyticalMarketSegment.isComplimentary());
            crudService.save(updatedMktSeg);
        }
        return updatedMktSeg;
    }

    private MktSeg createMarketSegmentForCode(String marketSegmentCode, boolean complimentary) {
        MktSeg marketSegment = new MktSeg();

        marketSegment.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        marketSegment.setCode(marketSegmentCode);
        marketSegment.setName(marketSegmentCode);
        marketSegment.setDescription(marketSegmentCode);
        marketSegment.setEditable(EDITABLE);
        marketSegment.setStatusId(Constants.ACTIVE_STATUS_ID);
        marketSegment.setComplimentary(complimentary);

        return crudService.save(marketSegment);
    }

    public MarketSegmentMaster updateMarketSegmentMaster(String originalMappedCode, AnalyticalMarketSegment analyticalMarketSegment,
                                                  AnalyticalMarketSegmentAttribute attribute, Integer editableFlag, Product product) {
        String mappedMarketCode = analyticalMarketSegment.getMappedMarketCode();

        // Look up the MarketSegmentMaster
        MarketSegmentMaster marketSegmentMaster = findMarketSegmentMaster(mappedMarketCode);
        if (marketSegmentMaster == null) {
            marketSegmentMaster = new MarketSegmentMaster();
        }

        // If the mapped market code has changed and the new mapped market code already exists
        // we need to remove the old master record
        MarketSegmentMaster originalMarketSegmentMaster = findMarketSegmentMaster(originalMappedCode);
        if (originalMarketSegmentMaster != null && !StringUtils.equals(originalMappedCode, mappedMarketCode)) {
            crudService.delete(originalMarketSegmentMaster);
        }

        // If this is a block attribute, set the booking block percent to block (100%)
        marketSegmentMaster.setBookingBlockPc(attribute.getBlock() ? BOOKING_BLOCK_PCT_IS_BLOCK : BOOKING_BLOCK_PCT_DEFAULT);

        marketSegmentMaster.setBusinessTypeId(attribute.getBusinessType().getId());
        ForecastActivityType forecastActivityType = analyticalMarketSegment.getForecastActivityType();
        marketSegmentMaster.setForecastActivityTypeId(forecastActivityType == null ? null : forecastActivityType.getId());
        marketSegmentMaster.setYieldTypeId(attribute.getYieldType().getId());
        marketSegmentMaster.setCode(mappedMarketCode);
        marketSegmentMaster.setDescription(mappedMarketCode);
        marketSegmentMaster.setName(mappedMarketCode);

        marketSegmentMaster.setFenced(attribute.getFenced() ? 1 : 0);
        marketSegmentMaster.setLink(attribute.getLinkType().getId());
        marketSegmentMaster.setPackageValue(attribute.getPackaged() ? 1 : 0);
        marketSegmentMaster.setPriceByBar(getPriceByBar(attribute, product));
        marketSegmentMaster.setQualified(attribute.getQualified() ? 1 : 0);
        marketSegmentMaster.setIsEditable(editableFlag);
        return crudService.save(marketSegmentMaster);
    }

    private int getPriceByBar(AnalyticalMarketSegmentAttribute attribute, Product product) {
        if (product == null || attribute != AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR) {
            return attribute.getPricedByBar() ? 1 : 0;
        }
        return product.isSystemDefault() ? 1 : 0;
    }

    private MktSegDetailsProposed createMktSegDetailsProposed(AnalyticalMarketSegment analyticalMarketSegment,
                                                              AnalyticalMarketSegmentAttribute attribute, MktSeg mktSeg, Product product) {

        MktSegDetailsProposed proposed = new MktSegDetailsProposed();

        setStdValues(proposed, analyticalMarketSegment, attribute, product);

        proposed.setMktSeg(mktSeg);
        mktSeg.setMktSegDetailsProposed(proposed);
        mktSeg.setEditable(EDITABLE);

        //Default these for newly created proposed details
        proposed.setProcessStatus(lookupProcessStatusById(ProcessStatus.UPDATED));
        proposed.setStatusId(Constants.ACTIVE_STATUS_ID);
        proposed.setTemplateDefault(0);
        proposed.setTemplateId(0);

        crudService.save(proposed);
        crudService.save(mktSeg);
        return proposed;
    }

    // Set the values for the MktSegDetailsProposed
    private void setStdValues(MktSegDetailsProposed mktSegDetailsProposed, AnalyticalMarketSegment analyticalMarketSegment,
                              AnalyticalMarketSegmentAttribute attribute, Product product) {
        final boolean isAttributeBlocked =
                attribute.equals(AnalyticalMarketSegmentAttribute.GROUP) || attribute.getBlock();
        mktSegDetailsProposed.setBookingBlockPc(isAttributeBlocked ? BOOKING_BLOCK_PCT_IS_BLOCK :
                BOOKING_BLOCK_PCT_DEFAULT);
        mktSegDetailsProposed.setBusinessType(lookupBusinessType(attribute.getBusinessType()));
        mktSegDetailsProposed.setForecastActivityType(analyticalMarketSegment.getForecastActivityType());
        mktSegDetailsProposed.setYieldType(lookupYieldType(attribute.getYieldType()));
        mktSegDetailsProposed.setFenced(attribute.getFenced() ? 1 : 0);
        mktSegDetailsProposed.setLink(attribute.getLinkType().getId());
        mktSegDetailsProposed.setPackageValue(attribute.getPackaged() ? 1 : 0);
        mktSegDetailsProposed.setPriceByBar(getPriceByBar(attribute, product));
        mktSegDetailsProposed.setQualified(attribute.getQualified() ? 1 : 0);
    }

    private Collection<MarketSegmentMaster> findMarketSegmentMasterByMarketCode(String marketCode) {
        MarketSegmentMaster master = new MarketSegmentMaster();
        master.setCode(marketCode);
        return crudService.findByExample(master);
    }

    public MarketSegmentMaster findMarketSegmentMaster(String marketCode) {
        crudService.flushAndClear();
        MarketSegmentMaster master = new MarketSegmentMaster();
        master.setCode(marketCode);
        return crudService.findByExampleSingleResult(master);
    }

    public MarketSegmentSummary findOrCreateStraightOrDefaultMarketSegmentSummary(String marketSegmentCode) {
        final MarketSegmentSummary summary = getMarketSegmentIfPresent(marketSegmentCode);
        if (summary != null) {
            return summary;
        }
        List<AnalyticalMarketSegment> analyticalMarketSegments = crudService.findByNamedQuery(AnalyticalMarketSegment.BY_MARKET_CODE,
                QueryParameter.with(MARKET_CODE, marketSegmentCode).parameters());
        java.util.function.Predicate<AnalyticalMarketSegment> isStraightOrDefault = ams -> STRAIGHT_OR_DEFAULT_RANKS.contains(ams.getRank());
        String mappedMarketCode = analyticalMarketSegments.stream().filter(isStraightOrDefault)
                .map(AnalyticalMarketSegment::getMappedMarketCode).findFirst().orElse(marketSegmentCode);
        return findOrCreateMarketSegmentSummary(mappedMarketCode);
    }

    public Boolean applyHistoryChangeImpact(Integer mktSegId) {
        List<AMSCompositionChange> entities = crudService.findAll(AMSCompositionChange.class);
        Map<Integer, Boolean> applyHistoryMap = new HashMap<>();
        entities.forEach(entity -> updateApplyHistoryMap(applyHistoryMap, entity));
        return applyHistoryMap.get(mktSegId);
    }

    public MarketSegmentSummary findOrCreateMarketSegmentSummary(String marketSegmentCode) {
        MarketSegmentSummary summary = getMarketSegmentIfPresent(marketSegmentCode);

        if (summary != null) {
            return summary;
        }
        // Save the new MktSeg
        return crudService.save(getMarketSegmentSummary(marketSegmentCode));
    }

    private MarketSegmentSummary getMarketSegmentIfPresent(final String marketSegmentCode) {
        return crudService.findByNamedQuerySingleResult(MarketSegmentSummary.BY_CODE,
                QueryParameter.with("code", marketSegmentCode).parameters());
    }

    private MarketSegmentSummary getMarketSegmentSummary(final String marketSegmentCode) {
        LOGGER.info("Unable to find Market Segment: " + marketSegmentCode + ", creating new MarketSegmentSummary");
        MarketSegmentSummary summary = new MarketSegmentSummary();
        summary.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        summary.setCode(marketSegmentCode);
        summary.setName(marketSegmentCode);
        summary.setDescription(marketSegmentCode);
        summary.setStatusId(Constants.ACTIVE_STATUS_ID);
        summary.setEditable(EDITABLE);
        return summary;
    }

    public void createMissingMarketSegmentFromHotelMarketSummary() {
        List<String> marketCodes = amsRepository.findMissedMarketSegmentCodesForProperty();
        final List<MarketSegmentSummary> marketSegmentSumaries =
                marketCodes.stream().map(this::getMarketSegmentSummary).collect(Collectors.toList());
        crudService.save(marketSegmentSumaries);
    }

    public boolean isAmsFlagEnabled() {
        return configParamsServiceLocal.getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value());
    }

    public BusinessType lookupBusinessType(AnalyticalMarketSegmentAttribute.MarketSegmentBusinessType marketSegmentBusinessType) {
        return (BusinessType) crudService.findByNamedQuerySingleResult(BusinessType.BY_NAME,
                QueryParameter.with("name", marketSegmentBusinessType.getName()).parameters());
    }

    public YieldType lookupYieldType(AnalyticalMarketSegmentAttribute.MarketSegmentYieldType segmentYieldType) {
        return (YieldType) crudService.findByNamedQuerySingleResult(YieldType.BY_NAME,
                QueryParameter.with("name", segmentYieldType.getName()).parameters());
    }

    public ProcessStatus lookupProcessStatusById(int statusId) {
        return crudService.find(ProcessStatus.class, statusId);
    }

    @SuppressWarnings("unchecked")
    private int unassignMarketSegment(AnalyticalMarketSegment analyticalMarketSegment) {
        crudService.delete(analyticalMarketSegment);
        deleteFromMarketSegmentMaster(analyticalMarketSegment.getMappedMarketCode());
        crudService.flush();

        // remove default if it's the last assignment
        boolean onlyDefaultsRemain = true;
        List<AnalyticalMarketSegment> assigned = crudService.findByNamedQuery(AnalyticalMarketSegment.BY_MARKET_CODE, QueryParameter.with(MARKET_CODE, analyticalMarketSegment.getMarketCode()).parameters());
        String defaultCode = analyticalMarketSegment.getMarketCode() + DEFAULT_AFFIX;

        for (AnalyticalMarketSegment assignment : assigned) {
            if (!StringUtils.equals(assignment.getMappedMarketCode(), defaultCode)) {
                onlyDefaultsRemain = false;
            }
        }

        if (onlyDefaultsRemain && !assigned.isEmpty()) {
            crudService.delete(assigned);
            deleteFromMarketSegmentMaster(defaultCode);
            deletePreservedAMS(defaultCode);
        }
        return 1;
    }

    private void deletePreservedAMS(String mappedMarketCode) {
        final List<AnalyticalMarketSegment> analyticalMarketSegments = crudService.findByNamedQuery(AnalyticalMarketSegment.BY_MAPPED_MARKET_CODE,
                QueryParameter.with(MAPPED_MARKET_CODE, mappedMarketCode).parameters());

        final List<AnalyticalMarketSegment> preservedAMSList = analyticalMarketSegments.stream()
                .filter(AnalyticalMarketSegment::isPreserved)
                .collect(Collectors.toList());

        crudService.delete(preservedAMSList);

    }

    private void deleteFromMarketSegmentMaster(String mappedCode) {
        if (shouldDeleteMappedCodeFromMaster(mappedCode)) {
            crudService.delete(findMarketSegmentMasterByMarketCode(mappedCode));
            independentProductsService.deleteMappingForMarketSegment(mappedCode);
            if (mappedCode.endsWith(DEFAULT_AFFIX)) {
                List<MarketSegmentMaster> masterList = new ArrayList<>(findMarketSegmentMasterByMarketCode(mappedCode.substring(0, mappedCode.indexOf(DEFAULT_AFFIX))));
                for (MarketSegmentMaster master : masterList) {
                    //Check to see if non default code exists in Mkt_Seg. If it exists, and also exists in the proposed table, delete the proposed entry.
                    MktSeg marketSegment = crudService.findByNamedQuerySingleResult(MktSeg.BY_CODE, QueryParameter.with("code", master.getCode()).parameters());
                    if (marketSegment != null) {
                        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
                        MktSegDetailsProposed proposed = crudService.findByNamedQuerySingleResult(MktSegDetailsProposed.BY_MKT_SEG_ID, QueryParameter.with("id", marketSegment.getId()).and(PROPERTY_ID, propertyId).parameters());
                        if (proposed != null) {
                            crudService.delete(proposed);
                        }
                    }
                }
                crudService.delete(masterList);
            }
        }
    }

    private boolean shouldDeleteMappedCodeFromMaster(String mappedCode) {
        List<AnalyticalMarketSegment> analyticalMarketSegments = crudService.findByNamedQuery(AnalyticalMarketSegment.BY_MAPPED_MARKET_CODE, QueryParameter.with(MAPPED_MARKET_CODE, mappedCode).parameters());
        return CollectionUtils.isEmpty(analyticalMarketSegments);
    }

    private boolean matches(String rateCode, String rateCodeRule, RateCodeTypeEnum rateCodeType) {
        if (RateCodeTypeEnum.ALL.equals(rateCodeType) &&
                (rateCodeRule == null)) {
            return true;
        }

        if (RateCodeTypeEnum.EQUALS.equals(rateCodeType) &&
                (StringUtils.equalsIgnoreCase(rateCodeRule, rateCode))) {
            return true;
        }

        if (RateCodeTypeEnum.STARTS_WITH.equals(rateCodeType) &&
                (rateCode != null) && rateCode.toUpperCase().startsWith(rateCodeRule.toUpperCase())) {
            return true;
        }

        if (RateCodeTypeEnum.ENDS_WITH.equals(rateCodeType) &&
                (rateCode != null) && rateCode.toUpperCase().endsWith(rateCodeRule.toUpperCase())) {
            return true;
        }

        if (RateCodeTypeEnum.CONTAINS.equals(rateCodeType) &&
                (rateCode != null) && rateCode.toUpperCase().contains(rateCodeRule.toUpperCase())) {
            return true;
        }

        return (RateCodeTypeEnum.DEFAULT.equals(rateCodeType) && (rateCode != null) && rateCode.equalsIgnoreCase(rateCodeRule));
    }

    private String buildMappedMarketCode(String marketCode, AnalyticalMarketSegmentAttribute attribute, Optional<Product> product) {
        if (product.isPresent() && product.get().isIndependentProduct() && isIndependentProductsEnabled() && isIdenticalMsAttributeForIPEnabled()) {
            String productPrefix = product.get().isSystemDefault() ? EMPTY : "IP" + product.get().getId();
            return marketCode + "_1_" + productPrefix + "_" + attribute.getSuffix();
        }
        String productPrefix = product.isEmpty() || product.get().isSystemDefault() ? EMPTY : "IP" + product.get().getId() + "_";
        return marketCode + "_" + productPrefix + attribute.getSuffix();
    }

    private boolean isIndependentProductsEnabled() {
        return configParamsServiceLocal.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
    }

    @SuppressWarnings("unchecked")
    private List<String> findRateCodes(final AnalyticalMarketSegment rule, boolean forPreview) {
        String sql;

        if (forPreview) {
            sql = "SELECT DISTINCT Rate_Code FROM Ams_Occupancy_Summary " +
                    "WHERE Market_Code = :marketCode AND Rate_Code LIKE :rateCode ";
        } else {
            sql = "SELECT DISTINCT Rate_Code FROM opera.Raw_Transaction " +
                    "WHERE Market_Code = :marketCode AND Rate_Code LIKE :rateCode ";
        }

        List<String> results = new ArrayList<>();

        RateCodeTypeEnum rateCodeType = rule.getRateCodeType();
        String rateCode = rule.getRateCode();
        if ((rateCodeType == null) || rateCodeType.equals(RateCodeTypeEnum.EQUALS)) {
            results.add(rateCode);
        } else {
            String marketCode = rule.getMarketCode();
            if (rateCodeType.equals(RateCodeTypeEnum.STARTS_WITH)) {
                List<String> rateCodes = crudService.findByNativeQuery(sql,
                        QueryParameter.with(MARKET_CODE, marketCode)
                                .and(RATE_CODE, rateCode + "%").parameters());
                results.addAll(rateCodes);
            } else if (rateCodeType.equals(RateCodeTypeEnum.CONTAINS)) {
                List<String> rateCodes = crudService.findByNativeQuery(sql,
                        QueryParameter.with(MARKET_CODE, marketCode)
                                .and(RATE_CODE, "%" + rateCode + "%").parameters());
                results.addAll(rateCodes);
            } else if (rateCodeType.equals(RateCodeTypeEnum.ENDS_WITH)) {
                List<String> rateCodes = crudService.findByNativeQuery(sql,
                        QueryParameter.with(MARKET_CODE, marketCode)
                                .and(RATE_CODE, "%" + rateCode).parameters());
                results.addAll(rateCodes);
            }
        }

        return results;
    }

    private YieldCategoryRule createYieldCategoryByRule(AnalyticalMarketSegment rule, String rateCode) {
        YieldCategoryRule ycbr = new YieldCategoryRule();
        ycbr.setMarketCode(rule.getMarketCode());
        ycbr.setAnalyticalMarketCode(rule.getMappedMarketCode());
        ycbr.setBookingStartDate(new LocalDate("1800-01-01"));
        ycbr.setBookingEndDate(new LocalDate("2999-12-31"));
        ycbr.setRank(rule.getRank());
        ycbr.setRateCode(rateCode);
        return ycbr;
    }

    public boolean needsInitialRefresh() {
        Integer propertyId = PacmanThreadLocalContextHolder.getWorkContext().getPropertyId();
        Map<String, Object> params = new HashMap<>();
        RowMapper<Integer> integerRowMapper = row -> (Integer) row[0];
        RowMapper<BigInteger> longRowMapper = row -> (BigInteger) row[0];

        String sqlSummaryFirstRecordQuery = "select top 1 Ams_Occupancy_Summary_ID from Ams_Occupancy_Summary";
        Integer amsOccupancySummaries = crudService.findByNativeQuerySingleResult(sqlSummaryFirstRecordQuery, params, integerRowMapper);

        if (isNGI()) {
            Property property = propertyService.getPropertyById(propertyId);
            Map<String, String> restParams = new HashMap<>();
            restParams.put("clientCode", property.getClient().getCode());
            restParams.put("propertyCode", property.getCode());
            List<JSONObject> jsonObjectList = restClient.getDataFromEndpoint(RestEndpoints.FETCH_ONE_OF_NGI_OCCUPANCY_SUMMARIES, restParams);
            return amsOccupancySummaries == null && jsonObjectList != null && !jsonObjectList.isEmpty();
        } else {
            if ((amsOccupancySummaries != null)) {
                return false;
            }
            String sqlHistoryFirstRecordQuery = "select top 1 History_Transaction_ID transactions from opera.History_Transaction";
            BigInteger transactions = crudService.findByNativeQuerySingleResult(sqlHistoryFirstRecordQuery, params, longRowMapper);

            return null != transactions;
        }
    }

    @SuppressWarnings("unchecked")
    public int getSharedRateCodesInThisMarketSegment(String marketSegmentId) {
        String sqlHistoryCount = "select count(p.Rate_Code) as rateCodeCount " +
                " from (select Distinct Market_Code, Rate_Code from Ams_Occupancy_Summary " +
                " where Rate_Code in (select Rate_Code from Ams_Occupancy_Summary aos where Market_Code = :marketCode and Rate_Code is not null and Rate_Code != '')) " +
                " as p " +
                " where not exists (select Rate_Code from Analytical_Mkt_Seg ams where Market_Code = :marketCode and p.Rate_Code = ams.Rate_Code and ams.Rate_Code is not NULL and ams.Rate_Code_Type = 'EQUALS') " +
                " and not exists (select aos.Rate_Code from Ams_Occupancy_Summary aos join Analytical_Mkt_Seg ams on aos.Market_Code = ams.Market_Code where aos.Market_Code = :marketCode and p.Rate_Code like ams.Rate_Code + '%' and ams.Rate_Code_Type = 'STARTS_WITH') " +
                " and not exists (select aos.Rate_Code from Ams_Occupancy_Summary aos join Analytical_Mkt_Seg ams on aos.Market_Code = ams.Market_Code where aos.Market_Code = :marketCode and p.Rate_Code like '%' + ams.Rate_Code and ams.Rate_Code_Type = 'ENDS_WITH') " +
                " and not exists (select aos.Rate_Code from Ams_Occupancy_Summary aos join Analytical_Mkt_Seg ams on aos.Market_Code = ams.Market_Code where aos.Market_Code = :marketCode and p.Rate_Code like '%' + ams.Rate_Code + '%' and ams.Rate_Code_Type = 'CONTAINS') " +
                " group by p.Rate_Code";
        Map<String, Object> params = new HashMap<>();
        params.put(MARKET_CODE, marketSegmentId);
        List<Integer> rateCodeCounts = crudService.findByNativeQuery(sqlHistoryCount, params);
        int numberOfSharedRateCodes = 0;
        for (Integer rateCodeCount : rateCodeCounts) {
            if (rateCodeCount > 1) {
                numberOfSharedRateCodes++;
            }
        }
        return numberOfSharedRateCodes;
    }

    public void loadDataFromNGI(List<? extends Map<String, Object>> list) {
        List<AmsOccupancySummary> occupancySummaries = new ArrayList<>(list.size());
        for (Map<String, Object> summaryMap : list) {
            String marketSegmentCode = (String) summaryMap.get("marketSegmentCode");
            String rateCode = (String) summaryMap.get(RATE_CODE);
            Integer roomsSold = (Integer) summaryMap.get("roomsSold");
            boolean group = (boolean) summaryMap.get("groupOccupancy");
            if (roomsSold == null) {
                roomsSold = 0;
            }
            Double roomRevenue = (Double) summaryMap.get(getRevenueKey());
            if (roomRevenue == null) {
                roomRevenue = 0d;
            }
            Integer block = (Integer) summaryMap.get("block");
            if (block == null) {
                block = 0;
            }
            Integer pickup = (Integer) summaryMap.get("pickup");
            if (pickup == null) {
                pickup = 0;
            }
            Date occupancyDate = null;
            try {
                occupancyDate = DateUtil.parseDate((String) summaryMap.get(OCCUPANCY_DATE), DateUtil.DEFAULT_DATE_FORMAT);
            } catch (ParseException pe) {
                LOGGER.error("error parsing occupancy date from string: " + summaryMap.get(OCCUPANCY_DATE), pe);
            }

            if (marketSegmentCode != null && !marketSegmentCode.isEmpty()) {
                AmsOccupancySummary occupancySummary = new AmsOccupancySummary();
                occupancySummary.setDataLoadMetadataID(0);
                occupancySummary.setRoomRevenue(roomRevenue);
                occupancySummary.setRoomsSold(roomsSold);
                occupancySummary.setMarketCode(marketSegmentCode);
                if (rateCode != null) {
                    occupancySummary.setRateCode(rateCode);
                } else {
                    occupancySummary.setRateCode("");
                }
                occupancySummary.setOccupancyDate(occupancyDate);
                occupancySummary.setBlock(block);
                occupancySummary.setPickup(pickup);
                occupancySummary.setGroup(group);
                occupancySummaries.add(occupancySummary);
            }
        }

        crudService.save(occupancySummaries);
    }

    private String getRevenueKey() {
        Integer totalRateEnabled = configParamsServiceLocal.getIntegerParameterValue(FeatureTogglesConfigParamName.TOTAL_RATE_ENABLED.value());
        if (totalRateEnabled != null && totalRateEnabled == 1) {
            return "roomRate";
        }
        return "roomRevenue";
    }

    private boolean isNGI() {
        String sourceSystem = configParamsServiceLocal.getParameterValue(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM);
        return Constants.NGI.equalsIgnoreCase(sourceSystem);
    }

    private int getUnassignedGroupAndIndividualMktSegmentCount() {
        int count;

        count = crudService.findByNamedQuerySingleResult(AnalyticalMarketSegmentSummary.UNASSIGNED_GROUP_AND_INDIVIDUAL_CNT);

        return count;
    }

    @SuppressWarnings("unchecked")
    public List<AnalyticalMarketSegment> getAnalyticalMarketSegmentByMappedMarketCodes(Collection<String> mappedMarketCodes) {
        if (CollectionUtils.isEmpty(mappedMarketCodes)) {
            return new ArrayList<>();
        }
        return crudService.findByNamedQuery(
                AnalyticalMarketSegment.BY_MAPPED_MARKET_CODES,
                QueryParameter.with("mappedMarketCodes", mappedMarketCodes).parameters());
    }

    public MktSegChangeType findExactChangeInGivenMktSeg(String mktSegCode) {
        return findExactChangeInProposedList(getMktSegDetailsProposedByMkSegCode(mktSegCode));
    }

    public MktSegChangeType findExactChangeInAllMktSeg() {
        return findExactChangeInProposedList(getAllMktSegDetailsProposed());
    }

    public Map<String, BigDecimal> getMarketSegmentWithBusinessTypeChangeVolumeMap() {

        return crudService.findAll(MktSeg.class).stream()
                .filter(mktSeg -> MktSegChangeType.BUSINESSTYPE.equals(findExactChangeInGivenMktSeg(mktSeg.getCode())))
                .collect(Collectors.toMap(MktSeg::getCode, mktSeg -> getMarketSegmentWithBusinessTypeChangeVolume(mktSeg.getCode())));
    }

    public BigDecimal getMarketSegmentWithBusinessTypeChangeVolume(String marketCode) {
        Date endDate = dateService.getCaughtUpDate();
        Date startDate = DateUtil.addYearsToDate(endDate, -2);
        BigDecimal roomsSold = crudService.findByNamedQuerySingleResult(MktSegAccomActivity.GET_ROOMS_SOLD_FOR_MARKET_SEGMENT,
                QueryParameter.with("code", marketCode).and("startDate", startDate).and("endDate", endDate).parameters());

        BigDecimal totalRoomsSold = crudService.findByNamedQuerySingleResult(MktSegAccomActivity.GET_TOTAL_ROOMS_SOLD,
                QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());

        return (totalRoomsSold == null || roomsSold == null || BigDecimal.ZERO.equals(totalRoomsSold) ?
                ZERO_SCALE_2 : roomsSold.divide(totalRoomsSold, 4, RoundingMode.HALF_UP)).multiply(new BigDecimal(100)).setScale(2);
    }

    public BigDecimal getTotalVolumeForBusinessTypeChanges() {
        Map<String, BigDecimal> marketSegmentsWithBusinessTypeChangeVolumeMap = getMarketSegmentWithBusinessTypeChangeVolumeMap();
        BigDecimal totalVolume = ZERO_SCALE_2;
        for (Map.Entry<String, BigDecimal> entry : marketSegmentsWithBusinessTypeChangeVolumeMap.entrySet()) {
            totalVolume = totalVolume.add(entry.getValue());
        }
        return totalVolume;
    }

    private MktSegChangeType findExactChangeInProposedList(List<MktSegDetailsProposed> mktSegDetailsProposedList) {
        List<MktSegAttributes> mktSegDetailsList = crudService.findByNamedQuery(MktSegDetails.GET_ALL);
        if (amsChangeApplyToHistoryPresent()) {
            if (mktSegDetailsList.isEmpty()) {
                return MktSegChangeType.NEW_PROPERTY;
            }
            return MktSegChangeType.COMPOSITION_CHANGE;
        }
        MktSegChangeType mktSegChangeType = MktSegChangeType.NO_CHANGE;
        //check for no change
        if (mktSegDetailsProposedList == null || mktSegDetailsProposedList.isEmpty()) {
            return mktSegChangeType;
        }

        //check for new property
        if (mktSegDetailsList.isEmpty()) {
            return MktSegChangeType.NEW_PROPERTY;
        }

        Map<Integer, MktSegAttributes> mktSegDetailsMap = mktSegDetailsList
                .stream()
                .collect(Collectors.toMap(m -> m.getMktSeg().getId(), Function.identity()));

        //find out exact change
        for (MktSegDetailsProposed mktSegDetailsProposed : mktSegDetailsProposedList) {
            MktSegAttributes mktSegDetails = mktSegDetailsMap.get(mktSegDetailsProposed.getMktSeg().getId());
            if (mktSegDetails != null && isMktSegBusinessTypeChanged(mktSegDetailsProposed, mktSegDetails)) {
                mktSegChangeType = MktSegChangeType.BUSINESSTYPE;
            }
        }
        return mktSegChangeType.equals(MktSegChangeType.BUSINESSTYPE) ? mktSegChangeType : MktSegChangeType.SECONDARY_ATTRIBUTE;
    }

    private List<MktSegDetailsProposed> getAllMktSegDetailsProposed() {
        return crudService.findByNamedQuery(MktSegDetailsProposed.ALL);
    }

    private List<MktSegDetailsProposed> getMktSegDetailsProposedByMkSegCode(String mktSegCode) {
        return crudService.findByNamedQuery(MktSegDetailsProposed.GET_MARKET_SEGMENTS_BY_NAME, QueryParameter.with("mktSegName", mktSegCode).parameters());
    }

    private boolean isMktSegBusinessTypeChanged(MktSegDetailsProposed mktSegDetailsProposed, MktSegAttributes mktSegDetails) {
        return !mktSegDetails.getBusinessType().getId().equals(mktSegDetailsProposed.getBusinessType().getId());
    }

    public List<String> getUnassignedMappedMarketCodes(Collection<String> mappedMarketCodes) {
        List<String> retVal;

        // Get a list of AnalyticalMarketSegments for the collection of mappedMarketCodes passed in
        List<AnalyticalMarketSegment> analyticalMarketSegments = getAnalyticalMarketSegmentByMappedMarketCodes(mappedMarketCodes);

        // Get a Hashset of analyticalMarketSegmentMappedCodes which were in the results
        Set<String> analyticalMarketSegmentMappedCodes = analyticalMarketSegments.stream().map(AnalyticalMarketSegment::getMappedMarketCode).collect(Collectors.toCollection(HashSet::new));

        // Collect a list of the mappedMarketCodes passed in which do not exist in the ones found in the database
        retVal = mappedMarketCodes.stream().filter(mmc -> !analyticalMarketSegmentMappedCodes.contains(mmc)).collect(Collectors.toList());

        return retVal;
    }

    public void setCrudService(CrudService crudService) {
        this.crudService = crudService;
    }

    public List<ForecastActivityType> getForecastActivityTypes() {
        return crudService.findAll(ForecastActivityType.class);
    }

    private MktSeg findMktSegByCode(String marketCode) {
        // Don't remove the propertyId clause. This is to identify valid market-codes when mkt_seg is in fuzzy state
        return crudService.findByNamedQuerySingleResult(MktSeg.BY_PROPERTY_ID_AND_CODE,
                QueryParameter.with("code", marketCode)
                        .and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public boolean isAMSMappingPresent() {
        return crudService.findOne(AnalyticalMarketSegment.class) != null;
    }

    public int loadOccupancyDataFromReservationNightAndGroupBlock(Date businessDate) {
        final String insertQuery = AmsOccupancySummary.INSERT_AMS_OCCUPANCY_DATA_FROM_RES_NIGHT_AND_NIGHT_CHANGE;
        LOGGER.info("Generating AMS Occupancy Summary By : " + insertQuery);
        return crudService.executeUpdateByNamedQuery(insertQuery, QueryParameter.with(OCCUPANCY_DATE, businessDate).and(TOTAL_RATE_ENABLED, configParamsServiceLocal.getIntegerParameterValue(FeatureTogglesConfigParamName.TOTAL_RATE_ENABLED.value())).parameters());
    }

    public boolean shouldLoadAmsOccupancyDataFromG3() {
        return propertyService.isStageAtLeast(Stage.POPULATION, PacmanThreadLocalContextHolder.getWorkContext().getPropertyId());
    }

    public List<String> getAMSMarketSegmentCodes() {
        List<Pair<String, String>> pmsMsToAmsMappings = crudService.findByNativeQuery(MktSeg.GET_AMS_TO_PMS_MS_MAPPINGS_QUERY, Collections.emptyMap(), row -> new Pair<>((String) row[0], (String) row[1]));
        if (isNotEmpty(pmsMsToAmsMappings)) {
            return new ArrayList<>(pmsMsToAmsMappings.stream().map(Pair::getFirst).collect(Collectors.toSet()));
        }
        return new ArrayList<>();
    }

    public List<AnalyticalMarketSegment> getAnalyticalMarketSegments() {
        return crudService.findAll(AnalyticalMarketSegment.class);
    }

    public List<String> getPMSMarketSegmentCodes(Integer propertyId) {
        List<String> marketCodes = crudService.findByNamedQuery(MktSeg.CODES_BY_PROPERTY_ID, QueryParameter.with(PROPERTY_ID, propertyId).parameters());
        List<Pair<String, String>> pmsMsToAmsMappings = crudService.findByNativeQuery(MktSeg.GET_AMS_TO_PMS_MS_MAPPINGS_QUERY, Collections.emptyMap(), row -> new Pair<>((String) row[0], (String) row[1]));

        if (isNotEmpty(pmsMsToAmsMappings)) {
            //remove analytical mkt segs from the list to be returned
            List<String> analyticalMktSegs = pmsMsToAmsMappings.stream().map(Pair::getFirst).collect(Collectors.toList());
            marketCodes.removeAll(analyticalMktSegs);

            // add pms mkt segs to the list to be returned
            List<String> pmsMktSegs = pmsMsToAmsMappings.stream().map(Pair::getSecond).distinct().collect(Collectors.toList());
            marketCodes.addAll(pmsMktSegs);
        }

        // remove duplicated pms mkt segs and return the list
        return new ArrayList<>(new HashSet<>(marketCodes));
    }

    public boolean amsAlreadyExists(final String hotelMktSeg,
                                    final AnalyticalMarketSegmentAttribute selectedAttribute) {
        final Long numIdenticalMs =
                crudService.findByNamedQuerySingleResult(AnalyticalMarketSegment.COUNT_BY_MARKET_ATTRIBUTE_NON_DEF,
                        QueryParameter.with(
                                        MARKET_CODE, hotelMktSeg)
                                .and("attribute", selectedAttribute)
                                .and("rank", RateCodeTypeEnum.DEFAULT.getRank()).parameters());

        return numIdenticalMs > 0;
    }

    public Set<String> getNonDefaultAnalyticalMarketSegmentCodesByMsAttribute(final String hotelMktSeg, final AnalyticalMarketSegmentAttribute selectedAttribute) {
        final java.util.function.Predicate<AnalyticalMarketSegment> isNotDefault =
                ams -> !RateCodeTypeEnum.DEFAULT.equals(ams.getRateCodeType());
        List<AnalyticalMarketSegment> result = crudService.findByNamedQuery(AnalyticalMarketSegment.BY_MARKET_CODE_AND_ATTRIBUTE, QueryParameter.with(MARKET_CODE, hotelMktSeg).and("attribute", selectedAttribute).parameters());
        return new TreeSet<>(result.stream().filter(isNotDefault).map(AnalyticalMarketSegment::getMappedMarketCode).collect(Collectors.toSet()));

    }

    public int createDiscontinuedDefaultOrStraightMarketSegmentEntries() {
        final List<Integer> analyticalMarketSegmentIds =
                crudService.findByNamedQuery(AnalyticalMarketSegment.IDS_WITHOUT_DEFAULT_OR_STRAIGHT_MKT_SEG_ENTRY_DISCONTINUED_MS);

        if (CollectionUtils.isEmpty(analyticalMarketSegmentIds)) {
            return 0;
        }
        final List<AnalyticalMarketSegment> analyticalMarketSegments = crudService.findByNamedQuery(AnalyticalMarketSegment.BY_IDS, QueryParameter.with("ids", analyticalMarketSegmentIds).parameters());
        analyticalMarketSegments.stream().forEach(this::createDefaultOrStraightMSForAMS);
        return analyticalMarketSegments.size();
    }

    @Transactional(propagation = Propagation.NEVER)
    public void updateDiscontinuedMsByStraightOrDefiniteMs() {
        crudService.executeUpdateByNativeQuery("exec dbo.usp_update_discontinued_ms_by_straight_or_def");
    }

    public List<Integer> findDiscontinuedMarketSegments(List<String> marketCodes) {
        return crudService.findByNamedQuery(MktSeg.GET_DISCONTINUED_MKT_SEG_BY_HOTEL_MKT_CODE, QueryParameter.with(MARKET_CODES, marketCodes).parameters());
    }

    public int syncComplimentaryAttributeForNewlyCreatedMs() {
        LocalDate today = LocalDate.now();
        LocalDate caughtUpDate = dateService.getCaughtUpLocalDate();
        if (today.equals(caughtUpDate)) {
            caughtUpDate = today.minusDays(1);
        }
        Integer propertyId = PacmanThreadLocalContextHolder.getWorkContext().getPropertyId();
        Map<String, Object> parameters = QueryParameter.with(PROPERTY_ID, propertyId)
                .and("caughtUpDate", caughtUpDate.toDate()).parameters();
        return crudService.executeUpdateByNamedQuery(MktSeg.SYNC_COMPLIMENTARY_VALUES_FOR_NEWLY_CREATED_MKT_SEGMENTS, parameters);
    }

    public int createNewMarketSegmentsFromReservationNights(final List<String> marketCodes) {
        if (CollectionUtils.isEmpty(marketCodes)) {
            return 0;
        }

        final boolean isStreamingEnabled = configParamsServiceLocal.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_STREAMING_POPULATION_ENABLED);
        List<String> analyticalMarketCodes =
                crudService.findByNativeQuery(isStreamingEnabled ? GET_NEW_AMS_TO_BE_CREATED_INCLUDE_GROUP : GET_NEW_AMS_TO_BE_CREATED, QueryParameter.with(MARKET_CODES, marketCodes).parameters());
        List<String> straightMarketCodes = crudService.findByNativeQuery(isStreamingEnabled ?
                        GET_NEW_STRAIGHT_DEFAULT_MS_TO_BE_CREATED_INCLUDE_GROUPS : GET_NEW_STRAIGHT_DEFAULT_MS_TO_BE_CREATED,
                QueryParameter.with("rateCodeTypes", Arrays.asList(RateCodeTypeEnum.DEFAULT.name(), RateCodeTypeEnum.ALL.name()))
                        .and(MARKET_CODES, marketCodes).parameters());
        Set<String> newMarketCodes = Stream.of(analyticalMarketCodes, straightMarketCodes)
                .flatMap(List::stream).collect(Collectors.toSet());
        Logger.getLogger("New market segments to be created with codes " + newMarketCodes.stream().collect(Collectors.joining(",")));
        final Collection<MktSeg> newMarketSegments =
                marketSegmentRepository.createMarketSegments(newMarketCodes);
        Logger.getLogger("New Market Segments for property " + PacmanWorkContextHelper.getPropertyId() + " " + newMarketSegments.size());
        marketSegmentService.createDetailsProposedFromMaster();
        return newMarketSegments.size();
    }

    public List<String> retrieveHotelMarketCodes() {
        return crudService.findByNamedQuery(AnalyticalMarketSegmentAudit.FIND_HOTEL_MKT_SEGS);
    }

    public List<String> getDistinctMappedMarketCodesOf(String marketCode) {
        if (marketCode.isEmpty()) {
            return Collections.emptyList();
        }
        return crudService.findByNamedQuery(AnalyticalMarketSegment.GET_DISTINCT_MAPPED_MARKET_CODES_BY_MARKET_CODE,
                QueryParameter.with(MARKET_CODE, marketCode).parameters());
    }

    public List<String> getDistinctMappedMarketCodesByMarketCodesIn(List<String> marketCodesList) {
        if (marketCodesList.isEmpty()) {
            return Collections.emptyList();
        }
        return crudService.findByNamedQuery(AnalyticalMarketSegment.GET_DISTINCT_MAPPED_MARKET_CODES_BY_MARKET_CODE_IN,
                QueryParameter.with(MARKET_CODE, marketCodesList).parameters());
    }

    public List<AnalyticalMarketSegment> getAMSRulesWith(List<String> mktCodes, Set<String> rateCodes) {
        if (mktCodes.isEmpty() || rateCodes.isEmpty()) {
            return Collections.emptyList();
        }
        return crudService.findByNamedQuery(AnalyticalMarketSegment.BY_MARKET_AND_RATE_CODES,
                QueryParameter.with(MARKET_CODES, mktCodes)
                        .and("rateCodes", rateCodes).parameters());
    }

    public int populateMissingAmsEntries() {
        final Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        return populateMissingAmsEntries(propertyId);
    }

    public int populateMissingAmsEntries(Integer propertyId) {
        final List<MktSeg> mktSegs = crudService.findByNamedQuery(MktSeg.BY_PROPERTY_ID, QueryParameter.with(PROPERTY_ID, propertyId).parameters());
        final List<String> distinctAuditMappedMarketCodes = crudService.findByNamedQuery(AnalyticalMarketSegmentAudit.GET_DISTINCT_MAPPED_MARKET_CODES);
        final List<String> distinctMappedMarketCodes = crudService.findByNamedQuery(AnalyticalMarketSegment.GET_DISTINCT_MAPPED_MARKET_CODES);
        final List<String> distinctMarketCodes = crudService.findByNamedQuery(AnalyticalMarketSegmentAudit.GET_DISTINCT_MARKET_CODES);

        final Set<String> amsCodes = Stream.of(distinctAuditMappedMarketCodes, distinctMappedMarketCodes, distinctMarketCodes).flatMap(List::stream).collect(Collectors.toSet());
        final List<AnalyticalMarketSegment> analyticalMarketSegments = mktSegs.stream()
                .filter(not(m -> amsCodes.contains(m.getCode())))
                .filter(m -> m.getMktSegDetails() != null || m.getMktSegDetailsProposed() != null)
                .map(this::toAms).collect(Collectors.toList());
        if (analyticalMarketSegments.isEmpty()) {
            LOGGER.info("No new AMS entries to be created");
            return 0;
        }
        LOGGER.info(analyticalMarketSegments.size() + " new AMS entries to be added");
        final Collection<AnalyticalMarketSegment> persisted = saveAll(analyticalMarketSegments);
        return persisted.size();
    }

    public Collection<AnalyticalMarketSegment> saveAll(List<AnalyticalMarketSegment> analyticalMarketSegments) {
        return crudService.save(analyticalMarketSegments);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void saveAndDelete(Collection<AnalyticalMarketSegment> analyticalMarketSegments) {
        amsRepository.saveTransactional(analyticalMarketSegments);
        amsRepository.deleteTransactional(analyticalMarketSegments);
    }


    public void deleteAll(Collection<AnalyticalMarketSegment> analyticalMarketSegments) {
        crudService.delete(analyticalMarketSegments);
    }

    public AnalyticalMarketSegment toAms(MktSeg mktSeg) {
        LOGGER.info("Creating a straight AMS entry for MS : " + mktSeg.getCode());
        MktSegAttributes mktSegAttributes = Stream.of(mktSeg.getMktSegDetailsProposed(), mktSeg.getMktSegDetails()).filter(Objects::nonNull).findFirst().get();
        AnalyticalMarketSegmentAttribute amsAttribute = AnalyticalMarketSegmentAttribute.findFromMarketSegmentDetail(mktSegAttributes);
        AnalyticalMarketSegment ams = new AnalyticalMarketSegment();
        ams.setMarketCode(mktSeg.getCode());
        ams.setMappedMarketCode(mktSeg.getCode());
        // If this is a block attribute, set the booking block percent to block (100%)
        ams.setBlockPercent(amsAttribute.getBlock() ? BOOKING_BLOCK_PCT_IS_BLOCK : BOOKING_BLOCK_PCT_DEFAULT);
        ams.setForecastActivityType(mktSegAttributes.getForecastActivityType());
        ams.setRateCodeType(RateCodeTypeEnum.ALL);
        ams.setRank(RateCodeTypeEnum.ALL.getRank());
        return ams;
    }

    public void deleteProductRateCodesFor(String marketSegment, Product product) {
        if (isNull(product)) {
            return;
        }

        List<String> rateCodes = stream(marketSegmentRepository.getRateCodesForMarketSegments(Arrays.asList(marketSegment)))
                .map(RateCodeSummary::getRateCode)
                .collect(Collectors.toList());
        List<Product> allProducts = crudService.findByNamedQuery(Product.GET_ALL_EXCLUDING_EXTENDED_STAY_TYPE);
        List<Product> childProductsOfCurrentProduct = AgileRatesUtils.getChildren(product, allProducts);
        independentProductsRepository.removeRateCodesFromProduct(product, rateCodes, childProductsOfCurrentProduct);
    }

    public List<AnalyticalMarketSegmentAuditMapping> getAMSAuditReportWithIPOn() {
        return crudService.findByNamedQuery(AnalyticalMarketSegmentAuditMapping.FIND_ALL_AUDITS_WITH_PRODUCT);
    }

    public void saveNonAMS(MktSeg marketSegment,String productName,AnalyticalMarketSegmentAttribute attribute, Integer forecastTypeId) {
        ForecastActivityType forecastActivityType = getForecastActivityType(forecastTypeId);
        Product product = null;
        if (isIndependentProductEnabled() && StringUtils.isNotEmpty(productName)) {
            product = getProduct(productName);
        }
        if (marketSegment.getMktSegDetailsProposed() != null) {
            updateMarketSegmentDetailsProposed(product, marketSegment.getMktSegDetailsProposed(), forecastActivityType, attribute);
        } else {
            MktSegDetailsProposed mktSegDetailsProposed = new MktSegDetailsProposed();
            mktSegDetailsProposed.setTemplateId(0);
            updateMarketSegmentDetailsProposed(product, mktSegDetailsProposed, forecastActivityType, attribute);
            marketSegment.setMktSegDetailsProposed(mktSegDetailsProposed);
        }
        marketSegmentService.persistMktSegDetails(List.of(marketSegment));
        Map<String, MarketSegmentProductMapping> marketSegmentProductMappings = independentProductsService.getMarketSegmentProductMappings();
        if(product != null) {
            MarketSegmentProductMapping mapping = marketSegmentProductMappings.getOrDefault(
                    marketSegment.getCode(),
                    independentProductsService.createNewMapping(marketSegment.getCode(), product, AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR.equals(attribute))
            );
            mapping.setProduct(product);
            mapping.setIp(attribute == AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
            independentProductsService.saveMappings(singleton(mapping));
        } else {
            independentProductsService.deleteMappingForMarketSegment(marketSegment.getCode());
        }
    }

    private void updateMarketSegmentDetailsProposed(Product product, MktSegDetailsProposed mktSegDetailsProposed, ForecastActivityType forecastActivityType,
                                                    AnalyticalMarketSegmentAttribute attribute) {
        mktSegDetailsProposed.setForecastActivityType(forecastActivityType);
        mktSegDetailsProposed.setBusinessType(getBusinessType(attribute.getBusinessType()));
        mktSegDetailsProposed.setYieldType(getYieldType(attribute.getYieldType()));
        mktSegDetailsProposed.setQualified(attribute.getQualified() ? 1 : 0);
        mktSegDetailsProposed.setBookingBlockPc(attribute.getBlock() ? 100 : 0);
        mktSegDetailsProposed.setPriceByBar(getPriceByBar(attribute, product));
        mktSegDetailsProposed.setFenced(attribute.getFenced() ? 1 : 0);
        mktSegDetailsProposed.setPackageValue(attribute.getPackaged() ? 1 : 0);
        mktSegDetailsProposed.setLink(attribute.getLinkType().getId());
    }
    private BusinessType getBusinessType(AnalyticalMarketSegmentAttribute.MarketSegmentBusinessType marketSegmentBusinessType) {
        BusinessType businessType = new BusinessType();
        businessType.setId(marketSegmentBusinessType.getId());
        businessType.setName(marketSegmentBusinessType.getName());
        return businessType;

    }

    private YieldType getYieldType(AnalyticalMarketSegmentAttribute.MarketSegmentYieldType marketSegmentYieldType) {
        YieldType yieldType = new YieldType();
        yieldType.setId(marketSegmentYieldType.getId());
        yieldType.setName(marketSegmentYieldType.getName());
        return yieldType;
    }

    public void updateDataInAMSCompositionChange(Optional<Integer> roomsSold, Optional<BigDecimal> hotelPercent) {
        Integer defaultRoomsSold = 100;
        BigDecimal defaultHotelPercent = BigDecimal.valueOf(95);

        Integer finalRoomsSold = roomsSold.orElse(defaultRoomsSold);
        BigDecimal finalHotelPercent = hotelPercent.orElse(defaultHotelPercent);

        List<MarketSegmentIdPair> mktSegIdList = crudService.findByNamedQuery(AnalyticalMarketSegment.GET_SEGMENTS_ID);
        for(MarketSegmentIdPair marketSegmentIdPair: mktSegIdList) {
            createAMSCompositionChangeRecordWithDefaultRoomsSoldAndHotelPercent(marketSegmentIdPair.getAnalyticalMktSegId(), marketSegmentIdPair.getMktSegId(), finalRoomsSold, finalHotelPercent);
        }
    }
    public void createAMSCompositionChangeRecordWithDefaultRoomsSoldAndHotelPercent(Integer amsId, Integer mktSegId, Integer roomsSold, BigDecimal hotelPercent) {
        createAMSCompositionChangeRecord(amsId, mktSegId, roomsSold, hotelPercent, true);
    }

    private final class SummaryPercentComparator implements Comparator<AnalyticalMarketSegmentSummary> {
        @Override
        public int compare(AnalyticalMarketSegmentSummary o1, AnalyticalMarketSegmentSummary o2) {
            return o2.getHotelPercent().compareTo(o1.getHotelPercent());
        }
    }

    public List<AnalyticalMarketSegment> getAMSRulesWithMarketCodes(Collection<String> marketCodes) {
        if (marketCodes.isEmpty()) {
            return Collections.emptyList();
        }
        return crudService.findByNamedQuery(AnalyticalMarketSegment.BY_MARKET_CODES, QueryParameter.with(MARKET_CODES, marketCodes).parameters());
    }


    public boolean amsChangeApplyToHistoryPresent() {
        return isNotEmpty(marketSegmentService.retrieveModifiedHotelMarketSegmentCodes());
    }

    private void updateApplyHistoryMap(Map<Integer, Boolean> applyHistoryMap, AMSCompositionChange entity) {
        Integer mktSegId = entity.getMarketSegmentId();
        boolean status = entity.isApplyToHistory();
        Boolean lastStoreStatus = applyHistoryMap.get(mktSegId);
        if (lastStoreStatus == null || !lastStoreStatus) {
            applyHistoryMap.put(mktSegId, status);
        }
    }

    public boolean splitAmsRulesPresent() {
        return isNotEmpty(crudService.findByNamedQuery(AnalyticalMarketSegment.GET_NON_STRAIGHT_AMS_RULES));
    }

    public List<AnalyticalMarketSegment> getAllAMSWithForecastActivityType() {

        List<Object[]> rows = crudService.findByNamedQuery(AnalyticalMarketSegment.GET_ALL_AMS_WITH_FORECAST_ACTIVITY_TYPE);
        Map<String, ForecastActivityType> fcstActivityMap = getForecastActivityTypeMap();
        return rows.stream().map(row -> {
            AnalyticalMarketSegment ams = new AnalyticalMarketSegment();
            ams.setMarketCode((String) row[0]);
            ams.setRateCode((String) row[1]);
            ams.setMappedMarketCode((String) row[2]);
            ams.setAttribute(AnalyticalMarketSegmentAttribute.valueOf((String) row[3]));
            ams.setForecastActivityType(fcstActivityMap.get((String) row[4]));
            return ams;
        }).collect(Collectors.toList());
    }

    private Map<String, ForecastActivityType> getForecastActivityTypeMap() {
        Map<String, ForecastActivityType> fcstActivityMap = new HashMap<>();
        List<ForecastActivityType> forecastActivityTypes = crudService.findAll(ForecastActivityType.class);
        forecastActivityTypes.forEach(fcstActivity -> {
            fcstActivityMap.put(fcstActivity.getName(), fcstActivity);
        });
        return fcstActivityMap;
    }

    public int deleteAms() {
        return crudService.deleteAll(AnalyticalMarketSegment.class);
    }
    public int removeAllAmsCompositionChangeRecords() {
        return crudService.deleteAll(AMSCompositionChange.class);
    }

    public void splitAmsForIpp(Integer propertyId) {
        if (isPropertyVirtual(propertyId)) {
            splitAmsBasedOnIndependentProductsForVirtualProperty(propertyId);
        } else {
            splitAmsBasedOnIndependentProducts();
        }
    }

    public void assignTiersToIpp(Integer propertyId) {
        if (isPropertyVirtual(propertyId)) {
            getVirtualPropertyMappings(propertyId).forEach(this::updateTierMarketSegmentsForVP);
        } else {
            updateTierMarketSegments();
        }
    }

    public boolean isPropertyVirtual(Integer propertyId) {
        Property property = propertyService.getPropertyById(propertyId);
        return property.isVirtualProperty();
    }

    public void splitAmsBasedOnIndependentProducts() {
        List<Product> independentProducts = getIndependentProducts();
        List<String> onlyTransientMsToUpdate = getMktSegCodesToSplit();
        List<AnalyticalMarketSegment> amsToUpdate = onlyTransientMsToUpdate.isEmpty() ? new ArrayList<>() : crudService.findByNamedQuery(
                AnalyticalMarketSegment.BY_MAPPED_MARKET_CODES, QueryParameter.with(MAPPED_MARKET_CODES, onlyTransientMsToUpdate).parameters());
        executeAmsSplit(independentProducts, amsToUpdate);
    }

    private void executeAmsSplit(List<Product> independentProducts, List<AnalyticalMarketSegment> amsToUpdate) {
        if (!independentProducts.isEmpty() && !amsToUpdate.isEmpty()) {
            List<String> mktSegCodeOfStraightMS = new ArrayList<>();
            splitAmsBasedOnIpp(independentProducts, amsToUpdate, mktSegCodeOfStraightMS);
            if (!mktSegCodeOfStraightMS.isEmpty()) {
                List<Integer> mktSegIdOfStraightMS = crudService.findByNamedQuery(MktSeg.ID_BY_CODES,
                        with("marketCodes", mktSegCodeOfStraightMS).parameters());
                if (!mktSegIdOfStraightMS.isEmpty()) updateProductRateCodeForStraightAms(independentProducts, mktSegIdOfStraightMS);
            }
        } else {
            LOGGER.info("Skipping Split because either independent products list or AMS to update list was empty.");
        }
    }

    public void splitAmsBasedOnIndependentProductsForVirtualProperty(Integer virtualPropertyId) {
        List<String> propertiesEligibleForSplit = getESPropertyCodesFromVirtualProperty(virtualPropertyId);
        List<Product> allIndependentProducts = getIndependentProducts();
        List<String> onlyTransientMsToUpdate = getMktSegCodesToSplit();
        List<AnalyticalMarketSegment> amsList = onlyTransientMsToUpdate.isEmpty() ? new ArrayList<>() : crudService.findByNamedQuery(
                AnalyticalMarketSegment.BY_MAPPED_MARKET_CODES, QueryParameter.with(MAPPED_MARKET_CODES, onlyTransientMsToUpdate).parameters());
        propertiesEligibleForSplit.forEach(property -> executeAmsSplitForVirtualProperty(allIndependentProducts, amsList, property));
    }

    public List<String> getESPropertyCodesFromVirtualProperty(Integer virtualPropertyId) {
        List<VirtualPropertyMapping> virtualPropertyMappings = getVirtualPropertyMappings(virtualPropertyId);
        return virtualPropertyMappings.stream()
                .filter(VirtualPropertyMapping::isExtendedStay).map(VirtualPropertyMapping::getPhysicalPropertyCode)
                .collect(Collectors.toList());
    }

    private List<VirtualPropertyMapping> getVirtualPropertyMappings(Integer virtualPropertyId) {
        return virtualPropertyMappingService.getMappingsForVirtualProperty(virtualPropertyId);
    }

    private void executeAmsSplitForVirtualProperty(List<Product> allIndependentProducts, List<AnalyticalMarketSegment> amsList, String property) {
        List<AnalyticalMarketSegment> amsToUpdatePerProperty = new ArrayList<>(amsList);
        amsToUpdatePerProperty.removeIf(ams -> ams.getRateCode() != null && !doesAmsBelongsToProperty(property, ams) || isTierAMS(ams));
        List<Product> independentProducts = new ArrayList<>(allIndependentProducts);
        independentProducts.removeIf(product -> !product.getName().startsWith(property + "_"));
        if (!independentProducts.isEmpty() && !amsToUpdatePerProperty.isEmpty()) {
            List<String> mktSegCodeOfStraightMS = new ArrayList<>();
            splitAmsBasedOnIpp(independentProducts, amsToUpdatePerProperty, mktSegCodeOfStraightMS);
            if (!mktSegCodeOfStraightMS.isEmpty()) {
                List<Integer> mktSegIdOfStraightMS = crudService.findByNamedQuery(MktSeg.ID_BY_CODES,
                        with("marketCodes", mktSegCodeOfStraightMS).parameters());
                if (!mktSegIdOfStraightMS.isEmpty()) updateProductRateCodeForStraightAmsForVP(independentProducts, mktSegIdOfStraightMS, property);
            }
        } else {
            LOGGER.info("Skipping Split because either independent products list or AMS to update list was empty.");
        }
    }

    private static boolean isTierAMS(AnalyticalMarketSegment ams) {
        return ams.getMappedMarketCode().toUpperCase().contains("TIER");
    }

    private static boolean doesAmsBelongsToProperty(String property, AnalyticalMarketSegment ams) {
        return ams.getRateCode().startsWith(property + "_");
    }

    public List<Product> getIndependentProducts() {
        return crudService.findByNamedQuery(Product.GET_ACTIVE_INDEPENDENT_PRODUCTS);
    }

    public void checkForNewRateCodeForStraightAMS(List<String> parentAmsCodesList){
        List<Product> products = getIndependentProducts();
        List<AnalyticalMarketSegment> existingAms = crudService.findByNamedQuery(AnalyticalMarketSegment.BY_RANK_AND_MARKET_CODES,
                QueryParameter.with("rank",1).and(MARKET_CODES, parentAmsCodesList).parameters());
        List<String> straightMsCodes = existingAms.stream()
                .filter(ams -> ams.getAttribute() != AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE)
                .map(AnalyticalMarketSegment::getMarketCode)
                .collect(Collectors.toList());
        List<Integer> mktSegIdList = getMktSegIdFromMktCodes(straightMsCodes);
        updateProductRateCodeForStraightAms(products, mktSegIdList);
    }

    public void checkForNewRateCodeForStraightAMSForVP(List<String> parentAmsCodesList, List<String> propertyCodes){
        for(String propertyCode : propertyCodes) {
            List<Product> allIndependentProducts = getIndependentProducts();
            List<AnalyticalMarketSegment> existingAmsList = crudService.findByNamedQuery(AnalyticalMarketSegment.BY_RANK_AND_MARKET_CODES,
                    QueryParameter.with("rank",1).and(MARKET_CODES, parentAmsCodesList).parameters());

            List<AnalyticalMarketSegment> amsToUpdatePerProperty = new ArrayList<>(existingAmsList);
            List<Product> independentProducts = new ArrayList<>(allIndependentProducts);
            independentProducts.removeIf(product -> !product.getName().startsWith(propertyCode + "_"));
            List<String> straightAms = amsToUpdatePerProperty.stream()
                    .filter(ams -> ams.getAttribute() != AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE)
                    .map(AnalyticalMarketSegment::getMarketCode)
                    .collect(Collectors.toList());
            List<Integer> mktSegIdList = getMktSegIdFromMktCodes(straightAms);
            updateProductRateCodeForStraightAmsForVP(independentProducts, mktSegIdList, propertyCode);
        }
    }

    public void updateProductRateCodeForStraightAms(List<Product> independentProducts, List<Integer> mktSegIdOfStraightAndDefaultMS) {
        if(!mktSegIdOfStraightAndDefaultMS.isEmpty()) {
            Pattern pattern = Pattern.compile("LV\\d");
            List<String> rateCodes = fetchRateCodesForMktSegIdInFromReservationNight(mktSegIdOfStraightAndDefaultMS)
                    .stream().filter(rateCode -> !pattern.matcher(rateCode).matches())
                    .filter(rateCode -> independentProducts.stream()
                            .noneMatch(product -> rateCode.contains("_" + product.getName())))
                    .collect(Collectors.toList());
            splitAndSaveProductRateCodes(independentProducts, rateCodes);
        }
    }

    public void updateProductRateCodeForStraightAmsForVP(List<Product> independentProducts, List<Integer> mktSegIdOfStraightAndDefaultMS, String propertyCode) {
        Pattern pattern = Pattern.compile(".*_LV\\d");
        List<String> rateCodes = fetchRateCodesForMktSegIdInFromReservationNightForVP(mktSegIdOfStraightAndDefaultMS, propertyCode)
                .stream().filter(rateCode -> !pattern.matcher(rateCode).matches())
                .filter(rateCode -> independentProducts.stream()
                        .noneMatch(product -> rateCode.contains("_" + product.getName())))
                .collect(Collectors.toList());
        splitAndSaveProductRateCodes(independentProducts, rateCodes);
    }

    private void splitAndSaveProductRateCodes(List<Product> independentProducts, List<String> rateCodes) {
        List<ProductRateCode> productRateCodeList = new ArrayList<>();
        for (Product product : independentProducts) {
            for (String rateCode : rateCodes) {
                createProductRateCode(productRateCodeList, product, formatRateCodeForIpp(rateCode, product.getName()));
            }
        }
        saveProductRateCodeListIfNotExist(productRateCodeList);
    }

    public void splitAmsBasedOnIpp(List<Product> independentProducts, List<AnalyticalMarketSegment> analyticalMktSegsList, List<String> mktSegCodeOfStraightMS){
        Map<String, BusinessGroup> originalMktCodeToBusinessGroupMapping = getBusinessGroupMktSegMap();
        Map<BusinessGroup, Set<MktSeg>> businessGroupToMktSegMap = new HashMap<>();
        List<MarketSegmentProductMapping> mktSegProductMappingList = new ArrayList<>();
        List<ProductRateCode> productRateCodeList = new ArrayList<>();
        final Map<String, MktSeg> mktSegMap = getMktSegByCode();
        for (Product product : independentProducts) {
            for (AnalyticalMarketSegment ams : analyticalMktSegsList) {
                if(!isAttributeNullOrQualifiedNonBlockNotLinkedYieldable(ams) && !isBarOrSpecialESMarketCode(ams)) {
                    createParentMktSegIfNotExists(mktSegMap, ams);
                    BusinessGroup businessGroup = originalMktCodeToBusinessGroupMapping.get(ams.getMappedMarketCode());
                    ams.setForecastActivityType(ForecastActivityType.DEMAND_AND_WASH_FAT);
                    AnalyticalMarketSegmentSummary amsSummary = createAmsSummary(product, ams);
                    AnalyticalMarketSegment newAms = createAnalyticalMarketSegment(amsSummary, ams.getAttribute(), ams.getForecastActivityType(), false);
                    updateMarketSegmentMaster(newAms.getMappedMarketCode(), newAms, ams.getAttribute(), EDITABLE, product);
                    MktSeg mktSeg = updateMktSeg(newAms.getMappedMarketCode(), newAms, true);
                    createMarketSegmentProductMapping(mktSegProductMappingList, product, ams);
                    createMktSegBusinessGroup(businessGroupToMktSegMap, businessGroup, mktSeg);
                    if(ams.getRateCode() != null) {
                        createProductRateCode(productRateCodeList, product, formatRateCodeForIpp(ams.getRateCode(), product.getName()));
                    } else if (ams.getRateCodeType() == RateCodeTypeEnum.ALL) {
                        mktSegCodeOfStraightMS.add(ams.getMarketCode());
                    }
                    updateMktSegDetailsProposed(newAms, newAms.getAttribute(), mktSeg, product);
                }
            }
        }
        saveMktSegProductMappingListIfNotExists(mktSegProductMappingList);
        saveProductRateCodeListIfNotExist(productRateCodeList);
        saveMktSegBusinessGroups(businessGroupToMktSegMap);
    }

    void createParentMktSegIfNotExists(Map<String, MktSeg> mktSegMap, AnalyticalMarketSegment ams) {
        if(Objects.isNull(mktSegMap.get(ams.getMappedMarketCode()))){
            updateMktSeg(ams.getMappedMarketCode(), ams, true);
        }
    }

    void saveMktSegBusinessGroups(Map<BusinessGroup, Set<MktSeg>> businessGroupToMktSegMap) {
        List<BusinessGroup> businessGroupsToSave = new ArrayList<>();
        businessGroupToMktSegMap.forEach((businessGroup, mktSegsInMap) -> {
                    mktSegsInMap.forEach(mktSeg -> {
                       MktSegBusinessGroup msbg = new MktSegBusinessGroup();
                       msbg.setMktSeg(mktSeg);
                       msbg.setRanking(1);
                        Optional.ofNullable(businessGroup.getMktSegBusinessGroups())
                                .orElseGet(() -> {
                                    businessGroup.setMktSegBusinessGroups(new HashSet<>());
                                    return businessGroup.getMktSegBusinessGroups();
                                })
                                .add(msbg);
                    });
                    businessGroupsToSave.add(businessGroup);
                });
        businessGroupService.save(businessGroupsToSave);
    }

    void createMktSegBusinessGroup(Map<BusinessGroup, Set<MktSeg>> mktSegBusinessGroupsMap, BusinessGroup businessGroup, MktSeg mktSeg) {
        if(businessGroup != null && mktSeg != null) {
            mktSegBusinessGroupsMap.computeIfAbsent(businessGroup, k -> new HashSet<>()).add(mktSeg);
        }
    }

    Map<String, BusinessGroup> getBusinessGroupMktSegMap() {
        List<MktSegBusinessGroup> mktSegBusinessGroups = crudService.findAll(MktSegBusinessGroup.class);
        return mktSegBusinessGroups.stream()
                .collect(Collectors.toMap(mktSegBusinessGroup -> mktSegBusinessGroup.getMktSeg().getCode(), MktSegBusinessGroup::getBusinessGroup));
    }

    public void updateSplitAmsForIpp(List<Product> independentProducts, List<AnalyticalMarketSegment> analyticalMktSegsListToUpdate, List<String> codesEligibleForSplit, List<AnalyticalMarketSegment> existingAmsList){
        List<ProductRateCode> productRateCodeList = new ArrayList<>();
        Map<String, AnalyticalMarketSegment> existingAmsMap = createUniqueKeyMappingForExistingAms(existingAmsList);
        for (AnalyticalMarketSegment ams : analyticalMktSegsListToUpdate) {
            if (codesEligibleForSplit.contains(ams.getMappedMarketCode()) && (ams.getAttribute() != AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE)) {
                updateSplitAms(independentProducts, productRateCodeList, existingAmsMap, ams);
            }
        }
        List<AnalyticalMarketSegment> amsToBeSaved = new ArrayList<>(existingAmsMap.values());
        crudService.save(amsToBeSaved);
        saveProductRateCodeListIfNotExist(productRateCodeList);
    }

    public void updateSplitAmsForIppForVP(List<Product> allIndependentProducts, List<AnalyticalMarketSegment> analyticalMktSegsListToUpdate, List<String> parentAmsMktCodes, List<AnalyticalMarketSegment> existingAmsList, List<String> propertyCodes){
        for(String propertyCode : propertyCodes) {
            List<AnalyticalMarketSegment> amsToUpdatePerProperty = new ArrayList<>(analyticalMktSegsListToUpdate);
            amsToUpdatePerProperty.removeIf(ams -> !(ams.getRateCode() == null) && !ams.getRateCode().startsWith(propertyCode + "_"));
            List<Product> independentProducts = new ArrayList<>(allIndependentProducts);
            independentProducts.removeIf(product -> !product.getName().startsWith(propertyCode + "_"));

            List<ProductRateCode> productRateCodeList = new ArrayList<>();
            Map<String, AnalyticalMarketSegment> existingAmsMap = createUniqueKeyMappingForExistingAms(existingAmsList);
            for (AnalyticalMarketSegment ams : amsToUpdatePerProperty) {
                if (parentAmsMktCodes.contains(ams.getMappedMarketCode())) {
                    updateSplitAms(independentProducts, productRateCodeList, existingAmsMap, ams);
                }
            }
            List<AnalyticalMarketSegment> amsToBeSaved = new ArrayList<>(existingAmsMap.values());
            crudService.save(amsToBeSaved);
            saveProductRateCodeListIfNotExist(productRateCodeList);
        }
    }

    private Map<String, AnalyticalMarketSegment> createUniqueKeyMappingForExistingAms(List<AnalyticalMarketSegment> existingAmsList) {
        return existingAmsList.stream()
                .collect(Collectors.toMap(ams -> {
                    String key;
                    if (ams.getRateCodeType() == RateCodeTypeEnum.DEFAULT) {
                        key = ams.getMappedMarketCode();
                    } else {
                        key = ams.getMarketCode() + (ams.getRateCodeType() == RateCodeTypeEnum.ALL ? "" : ams.getRateCode());
                    }
                    return key;
                }, existingAmsRule -> existingAmsRule));
    }

    private void updateSplitAms(List<Product> independentProducts, List<ProductRateCode> productRateCodeList, Map<String, AnalyticalMarketSegment> existingAmsMap, AnalyticalMarketSegment ams) {
        for (Product product : independentProducts) {
            AnalyticalMarketSegment currentSplitAms;
            String key;
            if(ams.getRateCodeType() == RateCodeTypeEnum.DEFAULT){

                int index = ams.getMappedMarketCode().indexOf('_');
                if (index != -1) {
                    key = ams.getMappedMarketCode().substring(0, index) + "_" + product.getName() + ams.getMappedMarketCode().substring(index);
                } else {
                    key = ams.getMappedMarketCode() + "_" + product.getName();
                }
            } else {
                key = ams.getMarketCode() + (ams.getRateCodeType() == RateCodeTypeEnum.ALL ? "_" : ams.getRateCode() + "_") + product.getName();
            }
            currentSplitAms = existingAmsMap.get(key);
            if (currentSplitAms == null) {
                createNewIppAmsSplits(productRateCodeList, existingAmsMap, ams, product);
            } else {
                updateExistingSplitAms(ams, currentSplitAms);
            }
        }
    }

    private void createNewIppAmsSplits(List<ProductRateCode> productRateCodeList, Map<String, AnalyticalMarketSegment> existingAmsMap, AnalyticalMarketSegment ams, Product product) {
        ams.setForecastActivityType(ForecastActivityType.DEMAND_AND_WASH_FAT);
        AnalyticalMarketSegmentSummary amsSummary = createAmsSummary(product, ams);
        AnalyticalMarketSegment newAms = createAnalyticalMarketSegment(amsSummary, ams.getAttribute(), ams.getForecastActivityType(), false);
        if (newAms.getRateCodeType() == RateCodeTypeEnum.DEFAULT) {
            existingAmsMap.put(newAms.getMappedMarketCode(), newAms);
        } else {
            existingAmsMap.put(newAms.getMarketCode() + (newAms.getRateCodeType() == RateCodeTypeEnum.ALL ? "" : newAms.getRateCode() + "_" + product.getName()), newAms);
        }
        if (ams.getRateCode() != null) {
            createProductRateCode(productRateCodeList, product, formatRateCodeForIpp(ams.getRateCode(), product.getName()));
        }
    }

    private void updateExistingSplitAms(AnalyticalMarketSegment ams, AnalyticalMarketSegment currentSplitAms) {
        currentSplitAms.setAttribute(ams.getAttribute());
        if (currentSplitAms.getRateCodeType() == RateCodeTypeEnum.EQUALS) {
            String currentSplitAmsCode = currentSplitAms.getMappedMarketCode();
            String amsCode = ams.getMappedMarketCode();
            int lastUnderscoreIndexCurrent = currentSplitAmsCode.lastIndexOf('_');
            int lastUnderscoreIndexAms = amsCode.lastIndexOf('_');
            String newSuffix = amsCode.substring(lastUnderscoreIndexAms + 1);
            if (lastUnderscoreIndexCurrent != -1) {
                currentSplitAms.setMappedMarketCode(currentSplitAmsCode.substring(0, lastUnderscoreIndexCurrent + 1) + newSuffix);
            }
        }
    }

    void updateTierMarketSegments() {
        Map<String, String> tierMktSegProductMap = getTierMktSegProductMap();
        tierMktSegProductMap.forEach((mktCode, productName) -> {
            Optional<AnalyticalMarketSegment> analyticalMarketSegment = Optional.ofNullable(crudService.findByNamedQuerySingleResult(AnalyticalMarketSegment.BY_MAPPED_MARKET_CODE,
                    QueryParameter.with(MAPPED_MARKET_CODE, mktCode).parameters()));
            if (analyticalMarketSegment.isPresent()) {
                AnalyticalMarketSegment ams = analyticalMarketSegment.get();
                ams.setAttribute(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
                crudService.save(ams);
                setForecastActivityTypeForAms(mktCode, ams);
                Optional<Product> product = Optional.ofNullable(crudService.findByNamedQuerySingleResult(Product.GET_BY_NAME,
                        QueryParameter.with("name", productName).parameters()));

                if (product.isPresent()) {
                    MktSeg mktSeg = updateMktSeg(ams.getMappedMarketCode(), ams, true);
                    updateMktSegDetailsProposed(ams, ams.getAttribute(), mktSeg, product.get());
                    upsertMktSegProductMappingForSpecialESMktSeg(product.get(), ams);
                }
            }
        });
    }

    public void updateTierMarketSegmentsForVP(VirtualPropertyMapping vpm) {
        String propertyCode = vpm.getPhysicalPropertyCode();
        Map<String, String> tierMktSegProductMap = getTierMktSegProductMapForVP(vpm);
        tierMktSegProductMap.forEach((mktCode, productName) -> {
            Optional<AnalyticalMarketSegment> analyticalMarketSegment = Optional.ofNullable(crudService.findByNamedQuerySingleResult(AnalyticalMarketSegment.BY_MAPPED_MARKET_CODE,
                    QueryParameter.with(MAPPED_MARKET_CODE, mktCode).parameters()));
            if (analyticalMarketSegment.isPresent()) {
                AnalyticalMarketSegment originalAms = analyticalMarketSegment.get();
                AnalyticalMarketSegment newAms = new AnalyticalMarketSegment(originalAms);
                newAms.setAttribute(AnalyticalMarketSegmentAttribute.EQUAL_TO_BAR);
                newAms.setMarketCode(propertyCode + "_" + originalAms.getMarketCode());
                newAms.setMappedMarketCode(propertyCode + "_" + originalAms.getMappedMarketCode());
                crudService.save(newAms);
                setForecastActivityTypeForAms(mktCode, newAms);
                Optional<Product> product = Optional.ofNullable(crudService.findByNamedQuerySingleResult(Product.GET_BY_NAME,
                        QueryParameter.with("name", propertyCode + "_" + productName).parameters()));
                if (product.isPresent()) {
                    MktSeg mktSeg = updateMktSeg(newAms.getMappedMarketCode(), newAms, true);
                    updateMktSegDetailsProposed(newAms, newAms.getAttribute(), mktSeg, product.get());
                    upsertMktSegProductMappingForSpecialESMktSeg(product.get(), newAms);
                    updateMarketSegmentMaster(newAms.getMappedMarketCode(), newAms, newAms.getAttribute(), EDITABLE, product.get());
                }
            }
        });
    }

    void setForecastActivityTypeForAms(String mktCode, AnalyticalMarketSegment ams) {
        MktSegDetails mktSegDetail = crudService.findByNamedQuerySingleResult(MktSegDetails.GET_MARKET_SEGMENTS_BY_NAME, QueryParameter.with("mktSegName", mktCode).parameters());
        if(mktSegDetail != null) {
            ams.setForecastActivityType(mktSegDetail.getForecastActivityType());
        } else {
            ams.setForecastActivityType(ForecastActivityType.DEMAND_AND_WASH_FAT);
        }
    }

    public Map<String, String> getTierMktSegProductMap() {
        if (isOperaPropertyInHilton()) {
            return Map.of(TIER1, "LV2",
                    TIER2, "LV3",
                    TIER3, "LV4");
        } else {
            return Map.of(TIER1, "LV1",
                    TIER2, "LV2",
                    TIER3, "LV3");
        }
    }

    public Map<String, String> getTierMktSegProductMapForVP(VirtualPropertyMapping vpm) {
        if (vpm.isOperaConfiguration()) {
            return Map.of(TIER1, "LV2",
                    TIER2, "LV3",
                    TIER3, "LV4");
        } else {
            return Map.of(TIER1, "LV1",
                    TIER2, "LV2",
                    TIER3, "LV3");
        }
    }

    private boolean isOperaPropertyInHilton() {
        return configParamsServiceLocal.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_OPERA_PROPERTY_IN_HILTON.value(), PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode());
    }

    public void upsertMktSegProductMappingForSpecialESMktSeg(Product product, AnalyticalMarketSegment ams) {
        MarketSegmentProductMapping mktSegProductMapping = crudService.findByNamedQuerySingleResult(MarketSegmentProductMapping.BY_MS, QueryParameter.with("marketSegmentCode", ams.getMappedMarketCode()).parameters());
        if (mktSegProductMapping == null) {
            MarketSegmentProductMapping newMktSegProductMapping = new MarketSegmentProductMapping();
            newMktSegProductMapping.setProduct(product);
            newMktSegProductMapping.setMarketSegmentCode(ams.getMappedMarketCode());
            newMktSegProductMapping.setIp(true);
            crudService.save(newMktSegProductMapping);
        } else {
            mktSegProductMapping.setProduct(product);
            mktSegProductMapping.setIp(true);
            crudService.save(mktSegProductMapping);
        }
    }

    public void saveMktSegProductMappingListIfNotExists(List<MarketSegmentProductMapping> mktSegProductMappingList) {
        List<String> existingMktSegProductMapping = crudService.findByNamedQuery(MarketSegmentProductMapping.GET_DISTINCT_MARKET_CODES);
        List<MarketSegmentProductMapping> distinctMktSegProductMappingList = new ArrayList<>(mktSegProductMappingList.stream()
                .collect(Collectors.toMap(MarketSegmentProductMapping::getMarketSegmentCode,
                        Function.identity(),
                        (existing, replacement) -> existing))
                .values());
        List<MarketSegmentProductMapping> mktSegProductMappingToBeSaved = new ArrayList<>(distinctMktSegProductMappingList.stream()
                .filter(mktSegProductMapping -> !existingMktSegProductMapping.contains(mktSegProductMapping.getMarketSegmentCode())).collect(Collectors.toList()));
        crudService.save(mktSegProductMappingToBeSaved);
    }

    public void saveProductRateCodeListIfNotExist(List<ProductRateCode> productRateCodeList) {
        List<String> existingProductRateCodes = crudService.findByNamedQuery(ProductRateCode.BY_DISTINCT_RATE_CODES);
        List<ProductRateCode> distinctproductRateCodeList = new ArrayList<>(productRateCodeList.stream()
                .collect(Collectors.toMap(ProductRateCode::getRateCode,
                        Function.identity(),
                        (existing, replacement) -> existing))
                .values());
        List<ProductRateCode> productRateCodeToBeSaved = new ArrayList<>(distinctproductRateCodeList.stream()
                .filter(productRateCode -> !existingProductRateCodes.contains(productRateCode.getRateCode())).collect(Collectors.toList()));
        crudService.save(productRateCodeToBeSaved);
    }

    public void createProductRateCode(List<ProductRateCode> productRateCodeList, Product product, String rateCode) {
        ProductRateCode productRateCode = new ProductRateCode();
        productRateCode.setProduct(product);
        productRateCode.setRateCode(rateCode);
        productRateCodeList.add(productRateCode);
    }

    public void createMarketSegmentProductMapping(List<MarketSegmentProductMapping> mktSegProductMappingList, Product product, AnalyticalMarketSegment ams) {
        MarketSegmentProductMapping marketSegmentProductMapping = new MarketSegmentProductMapping();
        marketSegmentProductMapping.setMarketSegmentCode(formatMappedCodeForAmsSplit(ams, product));
        marketSegmentProductMapping.setProduct(product);
        mktSegProductMappingList.add(marketSegmentProductMapping);
    }

    public AnalyticalMarketSegmentSummary createAmsSummary(Product product, AnalyticalMarketSegment ams) {
        AnalyticalMarketSegmentSummary amsSummary = new AnalyticalMarketSegmentSummary();
        if (ams.getRateCodeType() == RateCodeTypeEnum.ALL) {
            amsSummary.setMarketCode(formatMappedCodeForAmsSplit(ams, product));
        } else {
            amsSummary.setMarketCode(ams.getMarketCode());
        }
        amsSummary.setMappedCode(formatMappedCodeForAmsSplit(ams, product));
        amsSummary.setRateCode(formatRateCodeForIpp(ams.getRateCode(), product.getName()));
        amsSummary.setRateCodeType(ams.getRateCodeType().name());
        return amsSummary;
    }

    private static boolean isBarOrSpecialESMarketCode(AnalyticalMarketSegment ams) {
        String code = ams.getMappedMarketCode();
        return SPECIAL_IPP_MARKET_CODES.contains(code);
    }

    private static boolean isAttributeNullOrQualifiedNonBlockNotLinkedYieldable(AnalyticalMarketSegment ams) {
        return ams.getAttribute() == AnalyticalMarketSegmentAttribute.QUALIFIED_NONBLOCK_NOTLINKED_YIELDABLE || ams.getAttribute() == null;
    }

    List<String> getMktSegCodesToSplit() {
        return Stream.concat(
                        getMktSegCodesToSplitFromMktSegDetails(),
                        getMktSegCodesToSplitFromMktSegMaster()
        ).distinct()
        .collect(Collectors.toList());
    }

    private Stream<String> getMktSegCodesToSplitFromMktSegDetails() {
        return crudService.<MktSegDetails>findByNamedQuery(MktSegDetails.GET_ALL).stream()
                .filter(msd -> !(msd.getBusinessType() == BusinessType.GROUP
                        || Objects.equals(msd.getBookingBlockPc(), BOOKING_BLOCK_PCT_IS_BLOCK))
                        && Objects.equals(msd.getForecastActivityType(), ForecastActivityType.DEMAND_AND_WASH_FAT))
                .map(msd -> msd.getMktSeg().getCode());
    }

    private Stream<String> getMktSegCodesToSplitFromMktSegMaster() {
        return crudService.<MarketSegmentMaster>findByNamedQuery(MarketSegmentMaster.FIND_ALL).stream()
                .filter(msm -> !(msm.getBusinessType() == BusinessType.GROUP
                        || Objects.equals(msm.getBookingBlockPc(), BOOKING_BLOCK_PCT_IS_BLOCK))
                        && Objects.equals(msm.getForecastActivityType(), ForecastActivityType.DEMAND_AND_WASH_FAT))
                .map(MarketSegmentMaster::getCode);
    }

    public String formatRateCodeForIpp(String rateCode, String productName) {
        return (rateCode == null || rateCode.isEmpty()) ? null : rateCode + "_" + productName;
    }

    public String formatMappedCodeForAmsSplit(AnalyticalMarketSegment ams, Product product) {
        int index = ams.getMappedMarketCode().indexOf('_');
        if (index != -1) {
            return ams.getMappedMarketCode().substring(0, index) + "_" + product.getName() + ams.getMappedMarketCode().substring(index);
        } else {
            return ams.getMappedMarketCode() + "_" + product.getName();
        }
    }

    public boolean isIndependentProductEnabled() {
        return configParamsServiceLocal.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
    }

    public List<String> fetchRateCodesForMktSegIdInFromReservationNight(List<Integer> marketSegIds) {
        return crudService.findByNamedQuery(ReservationNight.GET_RATE_CODES_FOR_MKT_SEG_IN,
                QueryParameter.with("marketSegIds", marketSegIds).parameters());
    }

    public List<Pair<String, String>> getDistinctRateCodesFromReservationsForMktSegCode(Set<String> marketCodes) {
        List<Object[]> results = crudService.findByNamedQuery(ReservationNight.GET_DISTINCT_RATE_CODES_BY_MKT_SEG_CODE,
                with("marketCodes", marketCodes).parameters());
        if (isNotEmpty(results)) {
            return results.stream().map(row -> new Pair<>((String) row[1], (String) row[0])).collect(Collectors.toList());
        }
        return emptyList();
    }
    public List<String> fetchRateCodesForMktSegIdInFromReservationNightForVP(List<Integer> marketSegIds, String propertyCode) {
        return crudService.findByNamedQuery(ReservationNight.GET_RATE_CODES_FOR_MKT_SEG_IN_WERE_RATE_CODE_LIKE,
                QueryParameter.with("marketSegIds", marketSegIds).and("code", propertyCode).parameters());
    }

    public Map<String, Integer> mappedMarketCodesToAmsIdWithoutCodes(final List<String> excludeMarketCodes) {
        List<Object[]> amsResultSet = crudService.findByNamedQuery(AnalyticalMarketSegment.GET_NON_GROUP_STRAIGHT_AMS_WITHOUT_CODES,
                QueryParameter.with(MARKET_CODES, excludeMarketCodes).parameters());
        return amsResultSet.stream().collect(toMap(row -> row[1].toString(), row -> (Integer) row[0], (id1, id2) -> id2));
    }

    public boolean shouldProcessForIpp() {
        return isHiltonIppEnabled() && !isLDBProperty();
    }

    public boolean isHiltonIppEnabled() {
        return configParamsServiceLocal.getBooleanParameterValue(PreProductionConfigParamName.IS_HILTON_IPP_ENABLED);
    }

    public boolean isLDBProperty(){
        return configParamsServiceLocal.getBooleanParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED);
    }

    public List<Integer> getMktSegIdFromMktCodes(List<String> mktCodes) {
        if (!mktCodes.isEmpty()) {
            return crudService.findByNamedQuery(MktSeg.ID_BY_CODES,
                    with(MARKET_CODES, mktCodes).parameters());
        }
        return new ArrayList<>();
    }

    public List<AnalyticalMarketSegment> getAmsByMarketCodes(Set<String> marketCodes) {
        if (CollectionUtils.isNotEmpty(marketCodes)) {
            return crudService.findByNamedQuery(AnalyticalMarketSegment.GET_AMS_BY_MARKET_CODES,
                    QueryParameter.with(MARKET_CODES, marketCodes).and("preserved", false).parameters());
        }

        return new ArrayList<>();
    }
    public void addSlitMSIdToComposition(Set<String> marketCodes){
        List<MarketSegmentIdPair> mktSegIdList = crudService.findByNamedQuery(AnalyticalMarketSegment.GET_AMS_AND_MKT_SEG_ID_BY_MARKET_CODES,
                QueryParameter.with("marketCodes", marketCodes).parameters());
        for(MarketSegmentIdPair marketSegmentIdPair: mktSegIdList) {
            createAMSCompositionChangeRecordWithDefaultRoomsSoldAndHotelPercent(marketSegmentIdPair.getAnalyticalMktSegId(),
                    marketSegmentIdPair.getMktSegId(), 0, BigDecimal.valueOf(95));
        }
    }
}
