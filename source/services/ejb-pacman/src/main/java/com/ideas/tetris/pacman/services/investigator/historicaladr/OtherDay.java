package com.ideas.tetris.pacman.services.investigator.historicaladr;

import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEventInstance;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDate;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.ideas.tetris.pacman.services.investigator.historicaladr.HistoricalAdrCalculator.getBasicQuery;

public class OtherDay implements DayPosition {

    private static final String LAST_INSTANCE_DATE = "lastInstanceDate";
    private static final String COLON_LAST_INSTANCE_DATE = ":lastInstanceDate";

    @Override
    public String buildQueryWithParameters(PropertySpecialEventInstance lastPastInstance, QueryParameter parameter) {
        List<Date> daysConsiderForCalculation = extractDaysConsiderForAdrCalculation(lastPastInstance);
        int i = 0;
        StringBuilder query = new StringBuilder()
                .append("select SUM(Rate) as Rate, SUM(RoomSolds) as RoomSold, SUM(ADR) / :roomSolds as ADR ")
                .append("from ")
                .append("( ");
        for (Date date : daysConsiderForCalculation) {
            parameter.and(LAST_INSTANCE_DATE + i, date);
            query.append(String.format(getBasicQuery(), COLON_LAST_INSTANCE_DATE + i, COLON_LAST_INSTANCE_DATE + i,
                    COLON_LAST_INSTANCE_DATE + i, COLON_LAST_INSTANCE_DATE + i));
            query.append(" UNION ALL ");
            i++;
        }
        parameter.and("roomSolds", i);
        StringBuilder removedUnion = new StringBuilder(removeUnionFromString(query.toString()));
        removedUnion.append(") as t");
        return removedUnion.toString();
    }

    private List<Date> extractDaysConsiderForAdrCalculation(PropertySpecialEventInstance lastPastInstance) {
        LocalDate startDate = LocalDate.fromDateFields(lastPastInstance.getStartDate());
        LocalDate endDate = LocalDate.fromDateFields(lastPastInstance.getEndDate());
        if (startDate.compareTo(endDate) == 0) {
            List<Date> daysConsiderForCalculation = new ArrayList<>();
            daysConsiderForCalculation.add(startDate.toDate());
            return daysConsiderForCalculation;
        }
        return getDaysBetweenStartAndEndDate(startDate, endDate);
    }

    private List<Date> getDaysBetweenStartAndEndDate(LocalDate startDate, LocalDate endDate) {
        List<Date> daysConsiderForCalculation = new ArrayList<>();
        startDate = startDate.plusDays(1);
        while (startDate.compareTo(endDate) < 0) {
            daysConsiderForCalculation.add(startDate.toDate());
            startDate = startDate.plusDays(1);
        }
        return daysConsiderForCalculation;
    }

    private String removeUnionFromString(String query) {
        int unionIndex = query.lastIndexOf("UNION");
        return -1 != unionIndex ? query.substring(0, unionIndex) : StringUtils.EMPTY;
    }
}
