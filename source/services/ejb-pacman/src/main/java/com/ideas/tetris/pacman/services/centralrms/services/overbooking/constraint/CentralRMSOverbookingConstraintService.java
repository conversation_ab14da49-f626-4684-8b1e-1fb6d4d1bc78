package com.ideas.tetris.pacman.services.centralrms.services.overbooking.constraint;

import com.ideas.tetris.pacman.services.centralrms.models.overbooking.GracefulUnconstrainedOvrbkAccomDetails;
import com.ideas.tetris.pacman.services.centralrms.models.overbooking.UnconstrainedOverbookingConfig;
import com.ideas.tetris.pacman.services.centralrms.models.overbooking.UnconstrainedOverbookingOverrideConfig;
import com.ideas.tetris.pacman.services.centralrms.models.overbooking.UnconstrainedOverbookingSeasonConfig;
import com.ideas.tetris.pacman.services.centralrms.models.util.Tuple2;
import com.ideas.tetris.pacman.services.overbooking.dto.OverbookingPropertyLevelConfigDetails;
import com.ideas.tetris.pacman.services.overbooking.entity.DecisionOvrbkAccomOVR;
import com.ideas.tetris.pacman.services.overbooking.entity.DecisionOvrbkPropertyOVR;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Set;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@Component
@Transactional
public class CentralRMSOverbookingConstraintService {

    @Autowired
	private CentralRMSOverbookingPropertyLevelUnconstrainer propertyLevelUnconstrainer;

    @Autowired
	private CentralRMSOverbookingAccomLevelUnconstrainer accomLevelUnconstrainer;

    @Autowired
	private OverbookingPropertyLevelOverrideUnconstrainer propertyLevelOverrideUnconstrainer;

    @Autowired
	private OverbookingAccomLevelOverrideUnconstrainer accomLevelOverrideUnconstrainer;

    /**
     * Unconstrains any overbooking limits at the room-type level for default, ceiling default,
     * and/or season configurations
     *
     * @param unconstrainedDefaultConfigs
     * @param unconstrainedCeilingDefaultConfigs
     * @param unconstrainedSeasonConfigs
     * @return A graceful attempt to unconstrain overbooking. If attempting to unconstrain a season
     * that no longer exists for a given room type, then instead of throwing an exception, the
     * API will put the configuration into {@link GracefulUnconstrainedOvrbkAccomDetails#getInvalidUnconstrainedSeasonConfigs()}
     */
    public GracefulUnconstrainedOvrbkAccomDetails getUnconstrainedRoomTypeLevelConfigDetails(Set<UnconstrainedOverbookingConfig> unconstrainedDefaultConfigs,
                                                                                             Set<UnconstrainedOverbookingConfig> unconstrainedCeilingDefaultConfigs,
                                                                                             Set<UnconstrainedOverbookingSeasonConfig> unconstrainedSeasonConfigs) {

        return accomLevelUnconstrainer.getUnconstrainedConfigDetails(unconstrainedDefaultConfigs, unconstrainedCeilingDefaultConfigs, unconstrainedSeasonConfigs);
    }

    /**
     * Unconstrains any overbooking limits at the room-type level for Ceiling Default configurations.
     * There is some degree of "skip logic" taking place here. For example, say you want to remove
     * the ceiling for Monday, but there's already no ceiling for Monday. Then we'd "skip" configuring
     * Monday (even though the net effect would have been the same had we remove the ceiling anyways).
     *
     * @param unconstrainedCeilingDefaultConfig T1 := The Overbooking property level configuration describing the DOWs which were already
     *                                          unconstrained (if applicable). For example, say we're trying to remove the ceiling for
     *                                          Monday, but Monday is already set to "No Ceiling", then we'd return the configuration
     *                                          with those DOWs. If no DOWs were skipped, then T1 is null
     *                                          T2 := The unconstrained overbooking property level configuration
     */
    public Tuple2<UnconstrainedOverbookingConfig, OverbookingPropertyLevelConfigDetails> getUnconstrainedPropertyLevelConfigDetails(UnconstrainedOverbookingConfig unconstrainedCeilingDefaultConfig) {
        Assert.isNull(unconstrainedCeilingDefaultConfig.getRoomType(), "Room type must be null");
        return propertyLevelUnconstrainer.getUnconstrainedConfigDetails(unconstrainedCeilingDefaultConfig);
    }

    /**
     * Unconstrains any overbooking overrides at the property level for override configurations
     *
     * @param configs
     * @return A tuple where
     * T1 := Are the configs which were skipped due to extraneous edge cases, e.g., the override
     * no longer exists or is causing a problem
     * T2 := Are the unconstrained overbooking overrides
     */
    public Tuple2<List<UnconstrainedOverbookingOverrideConfig>, List<DecisionOvrbkPropertyOVR>> getUnconstrainedPropertyOverrides(List<UnconstrainedOverbookingOverrideConfig> configs) {
        return propertyLevelOverrideUnconstrainer.getUnconstrainedOverbookingOverrides(configs);
    }

    /**
     * Unconstrains any overbooking overrides at the property level for override configurations
     *
     * @param configs
     * @return A tuple where
     * T1 := Are the configs which were skipped due to extraneous edge cases, e.g., the override
     * no longer exists or is causing a problem
     * T2 := Are the unconstrained overbooking overrides
     */
    public Tuple2<List<UnconstrainedOverbookingOverrideConfig>, List<DecisionOvrbkPropertyOVR>> getUnconstrainedPropertyOverridesV2(List<UnconstrainedOverbookingOverrideConfig> configs) {
        return propertyLevelOverrideUnconstrainer.getUnconstrainedOverbookingOverridesV2(configs);
    }

    /**
     * Unconstrains any overbooking overrides at the room-type level for override configurations
     *
     * @param configs
     * @return A tuple where
     * T1 := Are the configs which were skipped due to extraneous edge cases, e.g., the override
     * no longer exists or is causing a problem
     * T2 := Are the unconstrained overbooking overrides
     */
    public Tuple2<List<UnconstrainedOverbookingOverrideConfig>, List<DecisionOvrbkAccomOVR>> getUnconstrainedAccomTypeOverrides(List<UnconstrainedOverbookingOverrideConfig> configs) {
        return accomLevelOverrideUnconstrainer.getUnconstrainedOverbookingOverrides(configs);
    }

    /**
     * Unconstrains any overbooking overrides at the room-type level for override configurations
     *
     * @param configs
     * @return A tuple where
     * T1 := Are the configs which were skipped due to extraneous edge cases, e.g., the override
     * no longer exists or is causing a problem
     * T2 := Are the unconstrained overbooking overrides
     */
    public Tuple2<List<UnconstrainedOverbookingOverrideConfig>, List<DecisionOvrbkAccomOVR>> getUnconstrainedAccomTypeOverridesV2(List<UnconstrainedOverbookingOverrideConfig> configs) {
        return accomLevelOverrideUnconstrainer.getUnconstrainedOverbookingOverridesV2(configs);
    }

}
