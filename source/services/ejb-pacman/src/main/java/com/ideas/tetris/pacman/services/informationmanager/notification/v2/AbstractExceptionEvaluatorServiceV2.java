package com.ideas.tetris.pacman.services.informationmanager.notification.v2;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.common.configparams.AlertConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceMarketOccupancyForecastNotifications;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.PaceMktSegActivity;
import com.ideas.tetris.pacman.services.dashboard.util.DateCalculator;
import com.ideas.tetris.pacman.services.informationmanager.alert.AlertEvaluationException;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.AlertService;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrExcepNotifEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrExcepNotifSkipDatesEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrHistoryEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrHistoryTypeEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrInstanceEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrStatusEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrTypeEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrAlertConfigEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrSubLevelEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrSubTypeEntity;
import com.ideas.tetris.pacman.services.informationmanager.enums.ExceptionSubType;
import com.ideas.tetris.pacman.services.informationmanager.enums.LevelType;
import com.ideas.tetris.pacman.services.informationmanager.enums.MetricType;
import com.ideas.tetris.pacman.services.informationmanager.enums.RelationalOperator;
import com.ideas.tetris.pacman.services.informationmanager.enums.SubLevelType;
import com.ideas.tetris.pacman.services.informationmanager.exception.types.InfoMgrExcepNotifSnoozer;
import com.ideas.tetris.pacman.services.informationmanager.exception.types.InfoMgrExcepNotifSnoozerFactory;
import com.ideas.tetris.pacman.services.informationmanager.notification.interfaces.INotificationEvaluator;
import com.ideas.tetris.pacman.services.informationmanager.notification.querybuilder.NotificationEvaluationQuery;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessType;
import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.specialevent.entity.PropertySpecialEventInstance;
import com.ideas.tetris.pacman.services.specialevent.service.SpecialEventService;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateAccomType;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompetitors;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.util.jdbc.JpaJdbcUtil;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.common.constants.Constants.ACCOM_CLASS_ID;
import static com.ideas.tetris.pacman.common.constants.Constants.COLON;
import static com.ideas.tetris.pacman.common.constants.Constants.DECISION_TYPE_BDE;
import static com.ideas.tetris.pacman.common.constants.Constants.DECISION_TYPE_CDP;
import static com.ideas.tetris.pacman.common.constants.Constants.IM_LABEL_METRIC;
import static com.ideas.tetris.pacman.services.dashboard.util.DateCalculator.calculateDateForLastYear;
import static com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrAlertConfigEntity.ADDITIONAL_CONDITION_PARAM_ROOM_CLASS_ID;
import static com.ideas.tetris.pacman.services.informationmanager.exception.service.ExceptionConfigService.ADDITIONAL_CONDITIONS_PARAM_VALUE_SEPARATOR;
import static com.ideas.tetris.pacman.services.informationmanager.exception.service.ExceptionConfigService.ADDITIONAL_CONDITIONS_SEPARATOR;
import static com.ideas.tetris.pacman.util.Runner.runIfTrue;
import static com.ideas.tetris.platform.common.utils.dateutil.DateUtil.sqlDate;
import static java.util.Arrays.asList;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

@SuppressWarnings({"java:S107", "java:S1871", "squid:S3776"})
@Transactional
@Component
public abstract class AbstractExceptionEvaluatorServiceV2 extends AbstractAlertServiceV2 implements INotificationEvaluator {
    public static final String DECISION_CHANGED_BY_ACCOM_CLASS = "select   " +
            "latest.CP_Pace_Decision_Bar_Output_ID ,     " +
            "latest.Property_ID,    " +
            "latest.Arrival_DT,    " +
            "latest.Accom_Type_ID ,   " +
            "latest.Final_BAR as latestFinalBar,   " +
            "latest.Decision_ID ,   " +
            "previous.Final_BAR as previousFinalBar,    " +
            "latest.Final_BAR - previous.Final_BAR as diff,   latest.LOS  " +
            "from (select a.CP_Pace_Decision_Bar_Output_ID, a.Arrival_DT, a.Property_ID, a.Accom_Type_ID, a.Final_BAR, a.Decision_ID, a.LOS from CP_Pace_Decision_Bar_Output a     " +
            "  where a.Accom_Type_ID= (select Accom_Type_ID from CP_Cfg_AC where Accom_Class_ID= :inp_accom_class_id)     " +
            "  and a.Arrival_DT between :startDate and :endDate     " +
            "  and a.Product_ID = 1     " +
            "  and a.Property_ID = :propertyId " +
            "  and a.Decision_ID = (select MAX(Decision_ID) from CP_Pace_Decision_Bar_Output        " +
            "       where Property_ID = :propertyId AND Decision_ID  = (select MAX(Decision_ID) from Decision where Decision_Type_ID in (:decisionTypeIds)))        " +
            ") as latest     " +
            "inner join  (select b.Final_BAR, b.Arrival_DT from CP_Pace_Decision_Bar_Output b     " +
            "   where b.Accom_Type_ID=(select Accom_Type_ID from CP_Cfg_AC where Accom_Class_ID= :inp_accom_class_id)     " +
            "   and b.Arrival_DT between :startDate and :endDate     " +
            "   and b.Product_ID = 1     " +
            "   and b.Property_ID = :propertyId " +
            "   and b.Decision_ID = (select MAX(Decision_ID) from CP_Pace_Decision_Bar_Output       " +
            "    where Property_ID = :propertyId AND Product_ID = 1 and Decision_ID  in (select Decision_ID from Decision where Decision_Type_ID in (:decisionTypeIds))       " +
            "    and Decision_ID <> (select MAX(Decision_ID) from CP_Pace_Decision_Bar_Output       " +
            "        where Property_ID = :propertyId AND Product_ID = 1 and Decision_ID  in (select Decision_ID from Decision where Decision_Type_ID in (:decisionTypeIds) )))     " +
            ") as previous     " +
            "on latest.Arrival_DT=previous.Arrival_DT        " +
            "and latest.Final_BAR-previous.Final_BAR <> 0 ";

    public static final String BUSINESS_DATE = "businessDate";
    public static final String PROPERTY_ID = "propertyId";
    public static final String OCCUPANCY_DATE = "occupancyDt";
    public static final String CONFIG_ID = "configId";
    public static final String IS_PHYSICAL_CAPACITY_ENABLED = "is_physical_capacity_enabled";
    public static final String SUB_TYPE = "subType";
    public static final String NOTIFICATION_EXCEPTION_MSG = "Unable to create or update the exception/notification.";
    public static final String DECISION_AS_OF_BY_ACCOM_CLASS = "select  CP_Pace_Decision_Bar_Output_ID , " +
            " Property_ID,  " +
            " Arrival_DT,  " +
            " Accom_Type_ID , " +
            " Final_BAR as optimalBar, " +
            " Decision_ID , " +
            " Pretty_BAR ,  " +
            " Final_BAR as diff,  " +
            " LOS  " +
            "   from CP_Pace_Decision_Bar_Output  " +
            "   where Accom_Type_ID= (select Accom_Type_ID from CP_Cfg_AC where Accom_Class_ID= :inp_accom_class_id) " +
            "   and Arrival_DT between :startDate and :endDate " +
            "   and Product_ID = 1 " +
            "   and Property_ID = :propertyId " +
            "   and Decision_ID = (select MAX(Decision_ID) from CP_Pace_Decision_Bar_Output " +
            "      where Product_ID = 1 and Decision_ID  = (select MAX(Decision_ID) from Decision where Decision_Type_ID in (:decisionTypeIds)))   ";

    public static final String TRANSIENT_ON_BOOKS_STLY_BY_DATES = new StringBuilder()
            .append("select  Occupancy_DT, SUM(Rooms_Sold) as on_books from PACE_Mkt_Activity as pmktact")
            .append(" inner join Mkt_Seg_Details as asd ")
            .append(" on pmktact.Mkt_Seg_ID = asd.Mkt_Seg_ID ")
            .append(" where asd.Business_Type_ID = 2 and asd.Booking_Block_Pc = 0 ")
            .append(" and pmktact.Occupancy_DT in :occupancyDates and Business_Day_End_DT = :businessDayEndDate")
            .append(" group by Occupancy_DT").toString();
    public static final String HISTORY = "history";
    public static final String EXCEPTION = "exception";
    public static final String DATE_FORMAT = "MM/dd/yyyy";
    public static final String EVALUATION_COMPLETED_SUCCESSFULLY = "Evaluation completed successfully";
    private static final Logger LOG = Logger.getLogger(AbstractExceptionEvaluatorServiceV2.class.getName());
    private static final BigDecimal BIG_DECIMAL_100 = new BigDecimal(100);
    @Autowired
	protected AlertService alertService;
    @Autowired
	protected PacmanConfigParamsService configParamService;
    @Autowired
	protected InfoMgrExcepNotifSnoozerFactory infoMgrExcepNotifSnoozerFactory;
    @Autowired
	protected SpecialEventService specialEventService;
    @Autowired
	private JpaJdbcUtil jpaJdbcUtil;

    public String evaluate(AlertType alertType, NotificationEvaluationQuery notificationEvaluationQuery, Boolean deletePastConfig) {
        if (isAlertTypeEnabled(alertType.toString())) {
            List<InformationMgrAlertConfigEntity> exceptionConfigs = findExceptionConfigurationsToProcess(alertType.toString());
            evaluateByType(exceptionConfigs, alertType, notificationEvaluationQuery, deletePastConfig);
        }
        return EVALUATION_COMPLETED_SUCCESSFULLY;
    }

    public void evaluateByType(List<InformationMgrAlertConfigEntity> exceptionConfigs, AlertType alertType, NotificationEvaluationQuery notificationEvaluationQuery, Boolean deletePastConfig) {
        evaluateByType(exceptionConfigs, alertType, notificationEvaluationQuery, deletePastConfig, getStringRateUnqualifiedMap(),
                getUserName(PacmanWorkContextHelper.getUserId()), getAllWebrateAccomType(),
                configParamService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value()), configParamService.isEnablePhysicalCapacityConsideration(),
                getCaughtUpDate(), getMasterAccomClassId(), (Boolean) configParamService.getParameterValue(AlertConfigParamName.USER_EXCEPTIONS_ENABLED),
                alertService.getMasterClassForProperty(PacmanWorkContextHelper.getPropertyId()), alertService.getRohRoomTypeForProperty(PacmanWorkContextHelper.getPropertyId()));
    }

    private Date getCaughtUpDate() {
        return dateService.getCaughtUpDate();
    }

    public void setInfoMgrExcepNotifSnoozerFactory(InfoMgrExcepNotifSnoozerFactory infoMgrExcepNotifSnoozerFactory) {
        this.infoMgrExcepNotifSnoozerFactory = infoMgrExcepNotifSnoozerFactory;
    }

    public void evaluateByType(List<InformationMgrAlertConfigEntity> exceptionConfigs, AlertType alertType, NotificationEvaluationQuery notificationEvaluationQuery, Boolean deletePastConfig,
                               Map<String, RateUnqualified> stringRateUnqualifiedMap, String userName, Map<Integer, WebrateAccomType> allWebrateAccomType, boolean isCPEnabled,
                               boolean enablePhysicalCapacityConsideration, Date caughtUpDate, Integer masterAccomClassId, Boolean exceptionsEnabled,
                               AccomClass masterClass, AccomType rohRoomType) {
        Date businessDate = dateService.getBusinessDate();
        List<InfoMgrStatusEntity> statusList = tenantCrudService.findAll(InfoMgrStatusEntity.class);
        List<InfoMgrHistoryTypeEntity> historyTypeEntities = tenantCrudService.findAll(InfoMgrHistoryTypeEntity.class);

        NotificationDetailsComponentsCache detailsComponentsCache = buildNotificationDetailsComponentsCache(businessDate);

        CrudOperations crudOperations = getCrudOperations();

        for (InformationMgrAlertConfigEntity config : exceptionConfigs) {
            try {
                evaluateException(config, businessDate, alertType, notificationEvaluationQuery, deletePastConfig, enablePhysicalCapacityConsideration, isCPEnabled, stringRateUnqualifiedMap, userName, allWebrateAccomType,
                        caughtUpDate, masterAccomClassId, exceptionsEnabled, masterClass, rohRoomType, crudOperations, statusList, historyTypeEntities, detailsComponentsCache);
            } catch (AlertEvaluationException e) {
                logErrorMessage(config, e);
            }
        }

        crudOperations.execute();
    }

    private NotificationDetailsComponentsCache buildNotificationDetailsComponentsCache(Date businessDate) {
        return NotificationDetailsComponentsCache.builder()
                .transientOnBooksStlyProvider(occDates -> this.bulkFetchTransientOnBooksStly(occDates, businessDate))
                .occupancyChangeByBusinessTypeProvider(this::bulkFetchOccupancyChangeByBusinessType)
                .specialEventLastYearExistsProvider(this::bulkFetchSpecialEventLastYearExists)
                .rankingForRateLevelProvider(this::bulkGetRankingForRateLevel)
                .rateByRankProvider(this::findRateByRank)
                .build();
    }

    public CrudOperations getCrudOperations() {
        return new CrudOperations(tenantCrudService, jpaJdbcUtil);
    }

    protected void evaluateException(InformationMgrAlertConfigEntity config, Date businessDate, AlertType alertType, NotificationEvaluationQuery notificationEvaluationQuery, Boolean deletePastConfig,
                                     boolean enablePhysicalCapacityConsideration, boolean isCPEnabled, Map<String, RateUnqualified> stringRateUnqualifiedMap, String userName,
                                     Map<Integer, WebrateAccomType> allWebrateAccomType, Date caughtUpDate, Integer masterAccomClassId, Boolean exceptionsEnabled, AccomClass masterClass,
                                     AccomType rohRoomType, CrudOperations crudOperations, List<InfoMgrStatusEntity> statusList, List<InfoMgrHistoryTypeEntity> historyTypeEntities,
                                     NotificationDetailsComponentsCache detailsComponentsCache) throws AlertEvaluationException {
        boolean pastConfig = deletePastConfig && isConfigInPast(config, businessDate, caughtUpDate);
        if (pastConfig) {
            decrementScoreAndInactivateException(config, crudOperations, detailsComponentsCache);
        } else {
            evaluateAndCreateException(config, businessDate, alertType, notificationEvaluationQuery, enablePhysicalCapacityConsideration, isCPEnabled, stringRateUnqualifiedMap, userName, allWebrateAccomType, caughtUpDate, masterAccomClassId,
                    exceptionsEnabled, masterClass, rohRoomType, crudOperations, statusList, historyTypeEntities, detailsComponentsCache);
        }
    }

    public boolean isConfigInPast(InformationMgrAlertConfigEntity config, Date businessDate, Date caughtUpDate) throws AlertEvaluationException {
        return isOccupancyDateInPast(parseDate(config.getEndDate(), caughtUpDate), businessDate);
    }

    protected boolean isOccupancyDateInPast(Date date, Date businessDate) {
        return date != null && (date.before(businessDate) || date.equals(businessDate));
    }

    protected void evaluateAndCreateException(InformationMgrAlertConfigEntity config, Date businessDate, AlertType alertType,
                                              NotificationEvaluationQuery notificationEvaluationQuery, boolean enablePhysicalCapacityConsideration,
                                              boolean isCPEnabled, Map<String, RateUnqualified> stringRateUnqualifiedMap, String userName, Map<Integer, WebrateAccomType> allWebrateAccomType,
                                              Date caughtUpDate, Integer masterAccomClassId, Boolean exceptionsEnabled, AccomClass masterClass, AccomType rohRoomType, CrudOperations crudOperations,
                                              List<InfoMgrStatusEntity> statusList, List<InfoMgrHistoryTypeEntity> historyTypeEntities, NotificationDetailsComponentsCache detailsComponentsCache) throws AlertEvaluationException {
        Map<String, Object> parameters = new HashMap<>();
        ParameterTypeV2 parameterType = ParameterTypeV2.valueOf(config.getExceptionLevel().getName());
        Map<String, Object> levelTypeParams = parameterType.levelTypeParams(config, this);
        parameters.putAll(levelTypeParams);
        parameters.put("startDate", sqlDate(parseDate(config.getStartDate(), caughtUpDate)));
        parameters.put("endDate", sqlDate(parseDate(config.getEndDate(), caughtUpDate)));
        String subType = config.getExceptionSubType().getName();
        String queryString = notificationEvaluationQuery.get(config.getExceptionSubType(), config.getExceptionLevel());

        if (alertType == AlertType.YOYPaceChangeEx) {
            parameters.put("lastStartDate", sqlDate(calculateDateForLastYear(parseDate(config.getStartDate(), caughtUpDate), true)));
            parameters.put("lastEndDate", sqlDate(calculateDateForLastYear(parseDate(config.getEndDate(), caughtUpDate), true)));
            parameters.put("lastBusinessDate", sqlDate(calculateDateForLastYear(businessDate, true)));
            parameters.put(BUSINESS_DATE, sqlDate(businessDate));
            parameters.put(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
            if (ParameterTypeV2.PROPERTY.equals(parameterType) || ParameterTypeV2.ROOM_CLASS.equals(parameterType) || ParameterTypeV2.ROOM_TYPE.equals(parameterType)) {
                parameters.put(IS_PHYSICAL_CAPACITY_ENABLED, enablePhysicalCapacityConsideration ? 1 : 0);
            }
        } else if (isBDEAlertType(alertType) || isCDPAlertType(alertType)) {
            //adding in the cp flow
            if (isCPNotificationEnabled(config, isCPEnabled)) {
                List<Integer> decisionTypeIds = getDecisionTypeIds(alertType);
                queryString = getCPDecisionQueryString(alertType);
                parameters.put(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
                parameters.put("decisionTypeIds", decisionTypeIds);
            } else {
                parameters.put(BUSINESS_DATE, sqlDate(businessDate));
                parameters.put(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
            }
        } else if (AlertType.CompetitorPriceAsOfLastNightlyOptimization.equals(alertType)) {
            parameters.put("webrateStatus", asList(Constants.WEBRATE_ACTIVE_STATUS_CODE));
            parameters.put(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
            if (isProductEnabledForCompetitorNotification() && config.getProduct() != null) {
                parameters.put("minlos", config.getProduct().getRateShoppingLOSMin());
                parameters.put("maxlos", config.getProduct().getRateShoppingLOSMax());
            } else {
                parameters.put("minlos", 1);
                parameters.put("maxlos", 1);
            }
            if (isCPCNotificationByRoomClassEnabled()) {
                parameters.put(ACCOM_CLASS_ID, getAccomClassId(config.getAdditionalConditions()));
            }
            LOG.debug("Query for generating CompetitorPriceAsOfLastNightlyOptimization Notification : " + queryString +
                    "\n Parameters : " + parameters.entrySet().stream()
                    .map(entry -> entry.getKey() + " : " + entry.getValue())
                    .collect(Collectors.joining(", ")));
        } else if (AlertType.CompetitorPriceChange.equals(alertType)) {
            parameters.put(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
            if (isProductEnabledForCompetitorNotification() && config.getProduct() != null) {
                parameters.put("minlos", config.getProduct().getRateShoppingLOSMin());
                parameters.put("maxlos", config.getProduct().getRateShoppingLOSMax());
            } else {
                parameters.put("minlos", 1);
                parameters.put("maxlos", 1);
            }
            if (isCPCNotificationByRoomClassEnabled()) {
                parameters.put(ACCOM_CLASS_ID, getAccomClassId(config.getAdditionalConditions()));
            }
            LOG.debug("Query for generating CompetitorPriceChange Notification : " + queryString +
                    "\n Parameters : " + parameters.entrySet().stream()
                    .map(entry -> entry.getKey() + " : " + entry.getValue())
                    .collect(Collectors.joining(", ")));
        } else if (alertType == AlertType.ForecastAsOfLastNightlyOptimization || alertType == AlertType.ForecastAsOfLastOptimization) {
            parameters.put(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId());
            parameters.put(SUB_TYPE, subType);
            if (!ExceptionSubType.WASH.toString().equals(subType)) {
                parameters.put(IS_PHYSICAL_CAPACITY_ENABLED, enablePhysicalCapacityConsideration ? 1 : 0);
            }
        } else if (alertType == AlertType.ForecastChangeEx || alertType == AlertType.ForecastChangeFromLastOptimization) {
            parameters.put(IS_PHYSICAL_CAPACITY_ENABLED, enablePhysicalCapacityConsideration ? 1 : 0);
        } else if (AlertType.BookingPaceEx.equals(alertType) || AlertType.UnexpectedDemandEx.equals(alertType)) {
            parameters.put(IS_PHYSICAL_CAPACITY_ENABLED, enablePhysicalCapacityConsideration ? 1 : 0);
            parameters.put(BUSINESS_DATE, caughtUpDate);
            parameters.put("masterClass", masterAccomClassId);
            parameters.remove("businessTypeId");
        }

        try {

            List<Object[]> resultList;
            if (!isCPNotificationEnabled(config, isCPEnabled) && alertType.isStaticThreshold()) {
                resultList = tenantCrudService.findByNamedQuery(
                        queryString,
                        parameters);
            } else {
                resultList = getResultSetFor(queryString, parameters);
            }

            createAndUpdateException(resultList, config, businessDate, stringRateUnqualifiedMap, userName, allWebrateAccomType,
                    exceptionsEnabled, masterClass, rohRoomType, crudOperations, statusList, historyTypeEntities, detailsComponentsCache, caughtUpDate);
        } catch (Exception e) {
            LOG.error(NOTIFICATION_EXCEPTION_MSG, e);
            throw new AlertEvaluationException(e.getMessage());
        }
    }

    private String getCPDecisionQueryString(AlertType alertType) {
        return isDecisionChangeAlert(alertType) ? DECISION_CHANGED_BY_ACCOM_CLASS : DECISION_AS_OF_BY_ACCOM_CLASS;
    }

    private boolean isDecisionChangeAlert(AlertType alertType) {
        return alertType.equals(AlertType.DecisionChangeEx) || alertType.equals(AlertType.DecisionChangeExAsOfLastOptimization);
    }

    private boolean isCDPAlertType(AlertType alertType) {
        return alertType == AlertType.DecisionChangeExAsOfLastOptimization || alertType == AlertType.DecisionAsOfLastOptimization;
    }

    private boolean isBDEAlertType(AlertType alertType) {
        return alertType == AlertType.DecisionChangeEx || alertType == AlertType.DecisionAsOfLastNightlyOptimization;
    }

    private List<Integer> getDecisionTypeIds(AlertType alertType) {
        return isCDPAlertType(alertType) ? Arrays.asList(DECISION_TYPE_BDE, DECISION_TYPE_CDP) : Arrays.asList(DECISION_TYPE_BDE);
    }

    private boolean isCPNotificationEnabled(InformationMgrAlertConfigEntity config, boolean isCPEnabled) {
        return config.getExceptionSubType().getName().equals(ExceptionSubType.PRICING_BY_VALUE.name()) && isCPEnabled;
    }

    protected void decrementScoreAndInactivateException(InformationMgrAlertConfigEntity config, CrudOperations crudOperations, NotificationDetailsComponentsCache detailsComponentsCache) throws AlertEvaluationException {
        InfoMgrExcepNotifEntity existingException = findExistingException(config, null);
        if (existingException != null) {
            List<InfoMgrExcepNotifEntity> listOfPastInstances = new ArrayList<>();
            listOfPastInstances.add(existingException);
            handleRaisedInstancesInPast(config, listOfPastInstances, crudOperations, detailsComponentsCache);
        }
    }


    public Date parseDate(String dateString) throws AlertEvaluationException {
        return parseDate(dateString, getCaughtUpDate());
    }

    // The date format is similar to: "TODAY+7"
    // We also accept static dates: "MM/dd/yyyy"
    public Date parseDate(String dateString, Date caughtUpDate) throws AlertEvaluationException {
        if (!StringUtils.containsIgnoreCase(dateString, "TODAY")) {
            try {
                return new SimpleDateFormat(DATE_FORMAT).parse(dateString);
            } catch (ParseException e) {
                throw new AlertEvaluationException("Unable to parse dateString: " + dateString + ". We expected MM/dd/yyyy");
            }
        }

        int plus = 0;

        if (dateString.length() > 5) {

            if (StringUtils.contains(dateString, "+")) {
                String plusString = StringUtils.substringAfterLast(dateString, "+");
                try {
                    plus = Integer.parseInt(plusString.trim());
                } catch (NumberFormatException e) {
                    throw new AlertEvaluationException(dateString);
                }
            } else if (StringUtils.contains(dateString, "-")) {
                String plusString = StringUtils.substringAfterLast(dateString, "-");
                try {
                    plus = -1 * Integer.parseInt(plusString.trim());
                } catch (NumberFormatException e) {
                    throw new AlertEvaluationException(dateString);
                }
            }
        }

        return DateUtil.addDaysToDate(caughtUpDate, plus);
    }


    /*
     * For existing (active) exceptions that no longer exceed their threshold
     */
    public int cutTheScoreInHalf(InfoMgrExcepNotifEntity exception) {
        int score = exception.getScore();
        if (score < 2) {
            return 0;
        }
        BigDecimal bigScore = new BigDecimal(score);

        return bigScore.divide(new BigDecimal(2), 0, BigDecimal.ROUND_HALF_UP).intValue();
    }

    /*
     * Calculate the score
     *
     * Should the multiplier be applied to the base score or the increment value?
     * From Jeorg's Excel spread sheet:
     * "80-50=30; it is 3 times higher than threshold. Hence the Actual Score will be 3 times base score"
     */
    public int calculateScore(InfoMgrExcepNotifEntity exception, BigDecimal threshold, BigDecimal delta, String operatorString) {

        int currentScore = exception.getScore();
        int baseScore = exception.getAlertType().getBaseScore();
        if (exception.getSubType().getName().equals(ExceptionSubType.WASH.getCode())) {
            delta = delta.abs().setScale(2, RoundingMode.HALF_UP);
        } else {
            delta = delta.abs().setScale(0, RoundingMode.HALF_UP);
        }
        BigDecimal multiplier = calculateMultiplier(delta, operatorString, threshold);
        BigDecimal newScore = (multiplier.multiply(new BigDecimal(baseScore))).add(new BigDecimal(currentScore));

        LOG.debug("The delta (" + delta + ") exceeds the threshold (" + threshold.doubleValue() + ")");
        LOG.debug("increasing the current score (" + currentScore + ") by [(multiplier: " + multiplier + ") * (base score: " + baseScore + ")] = " + newScore);

        return newScore.setScale(0, RoundingMode.HALF_UP).intValue();
    }


    /*
     * Divide the difference (before --> after) by the threshold.
     * The more that the delta exceeds the threshold the greater the multiple will be.
     * (We're rounding up to the nearest integer)
     */
    public BigDecimal calculateMultiplier(BigDecimal delta, String operatorString, BigDecimal threshold) {
        RelationalOperator operator = RelationalOperator.findByCode(operatorString);
        switch (operator) {
            case GREATER_OR_EQUAL:

            case LESS_THAN_OR_EQUAL:

            case INCREASED_OR_DECREASED_BY:

            case NOT_EQUAL:
                return delta.divide(threshold, 4, RoundingMode.HALF_UP);

            default:
                LOG.error("The threshold operator: " + operator.getCode()
                        + " is not supported yet");

        }

        return delta.divide(threshold, 4, RoundingMode.HALF_UP);

    }


    /*
     * compare the value to its threshold, using the configured thresholdType (
     * >,>=, <.. etc) and metric ( PERCENT, CURRENCY, ROOMS)
     */
    public boolean hasExceededThreshold(BigDecimal thresholdValue, BigDecimal delta, RelationalOperator operator, InformationMgrAlertConfigEntity config)
            throws AlertEvaluationException {
        delta = delta.setScale(2, RoundingMode.HALF_UP);

        switch (operator) {
            case GREATER_OR_EQUAL:
                return isThresholdGreaterOrEqualToDelta(thresholdValue, delta, config);
            case LESS_THAN_OR_EQUAL:
                return isThresholdLessThanOrEqualToDelta(thresholdValue, delta, config);
            case NOT_EQUAL:
                return isThresholdNotEqualToDelta(thresholdValue, delta);
            case LESS_THAN:
                return delta.compareTo(thresholdValue) < 0;
            case GREATER:
                return delta.compareTo(thresholdValue) > 0;
            case INCREASED_OR_DECREASED_BY:
                return isThresholdIncreasedOrDecreasedByDelta(thresholdValue, delta);
            default:
                throw new AlertEvaluationException("The threshold operator: " + operator.getCode()
                        + " is not supported yet");
        }
    }

    public void createAndUpdateException(List<Object[]> queryResultList, InformationMgrAlertConfigEntity config, final Date businessDate, Map<String, RateUnqualified> rateUnqualifiedMap,
                                         String userName, Map<Integer, WebrateAccomType> webrateAccomTypeMap, Boolean exceptionsEnabled,
                                         AccomClass masterClass, AccomType rohRoomType, CrudOperations crudOperations, List<InfoMgrStatusEntity> statusList,
                                         List<InfoMgrHistoryTypeEntity> infoMgrHistoryTypeEntities, NotificationDetailsComponentsCache detailsComponentsCache, Date caughtUpDate) throws AlertEvaluationException {
        Map<Integer, InfoMgrHistoryTypeEntity> typeMap = new HashMap<>();
        infoMgrHistoryTypeEntities.forEach(t -> typeMap.put(t.getId(), t));

        Map<Integer, InfoMgrStatusEntity> statusMap = new HashMap<>();
        statusList.forEach(s -> statusMap.put(s.getId(), s));
        List<InfoMgrHistoryEntity> historyEntities = new ArrayList<>();
        List<InfoMgrExcepNotifEntity> infoMgrExcepNotifEntities = new ArrayList<>();

        List<InfoMgrExcepNotifEntity> newlyCreatedNotifications = new ArrayList<>();

        InformationMgrAlertConfigEntity configEntity = tenantCrudService.find(InformationMgrAlertConfigEntity.class, config.getId());
        Map<String, List<InfoMgrExcepNotifEntity>> existingExcepNotifEntityMap = new HashMap<>();

        InfoMgrExcepNotifEntity criteria = new InfoMgrExcepNotifEntity();
        criteria.setPropertyId(config.getPropertyId());
        criteria.setAlertType(config.getAlertTypeEntity());
        criteria.setSubType(config.getExceptionSubType());
        criteria.setExceptionAlertConfigEntityId(config.getId());

        tenantCrudService.findByExample(criteria).forEach(e -> {
                    List<InfoMgrExcepNotifEntity> list = existingExcepNotifEntityMap.get(getKey(e));
                    if (list == null) {
                        list = new ArrayList<>();
                    }
                    list.add(e);
                    existingExcepNotifEntityMap.put(getKey(e), list);
                }
        );
        List<InfoMgrExcepNotifSkipDatesEntity> listDatesToSkipFromException = findTheDatesToSkipForConfiguration(config);
        List<Integer> reactivateStepsOfNotifications = new ArrayList<>();

        Set<Date> snoozedDates = Collections.emptySet();

        if (isAlertType(AlertType.BookingPaceEx, config) || isAlertType(AlertType.UnexpectedDemandEx, config)) {
            Set<Date> occupancyDates = queryResultList.stream()
                    .filter(this::hasSufficientDataForEvaluation)
                    .map(this::extractOccupancyDate)
                    .collect(Collectors.toSet());
            detailsComponentsCache.fetchOccupancyChangeByBusinessTypeLoadCache(occupancyDates);
            detailsComponentsCache.fetchTransientOnBooksStlyLoadCache(occupancyDates);
            detailsComponentsCache.specialEventLastYearExistsLoadCache(occupancyDates);

            InfoMgrExcepNotifSnoozer snoozer = infoMgrExcepNotifSnoozerFactory.createSystemExceptionInstance(configEntity.getAlertTypeEntity());
            if (snoozer != null) {
                snoozedDates = snoozer.getSnoozedOccupancyDatesFor(config);
            }
        }

        if (config.getExceptionSubType().getName().equalsIgnoreCase(ExceptionSubType.PRICING.getCode())) {
            Set<String> optValuesAsRateNames = queryResultList.stream()
                    .filter(this::hasSufficientDataForEvaluation)
                    .flatMap(row -> Stream.of(getOptimizationValue(config, row, 4), getOptimizationValue(config, row, 6)))
                    .collect(Collectors.toSet());
            detailsComponentsCache.getRankingForRateLevelLoadCache(optValuesAsRateNames);
        }

        Map<Date, String> specialEventsMap = getSpecialEventsDateWiseMap(queryResultList);

        for (Object[] objResult : queryResultList) {
            try {
                BigDecimal threshold = config.getThresholdValue();
                String operator = config.getThresholdOperator();
                if (hasSufficientDataForEvaluation(objResult)) {
                    BigDecimal delta = (BigDecimal) objResult[7];

                    Date occupancyDate = extractOccupancyDate(objResult);
                    String specialEvents = specialEventsMap.get(occupancyDate);
                    String currentOptimizationValStr = getOptimizationValue(config, objResult, 4);
                    String lastOptimizationValStr = getOptimizationValue(config, objResult, 6);

                    if (!isOccupancyDateInPast(occupancyDate, businessDate)) {
                        coreCreateAndUpdateException(config, objResult, threshold, operator,
                                delta, occupancyDate, currentOptimizationValStr,
                                lastOptimizationValStr, typeMap, statusMap, historyEntities, infoMgrExcepNotifEntities,
                                configEntity, existingExcepNotifEntityMap, listDatesToSkipFromException, rateUnqualifiedMap, webrateAccomTypeMap, userName, reactivateStepsOfNotifications, exceptionsEnabled,
                                masterClass, rohRoomType, webrateAccomTypeMap, newlyCreatedNotifications, detailsComponentsCache, snoozedDates, caughtUpDate, specialEvents);
                    }
                } else {
                    LOG.debug("No sufficient data to decide whether to create or update exception/notification.");
                }
            } catch (AlertEvaluationException e) {
                LOG.error(NOTIFICATION_EXCEPTION_MSG, e);
                throw new AlertEvaluationException(NOTIFICATION_EXCEPTION_MSG, e);
            }
        }

        crudOperations.createStepsFor(newlyCreatedNotifications);
        crudOperations.createHistories(historyEntities);
        crudOperations.reactivateStepsOf(reactivateStepsOfNotifications);
        crudOperations.updateNotifications(infoMgrExcepNotifEntities);

        List<InfoMgrExcepNotifEntity> listOfPastInstances = getListOfPastInstances(config);
        handleRaisedInstancesInPast(config, listOfPastInstances, crudOperations, detailsComponentsCache);
    }

    protected Date getConvertOccupancyDate(Object o) {
        Date occupancyDate;
        if (o instanceof String) {
            occupancyDate = DateUtil.toDate((String) o);
        } else {
            occupancyDate = (Date) o;
        }
        return occupancyDate;
    }

    protected Map<Date, String> getSpecialEventsDateWiseMap(List<Object[]> queryResultList) {
        return Collections.emptyMap();
    }

    private boolean hasSufficientDataForEvaluation(Object[] objResult) {
        return null != objResult[7] && null != objResult[2]
                && null != objResult[4] && null != objResult[6];
    }

    private Date extractOccupancyDate(Object[] objResult) {
        Date occupancyDate;

        if (objResult[2] instanceof String) {
            occupancyDate = DateUtil.toDate((String) objResult[2]);
        } else {
            occupancyDate = (Date) objResult[2];
        }
        return occupancyDate;
    }

    private Map<String, RateUnqualified> getStringRateUnqualifiedMap() {
        Map<String, RateUnqualified> rateUnqualifiedMap = new HashMap<>();
        List<RateUnqualified> rateUnqualifiedList = tenantCrudService.findByNamedQuery(RateUnqualified.ALL_RATES_BY_RANK,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
        rateUnqualifiedList.stream().forEach(r -> rateUnqualifiedMap.put(r.getName(), r));
        return rateUnqualifiedMap;
    }

    protected Map<Integer, WebrateAccomType> getAllWebrateAccomType() {
        return tenantCrudService.<Object[]>findByNamedQuery(WebrateAccomType.FETCH_SHALLOW_ALL)
                .stream()
                .map(row -> {
                    WebrateAccomType webrateAccomType = new WebrateAccomType();
                    webrateAccomType.setId((Integer) row[0]);
                    webrateAccomType.setWebrateAccomName((String) row[1]);
                    return webrateAccomType;
                })
                .collect(Collectors.toMap(WebrateAccomType::getId, c -> c));
    }

    public InfoMgrExcepNotifEntity getExistingExceptionByAccomType(Date occupancyDate, List<InfoMgrExcepNotifEntity> existingList, String accomType) {
        for (InfoMgrExcepNotifEntity excepNotifEntity : existingList) {
            if ((excepNotifEntity.getOccupancyDate().compareTo(occupancyDate) == 0 ||
                    excepNotifEntity.getOccupancyDate() == null) &&
                    excepNotifEntity.getAlertStatus().getId() != Constants.ALERT_STATUS_RESOLVED_ID &&
                    excepNotifEntity.getDetails().contains("room.type:" + accomType.trim())) {
                return excepNotifEntity;
            }
        }
        return null;
    }

    public String getOptimizationValue(InformationMgrAlertConfigEntity config, Object[] objectResult, int optimizationValueIndex) {
        return isAlertType(AlertType.BookingPaceEx, config) || isAlertType(AlertType.UnexpectedDemandEx, config) ? objectResult[3].toString() : objectResult[optimizationValueIndex].toString();
    }


    public String extractPriceValue(Object object) {
        String ratePlanNameWithPriceValueStr = (String) object;
        ratePlanNameWithPriceValueStr = ratePlanNameWithPriceValueStr.trim();
        String[] ratePlanNameWithPriceValues = ratePlanNameWithPriceValueStr.split("\\(");
        return (null != ratePlanNameWithPriceValues[0]) ? ratePlanNameWithPriceValues[0].trim() : "0";
    }

    // Need ro revisit and refactor parameters
    public void coreCreateAndUpdateException(InformationMgrAlertConfigEntity config,
                                             Object[] objResult, BigDecimal threshold, String operator,
                                             BigDecimal delta, Date occupancyDate,
                                             String currentOptimizationValStr, String lastOptimizationValStr,
                                             Map<Integer, InfoMgrHistoryTypeEntity> typeMap, Map<Integer, InfoMgrStatusEntity> statusMap,
                                             List<InfoMgrHistoryEntity> historyEntities, List<InfoMgrExcepNotifEntity> infoMgrExcepNotifEntities,
                                             InformationMgrAlertConfigEntity configEntity,
                                             Map<String, List<InfoMgrExcepNotifEntity>> existingExcepNotifEntityMap,
                                             List<InfoMgrExcepNotifSkipDatesEntity> listDatesToSkipFromException, Map<String, RateUnqualified> rateUnqualifiedMap, Map<Integer, WebrateAccomType> webrateAccomTypeMap,
                                             String userName, List<Integer> reactivateStepsOfNotifications, boolean exceptionsEnabled,
                                             AccomClass masterClass, AccomType rohRoomType, Map<Integer, WebrateAccomType> allWebrateAccomType,
                                             List<InfoMgrExcepNotifEntity> newlyCreatedNotifications, NotificationDetailsComponentsCache detailsComponentsCache, Set<Date> snoozedDates, Date caughtUpDate, String specialEvents)
            throws AlertEvaluationException {
        List<InfoMgrExcepNotifEntity> existingExceptionList = existingExcepNotifEntityMap.get(getKey(config));

        InfoMgrExcepNotifEntity existingException = extractExistingException(config, objResult, occupancyDate, webrateAccomTypeMap, existingExceptionList);

        int occupancyComparisonResult = compareOccupancyDate(occupancyDate, listDatesToSkipFromException, existingException);

        if (0 != occupancyComparisonResult) {

            delta = evaluateDeltaByThresholdMetricTypePercent(config, delta, currentOptimizationValStr, lastOptimizationValStr);

            delta = delta.setScale(2, RoundingMode.HALF_UP);
            BigDecimal changeddelta = delta;
            if (!config.getExceptionSubType().getName().equals(ExceptionSubType.WASH.getCode())) {
                changeddelta = checkForDeltaBasedOnOperator(operator, delta);
            }
            RelationalOperator relationalOperator = RelationalOperator.findByCode(operator);

            boolean hasThresholdExceeded;
            InfoMgrExcepNotifSnoozer infoMgrExcepNotifSnoozer = infoMgrExcepNotifSnoozerFactory.createSystemExceptionInstance(config.getAlertTypeEntity());
            if (Objects.nonNull(infoMgrExcepNotifSnoozer) && snoozedDates.contains(occupancyDate)) {
                hasThresholdExceeded = infoMgrExcepNotifSnoozer.isSnoozeConditionWorsen(config, occupancyDate, changeddelta, relationalOperator);
                runIfTrue(hasThresholdExceeded, () -> infoMgrExcepNotifSnoozer.unsnooze(createUnsnoozeParamMap(config, occupancyDate)));
            } else {
                hasThresholdExceeded = checkForThreshhold(config, threshold, changeddelta, relationalOperator);
            }

            if (hasThresholdExceeded) {

                if (exceptionsEnabled) {

                    changeddelta = calculateDeltaForStaticThreshold(config, threshold, currentOptimizationValStr, changeddelta, rateUnqualifiedMap);

                    if (existingException == null) {
                        LOG.debug("creating new exception for config ID: " + config.getId());

                        // Need ro revisit and refactor parameters
                        Map<String, Object> resultMap = createException(config, occupancyDate, currentOptimizationValStr,
                                lastOptimizationValStr, threshold, changeddelta, objResult, delta, typeMap, statusMap, userName, masterClass, rohRoomType,
                                allWebrateAccomType, newlyCreatedNotifications, detailsComponentsCache, caughtUpDate, specialEvents);

                        addNewEntriesIntoExistingMap(historyEntities, existingExcepNotifEntityMap, existingExceptionList, resultMap);

                    } else {
                        updateExistingExceptionScore(config, objResult, threshold, operator, delta, occupancyDate, currentOptimizationValStr, lastOptimizationValStr, typeMap, statusMap, historyEntities, infoMgrExcepNotifEntities,
                                configEntity, existingException, changeddelta, reactivateStepsOfNotifications, masterClass, rohRoomType, webrateAccomTypeMap, detailsComponentsCache, caughtUpDate, specialEvents);
                    }
                } else {
                    LOG.debug("We exceeded the threshold but the parameter that allows exceptions to be created (" + AlertConfigParamName.USER_EXCEPTIONS_ENABLED.value() + ") is FALSE");
                }
                // if the exception does not exists, cut the score in half
            } else {
                lowerExistingExceptionScore(config, objResult, threshold, delta, occupancyDate, currentOptimizationValStr, lastOptimizationValStr, typeMap, statusMap, historyEntities,
                        infoMgrExcepNotifEntities, configEntity, existingException, masterClass, rohRoomType, webrateAccomTypeMap, detailsComponentsCache, caughtUpDate, specialEvents);
            }
        }
    }

    public void createAndUpdateException(List<Object[]> queryResultList, InformationMgrAlertConfigEntity config, final Date businessDate) throws AlertEvaluationException {
        List<InfoMgrStatusEntity> statusList = tenantCrudService.findAll(InfoMgrStatusEntity.class);
        List<InfoMgrHistoryTypeEntity> historyTypeEntities = tenantCrudService.findAll(InfoMgrHistoryTypeEntity.class);

        CrudOperations crudOperations = getCrudOperations();
        createAndUpdateException(queryResultList, config, businessDate, getStringRateUnqualifiedMap(),
                getUserName(PacmanWorkContextHelper.getUserId()), getAllWebrateAccomType(),
                (Boolean) configParamService.getParameterValue(AlertConfigParamName.USER_EXCEPTIONS_ENABLED),
                alertService.getMasterClassForProperty(PacmanWorkContextHelper.getPropertyId()),
                alertService.getRohRoomTypeForProperty(PacmanWorkContextHelper.getPropertyId()), crudOperations, statusList, historyTypeEntities,
                buildNotificationDetailsComponentsCache(businessDate), getCaughtUpDate());
        crudOperations.execute();
    }

    private Map<String, Object> createUnsnoozeParamMap(InformationMgrAlertConfigEntity config, Date occupancyDate) {
        Map<String, Object> unsnoozeParameters = new HashMap<>();
        unsnoozeParameters.put(InfoMgrExcepNotifSnoozer.PROPERTY_ID, config.getPropertyId());
        unsnoozeParameters.put(InfoMgrExcepNotifSnoozer.EXCEP_NOTIF_CONFIG_ENTITY, config);
        unsnoozeParameters.put(InfoMgrExcepNotifSnoozer.OCCUPANCY_DATE, occupancyDate);
        return unsnoozeParameters;
    }

    public InfoMgrExcepNotifEntity getExistingExceptionPricingByValue(Date occupancyDate, List<InfoMgrExcepNotifEntity> existingList, String los) {
        for (InfoMgrExcepNotifEntity excepNotifEntity : existingList) {
            if ((excepNotifEntity.getOccupancyDate().compareTo(occupancyDate) == 0 ||
                    excepNotifEntity.getOccupancyDate() == null) &&
                    excepNotifEntity.getAlertStatus().getId() != Constants.ALERT_STATUS_RESOLVED_ID &&
                    excepNotifEntity.getDetails().contains("LOS:" + los.trim())) {
                return excepNotifEntity;
            }
        }
        return null;
    }

    public InfoMgrExcepNotifEntity getExistingException(Date occupancyDate, List<InfoMgrExcepNotifEntity> existingList) {
        for (InfoMgrExcepNotifEntity excepNotifEntity : existingList) {
            if ((occupancyDate == null || (excepNotifEntity.getOccupancyDate().compareTo(occupancyDate) == 0 ||
                    excepNotifEntity.getOccupancyDate() == null)) &&
                    excepNotifEntity.getAlertStatus().getId() != Constants.ALERT_STATUS_RESOLVED_ID) {
                return excepNotifEntity;
            }
        }
        return null;
    }

    private boolean checkForThreshhold(InformationMgrAlertConfigEntity config, BigDecimal threshold, BigDecimal changeddelta, RelationalOperator relationalOperator) throws AlertEvaluationException {
        if (config.getExceptionSubType().getName().equals(ExceptionSubType.WASH.getCode())) {
            return hasExceededThreshold(threshold, changeddelta, relationalOperator, config);
        }
        return hasExceededThreshold(threshold, changeddelta, relationalOperator, config) && changeddelta.setScale(0, RoundingMode.HALF_UP).compareTo(BigDecimal.ZERO) != 0;
    }

    public BigDecimal calculateDeltaForStaticThreshold(InformationMgrAlertConfigEntity config, BigDecimal threshold, String currentOptimizationValStr, BigDecimal changeddelta, Map<String, RateUnqualified> rateUnqualifieds) {
        if (config.getAlertTypeEntity().isStaticThreshold()) {
            if (config.getExceptionSubType().getName().equalsIgnoreCase(ExceptionSubType.PRICING.getCode())) {
                String currentOptValStr = rateUnqualifieds.get(currentOptimizationValStr).getRanking().toString();
                changeddelta = (new BigDecimal(currentOptValStr).subtract(threshold)).abs().add(threshold);
            } else {
                changeddelta = (new BigDecimal(currentOptimizationValStr).subtract(threshold)).abs().add(threshold);
            }
        }
        return changeddelta;
    }

    protected List<InfoMgrExcepNotifEntity> getListOfPastInstances(InformationMgrAlertConfigEntity config) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("type", config.getAlertTypeEntity().getName());
        parameters.put(SUB_TYPE, config.getExceptionSubType().getName());
        parameters.put(PROPERTY_ID, config.getPropertyId());
        parameters.put(BUSINESS_DATE, getBusinessDateForProperty(config.getPropertyId()));
        parameters.put("statusId", Constants.ALERT_STATUS_RESOLVED_ID);
        parameters.put(CONFIG_ID, config.getId());

        return tenantCrudService.findByNamedQuery(
                InfoMgrExcepNotifEntity.PAST_BUSINESS_DATE_BY_TYPES_AND_PROPERTY_ID,
                parameters);

    }

    public void handleRaisedInstancesInPast(InformationMgrAlertConfigEntity config, List<InfoMgrExcepNotifEntity> listPastNonResolved, CrudOperations crudOperations, NotificationDetailsComponentsCache detailsComponentsCache) throws AlertEvaluationException {
        List<InfoMgrHistoryEntity> historyEntities = new ArrayList<>();
        List<InfoMgrExcepNotifEntity> infoMgrExcepNotifEntities = new ArrayList<>();
        try {
            if (null != listPastNonResolved && !listPastNonResolved.isEmpty()) {
                InfoMgrStatusEntity alertStatusEntity = tenantCrudService.find(InfoMgrStatusEntity.class,
                        Constants.ALERT_STATUS_RESOLVED_ID);
                for (InfoMgrExcepNotifEntity pastInstance : listPastNonResolved) {
                    boolean isInPastAndAlreadyDecremented = false;
                    if (pastInstance.getStatusId().intValue() == Constants.INACTIVE_STATUS_ID.intValue()) {
                        isInPastAndAlreadyDecremented = true;
                    }
                    if (!isInPastAndAlreadyDecremented) {
                        pastInstance.setLastModificationDate(new Date());
                        pastInstance.setScore(cutTheScoreInHalf(pastInstance));
                        pastInstance.setStatusId(Constants.INACTIVE_STATUS_ID);
                        InfoMgrHistoryEntity history = null;
                        if (pastInstance.getScore() <= 1) {
                            LOG.debug("Exception score is now low enough that we're declaring it RESOLVED. (id:"
                                    + config.getId() + ")");
                            pastInstance.setScore(0);
                            pastInstance.setAlertStatus(alertStatusEntity);
                            history = createHistory(
                                    Constants.ALERT_HISTORY_RESOLVED_ID, pastInstance, detailsComponentsCache);
                        } else {
                            history = createHistory(
                                    Constants.ALERT_HISTORY_SCORE_DECREASED_ID, pastInstance, detailsComponentsCache);
                        }
                        historyEntities.add(history);
                        infoMgrExcepNotifEntities.add(pastInstance);
                    } else {
                        LOG.debug("Notification for date " + pastInstance.getOccupancyDate() + " is in past and already has score updated."
                                + " Configuration ID - " + config.getId() + " Property ID - " + config.getPropertyId());
                    }
                }
                crudOperations.createHistories(historyEntities);
                crudOperations.updateNotifications(infoMgrExcepNotifEntities);
            }
        } catch (Exception e) {
            LOG.error("Unable to update the Past Instances for Configuration Id " + config.getId(), e);
            throw new AlertEvaluationException("Unable to update the Past Instances for Configuration Id " + config.getId(), e);
        }
    }

    public BigDecimal checkForDeltaBasedOnOperator(String operator,
                                            BigDecimal delta) {
        if ((operator.equals(RelationalOperator.LESS_THAN_OR_EQUAL.getCode()) || operator.equals(RelationalOperator.NOT_EQUAL.getCode()))
                && delta.setScale(0, RoundingMode.HALF_UP).compareTo(BigDecimal.ZERO) == 0) {
            delta = BigDecimal.ONE;
        }
        return delta;
    }

    private Map<String, Object> getInfoMgrExcepNotifEntityParameters(InformationMgrAlertConfigEntity config, Date occupancyDate) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("type", config.getAlertTypeEntity().getName());
        parameters.put(SUB_TYPE, config.getExceptionSubType().getName());
        parameters.put(PROPERTY_ID, config.getPropertyId());
        parameters.put("occupancyDate", occupancyDate);
        parameters.put("statusId", Constants.ALERT_STATUS_RESOLVED_ID);
        parameters.put(CONFIG_ID, config.getId());
        return parameters;
    }

    protected InfoMgrExcepNotifEntity findExistingException(InformationMgrAlertConfigEntity config, Date occupancyDate) {
        String queryName = InfoMgrExcepNotifEntity.BY_TYPES_AND_PROPERTY_ID;
        //added clause for the OccupancyDate
        Map<String, Object> parameters = getInfoMgrExcepNotifEntityParameters(config, occupancyDate);

        return getInfoMgrExcepNotifEntity(queryName, parameters);
    }


    private InfoMgrExcepNotifEntity getInfoMgrExcepNotifEntity(String queryName, Map<String, Object> parameters) {
        try {
            return (InfoMgrExcepNotifEntity) tenantCrudService.findByNamedQuerySingleResult(queryName,
                    parameters);
        } catch (Exception e) {
            LOG.error("Error", e);
            return null;
        }
    }

    @SuppressWarnings("unchecked")
    public List<InfoMgrExcepNotifSkipDatesEntity> findTheDatesToSkipForConfiguration(InformationMgrAlertConfigEntity config) {
        return tenantCrudService.findByNamedQuery(InfoMgrExcepNotifSkipDatesEntity.BY_CONFIG, QueryParameter.with("exceptionConfigId", config.getId()).parameters());
    }

    /*
     * Create a new exception
     */
    public Map<String, Object> createException(InformationMgrAlertConfigEntity config,
                                        Date occupancyDate, String currentDecision, String lastDecision,
                                        BigDecimal threshold, BigDecimal changedDelta, Object[] objResult, BigDecimal delta, Map<Integer, InfoMgrHistoryTypeEntity> typeMap,
                                        Map<Integer, InfoMgrStatusEntity> statusMap, String userName, AccomClass masterClass, AccomType rohRoomType,
                                        Map<Integer, WebrateAccomType> allWebrateAccomType, List<InfoMgrExcepNotifEntity> newlyCreatedNotifications, NotificationDetailsComponentsCache detailsComponentsCache, Date caughtUpDate, String specialEvents) throws AlertEvaluationException {
        try {
            Map<String, Object> resultMap = new HashMap<>();
            LOG.info("creating a new Exception Alert");
            InfoMgrTypeEntity alertTypeEntity = config.getAlertTypeEntity();

            InfoMgrExcepNotifEntity exception = new InfoMgrExcepNotifEntity();
            String exceptionDetail = createDetails(config, occupancyDate, currentDecision, delta, threshold, objResult, specialEvents);
            List<InfoMgrInstanceEntity> competitorPriceChangeResolvedInstances = tenantCrudService.findByNamedQuery(InfoMgrInstanceEntity.BY_TYPE_AND_PROPERTY_ID_AND_DETAILS, QueryParameter.with("type", "CompetitorPriceChange").and("propertyId", config.getPropertyId()).and("details", exceptionDetail).and("category", Constants.EXCEPTION_CATEGORY).parameters());
            if (competitorPriceChangeResolvedInstances.isEmpty()) {
                exception.setAlertStatus(statusMap.get(Constants.ALERT_STATUS_NEW_ID));
                exception.setAlertType(alertTypeEntity);
                exception.setSubType(config.getExceptionSubType());
                exception.setCreateDate(new Date());
                exception.setCreatedBy(userName);
                exception.setDescription(alertTypeEntity.getDescription());
                exception.setDetails(createDetails(config, occupancyDate, currentDecision, delta, threshold, objResult, masterClass, rohRoomType, allWebrateAccomType, detailsComponentsCache, caughtUpDate, specialEvents));

                exception.setLastModificationDate(new Date());
                exception.setOccupancyDate(occupancyDate);
                exception.setPropertyId(config.getPropertyId());
                exception.setInfoMgrInstanceStepStateEntities(new ArrayList<>());

                int currentScore = 0;
                int baseScore = exception.getAlertType().getBaseScore();
                if (exception.getSubType().getName().equals(ExceptionSubType.WASH.getCode())) {
                    changedDelta = changedDelta.abs().setScale(2, RoundingMode.HALF_UP);
                } else {
                    changedDelta = changedDelta.abs().setScale(0, RoundingMode.HALF_UP);
                }
                BigDecimal multiplier = isAlertType(AlertType.BookingPaceEx, config) || isAlertType(AlertType.UnexpectedDemandEx, config) ?
                        changedDelta : calculateMultiplier(changedDelta, config.getThresholdOperator(), threshold);
                BigDecimal newScore = (multiplier.multiply(new BigDecimal(baseScore))).add(new BigDecimal(currentScore));

                exception.setScore(newScore.setScale(0, RoundingMode.HALF_UP).intValue());
                exception.setStatusId(Constants.ACTIVE_STATUS_ID);
                exception.setExceptionAlertConfigEntityId(config.getId());
                exception.setCurrentOptimizationValue(currentDecision);
                exception.setLastOptimizationValue(lastDecision);

                newlyCreatedNotifications.add(exception);
                exception = tenantCrudService.save(exception);
                InfoMgrHistoryEntity history = createHistory(
                        typeMap.get(Constants.ALERT_HISTORY_CREATED_ID), exception, userName);
                resultMap.put(HISTORY, history);
                resultMap.put(EXCEPTION, exception);
                return resultMap;
            }
            return null;
        } catch (Exception e) {
            logErrorMessage(config, new AlertEvaluationException("Unable to persist the Exception/Notification - " + e.getMessage()));
            throw new AlertEvaluationException("Unable to persist the Exception/Notification - ", e);
        }
    }

    @VisibleForTesting
    public String createDetails(InformationMgrAlertConfigEntity config, Date occupancyDate, String currentValue, BigDecimal delta, BigDecimal threshold, Object[] obj, String specialEvents) {
        NotificationDetailsComponentsCache detailsComponentsCache = buildNotificationDetailsComponentsCache(dateService.getBusinessDate());
        if (isAlertType(AlertType.BookingPaceEx, config) || isAlertType(AlertType.UnexpectedDemandEx, config)) {
            Set<Date> occupancyDates = Collections.singleton(occupancyDate);
            detailsComponentsCache.fetchOccupancyChangeByBusinessTypeLoadCache(occupancyDates);
            detailsComponentsCache.fetchTransientOnBooksStlyLoadCache(occupancyDates);
            detailsComponentsCache.specialEventLastYearExistsLoadCache(occupancyDates);
        }

        return createDetails(config, occupancyDate, currentValue, delta, threshold, obj, alertService.getMasterClassForProperty(PacmanWorkContextHelper.getPropertyId()),
                alertService.getRohRoomTypeForProperty(PacmanWorkContextHelper.getPropertyId()), getAllWebrateAccomType(), detailsComponentsCache, getCaughtUpDate(), specialEvents);
    }

    public String createDetails(InformationMgrAlertConfigEntity config, Date occupanctyDate,
                                String currentValue, BigDecimal delta, BigDecimal threshold, Object[] objResult, AccomClass masterClassForProperty,
                                AccomType rohRoomTypeForProperty, Map<Integer, WebrateAccomType> allWebrateAccomType,
                                NotificationDetailsComponentsCache detailsComponentsCache, Date caughtUpDate, String specialEvents) {

        StringBuilder detailsStr = new StringBuilder();
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MMM-yyyy");

            detailsStr.append(metricDetails(config));

            detailsStr.append(dateDetails(config, occupanctyDate, dateFormat, caughtUpDate));

            detailsStr.append(exceptionLevelDetails(config, masterClassForProperty, rohRoomTypeForProperty));

            String thresholdValue = threshold.toString();

            if (config.getAlertTypeEntity().getName().equalsIgnoreCase(AlertType.DecisionAsOfLastNightlyOptimization.toString())
                    || config.getAlertTypeEntity().getName().equalsIgnoreCase(AlertType.DecisionAsOfLastOptimization.toString())) {
                if (config.getExceptionSubType().getName().equalsIgnoreCase(ExceptionSubType.LRV.getCode())) {
                    detailsStr.append(Constants.IM_LABEL_CURRENT_LRV).append(COLON).append(new BigDecimal(currentValue).setScale(2, RoundingMode.HALF_UP));
                } else if (config.getExceptionSubType().getName().equalsIgnoreCase(ExceptionSubType.PRICING.getCode())) {
                    detailsStr.append(Constants.IM_LABEL_CURRENT_PRICE).append(COLON).append(currentValue);
                    RateUnqualified rateByRank = detailsComponentsCache.findRateByRank(threshold.intValue());
                    thresholdValue = rateByRank.getName();
                } else if (config.getExceptionSubType().getName().equalsIgnoreCase(ExceptionSubType.OVERBOOKING.getCode())) {
                    detailsStr.append(Constants.IM_LABEL_CURRENT_OVERBOOKING).append(COLON).append(new BigDecimal(currentValue).setScale(2, RoundingMode.HALF_UP));
                } else if (config.getExceptionSubType().getName().equalsIgnoreCase(ExceptionSubType.PRICING_BY_VALUE.getCode())) {
                    detailsStr.append(Constants.IM_LABEL_CURRENT_PRICE).append(COLON).append(currentValue);
                }

            } else if (config.getAlertTypeEntity().getName().equalsIgnoreCase(AlertType.ForecastAsOfLastNightlyOptimization.toString()) ||
                    config.getAlertTypeEntity().getName().equalsIgnoreCase(AlertType.ForecastAsOfLastOptimization.toString())) {
                detailsStr.append(Constants.IM_LABEL_CURRENT_FORECAST).append(COLON).append(new BigDecimal(currentValue).setScale(2, RoundingMode.HALF_UP));

            } else if (config.getAlertTypeEntity().getName().equalsIgnoreCase(AlertType.CompetitorPriceAsOfLastNightlyOptimization.toString())) {
                detailsStr.append(Constants.IM_LABEL_CURRENT_PRICE).append(COLON).append(new BigDecimal(currentValue).setScale(2, RoundingMode.HALF_UP));

            }

            detailsStr.append(alertTypesDetails(config, occupanctyDate, currentValue, delta, objResult, detailsComponentsCache));

            if (config.getThresholdMetricType().getCode().equalsIgnoreCase(MetricType.PERCENT.getCode())) {
                detailsStr.append(isValueZero(currentValue) ? "" : "%");
            }
            detailsStr.append(",");
            detailsStr.append(thresholdDetails(config, thresholdValue));

            detailsStr.append(losForDecisionChangeWithPricingOptionsDetails(config, objResult));

            detailsStr.append(losForDecisionAsOfWithPricingDetails(config, objResult));

            detailsStr.append(roomTypeForCompetitorPriceDetails(config, objResult, allWebrateAccomType));

            if (isCPCNotificationByRoomClassEnabled()) {
                detailsStr.append(roomClassForCompetitorPriceDetails(config, objResult));
            }
            return detailsStr.toString();
        } catch (Exception e) {
            logErrorMessage(config, new AlertEvaluationException("Unable to create details for Exception/Notification - ", e));
            return detailsStr.toString();
        }
    }

    private Map<Date, Object> bulkFetchOccupancyChangeByBusinessType(Set<Date> occupancyDates) {
        List<Object[]> resultSet = tenantCrudService.findByNativeQuery(PaceMktSegActivity.OCCUPANCY_CHANGE_AS_LAST_NIGHTLY_OPTIMIZATION_FOR_BUSINESS_TYPE_LEVEL_BY_DATES,
                QueryParameter.with("dates", occupancyDates).and("businessTypeId", 2).parameters());
        Map<Date, Object> actualValues = resultSet.stream().collect(Collectors.toMap(row -> (Date) row[2], row -> getBigDecimalValueFromResultSet(row[7])));
        occupancyDates.forEach(date -> actualValues.putIfAbsent(date, "NA"));
        return actualValues;
    }

    private Map<Date, Object> bulkFetchTransientOnBooksStly(Set<Date> occupancyDates, Date businessDate) {
        Map<Date, Date> stlyOcuppancyDates = occupancyDates.stream()
                .collect(Collectors.toMap(date -> calculateDateForLastYear(date, true), date -> date));
        final Date stlyBusinessDate = DateCalculator.calculateDateForLastYear(businessDate, true);
        final List<Object[]> resultSet = getResultSetFor(TRANSIENT_ON_BOOKS_STLY_BY_DATES,
                QueryParameter.with("occupancyDates", stlyOcuppancyDates.keySet()).and("businessDayEndDate", sqlDate(stlyBusinessDate)).parameters());
        Map<Date, Object> actualValues = resultSet.stream().collect(Collectors.toMap(row -> stlyOcuppancyDates.get((Date) row[0]), row -> getBigDecimalValueFromResultSet(row[1])));
        occupancyDates.forEach(date -> actualValues.putIfAbsent(date, "NA"));
        return actualValues;
    }

    protected List<Object[]> getResultSetFor(String occupancyChangeByBusinessType, Map<String, Object> parameterMap) {
        return tenantCrudService.findByNativeQuery(occupancyChangeByBusinessType,
                parameterMap);
    }

    private Map<Date, Object> bulkFetchSpecialEventLastYearExists(Set<Date> occupancyDates) {
        DateFormat dateFormatYYYYMMDD = DateUtil.getDateFormatYYYYMMDD();

        Map<String, Pair<Date, Date>> stlyOccupancyDateToDateMappings = occupancyDates.stream()
                .map(date -> Pair.of(date, calculateDateForLastYear(date, false)))
                .collect(Collectors.toMap(pair -> dateFormatYYYYMMDD.format(pair.getRight()), Function.identity()));

        List<Date> stlyDates = stlyOccupancyDateToDateMappings.values()
                .stream()
                .map(Pair::getRight)
                .collect(Collectors.toList());
        final List<Date> resultSet = tenantCrudService.findByNamedQuery(PropertySpecialEventInstance.CHECK_EXISTENCE_OF_EVENT_FOR_DATES,
                QueryParameter.with("dates", stlyDates).parameters());

        Map<Date, Object> actualValues = new HashMap<>();
        resultSet.forEach(date -> actualValues.put(stlyOccupancyDateToDateMappings.get(dateFormatYYYYMMDD.format(date)).getLeft(), "true"));
        occupancyDates.forEach(date -> actualValues.putIfAbsent(date, "NA"));
        return actualValues;
    }

    private BigDecimal calculateOccupancyForecastPercent(Object[] objResult) {
        BigDecimal metricValue;
        if (((Integer) objResult[1]) == 1) {
            if (objResult[10] == null || objResult[12] == null || BigDecimal.valueOf(0).compareTo((BigDecimal) objResult[12]) == 0) {
                metricValue = BigDecimal.valueOf(0);
            } else {
                final BigDecimal occupancyNbr = (BigDecimal) objResult[10];
                metricValue = occupancyNbr
                        .divide((BigDecimal) objResult[12], 4, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
            }

        } else {
            if (objResult[10] == null || objResult[9] == null || BigDecimal.valueOf(0).compareTo((BigDecimal) objResult[9]) == 0) {
                metricValue = BigDecimal.valueOf(0);
            } else {
                final BigDecimal occupancyNbr = (BigDecimal) objResult[10];
                metricValue = occupancyNbr
                        .divide((BigDecimal) objResult[9], 4, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
            }
        }
        return metricValue;
    }

    private boolean isAlertType(AlertType alertType, InformationMgrAlertConfigEntity config) {
        return StringUtils.equalsIgnoreCase(alertType.toString(), config.getAlertTypeEntity().getName());
    }

    private String getDefinedThreshold(InformationMgrAlertConfigEntity config) {
        return isAlertType(AlertType.BookingPaceEx, config) || isAlertType(AlertType.UnexpectedDemandEx, config) ? config.getTranslatedMsg() : config.getThresholdOperator();
    }

    private Object getBigDecimalValueFromResultSet(Object o) {
        return o != null ? ((BigDecimal) o).setScale(2, RoundingMode.HALF_UP) : "NA";
    }

    private Object getIntegerValueFromResultSet(Object o) {
        return o != null ? (Integer) o : "NA";
    }

    private String getStringValueFromResultSet(Object o) {
        return o != null ? (String) o : "NA";
    }

    private boolean isValueZero(String currentValue) {
        return "0.00".equals(currentValue);
    }

    public boolean isAlertTypeEnabled(String alertType) {
        InfoMgrTypeEntity alertTypeEntity = alertService.getAlertType(alertType);
        return alertTypeEntity.isEnabled();
    }

    public Integer getBusinessType(InformationMgrAlertConfigEntity config) {
        return tenantCrudService.find(BusinessType.class, config.getExceptionSubLevel()).getId();
    }

    public Integer getForecastGroup(InformationMgrAlertConfigEntity config) {
        return tenantCrudService.find(ForecastGroup.class, config.getExceptionSubLevel()).getId();
    }

    public Integer getMarketSegment(InformationMgrAlertConfigEntity config) {
        return tenantCrudService.find(MktSeg.class, config.getExceptionSubLevel()).getId();
    }

    public Integer getBusinessGroup(InformationMgrAlertConfigEntity config) {
        return tenantCrudService.find(BusinessGroup.class, config.getExceptionSubLevel()).getId();
    }


    /*
     * This method returns all the active Configurations for requested info_Mgr_type
     * enabled
     */
    @SuppressWarnings("unchecked")
    public List<InformationMgrAlertConfigEntity> findExceptionConfigurationsToProcess(String exceptionTypeName) {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("alertTypeName", exceptionTypeName);
        parameters.put(PROPERTY_ID, propertyId);
        String query = InformationMgrAlertConfigEntity.FIND_ACTIVE_BY_PROPERTY_AND_EXCEPTION_TYPE;
        if (configParamService.getBooleanParameterValue(PreProductionConfigParamName.IS_REDESIGNED_NOTIFICATION_FLOW_ENABLED.value())) {
            query = InformationMgrAlertConfigEntity.FIND_ACTIVE_BY_PROPERTY_AND_EXCEPTION_TYPE_EXCEPT_PRICING_BY_VALUE;
        }
        List<InformationMgrAlertConfigEntity> configs = tenantCrudService.findByNamedQuery(query, parameters);
        //Ignore the PACE YOY configuration
        if (!exceptionTypeName.equalsIgnoreCase(AlertType.YOYPaceChangeEx.toString())) {
            modifyConfigurationAccordingToVariableDecWindow(configs, getCaughtUpDate());
        }
        return configs;

    }

    private void modifyConfigurationAccordingToVariableDecWindow(List<InformationMgrAlertConfigEntity> configs, Date caughtUpDate) {
        Date optimizationWindowEndDateBDEVariable = dateService.getOptimizationWindowEndDateBDEVariableCheckApplyAndOverride();

        for (InformationMgrAlertConfigEntity objConfig : configs) {
            try {
                Date endDate = parseDate(objConfig.getEndDate(), caughtUpDate);

                if (optimizationWindowEndDateBDEVariable.before(endDate)) {
                    LOG.info("Varibale decision window end date is before the end date of Configuration. Configured endDate -" + endDate + " variable EndDate -" +
                            optimizationWindowEndDateBDEVariable + " for configuration " + objConfig.getAlertTypeEntity().getName());
                    objConfig.setEndDate(new SimpleDateFormat(DATE_FORMAT).format(optimizationWindowEndDateBDEVariable));
                }
            } catch (AlertEvaluationException e) {
                LOG.error("Unable to parse configuration end date.", e);
            } catch (Exception e) {
                LOG.error("Unexpected error - " + e.getMessage(), e);
            }
        }
    }

    protected void logErrorMessage(InformationMgrAlertConfigEntity config, AlertEvaluationException e) {
        StringBuilder msg = new StringBuilder("\n\n=================================\n");
        msg.append("Exception Alert config evaluation FAILED. id: ");
        msg.append(config.getId());
        msg.append("\n\tProperty ID: ");
        msg.append(config.getPropertyId());
        msg.append("\tProperty ID used by analytics builder: ");
        msg.append(PacmanWorkContextHelper.getPropertyId());
        msg.append("\n\t");
        msg.append(config.getExceptionSubType().getDescription());
        msg.append("\t");
        msg.append(config.getExceptionLevel().getDescription());
        msg.append("\t");
        msg.append(config.getExceptionSubLevel());
        msg.append("\n\tstart: ");
        msg.append(config.getStartDate());
        msg.append(", end: ");
        msg.append(config.getEndDate());
        msg.append("\t");
        msg.append("message: ");
        msg.append(e.getMessage());
        msg.append("=================================\n\n");
        LOG.warn(msg.toString());
    }

    public void setAlertService(AlertService alertService) {
        this.alertService = alertService;
    }

    private Integer getMasterAccomClassId() {
        Integer propertyId = PacmanThreadLocalContextHolder.getWorkContext().getPropertyId();
        AccomClass masterClass = (AccomClass) tenantCrudService.findByNamedQuerySingleResult(AccomClass.GET_MASTER_CLASS,
                QueryParameter.with(PROPERTY_ID, propertyId).parameters());
        if (masterClass != null) {
            return masterClass.getId();
        }
        return -1;
    }

    public String getKey(InfoMgrExcepNotifEntity excepNotifEntity) {
        StringBuilder key = new StringBuilder()
                .append(excepNotifEntity.getAlertType().getName()).append("::")
                .append(excepNotifEntity.getSubType().getName()).append("::")
                .append(excepNotifEntity.getPropertyId()).append("::")
                .append(excepNotifEntity.getExceptionAlertConfigEntityId());
        return key.toString();
    }

    public String getKey(InformationMgrAlertConfigEntity config) {
        StringBuilder key = new StringBuilder()
                .append(config.getAlertTypeEntity().getName()).append("::")
                .append(config.getExceptionSubType().getName()).append("::")
                .append(config.getPropertyId()).append("::")
                .append(config.getId());
        return key.toString();
    }

    private void addNewEntriesIntoExistingMap(List<InfoMgrHistoryEntity> historyEntities, Map<String, List<InfoMgrExcepNotifEntity>> existingExcepNotifEntityMap, List<InfoMgrExcepNotifEntity> existingList, Map<String, Object> resultMap) {
        if (null != resultMap) {
            InfoMgrHistoryEntity historyEntity = (InfoMgrHistoryEntity) resultMap.get(HISTORY);
            if (historyEntity != null) {
                historyEntities.add(historyEntity);
            }

            if (existingList == null) {
                existingList = new ArrayList<>();
            }
            InfoMgrExcepNotifEntity existingException = (InfoMgrExcepNotifEntity) resultMap.get(EXCEPTION);
            existingList.add(existingException);
            existingExcepNotifEntityMap.put(getKey(existingException), existingList);
        }
    }

    private InfoMgrExcepNotifEntity extractExistingException(InformationMgrAlertConfigEntity config, Object[] objResult, Date occupancyDate, Map<Integer, WebrateAccomType> webrateAccomTypeMap, List<InfoMgrExcepNotifEntity> existingList) {
        InfoMgrExcepNotifEntity existingException = null;
        if (existingList == null) {
            return existingException;
        }
        if (isAlertTypeDecisionChange(config.getAlertTypeEntity())
                && (config.getExceptionSubType().getName().equalsIgnoreCase(ExceptionSubType.PRICING_BY_VALUE.getCode())
                || config.getExceptionSubType().getName().equalsIgnoreCase(ExceptionSubType.PRICING.getCode())) &&
                isObjectElementValidLOS(objResult[8])) {
            String los = String.valueOf(objResult[8]);
            existingException = getExistingExceptionPricingByValue(occupancyDate, existingList, los);
        } else if (isAlertTypeDecisionAsOf(config.getAlertTypeEntity())
                && (StringUtils.equalsIgnoreCase(config.getExceptionSubType().getName(), ExceptionSubType.PRICING.getCode())
                || StringUtils.equalsIgnoreCase(config.getExceptionSubType().getName(), ExceptionSubType.PRICING_BY_VALUE.getCode()))
                && isObjectElementValidLOS(objResult[8])) {
            String los = String.valueOf(objResult[8]);
            existingException = getExistingExceptionPricingByValue(occupancyDate, existingList, los);
        } else if ((AlertType.CompetitorPriceChange.toString().equalsIgnoreCase(config.getAlertTypeEntity().getName()) ||
                AlertType.CompetitorPriceAsOfLastNightlyOptimization.toString().equalsIgnoreCase(config.getAlertTypeEntity().getName())) &&
                objResult[8] != null && (Integer) (objResult[8]) != -1) {
            WebrateAccomType webrateAccomType = webrateAccomTypeMap.get((Integer) objResult[8]);
            if (webrateAccomType != null) {
                existingException = getExistingExceptionByAccomType(occupancyDate, existingList, webrateAccomType.getWebrateAccomName());
            }
        } else {
            existingException = getExistingException(occupancyDate, existingList);
        }
        return existingException;
    }

    private int compareOccupancyDate(Date occupancyDate, List<InfoMgrExcepNotifSkipDatesEntity> listDatesToSkipFromException, InfoMgrExcepNotifEntity existingException) {
        int compareResult = -1;
        for (InfoMgrExcepNotifSkipDatesEntity objSkipDateEntity : listDatesToSkipFromException) {
            compareResult = objSkipDateEntity.getOccupancyDate().compareTo(occupancyDate);
            if (compareResult == 0) {
                break;
            }
        }
        if (existingException != null && existingException.getOccupancyDate() == null && existingException.getSkipOccupancyDate() == 1) {
            compareResult = 0;
        }
        return compareResult;
    }

    private BigDecimal evaluateDeltaByThresholdMetricTypePercent(InformationMgrAlertConfigEntity config, BigDecimal delta, String currentOptimizationValStr, String lastOptimizationValStr) {
        if (config.getThresholdMetricType() == MetricType.PERCENT) {
            BigDecimal currentOptimizationVal = new BigDecimal(currentOptimizationValStr);
            BigDecimal lastOptimizationVal = new BigDecimal(lastOptimizationValStr);

            if (config.getAlertTypeEntity().isStaticThreshold()) {
                delta = currentOptimizationVal;
            } else {
                if (lastOptimizationVal.compareTo(BigDecimal.ZERO) == 0) {
                    lastOptimizationVal = BigDecimal.ONE;
                    if (currentOptimizationVal.compareTo(BigDecimal.ZERO) == 0) {
                        currentOptimizationVal = BigDecimal.ONE;
                    }
                }
                delta = ((currentOptimizationVal.subtract(lastOptimizationVal)).divide(lastOptimizationVal, 4, RoundingMode.HALF_UP)).multiply(BIG_DECIMAL_100);
            }
        }
        return delta;
    }

    private boolean isObjectElementValidLOS(Object object) {
        return object != null && (Integer) (object) != -1;
    }

    private void lowerExistingExceptionScore(InformationMgrAlertConfigEntity config, Object[] objResult, BigDecimal threshold, BigDecimal delta, Date occupancyDate, String currentOptimizationValStr, String lastOptimizationValStr,
                                             Map<Integer, InfoMgrHistoryTypeEntity> typeMap, Map<Integer, InfoMgrStatusEntity> statusMap, List<InfoMgrHistoryEntity> historyEntities, List<InfoMgrExcepNotifEntity> infoMgrExcepNotifEntities,
                                             InformationMgrAlertConfigEntity configEntity, InfoMgrExcepNotifEntity existingException, AccomClass masterClass, AccomType rohRoomType, Map<Integer, WebrateAccomType> webrateAccomTypeMap,
                                             NotificationDetailsComponentsCache detailsComponentsCache, Date caughtUpDate, String specialEvents) {
        if (existingException != null && isExistingExceptionValid(config, objResult, existingException)) {
            LOG.debug("lowering the score of an exception that no longer exceeds its threshold. (id:"
                    + config.getId() + ")");
            existingException.setLastModificationDate(new Date());
            existingException.setCurrentOptimizationValue(currentOptimizationValStr);
            existingException.setLastOptimizationValue(lastOptimizationValStr);
            existingException.setScore(cutTheScoreInHalf(existingException));
            existingException.setStatusId(Constants.INACTIVE_STATUS_ID);
            existingException.setDetails(createDetails(config, occupancyDate, currentOptimizationValStr, delta, threshold, objResult, masterClass, rohRoomType, webrateAccomTypeMap, detailsComponentsCache, caughtUpDate, specialEvents));
            InfoMgrHistoryEntity history = null;
            if (existingException.getScore() <= 1) {
                LOG.debug("Exception score is now low enough that we're declaring it RESOLVED. (id:"
                        + config.getId() + ")");
                existingException.setScore(0);
                existingException.setAlertStatus(statusMap.get(Constants.ALERT_STATUS_RESOLVED_ID));
                history = createHistory(
                        typeMap.get(Constants.ALERT_HISTORY_RESOLVED_ID), existingException, configEntity, detailsComponentsCache);
            } else {
                history = createHistory(
                        typeMap.get(Constants.ALERT_HISTORY_SCORE_DECREASED_ID), existingException, configEntity, detailsComponentsCache);
            }
            historyEntities.add(history);
            infoMgrExcepNotifEntities.add(existingException);
        } else {
            LOG.debug("We did NOT exceed the threshold - no current exception exists. No action is being taken");
        }
    }

    private void updateExistingExceptionScore(InformationMgrAlertConfigEntity config, Object[] objResult, BigDecimal threshold, String operator, BigDecimal delta, Date occupancyDate, String currentOptimizationValStr, String lastOptimizationValStr, Map<Integer, InfoMgrHistoryTypeEntity> typeMap, Map<Integer, InfoMgrStatusEntity> statusMap,
                                              List<InfoMgrHistoryEntity> historyEntities, List<InfoMgrExcepNotifEntity> infoMgrExcepNotifEntities, InformationMgrAlertConfigEntity configEntity, InfoMgrExcepNotifEntity existingException, BigDecimal changeddelta, List<Integer> reactivateStepsOfNotifications, AccomClass masterClass, AccomType rohRoomType,
                                              Map<Integer, WebrateAccomType> webrateAccomTypeMap, NotificationDetailsComponentsCache detailsComponentsCache, Date caughtUpDate, String specialEvents) {
        if (isExistingExceptionValid(config, objResult, existingException)) {
            LOG.debug("The exception already exists, updating the score");
            existingException.setAlertStatus(statusMap.get(Constants.ALERT_STATUS_NEW_ID));
            existingException.setScore(calculateScore(existingException, threshold, changeddelta, operator));
            existingException.setLastModificationDate(new Date());
            existingException.setCurrentOptimizationValue(currentOptimizationValStr);
            existingException.setLastOptimizationValue(lastOptimizationValStr);
            existingException.setDetails(createDetails(config, occupancyDate, currentOptimizationValStr, delta, threshold, objResult, masterClass, rohRoomType, webrateAccomTypeMap, detailsComponentsCache, caughtUpDate, specialEvents));
            existingException.setStatusId(Constants.ACTIVE_STATUS_ID);
            InfoMgrHistoryEntity history = createHistory(
                    typeMap.get(Constants.ALERT_HISTORY_SCORE_INCREASED_ID), existingException, configEntity, detailsComponentsCache);
            if (existingException.getId() != null) {
                reactivateStepsOfNotifications.add(existingException.getId());
            }
            historyEntities.add(history);
            infoMgrExcepNotifEntities.add(existingException);
        }
    }

    private boolean isExistingExceptionValid(InformationMgrAlertConfigEntity config, Object[] objResult, InfoMgrExcepNotifEntity existingException) {
        return compareExceptionForDecisionChangeWithPricingByLOS(existingException, objResult, config) && compareExceptionForDecisionAsOfWithPricingByLOS(existingException, objResult, config);
    }

    private boolean compareExceptionForDecisionChangeWithPricingByLOS(InfoMgrExcepNotifEntity existingException, Object[] objResult, InformationMgrAlertConfigEntity config) {
        if (isExceptionDecisionChangePricingByLOS(existingException)) {
            if (isAlertTypeDecisionChange(config.getAlertTypeEntity())
                    && isExceptionSubTypePricing(config.getExceptionSubType())
                    && objResult[8] != null) {
                return compareLOS(existingException, objResult[8]);
            } else {
                return false;
            }
        }
        return true;
    }

    private boolean compareLOS(InfoMgrExcepNotifEntity existingException, Object object) {
        Integer los = fetchLOS(existingException);
        return ((Integer) (object)).compareTo(los) == 0;
    }

    private Integer fetchLOS(InfoMgrExcepNotifEntity existingException) {
        String[] exceptionDetails = existingException.getDetails().split(",");
        Integer los = Integer.MIN_VALUE;
        for (String exceptionDetail : exceptionDetails) {
            if (StringUtils.containsIgnoreCase(exceptionDetail, "LOS")) {
                los = NumberUtils.createInteger(StringUtils.trim(exceptionDetail.split(":")[1]));
            }
        }
        return los;
    }

    private boolean isExceptionDecisionChangePricingByLOS(InfoMgrExcepNotifEntity infoMgrExcepNotifEntity) {
        return isAlertTypeDecisionChange(infoMgrExcepNotifEntity.getAlertType())
                && isExceptionSubTypePricing(infoMgrExcepNotifEntity.getSubType())
                && StringUtils.containsIgnoreCase(infoMgrExcepNotifEntity.getDetails(), "LOS");
    }

    private boolean isExceptionSubTypePricing(InformationMgrSubTypeEntity informationMgrSubTypeEntity) {
        return StringUtils.equalsIgnoreCase(informationMgrSubTypeEntity.getName(), ExceptionSubType.PRICING.getCode());
    }

    private boolean isExceptionSubTypePricingByValue(InformationMgrSubTypeEntity informationMgrSubTypeEntity) {
        return StringUtils.equalsIgnoreCase(informationMgrSubTypeEntity.getName(), ExceptionSubType.PRICING_BY_VALUE.getCode());
    }

    private boolean isAlertTypeDecisionChange(InfoMgrTypeEntity infoMgrTypeEntity) {
        return StringUtils.equalsIgnoreCase(infoMgrTypeEntity.getName(), AlertType.DecisionChangeEx.toString())
                || StringUtils.equalsIgnoreCase(infoMgrTypeEntity.getName(), AlertType.DecisionChangeExAsOfLastOptimization.toString());
    }

    private boolean compareExceptionForDecisionAsOfWithPricingByLOS(InfoMgrExcepNotifEntity existingException, Object[] objResult, InformationMgrAlertConfigEntity config) {
        if (isExceptionDecisionAsOfPricingByLOS(existingException)) {
            if (isAlertTypeDecisionAsOf(config.getAlertTypeEntity())
                    && (isExceptionSubTypePricing(config.getExceptionSubType())
                    || isExceptionSubTypePricingByValue(config.getExceptionSubType()))
                    && objResult[8] != null) {
                return compareLOS(existingException, objResult[8]);
            } else {
                return false;
            }
        }
        return true;
    }

    private boolean isExceptionDecisionAsOfPricingByLOS(InfoMgrExcepNotifEntity infoMgrExcepNotifEntity) {
        return isAlertTypeDecisionAsOf(infoMgrExcepNotifEntity.getAlertType())
                && (isExceptionSubTypePricing(infoMgrExcepNotifEntity.getSubType())
                || isExceptionSubTypePricingByValue(infoMgrExcepNotifEntity.getSubType()))
                && StringUtils.containsIgnoreCase(infoMgrExcepNotifEntity.getDetails(), "LOS");
    }

    private boolean isAlertTypeDecisionAsOf(InfoMgrTypeEntity infoMgrTypeEntity) {
        return StringUtils.equalsIgnoreCase(infoMgrTypeEntity.getName(), AlertType.DecisionAsOfLastOptimization.toString())
                || StringUtils.equalsIgnoreCase(infoMgrTypeEntity.getName(), AlertType.DecisionAsOfLastNightlyOptimization.toString());
    }

    private StringBuilder losForDecisionChangeWithPricingOptionsDetails(InformationMgrAlertConfigEntity config, Object[] objResult) {
        StringBuilder detailsStr = new StringBuilder();
        if ((config.getAlertTypeEntity().getName().equalsIgnoreCase(AlertType.DecisionChangeEx.toString())
                || config.getAlertTypeEntity().getName().equalsIgnoreCase(AlertType.DecisionChangeExAsOfLastOptimization.toString()))
                && (config.getExceptionSubType().getName().equalsIgnoreCase(ExceptionSubType.PRICING_BY_VALUE.getCode()) ||
                config.getExceptionSubType().getName().equalsIgnoreCase(ExceptionSubType.PRICING.getCode()))) {
            int los = (Integer) objResult[8];
            if (los != -1) {
                detailsStr.append(",").append(Constants.IM_LABEL_LOS_ABBREVIATED).append(Constants.COLON).append(los);
            }
        }
        return detailsStr;
    }

    private StringBuilder losForDecisionAsOfWithPricingDetails(InformationMgrAlertConfigEntity config, Object[] objResult) {
        StringBuilder detailsStr = new StringBuilder();
        if ((StringUtils.equalsIgnoreCase(config.getAlertTypeEntity().getName(), AlertType.DecisionAsOfLastNightlyOptimization.toString())
                || StringUtils.equalsIgnoreCase(config.getAlertTypeEntity().getName(), AlertType.DecisionAsOfLastOptimization.toString()))
                && (StringUtils.equalsIgnoreCase(config.getExceptionSubType().getName(), ExceptionSubType.PRICING.getCode())
                || StringUtils.equalsIgnoreCase(config.getExceptionSubType().getName(), ExceptionSubType.PRICING_BY_VALUE.getCode()))) {
            int los = (Integer) objResult[8];
            if (los != -1) {
                detailsStr.append(",").append(Constants.IM_LABEL_LOS_ABBREVIATED).append(Constants.COLON).append(los);
            }
        }
        return detailsStr;
    }

    private StringBuilder decisionChangeDetails(InformationMgrAlertConfigEntity config, String currentValue, BigDecimal delta) {
        StringBuilder detailsStr = new StringBuilder();
        if (config.getExceptionSubType().getName().equalsIgnoreCase(ExceptionSubType.OVERBOOKING.getCode())) {

            detailsStr.append(Constants.IM_LABEL_CURRENT_OVERBOOKING).append(COLON).append(new BigDecimal(currentValue).setScale(2, RoundingMode.HALF_UP)).append(",");
            detailsStr.append(Constants.IM_LABEL_OVERBOOKING_CHANGE).append(COLON).append(delta);

        } else {

            if (config.getExceptionSubType().getName().equalsIgnoreCase(ExceptionSubType.LRV.getCode())) {
                detailsStr.append(Constants.IM_LABEL_CURRENT_LRV).append(COLON).append(new BigDecimal(currentValue).setScale(2, RoundingMode.HALF_UP)).append(",");
                detailsStr.append(Constants.IM_LABEL_LRV).append(" ").append(Constants.IM_LABEL_CHANGE).append(COLON).append(delta);

            } else {
                detailsStr.append(Constants.IM_LABEL_CURRENT_PRICE).append(COLON).append(currentValue).append(",");
                detailsStr.append(Constants.IM_LABEL_PRICING_CHANGE).append(COLON).append(delta);
            }

        }
        return detailsStr;
    }

    private StringBuilder exceptionLevelNotPropertyDetails(InformationMgrAlertConfigEntity config, AccomClass masterClassForProperty, AccomType rohRoomTypeForProperty) {
        StringBuilder detailsStr = new StringBuilder();
        if (!AlertType.CompetitorPriceChange.name().equalsIgnoreCase(config.getAlertTypeEntity().getName())
                && !isAlertType(AlertType.BookingPaceEx, config) && !isAlertType(AlertType.UnexpectedDemandEx, config)) {

            detailsStr.append(config.getExceptionLevel().getDescription()).append(COLON);
            String subLevel = getSubLevelForId(config).trim();
            detailsStr.append(subLevel);
            if (subLevel.equalsIgnoreCase(SubLevelType.MASTER_CLASS.getCode())) {
                AccomClass objAccomClass = masterClassForProperty;
                if (null != objAccomClass) {
                    detailsStr.append(" (").append(objAccomClass.getCode()).append(")");
                }
            } else if (subLevel.equalsIgnoreCase(SubLevelType.ROH_ROOM_TYPE.getCode())) {
                AccomType objAccomType = rohRoomTypeForProperty;
                if (null != objAccomType) {
                    detailsStr.append(" (").append(objAccomType.getAccomTypeCode()).append(")");
                }
            }
            detailsStr.append(",");
        }
        return detailsStr;
    }

    private StringBuilder metricDetails(InformationMgrAlertConfigEntity config) {
        StringBuilder detailsStr = new StringBuilder();
        if (AlertType.CompetitorPriceChange.name().equalsIgnoreCase(config.getAlertTypeEntity().getName())) {
            detailsStr.append(IM_LABEL_METRIC).append(COLON).append("Competitor").append(",");
        } else {
            detailsStr.append(IM_LABEL_METRIC).append(COLON).append(config.getExceptionSubType().getDescription()).append(",");
        }
        return detailsStr;
    }

    private StringBuilder yoyPaceChangeDetails(InformationMgrAlertConfigEntity config, SimpleDateFormat sdf, Date caughtUpDate) throws AlertEvaluationException {
        StringBuilder detailsStr = new StringBuilder();
        if (StringUtils.containsIgnoreCase(config.getStartDate(), "TODAY")) {
            final Date startDate = parseDate(config.getStartDate(), caughtUpDate);
            final Date endDate = parseDate(config.getEndDate(), caughtUpDate);

            config.setStartDate(DateUtil.formatDate(startDate, DATE_FORMAT));
            config.setEndDate(DateUtil.formatDate(endDate, DATE_FORMAT));
        }
        try {
            detailsStr.append(Constants.IM_LABEL_TIME_FRAME).append(COLON).append(Constants.IM_DATE_IDENTIFICATION_CODE).append(Constants.DECISION_FILE_NAME_SEPRATOR).append(sdf.format(DateUtil.parseDate(config.getStartDate(), DATE_FORMAT))).append(Constants.DECISION_FILE_NAME_SEPRATOR).append(Constants.IM_DATE_IDENTIFICATION_CODE)
                    .append("  ").append(Constants.IM_DATE_IDENTIFICATION_CODE).append(Constants.DECISION_FILE_NAME_SEPRATOR).append(sdf.format(DateUtil.parseDate(config.getEndDate(), DATE_FORMAT))).append(Constants.DECISION_FILE_NAME_SEPRATOR).append(Constants.IM_DATE_IDENTIFICATION_CODE).append(",");
        } catch (ParseException e) {
            throw new AlertEvaluationException("Unable to parse dateString: " + config.getStartDate() + ". We expected MM/dd/yyyy");
        }
        return detailsStr;
    }

    private StringBuilder roomTypeForCompetitorPriceDetails(InformationMgrAlertConfigEntity config, Object[] objResult, Map<Integer, WebrateAccomType> allWebrateAccomType) {
        StringBuilder detailsStr = new StringBuilder();
        if (AlertType.CompetitorPriceChange.name().equalsIgnoreCase(config.getAlertTypeEntity().getName()) ||
                AlertType.CompetitorPriceAsOfLastNightlyOptimization.name().equalsIgnoreCase(config.getAlertTypeEntity().getName())) {
            int accomTypeId = (Integer) objResult[8];
            if (accomTypeId != -1) {
                detailsStr.append(",").append(Constants.IM_LABEL_ROOM_TYPE).append(COLON).append(allWebrateAccomType.get(accomTypeId).getWebrateAccomName());
            }
        }
        return detailsStr;
    }

    private StringBuilder thresholdDetails(InformationMgrAlertConfigEntity config, String thresholdValue) {
        StringBuilder detailsStr = new StringBuilder();
        detailsStr.append(Constants.IM_LABEL_THRESHOLD).append(COLON).append(getDefinedThreshold(config)).append(" ")
                .append(isAlertType(AlertType.BookingPaceEx, config) || isAlertType(AlertType.UnexpectedDemandEx, config) ? "" : thresholdValue);

        //append % for Threshold if configuration is for %
        if (detailsStr.toString().contains(Constants.IM_LABEL_THRESHOLD) &&
                config.getThresholdMetricType().getCode().equalsIgnoreCase(MetricType.PERCENT.getCode())) {
            detailsStr.append("%");
        }
        return detailsStr;
    }

    private StringBuilder alertTypesDetails(InformationMgrAlertConfigEntity config, Date occupanctyDate, String currentValue, BigDecimal delta, Object[] objResult, NotificationDetailsComponentsCache detailsComponentsCache) {
        StringBuilder detailsStr = new StringBuilder();
        if (config.getAlertTypeEntity().getName().equalsIgnoreCase(AlertType.DecisionChangeEx.toString())
                || config.getAlertTypeEntity().getName().equalsIgnoreCase(AlertType.DecisionChangeExAsOfLastOptimization.toString())) {
            detailsStr.append(decisionChangeDetails(config, currentValue, delta));

        } else if (config.getAlertTypeEntity().getName().equalsIgnoreCase(AlertType.ForecastChangeEx.toString())
                || config.getAlertTypeEntity().getName().equalsIgnoreCase(AlertType.ForecastChangeFromLastOptimization.toString())) {

            detailsStr.append(Constants.IM_LABEL_CURRENT_FORECAST).append(COLON).append(new BigDecimal(currentValue).setScale(2, RoundingMode.HALF_UP)).append(",");
            detailsStr.append(Constants.IM_LABEL_FORECAST_CHANGE).append(COLON).append(delta);

        } else if (AlertType.CompetitorPriceChange.name().equalsIgnoreCase(config.getAlertTypeEntity().getName())) {
            detailsStr.append(Constants.IM_LABEL_COMPETITOR).append(COLON).append(getSubLevelForId(config)).append(",");
            detailsStr.append(Constants.IM_LABEL_CURRENT_PRICE).append(COLON).append(isValueZero(currentValue) ? "Closed" : new BigDecimal(currentValue).setScale(2, RoundingMode.HALF_UP)).append(",");
            detailsStr.append(Constants.IM_LABEL_PRICE_CHANGE).append(COLON).append(isValueZero(currentValue) ? "N/A" : delta);
        } else if (config.getAlertTypeEntity().getName().equalsIgnoreCase(AlertType.YOYPaceChangeEx.toString())) {
            detailsStr.append(Constants.IM_LABEL_CURRENT_VALUE).append(COLON).append(new BigDecimal(currentValue).setScale(2, RoundingMode.HALF_UP)).append(",");
            detailsStr.append(Constants.IM_LABEL_YOY_CHANGE).append(COLON).append(delta);
        } else if (config.getAlertTypeEntity().getName().equalsIgnoreCase(AlertType.OccupancyChangeEx.toString())
                || config.getAlertTypeEntity().getName().equalsIgnoreCase(AlertType.OccupancyChangeAsOfLastOptimization.toString())) {
            detailsStr.append(Constants.IM_LABEL_CURRENT_VALUE).append(COLON).append(new BigDecimal(currentValue).setScale(2, RoundingMode.HALF_UP)).append(",");
            detailsStr.append(Constants.IM_LABEL_OCCUPANY_CHANGE).append(COLON).append(delta);
        } else if (isAlertType(AlertType.BookingPaceEx, config) || isAlertType(AlertType.UnexpectedDemandEx, config)) {
            //Calculation of Occupancy Forecast
            detailsStr.append(Constants.IM_LABEL_OCCUPANCY_FORECAST).append(COLON).append(calculateOccupancyForecastPercent(objResult).setScale(2)).append("%,");
            detailsStr.append(Constants.IM_LABEL_LRV).append(COLON).append(getBigDecimalValueFromResultSet(objResult[13])).append(",");
            detailsStr.append(Constants.IM_LABEL_TRANSIENT_ON_BOOKS).append(COLON).append(getIntegerValueFromResultSet(objResult[3])).append(",");
            detailsStr.append(Constants.IM_LABEL_TRANSIENT_ON_BOOKS_STLY).append(COLON).append(detailsComponentsCache.fetchTransientOnBooksStly(occupanctyDate)).append(",");
            detailsStr.append(Constants.IM_LABEL_EXPECTED_RANGE).append(COLON).append(getBigDecimalValueFromResultSet(objResult[4])).append(" - ")
                    .append(getBigDecimalValueFromResultSet(objResult[6])).append(",");
            detailsStr.append(Constants.IM_LABEL_OCCUPANY_CHANGE).append(COLON).append(detailsComponentsCache.fetchOccupancyChangeByBusinessType(occupanctyDate))
                    .append(",");
            detailsStr.append(Constants.IM_LABEL_SPECIAL_EVENT_THIS_YEAR).append(COLON).append(getStringValueFromResultSet(objResult[14])).append(",");
            detailsStr.append(Constants.IM_LABEL_SPECIAL_EVENT_LAST_YEAR).append(COLON).append(detailsComponentsCache.specialEventLastYearExists(occupanctyDate));
        }
        return detailsStr;
    }

    private StringBuilder dateDetails(InformationMgrAlertConfigEntity config, Date occupanctyDate, SimpleDateFormat dateFormat, Date caughtUpDate) throws AlertEvaluationException {
        StringBuilder detailsStr = new StringBuilder();
        if (config.getAlertTypeEntity().getName().equalsIgnoreCase(AlertType.YOYPaceChangeEx.toString())) {
            detailsStr.append(yoyPaceChangeDetails(config, dateFormat, caughtUpDate));
        } else {
            detailsStr.append(Constants.IM_LABEL_OCCUPANCY_DATE).append(COLON).append(Constants.IM_DATE_IDENTIFICATION_CODE).append(Constants.DECISION_FILE_NAME_SEPRATOR).append(dateFormat.format(occupanctyDate)).append(Constants.DECISION_FILE_NAME_SEPRATOR).append(Constants.IM_DATE_IDENTIFICATION_CODE).append(",");
        }
        return detailsStr;
    }

    private StringBuilder exceptionLevelDetails(InformationMgrAlertConfigEntity config, AccomClass masterClassForProperty, AccomType rohRoomTypeForProperty) {
        StringBuilder detailsStr = new StringBuilder();
        if (config.getExceptionLevel().getName().equalsIgnoreCase(LevelType.PROPERTY.getCode())) {
            detailsStr.append(Constants.IM_LABEL_LEVEL).append(COLON).append(config.getExceptionLevel().getDescription().toLowerCase()).append(",");
        } else {
            detailsStr.append(exceptionLevelNotPropertyDetails(config, masterClassForProperty, rohRoomTypeForProperty));
        }
        return detailsStr;
    }

    private boolean isThresholdIncreasedOrDecreasedByDelta(BigDecimal thresholdValue, BigDecimal delta) {
        if (delta.compareTo(thresholdValue) >= 0) {
            return true;
        } else if (delta.compareTo(BigDecimal.ZERO) <= 0) {
            return delta.abs().compareTo(thresholdValue) >= 0;
        } else {
            return false;
        }
    }

    private boolean isThresholdNotEqualToDelta(BigDecimal thresholdValue, BigDecimal delta) {
        if (delta.compareTo(thresholdValue) > 0) {
            return true;
        } else if (delta.compareTo(BigDecimal.ZERO) <= 0) {
            return delta.abs().compareTo(thresholdValue) > 0;
        } else {
            return false;
        }
    }

    private boolean isThresholdLessThanOrEqualToDelta(BigDecimal thresholdValue, BigDecimal delta, InformationMgrAlertConfigEntity config) {
        if (config.getAlertTypeEntity().isStaticThreshold()) {
            if (config.getExceptionSubType().getName().equalsIgnoreCase(ExceptionSubType.PRICING.getCode())) {
                return delta.abs().compareTo(thresholdValue) >= 0;
            } else {
                return delta.abs().compareTo(thresholdValue) <= 0;
            }
        } else {
            if (delta.compareTo(BigDecimal.ZERO) <= 0) {
                return delta.abs().compareTo(thresholdValue) >= 0;
            } else {
                return false;
            }
        }
    }

    private boolean isThresholdGreaterOrEqualToDelta(BigDecimal thresholdValue, BigDecimal delta, InformationMgrAlertConfigEntity config) {
        if ((config.getAlertTypeEntity().getName().equalsIgnoreCase(AlertType.DecisionAsOfLastNightlyOptimization.name())
                || config.getAlertTypeEntity().getName().equalsIgnoreCase(AlertType.DecisionAsOfLastOptimization.name()))
                && config.getExceptionSubType().getName().equalsIgnoreCase(ExceptionSubType.PRICING.getCode())) {
            return delta.compareTo(thresholdValue) <= 0;
        } else {
            return delta.compareTo(thresholdValue) >= 0;
        }
    }

    public void setConfigParamService(PacmanConfigParamsService configParamService) {
        this.configParamService = configParamService;
    }

    /*
     * calculate the delta between two numbers expressed as a percentage (ie 100
     * --> 110 = 10.0 100 --> 90 = -10.0
     */
    public BigDecimal calculatePercentageChanged(BigDecimal before, BigDecimal after) {

        BigDecimal percentageChanged = after.divide(before, 3, RoundingMode.HALF_UP);
        percentageChanged = percentageChanged.subtract(BigDecimal.ONE);
        percentageChanged = percentageChanged.multiply(BIG_DECIMAL_100);
        return percentageChanged;
    }

    public List<Integer> getCompetitorIds(Integer subLevel) {
        List<Integer> listCompetitorIds = new ArrayList<>();
        WebrateCompetitors objCompetitor = tenantCrudService.find(WebrateCompetitors.class, subLevel);
        if (null != objCompetitor) {
            listCompetitorIds.add(objCompetitor.getId());
        }
        return listCompetitorIds;
    }

    public AccomClass getAccomClassForConfiguration(InformationMgrAlertConfigEntity config, int propertyID) {
        AccomClass objAccomClass = null;

        if (config.isSubLevelKeywordUsed()) {
            InformationMgrSubLevelEntity subLevelEntity = getSubLevelEntity(config.getExceptionSubLevel());
            if (null != subLevelEntity && subLevelEntity.getDescription().equalsIgnoreCase(SubLevelType.MASTER_CLASS.getCode())) {
                objAccomClass = (AccomClass) tenantCrudService.findByNamedQuerySingleResult(AccomClass.GET_MASTER_CLASS,
                        QueryParameter.with(PROPERTY_ID, propertyID)
                                .parameters()
                );
                if (null == objAccomClass) {
                    LOG.warn("Unable to find the Master class for property " + propertyID);
                }
            }
        } else {
            objAccomClass = tenantCrudService.find(AccomClass.class, config.getExceptionSubLevel());
        }
        return objAccomClass;
    }

    private InformationMgrSubLevelEntity getSubLevelEntity(Integer id) {
        return tenantCrudService.find(InformationMgrSubLevelEntity.class, id);
    }

    public Integer setLos() {
        String barDecisionValue = configParamService.getParameterValue(IPConfigParamName.BAR_BAR_DECISION.value());
        String maxLOS = configParamService.getParameterValue(IPConfigParamName.BAR_MAX_LOS.value());
        return StringUtils.equals(barDecisionValue, Constants.BAR_DECISION_VALUE_LOS) ? NumberUtils.toInt(maxLOS, 1) : -1;
    }

    public AccomType getAccomTypeForConfiguration(InformationMgrAlertConfigEntity config) {
        AccomType objAccomType = null;
        if (config.isSubLevelKeywordUsed()) {
            InformationMgrSubLevelEntity subLevelEntity = getSubLevelEntity(config.getExceptionSubLevel());
            if (subLevelEntity.getDescription().equalsIgnoreCase(SubLevelType.ROH_ROOM_TYPE.getCode())) {
                objAccomType = (AccomType) tenantCrudService.findByNamedQuerySingleResult(AccomType.GET_ROH_TYPE, QueryParameter.with(PROPERTY_ID, config.getPropertyId()).parameters());
            } else {
                LOG.warn("Unable to get the ROH Room Type for property - " + config.getPropertyId());
            }
        } else {
            objAccomType = tenantCrudService.find(AccomType.class, config.getExceptionSubLevel());
        }
        return objAccomType;
    }


    public List<String> getWebrateGenerationDate() {
        String businessDate = DateUtil.sqlDate(dateService.getBusinessDate());

        Integer stalenessThreshold = null;
        if (configParamService.getBooleanParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())) {
            stalenessThreshold = Integer.valueOf(configParamService
                    .getParameterValue(IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value()));


            String query = "select MIN(Webrate_GenerationDate), 'dummy' as DummyColumn  from Webrate wb " +
                    " inner join Webrate_Source_Property wsp on (wb.Webrate_Source_Property_ID= wsp.Webrate_Source_Property_ID) " +
                    " where wsp.Property_ID =:propertyId and Webrate_GenerationDate >= DATEADD(day,-:staleness,:businessDate)";

            return tenantCrudService.findByNativeQuery(query,
                    QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and(BUSINESS_DATE, businessDate).and("staleness", stalenessThreshold).parameters(),
                    row -> {
                        if (null != row[0]) {
                            return (row[0]).toString();
                        } else {
                            return null;
                        }
                    }
            );
        }
        return Collections.emptyList();
    }

    public boolean isCPCNotificationByRoomClassEnabled() {
        return configParamService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_CPC_NOTIFICATION_BY_ROOM_CLASS);
    }

    public boolean isProductEnabledForCompetitorNotification() {
        return configParamService == null ? Boolean.FALSE : configParamService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRODUCT_DIMENSION_FOR_COMPETITIVE_NOTIFICATIONS);
    }

    private Integer getAccomClassId(String additionalConditions) {
        int accomClassId = -1;
        if (StringUtils.isEmpty(additionalConditions)) {
            return accomClassId;
        }
        List<String> conditionsList = Arrays.asList(additionalConditions.split(ADDITIONAL_CONDITIONS_SEPARATOR));
        String roomClassCondition = conditionsList.stream().filter(condition ->
                        condition.contains(ADDITIONAL_CONDITION_PARAM_ROOM_CLASS_ID))
                .findFirst().orElse(null);
        if (StringUtils.isNotEmpty(roomClassCondition)) {
            List<String> rc = Arrays.asList(roomClassCondition.split(ADDITIONAL_CONDITIONS_PARAM_VALUE_SEPARATOR));
            accomClassId = CollectionUtils.isNotEmpty(rc) && rc.size() == 2 ? Integer.parseInt(rc.get(1)) : -1;
        }
        return accomClassId;
    }


    private StringBuilder roomClassForCompetitorPriceDetails(InformationMgrAlertConfigEntity config, Object[] objResult) {
        StringBuilder detailsStr = new StringBuilder();
        if (AlertType.CompetitorPriceChange.name().equalsIgnoreCase(config.getAlertTypeEntity().getName()) ||
                AlertType.CompetitorPriceAsOfLastNightlyOptimization.name().equalsIgnoreCase(config.getAlertTypeEntity().getName())) {
            int accomClassId = (Integer) objResult[9];
            if (accomClassId != -1) {
                detailsStr.append(",").append(Constants.IM_LABEL_ROOM_CLASS).append(COLON).append(getAccomClassNameById(config.getPropertyId(), accomClassId));
            }
        }
        return detailsStr;
    }

    public void setSpecialEventService(SpecialEventService specialEventService) {
        this.specialEventService = specialEventService;
    }

    private String getAccomClassNameById(Integer propertyId, int accomClassId) {
        Object accomClassName = multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId,
                AccomClass.GET_NAME,
                QueryParameter.with("accomClassId", accomClassId).parameters());
        return accomClassName != null ? String.valueOf(accomClassName) : StringUtils.EMPTY;
    }
}
