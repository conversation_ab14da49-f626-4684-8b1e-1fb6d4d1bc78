package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.VirtualPropertyMappingDTO;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@Transactional
public class VirtualPropertyMappingDataService {

    @GlobalCrudServiceBean.Qualifier
	@Qualifier("globalCrudServiceBean")
    @Autowired
	private CrudService globalCrudService;


    public Boolean loadVirtualPropertyMappingDataIntoPacman(List<VirtualPropertyMappingDTO> data) {

        boolean isDataSaved = false;
        StringBuilder query = new StringBuilder();

        query.append("update Property set Virtual_Property_Display_Code = '" + data.get(0).getVirtualPropertyCode() + "', Is_Virtual_Property = 1 where Property_id=11032;");
        data.forEach(vpm -> {

            query.append("INSERT [dbo].[Virtual_Property_Mapping]  " +
                    "([Virtual_Property_Id], [Physical_Property_Code], [Is_Primary], [Created_DTTM], [Last_Updated_DTTM], [Sequence_Number], [Created_By_User_ID], [Last_Updated_By_User_ID], [Parallel_Processing_Enabled], [External_System], [Brand_Code], [Global_Area], [Recovery_State], [Is_Opera_Configuration], [Is_LDB], [First_Extract_Date], [Real_Data_Start_Date], [Is_Extended_Stay], [Webrate_Source], [cp_migration_date]) \n" +
                    "VALUES(");
            query.append("" + vpm.getVirtualPropertyDatafeedID() + "");
            query.append("," + "'" + vpm.getActualPropertyInnCode() + "',0, CAST(N'2022-04-07T11:59:36.697' AS DateTime), CAST(N'2022-04-07T11:59:36.697' AS DateTime), 1, 1, 1, 0, N'ANY', N'BRAND_CODE', N'GLOBAL_AREA', N'ANY', 0, 0, CAST(N'2022-04-07' AS Date), CAST(N'2022-04-07' AS Date), 0, NULL, CAST(N'2022-04-07' AS Date));");


        });

        if (!query.toString().isEmpty()) {
            globalCrudService.executeUpdateByNativeQuery(query.toString());
            isDataSaved = true;
        }
        return isDataSaved;
    }

    public Boolean deleteData() {
        StringBuilder query = new StringBuilder();
        query.append("Delete from [dbo].[Virtual_Property_Mapping] where [Physical_Property_Code] IN ('DPGP01','SPGP01');");
        query.append("update [dbo].[Property] set Virtual_Property_Display_Code = null, Is_Virtual_Property = 0 where Property_id=11032");

        globalCrudService.executeUpdateByNativeQuery(query.toString());
        return true;
    }
}
