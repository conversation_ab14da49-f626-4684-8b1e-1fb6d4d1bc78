package com.ideas.tetris.pacman.services.fplos.batchupdate;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Joiner;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.util.jdbc.NamedParameterStatement;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.IntStream;

import static com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig.useWindowFunctionForFPLOSBatchRateAdjustmentQueryOptimization;
import static com.ideas.tetris.pacman.services.ratepopulation.QualifiedRateService.RATE_QUALIFIED_ADJUSTMENT;
import static com.ideas.tetris.pacman.services.ratepopulation.QualifiedRateService.RATE_QUALIFIED_ADJUSTMENT_FINAL;
import static org.apache.commons.lang.StringUtils.EMPTY;

public class SRPBasedDecisionQualifiedFPLOSBatchUpdater implements IDecisionQualifiedFPLOSBatchUpdater {
    private static final String LV0 = "_lv0";
    protected static final String PROPERTY_ID = "propertyId";
    protected static final String DECISION_ID = "decisionId";

    private static final Logger LOGGER = Logger.getLogger(SRPBasedDecisionQualifiedFPLOSBatchUpdater.class);
    static final int LRA_ENABLED_ALL_BAR_RATES_IMPACT = 2;
    public static final String UPDATE = "update #";
    public static final String FROM = "from #";
    public static final String TABLE_RATE_QUALIFIED_FIXED_DETAILS = "Rate_Qualified_Fixed_Details";
    private static final String TABLE_RATE_QUALIFIED_DETAILS = "Rate_Qualified_Details";
    private static final String TABLE_RATE_QUALIFIED_FIXED = "Rate_Qualified_Fixed";
    private static final String TABLE_RATE_QUALIFIED = "Rate_Qualified";

    protected NamedParameterStatement generateFplosStmt;

    protected static final String SPECIAL_SRP_ID = "specialSrpId";
    protected static final String START_DATE = "startDate";
    protected static final String END_DATE = "endDate";
    protected static final String EXTENDED_STAY = "extendedStay";
    protected static final String DEFAULT_ACCOM_TYPE_ID = "defaultAccomTypeId";
    static final String YIELDABLE_COST = "YieldableCost";
    static final String YIELDABLE_VALUE = "YieldableValue";

    private static final String LV0_UNQUALIFIED_ID = "lv0UnqualifiedId";
    private static final String MASTER_CLASS_ID = "masterClassId";
    private static final String RATE_QUALIFIED_IDS = "rateQualifiedIds";
    private final int lraEnabledValue;
    private final int lv0QualifiedId;
    private final boolean isContinuousPricingEnabled;
    protected final boolean isDerivedRatePlanEnabled;
    protected final boolean derivedRatesFPLOSCorrectionEnabled;
    protected final boolean isCorrectedFplosForDerivedRatesAndBarByLOS;
    private final boolean isBarByLos;
    private final Integer barMaxLos;
    private final boolean isRestrictHighestBarEnabled;
    protected final boolean isServicingCostEnabledAndConfigured;
    protected int propertyId;
    private boolean forceLegacyCardinalityEstimator;
    private boolean srpFplosAtTotalLevel;
    private boolean isLimitTotalSRPRatesEnabled;
    private final EffectiveLRVQueryBuilder effectiveLRVQueryBuilder;
    protected final ServicingCostQueryBuilder servicingCostQueryBuilder;
    private final AgileProductQualifiedFPLOSQueryBuilder agileProductQualifiedFPLOSQueryBuilder;
    private final ChannelRestrictionsAdjQueryBuilder channelRestrictionsAdjQueryBuilder = new ChannelRestrictionsAdjQueryBuilder();
    private List<Integer> rateQualifiedIds;
    private boolean generateAgileProductQualifiedFPLOS;
    private List<Integer> hospitalityRooms;
    private boolean isChannelRestrictionsAdjustmentsEnabled;
    protected Map<String, Integer> physicalPropertyMasterClassMap;
    protected boolean usePhysicalPropertyMasterClass;
    private final boolean useFixedRateQualifiedTableEnabled;
    private boolean excludeYieldableValueAdjustment;
    private boolean honorYieldAsYieldableCostAdjustment;


    public SRPBasedDecisionQualifiedFPLOSBatchUpdater(DecisionQualifiedFPLOSBatchUpdaterBean decisionQualifiedFPLOSBatchUpdaterBean) {

        this.lraEnabledValue = decisionQualifiedFPLOSBatchUpdaterBean.getLraEnabledValue();
        this.isContinuousPricingEnabled = decisionQualifiedFPLOSBatchUpdaterBean.isContinuousPricingEnabled();
        this.isDerivedRatePlanEnabled = decisionQualifiedFPLOSBatchUpdaterBean.isDerivedRatePlanEnabled();
        derivedRatesFPLOSCorrectionEnabled = decisionQualifiedFPLOSBatchUpdaterBean.isDerivedRatesFPLOSCorrectionEnabled();
        this.isBarByLos = decisionQualifiedFPLOSBatchUpdaterBean.isBarByLos();
        this.barMaxLos = decisionQualifiedFPLOSBatchUpdaterBean.getBarMaxLos();
        this.isRestrictHighestBarEnabled = decisionQualifiedFPLOSBatchUpdaterBean.isRestrictHighestBarEnabled();
        this.isServicingCostEnabledAndConfigured = decisionQualifiedFPLOSBatchUpdaterBean.isServicingCostEnabledAndConfigured();
        this.propertyId = decisionQualifiedFPLOSBatchUpdaterBean.getPropertyId();
        this.forceLegacyCardinalityEstimator = decisionQualifiedFPLOSBatchUpdaterBean.isForceLegacyCardinalityEstimation();
        this.srpFplosAtTotalLevel = decisionQualifiedFPLOSBatchUpdaterBean.isSrpFplosAtTotalLevel();
        this.lv0QualifiedId = decisionQualifiedFPLOSBatchUpdaterBean.getLV0QualifiedId();
        this.isLimitTotalSRPRatesEnabled = decisionQualifiedFPLOSBatchUpdaterBean.isLimitTotalSRPRatesEnabled();
        this.generateAgileProductQualifiedFPLOS = decisionQualifiedFPLOSBatchUpdaterBean.shouldGenerateAgileProductQualifiedFPLOS();
        isCorrectedFplosForDerivedRatesAndBarByLOS = derivedRatesFPLOSCorrectionEnabled && isDerivedRatePlanEnabled && isBarByLos;
        this.rateQualifiedIds = decisionQualifiedFPLOSBatchUpdaterBean.getRateQualifiedIds();
        this.hospitalityRooms = decisionQualifiedFPLOSBatchUpdaterBean.getHospitalityRooms();
        this.isChannelRestrictionsAdjustmentsEnabled = decisionQualifiedFPLOSBatchUpdaterBean.isChannelRestrictionsAdjustmentsEnabled();
        this.useFixedRateQualifiedTableEnabled = decisionQualifiedFPLOSBatchUpdaterBean.isUseFixedRateQualifiedTableEnabled();
        this.physicalPropertyMasterClassMap = decisionQualifiedFPLOSBatchUpdaterBean.getPhysicalPropertyMasterClassMap();
        this.usePhysicalPropertyMasterClass = decisionQualifiedFPLOSBatchUpdaterBean.isUsePhysicalPropertyMasterClass();
        LOGGER.info("propertyId = [" + propertyId + "], maxLOS = [" + decisionQualifiedFPLOSBatchUpdaterBean.getMaxLOS() + "], isSrpFplosAtTotalLevel = [" + srpFplosAtTotalLevel + "], lraEnabledValue = [" + lraEnabledValue + "], isContinuousPricingEnabled = [" + isContinuousPricingEnabled + "], isDerivedRatePlanEnabled = [" + isDerivedRatePlanEnabled + "], isBarByLos = [" + isBarByLos + "], barMaxLos = [" + barMaxLos + "], isServicingCostEnabledAndConfigured: [" + isServicingCostEnabledAndConfigured + " ], isChannelRestrictionsAdjustmentsEnabled: [" + isChannelRestrictionsAdjustmentsEnabled + "], usePhysicalPropertyMasterClass: [" + usePhysicalPropertyMasterClass + "] ");
        effectiveLRVQueryBuilder = new EffectiveLRVQueryBuilder();
        servicingCostQueryBuilder = new ServicingCostQueryBuilder(isServicingCostEnabledAndConfigured, propertyId);
        agileProductQualifiedFPLOSQueryBuilder = new AgileProductQualifiedFPLOSQueryBuilder(propertyId, isLimitTotalSRPRatesApplicable(), true, decisionQualifiedFPLOSBatchUpdaterBean.isConsiderRestrictionSeasonDatesForAgileQualifiedFPLOS(), isChannelRestrictionsAdjustmentsEnabled, useFixedRateQualifiedTableEnabled, this.isServicingCostEnabledAndConfigured);
        excludeYieldableValueAdjustment = decisionQualifiedFPLOSBatchUpdaterBean.isExcludeYieldableValueAdjustment();
        honorYieldAsYieldableCostAdjustment = decisionQualifiedFPLOSBatchUpdaterBean.isHonorYieldAsYieldableCostAdjustment();
        if (srpFplosAtTotalLevel) {
            initializeSrpFplosAtTotalLevel(decisionQualifiedFPLOSBatchUpdaterBean.getConnection(), decisionQualifiedFPLOSBatchUpdaterBean.getPropertyId(), decisionQualifiedFPLOSBatchUpdaterBean.getMaxLOS());
        } else if (isLimitTotalSRPRatesEnabled) {
            initializeSrpFplosAtTotalAndAccomTypeLevel(decisionQualifiedFPLOSBatchUpdaterBean.getConnection(), decisionQualifiedFPLOSBatchUpdaterBean.getPropertyId(), decisionQualifiedFPLOSBatchUpdaterBean.getMaxLOS());
        } else {
            initializeFplosAtAcomTypeLevel(decisionQualifiedFPLOSBatchUpdaterBean.getConnection(), decisionQualifiedFPLOSBatchUpdaterBean.getPropertyId(), decisionQualifiedFPLOSBatchUpdaterBean.getMaxLOS());
        }
    }

    private void initializeSrpFplosAtTotalLevel(Connection connection, int propertyId, int maxLOS) {
        String tableName = "temp_qualified_fplos_pid_" + propertyId;
        String queries = generateQueriesFplosBySRP(tableName, maxLOS);
        queries = updateQueryForHospitalityRoomsClause(queries);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Queries:\n" + queries);
        }
        try {
            generateFplosStmt = new NamedParameterStatement(connection, queries);
        } catch (SQLException se) {
            throw new TetrisException(ErrorCode.DATABASE_EXCEPTION, "Failed to build prepartStatement: " + queries, se);
        }
    }

    private void initializeFplosAtAcomTypeLevel(Connection connection, int propertyId, int maxLOS) {
        String tableName = "temp_qualified_fplos_pid_" + propertyId;
        String queries = generateQueries(tableName, maxLOS);
        queries = updateQueryForHospitalityRoomsClause(queries);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Queries:\n" + queries);
        }
        try {
            generateFplosStmt = new NamedParameterStatement(connection, queries);

        } catch (SQLException se) {
            throw new TetrisException(ErrorCode.DATABASE_EXCEPTION, "Failed to build prepartStatement: " + queries, se);
        }
    }

    private void initializeSrpFplosAtTotalAndAccomTypeLevel(Connection connection, int propertyId, int maxLOS) {
        String roomTypeLevelTableName = "temp_qualified_fplos_RT_pid_" + propertyId;
        String totalLevelTableName = "temp_qualified_fplos_LT_pid_" + propertyId;
        String queries = generateQueries(roomTypeLevelTableName, maxLOS) + generateQueriesFplosBySRP(totalLevelTableName, maxLOS);
        queries = updateQueryForHospitalityRoomsClause(queries);

        if (LOGGER.isDebugEnabled()) {
            LOGGER.info("Queries:\n" + queries);
        }
        try {
            generateFplosStmt = new NamedParameterStatement(connection, queries);
        } catch (SQLException se) {
            throw new TetrisException(ErrorCode.DATABASE_EXCEPTION, "Failed to build prepared statement: " + queries, se);
        }
    }

    private String updateQueryForHospitalityRoomsClause(String queries) {
        if (CollectionUtils.isNotEmpty(hospitalityRooms)) {
            queries = queries.replaceAll("hospitality_rooms_clause_aa", " or Accom_Activity.accom_type_id in (" + Joiner.on(",").join(hospitalityRooms) + ")");
            queries = queries.replaceAll("hospitality_rooms_clause_at", " or AT.accom_type_id in (" + Joiner.on(",").join(hospitalityRooms) + ")");
            queries = queries.replaceAll("hospitality_rooms_clause_accomType", " or accomType.accom_type_id in (" + Joiner.on(",").join(hospitalityRooms) + ")");
        } else {
            queries = queries.replaceAll("hospitality_rooms_clause_aa", "");
            queries = queries.replaceAll("hospitality_rooms_clause_at", "");
            queries = queries.replaceAll("hospitality_rooms_clause_accomType", "");
        }
        return queries;
    }

    public void closeStatement() {
        try {
            if (this.generateFplosStmt != null) {
                this.generateFplosStmt.close();
            }
        } catch (SQLException e) {
            throw new TetrisException(ErrorCode.DATABASE_EXCEPTION, "Failed to close namedParameterStatement: ", e);
        }
    }

    public void addFPLOSQueriesInBatch(int specialSrpId, int decisionId, Date startDate, Date endDate, int extendedStay, int lraEnabled,
                                       int lv0UnqualifiedId, int masterClassId, List<Integer> rateQualifiedIds) {

        try {
            int currentPropertyId = PacmanWorkContextHelper.getPropertyId();
            java.sql.Date sqlStartDate = new java.sql.Date(startDate.getTime());
            java.sql.Date sqlEndDate = new java.sql.Date(endDate.getTime());

            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("Parameters passed:\n" + "PROPERTY_ID=" + PacmanWorkContextHelper.getPropertyId() + ",\nSTART_DATE="
                        + sqlStartDate + ",\nEND_DATE=" + sqlEndDate + ",\nEXTENDED_STAY=" + extendedStay + ",\nSPECIAL_SRP_ID=" + specialSrpId
                        + ",\nDECISION_ID=" + decisionId + ",\nLRA_ENABLED=" + lraEnabled + ",\nLV0_UNQUALIFIED_ID=" + lv0UnqualifiedId
                        + ",\nMASTER_CLASS_ID=" + masterClassId + ",\nRATE_QUALIFIED_IDs=" + StringUtils.join(rateQualifiedIds, ","));
            }
            generateFplosStmt.setInt(PROPERTY_ID, currentPropertyId);
            generateFplosStmt.setDate(START_DATE, sqlStartDate);
            generateFplosStmt.setDate(END_DATE, sqlEndDate);
            generateFplosStmt.setInt(EXTENDED_STAY, extendedStay);
            generateFplosStmt.setInt(SPECIAL_SRP_ID, specialSrpId);
            generateFplosStmt.setInt(DECISION_ID, decisionId);
            generateFplosStmt.setString(RATE_QUALIFIED_IDS, StringUtils.join(rateQualifiedIds, ','));
            if (lraEnabledValue != LRA_ENABLED_ALL_BAR_RATES_IMPACT && isLv0SrpInChunk() && !isCpLraEnabled()) {
                generateFplosStmt.setInt(LV0_UNQUALIFIED_ID, lv0UnqualifiedId);
            }
            if (!isCpLraEnabled() && isLv0SrpInChunk()) {
                generateFplosStmt.setInt(MASTER_CLASS_ID, masterClassId);
            }
            generateFplosStmt.addBatch();
        } catch (SQLException se) {
            throw new TetrisException(ErrorCode.DATABASE_EXCEPTION, "addFPLOSQueriesInBatch failed", se);
        }
    }

    public void addFPLOSQueriesInBatch(int specialSrpId, int decisionId, Date startDate, Date endDate, int extendedStay,
                                       int defaultAccomTypeId, int lraEnabled, int lv0UnqualifiedId, int masterClassId, List<Integer> rateQualifiedIds) {
        try {
            int currentPropertyId = PacmanWorkContextHelper.getPropertyId();
            java.sql.Date sqlStartDate = new java.sql.Date(startDate.getTime());
            java.sql.Date sqlEndDate = new java.sql.Date(endDate.getTime());
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("Parameters passed:\n" + "PROPERTY_ID=" + PacmanWorkContextHelper.getPropertyId() + ",\nSTART_DATE="
                        + sqlStartDate + ",\nEND_DATE=" + sqlEndDate + ",\nEXTENDED_STAY=" + extendedStay + ",\nSPECIAL_SRP_ID=" + specialSrpId
                        + ",\nDECISION_ID=" + decisionId + ",\nDEFAULT_ACCOM_TYPE_ID=" + defaultAccomTypeId + ",\nLRA_ENABLED=" + lraEnabled
                        + ",\nLV0_UNQUALIFIED_ID=" + lv0UnqualifiedId + ",\nMASTER_CLASS_ID=" + masterClassId + ",\nRATE_QUALIFIED_IDs=" + StringUtils.join(rateQualifiedIds, ","));
            }

            generateFplosStmt.setInt(PROPERTY_ID, currentPropertyId);
            generateFplosStmt.setDate(START_DATE, sqlStartDate);
            generateFplosStmt.setDate(END_DATE, sqlEndDate);
            generateFplosStmt.setInt(EXTENDED_STAY, extendedStay);
            generateFplosStmt.setInt(SPECIAL_SRP_ID, specialSrpId);
            generateFplosStmt.setInt(DECISION_ID, decisionId);
            generateFplosStmt.setInt(DEFAULT_ACCOM_TYPE_ID, defaultAccomTypeId);
            if (lraEnabledValue != LRA_ENABLED_ALL_BAR_RATES_IMPACT && isLv0SrpInChunk() && !isCpLraEnabled()) {
                generateFplosStmt.setInt(LV0_UNQUALIFIED_ID, lv0UnqualifiedId);
            }
            if (!isCpLraEnabled() && isLv0SrpInChunk()) {
                generateFplosStmt.setInt(MASTER_CLASS_ID, masterClassId);
            }
            generateFplosStmt.setString(RATE_QUALIFIED_IDS, StringUtils.join(rateQualifiedIds, ','));
            generateFplosStmt.addBatch();
        } catch (SQLException se) {
            throw new TetrisException(ErrorCode.DATABASE_EXCEPTION, "addFPLOSQueriesInBatch failed", se);
        }
    }

    public void calculateFPLOS() {
        try {
            generateFplosStmt.executeBatch();
        } catch (SQLException se) {
            throw new TetrisException(ErrorCode.DATABASE_EXCEPTION, "executeBatch failed for calculate FPLOS", se);
        }
    }

    public String generateQueries(String tableName, int maxLOS) {
        boolean isFplosBySRP = false;
        boolean isLv0 = false;
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(createRateQualifiedTempTable());
        stringBuilder.append(servicingCostQueryBuilder.createAndPopulateServicingCostIfConfigured(tableName, maxLOS));
        stringBuilder.append(createEffectiveLRVTable(tableName, maxLOS));
        stringBuilder.append(populateEffectiveLRVTable(tableName, maxLOS, isFplosBySRP && !isLv0));
        stringBuilder.append(createAndPopulateDecisionBarOutputTableIfEnabled(tableName, maxLOS));
        stringBuilder.append(createEffectiveRatesTable(tableName, maxLOS, isFplosBySRP, isLv0));
        stringBuilder.append(createIntermediateRateTable(tableName, maxLOS, isFplosBySRP, isLv0));
        stringBuilder.append(populateIntermediateRateTable(tableName, maxLOS, isFplosBySRP, isLv0));

        if (isChannelRestrictionsAdjustmentsEnabled) {
            stringBuilder.append(createAndPopulateChannelRestrictionsAdjTable());
        }

        if (generateAgileProductQualifiedFPLOS) {
            // Agile rate FPLOS generation queries
            stringBuilder.append(generateAgileProductQualifiedFPLOS(tableName, maxLOS));
        }

        stringBuilder.append(populateEffectiveRatesTableBasedOnDow(tableName, maxLOS, isFplosBySRP, isLv0));
        if (isDerivedRatePlanEnabled) {
            stringBuilder.append(deleteNullBarRateRecordsFromTempTable(tableName, maxLOS));
            if (this.isBarByLos) {
                stringBuilder.append(createTempRateAdjustmentTypeTable(tableName));
                stringBuilder.append(populateTempRateAdjustmentTypeTable(tableName));
                stringBuilder.append(createTempRateAdjustment(tableName, maxLOS));
                stringBuilder.append(populateTempRateAdjustment(tableName, maxLOS, isFplosBySRP));
                if (derivedRatesFPLOSCorrectionEnabled) {
                    stringBuilder.append(updateTempTableForDerivedRatesAndRateAdjustments(tableName, maxLOS));
                } else {
                    stringBuilder.append(updateTempTableFromDerivedOfBarByLosDecisionsAndRateAdjustments(tableName, maxLOS));
                }
                stringBuilder.append(updateTempTableFromDerivedOfBarByLosDecisionsAndRateAdjustmentsForFixedQualifiedRates(tableName, maxLOS));
            } else {
                stringBuilder.append(updateTempTableForDecisionBarOutput(tableName, maxLOS));
            }
        }
        if (!(this.isBarByLos && this.isDerivedRatePlanEnabled)) {
            stringBuilder.append(createTempRateAdjustmentTypeTable(tableName));
            stringBuilder.append(populateTempRateAdjustmentTypeTable(tableName));
            stringBuilder.append(createTempRateAdjustment(tableName, maxLOS));
            stringBuilder.append(populateTempRateAdjustment(tableName, maxLOS, isFplosBySRP));
            stringBuilder.append(udateEffectiveRatesPerStay(tableName, maxLOS));
            stringBuilder.append(updateEffectiveRatesPerNightForYieldableAdjustmentType(tableName, maxLOS, YIELDABLE_COST));
            stringBuilder.append(updateEffectiveRatesPerNightForYieldableAdjustmentType(tableName, maxLOS, YIELDABLE_VALUE));
        }
        stringBuilder.append(createTempFPLOSTable(tableName, isFplosBySRP, isLv0));
        createDecisionReasonTypeData(tableName, maxLOS, stringBuilder);

        if (this.isRestrictHighestBarEnabled) {
            stringBuilder.append(createEffectiveRHBTable(tableName, maxLOS));
            stringBuilder.append(populateEffectiveRHBTable(tableName, maxLOS));
        }
        if (isChannelRestrictionsAdjustmentsEnabled) {
            stringBuilder.append(channelRestrictionsAdjQueryBuilder.updateChannelRestrictionsAdjustedRates("#" + tableName + "_LRA_1", "Rate", maxLOS, propertyId));
        }
        stringBuilder.append(populateTempFPLOSTable(tableName, maxLOS));
        if (generateAgileProductQualifiedFPLOS) {
            stringBuilder.append(buildQueriesToMergeAgileProductFPLOSIntoRegularQualifiedFPLOSForRoomTypeLevel(tableName));
        }
        stringBuilder.append(populateQualifiedFPLOSTable(tableName, isFplosBySRP, isLv0));
        stringBuilder.append(updateQualifiedFPLOSTable(tableName, isFplosBySRP, isLv0));
        stringBuilder.append(deleteInvalidFPLOS(tableName, maxLOS, isFplosBySRP, isLv0));
        if (!isLimitTotalSRPRatesApplicable()) {
            stringBuilder.append(populatePaceQualifiedFPLOSTable(isFplosBySRP, isLv0));
            stringBuilder.append(deleteRateQualifiedIdTempTable());
            stringBuilder.append(deleteChannelRestrictionsTempTable());
        }
        stringBuilder.append(cleanUpTempTables(tableName, isFplosBySRP, isLv0));
        stringBuilder.append(servicingCostQueryBuilder.cleanServicingCostTable());
        return stringBuilder.toString();
    }

    private String createRateAdjustmentTable() {
        return "SELECT rqa.rate_qualified_id,\n" +
                "       rqa.start_date_dt,\n" +
                "       rqa.end_date_dt,\n" +
                "       rqa.posting_rule_id,\n" +
                "       rqa.net_value,\n" +
                "       rqa.net_value_type_id,\n" +
                "       rqa.createdate_dttm,\n" +
                "       rqa.adjustmenttype\n" +
                "INTO   #rate_qualified_adjustment\n" +
                "FROM   rate_qualified_adjustment AS rqa\n" +
                "WHERE  rate_qualified_id NOT IN (SELECT rate_qualified_id\n" +
                "                                 FROM   (SELECT\n" +
                "              RQ.rate_qualified_id,\n" +
                "              RQ.rate_code_name,\n" +
                "              RQ1.rate_code_name    PARENT_RATE_CODE,\n" +
                "              RQ1.rate_qualified_id PARENT_RATE_qualified_id\n" +
                "                                         FROM   rate_qualified RQ\n" +
                "                                                JOIN linked_srp_mappings LSM\n" +
                "                                                  ON RQ.rate_code_name =\n" +
                "                                                     LSM.srp_code\n" +
                "                                                     AND yield_as = 'y'\n" +
                "                                                JOIN rate_qualified RQ1\n" +
                "                                                  ON RQ1.rate_code_name =\n" +
                "                                                     LSM.linked_srp_code\n" +
                "                                                     AND RQ1.status_id = 1\n" +
                "                                         WHERE  rq.status_id = 1\n" +
                "                                                AND RQ.end_date_dt >= Getdate())\n" +
                "                                        A)\n" +
                "UNION\n" +
                "SELECT rq.rate_qualified_id,\n" +
                "       rqa.start_date_dt,\n" +
                "       rqa.end_date_dt,\n" +
                "       rqa.posting_rule_id,\n" +
                "       rqa.net_value,\n" +
                "       rqa.net_value_type_id,\n" +
                "       rqa.createdate_dttm,\n" +
                "       rqa.adjustmenttype\n" +
                "FROM   rate_qualified RQ\n" +
                "       JOIN linked_srp_mappings LSM\n" +
                "         ON RQ.rate_code_name = LSM.srp_code\n" +
                "            AND yield_as = 'y'\n" +
                "       JOIN rate_qualified RQ1\n" +
                "         ON RQ1.rate_code_name = LSM.linked_srp_code\n" +
                "            AND RQ1.status_id = 1\n" +
                "       LEFT JOIN rate_qualified_adjustment RQA\n" +
                "              ON RQA.rate_qualified_id = RQ1.rate_qualified_id\n" +
                "WHERE  rq.status_id = 1\n" +
                "       AND rq1.end_date_dt >= Getdate() ";
    }

    private void createDecisionReasonTypeData(String tableName, int maxLOS, StringBuilder stringBuilder) {
        stringBuilder.append(createTempEffectiveRatesWithDecisionReasonTypeByLos(tableName, maxLOS));
        if (isLv0SrpInChunk()) {
            if (isCpLraEnabled()) {
                stringBuilder.append(populateContinuousPricingLraImpactedRates(maxLOS, tableName));
            } else {
                stringBuilder.append(createTempEffectiveRatesWithDecisionReasonType(tableName, maxLOS));
                stringBuilder.append(populateTempEffectiveRatesWithDecisionReasonType(tableName));
                stringBuilder.append(populateTempEffectiveRatesWithDecisionReasonTypeByLOS(tableName, maxLOS));
            }
        } else {
            stringBuilder.append(populateTempEffectiveRatesWithDecisionReasonTypeMissing(tableName, maxLOS));
        }
    }

    private String populateTempEffectiveRatesWithDecisionReasonTypeMissing(String currTableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("insert into #" + currTableName + "_LRA_1\n");

        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append(" select " + currTableName + "_1.*, null");
        } else {
            stringBuilder.append(" select #" + currTableName + "_1.*, null");
        }
        for (int los = 2; los <= maxLOS; los++) {
            stringBuilder.append(" ,null");
        }
        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append("\n from " + currTableName + "_1 ");
        } else {
            stringBuilder.append("\n from #" + currTableName + "_1 ");
        }
        stringBuilder.append(";");
        return stringBuilder.toString();
    }


    private boolean isCpLraEnabled() {
        return isContinuousPricingEnabled && lraEnabledValue == LRA_ENABLED_ALL_BAR_RATES_IMPACT;
    }

    public String generateQueriesFplosBySRP(String tableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();

        boolean isFplosBySRP = true;
        boolean isLv0 = false;
        if (!isLimitTotalSRPRatesApplicable()) {
            stringBuilder.append(createRateQualifiedTempTable());
            if (isChannelRestrictionsAdjustmentsEnabled) {
                stringBuilder.append(createAndPopulateChannelRestrictionsAdjTable());
            }
        }
        stringBuilder.append(createEffectiveLRVTable(tableName, maxLOS));
        stringBuilder.append(populateEffectiveLRVTable(tableName, maxLOS, isFplosBySRP && !isLv0));
        stringBuilder.append(servicingCostQueryBuilder.createAndPopulateServicingCostIfConfigured(tableName, maxLOS));
        stringBuilder.append(servicingCostQueryBuilder.createAndPopulateServicingCostAtTotalIfConfigured(tableName, maxLOS));

        if (generateAgileProductQualifiedFPLOS) {
            // Agile rate FPLOS generation queries
            stringBuilder.append(agileProductQualifiedFPLOSQueryBuilder.buildQueriesToGenerateFPLOSForSrpFPLOSAtTotalLevel(
                    tableName, maxLOS)
            );
        }
        stringBuilder.append(createAndPopulateDecisionBarOutputTableIfEnabled(tableName, maxLOS));
        if (isCorrectedFplosForDerivedRatesAndBarByLOS) {
            stringBuilder.append(createEffectiveRatesTableByLOS(tableName, maxLOS, isFplosBySRP, isLv0));
            stringBuilder.append(createIntermediateRateTable(tableName, maxLOS, isFplosBySRP, isLv0));
            stringBuilder.append(populateIntermediateRateTable(tableName, maxLOS, isFplosBySRP, isLv0));
            stringBuilder.append(populateEffectiveRatesTableBasedOnDowByLOS(tableName, maxLOS, isFplosBySRP, isLv0));
        } else {
            stringBuilder.append(createEffectiveRatesTable(tableName, maxLOS, isFplosBySRP, isLv0));
            stringBuilder.append(createIntermediateRateTable(tableName, maxLOS, isFplosBySRP, isLv0));
            stringBuilder.append(populateIntermediateRateTable(tableName, maxLOS, isFplosBySRP, isLv0));
            stringBuilder.append(populateEffectiveRatesTableBasedOnDow(tableName, maxLOS, isFplosBySRP, isLv0));
            if (SystemConfig.isUseRemCapFix()) {
                stringBuilder.append(updateRemCapacity(tableName, maxLOS));
            }
        }
        stringBuilder.append(updateTempTableForDecisionIfDerivedRateEnabled(tableName, "_1", maxLOS));
        stringBuilder.append(createTempRateAdjustmentTypeTable(tableName));
        stringBuilder.append(populateTempRateAdjustmentTypeTable(tableName));
        stringBuilder.append(createTempRateAdjustment(tableName, maxLOS));
        stringBuilder.append(populateTempRateAdjustment(tableName, maxLOS, isFplosBySRP));
        stringBuilder.append(udateEffectiveRatesPerStay(tableName, maxLOS));
        stringBuilder.append(updateEffectiveRatesPerNightForYieldableAdjustmentType(tableName, maxLOS, YIELDABLE_COST));
        stringBuilder.append(updateEffectiveRatesPerNightForYieldableAdjustmentType(tableName, maxLOS, YIELDABLE_VALUE));

        if (!SystemConfig.isFPLOSWrateFinalRewriteEnabled()) {
            stringBuilder.append(createTempWeightedRateTable(tableName, maxLOS));
            stringBuilder.append(populateTempWeightedRateTable(tableName, maxLOS));
        }

        stringBuilder.append(createTempWeightedRateFinalTable(tableName, maxLOS));
        stringBuilder.append(populateTempWeightedRateFinalTable(tableName, maxLOS));
        stringBuilder.append(createNonMasterRateCodeTable(tableName, maxLOS));

        stringBuilder.append(createIntermediateNonMasterRateCodeTable(tableName));
        stringBuilder.append(populateIntermediateNonMasterRateCodeTable(tableName));
        stringBuilder.append(createEffectiveRatesTableNonMaster(tableName, maxLOS));
        stringBuilder.append(populateEffectiveRatesTableNonMasterBasedOnDow(tableName, maxLOS));

        stringBuilder.append(updateTempTableForDecisionIfDerivedRateEnabled(tableName, "_non_master_1", maxLOS));

        stringBuilder.append(populateNonMasterRateCodeTableForDOW(tableName, maxLOS));

        stringBuilder.append(createNonMasterRateCodeTable_2(tableName, maxLOS));
        stringBuilder.append(populateNonMasterRateCodeTable_2(tableName));

        stringBuilder.append(populateTempWeightedRateFinalTable_withNonMaster(tableName, maxLOS));
        if (isCorrectedFplosForDerivedRatesAndBarByLOS) {
            stringBuilder.append(createCumulativeWeightedRateTable(tableName, maxLOS));
            stringBuilder.append(populateCumulativeWeightedRateTable(tableName, maxLOS));
        }
        stringBuilder.append(createTempFPLOSTable(tableName, isFplosBySRP, isLv0));
        if (isChannelRestrictionsAdjustmentsEnabled) {
            stringBuilder.append(channelRestrictionsAdjQueryBuilder.updateChannelRestrictionsAdjustedRates("#" + tableName + (isCorrectedFplosForDerivedRatesAndBarByLOS ? "_wrate_cumulative" : "_1_wrate_final")
                    , isCorrectedFplosForDerivedRatesAndBarByLOS ? "cumulative_Rate" : "wRate", maxLOS, propertyId));
        }
        stringBuilder.append(populateTempFPLOSTableNonLV0(tableName, maxLOS));
        if (generateAgileProductQualifiedFPLOS) {
            // Update restriction rate fplos by linked Agile product rate FPLOS
            stringBuilder.append(agileProductQualifiedFPLOSQueryBuilder.buildQueriesToMergeAgileProductFPLOSIntoRegularQualifiedFPLOSTempTableForSRPFPLOSAtTotalLevel(tableName));
        }
        stringBuilder.append(populateQualifiedFPLOSTable(tableName, isFplosBySRP, isLv0));
        stringBuilder.append(updateQualifiedFPLOSTable(tableName, isFplosBySRP, isLv0));
        stringBuilder.append(deleteInvalidFPLOS(tableName, maxLOS, isFplosBySRP, isLv0));
        stringBuilder.append(populatePaceQualifiedFPLOSTable(isFplosBySRP, isLv0));
        stringBuilder.append(cleanUpTempTables(tableName, isFplosBySRP, isLv0));

        isLv0 = true;
        final String currentTableName = tableName + LV0;
        stringBuilder.append(createEffectiveLRVTable(currentTableName, maxLOS));
        stringBuilder.append(populateEffectiveLRVTable(currentTableName, maxLOS, isFplosBySRP && !isLv0));

        stringBuilder.append(createEffectiveRatesTable(currentTableName, maxLOS, isFplosBySRP, isLv0));

        stringBuilder.append(createIntermediateRateTable(currentTableName, maxLOS, isFplosBySRP, isLv0));
        stringBuilder.append(populateIntermediateRateTable(currentTableName, maxLOS, isFplosBySRP, isLv0));
        stringBuilder.append(populateEffectiveRatesTableBasedOnDow(currentTableName, maxLOS, isFplosBySRP, isLv0));

        //This will not work for BarBylos Property LV0 derived
        if (!isBarByLos && isDerivedRatePlanEnabled) {
            stringBuilder.append(createAndPopulateDecisionBarOutputTableIfEnabled(currentTableName, maxLOS));
            stringBuilder.append(updateTempTableForDecisionIfDerivedRateEnabled(currentTableName, "_1", maxLOS));
        }

        stringBuilder.append(createTempRateAdjustmentTypeTable(currentTableName));
        stringBuilder.append(populateTempRateAdjustmentTypeTable(currentTableName));
        stringBuilder.append(createTempRateAdjustment(currentTableName, maxLOS));
        stringBuilder.append(populateTempRateAdjustment(currentTableName, maxLOS, isFplosBySRP));
        stringBuilder.append(udateEffectiveRatesPerStay(currentTableName, maxLOS));
        stringBuilder.append(updateEffectiveRatesPerNightForYieldableAdjustmentType(currentTableName, maxLOS, YIELDABLE_COST));
        stringBuilder.append(updateEffectiveRatesPerNightForYieldableAdjustmentType(currentTableName, maxLOS, YIELDABLE_VALUE));

        stringBuilder.append(createTempFPLOSTable(currentTableName, isFplosBySRP, isLv0));
        createDecisionReasonTypeData(currentTableName, maxLOS, stringBuilder);

        if (this.isRestrictHighestBarEnabled) {
            stringBuilder.append(createEffectiveRHBTable(currentTableName, maxLOS));
            stringBuilder.append(populateEffectiveRHBTable(currentTableName, maxLOS));
        }
        stringBuilder.append(populateTempFPLOSTableForLV0(currentTableName, maxLOS));
        stringBuilder.append(populateQualifiedFPLOSTable(currentTableName, isFplosBySRP, isLv0));
        stringBuilder.append(updateQualifiedFPLOSTable(currentTableName, isFplosBySRP, isLv0));
        stringBuilder.append(deleteInvalidFPLOS(currentTableName, maxLOS, isFplosBySRP, isLv0));
        stringBuilder.append(populatePaceQualifiedFPLOSTable(isFplosBySRP, isLv0));
        stringBuilder.append(cleanUpTempTables(currentTableName, isFplosBySRP, isLv0));
        stringBuilder.append(deleteRateQualifiedIdTempTable());
        stringBuilder.append(deleteChannelRestrictionsTempTable());
        stringBuilder.append(servicingCostQueryBuilder.cleanServicingCostTable());
        stringBuilder.append(servicingCostQueryBuilder.cleanServicingCostAtTotalTable());
        return stringBuilder.toString();
    }

    protected String createAndPopulateChannelRestrictionsAdjTable() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("IF OBJECT_ID('tempdb..#").append(propertyId).append("_ChannelRestrictionsAdj') IS NOT NULL DROP TABLE #").append(propertyId).append("_ChannelRestrictionsAdj;\n")
                .append("CREATE TABLE #").append(propertyId).append("_ChannelRestrictionsAdj(\n")
                .append(" [Rate_Qualified_ID] [int] NOT NULL,\n")
                .append(" [Arrival_DT] [date] NOT NULL,\n")
                .append(" [Fixed_Adj] [numeric](19,5),\n");
        for (int i = 1; i <= barMaxLos; i++) {
            stringBuilder.append(" [Percent_Adj").append(i).append("] [numeric](19,5),\n");
        }
        stringBuilder.append(" );\n");
        stringBuilder.append("INSERT INTO #").append(propertyId).append("_ChannelRestrictionsAdj")
                .append(" select \n" +
                        "Rate_Qualified_ID,\n" +
                        "(select cast (calendar_date as date)) arrival_dt,\n" +
                        "Fixed_Adj=case \n" +
                        "when DATEPART(WEEKDAY, calendar_date) = 1 then Sunday_Fixed_Adjustment_By_Yield\n" +
                        "when DATEPART(WEEKDAY, calendar_date) = 2 then Monday_Fixed_Adjustment_By_Yield\n" +
                        "when DATEPART(WEEKDAY, calendar_date) = 3 then Tuesday_Fixed_Adjustment_By_Yield\n" +
                        "when DATEPART(WEEKDAY, calendar_date) = 4 then Wednesday_Fixed_Adjustment_By_Yield\n" +
                        "when DATEPART(WEEKDAY, calendar_date) = 5 then Thursday_Fixed_Adjustment_By_Yield\n" +
                        "when DATEPART(WEEKDAY, calendar_date) = 6 then Friday_Fixed_Adjustment_By_Yield\n" +
                        "when DATEPART(WEEKDAY, calendar_date) = 7 then Saturday_Fixed_Adjustment_By_Yield\n" +
                        "end \n" +
                        "\n");
        for (int i = 0; i < barMaxLos; i++) {
            stringBuilder.append(percentAdjByLOS(i));
        }
        stringBuilder.append(
                " from calendar_dim cal join\n" +
                        " #" + propertyId + "_RateQualifiedTable rq on calendar_date between :" + START_DATE +
                        " and :" + END_DATE +
                        " \n join Channel_Srp_Group_Included_Srp_Mapping iSRP\n" +
                        " on rq.Rate_Code_Name = iSRP.Included_Srp_Code\n" +
                        " join Channel_Restriction_Adjustment cra\n" +
                        " on iSRP.Channel_Restriction_Adjustment_ID = cra.Channel_Restriction_Adjustment_ID ; ");
        return stringBuilder.toString();
    }

    private String percentAdjByLOS(int los) {
        return ", Percent_Adj" + (los + 1) + "=case \n" +
                "when DATEPART(WEEKDAY, DATEADD(DAY, " + los + ", calendar_date)) = 1 then Sunday_Percentage_Adjustment\n" +
                "when DATEPART(WEEKDAY, DATEADD(DAY, " + los + ", calendar_date)) = 2 then Monday_Percentage_Adjustment\n" +
                "when DATEPART(WEEKDAY, DATEADD(DAY, " + los + ", calendar_date)) = 3 then Tuesday_Percentage_Adjustment\n" +
                "when DATEPART(WEEKDAY, DATEADD(DAY, " + los + ", calendar_date)) = 4 then Wednesday_Percentage_Adjustment\n" +
                "when DATEPART(WEEKDAY, DATEADD(DAY, " + los + ", calendar_date)) = 5 then Thursday_Percentage_Adjustment\n" +
                "when DATEPART(WEEKDAY, DATEADD(DAY, " + los + ", calendar_date)) = 6 then Friday_Percentage_Adjustment\n" +
                "when DATEPART(WEEKDAY, DATEADD(DAY, " + los + ", calendar_date)) = 7 then Saturday_Percentage_Adjustment\n" +
                "end \n" +
                " \n";
    }

    @VisibleForTesting
    String populateContinuousPricingLraImpactedRates(int maxLOS, String currTableName) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("insert into #" + currTableName + "_LRA_1\n");

        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append(" select " + currTableName + "_1.*,ReasonType_los1");
        } else {
            stringBuilder.append(" select #" + currTableName + "_1.*,ReasonType_los1");
        }
        for (int los = 2; los <= maxLOS; los++) {
            stringBuilder.append(" ,ReasonType_los").append(los);
        }

        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append("\n from " + currTableName + "_1 left join ");
        } else {
            stringBuilder.append("\n from #" + currTableName + "_1 left join ");
        }

        stringBuilder.append("( ");

        if (SystemConfig.isOptimizeCPLRAEnabled()) {
            stringBuilder.append(" select * from " +
                    "  ( " +
                    "   SELECT   CP.Arrival_DT AS Arrival_DT , CP.Accom_Type_ID AS Accom_Type_ID,  :" + SPECIAL_SRP_ID + " Rate_Qualified_ID,  CP.Decision_Reason_Type_ID AS ReasonType_los1 ");
            for (int los = 2; los <= maxLOS; los++) {
                stringBuilder.append(" , CASE WHEN LEAD(CP.Arrival_DT, " + (los - 1) + ") OVER (PARTITION BY CP.Accom_Type_ID ORDER BY CP.Arrival_DT) = DATEADD(DAY, " + (los - 1) + " , CP.Arrival_DT) " +
                        "                    THEN LEAD(CP.Decision_Reason_Type_ID, " + (los - 1) + " ) OVER (PARTITION BY CP.Accom_Type_ID ORDER BY CP.Arrival_DT) ELSE NULL END AS ReasonType_los" + los + " ");
            }
            stringBuilder.append(" from CP_Decision_Bar_Output AS CP where CP.Arrival_DT BETWEEN :startDate AND DATEADD(DAY, " + maxLOS * 2 + " , :endDate) AND CP.LOS = -1 AND CP.Decision_Reason_Type_ID=6 AND CP.Product_ID=1  " +
                    ")a where Arrival_DT BETWEEN :startDate  " +
                    "  AND DATEADD(DAY, " + maxLOS + " , :endDate) ");
        } else {
            stringBuilder.append("SELECT CP.Arrival_DT AS Arrival_DT ,CP.Accom_Type_ID AS Accom_Type_ID,:" + SPECIAL_SRP_ID + " Rate_Qualified_ID, CP.Decision_Reason_Type_ID AS ReasonType_los1");
            for (int los = 2; los <= maxLOS; los++) {
                stringBuilder.append(", CP" + (los - 1) + ".Decision_Reason_Type_ID AS ReasonType_los" + los);
            }
            stringBuilder.append("\n from CP_Decision_Bar_Output AS CP \n");
            stringBuilder.append(" LEFT JOIN ");
            stringBuilder.append(" CP_Decision_Bar_Output AS CP1 ON CP.Accom_Type_ID = CP1.Accom_Type_ID AND CP1.Arrival_DT = dateadd(DAY," + 1 + ", CP.Arrival_DT)");
            stringBuilder.append(" AND CP1.Decision_Reason_Type_ID = 6 AND CP.Product_ID=CP1.Product_ID \n");
            for (int los = 2; los < maxLOS; los++) {
                stringBuilder.append(" LEFT JOIN CP_Decision_Bar_Output AS CP" + los + " ON CP.Accom_Type_ID = CP" + los + ".Accom_Type_ID AND CP" + los + ".Arrival_DT = dateadd(DAY," + los + ", CP.Arrival_DT)");
                stringBuilder.append(" AND CP" + los + ".Decision_Reason_Type_ID =6 AND CP" + (los - 1) + ".Decision_Reason_Type_ID =6 AND CP.Product_ID=CP" + los + ".Product_ID \n");
            }
            stringBuilder.append(" where CP.Arrival_DT BETWEEN :" + START_DATE + " AND DATEADD(DAY," + maxLOS + ", :" + END_DATE + ") AND CP.LOS = -1 AND CP.Decision_Reason_Type_ID=6 AND CP.Product_ID=1 \n");
        }
        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append(") LRA_IMPACT on " + currTableName + "_1.Arrival_DT=LRA_IMPACT.Arrival_DT and LRA_IMPACT.Accom_Type_ID=");
            stringBuilder.append(currTableName + "_1.Accom_Type_ID AND " + currTableName + "_1.Rate_Qualified_id=LRA_IMPACT.Rate_Qualified_ID;\n");
        } else {
            stringBuilder.append(") LRA_IMPACT on #" + currTableName + "_1.Arrival_DT=LRA_IMPACT.Arrival_DT and LRA_IMPACT.Accom_Type_ID=");
            stringBuilder.append("#" + currTableName + "_1.Accom_Type_ID AND #" + currTableName + "_1.Rate_Qualified_id=LRA_IMPACT.Rate_Qualified_ID;\n");
        }

        return stringBuilder.toString();
    }

    protected String createRateQualifiedTempTable() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("IF OBJECT_ID('tempdb..#").append(propertyId).append("_RateQualifiedTable') IS NOT NULL DROP TABLE #").append(propertyId).append("_RateQualifiedTable;\n")
                .append("CREATE TABLE #" + propertyId + "_RateQualifiedTable(\n")
                .append(" [Rate_Qualified_ID] [int] NOT NULL,\n")
                .append(" [Rate_Code_Name] [nvarchar](50) NOT NULL,\n")
                .append(" [Property_ID] [int] NOT NULL,\n")
                .append(" [Yieldable] [int] NOT NULL,\n")
                .append(" [Status_ID] [int] NOT NULL,\n")
                .append(" [Rate_Qualified_Type_Id] [int] NOT NULL,\n")
                .append(" CONSTRAINT [PK_").append(propertyId).append("_RateQualifiedTable] PRIMARY KEY CLUSTERED (\n")
                .append(" [Rate_Qualified_ID] ASC \n")
                .append(") );\n");
        stringBuilder.append("INSERT INTO #").append(propertyId).append("_RateQualifiedTable")
                .append(" SELECT \n")
                .append("   rq.Rate_Qualified_ID,\n")
                .append("   rq.Rate_Code_Name,\n")
                .append("   rq.Property_ID,\n")
                .append("   rq.Yieldable,\n")
                .append("   rq.Status_ID,\n")
                .append("   rq.Rate_Qualified_Type_Id\n")
                .append("from STRING_SPLIT(:rateQualifiedIds, ',')\n")
                .append(" INNER JOIN " + getRateQualifiedTableName() + " rq ON value = rq.Rate_Qualified_ID;\n");
        return stringBuilder.toString();
    }

    private String getRateQualifiedDetails() {
        return this.useFixedRateQualifiedTableEnabled ? TABLE_RATE_QUALIFIED_FIXED_DETAILS
                : TABLE_RATE_QUALIFIED_DETAILS;
    }

    private String getRateQualifiedTableName() {
        return this.useFixedRateQualifiedTableEnabled ? TABLE_RATE_QUALIFIED_FIXED : TABLE_RATE_QUALIFIED;
    }

    @VisibleForTesting
    boolean isLv0SrpInChunk() {
        return rateQualifiedIds.contains(lv0QualifiedId);
    }

    protected String createAndPopulateDecisionBarOutputTableIfEnabled(String tableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        if (isCorrectedFplosForDerivedRatesAndBarByLOS) {
            stringBuilder.append(createTempTableForDecisionBarOutputByLOS(tableName, maxLOS));
            stringBuilder.append(populateBarRatesWithLOS(tableName, maxLOS));
            stringBuilder.append(deleteBarRatesWhenAnyUnqualifiedDetailsMissing(tableName, maxLOS));
        } else if (isDerivedRatePlanEnabled) {
            stringBuilder.append(createTempTableForDecisionBarOutput(tableName, maxLOS));
            stringBuilder.append(populateTempTableForDecisionBarOutput(tableName, maxLOS));
        }
        return stringBuilder.toString();
    }

    private String deleteBarRatesWhenAnyUnqualifiedDetailsMissing(String currTableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("delete b from #" + currTableName + "_bar_value b inner join \n")
                .append(" (select Arrival_DT, Accom_Type_ID, count(*) as noOfLOS from #" + currTableName + "_bar_value group by Arrival_DT, Accom_Type_ID having count(*) < " + maxLOS + ") as rowsToBeDeleted  \n")
                .append(" on b.Arrival_DT = rowsToBeDeleted.Arrival_DT and b.Accom_Type_ID = rowsToBeDeleted.Accom_Type_ID; \n");

        return stringBuilder.toString();
    }

    protected String updateTempTableForDecisionIfDerivedRateEnabled(String tableName, String tableExtension, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        if (isDerivedRatePlanEnabled) {
            stringBuilder.append(deleteNullBarRateRecordsFromTempTable(tableName, tableExtension, maxLOS));
            if (derivedRatesFPLOSCorrectionEnabled && isBarByLos) {
                stringBuilder.append(updateTempTableWithBarAndDerivedRates(tableName, tableExtension, maxLOS));
            } else {
                stringBuilder.append(updateTempTableForDecisionBarOutput(tableName, tableExtension, maxLOS));
            }
        }
        return stringBuilder.toString();
    }

    protected String deleteNullBarRateRecordsFromTempTable(String currTableName, String extension, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();

        boolean isFPLOSWithNonTempTableEnabled = SystemConfig.isFPLOSWithNonTempTableEnabled() && !"_non_master_1".equalsIgnoreCase(extension);
        if (isFPLOSWithNonTempTableEnabled) {
            stringBuilder.append("delete ").append(currTableName).append(extension).append("\n")
                    .append(" from ").append(currTableName).append(extension).append("\n")
                    .append(" inner join #").append(propertyId).append("_RateQualifiedTable rq on ").append(currTableName).append(extension).append(".Rate_Qualified_id = rq.Rate_Qualified_id\n");
        } else {
            stringBuilder.append("delete #").append(currTableName).append(extension).append("\n")
                    .append(" from #").append(currTableName).append(extension).append("\n")
                    .append(" inner join #").append(propertyId).append("_RateQualifiedTable rq on #").append(currTableName).append(extension).append(".Rate_Qualified_id = rq.Rate_Qualified_id\n");
        }

        if (isLimitTotalSRPRatesApplicable()) {
            stringBuilder.append(" inner join Limit_Total_Rate_Qualified ON Limit_Total_Rate_Qualified.Limit_Total_Rate_Qualified_ID = rq.Rate_Qualified_ID \n");
        }

        if (isFPLOSWithNonTempTableEnabled) {
            stringBuilder.append(" left join #").append(currTableName).append("_bar_value on ").append(currTableName).append(extension).append(".arrival_dt = #").append(currTableName).append("_bar_value.arrival_dt and ")
                    .append(currTableName).append(extension).append(".Accom_Type_ID = #").append(currTableName).append("_bar_value.Accom_Type_ID\n");
        } else {
            stringBuilder.append(" left join #").append(currTableName).append("_bar_value on #").append(currTableName).append(extension).append(".arrival_dt = #").append(currTableName).append("_bar_value.arrival_dt and #")
                    .append(currTableName).append(extension).append(".Accom_Type_ID = #").append(currTableName).append("_bar_value.Accom_Type_ID\n");
        }
        stringBuilder.append(" where ( #").append(currTableName).append("_bar_value.BAR_Rate" + maxLOS + " is null)\n");
        stringBuilder.append(" and ( rq.Rate_Qualified_type_ID=1 or rq.Rate_Qualified_type_ID=2)\n");
        return stringBuilder.toString();
    }

    protected String updateTempTableForDecisionBarOutput(String currTableName, String extension, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("update temp_fplos set \n");
        for (int los = 1; los <= maxLOS; los++) {

            stringBuilder.append(" temp_fplos.rate").append(los).append(" = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate" + los + " + temp_fplos.Rate" + los + "\n")
                    .append(" when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate" + los + " + ((temp_bar.BAR_Rate" + los + " * temp_fplos.Rate" + los + ")/100)\n")
                    .append(" else temp_fplos.rate" + los + " end,\n");

        }
        stringBuilder.setLength(stringBuilder.length() - 2);

        boolean isFPLOSWithNonTempTableEnabled = SystemConfig.isFPLOSWithNonTempTableEnabled() && !"_non_master_1".equalsIgnoreCase(extension);
        if (isFPLOSWithNonTempTableEnabled) {
            stringBuilder.append("\n from ").append(currTableName).append(extension).append(" temp_fplos\n");
        } else {
            stringBuilder.append("\n from #").append(currTableName).append(extension).append(" temp_fplos\n");
        }

        stringBuilder.append(" INNER JOIN #").append(currTableName).append("_BAR_Value temp_bar on temp_bar.Arrival_DT = temp_fplos.arrival_DT and temp_bar.Accom_Type_ID = temp_fplos.Accom_Type_ID\n")
                .append(" INNER JOIN #").append(propertyId).append("_RateQualifiedTable rq on rq.Rate_Qualified_ID = temp_fplos.Rate_Qualified_id");
        if (isLimitTotalSRPRatesApplicable()) {
            stringBuilder.append("\n INNER JOIN Limit_Total_Rate_Qualified lt ON lt.Limit_Total_Rate_Qualified_ID = rq.Rate_Qualified_ID");
        }
        stringBuilder.append(";\n");
        return stringBuilder.toString();

    }

    @VisibleForTesting
    String updateTempTableWithBarAndDerivedRates(String currTableName, String extension, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("update temp_fplos set \n");
        for (int los = 1; los <= maxLOS; los++) {

            stringBuilder.append(" temp_fplos.rate").append(los).append(" = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate" + los + " + temp_fplos.Rate" + los + "\n")
                    .append(" when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate" + los + " + ((temp_bar.BAR_Rate" + los + " * temp_fplos.Rate" + los + ")/100)\n")
                    .append(" else temp_fplos.rate" + los + " end,\n");

        }
        stringBuilder.setLength(stringBuilder.length() - 2);

        boolean isFPLOSWithNonTempTableEnabled = SystemConfig.isFPLOSWithNonTempTableEnabled() && !"_non_master_1".equalsIgnoreCase(extension);
        if (isFPLOSWithNonTempTableEnabled) {
            stringBuilder.append("\n from ").append(currTableName).append(extension).append(" temp_fplos\n");
        } else {
            stringBuilder.append("\n from #").append(currTableName).append(extension).append(" temp_fplos\n");
        }

        stringBuilder.append(" INNER JOIN #").append(currTableName).append("_BAR_Value temp_bar on temp_bar.Arrival_DT = temp_fplos.arrival_DT and temp_bar.Accom_Type_ID = temp_fplos.Accom_Type_ID")
                .append(" and temp_fplos.los = temp_bar.los \n")
                .append(" INNER JOIN #").append(propertyId).append("_RateQualifiedTable rq on rq.Rate_Qualified_ID = temp_fplos.Rate_Qualified_id");
        if (isLimitTotalSRPRatesApplicable()) {
            stringBuilder.append("\n INNER JOIN Limit_Total_Rate_Qualified lt ON lt.Limit_Total_Rate_Qualified_ID = rq.Rate_Qualified_ID");
        }
        stringBuilder.append(";\n");
        return stringBuilder.toString();

    }

    public String populateNonMasterRateCodeTableForDOW(String tableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" insert into #" + tableName + "_non_master \n")
                .append(" select t1.Arrival_DT, t1.Rate_Qualified_id\n");
        if (isCorrectedFplosForDerivedRatesAndBarByLOS) {
            stringBuilder.append(", LOS\n");
        }
        for (int i = 1; i <= maxLOS; i++) {
            stringBuilder.append(", max(Rate").append(i).append(") as Rate").append(i).append("\n");
        }
        stringBuilder.append("from #" + tableName + "_non_master_1 t1\n");
        stringBuilder.append("group by t1.arrival_dt, t1.Rate_Qualified_ID");
        if (isCorrectedFplosForDerivedRatesAndBarByLOS) {
            stringBuilder.append(" ,LOS");
        }
        stringBuilder.append(";\n");
        return stringBuilder.toString();
    }

    protected String updateEffectiveRatesTablesIfServicingCostEnabled(String tableName, int maxLOS) {
        if (isServicingCostEnabledAndConfigured) {
            return updateEffectiveRatesTableBasedOnServicingCost(tableName + "_1", maxLOS);
        }
        return "";
    }

    private String updateEffectiveRatesTableBasedOnServicingCost(String currTableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(UPDATE + currTableName + "\n");
        stringBuilder.append(" set");
        StringJoiner sj = new StringJoiner(",");
        for (int los = 1; los <= maxLOS; los++) {
            sj.add(" Rate" + los + " = t.Rate" + los + " - servicingCost_los" + los + "\n");
        }
        stringBuilder.append(sj)
                .append(" " + FROM + currTableName + " as t inner join #" + servicingCostQueryBuilder.servicingCostTableTempTable + " as servicingCost ")
                .append(" on (t.Rate_Qualified_id = servicingCost.Rate_Qualified_id and t.Accom_Type_ID = servicingCost.Accom_Type_ID)\n");

        return stringBuilder.toString();
    }

    protected String populateIntermediateNonMasterRateCodeTable(String tableName) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" insert into #" + tableName + "a_non_master\n" +
                "select arrival_dt as Arrival_DT,rate_q.Accom_Type_ID,rate_q.Rate_Qualified_ID,Rate=case\n" +
                " when DATEPART(weekday,arrival_dt) = 1 then Sunday\n" +
                " when DATEPART(weekday,arrival_dt) = 2 then Monday\n" +
                " when DATEPART(weekday,arrival_dt) = 3 then Tuesday\n" +
                " when DATEPART(weekday,arrival_dt) = 4 then Wednesday\n" +
                " when DATEPART(weekday,arrival_dt) = 5 then Thursday\n" +
                " when DATEPART(weekday,arrival_dt) = 6 then Friday\n" +
                " when DATEPART(weekday,arrival_dt) = 7 then Saturday\n" +
                "end\n" +
                "from\n" +
                "( \n" +
                "select cast (calendar_date as date) arrival_dt from calendar_dim where calendar_date between :" + START_DATE + " and :" + END_DATE + " \n" +
                ") dt left join \n" +
                "( \n" +
                "select rqd.* from #").append(propertyId).append("_RateQualifiedTable rq inner join " + "" + getRateQualifiedDetails() + "" + " rqd on rq.Rate_Qualified_ID=rqd.Rate_Qualified_ID \n");
        if (isLimitTotalSRPRatesApplicable()) {
            stringBuilder.append(" inner join Limit_Total_Rate_Qualified lt ON lt.Limit_Total_Rate_Qualified_ID = rq.Rate_Qualified_ID \n");
        }
        stringBuilder.append("where rq.Property_ID=:" + PROPERTY_ID + " and rq.Rate_Qualified_ID!=:" + SPECIAL_SRP_ID + "  and rq.Status_ID=1 and rq.Yieldable=1 \n");
        stringBuilder.append(") rate_q on dt.arrival_dt between rate_q.Start_Date_DT and rate_q.End_Date_DT \n" +
                "inner join  \n" +
                "( \n" +
                "select AT.Accom_Type_ID from Accom_Type AT inner join Accom_Class AC on AT.Accom_Class_ID=AC.Accom_Class_ID  \n" +
                "where AT.System_Default=0 and AC.System_Default=0 and AT.Status_ID=1 and AC.Status_ID=1 \n" +
                "and AC.Master_Class!=1 and AT.Property_ID=:" + PROPERTY_ID + " \n" +
                "and (At.Accom_Type_Capacity>0 hospitality_rooms_clause_at ) \n" +
                ") AT on rate_q.Accom_Type_ID=AT.Accom_Type_ID\n");
        if (generateAgileProductQualifiedFPLOS) {
            stringBuilder.append(" and not exists( select agileProd.Rate_Qualified_ID from Agile_Product_Restriction_Association agileProd where agileProd.Rate_Qualified_ID = rate_q.Rate_Qualified_ID); \n");
        }
        return stringBuilder.toString();
    }

    protected String createEffectiveRatesTableNonMaster(String currTableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("IF OBJECT_ID('tempdb..#").append(currTableName).append("_non_master_1') IS NOT NULL DROP TABLE #").append(currTableName).append("_non_master_1;\n");
        stringBuilder.append("CREATE TABLE #").append(currTableName).append("_non_master_1(\n");
        stringBuilder.append("\n");
        stringBuilder.append("[Arrival_DT] [date] NOT NULL,\n");
        stringBuilder.append("[Accom_Type_ID] [int] NOT NULL,\n");
        stringBuilder.append("[Rate_Qualified_id] [int] NOT NULL,\n");
        if (isCorrectedFplosForDerivedRatesAndBarByLOS) {
            stringBuilder.append("[LOS] [int] NOT NULL,\n");
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("[Rate" + los + "] [numeric](19,5) NOT NULL,\n");
        }

        stringBuilder.append(" CONSTRAINT [PK_").append(currTableName).append("_non_master_1] PRIMARY KEY CLUSTERED \n");
        stringBuilder.append("(\n");
        stringBuilder.append("Arrival_DT,Accom_Type_ID,Rate_Qualified_id");
        if (isCorrectedFplosForDerivedRatesAndBarByLOS) {
            stringBuilder.append(", LOS");
        }
        stringBuilder.append(" ASC\n")
                .append(")\n");
        stringBuilder.append(");\n");
        return stringBuilder.toString();
    }

    protected String populateEffectiveRatesTableNonMasterBasedOnDow(String currTableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("insert into #" + currTableName + "_non_master_1\n");
        stringBuilder.append("select * from (");
        stringBuilder.append(" select t1.Arrival_DT,t1.Accom_Type_ID,t1.Rate_Qualified_ID\n");
        if (isCorrectedFplosForDerivedRatesAndBarByLOS) {
            stringBuilder.append(",LOS\n");
        }
        stringBuilder.append(", t1.Rate as Rate1");
        for (int los = 2; los <= maxLOS; los++) {
            stringBuilder.append(", LEAD(Rate, " + (los - 1) + ") OVER (PARTITION BY t1.Accom_Type_ID,t1.Rate_Qualified_ID ORDER BY t1.Arrival_DT) as Rate" + los + "\n");
        }
        stringBuilder.append("from #").append(currTableName).append("a_non_master t1 \n");
        if (isCorrectedFplosForDerivedRatesAndBarByLOS) {
            stringBuilder.append("inner join #").append(currTableName).append("_bar_value temp_bar on temp_bar.Arrival_DT = t1.arrival_DT and temp_bar.Accom_Type_ID = t1.Accom_Type_ID \n");
        }
        stringBuilder.append(" ) as t2")
                .append(" where t2.Rate" + maxLOS + " is not null ");
        stringBuilder.append(";");
        return stringBuilder.toString();
    }

    protected String createIntermediateNonMasterRateCodeTable(String tableName) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("IF OBJECT_ID('tempdb..#" + tableName + "a_non_master') IS NOT NULL DROP TABLE #" + tableName + "a_non_master;\n" +
                "CREATE TABLE #" + tableName + "a_non_master(\n" +
                "[Arrival_DT] [date] NOT NULL,\n" +
                "[Accom_Type_ID] [int] NOT NULL,\n" +
                "[Rate_Qualified_id] [int] NOT NULL,\n" +
                "[Rate] [numeric](19,5) NOT NULL,\t\n" +
                " CONSTRAINT [PK_" + tableName + "a_non_master] PRIMARY KEY CLUSTERED \n" +
                "(\n" +
                "Arrival_DT,Accom_Type_ID,Rate_Qualified_id ASC\n" +
                "));\n");
        return stringBuilder.toString();
    }

    public String createEffectiveLRVTable(String currTableName, int maxLOS) {
        return effectiveLRVQueryBuilder.createEffectiveLRVTable(currTableName, maxLOS);
    }

    public String populateEffectiveLRVTable(String currTableName, int maxLOS, boolean onlyForMasterClass) {
        return effectiveLRVQueryBuilder.populateEffectiveLRVTable(currTableName, maxLOS, onlyForMasterClass, usePhysicalPropertyMasterClass, physicalPropertyMasterClassMap);
    }

    public String createIntermediateRateTable(String currTableName, int maxLOS, boolean isFplosBySRP, boolean isLv0) {
        StringBuilder stringBuilder = new StringBuilder();

        stringBuilder.append("IF OBJECT_ID('tempdb..#" + currTableName + "_1a_rate_qualified') IS NOT NULL DROP TABLE #" + currTableName + "_1a_rate_qualified;\n")
                .append("CREATE TABLE #" + currTableName + "_1a_rate_qualified(\n")
                .append("[Accom_Type_ID] [int] NOT NULL\n")
                .append(" ,[Accom_Class_ID] [int] NOT NULL\n")
                .append(" ,[Rate_Qualified_id] [int] NOT NULL\n")
                .append(" ,[Rate_Code_Name] [nvarchar](50) NOT NULL\n")
                .append(" ,[Start_Date_DT] [date] not null\n")
                .append(" ,[End_Date_DT] [date] not null\n")
                .append(" ,[Sunday] [numeric](19, 5) NOT NULL\n")
                .append(" ,[Monday] [numeric](19, 5) NOT NULL\n")
                .append(" ,[Tuesday] [numeric](19, 5) NOT NULL\n")
                .append(" ,[Wednesday] [numeric](19, 5) NOT NULL\n")
                .append(" ,[Thursday] [numeric](19, 5) NOT NULL\n")
                .append(" ,[Friday] [numeric](19, 5) NOT NULL\n")
                .append(" ,[Saturday] [numeric](19, 5) NOT NULL\n")
                .append(" );\n");

        stringBuilder.append("IF OBJECT_ID('tempdb..#" + currTableName + "_1a') IS NOT NULL DROP TABLE #" + currTableName + "_1a;\n")
                .append("CREATE TABLE #" + currTableName + "_1a(\n")
                .append("    [Arrival_DT] [date] NOT NULL,\n")
                .append("    [Accom_Type_ID] [int] NOT NULL,\n")
                .append("    [Rate_Qualified_id] [int] NOT NULL,\n")
                .append("    [Rate] [numeric](19,5) NOT NULL,\n");
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("    [lrv" + los + "] [numeric](19,5) NOT NULL,\n");
        }
        if (isFplosBySRP && !isLv0) {
            stringBuilder.append("    [remCap] [numeric](19,5) NOT NULL,\n");
        }
        stringBuilder.append("\n");
        stringBuilder.append(" CONSTRAINT [PK_" + currTableName + "_1a] PRIMARY KEY CLUSTERED \n");
        stringBuilder.append("(\n");
        stringBuilder.append("    Arrival_DT,Accom_Type_ID,Rate_Qualified_id ASC )\n");
        stringBuilder.append(");\n");

        return stringBuilder.toString();
    }

    protected String createAndPopulateQualifiedRateMasterClassTable(String currTableName) {
        StringBuilder stringBuilder = new StringBuilder();

        stringBuilder.append("IF OBJECT_ID('tempdb..#" + currTableName + "_PhysicalPropertyMaterClass') IS NOT NULL DROP TABLE #" + currTableName + "_PhysicalPropertyMaterClass;\n" +
                "CREATE TABLE #" + currTableName + "_PhysicalPropertyMaterClass(\n" +
                "    [Physical_Property_Code] [nvarchar](50) NOT NULL,\n" +
                "    [Master_Class_ID] [int] NOT NULL);");
        stringBuilder.append("insert into #" + currTableName + "_PhysicalPropertyMaterClass\n")
                .append(" values ");
        physicalPropertyMasterClassMap.entrySet().forEach(entry -> {
            stringBuilder.append("('").append(entry.getKey()).append("',").append(entry.getValue()).append("),");
        });
        stringBuilder.setLength(Math.max(stringBuilder.length() - 1, 0));

        stringBuilder.append("; IF OBJECT_ID('tempdb..#" + currTableName + "_RateQualifiedMasterClass') IS NOT NULL DROP TABLE #" + currTableName + "_RateQualifiedMasterClass;\n" +
                "CREATE TABLE #" + currTableName + "_RateQualifiedMasterClass(\n" +
                "    [Master_Class_ID] [int] NOT NULL,\n" +
                "    [Rate_Qualified_ID] [int] NOT NULL,\n" +
                " CONSTRAINT [PK_temp_" + currTableName + "_RateQualifiedMasterClass] PRIMARY KEY CLUSTERED \n" +
                "(\n" +
                "    Master_Class_ID,Rate_Qualified_ID ASC )\n" +
                ");");
        stringBuilder.append("insert into #" + currTableName + "_RateQualifiedMasterClass\n")
                .append("select distinct " +
                        " pp.Master_Class_ID " +
                        ", rq.Rate_Qualified_ID from #" + currTableName + "_1a_rate_qualified rq join #" + currTableName + "_PhysicalPropertyMaterClass pp " +
                        " on rq.Rate_Code_Name like concat(pp.Physical_Property_Code,'_','%');");
        return stringBuilder.toString();
    }

    protected String populateIntermediateRateTable(String currTableName, int maxLOS, boolean isFplosBySRP, boolean isLv0) {
        StringBuilder stringBuilder = new StringBuilder();

        stringBuilder.append("insert into #" + currTableName + "_1a_rate_qualified\n")
                .append("select " + getRateQualifiedDetails() + ".Accom_Type_ID \n")
                .append(", Accom_Type.Accom_Class_ID \n")
                .append(", " + getRateQualifiedDetails() + ".Rate_Qualified_ID\n")
                .append(", rq.Rate_Code_Name\n")
                .append(", " + getRateQualifiedDetails() + ".Start_Date_DT\n")
                .append(", " + getRateQualifiedDetails() + ".End_Date_DT\n")
                .append(", Sunday\n")
                .append(", Monday\n")
                .append(", Tuesday\n")
                .append(", Wednesday\n")
                .append(", Thursday\n")
                .append(", Friday\n")
                .append(", Saturday          \n")
                .append("FROM " + getRateQualifiedDetails() + " \n")
                .append(" inner join #").append(propertyId).append("_RateQualifiedTable rq on rq.Rate_Qualified_ID=" + getRateQualifiedDetails() + ".Rate_Qualified_ID\n")
                .append("INNER JOIN Accom_Type ON " + getRateQualifiedDetails() + ".Accom_Type_ID = Accom_Type.Accom_Type_ID \n");
        if (isLimitTotalSRPRatesApplicable()) {
            if (isFplosBySRP) {
                stringBuilder.append(" inner join Limit_Total_Rate_Qualified lt ON lt.Limit_Total_Rate_Qualified_ID = rq.Rate_Qualified_ID \n");
                stringBuilder.append(" where ");
            } else {
                stringBuilder.append(" left join Limit_Total_Rate_Qualified lt ON lt.Limit_Total_Rate_Qualified_ID = rq.Rate_Qualified_ID \n");
                stringBuilder.append(" where lt.Limit_Total_Rate_Qualified_ID is null and \n");
            }
        } else {
            stringBuilder.append(" where ");
        }
        stringBuilder.append(" rq.Status_ID = 1\n" +
                "       AND rq.Yieldable = 1\n" +
                "       AND Accom_Type.Status_ID = 1\n" +
                " and rq.Property_ID=:" + PROPERTY_ID + "\n");
        if (isFplosBySRP) {
            if (isLv0) {
                stringBuilder.append(" and rq.Rate_Qualified_id=:" + SPECIAL_SRP_ID + " \n");
            } else {
                stringBuilder.append(" and rq.Rate_Qualified_id!=:" + SPECIAL_SRP_ID + " \n");
            }
        }
        stringBuilder.append(";");

        if (usePhysicalPropertyMasterClass && isFplosBySRP && !isLv0) {
            stringBuilder.append(createAndPopulateQualifiedRateMasterClassTable(currTableName));
        }

        stringBuilder.append(populateRateAndLRV(currTableName, maxLOS, isFplosBySRP, isLv0));
        return stringBuilder.toString();
    }

    protected String populateRateAndLRV(String currTableName, int maxLOS, boolean isFplosBySRP, boolean isLv0) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("insert into #" + currTableName + "_1a\n")
                .append("select calendar_date as Arrival_DT,#" + currTableName + "_1a_rate_qualified.Accom_Type_ID,#" + currTableName + "_1a_rate_qualified.Rate_Qualified_ID,Rate=case\n")
                .append(" when DATEPART(weekday,calendar_date) = 1 then Sunday\n")
                .append(" when DATEPART(weekday,calendar_date) = 2 then Monday\n")
                .append(" when DATEPART(weekday,calendar_date) = 3 then Tuesday\n")
                .append(" when DATEPART(weekday,calendar_date) = 4 then Wednesday\n")
                .append(" when DATEPART(weekday,calendar_date) = 5 then Thursday\n")
                .append(" when DATEPART(weekday,calendar_date) = 6 then Friday\n")
                .append(" when DATEPART(weekday,calendar_date) = 7 then Saturday\nend");
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append(", lrv" + los + "\n");
        }
        if (isFplosBySRP && !isLv0) {
            stringBuilder.append(", CASE WHEN (Accom_Capacity - Rooms_Sold - Rooms_Not_Avail_Maint - Rooms_Not_Avail_Other) < 0 THEN 0 ELSE (Accom_Capacity - Rooms_Sold - Rooms_Not_Avail_Maint - Rooms_Not_Avail_Other) END as remCap \n");
        }
        stringBuilder.append("from  #" + currTableName + "_1a_rate_qualified\n").append(
                " inner join calendar_dim on calendar_date between #" + currTableName + "_1a_rate_qualified.Start_Date_DT and #" + currTableName + "_1a_rate_qualified.End_Date_DT\n").append(
                " inner join Accom_Activity on #" + currTableName + "_1a_rate_qualified.Accom_Type_ID=Accom_Activity.Accom_Type_ID and calendar_date = Accom_Activity.Occupancy_DT\n").append(
                " inner join #").append(currTableName).append(" on #").append(currTableName).append(".Accom_Class_ID=#" + currTableName + "_1a_rate_qualified.Accom_Class_ID and #").append(currTableName).append(".Arrival_DT = calendar_date\n");
        if (usePhysicalPropertyMasterClass && isFplosBySRP && !isLv0) {
            stringBuilder.append(" inner join #" + currTableName + "_RateQualifiedMasterClass \n")
                    .append(" on #" + currTableName + "_1a_rate_qualified.Accom_Class_ID = #" + currTableName + "_RateQualifiedMasterClass.Master_Class_ID ")
                    .append(" and #" + currTableName + "_1a_rate_qualified.Rate_Qualified_ID = #" + currTableName + "_RateQualifiedMasterClass.Rate_Qualified_ID ");
        }
        stringBuilder.append(
                "where (Accom_Activity.Accom_Capacity > 0   hospitality_rooms_clause_aa ) \n").append(
                " and calendar_date between :" + START_DATE + " and :" + END_DATE + "\n");
        if (generateAgileProductQualifiedFPLOS) {
            stringBuilder.append(" and not exists( select agileProd.Rate_Qualified_ID from Agile_Product_Restriction_Association agileProd where agileProd.Rate_Qualified_ID = #" + currTableName + "_1a_rate_qualified.Rate_Qualified_id); \n");
        }
        if (usePhysicalPropertyMasterClass && isFplosBySRP && !isLv0) {
            stringBuilder.append(";drop table #" + currTableName + "_PhysicalPropertyMaterClass;\n");
            stringBuilder.append("drop table #" + currTableName + "_RateQualifiedMasterClass;");
        }
        return stringBuilder.toString();
    }

    public String createEffectiveRatesTable(String currTableName, int maxLOS, boolean isFplosBySRP, boolean isLv0) {
        StringBuilder stringBuilder = new StringBuilder();

        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append("IF OBJECT_ID('").append(currTableName).append("_1') IS NOT NULL DROP TABLE ").append(currTableName).append("_1;\n");
            stringBuilder.append("CREATE TABLE ").append(currTableName).append("_1(\n");
        } else {
            stringBuilder.append("IF OBJECT_ID('tempdb..#").append(currTableName).append("_1') IS NOT NULL DROP TABLE #").append(currTableName).append("_1;\n");
            stringBuilder.append("CREATE TABLE #").append(currTableName).append("_1(\n");
        }

        stringBuilder.append("\n");
        stringBuilder.append("[Arrival_DT] [date] NOT NULL,\n");
        stringBuilder.append("[Accom_Type_ID] [int] NOT NULL,\n");
        stringBuilder.append("[Rate_Qualified_id] [int] NOT NULL,\n");
        stringBuilder.append("[Rate] [numeric](19,5) NOT NULL,\n");
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("[Rate" + los + "] [numeric](19,5) NOT NULL,\n");
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("[lrv" + los + "] [numeric](19,5) NOT NULL,\n");
        }
        if (isFplosBySRP && !isLv0) {
            for (int los = 1; los <= maxLOS; los++) {
                stringBuilder.append("[remCap" + los + "] [numeric](19,5) NOT NULL,\n");
            }
        }
        stringBuilder.append("\n");
        stringBuilder.append(" CONSTRAINT [PK_").append(currTableName).append("_1] PRIMARY KEY CLUSTERED \n");
        stringBuilder.append("(\n");
        stringBuilder.append("Accom_Type_ID,Arrival_DT,Rate_Qualified_id ASC )\n");
        stringBuilder.append(");\n");
        return stringBuilder.toString();
    }

    @VisibleForTesting
    String createEffectiveRatesTableByLOS(String currTableName, int maxLOS, boolean isFplosBySRP, boolean isLv0) {
        StringBuilder stringBuilder = new StringBuilder();

        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append("IF OBJECT_ID('").append(currTableName).append("_1') IS NOT NULL DROP TABLE ").append(currTableName).append("_1;\n");
            stringBuilder.append("CREATE TABLE ").append(currTableName).append("_1(\n");
        } else {
            stringBuilder.append("IF OBJECT_ID('tempdb..#").append(currTableName).append("_1') IS NOT NULL DROP TABLE #").append(currTableName).append("_1;\n");
            stringBuilder.append("CREATE TABLE #").append(currTableName).append("_1(\n");
        }

        stringBuilder.append("\n");
        stringBuilder.append("[Arrival_DT] [date] NOT NULL,\n");
        stringBuilder.append("[Accom_Type_ID] [int] NOT NULL,\n");
        stringBuilder.append("[Rate_Qualified_id] [int] NOT NULL,\n");
        stringBuilder.append("[LOS] [int] NOT NULL,\n");
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("[Rate" + los + "] [numeric](19,5) NOT NULL,\n");
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("[lrv" + los + "] [numeric](19,5) NOT NULL,\n");
        }
        if (isFplosBySRP && !isLv0) {
            for (int los = 1; los <= maxLOS; los++) {
                stringBuilder.append("[remCap" + los + "] [numeric](19,5) NOT NULL,\n");
            }
        }
        stringBuilder.append("\n");
        stringBuilder.append(" CONSTRAINT [PK_").append(currTableName).append("_1] PRIMARY KEY CLUSTERED \n");
        stringBuilder.append("(\n");
        stringBuilder.append("Arrival_DT,Accom_Type_ID,Rate_Qualified_id, LOS ASC\n");
        stringBuilder
                .append(")\n");
        stringBuilder.append(");\n");
        return stringBuilder.toString();
    }

    public String populateEffectiveRatesTableBasedOnDow(String currTableName, int maxLOS, boolean isFplosBySRP, boolean isLv0) {
        StringBuilder stringBuilder = new StringBuilder();
        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append("insert into " + currTableName + "_1\n");
        } else {
            stringBuilder.append("insert into #" + currTableName + "_1\n");
        }
        stringBuilder.append("select * from( ");
        stringBuilder.append(" select t1.Arrival_DT,t1.Accom_Type_ID,t1.Rate_Qualified_ID,t1.Rate\n");
        stringBuilder.append(", t1.Rate AS Rate1 \n");
        for (int los = 2; los <= maxLOS; los++) {
            stringBuilder.append(", LEAD(Rate, " + (los - 1) + ") OVER (PARTITION BY t1.Accom_Type_ID,t1.Rate_Qualified_ID ORDER BY t1.Arrival_DT) as Rate" + los + " \n");
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append(", t1.lrv" + los + "\n");
        }
        if (isFplosBySRP && !isLv0) {
            stringBuilder.append(" , t1.remCap as remCap1");
            for (int los = 2; los <= maxLOS; los++) {
                stringBuilder.append(", LEAD(remCap, " + (los - 1) + ") OVER (PARTITION BY t1.Accom_Type_ID,t1.Rate_Qualified_ID ORDER BY t1.Arrival_DT) as remCap" + los + " \n");
            }
        }
        stringBuilder.append(FROM).append(currTableName).append("_1a t1) as t2 \n");
        stringBuilder.append("where ");
        stringBuilder.append(" t2.Rate" + maxLOS + " is not null ");

        if (isFplosBySRP && !isLv0) {
            stringBuilder.append(" and t2.remCap" + maxLOS + " is not null ");
        }
        // drop this table immediately afterwards
        stringBuilder.append(";drop table #" + currTableName + "_1a;\n");
        stringBuilder.append("drop table #" + currTableName + "_1a_rate_qualified;");
        stringBuilder.append("drop table #" + currTableName + ";");
        return stringBuilder.toString();
    }

    @VisibleForTesting
    String populateEffectiveRatesTableBasedOnDowByLOS(String currTableName, int maxLOS, boolean isFplosBySRP, boolean isLv0) {
        StringBuilder stringBuilder = new StringBuilder();

        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append("insert into " + currTableName + "_1\n");
        } else {
            stringBuilder.append("insert into #" + currTableName + "_1\n");
        }

        stringBuilder.append(" select t2.Arrival_DT,t2.Accom_Type_ID,t2.Rate_Qualified_ID,temp_bar.LOS\n");
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append(", t2.Rate" + los + "\n");
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append(", t2.lrv" + los + "\n");
        }
        if (isFplosBySRP && !isLv0) {
            for (int los = 1; los <= maxLOS; los++) {
                if (SystemConfig.isUseRemCapFix()) {
                    stringBuilder.append(", case when t2.remCap" + los + " <= 0 then 0.1 else t2.remCap" + los + " end\n");
                } else {
                    stringBuilder.append(", t2.remCap" + los + " \n");
                }
            }
        }
        stringBuilder.append("from(");
        stringBuilder.append(" select t1.Arrival_DT,t1.Accom_Type_ID,t1.Rate_Qualified_ID,t1.Rate\n");
        stringBuilder.append(", t1.Rate AS Rate1 \n");
        for (int los = 2; los <= maxLOS; los++) {
            stringBuilder.append(", LEAD(Rate, " + (los - 1) + ") OVER (PARTITION BY t1.Accom_Type_ID,t1.Rate_Qualified_ID ORDER BY t1.Arrival_DT) as Rate" + los + " \n");
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append(", t1.lrv" + los + "\n");
        }
        if (isFplosBySRP && !isLv0) {
            stringBuilder.append(" , t1.remCap as remCap1");
            for (int los = 2; los <= maxLOS; los++) {
                stringBuilder.append(", LEAD(remCap, " + (los - 1) + ") OVER (PARTITION BY t1.Accom_Type_ID,t1.Rate_Qualified_ID ORDER BY t1.Arrival_DT) as remCap" + los + " \n");
            }
        }
        stringBuilder.append(FROM).append(currTableName).append("_1a t1) as t2 \n");
        stringBuilder.append("inner join #").append(currTableName)
                .append("_bar_value temp_bar on temp_bar.Arrival_DT = t2.arrival_DT and temp_bar.Accom_Type_ID = t2.Accom_Type_ID \n");
        stringBuilder.append("where ");
        stringBuilder.append(" t2.Rate" + maxLOS + " is not null ");

        if (isFplosBySRP && !isLv0) {
            stringBuilder.append(" and t2.remCap" + maxLOS + " is not null ");
        }
        // drop this table immediately afterwards
        stringBuilder.append(";drop table #" + currTableName + "_1a;\n");
        stringBuilder.append("drop table #" + currTableName + "_1a_rate_qualified;");
        stringBuilder.append("drop table #" + currTableName + ";");
        return stringBuilder.toString();
    }

    public String createTempTableForDecisionBarOutput(String currTableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("IF OBJECT_ID('tempdb..#" + currTableName + "_bar_value') IS NOT NULL DROP TABLE #" + currTableName + "_bar_value;\n")
                .append("CREATE TABLE #" + currTableName + "_bar_value(\n")
                .append("    [Arrival_DT] [date] NOT NULL,\n")
                .append("    [Accom_Type_ID] [int] NOT NULL,\n");
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("    [BAR_Rate" + los + "] [numeric](19,5) NOT NULL,\n");
        }
        return stringBuilder.append(" CONSTRAINT [PK_" + currTableName + "_bar_value] PRIMARY KEY CLUSTERED \n")
                .append("(Arrival_DT,Accom_Type_ID ASC)\n")
                .append(");\n").toString();
    }

    public String createTempTableForDecisionBarOutputByLOS(String currTableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("IF OBJECT_ID('tempdb..#" + currTableName + "_bar_value') IS NOT NULL DROP TABLE #" + currTableName + "_bar_value;\n")
                .append("CREATE TABLE #" + currTableName + "_bar_value(\n")
                .append("    [Arrival_DT] [date] NOT NULL,\n")
                .append("    [Accom_Type_ID] [int] NOT NULL,\n")
                .append("    [LOS] [int] NOT NULL,\n");
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("    [BAR_Rate" + los + "] [numeric](19,5) NOT NULL,\n");
        }
        return stringBuilder.append(" CONSTRAINT [PK_" + currTableName + "_bar_value] PRIMARY KEY CLUSTERED \n")
                .append("(Arrival_DT,Accom_Type_ID, LOS ASC)\n")
                .append(");\n").toString();
    }

    public String populateTempTableForDecisionBarOutput(String currTableName, int maxLOS) {
        if (isContinuousPricingEnabled) {
            return populateContinuousPricingRates(maxLOS, currTableName);
        } else {
            if (this.isBarByLos) {
                return populateBarRatesForBarByLos(maxLOS, currTableName);
            } else {
                return populateBarRatesForRateOfTheDay(maxLOS, currTableName);
            }
        }
    }

    public String populateBarRatesForBarByLos(int maxLOS, String currTableName) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("insert into #" + currTableName + "_bar_value\n")
                .append("SELECT Arrival_DT, Accom_Type_ID\n");
        int barLos = 1;
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append(", [" + barLos + "] AS BAR_RATE" + los);
            if (los < barMaxLos) {
                barLos++;
            }
        }
        stringBuilder.append("\n FROM (")
                .append("SELECT D.Arrival_DT as Arrival_DT,at.Accom_Type_ID,LOS")
                .append(" ,CASE DATEPART(WEEKDAY, D.Arrival_DT)")
                .append(" WHEN 1 THEN (rud.Sunday)")
                .append(" WHEN 2 THEN (rud.Monday)")
                .append(" WHEN 3 THEN (rud.Tuesday)")
                .append(" WHEN 4 THEN (rud.Wednesday)")
                .append(" WHEN 5 THEN (rud.Thursday)")
                .append(" WHEN 6 THEN (rud.Friday)")
                .append("WHEN 7 THEN (rud.Saturday) END AS BAR_Rate\n")
                .append(" FROM Decision_Bar_Output as D\n")
                .append(" INNER JOIN Accom_Type as AT ON D.Accom_Class_ID = AT.Accom_Class_ID\n")
                .append(" INNER JOIN Rate_Unqualified_Details AS rud ON rud.Accom_Type_ID = at.Accom_Type_ID AND D.Rate_Unqualified_ID = rud.Rate_Unqualified_ID and D.Arrival_DT BETWEEN rud.Start_Date_DT AND rud.End_Date_DT\n")
                .append(" WHERE D.Arrival_DT BETWEEN :" + START_DATE + " AND :" + END_DATE + "\n")
                .append(" ) as A\n")
                .append(" PIVOT ( MAX(BAR_RATE) FOR los in ([1]");
        for (int los = 2; los <= barMaxLos; los++) {
            stringBuilder.append(" ,[" + los + "]");
        }
        stringBuilder.append(")) as piv\n");
        stringBuilder.append("where [1] is not null");
        for (int los = 2; los <= barMaxLos; los++) {
            stringBuilder.append(" and [" + los + "] is not null");
        }
        if (forceLegacyCardinalityEstimator) {
            stringBuilder.append("\nOPTION (USE HINT ('FORCE_LEGACY_CARDINALITY_ESTIMATION'))");
        }
        stringBuilder.append(";");
        return stringBuilder.toString();
    }

    public String populateBarRatesWithLOS(String currTableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("insert into #" + currTableName + "_bar_value\n")
                .append("SELECT D.Arrival_DT as Arrival_DT,at.Accom_Type_ID,LOS \n");
        int barLos = 1;
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append(" ,CASE DATEPART(WEEKDAY, dateadd(day," + (barLos - 1) + ",D.Arrival_DT))")
                    .append(" WHEN 1 THEN (rud.Sunday)")
                    .append(" WHEN 2 THEN (rud.Monday)")
                    .append(" WHEN 3 THEN (rud.Tuesday)")
                    .append(" WHEN 4 THEN (rud.Wednesday)")
                    .append(" WHEN 5 THEN (rud.Thursday)")
                    .append(" WHEN 6 THEN (rud.Friday)")
                    .append("WHEN 7 THEN (rud.Saturday) END AS BAR_Rate" + los + "\n");
            if (los < barMaxLos) {
                barLos++;
            }
        }
        stringBuilder.append(" FROM Decision_Bar_Output as D\n")
                .append(" INNER JOIN Accom_Type as AT ON D.Accom_Class_ID = AT.Accom_Class_ID\n")
                .append(" INNER JOIN Rate_Unqualified_Details AS rud ON rud.Accom_Type_ID = at.Accom_Type_ID AND D.Rate_Unqualified_ID = rud.Rate_Unqualified_ID and D.Arrival_DT BETWEEN rud.Start_Date_DT AND rud.End_Date_DT\n")
                .append(" WHERE LOS > 0 AND D.Arrival_DT BETWEEN :" + START_DATE + " AND :" + END_DATE);

        if (forceLegacyCardinalityEstimator) {
            stringBuilder.append("\nOPTION (USE HINT ('FORCE_LEGACY_CARDINALITY_ESTIMATION'))");
        }
        stringBuilder.append(";");
        return stringBuilder.toString();
    }

    private String populateContinuousPricingRates(int maxLOS, String currTableName) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("insert into #" + currTableName + "_bar_value\n");
        if (SystemConfig.isFplosCpWithLeadEnabled()) {
            stringBuilder.append(" select * from (")
                    .append(" SELECT CP.Arrival_DT AS Arrival_DT ,CP.Accom_Type_ID AS Accom_Type_ID,CP.Final_BAR AS BAR_Rate1 ");
            for (int los = 2; los <= maxLOS; los++) {
                stringBuilder.append(", LEAD(CP.Final_BAR, " + (los - 1) + ") OVER (PARTITION BY CP.Accom_Type_ID ORDER BY CP.Arrival_DT) AS BAR_Rate" + los);
            }
            stringBuilder.append(" from CP_Decision_Bar_Output AS CP ");

            stringBuilder.append(" where CP.Arrival_DT BETWEEN :" + START_DATE + " AND DATEADD(DAY," + maxLOS + ", :" + END_DATE + ")  AND CP.LOS = -1 AND CP.Final_BAR IS NOT NULL AND CP.Product_ID=1 ");
            stringBuilder.append(" ) CP2 WHERE  CP2.BAR_Rate" + maxLOS + " is not null;");
        } else {
            stringBuilder.append(" SELECT CP.Arrival_DT AS Arrival_DT ,CP.Accom_Type_ID AS Accom_Type_ID,CP.Final_BAR AS BAR_Rate1 ");
            for (int los = 2; los <= maxLOS; los++) {
                stringBuilder.append(" ,CP" + los + ".Final_BAR AS BAR_Rate" + los);
            }
            stringBuilder.append(" from CP_Decision_Bar_Output AS CP ");
            for (int los = 2; los <= maxLOS; los++) {
                stringBuilder.append(" INNER JOIN CP_Decision_Bar_Output AS CP" + los + " ON CP.Accom_Type_ID = CP" + los + ".Accom_Type_ID AND CP" + los + ".Arrival_DT = dateadd(DAY," + (los - 1) + ", CP.Arrival_DT)");
                stringBuilder.append("AND CP" + los + ".LOS = CP.LOS  AND CP" + los + ".Final_BAR IS NOT NULL AND CP.Product_ID=CP" + los + ".Product_ID");
            }
            stringBuilder.append(" where CP.Arrival_DT BETWEEN :" + START_DATE + " AND DATEADD(DAY," + maxLOS + ", :" + END_DATE + ")  AND CP.LOS = -1 AND CP.Final_BAR IS NOT NULL AND CP.Product_ID=1;");
        }
        return stringBuilder.toString();
    }

    private String populateBarRatesForRateOfTheDay(int maxLOS, String currTableName) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("insert into #" + currTableName + "_bar_value\n")
                .append(" SELECT D.Arrival_DT as Arrival_DT,at.Accom_Type_ID,\n")
                .append(" CASE (DATEPART(WEEKDAY, D.Arrival_DT))\n")
                .append(" WHEN 1 THEN (rud.Sunday)\n")
                .append(" WHEN 2 THEN (rud.Monday)\n")
                .append(" WHEN 3 THEN (rud.Tuesday)\n")
                .append(" WHEN 4 THEN (rud.Wednesday)\n")
                .append(" WHEN 5 THEN (rud.Thursday)\n")
                .append(" WHEN 6 THEN (rud.Friday)\n")
                .append(" WHEN 7 THEN (rud.Saturday) END AS BAR_Rate1\n");

        for (int los = 2; los <= maxLOS; los++) {
            stringBuilder.append(" ,CASE DATEPART(WEEKDAY, D" + los + ".Arrival_DT)\n")
                    .append(" WHEN 1 THEN (rud" + los + ".Sunday)\n")
                    .append(" WHEN 2 THEN (rud" + los + ".Monday)\n")
                    .append(" WHEN 3 THEN (rud" + los + ".Tuesday)\n")
                    .append(" WHEN 4 THEN (rud" + los + ".Wednesday)\n")
                    .append(" WHEN 5 THEN (rud" + los + ".Thursday)\n")
                    .append(" WHEN 6 THEN (rud" + los + ".Friday)\n")
                    .append(" WHEN 7 THEN (rud" + los + ".Saturday) END AS BAR_Rate" + los + "\n");
        }
        stringBuilder.append(" FROM Decision_Bar_Output as D\n")
                .append(" INNER JOIN Accom_Type as AT ON D.Accom_Class_ID = AT.Accom_Class_ID\n")
                .append(" INNER JOIN Rate_Unqualified_Details AS rud ON rud.Accom_Type_ID = at.Accom_Type_ID AND D.Rate_Unqualified_ID = rud.Rate_Unqualified_ID and D.Arrival_DT BETWEEN rud.Start_Date_DT AND rud.End_Date_DT\n");
        for (int los = 2; los <= maxLOS; los++) {
            stringBuilder.append(" INNER JOIN Decision_Bar_Output as D" + los + " ON D.Accom_Class_ID = D" + los + ".Accom_Class_ID and D" + los + ".Arrival_DT = dateadd(day, " + (los - 1) + ", D.Arrival_DT) and D.los = D" + los + ".los\n");
            stringBuilder.append(" INNER JOIN Rate_Unqualified_Details AS rud" + los + " ON rud.Accom_Type_ID = rud" + los + ".Accom_Type_ID AND D" + los + ".Rate_Unqualified_ID = rud" + los + ".Rate_Unqualified_ID and dateadd(day," + (los - 1) + ",D.Arrival_DT) BETWEEN rud" + los + ".Start_Date_DT AND rud" + los + ".End_Date_DT\n");
        }
        stringBuilder.append(" WHERE D.Arrival_DT BETWEEN rud.Start_Date_DT AND rud.End_Date_DT\n")
                .append("AND D.Arrival_DT BETWEEN :" + START_DATE + " AND DATEADD(DAY," + maxLOS + ",:" + END_DATE + ") AND D.los = -1\n");
        if (forceLegacyCardinalityEstimator) {
            stringBuilder.append("\nOPTION (USE HINT ('FORCE_LEGACY_CARDINALITY_ESTIMATION'));");
        }
        return stringBuilder.toString();
    }

    private String deleteNullBarRateRecordsFromTempTable(String currTableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();

        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append("delete ").append(currTableName).append("_1\n")
                    .append(" FROM ").append(currTableName).append("_1\n")
                    .append(" inner join #").append(propertyId).append("_RateQualifiedTable rq on ").append(currTableName).append("_1.Rate_Qualified_id = rq.Rate_Qualified_id\n")
                    .append(" left join #").append(currTableName).append("_bar_value on ").append(currTableName).append("_1.arrival_dt = #").append(currTableName).append("_bar_value.arrival_dt and ")
                    .append(currTableName).append("_1.Accom_Type_ID = #").append(currTableName).append("_bar_value.Accom_Type_ID\n");
        } else {
            stringBuilder.append("delete #").append(currTableName).append("_1\n")
                    .append(" " + FROM).append(currTableName).append("_1\n")
                    .append(" inner join #").append(propertyId).append("_RateQualifiedTable rq on #").append(currTableName).append("_1.Rate_Qualified_id = rq.Rate_Qualified_id\n")
                    .append(" left join #").append(currTableName).append("_bar_value on #").append(currTableName).append("_1.arrival_dt = #").append(currTableName).append("_bar_value.arrival_dt and #")
                    .append(currTableName).append("_1.Accom_Type_ID = #").append(currTableName).append("_bar_value.Accom_Type_ID\n");
        }

        if (isLimitTotalSRPRatesApplicable()) {
            stringBuilder.append(" left join Limit_Total_Rate_Qualified ON Limit_Total_Rate_Qualified.Limit_Total_Rate_Qualified_ID = rq.Rate_Qualified_ID \n");
            stringBuilder.append(" where Limit_Total_Rate_Qualified.Limit_Total_Rate_Qualified_ID is null and \n");
        } else {
            stringBuilder.append(" where ");
        }
        stringBuilder.append(" ( #").append(currTableName).append("_bar_value.BAR_Rate1 is null\n");
        IntStream.range(2, maxLOS + 1).forEach(los -> stringBuilder.append(" or #").append(currTableName).append("_bar_value.BAR_Rate" + los + " is null\n"));
        stringBuilder.append(") and ( rq.Rate_Qualified_type_ID=1 or rq.Rate_Qualified_type_ID=2)\n");
        return stringBuilder.toString();
    }

    @SuppressWarnings("squid:S3776")
    String updateTempTableFromDerivedOfBarByLosDecisionsAndRateAdjustments(String currTableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("update temp_fplos set\n");
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder
                    .append(" temp_fplos.rate")
                    .append(los)
                    .append(" =\n")
                    .append("  case\n")
                    .append("   when rq.Rate_Qualified_type_ID = 1 then (temp_bar.BAR_Rate" + los + " * " + los);
            for (int l = 1; l <= los; l++) {
                stringBuilder.append(" + temp_fplos.Rate").append(l);
            }
            stringBuilder
                    .append(")\n")
                    .append("    +\n")
                    .append("    case\n")
                    .append("     when ra1.Net_Value_Type_ID = 1 then ra1.Net_Value\n")
                    .append("     when ra1.Net_Value_Type_ID = 2 then (temp_bar.BAR_Rate" + los + " + temp_fplos.Rate1) * (ra1.Net_Value / 100.0)\n")
                    .append("     when ra1.Net_Value_Type_ID = 3 then - (temp_bar.BAR_Rate" + los + " * " + los);
            for (int l = 1; l <= los; l++) {
                stringBuilder.append(" + temp_fplos.Rate").append(l);
            }
            stringBuilder
                    .append(") + ra1.Net_Value\n")
                    .append("     else 0\n")
                    .append("    end\n");
            for (int l = 1; l <= los; l++) {
                stringBuilder
                        .append("    +\n")
                        .append("    case\n")
                        .append("     when ra2_cost.Net_Value_Type_ID" + l + " = 1 then ra2_cost.Net_Value" + l + "\n")
                        .append("     when ra2_cost.Net_Value_Type_ID" + l + " = 2 then (temp_bar.BAR_Rate" + los + " + temp_fplos.Rate" + l + ") * (ra2_cost.Net_Value" + l + " / 100.0)\n")
                        .append("     when ra2_cost.Net_Value_Type_ID" + l + " = 3 then - (temp_bar.BAR_Rate" + los + " + temp_fplos.Rate" + l + ") + ra2_cost.Net_Value" + l + "\n")
                        .append("     else 0\n")
                        .append("    end\n");
            }
            for (int l = 1; l <= los; l++) {
                stringBuilder
                        .append("    +\n")
                        .append("    case\n")
                        .append("     when ra2_value.Net_Value_Type_ID" + l + " = 1 then ra2_value.Net_Value" + l + "\n")
                        .append("     when ra2_value.Net_Value_Type_ID" + l + " = 2 then (temp_bar.BAR_Rate" + los + " + temp_fplos.Rate" + l + ") * (ra2_value.Net_Value" + l + " / 100.0)\n")
                        .append("     when ra2_value.Net_Value_Type_ID" + l + " = 3 then - (temp_bar.BAR_Rate" + los + " + temp_fplos.Rate" + l + ") + ra2_value.Net_Value" + l + "\n")
                        .append("     else 0\n")
                        .append("    end\n");
            }
            stringBuilder
                    .append("   when rq.Rate_Qualified_type_ID = 2 then (temp_bar.BAR_Rate" + los + " * (" + los + " + (temp_fplos.Rate1");
            for (int l = 2; l <= los; l++) {
                stringBuilder.append(" + temp_fplos.Rate").append(l);
            }
            stringBuilder
                    .append(") / 100.0))\n");
            stringBuilder
                    .append("    +\n")
                    .append("    case\n")
                    .append("     when ra1.Net_Value_Type_ID = 1 then ra1.Net_Value\n")
                    .append("     when ra1.Net_Value_Type_ID = 2 then (temp_bar.BAR_Rate" + los + " * (1 + temp_fplos.Rate1 / 100.0)) * (ra1.Net_Value / 100.0)\n")
                    .append("     when ra1.Net_Value_Type_ID = 3 then - (temp_bar.BAR_Rate" + los + " * (" + los + " + (temp_fplos.Rate1");
            for (int l = 2; l <= los; l++) {
                stringBuilder.append(" + temp_fplos.Rate").append(l);
            }
            stringBuilder
                    .append(") / 100.0)) + ra1.Net_Value\n")
                    .append("     else 0\n")
                    .append("    end\n");
            for (int l = 1; l <= los; l++) {
                stringBuilder
                        .append("    +\n")
                        .append("    case\n")
                        .append("     when ra2_cost.Net_Value_Type_ID" + l + " = 1 then ra2_cost.Net_Value" + l + "\n")
                        .append("     when ra2_cost.Net_Value_Type_ID" + l + " = 2 then (temp_bar.BAR_Rate" + los + " * (1 + temp_fplos.Rate" + l + " / 100.0)) * (ra2_cost.Net_Value" + l + " / 100.0)\n")
                        .append("     when ra2_cost.Net_Value_Type_ID" + l + " = 3 then - (temp_bar.BAR_Rate" + los + " * (1 + temp_fplos.Rate" + l + " / 100.0)) + ra2_cost.Net_Value" + l + "\n")
                        .append("     else 0\n")
                        .append("    end\n");
            }
            for (int l = 1; l <= los; l++) {
                stringBuilder
                        .append("    +\n")
                        .append("    case\n")
                        .append("     when ra2_value.Net_Value_Type_ID" + l + " = 1 then ra2_value.Net_Value" + l + "\n")
                        .append("     when ra2_value.Net_Value_Type_ID" + l + " = 2 then (temp_bar.BAR_Rate" + los + " * (1 + temp_fplos.Rate" + l + " / 100.0)) * (ra2_value.Net_Value" + l + " / 100.0)\n")
                        .append("     when ra2_value.Net_Value_Type_ID" + l + " = 3 then - (temp_bar.BAR_Rate" + los + " * (1 + temp_fplos.Rate" + l + " / 100.0)) + ra2_value.Net_Value" + l + "\n")
                        .append("     else 0\n")
                        .append("    end\n");
            }
            stringBuilder.append("  end\n");
            if (los != maxLOS) {
                stringBuilder.append(" ,\n");
            }
        }

        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append("FROM " + currTableName + "_1 temp_fplos\n");
        } else {
            stringBuilder.append(FROM + currTableName + "_1 temp_fplos\n");
        }

        stringBuilder.append("INNER JOIN #" + currTableName + "_bar_value temp_bar\n")
                .append(" on temp_bar.Arrival_DT = temp_fplos.arrival_DT\n")
                .append(" and temp_bar.Accom_Type_ID = temp_fplos.Accom_Type_ID\n")
                .append("INNER JOIN #").append(propertyId).append("_RateQualifiedTable rq\n")
                .append(" on rq.Rate_Qualified_ID = temp_fplos.Rate_Qualified_id\n")
                .append("LEFT JOIN ")
                .append(honorYieldAsYieldableCostAdjustment ? RATE_QUALIFIED_ADJUSTMENT_FINAL : RATE_QUALIFIED_ADJUSTMENT)
                .append(" ra1\n")
                .append(" on temp_fplos.Rate_Qualified_id = ra1.Rate_Qualified_ID\n")
                .append(" and temp_fplos.Arrival_DT between ra1.Start_Date_DT and ra1.End_Date_DT\n")
                .append(" and ra1.Posting_Rule_ID = 1\n")

                .append("LEFT JOIN #" + currTableName + "_RateAdjustment ra2_cost\n")
                .append(" on temp_fplos.Rate_Qualified_id = ra2_cost.Rate_Qualified_ID\n")
                .append(" and temp_fplos.Arrival_DT = ra2_cost.Arrival_DT\n")
                .append(" and ra2_cost.AdjustmentType = 'YieldableCost'\n")
                .append("LEFT JOIN #" + currTableName + "_RateAdjustment ra2_value\n")
                .append(" on temp_fplos.Rate_Qualified_id = ra2_value.Rate_Qualified_ID\n")
                .append(" and temp_fplos.Arrival_DT = ra2_value.Arrival_DT\n")
                .append(" and ra2_value.AdjustmentType = 'YieldableValue'\n")
                .append(excludeYieldableValueAdjustment ? " and ra2_value.AdjustmentType <> 'YieldableValue'" : "")
        ;
        if (isLimitTotalSRPRatesApplicable()) {
            stringBuilder.append(" LEFT JOIN Limit_Total_Rate_Qualified lt ON lt.Limit_Total_Rate_Qualified_ID = rq.Rate_Qualified_ID \n");
            stringBuilder.append(" where lt.Limit_Total_Rate_Qualified_ID is null and \n");
        } else {
            stringBuilder.append(" where");
        }
        stringBuilder.append(" rq.Rate_Qualified_type_ID in (1, 2);\n");
        return stringBuilder.toString();
    }


    String updateTempTableForDerivedRatesAndRateAdjustments(String currTableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(";WITH Bar_By_LOS_CTE AS (\n  SELECT temp_fplos.arrival_DT,\n temp_fplos.Accom_Type_ID,\n temp_fplos.Rate_Qualified_id,\n");
        int barLosCount = 1;
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder
                    .append(" rate" + los + " = max(\n")
                    .append("  case\n")
                    .append("   when rq.Rate_Qualified_type_ID = 1 and LOS =" + barLosCount + " then ( temp_bar.BAR_Rate1 + temp_fplos.Rate1");
            for (int l = 2; l <= los; l++) {
                stringBuilder.append(" + temp_bar.BAR_Rate" + l + " + temp_fplos.Rate" + l);
            }
            stringBuilder
                    .append(")\n")
                    .append("    +\n")
                    .append("    case\n")
                    .append("     when ra1.Net_Value_Type_ID = 1 then ra1.Net_Value\n")
                    .append("     when ra1.Net_Value_Type_ID = 2 then (temp_bar.BAR_Rate1 + temp_fplos.Rate1) * (ra1.Net_Value / 100.0)\n")
                    .append("     when ra1.Net_Value_Type_ID = 3 then - (temp_bar.BAR_Rate1 + temp_fplos.Rate1) + ra1.Net_Value\n")
                    .append("     else 0\n")
                    .append("    end\n");
            populateYieldableValueQuery(stringBuilder, los);
            populateYieldableCostQuery(stringBuilder, los);
            stringBuilder
                    .append("   when rq.Rate_Qualified_type_ID = 2 and LOS =" + barLosCount + " then ((temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0))");
            for (int l = 2; l <= los; l++) {
                stringBuilder.append(" + (temp_bar.BAR_Rate" + l + " * (1 + temp_fplos.Rate" + l + " / 100.0))");
            }

            stringBuilder.append(")\n")
                    .append("    +\n")
                    .append("    case\n")
                    .append("     when ra1.Net_Value_Type_ID = 1 then ra1.Net_Value\n")
                    .append("     when ra1.Net_Value_Type_ID = 2 then (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0) * (ra1.Net_Value / 100.0))\n")
                    .append("     when ra1.Net_Value_Type_ID = 3 then - (temp_bar.BAR_Rate1 * (1 + temp_fplos.Rate1 / 100.0)) + ra1.Net_Value\n")
                    .append("     else 0\n")
                    .append("    end\n");
            populateYieldableCostForDerivedPercentage(stringBuilder, los);
            populateYieldableValueForDerivedPercentage(stringBuilder, los);
            stringBuilder.append("  end)\n");
            if (los != maxLOS) {
                stringBuilder.append(" ,\n");
            }
            if (los < barMaxLos) {
                barLosCount++;
            }
        }

        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append("FROM " + currTableName + "_1 temp_fplos\n");
        } else {
            stringBuilder.append(FROM + currTableName + "_1 temp_fplos\n");
        }

        stringBuilder.append("INNER JOIN #" + currTableName + "_bar_value temp_bar\n")
                .append(" on temp_bar.Arrival_DT = temp_fplos.arrival_DT\n")
                .append(" and temp_bar.Accom_Type_ID = temp_fplos.Accom_Type_ID\n")
                .append("INNER JOIN #").append(propertyId).append("_RateQualifiedTable rq\n")
                .append(" on rq.Rate_Qualified_ID = temp_fplos.Rate_Qualified_id\n")
                .append("LEFT JOIN ")
                .append(honorYieldAsYieldableCostAdjustment ? RATE_QUALIFIED_ADJUSTMENT_FINAL : RATE_QUALIFIED_ADJUSTMENT)
                .append(" ra1\n")
                .append(" on temp_fplos.Rate_Qualified_id = ra1.Rate_Qualified_ID\n")
                .append(" and temp_fplos.Arrival_DT between ra1.Start_Date_DT and ra1.End_Date_DT\n")
                .append(" and ra1.Posting_Rule_ID = 1\n")
                .append("LEFT JOIN #" + currTableName + "_RateAdjustment ra2_cost\n")
                .append(" on temp_fplos.Rate_Qualified_id = ra2_cost.Rate_Qualified_ID\n")
                .append(" and temp_fplos.Arrival_DT = ra2_cost.Arrival_DT\n")
                .append(" and ra2_cost.AdjustmentType = 'YieldableCost'\n")
                .append("LEFT JOIN #" + currTableName + "_RateAdjustment ra2_value\n")
                .append(" on temp_fplos.Rate_Qualified_id = ra2_value.Rate_Qualified_ID\n")
                .append(" and temp_fplos.Arrival_DT = ra2_value.Arrival_DT\n")
                .append(" and ra2_value.AdjustmentType = 'YieldableValue'\n");
        if (isLimitTotalSRPRatesApplicable()) {
            stringBuilder.append(" LEFT JOIN Limit_Total_Rate_Qualified lt ON lt.Limit_Total_Rate_Qualified_ID = rq.Rate_Qualified_ID \n");
            stringBuilder.append(" where lt.Limit_Total_Rate_Qualified_ID is null and \n");
        } else {
            stringBuilder.append(" where");
        }
        stringBuilder.append(" rq.Rate_Qualified_type_ID in (1, 2)\n")
                .append(" group by temp_fplos.arrival_DT,\n")
                .append(" temp_fplos.Accom_Type_ID,\n")
                .append(" temp_fplos.Rate_Qualified_id)\n");

        updateFromCTETable(maxLOS, stringBuilder);

        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append(" FROM " + currTableName + "_1 temp_fplos2 \n");
        } else {
            stringBuilder.append(" " + FROM + currTableName + "_1 temp_fplos2 \n");
        }

        stringBuilder.append("INNER JOIN Bar_By_LOS_CTE byLosCTE on \n")
                .append("temp_fplos2.arrival_DT = byLosCTE.arrival_DT and \n")
                .append("temp_fplos2.Rate_Qualified_id = byLosCTE.Rate_Qualified_id and \n")
                .append("temp_fplos2.Accom_Type_ID = byLosCTE.Accom_Type_ID; \n");

        return stringBuilder.toString();
    }

    @VisibleForTesting
    void updateFromCTETable(int maxLOS, StringBuilder stringBuilder) {
        stringBuilder.append("update temp_fplos2 set \n");
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("temp_fplos2.rate" + los + " = byLosCTE.rate" + los + "");
            if (los != maxLOS) {
                stringBuilder.append(",\n");
            }
        }
    }

    private void populateYieldableValueForDerivedPercentage(StringBuilder stringBuilder, int los) {
        for (int l = 1; l <= los; l++) {
            stringBuilder
                    .append("    +\n")
                    .append("    case\n")
                    .append("     when ra2_value.Net_Value_Type_ID" + l + " = 1 then ra2_value.Net_Value" + l + "\n")
                    .append("     when ra2_value.Net_Value_Type_ID" + l + " = 2 then (temp_bar.BAR_Rate" + l + " * (1 + temp_fplos.Rate" + l + " / 100.0)) * (ra2_value.Net_Value" + l + " / 100.0)\n")
                    .append("     when ra2_value.Net_Value_Type_ID" + l + " = 3 then - (temp_bar.BAR_Rate" + l + " * (1 + temp_fplos.Rate" + l + " / 100.0)) + ra2_value.Net_Value" + l + "\n")
                    .append("     else 0\n")
                    .append("    end\n");
        }
    }

    private void populateYieldableCostForDerivedPercentage(StringBuilder stringBuilder, int los) {
        for (int l = 1; l <= los; l++) {
            stringBuilder
                    .append("    +\n")
                    .append("    case\n")
                    .append("     when ra2_cost.Net_Value_Type_ID" + l + " = 1 then ra2_cost.Net_Value" + l + "\n")
                    .append("     when ra2_cost.Net_Value_Type_ID" + l + " = 2 then (temp_bar.BAR_Rate" + l + " * (1 + temp_fplos.Rate" + l + " / 100.0)) * (ra2_cost.Net_Value" + l + " / 100.0)\n")
                    .append("     when ra2_cost.Net_Value_Type_ID" + l + " = 3 then - (temp_bar.BAR_Rate" + l + " * (1 + temp_fplos.Rate" + l + " / 100.0)) + ra2_cost.Net_Value" + l + "\n")
                    .append("     else 0\n")
                    .append("    end\n");
        }
    }

    private void populateYieldableCostQuery(StringBuilder stringBuilder, int los) {
        for (int l = 1; l <= los; l++) {
            stringBuilder
                    .append("    +\n")
                    .append("    case\n")
                    .append("     when ra2_value.Net_Value_Type_ID" + l + " = 1 then ra2_value.Net_Value" + l + "\n")
                    .append("     when ra2_value.Net_Value_Type_ID" + l + " = 2 then (temp_bar.BAR_Rate" + l + " + temp_fplos.Rate" + l + ") * (ra2_value.Net_Value" + l + " / 100.0)\n")
                    .append("     when ra2_value.Net_Value_Type_ID" + l + " = 3 then - (temp_bar.BAR_Rate" + l + " + temp_fplos.Rate" + l + ") + ra2_value.Net_Value" + l + "\n")
                    .append("     else 0\n")
                    .append("    end\n");
        }
    }

    private void populateYieldableValueQuery(StringBuilder stringBuilder, int los) {
        for (int l = 1; l <= los; l++) {
            stringBuilder
                    .append("    +\n")
                    .append("    case\n")
                    .append("     when ra2_cost.Net_Value_Type_ID" + l + " = 1 then ra2_cost.Net_Value" + l + "\n")
                    .append("     when ra2_cost.Net_Value_Type_ID" + l + " = 2 then (temp_bar.BAR_Rate" + l + " + temp_fplos.Rate" + l + ") * (ra2_cost.Net_Value" + l + " / 100.0)\n")
                    .append("     when ra2_cost.Net_Value_Type_ID" + l + " = 3 then - (temp_bar.BAR_Rate" + l + " + temp_fplos.Rate" + l + ") + ra2_cost.Net_Value" + l + "\n")
                    .append("     else 0\n")
                    .append("    end\n");
        }
    }

    String updateTempTableFromDerivedOfBarByLosDecisionsAndRateAdjustmentsForFixedQualifiedRates(String currTableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("update temp_fplos set\n");
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder
                    .append(" temp_fplos.rate" + los + " =\n")
                    .append("   temp_fplos.Rate1\n")
                    .append("   +\n")
                    .append("   case\n")
                    .append("    when ra1.Net_Value_Type_ID = 1 then ra1.Net_Value\n")
                    .append("    when ra1.Net_Value_Type_ID = 2 then temp_fplos.Rate1 * (ra1.Net_Value / 100.0)\n")
                    .append("    when ra1.Net_Value_Type_ID = 3 then - temp_fplos.Rate1 + ra1.Net_Value\n")
                    .append("    else 0\n")
                    .append("   end\n")
                    .append("   +\n")
                    .append("   case\n")
                    .append("    when ra2_cost.Net_Value_Type_ID1 = 1 then ra2_cost.Net_Value1\n")
                    .append("    when ra2_cost.Net_Value_Type_ID1 = 2 then temp_fplos.Rate1 * (ra2_cost.Net_Value1 / 100.0)\n")
                    .append("    when ra2_cost.Net_Value_Type_ID1 = 3 then - temp_fplos.Rate1 + ra2_cost.Net_Value1\n")
                    .append("    else 0\n")
                    .append("   end\n")
                    .append("   +\n")
                    .append("   case\n")
                    .append("    when ra2_value.Net_Value_Type_ID1 = 1 then ra2_value.Net_Value1\n")
                    .append("    when ra2_value.Net_Value_Type_ID1 = 2 then temp_fplos.Rate1 * (ra2_value.Net_Value1 / 100.0)\n")
                    .append("    when ra2_value.Net_Value_Type_ID1 = 3 then - temp_fplos.Rate1 + ra2_value.Net_Value1\n")
                    .append("    else 0\n")
                    .append("   end\n");
            for (int l = 2; l <= los; l++) {
                stringBuilder
                        .append("   +\n")
                        .append("   temp_fplos.Rate" + l + "\n")
                        .append("   +\n")
                        .append("   case\n")
                        .append("    when ra2_cost.Net_Value_Type_ID" + l + " = 1 then ra2_cost.Net_Value" + l + "\n")
                        .append("    when ra2_cost.Net_Value_Type_ID" + l + " = 2 then temp_fplos.Rate" + l + " * (ra2_cost.Net_Value" + l + " / 100.0)\n")
                        .append("    when ra2_cost.Net_Value_Type_ID" + l + " = 3 then - temp_fplos.Rate" + l + " + ra2_cost.Net_Value" + l + "\n")
                        .append("    else 0\n")
                        .append("   end\n")
                        .append("   +\n")
                        .append("   case\n")
                        .append("    when ra2_value.Net_Value_Type_ID" + l + " = 1 then ra2_value.Net_Value" + l + "\n")
                        .append("    when ra2_value.Net_Value_Type_ID" + l + " = 2 then temp_fplos.Rate" + l + " * (ra2_value.Net_Value" + l + " / 100.0)\n")
                        .append("    when ra2_value.Net_Value_Type_ID" + l + " = 3 then - temp_fplos.Rate" + l + " + ra2_value.Net_Value" + l + "\n")
                        .append("    else 0\n")
                        .append("   end\n");
            }
            if (los != maxLOS) {
                stringBuilder.append(" ,\n");
            }
        }
        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append("FROM " + currTableName + "_1 temp_fplos\n");
        } else {
            stringBuilder.append(FROM + currTableName + "_1 temp_fplos\n");
        }

        stringBuilder.append("INNER JOIN #").append(propertyId).append("_RateQualifiedTable rq\n")
                .append(" on rq.Rate_Qualified_ID = temp_fplos.Rate_Qualified_id\n")
                .append("LEFT JOIN ")
                .append(honorYieldAsYieldableCostAdjustment ? RATE_QUALIFIED_ADJUSTMENT_FINAL : RATE_QUALIFIED_ADJUSTMENT)
                .append(" ra1\n")
                .append(" on temp_fplos.Rate_Qualified_id = ra1.Rate_Qualified_ID\n")
                .append(" and temp_fplos.Arrival_DT between ra1.Start_Date_DT and ra1.End_Date_DT\n")
                .append(" and ra1.Posting_Rule_ID = 1\n")
                .append("LEFT JOIN #" + currTableName + "_RateAdjustment ra2_cost\n")
                .append(" on temp_fplos.Rate_Qualified_id = ra2_cost.Rate_Qualified_ID\n")
                .append(" and temp_fplos.Arrival_DT = ra2_cost.Arrival_DT\n")
                .append(" and ra2_cost.AdjustmentType = 'YieldableCost'\n")
                .append("LEFT JOIN #" + currTableName + "_RateAdjustment ra2_value\n")
                .append(" on temp_fplos.Rate_Qualified_id = ra2_value.Rate_Qualified_ID\n")
                .append(" and temp_fplos.Arrival_DT = ra2_value.Arrival_DT\n")
                .append(" and ra2_value.AdjustmentType = 'YieldableValue'\n");
        if (isLimitTotalSRPRatesApplicable()) {
            stringBuilder.append(" left join Limit_Total_Rate_Qualified lt ON lt.Limit_Total_Rate_Qualified_ID = rq.Rate_Qualified_ID \n");
            stringBuilder.append(" where lt.Limit_Total_Rate_Qualified_ID is null and \n");
        } else {
            stringBuilder.append(" where");
        }
        stringBuilder.append(" rq.Rate_Qualified_type_ID = 3;\n");
        return stringBuilder.toString();
    }


    public String updateTempTableForDecisionBarOutput(String currTableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("update temp_fplos set \n");
        for (int los = 1; los <= maxLOS; los++) {
            if (this.isBarByLos) {
                stringBuilder.append(" temp_fplos.rate").append(los).append(" = case when rq.Rate_Qualified_type_ID=1 then (temp_bar.BAR_Rate" + los + " * " + los);
                IntStream.range(1, los + 1).forEach(l -> stringBuilder.append(" + temp_fplos.Rate" + l));
                stringBuilder.append(")\n");
                stringBuilder.append(" when rq.Rate_Qualified_type_ID=2 then (temp_bar.BAR_Rate" + los + " * (" + los + " + (temp_fplos.Rate1");
                IntStream.range(2, los + 1).forEach(l -> stringBuilder.append(" + temp_fplos.Rate" + l));
                stringBuilder.append(") / 100.0))\n");
                stringBuilder.append(" else temp_fplos.Rate1");
                IntStream.range(2, los + 1).forEach(l -> stringBuilder.append(" + temp_fplos.Rate" + l));
                stringBuilder.append(" end,\n");
            } else {
                stringBuilder.append(" temp_fplos.rate").append(los).append(" = case when rq.Rate_Qualified_type_ID=1 then temp_bar.BAR_Rate" + los + " + temp_fplos.Rate" + los + "\n")
                        .append(" when rq.Rate_Qualified_type_ID=2 then temp_bar.BAR_Rate" + los + " + ((temp_bar.BAR_Rate" + los + " * temp_fplos.Rate" + los + ")/100)\n")
                        .append(" else temp_fplos.rate" + los + " end,\n");
            }
        }
        stringBuilder.setLength(stringBuilder.length() - 2);

        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append("\n FROM ").append(currTableName).append("_1 temp_fplos\n");
        } else {
            stringBuilder.append("\n " + FROM).append(currTableName).append("_1 temp_fplos\n");
        }

        stringBuilder.append(" INNER JOIN #").append(currTableName).append("_BAR_Value temp_bar on temp_bar.Arrival_DT = temp_fplos.arrival_DT and temp_bar.Accom_Type_ID = temp_fplos.Accom_Type_ID\n")
                .append(" INNER JOIN #").append(propertyId).append("_RateQualifiedTable rq on rq.Rate_Qualified_ID = temp_fplos.Rate_Qualified_id\n");
        if (isLimitTotalSRPRatesApplicable()) {
            stringBuilder.append(" LEFT JOIN Limit_Total_Rate_Qualified lt ON lt.Limit_Total_Rate_Qualified_ID = rq.Rate_Qualified_ID \n");
            stringBuilder.append(" where lt.Limit_Total_Rate_Qualified_ID is null; \n");
        }
        return stringBuilder.toString();
    }

    public String createTempRateAdjustment(String currTableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("IF OBJECT_ID('tempdb..#").append(currTableName).append("_RateAdjustment') IS NOT NULL DROP TABLE #").append(currTableName).append("_RateAdjustment;\n");
        stringBuilder.append("CREATE TABLE #").append(currTableName).append("_RateAdjustment(\n");
        stringBuilder.append("          [Arrival_DT] [date] NOT NULL,\n");
        stringBuilder.append("          [Rate_Qualified_id] [int] NOT NULL,\n");
        stringBuilder.append("          [AdjustmentType] [varchar](50) NOT NULL,\n");
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("          [Net_Value" + los + "] [numeric](19, 5),\n");
            stringBuilder.append("          [Net_Value_Type_ID" + los + "] [int],\n");
        }
        stringBuilder.append(" CONSTRAINT [PK_").append(currTableName).append("_RateAdjustment] PRIMARY KEY CLUSTERED ([Arrival_DT],[Rate_Qualified_ID],[AdjustmentType] ASC )\n");
        stringBuilder.append(" );\n");
        return stringBuilder.toString();
    }

    public String createTempRateAdjustmentTypeTable(String currTableName) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("IF OBJECT_ID('tempdb..#").append(currTableName).append("_RateAdjustmentType') IS NOT NULL DROP TABLE #").append(currTableName).append("_RateAdjustmentType;\n");
        stringBuilder.append("CREATE TABLE #").append(currTableName).append("_RateAdjustmentType(\n");
        stringBuilder.append("[AdjustmentType] [varchar](20) NOT NULL);\n");
        return stringBuilder.toString();
    }

    public String populateTempRateAdjustmentTypeTable(String currTableName) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("insert into #").append(currTableName).append("_RateAdjustmentType\n");
        stringBuilder.append("values ('YieldableValue'), ('YieldableCost');\n");
        return stringBuilder.toString();
    }

    public String populateTempRateAdjustment(String currTableName, int maxLOS, boolean calculateAtTotalLevel) {
        return useWindowFunctionForFPLOSBatchRateAdjustmentQueryOptimization() ?
                populateTempRateAdjustmentWithOptimizedQuery(currTableName, maxLOS, calculateAtTotalLevel)
                : populateTempRateAdjustmentOldQuery(currTableName, maxLOS, calculateAtTotalLevel);
    }

    private String populateTempRateAdjustmentWithOptimizedQuery(String currTableName, int maxLOS, boolean calculateAtTotalLevel) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("\ninsert into #").append(currTableName).append("_RateAdjustment");
        stringBuilder.append("\nselect Arrival_DT, Rate_Qualified_ID, AdjustmentType, ");
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("\n\t\tNet_Value" + los + ", Net_Value_Type_ID" + los).append((los == maxLOS) ? "" : ",");
        }
        stringBuilder.append("\nfrom (\n" +
                "\t\tselect cal.calendar_date as Arrival_DT, rq.Rate_Qualified_ID as Rate_Qualified_ID, rat.AdjustmentType as AdjustmentType,");
        for (int los = 0; los < maxLOS; los++) {
            stringBuilder.append("\n\t\t\tlead(adj.Net_Value," + los + ") over (partition by rq.Rate_Qualified_ID, rat.AdjustmentType order by cal.calendar_date) as Net_Value" + (los + 1) + " , lead(adj.Net_Value_Type_ID," + los + ") over (partition by rq.Rate_Qualified_ID , rat.AdjustmentType order by cal.calendar_date) as Net_Value_Type_ID" + (los + 1)).append((los == maxLOS - 1) ? "" : ",");
        }
        stringBuilder.append("\n\t\tfrom calendar_dim cal");
        stringBuilder.append("\n\t\t\tcross join #").append(propertyId).append("_RateQualifiedTable rq");
        stringBuilder.append("\ncross join #").append(currTableName).append("_RateAdjustmentType rat");
        stringBuilder.append("\n\t\t\tleft join ").append(honorYieldAsYieldableCostAdjustment ? RATE_QUALIFIED_ADJUSTMENT_FINAL : RATE_QUALIFIED_ADJUSTMENT).append(
                " adj on cal.calendar_date between adj.Start_Date_DT and adj.End_Date_DT and rq.Rate_Qualified_ID = adj.Rate_Qualified_ID and rat.AdjustmentType = adj.AdjustmentType and adj.Posting_Rule_ID = 2 ");
        if (excludeYieldableValueAdjustment) {
            stringBuilder.append(" and adj.adjustmentType <> 'YieldableValue' ");
        }

        if (isLimitTotalSRPRatesApplicable()) {
            if (calculateAtTotalLevel) {
                stringBuilder.append(" inner join Limit_Total_Rate_Qualified lt ON lt.Limit_Total_Rate_Qualified_ID = rq.Rate_Qualified_ID \n");
                stringBuilder.append(" where ");
            } else {
                stringBuilder.append(" left join Limit_Total_Rate_Qualified lt ON lt.Limit_Total_Rate_Qualified_ID = rq.Rate_Qualified_ID \n");
                stringBuilder.append(" where lt.Limit_Total_Rate_Qualified_ID is null and \n");
            }
        } else {
            stringBuilder.append("\n\t\twhere");
        }
        stringBuilder.append(" rq.Rate_Qualified_ID is not null " +
                "\n\t\t\t\tand calendar_date between :startDate and dateadd(day," + maxLOS + ",:endDate)" +
                "\n) as final " +
                "\nwhere final.Arrival_DT between :startDate and :endDate ");
        stringBuilder.append("\nand coalesce(Net_Value1");
        for (int los = 2; los <= maxLOS; los++) {
            stringBuilder.append(",Net_Value" + los);
        }
        stringBuilder.append(") is not null\n");
        return stringBuilder.toString();
    }

    public String populateTempRateAdjustmentOldQuery(String currTableName, int maxLOS, boolean calculateAtTotalLevel) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("insert into #").append(currTableName).append("_RateAdjustment");
        stringBuilder.append("\nselect cal.calendar_date as Arrival_DT, rq.Rate_Qualified_ID as Rate_Qualified_ID, rat.AdjustmentType as AdjustmentType");
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("\n, adj" + los + ".Net_Value as Net_Value" + los + ", adj" + los + ".Net_Value_Type_ID as Net_Value_Type_ID" + los);
        }
        stringBuilder.append("\nfrom calendar_dim cal");
        stringBuilder.append("\ncross join #").append(propertyId).append("_RateQualifiedTable rq");
        stringBuilder.append("\ncross join #").append(currTableName).append("_RateAdjustmentType rat");
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("\nleft join ")
                    .append(honorYieldAsYieldableCostAdjustment ? RATE_QUALIFIED_ADJUSTMENT_FINAL : RATE_QUALIFIED_ADJUSTMENT + " adj" + los + " on dateadd(day, " + (los - 1) + ", cal.calendar_date) between adj" + los + ".Start_Date_DT and adj" + los + ".End_Date_DT");
            stringBuilder.append("\nand rq.Rate_Qualified_ID = adj" + los + ".Rate_Qualified_ID");
            stringBuilder.append("\nand rat.AdjustmentType = adj" + los + ".AdjustmentType");
            stringBuilder.append("\nand adj" + los + ".Posting_Rule_ID = 2");
            if (excludeYieldableValueAdjustment) {
                stringBuilder.append(" and adj" + los + ".adjustmentType <> 'YieldableValue'");
            }
        }
        if (isLimitTotalSRPRatesApplicable()) {
            if (calculateAtTotalLevel) {
                stringBuilder.append(" inner join Limit_Total_Rate_Qualified lt ON lt.Limit_Total_Rate_Qualified_ID = rq.Rate_Qualified_ID \n");
                stringBuilder.append(" where ");
            } else {
                stringBuilder.append(" left join Limit_Total_Rate_Qualified lt ON lt.Limit_Total_Rate_Qualified_ID = rq.Rate_Qualified_ID \n");
                stringBuilder.append(" where lt.Limit_Total_Rate_Qualified_ID is null and \n");
            }
        } else {
            stringBuilder.append("\nwhere ");
        }
        stringBuilder.append("calendar_date between :" + START_DATE + " and :" + END_DATE + "");
        stringBuilder.append("\nand coalesce(adj1.Rate_Qualified_ID");
        for (int los = 2; los <= maxLOS; los++) {
            stringBuilder.append(", adj" + los + ".Rate_Qualified_ID");
        }
        stringBuilder.append(") is not null;\n");
        return stringBuilder.toString();
    }


    public String udateEffectiveRatesPerStay(String currTableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();

        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append("update " + currTableName + "_1 set\n");
        } else {
            stringBuilder.append("update #" + currTableName + "_1 set\n");
        }

        stringBuilder.append("Rate1 = case\n");
        stringBuilder.append("when Net_Value_Type_ID = 1 then Rate1 + Net_Value\n");
        stringBuilder.append("when Net_Value_Type_ID = 2 then Rate1 + ((Rate1 * Net_Value) / 100.0)\n");
        stringBuilder.append("when Net_Value_Type_ID = 3 then Net_Value\n");
        stringBuilder.append("end\n");
        for (int los = 2; los <= maxLOS; los++) {
            stringBuilder.append(",\n");
            stringBuilder.append("Rate" + los + " = case\n");
            stringBuilder.append("when Net_Value_Type_ID = 3 then 0\n");
            stringBuilder.append("else Rate" + los + "\n");
            stringBuilder.append("end\n");
        }

        stringBuilder.append("from\n");

        var rateQualifiedAdjustmentTable = honorYieldAsYieldableCostAdjustment ? "Rate_Qualified_Adjustment_final Rate_Qualified_Adjustment" : "Rate_Qualified_Adjustment";
        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append(currTableName).append("_1 inner join " + rateQualifiedAdjustmentTable);
            stringBuilder.append("\non ").append(currTableName).append("_1.Rate_Qualified_id = Rate_Qualified_Adjustment.Rate_Qualified_ID\n");
            stringBuilder.append("and ").append(currTableName).append("_1.Arrival_DT between Rate_Qualified_Adjustment.Start_Date_DT and Rate_Qualified_Adjustment.End_Date_DT\n");
        } else {
            stringBuilder.append("#" + currTableName + "_1 inner join " + rateQualifiedAdjustmentTable);
            stringBuilder.append("\non #" + currTableName + "_1.Rate_Qualified_id = Rate_Qualified_Adjustment.Rate_Qualified_ID\n");
            stringBuilder.append("and #" + currTableName + "_1.Arrival_DT between Rate_Qualified_Adjustment.Start_Date_DT and Rate_Qualified_Adjustment.End_Date_DT\n");
        }

        stringBuilder.append("where Posting_Rule_ID = 1;\n");
        return stringBuilder.toString();
    }

    public String updateEffectiveRatesPerNightByArrivalDateRateAdjustmentForAdjustmentType(String currTableName, int maxLOS, String adjustmentType) {
        StringBuilder stringBuilder = new StringBuilder();
        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append("update " + currTableName + "_1 set\n");
        } else {
            stringBuilder.append("update #" + currTableName + "_1 set\n");
        }

        stringBuilder.append("set Rate1= \n");
        stringBuilder.append("case \n");
        stringBuilder.append("    when Net_Value_Type_ID=1 then Rate1+Net_Value\n");
        stringBuilder.append("    when Net_Value_Type_ID=2 then Rate1+((Rate*Net_Value)/100.0)\n");
        stringBuilder.append("    when Net_Value_Type_ID=3 then Net_Value\n");
        stringBuilder.append("end\n");
        for (int los = 2; los <= maxLOS; los++) {
            stringBuilder.append(",\n");
            stringBuilder.append("Rate" + los + "= \n");
            stringBuilder.append("case \n");
            stringBuilder.append("    when Net_Value_Type_ID=1 then Rate+Net_Value\n");
            stringBuilder.append("    when Net_Value_Type_ID=2 then Rate" + los + "+((Rate*Net_Value)/100.0)\n");
            stringBuilder.append("    when Net_Value_Type_ID=3 then Net_Value\n");
            stringBuilder.append("end\n");
        }
        stringBuilder.append("from \n");

        var rateQualifiedAdjustmentTable = honorYieldAsYieldableCostAdjustment ? "Rate_Qualified_Adjustment_final Rate_Qualified_Adjustment" : "Rate_Qualified_Adjustment";
        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append(currTableName).append("_1 inner join " + rateQualifiedAdjustmentTable + "\n");
            stringBuilder.append("on ").append(currTableName).append("_1.Rate_Qualified_id=Rate_Qualified_Adjustment.Rate_Qualified_ID\n");
            stringBuilder.append("and ").append(currTableName).append("_1.Arrival_DT between Rate_Qualified_Adjustment.Start_Date_DT and Rate_Qualified_Adjustment.End_Date_DT\n");
        } else {
            stringBuilder.append("#" + currTableName + "_1 inner join " + rateQualifiedAdjustmentTable + "\n");
            stringBuilder.append("on #" + currTableName + "_1.Rate_Qualified_id=Rate_Qualified_Adjustment.Rate_Qualified_ID\n");
            stringBuilder.append("and #" + currTableName + "_1.Arrival_DT between Rate_Qualified_Adjustment.Start_Date_DT and Rate_Qualified_Adjustment.End_Date_DT\n");
        }

        stringBuilder.append("where\n");
        stringBuilder.append("Posting_Rule_ID=2 and Rate_Qualified_Adjustment.AdjustmentType='" + adjustmentType + "' ");
        if (excludeYieldableValueAdjustment) {
            stringBuilder.append(" and Rate_Qualified_Adjustment.AdjustmentType<>'YieldableValue'");
        }
        stringBuilder.append(";\n");

        return stringBuilder.toString();
    }

    public String updateEffectiveRatesPerNightForYieldableAdjustmentType(String currTableName, int maxLOS, String adjustmentType) {
        StringBuilder stringBuilder = new StringBuilder();
        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append("update " + currTableName + "_1 set");
        } else {
            stringBuilder.append("update #" + currTableName + "_1 set");
        }
        for (int losVal = 1; losVal <= maxLOS; losVal++) {
            final int los = losVal;
            stringBuilder.append("\nRate" + los + " = ");
            stringBuilder.append("\ncase when Net_Value_Type_ID" + los + " = 1 then Rate" + los + " + Net_Value" + los);
            stringBuilder.append("\nwhen Net_Value_Type_ID" + los + " = 2 then Rate" + los + " + ((Rate" + los + " * Net_Value" + los + ") / 100.0)");
            stringBuilder.append("\nwhen Net_Value_Type_ID" + los + " = 3 then Net_Value" + los);
            stringBuilder.append("\nelse Rate" + los + "");
            stringBuilder.append("\nend");
            if (los < maxLOS) {
                stringBuilder.append(",");
            }
        }

        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append("\nFROM " + currTableName + "_1");
            stringBuilder.append("\ninner join #" + currTableName + "_RateAdjustment ra" +
                    "\non " + currTableName + "_1.Rate_Qualified_id=ra.Rate_Qualified_ID" +
                    "\nand " + currTableName + "_1.Arrival_DT = ra.Arrival_DT");

        } else {
            stringBuilder.append("\n" + FROM + currTableName + "_1");
            stringBuilder.append("\ninner join #" + currTableName + "_RateAdjustment ra" +
                    "\non #" + currTableName + "_1.Rate_Qualified_id=ra.Rate_Qualified_ID" +
                    "\nand #" + currTableName + "_1.Arrival_DT = ra.Arrival_DT");
        }

        stringBuilder.append("\nwhere ra.AdjustmentType='" + adjustmentType + "';\n");
        return stringBuilder.toString();
    }

    // fplosbysrp
    public String createTempWeightedRateTable(String tableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("IF OBJECT_ID('tempdb..#" + tableName + "_1_wrate') IS NOT NULL DROP TABLE #" + tableName + "_1_wrate;\n");
        stringBuilder.append("CREATE TABLE #" + tableName + "_1_wrate(\n");
        stringBuilder.append("[Arrival_DT] [date] NOT NULL,\n");
        stringBuilder.append("[Accom_Type_ID] [int] NOT NULL,\n");
        stringBuilder.append("[Rate_Qualified_id] [int] NOT NULL,\n");
        if (isCorrectedFplosForDerivedRatesAndBarByLOS) {
            stringBuilder.append("[LOS] [int] NOT NULL,\n");
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("[remCap" + los + "] [numeric] (19,5) NOT NULL,\n");
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("[wRate" + los + "] [numeric](19,5) NOT NULL,\n");
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("[lrv" + los + "] [numeric](19,5) NOT NULL,\n");
        }
        stringBuilder.append("\n");
        stringBuilder.append(" CONSTRAINT [PK_" + tableName + "_1_wrate] PRIMARY KEY CLUSTERED \n");
        stringBuilder.append("(\n");
        stringBuilder.append("    Arrival_DT,Accom_Type_ID,Rate_Qualified_id");
        if (isCorrectedFplosForDerivedRatesAndBarByLOS) {
            stringBuilder.append(", LOS");
        }
        stringBuilder.append(" ASC\n");
        stringBuilder.append(")\n");
        stringBuilder.append(");\n");
        return stringBuilder.toString();
    }

    public String updateRemCapacity(String tableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();

        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append("update " + tableName + "_1 set \n");
        } else {
            stringBuilder.append("update #" + tableName + "_1 set \n");
        }

        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("remCap" + los + " = case when remCap" + los + " <= 0 then 0.1 else remCap" + los + " end");
            if (los < maxLOS) {
                stringBuilder.append(", \n");
            }
        }
        return stringBuilder.append(";\n").toString();
    }

    public String populateTempWeightedRateTable(String tableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("insert into #" + tableName + "_1_wrate\n");
        stringBuilder.append("select Arrival_DT,Accom_Type_ID,Rate_Qualified_id\n");
        if (isCorrectedFplosForDerivedRatesAndBarByLOS) {
            stringBuilder.append(",LOS\n");
        }

        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("    ,remCap" + los + "\n");
        }

        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("    ,Rate" + los + "*remCap" + los + " as wrate" + los + "\n");
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("    ,[lrv" + los + "]\n");
        }

        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append(" FROM " + tableName + "_1\n");
        } else {
            stringBuilder.append(" " + FROM + tableName + "_1\n");
        }
        return stringBuilder.toString();
    }

    public String createTempWeightedRateFinalTable(String tableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("IF OBJECT_ID('tempdb..#" + tableName + "_1_wrate_final') IS NOT NULL DROP TABLE #" + tableName
                + "_1_wrate_final;\n");
        stringBuilder.append("CREATE TABLE #" + tableName + "_1_wrate_final(\n");
        stringBuilder.append("    [Arrival_DT] [date] NOT NULL\n");
        stringBuilder.append("    ,[Rate_Qualified_id] [int] NOT NULL\n");
        if (isCorrectedFplosForDerivedRatesAndBarByLOS) {
            stringBuilder.append(",[LOS] [int] NOT NULL\n");
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append(",[wRate" + los + "] [numeric](19,5) NOT NULL\n");
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append(",[lrv" + los + "] [numeric](19,5) NOT NULL\n");
        }
        stringBuilder.append("\n");
        stringBuilder.append(" CONSTRAINT [PK_" + tableName + "_1_wrate_final] PRIMARY KEY CLUSTERED \n");
        stringBuilder.append("(\n");
        stringBuilder.append("    Arrival_DT,Rate_Qualified_id");
        if (isCorrectedFplosForDerivedRatesAndBarByLOS) {
            stringBuilder.append(", LOS");
        }
        stringBuilder.append(" ASC\n");
        stringBuilder
                .append(")\n");
        stringBuilder.append(");\n");
        return stringBuilder.toString();
    }

    public String populateTempWeightedRateFinalTable(String tableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("insert into #" + tableName + "_1_wrate_final\n");
        stringBuilder.append("select Arrival_DT,Rate_Qualified_id\n");
        if (isCorrectedFplosForDerivedRatesAndBarByLOS) {
            stringBuilder.append(",LOS\n");
        }

        for (int los = 1; los <= maxLOS; los++) {
            if (SystemConfig.isFPLOSWrateFinalRewriteEnabled()) {
                stringBuilder.append("    ,wRate" + los + "= case(sum(remCap" + los + ")) when 0 then 0 else sum(Rate" + los + "*remCap" + los + ")/sum(remCap" + los + ") end   \n");
            } else {
                stringBuilder.append("    ,wRate" + los + " = case(sum(remCap" + los + ")) when 0 then 0 else sum(wRate" + los + ")/sum(remCap" + los + ") end    \n");
            }
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("    ,avg(lrv" + los + ")\n");
        }

        if (SystemConfig.isFPLOSWrateFinalRewriteEnabled()) {
            if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
                stringBuilder.append(" FROM " + tableName + "_1\n");
            } else {
                stringBuilder.append(" " + FROM + tableName + "_1\n");
            }
        } else {
            stringBuilder.append(" " + FROM + tableName + "_1_wrate\n");
        }

        stringBuilder.append(" group by Arrival_DT,Rate_Qualified_id\n");
        if (isCorrectedFplosForDerivedRatesAndBarByLOS) {
            stringBuilder.append(",LOS;\n");
        }
        return stringBuilder.toString();
    }

    public String createNonMasterRateCodeTable(String tableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("IF OBJECT_ID('tempdb..#" + tableName + "_non_master') IS NOT NULL DROP TABLE #" + tableName
                + "_non_master;\n");
        stringBuilder.append("CREATE TABLE #" + tableName + "_non_master(\n");
        stringBuilder.append("    [Arrival_DT] [date] NOT NULL\n");
        stringBuilder.append("    ,[Rate_Qualified_id] [int] NOT NULL\n");
        if (isCorrectedFplosForDerivedRatesAndBarByLOS) {
            stringBuilder.append("    ,[LOS] [int] NOT NULL\n");
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("    ,[Rate" + los + "] [numeric](19,5) NOT NULL\n");
        }
        stringBuilder.append("\n");
        stringBuilder.append(" CONSTRAINT [PK_" + tableName + "_non_master] PRIMARY KEY CLUSTERED \n");
        stringBuilder.append("(\n");
        stringBuilder.append("    Arrival_DT,Rate_Qualified_id");
        if (isCorrectedFplosForDerivedRatesAndBarByLOS) {
            stringBuilder.append(", LOS");
        }
        stringBuilder.append(" ASC\n")
                .append(")\n");
        stringBuilder.append(");\n");
        return stringBuilder.toString();
    }

    public String populateNonMasterRateCodeTable(String tableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("insert into #" + tableName + "_non_master \n");
        stringBuilder.append("select Arrival_DT,Rate_Qualified_id\n");
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append(",Rate as Rate" + los + "\n");
        }
        stringBuilder.append("from \n");
        stringBuilder.append("( \n");
        stringBuilder.append("    select arrival_dt,Rate_Qualified_ID,max(rate) Rate \n");
        stringBuilder.append("    from \n");
        stringBuilder.append("    ( \n");
        stringBuilder.append("        select arrival_dt,Rate_Qualified_ID,rate_q.Accom_Type_ID, \n");
        stringBuilder.append("         Rate=case  \n");
        stringBuilder.append("             when DATEPART(weekday,Arrival_DT) = 1 then Sunday \n");
        stringBuilder.append("             when DATEPART(weekday,Arrival_DT) = 2 then Monday \n");
        stringBuilder.append("             when DATEPART(weekday,Arrival_DT) = 3 then Tuesday \n");
        stringBuilder.append("             when DATEPART(weekday,Arrival_DT) = 4 then Wednesday \n");
        stringBuilder.append("             when DATEPART(weekday,Arrival_DT) = 5 then Thursday \n");
        stringBuilder.append("             when DATEPART(weekday,Arrival_DT) = 6 then Friday \n");
        stringBuilder.append("             when DATEPART(weekday,Arrival_DT) = 7 then Saturday \n");
        stringBuilder.append("        end \n");
        stringBuilder.append("        from \n");
        stringBuilder.append("        ( \n");
        stringBuilder.append("            select cast (calendar_date as date) arrival_dt from calendar_dim where calendar_date between :" + START_DATE + " and :" + END_DATE + " \n");
        stringBuilder.append("        ) dt left join \n");
        stringBuilder.append("        ( \n");
        stringBuilder
                .append("            select rqd.* from #").append(propertyId).append("_RateQualifiedTable rq inner join " + getRateQualifiedDetails() + " rqd on rq.Rate_Qualified_ID=rqd.Rate_Qualified_ID \n");
        stringBuilder.append("        where rq.Property_ID=:" + PROPERTY_ID + " and rq.Rate_Qualified_ID!=:" + SPECIAL_SRP_ID + "  and rq.Status_ID=1 and rq.Yieldable=1 \n");
        stringBuilder.append("        ) rate_q on dt.arrival_dt between rate_q.Start_Date_DT and rate_q.End_Date_DT \n");
        stringBuilder.append("        inner join  \n");
        stringBuilder.append("        ( \n");
        stringBuilder
                .append("            select AT.Accom_Type_ID from Accom_Type AT inner join Accom_Class AC on AT.Accom_Class_ID=AC.Accom_Class_ID  \n");
        stringBuilder.append("            where AT.System_Default=0 and AC.System_Default=0 and AT.Status_ID=1 and AC.Status_ID=1 \n");
        stringBuilder.append("            and AC.Master_Class!=1 and AT.Property_ID=:" + PROPERTY_ID + " \n");
        stringBuilder.append("            and At.Accom_Type_Capacity>0 \n");
        stringBuilder.append("        ) AT on rate_q.Accom_Type_ID=AT.Accom_Type_ID \n");
        stringBuilder.append("    )data group by arrival_dt,Rate_Qualified_ID \n");
        stringBuilder.append(")data2 \n");
        return stringBuilder.toString();
    }

    protected String createNonMasterRateCodeTable_2(String tableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("IF OBJECT_ID('tempdb..#" + tableName + "_non_master_2') IS NOT NULL DROP TABLE #" + tableName
                + "_non_master_2;\n");
        stringBuilder.append("CREATE TABLE #" + tableName + "_non_master_2(\n");
        stringBuilder.append("    [Arrival_DT] [date] NOT NULL\n");
        stringBuilder.append("    ,[Rate_Qualified_id] [int] NOT NULL\n");
        if (isCorrectedFplosForDerivedRatesAndBarByLOS) {
            stringBuilder.append("    ,[LOS] [int] NOT NULL\n");
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("    ,[Rate" + los + "] [numeric](19,5) NOT NULL\n");
        }
        stringBuilder.append("\n");
        stringBuilder.append(" CONSTRAINT [PK_" + tableName + "_non_master_2] PRIMARY KEY CLUSTERED \n");
        stringBuilder.append("(\n");
        stringBuilder.append("    Arrival_DT,Rate_Qualified_id");
        if (isCorrectedFplosForDerivedRatesAndBarByLOS) {
            stringBuilder.append(",LOS");
        }
        stringBuilder.append(" ASC\n")
                .append(")\n");
        stringBuilder.append(");\n");
        return stringBuilder.toString();
    }

    public String populateNonMasterRateCodeTable_2(String tableName) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("insert into #" + tableName + "_non_master_2 \n");
        stringBuilder.append("select t1.* " + FROM + tableName + "_non_master t1  \n");
        stringBuilder.append("    left join #" + tableName + "_1_wrate_final t2 \n");
        stringBuilder.append("    on t1.Arrival_DT=t2.Arrival_DT and t1.Rate_Qualified_id=t2.Rate_Qualified_id \n");
        stringBuilder.append("    where t2.wRate1 is null \n");
        return stringBuilder.toString();
    }

    public String populateTempWeightedRateFinalTable_withNonMaster(String tableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("insert into #" + tableName + "_1_wrate_final \n");

        if (SystemConfig.isFPLOSWrateFinalRewriteEnabled()) {
            stringBuilder.append("select t1.Arrival_DT, t1.Rate_Qualified_id \n");

            if (isCorrectedFplosForDerivedRatesAndBarByLOS) {
                stringBuilder.append(", t1.LOS \n");
            }
            for (int los = 1; los <= maxLOS; los++) {
                stringBuilder.append("    ,Rate" + los + " \n");
            }
            for (int los = 1; los <= maxLOS; los++) {
                stringBuilder.append("    ,avg(lrv" + los + ") lrv" + los + " \n");
            }

            stringBuilder.append("from #" + tableName + "_non_master_2 t1 \n");
            stringBuilder.append("join #" + tableName + "_1_wrate_final t2 \n");
            stringBuilder.append("on t1.Arrival_DT = t2.Arrival_DT \n");
            stringBuilder.append("group by t1.Arrival_DT, t1.Rate_Qualified_id \n");

            if (isCorrectedFplosForDerivedRatesAndBarByLOS) {
                stringBuilder.append(", t1.LOS \n");
            }

            for (int los = 1; los <= maxLOS; los++) {
                stringBuilder.append("    ,t1.Rate" + los + " \n");
            }
        } else {
            stringBuilder.append("select data1.* \n");
            for (int los = 1; los <= maxLOS; los++) {
                stringBuilder.append("    ,lrv" + los + " \n");
            }
            stringBuilder.append(FROM + tableName + "_non_master_2 data1 \n");
            stringBuilder.append("left join \n");
            stringBuilder.append("( \n");
            stringBuilder.append("    select t1.Arrival_DT \n");
            for (int los = 1; los <= maxLOS; los++) {
                stringBuilder.append("        ,avg(lrv" + los + ") lrv" + los + " \n");
            }
            stringBuilder.append("     " + FROM + tableName + "_non_master_2 t1  \n");
            stringBuilder.append("        left join #" + tableName + "_1_wrate_final t2 \n");
            stringBuilder.append("        on t1.Arrival_DT=t2.Arrival_DT \n");
            stringBuilder.append("    group by t1.Arrival_DT \n");
            stringBuilder.append(") data2 on data1.Arrival_DT=data2.Arrival_DT \n");
            // TTRS-3887-Start
            stringBuilder.append("where lrv1 is not null \n");
            for (int los = 2; los <= maxLOS; los++) {
                stringBuilder.append(" and lrv" + los + " is not null \n");
            }
            // TTRS-3887-End
        }
        return stringBuilder.toString();
    }

    @VisibleForTesting
    String createCumulativeWeightedRateTable(String tableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("IF OBJECT_ID('tempdb..#" + tableName + "_wrate_cumulative') IS NOT NULL DROP TABLE #" + tableName
                + "_wrate_cumulative;\n");
        stringBuilder.append("CREATE TABLE #" + tableName + "_wrate_cumulative(\n");
        stringBuilder.append("    [Arrival_DT] [date] NOT NULL\n");
        stringBuilder.append("    ,[Rate_Qualified_id] [int] NOT NULL\n");

        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("    ,[cumulative_Rate" + los + "] [numeric](19,5) NOT NULL\n");
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("    ,[lrv" + los + "] [numeric](19,5) NOT NULL\n");
        }
        stringBuilder.append(" CONSTRAINT [PK_" + tableName + "_wrate_cumulative] PRIMARY KEY CLUSTERED \n")
                .append("(\n")
                .append("    Arrival_DT,Rate_Qualified_id")
                .append(" ASC\n")
                .append(")\n")
                .append(");\n");
        return stringBuilder.toString();
    }

    @VisibleForTesting
    String populateCumulativeWeightedRateTable(String tableName, int maxLOS) {

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("insert into #" + tableName + "_wrate_cumulative\n")
                .append("SELECT Arrival_DT, Rate_Qualified_id\n");
        int barLos = 1;
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append(", [" + barLos + "] AS cumulative_Rate").append(los);
            if (los < barMaxLos) {
                barLos++;
            }
        }
        stringBuilder.append("\n");
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append(", lrv").append(los);
        }

        stringBuilder.append("\n FROM (")
                .append(" select Arrival_DT,Rate_Qualified_id,LOS \n")
                .append(" ,case");
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append(" when LOS = " + los + " then");
            for (int rateSum = 1; rateSum <= los; rateSum++) {
                stringBuilder.append(" wrate").append(rateSum);
                if (rateSum < los) {
                    stringBuilder.append(" +");
                }
            }
            stringBuilder.append("\n");
        }
        stringBuilder.append("end as cumulative_Rate ");
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append(", lrv").append(los);
        }
        stringBuilder.append("\n" + FROM + tableName + "_1_wrate_final ) as A \n")
                .append(" PIVOT ( MAX(cumulative_Rate) FOR los in ([1]");
        for (int los = 2; los <= barMaxLos; los++) {
            stringBuilder.append(" ,[" + los + "]");
        }
        stringBuilder.append(")) as piv\n");
        stringBuilder.append("where [1] is not null");
        for (int los = 2; los <= barMaxLos; los++) {
            stringBuilder.append(" and [" + los + "] is not null");
        }

        return stringBuilder.append(";\n").toString();
    }


    // fplosbysrp

    public String createTempFPLOSTable(String currTableName, boolean isFplosBySRP, boolean isLv0) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("IF OBJECT_ID('tempdb..#" + currTableName + "_2') IS NOT NULL DROP TABLE #" + currTableName + "_2;\n");
        stringBuilder.append("CREATE TABLE #" + currTableName + "_2(\n");
        stringBuilder.append("    [Property_ID] [int] NOT NULL,\n");
        stringBuilder.append("    [Arrival_DT] [date] NOT NULL,\n");
        if (!isFplosBySRP || isLv0) {
            stringBuilder.append("    [Accom_Type_ID] [int] NOT NULL,\n");
        }
        stringBuilder.append("    [Rate_Qualified_id] [int] NOT NULL,\n");
        stringBuilder.append("    [FPLOS] [varchar](21) NOT NULL,\n");
        stringBuilder.append("\n");
        stringBuilder.append(" CONSTRAINT [PK_" + currTableName + "_2] PRIMARY KEY CLUSTERED \n");
        stringBuilder.append("(\n");
        stringBuilder.append("    Property_ID");
        if (!isFplosBySRP || isLv0) {
            stringBuilder.append(",Accom_Type_ID");
        }
        stringBuilder.append(",Arrival_DT,Rate_Qualified_id ASC )\n");
        stringBuilder.append(");\n");
        return stringBuilder.toString();
    }

    public String populateTempFPLOSTableNonLV0(String currTableName, int maxLOS) {
        String rate = "wRate";
        if (isCorrectedFplosForDerivedRatesAndBarByLOS) {
            rate = "cumulative_Rate";
        }
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("insert into #").append(currTableName).append("_2\n");
        stringBuilder.append("select :" + PROPERTY_ID + ",Arrival_DT\n");
        stringBuilder.append(",wrateFinal.Rate_Qualified_id, \n");
        stringBuilder.append("case when ").append(rate).append("1")
                .append(isServicingCostEnabledAndConfigured ? " - ISNULL(servicingCost.servicingCost_los1, 0) " : EMPTY)
                .append(">=lrv1 then 'Y' else 'N' end +\n");
        for (int i = 2; i <= maxLOS; i++) {
            if (maxLOS == i) {
                stringBuilder.append("case when :" + EXTENDED_STAY + "=1 and wrateFinal.Rate_Qualified_id=isnull(:" + SPECIAL_SRP_ID + ",-1) then 'Y' else\n");
            }
            if (isCorrectedFplosForDerivedRatesAndBarByLOS) {
                stringBuilder.append("case when ").append(rate).append(i);
            } else {
                stringBuilder.append("case when ").append(rate).append("1");
                for (int j = 2; j <= i; j++) {
                    stringBuilder.append("+").append(rate).append(j);
                }
            }
            if (isServicingCostEnabledAndConfigured) {
                stringBuilder.append(" - ISNULL(servicingCost.servicingCost_los").append(i).append(", 0) ");
            }
            stringBuilder.append(">=");
            stringBuilder.append("lrv").append(i);
            stringBuilder.append(" then 'Y' else 'N' end ");
            if (maxLOS == i) {
                stringBuilder.append("end \n");
            } else {
                stringBuilder.append("+ \n");
            }
        }
        stringBuilder.append(" as FPLOS\n");
        if (isCorrectedFplosForDerivedRatesAndBarByLOS) {
            stringBuilder.append(FROM).append(currTableName).append("_wrate_cumulative as wrateFinal \n");
        } else {
            stringBuilder.append(FROM).append(currTableName).append("_1_wrate_final as wrateFinal \n");
        }
        if (isServicingCostEnabledAndConfigured) {
            stringBuilder.append(" left join ").append("#").append(servicingCostQueryBuilder.servicingCostTableTempTableAtTotal).append(" as servicingCost \n");
            stringBuilder.append(" on wrateFinal.Rate_Qualified_id = servicingCost.Rate_Qualified_id ");
        }
        stringBuilder.append(";\n");
        return stringBuilder.toString();
    }

    public String createTempEffectiveRatesWithDecisionReasonType(String currTableName, int maxLOS) {
        currTableName = currTableName + "_LRA";

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("IF OBJECT_ID('tempdb..#").append(currTableName).append("') IS NOT NULL DROP TABLE #").append(currTableName)
                .append(";\n");
        stringBuilder.append("CREATE TABLE #").append(currTableName).append("(\n");
        stringBuilder.append("\n");
        stringBuilder.append("    [Arrival_DT] [date] NOT NULL,\n");
        stringBuilder.append("    [Accom_Type_ID] [int] NOT NULL,\n");
        stringBuilder.append("    [Rate_Qualified_id] [int] NOT NULL,\n");
        stringBuilder.append("    [Rate] [numeric](19,5) NOT NULL,\n");
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("    [Rate").append(los).append("] [numeric](19,5) NOT NULL,\n");
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("    [lrv").append(los).append("] [numeric](19,5) NOT NULL,\n");
        }
        stringBuilder.append("    [LOS] [int] NULL,\n");
        stringBuilder.append("    [Decision_Reason_Type_ID] [int]\n");
        stringBuilder.append(")\n");
        stringBuilder.append(";\n");

        return stringBuilder.toString();
    }

    public String populateTempEffectiveRatesWithDecisionReasonType(String currTableName) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("insert into #").append(currTableName).append("_LRA").append("\n");

        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append("select ").append(currTableName).append("_1.*,LRA_IMPACT.LOS,LRA_IMPACT.Decision_Reason_Type_ID ");
            stringBuilder.append("FROM ").append(currTableName).append("_1 left join \n");
        } else {
            stringBuilder.append("select #").append(currTableName).append("_1.*,LRA_IMPACT.LOS,LRA_IMPACT.Decision_Reason_Type_ID ");
            stringBuilder.append(FROM).append(currTableName).append("_1 left join \n");
        }

        stringBuilder.append("(\n");
        stringBuilder
                .append("    select Property_ID,Arrival_DT,sub_optimal_LV0.Accom_Class_ID,Accom_Type_ID,LOS,Rate_Unqualified_ID,:" + SPECIAL_SRP_ID + " Rate_Qualified_ID,Decision_Reason_Type_ID from\n");
        stringBuilder.append("    (\n");
        stringBuilder.append("        select Property_ID,Accom_Class_ID,Arrival_DT,Rate_Unqualified_ID,LOS,Decision_Reason_Type_ID from decision_bar_output \n");
        if (lraEnabledValue == LRA_ENABLED_ALL_BAR_RATES_IMPACT) {
            stringBuilder.append("        where Accom_Class_ID=:" + MASTER_CLASS_ID + " and Decision_Reason_Type_ID=6\n");
        } else {
            stringBuilder.append("        where Rate_Unqualified_ID=:" + LV0_UNQUALIFIED_ID + " and Accom_Class_ID=:" + MASTER_CLASS_ID + " and Decision_Reason_Type_ID=6\n");
        }
        stringBuilder.append(" and Arrival_DT BETWEEN :" + START_DATE + " AND :" + END_DATE + " \n");
        stringBuilder.append("    ) sub_optimal_LV0\n");
        stringBuilder.append("    inner join\n");
        stringBuilder.append("    (\n");
        stringBuilder.append("        select distinct AT.Accom_Type_ID,AC.Accom_Class_ID \n");
        stringBuilder.append("        from Accom_Class AC inner join Accom_Type AT on AC.Accom_Class_ID=AT.Accom_Class_ID\n");
        stringBuilder.append("        where AT.Status_ID=1 and AT.System_Default=0 and AC.Accom_Class_ID=:" + MASTER_CLASS_ID + " and AT.Property_ID=:" + PROPERTY_ID + "\n");
        stringBuilder.append("    ) MASTER_AC_AT\n");
        stringBuilder.append("    on sub_optimal_LV0.Accom_Class_ID=MASTER_AC_AT.Accom_Class_ID\n");
        stringBuilder.append(") LRA_IMPACT on ");

        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append(currTableName).append("_1.Arrival_DT=LRA_IMPACT.Arrival_DT and LRA_IMPACT.Accom_Type_ID=").append(currTableName).append("_1.Accom_Type_ID\n");
            stringBuilder.append("and ").append(currTableName).append("_1.Rate_Qualified_id=LRA_IMPACT.Rate_Qualified_ID\n");
        } else {
            stringBuilder.append("#" + currTableName + "_1.Arrival_DT=LRA_IMPACT.Arrival_DT and LRA_IMPACT.Accom_Type_ID=#").append(currTableName).append("_1.Accom_Type_ID\n");
            stringBuilder.append("and #").append(currTableName).append("_1.Rate_Qualified_id=LRA_IMPACT.Rate_Qualified_ID\n");
        }

        return stringBuilder.toString();
    }

    public String createTempEffectiveRatesWithDecisionReasonTypeByLos(String currTableName, int maxLOS) {
        currTableName = currTableName + "_LRA_1";

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("IF OBJECT_ID('tempdb..#").append(currTableName).append("') IS NOT NULL DROP TABLE #").append(currTableName)
                .append(";\n");
        stringBuilder.append("CREATE TABLE #").append(currTableName).append("(\n");
        stringBuilder.append("\n");
        stringBuilder.append("    [Arrival_DT] [date] NOT NULL\n");
        stringBuilder.append("    ,[Accom_Type_ID] [int] NOT NULL\n");
        stringBuilder.append("    ,[Rate_Qualified_id] [int] NOT NULL\n");
        stringBuilder.append("    ,[Rate] [numeric](19,5) NOT NULL\n");
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("    ,[Rate").append(los).append("] [numeric](19,5) NOT NULL\n");
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("    ,[lrv").append(los).append("] [numeric](19,5) NOT NULL\n");
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("    ,[ReasonType_los").append(los).append("] [int]\n");
        }
        stringBuilder.append(")\n");
        stringBuilder.append(";\n");

        return stringBuilder.toString();
    }

    public String populateTempEffectiveRatesWithDecisionReasonTypeByLOS(String currTableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("insert into #" + currTableName + "_LRA_1\n");
        stringBuilder.append(" select Arrival_DT,Accom_Type_ID,Rate_Qualified_id,Rate\n");
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append(" ,Rate").append(los).append("\n");
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append(" ,lrv").append(los).append("\n");
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append(" ,max([").append(los).append("])  as reasonType_los").append(los).append("\n");
        }
        stringBuilder.append(" from\n");
        stringBuilder.append(" (\n");
        stringBuilder.append("     select :" + PROPERTY_ID + " propertyID,Arrival_DT,Accom_Type_ID,Rate_Qualified_id,Rate\n");
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("     ,Rate").append(los).append("\n");
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("     ,lrv").append(los).append("\n");
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("     ,(case los when ").append(los).append(" then  Decision_Reason_Type_ID end ) as [").append(los)
                    .append("]\n");
        }
        stringBuilder.append("     " + FROM).append(currTableName).append("_LRA\n");
        stringBuilder.append(") as x group by propertyID,Arrival_DT,Accom_Type_ID,Rate_Qualified_id,Rate\n");
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append(",Rate").append(los).append("\n");
        }
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append(",lrv").append(los).append("\n");
        }

        return stringBuilder.toString();
    }

    public String createEffectiveRHBTable(String currTableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("IF OBJECT_ID('tempdb..#" + currTableName + "_RHB') IS NOT NULL DROP TABLE #" + currTableName + "_RHB;\n");
        stringBuilder.append("CREATE TABLE #" + currTableName + "_RHB(");
        stringBuilder.append("[Arrival_DT] [date] NOT NULL,\n");
        stringBuilder.append("[Accom_Type_ID] [int] NOT NULL,\n");
        for (int los = 1; los <= maxLOS; los++) {
            stringBuilder.append("[RHB_LOS" + los + "] [int] NULL,\n");
        }
        stringBuilder.append("[LV0QualifiedId] [int],\n");
        stringBuilder.append(" CONSTRAINT [PK_" + currTableName + "_RHB] PRIMARY KEY CLUSTERED \n");
        stringBuilder.append("(\n");
        stringBuilder.append("Accom_Type_ID,Arrival_DT ASC )\n");
        stringBuilder.append(");\n");

        return stringBuilder.toString();
    }

    public String populateEffectiveRHBTable(String currTableName, int maxLOS) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("insert into #" + currTableName + "_RHB \n")
                .append("SELECT Arrival_DT,Accom_Type_ID, [1] AS RHB_LOS1\n");
        for (int i = 2; i <= maxLOS; i++) {
            stringBuilder.append(", ").append("[").append(i < barMaxLos ? i : barMaxLos).append("] AS RHB_LOS").append(i);
        }
        stringBuilder.append(", " + lv0QualifiedId + " AS LV0QualifiedId");
        stringBuilder.append(" FROM (SELECT Arrival_DT, Accom_Type_ID, los from Decision_Restrict_Highest_Bar ) AS RHB \n")
                .append("PIVOT \n")
                .append("( MAX(Los) FOR Los IN ([1]");
        for (int i = 2; i <= barMaxLos; i++) {
            stringBuilder.append(", [").append(i).append("]");
        }
        stringBuilder.append(")) AS pivotJoin \n");
        return stringBuilder.toString();
    }

    @SuppressWarnings({"squid:MethodCyclomaticComplexity", "squid:S3776"})
    public String populateTempFPLOSTableForLV0(String currTableName, int maxLOS) {
        String rate = "Rate";
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("insert into #").append(currTableName).append("_2\n");
        stringBuilder.append("select :" + PROPERTY_ID + ",LRA_1.Arrival_DT\n");
        stringBuilder.append(",LRA_1.Accom_Type_ID\n");
        stringBuilder.append(",LRA_1.Rate_Qualified_id, \n");

        stringBuilder.append("case when (").append(this.lraEnabledValue).append(">0 and ReasonType_los1=6)");
        if (this.isRestrictHighestBarEnabled) {
            stringBuilder.append(" or restHighestBar.RHB_LOS1=1");
        }
        stringBuilder.append(" then 'N' else \n");
        stringBuilder.append("   case when Rate1").append(isServicingCostEnabledAndConfigured ? " - ISNULL(servicingCost_los1, 0) " : EMPTY).append(">=lrv1 then 'Y' else 'N' end end\n");
        for (int i = 2; i <= maxLOS; i++) {
            if (maxLOS == i) {
                stringBuilder.append("+");
                if (this.isRestrictHighestBarEnabled) {
                    stringBuilder.append("case when restHighestBar.RHB_LOS").append(i).append("=").append(i).append(" then 'N' else ").append("\n");
                }
                stringBuilder.append("case when :" + EXTENDED_STAY + "=1 and LRA_1.Rate_Qualified_id=isnull(:" + SPECIAL_SRP_ID + ",-1) \n");
                stringBuilder.append("    then 'Y' \n");
                stringBuilder.append("    else \n");
                stringBuilder.append("        case when ").append(this.lraEnabledValue).append(">0 and ReasonType_los").append(i).append("=6 \n");
                stringBuilder.append("            then 'N' \n");
                stringBuilder.append("            else\n");
                stringBuilder.append("                case when ");
                stringBuilder.append(rate).append("1");
                for (int j = 2; j <= i; j++) {
                    stringBuilder.append("+").append(rate).append(j);
                }
                stringBuilder.append(isServicingCostEnabledAndConfigured ? " - ISNULL(servicingCost_los" + i + ", 0) " : EMPTY);
                stringBuilder.append(">=");
                stringBuilder.append("lrv").append(i);

                stringBuilder.append(" \n");
                stringBuilder.append("                    then 'Y' \n");
                stringBuilder.append("                    else 'N' \n");
                stringBuilder.append("                end \n");
                stringBuilder.append("        end \n");
                stringBuilder.append("end \n");
                if (this.isRestrictHighestBarEnabled) {
                    stringBuilder.append("end \n");
                }
            } else {
                stringBuilder.append("+case when (").append(this.lraEnabledValue).append(">0 and ReasonType_los").append(i)
                        .append("=6)");
                if (this.isRestrictHighestBarEnabled) {
                    stringBuilder.append(" or restHighestBar.RHB_LOS").append(i).append("=").append(i);
                }
                stringBuilder.append(" then 'N' else \n");
                stringBuilder.append("    case when ");
                stringBuilder.append(rate).append("1");
                for (int j = 2; j <= i; j++) {
                    stringBuilder.append("+").append(rate).append(j);
                }
                stringBuilder.append(isServicingCostEnabledAndConfigured ? " - ISNULL(servicingCost_los" + i + ", 0) " : EMPTY);
                stringBuilder.append(">=");
                stringBuilder.append("lrv").append(i);

                stringBuilder.append(" then 'Y' else 'N' end end\n");
            }

        }
        stringBuilder.append(" as FPLOS\n");
        stringBuilder.append(FROM).append(currTableName).append("_LRA_1 AS LRA_1");
        if (isServicingCostEnabledAndConfigured) {
            stringBuilder.append(" left join #").append(servicingCostQueryBuilder.servicingCostTableTempTable).append(" as servicingCost \n");
            stringBuilder.append("on (LRA_1.Rate_Qualified_id = servicingCost.Rate_Qualified_id and LRA_1.Accom_Type_ID = servicingCost.Accom_Type_ID) ");
        }
        //connect tables.
        if (this.isRestrictHighestBarEnabled) {
            stringBuilder.append(" LEFT OUTER JOIN #").append(currTableName).append("_RHB AS restHighestBar ").append("\n");
            stringBuilder.append("ON LRA_1.Arrival_DT = restHighestBar.Arrival_DT ").append("\n");
            stringBuilder.append("AND LRA_1.Accom_Type_ID = restHighestBar.Accom_Type_ID ");
            stringBuilder.append("AND LRA_1.Rate_Qualified_id = restHighestBar.LV0QualifiedId ");
        }
        stringBuilder.append(" ;\n");
        return stringBuilder.toString();
    }

    @SuppressWarnings({"squid:MethodCyclomaticComplexity", "squid:S3776"})
    String populateTempFPLOSTable(String currTableName, int maxLOS) {
        String rate = "Rate";
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("insert into #").append(currTableName).append("_2\n");
        stringBuilder.append("select :" + PROPERTY_ID + ",LRA_1.Arrival_DT\n");
        stringBuilder.append(",LRA_1.Accom_Type_ID\n");
        stringBuilder.append(",LRA_1.Rate_Qualified_id, \n");

        stringBuilder.append("case when (").append(this.lraEnabledValue).append(">0 and ReasonType_los1=6)");
        if (this.isRestrictHighestBarEnabled) {
            stringBuilder.append(" or restHighestBar.RHB_LOS1=1");
        }
        stringBuilder.append(" then 'N' else \n");
        if (this.isServicingCostEnabledAndConfigured) {
            stringBuilder.append("    case when Rate1 - ISNULL(servicingCost_los1, 0) >=lrv1 then 'Y' else 'N' end end\n");
        } else {
            stringBuilder.append("    case when Rate1>=lrv1 then 'Y' else 'N' end end\n");
        }
        for (int i = 2; i <= maxLOS; i++) {
            if (maxLOS == i) {
                stringBuilder.append("+");
                if (this.isRestrictHighestBarEnabled) {
                    stringBuilder.append("case when restHighestBar.RHB_LOS").append(i).append("=").append(i).append(" then 'N' else ").append("\n");
                }
                stringBuilder.append("case when :" + EXTENDED_STAY + "=1 and LRA_1.Rate_Qualified_id=isnull(:" + SPECIAL_SRP_ID + ",-1) \n");
                stringBuilder.append("    then 'Y' \n");
                stringBuilder.append("    else \n");
                stringBuilder.append("        case when ").append(this.lraEnabledValue).append(">0 and ReasonType_los").append(i).append("=6 \n");
                stringBuilder.append("            then 'N' \n");
                stringBuilder.append("            else\n");
                stringBuilder.append("                case when ");
                updateQueryBasedOnDerivedRatePlanAndBarByLos(rate, stringBuilder, i);
                stringBuilder.append(">=");
                stringBuilder.append("lrv").append(i);

                stringBuilder.append(" \n");
                stringBuilder.append("                    then 'Y' \n");
                stringBuilder.append("                    else 'N' \n");
                stringBuilder.append("                end \n");
                stringBuilder.append("        end \n");
                stringBuilder.append("end \n");
                if (this.isRestrictHighestBarEnabled) {
                    stringBuilder.append("end \n");
                }
            } else {
                stringBuilder.append("+case when (").append(this.lraEnabledValue).append(">0 and ReasonType_los").append(i)
                        .append("=6)");
                if (this.isRestrictHighestBarEnabled) {
                    stringBuilder.append(" or restHighestBar.RHB_LOS").append(i).append("=").append(i);
                }
                stringBuilder.append(" then 'N' else \n");
                stringBuilder.append("    case when ");
                updateQueryBasedOnDerivedRatePlanAndBarByLos(rate, stringBuilder, i);
                stringBuilder.append(">=");
                stringBuilder.append("lrv").append(i);
                stringBuilder.append(" then 'Y' else 'N' end end\n");
            }
        }
        stringBuilder.append(" as FPLOS\n");
        stringBuilder.append(FROM).append(currTableName).append("_LRA_1 AS LRA_1");
        if (this.isServicingCostEnabledAndConfigured) {
            stringBuilder.append(" left join #" + servicingCostQueryBuilder.servicingCostTableTempTable + " as servicingCost ")
                    .append(" on (LRA_1.Rate_Qualified_id = servicingCost.Rate_Qualified_id and LRA_1.Accom_Type_ID = servicingCost.Accom_Type_ID)\n");
        }
        //connect tables.
        if (this.isRestrictHighestBarEnabled) {
            stringBuilder.append(" LEFT OUTER JOIN #").append(currTableName).append("_RHB AS restHighestBar ").append("\n");
            stringBuilder.append("ON LRA_1.Arrival_DT = restHighestBar.Arrival_DT ").append("\n");
            stringBuilder.append("AND LRA_1.Accom_Type_ID = restHighestBar.Accom_Type_ID ");
            stringBuilder.append("AND LRA_1.Rate_Qualified_id = restHighestBar.LV0QualifiedId ");
        }
        stringBuilder.append(" ;\n");
        return stringBuilder.toString();
    }

    private void updateQueryBasedOnDerivedRatePlanAndBarByLos(String rate, StringBuilder stringBuilder, int los) {
        if (this.isDerivedRatePlanEnabled && this.isBarByLos) {
            stringBuilder.append(rate).append(los);
            if (this.isServicingCostEnabledAndConfigured) {
                stringBuilder.append(" - ISNULL(servicingCost_los").append(los).append(", 0)");
            }
        } else {
            stringBuilder.append(rate).append("1");
            for (int j = 2; j <= los; j++) {
                stringBuilder.append("+").append(rate).append(j);
            }
            if (this.isServicingCostEnabledAndConfigured) {

                stringBuilder.append(" - ISNULL(servicingCost_los").append(los).append(", 0)");
            }

        }
    }

    public String populateQualifiedFPLOSTable(String currTableName, boolean isFplosBySRP, boolean isLv0) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder
                .append("insert into Decision_Qualified_FPLOS (Decision_ID,Property_ID,Accom_Type_ID,Rate_qualified_ID,Arrival_DT,FPLOS)\n");
        stringBuilder.append("select :" + DECISION_ID + ",:" + PROPERTY_ID + "\n");
        if (isFplosBySRP && !isLv0) {
            stringBuilder.append(",:" + DEFAULT_ACCOM_TYPE_ID + "\n");
        } else {
            stringBuilder.append(",a.Accom_Type_ID\n");
        }
        stringBuilder.append(",a.Rate_qualified_ID,a.Arrival_DT,a.FPLOS \n");
        stringBuilder.append(FROM).append(currTableName).append("_2 a left join Decision_Qualified_FPLOS b\n");
        stringBuilder.append("on\n");
        if (!isFplosBySRP || isLv0) {
            stringBuilder.append("a.Accom_Type_ID=b.Accom_Type_ID\n");
            stringBuilder.append("and\n");
        }
        stringBuilder.append("a.Arrival_DT=b.Arrival_DT\n");
        stringBuilder.append("and\n");
        stringBuilder.append("a.Rate_qualified_ID=b.Rate_qualified_ID\n");
        stringBuilder.append("where \n");
        stringBuilder.append("b.FPLOS is null;\n");
        return stringBuilder.toString();
    }

    public String updateQualifiedFPLOSTable(String currTableName, boolean isFplosBySRP, boolean isLv0) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("update Decision_Qualified_FPLOS \n");
        stringBuilder.append("set Decision_ID=:" + DECISION_ID + "\n");
        stringBuilder.append(",fplos = a.FPLOS\n");
        stringBuilder.append(FROM).append(currTableName).append("_2 a inner join Decision_Qualified_FPLOS b\n");
        stringBuilder.append("on \n");
        if (!isFplosBySRP || isLv0) {
            stringBuilder.append("a.Accom_Type_ID=b.Accom_Type_ID and \n");
        }
        stringBuilder.append("a.Arrival_DT=b.Arrival_DT and\n");
        stringBuilder.append("a.Rate_qualified_ID=b.Rate_qualified_ID and\n");
        stringBuilder.append("a.Property_ID=b.Property_ID\n");
        stringBuilder.append("where a.FPLOS <> b.FPLOS\n");
        stringBuilder.append(";\n");
        return stringBuilder.toString();
    }

    public String deleteInvalidFPLOS(String currTableName, int maxLOS, boolean isFplosBySRP, boolean isLv0) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("delete Decision_Qualified_FPLOS \n");
        stringBuilder.append(getCurrTable(currTableName, isFplosBySRP, isLv0));
        stringBuilder.append(" a right join Decision_Qualified_FPLOS b\n");
        stringBuilder.append("on \n");
        stringBuilder.append("a.Property_ID=b.Property_ID\n");
        if (isLimitTotalSRPRatesApplicable() || !isFplosBySRP || isLv0) {
            stringBuilder.append("and a.Accom_Type_ID=b.Accom_Type_ID\n");
        }
        stringBuilder.append("and\n");
        stringBuilder.append("a.Arrival_DT=b.Arrival_DT\n");
        stringBuilder.append("and\n");
        stringBuilder.append("a.Rate_Qualified_ID=b.Rate_Qualified_ID\n");
        stringBuilder.append(" inner join #").append(propertyId).append("_RateQualifiedTable tmp ON tmp.Rate_Qualified_Id = b.Rate_Qualified_Id \n");
        if (isLimitTotalSRPRatesApplicable()) {
            if (isFplosBySRP) {
                stringBuilder.append(" inner join Limit_Total_Rate_Qualified lt ON lt.Limit_Total_Rate_Qualified_ID = b.Rate_Qualified_ID \n");
                stringBuilder.append(" where ");
            } else {
                stringBuilder.append(" left join Limit_Total_Rate_Qualified lt ON lt.Limit_Total_Rate_Qualified_ID = b.Rate_Qualified_ID \n");
                stringBuilder.append(" where lt.Limit_Total_Rate_Qualified_ID is null and \n");
            }
        } else {
            stringBuilder.append("\nwhere ");
        }
        stringBuilder.append("b.Property_ID=:" + PROPERTY_ID + "\n");
        stringBuilder.append("and\n");
        stringBuilder.append("b.Arrival_DT between :" + START_DATE + " and DATEADD(DAY," + (-maxLOS + 1) + ",:" + END_DATE + ")\n");
        if (isFplosBySRP) {
            if (isLv0) {
                stringBuilder.append("and b.Rate_Qualified_ID=:" + SPECIAL_SRP_ID + "  \n");
            } else {
                stringBuilder.append("and b.Rate_Qualified_ID!=:" + SPECIAL_SRP_ID + "  \n");
            }
        }
        stringBuilder.append("and a.FPLOS is null;\n");
        return stringBuilder.toString();
    }

    private String getCurrTable(String currTableName, boolean isFplosBySrp, boolean isLV0) {
        StringBuilder stringBuilder = new StringBuilder();
        return (isLimitTotalSRPRatesApplicable() && isFplosBySrp && !isLV0) ?
                stringBuilder.append("from (select :propertyId as Property_ID, " +
                                "t.Rate_qualified_ID, t.Arrival_DT, t.FPLOS , :defaultAccomTypeId as Accom_Type_ID ")
                        .append(FROM).append(currTableName).append("_2 t)").toString()
                : stringBuilder.append(FROM).append(currTableName).append("_2").toString();
    }

    public String populatePaceQualifiedFPLOSTable(boolean isFplosBySRP, boolean isLv0) {

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder
                .append("insert into PACE_Qualified_FPLOS(Decision_ID,Property_ID,Accom_Type_ID,Rate_Qualified_ID,Arrival_DT,FPLOS)\n");
        stringBuilder
                .append("select dqf.Decision_ID,dqf.Property_ID,dqf.Accom_Type_ID,dqf.Rate_Qualified_ID,dqf.Arrival_DT,dqf.FPLOS from Decision_Qualified_FPLOS dqf \n");
        stringBuilder.append(" inner join #").append(propertyId).append("_RateQualifiedTable tmp ON tmp.Rate_Qualified_Id = dqf.Rate_Qualified_Id \n");
        stringBuilder.append("where dqf.property_id=:" + PROPERTY_ID + "\n");
        if (isFplosBySRP) {
            if (isLv0) {
                stringBuilder.append("and dqf.Rate_Qualified_ID=:" + SPECIAL_SRP_ID + "  \n");
            } else {
                stringBuilder.append("and dqf.Rate_Qualified_ID!=:" + SPECIAL_SRP_ID + "  \n");
            }
        }
        stringBuilder.append(" AND NOT EXISTS (SELECT 1 \n");
        stringBuilder.append("   FROM   pace_qualified_fplos \n");
        stringBuilder.append("   WHERE  dqf.decision_id = decision_id \n");
        stringBuilder.append("   AND dqf.property_id = property_id \n");
        stringBuilder.append("   AND dqf.accom_type_id = accom_type_id \n");
        stringBuilder.append("   AND dqf.rate_qualified_id = rate_qualified_id \n");
        stringBuilder.append("   AND dqf.arrival_dt = arrival_dt \n");
        stringBuilder.append("   AND dqf.fplos = fplos) \n");

        stringBuilder.append("and\n");
        stringBuilder.append("dqf.decision_id=:" + DECISION_ID + ";\n");
        return stringBuilder.toString();
    }

    public String cleanUpTempTables(String currTableName, boolean isFplosBySRP, boolean isLv0) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("IF OBJECT_ID('tempdb..#").append(currTableName).append("') IS NOT NULL DROP TABLE #").append(currTableName)
                .append(";\n");
        stringBuilder.append("IF OBJECT_ID('tempdb..#").append(currTableName).append("_1a') IS NOT NULL DROP TABLE #")
                .append(currTableName).append("_1a;\n");

        if (SystemConfig.isFPLOSWithNonTempTableEnabled()) {
            stringBuilder.append("IF OBJECT_ID('").append(currTableName).append("_1') IS NOT NULL DROP TABLE ")
                    .append(currTableName).append("_1;\n");
        } else {
            stringBuilder.append("IF OBJECT_ID('tempdb..#").append(currTableName).append("_1') IS NOT NULL DROP TABLE #")
                    .append(currTableName).append("_1;\n");
        }

        stringBuilder.append("IF OBJECT_ID('tempdb..#").append(currTableName).append("_2') IS NOT NULL DROP TABLE #")
                .append(currTableName).append("_2;\n");
        stringBuilder.append("IF OBJECT_ID('tempdb..#").append(currTableName).append("_LRA') IS NOT NULL DROP TABLE #")
                .append(currTableName).append("_LRA;\n");
        stringBuilder.append("IF OBJECT_ID('tempdb..#").append(currTableName).append("_LRA_1') IS NOT NULL DROP TABLE #")
                .append(currTableName).append("_LRA_1;\n");
        stringBuilder.append("IF OBJECT_ID('tempdb..#").append(currTableName).append("_bar_value') IS NOT NULL DROP TABLE #")
                .append(currTableName).append("_bar_value;\n");

        stringBuilder.append("IF OBJECT_ID('tempdb..#").append(currTableName).append("_RateAdjustment') IS NOT NULL DROP TABLE #")
                .append(currTableName).append("_RateAdjustment;\n");
        stringBuilder.append("IF OBJECT_ID('tempdb..#").append(currTableName).append("_RateAdjustmentType') IS NOT NULL DROP TABLE #")
                .append(currTableName).append("_RateAdjustmentType;\n");
        if (isFplosBySRP && !isLv0) {
            stringBuilder.append("IF OBJECT_ID('tempdb..#").append(currTableName).append("_1_wrate') IS NOT NULL DROP TABLE #")
                    .append(currTableName).append("_1_wrate;\n");
            stringBuilder.append("IF OBJECT_ID('tempdb..#").append(currTableName).append("_1_wrate_final') IS NOT NULL DROP TABLE #")
                    .append(currTableName).append("_1_wrate_final;\n");
            stringBuilder.append("IF OBJECT_ID('tempdb..#").append(currTableName).append("_wrate_cumulative') IS NOT NULL DROP TABLE #")
                    .append(currTableName).append("_wrate_cumulative;\n");
            stringBuilder.append("IF OBJECT_ID('tempdb..#").append(currTableName).append("_non_master') IS NOT NULL DROP TABLE #")
                    .append(currTableName).append("_non_master;\n");
            stringBuilder.append("IF OBJECT_ID('tempdb..#").append(currTableName).append("_non_master_1') IS NOT NULL DROP TABLE #")
                    .append(currTableName).append("_non_master_1;\n");
            stringBuilder.append("IF OBJECT_ID('tempdb..#").append(currTableName).append("_non_master_2') IS NOT NULL DROP TABLE #")
                    .append(currTableName).append("_non_master_2;\n");
            stringBuilder.append("IF OBJECT_ID('tempdb..#").append(currTableName).append("a_non_master') IS NOT NULL DROP TABLE #")
                    .append(currTableName).append("a_non_master;\n");
        }
        if (isRestrictHighestBarEnabled) {
            stringBuilder.append("IF OBJECT_ID('tempdb..#").append(currTableName).append("_RHB') IS NOT NULL DROP TABLE #")
                    .append(currTableName).append("_RHB;\n");
        }
        return stringBuilder.toString();
    }

    private String deleteRateQualifiedIdTempTable() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" IF OBJECT_ID('tempdb..#").append(propertyId).append("_RateQualifiedTable') IS NOT NULL DROP TABLE #").append(propertyId).append("_RateQualifiedTable;\n");
        return stringBuilder.toString();
    }

    private String deleteChannelRestrictionsTempTable() {
        StringBuilder stringBuilder = new StringBuilder();
        if (isChannelRestrictionsAdjustmentsEnabled) {
            stringBuilder.append("IF OBJECT_ID('tempdb..#").append(propertyId).append("_ChannelRestrictionsAdj') IS NOT NULL DROP TABLE #")
                    .append(propertyId).append("_ChannelRestrictionsAdj;\n");
        }
        return stringBuilder.toString();
    }

    private boolean isLimitTotalSRPRatesApplicable() {
        return !srpFplosAtTotalLevel && isLimitTotalSRPRatesEnabled;
    }

    protected String generateAgileProductQualifiedFPLOS(String tableName, int maxLOS) {
        return agileProductQualifiedFPLOSQueryBuilder.generateAgileProductQualifiedFPLOS(tableName, maxLOS);
    }

    protected String buildQueriesToMergeAgileProductFPLOSIntoRegularQualifiedFPLOSForRoomTypeLevel(String tableName) {
        return agileProductQualifiedFPLOSQueryBuilder.buildQueriesToMergeAgileProductFPLOSIntoRegularQualifiedFPLOSTempTableForRoomTypeLevel(tableName);
    }

}
