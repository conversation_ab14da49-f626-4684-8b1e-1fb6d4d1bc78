package com.ideas.tetris.pacman.services.reports.pricing.dto;

import com.ideas.tetris.pacman.services.scheduledreport.reportapi.annotations.ColumnHeader;
import com.ideas.tetris.pacman.services.scheduledreport.reportapi.datatypes.PropertyValueType;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Date;

public class PricingByDay {

    @ColumnHeader(titleKey = "report.dow", order = 1, type = PropertyValueType.class)
    private String dow;

    @ColumnHeader(titleKey = "common.arrivalDate", order = 2)
    private Date arrivalDate;

    @ColumnHeader(titleKey = "roomClass", order = 3)
    private String accomClassName;

    @ColumnHeader(titleKey = "property.rooms.sold", order = 4, pattern = "#0")
    private BigDecimal totalRooms;

    @ColumnHeader(titleKey = "report.occupancey.room.class", order = 5, pattern = "#0")
    private BigDecimal roomSold;

    @ColumnHeader(titleKey = "room.class.out.of.order", order = 6, pattern = "#0")
    private BigDecimal outOfOrder;

    @ColumnHeader(titleKey = "last.room.value", order = 7, pattern = "#0.00")
    private BigDecimal lrv;

    @ColumnHeader(titleKey = "occupancy.forecast.property", order = 8, pattern = "#0.00")
    private BigDecimal propertyOccupancyForecast;

    @ColumnHeader(titleKey = "property.occupancy.forecast.percent", order = 9, pattern = "#0.00")
    private BigDecimal propertyOccupancyForecastPercent;

    @ColumnHeader(titleKey = "room.class.occupancy.forecast", order = 10, pattern = "#0.00")
    private BigDecimal occupancyForecast;

    @ColumnHeader(titleKey = "room.class.occupancy.forecast.percent", order = 11, pattern = "#0.00")
    private BigDecimal occupancyForecastPercent;

    @ColumnHeader(titleKey = "notes.label", order = 12)
    private String notes;

    @ColumnHeader(titleKey = "report.barByDay.price", condition = "false", pattern = "#0.00")
    private BigDecimal price;

    @ColumnHeader(titleKey = "report.barByDay.override", condition = "false")
    private String override;
    private BigDecimal occupancyForecastPhysicalCapacityPercent;
    private BigDecimal propertyOccupancyForecastPhysicalCapacityPercent;

    @ColumnHeader(titleKey = "report.barByDay", order = 13, expression = {"(", "$rateCodeName", ")", "$price"})
    // Bar By Day Rate
    private String rateCodeName;
    private String accomTypeName;

    @ColumnHeader(titleKey = {"report.competitorRateFor", "method:getCompetitor1Name($data)"}, order = 13, pattern = "#0.00", condition = "showCompetitor1($data)")
    private BigDecimal compRate1;
    private String compName1;

    @ColumnHeader(titleKey = {"report.competitorRateFor", "method:getCompetitor2Name($data)"}, order = 14, pattern = "#0.00", condition = "showCompetitor2($data)")
    private BigDecimal compRate2;
    private String compName2;

    @ColumnHeader(titleKey = {"report.competitorRateFor", "method:getCompetitor3Name($data)"}, order = 15, pattern = "#0.00", condition = "showCompetitor3($data)")
    private BigDecimal compRate3;
    private String compName3;

    @ColumnHeader(titleKey = {"report.competitorRateFor", "method:getCompetitor4Name($data)"}, order = 16, pattern = "#0.00", condition = "showCompetitor4($data)")
    private BigDecimal compRate4;
    private String compName4;

    @ColumnHeader(titleKey = {"report.competitorRateFor", "method:getCompetitor5Name($data)"}, order = 17, pattern = "#0.00", condition = "showCompetitor5($data)")
    private BigDecimal compRate5;
    private String compName5;

    @ColumnHeader(titleKey = {"report.competitorRateFor", "method:getCompetitor6Name($data)"}, order = 18, pattern = "#0.00", condition = "showCompetitor6($data)")
    private BigDecimal compRate6;
    private String compName6;

    @ColumnHeader(titleKey = {"report.competitorRateFor", "method:getCompetitor7Name($data)"}, order = 19, pattern = "#0.00", condition = "showCompetitor7($data)")
    private BigDecimal compRate7;
    private String compName7;

    @ColumnHeader(titleKey = {"report.competitorRateFor", "method:getCompetitor8Name($data)"}, order = 20, pattern = "#0.00", condition = "showCompetitor8($data)")
    private BigDecimal compRate8;
    private String compName8;

    @ColumnHeader(titleKey = {"report.competitorRateFor", "method:getCompetitor9Name($data)"}, order = 21, pattern = "#0.00", condition = "showCompetitor9($data)")
    private BigDecimal compRate9;
    private String compName9;

    @ColumnHeader(titleKey = {"report.competitorRateFor", "method:getCompetitor10Name($data)"}, order = 22, pattern = "#0.00", condition = "showCompetitor10($data)")
    private BigDecimal compRate10;
    private String compName10;

    @ColumnHeader(titleKey = {"report.competitorRateFor", "method:getCompetitor11Name($data)"}, order = 23, pattern = "#0.00", condition = "showCompetitor11($data)")
    private BigDecimal compRate11;
    private String compName11;

    @ColumnHeader(titleKey = {"report.competitorRateFor", "method:getCompetitor12Name($data)"}, order = 24, pattern = "#0.00", condition = "showCompetitor12($data)")
    private BigDecimal compRate12;
    private String compName12;

    @ColumnHeader(titleKey = {"report.competitorRateFor", "method:getCompetitor13Name($data)"}, order = 25, pattern = "#0.00", condition = "showCompetitor13($data)")
    private BigDecimal compRate13;
    private String compName13;

    @ColumnHeader(titleKey = {"report.competitorRateFor", "method:getCompetitor14Name($data)"}, order = 26, pattern = "#0.00", condition = "showCompetitor14($data)")
    private BigDecimal compRate14;
    private String compName14;

    @ColumnHeader(titleKey = {"report.competitorRateFor", "method:getCompetitor15Name($data)"}, order = 27, pattern = "#0.00", condition = "showCompetitor15($data)")
    private BigDecimal compRate15;
    private String compName15;

    private Integer userId;

    @ColumnHeader(titleKey = "common.lastUpdatedBy", order = 28)
    private String userName;

    @ColumnHeader(titleKey = "common.lastUpdatedOn", order = 29)
    private ZonedDateTime createDateTime;


    public Date getArrivalDate() {
        return arrivalDate;
    }

    public void setArrivalDate(Date arrivalDate) {
        this.arrivalDate = arrivalDate;
    }

    public String getOverride() {
        return override;
    }

    public void setOverride(String override) {
        this.override = override;
    }

    public String getDow() {
        return dow;
    }

    public void setDow(String dow) {
        this.dow = dow;
    }

    public BigDecimal getRoomSold() {
        return roomSold;
    }

    public void setRoomSold(BigDecimal roomSold) {
        this.roomSold = roomSold;
    }

    public BigDecimal getOutOfOrder() {
        return outOfOrder;
    }

    public void setOutOfOrder(BigDecimal outOfOrder) {
        this.outOfOrder = outOfOrder;
    }

    public BigDecimal getOccupancyForecast() {
        return occupancyForecast;
    }

    public void setOccupancyForecast(BigDecimal occupancyForecast) {
        this.occupancyForecast = occupancyForecast;
    }

    public BigDecimal getOccupancyForecastPercent() {
        return occupancyForecastPercent;
    }

    public void setOccupancyForecastPercent(BigDecimal occupancyForecastPercent) {
        this.occupancyForecastPercent = occupancyForecastPercent;
    }

    public BigDecimal getPropertyOccupancyForecast() {
        return propertyOccupancyForecast;
    }

    public void setPropertyOccupancyForecast(
            BigDecimal propertyOccupancyForecast) {
        this.propertyOccupancyForecast = propertyOccupancyForecast;
    }

    public BigDecimal getPropertyOccupancyForecastPercent() {
        return propertyOccupancyForecastPercent;
    }

    public void setPropertyOccupancyForecastPercent(
            BigDecimal propertyOccupancyForecastPercent) {
        this.propertyOccupancyForecastPercent = propertyOccupancyForecastPercent;
    }

    public String getRateCodeName() {
        return rateCodeName;
    }

    public void setRateCodeName(String rateCodeName) {
        this.rateCodeName = rateCodeName;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getAccomClassName() {
        return accomClassName;
    }

    public void setAccomClassName(String accomClassName) {
        this.accomClassName = accomClassName;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public BigDecimal getCompRate1() {
        return compRate1;
    }

    public void setCompRate1(BigDecimal compRate1) {
        this.compRate1 = compRate1;
    }

    public String getCompName1() {
        return compName1;
    }

    public void setCompName1(String compName1) {
        this.compName1 = compName1;
    }

    public BigDecimal getCompRate2() {
        return compRate2;
    }

    public void setCompRate2(BigDecimal compRate2) {
        this.compRate2 = compRate2;
    }

    public String getCompName2() {
        return compName2;
    }

    public void setCompName2(String compName2) {
        this.compName2 = compName2;
    }

    public BigDecimal getCompRate3() {
        return compRate3;
    }

    public void setCompRate3(BigDecimal compRate3) {
        this.compRate3 = compRate3;
    }

    public String getCompName3() {
        return compName3;
    }

    public void setCompName3(String compName3) {
        this.compName3 = compName3;
    }

    public BigDecimal getCompRate4() {
        return compRate4;
    }

    public void setCompRate4(BigDecimal compRate4) {
        this.compRate4 = compRate4;
    }

    public String getCompName4() {
        return compName4;
    }

    public void setCompName4(String compName4) {
        this.compName4 = compName4;
    }

    public BigDecimal getCompRate5() {
        return compRate5;
    }

    public void setCompRate5(BigDecimal compRate5) {
        this.compRate5 = compRate5;
    }

    public String getCompName5() {
        return compName5;
    }

    public void setCompName5(String compName5) {
        this.compName5 = compName5;
    }

    public BigDecimal getLRV() {
        return lrv;
    }

    public void setLRV(BigDecimal lrv) {
        this.lrv = lrv;
    }

    public BigDecimal getTotalRooms() {
        return totalRooms;
    }

    public void setTotalRooms(BigDecimal totalRooms) {
        this.totalRooms = totalRooms;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public ZonedDateTime getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(ZonedDateTime createDateTime) {
        this.createDateTime = createDateTime;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getAccomTypeName() {
        return accomTypeName;
    }

    public void setAccomTypeName(String accomTypeName) {
        this.accomTypeName = accomTypeName;
    }

    public BigDecimal getOccupancyForecastPhysicalCapacityPercent() {
        return occupancyForecastPhysicalCapacityPercent;
    }

    public void setOccupancyForecastPhysicalCapacityPercent(BigDecimal occupancyForecastPhysicalCapacityPercent) {
        this.occupancyForecastPhysicalCapacityPercent = occupancyForecastPhysicalCapacityPercent;
    }

    public BigDecimal getPropertyOccupancyForecastPhysicalCapacityPercent() {
        return propertyOccupancyForecastPhysicalCapacityPercent;
    }

    public void setPropertyOccupancyForecastPhysicalCapacityPercent(BigDecimal propertyOccupancyForecastPhysicalCapacityPercent) {
        this.propertyOccupancyForecastPhysicalCapacityPercent = propertyOccupancyForecastPhysicalCapacityPercent;
    }

    public BigDecimal getCompRate6() {
        return compRate6;
    }

    public void setCompRate6(BigDecimal compRate6) {
        this.compRate6 = compRate6;
    }

    public String getCompName6() {
        return compName6;
    }

    public void setCompName6(String compName6) {
        this.compName6 = compName6;
    }

    public BigDecimal getCompRate7() {
        return compRate7;
    }

    public void setCompRate7(BigDecimal compRate7) {
        this.compRate7 = compRate7;
    }

    public String getCompName7() {
        return compName7;
    }

    public void setCompName7(String compName7) {
        this.compName7 = compName7;
    }

    public BigDecimal getCompRate8() {
        return compRate8;
    }

    public void setCompRate8(BigDecimal compRate8) {
        this.compRate8 = compRate8;
    }

    public String getCompName8() {
        return compName8;
    }

    public void setCompName8(String compName8) {
        this.compName8 = compName8;
    }

    public BigDecimal getCompRate9() {
        return compRate9;
    }

    public void setCompRate9(BigDecimal compRate9) {
        this.compRate9 = compRate9;
    }

    public String getCompName9() {
        return compName9;
    }

    public void setCompName9(String compName9) {
        this.compName9 = compName9;
    }

    public BigDecimal getCompRate10() {
        return compRate10;
    }

    public void setCompRate10(BigDecimal compRate10) {
        this.compRate10 = compRate10;
    }

    public String getCompName10() {
        return compName10;
    }

    public void setCompName10(String compName10) {
        this.compName10 = compName10;
    }

    public BigDecimal getCompRate11() {
        return compRate11;
    }

    public void setCompRate11(BigDecimal compRate11) {
        this.compRate11 = compRate11;
    }

    public String getCompName11() {
        return compName11;
    }

    public void setCompName11(String compName11) {
        this.compName11 = compName11;
    }

    public BigDecimal getCompRate12() {
        return compRate12;
    }

    public void setCompRate12(BigDecimal compRate12) {
        this.compRate12 = compRate12;
    }

    public String getCompName12() {
        return compName12;
    }

    public void setCompName12(String compName12) {
        this.compName12 = compName12;
    }

    public BigDecimal getCompRate13() {
        return compRate13;
    }

    public void setCompRate13(BigDecimal compRate13) {
        this.compRate13 = compRate13;
    }

    public String getCompName13() {
        return compName13;
    }

    public void setCompName13(String compName13) {
        this.compName13 = compName13;
    }

    public BigDecimal getCompRate14() {
        return compRate14;
    }

    public void setCompRate14(BigDecimal compRate14) {
        this.compRate14 = compRate14;
    }

    public String getCompName14() {
        return compName14;
    }

    public void setCompName14(String compName14) {
        this.compName14 = compName14;
    }

    public BigDecimal getCompRate15() {
        return compRate15;
    }

    public void setCompRate15(BigDecimal compRate15) {
        this.compRate15 = compRate15;
    }

    public String getCompName15() {
        return compName15;
    }

    public void setCompName15(String compName15) {
        this.compName15 = compName15;
    }

    public BigDecimal getLrv() {
        return lrv;
    }

    public void setLrv(BigDecimal lrv) {
        this.lrv = lrv;
    }

    @Override
    public String toString() {
        return "PricingByDay{" +
                "arrivalDate=" + arrivalDate +
                ", override='" + override + '\'' +
                ", dow='" + dow + '\'' +
                ", roomSold=" + roomSold +
                ", outOfOrder=" + outOfOrder +
                ", occupancyForecast=" + occupancyForecast +
                ", occupancyForecastPercent=" + occupancyForecastPercent +
                ", occupancyForecastPhysicalCapacityPercent=" + occupancyForecastPhysicalCapacityPercent +
                ", propertyOccupancyForecast=" + propertyOccupancyForecast +
                ", propertyOccupancyForecastPercent=" + propertyOccupancyForecastPercent +
                ", propertyOccupancyForecastPhysicalCapacityPercent=" + propertyOccupancyForecastPhysicalCapacityPercent +
                ", rateCodeName='" + rateCodeName + '\'' +
                ", price=" + price +
                ", accomClassName='" + accomClassName + '\'' +
                ", notes='" + notes + '\'' +
                ", compRate1=" + compRate1 +
                ", compName1='" + compName1 + '\'' +
                ", compRate2=" + compRate2 +
                ", compName2='" + compName2 + '\'' +
                ", compRate3=" + compRate3 +
                ", compName3='" + compName3 + '\'' +
                ", compRate4=" + compRate4 +
                ", compName4='" + compName4 + '\'' +
                ", compRate5=" + compRate5 +
                ", compName5='" + compName5 + '\'' +
                ", compRate6=" + compRate6 +
                ", compName6='" + compName6 + '\'' +
                ", compRate7=" + compRate7 +
                ", compName7='" + compName7 + '\'' +
                ", compRate8=" + compRate8 +
                ", compName8='" + compName8 + '\'' +
                ", compRate9=" + compRate9 +
                ", compName9='" + compName9 + '\'' +
                ", compRate10=" + compRate10 +
                ", compName10='" + compName10 + '\'' +
                ", compRate11=" + compRate11 +
                ", compName11='" + compName11 + '\'' +
                ", compRate12=" + compRate12 +
                ", compName12='" + compName12 + '\'' +
                ", compRate13=" + compRate13 +
                ", compName13='" + compName13 + '\'' +
                ", compRate14=" + compRate14 +
                ", compName14='" + compName14 + '\'' +
                ", compRate15=" + compRate15 +
                ", compName15='" + compName15 + '\'' +
                ", lrv=" + lrv +
                ", totalRooms=" + totalRooms +
                ", userId=" + userId +
                ", createDateTime=" + createDateTime +
                ", userName='" + userName + '\'' +
                ", accomTypeName='" + accomTypeName + '\'' +
                '}';
    }
}
