package com.ideas.tetris.pacman.services.str.service;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.fiscalCalendar.service.FiscalCalendarService;
import com.ideas.tetris.pacman.services.str.entity.STRDaily;
import com.ideas.tetris.pacman.services.str.entity.STRMonthly;
import com.ideas.tetris.pacman.services.str.entity.STRSummary;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.ngi.NGIConvertUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class STRService {
    private static final Logger LOGGER = Logger.getLogger(STRService.class);

    private static final String MANAGEMENT_ID = "mgmtId";
    private static final String HOTEL_NAME = "hotelName";
    private static final String CHAIN_ID = "chainId";
    private static final String OWNER_ID = "ownerId";

    private static final String OCCUPANCY_DATE_THIS_YEAR = "dateTY";
    private static final String PROPERTY_AVAILABLE_THIS_YEAR = "propSupTY";
    private static final String PROPERTY_SOLD_THIS_YEAR = "propDemTY";
    private static final String PROPERTY_REVENUE_THIS_YEAR = "propRevTY";
    private static final String COMP_SET_AVAILABLE_THIS_YEAR = "compSupTY";
    private static final String COMP_SET_SOLD_THIS_YEAR = "compDemTY";
    private static final String COMP_SET_REVENUE_THIS_YEAR = "compRevTY";

    private static final String OCCUPANCY_DATE_LAST_YEAR = "dateLY";
    private static final String PROPERTY_AVAILABLE_LAST_YEAR = "propSupLY";
    private static final String PROPERTY_SOLD_LAST_YEAR = "propDemLY";
    private static final String PROPERTY_REVENUE_LAST_YEAR = "propRevLY";
    private static final String COMP_SET_AVAILABLE_LAST_YEAR = "compSupLY";
    private static final String COMP_SET_SOLD_LAST_YEAR = "compDemLY";
    private static final String COMP_SET_REVENUE_LAST_YEAR = "compRevLY";

    @TenantCrudServiceBean.Qualifier
    @Autowired
    @Qualifier("tenantCrudServiceBean")
    protected CrudService tenantCrudService;

    @Autowired
    FiscalCalendarService fiscalCalendarService;

    @SuppressWarnings("unchecked")


    public void deleteStrDailyData() {
        tenantCrudService.deleteAll(STRDaily.class);
    }

    @SuppressWarnings("unchecked")


    public void deleteStrMonthlyData() {
        tenantCrudService.deleteAll(STRMonthly.class);
    }

    @SuppressWarnings("unchecked")


    public List<STRMonthly> findMonthly() {
        return tenantCrudService.findAll(STRMonthly.class);
    }

    public Map<LocalDate, STRMonthly> getStrMonthlies() {
        List<STRMonthly> strMonthlies = findMonthly();

        return strMonthlies.stream().collect(Collectors.toMap(STRMonthly::getDate, strMonthly -> strMonthly));
    }

    public STRSummary getSTRSummary(LocalDate startDate, LocalDate endDate) {
        List<STRDaily> strDailies =
                tenantCrudService.findByNamedQuery(STRDaily.BY_DATE_RANGE, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters());

        STRSummary summary = new STRSummary();

        if (strDailies.isEmpty()) {
            return summary;
        }

        BigDecimal averageRoomsAvailable = new BigDecimal(0);
        BigDecimal averageRoomsSold = new BigDecimal(0);
        BigDecimal averageRevenue = new BigDecimal(0);
        BigDecimal averageRevPar = new BigDecimal(0);

        for (STRDaily strDaily : strDailies) {
            averageRoomsAvailable = averageRoomsAvailable.add(new BigDecimal(strDaily.getCompetitiveSetAvailable()));
            averageRoomsSold = averageRoomsSold.add(new BigDecimal(strDaily.getCompetitiveSetSold()));
            averageRevenue = averageRevenue.add(strDaily.getCompetitiveSetRevenue());
            averageRevPar = averageRevPar.add(strDaily.getCompSetRevPAR());
        }

        averageRoomsAvailable = averageRoomsAvailable.divide(new BigDecimal(strDailies.size()), 8, BigDecimal.ROUND_HALF_UP);
        averageRoomsSold = averageRoomsSold.divide(new BigDecimal(strDailies.size()), 8, BigDecimal.ROUND_HALF_UP);
        averageRevenue = averageRevenue.divide(new BigDecimal(strDailies.size()), 8, BigDecimal.ROUND_HALF_UP);
        BigDecimal averageAdr = averageRoomsSold.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : averageRevenue.divide(averageRoomsSold, 8, BigDecimal.ROUND_HALF_UP);
        averageRevPar = averageRevPar.divide(new BigDecimal(strDailies.size()), 8, BigDecimal.ROUND_HALF_UP);

        summary.setOccupancyPercent(averageRoomsAvailable.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : averageRoomsSold.divide(averageRoomsAvailable, 8, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100.0)));
        summary.setOccupancy(averageRoomsSold);
        summary.setRevenue(averageRevenue);
        summary.setAdr(averageAdr);
        summary.setRevPar(averageRevPar);

        return summary;
    }

    public List<STRDaily> loadDailies(List<? extends Map<String, Object>> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            LOGGER.warn("STR Daily File Load - NGI Map is empty");
            return Collections.emptyList();
        }

        List<STRDaily> strDailies = new ArrayList<>();

        dtos.forEach(dto -> {
            strDailies.add(loadDaily(dto, true));
            strDailies.add(loadDaily(dto, false));
        });

        if (!strDailies.isEmpty()) {
            List<Object> datesFromDB = tenantCrudService.findByNamedQuery(STRDaily.ALL_DISTINCT_OCCUPANCY_DATES);

            Set<java.time.LocalDate> distinctOccupancyDates = datesFromDB
                    .stream()
                    .map(date -> {
                        return DateUtil.convertJodaToJavaLocalDate((LocalDate) date);
                    })
                    .collect(Collectors.toSet()); // convert the occupancy date to java.time because of the joda restriction

            List<STRDaily> uniqueObjectsToPersist = strDailies.stream()
                    .filter(strDaily -> {
                        return !distinctOccupancyDates.contains(DateUtil.convertJodaToJavaLocalDate(strDaily.getOccupancyDate()));
                    })
                    .collect(Collectors.groupingBy(STRDaily::getOccupancyDate))
                    .values()
                    .stream()
                    .map(list -> list.get(0))
                    .collect(Collectors.toList());

            saveDailies(uniqueObjectsToPersist);
        }
        return strDailies;
    }

    protected STRDaily loadDaily(Map<String, Object> dto, boolean isThisYear) {
        STRDaily strDaily = new STRDaily();

        strDaily.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        strDaily.setMgmtId(NGIConvertUtils.convert(dto.get(MANAGEMENT_ID), Integer.class));
        strDaily.setHotelName((String) dto.get(HOTEL_NAME));
        strDaily.setChainId(NGIConvertUtils.convert(dto.get(CHAIN_ID), Integer.class));
        strDaily.setOwnerId(NGIConvertUtils.convert(dto.get(OWNER_ID), Integer.class));

        strDaily.setOccupancyDate(NGIConvertUtils.convert(isThisYear ? dto.get(OCCUPANCY_DATE_THIS_YEAR) : dto.get(OCCUPANCY_DATE_LAST_YEAR), LocalDate.class));
        strDaily.setPropertyAvailable(NGIConvertUtils.convert(isThisYear ? dto.get(PROPERTY_AVAILABLE_THIS_YEAR) : dto.get(PROPERTY_AVAILABLE_LAST_YEAR), Integer.class));
        strDaily.setPropertySold(NGIConvertUtils.convert(isThisYear ? dto.get(PROPERTY_SOLD_THIS_YEAR) : dto.get(PROPERTY_SOLD_LAST_YEAR), Integer.class));
        strDaily.setPropertyRevenue(NGIConvertUtils.convert(isThisYear ? dto.get(PROPERTY_REVENUE_THIS_YEAR) : dto.get(PROPERTY_REVENUE_LAST_YEAR), BigDecimal.class));
        strDaily.setCompetitiveSetAvailable(NGIConvertUtils.convert(isThisYear ? dto.get(COMP_SET_AVAILABLE_THIS_YEAR) : dto.get(COMP_SET_AVAILABLE_LAST_YEAR), Integer.class));
        strDaily.setCompetitiveSetSold(NGIConvertUtils.convert(isThisYear ? dto.get(COMP_SET_SOLD_THIS_YEAR) : dto.get(COMP_SET_SOLD_LAST_YEAR), Integer.class));
        strDaily.setCompetitiveSetRevenue(NGIConvertUtils.convert(isThisYear ? dto.get(COMP_SET_REVENUE_THIS_YEAR) : dto.get(COMP_SET_REVENUE_LAST_YEAR), BigDecimal.class));

        return strDaily;
    }

    private void saveDailies(List<STRDaily> strDailies) {
        tenantCrudService.save(strDailies);
    }

    public STRDaily getSTRDailyBy(java.time.LocalDate occupancyDate) {
        return tenantCrudService
                .findByNamedQuerySingleResult(STRDaily.BY_DATE_RANGE,
                        QueryParameter.with("startDate", DateUtil.convertJavaToJodaLocalDate(occupancyDate))
                                .and("endDate", DateUtil.convertJavaToJodaLocalDate(occupancyDate))
                                .parameters());
    }

    public List<STRDaily> getSTRDailyForDateRange(java.time.LocalDate startDate, java.time.LocalDate endDate) {
        return tenantCrudService.findByNamedQuery(STRDaily.BY_DATE_RANGE,
                QueryParameter.with("startDate", DateUtil.convertJavaToJodaLocalDate(startDate))
                        .and("endDate", DateUtil.convertJavaToJodaLocalDate(endDate))
                        .parameters());
    }

    public java.time.LocalDate getMinimumOccupancyDateInSTRDaily() {
        LocalDate minimumOccupancyDate = tenantCrudService.findByNamedQuerySingleResult(STRDaily.MINIMUM_OCCUPANCY_DATE);
        return Objects.nonNull(minimumOccupancyDate) ? DateUtil.convertJodaToJavaLocalDate(minimumOccupancyDate) : null;
    }

    public java.time.LocalDate getMaximumOccupancyDateInSTRDaily() {
        LocalDate maximumOccupancyDate = tenantCrudService.findByNamedQuerySingleResult(STRDaily.MAXIMUM_OCCUPANCY_DATE);
        return Objects.nonNull(maximumOccupancyDate) ? DateUtil.convertJodaToJavaLocalDate(maximumOccupancyDate) : null;
    }
}
