package com.ideas.tetris.pacman.services.channelRestrictionAdjustment;

import com.ideas.tetris.pacman.Service;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.channelRestrictionAdjustment.repository.ChannelRestrictionAdjustmentRepository;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.services.daoandentities.entity.ChannelRestrictionAdjustment;
import com.ideas.tetris.platform.services.daoandentities.entity.ChannelRestrictionAdjustmentExcludedSRP;
import com.ideas.tetris.platform.services.globalproperty.service.ClientPropertyCacheService;
import org.apache.log4j.Logger;

import javax.inject.Inject;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

@Service
@Component
public class ChannelRestrictionAdjustmentService {

    private static final Logger LOGGER = Logger.getLogger(ChannelRestrictionAdjustmentService.class);
    @Autowired
    ClientPropertyCacheService clientPropertyCacheService;
    @Autowired
	private ChannelRestrictionAdjustmentRepository repository;
    @Autowired
	protected JobServiceLocal jobService;

    @Autowired
	private SyncChannelRestrictionAdjustmentService syncChannelRestrictionAdjustmentService;

    public ChannelRestrictionAdjustment saveChannelRestrictionAdjustment(ChannelRestrictionAdjustment channelRestrictionAdjustment) {
        return repository.saveChannelRestrictionAdjustment(channelRestrictionAdjustment);
    }

    public boolean deleteChannelRestrictionAdjustmentById(Integer channelRestrictionAdjustmentId) {
        return repository.deleteChannelRestrictionAdjustmentById(channelRestrictionAdjustmentId);
    }

    public ChannelRestrictionAdjustment getChannelRestrictionAdjustmentById(Integer channelRestrictionAdjustmentId) {
        return repository.getChannelRestrictionAdjustmentById(channelRestrictionAdjustmentId);
    }

    public List<ChannelRestrictionAdjustmentExcludedSRP> getExcludedSRPsByChannelRestrictionAdjustmentId(Integer channelRestrictionAdjustmentId) {
        return repository.getExcludedSRPsByChannelRestrictionAdjustmentId(channelRestrictionAdjustmentId);
    }

    public List<ChannelRestrictionAdjustment> getChannelRestrictionAdjustmentsBySrpGroups(List<String> srpGroups) {
        return repository.getChannelRestrictionAdjustmentsBySrpGroups(srpGroups);
    }

    public List<ChannelRestrictionAdjustment> getAllChannelRestrictionAdjustment() {
        return repository.getAllChannelRestrictionAdjustment();
    }

    public ChannelRestrictionAdjustment getChannelRestrictionAdjustment(String brandName, String globalRegion, String srpGroupCode,
                                                                        Date startDate, Date endDate) {
        return repository.getChannelRestrictionAdjustment(brandName, globalRegion, srpGroupCode, startDate, endDate);
    }

    public boolean syncWithTenantAdjustments() {
        if (!isJobAlreadyRunning()) {
            LOGGER.info("Triggering SyncChannelRestrictionAdjustmentJob for client " + PacmanWorkContextHelper.getClientCode());
            jobService.startGuaranteedNewInstance(JobName.SyncChannelRestrictionAdjustmentJob, getParameterMap());
            return true;
        }
        LOGGER.info("SyncChannelRestrictionAdjustmentJob for client " + PacmanWorkContextHelper.getClientCode() + " is already running");
        return false;
    }

    public boolean isJobAlreadyRunning() {
        return jobService.isJobActive(JobName.SyncChannelRestrictionAdjustmentJob, getParameterMap());
    }

    private Map<String, Object> getParameterMap() {
        Map<String, Object> parameterMap = new HashMap();
        parameterMap.put(JobParameterKey.CLIENT_CODE, PacmanWorkContextHelper.getClientCode());
        return parameterMap;
    }
}