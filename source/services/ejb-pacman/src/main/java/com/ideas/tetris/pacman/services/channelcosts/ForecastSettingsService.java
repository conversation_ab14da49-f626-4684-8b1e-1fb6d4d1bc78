package com.ideas.tetris.pacman.services.channelcosts;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.channelcosts.entity.ChannelDemandChannel;
import com.ideas.tetris.pacman.services.channelcosts.entity.ChannelDemandSource;
import com.ideas.tetris.pacman.services.channelcosts.entity.DOWType;
import com.ideas.tetris.pacman.services.channelcosts.hierarchical.entity.ForecastData;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.ChannelCostSource;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantProperty;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

import static com.ideas.tetris.pacman.services.channelcosts.entity.ChannelDemandChannel.INSERT_CHANNELS;
import static com.ideas.tetris.pacman.services.channelcosts.entity.ChannelDemandChannel.SELECT_CHANNEL;
import static com.ideas.tetris.pacman.services.channelcosts.entity.ChannelDemandChannel.SELECT_NON_EMPTY_CHANNEL;
import static com.ideas.tetris.pacman.services.channelcosts.entity.ChannelDemandSource.INSERT_SOURCES;
import static com.ideas.tetris.pacman.services.channelcosts.entity.ChannelDemandSource.SELECT_NON_EMPTY_SOURCES;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class ForecastSettingsService {

    private static final String PROPERTY_ID = "propertyId";

    private static String UNASSIGNED_CHANNEL_NAME = "";

    @TenantCrudServiceBean.Qualifier
	@Autowired
	@Qualifier("tenantCrudServiceBean")
    CrudService tenantCrudService;

    @Autowired
	private SyncEventAggregatorService syncEventAggregatorService;


    public List<DOWType> getAllDOWType() {
        return tenantCrudService.findAll(DOWType.class);
    }

    public void saveForecastSettingsData(List<DOWType> dowTypes, ForecastData channelOrSource,
                                         List<ChannelDemandSource> channelDemandSources,
                                         List<ChannelDemandChannel> channelDemandChannels, boolean isSyncRequired) {
        saveForecastDataSourceForProperty(channelOrSource);
        tenantCrudService.save(dowTypes);
        if (CollectionUtils.isNotEmpty(channelDemandSources)) {
            tenantCrudService.save(channelDemandSources);
        }
        if (CollectionUtils.isNotEmpty(channelDemandChannels)) {
            channelDemandChannels.forEach(channelDemandChannel -> {
                if (channelDemandChannel.getName().equals(UNASSIGNED_CHANNEL_NAME)) {
                    channelDemandChannel.setName("");
                }
            });
            tenantCrudService.save(channelDemandChannels);
        }
        //We can add 5 default .On changing default 5 we still need to save that data but we don't
        // need to turn on sync in that case because that data would be already present in db
        if (isSyncRequired) {
            syncEventAggregatorService.registerSyncEvent(SyncEvent.FORECAST_SETTINGS_CONFIGURATION_CHANGED);
        }
    }

    public void saveForecastDataSourceForProperty(ForecastData channelOrSource) {
        TenantProperty property = getProperty();
        ChannelCostSource channelCostSource = ChannelCostSource.valueOf(channelOrSource.name());
        property.setChannelCostSource(channelCostSource);
        tenantCrudService.save(property);
    }

    private TenantProperty getProperty() {
        return tenantCrudService.findByNamedQuerySingleResult(TenantProperty.GET_BY_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public ForecastData getForecastData() {
        TenantProperty property = getProperty();
        ChannelCostSource channelCostSource = property.getChannelCostSource();
        return ObjectUtils.isNotEmpty(channelCostSource) && EnumUtils.isValidEnum(ForecastData.class, channelCostSource.name()) ? ForecastData.valueOf(channelCostSource.name()) : null;
    }

    public void deleteChannelSourceForecast() {
        tenantCrudService.executeUpdateByNativeQuery("truncate table Channel_Src_FCST");
    }

    public List<ChannelDemandSource> getAllSources() {
        return tenantCrudService.findByNamedQuery(SELECT_NON_EMPTY_SOURCES);
    }

    public List<ChannelDemandChannel> getAllChannels(boolean isChannelForecastKPIDataEnabled) {
        if (!isChannelForecastKPIDataEnabled)
            return tenantCrudService.findByNamedQuery(SELECT_NON_EMPTY_CHANNEL);
        else {
            List<ChannelDemandChannel> allChannels = tenantCrudService.findByNamedQuery(SELECT_CHANNEL);
            List<ChannelDemandChannel> modifiedList = new ArrayList<>();

            for(ChannelDemandChannel channels: allChannels)
            {
                ChannelDemandChannel newDemandChannel = new ChannelDemandChannel();
                newDemandChannel.setId(channels.getId());
                newDemandChannel.setName((channels.getName().isBlank() || channels.getName().isEmpty()) ? UNASSIGNED_CHANNEL_NAME: channels.getName());
                newDemandChannel.setDefault(channels.isDefault());
                modifiedList.add(newDemandChannel);
            }
            modifiedList.sort(Comparator.comparing(channel ->
                    UNASSIGNED_CHANNEL_NAME.equals(channel.getName()) ? 1 : 0
            ));
            return modifiedList;
        }
    }

    public void populateSourceAndChannels() {
        tenantCrudService.executeUpdateByNamedQuery(INSERT_SOURCES);
        tenantCrudService.executeUpdateByNamedQuery(INSERT_CHANNELS);
    }

    public void populateChannelSourceMapping() {
        tenantCrudService.executeUpdateByNativeQuery("EXEC usp_update_channel_source_mapping;");
    }

    public void setUnAssignedChannelName(String text) {
        UNASSIGNED_CHANNEL_NAME = text;
    }
}
