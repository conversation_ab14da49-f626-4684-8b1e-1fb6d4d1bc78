package com.ideas.tetris.pacman.services.decisionconfig;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.platform.common.externalsystem.ExternalSystem;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import java.util.*;

import static com.ideas.tetris.pacman.common.constants.Constants.*;
import static com.ideas.tetris.pacman.services.decisionconfig.OutboundElement.*;
import static java.util.Arrays.asList;
import static org.apache.commons.lang.StringUtils.equalsIgnoreCase;
import static org.apache.commons.lang.StringUtils.isEmpty;
import static org.jadira.usertype.spi.utils.lang.StringUtils.isNotEmpty;

public enum DecisionConfig implements DecisionConfigValueValidation {

    VENDOR_SYSTEM_PROPERTY_ID() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            if (StringUtils.isEmpty(value)) {
                return new DecisionConfigValidationError(VENDOR_SYSTEM_PROPERTY_ID_IS_MANDATORY);
            } else {
                resultElements.put(OutboundElement.VENDOR_SYSTEM_PROPERTY_ID, new ConfigParamValueWithReasonDto(value.trim()));
                return null;
            }
        }
    },
    DAILY_BAR_UPLOAD_FORMAT() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            String dailyBarByRoomType = DAILY_BAR_BY_ROOM_TYPE;
            if (ExternalSystem.OPERA.name().equalsIgnoreCase(externalSystem) || (ExternalSystem.OCP.name().equalsIgnoreCase(externalSystem))) {
                return validateAndChangeElementsForDailyBAR(value.trim(), resultElements, dailyBarByRoomType, OutboundElement.DAILY_BAR);
            }
            if (ExternalSystem.TARSHTNG.name().equalsIgnoreCase(externalSystem)) {
                return validateAndChangeElementsForDailyBAR(value.trim(), resultElements, dailyBarByRoomType, OutboundElement.DAILY_BAR);
            }
            return new DecisionConfigValidationError("Upload format should be N/A or  " + dailyBarByRoomType);
        }
    },

    PROPERTY_CODE_HTNG() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            if (isNotEmpty(value) && Objects.equals(PacmanWorkContextHelper.getPropertyCode(), value.trim())) {
                resultElements.put(OutboundElement.PROPERTY_CODE_HTNG, new ConfigParamValueWithReasonDto(value.trim()));
                return null;
            } else {
                return new DecisionConfigValidationError(PROPERTY_CODE_IS_INVALID);
            }
        }
    },

    HTNG_USERNAME() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            if (isNotEmpty(value)) {
                resultElements.put(OutboundElement.HTNG_USERNAME, new ConfigParamValueWithReasonDto(value.trim()));
                return null;
            } else {
                return new DecisionConfigValidationError(USERNAME_IS_INVALID);
            }
        }
    },

    HTNG_PASSWORD() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            if (isNotEmpty(value)) {
                resultElements.put(OutboundElement.HTNG_PASSWORD, new ConfigParamValueWithReasonDto(value));
                return null;
            } else {
                return new DecisionConfigValidationError(PASSWORD_IS_INVALID);
            }
        }
    },

    HTNG_URL() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            if (isEmpty(value)) {
                return new DecisionConfigValidationError(URL_SHOULD_NOT_BE_EMPTY);
            } else {
                resultElements.put(OutboundElement.HTNG_URL, new ConfigParamValueWithReasonDto(value));
                return null;
            }
        }
    },

    HTNG_REGION_WISE_URL() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            if (isEmpty(value)) {
                return new DecisionConfigValidationError(URL_SHOULD_NOT_BE_EMPTY);
            } else {
                resultElements.put(OutboundElement.HTNG_REGION_WISE_URL, new ConfigParamValueWithReasonDto(value));
                return null;
            }
        }
    },
    MISCELLANEOUS_ADJUSTMENT() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return validateAndAddMiscellaneousAdjustment(value, changedElements, resultElements,OutboundElement.MISCELLANEOUS_ADJUSTMENT);
        }
    },
    DAILY_BAR_BY_RATE_CODE() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return null;
        }
    },
    DAILY_BAR_ROUNDING_PRECISION() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            try {
                if (isEmpty(value)) {
                    return new DecisionConfigValidationError(BAR_ROUNDING_PRECISIOB_SHOULD_BE_NA_OR_POSITIVE_NUMBER);
                }
                String barUpload = changedElements.get(OutboundElement.DAILY_BAR).getConfigElementValue();
                if (barUpload !=null && NOT_APPLICABLE.equalsIgnoreCase(barUpload.trim()) && NOT_APPLICABLE.equalsIgnoreCase(value)) {
                    resultElements.put(OutboundElement.DAILY_BAR_ROUNDING_PRECISION, new ConfigParamValueWithReasonDto(""));
                    return null;
                }
                if(!NOT_APPLICABLE.equalsIgnoreCase(barUpload) && NOT_APPLICABLE.equalsIgnoreCase(value)) {
                    resultElements.put(OutboundElement.DAILY_BAR_ROUNDING_PRECISION, new ConfigParamValueWithReasonDto(""));
                    return null;
                }
                if ("OFF".equalsIgnoreCase(value) || NOT_APPLICABLE.equalsIgnoreCase(value)) {
                    resultElements.put(OutboundElement.DAILY_BAR_ROUNDING_PRECISION, new ConfigParamValueWithReasonDto(""));
                }
                int precision = Integer.parseInt(value);
                if (precision >= 0) {
                    resultElements.put(OutboundElement.DAILY_BAR_ROUNDING_PRECISION, new ConfigParamValueWithReasonDto(Integer.toString(precision)));
                    return null;
                } else {
                    return new DecisionConfigValidationError(BAR_ROUNDING_PRECISIOB_SHOULD_BE_NA_OR_POSITIVE_NUMBER);
                }
            } catch (Exception e) {
                return new DecisionConfigValidationError(BAR_ROUNDING_PRECISIOB_SHOULD_BE_NA_OR_POSITIVE_NUMBER);
            }
        }
    },

    DAILY_BAR_RATE_CODE() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return validateAndAddRateCode(value, changedElements,resultElements, OutboundElement.DAILY_BAR_RATE_CODE);
        }
    },

    DAILY_BAR_MISCELLANEOUS_ADJUSTMENT() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return validateAndAddMiscellaneousAdjustment(value, changedElements, resultElements, OutboundElement.DAILY_BAR_MISCELLANEOUS_ADJUSTMENT);
        }
    },
    AGILE_RATES() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {

            String val = value.trim();
            if (TRUE.equalsIgnoreCase(val) || "ON".equalsIgnoreCase(val)) {
                resultElements.put(OutboundElement.AGILE_RATES, new ConfigParamValueWithReasonDto(DIFFERENTIAL));
                return null;
            } else if (FALSE.equalsIgnoreCase(val) || "OFF".equalsIgnoreCase(val)) {
                resultElements.put(OutboundElement.AGILE_RATES, new ConfigParamValueWithReasonDto(NONE));
                return null;
            }else{
                return new DecisionConfigValidationError(AGILE_RATES_VALUE_SHOULD_BE_TRUE_FALSE_OR_ON_OFF);
            }
        }
    },

    CHILD_AGE_BUCKETS() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            String val = value.trim();
            if (TRUE.equalsIgnoreCase(val) || "ON".equalsIgnoreCase(val)) {
                resultElements.put(OutboundElement.CHILD_AGE_BUCKETS, new ConfigParamValueWithReasonDto(TRUE));
                return null;
            } else if (FALSE.equalsIgnoreCase(val) || "OFF".equalsIgnoreCase(val)) {
                resultElements.put(OutboundElement.CHILD_AGE_BUCKETS, new ConfigParamValueWithReasonDto(FALSE));
                return null;
            } else {
                return new DecisionConfigValidationError(UPLOAD_CHILD_AGE_BUCKET_SHOULD_BE_TRUE_FALSE_OR_ON_OFF);
            }
        }
    },


    LRV_BY_ROOM_CLASS() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            if (isEmpty(value)) {
                return new DecisionConfigValidationError(LRV_VALUE_SHOULD_BE_NA_OR_BY_ROOM_CLASS);
            }
            String val = value.trim();
            String tax = changedElements.get(OutboundElement.TAX_LRV).getConfigElementValue();
            if (BY_ROOM_CLASS.equalsIgnoreCase(val)) {
                resultElements.put(OutboundElement.LRV_BY_ROOM_CLASS, new ConfigParamValueWithReasonDto(FULL));
                return null;
            }
            if (NOT_APPLICABLE.equalsIgnoreCase(val) && NOT_APPLICABLE.equalsIgnoreCase(tax)) {
                resultElements.put(OutboundElement.LRV_BY_ROOM_CLASS, new ConfigParamValueWithReasonDto(NONE));
                return null;
            } else if (NOT_APPLICABLE.equalsIgnoreCase(val) && !NOT_APPLICABLE.equalsIgnoreCase(tax)) {
                return new DecisionConfigValidationError(WHEN_LRV_IS_NA_THEN_LRV_TAX_SHOULD_BE_NA);
            } else {
                return new DecisionConfigValidationError(LRV_VALUE_SHOULD_BE_NA_OR_BY_ROOM_CLASS);
            }
        }
    },

    LRV_AT_ROOM_TYPE() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            if (isEmpty(value)) {
                return new DecisionConfigValidationError(LRV_VALUE_SHOULD_BE_NA_OR_AT_ROOM_TYPE);
            }
            String val = value.trim();
            if (AT_ROOM_TYPE.equalsIgnoreCase(val)) {
                resultElements.put(OutboundElement.LRV_AT_ROOM_TYPE, new ConfigParamValueWithReasonDto(FULL));
                updateChangedElementsForConsolidateDelta(resultElements);
                return null;
            }
            if (NONE.equalsIgnoreCase(val) || "OFF".equalsIgnoreCase(val) || NOT_APPLICABLE.equalsIgnoreCase(val)) {
                DecisionConfigValidationError error = validateThatLRVTaxIsNotApplicableWhenLRVIsNotApplicable(changedElements);
                if(error != null) {
                    return error;
                }else {
                    resultElements.put(OutboundElement.LRV_AT_ROOM_TYPE, new ConfigParamValueWithReasonDto(NONE));
                    updateChangedElementsForConsolidateDelta(resultElements);
                }
                return null;
            } else {
                return new DecisionConfigValidationError(LRV_VALUE_SHOULD_BE_NA_OR_AT_ROOM_TYPE);
            }
        }
    },
    LRV_AT_ROOM_CLASS() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            if (isEmpty(value)) {
                return new DecisionConfigValidationError(LRV_VALUE_SHOULD_BE_NA_OR_AT_ROOM_CLASS);
            }
            String val = value.trim();
            if(ExternalSystem.OCP.name().equalsIgnoreCase(externalSystem)){
                DecisionConfigValidationError error = validateLRVParamsIsNAWhenLRVIsNA(changedElements, externalSystem);
                if (error != null) {
                    return error;
                }
            }
            if (AT_ROOM_CLASS.equalsIgnoreCase(val)) {
                resultElements.put(OutboundElement.LRV_AT_ROOM_CLASS, new ConfigParamValueWithReasonDto(FULL));
                if (!ExternalSystem.OCP.name().equalsIgnoreCase(externalSystem)) {
                    updateChangedElementsForConsolidateDelta(changedElements);
                }
                return null;
            }
            if (NONE.equalsIgnoreCase(val) || "OFF".equalsIgnoreCase(val) || NOT_APPLICABLE.equalsIgnoreCase(val)) {
                resultElements.put(OutboundElement.LRV_AT_ROOM_CLASS, new ConfigParamValueWithReasonDto(NONE));
                if (!ExternalSystem.OCP.name().equalsIgnoreCase(externalSystem)) {
                    updateChangedElementsForConsolidateDelta(changedElements);
                }
                return null;
            } else {
                return new DecisionConfigValidationError(LRV_VALUE_SHOULD_BE_NA_OR_AT_ROOM_CLASS);
            }
        }
    },
    LRV() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return null;
        }
    },
    TAX() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            DecisionConfigValidationError error = validateIfDailyBARTaxAndLRVTaxAreEqual(changedElements, externalSystem);
            if (error != null) {
                return error;
            }else{
                return validateAndAddLRVTax(value, changedElements, resultElements, OutboundElement.TAX, externalSystem);
            }
        }
    },
    LRV_ROOM_TYPE_TAX() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            DecisionConfigValidationError error = validateThatLRVTaxIsNotApplicableWhenLRVIsNotApplicable(changedElements);
            if (error != null) {
                return error;
            } else {
                return validateAndAddLRVTax(value, changedElements, resultElements, OutboundElement.LRV_ROOM_TYPE_TAX, externalSystem);
            }
        }
    },
    USE_TAX() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            String dailyBAR = changedElements.get(OutboundElement.DAILY_BAR).getConfigElementValue();
            if (isEmpty(value)) {
                return new DecisionConfigValidationError(USE_TAX_SHOULD_BE_AMOUNT_BEFORE_TAX_OR_AMOUNT_AFTER_TAX);
            }
            if (equalsIgnoreCase(AMOUNT_BEFORE_TAX, value.trim())) {
                ConfigParamValueWithReasonDto dailyBARTax = changedElements.get(OutboundElement.DAILY_BAR_TAX);
                if (isTaxValid(dailyBARTax, true)) {
                    resultElements.put(OutboundElement.DAILY_BAR_TAX, new ConfigParamValueWithReasonDto(""));
                    return null;
                }else if(!NOT_APPLICABLE.equalsIgnoreCase(dailyBAR)){
                    return new DecisionConfigValidationError("Tax should be N/A when Amount Before Tax is selected");
                }else{
                    return null;
                }
            }
            if (equalsIgnoreCase(AMOUNT_AFTER_TAX, value.trim())) {
                ConfigParamValueWithReasonDto configParamValue = changedElements.get(OutboundElement.DAILY_BAR_TAX);
                if (isTaxValid(configParamValue, false)) {
                    resultElements.put(OutboundElement.DAILY_BAR_TAX, new ConfigParamValueWithReasonDto("0.0"));
                    return null;
                } else {
                    return new DecisionConfigValidationError("Tax should be 0 when Amount After Tax is selected.");
                }
            }
            return new DecisionConfigValidationError(USE_TAX_SHOULD_BE_AMOUNT_BEFORE_TAX_OR_AMOUNT_AFTER_TAX);
        }
    },

    DAILY_BAR_TAX() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return null;
        }
    },

    MIN_LOS_BY_RATE_CODE() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return validateAndAddLOS(value, resultElements, OutboundElement.MIN_LOS_BY_RATE_CODE,
                    asList(OutboundElement.MIN_LOS_BY_RATE_CODE_BY_ROOM_TYPE, OutboundElement.MIN_LOS_BY_RATE_CATEGORY, OutboundElement.MIN_LOS_BY_RATE_CATEGORY_BY_ROOM_TYPE));
        }
    },

    MIN_LOS_BY_RATE_CODE_BY_ROOM_TYPE() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return validateAndAddLOS(value, resultElements, OutboundElement.MIN_LOS_BY_RATE_CODE_BY_ROOM_TYPE,
                    asList(OutboundElement.MIN_LOS_BY_RATE_CODE, OutboundElement.MIN_LOS_BY_RATE_CATEGORY, OutboundElement.MIN_LOS_BY_RATE_CATEGORY_BY_ROOM_TYPE));
        }
    },

    MIN_LOS_BY_RATE_CATEGORY() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return validateAndAddLOS(value, resultElements, OutboundElement.MIN_LOS_BY_RATE_CATEGORY,
                    asList(OutboundElement.MIN_LOS_BY_RATE_CODE, OutboundElement.MIN_LOS_BY_RATE_CODE_BY_ROOM_TYPE, OutboundElement.MIN_LOS_BY_RATE_CATEGORY_BY_ROOM_TYPE));
        }
    },

    MIN_LOS_BY_RATE_CATEGORY_BY_ROOM_TYPE() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return validateAndAddLOS(value, resultElements, OutboundElement.MIN_LOS_BY_RATE_CATEGORY_BY_ROOM_TYPE,
                    asList(OutboundElement.MIN_LOS_BY_RATE_CODE, OutboundElement.MIN_LOS_BY_RATE_CODE_BY_ROOM_TYPE, OutboundElement.MIN_LOS_BY_RATE_CATEGORY));
        }
    },
    MINLOS() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return null;
        }
    },
    FPLOS_BY_RATE_CATEGORY() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return validateAndAddLOS(value, resultElements, OutboundElement.FPLOS_BY_RATE_CATEGORY,
                    asList(OutboundElement.FPLOS_BY_RATE_CODE,
                            OutboundElement.FPLOS_BY_RATE_CODE_BY_ROOM_TYPE, OutboundElement.FPLOS_BY_RATE_CATEGORY_BY_ROOM_TYPE));
        }
    },
    FPLOS_AT_ROOM_CATEGORY() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return validateAndAddLOS(value, resultElements, OutboundElement.FPLOS_AT_ROOM_CATEGORY,
                    asList(OutboundElement.FPLOS_BY_RATE_CATEGORY, OutboundElement.FPLOS_BY_RATE_CODE,
                            OutboundElement.FPLOS_BY_RATE_CODE_BY_ROOM_TYPE, OutboundElement.FPLOS_BY_RATE_CATEGORY_BY_ROOM_TYPE));
        }
    },

    FPLOS_BY_RATE_CODE () {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return validateAndAddLOS(value, resultElements, OutboundElement.FPLOS_BY_RATE_CODE,
                    asList(OutboundElement.FPLOS_BY_RATE_CATEGORY, OutboundElement.FPLOS_BY_RATE_CODE_BY_ROOM_TYPE, OutboundElement.FPLOS_BY_RATE_CATEGORY_BY_ROOM_TYPE));
        }
    },

    FPLOS_BY_RATE_CODE_BY_ROOM_TYPE() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return validateAndAddLOS(value, resultElements, OutboundElement.FPLOS_BY_RATE_CODE_BY_ROOM_TYPE,
                    asList(OutboundElement.FPLOS_BY_RATE_CATEGORY, OutboundElement.FPLOS_BY_RATE_CODE, OutboundElement.FPLOS_BY_RATE_CATEGORY_BY_ROOM_TYPE));
        }
    },

    FPLOS_BY_RATE_CATEGORY_BY_ROOM_TYPE() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return validateAndAddLOS(value, resultElements, OutboundElement.FPLOS_BY_RATE_CATEGORY_BY_ROOM_TYPE,
                    asList(OutboundElement.FPLOS_BY_RATE_CATEGORY, OutboundElement.FPLOS_BY_RATE_CODE, OutboundElement.FPLOS_BY_RATE_CODE_BY_ROOM_TYPE));
        }
    },
    FPLOS() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return null;
        }
    },

    LRV_ROOM_CLASS_INCLUDE_CEILING_DELTA_MAX_SOLD() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return validateAndAddIncludeDeltas(value, resultElements, OutboundElement.LRV_ROOM_CLASS_INCLUDE_CEILING_DELTA_MAX_SOLD);
        }
    },
    LRV_ROOM_TYPE_INCLUDE_CEILING_DELTA_MAX_SOLD() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return validateAndAddIncludeDeltas(value, resultElements, OutboundElement.LRV_ROOM_TYPE_INCLUDE_CEILING_DELTA_MAX_SOLD);
        }
    },

    CEILING_DELTA_MAX_SOLD() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            DecisionConfigValidationError error = validateCeilingDeltaIsNAWhenLRVIsNA(changedElements);
            if (error != null) {
                return error;
            } else {
                return validateAndAddIncludeDeltas(value, resultElements, OutboundElement.CEILING_DELTA_MAX_SOLD);
            }
        }
    },
    ROUNDING_LRV() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return validateAndAddRoundingRule(value, resultElements, OutboundElement.ROUNDING_LRV);
        }
    },
    ROUNDING() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return validateAndAddRoundingRule(value, resultElements, OutboundElement.ROUNDING);
        }
    },
    DAILY_BAR() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            DecisionConfigValidationError error;
            error = validateBARRateCodeWhenUploadFormatIsValid(changedElements,externalSystem);
            if(error != null) {
                return error;
            }
            if (ExternalSystem.TARSHTNG.name().equalsIgnoreCase(externalSystem)) {
                error = validateBARParamsIsNAWhenUploadFormatIsNA(changedElements);
            }
            if (ExternalSystem.OPERA.name().equalsIgnoreCase(externalSystem)) {
                error = validateBARParamsWhenUploadFormatIsNA(changedElements);
            }
            if(ExternalSystem.OCP.name().equalsIgnoreCase(externalSystem)) {
                error = validateBARRateCodeWhenUploadFormatIsNA(changedElements);
            }
            if (error != null) {
                return error;
            } else {
                return validateAndAddDailyBAR(value, resultElements, OutboundElement.DAILY_BAR);
            }
        }
    },

    DAILY_BAR_PMS_RATE_CODE() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return validateAndAddRateCode(value, changedElements, resultElements, OutboundElement.DAILY_BAR_PMS_RATE_CODE);
        }
    },

    HOTEL_OVERBOOKING() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return validateAndAddOverbooking(value, resultElements, OutboundElement.HOTEL_OVERBOOKING);
        }
    },

    ROOM_TYPE_OVERBOOKING() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return validateAndAddOverbooking(value, resultElements, OutboundElement.ROOM_TYPE_OVERBOOKING);
        }
    },
    TAX_LRV() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            if(ExternalSystem.OPERA.name().equalsIgnoreCase(externalSystem)) {
                return validateAndAddLRVTax(value, changedElements, resultElements, OutboundElement.TAX_LRV, externalSystem);
            }
            return validateAndAddLRVTax(value, changedElements, resultElements, OutboundElement.LRV_ROOM_CLASS_TAX, externalSystem);
        }
    },

    LRV_ROOM_CLASS_TAX() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            if(ExternalSystem.OCP.name().equalsIgnoreCase(externalSystem)){
                DecisionConfigValidationError error = validateThatLRVTaxWhenLRVIsNotApplicable(changedElements);
                if(error != null){
                    return error;
                }else {
                    return validateAndAddLRVTax(value, changedElements, resultElements, OutboundElement.LRV_ROOM_CLASS_TAX, externalSystem);
                }
            }
            return validateAndAddLRVTax(value, changedElements, resultElements, OutboundElement.LRV_ROOM_CLASS_TAX, externalSystem);
        }
    },
    LRV_ROOM_CLASS_ROUNDING_PRECISION() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            if (isEmpty(value)) {
                return new DecisionConfigValidationError(LRV_ROUNDING_SHOULD_BE_NA_OR_POSITIVE_INTEGER);
            }
            if ("OFF".equalsIgnoreCase(value) || NOT_APPLICABLE.equalsIgnoreCase(value)) {
                resultElements.put(OutboundElement.LRV_ROOM_CLASS_ROUNDING_PRECISION, new ConfigParamValueWithReasonDto(""));
                return null;
            }
            try {
                String val = value.trim();
                if (NumberUtils.isNumber(val) && (Double.parseDouble(val) >= 0)) {
                    resultElements.put(OutboundElement.LRV_ROOM_CLASS_ROUNDING_PRECISION, new ConfigParamValueWithReasonDto(val));
                    return null;

                }
            } catch (Exception e) {
                return new DecisionConfigValidationError(LRV_ROUNDING_SHOULD_BE_NA_OR_POSITIVE_INTEGER);
            }
            return new DecisionConfigValidationError(LRV_ROUNDING_SHOULD_BE_NA_OR_POSITIVE_INTEGER);
        }
    },
    VENDOR() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            if (StringUtils.isBlank(value)) {
                return new DecisionConfigValidationError(VENDOR_IS_MANDATORY);
            } else {
                resultElements.put(OutboundElement.VENDOR, new ConfigParamValueWithReasonDto(value.trim()));
                return null;
            }
        }
    },
    PROFIT_ADJUSTMENT() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return validateRichToggle(value, resultElements, OutboundElement.PROFIT_ADJUSTMENT, PROFIT_ADJUSTMENT_SHOULD_BE_TRUE_FALSE);
        }
    },
    HOTEL_CLOSE_TO_ARRIVAL() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return validateRichToggle(value, resultElements, OutboundElement.HOTEL_CLOSE_TO_ARRIVAL, HOTEL_CLOSE_TO_ARRIVAL_SHOULD_BE_TRUE_FALSE);
        }
    },
    ALL() {
        @Override
        public DecisionConfigValidationError addChangeElementsIfValid(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, String externalSystem) {
            return null;
        }
    };

    private static DecisionConfigValidationError validateIfDailyBARTaxAndLRVTaxAreEqual(Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                                        String externalSystem) {
        if(ExternalSystem.OPERA.name().equalsIgnoreCase(externalSystem)) {
            String lrvTax = changedElements.get(OutboundElement.TAX_LRV).getConfigElementValue();
            String barTax = changedElements.get(OutboundElement.TAX).getConfigElementValue();
            if(lrvTax !=null && !lrvTax.equalsIgnoreCase(barTax)) {
                return new DecisionConfigValidationError("'TAX' for BAR and 'TAX_LRV' should be same for OPERA - It can be either 'N/A' or Same Positive 'Number'.");
            }
        }
        return null;
    }

    private static final String WHEN_LRV_IS_NA_THEN_LRV_TAX_SHOULD_BE_NA = "When LRV is 'By RoomClass' then Tax LRV should be N/A";
    private static final String VENDOR_SYSTEM_PROPERTY_ID_IS_MANDATORY = "Vendor System Property ID cannot be empty";
    private static final String VENDOR_IS_MANDATORY = "Vendor cannot be empty";
    private static final String PROPERTY_CODE_IS_INVALID = "Property Code is invalid";
    private static final String USERNAME_IS_INVALID = "Username is invalid";
    private static final String PASSWORD_IS_INVALID = "Password is invalid";
    private static final String URL_SHOULD_NOT_BE_EMPTY = "URL should not be empty";
    private static final String BAR_ROUNDING_PRECISIOB_SHOULD_BE_NA_OR_POSITIVE_NUMBER = "BAR Rounding Precision should be 'N/A' or a positive number";
    private static final String AGILE_RATES_VALUE_SHOULD_BE_TRUE_FALSE_OR_ON_OFF = "Agile Rates value must be true/false or ON/OFF";
    private static final String UPLOAD_CHILD_AGE_BUCKET_SHOULD_BE_TRUE_FALSE_OR_ON_OFF = "Child Age Buckets value must be true/false or ON/OFF";
    private static final String LRV_VALUE_SHOULD_BE_NA_OR_BY_ROOM_CLASS = "LRV value should be 'N/A' or 'By RoomClass'";
    private static final String LRV_VALUE_SHOULD_BE_NA_OR_AT_ROOM_TYPE = "LRV value should be: N/A or 'At RoomType'";
    private static final String LRV_VALUE_SHOULD_BE_NA_OR_AT_ROOM_CLASS = "LRV value should be: N/A or 'At RoomClass'";
    private static final String USE_TAX_SHOULD_BE_AMOUNT_BEFORE_TAX_OR_AMOUNT_AFTER_TAX = "Use Tax should be 'Amount Before Tax' or 'Amount After Tax'";
    private static final String AMOUNT_AFTER_TAX = "Amount After Tax";
    private static final String AMOUNT_BEFORE_TAX = "Amount Before Tax";
    private static final String LRV_ROUNDING_SHOULD_BE_NA_OR_POSITIVE_INTEGER = "LRV Rounding should be 'N/A' or a positive number";
    private static final String DAILY_BAR_BY_ROOM_TYPE = "Daily BAR By Room Type";
    private static final String NOT_APPLICABLE = "N/A";
    private static final String WHEN_DAILY_BAR_UPLOAD_FORMAT_IS_VALID_THEN_DAILY_BAR_RATE_CODE_IS_MANDATORY = "When Daily BAR Upload format is 'Daily BAR By Room Type' then Daily BAR Rate Code is mandatory.";
    private static final String MISC_ADJUST_SHOULD_BE_NA_OR_A_POSITIVE_NUMBER = "Miscellaneous Adjustment should be 'N/A' or any positive number.";
    private static final String ROUNDING_LRV_SHOULD_BE_TRUE_FALSE = "Rounding LRV should be True/False.";
    private static final String DAILY_BAR_UPLOAD_FORMAT_SHOULD_BE_NA_OR_DAILY_BAR_BY_ROOM_TYPE = "Daily BAR Upload Format should be 'N/A' or 'Daily BAR By Room Type'";
    private static final String LRV_TAX_MUST_BE_BETWEEN_0_AND_100 = "LRV Tax % must be between 0 and 100.";
    private static final String OVERBOOKING_SHOULD_BE_TRUE_FALSE = "Overbooking should be True/False.";
    private static final String HOTEL_CLOSE_TO_ARRIVAL_SHOULD_BE_TRUE_FALSE = "Hotel Close to Arrival should be True/False.";
    private static final String PROFIT_ADJUSTMENT_SHOULD_BE_TRUE_FALSE = "Profit Adjustment should be True/False.";

    private static final List<String> LOS = new ArrayList<>(List.of(BY_RATE_CODE, BY_RATE_CATEGORY, BY_RATE_CATEGORY_BY_ROOM_TYPE, BY_RATE_CODE_BY_ROOM_TYPE));

    private static DecisionConfigValidationError validateBARRateCodeWhenUploadFormatIsValid(Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements, String externalSystem) {
        String barUpload = changedElements.get(OutboundElement.DAILY_BAR).getConfigElementValue();

        if (barUpload != null && equalsIgnoreCase(DAILY_BAR_BY_ROOM_TYPE, barUpload.trim())) {

            if (ExternalSystem.TARSHTNG.name().equalsIgnoreCase(externalSystem) ||
                    ExternalSystem.OCP.name().equalsIgnoreCase(externalSystem)) {
                String rateCode = changedElements.get(OutboundElement.DAILY_BAR_RATE_CODE).getConfigElementValue();
                if (rateCode != null && NOT_APPLICABLE.equalsIgnoreCase(rateCode.trim())) {
                    return new DecisionConfigValidationError(WHEN_DAILY_BAR_UPLOAD_FORMAT_IS_VALID_THEN_DAILY_BAR_RATE_CODE_IS_MANDATORY);
                }
            }
            if (ExternalSystem.OPERA.name().equalsIgnoreCase(externalSystem)) {
                String rateCode = changedElements.get(OutboundElement.DAILY_BAR_PMS_RATE_CODE).getConfigElementValue();
                if (rateCode != null && NOT_APPLICABLE.equalsIgnoreCase(rateCode.trim())) {
                    return new DecisionConfigValidationError(WHEN_DAILY_BAR_UPLOAD_FORMAT_IS_VALID_THEN_DAILY_BAR_RATE_CODE_IS_MANDATORY);
                }
            }
        }
        return null;
    }

    private static DecisionConfigValidationError validateLRVParamsIsNAWhenLRVIsNA(Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements, String externalSystem) {
        String lrv = changedElements.get(OutboundElement.LRV_AT_ROOM_CLASS).getConfigElementValue();
        String rounding = changedElements.get(OutboundElement.LRV_ROOM_CLASS_ROUNDING_PRECISION).getConfigElementValue();
        String tax = changedElements.get(OutboundElement.LRV_ROOM_CLASS_TAX).getConfigElementValue();
        if (NOT_APPLICABLE.equalsIgnoreCase(lrv)) {
            if (!NOT_APPLICABLE.equalsIgnoreCase(rounding)) {
                return new DecisionConfigValidationError("When LRV is 'N/A',then LRV Rounding Precision should be 'N/A'");
            }
            if(!ExternalSystem.OCP.name().equalsIgnoreCase(externalSystem)){
                if (!NOT_APPLICABLE.equalsIgnoreCase(tax)) {
                    return new DecisionConfigValidationError("When LRV is 'N/A',then LRV Tax should be 'N/A'");
                }
            }
        }
        return null;
    }

    private static DecisionConfigValidationError validateBARParamsWhenUploadFormatIsNA(Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements) {
        String dailyBAR = changedElements.get(OutboundElement.DAILY_BAR).getConfigElementValue();
        String rateCode = changedElements.get(OutboundElement.DAILY_BAR_PMS_RATE_CODE).getConfigElementValue();

        if (NOT_APPLICABLE.equalsIgnoreCase(dailyBAR) && !NOT_APPLICABLE.equalsIgnoreCase(rateCode)) {
            return new DecisionConfigValidationError("When Daily BAR Upload Format is 'N/A' then Daily BAR Rate Code should be 'N/A'");
        } else {
            return null;
        }
    }

    private static DecisionConfigValidationError validateBARRateCodeWhenUploadFormatIsNA(Map<DecisionConfigElement,ConfigParamValueWithReasonDto> changedElements) {
        String dailyBAR = changedElements.get(OutboundElement.DAILY_BAR).getConfigElementValue();
        String rateCode = changedElements.get(OutboundElement.DAILY_BAR_RATE_CODE).getConfigElementValue();

        if (NOT_APPLICABLE.equalsIgnoreCase(dailyBAR) && !NOT_APPLICABLE.equalsIgnoreCase(rateCode)) {
            return new DecisionConfigValidationError("When Daily BAR Upload Format is 'N/A' then Daily BAR Rate Code should be 'N/A'");
        } else {
            return null;
        }
    }
    private static DecisionConfigValidationError validateCeilingDeltaIsNAWhenLRVIsNA(Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements) {
        String lrv = changedElements.get(OutboundElement.LRV_BY_ROOM_CLASS).getConfigElementValue();
        String includeDelta = changedElements.get(OutboundElement.CEILING_DELTA_MAX_SOLD).getConfigElementValue();
        if (NOT_APPLICABLE.equalsIgnoreCase(lrv) && TRUE.equalsIgnoreCase(includeDelta)) {
            return new DecisionConfigValidationError("Ceiling Delta Max Sold should be False when LRV is 'N/A");
        }
        return null;
    }

    private static DecisionConfigValidationError validateBARParamsIsNAWhenUploadFormatIsNA(Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements) {
        ConfigParamValueWithReasonDto uploadValue = changedElements.get(OutboundElement.DAILY_BAR);
        if (uploadValue != null && NOT_APPLICABLE.equalsIgnoreCase(uploadValue.getConfigElementValue())) {
            String useTax = changedElements.get(OutboundElement.USE_TAX).getConfigElementValue();
            String tax = changedElements.get(OutboundElement.DAILY_BAR_TAX).getConfigElementValue();
            String rounding = changedElements.get(OutboundElement.DAILY_BAR_ROUNDING_PRECISION).getConfigElementValue();
            String rateCode = changedElements.get(OutboundElement.DAILY_BAR_RATE_CODE).getConfigElementValue();
            if (!AMOUNT_BEFORE_TAX.equalsIgnoreCase(useTax)) {
                return new DecisionConfigValidationError("When BAR Upload Format is 'N/A' then Use Tax should be 'Amount Before Tax'");
            }
            if (!NOT_APPLICABLE.equalsIgnoreCase(tax)) {
                return new DecisionConfigValidationError("When BAR Upload Format is 'N/A' then Daily BAR Tax should be 'N/A'");
            }
            if (!"2".equalsIgnoreCase(rounding)) {
                return new DecisionConfigValidationError("When BAR Upload Format is 'N/A' then BAR Rounding Precision should be 2");
            }
            if (!NOT_APPLICABLE.equalsIgnoreCase(rateCode)) {
                return new DecisionConfigValidationError("When BAR Upload Format is 'N/A' then Daily BAR Rate Code should be 'N/A'");
            }
        }
        return null;
    }

    private static DecisionConfigValidationError validateThatLRVTaxIsNotApplicableWhenLRVIsNotApplicable(
            Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements) {
        ConfigParamValueWithReasonDto taxValue = changedElements.get(OutboundElement.LRV_ROOM_TYPE_TAX);
        ConfigParamValueWithReasonDto lrvValue = changedElements.get(OutboundElement.LRV_AT_ROOM_TYPE);
        if (lrvValue != null && NOT_APPLICABLE.equalsIgnoreCase(lrvValue.getConfigElementValue())) {
            if (taxValue != null && !NOT_APPLICABLE.equalsIgnoreCase(taxValue.getConfigElementValue()) ) {
                return new DecisionConfigValidationError("When LRV is 'N/A' then LRV Tax should be N/A.");
            }
        }
        return null;
    }
    private static DecisionConfigValidationError validateThatLRVTaxWhenLRVIsNotApplicable(Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements) {
        ConfigParamValueWithReasonDto lrv = changedElements.get(OutboundElement.LRV_AT_ROOM_CLASS);
        ConfigParamValueWithReasonDto lrvTax = changedElements.get(OutboundElement.LRV_ROOM_CLASS_TAX);
        if (lrv != null && NOT_APPLICABLE.equalsIgnoreCase(lrv.getConfigElementValue())) {
            if (lrvTax != null && !NOT_APPLICABLE.equalsIgnoreCase(lrvTax.getConfigElementValue())) {
                return new DecisionConfigValidationError("When LRV is 'N/A' then LRV Tax should be N/A.");
            }
        }
        return null;
    }
    private static DecisionConfigValidationError validateAndChangeElementsForDailyBAR(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements,
                                                                                      String dailyBarByRoomType, OutboundElement element) {
        if (equalsIgnoreCase(value, dailyBarByRoomType)) {
            resultElements.put(element, new ConfigParamValueWithReasonDto(DIFFERENTIAL));
            return null;
        }
        if ("NONE".equalsIgnoreCase(value) || NOT_APPLICABLE.equalsIgnoreCase(value)) {
            resultElements.put(element, new ConfigParamValueWithReasonDto(NONE));
            return null;
        } else {
            return new DecisionConfigValidationError("Upload format should be 'N/A' or " + dailyBarByRoomType);
        }
    }

    private static DecisionConfigValidationError validateAndAddRateCode(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                        Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, OutboundElement element) {
        String dailyBAR = changedElements.get(OutboundElement.DAILY_BAR).getConfigElementValue();
        if (isNotEmpty(value) && !NOT_APPLICABLE.equalsIgnoreCase(value) && !NOT_APPLICABLE.equalsIgnoreCase(dailyBAR)) {
            resultElements.put(element, new ConfigParamValueWithReasonDto(value.trim()));
            return null;
        } else if (NOT_APPLICABLE.equalsIgnoreCase(dailyBAR) && NOT_APPLICABLE.equalsIgnoreCase(value)) {
            resultElements.put(element, new ConfigParamValueWithReasonDto(""));
            return null;
        } else if(isEmpty(value)) {
            return new DecisionConfigValidationError("Daily BAR Rate Code is mandatory.");
        }else {
            return null;
        }
    }

    private static DecisionConfigValidationError validateAndAddLRVTax(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                      Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, OutboundElement element, String externalSystem) {
        if (isEmpty(value)) {
            return new DecisionConfigValidationError("LRV Tax % must be between 0 and 100 or N/A.");
        }
        if(ExternalSystem.OCP.name().equalsIgnoreCase(externalSystem)){
            String lrv = changedElements.get(OutboundElement.LRV_AT_ROOM_CLASS).getConfigElementValue();
            if(NOT_APPLICABLE.equalsIgnoreCase(lrv) && NOT_APPLICABLE.equalsIgnoreCase(value)) {
                resultElements.put(element, new ConfigParamValueWithReasonDto(""));
                return null;
            }
        }
        if (NOT_APPLICABLE.equalsIgnoreCase(value)) {
            resultElements.put(element, new ConfigParamValueWithReasonDto(""));
            return null;
        }
        try {
            String val = value.trim();
            if (NumberUtils.isNumber(val)) {
                double tax = Double.parseDouble(val);
                if (tax >= 0 && tax <= 100) {
                    resultElements.put(element, new ConfigParamValueWithReasonDto(val));
                    return null;
                } else {
                    return new DecisionConfigValidationError(LRV_TAX_MUST_BE_BETWEEN_0_AND_100);
                }
            }
        } catch (Exception e) {
            return new DecisionConfigValidationError(LRV_TAX_MUST_BE_BETWEEN_0_AND_100);
        }
        return new DecisionConfigValidationError(LRV_TAX_MUST_BE_BETWEEN_0_AND_100);
    }

    private static DecisionConfigValidationError validateAndAddDailyBAR(String value,
                                                                        Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, OutboundElement element) {
        if (isEmpty(value)) {
            return new DecisionConfigValidationError(DAILY_BAR_UPLOAD_FORMAT_SHOULD_BE_NA_OR_DAILY_BAR_BY_ROOM_TYPE);
        }
        String val = value.trim();
        if (equalsIgnoreCase(DAILY_BAR_BY_ROOM_TYPE, val)) {
            resultElements.put(element, new ConfigParamValueWithReasonDto(DIFFERENTIAL));
            return null;
        } else if (equalsIgnoreCase("none", val) || "OFF".equalsIgnoreCase(val) || NOT_APPLICABLE.equalsIgnoreCase(val)) {
            resultElements.put(element, new ConfigParamValueWithReasonDto(NONE));
            return null;
        } else {
            return new DecisionConfigValidationError(DAILY_BAR_UPLOAD_FORMAT_SHOULD_BE_NA_OR_DAILY_BAR_BY_ROOM_TYPE);
        }
    }

    private static DecisionConfigValidationError validateAndAddRoundingRule(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements,
                                                                            OutboundElement element) {
        if (isEmpty(value)) {
            return new DecisionConfigValidationError(ROUNDING_LRV_SHOULD_BE_TRUE_FALSE);
        }
        String val = value.trim();
        if (TRUE.equalsIgnoreCase(val)) {
            resultElements.put(element, new ConfigParamValueWithReasonDto(TRUE));
            return null;
        }
        if (FALSE.equalsIgnoreCase(val)) {
            resultElements.put(element, new ConfigParamValueWithReasonDto(FALSE));
            return null;
        } else {
            return new DecisionConfigValidationError(ROUNDING_LRV_SHOULD_BE_TRUE_FALSE);
        }
    }

    private static DecisionConfigValidationError validateAndAddMiscellaneousAdjustment(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> changedElements,
                                                                                       Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements, OutboundElement element) {
        if (isEmpty(value)) {
            return new DecisionConfigValidationError(MISC_ADJUST_SHOULD_BE_NA_OR_A_POSITIVE_NUMBER);
        }
        String val = value.trim();
        String barUpload = changedElements.get(OutboundElement.DAILY_BAR).getConfigElementValue();
        if (barUpload !=null && NOT_APPLICABLE.equalsIgnoreCase(barUpload.trim()) && NOT_APPLICABLE.equalsIgnoreCase(val)) {
            resultElements.put(element, new ConfigParamValueWithReasonDto(""));
            return null;
        }
        if(!NOT_APPLICABLE.equalsIgnoreCase(barUpload) && NOT_APPLICABLE.equalsIgnoreCase(val)) {
            resultElements.put(element, new ConfigParamValueWithReasonDto(""));
            return null;
        }
        if (NumberUtils.isNumber(val)) {
            try {
                int adjustment = Integer.parseInt(val);
                if (adjustment >= 0) {
                    resultElements.put(element, new ConfigParamValueWithReasonDto(val));
                    return null;
                }
            } catch (Exception e) {
                return new DecisionConfigValidationError(MISC_ADJUST_SHOULD_BE_NA_OR_A_POSITIVE_NUMBER);
            }
        }
        return new DecisionConfigValidationError(MISC_ADJUST_SHOULD_BE_NA_OR_A_POSITIVE_NUMBER);
    }

    static DecisionConfigValidationError validateAndAddOverbooking(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements,
                                                                   OutboundElement element) {
        if (isEmpty(value)) {
            return new DecisionConfigValidationError(OVERBOOKING_SHOULD_BE_TRUE_FALSE);
        }
        String val = value.trim();
        if (TRUE.equalsIgnoreCase(val) || "ON".equalsIgnoreCase(val) || YES.equalsIgnoreCase(val)) {
            resultElements.put(element, new ConfigParamValueWithReasonDto(DIFFERENTIAL));
            return null;
        }
        if (FALSE.equalsIgnoreCase(val) || "OFF".equalsIgnoreCase(val) || NO.equalsIgnoreCase(val)) {
            resultElements.put(element, new ConfigParamValueWithReasonDto(NONE));
            return null;
        }
        return new DecisionConfigValidationError(OVERBOOKING_SHOULD_BE_TRUE_FALSE);
    }

    static DecisionConfigValidationError validateRichToggle(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements,
                                                                   OutboundElement element, String validationMsg) {
        if (isEmpty(value)) {
            return new DecisionConfigValidationError(validationMsg);
        }
        String val = value.trim();
        if (TRUE.equalsIgnoreCase(val) || "ON".equalsIgnoreCase(val) || YES.equalsIgnoreCase(val)) {
            resultElements.put(element, new ConfigParamValueWithReasonDto(DIFFERENTIAL));
            return null;
        }
        if (FALSE.equalsIgnoreCase(val) || "OFF".equalsIgnoreCase(val) || NO.equalsIgnoreCase(val)) {
            resultElements.put(element, new ConfigParamValueWithReasonDto(NONE));
            return null;
        }
        return new DecisionConfigValidationError(validationMsg);
    }

    private static DecisionConfigValidationError validateAndAddIncludeDeltas(String value, Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements,
                                                                             OutboundElement element) {
        if (isEmpty(value)) {
            return new DecisionConfigValidationError("Include Deltas should be true/false");
        }
        String val = value.trim();
        if (TRUE.equalsIgnoreCase(val)) {
            resultElements.put(element, new ConfigParamValueWithReasonDto(TRUE));
            return null;
        }
        if (FALSE.equalsIgnoreCase(val)) {
            resultElements.put(element, new ConfigParamValueWithReasonDto(FALSE));
            return null;
        } else {
            return new DecisionConfigValidationError("Include Deltas should be true/false");
        }
    }

    static DecisionConfigValidationError validateAndAddLOS(String value,
                                                           Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements,
                                                           DecisionConfigElement element, List<DecisionConfigElement> elementsToUpdate) {
        if (isEmpty(value)) {
            return new DecisionConfigValidationError("LOS should be NONE or " + LOS);
        }
        String val = value.trim();
        if (LOS.contains(val)) {
            resultElements.put(element, new ConfigParamValueWithReasonDto(DIFFERENTIAL));
            elementsToUpdate.forEach(e -> resultElements.put(e, new ConfigParamValueWithReasonDto(NONE)));
            return null;
        }
        if (NONE.equalsIgnoreCase(val) || "OFF".equalsIgnoreCase(val) || NOT_APPLICABLE.equalsIgnoreCase(val)) {
            resultElements.put(element, new ConfigParamValueWithReasonDto(NONE));
            return null;
        }
        return new DecisionConfigValidationError("LOS should be NONE or " + LOS);
    }


    private static boolean isTaxValid(ConfigParamValueWithReasonDto configParamValue, boolean isAmountBeforeTax) {
        try {
            if (configParamValue == null) {
                return false;
            }

            String value = configParamValue.getConfigElementValue();

            if (isAmountBeforeTax) {
                return StringUtils.isEmpty(value) || NOT_APPLICABLE.equalsIgnoreCase(value);
            } else {
                return Double.parseDouble(value) == 0;
            }
        } catch (Exception e) {
            return false;
        }
    }

    private static void updateChangedElementsForConsolidateDelta(Map<DecisionConfigElement, ConfigParamValueWithReasonDto> resultElements) {
        resultElements.put(LRV_ROOM_TYPE_CONSOLIDATE_CEILING_DELTA, new ConfigParamValueWithReasonDto(CONSOLIDATE_DELTA_NONE_VALUE));
        resultElements.put(LRV_ROOM_CLASS_CONSOLIDATE_CEILING_DELTA, new ConfigParamValueWithReasonDto(CONSOLIDATE_DELTA_NONE_VALUE));
    }
}
