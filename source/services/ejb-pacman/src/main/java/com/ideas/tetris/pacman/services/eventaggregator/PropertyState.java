package com.ideas.tetris.pacman.services.eventaggregator;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;


@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PropertyState {
    private PropertyRegulatorStatus regulatorStatus = PropertyRegulatorStatus.UNLOCKED;
    private boolean readOnly;
    private boolean editableDuringIDP;
    private List<StalenessFlag> staleness;
    //This state gets triggered when a CARE user clicks on the Lock icon in the Monitoring Dashboard for a failed job.
    private boolean readOnlyOverride;
    private boolean decisionUploadInProgress;
    private boolean forceSyncInProgress;
    private boolean showReadOnlyNotification;
    private boolean jobProcessingInProgress;


    public boolean isJobProcessingInProgress() {
        return jobProcessingInProgress;
    }

    public void setJobProcessingInProgress(boolean jobProcessingInProgress) {
        this.jobProcessingInProgress = jobProcessingInProgress;
    }

    public boolean isEditableDuringIDP() {
        return editableDuringIDP;
    }

    public void setEditableDuringIDP(boolean editableDuringIDP) {
        this.editableDuringIDP = editableDuringIDP;
    }

    public boolean isReadOnly() {
        return readOnly;
    }

    public void setReadOnly(boolean readOnly) {
        this.readOnly = readOnly;
    }

    public List<StalenessFlag> getStaleness() {
        return staleness;
    }

    public void setStaleness(List<StalenessFlag> staleness) {
        this.staleness = staleness;
    }

    public void addStaleness(StalenessFlag stalenessFlag) {
        if (staleness == null) {
            staleness = new ArrayList<StalenessFlag>();
        }

        staleness.add(stalenessFlag);
    }

    public PropertyRegulatorStatus getRegulatorStatus() {
        return regulatorStatus;
    }

    public void setRegulatorStatus(PropertyRegulatorStatus regulatorStatus) {
        this.regulatorStatus = regulatorStatus;
    }

    public boolean isReadOnlyOverride() {
        return readOnlyOverride;
    }

    public void setReadOnlyOverride(boolean readOnlyOverride) {
        this.readOnlyOverride = readOnlyOverride;
    }

    public boolean isDecisionUploadInProgress() {
        return decisionUploadInProgress;
    }

    public void setDecisionUploadInProgress(boolean decisionUploadInProgress) {
        this.decisionUploadInProgress = decisionUploadInProgress;
    }

    public boolean isForceSyncInProgress() {
        return forceSyncInProgress;
    }

    public void setForceSyncInProgress(boolean forceSyncInProgress) {
        this.forceSyncInProgress = forceSyncInProgress;
    }

    public void setShowReadOnlyNotification(boolean showReadOnlyNotification) {
        this.showReadOnlyNotification = showReadOnlyNotification;
    }

    public boolean isShowReadOnlyNotification() {
        return showReadOnlyNotification;
    }
}
