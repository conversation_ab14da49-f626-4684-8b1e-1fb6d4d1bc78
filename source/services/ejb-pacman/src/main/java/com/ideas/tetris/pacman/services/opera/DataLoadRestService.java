package com.ideas.tetris.pacman.services.opera;

import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.dataload.DataLoadService;
import com.ideas.tetris.pacman.services.marketsegment.entity.ProcessStatus;
import com.ideas.tetris.pacman.services.remoteAgent.AbstractAgentRestService;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.event.TetrisEvent;
import com.ideas.tetris.platform.services.jmsclient.TetrisEventManager;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.jboss.resteasy.plugins.providers.multipart.InputPart;
import org.jboss.resteasy.plugins.providers.multipart.MultipartFormDataInput;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.RecordType.T2SNAP_RECORD_TYPE_ID;

@Component
@Transactional
public class DataLoadRestService extends AbstractAgentRestService {
    private static final Logger LOGGER = Logger.getLogger(DataLoadRestService.class.getName());
    protected static final String UPLOADED_FILE_PARAM = "uploadedFile";
    private static final String PROCESS_STATUS = "processStatus";
    private static final String RECORD_TYPE_ID = "recordTypeId";
    private static final String PROPERTY_ID = "propertyId";

    @Autowired
    DataLoadService dataLoadService;
    @Autowired
    ProcessDecisionStatus processDecisionStatus;
    @Autowired
    TetrisEventManager tetrisEventManager;
    @Autowired
    AbstractMultiPropertyCrudService multiPropertyCrudService;


    public Response rebuildDatabase() {
        dataLoadService.rebuildDatabase();
        return Response.ok("Rebuild process has been successfully started.").build();
    }


    public Response rollBackProperty() {
        dataLoadService.rollBackProperty();
        return Response.ok("Roll back property process has been successfully started.").build();
    }


    public Response catchUp() {
        dataLoadService.catchUp();
        return Response.ok("Catch Up process has been successfully started.").build();
    }


    public String processDecisionStatusFile(MultipartFormDataInput multipartFormDataInput,
                                            String propertyIdString, String correlationId,
                                            String externalSystem,
                                            final HttpServletRequest request) {
        closeSession(request);
        InputStream inputStream = null;
        BufferedReader bufferedReader = null;
        return fetchAndUpdateProcessDecisionStatus(multipartFormDataInput,
                propertyIdString, correlationId, inputStream, bufferedReader,
                externalSystem);
    }


    public String processDecisionStatusFile(MultipartFile[] multipartFiles,
                                            String propertyIdString, String correlationId,
                                            String externalSystem,
                                            final HttpServletRequest request) {
        closeSession(request);
        return fetchAndUpdateProcessDecisionStatus(multipartFiles,
                propertyIdString, correlationId, externalSystem, request);
    }

    private String fetchAndUpdateProcessDecisionStatus(MultipartFormDataInput multipartFormDataInput,
                                                       String propertyIdString, String correlationId, InputStream inputStream, BufferedReader bufferedReader,
                                                       String externalSystem) {
        try {
            boolean processDecisionStatusRecords = false;
            Map<String, List<InputPart>> uploadForm = multipartFormDataInput.getFormDataMap();
            List<InputPart> inputParts = uploadForm.get(UPLOADED_FILE_PARAM);
            for (InputPart inputPart : inputParts) {

                inputStream = inputPart.getBody(InputStream.class, null);
                bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
                processDecisionStatusRecords = processDecisionStatus.processDecisionStatusRecords(bufferedReader, externalSystem);
            }
            try {
                if (processDecisionStatusRecords) {
                    updateBookkeeping(Integer.parseInt(propertyIdString), correlationId);
                }
            } catch (NumberFormatException e) {
                // if the propertyId is invalid, skip creating the bookkeeping event
            }
            return "Successfully processed file";
        } catch (IOException e) {
            LOGGER.error("IOException occured while processing Decision Status File  "
                    + "ERROR message is " + e);
            String errorMessage = "Could not process file ";
            return errorMessage + ". Received error: " + e.getMessage();
        } catch (TetrisException te) {
            LOGGER.error("TetrisException occured while processing Decision Status File ERROR message is " + te);
            return "Could not process file. Received error: " + te.getMessage();
        } finally {
            IOUtils.closeQuietly(inputStream);
            IOUtils.closeQuietly(bufferedReader);
        }
    }

    private String fetchAndUpdateProcessDecisionStatus(MultipartFile[] inputFiles,
                                                       String propertyIdString, String correlationId, String externalSystem, HttpServletRequest request) {

        String uploadedPart = request.getParameter(UPLOADED_FILE_PARAM);
        boolean processDecisionStatusRecords = false;

        if (Objects.isNull(inputFiles)) {
            try (BufferedReader bufferedReader = new BufferedReader(new StringReader(uploadedPart))) {
                processDecisionStatusRecords = processDecisionStatus.processDecisionStatusRecords(bufferedReader, externalSystem);
            } catch (IOException e) {
                LOGGER.error("IOException occurred while processing Decision Status File. ERROR message is " + e);
                return "Could not process file. Received error: " + e.getMessage();
            } catch (TetrisException te) {
                LOGGER.error("TetrisException occurred while processing Decision Status File. ERROR message is " + te);
                return "Could not process file. Received error: " + te.getMessage();
            }
        } else {
            for (MultipartFile multipartFile : inputFiles) {
                try (InputStream is = multipartFile.getInputStream();
                     BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(is))) {
                    processDecisionStatusRecords = processDecisionStatus.processDecisionStatusRecords(bufferedReader, externalSystem);
                } catch (IOException e) {
                    LOGGER.error("IOException occurred while processing Decision Status File  "
                            + "ERROR message is " + e);
                    String errorMessage = "Could not process file ";
                    return errorMessage + ". Received error: " + e.getMessage();
                } catch (TetrisException te) {
                    LOGGER.error("TetrisException occured while processing Decision Status File ERROR message is " + te);
                    return "Could not process file. Received error: " + te.getMessage();
                }
            }
        }

        try {
            if (processDecisionStatusRecords) {
                updateBookkeeping(Integer.parseInt(propertyIdString), correlationId);
            }
        } catch (NumberFormatException e) {
            // if the propertyId is invalid, skip creating the bookkeeping event
        }
        return "Successfully processed file";
    }

    public void updateBookkeeping(Integer propertyId, String decisionIdentifier) {
        FileMetadata fileMetadata = getMostRecentFileMetadata(propertyId);

        if (fileMetadata != null) { // skip bookkeeping if input correlation id cannot be determined
            String inputIdentifier = fileMetadata.getFileLocation();

            if (fileMetadata.getBde() == 1) {
                TetrisEvent te = tetrisEventManager.createBDEDecisionsUploadedEvent(propertyId, decisionIdentifier, inputIdentifier);
                tetrisEventManager.raiseEvent(te);
            } else {
                TetrisEvent te = tetrisEventManager.createCDPDecisionsUploadedEvent(propertyId, decisionIdentifier, inputIdentifier);
                tetrisEventManager.raiseEvent(te);
            }
        }
    }

    private FileMetadata getMostRecentFileMetadata(Integer propertyId) {
        return (FileMetadata) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId,
                FileMetadata.BY_RECORD_TYPE_AND_PROPERTY_AND_STATUS_ORDER_BY_SNAPSHOTDT_DESC,
                QueryParameter.with(PROPERTY_ID, Arrays.asList(propertyId))
                        .and(RECORD_TYPE_ID, T2SNAP_RECORD_TYPE_ID)
                        .and(PROCESS_STATUS, ProcessStatus.SUCCESSFUL).parameters());
    }


/*  Everything below here is Tech Debt.  It is not needed for Opera Agent 2015, however it is needed for the
G3OperaExtractFilesUploaded utility that is used to transfer data from the opera.History... tables of a
Production property to the opera.History... tables of a lower environment (via files) for testing.
The methods have been marked as deprecated, and should be eliminated as part of creating a better solution for
copying opera data from one environment to another.

 */


    @Deprecated
    public String uploadFile(MultipartFormDataInput multipartFormDataInput, String correlationId,
                             String fileType) {
        return uploadFileData(multipartFormDataInput, correlationId, fileType, true);
    }

    public String uploadFile(MultipartFile[] multipartFiles, String correlationId,
                             String fileType) {
        return uploadFileData(multipartFiles, correlationId, fileType, true);
    }


    public String uploadFile(MultipartFile[] multipartFiles, String correlationId,
                             String fileType, final HttpServletRequest request) {
        return uploadFileData(multipartFiles, correlationId, fileType, request);
    }

    private String uploadFileData(MultipartFile[] multipartFiles, String correlationId, String fileType, HttpServletRequest request) {
        OperaIncomingFileType operaFileType = null;
        String uploadedPart = request.getParameter(UPLOADED_FILE_PARAM);

        if (Objects.isNull(multipartFiles)) {
            try (BufferedReader bufferedReader = new BufferedReader(new StringReader(uploadedPart))) {
                if (!StringUtils.isBlank(fileType)) {
                    operaFileType = OperaIncomingFile.getByFileTypeCode(fileType);
                    dataLoadService.loadFile(bufferedReader, operaFileType, correlationId, true);
                }

                return new StringBuilder().append(correlationId).append(": Successfully loaded file [").
                        append(operaFileType).append("].").toString();
            } catch (IOException e) {
                String errorMessage = correlationId + ": Could not load file [" + operaFileType + "].";
                LOGGER.warn(errorMessage, e);
                return errorMessage + ". Received error: " + e.getMessage();
            }

        } else {
            return uploadFileData(multipartFiles, correlationId, fileType, true);
        }

    }

    @Deprecated
    private String uploadFileData(MultipartFormDataInput multipartFormDataInput, String correlationId, String fileType, boolean isChunked) {
        OperaIncomingFileType operaFileType = null;
        InputStream inputStream = null;
        BufferedReader reader = null;
        try {
            Map<String, List<InputPart>> uploadForm = multipartFormDataInput.getFormDataMap();
            List<InputPart> inputParts = uploadForm.get(UPLOADED_FILE_PARAM);

            for (InputPart inputPart : inputParts) {
                if (!StringUtils.isBlank(fileType)) {
                    operaFileType = OperaIncomingFile.getByFileTypeCode(fileType);
                }

                if (operaFileType == null) {
                    operaFileType = getOperaFileTypeFromInputPart(inputPart);
                }

                inputStream = inputPart.getBody(InputStream.class, null);
                reader = new BufferedReader(new InputStreamReader(inputStream));
                dataLoadService.loadFile(reader, operaFileType, correlationId, isChunked);
            }
            return correlationId + ": Successfully loaded file [" + operaFileType + "].";
        } catch (Exception e) {
            String errorMessage = correlationId + ": Could not load file [" + operaFileType + "].";
            LOGGER.warn(errorMessage, e);
            return errorMessage + ". Received error: " + e.getMessage();
        } finally {
            if (null != inputStream) {
                IOUtils.closeQuietly(inputStream);
            }
            if (null != reader) {
                IOUtils.closeQuietly(reader);
            }
        }
    }

    private String uploadFileData(MultipartFile[] inputFiles, String correlationId, String fileType, boolean isChunked) {

        OperaIncomingFileType operaFileType = null;
        BufferedReader reader = null;
        try {
            for (MultipartFile multipartFile : inputFiles) {
                if (!StringUtils.isBlank(fileType)) {
                    operaFileType = OperaIncomingFile.getByFileTypeCode(fileType);
                }

                if (operaFileType == null) {
                    operaFileType = getOperaFileTypeFromInputPart(multipartFile.getOriginalFilename());
                }
                reader = new BufferedReader(new InputStreamReader(multipartFile.getInputStream()));
                dataLoadService.loadFile(reader, operaFileType, correlationId, isChunked);
            }
            return correlationId + ": Successfully loaded file [" + operaFileType + "].";
        } catch (Exception e) {
            String errorMessage = correlationId + ": Could not load file [" + operaFileType + "].";
            LOGGER.warn(errorMessage, e);
            return errorMessage + ". Received error: " + e.getMessage();
        } finally {
            if (null != reader) {
                IOUtils.closeQuietly(reader);
            }
        }

    }

    @Deprecated
    private OperaIncomingFileType getOperaFileTypeFromInputPart(InputPart inputPart) {
        MultivaluedMap<String, String> header = inputPart.getHeaders();
        return getOperaFileTypeFromFilename(getFileName(header));
    }

    private OperaIncomingFileType getOperaFileTypeFromInputPart(String fileName) {
        return getOperaFileTypeFromFilename(fileName);
    }


    @Deprecated
    private OperaIncomingFileType getOperaFileTypeFromFilename(String inputFile) {
        for (OperaIncomingFileType operaFileType : OperaIncomingFile.values()) {
            if (inputFile.toUpperCase().contains("_" + operaFileType.getFileTypeCode().toUpperCase() + "_")) {
                return operaFileType;
            }
        }

        return null;
    }

    @Deprecated
    private String getFileName(MultivaluedMap<String, String> header) {
        String[] contentDisposition = header.getFirst("Content-Disposition").split(";");

        for (String filename : contentDisposition) {
            if ((filename.trim().startsWith("filename"))) {
                String[] name = filename.split("=");
                return name[1].trim().replaceAll("\"", "");
            }
        }
        return "unknown";
    }


}
