package com.ideas.tetris.pacman.services.sasoptimization.service;

import com.google.common.annotations.VisibleForTesting;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.common.xml.schema.optimization.request.v1.BarWhatIfRequestElement;
import com.ideas.tetris.pacman.common.xml.schema.optimization.request.v1.OptimizationRequestType;
import com.ideas.tetris.pacman.common.xml.schema.optimization.request.v1.SASRequest;
import com.ideas.tetris.pacman.common.xml.schema.optimization.request.v1.WhatIfRequest;
import com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.AccomClassData;
import com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.AccomTypeData;
import com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.BarOutputData;
import com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.DateData;
import com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.PropertyLevelData;
import com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.SASResponse;
import com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.SasWhatIfResponse;
import com.ideas.tetris.pacman.common.xml.schema.optimization.response.v1.WhatifImpactType;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.BarDecisionService;
import com.ideas.tetris.pacman.services.bestavailablerate.CPDecisionContext;
import com.ideas.tetris.pacman.services.bestavailablerate.CPManagementService;
import com.ideas.tetris.pacman.services.bestavailablerate.MasterClassOverrideHelperBean;
import com.ideas.tetris.pacman.services.bestavailablerate.OverrideService;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BARDecisionInfo;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BAROverride;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomTypeSupplementValue;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionBAROutput;
import com.ideas.tetris.pacman.services.componentrooms.services.ComponentRoomService;
import com.ideas.tetris.pacman.services.contextholder.BusinessContext;
import com.ideas.tetris.pacman.services.contextholder.BusinessContextService;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.dto.DateRange;
import com.ideas.tetris.pacman.services.decisiondelivery.LastRoomValueDecisionService;
import com.ideas.tetris.pacman.services.demandoverride.dto.ArrivalDemandOverride;
import com.ideas.tetris.pacman.services.demandoverride.dto.DemandOverride;
import com.ideas.tetris.pacman.services.demandoverride.dto.DemandWashOverrideRequest;
import com.ideas.tetris.pacman.services.demandoverride.dto.OccupancyDemandOverride;
import com.ideas.tetris.pacman.services.demandoverride.dto.WashOverride;
import com.ideas.tetris.pacman.services.demandoverride.entity.LastRoomValueAccomType;
import com.ideas.tetris.pacman.services.job.JobMonitorService;
import com.ideas.tetris.pacman.services.job.entity.JobView;
import com.ideas.tetris.pacman.services.overbooking.dto.OverbookingSaveOverrideDetails;
import com.ideas.tetris.pacman.services.pricing.ProductManagementService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.Supplement;
import com.ideas.tetris.pacman.services.problem.ProblemService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.restfortesting.ForTesting;
import com.ideas.tetris.pacman.services.sasoptimization.dto.SimplifiedCurrentValues;
import com.ideas.tetris.pacman.services.sasoptimization.dto.SimplifiedWhatIfAccomClassData;
import com.ideas.tetris.pacman.services.sasoptimization.dto.SimplifiedWhatIfAccomTypeData;
import com.ideas.tetris.pacman.services.sasoptimization.dto.SimplifiedWhatIfDateData;
import com.ideas.tetris.pacman.services.sasoptimization.dto.SimplifiedWhatIfImpactType;
import com.ideas.tetris.pacman.services.sasoptimization.dto.SimplifiedWhatIfResult;
import com.ideas.tetris.pacman.services.sasoptimization.dto.WhatIfItem;
import com.ideas.tetris.pacman.services.sasoptimization.dto.WhatIfRequestItem;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.services.useractivity.ActivityType;
import com.ideas.tetris.pacman.services.useractivity.UserActivityService;
import com.ideas.tetris.pacman.services.useractivity.entity.UserActivity;
import com.ideas.tetris.pacman.services.useractivity.entity.UserActivityPage;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateShoppingConfig;
import com.ideas.tetris.pacman.services.webrate.service.WebrateDataSchedulingService;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.pacman.util.RssStalenessEntriesUtil;
import com.ideas.tetris.pacman.util.jaxb.optimization.response.SimplifiedOptimizationResponseJAXBUtil;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.errorhandling.ErrorCode;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.util.jaxb.JAXBUtilLocal;
import com.ideas.tetris.platform.common.util.jdbc.JpaJdbcUtil;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.regulator.service.RegulatorService;
import com.ideas.tetris.platform.services.regulator.service.spring.RegulatorSpringService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Inject;
import javax.xml.datatype.XMLGregorianCalendar;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.BARDECISIONOVERRIDE_NONE;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Qualifier;

@Component
@Transactional
public class SimplifiedWhatIfService extends AbstractOptimizationService {
    private static final Logger LOGGER = Logger.getLogger(SimplifiedWhatIfService.class.getName());
    private static final int DECISION_ID = -1;
    private static final String OPERATION_TYPE_WHAT_IF = "WHAT_IF";
    private static final String OPERATION_TYPE_WHAT_IF_CDP = "CDP_WHAT_IF";
    private static final String PROPERTY_ID = "propertyId";
    private static final int REQUEST_SHOULDER_DAYS = 14;
    private static final String WHAT_IF_FAILED = "What If Failed";


    public static final int RESPONSE_NET_VARIANCE_DAYS = 7;
    public static final String WHAT_IF_SAS_INVOCATION_STEP = "WhatIfSasInvocationStep";
    public static final int PERF_LOG_LEVEL = 2;

    @Autowired
	protected BusinessContextService businessContextService;
    @Autowired
	protected MasterClassOverrideHelperBean masterClassHelper;
    @Autowired
	protected WhatIfItemFactory whatIfItemFactory;
    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;
    @SimplifiedOptimizationResponseJAXBUtil.SimplifiedOptimizationResponseQualifier
	@Qualifier("simplifiedOptimizationResponseJAXBUtil")
    @Autowired
	protected JAXBUtilLocal responseJaxbUtil;
    @Autowired
	protected RegulatorService regulatorService;
    @Autowired
    protected RegulatorSpringService regulatorSpringService;
    @Autowired
	protected UserActivityService userActivityService;
    @GlobalCrudServiceBean.Qualifier
	@Qualifier("globalCrudServiceBean")
    @Autowired
	protected CrudService globalCrudService;
    @TenantCrudServiceBean.Qualifier
    @Qualifier("tenantCrudServiceBean")
    @Autowired
	protected CrudService crudService;
    @Autowired
    JobMonitorService jobMonitorService;
    @Autowired
    JobServiceLocal jobService;
    @Autowired
	protected CPManagementService cpManagementService;
    @Autowired
	protected LastRoomValueDecisionService lastRoomValueDecisionService;
    @Autowired
	protected SimplifiedCurrentValuesService simplifiedCurrentValuesService;
    @Autowired
	protected BarDecisionService barService;
    @Autowired
	protected PricingConfigurationService pricingConfigurationService;
    @Autowired
	protected JpaJdbcUtil jpaJdbcUtil;
    @Autowired
	protected OverrideService barOverrideService;
    @Autowired
    ComponentRoomService componentRoomService;
    @Autowired
	protected WebrateDataSchedulingService webrateDataSchedulingService;
    @Autowired
    protected ProblemService problemService;
    @Autowired
	protected ProductManagementService productManagementService;
    @Autowired
    PropertyService propertyService;


    public SimplifiedWhatIfResult analyzeChanges(List<BAROverride> overrides) {
        if (overrides == null || overrides.isEmpty()) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "No overrides were passed to SimplifiedWhatIfService.analyzeChanges()");
        }
        LOGGER.info("SimplifiedWhatIfService.analyzeChanges for BAR: starting. . . .");
        BusinessContext businessContext = businessContextService.getCurrentBusinessContext();
        List<BAROverride> expandedOverrides = !isContinuousPricing() ? masterClassHelper.propagate(overrides, businessContext) : overrides;
        List<WhatIfItem> whatIfItems = whatIfItemFactory.convertToWhatIfItems(expandedOverrides);

        validateBAROverride(whatIfItems);

        return analyzeChanges(createOptimizationRequest(whatIfItems, businessContext), overrides);
    }

    public SimplifiedWhatIfResult analyzeChanges(List<WashOverride> washOverrides, List<DemandOverride> demandOverrides) {
        if ((washOverrides == null || washOverrides.isEmpty()) && (demandOverrides == null || demandOverrides.isEmpty())) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "No overrides were passed to SimplifiedWhatIfService.analyzeChanges()");
        }
        LOGGER.info("SimplifiedWhatIfService.executeWhatIf for Demand: starting. . . .");
        BusinessContext businessContext = businessContextService.getCurrentBusinessContext();
        List<WhatIfItem> whatIfItems = whatIfItemFactory.convertToWhatIfItems(demandOverrides, washOverrides);
        if (whatIfItems.size() > SystemConfig.getWhatIfItemsSizeLimit()) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "Multiplier Limit Exceeded");
        }
        return analyzeChanges(createOptimizationRequest(whatIfItems, businessContext), null);
    }

    public SimplifiedWhatIfResult analyzeChanges(OverbookingSaveOverrideDetails overrides) {
        if (overrides == null || overrides.isEmpty()) {
            throw new TetrisException(ErrorCode.INVALID_INPUT, "No overrides were passed to SimplifiedWhatIfService.executeWhatIf()");
        }
        LOGGER.info("SimplifiedWhatIfService.executeWhatIf for Overbooking: starting. . . .");
        BusinessContext businessContext = businessContextService.getCurrentBusinessContext();
        return analyzeChanges(createOptimizationRequest(whatIfItemFactory.convertToWhatIfItems(overrides), businessContext), null);
    }

    public SimplifiedWhatIfResult analyzeChanges(DemandWashOverrideRequest demandAndWashOverrides) {
        List<DemandOverride> demandOverrides = new ArrayList<>();
        List<ArrivalDemandOverride> arrivalDemandOverrides = demandAndWashOverrides.getArrivalDemandOverrides();
        if (arrivalDemandOverrides != null) {
            demandOverrides.addAll(arrivalDemandOverrides);
        }
        List<OccupancyDemandOverride> occupancyDemandOverrides = demandAndWashOverrides.getOccupancyDemandOverrides();
        if (occupancyDemandOverrides != null) {
            demandOverrides.addAll(occupancyDemandOverrides);
        }
        return analyzeChanges(demandAndWashOverrides.getWashOverrides(), demandOverrides);
    }

    public SimplifiedWhatIfResult analyzeChanges(OptimizationRequestType requestType, List<BAROverride> barOverrides) {
        SimplifiedWhatIfResult result = executeWhatIfJob(requestType);
        if (result != null && isContinuousPricing()) {
            DateRange dateRange = adjustDateRangeToSasResponse(computeDateRange(requestType),
                    result.getDateData().get(0).getDate(),
                    result.getDateData().get(result.getDateData().size() - 1).getDate());
            result = formatWhatIfResultsToFinalBAR(result, barOverrides, dateRange);
        }
        return result;
    }

    public SimplifiedWhatIfResult formatWhatIfResultsToFinalBAR(SimplifiedWhatIfResult simplifiedWhatIfResult,
                                                                List<BAROverride> barOverrides,
                                                                DateRange dateRange) {
        LocalDate startDate = new LocalDate(dateRange.getStartDate());
        LocalDate endDate = new LocalDate(dateRange.getEndDate());

        Map<LocalDate, Map<Integer, CPDecisionBAROutput>> arrivalDateToDecisionBAR = cpManagementService.findCPDecisionsBetweenDatesForAccomTypeIdsMap(startDate, endDate);

        CPDecisionContext cpDecisionContext = pricingConfigurationService.getCPDecisionContext(startDate, endDate, true, findAllRemovedOverrides(barOverrides));

        for (SimplifiedWhatIfDateData simplifiedWhatIfDateData : simplifiedWhatIfResult.getDateData()) {
            Date date = simplifiedWhatIfDateData.getDate();
            LocalDate localDate = new LocalDate(date);
            Map<Integer, CPDecisionBAROutput> cpDecisionBAROutputMap = arrivalDateToDecisionBAR.get(localDate);
            //ensures that the values we set to any CPDecisionBAROutput objects aren't persisted
            crudService.flushAndClear();

            for (SimplifiedWhatIfAccomClassData simplifiedWhatIfAccomClassData : simplifiedWhatIfDateData.getSimplifiedAccomClassData()) {
                updateResultsForFinalBAR(barOverrides, cpDecisionContext, date, cpDecisionBAROutputMap, simplifiedWhatIfAccomClassData);
            }
        }
        return simplifiedWhatIfResult;
    }

    @VisibleForTesting
	public
    String findAllRemovedOverrides(List<BAROverride> barOverrides) {
        String removedOverridesStr = "";
        if (CollectionUtils.isNotEmpty(barOverrides)) {
            StringBuilder removedOverrides = new StringBuilder();
            for (BAROverride barOverride : barOverrides) {
                if (barOverride.isRemove()) {
                    removedOverrides.append(barOverride.getAccomTypeId())
                            .append(".")
                            .append(LocalDate.fromDateFields(barOverride.getArrivalDate()))
                            .append(",");
                }
            }
            int length = removedOverrides.toString().length();
            if (length > 0) {
                removedOverridesStr = removedOverrides.toString().substring(0, length - 1);
            }
        }
        return removedOverridesStr;
    }

    private void updateResultsForFinalBAR(List<BAROverride> barOverrides, CPDecisionContext cpDecisionContext, Date date, Map<Integer, CPDecisionBAROutput> cpDecisionBAROutputMap, SimplifiedWhatIfAccomClassData simplifiedWhatIfAccomClassData) {
        for (SimplifiedWhatIfAccomTypeData whatIfAccomTypeData : simplifiedWhatIfAccomClassData.getAccomTypeData()) {
            int accomTypeId = whatIfAccomTypeData.getAccomTypeId();
            CPDecisionBAROutput output = cpDecisionBAROutputMap.get(accomTypeId);
            //Set old Rate to Current Pretty Bar of decision
            whatIfAccomTypeData.setOldBarRate(output.getFinalBAR());
            BigDecimal supplementValue = cpDecisionContext.getSupplement(output);
            boolean isSupplementPercentEnabled = cpDecisionContext.isSupplementPercentEnabled();
            //Calculate New Pretty rate based off of What If Response
            //we came from a continuous pricing mgmt what if
            if (barOverrides != null) {
                BAROverride barOverride = barOverrides.stream().filter(barOverrideBeingFiltered ->
                        barOverrideBeingFiltered.getAccomTypeId() == accomTypeId && barOverrideBeingFiltered.getArrivalDate().equals(date)).findFirst().orElse(null);
                //there is a pending BAR override for the date and accom type of the current whatIfDateData we're looking at
                if (barOverride != null && barOverride.isRemove()) {
                    output.setSpecificOverride(null);
                    output.setFloorOverride(null);
                    output.setCeilingOverride(null);
                } else if (barOverride != null && !barOverride.isRemove() && !isSupplementPercentEnabled) {
                    output.setSpecificOverride(BigDecimalUtil.addReturnsNull(barOverride.getSpecificRate(), supplementValue));
                    output.setFloorOverride(BigDecimalUtil.addReturnsNull(barOverride.getFloorRate(), supplementValue));
                    output.setCeilingOverride(BigDecimalUtil.addReturnsNull(barOverride.getCeilRate(), supplementValue));
                } else if (barOverride != null && !barOverride.isRemove() && isSupplementPercentEnabled) {
                    AccomTypeSupplementValue supplement = cpDecisionContext.getSupplementFor(output).orElse(Supplement.zeroSupplement());

                    output.setSpecificOverride(Supplement.addSupplementTo(barOverride.getSpecificRate(), supplement));
                    output.setFloorOverride(Supplement.addSupplementTo(barOverride.getFloorRate(), supplement));
                    output.setCeilingOverride(Supplement.addSupplementTo(barOverride.getCeilRate(), supplement));
                }
            }
            setVisibilityOfAccomTypeOnUI(whatIfAccomTypeData);

            // Only set optimal bar if we have a new bar rate
            if (whatIfAccomTypeData.getNewBarRate() != null) {
                output.setOptimalBAR(whatIfAccomTypeData.getNewBarRate());
            }

            output.setRoomsOnlyBAR(null);
            output.setFinalBAR(null);
            // Calculate the bar and make sure it respects floor/ceiling with respects to the supplement value
            BigDecimal roundedBarPrice = cpDecisionContext.calculateRoundedRate(output);
            // Set the new BAR value for display
            whatIfAccomTypeData.setNewBarRate(roundedBarPrice);
        }
    }

    private void setVisibilityOfAccomTypeOnUI(SimplifiedWhatIfAccomTypeData whatIfAccomTypeData) {
        if (whatIfAccomTypeData.getNewBarRate() == null) {
            whatIfAccomTypeData.setDoNotDisplay(true);
        }
    }

    public OptimizationRequestType createOptimizationRequest(List<WhatIfItem> whatIfItems, BusinessContext businessContext) {
        DateRange range = computeDateRange(whatIfItems, REQUEST_SHOULDER_DAYS);
        OptimizationRequestType optimizationRequest = new OptimizationRequestType();
        optimizationRequest.setAnalyticOptStartDate(xmlHelper.convertDateToXMLGregorian(range.getStartDate()));
        optimizationRequest.setAnalyticOptEndDate(xmlHelper.convertDateToXMLGregorian(range.getEndDate()));
        optimizationRequest.setOptStartDate(xmlHelper.convertDateToXMLGregorian(range.getStartDate()));
        optimizationRequest.setOptEndDate(xmlHelper.convertDateToXMLGregorian(range.getEndDate()));
        optimizationRequest.setCaughtUpDate(xmlHelper.convertDateToXMLGregorian(dateService.getCaughtUpDate()));
        if (dateService.getWasLastLoadBDE()) {
            optimizationRequest.setOperationType(OPERATION_TYPE_WHAT_IF);
        } else {
            optimizationRequest.setOperationType(OPERATION_TYPE_WHAT_IF_CDP);
        }
        optimizationRequest.setDecisionRateType(determineDecisionRateType());
        optimizationRequest.setLRAFeatureToggle(getLRAEnabledFlagForAnalytics());
        optimizationRequest.setContinuousPricing(isContinuousPricing());
        optimizationRequest.setTaxRate(getDefaultRoomRevenueTaxRate());

        WhatIfRequest whatIfRequestData = new WhatIfRequest();
        for (WhatIfItem item : whatIfItems) {
            item.attachToWhatIfRequest(whatIfRequestData);
        }

        optimizationRequest.setWhatIfRequestData(whatIfRequestData);
        optimizationRequest.setWebRateAlias(configService.getValue(Constants.getPropertyConfigContext(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode()), IPConfigParamName.BAR_WEB_RATE_ALIAS.value()));
        String stalenessThreshold = configService.getValue(Constants.getPropertyConfigContext(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode()), IPConfigParamName.BAR_WEB_RATE_STALENESS_THRESHOLD.value());
        if (stalenessThreshold != null) {
            optimizationRequest.setStaleness(Integer.valueOf(stalenessThreshold));
        }

        optimizationRequest.setStalenessEntries(buildStalenessEntries());

        if (isContinuousPricing()) {
            optimizationRequest.setMasterAccomClassId(-1);
        } else if (businessContext.isSingleBARDecisionEnabled() && businessContext.getMasterRoomClassId() != null) {
            optimizationRequest.setMasterAccomClassId(businessContext.getMasterRoomClassId());
        }
        optimizationRequest.setDecisionId(DECISION_ID);
        return optimizationRequest;
    }

    private OptimizationRequestType.StalenessEntries buildStalenessEntries() {
        OptimizationRequestType.StalenessEntries stalenessEntriesElement = new OptimizationRequestType.StalenessEntries();

        OptimizationRequestType.StalenessEntries.StalenessEntry stalenessEntryElement;
        List<WebrateShoppingConfig> webrateShoppingConfigList = webrateDataSchedulingService.getAllWebrateShoppingConfigsForProperty();
        List<com.ideas.tetris.pacman.common.utils.pojo.StalenessEntry> stalenessEntryList = RssStalenessEntriesUtil.buildStalenessEntriesCollection(webrateShoppingConfigList);

        if (null != stalenessEntryList && !stalenessEntryList.isEmpty()) {
            for (com.ideas.tetris.pacman.common.utils.pojo.StalenessEntry stalenessEntry : stalenessEntryList) {
                stalenessEntryElement = new OptimizationRequestType.StalenessEntries.StalenessEntry();
                stalenessEntryElement.setStartDta(stalenessEntry.getStartDta());
                stalenessEntryElement.setEndDta(stalenessEntry.getEndDta());
                stalenessEntryElement.setValue(stalenessEntry.getValue());
                stalenessEntriesElement.getStalenessEntry().add(stalenessEntryElement);
            }
        }
        return stalenessEntriesElement;
    }

    private SimplifiedWhatIfResult executeWhatIfJob(OptimizationRequestType requestType) {
        SASRequest sasRequest = createSasRequest(requestType);
        String requestXML = marshallRequest(sasRequest);
        LOGGER.debug("SimplifiedWhatIfService.executeWhatIf: Optimization Request marshalled: \n" + requestXML);
        boolean isPropertyReadOnly = false;
        if (regulatorService.isSpringTXEnableRegulatorService()) {
            isPropertyReadOnly = regulatorSpringService.isPropertyReadOnly();
        } else {
            isPropertyReadOnly = regulatorService.isPropertyReadOnly();
        }
        if (isPropertyReadOnly) {
            if (shouldEnableOverrideScreensDuringIDP()) {
                boolean propertySetToReadOnlyByCDP = propertyService.isPropertySetToReadOnlyDuringIDP(PacmanWorkContextHelper.getPropertyId());
                if (propertySetToReadOnlyByCDP) {
                    throw new TetrisException(ErrorCode.DENIED_BY_REGULATOR_SERVICE, "Another process is currently running.");
                }
            } else {
                throw new TetrisException(ErrorCode.DENIED_BY_REGULATOR_SERVICE, "Another process is currently running.");
            }
        }
        Integer activityId = userActivityService.startActivity(getCurrentPageCode(), ActivityType.TIMED_EVENT,
                "What-If SAS request", null, hasPropertyGroupAccess());

        DateRange dateRange = computeDateRange(requestType);
        SimplifiedWhatIfResult result = executeWhatIfJob(new WhatIfRequestItem(requestXML, dateRange));
        userActivityService.endActivity(activityId, null);
        LOGGER.debug("SimplifiedWhatIfService.executeWhatIf: Received response XML: \n" + result);
        return result;
    }

    private boolean shouldEnableOverrideScreensDuringIDP() {
        return shouldEnableOverrideScreensDuringCDP() || shouldEnableOverrideScreensDuringBDE();
    }

    private boolean shouldEnableOverrideScreensDuringCDP() {
        if (regulatorService.isSpringTXEnableRegulatorService(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode())) {
            boolean isIDPRunning = regulatorSpringService.isCDPJobRunning(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode());
            return isIDPRunning;
        } else {
            boolean isIDPRunning = regulatorService.isCDPJobRunning(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode());
            return isIDPRunning;
        }

    }

    private boolean shouldEnableOverrideScreensDuringBDE() {
        if (regulatorService.isSpringTXEnableRegulatorService(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode())) {
            boolean isIDPRunning = regulatorSpringService.isBDEJobRunning(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode());
            return isIDPRunning;
        } else {
            boolean isIDPRunning = regulatorService.isBDEJobRunning(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode());
            return isIDPRunning;
        }
    }

    @SuppressWarnings("serial")
    private DateRange computeDateRange(final List<WhatIfItem> whatIfItems, int shoulderDays) {
        Calendar earliestAffectedDate = Calendar.getInstance();
        earliestAffectedDate.setTime(whatIfItems.get(0).getOverrideDate());
        Calendar latestAffectedDate = Calendar.getInstance();
        latestAffectedDate.setTime(whatIfItems.get(0).getOverrideDate());
        for (WhatIfItem o : whatIfItems) {
            if (o.getOverrideDate().before(earliestAffectedDate.getTime())) {
                earliestAffectedDate.setTime(o.getOverrideDate());
            }
            if (o.getOverrideDate().after(latestAffectedDate.getTime())) {
                latestAffectedDate.setTime(o.getOverrideDate());
            }
        }
        return addShoulderDatesToDateRange(shoulderDays, earliestAffectedDate, latestAffectedDate);
    }

    private DateRange addShoulderDatesToDateRange(int shoulderDays, Date earliestAffectedDate, Date latestAffectedDate) {
        return addShoulderDatesToDateRange(shoulderDays, DateUtil.getCalendarForDate(earliestAffectedDate), DateUtil.getCalendarForDate(latestAffectedDate));
    }

    private DateRange addShoulderDatesToDateRange(int shoulderDays, Calendar earliestAffectedDate, Calendar latestAffectedDate) {
        earliestAffectedDate.add(Calendar.DATE, -shoulderDays);
        latestAffectedDate.add(Calendar.DATE, shoulderDays);
        return adjustDateRangeToOptimizationWindow(earliestAffectedDate, latestAffectedDate);
    }

    @SuppressWarnings("serial")
	public
    DateRange computeDateRange(final OptimizationRequestType optRequest) {
        // These are NOT backwards.  We almost certainly have a different value for shoulderDays as
        // what was used when creating the request, so initially we set the earliest day as the latest possible
        // and vice versa.  As long as our request has at least one override in it of some type, they will
        // get properly overridden.

        Calendar earliestAffectedDate = Calendar.getInstance();
        earliestAffectedDate.setTime(xmlHelper.convertXMLGregorianToDate(optRequest.getOptEndDate()));

        Calendar latestAffectedDate = Calendar.getInstance();
        latestAffectedDate.setTime(xmlHelper.convertXMLGregorianToDate(optRequest.getOptStartDate()));

        WhatIfRequest whatIfRequestData = optRequest.getWhatIfRequestData();
        if (whatIfRequestData != null) {
            whatIfRequestData.getBarWhatIfRequestElements().forEach(barWhatIfRequestElement ->
                    correctAffectedDates(earliestAffectedDate, latestAffectedDate, barWhatIfRequestElement.getArrivalDate()));
            whatIfRequestData.getArrivalOverride().forEach(arrivalDemandOverrideElement ->
                    correctAffectedDates(earliestAffectedDate, latestAffectedDate, arrivalDemandOverrideElement.getArrivalDate()));
            whatIfRequestData.getOccupancyOverride().forEach(occupancyDemandOverrideElement ->
                    correctAffectedDates(earliestAffectedDate, latestAffectedDate, occupancyDemandOverrideElement.getOccupancyDate()));
            whatIfRequestData.getWashOverride().forEach(washOverrideElement ->
                    correctAffectedDates(earliestAffectedDate, latestAffectedDate, washOverrideElement.getOccupancyDate()));
            whatIfRequestData.getOverbookingOverride().forEach(overbookingOverrideElement ->
                    correctAffectedDates(earliestAffectedDate, latestAffectedDate, overbookingOverrideElement.getOccupancyDate()));
            whatIfRequestData.getCostOfWalkOverride().forEach(costOfWalkOverrideElement ->
                    correctAffectedDates(earliestAffectedDate, latestAffectedDate, costOfWalkOverrideElement.getOccupancyDate()));
        }
        return adjustDateRangeToOptimizationWindow(earliestAffectedDate, latestAffectedDate);
    }

    private DateRange adjustDateRangeToOptimizationWindow(Calendar earliestAffectedDate, Calendar latestAffectedDate) {
        Date optimizationWindowStartDate = dateService.getOptimizationWindowStartDate();
        if (earliestAffectedDate.getTime().before(optimizationWindowStartDate)) {
            earliestAffectedDate.setTime(optimizationWindowStartDate);
        }
        Date optimizationWindowEndDate = getOptimizationWindowEndDateBDE();
        if (latestAffectedDate.getTime().after(optimizationWindowEndDate)) {
            Date maxDecisionDate = getMaxDecisionDate();
            if (maxDecisionDate.before(optimizationWindowEndDate)) {
                latestAffectedDate.setTime(maxDecisionDate);
            } else {
                latestAffectedDate.setTime(optimizationWindowEndDate);
            }
        }
        DateRange result = new DateRange();
        result.setStartDate(earliestAffectedDate.getTime());
        result.setEndDate(latestAffectedDate.getTime());
        return result;
    }

    private void correctAffectedDates(Calendar earliestAffectedDate, Calendar latestAffectedDate, XMLGregorianCalendar date) {
        if (earliestAffectedDate.after(date.toGregorianCalendar())) {
            earliestAffectedDate.setTime(xmlHelper.convertXMLGregorianToDate(date));
        }
        if (latestAffectedDate.before(date.toGregorianCalendar())) {
            latestAffectedDate.setTime(xmlHelper.convertXMLGregorianToDate(date));
        }
    }

    public SASRequest createSasRequest(OptimizationRequestType requestType) {
        SASRequest sasRequest = new SASRequest();
        sasRequest.setRequestHeader(optServiceUtil.createRequestHeader());
        sasRequest.setOptimizationRequest(requestType);

        if (isSASPerformanceDebugEnabled()) {
            sasRequest.getRequestHeader().setPerfLogLevel(PERF_LOG_LEVEL);
        }
        return sasRequest;
    }

    public SASResponse unmarshallResponse(String responseXML) {
        try {
            return (SASResponse) responseJaxbUtil.unmarshall(responseXML);
        } catch (Exception e) {
            LOGGER.error("SimplifiedWhatIfService could not parse SASResponse.", e);
            throw new TetrisException(ErrorCode.SAS_INTEGRATION_ERROR, "Could not parse SAS Response XML", e);
        }
    }

    public String marshallRequest(SASRequest sasRequest) {
        String requestXML = jaxbUtil.marshall(sasRequest);
        LOGGER.debug("SimplifiedWhatIfService.executeWhatIf: Optimization Request marshalled: \n" + requestXML);
        return requestXML;
    }

    private boolean isSASPerformanceDebugEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(IPConfigParamName.SAS_LOG_SASPERFORMANCE_DEBUG_ENABLED.value());
    }

    private String hasPropertyGroupAccess() {
        Integer propertyGroupId = PacmanWorkContextHelper.getWorkContext().getPropertyGroupId();
        return propertyGroupId != null ? "true" : "false";
    }

    @SuppressWarnings("unchecked")
    private String getCurrentPageCode() {
        List<UserActivity> currentPages = globalCrudService.findByNamedQuery(UserActivity.GET_CURRENT_ACTIVITY_FOR_USER,
                QueryParameter.with("type", ActivityType.VIEW_PAGE.toString()).
                        and("userId", Integer.valueOf(PacmanWorkContextHelper.getWorkContext().getUserId())).
                        parameters());
        if (currentPages == null || currentPages.isEmpty()) {
            return null;
        }
        Integer pageId = currentPages.get(0).getPageId();
        if (pageId == null) {
            return null;
        }
        return globalCrudService.find(UserActivityPage.class, pageId).getPageCode();
    }

    private Date getOptimizationWindowEndDateBDE() {
        return dateService.getOptimizationWindowEndDateBDE();
    }

    protected Date getMaxDecisionDate() {
        if (isContinuousPricing()) {
            LocalDate maxCPDecisionDate = crudService.findByNamedQuerySingleResult(CPDecisionBAROutput.MAX_DECISION_DATE,
                    CPDecisionBAROutput.params());
            return maxCPDecisionDate.toDate();
        }

        return crudService.findByNamedQuerySingleResult(DecisionBAROutput.MAX_DECISION_DATE,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    public SimplifiedWhatIfResult executeWhatIfJob(WhatIfRequestItem whatIfRequestItem) {
        // Pass in the groupEvaluation as a parameter
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(JobParameterKey.INCOMING_SERIALIZABLE, whatIfRequestItem);
        Long jobExecutionId = jobService.startGuaranteedNewInstance(JobName.WhatIfJob, parameters);
        Long jobInstanceId = jobMonitorService.getJobInstanceId(jobExecutionId);
        JobView jobView = jobMonitorService.getJobViewWaitUntilCompletedOrAbandoned(jobInstanceId, false);

        // If the response was a TetrisException, bring it to the UI
        Object response = jobView.getResponseFromStep(WHAT_IF_SAS_INVOCATION_STEP);

        if (!(response instanceof SimplifiedWhatIfResult)) {
            createProblem(jobExecutionId, jobInstanceId, jobView.getStepExecutionId(), PacmanWorkContextHelper.getPropertyId());
            handleException(response);
        }

        return (SimplifiedWhatIfResult) response;
    }

    private void handleException(Object response) {
        if (response instanceof TetrisException) {
            throw (TetrisException) response;
        } else if (!(response instanceof SimplifiedWhatIfResult)) {
            throw new TetrisException(ErrorCode.WHAT_IF_FAILED, "Unable to perform WhatIf");
        }
    }

    private void createProblem(Long jobExecutionId, Long jobInstanceId, Long stepExecutionId, Integer propertyId) {
        problemService.createClosedProblem(jobInstanceId, jobExecutionId, stepExecutionId, WHAT_IF_FAILED, ErrorCode.WHAT_IF_FAILED, propertyId);
    }

    @SuppressWarnings("unchecked")
    public SimplifiedWhatIfResult processResponse(DateRange range, SasWhatIfResponse whatIfResponse) {
        long currentTime = System.currentTimeMillis();
        DateRange adjustedRange = adjustDateRangeToSasResponse(range,
                xmlHelper.convertXMLGregorianToDate(whatIfResponse.getWhatIfDates().get(0).getDate()),
                xmlHelper.convertXMLGregorianToDate(whatIfResponse.getWhatIfDates()
                        .get(whatIfResponse.getWhatIfDates().size() - 1).getDate()));
        SimplifiedWhatIfResult result = new SimplifiedWhatIfResult();
        Map<Date, SimplifiedCurrentValues> currentHotelValuesMap =
                simplifiedCurrentValuesService.getHotelSimplifiedCurrentValues(isDisplayOccupancyForecastInWhatIfToggleEnabled(),
                        adjustedRange.getStartDate(), adjustedRange.getEndDate());
        Map<Date, Map<Integer, SimplifiedCurrentValues>> currentAccomValuesMap =
                simplifiedCurrentValuesService.getAccomClassSimplifiedCurrentValues(adjustedRange.getStartDate(), adjustedRange.getEndDate());
        Map<Date, Map<Integer, SimplifiedCurrentValues>> currentAccomTypeValuesMap =
                simplifiedCurrentValuesService.getAccomTypeSimplifiedCurrentValues(adjustedRange.getStartDate(), adjustedRange.getEndDate());

        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        Map<Integer, AccomClass> accomClasses = getAccomClasses(propertyId);
        Map<Integer, AccomType> accomTypes = getAccomTypes(propertyId);

        // Get the property's market segment forecast type mapping
        Map<Integer, String> propertyUnqualifiedRates = getUnqualifiedRateIdToCodeMapping(propertyId);

        // Get the Max LOS for the Property
        int maxLOS = getMaxLOS();

        Map<LocalDate, Map> dateToAccomTypeCPDecisionBARMap = new HashMap<>();
        Map<Date, Map> dateToAccomTypeLRVMap = new HashMap<>();
        populateDecisionBAROutputForDateRange(adjustedRange, accomTypes, dateToAccomTypeCPDecisionBARMap);
        if (isComponentRoomEnabled()) {
            populateAccomTypeLRVForDateRange(adjustedRange, dateToAccomTypeLRVMap);
        }
        Map<Date, Map<Integer, Map<Integer, BARDecisionInfo>>> barDecisionsForDateRangeAndLengthOfStayUsingMaxLOS =
                barService.getBarDecisionsForDateRangeAndLengthOfStayUsingMaxLOS(maxLOS, adjustedRange.getStartDate(), adjustedRange.getEndDate());

        whatIfResponse.getWhatIfImpact().forEach(whatifImpactType ->
                result.getWhatIfImpactTypeList().add(processWhatIfImpactType(whatifImpactType, accomClasses)));

        List<AccomClass> allAccomClasses = crudService.findByNamedQuery(AccomClass.ALL, QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
        result.getWhatIfImpactTypeList().forEach(data -> {
            Optional<AccomClass> accomClass = allAccomClasses.stream().filter(ac -> ac.getId().equals(data.getAccomClassId())).findFirst();
            accomClass.ifPresent(aClass -> data.setAccomClassViewOrder(aClass.getViewOrder()));
            if (data.getAccomClassViewOrder() == null) {
                data.setAccomClassViewOrder(0);
            }
        });
        List<SimplifiedWhatIfImpactType> whatIfImpactTypeList = result.getWhatIfImpactTypeList()
                .stream().sorted(Comparator.comparing(SimplifiedWhatIfImpactType::getAccomClassViewOrder)).collect(Collectors.toList());
        result.setWhatIfImpactTypeList(whatIfImpactTypeList);

        boolean isDisplayOccupancyForecastInWhatIfToggleEnabled = isDisplayOccupancyForecastInWhatIfToggleEnabled();
        boolean isEnablePhysicalCapacityConsideration = configService.isEnablePhysicalCapacityConsideration();

        for (DateData dateData : whatIfResponse.getWhatIfDates()) {
            Date occupancyDate = xmlHelper.convertXMLGregorianToDate(dateData.getDate());
            // Ignore dates outside the computed range.
            if (!occupancyDate.before(adjustedRange.getStartDate()) && !occupancyDate.after(adjustedRange.getEndDate())) {
                Map<Integer, CPDecisionBAROutput> accomTypeCPDecisionBARMap = dateToAccomTypeCPDecisionBARMap.get(new LocalDate(occupancyDate));
                Map<Integer, LastRoomValueAccomType> accomTypeLRVMap = dateToAccomTypeLRVMap.get(occupancyDate);
                Map<Integer, Map<Integer, BARDecisionInfo>> accomClassToLOSBarDecision = barDecisionsForDateRangeAndLengthOfStayUsingMaxLOS.get(occupancyDate);
                result.getDateData().add(processDateData(
                        new DateDataParameter().dateData(dateData)
                                .currentHotelValuesMap(currentHotelValuesMap)
                                .currentAccomValuesMap(currentAccomValuesMap)
                                .currentAccomTypeValuesMap(currentAccomTypeValuesMap)
                                .accomTypeLRVMap(accomTypeLRVMap)
                                .accomClasses(accomClasses)
                                .propertyUnqualifiedRates(propertyUnqualifiedRates)
                                .maxLOS(maxLOS).accomTypes(accomTypes)
                                .accomTypeCPDecisionBARMap(accomTypeCPDecisionBARMap)
                                .accomClassToLOSBarDecision(accomClassToLOSBarDecision)
                                .setDisplayOccupancyForecastInWhatIfToggleEnabled(isDisplayOccupancyForecastInWhatIfToggleEnabled)
                                .setEnablePhysicalCapacityConsideration(isEnablePhysicalCapacityConsideration)));
            }
        }
        result.getDateData().forEach(data -> sortSimplifiedAccomClassData(data, allAccomClasses));
        LOGGER.info("Total time taken to process data by what if service: " + (System.currentTimeMillis() - currentTime));
        DateRange netVarianceAdjustedDateRange = addShoulderDatesToDateRange(RESPONSE_NET_VARIANCE_DAYS, range.getStartDate(), range.getEndDate());
        result.setNetVarianceDateRange(netVarianceAdjustedDateRange);
        return result;

    }

    public void sortSimplifiedAccomTypeData(SimplifiedWhatIfAccomClassData accomClassData, List<AccomType> allAccomTypes, List<AccomType> baseAccomTypes) {
        List<SimplifiedWhatIfAccomTypeData> simplifiedWhatIfAccomTypeData = accomClassData.getAccomTypeData().stream()
                .sorted(Comparator.comparing(SimplifiedWhatIfAccomTypeData::getAccomTypeCode)).collect(Collectors.toList());
        accomClassData.setAccomTypeData(simplifiedWhatIfAccomTypeData);

        List<Integer> zeroCapacityRTs = allAccomTypes.stream()
                .filter(at -> at.getAccomTypeCapacity() == 0)
                .map(AccomType::getId).collect(Collectors.toList());

        accomClassData.getAccomTypeData().removeIf(at -> zeroCapacityRTs.contains(at.getAccomTypeId()));
        accomClassData.getAccomTypeData().forEach(data -> {
            Optional<AccomType> accomType = allAccomTypes.stream().filter(ac -> ac.getId().equals(data.getAccomTypeId())).findFirst();
            accomType.ifPresent(at -> data.setCapacity(at.getAccomTypeCapacity()));
        });

        AccomType baseAccomType = baseAccomTypes.stream().filter(base -> accomClassData.getAccomTypeData().stream().anyMatch(at -> at.getAccomTypeId() == base.getId())).findFirst().orElse(null);
        if (baseAccomType != null) {
            sortSimplifiedWhatIfAccomTypesByBaseRoomTypeAndCapacity(accomClassData.getAccomTypeData(), baseAccomType);
        }
    }

    public void sortSimplifiedWhatIfAccomTypesByBaseRoomTypeAndCapacity(List<SimplifiedWhatIfAccomTypeData> simplifiedWhatIfAccomTypeData, AccomType baseRoomTypeForSelectedRoomClass) {
        Collections.sort(simplifiedWhatIfAccomTypeData, new Comparator<>() {
            @Override
            public int compare(SimplifiedWhatIfAccomTypeData o1, SimplifiedWhatIfAccomTypeData o2) {
                Boolean o1Base = o1.getAccomTypeId() == baseRoomTypeForSelectedRoomClass.getId();
                Boolean o2Base = o2.getAccomTypeId() == baseRoomTypeForSelectedRoomClass.getId();
                //Compare base room type
                int c = (o2Base.compareTo(o1Base));
                //Compare room type capacity
                if (c == 0) {
                    c = o2.getCapacity().compareTo(o1.getCapacity());
                }
                return c;
            }
        });
    }

    public void sortSimplifiedAccomClassData(SimplifiedWhatIfDateData dateData, List<AccomClass> allAccomClasses) {
        dateData.getSimplifiedAccomClassData().forEach(data -> {
            Optional<AccomClass> accomClass = allAccomClasses.stream().filter(ac -> ac.getId().equals(data.getAccomClassId())).findFirst();
            accomClass.ifPresent(aClass -> data.setAccomClassViewOrder(aClass.getViewOrder()));
        });
        List<SimplifiedWhatIfAccomClassData> simplifiedWhatIfAccomClassDataList = dateData.getSimplifiedAccomClassData()
                .stream().sorted(Comparator.comparing(SimplifiedWhatIfAccomClassData::getAccomClassViewOrder)).collect(Collectors.toList());
        dateData.setSimplifiedAccomClassData(simplifiedWhatIfAccomClassDataList);
    }

    private DateRange adjustDateRangeToSasResponse(DateRange range, Date responseFirstDate, Date responseLastDate) {
        Calendar earliestRequestDate = Calendar.getInstance();
        earliestRequestDate.setTime(range.getStartDate());
        if (earliestRequestDate.getTime().after(responseFirstDate)) {
            range.setStartDate(responseFirstDate);
        }

        Calendar latestRequestDate = Calendar.getInstance();
        latestRequestDate.setTime(range.getEndDate());
        if (latestRequestDate.getTime().before(responseLastDate)) {
            range.setEndDate(responseLastDate);
        }
        return range;
    }

    public SimplifiedWhatIfImpactType processWhatIfImpactType(WhatifImpactType whatifImpactType, Map<Integer, AccomClass> accomClasses) {
        SimplifiedWhatIfImpactType simplifiedWhatIfImpactType = new SimplifiedWhatIfImpactType();
        simplifiedWhatIfImpactType.setAccomClassId(whatifImpactType.getAccomClassId());
        AccomClass accomClass = accomClasses.get(whatifImpactType.getAccomClassId());
        simplifiedWhatIfImpactType.setAccomClassCode(accomClass != null ? accomClass.getName() : null);
        simplifiedWhatIfImpactType.setOccNum(whatifImpactType.getOccNum());
        simplifiedWhatIfImpactType.setOccRev(whatifImpactType.getOccRev());
        simplifiedWhatIfImpactType.setOccLvl(whatifImpactType.getOccLvl());
        simplifiedWhatIfImpactType.setRevPAR(whatifImpactType.getRevPAR());
        simplifiedWhatIfImpactType.setAdr(whatifImpactType.getADR());
        simplifiedWhatIfImpactType.setOccProf(whatifImpactType.getOccProf());
        simplifiedWhatIfImpactType.setProPOR(whatifImpactType.getProPOR());
        simplifiedWhatIfImpactType.setProPAR(whatifImpactType.getProPAR());
        return simplifiedWhatIfImpactType;
    }

    public SimplifiedWhatIfDateData processDateData(DateDataParameter dateDataParameter) {
        SimplifiedWhatIfDateData result = new SimplifiedWhatIfDateData();
        Date occupancyDate = xmlHelper.convertXMLGregorianToDate(dateDataParameter.getDateData().getDate());
        result.setDate(occupancyDate);
        List<AccomType> allAccomTypes = crudService.findByNamedQuery(AccomType.ALL);
        List<AccomType> baseAccomTypes = productManagementService.getBaseAccomTypes();
        for (AccomClassData acData : dateDataParameter.getDateData().getAccomClassData()) {
            String accomClassName = dateDataParameter.getAccomClasses().get(acData.getAccomClassId()).getName();
            Map<Integer, BARDecisionInfo> losToBarDecision = null;
            if (dateDataParameter.getAccomClassToLOSBarDecision() != null) {
                losToBarDecision = dateDataParameter.getAccomClassToLOSBarDecision().get(acData.getAccomClassId());
            }
            SimplifiedWhatIfAccomClassData accomClassData = processAccomClassData(
                    new AccomClassDataParameter()
                            .acData(acData)
                            .accomClassName(accomClassName)
                            .simplifiedCurrentAccomClassValuesMap(dateDataParameter.getCurrentAccomValuesMap().get(occupancyDate))
                            .simplifiedCurrentAccomTypeValuesMap(dateDataParameter.getCurrentAccomTypeValuesMap().get(occupancyDate))
                            .accomTypeLRVMap(dateDataParameter.getAccomTypeLRVMap())
                            .propertyUnqualifiedRates(dateDataParameter.getPropertyUnqualifiedRates())
                            .maxLOS(dateDataParameter.getMaxLOS())
                            .accomTypes(dateDataParameter.getAccomTypes())
                            .accomTypeCPDecisionBARMap(dateDataParameter.getAccomTypeCPDecisionBARMap())
                            .previousLengthOfStayBarDecisions(losToBarDecision), allAccomTypes, baseAccomTypes);
            result.getSimplifiedAccomClassData().add(accomClassData);
        }

        PropertyLevelData propertyData = dateDataParameter.getDateData().getPropertyData();
        // Supply the corresponding current values
        SimplifiedCurrentValues simplifiedCurrentValues = dateDataParameter.getCurrentHotelValuesMap().get(occupancyDate);
        if (propertyData != null) {
            result.setNewExpectedWalks(propertyData.getExpectedWalks());
            result.setNewOverbooking(propertyData.getOverbooking().longValue());
            if (dateDataParameter.isDisplayOccupancyForecastInWhatIfToggleEnabled()
                    && dateDataParameter.isEnablePhysicalCapacityConsideration()) {
                result.setNewOccLvlChgTot(propertyData.getOccLvlChgTot());
                result.setNewOccLvlValTot(propertyData.getOccLvlValTot());
                result.setOldOccLvlValTot(simplifiedCurrentValues.getOccupancyForecastPercent());
            } else if (dateDataParameter.isDisplayOccupancyForecastInWhatIfToggleEnabled()
                    && !dateDataParameter.isEnablePhysicalCapacityConsideration()) {
                result.setNewOccLvlChgEff(propertyData.getOccLvlChgEff());
                result.setNewOccLvlValEff(propertyData.getOccLvlValEff());
                result.setOldOccLvlValEff(simplifiedCurrentValues.getOccupancyForecastPercent());
            }
        }
        result.setOldOverbooking(simplifiedCurrentValues.getOverbooking());
        result.setOldExpectedWalks(simplifiedCurrentValues.getExpectedWalks());
        result.setOnBooks(simplifiedCurrentValues.getRoomsSold());
        return result;
    }

    private SimplifiedWhatIfAccomClassData processAccomClassData(AccomClassDataParameter accomClassDataParameter, List<AccomType> allAccomTypes, List<AccomType> baseAccomTypes) {
        SimplifiedWhatIfAccomClassData result = new SimplifiedWhatIfAccomClassData();
        int accomClassId = accomClassDataParameter.getAcData().getAccomClassId();
        SimplifiedCurrentValues simplifiedCurrentAccomClassValues =
                accomClassDataParameter.getSimplifiedCurrentAccomClassValuesMap().get(accomClassId);

        result.setAccomClassId(accomClassId);
        result.setAccomClassCode(accomClassDataParameter.getAccomClassName());

        // Each BarOutputData gives us the rate for a particular LOS
        accomClassDataParameter.getAcData().getBarData().forEach(barData ->
                handleBarDataForAccomClass(accomClassDataParameter.getPropertyUnqualifiedRates(),
                        accomClassDataParameter.getMaxLOS(),
                        accomClassDataParameter.getPreviousLengthOfStayBarDecisions(),
                        result,
                        barData)
        );

        // OccupancyForecast is stored at the Accom Type/Mkt Seg Level, so again we must sum up
        accomClassDataParameter.getAcData().getAccomTypeDataList().forEach(atData ->
                processAccomTypeData(accomClassDataParameter.getSimplifiedCurrentAccomTypeValuesMap(),
                        accomClassDataParameter.getAccomTypeLRVMap(),
                        accomClassDataParameter.getAccomTypes(),
                        accomClassDataParameter.getAccomTypeCPDecisionBARMap(),
                        result,
                        atData));
        result.setNewLRV(accomClassDataParameter.getAcData().getLrv());
        result.setOnBooks(BigDecimal.valueOf(nullCheck(simplifiedCurrentAccomClassValues.getRoomsSold())));
        result.setOldLRV(simplifiedCurrentAccomClassValues.getLrv());
        sortSimplifiedAccomTypeData(result, allAccomTypes, baseAccomTypes);
        return result;
    }

    private void processAccomTypeData(Map<Integer, SimplifiedCurrentValues> simplifiedCurrentAccomTypeValuesMap,
                                      Map<Integer, LastRoomValueAccomType> accomTypeLRVMap,
                                      Map<Integer, AccomType> accomTypeMap,
                                      Map<Integer, CPDecisionBAROutput> accomTypeCPDecisionBARMap,
                                      SimplifiedWhatIfAccomClassData result,
                                      AccomTypeData atData) {
        SimplifiedWhatIfAccomTypeData whatIfAccomTypeData = new SimplifiedWhatIfAccomTypeData();
        whatIfAccomTypeData.setAccomTypeId(atData.getAccomTypeId());
        AccomType accomType = accomTypeMap.get(atData.getAccomTypeId());
        if (accomType != null) {
            whatIfAccomTypeData.setAccomTypeCode(accomType.getAccomTypeCode());
            if (accomTypeCPDecisionBARMap != null && accomTypeCPDecisionBARMap.get(accomType.getId()) != null) {
                CPDecisionBAROutput cpDecisionBAROutput = accomTypeCPDecisionBARMap.get(accomType.getId());
                whatIfAccomTypeData.setOldBarRate(cpDecisionBAROutput.getFinalBAR());
            }
        }
        whatIfAccomTypeData.setNewOverbooking(atData.getOverbooking());
        if (accomType != null && accomType.getAccomTypeCapacity() == 0) {
            whatIfAccomTypeData.setOldOverbooking(0);
        } else {
            if (simplifiedCurrentAccomTypeValuesMap != null) {
                whatIfAccomTypeData.setOldOverbooking(Math.toIntExact(
                        simplifiedCurrentAccomTypeValuesMap.get(atData.getAccomTypeId()).getOverbooking()));
            }
        }
        List<BarOutputData> barData = atData.getBarData();
        if (CollectionUtils.isNotEmpty(barData)) {
            //We grab the 0th item since right now only one LOS is supported
            whatIfAccomTypeData.setNewBarRate(barData.get(0).getRateValue());
        }
        if (isComponentRoomEnabled()) {
            whatIfAccomTypeData.setNewLRV(atData.getLrv());
            if (accomTypeLRVMap != null && accomTypeLRVMap.get(atData.getAccomTypeId()) != null) {
                whatIfAccomTypeData.setOldLRV(accomTypeLRVMap.get(atData.getAccomTypeId()).getValue());
            }
        }
        result.getAccomTypeData().add(whatIfAccomTypeData);
    }

    private void handleBarDataForAccomClass(Map<Integer, String> propertyUnqualifiedRates,
                                            int maxLOS,
                                            Map<Integer, BARDecisionInfo> previousLengthOfStayBarDecisions,
                                            SimplifiedWhatIfAccomClassData result,
                                            BarOutputData barData) {
        if (barData.getLos() <= maxLOS) {
            int rateUnqualifiedId = barData.getUnqRateId();
            String rateUnqualifiedCode = propertyUnqualifiedRates.get(rateUnqualifiedId);

            int barLOS = barData.getLos();
            result.getNewBarRateByLOS().put(barLOS, rateUnqualifiedCode);

            String previousRatePlan = "None";
            if (previousLengthOfStayBarDecisions != null && previousLengthOfStayBarDecisions.containsKey(barLOS)) {
                previousRatePlan = previousLengthOfStayBarDecisions.get(barLOS).getRatePlanName();
            }
            result.getOldBarRateByLOS().put(barLOS, previousRatePlan);
        }
    }

    private Long nullCheck(Long o) {
        if (o != null) {
            return o;
        }
        return 0L;
    }

    private int getMaxLOS() {
        if (!configService.getValue(Constants.getPropertyConfigContext(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode()), IPConfigParamName.BAR_BAR_DECISION.value()).equals(Constants.BAR_DECISION_VALUE_LOS)) {
            return 1;
        } else {
            return Integer.valueOf(configService.getValue(Constants.getPropertyConfigContext(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode()), IPConfigParamName.BAR_MAX_LOS.value()));
        }
    }

    @SuppressWarnings("unchecked")
	public
    Map<Integer, String> getUnqualifiedRateIdToCodeMapping(Integer propertyId) {
        Map<Integer, String> rateIdToNameMap = new HashMap<>();
        List<RateUnqualified> propertyUnqualifiedRates =
                crudService.findByNamedQuery(RateUnqualified.BY_PROPERTY_ID, QueryParameter.with(PROPERTY_ID, propertyId).parameters());
        if (propertyUnqualifiedRates != null) {
            for (RateUnqualified rateUnqualified : propertyUnqualifiedRates) {
                rateIdToNameMap.put(rateUnqualified.getId(), rateUnqualified.getName());
            }
        }
        return rateIdToNameMap;
    }

    @SuppressWarnings("unchecked")
	public
    Map<Integer, AccomClass> getAccomClasses(Integer propertyId) {
        Map<Integer, AccomClass> accomClassNames = new HashMap<>();
        List<AccomClass> accomClasses = crudService.findByNamedQuery(AccomClass.BY_PROPERTY_ID, QueryParameter.with(PROPERTY_ID, propertyId).parameters());
        if (accomClasses != null) {
            for (AccomClass accomClass : accomClasses) {
                accomClassNames.put(accomClass.getId(), accomClass);
            }
        }
        return accomClassNames;
    }

    @SuppressWarnings("unchecked")
	public
    Map<Integer, AccomType> getAccomTypes(Integer propertyId) {
        Map<Integer, AccomType> accomTypeMap = new HashMap<>();
        List<AccomType> accomTypes = crudService.findByNamedQuery(AccomType.BY_PROPERTY_ID, QueryParameter.with(PROPERTY_ID, propertyId).parameters());
        if (accomTypes != null) {
            for (AccomType accomType : accomTypes) {
                accomTypeMap.put(accomType.getId(), accomType);
            }
        }
        return accomTypeMap;
    }

    @ForTesting
	public
    void populateDecisionBAROutputForDateRange(DateRange range, Map<Integer, AccomType> accomTypes, Map<LocalDate, Map> dateToAccomTypeCPDecisionBARMap) {
        List<CPDecisionBAROutput> cpDecisionBAROutputs =
                cpManagementService.findCPDecisionsBetweenDatesForAccomTypes(new LocalDate(range.getStartDate()),
                        new LocalDate(range.getEndDate()),
                        new ArrayList<>(accomTypes.values()));
        cpDecisionBAROutputs.stream()
                .forEach(cpDecisionBAROutput -> {
                    LocalDate occupancyDate = cpDecisionBAROutput.getArrivalDate();
                    dateToAccomTypeCPDecisionBARMap.putIfAbsent(occupancyDate, new HashMap<>());
                    dateToAccomTypeCPDecisionBARMap.get(occupancyDate).put(cpDecisionBAROutput.getAccomType().getId(), cpDecisionBAROutput);
                });
    }

    @ForTesting
	public
    void populateAccomTypeLRVForDateRange(DateRange range, Map<Date, Map> dateToAccomTypeLRVMap) {
        List<LastRoomValueAccomType> lrvDecisionOutputs =
                lastRoomValueDecisionService.getLastRoomValueAtAccomTypeDecisions(range.getStartDate(), range.getEndDate());
        lrvDecisionOutputs.stream()
                .forEach(lrvDecisionOutput -> {
                    Date occupancyDate = lrvDecisionOutput.getOccupancyDate();
                    dateToAccomTypeLRVMap.putIfAbsent(occupancyDate, new HashMap<>());
                    dateToAccomTypeLRVMap.get(occupancyDate).put(lrvDecisionOutput.getAccomTypeID(), lrvDecisionOutput);
                });
    }

    private boolean isComponentRoomEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED.value()) &&
                componentRoomService.isComponentRoomEnabledWithValidConfiguration(PacmanWorkContextHelper.getPropertyId());
    }

    public boolean isDisplayOccupancyForecastInWhatIfToggleEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.DISPLAY_OCCUPANCY_FORECAST_IN_WHAT_IF.value());
    }

    public class AbstractDataParameter {
        protected Map<Integer, LastRoomValueAccomType> accomTypeLRVMap;
        protected Map<Integer, String> propertyUnqualifiedRates;
        protected int maxLOS;
        protected Map<Integer, AccomType> accomTypes;

        public Map<Integer, LastRoomValueAccomType> getAccomTypeLRVMap() {
            return accomTypeLRVMap;
        }

        public Map<Integer, String> getPropertyUnqualifiedRates() {
            return propertyUnqualifiedRates;
        }

        public int getMaxLOS() {
            return maxLOS;
        }

        public Map<Integer, AccomType> getAccomTypes() {
            return accomTypes;
        }
    }

    public class DateDataParameter extends AbstractDataParameter {
        private DateData dateData;
        private Map<Date, SimplifiedCurrentValues> currentHotelValuesMap;
        private Map<Date, Map<Integer, SimplifiedCurrentValues>> currentAccomValuesMap;
        private Map<Date, Map<Integer, SimplifiedCurrentValues>> currentAccomTypeValuesMap;
        private Map<Integer, AccomClass> accomClasses;
        private Map<Integer, CPDecisionBAROutput> accomTypeCPDecisionBARMap;
        private Map<Integer, Map<Integer, BARDecisionInfo>> accomClassToLOSBarDecision;
        private boolean isDisplayOccupancyForecastInWhatIfToggleEnabled;
        private boolean enablePhysicalCapacityConsideration;

        public DateDataParameter dateData(DateData dateData) {
            this.dateData = dateData;
            return this;
        }

        public DateDataParameter currentHotelValuesMap(Map<Date, SimplifiedCurrentValues> currentHotelValuesMap) {
            this.currentHotelValuesMap = currentHotelValuesMap;
            return this;
        }

        public DateDataParameter currentAccomValuesMap(Map<Date, Map<Integer, SimplifiedCurrentValues>> currentAccomValuesMap) {
            this.currentAccomValuesMap = currentAccomValuesMap;
            return this;
        }

        public DateDataParameter currentAccomTypeValuesMap(Map<Date, Map<Integer, SimplifiedCurrentValues>> currentAccomTypeValuesMap) {
            this.currentAccomTypeValuesMap = currentAccomTypeValuesMap;
            return this;
        }

        public DateDataParameter accomTypeLRVMap(Map<Integer, LastRoomValueAccomType> accomTypeLRVMap) {
            this.accomTypeLRVMap = accomTypeLRVMap;
            return this;
        }

        public DateDataParameter accomClasses(Map<Integer, AccomClass> accomClasses) {
            this.accomClasses = accomClasses;
            return this;
        }

        public DateDataParameter propertyUnqualifiedRates(Map<Integer, String> propertyUnqualifiedRates) {
            this.propertyUnqualifiedRates = propertyUnqualifiedRates;
            return this;
        }

        public DateDataParameter maxLOS(int maxLOS) {
            this.maxLOS = maxLOS;
            return this;
        }

        public DateDataParameter accomTypes(Map<Integer, AccomType> accomTypes) {
            this.accomTypes = accomTypes;
            return this;
        }

        public DateDataParameter accomTypeCPDecisionBARMap(Map<Integer, CPDecisionBAROutput> accomTypeCPDecisionBARMap) {
            this.accomTypeCPDecisionBARMap = accomTypeCPDecisionBARMap;
            return this;
        }

        public DateDataParameter accomClassToLOSBarDecision(Map<Integer, Map<Integer, BARDecisionInfo>> accomClassToLOSBarDecision) {
            this.accomClassToLOSBarDecision = accomClassToLOSBarDecision;
            return this;
        }

        public DateDataParameter setDisplayOccupancyForecastInWhatIfToggleEnabled(boolean displayOccupancyForecastInWhatIfToggleEnabled) {
            this.isDisplayOccupancyForecastInWhatIfToggleEnabled = displayOccupancyForecastInWhatIfToggleEnabled;
            return this;
        }

        public DateDataParameter setEnablePhysicalCapacityConsideration(boolean enablePhysicalCapacityConsideration) {
            this.enablePhysicalCapacityConsideration = enablePhysicalCapacityConsideration;
            return this;
        }

        public DateData getDateData() {
            return dateData;
        }

        public Map<Date, SimplifiedCurrentValues> getCurrentHotelValuesMap() {
            return currentHotelValuesMap;
        }

        public Map<Date, Map<Integer, SimplifiedCurrentValues>> getCurrentAccomValuesMap() {
            return currentAccomValuesMap;
        }

        public Map<Date, Map<Integer, SimplifiedCurrentValues>> getCurrentAccomTypeValuesMap() {
            return currentAccomTypeValuesMap;
        }

        public Map<Integer, AccomClass> getAccomClasses() {
            return accomClasses;
        }

        public Map<Integer, CPDecisionBAROutput> getAccomTypeCPDecisionBARMap() {
            return accomTypeCPDecisionBARMap;
        }

        public Map<Integer, Map<Integer, BARDecisionInfo>> getAccomClassToLOSBarDecision() {
            return accomClassToLOSBarDecision;
        }

        public boolean isDisplayOccupancyForecastInWhatIfToggleEnabled() {
            return isDisplayOccupancyForecastInWhatIfToggleEnabled;
        }

        public boolean isEnablePhysicalCapacityConsideration() {
            return enablePhysicalCapacityConsideration;
        }
    }

    public class AccomClassDataParameter extends AbstractDataParameter {
        private AccomClassData acData;
        private String accomClassName;
        private Map<Integer, SimplifiedCurrentValues> simplifiedCurrentAccomClassValuesMap;
        private Map<Integer, SimplifiedCurrentValues> simplifiedCurrentAccomTypeValuesMap;
        private Map<Integer, CPDecisionBAROutput> accomTypeCPDecisionBARMap;
        private Map<Integer, BARDecisionInfo> previousLengthOfStayBarDecisions;

        public AccomClassData getAcData() {
            return acData;
        }

        public String getAccomClassName() {
            return accomClassName;
        }

        public Map<Integer, SimplifiedCurrentValues> getSimplifiedCurrentAccomClassValuesMap() {
            return simplifiedCurrentAccomClassValuesMap;
        }

        public Map<Integer, SimplifiedCurrentValues> getSimplifiedCurrentAccomTypeValuesMap() {
            return simplifiedCurrentAccomTypeValuesMap;
        }

        public Map<Integer, CPDecisionBAROutput> getAccomTypeCPDecisionBARMap() {
            return accomTypeCPDecisionBARMap;
        }

        public Map<Integer, BARDecisionInfo> getPreviousLengthOfStayBarDecisions() {
            return previousLengthOfStayBarDecisions;
        }

        public AccomClassDataParameter acData(AccomClassData acData) {
            this.acData = acData;
            return this;
        }

        public AccomClassDataParameter accomClassName(String accomClassName) {
            this.accomClassName = accomClassName;
            return this;
        }

        public AccomClassDataParameter simplifiedCurrentAccomClassValuesMap(Map<Integer, SimplifiedCurrentValues> simplifiedCurrentAccomClassValuesMap) {
            this.simplifiedCurrentAccomClassValuesMap = simplifiedCurrentAccomClassValuesMap;
            return this;
        }

        public AccomClassDataParameter simplifiedCurrentAccomTypeValuesMap(Map<Integer, SimplifiedCurrentValues> simplifiedCurrentAccomTypeValuesMap) {
            this.simplifiedCurrentAccomTypeValuesMap = simplifiedCurrentAccomTypeValuesMap;
            return this;
        }

        public AccomClassDataParameter accomTypeLRVMap(Map<Integer, LastRoomValueAccomType> accomTypeLRVMap) {
            this.accomTypeLRVMap = accomTypeLRVMap;
            return this;
        }

        public AccomClassDataParameter propertyUnqualifiedRates(Map<Integer, String> propertyUnqualifiedRates) {
            this.propertyUnqualifiedRates = propertyUnqualifiedRates;
            return this;
        }

        public AccomClassDataParameter maxLOS(int maxLOS) {
            this.maxLOS = maxLOS;
            return this;
        }

        public AccomClassDataParameter accomTypes(Map<Integer, AccomType> accomTypes) {
            this.accomTypes = accomTypes;
            return this;
        }

        public AccomClassDataParameter accomTypeCPDecisionBARMap(Map<Integer, CPDecisionBAROutput> accomTypeCPDecisionBARMap) {
            this.accomTypeCPDecisionBARMap = accomTypeCPDecisionBARMap;
            return this;
        }

        public AccomClassDataParameter previousLengthOfStayBarDecisions(Map<Integer, BARDecisionInfo> previousLengthOfStayBarDecisions) {
            this.previousLengthOfStayBarDecisions = previousLengthOfStayBarDecisions;
            return this;
        }
    }

    private void validateBAROverride(List<WhatIfItem> whatIfItems) {
        whatIfItems.forEach(item -> {
            BarWhatIfRequestElement element = (BarWhatIfRequestElement) item.getXmlRepresentation();
            if (!element.getOverrideType().equalsIgnoreCase(BARDECISIONOVERRIDE_NONE) && ((element.getRateUnqualifiedId() != null && element.getRateUnqualifiedId() == 0)
                    && (element.getFloorRateUnqualifiedId() == null || (element.getFloorRateUnqualifiedId() != null && element.getFloorRateUnqualifiedId() == 0))
                    && (element.getCeilRateUnqualifiedId() == null || (element.getCeilRateUnqualifiedId() != null && element.getCeilRateUnqualifiedId() == 0)))) {
                throw new TetrisException(ErrorCode.INVALID_INPUT, "Invalid Rate Unqualified Id");
            }
        });
    }

    public boolean isDisplayProfitMetricsEnabled() {
        return isProfitOrChannelOptimizationEnabled() && isDisplayProfitMetricsOnWhatIfEnabled();
    }

    private boolean isDisplayProfitMetricsOnWhatIfEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.SHOW_PROFIT_METRICS_ON_WHAT_IF_SCREEN);
    }

    private boolean isProfitOrChannelOptimizationEnabled() {
        boolean isProfitOptimizationEnabled = pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PROFIT_OPTIMIZATION_ENABLED);
        boolean isChannelOptimizationEnabled = pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_CHANNEL_OPTIMIZATION);
        return isProfitOptimizationEnabled || isChannelOptimizationEnabled;
    }
}
