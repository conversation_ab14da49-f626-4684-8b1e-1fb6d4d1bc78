package com.ideas.tetris.pacman.services.forecast;


import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.MktSegAccomActivity;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyForecast;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.ReservationNight;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.TotalActivity;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.ClientPropertyView;
import com.ideas.tetris.pacman.services.dashboard.builder.AnalyticsBuilder;
import com.ideas.tetris.pacman.services.bookingpace.dto.ForecastGroupOccFcstDto;
import com.ideas.tetris.pacman.services.datafeed.dto.RevplanMarketOccupancyForecast;
import com.ideas.tetris.pacman.services.datafeed.service.DatafeedRequest;
import com.ideas.tetris.pacman.services.forecast.dto.*;
import com.ideas.tetris.pacman.services.groupblock.GroupBlockDetail;
import com.ideas.tetris.pacman.services.limiteddatabuild.dto.LDBProjectionDaySummary;
import com.ideas.tetris.pacman.services.limiteddatabuild.entity.LDBProjection;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.marketsegment.service.MarketSegmentService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.validation.dto.OccupancyFcstSummaryDto;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ideas.tetris.pacman.common.constants.Constants.*;
import static com.ideas.tetris.pacman.common.constants.NotificationKeyConstants.SYSTEM_DATE_FORMAT;
import static com.ideas.tetris.pacman.util.BigDecimalUtil.divide;

@Slf4j
@Component
@Transactional
public class OccupancyForecastService extends AbstractOccupancyForecastService {

    @Autowired
	private PropertyService propertyService;

    @Autowired
	private AnalyticsBuilder analyticsBuilder;

    @Autowired
	private MarketSegmentService marketSegmentService;


    public List<OccupancyForecastDTO> getOccupancyForecastForProperty(final String clientCode,
                                                                      final String propertyCode,
                                                                      final LocalDate startDate) {

        final List<OccupancyForecastDTO> occupancyForecasts =
                getOccupancyForecastInternal(clientCode, propertyCode, startDate);

        return !occupancyForecasts.isEmpty() ? occupancyForecasts
                : tryGetOccupancyForecastForLimitedDataBuild(clientCode, propertyCode, startDate);
    }

    public List<RoomTypeLevelOccupancyForecastDTO> getRoomTypeLevelOccupancyForecast(LocalDate startDate, LocalDate endDate){
        List<Object[]> rtOccupancyFcst = tenantCrudService.findByNamedQuery(OccupancyForecast.GET_ROOM_TYPE_LEVEL_OCCUPANCY_FCST_DATERANGE_STARTDATE_AND_ENDDATE,
                QueryParameter.with("startDate", DateUtil.convertLocalDateToJavaUtilDate(startDate))
                        .and("endDate",DateUtil.convertLocalDateToJavaUtilDate(endDate))
                        .parameters());
        return rtOccupancyFcst.stream().map(
                rtof -> new RoomTypeLevelOccupancyForecastDTO((Integer) rtof[0], (java.sql.Date) rtof[1], (BigDecimal) rtof[2], (BigDecimal) rtof[3])
        ).collect(Collectors.toList());
    }

    public List<OccupancyForecastDTO> getOccupancyForecastExcludingRateCodes(String clientCode, String propertyCode,
                                                                             LocalDate startDate, List<String> excludeRateCodeList) {

        log.debug(">>> Fetching occupancy forecast excluding rate codes for clientCode:{}, propertyCode:{}", clientCode, propertyCode);

        final int propertyId = getPropertyId(clientCode, propertyCode);
        final Date start = LocalDateUtils.toDate(startDate);
        final Date end = dateService.getLatestOccupancyForecastDate(propertyId);

        if (end == null || end.before(start)) {
            return List.of();
        }

        var revenueDifference = getRevenueDifference(startDate, excludeRateCodeList);
        var roomsSoldDifference = getRoomsSoldDifference(startDate, excludeRateCodeList);

        // rawOccupancyForecasts to create ForecastedOccupancyPercent
        Map<Date, BigDecimal> rawOccupancyForecasts = tenantCrudService.findByNamedQuery(OccupancyForecast.SUM_BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                QueryParameter
                        .with(START_DATE, start)
                        .and(END_DATE, end)
                        .and(PROPERTY_ID, propertyId)
                        .parameters())
                .stream().collect(Collectors.toMap(
                        result -> (java.sql.Date) ((Object[]) result)[0],
                        result -> (BigDecimal) ((Object[]) result)[2])
                );
        final Map<Date, BigDecimal> totalAccomCapacities = getTotalAccomCapacities(start, end, propertyId);

        return rawOccupancyForecasts.keySet().stream()
                .map(occupancyDate -> {
                    if (revenueDifference.containsKey(occupancyDate) && roomsSoldDifference.containsKey(occupancyDate) && roomsSoldDifference.get(occupancyDate) > 0) {
                        var occupancyForecastDTO = new OccupancyForecastDTO();
                        occupancyForecastDTO.setOccupancyDate(DateUtil.convertJavaUtilDateToLocalDate(occupancyDate));
                        occupancyForecastDTO.setCurrencyCode(getCurrencyCode());
                        occupancyForecastDTO.setForecastedADR(divide(revenueDifference.get(occupancyDate), BigDecimal.valueOf(roomsSoldDifference.get(occupancyDate))));
                        occupancyForecastDTO.setForecastedOccupancyPercent(getForecastedOccupancyPercent(occupancyDate, totalAccomCapacities, rawOccupancyForecasts.get(occupancyDate)));
                        return occupancyForecastDTO;
                    } else {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public List<OccupancyFcstSummaryDto> getOccupancyForecastSummary(Date startDate, Date endDate) {
        final List<Object[]> rawOccupancyForecasts =
                tenantCrudService.findByNamedQuery(OccupancyForecast.SUM_BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                        QueryParameter
                                .with(START_DATE, startDate)
                                .and(END_DATE, endDate)
                                .and(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                                .parameters());
        return rawOccupancyForecasts.stream()
                .map(r -> new OccupancyFcstSummaryDto(DateUtil.formatDate((Date) r[0], SYSTEM_DATE_FORMAT),
                        (BigDecimal) r[2], (BigDecimal) r[1]))
                .collect(Collectors.toList());

    }

    private List<OccupancyForecastDTO> getOccupancyForecastInternal(String clientCode, String propertyCode,
                                                                    LocalDate startDate) {
        log.debug(">>> Fetching occupancy forecast for clientCode:{}, propertyCode:{}", clientCode, propertyCode);

        final int propertyId = getPropertyId(clientCode, propertyCode);
        final Date start = LocalDateUtils.toDate(startDate);
        final Date end = dateService.getLatestOccupancyForecastDate(propertyId);

        if (end == null || end.before(start)) {
            return List.of();
        }

        final Map<Date, BigDecimal> totalAccomCapacities = getTotalAccomCapacities(start, end, propertyId);
        final Map<Date, BigDecimal> lv0Decisions = analyticsBuilder.buildHotelMasterRoomClassBARs(start, end, propertyId);
        final Map<Date, BigDecimal> adrForMasterRoomClasses = getAverageDailyRateForMasterRoomClasses(start, end, propertyId);

        final List<Object[]> rawOccupancyForecasts = tenantCrudService.findByNamedQuery(OccupancyForecast.SUM_BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                QueryParameter
                        .with(START_DATE, start)
                        .and(END_DATE, end)
                        .and(PROPERTY_ID, propertyId)
                        .parameters());

        return rawOccupancyForecasts
                .stream()
                .map(raw -> createOccupancyForecastDTO(raw, totalAccomCapacities, lv0Decisions, adrForMasterRoomClasses))
                .collect(Collectors.toList());
    }

    private List<OccupancyForecastDTO> tryGetOccupancyForecastForLimitedDataBuild(String clientCode,
                                                                                  String propertyCode,
                                                                                  LocalDate startDate) {
        if (!pacmanConfigParamsService.getBooleanParameterValue("pacman.core.LimitedDataBuildEnabled", clientCode, propertyCode)) {
            return List.of();
        }

        log.warn(">>> Fetching occupancy forecast from limited data build for clientCode: {}, propertyCode: {}",
                clientCode, propertyCode);

        final List<LDBProjectionDaySummary> hotelLdbProjectionDaySummaries = tenantCrudService.findByNamedQuery(LDBProjection.GET_AVERAGE_ROOM_REVENUE_FOR_HOTEL,
                QueryParameter
                        .with(START_DATE, LocalDateUtils.toDate(startDate))
                        .parameters());

        if (hotelLdbProjectionDaySummaries.isEmpty()) {
            return List.of();
        }

        Date endDate = hotelLdbProjectionDaySummaries.get(hotelLdbProjectionDaySummaries.size() - 1).getOccupancyDate();
        final int propertyId = getPropertyId(clientCode, propertyCode);
        final Map<Date, BigDecimal> totalAccomCapacities = getTotalAccomCapacities(LocalDateUtils.toDate(startDate), endDate, propertyId);

        final List<LDBProjectionDaySummary> barLdbProjectionDaySummaries = tenantCrudService.findByNamedQuery(LDBProjection.GET_AVERAGE_ROOM_REVENUE_FOR_BAR_SEGMENT,
                QueryParameter
                        .with(START_DATE, LocalDateUtils.toDate(startDate))
                        .parameters());

        Map<LocalDate, LDBProjectionDaySummary> hotelLdbProjectionDaySummaryByDate = hotelLdbProjectionDaySummaries.stream()
                .collect(Collectors.toMap(daySummary -> LocalDateUtils.toJavaLocalDate(daySummary.getOccupancyDate()), Function.identity(), (prev, next) -> next));

        Map<LocalDate, LDBProjectionDaySummary> barLdbProjectionDaySummaryByDate = barLdbProjectionDaySummaries.stream()
                .collect(Collectors.toMap(daySummary -> LocalDateUtils.toJavaLocalDate(daySummary.getOccupancyDate()), Function.identity(), (prev, next) -> next));

        return createOccupancyForecastLDB(startDate, LocalDateUtils.toJavaLocalDate(endDate),
                hotelLdbProjectionDaySummaryByDate, barLdbProjectionDaySummaryByDate, totalAccomCapacities);
    }

    public List<OccupancyForecastByMarketSegmentDTO> getOccupancyForecastForPropertyByMarketSegment(final String marketSegmentCode,
                                                                                                    final String clientCode,
                                                                                                    final String propertyCode,
                                                                                                    final LocalDate startDate) {

        log.debug(">>> Fetching occupancy forecast by market segment for clientCode:{}, propertyCode:{}, marketSegment:{}", clientCode, propertyCode, marketSegmentCode);

        final int propertyId = getPropertyId(clientCode, propertyCode);
        final Date start = LocalDateUtils.toDate(startDate);
        final Date end = dateService.getLatestOccupancyForecastDate(propertyId);

        return Optional.ofNullable(marketSegmentService.findByCode(marketSegmentCode))
                .map(MktSeg::getId)
                .map(marketSegmentId -> {
                    final Map<Date, BigDecimal> totalAccomCapacities = getTotalAccomCapacities(start, end, propertyId);
                    final Map<Date, BigDecimal> msOccupancyCounts = getMSOccupancyCounts(start, end, propertyId, marketSegmentId);

                    final List<Object[]> occupancyForecasts = tenantCrudService.findByNamedQuery(OccupancyForecast.SUM_BY_OCCUPANCY_DATERANGE_PROPERTY_ID_AND_MARKET_SEGMENT_ID,
                            QueryParameter
                                    .with(START_DATE, start)
                                    .and(END_DATE, end)
                                    .and(PROPERTY_ID, propertyId)
                                    .and("marketSegmentId", marketSegmentId)
                                    .parameters());

                    return occupancyForecasts
                            .stream()
                            .map(raw -> createOccupancyForecastByMarketSegmentDTO(raw, totalAccomCapacities, msOccupancyCounts))
                            .collect(Collectors.toList());
                })
                .orElse(List.of());
    }

    public List<OccupancyForecastByRoomTypeDTO> getOccupancyForecastForPropertyByRoomType(final String roomTypeCode,
                                                                                          final String clientCode,
                                                                                          final String propertyCode,
                                                                                          final LocalDate startDate) {
        final List<OccupancyForecastByRoomTypeDTO> occupancyForecasts = getOccupancyForecastForPropertyByRoomTypeInternal(
                roomTypeCode, clientCode, propertyCode, startDate);

        return !occupancyForecasts.isEmpty()
                ? occupancyForecasts
                : tryGetOccupancyForecastForLimitedDataBuild(roomTypeCode, clientCode, propertyCode, startDate);
    }

    public List<ForecastGroupOccFcstDto> getForecastByForecastGroupByAccomClass(LocalDate startDate, LocalDate endDate) {
        return tenantCrudService.findByNamedQuery(OccupancyForecast.FORECAST_BETWEEN_BY_FORECAST_GROUP_BY_ACCOM_CLASS,
                QueryParameter.with("startDate", startDate.toString()).and("endDate", endDate.toString()).parameters());
    }

    private List<OccupancyForecastByRoomTypeDTO> getOccupancyForecastForPropertyByRoomTypeInternal(final String roomTypeCode,
                                                                                                   final String clientCode,
                                                                                                   final String propertyCode,
                                                                                                   final LocalDate startDate) {
        log.debug(">>> Fetching occupancy forecast by room type for clientCode: {}, propertyCode: {} by room type", clientCode, propertyCode);

        final int propertyId = getPropertyId(clientCode, propertyCode);
        final Date start = LocalDateUtils.toDate(startDate);
        final Date end = dateService.getLatestOccupancyForecastDate(propertyId);

        if (end == null || end.before(start)) {
            log.warn(">>> No occupancy forecast found for clientCode: {}, propertyCode: {} by room type {}, because end date '{}' is not after start date '{}'",
                    clientCode, propertyCode, roomTypeCode, end, start);
            return List.of();
        }

        final Map<LocalDate, OccupancyForecastByRoomTypeDTO> roomTypeForecasts = getOccupancyForecastForPropertyByRoomTypeInternal(clientCode, propertyCode, roomTypeCode, start, end);

        if (roomTypeForecasts.size() >= ChronoUnit.DAYS.between(startDate, LocalDateUtils.toJavaLocalDate(end))) {
            return new ArrayList<>(roomTypeForecasts.values());
        }

        final Map<LocalDate, OccupancyForecastByRoomTypeDTO> masterRoomTypeForecasts = Optional.ofNullable(tenantCrudService.findByNamedQuerySingleResult(AccomType.MASTER_ACCOMMODATION_TYPE,
                        QueryParameter.with(PROPERTY_ID, propertyId).parameters()))
                .map(AccomType.class::cast)
                .map(AccomType::getAccomTypeCode)
                .map(masterRoomTypeCode -> getOccupancyForecastForPropertyByRoomTypeInternal(clientCode, propertyCode, masterRoomTypeCode, start, end))
                .orElseGet(LinkedHashMap::new);

        final Map<LocalDate, OccupancyForecastByRoomTypeDTO> mergedForecasts = new TreeMap<>();
        mergedForecasts.putAll(masterRoomTypeForecasts);
        mergedForecasts.putAll(roomTypeForecasts);

        return new ArrayList<>(mergedForecasts.values());
    }

    private List<OccupancyForecastByRoomTypeDTO> tryGetOccupancyForecastForLimitedDataBuild(String roomTypeCode,
                                                                                            String clientCode,
                                                                                            String propertyCode,
                                                                                            LocalDate startDate) {

        if (!pacmanConfigParamsService.getBooleanParameterValue("pacman.core.LimitedDataBuildEnabled", clientCode, propertyCode)) {
            return List.of();
        }

        log.warn(">>> Fetching occupancy forecast by room type from limited data build for clientCode: {}, propertyCode: {}, roomType: {}",
                clientCode, propertyCode, roomTypeCode);

        final List<LDBProjectionDaySummary> avgRoomRevenues = tenantCrudService.findByNamedQuery(LDBProjection.GET_AVERAGE_ROOM_REVENUE_FOR_BAR_SEGMENT,
                QueryParameter
                        .with(START_DATE, LocalDateUtils.toDate(startDate))
                        .parameters());

        if (!avgRoomRevenues.isEmpty()) {
            return avgRoomRevenues.stream()
                    .map(daySummary -> createLDBOccupancyForecastByRoomTypeDTO(LocalDateUtils.toJavaLocalDate(daySummary.getOccupancyDate()),
                            roomTypeCode, daySummary.getAvgRate(), daySummary.getAvgRate()))
                    .collect(Collectors.toList());
        }
        return startDate.datesUntil(startDate.plusYears(1))
                .map(occupancyDate -> createLDBOccupancyForecastByRoomTypeDTO(occupancyDate, roomTypeCode, BigDecimal.ZERO, BigDecimal.ZERO))
                .collect(Collectors.toList());
    }

    private Map<Date, BigDecimal> getAverageDailyRateForMasterRoomClasses(final Date startDate, final Date endDate,
                                                                          final int propertyId) {

        final List<Object[]> adrForMasterClasses = tenantCrudService.findByNamedQuery(OccupancyForecast.ADR_BY_OCCUPANCY_DATERANGE_PROPERTY_ID_AND_MASTER_CLASS,
                QueryParameter
                        .with(START_DATE, startDate)
                        .and(END_DATE, endDate)
                        .and(PROPERTY_ID, propertyId)
                        .and(MASTER_CLASS, 1)
                        .parameters());

        return adrForMasterClasses
                .stream()
                .collect(Collectors.toMap(
                        raw -> (Date) raw[0],
                        raw -> (BigDecimal) raw[1]
                ));
    }

    private Map<Date, BigDecimal> getTotalAccomCapacities(final Date startDate, final Date endDate,
                                                          final int propertyId) {

        return tenantCrudService.findByNamedQuery(TotalActivity.BY_OCCUPANCY_DATERANGE_AND_PROPERTY_ID,
                        QueryParameter
                                .with(START_DATE, startDate)
                                .and(END_DATE, endDate)
                                .and(PROPERTY_ID, propertyId)
                                .parameters())
                .stream()
                .map(TotalActivity.class::cast)
                .collect(Collectors.toMap(TotalActivity::getOccupancyDate, TotalActivity::getTotalAccomCapacity));
    }

    private Map<Date, BigDecimal> getMSOccupancyCounts(final Date startDate, final Date endDate,
                                                       final int propertyId, final int mktSegId) {

        final List<Object[]> msOccupancyCounts = tenantCrudService.findByNamedQuery(MktSegAccomActivity.SUM_ROOM_SOLD_BY_PROPERTY_ID_MKT_SEG_ID_OCCUPANCY_DATE_RANGE,
                QueryParameter
                        .with(START_DATE, startDate)
                        .and(END_DATE, endDate)
                        .and(PROPERTY_ID, propertyId)
                        .and("mktSegId", mktSegId)
                        .parameters());

        return msOccupancyCounts
                .stream()
                .collect(Collectors.toMap(
                        raw -> (Date) raw[0],
                        raw -> (BigDecimal) raw[1]
                ));
    }

    public List<RevplanMarketOccupancyForecast> getRevplanMarketOccupancyForecastDtos(DatafeedRequest datafeedRequest) {
        Date startDate = datafeedRequest.getStartDate();
        Date endDate = datafeedRequest.getEndDate();
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        String query = "SELECT " +
                "CAST(SUM(f.Occupancy_NBR) AS DECIMAL(19,2)) AS Occupancy_Forecast, " +
                "ms_map.Market_Code AS Market_Code, " +
                "t1.accom_type_code AS Accom_Type_Code, " +
                "f.Occupancy_DT AS Occupancy_DT, " +
                "CAST(SUM(f.Revenue) AS DECIMAL(19,2)) AS Revenue_Forecast " +
                "FROM Occupancy_FCST f " +
                "INNER JOIN (" +
                "    SELECT " +
                "        Occupancy_DT, " +
                "        MAX(Decision_ID) AS Decision_ID " +
                "    FROM Occupancy_FCST " +
                "    WHERE Occupancy_DT BETWEEN :startDate AND :endDate " +
                "    GROUP BY Occupancy_DT" +
                ") d ON f.Occupancy_DT = d.Occupancy_DT AND f.Decision_ID = d.Decision_ID " +
                "INNER JOIN Mkt_Seg ms ON f.MKT_SEG_ID = ms.Mkt_Seg_ID " +
                "INNER JOIN (" +
                "    SELECT Market_code, Mapped_Market_Code FROM Analytical_Mkt_Seg " +
                "    GROUP BY Market_code, Mapped_Market_Code " +
                "    UNION ALL " +
                "    SELECT Mkt_Seg_Code as Market_Code, Mkt_Seg_Code as Mapped_Market_Code " +
                "    FROM Mkt_Seg " +
                "    WHERE NOT EXISTS (SELECT 1 FROM Analytical_Mkt_Seg) " +
                ") ms_map ON ms_map.Mapped_Market_Code = ms.Mkt_Seg_Code " +
                "INNER JOIN accom_type t1 ON t1.Accom_Type_ID = f.accom_type_id " +
                "WHERE f.Property_ID = :propertyId " +
                "GROUP BY f.Occupancy_DT, ms_map.Market_Code, t1.accom_type_code " +
                "ORDER BY f.Occupancy_DT, ms_map.Market_Code, t1.accom_type_code";
        List<RevplanMarketOccupancyForecast> result = tenantCrudService.findByNativeQuery(query,
                QueryParameter.with(START_DATE, startDate)
                        .and(END_DATE, endDate)
                        .and(PROPERTY_ID, propertyId)
                        .parameters(),
                datafeedRequest.getStartPosition(), datafeedRequest.getSize(), revplanMarketOccupancyForecastRowMapper());
        return result != null ? result : Collections.emptyList();
    }

    private RowMapper<RevplanMarketOccupancyForecast> revplanMarketOccupancyForecastRowMapper() {
        return row -> {
            RevplanMarketOccupancyForecast revplanMarketOccupancyForecast = new RevplanMarketOccupancyForecast();
            revplanMarketOccupancyForecast.setRoomsSold((BigDecimal) row[0]);
            revplanMarketOccupancyForecast.setMarketSegmentCode((String) row[1]);
            revplanMarketOccupancyForecast.setRoomTypeCode((String) row[2]);
            revplanMarketOccupancyForecast.setOccupancyDate((Date) row[3]);
            revplanMarketOccupancyForecast.setRoomsRevenue((BigDecimal) row[4]);


            return revplanMarketOccupancyForecast;
        };
    }

    private int getPropertyId(final String clientCode, final String propertyCode) {

        final ClientPropertyView clientPropertyView = propertyService.getClientPropertyIntegrationDto(clientCode, propertyCode);
        final int propertyId = clientPropertyView.getPropertyId();

        final WorkContextType context = new WorkContextType();
        context.setClientCode(clientPropertyView.getClientCode());
        context.setClientId(clientPropertyView.getClientId());
        context.setPropertyCode(clientPropertyView.getPropertyCode());
        context.setPropertyId(propertyId);

        PacmanWorkContextHelper.setWorkContext(context);

        return propertyId;
    }

    private Map<Date, Integer> getRoomsSoldDifference(LocalDate startDate, List<String> excludeRateCodeList) {
        var reservationRoomsSoldDifferenceByOccupancyDate = tenantCrudService.findByNamedQuery(ReservationNight.GET_ROOMS_SOLD_BY_OCCUPANCY_EXCLUDE_RATE_CODE_LIST,
                        QueryParameter.with("startDate", DateUtil.convertLocalDateToJavaUtilDate(startDate))
                                .and("rateCodeList", excludeRateCodeList)
                                .parameters())
                .stream().collect(Collectors.toMap(
                        result -> (java.sql.Date) ((Object[]) result)[0],
                        result -> (Integer) ((Object[]) result)[1])
                );
        var groupBlockRoomsSoldDifferenceByOccupancyDate = tenantCrudService.findByNamedQuery(GroupBlockDetail.GET_ROOMS_SOLD_DIFFERENCE_FROM_DATE,
                        QueryParameter.with("startDate", DateUtil.convertLocalDateToJavaUtilDate(startDate))
                                .parameters())
                .stream().collect(Collectors.toMap(
                        result -> (java.sql.Date) ((Object[]) result)[0],
                        result -> (Integer) ((Object[]) result)[1])
                );
        return Stream.concat(
                        reservationRoomsSoldDifferenceByOccupancyDate.entrySet().stream(),
                        groupBlockRoomsSoldDifferenceByOccupancyDate.entrySet().stream()
                )
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        Integer::sum
                ));
    }

    private Map<Date, BigDecimal> getRevenueDifference(LocalDate startDate, List<String> excludeRateCodeList) {

        Map<Date, BigDecimal> reservationRevenueDifferenceByOccupancyDate = tenantCrudService.findByNamedQuery(ReservationNight.GET_ROOM_REVENUE_BY_OCCUPANCY_FROM_DATE_EXCLUDE_RATE_CODE_LIST,
                        QueryParameter.with("startDate", DateUtil.convertLocalDateToJavaUtilDate(startDate))
                                .and("rateCodeList", excludeRateCodeList)
                                .parameters())
                .stream()
                .collect(Collectors.toMap(
                        result -> (java.sql.Date) ((Object[]) result)[0],
                        result -> (BigDecimal) ((Object[]) result)[1])
                );
        Map<Date, BigDecimal> groupBlockRevenueDifferenceByOccupancyDate = tenantCrudService.findByNamedQuery(GroupBlockDetail.GET_REVENUE_DIFFERENCE_FROM_DATE,
                        QueryParameter.with("startDate", DateUtil.convertLocalDateToJavaUtilDate(startDate))
                                .parameters())
                .stream().collect(Collectors.toMap(
                        result -> (java.sql.Date) ((Object[]) result)[0],
                        result -> (BigDecimal) ((Object[]) result)[1])
                );
        return Stream.concat(
                        reservationRevenueDifferenceByOccupancyDate.entrySet().stream(),
                        groupBlockRevenueDifferenceByOccupancyDate.entrySet().stream()
                )
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        BigDecimal::add
                ));
    }
}
