package com.ideas.tetris.pacman.services.informationmanager.service;

import com.ideas.infra.tetris.security.LDAPException;
import com.ideas.tetris.pacman.common.configparams.AlertConfigParamName;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.bookeddataservice.BookedDataService;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.FileMetadata;
import com.ideas.tetris.pacman.services.componentrooms.services.ComponentRoomService;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.fplos.constants.StatisticalOutlierAlertConstants;
import com.ideas.tetris.pacman.services.informationmanager.alert.service.InsufficientCompetitorExceptionService;
import com.ideas.tetris.pacman.services.informationmanager.dto.Alert;
import com.ideas.tetris.pacman.services.informationmanager.dto.AlertType;
import com.ideas.tetris.pacman.services.informationmanager.dto.ExceptionAlert;
import com.ideas.tetris.pacman.services.informationmanager.dto.ScoreRange;
import com.ideas.tetris.pacman.services.informationmanager.dto.SearchCriteriaDTO;
import com.ideas.tetris.pacman.services.informationmanager.dto.Step;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrCommentEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrExcepNotifEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrHistoryEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrHistoryTypeEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrInstanceEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrInstanceStepStateEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrStatusEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrStepsEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InfoMgrTypeEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrAlertConfigEntity;
import com.ideas.tetris.pacman.services.informationmanager.entity.InformationMgrSubLevelEntity;
import com.ideas.tetris.pacman.services.informationmanager.enums.ExceptionSubType;
import com.ideas.tetris.pacman.services.informationmanager.enums.LevelType;
import com.ideas.tetris.pacman.services.informationmanager.enums.RelationalOperator;
import com.ideas.tetris.pacman.services.limiteddatabuild.LDBService;
import com.ideas.tetris.pacman.services.limiteddatabuild.entity.LDBConfig;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.BusinessType;
import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastGroup;
import com.ideas.tetris.pacman.services.marketsegment.entity.MktSeg;
import com.ideas.tetris.pacman.services.marketsegment.service.AnalyticalMarketSegmentService;
import com.ideas.tetris.pacman.services.mktsegrecoding.service.MktSegRecodingService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.pacman.services.security.AuthorizationService;
import com.ideas.tetris.pacman.services.security.User;
import com.ideas.tetris.pacman.services.security.UserService;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.RateUnqualified;
import com.ideas.tetris.pacman.services.webrate.entity.WebrateCompetitors;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.log4j.Logger;
import org.joda.time.LocalDate;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.bookeddataservice.BookedDataService.I_AM_SATISFIED_WITH_DECISIONS;
import static com.ideas.tetris.pacman.services.bookeddataservice.BookedDataService.I_AM_SATISFIED_WITH_DECISIONS_AND_MOVE_TO_TWO_WAY;
import static com.ideas.tetris.pacman.services.bookeddataservice.BookedDataService.USE_BOOKED_DATA;
import static com.ideas.tetris.pacman.services.bookeddataservice.BookedDataService.USE_BOOKED_DATA_AND_MOVE_TO_ONE_WAY;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@Transactional
@Component
public abstract class AbstractAlertService {

    private static final Logger LOGGER = Logger.getLogger(AbstractAlertService.class.getName());
    public static final String TYPE_ID = "typeId";
    public static final String SUB_TYPE_ID = "subTypeId";
    public static final String PROPERTY_ID = "propertyId";
    public static final String CATEGORY = "category";
    public static final String RECORD_TYPE_ID = "recordTypeId";
    public static final String UNABLE_TO_GET_THE_NAME_FOR_SUBLEVEL = "unable to get the Name for Sublevel ";
    public static final String DEFAULT_PRODUCT_DETAILS = "agile.rates.base.product: ---, agile.rates.base.product.final.price: ---, LRV: ---,";
    public static final String OPTIMIZE_ICON = "<span class=\"v-icon SMALL green-color FontAwesome\">&#xf192;</span>";
    public static final String CUBE = "<span class=\"v-icon light-purple-icon FontAwesome\">&#xf1b2;</span>";
    public static final String USERS = "<span class=\"v-icon blue-icon FontAwesome\">&#xf0c0;</span>";
    public static final String LINK = "<span class=\"v-icon orange-icon FontAwesome\">&#xf0c1;</span>";
    public static final String PRODUCT_LABEL = "agile.rates.product.name";
    private static final String PMS_MIGRATION_COMPLETE_CCFG_STEP = "PMSMigrationCCFGValidation.url";
    private static final String MS_RECODING_CCFG_VALIDATION_URL = "MSRecodingCCFGValidation.url";
    private static final String PMS_MIGRATION_CCFG_VALIDATION_SUCCESS = "PMSMigrationCCFGValidation.success";
    private static final String MS_RECODING_CCFG_VALIDATION_SUCCESS = "MSRecodingCCFGValidation.success";

    @Autowired
	protected AbstractMultiPropertyCrudService multiPropertyCrudService;

    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	protected CrudService tenantCrudService;

    @Autowired
	public DateService dateService;

    @GlobalCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("globalCrudServiceBean")
	protected CrudService globalCrudService;

    @Autowired
	protected AuthorizationService authorizationService;

    @Autowired
	protected PacmanConfigParamsService configService;

    @Autowired
	public UserService userService;

    @Autowired
	public PropertyGroupService propertyGroupService;

    @Autowired
	protected PropertyService propertyService;

    @Autowired
	protected AnalyticalMarketSegmentService analyticalMarketSegmentService;

    @Autowired
	private BookedDataService bookedDataService;

    @Autowired
	private MktSegRecodingService mktSegRecodingService;

    @Autowired
	private InsufficientCompetitorExceptionService inSufficientCompetitorExceptionService;

    @Autowired
    @Qualifier("ldbService")
    private LDBService ldbService;

    public void populate(Alert alert, WorkContextType workContext, InfoMgrTypeEntity alertTypeEntity,
                         String description, String details, AlertType alertType) {
        alert.setActive(true);
        alert.setCreatedBy(getUserName(workContext.getPropertyId(), workContext.getUserId()));
        alert.setDescription(description);
        alert.setDetails(details);
        alert.setName(alertTypeEntity.getDescription());
        alert.setPropertyId(workContext.getPropertyId());
        alert.setPropertyCode(workContext.getPropertyCode());
        alert.setScore(alertTypeEntity.getBaseScore());
        alert.setType(alertType);
    }

    protected InfoMgrHistoryEntity createHistory(int typeId, InfoMgrInstanceEntity alert) {
        InfoMgrHistoryTypeEntity type = multiPropertyCrudService.find(alert.getPropertyId(), InfoMgrHistoryTypeEntity.class, typeId);
        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
        String userName = getUserName(alert.getPropertyId(), workContext.getUserId());
        return createHistory(type, alert, userName);
    }

    protected InfoMgrHistoryEntity createHistory(InfoMgrHistoryTypeEntity type, InfoMgrInstanceEntity alert, String userName) {
        InfoMgrHistoryEntity history = new InfoMgrHistoryEntity();

        history.setCreatedBy(userName);

        history.setAlertInstanceId(alert.getId());
        history.setScore(alert.getScore());
        history.setCreateDate(new Date()); // should not be necessary
        history.setType(type);
        history.setDescription(type.getName());
        history.setAlertStatus(alert.getAlertStatus());
        //when to add Details History
        if (type.getId() == Constants.ALERT_HISTORY_CREATED_ID || type.getId() == Constants.ALERT_HISTORY_SCORE_INCREASED_ID
                || type.getId() == Constants.ALERT_HISTORY_SCORE_DECREASED_ID || type.getId() == Constants.ALERT_HISTORY_SNOOZED_ID) {
            String historyDetails = alert.getDetails();
            history.setDetailsHistory(historyDetails);
        }
        //remove the Graph contents from Original details
        String historyDetailsUpdated = alert.getDetails();
        if (historyDetailsUpdated.contains(",Graph:")) {
            Integer i = historyDetailsUpdated.indexOf(",Graph:");
            String detailsOrig = historyDetailsUpdated.substring(0, i);
            alert.setDetails(detailsOrig);
        }
        return history;
    }

    protected InfoMgrHistoryEntity createHistory(int typeId, InfoMgrExcepNotifEntity notifEntity) {
        InfoMgrHistoryTypeEntity type = multiPropertyCrudService.find(notifEntity.getPropertyId(), InfoMgrHistoryTypeEntity.class, typeId);
        InformationMgrAlertConfigEntity configEntity = multiPropertyCrudService.find(notifEntity.getPropertyId(), InformationMgrAlertConfigEntity.class, notifEntity.getExceptionAlertConfigEntityId());

        return createHistory(type, notifEntity, configEntity);
    }

    protected InfoMgrHistoryEntity createHistory(InfoMgrHistoryTypeEntity type, InfoMgrExcepNotifEntity notifEntity, InformationMgrAlertConfigEntity configEntity) {
        StringBuilder buff = new StringBuilder();
        buff.append(notifEntity.getDetails());
        String alertName = notifEntity.getAlertType().getName();
        if (!(alertName.equalsIgnoreCase(AlertType.LRVExceedingUserDefinedBAR.toString()))
                || !(alertName.equalsIgnoreCase(AlertType.OOORoomsAffectingDemand.toString()))
                || !(alertName.equalsIgnoreCase(AlertType.LRVExceedingCeilingBAR.toString()))
                || !(alertName.equalsIgnoreCase(AlertType.CPCeilingOverrideBelowLRV.toString()))
                || !(alertName.equalsIgnoreCase(AlertType.CPUserOverrideBelowLRV.toString()))) {
            buff.append(",Graph:,");
            String subTypeName = notifEntity.getSubType().getName();
            buff.append("SubType:").append(subTypeName);

            String lastValue = null != notifEntity.getLastOptimizationValue() ? notifEntity.getLastOptimizationValue() : "";

            if (null != configEntity) {
                buff.append(",MetricType:").append(configEntity.getThresholdMetricType().getCode());
                if (configEntity.getExceptionSubType().getName().equalsIgnoreCase(ExceptionSubType.PRICING.getCode())) {
                    buff.append(",CurrValuePricing:").append(getRankingForRateLevel(notifEntity.getCurrentOptimizationValue(), configEntity.getPropertyId())).append(",");
                    if (notifEntity.getAlertType().getName().equals(AlertType.DecisionAsOfLastNightlyOptimization.toString())
                            || notifEntity.getAlertType().getName().equals(AlertType.DecisionAsOfLastOptimization.toString())) {
                        RateUnqualified rateByRank = findRateByRank(configEntity.getThresholdValue().intValue(), notifEntity.getPropertyId());
                        buff.append("LastValuePricing:").append(configEntity.getThresholdValue());
                        lastValue = rateByRank.getName();
                    } else {
                        buff.append("LastValuePricing:").append(getRankingForRateLevel(notifEntity.getLastOptimizationValue(), configEntity.getPropertyId()));
                    }
                }
            }
            buff.append(",LastValue:").append(lastValue);
            notifEntity.setDetails(buff.toString());
        }
        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
        String userName = getUserName(workContext.getPropertyId(), workContext.getUserId());
        return createHistory(type, (InfoMgrInstanceEntity) notifEntity, userName);
    }

    protected String getUserName(Integer propertyId, String userId) {
        String userName = userId;
        try {
            User user = multiPropertyCrudService.find(propertyId, User.class, Integer.parseInt(userId));
            if (user != null) {
                userName = user.getName();
            }
        } catch (NumberFormatException e) {
            LOGGER.debug(
                    "Here, an Integer userId was expected. A different string was passed." +
                            " This could be a normal situation. userId in context was -> " + userId
            );
        }
        return userName;
    }

    public List<InfoMgrInstanceStepStateEntity> createSteps(InfoMgrInstanceEntity alert) {
        List<InfoMgrStepsEntity> steps = getAlertSteps(alert);
        return createSteps(steps, alert);
    }

    @SuppressWarnings("unchecked")
    public List<InfoMgrInstanceStepStateEntity> createSteps(List<InfoMgrStepsEntity> steps, InfoMgrInstanceEntity alert) {
        List<InfoMgrStepsEntity> reducedSteps = getReducedSteps(alert, steps);

        List<InfoMgrInstanceStepStateEntity> stepInstances = new ArrayList<>();
        for (InfoMgrStepsEntity step : reducedSteps) {
            InfoMgrInstanceStepStateEntity instanceStep = new InfoMgrInstanceStepStateEntity();
            instanceStep.setAlert(alert);
            instanceStep.setStep(step);
            instanceStep.setActioned(false);
            stepInstances.add(instanceStep);
        }
        return stepInstances;
    }

    public List<InfoMgrStepsEntity> getReducedSteps(InfoMgrInstanceEntity alert, List<InfoMgrStepsEntity> steps) {
        List<InfoMgrStepsEntity> retSteps = new ArrayList<>();

        for (InfoMgrStepsEntity infoMgrStepsEntity : steps) {
            if (!excludeStep(alert, infoMgrStepsEntity)) {
                retSteps.add(infoMgrStepsEntity);
            }
        }

        return retSteps;
    }

    private List<InfoMgrStepsEntity> getAlertSteps(InfoMgrInstanceEntity alert) {
        List<InfoMgrStepsEntity> steps;
        if (alert.getAlertType().getAlertCategory().equalsIgnoreCase(Constants.EXCEPTION_CATEGORY)) {
            steps = (List<InfoMgrStepsEntity>) multiPropertyCrudService.findByNamedQueryForSingleProperty(alert.getPropertyId(), InfoMgrStepsEntity.BY_TYPE_AND_SUB_TYPE,
                    QueryParameter.with(TYPE_ID, alert.getAlertType().getId()).and(SUB_TYPE_ID, ((InfoMgrExcepNotifEntity) alert).getSubType().getId()).parameters());
        } else {
            steps = (List<InfoMgrStepsEntity>) multiPropertyCrudService.findByNamedQueryForSingleProperty(alert.getPropertyId(), InfoMgrStepsEntity.BY_TYPE,
                    QueryParameter.with(TYPE_ID, alert.getAlertType().getId()).parameters());
        }
        return steps;
    }

    public List<InfoMgrStepsEntity> getAlertSteps(InformationMgrAlertConfigEntity config) {
        List<InfoMgrStepsEntity> steps;
        if (config.getAlertTypeEntity().getAlertCategory().equalsIgnoreCase(Constants.EXCEPTION_CATEGORY)) {
            steps = (List<InfoMgrStepsEntity>) multiPropertyCrudService.findByNamedQueryForSingleProperty(config.getPropertyId(), InfoMgrStepsEntity.BY_TYPE_AND_SUB_TYPE,
                    QueryParameter.with(TYPE_ID, config.getAlertTypeEntity().getId()).and(SUB_TYPE_ID, config.getExceptionSubType().getId()).parameters());
        } else {
            steps = (List<InfoMgrStepsEntity>) multiPropertyCrudService.findByNamedQueryForSingleProperty(config.getPropertyId(), InfoMgrStepsEntity.BY_TYPE,
                    QueryParameter.with(TYPE_ID, config.getAlertTypeEntity().getId()).parameters());
        }
        return steps;
    }

    public void addComment(int alertId, int propertyId, String comment) {
        InfoMgrInstanceEntity alertEntity = multiPropertyCrudService.find(propertyId, InfoMgrInstanceEntity.class, alertId);
        if (alertEntity == null || alertEntity.getAlertStatus().getId().intValue() == Constants.ALERT_STATUS_RESOLVED_ID) {
            return;
        }
        if (alertEntity.getAlertStatus().getId().intValue() != Constants.ALERT_STATUS_ACTIONED_ID) {
            int alertStatusId;
            int existingAlertStatusId = alertEntity.getAlertStatus().getId().intValue();
            if (existingAlertStatusId != Constants.ALERT_STATUS_SUSPENDED_ID && existingAlertStatusId != Constants.ALERT_STATUS_SNOOZED_ID) {
                alertStatusId = Constants.ALERT_STATUS_VIEWED_ID;
            } else {
                alertStatusId = existingAlertStatusId;
            }
            InfoMgrStatusEntity status = multiPropertyCrudService.find(propertyId, InfoMgrStatusEntity.class, alertStatusId);
            alertEntity.setAlertStatus(status);
        }
        alertEntity.setLastModificationDate(new Date());
        multiPropertyCrudService.save(alertEntity);
        InfoMgrHistoryEntity history = new InfoMgrHistoryEntity();
        WorkContextType workContext = (WorkContextType) PacmanThreadLocalContextHolder.get(Constants.CONTEXT_KEY);
        history.setCreatedBy(getUserName(propertyId, workContext.getUserId()));
        history.setAlertInstanceId(alertId);
        history.setScore(alertEntity.getScore());
        history.setAlertStatus(alertEntity.getAlertStatus());
        history.setComments(new HashSet<InfoMgrCommentEntity>());
        history.setCreateDate(new Date()); // should not be necessary
        InfoMgrHistoryTypeEntity type = multiPropertyCrudService.find(propertyId, InfoMgrHistoryTypeEntity.class, Constants.ALERT_HISTORY_COMMENT_ADDED_ID);
        history.setType(type);
        history.setDescription(type.getName());
        history = multiPropertyCrudService.save(propertyId, history);
        InfoMgrCommentEntity commentEntity = new InfoMgrCommentEntity();
        commentEntity.setCommentText(comment);
        commentEntity.setAlertHistory(history);
        commentEntity = multiPropertyCrudService.save(propertyId, commentEntity);
        history.getComments().add(commentEntity);
    }

    protected Map<Integer, Property> getAuthorizedProperties() {
        Map<Integer, Property> propertiesById = new HashMap<>();

        List<Property> authorizedProperties = authorizationService.retrieveAuthorizedProperties();
        if (authorizedProperties != null) {

            for (Property property : authorizedProperties) {
                if (property.isActive()) {
                    propertiesById.put(property.getId(), property);
                }
            }
        }

        return propertiesById;
    }

    /**
     * Returns the first authorized property
     *
     * @return
     */
    protected Property getAuthorizedProperty() {
        List<Property> authorizedProperties = authorizationService.retrieveAuthorizedProperties();
        return authorizedProperties.iterator().next();
    }

    public InfoMgrTypeEntity getAlertType(String alertTypeName) {
        return getAlertType(PacmanThreadLocalContextHolder.getWorkContext().getPropertyId(), alertTypeName);
    }

    public InfoMgrTypeEntity getAlertType(Integer propertyId, String alertTypeName) {
        return (InfoMgrTypeEntity) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId, InfoMgrTypeEntity.BY_NAME, QueryParameter.with("name", alertTypeName).parameters());
    }

    /**
     * This method will populate additional attributes to the DTO for UI Use.
     *
     * @param objExceptionAlert
     * @return
     */
    public ExceptionAlert addExceptionConfigurationDetailsToAlert(ExceptionAlert objExceptionAlert) {

        InformationMgrAlertConfigEntity objExceptionConfig = multiPropertyCrudService.find(objExceptionAlert.getPropertyId(), InformationMgrAlertConfigEntity.class, objExceptionAlert.getExceptionConfigId());
        objExceptionAlert.setThresholdOperator(objExceptionConfig.getThresholdOperator());
        objExceptionAlert.setThresholdValue(objExceptionConfig.getThresholdValue());
        objExceptionAlert.setExceptionLevel(objExceptionConfig.getExceptionLevel().getName());
        objExceptionAlert.setExceptionSubLevel(getSubLevelForId(objExceptionConfig));
        objExceptionAlert.setMetricType(objExceptionConfig.getThresholdMetricType());
        if (objExceptionAlert.getType().getName().equals(AlertType.DecisionAsOfLastNightlyOptimization.getName())
                || objExceptionAlert.getType().getName().equals(AlertType.DecisionAsOfLastOptimization.getName())) {
            objExceptionAlert.setLastOptVal(objExceptionAlert.getThresholdValue().toString());
        }
        setRankingForPricingException(objExceptionAlert);
        if (isProductDetailsEnabled(objExceptionConfig.getExceptionSubType().getName())) {
            objExceptionAlert.setProduct(objExceptionConfig.getProduct());
        }
        return objExceptionAlert;
    }

    public void setRankingForPricingException(ExceptionAlert objExceptionAlert) {
        if (ExceptionSubType.PRICING.getCode().equalsIgnoreCase(objExceptionAlert.getSubType().getName())) {
            objExceptionAlert.setCurrentOptValueForPricing(getRankingForRateLevel(objExceptionAlert.getCurrentOptimizationValue(), objExceptionAlert.getPropertyId()).toString());
            if (objExceptionAlert.getType().getName().equals(AlertType.DecisionAsOfLastNightlyOptimization.getName())
                    || objExceptionAlert.getType().getName().equals(AlertType.DecisionAsOfLastOptimization.getName())) {
                Integer rankSetInThreshold = objExceptionAlert.getThresholdValue().intValue();
                objExceptionAlert.setLastOptValueForPricing(rankSetInThreshold.toString());
                RateUnqualified rateDefinedInThreshold = findRateByRank(rankSetInThreshold, objExceptionAlert.getPropertyId());
                objExceptionAlert.setLastOptVal(rateDefinedInThreshold.getName());
            } else {
                objExceptionAlert.setLastOptValueForPricing(getRankingForRateLevel(objExceptionAlert.getLastOptVal(), objExceptionAlert.getPropertyId()).toString());
            }
            objExceptionAlert.setMaxRankForPricing(getMaxRankLevelForPricing(objExceptionAlert.getPropertyId()).toString());
            objExceptionAlert.setMinRankForPricing(getMinRankLevelForPricing(objExceptionAlert.getPropertyId()).toString());
            populateHigherAndLowerLevel(objExceptionAlert);
        }
    }

    public RateUnqualified findRateByRank(Integer rank, Integer propertyId) {
        return (RateUnqualified) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId,
                RateUnqualified.FIND_BY_RANK, QueryParameter.with("rankLevel", rank).parameters());
    }

    private Object getRankingForRateLevel(String code, Integer propertyId) {
        return multiPropertyCrudService
                .findByNamedQuerySingleResultForSingleProperty(
                        propertyId,
                        RateUnqualified.RANK_BY_PROPERTY_AND_LEVEL,
                        QueryParameter.with(PROPERTY_ID, propertyId)
                                .and("name", code).parameters()
                );
    }

    public Object getMaxRankLevelForPricing(Integer propertyId) {
        return multiPropertyCrudService
                .findByNamedQuerySingleResultForSingleProperty(propertyId, RateUnqualified.GET_MAX_RANKING_LEVEL_FOR_PROPERTY,
                        QueryParameter.with(PROPERTY_ID, propertyId).parameters());
    }

    public Object getMinRankLevelForPricing(Integer propertyId) {
        return multiPropertyCrudService
                .findByNamedQuerySingleResultForSingleProperty(propertyId, RateUnqualified.GET_MIN_RANKING_LEVEL_FOR_PROPERTY,
                        QueryParameter.with(PROPERTY_ID, propertyId).parameters());
    }

    public void populateHigherAndLowerLevel(ExceptionAlert objExceptionAlert) {
        String operator = objExceptionAlert.getThresholdOperator();
        BigDecimal thresholdValue = objExceptionAlert.getThresholdValue();
        List<Integer> propertyIds = new ArrayList<>();
        propertyIds.add(objExceptionAlert.getPropertyId());
        int higherLevelValue = 0;
        int lowerLevelValue = 0;
        String higherLevelRate = "";
        String lowerLevelRate = "";
        int minRank = NumberUtils.toInt(objExceptionAlert.getMinRankForPricing());
        int maxRank = NumberUtils.toInt(objExceptionAlert.getMaxRankForPricing());

        Map<Integer, String> rateCodeByRank = new HashMap<>();

        List<Object[]> resultList = multiPropertyCrudService
                .findByNamedQueryUnionAcrossProperties(propertyIds,
                        RateUnqualified.ALL_ORDER_BY_RANK, QueryParameter
                                .with(PROPERTY_ID, propertyIds).parameters()
                );

        if (CollectionUtils.isNotEmpty(resultList)) {
            for (Object[] result : resultList) {
                rateCodeByRank.put((Integer) result[0], (String) result[1]);
            }
        }

        if (operator.equals(RelationalOperator.NOT_EQUAL.getCode())) {
            higherLevelValue = NumberUtils.toInt(objExceptionAlert.getLastOptValueForPricing());
            lowerLevelValue = NumberUtils.toInt(objExceptionAlert.getLastOptValueForPricing()) + thresholdValue.intValue();
            higherLevelRate = extractHigherLevelRate(higherLevelValue, minRank, rateCodeByRank);
            lowerLevelRate = extractLowerLevelRate(lowerLevelValue, maxRank, rateCodeByRank);

            objExceptionAlert.setHigherLimitRateCode(higherLevelRate);
            objExceptionAlert.setLowerLimitRateCode(lowerLevelRate);
        }

        if (operator.equals(RelationalOperator.GREATER_OR_EQUAL.getCode())) {
            higherLevelValue = NumberUtils.toInt(objExceptionAlert.getLastOptValueForPricing()) - thresholdValue.intValue();
            higherLevelRate = extractHigherLevelRate(higherLevelValue, minRank, rateCodeByRank);
            objExceptionAlert.setHigherLimitRateCode(higherLevelRate);
            objExceptionAlert.setLowerLimitRateCode(rateCodeByRank.get(objExceptionAlert.getLastOptValueForPricing()));
        }

        if (operator.equals(RelationalOperator.LESS_THAN_OR_EQUAL.getCode())) {
            lowerLevelValue = NumberUtils.toInt(objExceptionAlert.getLastOptValueForPricing()) + thresholdValue.intValue();
            if (lowerLevelValue > maxRank) {
                lowerLevelRate = rateCodeByRank.get(maxRank);
            } else {
                lowerLevelRate = rateCodeByRank.get(lowerLevelValue);
                if (null == lowerLevelRate) {
                    lowerLevelRate = rateCodeByRank.get(NumberUtils.toInt(objExceptionAlert.getLastOptValueForPricing()) - thresholdValue.intValue());
                }
                if (StringUtils.equalsIgnoreCase(lowerLevelRate, "null")) {
                    lowerLevelRate = rateCodeByRank.get(maxRank);
                }
            }
            objExceptionAlert.setLowerLimitRateCode(lowerLevelRate);
            objExceptionAlert.setHigherLimitRateCode(rateCodeByRank.get(objExceptionAlert.getLastOptValueForPricing()));
        }
    }

    protected Integer getNewCategoryCount(String category) {
        return getCount(InfoMgrInstanceEntity.COUNT_NEW_ALERTS, category);
    }

    protected Integer getOpenCategoryCount(String category) {
        return getCount(InfoMgrInstanceEntity.COUNT_BY_PROPERTY_ID_AND_TYPE, category);
    }

    @SuppressWarnings("unchecked")
	public
    Integer getCount(String query, String category) {
        Map<Integer, Property> propertiesById = getAuthorizedProperties();
        List<Integer> propertyIds = new ArrayList<>(propertiesById.keySet());

        Long totalCount = 0l;
        List<Long> allPropertyCounts = multiPropertyCrudService.findByNamedQuerySingleResult(propertyIds, query, QueryParameter.with(PROPERTY_ID, propertyIds).and(CATEGORY, category).parameters());
        if (allPropertyCounts != null) {

            for (Long propertyCount : allPropertyCounts) {
                totalCount += propertyCount;
            }
        }

        return totalCount.intValue();
    }

    public List<Step> getSteps(InfoMgrInstanceEntity infoMgrInstanceEntity) {
        List<Step> steps = new ArrayList<>();

        if (infoMgrInstanceEntity.getInfoMgrInstanceStepStateEntities() != null) {
            // Sort the InfoMgrInstanceStepStateEntity by ordinal - couldn't do it in database query due to order by limitation
            infoMgrInstanceEntity.getInfoMgrInstanceStepStateEntities().sort(Comparator.comparing(stepEntity -> stepEntity.getStep().getOrdinal()));

            int index = 1;
            for (InfoMgrInstanceStepStateEntity instanceStep : infoMgrInstanceEntity.getInfoMgrInstanceStepStateEntities()) {
                if (!excludeStep(instanceStep.getAlert(), instanceStep.getStep())) {
                    steps.add(new Step(instanceStep, index++, instanceStep.getStep().getStepType(), instanceStep.getStep().getOrdinal()));
                }
            }
        }
        return steps;
    }

    @SuppressWarnings("unchecked")
    public List<Step> getSteps(Integer id, Integer propertyId) {
        List<Step> steps = new ArrayList<>();
        List<InfoMgrInstanceStepStateEntity> entities = (List<InfoMgrInstanceStepStateEntity>) multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, InfoMgrInstanceStepStateEntity.BY_ALERT_INSTANCE,
                QueryParameter.with("instanceId", id).parameters());
        int index = 1;
        for (InfoMgrInstanceStepStateEntity instanceStep : entities) {
            InfoMgrInstanceEntity alert = instanceStep.getAlert();
            InfoMgrStepsEntity instanceStepEntity = instanceStep.getStep();
            if (!excludeStep(alert, instanceStepEntity)) {
                if (!shouldAddProductInStepURL(alert)) {
                    steps.add(new Step(instanceStep, index++, instanceStepEntity.getStepType(), instanceStepEntity.getOrdinal()));
                } else {
                    steps.add(new Step(instanceStep, index++, instanceStepEntity.getStepType(), instanceStepEntity.getOrdinal(), getProductIdFromConfig(alert, propertyId)));
                }
            }
        }
        return steps;
    }

    public String getPropertyCode(Integer propertyId) {
        Property property = globalCrudService.find(Property.class, propertyId);
        if (property != null) {
            return property.getCode();
        } else {
            return null;
        }
    }

    public void setAuthorizationService(AuthorizationService authorizationService) {
        this.authorizationService = authorizationService;
    }

    public boolean isAlertCreationEnabled() {
        return configService.getParameterValue(AlertConfigParamName.ALERTS_ENABLED).equals(true);
    }

    public boolean isUserExceptionCreationEnabled() {
        return configService.getParameterValue(AlertConfigParamName.USER_EXCEPTIONS_ENABLED);
    }

    public void setMultiPropertyCrudService(AbstractMultiPropertyCrudService multiPropertyCrudService) {
        this.multiPropertyCrudService = multiPropertyCrudService;
    }

    public void setGlobalCrudService(CrudService globalCrudService) {
        this.globalCrudService = globalCrudService;
    }


    public void setPropertyService(PropertyService propertyService) {
        this.propertyService = propertyService;
    }


    private static final String BASE_QUERY = "select imi.* from info_mgr_instance imi inner join Info_Mgr_Type imt on imi.Info_Mgr_Type_ID = imt.Info_Mgr_Type_ID where imt.Alert_Category = :category";
    private static final String WITH_PROPERTIES = " and imi.property_id in (:propertyId)";
    private static final String WITH_EXCEPTION_TYPES = " and imi.info_mgr_type_Id in (:exceptionTypes)";
    private static final String WITH_EXCEPTION_STATUS = " and imi.info_mgr_status_Id in (:exceptionStatus)";
    private static final String WITH_EXCEPTION_NOT_RESOLVED_NOT_SUSPENDED = " and imi.info_mgr_status_Id not in (:exceptionStatus)";
    private static final String WITH_EXCEPTION_DATES = " and imi.Occupancy_Date >= :startDate and imi.Occupancy_Date <= :endDate";
    private static final String WITH_CREATED_DATES = " and CAST(imi.Created_Date AS DATE) >= :createdStartDate and CAST(imi.Created_Date AS DATE) <= :createdEndDate";
    private static final String WITH_STATUS = " and imi.status_id in (:statusIds)";
    private static final String ORDER_BY = " order by imi.Score desc , imi.Occupancy_Date asc";

    public List<InfoMgrExcepNotifEntity> searchWithCriteria(SearchCriteriaDTO searchCriteria, String category) {
        List<InfoMgrExcepNotifEntity> listFoundExceptionsRaised = null;
        Map<Integer, Property> validPropertyIdsMap = extractValidPropertyIdsMap(searchCriteria);
        List<Integer> validPropertyIds = new ArrayList<>(validPropertyIdsMap.keySet());

        if (null != searchCriteria) {

            StringBuilder queryStr = createSearchQuery(searchCriteria);
            List<Integer> propertyIds = searchCriteria.getPropertyIds();
            if (propertyIds != null && !propertyIds.isEmpty()) {
                validPropertyIds = propertyIds;
            }
            searchCriteria.setPropertyIds(validPropertyIds);

            Map<String, Object> parameters = new HashMap<>();
            parameters.put(CATEGORY, category);

            //again either exceptionType or Notification Type is populated
            populateExceptionTypesParameter(searchCriteria, parameters);

            parameters.put("exceptionStatus", populateExceptionStatusParameter(searchCriteria));
            if (null != searchCriteria.getStartDate() && null != searchCriteria.getEndDate()) {
                parameters.put("startDate", new SimpleDateFormat("yyyy-MM-dd").format(searchCriteria.getStartDate().getTime()));
                parameters.put("endDate", new SimpleDateFormat("yyyy-MM-dd").format(searchCriteria.getEndDate().getTime()));
            }

            if (null != searchCriteria.getCreationStartDate() && null != searchCriteria.getCreationEndDate()) {
                parameters.put("createdStartDate", new SimpleDateFormat("yyyy-MM-dd").format(searchCriteria.getCreationStartDate().getTime()));
                parameters.put("createdEndDate", new SimpleDateFormat("yyyy-MM-dd").format(searchCriteria.getCreationEndDate().getTime()));
            }

            parameters.put(PROPERTY_ID, searchCriteria.getPropertyIds());

            parameters.put("statusIds", populateStatusIdsParameter(searchCriteria));


            listFoundExceptionsRaised = multiPropertyCrudService.findByNativeQueryUnionAcrossProperties(validPropertyIds, queryStr.toString(), parameters, InfoMgrExcepNotifEntity.class);

            listFoundExceptionsRaised.removeIf(infoMgrExcepNotifEntity -> infoMgrExcepNotifEntity.getAlertType().getName().equals(AlertType.InsufficientCompetitorRatesForCompetitiveMarket.toString())
                    && !inSufficientCompetitorExceptionService.isInsufficientCompetitorCMPExceptionEnabled());

            List<Integer> dows = searchCriteria.getDows();
            if (CollectionUtils.isNotEmpty(dows)) {
                listFoundExceptionsRaised = listFoundExceptionsRaised.stream().filter(infoMgrExcepNotifEntity -> infoMgrExcepNotifEntity.getOccupancyDate() != null
                        && dows.contains(DayOfWeek.getDayOfWeek(dateService.getDayofWeek(infoMgrExcepNotifEntity.getOccupancyDate())).getCalendarDayOfWeek())).collect(Collectors.toList());
            }

        } else {
            //initial load of the screen sends the Search criteria object null
            //fetching all records except the Resolved for passed category
            searchCriteria = new SearchCriteriaDTO();
            searchCriteria.setPropertyIds(validPropertyIds);
            Map<String, Object> parameters = new HashMap<>();
            parameters.put(PROPERTY_ID, getAuthorizedProperties());
            parameters.put(CATEGORY, category);
            listFoundExceptionsRaised = multiPropertyCrudService.findByNamedQueryUnionAcrossProperties(validPropertyIds, InfoMgrInstanceEntity.BY_TYPE_AND_PROPERTY_IDS, parameters);
        }

        Map<Property, TimeZone> propertyIdTimeZoneMap = new HashMap<>();

        listFoundExceptionsRaised.forEach(objEntity -> {
            final Property property = validPropertyIdsMap.get(objEntity.getPropertyId());
            objEntity.setPropertyCode(property.getCode());
            objEntity.setPropertyName(property.getName());
            objEntity.setPropertyDisplayLabelField(property.getDisplayLabelField());
            updateTimeAsPerPropertyTimeZone(objEntity, propertyIdTimeZoneMap.computeIfAbsent(property, this::getPropertyTimeZone));
        });
        return listFoundExceptionsRaised;
    }

    private Map<Integer, TimeZone> getPropertyTimeZoneMap(List<Integer> propertyIdList, Map<Integer, Property> validPropertyIdsMap) {
        Map<Integer, TimeZone> propertyIdTimeZoneMap = new HashMap<>();
        propertyIdList.forEach(propertyId -> {
            Property objProperty = validPropertyIdsMap.get(propertyId);
            propertyIdTimeZoneMap.put(objProperty.getId(), getPropertyTimeZone(objProperty));
        });
        return propertyIdTimeZoneMap;
    }

    /**
     * This method validates selected property id's against authorised properties for logged in user
     *
     * @return
     */
    protected Map<Integer, Property> validatePropertyIds(SearchCriteriaDTO searchCriteria) {
        List<Integer> selectedPropertyIds;
        if (null != searchCriteria && null != searchCriteria.getPropertyIds() && !searchCriteria.getPropertyIds().isEmpty()) {
            selectedPropertyIds = searchCriteria.getPropertyIds();
        } else {
            selectedPropertyIds = propertyGroupService.getPropertyContextAsList();
        }

        return filterPropertyIds(selectedPropertyIds);
    }

    protected Map<Integer, Property> filterPropertyIds(List<Integer> selectedPropertyIds) {
        List<Property> properties = propertyService.getPropertiesWithDisplayLabelFieldByIds(selectedPropertyIds);
        return properties.stream().collect(Collectors.toMap(Property::getId, property -> property));
    }

    private static final String DATE_FORMAT = "EEE dd-MMM-yyyy HH:mm:ss";

    /**
     * This method updates the Time as per Property Time Zone.
     *
     * @param objEntity instance of InfoMgrExcepNotifEntity
     * @param pTimeZone Timezone of property
     */
    private void updateTimeAsPerPropertyTimeZone(InfoMgrExcepNotifEntity objEntity, TimeZone pTimeZone) {
        try {
            DateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT);
            Date dateCreated = dateFormat.parse(dateService.formatDate(objEntity.getCreateDate(), true, pTimeZone));
            Date dateLastModified = dateFormat.parse(dateService.formatDate(objEntity.getLastModificationDate(), true, pTimeZone));

            boolean areWeDaylightSavingsNow = Calendar.getInstance().getTimeZone().inDaylightTime(dateCreated);

            objEntity.setCreateDate(dateCreated);
            objEntity.setLastModificationDate(dateLastModified);
            objEntity.setPropertyTimeZone(pTimeZone.getDisplayName(areWeDaylightSavingsNow, 0));
        } catch (ParseException e) {
            LOGGER.error("Unable to parse created Date and Last modification date as per Property TimeZone.", e);
        }
    }

    public void updateTimeAsPerPropertyTimeZone(InfoMgrInstanceEntity objEntity) {
        try {
            Property objProperty = globalCrudService.find(Property.class, objEntity.getPropertyId());
            TimeZone pTimeZone = getPropertyTimeZone(objProperty);
            DateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT);

            Date dateCreated = dateFormat.parse(dateService.formatDate(objEntity.getCreateDate(), true, pTimeZone));
            Date dateLastModified = dateFormat.parse(dateService.formatDate(objEntity.getLastModificationDate(), true, pTimeZone));

            boolean areWeDaylightSavingsNow = Calendar.getInstance().getTimeZone().inDaylightTime(dateCreated);

            objEntity.setCreateDate(dateCreated);
            objEntity.setLastModificationDate(dateLastModified);
            objEntity.setPropertyTimeZone(pTimeZone.getDisplayName(areWeDaylightSavingsNow, 0));
        } catch (ParseException e) {
            LOGGER.error("Unable to parse created Date and Last modification date as per Property TimeZone.", e);
        }
    }


    public void clearStepActionedState(Integer propertyId, Integer instanceId) {
        try {
            @SuppressWarnings("unchecked")
            List<InfoMgrInstanceStepStateEntity> entities = (List<InfoMgrInstanceStepStateEntity>) multiPropertyCrudService.findByNamedQueryForSingleProperty(propertyId, InfoMgrInstanceStepStateEntity.BY_ALERT_INSTANCE,
                    QueryParameter.with("instanceId", instanceId).parameters());

            for (InfoMgrInstanceStepStateEntity stepState : entities) {
                if (stepState.getActioned()) {
                    stepState.setActioned(false);
                    multiPropertyCrudService.save(propertyId, stepState);
                }
            }
        } catch (Exception e) {
            LOGGER.error("Unable to clear the Step States when score got increased for Notification instance. Property - " + propertyId, e);
        }
    }

    /**
     * This method returns the Stage of the Property
     *
     * @return
     */
    public String getPropertyStage() {
        Property property = propertyService.getPropertyById(PacmanWorkContextHelper.getPropertyId());
        Stage stage = property.getStage();

        if (stage != null) {
            return stage.getCode();
        }

        return null;
    }

    private static final String DEFAULT_UTC_TIME_ZONE = "UTC";

    /*
     * To get property time zone.
     */
    public TimeZone getPropertyTimeZone(Property objProperty) {
        return getPropertyTimeZone(objProperty.getClient().getCode(), objProperty.getCode());
    }

    public TimeZone getPropertyTimeZone(String clientCode, String propertyCode) {
        StringBuilder nodeName = getNodeName(clientCode, propertyCode);
        String parameterValue = configService.getParameterValue(nodeName.toString(),
                IntegrationConfigParamName.CORE_PROPERTY_TIME_ZONE);
        TimeZone pTimezone = TimeZone.getTimeZone(null != parameterValue ? parameterValue : DEFAULT_UTC_TIME_ZONE);
        LOGGER.debug("getPropertyTimeZone() - parameterValue: " + parameterValue + ", returning: " + pTimezone);
        return pTimezone;
    }

    public StringBuilder getNodeName(String clientCode, String propertyCode) {
        StringBuilder nodeName = new StringBuilder();
        nodeName.append(Constants.CONFIG_PARAMS_NODE_PREFIX).append(".")
                .append(clientCode).append(".")
                .append(propertyCode);
        return nodeName;
    }

    public static final Integer T2SNAP_RECORD_ID = 3;

    /**
     * This method returns the Caught up date for the Property
     *
     * @param propertyId
     * @return
     */
    public Date getCaughtUpDateForProperty(Integer propertyId) {
        FileMetadata fileMetadata = (FileMetadata) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId, FileMetadata.BY_RECORD_TYPE_AND_PROPERTY_ISBDE_ORDER_BY_SNAPSHOTDT_DESC,
                QueryParameter.with(PROPERTY_ID, propertyId)
                        .and(RECORD_TYPE_ID, T2SNAP_RECORD_ID).parameters()
        );
        return (null != fileMetadata ? DateUtil.mergeSqlDateAndTime(fileMetadata.getSnapshotDt(), fileMetadata.getSnapshotTm()).getTime() : dateService.getCurrentDate());
    }

    /**
     * This method return the Business date for the property.
     *
     * @param propertyId
     * @return
     */
    public Date getBusinessDateForProperty(Integer propertyId) {
        FileMetadata fileMetadata = (FileMetadata) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(
                propertyId,
                FileMetadata.BY_RECORD_TYPE_AND_PROPERTY_AND_STATUS_ORDER_BY_SNAPSHOTDT_DESC,
                QueryParameter.with(PROPERTY_ID, propertyId)
                        .and(RECORD_TYPE_ID, T2SNAP_RECORD_ID).and("processStatus", 13).parameters()
        );

        if (null != fileMetadata && fileMetadata.getBde() == 1) {
            Calendar c = DateUtil.mergeSqlDateAndTime(fileMetadata.getSnapshotDt(), fileMetadata.getSnapshotTm());
            c.add(Calendar.DATE, -1);
            return c.getTime();
        } else if (null != fileMetadata && fileMetadata.getBde() == 0) {
            Calendar c = DateUtil.mergeSqlDateAndTime(fileMetadata.getSnapshotDt(), fileMetadata.getSnapshotTm());
            return c.getTime();
        } else {
            fileMetadata = (FileMetadata) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(
                    propertyId,
                    FileMetadata.BY_RECORD_TYPE_AND_PROPERTY_AND_STATUS_ORDER_BY_SNAPSHOTDT_DESC,
                    QueryParameter.with(PROPERTY_ID, propertyId)
                            .and(RECORD_TYPE_ID, T2SNAP_RECORD_ID).and("processStatus", 13).parameters()
            );
            return (null != fileMetadata ? DateUtil.mergeSqlDateAndTime(fileMetadata.getSnapshotDt(), fileMetadata.getSnapshotTm()).getTime() : dateService.getCurrentDate());
        }
    }

    public void setConfigService(PacmanConfigParamsService configService) {
        this.configService = configService;
    }

    public void setDateService(DateService dateService) {
        this.dateService = dateService;
    }

    /**
     * This method populates the Sub level as per selected Level
     * Currently we are storing the Sublevel as id for respective selected level. We have to fetch actual name/code for sublevel id.
     *
     * @param config
     * @return
     */
    public String getSubLevelForId(InformationMgrAlertConfigEntity config) {
        String subLevel = null;

        try {
            if (config.getExceptionLevel().getName().equalsIgnoreCase(LevelType.ROOM_CLASS.getCode())) {

                subLevel = extractSubLevelForIdByRoomClass(config, subLevel);

            } else if (config.getExceptionLevel().getName().equalsIgnoreCase(LevelType.ROOM_TYPE.getCode())) {

                subLevel = extractSubLevelForIdByRoomType(config, subLevel);

            } else if (config.getExceptionLevel().getName().equalsIgnoreCase(LevelType.BUSINESS_TYPE.getCode())) {

                BusinessType objBusinessType = multiPropertyCrudService.find(config.getPropertyId(), BusinessType.class, config.getExceptionSubLevel());
                subLevel = objBusinessType.getName();

            } else if (config.getExceptionLevel().getName().equalsIgnoreCase(LevelType.FORECAST_GROUP.getCode())) {

                ForecastGroup objForecastGroup = multiPropertyCrudService.find(config.getPropertyId(), ForecastGroup.class, config.getExceptionSubLevel());
                subLevel = objForecastGroup.getName();
            } else if (config.getExceptionLevel().getName().equalsIgnoreCase(LevelType.MARKET_SEGMENT.getCode())) {
                MktSeg objMktSeg = multiPropertyCrudService.find(config.getPropertyId(), MktSeg.class, config.getExceptionSubLevel());
                subLevel = objMktSeg.getName();
            } else if (config.getExceptionLevel().getName().equalsIgnoreCase(LevelType.PROPERTY_BUSINESS_VIEW.getCode())) {

                BusinessGroup objBusinessGroup = multiPropertyCrudService.find(config.getPropertyId(), BusinessGroup.class, config.getExceptionSubLevel());
                subLevel = objBusinessGroup.getName();
            } else if (config.getExceptionLevel().getName().equalsIgnoreCase(LevelType.COMPETITOR_SELECT.getCode())) {

                WebrateCompetitors objWebrateCompetitors = multiPropertyCrudService.find(config.getPropertyId(), WebrateCompetitors.class, config.getExceptionSubLevel());
                subLevel = objWebrateCompetitors.getWebrateCompetitorsName();
            }

        } catch (Exception e){
            LOGGER.warn("Error occurred while fetching the sub Level for Config Id :"+config.getId());
        }
        return subLevel;
    }

    /**
     * This method populates the Sub level as per selected Level
     * Currently we are storing the Sublevel as id for respective selected level. We have to fetch actual name/code for sublevel id.
     *
     * @param config
     * @return
     */
    public String getSubLevelCodeForId(InformationMgrAlertConfigEntity config) {
        String subLevel = null;
        if (config.getExceptionLevel().getName().equalsIgnoreCase(LevelType.ROOM_CLASS.getCode())) {
            subLevel = extractSubLevelCodeForIdByRoomClass(config, subLevel);
        } else if (config.getExceptionLevel().getName().equalsIgnoreCase(LevelType.ROOM_TYPE.getCode())) {
            subLevel = extractSubLevelCodeForIdByRoomType(config, subLevel);
        } else if (config.getExceptionLevel().getName().equalsIgnoreCase(LevelType.BUSINESS_TYPE.getCode())) {
            subLevel = extractSubLevelCodeForIdByExceptionLevelBusinessType(config, subLevel);
        } else if (config.getExceptionLevel().getName().equalsIgnoreCase(LevelType.FORECAST_GROUP.getCode())) {
            subLevel = extractSubLevelCodeForIdByExceptionLevelForecastGroup(config, subLevel);
        } else if (config.getExceptionLevel().getName().equalsIgnoreCase(LevelType.MARKET_SEGMENT.getCode())) {
            subLevel = extractSubLevelCodeForIdByExceptionLevelMarketSegment(config, subLevel);
        } else if (config.getExceptionLevel().getName().equalsIgnoreCase(LevelType.PROPERTY_BUSINESS_VIEW.getCode())) {
            subLevel = extractSubLevelCodeForIdByExceptionLevelPropertyBusinessView(config, subLevel);
        } else if (config.getExceptionLevel().getName().equalsIgnoreCase(LevelType.COMPETITOR_SELECT.getCode())) {
            subLevel = extractSubLevelCodeForIdByExceptionLevelCompetitorSelect(config, subLevel);
        } else if (config.getExceptionLevel().getName().equalsIgnoreCase(LevelType.PROPERTY.getCode())) {
            subLevel = "PropertySubLevel";
        }
        return subLevel;
    }

    public void applyPermissionsToSteps(List<Step> steps, Set<String> pages) {
        try {
            if (null != pages && !pages.contains("-666") && !pages.isEmpty()) {
                for (Step objStep : steps) {
                    if (null != objStep.getUrl()) {
                        String[] arr = objStep.getUrl().split("/");
                        if (null != arr[4]) {
                            String[] part = arr[4].split("\\?");
                            if (null != part[0]) {
                                if (!pages.contains(part[0].trim())) {
                                    objStep.setHasPermission(false);
                                    if (("pricing-management".equalsIgnoreCase(part[0].trim()) && pages.contains("pricing")) || "room-type-recoding".equalsIgnoreCase(part[0].trim())) {
                                        objStep.setHasPermission(true);
                                    }
                                }
                            } else {
                                objStep.setHasPermission(true);
                            }
                        }
                    } else {
                        //step without URL
                        LOGGER.debug("Url not present." + objStep.getStepId());
                    }
                }
            } else if (null != pages && pages.contains("-666")) {
                LOGGER.debug("All permissions present");
            } else {
                for (Step objStep : steps) {
                    objStep.setHasPermission(false);
                }
            }
        } catch (Exception e) {
            LOGGER.warn("unable to get the permission pages for user and property", e);
        }
    }

    /**
     * @param propertyIds
     * @return
     */
    public Map<Integer, Set<String>> getPermissionPerProperty(List<Integer> propertyIds) {
        if (null == propertyIds) {
            propertyIds = new ArrayList<>(getAuthorizedProperties().keySet());
        }
        Map<Integer, Set<String>> pagesPerProperty = new HashMap<>();
        try {
            for (Integer propertyId : propertyIds) {
                Set<String> pages = userService.getAuthorizedPagesForProperty(PacmanWorkContextHelper.getWorkContext().getUserId(), String.valueOf(propertyId));
                pagesPerProperty.put(propertyId, pages);
            }
        } catch (LDAPException e) {
            LOGGER.warn("unable to get the permission pages for user and property", e);
        }
        return pagesPerProperty;
    }

    /**
     * @param objExceptionAlert
     * @return
     */
    public ExceptionAlert getExtendedDetailsForExceptionAlertInstance(ExceptionAlert objExceptionAlert) {
        InfoMgrExcepNotifEntity infoMgrExcepNotifEntity = getInfoMgrExcepNotifEntity(objExceptionAlert.getPropertyId(), objExceptionAlert.getAlertId());
        setOptimizationValueForExceptionAlertInstance(infoMgrExcepNotifEntity, objExceptionAlert);
        Map<Integer, Set<String>> permissionPerProperty = getPermissionPerProperty(Arrays.asList(objExceptionAlert.getPropertyId()));
        objExceptionAlert = addExceptionConfigurationDetailsToAlert(objExceptionAlert);
        objExceptionAlert.setSteps(getSteps(objExceptionAlert.getAlertId(), objExceptionAlert.getPropertyId()));
        applyPermissionsToSteps(objExceptionAlert.getSteps(), permissionPerProperty.get(objExceptionAlert.getPropertyId()));
        setDetailsMetaDataForExceptionAlertInstance(infoMgrExcepNotifEntity, objExceptionAlert);
        setExceptionDetails(objExceptionAlert);
        return objExceptionAlert;
    }

    /**
     * @param objExceptionAlerts list of ExceptionAlerts
     * @return
     */
    public List<ExceptionAlert> getExtendedDetailsForExceptionAlertsList(List<ExceptionAlert> objExceptionAlerts) {
        Map<Integer, List<ExceptionAlert>> exceptionAlertsByPropertyID = objExceptionAlerts.stream().collect(Collectors.groupingBy(ExceptionAlert::getPropertyId));
        Map<Integer, Set<String>> permissionPerProperty = getPermissionPerProperty(new ArrayList<>(exceptionAlertsByPropertyID.keySet()));

        exceptionAlertsByPropertyID.keySet().forEach(propertyId -> {
            List<ExceptionAlert> exceptionAlertsList = exceptionAlertsByPropertyID.get(propertyId);
            Map<Integer, InfoMgrExcepNotifEntity> excepNotifEntitiesByIdMap = getInfoMgrExcepNotifEntitiesMap(exceptionAlertsList, propertyId);
            exceptionAlertsList.forEach(objExceptionAlert -> objExceptionAlert.setSteps(
                    getStepsWithPermissions(permissionPerProperty.get(propertyId), excepNotifEntitiesByIdMap.get(objExceptionAlert.getAlertId()))));
        });
        return objExceptionAlerts;
    }

    private List<Step> getStepsWithPermissions(Set<String> permissions, InfoMgrExcepNotifEntity infoMgrExcepNotifEntity) {
        List<Step> steps = getSteps(infoMgrExcepNotifEntity);
        applyPermissionsToSteps(steps, permissions);
        return steps;
    }

    private Map<Integer, InfoMgrExcepNotifEntity> getInfoMgrExcepNotifEntitiesMap(List<ExceptionAlert> exceptionAlertsList, Integer propertyId) {
        List<Integer> alertIds = exceptionAlertsList.stream().map(ExceptionAlert::getAlertId).collect(Collectors.toList());
        List<InfoMgrExcepNotifEntity> excepNotifEntityList = multiPropertyCrudService
                .findByNamedQueryForSingleProperty(propertyId, InfoMgrInstanceEntity.FIND_BY_ID_LIST, QueryParameter.with("ids", alertIds).parameters());
        return excepNotifEntityList.stream().collect(Collectors.toMap(InfoMgrExcepNotifEntity::getId, e -> e));
    }

    private void setExceptionDetails(ExceptionAlert objExceptionAlert) {
        if (objExceptionAlert.getName().equalsIgnoreCase(AlertType.StraightLineAvailabilityEx.getName())) {
            objExceptionAlert.setDetails("sla.text");
        }
        if (objExceptionAlert.getName().equalsIgnoreCase(AlertType.InsufficientCompetitorRatesForCompetitiveMarket.getName())) {
            objExceptionAlert.setDetails("insufficient.competitor.rates.competitive.market.position.details");
        }
    }

    private InfoMgrExcepNotifEntity getInfoMgrExcepNotifEntity(Integer propertyId, Integer alertId) {
        return multiPropertyCrudService.find(propertyId, InfoMgrExcepNotifEntity.class, alertId);
    }

    public void setDetailsMetaDataForExceptionAlertInstance(InfoMgrExcepNotifEntity infoMgrExcepNotifEntity, ExceptionAlert exceptionAlert) {
        String alertName = infoMgrExcepNotifEntity.getAlertType().getName();
        if (alertName.equalsIgnoreCase(AlertType.LRVExceedingUserDefinedBAR.toString()) || alertName.equalsIgnoreCase(AlertType.LRVExceedingCeilingBAR.toString()) || alertName.equalsIgnoreCase(AlertType.CPCeilingOverrideBelowLRV.toString()) || alertName.equalsIgnoreCase(AlertType.CPUserOverrideBelowLRV.toString())) {
            setDetailsByLOS(infoMgrExcepNotifEntity, exceptionAlert);
        } else {
            String details = infoMgrExcepNotifEntity.getDetails();

            if (isProductDetailsEnabled(exceptionAlert.getSubType().getName())) {
                details = addDetailsForPricingAlert(exceptionAlert, details);
            } else if ((isPricingNotfication(exceptionAlert.getSubType().getName())
                    || isCompetitiveNotification(exceptionAlert.getSubType().getName())) && details.contains(PRODUCT_LABEL)) {
                details = details.substring(0, details.indexOf(PRODUCT_LABEL));
            }

            if (infoMgrExcepNotifEntity.getAlertType().isStaticThreshold() &&
                    ExceptionSubType.PRICING.getCode().equalsIgnoreCase(exceptionAlert.getSubType().getName())
                    && alertTypeNotDecisionAsOf(alertName)) {
                details = details.trim().substring(0, details.lastIndexOf(' '));
                details += " " + exceptionAlert.getLastOptVal();
            }
            exceptionAlert.setDetailsMetadata(exceptionAlert.parseAlertDetails(details));
        }
    }

    private String addDetailsForPricingAlert(ExceptionAlert exceptionAlert, String details) {
        boolean isIndependentProductsEnabled = isIndependentProductsEnabled();
        Product product = exceptionAlert.getProduct();
        String productIcon = getProductIcon(product, isIndependentProductsEnabled);
        String productName = ": <span>" + product.getName() + "</span> " + productIcon + ",";
        String productDetails = details.contains(PRODUCT_LABEL) ? details.substring(details.indexOf(",", details.indexOf(PRODUCT_LABEL)) + 1) + "," : DEFAULT_PRODUCT_DETAILS;

        details = details.contains(PRODUCT_LABEL) ? details.substring(0, details.indexOf(PRODUCT_LABEL)) : details;
        details = insertString(details, PRODUCT_LABEL + productName, details.indexOf(","));

        if (isPricingNotfication(exceptionAlert.getSubType().getName())) {
            Product dependentProduct = product.getDependentProductId() == null ? null : tenantCrudService.findByNamedQuerySingleResult(Product.GET_BY_ID,
                    QueryParameter.with("productId", product.getDependentProductId()).parameters());
            String dependentProductIcon = dependentProduct == null ? "" : " " + getProductIcon(dependentProduct, isIndependentProductsEnabled);

            details = insertString(details, productDetails, details.indexOf(",", details.indexOf("room")));
            details = insertString(details, dependentProductIcon, details.indexOf(",", details.indexOf("base.product")) - 1);
        }
        return details;
    }

    private String insertString(String originalString, String stringToBeInserted, int index) {
        return originalString.substring(0, index + 1)
                + stringToBeInserted
                + originalString.substring(index + 1);
    }

    public boolean isAgileRatesEnabled() {
        return configService == null ? Boolean.FALSE : configService.getBooleanParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED) &&
                configService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_AGILE_NOTIFICATIONS);
    }

    public boolean isPricingNotfication(String subTypeName) {
        return ExceptionSubType.PRICING_BY_VALUE.toString().equalsIgnoreCase(subTypeName)
                || ExceptionSubType.PRICING.toString().equalsIgnoreCase(subTypeName);
    }

    public boolean isProductEnabledForCompetitorNotification() {
        return configService == null ? Boolean.FALSE : configService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRODUCT_DIMENSION_FOR_COMPETITIVE_NOTIFICATIONS);
    }

    public boolean isCompetitiveNotification(String subTypeName) {
        return ExceptionSubType.COMP_SUBTYPE.toString().equalsIgnoreCase(subTypeName);
    }

    public boolean isProductDetailsEnabled(String subTypeName) {
        return isAgileRatesEnabled() && (isPricingNotfication(subTypeName)
                || (isProductEnabledForCompetitorNotification() && isCompetitiveNotification(subTypeName)));
    }

    public boolean isRoomTypeLevelNotification(InformationMgrAlertConfigEntity informationMgrAlertConfigEntity) {
        return LevelType.ROOM_TYPE.name().equalsIgnoreCase(informationMgrAlertConfigEntity.getExceptionLevel().getName());
    }

    public int getAccomClassId(InformationMgrAlertConfigEntity informationMgrAlertConfigEntity) {
        return isRoomTypeLevelNotification(informationMgrAlertConfigEntity)
                ? tenantCrudService.find(AccomType.class, informationMgrAlertConfigEntity.getExceptionSubLevel()).getAccomClass().getId()
                : informationMgrAlertConfigEntity.getExceptionSubLevel();
    }

    public boolean isIndependentProductsEnabled() {
        return configService == null ? Boolean.FALSE : configService.getBooleanParameterValue(PreProductionConfigParamName.INDEPENDENT_PRODUCTS_ENABLED);
    }

    public String getProductIcon(Product product, boolean isIndependentProductsEnabled) {
        if (product.isIndependentProduct() && isIndependentProductsEnabled) {
            return CUBE;
        } else if (product.isGroupProduct()) {
            return USERS;
        } else if (product.isOptimized()) {
            return OPTIMIZE_ICON;
        } else if (!product.isSystemDefault() && isIndependentProductsEnabled) {
            return LINK;
        }
        return "";
    }

    public void setDetailsByLOS(InfoMgrExcepNotifEntity infoMgrExcepNotifEntity, ExceptionAlert exceptionAlert) {
        List<String> detailsList = new ArrayList<>();
        String[] detailsRow = infoMgrExcepNotifEntity.getDetails().split(",");
        for (String str : detailsRow) {
            detailsList.add(str);
        }
        exceptionAlert.setDetailsByLOS(detailsList);
    }

    public void setOptimizationValueForExceptionAlertInstance(InfoMgrExcepNotifEntity infoMgrExcepNotifEntity, ExceptionAlert exceptionAlert) {
        if (ExceptionSubType.PRICING_BY_VALUE.getCode().equalsIgnoreCase(exceptionAlert.getSubType().getName())) {
            exceptionAlert.setCurrentOptValueForPricing(infoMgrExcepNotifEntity.getCurrentOptimizationValue());
            exceptionAlert.setLastOptValueForPricing(infoMgrExcepNotifEntity.getLastOptimizationValue());
            exceptionAlert.setCurrentOptimizationValue(extractPriceValue(infoMgrExcepNotifEntity.getCurrentOptimizationValue()));
            exceptionAlert.setLastOptVal(extractPriceValue(infoMgrExcepNotifEntity.getLastOptimizationValue()));
        } else {
            exceptionAlert.setCurrentOptimizationValue(infoMgrExcepNotifEntity.getCurrentOptimizationValue());
            exceptionAlert.setLastOptVal(infoMgrExcepNotifEntity.getLastOptimizationValue());
        }
    }

    private String extractPriceValue(Object object) {
        String ratePlanNameWithPriceValueStr = (String) object;
        ratePlanNameWithPriceValueStr = ratePlanNameWithPriceValueStr.trim();
        String[] ratePlanNameWithPriceValues = ratePlanNameWithPriceValueStr.split("\\(");
        return (null != ratePlanNameWithPriceValues[0]) ? ratePlanNameWithPriceValues[0].trim() : "0.0";
    }

    public Alert getExtendedDetailsForAlertInstance(Alert objAlert) {
        Map<Integer, Set<String>> permissionPerProperty = getPermissionPerProperty(Arrays.asList(objAlert.getPropertyId()));
        objAlert.setSteps(getSteps(objAlert.getAlertId(), objAlert.getPropertyId()));
        applyPermissionsToSteps(objAlert.getSteps(), permissionPerProperty.get(objAlert.getPropertyId()));
        if (objAlert.getType().getName().equalsIgnoreCase(AlertType.ExcessOOOInPast.getName())) {
            updateDetailsMetadata(objAlert);
        }
        if (objAlert.getType().getName().equalsIgnoreCase(AlertType.LowestBarFplosCheckFail.getName())
                || objAlert.getType().getName().equalsIgnoreCase(AlertType.HighestBarFplosCheckFail.getName())
                || objAlert.getType().getName().equalsIgnoreCase(AlertType.LowestOptimizationDecisionCheckFail.getName())
                || objAlert.getType().getName().equalsIgnoreCase(AlertType.HighestOptimizationDecisionCheckFail.getName())) {
            removeKeysFromDetails(objAlert, StatisticalOutlierAlertConstants.DECISION_JOB_ID_KEY);
            updateDetailsMetadata(objAlert);
        }
        if (objAlert.getType().getName().equalsIgnoreCase(AlertType.CROOONotUpdated.getName())) {
            String details = objAlert.getDetails();
            try {
                String[] strArr1 = details.split(ComponentRoomService.OCCUPANCY_DATE + ComponentRoomService.BLANK_SPACE);
                SimpleDateFormat format = new SimpleDateFormat(dateService.getUserDateFormat());
                for (String str1 : strArr1) {
                    if (!"".equals(str1)) {
                        String occupancyDate = str1.split("<\\/B>")[0];
                        Date date = new LocalDate(occupancyDate).toDate();
                        String formattedDate = format.format(date);
                        details = details.replace(occupancyDate, formattedDate);
                    }
                }
                objAlert.setDetails(details);
            } catch (Exception e) {
                LOGGER.warn("Failed to format date in User date format... ", e);
            }
        }
        changeStepTextForMsRecodingAlert(objAlert);
        return objAlert;
    }

    public void changeStepTextForMsRecodingAlert(Alert objAlert) {
        if (mktSegRecodingService.isMktSegRecodingConfigInProgress()) {
            if (objAlert.getType().getName().equalsIgnoreCase(AlertType.PMSMigrationInconsistentMappingsValidation.getName())) {
                objAlert.getSteps().stream().forEach(step -> {
                    if ("PMSMigrationInconsistentMappingsValidation.url".equals(step.getText())) {
                        step.setText("msrecoding.inconsistent.mappings.validation.url");
                    } else if ("PMSMigrationInconsistentMappingsValidation.success".equals(step.getText())) {
                        step.setText("msrecoding.inconsistent.mappings.validation.success");
                    }
                });
            }
            if (objAlert.getType().getName().equalsIgnoreCase(AlertType.PMSMigrationCCFGValidation.getName())) {
                objAlert.getSteps().stream()
                        .filter(step1 -> PMS_MIGRATION_COMPLETE_CCFG_STEP.equalsIgnoreCase(step1.getText())).findFirst()
                        .ifPresent(step -> step.setText(MS_RECODING_CCFG_VALIDATION_URL));
                objAlert.getSteps().stream()
                        .filter(step1 -> PMS_MIGRATION_CCFG_VALIDATION_SUCCESS.equalsIgnoreCase(step1.getText())).findFirst()
                        .ifPresent(step -> step.setText(MS_RECODING_CCFG_VALIDATION_SUCCESS));
            }
        }
    }

    private void updateDetailsMetadata(Alert objAlert) {
        objAlert.setDetailsMetadata(objAlert.parseAlertDetails(objAlert.getDetails()));
    }

    public void removeKeysFromDetails(Alert objAlert, String... keysToRemove) {
        String alertDetails = objAlert.getDetails();
        for (String singleKeyToRemove : keysToRemove) {
            int index = alertDetails.indexOf(singleKeyToRemove);
            if (index != -1) {
                int indexOfNextComma = alertDetails.indexOf(',', index);
                if (index == 0) {
                    indexOfNextComma++;
                } else if (indexOfNextComma == -1) {
                    indexOfNextComma = alertDetails.length();
                    index--;
                } else if (indexOfNextComma < alertDetails.length()) {
                    index--;
                }
                alertDetails = alertDetails.replace(alertDetails.substring(index, indexOfNextComma), "");
            }
        }
        objAlert.setDetails(alertDetails);
    }

    public void sortOnScoreDesc(List<InfoMgrExcepNotifEntity> listFoundExceptionsRaised) {
        Comparator<InfoMgrExcepNotifEntity> c = (e1, e2) ->
                ((Integer) e1.getScore()).compareTo((Integer) e2.getScore());
        listFoundExceptionsRaised.sort(c.reversed());
    }

    public List<InfoMgrExcepNotifEntity> getInstancesSortedOnScore(SearchCriteriaDTO searchCriteria, String alertCategory) {
        List<InfoMgrExcepNotifEntity> listFoundExceptionsRaised = searchWithCriteria(searchCriteria, alertCategory);
        sortOnScoreDesc(listFoundExceptionsRaised);
        return listFoundExceptionsRaised;
    }

    private boolean shouldAddProductInStepURL(InfoMgrInstanceEntity alert) {
        return alert.getAlertType().getName().equals(AlertType.InsufficientCompetitorRatesForCompetitiveMarket.name());
    }

    private Integer getProductIdFromConfig(InfoMgrInstanceEntity alert, Integer propertyId) {
        InformationMgrAlertConfigEntity configEntity = multiPropertyCrudService.find(propertyId, InformationMgrAlertConfigEntity.class, alert.getExceptionAlertConfigEntityId());
        return configEntity != null && configEntity.getProduct() != null ? configEntity.getProduct().getId() : null;
    }

    private boolean excludeStep(InfoMgrInstanceEntity alert, InfoMgrStepsEntity step) {
        if (alert.getAlertType().getName().equals(AlertType.UnassignedMarketSegment.name())) {

            boolean amsEnabled = configService.getBooleanParameterValue(FeatureTogglesConfigParamName.ANALYTICAL_MARKET_SEGMENT_ENABLED.value());

            // If this is the assign market segments in AMS step...
            if ((step.getOrdinal() == 1)) {

                // If AMS is not even enabled...
                if (!amsEnabled) {
                    // skip this step
                    return true;
                }

                // AMS is enabled - need to check to see if all of the market segments have been attributed in AMS
                List<String> marketSegmentCodes = parseMarketSegmentCodes(alert.getDetails());

                List<String> unassignedMarketCodes = this.analyticalMarketSegmentService.getUnassignedMappedMarketCodes(marketSegmentCodes);

                // If there are actually no unassigned market segment codes...
                if (unassignedMarketCodes.isEmpty()) {
                    // skip this step
                    return true;
                }
            }

            // Exclude the Assign attributes action if AMS not enabled
            if ((step.getOrdinal() == 1) && !amsEnabled) {
                return true;
            }

            // Exclude the alert to send to Market Segment screen if AMS enabled
            if ((step.getOrdinal() == 2) && amsEnabled) {
                return true;
            }
        }

        if (alert.getAlertType().getName().equals(AlertType.UnassignedRoomType.name())) {
            if ((step.getOrdinal() == 2)) {
                return true;
            }
            if ((step.getOrdinal() == 5)
                    && !configService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())) {
                return true;
            }
            if ((step.getOrdinal() == 6)
                    && !configService.getBooleanParameterValue(PreProductionConfigParamName.COMPLIMENTARY_CROSS_CHARGE_CONFIGURATION_ENABLED)) {
                return true;
            }
            if ((step.getOrdinal() == 7)
                    && !configService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)) {
                return true;
            }

            if ((step.getOrdinal() == 8)
                    && !configService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)) {
                return true;
            }

            if ((step.getOrdinal() == 9)
                    && (!Constants.BAR_DECISION_VALUE_RATEOFDAY.equals(configService.getParameterValue(getContext(), IPConfigParamName.BAR_BAR_DECISION))
                    || configService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED))) {
                return true;
            }

            if ((step.getOrdinal() == 10)
                    && !configService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED)) {
                return true;
            }

            if ((step.getOrdinal() == 12)
                    && !configService.getBooleanParameterValue(FeatureTogglesConfigParamName.CHANNEL_COST_ENABLED)) {
                return true;
            }

            if ((step.getOrdinal() == 13)
                    && !configService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED)) {
                return true;
            }

            if ((step.getOrdinal() == 14)
                    && !configService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED)) {
                return true;
            }
        }

        if (alert.getAlertType().getName().equals(AlertType.UnassignedZeroCapacityRoomType.name())) {
            if ((step.getOrdinal() == 2)) {
                return true;
            }
            if ((step.getOrdinal() == 5)
                    && !configService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED)) {
                return true;
            }
            if ((step.getOrdinal() == 6)
                    && !configService.getBooleanParameterValue(PreProductionConfigParamName.COMPLIMENTARY_CROSS_CHARGE_CONFIGURATION_ENABLED)) {
                return true;
            }
            if ((step.getOrdinal() == 7)
                    && !configService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)) {
                return true;
            }
            if ((step.getOrdinal() == 8)
                    && !configService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)) {
                return true;
            }
            if ((step.getOrdinal() == 9)
                    && (!Constants.BAR_DECISION_VALUE_RATEOFDAY.equals(configService.getParameterValue(getContext(), IPConfigParamName.BAR_BAR_DECISION))
                    || configService.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED))) {
                return true;
            }

            if ((step.getOrdinal() == 10)
                    && !configService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED)) {
                return true;
            }
            if ((step.getOrdinal() == 12)
                    && !configService.getBooleanParameterValue(FeatureTogglesConfigParamName.CHANNEL_COST_ENABLED)) {
                return true;
            }
            if ((step.getOrdinal() == 13)
                    && !configService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED)) {
                return true;
            }
            if ((step.getOrdinal() == 14)
                    && !configService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED)) {
                return true;
            }
        }

        if (alert.getAlertType().getName().equals(AlertType.IncompleteLDBConfiguration.name())) {
            if (PacmanWorkContextHelper.isHiltonClientCode()) {
                java.time.LocalDate caughtupDate = dateService.getCaughtUpJavaLocalDate();
                LDBConfig ldbConfig = ldbService.getLDBConfig();
                if (ldbConfig != null && ldbConfig.getSoftOpeningEndDate() != null
                        && caughtupDate.isBefore(JavaLocalDateUtils.toJavaLocalDate(ldbConfig.getSoftOpeningEndDate()))
                        && step.getOrdinal() == 2 && step.getStepType() == 4) {
                    return true;
                }
            }
        }


        if (alert.getAlertType().getName().equals(AlertType.NewWebRateCompetitorFound.name())
                && (step.getOrdinal() == 2)
                && !configService.getBooleanParameterValue(FeatureTogglesConfigParamName.EXTENDED_STAY_UNQUALIFIED_RATE_MANAGEMENT_ENABLED.value())) {
            return true;
        }
        if (alert.getAlertType().getName().equals(AlertType.OOORoomsAffectingDemand.name())
                && step.getOrdinal() == 3 && step.getStepType() == 4
                && !configService.getBooleanParameterValue(PreProductionConfigParamName.SNOOZE_COSTLY_OOO_EXCEPTION_UNTIL_THRESHOLD_CONDITION)) {
            return true;
        }
        if (alert.getAlertType().getName().equals(AlertType.BookedDataUsageStarted.name())) {
            int userSelection = bookedDataService.getUserSelectedOptionToResolveAlert(AlertType.SufficientBookedRTDataAvailable);
            if (userSelection == USE_BOOKED_DATA && step.getOrdinal() == I_AM_SATISFIED_WITH_DECISIONS_AND_MOVE_TO_TWO_WAY) {
                return true;
            }
            if (userSelection == USE_BOOKED_DATA_AND_MOVE_TO_ONE_WAY && step.getOrdinal() == I_AM_SATISFIED_WITH_DECISIONS) {
                return true;
            }
        }

        return "view.the.agile.configuration".equalsIgnoreCase(step.getText()) && !isAgileRatesEnabled();
    }

    private List<String> parseMarketSegmentCodes(String marketSegmentCodes) {

        // Split the string by comma
        List<String> origMarketSegmentCodes = Arrays.asList(marketSegmentCodes.split(","));

        // Trim all of the leading and trailing whitespace from each code
        return origMarketSegmentCodes.stream().map(String::trim).collect(Collectors.toList());
    }

    public void setAnalyticalMarketSegmentService(AnalyticalMarketSegmentService analyticalMarketSegmentService) {
        this.analyticalMarketSegmentService = analyticalMarketSegmentService;
    }

    private boolean alertTypeNotDecisionAsOf(String alertName) {
        return !(StringUtils.equalsIgnoreCase(alertName, AlertType.DecisionAsOfLastNightlyOptimization.toString())
                || StringUtils.equalsIgnoreCase(alertName, AlertType.DecisionAsOfLastOptimization.toString()));
    }

    private String extractHigherLevelRate(int higherLevelValue, int minRank, Map<Integer, String> rateCodeByRank) {
        String higherLevelRate;
        if (higherLevelValue < minRank) {
            higherLevelRate = rateCodeByRank.get(minRank);
        } else {
            higherLevelRate = rateCodeByRank.get(higherLevelValue);
            if (null == higherLevelRate || StringUtils.equalsIgnoreCase(higherLevelRate, "null")) {
                higherLevelRate = rateCodeByRank.get(minRank);
            }
        }
        return higherLevelRate;
    }

    private String extractLowerLevelRate(int lowerLevelValue, int maxRank, Map<Integer, String> rateCodeByRank) {
        String lowerLevelRate;
        if (lowerLevelValue > maxRank) {
            lowerLevelRate = rateCodeByRank.get(maxRank);
        } else {
            lowerLevelRate = rateCodeByRank.get(lowerLevelValue);

            if (null == lowerLevelRate || StringUtils.equalsIgnoreCase(lowerLevelRate, "null")) {
                lowerLevelRate = rateCodeByRank.get(maxRank);
            }
        }
        return lowerLevelRate;
    }

    private Map<Integer, Property> extractValidPropertyIdsMap(SearchCriteriaDTO searchCriteria) {
        Map<Integer, Property> validPropertyIdsMap;
        if (SystemConfig.isInfoMgrPropertyAndPropertyGroupEnabled()) {
            validPropertyIdsMap = validatePropertyIds(searchCriteria);
        } else {
            validPropertyIdsMap = getAuthorizedProperties();
        }
        return validPropertyIdsMap;
    }

    private void populateExceptionTypesParameter(SearchCriteriaDTO searchCriteria, Map<String, Object> parameters) {
        if (CollectionUtils.isNotEmpty(searchCriteria.getExceptionTypes())) {
            parameters.put("exceptionTypes", searchCriteria.getExceptionTypes());
        } else if (CollectionUtils.isNotEmpty(searchCriteria.getNotificationTypes())) {
            parameters.put("exceptionTypes", searchCriteria.getNotificationTypes());
        }
    }

    private List<Integer> populateExceptionStatusParameter(SearchCriteriaDTO searchCriteria) {
        if (CollectionUtils.isNotEmpty(searchCriteria.getExceptionStatus())) {
            return searchCriteria.getExceptionStatus();
        } else {
            List<Integer> status = new ArrayList<>();
            status.add(Constants.ALERT_STATUS_RESOLVED_ID);
            status.add(Constants.ALERT_STATUS_SUSPENDED_ID);
            status.add(Constants.ALERT_STATUS_SNOOZED_ID);
            return status;
        }
    }

    private List<Integer> populateStatusIdsParameter(SearchCriteriaDTO searchCriteria) {
        if (CollectionUtils.isNotEmpty(searchCriteria.getStatusIds())) {
            return searchCriteria.getStatusIds();
        } else {
            List<Integer> statusIds = new ArrayList<>();
            statusIds.add(Constants.ACTIVE_STATUS_ID);
            return statusIds;
        }
    }

    private StringBuilder createSearchQuery(SearchCriteriaDTO searchCriteria) {
        StringBuilder queryStr = new StringBuilder(BASE_QUERY);

        queryStr.append(WITH_PROPERTIES);

        if (CollectionUtils.isNotEmpty(searchCriteria.getExceptionTypes())
                || CollectionUtils.isNotEmpty(searchCriteria.getNotificationTypes())) {
            queryStr.append(WITH_EXCEPTION_TYPES);
        }

        if (CollectionUtils.isNotEmpty(searchCriteria.getExceptionStatus())) {
            queryStr.append(WITH_EXCEPTION_STATUS);
        } else {
            queryStr.append(WITH_EXCEPTION_NOT_RESOLVED_NOT_SUSPENDED);
        }
        if (null != searchCriteria.getStartDate() && null != searchCriteria.getEndDate()) {
            queryStr.append(WITH_EXCEPTION_DATES);
        }

        if (null != searchCriteria.getCreationStartDate() && null != searchCriteria.getCreationEndDate()) {
            queryStr.append(WITH_CREATED_DATES);
        }
        if (CollectionUtils.isNotEmpty(searchCriteria.getScores())) {
            queryStr.append(" and (");
            List<Integer> scoreIds = searchCriteria.getScores();
            for (int i = 0; i < scoreIds.size(); i++) {
                queryStr.append(ScoreRange.valueById(scoreIds.get(i)).getSqlString());
                if (scoreIds.size() > 1 && i != (scoreIds.size() - 1)) {
                    queryStr.append(" OR ");
                }
            }
            queryStr.append(" ) ");
        }
        queryStr.append(WITH_STATUS);
        queryStr.append(ORDER_BY);
        return queryStr;
    }

    private String extractSubLevelForIdByRoomType(InformationMgrAlertConfigEntity config, String subLevel) {
        if (!config.isSubLevelKeywordUsed()) {
            AccomType objAccomType = multiPropertyCrudService.find(config.getPropertyId(), AccomType.class, config.getExceptionSubLevel());
            if (null != objAccomType) {
                subLevel = objAccomType.getName();
            } else {
                LOGGER.error(UNABLE_TO_GET_THE_NAME_FOR_SUBLEVEL);
            }
        } else {
            // case when ROH Room Type Keyword is used
            InformationMgrSubLevelEntity subLevelEntity = multiPropertyCrudService.find(config.getPropertyId(), InformationMgrSubLevelEntity.class, config.getExceptionSubLevel());
            subLevel = subLevelEntity.getDescription();
        }
        return subLevel;
    }

    private String extractSubLevelForIdByRoomClass(InformationMgrAlertConfigEntity config, String subLevel) {
        if (!config.isSubLevelKeywordUsed()) {
            AccomClass objAccomClass = multiPropertyCrudService.find(config.getPropertyId(), AccomClass.class, config.getExceptionSubLevel());
            if (null != objAccomClass) {
                subLevel = objAccomClass.getName();
            } else {
                LOGGER.error(UNABLE_TO_GET_THE_NAME_FOR_SUBLEVEL);
            }
        } else {
            // when Master Class keyword used
            InformationMgrSubLevelEntity subLevelEntity = multiPropertyCrudService.find(config.getPropertyId(), InformationMgrSubLevelEntity.class, config.getExceptionSubLevel());
            subLevel = subLevelEntity.getDescription();
        }
        return subLevel;
    }

    private String extractSubLevelCodeForIdByExceptionLevelCompetitorSelect(InformationMgrAlertConfigEntity config, String subLevel) {
        WebrateCompetitors objWebrateCompetitors = multiPropertyCrudService.find(config.getPropertyId(), WebrateCompetitors.class, config.getExceptionSubLevel());
        if (null != objWebrateCompetitors) {
            subLevel = objWebrateCompetitors.getWebrateCompetitorsName();
        } else {
            LOGGER.error("unable to get the Webrate competitor for Sublevel ");
        }
        return subLevel;
    }

    private String extractSubLevelCodeForIdByExceptionLevelPropertyBusinessView(InformationMgrAlertConfigEntity config, String subLevel) {
        BusinessGroup objBusinessGroup = multiPropertyCrudService.find(config.getPropertyId(), BusinessGroup.class, config.getExceptionSubLevel());
        if (null != objBusinessGroup) {
            subLevel = objBusinessGroup.getName();
        } else {
            LOGGER.error("unable to get the Business Group for Sublevel ");
        }
        return subLevel;
    }

    private String extractSubLevelCodeForIdByExceptionLevelMarketSegment(InformationMgrAlertConfigEntity config, String subLevel) {
        MktSeg objMktSeg = multiPropertyCrudService.find(config.getPropertyId(), MktSeg.class, config.getExceptionSubLevel());
        if (null != objMktSeg) {
            subLevel = objMktSeg.getCode();
        } else {
            LOGGER.error("unable to get the Market Seg for Sublevel ");
        }
        return subLevel;
    }

    private String extractSubLevelCodeForIdByExceptionLevelForecastGroup(InformationMgrAlertConfigEntity config, String subLevel) {
        ForecastGroup objForecastGroup = multiPropertyCrudService.find(config.getPropertyId(), ForecastGroup.class, config.getExceptionSubLevel());
        if (null != objForecastGroup) {
            subLevel = objForecastGroup.getName();
        } else {
            LOGGER.error("unable to get the Forecast Group for Sublevel ");
        }
        return subLevel;
    }

    private String extractSubLevelCodeForIdByExceptionLevelBusinessType(InformationMgrAlertConfigEntity config, String subLevel) {
        BusinessType objBusinessType = multiPropertyCrudService.find(config.getPropertyId(), BusinessType.class, config.getExceptionSubLevel());
        if (null != objBusinessType) {
            subLevel = objBusinessType.getName();
        } else {
            LOGGER.error("unable to get the Business Type for Sublevel ");
        }
        return subLevel;
    }

    private String extractSubLevelCodeForIdByRoomType(InformationMgrAlertConfigEntity config, String subLevel) {
        if (!config.isSubLevelKeywordUsed()) {
            AccomType objAccomType = multiPropertyCrudService.find(config.getPropertyId(), AccomType.class, config.getExceptionSubLevel());
            if (null != objAccomType) {
                subLevel = objAccomType.getAccomTypeCode();
            } else {
                LOGGER.error("unable to get the Room Type Name for Sublevel ");
            }
        } else {
            // case when ROH Room Type Keyword is used
            InformationMgrSubLevelEntity subLevelEntity = multiPropertyCrudService.find(config.getPropertyId(), InformationMgrSubLevelEntity.class, config.getExceptionSubLevel());
            if (null != subLevelEntity) {
                subLevel = subLevelEntity.getDescription();
            }
        }
        return subLevel;
    }

    private String extractSubLevelCodeForIdByRoomClass(InformationMgrAlertConfigEntity config, String subLevel) {
        if (!config.isSubLevelKeywordUsed()) {
            AccomClass objAccomClass = multiPropertyCrudService.find(config.getPropertyId(), AccomClass.class, config.getExceptionSubLevel());
            if (null != objAccomClass) {
                subLevel = objAccomClass.getCode();
            } else {
                LOGGER.error(UNABLE_TO_GET_THE_NAME_FOR_SUBLEVEL);
            }
        } else {
            // when Master Class keyword used
            InformationMgrSubLevelEntity subLevelEntity = multiPropertyCrudService.find(config.getPropertyId(), InformationMgrSubLevelEntity.class, config.getExceptionSubLevel());
            subLevel = subLevelEntity.getDescription();
        }
        return subLevel;
    }

    public String getContext() {
        return configService.propertyNode(PacmanWorkContextHelper.getWorkContext());
    }
}
