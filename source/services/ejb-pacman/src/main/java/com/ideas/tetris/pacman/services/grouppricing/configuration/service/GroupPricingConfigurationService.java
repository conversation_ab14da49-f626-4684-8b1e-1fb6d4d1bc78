package com.ideas.tetris.pacman.services.grouppricing.configuration.service;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.configautomation.dto.ServicingCosts;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigAccomType;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfiguration;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingEvaluationMethod;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.PriceAcceleratorCfg;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.service.GroupEvaluationService;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationLTBDEService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.servicingcostbylos.service.ServicingCostByLOSService;
import com.ideas.tetris.platform.common.crudservice.AbstractMultiPropertyCrudService;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.hibernate.Hibernate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Objects.isNull;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;


@Component
@Transactional
public class GroupPricingConfigurationService {
    public static final BigDecimal DEFAULT_PER_ROOM_SERVICING_COST = BigDecimal.ZERO;
    private static final Logger LOGGER = Logger.getLogger(GroupPricingConfigurationService.class);
    public static final String PROPERTY_ID = "propertyId";
    public static final String DEFAULT_EVALUATION_METHOD = "defaultEvaluationMethod";
    @Autowired
    PacmanConfigParamsService pacmanConfigParamsService;
    @Autowired
    AbstractMultiPropertyCrudService multiPropertyCrudService;
    @TenantCrudServiceBean.Qualifier
    @Autowired
	@Qualifier("tenantCrudServiceBean")
	private CrudService tenantCrudService;
    @Autowired
	private ServicingCostByLOSService servicingCostByLOSService;
    @Autowired
	private GroupEvaluationService groupEvaluationService;
    @Autowired
	private DateService dateService;

    @Autowired
    PricingConfigurationLTBDEService pricingConfigurationLTBDEService;

    @Autowired
    PricingConfigurationService pricingConfigurationService;

    private List<GroupPricingConfiguration> buildNewAccomClassGroupPricingConfigurations(Integer propertyId) {
        return getAllActiveNonDefaultAccomClassByViewOrder()
                .stream().map(accomClass -> buildGroupPricingConfigurationForRoomClass(propertyId, accomClass))
                .collect(Collectors.toList());
    }

    private GroupPricingConfiguration buildROHGroupPricingConfiguration(Integer propertyId) {
        GroupPricingConfiguration groupPricingConfiguration = new GroupPricingConfiguration();
        groupPricingConfiguration.setPropertyId(propertyId);
        groupPricingConfiguration.setDefaultEvaluationMethod(GroupPricingEvaluationMethod.ROH);
        groupPricingConfiguration.setPerRoomServicingCost(DEFAULT_PER_ROOM_SERVICING_COST);

        return groupPricingConfiguration;
    }

    private List<String> buildRoomClassCodeList(List<AccomClass> roomClasses) {
        List<String> roomClassCodes = new ArrayList<>();

        for (AccomClass roomClass : roomClasses) {
            roomClassCodes.add(roomClass.getCode());
        }

        return roomClassCodes;
    }

    public void checkThatAllRoomClassesAreAccountedFor() {
        List<GroupPricingConfiguration> configurationsForRoomClasses = getConfigurationsForRoomClasses(PacmanWorkContextHelper.getPropertyId());
        checkThatAllRoomClassesAreAcountedFor(configurationsForRoomClasses);
    }

    protected List<GroupPricingConfiguration> checkThatAllRoomClassesAreAcountedFor(
            List<GroupPricingConfiguration> roomClassesConfigurations) {

        List<GroupPricingConfiguration> validRoomClassConfigurations = new ArrayList<>();
        List<AccomClass> roomClasses = getAllActiveNonDefaultAccomClassByViewOrder();

        // handle case of a new room class
        for (AccomClass roomClass : roomClasses) {
            GroupPricingConfiguration groupPricingConfiguration = findGroupPricingConfigurationForRoomClass(roomClass);
            if (groupPricingConfiguration == null) {
                groupPricingConfiguration = buildGroupPricingConfigurationForRoomClass(
                        PacmanWorkContextHelper.getPropertyId(), roomClass);
            }
            Hibernate.initialize(groupPricingConfiguration.getAccomClass());
            validRoomClassConfigurations.add(groupPricingConfiguration);
        }

        // handle case of a deleted room class
        handleDeletedRoomClass(roomClassesConfigurations, roomClasses);

        return validRoomClassConfigurations;
    }


    public void delete() {
        GroupPricingConfiguration groupPricingConfiguration = getROHGroupPricingConfiguration(PacmanWorkContextHelper.getPropertyId());
        if (groupPricingConfiguration != null) {
            tenantCrudService.delete(groupPricingConfiguration);
            servicingCostByLOSService.deleteROHConfiguration();
        }
    }

    public void delete(GroupPricingConfiguration groupPricingConfiguration) {
        tenantCrudService.delete(groupPricingConfiguration);
        servicingCostByLOSService.deleteRoomClassServicingCostConfiguration(groupPricingConfiguration);
    }

    protected GroupPricingConfiguration findGroupPricingConfigurationForRoomClass(AccomClass roomClass) {
        return (GroupPricingConfiguration) tenantCrudService.findByNamedQuerySingleResult(
                GroupPricingConfiguration.FIND_BY_ACCOM_CLASS_ID,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).and("accomClass", roomClass)
                        .parameters());
    }

    @SuppressWarnings("unchecked")
    public List<AccomClass> getAllActiveNonDefaultAccomClassByViewOrder() {
        return tenantCrudService.findByNamedQuery(AccomClass.ALL_ACTIVE_NON_DEFAULT_NONEMPTY_BY_RANK_ORDER,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    @SuppressWarnings("unchecked")
    public List<GroupPricingConfigAccomType> getAllConfigAccomType() {
        List<GroupPricingConfigAccomType> groupPricingConfigAccomTypeList = getGroupPricingConfigAccomTypes();
        addActiveAccomTypes(groupPricingConfigAccomTypeList);
        return groupPricingConfigAccomTypeList;
    }

    public List<GroupPricingConfigAccomType> getAllGroupPricingConfigAccomType() {
        List<GroupPricingConfigAccomType> groupPricingConfigAccomTypeList = getAllGroupPricingAccomTypes();
        addActiveAccomTypes(groupPricingConfigAccomTypeList);
        return groupPricingConfigAccomTypeList;
    }

    private void addActiveAccomTypes(List<GroupPricingConfigAccomType> groupPricingConfigAccomTypeList) {
        Set<AccomType> accomTypeSet = getActiveAccomTypes(groupPricingConfigAccomTypeList);
        List<GroupPricingConfigAccomType> newGroupPricingConfigAccomTypeList = createGroupPricingConfigAccomTypesUsing(accomTypeSet, false);
        groupPricingConfigAccomTypeList.addAll(newGroupPricingConfigAccomTypeList);
    }

    private Set<AccomType> getActiveAccomTypes(List<GroupPricingConfigAccomType> groupPricingConfigAccomTypeList) {
        List<AccomType> accomTypeList = getActiveAccomTypes();
        Set<AccomType> accomTypeSet = new HashSet<>(accomTypeList);

        // find all AccomTypes that do not have a corresponding GroupPricingConfigAccomType.
        for (GroupPricingConfigAccomType groupPricingConfigAccomType : groupPricingConfigAccomTypeList) {
            accomTypeSet.remove(groupPricingConfigAccomType.getAccomType());
        }
        return accomTypeSet;
    }

    public List<GroupPricingConfigAccomType> createGroupPricingConfigAccomTypesUsing(Set<AccomType> accomTypeSet,
                                                                                     boolean active) {
        return accomTypeSet.stream()
                .map(accomType -> {
                    GroupPricingConfigAccomType groupPricingConfigAccomType = new GroupPricingConfigAccomType();
                    groupPricingConfigAccomType.setAccomType(accomType);
                    groupPricingConfigAccomType.setPropertyId(PacmanWorkContextHelper.getPropertyId());
                    groupPricingConfigAccomType.setActive(active);
                    return groupPricingConfigAccomType;
                })
                .collect(Collectors.toList());
    }

    public void configureGroupPricingConfigAllAccomTypes() {
        Set<AccomType> accomTypesToConfigureForGroupEvaluation = isRoomTypeRecodingUIEnabled()
                ? getActiveAccomTypes().stream().filter(accomType -> accomType.getDisplayStatusId() != 2).collect(Collectors.toSet())
                : new HashSet<>(getActiveAccomTypes());
        List<GroupPricingConfigAccomType> groupPricingConfigAccomTypes = createGroupPricingConfigAccomTypesUsing(accomTypesToConfigureForGroupEvaluation, true);
        saveConfigAccomType(groupPricingConfigAccomTypes);
    }

    public boolean isRoomTypeRecodingUIEnabled() {
        return pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ROOM_TYPE_RECODING_UI_ENABLED);
    }

    public List<AccomType> getAccomTypesConfiguredForRoomClassEvaluations() {
        List<GroupPricingConfigAccomType> configAccomTypes = tenantCrudService.findByNamedQuery(GroupPricingConfigAccomType.FIND_ACTIVE,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
        return configAccomTypes.stream().map(GroupPricingConfigAccomType::getAccomType).collect(Collectors.toList());
    }

    public List<AccomType> getActiveAccomTypes() {
        List<AccomType> accomTypeList = tenantCrudService.findByNamedQuery(AccomType.ALL_ACTIVE,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());

        return accomTypeList.stream().filter(accomType -> !accomType.getAccomClass()
                        .isExcludedForGroupEvaluation())
                .collect(Collectors.toList());
    }

    protected List<GroupPricingConfigAccomType> getGroupPricingConfigAccomTypes() {
        List<GroupPricingConfigAccomType> accomTypes = getAllGroupPricingAccomTypes();

        return accomTypes.stream().filter(accomType -> !accomType.getAccomType().getAccomClass()
                        .isExcludedForGroupEvaluation())
                .collect(Collectors.toList());
    }

    private List<GroupPricingConfigAccomType> getAllGroupPricingAccomTypes() {
        return tenantCrudService.findByNamedQuery(GroupPricingConfigAccomType.FIND_ALL,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters());
    }

    private List<GroupPricingConfiguration> getConfigurationsForRoomClasses(Integer propertyId) {
        List<GroupPricingConfiguration> retList = new ArrayList<>();
        List<GroupPricingConfiguration> allConfigurations = getGroupPricingConfigurations(propertyId);

        allConfigurations.stream().filter(groupPriceConfiguration -> groupPriceConfiguration.getDefaultEvaluationMethod()
                .equals(GroupPricingEvaluationMethod.RC)).forEach(groupPriceConfiguration -> {
            Hibernate.initialize(groupPriceConfiguration.getAccomClass());
            retList.add(groupPriceConfiguration);
        });

        return retList;
    }

    @SuppressWarnings("unchecked")
    public List<GroupPricingConfiguration> getGroupPricingConfigurations(Integer propertyId) {
        return tenantCrudService.findByNamedQuery(
                GroupPricingConfiguration.FIND_BY_PROPERTY_ID,
                QueryParameter.with(PROPERTY_ID, propertyId).parameters());
    }

    public List<PriceAcceleratorCfg> getPriceAccelerators() {
        return tenantCrudService.findByNamedQuery(PriceAcceleratorCfg.FIND_ALL);
    }

    public List<GroupPricingConfiguration> getGroupPricingConfigurationsPerRoomClass() {
        Integer propertyId = PacmanWorkContextHelper.getPropertyId();
        List<GroupPricingConfiguration> roomClassesConfigurations = getConfigurationsForRoomClasses(propertyId);

        if (CollectionUtils.isEmpty(roomClassesConfigurations)) {
            roomClassesConfigurations.addAll(buildNewAccomClassGroupPricingConfigurations(propertyId));
            return roomClassesConfigurations;
        }

        return checkThatAllRoomClassesAreAcountedFor(roomClassesConfigurations);
    }

    public GroupPricingConfiguration getROHGroupPricingConfigurationForProperty(Integer propertyId) {
        GroupPricingConfiguration groupPricingConfiguration = getROHGroupPricingConfiguration(propertyId);

        if (groupPricingConfiguration == null) {
            return getGroupPricingConfiguration(propertyId);
        }

        return groupPricingConfiguration;
    }

    public GroupPricingConfiguration getROHGroupPricingConfigurationForPropertyUsingTenant(Integer propertyId) {
        GroupPricingConfiguration groupPricingConfiguration = getROHGroupPricingConfigurationUsingTenant(propertyId);

        if (groupPricingConfiguration == null) {
            return getGroupPricingConfigurationUsingTenant(propertyId);
        }

        return groupPricingConfiguration;
    }

    private GroupPricingConfiguration getGroupPricingConfigurationUsingTenant(Integer propertyId) {
        GroupPricingConfiguration groupPricingConfiguration;
        groupPricingConfiguration = buildROHGroupPricingConfiguration(propertyId);
        saveUsingTenant(groupPricingConfiguration);
        servicingCostByLOSService.createROHServicingCostConfiguration(groupPricingConfiguration);
        return groupPricingConfiguration;
    }

    private void saveUsingTenant(GroupPricingConfiguration groupPricingConfiguration) {
        if (groupPricingConfiguration.getPerRoomServicingCost() == null) {
            groupPricingConfiguration.setPerRoomServicingCost(DEFAULT_PER_ROOM_SERVICING_COST);
        }
        tenantCrudService.save(groupPricingConfiguration);
    }

    private GroupPricingConfiguration getGroupPricingConfiguration(Integer propertyId) {
        GroupPricingConfiguration groupPricingConfiguration;
        groupPricingConfiguration = buildROHGroupPricingConfiguration(propertyId);
        save(groupPricingConfiguration);
        servicingCostByLOSService.createROHServicingCostConfiguration(groupPricingConfiguration);
        return groupPricingConfiguration;
    }

    private GroupPricingConfiguration getROHGroupPricingConfiguration(Integer propertyId) {
        return (GroupPricingConfiguration) multiPropertyCrudService.findByNamedQuerySingleResultForSingleProperty(propertyId,
                GroupPricingConfiguration.FIND_BY_EVALUATION_METHOD, QueryParameter.with(PROPERTY_ID, propertyId)
                        .and(DEFAULT_EVALUATION_METHOD, GroupPricingEvaluationMethod.ROH).parameters());

    }

    private GroupPricingConfiguration getROHGroupPricingConfigurationUsingTenant(Integer propertyId) {
        return (GroupPricingConfiguration) tenantCrudService.findByNamedQuerySingleResult(
                GroupPricingConfiguration.FIND_BY_EVALUATION_METHOD, QueryParameter.with(PROPERTY_ID, propertyId)
                        .and(DEFAULT_EVALUATION_METHOD, GroupPricingEvaluationMethod.ROH).parameters());

    }

    private void handleDeletedRoomClass(List<GroupPricingConfiguration> roomClassesConfigurations,
                                        List<AccomClass> roomClasses) {
        List<String> roomClassCodes = buildRoomClassCodeList(roomClasses);
        roomClassesConfigurations.stream().filter(groupPricingConfiguration -> !roomClassCodes.contains(groupPricingConfiguration.getAccomClass().getCode())).forEach(this::delete);
    }

    private GroupPricingConfiguration buildGroupPricingConfigurationForRoomClass(Integer propertyId,
                                                                                 AccomClass accomClass) {
        GroupPricingConfiguration groupPricingConfiguration = new GroupPricingConfiguration();
        groupPricingConfiguration.setPropertyId(propertyId);
        groupPricingConfiguration.setDefaultEvaluationMethod(GroupPricingEvaluationMethod.RC);
        groupPricingConfiguration.setAccomClass(accomClass);
        return groupPricingConfiguration;
    }

    public void save(GroupPricingConfiguration groupPricingConfiguration) {
        if (groupPricingConfiguration.getPerRoomServicingCost() == null) {
            groupPricingConfiguration.setPerRoomServicingCost(DEFAULT_PER_ROOM_SERVICING_COST);
        }
        multiPropertyCrudService.save(groupPricingConfiguration.getPropertyId(), groupPricingConfiguration);
    }

    public void save(List<GroupPricingConfiguration> groupPricingConfigurations) {
        BigDecimal totalRooms = getTotalRooms(groupPricingConfigurations);
        BigDecimal rohServiceCost = BigDecimal.ZERO;

        for (GroupPricingConfiguration groupPricingConfiguration : groupPricingConfigurations) {

            if (groupPricingConfiguration.getPerRoomServicingCost() == null) {
                groupPricingConfiguration.setPerRoomServicingCost(DEFAULT_PER_ROOM_SERVICING_COST);
            }

            if (groupPricingConfiguration.isRoomClassEvaluationMethod()
                    && groupPricingConfiguration.getPerRoomServicingCost().compareTo(BigDecimal.ZERO) > 0
                    && !groupPricingConfiguration.isRoomClassExcluded()) {

                BigDecimal rcPctOfTotalRooms = groupPricingConfiguration.getRoomClassCapacity()
                        .divide(totalRooms, 2, RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_UP);
                rohServiceCost = rohServiceCost
                        .add(groupPricingConfiguration.getPerRoomServicingCost().multiply(rcPctOfTotalRooms));
            }
        }

        updateROHGroupPricingConfiguration(groupPricingConfigurations, rohServiceCost);

        tenantCrudService.save(groupPricingConfigurations);
        servicingCostByLOSService.syncConfigurations(PacmanWorkContextHelper.getPropertyId(), groupPricingConfigurations);
    }

    public void saveConfigAccomType(List<GroupPricingConfigAccomType> groupPricingConfigAccomTypes) {
        List existingConfigTypes = getGroupPricingConfigAccomTypes();

        if (isNotEmpty(existingConfigTypes)) {
            // To avoid duplication issues, just delete all existing entries, then save the new entries
            tenantCrudService.delete(existingConfigTypes);
        }

        tenantCrudService.save(groupPricingConfigAccomTypes);
    }

    public void setTenantCrudService(CrudService crudService) {
        this.tenantCrudService = crudService;
    }

    private BigDecimal getTotalRooms(List<GroupPricingConfiguration> groupPricingConfigurations) {
        BigDecimal totalRooms = BigDecimal.ZERO;

        for (GroupPricingConfiguration groupPricingConfiguration : groupPricingConfigurations) {
            if (groupPricingConfiguration.isRoomClassEvaluationMethod() && !groupPricingConfiguration.isRoomClassExcluded()) {
                totalRooms = totalRooms.add(groupPricingConfiguration.getRoomClassCapacity());
            }
        }

        return totalRooms;
    }

    private void updateROHGroupPricingConfiguration(List<GroupPricingConfiguration> groupPricingConfigurations,
                                                    BigDecimal rohServiceCost) {

        for (GroupPricingConfiguration gpc : groupPricingConfigurations) {
            if (gpc.isROHEvaluationMethod()) {
                gpc.setPerRoomServicingCost(rohServiceCost);
                return;
            }
        }
        // see if it exists in db
        GroupPricingConfiguration roh = getROHGroupPricingConfiguration(PacmanWorkContextHelper.getPropertyId());

        if (roh == null) {
            roh = buildROHGroupPricingConfiguration(PacmanWorkContextHelper.getPropertyId());
        }
        roh.setPerRoomServicingCost(rohServiceCost);
        groupPricingConfigurations.add(roh);
    }

    public void handleModifiedRoomClasses() {
        List<GroupPricingConfiguration> groupPricingConfigurations = getGroupPricingConfigurations(
                PacmanWorkContextHelper.getPropertyId());
        List<GroupPricingConfiguration> deletedGroupPricingConfigurations = new ArrayList<>();
        for (Iterator<GroupPricingConfiguration> iter = groupPricingConfigurations.iterator(); iter.hasNext(); ) {
            GroupPricingConfiguration gpc = iter.next();
            if (gpc.isRoomClassEvaluationMethod() && CollectionUtils.isEmpty(gpc.getAccomClass().getAccomTypes())) {
                LOGGER.info("Deleting Group Pricing Configuration for Accom Class: " + gpc.getAccomClass().getId());
                deletedGroupPricingConfigurations.add(gpc);
                tenantCrudService.delete(gpc);
                iter.remove();
            }
        }

        // Need to re-calculate ROH service cost
        save(groupPricingConfigurations);
        servicingCostByLOSService.deleteRoomClassServicingCostConfigurations(deletedGroupPricingConfigurations);
    }

    public List<GroupPricingConfiguration> findGroupPricingConfigurationForAllRoomClasses(Integer propertyId) {
        return tenantCrudService.findByNamedQuery(GroupPricingConfiguration.FIND_BY_EVALUATION_METHOD,
                QueryParameter.with(PROPERTY_ID, propertyId).and(DEFAULT_EVALUATION_METHOD, GroupPricingEvaluationMethod.RC).parameters());
    }


    public void savePriceAcceleratorCfgs(List<PriceAcceleratorCfg> priceAcceleratorCfgs) {
        tenantCrudService.save(priceAcceleratorCfgs);
    }

    public void deletePriceAcceleratorCfgs(List<PriceAcceleratorCfg> priceAcceleratorCfgs) {
        tenantCrudService.delete(priceAcceleratorCfgs);
    }

    public Double getDefaultPriceAccelerator() {
        return pacmanConfigParamsService.getParameterValue(IPConfigParamName.IP_CONFIG_DEF_PRICE_ACCELERATOR);
    }

    public void saveDefaultPriceAccelerator(double defaultPriceAcclerator) {
        pacmanConfigParamsService.updateParameterValue(IPConfigParamName.IP_CONFIG_DEF_PRICE_ACCELERATOR.getParameterName(), String.valueOf(defaultPriceAcclerator));
        enableLTBDE();
    }

    public void enableLTBDE() {
        if (pricingConfigurationLTBDEService.isLTBDEOnConfigChangeEnabled()) {
            pricingConfigurationLTBDEService.enableLTBDEForPricing(true);
            LOGGER.info("Enabled LTBDE due to price accelerator default/season change");
        }
    }

    public void enableLTBDEForPricing(List<PriceAcceleratorCfg> deletedPriceCfgs, List<PriceAcceleratorCfg> modifiedCfgs,
                                      List<PriceAcceleratorCfg> originalCfgs) {
        if (isSeasonChangedBeyondOptimisationWindow(Stream.concat(modifiedCfgs.stream(), deletedPriceCfgs.stream()).collect(Collectors.toList())) ||
                isSeasonShiftedToOptimizationWindow(modifiedCfgs, originalCfgs)) {
            enableLTBDE();
        }
    }

    private boolean isSeasonShiftedToOptimizationWindow(List<PriceAcceleratorCfg> modifiedCfgs, List<PriceAcceleratorCfg> originalCfgs) {
        return isSeasonChangedBeyondOptimisationWindow(originalCfgs.stream()
                .filter(originalRecord -> modifiedCfgs.stream()
                        .anyMatch(modifiedRecord -> (Objects.equals(modifiedRecord.getId(), originalRecord.getId()))
                                && (modifiedRecord.getJavaLocalEndDate().compareTo(originalRecord.getJavaLocalEndDate()) < 0)))
                .collect(Collectors.toList()));
    }

    private boolean isSeasonChangedBeyondOptimisationWindow(List<PriceAcceleratorCfg> priceAcceleratorCfgs) {
        return priceAcceleratorCfgs.stream().anyMatch(
                pr -> pricingConfigurationLTBDEService
                        .isDateBeyondOptimizationWindow(pr.getJavaLocalEndDate(), dateService.getCaughtUpJavaLocalDate()));
    }

    public List<String> validateServicingCosts(List<ServicingCosts> servicingCosts) {
        return validateServicingCostDTO(servicingCosts);
    }

    public void configureGroupPricingServicingCost(List<ServicingCosts> servicingCosts, Boolean preserveConfigurations) {
        List<GroupPricingConfiguration> groupPricingConfigurations = getGroupPricingConfigurationsPerRoomClass();
        setPerRoomServicingCost(groupPricingConfigurations, servicingCosts, preserveConfigurations);
        groupPricingConfigurations.add(getROHGroupPricingConfigurationForProperty(PacmanWorkContextHelper.getPropertyId()));
        save(groupPricingConfigurations);
    }

    private void setPerRoomServicingCost(List<GroupPricingConfiguration> groupPricingConfigurations, List<ServicingCosts> servicingCosts, Boolean preserveConfigurations) {
        for(GroupPricingConfiguration grpPricingConfig : groupPricingConfigurations) {

            String accomClassName = grpPricingConfig.getAccomClass().getName();
            Optional<ServicingCosts> serviceCost = servicingCosts.stream()
                    .filter(sc -> StringUtils.equalsIgnoreCase(sc.getRoomClass(), accomClassName))
                    .findFirst();
            BigDecimal cost;
            if (Boolean.FALSE.equals(preserveConfigurations)) {
                cost = serviceCost.isPresent() ? serviceCost.get().getServicingCost() : new BigDecimal(-1);
            } else if (preserveConfigurations && Objects.isNull(grpPricingConfig.getPerRoomServicingCost())) {
                cost = serviceCost.isPresent() ? serviceCost.get().getServicingCost() : new BigDecimal(-1);
            } else {
                LOGGER.info("AUTOMATION GROUP PRICING CONFIGURATION: GP Servicing cost config already present");
                cost = grpPricingConfig.getPerRoomServicingCost();
            }
            if(cost.compareTo(BigDecimal.ZERO) < 0) {
                    throw new IllegalArgumentException("Could not find Accom Class: " + accomClassName);
            }

            grpPricingConfig.setPerRoomServicingCost(cost);
        }
    }

    private List<String> validateServicingCostDTO(List<ServicingCosts> servicingCosts) {

        List<String> errors = new ArrayList<>();
        Set<String> accomClasses = servicingCosts.stream().map(ServicingCosts::getRoomClass).collect(Collectors.toSet());

        List<AccomClass> validAccomClasses = getAllActiveNonDefaultAccomClassByViewOrder();
        List<String> names = validAccomClasses.stream().map(AccomClass::getName).collect(Collectors.toList());

        if(accomClasses.stream().anyMatch(ac-> isNull(ac) || ac.isEmpty())) {
            errors.add("Accom Class cannot be 'NULL' or EMPTY.");
        }

        List<String> missingClasses = accomClasses.stream().filter(accomClass -> !names.contains(accomClass)).collect(Collectors.toList());
        if (!missingClasses.isEmpty()) {
            errors.add("Invalid Accom Classes: " +missingClasses);
        }

        servicingCosts.forEach(pair -> {
            if (pair.getServicingCost().compareTo(BigDecimal.ZERO) < 0) {
                errors.add("Servicing Cost should be zero or positive number for Accom Class: "+pair.getRoomClass());
            }
        });

        return errors;
    }

    public void configureGroupPricingPriceIncludedAccomTypes() {
        Set<AccomType> priceIncludedAccomTypes = getPriceIncludedAccomTypes();
        List<GroupPricingConfigAccomType> existingConfigTypes = getGroupPricingConfigAccomTypes();
        if (CollectionUtils.isNotEmpty(priceIncludedAccomTypes)) {
            List<GroupPricingConfigAccomType> groupPricingConfigAccomTypes = populateGroupPricingAccomtypes(priceIncludedAccomTypes, existingConfigTypes);
            if (CollectionUtils.isNotEmpty(groupPricingConfigAccomTypes)) {
                tenantCrudService.save(groupPricingConfigAccomTypes);
            }else{
                LOGGER.info("AUTOMATION GROUP PRICING CONFIGURATION: Failed to config room types");
            }
        }
    }

    private List<GroupPricingConfigAccomType> populateGroupPricingAccomtypes(Set<AccomType> priceIncludedAccomTypes, List<GroupPricingConfigAccomType> existingConfigTypes) {
        List<GroupPricingConfigAccomType> groupPricingConfigAccomTypes;
        if (CollectionUtils.isNotEmpty(existingConfigTypes)) {
            Set<AccomType> accomTypesToBeConfigure = priceIncludedAccomTypes.stream().filter(k -> existingConfigTypes.stream().noneMatch(e -> e.getAccomType().equals(k))).collect(Collectors.toSet());
            groupPricingConfigAccomTypes = createGroupPricingConfigAccomTypesUsing(accomTypesToBeConfigure, true);
        } else {
            groupPricingConfigAccomTypes = createGroupPricingConfigAccomTypesUsing(priceIncludedAccomTypes, true);
        }
        return groupPricingConfigAccomTypes;
    }

    public Set<AccomType> getPriceIncludedAccomTypes() {
        Set<AccomType> allActiveAccomTypes = new HashSet<>(getActiveAccomTypes());
        Set<AccomType> priceExcludedAccomType = pricingConfigurationService.getPriceExcludedPricingAccomClasses()
                .stream()
                .map(PricingAccomClass::getAccomType)
                .collect(Collectors.toSet());
        return allActiveAccomTypes
                .stream()
                .filter(accomType -> !priceExcludedAccomType.contains(accomType))
                .collect(Collectors.toSet());
    }
}
