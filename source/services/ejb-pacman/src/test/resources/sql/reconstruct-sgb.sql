-- Test data used in the integration test to reconstruct zero block/pickup records in Stage_Group_Block from Group_Block table.

SET IDENTITY_INSERT [dbo].[Group_Master] ON;

-- Case 340 Disqualified, Start_Date < snapshot start date and End_Date < BDE snapshot date  (either one alone is disqualifying)
INSERT [dbo].[Group_Master] ([Group_ID], [Property_ID], [Group_Code], [Group_Name], [Group_Description], [Master_Group_ID], [Master_Group_Code], [Group_Status_Code], [Group_Type_Code], [Mkt_Seg_ID], [Start_DT], [End_DT], [Booking_DT], [Pickup_Type_Code], [Cancel_DT], [Booking_type], [Sales_Person], [Cut_Off_date], [Cut_Off_days])
VALUES (340, 1965, '999340', '1017PROMUS_Principia College', 'Principia College', NULL, NULL, 'DEF', 'GROUP', 1,      '2008-08-31', '2008-09-05', '2016-06-28', NULL, NULL, NULL, NULL, NULL, NULL);
-- Case 341 Disqualified, but already present in S_G_B so it should remain as is
INSERT [dbo].[Group_Master] ([Group_ID], [Property_ID], [Group_Code], [Group_Name], [Group_Description], [Master_Group_ID], [Master_Group_Code], [Group_Status_Code], [Group_Type_Code], [Mkt_Seg_ID], [Start_DT], [End_DT], [Booking_DT], [Pickup_Type_Code], [Cancel_DT], [Booking_type], [Sales_Person], [Cut_Off_date], [Cut_Off_days])
VALUES (341, 1965, '999341', '1217GUNTHE_Gunther Tours', 'Gunther Tours', NULL, NULL, 'DEF', 'GROUP', 1,              '2008-08-31', '2008-09-22', '2016-09-07', NULL, NULL, NULL, NULL, NULL, NULL);
-- Case 342 Qualfied, Added to S_G_B
INSERT [dbo].[Group_Master] ([Group_ID], [Property_ID], [Group_Code], [Group_Name], [Group_Description], [Master_Group_ID], [Master_Group_Code], [Group_Status_Code], [Group_Type_Code], [Mkt_Seg_ID], [Start_DT], [End_DT], [Booking_DT], [Pickup_Type_Code], [Cancel_DT], [Booking_type], [Sales_Person], [Cut_Off_date], [Cut_Off_days])
VALUES (342, 1965, '999342', '1017ENCORE_Encore Group', 'Encore Group', NULL, NULL, 'CXL', 'GROUP', 1,                '2008-09-01', '2008-09-12', '2008-08-12', NULL, NULL, NULL, NULL, NULL, NULL);
-- Case 343 Qualified, Not Added because already present in S_G_B
INSERT [dbo].[Group_Master] ([Group_ID], [Property_ID], [Group_Code], [Group_Name], [Group_Description], [Master_Group_ID], [Master_Group_Code], [Group_Status_Code], [Group_Type_Code], [Mkt_Seg_ID], [Start_DT], [End_DT], [Booking_DT], [Pickup_Type_Code], [Cancel_DT], [Booking_type], [Sales_Person], [Cut_Off_date], [Cut_Off_days])
VALUES (343, 1965, '999343', '1017KROUSE_Krouse Group Travel', 'Krouse Group Travel', NULL, NULL, 'DEF', 'GROUP', 1,  '2008-09-01', '2008-09-11', '2016-10-13', NULL, NULL, NULL, NULL, NULL, NULL);
-- Case 344 Qualified, Added to S_G_B
INSERT [dbo].[Group_Master] ([Group_ID], [Property_ID], [Group_Code], [Group_Name], [Group_Description], [Master_Group_ID], [Master_Group_Code], [Group_Status_Code], [Group_Type_Code], [Mkt_Seg_ID], [Start_DT], [End_DT], [Booking_DT], [Pickup_Type_Code], [Cancel_DT], [Booking_type], [Sales_Person], [Cut_Off_date], [Cut_Off_days])
VALUES (344, 1965, '999344', '1217FREDST_Freds Travel World', 'Freds Travel World', NULL, NULL, 'DEF', 'GROUP', 1,    '2008-09-13', '2008-09-20', '2016-10-19', NULL, NULL, NULL, NULL, NULL, NULL);
-- Case 345 Disqualified, Not Added
INSERT [dbo].[Group_Master] ([Group_ID], [Property_ID], [Group_Code], [Group_Name], [Group_Description], [Master_Group_ID], [Master_Group_Code], [Group_Status_Code], [Group_Type_Code], [Mkt_Seg_ID], [Start_DT], [End_DT], [Booking_DT], [Pickup_Type_Code], [Cancel_DT], [Booking_type], [Sales_Person], [Cut_Off_date], [Cut_Off_days])
VALUES (345, 1965, '999345', 'AARP', 'AARP', NULL, NULL, 'DEF', 'GROUP', 1,                                           '2008-09-03', '2008-09-08', '2016-10-19', NULL, NULL, NULL, NULL, NULL, NULL);
-- Case 346 Qualified, Added to S_G_B
INSERT [dbo].[Group_Master] ([Group_ID], [Property_ID], [Group_Code], [Group_Name], [Group_Description], [Master_Group_ID], [Master_Group_Code], [Group_Status_Code], [Group_Type_Code], [Mkt_Seg_ID], [Start_DT], [End_DT], [Booking_DT], [Pickup_Type_Code], [Cancel_DT], [Booking_type], [Sales_Person], [Cut_Off_date], [Cut_Off_days])
VALUES (346, 1965, '999346', 'AARP2', 'AARP2', NULL, NULL, 'DEF', 'GROUP', 1,                                         '2008-09-05', '2008-09-13', '2016-10-19', NULL, NULL, NULL, NULL, NULL, NULL);

-- Stage_Group_Block  (sgb.group_id == gm.group_code)  (6 digits)
INSERT [opera].[Stage_Group_Block] ([Hotel_Code], [Group_ID], [Block_DT], [Forecast], [Room_Type], [Block], [Pickup], [Single_Occupancy], [Double_Occupancy], [Triple_Occupancy], [Quadruple_Occupancy], [Extra_Occupancy], [Single_Rate], [Double_Rate], [Triple_Rate], [Quadruple_Rate], [Extra_Rate], [Data_Load_Metadata_ID], [Accom_Type_ID], [G3_Rate_Value], [Business_Day_End_DT])
VALUES ('0001', 999341, '2008-09-01', 13, 'DLX', 13, 1, 0, 0, 0, 0, 0, 133.00, 279.00, 289.00 , 319.00, 0.00, (select top(1) data_load_metadata_id from opera.data_load_metadata), 4, 294.00, '2008-09-01');
INSERT [opera].[Stage_Group_Block] ([Hotel_Code], [Group_ID], [Block_DT], [Forecast], [Room_Type], [Block], [Pickup], [Single_Occupancy], [Double_Occupancy], [Triple_Occupancy], [Quadruple_Occupancy], [Extra_Occupancy], [Single_Rate], [Double_Rate], [Triple_Rate], [Quadruple_Rate], [Extra_Rate], [Data_Load_Metadata_ID], [Accom_Type_ID], [G3_Rate_Value], [Business_Day_End_DT])
VALUES ('0001', 999341, '2008-09-01', 13, 'STE', 13, 2, 0, 0, 0, 0, 0, 143.00, 279.00, 299.00, 319.00, 0.00, (select top(1) data_load_metadata_id from opera.data_load_metadata), 5, 294.00, '2008-09-01');

INSERT [opera].[Stage_Group_Block] ([Hotel_Code], [Group_ID], [Block_DT], [Forecast], [Room_Type], [Block], [Pickup], [Single_Occupancy], [Double_Occupancy], [Triple_Occupancy], [Quadruple_Occupancy], [Extra_Occupancy], [Single_Rate], [Double_Rate], [Triple_Rate], [Quadruple_Rate], [Extra_Rate], [Data_Load_Metadata_ID], [Accom_Type_ID], [G3_Rate_Value], [Business_Day_End_DT])
VALUES ('0001', 999343, '2008-09-03', 17, 'DLX', 17, 1, 0, 0, 0, 0, 0, 137.00, 299.00, 318.00, 320.00, 0.00, (select top(1) data_load_metadata_id from opera.data_load_metadata), 4, 299.00, '2008-09-16');
INSERT [opera].[Stage_Group_Block] ([Hotel_Code], [Group_ID], [Block_DT], [Forecast], [Room_Type], [Block], [Pickup], [Single_Occupancy], [Double_Occupancy], [Triple_Occupancy], [Quadruple_Occupancy], [Extra_Occupancy], [Single_Rate], [Double_Rate], [Triple_Rate], [Quadruple_Rate], [Extra_Rate], [Data_Load_Metadata_ID], [Accom_Type_ID], [G3_Rate_Value], [Business_Day_End_DT])
VALUES ('0001', 999343, '2008-09-03', 17, 'STE', 17, 2, 18, 0, 0, 0, 0, 147.00, 299.00, 319.00, 339.00, 0.00, (select top(1) data_load_metadata_id from opera.data_load_metadata), 5, 314.00, '2008-09-16')

-- Group_Block  (gm.group_id == gb.group_id)  (3 digits)
-- 340 Disqualified
INSERT [dbo].[Group_Block] ([Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate])
VALUES (340, '2008-09-10', 4, 40,1,40, 131.00);
INSERT [dbo].[Group_Block] ([Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate])
VALUES (340, '2008-09-10', 5, 40,2,40, 141.00);
-- 341 Disqualified, exists in S_G_B and should remain unchanged
INSERT [dbo].[Group_Block] ([Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate])
VALUES (341, '2008-09-01', 4, 41,1,41, 133.00);
INSERT [dbo].[Group_Block] ([Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate])
VALUES (341, '2008-09-01', 5, 41,2,41, 143.00);
-- 342 Should be added to S_G_B
INSERT [dbo].[Group_Block] ([Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate])
VALUES (342, '2008-09-02', 4, 42,1,42, 135.00);
INSERT [dbo].[Group_Block] ([Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate])
VALUES (342, '2008-09-02', 5, 42,2,42, 145.00);
-- 343 Qualified, exists in S_G_B and should remain unchanged
INSERT [dbo].[Group_Block] ([Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate])
VALUES (343, '2008-09-03', 4, 43,1,43, 137.00);
INSERT [dbo].[Group_Block] ([Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate])
VALUES (343, '2008-09-03', 5, 43,2,43, 147.00);
--  344 should be added to S_G_B
INSERT [dbo].[Group_Block] ([Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate])
VALUES (344, '2008-09-04', 4, 44,1,44, 139.00);
INSERT [dbo].[Group_Block] ([Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate])
VALUES (344, '2008-09-04', 5, 44,2,44, 149.00);
--  345 Disqualified, should NOT be added to S_G_B
INSERT [dbo].[Group_Block] ([Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate])
VALUES (345, '2008-09-05', 4, 45,1,45, 139.00);
INSERT [dbo].[Group_Block] ([Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate])
VALUES (345, '2008-09-05', 5, 45,2,45, 149.00);
--  346 should be added to S_G_B
INSERT [dbo].[Group_Block] ([Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate])
VALUES (346, '2008-09-06', 4, 46,1,46, 139.00);
INSERT [dbo].[Group_Block] ([Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate])
VALUES (346, '2008-09-06', 5, 46,2,46, 149.00);
INSERT [dbo].[Group_Block] ([Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate])
VALUES (346, '2008-09-12', 4, 46,1,46, 139.00);
INSERT [dbo].[Group_Block] ([Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate])
VALUES (346, '2008-09-12', 5, 46,2,46, 149.00);