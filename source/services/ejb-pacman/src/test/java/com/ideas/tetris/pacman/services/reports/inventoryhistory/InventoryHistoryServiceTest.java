package com.ideas.tetris.pacman.services.reports.inventoryhistory;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.recommendation.model.s3.RecommendationResponse;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.entity.UniqueDecisionCreator;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.Decision;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.decisiontranslator.entity.DecisionAckStatus;
import com.ideas.tetris.pacman.services.fplos.FplosService;
import com.ideas.tetris.pacman.services.fplos.FplosServiceTest;
import com.ideas.tetris.pacman.services.fplos.entity.DecisionQualifiedFPLOS;
import com.ideas.tetris.pacman.services.fplos.entity.PaceDecisionQualifiedFPLOS;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.qualifiedrate.entity.RateQualified;
import com.ideas.tetris.pacman.services.reports.inventoryhistory.dto.InventoryDecisionType;
import com.ideas.tetris.pacman.services.reports.inventoryhistory.dto.InventoryFPLOSLevel;
import com.ideas.tetris.pacman.services.reports.inventoryhistory.dto.InventoryHistoryParams;
import com.ideas.tetris.pacman.services.reports.inventoryhistory.dto.InventoryOverbookingLevel;
import com.ideas.tetris.pacman.services.unqualifiedrate.entity.UniqueRateQualified;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.Date;

import static com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName.ENABLE_PACE_REPORTING_FROM_SERVICE;
import static com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper.getPropertyId;
import static com.ideas.tetris.pacman.services.unqualifiedrate.entity.UniqueRateQualified.createRateQualifiedByDateAndName;
import static com.ideas.tetris.pacman.testdatabuilder.AccomClassBuilder.buildDefaultAccomClass;
import static com.ideas.tetris.pacman.testdatabuilder.AccomTypeBuilder.buildAccomType;
import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;
import static java.util.Objects.isNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

public class InventoryHistoryServiceTest extends AbstractG3JupiterTest {

    @InjectMocks
    private InventoryHistoryService inventoryHistoryService;

    @Mock
    PropertyService propertyService;

    private static String FPLOS = "FPLOS";
    private static String OVBK = "Overbooking";
    private static String YYYYYYY = "YYYYYYY";
    private static String NNNNNNN = "NNNNNNN";
    private static String ALL = "ALL";
    public static final String MINUS_ONE_AS_STRING = "-1";
    private static final String dummyDateTime = "'2015-01-01 10:30:15'";


    @Mock
    DateService dateService;

    @Mock
    PacmanConfigParamsService configParamsService;

    @Mock
    FplosService fplosService;

    @Mock
    DecisionService decisionService;

/*
AccomTypes
6	DBL
4	DLX
8	K
7	Q
5	STE
*/
/*
RateUnqualified
3	None
4	LV0
5	LV1
6	LV2
7	LV3
8	LV4
9	LV5
10	LV6
11	LV7
12	LV8
*/

    /*
    ufn_get_inventory_history_report
    Input Parameters :
    @property_id int -- possible values:14
    @start_date date -- possible values:'2011-07-31'
    @end_date date  -- possible values:'2011-08-09'
    @decision_type nvarchar(20) -- possible values:'all','FPLOS','Overbooking'
    @level nvarchar(10) -- possible values:'all', for 'FPLOS'->'RL','SRP,'RLSRP', for 'Overbooking'->'HOUSE','RT','RTHOUSE'
    @rate_level_list nvarchar(max) -- possible values:'all', Actual comma separated rateLevelIds
    @srp_list nvarchar(max) -- possible values:'all', Actual comma separeted srpIds
    @room_type_list nvarchar(max) -- possible values:'all', Actuall comma separated roomTypeIds
    @isSrpFpLosAtHotel int -- possible values:1,0
    @isLV0 int -- possible values:1,0
    @isRollingDate int -- possible values:1,0
    @rolling_start_date nvarchar(50) -- possible values:'TODAY', 'TODAY+...', 'TODAY-7'
    @rolling_end_date nvarchar(50) -- possible values:'TODAY', 'TODAY+...', 'TODAY-7'
    */

    @Test
    public void isGeneratedDataExceedingExcelLimits_FPLOS_RATE_LEVEL() {
        inventoryHistoryService.tenantCrudService = tenantCrudService();
        Date startDate = DateUtil.getCurrentDate();
        LocalDate now = LocalDate.now();
        Date endDate = now.plusDays(1).toDate();
        RateQualified lv0 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "LV0");
        RateQualified srp1 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP1");
        RateQualified srp2 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP2");
        RateQualified srp3 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP3");

        insertIntoDecisionACKStatus(null, now, 5, null, FPLOS, YYYYYYY, 0, now.toDate());
        insertIntoDecisionACKStatus(null, now, 5, null, FPLOS, YYYYYYY, 0, now.plusDays(1).toDate());
        insertIntoDecisionACKStatus(null, now, 5, null, FPLOS, YYYYYYY, 1001, now.minusDays(1).toDate());

        InventoryHistoryParams params = new InventoryHistoryParams();
        params.setStartDate(now.toString());
        params.setEndDate(now.toString());
        params.setInventoryDecisionType(InventoryDecisionType.FPLOS);
        params.setLevel(InventoryFPLOSLevel.RATE_LEVEL.getParamValue());
        params.setSelectedRateLevels("5");
        params.setSelectedSRP(MINUS_ONE_AS_STRING);
        params.setSelectedRoomTypes("6");
        params.setSRPFPLOSAtTotalEnabled(FALSE);
        params.setLV0Selected(FALSE);
        params.setRollingDate(FALSE);
        params.setIncludeSuccessfulControls("1");
        params.setIncludeFailures("1");
        params.setIncludePace(TRUE);
        Integer count = getCountOfDecisions(params);
        assertEquals(3, count);
        assertEquals(count, inventoryHistoryService.getCountOfRecords(params));

    }

    @Test
    public void isGeneratedDataExceedingExcelLimits_FPLOS_RATE_LEVEL_SUCCESS_PACE() {
        inventoryHistoryService.tenantCrudService = tenantCrudService();
        Date startDate = DateUtil.getCurrentDate();
        LocalDate now = LocalDate.now();
        Date endDate = now.plusDays(1).toDate();
        RateQualified lv0 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "LV0");
        RateQualified srp1 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP1");
        RateQualified srp2 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP2");
        RateQualified srp3 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP3");

        insertIntoDecisionACKStatus(null, now, 5, null, FPLOS, YYYYYYY, 0, now.toDate());
        insertIntoDecisionACKStatus(null, now, 5, null, FPLOS, YYYYYYY, 0, now.plusDays(1).toDate());
        insertIntoDecisionACKStatus(null, now, 5, null, FPLOS, YYYYYYY, 1001, now.minusDays(1).toDate());

        InventoryHistoryParams params = new InventoryHistoryParams();
        params.setStartDate(now.toString());
        params.setEndDate(now.toString());
        params.setInventoryDecisionType(InventoryDecisionType.FPLOS);
        params.setLevel(InventoryFPLOSLevel.RATE_LEVEL.getParamValue());
        params.setSelectedRateLevels("5");
        params.setSelectedSRP(MINUS_ONE_AS_STRING);
        params.setSelectedRoomTypes("6");
        params.setSRPFPLOSAtTotalEnabled(FALSE);
        params.setLV0Selected(FALSE);
        params.setRollingDate(FALSE);
        params.setIncludeSuccessfulControls("1");
        params.setIncludeFailures("0");
        params.setIncludePace(TRUE);
        Integer count = getCountOfDecisions(params);
        assertEquals(2, count);
        assertEquals(count, inventoryHistoryService.getCountOfRecords(params));

    }

    @Test
    public void isGeneratedDataExceedingExcelLimits_FPLOS_RATE_LEVEL_SUCCESS_CURRENT() {
        inventoryHistoryService.tenantCrudService = tenantCrudService();
        Date startDate = DateUtil.getCurrentDate();
        LocalDate now = LocalDate.now();
        Date endDate = now.plusDays(1).toDate();
        RateQualified lv0 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "LV0");
        RateQualified srp1 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP1");
        RateQualified srp2 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP2");
        RateQualified srp3 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP3");

        insertIntoDecisionACKStatus(null, now, 5, null, FPLOS, YYYYYYY, 0, now.toDate());
        insertIntoDecisionACKStatus(null, now, 5, null, FPLOS, YYYYYYY, 0, now.plusDays(1).toDate());
        insertIntoDecisionACKStatus(null, now, 5, null, FPLOS, YYYYYYY, 1001, now.minusDays(1).toDate());

        InventoryHistoryParams params = new InventoryHistoryParams();
        params.setStartDate(now.toString());
        params.setEndDate(now.toString());
        params.setInventoryDecisionType(InventoryDecisionType.FPLOS);
        params.setLevel(InventoryFPLOSLevel.RATE_LEVEL.getParamValue());
        params.setSelectedRateLevels("5");
        params.setSelectedSRP(MINUS_ONE_AS_STRING);
        params.setSelectedRoomTypes("6");
        params.setSRPFPLOSAtTotalEnabled(FALSE);
        params.setLV0Selected(FALSE);
        params.setRollingDate(FALSE);
        params.setIncludeSuccessfulControls("1");
        params.setIncludeFailures("0");
        params.setIncludePace(FALSE);
        Integer count = getCountOfDecisions(params);
        assertEquals(1, count);
        assertEquals(count, inventoryHistoryService.getCountOfRecords(params));

    }

    @Test
    public void isGeneratedDataExceedingExcelLimits_FPLOS_RATE_LEVEL_FAILURES_CURRENT() {
        inventoryHistoryService.tenantCrudService = tenantCrudService();
        Date startDate = DateUtil.getCurrentDate();
        LocalDate now = LocalDate.now();
        Date endDate = now.plusDays(1).toDate();
        RateQualified lv0 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "LV0");
        RateQualified srp1 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP1");
        RateQualified srp2 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP2");
        RateQualified srp3 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP3");

        insertIntoDecisionACKStatus(null, now, 5, null, FPLOS, YYYYYYY, 0, now.toDate());
        insertIntoDecisionACKStatus(null, now, 5, null, FPLOS, YYYYYYY, 1001, now.minusDays(1).toDate());
        insertIntoDecisionACKStatus(null, now, 5, null, FPLOS, YYYYYYY, 1001, now.plusDays(1).toDate());
        insertIntoDecisionACKStatus(null, now.plusDays(1), 5, null, FPLOS, YYYYYYY, 1001, now.plusDays(1).toDate());

        InventoryHistoryParams params = new InventoryHistoryParams();
        params.setStartDate(now.toString());
        params.setEndDate(now.plusDays(1).toString());
        params.setInventoryDecisionType(InventoryDecisionType.FPLOS);
        params.setLevel(InventoryFPLOSLevel.RATE_LEVEL.getParamValue());
        params.setSelectedRateLevels("5");
        params.setSelectedSRP(MINUS_ONE_AS_STRING);
        params.setSelectedRoomTypes("6");
        params.setSRPFPLOSAtTotalEnabled(FALSE);
        params.setLV0Selected(FALSE);
        params.setRollingDate(FALSE);
        params.setIncludeSuccessfulControls("0");
        params.setIncludeFailures("1");
        params.setIncludePace(FALSE);
        Integer count = getCountOfDecisions(params);
        assertEquals(2, count);
        assertEquals(count, inventoryHistoryService.getCountOfRecords(params));

    }

    @Test
    @Disabled("Flaky")
    public void isGeneratedDataExceedingExcelLimits_FPLOS_RATE_LEVEL_FAILURES_NonPace_SkipDecisionAckSuccessStatusSRPFPLOSEnabled() {
        inventoryHistoryService.tenantCrudService = tenantCrudService();
        LocalDate now = LocalDate.now();
        String startDt = "2020-01-01";
        String endDt = "2020-01-05";

        Decision paceDecision = UniqueDecisionCreator.createDecisionFor(getPropertyId(), new LocalDate().minusDays(1).toDate(), 14);
        paceDecision.setStartDate(now.minusDays(1).toDate());
        tenantCrudService().save(paceDecision);
        Decision currentDecision = UniqueDecisionCreator.createDecisionFor(getPropertyId(), new LocalDate().toDate(), 14);
        currentDecision.setStartDate(now.toDate());
        tenantCrudService().save(currentDecision);

        AccomClass testAccomClass = tenantCrudService().save(buildDefaultAccomClass(getPropertyId(), "TEST"));
        AccomType RT1 = tenantCrudService().save(buildAccomType(testAccomClass, "RT1"));
        AccomType RT2 = tenantCrudService().save(buildAccomType(testAccomClass, "RT2"));

        RateQualified srp1 = createRateQualifiedByDateAndName(LocalDate.parse(startDt).toDate(), LocalDate.parse(endDt).toDate(), getPropertyId(), "SRP1");

        createPaceDecision(srp1, "2020-01-01", "YYYYYYN", paceDecision, RT1.getId());
        createDecision(srp1, "2020-01-01", "YYYYYYY", currentDecision, RT1.getId());
        createPaceDecision(srp1, "2020-01-01", "NNNNNNY", paceDecision, RT2.getId());
        createDecision(srp1, "2020-01-01", "NNNNNNN", currentDecision, RT2.getId());

        createAcknowledgementForCurrentDecision(srp1, currentDecision, RT1, LocalDate.parse("2020-01-01"), "YYYYYYY", 1, "Error");

        InventoryHistoryParams params = new InventoryHistoryParams();
        params.setStartDate(startDt);
        params.setEndDate(endDt);
        params.setInventoryDecisionType(InventoryDecisionType.FPLOS);
        params.setLevel(InventoryFPLOSLevel.SRP.getParamValue());
        params.setSelectedSRP(String.valueOf(srp1.getId()));
        params.setSelectedRoomTypes(RT1.getId() + "," + RT2.getId());
        params.setSRPFPLOSAtTotalEnabled(FALSE);
        params.setLV0Selected(FALSE);
        params.setRollingDate(FALSE);
        params.setIncludeSuccessfulControls("0");
        params.setIncludeFailures("1");
        params.setIncludePace(FALSE);
        Integer count = getCountOfDecisions(params);
        assertEquals(1, count);
        assertEquals(count, inventoryHistoryService.getCountOfRecords(params));

    }

    public void createAcknowledgementForCurrentDecision(RateQualified rate, Decision currentDecision, AccomType roomType, LocalDate occupancyDate, String fplos, int errorCode,
                                                        String errorDescription) {
        DecisionAckStatus status = new DecisionAckStatus();
        status.setRateQualifiedId(rate.getId());
        status.setAccomTypeId(isNull(roomType) ? null : roomType.getId());
        status.setAckDateTime(new Date());
        status.setDecision(fplos);
        status.setDecisionDateTime(currentDecision.getCreateDate());
        status.setDecisionType("FPLOS");
        status.setErrorCode(errorCode);
        status.setErrorDesc(errorDescription);
        status.setOccupancyDate(occupancyDate.toDate());
        status.setPropertyId(getPropertyId());
        status.setTransactionId("T000001");
        status.setDecisionFileName(DateUtil.formatDate(currentDecision.getCaughtUpDate(), "'AAAAAA.'yyyyMMdd'.'HHmmss'.recom'"));
        tenantCrudService().save(status);
    }

    private void createDecision(RateQualified rate, String occupancyDate, String fplos, Decision d, Integer accomTypeID) {
        createPaceDecision(rate, occupancyDate, fplos, d, accomTypeID);
        DecisionQualifiedFPLOS decision = new DecisionQualifiedFPLOS();
        decision.setAccomTypeId(accomTypeID);
        decision.setArrivalDate(LocalDate.parse(occupancyDate).toDate());
        decision.setCreateDate(d.getStartDate());
        decision.setDecisionId(d.getId());
        decision.setRateQualifiedId(rate.getId());
        decision.setPropertyId(getPropertyId());
        decision.setFplos(fplos);
        tenantCrudService().save(decision);
    }

    private void createPaceDecision(RateQualified rate, String occupancyDate, String fplos, Decision d, Integer accomTypeId) {
        PaceDecisionQualifiedFPLOS decision = new PaceDecisionQualifiedFPLOS();
        decision.setAccomTypeId(accomTypeId);
        decision.setArrivalDate(LocalDate.parse(occupancyDate).toDate());
        decision.setCreateDate(d.getStartDate());
        decision.setDecisionId(d.getId());
        decision.setRateQualifiedId(rate.getId());
        decision.setPropertyId(getPropertyId());
        decision.setFplos(fplos);
        tenantCrudService().save(decision);
    }

    private Integer getCountOfDecisions(InventoryHistoryParams params) {
        return tenantCrudService().findByNativeQuerySingleResult("select count(*) from ufn_get_inventory_history_report (5, '" + params.getStartDate() + "', '" + params.getEndDate() + "'," +
                        " '" + params.getInventoryDecisionType() + "', '" + params.getLevel() + "', " +
                        " '" + params.getSelectedRateLevels() + "', '" + params.getSelectedSRP() + "', '" + params.getSelectedRoomTypes() + "'," +
                        " " + (params.isSRPFPLOSAtTotalEnabled() ? 1 : 0) + ", " + (params.isLV0Selected() ? 1 : 0) + ", " + (params.isRollingDate() ? 1 : 0) + ", '" + (params.getRollingStartDate() == null ? "" : params.getRollingStartDate()) + "', '" + (params.getRollingEndDate() == null ? "" : params.getRollingEndDate())
                        + "'," + params.isIncludeSuccessfulControls() + "," + params.isIncludeFailures() + "," + (params.isIncludePace() ? 1 : 0) + "," + (params.isIncludeConsortia() ? 1 : 0) + ",1,0,'TwoWay', "+dummyDateTime+");"
                , null);
    }

    public DecisionAckStatus insertIntoDecisionACKStatus(Integer accomTypeId, LocalDate occupancyDate, Integer rateUnqualifiedId,
                                                         Integer rateQualifiedId, String decisionType, String decision, int errorCode, Date decisionDateTime) {
        DecisionAckStatus decisionAckStatus = new DecisionAckStatus();
        decisionAckStatus.setPropertyId(PacmanWorkContextHelper.getPropertyId());
        decisionAckStatus.setAccomTypeId(accomTypeId);
        decisionAckStatus.setDecisionFileName("H1.20190319.001600.recom");
        decisionAckStatus.setTransactionId("TID000000212");
        decisionAckStatus.setOccupancyDate(occupancyDate.toDate());
        decisionAckStatus.setRateUnqualifiedId(rateUnqualifiedId);
        decisionAckStatus.setRateQualifiedId(rateQualifiedId);
        decisionAckStatus.setDecisionType(decisionType);
        decisionAckStatus.setDecision(decision);
        decisionAckStatus.setDecisionDateTime(decisionDateTime);
        decisionAckStatus.setAckDateTime(occupancyDate.toDate());
        decisionAckStatus.setErrorCode(errorCode);
        decisionAckStatus.setErrorDesc("Success");
        return tenantCrudService().save(decisionAckStatus);
    }

    @Test
    public void isGeneratedDataExceedingExcelLimits_FPLOS_SRP_LEVEL() {
        inventoryHistoryService.tenantCrudService = tenantCrudService();
        Date startDate = DateUtil.getCurrentDate();
        LocalDate now = LocalDate.now();
        Date endDate = now.plusDays(1).toDate();
        RateQualified lv0 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "LV0");
        RateQualified srp1 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP1");
        RateQualified srp2 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP2");
        RateQualified srp3 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP3");

        insertIntoDecisionACKStatus(6, now, null, srp1.getId(), FPLOS, YYYYYYY, 0, now.toDate());

        InventoryHistoryParams params = new InventoryHistoryParams();
        params.setStartDate(now.toString());
        params.setEndDate(now.toString());
        params.setInventoryDecisionType(InventoryDecisionType.FPLOS);
        params.setLevel(InventoryFPLOSLevel.SRP.getParamValue());
        params.setSelectedRateLevels(MINUS_ONE_AS_STRING);
        params.setSelectedSRP(srp1.getId().toString());
        params.setSelectedRoomTypes("6");
        params.setSRPFPLOSAtTotalEnabled(FALSE);
        params.setLV0Selected(FALSE);
        params.setRollingDate(FALSE);
        params.setIncludeSuccessfulControls("1");
        params.setIncludeFailures("1");
        params.setIncludePace(TRUE);
        Integer count = getCountOfDecisions(params);
        assertEquals(count, inventoryHistoryService.getCountOfRecords(params));
    }

    @Test
    public void isGeneratedDataExceedingExcelLimits_FPLOS_SRP_RT_LEVEL() {
        inventoryHistoryService.tenantCrudService = tenantCrudService();
        Date startDate = DateUtil.getCurrentDate();
        LocalDate now = LocalDate.now();
        Date endDate = now.plusDays(1).toDate();
        RateQualified lv0 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "LV0");
        RateQualified srp1 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP1");
        RateQualified srp2 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP2");
        RateQualified srp3 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP3");

        insertIntoDecisionACKStatus(6, now, null, srp1.getId(), FPLOS, YYYYYYY, 0, now.toDate());
        insertIntoDecisionACKStatus(null, now, 5, null, FPLOS, YYYYYYY, 0, now.toDate());
//        tenantCrudService().getEntityManager().getTransaction().commit();

        InventoryHistoryParams params = new InventoryHistoryParams();
        params.setStartDate(now.toString());
        params.setEndDate(now.toString());
        params.setInventoryDecisionType(InventoryDecisionType.FPLOS);
        params.setLevel(InventoryFPLOSLevel.RATE_LEVEL_AND_SRP.getParamValue());
        params.setSelectedRateLevels("5");
        params.setSelectedSRP(srp1.getId().toString());
        params.setSelectedRoomTypes("6");
        params.setSRPFPLOSAtTotalEnabled(FALSE);
        params.setLV0Selected(FALSE);
        params.setRollingDate(FALSE);
        params.setIncludeSuccessfulControls("1");
        params.setIncludeFailures("1");
        params.setIncludePace(TRUE);
        Integer count = getCountOfDecisions(params);
        assertEquals(Integer.valueOf(1), count);
        assertEquals(count, inventoryHistoryService.getCountOfRecords(params));
    }

    @Test
    public void isGeneratedDataExceedingExcelLimits_PROP_OVBK_LEVEL() {
        inventoryHistoryService.tenantCrudService = tenantCrudService();
        Date startDate = DateUtil.getCurrentDate();
        LocalDate now = LocalDate.now();
        Date endDate = now.plusDays(1).toDate();
        RateQualified lv0 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "LV0");
        RateQualified srp1 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP1");
        RateQualified srp2 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP2");
        RateQualified srp3 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP3");

        insertIntoDecisionACKStatus(null, now, null, null, OVBK, YYYYYYY, 0, now.toDate());

        InventoryHistoryParams params = new InventoryHistoryParams();
        params.setStartDate(now.toString());
        params.setEndDate(now.toString());
        params.setInventoryDecisionType(InventoryDecisionType.OVERBOOKING);
        params.setLevel(InventoryOverbookingLevel.HOUSE.getParamValue());
        params.setSelectedRateLevels(MINUS_ONE_AS_STRING);
        params.setSelectedSRP(MINUS_ONE_AS_STRING);
        params.setSelectedRoomTypes(MINUS_ONE_AS_STRING);
        params.setSRPFPLOSAtTotalEnabled(FALSE);
        params.setLV0Selected(FALSE);
        params.setRollingDate(FALSE);
        params.setIncludeSuccessfulControls("1");
        params.setIncludeFailures("1");
        params.setIncludePace(TRUE);
        Integer count = getCountOfDecisions(params);
        assertEquals(Integer.valueOf(1), count);
        assertEquals(count, inventoryHistoryService.getCountOfRecords(params));
    }

    @Test
    public void isGeneratedDataExceedingExcelLimits_RT_OVBK_LEVEL() {
        inventoryHistoryService.tenantCrudService = tenantCrudService();
        Date startDate = DateUtil.getCurrentDate();
        LocalDate now = LocalDate.now();
        Date endDate = now.plusDays(1).toDate();
        RateQualified lv0 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "LV0");
        RateQualified srp1 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP1");
        RateQualified srp2 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP2");
        RateQualified srp3 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP3");

        insertIntoDecisionACKStatus(6, now, null, null, OVBK, YYYYYYY, 0, now.toDate());
//        tenantCrudService().getEntityManager().getTransaction().commit();

        InventoryHistoryParams params = new InventoryHistoryParams();
        params.setStartDate(now.toString());
        params.setEndDate(now.toString());
        params.setInventoryDecisionType(InventoryDecisionType.OVERBOOKING);
        params.setLevel(InventoryOverbookingLevel.ROOM_TYPE.getParamValue());
        params.setSelectedRateLevels(MINUS_ONE_AS_STRING);
        params.setSelectedSRP(MINUS_ONE_AS_STRING);
        params.setSelectedRoomTypes("6");
        params.setSRPFPLOSAtTotalEnabled(FALSE);
        params.setLV0Selected(FALSE);
        params.setRollingDate(FALSE);
        params.setIncludeSuccessfulControls("1");
        params.setIncludeFailures("1");
        params.setIncludePace(TRUE);
        Integer count = getCountOfDecisions(params);
        assertEquals(Integer.valueOf(1), count);
        assertEquals(count, inventoryHistoryService.getCountOfRecords(params));
    }

    @Test
    public void isGeneratedDataExceedingExcelLimits_ALL() {
        inventoryHistoryService.tenantCrudService = tenantCrudService();
        Date startDate = DateUtil.getCurrentDate();
        LocalDate now = LocalDate.now();
        Date endDate = now.plusDays(1).toDate();
        RateQualified lv0 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "LV0");
        RateQualified srp1 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP1");
        RateQualified srp2 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP2");
        RateQualified srp3 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP3");

        insertIntoDecisionACKStatus(6, now, null, srp1.getId(), FPLOS, YYYYYYY, 0, now.toDate());
        insertIntoDecisionACKStatus(null, now, 5, null, FPLOS, YYYYYYY, 0, now.toDate());
        insertIntoDecisionACKStatus(null, now, null, null, OVBK, YYYYYYY, 0, now.toDate());
        insertIntoDecisionACKStatus(6, now, null, null, OVBK, YYYYYYY, 0, now.toDate());
        insertIntoDecisionACKStatus(6, now, 5, null, "DailyBar", "100", 0, now.toDate());

        InventoryHistoryParams params = new InventoryHistoryParams();
        params.setStartDate(now.toString());
        params.setEndDate(now.toString());
        params.setInventoryDecisionType(InventoryDecisionType.ALL);
        params.setLevel(ALL);
        params.setSelectedRateLevels(ALL);
        params.setSelectedSRP(ALL);
        params.setSelectedRoomTypes(ALL);
        params.setSRPFPLOSAtTotalEnabled(FALSE);
        params.setLV0Selected(FALSE);
        params.setRollingDate(FALSE);
        params.setIncludeSuccessfulControls("1");
        params.setIncludeFailures("1");
        params.setIncludePace(TRUE);
        Integer count = getCountOfDecisions(params);
        assertEquals(Integer.valueOf(4), count);
        assertEquals(count, inventoryHistoryService.getCountOfRecords(params));
    }

    @Test
    public void isGeneratedDataExceedingExcelLimits_ALL_LV0() {
        inventoryHistoryService.tenantCrudService = tenantCrudService();
        Date startDate = DateUtil.getCurrentDate();
        LocalDate now = LocalDate.now();
        Date endDate = now.plusDays(1).toDate();
        RateQualified lv0 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "LV0");
        RateQualified srp1 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP1");
        RateQualified srp2 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP2");
        RateQualified srp3 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP3");

        insertIntoDecisionACKStatus(6, now, null, lv0.getId(), FPLOS, YYYYYYY, 0, now.toDate());
        insertIntoDecisionACKStatus(6, now, null, srp1.getId(), FPLOS, YYYYYYY, 0, now.toDate());
        insertIntoDecisionACKStatus(null, now, 5, null, FPLOS, YYYYYYY, 0, now.toDate());
        insertIntoDecisionACKStatus(null, now, null, null, OVBK, YYYYYYY, 0, now.toDate());
        insertIntoDecisionACKStatus(6, now, null, null, OVBK, YYYYYYY, 0, now.toDate());
//        tenantCrudService().getEntityManager().getTransaction().commit();

        InventoryHistoryParams params = new InventoryHistoryParams();
        params.setStartDate(now.toString());
        params.setEndDate(now.toString());
        params.setInventoryDecisionType(InventoryDecisionType.ALL);
        params.setLevel(ALL);
        params.setSelectedRateLevels(ALL);
        params.setSelectedSRP(ALL);
        params.setSelectedRoomTypes(ALL);
        params.setSRPFPLOSAtTotalEnabled(FALSE);
        params.setLV0Selected(TRUE);
        params.setRollingDate(FALSE);
        params.setIncludeSuccessfulControls("1");
        params.setIncludeFailures("1");
        params.setIncludePace(TRUE);
        Integer count = getCountOfDecisions(params);
        assertEquals(Integer.valueOf(3), count);
        assertEquals(4, inventoryHistoryService.getCountOfRecords(params));
    }

    @Test
    public void isGeneratedDataExceedingExcelLimits_SRPAtTotal() {
        inventoryHistoryService.tenantCrudService = tenantCrudService();
        Date startDate = DateUtil.getCurrentDate();
        LocalDate now = LocalDate.now();
        Date endDate = now.plusDays(1).toDate();
        RateQualified lv0 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "LV0");
        RateQualified srp1 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP1");
        RateQualified srp2 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP2");
        RateQualified srp3 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP3");

        insertIntoDecisionACKStatus(null, now, null, srp1.getId(), FPLOS, YYYYYYY, 0, now.toDate());
        insertIntoDecisionACKStatus(null, now, null, srp2.getId(), FPLOS, YYYYYYY, 0, now.toDate());
        insertIntoDecisionACKStatus(null, now, null, srp3.getId(), OVBK, YYYYYYY, 0, now.toDate());
        insertIntoDecisionACKStatus(6, now, null, lv0.getId(), OVBK, YYYYYYY, 0, now.toDate());

        InventoryHistoryParams params = new InventoryHistoryParams();
        params.setStartDate(now.toString());
        params.setEndDate(now.toString());
        params.setInventoryDecisionType(InventoryDecisionType.ALL);
        params.setLevel(ALL);
        params.setSelectedRateLevels(ALL);
        params.setSelectedSRP(ALL);
        params.setSelectedRoomTypes(ALL);
        params.setSRPFPLOSAtTotalEnabled(TRUE);
        params.setLV0Selected(TRUE);
        params.setRollingDate(FALSE);
        params.setIncludeSuccessfulControls("1");
        params.setIncludeFailures("1");
        params.setIncludePace(TRUE);
        Integer count = getCountOfDecisions(params);
        assertEquals(Integer.valueOf(2), count);
        assertEquals(count, inventoryHistoryService.getCountOfRecords(params));
    }

    @Test
    public void isGeneratedDataExceedingExcelLimits_SRPAtTotal_SkipDecisionAckSuccessStatusSRPFPLOSEnabled() {
        inventoryHistoryService.tenantCrudService = tenantCrudService();
        LocalDate now = LocalDate.now();
        String startDt = "2020-01-01";
        String endDt = "2020-01-05";

        Decision paceDecision = UniqueDecisionCreator.createDecisionFor(getPropertyId(), new LocalDate().minusDays(1).toDate(), 14);
        paceDecision.setStartDate(now.minusDays(1).toDate());
        tenantCrudService().save(paceDecision);
        Decision currentDecision = UniqueDecisionCreator.createDecisionFor(getPropertyId(), new LocalDate().toDate(), 14);
        currentDecision.setStartDate(now.toDate());
        tenantCrudService().save(currentDecision);

        AccomClass testAccomClass = tenantCrudService().save(buildDefaultAccomClass(getPropertyId(), "TEST"));
        AccomType RT1 = tenantCrudService().save(buildAccomType(testAccomClass, "RT1"));

        RateQualified lv0 = createRateQualifiedByDateAndName(LocalDate.parse(startDt).toDate(), LocalDate.parse(endDt).toDate(), getPropertyId(), "LV0");
        RateQualified srp1 = createRateQualifiedByDateAndName(LocalDate.parse(startDt).toDate(), LocalDate.parse(endDt).toDate(), getPropertyId(), "SRP1");
        RateQualified srp2 = createRateQualifiedByDateAndName(LocalDate.parse(startDt).toDate(), LocalDate.parse(endDt).toDate(), getPropertyId(), "SRP2");
        RateQualified srp3 = createRateQualifiedByDateAndName(LocalDate.parse(startDt).toDate(), LocalDate.parse(endDt).toDate(), getPropertyId(), "SRP3");

        createPaceDecision(srp1, "2020-01-01", "YYYNNNY", paceDecision, RT1.getId());
        createDecision(srp1, "2020-01-01", "YYNNYYN", currentDecision, RT1.getId());

        insertIntoDecisionACKStatus(null, LocalDate.parse(startDt), null, srp1.getId(), FPLOS, YYYYYYY, 1001, LocalDate.parse(startDt).toDate());
        insertIntoDecisionACKStatus(null, LocalDate.parse(startDt), null, srp2.getId(), FPLOS, YYYYYYY, 1001, LocalDate.parse(startDt).toDate());
        insertIntoDecisionACKStatus(null, LocalDate.parse(startDt), null, srp3.getId(), OVBK, YYYYYYY, 1001, LocalDate.parse(startDt).toDate());
        insertIntoDecisionACKStatus(6, LocalDate.parse(startDt), null, lv0.getId(), OVBK, YYYYYYY, 1001, LocalDate.parse(startDt).toDate());

        InventoryHistoryParams params = new InventoryHistoryParams();
        params.setStartDate(startDt);
        params.setEndDate(endDt);
        params.setInventoryDecisionType(InventoryDecisionType.FPLOS);
        params.setLevel(InventoryFPLOSLevel.SRP.getParamValue());
        params.setSelectedRateLevels(ALL);
        params.setSelectedSRP(ALL);
        params.setSelectedRoomTypes(ALL);
        params.setSRPFPLOSAtTotalEnabled(TRUE);
        params.setLV0Selected(TRUE);
        params.setRollingDate(FALSE);
        params.setIncludeSuccessfulControls("1");
        params.setIncludeFailures("1");
        params.setIncludePace(TRUE);
        Integer count = getCountOfDecisions(params);
        assertEquals(Integer.valueOf(2), count);
        assertEquals(count, inventoryHistoryService.getCountOfRecords(params));
    }

    @Test
    public void isGeneratedDataExceedingExcelLimits_SRPAtTotal_ROLLING_DATE() {
        inventoryHistoryService.tenantCrudService = tenantCrudService();
        Date startDate = DateUtil.getCurrentDate();
        LocalDate now = LocalDate.now();
        Date endDate = now.plusDays(1).toDate();
        when(dateService.getCaughtUpLocalDate()).thenReturn(now);
        RateQualified lv0 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "LV0");
        RateQualified srp1 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP1");
        RateQualified srp2 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP2");
        RateQualified srp3 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP3");

        insertIntoDecisionACKStatus(null, now, null, srp1.getId(), FPLOS, YYYYYYY, 0, now.toDate());
        insertIntoDecisionACKStatus(null, now, null, srp2.getId(), FPLOS, YYYYYYY, 0, now.toDate());
        insertIntoDecisionACKStatus(null, now, null, srp3.getId(), OVBK, YYYYYYY, 0, now.toDate());
        insertIntoDecisionACKStatus(6, now.plusDays(30), null, lv0.getId(), OVBK, YYYYYYY, 0, now.plusDays(30).toDate());

        InventoryHistoryParams params = new InventoryHistoryParams();
        params.setRollingStartDate("TODAY");
        params.setRollingEndDate("TODAY+30");
        params.setInventoryDecisionType(InventoryDecisionType.ALL);
        params.setLevel(ALL);
        params.setSelectedRateLevels(ALL);
        params.setSelectedSRP(ALL);
        params.setSelectedRoomTypes(ALL);
        params.setSRPFPLOSAtTotalEnabled(TRUE);
        params.setLV0Selected(TRUE);
        params.setRollingDate(TRUE);
        params.setIncludeSuccessfulControls("1");
        params.setIncludeFailures("1");
        params.setIncludePace(TRUE);
        assertEquals(Integer.valueOf(2), inventoryHistoryService.getCountOfRecords(params));
    }

    @Test
    public void isGeneratedDataExceedingExcelLimits_SRPAtTotal_ROLLING_DATE_DURATION() {
        inventoryHistoryService.tenantCrudService = tenantCrudService();
        Date startDate = DateUtil.getCurrentDate();
        LocalDate now = LocalDate.now();
        Date endDate = now.plusDays(1).toDate();
        when(dateService.getCaughtUpLocalDate()).thenReturn(now);
        RateQualified lv0 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "LV0");
        RateQualified srp1 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP1");
        RateQualified srp2 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP2");
        RateQualified srp3 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP3");

        insertIntoDecisionACKStatus(null, now.minusDays(30), null, srp1.getId(), FPLOS, YYYYYYY, 0, now.minusDays(30).toDate());
        insertIntoDecisionACKStatus(null, now, null, srp2.getId(), FPLOS, YYYYYYY, 0, now.toDate());
        insertIntoDecisionACKStatus(null, now, null, srp3.getId(), OVBK, YYYYYYY, 0, now.toDate());
        insertIntoDecisionACKStatus(6, now.plusDays(30), null, lv0.getId(), OVBK, YYYYYYY, 0, now.plusDays(30).toDate());

        InventoryHistoryParams params = new InventoryHistoryParams();
        params.setRollingStartDate("TODAY-30");
        params.setRollingEndDate("TODAY");
        params.setInventoryDecisionType(InventoryDecisionType.ALL);
        params.setLevel(ALL);
        params.setSelectedRateLevels(ALL);
        params.setSelectedSRP(ALL);
        params.setSelectedRoomTypes(ALL);
        params.setSRPFPLOSAtTotalEnabled(TRUE);
        params.setLV0Selected(TRUE);
        params.setRollingDate(TRUE);
        params.setIncludeSuccessfulControls("1");
        params.setIncludeFailures("1");
        params.setIncludePace(TRUE);
        assertEquals(Integer.valueOf(1), inventoryHistoryService.getCountOfRecords(params));
    }

    @Test
    public void isGeneratedDataExceedingExcelLimits_SRPAtTotal_RT_FILTER() {
        inventoryHistoryService.tenantCrudService = tenantCrudService();
        Date startDate = DateUtil.getCurrentDate();
        LocalDate now = LocalDate.now();
        Date endDate = now.plusDays(1).toDate();
        when(dateService.getCaughtUpLocalDate()).thenReturn(now);
        RateQualified lv0 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "LV0");
        RateQualified srp1 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP1");
        RateQualified srp2 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP2");
        RateQualified srp3 = UniqueRateQualified.createRateQualifiedByDateAndName(startDate, endDate, PacmanWorkContextHelper.getPropertyId(), "SRP3");

        insertIntoDecisionACKStatus(5, now.minusDays(30), null, srp1.getId(), FPLOS, YYYYYYY, 0, now.minusDays(30).toDate());
        insertIntoDecisionACKStatus(6, now, null, srp2.getId(), FPLOS, YYYYYYY, 0, now.toDate());
        insertIntoDecisionACKStatus(7, now, null, srp3.getId(), FPLOS, YYYYYYY, 0, now.toDate());
        insertIntoDecisionACKStatus(8, now.plusDays(30), null, lv0.getId(), FPLOS, YYYYYYY, 0, now.plusDays(30).toDate());

        InventoryHistoryParams params = new InventoryHistoryParams();
        params.setRollingStartDate("TODAY-30");
        params.setRollingEndDate("TODAY+30");
        params.setInventoryDecisionType(InventoryDecisionType.FPLOS);
        params.setLevel(InventoryFPLOSLevel.SRP.getParamValue());
        params.setSelectedRateLevels(MINUS_ONE_AS_STRING);
        params.setSelectedSRP(ALL);
        params.setSelectedRoomTypes("5,7,8");
        params.setSRPFPLOSAtTotalEnabled(FALSE);
        params.setLV0Selected(TRUE);
        params.setRollingDate(TRUE);
        params.setIncludeSuccessfulControls("1");
        params.setIncludeFailures("1");
        params.setIncludePace(TRUE);
        assertEquals(Integer.valueOf(1), inventoryHistoryService.getCountOfRecords(params));
    }

    @Test
    void testGetVirtualPropertyDisplayCode() {
        when(propertyService.getVirtualPropertyDisplayCode(any())).thenReturn("TEST_VP_CODE");
        assertEquals("TEST_VP_CODE", inventoryHistoryService.getVirtualPropertyDisplayCode(1));
        verify(propertyService, times(1)).getVirtualPropertyDisplayCode(1);
    }

    @Test
    void testPopulatePaceFromServiceMultipleInserts() {
        inventoryHistoryService.tenantCrudService = tenantCrudService();

        inventoryHistoryService.truncatePaceFromServiceTable();
        String sql = "INSERT INTO PACE_From_Service(arrival_dt,rate_qualified_id,accom_type_id,fplos,decision_id) VALUES ('2023-03-05',9999,2,'YYYYYYY',1);INSERT INTO PACE_From_Service(arrival_dt,rate_qualified_id,accom_type_id,fplos,decision_id) VALUES ('2023-03-05',9999,2,'YYYNYYY',2),('2023-03-03',9999,2,'NNNNNNN',3);";
        tenantCrudService().executeUpdateByNativeQuery(sql);
        var paceList = tenantCrudService().findByNativeQuery("select * from PACE_From_Service ");

        assertEquals(3, paceList.size());
    }

    @Test
    void isGeneratedDataExceedingExcelLimits() {
        inventoryHistoryService.tenantCrudService = tenantCrudService();
        var params = getInventoryHistoryParams();
        var pace = getPace();
        pace.setDecisionCount(1048577);

        when(fplosService.retrievePace(any(), any(), anyList(), anyList())).thenReturn(getPace());
        when(dateService.getCaughtUpLocalDate()).thenReturn(LocalDate.now());
        when(configParamsService.getBooleanParameterValue(ENABLE_PACE_REPORTING_FROM_SERVICE)).thenReturn(true);

        var response = inventoryHistoryService.isGeneratedDataExceedingExcelLimits(params);
        assertEquals(1048577, response.getActualNoOfRecords());
        assertTrue(response.isLimitExceeding());
    }

    @Test
    void populatePace(){
        inventoryHistoryService.tenantCrudService = tenantCrudService();
        var decision = new Decision();
        decision.setCreateDate(LocalDate.now().toDate());

        when(decisionService.find(10000)).thenReturn(decision);

        inventoryHistoryService.populatePace(getPace());
        var paceRecords = tenantCrudService().findByNativeQuery("select * from PACE_From_Service");
        assertEquals(9,paceRecords.size());
    }

    @Test
    void getSelectedRateIdsAndRateLevelIds() {
        var params = new InventoryHistoryParams();
        params.setSelectedSRP("7777,8888,9999");
        params.setSelectedRateLevels("1,2,3");

        var result = inventoryHistoryService.getSelectedRateIdsAndRateLevelIds(params);
        assertEquals(6, result.size());
        assertEquals(7777, result.get(0));
        assertEquals(1, result.get(3));
    }

    @Test
    void getSelectedAccomTypeIds() {
        var params = new InventoryHistoryParams();
        params.setSelectedRoomTypes("1,2,3");

        var result = inventoryHistoryService.getSelectedAccomTypeIds(params);
        assertEquals(3, result.size());
        assertEquals(1, result.get(0));
    }

    private static InventoryHistoryParams getInventoryHistoryParams() {
        var params = new InventoryHistoryParams();
        params.setSelectedSRP("7777,8888,9999");
        params.setSelectedRateLevels("1,2,3");
        params.setSelectedRoomTypes("1,2,3");
        params.setRollingStartDate("TODAY");
        params.setRollingEndDate("TODAY+30");
        params.setStartDate(LocalDate.now().toString());
        params.setEndDate(LocalDate.now().plusDays(30).toString());
        params.setRollingDate(true);
        params.setInventoryDecisionType(InventoryDecisionType.FPLOS);
        return params;
    }

    private RecommendationResponse getPace() {
        var pace = new RecommendationResponse();
        pace.add(7777, java.time.LocalDate.now(), 1, new RecommendationResponse.Fplos("YYYYYYY", 10000));
        pace.add(7777, java.time.LocalDate.now(), 2, new RecommendationResponse.Fplos("YYYYYYY", 10000));
        pace.add(7777, java.time.LocalDate.now(), 3, new RecommendationResponse.Fplos("YYYYYYY", 10000));
        pace.add(8888, java.time.LocalDate.now(), 1, new RecommendationResponse.Fplos("YYYYYYY", 10000));
        pace.add(8888, java.time.LocalDate.now(), 2, new RecommendationResponse.Fplos("YYYYYYY", 10000));
        pace.add(8888, java.time.LocalDate.now(), 3, new RecommendationResponse.Fplos("YYYYYYY", 10000));
        pace.add(9999, java.time.LocalDate.now(), 1, new RecommendationResponse.Fplos("YYYYYYY", 10000));
        pace.add(9999, java.time.LocalDate.now(), 2, new RecommendationResponse.Fplos("YYYYYYY", 10000));
        pace.add(9999, java.time.LocalDate.now(), 3, new RecommendationResponse.Fplos("YYYYYYY", 10000));
        pace.setDecisionCount(1048577);

        FplosServiceTest.compress(pace);
        return pace;
    }
}