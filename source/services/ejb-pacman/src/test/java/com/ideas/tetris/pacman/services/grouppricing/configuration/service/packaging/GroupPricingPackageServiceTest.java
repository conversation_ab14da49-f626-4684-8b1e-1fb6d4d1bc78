package com.ideas.tetris.pacman.services.grouppricing.configuration.service.packaging;

import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.functionspace.configuration.dto.PackageDTO;
import com.ideas.tetris.pacman.services.functionspace.configuration.dto.PackageElementMapDTO;
import com.ideas.tetris.pacman.services.functionspace.configuration.entity.*;
import com.ideas.tetris.pacman.services.functionspace.configuration.packaging.dto.PackageElementDto;
import com.ideas.tetris.pacman.services.functionspace.configuration.packaging.dto.RevenueGroupDto;
import com.ideas.tetris.pacman.services.functionspace.configuration.service.FunctionSpacePackageService;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigurationConferenceAndBanquet;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.packaging.GroupPricingConfigurationPackage;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.packaging.GroupPricingConfigurationPackageElement;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.packaging.GroupPricingConfigurationPackageElementMap;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyCollection;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith({MockitoExtension.class})
class GroupPricingPackageServiceTest {

    @InjectMocks
    private GroupPricingPackageService groupPricingPackageService;

    @Mock
    private CrudService tenantCrudService;
    @Mock
    private PacmanConfigParamsService pacmanConfigParamsService;

    @Mock
    private FunctionSpacePackageService functionSpacePackageService;

    @Test
    void shouldGetAllActivePackageTypes() {
        when(tenantCrudService.findByNamedQuery(FunctionSpacePackageType.FIND_ALL_ACTIVE_PACKAGE_TYPES)).thenReturn(List.of(
                new FunctionSpacePackageType(), new FunctionSpacePackageType()));

        List<FunctionSpacePackageType> packageTypes = groupPricingPackageService.getAllActivePackageTypes();

        assertNotNull(packageTypes);
        assertEquals(2, packageTypes.size());
        verify(tenantCrudService).findByNamedQuery(FunctionSpacePackageType.FIND_ALL_ACTIVE_PACKAGE_TYPES);
    }

    @Test
    void shouldSavePackageTypes() {
        List<FunctionSpacePackageType> packageTypes = List.of(new FunctionSpacePackageType());
        when(tenantCrudService.save(packageTypes)).thenReturn(packageTypes);

        groupPricingPackageService.savePackageTypes(packageTypes);

        ArgumentCaptor<List<FunctionSpacePackageType>> packageTypeCaptor = ArgumentCaptor.forClass(List.class);
        verify(tenantCrudService).save(packageTypeCaptor.capture());
        assertNotNull(packageTypeCaptor.getValue());
        assertEquals(1, packageTypeCaptor.getValue().size());
    }

    @Test
    void shouldGetAllPackageElementDtosSortedByName() {
        GroupPricingConfigurationConferenceAndBanquet revenueGroup1 = createRevenueGroup(1, "AVI");
        GroupPricingConfigurationConferenceAndBanquet revenueGroup2 = createRevenueGroup(2, "BV");
        GroupPricingConfigurationPackageElement packageElement1 = createPackageElement(1, "Food Lunch", revenueGroup1);
        GroupPricingConfigurationPackageElement packageElement2 = createPackageElement(2, "Bev Lunch", revenueGroup1);
        GroupPricingConfigurationPackageElement packageElement3 = createPackageElement(3, "Bev Mtg", revenueGroup2);
        when(tenantCrudService.findByNamedQuery(eq(GroupPricingConfigurationPackageElement.FIND_ALL_ACTIVE_PACKAGE_ELEMENTS)))
                .thenReturn(List.of(packageElement1, packageElement2, packageElement3));

        List<PackageElementDto> packageElementDtos = groupPricingPackageService.getAllActivePackageElementDtoSortedByName();

        packageElementDtos.sort(Comparator.comparing(PackageElementDto::getName));
        assertEquals(3, packageElementDtos.size());
        assertPackageElement(packageElementDtos.get(0), packageElement2);
        assertPackageElement(packageElementDtos.get(1), packageElement3);
        assertPackageElement(packageElementDtos.get(2), packageElement1);
        verify(tenantCrudService).findByNamedQuery(eq(GroupPricingConfigurationPackageElement.FIND_ALL_ACTIVE_PACKAGE_ELEMENTS));
    }

    @Test
    void shouldGetAllRevenueGroupDtosSortedByName() {
        GroupPricingConfigurationConferenceAndBanquet revenueGroup1 = createRevenueGroup(1, "AVI");
        GroupPricingConfigurationConferenceAndBanquet revenueGroup2 = createRevenueGroup(2, "BV");
        when(tenantCrudService.findByNamedQuery(eq(GroupPricingConfigurationConferenceAndBanquet.FIND_BY_PROPERTY_ID), anyMap()))
                .thenReturn(List.of(revenueGroup2, revenueGroup1));

        List<RevenueGroupDto> revenueGroupDtos = groupPricingPackageService.getRevenueGroupDtoSortedByName();

        revenueGroupDtos.sort(Comparator.comparing(RevenueGroupDto::getName));
        assertRevenueGroupDto(revenueGroupDtos.get(0), revenueGroup1);
        assertRevenueGroupDto(revenueGroupDtos.get(1), revenueGroup2);
    }

    @Test
    void shouldInsertPackageElements() {
        RevenueGroupDto revenueGroupDto = createRevenueGroupDto(1, "AVI");
        PackageElementDto packageElementDto1 = createPackageElementDto(null, "Food Lunch", revenueGroupDto);
        PackageElementDto packageElementDto2 = createPackageElementDto(null, "Bev Lunch", revenueGroupDto);
        when(tenantCrudService.findByNamedQuery(eq(GroupPricingConfigurationConferenceAndBanquet.FIND_BY_ID_LIST), anyMap()))
                .thenReturn(List.of(createRevenueGroup(1, "AVI")));
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP)).thenReturn(false);
        groupPricingPackageService.savePackageElements(List.of(packageElementDto1, packageElementDto2));

        verify(tenantCrudService).findByNamedQuery(eq(GroupPricingConfigurationConferenceAndBanquet.FIND_BY_ID_LIST), anyMap());
        ArgumentCaptor<List<GroupPricingConfigurationPackageElement>> packageElementsArgumentCaptor = ArgumentCaptor.forClass(List.class);
        verify(tenantCrudService).save(packageElementsArgumentCaptor.capture());
        List<GroupPricingConfigurationPackageElement> packageElements = packageElementsArgumentCaptor.getValue();
        assertEquals(2, packageElements.size());
        packageElements.sort(Comparator.comparing(GroupPricingConfigurationPackageElement::getName));
        assertPackageElement(packageElementDto2, packageElements.get(0));
        assertPackageElement(packageElementDto1, packageElements.get(1));
    }

    @Test
    void shouldUpdatePackageElements() {
        GroupPricingConfigurationConferenceAndBanquet revenueGroup1 = createRevenueGroup(1, "AVI");
        GroupPricingConfigurationConferenceAndBanquet revenueGroup2 = createRevenueGroup(2, "AVI");
        RevenueGroupDto revenueGroupDto = createRevenueGroupDto(1, "AVI");
        PackageElementDto packageElementDto1 = createPackageElementDto(1, "Food Lunch", revenueGroupDto);
        PackageElementDto packageElementDto2 = createPackageElementDto(2, "Bev Lunch", revenueGroupDto);
        GroupPricingConfigurationPackageElement packageElement1 = createPackageElement(1, "Lunch", revenueGroup2);
        GroupPricingConfigurationPackageElement packageElement2 = createPackageElement(2, "Bev", revenueGroup1);
        when(tenantCrudService.findByNamedQuery(eq(GroupPricingConfigurationConferenceAndBanquet.FIND_BY_ID_LIST), anyMap()))
                .thenReturn(List.of(revenueGroup1));
        when(tenantCrudService.findByNamedQuery(eq(GroupPricingConfigurationPackageElement.FIND_ALL_PACKAGE_ELEMENTS_BY_ID), anyMap()))
                .thenReturn(List.of(packageElement1, packageElement2));
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP)).thenReturn(false);
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackageElement.FIND_ALL_ACTIVE_PACKAGE_ELEMENTS)).thenReturn(anyList());

        groupPricingPackageService.savePackageElements(List.of(packageElementDto1, packageElementDto2));

        verify(tenantCrudService, times(1)).findByNamedQuery(eq(GroupPricingConfigurationConferenceAndBanquet.FIND_BY_ID_LIST), anyMap());
        verify(tenantCrudService, times(1)).findByNamedQuery(eq(GroupPricingConfigurationPackageElement.FIND_ALL_PACKAGE_ELEMENTS_BY_ID), anyMap());
        ArgumentCaptor<List<GroupPricingConfigurationPackageElement>> packageElementsArgumentCaptor = ArgumentCaptor.forClass(List.class);
        verify(tenantCrudService).save(packageElementsArgumentCaptor.capture());
        List<GroupPricingConfigurationPackageElement> packageElements = new ArrayList<>(packageElementsArgumentCaptor.getValue());
        assertEquals(2, packageElements.size());
        packageElements.sort(Comparator.comparing(GroupPricingConfigurationPackageElement::getName));
        assertPackageElement(packageElementDto2, packageElements.get(0));
        assertPackageElement(packageElementDto1, packageElements.get(1));
    }

    @Test
    void shouldGetAllPackagesByPackageType() {
        FunctionSpacePackageType packageType1 = new FunctionSpacePackageType("Full Day", TenantStatusEnum.ACTIVE);
        FunctionSpacePackageType packageType2 = new FunctionSpacePackageType("Half Day", TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackage pkg1 = new GroupPricingConfigurationPackage("Full Day Basic", packageType1, true, TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackage pkg2 = new GroupPricingConfigurationPackage("Full Board Basic", packageType1, true, TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackage pkg3 = new GroupPricingConfigurationPackage("Half Day Basic", packageType2, true, TenantStatusEnum.ACTIVE);
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackage.FIND_ACTIVE_PACKAGES)).thenReturn(List.of(pkg1, pkg2, pkg3));

        Map<FunctionSpacePackageType, List<GroupPricingConfigurationPackage>> result = groupPricingPackageService.getAllPackagesByPackageType();

        assertEquals(2, result.size());
        assertEquals(2, result.get(packageType1).size());
        List<String> fullDayPackageNames = List.of("Full Day Basic", "Full Board Basic");
        assertTrue(result.get(packageType1).stream().map(GroupPricingConfigurationPackage::getName).allMatch(fullDayPackageNames::contains));
        assertEquals(1, result.get(packageType2).size());
        assertEquals("Half Day Basic", result.get(packageType2).get(0).getName());
    }

    @Test
    void shouldGetGroupPricingPackageDetails() {
        FunctionSpacePackageType packageType = new FunctionSpacePackageType("Full Day", TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackage pkg1 = new GroupPricingConfigurationPackage("Full Day Basic", packageType, true, TenantStatusEnum.ACTIVE);
        pkg1.setId(2);
        GroupPricingConfigurationPackage pkg2 = new GroupPricingConfigurationPackage("Full Board Basic", packageType, true, TenantStatusEnum.ACTIVE);
        pkg2.setId(3);
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackage.FIND_ACTIVE_PACKAGES_BY_ID,
                QueryParameter.with("idSet", Set.of(2, 3)).parameters())).thenReturn(List.of(pkg1, pkg2));

        Map<Integer, GroupPricingConfigurationPackage> result = groupPricingPackageService.getGroupPricingPackageDetails(Set.of(2, 3));

        assertEquals(2, result.size());
        assertEquals(2, result.get(2).getId());
        assertEquals("Full Day Basic", result.get(2).getName());
        assertEquals(3, result.get(3).getId());
        assertEquals("Full Board Basic", result.get(3).getName());
    }

    @Test
    public void getAllPackages() {
        GroupPricingConfigurationPackage package1 = new GroupPricingConfigurationPackage();
        package1.setName("Test");
        GroupPricingConfigurationPackage package2 = new GroupPricingConfigurationPackage();
        package2.setName("ZTest");
        GroupPricingConfigurationPackage package3 = new GroupPricingConfigurationPackage();
        package3.setName("123Test");
        GroupPricingConfigurationPackage package4 = new GroupPricingConfigurationPackage();
        package4.setName("ATest");

        Mockito.when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackage.FIND_ALL_ACTIVE_PACKAGES)).thenReturn(Arrays.asList(package1, package2, package3, package4));

        List<GroupPricingConfigurationPackage> results = groupPricingPackageService.getAllPackagesSortedByName();
        assertEquals(4, results.size());
        assertEquals(package3, results.get(0));
        assertEquals(package4, results.get(1));
        assertEquals(package1, results.get(2));
        assertEquals(package2, results.get(3));
        Mockito.verify(tenantCrudService).findByNamedQuery(GroupPricingConfigurationPackage.FIND_ALL_ACTIVE_PACKAGES);
    }

    @Test
    public void savePackages() {
        List<GroupPricingConfigurationPackage> packagesToBeSaved = new ArrayList<>();
        GroupPricingConfigurationPackage groupPricingConfigurationPackage = new GroupPricingConfigurationPackage();
        packagesToBeSaved.add(groupPricingConfigurationPackage);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage1 = new GroupPricingConfigurationPackage();
        packagesToBeSaved.add(groupPricingConfigurationPackage1);

        groupPricingPackageService.savePackages(packagesToBeSaved);
        Mockito.verify(tenantCrudService).save(packagesToBeSaved);
    }

    @Test
    public void savePackageElementMaps() {
        List<GroupPricingConfigurationPackageElementMap> packageElementMapsToBeSaved = new ArrayList<>();
        GroupPricingConfigurationPackageElementMap packageElementMap1 = new GroupPricingConfigurationPackageElementMap();
        packageElementMapsToBeSaved.add(packageElementMap1);
        GroupPricingConfigurationPackageElementMap packageElementMap2 = new GroupPricingConfigurationPackageElementMap();
        packageElementMapsToBeSaved.add(packageElementMap2);

        groupPricingPackageService.savePackageElementMaps(packageElementMapsToBeSaved);
        Mockito.verify(tenantCrudService).save(packageElementMapsToBeSaved);
    }

    @Test
    void shouldDeletePackagesAndAssociatedPackageElementMapsWhenUseFSRevenueStreamsIsFalse() {
        GroupPricingConfigurationPackage groupPricingConfigurationPackage1 = new GroupPricingConfigurationPackage();
        groupPricingConfigurationPackage1.setId(1);
        groupPricingConfigurationPackage1.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage2 = new GroupPricingConfigurationPackage();
        groupPricingConfigurationPackage2.setId(2);
        groupPricingConfigurationPackage2.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackageElementMap groupPricingConfigurationPackageElementMap1 = new GroupPricingConfigurationPackageElementMap();
        groupPricingConfigurationPackageElementMap1.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackageElementMap groupPricingConfigurationPackageElementMap2 = new GroupPricingConfigurationPackageElementMap();
        groupPricingConfigurationPackageElementMap2.setStatus(TenantStatusEnum.ACTIVE);
        FunctionSpacePackageType packageType1 = buildPackageType(1, "PackageType1", TenantStatusEnum.ACTIVE);
        PackageDTO packageDTO1 = buildPackageDTO(1, "Package1", packageType1, TenantStatusEnum.ACTIVE, true);
        PackageDTO packageDTO2 = buildPackageDTO(2, "Package1", packageType1, TenantStatusEnum.ACTIVE, true);
        when(tenantCrudService.findByNamedQuery(eq(GroupPricingConfigurationPackage.FIND_ACTIVE_PACKAGES_BY_ID),
                anyMap())).thenReturn(List.of(groupPricingConfigurationPackage1, groupPricingConfigurationPackage2));
        when(tenantCrudService.findByNamedQuery(eq(GroupPricingConfigurationPackageElementMap.FIND_ACTIVE_PACKAGE_ELEMENT_MAPS_BY_PACKAGE_ID),
                anyMap())).thenReturn(List.of(groupPricingConfigurationPackageElementMap1, groupPricingConfigurationPackageElementMap2));

        groupPricingPackageService.softDeletePackages(List.of(packageDTO1, packageDTO2));

        assertEquals(TenantStatusEnum.DELETED, groupPricingConfigurationPackage1.getStatus());
        assertEquals(TenantStatusEnum.DELETED, groupPricingConfigurationPackage2.getStatus());
        assertEquals(TenantStatusEnum.DELETED, groupPricingConfigurationPackageElementMap1.getStatus());
        assertEquals(TenantStatusEnum.DELETED, groupPricingConfigurationPackageElementMap2.getStatus());
        verify(tenantCrudService, times(2)).save(any(List.class));
        verify(tenantCrudService).findByNamedQuery(eq(GroupPricingConfigurationPackage.FIND_ACTIVE_PACKAGES_BY_ID), anyMap());
        verify(tenantCrudService).findByNamedQuery(eq(GroupPricingConfigurationPackageElementMap.FIND_ACTIVE_PACKAGE_ELEMENT_MAPS_BY_PACKAGE_ID), anyMap());
    }

    @Test
    void shouldDeletePackagesAndAssociatedPackageElementMapsWhenUseFSRevenueStreamsIsTrue() {
        GroupPricingConfigurationPackage groupPricingConfigurationPackage1 = new GroupPricingConfigurationPackage();
        groupPricingConfigurationPackage1.setName("Package1");
        groupPricingConfigurationPackage1.setId(1);
        groupPricingConfigurationPackage1.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage2 = new GroupPricingConfigurationPackage();
        groupPricingConfigurationPackage2.setName("Package2");
        groupPricingConfigurationPackage2.setId(2);
        groupPricingConfigurationPackage2.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackageElementMap groupPricingConfigurationPackageElementMap1 = new GroupPricingConfigurationPackageElementMap();
        groupPricingConfigurationPackageElementMap1.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackageElementMap groupPricingConfigurationPackageElementMap2 = new GroupPricingConfigurationPackageElementMap();
        groupPricingConfigurationPackageElementMap2.setStatus(TenantStatusEnum.ACTIVE);
        FunctionSpacePackageType fullDayPackageType = new FunctionSpacePackageType("Full Day", TenantStatusEnum.ACTIVE);
        FunctionSpacePackage fullDayPackage = FunctionSpaceObjectMother.buildFunctionSpacePackage("Package1", fullDayPackageType);
        fullDayPackage.setId(2);
        FunctionSpacePackage foodPackage = FunctionSpaceObjectMother.buildFunctionSpacePackage("Package2", fullDayPackageType);
        foodPackage.setId(3);
        FunctionSpacePackageType packageType1 = buildPackageType(1, "PackageType1", TenantStatusEnum.ACTIVE);
        PackageDTO packageDTO1 = buildPackageDTO(2, "Package1", packageType1, TenantStatusEnum.ACTIVE, true);
        PackageDTO packageDTO2 = buildPackageDTO(3, "Package2", packageType1, TenantStatusEnum.ACTIVE, true);
        when(tenantCrudService.findByNamedQuery(eq(GroupPricingConfigurationPackage.FIND_ACTIVE_PACKAGES_BY_NAME),
                anyMap())).thenReturn(List.of(groupPricingConfigurationPackage1, groupPricingConfigurationPackage2));
        when(tenantCrudService.findByNamedQuery(eq(GroupPricingConfigurationPackageElementMap.FIND_ACTIVE_PACKAGE_ELEMENT_MAPS_BY_PACKAGE_ID),
                anyMap())).thenReturn(List.of(groupPricingConfigurationPackageElementMap1, groupPricingConfigurationPackageElementMap2));
        when(functionSpacePackageService.getFunctionSpacePackagesUsingId(Set.of(2, 3))).thenReturn(List.of(fullDayPackage, foodPackage));
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP)).thenReturn(true);

        groupPricingPackageService.softDeletePackages(List.of(packageDTO1, packageDTO2));

        assertEquals(TenantStatusEnum.DELETED, groupPricingConfigurationPackage1.getStatus());
        assertEquals(TenantStatusEnum.DELETED, groupPricingConfigurationPackage2.getStatus());
        assertEquals(TenantStatusEnum.DELETED, groupPricingConfigurationPackageElementMap1.getStatus());
        assertEquals(TenantStatusEnum.DELETED, groupPricingConfigurationPackageElementMap2.getStatus());
        verify(functionSpacePackageService).getFunctionSpacePackagesUsingId(anySet());
        verify(tenantCrudService).findByNamedQuery(eq(GroupPricingConfigurationPackage.FIND_ACTIVE_PACKAGES_BY_NAME), anyMap());
        verify(tenantCrudService).findByNamedQuery(eq(GroupPricingConfigurationPackageElementMap.FIND_ACTIVE_PACKAGE_ELEMENT_MAPS_BY_PACKAGE_ID), anyMap());
        verify(tenantCrudService, times(2)).save(any(List.class));
    }

    @Test
    public void getAllActivePackageElementMaps() {
        GroupPricingConfigurationPackageElementMap map = new GroupPricingConfigurationPackageElementMap();

        Mockito.when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackageElementMap.FIND_ALL_ACTIVE_PACKAGE_ELEMENT_MAPS)).thenReturn(Arrays.asList(map));

        List<GroupPricingConfigurationPackageElementMap> results = groupPricingPackageService.getAllActivePackageElementMaps();
        assertEquals(1, results.size());
        assertEquals(map, results.get(0));
        Mockito.verify(tenantCrudService).findByNamedQuery(GroupPricingConfigurationPackageElementMap.FIND_ALL_ACTIVE_PACKAGE_ELEMENT_MAPS);
    }

    @Test
    void shouldReturnFalseWhenConfBanqIsNull() {
        assertFalse(groupPricingPackageService.arePackageElementsConfiguredUsingConfBanq(null));
        verify(tenantCrudService, times(0)).findByNamedQuery(eq(GroupPricingConfigurationPackageElement.FIND_ALL_PACKAGE_ELEMENTS_ASSOCIATED_WITH_CONFERENCE_BANQUET), anyMap());
    }

    @Test
    void shouldReturnTrueWhenPackageElementsAreConfiguredUsingGivenRevenueStream() {
        GroupPricingConfigurationConferenceAndBanquet revenueGroup = createRevenueGroup(1, "AVI");
        GroupPricingConfigurationPackageElement packageElement = createPackageElement(1, "Food Lunch", revenueGroup);
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackageElement.FIND_ALL_PACKAGE_ELEMENTS_ASSOCIATED_WITH_CONFERENCE_BANQUET, Map.of(
                "idList", List.of(revenueGroup.getId())))).thenReturn(List.of(packageElement));

        assertTrue(groupPricingPackageService.arePackageElementsConfiguredUsingConfBanq(List.of(revenueGroup)));
        verify(tenantCrudService).findByNamedQuery(GroupPricingConfigurationPackageElement.FIND_ALL_PACKAGE_ELEMENTS_ASSOCIATED_WITH_CONFERENCE_BANQUET, Map.of(
                "idList", List.of(revenueGroup.getId())));
    }

    @Test
    void shouldReturnFalseWhenPackageElementsAreNotConfiguredUsingGivenRevenueStream() {
        GroupPricingConfigurationConferenceAndBanquet revenueGroup = createRevenueGroup(1, "AVI");
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackageElement.FIND_ALL_PACKAGE_ELEMENTS_ASSOCIATED_WITH_CONFERENCE_BANQUET, Map.of(
                "idList", List.of(revenueGroup.getId())))).thenReturn(List.of());

        assertFalse(groupPricingPackageService.arePackageElementsConfiguredUsingConfBanq(List.of(revenueGroup)));
        verify(tenantCrudService).findByNamedQuery(GroupPricingConfigurationPackageElement.FIND_ALL_PACKAGE_ELEMENTS_ASSOCIATED_WITH_CONFERENCE_BANQUET, Map.of(
                "idList", List.of(revenueGroup.getId())));
    }

    @Test
    void shouldReturnFalseWhenRevenueStreamsAreEmpty() {
        assertFalse(groupPricingPackageService.arePackageElementsConfiguredUsingConfBanq(List.of()));
        verify(tenantCrudService, times(0)).findByNamedQuery(eq(GroupPricingConfigurationPackageElement.FIND_ALL_PACKAGE_ELEMENTS_ASSOCIATED_WITH_CONFERENCE_BANQUET), anyMap());
    }

    @Test
    void shouldDeletePackageConfigurationWhenAssociatedRevenueStreamIsDeleted() {
        int inActiveStatusId = 2;
        GroupPricingConfigurationConferenceAndBanquet revenueGroup1 = createRevenueGroup(1, "AVI");
        revenueGroup1.setStatusId(inActiveStatusId);
        GroupPricingConfigurationPackageElement packageElement1 = createPackageElement(1, "Food Lunch", revenueGroup1);
        packageElement1.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackageElement packageElement2 = createPackageElement(2, "Bev Lunch", revenueGroup1);
        packageElement2.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage = createGroupPricingPackage(1, "Food Package");
        groupPricingConfigurationPackage.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackageElementMap packageElementMap = createGroupPricingPackageElementMap(1, packageElement1, groupPricingConfigurationPackage);
        packageElementMap.setStatus(TenantStatusEnum.ACTIVE);
        groupPricingConfigurationPackage.setGroupPricingPackageElementMapSet(Set.of(packageElementMap));
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackageElement.FIND_ALL_PACKAGE_ELEMENTS_ASSOCIATED_WITH_CONFERENCE_BANQUET, Map.of(
                "idList", List.of(revenueGroup1.getId())))).thenReturn(List.of(packageElement1, packageElement2));
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackageElementMap.FIND_ACTIVE_PACKAGE_ELEMENT_MAPS_ASSOCIATED_WITH_PACKAGE_ELEMENTS, Map.of(
                "idSet", List.of(packageElement1.getId(), packageElement2.getId())))).thenReturn(List.of(packageElementMap));
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackage.FIND_ACTIVE_PACKAGES_ASSOCIATED_WITH_PACKAGE_ELEMENTS, Map.of(
                "idSet", List.of(packageElement1.getId(), packageElement2.getId())))).thenReturn(List.of(groupPricingConfigurationPackage));
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackageElementMap.FIND_ACTIVE_PACKAGE_ELEMENT_MAPS_BY_PACKAGE_ID, Map.of(
                "idSet", Set.of(groupPricingConfigurationPackage.getId())))).thenReturn(List.of(packageElementMap));

        groupPricingPackageService.deletePackageConfigurationAssociatedWithRevenueStream(List.of(revenueGroup1));

        assertEquals(TenantStatusEnum.DELETED, packageElement1.getStatus());
        assertEquals(TenantStatusEnum.DELETED, packageElement2.getStatus());
        assertEquals(TenantStatusEnum.DELETED, packageElementMap.getStatus());
        assertEquals(TenantStatusEnum.INACTIVE, groupPricingConfigurationPackage.getStatus());
        verify(tenantCrudService).findByNamedQuery(eq(GroupPricingConfigurationPackageElement.FIND_ALL_PACKAGE_ELEMENTS_ASSOCIATED_WITH_CONFERENCE_BANQUET), anyMap());
        verify(tenantCrudService).findByNamedQuery(eq(GroupPricingConfigurationPackageElementMap.FIND_ACTIVE_PACKAGE_ELEMENT_MAPS_ASSOCIATED_WITH_PACKAGE_ELEMENTS), anyMap());
        verify(tenantCrudService).findByNamedQuery(eq(GroupPricingConfigurationPackage.FIND_ACTIVE_PACKAGES_ASSOCIATED_WITH_PACKAGE_ELEMENTS), anyMap());
        verify(tenantCrudService).findByNamedQuery(eq(GroupPricingConfigurationPackageElementMap.FIND_ACTIVE_PACKAGE_ELEMENT_MAPS_BY_PACKAGE_ID), anyMap());
        verify(tenantCrudService, times(3)).save(anyCollection());
    }

    @Test
    void shouldInactivatePackagesHavingAllDeletedPackageElements() {
        int activeStatusId = 1;
        int inActiveStatusId = 2;
        GroupPricingConfigurationConferenceAndBanquet revenueGroup1 = createRevenueGroup(1, "AVI");
        revenueGroup1.setStatusId(inActiveStatusId);
        GroupPricingConfigurationConferenceAndBanquet revenueGroup2 = createRevenueGroup(2, "AVI");
        revenueGroup2.setStatusId(activeStatusId);
        GroupPricingConfigurationPackageElement packageElement1 = createPackageElement(1, "Food Lunch", revenueGroup1);
        packageElement1.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackageElement packageElement2 = createPackageElement(2, "Bev Lunch", revenueGroup1);
        packageElement2.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackageElement packageElement3 = createPackageElement(3, "AV", revenueGroup2);
        packageElement3.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage1 = createGroupPricingPackage(1, "Food Package");
        groupPricingConfigurationPackage1.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage2 = createGroupPricingPackage(2, "Food Package");
        groupPricingConfigurationPackage2.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackageElementMap packageElementMap1 = createGroupPricingPackageElementMap(1, packageElement1, groupPricingConfigurationPackage1);
        packageElementMap1.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackageElementMap packageElementMap2 = createGroupPricingPackageElementMap(2, packageElement2, groupPricingConfigurationPackage1);
        packageElementMap2.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackageElementMap packageElementMap3 = createGroupPricingPackageElementMap(3, packageElement2, groupPricingConfigurationPackage2);
        packageElementMap3.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackageElementMap packageElementMap4 = createGroupPricingPackageElementMap(4, packageElement3, groupPricingConfigurationPackage2);
        packageElementMap4.setStatus(TenantStatusEnum.ACTIVE);
        groupPricingConfigurationPackage1.setGroupPricingPackageElementMapSet(Set.of(packageElementMap1, packageElementMap2));
        groupPricingConfigurationPackage2.setGroupPricingPackageElementMapSet(Set.of(packageElementMap3, packageElementMap4));
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackageElement.FIND_ALL_PACKAGE_ELEMENTS_ASSOCIATED_WITH_CONFERENCE_BANQUET, Map.of(
                "idList", List.of(revenueGroup1.getId())))).thenReturn(List.of(packageElement1, packageElement2));
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackageElementMap.FIND_ACTIVE_PACKAGE_ELEMENT_MAPS_ASSOCIATED_WITH_PACKAGE_ELEMENTS, Map.of(
                "idSet", List.of(packageElement1.getId(), packageElement2.getId())))).thenReturn(List.of(packageElementMap1, packageElementMap2, packageElementMap3));
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackage.FIND_ACTIVE_PACKAGES_ASSOCIATED_WITH_PACKAGE_ELEMENTS, Map.of(
                "idSet", List.of(packageElement1.getId(), packageElement2.getId())))).thenReturn(List.of(groupPricingConfigurationPackage1, groupPricingConfigurationPackage2));
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackageElementMap.FIND_ACTIVE_PACKAGE_ELEMENT_MAPS_BY_PACKAGE_ID, Map.of(
                "idSet", Set.of(groupPricingConfigurationPackage1.getId(), groupPricingConfigurationPackage2.getId()))))
                .thenReturn(List.of(packageElementMap1, packageElementMap2, packageElementMap3, packageElementMap4));

        groupPricingPackageService.deletePackageConfigurationAssociatedWithRevenueStream(List.of(revenueGroup1));

        assertEquals(TenantStatusEnum.DELETED, packageElement1.getStatus());
        assertEquals(TenantStatusEnum.DELETED, packageElement2.getStatus());
        assertEquals(TenantStatusEnum.ACTIVE, packageElement3.getStatus());
        assertEquals(TenantStatusEnum.DELETED, packageElementMap1.getStatus());
        assertEquals(TenantStatusEnum.DELETED, packageElementMap2.getStatus());
        assertEquals(TenantStatusEnum.DELETED, packageElementMap3.getStatus());
        assertEquals(TenantStatusEnum.ACTIVE, packageElementMap4.getStatus());
        assertEquals(TenantStatusEnum.INACTIVE, groupPricingConfigurationPackage1.getStatus());
        assertEquals(TenantStatusEnum.ACTIVE, groupPricingConfigurationPackage2.getStatus());
        verify(tenantCrudService).findByNamedQuery(eq(GroupPricingConfigurationPackageElement.FIND_ALL_PACKAGE_ELEMENTS_ASSOCIATED_WITH_CONFERENCE_BANQUET), anyMap());
        verify(tenantCrudService).findByNamedQuery(eq(GroupPricingConfigurationPackageElementMap.FIND_ACTIVE_PACKAGE_ELEMENT_MAPS_ASSOCIATED_WITH_PACKAGE_ELEMENTS), anyMap());
        verify(tenantCrudService).findByNamedQuery(eq(GroupPricingConfigurationPackage.FIND_ACTIVE_PACKAGES_ASSOCIATED_WITH_PACKAGE_ELEMENTS), anyMap());
        verify(tenantCrudService).findByNamedQuery(eq(GroupPricingConfigurationPackageElementMap.FIND_ACTIVE_PACKAGE_ELEMENT_MAPS_BY_PACKAGE_ID), anyMap());
        verify(tenantCrudService, times(3)).save(anyCollection());
    }

    @Test
    void shouldNotInactivatePackagesHavingAllActivePackageElements() {
        int activeStatusId = 1;
        int inActiveStatusId = 2;
        GroupPricingConfigurationConferenceAndBanquet revenueGroup1 = createRevenueGroup(1, "AVI");
        revenueGroup1.setStatusId(inActiveStatusId);
        GroupPricingConfigurationConferenceAndBanquet revenueGroup2 = createRevenueGroup(2, "AVI");
        revenueGroup2.setStatusId(activeStatusId);
        GroupPricingConfigurationPackageElement packageElement1 = createPackageElement(1, "Food Lunch", revenueGroup1);
        packageElement1.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackageElement packageElement2 = createPackageElement(2, "Bev Lunch", revenueGroup1);
        packageElement2.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackageElement packageElement3 = createPackageElement(3, "AV", revenueGroup2);
        packageElement3.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackage groupPricingConfigurationPackage = createGroupPricingPackage(1, "Food Package");
        groupPricingConfigurationPackage.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackageElementMap packageElementMap = createGroupPricingPackageElementMap(1, packageElement3, groupPricingConfigurationPackage);
        packageElementMap.setStatus(TenantStatusEnum.ACTIVE);
        groupPricingConfigurationPackage.setGroupPricingPackageElementMapSet(Set.of(packageElementMap));
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackageElement.FIND_ALL_PACKAGE_ELEMENTS_ASSOCIATED_WITH_CONFERENCE_BANQUET, Map.of(
                "idList", List.of(revenueGroup1.getId())))).thenReturn(List.of(packageElement1, packageElement2));
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackageElementMap.FIND_ACTIVE_PACKAGE_ELEMENT_MAPS_ASSOCIATED_WITH_PACKAGE_ELEMENTS, Map.of(
                "idSet", List.of(packageElement1.getId(), packageElement2.getId())))).thenReturn(List.of());
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackage.FIND_ACTIVE_PACKAGES_ASSOCIATED_WITH_PACKAGE_ELEMENTS, Map.of(
                "idSet", List.of(packageElement1.getId(), packageElement2.getId())))).thenReturn(List.of());

        groupPricingPackageService.deletePackageConfigurationAssociatedWithRevenueStream(List.of(revenueGroup1));

        assertEquals(TenantStatusEnum.DELETED, packageElement1.getStatus());
        assertEquals(TenantStatusEnum.DELETED, packageElement2.getStatus());
        assertEquals(TenantStatusEnum.ACTIVE, packageElement3.getStatus());
        assertEquals(TenantStatusEnum.ACTIVE, packageElementMap.getStatus());
        assertEquals(TenantStatusEnum.ACTIVE, groupPricingConfigurationPackage.getStatus());
        verify(tenantCrudService).findByNamedQuery(eq(GroupPricingConfigurationPackageElement.FIND_ALL_PACKAGE_ELEMENTS_ASSOCIATED_WITH_CONFERENCE_BANQUET), anyMap());
        verify(tenantCrudService).findByNamedQuery(eq(GroupPricingConfigurationPackageElementMap.FIND_ACTIVE_PACKAGE_ELEMENT_MAPS_ASSOCIATED_WITH_PACKAGE_ELEMENTS), anyMap());
        verify(tenantCrudService).findByNamedQuery(eq(GroupPricingConfigurationPackage.FIND_ACTIVE_PACKAGES_ASSOCIATED_WITH_PACKAGE_ELEMENTS), anyMap());
        verify(tenantCrudService, times(0)).findByNamedQuery(eq(GroupPricingConfigurationPackageElementMap.FIND_ACTIVE_PACKAGE_ELEMENT_MAPS_BY_PACKAGE_ID), anyMap());
        verify(tenantCrudService, times(3)).save(anyCollection());
    }

    @Test
    void shouldSavePackageElementsIntoGroupPricingConfigurationWhenPkgElementIsNotAlreadyPresent() {
        ArgumentCaptor<List<GroupPricingConfigurationPackageElement>> gpPackageElementsCaptor = ArgumentCaptor.forClass(List.class);
        RevenueGroupDto revenueGroupDto = createRevenueGroupDto(1, "Food");
        List<PackageElementDto> packageElementDtosToBeSaved = Arrays.asList(
                createPackageElementDto(null, "PackageElement1", revenueGroupDto),
                createPackageElementDto(null, "PackageElement2", revenueGroupDto)
        );
        GroupPricingConfigurationConferenceAndBanquet revenueGroup1 = createRevenueGroup(4, "Food");
        GroupPricingConfigurationConferenceAndBanquet revenueGroup2 = createRevenueGroup(6, "Beverage");
        List<GroupPricingConfigurationPackageElement> existingGpPackageElements = Arrays.asList(
                createPackageElement(1, "PE1", revenueGroup2),
                createPackageElement(2, "PE2", revenueGroup2));
        List<FunctionSpacePackageElement> functionSpacePackageElements = new ArrayList<>();
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP)).thenReturn(true);
        when(tenantCrudService.findByNamedQuerySingleResult(
                GroupPricingConfigurationConferenceAndBanquet.FIND_BY_REVENUE_STREAM_NAME,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("revenueStream", "Food").parameters()
        )).thenReturn(revenueGroup1);
        when(tenantCrudService.findByNamedQuery(eq(FunctionSpacePackageElement.FIND_ALL_PACKAGE_ELEMENTS_BY_ID), any())).thenReturn(List.of(createFSPackageElement(2, "PackageElement1")));
        when(tenantCrudService.findByNamedQuery(eq(GroupPricingConfigurationPackageElement.FIND_ALL_ACTIVE_PACKAGE_ELEMENTS))).thenReturn(Arrays.asList(
                createPackageElement(2, "PE2", revenueGroup2)));

        groupPricingPackageService.savePackageElements(packageElementDtosToBeSaved);

        verify(tenantCrudService, times(1)).save(gpPackageElementsCaptor.capture());
        List<GroupPricingConfigurationPackageElement> savedElements = gpPackageElementsCaptor.getAllValues().get(0);
        assertEquals(2, savedElements.size());
        assertEquals(null, savedElements.get(0).getId());
        assertEquals("PackageElement1", savedElements.get(0).getName());
        assertEquals(null, savedElements.get(1).getId());
        assertEquals("PackageElement2", savedElements.get(1).getName());
        verify(tenantCrudService, times(1)).save(anyList());
    }

    @Test
    void shouldUpdatePackageElementInGPWhenPackageElementToBeUpdatedIsAlreadyPresent() {
        ArgumentCaptor<List<GroupPricingConfigurationPackageElement>> gpPackageElementsCaptor = ArgumentCaptor.forClass(List.class);
        RevenueGroupDto revenueGroupDto = createRevenueGroupDto(1, "Food");
        List<PackageElementDto> packageElementDtosToBeSaved = Arrays.asList(
                createPackageElementDto(2, "PackageElementUpdated", revenueGroupDto)
        );
        packageElementDtosToBeSaved.get(0).setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationConferenceAndBanquet revenueGroup1 = createRevenueGroup(4, "Food");
        GroupPricingConfigurationConferenceAndBanquet revenueGroup2 = createRevenueGroup(6, "Beverage");
        List<GroupPricingConfigurationPackageElement> existingGpPackageElements = Arrays.asList(
                createPackageElement(1, "PackageElement1", revenueGroup2),
                createPackageElement(2, "PE2", revenueGroup2));
        existingGpPackageElements.get(0).setStatus(TenantStatusEnum.ACTIVE);
        existingGpPackageElements.get(1).setStatus(TenantStatusEnum.ACTIVE);
        List<FunctionSpacePackageElement> functionSpacePackageElements = Arrays.asList(createFSPackageElement(2, "PackageElement1"));
        Map<Integer, PackageElementDto> idToDtoMap = new HashMap<>();
        idToDtoMap.put(1, packageElementDtosToBeSaved.get(0));
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP)).thenReturn(true);
        when(tenantCrudService.findByNamedQuerySingleResult(
                GroupPricingConfigurationConferenceAndBanquet.FIND_BY_REVENUE_STREAM_NAME,
                QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId())
                        .and("revenueStream", "Food").parameters()
        )).thenReturn(revenueGroup1);
        when(tenantCrudService.findByNamedQuery(eq(FunctionSpacePackageElement.FIND_ALL_PACKAGE_ELEMENTS_BY_ID), any())).thenReturn(List.of(createFSPackageElement(2, "PackageElement1")));
        when(tenantCrudService.findByNamedQuery(eq(GroupPricingConfigurationPackageElement.FIND_ALL_ACTIVE_PACKAGE_ELEMENTS))).thenReturn(Arrays.asList(createPackageElement(1, "PackageElement1", revenueGroup2),
                createPackageElement(2, "PE2", revenueGroup2)));
        when(tenantCrudService.findByNamedQuery(eq(GroupPricingConfigurationPackageElement.FIND_ALL_PACKAGE_ELEMENTS_BY_ID),
                any())).thenReturn(Arrays.asList(createPackageElement(1, "PackageElement1", revenueGroup2)));

        groupPricingPackageService.savePackageElements(packageElementDtosToBeSaved);

        verify(tenantCrudService, times(1)).save(gpPackageElementsCaptor.capture());
        List<GroupPricingConfigurationPackageElement> savedElements = gpPackageElementsCaptor.getValue();
        assertEquals(1, savedElements.get(0).getId());
        assertEquals("PackageElementUpdated", savedElements.get(0).getName());
        assertEquals(revenueGroupDto.getName(), savedElements.get(0).getConferenceAndBanquet().getRevenueStream());
        verify(tenantCrudService, times(1)).save(any(List.class));
    }

    @Test
    void shouldSaveNewPackagesAlongWithPackageElementMaps() {
        RevenueGroupDto revenueGroupDto = createRevenueGroupDto(1, "AVI");
        FunctionSpacePackageType packageType1 = buildPackageType(1, "PackageType1", TenantStatusEnum.ACTIVE);
        PackageElementDto packageElementDto1 = createPackageElementDto(1, "Food Lunch", revenueGroupDto);
        PackageElementDto packageElementDto2 = createPackageElementDto(2, "Bev Lunch", revenueGroupDto);
        PackageElementMapDTO packageElementMap1 = buildPackageElementMapDTO(null, packageElementDto1, BigDecimal.ONE, TenantStatusEnum.NEW);
        PackageElementMapDTO packageElementMap2 = buildPackageElementMapDTO(null, packageElementDto2, BigDecimal.TEN, TenantStatusEnum.NEW);
        PackageDTO packageDTO1 = buildPackageDTO(-1, "Package1", packageType1, TenantStatusEnum.ACTIVE, true);
        packageDTO1.setFunctionSpacePackageElementMapDTOList(List.of(packageElementMap1, packageElementMap2));

        groupPricingPackageService.saveNewPackages(List.of(packageDTO1));

        ArgumentCaptor<List> packagesToBeSaved = ArgumentCaptor.forClass(List.class);
        verify(tenantCrudService, times(2)).save(packagesToBeSaved.capture());
        List<GroupPricingConfigurationPackage> savedPackages = packagesToBeSaved.getAllValues().get(0);
        assertEquals(1, savedPackages.size());
        assertPackage(savedPackages.get(0), packageDTO1);
        List<GroupPricingConfigurationPackageElementMap> savedPackageElementMaps = packagesToBeSaved.getAllValues().get(1);
        assertEquals(2, savedPackageElementMaps.size());
        GroupPricingConfigurationPackageElementMap actualPackageElementMap1 = savedPackageElementMaps.get(0);
        assertEquals(packageElementMap1.getPackageElementDTO().getId(), actualPackageElementMap1.getGroupPricingConfigurationPackageElement().getId());
        assertTrue(BigDecimalUtil.equals(packageElementMap1.getAmount(), actualPackageElementMap1.getAmount()));
        assertEquals(TenantStatusEnum.ACTIVE, savedPackageElementMaps.get(0).getStatus());
        GroupPricingConfigurationPackageElementMap actualPackageElementMap = savedPackageElementMaps.get(1);
        assertEquals(packageElementMap2.getPackageElementDTO().getId(), actualPackageElementMap.getGroupPricingConfigurationPackageElement().getId());
        assertTrue(BigDecimalUtil.equals(packageElementMap2.getAmount(), actualPackageElementMap.getAmount()));
        assertEquals(TenantStatusEnum.ACTIVE, savedPackageElementMaps.get(1).getStatus());
    }

    @Test
    void shouldSaveNewPackagesAlongWithPackageElementMapsWhenUseFSRevenueStreamsIsTrue() {
        RevenueGroupDto revenueGroupDto = createRevenueGroupDto(1, "AVI");
        FunctionSpacePackageType packageType1 = buildPackageType(1, "PackageType1", TenantStatusEnum.ACTIVE);
        PackageElementDto packageElementDto1 = createPackageElementDto(3, "Food Lunch", revenueGroupDto);
        PackageElementDto packageElementDto2 = createPackageElementDto(4, "Bev Lunch", revenueGroupDto);
        PackageElementMapDTO packageElementMap1 = buildPackageElementMapDTO(null, packageElementDto1, BigDecimal.ONE, TenantStatusEnum.NEW);
        PackageElementMapDTO packageElementMap2 = buildPackageElementMapDTO(null, packageElementDto2, BigDecimal.TEN, TenantStatusEnum.NEW);
        PackageDTO packageDTO = buildPackageDTO(-1, "Package1", packageType1, TenantStatusEnum.ACTIVE, true);
        packageDTO.setFunctionSpacePackageElementMapDTOList(List.of(packageElementMap1, packageElementMap2));
        FunctionSpacePackageElement functionSpacePackageElement1 = buildFunctionSpacePackageElement(3, "Food Lunch");
        functionSpacePackageElement1.setTaxBucket(BigDecimal.TEN);
        FunctionSpacePackageElement functionSpacePackageElement2 = buildFunctionSpacePackageElement(4, "Bev Lunch");
        GroupPricingConfigurationPackageElement groupPricingConfigurationPackageElement = foodLunchGroupPricingConfigurationPackageElement();
        when(tenantCrudService.findByNamedQuery(eq(FunctionSpacePackageElement.FIND_ALL_PACKAGE_ELEMENTS_BY_ID), anyMap()))
                .thenReturn(List.of(functionSpacePackageElement1, functionSpacePackageElement2));
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackageElement.FIND_ALL_ACTIVE_PACKAGE_ELEMENTS))
                .thenReturn(List.of(groupPricingConfigurationPackageElement));
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP)).thenReturn(true);

        groupPricingPackageService.saveNewPackages(List.of(packageDTO));

        ArgumentCaptor<List> packagesToBeSaved = ArgumentCaptor.forClass(List.class);
        verify(tenantCrudService, times(2)).save(packagesToBeSaved.capture());
        List<GroupPricingConfigurationPackage> savedPackages = packagesToBeSaved.getAllValues().get(0);
        assertEquals(1, savedPackages.size());
        assertPackage(savedPackages.get(0), packageDTO);
        List<GroupPricingConfigurationPackageElementMap> savedPackageElementMaps = packagesToBeSaved.getAllValues().get(1);
        assertEquals(1, savedPackageElementMaps.size());
        GroupPricingConfigurationPackageElementMap actualPackageElementMap1 = savedPackageElementMaps.get(0);
        assertEquals(groupPricingConfigurationPackageElement.getId(), actualPackageElementMap1.getGroupPricingConfigurationPackageElement().getId());
        assertTrue(BigDecimalUtil.equals(packageElementMap1.getAmount(), actualPackageElementMap1.getAmount()));
        assertEquals(TenantStatusEnum.ACTIVE, savedPackageElementMaps.get(0).getStatus());
    }

    @Test
    void shouldUpdatePackagesAlongWithNewPackageElementMapsWhenUseFSRevenueStreamsIsFalse() {
        RevenueGroupDto revenueGroupDto = createRevenueGroupDto(1, "AVI");
        FunctionSpacePackageType fullDayPackageType = new FunctionSpacePackageType("Full Day", TenantStatusEnum.ACTIVE);
        FunctionSpacePackageType packageType1 = buildPackageType(1, "PackageType1", TenantStatusEnum.ACTIVE);
        PackageElementDto packageElementDto1 = createPackageElementDto(1, "Food Lunch", revenueGroupDto);
        PackageElementDto packageElementDto2 = createPackageElementDto(2, "Bev Lunch", revenueGroupDto);
        PackageElementMapDTO packageElementMap1 = buildPackageElementMapDTO(null, packageElementDto1, BigDecimal.ONE, TenantStatusEnum.NEW);
        PackageElementMapDTO packageElementMap2 = buildPackageElementMapDTO(null, packageElementDto2, BigDecimal.TEN, TenantStatusEnum.NEW);
        PackageDTO modifiedPackage = buildPackageDTO(1, "Package1", packageType1, TenantStatusEnum.ACTIVE, true);
        modifiedPackage.setFunctionSpacePackageElementMapDTOList(List.of(packageElementMap1, packageElementMap2));
        GroupPricingConfigurationPackage existingPackage = new GroupPricingConfigurationPackage("Full Day Basic", fullDayPackageType, false, TenantStatusEnum.ACTIVE);
        existingPackage.setId(1);
        existingPackage.setLastUpdatedDate(LocalDateTime.now());
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackage.FIND_ACTIVE_PACKAGES_BY_ID,
                QueryParameter.with("idSet", Set.of(1)).parameters())).thenReturn(List.of(existingPackage));

        groupPricingPackageService.updatePackages(List.of(modifiedPackage));

        ArgumentCaptor<List> packagesToBeSaved = ArgumentCaptor.forClass(List.class);
        verify(tenantCrudService, times(2)).save(packagesToBeSaved.capture());
        List<GroupPricingConfigurationPackage> savedPackages = packagesToBeSaved.getAllValues().get(0);
        assertEquals(1, savedPackages.size());
        assertPackage(savedPackages.get(0), modifiedPackage);
        List<GroupPricingConfigurationPackageElementMap> savedPackageElementMaps = packagesToBeSaved.getAllValues().get(1);
        assertEquals(2, savedPackageElementMaps.size());
        GroupPricingConfigurationPackageElementMap actualPackageElementMap1 = savedPackageElementMaps.get(0);
        assertEquals(packageElementMap1.getPackageElementDTO().getId(), actualPackageElementMap1.getGroupPricingConfigurationPackageElement().getId());
        assertTrue(BigDecimalUtil.equals(packageElementMap1.getAmount(), actualPackageElementMap1.getAmount()));
        assertEquals(TenantStatusEnum.ACTIVE, savedPackageElementMaps.get(0).getStatus());
        GroupPricingConfigurationPackageElementMap actualPackageElementMap = savedPackageElementMaps.get(1);
        assertEquals(packageElementMap2.getPackageElementDTO().getId(), actualPackageElementMap.getGroupPricingConfigurationPackageElement().getId());
        assertTrue(BigDecimalUtil.equals(packageElementMap2.getAmount(), actualPackageElementMap.getAmount()));
        assertEquals(TenantStatusEnum.ACTIVE, savedPackageElementMaps.get(1).getStatus());
        verify(pacmanConfigParamsService, times(4)).getBooleanParameterValue(PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP);
    }

    @Test
    void shouldUpdatePackagesAlongWithNewPackageElementMapsWhenUseFSRevenueStreamsIsTrue() {
        RevenueGroupDto revenueGroupDto = createRevenueGroupDto(1, "AVI");
        FunctionSpacePackageType fullDayPackageType = new FunctionSpacePackageType("Full Day", TenantStatusEnum.ACTIVE);
        FunctionSpacePackageType packageType1 = buildPackageType(1, "PackageType1", TenantStatusEnum.ACTIVE);
        PackageElementDto packageElementDto1 = createPackageElementDto(3, "Food Lunch", revenueGroupDto);
        PackageElementDto packageElementDto2 = createPackageElementDto(4, "Bev Lunch", revenueGroupDto);
        PackageElementMapDTO packageElementMap1 = buildPackageElementMapDTO(null, packageElementDto1, BigDecimal.ONE, TenantStatusEnum.NEW);
        PackageElementMapDTO packageElementMap2 = buildPackageElementMapDTO(null, packageElementDto2, BigDecimal.TEN, TenantStatusEnum.NEW);
        PackageDTO modifiedPackageDTO = buildPackageDTO(2, "Package1", packageType1, TenantStatusEnum.ACTIVE, true);
        modifiedPackageDTO.setFunctionSpacePackageElementMapDTOList(List.of(packageElementMap1, packageElementMap2));
        GroupPricingConfigurationPackage groupPricingConfigurationPackage = new GroupPricingConfigurationPackage("Food Package", fullDayPackageType, false, TenantStatusEnum.ACTIVE);
        groupPricingConfigurationPackage.setId(1);
        groupPricingConfigurationPackage.setLastUpdatedDate(LocalDateTime.now());
        FunctionSpacePackage functionSpacePackage = FunctionSpaceObjectMother.buildFunctionSpacePackage("Food Package", fullDayPackageType);
        functionSpacePackage.setId(2);
        when(functionSpacePackageService.getFunctionSpacePackagesUsingId(Set.of(2))).thenReturn(List.of(functionSpacePackage));
        when(tenantCrudService.findByNamedQuery(eq(GroupPricingConfigurationPackage.FIND_ACTIVE_PACKAGES_BY_NAME),
                anyMap())).thenReturn(List.of(groupPricingConfigurationPackage));
        FunctionSpacePackageElement functionSpacePackageElement1 = buildFunctionSpacePackageElement(3, "Food Lunch");
        functionSpacePackageElement1.setTaxBucket(BigDecimal.TEN);
        FunctionSpacePackageElement functionSpacePackageElement2 = buildFunctionSpacePackageElement(4, "Bev Lunch");
        GroupPricingConfigurationPackageElement groupPricingConfigurationPackageElement = foodLunchGroupPricingConfigurationPackageElement();
        when(tenantCrudService.findByNamedQuery(eq(FunctionSpacePackageElement.FIND_ALL_PACKAGE_ELEMENTS_BY_ID), anyMap()))
                .thenReturn(List.of(functionSpacePackageElement1, functionSpacePackageElement2));
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackageElement.FIND_ALL_ACTIVE_PACKAGE_ELEMENTS))
                .thenReturn(List.of(groupPricingConfigurationPackageElement));
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP)).thenReturn(true);

        groupPricingPackageService.updatePackages(List.of(modifiedPackageDTO));

        ArgumentCaptor<List> packagesToBeSaved = ArgumentCaptor.forClass(List.class);
        verify(tenantCrudService, times(2)).save(packagesToBeSaved.capture());
        List<GroupPricingConfigurationPackage> savedPackages = packagesToBeSaved.getAllValues().get(0);
        assertEquals(1, savedPackages.size());
        assertPackage(savedPackages.get(0), modifiedPackageDTO);
        List<GroupPricingConfigurationPackageElementMap> savedPackageElementMaps = packagesToBeSaved.getAllValues().get(1);
        assertEquals(1, savedPackageElementMaps.size());
        GroupPricingConfigurationPackageElementMap actualPackageElementMap1 = savedPackageElementMaps.get(0);
        assertEquals(groupPricingConfigurationPackageElement.getId(), actualPackageElementMap1.getGroupPricingConfigurationPackageElement().getId());
        assertTrue(BigDecimalUtil.equals(packageElementMap1.getAmount(), actualPackageElementMap1.getAmount()));
        assertEquals(TenantStatusEnum.ACTIVE, savedPackageElementMaps.get(0).getStatus());
        verify(pacmanConfigParamsService, times(6)).getBooleanParameterValue(PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP);
    }

    @Test
    void shouldNotCallSaveWhenExistingPackageDoNotExistWhenUseFSRevenueStreamsIsFalse() {
        RevenueGroupDto revenueGroupDto = createRevenueGroupDto(1, "AVI");
        FunctionSpacePackageType packageType = buildPackageType(1, "PackageType1", TenantStatusEnum.ACTIVE);
        PackageElementDto packageElementDto = createPackageElementDto(1, "Food Lunch", revenueGroupDto);
        PackageElementMapDTO packageElementMap = buildPackageElementMapDTO(1, packageElementDto, BigDecimal.ONE, TenantStatusEnum.NEW);
        PackageDTO modifiedPackage = buildPackageDTO(1, "Package1", packageType, TenantStatusEnum.ACTIVE, true);
        modifiedPackage.setFunctionSpacePackageElementMapDTOList(List.of(packageElementMap));
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackage.FIND_ACTIVE_PACKAGES_BY_ID,
                QueryParameter.with("idSet", Set.of(1)).parameters())).thenReturn(List.of());
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP)).thenReturn(false);

        groupPricingPackageService.updatePackages(List.of(modifiedPackage));

        verify(tenantCrudService, never()).save(anyList());
    }

    @Test
    void shouldNotCallSaveWhenExistingPackageDoNotExistWhenUseFSRevenueStreamsIsTrue() {
        FunctionSpacePackageType fullDayPackageType = new FunctionSpacePackageType("Full Day", TenantStatusEnum.ACTIVE);
        FunctionSpacePackageType packageType1 = buildPackageType(1, "PackageType1", TenantStatusEnum.ACTIVE);
        PackageDTO modifiedPackageDTO = buildPackageDTO(2, "Package1", packageType1, TenantStatusEnum.ACTIVE, true);
        FunctionSpacePackage functionSpacePackage = FunctionSpaceObjectMother.buildFunctionSpacePackage("Food Package", fullDayPackageType);
        functionSpacePackage.setId(2);
        when(functionSpacePackageService.getFunctionSpacePackagesUsingId(Set.of(2))).thenReturn(List.of(functionSpacePackage));
        when(tenantCrudService.findByNamedQuery(eq(GroupPricingConfigurationPackage.FIND_ACTIVE_PACKAGES_BY_NAME),
                anyMap())).thenReturn(List.of());
        FunctionSpacePackageElement functionSpacePackageElement1 = buildFunctionSpacePackageElement(3, "Food Lunch");
        FunctionSpacePackageElement functionSpacePackageElement2 = buildFunctionSpacePackageElement(4, "Bev Lunch");
        when(tenantCrudService.findByNamedQuery(eq(FunctionSpacePackageElement.FIND_ALL_PACKAGE_ELEMENTS_BY_ID), anyMap()))
                .thenReturn(List.of(functionSpacePackageElement1, functionSpacePackageElement2));
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP)).thenReturn(true);

        groupPricingPackageService.updatePackages(List.of(modifiedPackageDTO));

        verify(tenantCrudService, never()).save(anyList());
    }

    @Test
    void shouldUpdatePackagesAlongWithModifiedPackageElementMapsWhenUseFSRevenueStreamsIsFalse() {
        FunctionSpacePackageType fullDayPackageType = new FunctionSpacePackageType("Full Day", TenantStatusEnum.ACTIVE);
        RevenueGroupDto revenueGroupDto = createRevenueGroupDto(1, "AVI");
        PackageElementDto packageElementDto1 = createPackageElementDto(1, "Food Lunch", revenueGroupDto);
        PackageElementDto packageElementDto2 = createPackageElementDto(2, "Bev Lunch", revenueGroupDto);
        PackageElementMapDTO packageElementMap1 = buildPackageElementMapDTO(1, packageElementDto1, BigDecimal.ONE, TenantStatusEnum.DELETED);
        PackageElementMapDTO packageElementMap2 = buildPackageElementMapDTO(2, packageElementDto2, BigDecimal.TEN, TenantStatusEnum.ACTIVE);
        PackageDTO modifiedPackageDTO = buildPackageDTO(1, "Food Package", fullDayPackageType, TenantStatusEnum.ACTIVE, false);
        modifiedPackageDTO.setFunctionSpacePackageElementMapDTOList(List.of(packageElementMap1, packageElementMap2));
        GroupPricingConfigurationConferenceAndBanquet revenueGroup = createRevenueGroup(1, "AVI");
        GroupPricingConfigurationPackageElement packageElement = createPackageElement(1, "Food Lunch", revenueGroup);
        packageElement.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackage existingPackage = createGroupPricingPackage(1, "Food Package");
        existingPackage.setPackageType(fullDayPackageType);
        existingPackage.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackageElementMap packageElementMap = createGroupPricingPackageElementMap(1, packageElement, existingPackage);
        packageElementMap.setStatus(TenantStatusEnum.ACTIVE);
        existingPackage.setGroupPricingPackageElementMapSet(Set.of(packageElementMap));
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackage.FIND_ACTIVE_PACKAGES_BY_ID,
                QueryParameter.with("idSet", Set.of(1)).parameters())).thenReturn(List.of(existingPackage));

        groupPricingPackageService.updatePackages(List.of(modifiedPackageDTO));

        ArgumentCaptor<List> packagesToBeSaved = ArgumentCaptor.forClass(List.class);
        verify(tenantCrudService, times(2)).save(packagesToBeSaved.capture());
        List<GroupPricingConfigurationPackage> savedPackages = packagesToBeSaved.getAllValues().get(0);
        assertEquals(1, savedPackages.size());
        assertPackage(savedPackages.get(0), modifiedPackageDTO);
        List<GroupPricingConfigurationPackageElementMap> savedPackageElementMaps = packagesToBeSaved.getAllValues().get(1);
        assertEquals(1, savedPackageElementMaps.size());
        GroupPricingConfigurationPackageElementMap actualPackageElementMap = savedPackageElementMaps.get(0);
        assertEquals(packageElementMap1.getPackageElementDTO().getId(), actualPackageElementMap.getGroupPricingConfigurationPackageElement().getId());
        assertTrue(BigDecimalUtil.equals(packageElementMap1.getAmount(), actualPackageElementMap.getAmount()));
        assertEquals(TenantStatusEnum.DELETED, savedPackageElementMaps.get(0).getStatus());
    }

    @Test
    void shouldUpdatePackagesAlongWithModifiedPackageElementMapsWhenUseFSRevenueStreamsIsTrue() {
        RevenueGroupDto revenueGroupDto = createRevenueGroupDto(1, "AVI");
        FunctionSpacePackageType fullDayPackageType = new FunctionSpacePackageType("Full Day", TenantStatusEnum.ACTIVE);
        FunctionSpacePackageType packageType1 = buildPackageType(1, "PackageType1", TenantStatusEnum.ACTIVE);
        PackageElementDto packageElementDto1 = createPackageElementDto(3, "Food Lunch", revenueGroupDto);
        packageElementDto1.setTaxBucket(BigDecimal.TEN);
        PackageElementDto packageElementDto2 = createPackageElementDto(4, "Bev Lunch", revenueGroupDto);
        PackageElementMapDTO packageElementMap1 = buildPackageElementMapDTO(1, packageElementDto1, BigDecimal.ONE, TenantStatusEnum.ACTIVE);
        PackageElementMapDTO packageElementMap2 = buildPackageElementMapDTO(2, packageElementDto2, BigDecimal.TEN, TenantStatusEnum.ACTIVE);
        PackageDTO modifiedPackageDTO = buildPackageDTO(2, "Package1", packageType1, TenantStatusEnum.ACTIVE, true);
        modifiedPackageDTO.setFunctionSpacePackageElementMapDTOList(List.of(packageElementMap1, packageElementMap2));
        GroupPricingConfigurationPackageElement groupPricingConfigurationPackageElement = foodLunchGroupPricingConfigurationPackageElement();
        GroupPricingConfigurationPackage groupPricingConfigurationPackage = foodPackageGroupPricingConfigurationPackage(fullDayPackageType, groupPricingConfigurationPackageElement);
        FunctionSpacePackage functionSpacePackage = FunctionSpaceObjectMother.buildFunctionSpacePackage("Food Package", fullDayPackageType);
        functionSpacePackage.setId(2);
        when(functionSpacePackageService.getFunctionSpacePackagesUsingId(Set.of(2))).thenReturn(List.of(functionSpacePackage));
        when(tenantCrudService.findByNamedQuery(eq(GroupPricingConfigurationPackage.FIND_ACTIVE_PACKAGES_BY_NAME),
                anyMap())).thenReturn(List.of(groupPricingConfigurationPackage));
        FunctionSpacePackageElement functionSpacePackageElement1 = buildFunctionSpacePackageElement(3, "Food Lunch");
        functionSpacePackageElement1.setTaxBucket(BigDecimal.TEN);
        FunctionSpacePackageElement functionSpacePackageElement2 = buildFunctionSpacePackageElement(4, "Bev Lunch");
        when(tenantCrudService.findByNamedQuery(eq(FunctionSpacePackageElement.FIND_ALL_PACKAGE_ELEMENTS_BY_ID), anyMap()))
                .thenReturn(List.of(functionSpacePackageElement1, functionSpacePackageElement2));
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackageElement.FIND_ALL_ACTIVE_PACKAGE_ELEMENTS))
                .thenReturn(List.of(groupPricingConfigurationPackageElement));
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP)).thenReturn(true);

        groupPricingPackageService.updatePackages(List.of(modifiedPackageDTO));

        ArgumentCaptor<List> packagesToBeSaved = ArgumentCaptor.forClass(List.class);
        verify(tenantCrudService, times(2)).save(packagesToBeSaved.capture());
        List<GroupPricingConfigurationPackage> savedPackages = packagesToBeSaved.getAllValues().get(0);
        assertEquals(1, savedPackages.size());
        assertPackage(savedPackages.get(0), modifiedPackageDTO);
        List<GroupPricingConfigurationPackageElementMap> savedPackageElementMaps = packagesToBeSaved.getAllValues().get(1);
        assertEquals(1, savedPackageElementMaps.size());
        GroupPricingConfigurationPackageElementMap actualPackageElementMap = savedPackageElementMaps.get(0);
        assertEquals(packageElementMap1.getPackageElementDTO().getName(), actualPackageElementMap.getGroupPricingConfigurationPackageElement().getName());
        assertTrue(BigDecimalUtil.equals(packageElementMap1.getPackageElementDTO().getTaxBucket(), actualPackageElementMap.getGroupPricingConfigurationPackageElement().getTaxBucket()));
        assertTrue(BigDecimalUtil.equals(packageElementMap1.getAmount(), actualPackageElementMap.getAmount()));
        assertEquals(TenantStatusEnum.ACTIVE, savedPackageElementMaps.get(0).getStatus());
        verify(pacmanConfigParamsService, times(6)).getBooleanParameterValue(PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP);
    }

    @Test
    void shouldUpdatePackagesOnlyWhenUseFSRevenueStreamsIsTrue() {
        FunctionSpacePackageType fullDayPackageType = new FunctionSpacePackageType("Full Day", TenantStatusEnum.ACTIVE);
        FunctionSpacePackageType packageType1 = buildPackageType(1, "PackageType1", TenantStatusEnum.ACTIVE);
        PackageDTO modifiedPackageDTO = buildPackageDTO(2, "Package1", packageType1, TenantStatusEnum.ACTIVE, true);
        GroupPricingConfigurationPackageElement groupPricingConfigurationPackageElement = foodLunchGroupPricingConfigurationPackageElement();
        GroupPricingConfigurationPackage groupPricingConfigurationPackage = foodPackageGroupPricingConfigurationPackage(fullDayPackageType, groupPricingConfigurationPackageElement);
        FunctionSpacePackage functionSpacePackage = FunctionSpaceObjectMother.buildFunctionSpacePackage("Food Package", fullDayPackageType);
        functionSpacePackage.setId(2);
        when(functionSpacePackageService.getFunctionSpacePackagesUsingId(Set.of(2))).thenReturn(List.of(functionSpacePackage));
        when(tenantCrudService.findByNamedQuery(eq(GroupPricingConfigurationPackage.FIND_ACTIVE_PACKAGES_BY_NAME),
                anyMap())).thenReturn(List.of(groupPricingConfigurationPackage));
        FunctionSpacePackageElement functionSpacePackageElement1 = buildFunctionSpacePackageElement(3, "Food Lunch");
        functionSpacePackageElement1.setTaxBucket(BigDecimal.TEN);
        FunctionSpacePackageElement functionSpacePackageElement2 = buildFunctionSpacePackageElement(4, "Bev Lunch");
        when(tenantCrudService.findByNamedQuery(eq(FunctionSpacePackageElement.FIND_ALL_PACKAGE_ELEMENTS_BY_ID), anyMap()))
                .thenReturn(List.of(functionSpacePackageElement1, functionSpacePackageElement2));
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackageElement.FIND_ALL_ACTIVE_PACKAGE_ELEMENTS))
                .thenReturn(List.of(groupPricingConfigurationPackageElement));
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP)).thenReturn(true);

        groupPricingPackageService.updatePackages(List.of(modifiedPackageDTO));

        ArgumentCaptor<List> packagesToBeSaved = ArgumentCaptor.forClass(List.class);
        verify(tenantCrudService, times(1)).save(packagesToBeSaved.capture());
        List<GroupPricingConfigurationPackage> savedPackages = packagesToBeSaved.getAllValues().get(0);
        assertEquals(1, savedPackages.size());
        assertPackage(savedPackages.get(0), modifiedPackageDTO);
        verify(pacmanConfigParamsService, times(1)).getBooleanParameterValue(PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP);
    }

    @Test
    void shouldUpdatePackagesAlongWithModifiedPackageElementMapsWithInvalidAmountWhenUseFSRevenueStreamsIsFalse() {
        FunctionSpacePackageType fullDayPackageType = new FunctionSpacePackageType("Full Day", TenantStatusEnum.ACTIVE);
        RevenueGroupDto revenueGroupDto = createRevenueGroupDto(1, "AVI");
        PackageElementDto packageElementDto1 = createPackageElementDto(1, "Food Lunch", revenueGroupDto);
        PackageElementDto packageElementDto2 = createPackageElementDto(2, "Bev Lunch", revenueGroupDto);
        PackageElementMapDTO packageElementMap1 = buildPackageElementMapDTO(1, packageElementDto1, new BigDecimal("999999999999999999.0"), TenantStatusEnum.DELETED);
        PackageElementMapDTO packageElementMap2 = buildPackageElementMapDTO(2, packageElementDto2, BigDecimal.TEN, TenantStatusEnum.ACTIVE);
        PackageDTO modifiedPackage = buildPackageDTO(1, "Food Package", fullDayPackageType, TenantStatusEnum.ACTIVE, false);
        modifiedPackage.setFunctionSpacePackageElementMapDTOList(List.of(packageElementMap1, packageElementMap2));
        GroupPricingConfigurationConferenceAndBanquet revenueGroup = createRevenueGroup(1, "AVI");
        GroupPricingConfigurationPackageElement packageElement = createPackageElement(1, "Food Lunch", revenueGroup);
        packageElement.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackage existingPackage = createGroupPricingPackage(1, "Food Package");
        existingPackage.setPackageType(fullDayPackageType);
        existingPackage.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackageElementMap packageElementMap = createGroupPricingPackageElementMap(1, packageElement, existingPackage);
        packageElementMap.setStatus(TenantStatusEnum.ACTIVE);
        existingPackage.setGroupPricingPackageElementMapSet(Set.of(packageElementMap));
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackage.FIND_ACTIVE_PACKAGES_BY_ID,
                QueryParameter.with("idSet", Set.of(1)).parameters())).thenReturn(List.of(existingPackage));

        groupPricingPackageService.updatePackages(List.of(modifiedPackage));

        ArgumentCaptor<List> packagesToBeSaved = ArgumentCaptor.forClass(List.class);
        verify(tenantCrudService, times(2)).save(packagesToBeSaved.capture());
        List<GroupPricingConfigurationPackage> savedPackages = packagesToBeSaved.getAllValues().get(0);
        assertEquals(1, savedPackages.size());
        assertPackage(savedPackages.get(0), modifiedPackage);
        List<GroupPricingConfigurationPackageElementMap> savedPackageElementMaps = packagesToBeSaved.getAllValues().get(1);
        assertEquals(1, savedPackageElementMaps.size());
        GroupPricingConfigurationPackageElementMap actualPackageElementMap = savedPackageElementMaps.get(0);
        assertEquals(packageElementMap1.getPackageElementDTO().getId(), actualPackageElementMap.getGroupPricingConfigurationPackageElement().getId());
        assertTrue(BigDecimalUtil.equals(BigDecimal.ZERO, actualPackageElementMap.getAmount()));
        assertEquals(TenantStatusEnum.DELETED, actualPackageElementMap.getStatus());
    }

    @Test
    void shouldUpdatePackagesAlongWithModifiedPackageElementMapsWithInvalidAmountWhenUseFSRevenueStreamsIsTrue() {
        RevenueGroupDto revenueGroupDto = createRevenueGroupDto(1, "AVI");
        FunctionSpacePackageType fullDayPackageType = new FunctionSpacePackageType("Full Day", TenantStatusEnum.ACTIVE);
        FunctionSpacePackageType packageType1 = buildPackageType(1, "PackageType1", TenantStatusEnum.ACTIVE);
        PackageElementDto packageElementDto1 = createPackageElementDto(3, "Food Lunch", revenueGroupDto);
        packageElementDto1.setTaxBucket(BigDecimal.TEN);
        PackageElementDto packageElementDto2 = createPackageElementDto(4, "Bev Lunch", revenueGroupDto);
        PackageElementMapDTO packageElementMap1 = buildPackageElementMapDTO(1, packageElementDto1, new BigDecimal("999999999999999999.0"), TenantStatusEnum.ACTIVE);
        PackageElementMapDTO packageElementMap2 = buildPackageElementMapDTO(2, packageElementDto2, BigDecimal.TEN, TenantStatusEnum.ACTIVE);
        PackageDTO modifiedPackageDTO = buildPackageDTO(2, "Package1", packageType1, TenantStatusEnum.ACTIVE, true);
        modifiedPackageDTO.setFunctionSpacePackageElementMapDTOList(List.of(packageElementMap1, packageElementMap2));
        GroupPricingConfigurationPackageElement groupPricingConfigurationPackageElement = foodLunchGroupPricingConfigurationPackageElement();
        GroupPricingConfigurationPackage groupPricingConfigurationPackage = foodPackageGroupPricingConfigurationPackage(fullDayPackageType, groupPricingConfigurationPackageElement);
        FunctionSpacePackage functionSpacePackage = FunctionSpaceObjectMother.buildFunctionSpacePackage("Food Package", fullDayPackageType);
        functionSpacePackage.setId(2);
        when(functionSpacePackageService.getFunctionSpacePackagesUsingId(Set.of(2))).thenReturn(List.of(functionSpacePackage));
        when(tenantCrudService.findByNamedQuery(eq(GroupPricingConfigurationPackage.FIND_ACTIVE_PACKAGES_BY_NAME),
                anyMap())).thenReturn(List.of(groupPricingConfigurationPackage));
        FunctionSpacePackageElement functionSpacePackageElement1 = buildFunctionSpacePackageElement(3, "Food Lunch");
        functionSpacePackageElement1.setTaxBucket(BigDecimal.TEN);
        FunctionSpacePackageElement functionSpacePackageElement2 = buildFunctionSpacePackageElement(4, "Bev Lunch");
        when(tenantCrudService.findByNamedQuery(eq(FunctionSpacePackageElement.FIND_ALL_PACKAGE_ELEMENTS_BY_ID), anyMap()))
                .thenReturn(List.of(functionSpacePackageElement1, functionSpacePackageElement2));
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackageElement.FIND_ALL_ACTIVE_PACKAGE_ELEMENTS))
                .thenReturn(List.of(groupPricingConfigurationPackageElement));
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP)).thenReturn(true);

        groupPricingPackageService.updatePackages(List.of(modifiedPackageDTO));

        ArgumentCaptor<List> packagesToBeSaved = ArgumentCaptor.forClass(List.class);
        verify(tenantCrudService, times(2)).save(packagesToBeSaved.capture());
        List<GroupPricingConfigurationPackage> savedPackages = packagesToBeSaved.getAllValues().get(0);
        assertEquals(1, savedPackages.size());
        assertPackage(savedPackages.get(0), modifiedPackageDTO);
        List<GroupPricingConfigurationPackageElementMap> savedPackageElementMaps = packagesToBeSaved.getAllValues().get(1);
        assertEquals(1, savedPackageElementMaps.size());
        GroupPricingConfigurationPackageElementMap actualPackageElementMap = savedPackageElementMaps.get(0);
        assertEquals(packageElementMap1.getPackageElementDTO().getName(), actualPackageElementMap.getGroupPricingConfigurationPackageElement().getName());
        assertTrue(BigDecimalUtil.equals(packageElementMap1.getPackageElementDTO().getTaxBucket(), actualPackageElementMap.getGroupPricingConfigurationPackageElement().getTaxBucket()));
        assertTrue(BigDecimalUtil.equals(BigDecimal.ZERO, actualPackageElementMap.getAmount()));
        assertEquals(TenantStatusEnum.ACTIVE, savedPackageElementMaps.get(0).getStatus());
        verify(pacmanConfigParamsService, times(6)).getBooleanParameterValue(PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP);
    }

    private GroupPricingConfigurationPackage foodPackageGroupPricingConfigurationPackage(FunctionSpacePackageType fullDayPackageType, GroupPricingConfigurationPackageElement groupPricingConfigurationPackageElement) {
        GroupPricingConfigurationPackage groupPricingConfigurationPackage = new GroupPricingConfigurationPackage("Food Package", fullDayPackageType, false, TenantStatusEnum.ACTIVE);
        groupPricingConfigurationPackage.setId(1);
        groupPricingConfigurationPackage.setLastUpdatedDate(LocalDateTime.now());
        GroupPricingConfigurationPackageElementMap groupPricingPackageElementMap = createGroupPricingPackageElementMap(1, groupPricingConfigurationPackageElement, groupPricingConfigurationPackage);
        groupPricingPackageElementMap.setStatus(TenantStatusEnum.ACTIVE);
        groupPricingConfigurationPackage.setGroupPricingPackageElementMapSet(Set.of(groupPricingPackageElementMap));
        return groupPricingConfigurationPackage;
    }

    private GroupPricingConfigurationPackageElement foodLunchGroupPricingConfigurationPackageElement() {
        GroupPricingConfigurationConferenceAndBanquet revenueGroup = createRevenueGroup(1, "AVI");
        GroupPricingConfigurationPackageElement groupPricingConfigurationPackageElement = createPackageElement(1, "Food Lunch", revenueGroup);
        groupPricingConfigurationPackageElement.setTaxBucket(BigDecimal.TEN);
        groupPricingConfigurationPackageElement.setStatus(TenantStatusEnum.ACTIVE);
        return groupPricingConfigurationPackageElement;
    }

    private FunctionSpacePackageElement buildFunctionSpacePackageElement(Integer id, String packageElementName) {
        FunctionSpacePackageElement functionSpacePackageElement = new FunctionSpacePackageElement();
        functionSpacePackageElement.setId(id);
        functionSpacePackageElement.setName(packageElementName);
        functionSpacePackageElement.setStatus(TenantStatusEnum.ACTIVE);
        return functionSpacePackageElement;
    }

    private void assertPackage(GroupPricingConfigurationPackage actualPackage,
                               PackageDTO expectedPackage) {
        assertEquals(expectedPackage.getPackageName(), actualPackage.getName());
        assertEquals(expectedPackage.getPackageType(), actualPackage.getPackageType());
        assertEquals(expectedPackage.getStatus(), actualPackage.getStatus());
        assertEquals(expectedPackage.isGuestRoomIncluded(), actualPackage.isGuestRoomIncluded());
    }

    private static PackageElementMapDTO buildPackageElementMapDTO(Integer id,
                                                                  PackageElementDto packageElementDto,
                                                                  BigDecimal amount,
                                                                  TenantStatusEnum status) {
        PackageElementMapDTO packageElementMap1 = new PackageElementMapDTO();
        packageElementMap1.setPackageElementMapId(id);
        packageElementMap1.setPackageElementDto(packageElementDto);
        packageElementMap1.setAmount(amount);
        packageElementMap1.setStatus(status);
        return packageElementMap1;
    }

    private FunctionSpacePackageType buildPackageType(int id,
                                                             String packageTypeName,
                                                             TenantStatusEnum status) {
        FunctionSpacePackageType functionSpacePackageType1 = new FunctionSpacePackageType();
        functionSpacePackageType1.setId(id);
        functionSpacePackageType1.setName(packageTypeName);
        functionSpacePackageType1.setStatus(status);
        return functionSpacePackageType1;
    }

    private PackageDTO buildPackageDTO(int packageId,
                                              String packageName,
                                              FunctionSpacePackageType packageType,
                                              TenantStatusEnum status,
                                              boolean isGuestRoomIncluded) {
        PackageDTO packageDTO1 = new PackageDTO();
        packageDTO1.setPackageId(packageId);
        packageDTO1.setPackageName(packageName);
        packageDTO1.setStatus(status);
        packageDTO1.setPackageType(packageType);
        packageDTO1.setGuestRoomIncluded(isGuestRoomIncluded);
        return packageDTO1;
    }


    @Test
    void shouldDeletePackageElementsFromGroupPricingWhenToggleToUseFunctionSpaceDataInGroupPricingIsDisabled(){
        ArgumentCaptor<List<GroupPricingConfigurationPackageElement>> gpPackageElementsCaptor = ArgumentCaptor.forClass(List.class);
        GroupPricingConfigurationConferenceAndBanquet revenueGroup = createRevenueGroup(4, "Food");
        GroupPricingConfigurationPackageElement packageElement1 =  createPackageElement(1,"PkgElm1",revenueGroup);
        packageElement1.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackageElement packageElement2 =  createPackageElement(2,"PkgElm2",revenueGroup);
        packageElement2.setStatus(TenantStatusEnum.ACTIVE);
        RevenueGroupDto revenueGroupDto = createRevenueGroupDto(4, "Food");
        List<PackageElementDto> tobeDeletedPackageElementDtos = Arrays.asList(
                createPackageElementDto(1,"PkgElm1",revenueGroupDto),
                createPackageElementDto(2,"PkgElm2",revenueGroupDto));
        tobeDeletedPackageElementDtos.get(0).setStatus(TenantStatusEnum.ACTIVE);
        tobeDeletedPackageElementDtos.get(1).setStatus(TenantStatusEnum.ACTIVE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP)).thenReturn(false);
        when(tenantCrudService.findByNamedQuery(eq(GroupPricingConfigurationPackageElement.FIND_ALL_ACTIVE_PACKAGE_ELEMENTS))).thenReturn(Arrays.asList(packageElement1,packageElement2));

        groupPricingPackageService.deletePackageElements(tobeDeletedPackageElementDtos);

        verify(tenantCrudService, times(1)).save(gpPackageElementsCaptor.capture());
        List<GroupPricingConfigurationPackageElement> deletedElements = gpPackageElementsCaptor.getValue();
        assertEquals(1, deletedElements.get(0).getId());
        assertEquals("PkgElm1", deletedElements.get(0).getName());
        assertEquals(TenantStatusEnum.DELETED, deletedElements.get(0).getStatus());
        assertEquals(2, deletedElements.get(1).getId());
        assertEquals("PkgElm2", deletedElements.get(1).getName());
        assertEquals(TenantStatusEnum.DELETED, deletedElements.get(1).getStatus());
        verify(pacmanConfigParamsService,times(2)).getBooleanParameterValue(PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP);
        verify(tenantCrudService).findByNamedQuery(GroupPricingConfigurationPackageElement.FIND_ALL_ACTIVE_PACKAGE_ELEMENTS);
    }

    @Test
    void shouldDeletePackageElementsFromGroupPricingWhenToggleToUseFunctionSpaceDataInGroupPricingIsEnabled(){
        ArgumentCaptor<List<GroupPricingConfigurationPackageElement>> gpPackageElementsCaptor = ArgumentCaptor.forClass(List.class);
        GroupPricingConfigurationConferenceAndBanquet revenueGroup = createRevenueGroup(4, "Food");
        GroupPricingConfigurationPackageElement packageElement1 =  createPackageElement(1,"PkgElm1",revenueGroup);
        packageElement1.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackageElement packageElement2 =  createPackageElement(2,"PkgElm2",revenueGroup);
        packageElement2.setStatus(TenantStatusEnum.ACTIVE);
        RevenueGroupDto revenueGroupDto = createRevenueGroupDto(4, "Food");
        List<PackageElementDto> tobeDeletedPackageElementDtos = Arrays.asList(
                createPackageElementDto(6,"PkgElm1",revenueGroupDto),
                createPackageElementDto(4,"PkgElm2",revenueGroupDto)
        );
        tobeDeletedPackageElementDtos.get(0).setStatus(TenantStatusEnum.ACTIVE);
        tobeDeletedPackageElementDtos.get(1).setStatus(TenantStatusEnum.ACTIVE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP)).thenReturn(true);
        when(tenantCrudService.findByNamedQuery(eq(GroupPricingConfigurationPackageElement.FIND_ALL_ACTIVE_PACKAGE_ELEMENTS))).thenReturn(Arrays.asList(packageElement1,packageElement2));

        groupPricingPackageService.deletePackageElements(tobeDeletedPackageElementDtos);

        verify(tenantCrudService, times(1)).save(gpPackageElementsCaptor.capture());
        List<GroupPricingConfigurationPackageElement> deletedElements = gpPackageElementsCaptor.getValue();
        assertEquals(1, deletedElements.get(0).getId());
        assertEquals("PkgElm1", deletedElements.get(0).getName());
        assertEquals(TenantStatusEnum.DELETED, deletedElements.get(0).getStatus());
        assertEquals(2, deletedElements.get(1).getId());
        assertEquals("PkgElm2", deletedElements.get(1).getName());
        assertEquals(TenantStatusEnum.DELETED, deletedElements.get(1).getStatus());
        verify(pacmanConfigParamsService,times(2)).getBooleanParameterValue(PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP);
        verify(tenantCrudService).findByNamedQuery(GroupPricingConfigurationPackageElement.FIND_ALL_ACTIVE_PACKAGE_ELEMENTS);
    }

    @Test
    void shouldDeletePkgElmMapAssociatedWithDeletedPkgElmWhenToggleToUseFunctionSpaceDataInGroupPricingIsEnabled(){
        ArgumentCaptor<List<GroupPricingConfigurationPackageElementMap>> gpPackageElementsCaptor = ArgumentCaptor.forClass(List.class);
        RevenueGroupDto revenueGroupDto = createRevenueGroupDto(1, "Food");
        List<PackageElementDto> tobeDeletedPackageElementDtos = Arrays.asList(
                createPackageElementDto(6,"PackageElement1",revenueGroupDto),
                createPackageElementDto(4,"PackageElement2",revenueGroupDto));
        tobeDeletedPackageElementDtos.get(0).setStatus(TenantStatusEnum.ACTIVE);
        tobeDeletedPackageElementDtos.get(1).setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationConferenceAndBanquet revenueGroup = createRevenueGroup(4, "Food");
        GroupPricingConfigurationPackageElement packageElement1 = createPackageElement(1, "PackageElement1", revenueGroup);
        packageElement1.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackageElement packageElement2 = createPackageElement(2, "PackageElement2", revenueGroup);
        packageElement2.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackage gpPackage = createGroupPricingPackage(1, "Package");
        GroupPricingConfigurationPackageElementMap packageElementMap1 = new GroupPricingConfigurationPackageElementMap();
        packageElementMap1.setId(1);
        packageElementMap1.setGroupPricingConfigurationPackage(gpPackage);
        packageElementMap1.setGroupPricingConfigurationPackageElement(packageElement1);
        packageElementMap1.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackageElementMap packageElementMap2 = new GroupPricingConfigurationPackageElementMap();
        packageElementMap2.setId(2);
        packageElementMap2.setGroupPricingConfigurationPackage(gpPackage);
        packageElementMap2.setGroupPricingConfigurationPackageElement(packageElement2);
        packageElementMap2.setStatus(TenantStatusEnum.ACTIVE);
        FunctionSpacePackageElement fsPackageElement1 = createFSPackageElement(6,"PackageElement1");
        FunctionSpacePackageElement fsPackageElement2 = createFSPackageElement(4,"PackageElement2");
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP)).thenReturn(true);
        when(tenantCrudService.findByNamedQuery(eq(FunctionSpacePackageElement.FIND_ALL_PACKAGE_ELEMENTS_BY_ID), any())).thenReturn(List.of(fsPackageElement1,fsPackageElement2));
        when(tenantCrudService.findByNamedQuery(eq(GroupPricingConfigurationPackageElement.FIND_ALL_ACTIVE_PACKAGE_ELEMENTS))).thenReturn(List.of(packageElement1,packageElement2));
        when(tenantCrudService.findByNamedQuery(eq(GroupPricingConfigurationPackageElement.FIND_ALL_PACKAGE_ELEMENTS_BY_ID),any())).thenReturn(List.of(packageElement1,packageElement2));
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackageElementMap.FIND_ACTIVE_PACKAGE_ELEMENT_MAPS_ASSOCIATED_WITH_PACKAGE_ELEMENTS, Map.of(
                "idSet", List.of(packageElement1.getId(), packageElement2.getId())))).thenReturn(List.of(packageElementMap1, packageElementMap2));

        groupPricingPackageService.deletePackageElementMaps(tobeDeletedPackageElementDtos);

        verify(tenantCrudService, times(1)).save(gpPackageElementsCaptor.capture());
        List<GroupPricingConfigurationPackageElementMap> deletedPkgElmMaps = gpPackageElementsCaptor.getValue();
        assertEquals(2,deletedPkgElmMaps.size());
        assertEquals(1,deletedPkgElmMaps.get(0).getId());
        assertEquals(TenantStatusEnum.DELETED,deletedPkgElmMaps.get(0).getStatus());
        assertEquals(2,deletedPkgElmMaps.get(1).getId());
        assertEquals(TenantStatusEnum.DELETED,deletedPkgElmMaps.get(1).getStatus());
        verify(pacmanConfigParamsService,times(2)).getBooleanParameterValue(PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP);
        verify(tenantCrudService).findByNamedQuery(GroupPricingConfigurationPackageElement.FIND_ALL_ACTIVE_PACKAGE_ELEMENTS);
        verify(tenantCrudService).findByNamedQuery(eq(GroupPricingConfigurationPackageElement.FIND_ALL_PACKAGE_ELEMENTS_BY_ID), any());
        verify(tenantCrudService).findByNamedQuery(eq(FunctionSpacePackageElement.FIND_ALL_PACKAGE_ELEMENTS_BY_ID), any());
        verify(tenantCrudService).findByNamedQuery(eq(GroupPricingConfigurationPackageElementMap.FIND_ACTIVE_PACKAGE_ELEMENT_MAPS_ASSOCIATED_WITH_PACKAGE_ELEMENTS), anyMap());
    }

    @Test
    void shouldDeletePackageElementMapAssociatedWithPackageToBeDeletedWhenToggleToUseFunctionSpaceDataInGroupPricingIsDisabled(){
        ArgumentCaptor<List<GroupPricingConfigurationPackageElementMap>> gpPackageElementsCaptor = ArgumentCaptor.forClass(List.class);
        RevenueGroupDto revenueGroupDto = createRevenueGroupDto(1, "Food");
        List<PackageElementDto> tobeDeletedPackageElementDtos = List.of(
                createPackageElementDto(1, "PackageElement1", revenueGroupDto));
        tobeDeletedPackageElementDtos.get(0).setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationConferenceAndBanquet revenueGroup = createRevenueGroup(4, "Food");
        GroupPricingConfigurationPackageElement packageElement1 = createPackageElement(1, "PackageElement1", revenueGroup);
        packageElement1.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackageElement packageElement2 = createPackageElement(2, "PackageElement2", revenueGroup);
        packageElement2.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackage gpPackage = createGroupPricingPackage(1, "Package");
        GroupPricingConfigurationPackageElementMap packageElementMap1 = new GroupPricingConfigurationPackageElementMap();
        packageElementMap1.setId(1);
        packageElementMap1.setGroupPricingConfigurationPackage(gpPackage);
        packageElementMap1.setGroupPricingConfigurationPackageElement(packageElement1);
        packageElementMap1.setStatus(TenantStatusEnum.ACTIVE);
        GroupPricingConfigurationPackageElementMap packageElementMap2 = new GroupPricingConfigurationPackageElementMap();
        packageElementMap2.setId(2);
        packageElementMap2.setGroupPricingConfigurationPackage(gpPackage);
        packageElementMap2.setGroupPricingConfigurationPackageElement(packageElement2);
        packageElementMap2.setStatus(TenantStatusEnum.ACTIVE);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP)).thenReturn(false);
        when(tenantCrudService.findByNamedQuery(eq(GroupPricingConfigurationPackageElement.FIND_ALL_PACKAGE_ELEMENTS_BY_ID),any())).thenReturn(List.of(packageElement1));
        when(tenantCrudService.findByNamedQuery(GroupPricingConfigurationPackageElementMap.FIND_ACTIVE_PACKAGE_ELEMENT_MAPS_ASSOCIATED_WITH_PACKAGE_ELEMENTS, Map.of(
                "idSet", List.of(packageElement1.getId())))).thenReturn(List.of(packageElementMap1));

        groupPricingPackageService.deletePackageElementMaps(tobeDeletedPackageElementDtos);

        verify(tenantCrudService, times(1)).save(gpPackageElementsCaptor.capture());
        List<GroupPricingConfigurationPackageElementMap> deletedPkgElmMaps = gpPackageElementsCaptor.getValue();
        assertEquals(1,deletedPkgElmMaps.size());
        assertEquals(1,deletedPkgElmMaps.get(0).getId());
        assertEquals(TenantStatusEnum.DELETED,deletedPkgElmMaps.get(0).getStatus());
        verify(pacmanConfigParamsService).getBooleanParameterValue(PreProductionConfigParamName.USE_FS_REVENUE_STREAMS_FOR_GP);
        verify(tenantCrudService).findByNamedQuery(eq(GroupPricingConfigurationPackageElement.FIND_ALL_PACKAGE_ELEMENTS_BY_ID), any());
        verify(tenantCrudService).findByNamedQuery(eq(GroupPricingConfigurationPackageElementMap.FIND_ACTIVE_PACKAGE_ELEMENT_MAPS_ASSOCIATED_WITH_PACKAGE_ELEMENTS), anyMap());
    }

    private FunctionSpacePackageElement createFSPackageElement(Integer id, String name) {
        FunctionSpacePackageElement packageElement = new FunctionSpacePackageElement();
        packageElement.setId(id);
        packageElement.setName(name);
        packageElement.setTaxBucket(BigDecimal.ZERO);
        packageElement.setFunctionSpaceRevenueGroup(buildFSRevenueGroup(1, "Food"));
        packageElement.setStatus(TenantStatusEnum.ACTIVE);
        packageElement.setIncludedInPackage(true);
        packageElement.setCommissionable(false);
        packageElement.setServiceCharge(true);

        return packageElement;
    }

    private FunctionSpaceRevenueGroup buildFSRevenueGroup(int id, String name) {
        FunctionSpaceRevenueGroup revenueGroup = new FunctionSpaceRevenueGroup();
        revenueGroup.setId(id);
        revenueGroup.setName(name);
        revenueGroup.setProfitPercent(BigDecimal.TEN);
        revenueGroup.setStatus(TenantStatusEnum.ACTIVE);
        return revenueGroup;
    }

    private GroupPricingConfigurationPackageElementMap createGroupPricingPackageElementMap(int id,
                                                                                           GroupPricingConfigurationPackageElement packageElement,
                                                                                           GroupPricingConfigurationPackage groupPricingConfigurationPackage) {
        GroupPricingConfigurationPackageElementMap packageElementMap = new GroupPricingConfigurationPackageElementMap();
        packageElementMap.setId(id);
        packageElementMap.setGroupPricingConfigurationPackageElement(packageElement);
        packageElementMap.setGroupPricingConfigurationPackage(groupPricingConfigurationPackage);
        return packageElementMap;
    }

    private GroupPricingConfigurationPackage createGroupPricingPackage(int id, String packageName) {
        GroupPricingConfigurationPackage groupPricingConfigurationPackage = new GroupPricingConfigurationPackage();
        groupPricingConfigurationPackage.setId(id);
        groupPricingConfigurationPackage.setName(packageName);
        groupPricingConfigurationPackage.setLastUpdatedDate(LocalDateTime.now());
        return groupPricingConfigurationPackage;
    }

    private void assertRevenueGroupDto(RevenueGroupDto revenueGroupDto,
                                       GroupPricingConfigurationConferenceAndBanquet revenuegroup) {
        assertEquals(revenuegroup.getId(), revenueGroupDto.getId());
        assertEquals(revenuegroup.getRevenueStream(), revenueGroupDto.getName());
        assertEquals(revenuegroup.getProfitPercentage(), revenueGroupDto.getProfitPercent());
    }

    private void assertPackageElement(PackageElementDto packageElementDto,
                                      GroupPricingConfigurationPackageElement packageElement) {
        assertEquals(packageElement.getId(), packageElementDto.getId());
        assertEquals(packageElement.getName(), packageElementDto.getName());
        assertEquals(0, packageElement.getTaxBucket().compareTo(packageElementDto.getTaxBucket()));
        assertEquals(packageElement.isIncludedInPackage(), packageElementDto.isIncludedInPackage());
        assertEquals(packageElement.isServiceCharge(), packageElementDto.isServiceCharge());
        assertEquals(packageElement.isCommissionable(), packageElementDto.isCommissionable());
        assertEquals(packageElement.getConferenceAndBanquet().getId(), packageElementDto.getRevenueGroupDto().getId());
        assertEquals(packageElement.getStatus(), packageElementDto.getStatus());
    }

    private PackageElementDto createPackageElementDto(Integer id, String name,
                                                      RevenueGroupDto revenueGroupDto) {
        PackageElementDto packageElementDto = new PackageElementDto();
        packageElementDto.setId(id);
        packageElementDto.setName(name);
        packageElementDto.setRevenueGroup(revenueGroupDto);
        packageElementDto.setTaxBucket(BigDecimal.ZERO);
        packageElementDto.setCommissionable(true);
        packageElementDto.setServiceCharge(true);
        packageElementDto.setIncludedInPackage(true);
        packageElementDto.setStatus(TenantStatusEnum.DELETED);
        return packageElementDto;
    }

    private GroupPricingConfigurationPackageElement createPackageElement(Integer id, String name,
                                                                         GroupPricingConfigurationConferenceAndBanquet conferenceAndBanquet) {
        GroupPricingConfigurationPackageElement packageElement = new GroupPricingConfigurationPackageElement();
        packageElement.setId(id);
        packageElement.setName(name);
        packageElement.setConferenceAndBanquet(conferenceAndBanquet);
        packageElement.setTaxBucket(BigDecimal.ZERO);
        packageElement.setCommissionable(true);
        packageElement.setServiceCharge(true);
        packageElement.setIncludedInPackage(true);
        packageElement.setStatus(TenantStatusEnum.DELETED);
        return packageElement;
    }

    private RevenueGroupDto createRevenueGroupDto(Integer id, String name) {
        RevenueGroupDto revenueGroupDto = new RevenueGroupDto();
        revenueGroupDto.setId(id);
        revenueGroupDto.setName(name);
        revenueGroupDto.setProfitPercent(BigDecimal.TEN);
        return revenueGroupDto;
    }

    private GroupPricingConfigurationConferenceAndBanquet createRevenueGroup(Integer id, String name) {
        GroupPricingConfigurationConferenceAndBanquet revenueGroup = new GroupPricingConfigurationConferenceAndBanquet();
        revenueGroup.setId(id);
        revenueGroup.setRevenueStream(name);
        revenueGroup.setProfitPercentage(BigDecimal.TEN);
        return revenueGroup;
    }
}