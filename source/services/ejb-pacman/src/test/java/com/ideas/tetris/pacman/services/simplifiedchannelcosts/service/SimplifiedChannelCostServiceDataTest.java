package com.ideas.tetris.pacman.services.simplifiedchannelcosts.service;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.services.simplifiedchannelcosts.dto.SimplifiedChannelCostConfigDTO;
import com.ideas.tetris.pacman.services.simplifiedchannelcosts.entity.SimplifiedChannelCostConfiguration;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.utils.map.MapBuilder;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.apache.commons.lang.ArrayUtils;
import org.exparity.hamcrest.date.DateMatchers;
import org.hamcrest.Matcher;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.services.simplifiedchannelcosts.enums.ChannelCostType.COMMISSION;
import static java.math.BigDecimal.ROUND_HALF_UP;
import static java.math.BigDecimal.ZERO;
import static java.math.BigDecimal.valueOf;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.closeTo;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasProperty;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
public class SimplifiedChannelCostServiceDataTest extends AbstractG3JupiterTest {

    private static final Date START_DATE = DateUtil.getCurrentDate();
    private static final Date END_DATE = DateUtil.addDaysToDate(DateUtil.getCurrentDate(), 10);
    private static final BigDecimal FIXED_COST = valueOf(9.3);
    private static final BigDecimal PERCENTAGE_COST = valueOf(12);
    private static final String EXPEDIA = "EXPEDIAEXPEDIAEXPEDIAEXPEDIAEXPEDIAEXPEDIAEXPEDIAEXPEDIA";
    private static final String TRUNCATED_EXPEDIA = EXPEDIA.substring(0, 50);
    private static final String GDS = "GDS";
    private static final String MARKET_CODE_1 = "M1";
    private static final String RATE_CODE_1 = "R1";

    private static final ScriptEngine EXPRESSION_EVALUATOR = new ScriptEngineManager().getEngineByName("JavaScript");

    /*
     * Formulae for channel cost calculations
     */
    private static final String TOTAL_CC_FORMULA = "= RoomRevenue * Percentage_Cost/100 + (Fixed_Cost/Length_Of_Stay)";
    private static final String TOTAL_AQ_CC_FORMULA = "= RateValue * Percentage_Cost/100 + (Fixed_Cost/Length_Of_Stay)";

    private static final String NET_REVENUE_FORMULA = "= RoomRevenue - (RoomRevenue * Percentage_Cost/100 + (Fixed_Cost/Length_Of_Stay))";
    private static final String NET_RATE_VALUE_FORMULA = "= RateValue - (RateValue * Percentage_Cost/100 + (Fixed_Cost/Length_Of_Stay))";

    private static final String TOTAL_REVENUE_FORMULA = "= TotalRevenue + (TotalRevenue * Percentage_Cost/100 + (Fixed_Cost/Length_Of_Stay))";
    private static final String ROOM_REVENUE_FORMULA = "= RoomRevenue + (RoomRevenue * Percentage_Cost/100 + (Fixed_Cost/Length_Of_Stay))";
    private static final String FOOD_REVENUE_FORMULA = "= FoodRevenue + (FoodRevenue * Percentage_Cost/100)";
    private static final String BEVERAGE_REVENUE_FORMULA = "= BeverageRevenue + (BeverageRevenue * Percentage_Cost/100)";
    private static final String TELECOM_REVENUE_FORMULA = "= TelecomRevenue + (TelecomRevenue * Percentage_Cost/100)";
    private static final String OTHER_REVENUE_FORMULA = "= OtherRevenue + (OtherRevenue * Percentage_Cost/100)";
    private static final String RATE_VALUE_FORMULA = "= RateValue + (RateValue * Percentage_Cost/100 + (Fixed_Cost/Length_Of_Stay))";


    private static final Object[][][] SCENARIO_COMMISSION = {
            // Channel Cost Configurations
            {
                    {"ConfigId", "source", "Channel", "MarketCode", "RateCode", "StartDate", "EndDate", "Type", "FixedCost", "%Cost", "createdByUserId", "creaedDateTime", "lastUpdateduserId", "lastUpdatedDTTM", "DefaultCount",},
                    {"1", "'DEFAULT'", "'DEFAULT'", "'DEFAULT'", "'DEFAULT'", "'2011-08-10'", "'2011-08-10'", "'COMMISSION'", "9.6", "14", "1", "getdate()", "1", "getdate()", "4",},
                    {"2", "'DEFAULT'", "'TestC'", "'DEFAULT'", "'DEFAULT'", "'2011-08-10'", "'2011-08-12'", "'COMMISSION'", "8.68", "13", "1", "getdate()", "1", "getdate()", "3",},
                    {"3", "'DEFAULT'", "'TestD'", "'CONS'", "'DEFAULT'", "'2011-08-10'", "'2011-08-12'", "'COMMISSION'", "7.68", "12", "1", "getdate()", "1", "getdate()", "2",},
                    {"4", "'TestS'", "'TestC'", "'COMP'", "'DEFAULT'", "'2011-08-10'", "'2011-08-12'", "'COMMISSION'", "6.68", "11", "1", "getdate()", "1", "getdate()", "1",},
                    {"5", "'TestS'", "'TestC'", "'COMP'", "'SHHQO1'", "'2011-08-10'", "'2011-08-12'", "'COMMISSION'", "5", "10", "1", "getdate()", "1", "getdate()", "0",},
            },

            // Reservation Night Data
            {
                    {"ResIdentifier", "individualStatus", "ArrivalDate", "DepartureDate", "BookingDate", "cancelationDate", "bookedAccomTypeCode", "AccomTypeId", "mktSegId", "RoomRevenue", "FoodRevenue", "BeverageRevenue", "TelecomRevenue", "OtherRevenue", "TotalRevenue", "SourceBooking", "Nationality", "RateCode", "RateValue", "RoomNumber", "bookingType", "Children", "Adults", "createDate", "confirmationNumber", "Channel", "BookingTime", "OccupancyDate", "PersistentKey", "AnalyticsBookingDt", "InvBlockCode", "marketCode",},
                    {"'123'", "'SS'", "'2011-08-10'", "'2011-08-13'", "'2011-06-01'", "''", "'SXBL'", "4", "1", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.00000", "''", "''", "'SHHQO1'", "30.00000", "127", "'IN'", "20", "40", "getdate()", "NULL", "NULL", "NULL", "'2011-08-10'", "'7264078401,2011-08-10'", "NULL", "NULL", "NULL",},
                    {"'123'", "'SS'", "'2011-08-10'", "'2011-08-13'", "'2011-06-01'", "''", "'SXBL'", "4", "2", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.00000", "'TestS'", "''", "'SHHQO2'", "30.00000", "127", "'IN'", "20", "40", "getdate()", "NULL", "'TestC'", "NULL", "'2011-08-11'", "'7264078401,2011-08-10'", "NULL", "NULL", "NULL",},
                    {"'123'", "'SS'", "'2011-08-10'", "'2011-08-13'", "'2011-06-01'", "''", "'SXBL'", "4", "1", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.00000", "''", "''", "'SHHQO1'", "30.00000", "127", "'IN'", "20", "40", "getdate()", "NULL", "'TestC'", "NULL", "'2011-08-12'", "'7264078401,2011-08-10'", "NULL", "NULL", "NULL",},
                    {"'124'", "'SS'", "'2011-08-10'", "'2011-08-11'", "'2011-06-01'", "''", "'SXBL'", "4", "2", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.00000", "'TestS'", "''", "'SHHQO1'", "30.00000", "127", "'IN'", "20", "40", "getdate()", "NULL", "'TestC'", "NULL", "'2011-08-10'", "'7264078401,2011-08-10'", "NULL", "NULL", "NULL",},
                    {"'125'", "'SS'", "'2011-08-10'", "'2011-08-11'", "'2011-06-01'", "''", "'SXBL'", "4", "3", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.00000", "'TestS'", "''", "'SHHQO1'", "30.00000", "127", "'IN'", "20", "40", "getdate()", "NULL", "'TestD'", "NULL", "'2011-08-10'", "'7264078401,2011-08-10'", "NULL", "NULL", "NULL",},
            },

            // Expected Business Insights Data
            {
                    {"reservation_identifier", "individual_status", "Occupancy_Date", "arrival_dt", "departure_dt", "Mkt_Seg_Code", "Room_Revenue", "Room_Revenue_For_RevPAR", "Food_Revenue", "Beverage_Revenue", "Telecom_Revenue", "Other_Revenue", "Total_Revenue", "Total_Channel_Cost", "Total_Acquisition_Cost", "Net_Revenue", "source_booking", "Rate_Code", "Rate_Value", "Net_Rate_Value", "channel", "Channel_Source_Booking", "Transaction_Source", "file_metadata_id", "property_id", "day_of_week", "week_of_year", "nationality", "room_number", "booking_type", "number_children", "number_adults", "confirmation_no", "booking_tm", "ConsumedCostConfig",},
                    {"123", "SS", "2011-08-10", "2011-08-10", "2011-08-13", "BART", "70.000000000", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.000000000", TOTAL_CC_FORMULA, TOTAL_AQ_CC_FORMULA, NET_REVENUE_FORMULA, "", "SHHQO1", "30.000000000", NET_RATE_VALUE_FORMULA, "NULL", "NULL", "I", "1", "5", "4", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "1",},
                    {"123", "SS", "2011-08-11", "2011-08-10", "2011-08-13", "COMP", "70.000000000", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.000000000", TOTAL_CC_FORMULA, TOTAL_AQ_CC_FORMULA, NET_REVENUE_FORMULA, "TestS", "SHHQO2", "30.000000000", NET_RATE_VALUE_FORMULA, "TestC", "TestC/TestS", "I", "1", "5", "5", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "4",},
                    {"123", "SS", "2011-08-12", "2011-08-10", "2011-08-13", "BART", "70.000000000", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.000000000", TOTAL_CC_FORMULA, TOTAL_AQ_CC_FORMULA, NET_REVENUE_FORMULA, "", "SHHQO1", "30.000000000", NET_RATE_VALUE_FORMULA, "TestC", "TestC/", "I", "1", "5", "6", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "2",},
                    {"123", "SS", "2011-08-13", "2011-08-10", "2011-08-13", "BART", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "", "SHHQO1", "NULL", "NULL", "TestC", "TestC/", "I", "1", "5", "7", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "-",},
                    {"124", "SS", "2011-08-10", "2011-08-10", "2011-08-11", "COMP", "70.000000000", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.000000000", TOTAL_CC_FORMULA, TOTAL_AQ_CC_FORMULA, NET_REVENUE_FORMULA, "TestS", "SHHQO1", "30.000000000", NET_RATE_VALUE_FORMULA, "TestC", "TestC/TestS", "I", "1", "5", "4", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "5",},
                    {"124", "SS", "2011-08-11", "2011-08-10", "2011-08-11", "COMP", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "TestS", "SHHQO1", "NULL", "NULL", "TestC", "TestC/TestS", "I", "1", "5", "5", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "5",},
                    {"125", "SS", "2011-08-10", "2011-08-10", "2011-08-11", "CONS", "70.000000000", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.000000000", TOTAL_CC_FORMULA, TOTAL_AQ_CC_FORMULA, NET_REVENUE_FORMULA, "TestS", "SHHQO1", "30.000000000", NET_RATE_VALUE_FORMULA, "TestD", "TestD/TestS", "I", "1", "5", "4", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "3",},
                    {"125", "SS", "2011-08-11", "2011-08-10", "2011-08-11", "CONS", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "TestS", "SHHQO1", "NULL", "NULL", "TestD", "TestD/TestS", "I", "1", "5", "5", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "3",},
            }

    };

    private static final Object[][][] SCENARIO_MERCHANT = {
            // Channel Cost Configurations
            {
                    {"ConfigId", "source", "Channel", "MarketCode", "RateCode", "StartDate", "EndDate", "Type", "FixedCost", "%Cost", "createdByUserId", "creaedDateTime", "lastUpdateduserId", "lastUpdatedDTTM", "DefaultCount",},
                    {"1", "'DEFAULT'", "'DEFAULT'", "'DEFAULT'", "'DEFAULT'", "'2011-08-10'", "'2011-08-10'", "'MERCHANT'", "9.6", "14", "1", "getdate()", "1", "getdate()", "4",},
                    {"2", "'DEFAULT'", "'TestC'", "'DEFAULT'", "'DEFAULT'", "'2011-08-10'", "'2011-08-12'", "'MERCHANT'", "8.68", "13", "1", "getdate()", "1", "getdate()", "3",},
                    {"3", "'DEFAULT'", "'TestD'", "'CONS'", "'DEFAULT'", "'2011-08-10'", "'2011-08-12'", "'MERCHANT'", "7.68", "12", "1", "getdate()", "1", "getdate()", "2",},
                    {"4", "'TestS'", "'TestC'", "'COMP'", "'DEFAULT'", "'2011-08-10'", "'2011-08-12'", "'MERCHANT'", "6.68", "11", "1", "getdate()", "1", "getdate()", "1",},
                    {"5", "'TestS'", "'TestC'", "'COMP'", "'SHHQO1'", "'2011-08-10'", "'2011-08-12'", "'MERCHANT'", "5", "10", "1", "getdate()", "1", "getdate()", "0",},
            },

            // Reservation Night Data
            {
                    {"ResIdentifier", "individualStatus", "ArrivalDate", "DepartureDate", "BookingDate", "cancelationDate", "bookedAccomTypeCode", "AccomTypeId", "mktSegId", "RoomRevenue", "FoodRevenue", "BeverageRevenue", "TelecomRevenue", "OtherRevenue", "TotalRevenue", "SourceBooking", "Nationality", "RateCode", "RateValue", "RoomNumber", "bookingType", "Children", "Adults", "createDate", "confirmationNumber", "Channel", "BookingTime", "OccupancyDate", "PersistentKey", "AnalyticsBookingDt", "InvBlockCode", "marketCode",},
                    {"'123'", "'SS'", "'2011-08-10'", "'2011-08-13'", "'2011-06-01'", "''", "'SXBL'", "4", "1", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.00000", "''", "''", "'SHHQO1'", "30.00000", "127", "'IN'", "20", "40", "getdate()", "NULL", "NULL", "NULL", "'2011-08-10'", "'7264078401,2011-08-10'", "NULL", "NULL", "NULL",},
                    {"'123'", "'SS'", "'2011-08-10'", "'2011-08-13'", "'2011-06-01'", "''", "'SXBL'", "4", "2", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.00000", "'TestS'", "''", "'SHHQO2'", "30.00000", "127", "'IN'", "20", "40", "getdate()", "NULL", "'TestC'", "NULL", "'2011-08-11'", "'7264078401,2011-08-10'", "NULL", "NULL", "NULL",},
                    {"'123'", "'SS'", "'2011-08-10'", "'2011-08-13'", "'2011-06-01'", "''", "'SXBL'", "4", "1", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.00000", "''", "''", "'SHHQO1'", "30.00000", "127", "'IN'", "20", "40", "getdate()", "NULL", "'TestC'", "NULL", "'2011-08-12'", "'7264078401,2011-08-10'", "NULL", "NULL", "NULL",},
                    {"'124'", "'SS'", "'2011-08-10'", "'2011-08-11'", "'2011-06-01'", "''", "'SXBL'", "4", "2", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.00000", "'TestS'", "''", "'SHHQO1'", "30.00000", "127", "'IN'", "20", "40", "getdate()", "NULL", "'TestC'", "NULL", "'2011-08-10'", "'7264078401,2011-08-10'", "NULL", "NULL", "NULL",},
                    {"'125'", "'SS'", "'2011-08-10'", "'2011-08-11'", "'2011-06-01'", "''", "'SXBL'", "4", "3", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.00000", "'TestS'", "''", "'SHHQO1'", "30.00000", "127", "'IN'", "20", "40", "getdate()", "NULL", "'TestD'", "NULL", "'2011-08-10'", "'7264078401,2011-08-10'", "NULL", "NULL", "NULL",},
            },

            // Expected Business Insights Data
            {
                    {"reservation_identifier", "individual_status", "Occupancy_Date", "arrival_dt", "departure_dt", "Mkt_Seg_Code", "Room_Revenue", "Room_Revenue_For_RevPAR", "Food_Revenue", "Beverage_Revenue", "Telecom_Revenue", "Other_Revenue", "Total_Revenue", "Total_Channel_Cost", "Total_Acquisition_Cost", "Net_Revenue", "source_booking", "Rate_Code", "Rate_Value", "Net_Rate_Value", "channel", "Channel_Source_Booking", "Transaction_Source", "file_metadata_id", "property_id", "day_of_week", "week_of_year", "nationality", "room_number", "booking_type", "number_children", "number_adults", "confirmation_no", "booking_tm", "ConsumedCostConfig",},
                    {"123", "SS", "2011-08-10", "2011-08-10", "2011-08-13", "BART", ROOM_REVENUE_FORMULA, "70.00000", FOOD_REVENUE_FORMULA, BEVERAGE_REVENUE_FORMULA, TELECOM_REVENUE_FORMULA, OTHER_REVENUE_FORMULA, TOTAL_REVENUE_FORMULA, TOTAL_CC_FORMULA, TOTAL_AQ_CC_FORMULA, "70.000000000", "", "SHHQO1", RATE_VALUE_FORMULA, "30.000000000", "NULL", "NULL", "I", "1", "5", "4", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "1",},
                    {"123", "SS", "2011-08-11", "2011-08-10", "2011-08-13", "COMP", ROOM_REVENUE_FORMULA, "70.00000", FOOD_REVENUE_FORMULA, BEVERAGE_REVENUE_FORMULA, TELECOM_REVENUE_FORMULA, OTHER_REVENUE_FORMULA, TOTAL_REVENUE_FORMULA, TOTAL_CC_FORMULA, TOTAL_AQ_CC_FORMULA, "70.000000000", "TestS", "SHHQO2", RATE_VALUE_FORMULA, "30.000000000", "TestC", "TestC/TestS", "I", "1", "5", "5", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "4",},
                    {"123", "SS", "2011-08-12", "2011-08-10", "2011-08-13", "BART", ROOM_REVENUE_FORMULA, "70.00000", FOOD_REVENUE_FORMULA, BEVERAGE_REVENUE_FORMULA, TELECOM_REVENUE_FORMULA, OTHER_REVENUE_FORMULA, TOTAL_REVENUE_FORMULA, TOTAL_CC_FORMULA, TOTAL_AQ_CC_FORMULA, "70.000000000", "", "SHHQO1", RATE_VALUE_FORMULA, "30.000000000", "TestC", "TestC/", "I", "1", "5", "6", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "2",},
                    {"123", "SS", "2011-08-13", "2011-08-10", "2011-08-13", "BART", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "", "SHHQO1", "NULL", "NULL", "TestC", "TestC/", "I", "1", "5", "7", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "-",},
                    {"124", "SS", "2011-08-10", "2011-08-10", "2011-08-11", "COMP", ROOM_REVENUE_FORMULA, "70.00000", FOOD_REVENUE_FORMULA, BEVERAGE_REVENUE_FORMULA, TELECOM_REVENUE_FORMULA, OTHER_REVENUE_FORMULA, TOTAL_REVENUE_FORMULA, TOTAL_CC_FORMULA, TOTAL_AQ_CC_FORMULA, "70.000000000", "TestS", "SHHQO1", RATE_VALUE_FORMULA, "30.000000000", "TestC", "TestC/TestS", "I", "1", "5", "4", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "5",},
                    {"124", "SS", "2011-08-11", "2011-08-10", "2011-08-11", "COMP", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "TestS", "SHHQO1", "NULL", "NULL", "TestC", "TestC/TestS", "I", "1", "5", "5", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "5",},
                    {"125", "SS", "2011-08-10", "2011-08-10", "2011-08-11", "CONS", ROOM_REVENUE_FORMULA, "70.00000", FOOD_REVENUE_FORMULA, BEVERAGE_REVENUE_FORMULA, TELECOM_REVENUE_FORMULA, OTHER_REVENUE_FORMULA, TOTAL_REVENUE_FORMULA, TOTAL_CC_FORMULA, TOTAL_AQ_CC_FORMULA, "70.000000000", "TestS", "SHHQO1", RATE_VALUE_FORMULA, "30.000000000", "TestD", "TestD/TestS", "I", "1", "5", "4", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "3",},
                    {"125", "SS", "2011-08-11", "2011-08-10", "2011-08-11", "CONS", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "TestS", "SHHQO1", "NULL", "NULL", "TestD", "TestD/TestS", "I", "1", "5", "5", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "3",},
            }
    };

    private static final Object[][][] SCENARIO_MERCHANT_COMMISSION_HYBRID = {
            // Channel Cost Configurations
            {
                    {"ConfigId", "source", "Channel", "MarketCode", "RateCode", "StartDate", "EndDate", "Type", "FixedCost", "%Cost", "createdByUserId", "creaedDateTime", "lastUpdateduserId", "lastUpdatedDTTM", "DefaultCount",},
                    {"1", "'DEFAULT'", "'DEFAULT'", "'DEFAULT'", "'DEFAULT'", "'2011-08-10'", "'2011-08-10'", "'MERCHANT'", "9.6", "14", "1", "getdate()", "1", "getdate()", "4",},
                    {"2", "'DEFAULT'", "'TestC'", "'DEFAULT'", "'DEFAULT'", "'2011-08-10'", "'2011-08-12'", "'MERCHANT'", "8.68", "13", "1", "getdate()", "1", "getdate()", "3",},
                    {"3", "'DEFAULT'", "'TestD'", "'CONS'", "'DEFAULT'", "'2011-08-10'", "'2011-08-12'", "'COMMISSION'", "7.68", "12", "1", "getdate()", "1", "getdate()", "2",},
                    {"4", "'TestS'", "'TestC'", "'COMP'", "'DEFAULT'", "'2011-08-10'", "'2011-08-12'", "'MERCHANT'", "6.68", "11", "1", "getdate()", "1", "getdate()", "1",},
                    {"5", "'TestS'", "'TestC'", "'COMP'", "'SHHQO1'", "'2011-08-10'", "'2011-08-12'", "'COMMISSION'", "5", "10", "1", "getdate()", "1", "getdate()", "0",},
            },

            // Reservation Night Data
            {
                    {"ResIdentifier", "individualStatus", "ArrivalDate", "DepartureDate", "BookingDate", "cancelationDate", "bookedAccomTypeCode", "AccomTypeId", "mktSegId", "RoomRevenue", "FoodRevenue", "BeverageRevenue", "TelecomRevenue", "OtherRevenue", "TotalRevenue", "SourceBooking", "Nationality", "RateCode", "RateValue", "RoomNumber", "bookingType", "Children", "Adults", "createDate", "confirmationNumber", "Channel", "BookingTime", "OccupancyDate", "PersistentKey", "AnalyticsBookingDt", "InvBlockCode", "marketCode",},
                    {"'123'", "'SS'", "'2011-08-10'", "'2011-08-13'", "'2011-06-01'", "''", "'SXBL'", "4", "1", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.00000", "''", "''", "'SHHQO1'", "30.00000", "127", "'IN'", "20", "40", "getdate()", "NULL", "NULL", "NULL", "'2011-08-10'", "'7264078401,2011-08-10'", "NULL", "NULL", "NULL",},
                    {"'123'", "'SS'", "'2011-08-10'", "'2011-08-13'", "'2011-06-01'", "''", "'SXBL'", "4", "2", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.00000", "'TestS'", "''", "'SHHQO2'", "30.00000", "127", "'IN'", "20", "40", "getdate()", "NULL", "'TestC'", "NULL", "'2011-08-11'", "'7264078401,2011-08-10'", "NULL", "NULL", "NULL",},
                    {"'123'", "'SS'", "'2011-08-10'", "'2011-08-13'", "'2011-06-01'", "''", "'SXBL'", "4", "1", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.00000", "''", "''", "'SHHQO1'", "30.00000", "127", "'IN'", "20", "40", "getdate()", "NULL", "'TestC'", "NULL", "'2011-08-12'", "'7264078401,2011-08-10'", "NULL", "NULL", "NULL",},
                    {"'124'", "'SS'", "'2011-08-10'", "'2011-08-11'", "'2011-06-01'", "''", "'SXBL'", "4", "2", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.00000", "'TestS'", "''", "'SHHQO1'", "30.00000", "127", "'IN'", "20", "40", "getdate()", "NULL", "'TestC'", "NULL", "'2011-08-10'", "'7264078401,2011-08-10'", "NULL", "NULL", "NULL",},
                    {"'125'", "'SS'", "'2011-08-10'", "'2011-08-11'", "'2011-06-01'", "''", "'SXBL'", "4", "3", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.00000", "'TestS'", "''", "'SHHQO1'", "30.00000", "127", "'IN'", "20", "40", "getdate()", "NULL", "'TestD'", "NULL", "'2011-08-10'", "'7264078401,2011-08-10'", "NULL", "NULL", "NULL",},
            },

            // Expected Business Insights Data
            {
                    {"reservation_identifier", "individual_status", "Occupancy_Date", "arrival_dt", "departure_dt", "Mkt_Seg_Code", "Room_Revenue", "Room_Revenue_For_RevPAR", "Food_Revenue", "Beverage_Revenue", "Telecom_Revenue", "Other_Revenue", "Total_Revenue", "Total_Channel_Cost", "Total_Acquisition_Cost", "Net_Revenue", "source_booking", "Rate_Code", "Rate_Value", "Net_Rate_Value", "channel", "Channel_Source_Booking", "Transaction_Source", "file_metadata_id", "property_id", "day_of_week", "week_of_year", "nationality", "room_number", "booking_type", "number_children", "number_adults", "confirmation_no", "booking_tm", "ConsumedCostConfig",},
                    {"123", "SS", "2011-08-10", "2011-08-10", "2011-08-13", "BART", ROOM_REVENUE_FORMULA, "70.00000", FOOD_REVENUE_FORMULA, BEVERAGE_REVENUE_FORMULA, TELECOM_REVENUE_FORMULA, OTHER_REVENUE_FORMULA, TOTAL_REVENUE_FORMULA, TOTAL_CC_FORMULA, TOTAL_AQ_CC_FORMULA, "70.000000000", "", "SHHQO1", RATE_VALUE_FORMULA, "30.000000000", "NULL", "NULL", "I", "1", "5", "4", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "1",},
                    {"123", "SS", "2011-08-11", "2011-08-10", "2011-08-13", "COMP", ROOM_REVENUE_FORMULA, "70.00000", FOOD_REVENUE_FORMULA, BEVERAGE_REVENUE_FORMULA, TELECOM_REVENUE_FORMULA, OTHER_REVENUE_FORMULA, TOTAL_REVENUE_FORMULA, TOTAL_CC_FORMULA, TOTAL_AQ_CC_FORMULA, "70.000000000", "TestS", "SHHQO2", RATE_VALUE_FORMULA, "30.000000000", "TestC", "TestC/TestS", "I", "1", "5", "5", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "4",},
                    {"123", "SS", "2011-08-12", "2011-08-10", "2011-08-13", "BART", ROOM_REVENUE_FORMULA, "70.00000", FOOD_REVENUE_FORMULA, BEVERAGE_REVENUE_FORMULA, TELECOM_REVENUE_FORMULA, OTHER_REVENUE_FORMULA, TOTAL_REVENUE_FORMULA, TOTAL_CC_FORMULA, TOTAL_AQ_CC_FORMULA, "70.000000000", "", "SHHQO1", RATE_VALUE_FORMULA, "30.000000000", "TestC", "TestC/", "I", "1", "5", "6", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "2",},
                    {"123", "SS", "2011-08-13", "2011-08-10", "2011-08-13", "BART", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "", "SHHQO1", "NULL", "NULL", "TestC", "TestC/", "I", "1", "5", "7", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "-",},
                    {"124", "SS", "2011-08-10", "2011-08-10", "2011-08-11", "COMP", "70.000000000", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.000000000", TOTAL_CC_FORMULA, TOTAL_AQ_CC_FORMULA, NET_REVENUE_FORMULA, "TestS", "SHHQO1", "30.000000000", NET_RATE_VALUE_FORMULA, "TestC", "TestC/TestS", "I", "1", "5", "4", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "5",},
                    {"124", "SS", "2011-08-11", "2011-08-10", "2011-08-11", "COMP", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "TestS", "SHHQO1", "NULL", "NULL", "TestC", "TestC/TestS", "I", "1", "5", "5", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "5",},
                    {"125", "SS", "2011-08-10", "2011-08-10", "2011-08-11", "CONS", "70.000000000", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.000000000", TOTAL_CC_FORMULA, TOTAL_AQ_CC_FORMULA, NET_REVENUE_FORMULA, "TestS", "SHHQO1", "30.000000000", NET_RATE_VALUE_FORMULA, "TestD", "TestD/TestS", "I", "1", "5", "4", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "3",},
                    {"125", "SS", "2011-08-11", "2011-08-10", "2011-08-11", "CONS", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "TestS", "SHHQO1", "NULL", "NULL", "TestD", "TestD/TestS", "I", "1", "5", "5", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "3",},
            }
    };

    private static final Object[][][] SCENARIO_COMMISSION_AMS = {

            // Channel Cost Configurations
            {
                    {"ConfigId", "source", "Channel", "MarketCode", "RateCode", "StartDate", "EndDate", "Type", "FixedCost", "%Cost", "createdByUserId", "creaedDateTime", "lastUpdateduserId", "lastUpdatedDTTM", "DefaultCount",},
                    {"1", "'DEFAULT'", "'DEFAULT'", "'DEFAULT'", "'DEFAULT'", "'2011-08-10'", "'2011-08-13'", "'COMMISSION'", "9.6", "12", "1", "getdate()", "1", "getdate()", "4",},
                    {"2", "'DEFAULT'", "'DEFAULT'", "'COMP'", "'DEFAULT'", "'2011-08-10'", "'2011-08-13'", "'COMMISSION'", "6.68", "18", "1", "getdate()", "1", "getdate()", "3",},
            },

            // Reservation Night Data
            {
                    {"ResIdentifier", "individualStatus", "ArrivalDate", "DepartureDate", "BookingDate", "cancelationDate", "bookedAccomTypeCode", "AccomTypeId", "mktSegId", "RoomRevenue", "FoodRevenue", "BeverageRevenue", "TelecomRevenue", "OtherRevenue", "TotalRevenue", "SourceBooking", "Nationality", "RateCode", "RateValue", "RoomNumber", "bookingType", "Children", "Adults", "createDate", "confirmationNumber", "Channel", "BookingTime", "OccupancyDate", "PersistentKey", "AnalyticsBookingDt", "InvBlockCode", "marketCode",},
                    {"'123'", "'SS'", "'2011-08-10'", "'2011-08-13'", "'2011-06-01'", "''", "'SXBL'", "4", "2", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.00000", "''", "''", "'SHHQO1'", "30.00000", "127", "'IN'", "20", "40", "getdate()", "NULL", "NULL", "NULL", "'2011-08-10'", "'7264078401,2011-08-10'", "NULL", "NULL", "NULL",},
                    {"'123'", "'SS'", "'2011-08-10'", "'2011-08-13'", "'2011-06-01'", "''", "'SXBL'", "4", "1", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.00000", "''", "''", "'SHHQO1'", "30.00000", "127", "'IN'", "20", "40", "getdate()", "NULL", "NULL", "NULL", "'2011-08-11'", "'7264078401,2011-08-10'", "NULL", "NULL", "NULL",},
                    {"'123'", "'SS'", "'2011-08-10'", "'2011-08-13'", "'2011-06-01'", "''", "'SXBL'", "4", "1", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.00000", "''", "''", "'SHHQO1'", "30.00000", "127", "'IN'", "20", "40", "getdate()", "NULL", "NULL", "NULL", "'2011-08-12'", "'7264078401,2011-08-10'", "NULL", "NULL", "NULL",},
            },

            // Expected Business Insights Data
            {
                    {"reservation_identifier", "individual_status", "Occupancy_Date", "arrival_dt", "departure_dt", "Mkt_Seg_Code", "Room_Revenue", "Room_Revenue_For_RevPAR", "Food_Revenue", "Beverage_Revenue", "Telecom_Revenue", "Other_Revenue", "Total_Revenue", "Total_Channel_Cost", "Total_Acquisition_Cost", "Net_Revenue", "source_booking", "Rate_Code", "Rate_Value", "Net_Rate_Value", "channel", "Channel_Source_Booking", "Transaction_Source", "file_metadata_id", "property_id", "day_of_week", "week_of_year", "nationality", "room_number", "booking_type", "number_children", "number_adults", "confirmation_no", "booking_tm", "ConsumedCostConfig",},
                    {"123", "SS", "2011-08-10", "2011-08-10", "2011-08-13", "COMP_USB", "70.000000000", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.000000000", TOTAL_CC_FORMULA, TOTAL_AQ_CC_FORMULA, NET_REVENUE_FORMULA, "", "SHHQO1", "30.000000000", NET_RATE_VALUE_FORMULA, "NULL", "NULL", "I", "1", "5", "4", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "2",},
                    {"123", "SS", "2011-08-11", "2011-08-10", "2011-08-13", "BART", "70.000000000", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.000000000", TOTAL_CC_FORMULA, TOTAL_AQ_CC_FORMULA, NET_REVENUE_FORMULA, "", "SHHQO1", "30.000000000", NET_RATE_VALUE_FORMULA, "NULL", "NULL", "I", "1", "5", "5", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "1",},
                    {"123", "SS", "2011-08-12", "2011-08-10", "2011-08-13", "BART", "70.000000000", "70.00000", "50.00000", "65.00000", "21.00000", "0.00000", "90.000000000", TOTAL_CC_FORMULA, TOTAL_AQ_CC_FORMULA, NET_REVENUE_FORMULA, "", "SHHQO1", "30.000000000", NET_RATE_VALUE_FORMULA, "NULL", "NULL", "I", "1", "5", "6", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "1",},
                    {"123", "SS", "2011-08-13", "2011-08-10", "2011-08-13", "BART", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "", "SHHQO1", "NULL", "NULL", "NULL", "NULL", "I", "1", "5", "7", "33", "", "127", "IN", "20", "40", "NULL", "NULL", "1",},
            }
    };


    private CrudService tenantCrudService;
    private SimplifiedChannelCostService theService;
    @Mock
    private PacmanConfigParamsService pacmanConfigParamsService;

    @BeforeEach
    public void setUp() {
        theService = new SimplifiedChannelCostService();
        tenantCrudService = tenantCrudService();
        inject(theService, "tenantCrudService", tenantCrudService);
        inject(theService, "pacmanConfigParamsService", pacmanConfigParamsService);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_CHANNEL_OPTIMIZATION)).thenReturn(false);
    }


    @Test
    public void shouldFetchAllConfigurations() {
        createAndPersistConfigEntity();

        List<SimplifiedChannelCostConfigDTO> channelCostConfigDTOS = theService.fetchAllConfigurations();

        assertThat(channelCostConfigDTOS, hasSize(equalTo(1)));
        Map<String, Object> expectedDTOFields = MapBuilder.with("source", GDS).and("channel", TRUNCATED_EXPEDIA).and("marketCode", MARKET_CODE_1)
                .and("rateCode", RATE_CODE_1).and("startDate", START_DATE).and("endDate", END_DATE)
                .and("fixedCost", FIXED_COST).and("percentageCost", PERCENTAGE_COST).and("type", COMMISSION.name()).get();
        assertConfigDTO(channelCostConfigDTOS.get(0), expectedDTOFields);
    }

    @Test
    public void shouldSaveGivenConfigurations() {
        SimplifiedChannelCostConfigDTO configDTO = createConfigDTO();

        theService.saveConfigurations(Collections.singletonList(configDTO));

        List<SimplifiedChannelCostConfiguration> savedConfigs = tenantCrudService.findAll(SimplifiedChannelCostConfiguration.class);
        assertThat(savedConfigs, hasSize(equalTo(1)));
        Map<String, Object> expectedConfigsFields = MapBuilder.with("source", GDS).and("channel", TRUNCATED_EXPEDIA).and("marketCode", MARKET_CODE_1)
                .and("rateCode", RATE_CODE_1).and("startDate", START_DATE).and("endDate", END_DATE)
                .and("fixedCost", FIXED_COST).and("percentageCost", PERCENTAGE_COST).and("type", COMMISSION).get();
        assertConfigDTO(savedConfigs.get(0), expectedConfigsFields);
    }

    @Test
    public void shouldDeleteAllConfigurations() {
        createAndPersistConfigEntity();

        theService.deleteAllConfigurations();

        List<SimplifiedChannelCostConfiguration> configs = tenantCrudService.findAll(SimplifiedChannelCostConfiguration.class);
        assertThat(configs, is(empty()));
    }

    @Test
    public void test_scenario_merchant() throws Exception {
        setupRunAndAssert(SCENARIO_MERCHANT);
    }

    @Test
    public void test_scenario_commission() throws Exception {
        setupRunAndAssert(SCENARIO_COMMISSION);
    }

    @Test
    public void test_scenario_merchant_commission_hybrid() throws Exception {
        setupRunAndAssert(SCENARIO_MERCHANT_COMMISSION_HYBRID);
    }

    @Test
    public void test_scenario_ams() throws Exception {
        tenantCrudService.executeUpdateByNativeQuery("update Mkt_Seg set Mkt_Seg_Code='COMP_USB' where Mkt_Seg_Code='COMP'");

        setupRunAndAssert(SCENARIO_COMMISSION_AMS);
    }

    private void setupRunAndAssert(Object[][][] testScenario) throws ParseException, ScriptException {
        Object[][] configData = testScenario[0];
        Object[][] reservationData = testScenario[1];
        Object[][] biViewData = testScenario[2];

        setupChannelCostConfigurationsData(configData);
        setupReservationNightData(reservationData);

        List<String> resIdentifiers = Arrays.stream(ArrayUtils.subarray(reservationData, 1, reservationData.length))
                .map(resRow -> ((Object[]) resRow)[0].toString().replace("'", ""))
                .distinct().collect(Collectors.toList());

        List<Object[]> insightsData = selectDataFromBusinessInSghtViewForGivenReservationIdentifier(resIdentifiers);

        assertBIData(configData, reservationData, biViewData, insightsData);
    }

    private void assertBIData(Object[][] configData, Object[][] reservationData, Object[][] biViewData, List<Object[]> insightsData) throws ParseException, ScriptException {
        List<Object> configColumnNames = Arrays.asList(configData[0]);
        List<Object> viewColumnNames = Arrays.asList(biViewData[0]);

        for (int rowNum = 0; rowNum < insightsData.size(); rowNum++) {
            Object[] actualDataRow = insightsData.get(rowNum);
            Object[] expectedDataRow = biViewData[rowNum + 1];

            for (int j = 0; j < actualDataRow.length; j++) {
                String actualVal = "NULL";
                if (actualDataRow[j] != null) {
                    actualVal = actualDataRow[j].toString();
                }
                String expectedVal = expectedDataRow[j].toString();

                // If expected-value starts with '=' then it's a formula expression, so evaluate it first
                if (expectedVal.startsWith("=")) {
                    expectedVal = evaluateCellExpression(configData, configColumnNames, reservationData, viewColumnNames, expectedDataRow, actualVal, expectedVal);
                }

                assertEquals(expectedVal, actualVal, "Mismatch found for: Row- " + (rowNum + 1) + " and Column- " + viewColumnNames.get(j));
            }
        }
    }

    private String evaluateCellExpression(Object[][] configData, List<Object> configColumnNames, Object[][] reservationData, List<Object> viewColumnNames, Object[] expectedDataRow, String actualVal, String formulaExpression) throws ParseException, ScriptException {
        List<Object> resColumnNames = Arrays.asList(reservationData[0]);

        // Formula contains variables like RoomRevenue, RateValue, ... which must be substituted with values from corresponding reservation record. So pull it first.
        Object[] corrReservation = findReservationForExpectedBIRow(reservationData, viewColumnNames, expectedDataRow, resColumnNames);

        formulaExpression = substituteReservationVariablesInFormulaWithValues(formulaExpression, resColumnNames, corrReservation);

        formulaExpression = substituteOtherVariablesInFormulaWithValues(configData, configColumnNames, viewColumnNames, expectedDataRow, formulaExpression);

        // Evaluate formula expression
        String expressionValue = EXPRESSION_EVALUATOR.eval(formulaExpression).toString();

        // Adjust scale
        expressionValue = new BigDecimal(expressionValue).setScale(new BigDecimal(actualVal).scale(), ROUND_HALF_UP).toString();

        return expressionValue;
    }

    private String substituteOtherVariablesInFormulaWithValues(Object[][] configData, List<Object> configColumnNames, List<Object> viewColumnNames, Object[] expectedDataRow, String formulaExpression) throws ParseException {
        // Find expected config to be applied
        String consumedCostConfigId = expectedDataRow[viewColumnNames.indexOf("ConsumedCostConfig")].toString();
        Object[] consumedCostConfig = Arrays.stream(configData)
                .filter(config -> config[configColumnNames.indexOf("ConfigId")].toString().equals(consumedCostConfigId))
                .findFirst().orElse(null);
        assert Objects.nonNull(consumedCostConfig);

        // Find LOS from
        String los = getDiff(expectedDataRow[viewColumnNames.indexOf("arrival_dt")].toString(), expectedDataRow[viewColumnNames.indexOf("departure_dt")].toString()).toString();

        // Substitute values
        formulaExpression = formulaExpression.replace("Percentage_Cost", consumedCostConfig[configColumnNames.indexOf("%Cost")].toString());
        formulaExpression = formulaExpression.replace("Fixed_Cost", consumedCostConfig[configColumnNames.indexOf("FixedCost")].toString());
        formulaExpression = formulaExpression.replace("Length_Of_Stay", los);
        formulaExpression = formulaExpression.replace("=", "");
        return formulaExpression;
    }

    private String substituteReservationVariablesInFormulaWithValues(String formulaExpression, List<Object> resColumnNames, Object[] corrReservation) {
        for (int i = 0; i < resColumnNames.size(); i++) {
            String resColumn = resColumnNames.get(i).toString();
            if (formulaExpression.contains(resColumn)) {
                formulaExpression = formulaExpression.replace(resColumn, corrReservation[i].toString());
            }
        }
        return formulaExpression;
    }

    private Object[] findReservationForExpectedBIRow(Object[][] reservationData, List<Object> viewColumnNames, Object[] expectedDataRow, List<Object> resColumnNames) {
        Object[] corrReservation = null;
        for (Object[] res : reservationData) {
            if (res[resColumnNames.indexOf("ResIdentifier")].toString().replace("'", "").equals(expectedDataRow[viewColumnNames.indexOf("reservation_identifier")])
                    && res[resColumnNames.indexOf("OccupancyDate")].toString().replace("'", "").equals(expectedDataRow[viewColumnNames.indexOf("Occupancy_Date")])) {
                corrReservation = res;
                break;
            }
        }
        assert Objects.nonNull(corrReservation);
        return corrReservation;
    }

    private Long getDiff(String arrivalDate, String departureDate) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date1 = sdf.parse(arrivalDate.replaceAll("'", ""));
        Date date2 = sdf.parse(departureDate.replaceAll("'", ""));
        return DateUtil.getDateDiffDays(date2.getTime(), date1.getTime());
    }

    private List<Object[]> selectDataFromBusinessInSghtViewForGivenReservationIdentifier(List<String> reservationIdentifiers) {
        return tenantCrudService().findByNativeQuery("select reservation_identifier,  individual_status,  convert(date, Occupancy_Date) Occupancy_Date,  arrival_dt,  departure_dt,  Mkt_Seg_Code,  " +
                "Room_Revenue,  Room_Revenue_For_RevPAR,  cast(Food_Revenue as numeric(19, 5)) Food_Revenue,  cast(Beverage_Revenue as numeric(19, 5)) Beverage_Revenue,  " +
                "cast(Telecom_Revenue as numeric(19, 5)) Telecom_Revenue,  cast(Other_Revenue as numeric(19, 5)) Other_Revenue,  Total_Revenue,  Total_Channel_Cost,  Total_Acquisition_Cost,  Net_Revenue,  " +
                "source_booking,  Rate_Code, Rate_Value,  Net_Rate_Value,  channel,  Channel_Source_Booking, Transaction_Source,  " +
                "file_metadata_id, property_id, day_of_week, week_of_year, nationality, room_number, booking_type, number_children, number_adults, confirmation_no, booking_tm " +
                "from VW_BUSINESS_INSIGHTS_TRANSACTIONS_SIMPLIFIED_CC where reservation_identifier in (:resIds) order by reservation_identifier, Occupancy_Date", QueryParameter.with("resIds", reservationIdentifiers).parameters());
    }

    private void setupChannelCostConfigurationsData(Object[][] scenario) {
        StringBuilder insertQuery2 = new StringBuilder();
        for (int i = 1; i < scenario.length; i++) {
            insertQuery2.append("INSERT INTO [dbo].[Channel_Cost_Simplified]([Source],[Channel]," +
                    "[Market_Code],[Rate_Code],[Start_Date],[End_Date],[Type],[Fixed_Cost],[Percentage_Cost],[Created_By_User_ID]," +
                    "[Created_DTTM],[Last_Updated_By_User_ID],[Last_Updated_DTTM],[Default_Cnt]) VALUES (");
            for (int j = 1; j < scenario[i].length; j++) {
                insertQuery2.append(scenario[i][j]).append(",");
            }
            insertQuery2 = insertQuery2.replace(insertQuery2.length() - 1, insertQuery2.length(), "");
            insertQuery2.append(")");
        }
        tenantCrudService().executeUpdateByNativeQuery(insertQuery2.toString());
    }

    private void setupReservationNightData(Object[][] reservationData) {
        StringBuilder insertQuery1 = new StringBuilder();
        for (int i = 1; i < reservationData.length; i++) {
            insertQuery1.append("INSERT [dbo].[Reservation_Night] ( [File_Metadata_ID], [Property_ID], " +
                    "[Reservation_Identifier], [Individual_Status], [Arrival_DT], [Departure_DT], [Booking_DT], " +
                    "[Cancellation_DT], [Booked_Accom_Type_Code], [Accom_Type_ID], [Mkt_Seg_ID], [Room_Revenue], " +
                    "[Food_Revenue], [Beverage_Revenue], [Telecom_Revenue], [Other_Revenue], " +
                    "[Total_Revenue], [Source_Booking], [Nationality], [Rate_Code], [Rate_Value], [Room_Number], " +
                    "[Booking_type], [Number_Children], [Number_Adults], [CreateDate_DTTM], [Confirmation_No], " +
                    "[Channel], [Booking_TM], [Occupancy_DT], [Persistent_Key], [Analytics_Booking_Dt], [Inv_Block_Code], " +
                    "[Market_Code]) VALUES ( 1, 5");
            for (int j = 0; j < reservationData[i].length; j++) {
                insertQuery1.append(",").append(reservationData[i][j]);
            }
            insertQuery1.append(");");
        }
        tenantCrudService().executeUpdateByNativeQuery(insertQuery1.toString());
    }

    private void assertConfigDTO(Object actualDTO, Map<String, Object> expectedDTOFields) {
        expectedDTOFields.forEach((propertyName, value) -> {
            final Matcher<?> valueMatcher;
            if (value instanceof BigDecimal) {
                valueMatcher = closeTo((BigDecimal) value, ZERO);
            } else if (value instanceof Date) {
                valueMatcher = DateMatchers.within(0, ChronoUnit.SECONDS, (Date) value);
            } else {
                valueMatcher = is(value);
            }
            assertThat(actualDTO, hasProperty(propertyName, valueMatcher));
        });
    }

    private SimplifiedChannelCostConfigDTO createConfigDTO() {
        SimplifiedChannelCostConfigDTO dto = new SimplifiedChannelCostConfigDTO();
        dto.setSource(GDS);
        dto.setChannel(EXPEDIA);
        dto.setMarketCode(MARKET_CODE_1);
        dto.setRateCode(RATE_CODE_1);
        dto.setStartDate(START_DATE);
        dto.setEndDate(END_DATE);
        dto.setFixedCost(FIXED_COST);
        dto.setPercentageCost(PERCENTAGE_COST);
        dto.setType(COMMISSION.name());
        dto.computeDefaultCount();
        return dto;
    }

    private void createAndPersistConfigEntity() {
        SimplifiedChannelCostConfiguration config = new SimplifiedChannelCostConfiguration();
        config.setSource(GDS);
        config.setChannel(EXPEDIA);
        config.setMarketCode(MARKET_CODE_1);
        config.setRateCode(RATE_CODE_1);
        config.setStartDate(START_DATE);
        config.setEndDate(END_DATE);
        config.setFixedCost(FIXED_COST);
        config.setPercentageCost(PERCENTAGE_COST);
        config.setType(COMMISSION);
        config.setDefaultCount(0);
        tenantCrudService.save(config);
    }
}