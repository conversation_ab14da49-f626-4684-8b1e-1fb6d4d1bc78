package com.ideas.tetris.pacman.services.property.dataextraction;

import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.client.service.ClientService;
import com.ideas.tetris.pacman.services.crudservice.GlobalCrudServiceBean;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.email.EmailService;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameter;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.util.jdbc.JpaJdbcUtil;
import com.ideas.tetris.platform.common.util.zip.ZipDirectory;
import com.ideas.tetris.platform.common.util.zip.ZipDirectoryService;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.Client;
import com.ideas.tetris.platform.services.daoandentities.entity.DBLoc;
import com.ideas.tetris.platform.services.daoandentities.entity.Property;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Path;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class DatabaseExtractionServiceMockTest {
    private static final String EMAIL_ADDRESS = "<EMAIL>";
    private static final String FOLDER_BACKUP_DB = "/extractDb";
    private static final String DB_NAME_GLOBAL = "GlobalDb";
    private static final String DB_NAME_TENANT = "TenantDb";

    @TempDir
    Path LINUX_TEMP_DIR_PATH;

    private File backupDatabaseFolder = null;

    @InjectMocks
    @Spy
    private DatabaseExtractionService dbExtractionService;

    @Mock
    @GlobalCrudServiceBean.Qualifier
    private CrudService globalCrudService;
    @Mock
    @TenantCrudServiceBean.Qualifier
    private CrudService tenantCrudService;
    @Mock
    private EmailService mockEmailService;
    @Mock
    private ZipDirectoryService zipService;

    // Bowels of the class
    @Mock
    JpaJdbcUtil jpaJdbcUtil;
    @Mock
    EntityManager entityManager;
    @Mock
    Query query;
    @Mock
    Connection connection;
    @Mock
    PreparedStatement statement;
    @Mock
    File zippedFile;
    @Mock
    Process mockedRuntimeProcess;
    @Mock(answer = Answers.RETURNS_DEEP_STUBS)
    private PacmanConfigParamsService configParamsService;

    @Mock
    private ClientService clientService;

    @Mock
    private PropertyService propertyService;

    @BeforeEach
    public void setUp() throws Exception {
       backupDatabaseFolder = resource();

        WorkContextType wc = new WorkContextType();
        PacmanWorkContextHelper.setWorkContext(wc);
        PacmanWorkContextHelper.setClientCode("BSTN");
        PacmanWorkContextHelper.setPropertyCode("H1");

        Mockito.doReturn(jpaJdbcUtil).when(dbExtractionService).getJpaJdbcUtil();
        Mockito.doReturn(backupDatabaseFolder.getAbsolutePath()).when(dbExtractionService).getTempZipDirectory();

        Mockito.doReturn(globalCrudService).when(dbExtractionService).getGlobalCrudService();
        Mockito.doReturn(tenantCrudService).when(dbExtractionService).getTenantCrudService();

        when(globalCrudService.getEntityManager()).thenReturn(entityManager);
        when(tenantCrudService.getEntityManager()).thenReturn(entityManager);
        when(entityManager.createNativeQuery(DatabaseExtractionService.SQL_CHECK_DB)).thenReturn(query);

        when(connection.prepareStatement(anyString())).thenReturn(statement);
        when(statement.execute()).thenReturn(true);
        Mockito.doNothing().when(statement).close();
        Mockito.doNothing().when(connection).close();
    }

    private File resource() {
        try {
            Class<? extends DatabaseExtractionServiceMockTest> clazz = this.getClass();
            URL url = clazz.getResource(FOLDER_BACKUP_DB);
            URI uri = url.toURI();

            return new File(uri);
        } catch (URISyntaxException ue) {
            throw new RuntimeException("bad resourse", ue);
        }
    }

    @Test
    public void backupGlobalDatabase() throws Exception {
        when(query.getResultList()).thenReturn(Arrays.asList(new Object[]{DB_NAME_GLOBAL}));
        when(jpaJdbcUtil.getJdbcConnection(globalCrudService)).thenReturn(connection);

        dbExtractionService.backupDatabase(backupDatabaseFolder.getAbsolutePath(), DB_NAME_GLOBAL);

        Mockito.verify(dbExtractionService, Mockito.times(2)).getGlobalCrudService();
        Mockito.verify(dbExtractionService, Mockito.never()).getTenantCrudService();
        Mockito.verify(dbExtractionService, Mockito.times(1)).getJpaJdbcUtil();
        Mockito.verify(dbExtractionService, Mockito.never()).getTempZipDirectory();

        Mockito.verify(globalCrudService, Mockito.times(1)).getEntityManager();
        Mockito.verify(tenantCrudService, Mockito.never()).getEntityManager();
        Mockito.verify(entityManager, Mockito.times(1)).createNativeQuery(DatabaseExtractionService.SQL_CHECK_DB);
        Mockito.verify(query, Mockito.times(1)).getResultList();

        Mockito.verify(jpaJdbcUtil, Mockito.times(1)).getJdbcConnection(globalCrudService);
        Mockito.verify(connection, Mockito.times(1)).prepareStatement(anyString());
        Mockito.verify(statement, Mockito.times(1)).execute();
        Mockito.verify(statement, Mockito.times(1)).close();
        Mockito.verify(jpaJdbcUtil, Mockito.times(1)).closeConnection(globalCrudService, connection);
    }

    @Test
    public void backupTenantDatabase() throws Exception {
        when(query.getResultList())
                .thenReturn(new ArrayList<Object[]>())
                .thenReturn(Arrays.asList(new Object[]{DB_NAME_TENANT}));
        when(jpaJdbcUtil.getJdbcConnection(Mockito.isA(CrudService.class))).thenReturn(connection);

        dbExtractionService.backupDatabase(backupDatabaseFolder.getAbsolutePath(), DB_NAME_TENANT);

        Mockito.verify(dbExtractionService, Mockito.times(1)).getGlobalCrudService();
        Mockito.verify(dbExtractionService, Mockito.times(2)).getTenantCrudService();
        Mockito.verify(dbExtractionService, Mockito.times(1)).getJpaJdbcUtil();
        Mockito.verify(dbExtractionService, Mockito.never()).getTempZipDirectory();

        Mockito.verify(globalCrudService, Mockito.times(1)).getEntityManager();
        Mockito.verify(tenantCrudService, Mockito.times(1)).getEntityManager();
        Mockito.verify(entityManager, Mockito.times(2)).createNativeQuery(DatabaseExtractionService.SQL_CHECK_DB);
        Mockito.verify(query, Mockito.times(2)).getResultList();

        Mockito.verify(jpaJdbcUtil, Mockito.times(1)).getJdbcConnection(Mockito.isA(CrudService.class));
        Mockito.verify(connection, Mockito.times(1)).prepareStatement(anyString());
        Mockito.verify(statement, Mockito.times(1)).execute();
        Mockito.verify(statement, Mockito.times(1)).close();
        Mockito.verify(jpaJdbcUtil, Mockito.times(1)).closeConnection(tenantCrudService, connection);
    }

    /*
     * extractDatabase is superset of backupDatabase
     */
    @Test
    public void extractDatabase() throws Exception {
        when(query.getResultList()).thenReturn(Arrays.asList(new Object[]{DB_NAME_GLOBAL}));

        when(mockEmailService.sendText(Mockito.isA(String.class), Mockito.isA(String.class),
                Mockito.isA(String.class), Mockito.isA(String.class))).thenReturn("Booya");
        Mockito.doReturn(zippedFile).when(dbExtractionService).createZipFile(anyString(), Mockito.any(ZipDirectory.class));
        when(zippedFile.delete()).thenReturn(true);
        Mockito.doNothing().when(dbExtractionService).createFtpDirectoryIfNotExists(anyString());
        Mockito.doReturn("aspftp.ideas.com/yumyum").when(dbExtractionService)
                .ftpZipFile(anyString(), anyString());
        when(jpaJdbcUtil.getJdbcConnection(Mockito.isA(CrudService.class))).thenReturn(connection);

        dbExtractionService.extractDatabase(DB_NAME_GLOBAL, EMAIL_ADDRESS,
                PacmanWorkContextHelper.getClientCode(), getCurrentProperty());

        Mockito.verify(dbExtractionService, Mockito.times(2)).getGlobalCrudService();
        Mockito.verify(dbExtractionService, Mockito.never()).getTenantCrudService();
        Mockito.verify(dbExtractionService, Mockito.times(1)).getJpaJdbcUtil();
        Mockito.verify(dbExtractionService, Mockito.times(2)).getTempZipDirectory();
        Mockito.verify(dbExtractionService, Mockito.times(1)).getTempZipDirectoryForLinux();

        Mockito.verify(globalCrudService, Mockito.times(1)).getEntityManager();
        Mockito.verify(tenantCrudService, Mockito.never()).getEntityManager();
        Mockito.verify(entityManager, Mockito.times(1)).createNativeQuery(DatabaseExtractionService.SQL_CHECK_DB);
        Mockito.verify(query, Mockito.times(1)).getResultList();

        Mockito.verify(jpaJdbcUtil, Mockito.times(1)).getJdbcConnection(globalCrudService);
        Mockito.verify(connection, Mockito.times(1)).prepareStatement(anyString());
        Mockito.verify(statement, Mockito.times(1)).execute();
        Mockito.verify(statement, Mockito.times(1)).close();
        Mockito.verify(jpaJdbcUtil, Mockito.times(1)).closeConnection(globalCrudService, connection);

        Mockito.verify(mockEmailService, Mockito.times(1)).sendText(Mockito.isA(String.class), Mockito.isA(String.class),
                Mockito.isA(String.class), Mockito.isA(String.class));
        Mockito.verify(dbExtractionService, Mockito.times(1)).createZipFile(anyString(), Mockito.any(ZipDirectory.class));
        Mockito.verify(zippedFile, Mockito.times(1)).delete();
        Mockito.verify(dbExtractionService, Mockito.times(1)).createFtpDirectoryIfNotExists(anyString());
        Mockito.verify(dbExtractionService, Mockito.times(1)).ftpZipFile(anyString(), anyString());
    }

    @Test
    public void extractDatabaseFromLinux() throws Exception {
        when(query.getResultList()).thenReturn(Arrays.asList(new Object[]{DB_NAME_GLOBAL}));

        when(mockEmailService.sendText(Mockito.isA(String.class), Mockito.isA(String.class),
                Mockito.isA(String.class), Mockito.isA(String.class))).thenReturn("Booya");
        Mockito.doReturn(zippedFile).when(dbExtractionService).createZipFile(anyString(), Mockito.any(ZipDirectory.class));
        when(zippedFile.delete()).thenReturn(true);
        Mockito.doNothing().when(dbExtractionService).createFtpDirectoryIfNotExists(anyString());
        Mockito.doReturn("aspftp.ideas.com/yumyum").when(dbExtractionService)
                .ftpZipFile(anyString(), anyString());
        when(jpaJdbcUtil.getJdbcConnection(Mockito.isA(CrudService.class))).thenReturn(connection);
        when(connection.prepareStatement(anyString())).thenReturn(statement);
        Mockito.doReturn(LINUX_TEMP_DIR_PATH.toString()).when(dbExtractionService).getTempZipDirectoryForLinux();


        dbExtractionService.extractDatabase(DB_NAME_GLOBAL, EMAIL_ADDRESS,
                PacmanWorkContextHelper.getClientCode(), getCurrentProperty());

        Mockito.verify(dbExtractionService, Mockito.times(2)).getGlobalCrudService();
        Mockito.verify(dbExtractionService, Mockito.never()).getTenantCrudService();
        Mockito.verify(dbExtractionService, Mockito.times(1)).getJpaJdbcUtil();
        Mockito.verify(dbExtractionService, Mockito.times(2)).getTempZipDirectory();
        Mockito.verify(dbExtractionService, Mockito.times(1)).getTempZipDirectoryForLinux();

        Mockito.verify(globalCrudService, Mockito.times(1)).getEntityManager();
        Mockito.verify(tenantCrudService, Mockito.never()).getEntityManager();
        Mockito.verify(entityManager, Mockito.times(1)).createNativeQuery(DatabaseExtractionService.SQL_CHECK_DB);
        Mockito.verify(query, Mockito.times(1)).getResultList();

        Mockito.verify(jpaJdbcUtil, Mockito.times(1)).getJdbcConnection(globalCrudService);
        Mockito.verify(connection, Mockito.times(1)).prepareStatement(anyString());
        Mockito.verify(statement, Mockito.times(1)).setString(eq(1), startsWith(LINUX_TEMP_DIR_PATH.toString()));
        Mockito.verify(statement, Mockito.times(1)).execute();
        Mockito.verify(statement, Mockito.times(1)).close();
        Mockito.verify(jpaJdbcUtil, Mockito.times(1)).closeConnection(globalCrudService, connection);

        Mockito.verify(mockEmailService, Mockito.times(1)).sendText(Mockito.isA(String.class), Mockito.isA(String.class),
                Mockito.isA(String.class), Mockito.isA(String.class));
        Mockito.verify(dbExtractionService, Mockito.times(1)).createZipFile(anyString(), Mockito.any(ZipDirectory.class));
        Mockito.verify(zippedFile, Mockito.times(1)).delete();
        Mockito.verify(dbExtractionService, Mockito.times(1)).createFtpDirectoryIfNotExists(anyString());
        Mockito.verify(dbExtractionService, Mockito.times(1)).ftpZipFile(anyString(), anyString());
    }

    @Test
    public void extractDatabaseWhenBackupFails() throws Exception {
        when(mockEmailService.sendText(Mockito.isA(String.class), Mockito.isA(String.class),
                Mockito.isA(String.class), Mockito.isA(String.class))).thenReturn("Booya");

        when(query.getResultList()).thenReturn(Arrays.asList(new Object[]{DB_NAME_GLOBAL}));

        Mockito.reset(statement);
        when(statement.execute()).thenThrow(new SQLException("Not today buddy"));
        Mockito.doNothing().when(statement).close();

        Mockito.doReturn(zippedFile).when(dbExtractionService).createZipFile(anyString(), Mockito.any(ZipDirectory.class));
        when(zippedFile.delete()).thenReturn(true);
        Mockito.doNothing().when(dbExtractionService).createFtpDirectoryIfNotExists(anyString());
        Mockito.doReturn("aspftp.ideas.com/yumyum").when(dbExtractionService)
                .ftpZipFile(anyString(), anyString());
        when(jpaJdbcUtil.getJdbcConnection(Mockito.isA(CrudService.class))).thenReturn(connection);

        dbExtractionService.extractDatabase(DB_NAME_GLOBAL, EMAIL_ADDRESS,
                PacmanWorkContextHelper.getClientCode(), getCurrentProperty());

        Mockito.verify(dbExtractionService, Mockito.times(2)).getGlobalCrudService();
        Mockito.verify(dbExtractionService, Mockito.never()).getTenantCrudService();
        Mockito.verify(dbExtractionService, Mockito.times(1)).getJpaJdbcUtil();
        Mockito.verify(dbExtractionService, Mockito.times(1)).getTempZipDirectory();
        Mockito.verify(dbExtractionService, Mockito.times(1)).getTempZipDirectoryForLinux();
        Mockito.verify(globalCrudService, Mockito.times(1)).getEntityManager();
        Mockito.verify(tenantCrudService, Mockito.never()).getEntityManager();
        Mockito.verify(entityManager, Mockito.times(1)).createNativeQuery(DatabaseExtractionService.SQL_CHECK_DB);
        Mockito.verify(query, Mockito.times(1)).getResultList();

        Mockito.verify(jpaJdbcUtil, Mockito.times(1)).getJdbcConnection(globalCrudService);
        Mockito.verify(connection, Mockito.times(1)).prepareStatement(anyString());
        Mockito.verify(statement, Mockito.times(1)).execute();
        Mockito.verify(statement, Mockito.times(1)).close();
        Mockito.verify(jpaJdbcUtil, Mockito.times(1)).closeConnection(globalCrudService, connection);

        Mockito.verify(mockEmailService, Mockito.times(1)).sendText(Mockito.isA(String.class), Mockito.isA(String.class),
                Mockito.isA(String.class), Mockito.isA(String.class));
        Mockito.verify(dbExtractionService, Mockito.never()).createZipFile(anyString(), Mockito.any(ZipDirectory.class));
        Mockito.verify(zippedFile, Mockito.never()).delete();
        Mockito.verify(dbExtractionService, Mockito.never()).createFtpDirectoryIfNotExists(anyString());
        Mockito.verify(dbExtractionService, Mockito.never()).ftpZipFile(anyString(), anyString());
    }

    @Test
    public void extractDatabaseHasEmailException() throws Exception {
        // Nothing much changes from happy path as email is last step and we eat exception as not much we can do
        when(query.getResultList()).thenReturn(Arrays.asList(new Object[]{DB_NAME_GLOBAL}));

        when(mockEmailService.sendText(Mockito.isA(String.class), Mockito.isA(String.class),
                Mockito.isA(String.class), Mockito.isA(String.class))).thenThrow(new TetrisException("Kaboom"));
        Mockito.doReturn(zippedFile).when(dbExtractionService).createZipFile(anyString(), Mockito.any(ZipDirectory.class));
        when(zippedFile.delete()).thenReturn(true);
        Mockito.doNothing().when(dbExtractionService).createFtpDirectoryIfNotExists(anyString());
        Mockito.doReturn("aspftp.ideas.com/yumyum").when(dbExtractionService)
                .ftpZipFile(anyString(), anyString());
        when(jpaJdbcUtil.getJdbcConnection(Mockito.isA(CrudService.class))).thenReturn(connection);

        try {
            dbExtractionService.extractDatabase(DB_NAME_GLOBAL, EMAIL_ADDRESS,
                    PacmanWorkContextHelper.getClientCode(), getCurrentProperty());

            fail("Expecting an exception to be thrown");
        } catch (TetrisException te) {
            Mockito.verify(dbExtractionService, Mockito.times(2)).getGlobalCrudService();
            Mockito.verify(dbExtractionService, Mockito.never()).getTenantCrudService();
            Mockito.verify(dbExtractionService, Mockito.times(1)).getJpaJdbcUtil();
            Mockito.verify(dbExtractionService, Mockito.times(2)).getTempZipDirectory();
            Mockito.verify(dbExtractionService, Mockito.times(1)).getTempZipDirectoryForLinux();
            Mockito.verify(globalCrudService, Mockito.times(1)).getEntityManager();
            Mockito.verify(tenantCrudService, Mockito.never()).getEntityManager();
            Mockito.verify(entityManager, Mockito.times(1)).createNativeQuery(DatabaseExtractionService.SQL_CHECK_DB);
            Mockito.verify(query, Mockito.times(1)).getResultList();

            Mockito.verify(jpaJdbcUtil, Mockito.times(1)).getJdbcConnection(globalCrudService);
            Mockito.verify(connection, Mockito.times(1)).prepareStatement(anyString());
            Mockito.verify(statement, Mockito.times(1)).execute();
            Mockito.verify(statement, Mockito.times(1)).close();
            Mockito.verify(jpaJdbcUtil, Mockito.times(1)).closeConnection(globalCrudService, connection);

            Mockito.verify(mockEmailService, Mockito.times(1)).sendText(Mockito.isA(String.class), Mockito.isA(String.class),
                    Mockito.isA(String.class), Mockito.isA(String.class));
            Mockito.verify(dbExtractionService, Mockito.times(1)).createZipFile(anyString(), Mockito.any(ZipDirectory.class));
            Mockito.verify(zippedFile, Mockito.times(1)).delete();
            Mockito.verify(dbExtractionService, Mockito.times(1)).createFtpDirectoryIfNotExists(anyString());
            Mockito.verify(dbExtractionService, Mockito.times(1)).ftpZipFile(anyString(), anyString());
        }
    }

    @Test
    public void getConfigParameterSqlAsString_noValue() {
        String result = dbExtractionService.getConfigParameterSqlAsString();
        assertNotNull(result);
        assertEquals("", result);
    }

    @Test
    public void getConfigParameterSqlAsString() {
        ConfigParameter configParameter = getConfigParam("parameter");
        configParameter.setDefaultValue("ok");
        when(globalCrudService.findByNativeQuery(DatabaseExtractionService.GET_PARAMETERS_EXCLUDING_CATEGORY_NAME_AND_GROUP_NAME, QueryParameter.with("categoryName", DatabaseExtractionService.CONFIG_PARAMETER_CATEGORY_NAME_OUTBOUND_CONNECTION_DETAILS)
                .and("groupName_1", DatabaseExtractionService.CONFIG_PARAMETER_GROUP_NAME_DATAFEED).and("groupName_2", DatabaseExtractionService.CONFIG_PARAMETER_GROUP_NAME_VENDOR).and("groupName_3", DatabaseExtractionService.CONFIG_PARAMETER_GROUP_NAME_DEFAULT_VENDOR).parameters()))
                .thenReturn(Collections.singletonList(configParameter.getName()));
        when(configParamsService.getParameterValue("parameter")).thenReturn("theParameterValue");

        String result = dbExtractionService.getConfigParameterSqlAsString();

        Mockito.verify(globalCrudService).findByNativeQuery(DatabaseExtractionService.GET_PARAMETERS_EXCLUDING_CATEGORY_NAME_AND_GROUP_NAME, QueryParameter.with("categoryName", DatabaseExtractionService.CONFIG_PARAMETER_CATEGORY_NAME_OUTBOUND_CONNECTION_DETAILS)
                .and("groupName_1", DatabaseExtractionService.CONFIG_PARAMETER_GROUP_NAME_DATAFEED).and("groupName_2", DatabaseExtractionService.CONFIG_PARAMETER_GROUP_NAME_VENDOR).and("groupName_3", DatabaseExtractionService.CONFIG_PARAMETER_GROUP_NAME_DEFAULT_VENDOR).parameters());
        Mockito.verify(configParamsService).getParameterValue("parameter");
    }

    @Test
    public void testAddConfigParamSqlForVendorGroup() {

        String contextLike = "%.BSTN.H1";
        String context = "advantagereserve.BSTN.H1";
        VendorGroupConfigDetails configParameter_1 = new VendorGroupConfigDetails();

        configParameter_1.setConfigParamName("pacman.integration.soapChunkSize");
        configParameter_1.setConfigParamContext(context);
        configParameter_1.setConfigFixedValue("25");
        configParameter_1.setConfigValue("");

        VendorGroupConfigDetails configParameter_2 = new VendorGroupConfigDetails();
        configParameter_2.setConfigParamName("pacman.integration.Fplos.uploadtype");
        configParameter_2.setConfigParamContext(context);
        configParameter_2.setConfigFixedValue("");
        configParameter_2.setConfigValue("differential");

        VendorGroupConfigDetails configParameter_3 = new VendorGroupConfigDetails();
        configParameter_3.setConfigParamName("pacman.integration.UploadCurrencyForDailyBAR");
        configParameter_3.setConfigParamContext(context);
        configParameter_3.setConfigFixedValue("24");
        configParameter_3.setConfigValue("");

        when(globalCrudService.findByNativeQuery(eq(DatabaseExtractionService.GET_PARAMETERS_FOR_GROUP_NAME_VENDOR), eq(QueryParameter.with("Context", contextLike).parameters()), any(RowMapper.class))
        ).thenReturn(List.of(configParameter_1, configParameter_2, configParameter_3));


        when(configParamsService.getParameterValue("pacman.integration.soapChunkSize")).thenReturn("25");
        when(configParamsService.getParameterValue("pacman.integration.UploadCurrencyForDailyBAR")).thenReturn("24");
        when(configParamsService.getParameterValue("pacman.integration.Fplos.uploadtype")).thenReturn("differential");

        List<String> result = dbExtractionService.addConfigParamSqlForVendorGroup();
        assertEquals(3, result.size());

        // assert the context
        assertTrue(result.get(0).contains("advantagereserve.BSTN.H1"));
        assertTrue(result.get(1).contains("advantagereserve.BSTN.H1"));
        assertTrue(result.get(2).contains("advantagereserve.BSTN.H1"));

        //assert the exact query
        String[] generatedQueries_1 = result.get(0).split("\n");
        String deleteQuery_2 = "DELETE from [dbo].[Config_Parameter_Value] WHERE  Context = 'advantagereserve.BSTN.H1' and Config_Parameter_ID = (SELECT [Config_Parameter_ID] from [dbo].[Config_Parameter] where Name = 'pacman.integration.soapChunkSize');";
        String insertQuery_2 = "INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID],[Context], [FixedValue],[Config_Parameter_Predefined_Value_ID],[Created_By_User_ID],[Last_Updated_By_User_ID]) VALUES ((SELECT [Config_Parameter_ID] from [dbo].[Config_Parameter] where Name = 'pacman.integration.soapChunkSize'), 'advantagereserve.BSTN.H1', '25', (SELECT  cppv.Config_Parameter_Predefined_Value_ID  from  Config_Parameter_Predefined_Value cppv WHERE cppv.Value = null and cppv.Config_Parameter_Predefined_Value_Type_ID = (SELECT Config_Parameter_Predefined_Value_Type_ID from Config_Parameter where Config_Parameter.Name = 'pacman.integration.soapChunkSize')), 1, 1);";


        assertEquals(deleteQuery_2, generatedQueries_1[0]);
        assertEquals(insertQuery_2, generatedQueries_1[1] + " " + generatedQueries_1[2]);

        String[] generatedQueries_2 = result.get(1).split("\n");
        String deleteQuery_1 = "DELETE from [dbo].[Config_Parameter_Value] WHERE  Context = 'advantagereserve.BSTN.H1' and Config_Parameter_ID = (SELECT [Config_Parameter_ID] from [dbo].[Config_Parameter] where Name = 'pacman.integration.Fplos.uploadtype');";
        String insertQuery_1 = "INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID],[Context], [FixedValue],[Config_Parameter_Predefined_Value_ID],[Created_By_User_ID],[Last_Updated_By_User_ID]) VALUES ((SELECT [Config_Parameter_ID] from [dbo].[Config_Parameter] where Name = 'pacman.integration.Fplos.uploadtype'), 'advantagereserve.BSTN.H1', null, (SELECT  cppv.Config_Parameter_Predefined_Value_ID  from  Config_Parameter_Predefined_Value cppv WHERE cppv.Value = 'differential' and cppv.Config_Parameter_Predefined_Value_Type_ID = (SELECT Config_Parameter_Predefined_Value_Type_ID from Config_Parameter where Config_Parameter.Name = 'pacman.integration.Fplos.uploadtype')), 1, 1);";

        assertEquals(deleteQuery_1, generatedQueries_2[0]);
        assertEquals(insertQuery_1, generatedQueries_2[1] + " " + generatedQueries_2[2]);

        String[] generatedQueries_3 = result.get(2).split("\n");
        String deleteQuery_3 = "DELETE from [dbo].[Config_Parameter_Value] WHERE  Context = 'advantagereserve.BSTN.H1' and Config_Parameter_ID = (SELECT [Config_Parameter_ID] from [dbo].[Config_Parameter] where Name = 'pacman.integration.UploadCurrencyForDailyBAR');";
        String insertQuery_3 = "INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID],[Context], [FixedValue],[Config_Parameter_Predefined_Value_ID],[Created_By_User_ID],[Last_Updated_By_User_ID]) VALUES ((SELECT [Config_Parameter_ID] from [dbo].[Config_Parameter] where Name = 'pacman.integration.UploadCurrencyForDailyBAR'), 'advantagereserve.BSTN.H1', '24', (SELECT  cppv.Config_Parameter_Predefined_Value_ID  from  Config_Parameter_Predefined_Value cppv WHERE cppv.Value = null and cppv.Config_Parameter_Predefined_Value_Type_ID = (SELECT Config_Parameter_Predefined_Value_Type_ID from Config_Parameter where Config_Parameter.Name = 'pacman.integration.UploadCurrencyForDailyBAR')), 1, 1);";

        assertEquals(deleteQuery_3, generatedQueries_3[0]);
        assertEquals(insertQuery_3, generatedQueries_3[1] + " " + generatedQueries_3[2]);

        Mockito.verify(globalCrudService).findByNativeQuery(eq(DatabaseExtractionService.GET_PARAMETERS_FOR_GROUP_NAME_VENDOR), eq(QueryParameter.with("Context", contextLike).parameters()), any(RowMapper.class));
    }

    @Test
    public void testAddConfigParamSqlForVendorGroupWhenNoVendorsPresent() {
        String contextLike = "%.Hilton.LAXAG";
        when(globalCrudService.findByNativeQuery(eq(DatabaseExtractionService.GET_PARAMETERS_FOR_GROUP_NAME_VENDOR), eq(QueryParameter.with("Context", contextLike).parameters()), any(RowMapper.class))
        ).thenReturn(null);
        List<String> result = dbExtractionService.addConfigParamSqlForVendorGroup();
        assertEquals(0, result.size());
    }

    @Test
    public void extractLimitedDatabaseDataWhenAllIncluded() throws Exception {
        Mockito.doReturn(mockedRuntimeProcess).when(dbExtractionService).getRunTimeProcess(anyString());
        Mockito.doReturn(0).when(mockedRuntimeProcess).waitFor();
        Mockito.doReturn(getFakeInputStream()).when(mockedRuntimeProcess).getInputStream();
        when(clientService.getClientByCode(PacmanWorkContextHelper.getClientCode())).thenReturn(buildClient());
        when(propertyService.getPropertyByCode(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode())).thenReturn(buildProperty());

        assertTrue(dbExtractionService.extractLimitedDatabaseData(backupDatabaseFolder.getAbsolutePath() + "/test", getDBLoc(), false, false, false));
        Mockito.verify(dbExtractionService, atLeast(1)).executeSchemaZen(anyString());

    }

    @Test
    public void extractGlobalParameterConfigsData() throws Exception {
        ConfigParameter configParameter = getConfigParam("parameter1");
        configParameter.setDefaultValue("ok");

        when(globalCrudService.findByNativeQuery(DatabaseExtractionService.GET_PARAMETERS_EXCLUDING_CATEGORY_NAME_AND_GROUP_NAME, QueryParameter.with("categoryName", DatabaseExtractionService.CONFIG_PARAMETER_CATEGORY_NAME_OUTBOUND_CONNECTION_DETAILS)
                .and("groupName_1", DatabaseExtractionService.CONFIG_PARAMETER_GROUP_NAME_DATAFEED).and("groupName_2", DatabaseExtractionService.CONFIG_PARAMETER_GROUP_NAME_VENDOR).and("groupName_3", DatabaseExtractionService.CONFIG_PARAMETER_GROUP_NAME_DEFAULT_VENDOR).parameters()))
                .thenReturn(Collections.singletonList(configParameter.getName()));
        when(configParamsService.getParameterValue("parameter")).thenReturn("theParameterValue");

        Mockito.doReturn(mockedRuntimeProcess).when(dbExtractionService).getRunTimeProcess(anyString());
        Mockito.doReturn(0).when(mockedRuntimeProcess).waitFor();
        Mockito.doReturn(getFakeInputStream()).when(mockedRuntimeProcess).getInputStream();
        when(clientService.getClientByCode(PacmanWorkContextHelper.getClientCode())).thenReturn(buildClient());
        when(propertyService.getPropertyByCode(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode())).thenReturn(buildProperty());
        dbExtractionService.extractGlobalConfigParameters(backupDatabaseFolder.getAbsolutePath() + "/test", getDBLoc());
        Mockito.verify(configParamsService, atLeast(1)).getParameterValue(anyString());
    }

    @Test
    public void extractLimitedDatabaseDataWhenOperaPaceRevenueStreamExcluded() throws Exception {
        Mockito.doReturn(mockedRuntimeProcess).when(dbExtractionService).getRunTimeProcess(anyString());
        Mockito.doReturn(0).when(mockedRuntimeProcess).waitFor();
        Mockito.doReturn(getFakeInputStream()).when(mockedRuntimeProcess).getInputStream();
        when(clientService.getClientByCode(PacmanWorkContextHelper.getClientCode())).thenReturn(buildClient());
        when(propertyService.getPropertyByCode(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode())).thenReturn(buildProperty());

        assertTrue(dbExtractionService.extractLimitedDatabaseData(backupDatabaseFolder.getAbsolutePath() + "/test", getDBLoc(), true, true, true));
        Mockito.verify(dbExtractionService, atMostOnce()).executeSchemaZen(anyString());
    }

    @Test
    public void extractLimitedDatabaseDataFailure() throws Exception {
        Mockito.doReturn(mockedRuntimeProcess).when(dbExtractionService).getRunTimeProcess(anyString());
        Mockito.doThrow(new InterruptedException("failure")).when(mockedRuntimeProcess).waitFor();
        when(clientService.getClientByCode(PacmanWorkContextHelper.getClientCode())).thenReturn(buildClient());
        when(propertyService.getPropertyByCode(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode())).thenReturn(buildProperty());

        assertFalse(dbExtractionService.extractLimitedDatabaseData(backupDatabaseFolder.getAbsolutePath() + "/test", getDBLoc(), false, false, false));
    }
    @Test
    public void testRemoveScheduledTableName() throws Exception {
        String baseTableNames = String.join(",", DatabaseExtractionInfo.BASE_TABLES_TO_EXTRACT);
        String[] baseTableNameArray = baseTableNames.split(",");
        assertTrue(Arrays.stream(baseTableNameArray).anyMatch(DatabaseExtractionInfo.SCHEDULED_REPORTS::contains));
        String modfiedString = dbExtractionService.removeScheduledTableName(baseTableNames);
        baseTableNameArray = modfiedString.split(",");
        assertFalse(Arrays.stream(baseTableNameArray).anyMatch(DatabaseExtractionInfo.SCHEDULED_REPORTS::contains));
    }

    @Test
    public void extractLimitedDatabaseDataWhenSkipScheduledReportToggleOn() throws Exception {
        Mockito.doReturn(true).when(dbExtractionService).skipScheduleReportTableToggleEnabled();
        Mockito.doReturn(mockedRuntimeProcess).when(dbExtractionService).getRunTimeProcess(anyString());
        Mockito.doReturn(0).when(mockedRuntimeProcess).waitFor();
        Mockito.doReturn(getFakeInputStream()).when(mockedRuntimeProcess).getInputStream();
        when(clientService.getClientByCode(PacmanWorkContextHelper.getClientCode())).thenReturn(buildClient());
        when(propertyService.getPropertyByCode(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode())).thenReturn(buildProperty());
        assertTrue(dbExtractionService.extractLimitedDatabaseData(backupDatabaseFolder.getAbsolutePath() + "/test", getDBLoc(), false, false, false));
        Mockito.verify(dbExtractionService, atLeast(1)).removeScheduledTableName(anyString());
    }

    @Test
    public void buildClientPropertySql() {
        Integer propertyId = 999555;

        Client client = buildClient();
        Property property = buildProperty();
        property.setId(propertyId);
        property.setClient(client);

        when(clientService.getClientByCode(client.getCode())).thenReturn(client);
        when(propertyService.getPropertyByCode(PacmanWorkContextHelper.getClientCode(), PacmanWorkContextHelper.getPropertyCode())).thenReturn(property);

        String sql = dbExtractionService.buildClientPropertySql();

        assertNotNull(sql);
        assertTrue(sql.contains(String.valueOf(propertyId)));
    }

    private DBLoc getDBLoc() {
        DBLoc dbloc = new DBLoc();
        dbloc.setDbName("abc");
        dbloc.setPortNumber(123);
        dbloc.setServerName("server");
        return dbloc;
    }

    private InputStream getFakeInputStream() {
        InputStream inputStream = new ByteArrayInputStream("Success".getBytes());
        return inputStream;
    }

    private Property getCurrentProperty() {
        Property property = new Property();
        property.setId(PacmanWorkContextHelper.getPropertyId());
        property.setCode(PacmanWorkContextHelper.getPropertyCode());
        return property;
    }

    private ConfigParameter getConfigParam(String name) {
        ConfigParameter configParameter = new ConfigParameter();
        configParameter.setName(name);
        return configParameter;
    }

    private Client buildClient() {
        Client client = new Client();
        client.setCode("BSTN");

        return client;
    }

    private Property buildProperty() {
        Property property = new Property();
        property.setCode("H1");
        property.setId(5);

        return property;
    }
}
