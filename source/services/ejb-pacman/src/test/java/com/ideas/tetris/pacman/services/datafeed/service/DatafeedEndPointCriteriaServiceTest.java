package com.ideas.tetris.pacman.services.datafeed.service;

import com.ideas.tetris.pacman.common.configparams.*;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPConfiguration;
import com.ideas.tetris.pacman.services.datafeed.endpoint.DatafeedEndPointCriteria;
import com.ideas.tetris.pacman.services.datafeed.endpoint.EndpointBucket;
import com.ideas.tetris.pacman.services.datafeed.endpoint.EndpointFrequencyType;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.hamcrest.collection.IsCollectionWithSize;
import org.hamcrest.core.Is;
import org.hamcrest.core.IsCollectionContaining;
import org.hamcrest.core.IsEqual;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)

public class DatafeedEndPointCriteriaServiceTest {

    public static final String TEST_CLIENT = "TestClient";
    @InjectMocks
    private DatafeedEndPointCriteriaService datafeedEndPointCriteriaService;

    @Mock
    private PacmanConfigParamsService pacmanConfigParamsServiceMock;

    @Mock
    private PricingConfigurationService pricingConfigurationServiceMock;

    private final Set<EndpointFrequencyType> frequencyTypes = Stream.of(EndpointFrequencyType.DAILY).collect(Collectors.toSet());

    @BeforeEach
    public void setUp() {
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value())).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.LDB_PROJECTION_DATAFEED_ENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.ENABLE_GROUP_EVALUATION_IN_DATA_FEED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.ENABLE_BOOKING_ID_COLUMN_IN_GROUP_EVALUATION_FILE)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.AGILE_RATES_PRODUCT_CONFIGURATION_ENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.GROUP_FINAL_FORECAST_OVERRIDE_ENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.ENABLE_PROPERTY_SPECIFIC_DATAFEED_NEW_COLUMNS)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.ENABLE_PROFIT_POPULATION)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.PROFIT_METRICS_DATAFEED_ENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.ENABLE_OOO_OVERRIDE_DATAFEED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.OUT_OF_ORDER_OVERRIDES_ENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.ENABLE_TAX_VALUE_DATAFEED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.HIERARCHY_INDEPENDENT_PRODUCT_COLUMN_ENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.ENABLE_PRODUCT_SEND_DECISION_ADJUSTMENT_DATAFEED_FILE)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.ENABLE_INVENTORY_HISTORY_FILE_ADJUSTMENT_COLUMNS)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.STRENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.DEMAND360SUBSCRIBER_PROPERTY_ID)).thenReturn(null);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.RATE_PROTECT_FOR_DATAFEED_ENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.EXTENDED_STAY_UNQUALIFIED_RATE_MANAGEMENT_ENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValueByClientLevel(PreProductionConfigParamName.VIRTUAL_PROPERTY_MAPPING_ENABLED.getParameterName())).thenReturn(Boolean.FALSE.toString());
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.ENABLE_ST19_FOR_DATAFEED_MSRT)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.HILTON_AGE_BASED_PRICING_DATAFEED_ENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.HILTON_AGE_BASED_PRICING_ENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.ENHANCED_PACKAGE_ELEMENT_DF_ENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.GP_MIN_PROFIT_CONFIGURATION_DATAFEED_FILES)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.IS_PRICE_DROP_RESTRICTIONS_DATAFEED_ENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_COlUMN_FOR_CPSUPPLEMENT_DATAFEED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.INCLUDE_ADDITIONAL_INFORMATION_DATAFEED_LIMITED_DATA_BUILD_UPDATE)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.INCLUDE_ADDITIONAL_INFORMATION_DATAFEED_LIMITED_DATA_BUILD_UPDATE)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.MEETING_PACKAGE_PRICING_ENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.IS_MEETING_PACKAGE_PRICING_DATAFEED_ENABLED)).thenReturn(Boolean.FALSE);
    }

    @Test
    public void shouldSetContinuousPricingEnabledCriteria() throws Exception {
        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED.value())).thenReturn(Boolean.TRUE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isContinuousPricingEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void shouldSetContinuousPricingDisabledCriteria() throws Exception {
        mockDatafeedCoreBucket();

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isContinuousPricingEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void shouldSetPercentageColumnForCPSupplementEnabledCriteria() {
        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_COlUMN_FOR_CPSUPPLEMENT_DATAFEED)).thenReturn(Boolean.TRUE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isPercentageColumnForCPSupplementOfDatafeedEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void shouldSetPercentageColumnForCPSupplementDisabledCriteria() {
        mockDatafeedCoreBucket();

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isContinuousPricingEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void shouldNotSetEnableInventoryLimit_whenFalse() {
        mockDatafeedConfigurationBucket();
        final DatafeedEndPointCriteria datafeedEndPointCriteria = datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isEnabledInventoryLimit(), Is.is(Boolean.FALSE));
    }

    @Test
    public void shouldSetEnableInventoryLimit_whenTrue() {
        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_INVENTORY_LIMIT_DATAFEED)).thenReturn(Boolean.TRUE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria = datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isEnabledInventoryLimit(), Is.is(Boolean.TRUE));
    }

    @Test
    public void shouldSetSpecialUseRoomTypes_whenFalse() {
        mockDatafeedConfigurationBucket();
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isSpecialUseRoomTypesDatafeedEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void shouldSetSpecialUseRoomTypes_whenTrue() {
        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.SPECIAL_USE_ROOM_TYPES_DATAFEED_ENABLED)).thenReturn(Boolean.TRUE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isSpecialUseRoomTypesDatafeedEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyProductPackageElementDFEnabled() {
        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED)).thenReturn(Boolean.TRUE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isAgileRatesEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyProductPackageElementDFDisabled() {
        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.ENHANCED_PACKAGE_ELEMENT_DF_ENABLED)).thenReturn(Boolean.FALSE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isEnhancedProductPackageElementDFEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaWithAgileRatesEnabled() {
        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.ENHANCED_PACKAGE_ELEMENT_DF_ENABLED)).thenReturn(Boolean.TRUE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isEnhancedProductPackageElementDFEnabled(), Is.is(Boolean.TRUE));
    }
    @Test
    public void shouldSetLastLDBUpdateColumnForPropertyBasicInformationDisabledCriteria() {
        mockDatafeedCoreBucket();

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isAdditionalInformationalFieldsLastLDBUpdateEnabled(), Is.is(Boolean.FALSE));
    }
    @Test
    public void verifyDataFeedCriteriaWithAgileRatesDisabled() {
        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED)).thenReturn(Boolean.FALSE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isAgileRatesEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaWithAgileRatesEnabledProductConfigurationEnabled() {
        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.AGILE_RATES_PRODUCT_CONFIGURATION_ENABLED)).thenReturn(Boolean.TRUE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isAgileRatesProductConfigurationEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaWithAgileRatesEnabledProductConfigurationDisabled() {
        mockDatafeedCoreBucket();

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isAgileRatesProductConfigurationEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaWithAgeBasedPricingDFEnabled() {
        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.HILTON_AGE_BASED_PRICING_DATAFEED_ENABLED)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.HILTON_AGE_BASED_PRICING_ENABLED)).thenReturn(Boolean.TRUE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isAgeBasedPricingForHiltonEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaWithAgeBasedPricingDFDisabled() {
        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.HILTON_AGE_BASED_PRICING_DATAFEED_ENABLED)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.HILTON_AGE_BASED_PRICING_ENABLED)).thenReturn(Boolean.FALSE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isAgeBasedPricingForHiltonEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaWithLDBEnabledAndLDBProjectionEnabled() {
        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.LDB_PROJECTION_DATAFEED_ENABLED)).thenReturn(Boolean.TRUE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.WEEKLY).collect(Collectors.toSet()), TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isLDBProjectionEnabled(), Is.is(Boolean.TRUE));
    }

    public void verifyDataFeedCriteriaWithLDBEnabledAndLDBProjectionDisabled() {
        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.LDB_PROJECTION_DATAFEED_ENABLED)).thenReturn(Boolean.FALSE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.WEEKLY).collect(Collectors.toSet()), TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isLDBProjectionEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaWithLDBDisabledAndLDBProjectionEnabled() {
        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(IPConfigParamName.CORE_LIMITED_DATA_BUILD_ENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.LDB_PROJECTION_DATAFEED_ENABLED)).thenReturn(Boolean.TRUE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.WEEKLY).collect(Collectors.toSet()), TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isLDBProjectionEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaPropertySpecificNewColumnsEnabled() {

        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.ENABLE_PROPERTY_SPECIFIC_DATAFEED_NEW_COLUMNS)).thenReturn(Boolean.TRUE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isPropertySpecificNewColumnsEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaPropertySpecificNewColumnsDisabled() {

        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.ENABLE_PROPERTY_SPECIFIC_DATAFEED_NEW_COLUMNS)).thenReturn(Boolean.FALSE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isPropertySpecificNewColumnsEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaPropertySpecificUpdatedColumnsEnabled() {
        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.ENABLE_PROPERTY_SPECIFIC_DATAFEED_NEW_COLUMNS)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.IS_PRICE_DROP_RESTRICTIONS_DATAFEED_ENABLED)).thenReturn(Boolean.TRUE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isPropertySpecificUpdatedColumnsEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaPropertySpecificUpdatedColumnsDisabled() {
        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.ENABLE_PROPERTY_SPECIFIC_DATAFEED_NEW_COLUMNS)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.IS_PRICE_DROP_RESTRICTIONS_DATAFEED_ENABLED)).thenReturn(Boolean.FALSE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isPropertySpecificUpdatedColumnsEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyProductSendDecisionAsAdjustmentDataFeedFileEnabled() {

        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.ENABLE_PRODUCT_SEND_DECISION_ADJUSTMENT_DATAFEED_FILE)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED)).thenReturn(Boolean.TRUE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isSendDecisionAdjustmentEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyProductSendDecisionAsAdjustmentDataFeedFileDisabled() {

        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.HILTON_OPTION_TO_SEND_ADJUSTMENT_FOR_AGILE_ENABLED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.ENABLE_PRODUCT_SEND_DECISION_ADJUSTMENT_DATAFEED_FILE)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED)).thenReturn(Boolean.TRUE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isSendDecisionAdjustmentEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaWithGroupFinalForecastEnabled() {

        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.GROUP_FINAL_FORECAST_OVERRIDE_ENABLED)).thenReturn(Boolean.TRUE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.DAILY).collect(Collectors.toSet()), TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isGroupFinalForecastEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaWithGroupFinalForecastDisabled() {

        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.GROUP_FINAL_FORECAST_OVERRIDE_ENABLED)).thenReturn(Boolean.FALSE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.DAILY).collect(Collectors.toSet()), TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isGroupFinalForecastEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaWithOutOfOrderEnabled() {

        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.ENABLE_OOO_OVERRIDE_DATAFEED)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.OUT_OF_ORDER_OVERRIDES_ENABLED)).thenReturn(Boolean.TRUE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.WEEKLY).collect(Collectors.toSet()), TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isOutOfOrderOverridesEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaWithOutOfOrderDisabled() {

        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.ENABLE_OOO_OVERRIDE_DATAFEED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.OUT_OF_ORDER_OVERRIDES_ENABLED)).thenReturn(Boolean.FALSE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.WEEKLY).collect(Collectors.toSet()), TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isOutOfOrderOverridesEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyAgileRatesProductGroupsFilesEnabled() {

        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED)).thenReturn(Boolean.TRUE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isAgileProductOptimizationFilesEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyAgileRatesProductGroupsFilesDisabled() {

        mockDatafeedConfigurationBucket();
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isAgileProductOptimizationFilesEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyAgileRatesIndependentProductOptimizationFilesEnabled() {
        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.HIERARCHY_INDEPENDENT_PRODUCT_COLUMN_ENABLED)).thenReturn(Boolean.TRUE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isAgileIndependentProductHierarchyColumnEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyAgileRatesIndependentProductOptimizationFilesDisabled() {

        mockDatafeedConfigurationBucket();
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isAgileIndependentProductHierarchyColumnEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaWithTaxInclusiveConfigurationEnabled() {

        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.ENABLE_TAX_VALUE_DATAFEED)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsServiceMock.getParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(Boolean.TRUE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isTaxInclusiveConfigurationEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaWithTaxInclusiveConfigurationDisabled() {

        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.ENABLE_TAX_VALUE_DATAFEED)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(Boolean.TRUE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isTaxInclusiveConfigurationEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaWithStrDisabled() {
        mockDatafeedCoreBucket();
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.DAILY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isStrEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaWithStrEnabled() {
        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.STRENABLED)).thenReturn(Boolean.TRUE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.DAILY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isStrEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaWithD360PropertyIDDisabled() {
        mockDatafeedCoreBucket();
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.DAILY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isDemand360Enabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaWithD360PropertyIDEnabled() {
        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.DEMAND360SUBSCRIBER_PROPERTY_ID)).thenReturn("ANY");
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.DAILY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isDemand360Enabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaWithRateProtectEnabled() {
        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.RATE_PROTECT_FOR_DATAFEED_ENABLED)).thenReturn(Boolean.TRUE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isRateProtectEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaWithRateProtectDisabled() {
        mockDatafeedConfigurationBucket();
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isRateProtectEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaWithVirtualPropertyMappingDisabled() {
        mockDatafeedConfigurationBucket();
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.CLIENT_DAILY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isVirtualPropertyMappingForHiltonEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaWithVirtualPropertyMappingEnabled() {
        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getParameterValueByClientLevel(PreProductionConfigParamName.VIRTUAL_PROPERTY_MAPPING_ENABLED.getParameterName())).thenReturn(Boolean.TRUE.toString());
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.CLIENT_DAILY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isVirtualPropertyMappingForHiltonEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaWithST19ForDatafeedMSRTEnabled() {
        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.ENABLE_ST19_FOR_DATAFEED_MSRT)).thenReturn(Boolean.TRUE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.DAILY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isSt19ForDatafeedMSRTEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaWithST19ForDatafeedMSRTDisabled() {
        mockDatafeedCoreBucket();
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.DAILY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isSt19ForDatafeedMSRTEnabled(), Is.is(Boolean.FALSE));
    }


    @Test
    public void verifyDataFeedCriteriaWithExtendedStayRateManagementEnabled() {
        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.EXTENDED_STAY_UNQUALIFIED_RATE_MANAGEMENT_ENABLED)).thenReturn(Boolean.TRUE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isExtendedStayUnqualifiedRateManagementEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaWithExtendedStayRateManagementDisabled() {
        mockDatafeedConfigurationBucket();
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isExtendedStayUnqualifiedRateManagementEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void shouldSetProfitMetricsDatafeedEnabledCriteria() {
        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.PROFIT_METRICS_DATAFEED_ENABLED)).thenReturn(Boolean.TRUE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.DAILY).collect(Collectors.toSet()), TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isProfitMetricsDatafeedEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void shouldSetProfitPopulationEnabledCriteria() {
        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.ENABLE_PROFIT_POPULATION)).thenReturn(Boolean.TRUE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.DAILY).collect(Collectors.toSet()), TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isProfitPopulationEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void shouldSetSupplementsEnabledCriteria() throws Exception {
        mockDatafeedCoreBucket();
        CPConfiguration cpConfigurationMock = mock(CPConfiguration.class);
        when(cpConfigurationMock.isEnableSupplements()).thenReturn(Boolean.TRUE);
        when(pricingConfigurationServiceMock.findCPConfiguration()).thenReturn(cpConfigurationMock);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isSupplementsEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void shouldSetSupplementsDisabledCriteriaWhenSupplementsConfigurationIsNotAvailable() throws Exception {
        mockDatafeedCoreBucket();
        when(pricingConfigurationServiceMock.findCPConfiguration()).thenReturn(null);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isSupplementsEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void shouldSetSupplementsDisabledCriteria() throws Exception {
        mockDatafeedCoreBucket();
        CPConfiguration cpConfigurationMock = mock(CPConfiguration.class);
        when(cpConfigurationMock.isEnableSupplements()).thenReturn(Boolean.FALSE);
        when(pricingConfigurationServiceMock.findCPConfiguration()).thenReturn(cpConfigurationMock);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isSupplementsEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void shouldSetSystemHealthEnabledCriteria() throws Exception {
        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(FeatureTogglesConfigParamName.SHOW_SYSTEM_HEALTH_FOR_EXTERNAL_USER.value())).thenReturn(Boolean.TRUE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isSystemHealthEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void shouldSetSystemHealthDisabledCriteria() throws Exception {
        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(FeatureTogglesConfigParamName.SHOW_SYSTEM_HEALTH_FOR_EXTERNAL_USER.value())).thenReturn(Boolean.FALSE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isSystemHealthEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void shouldSetClientLevelEnabledCriteria() throws Exception {
        when(pacmanConfigParamsServiceMock.getParameterValueByClientLevel(IntegrationConfigParamName.DATAFEED_BUCKET.value(), TEST_CLIENT)).thenReturn(Stream.of(EndpointBucket.CORE).map(EndpointBucket::getBucketName).collect(Collectors.joining(",")));
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, TEST_CLIENT, Boolean.TRUE);
        assertThat(datafeedEndPointCriteria.isClientLevelRequest(), Is.is(Boolean.TRUE));
    }

    @Test
    public void shouldSetClientLevelDisabledCriteria() throws Exception {
        mockDatafeedCoreBucket();

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.isClientLevelRequest(), Is.is(Boolean.FALSE));
    }

    @Test
    public void shouldSetClientCodeCriteria() throws Exception {
        mockDatafeedCoreBucket();

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.getClientCode(), IsEqual.equalTo(TEST_CLIENT));
    }

    @Test
    public void shouldSetFrequencyListCriteria() throws Exception {
        mockDatafeedCoreBucket();

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, TEST_CLIENT);

        assertThat(datafeedEndPointCriteria.getFrequencies(), IsCollectionWithSize.hasSize(1));
        assertThat(datafeedEndPointCriteria.getFrequencies().iterator().next(), IsEqual.equalTo(EndpointFrequencyType.DAILY));
    }

    @Test
    public void shouldSetClientBucketCriteriaWhenClientLevelRequestIsTrue() throws Exception {
        final String clientCode = "Hilton";
        final String endpointBuckets = Stream.of(EndpointBucket.CORE, EndpointBucket.SYS_CONFIG).map(EndpointBucket::getBucketName).collect(Collectors.joining(","));
        when(pacmanConfigParamsServiceMock.getParameterValueByClientLevel(IntegrationConfigParamName.DATAFEED_BUCKET.value(), clientCode)).thenReturn(endpointBuckets);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, clientCode, Boolean.TRUE);

        assertTrue(datafeedEndPointCriteria.getClientBuckets().stream().anyMatch(EndpointBucket.CORE::equals));
        assertTrue(datafeedEndPointCriteria.getClientBuckets().stream().anyMatch(EndpointBucket.SYS_CONFIG::equals));

        verify(pacmanConfigParamsServiceMock, times(1)).getParameterValueByClientLevel(IntegrationConfigParamName.DATAFEED_BUCKET.value(), clientCode);
        verify(pacmanConfigParamsServiceMock, times(0)).getParameterValue(IntegrationConfigParamName.DATAFEED_BUCKET.value());
    }

    @Test
    public void shouldSetClientBucketCriteriaWhenClientLevelRequestIsFalse() throws Exception {
        final String clientCode = "Hilton";
        final String endpointBuckets = Stream.of(EndpointBucket.SYS_CONFIG).map(EndpointBucket::getBucketName).collect(Collectors.joining(","));
        when(pacmanConfigParamsServiceMock.getParameterValue(IntegrationConfigParamName.DATAFEED_BUCKET.value())).thenReturn(endpointBuckets);
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED)).thenReturn(Boolean.FALSE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, clientCode);

        assertTrue(datafeedEndPointCriteria.getClientBuckets().stream().anyMatch(EndpointBucket.CORE::equals));
        assertTrue(datafeedEndPointCriteria.getClientBuckets().stream().anyMatch(EndpointBucket.SYS_CONFIG::equals));

        verify(pacmanConfigParamsServiceMock, times(1)).getParameterValue(IntegrationConfigParamName.DATAFEED_BUCKET.value());
        verify(pacmanConfigParamsServiceMock, times(0)).getParameterValueByClientLevel(IntegrationConfigParamName.DATAFEED_BUCKET.value(), clientCode);
    }

    @Test
    public void shouldIncludeCOREBucketByDefault() throws Exception {
        final String clientCode = "Hilton";
        final String endpointBuckets = "";
        when(pacmanConfigParamsServiceMock.getParameterValue(IntegrationConfigParamName.DATAFEED_BUCKET.value())).thenReturn(endpointBuckets);
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.AGILE_RATES_ENABLED)).thenReturn(Boolean.FALSE);

        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, clientCode);

        assertThat(datafeedEndPointCriteria.getClientBuckets(), IsCollectionContaining.hasItem(EndpointBucket.CORE));
    }

    @Test
    public void shouldNotSetOptionalVariablesWhenIsClientRequestTrue() throws Exception {
        when(pacmanConfigParamsServiceMock.getParameterValueByClientLevel(IntegrationConfigParamName.DATAFEED_BUCKET.value(), TEST_CLIENT)).thenReturn(Stream.of(EndpointBucket.CORE).map(EndpointBucket::getBucketName).collect(Collectors.joining(",")));
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(frequencyTypes, TEST_CLIENT, Boolean.TRUE);
        assertThat(datafeedEndPointCriteria.isContinuousPricingEnabled(), Is.is(Boolean.FALSE));
        assertThat(datafeedEndPointCriteria.isSystemHealthEnabled(), Is.is(Boolean.FALSE));
        assertThat(datafeedEndPointCriteria.isSupplementsEnabled(), Is.is(Boolean.FALSE));
        verify(pacmanConfigParamsServiceMock, never()).getBooleanParameterValue(anyString());
        verify(pricingConfigurationServiceMock, never()).findCPConfiguration();
    }

    @Test
    public void verifyDataFeedCriteriaWithProductRateShopDefinitionEnabled() {

        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRODUCT_RATE_SHOP_DEFINITION)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(Boolean.TRUE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isProductRateShopDefinitionEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaWithProductRateShopDefinitionDisabled() {

        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRODUCT_RATE_SHOP_DEFINITION)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(Boolean.TRUE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isProductRateShopDefinitionEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaWithProductRateShopDefinitionEnhancedEnabled() {

        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRODUCT_RATE_SHOP_DEFINITION)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_RDL_RATE_TYPE_COLUMN_TO_PRSD_FILE)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(Boolean.TRUE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isProductRateShopDefinitionEnhancedEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaWithProductRateShopDefinitionEnhancedDisabled() {

        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRODUCT_RATE_SHOP_DEFINITION)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_RDL_RATE_TYPE_COLUMN_TO_PRSD_FILE)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(Boolean.TRUE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isProductRateShopDefinitionEnhancedEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaWithProductClassificationEnabled() {

        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRODUCT_CLASSIFICATION)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(Boolean.TRUE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isProductClassificationEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaWithProductClassificationDisabled() {

        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRODUCT_CLASSIFICATION)).thenReturn(Boolean.FALSE);
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(GUIConfigParamName.IS_CONTINUOUS_PRICING_ENABLED)).thenReturn(Boolean.TRUE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isProductClassificationEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaWithBenefitMeasurementEnabled() {

        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.BENEFIT_MEASUREMENT_DATAFEED_ENABLED)).thenReturn(Boolean.TRUE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isBenefitMeasurementEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaWithBenefitMeasurementDisabled() {

        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.BENEFIT_MEASUREMENT_DATAFEED_ENABLED)).thenReturn(Boolean.FALSE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isBenefitMeasurementEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaWithBenefitMeasurementWithProfitEnabled() {

        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PROFIT_COLUMNS_TO_BM_FILE)).thenReturn(Boolean.TRUE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isBenefitMeasurementWithProfitColumnsEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaWithBenefitMeasurementWithProfitDisabled() {

        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PROFIT_COLUMNS_TO_BM_FILE)).thenReturn(Boolean.FALSE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isBenefitMeasurementWithProfitColumnsEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaWithScheduledReportsEnabled() {

        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.SCHEDULED_REPORTS_DATAFEED_ENABLED)).thenReturn(Boolean.TRUE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.WEEKLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isScheduledReportsEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaWithScheduledReportsDisabled() {

        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.SCHEDULED_REPORTS_DATAFEED_ENABLED)).thenReturn(Boolean.FALSE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.WEEKLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isScheduledReportsEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaWithChannelForecastEnabled() {

        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.IS_CHANNEL_FORECAST_DATAFEED_ENABLED)).thenReturn(Boolean.TRUE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.DAILY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isChannelForecastEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaWithChannelForecastDisabled() {

        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.IS_CHANNEL_FORECAST_DATAFEED_ENABLED)).thenReturn(Boolean.FALSE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.DAILY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isChannelForecastEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaWithHiltonConsortiaFreeNightEnabled() {

        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_CONSORTIA_FREE_NIGHT_ENABLED)).thenReturn(Boolean.TRUE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isHiltonConsortiaFreeNightEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaWithHiltonConsortiaFreeNightDisabled() {

        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(FeatureTogglesConfigParamName.HILTON_CONSORTIA_FREE_NIGHT_ENABLED)).thenReturn(Boolean.FALSE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isHiltonConsortiaFreeNightEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaWithProductFreeNightDefinitionEnabled() {

        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRODUCT_FREE_NIGHT_DEFINITION_DATAFEED_FILE)).thenReturn(Boolean.TRUE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isProductFreeNightDefinitionEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaWithProductFreeNightDefinitionDisabled() {

        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRODUCT_FREE_NIGHT_DEFINITION_DATAFEED_FILE)).thenReturn(Boolean.FALSE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isProductFreeNightDefinitionEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaWithProductGroupProductDefinitionEnabled() {
        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.SMALL_GROUP_PRODUCT_ENABLED)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRODUCT_GROUP_PRODUCT_DEFINITION_DATAFEED)).thenReturn(Boolean.TRUE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isProductGroupProductDefinitionEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaWithProductGroupProductDefinitionDisabled() {
        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.SMALL_GROUP_PRODUCT_ENABLED)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRODUCT_GROUP_PRODUCT_DEFINITION_DATAFEED)).thenReturn(Boolean.FALSE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isProductGroupProductDefinitionEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaWithGPMinConfigurationEnabled() {

        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.GP_MIN_PROFIT_CONFIGURATION_DATAFEED_FILES)).thenReturn(Boolean.TRUE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isGroupPricingMinProfitEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaWithGPMinConfigurationDisabled() {

        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getParameterValue(PreProductionConfigParamName.GP_MIN_PROFIT_CONFIGURATION_DATAFEED_FILES)).thenReturn(Boolean.FALSE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isGroupPricingMinProfitEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaWithGroupPricingSCMarketSegmentMappingEnabled() {
        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_SNC_MSRT_MAP_DATAFEED)).thenReturn(Boolean.TRUE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isGroupPricingSCMarketSegmentMappingEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaWithGroupPricingSCMarketSegmentMappingDisabled() {
        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_SNC_MSRT_MAP_DATAFEED)).thenReturn(Boolean.FALSE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isProductGroupProductDefinitionEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    public void verifyDataFeedCriteriaWithGroupPricingSCRoomTypeMappingEnabled() {
        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_SNC_MSRT_MAP_DATAFEED)).thenReturn(Boolean.TRUE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isGroupPricingSCRoomTypeMappingEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    public void verifyDataFeedCriteriaWithGroupPricingSCRoomTypeMappingDisabled() {
        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(IntegrationConfigParamName.SALES_AND_CATERING_EVALUATION_ENABLED)).thenReturn(Boolean.TRUE);
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_SNC_MSRT_MAP_DATAFEED)).thenReturn(Boolean.FALSE);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isGroupPricingSCRoomTypeMappingEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    void verifyDataFeedCriteriaWithRolePermissionWithRankColumnEnabled() {
        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getParameterValueByClientLevel(PreProductionConfigParamName.ENABLE_RANK_COLUMN_TO_ROLE_FILE.getParameterName(), TEST_CLIENT)).thenReturn("true");
        when(pacmanConfigParamsServiceMock.getParameterValueByClientLevel(IntegrationConfigParamName.DATAFEED_BUCKET.value(), TEST_CLIENT)).thenReturn("USER_ACTIVITY");
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.CLIENT_MONTHLY).collect(Collectors.toSet()), TEST_CLIENT, true);
        assertThat(datafeedEndPointCriteria.isRolePermissionWithRankColumnEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    void verifyDataFeedCriteriaWithRolePermissionWithRankColumnDisabled() {
        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getParameterValueByClientLevel(PreProductionConfigParamName.ENABLE_RANK_COLUMN_TO_ROLE_FILE.getParameterName(), TEST_CLIENT)).thenReturn("false");
        when(pacmanConfigParamsServiceMock.getParameterValueByClientLevel(IntegrationConfigParamName.DATAFEED_BUCKET.value(), TEST_CLIENT)).thenReturn("USER_ACTIVITY");
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.CLIENT_MONTHLY).collect(Collectors.toSet()), TEST_CLIENT, true);
        assertThat(datafeedEndPointCriteria.isRolePermissionWithRankColumnEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    void shouldSetRateShoppingOccupancyBasedCMPCCriteria_whenToggleIsEnabled() {
        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.IS_RATE_SHOPPING_OCCUPANCY_BASED_CMPC_DATAFEED_ENABLED)).thenReturn(true);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.CLIENT_MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isRateShoppingOccupancyBasedCMPCEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    void shouldNotSetRateShoppingOccupancyBasedCMPCCriteria_whenToggleIsDisabled() {
        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.IS_RATE_SHOPPING_OCCUPANCY_BASED_CMPC_DATAFEED_ENABLED)).thenReturn(false);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.CLIENT_MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isRateShoppingOccupancyBasedCMPCEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    void shouldSetRateShoppingIgnoreChannelConfigCriteria_whenToggleIsEnabled() {
        mockDatafeedConfigurationBucket();;
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.IS_RATE_SHOPPING_IGNORE_CHANNEL_CONFIG_DATAFEED_ENABLED)).thenReturn(true);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.CLIENT_MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isRateShoppingIgnoreChannelConfigEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    void shouldNotSetRateShoppingIgnoreChannelConfigCriteria_whenToggleIsDisabled() {
        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.IS_RATE_SHOPPING_IGNORE_CHANNEL_CONFIG_DATAFEED_ENABLED)).thenReturn(false);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.CLIENT_MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isRateShoppingIgnoreChannelConfigEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    void shouldSetPropertyCodeInUserReport_whenToggleIsEnabled() {
        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getParameterValueByClientLevel(IntegrationConfigParamName.DATAFEED_BUCKET.value(), TEST_CLIENT)).thenReturn("USER_ACTIVITY");
        when(pacmanConfigParamsServiceMock.getParameterValueByClientLevel(
                PreProductionConfigParamName.IS_PROPERTY_CODE_ENABLED_IN_USER_REPORT_DATAFEED.getParameterName(), TEST_CLIENT)).thenReturn("true");
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.CLIENT_DAILY).collect(Collectors.toSet()), TEST_CLIENT, true);
        assertThat(datafeedEndPointCriteria.isPropertyCodeEnabledInUserReportDataFeed(), Is.is(Boolean.TRUE));
    }

    @Test
    void shouldNotSetPropertyCodeInUserReport_whenToggleIsDisabled() {
        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getParameterValueByClientLevel(IntegrationConfigParamName.DATAFEED_BUCKET.value(), TEST_CLIENT)).thenReturn("USER_ACTIVITY");
        when(pacmanConfigParamsServiceMock.getParameterValueByClientLevel(
                PreProductionConfigParamName.IS_PROPERTY_CODE_ENABLED_IN_USER_REPORT_DATAFEED.getParameterName(), TEST_CLIENT)).thenReturn("false");
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.CLIENT_DAILY).collect(Collectors.toSet()), TEST_CLIENT, true);
        assertThat(datafeedEndPointCriteria.isPropertyCodeEnabledInUserReportDataFeed(), Is.is(Boolean.FALSE));
    }

    @Test
    void shouldSetChannelColumnInIgnoreCompetitor_whenToggleIsEnabled() {
        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.IS_CHANNEL_ENABLED_IN_IGNORE_COMPETITOR_DATAFEED)).thenReturn(true);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.CLIENT_MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isChannelColumnEnabledInIgnoreCompetitor(), Is.is(Boolean.TRUE));
    }

    @Test
    void shouldNotSetChannelColumnInIgnoreCompetitor_whenToggleIsDisabled() {
        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.IS_CHANNEL_ENABLED_IN_IGNORE_COMPETITOR_DATAFEED)).thenReturn(false);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.CLIENT_MONTHLY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isChannelColumnEnabledInIgnoreCompetitor(), Is.is(Boolean.FALSE));
    }

    @Test
    void shouldSetDiscontinuedRoomTypeColumnInRoomClassConfig_whenToggleIsEnabled() {
        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.IS_DISCONTINUED_ROOM_TYPE_ENABLED_IN_ROOM_CLASS_CONFIG_DATAFEED)).thenReturn(true);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.DAILY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isDiscontinuedRTEnabledInRoomClassConfigDataFeed(), Is.is(Boolean.TRUE));
    }

    @Test
    void shouldNotSetDiscontinuedRoomTypeColumnInRoomClassConfig_whenToggleIsDisabled() {
        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.IS_DISCONTINUED_ROOM_TYPE_ENABLED_IN_ROOM_CLASS_CONFIG_DATAFEED)).thenReturn(false);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.DAILY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isDiscontinuedRTEnabledInRoomClassConfigDataFeed(), Is.is(Boolean.FALSE));
    }

    @Test
    void shouldSetMeetingPackagePricingCriteria_whenTogglesAreEnabled() {
        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.IS_MEETING_PACKAGE_PRICING_DATAFEED_ENABLED)).thenReturn(true);
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.MEETING_PACKAGE_PRICING_ENABLED)).thenReturn(true);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.DAILY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isMeetingPackagePricingDataFeedEnabled(), Is.is(Boolean.TRUE));
    }

    @Test
    void shouldNotSetMeetingPackagePricingCriteria_whenMeetingPackagePricingFeatureAndDatafeedTogglesAreDisabled() {
        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.IS_MEETING_PACKAGE_PRICING_DATAFEED_ENABLED)).thenReturn(false);
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.MEETING_PACKAGE_PRICING_ENABLED)).thenReturn(false);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.DAILY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isMeetingPackagePricingDataFeedEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    void shouldNotSetMeetingPackagePricingCriteria_whenMeetingPackagePricingFeatureEnabled_andDatafeedDisabled() {
        mockDatafeedCoreBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.IS_MEETING_PACKAGE_PRICING_DATAFEED_ENABLED)).thenReturn(false);
        when(pacmanConfigParamsServiceMock.getParameterValue(FeatureTogglesConfigParamName.MEETING_PACKAGE_PRICING_ENABLED)).thenReturn(true);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.DAILY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isMeetingPackagePricingDataFeedEnabled(), Is.is(Boolean.FALSE));
    }

    @Test
    void shouldSetWindowSettingsInInformationalDatafeed_whenToggleIsEnabled() {
        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.IS_WINDOW_SETTINGS_ENABLED_IN_INFORMATIONAL_DATAFEED)).thenReturn(true);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.DAILY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isWindowSettingsEnabledInInformationalDataFeed(), Is.is(Boolean.TRUE));
    }

    @Test
    void shouldNotSetWindowSettingsInInformationalDatafeed_whenToggleIsEnabled() {
        mockDatafeedConfigurationBucket();
        when(pacmanConfigParamsServiceMock.getBooleanParameterValue(PreProductionConfigParamName.IS_WINDOW_SETTINGS_ENABLED_IN_INFORMATIONAL_DATAFEED)).thenReturn(false);
        final DatafeedEndPointCriteria datafeedEndPointCriteria =
                datafeedEndPointCriteriaService.getDatafeedEndPointCriteria(Stream.of(EndpointFrequencyType.DAILY).collect(Collectors.toSet()), TEST_CLIENT);
        assertThat(datafeedEndPointCriteria.isWindowSettingsEnabledInInformationalDataFeed(), Is.is(Boolean.FALSE));
    }

    private void mockDatafeedCoreBucket() {
        when(pacmanConfigParamsServiceMock.getParameterValue(IntegrationConfigParamName.DATAFEED_BUCKET.value())).thenReturn(Stream.of(EndpointBucket.CORE).map(EndpointBucket::getBucketName).collect(Collectors.joining(",")));
    }

    private void mockDatafeedConfigurationBucket() {
        when(pacmanConfigParamsServiceMock.getParameterValue(IntegrationConfigParamName.DATAFEED_BUCKET.value())).thenReturn(Stream.of(EndpointBucket.SYS_CONFIG).map(EndpointBucket::getBucketName).collect(Collectors.joining(",")));
    }

    private void mockDatafeedSystemOverrideBucket() {
        when(pacmanConfigParamsServiceMock.getParameterValue(IntegrationConfigParamName.DATAFEED_BUCKET.value())).thenReturn(Stream.of(EndpointBucket.SYS_OVERRIDE).map(EndpointBucket::getBucketName).collect(Collectors.joining(",")));
    }

    private void mockDatafeedInfoManagerBucket() {
        when(pacmanConfigParamsServiceMock.getParameterValue(IntegrationConfigParamName.DATAFEED_BUCKET.value())).thenReturn(Stream.of(EndpointBucket.INFO_MANAGER).map(EndpointBucket::getBucketName).collect(Collectors.joining(",")));
    }
}
