package com.ideas.tetris.pacman.services.opera;

import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.services.opera.dto.MissingDayInformation;
import com.ideas.tetris.pacman.services.opera.entity.DataLoadMetadata;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static com.ideas.tetris.pacman.common.constants.Constants.NEW_LINE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class OperaCorrectMissingTransactionRawServiceTest extends AbstractG3JupiterTest {

    public static final String FIRST_CORRELATION_ID = "TEST123";
    public static final String SECOND_CORRELATION_ID = "TEST12345";
    public static final String THIRD_CORRELATION_ID = "TEST1234567";
    public static final String FORTH_CORRELATION_ID = "TEST1234567890";
    private OperaCorrectMissingTransactionRawService operaCorrectMissingTransactionRawService = new OperaCorrectMissingTransactionRawService();
    OperaUtilityService operaUtilityService;
    PacmanConfigParamsService pacmanConfigParamsService;
    OperaFilterStageDataService operaFilterStageDataService;

    @BeforeEach
    public void setUp() throws Exception {
        operaCorrectMissingTransactionRawService.crudService = tenantCrudService();
        operaUtilityService = new OperaUtilityService();
        operaUtilityService.crudService = tenantCrudService();
        operaCorrectMissingTransactionRawService.operaUtilityService = operaUtilityService;
        pacmanConfigParamsService = mock(PacmanConfigParamsService.class);
        when(pacmanConfigParamsService.getIntegerParameterValue(IntegrationConfigParamName.OPERA_CORRECT_MISSING_TRANSACTION_NUMBER.value())).thenReturn(10);
        operaCorrectMissingTransactionRawService.configParamsService = pacmanConfigParamsService;
        operaFilterStageDataService = mock(OperaFilterStageDataService.class);
        operaCorrectMissingTransactionRawService.operaFilterStageDataService = operaFilterStageDataService;
    }

    @Test
    public void correctMissingTransactionInRawFromHistory() {
        //GIVEN
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode(), new LocalDate("2008-08-01"));
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-01"));
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-01"));
        DataLoadMetadata dataLoadMetadataForIncomingMetadata_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForCTRANS_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForPTRANS_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode());
        addHistoryIncomingMetadata(dataLoadMetadataForIncomingMetadata_FIRST, "51", "2008-08-01", "365");
        //History
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-01", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-02 00:00:00.0", "2008-08-01 00:00:00.0", "2008-08-04 00:00:00.0", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-03", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-29", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-30", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-31", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        //Raw
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-01", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-02", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-03", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-29", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-30", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-31", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        //SECOND CORRELATION_ID
        addDataLoadMetadata(SECOND_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode(), new LocalDate("2008-08-02"));
        addDataLoadMetadata(SECOND_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-02"));
        addDataLoadMetadata(SECOND_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-02"));
        DataLoadMetadata dataLoadMetadataForIncomingMetadata_SECOND = getDataLoadMetadataForFileType(SECOND_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForCTRANS_SECOND = getDataLoadMetadataForFileType(SECOND_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForPTRANS_SECOND = getDataLoadMetadataForFileType(SECOND_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode());
        addHistoryIncomingMetadata(dataLoadMetadataForIncomingMetadata_SECOND, "51", "2008-08-02", "365");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-08-01", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-03", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-07-29", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-07-31", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        deleteAllRawTransactionData();
        addRawTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-08-01", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-03", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-07-29", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-07-31", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        //WHEN
        String result = operaCorrectMissingTransactionRawService.correctMissingRawTransaction(SECOND_CORRELATION_ID);
        //THEN
        assertResult("2", "10", "2", result);
        List<Object[]> resultCTrans = tenantCrudService().findByNativeQuery("select * from opera.Raw_Transaction where cast(Arrival_DT as date) = '2008-08-01' and cast(Departure_DT as date) = '2008-08-04' order by Transaction_DT");
        assertEquals(3, resultCTrans.size());
        assertEquals("2008-08-01", resultCTrans.get(0)[5]);
        assertEquals("2008-08-02 00:00:00.0", resultCTrans.get(1)[5]);
        assertEquals("2008-08-03", resultCTrans.get(2)[5]);
        List<Object[]> resultPTrans = tenantCrudService().findByNativeQuery("select * from opera.Raw_Transaction where Arrival_DT = '2008-07-29' and Departure_DT = '2008-08-01' order by Transaction_DT");
        assertEquals(3, resultCTrans.size());
        assertEquals("2008-07-29", resultPTrans.get(0)[5]);
        assertEquals("2008-07-30", resultPTrans.get(1)[5]);
        assertEquals("2008-07-31", resultPTrans.get(2)[5]);
    }

    @Test
    public void correctMissingTransactionInRawFromHistoryWhenMissingTransactionDateIsStartDateOfWindow() {
        //GIVEN
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode(), new LocalDate("2008-07-31"));
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode(), new LocalDate("2008-07-31"));
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode(), new LocalDate("2008-07-31"));
        DataLoadMetadata dataLoadMetadataForIncomingMetadata_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForCTRANS_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForPTRANS_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode());
        addHistoryIncomingMetadata(dataLoadMetadataForIncomingMetadata_FIRST, "2", "2008-07-31", "10");
        //History
        addHistoryTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-30", "2008-07-30", "2008-08-02", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-07-31", "2008-07-30", "2008-08-02", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-01", "2008-07-30", "2008-08-02", "***********", "CHECKED IN");
        //Raw
        addRawTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-30", "2008-07-30", "2008-08-02", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-07-31", "2008-07-30", "2008-08-02", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-01", "2008-07-30", "2008-08-02", "***********", "CHECKED IN");
        //SECOND CORRELATION_ID
        addDataLoadMetadata(SECOND_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode(), new LocalDate("2008-08-01"));
        addDataLoadMetadata(SECOND_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-01"));
        addDataLoadMetadata(SECOND_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-01"));
        DataLoadMetadata dataLoadMetadataForIncomingMetadata_SECOND = getDataLoadMetadataForFileType(SECOND_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForCTRANS_SECOND = getDataLoadMetadataForFileType(SECOND_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForPTRANS_SECOND = getDataLoadMetadataForFileType(SECOND_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode());
        addHistoryIncomingMetadata(dataLoadMetadataForIncomingMetadata_SECOND, "2", "2008-08-01", "10");
        //History
        addHistoryTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-07-31", "2008-07-30", "2008-08-02", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-01", "2008-07-30", "2008-08-02", "***********", "CHECKED IN");
        deleteAllRawTransactionData();
        addRawTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-07-31", "2008-07-30", "2008-08-02", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-01", "2008-07-30", "2008-08-02", "***********", "CHECKED IN");
        //WHEN
        String result = operaCorrectMissingTransactionRawService.correctMissingRawTransaction(SECOND_CORRELATION_ID);
        //THEN
        assertResult("1", "10", "1", result);
        List<Object[]> resultCTrans = tenantCrudService().findByNativeQuery("select * from opera.Raw_Transaction where Arrival_DT = '2008-07-30' and Departure_DT = '2008-08-02' order by Transaction_DT");
        assertEquals(3, resultCTrans.size());
        assertEquals("2008-07-30", resultCTrans.get(0)[5]);
        assertEquals("2008-07-31", resultCTrans.get(1)[5]);
        assertEquals("2008-08-01", resultCTrans.get(2)[5]);
    }

    private void assertResult(String expectedTotalNumberOfMissingTransaction, String expectedOperaCorrectMissingTransactionNumber,
                              String expectedTotalNumberOfMissingTransactionFixed, String result) {
        assertEquals("Total number of missing transactions are : " + expectedTotalNumberOfMissingTransaction + NEW_LINE +
                "OperaCorrectMissingTransactionNumber is : " + expectedOperaCorrectMissingTransactionNumber + NEW_LINE +
                "Total number of missing transactions fixed are : " + expectedTotalNumberOfMissingTransactionFixed, result);
    }

    @Test
    public void correctMissingTransactionInRawFromHistoryWhenMissingTransactionDateIsEndDateOfWindow() {
        //GIVEN
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode(), new LocalDate("2008-08-01"));
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-01"));
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-01"));
        DataLoadMetadata dataLoadMetadataForIncomingMetadata_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForCTRANS_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode());
        addHistoryIncomingMetadata(dataLoadMetadataForIncomingMetadata_FIRST, "2", "2008-08-01", "4");
        //History
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-01", "2008-08-01", "2008-08-05", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-02", "2008-08-01", "2008-08-05", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-03", "2008-08-01", "2008-08-05", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-04", "2008-08-01", "2008-08-05", "***********", "CHECKED IN");
        //Raw
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-01", "2008-08-01", "2008-08-05", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-02", "2008-08-01", "2008-08-05", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-03", "2008-08-01", "2008-08-05", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-04", "2008-08-01", "2008-08-05", "***********", "CHECKED IN");
        //SECOND CORRELATION_ID
        addDataLoadMetadata(SECOND_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode(), new LocalDate("2008-08-02"));
        addDataLoadMetadata(SECOND_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-02"));
        addDataLoadMetadata(SECOND_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-02"));
        DataLoadMetadata dataLoadMetadataForIncomingMetadata_SECOND = getDataLoadMetadataForFileType(SECOND_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForCTRANS_SECOND = getDataLoadMetadataForFileType(SECOND_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForPTRANS_SECOND = getDataLoadMetadataForFileType(SECOND_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode());
        addHistoryIncomingMetadata(dataLoadMetadataForIncomingMetadata_SECOND, "2", "2008-08-02", "3");
        //History
        addHistoryTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-08-01", "2008-08-01", "2008-08-05", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-02", "2008-08-01", "2008-08-05", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-03", "2008-08-01", "2008-08-05", "***********", "CHECKED IN");
        deleteAllRawTransactionData();
        addRawTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-08-01", "2008-08-01", "2008-08-05", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-02", "2008-08-01", "2008-08-05", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-03", "2008-08-01", "2008-08-05", "***********", "CHECKED IN");
        //WHEN
        String result = operaCorrectMissingTransactionRawService.correctMissingRawTransaction(SECOND_CORRELATION_ID);
        //THEN
        assertResult("1", "10", "1", result);
        List<Object[]> resultCTrans = tenantCrudService().findByNativeQuery("select * from opera.Raw_Transaction where Arrival_DT = '2008-08-01' and Departure_DT = '2008-08-05' order by Transaction_DT");
        assertEquals(4, resultCTrans.size());
        assertEquals("2008-08-01", resultCTrans.get(0)[5]);
        assertEquals("2008-08-02", resultCTrans.get(1)[5]);
        assertEquals("2008-08-03", resultCTrans.get(2)[5]);
        assertEquals("2008-08-04", resultCTrans.get(3)[5]);
    }

    @Test
    public void noNeedToCorrectMissingTransactionInRawFromHistoryWhenNoMissingDatesFoundInRawTransaction() {
        //GIVEN
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode(), new LocalDate("2008-08-01"));
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-01"));
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-01"));
        DataLoadMetadata dataLoadMetadataForIncomingMetadata_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForCTRANS_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForPTRANS_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode());
        addIncomingMetadata(dataLoadMetadataForIncomingMetadata_FIRST, "51", "2008-08-01", "365");
        //History
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-01", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-02", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-03", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-29", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-30", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-31", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        //Raw
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-01", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-02", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-03", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-29", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-30", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-31", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        //SECOND CORRELATION_ID
        addDataLoadMetadata(SECOND_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode(), new LocalDate("2008-08-02"));
        addDataLoadMetadata(SECOND_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-02"));
        addDataLoadMetadata(SECOND_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-02"));
        DataLoadMetadata dataLoadMetadataForIncomingMetadata_SECOND = getDataLoadMetadataForFileType(SECOND_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForCTRANS_SECOND = getDataLoadMetadataForFileType(SECOND_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForPTRANS_SECOND = getDataLoadMetadataForFileType(SECOND_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode());
        addIncomingMetadata(dataLoadMetadataForIncomingMetadata_SECOND, "51", "2008-08-02", "365");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-01", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-02", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-03", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-07-29", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-07-30", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-07-31", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        //WHEN
        String result = operaCorrectMissingTransactionRawService.correctMissingRawTransaction(SECOND_CORRELATION_ID);
        //THEN
        assertResult("0", "10", "0", result);
        List<Object[]> resultCTrans = tenantCrudService().findByNativeQuery("select * from opera.Raw_Transaction where Arrival_DT = '2008-08-01' and Departure_DT = '2008-08-04' order by Transaction_DT");
        assertEquals(3, resultCTrans.size());
        assertEquals("2008-08-01", resultCTrans.get(0)[5]);
        assertEquals("2008-08-02", resultCTrans.get(1)[5]);
        assertEquals("2008-08-03", resultCTrans.get(2)[5]);
        List<Object[]> resultPTrans = tenantCrudService().findByNativeQuery("select * from opera.Raw_Transaction where Arrival_DT = '2008-07-29' and Departure_DT = '2008-08-01' order by Transaction_DT");
        assertEquals(3, resultCTrans.size());
        assertEquals("2008-07-29", resultPTrans.get(0)[5]);
        assertEquals("2008-07-30", resultPTrans.get(1)[5]);
        assertEquals("2008-07-31", resultPTrans.get(2)[5]);
    }

    @Test
    public void correctMissingTransactionInRawFromHistoryWhenMissingDatesInCTransAndNotPTrans() {
        //GIVEN
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode(), new LocalDate("2008-08-01"));
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-01"));
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-01"));
        DataLoadMetadata dataLoadMetadataForIncomingMetadata_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForCTRANS_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForPTRANS_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode());
        addIncomingMetadata(dataLoadMetadataForIncomingMetadata_FIRST, "51", "2008-08-01", "365");
        //History
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-01", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-02", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-03", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-29", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-30", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-31", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        //Raw
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-01", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-02", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-03", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-29", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-30", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-31", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        //SECOND CORRELATION_ID
        addDataLoadMetadata(SECOND_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode(), new LocalDate("2008-08-02"));
        addDataLoadMetadata(SECOND_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-02"));
        addDataLoadMetadata(SECOND_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-02"));
        DataLoadMetadata dataLoadMetadataForIncomingMetadata_SECOND = getDataLoadMetadataForFileType(SECOND_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForCTRANS_SECOND = getDataLoadMetadataForFileType(SECOND_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForPTRANS_SECOND = getDataLoadMetadataForFileType(SECOND_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode());
        addIncomingMetadata(dataLoadMetadataForIncomingMetadata_SECOND, "51", "2008-08-02", "365");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-01", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-03", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-07-29", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-07-30", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-07-31", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        deleteRawTransactionData("2008-08-02");
        deleteAllRawTransactionData();
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-01", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-03", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-07-29", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-07-30", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-07-31", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        //WHEN
        String result = operaCorrectMissingTransactionRawService.correctMissingRawTransaction(SECOND_CORRELATION_ID);
        //THEN
        assertResult("1", "10", "1", result);
        List<Object[]> resultCTrans = tenantCrudService().findByNativeQuery("select * from opera.Raw_Transaction where Arrival_DT = '2008-08-01' and Departure_DT = '2008-08-04' order by Transaction_DT");
        assertEquals(3, resultCTrans.size());
        assertEquals("2008-08-01", resultCTrans.get(0)[5]);
        assertEquals("2008-08-02", resultCTrans.get(1)[5]);
        assertEquals("2008-08-03", resultCTrans.get(2)[5]);
        List<Object[]> resultPTrans = tenantCrudService().findByNativeQuery("select * from opera.Raw_Transaction where Arrival_DT = '2008-07-29' and Departure_DT = '2008-08-01' order by Transaction_DT");
        assertEquals(3, resultCTrans.size());
        assertEquals("2008-07-29", resultPTrans.get(0)[5]);
        assertEquals("2008-07-30", resultPTrans.get(1)[5]);
        assertEquals("2008-07-31", resultPTrans.get(2)[5]);
    }

    @Test
    public void shouldNotFailWhenMissingDatesAreOutOfIncomingWindow() {
        //GIVEN
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode(), new LocalDate("2008-08-01"));
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-01"));
        DataLoadMetadata dataLoadMetadataForCTRANS_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForIncomingMetadata_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode());
        addIncomingMetadata(dataLoadMetadataForIncomingMetadata_FIRST, "2", "2008-08-01", "10");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-07-30", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-07-31", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-09", "2008-08-09", "2008-08-14", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-10", "2008-08-09", "2008-08-14", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-11", "2008-08-09", "2008-08-14", "***********", "CHECKED IN");

        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-07-30", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-07-31", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-09", "2008-08-09", "2008-08-14", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-10", "2008-08-09", "2008-08-14", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-11", "2008-08-09", "2008-08-14", "***********", "CHECKED IN");
        //WHEN
        String result = operaCorrectMissingTransactionRawService.correctMissingRawTransaction(FIRST_CORRELATION_ID);
        //THEN
        assertResult("0", "10", "0", result);
        List<Object[]> resultCTrans = tenantCrudService().findByNativeQuery("select * from opera.Raw_Transaction where Arrival_DT = '2008-07-29' and Departure_DT = '2008-08-01' order by Transaction_DT");
        assertEquals(2, resultCTrans.size());
        assertEquals("2008-07-30", resultCTrans.get(0)[5]);
        assertEquals("2008-07-31", resultCTrans.get(1)[5]);
    }

    @Test
    public void shouldFailWhenMissingDatesNotFoundInHistory() {
        //GIVEN
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-01"));
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode(), new LocalDate("2008-08-01"));
        DataLoadMetadata dataLoadMetadataForCTRANS_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForIncomingMetadata_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode());
        addIncomingMetadata(dataLoadMetadataForIncomingMetadata_FIRST, "51", "2008-08-01", "365");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-01", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-03", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-01", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-03", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        //SECOND CORRELATION_ID
        addDataLoadMetadata(SECOND_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-02"));
        addDataLoadMetadata(SECOND_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode(), new LocalDate("2008-08-02"));
        DataLoadMetadata dataLoadMetadataForCTRANS_SECOND = getDataLoadMetadataForFileType(SECOND_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForIncomingMetadata_SECOND = getDataLoadMetadataForFileType(SECOND_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode());
        addIncomingMetadata(dataLoadMetadataForIncomingMetadata_SECOND, "51", "2008-08-02", "365");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-01", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-03", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        deleteAllRawTransactionData();
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-01", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-03", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        //WHEN
        assertThrows(TetrisException.class, () -> operaCorrectMissingTransactionRawService.correctMissingRawTransaction(SECOND_CORRELATION_ID));
    }

    @Test
    public void correctMissingTransactionInRawFromHistoryWhenRunningSecondFeedDuringRollback() {
        //GIVEN
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode(), new LocalDate("2008-08-01"));
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-01"));
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-01"));
        DataLoadMetadata dataLoadMetadataForIncomingMetadata_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForCTRANS_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForPTRANS_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode());
        addHistoryIncomingMetadata(dataLoadMetadataForIncomingMetadata_FIRST, "51", "2008-08-01", "365");
        //History
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-01", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-02", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-03", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-29", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-30", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-31", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        //Raw
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-01", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-02", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-03", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-29", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-30", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-31", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        //SECOND CORRELATION_ID
        addDataLoadMetadata(SECOND_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode(), new LocalDate("2008-08-02"));
        addDataLoadMetadata(SECOND_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-02"));
        addDataLoadMetadata(SECOND_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-02"));
        DataLoadMetadata dataLoadMetadataForIncomingMetadata_SECOND = getDataLoadMetadataForFileType(SECOND_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForCTRANS_SECOND = getDataLoadMetadataForFileType(SECOND_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForPTRANS_SECOND = getDataLoadMetadataForFileType(SECOND_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode());
        addHistoryIncomingMetadata(dataLoadMetadataForIncomingMetadata_SECOND, "51", "2008-08-02", "365");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-08-01", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-03", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-07-29", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-07-31", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        deleteAllRawTransactionData();
        addRawTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-08-01", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-03", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-07-29", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-07-31", "2008-07-29", "2008-08-01", "***********", "CHECKED IN");
        //THIRD CORRELATION_ID
        addDataLoadMetadata(THIRD_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode(), new LocalDate("2008-08-03"));
        addDataLoadMetadata(THIRD_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-03"));
        addDataLoadMetadata(THIRD_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-03"));
        //FORTH CORRELATION_ID
        addDataLoadMetadata(FORTH_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode(), new LocalDate("2008-08-04"));
        addDataLoadMetadata(FORTH_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-04"));
        addDataLoadMetadata(FORTH_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode(), new LocalDate("2008-08-04"));
        //WHEN
        String result = operaCorrectMissingTransactionRawService.correctMissingRawTransaction(SECOND_CORRELATION_ID);
        //THEN
        assertResult("2", "10", "2", result);
        List<Object[]> resultCTrans = tenantCrudService().findByNativeQuery("select * from opera.Raw_Transaction where Arrival_DT = '2008-08-01' and Departure_DT = '2008-08-04' order by Transaction_DT");
        assertEquals(3, resultCTrans.size());
        assertEquals("2008-08-01", resultCTrans.get(0)[5]);
        assertEquals("2008-08-02", resultCTrans.get(1)[5]);
        assertEquals("2008-08-03", resultCTrans.get(2)[5]);
        List<Object[]> resultPTrans = tenantCrudService().findByNativeQuery("select * from opera.Raw_Transaction where Arrival_DT = '2008-07-29' and Departure_DT = '2008-08-01' order by Transaction_DT");
        assertEquals(3, resultCTrans.size());
        assertEquals("2008-07-29", resultPTrans.get(0)[5]);
        assertEquals("2008-07-30", resultPTrans.get(1)[5]);
        assertEquals("2008-07-31", resultPTrans.get(2)[5]);
    }

    private void addIncomingMetadata(DataLoadMetadata dataLoadMetadataForIncomingMetadata_FIRST, String pastDays, final String businessDate, final String futureDays) {
        tenantCrudService().executeUpdateByNativeQuery("insert into opera.History_Incoming_Metadata values(1.00, 1000, 1234-5678, " + pastDays + ", " + futureDays + ", '" + businessDate
                + "', '11:35:00', '" + businessDate + "', '11:35:00', "
                + dataLoadMetadataForIncomingMetadata_FIRST.getId() + ")");
    }

    private void addHistoryIncomingMetadata(DataLoadMetadata dataLoadMetadataForIncomingMetadata_FIRST, String pastDays, final String businessDate, final String futureDays) {
        tenantCrudService().executeUpdateByNativeQuery("insert into opera.History_Incoming_Metadata values(1.00, 1000, 1234-5678, " + pastDays + ", " + futureDays + ", '" + businessDate
                + "', '11:35:00', '" + businessDate + "', '11:35:00', "
                + dataLoadMetadataForIncomingMetadata_FIRST.getId() + ")");
    }

    @Test
    public void shouldFailWhenMissingDatesInRawTransactionIsMoreThanConfigurableParameter() {
        //GIVEN
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode(), LocalDate.now());
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode(), LocalDate.now());
        DataLoadMetadata dataLoadMetadataForCTRANS_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForIncomingMetadata_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode());
        addIncomingMetadata(dataLoadMetadataForIncomingMetadata_FIRST, "51", "2008-08-01", "365");
        //Transaction 1
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-01", "2008-08-01", "2008-08-04", "11122233311", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-02", "2008-08-01", "2008-08-04", "11122233311", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-03", "2008-08-01", "2008-08-04", "11122233311", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-01", "2008-08-01", "2008-08-04", "11122233311", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-02", "2008-08-01", "2008-08-04", "11122233311", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-03", "2008-08-01", "2008-08-04", "11122233311", "CHECKED IN");
        //Transaction 2
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-05", "2008-08-05", "2008-08-08", "11122233322", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-06", "2008-08-05", "2008-08-08", "11122233322", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-07", "2008-08-05", "2008-08-08", "11122233322", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-05", "2008-08-05", "2008-08-08", "11122233322", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-06", "2008-08-05", "2008-08-08", "11122233322", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-07", "2008-08-05", "2008-08-08", "11122233322", "CHECKED IN");
        //Transaction 3
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-06", "2008-08-06", "2008-08-09", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-07", "2008-08-06", "2008-08-09", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-08", "2008-08-06", "2008-08-09", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-06", "2008-08-06", "2008-08-09", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-07", "2008-08-06", "2008-08-09", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-08", "2008-08-06", "2008-08-09", "***********", "CHECKED IN");
        //Transaction 4
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-08", "2008-08-08", "2008-08-11", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-09", "2008-08-08", "2008-08-11", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-10", "2008-08-08", "2008-08-11", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-08", "2008-08-08", "2008-08-11", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-09", "2008-08-08", "2008-08-11", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-10", "2008-08-08", "2008-08-11", "***********", "CHECKED IN");
        //Transaction 5
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-10", "2008-08-10", "2008-08-13", "11122233355", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-11", "2008-08-10", "2008-08-13", "11122233355", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-12", "2008-08-10", "2008-08-13", "11122233355", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-10", "2008-08-10", "2008-08-13", "11122233355", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-11", "2008-08-10", "2008-08-13", "11122233355", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-12", "2008-08-10", "2008-08-13", "11122233355", "CHECKED IN");
        //Transaction 6
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-12", "2008-08-12", "2008-08-15", "11122233366", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-13", "2008-08-12", "2008-08-15", "11122233366", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-14", "2008-08-12", "2008-08-15", "11122233366", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-12", "2008-08-12", "2008-08-15", "11122233366", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-13", "2008-08-12", "2008-08-15", "11122233366", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-14", "2008-08-12", "2008-08-15", "11122233366", "CHECKED IN");
        //Transaction 7
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-14", "2008-08-14", "2008-08-17", "11122233377", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-15", "2008-08-14", "2008-08-17", "11122233377", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-16", "2008-08-14", "2008-08-17", "11122233377", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-14", "2008-08-14", "2008-08-17", "11122233377", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-15", "2008-08-14", "2008-08-17", "11122233377", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-16", "2008-08-14", "2008-08-17", "11122233377", "CHECKED IN");
        //Transaction 8
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-18", "2008-08-18", "2008-08-21", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-19", "2008-08-18", "2008-08-21", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-20", "2008-08-18", "2008-08-21", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-18", "2008-08-18", "2008-08-21", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-19", "2008-08-18", "2008-08-21", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-20", "2008-08-18", "2008-08-21", "***********", "CHECKED IN");
        //Transaction 9
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-20", "2008-08-20", "2008-08-23", "11122233399", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-21", "2008-08-20", "2008-08-23", "11122233399", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-22", "2008-08-20", "2008-08-23", "11122233399", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-20", "2008-08-20", "2008-08-23", "11122233399", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-21", "2008-08-20", "2008-08-23", "11122233399", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-22", "2008-08-20", "2008-08-23", "11122233399", "CHECKED IN");
        //Transaction 10
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-21", "2008-08-21", "2008-08-24", "11122233310", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-22", "2008-08-21", "2008-08-24", "11122233310", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-23", "2008-08-21", "2008-08-24", "11122233310", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-21", "2008-08-21", "2008-08-24", "11122233310", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-22", "2008-08-21", "2008-08-24", "11122233310", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-23", "2008-08-21", "2008-08-24", "11122233310", "CHECKED IN");
        //Transaction 11
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-25", "2008-08-25", "2008-08-28", "11122233312", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-26", "2008-08-25", "2008-08-28", "11122233312", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-27", "2008-08-25", "2008-08-28", "11122233312", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-25", "2008-08-25", "2008-08-28", "11122233312", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-26", "2008-08-25", "2008-08-28", "11122233312", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-27", "2008-08-25", "2008-08-28", "11122233312", "CHECKED IN");
        //SECOND CORRELATION_ID
        addDataLoadMetadata(SECOND_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode(), LocalDate.now());
        addDataLoadMetadata(SECOND_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode(), LocalDate.now());
        DataLoadMetadata dataLoadMetadataForCTRANS_SECOND = getDataLoadMetadataForFileType(SECOND_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForIncomingMetadata_SECOND = getDataLoadMetadataForFileType(SECOND_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode());
        addIncomingMetadata(dataLoadMetadataForIncomingMetadata_SECOND, "51", "2008-08-02", "365");
        //Transaction 1
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-01", "2008-08-01", "2008-08-04", "11122233311", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-03", "2008-08-01", "2008-08-04", "11122233311", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-01", "2008-08-01", "2008-08-04", "11122233311", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-03", "2008-08-01", "2008-08-04", "11122233311", "CHECKED IN");
        deleteRawTransactionData("2008-08-02");
        //Transaction 2
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-05", "2008-08-05", "2008-08-08", "11122233322", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-07", "2008-08-05", "2008-08-08", "11122233322", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-05", "2008-08-05", "2008-08-08", "11122233322", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-07", "2008-08-05", "2008-08-08", "11122233322", "CHECKED IN");
        deleteRawTransactionData("2008-08-06");
        //Transaction 3
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-06", "2008-08-06", "2008-08-09", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-08", "2008-08-06", "2008-08-09", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-06", "2008-08-06", "2008-08-09", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-08", "2008-08-06", "2008-08-09", "***********", "CHECKED IN");
        deleteRawTransactionData("2008-08-07");
        //Transaction 4
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-08", "2008-08-08", "2008-08-11", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-10", "2008-08-08", "2008-08-11", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-08", "2008-08-08", "2008-08-11", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-10", "2008-08-08", "2008-08-11", "***********", "CHECKED IN");
        deleteRawTransactionData("2008-08-09");
        //Transaction 5
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-10", "2008-08-10", "2008-08-13", "11122233355", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-12", "2008-08-10", "2008-08-13", "11122233355", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-10", "2008-08-10", "2008-08-13", "11122233355", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-12", "2008-08-10", "2008-08-13", "11122233355", "CHECKED IN");
        deleteRawTransactionData("2008-08-11");
        //Transaction 6
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-12", "2008-08-12", "2008-08-15", "11122233366", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-14", "2008-08-12", "2008-08-15", "11122233366", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-12", "2008-08-12", "2008-08-15", "11122233366", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-14", "2008-08-12", "2008-08-15", "11122233366", "CHECKED IN");
        deleteRawTransactionData("2008-08-13");
        //Transaction 7
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-14", "2008-08-14", "2008-08-17", "11122233377", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-16", "2008-08-14", "2008-08-17", "11122233377", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-14", "2008-08-14", "2008-08-17", "11122233377", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-16", "2008-08-14", "2008-08-17", "11122233377", "CHECKED IN");
        deleteRawTransactionData("2008-08-15");
        //Transaction 8
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-18", "2008-08-18", "2008-08-21", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-20", "2008-08-18", "2008-08-21", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-18", "2008-08-18", "2008-08-21", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-20", "2008-08-18", "2008-08-21", "***********", "CHECKED IN");
        deleteRawTransactionData("2008-08-19");
        //Transaction 9
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-20", "2008-08-20", "2008-08-23", "11122233399", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-22", "2008-08-20", "2008-08-23", "11122233399", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-20", "2008-08-20", "2008-08-23", "11122233399", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-22", "2008-08-20", "2008-08-23", "11122233399", "CHECKED IN");
        deleteRawTransactionData("2008-08-21");
        //Transaction 10
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-21", "2008-08-21", "2008-08-24", "11122233310", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-23", "2008-08-21", "2008-08-24", "11122233310", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-21", "2008-08-21", "2008-08-24", "11122233310", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-23", "2008-08-21", "2008-08-24", "11122233310", "CHECKED IN");
        deleteRawTransactionData("2008-08-22");
        //Transaction 11
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-25", "2008-08-25", "2008-08-28", "11122233312", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-27", "2008-08-25", "2008-08-28", "11122233312", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-25", "2008-08-25", "2008-08-28", "11122233312", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-27", "2008-08-25", "2008-08-28", "11122233312", "CHECKED IN");
        deleteRawTransactionData("2008-08-24");
        //WHEN
        assertThrows(Exception.class, () -> operaCorrectMissingTransactionRawService.correctMissingRawTransaction(SECOND_CORRELATION_ID));
        verify(pacmanConfigParamsService).getIntegerParameterValue(IntegrationConfigParamName.OPERA_CORRECT_MISSING_TRANSACTION_NUMBER.value());
    }

    @Test
    public void shouldBeAbleToFindMissingDatesInRawTransactionForTheLatestCorrelationID() {
        //GIVEN
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode(), LocalDate.now());
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode(), LocalDate.now());
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode(), LocalDate.now());
        DataLoadMetadata dataLoadMetadataForCTRANS_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForPTRANS_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForIncomingMetadata_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode());
        addIncomingMetadata(dataLoadMetadataForIncomingMetadata_FIRST, "2", "2008-08-01", "365");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-01", "2008-08-01", "2008-08-05", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-02", "2008-08-01", "2008-08-05", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-31", "2008-07-31", "2008-08-03", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-08-01", "2008-07-31", "2008-08-03", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-01", "2008-08-01", "2008-08-05", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-02", "2008-08-01", "2008-08-05", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-31", "2008-07-31", "2008-08-03", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-08-01", "2008-07-31", "2008-08-03", "***********", "CHECKED IN");
        //SECOND CORRELATION_ID
        addDataLoadMetadata(SECOND_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode(), LocalDate.now());
        addDataLoadMetadata(SECOND_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode(), LocalDate.now());
        addDataLoadMetadata(SECOND_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode(), LocalDate.now());
        DataLoadMetadata dataLoadMetadataForIncomingMetadata_SECOND = getDataLoadMetadataForFileType(SECOND_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForCTRANS_SECOND = getDataLoadMetadataForFileType(SECOND_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForPTRANS_SECOND = getDataLoadMetadataForFileType(SECOND_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode());
        addIncomingMetadata(dataLoadMetadataForIncomingMetadata_SECOND, "2", "2008-08-02", "365");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-02", "2008-08-01", "2008-08-05", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-03", "2008-08-01", "2008-08-05", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-04", "2008-08-01", "2008-08-05", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-07-31", "2008-07-31", "2008-08-03", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-08-01", "2008-07-31", "2008-08-03", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-02", "2008-08-02", "2008-08-06", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-03", "2008-08-02", "2008-08-06", "***********", "CHECKED IN");
        addHistoryTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-04", "2008-08-02", "2008-08-06", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-02", "2008-08-01 00:00:00.0", "2008-08-05 00:00:00.0", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-03", "2008-08-01", "2008-08-05", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-04", "2008-08-01", "2008-08-05", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-07-31", "2008-07-31", "2008-08-03", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_SECOND, "2008-08-01", "2008-07-31", "2008-08-03", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-02", "2008-08-02", "2008-08-06", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-03", "2008-08-02", "2008-08-06", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_SECOND, "2008-08-04", "2008-08-02", "2008-08-06", "***********", "CHECKED IN");
        Date businessDate = operaUtilityService.getBusinessDate(SECOND_CORRELATION_ID);
        Date windowStartDate = operaUtilityService.getMaxPastDate(businessDate, "Past_Days", SECOND_CORRELATION_ID);
        Date windowEndDate = operaUtilityService.getMaxFutureDate(businessDate, "Future_Days", SECOND_CORRELATION_ID);
        //WHEN
        List<MissingDayInformation> missingDatesInRawTransactionInSecondCorrelation = operaCorrectMissingTransactionRawService.findMissingDatesInRawTransaction(SECOND_CORRELATION_ID,
                LocalDate.fromDateFields(windowStartDate), LocalDate.fromDateFields(windowEndDate));
        //THEN
        assertEquals(3, missingDatesInRawTransactionInSecondCorrelation.size());
        assertDateMatch(missingDatesInRawTransactionInSecondCorrelation, LocalDate.parse("2008-08-01"));
        assertDateMatch(missingDatesInRawTransactionInSecondCorrelation, LocalDate.parse("2008-08-02"));
        assertDateMatch(missingDatesInRawTransactionInSecondCorrelation, LocalDate.parse("2008-08-05"));
        //WHEN
        businessDate = operaUtilityService.getBusinessDate(SECOND_CORRELATION_ID);
        windowStartDate = operaUtilityService.getMaxPastDate(businessDate, "Past_Days", SECOND_CORRELATION_ID);
        windowEndDate = operaUtilityService.getMaxFutureDate(businessDate, "Future_Days", SECOND_CORRELATION_ID);
        List<MissingDayInformation> missingDatesInRawTransactionInFirstCorrelation = operaCorrectMissingTransactionRawService.findMissingDatesInRawTransaction(FIRST_CORRELATION_ID,
                LocalDate.fromDateFields(windowStartDate), LocalDate.fromDateFields(windowEndDate));
        //THEN
        assertEquals(3, missingDatesInRawTransactionInFirstCorrelation.size());
        assertDateMatch(missingDatesInRawTransactionInFirstCorrelation, LocalDate.parse("2008-08-02"));
        assertDateMatch(missingDatesInRawTransactionInFirstCorrelation, LocalDate.parse("2008-08-03"));
        assertDateMatch(missingDatesInRawTransactionInFirstCorrelation, LocalDate.parse("2008-08-04"));
    }

    @Test
    public void shouldIgnoreCancelledReservationStatusWhileFindingMissingTransactionInRaw() {
        //GIVEN
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode(), LocalDate.now());
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode(), LocalDate.now());
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode(), LocalDate.now());
        DataLoadMetadata dataLoadMetadataForIncomingMetadata_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForCTRANS_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForPTRANS_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode());
        addHistoryIncomingMetadata(dataLoadMetadataForIncomingMetadata_FIRST, "51", "2008-08-01", "365");
        //Raw
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-01", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-03", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-29", "2008-07-29", "2008-08-03", "***********", "CANCELLED");
        addRawTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-30", "2008-07-29", "2008-08-03", "***********", "CANCELLED");
        addRawTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-31", "2008-07-29", "2008-08-03", "***********", "CANCELLED");
        Date businessDate = operaUtilityService.getBusinessDate(FIRST_CORRELATION_ID);
        Date windowStartDate = operaUtilityService.getMaxPastDate(businessDate, "Past_Days", FIRST_CORRELATION_ID);
        Date windowEndDate = operaUtilityService.getMaxFutureDate(businessDate, "Future_Days", FIRST_CORRELATION_ID);
        //WHEN
        List<MissingDayInformation> missingDatesInRawTransactionInFirstCorrelation = operaCorrectMissingTransactionRawService.findMissingDatesInRawTransaction(FIRST_CORRELATION_ID,
                LocalDate.fromDateFields(windowStartDate), LocalDate.fromDateFields(windowEndDate));
        //THEN
        assertEquals(1, missingDatesInRawTransactionInFirstCorrelation.size());
        assertDateMatch(missingDatesInRawTransactionInFirstCorrelation, LocalDate.parse("2008-08-02"));
    }

    @Test
    public void shouldIgnoreNoShowReservationStatusWhileFindingMissingTransactionInRaw() {
        //GIVEN
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode(), LocalDate.now());
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode(), LocalDate.now());
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode(), LocalDate.now());
        DataLoadMetadata dataLoadMetadataForIncomingMetadata_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForCTRANS_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForPTRANS_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode());
        addHistoryIncomingMetadata(dataLoadMetadataForIncomingMetadata_FIRST, "51", "2008-08-01", "365");
        //Raw
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-01", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-03", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-29", "2008-07-29", "2008-08-03", "***********", "NO SHOW");
        addRawTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-30", "2008-07-29", "2008-08-03", "***********", "NO SHOW");
        addRawTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-31", "2008-07-29", "2008-08-03", "***********", "NO SHOW");
        Date businessDate = operaUtilityService.getBusinessDate(FIRST_CORRELATION_ID);
        Date windowStartDate = operaUtilityService.getMaxPastDate(businessDate, "Past_Days", FIRST_CORRELATION_ID);
        Date windowEndDate = operaUtilityService.getMaxFutureDate(businessDate, "Future_Days", FIRST_CORRELATION_ID);
        //WHEN
        List<MissingDayInformation> missingDatesInRawTransactionInFirstCorrelation = operaCorrectMissingTransactionRawService.findMissingDatesInRawTransaction(FIRST_CORRELATION_ID,
                LocalDate.fromDateFields(windowStartDate), LocalDate.fromDateFields(windowEndDate));
        //THEN
        assertEquals(1, missingDatesInRawTransactionInFirstCorrelation.size());
        assertDateMatch(missingDatesInRawTransactionInFirstCorrelation, LocalDate.parse("2008-08-02"));
    }

    @Test
    public void shouldIgnoreReservationStatusesToFilterParameterWhileFindingMissingTransactionInRaw() {
        //GIVEN
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode(), LocalDate.now());
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode(), LocalDate.now());
        addDataLoadMetadata(FIRST_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode(), LocalDate.now());
        DataLoadMetadata dataLoadMetadataForIncomingMetadata_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.INCOMING_METADATA.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForCTRANS_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.CURRENT_TRANSACTION.getFileTypeCode());
        DataLoadMetadata dataLoadMetadataForPTRANS_FIRST = getDataLoadMetadataForFileType(FIRST_CORRELATION_ID, OperaIncomingFile.PAST_TRANSACTION.getFileTypeCode());
        addHistoryIncomingMetadata(dataLoadMetadataForIncomingMetadata_FIRST, "51", "2008-08-01", "365");
        when(operaFilterStageDataService.getReservationStatusesToFilter()).thenReturn(Arrays.asList("WAITLIST", "PROSPECT"));
        //Raw
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-01", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForCTRANS_FIRST, "2008-08-03", "2008-08-01", "2008-08-04", "***********", "CHECKED IN");
        addRawTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-29", "2008-07-29", "2008-08-03", "***********", "WAITLIST");
        addRawTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-30", "2008-07-29", "2008-08-03", "***********", "PROSPECT");
        addRawTransactionData(dataLoadMetadataForPTRANS_FIRST, "2008-07-31", "2008-07-29", "2008-08-03", "***********", "CANCELLED");
        Date businessDate = operaUtilityService.getBusinessDate(FIRST_CORRELATION_ID);
        Date windowStartDate = operaUtilityService.getMaxPastDate(businessDate, "Past_Days", FIRST_CORRELATION_ID);
        Date windowEndDate = operaUtilityService.getMaxFutureDate(businessDate, "Future_Days", FIRST_CORRELATION_ID);
        //WHEN
        List<MissingDayInformation> missingDatesInRawTransactionInFirstCorrelation = operaCorrectMissingTransactionRawService.findMissingDatesInRawTransaction(FIRST_CORRELATION_ID,
                LocalDate.fromDateFields(windowStartDate), LocalDate.fromDateFields(windowEndDate));
        //THEN
        assertEquals(1, missingDatesInRawTransactionInFirstCorrelation.size());
        assertDateMatch(missingDatesInRawTransactionInFirstCorrelation, LocalDate.parse("2008-08-02"));
        verify(operaFilterStageDataService).getReservationStatusesToFilter();
    }

    private void assertDateMatch(List<MissingDayInformation> missingDates, LocalDate expectedMissingDate) {
        boolean result = missingDates.stream().anyMatch(missingDayInformation ->
                missingDayInformation.getMissingDate().compareTo(expectedMissingDate) == 0
        );
        assertTrue(result, "Expected missing date: " + expectedMissingDate + " not found in actual missing dates.");
    }

    private void deleteRawTransactionData(final String transactionDate) {
        tenantCrudService().executeUpdateByNativeQuery("delete from opera.Raw_Transaction where Transaction_DT = '" + transactionDate + "'");
    }

    private void deleteAllRawTransactionData() {
        tenantCrudService().executeUpdateByNativeQuery("delete from opera.Raw_Transaction");
    }

    private void addDataLoadMetadata(String secondCorrelationId, String fileType, LocalDate createDate) {
        tenantCrudService().executeUpdateByNativeQuery("insert into opera.Data_Load_Metadata VALUES('" + secondCorrelationId + "', '" + fileType + "', '" + createDate + "')");
    }

    private DataLoadMetadata getDataLoadMetadataForFileType(String correlationId, String fileTypeCode) {
        return operaUtilityService.getDataLoadMetadataForFileType(correlationId,
                fileTypeCode);
    }

    private void addHistoryTransactionData(DataLoadMetadata dataLoadMetaDataIdForMetadata, String transactionDate,
                                           final String arrivalDate, final String departureDate, final String reservationId, final String reservationStatus) {
        tenantCrudService().executeUpdateByNativeQuery("insert into opera.History_Transaction values(88888888888, '" + reservationStatus + "', 'N', '', '"
                + transactionDate + "', '" + arrivalDate + "', '" + departureDate + "', '', '', \n" +
                "'2008-07-25', 'CV50', '1350', 'CV', 420, 1000.00, 80.30, 0, 1080.30, 'QR', 'UA', 'GDS', 'QR', '', 'CHECKED IN', 0, 1, " + reservationId + ", "
                + dataLoadMetaDataIdForMetadata.getId() + ", null, null, null)");
    }

    private void addRawTransactionData(DataLoadMetadata dataLoadMetaDataIdForMetadata, String transactionDate,
                                       final String arrivalDate, final String departureDate, final String reservationId, final String reservationStatus) {
        tenantCrudService().executeUpdateByNativeQuery("insert into opera.Raw_Transaction values(88888888888, '" + reservationStatus + "', 'N', '', '"
                + transactionDate + "', '" + arrivalDate + "', '" + departureDate + "', '', '', \n" +
                "'2008-07-25', 'CV50', '1350', 'CV', 420, 1000.00, 80.30, 0, 1080.30, 'QR', 'UA', 'GDS', 'QR', '', 'CHECKED IN', 0, 1, " + reservationId + ", "
                + dataLoadMetaDataIdForMetadata.getId() + ", null, null, null)");
    }

}