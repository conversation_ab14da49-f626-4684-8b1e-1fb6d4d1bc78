package com.ideas.tetris.pacman.services.security;

import com.google.common.collect.Sets;
import com.ideas.cache.redis.configuration.IdeasRedisCacheManager;
import com.ideas.g3.data.TestClient;
import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.infra.tetris.security.AuthGroupExploder;
import com.ideas.infra.tetris.security.ExplosionException;
import com.ideas.infra.tetris.security.LDAPConstants;
import com.ideas.infra.tetris.security.LDAPException;
import com.ideas.infra.tetris.security.domain.LDAPUser;
import com.ideas.infra.tetris.security.domain.Role;
import com.ideas.infra.tetris.security.domain.TetrisPermissionKey;
import com.ideas.infra.tetris.security.jaas.TetrisPrincipal;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.authgroup.services.AuthGroupManagementService;
import com.ideas.tetris.pacman.services.client.service.ClientConfigService;
import com.ideas.tetris.pacman.services.client.service.ClientService;
import com.ideas.tetris.pacman.services.commondaoandenities.global.entity.*;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.dao.UniqueClientAttributeValueCreator;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.dao.UniquePropertyGroupCreator;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.dao.UniquePropertyPropertyGroupCreator;
import com.ideas.tetris.pacman.services.customattributeservice.entity.CustomAttributeSearchCriteria;
import com.ideas.tetris.pacman.services.customattributeservice.entity.SearchJoinCondition;
import com.ideas.tetris.pacman.services.customattributeservice.entity.SearchOperator;
import com.ideas.tetris.pacman.services.customattributeservice.service.CustomAttributeSearchBean;
import com.ideas.tetris.pacman.services.customattributeservice.service.CustomAttributeService;
import com.ideas.tetris.pacman.services.dateservice.ServiceInitializer;
import com.ideas.tetris.pacman.services.fds.uas.UASService;
import com.ideas.tetris.pacman.services.fds.uas.model.UASAuthGroup;
import com.ideas.tetris.pacman.services.fds.uas.model.UASRuleDetails;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.pacman.services.rateshoppingadjustment.service.RateShoppingAdjustmentService;
import com.ideas.tetris.pacman.services.rules.*;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.contextholder.PacmanWorkContextTestHelper;
import com.ideas.tetris.platform.common.contextholder.PlatformWorkContextTestHelper;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.crudservice.RowMapper;
import com.ideas.tetris.platform.common.errorhandling.TetrisException;
import com.ideas.tetris.platform.common.errorhandling.TetrisSecurityException;
import com.ideas.tetris.platform.common.event.publisher.GlobalServicesEventPublisher;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.daoandentities.entity.*;
import com.ideas.tetris.platform.services.globalproperty.service.ClientCache;
import com.ideas.tetris.platform.services.globalproperty.service.ClientPropertyCacheService;
import com.ideas.tetris.platform.services.globalproperty.service.ClientPropertyRelationshipCache;
import com.ideas.tetris.platform.services.globalproperty.service.PropertyCache;
import com.ideas.tetris.platform.services.util.bean.BeanLocator;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.redisson.api.RLocalCachedMap;

import javax.persistence.Query;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.ideas.tetris.platform.common.contextholder.PacmanWorkContextTestHelper.createTetrisPrincipal;
import static com.ideas.tetris.platform.common.contextholder.PacmanWorkContextTestHelper.updateWorkContext;
import static com.ideas.tetris.platform.common.contextholder.PlatformWorkContextTestHelper.*;
import static com.ideas.tetris.platform.common.errorhandling.ErrorCode.UNEXPECTED_ERROR;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.*;

@SuppressWarnings("deprecation")
@MockitoSettings(strictness = Strictness.LENIENT)
public class AuthorizationServiceTest extends AbstractG3JupiterTest {

    public static final Integer USER_ID = 1;
    public static final String clientCode_BSTN = "BSTN";
    public static final String clientCode_HILTON = "Hilton";
    public static final String ACCESS_READ_WRITE = "&access=readWrite";
    private static Property PROPERTY_BLKSTN_PARIS;
    private static Property PROPERTY_HILTON_BOSCO;
    private static PropertyGroup PROPERTYGROUP_BLKSTN_ALL;
    @Mock
    UserAuthorizedPropertyCache userAuthorizedPropertyCache;
    @Mock
    RateShoppingAdjustmentService rateShoppingAdjustmentService;
    private String dn;
    private String propertyId;
    private PropertyGroupService propertyGroupService;
    @Mock
    private UserGlobalDBService mockUserGlobalDBService;
    @Mock
    private CustomAttributeSearchBean searcher;
    @Mock
    private PropertyGroupService mockPropertyGroupService;
    @Mock
    private ClientConfigService clientConfigService;
    @Mock
    private CrudService globalCrudService;
    @Mock
    private CustomAttributeService customAttributeService;
    @Mock
    private RulesService rulesService;
    @Mock
    private ClientConfigService mockClientConfigService;
    @Mock
    private PacmanConfigParamsService configService;
    @Mock
    private BeanLocator beanLocator;
    @Mock
    private UserService userService;
    @Mock
    private RoleService roleService;
    @Mock
    private ClientService clientService;
    @Mock
    private PacmanConfigParamsService pacmanConfigParamsService;
    @Mock
    private PacmanConfigParamsService mockConfigService;
    @Mock
    private AuthGroupManagementService authGroupManagementService;
    @Mock
    private UASService uasService;
    private UserSynchronizationService userSynchronizationService;
    private ClientPropertyCacheService clientPropertyCacheService;
    private ClientCache clientCache;
    private PropertyCache propertyCache;
    private ClientPropertyRelationshipCache clientPropertyRelationshipCache;

    @InjectMocks
    @Spy
    private AuthorizationService service;

    @Spy
    private AuthorizationService authService;
    @Mock
    private GlobalServicesEventPublisher globalServicesEventPublisher;
    @Mock
    private AsyncUserService asyncUserService;

    @BeforeEach
    public void setUp() throws Exception {
        authService = new AuthorizationService();
        MockitoAnnotations.initMocks(this);

        service.setCrudService(tenantCrudService());
        service.setGlobalCrudService(globalCrudService());
        dn = "dn=test";
        propertyId = "1";

        if (PROPERTY_BLKSTN_PARIS == null) {
            PROPERTY_BLKSTN_PARIS = globalCrudService().findByNamedQuerySingleResult(Property.BY_ID_WITH_CLIENT_AND_DB_LOC,
                    QueryParameter.with(Property.PARAM_PROPERTY_ID, WC_PROPERTY_ID_PARIS).parameters());
        }
        if (PROPERTY_HILTON_BOSCO == null) {
            PROPERTY_HILTON_BOSCO = globalCrudService().findByNamedQuerySingleResult(Property.BY_ID_WITH_CLIENT_AND_DB_LOC,
                    QueryParameter.with(Property.PARAM_PROPERTY_ID, WC_PROPERTY_ID_HILTON_BOSCO).parameters());
        }
        if (PROPERTYGROUP_BLKSTN_ALL == null) {
            PROPERTYGROUP_BLKSTN_ALL = globalCrudService().find(PropertyGroup.class, WC_PROPERTY_GROUP_ID_BLKSTN_ALL);
        }
        propertyGroupService = new PropertyGroupService();
        propertyGroupService.setGlobalCrudService(globalCrudService());
        service.setPropertyGroupService(propertyGroupService);

        UserGlobalDBService userGlobalDBService = new UserGlobalDBService();
        userGlobalDBService.setGlobalCrudService(globalCrudService());
        service.setUserGlobalDBService(userGlobalDBService);

        service.setUasService(uasService);

        service.setClientConfigService(mockClientConfigService);
        userSynchronizationService = new UserSynchronizationService();
        userSynchronizationService.userSynchronizationAsyncService = new UserSynchronizationAsyncService();
        userSynchronizationService.pacmanConfigParamsService = ServiceInitializer.initConfigParamsService(globalCrudService(), globalServicesEventPublisher);
        userSynchronizationService.globalCrudService = globalCrudService();
        userSynchronizationService.userService = userService;

        updateWorkContext(workContext(), WC_USER_ID_SSO, WC_CLIENT_ID_DUMMY, WC_CLIENT_CODE_TEST, // Needs
                // to
                // be
                // unittest
                // for
                // LDAP
                // to
                // write
                WC_PROPERTY_ID_PUNE, WC_PROPERTY_CODE_PUNE);

        createTetrisPrincipal(WC_DN_REGULAR_USER, WC_CN_REGULAR_USER, WC_USER_ID_SSO, WC_CLIENT_CODE_TEST, false);

        propertyGroupService.setConfigParamService(mockConfigService);

        clientPropertyCacheService = new ClientPropertyCacheService();
        service.setClientPropertyCacheService(clientPropertyCacheService);

        IdeasRedisCacheManager ideasRedisCacheManager = new IdeasRedisCacheManager();
        clientCache = new ClientCache();
        clientCache.setIdeasRedisCacheManager(ideasRedisCacheManager);
        inject(clientCache, "crudService", globalCrudService());
        clientPropertyCacheService.setClientCache(clientCache);

        propertyCache = new PropertyCache();
        propertyCache.setIdeasRedisCacheManager(ideasRedisCacheManager);
        inject(propertyCache, "crudService", globalCrudService());
        clientPropertyCacheService.setPropertyCache(propertyCache);

        clientPropertyRelationshipCache = new ClientPropertyRelationshipCache();
        clientPropertyRelationshipCache.setIdeasRedisCacheManager(ideasRedisCacheManager);
        inject(clientPropertyRelationshipCache, "crudService", globalCrudService());
        clientPropertyCacheService.setClientPropertyRelationshipCache(clientPropertyRelationshipCache);

        clientCache.clear();
        propertyCache.clear();
        clientPropertyRelationshipCache.clear();

        when(pacmanConfigParamsService.getParameterValue(GUIConfigParamName.IS_PROPERTY_READY_FOR_EXTERNAL_USER)).thenReturn(true);
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.getParameterName())).thenReturn("false");
    }

    @Test
    public void deleteAuthorizationGroup() {
        AuthorizationGroup authGroup = new AuthorizationGroup();
        authGroup.setClientId(WC_CLIENT_ID_BLACKSTONE);
        authGroup.setName("The Tyranny of Evil Men");
        authGroup.setCreateDate(new Timestamp(new Date().getTime()));
        authGroup.setDescription("This is a test");
        authGroup.setStatusId(Constants.ACTIVE_STATUS_ID);
        authGroup.setCreatedByUserId(Integer.parseInt(WC_USER_ID_SSO));

        authGroup = globalCrudService().save(authGroup);
        assertNotNull(globalCrudService().find(AuthorizationGroup.class, authGroup.getId()));

        Client client = new Client();
        client.setId(PacmanWorkContextTestHelper.WC_CLIENT_ID_BLACKSTONE);

        when(mockClientConfigService.getClientByCode("unittest")).thenReturn(client);

        service.deleteAuthorizationGroup(authGroup.getId());

        assertNull(globalCrudService().find(AuthorizationGroup.class, authGroup.getId()));
        verify(uasService, never()).deleteAuthGroupInFDS(any());
    }

    @Test
    public void deleteAuthorizationGroup_FDSEnabled() {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.getParameterName())).thenReturn("true");
        AuthorizationGroup authGroup = new AuthorizationGroup();
        authGroup.setClientId(WC_CLIENT_ID_BLACKSTONE);
        authGroup.setName("The Tyranny of Evil Men");
        authGroup.setCreateDate(new Timestamp(new Date().getTime()));
        authGroup.setDescription("This is a test");
        authGroup.setStatusId(Constants.ACTIVE_STATUS_ID);
        authGroup.setCreatedByUserId(Integer.parseInt(WC_USER_ID_SSO));
        authGroup.setUasAuthGroupUuid(UUID.randomUUID().toString());

        authGroup = globalCrudService().save(authGroup);
        assertNotNull(globalCrudService().find(AuthorizationGroup.class, authGroup.getId()));

        Client client = new Client();
        client.setId(PacmanWorkContextTestHelper.WC_CLIENT_ID_BLACKSTONE);

        when(mockClientConfigService.getClientByCode("unittest")).thenReturn(client);

        service.deleteAuthorizationGroup(authGroup.getId());

        assertNull(globalCrudService().find(AuthorizationGroup.class, authGroup.getId()));
        verify(uasService).deleteAuthGroupInFDS(authGroup.getUasAuthGroupUuid());
    }

    @Test
    public void deleteAuthorizationGroupFindsNoClients() throws LDAPException {
        AuthorizationGroup authGroup = new AuthorizationGroup();
        authGroup.setClientId(WC_CLIENT_ID_BLACKSTONE);
        authGroup.setName("The Tyranny of Evil Men");
        authGroup.setCreateDate(new Timestamp(new Date().getTime()));
        authGroup.setDescription("This is a test");
        authGroup.setStatusId(Constants.ACTIVE_STATUS_ID);
        authGroup.setCreatedByUserId(Integer.parseInt(WC_USER_ID_SSO));

        authGroup = globalCrudService().save(authGroup);
        assertNotNull(globalCrudService().find(AuthorizationGroup.class, authGroup.getId()));
        when(clientConfigService.getClientCodes()).thenThrow(new LDAPException("Boom goes the dynamite"));

        Set<LDAPUser> ldapUserSet = new HashSet<LDAPUser>();
        LDAPUser ldapUser = new LDAPUser();
        ldapUser.setDN(WC_DN_REGULAR_USER);
        ldapUserSet.add(ldapUser);

        Client client = new Client();
        client.setId(PacmanWorkContextTestHelper.WC_CLIENT_ID_BLACKSTONE);

        when(mockClientConfigService.getClientByCode("unittest")).thenReturn(client);

        service.deleteAuthorizationGroup(authGroup.getId());
        assertNull(globalCrudService().find(AuthorizationGroup.class, authGroup.getId()));
        verify(uasService, never()).deleteAuthGroupInFDS(any());
    }

    @Test
    public void deleteAuthorizationGroupFindsNoClients_FDSEnabled() throws LDAPException {
        AuthorizationGroup authGroup = new AuthorizationGroup();
        authGroup.setClientId(WC_CLIENT_ID_BLACKSTONE);
        authGroup.setName("The Tyranny of Evil Men");
        authGroup.setCreateDate(new Timestamp(new Date().getTime()));
        authGroup.setDescription("This is a test");
        authGroup.setStatusId(Constants.ACTIVE_STATUS_ID);
        authGroup.setCreatedByUserId(Integer.parseInt(WC_USER_ID_SSO));

        authGroup = globalCrudService().save(authGroup);
        assertNotNull(globalCrudService().find(AuthorizationGroup.class, authGroup.getId()));
        when(clientConfigService.getClientCodes()).thenThrow(new LDAPException("Boom goes the dynamite"));

        Set<LDAPUser> ldapUserSet = new HashSet<LDAPUser>();
        LDAPUser ldapUser = new LDAPUser();
        ldapUser.setDN(WC_DN_REGULAR_USER);
        ldapUserSet.add(ldapUser);

        Client client = new Client();
        client.setId(PacmanWorkContextTestHelper.WC_CLIENT_ID_BLACKSTONE);

        when(mockClientConfigService.getClientByCode("unittest")).thenReturn(client);

        service.deleteAuthorizationGroup(authGroup.getId());
        assertNull(globalCrudService().find(AuthorizationGroup.class, authGroup.getId()));
        verify(uasService, never()).deleteAuthGroupInFDS(any());
    }

    @Test
    public void deleteAuthorizationGroupFindsNoUsers() throws LDAPException {
        AuthorizationGroup authGroup = new AuthorizationGroup();
        authGroup.setClientId(WC_CLIENT_ID_BLACKSTONE);
        authGroup.setName("The Tyranny of Evil Men");
        authGroup.setCreateDate(new Timestamp(new Date().getTime()));
        authGroup.setDescription("This is a test");
        authGroup.setStatusId(Constants.ACTIVE_STATUS_ID);
        authGroup.setCreatedByUserId(Integer.parseInt(WC_USER_ID_SSO));

        authGroup = globalCrudService().save(authGroup);
        assertNotNull(globalCrudService().find(AuthorizationGroup.class, authGroup.getId()));

        List<String> clients = Arrays.asList(PacmanWorkContextTestHelper.WC_CLIENT_CODE_BLACKSTONE);
        when(clientConfigService.getClientCodes()).thenReturn(clients);

        Client client = new Client();
        client.setId(PacmanWorkContextTestHelper.WC_CLIENT_ID_BLACKSTONE);

        when(mockClientConfigService.getClientByCode("unittest")).thenReturn(client);

        service.deleteAuthorizationGroup(authGroup.getId());
        assertNull(globalCrudService().find(AuthorizationGroup.class, authGroup.getId()));
        verify(uasService, never()).deleteAuthGroupInFDS(any());
    }

    @Test
    public void deleteAuthorizationGroup_withCode() {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.getParameterName())).thenReturn("true");
        AuthorizationGroup authGroup = new AuthorizationGroup();
        authGroup.setClientId(WC_CLIENT_ID_BLACKSTONE);
        authGroup.setName("The Tyranny of Evil Men");
        authGroup.setCreateDate(new Timestamp(new Date().getTime()));
        authGroup.setDescription("This is a test");
        authGroup.setStatusId(Constants.ACTIVE_STATUS_ID);
        authGroup.setCreatedByUserId(Integer.parseInt(WC_USER_ID_SSO));
        authGroup.setUasAuthGroupUuid(UUID.randomUUID().toString());

        authGroup = globalCrudService().save(authGroup);
        assertNotNull(globalCrudService().find(AuthorizationGroup.class, authGroup.getId()));

        Client client = new Client();
        client.setId(10);
        client.setCode("Hilton");
        when(mockClientConfigService.getClientByCode(client.getCode())).thenReturn(client);

        service.deleteAuthorizationGroup(authGroup.getId(), client.getCode());

        assertNull(globalCrudService().find(AuthorizationGroup.class, authGroup.getId()));
        verify(uasService, never()).deleteAuthGroupInFDS(authGroup.getUasAuthGroupUuid());
    }

    @Test
    public void deleteAuthorizationGroupFindsNoUsers_withCode() throws LDAPException {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.getParameterName())).thenReturn("true");
        AuthorizationGroup authGroup = new AuthorizationGroup();
        authGroup.setClientId(WC_CLIENT_ID_BLACKSTONE);
        authGroup.setName("The Tyranny of Evil Men");
        authGroup.setCreateDate(new Timestamp(new Date().getTime()));
        authGroup.setDescription("This is a test");
        authGroup.setStatusId(Constants.ACTIVE_STATUS_ID);
        authGroup.setCreatedByUserId(Integer.parseInt(WC_USER_ID_SSO));

        authGroup = globalCrudService().save(authGroup);
        assertNotNull(globalCrudService().find(AuthorizationGroup.class, authGroup.getId()));

        List<String> clients = Arrays.asList(PacmanWorkContextTestHelper.WC_CLIENT_CODE_BLACKSTONE);
        when(clientConfigService.getClientCodes()).thenReturn(clients);

        Client client = new Client();
        client.setId(10);
        client.setCode("Hilton");
        when(mockClientConfigService.getClientByCode(client.getCode())).thenReturn(client);

        service.deleteAuthorizationGroup(authGroup.getId(), client.getCode());
        assertNull(globalCrudService().find(AuthorizationGroup.class, authGroup.getId()));
        verify(uasService, never()).deleteAuthGroupInFDS(any());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void deletePropertyFromAuthorizationGroups() {
        AuthorizationGroup authGroup = new AuthorizationGroup();
        authGroup.setClientId(WC_CLIENT_ID_BLACKSTONE);
        authGroup.setName("The Tyranny of Evil Men");
        authGroup.setCreateDate(new Timestamp(new Date().getTime()));
        authGroup.setDescription("This is a test");
        authGroup.setStatusId(Constants.ACTIVE_STATUS_ID);
        authGroup.setCreatedByUserId(Integer.parseInt(WC_USER_ID_SSO));
        authGroup.setAuthGroupPropertyMappings(new HashSet<AuthorizationGroupPropertyMapping>());
        authGroup = globalCrudService().save(authGroup);
        assertNotNull(globalCrudService().find(AuthorizationGroup.class, authGroup.getId()));

        AuthorizationGroupPropertyMapping authMapping = new AuthorizationGroupPropertyMapping();
        authMapping.setAuthorizationGroup(authGroup);
        Property property = globalCrudService().find(Property.class, PacmanWorkContextTestHelper.WC_PROPERTY_ID_DUMMY);
        authMapping.setPropertyId(property.getId());
        authGroup.getAuthGroupPropertyMappings().add(authMapping);

        Client client = new Client();
        client.setId(PacmanWorkContextTestHelper.WC_CLIENT_ID_BLACKSTONE);

        when(mockClientConfigService.getClientByCode("unittest")).thenReturn(client);

        authGroup = service.persistAuthorizationGroup(authGroup, null);

        List<AuthorizationGroupPropertyMapping> preMappings = globalCrudService().findByNamedQuery(
                AuthorizationGroupPropertyMapping.BY_PROPERTY,
                QueryParameter.with(AuthorizationGroupPropertyMapping.PARAM_PROPERTY_ID, PacmanWorkContextTestHelper.WC_PROPERTY_ID_DUMMY)
                        .parameters());
        service.deletePropertyFromAuthorizationGroups(PacmanWorkContextTestHelper.WC_PROPERTY_ID_DUMMY);
        List<AuthorizationGroupPropertyMapping> postMappings = globalCrudService().findByNamedQuery(
                AuthorizationGroupPropertyMapping.BY_PROPERTY,
                QueryParameter.with(AuthorizationGroupPropertyMapping.PARAM_PROPERTY_ID, PacmanWorkContextTestHelper.WC_PROPERTY_ID_DUMMY)
                        .parameters());

        Assertions.assertTrue(preMappings.size() > postMappings.size());
        verify(uasService, never()).deleteAuthGroupInFDS(any());
    }

    @Test
    public void deletePropertyFromAuthorizationGroups_FDSEnabled() {
        AuthorizationGroup authGroup = new AuthorizationGroup();
        authGroup.setClientId(WC_CLIENT_ID_BLACKSTONE);
        authGroup.setName("The Tyranny of Evil Men");
        authGroup.setCreateDate(new Timestamp(new Date().getTime()));
        authGroup.setDescription("This is a test");
        authGroup.setStatusId(Constants.ACTIVE_STATUS_ID);
        authGroup.setCreatedByUserId(Integer.parseInt(WC_USER_ID_SSO));
        authGroup.setAuthGroupPropertyMappings(new HashSet<AuthorizationGroupPropertyMapping>());
        authGroup = globalCrudService().save(authGroup);
        assertNotNull(globalCrudService().find(AuthorizationGroup.class, authGroup.getId()));

        AuthorizationGroupPropertyMapping authMapping = new AuthorizationGroupPropertyMapping();
        authMapping.setAuthorizationGroup(authGroup);
        Property property = globalCrudService().find(Property.class, PacmanWorkContextTestHelper.WC_PROPERTY_ID_DUMMY);
        authMapping.setPropertyId(property.getId());
        authGroup.getAuthGroupPropertyMappings().add(authMapping);

        Client client = new Client();
        client.setId(PacmanWorkContextTestHelper.WC_CLIENT_ID_BLACKSTONE);

        when(mockClientConfigService.getClientByCode("unittest")).thenReturn(client);

        authGroup = service.persistAuthorizationGroup(authGroup, null);

        List<AuthorizationGroupPropertyMapping> preMappings = globalCrudService().findByNamedQuery(
                AuthorizationGroupPropertyMapping.BY_PROPERTY,
                QueryParameter.with(AuthorizationGroupPropertyMapping.PARAM_PROPERTY_ID, PacmanWorkContextTestHelper.WC_PROPERTY_ID_DUMMY)
                        .parameters());
        service.deletePropertyFromAuthorizationGroups(PacmanWorkContextTestHelper.WC_PROPERTY_ID_DUMMY);
        List<AuthorizationGroupPropertyMapping> postMappings = globalCrudService().findByNamedQuery(
                AuthorizationGroupPropertyMapping.BY_PROPERTY,
                QueryParameter.with(AuthorizationGroupPropertyMapping.PARAM_PROPERTY_ID, PacmanWorkContextTestHelper.WC_PROPERTY_ID_DUMMY)
                        .parameters());

        Assertions.assertTrue(preMappings.size() > postMappings.size());
        verify(uasService, never()).deleteAuthGroupInFDS(any());
    }

    @Test
    public void explodeAuthGroups_validGroup_hasProperties() throws ExplosionException {
        assertTrue(service.explodeAuthGroup(1).size() > 0, "Has properties");
    }

    @Test
    public void explodeAuthGroups_allPropertiesGroup_hasProperties() throws ExplosionException {
        assertTrue(service.explodeAuthGroup(AuthorizationGroup.ALL_PROP_ID).size() > 0, "Has properties");
    }

    @Test
    public void explodeAuthGroups_allPropertiesGroup_hasPropertiesWithNewCache() throws ExplosionException {
        assertTrue(service.explodeAuthGroup(AuthorizationGroup.ALL_PROP_ID).size() > 0, "Has properties");
    }

    @Test
    public void retrievePropertyByUserForUser1() {
        // could mock out querymanager, OR just pass in a real one
        LDAPUser u1 = new LDAPUser();
        u1.setUserId(1);
        ArrayList<String> roleList = new ArrayList<>();
        roleList.add("roleId=-666 ON GROUP groupId=-666");
        u1.setRoles(roleList);
        when(userService.getById("11403")).thenReturn(u1);
        Set<String> propertySet = new HashSet<String>();
        propertySet.add("5");
        when(roleService.getPropertiesForUser(nullable(String.class), anyObject())).thenReturn(propertySet);

        // should be two properties for user 1
        // the user is determined by the thread local work context which is
        // setup in a base test case

        // Needs to Assume BLKSTN Client
        // workContext.setClientId(WC_CLIENT_ID_BLACKSTONE);
        mockConfigParameterService("Code", TestProperty.H1, 1, clientCode_BSTN);
        List<Property> Property = service.retrieveAuthorizedProperties();
        assertNotNull(Property, "Property shouldn't be null");
        assertTrue(Property.size() > 0, "Should be properties");
    }

    @Test
    public void retrievePropertyByUserForUser1WithNewCache() {
        // could mock out querymanager, OR just pass in a real one
        LDAPUser u1 = new LDAPUser();
        u1.setUserId(1);
        ArrayList<String> roleList = new ArrayList<>();
        roleList.add("roleId=-666 ON GROUP groupId=-666");
        u1.setRoles(roleList);
        when(userService.getById("11403")).thenReturn(u1);
        Set<String> propertySet = new HashSet<String>();
        propertySet.add("5");
        when(roleService.getPropertiesForUser(nullable(String.class), anyObject())).thenReturn(propertySet);

        // should be two properties for user 1
        // the user is determined by the thread local work context which is
        // setup in a base test case

        // Needs to Assume BLKSTN Client
        // workContext.setClientId(WC_CLIENT_ID_BLACKSTONE);
        mockConfigParameterService("Code", TestProperty.H1, 1, clientCode_BSTN);
        List<Property> Property = service.retrieveAuthorizedProperties();
        assertNotNull(Property, "Property shouldn't be null");
        assertTrue(Property.size() > 0, "Should be properties");
    }


    @Test
    public void testRetrievePropertyByUserForInvalidUser() {
        workContext().setUserId(WC_USER_ID_INVALID);

        // should be zero mappings for user -1
        List<Property> properties = service.retrieveAuthorizedProperties();
        assertNotNull(properties, "Property shouldn't be null");
        assertEquals(0, properties.size(), "Should  be zero mapping");
    }

    @Test
    public void shouldRetrieveOnlyActiveProperty() {
        LDAPUser u1 = new LDAPUser();
        u1.setUserId(1);
        when(userService.getById("11403")).thenReturn(u1);

        Property property1 = mock(Property.class);
        when(property1.isActive()).thenReturn(true);

        Property property2 = mock(Property.class);
        when(property2.isActive()).thenReturn(false);

        Property property3 = mock(Property.class);
        when(property3.isActive()).thenReturn(true);

        List<Property> propertyList = Arrays.asList(property1, property2);

        when(service.retrieveAuthorizedProperties()).thenReturn(propertyList);

        List<Property> properties = service.retrieveActiveAuthorizedProperties();
        assertNotNull(properties, "Property shouldn't be null");
        assertTrue(properties.size() > 0, "Should contain properties");
        properties.stream().forEach(property -> assertTrue(property.isActive()));
    }

    @Test
    public void testRetrieveClientByUserForUser1() {
        CrudService mockCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockCrudService);

        // the user is determined by the thread local work context which is
        // setup in a base test case
        when(mockCrudService.find(Client.class, 1)).thenReturn(new Client());

        List<Client> clients = service.retrieveClientByUser();

        Assertions.assertFalse(clients.isEmpty(), "we should have returned a client");
    }

    @Test
    public void testRetrieveClientByUserForUser1WithNewCache() {
        CrudService mockCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockCrudService);

        // the user is determined by the thread local work context which is
        // setup in a base test case
        when(mockCrudService.find(Client.class, 1)).thenReturn(new Client());

        List<Client> clients = service.retrieveClientByUser();

        Assertions.assertFalse(clients.isEmpty(), "we should have returned a client");
    }

    @Test
    public void getPermsForUserAndProperty_configParamDisabled_groupWashNotAuthorized() throws Exception {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.GROUP_WASH_PERMISSION_KEY));

        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertFalse(perms.contains(AuthorizationService.GROUP_WASH_PERMISSION_KEY), "Group wash NOT authorized");
    }


    @Test
    public void getCapacityForPropertyGroup_whenPropertyGroupNull_shouldThrowSecurityException() {
        Exception exception = assertThrows(TetrisSecurityException.class, () -> {
            service.getCapacityForPropertyGroup(null, Collections.emptyList());
        });
        String expectedMessage = "User lacks access to requested property group";
        String actualMessage = exception.getMessage();
        assertTrue(actualMessage.contains(expectedMessage));
    }

    @Test
    public void getPermsForUserAndProperty_configParamEnabled_groupWashAuthorized() throws Exception {

        // We get set with group-wash
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.GROUP_WASH_PERMISSION_KEY));
        // now, enabled!
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_WASH_BY_GROUP_ENABLED.value())).thenReturn(true);
        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertTrue(perms.contains(AuthorizationService.GROUP_WASH_PERMISSION_KEY), "Group wash IS authorized");
    }

    @Test
    public void getPermsForUserAndProperty_configParamDisabled_groupPricingConfigNotAuthorized() throws Exception {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.GROUP_PRICING_CONFIGURATION_PERMISSION_KEY));
        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertFalse(perms.contains(AuthorizationService.GROUP_PRICING_CONFIGURATION_PERMISSION_KEY), "Group pricing config NOT authorized");
    }

    @Test
    public void getPermsForUserAndProperty_configParamEnabled_groupPricingConfigAuthorized() throws Exception {
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.GROUP_PRICING_CONFIGURATION_PERMISSION_KEY));
        // now, enabled!
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED.value())).thenReturn(true);
        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertTrue(perms.contains(AuthorizationService.GROUP_PRICING_CONFIGURATION_PERMISSION_KEY), "Group pricing config IS authorized");
    }

    @Test
    public void getPermsForUserAndProperty_configParamDisabled_groupPricingEvaluationNotAuthorized() throws Exception {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.GROUP_PRICING_EVALUATION_PERMISSION_KEY));
        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertFalse(perms.contains(AuthorizationService.GROUP_PRICING_EVALUATION_PERMISSION_KEY), "Group pricing evaluation NOT authorized");
    }

    @Test
    public void getPermsForUserAndProperty_configParamEnabled_groupPricingEvaluationAuthorized() throws Exception {
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.GROUP_PRICING_EVALUATION_PERMISSION_KEY));
        // now, enabled!
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED.value())).thenReturn(true);
        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertTrue(perms.contains(AuthorizationService.GROUP_PRICING_EVALUATION_PERMISSION_KEY), "Group pricing evaluation IS authorized");
    }
    @Test
    public void getPermsForUserAndProperty_configParamDisabled_saveConfigInReportsNotAuthorized() {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.SAVED_REPORTS_PERMISSION_KEY));
        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertFalse(perms.contains(AuthorizationService.SAVED_REPORTS_PERMISSION_KEY), "Saved Config In Reports NOT authorized");
    }

    @Test
    public void getPermsForUserAndProperty_configParamEnabled_saveConfigInReportsAuthorized() {
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.SAVED_REPORTS_PERMISSION_KEY));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_SAVE_CONFIG_IN_REPORTS.value())).thenReturn(true);
        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertTrue(perms.contains(AuthorizationService.SAVED_REPORTS_PERMISSION_KEY), "Save Config In Reports IS authorized");
    }
    @Test
    public void getPermsForUserAndProperty_configParamDisabled_meetingPackagePricingReportEnabledNotAuthorized() {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.MEETING_PACKAGE_PRICING_REPORT_KEY));
        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertFalse(perms.contains(AuthorizationService.MEETING_PACKAGE_PRICING_REPORT_KEY), "Meeting Package Pricing Reports Is NOT authorized");
    }

    @Test
    public void getPermsForUserAndProperty_configParamEnabled_meetingPackagePricingReportEnabledAuthorized() {
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.MEETING_PACKAGE_PRICING_REPORT_KEY));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.MEETING_PACKAGE_REPORT_ENABLED.value())).thenReturn(true);
        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertTrue(perms.contains(AuthorizationService.MEETING_PACKAGE_PRICING_REPORT_KEY), "Meeting Package Pricing Reports IS authorized");
    }
    @Test
    public void getPermsForUserAndProperty_configParamDisabled_RatePlanConfigurationNotAuthorized() throws Exception {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY));
        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertFalse(perms.contains(AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY), "Rate Plan Configuration feature is OFF for property 1");
    }

    @Test
    public void getPermsForUserAnsProperty_FeatureEnabled_RatePlanConfigurationAuthorised() {
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertTrue(perms.contains(AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY),
                "Rate Plan Configuration feature is turned ON for property 1");
    }

    @Test
    public void shouldNotAccessRatePlanAndGroupWashByGroupfeatures() {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY, AuthorizationService.GROUP_WASH_PERMISSION_KEY));
        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertFalse(perms.contains(AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY),
                "Rate Plan Configuration feature is turned ON for property 1");
        assertFalse(perms.contains(AuthorizationService.GROUP_WASH_PERMISSION_KEY),
                "Group wash NOT authorized");
    }

    @Test
    public void shouldAccessRatePlanAndNotGroupWashByGroupfeatures() {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY, AuthorizationService.GROUP_WASH_PERMISSION_KEY));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertTrue(perms.contains(AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY),
                "Rate Plan Configuration feature is turned ON for property 1");
        assertFalse(perms.contains(AuthorizationService.GROUP_WASH_PERMISSION_KEY),
                "Group wash NOT authorized");
    }

    @Test
    public void shouldNotAccessRatePlanAndAccessGroupWashByGroupfeatures() {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY, AuthorizationService.GROUP_WASH_PERMISSION_KEY));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_WASH_BY_GROUP_ENABLED.value())).thenReturn(true);
        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertFalse(perms.contains(AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY),
                "Rate Plan Configuration feature is turned ON for property 1");
        assertTrue(perms.contains(AuthorizationService.GROUP_WASH_PERMISSION_KEY),
                "Group wash IS authorized");
    }

    @Test
    public void shouldAccessRatePlanAndGroupWashByGroupfeatures() {
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY, AuthorizationService.GROUP_WASH_PERMISSION_KEY));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_WASH_BY_GROUP_ENABLED.value())).thenReturn(true);
        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertTrue(perms.contains(AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY),
                "Rate Plan Configuration feature is turned ON for property 1");
        assertTrue(perms.contains(AuthorizationService.GROUP_WASH_PERMISSION_KEY),
                "Group wash NOT authorized");
    }

    @Test
    public void shouldNotFailIfRatePlanAndGrpWashPermissionIsnotThere() {
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(Sets.newHashSet("abcd"));
        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertTrue(perms.contains("abcd"));
        assertEquals(1, perms.size());
    }

    @Test
    public void shouldAccessInventoryHistoryReportFeature() {
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.INVENTORY_HISTORY_REPORT_PERMISSION_KEY,
                        AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY, AuthorizationService.GROUP_WASH_PERMISSION_KEY));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.INVENTORY_HISTORY_REPORT_ENABLED.value()))
                .thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_WASH_BY_GROUP_ENABLED.value())).thenReturn(true);
        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertTrue(perms.contains(AuthorizationService.INVENTORY_HISTORY_REPORT_PERMISSION_KEY));
        assertEquals(3, perms.size());
    }

    @Test
    public void shouldAccessRatePlanReportFeature() {
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.RATE_PLAN_REPORT_PERMISSION_KEY, AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY,
                        AuthorizationService.GROUP_WASH_PERMISSION_KEY));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ALL_SRPREPORT_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_WASH_BY_GROUP_ENABLED.value())).thenReturn(true);
        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertTrue(perms.contains(AuthorizationService.RATE_PLAN_REPORT_PERMISSION_KEY));
        assertEquals(3, perms.size());
    }

    @Test
    public void shouldNotAccessInventoryHistoryReportFeature() {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.INVENTORY_HISTORY_REPORT_PERMISSION_KEY,
                        AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY, AuthorizationService.GROUP_WASH_PERMISSION_KEY));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_WASH_BY_GROUP_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.INVENTORY_HISTORY_REPORT_ENABLED.value())).thenReturn(
                false);
        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertFalse(perms.contains(AuthorizationService.INVENTORY_HISTORY_REPORT_PERMISSION_KEY));
        assertEquals(2, perms.size());
    }

    @Test
    public void shouldAccessDecisionConfigurationScreen() {
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY,
                        AuthorizationService.DECISION_CONFIGURATION_PERMISSION_KEY));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.DECISION_CONFIGURATION_ENABLED.value())).thenReturn(true);
        Set<String> permsForUserAndProperty = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertTrue(permsForUserAndProperty.contains(AuthorizationService.DECISION_CONFIGURATION_PERMISSION_KEY));
        assertEquals(2, permsForUserAndProperty.size());
    }

    @Test
    public void shouldNotAccessDecisionConfigurationScreen() {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY,
                        AuthorizationService.DECISION_CONFIGURATION_PERMISSION_KEY));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.DECISION_CONFIGURATION_ENABLED.value())).thenReturn(false);
        Set<String> permsForUserAndProperty = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertFalse(permsForUserAndProperty.contains(AuthorizationService.DECISION_CONFIGURATION_PERMISSION_KEY));
        assertEquals(1, permsForUserAndProperty.size());
    }

    @Test
    public void shouldNotAccessClientQuestionnaireScreen() {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.CLIENT_QUESTIONNAIRE_PERMISSION_KEY));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_CLIENT_QUESTIONNAIRE.value())).thenReturn(false);
        Set<String> permsForUserAndProperty = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertFalse(permsForUserAndProperty.contains(AuthorizationService.CLIENT_QUESTIONNAIRE_PERMISSION_KEY));
        assertEquals(0, permsForUserAndProperty.size());
    }

    @Test
    public void shouldAccessClientQuestionnaireScreen() {
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.CLIENT_QUESTIONNAIRE_PERMISSION_KEY));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_CLIENT_QUESTIONNAIRE.value())).thenReturn(true);
        Set<String> permsForUserAndProperty = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertTrue(permsForUserAndProperty.contains(AuthorizationService.CLIENT_QUESTIONNAIRE_PERMISSION_KEY));
        assertEquals(1, permsForUserAndProperty.size());
    }

    @Test
    public void shouldNotAccessServicingCostByLOSConfigurationWhenToggleIsDisabled() {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(Sets.newHashSet(AuthorizationService.SERVICING_COST_BY_LOS_PERMISSION_KEY));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PROFIT_OPTIMIZATION_ENABLED.value())).thenReturn(false);
        Set<String> permsForUserAndProperty = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertFalse(permsForUserAndProperty.contains(AuthorizationService.SERVICING_COST_BY_LOS_PERMISSION_KEY));
        assertEquals(0, permsForUserAndProperty.size());
    }

    @Test
    public void shouldAccessServicingCostByLOSConfigurationWhenToggleIsEnabled() {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(Sets.newHashSet(AuthorizationService.SERVICING_COST_BY_LOS_PERMISSION_KEY));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.PROFIT_OPTIMIZATION_ENABLED.value())).thenReturn(true);
        Set<String> permsForUserAndProperty = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertTrue(permsForUserAndProperty.contains(AuthorizationService.SERVICING_COST_BY_LOS_PERMISSION_KEY));
        assertEquals(1, permsForUserAndProperty.size());
    }

    @Test
    public void shouldNotAccessRatePlanReportFeature() {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.RATE_PLAN_REPORT_PERMISSION_KEY, AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY,
                        AuthorizationService.GROUP_WASH_PERMISSION_KEY));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_WASH_BY_GROUP_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ALL_SRPREPORT_ENABLED.value())).thenReturn(false);
        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertFalse(perms.contains(AuthorizationService.RATE_PLAN_REPORT_PERMISSION_KEY));
        assertEquals(2, perms.size());
    }

    @Test
    public void shouldAccessForecastValidationReportFeature() {
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.FORECAST_VALIDATION_REPORT_PERMISSION_KEY,
                        AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY, AuthorizationService.GROUP_WASH_PERMISSION_KEY));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FORECAST_VALIDATION_REPORT_ENABLED.value())).thenReturn(
                true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_WASH_BY_GROUP_ENABLED.value())).thenReturn(true);
        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertTrue(perms.contains(AuthorizationService.FORECAST_VALIDATION_REPORT_PERMISSION_KEY));
        assertEquals(3, perms.size());
    }

    @Test
    public void shouldNotAccessForecastValidationReportFeature() {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.FORECAST_VALIDATION_REPORT_PERMISSION_KEY,
                        AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY, AuthorizationService.GROUP_WASH_PERMISSION_KEY));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FORECAST_VALIDATION_REPORT_ENABLED.value())).thenReturn(
                false);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_WASH_BY_GROUP_ENABLED.value())).thenReturn(true);
        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertFalse(perms.contains(AuthorizationService.FORECAST_VALIDATION_REPORT_PERMISSION_KEY));
        assertEquals(2, perms.size());
    }

    @Test
    public void shouldAccessScheduleReportPage() {
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.SCHEDULE_REPORT_PERMISSION_KEY));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SCHEDULE_REPORT_ENABLED.value())).thenReturn(true);
        Set<String> permsForUserAndProperty = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertTrue(permsForUserAndProperty.contains(AuthorizationService.SCHEDULE_REPORT_PERMISSION_KEY));
        assertEquals(1, permsForUserAndProperty.size());
    }

    @Test
    public void shouldNotAccessScheduleReportPage() {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.SCHEDULE_REPORT_PERMISSION_KEY));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SCHEDULE_REPORT_ENABLED.value())).thenReturn(false);
        Set<String> permsForUserAndProperty = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertFalse(permsForUserAndProperty.contains(AuthorizationService.SCHEDULE_REPORT_PERMISSION_KEY));
        assertEquals(0, permsForUserAndProperty.size());
    }

    @Test
    public void getPermsForUserAndProperty_MockedRoleWhenEnabled() throws Exception {
        System.setProperty("mock.roles.enabled", "true");

        Role role = new Role();
        role.setPermissions(Collections.singletonList("testPermission"));

        MockRoleTokenStore.setMockRole(role);

        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertEquals(1, perms.size());
        assertEquals("testPermission", perms.iterator().next());
    }

    @Test
    public void shouldAccessInstallationStatusScreen() {
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY,
                        AuthorizationService.INSTALLATION_STATUS_PERMISSION_KEY));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.INSTALLATION_STATUS_ENABLED.value())).thenReturn(true);
        Set<String> permsForUserAndProperty = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertTrue(permsForUserAndProperty.contains(AuthorizationService.INSTALLATION_STATUS_PERMISSION_KEY));
        assertEquals(2, permsForUserAndProperty.size());
    }

    @Test
    public void shouldNotAccessInstallationStatusScreen() {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY,
                        AuthorizationService.INSTALLATION_STATUS_PERMISSION_KEY));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.INSTALLATION_STATUS_ENABLED.value())).thenReturn(false);
        Set<String> permsForUserAndProperty = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertFalse(permsForUserAndProperty.contains(AuthorizationService.INSTALLATION_STATUS_PERMISSION_KEY));
        assertEquals(1, permsForUserAndProperty.size());
    }

    @Test
    public void shouldAccessQualifiedRatePlanConfigurationScreen() {
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY,
                        AuthorizationService.QUALIFIED_RATE_PLAN_PERMISSION_KEY));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        Set<String> permsForUserAndProperty = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertTrue(permsForUserAndProperty.contains(AuthorizationService.QUALIFIED_RATE_PLAN_PERMISSION_KEY));
        assertEquals(2, permsForUserAndProperty.size());
    }

    @Test
    public void shouldNotAccessQualifiedRatePlanConfigurationScreen() {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY,
                        AuthorizationService.QUALIFIED_RATE_PLAN_PERMISSION_KEY));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.INSTALLATION_STATUS_ENABLED.value())).thenReturn(false);
        Set<String> permsForUserAndProperty = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertTrue(permsForUserAndProperty.contains(AuthorizationService.QUALIFIED_RATE_PLAN_PERMISSION_KEY));
        assertEquals(2, permsForUserAndProperty.size());
    }

    /**
     * When i looked in DB, this user had access to properties 6, 9, 10 for
     * Blkstn Property_ID Client_ID Property_Code Property_Name 5 2 H1 Hilton -
     * Pune 6 2 H2 Hilton - Paris 9 2 LONME Hilton London Metropole 10 2 S2
     * Sheraton - Cary 11 2 FAYNC Hampton Inn Fayetteville
     */
    @Test
    public void userHasAccessToProperty_noPrincipal_noAccess() {
        // no principal
        PacmanThreadLocalContextHolder.put(Constants.SSO_USER_PRINCIPAL, null);
        assertFalse(service.userHasAccessToProperty(WC_PROPERTY_ID_PARIS), "Not authorized");
    }

    @Test
    public void userHasAccessToProperty_noPrincipal_noAccessWithNewCache() {
        // no principal
        PacmanThreadLocalContextHolder.put(Constants.SSO_USER_PRINCIPAL, null);
        assertFalse(service.userHasAccessToProperty(WC_PROPERTY_ID_PARIS), "Not authorized");
    }

    @SuppressWarnings("unchecked")
    @Test
    public void userHasAccessToProperty_hasPrincipalAndAuthorizedProperty_hasAccess() {
        // could mock out queryManager.getUserByUid, getPropertiesForUser, etc.,
        // etc OR just pass in querymanager
        LDAPUser u1 = new LDAPUser();
        u1.setUserId(1);
        ArrayList<String> roleList = new ArrayList<>();
        roleList.add("roleId=-666 ON GROUP groupId=-666");
        u1.setRoles(roleList);
        when(userService.getById("11403")).thenReturn(u1);
        Set<String> propertySet = new HashSet<String>();
        propertySet.add("11");
        propertySet.add("5");
        propertySet.add("6");
        propertySet.add("9");
        propertySet.add("10");
        when(roleService.getPropertiesForUser(nullable(String.class), anyObject())).thenReturn(propertySet);

//		when(service.getQueryManager()).thenReturn(TetrisSecurityService.getQueryManager());

        // mock a cache miss on the 1st call. 2nd call a cache hit.
        UserAuthorizedPropertyCache userAuthorizedPropertyCache = new UserAuthorizedPropertyCache();
        RLocalCachedMap authCache = mock(RLocalCachedMap.class);
        IdeasRedisCacheManager ideasRedisCacheManager = ideasRedisCacheManager(authCache);

        when(authCache.getCachedMap()).thenReturn(Map.of()).thenReturn(Map.of("2-11403", Arrays.asList(11, 5, 6, 9, 10)));
        when(authCache.put(("2-11403"), Arrays.asList(11, 5, 6, 9, 10))).thenReturn(Arrays.asList(11, 5, 6, 9, 10));

        userAuthorizedPropertyCache.setIdeasRedisCacheManager(ideasRedisCacheManager);

        service.setUserAuthorizedPropertyCache(userAuthorizedPropertyCache);

        // Needs to Assume BLKSTN Client
        mockConfigParameterService("Code", TestProperty.H1, 1, clientCode_BSTN);
        workContext().setClientId(WC_CLIENT_ID_BLACKSTONE);

        boolean userHasAccessToProperty = service.userHasAccessToProperty(WC_PROPERTY_ID_PARIS);
        assertTrue(userHasAccessToProperty, "Is authorized");

        // validate cache
        userHasAccessToProperty = service.userHasAccessToProperty(WC_PROPERTY_ID_PARIS);
        assertTrue(userHasAccessToProperty, "Is authorized");

        verify(mockConfigService, times(1)).getValue(anyString(), anyString());
        verify(authCache, times(2)).getCachedMap();
        verify(authCache, times(1)).put("2-11403", Arrays.asList(11, 5, 6, 9, 10));
    }

    @SuppressWarnings("unchecked")
    @Test
    public void userHasAccessToProperty_hasPrincipalAndAuthorizedProperty_hasAccessWithNewCache() {
        // could mock out queryManager.getUserByUid, getPropertiesForUser, etc.,
        // etc OR just pass in querymanager
        LDAPUser u1 = new LDAPUser();
        u1.setUserId(1);
        ArrayList<String> roleList = new ArrayList<>();
        roleList.add("roleId=-666 ON GROUP groupId=-666");
        u1.setRoles(roleList);
        when(userService.getById("11403")).thenReturn(u1);
        Set<String> propertySet = new HashSet<String>();
        propertySet.add("11");
        propertySet.add("5");
        propertySet.add("6");
        propertySet.add("9");
        propertySet.add("10");
        when(roleService.getPropertiesForUser(nullable(String.class), anyObject())).thenReturn(propertySet);

        // mock a cache miss on the 1st call. 2nd call a cache hit.
        RLocalCachedMap authCache = mock(RLocalCachedMap.class);
        IdeasRedisCacheManager ideasRedisCacheManager = ideasRedisCacheManager(authCache);
        when(authCache.getCachedMap()).thenReturn(Map.of()).thenReturn(Map.of("2-11403", Arrays.asList(11, 5, 6, 9, 10)));
        when(authCache.put(("2-11403"), Arrays.asList(11, 5, 6, 9, 10))).thenReturn(Arrays.asList(11, 5, 6, 9, 10));

        UserAuthorizedPropertyCache userAuthorizedPropertyCache = new UserAuthorizedPropertyCache();
        userAuthorizedPropertyCache.setIdeasRedisCacheManager(ideasRedisCacheManager);
        userAuthorizedPropertyCache.clear();
        service.setUserAuthorizedPropertyCache(userAuthorizedPropertyCache);

        // Needs to Assume BLKSTN Client
        mockConfigParameterService("Code", TestProperty.H1, 1, clientCode_BSTN);
        workContext().setClientId(WC_CLIENT_ID_BLACKSTONE);

        boolean userHasAccessToProperty = service.userHasAccessToProperty(WC_PROPERTY_ID_PARIS);
        assertTrue(userHasAccessToProperty, "Is authorized");

        // validate cache
        userHasAccessToProperty = service.userHasAccessToProperty(WC_PROPERTY_ID_PARIS);
        assertTrue(userHasAccessToProperty, "Is authorized");

        verify(mockConfigService, times(1)).getValue(anyString(), anyString());
        verify(authCache, times(2)).getCachedMap();
        verify(authCache, times(1)).put("2-11403", Arrays.asList(11, 5, 6, 9, 10));
    }

    @SuppressWarnings("unchecked")
    @Test
    public void userHasAccessToProperty_unauthorizedPropertyId_noAccess() {
        RLocalCachedMap authCache = mock(RLocalCachedMap.class);
        IdeasRedisCacheManager ideasRedisCacheManager = ideasRedisCacheManager(authCache);
        when(authCache.get(WC_CLIENT_ID_HILTON + "-" + WC_USER_ID_REGULAR)).thenReturn(null);

        UserAuthorizedPropertyCache userAuthorizedPropertyCache = new UserAuthorizedPropertyCache();
        userAuthorizedPropertyCache.setIdeasRedisCacheManager(ideasRedisCacheManager);
        service.setUserAuthorizedPropertyCache(userAuthorizedPropertyCache);

        setupWorkContextForRegularUnitTestUser();
        mockConfigService = mock(PacmanConfigParamsService.class);
        when(mockConfigService.getValue("pacman.Hilton", GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value())).thenReturn("Code");
        propertyGroupService.setConfigParamService(mockConfigService);
        service.setPacmanConfigParamsService(mockConfigService);
        boolean userHasAccessToProperty = service.userHasAccessToProperty(WC_PROPERTY_ID_HILTON_BOSCO);

        assertFalse(userHasAccessToProperty, "Not authorized");

    }

    @SuppressWarnings("unchecked")
    @Test
    public void userHasAccessToProperty_unauthorizedPropertyId_noAccessWithNewCache() {
        RLocalCachedMap authCache = mock(RLocalCachedMap.class);
        IdeasRedisCacheManager ideasRedisCacheManager = ideasRedisCacheManager(authCache);

        when(authCache.get(WC_CLIENT_ID_HILTON + "-" + WC_USER_ID_REGULAR)).thenReturn(null);

        UserAuthorizedPropertyCache userAuthorizedPropertyCache = new UserAuthorizedPropertyCache();
        userAuthorizedPropertyCache.setIdeasRedisCacheManager(ideasRedisCacheManager);
        service.setUserAuthorizedPropertyCache(userAuthorizedPropertyCache);

        setupWorkContextForRegularUnitTestUser();
        mockConfigService = mock(PacmanConfigParamsService.class);
        when(mockConfigService.getValue("pacman.Hilton", GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value())).thenReturn("Code");
        propertyGroupService.setConfigParamService(mockConfigService);
        service.setPacmanConfigParamsService(mockConfigService);
        boolean userHasAccessToProperty = service.userHasAccessToProperty(WC_PROPERTY_ID_HILTON_BOSCO);

        assertFalse(userHasAccessToProperty, "Not authorized");

        verify(authCache).getCachedMap();
    }

    @SuppressWarnings("unchecked")
    @Test
    public void userHasAccessToProperty_authorizedProperty_hasAccess() {

        LDAPUser u1 = new LDAPUser();
        u1.setUserId(1);
        ArrayList<String> roleList = new ArrayList<>();
        roleList.add("roleId=-666 ON GROUP groupId=-666");
        u1.setRoles(roleList);
        when(userService.getById("11403")).thenReturn(u1);
        Set<String> propertySet = new HashSet<String>();
        propertySet.add("11");
        propertySet.add("5");
        propertySet.add("6");
        propertySet.add("9");
        propertySet.add("10");
        when(roleService.getPropertiesForUser(nullable(String.class), anyObject())).thenReturn(propertySet);

        RLocalCachedMap authCache = mock(RLocalCachedMap.class);
        IdeasRedisCacheManager ideasRedisCacheManager = ideasRedisCacheManager(authCache);
        when(authCache.get("2-11403")).thenReturn(null);
        when(authCache.put("2-11403", Arrays.asList(11, 5, 6, 9, 10))).thenReturn(Arrays.asList(11, 5, 6, 9, 10));

        UserAuthorizedPropertyCache userAuthorizedPropertyCache = new UserAuthorizedPropertyCache();
        userAuthorizedPropertyCache.setIdeasRedisCacheManager(ideasRedisCacheManager);
        service.setUserAuthorizedPropertyCache(userAuthorizedPropertyCache);

        // Needs to Assume BLKSTN Client
        workContext().setClientId(WC_CLIENT_ID_BLACKSTONE);
        mockConfigParameterService("Code", TestProperty.H1, 1, clientCode_BSTN);
        boolean userHasAccessToProperty = service.userHasAccessToProperty(PROPERTY_BLKSTN_PARIS);

        assertTrue(userHasAccessToProperty, "Is authorized");

        verify(authCache).getCachedMap();
        verify(authCache, times(1)).put("2-11403", Arrays.asList(11, 5, 6, 9, 10));
    }

    @SuppressWarnings("unchecked")
    @Test
    public void userHasAccessToProperty_authorizedProperty_hasAccessWithNewCache() {
        LDAPUser u1 = new LDAPUser();
        u1.setUserId(1);
        ArrayList<String> roleList = new ArrayList<>();
        roleList.add("roleId=-666 ON GROUP groupId=-666");
        u1.setRoles(roleList);
        when(userService.getById("11403")).thenReturn(u1);
        Set<String> propertySet = new HashSet<String>();
        propertySet.add("11");
        propertySet.add("5");
        propertySet.add("6");
        propertySet.add("9");
        propertySet.add("10");
        when(roleService.getPropertiesForUser(nullable(String.class), anyObject())).thenReturn(propertySet);

        RLocalCachedMap authCache = mock(RLocalCachedMap.class);
        IdeasRedisCacheManager ideasRedisCacheManager = ideasRedisCacheManager(authCache);
        when(authCache.get("2-11403")).thenReturn(null);
        when(authCache.put("2-11403", Arrays.asList(11, 5, 6, 9, 10))).thenReturn(Arrays.asList(11, 5, 6, 9, 10));

        UserAuthorizedPropertyCache userAuthorizedPropertyCache = new UserAuthorizedPropertyCache();
        userAuthorizedPropertyCache.setIdeasRedisCacheManager(ideasRedisCacheManager);
        service.setUserAuthorizedPropertyCache(userAuthorizedPropertyCache);

        // Needs to Assume BLKSTN Client
        workContext().setClientId(WC_CLIENT_ID_BLACKSTONE);
        mockConfigParameterService("Code", TestProperty.H1, 1, clientCode_BSTN);
        boolean userHasAccessToProperty = service.userHasAccessToProperty(PROPERTY_BLKSTN_PARIS);

        assertTrue(userHasAccessToProperty, "Is authorized");

        verify(authCache).getCachedMap();
        verify(authCache, times(1)).put("2-11403", Arrays.asList(11, 5, 6, 9, 10));
    }

    @SuppressWarnings("unchecked")
    @Test
    public void userHasAccessToProperty_unauthorizedProperty_noAccess() {
        RLocalCachedMap authCache = mock(RLocalCachedMap.class);
        IdeasRedisCacheManager ideasRedisCacheManager = ideasRedisCacheManager(authCache);
        when(authCache.get(WC_CLIENT_ID_DUMMY + "-" + WC_USER_ID_REGULAR)).thenReturn(null);

        UserAuthorizedPropertyCache userAuthorizedPropertyCache = new UserAuthorizedPropertyCache();
        userAuthorizedPropertyCache.setIdeasRedisCacheManager(ideasRedisCacheManager);
        service.setUserAuthorizedPropertyCache(userAuthorizedPropertyCache);

        mockConfigParameterService("Code", TestProperty.H1, 1, WC_CLIENT_CODE_DUMMY);
        setupWorkContextForRegularUnitTestUser();

        boolean userHasAccessToProperty = service.userHasAccessToProperty(PROPERTY_BLKSTN_PARIS);
        assertFalse(userHasAccessToProperty, "Should not be authorized");

        verify(authCache).getCachedMap();
    }

    @SuppressWarnings("unchecked")
    @Test
    public void userHasAccessToProperty_authorizedPrincipalForProperty_hasAccess() {
        LDAPUser u1 = new LDAPUser();
        u1.setUserId(1);
        ArrayList<String> roleList = new ArrayList<>();
        roleList.add("roleId=f3fe5208-ba42-4091-a23b-277b4a473c9b ON GROUP groupId=-666");
        u1.setRoles(roleList);
        when(userService.getById("16557")).thenReturn(u1);
        Set<String> propertySet = Sets.newHashSet("11", "5", "6", "9", "10");
        when(roleService.getPropertiesForUser(nullable(String.class), anyObject())).thenReturn(propertySet);

        RLocalCachedMap authCache = mock(RLocalCachedMap.class);
        IdeasRedisCacheManager ideasRedisCacheManager = ideasRedisCacheManager(authCache);
        when(authCache.get(WC_CLIENT_ID_BLACKSTONE + "-" + WC_USER_ID_BLACKSTONE_REV_ANALYST)).thenReturn(null).thenReturn(
                Arrays.asList(11, 5, 6, 9, 10));
        when(authCache.put(WC_CLIENT_ID_BLACKSTONE + "-" + WC_USER_ID_BLACKSTONE_REV_ANALYST, Arrays.asList(11, 5, 6, 9, 10))).thenReturn(
                Arrays.asList(11, 5, 6, 9, 10));

        UserAuthorizedPropertyCache userAuthorizedPropertyCache = new UserAuthorizedPropertyCache();
        userAuthorizedPropertyCache.setIdeasRedisCacheManager(ideasRedisCacheManager);
        service.setUserAuthorizedPropertyCache(userAuthorizedPropertyCache);

        mockConfigParameterService("Code", TestProperty.H1, 1, clientCode_BSTN);
        TetrisPrincipal revAnalystPrincipal = setupWorkContextForBlkstnRevenueAnalyst();

        boolean userHasAccessToProperty = service.userHasAccessToProperty(revAnalystPrincipal, PROPERTY_BLKSTN_PARIS);

        assertTrue(userHasAccessToProperty, "Is authorized");

        userHasAccessToProperty = service.userHasAccessToProperty(PROPERTY_BLKSTN_PARIS);
        assertTrue(userHasAccessToProperty, "Is authorized");
    }

    @SuppressWarnings("unchecked")
    @Test
    public void userHasAccessToProperty_authorizedPrincipalForProperty_hasAccessWithNewCache() {
        LDAPUser u1 = new LDAPUser();
        u1.setUserId(1);
        ArrayList<String> roleList = new ArrayList<>();
        roleList.add("roleId=f3fe5208-ba42-4091-a23b-277b4a473c9b ON GROUP groupId=-666");
        u1.setRoles(roleList);
        when(userService.getById("16557")).thenReturn(u1);
        Set<String> propertySet = Sets.newHashSet("11", "5", "6", "9", "10");
        when(roleService.getPropertiesForUser(nullable(String.class), anyObject())).thenReturn(propertySet);

        RLocalCachedMap authCache = mock(RLocalCachedMap.class);
        IdeasRedisCacheManager ideasRedisCacheManager = ideasRedisCacheManager(authCache);
        when(authCache.get(WC_CLIENT_ID_BLACKSTONE + "-" + WC_USER_ID_BLACKSTONE_REV_ANALYST)).thenReturn(null).thenReturn(
                Arrays.asList(11, 5, 6, 9, 10));
        when(authCache.put(WC_CLIENT_ID_BLACKSTONE + "-" + WC_USER_ID_BLACKSTONE_REV_ANALYST, Arrays.asList(11, 5, 6, 9, 10))).thenReturn(
                Arrays.asList(11, 5, 6, 9, 10));

        UserAuthorizedPropertyCache userAuthorizedPropertyCache = new UserAuthorizedPropertyCache();
        userAuthorizedPropertyCache.setIdeasRedisCacheManager(ideasRedisCacheManager);
        service.setUserAuthorizedPropertyCache(userAuthorizedPropertyCache);

        mockConfigParameterService("Code", TestProperty.H1, 1, clientCode_BSTN);
        TetrisPrincipal revAnalystPrincipal = setupWorkContextForBlkstnRevenueAnalyst();

        boolean userHasAccessToProperty = service.userHasAccessToProperty(revAnalystPrincipal, PROPERTY_BLKSTN_PARIS);

        assertTrue(userHasAccessToProperty, "Is authorized");

        userHasAccessToProperty = service.userHasAccessToProperty(PROPERTY_BLKSTN_PARIS);
        assertTrue(userHasAccessToProperty, "Is authorized");
    }

    @Test
    public void userHasAccessToProperty_unauthorizedPrincipalForProperty_throwsSecurityException() {
        assertThrows(TetrisSecurityException.class, () -> {
            setupWorkContextForBlkstnRevenueAnalyst();
            service.userHasAccessToProperty(PacmanThreadLocalContextHolder.getPrincipal(), PROPERTY_HILTON_BOSCO);
        });
    }

    @Test
    public void userHasAccessToPropertyGroup_authorizedPrincipalForPropertyGroup_hasAccess() {
        mockConfigParameterService("Code", TestProperty.H1, 2, clientCode_BSTN);
        boolean userHasAccessToPropertyGroup = service.userHasAccessToPropertyGroup(PacmanThreadLocalContextHolder.getPrincipal(),
                PROPERTYGROUP_BLKSTN_ALL);
        assertTrue(userHasAccessToPropertyGroup, "Is authorized");
    }

    @Test
    public void userHasAccessToPropertyNullPropertyAndPrincipal() {
        Property property = null;
        assertFalse(service.userHasAccessToProperty(property), "Null prop should be not authorized");
        TetrisPrincipal principal = PacmanThreadLocalContextHolder.getPrincipal();
        assertFalse(service.userHasAccessToProperty(principal, property), "Null prop should be not authorized");
        principal = null;
        property = new Property();
        assertFalse(service.userHasAccessToProperty(principal, property), "Null principal should be not authorized");
    }

    @Test
    public void userHasAccessToPropertyGroupNullPropertyAndPrincipal() {
        PropertyGroup propertyGroup = null;
        TetrisPrincipal principal = PacmanThreadLocalContextHolder.getPrincipal();
        assertFalse(service.userHasAccessToPropertyGroup(principal, propertyGroup), "Null prop group should be not authorized");
        principal = null;
        propertyGroup = new PropertyGroup();
        assertFalse(service.userHasAccessToPropertyGroup(principal, propertyGroup), "Null principal should be not authorized");
    }

    @Test
    public void userHasAccessToPropertyNullPrincipal() {
        Property property = null;
        TetrisPrincipal principal = PacmanThreadLocalContextHolder.getPrincipal();
        assertFalse(service.userHasAccessToProperty(principal, property), "Null prop should be not authorized");
        principal = null;
        property = new Property();
        assertFalse(service.userHasAccessToProperty(principal, property), "Null principal should be not authorized");
    }

    @Test
    public void retrieveAllUsers() {
        List<User> users = service.retrieveAllUsers();
        assertNotNull(users);
        assertTrue(users.size() > 0);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void getAllInternalAssignableAuthGroupDetails() {
        CrudService mockCrud = mock(CrudService.class);
        service.setGlobalCrudService(mockCrud);

        Property p1 = new Property();
        p1.setId(1);
        p1.setCode("a");
        Property p2 = new Property();
        p2.setId(2);
        p2.setCode("b");

        AuthorizationGroupPropertyMapping map1 = new AuthorizationGroupPropertyMapping();
        map1.setPropertyId(p1.getId());
        AuthorizationGroupPropertyMapping map2 = new AuthorizationGroupPropertyMapping();
        map2.setPropertyId(p2.getId());

        AuthorizationGroup ag1 = new AuthorizationGroup();
        Set<AuthorizationGroupPropertyMapping> set1 = new HashSet<AuthorizationGroupPropertyMapping>();
        set1.add(map1);
        ag1.setAuthGroupPropertyMappings(set1);

        AuthorizationGroup ag2 = new AuthorizationGroup();
        Set<AuthorizationGroupPropertyMapping> set2 = new HashSet<AuthorizationGroupPropertyMapping>();
        set2.add(map2);
        ag2.setAuthGroupPropertyMappings(set2);

        List listProperty = Arrays.asList(p1, p2);
        List<AuthorizationGroup> allAuthGroups = Arrays.asList(ag1, ag2);
        when(mockCrud.<AuthorizationGroup>findByNamedQuery(AuthorizationGroup.BY_CLIENT_ID, QueryParameter.with("clientId", WC_CLIENT_ID_DUMMY).parameters()))
                .thenReturn(allAuthGroups);
        when(mockCrud.findByNativeQuery(anyString(), anyMap(), any(RowMapper.class))).thenReturn(listProperty);

        Client c1 = new Client();
        c1.setCode("Dummy Client");
        c1.setId(1);
        c1.setStatus(Status.ACTIVE);

        when(mockCrud.find(Client.class, 1)).thenReturn(c1);

        when(mockConfigService.getValue("pacman.Dummy Client", GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value())).thenReturn("Code");
        propertyGroupService.setConfigParamService(mockConfigService);
        service.setPacmanConfigParamsService(mockConfigService);
        List<AuthorizationGroup> all = service.getAllAssignableAuthGroupDetails(true);
        assertEquals(1, all.size());
        assertEquals(1, howManyAuthGroupsAssignableByUser(all));
        AuthorizationGroup allProp = all.get(0);
        assertEquals(allProp.getId(), AuthorizationGroup.ALL_PROP_ID, "Should be the all props auth group");
        assertTrue(allProp.isAssignableByTheCurrentUser());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void getAllNonInternalAssignableAuthGroupDetailsWithAllPropsWithNewCache() throws LDAPException {
        CrudService mockCrud = mock(CrudService.class);
        service.setGlobalCrudService(mockCrud);

        Set<String> userProperties = new HashSet<String>();
        List allProperties = new ArrayList<Property>();
        Property p1 = new Property();
        p1.setId(1);
        p1.setCode("P1");
        p1.setName("Prop 1");
        userProperties.add("1");
        allProperties.add(p1);

        Property p2 = new Property();
        p2.setId(2);
        userProperties.add("2");
        p2.setCode("P2");
        p2.setName("Prop 2");
        allProperties.add(p2);

        Property p5 = new Property();
        p5.setId(5);
        userProperties.add("5");
        p5.setCode("P5");
        p5.setName("Prop 5");
        allProperties.add(p5);

        AuthorizationGroupPropertyMapping map1 = new AuthorizationGroupPropertyMapping();
        map1.setPropertyId(p1.getId());
        AuthorizationGroupPropertyMapping map2 = new AuthorizationGroupPropertyMapping();
        map2.setPropertyId(p2.getId());

        AuthorizationGroup ag1 = new AuthorizationGroup();
        Set<AuthorizationGroupPropertyMapping> set1 = new HashSet<AuthorizationGroupPropertyMapping>();
        set1.add(map1);
        ag1.setAuthGroupPropertyMappings(set1);
        AuthorizationGroup ag2 = new AuthorizationGroup();
        Set<AuthorizationGroupPropertyMapping> set2 = new HashSet<AuthorizationGroupPropertyMapping>();
        set2.add(map2);
        ag2.setAuthGroupPropertyMappings(set2);

        List allAuthGroups = Arrays.asList(ag1, ag2);

        when(mockCrud.findByNativeQuery(anyString(), anyMap(), any(RowMapper.class))).thenReturn(allProperties);
        when(
                mockCrud.findByNamedQuerySingleResult(Property.NUMBER_OF_PROPERTIES_FOR_CLIENT,
                        QueryParameter.with("clientId", WC_CLIENT_ID_DUMMY).parameters())).thenReturn((long) allProperties.size());
        when(mockCrud.findByNamedQuery(AuthorizationGroup.BY_CLIENT_ID, QueryParameter.with("clientId", WC_CLIENT_ID_DUMMY).parameters()))
                .thenReturn(allAuthGroups);

        when(roleService.getPropertiesForUser(anyString(), any(AuthGroupExploder.class))).thenReturn(userProperties);
        LDAPUser ldapUser = new LDAPUser();
        ldapUser.setDN(WC_DN_REGULAR_USER);
        when(userService.getById(anyString())).thenReturn(ldapUser);
        Client c1 = new Client();
        c1.setCode("Dummy Client");
        c1.setId(1);
        when(mockCrud.find(Client.class, 1)).thenReturn(c1);
        mockConfigService = mock(PacmanConfigParamsService.class);
        when(mockConfigService.getValue("pacman.Dummy Client", GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value())).thenReturn("Code");
        service.setPacmanConfigParamsService(mockConfigService);

        AuthorizationService.PropertyAuthGroupExploder propertyAuthGroupExploder = service.new PropertyAuthGroupExploder(c1.getId());

        when(service.createPropertyAuthGroupExploder(c1.getId())).thenReturn(propertyAuthGroupExploder);
        clientCache.put(c1.getId(), c1);
        List<Integer> items = (List<Integer>) allProperties.stream()
                .map(item -> ((Property) item).getId()).collect(Collectors.toList());
        clientPropertyRelationshipCache.put(c1.getId(), items);
        allProperties.forEach(property -> propertyCache.put(((Property) property).getId(), ((Property) property)));

        List<AuthorizationGroup> all = service.getAllAssignableAuthGroupDetails(false);
        assertNotNull(all);
        assertEquals(3, all.size());
        assertEquals(3, howManyAuthGroupsAssignableByUser(all));
        AuthorizationGroup group = getAllPropsAuthorizationGroup(all);
        assertNotNull(group, "Should be an all props auth group");
        assertTrue(group.isAssignableByTheCurrentUser());
        assertEquals(1, getAllPropsAuthGroupQuantity(all), "Should only be one all props auth group");
    }

    @SuppressWarnings("unchecked")
    @Test
    public void getAllNonInternalAssignableAuthGroupDetailsWithoutAllProps() throws LDAPException {
        CrudService mockCrud = mock(CrudService.class);
        service.setGlobalCrudService(mockCrud);

        LDAPUser u1 = new LDAPUser();
        u1.setUserId(5003);
        ArrayList<String> roleList = new ArrayList<>();
        roleList.add("roleId=-666 ON GROUP groupId=-666");
        u1.setRoles(roleList);
        when(userService.getById("5003")).thenReturn(u1);
        Set<String> propertySet = new HashSet<String>();
        propertySet.add("1");
        propertySet.add("5");
        propertySet.add("2");

        when(roleService.getPropertiesForUser(anyString(), anyObject())).thenReturn(propertySet);

        Set<String> userProperties = new HashSet<String>();
        List allProperties = new ArrayList<Property>();
        Property p1 = new Property();
        p1.setId(1);
        p1.setCode("1");
        userProperties.add("1");
        allProperties.add(p1);
        Property p2 = new Property();
        p2.setId(2);
        p2.setCode("2");
        // userProperties.add("2");
        allProperties.add(p2);
        Property p5 = new Property();
        p5.setId(5);
        p5.setCode("5");
        userProperties.add("5");
        allProperties.add(p5);

        AuthorizationGroupPropertyMapping map1 = new AuthorizationGroupPropertyMapping();
        map1.setPropertyId(p1.getId());
        AuthorizationGroupPropertyMapping map2 = new AuthorizationGroupPropertyMapping();
        map2.setPropertyId(p2.getId());

        AuthorizationGroup ag1 = new AuthorizationGroup();
        Set<AuthorizationGroupPropertyMapping> set1 = new HashSet<AuthorizationGroupPropertyMapping>();
        set1.add(map1);
        ag1.setAuthGroupPropertyMappings(set1);
        AuthorizationGroup ag2 = new AuthorizationGroup();
        Set<AuthorizationGroupPropertyMapping> set2 = new HashSet<AuthorizationGroupPropertyMapping>();
        set2.add(map2);
        ag2.setAuthGroupPropertyMappings(set2);

        List allAuthGroups = Arrays.asList(ag1, ag2);

        when(
                mockCrud.findByNamedQuerySingleResult(Property.BY_ID_WITH_CLIENT_AND_DB_LOC, QueryParameter.with("propertyId", 1)
                        .parameters())).thenReturn(p1);
        when(
                mockCrud.findByNamedQuerySingleResult(Property.BY_ID_WITH_CLIENT_AND_DB_LOC, QueryParameter.with("propertyId", 5)
                        .parameters())).thenReturn(p5);

        when(mockCrud.findByNativeQuery(anyString(), anyMap(), any(RowMapper.class))).thenReturn(allProperties);
        when(
                mockCrud.findByNamedQuerySingleResult(Property.NUMBER_OF_PROPERTIES_FOR_CLIENT,
                        QueryParameter.with("clientId", WC_CLIENT_ID_DUMMY).parameters())).thenReturn((long) allProperties.size());
        when(mockCrud.findByNamedQuery(AuthorizationGroup.BY_CLIENT_ID, QueryParameter.with("clientId", WC_CLIENT_ID_DUMMY).parameters()))
                .thenReturn(allAuthGroups);
        when(roleService.getPropertiesForUser(anyString(), any(AuthGroupExploder.class))).thenReturn(userProperties);
        LDAPUser ldapUser = new LDAPUser();
        ldapUser.setDN(WC_DN_REGULAR_USER);
        when(userService.getById(anyString())).thenReturn(ldapUser);
        Client c1 = new Client();
        c1.setCode("Dummy Client");
        c1.setId(1);
        when(mockCrud.find(Client.class, 1)).thenReturn(c1);

        mockConfigService = mock(PacmanConfigParamsService.class);
        when(mockConfigService.getValue("pacman.Dummy Client", GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value())).thenReturn("Code");
        service.setPacmanConfigParamsService(mockConfigService);

        AuthorizationService.PropertyAuthGroupExploder propertyAuthGroupExploder = new AuthorizationService().new PropertyAuthGroupExploder(c1.getId());

        when(service.createPropertyAuthGroupExploder(c1.getId())).thenReturn(propertyAuthGroupExploder);

        List<AuthorizationGroup> all = service.getAllAssignableAuthGroupDetails(false);

        assertNotNull(all);
        assertEquals(2, all.size());
        assertEquals(1, howManyAuthGroupsAssignableByUser(all));
        AuthorizationGroup group = getAllPropsAuthorizationGroup(all);
        assertNotNull(group, "Should be an all props auth group");
        Assertions.assertFalse(group.isAssignableByTheCurrentUser());
        assertEquals(1, getAllPropsAuthGroupQuantity(all), "Should only be one all props auth group");
    }

    @SuppressWarnings("unchecked")
    @Test
    public void getAllNonInternalAssignableAuthGroupDetailsWithoutAllPropsWithNewCache() throws LDAPException {
        CrudService mockCrud = mock(CrudService.class);
        service.setGlobalCrudService(mockCrud);

        LDAPUser u1 = new LDAPUser();
        u1.setUserId(5003);
        ArrayList<String> roleList = new ArrayList<>();
        roleList.add("roleId=-666 ON GROUP groupId=-666");
        u1.setRoles(roleList);
        when(userService.getById("5003")).thenReturn(u1);
        Set<String> propertySet = new HashSet<String>();
        propertySet.add("1");
        propertySet.add("5");
        propertySet.add("2");

        when(roleService.getPropertiesForUser(anyString(), anyObject())).thenReturn(propertySet);

        Set<String> userProperties = new HashSet<String>();
        List allProperties = new ArrayList<Property>();
        Property p1 = new Property();
        p1.setId(1);
        p1.setCode("1");
        userProperties.add("1");
        allProperties.add(p1);
        Property p2 = new Property();
        p2.setId(2);
        p2.setCode("2");
        // userProperties.add("2");
        allProperties.add(p2);
        Property p5 = new Property();
        p5.setId(5);
        p5.setCode("5");
        userProperties.add("5");
        allProperties.add(p5);

        AuthorizationGroupPropertyMapping map1 = new AuthorizationGroupPropertyMapping();
        map1.setPropertyId(p1.getId());
        AuthorizationGroupPropertyMapping map2 = new AuthorizationGroupPropertyMapping();
        map2.setPropertyId(p2.getId());

        AuthorizationGroup ag1 = new AuthorizationGroup();
        Set<AuthorizationGroupPropertyMapping> set1 = new HashSet<AuthorizationGroupPropertyMapping>();
        set1.add(map1);
        ag1.setAuthGroupPropertyMappings(set1);
        AuthorizationGroup ag2 = new AuthorizationGroup();
        Set<AuthorizationGroupPropertyMapping> set2 = new HashSet<AuthorizationGroupPropertyMapping>();
        set2.add(map2);
        ag2.setAuthGroupPropertyMappings(set2);

        List allAuthGroups = Arrays.asList(ag1, ag2);

        when(
                mockCrud.findByNamedQuerySingleResult(Property.BY_ID_WITH_CLIENT_AND_DB_LOC, QueryParameter.with("propertyId", 1)
                        .parameters())).thenReturn(p1);
        when(
                mockCrud.findByNamedQuerySingleResult(Property.BY_ID_WITH_CLIENT_AND_DB_LOC, QueryParameter.with("propertyId", 5)
                        .parameters())).thenReturn(p5);

        when(mockCrud.findByNativeQuery(anyString(), anyMap(), any(RowMapper.class))).thenReturn(allProperties);
        when(
                mockCrud.findByNamedQuerySingleResult(Property.NUMBER_OF_PROPERTIES_FOR_CLIENT,
                        QueryParameter.with("clientId", WC_CLIENT_ID_DUMMY).parameters())).thenReturn((long) allProperties.size());
        when(mockCrud.findByNamedQuery(AuthorizationGroup.BY_CLIENT_ID, QueryParameter.with("clientId", WC_CLIENT_ID_DUMMY).parameters()))
                .thenReturn(allAuthGroups);
        when(roleService.getPropertiesForUser(anyString(), any(AuthGroupExploder.class))).thenReturn(userProperties);
        LDAPUser ldapUser = new LDAPUser();
        ldapUser.setDN(WC_DN_REGULAR_USER);
        when(userService.getById(anyString())).thenReturn(ldapUser);
        Client c1 = new Client();
        c1.setCode("Dummy Client");
        c1.setId(1);
        when(mockCrud.find(Client.class, 1)).thenReturn(c1);

        mockConfigService = mock(PacmanConfigParamsService.class);
        when(mockConfigService.getValue("pacman.Dummy Client", GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value())).thenReturn("Code");
        service.setPacmanConfigParamsService(mockConfigService);

        AuthorizationService.PropertyAuthGroupExploder propertyAuthGroupExploder = new AuthorizationService().new PropertyAuthGroupExploder(c1.getId());

        when(service.createPropertyAuthGroupExploder(c1.getId())).thenReturn(propertyAuthGroupExploder);

        List<AuthorizationGroup> all = service.getAllAssignableAuthGroupDetails(false);

        assertNotNull(all);
        assertEquals(2, all.size());
        assertEquals(1, howManyAuthGroupsAssignableByUser(all));
        AuthorizationGroup group = getAllPropsAuthorizationGroup(all);
        assertNotNull(group, "Should be an all props auth group");
        Assertions.assertFalse(group.isAssignableByTheCurrentUser());
        assertEquals(1, getAllPropsAuthGroupQuantity(all), "Should only be one all props auth group");
    }

    @SuppressWarnings("unchecked")
    @Test
    public void getAllNonInternalAssignableAuthGroupDetailsWithNoPropAuthGroup() throws LDAPException {
        CrudService mockCrud = mock(CrudService.class);
        service.setGlobalCrudService(mockCrud);

        LDAPUser u1 = new LDAPUser();
        u1.setUserId(1);
        ArrayList<String> roleList = new ArrayList<>();
        roleList.add("roleId=-666 ON GROUP groupId=-666");
        u1.setRoles(roleList);
        when(userService.getById("11403")).thenReturn(u1);
        Set<String> propertySet = new HashSet<String>();
        propertySet.add("1");
        propertySet.add("5");
        propertySet.add("2");

        when(roleService.getPropertiesForUser(anyString(), anyObject())).thenReturn(propertySet);

        Set<String> userProperties = new HashSet<String>();
        List allProperties = new ArrayList<Property>();
        Property p1 = new Property();
        p1.setId(1);
        p1.setCode("1");
        userProperties.add("1");
        allProperties.add(p1);
        Property p2 = new Property();
        p2.setId(2);
        p2.setCode("2");
        // userProperties.add("2");
        allProperties.add(p2);
        Property p5 = new Property();
        p5.setId(5);
        p5.setCode("5");
        userProperties.add("5");
        allProperties.add(p5);

        AuthorizationGroupPropertyMapping map1 = new AuthorizationGroupPropertyMapping();
        map1.setPropertyId(p1.getId());
        AuthorizationGroupPropertyMapping map2 = new AuthorizationGroupPropertyMapping();
        map2.setPropertyId(p2.getId());

        AuthorizationGroup ag1 = new AuthorizationGroup();
        ag1.setName("AG1");
        Set<AuthorizationGroupPropertyMapping> set1 = new HashSet<AuthorizationGroupPropertyMapping>();
        set1.add(map1);
        ag1.setAuthGroupPropertyMappings(set1);
        AuthorizationGroup ag2 = new AuthorizationGroup();
        ag2.setName("AG2");
        Set<AuthorizationGroupPropertyMapping> set2 = new HashSet<AuthorizationGroupPropertyMapping>();
        set2.add(map2);
        ag2.setAuthGroupPropertyMappings(set2);
        AuthorizationGroup ag3 = new AuthorizationGroup();
        ag3.setName("AG3");
        ag3.setAuthGroupPropertyMappings(new HashSet<AuthorizationGroupPropertyMapping>());

        List<AuthorizationGroup> allAuthGroups = Arrays.asList(ag1, ag2, ag3);

        when(mockCrud.findByNativeQuery(anyString(), anyMap(), any(RowMapper.class))).thenReturn(allProperties);
        when(
                mockCrud.findByNamedQuerySingleResult(Property.NUMBER_OF_PROPERTIES_FOR_CLIENT,
                        QueryParameter.with("clientId", WC_CLIENT_ID_DUMMY).parameters())).thenReturn((long) allProperties.size());
        when(mockCrud.<AuthorizationGroup>findByNamedQuery(AuthorizationGroup.BY_CLIENT_ID, QueryParameter.with("clientId", WC_CLIENT_ID_DUMMY).parameters()))
                .thenReturn(allAuthGroups);
        when(roleService.getPropertiesForUser(anyString(), any(AuthGroupExploder.class))).thenReturn(userProperties);
        LDAPUser ldapUser = new LDAPUser();
        ldapUser.setDN(WC_DN_REGULAR_USER);
        when(userService.getById(anyString())).thenReturn(ldapUser);
        Client c1 = new Client();
        c1.setCode("Dummy Client");
        c1.setId(1);
        when(mockCrud.find(Client.class, 1)).thenReturn(c1);

        mockConfigService = mock(PacmanConfigParamsService.class);
        when(mockConfigService.getValue("pacman.Dummy Client", GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value())).thenReturn("Code");
        service.setPacmanConfigParamsService(mockConfigService);

        AuthorizationService.PropertyAuthGroupExploder propertyAuthGroupExploder = new AuthorizationService().new PropertyAuthGroupExploder(c1.getId());

        when(service.createPropertyAuthGroupExploder(c1.getId())).thenReturn(propertyAuthGroupExploder);

        List<AuthorizationGroup> all = service.getAllAssignableAuthGroupDetails(false);

        assertNotNull(all);
        assertEquals(3, all.size());
        assertEquals(1, howManyAuthGroupsAssignableByUser(all));
        AuthorizationGroup group = getAllPropsAuthorizationGroup(all);
        assertNotNull(group, "Should be an all props auth group");
        Assertions.assertFalse(group.isAssignableByTheCurrentUser());
        assertEquals(1, getAllPropsAuthGroupQuantity(all), "Should only be one all props auth group");
    }

    @SuppressWarnings("unchecked")
    @Test
    public void getAllNonInternalAssignableAuthGroupDetailsWithNoPropAuthGroupWithNewCache() throws LDAPException {
        CrudService mockCrud = mock(CrudService.class);
        service.setGlobalCrudService(mockCrud);

        LDAPUser u1 = new LDAPUser();
        u1.setUserId(1);
        ArrayList<String> roleList = new ArrayList<>();
        roleList.add("roleId=-666 ON GROUP groupId=-666");
        u1.setRoles(roleList);
        when(userService.getById("11403")).thenReturn(u1);
        Set<String> propertySet = new HashSet<String>();
        propertySet.add("1");
        propertySet.add("5");
        propertySet.add("2");

        when(roleService.getPropertiesForUser(anyString(), anyObject())).thenReturn(propertySet);

        Set<String> userProperties = new HashSet<String>();
        List allProperties = new ArrayList<Property>();
        Property p1 = new Property();
        p1.setId(1);
        p1.setCode("1");
        userProperties.add("1");
        allProperties.add(p1);
        Property p2 = new Property();
        p2.setId(2);
        p2.setCode("2");
        // userProperties.add("2");
        allProperties.add(p2);
        Property p5 = new Property();
        p5.setId(5);
        p5.setCode("5");
        userProperties.add("5");
        allProperties.add(p5);

        AuthorizationGroupPropertyMapping map1 = new AuthorizationGroupPropertyMapping();
        map1.setPropertyId(p1.getId());
        AuthorizationGroupPropertyMapping map2 = new AuthorizationGroupPropertyMapping();
        map2.setPropertyId(p2.getId());

        AuthorizationGroup ag1 = new AuthorizationGroup();
        ag1.setName("AG1");
        Set<AuthorizationGroupPropertyMapping> set1 = new HashSet<AuthorizationGroupPropertyMapping>();
        set1.add(map1);
        ag1.setAuthGroupPropertyMappings(set1);
        AuthorizationGroup ag2 = new AuthorizationGroup();
        ag2.setName("AG2");
        Set<AuthorizationGroupPropertyMapping> set2 = new HashSet<AuthorizationGroupPropertyMapping>();
        set2.add(map2);
        ag2.setAuthGroupPropertyMappings(set2);
        AuthorizationGroup ag3 = new AuthorizationGroup();
        ag3.setName("AG3");
        ag3.setAuthGroupPropertyMappings(new HashSet<AuthorizationGroupPropertyMapping>());

        List<AuthorizationGroup> allAuthGroups = Arrays.asList(ag1, ag2, ag3);

        when(mockCrud.findByNativeQuery(anyString(), anyMap(), any(RowMapper.class))).thenReturn(allProperties);
        when(
                mockCrud.findByNamedQuerySingleResult(Property.NUMBER_OF_PROPERTIES_FOR_CLIENT,
                        QueryParameter.with("clientId", WC_CLIENT_ID_DUMMY).parameters())).thenReturn((long) allProperties.size());
        when(mockCrud.<AuthorizationGroup>findByNamedQuery(AuthorizationGroup.BY_CLIENT_ID, QueryParameter.with("clientId", WC_CLIENT_ID_DUMMY).parameters()))
                .thenReturn(allAuthGroups);
        when(roleService.getPropertiesForUser(anyString(), any(AuthGroupExploder.class))).thenReturn(userProperties);
        LDAPUser ldapUser = new LDAPUser();
        ldapUser.setDN(WC_DN_REGULAR_USER);
        when(userService.getById(anyString())).thenReturn(ldapUser);
        Client c1 = new Client();
        c1.setCode("Dummy Client");
        c1.setId(1);
        when(mockCrud.find(Client.class, 1)).thenReturn(c1);

        mockConfigService = mock(PacmanConfigParamsService.class);
        when(mockConfigService.getValue("pacman.Dummy Client", GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value())).thenReturn("Code");
        service.setPacmanConfigParamsService(mockConfigService);

        AuthorizationService.PropertyAuthGroupExploder propertyAuthGroupExploder = new AuthorizationService().new PropertyAuthGroupExploder(c1.getId());

        when(service.createPropertyAuthGroupExploder(c1.getId())).thenReturn(propertyAuthGroupExploder);

        List<AuthorizationGroup> all = service.getAllAssignableAuthGroupDetails(false);

        assertNotNull(all);
        assertEquals(3, all.size());
        assertEquals(1, howManyAuthGroupsAssignableByUser(all));
        AuthorizationGroup group = getAllPropsAuthorizationGroup(all);
        assertNotNull(group, "Should be an all props auth group");
        Assertions.assertFalse(group.isAssignableByTheCurrentUser());
        assertEquals(1, getAllPropsAuthGroupQuantity(all), "Should only be one all props auth group");
    }

    @Test
    public void areAllPropsInAuthGroupAvailableToThisUser() {
        Property p1 = new Property();
        p1.setId(1);
        p1.setName("p1");
        Property p2 = new Property();
        p2.setId(2);
        p2.setName("p2");
        Property p3 = new Property();
        p3.setId(3);
        p3.setName("p3");
        Property p4 = new Property();
        p4.setId(4);
        p4.setName("p4");
        Property p5 = new Property();
        p5.setId(5);
        p5.setName("p5");
        Property p6 = new Property();
        p6.setId(6);
        p6.setName("p6");
        Property p7 = new Property();
        p7.setId(7);
        p7.setName("p7");

        AuthorizationGroupPropertyMapping map1 = new AuthorizationGroupPropertyMapping();
        map1.setPropertyId(p1.getId());

        AuthorizationGroupPropertyMapping map2 = new AuthorizationGroupPropertyMapping();
        map2.setPropertyId(p2.getId());

        AuthorizationGroupPropertyMapping map3 = new AuthorizationGroupPropertyMapping();
        map3.setPropertyId(p3.getId());

        AuthorizationGroupPropertyMapping map4 = new AuthorizationGroupPropertyMapping();
        map4.setPropertyId(p4.getId());

        AuthorizationGroupPropertyMapping map5 = new AuthorizationGroupPropertyMapping();
        map5.setPropertyId(p5.getId());

        AuthorizationGroupPropertyMapping map6 = new AuthorizationGroupPropertyMapping();
        map6.setPropertyId(p6.getId());

        AuthorizationGroupPropertyMapping map7 = new AuthorizationGroupPropertyMapping();
        map7.setPropertyId(p7.getId());

        Set<AuthorizationGroupPropertyMapping> ag1 = new HashSet<AuthorizationGroupPropertyMapping>();
        ag1.add(map1);
        ag1.add(map2);
        ag1.add(map3);
        ag1.add(map4);
        ag1.add(map5);

        Set<AuthorizationGroupPropertyMapping> ag2 = new HashSet<AuthorizationGroupPropertyMapping>();
        ag2.add(map1);
        ag2.add(map5);

        Set<AuthorizationGroupPropertyMapping> ag3 = new HashSet<AuthorizationGroupPropertyMapping>();
        ag3.add(map6);
        ag3.add(map7);

        Set<AuthorizationGroupPropertyMapping> ag4 = new HashSet<AuthorizationGroupPropertyMapping>();
        ag4.add(map1);
        ag4.add(map7);

        // user has access to AG1
        List<Property> props_12345 = Arrays.asList(p1, p2, p3, p4, p5);
        assertTrue(service.areAllPropsInAuthGroupAvailableToThisUser(props_12345, ag1), "doh");
        assertTrue(service.areAllPropsInAuthGroupAvailableToThisUser(props_12345, ag2), "doh");
        Assertions.assertFalse(service.areAllPropsInAuthGroupAvailableToThisUser(props_12345, ag3), "doh");
        Assertions.assertFalse(service.areAllPropsInAuthGroupAvailableToThisUser(props_12345, ag4), "doh");

        // user has access to AG2
        List<Property> props_15 = Arrays.asList(p1, p5);
        Assertions.assertFalse(service.areAllPropsInAuthGroupAvailableToThisUser(props_15, ag1), "doh");
        assertTrue(service.areAllPropsInAuthGroupAvailableToThisUser(props_15, ag2), "doh");
        Assertions.assertFalse(service.areAllPropsInAuthGroupAvailableToThisUser(props_15, ag3), "doh");
        Assertions.assertFalse(service.areAllPropsInAuthGroupAvailableToThisUser(props_15, ag4), "doh");

        // user has access to AG3
        List<Property> props_67 = Arrays.asList(p6, p7);
        Assertions.assertFalse(service.areAllPropsInAuthGroupAvailableToThisUser(props_67, ag1), "doh");
        Assertions.assertFalse(service.areAllPropsInAuthGroupAvailableToThisUser(props_67, ag2), "doh");
        assertTrue(service.areAllPropsInAuthGroupAvailableToThisUser(props_67, ag3), "doh");
        Assertions.assertFalse(service.areAllPropsInAuthGroupAvailableToThisUser(props_67, ag4), "doh");

        // user has access to AG4
        List<Property> props_17 = Arrays.asList(p1, p7);
        Assertions.assertFalse(service.areAllPropsInAuthGroupAvailableToThisUser(props_17, ag1), "doh");
        Assertions.assertFalse(service.areAllPropsInAuthGroupAvailableToThisUser(props_17, ag2), "doh");
        Assertions.assertFalse(service.areAllPropsInAuthGroupAvailableToThisUser(props_17, ag3), "doh");
        assertTrue(service.areAllPropsInAuthGroupAvailableToThisUser(props_17, ag4), "doh");

        // user has access to AG1 *and* P7
        List<Property> props_123457 = Arrays.asList(p1, p2, p3, p4, p5, p7);
        assertTrue(service.areAllPropsInAuthGroupAvailableToThisUser(props_123457, ag1), "doh");
        assertTrue(service.areAllPropsInAuthGroupAvailableToThisUser(props_123457, ag2), "doh");
        Assertions.assertFalse(service.areAllPropsInAuthGroupAvailableToThisUser(props_123457, ag3), "doh");
        assertTrue(service.areAllPropsInAuthGroupAvailableToThisUser(props_123457, ag4), "doh");
    }

    @Test
    public void testGetAuthorizationGroupByName() {
        CrudService mockCrud = mock(CrudService.class);
        service.setGlobalCrudService(mockCrud);

        when(mockCrud.findByNamedQuery(anyString(), any(Map.class))).thenReturn(
                Arrays.asList(
                        new AuthorizationGroup(1, "Mock-Auth-Group-1", "Mock-Auth-Group-1")
                )
        );

        Optional<AuthorizationGroup> authGroups = service.getAuthorizationGroupByName("Mock-Auth-Group-1");
        assertTrue(authGroups.isPresent());
        AuthorizationGroup authGroup = authGroups.get();
        assertEquals(1, authGroup.getId().intValue());
        assertEquals("Mock-Auth-Group-1", authGroup.getName());
        assertEquals("Mock-Auth-Group-1", authGroup.getDescription());
    }

    @Test
    public void testGetAuthorizationGroupByNameWorksForAllPropertiesGroup() {

        Optional<AuthorizationGroup> authGroups = service.getAuthorizationGroupByName(AuthorizationGroup.ALL_PROP_NAME);
        assertTrue(authGroups.isPresent());
        AuthorizationGroup authGroup = authGroups.get();
        assertEquals(AuthorizationGroup.ALL_PROP_ID, authGroup.getId());
        assertEquals(AuthorizationGroup.ALL_PROP_NAME, authGroup.getName());
    }

    @Test
    public void subsetCompareFullSubset() {
        CrudService mockCrud = mock(CrudService.class);
        service.setGlobalCrudService(mockCrud);

        AuthorizationGroupSummary parentAuthGroup = new AuthorizationGroupSummary();
        parentAuthGroup.setId(1);
        AuthorizationGroupSummary childAuthGroup = new AuthorizationGroupSummary();
        childAuthGroup.setId(2);

        Property p1 = new Property();
        p1.setId(1);

        p1.setCode("1");
        Property p2 = new Property();
        p2.setId(2);
        p2.setCode("2");
        Property p3 = new Property();
        p3.setId(3);
        p3.setCode("3");
        Property p4 = new Property();
        p3.setId(4);
        p3.setCode("4");

        Set<Property> parentProperties = new HashSet<Property>();
        parentProperties.add(p1);
        parentProperties.add(p2);
        parentProperties.add(p3);
        parentAuthGroup.setProperties(parentProperties);

        Set<Property> childProperties = new HashSet<Property>();
        childProperties.add(p2);
        childProperties.add(p3);
        childAuthGroup.setProperties(childProperties);

        when((mockCrud.find(AuthorizationGroupSummary.class, 1))).thenReturn(parentAuthGroup);
        when((mockCrud.find(AuthorizationGroupSummary.class, 2))).thenReturn(childAuthGroup);

        List<Property> missingProperties = service.subsetCompare(1, 2);

        Assertions.assertNotNull(missingProperties);
        Assertions.assertTrue(missingProperties.isEmpty());

        childAuthGroup.getProperties().add(p4);

        when((mockCrud.find(AuthorizationGroupSummary.class, 1))).thenReturn(parentAuthGroup);
        when((mockCrud.find(AuthorizationGroupSummary.class, 2))).thenReturn(childAuthGroup);

        missingProperties = service.subsetCompare(1, 2);

        Assertions.assertNotNull(missingProperties);
        Assertions.assertEquals(1, missingProperties.size());
        Assertions.assertEquals(p4, missingProperties.get(0));
    }

    @Test
    public void isAuthGroupInUse() {
        globalCrudService().executeUpdateByNativeQuery("update Users set Client_Code='ideas' where USER_ID in(1,11403)");
        assertTrue(service.isAuthGroupInUse(-666, "ideas"));
        Assertions.assertFalse(service.isAuthGroupInUse(WC_AUTH_GROUP_UNUSED));
    }

    @Test
    public void permsForNewClientsSansProperties() {
        PacmanWorkContextHelper.setPropertyId(null);

        CrudService mockCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockCrudService);

        when(
                mockCrudService.findByNamedQuerySingleResult(Property.NUMBER_OF_PROPERTIES_FOR_CLIENT,
                        QueryParameter.with("clientId", WC_CLIENT_ID_DUMMY).parameters())).thenReturn(0L);

        Set<String> perms = service.getPermsForContext(false);

        assertNotNull(perms);
        assertEquals(1, perms.size(), "Should only be 1 perm");
        assertTrue(perms.contains(Role.NO_CLIENT_PROPS_ID), "Should be the special no client props perm");
    }

    @SuppressWarnings("unchecked")
    @Test
    public void getCapacityForProperties() throws Exception {
        PropertyCapacityCache propertyCapacityCache = mock(PropertyCapacityCache.class);
        service.setPropertyCapacityCache(propertyCapacityCache);

        Map<Integer, BigDecimal> propertyCapacities = new HashMap<Integer, BigDecimal>();
        propertyCapacities.put(TestProperty.H1.getId(), new BigDecimal(448));
        when((propertyCapacityCache.getCapacitiesForProperties(anyList()))).thenReturn(propertyCapacities);

        mockConfigParameterService("Code", TestProperty.H1, 1, clientCode_BSTN);

        List<Property> properties = new ArrayList<Property>();
        properties.add(globalCrudService().find(Property.class, WC_PROPERTY_ID_PUNE));

        Map<Integer, BigDecimal> propertyCapacityMap = service.getCapacityForAuthorizedProperties();
        assertEquals(448, propertyCapacityMap.get(WC_PROPERTY_ID_PUNE).longValue());

        verify(propertyCapacityCache).getCapacitiesForProperties(anyList());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void getPropertyGroupCapacity() {
        setWorkContextProperty(TestProperty.H1);

        PropertyCapacityCache propertyCapacityCache = mock(PropertyCapacityCache.class);
        service.setPropertyCapacityCache(propertyCapacityCache);

        Map<Integer, BigDecimal> propertyCapacities = new HashMap<Integer, BigDecimal>();
        propertyCapacities.put(WC_PROPERTY_ID_PUNE, new BigDecimal(448));
        propertyCapacities.put(WC_PROPERTY_ID_PARIS, new BigDecimal(295));
        when((propertyCapacityCache.getCapacitiesForProperties(isA(List.class)))).thenReturn(propertyCapacities);

        mockConfigService = mock(PacmanConfigParamsService.class);
        when(
                mockConfigService.getParameterValueByClientLevel(GUIConfigParamName.CORE_PROPERTY_MAX_PROPERTY_PER_PROPERTY_GROUP.value(),
                        PacmanWorkContextTestHelper.WC_CLIENT_CODE_BLACKSTONE)).thenReturn("50");
        when(mockConfigService.getValue("pacman." + "BSTN", GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value())).thenReturn("Code");
        propertyGroupService.setConfigParamService(mockConfigService);
        service.setPacmanConfigParamsService(mockConfigService);
        PacmanWorkContextHelper.setPropertyGroupId(WC_PROPERTY_GROUP_ID_BLKSTN_ALL);
        PacmanWorkContextHelper.setPropertyId(null);
        BigDecimal propertyGroupCapacity = service.getPropertyGroupCapacity();
        assertEquals(new BigDecimal(743), propertyGroupCapacity);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void getCapacityForAuthorizedPropertyGroups() {
        setWorkContextProperty(TestProperty.H1);

        mockConfigService = mock(PacmanConfigParamsService.class);
        when(
                mockConfigService.getParameterValueByClientLevel(GUIConfigParamName.CORE_PROPERTY_MAX_PROPERTY_PER_PROPERTY_GROUP.value(),
                        PacmanWorkContextTestHelper.WC_CLIENT_CODE_BLACKSTONE)).thenReturn("50");
        when(mockConfigService.getValue("pacman." + "BSTN", GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value())).thenReturn("Code");
        when(mockConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.IS_COMMON_PROPERTY_GROUP_ACCESS_ENABLED)).thenReturn(false);
        propertyGroupService.setConfigParamService(mockConfigService);
        service.setPacmanConfigParamsService(mockConfigService);

        PropertyCapacityCache propertyCapacityCache = mock(PropertyCapacityCache.class);
        service.setPropertyCapacityCache(propertyCapacityCache);

        Map<Integer, BigDecimal> propertyCapacities = new HashMap<Integer, BigDecimal>();
        propertyCapacities.put(WC_PROPERTY_ID_PUNE, new BigDecimal(448));
        propertyCapacities.put(WC_PROPERTY_ID_PARIS, new BigDecimal(295));
        when((propertyCapacityCache.getCapacitiesForProperties(anyList()))).thenReturn(propertyCapacities);

        BigDecimal actual = service.getCapacityForAuthorizedPropertyGroups().get(WC_PROPERTY_GROUP_ID_BLKSTN_ALL);
        assertEquals(new BigDecimal(743), actual);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void getCapacitySumForProperties() {
        List<Property> properties = getProperties();

        PropertyCapacityCache propertyCapacityCache = mock(PropertyCapacityCache.class);
        service.setPropertyCapacityCache(propertyCapacityCache);

        Map<Integer, BigDecimal> propertyCapacities = new HashMap<Integer, BigDecimal>();
        propertyCapacities.put(WC_PROPERTY_ID_PUNE, new BigDecimal(448));
        propertyCapacities.put(WC_PROPERTY_ID_PARIS, new BigDecimal(295));
        when((propertyCapacityCache.getCapacitiesForProperties(isA(List.class)))).thenReturn(propertyCapacities);

        assertEquals(new BigDecimal(743), service.getCapacitySumForProperties(properties));

        verify(propertyCapacityCache).getCapacitiesForProperties(anyList());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void shouldReturnZeroWhenCacheIsEmpty() {
        List<Property> properties = getProperties();

        PropertyCapacityCache propertyCapacityCache = mock(PropertyCapacityCache.class);
        service.setPropertyCapacityCache(propertyCapacityCache);
        Map<Integer, BigDecimal> propertyCapacities = new HashMap<Integer, BigDecimal>();
        when((propertyCapacityCache.getCapacitiesForProperties(isA(List.class)))).thenReturn(propertyCapacities);
        assertEquals(BigDecimal.ZERO, service.getCapacitySumForProperties(properties));
    }

    @SuppressWarnings("serial")
    private List<Property> getProperties() {
        return new ArrayList<Property>() {
            {
                add(new Property() {
                    {
                        setId(WC_PROPERTY_ID_PUNE);
                    }
                });
                add(new Property() {
                    {
                        setId(WC_PROPERTY_ID_PARIS);
                    }
                });
            }
        };
    }

    @Test
    public void retrieveAuthorizedPropertyGroup_validId_findsGroup() {
        setWorkContextProperty(TestProperty.H1);

        service.setPacmanConfigParamsService(mockConfigService);
        when(mockConfigService.getValue("pacman." + "BSTN", GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value())).thenReturn("Code");
        when(
                mockConfigService.getParameterValueByClientLevel(GUIConfigParamName.CORE_PROPERTY_MAX_PROPERTY_PER_PROPERTY_GROUP.value(),
                        PacmanWorkContextTestHelper.WC_CLIENT_CODE_BLACKSTONE)).thenReturn("50");
        PropertyGroup blkstnAll = service.retrieveAuthorizedPropertyGroup(WC_PROPERTY_GROUP_ID_BLKSTN_ALL);
        assertEquals(WC_PROPERTY_GROUP_ID_BLKSTN_ALL, blkstnAll.getId());
    }

    @Test
    public void authGroupMappingStatusIsNotNull() {
        AuthorizationGroup authGroup = new AuthorizationGroup();
        authGroup.setClientId(WC_CLIENT_ID_BLACKSTONE);
        authGroup.setName("The Tyranny of Evil Men");
        authGroup.setCreateDate(new Timestamp(new Date().getTime()));
        authGroup.setDescription("This is a test");
        authGroup.setStatusId(Constants.ACTIVE_STATUS_ID);
        authGroup.setCreatedByUserId(Integer.parseInt(WC_USER_ID_SSO));

        Property property = globalCrudService().find(Property.class, WC_PROPERTY_ID_PUNE);
        assertNotNull(property);

        HashSet<AuthorizationGroupPropertyMapping> mappings = new HashSet<AuthorizationGroupPropertyMapping>();
        AuthorizationGroupPropertyMapping mapping = new AuthorizationGroupPropertyMapping();
        mapping.setPropertyId(property.getId());
        mapping.setAuthorizationGroup(authGroup);
        mapping.setStatusId(null);
        mappings.add(mapping);
        authGroup.setAuthGroupPropertyMappings(mappings);

        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.getParameterName())).thenReturn("false");

        authGroup = service.persistAuthorizationGroup(authGroup, null);
        assertNotNull(authGroup);
        assertNotNull(authGroup.getAuthGroupPropertyMappings());
        assertEquals(1, authGroup.getAuthGroupPropertyMappings().size());

        mapping = authGroup.getAuthGroupPropertyMappings().iterator().next();
        assertNotNull(mapping);
        assertNotNull(mapping.getPropertyId());
        assertEquals(property.getId(), mapping.getPropertyId());
        assertNotNull(authGroup.getStatusId());
        assertEquals(Constants.ACTIVE_STATUS_ID, authGroup.getStatusId());

        verify(uasService, never()).migrateAuthGroupToFDS(authGroup);
    }

    @Test
    public void authGroupMappingStatusIsNotNull_FDSEnabled() {
        AuthorizationGroup authGroup = new AuthorizationGroup();
        authGroup.setClientId(WC_CLIENT_ID_BLACKSTONE);
        authGroup.setName("The Tyranny of Evil Men");
        authGroup.setCreateDate(new Timestamp(new Date().getTime()));
        authGroup.setDescription("This is a test");
        authGroup.setStatusId(Constants.ACTIVE_STATUS_ID);
        authGroup.setCreatedByUserId(Integer.parseInt(WC_USER_ID_SSO));

        Property property = globalCrudService().find(Property.class, WC_PROPERTY_ID_PUNE);
        assertNotNull(property);

        HashSet<AuthorizationGroupPropertyMapping> mappings = new HashSet<AuthorizationGroupPropertyMapping>();
        AuthorizationGroupPropertyMapping mapping = new AuthorizationGroupPropertyMapping();
        mapping.setPropertyId(property.getId());
        mapping.setAuthorizationGroup(authGroup);
        mapping.setStatusId(null);
        mappings.add(mapping);
        authGroup.setAuthGroupPropertyMappings(mappings);

        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.getParameterName())).thenReturn("true");

        authGroup = service.persistAuthorizationGroup(authGroup, null);
        assertNotNull(authGroup);
        assertNotNull(authGroup.getAuthGroupPropertyMappings());
        assertEquals(1, authGroup.getAuthGroupPropertyMappings().size());

        mapping = authGroup.getAuthGroupPropertyMappings().iterator().next();
        assertNotNull(mapping);
        assertNotNull(mapping.getPropertyId());
        assertEquals(property.getId(), mapping.getPropertyId());
        assertNotNull(authGroup.getStatusId());
        assertEquals(Constants.ACTIVE_STATUS_ID, authGroup.getStatusId());

        verify(uasService).migrateAuthGroupToFDS(authGroup);
    }

    @Test
    public void getAuthGroupPropertyIds() throws Exception {
        List<Integer> properties = service.getAuthGroupPropertyIDs(PacmanWorkContextTestHelper.WC_AUTH_GROUP_ID_HILTON_WEST);
        assertNotNull(properties);
        assertTrue(properties.size() > 0);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void getCapacityForAuthorizedProperties() {
        PropertyCapacityCache propertyCapacityCache = mock(PropertyCapacityCache.class);
        service.setPropertyCapacityCache(propertyCapacityCache);

        Map<Integer, BigDecimal> propertyCapacities = new HashMap<Integer, BigDecimal>();
        propertyCapacities.put(TestProperty.H1.getId(), new BigDecimal(100));

        when(propertyCapacityCache.getCapacitiesForProperties(anyList())).thenReturn(propertyCapacities);

        mockConfigParameterService("Code", TestProperty.H1, 1, clientCode_BSTN);
        Map<Integer, BigDecimal> capacities = service.getCapacityForAuthorizedProperties();
        assertNotNull(capacities);
        assertTrue(capacities.size() > 0);
        verify(propertyCapacityCache).getCapacitiesForProperties(anyList());

        List<Property> propertyList = new ArrayList<>();
        propertyList.add(PROPERTY_BLKSTN_PARIS);
        Map<Integer, BigDecimal> capacityPropertyGroups = service.getCapacityForAuthorizedProperties(propertyList);

        assertNotNull(capacityPropertyGroups);
        assertTrue(capacityPropertyGroups.size() > 0);
    }

    @Test
    public void getPropertyGroups() {
        workContext().setClientId(WC_CLIENT_ID_BLACKSTONE);
        when(mockConfigService.getValue("pacman.unittest", GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value())).thenReturn("Code");
        when(
                mockConfigService.getParameterValueByClientLevel(GUIConfigParamName.CORE_PROPERTY_MAX_PROPERTY_PER_PROPERTY_GROUP.value(),
                        PacmanWorkContextTestHelper.WC_CLIENT_CODE_TEST)).thenReturn("50");
        List<Map<String, Object>> propertyGroups = service.getPropertyGroups();
        verify(mockConfigService).getValue(anyString(), anyString());
        verify(mockConfigService).getParameterValueByClientLevel(anyString(), anyString());
        assertNotNull(propertyGroups);
        assertTrue(propertyGroups.size() > 0);
    }

    @Test
    public void propertyShouldDisplayCodeBasedOnConfiguration() {
        mockConfigParameterService("Code", TestProperty.H1, 1, clientCode_BSTN);
        List<Map<String, Object>> properties = service.getProperties();
        verifyZeroInteractions(mockConfigService);
        for (Map<String, Object> property : properties) {
            Integer propertyId = (Integer) property.get("id");
            Property authorizedProperty = globalCrudService().find(Property.class, propertyId);
            assertEquals(authorizedProperty.getCode(), property.get("displayLabelField"));
        }
    }

    @Test
    public void propertyShouldDisplayNameBasedOnConfiguration() {
        mockConfigParameterService("Name", TestProperty.H1, 1, clientCode_BSTN);
        List<Map<String, Object>> properties = service.getProperties();
        verifyZeroInteractions(mockConfigService);
        for (Map<String, Object> property : properties) {
            Integer propertyId = (Integer) property.get("id");
            Property authorizedProperty = globalCrudService().find(Property.class, propertyId);
            assertEquals(authorizedProperty.getName(), property.get("displayLabelField"));
        }
    }

    @Test
    public void getClients() {
        List<Map<String, Object>> clients = service.getClients();
        assertNotNull(clients);
        assertTrue(clients.size() > 0);
    }

    @Test
    public void getClientsWithNewCache() {
        List<Map<String, Object>> clients = service.getClients();
        assertNotNull(clients);
        assertTrue(clients.size() > 0);
    }

    @Test
    public void isInternalUserSansPrincipal() {
        PacmanThreadLocalContextHolder.setPrincipal(null);
        assertFalse(service.isInternalUser());
    }

    @Test
    public void authorizationGroupOO() {
        AuthorizationGroup group = new AuthorizationGroup(17, "Test Group", "Just a test");
        assertNull(group.getMappedProperties());
        group.setMappedProperties(Arrays.asList(1, 2, 3));
        assertNotNull(group.getMappedProperties());
        assertEquals(3, group.getMappedProperties().size());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void getPropertiesInAuthGroup() {
        List<Integer> properties = globalCrudService().findByNamedQuery(AuthorizationGroupPropertyMapping.GET_PROPERTIES_IN_AUTH_GROUP,
                QueryParameter.with("authGroupId", 1).parameters());
        assertEquals(2, properties.size());
    }

    @Test
    public void shouldGetPropertiesContainCodeAsDisplayLabel() {
        mockConfigParameterService("Code", TestProperty.H1, 1, clientCode_BSTN);
        List<Property> retrieveAuthorizedProperties = service.retrieveAuthorizedProperties();
        for (Property property : retrieveAuthorizedProperties) {
            assertEquals(property.getCode(), property.getDisplayLabelField());
        }
    }

    @Test
    public void shouldGetPropertiesContainNameAsDisplayLabel() {
        mockConfigParameterService("Name", TestProperty.H1, 1, clientCode_BSTN);
        List<Property> retrieveAuthorizedProperties = service.retrieveAuthorizedProperties();
        for (Property property : retrieveAuthorizedProperties) {
            assertEquals(property.getName(), property.getDisplayLabelField());
        }
    }

    @Test
    public void shouldGetPropertiesInSortedOrderByDisplayLabelAsName() {
        LDAPUser u1 = new LDAPUser();
        u1.setUserId(1);
        ArrayList<String> roleList = new ArrayList<>();
        roleList.add("roleId=-666 ON GROUP groupId=-666");
        u1.setRoles(roleList);
        when(userService.getById("11403")).thenReturn(u1);
        Set<String> propertySet = new HashSet<String>();
        propertySet.add("11");
        propertySet.add("5");
        propertySet.add("6");
        propertySet.add("9");
        propertySet.add("10");
        when(roleService.getPropertiesForUser(nullable(String.class), anyObject())).thenReturn(propertySet);

        setWorkContextProperty(TestProperty.H1);

        mockConfigParameterService("Name", TestProperty.H1, 1, clientCode_BSTN);
        List<Property> retrieveAuthorizedProperties = service.retrieveAuthorizedProperties();

        assertEquals("Hampton Inn Fayetteville", retrieveAuthorizedProperties.get(0).getDisplayLabelField());
        assertEquals("Hilton - Paris", retrieveAuthorizedProperties.get(1).getDisplayLabelField());
        assertEquals("Hilton - Pune", retrieveAuthorizedProperties.get(2).getDisplayLabelField());
        assertEquals("Hilton London Metropole", retrieveAuthorizedProperties.get(3).getDisplayLabelField());
        assertEquals("Sheraton - Cary", retrieveAuthorizedProperties.get(4).getDisplayLabelField());
    }

    @Test
    public void shouldGetPropertiesInSortedOrderByDisplayLabelAsNameWithNewCache() {
        LDAPUser u1 = new LDAPUser();
        u1.setUserId(1);
        ArrayList<String> roleList = new ArrayList<>();
        roleList.add("roleId=-666 ON GROUP groupId=-666");
        u1.setRoles(roleList);
        when(userService.getById("11403")).thenReturn(u1);
        Set<String> propertySet = new HashSet<String>();
        propertySet.add("11");
        propertySet.add("5");
        propertySet.add("6");
        propertySet.add("9");
        propertySet.add("10");
        when(roleService.getPropertiesForUser(nullable(String.class), anyObject())).thenReturn(propertySet);

        setWorkContextProperty(TestProperty.H1);

        mockConfigParameterService("Name", TestProperty.H1, 1, clientCode_BSTN);
        List<Property> retrieveAuthorizedProperties = service.retrieveAuthorizedProperties();

        assertEquals("Hampton Inn Fayetteville", retrieveAuthorizedProperties.get(0).getDisplayLabelField());
        assertEquals("Hilton - Paris", retrieveAuthorizedProperties.get(1).getDisplayLabelField());
        assertEquals("Hilton - Pune", retrieveAuthorizedProperties.get(2).getDisplayLabelField());
        assertEquals("Hilton London Metropole", retrieveAuthorizedProperties.get(3).getDisplayLabelField());
        assertEquals("Sheraton - Cary", retrieveAuthorizedProperties.get(4).getDisplayLabelField());
    }

    @Test
    public void shouldSortByDisplayLabel() {
        PROPERTY_BLKSTN_PARIS.setDisplayLabelField(PROPERTY_BLKSTN_PARIS.getCode());
        PROPERTY_HILTON_BOSCO.setDisplayLabelField(PROPERTY_HILTON_BOSCO.getCode());

        List<Property> properties = new ArrayList<Property>();
        properties.add(PROPERTY_BLKSTN_PARIS);
        properties.add(PROPERTY_HILTON_BOSCO);
        service.sortByDisplayLabelField(properties);

        assertEquals(PROPERTY_HILTON_BOSCO.getId(), properties.get(0).getId());
        assertEquals(PROPERTY_BLKSTN_PARIS.getId(), properties.get(1).getId());
    }

    @Test
    public void testSynchronizePropertyGroupsForAllUsers() {
        updateWorkContext(workContext(), WC_USER_ID_SSO, WC_CLIENT_ID_BLACKSTONE, WC_CLIENT_CODE_BLACKSTONE, // Needs
                // to
                // be
                // unittest
                // for
                // LDAP
                // to
                // write
                WC_PROPERTY_ID_PUNE, WC_PROPERTY_CODE_PUNE);

        Client client = new Client();
        client.setId(PlatformWorkContextTestHelper.WC_CLIENT_ID_DUMMY);

        when(mockClientConfigService.getClientByCode(PlatformWorkContextTestHelper.WC_CLIENT_CODE_BLACKSTONE)).thenReturn(client);

        List<PropertyGroup> propertyGroupList = new ArrayList<PropertyGroup>();
        Set<PropertyPropertyGroup> propPropSet1 = new HashSet<PropertyPropertyGroup>();
        Set<PropertyPropertyGroup> propPropSet2 = new HashSet<PropertyPropertyGroup>();

        PropertyGroup PG1 = UniquePropertyGroupCreator.createPropertyGroup(globalCrudService(),
                PlatformWorkContextTestHelper.WC_CLIENT_ID_BLACKSTONE,
                Integer.valueOf(PlatformWorkContextTestHelper.WC_USER_ID_SSO));
        PropertyGroup PG2 = UniquePropertyGroupCreator.createPropertyGroup(globalCrudService(),
                PlatformWorkContextTestHelper.WC_CLIENT_ID_BLACKSTONE,
                Integer.valueOf(PlatformWorkContextTestHelper.WC_USER_ID_SSO));

        PropertyPropertyGroup propPropObj1 = UniquePropertyPropertyGroupCreator.createPropertyPropertyGroupByPropertyGroup(
                globalCrudService(), PG1, 5);
        PropertyPropertyGroup propPropObj2 = UniquePropertyPropertyGroupCreator.createPropertyPropertyGroupByPropertyGroup(
                globalCrudService(), PG2, 5);

        PropertyGroup propGroup1 = propPropObj1.getPropertyGroup();
        PropertyGroup propGroup2 = propPropObj2.getPropertyGroup();

        propPropSet1.add(propPropObj1);
        propPropSet2.add(propPropObj2);

        propertyGroupList.add(propGroup1);
        propertyGroupList.add(propGroup2);

        propGroup1.setPropertyPropertyGroups(propPropSet1);
        propGroup2.setPropertyPropertyGroups(propPropSet2);

        mockConfigService = mock(PacmanConfigParamsService.class);
        propertyGroupService.setConfigParamService(mockConfigService);
        service.setPacmanConfigParamsService(mockConfigService);

        when(
                mockConfigService.getParameterValueByClientLevel(GUIConfigParamName.CORE_PROPERTY_MAX_PROPERTY_PER_PROPERTY_GROUP.value(),
                        PlatformWorkContextTestHelper.WC_CLIENT_CODE_BLACKSTONE)).thenReturn("50");

        List<PropertyGroupOneTimeCleanUpDto> propertyGroupDtoList = service
                .synchronizePropertyGroupsForAllUsers(PlatformWorkContextTestHelper.WC_CLIENT_CODE_BLACKSTONE);

        assertNotNull(propertyGroupDtoList);
    }

    @Test
    public void principalsOffice() {
        Map<String, String> principalAttributes = service.visitThePrincipalsOffice();
        assertNotNull(principalAttributes);
    }

    @Test
    public void shouldDeleteUserAuthGroupAssociation() {
        AuthorizationGroup authGroup = new AuthorizationGroup();
        authGroup.setClientId(WC_CLIENT_ID_BLACKSTONE);
        authGroup.setName("The Tyranny of Evil Men");
        authGroup.setCreateDate(new Timestamp(new Date().getTime()));
        authGroup.setDescription("This is a test");
        authGroup.setStatusId(Constants.ACTIVE_STATUS_ID);
        authGroup.setCreatedByUserId(Integer.parseInt(WC_USER_ID_SSO));

        authGroup = globalCrudService().save(authGroup);
        assertNotNull(globalCrudService().find(AuthorizationGroup.class, authGroup.getId()));
        service.deleteUserAndAuthGroupAssociation(authGroup.getId());
    }

    @Test
    public void shouldDeleteRuleAndAuthGroupAssociation() {
        Integer clientId = PacmanWorkContextHelper.getClientId();
        int userId = Integer.parseInt(PacmanWorkContextHelper.getUserId());
        AuthorizationGroup authGroup = UniqueAuthorizationGroupCreator.createAuthorizationGroup(globalCrudService(), clientId, userId);
        ClientAttributeValue attriButeValue = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(),
                clientId);
        Rule rule = UniqueRuleCreator.createRule(globalCrudService(), clientId, userId);
        UniqueRuleAttributeValueMappingCreator.createRuleAttributeMapping(globalCrudService(), attriButeValue.getId(), rule.getId());
        authGroup.setRuleId(rule.getId());
        globalCrudService().save(authGroup);

        CustomAttributeService mockCustomAttributeService = new CustomAttributeService();
        mockCustomAttributeService.setGlobalCrudService(globalCrudService());
        service.setCustomAttributeService(mockCustomAttributeService);

        Query qryBefore = globalCrudService().getEntityManager().createNativeQuery("select * from rules where rule_id = :ruleId")
                .setParameter("ruleId", rule.getId());
        assertFalse(qryBefore.getResultList().isEmpty());

        service.deleteRuleAndAuthGroupAssociation(rule.getId());

        Query qryAfter = globalCrudService().getEntityManager().createNativeQuery("select * from rules where rule_id = :ruleId")
                .setParameter("ruleId", rule.getId());
        assertTrue(qryAfter.getResultList().isEmpty());
    }

    @Test
    public void shouldDeleteAuthorizationGroupWithRulesAndValues() {
        Integer clientId = PacmanWorkContextHelper.getClientId();
        int userId = Integer.parseInt(PacmanWorkContextHelper.getUserId());
        AuthorizationGroup authGroup = UniqueAuthorizationGroupCreator.createAuthorizationGroup(globalCrudService(), clientId, userId);
        ClientAttributeValue attriButeValue = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(),
                clientId);
        Rule rule = UniqueRuleCreator.createRule(globalCrudService(), clientId, userId);
        UniqueRuleAttributeValueMappingCreator.createRuleAttributeMapping(globalCrudService(), attriButeValue.getId(), rule.getId());
        authGroup.setRuleId(rule.getId());
        globalCrudService().save(authGroup);

        CustomAttributeService mockCustomAttributeService = new CustomAttributeService();
        mockCustomAttributeService.setGlobalCrudService(globalCrudService());
        service.setCustomAttributeService(mockCustomAttributeService);

        Client client = new Client();
        client.setId(PacmanWorkContextTestHelper.WC_CLIENT_ID_BLACKSTONE);
        when(mockClientConfigService.getClientByCode("unittest")).thenReturn(client);

        Query qryBefore = globalCrudService().getEntityManager().createNativeQuery("select * from rules where rule_id = :ruleId")
                .setParameter("ruleId", rule.getId());
        assertFalse(qryBefore.getResultList().isEmpty());

        service.deleteAuthorizationGroup(authGroup.getId());

        Query qryAfter = globalCrudService().getEntityManager().createNativeQuery("select * from rules where rule_id = :ruleId")
                .setParameter("ruleId", rule.getId());
        assertTrue(qryAfter.getResultList().isEmpty());

        AuthorizationGroup authGroupAfter = globalCrudService().find(AuthorizationGroup.class, authGroup.getId());
        assertNull(authGroupAfter);
        verify(uasService, never()).deleteAuthGroupInFDS(anyString());
    }

    @Test
    public void shouldDeleteAuthorizationGroupWithRulesAndValues_WithCode() {
        Client client = new Client();
        client.setId(10);
        client.setCode("Hilton");
        when(mockClientConfigService.getClientByCode(client.getCode())).thenReturn(client);

        int userId = Integer.parseInt(PacmanWorkContextHelper.getUserId());
        AuthorizationGroup authGroup = UniqueAuthorizationGroupCreator.createAuthorizationGroup(globalCrudService(), client.getId(), userId);
        ClientAttributeValue attriButeValue = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(),
                client.getId());
        Rule rule = UniqueRuleCreator.createRule(globalCrudService(), client.getId(), userId);
        UniqueRuleAttributeValueMappingCreator.createRuleAttributeMapping(globalCrudService(), attriButeValue.getId(), rule.getId());
        authGroup.setRuleId(rule.getId());
        globalCrudService().save(authGroup);

        CustomAttributeService mockCustomAttributeService = new CustomAttributeService();
        mockCustomAttributeService.setGlobalCrudService(globalCrudService());
        service.setCustomAttributeService(mockCustomAttributeService);

        Query qryBefore = globalCrudService().getEntityManager().createNativeQuery("select * from rules where rule_id = :ruleId")
                .setParameter("ruleId", rule.getId());
        assertFalse(qryBefore.getResultList().isEmpty());

        service.deleteAuthorizationGroup(authGroup.getId(), client.getCode());

        Query qryAfter = globalCrudService().getEntityManager().createNativeQuery("select * from rules where rule_id = :ruleId")
                .setParameter("ruleId", rule.getId());
        assertTrue(qryAfter.getResultList().isEmpty());

        AuthorizationGroup authGroupAfter = globalCrudService().find(AuthorizationGroup.class, authGroup.getId());
        assertNull(authGroupAfter);
        verify(uasService, never()).deleteAuthGroupInFDS(anyString());
    }

    @Test
    public void shouldDeleteAuthorizationGroupWithRulesAndValues_FDSEnabled() {
        Integer clientId = PacmanWorkContextHelper.getClientId();
        int userId = Integer.parseInt(PacmanWorkContextHelper.getUserId());
        AuthorizationGroup authGroup = UniqueAuthorizationGroupCreator.createAuthorizationGroup(globalCrudService(), clientId, userId);
        ClientAttributeValue attriButeValue = UniqueClientAttributeValueCreator.createUniqueClientAttributeValue(globalCrudService(),
                clientId);
        Rule rule = UniqueRuleCreator.createRule(globalCrudService(), clientId, userId);
        UniqueRuleAttributeValueMappingCreator.createRuleAttributeMapping(globalCrudService(), attriButeValue.getId(), rule.getId());
        authGroup.setRuleId(rule.getId());
        globalCrudService().save(authGroup);

        CustomAttributeService mockCustomAttributeService = new CustomAttributeService();
        mockCustomAttributeService.setGlobalCrudService(globalCrudService());
        service.setCustomAttributeService(mockCustomAttributeService);

        Client client = new Client();
        client.setId(PacmanWorkContextTestHelper.WC_CLIENT_ID_BLACKSTONE);
        when(mockClientConfigService.getClientByCode("unittest")).thenReturn(client);

        Query qryBefore = globalCrudService().getEntityManager().createNativeQuery("select * from rules where rule_id = :ruleId")
                .setParameter("ruleId", rule.getId());
        assertFalse(qryBefore.getResultList().isEmpty());

        service.deleteAuthorizationGroup(authGroup.getId());

        Query qryAfter = globalCrudService().getEntityManager().createNativeQuery("select * from rules where rule_id = :ruleId")
                .setParameter("ruleId", rule.getId());
        assertTrue(qryAfter.getResultList().isEmpty());

        AuthorizationGroup authGroupAfter = globalCrudService().find(AuthorizationGroup.class, authGroup.getId());
        assertNull(authGroupAfter);
        verify(uasService, never()).deleteAuthGroupInFDS(anyString());
    }

    @Test
    public void clearAuthorizationGroupsUasUuid() {
        CrudService mockCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockCrudService);

        AuthorizationGroup authorizationGroup = new AuthorizationGroup();
        authorizationGroup.setUasAuthGroupUuid(UUID.randomUUID().toString());
        AuthorizationGroup authorizationGroup2 = new AuthorizationGroup();
        authorizationGroup2.setUasAuthGroupUuid(UUID.randomUUID().toString());

        when(mockCrudService.findByNamedQuery(AuthorizationGroup.BY_CLIENT_ID, QueryParameter
                .with(AuthorizationGroup.PARAM_CLIENT_ID, 10).parameters())).thenReturn(Arrays.asList(authorizationGroup, authorizationGroup2));

        service.clearAuthorizationGroupsUasUuid(10);
        assertNull(authorizationGroup.getUasAuthGroupUuid());
        assertNull(authorizationGroup2.getUasAuthGroupUuid());
        verify(mockCrudService).save(Arrays.asList(authorizationGroup, authorizationGroup2));
    }

    @Test
    public void clearAuthorizationGroupsUasUuid_NoAuthGroups() {
        CrudService mockCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockCrudService);

        when(mockCrudService.findByNamedQuery(AuthorizationGroup.BY_CLIENT_ID, QueryParameter
                .with(AuthorizationGroup.PARAM_CLIENT_ID, 10).parameters())).thenReturn(null);

        service.clearAuthorizationGroupsUasUuid(10);
        verify(mockCrudService, never()).save(anyList());
    }

    @Test
    public void findClientProperties() {
        Client client = new Client();
        client.setId(2);
        client.setName("Name");
        client.setCode("Code");
        client.setStatus(Status.ACTIVE);

        List<Property> properties = service.findClientProperties(client);
        assertTrue(properties.size() > 0);

        Property property = properties.get(0);
        assertNotNull(property.getId());
        assertNotNull(property.getName());
        assertNotNull(property.getCode());
        assertNotNull(property.getStatus());

        Client propertyClient = property.getClient();
        assertEquals(client.getId(), propertyClient.getId());
        assertEquals(client.getName(), propertyClient.getName());
        assertEquals(client.getCode(), propertyClient.getCode());
        assertEquals(client.getStatus(), propertyClient.getStatus());
    }

    @Test
    public void shouldReturnReadWritePermissionForSSOUserForScheduleReportPage() {
        String dn = "uid=5001,ou=users,o=unittest,dc=ideas,dc=com";
        when(roleService.getPermsForUser(dn, WC_PROPERTY_ID_SHERATON_CARY.toString(), service)).thenReturn(Sets.newHashSet("-666"));

        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getInternalUser(WC_USER_ID_SUPER_DUPER));

        createTetrisPrincipal(WC_DN_SUPER_DUPER_USER, WC_CN_SUPER_DUPER_USER, WC_USER_ID_SUPER_DUPER, WC_CLIENT_CODE_TEST, true);

        workContext().setUserId(WC_USER_ID_SUPER_DUPER);
        workContext().setPropertyId(WC_PROPERTY_ID_SHERATON_CARY);

        String permissionForScheduleReportPage = service.getPermsForRequestedPage("schedule-report");
        Assertions.assertEquals(PermissionKeys.READ_WRITE_ACCESS.getLabel(), permissionForScheduleReportPage,
                "Super duper admin has read write permission to Schdule Report Page");
    }

    @Test
    public void shouldReturnNullForNoAccessPermissionForScheduleReportPage() {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getInternalUser(WC_USER_ID_SSO));
        createTetrisPrincipal(WC_DN_REGULAR_USER, WC_CN_REGULAR_USER, WC_USER_ID_SSO, WC_CLIENT_CODE_TEST, false);

        workContext().setUserId(WC_USER_ID_SSO);
        workContext().setPropertyId(WC_PROPERTY_ID_SHERATON_CARY);

        String permissionForScheduleReportPage = service.getPermsForRequestedPage("schedule-report");
        Assertions.assertEquals(null, permissionForScheduleReportPage,
                "Super duper admin has read write permission to Schdule Report Page");
    }

    @Test
    public void shouldReturnReadWriteForReadWritePermissionForScheduleReportPage() {
        createTetrisPrincipal(WC_DN_REGULAR_USER, WC_CN_REGULAR_USER, WC_USER_ID_SSO, WC_CLIENT_CODE_TEST, false);

        workContext().setUserId(WC_USER_ID_SSO);
        workContext().setPropertyId(WC_PROPERTY_ID_SHERATON_CARY);

        PacmanWorkContextHelper.setWorkContext(workContext());
        // service.queryManager = mockQueryManager;
        service.setPacmanConfigParamsService(configService);

        Set<String> perms = new HashSet<String>();
        perms.add("pageCode=information-manager&access=readOnly");
        perms.add("pageCode=business-analysis&access=readWrite");
        perms.add("pageCode=booking-pace-report&access=readWrite");
        perms.add("pageCode=input-override-report&access=readWrite");
        perms.add("pageCode=pick-up-change-and-differential-control-report&access=readWrite");
        perms.add("pageCode=schedule-reports&access=readWrite");
        perms.add("pageCode=demand-and-wash-management&access=readWrite");
        perms.add("pageCode=group-wash&access=readOnly");
        perms.add("pageCode=room-class-configuration&access=readOnly");

        when(configService.getBooleanParameterValue(FeatureTogglesConfigParamName.SCHEDULE_REPORT_ENABLED.value())).thenReturn(true);
        when(roleService.getPermsForUser(WC_DN_REGULAR_USER, WC_PROPERTY_ID_SHERATON_CARY.toString(), service)).thenReturn(perms);

        String permissionForScheduleReportPage = service.getPermsForRequestedPage("schedule-report");
        Assertions.assertEquals(PermissionKeys.READ_WRITE_ACCESS.getLabel(),
                permissionForScheduleReportPage, "User has read write permission to Schdule Report Page");
    }

    static Role getTestRole() {
        DateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String roleName = MessageFormat.format("TestRole{0}", DATE_FORMAT.format(Calendar.getInstance().getTime()));
        Role role = new Role();
        role.setRoleName(roleName);
        role.setDescription("This is a unit test role. Should have been reaped after test. Role Service Test.");
        return role;
    }

    private AuthorizationGroup getAllPropsAuthorizationGroup(List<AuthorizationGroup> groups) {
        AuthorizationGroup allPropsAuthorizationGroup = null;
        for (AuthorizationGroup group : groups) {
            if (group.getId() == AuthorizationGroup.ALL_PROP_ID) {
                allPropsAuthorizationGroup = group;
                break;
            }
        }
        return allPropsAuthorizationGroup;
    }

    private int getAllPropsAuthGroupQuantity(List<AuthorizationGroup> groups) {
        int matches = 0;
        for (AuthorizationGroup group : groups) {
            if (group.getId() == AuthorizationGroup.ALL_PROP_ID) {
                matches++;
            }
        }
        return matches;
    }

    private void mockConfigParameterService(String expectedReturnParameter, TestProperty testProperty, int noOfReturnTimes,
                                            String clientCode) {

        setWorkContextProperty(testProperty);
        when(mockConfigService.getValue("pacman." + clientCode, GUIConfigParamName.CORE_DISPLAY_PROPERTY_CODE_OR_NAME.value())).thenReturn(expectedReturnParameter);
        propertyGroupService.setConfigParamService(mockConfigService);
        service.setPacmanConfigParamsService(mockConfigService);
    }

    private TetrisPrincipal setupWorkContextForRegularUnitTestUser() {
        workContext().setUserId(WC_USER_ID_REGULAR);
        workContext().setClientId(WC_CLIENT_ID_DUMMY);
        workContext().setClientCode(WC_CLIENT_CODE_DUMMY);

        PacmanThreadLocalContextHolder.setWorkContext(workContext());

        TetrisPrincipal tp = createTetrisPrincipal(WC_DN_REGULAR_USER, WC_CN_REGULAR_USER, WC_USER_ID_REGULAR, WC_CLIENT_CODE_DUMMY, false);

        PacmanThreadLocalContextHolder.setPrincipal(tp);
        return tp;
    }

    private TetrisPrincipal setupWorkContextForBlkstnRevenueAnalyst() {
        workContext().setUserId(WC_USER_ID_BLACKSTONE_REV_ANALYST);
        workContext().setClientId(WC_CLIENT_ID_BLACKSTONE);

        PacmanThreadLocalContextHolder.setWorkContext(workContext());
        TetrisPrincipal tp = createTetrisPrincipal(WC_DN_BLKSTN_REVENUE_ANALYST, WC_CN_BLKSTN_REVENUE_ANALYST,
                WC_USER_ID_BLACKSTONE_REV_ANALYST, WC_CLIENT_CODE_BLACKSTONE, false);

        PacmanThreadLocalContextHolder.setPrincipal(tp);
        return tp;
    }

    private int howManyAuthGroupsAssignableByUser(List<AuthorizationGroup> groups) {
        int count = 0;
        // Can we just use a predicate?
        for (AuthorizationGroup group : groups) {
            if (group.isAssignableByTheCurrentUser())
                count++;
        }
        return count;
    }

    @Test
    public void testPersistNewAuthGroupWithoutRuleAndProperties() {
        AuthorizationGroup authorizationGroup = createEmptyAuthGroup();

        AuthorizationGroup expectedAuthorizationGroup = createEmptyAuthGroup();
        expectedAuthorizationGroup.setId(1);

        authService.setGlobalCrudService(globalCrudService);
        when(globalCrudService.save(authorizationGroup)).thenReturn(expectedAuthorizationGroup);
        authService.setPacmanConfigParamsService(pacmanConfigParamsService);
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.getParameterName())).thenReturn("false");

        AuthorizationGroup savedAuthorizationGroup = authService.persistAuthorizationGroup(authorizationGroup, null);

        Assertions.assertEquals(expectedAuthorizationGroup.getId(), savedAuthorizationGroup.getId());
        Assertions.assertEquals(expectedAuthorizationGroup.getName(), savedAuthorizationGroup.getName());
        Assertions.assertEquals(expectedAuthorizationGroup.getDescription(), savedAuthorizationGroup.getDescription());
        Assertions.assertEquals(expectedAuthorizationGroup.getStatusId(), savedAuthorizationGroup.getStatusId());

        verifyUpdateMethodsNeverCalled(authorizationGroup);
        verify(uasService, never()).migrateAuthGroupToFDS(savedAuthorizationGroup);
    }

    @Test
    public void testPersistNewAuthGroupWithoutRuleAndProperties_FDSEnabled() {
        AuthorizationGroup authorizationGroup = createEmptyAuthGroup();

        AuthorizationGroup expectedAuthorizationGroup = createEmptyAuthGroup();
        expectedAuthorizationGroup.setId(1);

        authService.setGlobalCrudService(globalCrudService);
        when(globalCrudService.save(authorizationGroup)).thenReturn(expectedAuthorizationGroup);
        authService.setPacmanConfigParamsService(pacmanConfigParamsService);
        authService.setUasService(uasService);
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.getParameterName())).thenReturn("true");

        AuthorizationGroup savedAuthorizationGroup = authService.persistAuthorizationGroup(authorizationGroup, null);

        Assertions.assertEquals(expectedAuthorizationGroup.getId(), savedAuthorizationGroup.getId());
        Assertions.assertEquals(expectedAuthorizationGroup.getName(), savedAuthorizationGroup.getName());
        Assertions.assertEquals(expectedAuthorizationGroup.getDescription(), savedAuthorizationGroup.getDescription());
        Assertions.assertEquals(expectedAuthorizationGroup.getStatusId(), savedAuthorizationGroup.getStatusId());

        verifyUpdateMethodsNeverCalled(authorizationGroup);
        verify(uasService).migrateAuthGroupToFDS(savedAuthorizationGroup);
    }

    private void verifyUpdateMethodsNeverCalled(AuthorizationGroup authorizationGroup) {
        verify(rulesService, never()).deleteRule(anyInt());
        verify(rulesService, never()).updatePropertySetFromAuthGroup(authorizationGroup);
    }

    @Test
    public void testPersistModifyAuthGroupWithoutRuleAndPropertiesWithOptimisationEnabled() {
        verifyPersistModifyAuthGroupWithoutRuleAndPropertiesWithOptimisationToggle(Boolean.TRUE);
    }

    @Test
    public void testPersistModifyAuthGroupWithoutRuleAndPropertiesWithOptimisationDisabled() {
        verifyPersistModifyAuthGroupWithoutRuleAndPropertiesWithOptimisationToggle(Boolean.FALSE);
    }

    private void verifyPersistModifyAuthGroupWithoutRuleAndPropertiesWithOptimisationToggle(Boolean isOptimisatonEnabled) {
        AuthorizationGroup authorizationGroup = createEmptyAuthGroup();
        authorizationGroup.setId(1);
        setDependentServices();
        Client client = buidClient(WC_CLIENT_CODE_TEST);

        when(mockPropertyGroupService.getAllRuleBasedPropertyGroupsForUser(Integer.parseInt(WC_USER_ID_SSO), WC_CLIENT_ID_DUMMY)).thenReturn(Collections.<PropertyGroup>emptyList());
        when(mockUserGlobalDBService.listUsersForAuthGroup(authorizationGroup.getId(), WC_CLIENT_CODE_TEST)).thenReturn(loadUserSet());
        when(clientConfigService.getClientByCode(WC_CLIENT_CODE_TEST)).thenReturn(client);
        when(globalCrudService.findByNamedQuerySingleResult(AuthorizationGroup.BY_ID,
                QueryParameter.with("id", authorizationGroup.getId()).parameters())).thenReturn(authorizationGroup);
        when(globalCrudService.save(authorizationGroup)).thenReturn(authorizationGroup);
        authService.setPacmanConfigParamsService(pacmanConfigParamsService);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IMPROVE_PERFORMANCE_FOR_AUTHORIZATION_GROUP)).thenReturn(isOptimisatonEnabled);
        authService.authGroupManagementService = authGroupManagementService;
        inject(authService, "asyncUserService", asyncUserService);
        AuthorizationGroup savedAuthorizationGroup = authService.persistAuthorizationGroup(authorizationGroup, null);
        Assertions.assertEquals(authorizationGroup.getId(), savedAuthorizationGroup.getId());
        Assertions.assertEquals(authorizationGroup.getName(), savedAuthorizationGroup.getName());
        Assertions.assertEquals(authorizationGroup.getDescription(), savedAuthorizationGroup.getDescription());
        Assertions.assertEquals(authorizationGroup.getStatusId(), savedAuthorizationGroup.getStatusId());

        verify(globalCrudService).save(authorizationGroup);
        verify(uasService, never()).deleteAuthGroupInFDS(any());
    }


    @Test
    public void testPersistModifyAuthGroupWithoutRuleAndPropertiesWithOptimisationEnabled_FDSEnabled() {
        verifyPersistModifyAuthGroupWithoutRuleAndPropertiesWithOptimisationToggle_FDSEnabled(Boolean.TRUE);
    }

    @Test
    public void testPersistModifyAuthGroupWithoutRuleAndPropertiesWithOptimisationDisabled_FDSEnabled() {
        verifyPersistModifyAuthGroupWithoutRuleAndPropertiesWithOptimisationToggle_FDSEnabled(Boolean.FALSE);
    }

    private void verifyPersistModifyAuthGroupWithoutRuleAndPropertiesWithOptimisationToggle_FDSEnabled(Boolean isOptimisatonEnabled) {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.getParameterName())).thenReturn("true");
        AuthorizationGroup authorizationGroup = createEmptyAuthGroup();
        authorizationGroup.setId(1);
        setDependentServices();
        Client client = buidClient(WC_CLIENT_CODE_TEST);

        //when(beanLocator.getPacmanBean(UserService.class, "UserService")).thenReturn(userService);
        when(authService.getBeanLocator()).thenReturn(beanLocator);
        when(mockPropertyGroupService.getAllRuleBasedPropertyGroupsForUser(Integer.parseInt(WC_USER_ID_SSO), WC_CLIENT_ID_DUMMY)).thenReturn(Collections.<PropertyGroup>emptyList());
        when(mockUserGlobalDBService.listUsersForAuthGroup(authorizationGroup.getId(), WC_CLIENT_CODE_TEST)).thenReturn(loadUserSet());
        when(clientConfigService.getClientByCode(WC_CLIENT_CODE_TEST)).thenReturn(client);
        when(globalCrudService.findByNamedQuerySingleResult(AuthorizationGroup.BY_ID,
                QueryParameter.with("id", authorizationGroup.getId()).parameters())).thenReturn(authorizationGroup);
        when(globalCrudService.save(authorizationGroup)).thenReturn(authorizationGroup);
        authService.setPacmanConfigParamsService(pacmanConfigParamsService);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IMPROVE_PERFORMANCE_FOR_AUTHORIZATION_GROUP)).thenReturn(isOptimisatonEnabled);
        authService.authGroupManagementService = authGroupManagementService;
        inject(authService, "asyncUserService", asyncUserService);
        AuthorizationGroup savedAuthorizationGroup = authService.persistAuthorizationGroup(authorizationGroup, null);
        Assertions.assertEquals(authorizationGroup.getId(), savedAuthorizationGroup.getId());
        Assertions.assertEquals(authorizationGroup.getName(), savedAuthorizationGroup.getName());
        Assertions.assertEquals(authorizationGroup.getDescription(), savedAuthorizationGroup.getDescription());
        Assertions.assertEquals(authorizationGroup.getStatusId(), savedAuthorizationGroup.getStatusId());

        verify(globalCrudService).save(authorizationGroup);
        verify(uasService).migrateAuthGroupToFDS(savedAuthorizationGroup);
    }

    @Test
    public void testPersistNewAuthGroupWithPropertiesButWithoutRule() {
        AuthorizationGroup authorizationGroup = createEmptyAuthGroup();
        addPropertiesToAuthGroup(authorizationGroup, 10, "Prop1", "BSTN");

        AuthorizationGroup expectedAuthorizationGroup = createEmptyAuthGroup();
        expectedAuthorizationGroup.setId(1);
        addPropertiesToAuthGroup(expectedAuthorizationGroup, 10, "Prop1", "BSTN");

        authService.setGlobalCrudService(globalCrudService);
        when(globalCrudService.save(authorizationGroup)).thenReturn(expectedAuthorizationGroup);
        authService.setPacmanConfigParamsService(pacmanConfigParamsService);
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.getParameterName())).thenReturn("false");

        AuthorizationGroup savedAuthorizationGroup = authService.persistAuthorizationGroup(authorizationGroup, null);

        Assertions.assertEquals(expectedAuthorizationGroup.getId(), savedAuthorizationGroup.getId());
        Assertions.assertEquals(expectedAuthorizationGroup.getName(), savedAuthorizationGroup.getName());
        Assertions.assertEquals(expectedAuthorizationGroup.getDescription(), savedAuthorizationGroup.getDescription());
        Assertions.assertEquals(expectedAuthorizationGroup.getStatusId(), savedAuthorizationGroup.getStatusId());
        Set<AuthorizationGroupPropertyMapping> authGroupPropertyMappings = savedAuthorizationGroup.getAuthGroupPropertyMappings();
        Assertions.assertEquals(expectedAuthorizationGroup.getAuthGroupPropertyMappings().size(), authGroupPropertyMappings.size());
        Assertions.assertEquals(new Integer(10), authGroupPropertyMappings.iterator().next().getPropertyId());

        verifyUpdateMethodsNeverCalled(authorizationGroup);
        verify(uasService, never()).migrateAuthGroupToFDS(savedAuthorizationGroup);
    }

    @Test
    public void testPersistNewAuthGroupWithPropertiesButWithoutRule_FDSEnabled() {
        AuthorizationGroup authorizationGroup = createEmptyAuthGroup();
        addPropertiesToAuthGroup(authorizationGroup, 10, "Prop1", "BSTN");

        AuthorizationGroup expectedAuthorizationGroup = createEmptyAuthGroup();
        expectedAuthorizationGroup.setId(1);
        addPropertiesToAuthGroup(expectedAuthorizationGroup, 10, "Prop1", "BSTN");

        authService.setGlobalCrudService(globalCrudService);
        when(globalCrudService.save(authorizationGroup)).thenReturn(expectedAuthorizationGroup);
        authService.setPacmanConfigParamsService(pacmanConfigParamsService);
        authService.setUasService(uasService);
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.getParameterName())).thenReturn("true");

        AuthorizationGroup savedAuthorizationGroup = authService.persistAuthorizationGroup(authorizationGroup, null);

        Assertions.assertEquals(expectedAuthorizationGroup.getId(), savedAuthorizationGroup.getId());
        Assertions.assertEquals(expectedAuthorizationGroup.getName(), savedAuthorizationGroup.getName());
        Assertions.assertEquals(expectedAuthorizationGroup.getDescription(), savedAuthorizationGroup.getDescription());
        Assertions.assertEquals(expectedAuthorizationGroup.getStatusId(), savedAuthorizationGroup.getStatusId());
        Set<AuthorizationGroupPropertyMapping> authGroupPropertyMappings = savedAuthorizationGroup.getAuthGroupPropertyMappings();
        Assertions.assertEquals(expectedAuthorizationGroup.getAuthGroupPropertyMappings().size(), authGroupPropertyMappings.size());
        Assertions.assertEquals(new Integer(10), authGroupPropertyMappings.iterator().next().getPropertyId());

        verifyUpdateMethodsNeverCalled(authorizationGroup);
        verify(uasService).migrateAuthGroupToFDS(savedAuthorizationGroup);
    }

    @Test
    public void testRemoveAddedPropertiesFromExistingAuthGroupWithoutRuleWithOptimisationEnabled() {
        verifyRemoveAddedPropertiesFromExistingAuthGroupWithoutRuleWithOptimisationToggle(Boolean.TRUE);
    }

    @Test
    public void testRemoveAddedPropertiesFromExistingAuthGroupWithoutRuleWithOptimisationDisabled() {
        verifyRemoveAddedPropertiesFromExistingAuthGroupWithoutRuleWithOptimisationToggle(Boolean.FALSE);
    }

    private void verifyRemoveAddedPropertiesFromExistingAuthGroupWithoutRuleWithOptimisationToggle(Boolean isOptimisationEnabled) {
        AuthorizationGroup authorizationGroup = createEmptyAuthGroup();
        authorizationGroup.setId(1);

        AuthorizationGroup savedAuthGroupInDB = createEmptyAuthGroup();
        addPropertiesToAuthGroup(savedAuthGroupInDB, 10, "Prop1", "BSTN");
        savedAuthGroupInDB.setId(1);

        AuthorizationGroup expectedAuthorizationGroup = createEmptyAuthGroup();
        expectedAuthorizationGroup.setId(1);
        addPropertiesToAuthGroup(expectedAuthorizationGroup, 10, "Prop1", "BSTN");

        setDependentServices();
        Client client = buidClient(WC_CLIENT_CODE_TEST);

        when(mockUserGlobalDBService.listUsersForAuthGroup(authorizationGroup.getId(), WC_CLIENT_CODE_TEST)).thenReturn(loadUserSet());
        when(clientConfigService.getClientByCode(WC_CLIENT_CODE_TEST)).thenReturn(client);
        when(globalCrudService.findByNamedQuerySingleResult(AuthorizationGroup.BY_ID,
                QueryParameter.with("id", authorizationGroup.getId()).parameters())).thenReturn(savedAuthGroupInDB);
        when(globalCrudService.save(authorizationGroup)).thenReturn(authorizationGroup);
        when(mockPropertyGroupService.getAllPropertyGroupsForUser(1, client.getId())).thenReturn(Collections.<PropertyGroup>emptyList());
        authService.authGroupManagementService = authGroupManagementService;
        inject(authService, "asyncUserService", asyncUserService);
        authService.setPacmanConfigParamsService(pacmanConfigParamsService);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IMPROVE_PERFORMANCE_FOR_AUTHORIZATION_GROUP)).thenReturn(isOptimisationEnabled);
        AuthorizationGroup savedAuthorizationGroup = authService.persistAuthorizationGroup(authorizationGroup, null);

        Assertions.assertEquals(authorizationGroup.getId(), savedAuthorizationGroup.getId());
        Assertions.assertEquals(authorizationGroup.getName(), savedAuthorizationGroup.getName());
        Assertions.assertEquals(authorizationGroup.getDescription(), savedAuthorizationGroup.getDescription());
        Assertions.assertEquals(authorizationGroup.getStatusId(), savedAuthorizationGroup.getStatusId());
        Assertions.assertTrue(savedAuthorizationGroup.getAuthGroupPropertyMappings().isEmpty());

        verify(globalCrudService).save(authorizationGroup);
        verify(uasService, never()).deleteAuthGroupInFDS(any());
    }

    @Test
    public void testRemoveAddedPropertiesFromExistingAuthGroupWithoutRuleWithOptimisationEnabled_FDSEnabled() {
        verifyRemoveAddedPropertiesFromExistingAuthGroupWithoutRuleWithOptimisationToggle_FDSEnabled(Boolean.TRUE);
    }

    @Test
    public void testRemoveAddedPropertiesFromExistingAuthGroupWithoutRuleWithOptimisationDisabled_FDSEnabled() {
        verifyRemoveAddedPropertiesFromExistingAuthGroupWithoutRuleWithOptimisationToggle_FDSEnabled(Boolean.FALSE);
    }

    private void verifyRemoveAddedPropertiesFromExistingAuthGroupWithoutRuleWithOptimisationToggle_FDSEnabled(Boolean isOptimisationEnabled) {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.getParameterName())).thenReturn("true");
        AuthorizationGroup authorizationGroup = createEmptyAuthGroup();
        authorizationGroup.setId(1);

        AuthorizationGroup savedAuthGroupInDB = createEmptyAuthGroup();
        addPropertiesToAuthGroup(savedAuthGroupInDB, 10, "Prop1", "BSTN");
        savedAuthGroupInDB.setId(1);

        AuthorizationGroup expectedAuthorizationGroup = createEmptyAuthGroup();
        expectedAuthorizationGroup.setId(1);
        addPropertiesToAuthGroup(expectedAuthorizationGroup, 10, "Prop1", "BSTN");

        setDependentServices();
        Client client = buidClient(WC_CLIENT_CODE_TEST);

        //when(beanLocator.getPacmanBean(UserService.class, "UserService")).thenReturn(userService);
        when(authService.getBeanLocator()).thenReturn(beanLocator);
        when(mockUserGlobalDBService.listUsersForAuthGroup(authorizationGroup.getId(), WC_CLIENT_CODE_TEST)).thenReturn(loadUserSet());
        when(clientConfigService.getClientByCode(WC_CLIENT_CODE_TEST)).thenReturn(client);
        when(globalCrudService.findByNamedQuerySingleResult(AuthorizationGroup.BY_ID,
                QueryParameter.with("id", authorizationGroup.getId()).parameters())).thenReturn(savedAuthGroupInDB);
        when(globalCrudService.save(authorizationGroup)).thenReturn(authorizationGroup);
        when(mockPropertyGroupService.getAllPropertyGroupsForUser(1, client.getId())).thenReturn(Collections.<PropertyGroup>emptyList());
        authService.authGroupManagementService = authGroupManagementService;
        inject(authService, "asyncUserService", asyncUserService);
        authService.setPacmanConfigParamsService(pacmanConfigParamsService);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IMPROVE_PERFORMANCE_FOR_AUTHORIZATION_GROUP)).thenReturn(isOptimisationEnabled);

        AuthorizationGroup savedAuthorizationGroup = authService.persistAuthorizationGroup(authorizationGroup, null);

        Assertions.assertEquals(authorizationGroup.getId(), savedAuthorizationGroup.getId());
        Assertions.assertEquals(authorizationGroup.getName(), savedAuthorizationGroup.getName());
        Assertions.assertEquals(authorizationGroup.getDescription(), savedAuthorizationGroup.getDescription());
        Assertions.assertEquals(authorizationGroup.getStatusId(), savedAuthorizationGroup.getStatusId());
        Assertions.assertTrue(savedAuthorizationGroup.getAuthGroupPropertyMappings().isEmpty());

        verify(globalCrudService).save(authorizationGroup);
        verify(uasService).migrateAuthGroupToFDS(authorizationGroup);
    }

    @Test
    public void testUpdateAddedPropertiesFromExistingAuthGroupWithoutRuleWithOptimisationEnabled() {
        verifyUpdateAddedPropertiesFromExistingAuthGroupWithoutRuleWithOptimisationToggle(Boolean.TRUE);
    }

    @Test
    public void testUpdateAddedPropertiesFromExistingAuthGroupWithoutRuleWithOptimisationDisabled() {
        verifyUpdateAddedPropertiesFromExistingAuthGroupWithoutRuleWithOptimisationToggle(Boolean.FALSE);
    }

    private void verifyUpdateAddedPropertiesFromExistingAuthGroupWithoutRuleWithOptimisationToggle(Boolean isOptimisationEnabled) {
        updateWorkContext(workContext(), WC_USER_ID_SSO, WC_CLIENT_ID_BLACKSTONE, WC_CLIENT_CODE_BLACKSTONE, WC_PROPERTY_ID_PUNE, WC_PROPERTY_CODE_PUNE);
        AuthorizationGroup authorizationGroup = createEmptyAuthGroup();
        Property property = buildProperty(11, "Prop2", "BSTN");
        addPropertiesToAuthGroup(authorizationGroup, 11, "Prop2", "BSTN");
        authorizationGroup.setId(1);
        authorizationGroup.setStatusId(1);
        AuthorizationGroupPropertyMapping groupPropertyMapping = buildPropertyMapping(authorizationGroup, property.getId(), property.getCode(), property.getClient().getCode());

        AuthorizationGroup savedAuthGroupInDB = createEmptyAuthGroup();
        addPropertiesToAuthGroup(savedAuthGroupInDB, 10, "Prop1", "BSTN");
        savedAuthGroupInDB.setId(1);

        setDependentServices();

        Client client = buidClient(clientCode_BSTN);

        when(globalCrudService.save(groupPropertyMapping)).thenReturn(groupPropertyMapping);
        when(globalCrudService.findByNamedQuerySingleResult(Property.BY_ID_WITH_CLIENT_AND_DB_LOC,
                QueryParameter.with(Property.PARAM_PROPERTY_ID, 11).parameters())).thenReturn(property);
        when(rulesService.deleteRule(savedAuthGroupInDB.getRuleId())).thenReturn(true);
        when(globalCrudService.delete(AuthorizationGroupPropertyMapping.class, 1)).thenReturn(true);
        when(clientConfigService.getClientByCode(clientCode_BSTN)).thenReturn(client);
        when(globalCrudService.findByNamedQuerySingleResult(AuthorizationGroup.BY_ID,
                QueryParameter.with("id", authorizationGroup.getId()).parameters())).thenReturn(savedAuthGroupInDB);
        when(globalCrudService.save(authorizationGroup)).thenReturn(authorizationGroup);
        when(mockUserGlobalDBService.listUsersForAuthGroup(authorizationGroup.getId(), WC_CLIENT_CODE_TEST)).thenReturn(loadUserSet());
        authService.authGroupManagementService = authGroupManagementService;
        authService.setPacmanConfigParamsService(pacmanConfigParamsService);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IMPROVE_PERFORMANCE_FOR_AUTHORIZATION_GROUP)).thenReturn(isOptimisationEnabled);
        AuthorizationGroup savedAuthorizationGroup = authService.persistAuthorizationGroup(authorizationGroup, null);

        Assertions.assertEquals(new Integer(1), savedAuthorizationGroup.getId());
        Assertions.assertEquals("TestAuthGroup", savedAuthorizationGroup.getName());
        Assertions.assertEquals("AuthGrpDesc", savedAuthorizationGroup.getDescription());
        Assertions.assertEquals(new Integer(1), savedAuthorizationGroup.getStatusId());
        Set<AuthorizationGroupPropertyMapping> authGroupPropertyMappings = savedAuthorizationGroup.getAuthGroupPropertyMappings();
        Assertions.assertEquals(1, authGroupPropertyMappings.size());
        Assertions.assertEquals(new Integer(11), authGroupPropertyMappings.iterator().next().getPropertyId());

        verify(globalCrudService).save(authorizationGroup);
        verify(uasService, never()).deleteAuthGroupInFDS(any());
    }

    @Test
    public void testUpdateAddedPropertiesFromExistingAuthGroupWithoutRuleWithOptimisationEnabled_FDSEnabled() {
        verifyUpdateAddedPropertiesFromExistingAuthGroupWithoutRuleWithOptimisationToggle_FDSEnabled(Boolean.TRUE);
    }

    @Test
    public void testUpdateAddedPropertiesFromExistingAuthGroupWithoutRuleWithOptimisationDisabled_FDSEnabled() {
        verifyUpdateAddedPropertiesFromExistingAuthGroupWithoutRuleWithOptimisationToggle_FDSEnabled(Boolean.FALSE);
    }

    private void verifyUpdateAddedPropertiesFromExistingAuthGroupWithoutRuleWithOptimisationToggle_FDSEnabled(Boolean isOptimisationEnabled) {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.getParameterName())).thenReturn("true");
        updateWorkContext(workContext(), WC_USER_ID_SSO, WC_CLIENT_ID_BLACKSTONE, WC_CLIENT_CODE_BLACKSTONE, WC_PROPERTY_ID_PUNE, WC_PROPERTY_CODE_PUNE);
        AuthorizationGroup authorizationGroup = createEmptyAuthGroup();
        Property property = buildProperty(11, "Prop2", "BSTN");
        addPropertiesToAuthGroup(authorizationGroup, 11, "Prop2", "BSTN");
        authorizationGroup.setId(1);
        authorizationGroup.setStatusId(1);
        AuthorizationGroupPropertyMapping groupPropertyMapping = buildPropertyMapping(authorizationGroup, property.getId(), property.getCode(), property.getClient().getCode());

        AuthorizationGroup savedAuthGroupInDB = createEmptyAuthGroup();
        addPropertiesToAuthGroup(savedAuthGroupInDB, 10, "Prop1", "BSTN");
        savedAuthGroupInDB.setId(1);

        setDependentServices();

        Client client = buidClient(clientCode_BSTN);

        when(globalCrudService.save(groupPropertyMapping)).thenReturn(groupPropertyMapping);
        when(globalCrudService.findByNamedQuerySingleResult(Property.BY_ID_WITH_CLIENT_AND_DB_LOC,
                QueryParameter.with(Property.PARAM_PROPERTY_ID, 11).parameters())).thenReturn(property);
        when(rulesService.deleteRule(savedAuthGroupInDB.getRuleId())).thenReturn(true);
        when(globalCrudService.delete(AuthorizationGroupPropertyMapping.class, 1)).thenReturn(true);
        when(clientConfigService.getClientByCode(clientCode_BSTN)).thenReturn(client);
        when(globalCrudService.findByNamedQuerySingleResult(AuthorizationGroup.BY_ID,
                QueryParameter.with("id", authorizationGroup.getId()).parameters())).thenReturn(savedAuthGroupInDB);
        when(globalCrudService.save(authorizationGroup)).thenReturn(authorizationGroup);
        when(mockUserGlobalDBService.listUsersForAuthGroup(authorizationGroup.getId(), WC_CLIENT_CODE_TEST)).thenReturn(loadUserSet());
        authService.authGroupManagementService = authGroupManagementService;
        authService.setPacmanConfigParamsService(pacmanConfigParamsService);
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IMPROVE_PERFORMANCE_FOR_AUTHORIZATION_GROUP)).thenReturn(isOptimisationEnabled);
        AuthorizationGroup savedAuthorizationGroup = authService.persistAuthorizationGroup(authorizationGroup, null);

        Assertions.assertEquals(new Integer(1), savedAuthorizationGroup.getId());
        Assertions.assertEquals("TestAuthGroup", savedAuthorizationGroup.getName());
        Assertions.assertEquals("AuthGrpDesc", savedAuthorizationGroup.getDescription());
        Assertions.assertEquals(new Integer(1), savedAuthorizationGroup.getStatusId());
        Set<AuthorizationGroupPropertyMapping> authGroupPropertyMappings = savedAuthorizationGroup.getAuthGroupPropertyMappings();
        Assertions.assertEquals(1, authGroupPropertyMappings.size());
        Assertions.assertEquals(new Integer(11), authGroupPropertyMappings.iterator().next().getPropertyId());

        verify(globalCrudService).save(authorizationGroup);
        verify(uasService).migrateAuthGroupToFDS(savedAuthorizationGroup);
    }

    @Test
    public void testPersistNewAuthGroupWithRule() {
        AuthorizationGroup authorizationGroup = createEmptyAuthGroup();
        List<CustomAttributeSearchCriteria> criteria = buildCriteria(authorizationGroup);

        AuthorizationGroup expectedAuthorizationGroup = createEmptyAuthGroup();
        expectedAuthorizationGroup.setRuleId(1);
        expectedAuthorizationGroup.setId(1);
        expectedAuthorizationGroup.setStatusId(1);
        addPropertiesToAuthGroup(expectedAuthorizationGroup, 1, "H1", "BSTN");
        setDependentServices();
        rulesService.setSearcher(searcher);

        when(globalCrudService.findByNamedQuerySingleResult(Property.BY_ID_WITH_CLIENT_AND_DB_LOC,
                QueryParameter.with("propertyId", 1).parameters())).thenReturn(buildProperty(1, "H1", "BSTN"));
        when(searcher.searchForPropertiesByCustomAttributeValue(criteria)).thenReturn(new ArrayList<Integer>(1));
        when(globalCrudService.find(Rule.class, 1)).thenReturn(null);
        when(rulesService.saveSearchCriteria(criteria)).thenReturn(1);
        when(globalCrudService.save(authorizationGroup)).thenReturn(expectedAuthorizationGroup);
        authService.setPacmanConfigParamsService(pacmanConfigParamsService);
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.getParameterName())).thenReturn("false");

        AuthorizationGroup savedAuthorizationGroup = authService.persistAuthorizationGroup(authorizationGroup, criteria);
        verify(rulesService).updatePropertySet(expectedAuthorizationGroup);
        Assertions.assertEquals(new Integer(1), savedAuthorizationGroup.getId());
        Assertions.assertEquals(new Integer(1), savedAuthorizationGroup.getRuleId());
        Assertions.assertEquals(1, savedAuthorizationGroup.getAuthGroupPropertyMappings().size());

        verifyUpdateMethodsNeverCalled(authorizationGroup);
        verify(uasService, never()).migrateAuthGroupToFDS(savedAuthorizationGroup);
    }

    @Test
    public void testPersistNewAuthGroupWithRule_FDSEnabled() {
        AuthorizationGroup authorizationGroup = createEmptyAuthGroup();
        List<CustomAttributeSearchCriteria> criteria = buildCriteria(authorizationGroup);

        AuthorizationGroup expectedAuthorizationGroup = createEmptyAuthGroup();
        expectedAuthorizationGroup.setRuleId(1);
        expectedAuthorizationGroup.setId(1);
        expectedAuthorizationGroup.setStatusId(1);
        addPropertiesToAuthGroup(expectedAuthorizationGroup, 1, "H1", "BSTN");
        setDependentServices();
        rulesService.setSearcher(searcher);

        when(globalCrudService.findByNamedQuerySingleResult(Property.BY_ID_WITH_CLIENT_AND_DB_LOC,
                QueryParameter.with("propertyId", 1).parameters())).thenReturn(buildProperty(1, "H1", "BSTN"));
        when(searcher.searchForPropertiesByCustomAttributeValue(criteria)).thenReturn(new ArrayList<Integer>(1));
        when(globalCrudService.find(Rule.class, 1)).thenReturn(null);
        when(rulesService.saveSearchCriteria(criteria)).thenReturn(1);
        when(globalCrudService.save(authorizationGroup)).thenReturn(expectedAuthorizationGroup);
        authService.setPacmanConfigParamsService(pacmanConfigParamsService);
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.getParameterName())).thenReturn("true");

        AuthorizationGroup savedAuthorizationGroup = authService.persistAuthorizationGroup(authorizationGroup, criteria);
        verify(rulesService).updatePropertySet(expectedAuthorizationGroup);
        Assertions.assertEquals(new Integer(1), savedAuthorizationGroup.getId());
        Assertions.assertEquals(new Integer(1), savedAuthorizationGroup.getRuleId());
        Assertions.assertEquals(1, savedAuthorizationGroup.getAuthGroupPropertyMappings().size());

        verifyUpdateMethodsNeverCalled(authorizationGroup);
        verify(uasService).migrateAuthGroupToFDS(savedAuthorizationGroup);
    }

    @Test
    public void testUpdateAuthGroupWithRuleWhenOptimisationFalse() {
        verifyUpdateAuthGroupWithRuleWithOptimisationToggle(Boolean.FALSE);
    }

    @Test
    public void testUpdateAuthGroupWithRuleWhenOptimisationTrue() {
        verifyUpdateAuthGroupWithRuleWithOptimisationToggle(Boolean.TRUE);
    }

    private void verifyUpdateAuthGroupWithRuleWithOptimisationToggle(Boolean isOptimisationEnabled) {
        AuthorizationGroup authorizationGroup = createEmptyAuthGroup();
        authorizationGroup.setRuleId(1);
        authorizationGroup.setId(1);
        authorizationGroup.setStatusId(1);
        addPropertiesToAuthGroup(authorizationGroup, 2, "H2", "BSTN");

        List<CustomAttributeSearchCriteria> criteria = buildCriteria(authorizationGroup);

        AuthorizationGroup expectedAuthorizationGroup = createEmptyAuthGroup();
        expectedAuthorizationGroup.setRuleId(2);
        expectedAuthorizationGroup.setId(1);
        expectedAuthorizationGroup.setStatusId(1);
        addPropertiesToAuthGroup(expectedAuthorizationGroup, 1, "H1", "BSTN");

        setDependentServices();
        rulesService.setSearcher(searcher);
        Client client = buidClient(WC_CLIENT_CODE_TEST);

        when(clientConfigService.getClientByCode(WC_CLIENT_CODE_TEST)).thenReturn(client);
        when(globalCrudService.findByNamedQuerySingleResult(AuthorizationGroup.BY_ID,
                QueryParameter.with("id", authorizationGroup.getId()).parameters())).thenReturn(authorizationGroup);
        when(globalCrudService.findByNamedQuerySingleResult(Property.BY_ID_WITH_CLIENT_AND_DB_LOC,
                QueryParameter.with("propertyId", 1).parameters())).thenReturn(buildProperty(1, "H1", "BSTN"));
        when(searcher.searchForPropertiesByCustomAttributeValue(criteria)).thenReturn(new ArrayList<Integer>(1));
        when(globalCrudService.find(Rule.class, 1)).thenReturn(null);
        when(rulesService.saveSearchCriteria(criteria)).thenReturn(1);
        when(globalCrudService.save(authorizationGroup)).thenReturn(expectedAuthorizationGroup);
        when(mockPropertyGroupService.getAllRuleBasedPropertyGroupsForUser(1, 2)).thenReturn(new ArrayList<PropertyGroup>());
        authService.setPacmanConfigParamsService(pacmanConfigParamsService);
        authService.authGroupManagementService = authGroupManagementService;
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IMPROVE_PERFORMANCE_FOR_AUTHORIZATION_GROUP)).thenReturn(isOptimisationEnabled);
        AuthorizationGroup savedAuthorizationGroup = authService.persistAuthorizationGroup(authorizationGroup, criteria);
        verify(rulesService).updateSearchCriteria(authorizationGroup.getRuleId(), criteria);
        verify(rulesService).updatePropertySetFromAuthGroup(authorizationGroup);
        Assertions.assertEquals(new Integer(1), savedAuthorizationGroup.getId());
        Assertions.assertEquals(new Integer(2), savedAuthorizationGroup.getRuleId());
        Set<AuthorizationGroupPropertyMapping> authGroupPropertyMappings = savedAuthorizationGroup.getAuthGroupPropertyMappings();
        Assertions.assertEquals(1, authGroupPropertyMappings.size());
        Assertions.assertEquals(Integer.valueOf(1), authGroupPropertyMappings.iterator().next().getPropertyId());
        verify(globalCrudService).save(authorizationGroup);
        verify(uasService, never()).deleteAuthGroupInFDS(any());
    }

    @Test
    public void testUpdateAuthGroupWithRuleWhenOptimisationFalse_FDSEnabled() {
        verifyUpdateAuthGroupWithRuleWithOptimisationToggle_FDSEnabled(Boolean.FALSE);
    }

    @Test
    public void testUpdateAuthGroupWithRuleWhenOptimisationTrue_FDSEnabled() {
        verifyUpdateAuthGroupWithRuleWithOptimisationToggle_FDSEnabled(Boolean.TRUE);
    }

    private void verifyUpdateAuthGroupWithRuleWithOptimisationToggle_FDSEnabled(Boolean isOptimisationEnabled) {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.getParameterName())).thenReturn("true");
        AuthorizationGroup authorizationGroup = createEmptyAuthGroup();
        authorizationGroup.setRuleId(1);
        authorizationGroup.setId(1);
        authorizationGroup.setStatusId(1);
        addPropertiesToAuthGroup(authorizationGroup, 2, "H2", "BSTN");

        List<CustomAttributeSearchCriteria> criteria = buildCriteria(authorizationGroup);

        AuthorizationGroup expectedAuthorizationGroup = createEmptyAuthGroup();
        expectedAuthorizationGroup.setRuleId(2);
        expectedAuthorizationGroup.setId(1);
        expectedAuthorizationGroup.setStatusId(1);
        addPropertiesToAuthGroup(expectedAuthorizationGroup, 1, "H1", "BSTN");

        setDependentServices();
        rulesService.setSearcher(searcher);
        Client client = buidClient(WC_CLIENT_CODE_TEST);

        when(clientConfigService.getClientByCode(WC_CLIENT_CODE_TEST)).thenReturn(client);
        when(globalCrudService.findByNamedQuerySingleResult(AuthorizationGroup.BY_ID,
                QueryParameter.with("id", authorizationGroup.getId()).parameters())).thenReturn(authorizationGroup);
        when(globalCrudService.findByNamedQuerySingleResult(Property.BY_ID_WITH_CLIENT_AND_DB_LOC,
                QueryParameter.with("propertyId", 1).parameters())).thenReturn(buildProperty(1, "H1", "BSTN"));
        when(searcher.searchForPropertiesByCustomAttributeValue(criteria)).thenReturn(new ArrayList<Integer>(1));
        when(globalCrudService.find(Rule.class, 1)).thenReturn(null);
        when(rulesService.saveSearchCriteria(criteria)).thenReturn(1);
        when(globalCrudService.save(authorizationGroup)).thenReturn(expectedAuthorizationGroup);
        when(mockPropertyGroupService.getAllRuleBasedPropertyGroupsForUser(1, 2)).thenReturn(new ArrayList<PropertyGroup>());
        authService.setPacmanConfigParamsService(pacmanConfigParamsService);
        authService.authGroupManagementService = authGroupManagementService;
        when(pacmanConfigParamsService.getBooleanParameterValue(PreProductionConfigParamName.IMPROVE_PERFORMANCE_FOR_AUTHORIZATION_GROUP)).thenReturn(isOptimisationEnabled);
        AuthorizationGroup savedAuthorizationGroup = authService.persistAuthorizationGroup(authorizationGroup, criteria);
        verify(rulesService).updateSearchCriteria(authorizationGroup.getRuleId(), criteria);
        verify(rulesService).updatePropertySetFromAuthGroup(authorizationGroup);
        Assertions.assertEquals(new Integer(1), savedAuthorizationGroup.getId());
        Assertions.assertEquals(new Integer(2), savedAuthorizationGroup.getRuleId());
        Set<AuthorizationGroupPropertyMapping> authGroupPropertyMappings = savedAuthorizationGroup.getAuthGroupPropertyMappings();
        Assertions.assertEquals(1, authGroupPropertyMappings.size());
        Assertions.assertEquals(Integer.valueOf(1), authGroupPropertyMappings.iterator().next().getPropertyId());
        verify(globalCrudService).save(authorizationGroup);
        verify(uasService).migrateAuthGroupToFDS(savedAuthorizationGroup);
    }

    private void setDependentServices() {
        authService.setPropertyGroupService(mockPropertyGroupService);
        authService.setGlobalCrudService(globalCrudService);
        authService.setRulesService(rulesService);
        authService.setClientConfigService(clientConfigService);
        authService.setUserGlobalDBService(mockUserGlobalDBService);
        authService.setUserAuthorizedPropertyCache(userAuthorizedPropertyCache);
        authService.setUasService(uasService);
    }

    private Set<LDAPUser> loadUserSet() {
        Set<LDAPUser> users = new HashSet<LDAPUser>();
        LDAPUser u1 = new LDAPUser();
        u1.setUserId(1);
        LDAPUser u2 = new LDAPUser();
        u2.setUserId(2);
        users.add(u1);
        users.add(u2);
        return users;
    }

    private List<CustomAttributeSearchCriteria> buildCriteria(AuthorizationGroup authorizationGroup) {
        List<CustomAttributeSearchCriteria> searchCriteria = new ArrayList<CustomAttributeSearchCriteria>();
        CustomAttributeSearchCriteria equalsHilton = new CustomAttributeSearchCriteria();
        equalsHilton.setClientAttributeValueId(1);
        equalsHilton.setOperator(SearchOperator.EQUALS);
        equalsHilton.setJoinCondition(SearchJoinCondition.AND);

        CustomAttributeSearchCriteria notEqualsSheraton = new CustomAttributeSearchCriteria();
        notEqualsSheraton.setClientAttributeValueId(2);
        notEqualsSheraton.setOperator(SearchOperator.NOT_EQUALS);

        searchCriteria.add(equalsHilton);
        searchCriteria.add(notEqualsSheraton);

        return searchCriteria;
    }

    private AuthorizationGroup createEmptyAuthGroup() {
        AuthorizationGroup authorizationGroup = new AuthorizationGroup();
        authorizationGroup.setName("TestAuthGroup");
        authorizationGroup.setDescription("AuthGrpDesc");
        authorizationGroup.setAuthGroupPropertyMappings(new HashSet<AuthorizationGroupPropertyMapping>());
        return authorizationGroup;
    }

    private void addPropertiesToAuthGroup(AuthorizationGroup authorizationGroup, Integer propertyId, String propertyCode, String clientCode) {
        Set<AuthorizationGroupPropertyMapping> authorizationGroupPropertyMappings = new HashSet<AuthorizationGroupPropertyMapping>();
        AuthorizationGroupPropertyMapping groupPropertyMapping = buildPropertyMapping(authorizationGroup, propertyId, propertyCode, clientCode);
        authorizationGroupPropertyMappings.add(groupPropertyMapping);
        if (null == authorizationGroup.getAuthGroupPropertyMappings()) {
            authorizationGroup.setAuthGroupPropertyMappings(authorizationGroupPropertyMappings);
        } else {
            authorizationGroup.getAuthGroupPropertyMappings().addAll(authorizationGroupPropertyMappings);
        }
    }

    private AuthorizationGroupPropertyMapping buildPropertyMapping(AuthorizationGroup authorizationGroup, Integer propertyId, String propertyCode, String clientCode) {
        AuthorizationGroupPropertyMapping groupPropertyMapping = new AuthorizationGroupPropertyMapping();
        groupPropertyMapping.setId(1);
        groupPropertyMapping.setAuthorizationGroup(authorizationGroup);
        groupPropertyMapping.setPropertyId(buildProperty(propertyId, propertyCode, clientCode).getId());
        groupPropertyMapping.setStatusId(1);
        return groupPropertyMapping;
    }

    private Property buildProperty(Integer id, String propertyCode, String clientCode) {
        Property property = new Property();
        property.setCode(propertyCode);
        property.setId(id);
        property.setName(propertyCode);
        property.setClient(buidClient(clientCode));
        return property;
    }

    private Client buidClient(String clientCode) {
        Client client = new Client();
        client.setCode(clientCode);
        client.setId(1);
        client.setName("some client");
        return client;
    }

    @Test
    public void shouldReturnFeaturePermsMap() {
        mockConfigService = mock(PacmanConfigParamsService.class);
        service.setPacmanConfigParamsService(mockConfigService);
        when(mockConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.INVENTORY_HISTORY_REPORT_ENABLED.value())).thenReturn(true);
        when(mockConfigService.getBooleanParameterValue(FeatureTogglesConfigParamName.FORECAST_VALIDATION_REPORT_ENABLED.value())).thenReturn(false);
        final Map<String, Boolean> permsFeatureMapping = service.getPermsFeatureMappingForContext(false);
        assertEquals(true, permsFeatureMapping.get(AuthorizationService.INVENTORY_HISTORY_REPORT_PERMISSION_KEY));
        assertEquals(false, permsFeatureMapping.get(AuthorizationService.FORECAST_VALIDATION_REPORT_PERMISSION_KEY));
    }

    @Test
    public void shouldReturnTrueIfAtLeastOnePropertyHasAccessToFeature() {
        service.setCrudService(tenantCrudService());
        service.setGlobalCrudService(globalCrudService());
        PacmanThreadLocalContextHolder.getWorkContext().setClientCode("BSTN");
        insertRecordInConfigParamValue("pacman.feature.isContinuousPricingEnabled", "pacman.BSTN.H1");
        boolean CPTurnedOnAtPropertyLevel = service.atLeastOnePropertyHasAccessToFeature("pacman.feature.isContinuousPricingEnabled");
        assertTrue(CPTurnedOnAtPropertyLevel, "isContinuousPricingEnabled is not turned on for any specific property for BSTN");
    }

    private void insertRecordInConfigParamValue(String featureName, String context) {
        globalCrudService().executeUpdateByNativeQuery("INSERT INTO [dbo].[Config_Parameter_Value] " +
                "([Config_Parameter_ID],[Context],[FixedValue],[Config_Parameter_Predefined_Value_ID], [Created_By_User_ID],[Last_Updated_By_User_ID]) " +
                "VALUES ((SELECT [Config_Parameter_ID] from [dbo].[Config_Parameter] where Name='" + featureName + "'), '" + context + "', NULL, 1000, '11403','11403');");
    }

    @Test
    public void shouldReturnAllPermissionForSSOUser() {
        PacmanWorkContextHelper.setPropertyId(null);

        CrudService mockCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockCrudService);

        when(roleService.getPermsForUser(WC_DN_SSO_USER, WC_PROPERTY_ID_PUNE.toString(), service)).thenReturn(
                Sets.newHashSet(Role.ALL_PERMS_ID));
        updateWorkContext(workContext(), WC_USER_ID_SSO, WC_CLIENT_ID_DUMMY, WC_CLIENT_CODE_TEST,
                WC_PROPERTY_ID_PUNE, WC_PROPERTY_CODE_PUNE);

        createTetrisPrincipal(WC_DN_SSO_USER, WC_CN_SSO_USER, WC_USER_ID_SSO, WC_CLIENT_CODE_TEST, false);

        Set<String> perms = service.getPermsForContext(false);

        assertNotNull(perms);
        assertEquals(1, perms.size(), "Should only be 1 perm");
        assertTrue(perms.contains(Role.ALL_PERMS_ID), "Should be the special no client props perm");
    }

    @Test
    public void shouldReturnGivenPermissionForOtherInternalUser() {
        PacmanWorkContextHelper.setPropertyId(null);
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getInternalUser(WC_USER_ID_REGULAR));
        CrudService mockCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockCrudService);

        when(roleService.getPermsForUser(WC_DN_REGULAR_USER, WC_PROPERTY_ID_PUNE.toString(), service)).thenReturn(
                Sets.newHashSet(AuthorizationService.FORECAST_VALIDATION_REPORT_PERMISSION_KEY,
                        AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY, AuthorizationService.GROUP_WASH_PERMISSION_KEY));
        updateWorkContext(workContext(), WC_USER_ID_REGULAR, WC_CLIENT_ID_DUMMY, WC_CLIENT_CODE_TEST,
                WC_PROPERTY_ID_PUNE, WC_PROPERTY_CODE_PUNE);

        createTetrisPrincipal(WC_DN_REGULAR_USER, WC_CN_REGULAR_USER, WC_USER_ID_REGULAR, WC_CLIENT_CODE_TEST, false);

        Set<String> perms = service.getPermsForContext(false);

        assertNotNull(perms);
        assertEquals(3, perms.size(), "Should only be 1 perm");
        assertTrue(perms.contains(AuthorizationService.FORECAST_VALIDATION_REPORT_PERMISSION_KEY));
        assertTrue(perms.contains(AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY));
        assertTrue(perms.contains(AuthorizationService.GROUP_WASH_PERMISSION_KEY));
    }

    @Test
    public void shouldReturnGivenPermissionForInternalUser() {
        PacmanWorkContextHelper.setPropertyId(null);
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        CrudService mockCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockCrudService);

        when(roleService.getPermsForUser(WC_DN_REGULAR_USER, WC_PROPERTY_ID_PUNE.toString(), service)).thenReturn(
                Sets.newHashSet(AuthorizationService.FORECAST_VALIDATION_REPORT_PERMISSION_KEY,
                        AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY,
                        AuthorizationService.GROUP_WASH_PERMISSION_KEY));

        updateWorkContext(workContext(), WC_USER_ID_REGULAR, WC_CLIENT_ID_DUMMY, WC_CLIENT_CODE_TEST,
                WC_PROPERTY_ID_PUNE, WC_PROPERTY_CODE_PUNE);

        createTetrisPrincipal(WC_DN_REGULAR_USER, WC_CN_REGULAR_USER, WC_USER_ID_REGULAR, WC_CLIENT_CODE_TEST, false);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.RATE_PLAN_CONFIGURATION_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_WASH_BY_GROUP_ENABLED.value())).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FORECAST_VALIDATION_REPORT_ENABLED.value())).thenReturn(true);
        Set<String> perms = service.getPermsForContext(false);

        assertNotNull(perms);
        assertEquals(2, perms.size(), "Should only be 1 perm");
        assertTrue(perms.contains(AuthorizationService.RATE_PLAN_CONFIG_PERMISSION_KEY));
        assertTrue(perms.contains(AuthorizationService.FORECAST_VALIDATION_REPORT_PERMISSION_KEY));
        assertFalse(perms.contains(AuthorizationService.GROUP_WASH_PERMISSION_KEY));
    }

    @Test
    public void shouldComponentRoomsDisabled() throws Exception {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.COMPONENT_ROOMS_PERMISSION_KEY, AuthorizationService.OUT_OF_ORDER_COMPONENT_ROOMS_PERMISSION_KEY));
        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertFalse(perms.contains(AuthorizationService.COMPONENT_ROOMS_PERMISSION_KEY), "Component Rooms is NOT authorized");
        assertFalse(perms.contains(AuthorizationService.OUT_OF_ORDER_COMPONENT_ROOMS_PERMISSION_KEY), "Out of Order Component Rooms is NOT authorized");
    }

    @Test
    public void shouldComponentRoomsEnabled() throws Exception {
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(
                Sets.newHashSet(AuthorizationService.COMPONENT_ROOMS_PERMISSION_KEY, AuthorizationService.OUT_OF_ORDER_COMPONENT_ROOMS_PERMISSION_KEY));
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.COMPONENT_ROOMS_ENABLED.value())).thenReturn(true);
        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertTrue(perms.contains(AuthorizationService.COMPONENT_ROOMS_PERMISSION_KEY), "Component Rooms is authorized");
        assertTrue(perms.contains(AuthorizationService.OUT_OF_ORDER_COMPONENT_ROOMS_PERMISSION_KEY), "Out of Order Component Rooms is authorized");
    }

    @Test
    public void testCacheWhenEmptyPropertyList() {
        when(userAuthorizedPropertyCache.get(TestClient.BSTN.getId(), USER_ID)).thenReturn(Collections.emptyList());
        service.retrieveAuthorizedPropertyIds(USER_ID, TestClient.BSTN.getId());
        verify(service).retrieveAuthorizedProperties(USER_ID, TestClient.BSTN.getId(), false);
    }

    @Test
    public void testCacheWhenCacheHasAtLeastOneProperty() {
        when(userAuthorizedPropertyCache.get(TestClient.BSTN.getId(), USER_ID)).thenReturn(Arrays.asList(TestProperty.H1.getId()));
        service.retrieveAuthorizedPropertyIds(USER_ID, TestClient.BSTN.getId());
        verify(service, never()).retrieveAuthorizedProperties(USER_ID, TestClient.BSTN.getId(), true);
    }

    @Test
    public void retrieveAuthorizedProperties_TestCacheWhenEmptyPropertyList() {
        LDAPUser u1 = new LDAPUser();
        u1.setUserId(1);
        u1.setDN("test");
        when(userService.getById(USER_ID.toString())).thenReturn(u1);

        when(userAuthorizedPropertyCache.get(TestClient.BSTN.getId(), USER_ID)).thenReturn(Collections.emptyList());

        AuthorizationService.PropertyAuthGroupExploder propertyAuthGroupExploder = service.new PropertyAuthGroupExploder(TestClient.BSTN.getId());
        when(service.createPropertyAuthGroupExploder(TestClient.BSTN.getId())).thenReturn(propertyAuthGroupExploder);

        service.retrieveAuthorizedProperties(USER_ID, TestClient.BSTN.getId(), true);
        verify(roleService).getPropertiesForUser("test", propertyAuthGroupExploder);
    }

    @Test
    public void retrieveAuthorizedProperties_TestCacheWhenCacheHasAtLeastOneProperty_CheckCacheTrue() {
        LDAPUser u1 = new LDAPUser();
        u1.setUserId(1);
        u1.setDN("test");
        when(userService.getById(USER_ID.toString())).thenReturn(u1);

        when(userAuthorizedPropertyCache.get(TestClient.BSTN.getId(), USER_ID)).thenReturn(Arrays.asList(TestProperty.H1.getId()));

        AuthorizationService.PropertyAuthGroupExploder propertyAuthGroupExploder = service.new PropertyAuthGroupExploder(TestClient.BSTN.getId());
        when(service.createPropertyAuthGroupExploder(TestClient.BSTN.getId())).thenReturn(propertyAuthGroupExploder);

        service.retrieveAuthorizedProperties(USER_ID, TestClient.BSTN.getId(), true);
        verify(roleService, never()).getPropertiesForUser("test", propertyAuthGroupExploder);
    }

    @Test
    public void retrieveAuthorizedProperties_TestCacheWhenCacheHasAtLeastOneProperty_CheckCacheTrueWithNewCache() {
        LDAPUser u1 = new LDAPUser();
        u1.setUserId(1);
        u1.setDN("test");
        when(userService.getById(USER_ID.toString())).thenReturn(u1);

        when(userAuthorizedPropertyCache.get(TestClient.BSTN.getId(), USER_ID)).thenReturn(Arrays.asList(TestProperty.H1.getId()));

        AuthorizationService.PropertyAuthGroupExploder propertyAuthGroupExploder = service.new PropertyAuthGroupExploder(TestClient.BSTN.getId());
        when(service.createPropertyAuthGroupExploder(TestClient.BSTN.getId())).thenReturn(propertyAuthGroupExploder);

        service.retrieveAuthorizedProperties(USER_ID, TestClient.BSTN.getId(), true);
        verify(roleService, never()).getPropertiesForUser("test", propertyAuthGroupExploder);
    }


    @Test
    public void retrieveAuthorizedProperties_TestCacheWhenCacheHasAtLeastOneProperty_CheckCacheFalse() {
        LDAPUser u1 = new LDAPUser();
        u1.setUserId(1);
        u1.setDN("test");
        when(userService.getById(USER_ID.toString())).thenReturn(u1);

        when(userAuthorizedPropertyCache.get(TestClient.BSTN.getId(), USER_ID)).thenReturn(Arrays.asList(TestProperty.H1.getId()));

        AuthorizationService.PropertyAuthGroupExploder propertyAuthGroupExploder = service.new PropertyAuthGroupExploder(TestClient.BSTN.getId());
        when(service.createPropertyAuthGroupExploder(TestClient.BSTN.getId())).thenReturn(propertyAuthGroupExploder);

        service.retrieveAuthorizedProperties(USER_ID, TestClient.BSTN.getId(), false);
        verify(roleService).getPropertiesForUser("test", propertyAuthGroupExploder);
    }

    @Test
    public void testGetUsersForAuthGroupNameAndRoleId() {
        when(mockUserGlobalDBService.listUsersForAuthGroupIdAndRoleId(Mockito.anyInt(), eq("11"))).thenReturn(new ArrayList<>());
        service.setUserGlobalDBService(mockUserGlobalDBService);

        service.getUsersForAuthGroupNameAndRoleId(AuthorizationGroup.ALL_PROP_NAME, "11");

        verify(mockUserGlobalDBService, Mockito.times(1)).listUsersForAuthGroupIdAndRoleId(Mockito.anyInt(), eq("11"));
    }

    @Test
    public void testGetUsersForAuthGroupNameAndRoleIdWithException() {
        assertThrows(TetrisException.class, () -> {
            service.getUsersForAuthGroupNameAndRoleId("Invalid group name", "11");
        });
    }

    @Test
    public void processAuthGroupCreateUpdateFromFDS() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYNC_AUTH_GROUP_CHANGES_FROM_FDS.value())).thenReturn(true);

        CrudService mockCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockCrudService);

        Client client = new Client();
        UUID clientUpsUuid = UUID.randomUUID();
        client.setUpsClientUuid(clientUpsUuid.toString());
        UUID authGroupUuid = UUID.randomUUID();

        Property property1 = new Property();
        property1.setId(1);
        UUID upsId1 = UUID.randomUUID();
        property1.setUpsId(upsId1.toString());

        ClientPropertyCacheService mockClientPropertyCacheService = mock(ClientPropertyCacheService.class);
        service.setClientPropertyCacheService(mockClientPropertyCacheService);
        when(mockClientPropertyCacheService.getClientPropertiesByClientId(client.getId())).thenReturn(Arrays.asList(property1));

        AuthorizationGroup authorizationGroup = new AuthorizationGroup();
        authorizationGroup.setUasAuthGroupUuid(authGroupUuid.toString());
        authorizationGroup.setClientId(client.getId());
        AuthorizationGroupPropertyMapping authGroupMapping1 = new AuthorizationGroupPropertyMapping();
        authGroupMapping1.setStatusId(1);
        authGroupMapping1.setAuthorizationGroup(authorizationGroup);
        authGroupMapping1.setPropertyId(property1.getId());
        authorizationGroup.setAuthGroupPropertyMappings(new HashSet<>(Arrays.asList(authGroupMapping1)));

        UASAuthGroup uasAuthGroup = new UASAuthGroup();
        uasAuthGroup.setAuthGroupId(authGroupUuid);
        uasAuthGroup.setClientId(clientUpsUuid);
        uasAuthGroup.setName("updatedName");
        uasAuthGroup.setDescription("updatedDescription");
        uasAuthGroup.setPropertyList(Arrays.asList(upsId1.toString()));
        uasAuthGroup.setRule(null);
        when(uasService.getAuthGroupFromFDS(authGroupUuid.toString())).thenReturn(uasAuthGroup);

        when(mockCrudService.findByNamedQuerySingleResult(AuthorizationGroup.BY_UAS_UUID,
                QueryParameter.with("authGroupId", authGroupUuid.toString())
                        .and("clientId", client.getId()).parameters())).thenReturn(authorizationGroup);

        service.processAuthGroupCreateUpdateFromFDS(client, authGroupUuid.toString());
        verify(mockCrudService).findByNamedQuerySingleResult(AuthorizationGroup.BY_UAS_UUID,
                QueryParameter.with("authGroupId", authGroupUuid.toString())
                        .and("clientId", client.getId()).parameters());
        verify(uasService).getAuthGroupFromFDS(authGroupUuid.toString());
        verify(mockCrudService).save(any(AuthorizationGroup.class));
    }

    @Test
    public void createAuthGroupFromFDS() {
        Client client = new Client();
        client.setId(5);
        UUID clientUpsUuid = UUID.randomUUID();
        client.setUpsClientUuid(clientUpsUuid.toString());

        Property property1 = new Property();
        property1.setId(1);
        UUID upsId1 = UUID.randomUUID();
        property1.setUpsId(upsId1.toString());

        ClientPropertyCacheService mockClientPropertyCacheService = mock(ClientPropertyCacheService.class);
        service.setClientPropertyCacheService(mockClientPropertyCacheService);

        when(mockClientPropertyCacheService.getClientPropertiesByClientId(client.getId())).thenReturn(Arrays.asList(property1));

        CrudService mockCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockCrudService);

        ConjunctionType conjunctionType = new ConjunctionType();
        conjunctionType.setId(2);
        conjunctionType.setName("AND");
        when(mockCrudService.findAll(ConjunctionType.class)).thenReturn(Arrays.asList(conjunctionType));

        ConditionType conditionType = new ConditionType();
        conditionType.setId(2);
        conditionType.setName("Not Equals");
        when(mockCrudService.findAll(ConditionType.class)).thenReturn(Arrays.asList(conditionType));

        ClientAttribute clientAttribute = new ClientAttribute();
        Integer clientId = 5;
        clientAttribute.setClientId(clientId);
        UUID clientAttributeUuid = UUID.randomUUID();
        clientAttribute.setUpsCustomAttributeUuid(clientAttributeUuid.toString());
        when(customAttributeService.getClientAttributeListByClientId(clientId)).thenReturn(Arrays.asList(clientAttribute));

        ClientAttributeValue clientAttributeValue = new ClientAttributeValue();
        clientAttributeValue.setClientAttribute(clientAttribute);
        clientAttributeValue.setClientAttributeValue("Test");
        when(customAttributeService.getClientAttributeValuesListByClientId(clientId)).thenReturn(Arrays.asList(clientAttributeValue));

        UUID authGroupUuid = UUID.randomUUID();
        UASAuthGroup uasAuthGroup = new UASAuthGroup();
        uasAuthGroup.setName("TestName");
        uasAuthGroup.setAuthGroupId(authGroupUuid);
        uasAuthGroup.setClientId(clientUpsUuid);
        uasAuthGroup.setPropertyList(Arrays.asList(upsId1.toString()));
        Map<Integer, UASRuleDetails> ruleFromUAS = new HashMap<>();
        UASRuleDetails uasRuleDetails = new UASRuleDetails();
        uasRuleDetails.setCustomAttributeId(clientAttributeUuid);
        uasRuleDetails.setCustomAttributeValues(Arrays.asList(clientAttributeValue.getClientAttributeValue()));
        uasRuleDetails.setConditionType(conditionType.getName());
        uasRuleDetails.setConjunctionType(conjunctionType.getName());
        ruleFromUAS.put(1, uasRuleDetails);
        uasAuthGroup.setRule(ruleFromUAS);
        when(uasService.getAuthGroupFromFDS(authGroupUuid.toString())).thenReturn(uasAuthGroup);

        service.createAuthGroupFromFDS(client, authGroupUuid.toString());
        verify(mockCrudService).save(any(AuthorizationGroup.class));
        verify(mockCrudService).save(any(Rule.class));
    }

    @Test
    public void getClientPropertiesMapByClientId() {
        Client client = new Client();
        client.setId(5);

        Property property1 = new Property();
        property1.setId(1);
        UUID upsId1 = UUID.randomUUID();
        property1.setUpsId(upsId1.toString());

        Property property2 = new Property();
        property2.setId(2);
        UUID upsId2 = UUID.randomUUID();
        property2.setUpsId(upsId2.toString());

        ClientPropertyCacheService mockClientPropertyCacheService = mock(ClientPropertyCacheService.class);
        service.setClientPropertyCacheService(mockClientPropertyCacheService);

        when(mockClientPropertyCacheService.getClientPropertiesByClientId(client.getId())).thenReturn(Arrays.asList(property1, property2));

        Map<String, Property> result = service.getClientUPSPropertiesMapByClientId(client.getId());
        assertEquals(2, result.size());
        assertEquals(property1, result.get(upsId1.toString()));
        assertEquals(property2, result.get(upsId2.toString()));
    }

    @Test
    public void createAuthorizationGroupPropertyMappings() {
        AuthorizationGroup authorizationGroup = new AuthorizationGroup();
        Map<String, Property> clientPropertiesMap = new HashMap<>();
        List<String> propertyList = new ArrayList<>();
        //property in G3 but not in rule property list
        Property propertyInG3NotInRule = new Property();
        propertyInG3NotInRule.setId(1);
        UUID propertyUuidInG3NotInRule = UUID.randomUUID();
        propertyInG3NotInRule.setUpsId(propertyUuidInG3NotInRule.toString());
        clientPropertiesMap.put(propertyUuidInG3NotInRule.toString(), propertyInG3NotInRule);
        //property in G3 and in property list
        Property propertyInG3AndInRule = new Property();
        propertyInG3AndInRule.setId(2);
        UUID propertyUuidInG3AndInRule = UUID.randomUUID();
        propertyList.add(propertyUuidInG3AndInRule.toString());
        propertyInG3AndInRule.setUpsId(propertyUuidInG3AndInRule.toString());
        clientPropertiesMap.put(propertyUuidInG3AndInRule.toString(), propertyInG3AndInRule);
        //property not in g3 but in property list
        UUID propertyUuidNotInG3 = UUID.randomUUID();
        propertyList.add(propertyUuidNotInG3.toString());

        Set<AuthorizationGroupPropertyMapping> result = service.createAuthorizationGroupPropertyMappings(authorizationGroup, clientPropertiesMap, propertyList);
        assertEquals(1, result.size());
        AuthorizationGroupPropertyMapping authorizationGroupPropertyMappingResult = result.iterator().next();
        assertEquals(authorizationGroup, authorizationGroupPropertyMappingResult.getAuthorizationGroup());
        assertEquals(propertyInG3AndInRule.getId(), authorizationGroupPropertyMappingResult.getPropertyId());
        assertEquals(Status.ACTIVE.getId(), authorizationGroupPropertyMappingResult.getStatusId());
    }

    @Test
    public void createRule() {
        CrudService mockCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockCrudService);

        ConjunctionType conjunctionType = new ConjunctionType();
        conjunctionType.setId(2);
        conjunctionType.setName("AND");
        when(mockCrudService.findAll(ConjunctionType.class)).thenReturn(Arrays.asList(conjunctionType));

        ConditionType conditionType = new ConditionType();
        conditionType.setId(2);
        conditionType.setName("Not Equals");
        when(mockCrudService.findAll(ConditionType.class)).thenReturn(Arrays.asList(conditionType));

        ClientAttribute clientAttribute = new ClientAttribute();
        Integer clientId = 5;
        clientAttribute.setClientId(clientId);
        UUID clientAttributeUuid = UUID.randomUUID();
        clientAttribute.setUpsCustomAttributeUuid(clientAttributeUuid.toString());
        when(customAttributeService.getClientAttributeListByClientId(clientId)).thenReturn(Arrays.asList(clientAttribute));

        ClientAttributeValue clientAttributeValue = new ClientAttributeValue();
        clientAttributeValue.setClientAttribute(clientAttribute);
        clientAttributeValue.setClientAttributeValue("Test");
        when(customAttributeService.getClientAttributeValuesListByClientId(clientId)).thenReturn(Arrays.asList(clientAttributeValue));

        Map<Integer, UASRuleDetails> ruleFromUAS = new HashMap<>();
        UASRuleDetails uasRuleDetails = new UASRuleDetails();
        uasRuleDetails.setCustomAttributeId(clientAttributeUuid);
        uasRuleDetails.setCustomAttributeValues(Arrays.asList(clientAttributeValue.getClientAttributeValue()));
        uasRuleDetails.setConditionType(conditionType.getName());
        uasRuleDetails.setConjunctionType(conjunctionType.getName());
        ruleFromUAS.put(1, uasRuleDetails);

        Rule result = service.createRule(clientId, ruleFromUAS);
        assertEquals(clientId, result.getClientId());
        assertTrue(result.getCreateDate() != null);
        assertEquals(Constants.SYSTEM_USER_ID, result.getCreatedByUserId());
        assertEquals(1, result.getRuleAttributeValueMappings().size());
        RuleAttributeValueMapping ruleAttributeValueMappingResult = result.getRuleAttributeValueMappings().iterator().next();
        Rule ruleResult = ruleAttributeValueMappingResult.getRule();
        assertEquals(clientId, ruleResult.getClientId());
        assertTrue(ruleResult.getCreateDate() != null);
        assertEquals(Constants.SYSTEM_USER_ID, ruleResult.getCreatedByUserId());
        assertEquals(1, ruleResult.getRuleAttributeValueMappings().size());
        assertEquals(ruleAttributeValueMappingResult, ruleResult.getRuleAttributeValueMappings().iterator().next());
        assertEquals(clientAttributeValue, ruleAttributeValueMappingResult.getClientAttributeValue());
        assertEquals(conditionType, ruleAttributeValueMappingResult.getConditionType());
        assertEquals(conjunctionType, ruleAttributeValueMappingResult.getConjunctionType());
        assertEquals(1, ruleAttributeValueMappingResult.getRanking());
        assertTrue(ruleAttributeValueMappingResult.getCreateDate() != null);
        verify(mockCrudService).findAll(ConjunctionType.class);
        verify(mockCrudService).findAll(ConditionType.class);
        verify(customAttributeService).getClientAttributeListByClientId(clientId);
        verify(customAttributeService).getClientAttributeValuesListByClientId(clientId);
        verify(mockCrudService).save(any(Rule.class));
    }

    @Test
    public void deleteRule() {
        service.deleteRule(1);
        verify(rulesService).deleteRule(1);
    }

    @Test
    public void getClientAttributeValue() {
        ClientAttribute clientAttribute = new ClientAttribute();
        UUID clientAttributeUuid = UUID.randomUUID();
        clientAttribute.setUpsCustomAttributeUuid(clientAttributeUuid.toString());
        ClientAttributeValue clientAttributeValue = new ClientAttributeValue();
        clientAttributeValue.setClientAttribute(clientAttribute);
        clientAttributeValue.setClientAttributeValue("Test");
        UASRuleDetails uasRuleDetails = new UASRuleDetails();
        uasRuleDetails.setCustomAttributeId(clientAttributeUuid);
        uasRuleDetails.setCustomAttributeValues(Arrays.asList(clientAttributeValue.getClientAttributeValue()));

        assertEquals(clientAttributeValue, service.getClientAttributeValue(Arrays.asList(clientAttribute), Arrays.asList(clientAttributeValue), uasRuleDetails));
    }

    @Test
    public void getClientAttributeValue_missingClientAttribute() {
        ClientAttribute clientAttribute = new ClientAttribute();
        clientAttribute.setUpsCustomAttributeUuid(UUID.randomUUID().toString());
        ClientAttributeValue clientAttributeValue = new ClientAttributeValue();
        clientAttributeValue.setClientAttribute(clientAttribute);
        clientAttributeValue.setClientAttributeValue("Test");
        UASRuleDetails uasRuleDetails = new UASRuleDetails();
        UUID customAttributeUuid = UUID.randomUUID();
        uasRuleDetails.setCustomAttributeId(customAttributeUuid);
        uasRuleDetails.setCustomAttributeValues(Arrays.asList(clientAttributeValue.getClientAttributeValue()));

        Exception exception = assertThrows(TetrisException.class, () -> {
            service.getClientAttributeValue(Arrays.asList(clientAttribute), Arrays.asList(clientAttributeValue), uasRuleDetails);
        });
        assertEquals(TetrisException.class, exception.getClass());
        assertTrue(exception.getMessage().contains("UNEXPECTED_ERROR - Unable to find ClientAttribute for UPS Custom Attribute UUID: " + customAttributeUuid.toString()));
    }

    @Test
    public void getClientAttributeValue_missingClientAttribute_missingClientAttributeValue() {
        ClientAttribute clientAttribute = new ClientAttribute();
        clientAttribute.setUpsCustomAttributeUuid(UUID.randomUUID().toString());
        ClientAttributeValue clientAttributeValue = new ClientAttributeValue();
        clientAttributeValue.setClientAttribute(clientAttribute);
        clientAttributeValue.setClientAttributeValue("Test");
        UASRuleDetails uasRuleDetails = new UASRuleDetails();
        UUID customAttributeUuid = UUID.randomUUID();
        uasRuleDetails.setCustomAttributeId(customAttributeUuid);
        uasRuleDetails.setCustomAttributeValues(Arrays.asList("Mismatch"));

        Exception exception = assertThrows(TetrisException.class, () -> {
            service.getClientAttributeValue(Arrays.asList(clientAttribute), Arrays.asList(clientAttributeValue), uasRuleDetails);
        });
        assertEquals(TetrisException.class, exception.getClass());
        assertTrue(exception.getMessage().contains("UNEXPECTED_ERROR - Unable to find ClientAttribute for UPS Custom Attribute UUID: " + customAttributeUuid.toString()));
    }

    @Test
    public void getConditionType() {
        String fdsConditionType = "NOT_EQUALS";
        ConditionType conditionType = new ConditionType();
        conditionType.setId(2);
        conditionType.setName("Not Equals");

        assertEquals(conditionType, authService.getConditionType(Arrays.asList(conditionType), fdsConditionType));

        Exception exception = assertThrows(TetrisException.class, () -> {
            authService.getConditionType(Arrays.asList(conditionType), "invalid");
        });
        assertEquals(TetrisException.class, exception.getClass());
        assertTrue(exception.getMessage().contains("UNEXPECTED_ERROR - Unable to find ConditionType for: invalid"));
    }

    @Test
    public void getConjunctionType() {
        ConjunctionType conjunctionType = new ConjunctionType();
        conjunctionType.setId(2);
        conjunctionType.setName("AND");

        assertEquals(conjunctionType, authService.getConjunctionType(Arrays.asList(conjunctionType), conjunctionType.getName()));

        Exception exception = assertThrows(TetrisException.class, () -> {
            authService.getConjunctionType(Arrays.asList(conjunctionType), "invalid");
        });
        assertEquals(TetrisException.class, exception.getClass());
        assertTrue(exception.getMessage().contains("UNEXPECTED_ERROR - Unable to find ConjunctionType for: invalid"));
    }

    @Test
    public void processAuthGroupDeleteFromFDS() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYNC_AUTH_GROUP_CHANGES_FROM_FDS.value())).thenReturn(true);

        CustomAttributeService mockCustomAttributeService = mock(CustomAttributeService.class);
        mockCustomAttributeService.setGlobalCrudService(globalCrudService());
        service.setCustomAttributeService(mockCustomAttributeService);
        CrudService mockCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockCrudService);

        Client client = new Client();
        client.setId(1);
        client.setCode("Hilton");
        UUID authGroupUuid = UUID.randomUUID();

        AuthorizationGroup authorizationGroup = new AuthorizationGroup();
        authorizationGroup.setId(10);
        authorizationGroup.setRuleId(20);
        authorizationGroup.setUasAuthGroupUuid(authGroupUuid.toString());
        authorizationGroup.setClientId(client.getId());

        when(mockCrudService.findByNamedQuerySingleResult(AuthorizationGroup.BY_UAS_UUID,
                QueryParameter.with("authGroupId", authGroupUuid.toString())
                        .and("clientId", client.getId()).parameters())).thenReturn(authorizationGroup);
        when(mockCrudService.find(AuthorizationGroup.class, authorizationGroup.getId())).thenReturn(authorizationGroup);
        when(mockClientConfigService.getClientByCode(client.getCode())).thenReturn(client);

        service.processAuthGroupDeleteFromFDS(client, authGroupUuid.toString());
        verify(mockCrudService).findByNamedQuerySingleResult(AuthorizationGroup.BY_UAS_UUID,
                QueryParameter.with("authGroupId", authGroupUuid.toString())
                        .and("clientId", client.getId()).parameters());
        verify(mockCrudService).find(AuthorizationGroup.class, authorizationGroup.getId());
        verify(mockCrudService).delete(AuthorizationGroup.class, authorizationGroup.getId());
        verify(mockCustomAttributeService).deleteRulesAndValueMappings(Collections.singletonList(authorizationGroup.getRuleId()), false, null);
        verify(uasService, never()).deleteAuthGroupInFDS(authGroupUuid.toString());
        verify(clientConfigService, never()).getClientByCode(client.getCode());
    }

    @Test
    public void processAuthGroupDeleteFromFDS_MissingAuthGroup() {
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.SYNC_AUTH_GROUP_CHANGES_FROM_FDS.value())).thenReturn(true);

        CustomAttributeService mockCustomAttributeService = new CustomAttributeService();
        mockCustomAttributeService.setGlobalCrudService(globalCrudService());
        service.setCustomAttributeService(mockCustomAttributeService);
        CrudService mockCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockCrudService);

        Client client = new Client();
        client.setId(1);
        UUID authGroupUuid = UUID.randomUUID();

        when(mockCrudService.findByNamedQuerySingleResult(AuthorizationGroup.BY_UAS_UUID,
                QueryParameter.with("authGroupId", authGroupUuid.toString())
                        .and("clientId", client.getId()).parameters())).thenReturn(null);

        service.processAuthGroupDeleteFromFDS(client, authGroupUuid.toString());
        verify(mockCrudService).findByNamedQuerySingleResult(AuthorizationGroup.BY_UAS_UUID,
                QueryParameter.with("authGroupId", authGroupUuid.toString())
                        .and("clientId", client.getId()).parameters());
        verify(uasService, never()).deleteAuthGroupInFDS(authGroupUuid.toString());
    }

    @Test
    public void getAuthGroupByUasAuthGroupUuid() {
        CrudService mockCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockCrudService);

        Client client = new Client();
        client.setId(1);
        UUID authGroupUuid = UUID.randomUUID();

        service.getAuthGroupByUasAuthGroupUuid(authGroupUuid.toString(), client.getId());
        verify(mockCrudService).findByNamedQuerySingleResult(AuthorizationGroup.BY_UAS_UUID,
                QueryParameter.with("authGroupId", authGroupUuid.toString())
                        .and("clientId", client.getId()).parameters());
    }

    private GlobalUser getExternalUser() {
        GlobalUser user = new GlobalUser();
        user.setInternal(false);
        return user;
    }

    private GlobalUser getInternalUser(String wcUserIdSuperDuper) {
        GlobalUser user = new GlobalUser();
        user.setInternal(true);
        user.setId(Integer.parseInt(wcUserIdSuperDuper));
        return user;
    }

    @Test
    public void shouldAllowDeactivateWhenLoggedInUserHasAccessToAllProperties() {
        LDAPUser user1 = createTestLDAPUser();
        int loggedInUserAuthGroupId = -666;
        int editedUserAuthGroupId = 2;
        Object[] expectedQueryResult = createTestDbResult(loggedInUserAuthGroupId, editedUserAuthGroupId);
        List<Object[]> resultList = new ArrayList();
        resultList.add(expectedQueryResult);

        service.setGlobalCrudService(globalCrudService);

        when(globalCrudService.<Object[]>findByNativeQuery(eq(AuthorizationService.GET_USER_AUTH_GROUP_IDS_FOR_SELECTED_USERS),
                anyMap())).thenReturn(resultList);

        assertEquals(true, service.isActivateDeactivateAllowedForSelectedUsers(Arrays.asList(user1)));
    }

    private LDAPUser createTestLDAPUser() {
        LDAPUser user1 = new LDAPUser();
        user1.setUserId(5);
        return user1;
    }

    @Test
    public void shouldNotAllowDeactivateWhenEditedUserHasAccessToAllProperties() {
        LDAPUser user1 = createTestLDAPUser();
        int loggedInUserAuthGroupId = 3;
        int editedUserAuthGroupId = -666;
        Object[] expectedQueryResult = createTestDbResult(loggedInUserAuthGroupId, editedUserAuthGroupId);
        List<Object[]> resultList = new ArrayList();
        resultList.add(expectedQueryResult);

        service.setGlobalCrudService(globalCrudService);
        when(globalCrudService.<Object[]>findByNativeQuery(eq(AuthorizationService.GET_USER_AUTH_GROUP_IDS_FOR_SELECTED_USERS),
                anyMap())).thenReturn(resultList);

        assertEquals(false, service.isActivateDeactivateAllowedForSelectedUsers(Arrays.asList(user1)));
    }

    @Test
    public void shouldNotAllowDeactivateWhenOneOfEditedUsersHasAccessToAllProperties() {
        LDAPUser user1 = createTestLDAPUser();
        int loggedInUserAuthGroupId = 3;

        Object[] expectedQueryResult1 = createTestDbResult(loggedInUserAuthGroupId, 2);
        Object[] expectedQueryResult2 = createTestDbResult(loggedInUserAuthGroupId, -666);

        List<Object[]> resultList = new ArrayList();
        resultList.add(expectedQueryResult1);
        resultList.add(expectedQueryResult2);
        service.setGlobalCrudService(globalCrudService);

        when(globalCrudService.<Object[]>findByNativeQuery(eq(AuthorizationService.GET_USER_AUTH_GROUP_IDS_FOR_SELECTED_USERS),
                anyMap())).thenReturn(resultList);

        assertEquals(false, service.isActivateDeactivateAllowedForSelectedUsers(Arrays.asList(user1)));
    }

    @Test
    public void shouldAllowDeactivateWhenLoggedInUserHasAccessToMoreProperties() {
        LDAPUser user1 = createTestLDAPUser();
        int loggedInUserAuthGroupId = 2;
        int editedUserAuthGroupId = 2;

        Object[] expectedQueryResult = createTestDbResult(loggedInUserAuthGroupId, editedUserAuthGroupId);
        List<Object[]> resultList = new ArrayList();
        resultList.add(expectedQueryResult);

        service.setGlobalCrudService(globalCrudService);

        when(globalCrudService.<Object[]>findByNativeQuery(eq(AuthorizationService.GET_USER_AUTH_GROUP_IDS_FOR_SELECTED_USERS),
                anyMap())).thenReturn(resultList);
        when(globalCrudService.findByNativeQuerySingleResult(eq(AuthorizationService.IS_EDITED_USERS_HAVING_MORE_ASSIGNED_PROPERTIES), anyMap())).thenReturn(0);

        assertEquals(true, service.isActivateDeactivateAllowedForSelectedUsers(Arrays.asList(user1)));
    }

    @Test
    public void shouldNotAllowDeactivateWhenEditedUserHasAccessToMoreProperties() {
        LDAPUser user1 = createTestLDAPUser();
        int loggedInUserAuthGroupId = 2;
        int editedUserAuthGroupId = 2;

        Object[] expectedQueryResult = createTestDbResult(loggedInUserAuthGroupId, editedUserAuthGroupId);

        List<Object[]> resultList = new ArrayList();
        resultList.add(expectedQueryResult);

        service.setGlobalCrudService(globalCrudService);

        when(globalCrudService.<Object[]>findByNativeQuery(eq(AuthorizationService.GET_USER_AUTH_GROUP_IDS_FOR_SELECTED_USERS),
                anyMap())).thenReturn(resultList);

        when(globalCrudService.findByNativeQuerySingleResult(eq(AuthorizationService.IS_EDITED_USERS_HAVING_MORE_ASSIGNED_PROPERTIES), anyMap())).thenReturn(2);

        assertEquals(false, service.isActivateDeactivateAllowedForSelectedUsers(Arrays.asList(user1)));
    }

    private Object[] createTestDbResult(int loggedInUserAuthGroupId, int editedUserAuthGroupId) {
        Object[] expectedQueryResult = new Object[2];
        expectedQueryResult[0] = loggedInUserAuthGroupId;
        expectedQueryResult[1] = editedUserAuthGroupId;
        return expectedQueryResult;
    }


    @Test
    public void forInternalUserSystemAccessToggleDoesNotHaveAnyImpact() throws Exception {
        Set<String> expectedPermissions = Sets.newHashSet(AuthorizationService.GROUP_WASH_PERMISSION_KEY, AuthorizationService.CHANNEL_COSTS_PERMISSION_KEY, AuthorizationService.CLIENT_QUESTIONNAIRE_PERMISSION_KEY);
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getInternalUser(WC_USER_ID_SUPER_DUPER));
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(expectedPermissions);
        when(pacmanConfigParamsService.getParameterValue(GUIConfigParamName.IS_PROPERTY_READY_FOR_EXTERNAL_USER)).thenReturn(false);

        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);

        assertTrue(perms.containsAll(expectedPermissions), "Should have access to G3 modules ");
    }

    @Test
    public void forExternalUserWhenSystemAccessIsDisabledThenNoG3ModuleIsAccessible() throws Exception {
        HashSet<String> expectedPermissions = Sets.newHashSet(AuthorizationService.GROUP_WASH_PERMISSION_KEY, AuthorizationService.CHANNEL_COSTS_PERMISSION_KEY, AuthorizationService.CLIENT_QUESTIONNAIRE_PERMISSION_KEY);
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(pacmanConfigParamsService.getParameterValue(GUIConfigParamName.IS_PROPERTY_READY_FOR_EXTERNAL_USER)).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_WASH_BY_GROUP_ENABLED.value())).thenReturn(true);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(expectedPermissions);

        Set<String> perms = service.getPermsForUserAndProperty(dn, propertyId, false);

        assertTrue(perms.isEmpty(), "Should Not have any permission enabled");
    }


    @Test
    public void forExternalUserWhenSystemAccessIsDisabledThenOnlyQuestionnairePageAndMainMenuCanBeAccessed() throws Exception {
        String configMenuPermission = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.CONFIGURE;
        String authorizationMenuPermission = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.AUTHORIZATIONS;

        HashSet<String> expectedPermissions = Sets.newHashSet(AuthorizationService.GROUP_WASH_PERMISSION_KEY, AuthorizationService.CHANNEL_COSTS_PERMISSION_KEY,
                AuthorizationService.CLIENT_QUESTIONNAIRE_PERMISSION_KEY, configMenuPermission, authorizationMenuPermission);
        setMockData(expectedPermissions);

        Set<String> permissionsToExternalUser = service.getPermsForUserAndProperty(dn, propertyId, false);

        verifyResult(configMenuPermission, authorizationMenuPermission, permissionsToExternalUser);
    }

    @Test
    public void getAuthGroupsByClientId() {
        CrudService mockCrud = mock(CrudService.class);
        service.setGlobalCrudService(mockCrud);

        AuthorizationGroupPropertyMapping map1 = new AuthorizationGroupPropertyMapping();
        AuthorizationGroupPropertyMapping map2 = new AuthorizationGroupPropertyMapping();

        AuthorizationGroup ag1 = new AuthorizationGroup();
        Set<AuthorizationGroupPropertyMapping> set1 = new HashSet<AuthorizationGroupPropertyMapping>();
        set1.add(map1);
        ag1.setAuthGroupPropertyMappings(set1);

        AuthorizationGroup ag2 = new AuthorizationGroup();
        Set<AuthorizationGroupPropertyMapping> set2 = new HashSet<AuthorizationGroupPropertyMapping>();
        set2.add(map2);
        ag2.setAuthGroupPropertyMappings(set2);

        List<AuthorizationGroup> allAuthGroups = Arrays.asList(ag1, ag2);
        when(mockCrud.<AuthorizationGroup>findByNamedQuery(AuthorizationGroup.BY_CLIENT_ID, QueryParameter.with("clientId", WC_CLIENT_ID_DUMMY).parameters()))
                .thenReturn(allAuthGroups);

        List<AuthorizationGroup> result = service.getAuthGroupsByClientId(WC_CLIENT_ID_DUMMY);
        assertEquals(2, result.size());
        assertTrue(result.contains(ag1));
        assertTrue(result.contains(ag2));
    }

    @Test
    public void saveAuthGroup() {
        CrudService mockCrud = mock(CrudService.class);
        service.setGlobalCrudService(mockCrud);

        AuthorizationGroup authorizationGroup = new AuthorizationGroup();

        service.saveAuthGroup(authorizationGroup);
        verify(mockCrud).save(authorizationGroup);
    }

    @Test
    public void updateAuthGroupFromFDS_Update() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);

        Client client = new Client();
        client.setId(1);
        UUID clientUuid = UUID.randomUUID();
        client.setUpsClientUuid(clientUuid.toString());
        when(clientService.findClientByUpsClientUuid(clientUuid.toString())).thenReturn(client);

        Property property1 = new Property();
        property1.setId(1);
        UUID upsId1 = UUID.randomUUID();
        property1.setUpsId(upsId1.toString());

        ClientPropertyCacheService mockClientPropertyCacheService = mock(ClientPropertyCacheService.class);
        service.setClientPropertyCacheService(mockClientPropertyCacheService);
        when(mockClientPropertyCacheService.getClientPropertiesByClientId(client.getId())).thenReturn(Arrays.asList(property1));

        UUID uasAuthGroupUuid = UUID.randomUUID();
        UASAuthGroup uasAuthGroup = new UASAuthGroup();
        uasAuthGroup.setAuthGroupId(uasAuthGroupUuid);
        uasAuthGroup.setClientId(clientUuid);
        uasAuthGroup.setName("updatedName");
        uasAuthGroup.setDescription("updatedDescription");
        uasAuthGroup.setPropertyList(Arrays.asList(upsId1.toString()));
        uasAuthGroup.setRule(null);

        AuthorizationGroup authGroup = new AuthorizationGroup();
        authGroup.setUasAuthGroupUuid(uasAuthGroupUuid.toString());
        authGroup.setClientId(client.getId());
        AuthorizationGroupPropertyMapping authGroupMapping1 = new AuthorizationGroupPropertyMapping();
        authGroupMapping1.setStatusId(1);
        authGroupMapping1.setAuthorizationGroup(authGroup);
        authGroupMapping1.setPropertyId(property1.getId());
        authGroup.setAuthGroupPropertyMappings(new HashSet<>(Arrays.asList(authGroupMapping1)));

        when(mockGlobalCrudService.findByNamedQuerySingleResult(AuthorizationGroup.BY_UAS_UUID,
                QueryParameter.with("authGroupId", uasAuthGroupUuid.toString())
                        .and("clientId", client.getId()).parameters())).thenReturn(authGroup);

        when(uasService.getAuthGroupFromFDS(uasAuthGroupUuid.toString())).thenReturn(uasAuthGroup);

        service.updateAuthGroupFromFDS(clientUuid.toString(), uasAuthGroupUuid.toString());
        verify(clientService).findClientByUpsClientUuid(clientUuid.toString());
        verify(uasService).getAuthGroupFromFDS(uasAuthGroupUuid.toString());
        verify(mockGlobalCrudService).save(authGroup);
    }

    @Test
    public void updateAuthGroupFromFDS_Create() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);

        Client client = new Client();
        client.setId(1);
        UUID clientUuid = UUID.randomUUID();
        client.setUpsClientUuid(clientUuid.toString());
        when(clientService.findClientByUpsClientUuid(clientUuid.toString())).thenReturn(client);

        Property property1 = new Property();
        property1.setId(1);
        UUID upsId1 = UUID.randomUUID();
        property1.setUpsId(upsId1.toString());

        UUID uasAuthGroupUuid = UUID.randomUUID();
        UASAuthGroup uasAuthGroup = new UASAuthGroup();
        uasAuthGroup.setAuthGroupId(uasAuthGroupUuid);
        uasAuthGroup.setClientId(clientUuid);
        uasAuthGroup.setName("updatedName");
        uasAuthGroup.setDescription("updatedDescription");
        uasAuthGroup.setPropertyList(Arrays.asList(upsId1.toString()));
        uasAuthGroup.setRule(null);
        when(mockGlobalCrudService.findByNamedQuerySingleResult(AuthorizationGroup.BY_UAS_UUID,
                QueryParameter.with("authGroupId", uasAuthGroupUuid)
                        .and("clientId", client.getId()).parameters())).thenReturn(null);
        when(uasService.getAuthGroupFromFDS(uasAuthGroupUuid.toString())).thenReturn(uasAuthGroup);

        service.updateAuthGroupFromFDS(clientUuid.toString(), uasAuthGroupUuid.toString());
        verify(clientService).findClientByUpsClientUuid(clientUuid.toString());
        verify(uasService).getAuthGroupFromFDS(uasAuthGroupUuid.toString());
        verify(mockGlobalCrudService).save(any(AuthorizationGroup.class));
    }

    @Test
    public void updateAuthGroupFromFDS_UpdateAuthGroup_UnknownAuthGroupInFDS() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);

        Client client = new Client();
        client.setId(1);
        UUID clientUuid = UUID.randomUUID();
        client.setUpsClientUuid(clientUuid.toString());
        when(clientService.findClientByUpsClientUuid(clientUuid.toString())).thenReturn(client);

        UUID uasAuthGroupUuid = UUID.randomUUID();
        AuthorizationGroup authGroup = new AuthorizationGroup();
        authGroup.setUasAuthGroupUuid(uasAuthGroupUuid.toString());

        when(mockGlobalCrudService.findByNamedQuerySingleResult(AuthorizationGroup.BY_UAS_UUID,
                QueryParameter.with("authGroupId", uasAuthGroupUuid.toString())
                        .and("clientId", client.getId()).parameters())).thenReturn(authGroup);

        when(uasService.getAuthGroupFromFDS(uasAuthGroupUuid.toString())).thenReturn(null);

        final TetrisException tetrisException = assertThrows(TetrisException.class, () -> service.updateAuthGroupFromFDS(clientUuid.toString(), uasAuthGroupUuid.toString()));
        assertEquals(UNEXPECTED_ERROR, tetrisException.getErrorCode());
        assertEquals("Error updating authGroupId: " + uasAuthGroupUuid + " for clientId: " + client.getUpsClientUuid() + ", as the auth group doesn't exist in FDS", tetrisException.getBaseMessage());
        verify(clientService).findClientByUpsClientUuid(clientUuid.toString());
        verify(uasService).getAuthGroupFromFDS(uasAuthGroupUuid.toString());
        verify(mockGlobalCrudService, never()).save(any(AuthorizationGroup.class));
    }

    @Test
    public void updateAuthGroupFromFDS_CreateAuthGroup_UnknownAuthGroupInFDS() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);

        Client client = new Client();
        client.setId(1);
        UUID clientUuid = UUID.randomUUID();
        client.setUpsClientUuid(clientUuid.toString());
        when(clientService.findClientByUpsClientUuid(clientUuid.toString())).thenReturn(client);

        UUID uasAuthGroupUuid = UUID.randomUUID();
        when(mockGlobalCrudService.findByNamedQuerySingleResult(AuthorizationGroup.BY_UAS_UUID,
                QueryParameter.with("authGroupId", uasAuthGroupUuid)
                        .and("clientId", client.getId()).parameters())).thenReturn(null);

        when(uasService.getAuthGroupFromFDS(uasAuthGroupUuid.toString())).thenReturn(null);

        final TetrisException tetrisException = assertThrows(TetrisException.class, () -> service.updateAuthGroupFromFDS(clientUuid.toString(), uasAuthGroupUuid.toString()));
        assertEquals(UNEXPECTED_ERROR, tetrisException.getErrorCode());
        assertEquals("Error creating authGroupId: " + uasAuthGroupUuid + " for clientId: " + client.getUpsClientUuid() + ", as the auth group doesn't exist in FDS", tetrisException.getBaseMessage());
        verify(clientService).findClientByUpsClientUuid(clientUuid.toString());
        verify(uasService).getAuthGroupFromFDS(uasAuthGroupUuid.toString());
        verify(mockGlobalCrudService, never()).save(any(AuthorizationGroup.class));
    }

    @Test
    public void updateAuthGroupFromFDS_UnknownClient() {
        CrudService mockGlobalCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockGlobalCrudService);

        UUID clientUuid = UUID.randomUUID();
        when(clientService.findClientByUpsClientUuid(clientUuid.toString())).thenReturn(null);

        UUID uasAuthGroupUuid = UUID.randomUUID();

        when(uasService.getAuthGroupFromFDS(uasAuthGroupUuid.toString())).thenReturn(null);

        final TetrisException tetrisException = assertThrows(TetrisException.class, () -> service.updateAuthGroupFromFDS(clientUuid.toString(), uasAuthGroupUuid.toString()));
        assertEquals(UNEXPECTED_ERROR, tetrisException.getErrorCode());
        assertEquals("Error updating authGroupId: " + uasAuthGroupUuid + ", as the client doesn't exist with clientId: " + clientUuid, tetrisException.getBaseMessage());
        verify(clientService).findClientByUpsClientUuid(clientUuid.toString());
        verify(uasService, never()).getAuthGroupFromFDS(uasAuthGroupUuid.toString());
        verify(mockGlobalCrudService, never()).save(any(AuthorizationGroup.class));
    }

    @Test
    public void updateAuthGroupFromFDS_StaticNoRule() {
        Client client = new Client();
        client.setId(5);
        UUID clientUpsUuid = UUID.randomUUID();
        client.setUpsClientUuid(clientUpsUuid.toString());

        //property stays the same
        Property property1 = new Property();
        property1.setId(1);
        UUID upsId1 = UUID.randomUUID();
        property1.setUpsId(upsId1.toString());

        //property added to auth group
        Property property2 = new Property();
        property2.setId(2);
        UUID upsId2 = UUID.randomUUID();
        property2.setUpsId(upsId2.toString());

        //property removed from auth group
        Property property3 = new Property();
        property3.setId(3);
        UUID upsId3 = UUID.randomUUID();
        property3.setUpsId(upsId3.toString());

        ClientPropertyCacheService mockClientPropertyCacheService = mock(ClientPropertyCacheService.class);
        service.setClientPropertyCacheService(mockClientPropertyCacheService);

        when(mockClientPropertyCacheService.getClientPropertiesByClientId(client.getId())).thenReturn(Arrays.asList(property1, property2, property3));

        CrudService mockCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockCrudService);

        ConjunctionType conjunctionType = new ConjunctionType();
        conjunctionType.setId(2);
        conjunctionType.setName("AND");
        when(mockCrudService.findAll(ConjunctionType.class)).thenReturn(Arrays.asList(conjunctionType));

        ConditionType conditionType = new ConditionType();
        conditionType.setId(2);
        conditionType.setName("Not Equals");
        when(mockCrudService.findAll(ConditionType.class)).thenReturn(Arrays.asList(conditionType));

        ClientAttribute clientAttribute = new ClientAttribute();
        Integer clientId = 5;
        clientAttribute.setClientId(clientId);
        UUID clientAttributeUuid = UUID.randomUUID();
        clientAttribute.setUpsCustomAttributeUuid(clientAttributeUuid.toString());
        when(customAttributeService.getClientAttributeListByClientId(clientId)).thenReturn(Arrays.asList(clientAttribute));

        ClientAttributeValue clientAttributeValue = new ClientAttributeValue();
        clientAttributeValue.setClientAttribute(clientAttribute);
        clientAttributeValue.setClientAttributeValue("Test");
        when(customAttributeService.getClientAttributeValuesListByClientId(clientId)).thenReturn(Arrays.asList(clientAttributeValue));

        UUID authGroupUuid = UUID.randomUUID();
        UASAuthGroup uasAuthGroup = new UASAuthGroup();
        uasAuthGroup.setAuthGroupId(authGroupUuid);
        uasAuthGroup.setClientId(clientUpsUuid);
        uasAuthGroup.setStatus(1);
        uasAuthGroup.setName("updatedName");
        uasAuthGroup.setDescription("updatedDescription");
        uasAuthGroup.setPropertyList(Arrays.asList(upsId1.toString(), upsId2.toString()));
        uasAuthGroup.setRule(null);
        when(uasService.getAuthGroupFromFDS(authGroupUuid.toString())).thenReturn(uasAuthGroup);

        AuthorizationGroup authGroup = new AuthorizationGroup();
        authGroup.setId(1);
        authGroup.setClientId(clientId);
        authGroup.setName("originalName");
        authGroup.setDescription("originalDescription");
        authGroup.setRuleId(null);
        authGroup.setStatusId(1);
        AuthorizationGroupPropertyMapping authGroupMapping1 = new AuthorizationGroupPropertyMapping();
        authGroupMapping1.setStatusId(1);
        authGroupMapping1.setAuthorizationGroup(authGroup);
        authGroupMapping1.setPropertyId(property1.getId());
        AuthorizationGroupPropertyMapping authGroupMapping3 = new AuthorizationGroupPropertyMapping();
        authGroupMapping3.setStatusId(1);
        authGroupMapping3.setAuthorizationGroup(authGroup);
        authGroupMapping3.setPropertyId(property3.getId());
        authGroup.setAuthGroupPropertyMappings(new HashSet<>(Arrays.asList(authGroupMapping1, authGroupMapping3)));

        service.updateAuthGroupFromFDS(client, authGroupUuid.toString(), authGroup);
        assertEquals(uasAuthGroup.getName(), authGroup.getName());
        assertEquals(uasAuthGroup.getDescription(), authGroup.getDescription());
        assertEquals(1, authGroup.getStatusId());
        assertEquals(2, authGroup.getAuthGroupPropertyMappings().size());
        List<Integer> resultPropertyMappings = authGroup.getAuthGroupPropertyMappings().stream().map(AuthorizationGroupPropertyMapping::getPropertyId).collect(Collectors.toList());
        assertTrue(resultPropertyMappings.contains(property1.getId()));
        assertTrue(resultPropertyMappings.contains(property2.getId()));
        assertNull(authGroup.getRuleId());
        verify(uasService).getAuthGroupFromFDS(authGroupUuid.toString());
        verify(mockClientPropertyCacheService).getClientPropertiesByClientId(client.getId());
        verify(mockCrudService).save(any(AuthorizationGroup.class));
        verify(mockCrudService, never()).delete(any(Rule.class));
        verify(rulesService).removeMappingsFromAuthorizationGroupPropertyMapping(Arrays.asList(property3.getId()), authGroup.getId());
        verify(mockCrudService, never()).save(any(Rule.class));
    }

    @Test
    public void updateAuthGroupFromFDS_DynamicExistingRule() {
        Client client = new Client();
        client.setId(5);
        UUID clientUpsUuid = UUID.randomUUID();
        client.setUpsClientUuid(clientUpsUuid.toString());

        //property stays the same
        Property property1 = new Property();
        property1.setId(1);
        UUID upsId1 = UUID.randomUUID();
        property1.setUpsId(upsId1.toString());

        //property added to auth group
        Property property2 = new Property();
        property2.setId(2);
        UUID upsId2 = UUID.randomUUID();
        property2.setUpsId(upsId2.toString());

        //property removed from auth group
        Property property3 = new Property();
        property3.setId(3);
        UUID upsId3 = UUID.randomUUID();
        property3.setUpsId(upsId3.toString());

        ClientPropertyCacheService mockClientPropertyCacheService = mock(ClientPropertyCacheService.class);
        service.setClientPropertyCacheService(mockClientPropertyCacheService);

        when(mockClientPropertyCacheService.getClientPropertiesByClientId(client.getId())).thenReturn(Arrays.asList(property1, property2, property3));

        CrudService mockCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockCrudService);

        ConjunctionType conjunctionType = new ConjunctionType();
        conjunctionType.setId(2);
        conjunctionType.setName("AND");
        when(mockCrudService.findAll(ConjunctionType.class)).thenReturn(Arrays.asList(conjunctionType));

        ConditionType conditionType = new ConditionType();
        conditionType.setId(2);
        conditionType.setName("Not Equals");
        when(mockCrudService.findAll(ConditionType.class)).thenReturn(Arrays.asList(conditionType));

        ClientAttribute clientAttribute = new ClientAttribute();
        Integer clientId = 5;
        clientAttribute.setClientId(clientId);
        UUID clientAttributeUuid = UUID.randomUUID();
        clientAttribute.setUpsCustomAttributeUuid(clientAttributeUuid.toString());
        when(customAttributeService.getClientAttributeListByClientId(clientId)).thenReturn(Arrays.asList(clientAttribute));

        ClientAttributeValue clientAttributeValue = new ClientAttributeValue();
        clientAttributeValue.setClientAttribute(clientAttribute);
        clientAttributeValue.setClientAttributeValue("Test");
        when(customAttributeService.getClientAttributeValuesListByClientId(clientId)).thenReturn(Arrays.asList(clientAttributeValue));

        UUID authGroupUuid = UUID.randomUUID();
        UASAuthGroup uasAuthGroup = new UASAuthGroup();
        uasAuthGroup.setAuthGroupId(authGroupUuid);
        uasAuthGroup.setClientId(clientUpsUuid);
        uasAuthGroup.setStatus(1);
        uasAuthGroup.setName("updatedName");
        uasAuthGroup.setDescription("updatedDescription");
        uasAuthGroup.setPropertyList(Arrays.asList(upsId1.toString(), upsId2.toString()));
        uasAuthGroup.setRule(null);
        Map<Integer, UASRuleDetails> ruleFromUAS = new HashMap<>();
        UASRuleDetails uasRuleDetails = new UASRuleDetails();
        uasRuleDetails.setCustomAttributeId(clientAttributeUuid);
        uasRuleDetails.setCustomAttributeValues(Arrays.asList(clientAttributeValue.getClientAttributeValue()));
        uasRuleDetails.setConditionType(conditionType.getName());
        uasRuleDetails.setConjunctionType(conjunctionType.getName());
        ruleFromUAS.put(1, uasRuleDetails);
        uasAuthGroup.setRule(ruleFromUAS);
        when(uasService.getAuthGroupFromFDS(authGroupUuid.toString())).thenReturn(uasAuthGroup);

        AuthorizationGroup authGroup = new AuthorizationGroup();
        authGroup.setId(1);
        authGroup.setClientId(clientId);
        authGroup.setName("originalName");
        authGroup.setDescription("originalDescription");
        Rule rule = new Rule();
        rule.setId(10);
        rule.setClientId(clientId);
        authGroup.setRuleId(rule.getId());
        authGroup.setStatusId(1);
        AuthorizationGroupPropertyMapping authGroupMapping1 = new AuthorizationGroupPropertyMapping();
        authGroupMapping1.setStatusId(1);
        authGroupMapping1.setAuthorizationGroup(authGroup);
        authGroupMapping1.setPropertyId(property1.getId());
        AuthorizationGroupPropertyMapping authGroupMapping3 = new AuthorizationGroupPropertyMapping();
        authGroupMapping3.setStatusId(1);
        authGroupMapping3.setAuthorizationGroup(authGroup);
        authGroupMapping3.setPropertyId(property3.getId());
        authGroup.setAuthGroupPropertyMappings(new HashSet<>(Arrays.asList(authGroupMapping1, authGroupMapping3)));

        service.updateAuthGroupFromFDS(client, authGroupUuid.toString(), authGroup);
        assertEquals(uasAuthGroup.getName(), authGroup.getName());
        assertEquals(uasAuthGroup.getDescription(), authGroup.getDescription());
        assertEquals(1, authGroup.getStatusId());
        assertEquals(2, authGroup.getAuthGroupPropertyMappings().size());
        List<Integer> resultPropertyMappings = authGroup.getAuthGroupPropertyMappings().stream().map(AuthorizationGroupPropertyMapping::getPropertyId).collect(Collectors.toList());
        assertTrue(resultPropertyMappings.contains(property1.getId()));
        assertTrue(resultPropertyMappings.contains(property2.getId()));
        assertNull(authGroup.getRuleId());
        verify(uasService).getAuthGroupFromFDS(authGroupUuid.toString());
        verify(mockClientPropertyCacheService).getClientPropertiesByClientId(client.getId());
        verify(mockCrudService).save(any(AuthorizationGroup.class));
        verify(rulesService).deleteRule(rule.getId());
        verify(rulesService).removeMappingsFromAuthorizationGroupPropertyMapping(Arrays.asList(property3.getId()), authGroup.getId());
        verify(mockCrudService).save(any(Rule.class));
    }

    @Test
    public void updateAuthGroupFromFDS_DynamicNewRule() {
        Client client = new Client();
        client.setId(5);
        UUID clientUpsUuid = UUID.randomUUID();
        client.setUpsClientUuid(clientUpsUuid.toString());

        //property stays the same
        Property property1 = new Property();
        property1.setId(1);
        UUID upsId1 = UUID.randomUUID();
        property1.setUpsId(upsId1.toString());

        //property added to auth group
        Property property2 = new Property();
        property2.setId(2);
        UUID upsId2 = UUID.randomUUID();
        property2.setUpsId(upsId2.toString());

        //property removed from auth group
        Property property3 = new Property();
        property3.setId(3);
        UUID upsId3 = UUID.randomUUID();
        property3.setUpsId(upsId3.toString());

        ClientPropertyCacheService mockClientPropertyCacheService = mock(ClientPropertyCacheService.class);
        service.setClientPropertyCacheService(mockClientPropertyCacheService);

        when(mockClientPropertyCacheService.getClientPropertiesByClientId(client.getId())).thenReturn(Arrays.asList(property1, property2, property3));

        CrudService mockCrudService = mock(CrudService.class);
        service.setGlobalCrudService(mockCrudService);

        ConjunctionType conjunctionType = new ConjunctionType();
        conjunctionType.setId(2);
        conjunctionType.setName("AND");
        when(mockCrudService.findAll(ConjunctionType.class)).thenReturn(Arrays.asList(conjunctionType));

        ConditionType conditionType = new ConditionType();
        conditionType.setId(2);
        conditionType.setName("Not Equals");
        when(mockCrudService.findAll(ConditionType.class)).thenReturn(Arrays.asList(conditionType));

        ClientAttribute clientAttribute = new ClientAttribute();
        Integer clientId = 5;
        clientAttribute.setClientId(clientId);
        UUID clientAttributeUuid = UUID.randomUUID();
        clientAttribute.setUpsCustomAttributeUuid(clientAttributeUuid.toString());
        when(customAttributeService.getClientAttributeListByClientId(clientId)).thenReturn(Arrays.asList(clientAttribute));

        ClientAttributeValue clientAttributeValue = new ClientAttributeValue();
        clientAttributeValue.setClientAttribute(clientAttribute);
        clientAttributeValue.setClientAttributeValue("Test");
        when(customAttributeService.getClientAttributeValuesListByClientId(clientId)).thenReturn(Arrays.asList(clientAttributeValue));

        UUID authGroupUuid = UUID.randomUUID();
        UASAuthGroup uasAuthGroup = new UASAuthGroup();
        uasAuthGroup.setAuthGroupId(authGroupUuid);
        uasAuthGroup.setClientId(clientUpsUuid);
        uasAuthGroup.setName("updatedName");
        uasAuthGroup.setDescription("updatedDescription");
        uasAuthGroup.setStatus(2);
        uasAuthGroup.setPropertyList(Arrays.asList(upsId1.toString(), upsId2.toString()));
        uasAuthGroup.setRule(null);
        Map<Integer, UASRuleDetails> ruleFromUAS = new HashMap<>();
        UASRuleDetails uasRuleDetails = new UASRuleDetails();
        uasRuleDetails.setCustomAttributeId(clientAttributeUuid);
        uasRuleDetails.setCustomAttributeValues(Arrays.asList(clientAttributeValue.getClientAttributeValue()));
        uasRuleDetails.setConditionType(conditionType.getName());
        uasRuleDetails.setConjunctionType(conjunctionType.getName());
        ruleFromUAS.put(1, uasRuleDetails);
        uasAuthGroup.setRule(ruleFromUAS);
        when(uasService.getAuthGroupFromFDS(authGroupUuid.toString())).thenReturn(uasAuthGroup);

        AuthorizationGroup authGroup = new AuthorizationGroup();
        authGroup.setId(1);
        authGroup.setClientId(clientId);
        authGroup.setName("originalName");
        authGroup.setDescription("originalDescription");
        authGroup.setRuleId(null);
        authGroup.setStatusId(1);
        AuthorizationGroupPropertyMapping authGroupMapping1 = new AuthorizationGroupPropertyMapping();
        authGroupMapping1.setStatusId(1);
        authGroupMapping1.setAuthorizationGroup(authGroup);
        authGroupMapping1.setPropertyId(property1.getId());
        AuthorizationGroupPropertyMapping authGroupMapping3 = new AuthorizationGroupPropertyMapping();
        authGroupMapping3.setStatusId(1);
        authGroupMapping3.setAuthorizationGroup(authGroup);
        authGroupMapping3.setPropertyId(property3.getId());
        authGroup.setAuthGroupPropertyMappings(new HashSet<>(Arrays.asList(authGroupMapping1, authGroupMapping3)));

        service.updateAuthGroupFromFDS(client, authGroupUuid.toString(), authGroup);
        assertEquals(uasAuthGroup.getName(), authGroup.getName());
        assertEquals(uasAuthGroup.getDescription(), authGroup.getDescription());
        assertEquals(2, authGroup.getStatusId());
        assertEquals(2, authGroup.getAuthGroupPropertyMappings().size());
        List<Integer> resultPropertyMappings = authGroup.getAuthGroupPropertyMappings().stream().map(AuthorizationGroupPropertyMapping::getPropertyId).collect(Collectors.toList());
        assertTrue(resultPropertyMappings.contains(property1.getId()));
        assertTrue(resultPropertyMappings.contains(property2.getId()));
        assertNull(authGroup.getRuleId());
        verify(uasService).getAuthGroupFromFDS(authGroupUuid.toString());
        verify(mockClientPropertyCacheService).getClientPropertiesByClientId(client.getId());
        verify(mockCrudService).save(any(AuthorizationGroup.class));
        verify(mockCrudService, never()).delete(any(Rule.class));
        verify(rulesService).removeMappingsFromAuthorizationGroupPropertyMapping(Arrays.asList(property3.getId()), authGroup.getId());
        verify(mockCrudService).save(any(Rule.class));
    }

    private void verifyResult(String configMenuPermission, String authorizationMenuPermission, Set<String> permissionsToExternalUser) {
        assertTrue(permissionsToExternalUser.contains(AuthorizationService.CLIENT_QUESTIONNAIRE_PERMISSION_KEY), "Questionnaire should be accessible");
        assertTrue(permissionsToExternalUser.contains(configMenuPermission), "Config Menu should be accessible");
        assertTrue(permissionsToExternalUser.contains(authorizationMenuPermission), "Authorization Menu should be accessible");
        assertFalse(permissionsToExternalUser.contains(AuthorizationService.GROUP_WASH_PERMISSION_KEY), "Group Wash by Group should be disabled");
    }

    @Test
    public void forExternalUserWhenUserHasCorporateAccessWithAllPermissions() {
        HashSet<String> permissions = Sets.newHashSet(Role.ALL_PERMS_ID);

        setMockData(permissions);

        String configMenuPermission = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.CONFIGURE;
        String authorizationMenuPermission = LDAPConstants.PAGE_CODE_KEY_VALUE + TetrisPermissionKey.AUTHORIZATIONS;
        Set<String> permissionsToExternalUser = service.getPermsForUserAndProperty(dn, propertyId, false);

        verifyResults(configMenuPermission, authorizationMenuPermission, permissionsToExternalUser);
    }

    private void setMockData(HashSet<String> permissions) {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(pacmanConfigParamsService.getParameterValue(GUIConfigParamName.IS_PROPERTY_READY_FOR_EXTERNAL_USER)).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_WASH_BY_GROUP_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_CLIENT_QUESTIONNAIRE.value())).thenReturn(true);
        when(pacmanConfigParamsService.getParameterValue(FeatureTogglesConfigParamName.ENABLE_CLIENT_QUESTIONNAIRE)).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_CLIENT_QUESTIONNAIRE)).thenReturn(true);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(permissions);
    }

    private void verifyResults(String configMenuPermission, String authorizationMenuPermission, Set<String> permissionsToExternalUser) {
        assertTrue(permissionsToExternalUser.contains(AuthorizationService.CLIENT_QUESTIONNAIRE_PERMISSION_KEY + ACCESS_READ_WRITE), "Questionnaire should be accessible");
        assertTrue(permissionsToExternalUser.contains(configMenuPermission + ACCESS_READ_WRITE), "Config Menu should be accessible");
        assertTrue(permissionsToExternalUser.contains(authorizationMenuPermission + ACCESS_READ_WRITE), "Authorization Menu should be accessible");
        assertFalse(permissionsToExternalUser.contains(AuthorizationService.GROUP_WASH_PERMISSION_KEY), "Group Wash by Group should be disabled");
    }

    @Test
    public void isFDSAuthGroupsEnabled() {
        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.getParameterName())).thenReturn("true");
        assertTrue(service.isFDSAuthGroupEnabled());

        when(pacmanConfigParamsService.getParameterValueByClientLevel(FeatureTogglesConfigParamName.FDS_AUTH_GROUPS_ENABLED.getParameterName())).thenReturn("false");
        assertFalse(service.isFDSAuthGroupEnabled());
    }

    @Test
    public void getPermsForUserAndProperty_allowFunctionSpaceConfigurationIsEnabledAndFunctionSpaceIsNotEnabled() throws Exception {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        Set<String> permissions = new HashSet<>();
        permissions.add("pageCode=function-space-configuration&access=readWrite&functions={functionSpaceConfigurationGeneralConfiguration:readOnly,functionSpaceConfigurationRateConfiguration:readOnly,functionSpaceConfigurationConferenceBanquet:readWrite,functionSpaceConfigurationAncillary:readWrite,functionSpaceConfigurationDayParts:readOnly,functionSpaceConfigurationFunctionRooms:readOnly,functionSpaceConfigurationForecastLevels:readOnly,functionSpaceConfigurationEventTypes:readOnly,functionSpaceConfigurationMarketSegment:readOnly,functionSpaceConfigurationPackageConfiguration:readOnly,functionSpaceConfigurationGuestRoomType:readOnly,functionSpaceConfigurationMinProfitPercentage:readOnly,functionSpaceConfigurationCeilingFloor:readOnly,functionSpaceConfigurationStatusCodes:readOnly}");
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(permissions);

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value())).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ALLOW_FUNCTION_SPACE_CONFIGURATION.value())).thenReturn(true);

        Set<String> permissionsToExternalUser = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertEquals(1, permissionsToExternalUser.size());
        permissionsToExternalUser.forEach(
                s-> assertTrue(s.contains(TetrisPermissionKey.FUNCTION_SPACE_CONFIGURATION)));
    }

    @Test
    public void getPermsForUserAndProperty_allowFunctionSpaceConfigurationIsDisableAndFunctionSpaceIsDisabled() throws Exception {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        Set<String> permissions = new HashSet<>();
        permissions.add("pageCode=function-space-configuration&access=readWrite&functions={functionSpaceConfigurationGeneralConfiguration:readOnly,functionSpaceConfigurationRateConfiguration:readOnly,functionSpaceConfigurationConferenceBanquet:readWrite,functionSpaceConfigurationAncillary:readWrite,functionSpaceConfigurationDayParts:readOnly,functionSpaceConfigurationFunctionRooms:readOnly,functionSpaceConfigurationForecastLevels:readOnly,functionSpaceConfigurationEventTypes:readOnly,functionSpaceConfigurationMarketSegment:readOnly,functionSpaceConfigurationPackageConfiguration:readOnly,functionSpaceConfigurationGuestRoomType:readOnly,functionSpaceConfigurationMinProfitPercentage:readOnly,functionSpaceConfigurationCeilingFloor:readOnly,functionSpaceConfigurationStatusCodes:readOnly}");
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(permissions);

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value())).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ALLOW_FUNCTION_SPACE_CONFIGURATION.value())).thenReturn(false);

        Set<String> permissionsToExternalUser = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertEquals(0, permissionsToExternalUser.size());
    }

    @Test
    public void getPermsForUserAndProperty_allowFunctionSpaceConfigurationIsEnabledAndFunctionSpaceIsEnabled() throws Exception {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        Set<String> permissions = new HashSet<>();
        permissions.add("pageCode=function-space-configuration&access=readWrite&functions={functionSpaceConfigurationGeneralConfiguration:readOnly,functionSpaceConfigurationRateConfiguration:readOnly,functionSpaceConfigurationConferenceBanquet:readWrite,functionSpaceConfigurationAncillary:readWrite,functionSpaceConfigurationDayParts:readOnly,functionSpaceConfigurationFunctionRooms:readOnly,functionSpaceConfigurationForecastLevels:readOnly,functionSpaceConfigurationEventTypes:readOnly,functionSpaceConfigurationMarketSegment:readOnly,functionSpaceConfigurationPackageConfiguration:readOnly,functionSpaceConfigurationGuestRoomType:readOnly,functionSpaceConfigurationMinProfitPercentage:readOnly,functionSpaceConfigurationCeilingFloor:readOnly,functionSpaceConfigurationStatusCodes:readOnly}");
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(permissions);

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ALLOW_FUNCTION_SPACE_CONFIGURATION.value())).thenReturn(true);

        Set<String> permissionsToExternalUser = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertEquals(1, permissionsToExternalUser.size());
        permissionsToExternalUser.forEach(
                s-> assertTrue(s.contains(TetrisPermissionKey.FUNCTION_SPACE_CONFIGURATION)));
    }

    @Test
    public void getPermsForUserAndProperty_allowFunctionSpaceConfigurationIsDisabledAndFunctionSpaceIsEnabled() throws Exception {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        Set<String> permissions = new HashSet<>();
        permissions.add("pageCode=function-space-configuration&access=readWrite&functions={functionSpaceConfigurationGeneralConfiguration:readOnly,functionSpaceConfigurationRateConfiguration:readOnly,functionSpaceConfigurationConferenceBanquet:readWrite,functionSpaceConfigurationAncillary:readWrite,functionSpaceConfigurationDayParts:readOnly,functionSpaceConfigurationFunctionRooms:readOnly,functionSpaceConfigurationForecastLevels:readOnly,functionSpaceConfigurationEventTypes:readOnly,functionSpaceConfigurationMarketSegment:readOnly,functionSpaceConfigurationPackageConfiguration:readOnly,functionSpaceConfigurationGuestRoomType:readOnly,functionSpaceConfigurationMinProfitPercentage:readOnly,functionSpaceConfigurationCeilingFloor:readOnly,functionSpaceConfigurationStatusCodes:readOnly}");
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(permissions);

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.FUNCTION_SPACE_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ALLOW_FUNCTION_SPACE_CONFIGURATION.value())).thenReturn(false);

        Set<String> permissionsToExternalUser = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertEquals(1, permissionsToExternalUser.size());
        permissionsToExternalUser.forEach(
                s-> assertTrue(s.contains(TetrisPermissionKey.FUNCTION_SPACE_CONFIGURATION)));
    }

    @Test
    public void getPermsForUserAndProperty_allowFunctionSpaceConfigurationIsDisabledAndGroupPricingIsEnabled() throws Exception {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        Set<String> permissions = new HashSet<>();
        permissions.add("pageCode=function-space-configuration&access=readWrite&functions={functionSpaceConfigurationGeneralConfiguration:readOnly,functionSpaceConfigurationRateConfiguration:readOnly,functionSpaceConfigurationConferenceBanquet:readWrite,functionSpaceConfigurationAncillary:readWrite,functionSpaceConfigurationDayParts:readOnly,functionSpaceConfigurationFunctionRooms:readOnly,functionSpaceConfigurationForecastLevels:readOnly,functionSpaceConfigurationEventTypes:readOnly,functionSpaceConfigurationMarketSegment:readOnly,functionSpaceConfigurationPackageConfiguration:readOnly,functionSpaceConfigurationGuestRoomType:readOnly,functionSpaceConfigurationMinProfitPercentage:readOnly,functionSpaceConfigurationCeilingFloor:readOnly,functionSpaceConfigurationStatusCodes:readOnly}");
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(permissions);

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED.value())).thenReturn(true);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ALLOW_FUNCTION_SPACE_CONFIGURATION.value())).thenReturn(false);

        Set<String> permissionsToExternalUser = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertEquals(0, permissionsToExternalUser.size());
    }

    @Test
    public void getPermsForUserAndProperty_allowFunctionSpaceConfigurationIsEnabledAndGroupPricingIsDisabled() throws Exception {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        Set<String> permissions = new HashSet<>();
        permissions.add("pageCode=function-space-configuration&access=readWrite&functions={functionSpaceConfigurationGeneralConfiguration:readOnly,functionSpaceConfigurationRateConfiguration:readOnly,functionSpaceConfigurationConferenceBanquet:readWrite,functionSpaceConfigurationAncillary:readWrite,functionSpaceConfigurationDayParts:readOnly,functionSpaceConfigurationFunctionRooms:readOnly,functionSpaceConfigurationForecastLevels:readOnly,functionSpaceConfigurationEventTypes:readOnly,functionSpaceConfigurationMarketSegment:readOnly,functionSpaceConfigurationPackageConfiguration:readOnly,functionSpaceConfigurationGuestRoomType:readOnly,functionSpaceConfigurationMinProfitPercentage:readOnly,functionSpaceConfigurationCeilingFloor:readOnly,functionSpaceConfigurationStatusCodes:readOnly}");
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(permissions);

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED.value())).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ALLOW_FUNCTION_SPACE_CONFIGURATION.value())).thenReturn(true);

        Set<String> permissionsToExternalUser = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertEquals(1, permissionsToExternalUser.size());
        permissionsToExternalUser.forEach(
                s-> assertTrue(s.contains(TetrisPermissionKey.FUNCTION_SPACE_CONFIGURATION)));
    }

    @Test
    public void getPermsForUserAndProperty_allowFunctionSpaceConfigurationIsEnabledAndNoPermissionToUser() throws Exception {
        service.setUserGlobalDBService(mockUserGlobalDBService);
        when(mockUserGlobalDBService.getGlobalUserById(anyInt())).thenReturn(getExternalUser());
        Set<String> permissions = new HashSet<>();
        permissions.add("pageCode=information-manager&access=readOnly");
        when(roleService.getPermsForUser(dn, propertyId, service)).thenReturn(permissions);

        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.GROUP_PRICING_ENABLED.value())).thenReturn(false);
        when(pacmanConfigParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ALLOW_FUNCTION_SPACE_CONFIGURATION.value())).thenReturn(true);

        Set<String> permissionsToExternalUser = service.getPermsForUserAndProperty(dn, propertyId, false);
        assertEquals(1, permissionsToExternalUser.size());
        permissionsToExternalUser.forEach(
                s-> assertFalse(s.contains(TetrisPermissionKey.FUNCTION_SPACE_CONFIGURATION)));
    }
}
