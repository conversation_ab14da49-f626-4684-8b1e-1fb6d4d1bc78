package com.ideas.tetris.pacman.services.bestavailablerate;

import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesChargeType;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesDecisionsSentBy;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesOffsetMethod;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesPackage;
import com.ideas.tetris.pacman.services.agilerates.configuration.service.AgileRatesConfigurationService;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.CPDecisionBAROutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.DecisionDailybarOutput;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyType;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.OptimalBarType;
import com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesOptimalBarsServiceTestContext;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.product.RoundingRule;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import static com.ideas.tetris.pacman.common.constants.Constants.CONTEXT_KEY;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesOptimalBarsServiceTestContext.createOutputs;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesOptimalBarsServiceTestContext.generateRangeOfDatesList;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.CHILD_ONE_OF_INDEPENDENT_PRODUCT;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.CHILD_ONE_OF_SYSTEM_DEFAULT;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.GRANDCHILD_ONE_OF_CHILD_ONE;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.INDEPENDENT_PRODUCT;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.SYSTEM_DEFAULT;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.calculatePercent;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.createAdultBreakfastPackage;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.createBreakfastPackageChargeType;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.createMockCpDecisionContext;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.createPricingRules;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.createProductRateOffsets;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.getDefaultPrecisionValueOf;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesServiceTestUtil.precisionTwoValueOf;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class AgileRatesCPOptimalBARServiceMockTest {

    @Mock
    PacmanConfigParamsService configParamsService;

    @Mock
    CPManagementService cpManagementService;

    @Mock
    PricingConfigurationService pricingConfigurationService;

    @Mock
    AgileRatesConfigurationService agileRatesConfigurationService;

    @Mock(name = "tenantCrudService")
    CrudService crudService;

    @InjectMocks
    CPOptimalBARService service;
    @Mock
    private DateService dateService;

    @BeforeEach
    public void setUp() {
        WorkContextType wc = new WorkContextType();
        wc.setUserId("1");
        wc.setPropertyId(1);
        wc.setClientCode("TEST");
        wc.setClientId(2);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
        when(dateService.getBusinessDate()).thenReturn(new java.util.Date());
    }

    @Test
    public void findCPDecisionBAROutputs() {
        AgileRatesOptimalBarsServiceTestContext context = new AgileRatesOptimalBarsServiceTestContext();
        LocalDate startDate = LocalDate.parse("2000-01-01");
        LocalDate endDate = LocalDate.parse("2000-01-03");
        context.setStartDate(startDate);
        context.setEndDate(endDate);
        Map<Product, List<Product>> products = context.getProducts();
        List<LocalDate> rangeOfDates = generateRangeOfDatesList(startDate, endDate);
        AccomClass accomClass = context.getBaseAccomType().getAccomClass();
        List<CPDecisionBAROutput> outputs = createOutputs(products, rangeOfDates, accomClass);

        //double the outputs with the same products to test the product level mapping
        outputs.addAll(createOutputs(products, rangeOfDates, accomClass));

        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(outputs);

        final TreeMap<LocalDate, Map<Product, List<CPDecisionBAROutput>>> treeMap =
                service.findCPDecisionBAROutputs(startDate, endDate);

        //assert treeMap dates
        assertEquals(3, treeMap.size());
        final ArrayList<LocalDate> dates = new ArrayList<>(treeMap.keySet());

        assertFalse(rangeOfDates.isEmpty());
        assertEquals(rangeOfDates.get(0), dates.get(0));
        assertEquals(rangeOfDates.get(1), dates.get(1));
        assertEquals(rangeOfDates.get(2), dates.get(2));

        //assert treeMap products
        final List<Map<Product, List<CPDecisionBAROutput>>> maps = new ArrayList<>(treeMap.values());
        assertEquals(3, maps.size());
        maps.forEach(map -> {
            //assert all products represented
            products.forEach((product, productList) -> {
                assertTrue(map.keySet().containsAll(productList));
            });
            assertEquals(8, map.size());
        });

        maps.forEach(map ->
                map.keySet().forEach(product -> {
                    assertEquals(2, map.get(product).size());
                    //assert all outputs represented
                    map.get(product).forEach(output -> assertTrue(outputs.remove(output)));
                })
        );

        assertEquals(0, outputs.size());

        verify(crudService, never()).<Product>findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
        verify(cpManagementService, never()).findCPDecisionsBetweenDatesWithCapacity(eq(startDate), eq(endDate), any(Product.class));
    }

    @Test
    public void roundOptimalBARsProductRateOffsetsFixedAtMinusTenOptimalBarTypeNullNoPackage() {
        final double PRODUCT_RATE_OFFSET_VALUE = -10;

        AgileRatesOptimalBarsServiceTestContext context = new AgileRatesOptimalBarsServiceTestContext();
        LocalDate startDate = LocalDate.parse("2000-01-01");
        LocalDate endDate = LocalDate.parse("2000-01-01");
        context.setStartDate(startDate);
        context.setEndDate(endDate);
        List<LocalDate> rangeOfDates = generateRangeOfDatesList(startDate, endDate);

        AccomType baseAccomType = context.getBaseAccomType();
        List<CPDecisionBAROutput> outputs = createOutputs(context.getProducts(), rangeOfDates, baseAccomType.getAccomClass());
        context.setOutputs(outputs);

        Map<Product, List<Product>> products = context.getProducts();

        //set inputs from what analytics would set
        context.setOptimalBarValuesByProductName(SYSTEM_DEFAULT, 100.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, 90.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, 80.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, 70.00);
        context.setOptimalBarValuesByProductName(INDEPENDENT_PRODUCT, 105.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, 95.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 85.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 75.00);

        CPDecisionContext mockCpDecisionContext =
                createMockCpDecisionContext(
                        products,
                        baseAccomType,
                        rangeOfDates,
                        outputs,
                        PRODUCT_RATE_OFFSET_VALUE,
                        false,
                        false,
                        false,
                        false,
                        false);

        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(outputs);
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(mockCpDecisionContext);
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);

        service.roundOptimalBARs(startDate, endDate);

        //assert BAR outputs
        List<CPDecisionBAROutput> defaultOutputs = context.getOutputsByProductName(SYSTEM_DEFAULT);
        CPDecisionBAROutput systemDefaultOutputStart = defaultOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(100), systemDefaultOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), systemDefaultOutputStart.getFinalBAR());

        //assert second gen
        List<CPDecisionBAROutput> childOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        CPDecisionBAROutput childOneOfBarOutputStart = childOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(90), childOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(89.99), childOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-10.00), childOneOfBarOutputStart.getAdjustmentValue());

        //assert second gen
        List<CPDecisionBAROutput> grandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput grandchildOfChildOneOutputStart = grandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(80), grandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(79.99), grandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-11.11), grandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert third gen
        List<CPDecisionBAROutput> greatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput greatGrandChildOfGrandchildOneOutputStart = greatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(70), greatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(69.99), greatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-10.00), greatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());

        //assert independent outputs
        List<CPDecisionBAROutput> independentOutputs = context.getOutputsByProductName(INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentOutputStart = independentOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(105), independentOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(104.99), independentOutputStart.getFinalBAR());

        //assert second gen independent
        List<CPDecisionBAROutput> independentChildOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentChildOneOfBarOutputStart = independentChildOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(95), independentChildOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(94.99), independentChildOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-10.00), independentChildOneOfBarOutputStart.getAdjustmentValue());

        //assert second gen independent
        List<CPDecisionBAROutput> independentGrandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGrandchildOfChildOneOutputStart = independentGrandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(85), independentGrandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(84.99), independentGrandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-10.53), independentGrandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert third gen independent
        List<CPDecisionBAROutput> independentGreatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGreatGrandChildOfGrandchildOneOutputStart = independentGreatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(75), independentGreatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(74.99), independentGreatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-10.00), independentGreatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());
    }

    private Map<Product, List<AgileRatesPackage>> createProductPackagesWhereAdultBreakfastForAllProducts(Map<Product, List<Product>> products) {
        HashMap<Product, List<AgileRatesPackage>> productPackages = new HashMap<>();
        products.forEach((product, productList) -> {
            productList.stream()
                    .filter(p -> !p.isSystemDefaultOrIndependentProduct())
                    .forEach(p ->
                            productPackages.put(
                                    p,
                                    Collections.singletonList(createAdultBreakfastPackage(precisionTwoValueOf(5.00)))));
        });
        return productPackages;
    }

    private Map<Product, List<AgileRatesPackage>> createProductPackagesWithChargeTypeDetail(Map<Product, List<Product>> products, AgileRatesChargeType agileRatesChargeType, boolean isSeason) {
        HashMap<Product, List<AgileRatesPackage>> productPackages = new HashMap<>();
        products.forEach((product, productList) -> {
            productList.stream()
                    .filter(p -> !p.isSystemDefaultOrIndependentProduct())
                    .forEach(p ->
                            productPackages.put(
                                    p,
                                    Collections.singletonList(createBreakfastPackageChargeType(precisionTwoValueOf(5.00), agileRatesChargeType, isSeason))));
        });
        return productPackages;
    }

    @Test
    public void roundOptimalBARsProductRateOffsetsFixedOptimalBarTypeNullWithPackage() {
        AgileRatesOptimalBarsServiceTestContext context = getContextNonOptimalProductsAllFixedAndWithPackage();

        Map<Product, List<Product>> products = context.getProducts();

        //set inputs from what analytics would set
        context.setOptimalBarValuesByProductName(SYSTEM_DEFAULT, 100.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, 90.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, 80.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, 70.00);
        context.setOptimalBarValuesByProductName(INDEPENDENT_PRODUCT, 105.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, 95.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 85.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 75.00);

        LocalDate startDate = context.getStartDate();
        LocalDate endDate = context.getEndDate();
        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(context.getOutputs());
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(context.getCpDecisionContext());
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);

        service.roundOptimalBARs(startDate, endDate);

        //assert BAR outputs
        List<CPDecisionBAROutput> defaultOutputs = context.getOutputsByProductName(SYSTEM_DEFAULT);
        CPDecisionBAROutput systemDefaultOutputStart = defaultOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(100), systemDefaultOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), systemDefaultOutputStart.getFinalBAR());

        //assert second gen
        List<CPDecisionBAROutput> childOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        CPDecisionBAROutput childOneOfBarOutputStart = childOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(90), childOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(94.99), childOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.00), childOneOfBarOutputStart.getAdjustmentValue());

        //assert second gen
        List<CPDecisionBAROutput> grandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput grandchildOfChildOneOutputStart = grandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(80), grandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(89.99), grandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.26), grandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert third gen
        List<CPDecisionBAROutput> greatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput greatGrandChildOfGrandchildOneOutputStart = greatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(70), greatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(84.99), greatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.00), greatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());

        //assert independent outputs
        List<CPDecisionBAROutput> independentOutputs = context.getOutputsByProductName(INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentOutputStart = independentOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(105), independentOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(104.99), independentOutputStart.getFinalBAR());

        //assert second gen independent
        List<CPDecisionBAROutput> independentChildOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentChildOneOfBarOutputStart = independentChildOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(95), independentChildOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), independentChildOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.00), independentChildOneOfBarOutputStart.getAdjustmentValue());

        //assert second gen independent
        List<CPDecisionBAROutput> independentGrandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGrandchildOfChildOneOutputStart = independentGrandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(85), independentGrandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(94.99), independentGrandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.00), independentGrandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert third gen independent
        List<CPDecisionBAROutput> independentGreatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGreatGrandChildOfGrandchildOneOutputStart = independentGreatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(75), independentGreatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(89.99), independentGreatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.00), independentGreatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());
    }

    @Test
    public void roundOptimalBARsProductRateOffsetsFixedOptimalBarTypeNullWithPackageAndChildAgeBucketPackageEnabledForOccupancySingleWithSeason() {
        AgileRatesOptimalBarsServiceTestContext context = getContextNonOptimalProductsAllFixedAndWithPackageChargeType(OccupancyType.SINGLE, AgileRatesChargeType.PER_ADULT, true);

        Map<Product, List<Product>> products = context.getProducts();

        //set inputs from what analytics would set
        context.setOptimalBarValuesByProductName(SYSTEM_DEFAULT, 100.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, 90.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, 80.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, 70.00);
        context.setOptimalBarValuesByProductName(INDEPENDENT_PRODUCT, 105.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, 95.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 85.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 75.00);

        LocalDate startDate = context.getStartDate();
        LocalDate endDate = context.getEndDate();
        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(context.getOutputs());
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(context.getCpDecisionContext());
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);

        service.roundOptimalBARs(startDate, endDate);

        //assert BAR outputs
        List<CPDecisionBAROutput> defaultOutputs = context.getOutputsByProductName(SYSTEM_DEFAULT);
        CPDecisionBAROutput systemDefaultOutputStart = defaultOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(100), systemDefaultOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), systemDefaultOutputStart.getFinalBAR());

        //assert second gen
        List<CPDecisionBAROutput> childOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        CPDecisionBAROutput childOneOfBarOutputStart = childOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(90), childOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), childOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(0.00), childOneOfBarOutputStart.getAdjustmentValue());

        //assert second gen
        List<CPDecisionBAROutput> grandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput grandchildOfChildOneOutputStart = grandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(80), grandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), grandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(0), grandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert third gen
        List<CPDecisionBAROutput> greatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput greatGrandChildOfGrandchildOneOutputStart = greatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(70), greatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), greatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(0), greatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());

        //assert independent outputs
        List<CPDecisionBAROutput> independentOutputs = context.getOutputsByProductName(INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentOutputStart = independentOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(105), independentOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(104.99), independentOutputStart.getFinalBAR());

        //assert second gen independent
        List<CPDecisionBAROutput> independentChildOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentChildOneOfBarOutputStart = independentChildOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(95), independentChildOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(104.99), independentChildOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(0), independentChildOneOfBarOutputStart.getAdjustmentValue());

        //assert second gen independent
        List<CPDecisionBAROutput> independentGrandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGrandchildOfChildOneOutputStart = independentGrandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(85), independentGrandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(104.99), independentGrandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(0), independentGrandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert third gen independent
        List<CPDecisionBAROutput> independentGreatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGreatGrandChildOfGrandchildOneOutputStart = independentGreatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(75), independentGreatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(104.99), independentGreatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(0), independentGreatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());
    }


    @Test
    public void roundOptimalBARsProductRateOffsetsFixedOptimalBarTypeNullWithPackageAndChildAgeBucketPackageEnabledForOccupancySingle() {
        AgileRatesOptimalBarsServiceTestContext context = getContextNonOptimalProductsAllFixedAndWithPackageChargeType(OccupancyType.SINGLE, AgileRatesChargeType.PER_ADULT);

        Map<Product, List<Product>> products = context.getProducts();

        //set inputs from what analytics would set
        context.setOptimalBarValuesByProductName(SYSTEM_DEFAULT, 100.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, 90.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, 80.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, 70.00);
        context.setOptimalBarValuesByProductName(INDEPENDENT_PRODUCT, 105.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, 95.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 85.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 75.00);

        LocalDate startDate = context.getStartDate();
        LocalDate endDate = context.getEndDate();
        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(context.getOutputs());
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(context.getCpDecisionContext());
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);

        service.roundOptimalBARs(startDate, endDate);

        //assert BAR outputs
        List<CPDecisionBAROutput> defaultOutputs = context.getOutputsByProductName(SYSTEM_DEFAULT);
        CPDecisionBAROutput systemDefaultOutputStart = defaultOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(100), systemDefaultOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), systemDefaultOutputStart.getFinalBAR());

        //assert second gen
        List<CPDecisionBAROutput> childOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        CPDecisionBAROutput childOneOfBarOutputStart = childOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(90), childOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(94.99), childOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.00), childOneOfBarOutputStart.getAdjustmentValue());

        //assert second gen
        List<CPDecisionBAROutput> grandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput grandchildOfChildOneOutputStart = grandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(80), grandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(89.99), grandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.26), grandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert third gen
        List<CPDecisionBAROutput> greatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput greatGrandChildOfGrandchildOneOutputStart = greatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(70), greatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(84.99), greatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.00), greatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());

        //assert independent outputs
        List<CPDecisionBAROutput> independentOutputs = context.getOutputsByProductName(INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentOutputStart = independentOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(105), independentOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(104.99), independentOutputStart.getFinalBAR());

        //assert second gen independent
        List<CPDecisionBAROutput> independentChildOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentChildOneOfBarOutputStart = independentChildOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(95), independentChildOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), independentChildOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.00), independentChildOneOfBarOutputStart.getAdjustmentValue());

        //assert second gen independent
        List<CPDecisionBAROutput> independentGrandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGrandchildOfChildOneOutputStart = independentGrandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(85), independentGrandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(94.99), independentGrandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.00), independentGrandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert third gen independent
        List<CPDecisionBAROutput> independentGreatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGreatGrandChildOfGrandchildOneOutputStart = independentGreatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(75), independentGreatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(89.99), independentGreatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.00), independentGreatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());
    }

    @Test
    public void roundOptimalBARsProductRateOffsetsFixedOptimalBarTypeNullWithPackageAndChildAgeBucketPackageEnabledForExtraChild() {
        AgileRatesOptimalBarsServiceTestContext context = getContextNonOptimalProductsAllFixedAndWithPackageChargeType(OccupancyType.SINGLE, AgileRatesChargeType.PER_CHILD);

        Map<Product, List<Product>> products = context.getProducts();

        //set inputs from what analytics would set
        context.setOptimalBarValuesByProductName(SYSTEM_DEFAULT, 100.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, 90.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, 80.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, 70.00);
        context.setOptimalBarValuesByProductName(INDEPENDENT_PRODUCT, 105.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, 95.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 85.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 75.00);

        LocalDate startDate = context.getStartDate();
        LocalDate endDate = context.getEndDate();
        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(context.getOutputs());
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(context.getCpDecisionContext());
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);

        service.roundOptimalBARs(startDate, endDate);

        //assert BAR outputs
        List<CPDecisionBAROutput> defaultOutputs = context.getOutputsByProductName(SYSTEM_DEFAULT);
        CPDecisionBAROutput systemDefaultOutputStart = defaultOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(100), systemDefaultOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), systemDefaultOutputStart.getFinalBAR());

        //assert second gen
        List<CPDecisionBAROutput> childOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        CPDecisionBAROutput childOneOfBarOutputStart = childOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(90), childOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(94.99), childOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.00), childOneOfBarOutputStart.getAdjustmentValue());

        //assert second gen
        List<CPDecisionBAROutput> grandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput grandchildOfChildOneOutputStart = grandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(80), grandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(89.99), grandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.26), grandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert third gen
        List<CPDecisionBAROutput> greatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput greatGrandChildOfGrandchildOneOutputStart = greatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(70), greatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(84.99), greatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.00), greatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());

        //assert independent outputs
        List<CPDecisionBAROutput> independentOutputs = context.getOutputsByProductName(INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentOutputStart = independentOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(105), independentOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(104.99), independentOutputStart.getFinalBAR());

        //assert second gen independent
        List<CPDecisionBAROutput> independentChildOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentChildOneOfBarOutputStart = independentChildOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(95), independentChildOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), independentChildOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.00), independentChildOneOfBarOutputStart.getAdjustmentValue());

        //assert second gen independent
        List<CPDecisionBAROutput> independentGrandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGrandchildOfChildOneOutputStart = independentGrandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(85), independentGrandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(94.99), independentGrandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.00), independentGrandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert third gen independent
        List<CPDecisionBAROutput> independentGreatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGreatGrandChildOfGrandchildOneOutputStart = independentGreatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(75), independentGreatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(89.99), independentGreatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.00), independentGreatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());
    }

    @Test
    public void roundOptimalBARsProductRateOffsetsFixedOptimalBarTypeNullWithPackageAndChildAgeBucketPackageEnabledForSETAmount() {
        AgileRatesOptimalBarsServiceTestContext context = getContextNonOptimalProductsAllFixedAndWithPackageChargeType(OccupancyType.SINGLE, AgileRatesChargeType.SET_AMOUNT);

        Map<Product, List<Product>> products = context.getProducts();

        //set inputs from what analytics would set
        context.setOptimalBarValuesByProductName(SYSTEM_DEFAULT, 100.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, 90.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, 80.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, 70.00);
        context.setOptimalBarValuesByProductName(INDEPENDENT_PRODUCT, 105.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, 95.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 85.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 75.00);

        LocalDate startDate = context.getStartDate();
        LocalDate endDate = context.getEndDate();
        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(context.getOutputs());
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(context.getCpDecisionContext());
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);

        service.roundOptimalBARs(startDate, endDate);

        //assert BAR outputs
        List<CPDecisionBAROutput> defaultOutputs = context.getOutputsByProductName(SYSTEM_DEFAULT);
        CPDecisionBAROutput systemDefaultOutputStart = defaultOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(100), systemDefaultOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), systemDefaultOutputStart.getFinalBAR());

        //assert second gen
        List<CPDecisionBAROutput> childOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        CPDecisionBAROutput childOneOfBarOutputStart = childOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(90), childOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(96.99), childOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-3.00), childOneOfBarOutputStart.getAdjustmentValue());

    }

    @Test
    public void roundOptimalBARsProductRateOffsetsFixedOptimalBarTypeNullWithPackageAndChildAgeBucketPackageEnabledForDouble() {
        AgileRatesOptimalBarsServiceTestContext context = getContextNonOptimalProductsAllFixedAndWithPackageChargeType(OccupancyType.DOUBLE, AgileRatesChargeType.PER_ADULT);

        //set inputs from what analytics would set
        context.setOptimalBarValuesByProductName(SYSTEM_DEFAULT, 100.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, 90.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, 80.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, 70.00);
        context.setOptimalBarValuesByProductName(INDEPENDENT_PRODUCT, 105.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, 95.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 85.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 75.00);

        LocalDate startDate = context.getStartDate();
        LocalDate endDate = context.getEndDate();
        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(context.getOutputs());
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(context.getCpDecisionContext());
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);

        service.roundOptimalBARs(startDate, endDate);

        //assert BAR outputs
        List<CPDecisionBAROutput> defaultOutputs = context.getOutputsByProductName(SYSTEM_DEFAULT);
        CPDecisionBAROutput systemDefaultOutputStart = defaultOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(100), systemDefaultOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), systemDefaultOutputStart.getFinalBAR());

        //assert second gen
        List<CPDecisionBAROutput> childOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        CPDecisionBAROutput childOneOfBarOutputStart = childOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(90), childOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), childOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(0.00), childOneOfBarOutputStart.getAdjustmentValue());

        //assert second gen
        List<CPDecisionBAROutput> grandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput grandchildOfChildOneOutputStart = grandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(80), grandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), grandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-0), grandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert third gen
        List<CPDecisionBAROutput> greatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput greatGrandChildOfGrandchildOneOutputStart = greatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(70), greatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), greatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(0.00), greatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());

        //assert independent outputs
        List<CPDecisionBAROutput> independentOutputs = context.getOutputsByProductName(INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentOutputStart = independentOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(105), independentOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(104.99), independentOutputStart.getFinalBAR());

        //assert second gen independent
        List<CPDecisionBAROutput> independentChildOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentChildOneOfBarOutputStart = independentChildOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(95), independentChildOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(104.99), independentChildOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(0.00), independentChildOneOfBarOutputStart.getAdjustmentValue());

        //assert second gen independent
        List<CPDecisionBAROutput> independentGrandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGrandchildOfChildOneOutputStart = independentGrandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(85), independentGrandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(104.99), independentGrandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(0.00), independentGrandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert third gen independent
        List<CPDecisionBAROutput> independentGreatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGreatGrandChildOfGrandchildOneOutputStart = independentGreatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(75), independentGreatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(104.99), independentGreatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(0.00), independentGreatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());
    }

    @Test
    public void roundOptimalBARsOptimizedAllFixedWithPackage() {
        AgileRatesOptimalBarsServiceTestContext context = getContextAllProductsIsOptimalAndFixedAndWithPackage();

        //set products as optimized
        Product child = context.getProductByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        child.setOptimized(true);
        Product grandChild = context.getProductByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        grandChild.setOptimized(true);
        Product greatGrandChild = context.getProductByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        greatGrandChild.setOptimized(true);
        Product independentChild = context.getProductByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        independentChild.setOptimized(true);
        Product independentGrandChild = context.getProductByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        independentGrandChild.setOptimized(true);
        Product independentGreatGrandChild = context.getProductByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        independentGreatGrandChild.setOptimized(true);

        //set inputs from analytics
        context.setOptimalBarValuesByProductName(SYSTEM_DEFAULT, 100.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, -10.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, -10.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, -10.00);
        context.setOptimalBarValuesByProductName(INDEPENDENT_PRODUCT, 105.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, -15.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, -15.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, -15.00);

        LocalDate startDate = context.getStartDate();
        LocalDate endDate = context.getEndDate();

        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(context.getOutputs());
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(context.getCpDecisionContext());
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);

        service.roundOptimalBARs(startDate, endDate);

        //assert BAR outputs
        CPDecisionBAROutput systemDefaultOutputStart = context.getOutputsByProductName(SYSTEM_DEFAULT).get(0);
        assertEquals(getDefaultPrecisionValueOf(100.00), systemDefaultOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), systemDefaultOutputStart.getFinalBAR());

        //assert second gen:
        //optimal bar: -10 SAS derived offset
        //final bar: optimal bar + 5 for package
        List<CPDecisionBAROutput> childOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        CPDecisionBAROutput childOneOfBarOutputStart = childOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-10), childOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(94.99), childOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.00), childOneOfBarOutputStart.getAdjustmentValue());

        //assert second gen:
        //optimal bar: -10 SAS derived offset
        //final bar: optimal bar + 10 for two gen of packages
        List<CPDecisionBAROutput> grandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput grandchildOfChildOneOutputStart = grandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-10), grandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(89.99), grandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.26), grandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert third gen:
        //optimal bar: -10 SAS derived offset
        //final bar: optimal bar + 15 for three gen of packages
        List<CPDecisionBAROutput> greatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput greatGrandChildOfGrandchildOneOutputStart = greatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-10), greatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(84.99), greatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.00), greatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());

        //assert independent outputs
        CPDecisionBAROutput independentOutputStart = context.getOutputsByProductName(INDEPENDENT_PRODUCT).get(0);
        assertEquals(getDefaultPrecisionValueOf(105.00), independentOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(104.99), independentOutputStart.getFinalBAR());

        //assert independent second gen:
        //optimal bar: -15 SAS derived offset
        //final bar: optimal bar + 5 for package
        List<CPDecisionBAROutput> independentChildOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        CPDecisionBAROutput indendentChildOneOfBarOutputStart = independentChildOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-15), indendentChildOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(94.99), indendentChildOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-10.00), indendentChildOneOfBarOutputStart.getAdjustmentValue());

        //assert independent second gen:
        //optimal bar: -15 SAS derived offset
        //final bar: optimal bar + 10 for two gen of packages
        List<CPDecisionBAROutput> independentGrandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGrandchildOfChildOneOutputStart = independentGrandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-15), independentGrandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(84.99), independentGrandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-10.53), independentGrandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert independent third gen:
        //optimal bar: -15 SAS derived offset
        //final bar: optimal bar + 15 for three gen of packages
        List<CPDecisionBAROutput> independentGreatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGreatGrandChildOfGrandchildOneOutputStart = independentGreatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-15), independentGreatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(74.99), independentGreatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-10.00), independentGreatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());
    }

    @Test
    public void roundOptimalBARsOptimizedAllFixedWithPackageAndChildAgeBucketPackageEnabled() {
        AgileRatesOptimalBarsServiceTestContext context = getContextAllProductsIsOptimalAndFixedAndWithPackageChargeType();

        //set products as optimized
        Product child = context.getProductByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        child.setOptimized(true);
        Product grandChild = context.getProductByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        grandChild.setOptimized(true);
        Product greatGrandChild = context.getProductByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        greatGrandChild.setOptimized(true);
        Product independentChild = context.getProductByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        independentChild.setOptimized(true);
        Product independentGrandChild = context.getProductByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        independentGrandChild.setOptimized(true);
        Product independentGreatGrandChild = context.getProductByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        independentGreatGrandChild.setOptimized(true);

        //set inputs from analytics
        context.setOptimalBarValuesByProductName(SYSTEM_DEFAULT, 100.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, -10.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, -10.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, -10.00);
        context.setOptimalBarValuesByProductName(INDEPENDENT_PRODUCT, 105.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, -15.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, -15.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, -15.00);

        LocalDate startDate = context.getStartDate();
        LocalDate endDate = context.getEndDate();

        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(context.getOutputs());
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(context.getCpDecisionContext());
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);

        service.roundOptimalBARs(startDate, endDate);

        //assert BAR outputs
        CPDecisionBAROutput systemDefaultOutputStart = context.getOutputsByProductName(SYSTEM_DEFAULT).get(0);
        assertEquals(getDefaultPrecisionValueOf(100.00), systemDefaultOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), systemDefaultOutputStart.getFinalBAR());

        //assert second gen:
        //optimal bar: -10 SAS derived offset
        //final bar: optimal bar + 5 for package
        List<CPDecisionBAROutput> childOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        CPDecisionBAROutput childOneOfBarOutputStart = childOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-10), childOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(94.99), childOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.00), childOneOfBarOutputStart.getAdjustmentValue());

        //assert second gen:
        //optimal bar: -10 SAS derived offset
        //final bar: optimal bar + 10 for two gen of packages
        List<CPDecisionBAROutput> grandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput grandchildOfChildOneOutputStart = grandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-10), grandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(89.99), grandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.26), grandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert third gen:
        //optimal bar: -10 SAS derived offset
        //final bar: optimal bar + 15 for three gen of packages
        List<CPDecisionBAROutput> greatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput greatGrandChildOfGrandchildOneOutputStart = greatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-10), greatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(84.99), greatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.00), greatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());

        //assert independent outputs
        CPDecisionBAROutput independentOutputStart = context.getOutputsByProductName(INDEPENDENT_PRODUCT).get(0);
        assertEquals(getDefaultPrecisionValueOf(105.00), independentOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(104.99), independentOutputStart.getFinalBAR());

        //assert independent second gen:
        //optimal bar: -15 SAS derived offset
        //final bar: optimal bar + 5 for package
        List<CPDecisionBAROutput> independentChildOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        CPDecisionBAROutput indendentChildOneOfBarOutputStart = independentChildOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-15), indendentChildOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(94.99), indendentChildOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-10.00), indendentChildOneOfBarOutputStart.getAdjustmentValue());

        //assert independent second gen:
        //optimal bar: -15 SAS derived offset
        //final bar: optimal bar + 10 for two gen of packages
        List<CPDecisionBAROutput> independentGrandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGrandchildOfChildOneOutputStart = independentGrandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-15), independentGrandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(84.99), independentGrandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-10.53), independentGrandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert independent third gen:
        //optimal bar: -15 SAS derived offset
        //final bar: optimal bar + 15 for three gen of packages
        List<CPDecisionBAROutput> independentGreatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGreatGrandChildOfGrandchildOneOutputStart = independentGreatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-15), independentGreatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(74.99), independentGreatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-10.00), independentGreatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());
    }

    @Test
    public void roundOptimalBARsMixedOptimizedAllFixedWithPackage() {
        double offsetValue = 30.00;
        AgileRatesOptimalBarsServiceTestContext context =
                getContextWithPackage(AgileRatesOffsetMethod.FIXED, offsetValue);

        //set products isOptimized true
        Product child = context.getProductByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        child.setOptimized(true);
        Product greatGrandChild = context.getProductByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        greatGrandChild.setOptimized(true);
        Product independentChild = context.getProductByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        independentChild.setOptimized(true);
        Product independentGreatGrandChild = context.getProductByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        independentGreatGrandChild.setOptimized(true);

        //set inputs from analytics
        double systemDefaultOptimalBar = 100.00;
        context.setOptimalBarValuesByProductName(SYSTEM_DEFAULT, systemDefaultOptimalBar);

        //child one IS optimized and fixed
        double childOneOptimizedOffset = -10.00;
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, childOneOptimizedOffset);
        List<CPDecisionBAROutput> childOneOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        childOneOutputs.forEach(output -> output.setOptimalBarType(OptimalBarType.FIXED));

        //grandchild one NOT optimized; offsetMethod fixed + 30
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, systemDefaultOptimalBar + childOneOptimizedOffset + offsetValue);
        List<CPDecisionBAROutput> grandchildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        grandchildOneOutputs.forEach(output -> {
            output.setOptimalBarType(OptimalBarType.PRICE);
        });

        //grandchild one IS optimized; offsetMethod fixed -10
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, 10.00);
        List<CPDecisionBAROutput> greatGrandChildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        greatGrandChildOneOutputs.forEach(output -> output.setOptimalBarType(OptimalBarType.FIXED));

        double independentOptimalBar = 105.00;
        context.setOptimalBarValuesByProductName(INDEPENDENT_PRODUCT, independentOptimalBar);

        //independent child one IS optimized and fixed
        double independentChildOneOptimizedOffset = -15.00;
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, independentChildOneOptimizedOffset);
        List<CPDecisionBAROutput> independentChildOneOutputs = context.getOutputsByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        independentChildOneOutputs.forEach(output -> output.setOptimalBarType(OptimalBarType.FIXED));

        //independent grandchild one NOT optimized; offsetMethod fixed + 30
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, independentOptimalBar + independentChildOneOptimizedOffset + offsetValue);
        List<CPDecisionBAROutput> independentGrandchildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        independentGrandchildOneOutputs.forEach(output -> {
            output.setOptimalBarType(OptimalBarType.PRICE);
        });

        //independent grandchild one IS optimized; offsetMethod fixed -10
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 15.00);
        List<CPDecisionBAROutput> independentGreatGrandChildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        independentGreatGrandChildOneOutputs.forEach(output -> output.setOptimalBarType(OptimalBarType.FIXED));

        LocalDate startDate = context.getStartDate();
        LocalDate endDate = context.getEndDate();

        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(context.getOutputs());
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(context.getCpDecisionContext());
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);

        service.roundOptimalBARs(startDate, endDate);

        //assert BAR outputs
        CPDecisionBAROutput systemDefaultOutputStart = context.getOutputsByProductName(SYSTEM_DEFAULT).get(0);
        assertEquals(getDefaultPrecisionValueOf(100.00), systemDefaultOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), systemDefaultOutputStart.getFinalBAR());

        //assert second gen:
        //optimal bar: -10 SAS derived offset
        //final bar: optimal bar + 5 for package
        List<CPDecisionBAROutput> childOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        CPDecisionBAROutput childOneOfBarOutputStart = childOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-10), childOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(94.99), childOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.00), childOneOfBarOutputStart.getAdjustmentValue());

        //assert second gen:
        //optimal bar: 90 + 30 for product rate offset
        //final bar: optimal bar + 10 for two gen of packages
        List<CPDecisionBAROutput> grandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput grandchildOfChildOneOutputStart = grandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(120), grandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(129.99), grandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(36.85), grandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert third gen:
        //optimal bar: 10 SAS derived offset
        //final bar: optimal bar + 15 for three gen of packages
        List<CPDecisionBAROutput> greatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput greatGrandChildOfGrandchildOneOutputStart = greatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(10), greatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(15.00), greatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());

        //assert independent outputs
        CPDecisionBAROutput independentOutputStart = context.getOutputsByProductName(INDEPENDENT_PRODUCT).get(0);
        assertEquals(getDefaultPrecisionValueOf(105.00), independentOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(104.99), independentOutputStart.getFinalBAR());

        //assert independent second gen:
        //optimal bar: -10 SAS derived offset
        //final bar: optimal bar + 5 for package
        List<CPDecisionBAROutput> independentChildOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentChildOneOfBarOutputStart = independentChildOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-15), independentChildOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(94.99), independentChildOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-10.00), independentChildOneOfBarOutputStart.getAdjustmentValue());

        //assert independent second gen:
        //optimal bar: 90 + 30 for product rate offset
        //final bar: optimal bar + 10 for two gen of packages
        List<CPDecisionBAROutput> independentGrandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGrandchildOfChildOneOutputStart = independentGrandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(120), independentGrandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(129.99), independentGrandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(36.85), independentGrandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert independent third gen:
        //optimal bar: 10 SAS derived offset
        //final bar: optimal bar + 15 for three gen of packages
        List<CPDecisionBAROutput> independentGreatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGreatGrandChildOfGrandchildOneOutputStart = independentGreatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(15), independentGreatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(20.00), independentGreatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());
    }

    @Test
    public void roundOptimalBARsMixedOptimizedAllFixedWithPackageAndChildAgeBucketPackageEnabled() {
        double offsetValue = 30.00;
        AgileRatesOptimalBarsServiceTestContext context =
                getContextWithPackageChargeType(AgileRatesOffsetMethod.FIXED, offsetValue);

        //set products isOptimized true
        Product child = context.getProductByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        child.setOptimized(true);
        Product greatGrandChild = context.getProductByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        greatGrandChild.setOptimized(true);
        Product independentChild = context.getProductByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        independentChild.setOptimized(true);
        Product independentGreatGrandChild = context.getProductByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        independentGreatGrandChild.setOptimized(true);

        //set inputs from analytics
        double systemDefaultOptimalBar = 100.00;
        context.setOptimalBarValuesByProductName(SYSTEM_DEFAULT, systemDefaultOptimalBar);

        //child one IS optimized and fixed
        double childOneOptimizedOffset = -10.00;
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, childOneOptimizedOffset);
        List<CPDecisionBAROutput> childOneOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        childOneOutputs.forEach(output -> output.setOptimalBarType(OptimalBarType.FIXED));

        //grandchild one NOT optimized; offsetMethod fixed + 30
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, systemDefaultOptimalBar + childOneOptimizedOffset + offsetValue);
        List<CPDecisionBAROutput> grandchildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        grandchildOneOutputs.forEach(output -> {
            output.setOptimalBarType(OptimalBarType.PRICE);
        });

        //grandchild one IS optimized; offsetMethod fixed -10
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, 10.00);
        List<CPDecisionBAROutput> greatGrandChildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        greatGrandChildOneOutputs.forEach(output -> output.setOptimalBarType(OptimalBarType.FIXED));

        double independentOptimalBar = 105.00;
        context.setOptimalBarValuesByProductName(INDEPENDENT_PRODUCT, independentOptimalBar);

        //independent child one IS optimized and fixed
        double independentChildOneOptimizedOffset = -15.00;
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, independentChildOneOptimizedOffset);
        List<CPDecisionBAROutput> independentChildOneOutputs = context.getOutputsByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        independentChildOneOutputs.forEach(output -> output.setOptimalBarType(OptimalBarType.FIXED));

        //independent grandchild one NOT optimized; offsetMethod fixed + 30
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, independentOptimalBar + independentChildOneOptimizedOffset + offsetValue);
        List<CPDecisionBAROutput> independentGrandchildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        independentGrandchildOneOutputs.forEach(output -> {
            output.setOptimalBarType(OptimalBarType.PRICE);
        });

        //independent grandchild one IS optimized; offsetMethod fixed -10
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 15.00);
        List<CPDecisionBAROutput> independentGreatGrandChildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        independentGreatGrandChildOneOutputs.forEach(output -> output.setOptimalBarType(OptimalBarType.FIXED));

        LocalDate startDate = context.getStartDate();
        LocalDate endDate = context.getEndDate();

        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(context.getOutputs());
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(context.getCpDecisionContext());
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);

        service.roundOptimalBARs(startDate, endDate);

        //assert BAR outputs
        CPDecisionBAROutput systemDefaultOutputStart = context.getOutputsByProductName(SYSTEM_DEFAULT).get(0);
        assertEquals(getDefaultPrecisionValueOf(100.00), systemDefaultOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), systemDefaultOutputStart.getFinalBAR());

        //assert second gen:
        //optimal bar: -10 SAS derived offset
        //final bar: optimal bar + 5 for package
        List<CPDecisionBAROutput> childOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        CPDecisionBAROutput childOneOfBarOutputStart = childOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-10), childOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(94.99), childOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.00), childOneOfBarOutputStart.getAdjustmentValue());

        //assert second gen:
        //optimal bar: 90 + 30 for product rate offset
        //final bar: optimal bar + 10 for two gen of packages
        List<CPDecisionBAROutput> grandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput grandchildOfChildOneOutputStart = grandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(120), grandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(129.99), grandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(36.85), grandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert third gen:
        //optimal bar: 10 SAS derived offset
        //final bar: optimal bar + 15 for three gen of packages
        List<CPDecisionBAROutput> greatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput greatGrandChildOfGrandchildOneOutputStart = greatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(10), greatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(15.00), greatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());

        //assert independent outputs
        CPDecisionBAROutput independentOutputStart = context.getOutputsByProductName(INDEPENDENT_PRODUCT).get(0);
        assertEquals(getDefaultPrecisionValueOf(105.00), independentOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(104.99), independentOutputStart.getFinalBAR());

        //assert independent second gen:
        //optimal bar: -10 SAS derived offset
        //final bar: optimal bar + 5 for package
        List<CPDecisionBAROutput> independentChildOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentChildOneOfBarOutputStart = independentChildOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-15), independentChildOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(94.99), independentChildOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-10.00), independentChildOneOfBarOutputStart.getAdjustmentValue());

        //assert independent second gen:
        //optimal bar: 90 + 30 for product rate offset
        //final bar: optimal bar + 10 for two gen of packages
        List<CPDecisionBAROutput> independentGrandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGrandchildOfChildOneOutputStart = independentGrandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(120), independentGrandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(129.99), independentGrandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(36.85), independentGrandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert independent third gen:
        //optimal bar: 10 SAS derived offset
        //final bar: optimal bar + 15 for three gen of packages
        List<CPDecisionBAROutput> independentGreatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGreatGrandChildOfGrandchildOneOutputStart = independentGreatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(15), independentGreatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(20.00), independentGreatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());
    }

    @Test
    public void roundOptimalBARsMixedOptimizedMixedOffsetsWithPackage() {
        double offsetPercentValue = -5;
        AgileRatesOptimalBarsServiceTestContext context = getContextWithPackage(AgileRatesOffsetMethod.PERCENTAGE, offsetPercentValue);

        //GIVEN
        double systemDefaultOptimalBar = 100.00;
        context.setOptimalBarValuesByProductName(SYSTEM_DEFAULT, systemDefaultOptimalBar);

        //set products as optimized
        Product child = context.getProductByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        child.setOptimized(true);
        Product greatGrandChild = context.getProductByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        greatGrandChild.setOptimized(true);

        double independentOptimalBar = 105.00;
        context.setOptimalBarValuesByProductName(INDEPENDENT_PRODUCT, independentOptimalBar);
        Product independentChild = context.getProductByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        independentChild.setOptimized(true);
        Product independentGreatGrandChild = context.getProductByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        independentGreatGrandChild.setOptimized(true);

        //child one IS optimized and fixed
        double childOneOptimizedOffset = -10.00;
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, childOneOptimizedOffset);
        List<CPDecisionBAROutput> childOneOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        childOneOutputs.forEach(output -> output.setOptimalBarType(OptimalBarType.FIXED));

        //grandchild one NOT optimized; offset -5%
        double childOptimalBarRate = systemDefaultOptimalBar + childOneOptimizedOffset;
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, childOptimalBarRate + calculatePercent(childOptimalBarRate, offsetPercentValue));
        List<CPDecisionBAROutput> grandchildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        grandchildOneOutputs.forEach(output -> {
            output.setOptimalBarType(OptimalBarType.PRICE);
        });

        //grandchild one IS optimized; offsetMethod fixed -10
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, 10.00);
        List<CPDecisionBAROutput> greatGrandChildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        greatGrandChildOneOutputs.forEach(output -> output.setOptimalBarType(OptimalBarType.PERCENT));

        //independent child one IS optimized and fixed
        double independentChildOneOptimizedOffset = -10.00;
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, independentChildOneOptimizedOffset);
        List<CPDecisionBAROutput> independentChildOneOutputs = context.getOutputsByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        independentChildOneOutputs.forEach(output -> output.setOptimalBarType(OptimalBarType.FIXED));

        //independent grandchild one NOT optimized; offset -5%
        double independentChildOptimalBarRate = independentOptimalBar + independentChildOneOptimizedOffset;
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, independentChildOptimalBarRate + calculatePercent(independentChildOptimalBarRate, offsetPercentValue));
        List<CPDecisionBAROutput> independentGrandchildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        independentGrandchildOneOutputs.forEach(output -> {
            output.setOptimalBarType(OptimalBarType.PRICE);
        });

        //grandchild one IS optimized; offsetMethod fixed -10
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 10.00);
        List<CPDecisionBAROutput> independentGreatGrandChildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        independentGreatGrandChildOneOutputs.forEach(output -> output.setOptimalBarType(OptimalBarType.PERCENT));

        LocalDate startDate = context.getStartDate();
        LocalDate endDate = context.getEndDate();

        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(context.getOutputs());
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(context.getCpDecisionContext());
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);

        //WHEN
        service.roundOptimalBARs(startDate, endDate);

        //THEN
        //assert BAR outputs
        CPDecisionBAROutput systemDefaultOutputStart = context.getOutputsByProductName(SYSTEM_DEFAULT).get(0);
        assertEquals(getDefaultPrecisionValueOf(100.00), systemDefaultOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), systemDefaultOutputStart.getFinalBAR());

        //assert second gen:
        //optimal bar: -10 SAS derived offset
        //final bar: optimal bar + 5 for package
        List<CPDecisionBAROutput> childOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        CPDecisionBAROutput childOneOfBarOutputStart = childOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-10), childOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(94.99), childOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.00), childOneOfBarOutputStart.getAdjustmentValue());

        //assert third gen:
        //optimal bar: 90 - 5% for product rate offset
        //final bar: parent final bar (94.99) - 4.74950  + 5 for package = 94.99 (95.2405 rounded)
        List<CPDecisionBAROutput> grandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput grandchildOfChildOneOutputStart = grandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(85.50), grandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(94.99), grandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(0), grandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert fourth gen:
        //optimal bar: 10 SAS derived offset
        //final bar: parent final bar (94.99) + 9.49900 + 5 for package = 109.99 (109.48900 rounded)
        List<CPDecisionBAROutput> greatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput greatGrandChildOfGrandchildOneOutputStart = greatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(10), greatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(109.99), greatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(15.00), greatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());

        //assert independent outputs
        CPDecisionBAROutput independentOutputStart = context.getOutputsByProductName(INDEPENDENT_PRODUCT).get(0);
        assertEquals(getDefaultPrecisionValueOf(105.00), independentOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(104.99), independentOutputStart.getFinalBAR());

        //assert independent second gen:
        //optimal bar: -10 SAS derived offset
        //final bar: optimal bar + 5 for package
        List<CPDecisionBAROutput> indepdentChildOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentChildOneOfBarOutputStart = indepdentChildOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-10), independentChildOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), independentChildOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-5.00), independentChildOneOfBarOutputStart.getAdjustmentValue());

        //assert independent third gen:
        //optimal bar: 90 - 5% for product rate offset
        //final bar: parent final bar (104.99) - 4.74950  + 5 for package = 99.99 (99.2405 rounded)
        List<CPDecisionBAROutput> independentGrandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGrandchildOfChildOneOutputStart = independentGrandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(90.25), independentGrandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), independentGrandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(0), independentGrandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert independent fourth gen:
        //optimal bar: 10 SAS derived offset
        //final bar: parent final bar (99.99) + 9.49900 + 5 for package = 114.99 (114.48900 rounded)
        List<CPDecisionBAROutput> independentGreatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGreatGrandChildOfGrandchildOneOutputStart = independentGreatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(10), independentGreatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(114.99), independentGreatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(15.00), independentGreatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());
    }

    @Test
    public void roundOptimalBarsProductRoundingRuleNone() {
        //GIVEN
        //4 generations of optimized, rounding rule NONE products
        AgileRatesOptimalBarsServiceTestContext context = createBaseContext();
        Map<Product, List<Product>> products = context.getProducts();
        products.forEach((product, productList) -> {
            productList.stream()
                    .filter(p -> !p.isSystemDefaultOrIndependentProduct())
                    .forEach(p -> {
                        p.setRoundingRule(RoundingRule.NONE);
                        p.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
                    });
        });

        //bar optimal bar = 123.45
        context.setOptimalBarValuesByProductName(SYSTEM_DEFAULT, 123.45);

        //child optimal bar = 132.0915 (123.45 + 7%)
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, 132.0915);

        //grandchild optimal bar = 141.33791 (132.0915 + 7%)
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, 141.33791);

        //great grandchild optimal bar = 151.23156 (141.33791 + 7%)
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, 151.23156);

        //independent optimal bar = 123.45
        context.setOptimalBarValuesByProductName(INDEPENDENT_PRODUCT, 128.45);

        //independent child optimal bar = 132.0915 (123.45 + 7%)
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, 137.0915);

        //independent grandchild optimal bar = 141.33791 (132.0915 + 7%)
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 146.33791);

        //independent great grandchild optimal bar = 151.23156 (141.33791 + 7%)
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 156.23156);

        LocalDate startDate = context.getStartDate();
        LocalDate endDate = context.getEndDate();
        AccomType baseAccomType = context.getBaseAccomType();
        context.setCpDecisionContext(
                createMockCpDecisionContext(
                        OccupancyType.SINGLE,
                        context.getProducts(),
                        Collections.emptyMap(),
                        createPricingRules(),
                        baseAccomType,
                        generateRangeOfDatesList(context.getStartDate(), context.getEndDate()),
                        0,
                        new HashMap<>(),
                        new HashMap<>(),
                        createProductRateOffsets(
                                baseAccomType.getAccomClass().getId(),
                                context.getOutputs(),
                                AgileRatesOffsetMethod.PERCENTAGE,
                                7.0),
                        false,
                        false,
                        false,
                        false,
                        false));

        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(context.getCpDecisionContext());
        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(context.getOutputs());

        //WHEN
        service.roundOptimalBARs(startDate, endDate);

        //THEN
        //assert BAR outputs
        CPDecisionBAROutput systemDefaultOutputStart = context.getOutputsByProductName(SYSTEM_DEFAULT).get(0);
        assertEquals(getDefaultPrecisionValueOf(123.45), systemDefaultOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(122.99), systemDefaultOutputStart.getFinalBAR());

        //assert second gen:
        //optimal bar: 123.45 + 7% (8.6415) = 132.0915
        //final bar: 122.99 + 7% (8.6093) = 131.5993
        List<CPDecisionBAROutput> childOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        CPDecisionBAROutput childOneOfBarOutputStart = childOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(132.0915), childOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(131.60), childOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(7.00), childOneOfBarOutputStart.getAdjustmentValue());

        //assert third gen:
        //optimal bar: 132.09150 + 7% (9.246405) = 141.33791
        //final bar: 131.60 + 7% (9.212) = 140.812
        List<CPDecisionBAROutput> grandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput grandchildOfChildOneOutputStart = grandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(141.33791), grandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(140.81), grandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(7.00), grandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert fourth gen:
        //optimal bar: 141.33791 + 7% (9.89365) = 151.23156
        //final bar: 140.81 + 7% (9.8567) = 150.6667
        List<CPDecisionBAROutput> greatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput greatGrandChildOfGrandchildOneOutputStart = greatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(151.23156), greatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(150.67), greatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(7.00), greatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());

        //assert BAR outputs
        CPDecisionBAROutput independentOutputStart = context.getOutputsByProductName(INDEPENDENT_PRODUCT).get(0);
        assertEquals(getDefaultPrecisionValueOf(128.45), independentOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(127.99), independentOutputStart.getFinalBAR());

        //assert second gen:
        //optimal bar: 128.45 + 7%  = 132.0915
        //final bar: 127.99 + 7%  = 136.95
        List<CPDecisionBAROutput> independentChildOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentChildOneOfBarOutputStart = independentChildOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(137.0915), independentChildOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(136.95), independentChildOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(7.00), independentChildOneOfBarOutputStart.getAdjustmentValue());

        //assert third gen:
        //optimal bar: 132.0915 + 7%  = 146.33791
        //final bar: 136.95 + 7%  = 146.54
        List<CPDecisionBAROutput> independentGrandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGrandchildOfChildOneOutputStart = independentGrandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(146.33791), independentGrandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(146.54), independentGrandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(7.00), independentGrandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert fourth gen:
        //optimal bar: 146.33791 + 7% = 156.23156
        //final bar: 146.54 + 7% = 156.80
        List<CPDecisionBAROutput> independentGreatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGreatGrandChildOfGrandchildOneOutputStart = independentGreatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(156.23156), independentGreatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(156.80), independentGreatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(7.00), independentGreatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());
    }

    @Test
    public void roundOptimalBarsProductRoundingRuleDown() {
        //GIVEN
        //4 generations of optimized, rounding rule NONE products
        AgileRatesOptimalBarsServiceTestContext context = createBaseContext();
        Map<Product, List<Product>> products = context.getProducts();
        products.forEach((product, productList) -> {
            productList.stream()
                    .filter(p -> !p.isSystemDefaultOrIndependentProduct())
                    .forEach(p -> {
                        p.setRoundingRule(RoundingRule.DOWN);
                        p.setOffsetMethod(AgileRatesOffsetMethod.PERCENTAGE);
                    });
        });

        //bar optimal bar = 123.45
        context.setOptimalBarValuesByProductName(SYSTEM_DEFAULT, 123.45);

        //child optimal bar = 132.0915 (123.45 + 7%)
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, 132.0915);

        //grandchild optimal bar = 141.33791 (132.0915 + 7%)
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, 141.33791);

        //great grandchild optimal bar = 151.23156 (141.33791 + 7%)
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, 151.23156);

        //independent optimal bar = 128.45
        context.setOptimalBarValuesByProductName(INDEPENDENT_PRODUCT, 128.45);

        //independent child optimal bar = 137.4415 (128.45 + 7%)
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, 137.4415);

        //independent grandchild optimal bar = 147.062405 (137.4415 + 7%)
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 147.062405);

        //independent great grandchild optimal bar = 157.35677335 (147.062405 + 7%)
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 157.35677335);


        LocalDate startDate = context.getStartDate();
        LocalDate endDate = context.getEndDate();
        AccomType baseAccomType = context.getBaseAccomType();
        context.setCpDecisionContext(
                createMockCpDecisionContext(
                        OccupancyType.SINGLE,
                        context.getProducts(),
                        Collections.emptyMap(),
                        createPricingRules(),
                        baseAccomType,
                        generateRangeOfDatesList(context.getStartDate(), context.getEndDate()),
                        0,
                        new HashMap<>(),
                        new HashMap<>(),
                        createProductRateOffsets(
                                baseAccomType.getAccomClass().getId(),
                                context.getOutputs(),
                                AgileRatesOffsetMethod.PERCENTAGE,
                                7.0),
                        false,
                        false,
                        false,
                        false,
                        false));

        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(context.getCpDecisionContext());
        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(context.getOutputs());

        //WHEN
        service.roundOptimalBARs(startDate, endDate);

        //THEN
        //assert BAR outputs
        CPDecisionBAROutput systemDefaultOutputStart = context.getOutputsByProductName(SYSTEM_DEFAULT).get(0);
        assertEquals(getDefaultPrecisionValueOf(123.45), systemDefaultOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(122.99), systemDefaultOutputStart.getFinalBAR());

        //assert second gen:
        //optimal bar: 123.45 + 7% (8.6415) = 132.0915
        //final bar: 122.99 + 7% (8.6093) = 131.00 (131.5993 rounded down)
        List<CPDecisionBAROutput> childOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        CPDecisionBAROutput childOneOfBarOutputStart = childOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(132.09150), childOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(131.00), childOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(6.51), childOneOfBarOutputStart.getAdjustmentValue());

        //assert third gen:
        //optimal bar: 132.09150 + 7% (9.246405) = 141.33791
        //final bar: 131.00 + 7% (9.17) = 140.00 (140.17 rounded down)
        List<CPDecisionBAROutput> grandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput grandchildOfChildOneOutputStart = grandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(141.33791), grandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(140.00), grandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(6.87), grandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert fourth gen:
        //optimal bar: 141.33791 + 7% (9.89365) = 151.23156
        //final bar: 140.00 + 7% (9.8) = 149.00 (149.8 rounded down)
        List<CPDecisionBAROutput> greatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput greatGrandChildOfGrandchildOneOutputStart = greatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(151.23156), greatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(149.00), greatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(6.43), greatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());

        //assert independent outputs
        CPDecisionBAROutput independentOutputStart = context.getOutputsByProductName(INDEPENDENT_PRODUCT).get(0);
        assertEquals(getDefaultPrecisionValueOf(128.45), independentOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(127.99), independentOutputStart.getFinalBAR());

        //assert independent second gen:
        //optimal bar: 128.45 + 7% = 137.44150
        //final bar: 127.99 + 7% = 136.00 (136.9493 rounded down)
        List<CPDecisionBAROutput> independentChildOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentChildOneOfBarOutputStart = independentChildOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(137.44150), independentChildOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(136.00), independentChildOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(6.26), independentChildOneOfBarOutputStart.getAdjustmentValue());

        //assert independent third gen:
        //optimal bar: 137.44150 + 7%  = 147.06241
        //final bar: 136.00 + 7% = 145.00 (145.52 rounded down)
        List<CPDecisionBAROutput> independentGrandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGrandchildOfChildOneOutputStart = independentGrandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(147.06241), independentGrandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(145.00), independentGrandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(6.62), independentGrandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert independent fourth gen:
        //optimal bar: 147.06241 + 7%  = 157.35677
        //final bar: 145.00 + 7% = 155.00 (155.15 rounded down)
        List<CPDecisionBAROutput> independentGreatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGreatGrandChildOfGrandchildOneOutputStart = independentGreatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(157.35677), independentGreatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(155.00), independentGreatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(6.90), independentGreatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());
    }

    @Test
    public void roundOptimalBarsProductRoundingRuleUp() {
        //GIVEN
        //4 generations of optimized, rounding rule NONE products
        AgileRatesOptimalBarsServiceTestContext context = createBaseContext();
        Map<Product, List<Product>> products = context.getProducts();
        products.forEach((product, productList) -> {
            productList.stream()
                    .filter(p -> !p.isSystemDefaultOrIndependentProduct())
                    .forEach(p -> {
                        p.setRoundingRule(RoundingRule.UP);
                    });
        });
        //bar optimal bar = 123.45
        context.setOptimalBarValuesByProductName(SYSTEM_DEFAULT, 123.45);

        //child optimal bar = 132.0915 (123.45 + 7%)
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, 132.0915);

        //grandchild optimal bar = 141.33791 (132.0915 + 7%)
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, 141.33791);

        //great grandchild optimal bar = 151.23156 (141.33791 + 7%)
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, 151.23156);

        //independent optimal bar = 128.45
        context.setOptimalBarValuesByProductName(INDEPENDENT_PRODUCT, 128.45);

        //independent child optimal bar = 137.4415 (128.45 + 7%)
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, 137.4415);

        //independent grandchild optimal bar = 147.062405 (137.4415 + 7%)
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 147.062405);

        //independent great grandchild optimal bar = 157.35677335 (147.062405 + 7%)
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 157.35677335);

        LocalDate startDate = context.getStartDate();
        LocalDate endDate = context.getEndDate();
        AccomType baseAccomType = context.getBaseAccomType();
        context.setCpDecisionContext(
                createMockCpDecisionContext(
                        OccupancyType.SINGLE,
                        context.getProducts(),
                        Collections.emptyMap(),
                        createPricingRules(),
                        baseAccomType,
                        generateRangeOfDatesList(context.getStartDate(), context.getEndDate()),
                        0,
                        new HashMap<>(),
                        new HashMap<>(),
                        createProductRateOffsets(
                                baseAccomType.getAccomClass().getId(),
                                context.getOutputs(),
                                AgileRatesOffsetMethod.PERCENTAGE,
                                7.0),
                        false,
                        false,
                        false,
                        false,
                        false));

        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(context.getCpDecisionContext());
        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(context.getOutputs());

        //WHEN
        service.roundOptimalBARs(startDate, endDate);

        //THEN
        //assert BAR outputs
        CPDecisionBAROutput systemDefaultOutputStart = context.getOutputsByProductName(SYSTEM_DEFAULT).get(0);
        assertEquals(getDefaultPrecisionValueOf(123.45), systemDefaultOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(122.99), systemDefaultOutputStart.getFinalBAR());

        //assert second gen:
        //optimal bar: 123.45 + 7% (8.6415) = 132.0915
        //final bar: 122.99 + 7% (8.6093) = 132.00 (131.5993 rounded up)
        List<CPDecisionBAROutput> childOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        CPDecisionBAROutput childOneOfBarOutputStart = childOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(132.09150), childOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(132.00), childOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(9.01), childOneOfBarOutputStart.getAdjustmentValue());

        //assert third gen:
        //optimal bar: 132.09150 + 7% (9.246405) = 141.33791
        //final bar: 132.00 + 7% (9.24) = 142.00 (141.24 rounded up)
        List<CPDecisionBAROutput> grandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput grandchildOfChildOneOutputStart = grandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(141.33791), grandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(142.00), grandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(7.58), grandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert fourth gen:
        //optimal bar: 141.33791 + 7% (9.89365) = 151.23156
        //final bar: 142.00 + 7% (9.94) = 152.00 (151.94 rounded up)
        List<CPDecisionBAROutput> greatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput greatGrandChildOfGrandchildOneOutputStart = greatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(151.23156), greatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(152.00), greatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(10.00), greatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());

        //assert independent outputs
        CPDecisionBAROutput independentOutputStart = context.getOutputsByProductName(INDEPENDENT_PRODUCT).get(0);
        assertEquals(getDefaultPrecisionValueOf(128.45), independentOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(127.99), independentOutputStart.getFinalBAR());

        //assert independent second gen:
        //optimal bar: 128.45 + 7%  = 132.0915
        //final bar: 127.99 + 7%  = 137.00 (137.44150 rounded up)
        List<CPDecisionBAROutput> independentChildOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentChildOneOfBarOutputStart = independentChildOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(137.44150), independentChildOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(137.00), independentChildOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(9.01), independentChildOneOfBarOutputStart.getAdjustmentValue());

        //assert independent third gen:
        //optimal bar: 137.44150 + 7%  = 147.06241
        //final bar: 137.00 + 7%  = 147.00 (146.59 rounded up)
        List<CPDecisionBAROutput> independentGrandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGrandchildOfChildOneOutputStart = independentGrandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(147.06241), independentGrandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(147.00), independentGrandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(7.30), independentGrandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert independent fourth gen:
        //optimal bar: 147.06241 + 7%  = 157.35677
        //final bar: 147.00 + 7%  = 158.00 (157.29 rounded up)
        List<CPDecisionBAROutput> independentGreatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGreatGrandChildOfGrandchildOneOutputStart = independentGreatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(157.35677), independentGreatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(158.00), independentGreatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(11.00), independentGreatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());
    }

    @Test
    public void roundOptimalBarsProductRoundingRuleNearestWholeNumber() {
        //GIVEN
        //4 generations of optimized, rounding rule NONE products
        AgileRatesOptimalBarsServiceTestContext context = createBaseContext();
        Map<Product, List<Product>> products = context.getProducts();
        products.forEach((product, productList) -> {
            productList.stream()
                    .filter(p -> !p.isSystemDefaultOrIndependentProduct())
                    .forEach(p -> {
                        p.setRoundingRule(RoundingRule.WHOLE);
                    });
        });
        //bar optimal bar = 123.45
        context.setOptimalBarValuesByProductName(SYSTEM_DEFAULT, 123.45);

        //child optimal bar = 132.0915 (123.45 + 7%)
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, 132.0915);

        //grandchild optimal bar = 141.33791 (132.0915 + 7%)
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, 141.33791);

        //great grandchild optimal bar = 151.23156 (141.33791 + 7%)
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, 151.23156);

        //independent optimal bar = 128.45
        context.setOptimalBarValuesByProductName(INDEPENDENT_PRODUCT, 128.45);

        //independent child optimal bar = 137.4415 (128.45 + 7%)
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, 137.4415);

        //independent grandchild optimal bar = 147.062405 (137.4415 + 7%)
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 147.062405);

        //independent great grandchild optimal bar = 157.35677335 (147.062405 + 7%)
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 157.35677335);


        LocalDate startDate = context.getStartDate();
        LocalDate endDate = context.getEndDate();
        AccomType baseAccomType = context.getBaseAccomType();
        context.setCpDecisionContext(
                createMockCpDecisionContext(
                        OccupancyType.SINGLE,
                        context.getProducts(),
                        Collections.emptyMap(),
                        createPricingRules(),
                        baseAccomType,
                        generateRangeOfDatesList(context.getStartDate(), context.getEndDate()),
                        0,
                        new HashMap<>(),
                        new HashMap<>(),
                        createProductRateOffsets(
                                baseAccomType.getAccomClass().getId(),
                                context.getOutputs(),
                                AgileRatesOffsetMethod.PERCENTAGE,
                                7.0),
                        false,
                        false,
                        false,
                        false,
                        false));

        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(context.getCpDecisionContext());
        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(context.getOutputs());

        //WHEN
        service.roundOptimalBARs(startDate, endDate);

        //THEN
        //assert BAR outputs
        CPDecisionBAROutput systemDefaultOutputStart = context.getOutputsByProductName(SYSTEM_DEFAULT).get(0);
        assertEquals(getDefaultPrecisionValueOf(123.45), systemDefaultOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(122.99), systemDefaultOutputStart.getFinalBAR());

        //assert second gen:
        //optimal bar: 123.45 + 7% (8.6415) = 132.0915
        //final bar: 122.99 + 7% (8.6093) = 132.00 (131.5993 rounded to nearest whole number)
        List<CPDecisionBAROutput> childOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        CPDecisionBAROutput childOneOfBarOutputStart = childOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(132.09150), childOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(132.00), childOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(9.01), childOneOfBarOutputStart.getAdjustmentValue());

        //assert third gen:
        //optimal bar: 132.09150 + 7% (9.246405) = 141.33791
        //final bar: 132.00 + 7% (9.24) = 141.00 (141.24 rounded to nearest whole number)
        List<CPDecisionBAROutput> grandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput grandchildOfChildOneOutputStart = grandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(141.33791), grandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(141.00), grandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(6.82), grandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert fourth gen:
        //optimal bar: 141.33791 + 7% (9.89365) = 151.23156
        //final bar: 141.00 + 7% (9.87) = 151.00 (150.87 rounded to nearest whole number)
        List<CPDecisionBAROutput> greatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput greatGrandChildOfGrandchildOneOutputStart = greatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(151.23156), greatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(151.00), greatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(10.00), greatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());

        //assert independent outputs
        CPDecisionBAROutput independentProductOutputStart = context.getOutputsByProductName(INDEPENDENT_PRODUCT).get(0);
        assertEquals(getDefaultPrecisionValueOf(128.45), independentProductOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(127.99), independentProductOutputStart.getFinalBAR());

        //assert independent second gen:
        //optimal bar: 128.45 + 7%  = 137.44150
        //final bar: 127.99 + 7%  = 137.00 (136.5993 rounded to nearest whole number)
        List<CPDecisionBAROutput> independentChildOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentChildOneOfBarOutputStart = independentChildOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(137.44150), independentChildOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(137.00), independentChildOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(9.01), independentChildOneOfBarOutputStart.getAdjustmentValue());

        //assert independent third gen:
        //optimal bar: 132.09150 + 7% = 141.33791
        //final bar: 132.00 + 7% = 141.00 (141.24 rounded to nearest whole number)
        List<CPDecisionBAROutput> independentGrandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput independentGrandchildOfChildOneOutputStart = independentGrandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(141.33791), independentGrandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(141.00), independentGrandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(6.82), independentGrandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert independent fourth gen:
        //optimal bar: 141.33791 + 7%  = 151.23156
        //final bar: 141.00 + 7%  = 151.00 (150.87 rounded to nearest whole number)
        List<CPDecisionBAROutput> independentGreatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput independentGreatGrandChildOfGrandchildOneOutputStart = independentGreatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(151.23156), independentGreatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(151.00), independentGreatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(10.00), independentGreatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());
    }

    @Test
    public void roundOptimalBarsProductFloor() {
        //GIVEN
        AgileRatesOptimalBarsServiceTestContext context = createBaseContext();

        //child one has a product floor of 120
        Product childOneProduct = context.getProductByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        childOneProduct.setFloor(precisionTwoValueOf(120));

        //child one has a product floor of 120
        Product greatGrandChild = context.getProductByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        greatGrandChild.setFloor(precisionTwoValueOf(105.25));

        //bar optimal bar = 123.45
        context.setOptimalBarValuesByProductName(SYSTEM_DEFAULT, 123.45);

        //child optimal bar = 114.8085 (123.45 - 7%)
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, 114.8085);

        //grandchild optimal bar = 106.77191 (114.8085 - 7%)
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, 106.77191);

        //great grandchild optimal bar = 99.29788 (106.77191 - 7%)
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, 99.29788);

        //independent optimal bar = 128.45
        context.setOptimalBarValuesByProductName(INDEPENDENT_PRODUCT, 128.45);

        //independent child optimal bar = 119.4585 (128.45 - 7%)
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, 119.4585);

        //independent grandchild optimal bar = 111.096405 (119.4585 - 7%)
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 111.096405);

        //independent great grandchild optimal bar = 103.31965665 (111.096405 - 7%)
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 103.31965665);


        LocalDate startDate = context.getStartDate();
        LocalDate endDate = context.getEndDate();
        AccomType baseAccomType = context.getBaseAccomType();
        context.setCpDecisionContext(
                createMockCpDecisionContext(
                        OccupancyType.SINGLE,
                        context.getProducts(),
                        Collections.emptyMap(),
                        createPricingRules(),
                        baseAccomType,
                        generateRangeOfDatesList(context.getStartDate(), context.getEndDate()),
                        0,
                        new HashMap<>(),
                        new HashMap<>(),
                        createProductRateOffsets(
                                baseAccomType.getAccomClass().getId(),
                                context.getOutputs(),
                                AgileRatesOffsetMethod.PERCENTAGE,
                                -7.0),
                        false
                        , false
                        , false
                        , false
                        , false));

        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(context.getCpDecisionContext());
        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(context.getOutputs());

        //WHEN
        service.roundOptimalBARs(startDate, endDate);

        //THEN
        //assert BAR outputs
        CPDecisionBAROutput systemDefaultOutputStart = context.getOutputsByProductName(SYSTEM_DEFAULT).get(0);
        assertEquals(getDefaultPrecisionValueOf(123.45), systemDefaultOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(122.99), systemDefaultOutputStart.getFinalBAR());

        //assert second gen:
        //optimal bar: 123.45 - 7% (8.6415) = 114.8085
        //final bar: 122.99 - 7% (8.6093) = 120.00 (113.99 original value is below floor)
        List<CPDecisionBAROutput> childOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        CPDecisionBAROutput childOneOfBarOutputStart = childOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(114.80850), childOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(120.00), childOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-2.99), childOneOfBarOutputStart.getAdjustmentValue());

        //assert third gen:
        //optimal bar: 114.80850 - 7% (8.036595) = 106.771905
        //final bar: 120.00 + 7% (8.4) = 111.99 (111.60 rounded)
        List<CPDecisionBAROutput> grandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput grandchildOfChildOneOutputStart = grandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(106.77191), grandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(111.99), grandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-6.68), grandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert fourth gen:
        //optimal bar: 106.77191 - 7%  = 99.29788
        //final bar: 111.99 - 7%  = 105.25 (104.1507 replace by floor)
        List<CPDecisionBAROutput> greatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput greatGrandChildOfGrandchildOneOutputStart = greatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(99.29788), greatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(105.25), greatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-6.74), greatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());

        //assert independent outputs
        CPDecisionBAROutput independentProductOutputStart = context.getOutputsByProductName(INDEPENDENT_PRODUCT).get(0);
        assertEquals(getDefaultPrecisionValueOf(128.45), independentProductOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(127.99), independentProductOutputStart.getFinalBAR());

        //assert independent second gen:
        //optimal bar: 128.45 - 7% = 119.45850
        //final bar: 122.99 - 7%  = 120.00 (113.99 original value is below floor)
        List<CPDecisionBAROutput> independentChildOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentChildOneOfBarOutputStart = independentChildOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(119.45850), independentChildOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(118.99), independentChildOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-9.00), independentChildOneOfBarOutputStart.getAdjustmentValue());

        //assert independent third gen:
        //optimal bar: 114.80850 - 7% = 106.771905
        //final bar: 120.00 + 7%  = 111.99 (111.60 rounded)
        List<CPDecisionBAROutput> independentGrandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGrandchildOfChildOneOutputStart = independentGrandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(111.09641), independentGrandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(110.99), independentGrandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-6.72), independentGrandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert independent fourth gen:
        //optimal bar: 111.09641 - 7%  = 103.31966
        //final bar: 110.99 - 7%  = 102.99 (103.2207 replace by floor)
        List<CPDecisionBAROutput> independentGreatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGreatGrandChildOfGrandchildOneOutputStart = independentGreatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(103.31966), independentGreatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(102.99), independentGreatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-8.00), independentGreatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());
    }

    @Test
    public void roundOptimalBars_MinimumPriceChangeUpdateNotRequired_NeitherBarNorAllDependentNonOptimizedProducts() {
        //GIVEN

        AgileRatesOptimalBarsServiceTestContext context = createBaseContext();

        //child one has a minimumPriceChange of 5
        Product grandchildOneProduct = context.getProductByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        grandchildOneProduct.setOptimized(true);
        grandchildOneProduct.setMinimumPriceChange(precisionTwoValueOf(1));

        //great grand child one has a minimumPriceChange of 500
        Product greatGrandChildOneProduct = context.getProductByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        greatGrandChildOneProduct.setOptimized(true);
        greatGrandChildOneProduct.setMinimumPriceChange(precisionTwoValueOf(500));

        //child one has a minimumPriceChange of 5
        Product independentGrandchildOneProduct = context.getProductByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        independentGrandchildOneProduct.setOptimized(true);
        independentGrandchildOneProduct.setMinimumPriceChange(precisionTwoValueOf(1));

        //great grand child one has a minimumPriceChange of 500
        Product independentGreatGrandChildOneProduct = context.getProductByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        independentGreatGrandChildOneProduct.setOptimized(true);
        independentGreatGrandChildOneProduct.setMinimumPriceChange(precisionTwoValueOf(500));

        //bar optimal bar = 123.45
        context.setOptimalBarValuesByProductName(SYSTEM_DEFAULT, 123.45);

        //child optimal bar = 114.8085 (123.45 - 7%)
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, 114.8085);

        //grandchild optimal bar = -7%
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, -7.0, OptimalBarType.PERCENT);

        //great grandchild optimal bar = -7%
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, -7.0, OptimalBarType.PERCENT);

        //independent optimal bar = 128.45
        context.setOptimalBarValuesByProductName(INDEPENDENT_PRODUCT, 128.45);

        //independent child optimal bar = 119.4585 (128.45 - 7%)
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, 119.4585);

        //independent grandchild optimal bar = 111.096405 (119.4585 - 7%)
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, -7.0, OptimalBarType.PERCENT);

        //independent great grandchild optimal bar = 103.31965665 (111.096405 - 7%)
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, -7.0, OptimalBarType.PERCENT);

        LocalDate startDate = context.getStartDate();
        LocalDate endDate = context.getEndDate();
        int minimumIncrementValueForBAR = 5;
        AccomType baseAccomType = context.getBaseAccomType();
        context.setCpDecisionContext(
                createMockCpDecisionContext(
                        OccupancyType.SINGLE,
                        context.getProducts(),
                        Collections.emptyMap(),
                        createPricingRules(),
                        baseAccomType,
                        generateRangeOfDatesList(context.getStartDate(), context.getEndDate()),
                        minimumIncrementValueForBAR,
                        new HashMap<>(),
                        new HashMap<>(),
                        createProductRateOffsets(
                                baseAccomType.getAccomClass().getId(),
                                context.getOutputs(),
                                AgileRatesOffsetMethod.PERCENTAGE,
                                -7.0),
                        false,
                        false,
                        false,
                        false,
                        false));

        List<DecisionDailybarOutput> previousDecisionDailybarOutputs = Arrays.asList(
                createDecisionDailyBarOutput(
                        context.getStartDate(),
                        context.getBaseAccomType(),
                        precisionTwoValueOf(123.99),
                        context.getProductByProductName(SYSTEM_DEFAULT)),
                createDecisionDailyBarOutput(
                        context.getStartDate(),
                        context.getBaseAccomType(),
                        precisionTwoValueOf(1.23),
                        context.getProductByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT)),
                createDecisionDailyBarOutput(
                        context.getStartDate(),
                        context.getBaseAccomType(),
                        precisionTwoValueOf(4.56),
                        context.getProductByProductName(GRANDCHILD_ONE_OF_CHILD_ONE)),
                createDecisionDailyBarOutput(
                        context.getStartDate(),
                        context.getBaseAccomType(),
                        precisionTwoValueOf(7.89),
                        context.getProductByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE)),
                createDecisionDailyBarOutput(
                        context.getStartDate(),
                        context.getBaseAccomType(),
                        precisionTwoValueOf(123.99),
                        context.getProductByProductName(INDEPENDENT_PRODUCT)),
                createDecisionDailyBarOutput(
                        context.getStartDate(),
                        context.getBaseAccomType(),
                        precisionTwoValueOf(1.23),
                        context.getProductByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT)),
                createDecisionDailyBarOutput(
                        context.getStartDate(),
                        context.getBaseAccomType(),
                        precisionTwoValueOf(4.56),
                        context.getProductByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE)),
                createDecisionDailyBarOutput(
                        context.getStartDate(),
                        context.getBaseAccomType(),
                        precisionTwoValueOf(7.89),
                        context.getProductByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE))
        );
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(context.getCpDecisionContext());
        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(context.getOutputs());
        when(crudService.<DecisionDailybarOutput>findByNamedQuery(DecisionDailybarOutput.FIND_BY_OCCUPANCY_DATE_BETWEEN, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters()))
                .thenReturn(previousDecisionDailybarOutputs);

        //WHEN
        service.roundOptimalBARs(startDate, endDate);

        //THEN
        //assert BAR outputs
        //final bar 123.99 (from 122.99 due to not exceeding minimum increment)
        CPDecisionBAROutput systemDefaultOutputStart = context.getOutputsByProductName(SYSTEM_DEFAULT).get(0);
        assertEquals(getDefaultPrecisionValueOf(123.45), systemDefaultOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(123.99), systemDefaultOutputStart.getFinalBAR());

        //assert second gen:
        //optimal bar: 123.45 - 7% = 114.8085
        //final bar: 1.23 (using previous daily bar output and not 113.99)
        List<CPDecisionBAROutput> childOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        CPDecisionBAROutput childOneOfBarOutputStart = childOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(114.8085), childOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(1.23), childOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-122.76), childOneOfBarOutputStart.getAdjustmentValue());

        //assert third gen:
        //optimal bar: -7 SAS derived offset
        //final bar: 0.99 (1.1439 (1.23 -7%) rounded)
        List<CPDecisionBAROutput> grandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput grandchildOfChildOneOutputStart = grandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-7), grandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(0.99), grandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-19.51), grandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert fourth gen:
        //optimal bar: -7 SAS derived offset
        //final bar: 7.89 (using previous daily bar output)
        List<CPDecisionBAROutput> greatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput greatGrandChildOfGrandchildOneOutputStart = greatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-7), greatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(7.89), greatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(6.90), greatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());

        //assert independent outputs
        //final bar 123.99 (from 128.99 due to not exceeding minimum increment)
        CPDecisionBAROutput independentProductOutputStart = context.getOutputsByProductName(INDEPENDENT_PRODUCT).get(0);
        assertEquals(getDefaultPrecisionValueOf(128.45), independentProductOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(123.99), independentProductOutputStart.getFinalBAR());

        //assert independent second gen:
        //optimal bar: 128.45 - 7% = 119.45850
        //final bar: 1.23 (using previous daily bar output and not 118.99)
        List<CPDecisionBAROutput> independentChildOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentChildOneOfBarOutputStart = independentChildOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(119.45850), independentChildOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(1.23), independentChildOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-122.76), independentChildOneOfBarOutputStart.getAdjustmentValue());

        //assert independent third gen:
        //optimal bar: -7 SAS derived offset
        //final bar: 0.99 (1.1439 (1.23 -7%) rounded)
        List<CPDecisionBAROutput> independentGrandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGrandchildOfChildOneOutputStart = independentGrandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-7), independentGrandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(0.99), independentGrandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-19.51), independentGrandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert independent fourth gen:
        //optimal bar: -7 SAS derived offset
        //final bar: 7.89 (using previous daily bar output)
        List<CPDecisionBAROutput> independentGreatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGreatGrandChildOfGrandchildOneOutputStart = independentGreatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-7), independentGreatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(7.89), independentGreatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(6.90), independentGreatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());
    }

    @Test
    public void roundOptimalBars_MinimumPriceChangeUpdateNotRequired_ForGrandChildOnly() {
        //GIVEN
        AgileRatesOptimalBarsServiceTestContext context = createBaseContext();

        //bar optimal bar = 123.45
        context.setOptimalBarValuesByProductName(SYSTEM_DEFAULT, 123.45);

        //child optimal bar = - 7%
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, -7.0, OptimalBarType.PERCENT);

        //grandchild optimal bar = 141.33791 (132.0915 + 7%)
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, -7.0, OptimalBarType.PERCENT);

        //great grandchild optimal bar = 151.23156 (141.33791 + 7%)
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, 99.29788);

        //independent optimal bar = 128.45
        context.setOptimalBarValuesByProductName(INDEPENDENT_PRODUCT, 128.45);

        //independent child optimal bar = - 7%
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, -7.0, OptimalBarType.PERCENT);

        //independent grandchild optimal bar = 141.33791 (132.0915 + 7%)
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, -7.0, OptimalBarType.PERCENT);

        //independent great grandchild optimal bar = 151.23156 (141.33791 + 7%)
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 99.29788);


        LocalDate startDate = context.getStartDate();
        LocalDate endDate = context.getEndDate();
        double barLevelMinimumIncrement = 0.5; //small value to trigger BAR update
        AccomType baseAccomType = context.getBaseAccomType();
        context.setCpDecisionContext(
                createMockCpDecisionContext(
                        OccupancyType.SINGLE,
                        context.getProducts(),
                        Collections.emptyMap(),
                        createPricingRules(),
                        baseAccomType,
                        generateRangeOfDatesList(context.getStartDate(), context.getEndDate()),
                        barLevelMinimumIncrement,
                        new HashMap<>(),
                        new HashMap<>(),
                        createProductRateOffsets(
                                baseAccomType.getAccomClass().getId(),
                                context.getOutputs(),
                                AgileRatesOffsetMethod.PERCENTAGE,
                                -7.0),
                        false,
                        false,
                        false,
                        false,
                        false));

        List<DecisionDailybarOutput> previousDecisionDailybarOutputs = Arrays.asList(
                createDecisionDailyBarOutput(
                        context.getStartDate(),
                        context.getBaseAccomType(),
                        precisionTwoValueOf(123.99),
                        context.getProductByProductName(SYSTEM_DEFAULT)),
                createDecisionDailyBarOutput(
                        context.getStartDate(),
                        context.getBaseAccomType(),
                        precisionTwoValueOf(115.99),
                        context.getProductByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT)),
                createDecisionDailyBarOutput(
                        context.getStartDate(),
                        context.getBaseAccomType(),
                        precisionTwoValueOf(1.23),
                        context.getProductByProductName(GRANDCHILD_ONE_OF_CHILD_ONE)),
                createDecisionDailyBarOutput(
                        context.getStartDate(),
                        context.getBaseAccomType(),
                        precisionTwoValueOf(4.56),
                        context.getProductByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE)),
                createDecisionDailyBarOutput(
                        context.getStartDate(),
                        context.getBaseAccomType(),
                        precisionTwoValueOf(128.99),
                        context.getProductByProductName(INDEPENDENT_PRODUCT)),
                createDecisionDailyBarOutput(
                        context.getStartDate(),
                        context.getBaseAccomType(),
                        precisionTwoValueOf(115.99),
                        context.getProductByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT)),
                createDecisionDailyBarOutput(
                        context.getStartDate(),
                        context.getBaseAccomType(),
                        precisionTwoValueOf(1.23),
                        context.getProductByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE)),
                createDecisionDailyBarOutput(
                        context.getStartDate(),
                        context.getBaseAccomType(),
                        precisionTwoValueOf(4.56),
                        context.getProductByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE))
        );

        //child one has a minimumPriceChange of 1 to trigger update
        Product childOneProduct = context.getProductByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        childOneProduct.setOptimized(true);
        childOneProduct.setMinimumPriceChange(precisionTwoValueOf(1));

        //grand child one has a minimumPriceChange of 500 to block update
        Product grandchildOneProduct = context.getProductByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        grandchildOneProduct.setOptimized(true);
        grandchildOneProduct.setMinimumPriceChange(precisionTwoValueOf(500));

        //independent child one has a minimumPriceChange of 1 to trigger update
        Product independentChildOneProduct = context.getProductByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        independentChildOneProduct.setOptimized(true);
        independentChildOneProduct.setMinimumPriceChange(precisionTwoValueOf(1));

        //independent grand child one has a minimumPriceChange of 500 to block update
        Product independentGrandchildOneProduct = context.getProductByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        independentGrandchildOneProduct.setOptimized(true);
        independentGrandchildOneProduct.setMinimumPriceChange(precisionTwoValueOf(500));

        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(context.getCpDecisionContext());
        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(context.getOutputs());
        when(crudService.<DecisionDailybarOutput>findByNamedQuery(DecisionDailybarOutput.FIND_BY_OCCUPANCY_DATE_BETWEEN, QueryParameter.with("startDate", startDate).and("endDate", endDate).parameters()))
                .thenReturn(previousDecisionDailybarOutputs);

        //WHEN
        service.roundOptimalBARs(startDate, endDate);

        //THEN
        //assert BAR outputs
        //final bar: 123.99 (from 122.99 due to not exceeding minimum increment)
        CPDecisionBAROutput systemDefaultOutputStart = context.getOutputsByProductName(SYSTEM_DEFAULT).get(0);
        assertEquals(getDefaultPrecisionValueOf(123.45), systemDefaultOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(122.99), systemDefaultOutputStart.getFinalBAR());

        //assert second gen:
        //minimumPriceChange of 1 to trigger update
        //optimal bar: -7 SAS derived offset
        //final bar: 122.99 - 7% (8.6093) = 113.99 (rounded from 114.3807 and exceeded minimum price increment of 1)
        List<CPDecisionBAROutput> childOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        CPDecisionBAROutput childOneOfBarOutputStart = childOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-7), childOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(113.99), childOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-9.00), childOneOfBarOutputStart.getAdjustmentValue());

        //assert third gen:
        //minimumPriceChange of 500 to block update
        //optimal bar: -7 SAS derived offset
        //final bar: 1.23 => did not meet minimum price increment of 5 (113.99 - 7% (7.9793) = 105.99 (round from 106.0107))
        List<CPDecisionBAROutput> grandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput grandchildOfChildOneOutputStart = grandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-7), grandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(1.23), grandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-98.92), grandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert fourth gen:
        //optimal bar: 106.77191 - 7% (7.4740337) = 99.29788
        //final bar: 4.56 (using previous daily bar output)
        List<CPDecisionBAROutput> greatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput greatGrandChildOfGrandchildOneOutputStart = greatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(99.29788), greatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(4.56), greatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(3.33), greatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());

        //assert independent outputs
        //final bar: 128.99 (from 127.99 due to not exceeding minimum increment)
        CPDecisionBAROutput independentProductOutputStart = context.getOutputsByProductName(INDEPENDENT_PRODUCT).get(0);
        assertEquals(getDefaultPrecisionValueOf(128.45), independentProductOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(127.99), independentProductOutputStart.getFinalBAR());

        //assert independent second gen:
        //minimumPriceChange of 1 to trigger update
        //optimal bar: -7 SAS derived offset
        //final bar: 127.99 - 7% = 118.99 (rounded from 119.3807 and exceeded minimum price increment of 1)
        List<CPDecisionBAROutput> independentChildOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentChildOneOfBarOutputStart = independentChildOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-7), independentChildOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(118.99), independentChildOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-9.00), independentChildOneOfBarOutputStart.getAdjustmentValue());

        //assert independent third gen:
        //minimumPriceChange of 500 to block update
        //optimal bar: -7 SAS derived offset
        //final bar: 1.23 => did not meet minimum price increment of 5 (113.99 - 7%  = 105.99 (round from 106.0107))
        List<CPDecisionBAROutput> independentGrandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGrandchildOfChildOneOutputStart = independentGrandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(-7), independentGrandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(1.23), independentGrandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-98.97), independentGrandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert independent fourth gen:
        //optimal bar: 106.77191 - 7% (7.4740337) = 99.29788
        //final bar: 4.56 (using previous daily bar output)
        List<CPDecisionBAROutput> independentGreatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGreatGrandChildOfGrandchildOneOutputStart = independentGreatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(99.29788), independentGreatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(4.56), independentGreatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(3.33), independentGreatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());
    }

    @Test
    public void roundOptimalBARsProductRateOffsetsFixedAtMinusTenOptimalBarTypeNullNoPackageWithHiltonSendAdjustmentEnabled() {
        final double PRODUCT_RATE_OFFSET_VALUE = -10;

        WorkContextType wc = new WorkContextType();
        wc.setUserId("1");
        wc.setPropertyId(1);
        wc.setClientCode("Hilton");
        wc.setClientId(2);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);

        AgileRatesOptimalBarsServiceTestContext context = new AgileRatesOptimalBarsServiceTestContext();
        LocalDate startDate = LocalDate.parse("2000-01-01");
        LocalDate endDate = LocalDate.parse("2000-01-01");
        context.setStartDate(startDate);
        context.setEndDate(endDate);
        List<LocalDate> rangeOfDates = generateRangeOfDatesList(startDate, endDate);

        AccomType baseAccomType = context.getBaseAccomType();
        List<CPDecisionBAROutput> outputs = createOutputs(context.getProducts(), rangeOfDates, baseAccomType.getAccomClass());
        context.setOutputs(outputs);

        Map<Product, List<Product>> products = context.getProducts();

        //set inputs from what analytics would set
        context.setOptimalBarValuesByProductName(SYSTEM_DEFAULT, 100.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, 90.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, 80.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, 70.00);

        context.setDecisionsSentByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, AgileRatesDecisionsSentBy.ADJUSTMENT);
        context.setDecisionsSentByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, AgileRatesDecisionsSentBy.ADJUSTMENT);
        context.setDecisionsSentByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, AgileRatesDecisionsSentBy.ADJUSTMENT);

        context.setOffsetMethodByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, AgileRatesOffsetMethod.FIXED);
        context.setOffsetMethodByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, AgileRatesOffsetMethod.FIXED);
        context.setOffsetMethodByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, AgileRatesOffsetMethod.FIXED);

        context.setOptimalBarValuesByProductName(INDEPENDENT_PRODUCT, 105.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, 95.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 85.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 75.00);

        context.setDecisionsSentByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, AgileRatesDecisionsSentBy.ADJUSTMENT);
        context.setDecisionsSentByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, AgileRatesDecisionsSentBy.ADJUSTMENT);
        context.setDecisionsSentByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, AgileRatesDecisionsSentBy.ADJUSTMENT);

        context.setOffsetMethodByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, AgileRatesOffsetMethod.FIXED);
        context.setOffsetMethodByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, AgileRatesOffsetMethod.FIXED);
        context.setOffsetMethodByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, AgileRatesOffsetMethod.FIXED);

        CPDecisionContext mockCpDecisionContext =
                createMockCpDecisionContext(
                        products,
                        baseAccomType,
                        rangeOfDates,
                        outputs,
                        PRODUCT_RATE_OFFSET_VALUE,
                        true,
                        true,
                        false,
                        false,
                        false);

        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(outputs);
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(mockCpDecisionContext);
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);

        service.roundOptimalBARs(startDate, endDate);

        //assert BAR outputs
        List<CPDecisionBAROutput> defaultOutputs = context.getOutputsByProductName(SYSTEM_DEFAULT);
        CPDecisionBAROutput systemDefaultOutputStart = defaultOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(100), systemDefaultOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), systemDefaultOutputStart.getFinalBAR());

        //assert first gen
        List<CPDecisionBAROutput> childOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        CPDecisionBAROutput childOneOfBarOutputStart = childOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(90), childOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(89.99), childOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-10), childOneOfBarOutputStart.getAdjustmentValue());

        //assert second gen
        List<CPDecisionBAROutput> grandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput grandchildOfChildOneOutputStart = grandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(80), grandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(79.99), grandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-20), grandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert third gen
        List<CPDecisionBAROutput> greatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput greatGrandChildOfGrandchildOneOutputStart = greatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(70), greatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(69.99), greatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-30), greatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());

        //assert independent outputs
        List<CPDecisionBAROutput> independentOutputs = context.getOutputsByProductName(INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentProductsOutputStart = independentOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(105), independentProductsOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(104.99), independentProductsOutputStart.getFinalBAR());

        //assert first gen
        List<CPDecisionBAROutput> independentChildOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentChildOneOfBarOutputStart = independentChildOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(95), independentChildOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(94.99), independentChildOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-10), independentChildOneOfBarOutputStart.getAdjustmentValue());

        //assert second gen
        List<CPDecisionBAROutput> independentGrandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGrandchildOfChildOneOutputStart = independentGrandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(85), independentGrandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(84.99), independentGrandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-20), independentGrandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert third gen
        List<CPDecisionBAROutput> independentGreatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGreatGrandChildOfGrandchildOneOutputStart = independentGreatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(75), independentGreatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(74.99), independentGreatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-30), independentGreatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());
    }

    @Test
    public void roundOptimalBARsProductRateOffsetsFixedAtPositiveTenOptimalBarTypeNullNoPackageWithHiltonSendAdjustmentEnabled() {
        final double PRODUCT_RATE_OFFSET_VALUE = 10;

        WorkContextType wc = new WorkContextType();
        wc.setUserId("1");
        wc.setPropertyId(1);
        wc.setClientCode("Hilton");
        wc.setClientId(2);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);

        AgileRatesOptimalBarsServiceTestContext context = new AgileRatesOptimalBarsServiceTestContext();
        LocalDate startDate = LocalDate.parse("2000-01-01");
        LocalDate endDate = LocalDate.parse("2000-01-01");
        context.setStartDate(startDate);
        context.setEndDate(endDate);
        List<LocalDate> rangeOfDates = generateRangeOfDatesList(startDate, endDate);

        AccomType baseAccomType = context.getBaseAccomType();
        List<CPDecisionBAROutput> outputs = createOutputs(context.getProducts(), rangeOfDates, baseAccomType.getAccomClass());
        context.setOutputs(outputs);

        Map<Product, List<Product>> products = context.getProducts();

        //set inputs from what analytics would set
        context.setOptimalBarValuesByProductName(SYSTEM_DEFAULT, 100.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, 110.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, 120.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, 130.00);

        context.setDecisionsSentByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, AgileRatesDecisionsSentBy.ADJUSTMENT);
        context.setDecisionsSentByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, AgileRatesDecisionsSentBy.ADJUSTMENT);
        context.setDecisionsSentByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, AgileRatesDecisionsSentBy.ADJUSTMENT);

        context.setOffsetMethodByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, AgileRatesOffsetMethod.FIXED);
        context.setOffsetMethodByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, AgileRatesOffsetMethod.FIXED);
        context.setOffsetMethodByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, AgileRatesOffsetMethod.FIXED);

        context.setOptimalBarValuesByProductName(INDEPENDENT_PRODUCT, 105.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, 115.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 125.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 135.00);

        context.setDecisionsSentByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, AgileRatesDecisionsSentBy.ADJUSTMENT);
        context.setDecisionsSentByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, AgileRatesDecisionsSentBy.ADJUSTMENT);
        context.setDecisionsSentByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, AgileRatesDecisionsSentBy.ADJUSTMENT);

        context.setOffsetMethodByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, AgileRatesOffsetMethod.FIXED);
        context.setOffsetMethodByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, AgileRatesOffsetMethod.FIXED);
        context.setOffsetMethodByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, AgileRatesOffsetMethod.FIXED);

        CPDecisionContext mockCpDecisionContext =
                createMockCpDecisionContext(
                        products,
                        baseAccomType,
                        rangeOfDates,
                        outputs,
                        PRODUCT_RATE_OFFSET_VALUE,
                        true,
                        true,
                        false,
                        false,
                        false);

        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(outputs);
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(mockCpDecisionContext);
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);

        service.roundOptimalBARs(startDate, endDate);

        //assert BAR outputs
        List<CPDecisionBAROutput> defaultOutputs = context.getOutputsByProductName(SYSTEM_DEFAULT);
        CPDecisionBAROutput systemDefaultOutputStart = defaultOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(100), systemDefaultOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), systemDefaultOutputStart.getFinalBAR());

        //assert first gen
        List<CPDecisionBAROutput> childOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        CPDecisionBAROutput childOneOfBarOutputStart = childOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(110), childOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(109.99), childOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(10), childOneOfBarOutputStart.getAdjustmentValue());

        //assert second gen
        List<CPDecisionBAROutput> grandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput grandchildOfChildOneOutputStart = grandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(120), grandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(119.99), grandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(20), grandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert third gen
        List<CPDecisionBAROutput> greatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput greatGrandChildOfGrandchildOneOutputStart = greatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(130), greatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(129.99), greatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(30), greatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());

        //assert independent outputs
        List<CPDecisionBAROutput> independentOutputs = context.getOutputsByProductName(INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentProductOutputStart = independentOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(105), independentProductOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(104.99), independentProductOutputStart.getFinalBAR());

        //assert independent first gen
        List<CPDecisionBAROutput> independentChildOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentChildOneOfBarOutputStart = independentChildOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(115), independentChildOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(114.99), independentChildOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(10), independentChildOneOfBarOutputStart.getAdjustmentValue());

        //assert independent second gen
        List<CPDecisionBAROutput> independentGrandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGrandchildOfChildOneOutputStart = independentGrandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(125), independentGrandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(124.99), independentGrandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(20), independentGrandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert independent third gen
        List<CPDecisionBAROutput> independentGreatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGreatGrandChildOfGrandchildOneOutputStart = independentGreatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(135), independentGreatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(134.99), independentGreatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(30), independentGreatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());
    }

    @Test
    public void roundOptimalBARsProductRateOffsetsPercentageAtPositiveTenOptimalBarTypeNullNoPackageWithHiltonSendAdjustmentEnabled() {
        final double PRODUCT_RATE_OFFSET_VALUE = 10;

        WorkContextType wc = new WorkContextType();
        wc.setUserId("1");
        wc.setPropertyId(1);
        wc.setClientCode("Hilton");
        wc.setClientId(2);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);

        AgileRatesOptimalBarsServiceTestContext context = new AgileRatesOptimalBarsServiceTestContext();
        LocalDate startDate = LocalDate.parse("2000-01-01");
        LocalDate endDate = LocalDate.parse("2000-01-01");
        context.setStartDate(startDate);
        context.setEndDate(endDate);
        List<LocalDate> rangeOfDates = generateRangeOfDatesList(startDate, endDate);

        AccomType baseAccomType = context.getBaseAccomType();
        List<CPDecisionBAROutput> outputs = createOutputs(context.getProducts(), rangeOfDates, baseAccomType.getAccomClass());
        context.setOutputs(outputs);

        Map<Product, List<Product>> products = context.getProducts();

        //set inputs from what analytics would set
        context.setOptimalBarValuesByProductName(SYSTEM_DEFAULT, 100.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, 110.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, 121.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, 133.10);

        context.setDecisionsSentByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, AgileRatesDecisionsSentBy.ADJUSTMENT);
        context.setDecisionsSentByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, AgileRatesDecisionsSentBy.ADJUSTMENT);
        context.setDecisionsSentByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, AgileRatesDecisionsSentBy.ADJUSTMENT);

        context.setOffsetMethodByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, AgileRatesOffsetMethod.FIXED);
        context.setOffsetMethodByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, AgileRatesOffsetMethod.FIXED);
        context.setOffsetMethodByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, AgileRatesOffsetMethod.FIXED);

        context.setOptimalBarValuesByProductName(INDEPENDENT_PRODUCT, 105.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, 115.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 126.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 138.10);

        context.setDecisionsSentByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, AgileRatesDecisionsSentBy.ADJUSTMENT);
        context.setDecisionsSentByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, AgileRatesDecisionsSentBy.ADJUSTMENT);
        context.setDecisionsSentByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, AgileRatesDecisionsSentBy.ADJUSTMENT);

        context.setOffsetMethodByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, AgileRatesOffsetMethod.FIXED);
        context.setOffsetMethodByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, AgileRatesOffsetMethod.FIXED);
        context.setOffsetMethodByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, AgileRatesOffsetMethod.FIXED);

        CPDecisionContext mockCpDecisionContext =
                createMockCpDecisionContext(
                        OccupancyType.SINGLE,
                        context.getProducts(),
                        Collections.emptyMap(),
                        createPricingRules(),
                        baseAccomType,
                        generateRangeOfDatesList(context.getStartDate(), context.getEndDate()),
                        0,
                        new HashMap<>(),
                        new HashMap<>(),
                        createProductRateOffsets(
                                baseAccomType.getAccomClass().getId(),
                                context.getOutputs(),
                                AgileRatesOffsetMethod.PERCENTAGE,
                                PRODUCT_RATE_OFFSET_VALUE),
                        true,
                        true,
                        false,
                        false,
                        false);

        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(outputs);
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(mockCpDecisionContext);
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);

        service.roundOptimalBARs(startDate, endDate);

        //assert BAR outputs
        List<CPDecisionBAROutput> defaultOutputs = context.getOutputsByProductName(SYSTEM_DEFAULT);
        CPDecisionBAROutput systemDefaultOutputStart = defaultOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(100), systemDefaultOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), systemDefaultOutputStart.getFinalBAR());

        //assert first gen
        List<CPDecisionBAROutput> childOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        CPDecisionBAROutput childOneOfBarOutputStart = childOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(110), childOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(109.99), childOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(10), childOneOfBarOutputStart.getAdjustmentValue());

        //assert second gen
        List<CPDecisionBAROutput> grandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput grandchildOfChildOneOutputStart = grandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(121), grandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(120.99), grandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(21), grandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert third gen
        List<CPDecisionBAROutput> greatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput greatGrandChildOfGrandchildOneOutputStart = greatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(133.1), greatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(132.99), greatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(33), greatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());

        //assert independent outputs
        List<CPDecisionBAROutput> independentOutputs = context.getOutputsByProductName(INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentProductOutputStart = independentOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(105), independentProductOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(104.99), independentProductOutputStart.getFinalBAR());

        //assert independent first gen
        List<CPDecisionBAROutput> independentChildOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentChildOneOfBarOutputStart = independentChildOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(115), independentChildOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(115.99), independentChildOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(11), independentChildOneOfBarOutputStart.getAdjustmentValue());

        //assert independent second gen
        List<CPDecisionBAROutput> independentGrandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGrandchildOfChildOneOutputStart = independentGrandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(126), independentGrandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(127.99), independentGrandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(23), independentGrandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert independent third gen
        List<CPDecisionBAROutput> independentGreatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGreatGrandChildOfGrandchildOneOutputStart = independentGreatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(138.1), independentGreatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(140.99), independentGreatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(36), independentGreatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());
    }

    @Test
    public void roundOptimalBARsProductRateOffsetsPercentageAtMinusTenOptimalBarTypeNullNoPackageWithHiltonSendAdjustmentEnabled() {
        final double PRODUCT_RATE_OFFSET_VALUE = -10;

        WorkContextType wc = new WorkContextType();
        wc.setUserId("1");
        wc.setPropertyId(1);
        wc.setClientCode("Hilton");
        wc.setClientId(2);
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);

        AgileRatesOptimalBarsServiceTestContext context = new AgileRatesOptimalBarsServiceTestContext();
        LocalDate startDate = LocalDate.parse("2000-01-01");
        LocalDate endDate = LocalDate.parse("2000-01-01");
        context.setStartDate(startDate);
        context.setEndDate(endDate);
        List<LocalDate> rangeOfDates = generateRangeOfDatesList(startDate, endDate);

        AccomType baseAccomType = context.getBaseAccomType();
        List<CPDecisionBAROutput> outputs = createOutputs(context.getProducts(), rangeOfDates, baseAccomType.getAccomClass());
        context.setOutputs(outputs);

        Map<Product, List<Product>> products = context.getProducts();

        //set inputs from what analytics would set
        context.setOptimalBarValuesByProductName(SYSTEM_DEFAULT, 100.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, 90.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, 81.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, 72.90);

        context.setDecisionsSentByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, AgileRatesDecisionsSentBy.ADJUSTMENT);
        context.setDecisionsSentByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, AgileRatesDecisionsSentBy.ADJUSTMENT);
        context.setDecisionsSentByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, AgileRatesDecisionsSentBy.ADJUSTMENT);

        context.setOffsetMethodByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT, AgileRatesOffsetMethod.FIXED);
        context.setOffsetMethodByProductName(GRANDCHILD_ONE_OF_CHILD_ONE, AgileRatesOffsetMethod.FIXED);
        context.setOffsetMethodByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE, AgileRatesOffsetMethod.FIXED);

        context.setOptimalBarValuesByProductName(INDEPENDENT_PRODUCT, 105.00);
        context.setOptimalBarValuesByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, 95.00);
        context.setOptimalBarValuesByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 86.00);
        context.setOptimalBarValuesByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, 77.90);

        context.setDecisionsSentByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, AgileRatesDecisionsSentBy.ADJUSTMENT);
        context.setDecisionsSentByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, AgileRatesDecisionsSentBy.ADJUSTMENT);
        context.setDecisionsSentByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, AgileRatesDecisionsSentBy.ADJUSTMENT);

        context.setOffsetMethodByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT, AgileRatesOffsetMethod.FIXED);
        context.setOffsetMethodByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, AgileRatesOffsetMethod.FIXED);
        context.setOffsetMethodByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE, AgileRatesOffsetMethod.FIXED);

        CPDecisionContext mockCpDecisionContext =
                createMockCpDecisionContext(
                        OccupancyType.SINGLE,
                        context.getProducts(),
                        Collections.emptyMap(),
                        createPricingRules(),
                        baseAccomType,
                        generateRangeOfDatesList(context.getStartDate(), context.getEndDate()),
                        0,
                        new HashMap<>(),
                        new HashMap<>(),
                        createProductRateOffsets(
                                baseAccomType.getAccomClass().getId(),
                                context.getOutputs(),
                                AgileRatesOffsetMethod.PERCENTAGE,
                                PRODUCT_RATE_OFFSET_VALUE),
                        true,
                        true,
                        false,
                        false,
                        false);

        when(cpManagementService.findCPDecisionsBetweenDatesWithCapacity(startDate, endDate)).thenReturn(outputs);
        when(pricingConfigurationService.getCPDecisionContext(startDate, endDate)).thenReturn(mockCpDecisionContext);
        when(configParamsService.getParameterValue(FeatureTogglesConfigParamName.PER_PERSON_PRICING_ENABLED)).thenReturn(false);

        service.roundOptimalBARs(startDate, endDate);

        //assert BAR outputs
        List<CPDecisionBAROutput> defaultOutputs = context.getOutputsByProductName(SYSTEM_DEFAULT);
        CPDecisionBAROutput systemDefaultOutputStart = defaultOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(100), systemDefaultOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(99.99), systemDefaultOutputStart.getFinalBAR());

        //assert first gen
        List<CPDecisionBAROutput> childOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_SYSTEM_DEFAULT);
        CPDecisionBAROutput childOneOfBarOutputStart = childOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(90), childOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(89.99), childOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-10), childOneOfBarOutputStart.getAdjustmentValue());

        //assert second gen
        List<CPDecisionBAROutput> grandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput grandchildOfChildOneOutputStart = grandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(81), grandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(80.99), grandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-19), grandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert third gen
        List<CPDecisionBAROutput> greatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_CHILD_ONE);
        CPDecisionBAROutput greatGrandChildOfGrandchildOneOutputStart = greatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(72.9), greatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(72.99), greatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-27), greatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());

        //assert independent outputs
        List<CPDecisionBAROutput> independentOutputs = context.getOutputsByProductName(INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentProductOutputStart = independentOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(105), independentProductOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(104.99), independentProductOutputStart.getFinalBAR());

        //assert independent first gen
        List<CPDecisionBAROutput> independentChildOneOfBarOutputs = context.getOutputsByProductName(CHILD_ONE_OF_INDEPENDENT_PRODUCT);
        CPDecisionBAROutput independentChildOneOfBarOutputStart = independentChildOneOfBarOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(95), independentChildOneOfBarOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(94.99), independentChildOneOfBarOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-10), independentChildOneOfBarOutputStart.getAdjustmentValue());

        //assert independent second gen
        List<CPDecisionBAROutput> independentGrandchildOneOfChildOneOutputs = context.getOutputsByProductName(GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGrandchildOfChildOneOutputStart = independentGrandchildOneOfChildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(86), independentGrandchildOfChildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(85.99), independentGrandchildOfChildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-19), independentGrandchildOfChildOneOutputStart.getAdjustmentValue());

        //assert independent third gen
        List<CPDecisionBAROutput> independentGreatGrandChildOfGrandchildOneOutputs = context.getOutputsByProductName(GREAT_GRANDCHILD_OF_GRANDCHILD_ONE_OF_INDEPENDENT_CHILD_ONE);
        CPDecisionBAROutput independentGreatGrandChildOfGrandchildOneOutputStart = independentGreatGrandChildOfGrandchildOneOutputs.get(0);
        assertEquals(getDefaultPrecisionValueOf(77.9), independentGreatGrandChildOfGrandchildOneOutputStart.getOptimalBAR());
        assertEquals(precisionTwoValueOf(76.99), independentGreatGrandChildOfGrandchildOneOutputStart.getFinalBAR());
        assertEquals(precisionTwoValueOf(-28), independentGreatGrandChildOfGrandchildOneOutputStart.getAdjustmentValue());
    }

    private DecisionDailybarOutput createDecisionDailyBarOutput(LocalDate occupancyDate, AccomType accomType, BigDecimal rate, Product product) {
        DecisionDailybarOutput output = new DecisionDailybarOutput();
        output.setOccupancyDate(occupancyDate);
        output.setAccomType(accomType);
        output.setSingleRate(rate);
        output.setDoubleRate(rate);
        output.setProduct(product);
        return output;
    }

    private AgileRatesOptimalBarsServiceTestContext getContextAllProductsIsOptimalAndFixedAndWithPackage() {
        final double PRODUCT_RATE_OFFSET_VALUE = -10;

        AgileRatesOptimalBarsServiceTestContext context = createBaseContext();

        List<CPDecisionBAROutput> outputs = context.getOutputs();
        //set inputs from what analytics would set
        List<CPDecisionBAROutput> systemDefaultOutputs = context.getOutputsByProductName(SYSTEM_DEFAULT);
        systemDefaultOutputs.forEach(output -> output.setOptimalBarType(OptimalBarType.PRICE));

        Map<Product, List<Product>> products = context.getProducts();
        //set all products to have adult breakfast package
        Map<Product, List<AgileRatesPackage>> productPackagesWhereAdultBreakfastForAllProducts =
                createProductPackagesWhereAdultBreakfastForAllProducts(products);

        AccomType baseAccomType = context.getBaseAccomType();
        context.setCpDecisionContext(
                createMockCpDecisionContext(
                        OccupancyType.SINGLE,
                        products,
                        productPackagesWhereAdultBreakfastForAllProducts,
                        createPricingRules(),
                        baseAccomType,
                        generateRangeOfDatesList(context.getStartDate(), context.getEndDate()),
                        0,
                        new HashMap<>(),
                        new HashMap<>(),
                        createProductRateOffsets(
                                baseAccomType.getAccomClass().getId(),
                                outputs,
                                AgileRatesOffsetMethod.FIXED,
                                PRODUCT_RATE_OFFSET_VALUE),
                        false,
                        false,
                        false,
                        false,
                        false));
        return context;
    }

    private AgileRatesOptimalBarsServiceTestContext getContextAllProductsIsOptimalAndFixedAndWithPackageChargeType() {
        final double PRODUCT_RATE_OFFSET_VALUE = -10;

        AgileRatesOptimalBarsServiceTestContext context = createBaseContext();

        List<CPDecisionBAROutput> outputs = context.getOutputs();
        //set inputs from what analytics would set
        List<CPDecisionBAROutput> systemDefaultOutputs = context.getOutputsByProductName(SYSTEM_DEFAULT);
        systemDefaultOutputs.forEach(output -> output.setOptimalBarType(OptimalBarType.PRICE));

        Map<Product, List<Product>> products = context.getProducts();
        //set all products to have provided occupancyType breakfast package
        Map<Product, List<AgileRatesPackage>> productPackages =
                createProductPackagesWithChargeTypeDetail(products, AgileRatesChargeType.PER_ADULT, false);

        AccomType baseAccomType = context.getBaseAccomType();
        context.setCpDecisionContext(
                createMockCpDecisionContext(
                        OccupancyType.SINGLE,
                        products,
                        productPackages,
                        createPricingRules(),
                        baseAccomType,
                        generateRangeOfDatesList(context.getStartDate(), context.getEndDate()),
                        0,
                        new HashMap<>(),
                        new HashMap<>(),
                        createProductRateOffsets(
                                baseAccomType.getAccomClass().getId(),
                                outputs,
                                AgileRatesOffsetMethod.FIXED,
                                PRODUCT_RATE_OFFSET_VALUE),
                        false,
                        false,
                        false,
                        false,
                        true));
        return context;
    }

    private AgileRatesOptimalBarsServiceTestContext getContextNonOptimalProductsAllFixedAndWithPackage() {
        final double PRODUCT_RATE_OFFSET_VALUE = -10;

        AgileRatesOptimalBarsServiceTestContext context = createBaseContext();

        List<CPDecisionBAROutput> outputs = context.getOutputs();

        List<CPDecisionBAROutput> systemDefaultOutputs = context.getOutputsByProductName(SYSTEM_DEFAULT);
        systemDefaultOutputs.forEach(output -> output.setOptimalBarType(OptimalBarType.PRICE));

        Map<Product, List<Product>> products = context.getProducts();
        //set all products to have adult breakfast package
        Map<Product, List<AgileRatesPackage>> productPackagesWhereAdultBreakfastForAllProducts =
                createProductPackagesWhereAdultBreakfastForAllProducts(products);

        AccomType baseAccomType = context.getBaseAccomType();
        context.setCpDecisionContext(
                createMockCpDecisionContext(
                        OccupancyType.SINGLE,
                        products,
                        productPackagesWhereAdultBreakfastForAllProducts,
                        createPricingRules(),
                        baseAccomType,
                        generateRangeOfDatesList(context.getStartDate(), context.getEndDate()),
                        0,
                        new HashMap<>(),
                        new HashMap<>(),
                        createProductRateOffsets(
                                baseAccomType.getAccomClass().getId(),
                                outputs,
                                AgileRatesOffsetMethod.FIXED,
                                PRODUCT_RATE_OFFSET_VALUE),
                        false,
                        false,
                        false,
                        false,
                        false));
        return context;
    }

    private AgileRatesOptimalBarsServiceTestContext getContextNonOptimalProductsAllFixedAndWithPackageChargeType(OccupancyType occupancyType, AgileRatesChargeType agileRatesChargeType) {
        return this.getContextNonOptimalProductsAllFixedAndWithPackageChargeType(occupancyType, agileRatesChargeType, false);
    }


    private AgileRatesOptimalBarsServiceTestContext getContextNonOptimalProductsAllFixedAndWithPackageChargeType(OccupancyType occupancyType, AgileRatesChargeType agileRatesChargeType, boolean isSeason) {
        final double PRODUCT_RATE_OFFSET_VALUE = -10;

        AgileRatesOptimalBarsServiceTestContext context = createBaseContext();

        List<CPDecisionBAROutput> outputs = context.getOutputs();

        List<CPDecisionBAROutput> systemDefaultOutputs = context.getOutputsByProductName(SYSTEM_DEFAULT);
        systemDefaultOutputs.forEach(output -> output.setOptimalBarType(OptimalBarType.PRICE));

        Map<Product, List<Product>> products = context.getProducts();
        //set all products to have provided occupancyType breakfast package
        Map<Product, List<AgileRatesPackage>> productPackages =
                createProductPackagesWithChargeTypeDetail(products, agileRatesChargeType, isSeason);

        AccomType baseAccomType = context.getBaseAccomType();
        context.setCpDecisionContext(
                createMockCpDecisionContext(
                        occupancyType,
                        products,
                        productPackages,
                        createPricingRules(),
                        baseAccomType,
                        generateRangeOfDatesList(context.getStartDate(), context.getEndDate()),
                        0,
                        new HashMap<>(),
                        new HashMap<>(),
                        createProductRateOffsets(
                                baseAccomType.getAccomClass().getId(),
                                outputs,
                                AgileRatesOffsetMethod.FIXED,
                                PRODUCT_RATE_OFFSET_VALUE),
                        false,
                        false,
                        false,
                        false,
                        true));
        return context;
    }

    private AgileRatesOptimalBarsServiceTestContext getContextWithPackage(
            AgileRatesOffsetMethod offsetMethod,
            double offsetValue) {

        AgileRatesOptimalBarsServiceTestContext context = createBaseContext();

        List<CPDecisionBAROutput> outputs = context.getOutputs();

        Map<Product, List<Product>> products = context.getProducts();
        //set all products to have adult breakfast package
        Map<Product, List<AgileRatesPackage>> productPackagesWhereAdultBreakfastForAllProducts =
                createProductPackagesWhereAdultBreakfastForAllProducts(products);

        AccomType baseAccomType = context.getBaseAccomType();
        context.setCpDecisionContext(
                createMockCpDecisionContext(
                        OccupancyType.SINGLE,
                        products,
                        productPackagesWhereAdultBreakfastForAllProducts,
                        createPricingRules(),
                        baseAccomType,
                        generateRangeOfDatesList(context.getStartDate(), context.getEndDate()),
                        0,
                        new HashMap<>(),
                        new HashMap<>(),
                        createProductRateOffsets(
                                baseAccomType.getAccomClass().getId(),
                                outputs,
                                offsetMethod,
                                offsetValue),
                        false,
                        false,
                        false,
                        false,
                        false));
        return context;
    }

    private AgileRatesOptimalBarsServiceTestContext getContextWithPackageChargeType(
            AgileRatesOffsetMethod offsetMethod,
            double offsetValue) {

        AgileRatesOptimalBarsServiceTestContext context = createBaseContext();

        List<CPDecisionBAROutput> outputs = context.getOutputs();

        Map<Product, List<Product>> products = context.getProducts();
        Map<Product, List<AgileRatesPackage>> productPackages =
                createProductPackagesWithChargeTypeDetail(products, AgileRatesChargeType.PER_ADULT, false);

        AccomType baseAccomType = context.getBaseAccomType();
        context.setCpDecisionContext(
                createMockCpDecisionContext(
                        OccupancyType.SINGLE,
                        products,
                        productPackages,
                        createPricingRules(),
                        baseAccomType,
                        generateRangeOfDatesList(context.getStartDate(), context.getEndDate()),
                        0,
                        new HashMap<>(),
                        new HashMap<>(),
                        createProductRateOffsets(
                                baseAccomType.getAccomClass().getId(),
                                outputs,
                                offsetMethod,
                                offsetValue),
                        false,
                        false,
                        false,
                        false,
                        true));
        return context;
    }

    private AgileRatesOptimalBarsServiceTestContext createBaseContext() {
        AgileRatesOptimalBarsServiceTestContext context = new AgileRatesOptimalBarsServiceTestContext();
        LocalDate startDate = LocalDate.parse("2000-01-01");
        LocalDate endDate = LocalDate.parse("2000-01-01");
        context.setStartDate(startDate);
        context.setEndDate(endDate);

        Map<Product, List<Product>> products = context.getProducts();
        List<LocalDate> rangeOfDates = generateRangeOfDatesList(startDate, endDate);
        List<CPDecisionBAROutput> outputs = createOutputs(products, rangeOfDates, context.getBaseAccomType().getAccomClass());
        context.setOutputs(outputs);
        return context;
    }
}
