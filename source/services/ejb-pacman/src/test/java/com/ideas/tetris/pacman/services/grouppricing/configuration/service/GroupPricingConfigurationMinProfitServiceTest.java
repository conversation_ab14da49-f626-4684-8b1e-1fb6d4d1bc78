package com.ideas.tetris.pacman.services.grouppricing.configuration.service;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.configautomation.dto.MinimumProfitPercent;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigurationMinProfit;
import com.ideas.tetris.pacman.services.grouppricing.configuration.entity.GroupPricingConfigurationObjectMother;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluation;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationArrivalDate;
import com.ideas.tetris.pacman.services.grouppricing.evaluation.entity.GroupEvaluationObjectMother;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.math.RoundingMode.HALF_UP;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class GroupPricingConfigurationMinProfitServiceTest extends AbstractG3JupiterTest {
    @Mock
    private CrudService tenantCrudService;

    @Mock
    private DateService dateService;

    @InjectMocks
    private GroupPricingConfigurationMinProfitService service;

    @Test
    public void find() throws Exception {
        GroupPricingConfigurationMinProfit minProfit = GroupPricingConfigurationObjectMother.buildGroupPricingConfigurationMinProfitSeason();

        when(tenantCrudService.findAll(GroupPricingConfigurationMinProfit.class)).thenReturn(Collections.singletonList(minProfit));

        List<GroupPricingConfigurationMinProfit> retMinProfits = service.find();

        assertNotNull(retMinProfits);
        assertTrue(retMinProfits.size() == 1);
        verify(tenantCrudService).findAll(GroupPricingConfigurationMinProfit.class);
    }

    @Test
    public void save() throws Exception {
        GroupPricingConfigurationMinProfit minProfit1 = GroupPricingConfigurationObjectMother.buildGroupPricingConfigurationMinProfitSeason();
        GroupPricingConfigurationMinProfit minProfit2 = GroupPricingConfigurationObjectMother.buildGroupPricingConfigurationMinProfitSeason();

        List<GroupPricingConfigurationMinProfit> minProfits = new ArrayList<>();
        minProfits.add(minProfit1);
        minProfits.add(minProfit2);

        when(tenantCrudService.save(minProfits)).thenReturn(minProfits);

        service.save(minProfits);

        verify(tenantCrudService, times(1)).deleteAll(GroupPricingConfigurationMinProfit.class);
        verify(tenantCrudService, times(1)).save(minProfits);
    }

    @Test
    public void deleteAll() throws Exception {
        service.deleteAll();
        verify(tenantCrudService, times(1)).deleteAll(GroupPricingConfigurationMinProfit.class);
    }

    @Test
    public void getConfiguredMinProfitPcts() throws Exception {
        service.setTenantCrudService(tenantCrudService());
        setWorkContextProperty(TestProperty.H2);
        GroupPricingConfigurationMinProfit minProfitDefaults = GroupPricingConfigurationObjectMother.buildGroupPricingConfigurationMinProfitDefault();
        minProfitDefaults.setPropertyId(TestProperty.H2.getId());
        tenantCrudService().save(minProfitDefaults);
        tenantCrudService().flushAndClear();
        GroupPricingConfigurationMinProfit minProfitSeasons = GroupPricingConfigurationObjectMother.buildGroupPricingConfigurationMinProfitSeason();
        minProfitSeasons.setPropertyId(TestProperty.H2.getId());
        tenantCrudService().save(minProfitSeasons);
        tenantCrudService().flushAndClear();

        LocalDate arrivalDate = minProfitSeasons.getSeasonEndDate();
        LocalDate endDate = arrivalDate.plusDays(3);
        Integer daysToArrival = 25;

        Map<LocalDate, BigDecimal> retValues = service.getConfiguredMinProfitPcts(arrivalDate, endDate, daysToArrival);

        assertNotNull(retValues);
        assertEquals(4, retValues.size());
        // Assert that the last day of season percent is used
        assertEquals(minProfitSeasons.getWednesdayPct().setScale(2, HALF_UP), retValues.get(arrivalDate));
        // Assert that remaining days use default percents
        assertEquals(minProfitDefaults.getThursdayPct().setScale(2, HALF_UP), retValues.get(arrivalDate.plusDays(1)));
        assertEquals(minProfitDefaults.getFridayPct().setScale(2, HALF_UP), retValues.get(arrivalDate.plusDays(2)));
        assertEquals(minProfitDefaults.getSaturdayPct().setScale(2, HALF_UP), retValues.get(arrivalDate.plusDays(3)));
    }

    @Test
    public void calculateMinProfitPercentThreshold() throws Exception {
        GroupPricingConfigurationMinProfitService spy = spy(service);
        Integer daysToArrival = 30;

        GroupEvaluation groupEvaluation = GroupEvaluationObjectMother.buildGroupEvaluation();
        GroupEvaluationArrivalDate arrivalDate = groupEvaluation.getGroupEvaluationArrivalDates().iterator().next();

        Map<LocalDate, BigDecimal> dailyPcts = buildDailyPercents(arrivalDate.getArrivalDate(), arrivalDate.getLastDayOfStay());

        doReturn(dailyPcts).when(spy).getConfiguredMinProfitPcts(arrivalDate.getArrivalDate(), arrivalDate.getLastDayOfStay(), daysToArrival);
        doReturn(daysToArrival).when(spy).getNumberOfDaysToArrival(arrivalDate.getArrivalDate());

        BigDecimal retPctThreshold = spy.calculateMinProfitPercentThreshold(arrivalDate);

        assertEquals(new BigDecimal(20.00).setScale(2, HALF_UP), retPctThreshold);
        verify(spy).getConfiguredMinProfitPcts(arrivalDate.getArrivalDate(), arrivalDate.getLastDayOfStay(), daysToArrival);
        verify(spy).getNumberOfDaysToArrival(arrivalDate.getArrivalDate());
    }

    @Test
    public void calculateMinProfitPercentThreshold_noValueForOneDay() throws Exception {
        GroupPricingConfigurationMinProfitService spy = spy(service);
        Integer daysToArrival = 30;

        GroupEvaluation groupEvaluation = GroupEvaluationObjectMother.buildGroupEvaluation();
        GroupEvaluationArrivalDate arrivalDate = groupEvaluation.getGroupEvaluationArrivalDates().iterator().next();

        Map<LocalDate, BigDecimal> dailyPcts = buildDailyPercents(arrivalDate.getArrivalDate(), arrivalDate.getLastDayOfStay());
        dailyPcts.put(arrivalDate.getArrivalDate().plusDays(1), null);

        doReturn(dailyPcts).when(spy).getConfiguredMinProfitPcts(arrivalDate.getArrivalDate(), arrivalDate.getLastDayOfStay(), daysToArrival);
        doReturn(daysToArrival).when(spy).getNumberOfDaysToArrival(arrivalDate.getArrivalDate());

        BigDecimal retPctThreshold = spy.calculateMinProfitPercentThreshold(arrivalDate);

        assertEquals(new BigDecimal(13.33).setScale(2, HALF_UP), retPctThreshold);
        verify(spy).getConfiguredMinProfitPcts(arrivalDate.getArrivalDate(), arrivalDate.getLastDayOfStay(), daysToArrival);
        verify(spy).getNumberOfDaysToArrival(arrivalDate.getArrivalDate());
    }

    @Test
    public void calculateMinProfitPercentThreshold_onlyOneDayHasAPercentValue() throws Exception {
        GroupPricingConfigurationMinProfitService spy = spy(service);
        Integer daysToArrival = 30;

        GroupEvaluation groupEvaluation = GroupEvaluationObjectMother.buildGroupEvaluation();
        GroupEvaluationArrivalDate arrivalDate = groupEvaluation.getGroupEvaluationArrivalDates().iterator().next();

        Map<LocalDate, BigDecimal> dailyPcts = buildDailyPercents(arrivalDate.getArrivalDate(), arrivalDate.getLastDayOfStay());
        dailyPcts.put(arrivalDate.getArrivalDate(), null);
        dailyPcts.put(arrivalDate.getArrivalDate().plusDays(1), null);

        doReturn(dailyPcts).when(spy).getConfiguredMinProfitPcts(arrivalDate.getArrivalDate(), arrivalDate.getLastDayOfStay(), daysToArrival);
        doReturn(daysToArrival).when(spy).getNumberOfDaysToArrival(arrivalDate.getArrivalDate());

        BigDecimal retPctThreshold = spy.calculateMinProfitPercentThreshold(arrivalDate);

        assertEquals(new BigDecimal(6.67).setScale(2, HALF_UP), retPctThreshold);
        verify(spy).getConfiguredMinProfitPcts(arrivalDate.getArrivalDate(), arrivalDate.getLastDayOfStay(), daysToArrival);
        verify(spy).getNumberOfDaysToArrival(arrivalDate.getArrivalDate());
    }

    @Test
    public void calculateMinProfitPercentThreshold_noDailyMinProfitPcts() throws Exception {
        GroupPricingConfigurationMinProfitService spy = spy(service);
        Integer daysToArrival = 30;

        GroupEvaluation groupEvaluation = GroupEvaluationObjectMother.buildGroupEvaluation();
        GroupEvaluationArrivalDate arrivalDate = groupEvaluation.getGroupEvaluationArrivalDates().iterator().next();

        Map<LocalDate, BigDecimal> dailyPcts = buildDailyPercents(arrivalDate.getArrivalDate(), arrivalDate.getLastDayOfStay());
        dailyPcts.put(arrivalDate.getArrivalDate(), null);
        dailyPcts.put(arrivalDate.getArrivalDate().plusDays(1), null);
        dailyPcts.put(arrivalDate.getArrivalDate().plusDays(2), null);

        doReturn(dailyPcts).when(spy).getConfiguredMinProfitPcts(arrivalDate.getArrivalDate(), arrivalDate.getLastDayOfStay(), daysToArrival);
        doReturn(daysToArrival).when(spy).getNumberOfDaysToArrival(arrivalDate.getArrivalDate());

        BigDecimal retPctThreshold = spy.calculateMinProfitPercentThreshold(arrivalDate);

        assertEquals(new BigDecimal(0.00).setScale(2, HALF_UP), retPctThreshold);
        verify(spy).getConfiguredMinProfitPcts(arrivalDate.getArrivalDate(), arrivalDate.getLastDayOfStay(), daysToArrival);
        verify(spy).getNumberOfDaysToArrival(arrivalDate.getArrivalDate());
    }

    @Test
    public void calculateMinProfitPercentThreshold_zeroRoomsForOneDayOfStay() throws Exception {
        GroupPricingConfigurationMinProfitService spy = spy(service);
        Integer daysToArrival = 30;

        GroupEvaluation groupEvaluation = GroupEvaluationObjectMother.buildGroupEvaluation();
        // set number of rooms for one day of stay to zero
        groupEvaluation.getGroupEvaluationDayOfStays().iterator().next().setNumberOfRooms(0);
        GroupEvaluationArrivalDate arrivalDate = groupEvaluation.getGroupEvaluationArrivalDates().iterator().next();

        Map<LocalDate, BigDecimal> dailyPcts = buildDailyPercents(arrivalDate.getArrivalDate(), arrivalDate.getLastDayOfStay());

        doReturn(dailyPcts).when(spy).getConfiguredMinProfitPcts(arrivalDate.getArrivalDate(), arrivalDate.getLastDayOfStay(), daysToArrival);
        doReturn(daysToArrival).when(spy).getNumberOfDaysToArrival(arrivalDate.getArrivalDate());

        BigDecimal retPctThreshold = spy.calculateMinProfitPercentThreshold(arrivalDate);

        assertEquals(new BigDecimal(20.00).setScale(2, HALF_UP), retPctThreshold);
        verify(spy).getConfiguredMinProfitPcts(arrivalDate.getArrivalDate(), arrivalDate.getLastDayOfStay(), daysToArrival);
        verify(spy).getNumberOfDaysToArrival(arrivalDate.getArrivalDate());
    }

    @Test
    public void getNumberOfDaysToArrival() throws Exception {
        LocalDate systemDate = new LocalDate(2016, 10, 1);
        LocalDate arrivalDate = new LocalDate(2016, 10, 30);
        when(dateService.getCaughtUpLocalDate()).thenReturn(systemDate);

        assertEquals(Integer.valueOf(29), service.getNumberOfDaysToArrival(arrivalDate));
        verify(dateService).getCaughtUpLocalDate();
    }

    @Test
    public void getNumberOfDaysToArrival_oneDay() throws Exception {
        LocalDate systemDate = new LocalDate(2016, 10, 1);
        LocalDate arrivalDate = new LocalDate(2016, 10, 2);
        when(dateService.getCaughtUpLocalDate()).thenReturn(systemDate);

        assertEquals(Integer.valueOf(1), service.getNumberOfDaysToArrival(arrivalDate));
        verify(dateService).getCaughtUpLocalDate();
    }

    @Test
    public void shouldConfigureProfitPercent_forValidMinimumProfitPercent() {
        Map<Pair<Integer,Integer>,BigDecimal> daysToConfig = new HashMap<>();
        daysToConfig.put(Pair.of(0,10),BigDecimal.TEN);
        daysToConfig.put(Pair.of(11,20),BigDecimal.TEN);
        daysToConfig.put(Pair.of(21,null),BigDecimal.ZERO);
        MinimumProfitPercent minimumProfitPercent = createMinimumProfitPercentDTO(daysToConfig);
        List<String> errors = service.createGroupPricingMinimumProfitPercent(minimumProfitPercent);

        assertEquals(0,errors.size());
        verify(tenantCrudService, times(1)).save(anyList());
    }

    @Test
    public void shouldGiveValidationError_whenOverlappingDayRanges_whenSavingMinimumProfitPercent() {
        Map<Pair<Integer,Integer>,BigDecimal> daysToConfig = new HashMap<>();
        daysToConfig.put(Pair.of(0,10),BigDecimal.TEN);
        daysToConfig.put(Pair.of(5,20),BigDecimal.TEN);
        daysToConfig.put(Pair.of(21,null),BigDecimal.ZERO);
        MinimumProfitPercent minimumProfitPercent = createMinimumProfitPercentDTO(daysToConfig);
        List<String> errors = service.createGroupPricingMinimumProfitPercent(minimumProfitPercent);

        assertEquals(2,errors.size());
        assertTrue(errors.contains("Overlap detected between range 0-10 and 5-20"));
        assertTrue(errors.contains("For range with startDay 5: startDay should be equal to 11"));
        verify(tenantCrudService, times(0)).save(anyList());
    }

    @Test
    public void shouldGiveValidationError_whenProfitPercentIsNegative_whenSavingMinimumProfitPercent() {
        MinimumProfitPercent minimumProfitPercent = createMinimumProfitPercentDTO(Map.of(Pair.of(0,null),BigDecimal.valueOf(-1)));
        List<String> errors = service.createGroupPricingMinimumProfitPercent(minimumProfitPercent);

        assertEquals(1,errors.size());
        assertTrue(errors.contains("Profit Percent must be between 0 and 100."));
        verify(tenantCrudService, times(0)).save(anyList());
    }

    @Test
    public void shouldGiveValidationError_whenDaysToArrivalConfigureIsMoreThan4_whenSavingMinimumProfitPercent() {
        Map<Pair<Integer,Integer>,BigDecimal> daysToConfig = new HashMap<>();
        daysToConfig.put(Pair.of(0,10),BigDecimal.TEN);
        daysToConfig.put(Pair.of(11,20),BigDecimal.TEN);
        daysToConfig.put(Pair.of(21,30),BigDecimal.TEN);
        daysToConfig.put(Pair.of(31,40),BigDecimal.TEN);
        daysToConfig.put(Pair.of(41,null),BigDecimal.ZERO);
        MinimumProfitPercent minimumProfitPercent = createMinimumProfitPercentDTO(daysToConfig);
        List<String> errors = service.createGroupPricingMinimumProfitPercent(minimumProfitPercent);

        assertEquals(1,errors.size());
        assertTrue(errors.contains("Only 4 days to arrival configuration is allowed."));
        verify(tenantCrudService, times(0)).save(anyList());
    }

    @Test
    public void shouldGiveValidationError_whenProfitPercentIsNull_whenSavingMinimumProfitPercent() {
        Map<Pair<Integer,Integer>,BigDecimal> daysToConfig = new HashMap<>();
        daysToConfig.put(Pair.of(0,null),null);

        MinimumProfitPercent minimumProfitPercent = createMinimumProfitPercentDTO(daysToConfig);
        List<String> errors = service.createGroupPricingMinimumProfitPercent(minimumProfitPercent);

        assertEquals(1,errors.size());
        assertTrue(errors.contains("At least one day of week field must be entered for days to arrival when 'values' is empty or null."));
        verify(tenantCrudService, times(0)).save(anyList());
    }

    private MinimumProfitPercent createMinimumProfitPercentDTO(Map<Pair<Integer, Integer>, BigDecimal> dayRanges) {
        MinimumProfitPercent minimumProfitPercent = new MinimumProfitPercent();
        List<MinimumProfitPercent.DaysToArrivalConfig> totalDaysToArrival = new ArrayList<>();
        for (Map.Entry<Pair<Integer, Integer>, BigDecimal> entry : dayRanges.entrySet()) {

            Pair<Integer, Integer> dayRange = entry.getKey();
            MinimumProfitPercent.DaysToArrivalConfig daysToArrivalConfig = new MinimumProfitPercent.DaysToArrivalConfig();
            MinimumProfitPercent.DaysOfWeek daysOfWeek = new MinimumProfitPercent.DaysOfWeek();
            daysOfWeek.setValues(entry.getValue());
            service.setProfitPercentForAllDaysOfWeek(daysOfWeek);
            daysOfWeek.setValues(null);
            daysToArrivalConfig.setStartDay(dayRange.getLeft());
            daysToArrivalConfig.setEndDay(dayRange.getRight());
            daysToArrivalConfig.setDaysOfWeek(daysOfWeek);
            totalDaysToArrival.add(daysToArrivalConfig);
        }
        minimumProfitPercent.setDaysToArrivalRanges(totalDaysToArrival);
        return minimumProfitPercent;
    }

    private Map<LocalDate, BigDecimal> buildDailyPercents(LocalDate arrivalDate, LocalDate lastDayOfStay) {
        Map<LocalDate, BigDecimal> dailyPcts = new HashMap<>();

        while (!arrivalDate.isAfter(lastDayOfStay)) {
            dailyPcts.put(arrivalDate, new BigDecimal(20.00));
            arrivalDate = arrivalDate.plusDays(1);
        }

        return dailyPcts;
    }
}
