package com.ideas.tetris.pacman.services.bestavailablerate;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.BAROverride;
import com.ideas.tetris.pacman.services.contextholder.BusinessContextService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static com.ideas.tetris.pacman.common.constants.Constants.CONTEXT_KEY;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Created by idnrar on 04-03-2015.
 */
public class OverrideServiceCeilingBarByLosDataTest extends AbstractG3JupiterTest {

    private static final int ACCOM_CLASS_ID_FOR_STN = 6;
    private static final String rateBAR0 = "13";
    private static final String rateBAR1 = "14";
    private static final String rateBAR2 = "15";
    private static final String rateBAR3 = "16";
    private static final String rateBAR4 = "17";
    private static final String rateBAR5 = "18";
    private static final String rateBAR6 = "19";
    private static final String rateBAR7 = "20";
    private static final String rateBAR8 = "21";
    private static final BigDecimal BAR3ValueForMasterClass = new BigDecimal(3136.73438);
    private static final BigDecimal BAR4ValueForMasterClass = new BigDecimal(2940.73438);
    private static final BigDecimal BAR5ValueForMasterClass = new BigDecimal(2744.73438);
    private static final BigDecimal BAR6ValueForMasterClass = new BigDecimal(2548.73438);
    private static final BigDecimal BAR7ValueForMasterClass = new BigDecimal(2450.73438);
    private static final BigDecimal BAR3ValueForNonMasterClass = new BigDecimal(5236.73438);
    private static final BigDecimal BAR4ValueForNonMasterClass = new BigDecimal(4940.73438);
    private static final BigDecimal BAR5ValueForNonMasterClass = new BigDecimal(4744.73438);
    private static final BigDecimal BAR6ValueForNonMasterClass = new BigDecimal(4548.73438);
    private static final BigDecimal BAR7ValueForNonMasterClass = new BigDecimal(4450.73438);
    private static final BigDecimal NoBARValue = new BigDecimal(0.00);
    private OverrideService service = new OverrideService();
    private DateService dateService;
    private BusinessContextService businessContextService = new BusinessContextService();
    private PriceService priceService = new PriceService();
    private PacmanConfigParamsService configParamsService;
    private MasterClassOverrideHelperBean masterClassOverrideHelper;
    private LocalDate startDate;
    private int decisionReasonTypeIdForAllIsWell = 1;
    private String overrideTypeCeiling = Constants.BARDECISIONOVERRIDE_CEILING;
    private String overrideTypeFloor = "Floor";
    private String overrideTypeFloorAndCeiling = Constants.BARDECISIONOVERRIDE_FLOOR_AND_CEILING;
    private String overrideTypeNone = "None";
    private String overrideTypeUser = "User";
    private String overrideTypePending = "Pending";
    private int accomTypeIdNine = 9;
    private int accomTypeIdTen = 10;
    private int accomTypeIdEleven = 11;
    private int accomTypeIdTwelve = 12;
    private int losOne = 1;
    private int losTwo = 2;
    private int losThree = 3;
    private int losFive = 5;
    private int losSix = 6;
    private int losSeven = 7;
    private int masterClassId = 7;
    private int propertyIdForH2 = 6;

    @BeforeEach
    public void setUp() {
        configParamsService = mock(PacmanConfigParamsService.class);
        dateService = new DateService() {

            @Override
            public Date getCaughtUpDate() {
                return DateUtil.getFirstDayOfCurrentMonth();
            }

            @Override
            public Date getBusinessDate() {
                return getCaughtUpDate();
            }

            @Override
            public Date getWebRateShoppingDate() {
                return getCaughtUpDate();
            }

            @Override
            public Date getUnqualifiedRateCaughtUpDate() {
                return getCaughtUpDate();
            }

            @Override
            public Date getBARDisplayWindowEndDate() {
                return DateUtil.getLastDayOfCurrentMonth();
            }

            @Override
            public Date getBARDisplayWindowStartDate() {
                return getCaughtUpDate();
            }
        };
        service.setCrudService(tenantCrudService());
        businessContextService.setConfigService(configParamsService);
        businessContextService.setCrudService(tenantCrudService());
        service.setBusinessContextService(businessContextService);
        service.configService = configParamsService;
        DecisionService decisionService = DecisionService.createTestInstance();
        RateDeterminator rateDeterminator = new RateDeterminator();

        priceService.setCrudService(tenantCrudService());
        try {
            decisionService.setDateServiceLocal(dateService);
            decisionService.setCrudService(tenantCrudService());
            service.setDecisionService(decisionService);

            priceService.rateDeterminator = rateDeterminator;
            service.priceService = priceService;

        } catch (Exception e) {
            fail("couldn't inject dateService and crudService into DecisionService.");
        }


        rateDeterminator.setCrudService(tenantCrudService());
        service.setRateDeterminator(rateDeterminator);

        masterClassOverrideHelper = new MasterClassOverrideHelperBean();
        masterClassOverrideHelper.setCrudService(tenantCrudService());
        masterClassOverrideHelper.setBusinessContextService(businessContextService);
        service.setMasterClassHelper(masterClassOverrideHelper);

        WorkContextType wc = new WorkContextType();
        wc.setUserId("1");
        PacmanThreadLocalContextHolder.put(CONTEXT_KEY, wc);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testSaveCeilingOverrideWhenSystemDecisionLessThanOverrideForLOS1() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setCeilingOverrideForGivenDate(3, Integer.valueOf(rateBAR4), masterClassId, losOne);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeNone, null, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeNone, null, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForMasterClass, losOne, overrideTypeCeiling, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForNonMasterClass, losOne, overrideTypeCeiling, null, rateBAR4);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, NoBARValue, NoBARValue, BAR4ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR5ValueForNonMasterClass, NoBARValue, NoBARValue, BAR4ValueForNonMasterClass);
    }

    @Test
    public void testSaveCeilingAndFloorOverrideWhenSystemDecisionLessThanOverrideForLOS1() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setCeilingAndFloorOverrideForGivenDate(3, Integer.valueOf(rateBAR3), Integer.valueOf(rateBAR4), masterClassId, losOne);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, losOne, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, losOne, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeNone, null, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeNone, null, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForMasterClass, losOne, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForNonMasterClass, losOne, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR4ValueForMasterClass, BAR4ValueForMasterClass, NoBARValue, BAR3ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, NoBARValue, BAR3ValueForNonMasterClass);
    }

    @Test
    public void testSaveMultiDayCeilingOverrideWhenSystemDecisionLessThanOverrideForLOS1ForCalendarView() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setMultiDayCeilingOverrideForGivenDateRangeForCalendarView(3, 4, 5, Integer.valueOf(rateBAR4), masterClassId, losTwo);
        tenantCrudService().flushAndClear();

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class For Date1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data For Date1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypeNone, null, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class For Date1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypeNone, null, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForMasterClass, losTwo, overrideTypeCeiling, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class For Date1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForNonMasterClass, losTwo, overrideTypeCeiling, null, rateBAR4);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losTwo);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losTwo);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date1 ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, NoBARValue, NoBARValue, BAR4ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date1 ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR5ValueForNonMasterClass, NoBARValue, NoBARValue, BAR4ValueForNonMasterClass);

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date2 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, losTwo, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class For Date2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR5, losTwo, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data For Date2 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, losTwo, overrideTypeNone, null, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class For Date2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR5, losTwo, overrideTypeNone, null, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date2 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, BAR5ValueForMasterClass, losTwo, overrideTypeCeiling, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class For Date2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR5, BAR5ValueForNonMasterClass, losTwo, overrideTypeCeiling, null, rateBAR4);
        int decisionBarOutputOVRIdForMasterClass2 = getDecisionBarOutputOVRId(3, masterClassId, losTwo);
        int decisionBarOutputOVRIdForNonMasterClass2 = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losTwo);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date2 ", decisionBarOutputOVRIdForMasterClass2, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, NoBARValue, NoBARValue, BAR4ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date2 ", decisionBarOutputOVRIdForNonMasterClass2, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR5ValueForNonMasterClass, NoBARValue, NoBARValue, BAR4ValueForNonMasterClass);

    }

    @Test
    public void testSaveMultiDayCeilingAndFloorOverrideWhenSystemDecisionLessThanOverrideForLOS1ForCalendarView() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setMultiDayCeilingAndFloorOverrideForGivenDateRangeForCalendarView(3, 4, 5, Integer.valueOf(rateBAR3), Integer.valueOf(rateBAR4), masterClassId, losTwo);
        tenantCrudService().flushAndClear();

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class For Date1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypeNone, null, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class For Date1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypeNone, null, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForMasterClass, losTwo, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class For Date1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForNonMasterClass, losTwo, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losTwo);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losTwo);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date1 ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR4ValueForMasterClass, BAR4ValueForMasterClass, NoBARValue, BAR3ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date1 ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, NoBARValue, BAR3ValueForNonMasterClass);

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date2 ", masterClassId, startDate.plusDays(5).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class For Date2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data For Date2 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, losTwo, overrideTypeNone, null, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class For Date2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR5, losTwo, overrideTypeNone, null, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date2 ", masterClassId, startDate.plusDays(5).toString(), rateBAR4, BAR4ValueForMasterClass, losTwo, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class For Date2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR4, BAR4ValueForNonMasterClass, losTwo, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        int decisionBarOutputOVRIdForMasterClass2 = getDecisionBarOutputOVRId(5, masterClassId, losTwo);
        int decisionBarOutputOVRIdForNonMasterClass2 = getDecisionBarOutputOVRId(5, ACCOM_CLASS_ID_FOR_STN, losTwo);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date2 ", decisionBarOutputOVRIdForMasterClass2, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR4ValueForMasterClass, BAR4ValueForMasterClass, NoBARValue, BAR3ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date2 ", decisionBarOutputOVRIdForNonMasterClass2, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, NoBARValue, BAR3ValueForNonMasterClass);

    }


    @Test
    public void testSaveMultiDayRemoveCeilingOverrideWhenSystemDecisionLessThanOverrideForLOS1ForCalendarView() throws Exception {

        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setMultiDayCeilingOverrideForGivenDateRangeForCalendarView(3, 4, 5, Integer.valueOf(rateBAR4), masterClassId, losTwo);
        setMultiDayRemoveCeilingOverrideForGivenDateRangeForCalendarView(3, 4, 5, Integer.valueOf(rateBAR4), masterClassId, losTwo);

        tenantCrudService().flushAndClear();

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class for Date 1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class for Date 1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypeCeiling, null, overrideTypePending, rateBAR5, null, rateBAR4, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class for Date 1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypeCeiling, null, overrideTypePending, rateBAR5, null, rateBAR4, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class for Date 1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForMasterClass, losTwo, overrideTypePending, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class for Date 1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForNonMasterClass, losTwo, overrideTypePending, null, rateBAR4);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losTwo);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losTwo);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date1 ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, NoBARValue, BAR4ValueForMasterClass, NoBARValue);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date1 ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR5ValueForNonMasterClass, NoBARValue, BAR4ValueForNonMasterClass, NoBARValue);

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class for Date 2 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, losTwo, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class for Date 2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR5, losTwo, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 2 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, losTwo, overrideTypeCeiling, null, overrideTypePending, rateBAR5, null, rateBAR4, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class for Date 2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR5, losTwo, overrideTypeCeiling, null, overrideTypePending, rateBAR5, null, rateBAR4, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class for Date 2 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, BAR5ValueForMasterClass, losTwo, overrideTypePending, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class for Date 2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR5, BAR5ValueForNonMasterClass, losTwo, overrideTypePending, null, rateBAR4);
        int decisionBarOutputOVRIdForMasterClass2 = getDecisionBarOutputOVRId(5, masterClassId, losTwo);
        int decisionBarOutputOVRIdForNonMasterClass2 = getDecisionBarOutputOVRId(5, ACCOM_CLASS_ID_FOR_STN, losTwo);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date 2 ", decisionBarOutputOVRIdForMasterClass2, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, NoBARValue, BAR4ValueForMasterClass, NoBARValue);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date 2 ", decisionBarOutputOVRIdForNonMasterClass2, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR5ValueForNonMasterClass, NoBARValue, BAR4ValueForNonMasterClass, NoBARValue);

    }


    @Test
    public void testSaveMultiDayRemoveCeilingAndFloorOverrideWhenSystemDecisionLessThanOverrideForLOS1ForCalendarView() throws Exception {

        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setMultiDayCeilingAndFloorOverrideForGivenDateRangeForCalendarView(3, 4, 5, Integer.valueOf(rateBAR3), Integer.valueOf(rateBAR4), masterClassId, losTwo);
        setMultiDayRemoveCeilingAndFloorOverrideForGivenDateRangeForCalendarView(3, 4, 5, Integer.valueOf(rateBAR3), Integer.valueOf(rateBAR4), masterClassId, losTwo);

        tenantCrudService().flushAndClear();

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class for Date 1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, losTwo, overrideTypePending, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class for Date 1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, losTwo, overrideTypePending, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, overrideTypePending, rateBAR4, null, rateBAR3, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class for Date 1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, overrideTypePending, rateBAR4, null, rateBAR3, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class for Date 1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForMasterClass, losTwo, overrideTypePending, rateBAR4, rateBAR5);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class for Date 1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForNonMasterClass, losTwo, overrideTypePending, rateBAR4, rateBAR5);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losTwo);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losTwo);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date1 ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR4ValueForMasterClass, BAR4ValueForMasterClass, BAR4ValueForMasterClass, NoBARValue, BAR3ValueForMasterClass, NoBARValue);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date1 ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, NoBARValue, BAR3ValueForNonMasterClass, NoBARValue);

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class for Date 2 ", masterClassId, startDate.plusDays(4).toString(), rateBAR4, losTwo, overrideTypePending, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class for Date 2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(4).toString(), rateBAR4, losTwo, overrideTypePending, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Date 2 ", masterClassId, startDate.plusDays(4).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, overrideTypePending, rateBAR4, null, rateBAR3, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class for Date 2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(4).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, overrideTypePending, rateBAR4, null, rateBAR3, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class for Date 2 ", masterClassId, startDate.plusDays(4).toString(), rateBAR4, BAR4ValueForMasterClass, losTwo, overrideTypePending, rateBAR4, rateBAR5);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class for Date 2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(4).toString(), rateBAR4, BAR4ValueForNonMasterClass, losTwo, overrideTypePending, rateBAR4, rateBAR5);
        int decisionBarOutputOVRIdForMasterClass2 = getDecisionBarOutputOVRId(4, masterClassId, losTwo);
        int decisionBarOutputOVRIdForNonMasterClass2 = getDecisionBarOutputOVRId(4, ACCOM_CLASS_ID_FOR_STN, losTwo);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date2 ", decisionBarOutputOVRIdForMasterClass2, accomTypeIdTen, BAR4ValueForMasterClass, BAR4ValueForMasterClass, BAR4ValueForMasterClass, NoBARValue, BAR3ValueForMasterClass, NoBARValue);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date2 ", decisionBarOutputOVRIdForNonMasterClass2, accomTypeIdEleven, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, NoBARValue, BAR3ValueForNonMasterClass, NoBARValue);

    }


    @Test
    public void testSaveMultiDayCeilingOverrideWhenSystemDecisionLessThanOverrideForTabularView() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setMultiDayCeilingOverrideForGivenDateRangeForTabularView(3, 4, 5, Integer.valueOf(rateBAR4), masterClassId, losTwo, losSix);
        tenantCrudService().flushAndClear();

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date1 For LOS2 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class For Date1 For LOS2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data For Date1 For LOS2 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypeNone, null, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class For Date1 For LOS2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypeNone, null, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date1 For LOS2 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForMasterClass, losTwo, overrideTypeCeiling, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class For Date1 For LOS2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForNonMasterClass, losTwo, overrideTypeCeiling, null, rateBAR4);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losTwo);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losTwo);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date1 For LOS2 ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, NoBARValue, NoBARValue, BAR4ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date1 For LOS2 ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR5ValueForNonMasterClass, NoBARValue, NoBARValue, BAR4ValueForNonMasterClass);

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date2 For LOS6 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, losSix, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class For Date2 For LOS6 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR5, losSix, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data For Date2 For LOS6 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, losSix, overrideTypeNone, null, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class For Date2 For LOS6 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR5, losSix, overrideTypeNone, null, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date2 For LOS6 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, BAR5ValueForMasterClass, losSix, overrideTypeCeiling, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class For Date2 For LOS6 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR5, BAR5ValueForNonMasterClass, losSix, overrideTypeCeiling, null, rateBAR4);
        int decisionBarOutputOVRIdForMasterClass2 = getDecisionBarOutputOVRId(3, masterClassId, losSix);
        int decisionBarOutputOVRIdForNonMasterClass2 = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losSix);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date2 For LOS6 ", decisionBarOutputOVRIdForMasterClass2, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, NoBARValue, NoBARValue, BAR4ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date2 For LOS6 ", decisionBarOutputOVRIdForNonMasterClass2, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR5ValueForNonMasterClass, NoBARValue, NoBARValue, BAR4ValueForNonMasterClass);
    }


    @Test
    public void testSaveMultiDayCeilingAndFloorOverrideWhenSystemDecisionLessThanOverrideForTabularView() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setMultiDayCeilingAndFloorOverrideForGivenDateRangeForTabularView(3, 4, 5, Integer.valueOf(rateBAR3), Integer.valueOf(rateBAR4), masterClassId, losTwo, losSix);
        tenantCrudService().flushAndClear();

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date1 and LOS2  ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class For Date1 and LOS2  ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypeNone, null, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class For Date1 and LOS2  ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypeNone, null, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date1 and LOS2  ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForMasterClass, losTwo, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class For Date1 and LOS2  ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForNonMasterClass, losTwo, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losTwo);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losTwo);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date1 and LOS2  ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR4ValueForMasterClass, BAR4ValueForMasterClass, NoBARValue, BAR3ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date1 and LOS2  ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, NoBARValue, BAR3ValueForNonMasterClass);

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date2 and LOS6  ", masterClassId, startDate.plusDays(5).toString(), rateBAR4, losSix, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class For Date2 and LOS6  ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR4, losSix, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, losSix, overrideTypeNone, null, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class For Date2 and LOS6  ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR5, losSix, overrideTypeNone, null, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date2 and LOS6  ", masterClassId, startDate.plusDays(5).toString(), rateBAR4, BAR4ValueForMasterClass, losSix, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class For Date2 and LOS6  ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR4, BAR4ValueForNonMasterClass, losSix, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        int decisionBarOutputOVRIdForMasterClass2 = getDecisionBarOutputOVRId(5, masterClassId, losSix);
        int decisionBarOutputOVRIdForNonMasterClass2 = getDecisionBarOutputOVRId(5, ACCOM_CLASS_ID_FOR_STN, losSix);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date2 and LOS6  ", decisionBarOutputOVRIdForMasterClass2, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR4ValueForMasterClass, BAR4ValueForMasterClass, NoBARValue, BAR3ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date2 and LOS6  ", decisionBarOutputOVRIdForNonMasterClass2, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, NoBARValue, BAR3ValueForNonMasterClass);
    }

    @Test
    public void testSaveCeilingOverrideWhenSystemDecisionGreaterThanOverrideForLOS1() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setCeilingOverrideForGivenDate(3, Integer.valueOf(rateBAR6), masterClassId, losOne);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR6, losOne, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR6);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR6, losOne, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR6);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeNone, null, overrideTypeCeiling, rateBAR6, null, null, rateBAR6);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeNone, null, overrideTypeCeiling, rateBAR6, null, null, rateBAR6);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR6, BAR6ValueForMasterClass, losOne, overrideTypeCeiling, null, rateBAR6);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR6, BAR6ValueForNonMasterClass, losOne, overrideTypeCeiling, null, rateBAR6);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR6ValueForMasterClass, NoBARValue, NoBARValue, BAR6ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR6ValueForNonMasterClass, NoBARValue, NoBARValue, BAR6ValueForNonMasterClass);
    }

    @Test
    public void testSaveCeilingAndFloorOverrideWhenSystemDecisionGreaterThanOverrideForLOS1() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setCeilingAndFloorOverrideForGivenDate(3, Integer.valueOf(rateBAR6), Integer.valueOf(rateBAR7), masterClassId, losOne);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR6, losOne, overrideTypeFloorAndCeiling, rateBAR7, decisionReasonTypeIdForAllIsWell, rateBAR6);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR6, losOne, overrideTypeFloorAndCeiling, rateBAR7, decisionReasonTypeIdForAllIsWell, rateBAR6);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeNone, null, overrideTypeFloorAndCeiling, rateBAR6, rateBAR7, null, rateBAR6);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeNone, null, overrideTypeFloorAndCeiling, rateBAR6, rateBAR7, null, rateBAR6);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR6, BAR6ValueForMasterClass, losOne, overrideTypeFloorAndCeiling, rateBAR7, rateBAR6);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR6, BAR6ValueForNonMasterClass, losOne, overrideTypeFloorAndCeiling, rateBAR7, rateBAR6);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR6ValueForMasterClass, BAR7ValueForMasterClass, NoBARValue, BAR6ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR6ValueForNonMasterClass, BAR7ValueForNonMasterClass, NoBARValue, BAR6ValueForNonMasterClass);
    }

    @Test
    public void testSaveCeilingOverrideWhenSystemDecisionEqualToOverrideForLOS1() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setCeilingOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losOne);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR5);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR5);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeNone, null, overrideTypeCeiling, rateBAR5, null, null, rateBAR5);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeNone, null, overrideTypeCeiling, rateBAR5, null, null, rateBAR5);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForMasterClass, losOne, overrideTypeCeiling, null, rateBAR5);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForNonMasterClass, losOne, overrideTypeCeiling, null, rateBAR5);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, NoBARValue, NoBARValue, BAR5ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR5ValueForNonMasterClass, NoBARValue, NoBARValue, BAR5ValueForNonMasterClass);
    }

    @Test
    public void testSaveCeilingOverrideWhenUserOverrideDecisionLessThanOverrideForLOS1() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setUserOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losOne);
        setCeilingOverrideForGivenDate(3, Integer.valueOf(rateBAR4), masterClassId, losOne);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeUser, null, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeUser, null, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForMasterClass, losOne, overrideTypeCeiling, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForNonMasterClass, losOne, overrideTypeCeiling, null, rateBAR4);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, NoBARValue, NoBARValue, BAR4ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR5ValueForNonMasterClass, NoBARValue, NoBARValue, BAR4ValueForNonMasterClass);
    }


    @Test
    public void testSaveCeilingAndFloorOverrideWhenUserOverrideDecisionLessThanOverrideForLOS1() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setUserOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losTwo);
        setCeilingAndFloorOverrideForGivenDate(3, Integer.valueOf(rateBAR3), Integer.valueOf(rateBAR4), masterClassId, losTwo);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date1 and LOS2  ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class For Date1 and LOS2  ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypeUser, null, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class For Date1 and LOS2  ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypeUser, null, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date1 and LOS2  ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForMasterClass, losTwo, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class For Date1 and LOS2  ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForNonMasterClass, losTwo, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losTwo);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losTwo);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date1 and LOS2  ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR4ValueForMasterClass, BAR4ValueForMasterClass, NoBARValue, BAR3ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date1 and LOS2  ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, NoBARValue, BAR3ValueForNonMasterClass);
    }


    @Test
    public void testSaveMultiDayCeilingOverrideWhenUserOverrideDecisionLessThanOverrideForLOS1ForCalendarView() throws Exception {

        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setUserOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losFive);
        setUserOverrideForGivenDate(4, Integer.valueOf(rateBAR5), masterClassId, losFive);
        setMultiDayCeilingOverrideForGivenDateRangeForCalendarView(3, 4, 5, Integer.valueOf(rateBAR4), masterClassId, losFive);
        tenantCrudService().flushAndClear();

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losFive, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class For Date1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losFive, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data For Date1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losFive, overrideTypeUser, null, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class For Date1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losFive, overrideTypeUser, null, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForMasterClass, losFive, overrideTypeCeiling, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class For Date1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForNonMasterClass, losFive, overrideTypeCeiling, null, rateBAR4);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losFive);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losFive);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date1 ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, NoBARValue, NoBARValue, BAR4ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date1 ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR5ValueForNonMasterClass, NoBARValue, NoBARValue, BAR4ValueForNonMasterClass);

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date2 ", masterClassId, startDate.plusDays(4).toString(), rateBAR5, losFive, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class For Date2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(4).toString(), rateBAR5, losFive, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data For Date2 ", masterClassId, startDate.plusDays(4).toString(), rateBAR5, losFive, overrideTypeUser, null, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class For Date2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(4).toString(), rateBAR5, losFive, overrideTypeUser, null, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date2 ", masterClassId, startDate.plusDays(4).toString(), rateBAR5, BAR5ValueForMasterClass, losFive, overrideTypeCeiling, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class For Date2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(4).toString(), rateBAR5, BAR5ValueForNonMasterClass, losFive, overrideTypeCeiling, null, rateBAR4);
        int decisionBarOutputOVRIdForMasterClass2 = getDecisionBarOutputOVRId(3, masterClassId, losFive);
        int decisionBarOutputOVRIdForNonMasterClass2 = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losFive);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date2 ", decisionBarOutputOVRIdForMasterClass2, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, NoBARValue, NoBARValue, BAR4ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date2 ", decisionBarOutputOVRIdForNonMasterClass2, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR5ValueForNonMasterClass, NoBARValue, NoBARValue, BAR4ValueForNonMasterClass);

    }


    @Test
    public void testSaveMultiDayCeilingAndFloorOverrideWhenUserOverrideDecisionLessThanOverrideForLOS1ForCalendarView() throws Exception {

        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setUserOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losTwo);
        setUserOverrideForGivenDate(5, Integer.valueOf(rateBAR5), masterClassId, losTwo);
        setMultiDayCeilingAndFloorOverrideForGivenDateRangeForCalendarView(3, 4, 5, Integer.valueOf(rateBAR3), Integer.valueOf(rateBAR4), masterClassId, losTwo);
        tenantCrudService().flushAndClear();

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date1 and LOS2  ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class For Date1 and LOS2  ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypeUser, null, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class For Date1 and LOS2  ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypeUser, null, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date1 and LOS2  ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForMasterClass, losTwo, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class For Date1 and LOS2  ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForNonMasterClass, losTwo, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losTwo);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losTwo);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date1 and LOS2  ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR4ValueForMasterClass, BAR4ValueForMasterClass, NoBARValue, BAR3ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date1 and LOS2  ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, NoBARValue, BAR3ValueForNonMasterClass);

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date2 and LOS2  ", masterClassId, startDate.plusDays(5).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class For Date2 and LOS2  ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data For Date2 and LOS2 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, losTwo, overrideTypeUser, null, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class For Date2 and LOS2  ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR5, losTwo, overrideTypeUser, null, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date2 and LOS2  ", masterClassId, startDate.plusDays(5).toString(), rateBAR4, BAR4ValueForMasterClass, losTwo, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class For Date2 and LOS2  ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR4, BAR4ValueForNonMasterClass, losTwo, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        int decisionBarOutputOVRIdForMasterClass2 = getDecisionBarOutputOVRId(5, masterClassId, losTwo);
        int decisionBarOutputOVRIdForNonMasterClass2 = getDecisionBarOutputOVRId(5, ACCOM_CLASS_ID_FOR_STN, losTwo);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date2 and LOS2  ", decisionBarOutputOVRIdForMasterClass2, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR4ValueForMasterClass, BAR4ValueForMasterClass, NoBARValue, BAR3ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date2 and LOS2  ", decisionBarOutputOVRIdForNonMasterClass2, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, NoBARValue, BAR3ValueForNonMasterClass);

    }

    @Test
    public void testSaveMultiDayCeilingOverrideWhenUserOverrideDecisionLessThanOverrideForTabularView() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setUserOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losFive);
        setUserOverrideForGivenDate(5, Integer.valueOf(rateBAR5), masterClassId, losSeven);
        setMultiDayCeilingOverrideForGivenDateRangeForTabularView(3, 4, 5, Integer.valueOf(rateBAR4), masterClassId, losFive, losSeven);
        tenantCrudService().flushAndClear();

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date1 For LOS5 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losFive, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class For Date1 For LOS5 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losFive, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data For Date1 For LOS5 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losFive, overrideTypeUser, null, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class For Date1 For LOS5 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losFive, overrideTypeUser, null, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date1 For LOS5 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForMasterClass, losFive, overrideTypeCeiling, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class For Date1 For LOS5 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForNonMasterClass, losFive, overrideTypeCeiling, null, rateBAR4);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losFive);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losFive);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date1 For LOS5 ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, NoBARValue, NoBARValue, BAR4ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date1 For LOS5 ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR5ValueForNonMasterClass, NoBARValue, NoBARValue, BAR4ValueForNonMasterClass);

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date2 For LOS7 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, losSeven, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class For Date2 For LOS7 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR5, losSeven, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data For Date2 For LOS7 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, losSeven, overrideTypeUser, null, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class For Date2 For LOS7 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR5, losSeven, overrideTypeUser, null, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date2 For LOS7 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, BAR5ValueForMasterClass, losSeven, overrideTypeCeiling, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class For Date2 For LOS7 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR5, BAR5ValueForNonMasterClass, losSeven, overrideTypeCeiling, null, rateBAR4);
        int decisionBarOutputOVRIdForMasterClass2 = getDecisionBarOutputOVRId(5, masterClassId, losSeven);
        int decisionBarOutputOVRIdForNonMasterClass2 = getDecisionBarOutputOVRId(5, ACCOM_CLASS_ID_FOR_STN, losSeven);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date2 For LOS7 ", decisionBarOutputOVRIdForMasterClass2, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, NoBARValue, NoBARValue, BAR4ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date2 For LOS7 ", decisionBarOutputOVRIdForNonMasterClass2, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR5ValueForNonMasterClass, NoBARValue, NoBARValue, BAR4ValueForNonMasterClass);

    }


    @Test
    public void testSaveMultiDayCeilingAndFloorOverrideWhenUserOverrideDecisionLessThanOverrideForTabularView() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setUserOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losFive);
        setUserOverrideForGivenDate(5, Integer.valueOf(rateBAR5), masterClassId, losSeven);
        setMultiDayCeilingAndFloorOverrideForGivenDateRangeForTabularView(3, 4, 5, Integer.valueOf(rateBAR3), Integer.valueOf(rateBAR4), masterClassId, losFive, losSeven);
        tenantCrudService().flushAndClear();

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date1 and LOS5  ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, losFive, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class For Date1 and LOS5  ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, losFive, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losFive, overrideTypeUser, null, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class For Date1 and LOS5  ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losFive, overrideTypeUser, null, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date1 and LOS5  ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForMasterClass, losFive, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class For Date1 and LOS5  ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForNonMasterClass, losFive, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losFive);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losFive);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date1 and LOS5  ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR4ValueForMasterClass, BAR4ValueForMasterClass, NoBARValue, BAR3ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date1 and LOS5  ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, NoBARValue, BAR3ValueForNonMasterClass);

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date2 and LOS7  ", masterClassId, startDate.plusDays(5).toString(), rateBAR4, losSeven, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class For Date2 and LOS7  ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR4, losSeven, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data For Date2 and LOS7 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, losSeven, overrideTypeUser, null, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class For Date2 and LOS7  ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR5, losSeven, overrideTypeUser, null, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date2 and LOS7  ", masterClassId, startDate.plusDays(5).toString(), rateBAR4, BAR4ValueForMasterClass, losSeven, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class For Date2 and LOS7  ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR4, BAR4ValueForNonMasterClass, losSeven, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        int decisionBarOutputOVRIdForMasterClass2 = getDecisionBarOutputOVRId(5, masterClassId, losSeven);
        int decisionBarOutputOVRIdForNonMasterClass2 = getDecisionBarOutputOVRId(5, ACCOM_CLASS_ID_FOR_STN, losSeven);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date2 and LOS7  ", decisionBarOutputOVRIdForMasterClass2, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR4ValueForMasterClass, BAR4ValueForMasterClass, NoBARValue, BAR3ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date2 and LOS7  ", decisionBarOutputOVRIdForNonMasterClass2, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, NoBARValue, BAR3ValueForNonMasterClass);

    }

    @Test
    public void testSaveCeilingOverrideWhenUserOverrideDecisionGreaterThanOverrideForLOS1() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setUserOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losOne);
        setCeilingOverrideForGivenDate(3, Integer.valueOf(rateBAR6), masterClassId, losOne);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR6, losOne, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR6);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeUser, null, overrideTypeCeiling, rateBAR6, null, null, rateBAR6);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR6, BAR6ValueForMasterClass, losOne, overrideTypeCeiling, null, rateBAR6);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR6ValueForMasterClass, NoBARValue, NoBARValue, BAR6ValueForMasterClass);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(2, ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString());
    }

    @Test
    public void testSaveCeilingAndFloorOverrideWhenUserOverrideDecisionGreaterThanOverrideForLOS1() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setUserOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losOne);
        setCeilingAndFloorOverrideForGivenDate(3, Integer.valueOf(rateBAR6), Integer.valueOf(rateBAR7), masterClassId, losOne);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR6, losOne, overrideTypeFloorAndCeiling, rateBAR7, decisionReasonTypeIdForAllIsWell, rateBAR6);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeUser, null, overrideTypeFloorAndCeiling, rateBAR6, rateBAR7, null, rateBAR6);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR6, BAR6ValueForMasterClass, losOne, overrideTypeFloorAndCeiling, rateBAR7, rateBAR6);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR6ValueForMasterClass, BAR7ValueForMasterClass, NoBARValue, BAR6ValueForMasterClass);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(2, ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString());
    }

    @Test
    public void testSaveCeilingOverrideWhenUserOverrideDecisionEqualToOverrideForLOS1() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setUserOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losOne);
        setCeilingOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losOne);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR5);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeUser, null, overrideTypeCeiling, rateBAR5, null, null, rateBAR5);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForMasterClass, losOne, overrideTypeCeiling, null, rateBAR5);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, NoBARValue, NoBARValue, BAR5ValueForMasterClass);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(2, ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString());
    }


    @Test
    public void testSaveCeilingAndFloorOverrideWhenUserOverrideDecisionEqualToOverrideForLOS1() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setUserOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losOne);
        setCeilingAndFloorOverrideForGivenDate(3, Integer.valueOf(rateBAR4), Integer.valueOf(rateBAR5), masterClassId, losOne);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeFloorAndCeiling, rateBAR5, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeUser, null, overrideTypeFloorAndCeiling, rateBAR5, rateBAR5, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForMasterClass, losOne, overrideTypeFloorAndCeiling, rateBAR5, rateBAR4);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, BAR5ValueForMasterClass, NoBARValue, BAR4ValueForMasterClass);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(2, ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString());
    }

    @Test
    public void testSaveCeilingOverrideWhenFloorOverrideDecisionLessThanOverrideForLOS1() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setFloorOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losOne);
        setCeilingOverrideForGivenDate(3, Integer.valueOf(rateBAR4), masterClassId, losOne);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeFloor, rateBAR5, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeFloor, rateBAR5, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForMasterClass, losOne, overrideTypeCeiling, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForNonMasterClass, losOne, overrideTypeCeiling, null, rateBAR4);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, BAR5ValueForMasterClass, BAR5ValueForMasterClass, NoBARValue, NoBARValue, BAR4ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR5ValueForNonMasterClass, BAR5ValueForNonMasterClass, BAR5ValueForNonMasterClass, NoBARValue, NoBARValue, BAR4ValueForNonMasterClass);
    }

    @Test
    public void testSaveCeilingAndFloorOverrideWhenFloorOverrideDecisionLessThanOverrideForLOS1() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setFloorOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losTwo);
        setCeilingAndFloorOverrideForGivenDate(3, Integer.valueOf(rateBAR3), Integer.valueOf(rateBAR4), masterClassId, losTwo);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypeFloor, rateBAR5, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypeFloor, rateBAR5, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class  ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForMasterClass, losTwo, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForNonMasterClass, losTwo, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losTwo);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losTwo);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, BAR5ValueForMasterClass, BAR4ValueForMasterClass, BAR4ValueForMasterClass, NoBARValue, BAR3ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR5ValueForNonMasterClass, BAR5ValueForNonMasterClass, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, NoBARValue, BAR3ValueForNonMasterClass);
    }

    @Test
    public void testSaveMultiDayCeilingOverrideWhenFloorOverrideDecisionLessThanOverrideForLOS1ForCalendarView() throws Exception {

        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setFloorOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losOne);
        setFloorOverrideForGivenDate(5, Integer.valueOf(rateBAR5), masterClassId, losOne);
        setMultiDayCeilingOverrideForGivenDateRangeForCalendarView(3, 4, 5, Integer.valueOf(rateBAR4), masterClassId, losOne);
        tenantCrudService().flushAndClear();

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date1 For LOS1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class For Date1 For LOS1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data For Date1 For LOS1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeFloor, rateBAR5, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class For Date1 For LOS1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeFloor, rateBAR5, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date1 For LOS1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForMasterClass, losOne, overrideTypeCeiling, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class For Date1 For LOS1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForNonMasterClass, losOne, overrideTypeCeiling, null, rateBAR4);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date1 For LOS1 ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, BAR5ValueForMasterClass, BAR5ValueForMasterClass, NoBARValue, NoBARValue, BAR4ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date1 For LOS1 ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR5ValueForNonMasterClass, BAR5ValueForNonMasterClass, BAR5ValueForNonMasterClass, NoBARValue, NoBARValue, BAR4ValueForNonMasterClass);

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date2 For LOS1 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, losOne, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class For Date2 For LOS1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR5, losOne, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data For Date2 For LOS1 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, losOne, overrideTypeFloor, rateBAR5, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class For Date2 For LOS1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR5, losOne, overrideTypeFloor, rateBAR5, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date2 For LOS1 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, BAR5ValueForMasterClass, losOne, overrideTypeCeiling, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class For Date2 For LOS1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR5, BAR5ValueForNonMasterClass, losOne, overrideTypeCeiling, null, rateBAR4);
        int decisionBarOutputOVRIdForMasterClass2 = getDecisionBarOutputOVRId(5, masterClassId, losOne);
        int decisionBarOutputOVRIdForNonMasterClass2 = getDecisionBarOutputOVRId(5, ACCOM_CLASS_ID_FOR_STN, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date2 For LOS1 ", decisionBarOutputOVRIdForMasterClass2, accomTypeIdTen, BAR5ValueForMasterClass, BAR5ValueForMasterClass, BAR5ValueForMasterClass, NoBARValue, NoBARValue, BAR4ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date2 For LOS1 ", decisionBarOutputOVRIdForNonMasterClass2, accomTypeIdEleven, BAR5ValueForNonMasterClass, BAR5ValueForNonMasterClass, BAR5ValueForNonMasterClass, NoBARValue, NoBARValue, BAR4ValueForNonMasterClass);


    }

    @Test
    public void testSaveMultiDayCeilingAndFloorOverrideWhenFloorOverrideDecisionLessThanOverrideForLOS1ForCalendarView() throws Exception {

        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setFloorOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losTwo);
        setFloorOverrideForGivenDate(5, Integer.valueOf(rateBAR5), masterClassId, losTwo);
        setMultiDayCeilingAndFloorOverrideForGivenDateRangeForCalendarView(3, 4, 5, Integer.valueOf(rateBAR3), Integer.valueOf(rateBAR4), masterClassId, losTwo);
        tenantCrudService().flushAndClear();

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master ClassFor Date1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override dataFor Date1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypeFloor, rateBAR5, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master ClassFor Date1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypeFloor, rateBAR5, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForMasterClass, losTwo, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master ClassFor Date1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForNonMasterClass, losTwo, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losTwo);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losTwo);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master ClassFor Date1 ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, BAR5ValueForMasterClass, BAR4ValueForMasterClass, BAR4ValueForMasterClass, NoBARValue, BAR3ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master ClassFor Date1 ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR5ValueForNonMasterClass, BAR5ValueForNonMasterClass, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, NoBARValue, BAR3ValueForNonMasterClass);

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master ClassFor Date2 ", masterClassId, startDate.plusDays(5).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master ClassFor Date2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override dataFor Date2 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, losTwo, overrideTypeFloor, rateBAR5, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master ClassFor Date2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR5, losTwo, overrideTypeFloor, rateBAR5, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date2 ", masterClassId, startDate.plusDays(5).toString(), rateBAR4, BAR4ValueForMasterClass, losTwo, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master ClassFor Date2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR4, BAR4ValueForNonMasterClass, losTwo, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        int decisionBarOutputOVRIdForMasterClass2 = getDecisionBarOutputOVRId(5, masterClassId, losTwo);
        int decisionBarOutputOVRIdForNonMasterClass2 = getDecisionBarOutputOVRId(5, ACCOM_CLASS_ID_FOR_STN, losTwo);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master ClassFor Date2 ", decisionBarOutputOVRIdForMasterClass2, accomTypeIdTen, BAR5ValueForMasterClass, BAR5ValueForMasterClass, BAR4ValueForMasterClass, BAR4ValueForMasterClass, NoBARValue, BAR3ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master ClassFor Date2 ", decisionBarOutputOVRIdForNonMasterClass2, accomTypeIdEleven, BAR5ValueForNonMasterClass, BAR5ValueForNonMasterClass, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, NoBARValue, BAR3ValueForNonMasterClass);


    }

    @Test
    public void testSaveMultiDayCeilingOverrideWhenFloorOverrideDecisionLessThanOverrideForTabularView() throws Exception {

        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setFloorOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losOne);
        setFloorOverrideForGivenDate(4, Integer.valueOf(rateBAR5), masterClassId, losThree);
        setMultiDayCeilingOverrideForGivenDateRangeForTabularView(3, 4, 5, Integer.valueOf(rateBAR4), masterClassId, losOne, losThree);
        tenantCrudService().flushAndClear();

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date1 For LOS1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class For Date1 For LOS1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data For Date1 For LOS1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeFloor, rateBAR5, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class For Date1 For LOS1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeFloor, rateBAR5, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date1 For LOS1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForMasterClass, losOne, overrideTypeCeiling, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class For Date1 For LOS1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForNonMasterClass, losOne, overrideTypeCeiling, null, rateBAR4);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date1 For LOS1 ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, BAR5ValueForMasterClass, BAR5ValueForMasterClass, NoBARValue, NoBARValue, BAR4ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date1 For LOS1 ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR5ValueForNonMasterClass, BAR5ValueForNonMasterClass, BAR5ValueForNonMasterClass, NoBARValue, NoBARValue, BAR4ValueForNonMasterClass);

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date2 For LOS3 ", masterClassId, startDate.plusDays(4).toString(), rateBAR5, losThree, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class For Date2 For LOS3 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(4).toString(), rateBAR5, losThree, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data For Date2 For LOS3 ", masterClassId, startDate.plusDays(4).toString(), rateBAR5, losThree, overrideTypeFloor, rateBAR5, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class For Date2 For LOS3 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(4).toString(), rateBAR5, losThree, overrideTypeFloor, rateBAR5, overrideTypeCeiling, rateBAR5, null, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date2 For LOS3 ", masterClassId, startDate.plusDays(4).toString(), rateBAR5, BAR5ValueForMasterClass, losThree, overrideTypeCeiling, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class For Date2 For LOS3 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(4).toString(), rateBAR5, BAR5ValueForNonMasterClass, losThree, overrideTypeCeiling, null, rateBAR4);
        int decisionBarOutputOVRIdForMasterClass2 = getDecisionBarOutputOVRId(4, masterClassId, losThree);
        int decisionBarOutputOVRIdForNonMasterClass2 = getDecisionBarOutputOVRId(4, ACCOM_CLASS_ID_FOR_STN, losThree);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date2 For LOS3 ", decisionBarOutputOVRIdForMasterClass2, accomTypeIdTen, BAR5ValueForMasterClass, BAR5ValueForMasterClass, BAR5ValueForMasterClass, NoBARValue, NoBARValue, BAR4ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date2 For LOS3 ", decisionBarOutputOVRIdForNonMasterClass2, accomTypeIdEleven, BAR5ValueForNonMasterClass, BAR5ValueForNonMasterClass, BAR5ValueForNonMasterClass, NoBARValue, NoBARValue, BAR4ValueForNonMasterClass);

    }

    @Test
    public void testSaveMultiDayCeilingAndFloorOverrideWhenFloorOverrideDecisionLessThanOverrideForTabularView() throws Exception {

        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setFloorOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losTwo);
        setFloorOverrideForGivenDate(5, Integer.valueOf(rateBAR5), masterClassId, losThree);
        setMultiDayCeilingAndFloorOverrideForGivenDateRangeForTabularView(3, 4, 5, Integer.valueOf(rateBAR3), Integer.valueOf(rateBAR4), masterClassId, losTwo, losThree);
        tenantCrudService().flushAndClear();

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date1 For LOS2 ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master ClassFor Date1 For LOS2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override dataFor Date1 For LOS2 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypeFloor, rateBAR5, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master ClassFor Date1 For LOS2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losTwo, overrideTypeFloor, rateBAR5, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date1 For LOS2 ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForMasterClass, losTwo, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master ClassFor Date1 For LOS2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForNonMasterClass, losTwo, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losTwo);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losTwo);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master ClassFor Date1 For LOS2 ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, BAR5ValueForMasterClass, BAR4ValueForMasterClass, BAR4ValueForMasterClass, NoBARValue, BAR3ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master ClassFor Date1 For LOS2 ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR5ValueForNonMasterClass, BAR5ValueForNonMasterClass, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, NoBARValue, BAR3ValueForNonMasterClass);

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master ClassFor Date1 For LOS3 ", masterClassId, startDate.plusDays(5).toString(), rateBAR4, losThree, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master ClassFor Date1 For LOS3 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR4, losThree, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override dataFor Date1 For LOS3 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, losThree, overrideTypeFloor, rateBAR5, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master ClassFor Date1 For LOS3 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR5, losThree, overrideTypeFloor, rateBAR5, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, null, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date1 For LOS3 ", masterClassId, startDate.plusDays(5).toString(), rateBAR4, BAR4ValueForMasterClass, losThree, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master ClassFor Date1 For LOS3 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR4, BAR4ValueForNonMasterClass, losThree, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        int decisionBarOutputOVRIdForMasterClass2 = getDecisionBarOutputOVRId(5, masterClassId, losThree);
        int decisionBarOutputOVRIdForNonMasterClass2 = getDecisionBarOutputOVRId(5, ACCOM_CLASS_ID_FOR_STN, losThree);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master ClassFor Date1 For LOS3 ", decisionBarOutputOVRIdForMasterClass2, accomTypeIdTen, BAR5ValueForMasterClass, BAR5ValueForMasterClass, BAR4ValueForMasterClass, BAR4ValueForMasterClass, NoBARValue, BAR3ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master ClassFor Date1 For LOS3 ", decisionBarOutputOVRIdForNonMasterClass2, accomTypeIdEleven, BAR5ValueForNonMasterClass, BAR5ValueForNonMasterClass, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, NoBARValue, BAR3ValueForNonMasterClass);

    }

    @Test
    public void testSaveRemoveMultiDayCeilingOverrideWhenFloorOverrideDecisionLessThanOverrideForTabularView() throws Exception {

        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setFloorOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losOne);
        setFloorOverrideForGivenDate(5, Integer.valueOf(rateBAR5), masterClassId, losThree);
        setMultiDayCeilingOverrideForGivenDateRangeForTabularView(3, 4, 5, Integer.valueOf(rateBAR4), masterClassId, losOne, losThree);
        setMultiDayRemoveCeilingOverrideForGivenDateRangeForTabularView(3, 4, 5, Integer.valueOf(rateBAR4), masterClassId, losOne, losThree);
        tenantCrudService().flushAndClear();

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date1 For LOS1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, rateBAR5);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class For Date1 For LOS1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, rateBAR5);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data For Date1 For LOS1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeCeiling, null, overrideTypePending, rateBAR5, null, rateBAR4, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class For Date1 For LOS1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeCeiling, null, overrideTypePending, rateBAR5, null, rateBAR4, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date1 For LOS1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForMasterClass, losOne, overrideTypePending, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class For Date1 For LOS1 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForNonMasterClass, losOne, overrideTypePending, null, rateBAR4);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date1 For LOS1 ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, NoBARValue, BAR4ValueForMasterClass, NoBARValue);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date1 For LOS1 ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR5ValueForNonMasterClass, NoBARValue, BAR4ValueForNonMasterClass, NoBARValue);

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date2 For LOS3 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, losOne, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, rateBAR5);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class For Date2 For LOS3 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR5, losOne, overrideTypePending, null, decisionReasonTypeIdForAllIsWell, rateBAR5);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data For Date2 For LOS3 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, losOne, overrideTypeCeiling, null, overrideTypePending, rateBAR5, null, rateBAR4, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class For Date2 For LOS3 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR5, losOne, overrideTypeCeiling, null, overrideTypePending, rateBAR5, null, rateBAR4, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date2 For LOS3 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, BAR5ValueForMasterClass, losOne, overrideTypePending, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class For Date2 For LOS3 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR5, BAR5ValueForNonMasterClass, losOne, overrideTypePending, null, rateBAR4);
        int decisionBarOutputOVRIdForMasterClass2 = getDecisionBarOutputOVRId(5, masterClassId, losOne);
        int decisionBarOutputOVRIdForNonMasterClass2 = getDecisionBarOutputOVRId(5, ACCOM_CLASS_ID_FOR_STN, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class For Date2 For LOS3 ", decisionBarOutputOVRIdForMasterClass2, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, NoBARValue, BAR4ValueForMasterClass, NoBARValue);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class For Date2 For LOS3 ", decisionBarOutputOVRIdForNonMasterClass2, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR5ValueForNonMasterClass, NoBARValue, BAR4ValueForNonMasterClass, NoBARValue);

    }

    @Test
    public void testSaveRemoveMultiDayCeilingAndFloorOverrideWhenFloorOverrideDecisionLessThanOverrideForTabularView() throws Exception {

        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setFloorOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losTwo);
        setFloorOverrideForGivenDate(5, Integer.valueOf(rateBAR5), masterClassId, losThree);
        setMultiDayCeilingAndFloorOverrideForGivenDateRangeForTabularView(3, 4, 5, Integer.valueOf(rateBAR3), Integer.valueOf(rateBAR4), masterClassId, losTwo, losThree);
        setMultiDayRemoveCeilingAndFloorOverrideForGivenDateRangeForTabularView(3, 4, 5, Integer.valueOf(rateBAR3), Integer.valueOf(rateBAR4), masterClassId, losTwo, losThree);
        tenantCrudService().flushAndClear();

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date1 For LOS2 ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, losTwo, overrideTypePending, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master ClassFor Date1 For LOS2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, losTwo, overrideTypePending, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override dataFor Date1 For LOS2 ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, overrideTypePending, rateBAR4, rateBAR4, rateBAR3, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master ClassFor Date1 For LOS2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, losTwo, overrideTypeFloorAndCeiling, rateBAR4, overrideTypePending, rateBAR4, rateBAR4, rateBAR3, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date1 For LOS2 ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForMasterClass, losTwo, overrideTypePending, rateBAR4, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master ClassFor Date1 For LOS2 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForNonMasterClass, losTwo, overrideTypePending, rateBAR4, rateBAR3);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losTwo);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losTwo);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master ClassFor Date1 For LOS2 ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR4ValueForMasterClass, BAR4ValueForMasterClass, BAR4ValueForMasterClass, BAR4ValueForMasterClass, BAR3ValueForMasterClass, NoBARValue);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master ClassFor Date1 For LOS2 ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, BAR3ValueForNonMasterClass, NoBARValue);

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class For Date2 For LOS3 ", masterClassId, startDate.plusDays(5).toString(), rateBAR4, losThree, overrideTypePending, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master ClassFor Date2 For LOS3 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR4, losThree, overrideTypePending, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override dataFor Date2 For LOS3 ", masterClassId, startDate.plusDays(5).toString(), rateBAR4, losThree, overrideTypeFloorAndCeiling, rateBAR4, overrideTypePending, rateBAR4, rateBAR4, rateBAR3, null);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master ClassFor Date2 For LOS3 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR4, losThree, overrideTypeFloorAndCeiling, rateBAR4, overrideTypePending, rateBAR4, rateBAR4, rateBAR3, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class For Date2 For LOS3 ", masterClassId, startDate.plusDays(5).toString(), rateBAR4, BAR4ValueForMasterClass, losThree, overrideTypePending, rateBAR4, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master ClassFor Date2 For LOS3 ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString(), rateBAR4, BAR4ValueForNonMasterClass, losThree, overrideTypePending, rateBAR4, rateBAR3);
        int decisionBarOutputOVRIdForMasterClass2 = getDecisionBarOutputOVRId(5, masterClassId, losThree);
        int decisionBarOutputOVRIdForNonMasterClass2 = getDecisionBarOutputOVRId(5, ACCOM_CLASS_ID_FOR_STN, losThree);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master ClassFor Date2 For LOS3 ", decisionBarOutputOVRIdForMasterClass2, accomTypeIdTen, BAR4ValueForMasterClass, BAR4ValueForMasterClass, BAR4ValueForMasterClass, BAR4ValueForMasterClass, BAR3ValueForMasterClass, NoBARValue);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master ClassFor Date2 For LOS3 ", decisionBarOutputOVRIdForNonMasterClass2, accomTypeIdEleven, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, BAR3ValueForNonMasterClass, NoBARValue);

    }

    @Test
    public void testSaveCeilingOverrideWhenFloorOverrideDecisionGreaterThanOverrideForLOS1() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setFloorOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losOne);
        setCeilingOverrideForGivenDate(3, Integer.valueOf(rateBAR6), masterClassId, losOne);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR6, losOne, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR6);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeFloor, rateBAR5, overrideTypeCeiling, rateBAR6, null, null, rateBAR6);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR6, BAR6ValueForMasterClass, losOne, overrideTypeCeiling, null, rateBAR6);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, BAR5ValueForMasterClass, BAR6ValueForMasterClass, NoBARValue, NoBARValue, BAR6ValueForMasterClass);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(2, ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString());
    }

    @Test
    public void testSaveCeilingAndFloorOverrideWhenFloorOverrideDecisionGreaterThanOverrideForLOS1() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setFloorOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losOne);
        setCeilingAndFloorOverrideForGivenDate(3, Integer.valueOf(rateBAR6), Integer.valueOf(rateBAR7), masterClassId, losOne);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR6, losOne, overrideTypeFloorAndCeiling, rateBAR7, decisionReasonTypeIdForAllIsWell, rateBAR6);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeFloor, rateBAR5, overrideTypeFloorAndCeiling, rateBAR6, rateBAR7, null, rateBAR6);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR6, BAR6ValueForMasterClass, losOne, overrideTypeFloorAndCeiling, rateBAR7, rateBAR6);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, BAR5ValueForMasterClass, BAR6ValueForMasterClass, BAR7ValueForMasterClass, NoBARValue, BAR6ValueForMasterClass);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(2, ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString());
    }


    @Test
    public void testSaveCeilingOverrideWhenFloorOverrideDecisionEqualToOverrideForLOS1() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setFloorOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losOne);
        setCeilingOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losOne);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR5);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeFloor, rateBAR5, overrideTypeCeiling, rateBAR5, null, null, rateBAR5);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForMasterClass, losOne, overrideTypeCeiling, null, rateBAR5);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, BAR5ValueForMasterClass, BAR5ValueForMasterClass, NoBARValue, NoBARValue, BAR5ValueForMasterClass);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(2, ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString());
    }

    @Test
    public void testSaveCeilingAndFloorOverrideWhenFloorOverrideDecisionEqualToOverrideForLOS1() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setFloorOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losOne);
        setCeilingAndFloorOverrideForGivenDate(3, Integer.valueOf(rateBAR4), Integer.valueOf(rateBAR5), masterClassId, losOne);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeFloorAndCeiling, rateBAR5, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeFloor, rateBAR5, overrideTypeFloorAndCeiling, rateBAR5, rateBAR5, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForMasterClass, losOne, overrideTypeFloorAndCeiling, rateBAR5, rateBAR4);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, BAR5ValueForMasterClass, BAR5ValueForMasterClass, BAR5ValueForMasterClass, NoBARValue, BAR4ValueForMasterClass);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(2, ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString());
    }

    @Test
    public void testSaveCeilingOverrideWhenCeilingOverrideDecisionLessThanOverrideForLOS1() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setCeilingOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losOne);
        setCeilingOverrideForGivenDate(3, Integer.valueOf(rateBAR4), masterClassId, losOne);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeCeiling, null, overrideTypeCeiling, rateBAR5, null, rateBAR5, rateBAR4);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeCeiling, null, overrideTypeCeiling, rateBAR5, null, rateBAR5, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForMasterClass, losOne, overrideTypeCeiling, null, rateBAR4);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForNonMasterClass, losOne, overrideTypeCeiling, null, rateBAR4);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, BAR4ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR5ValueForNonMasterClass, NoBARValue, BAR5ValueForNonMasterClass, BAR4ValueForNonMasterClass);
    }

    @Test
    public void testSaveCeilingAndFloorOverrideWhenCeilingOverrideDecisionLessThanOverrideForLOS1() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setCeilingOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losOne);
        setCeilingAndFloorOverrideForGivenDate(3, Integer.valueOf(rateBAR3), Integer.valueOf(rateBAR4), masterClassId, losOne);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, losOne, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOut("Verifying Decision Bar out Put data for Non Master Class ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, losOne, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeCeiling, null, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, rateBAR5, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data for Non Master Class", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeCeiling, null, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, rateBAR5, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForMasterClass, losOne, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Non Master Class ", ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForNonMasterClass, losOne, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        int decisionBarOutputOVRIdForNonMasterClass = getDecisionBarOutputOVRId(3, ACCOM_CLASS_ID_FOR_STN, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR4ValueForMasterClass, BAR4ValueForMasterClass, BAR5ValueForMasterClass, BAR3ValueForMasterClass);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Non Master Class ", decisionBarOutputOVRIdForNonMasterClass, accomTypeIdEleven, BAR5ValueForNonMasterClass, NoBARValue, BAR4ValueForNonMasterClass, BAR4ValueForNonMasterClass, BAR5ValueForNonMasterClass, BAR3ValueForNonMasterClass);
    }

    @Test
    public void testSaveCeilingOverrideWhenCeilingOverrideDecisionGreaterThanOverrideForLOS1() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setCeilingOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losOne);
        setCeilingOverrideForGivenDate(3, Integer.valueOf(rateBAR6), masterClassId, losOne);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR6, losOne, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR6);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeCeiling, null, overrideTypeCeiling, rateBAR6, null, rateBAR5, rateBAR6);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR6, BAR6ValueForMasterClass, losOne, overrideTypeCeiling, null, rateBAR6);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR6ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, BAR6ValueForMasterClass);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(2, ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString());
    }

    @Test
    public void testSaveCeilingOverrideWhenCeilingOverrideDecisionEqualToOverrideForLOS1() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setCeilingOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losOne);
        setCeilingOverrideForGivenDate(3, Integer.valueOf(rateBAR5), masterClassId, losOne);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeCeiling, null, decisionReasonTypeIdForAllIsWell, rateBAR5);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeCeiling, null, overrideTypeCeiling, rateBAR5, null, rateBAR5, rateBAR5);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForMasterClass, losOne, overrideTypeCeiling, null, rateBAR5);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, BAR5ValueForMasterClass);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(2, ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString());
    }

    @Test
    public void testSaveCeilingAndFloorOverrideWhenExistingCeilingAndFloorOverrideDecisionLessThanOverrideForLOS1() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setCeilingAndFloorOverrideForGivenDate(3, Integer.valueOf(rateBAR5), Integer.valueOf(rateBAR6), masterClassId, losOne);
        setCeilingAndFloorOverrideForGivenDate(3, Integer.valueOf(rateBAR3), Integer.valueOf(rateBAR4), masterClassId, losOne);
        tenantCrudService().flushAndClear();
        assertDecisionBarOut("Verifying Decision Bar out Put data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, losOne, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override data ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeFloorAndCeiling, rateBAR6, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, rateBAR5, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master Class ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForMasterClass, losOne, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master Class ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, BAR6ValueForMasterClass, BAR4ValueForMasterClass, BAR4ValueForMasterClass, BAR5ValueForMasterClass, BAR3ValueForMasterClass);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(2, ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString());
    }

    @Test
    public void testSaveCeilingAndFloorOverrideWhenExistingCeilingAndFloorOverrideDecisionLessThanOverrideForCalendarView() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setMultiDayCeilingAndFloorOverrideForGivenDateRangeForCalendarView(3, 4, 5, Integer.valueOf(rateBAR5), Integer.valueOf(rateBAR6), masterClassId, losOne);
        setMultiDayCeilingAndFloorOverrideForGivenDateRangeForCalendarView(3, 4, 5, Integer.valueOf(rateBAR3), Integer.valueOf(rateBAR4), masterClassId, losOne);
        tenantCrudService().flushAndClear();

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master ClassFor Date1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, losOne, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override dataFor Date1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeFloorAndCeiling, rateBAR6, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, rateBAR5, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master ClassFor Date1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForMasterClass, losOne, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master ClassFor Date1 ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, BAR6ValueForMasterClass, BAR4ValueForMasterClass, BAR4ValueForMasterClass, BAR5ValueForMasterClass, BAR3ValueForMasterClass);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(2, ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString());

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master ClassFor Date2 ", masterClassId, startDate.plusDays(5).toString(), rateBAR4, losOne, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override dataFor Date2 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, losOne, overrideTypeFloorAndCeiling, rateBAR6, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, rateBAR5, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master ClassFor Date2 ", masterClassId, startDate.plusDays(5).toString(), rateBAR4, BAR4ValueForMasterClass, losOne, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        int decisionBarOutputOVRIdForMasterClass2 = getDecisionBarOutputOVRId(5, masterClassId, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master ClassFor Date2 ", decisionBarOutputOVRIdForMasterClass2, accomTypeIdTen, BAR5ValueForMasterClass, BAR6ValueForMasterClass, BAR4ValueForMasterClass, BAR4ValueForMasterClass, BAR5ValueForMasterClass, BAR3ValueForMasterClass);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(2, ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString());
    }


    @Test
    public void testSaveRemoveCeilingAndFloorOverrideWhenExistingCeilingAndFloorOverrideDecisionLessThanOverrideForCalendarView() throws Exception {

        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setMultiDayCeilingAndFloorOverrideForGivenDateRangeForCalendarView(3, 4, 5, Integer.valueOf(rateBAR5), Integer.valueOf(rateBAR6), masterClassId, losOne);
        setMultiDayRemoveCeilingAndFloorOverrideForGivenDateRangeForCalendarView(3, 4, 5, Integer.valueOf(rateBAR5), Integer.valueOf(rateBAR6), masterClassId, losOne);
        tenantCrudService().flushAndClear();

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master ClassFor Date1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypePending, rateBAR6, decisionReasonTypeIdForAllIsWell, rateBAR5);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override dataFor Date1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeFloorAndCeiling, rateBAR6, overrideTypePending, rateBAR5, null, rateBAR5, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master ClassFor Date1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForMasterClass, losOne, overrideTypePending, rateBAR6, rateBAR5);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master ClassFor Date1 ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, BAR6ValueForMasterClass, BAR5ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, NoBARValue);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(2, ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString());

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master ClassFor Date2 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, losOne, overrideTypePending, rateBAR6, decisionReasonTypeIdForAllIsWell, rateBAR5);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override dataFor Date2 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, losOne, overrideTypeFloorAndCeiling, rateBAR6, overrideTypePending, rateBAR5, null, rateBAR5, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master ClassFor Date2 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, BAR5ValueForMasterClass, losOne, overrideTypePending, rateBAR6, rateBAR5);
        int decisionBarOutputOVRIdForMasterClass2 = getDecisionBarOutputOVRId(5, masterClassId, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master ClassFor Date2 ", decisionBarOutputOVRIdForMasterClass2, accomTypeIdTen, BAR5ValueForMasterClass, BAR6ValueForMasterClass, BAR5ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, NoBARValue);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(2, ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString());

    }


    @Test
    public void testSaveRemoveCeilingAndFloorOverrideWhenExistingCeilingAndFloorOverrideDecisionLessThanOverrideForTabularView() throws Exception {

        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setMultiDayCeilingAndFloorOverrideForGivenDateRangeForTabularView(3, 4, 5, Integer.valueOf(rateBAR5), Integer.valueOf(rateBAR6), masterClassId, losOne, losSeven);
        setMultiDayRemoveCeilingAndFloorOverrideForGivenDateRangeForTabularView(3, 4, 5, Integer.valueOf(rateBAR5), Integer.valueOf(rateBAR6), masterClassId, losOne, losSeven);
        tenantCrudService().flushAndClear();

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master ClassFor Date1 For LOS1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypePending, rateBAR6, decisionReasonTypeIdForAllIsWell, rateBAR5);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override dataFor Date1 For LOS1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeFloorAndCeiling, rateBAR6, overrideTypePending, rateBAR5, null, rateBAR5, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master ClassFor Date1 For LOS1 ", masterClassId, startDate.plusDays(3).toString(), rateBAR5, BAR5ValueForMasterClass, losOne, overrideTypePending, rateBAR6, rateBAR5);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master ClassFor Date1 For LOS1 ", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, BAR6ValueForMasterClass, BAR5ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, NoBARValue);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(4, ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString());

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master ClassFor Date2 For LOS7 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, losSeven, overrideTypePending, rateBAR6, decisionReasonTypeIdForAllIsWell, rateBAR5);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override dataFor Date2 For LOS7 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, losSeven, overrideTypeFloorAndCeiling, rateBAR6, overrideTypePending, rateBAR5, null, rateBAR5, null);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master ClassFor Date2 For LOS7 ", masterClassId, startDate.plusDays(5).toString(), rateBAR5, BAR5ValueForMasterClass, losSeven, overrideTypePending, rateBAR6, rateBAR5);
        int decisionBarOutputOVRIdForMasterClass2 = getDecisionBarOutputOVRId(5, masterClassId, losSeven);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master ClassFor Date2 For LOS7 ", decisionBarOutputOVRIdForMasterClass2, accomTypeIdTen, BAR5ValueForMasterClass, BAR6ValueForMasterClass, BAR5ValueForMasterClass, NoBARValue, BAR5ValueForMasterClass, NoBARValue);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(4, ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString());

    }

    @Test
    public void testSaveCeilingAndFloorOverrideWhenExistingCeilingAndFloorOverrideDecisionLessThanOverrideForTabularView() throws Exception {
        setWorkContextProperty(TestProperty.H2);
        setUpInitialDataWithSingleBarDecisionTrue();
        setMultiDayCeilingAndFloorOverrideForGivenDateRangeForTabularView(3, 4, 5, Integer.valueOf(rateBAR5), Integer.valueOf(rateBAR6), masterClassId, losOne, losFive);
        setMultiDayCeilingAndFloorOverrideForGivenDateRangeForTabularView(3, 4, 5, Integer.valueOf(rateBAR3), Integer.valueOf(rateBAR4), masterClassId, losOne, losFive);
        tenantCrudService().flushAndClear();

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master ClassFor Date1 For LOS1", masterClassId, startDate.plusDays(3).toString(), rateBAR4, losOne, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override dataFor Date1 For LOS1", masterClassId, startDate.plusDays(3).toString(), rateBAR5, losOne, overrideTypeFloorAndCeiling, rateBAR6, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, rateBAR5, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master ClassFor Date1 For LOS1", masterClassId, startDate.plusDays(3).toString(), rateBAR4, BAR4ValueForMasterClass, losOne, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        int decisionBarOutputOVRIdForMasterClass = getDecisionBarOutputOVRId(3, masterClassId, losOne);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master ClassFor Date1 For LOS1", decisionBarOutputOVRIdForMasterClass, accomTypeIdTen, BAR5ValueForMasterClass, BAR6ValueForMasterClass, BAR4ValueForMasterClass, BAR4ValueForMasterClass, BAR5ValueForMasterClass, BAR3ValueForMasterClass);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(4, ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(3).toString());

        assertDecisionBarOut("Verifying Decision Bar out Put data for Master ClassFor Date2 For LOS5", masterClassId, startDate.plusDays(5).toString(), rateBAR4, losFive, overrideTypeFloorAndCeiling, rateBAR4, decisionReasonTypeIdForAllIsWell, rateBAR3);
        assertDecisionBarOutOvr("Verifying Decision Bar out Put override dataFor Date2 For LOS5", masterClassId, startDate.plusDays(5).toString(), rateBAR5, losFive, overrideTypeFloorAndCeiling, rateBAR6, overrideTypeFloorAndCeiling, rateBAR4, rateBAR4, rateBAR5, rateBAR3);
        assertDecisionBarOutOvrPace("Verifying Decision Bar out Put Pace data for Master ClassFor Date2 For LOS5", masterClassId, startDate.plusDays(5).toString(), rateBAR4, BAR4ValueForMasterClass, losFive, overrideTypeFloorAndCeiling, rateBAR4, rateBAR3);
        int decisionBarOutputOVRIdForMasterClass2 = getDecisionBarOutputOVRId(5, masterClassId, losFive);
        assertDecisionBarOutOvrDetails("Verifying Decision Bar out Put override Details data for Master ClassFor Date2 For LOS5", decisionBarOutputOVRIdForMasterClass2, accomTypeIdTen, BAR5ValueForMasterClass, BAR6ValueForMasterClass, BAR4ValueForMasterClass, BAR4ValueForMasterClass, BAR5ValueForMasterClass, BAR3ValueForMasterClass);
        assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(4, ACCOM_CLASS_ID_FOR_STN, startDate.plusDays(5).toString());
    }


    private void setCeilingOverrideForGivenDate(int dateNumber, int ceilingRateUnqualifiedId, int accomClassId, int los) {
        BAROverride override = new BAROverride();
        override.setArrivalDate(startDate.plusDays(dateNumber).toDate());
        override.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override.setLengthOfStay(los);
        override.setAccomClassId(accomClassId);
        service.saveBAROverrides(Arrays.asList(new BAROverride[]{override}));
    }

    private void setCeilingAndFloorOverrideForGivenDate(int dateNumber, int ceilingRateUnqualifiedId, int floorRateUnqualifiedId, int accomClassId, int los) {
        BAROverride override = new BAROverride();
        override.setArrivalDate(startDate.plusDays(dateNumber).toDate());
        override.setFloorRateUnqualifiedId(floorRateUnqualifiedId);
        override.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override.setLengthOfStay(los);
        override.setAccomClassId(accomClassId);
        service.saveBAROverrides(Arrays.asList(new BAROverride[]{override}));
    }

    private void setFloorOverrideForGivenDate(int dateNumber, int floorRateUnqualifiedId, int accomClassId, int los) {
        BAROverride override = new BAROverride();
        override.setArrivalDate(startDate.plusDays(dateNumber).toDate());
        override.setFloorRateUnqualifiedId(floorRateUnqualifiedId);
        override.setAccomClassId(accomClassId);
        override.setLengthOfStay(los);
        service.saveBAROverrides(Arrays.asList(new BAROverride[]{override}));
    }

    private void setUserOverrideForGivenDate(int dateNumber, int userRateUnqualifiedId, int accomClassId, int los) {
        BAROverride override = new BAROverride();
        override.setArrivalDate(startDate.plusDays(dateNumber).toDate());
        override.setSpecificRateUnqualifiedId(userRateUnqualifiedId);
        override.setAccomClassId(accomClassId);
        override.setLengthOfStay(los);
        service.saveBAROverrides(Arrays.asList(new BAROverride[]{override}));
    }


    private void setMultiDayCeilingOverrideForGivenDateRangeForCalendarView(int dateNumber1, int dateNumber2, int dateNumber3, int ceilingRateUnqualifiedId, int accomClassId, int los) {
        BAROverride override1 = new BAROverride();
        BAROverride override2 = new BAROverride();
        BAROverride override3 = new BAROverride();

        override1.setArrivalDate(startDate.plusDays(dateNumber1).toDate());
        override1.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override1.setAccomClassId(accomClassId);
        override1.setLengthOfStay(los);

        override2.setArrivalDate(startDate.plusDays(dateNumber2).toDate());
        override2.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override2.setAccomClassId(accomClassId);
        override2.setLengthOfStay(los);

        override3.setArrivalDate(startDate.plusDays(dateNumber3).toDate());
        override3.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override3.setAccomClassId(accomClassId);
        override3.setLengthOfStay(los);

        service.saveBAROverrides(Arrays.asList(new BAROverride[]{override1, override2, override3}));
    }

    private void setMultiDayCeilingAndFloorOverrideForGivenDateRangeForCalendarView(int dateNumber1, int dateNumber2, int dateNumber3, int ceilingRateUnqualifiedId, int floorRateUnqualifiedId, int accomClassId, int los) {
        BAROverride override1 = new BAROverride();
        BAROverride override2 = new BAROverride();
        BAROverride override3 = new BAROverride();

        override1.setArrivalDate(startDate.plusDays(dateNumber1).toDate());
        override1.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override1.setFloorRateUnqualifiedId(floorRateUnqualifiedId);
        override1.setAccomClassId(accomClassId);
        override1.setLengthOfStay(los);

        override2.setArrivalDate(startDate.plusDays(dateNumber2).toDate());
        override2.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override2.setFloorRateUnqualifiedId(floorRateUnqualifiedId);
        override2.setAccomClassId(accomClassId);
        override2.setLengthOfStay(los);

        override3.setArrivalDate(startDate.plusDays(dateNumber3).toDate());
        override3.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override3.setFloorRateUnqualifiedId(floorRateUnqualifiedId);
        override3.setAccomClassId(accomClassId);
        override3.setLengthOfStay(los);

        service.saveBAROverrides(Arrays.asList(new BAROverride[]{override1, override2, override3}));
    }

    private void setMultiDayRemoveCeilingOverrideForGivenDateRangeForCalendarView(int dateNumber1, int dateNumber2, int dateNumber3, int ceilingRateUnqualifiedId, int accomClassId, int los) {
        BAROverride override1 = new BAROverride();
        BAROverride override2 = new BAROverride();
        BAROverride override3 = new BAROverride();

        override1.setArrivalDate(startDate.plusDays(dateNumber1).toDate());
        override1.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override1.setAccomClassId(accomClassId);
        override1.setLengthOfStay(los);
        override1.setRemove(true);

        override2.setArrivalDate(startDate.plusDays(dateNumber2).toDate());
        override2.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override2.setAccomClassId(accomClassId);
        override2.setLengthOfStay(los);
        override2.setRemove(true);

        override3.setArrivalDate(startDate.plusDays(dateNumber3).toDate());
        override3.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override3.setAccomClassId(accomClassId);
        override3.setLengthOfStay(los);
        override3.setRemove(true);

        service.saveBAROverrides(Arrays.asList(new BAROverride[]{override1, override2, override3}));
    }


    private void setMultiDayRemoveCeilingAndFloorOverrideForGivenDateRangeForCalendarView(int dateNumber1, int dateNumber2, int dateNumber3, int ceilingRateUnqualifiedId, int floorRateUnqualifiedId, int accomClassId, int los) {
        BAROverride override1 = new BAROverride();
        BAROverride override2 = new BAROverride();
        BAROverride override3 = new BAROverride();

        override1.setArrivalDate(startDate.plusDays(dateNumber1).toDate());
        override1.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override1.setFloorRateUnqualifiedId(floorRateUnqualifiedId);
        override1.setAccomClassId(accomClassId);
        override1.setLengthOfStay(los);
        override1.setRemove(true);

        override2.setArrivalDate(startDate.plusDays(dateNumber2).toDate());
        override2.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override1.setFloorRateUnqualifiedId(floorRateUnqualifiedId);
        override2.setAccomClassId(accomClassId);
        override2.setLengthOfStay(los);
        override2.setRemove(true);

        override3.setArrivalDate(startDate.plusDays(dateNumber3).toDate());
        override3.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override1.setFloorRateUnqualifiedId(floorRateUnqualifiedId);
        override3.setAccomClassId(accomClassId);
        override3.setLengthOfStay(los);
        override3.setRemove(true);

        service.saveBAROverrides(Arrays.asList(new BAROverride[]{override1, override2, override3}));
    }

    private void setMultiDayCeilingOverrideForGivenDateRangeForTabularView(int dateNumber1, int dateNumber2, int dateNumber3, int ceilingRateUnqualifiedId, int accomClassId, int los1, int los2) {
        BAROverride override1 = new BAROverride();
        BAROverride override2 = new BAROverride();
        BAROverride override3 = new BAROverride();
        BAROverride override4 = new BAROverride();
        BAROverride override5 = new BAROverride();
        BAROverride override6 = new BAROverride();


        override1.setArrivalDate(startDate.plusDays(dateNumber1).toDate());
        override1.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override1.setAccomClassId(accomClassId);
        override1.setLengthOfStay(los1);

        override4.setArrivalDate(startDate.plusDays(dateNumber1).toDate());
        override4.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override4.setAccomClassId(accomClassId);
        override4.setLengthOfStay(los2);

        override2.setArrivalDate(startDate.plusDays(dateNumber2).toDate());
        override2.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override2.setAccomClassId(accomClassId);
        override2.setLengthOfStay(los1);

        override5.setArrivalDate(startDate.plusDays(dateNumber2).toDate());
        override5.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override5.setAccomClassId(accomClassId);
        override5.setLengthOfStay(los2);

        override3.setArrivalDate(startDate.plusDays(dateNumber3).toDate());
        override3.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override3.setAccomClassId(accomClassId);
        override3.setLengthOfStay(los1);

        override6.setArrivalDate(startDate.plusDays(dateNumber3).toDate());
        override6.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override6.setAccomClassId(accomClassId);
        override6.setLengthOfStay(los2);

        service.saveBAROverrides(Arrays.asList(new BAROverride[]{override1, override2, override3, override4, override5, override6}));
    }

    private void setMultiDayCeilingAndFloorOverrideForGivenDateRangeForTabularView(int dateNumber1, int dateNumber2, int dateNumber3, int ceilingRateUnqualifiedId, int floorRateUnqualifiedId, int accomClassId, int los1, int los2) {
        BAROverride override1 = new BAROverride();
        BAROverride override2 = new BAROverride();
        BAROverride override3 = new BAROverride();
        BAROverride override4 = new BAROverride();
        BAROverride override5 = new BAROverride();
        BAROverride override6 = new BAROverride();


        override1.setArrivalDate(startDate.plusDays(dateNumber1).toDate());
        override1.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override1.setFloorRateUnqualifiedId(floorRateUnqualifiedId);
        override1.setAccomClassId(accomClassId);
        override1.setLengthOfStay(los1);

        override4.setArrivalDate(startDate.plusDays(dateNumber1).toDate());
        override4.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override4.setFloorRateUnqualifiedId(floorRateUnqualifiedId);
        override4.setAccomClassId(accomClassId);
        override4.setLengthOfStay(los2);

        override2.setArrivalDate(startDate.plusDays(dateNumber2).toDate());
        override2.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override2.setFloorRateUnqualifiedId(floorRateUnqualifiedId);
        override2.setAccomClassId(accomClassId);
        override2.setLengthOfStay(los1);

        override5.setArrivalDate(startDate.plusDays(dateNumber2).toDate());
        override5.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override5.setFloorRateUnqualifiedId(floorRateUnqualifiedId);
        override5.setAccomClassId(accomClassId);
        override5.setLengthOfStay(los2);

        override3.setArrivalDate(startDate.plusDays(dateNumber3).toDate());
        override3.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override3.setFloorRateUnqualifiedId(floorRateUnqualifiedId);
        override3.setAccomClassId(accomClassId);
        override3.setLengthOfStay(los1);

        override6.setArrivalDate(startDate.plusDays(dateNumber3).toDate());
        override6.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override6.setFloorRateUnqualifiedId(floorRateUnqualifiedId);
        override6.setAccomClassId(accomClassId);
        override6.setLengthOfStay(los2);

        service.saveBAROverrides(Arrays.asList(new BAROverride[]{override1, override2, override3, override4, override5, override6}));
    }

    private void setMultiDayRemoveCeilingOverrideForGivenDateRangeForTabularView(int dateNumber1, int dateNumber2, int dateNumber3, int ceilingRateUnqualifiedId, int accomClassId, int los1, int los2) {
        BAROverride override1 = new BAROverride();
        BAROverride override2 = new BAROverride();
        BAROverride override3 = new BAROverride();
        BAROverride override4 = new BAROverride();
        BAROverride override5 = new BAROverride();
        BAROverride override6 = new BAROverride();


        override1.setArrivalDate(startDate.plusDays(dateNumber1).toDate());
        override1.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override1.setAccomClassId(accomClassId);
        override1.setLengthOfStay(los1);
        override1.setRemove(true);

        override4.setArrivalDate(startDate.plusDays(dateNumber1).toDate());
        override4.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override4.setAccomClassId(accomClassId);
        override4.setLengthOfStay(los2);
        override4.setRemove(true);

        override2.setArrivalDate(startDate.plusDays(dateNumber2).toDate());
        override2.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override2.setAccomClassId(accomClassId);
        override2.setLengthOfStay(los1);
        override2.setRemove(true);

        override5.setArrivalDate(startDate.plusDays(dateNumber2).toDate());
        override5.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override5.setAccomClassId(accomClassId);
        override5.setLengthOfStay(los2);
        override5.setRemove(true);

        override3.setArrivalDate(startDate.plusDays(dateNumber3).toDate());
        override3.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override3.setAccomClassId(accomClassId);
        override3.setLengthOfStay(los1);
        override3.setRemove(true);

        override6.setArrivalDate(startDate.plusDays(dateNumber3).toDate());
        override6.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override6.setAccomClassId(accomClassId);
        override6.setLengthOfStay(los2);
        override6.setRemove(true);

        service.saveBAROverrides(Arrays.asList(new BAROverride[]{override1, override2, override3, override4, override5, override6}));
    }

    private void setMultiDayRemoveCeilingAndFloorOverrideForGivenDateRangeForTabularView(int dateNumber1, int dateNumber2, int dateNumber3, int ceilingRateUnqualifiedId, int floorRateUnqualifiedId, int accomClassId, int los1, int los2) {
        BAROverride override1 = new BAROverride();
        BAROverride override2 = new BAROverride();
        BAROverride override3 = new BAROverride();
        BAROverride override4 = new BAROverride();
        BAROverride override5 = new BAROverride();
        BAROverride override6 = new BAROverride();


        override1.setArrivalDate(startDate.plusDays(dateNumber1).toDate());
        override1.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override1.setFloorRateUnqualifiedId(floorRateUnqualifiedId);
        override1.setAccomClassId(accomClassId);
        override1.setLengthOfStay(los1);
        override1.setRemove(true);

        override4.setArrivalDate(startDate.plusDays(dateNumber1).toDate());
        override4.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override4.setFloorRateUnqualifiedId(floorRateUnqualifiedId);
        override4.setAccomClassId(accomClassId);
        override4.setLengthOfStay(los2);
        override4.setRemove(true);

        override2.setArrivalDate(startDate.plusDays(dateNumber2).toDate());
        override2.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override2.setFloorRateUnqualifiedId(floorRateUnqualifiedId);
        override2.setAccomClassId(accomClassId);
        override2.setLengthOfStay(los1);
        override2.setRemove(true);

        override5.setArrivalDate(startDate.plusDays(dateNumber2).toDate());
        override5.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override5.setFloorRateUnqualifiedId(floorRateUnqualifiedId);
        override5.setAccomClassId(accomClassId);
        override5.setLengthOfStay(los2);
        override5.setRemove(true);

        override3.setArrivalDate(startDate.plusDays(dateNumber3).toDate());
        override3.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override3.setFloorRateUnqualifiedId(floorRateUnqualifiedId);
        override3.setAccomClassId(accomClassId);
        override3.setLengthOfStay(los1);
        override3.setRemove(true);

        override6.setArrivalDate(startDate.plusDays(dateNumber3).toDate());
        override6.setCeilingRateUnqualifiedId(ceilingRateUnqualifiedId);
        override6.setFloorRateUnqualifiedId(floorRateUnqualifiedId);
        override6.setAccomClassId(accomClassId);
        override6.setLengthOfStay(los2);
        override6.setRemove(true);

        service.saveBAROverrides(Arrays.asList(new BAROverride[]{override1, override2, override3, override4, override5, override6}));
    }

    private void assertDecisionBarOut(String Level, int accomClassId, String arrivalDate, String rateUnqualifiedId, int los,
                                      String overrideType, String floorRateUnqualifiedId, int decisionReasonTypeId, String ceilingRateUnqualifiedId) {
        List<Object[]> decisionBarOutPutData = tenantCrudService().findByNativeQuery("select * from Decision_Bar_Output where Arrival_DT='" + arrivalDate + "' " +
                " and Accom_Class_ID=" + accomClassId + " and los= " + los + " ");

        assertEquals(accomClassId, (decisionBarOutPutData.get(0)[3]), Level + " - Accom Class Id ");
        assertEquals(arrivalDate, (decisionBarOutPutData.get(0)[4].toString()), Level + " - Arrival Date ");
        assertEquals(rateUnqualifiedId, (decisionBarOutPutData.get(0)[5].toString()), Level + " - Rate Unqualified Id ");
        assertEquals(los, (decisionBarOutPutData.get(0)[6]), Level + " - LOS ");
        assertEquals(overrideType, (decisionBarOutPutData.get(0)[7].toString()), Level + " - Override Type ");
        if (decisionBarOutPutData.get(0)[8] != null) {
            assertEquals(floorRateUnqualifiedId, (decisionBarOutPutData.get(0)[8].toString()), Level + " - Floor Rate Unqualified Id ");
        }
        if (decisionBarOutPutData.get(0)[9] != null) {
            assertEquals(decisionReasonTypeId, (decisionBarOutPutData.get(0)[9]), Level + " - Decision Reason Type Id ");
        }
        if (decisionBarOutPutData.get(0)[13] != null) {
            assertEquals(ceilingRateUnqualifiedId, (decisionBarOutPutData.get(0)[13].toString()), Level + " - Ceiling Rate Unqualified Id ");
        }
    }

    private void assertDecisionBarOutOvr(String Level, int accomClassId, String arrivalDate, String oldRateUnqualifiedId, int los,
                                         String oldOverrideType, String oldFloorRateUnqualifiedId, String newOverrideType, String newRateUnqualifiedId,
                                         String newFloorRateUnqualifiedId, String oldCeilingRateUnqualifiedId, String newCeilingRateUnqualifiedId) {
        List<Object[]> decisionBarOutPutDataOvr = tenantCrudService().findByNativeQuery("select * from Decision_Bar_Output_ovr where Arrival_DT='" + arrivalDate + "' " +
                " and Accom_Class_ID=" + accomClassId + " and los=" + los + " order by Decision_ID desc ");
        assertEquals(accomClassId, (decisionBarOutPutDataOvr.get(0)[3]), Level + " - Accom Class Id ");
        assertEquals(arrivalDate, (decisionBarOutPutDataOvr.get(0)[4].toString()), Level + " - Arrival Date ");
        assertEquals(oldRateUnqualifiedId, (decisionBarOutPutDataOvr.get(0)[6].toString()), Level + " - Old Rate Unqualified Id ");
        assertEquals(los, (decisionBarOutPutDataOvr.get(0)[7]), Level + " - LOS ");
        assertEquals(oldOverrideType, (decisionBarOutPutDataOvr.get(0)[8].toString()), Level + " - Old Override Type ");
        if (decisionBarOutPutDataOvr.get(0)[9] != null) {
            assertEquals(oldFloorRateUnqualifiedId, (decisionBarOutPutDataOvr.get(0)[9].toString()));
        }
        if (decisionBarOutPutDataOvr.get(0)[10] != null) {
            assertEquals(newOverrideType, (decisionBarOutPutDataOvr.get(0)[10]), Level + " - New Override Type ");
        }
        if (decisionBarOutPutDataOvr.get(0)[11] != null) {
            assertEquals(newRateUnqualifiedId, (decisionBarOutPutDataOvr.get(0)[11].toString()), Level + " - New Rate Unqualified Id ");
        }
        if (decisionBarOutPutDataOvr.get(0)[12] != null) {
            assertEquals(newFloorRateUnqualifiedId, (decisionBarOutPutDataOvr.get(0)[12].toString()), Level + " - New Floor Rate Unqualified Id ");
        }
        if (decisionBarOutPutDataOvr.get(0)[14] != null) {
            assertEquals(oldCeilingRateUnqualifiedId, (decisionBarOutPutDataOvr.get(0)[14].toString()), Level + " - Old Ceiling Rate Unqualified Id ");
        }
        if (decisionBarOutPutDataOvr.get(0)[15] != null) {
            assertEquals(newCeilingRateUnqualifiedId, (decisionBarOutPutDataOvr.get(0)[15].toString()), Level + " - New Ceiling Rate Unqualified Id ");
        }
    }

    private void assertDecisionBarOutOvrDetails(String Level, int decisionBarOutputOVRId, int accomTypeId,
                                                BigDecimal OldRateUnqualifiedValue, BigDecimal OldFloorRateUnqualifiedValue,
                                                BigDecimal newRateUnqualifiedValue,
                                                BigDecimal newFloorRateUnqualifiedValue, BigDecimal OldCeilingRateUnqualifiedValue, BigDecimal newCeilingRateUnqualifiedValue) {
        List<Object[]> decisionBarOutPutDataOvrDetails = tenantCrudService().findByNativeQuery("select * from Decision_Bar_Output_OVR_Details where Decision_Bar_Output_OVR_ID=" + decisionBarOutputOVRId + " and Accom_Type_ID=" + accomTypeId + "  ");
        assertEquals(decisionBarOutputOVRId, (decisionBarOutPutDataOvrDetails.get(0)[1]), Level + " - Decision Bar Output Override Id ");
        assertEquals(accomTypeId, (decisionBarOutPutDataOvrDetails.get(0)[2]), Level + " - Accom Type Id ");

        if (decisionBarOutPutDataOvrDetails.get(0)[3] != null) {
            assertEquals(OldRateUnqualifiedValue.setScale(5, RoundingMode.HALF_UP), (decisionBarOutPutDataOvrDetails.get(0)[3]), Level + " - Old Rate Unqualified Value ");
        }

        if (decisionBarOutPutDataOvrDetails.get(0)[4] != null) {
            assertEquals(OldFloorRateUnqualifiedValue.setScale(5, RoundingMode.HALF_UP), (decisionBarOutPutDataOvrDetails.get(0)[4]), Level + " - Old Floor Rate Unqualified Value ");
        }

        if (decisionBarOutPutDataOvrDetails.get(0)[5] != null) {
            assertEquals(newRateUnqualifiedValue.setScale(5, RoundingMode.HALF_UP), (decisionBarOutPutDataOvrDetails.get(0)[5]), Level + " - New Rate Unqualified Value ");
        }

        if (decisionBarOutPutDataOvrDetails.get(0)[6] != null) {
            assertEquals(newFloorRateUnqualifiedValue.setScale(5, RoundingMode.HALF_UP), (decisionBarOutPutDataOvrDetails.get(0)[6]), Level + " - New Floor Rate Unqualified Value ");
        }

        if (decisionBarOutPutDataOvrDetails.get(0)[8] != null) {
            assertEquals(OldCeilingRateUnqualifiedValue.setScale(5, RoundingMode.HALF_UP), (decisionBarOutPutDataOvrDetails.get(0)[8]), Level + " - Old Ceiling Rate Unqualified Value ");
        }

        if (decisionBarOutPutDataOvrDetails.get(0)[9] != null) {
            assertEquals(newCeilingRateUnqualifiedValue.setScale(5, RoundingMode.HALF_UP), (decisionBarOutPutDataOvrDetails.get(0)[9]), Level + " - New Ceiling Rate Unqualified Value ");
        }
    }

    private void assertDecisionBarOutOvrPace(String Level, int accomClassId, String arrivalDate, String RateUnqualifiedId, BigDecimal derivedRateUnqualifiedValue, int los,
                                             String OverrideType, String FloorRateUnqualifiedValue, String CeilingRateUnqualifiedValue) {
        List<Object[]> assertDecisionBarOutOvrPace = tenantCrudService().findByNativeQuery("select * from PACE_Bar_Output where Arrival_DT='" + arrivalDate + "' " +
                " and Accom_Class_ID=" + accomClassId + " and los=" + los + " order by Decision_ID desc ");
        assertEquals(accomClassId, (assertDecisionBarOutOvrPace.get(0)[3]), Level + " - Accom Class Id ");
        assertEquals(arrivalDate, (assertDecisionBarOutOvrPace.get(0)[4].toString()), Level + " - Arrival Date ");
        assertEquals(RateUnqualifiedId, (assertDecisionBarOutOvrPace.get(0)[5].toString()), Level + " - Rate Unqualified Id ");
        assertEquals(derivedRateUnqualifiedValue.setScale(5, RoundingMode.HALF_UP), (assertDecisionBarOutOvrPace.get(0)[6]), Level + " - Derived Rate Unqualified Value ");
        assertEquals(los, (assertDecisionBarOutOvrPace.get(0)[7]), Level + " - LOS ");
        assertEquals(OverrideType, (assertDecisionBarOutOvrPace.get(0)[8].toString()), Level + " - Override Type ");
        if (assertDecisionBarOutOvrPace.get(0)[9] != null) {
            assertEquals(FloorRateUnqualifiedValue, (assertDecisionBarOutOvrPace.get(0)[9].toString()), Level + " - Floor Rate Unqualified Value ");
        }
        if (assertDecisionBarOutOvrPace.get(0)[14] != null) {
            assertEquals(CeilingRateUnqualifiedValue, (assertDecisionBarOutOvrPace.get(0)[14].toString()), Level + " - Ceiling Rate Unqualified Id ");
        }
    }

    private void UpdateBarRateDetails() {

        StringBuilder insertQuery = new StringBuilder();

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=2303.73438,Monday=2303.73438,Tuesday=2303.73438, ");
        insertQuery.append(" Wednesday=2303.73438,Thursday=2303.73438,Friday=2303.73438,Saturday=2303.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=" + rateBAR8 + " and Accom_Type_ID in (" + accomTypeIdTen + ") ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=4303.73438,Monday=4303.73438,Tuesday=4303.73438, ");
        insertQuery.append(" Wednesday=4303.73438,Thursday=4303.73438,Friday=4303.73438,Saturday=4303.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=" + rateBAR8 + " and Accom_Type_ID in (" + accomTypeIdNine + "," + accomTypeIdEleven + "," + accomTypeIdTwelve + ") ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=2450.73438,Monday=2450.73438,Tuesday=2450.73438, ");
        insertQuery.append(" Wednesday=2450.73438,Thursday=2450.73438,Friday=2450.73438,Saturday=2450.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=" + rateBAR7 + " and Accom_Type_ID in (" + accomTypeIdTen + ") ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=4450.73438,Monday=4450.73438,Tuesday=4450.73438, ");
        insertQuery.append(" Wednesday=4450.73438,Thursday=4450.73438,Friday=4450.73438,Saturday=4450.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=" + rateBAR7 + " and Accom_Type_ID in (" + accomTypeIdNine + "," + accomTypeIdEleven + "," + accomTypeIdTwelve + ") ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=2548.73438,Monday=2548.73438,Tuesday=2548.73438, ");
        insertQuery.append(" Wednesday=2548.73438,Thursday=2548.73438,Friday=2548.73438,Saturday=2548.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=" + rateBAR6 + " and Accom_Type_ID in (" + accomTypeIdTen + ")  ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=4548.73438,Monday=4548.73438,Tuesday=4548.73438, ");
        insertQuery.append(" Wednesday=4548.73438,Thursday=4548.73438,Friday=4548.73438,Saturday=4548.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=" + rateBAR6 + " and Accom_Type_ID in (" + accomTypeIdNine + "," + accomTypeIdEleven + "," + accomTypeIdTwelve + ")  ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=2744.73438,Monday=2744.73438,Tuesday=2744.73438, ");
        insertQuery.append(" Wednesday=2744.73438,Thursday=2744.73438,Friday=2744.73438,Saturday=2744.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=" + rateBAR5 + " and Accom_Type_ID in (" + accomTypeIdTen + ")  ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=4744.73438,Monday=4744.73438,Tuesday=4744.73438, ");
        insertQuery.append(" Wednesday=4744.73438,Thursday=4744.73438,Friday=4744.73438,Saturday=4744.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=" + rateBAR5 + " and Accom_Type_ID in (" + accomTypeIdNine + "," + accomTypeIdEleven + "," + accomTypeIdTwelve + ")  ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=2940.73438,Monday=2940.73438,Tuesday=2940.73438, ");
        insertQuery.append(" Wednesday=2940.73438,Thursday=2940.73438,Friday=2940.73438,Saturday=2940.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=" + rateBAR4 + " and Accom_Type_ID in (" + accomTypeIdTen + ") ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=4940.73438,Monday=4940.73438,Tuesday=4940.73438, ");
        insertQuery.append(" Wednesday=4940.73438,Thursday=4940.73438,Friday=4940.73438,Saturday=4940.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=" + rateBAR4 + " and Accom_Type_ID in (" + accomTypeIdNine + "," + accomTypeIdEleven + "," + accomTypeIdTwelve + ") ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=3136.73438,Monday=3136.73438,Tuesday=3136.73438, ");
        insertQuery.append(" Wednesday=3136.73438,Thursday=3136.73438,Friday=3136.73438,Saturday=3136.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=" + rateBAR3 + " and Accom_Type_ID in (" + accomTypeIdTen + ")  ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=5236.73438,Monday=5236.73438,Tuesday=5236.73438, ");
        insertQuery.append(" Wednesday=5236.73438,Thursday=5236.73438,Friday=5236.73438,Saturday=5236.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=" + rateBAR3 + " and Accom_Type_ID in (" + accomTypeIdNine + "," + accomTypeIdEleven + "," + accomTypeIdTwelve + ")  ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=3457.73438,Monday=3457.73438,Tuesday=3457.73438, ");
        insertQuery.append(" Wednesday=3457.73438,Thursday=3457.73438,Friday=3457.73438,Saturday=3457.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=" + rateBAR2 + " and Accom_Type_ID in (" + accomTypeIdTen + ") ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=5336.73438,Monday=5336.73438,Tuesday=5336.73438, ");
        insertQuery.append(" Wednesday=5336.73438,Thursday=5336.73438,Friday=5336.73438,Saturday=5336.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=" + rateBAR2 + " and Accom_Type_ID in (" + accomTypeIdNine + "," + accomTypeIdEleven + "," + accomTypeIdTwelve + ") ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=3795.73438,Monday=3795.73438,Tuesday=3795.73438, ");
        insertQuery.append(" Wednesday=3795.73438,Thursday=3795.73438,Friday=3795.73438,Saturday=3795.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=" + rateBAR1 + " and Accom_Type_ID in (" + accomTypeIdTen + ") ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=5476.73438,Monday=5476.73438,Tuesday=5476.73438, ");
        insertQuery.append(" Wednesday=5476.73438,Thursday=5476.73438,Friday=5476.73438,Saturday=5476.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=" + rateBAR1 + " and Accom_Type_ID in (" + accomTypeIdNine + "," + accomTypeIdEleven + "," + accomTypeIdTwelve + ") ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=3987.73438,Monday=3987.73438,Tuesday=3987.73438, ");
        insertQuery.append(" Wednesday=3987.73438,Thursday=3987.73438,Friday=3987.73438,Saturday=3987.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=" + rateBAR0 + " and Accom_Type_ID in (" + accomTypeIdTen + ")  ");

        insertQuery.append(" update Rate_Unqualified_Details set Sunday=5586.73438,Monday=5586.73438,Tuesday=5586.73438, ");
        insertQuery.append(" Wednesday=5586.73438,Thursday=5586.73438,Friday=5586.73438,Saturday=5586.73438 ");
        insertQuery.append(" where Rate_Unqualified_ID=" + rateBAR0 + " and Accom_Type_ID in (" + accomTypeIdNine + "," + accomTypeIdEleven + "," + accomTypeIdTwelve + ")  ");

        tenantCrudService().executeUpdateByNativeQuery(insertQuery.toString());

    }

    private int getDecisionBarOutputOVRId(int dateNumber, int accomClassId, int los) {
        List decisionBarOutputOVRIdList = tenantCrudService().findByNativeQuery("select Decision_Bar_Output_OVR_ID from Decision_Bar_Output_OVR where " +
                " Arrival_DT='" + startDate.plusDays(dateNumber).toString() + "' and Accom_Class_ID=" + accomClassId + " and los=" + los + " " +
                " order by Decision_ID desc");
        int decisionBarOutputOVRId = Integer.valueOf(decisionBarOutputOVRIdList.get(0).toString());
        return decisionBarOutputOVRId;
    }

    private LocalDate getLocalDate(int propertyID) {
        List caughtUpDates = tenantCrudService().findByNativeQuery("select  dbo.ufn_get_caughtup_date_by_property(" + propertyID + ",3,13)");
        String caughtUpDate = caughtUpDates.get(0).toString();
        return LocalDate.parse(caughtUpDate);
    }


    private int getMaxDecisionIdFromPaceBarOutPut(int accomClassId) {
        List maxDecisionIdFromPaceBarOutPutList = tenantCrudService().findByNativeQuery("select MAX(Decision_ID) from " +
                " PACE_Bar_Output where Arrival_DT='" + startDate.plusDays(3).toString() + "' and Accom_Class_ID=" + accomClassId + "  ");
        String maxDecisionIdFromPaceBarOutPut = maxDecisionIdFromPaceBarOutPutList.get(0).toString();
        return Integer.valueOf(maxDecisionIdFromPaceBarOutPut);
    }

    private void assertOnDecisionBarOutOverrideForRecordCountForGivenAccomClassForGivenDate(int expectedRowCount, int accomClassId, String arrivalDate) {

        List<Object[]> decisionBarOutOverrideData = tenantCrudService().findByNativeQuery("select count(*) from Decision_Bar_Output_OVR where Accom_Class_ID=" + accomClassId + " " +
                "and Arrival_DT='" + arrivalDate + "' ");
        assertEquals(expectedRowCount, decisionBarOutOverrideData.get(0), "Record Count in Decision Bar Out Override for accom class ID " + accomClassId + " - ");

    }

    private void createTestData() {
        setWorkContextProperty(TestProperty.H2);
        UpdateBarRateDetails();
        int maxDecisionIdFromPaceBarOutPutOfMasterClass = getMaxDecisionIdFromPaceBarOutPut(masterClassId);
        int maxDecisionIdFromPaceBarOutPutOfNonMasterClass = getMaxDecisionIdFromPaceBarOutPut(ACCOM_CLASS_ID_FOR_STN);
        tenantCrudService().executeUpdateByNativeQuery("delete from Decision_Bar_Output_OVR where Arrival_DT between '" + startDate.plusDays(3).toString() + "' and '" + startDate.plusDays(6).toString() + "' ");
        //tenantCrudService().executeUpdateByNativeQuery(" update Rate_Unqualified_Details set End_Date_DT='2373-10-14' where End_Date_DT='2173-10-14' ");
        tenantCrudService().executeUpdateByNativeQuery(" update Rate_Unqualified_Details set End_Date_DT='2373-10-14' where Accom_Type_ID=8");

        tenantCrudService().executeUpdateByNativeQuery("update Decision_Bar_Output set Override='None',Rate_Unqualified_ID=" + rateBAR5 + ",Floor_Rate_Unqualified_ID=NULL,Ceil_Rate_Unqualified_ID=NULL where Arrival_DT between '" + startDate.plusDays(3).toString() + "' and '" + startDate.plusDays(6).toString() + "' ");

        tenantCrudService().executeUpdateByNativeQuery("update PACE_Bar_Output set Override='None',Rate_Unqualified_ID=" + rateBAR5 + ",Derived_Unqualified_Value=90.1234," +
                "Floor_Rate_Unqualified_ID=NULL," +
                " Ceil_Rate_Unqualified_ID=NULL where Arrival_DT between '" + startDate.plusDays(3).toString() + "' and '" + startDate.plusDays(6).toString() + "' " +
                " and Decision_ID=" + maxDecisionIdFromPaceBarOutPutOfMasterClass + " ");

        tenantCrudService().executeUpdateByNativeQuery("update PACE_Bar_Output set Override='None',Rate_Unqualified_ID=" + rateBAR5 + ",Derived_Unqualified_Value=95.4321,Floor_Rate_Unqualified_ID=NULL," +
                " Ceil_Rate_Unqualified_ID=NULL where Arrival_DT between '" + startDate.plusDays(3).toString() + "' and '" + startDate.plusDays(6).toString() + "' " +
                " and Accom_Class_ID=" + ACCOM_CLASS_ID_FOR_STN + " and Decision_ID=" + maxDecisionIdFromPaceBarOutPutOfNonMasterClass + " ");

        tenantCrudService().executeUpdateByNativeQuery("delete from Decision_Bar_Output_OVR_Details");

    }

    private void setUpInitialDataWithSingleBarDecisionTrue() {
        startDate = getLocalDate(propertyIdForH2);

        createTestData();
        when(configParamsService.getParameterValue(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value())).thenReturn("true");
        when(configParamsService.getParameterValue(IPConfigParamName.ROH_ROHENABLED.value())).thenReturn("true");
    }


}
