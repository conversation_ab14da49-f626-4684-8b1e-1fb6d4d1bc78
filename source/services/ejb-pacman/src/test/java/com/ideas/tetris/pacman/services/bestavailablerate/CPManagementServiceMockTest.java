package com.ideas.tetris.pacman.services.bestavailablerate;

import com.ideas.infra.tetris.security.domain.Role;
import com.ideas.tetris.pacman.common.configparams.FeatureTogglesConfigParamName;
import com.ideas.tetris.pacman.common.configparams.GUIConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.PreProductionConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomClass;
import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.accommodation.service.AccommodationService;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.AgileRatesOffsetMethod;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.OptimizationLevel;
import com.ideas.tetris.pacman.services.agilerates.configuration.entity.ProductAccomType;
import com.ideas.tetris.pacman.services.bestavailablerate.dto.*;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.*;
import com.ideas.tetris.pacman.services.businessanalysis.BusinessAnalysisDashboardService;
import com.ideas.tetris.pacman.services.businessanalysis.dto.BusinessAnalysisDailyDataDto;
import com.ideas.tetris.pacman.services.businessanalysis.dto.BusinessAnalysisDailyIndicatorDto;
import com.ideas.tetris.pacman.services.businessanalysis.dto.BusinessAnalysisSpecialEventDto;
import com.ideas.tetris.pacman.services.commondaoandenities.tenant.entity.TenantStatusEnum;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.datafeed.dto.RolePermission;
import com.ideas.tetris.pacman.services.datafeed.service.RolePermissionService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEvent;
import com.ideas.tetris.pacman.services.eventaggregator.SyncEventAggregatorService;
import com.ideas.tetris.pacman.services.notes.NotesService;
import com.ideas.tetris.pacman.services.pricing.ProductManagementService;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingAccomClass;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.PricingBaseAccomType;
import com.ideas.tetris.pacman.services.pricingconfiguration.entity.TransientPricingBaseAccomType;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.PricingConfigurationService;
import com.ideas.tetris.pacman.services.pricingconfiguration.service.Supplement;
import com.ideas.tetris.pacman.services.product.Product;
import com.ideas.tetris.pacman.services.property.PropertyService;
import com.ideas.tetris.pacman.services.rms.prices.dto.PricingDto;
import com.ideas.tetris.pacman.services.security.RoleService;
import com.ideas.tetris.pacman.services.security.login.mapper.RoleModulePermissionMapperTest;
import com.ideas.tetris.pacman.services.security.login.util.RoleModulePermissionMapperUtil;
import com.ideas.tetris.pacman.services.tax.entity.Tax;
import com.ideas.tetris.pacman.services.tax.service.TaxService;
import com.ideas.tetris.pacman.services.validation.dto.OverrideRequest;
import com.ideas.tetris.pacman.services.validation.dto.ResponseDTO;
import com.ideas.tetris.pacman.util.BigDecimalUtil;
import com.ideas.tetris.platform.common.contextholder.PacmanWorkContextTestHelper;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.entity.DayOfWeek;
import com.ideas.tetris.platform.common.entity.IdAware;
import com.ideas.tetris.platform.common.time.JavaLocalDateUtils;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.Stage;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.regulator.service.spring.RegulatorSpringService;
import org.apache.commons.httpclient.HttpStatus;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;

import static com.ideas.tetris.pacman.common.constants.Constants.ACCOM_CLASS_IDS;
import static com.ideas.tetris.pacman.common.constants.Constants.STATUS;
import static com.ideas.tetris.pacman.services.bestavailablerate.CPManagementService.*;
import static com.ideas.tetris.pacman.services.bestavailablerate.entity.OccupancyType.SINGLE;
import static com.ideas.tetris.pacman.services.bestavailablerate.helper.AgileRatesOptimalBarsServiceTestContext.createProductAccomType;
import static com.ideas.tetris.platform.common.contextholder.PlatformWorkContextTestHelper.WC_CLIENT_CODE_BLACKSTONE;
import static com.ideas.tetris.platform.common.contextholder.PlatformWorkContextTestHelper.WC_USER_ID_SSO;
import static java.util.Arrays.asList;
import static java.util.Collections.singletonList;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;

@SuppressWarnings("rawtypes")
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class CPManagementServiceMockTest {
    private static final Set<DayOfWeek> DAY_OF_WEEKS = new HashSet<DayOfWeek>() {
        {
            add(DayOfWeek.TUESDAY);
            add(DayOfWeek.THURSDAY);
        }
    };
    private static final LocalDate START_DATE_RANGE = new LocalDate(2016, 11, 1);
    private static final LocalDate END_DATE_RANGE = new LocalDate(2016, 11, 3);
    public static final String PRICING = "PRICING";
    private static final String TEST_DATE = "2018-01-03";
    private static final Integer BAR_PRODUCT_ID = 1;
    private static final java.time.LocalDate CURRENT_DATE = java.time.LocalDate.now();
    private final AccomClass ACCOM_CLASS = createAccomClass("STANDARD", 1);
    private final AccomType ACCOM_TYPE = createAccomType(ACCOM_CLASS, 10, "STD");
    private final Product barProduct = buildBarProduct();

    @Mock
    TenantCrudServiceBean crudService;
    @Mock
    DateService dateService;
    @Mock
    PricingConfigurationService pricingConfigurationService;
    @Mock
    BusinessAnalysisDashboardService businessAnalysisDashboardService;
    @Mock
    SyncEventAggregatorService syncEventAggregatorService;
    @Mock
    BarDecisionService barDecisionService;
    @Mock
    NotesService notesService;
    @Mock
    DecisionService decisionService;
    @Captor
    ArgumentCaptor<Object> overrideCaptor;
    @InjectMocks
    CPManagementService service;
    @Spy
    @InjectMocks
    CPManagementService spyService;
    @Mock
    AccomTypeSupplementService accomTypeSupplementService;
    @Mock
    private AccommodationService roomClassServiceMock;
    @Mock
    private PacmanConfigParamsService configParamsService;

    @Mock
    private TaxService taxService;

    @Mock
    private RolePermissionService rolePermissionServiceMock;

    @Mock
    private RoleService roleServiceMock;

    @Mock
    CPDecisionContext cpDecisionContext;

    @Mock
    PrettyPricingService prettyPricingService;
    @Mock
    private ProductManagementService productManagementService;
    @Mock
    private RegulatorSpringService regulatorSpringService;
    @Mock
    private OverrideService overrideService;
    @Mock
    private PropertyService propertyService;
    @Mock
    private AccommodationService accommodationService;

    @Test
    public void search() {
        //setup
        List<CPDecisionBAROutput> expectedResults = asList(new CPDecisionBAROutput(), new CPDecisionBAROutput());
        PricingManagementCPSearchCriteria searchCriteria = new PricingManagementCPSearchCriteria();
        searchCriteria.setPropertyId(6);
        when(crudService.findByCriteria(searchCriteria)).thenReturn(expectedResults);

        //when
        List<CPDecisionBAROutput> actual = service.search(searchCriteria);

        //then
        assertEquals(expectedResults, actual);
    }

    @Test
    public void searchForDTO() {
        //setup
        LocalDate today = new LocalDate();
        LocalDate oneMonth = today.plusDays(30);
        LocalDate tomorrow = today.plusDays(1);
        LocalDate dayAfter = tomorrow.plusDays(1);

        AccomType accomType1 = new AccomType();
        accomType1.setId(1);
        AccomType accomType2 = new AccomType();
        accomType2.setId(2);
        AccomClass roomClass = new AccomClass();
        roomClass.setId(12345);

        CPDecisionBAROutput decision1 = new CPDecisionBAROutput();
        decision1.setArrivalDate(today);
        decision1.setId(Long.valueOf(1));
        decision1.setAccomType(accomType1);

        CPDecisionBAROutput decision2 = new CPDecisionBAROutput();
        decision2.setArrivalDate(today);
        decision2.setId(Long.valueOf(2));
        decision2.setAccomType(accomType2);

        CPDecisionBAROutput decision3 = new CPDecisionBAROutput();
        decision3.setArrivalDate(tomorrow);
        decision3.setId(Long.valueOf(3));
        decision3.setAccomType(accomType1);

        CPDecisionBAROutput decision4 = new CPDecisionBAROutput();
        decision4.setArrivalDate(tomorrow);
        decision4.setId(Long.valueOf(4));
        decision4.setAccomType(accomType2);

        CPDecisionBAROutput decision5 = new CPDecisionBAROutput();
        decision5.setArrivalDate(dayAfter);
        decision5.setId(Long.valueOf(5));
        decision5.setAccomType(accomType1);

        CPDecisionBAROutput decision6 = new CPDecisionBAROutput();
        decision6.setArrivalDate(dayAfter);
        decision6.setId(Long.valueOf(6));
        decision6.setAccomType(accomType2);

        PricingManagementCPSearchCriteria searchCriteria = new PricingManagementCPSearchCriteria();
        searchCriteria.setPropertyId(6);
        searchCriteria.setStartDate(today);
        searchCriteria.setEndDate(oneMonth);
        searchCriteria.setRoomClass(roomClass);

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(false);
        when(crudService.findByCriteria(searchCriteria)).thenReturn(asList(decision1, decision2, decision3, decision4, decision5, decision6));

        BusinessAnalysisDailyDataDto dataDto1 = new BusinessAnalysisDailyDataDto();
        dataDto1.setDate(DateParameter.fromDate(today.toDate()));
        dataDto1.setOccupancyForecast(BigDecimal.TEN);
        dataDto1.setOccupancyForecastPerc(BigDecimal.ONE);
        dataDto1.setOutOfOrder((long) 2);
        dataDto1.setOnBooks((long) 50);
        dataDto1.setLrv(BigDecimal.valueOf(25));

        when(businessAnalysisDashboardService.getBusinessAnalysisDailyDataDtos(today.toDate(), oneMonth.toDate())).thenReturn(singletonList(dataDto1));

        BusinessAnalysisDailyIndicatorDto indicatorDto1 = new BusinessAnalysisDailyIndicatorDto();
        indicatorDto1.setDate(DateParameter.fromDate(today.toDate()));
        indicatorDto1.setSpecialEventImpactFCST(true);
        BusinessAnalysisDailyIndicatorDto indicatorDto2 = new BusinessAnalysisDailyIndicatorDto();
        indicatorDto2.setDate(DateParameter.fromDate(tomorrow.toDate()));
        indicatorDto2.setSpecialEventInfoOnly(true);
        when(businessAnalysisDashboardService.getBusinessAnalysisDailyIndicatorDtos(today.toDate(), oneMonth.toDate())).thenReturn(asList(indicatorDto1, indicatorDto2));
        List<BusinessAnalysisSpecialEventDto> todaysEvents = asList(new BusinessAnalysisSpecialEventDto(), new BusinessAnalysisSpecialEventDto());
        List<BusinessAnalysisSpecialEventDto> tomorrowsEvents = asList(new BusinessAnalysisSpecialEventDto());
        when(businessAnalysisDashboardService.getBusinessAnalysisSpecialEventDtos(today.toDate())).thenReturn(todaysEvents);
        when(businessAnalysisDashboardService.getBusinessAnalysisSpecialEventDtos(tomorrow.toDate())).thenReturn(tomorrowsEvents);

        Map<Date, CompetitorInfo> competitorInfoMap = new HashMap<Date, CompetitorInfo>();
        CompetitorInfo competitorInfo1 = new CompetitorInfo();
        competitorInfo1.setCompetitorPrice("50.99");
        competitorInfoMap.put(today.toDate(), competitorInfo1);

        Map<Date, BigDecimal> lrvMap = new HashMap<Date, BigDecimal>();
        lrvMap.put(today.toDate(), new BigDecimal("28"));

        when(barDecisionService.getLastRoomValue(12345, today.toDate(), oneMonth.toDate())).thenReturn(lrvMap);
        when(barDecisionService.getCompetitorInfo(12345, 1, today.toDate(), oneMonth.toDate())).thenReturn(competitorInfoMap);

        //when
        List<CPBARDecisionDTO> actual = service.searchForBarDecisionDTO(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value()), searchCriteria, false);

        //then
        assertEquals(3, actual.size());
        CPBARDecisionDTO dto1 = actual.get(0);
        assertEquals(today, dto1.getDate());
        assertEquals(asList(decision1, decision2), dto1.getDecisions());
        assertEquals(BigDecimal.TEN, dto1.getOccupancyForecast());
        assertEquals(BigDecimal.ONE, dto1.getOccupancyForecastPercentage());
        assertEquals(Long.valueOf(2), dto1.getOutOfOrder());
        assertEquals(Long.valueOf(50), dto1.getRoomsOnBooks());
        assertEquals(BigDecimal.valueOf(28), dto1.getLrv());
        assertEquals(todaysEvents, dto1.getSpecialEvents());
        assertEquals("50.99", dto1.getCompetitorRate());

        CPBARDecisionDTO dto2 = actual.get(1);
        assertEquals(tomorrow, dto2.getDate());
        assertEquals(asList(decision3, decision4), dto2.getDecisions());
        assertEquals(tomorrowsEvents, dto2.getSpecialEvents());

        CPBARDecisionDTO dto3 = actual.get(2);
        assertEquals(dayAfter, dto3.getDate());
        assertEquals(asList(decision5, decision6), dto3.getDecisions());
    }

    @Test
    public void searchForDTO_baseRoomTypeOnly() {
        LocalDate today = new LocalDate();
        LocalDate oneMonth = today.plusDays(30);
        LocalDate tomorrow = today.plusDays(1);

        AccomType baseRoomType1 = new AccomType();
        baseRoomType1.setId(1);
        AccomClass roomClass1 = new AccomClass();
        roomClass1.setId(1);
        baseRoomType1.setAccomClass(roomClass1);

        CPDecisionBAROutput decision1 = new CPDecisionBAROutput();
        decision1.setArrivalDate(today);
        decision1.setId(Long.valueOf(1));
        decision1.setAccomType(baseRoomType1);

        PricingManagementCPSearchCriteria searchCriteria = new PricingManagementCPSearchCriteria();
        searchCriteria.setPropertyId(6);
        searchCriteria.setStartDate(today);
        searchCriteria.setEndDate(oneMonth);
        searchCriteria.setBaseRoomTypes(singletonList(baseRoomType1));

        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(true);
        when(crudService.findByCriteria(searchCriteria)).thenReturn(singletonList(decision1));

        BusinessAnalysisDailyDataDto dataDto1 = new BusinessAnalysisDailyDataDto();
        dataDto1.setDate(DateParameter.fromDate(today.toDate()));
        dataDto1.setOccupancyForecast(BigDecimal.TEN);
        dataDto1.setOccupancyForecastPerc(BigDecimal.ONE);
        dataDto1.setOutOfOrder((long) 2);
        dataDto1.setOnBooks((long) 50);
        dataDto1.setLrv(BigDecimal.valueOf(25));

        when(businessAnalysisDashboardService.getBusinessAnalysisDailyDataDtos(today.toDate(), oneMonth.toDate())).thenReturn(singletonList(dataDto1));

        BusinessAnalysisDailyIndicatorDto indicatorDto1 = new BusinessAnalysisDailyIndicatorDto();
        indicatorDto1.setDate(DateParameter.fromDate(today.toDate()));
        indicatorDto1.setSpecialEventImpactFCST(true);
        BusinessAnalysisDailyIndicatorDto indicatorDto2 = new BusinessAnalysisDailyIndicatorDto();
        indicatorDto2.setDate(DateParameter.fromDate(tomorrow.toDate()));
        indicatorDto2.setSpecialEventInfoOnly(true);

        when(businessAnalysisDashboardService.getBusinessAnalysisDailyIndicatorDtos(today.toDate(), oneMonth.toDate())).thenReturn(asList(indicatorDto1, indicatorDto2));

        List<BusinessAnalysisSpecialEventDto> todaysEvents = asList(new BusinessAnalysisSpecialEventDto(), new BusinessAnalysisSpecialEventDto());
        List<BusinessAnalysisSpecialEventDto> tomorrowsEvents = singletonList(new BusinessAnalysisSpecialEventDto());

        when(businessAnalysisDashboardService.getBusinessAnalysisSpecialEventDtos(today.toDate())).thenReturn(todaysEvents);
        when(businessAnalysisDashboardService.getBusinessAnalysisSpecialEventDtos(tomorrow.toDate())).thenReturn(tomorrowsEvents);

        Map<Date, CompetitorInfo> competitorInfoMapForRoomClass1 = new HashMap<Date, CompetitorInfo>();
        CompetitorInfo competitorInfo1 = new CompetitorInfo();
        competitorInfo1.setCompetitorPrice("50.99");
        competitorInfoMapForRoomClass1.put(today.toDate(), competitorInfo1);

        Map<Date, BigDecimal> lrvMapForRoomClass1 = new HashMap<Date, BigDecimal>();
        lrvMapForRoomClass1.put(today.toDate(), new BigDecimal("28"));

        when(barDecisionService.getLastRoomValue(1, today.toDate(), oneMonth.toDate())).thenReturn(lrvMapForRoomClass1);
        when(barDecisionService.getCompetitorInfo(1, 1, today.toDate(), oneMonth.toDate())).thenReturn(competitorInfoMapForRoomClass1);

        List<CPBARDecisionDTO> actual = service.searchForBarDecisionDTO(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value()), searchCriteria, false);

        assertEquals(1, actual.size());
        CPBARDecisionDTO dto1 = actual.get(0);
        CPDecisionBAROutput output1 = dto1.getDecisions().get(0);

        assertEquals(today, dto1.getDate());
        assertEquals(singletonList(decision1), dto1.getDecisions());
        assertEquals(BigDecimal.TEN, dto1.getOccupancyForecast());
        assertEquals(BigDecimal.ONE, dto1.getOccupancyForecastPercentage());
        assertEquals(Long.valueOf(2), dto1.getOutOfOrder());
        assertEquals(Long.valueOf(50), dto1.getRoomsOnBooks());
        assertEquals(todaysEvents, dto1.getSpecialEvents());
        assertEquals(BigDecimal.valueOf(28), output1.getLrv());
        assertEquals("50.99", output1.getCompetitorRate());
    }

    @Test
    public void addLrvAndCompetotirRateInfoToBaseRoomType() {
        LocalDate startLocalDate = new LocalDate(2017, 4, 1);
        Date startDate = startLocalDate.toDate();
        List<CPDecisionBAROutput> barOutputs = new ArrayList<>();

        CPDecisionBAROutput barOutput1 = new CPDecisionBAROutput();
        AccomType accomType1 = new AccomType();
        accomType1.setId(1);
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(1);
        accomType1.setAccomClass(accomClass1);
        barOutput1.setAccomType(accomType1);
        barOutput1.setArrivalDate(startLocalDate);
        barOutputs.add(barOutput1);

        CPDecisionBAROutput barOutput2 = new CPDecisionBAROutput();
        AccomType accomType2 = new AccomType();
        accomType2.setId(3);
        AccomClass accomClass2 = new AccomClass();
        accomClass2.setId(2);
        accomType2.setAccomClass(accomClass2);
        barOutput2.setAccomType(accomType2);
        barOutput2.setArrivalDate(startLocalDate);
        barOutputs.add(barOutput2);

        CPDecisionBAROutput barOutput3 = new CPDecisionBAROutput();
        AccomType accomType3 = new AccomType();
        accomType2.setId(3);
        AccomClass accomClass3 = new AccomClass();
        accomClass3.setId(3);
        accomType3.setAccomClass(accomClass3);
        barOutput3.setAccomType(accomType3);
        barOutput3.setArrivalDate(startLocalDate);
        barOutputs.add(barOutput3);

        Map<Date, BigDecimal> lrvMap1 = new HashMap<>();
        lrvMap1.put(startDate, new BigDecimal(70.00));

        Map<Date, BigDecimal> lrvMap2 = new HashMap<>();
        lrvMap2.put(startDate, new BigDecimal(80.00));

        Map<Date, BigDecimal> lrvMap3 = new HashMap<>();
        lrvMap3.put(startDate, new BigDecimal(90.00));

        Map<Date, CompetitorInfo> rateMap1 = new HashMap<>();
        CompetitorInfo info1 = new CompetitorInfo();
        info1.setCompetitorPrice("79.99");
        rateMap1.put(startDate, info1);

        Map<Date, CompetitorInfo> rateMap2 = new HashMap<>();
        CompetitorInfo info2 = new CompetitorInfo();
        info2.setCompetitorPrice("89.99");
        rateMap2.put(startDate, info2);

        Map<Date, CompetitorInfo> rateMap3 = new HashMap<>();
        CompetitorInfo info3 = new CompetitorInfo();
        info3.setCompetitorPrice("99.99");
        rateMap3.put(startDate, info3);

        when(barDecisionService.getLastRoomValue(1, startDate, startDate)).thenReturn(lrvMap1);
        when(barDecisionService.getLastRoomValue(2, startDate, startDate)).thenReturn(lrvMap2);
        when(barDecisionService.getLastRoomValue(3, startDate, startDate)).thenReturn(lrvMap3);

        when(barDecisionService.getCompetitorInfo(1, 1, startDate, startDate)).thenReturn(rateMap1);
        when(barDecisionService.getCompetitorInfo(2, 1, startDate, startDate)).thenReturn(rateMap2);
        when(barDecisionService.getCompetitorInfo(3, 1, startDate, startDate)).thenReturn(rateMap3);

        service.addLrvAndCompetotirRateInfoToBaseRoomType(barOutputs, startDate, startDate);

        assertEquals(new BigDecimal(70.00), barOutput1.getLrv());
        assertEquals("79.99", barOutput1.getCompetitorRate());

        assertEquals(new BigDecimal(80.00), barOutput2.getLrv());
        assertEquals("89.99", barOutput2.getCompetitorRate());

        assertEquals(new BigDecimal(90.00), barOutput3.getLrv());
        assertEquals("99.99", barOutput3.getCompetitorRate());
    }

    @Test
    public void findCPDecisionsBetweenDates() {
        LocalDate startDate = new LocalDate();
        LocalDate endDate = new LocalDate();

        //setup
        Product product = new Product();
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(product);
        List expectedResults = asList(new CPDecisionBAROutput(), new CPDecisionBAROutput());
        when(crudService.findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN, CPDecisionBAROutput.params(product, startDate, endDate))).thenReturn(expectedResults);

        //when
        List<CPDecisionBAROutput> result = service.findCPDecisionsBetweenDates(startDate, endDate);

        //then
        assertEquals(expectedResults, result);
    }

    @Test
    public void findCPDecisionsBetweenDatesForAccomTypes() {
        LocalDate startDate = new LocalDate();
        LocalDate endDate = new LocalDate();
        List<AccomType> accomTypes = asList(new AccomType(), new AccomType());

        //setup
        List expectedResults = asList(new CPDecisionBAROutput(), new CPDecisionBAROutput());
        Product product = new Product();
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(product);
        when(crudService.findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN_FOR_ACCOM_TYPES, CPDecisionBAROutput.params(product, startDate, endDate, accomTypes))).thenReturn(expectedResults);

        //when
        List<CPDecisionBAROutput> result = service.findCPDecisionsBetweenDatesForAccomTypes(startDate, endDate, accomTypes);

        //then
        assertEquals(expectedResults, result);
    }

    @Test
    public void findCPDecisionsBetweenDatesForAccomTypes_systemDefaultProductOnly() {
        LocalDate startDate = new LocalDate();
        LocalDate endDate = new LocalDate();
        List<AccomType> accomTypes = asList(new AccomType(), new AccomType());

        Product defaultProduct = new Product();
        defaultProduct.setSystemDefault(true);
        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setProduct(defaultProduct);
        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        cpDecisionBAROutput2.setProduct(defaultProduct);

        List expectedResults = asList(cpDecisionBAROutput1, cpDecisionBAROutput2);
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(defaultProduct);
        when(crudService.findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN_FOR_ACCOM_TYPES, CPDecisionBAROutput.params(defaultProduct, startDate, endDate, accomTypes))).thenReturn(expectedResults);

        List<CPDecisionBAROutput> result = service.findCPDecisionsBetweenDatesForAccomTypes(startDate, endDate, accomTypes);

        assertEquals(expectedResults, result);
        assertTrue(result.get(0).getProduct().isSystemDefault());
        assertTrue(result.get(1).getProduct().isSystemDefault());
        verify(crudService, times(1).description("Central RMS Expects this method to return CPDecisionBAROutput records for default system product only")).findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT);
        verify(crudService).findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN_FOR_ACCOM_TYPES, CPDecisionBAROutput.params(defaultProduct, startDate, endDate, accomTypes));
    }

    @Test
    public void findCPDecisionsBetweenDatesForAccomTypeIds() {
        LocalDate startDate = new LocalDate();
        LocalDate endDate = new LocalDate();
        List<Integer> accomTypeIds = asList(1, 2);
        AccomType accomType1 = new AccomType();
        AccomType accomType2 = new AccomType();
        List<AccomType> accomTypes = asList(accomType1, accomType2);

        //setup
        List expectedResults = asList(new CPDecisionBAROutput(), new CPDecisionBAROutput());
        when(crudService.find(AccomType.class, 1)).thenReturn(accomType1);
        when(crudService.find(AccomType.class, 2)).thenReturn(accomType2);

        Product product = new Product();
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(product);

        when(crudService.findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN_FOR_ACCOM_TYPES, CPDecisionBAROutput.params(product, startDate, endDate, accomTypes))).thenReturn(expectedResults);

        //when
        List<CPDecisionBAROutput> result = service.findCPDecisionsBetweenDatesForAccomTypeIds(startDate, endDate, accomTypeIds);

        //then
        assertEquals(expectedResults, result);
    }

    @Test
    public void findCPDecisionsBetweenDatesForAccomTypeIdsMap() {
        LocalDate startDate = new LocalDate();
        LocalDate endDate = new LocalDate();
        List<Integer> accomTypeIds = asList(1, 2);
        AccomType accomType1 = new AccomType();
        accomType1.setId(1);
        AccomType accomType2 = new AccomType();
        accomType2.setId(2);
        List<AccomType> accomTypes = asList(accomType1, accomType2);

        //setup
        CPDecisionBAROutput cpDecisionBAROutput1 = new CPDecisionBAROutput();
        cpDecisionBAROutput1.setAccomType(accomType1);
        CPDecisionBAROutput cpDecisionBAROutput2 = new CPDecisionBAROutput();
        cpDecisionBAROutput2.setAccomType(accomType2);
        List decisionResults = asList(cpDecisionBAROutput1, cpDecisionBAROutput2);
        when(crudService.find(AccomType.class, 1)).thenReturn(accomType1);
        when(crudService.find(AccomType.class, 2)).thenReturn(accomType2);

        Product product = new Product();
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(product);

        when(crudService.findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN_FOR_ACCOM_TYPES, CPDecisionBAROutput.params(product, startDate, endDate, accomTypes))).thenReturn(decisionResults);

        //when
        Map<Integer, CPDecisionBAROutput> result = service.findCPDecisionsBetweenDatesForAccomTypeIdsMap(startDate, endDate, accomTypeIds);

        //then
        assertEquals(2, result.keySet().size());
        assertEquals(cpDecisionBAROutput1, result.get(1));
        assertEquals(cpDecisionBAROutput2, result.get(2));
    }

    @Test
    public void findCPDecisionsBetweenDatesForAccomTypeIdsForArrivalDates() {
        LocalDate startDate = new LocalDate();
        LocalDate endDate = new LocalDate();

        AccomType accomType1 = new AccomType();
        accomType1.setId(1);
        AccomType accomType2 = new AccomType();
        accomType2.setId(2);

        CPDecisionBAROutput cpDecisionBAROutput1 = getCPDecisionBAROutput(startDate, accomType1);
        CPDecisionBAROutput cpDecisionBAROutput2 = getCPDecisionBAROutput(startDate, accomType2);
        CPDecisionBAROutput cpDecisionBAROutput3 = getCPDecisionBAROutput(startDate.plusDays(2), accomType1);
        CPDecisionBAROutput cpDecisionBAROutput4 = getCPDecisionBAROutput(startDate.plusDays(2), accomType2);
        CPDecisionBAROutput cpDecisionBAROutput5 = getCPDecisionBAROutput(endDate.plusMonths(1), accomType2);
        List decisionResults = asList(cpDecisionBAROutput1, cpDecisionBAROutput2, cpDecisionBAROutput3, cpDecisionBAROutput4, cpDecisionBAROutput5);

        Product product = new Product();
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(product);

        when(crudService.findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN, CPDecisionBAROutput.params(product, startDate, endDate))).thenReturn(decisionResults);

        Map<LocalDate, Map<Integer, CPDecisionBAROutput>> result = service.findCPDecisionsBetweenDatesForAccomTypeIdsMap(startDate, endDate);
        assertEquals(3, result.size());

        Map<Integer, CPDecisionBAROutput> data1 = result.get(startDate);
        assertEquals(2, data1.size());
        assertEquals(cpDecisionBAROutput1, data1.get(1));
        assertEquals(cpDecisionBAROutput2, data1.get(2));

        Map<Integer, CPDecisionBAROutput> data2 = result.get(startDate.plusDays(2));
        assertEquals(2, data2.size());
        assertEquals(cpDecisionBAROutput3, data2.get(1));
        assertEquals(cpDecisionBAROutput4, data2.get(2));

        Map<Integer, CPDecisionBAROutput> data3 = result.get(endDate.plusMonths(1));
        assertEquals(1, data3.size());
        assertEquals(cpDecisionBAROutput5, data3.get(2));
    }

    private CPDecisionBAROutput getCPDecisionBAROutput(LocalDate arrivalDate, AccomType accomType) {
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        cpDecisionBAROutput.setAccomType(accomType);
        cpDecisionBAROutput.setArrivalDate(arrivalDate);
        return cpDecisionBAROutput;
    }

    @Test
    public void findCPDecisionForDateNotMatchingPrices() {
        LocalDate startDate = new LocalDate();
        LocalDate endDate = new LocalDate();
        AccomType accomType1 = new AccomType();

        //setup
        CPDecisionBAROutput expectedResults = new CPDecisionBAROutput();
        when(crudService.find(AccomType.class, 1)).thenReturn(accomType1);
        Product product = new Product();
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(product);
        when(crudService.findByNamedQuerySingleResult(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN_FOR_ACCOM_TYPES, CPDecisionBAROutput.params(product, startDate, endDate, Arrays.asList(accomType1)))).thenReturn(expectedResults);

        //when
        CPDecisionBAROutput result = service.findCPDecisionBetweenDatesForAccomTypeId(startDate, endDate, 1);

        //then
        assertEquals(expectedResults, result);
    }

    @Test
    public void findCurrentCPDecisionsForProperty() {
        //setup
        Date caughtUpDate = new Date();
        when(dateService.getCaughtUpDate()).thenReturn(caughtUpDate);
        Product product = new Product();
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(product);
        List expectedResults = asList(new CPDecisionBAROutput(), new CPDecisionBAROutput());
        when(crudService.findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_FOR_PROPERTY_BY_DATE, CPDecisionBAROutput.params(product, new LocalDate(caughtUpDate)), 0, 200)).thenReturn(expectedResults);

        //when
        List<CPDecisionBAROutput> result = service.findCurrentCPDecisionsForProperty(12345, 0, 200);

        //then
        assertEquals(expectedResults, result);
    }

    @Test
    public void saveOverrideForCPDecision_containsSpecificOverride() {
        //setup
        BigDecimal overridePrice = BigDecimal.valueOf(459);
        BigDecimal floorOverridePrice = BigDecimal.valueOf(100);
        BigDecimal ceilingOverridePrice = BigDecimal.valueOf(1000);
        BigDecimal originalPrice = BigDecimal.valueOf(200);
        CPDecisionBAROutputOverride override = new CPDecisionBAROutputOverride();
        override.setAccomType(buildAccomType());
        override.setNewUserOverride(overridePrice);
        override.setNewFloorRate(floorOverridePrice);
        override.setNewCeilingRate(ceilingOverridePrice);
        override.setNewOverrideType(DecisionOverrideType.CEIL);
        CPDecisionBAROutput output = new CPDecisionBAROutput();
        output.setAccomType(buildAccomType());
        output.setFinalBAR(originalPrice);
        Decision barOverrideDecision = new Decision();
        barOverrideDecision.setId(2);
        Mockito.when(decisionService.createBAROverrideDecision()).thenReturn(barOverrideDecision);
        AccomTypeSupplementValue supplement = new AccomTypeSupplementValue();
        when(cpDecisionContext.getSupplementFor(output)).thenReturn(Optional.of(supplement));

        //when
        service.saveOverrideForCPDecision(cpDecisionContext, override, output, false);

        //then
        assertEquals(overridePrice, output.getPrettyBAR());
        assertEquals(overridePrice, output.getFinalBAR());
        assertEquals(overridePrice, output.getSpecificOverride());
        assertEquals(floorOverridePrice, output.getFloorOverride());
        assertEquals(ceilingOverridePrice, output.getCeilingOverride());
        assertEquals(DecisionOverrideType.CEIL, output.getOverrideType());
        assertEquals(barOverrideDecision.getId(), output.getDecisionId());
        assertEquals(barOverrideDecision.getId(), override.getDecisionId());
        verify(crudService).save(override);
        verify(crudService).save(output);
    }

    @Test
    public void saveOverrideForCPDecision_containsSpecificOverrideWithSupplementPercent() {
        //setup
        BigDecimal overridePrice = BigDecimal.valueOf(459);
        BigDecimal floorOverridePrice = BigDecimal.valueOf(100);
        BigDecimal ceilingOverridePrice = BigDecimal.valueOf(1000);
        BigDecimal originalPrice = BigDecimal.valueOf(200);
        CPDecisionBAROutputOverride override = new CPDecisionBAROutputOverride();
        override.setAccomType(buildAccomType());
        override.setNewUserOverride(overridePrice);
        override.setNewFloorRate(floorOverridePrice);
        override.setNewCeilingRate(ceilingOverridePrice);
        override.setNewOverrideType(DecisionOverrideType.CEIL);
        CPDecisionBAROutput output = new CPDecisionBAROutput();
        output.setAccomType(buildAccomType());
        output.setFinalBAR(originalPrice);
        Decision barOverrideDecision = new Decision();
        barOverrideDecision.setId(2);
        Mockito.when(decisionService.createBAROverrideDecision()).thenReturn(barOverrideDecision);
        AccomTypeSupplementValue supplement = new AccomTypeSupplementValue();
        supplement.setValue(BigDecimal.TEN);
        supplement.setOffsetMethod(OffsetMethod.PERCENTAGE);
        when(cpDecisionContext.getSupplementFor(output)).thenReturn(Optional.of(supplement));

        //when
        service.saveOverrideForCPDecision(cpDecisionContext, override, output, false);

        //then
        assertEquals(Supplement.addSupplementTo(overridePrice, supplement), output.getPrettyBAR());
        assertEquals(Supplement.addSupplementTo(overridePrice, supplement), output.getFinalBAR());
        assertEquals(Supplement.addSupplementTo(overridePrice, supplement), output.getSpecificOverride());
        assertEquals(Supplement.addSupplementTo(floorOverridePrice, supplement), output.getFloorOverride());
        assertEquals(Supplement.addSupplementTo(ceilingOverridePrice, supplement), output.getCeilingOverride());
        assertEquals(DecisionOverrideType.CEIL, output.getOverrideType());
        assertEquals(barOverrideDecision.getId(), output.getDecisionId());
        assertEquals(barOverrideDecision.getId(), override.getDecisionId());
        verify(crudService).save(override);
        verify(crudService).save(output);
    }

    @Test
    public void saveOverrideForCPDecision_containsSpecificOverrideWithSupplementFixed() {
        //setup
        BigDecimal overridePrice = BigDecimal.valueOf(459);
        BigDecimal floorOverridePrice = BigDecimal.valueOf(100);
        BigDecimal ceilingOverridePrice = BigDecimal.valueOf(1000);
        BigDecimal originalPrice = BigDecimal.valueOf(200);
        CPDecisionBAROutputOverride override = new CPDecisionBAROutputOverride();
        override.setAccomType(buildAccomType());
        override.setNewUserOverride(overridePrice);
        override.setNewFloorRate(floorOverridePrice);
        override.setNewCeilingRate(ceilingOverridePrice);
        override.setNewOverrideType(DecisionOverrideType.CEIL);
        CPDecisionBAROutput output = new CPDecisionBAROutput();
        output.setAccomType(buildAccomType());
        output.setFinalBAR(originalPrice);
        Decision barOverrideDecision = new Decision();
        barOverrideDecision.setId(2);
        Mockito.when(decisionService.createBAROverrideDecision()).thenReturn(barOverrideDecision);
        AccomTypeSupplementValue supplement = new AccomTypeSupplementValue();
        supplement.setValue(BigDecimal.TEN);
        supplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        when(cpDecisionContext.getSupplementFor(output)).thenReturn(Optional.of(supplement));

        //when
        service.saveOverrideForCPDecision(cpDecisionContext, override, output, false);

        //then
        assertEquals(Supplement.addSupplementTo(overridePrice, supplement), output.getPrettyBAR());
        assertEquals(Supplement.addSupplementTo(overridePrice, supplement), output.getFinalBAR());
        assertEquals(Supplement.addSupplementTo(overridePrice, supplement), output.getSpecificOverride());
        assertEquals(Supplement.addSupplementTo(floorOverridePrice, supplement), output.getFloorOverride());
        assertEquals(Supplement.addSupplementTo(ceilingOverridePrice, supplement), output.getCeilingOverride());
        assertEquals(DecisionOverrideType.CEIL, output.getOverrideType());
        assertEquals(barOverrideDecision.getId(), output.getDecisionId());
        assertEquals(barOverrideDecision.getId(), override.getDecisionId());
        verify(crudService).save(override);
        verify(crudService).save(output);
    }

    @Test
    public void saveOverrideForCPDecision_cascadeOverrides() {
        //setup
        BigDecimal overridePrice = BigDecimal.valueOf(459);
        BigDecimal originalPrice = BigDecimal.valueOf(200);
        CPDecisionBAROutputOverride override = new CPDecisionBAROutputOverride();
        override.setNewUserOverride(overridePrice);
        override.setNewOverrideType(DecisionOverrideType.CEIL);
        CPDecisionBAROutput output = new CPDecisionBAROutput();
        output.setFinalBAR(originalPrice);
        LocalDate arrivalDate = new LocalDate();
        output.setArrivalDate(arrivalDate);
        AccomType baseRoomType = new AccomType();
        AccomClass baseRoomClass = new AccomClass();
        baseRoomType.setAccomClass(baseRoomClass);
        output.setAccomType(baseRoomType);

        Decision barOverrideDecision = new Decision();
        barOverrideDecision.setId(2);
        Mockito.when(decisionService.createBAROverrideDecision()).thenReturn(barOverrideDecision);

        CPDecisionBAROutput otherDecision1 = new CPDecisionBAROutput();
        AccomType otherDecision1RoomType = new AccomType();
        AccomClass otherDecision1RoomClass = new AccomClass();
        otherDecision1RoomType.setAccomClass(otherDecision1RoomClass);
        otherDecision1.setAccomType(otherDecision1RoomType);
        CPDecisionBAROutput otherDecision2 = new CPDecisionBAROutput();
        AccomType otherDecision2RoomType = new AccomType();
        otherDecision2RoomType.setId(123);
        otherDecision2RoomType.setAccomClass(baseRoomClass);
        otherDecision2.setAccomType(otherDecision2RoomType);
        otherDecision2.setOptimalBAR(BigDecimal.valueOf(100));
        otherDecision2.setPropertyId(3);
        otherDecision2.setDecisionId(4);
        otherDecision2.setArrivalDate(arrivalDate);
        otherDecision2.setProduct(new Product());
        otherDecision2.setOverrideType(DecisionOverrideType.CEIL);
        otherDecision2.setSpecificOverride(BigDecimal.valueOf(35));

        Product product = new Product();
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(product);
        when(pricingConfigurationService.getCPDecisionContext(arrivalDate, arrivalDate)).thenReturn(cpDecisionContext);

        List<CPDecisionBAROutput> decisionsForDay = asList(output, otherDecision1, otherDecision2);
        when(crudService.<CPDecisionBAROutput>findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN, CPDecisionBAROutput.params(product, arrivalDate, arrivalDate)))
                .thenReturn(decisionsForDay);

        CPConfigMergedOffset offset1 = new CPConfigMergedOffset();
        offset1.setId(new CPConfigMergedOffsetPK());
        CPConfigMergedOffset offset2 = new CPConfigMergedOffset();
        CPConfigMergedOffsetPK id = new CPConfigMergedOffsetPK();
        id.setAccomTypeId(123);
        id.setOccupancyType(OccupancyType.SINGLE);
        offset2.setId(id);
        offset2.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        offset2.setOffsetValue(BigDecimal.valueOf(20));
        List<CPConfigMergedOffset> offsets = asList(offset1, offset2);
        when(cpDecisionContext.applyOffset(otherDecision2, override.getNewUserOverride())).thenReturn(BigDecimalUtil.add(override.getNewUserOverride(), offset2.getOffsetValue()));
        AccomTypeSupplementValue supplement = new AccomTypeSupplementValue();
        supplement.setValue(BigDecimal.ZERO);
        supplement.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        when(cpDecisionContext.getSupplementFor(any(CPDecisionBAROutput.class))).thenReturn(Optional.of(supplement));

        //when
        service.saveOverrideForCPDecision(cpDecisionContext, override, output, true);

        //then
        assertEquals(overridePrice, output.getPrettyBAR());
        assertEquals(overridePrice, output.getFinalBAR());
        assertEquals(overridePrice, output.getSpecificOverride());
        assertEquals(DecisionOverrideType.CEIL, output.getOverrideType());
        assertEquals(barOverrideDecision.getId(), output.getDecisionId());
        assertEquals(barOverrideDecision.getId(), override.getDecisionId());
        ArgumentCaptor<IdAware> captor = ArgumentCaptor.forClass(IdAware.class);
        verify(crudService, times(4)).save(captor.capture());

        List<IdAware> allValues = captor.getAllValues();
        assertEquals(output, allValues.get(0));
        assertEquals(override, allValues.get(1));
        assertEquals(otherDecision2, allValues.get(2));
        CPDecisionBAROutputOverride otherOverride = (CPDecisionBAROutputOverride) allValues.get(3);

        assertEquals(overridePrice.add(offset2.getOffsetValue().setScale(2)), otherOverride.getNewUserOverride().setScale(2));
        assertEquals(DecisionOverrideType.CEIL, otherOverride.getNewOverrideType());
        assertEquals(barOverrideDecision.getId(), otherOverride.getDecisionId());

        assertEquals(overridePrice.add(offset2.getOffsetValue().setScale(2)), otherDecision2.getPrettyBAR().setScale(2));
        assertEquals(overridePrice.add(offset2.getOffsetValue().setScale(2)), otherDecision2.getFinalBAR().setScale(2));
        assertEquals(overridePrice.add(offset2.getOffsetValue().setScale(2)), otherDecision2.getSpecificOverride().setScale(2));
        assertEquals(DecisionOverrideType.CEIL, otherDecision2.getOverrideType());
    }

    @Test
    public void saveOverrideForCPDecision_noSpecificOverride() {
        //setup
        BigDecimal floorOverridePrice = BigDecimal.valueOf(100);
        BigDecimal ceilingOverridePrice = BigDecimal.valueOf(1000);
        BigDecimal originalPrice = BigDecimal.valueOf(200);
        BigDecimal optimalPrice = BigDecimal.valueOf(210);
        BigDecimal prettiedPrice = BigDecimal.valueOf(300);
        BigDecimal oldCeilingOverride = BigDecimal.valueOf(500);
        BigDecimal oldFloorOverride = BigDecimal.valueOf(150);
        CPDecisionBAROutputOverride override = new CPDecisionBAROutputOverride();
        override.setNewFloorRate(floorOverridePrice);
        override.setNewCeilingRate(ceilingOverridePrice);
        override.setNewOverrideType(DecisionOverrideType.CEIL);
        CPDecisionBAROutput output = new CPDecisionBAROutput();
        output.setFinalBAR(originalPrice);
        output.setOptimalBAR(optimalPrice);
        output.setCeilingOverride(oldCeilingOverride);
        output.setFloorOverride(oldFloorOverride);
        output.setPropertyId(1);
        output.setAccomType(new AccomType());

        Decision barOverrideDecision = new Decision();
        barOverrideDecision.setId(2);
        Mockito.when(decisionService.createBAROverrideDecision()).thenReturn(barOverrideDecision);

        Mockito.when(cpDecisionContext.calculateRoundedRate(output)).thenReturn(prettiedPrice);
        AccomTypeSupplementValue supplement = new AccomTypeSupplementValue();
        when(cpDecisionContext.getSupplementFor(output)).thenReturn(Optional.of(supplement));

        //when
        service.saveOverrideForCPDecision(cpDecisionContext, override, output, false);

        //then
        assertEquals(prettiedPrice, output.getPrettyBAR());
        assertEquals(prettiedPrice, output.getFinalBAR());
        assertNull(output.getSpecificOverride());
        assertEquals(floorOverridePrice, output.getFloorOverride());
        assertEquals(ceilingOverridePrice, output.getCeilingOverride());
        assertEquals(DecisionOverrideType.CEIL, output.getOverrideType());
        assertEquals(barOverrideDecision.getId(), output.getDecisionId());
        verify(crudService).save(override);
        verify(crudService).save(output);
    }

    @Test
    public void saveOverrideForCPDecision_duplicateKeyExceptionFix() {
        //setup
        BigDecimal floorOverridePrice = BigDecimal.valueOf(100);
        BigDecimal ceilingOverridePrice = BigDecimal.valueOf(1000);
        BigDecimal originalPrice = BigDecimal.valueOf(200);
        BigDecimal optimalPrice = BigDecimal.valueOf(210);
        BigDecimal prettiedPrice = BigDecimal.valueOf(300);
        BigDecimal oldCeilingOverride = BigDecimal.valueOf(500);
        BigDecimal oldFloorOverride = BigDecimal.valueOf(150);
        CPDecisionBAROutputOverride override = new CPDecisionBAROutputOverride();
        override.setNewFloorRate(floorOverridePrice);
        override.setNewCeilingRate(ceilingOverridePrice);
        override.setNewOverrideType(DecisionOverrideType.CEIL);
        CPDecisionBAROutput output = new CPDecisionBAROutput();
        output.setFinalBAR(originalPrice);
        output.setOptimalBAR(optimalPrice);
        output.setCeilingOverride(oldCeilingOverride);
        output.setFloorOverride(oldFloorOverride);
        output.setPropertyId(1);
        output.setAccomType(new AccomType());

        Decision barOverrideDecision = new Decision();
        barOverrideDecision.setId(2);
        Mockito.when(decisionService.createBAROverrideDecision()).thenReturn(barOverrideDecision);

        CPDecisionBAROutput newCPDecisionBarOutput = new CPDecisionBAROutput();
        newCPDecisionBarOutput.setId(Long.valueOf(10));
        when(crudService.findByNamedQuerySingleResult(CPDecisionBAROutput.GET_DECISION_BAR_OUTPUT,
                CPDecisionBAROutput.params(output.getProduct(), output.getArrivalDate(), output.getAccomType()))).thenReturn(newCPDecisionBarOutput);
        Mockito.when(cpDecisionContext.calculateRoundedRate(output)).thenReturn(prettiedPrice);
        AccomTypeSupplementValue supplement = new AccomTypeSupplementValue();
        when(cpDecisionContext.getSupplementFor(output)).thenReturn(Optional.of(supplement));

        //when
        service.saveOverrideForCPDecision(cpDecisionContext, override, output, false);

        //then
        assertEquals(newCPDecisionBarOutput.getId(), output.getId());
        assertEquals(prettiedPrice, output.getPrettyBAR());
        assertEquals(prettiedPrice, output.getFinalBAR());
        assertNull(output.getSpecificOverride());
        assertEquals(floorOverridePrice, output.getFloorOverride());
        assertEquals(ceilingOverridePrice, output.getCeilingOverride());
        assertEquals(DecisionOverrideType.CEIL, output.getOverrideType());
        assertEquals(barOverrideDecision.getId(), output.getDecisionId());
        verify(crudService).save(override);
        verify(crudService).save(output);
    }

    @Test
    public void removeOverrideWithSupplementPercentage() {
        AccomTypeSupplementValue supplementValue = new AccomTypeSupplementValue();
        supplementValue.setValue(BigDecimal.TEN);
        supplementValue.setOffsetMethod(OffsetMethod.PERCENTAGE);

        CPDecisionBAROutput decisionBARCPOutput = new CPDecisionBAROutput();
        decisionBARCPOutput.setOptimalBAR(BigDecimal.valueOf(150));
        decisionBARCPOutput.setPropertyId(1);
        decisionBARCPOutput.setDecisionId(2);
        decisionBARCPOutput.setAccomType(new AccomType());
        decisionBARCPOutput.setArrivalDate(new LocalDate());
        decisionBARCPOutput.setProduct(new Product());
        decisionBARCPOutput.setOverrideType(DecisionOverrideType.CEIL);
        decisionBARCPOutput.setSpecificOverride(BigDecimal.valueOf(45));
        decisionBARCPOutput.setCeilingOverride(BigDecimal.valueOf(46));
        decisionBARCPOutput.setFloorOverride(BigDecimal.valueOf(47));
        decisionBARCPOutput.setLengthOfStay(1);

        when(cpDecisionContext.getSupplementFor(decisionBARCPOutput)).thenReturn(Optional.of(supplementValue));

        Mockito.when(cpDecisionContext.calculateRoundedRate(decisionBARCPOutput)).thenReturn(BigDecimal.valueOf(200));

        PricingRule pricingRule = new PricingRule();
        expectFloorCeiling(pricingRule, decisionBARCPOutput, new BigDecimal("10"), new BigDecimal("200"), supplementValue.getValue());

        service.removeOverride(cpDecisionContext, decisionBARCPOutput, 1, 12, false, true, true);

        assertNull(decisionBARCPOutput.getSpecificOverride());
        assertNull(decisionBARCPOutput.getFloorOverride());
        assertNull(decisionBARCPOutput.getCeilingOverride());
        assertEquals(BigDecimal.valueOf(200), decisionBARCPOutput.getFinalBAR());

        //Casting is necessary here to appease the compiler
        verify(crudService, times(2)).save((CPDecisionBAROutputOverride) overrideCaptor.capture());
        verify(cpDecisionContext).getSupplementFor(decisionBARCPOutput);
        verify(syncEventAggregatorService, times(1)).registerSyncEvent(SyncEvent.CONTINUOUS_PRICING_SPECIFIC_OVERRIDE_REMOVED);
        verify(syncEventAggregatorService, times(1)).registerSyncEvent(SyncEvent.CONTINUOUS_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        List<Object> values = overrideCaptor.getAllValues();

        assertEquals(decisionBARCPOutput, values.get(0));

        CPDecisionBAROutputOverride override = (CPDecisionBAROutputOverride) values.get(1);

        assertEquals(Integer.valueOf(1), override.getDecisionId());
        assertEquals(override.getDecisionId(), decisionBARCPOutput.getDecisionId());
        assertEquals(DecisionOverrideType.CEIL, override.getOldOverrideType());
        assertEquals(DecisionOverrideType.PENDING, override.getNewOverrideType());
        assertEquals(Supplement.removeSupplementFrom(BigDecimal.valueOf(45), supplementValue), override.getOldUserOverride());
        assertNull(override.getNewUserOverride());
        assertEquals(Supplement.removeSupplementFrom(BigDecimal.valueOf(47), supplementValue), override.getOldFloorRate());
        assertNull(override.getNewFloorRate());
        assertEquals(Supplement.removeSupplementFrom(BigDecimal.valueOf(46), supplementValue), override.getOldCeilingRate());
        assertNull(override.getNewCeilingRate());
        assertEquals(decisionBARCPOutput.getPropertyId(), override.getPropertyId());
        assertEquals(decisionBARCPOutput.getDecisionId(), override.getDecisionId());
        assertEquals(decisionBARCPOutput.getAccomType(), override.getAccomType());
        assertEquals(decisionBARCPOutput.getArrivalDate(), override.getArrivalDate());
        assertEquals(decisionBARCPOutput.getLengthOfStay(), override.getLengthOfStay());
        assertEquals(decisionBARCPOutput.getProduct(), override.getProduct());
        assertEquals((Integer) 12, override.getUser());
    }

    @Test
    public void removeOverrideWithSupplementFixed() {
        AccomTypeSupplementValue supplementValue = new AccomTypeSupplementValue();
        supplementValue.setValue(BigDecimal.TEN);
        supplementValue.setOffsetMethod(OffsetMethod.FIXED_OFFSET);

        CPDecisionBAROutput decisionBARCPOutput = new CPDecisionBAROutput();
        decisionBARCPOutput.setOptimalBAR(BigDecimal.valueOf(150));
        decisionBARCPOutput.setPropertyId(1);
        decisionBARCPOutput.setDecisionId(2);
        decisionBARCPOutput.setAccomType(new AccomType());
        decisionBARCPOutput.setArrivalDate(new LocalDate());
        decisionBARCPOutput.setProduct(new Product());
        decisionBARCPOutput.setOverrideType(DecisionOverrideType.CEIL);
        decisionBARCPOutput.setSpecificOverride(BigDecimal.valueOf(45));
        decisionBARCPOutput.setCeilingOverride(BigDecimal.valueOf(46));
        decisionBARCPOutput.setFloorOverride(BigDecimal.valueOf(47));
        decisionBARCPOutput.setLengthOfStay(1);

        when(cpDecisionContext.getSupplementFor(decisionBARCPOutput)).thenReturn(Optional.of(supplementValue));

        Mockito.when(cpDecisionContext.calculateRoundedRate(decisionBARCPOutput)).thenReturn(BigDecimal.valueOf(200));

        PricingRule pricingRule = new PricingRule();
        expectFloorCeiling(pricingRule, decisionBARCPOutput, new BigDecimal("10"), new BigDecimal("200"), supplementValue.getValue());

        service.removeOverride(cpDecisionContext, decisionBARCPOutput, 1, 12, false, true, true);

        assertNull(decisionBARCPOutput.getSpecificOverride());
        assertNull(decisionBARCPOutput.getFloorOverride());
        assertNull(decisionBARCPOutput.getCeilingOverride());
        assertEquals(BigDecimal.valueOf(200), decisionBARCPOutput.getFinalBAR());

        //Casting is necessary here to appease the compiler
        verify(crudService, times(2)).save((CPDecisionBAROutputOverride) overrideCaptor.capture());
        verify(cpDecisionContext).getSupplementFor(decisionBARCPOutput);
        verify(syncEventAggregatorService, times(1)).registerSyncEvent(SyncEvent.CONTINUOUS_PRICING_SPECIFIC_OVERRIDE_REMOVED);
        verify(syncEventAggregatorService, times(1)).registerSyncEvent(SyncEvent.CONTINUOUS_PRICING_FLOOR_CEILING_OVERRIDE_CHANGED);
        List<Object> values = overrideCaptor.getAllValues();

        assertEquals(decisionBARCPOutput, values.get(0));

        CPDecisionBAROutputOverride override = (CPDecisionBAROutputOverride) values.get(1);

        assertEquals(Integer.valueOf(1), override.getDecisionId());
        assertEquals(override.getDecisionId(), decisionBARCPOutput.getDecisionId());
        assertEquals(DecisionOverrideType.CEIL, override.getOldOverrideType());
        assertEquals(DecisionOverrideType.PENDING, override.getNewOverrideType());
        assertEquals(Supplement.removeSupplementFrom(BigDecimal.valueOf(45), supplementValue), override.getOldUserOverride());
        assertNull(override.getNewUserOverride());
        assertEquals(Supplement.removeSupplementFrom(BigDecimal.valueOf(47), supplementValue), override.getOldFloorRate());
        assertNull(override.getNewFloorRate());
        assertEquals(Supplement.removeSupplementFrom(BigDecimal.valueOf(46), supplementValue), override.getOldCeilingRate());
        assertNull(override.getNewCeilingRate());
        assertEquals(decisionBARCPOutput.getPropertyId(), override.getPropertyId());
        assertEquals(decisionBARCPOutput.getDecisionId(), override.getDecisionId());
        assertEquals(decisionBARCPOutput.getAccomType(), override.getAccomType());
        assertEquals(decisionBARCPOutput.getArrivalDate(), override.getArrivalDate());
        assertEquals(decisionBARCPOutput.getLengthOfStay(), override.getLengthOfStay());
        assertEquals(decisionBARCPOutput.getProduct(), override.getProduct());
        assertEquals((Integer) 12, override.getUser());
    }

    @Test
    public void removeOverride_cascade() {
        CPDecisionBAROutput decisionBARCPOutput = new CPDecisionBAROutput();
        decisionBARCPOutput.setOptimalBAR(BigDecimal.valueOf(150));
        decisionBARCPOutput.setPropertyId(1);
        decisionBARCPOutput.setDecisionId(2);
        AccomType baseRoomType = new AccomType();
        AccomClass baseRoomClass = new AccomClass();
        baseRoomType.setAccomClass(baseRoomClass);
        decisionBARCPOutput.setAccomType(baseRoomType);
        LocalDate arrivalDate = new LocalDate();
        decisionBARCPOutput.setArrivalDate(arrivalDate);
        decisionBARCPOutput.setProduct(new Product());
        decisionBARCPOutput.setOverrideType(DecisionOverrideType.CEIL);
        decisionBARCPOutput.setSpecificOverride(BigDecimal.valueOf(45));
        decisionBARCPOutput.setCeilingOverride(BigDecimal.valueOf(46));
        decisionBARCPOutput.setFloorOverride(BigDecimal.valueOf(47));

        PricingRule pricingRule = new PricingRule();
        when(cpDecisionContext.isSupplementPercentEnabled()).thenReturn(true);
        Mockito.when(cpDecisionContext.calculateRoundedRate(decisionBARCPOutput)).thenReturn(BigDecimal.valueOf(190));

        CPDecisionBAROutput otherDecision1 = new CPDecisionBAROutput();
        AccomType otherDecision1RoomType = new AccomType();
        AccomClass otherDecision1RoomClass = new AccomClass();
        otherDecision1RoomType.setAccomClass(otherDecision1RoomClass);
        otherDecision1.setAccomType(otherDecision1RoomType);
        CPDecisionBAROutput otherDecision2 = new CPDecisionBAROutput();
        AccomType otherDecision2RoomType = new AccomType();
        otherDecision2RoomType.setAccomClass(baseRoomClass);
        otherDecision2.setAccomType(otherDecision2RoomType);
        otherDecision2.setOptimalBAR(BigDecimal.valueOf(100));
        otherDecision2.setPropertyId(3);
        otherDecision2.setDecisionId(4);
        otherDecision2.setArrivalDate(arrivalDate);
        otherDecision2.setProduct(new Product());
        otherDecision2.setOverrideType(DecisionOverrideType.CEIL);

        AccomTypeSupplementValue supplementValue = new AccomTypeSupplementValue();
        supplementValue.setValue(BigDecimal.valueOf(10));
        supplementValue.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        when(cpDecisionContext.getSupplementFor(any(CPDecisionBAROutput.class))).thenReturn(Optional.of(supplementValue));

        otherDecision2.setSpecificOverride(BigDecimal.valueOf(35));
        otherDecision2.setCeilingOverride(BigDecimal.valueOf(36));
        otherDecision2.setFloorOverride(BigDecimal.valueOf(37));

        Product product = new Product();
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(product);

        List<CPDecisionBAROutput> decisionsForDay = asList(decisionBARCPOutput, otherDecision1, otherDecision2);
        when(crudService.<CPDecisionBAROutput>findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN, CPDecisionBAROutput.params(product, arrivalDate, arrivalDate)))
                .thenReturn(decisionsForDay);

        expectFloorCeiling(pricingRule, decisionBARCPOutput, new BigDecimal("10"), new BigDecimal("200"), supplementValue.getValue());

        //when
        service.removeOverride(cpDecisionContext, decisionBARCPOutput, 1, 12, true, true, true);

        //then
        assertNull(decisionBARCPOutput.getSpecificOverride());
        assertNull(decisionBARCPOutput.getFloorOverride());
        assertNull(decisionBARCPOutput.getCeilingOverride());
        assertEquals(BigDecimal.valueOf(190), decisionBARCPOutput.getFinalBAR());
        assertEquals(Integer.valueOf(1), decisionBARCPOutput.getDecisionId());

        //Casting is necessary here to appease the compiler
        verify(crudService, times(4)).save((CPDecisionBAROutputOverride) overrideCaptor.capture());
        verify(cpDecisionContext).getSupplementFor(decisionBARCPOutput);

        List<Object> values = overrideCaptor.getAllValues();

        assertEquals(decisionBARCPOutput, values.get(0));

        CPDecisionBAROutputOverride override = (CPDecisionBAROutputOverride) values.get(1);

        assertEquals(DecisionOverrideType.CEIL, override.getOldOverrideType());
        assertEquals(DecisionOverrideType.PENDING, override.getNewOverrideType());
        assertEquals(Supplement.removeSupplementFrom(BigDecimal.valueOf(45), supplementValue), override.getOldUserOverride());
        assertNull(override.getNewUserOverride());
        assertEquals(Supplement.removeSupplementFrom(BigDecimal.valueOf(47), supplementValue), override.getOldFloorRate());
        assertNull(override.getNewFloorRate());
        assertEquals(Supplement.removeSupplementFrom(BigDecimal.valueOf(46), supplementValue), override.getOldCeilingRate());
        assertNull(override.getNewCeilingRate());
        assertEquals(decisionBARCPOutput.getPropertyId(), override.getPropertyId());
        assertEquals(decisionBARCPOutput.getDecisionId(), override.getDecisionId());
        assertEquals(decisionBARCPOutput.getAccomType(), override.getAccomType());
        assertEquals(decisionBARCPOutput.getArrivalDate(), override.getArrivalDate());
        assertEquals(decisionBARCPOutput.getProduct(), override.getProduct());
        assertEquals((Integer) 12, override.getUser());
        assertEquals(decisionBARCPOutput.getDecisionId(), override.getDecisionId());

        assertEquals(otherDecision2, values.get(2));

        override = (CPDecisionBAROutputOverride) values.get(3);
        assertEquals(DecisionOverrideType.CEIL, override.getOldOverrideType());
        assertEquals(DecisionOverrideType.PENDING, override.getNewOverrideType());
        assertEquals(Supplement.removeSupplementFrom(BigDecimal.valueOf(35), supplementValue), override.getOldUserOverride());
        assertNull(override.getNewUserOverride());
        assertEquals(Supplement.removeSupplementFrom(BigDecimal.valueOf(37), supplementValue), override.getOldFloorRate());
        assertNull(override.getNewFloorRate());
        assertEquals(Supplement.removeSupplementFrom(BigDecimal.valueOf(36), supplementValue), override.getOldCeilingRate());
        assertNull(override.getNewCeilingRate());
        assertEquals(otherDecision2.getPropertyId(), override.getPropertyId());
        assertEquals(otherDecision2.getDecisionId(), override.getDecisionId());
        assertEquals(otherDecision2.getAccomType(), override.getAccomType());
        assertEquals(otherDecision2.getArrivalDate(), override.getArrivalDate());
        assertEquals(otherDecision2.getProduct(), override.getProduct());
        assertEquals((Integer) 12, override.getUser());
    }


    @Test
    public void findDemandForecastForAccomClass() {
        //setup
        List expectedResults = asList(new CPUnqualifedDemandForecastPrice(), new CPUnqualifedDemandForecastPrice());
        AccomClass accomClass = new AccomClass();
        LocalDate arrivalDate = new LocalDate();
        CPManagementService spy = Mockito.spy(service);
        Mockito.when(spy.findDemandForecastBetweenDatesForAccomClass(accomClass, arrivalDate, arrivalDate))
                .thenReturn(expectedResults);
        //when
        List<CPUnqualifedDemandForecastPrice> result = spy.findDemandForecastForAccomClass(accomClass, arrivalDate);

        //then
        assertEquals(expectedResults, result);
    }


    @Test
    public void findDemandForecastBetweenDatesForAccomClass() {
        //setup
        List expectedResults = asList(new CPUnqualifedDemandForecastPrice(), new CPUnqualifedDemandForecastPrice());
        AccomClass accomClass = new AccomClass();
        LocalDate arrivalDate = new LocalDate();

        Product product = buildBarProduct();
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(product);

        when(crudService.findByNamedQuery(CPUnqualifedDemandForecastPrice.GET_DEMAND_FORECAST_FOR_ACCOM_CLASS,
                CPUnqualifedDemandForecastPrice.params(product, accomClass, arrivalDate, arrivalDate))).thenReturn(expectedResults);

        //when
        List<CPUnqualifedDemandForecastPrice> result = service.findDemandForecastForAccomClass(accomClass, arrivalDate);

        //then
        assertEquals(expectedResults, result);
    }

    @Test
    public void isPriceBetweenFloorAndCeiling() {
        // setup
        java.time.LocalDate startDate = java.time.LocalDate.now();
        LocalDate occupancyDate = new LocalDate(startDate.toString());
        AccomType accomType = buildAccomType();

        Product product = buildBarProduct();
        when(service.findDemandForecastBetweenDatesForAccomClass(product, accomType.getAccomClass(), occupancyDate, occupancyDate))
                .thenReturn(buildCPUnqualifiedDemandForecastPrice(occupancyDate, occupancyDate));

        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(pricingConfigurationService.getCPDecisionContext(new LocalDate(startDate.toString()), new LocalDate(startDate.toString()), false, OccupancyType.SINGLE)).thenReturn(cpDecisionContext);
        BigDecimal ceiling = new BigDecimal(115);
        BigDecimal floor = new BigDecimal(25);
        when(cpDecisionContext.calculatePrettyPrice(product.getId(), ceiling)).thenReturn(ceiling);
        when(cpDecisionContext.calculatePrettyPrice(product.getId(), floor)).thenReturn(floor);
        when(cpDecisionContext.getPrimaryProduct(product)).thenReturn(product);

        // Validate against a valid price
        assertTrue(service.isPriceBetweenFloorAndCeiling(product, occupancyDate, accomType,
                BigDecimal.valueOf(115), BigDecimal.valueOf(25)), "values should be within the range");
        // Below Floor
        assertFalse(service.isPriceBetweenFloorAndCeiling(product, occupancyDate, accomType,
                BigDecimal.valueOf(115), BigDecimal.valueOf(24)), "floor value is below the range");

        // Above ceiling
        assertFalse(service.isPriceBetweenFloorAndCeiling(product, occupancyDate, accomType,
                BigDecimal.valueOf(126), BigDecimal.valueOf(25)), "ceiling value is above the range");

        // Validate against a valid price

        Product independentProduct = buildIndependentProduct();

        CPConfigMergedCeilingAndFloor ceilingAndFloor = new CPConfigMergedCeilingAndFloor();
        ceilingAndFloor.setFloorRate(floor);
        ceilingAndFloor.setCeilingRate(ceiling);

        CPConfigMergedCeilingAndFloorPK cpConfigMergedCeilingAndFloorPK = new CPConfigMergedCeilingAndFloorPK(independentProduct.getId(), occupancyDate, accomType.getId());
        ceilingAndFloor.setId(cpConfigMergedCeilingAndFloorPK);

        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloors = new HashMap<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor>();
        ceilingAndFloors.put(ceilingAndFloor.getId(), ceilingAndFloor);

        when(pricingConfigurationService.findCeilingAndFloorConfigForDates(occupancyDate, occupancyDate, false)).thenReturn(ceilingAndFloors);

        assertTrue(service.isPriceBetweenFloorAndCeiling(independentProduct, occupancyDate, accomType,
                BigDecimal.valueOf(115), BigDecimal.valueOf(25)), "values should be within the range");

        assertFalse(service.isPriceBetweenFloorAndCeiling(independentProduct, occupancyDate, accomType,
                BigDecimal.valueOf(125), BigDecimal.valueOf(25)), "values should be within the range");

        assertFalse(service.isPriceBetweenFloorAndCeiling(independentProduct, occupancyDate, accomType,
                BigDecimal.valueOf(115), BigDecimal.valueOf(15)), "values should be within the range");

        verify(pricingConfigurationService, times(3)).getBaseOccupancyType();
        verify(pricingConfigurationService, times(3)).getCPDecisionContext(new LocalDate(startDate.toString()), new LocalDate(startDate.toString()), false, OccupancyType.SINGLE);
    }

    @Test
    public void isPriceBetweenFloorAndCeilingWithNoSupplementIsPresent() {
        // setup
        java.time.LocalDate startDate = java.time.LocalDate.now();
        LocalDate occupancyDate = new LocalDate(startDate.toString());
        AccomType accomType = buildAccomType();

        Product product = buildBarProduct();
        when(service.findDemandForecastBetweenDatesForAccomClass(product, accomType.getAccomClass(), occupancyDate, occupancyDate))
                .thenReturn(buildCPUnqualifiedDemandForecastPrice(occupancyDate, occupancyDate));

        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(pricingConfigurationService.getCPDecisionContext(new LocalDate(startDate.toString()), new LocalDate(startDate.toString()), false, OccupancyType.SINGLE)).thenReturn(cpDecisionContext);
        BigDecimal ceiling = new BigDecimal(115);
        BigDecimal floor = new BigDecimal(25);
        when(cpDecisionContext.calculatePrettyPrice(product.getId(), ceiling)).thenReturn(ceiling);
        when(cpDecisionContext.calculatePrettyPrice(product.getId(), floor)).thenReturn(floor);
        when(cpDecisionContext.getPrimaryProduct(product)).thenReturn(product);
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_SUPPLEMENTAL_ENABLED.value())).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PERCENTAGE_FOR_SUPPLEMENT)).thenReturn(true);
        when(accomTypeSupplementService.getSupplementValueMap(JavaLocalDateUtils.toJodaLocalDate(startDate), JavaLocalDateUtils.toJodaLocalDate(startDate))).thenReturn(Collections.emptyMap());

        // Validate against a valid price
        assertTrue(service.isPriceBetweenFloorAndCeiling(product, occupancyDate, accomType,
                BigDecimal.valueOf(115), BigDecimal.valueOf(25)), "values should be within the range");
        // Below Floor
        assertFalse(service.isPriceBetweenFloorAndCeiling(product, occupancyDate, accomType,
                BigDecimal.valueOf(115), BigDecimal.valueOf(24)), "floor value is below the range");

        // Above ceiling
        assertFalse(service.isPriceBetweenFloorAndCeiling(product, occupancyDate, accomType,
                BigDecimal.valueOf(126), BigDecimal.valueOf(25)), "ceiling value is above the range");

        // Validate against a valid price

        Product independentProduct = buildIndependentProduct();

        CPConfigMergedCeilingAndFloor ceilingAndFloor = new CPConfigMergedCeilingAndFloor();
        ceilingAndFloor.setFloorRate(floor);
        ceilingAndFloor.setCeilingRate(ceiling);

        CPConfigMergedCeilingAndFloorPK cpConfigMergedCeilingAndFloorPK = new CPConfigMergedCeilingAndFloorPK(independentProduct.getId(), occupancyDate, accomType.getId());
        ceilingAndFloor.setId(cpConfigMergedCeilingAndFloorPK);

        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloors = new HashMap<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor>();
        ceilingAndFloors.put(ceilingAndFloor.getId(), ceilingAndFloor);

        when(pricingConfigurationService.findCeilingAndFloorConfigForDates(occupancyDate, occupancyDate, false)).thenReturn(ceilingAndFloors);

        assertTrue(service.isPriceBetweenFloorAndCeiling(independentProduct, occupancyDate, accomType,
                BigDecimal.valueOf(115), BigDecimal.valueOf(25)), "values should be within the range");

        assertFalse(service.isPriceBetweenFloorAndCeiling(independentProduct, occupancyDate, accomType,
                BigDecimal.valueOf(125), BigDecimal.valueOf(25)), "values should be within the range");

        assertFalse(service.isPriceBetweenFloorAndCeiling(independentProduct, occupancyDate, accomType,
                BigDecimal.valueOf(115), BigDecimal.valueOf(15)), "values should be within the range");

        verify(pricingConfigurationService, times(3)).getBaseOccupancyType();
        verify(pricingConfigurationService, times(3)).getCPDecisionContext(new LocalDate(startDate.toString()), new LocalDate(startDate.toString()), false, OccupancyType.SINGLE);
    }


    private Product buildIndependentProduct() {
        Product product = new Product();
        product.setId(24);
        product.setSystemDefault(false);
        product.setCode(Product.INDEPENDENT_PRODUCT_CODE);
        return product;
    }

    private List<CPUnqualifedDemandForecastPrice> buildCPUnqualifiedDemandForecastPrice(LocalDate occupancyDate, LocalDate date) {
        List<CPUnqualifedDemandForecastPrice> cpUnqualifedDemandForecastPriceList = new ArrayList<>();
        LocalDate tempDate = new LocalDate(occupancyDate.toDate());
        while (tempDate.compareTo(date) < 1) {
            for (int i = 0; i < 10; i++) {
                cpUnqualifedDemandForecastPriceList.add(createDemandForeCastPrice(tempDate, i));
            }
            tempDate = tempDate.plusDays(1);

        }
        return cpUnqualifedDemandForecastPriceList;

    }

    private CPUnqualifedDemandForecastPrice createDemandForeCastPrice(LocalDate arrivalDate, int rate) {
        CPUnqualifedDemandForecastPrice demandForecastPrice = new CPUnqualifedDemandForecastPrice();
        demandForecastPrice.setArrivalDate(arrivalDate);
        demandForecastPrice.setRate(BigDecimal.valueOf(25 + rate * 10));

        demandForecastPrice.setProduct(buildBarProduct());
        return demandForecastPrice;
    }


    @Test
    public void isPriceBetweenFloorAndCeilingSupplementEnabled() {
        // setup
        java.time.LocalDate startDate = java.time.LocalDate.now();
        LocalDate occupancyDate = new LocalDate(startDate.toString());
        AccomType accomType = buildAccomType();
        Product product = buildBarProduct();

        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(service.findDemandForecastBetweenDatesForAccomClass(product, buildAccomType().getAccomClass(), occupancyDate, occupancyDate))
                .thenReturn(buildCPUnqualifiedDemandForecastPrice(occupancyDate, occupancyDate));

        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_SUPPLEMENTAL_ENABLED.value())).thenReturn(true);
        Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> map = new HashMap<>();
        buildSupplementMap(occupancyDate, accomType.getId(), map);
        when(accomTypeSupplementService.getSupplementValueMap(occupancyDate, occupancyDate)).thenReturn(map);
        when(pricingConfigurationService.getCPDecisionContext(new LocalDate(startDate.toString()), new LocalDate(startDate.toString()), false, OccupancyType.SINGLE)).thenReturn(cpDecisionContext);
        BigDecimal ceiling = new BigDecimal(125);
        BigDecimal floor = new BigDecimal(35);
        when(cpDecisionContext.calculatePrettyPrice(product.getId(), ceiling)).thenReturn(ceiling);
        when(cpDecisionContext.calculatePrettyPrice(product.getId(), floor)).thenReturn(floor);
        when(cpDecisionContext.getPrimaryProduct(product)).thenReturn(product);

        // Validate against a valid price
        assertTrue(service.isPriceBetweenFloorAndCeiling(product, occupancyDate, accomType,
                BigDecimal.valueOf(125), BigDecimal.valueOf(35)), "values should be within the range");

        // Below Floor
        assertFalse(service.isPriceBetweenFloorAndCeiling(product, occupancyDate, accomType,
                BigDecimal.valueOf(125), BigDecimal.valueOf(25)), "floor value is below the range");

        // Above ceiling
        assertFalse(service.isPriceBetweenFloorAndCeiling(product, occupancyDate, accomType,
                BigDecimal.valueOf(136), BigDecimal.valueOf(85)), "ceiling value is above the range");

        verify(pricingConfigurationService, times(3)).getBaseOccupancyType();
        verify(pricingConfigurationService, times(3)).getCPDecisionContext(new LocalDate(startDate.toString()), new LocalDate(startDate.toString()), false, OccupancyType.SINGLE);
    }

    @Test
    public void isPriceBetweenFloorAndCeilingTaxAndSupplementEnabled() {
        // setup
        java.time.LocalDate startDate = java.time.LocalDate.now();
        LocalDate occupancyDate = new LocalDate(startDate.toString());
        AccomType accomType = buildAccomType();
        Product product = buildBarProduct();

        when(service.findDemandForecastBetweenDatesForAccomClass(product, buildAccomType().getAccomClass(), occupancyDate, occupancyDate))
                .thenReturn(buildCPUnqualifiedDemandForecastPrice(occupancyDate, occupancyDate));

        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_SUPPLEMENTAL_ENABLED.value())).thenReturn(true);
        Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> map = new HashMap<>();
        buildSupplementMap(occupancyDate, accomType.getId(), map);
        when(accomTypeSupplementService.getSupplementValueMap(occupancyDate, occupancyDate)).thenReturn(map);

        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(pricingConfigurationService.getCPDecisionContext(new LocalDate(startDate.toString()), new LocalDate(startDate.toString()), false, OccupancyType.SINGLE)).thenReturn(cpDecisionContext);
        BigDecimal ceiling = new BigDecimal(142.25000).setScale(5);
        BigDecimal floor = new BigDecimal(38.75000).setScale(5);
        when(cpDecisionContext.calculatePrettyPrice(product.getId(), ceiling)).thenReturn(ceiling);
        when(cpDecisionContext.calculatePrettyPrice(product.getId(), floor)).thenReturn(floor);
        when(cpDecisionContext.getPrimaryProduct(product)).thenReturn(product);

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(15));
        Map<LocalDate, Tax> taxesByDate = new HashMap<>();
        taxesByDate.put(occupancyDate, tax);
        when(taxService.findTaxesForDateRange(occupancyDate, occupancyDate)).thenReturn(taxesByDate);
        //UpperboundLimit:142.25000 LowerBoundLimit:38.75000
        // Validate against a valid price
        assertTrue(service.isPriceBetweenFloorAndCeiling(product, occupancyDate, accomType,
                BigDecimal.valueOf(142.25), BigDecimal.valueOf(38.75)), "values should be within the range");

        // Below Floor
        assertFalse(service.isPriceBetweenFloorAndCeiling(product, occupancyDate, accomType,
                BigDecimal.valueOf(143), BigDecimal.valueOf(37)), "floor value is below the range");

        verify(taxService, times(2)).findTaxesForDateRange(occupancyDate, occupancyDate);
        verify(pricingConfigurationService, times(2)).getBaseOccupancyType();
        verify(pricingConfigurationService, times(2)).getCPDecisionContext(new LocalDate(startDate.toString()), new LocalDate(startDate.toString()), false, OccupancyType.SINGLE);
    }


    private void buildSupplementMap(LocalDate startDate, LocalDate endDate, Integer accomTypeId,
                                    Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> map) {
        LocalDate tempDate = startDate;
        while (tempDate.compareTo(endDate) < 1) {
            buildSupplementMap(tempDate, accomTypeId, map);
            tempDate = tempDate.plusDays(1);
        }
    }

    private void buildSupplementMap(LocalDate arrivalDate, Integer
            accomTypeId, Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> map) {

        AccomTypeSupplementValuePK accomTypeSupplementValuePK = new AccomTypeSupplementValuePK(1, arrivalDate,
                accomTypeId, OccupancyType.SINGLE);
        accomTypeSupplementValuePK.setArrivalDate(arrivalDate);
        AccomTypeSupplementValue accomTypeSupplementValue = new AccomTypeSupplementValue();
        accomTypeSupplementValue.setId(accomTypeSupplementValuePK);
        accomTypeSupplementValue.setValue(BigDecimal.TEN);

        map.put(accomTypeSupplementValuePK, accomTypeSupplementValue);
    }

    @Test
    public void isPriceBetweenFloorAndCeilingMultiDay() {
        AccomType accomType = buildAccomType();
        Product product = buildBarProduct();

        when(service.findDemandForecastBetweenDatesForAccomClass(product, accomType.getAccomClass(), START_DATE_RANGE, END_DATE_RANGE))
                .thenReturn(buildCPUnqualifiedDemandForecastPrice(START_DATE_RANGE, END_DATE_RANGE));
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(pricingConfigurationService.getCPDecisionContext(new LocalDate(START_DATE_RANGE.toString()), new LocalDate(END_DATE_RANGE.toString()), false, OccupancyType.SINGLE)).thenReturn(cpDecisionContext);
        BigDecimal ceiling = new BigDecimal(115);
        BigDecimal floor = new BigDecimal(25);
        when(cpDecisionContext.calculatePrettyPrice(product.getId(), ceiling)).thenReturn(ceiling);
        when(cpDecisionContext.calculatePrettyPrice(product.getId(), floor)).thenReturn(floor);
        when(cpDecisionContext.getPrimaryProduct(product)).thenReturn(product);

        assertFalse(service.isPriceBetweenFloorAndCeilingMultiDay(product, START_DATE_RANGE, END_DATE_RANGE, accomType,
                BigDecimal.valueOf(10), BigDecimal.valueOf(10), DAY_OF_WEEKS), "10 is not between max floor and min ceiling");

        assertTrue(service.isPriceBetweenFloorAndCeilingMultiDay(product, START_DATE_RANGE, END_DATE_RANGE,
                accomType, BigDecimal.valueOf(25), BigDecimal.valueOf(25), DAY_OF_WEEKS), "25 is between max floor and min ceiling");

        assertTrue(service.isPriceBetweenFloorAndCeilingMultiDay(product, START_DATE_RANGE, END_DATE_RANGE,
                accomType, BigDecimal.valueOf(40), BigDecimal.valueOf(40), DAY_OF_WEEKS), "40 is between max floor and min ceiling");

        assertFalse(service.isPriceBetweenFloorAndCeilingMultiDay(product, START_DATE_RANGE, END_DATE_RANGE,
                accomType, BigDecimal.valueOf(126), BigDecimal.valueOf(126), DAY_OF_WEEKS), "126 is not between max floor and min ceiling");

        verify(pricingConfigurationService, times(4)).getBaseOccupancyType();
        verify(pricingConfigurationService, times(4)).getCPDecisionContext(new LocalDate(START_DATE_RANGE.toString()), new LocalDate(END_DATE_RANGE.toString()), false, OccupancyType.SINGLE);
    }

    @Test
    public void isPriceBetweenFloorAndCeilingMultiDaySupplementEnabled() {
        AccomType accomType = buildAccomType();
        Product product = buildBarProduct();

        when(service.findDemandForecastBetweenDatesForAccomClass(product, accomType.getAccomClass(), START_DATE_RANGE, END_DATE_RANGE))
                .thenReturn(buildCPUnqualifiedDemandForecastPrice(START_DATE_RANGE, END_DATE_RANGE));

        //minceiling=115, maxfloor=35
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_SUPPLEMENTAL_ENABLED.value())).thenReturn(true);

        Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> map = new HashMap<>();
        buildSupplementMap(START_DATE_RANGE, END_DATE_RANGE, accomType.getId(), map);
        when(accomTypeSupplementService.getSupplementValueMap(START_DATE_RANGE, END_DATE_RANGE)).thenReturn(map);

        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(pricingConfigurationService.getCPDecisionContext(new LocalDate(START_DATE_RANGE.toString()), new LocalDate(END_DATE_RANGE.toString()), false, OccupancyType.SINGLE)).thenReturn(cpDecisionContext);
        BigDecimal ceiling = new BigDecimal(125);
        BigDecimal floor = new BigDecimal(35);
        when(cpDecisionContext.calculatePrettyPrice(product.getId(), ceiling)).thenReturn(ceiling);
        when(cpDecisionContext.calculatePrettyPrice(product.getId(), floor)).thenReturn(floor);
        when(cpDecisionContext.getPrimaryProduct(product)).thenReturn(product);

        assertFalse(service.isPriceBetweenFloorAndCeilingMultiDay(product, START_DATE_RANGE,
                        END_DATE_RANGE, accomType,
                        BigDecimal.valueOf(10), BigDecimal.valueOf(10), DAY_OF_WEEKS),
                "10 should be lower");

        assertTrue(service.isPriceBetweenFloorAndCeilingMultiDay(product, START_DATE_RANGE, END_DATE_RANGE,
                        accomType, BigDecimal.valueOf(35), BigDecimal.valueOf(35), DAY_OF_WEEKS),
                "35 should be between max floor and min ceiling");

        assertTrue(service.isPriceBetweenFloorAndCeilingMultiDay(product, START_DATE_RANGE, END_DATE_RANGE,
                        accomType, BigDecimal.valueOf(115), BigDecimal.valueOf(115), DAY_OF_WEEKS),
                "115 should be between max floor and min ceiling");

        assertFalse(service.isPriceBetweenFloorAndCeilingMultiDay(product, START_DATE_RANGE, END_DATE_RANGE,
                        accomType, BigDecimal.valueOf(135), BigDecimal.valueOf(135), DAY_OF_WEEKS),
                "135 is not between max floor and min ceiling");

        verify(pricingConfigurationService, times(4)).getBaseOccupancyType();
        verify(pricingConfigurationService, times(4)).getCPDecisionContext(new LocalDate(START_DATE_RANGE.toString()), new LocalDate(END_DATE_RANGE.toString()), false, OccupancyType.SINGLE);
    }

    @Test
    public void isPriceBetweenFloorAndCeilingMultiDayTaxAndSupplementEnabled() {
        AccomType accomType = buildAccomType();
        Product product = buildBarProduct();

        when(service.findDemandForecastBetweenDatesForAccomClass(product, accomType.getAccomClass(), START_DATE_RANGE, END_DATE_RANGE))
                .thenReturn(buildCPUnqualifiedDemandForecastPrice(START_DATE_RANGE, END_DATE_RANGE));

        //minceiling=136.5, maxfloor=37.5
        when(configParamsService.getBooleanParameterValue(GUIConfigParamName.IS_SUPPLEMENTAL_ENABLED.value())).thenReturn(true);
        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);

        Map<AccomTypeSupplementValuePK, AccomTypeSupplementValue> map = new HashMap<>();
        buildSupplementMap(START_DATE_RANGE, END_DATE_RANGE, accomType.getId(), map);
        when(accomTypeSupplementService.getSupplementValueMap(START_DATE_RANGE, END_DATE_RANGE)).thenReturn(map);

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.TEN);
        Map<LocalDate, Tax> taxesByDate = new HashMap<>();
        taxesByDate.put(START_DATE_RANGE, tax);
        taxesByDate.put(START_DATE_RANGE.plusDays(1), tax);
        taxesByDate.put(END_DATE_RANGE, tax);
        when(taxService.findTaxesForDateRange(START_DATE_RANGE, END_DATE_RANGE)).thenReturn(taxesByDate);

        when(pricingConfigurationService.getBaseOccupancyType()).thenReturn(OccupancyType.SINGLE);
        when(pricingConfigurationService.getCPDecisionContext(new LocalDate(START_DATE_RANGE.toString()), new LocalDate(END_DATE_RANGE.toString()), false, OccupancyType.SINGLE)).thenReturn(cpDecisionContext);
        BigDecimal ceiling = new BigDecimal(136.50000).setScale(5);
        BigDecimal floor = new BigDecimal(37.50000).setScale(5);
        when(cpDecisionContext.calculatePrettyPrice(product.getId(), ceiling)).thenReturn(ceiling);
        when(cpDecisionContext.calculatePrettyPrice(product.getId(), floor)).thenReturn(floor);
        when(cpDecisionContext.getPrimaryProduct(product)).thenReturn(product);

        assertFalse(service.isPriceBetweenFloorAndCeilingMultiDay
                        (product, START_DATE_RANGE, END_DATE_RANGE, accomType, BigDecimal.valueOf(137), BigDecimal.valueOf(36), DAY_OF_WEEKS),
                "values should be outside of ceiling floor");

        assertTrue(service.isPriceBetweenFloorAndCeilingMultiDay
                        (product, START_DATE_RANGE, END_DATE_RANGE, accomType, BigDecimal.valueOf(136.5), BigDecimal.valueOf(37.5), DAY_OF_WEEKS),
                "values should be between max floor and min ceiling");
        verify(taxService, times(2)).findTaxesForDateRange(START_DATE_RANGE, END_DATE_RANGE);

        verify(pricingConfigurationService, times(2)).getBaseOccupancyType();
        verify(pricingConfigurationService, times(2)).getCPDecisionContext(new LocalDate(START_DATE_RANGE.toString()), new LocalDate(END_DATE_RANGE.toString()), false, OccupancyType.SINGLE);
    }

    @Test
    public void isPriceBetweenFloorAndCeilingMultiDay_empty() {
        List<CPConfigMergedCeilingAndFloor> mergedCeilingAndFloorList = new ArrayList<>();

        Mockito.when(pricingConfigurationService.getCeilingAndFloorConfig(START_DATE_RANGE, END_DATE_RANGE, OccupancyType.SINGLE, true))
                .thenReturn(mergedCeilingAndFloorList);

        assertTrue(service.isPriceBetweenFloorAndCeilingMultiDay(new Product(), START_DATE_RANGE,
                        END_DATE_RANGE, new AccomType(), BigDecimal.valueOf(10), BigDecimal.valueOf(10), null),
                "default true");

        verify(pricingConfigurationService, never()).getBaseOccupancyType();
        verify(pricingConfigurationService, never()).getCPDecisionContext(new LocalDate(START_DATE_RANGE.toString()), new LocalDate(END_DATE_RANGE.toString()), false, OccupancyType.SINGLE);
    }

    private PricingBaseAccomType createPricingBaseAccomType() {
        TransientPricingBaseAccomType transientPricingBaseAccomType = new TransientPricingBaseAccomType();
        transientPricingBaseAccomType.setAccomType(buildAccomType());
        return transientPricingBaseAccomType;
    }

    private CPConfigMergedCeilingAndFloor getCeilingAndFloor(int ceiling, int floor, LocalDate localDate) {
        CPConfigMergedCeilingAndFloor cpConfigMergedCeilingAndFloor = new CPConfigMergedCeilingAndFloor();
        cpConfigMergedCeilingAndFloor.setCeilingRate(BigDecimal.valueOf(ceiling));
        cpConfigMergedCeilingAndFloor.setFloorRate(BigDecimal.valueOf(floor));

        CPConfigMergedCeilingAndFloorPK cpConfigMergedCeilingAndFloorPK = new CPConfigMergedCeilingAndFloorPK(1, localDate, buildAccomType().getId());
        cpConfigMergedCeilingAndFloor.setId(cpConfigMergedCeilingAndFloorPK);

        return cpConfigMergedCeilingAndFloor;
    }

    private void expectFloorCeiling(PricingRule pricingRule, CPDecisionBAROutput output, BigDecimal floor, BigDecimal ceiling) {
        expectFloorCeiling(pricingRule, output, floor, ceiling, null);
    }

    private void expectFloorCeiling(PricingRule pricingRule, CPDecisionBAROutput output, BigDecimal floor, BigDecimal ceiling, BigDecimal supplement) {
        CPConfigMergedCeilingAndFloor ceilingAndFloor = new CPConfigMergedCeilingAndFloor();
        ceilingAndFloor.setFloorRate(floor);
        ceilingAndFloor.setCeilingRate(ceiling);

        CPConfigMergedCeilingAndFloorPK cpConfigMergedCeilingAndFloorPK = new CPConfigMergedCeilingAndFloorPK(1, output.getArrivalDate(), output.getAccomType().getId());
        ceilingAndFloor.setId(cpConfigMergedCeilingAndFloorPK);

        Map<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor> ceilingAndFloors = new HashMap<CPConfigMergedCeilingAndFloorPK, CPConfigMergedCeilingAndFloor>();
        ceilingAndFloors.put(ceilingAndFloor.getId(), ceilingAndFloor);

        when(pricingConfigurationService.findCeilingAndFloorConfigForDates(output.getArrivalDate(), output.getArrivalDate(), true)).thenReturn(ceilingAndFloors);
    }

    private AccomType buildAccomType() {
        AccomType accomType = new AccomType();
        accomType.setId(1);
        accomType.setAccomClass(buildAccomClass());
        return accomType;
    }

    private AccomClass buildAccomClass() {
        AccomClass accomClass = new AccomClass();
        accomClass.setId(1);
        return accomClass;
    }

    private Product buildBarProduct() {
        Product product = new Product();
        product.setId(1);
        product.setSystemDefault(true);
        product.setCode(Product.BAR);
        return product;
    }

    private Product buildAgileNonOptimizedProduct() {
        Product product = new Product();
        product.setId(1);
        product.setSystemDefault(false);
        product.setOptimized(false);
        product.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        return product;
    }

    @Test
    public void shouldGetCPBarDecisionDetails() {
        doReturn(Arrays.asList(createCPUnqualifedDemandForecastPrice(BigDecimal.TEN))).when(spyService).findDemandForecastForAccomClass(Mockito.any(AccomClass.class), Mockito.any(LocalDate.class));
        when(roleServiceMock.getPropertyUserRole(anyString(), anyInt())).thenReturn(populateRole(WC_CLIENT_CODE_BLACKSTONE, "1"));
        when(rolePermissionServiceMock.getAccessibleRolePermissions()).thenReturn(asList(RoleModulePermissionMapperTest.prepareRolePermission("role1", PRICING, RoleModulePermissionMapperUtil.PERMISSION_READ_WRITE)));
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(true);
        List<CPBARDecisionDTO> cpbarDecisionDTOS = Arrays.asList(createCPBARDecisionDTO("2017-12-19", BigDecimal.TEN));
        doReturn(cpbarDecisionDTOS).when(spyService).searchForBarDecisionDTO(Mockito.eq(true), Mockito.any(PricingManagementCPSearchCriteria.class), Mockito.eq(true));
        List<CPBarOverride> cpBarOverrides = new ArrayList<>();
        LocalDate occupancyDate = LocalDate.parse("2017-12-19");
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(15));
        when(taxService.findTaxFor(occupancyDate)).thenReturn(tax);

        doReturn(cpBarOverrides).when(spyService).prepareCPBarOverrides(cpbarDecisionDTOS.get(0).getDecisions(), tax, BAR_PRODUCT_ID);
        BarDecisionDetailsData barDecisionDetailsData = spyService.getCPBarDecisionDetails("2017-12-19");
        assertNotNull(barDecisionDetailsData);
        assertEquals(DateUtil.getDayofWeek("2017-12-19"), barDecisionDetailsData.getDow());
        assertNull(barDecisionDetailsData.getLrv());
        assertEquals("12-19-2017", barDecisionDetailsData.getOccupancyDate());
        assertEquals(Constants.BAR_DECISION_VALUE_RATEOFDAY + "_CP", barDecisionDetailsData.getPricingType());
        assertEquals("10.00", barDecisionDetailsData.getPropertyOccupancyForecastPerc());
        CPBarDecisionDetails cpBarDecisionDetails = barDecisionDetailsData.getCpBarDecisionDetails();
        assertNotNull(cpBarDecisionDetails);
        assertEquals(RoleModulePermissionMapperUtil.PERMISSION_WRITE, cpBarDecisionDetails.getCpBarOverridePermission());
        assertTrue(cpBarDecisionDetails.isCpBaseRoomTypeOnlyEnabled());
        verify(taxService).findTaxFor(occupancyDate);
    }

    @Test
    public void testGetCPBarDecisionDetailsForNullPropertyOccupancyForecast() {
        when(roleServiceMock.getPropertyUserRole(anyString(), anyInt())).thenReturn(populateRole(WC_CLIENT_CODE_BLACKSTONE, "1"));
        when(rolePermissionServiceMock.getAccessibleRolePermissions()).thenReturn(asList(RoleModulePermissionMapperTest.prepareRolePermission("role1", PRICING, RoleModulePermissionMapperUtil.PERMISSION_READ_WRITE)));
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(true);
        List<CPBARDecisionDTO> cpBarDecisionDTOS = Arrays.asList(createCPBARDecisionDTO("2017-12-19", null));
        doReturn(cpBarDecisionDTOS).when(spyService).searchForBarDecisionDTO(Mockito.eq(true), Mockito.any(PricingManagementCPSearchCriteria.class), Mockito.eq(true));

        LocalDate occupancyDate = LocalDate.parse("2017-12-19");
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(15));
        when(taxService.findTaxFor(occupancyDate)).thenReturn(tax);
        doReturn(Collections.emptyList()).when(spyService).prepareCPBarOverrides(cpBarDecisionDTOS.get(0).getDecisions(), tax, BAR_PRODUCT_ID);

        BarDecisionDetailsData barDecisionDetailsData = spyService.getCPBarDecisionDetails("2017-12-19");
        assertNotNull(barDecisionDetailsData);
        assertNull(barDecisionDetailsData.getPropertyOccupancyForecastPerc());
        CPBarDecisionDetails cpBarDecisionDetails = barDecisionDetailsData.getCpBarDecisionDetails();
        assertNotNull(cpBarDecisionDetails);
    }

    @Test
    public void testGetCPBarDecisionDetailsWhenNoCPBarDecisionInfoAvailable() {
        when(roleServiceMock.getPropertyUserRole(anyString(), anyInt())).thenReturn(populateRole(WC_CLIENT_CODE_BLACKSTONE, "1"));
        when(rolePermissionServiceMock.getAccessibleRolePermissions()).thenReturn(asList(RoleModulePermissionMapperTest.prepareRolePermission("role1", PRICING, RoleModulePermissionMapperUtil.PERMISSION_READ_WRITE)));
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())).thenReturn(false);
        doReturn(Collections.emptyList()).when(spyService).searchForBarDecisionDTO(Mockito.eq(true), Mockito.any(PricingManagementCPSearchCriteria.class), Mockito.eq(true));
        BarDecisionDetailsData barDecisionDetailsData = spyService.getCPBarDecisionDetails("2017-12-19");
        assertNotNull(barDecisionDetailsData);
        assertEquals(Constants.BAR_DECISION_VALUE_RATEOFDAY + "_CP", barDecisionDetailsData.getPricingType());
        CPBarDecisionDetails cpBarDecisionDetails = barDecisionDetailsData.getCpBarDecisionDetails();
        assertEquals(RoleModulePermissionMapperUtil.PERMISSION_WRITE, cpBarDecisionDetails.getCpBarOverridePermission());
        assertTrue(cpBarDecisionDetails.isCpBaseRoomTypeOnlyEnabled());
        assertFalse(barDecisionDetailsData.isCompetitorsDataAvailable());
        assertEquals(0, cpBarDecisionDetails.getCpBarOverrides().size());
    }

    CPBARDecisionDTO createCPBARDecisionDTO(String dateString, BigDecimal occupancyForecastPercentage) {
        CPBARDecisionDTO cpbarDecisionDTO = new CPBARDecisionDTO();
        cpbarDecisionDTO.setDate(LocalDate.parse(dateString));
        cpbarDecisionDTO.setOccupancyForecastPercentage(occupancyForecastPercentage);
        cpbarDecisionDTO.setDecisions(Arrays.asList(createCPDecisionBAROutput(dateString, BigDecimal.ONE, null, null, Long.valueOf(1), 5, 1, 1, createAccomType(createAccomClass("Standard", 2), 1, "Test"))));
        return cpbarDecisionDTO;
    }

    CPUnqualifedDemandForecastPrice createCPUnqualifedDemandForecastPrice(BigDecimal rate) {
        CPUnqualifedDemandForecastPrice cpUnqualifedDemandForecastPrice = new CPUnqualifedDemandForecastPrice();
        cpUnqualifedDemandForecastPrice.setRate(rate);
        return cpUnqualifedDemandForecastPrice;
    }

    @Test
    public void shouldGetCPBarOverridePermission() {
        when(roleServiceMock.getPropertyUserRole(anyString(), anyInt())).thenReturn(populateRole(WC_CLIENT_CODE_BLACKSTONE, "1"));
        when(rolePermissionServiceMock.getAccessibleRolePermissions()).thenReturn(asList(RoleModulePermissionMapperTest.prepareRolePermission("role1", PRICING, RoleModulePermissionMapperUtil.PERMISSION_READ_WRITE)));
        Optional<RolePermission> cpBarOverridePermission = service.getCPBarOverridePermission();
        assertTrue(cpBarOverridePermission.isPresent());
        assertEquals(RoleModulePermissionMapperUtil.PERMISSION_READ_WRITE, cpBarOverridePermission.get().getPermission());
    }

    @Test
    public void shouldGetCPBarOverridePermissionForCurrentPropertyOfLoggedInUser() {
        when(rolePermissionServiceMock.getAccessibleRolePermissions()).thenReturn(asList(
                RoleModulePermissionMapperTest.prepareRolePermission("role1", PRICING, RoleModulePermissionMapperUtil.PERMISSION_READ_WRITE),
                RoleModulePermissionMapperTest.prepareRolePermission("role2", PRICING, RoleModulePermissionMapperUtil.PERMISSION_READ_ONLY)));
        when(roleServiceMock.getPropertyUserRole(anyString(), anyInt())).thenReturn(populateRole(WC_CLIENT_CODE_BLACKSTONE, "2"));
        Optional<RolePermission> cpBarOverridePermission = service.getCPBarOverridePermission();
        assertTrue(cpBarOverridePermission.isPresent());
        assertEquals(RoleModulePermissionMapperUtil.PERMISSION_READ_ONLY, cpBarOverridePermission.get().getPermission());
    }

    @Test
    public void shouldNotFailIfGetCPBarOverridePermissionIsNotSet() {
        when(roleServiceMock.getPropertyUserRole(anyString(), anyInt())).thenReturn(populateRole(WC_CLIENT_CODE_BLACKSTONE, "1"));
        when(rolePermissionServiceMock.getAccessibleRolePermissions()).thenReturn(asList(RoleModulePermissionMapperTest.prepareRolePermission("role1", "Pricing Management", RoleModulePermissionMapperUtil.PERMISSION_READ_WRITE)));
        Optional<RolePermission> cpBarOverridePermission = spyService.getCPBarOverridePermission();
        assertFalse(cpBarOverridePermission.isPresent());
    }

    @Test
    public void shouldLoadBaseRoomTypes() {
        List<PricingBaseAccomType> pricingAccomTypes = new ArrayList<>();
        pricingAccomTypes.add(createPricingBaseAccomType());
        when(pricingConfigurationService.getAllBaseRoomTypes()).thenReturn(pricingAccomTypes);
        assertEquals(1, spyService.loadBaseRoomTypes().size());
    }

    @Test
    public void loadBaseRoomTypesShouldReturnEmptyListIfNoBaseRoomTypesConfigured() {
        when(pricingConfigurationService.getAllBaseRoomTypes()).thenReturn(Collections.emptyList());
        assertEquals(0, spyService.loadBaseRoomTypes().size());
    }

    @Test
    public void shouldPopulatePricingManagementCPSearchCriteria() throws ParseException {
        PacmanWorkContextTestHelper.setup_SSOUser_PuneProperty_WorkContext();
        List<PricingBaseAccomType> pricingAccomTypes = new ArrayList<>();
        pricingAccomTypes.add(createPricingBaseAccomType());
        when(pricingConfigurationService.getAllBaseRoomTypes()).thenReturn(pricingAccomTypes);

        Date date = DateUtil.parseDate("2017-12-08", DateUtil.DEFAULT_DATE_FORMAT);
        PricingManagementCPSearchCriteria pricingManagementCPSearchCriteria = spyService.populatePricingMagmtCPSearchCriteriaForAllActiveProduct(date, BAR_PRODUCT_ID);
        assertNotNull(pricingManagementCPSearchCriteria);
        assertEquals(LocalDate.fromDateFields(date), pricingManagementCPSearchCriteria.getEndDate());
        assertEquals(LocalDate.fromDateFields(date), pricingManagementCPSearchCriteria.getStartDate());
        assertEquals(1, pricingManagementCPSearchCriteria.getLengthOfStay().intValue());
        assertEquals(1, pricingManagementCPSearchCriteria.getProductId().intValue());
        assertEquals(5, pricingManagementCPSearchCriteria.getPropertyId().intValue());
        assertEquals(1, pricingManagementCPSearchCriteria.getBaseRoomTypes().size());
    }

    @Test
    public void prepareCPBarOverridesShouldReturnEmptyListIfNoBarDecisionsAvailable() {
        Tax tax = new Tax();
        when(taxService.findTax()).thenReturn(tax);
        when(roomClassServiceMock.getAccomClassesByRankOrder()).thenReturn(new ArrayList<>());
        assertEquals(0, spyService.prepareCPBarOverrides(Collections.emptyList(), tax, BAR_PRODUCT_ID).size());
    }

    @Test
    public void shouldPrepareCPBarOverridesTaxIncluded() {
        doReturn(Arrays.asList(createCPUnqualifedDemandForecastPrice(BigDecimal.ONE), createCPUnqualifedDemandForecastPrice(BigDecimal.TEN))).when(spyService).findDemandForecastForAccomClass(Mockito.any(AccomClass.class), Mockito.any(LocalDate.class));
        CPDecisionBAROutput cpDecisionBAROutput = createCPDecisionBAROutput("2017-12-18", BigDecimal.ONE, null, null, Long.valueOf(1), 5, 1, 1, createAccomType(createAccomClass("Standard", 2), 1, "Test"));
        Product product = buildBarProduct();
        BigDecimal supplementValue = new BigDecimal("10.00");
        Tax tax = new Tax();
        tax.setRoomTaxRate(new BigDecimal("100.00"));
        when(taxService.findTax()).thenReturn(tax);
        when(prettyPricingService.getPricingRule(1)).thenReturn(new PricingRule());
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRICING_SCREEN_OPTIMIZATION)).thenReturn(true);
        AccomTypeSupplementValue accomTypeSupplementValue = getAccomTypeSupplementValue(java.time.LocalDate.now(), OffsetMethod.FIXED_PRICE, supplementValue);
        when(accomTypeSupplementService.getSupplementValueFor(any(Integer.class), any(LocalDate.class), any(Integer.class))).thenReturn(accomTypeSupplementValue);
        CPConfigMergedOffset cpMergedOffset = new CPConfigMergedOffset();
        cpMergedOffset.setOffsetValue(BigDecimal.valueOf(20));
        cpMergedOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        when(pricingConfigurationService.findMergedOffset(product.getId(), cpDecisionBAROutput.getArrivalDate(), cpDecisionBAROutput.getAccomType().getId())).thenReturn(cpMergedOffset);
        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(Arrays.asList(preparePricingAccomClass(cpDecisionBAROutput.getAccomType().getAccomClass())));
        List<CPBarOverride> barDecisions = spyService.prepareCPBarOverrides(Arrays.asList(cpDecisionBAROutput), tax, BAR_PRODUCT_ID);
        assertCPBarOverrides(cpDecisionBAROutput, barDecisions);
    }

    @Test
    public void shouldGetActiveProductDecisionDetails() {
        doReturn(Arrays.asList(createCPUnqualifedDemandForecastPrice(BigDecimal.TEN))).when(spyService).findDemandForecastForAccomClass(Mockito.any(AccomClass.class), Mockito.any(LocalDate.class));
        when(roleServiceMock.getPropertyUserRole(anyString(), anyInt())).thenReturn(populateRole(WC_CLIENT_CODE_BLACKSTONE, "1"));
        when(rolePermissionServiceMock.getAccessibleRolePermissions()).thenReturn(asList(RoleModulePermissionMapperTest.prepareRolePermission("role1", PRICING, RoleModulePermissionMapperUtil.PERMISSION_READ_WRITE)));
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(true);
        List<CPBARDecisionDTO> cpbarDecisionDTOS = Arrays.asList(createCPBARDecisionDTO("2017-12-19", BigDecimal.TEN));
        doReturn(cpbarDecisionDTOS).when(spyService).searchForBarDecisionDTO(Mockito.eq(true), Mockito.any(PricingManagementCPSearchCriteria.class), Mockito.eq(true));
        List<CPBarOverride> cpBarOverrides = new ArrayList<>();
        LocalDate occupancyDate = LocalDate.parse("2017-12-19");
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(15));
        when(taxService.findTaxFor(occupancyDate)).thenReturn(tax);

        doReturn(cpBarOverrides).when(spyService).prepareCPBarOverrides(cpbarDecisionDTOS.get(0).getDecisions(), tax, BAR_PRODUCT_ID);
        BarDecisionDetailsData barDecisionDetailsData = spyService.getActiveProductDecisionDetails("2017-12-19", BAR_PRODUCT_ID);
        assertNotNull(barDecisionDetailsData);
        assertEquals(DateUtil.getDayofWeek("2017-12-19"), barDecisionDetailsData.getDow());
        assertNull(barDecisionDetailsData.getLrv());
        assertEquals("12-19-2017", barDecisionDetailsData.getOccupancyDate());
        assertEquals(Constants.BAR_DECISION_VALUE_RATEOFDAY + "_CP", barDecisionDetailsData.getPricingType());
        assertEquals("10.00", barDecisionDetailsData.getPropertyOccupancyForecastPerc());
        CPBarDecisionDetails cpBarDecisionDetails = barDecisionDetailsData.getCpBarDecisionDetails();
        assertNotNull(cpBarDecisionDetails);
        assertEquals(RoleModulePermissionMapperUtil.PERMISSION_WRITE, cpBarDecisionDetails.getCpBarOverridePermission());
        assertTrue(cpBarDecisionDetails.isCpBaseRoomTypeOnlyEnabled());
        verify(taxService).findTaxFor(occupancyDate);
    }

    @Test
    public void testGetActiveProductDecisionDetailsForNullPropertyOccupancyForecast() {
        when(roleServiceMock.getPropertyUserRole(anyString(), anyInt())).thenReturn(populateRole(WC_CLIENT_CODE_BLACKSTONE, "1"));
        when(rolePermissionServiceMock.getAccessibleRolePermissions()).thenReturn(asList(RoleModulePermissionMapperTest.prepareRolePermission("role1", PRICING, RoleModulePermissionMapperUtil.PERMISSION_READ_WRITE)));
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(true);
        List<CPBARDecisionDTO> cpBarDecisionDTOS = Arrays.asList(createCPBARDecisionDTO("2017-12-19", null));
        doReturn(cpBarDecisionDTOS).when(spyService).searchForBarDecisionDTO(Mockito.eq(true), Mockito.any(PricingManagementCPSearchCriteria.class), Mockito.eq(true));

        LocalDate occupancyDate = LocalDate.parse("2017-12-19");
        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.valueOf(15));
        when(taxService.findTaxFor(occupancyDate)).thenReturn(tax);
        doReturn(Collections.emptyList()).when(spyService).prepareCPBarOverrides(cpBarDecisionDTOS.get(0).getDecisions(), tax, BAR_PRODUCT_ID);

        BarDecisionDetailsData barDecisionDetailsData = spyService.getActiveProductDecisionDetails("2017-12-19", BAR_PRODUCT_ID);
        assertNotNull(barDecisionDetailsData);
        assertNull(barDecisionDetailsData.getPropertyOccupancyForecastPerc());
        CPBarDecisionDetails cpBarDecisionDetails = barDecisionDetailsData.getCpBarDecisionDetails();
        assertNotNull(cpBarDecisionDetails);
    }

    @Test
    public void testGetActiveProductDecisionDetailsWhenNoCPBarDecisionInfoAvailable() {
        when(roleServiceMock.getPropertyUserRole(anyString(), anyInt())).thenReturn(populateRole(WC_CLIENT_CODE_BLACKSTONE, "1"));
        when(rolePermissionServiceMock.getAccessibleRolePermissions()).thenReturn(asList(RoleModulePermissionMapperTest.prepareRolePermission("role1", PRICING, RoleModulePermissionMapperUtil.PERMISSION_READ_WRITE)));
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(true);
        when(configParamsService.getBooleanParameterValue(IPConfigParamName.BAR_WEB_RATE_SHOPPING_ENABLED.value())).thenReturn(false);
        doReturn(Collections.emptyList()).when(spyService).searchForBarDecisionDTO(Mockito.eq(true), Mockito.any(PricingManagementCPSearchCriteria.class), Mockito.eq(true));
        BarDecisionDetailsData barDecisionDetailsData = spyService.getActiveProductDecisionDetails("2017-12-19", BAR_PRODUCT_ID);
        assertNotNull(barDecisionDetailsData);
        assertEquals(Constants.BAR_DECISION_VALUE_RATEOFDAY + "_CP", barDecisionDetailsData.getPricingType());
        CPBarDecisionDetails cpBarDecisionDetails = barDecisionDetailsData.getCpBarDecisionDetails();
        assertEquals(RoleModulePermissionMapperUtil.PERMISSION_WRITE, cpBarDecisionDetails.getCpBarOverridePermission());
        assertTrue(cpBarDecisionDetails.isCpBaseRoomTypeOnlyEnabled());
        assertFalse(barDecisionDetailsData.isCompetitorsDataAvailable());
        assertEquals(0, cpBarDecisionDetails.getCpBarOverrides().size());
    }

    private void assertCPBarOverrides(CPDecisionBAROutput cpDecisionBAROutput, List<CPBarOverride> barDecisions) {
        assertEquals(1, barDecisions.size());
        CPBarOverride cpBarOverride = barDecisions.get(0);
        assertEquals(cpDecisionBAROutput.getOverrideType().toString(), cpBarOverride.getOverrideType());
        assertEquals(cpDecisionBAROutput.getLengthOfStay(), cpBarOverride.getLengthOfStay());
        assertEquals(cpDecisionBAROutput.getAccomType().getName(), cpBarOverride.getRoomTypeName());
        assertEquals(cpDecisionBAROutput.getAccomType().getId(), cpBarOverride.getRoomTypeId());
        assertEquals(DateUtil.formatDate(cpDecisionBAROutput.getArrivalDate().toDate(), DateUtil.DATE_FORMAT_MM_DD_YYYY), cpBarOverride.getArrivalDate());
        assertEquals("1.00", cpBarOverride.getOldSpecificOverride());
        assertEquals(cpDecisionBAROutput.getCompetitorRate(), cpBarOverride.getCompetitor());
        assertEquals("1.00", cpBarOverride.getLrv());
        assertEquals("10.00", cpBarOverride.getFinalBAR());
        assertEquals("32.00", cpBarOverride.getFloorRate());
        assertEquals("50.00", cpBarOverride.getCeilingRate());
        assertEquals(DateUtil.getDayofWeek("2017-12-18"), cpBarOverride.getDow());
        assertEquals(cpDecisionBAROutput.getId(), cpBarOverride.getCpDecisionBarOutputId());
        assertEquals(cpDecisionBAROutput.getAccomType().getAccomClass().getId(), cpBarOverride.getRoomClassId());
        assertEquals(cpDecisionBAROutput.getAccomType().getAccomClass().getName(), cpBarOverride.getRoomClassName());
    }

    @Test
    public void getCPBarDecisionDetailsShouldNotFailIfNoBarDecisionsAvailable() {
        String occupancyDate = "2017-12-18";
        doReturn(Collections.emptyList()).when(spyService).searchForBarDecisionDTO(Mockito.eq(true), Mockito.any(PricingManagementCPSearchCriteria.class), Mockito.eq(true));
        BarDecisionDetailsData barDecisionDetailsData = spyService.getCPBarDecisionDetails(occupancyDate);
        assertNotNull(barDecisionDetailsData);
        assertNull(barDecisionDetailsData.getDow());
        assertNull(barDecisionDetailsData.getOccupancyDate());
        assertNull(barDecisionDetailsData.getPropertyOccupancyForecastPerc());
        CPBarDecisionDetails cpBarDecisionDetails = barDecisionDetailsData.getCpBarDecisionDetails();
        assertNotNull(cpBarDecisionDetails);
        assertEquals(0, cpBarDecisionDetails.getCpBarOverrides().size());
    }

    @Test
    public void shouldPopulateCPBarDecisionDetails() {
        String occupancyDate = "2017-12-18";
        CPDecisionBAROutput cpDecisionBAROutput = createCPDecisionBAROutput(occupancyDate, BigDecimal.ONE, null, null, Long.valueOf(1), 5, 1, 1, createAccomType(createAccomClass("Standard", 2), 1, "Test"));
        CPBARDecisionDTO cpbarDecisionDTO = new CPBARDecisionDTO();
        cpbarDecisionDTO.setDate(LocalDate.parse(occupancyDate));
        cpbarDecisionDTO.setOccupancyForecastPercentage(BigDecimal.TEN);
        cpbarDecisionDTO.setDecisions(Arrays.asList(cpDecisionBAROutput));

        PacmanWorkContextTestHelper.setup_SSOUser_PuneProperty_WorkContext();
        List<PricingBaseAccomType> pricingAccomTypes = new ArrayList<>();
        pricingAccomTypes.add(createPricingBaseAccomType());
        when(pricingConfigurationService.getAllBaseRoomTypes()).thenReturn(pricingAccomTypes);
        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(Arrays.asList(preparePricingAccomClass(cpDecisionBAROutput.getAccomType().getAccomClass())));
        when(roleServiceMock.getPropertyUserRole(anyString(), anyInt())).thenReturn(populateRole(WC_CLIENT_CODE_BLACKSTONE, "1"));
        when(rolePermissionServiceMock.getAccessibleRolePermissions()).thenReturn(asList(RoleModulePermissionMapperTest.prepareRolePermission("role1", PRICING, RoleModulePermissionMapperUtil.PERMISSION_READ_WRITE)));
        AccomTypeSupplement supplement = new AccomTypeSupplement();
        when(accomTypeSupplementService.getSupplement(cpDecisionBAROutput)).thenReturn(supplement);
        doReturn(Arrays.asList(cpbarDecisionDTO)).when(spyService).searchForBarDecisionDTO(Mockito.eq(true), Mockito.any(PricingManagementCPSearchCriteria.class), Mockito.eq(true));
        BarDecisionDetailsData barDecisionDetailsData = spyService.getCPBarDecisionDetails(occupancyDate);
        assertNotNull(barDecisionDetailsData);
        assertEquals(DateUtil.getDayofWeek(occupancyDate), barDecisionDetailsData.getDow());
        assertEquals(DateUtil.formatDate(LocalDate.parse(occupancyDate).toDate(), DateUtil.DATE_FORMAT_MM_DD_YYYY), barDecisionDetailsData.getOccupancyDate());
        assertEquals("10.00", barDecisionDetailsData.getPropertyOccupancyForecastPerc());
        assertEquals(1, barDecisionDetailsData.getCpBarDecisionDetails().getCpBarOverrides().size());
    }

    @Test
    public void testAddTaxAndSupplementsToDemandForecast() {
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        LocalDate arrivalDate = new LocalDate();
        cpDecisionBAROutput.setArrivalDate(arrivalDate);
        AccomType accomType = new AccomType();
        accomType.setId(1);
        AccomClass accomClass1 = new AccomClass();
        accomClass1.setId(45);
        accomType.setAccomClass(accomClass1);
        cpDecisionBAROutput.setAccomType(accomType);

        AccomClass accomClass = new AccomClass();
        Product product = buildBarProduct();
        cpDecisionBAROutput.setProduct(product);

        BigDecimal supplementValue = new BigDecimal("10.00");

        List<CPUnqualifedDemandForecastPrice> demandForecastPrices = new ArrayList<>();
        CPUnqualifedDemandForecastPrice price1 = new CPUnqualifedDemandForecastPrice();
        price1.setRate(new BigDecimal("100.00"));
        CPUnqualifedDemandForecastPrice price2 = new CPUnqualifedDemandForecastPrice();
        price2.setAccomClass(accomClass);
        price2.setRate(new BigDecimal("200.00"));
        demandForecastPrices.add(price1);
        demandForecastPrices.add(price2);

        Tax tax = new Tax();
        tax.setRoomTaxRate(new BigDecimal("15.00"));

        when(taxService.findTax()).thenReturn(tax);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRICING_SCREEN_OPTIMIZATION)).thenReturn(false);
        final AccomTypeSupplementValue value = new AccomTypeSupplementValue();
        value.setValue(supplementValue);
        when(accomTypeSupplementService.getSupplementValueFor(product.getId(), arrivalDate, 1)).thenReturn(value);
        List<PricingAccomClass> pricingAccomClasses = Arrays.asList(preparePricingAccomClass(accomClass), preparePricingAccomClass(accomType.getAccomClass()));
        when(pricingConfigurationService.getPricingAccomClasses()).thenReturn(pricingAccomClasses);
        //when(pricingConfigurationService.isAccomClassPriceExcluded(accomType.getAccomClass())).thenReturn(false);
        //when(pricingConfigurationService.isAccomClassPriceExcluded(accomClass)).thenReturn(true);
        CPConfigMergedOffset cpMergedOffset = new CPConfigMergedOffset();
        cpMergedOffset.setOffsetValue(BigDecimal.TEN);
        cpMergedOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        when(pricingConfigurationService.findMergedOffset(product.getId(), arrivalDate, accomType.getId())).thenReturn(cpMergedOffset);

        service.addTaxAndSupplementsToDemandForecast(product.getId(), demandForecastPrices, cpDecisionBAROutput, tax, pricingAccomClasses);

        assertEquals(new BigDecimal("135.00"), demandForecastPrices.get(0).getRate());
        assertEquals(new BigDecimal("250.00"), demandForecastPrices.get(1).getRate());
    }

    @Test
    public void testAddTaxAndSupplementsToDemandForecastForFixedPrice() {
        AccomClass accomClass = new AccomClass();
        accomClass.setId(1);
        AccomType accomType = new AccomType();
        accomType.setAccomClass(accomClass);

        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        cpDecisionBAROutput.setArrivalDate(LocalDate.now());
        cpDecisionBAROutput.setAccomType(accomType);
        cpDecisionBAROutput.setFinalBAR(new BigDecimal("500.00"));

        CPConfigMergedOffset cpMergedOffset = new CPConfigMergedOffset();
        cpMergedOffset.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        final Product product = new Product();
        product.setId(1);
        cpDecisionBAROutput.setProduct(product);

        List<CPUnqualifedDemandForecastPrice> demandForecastPrices = new ArrayList<>();
        CPUnqualifedDemandForecastPrice price1 = new CPUnqualifedDemandForecastPrice();
        CPUnqualifedDemandForecastPrice price2 = new CPUnqualifedDemandForecastPrice();
        price2.setRate(new BigDecimal("200.00"));

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(accomClass);

        when(accomTypeSupplementService.getSupplementValue(1, cpDecisionBAROutput.getArrivalDate(), cpDecisionBAROutput.getAccomType().getId())).thenReturn(BigDecimal.ZERO);

        service.addTaxAndSupplementsToDemandForecast(1, demandForecastPrices, cpDecisionBAROutput, null, Arrays.asList(pricingAccomClass));
        demandForecastPrices.forEach(forecastPrice -> assertEquals(cpDecisionBAROutput.getFinalBAR(), forecastPrice.getRate()));
    }

    @Test
    public void testAddTaxAndSupplementsToDemandForecastWithPercentOffset() {
        AccomClass accomClass = new AccomClass();
        accomClass.setId(1);
        AccomType accomType = new AccomType();
        accomType.setAccomClass(accomClass);

        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        cpDecisionBAROutput.setArrivalDate(LocalDate.now());
        cpDecisionBAROutput.setAccomType(accomType);
        final Product product = new Product();
        product.setId(1);
        cpDecisionBAROutput.setProduct(product);

        CPConfigMergedOffset cpMergedOffset = new CPConfigMergedOffset();
        cpMergedOffset.setOffsetMethod(OffsetMethod.PERCENTAGE);
        cpMergedOffset.setOffsetValue(BigDecimal.TEN);

        List<CPUnqualifedDemandForecastPrice> demandForecastPrices = new ArrayList<>();
        CPUnqualifedDemandForecastPrice price1 = new CPUnqualifedDemandForecastPrice();
        price1.setRate(new BigDecimal("100.00"));
        demandForecastPrices.add(price1);

        CPUnqualifedDemandForecastPrice price2 = new CPUnqualifedDemandForecastPrice();
        price2.setRate(new BigDecimal("200.00"));
        demandForecastPrices.add(price2);

        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(accomClass);

        when(pricingConfigurationService.findMergedOffset(1, cpDecisionBAROutput.getArrivalDate(), cpDecisionBAROutput.getAccomType().getId())).thenReturn(cpMergedOffset);
        when(accomTypeSupplementService.getSupplementValue(1, cpDecisionBAROutput.getArrivalDate(), cpDecisionBAROutput.getAccomType().getId())).thenReturn(BigDecimal.ZERO);

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.TEN);

        service.addTaxAndSupplementsToDemandForecast(1, demandForecastPrices, cpDecisionBAROutput, tax, Arrays.asList(pricingAccomClass));

        assertEquals(new BigDecimal("121.00"), demandForecastPrices.get(0).getRate());
        assertEquals(new BigDecimal("242.00"), demandForecastPrices.get(1).getRate());
    }

    private PricingAccomClass preparePricingAccomClass(AccomClass accomClass) {
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(accomClass);
        return pricingAccomClass;
    }

    private CPDecisionBAROutput createCPDecisionBAROutput(String date, BigDecimal specificOverride, BigDecimal floorOverride, BigDecimal ceilingOverride, Long id, Integer propertyId, Integer decisionId, Integer productId, AccomType accomType) {
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        cpDecisionBAROutput.setCompetitorRate("125.00");
        cpDecisionBAROutput.setFinalBAR(BigDecimal.TEN);
        cpDecisionBAROutput.setLrv(BigDecimal.ONE);
        cpDecisionBAROutput.setArrivalDate(LocalDate.parse(date));
        cpDecisionBAROutput.setLengthOfStay(1);
        cpDecisionBAROutput.setOverrideType(DecisionOverrideType.USER);
        cpDecisionBAROutput.setSpecificOverride(specificOverride);
        cpDecisionBAROutput.setFloorOverride(floorOverride);
        cpDecisionBAROutput.setCeilingOverride(ceilingOverride);
        cpDecisionBAROutput.setAccomType(accomType);
        cpDecisionBAROutput.setId(id);
        cpDecisionBAROutput.setPropertyId(propertyId);
        cpDecisionBAROutput.setDecisionId(decisionId);
        Product product = new Product();
        product.setId(productId);
        cpDecisionBAROutput.setProduct(product);
        return cpDecisionBAROutput;
    }

    private AccomType createAccomType(AccomClass accomClass, int id, String name) {
        AccomType accomType = new AccomType();
        accomType.setAccomClass(accomClass);
        accomType.setId(id);
        accomType.setName(name);
        return accomType;
    }

    private AccomClass createAccomClass(String name, int id) {
        AccomClass accomClass = new AccomClass();
        accomClass.setName(name);
        accomClass.setId(id);
        accomClass.setAccomTypes(new HashSet<>());
        return accomClass;
    }

    @Test
    public void testRemoveOverrideForCPBarOverrideWithApplyOverrideAcrossRoomTypesFlagAsTrue() {
        PacmanWorkContextTestHelper.setup_SSOUser_PuneProperty_WorkContext();
        final Integer userId = Integer.valueOf(WC_USER_ID_SSO);
        Decision decision = new Decision();
        decision.setId(1);
        CPDecisionBAROutput cpDecisionBAROutput = createCPDecisionBAROutput(TEST_DATE, BigDecimal.ONE, null, null, Long.valueOf(1), 5, 1, 1, createAccomType(createAccomClass("Standard", 2), 1, "Test"));
        CPDecisionBAROutput nonBaseRoomTypeCPDecisionBAROutput = createCPDecisionBAROutput(TEST_DATE, BigDecimal.ONE, null, null, Long.valueOf(2), 5, 1, 1, createAccomType(createAccomClass("Standard", 2), 2, "Test1"));
        doReturn(Arrays.asList(nonBaseRoomTypeCPDecisionBAROutput)).when(spyService).getNonBaseRoomTypeBarOverrides(cpDecisionBAROutput);
        doReturn(cpDecisionBAROutput).when(spyService).findCPDecisionBAROutputById(Long.valueOf(1));
        doNothing().when(spyService).removeOverride(cpDecisionContext, cpDecisionBAROutput, decision.getId(), userId, false, false, true);
        doNothing().when(spyService).removeOverride(cpDecisionContext, nonBaseRoomTypeCPDecisionBAROutput, decision.getId(), userId, false, true, true);
        when(cpDecisionContext.isBaseRoomTypeOnlyEnabled()).thenReturn(true);
        when(decisionService.createBAROverrideDecision()).thenReturn(decision);
        when(pricingConfigurationService.getCPDecisionContext(any(LocalDate.class), any(LocalDate.class), eq(false))).thenReturn(cpDecisionContext);
        CPBarOverride cpBarOverride = new CPBarOverride(cpDecisionBAROutput, BigDecimal.ONE, BigDecimal.TEN, false);
        cpBarOverride.setApplyOverrideAcrossRoomTypes(true);
        AccomTypeSupplementValue supplement = new AccomTypeSupplementValue();
        when(cpDecisionContext.getSupplementFor(cpDecisionBAROutput)).thenReturn(Optional.of(supplement));

        Response status = spyService.removeBarOverride(cpBarOverride);
        assertEquals("SUCCESS", status.getStatus());
        verify(spyService, times(1)).removeOverride(cpDecisionContext, cpDecisionBAROutput, decision.getId(), userId, false, true, true);
        verify(spyService, times(1)).removeOverride(cpDecisionContext, nonBaseRoomTypeCPDecisionBAROutput, decision.getId(), userId, false, true, true);
    }

    @Test
    public void testRemoveOverrideForCPBarOverrideWithBaseRoomTypeOnlyFlagAsFalse() {
        PacmanWorkContextTestHelper.setup_SSOUser_PuneProperty_WorkContext();
        final Integer userId = Integer.valueOf(WC_USER_ID_SSO);
        Decision decision = new Decision();
        decision.setId(1);
        CPDecisionBAROutput cpDecisionBAROutput = createCPDecisionBAROutput(TEST_DATE, BigDecimal.ONE, null, null, Long.valueOf(1), 5, 1, 1, createAccomType(createAccomClass("Standard", 2), 1, "Test"));
        CPDecisionBAROutput nonBaseRoomTypeCPDecisionBAROutput = createCPDecisionBAROutput(TEST_DATE, BigDecimal.ONE, null, null, Long.valueOf(2), 5, 1, 1, createAccomType(createAccomClass("Standard", 2), 2, "Test1"));
        doReturn(Arrays.asList(nonBaseRoomTypeCPDecisionBAROutput)).when(spyService).getNonBaseRoomTypeBarOverrides(cpDecisionBAROutput);
        doReturn(cpDecisionBAROutput).when(spyService).findCPDecisionBAROutputById(Long.valueOf(1));
        doNothing().when(spyService).removeOverride(cpDecisionContext, cpDecisionBAROutput, decision.getId(), userId, false, false, true);
        doNothing().when(spyService).removeOverride(cpDecisionContext, nonBaseRoomTypeCPDecisionBAROutput, decision.getId(), userId, false, true, true);
        when(cpDecisionContext.isBaseRoomTypeOnlyEnabled()).thenReturn(false);
        when(decisionService.createBAROverrideDecision()).thenReturn(decision);
        AccomTypeSupplementValue supplement = new AccomTypeSupplementValue();
        when(cpDecisionContext.getSupplementFor(cpDecisionBAROutput)).thenReturn(Optional.of(supplement));
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.CPBASE_ROOM_TYPE_ONLY_ENABLED.value())).thenReturn(false);
        when(pricingConfigurationService.getCPDecisionContext(any(LocalDate.class), any(LocalDate.class), eq(false))).thenReturn(cpDecisionContext);
        CPBarOverride cpBarOverride = new CPBarOverride(cpDecisionBAROutput, BigDecimal.ONE, BigDecimal.TEN, false);
        Response status = spyService.removeBarOverride(cpBarOverride);
        assertEquals("SUCCESS", status.getStatus());
        verify(spyService, times(1)).removeOverride(cpDecisionContext, cpDecisionBAROutput, decision.getId(), userId, false, true, true);
        verify(spyService, times(0)).removeOverride(cpDecisionContext, nonBaseRoomTypeCPDecisionBAROutput, decision.getId(), userId, false, true, true);
    }


    @Test
    public void testRemoveOverrideForCPBarOverrideWithApplyOverrideAcrossRoomTypesFlagAsFalse() {
        PacmanWorkContextTestHelper.setup_SSOUser_PuneProperty_WorkContext();
        final Integer userId = Integer.valueOf(WC_USER_ID_SSO);
        Decision decision = new Decision();
        decision.setId(1);
        CPDecisionBAROutput cpDecisionBAROutput = createCPDecisionBAROutput(TEST_DATE, BigDecimal.ONE, null, null, Long.valueOf(1), 5, 1, 1, createAccomType(createAccomClass("Standard", 2), 1, "Test"));
        CPDecisionBAROutput nonBaseRoomTypeCPDecisionBAROutput = createCPDecisionBAROutput(TEST_DATE, BigDecimal.ONE, null, null, Long.valueOf(2), 5, 1, 1, createAccomType(createAccomClass("Standard", 2), 2, "Test1"));
        doReturn(Arrays.asList(nonBaseRoomTypeCPDecisionBAROutput)).when(spyService).getNonBaseRoomTypeBarOverrides(cpDecisionBAROutput);
        doReturn(cpDecisionBAROutput).when(spyService).findCPDecisionBAROutputById(Long.valueOf(1));
        doNothing().when(spyService).removeOverride(cpDecisionContext, cpDecisionBAROutput, decision.getId(), userId, false, false, true);
        when(decisionService.createBAROverrideDecision()).thenReturn(decision);
        AccomTypeSupplementValue supplement = new AccomTypeSupplementValue();
        when(cpDecisionContext.getSupplementFor(cpDecisionBAROutput)).thenReturn(Optional.of(supplement));
        when(pricingConfigurationService.getCPDecisionContext(any(LocalDate.class), any(LocalDate.class), eq(false))).thenReturn(cpDecisionContext);
        CPBarOverride cpBarOverride = new CPBarOverride(cpDecisionBAROutput, BigDecimal.ONE, BigDecimal.TEN, false);
        Response status = spyService.removeBarOverride(cpBarOverride);
        assertEquals("SUCCESS", status.getStatus());
        verify(spyService, times(1)).removeOverride(cpDecisionContext, cpDecisionBAROutput, decision.getId(), userId, false, true, true);
        verify(spyService, never()).removeOverride(cpDecisionContext, nonBaseRoomTypeCPDecisionBAROutput, decision.getId(), userId, false, true, true);
    }

    @Test
    public void testRemoveOverrideForCPBarOverrideRestCall() {
        PacmanWorkContextTestHelper.setup_SSOUser_PuneProperty_WorkContext();
        final Integer userId = Integer.valueOf(WC_USER_ID_SSO);
        Decision decision = new Decision();
        decision.setId(1);
        AccomClass standard = createAccomClass("Standard", 2);
        standard.setAccomTypes(new HashSet<>());
        AccomType accomType = setUpAccomTypeAndPricingAccomClass(standard);
        CPDecisionBAROutput cpDecisionBAROutput = createCPDecisionBAROutput(TEST_DATE, BigDecimal.ONE, null, null, Long.valueOf(1), 5, 1, 1, accomType);
        CPDecisionBAROutput nonBaseRoomTypeCPDecisionBAROutput = createCPDecisionBAROutput(TEST_DATE, BigDecimal.ONE, null, null, Long.valueOf(2), 5, 1, 1, createAccomType(createAccomClass("Standard", 2), 2, "Test1"));
        doReturn(Arrays.asList(nonBaseRoomTypeCPDecisionBAROutput)).when(spyService).getNonBaseRoomTypeBarOverrides(cpDecisionBAROutput);
        doReturn(cpDecisionBAROutput).when(spyService).findCPDecisionBAROutputById(Long.valueOf(1));
        doNothing().when(spyService).removeOverride(cpDecisionContext, cpDecisionBAROutput, decision.getId(), userId, false, false, true);
        when(decisionService.createBAROverrideDecision()).thenReturn(decision);
        when(pricingConfigurationService.getCPDecisionContext(any(LocalDate.class), any(LocalDate.class), eq(false))).thenReturn(cpDecisionContext);
        Product product = new Product();
        product.setId(1);
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(product);
        when(crudService.findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN_FOR_ACCOM_TYPES,
                CPDecisionBAROutput.params(product, LocalDate.parse(TEST_DATE), LocalDate.parse(TEST_DATE), Collections.singletonList(accomType))))
                .thenReturn(Collections.singletonList(cpDecisionBAROutput));
        AccomTypeSupplementValue supplement = new AccomTypeSupplementValue();
        when(cpDecisionContext.getSupplementFor(cpDecisionBAROutput)).thenReturn(Optional.of(supplement));

        javax.ws.rs.core.Response status = spyService.removeBAROverrideForRoomType(1, TEST_DATE, TEST_DATE);

        assertEquals("SUCCESS", status.getEntity().toString());
        verify(spyService).removeOverride(cpDecisionContext, cpDecisionBAROutput, decision.getId(), userId, false, true, true);
        verify(syncEventAggregatorService).registerSyncEvent(SyncEvent.CONTINUOUS_PRICING_SPECIFIC_OVERRIDE_REMOVED);
        verify(spyService, never()).removeOverride(cpDecisionContext, nonBaseRoomTypeCPDecisionBAROutput, decision.getId(), userId, false, true, true);
    }

    private AccomType setUpAccomTypeAndPricingAccomClass(AccomClass standard) {
        AccomType accomType = createAccomType(standard, 1, "Test");
        standard.getAccomTypes().add(accomType);
        when(crudService.find(AccomType.class, 1)).thenReturn(accomType);
        setPricingAccomClass(standard, accomType);
        return accomType;
    }


    @Test
    public void testSaveCPBarOverride() {
        CPDecisionBAROutput cpDecisionBAROutput = prepareCPDecisionBAROutput();
        when(crudService.find(any(Class.class), any(Long.class))).thenReturn(cpDecisionBAROutput);
        when(pricingConfigurationService.getCPDecisionContext(any(LocalDate.class), any(LocalDate.class), eq(false))).thenReturn(cpDecisionContext);
        when(accomTypeSupplementService.getSupplementValue(any(Integer.class), any(LocalDate.class), any(Integer.class))).thenReturn(new BigDecimal("0"));
        CPDecisionBAROutputOverride cpDecisionBAROutputOverride = prepareCPDecisionBAROutputOverride();
        doReturn(cpDecisionBAROutputOverride).when(spyService).createCPDecisionBAROutputOverride(any(CPDecisionContext.class), any(CPDecisionBAROutput.class), any(DecisionOverrideType.class), any(DecisionOverrideType.class),
                any(BigDecimal.class), any(BigDecimal.class), any(BigDecimal.class), any(BigDecimal.class), any(BigDecimal.class));
        doNothing().when(spyService).saveOverrideForCPDecision(any(CPDecisionContext.class), any(CPDecisionBAROutputOverride.class), any(CPDecisionBAROutput.class), any(boolean.class));
        doNothing().when(spyService).appendNotes(any(CPBarOverride.class));
        CPBarOverride cpBarOverride = prepareCPBarOverride(false);
        AccomTypeSupplementValue supplement = new AccomTypeSupplementValue();
        when(cpDecisionContext.getSupplementFor(cpDecisionBAROutput)).thenReturn(Optional.of(supplement));

        Response response = spyService.saveCPBarOverride(cpBarOverride);
        assertEquals("SUCCESS", response.getStatus());
        verify(spyService, times(1)).saveCPBarOverride(cpBarOverride);
        verify(spyService, times(1)).saveOverrideForCPDecision(any(CPDecisionContext.class), any(CPDecisionBAROutputOverride.class), any(CPDecisionBAROutput.class), any(boolean.class));
        verify(spyService, never()).getNonBaseRoomTypeBarOverrides(cpDecisionBAROutput);
    }

    @Test
    public void testsaveCeilingFloorBAROverrideForRoomTypeRestCall() {
        CPDecisionBAROutput cpDecisionBAROutput = prepareCPDecisionBAROutput();
        CPDecisionBAROutput nonBaseRoomTypeCPDecisionBAROutput = prepareCPDecisionBAROutput();
        AccomType accomType = setUpAccomTypeAndPricingAccomClass(createAccomClass("STD", 2));
        when(accomTypeSupplementService.getSupplementValue(any(Integer.class), any(LocalDate.class), any(Integer.class))).thenReturn(new BigDecimal("0"));
        CPDecisionBAROutputOverride cpDecisionBAROutputOverride = prepareCPDecisionBAROutputOverride();
        doReturn(cpDecisionBAROutputOverride).when(spyService).createCPDecisionBAROutputOverride(any(CPDecisionContext.class), any(CPDecisionBAROutput.class), any(DecisionOverrideType.class), any(DecisionOverrideType.class),
                any(BigDecimal.class), any(BigDecimal.class), any(BigDecimal.class), any(BigDecimal.class), any(BigDecimal.class));
        doNothing().when(spyService).saveOverrideForCPDecision(any(CPDecisionContext.class), any(CPDecisionBAROutputOverride.class), any(CPDecisionBAROutput.class), any(boolean.class));
        doNothing().when(spyService).appendNotes(any(CPBarOverride.class));
        Product product = new Product();
        product.setId(1);
        when(pricingConfigurationService.getCPDecisionContext(any(LocalDate.class), any(LocalDate.class), eq(false))).thenReturn(cpDecisionContext);
        when(crudService.findByNamedQuerySingleResult(Product.GET_SYSTEM_DEFAULT)).thenReturn(product);
        when(crudService.findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN_FOR_ACCOM_TYPES,
                CPDecisionBAROutput.params(product, LocalDate.parse(TEST_DATE), LocalDate.parse(TEST_DATE), Collections.singletonList(accomType))))
                .thenReturn(Collections.singletonList(cpDecisionBAROutput));
        doReturn(Arrays.asList(nonBaseRoomTypeCPDecisionBAROutput)).when(spyService).getNonBaseRoomTypeBarOverrides(cpDecisionBAROutput);
        AccomTypeSupplementValue supplement = new AccomTypeSupplementValue();
        when(cpDecisionContext.getSupplementFor(cpDecisionBAROutput)).thenReturn(Optional.of(supplement));

        javax.ws.rs.core.Response response = spyService.saveCeilingFloorBAROverrideForRoomType(1, TEST_DATE, TEST_DATE, "29", "629");

        assertEquals("SUCCESS", response.getEntity());
        verify(spyService).saveOverrideForCPDecision(any(CPDecisionContext.class), any(CPDecisionBAROutputOverride.class), any(CPDecisionBAROutput.class), any(boolean.class));
        verify(spyService).getNonBaseRoomTypeBarOverrides(cpDecisionBAROutput);
    }

    @Test
    public void testAppendNotesWhenExceptionIsEncountered() {
        CPBarOverride cpBarOverride = new CPBarOverride();
        cpBarOverride.setNotes("hi");
        cpBarOverride.setArrivalDate("2015");
        spyService.appendNotes(cpBarOverride);
        verify(notesService, never()).appendNote(any(String.class), any(Date.class), any(String.class));
    }

    @Test
    public void testAppendNotesWhenNotesAreNotSupplied() {
        CPBarOverride cpBarOverride = new CPBarOverride();
        cpBarOverride.setNotes(null);
        cpBarOverride.setArrivalDate("09-10-2015");
        spyService.appendNotes(cpBarOverride);
        verify(notesService, never()).appendNote(any(String.class), any(Date.class), any(String.class));
    }

    @Test
    public void testSaveCPBarOverrideWhenSpecificOverrideIsDone() {
        WorkContextType wc = new WorkContextType();
        PacmanWorkContextHelper.setWorkContext(wc);
        wc.setUserId("11403");
        CPDecisionBAROutput cpDecisionBAROutput = prepareCPDecisionBAROutput();
        when(crudService.find(any(Class.class), any(Long.class))).thenReturn(cpDecisionBAROutput);
        when(pricingConfigurationService.getCPDecisionContext(any(LocalDate.class), any(LocalDate.class), eq(false))).thenReturn(cpDecisionContext);
        when(cpDecisionContext.getSupplement(cpDecisionBAROutput)).thenReturn(new BigDecimal("0"));
        CPDecisionBAROutputOverride cpDecisionBAROutputOverride = prepareCPDecisionBAROutputOverride();
        doReturn(cpDecisionBAROutputOverride).when(spyService).createCPDecisionBAROutputOverride(any(CPDecisionContext.class), any(CPDecisionBAROutput.class), any(DecisionOverrideType.class), any(DecisionOverrideType.class),
                any(BigDecimal.class), any(BigDecimal.class), any(BigDecimal.class), any(BigDecimal.class), any(BigDecimal.class));

        doNothing().when(spyService).saveOverrideForCPDecision(any(CPDecisionContext.class), any(CPDecisionBAROutputOverride.class), any(CPDecisionBAROutput.class), any(boolean.class));
        doNothing().when(spyService).save(any(List.class));
        doNothing().when(spyService).appendNotes(any(CPBarOverride.class));
        doReturn(Arrays.asList(cpDecisionBAROutput)).when(spyService).getNonBaseRoomTypeBarOverrides(any(CPDecisionBAROutput.class));
        CPBarOverride cpBarOverride = prepareCPBarOverride(true);
        AccomTypeSupplementValue supplement = new AccomTypeSupplementValue();
        when(cpDecisionContext.getSupplementFor(cpDecisionBAROutput)).thenReturn(Optional.of(supplement));
        Response response = spyService.saveCPBarOverride(cpBarOverride);
        assertEquals("SUCCESS", response.getStatus());
        verify(spyService, times(1)).saveCPBarOverride(cpBarOverride);
        verify(spyService, times(1)).save(any(List.class));
        //verify(spyService,times(1)).handleSpecificOverride(any(CPBarOverride.class),any(Map.class),any(CPManagementService.Overrides.class));
        verify(spyService, never()).handleFloorAndCeilingOverrides(any(CPDecisionContext.class), any(CPDecisionBAROutput.class), any(Map.class), any(CPManagementService.Overrides.class));
        verify(spyService, times(1)).getNonBaseRoomTypeBarOverrides(cpDecisionBAROutput);
    }


    @Test
    public void testSaveCPBarOverrideWhenCeilingOverrideIsDone() {
        CPDecisionBAROutput cpDecisionBAROutput = prepareCPDecisionBAROutputForCeilingAndFloor(true);
        when(crudService.find(any(Class.class), any(Long.class))).thenReturn(cpDecisionBAROutput);
        when(pricingConfigurationService.getCPDecisionContext(any(LocalDate.class), any(LocalDate.class), eq(false))).thenReturn(cpDecisionContext);
        when(cpDecisionContext.getSupplement(any(CPDecisionBAROutput.class))).thenReturn(BigDecimal.ZERO);
        CPDecisionBAROutputOverride cpDecisionBAROutputOverride = prepareCPDecisionBAROutputOverride();
        doReturn(cpDecisionBAROutputOverride).when(spyService).createCPDecisionBAROutputOverride(any(CPDecisionContext.class), any(CPDecisionBAROutput.class), any(DecisionOverrideType.class), any(DecisionOverrideType.class),
                any(BigDecimal.class), any(BigDecimal.class), any(BigDecimal.class), any(BigDecimal.class), any(BigDecimal.class));

        doNothing().when(spyService).saveOverrideForCPDecision(any(CPDecisionContext.class), any(CPDecisionBAROutputOverride.class), any(CPDecisionBAROutput.class), any(boolean.class));
        doNothing().when(spyService).save(any(List.class));
        doNothing().when(spyService).appendNotes(any(CPBarOverride.class));
        doReturn(Arrays.asList(cpDecisionBAROutput)).when(spyService).getNonBaseRoomTypeBarOverrides(any(CPDecisionBAROutput.class));
        CPBarOverride cpBarOverride = prepareCPBarOverrideForCeilingAndFloorOverride(true, true);
        AccomTypeSupplementValue supplement = new AccomTypeSupplementValue();
        when(cpDecisionContext.getSupplementFor(cpDecisionBAROutput)).thenReturn(Optional.of(supplement));
        Response response = spyService.saveCPBarOverride(cpBarOverride);
        assertEquals("SUCCESS", response.getStatus());
        verify(spyService, times(1)).saveCPBarOverride(cpBarOverride);
        verify(spyService, times(1)).save(any(List.class));
        //verify(spyService,never()).handleSpecificOverride(any(CPBarOverride.class),any(Map.class),any(CPManagementService.Overrides.class));
        verify(spyService, times(1)).handleFloorAndCeilingOverrides(any(CPDecisionContext.class), any(CPDecisionBAROutput.class), any(Map.class), any(CPManagementService.Overrides.class));
        verify(spyService, times(1)).getNonBaseRoomTypeBarOverrides(cpDecisionBAROutput);
    }

    @Test
    public void testSaveCPBarOverrideWhenCeilingAndFloorOverrideIsDone() {
        CPDecisionBAROutput cpDecisionBAROutput = prepareCPDecisionBAROutputForCeilingAndFloor(false);
        when(crudService.find(any(Class.class), any(Long.class))).thenReturn(cpDecisionBAROutput);
        when(pricingConfigurationService.getCPDecisionContext(any(LocalDate.class), any(LocalDate.class), eq(false))).thenReturn(cpDecisionContext);
        CPDecisionBAROutputOverride cpDecisionBAROutputOverride = prepareCPDecisionBAROutputOverride();
        doReturn(cpDecisionBAROutputOverride).when(spyService).createCPDecisionBAROutputOverride(any(CPDecisionContext.class), any(CPDecisionBAROutput.class), any(DecisionOverrideType.class), any(DecisionOverrideType.class),
                any(BigDecimal.class), any(BigDecimal.class), any(BigDecimal.class), any(BigDecimal.class), any(BigDecimal.class));

        doNothing().when(spyService).saveOverrideForCPDecision(any(CPDecisionContext.class), any(CPDecisionBAROutputOverride.class), any(CPDecisionBAROutput.class), any(boolean.class));
        doNothing().when(spyService).save(any(List.class));
        doNothing().when(spyService).appendNotes(any(CPBarOverride.class));
        doReturn(Arrays.asList(cpDecisionBAROutput)).when(spyService).getNonBaseRoomTypeBarOverrides(any(CPDecisionBAROutput.class));
        CPBarOverride cpBarOverride = prepareCPBarOverrideForCeilingAndFloorOverride(true, false);
        AccomTypeSupplementValue supplement = new AccomTypeSupplementValue();
        when(cpDecisionContext.getSupplementFor(cpDecisionBAROutput)).thenReturn(Optional.of(supplement));
        Response response = spyService.saveCPBarOverride(cpBarOverride);
        assertEquals("SUCCESS", response.getStatus());
        verify(spyService, times(1)).saveCPBarOverride(cpBarOverride);
        verify(spyService, times(1)).save(any(List.class));
        //verify(spyService,never()).handleSpecificOverride(any(CPBarOverride.class),any(Map.class),any(CPManagementService.Overrides.class));
        verify(spyService, times(1)).handleFloorAndCeilingOverrides(any(CPDecisionContext.class), any(CPDecisionBAROutput.class), any(Map.class), any(CPManagementService.Overrides.class));
        verify(spyService, times(1)).getNonBaseRoomTypeBarOverrides(cpDecisionBAROutput);
    }

    @Test
    public void addTaxAndSupplementsToDemandForecastWithTaxAndSupplementsNoOffsets() {
        CPDecisionBAROutput cpDecisionBAROutput = prepareCPDecisionBAROutput();

        List<CPUnqualifedDemandForecastPrice> cpUnqualifedDemandForecastPrices = new ArrayList<>();
        cpUnqualifedDemandForecastPrices.add(createDemandForecastPrice(LocalDate.now(), new BigDecimal("160.05")));
        cpUnqualifedDemandForecastPrices.add(createDemandForecastPrice(LocalDate.now(), new BigDecimal("260.00")));

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.TEN);

        AccomType accomType = buildAccomType();
        cpDecisionBAROutput.setAccomType(accomType);

        List<PricingAccomClass> pricingAccomClasses = new ArrayList<>();
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(accomType.getAccomClass());
        pricingAccomClass.setAccomType(accomType);
        pricingAccomClasses.add(pricingAccomClass);
        final AccomTypeSupplementValue accomTypeSupplementValue = new AccomTypeSupplementValue();
        accomTypeSupplementValue.setValue(new BigDecimal("4.95"));
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRICING_SCREEN_OPTIMIZATION)).thenReturn(false);
        when(accomTypeSupplementService.getSupplementValueFor(any(Integer.class), any(LocalDate.class), any(Integer.class))).thenReturn(accomTypeSupplementValue);

        service.addTaxAndSupplementsToDemandForecast(1, cpUnqualifedDemandForecastPrices, cpDecisionBAROutput, tax, pricingAccomClasses);

        assertEquals(new BigDecimal("181.01"), cpUnqualifedDemandForecastPrices.get(0).getRate());
        assertEquals(new BigDecimal("290.95"), cpUnqualifedDemandForecastPrices.get(1).getRate());
    }

    @Test
    public void addTaxAndSupplementsToDemandForecastWithTaxAndSupplementsFixedOffsets() {
        CPDecisionBAROutput cpDecisionBAROutput = prepareCPDecisionBAROutput();

        List<CPUnqualifedDemandForecastPrice> cpUnqualifedDemandForecastPrices = new ArrayList<>();
        cpUnqualifedDemandForecastPrices.add(createDemandForecastPrice(LocalDate.now(), new BigDecimal("160.05")));
        cpUnqualifedDemandForecastPrices.add(createDemandForecastPrice(LocalDate.now(), new BigDecimal("260.00")));

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.TEN);

        AccomType accomType = buildAccomType();
        cpDecisionBAROutput.setAccomType(accomType);

        List<PricingAccomClass> pricingAccomClasses = new ArrayList<>();
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(accomType.getAccomClass());
        pricingAccomClass.setAccomType(accomType);
        pricingAccomClasses.add(pricingAccomClass);

        CPConfigMergedOffset cpMergedOffset = new CPConfigMergedOffset();
        cpMergedOffset.setOffsetValue(new BigDecimal("27.27"));
        cpMergedOffset.setOffsetMethod(OffsetMethod.FIXED_OFFSET);
        AccomTypeSupplementValue accomTypeSupplementValue = getAccomTypeSupplementValue(java.time.LocalDate.now(), OffsetMethod.FIXED_OFFSET, new BigDecimal("4.95"));
        when(pricingConfigurationService.findMergedOffset(1, cpDecisionBAROutput.getArrivalDate(), cpDecisionBAROutput.getAccomType().getId())).thenReturn(cpMergedOffset);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRICING_SCREEN_OPTIMIZATION)).thenReturn(false);
        when(accomTypeSupplementService.getSupplementValueFor(any(Integer.class), any(LocalDate.class), any(Integer.class))).thenReturn(accomTypeSupplementValue);

        service.addTaxAndSupplementsToDemandForecast(1, cpUnqualifedDemandForecastPrices, cpDecisionBAROutput, tax, pricingAccomClasses);

        assertEquals(new BigDecimal("208.28"), cpUnqualifedDemandForecastPrices.get(0).getRate());
        assertEquals(new BigDecimal("318.22"), cpUnqualifedDemandForecastPrices.get(1).getRate());
    }

    @Test
    public void addTaxAndSupplementsToDemandForecastWithTaxAndSupplementsPercentOffsets() {
        CPDecisionBAROutput cpDecisionBAROutput = prepareCPDecisionBAROutput();

        List<CPUnqualifedDemandForecastPrice> cpUnqualifedDemandForecastPrices = new ArrayList<>();
        cpUnqualifedDemandForecastPrices.add(createDemandForecastPrice(LocalDate.now(), new BigDecimal("160.05")));
        cpUnqualifedDemandForecastPrices.add(createDemandForecastPrice(LocalDate.now(), new BigDecimal("260.00")));

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.TEN);

        AccomType accomType = buildAccomType();
        cpDecisionBAROutput.setAccomType(accomType);

        List<PricingAccomClass> pricingAccomClasses = new ArrayList<>();
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(accomType.getAccomClass());
        pricingAccomClass.setAccomType(accomType);
        pricingAccomClasses.add(pricingAccomClass);

        CPConfigMergedOffset cpMergedOffset = new CPConfigMergedOffset();
        cpMergedOffset.setOffsetValue(new BigDecimal("15.00"));
        cpMergedOffset.setOffsetMethod(OffsetMethod.PERCENTAGE);
        when(pricingConfigurationService.findMergedOffset(1, cpDecisionBAROutput.getArrivalDate(), cpDecisionBAROutput.getAccomType().getId())).thenReturn(cpMergedOffset);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRICING_SCREEN_OPTIMIZATION)).thenReturn(true);
        AccomTypeSupplementValue accomTypeSupplementValue = getAccomTypeSupplementValue(java.time.LocalDate.now(), OffsetMethod.PERCENTAGE, new BigDecimal("4.95"));
        when(accomTypeSupplementService.getSupplementValueFor(any(Integer.class), any(LocalDate.class), any(Integer.class))).thenReturn(accomTypeSupplementValue);

        service.addTaxAndSupplementsToDemandForecast(1, cpUnqualifedDemandForecastPrices, cpDecisionBAROutput, tax, pricingAccomClasses);

        assertEquals(new BigDecimal("207.41"), cpUnqualifedDemandForecastPrices.get(0).getRate());
        assertEquals(new BigDecimal("333.85"), cpUnqualifedDemandForecastPrices.get(1).getRate());
    }

    @Test
    public void addTaxAndSupplementsToDemandForecastWithTaxAndSupplementsSetOffsets() {
        CPDecisionBAROutput cpDecisionBAROutput = prepareCPDecisionBAROutput();
        cpDecisionBAROutput.setFinalBAR(new BigDecimal("500.00"));

        List<CPUnqualifedDemandForecastPrice> cpUnqualifedDemandForecastPrices = new ArrayList<>();
        cpUnqualifedDemandForecastPrices.add(createDemandForecastPrice(LocalDate.now(), new BigDecimal("160.05")));
        cpUnqualifedDemandForecastPrices.add(createDemandForecastPrice(LocalDate.now(), new BigDecimal("260.00")));

        Tax tax = new Tax();
        tax.setRoomTaxRate(BigDecimal.TEN);

        AccomType accomType = buildAccomType();
        cpDecisionBAROutput.setAccomType(accomType);

        List<PricingAccomClass> pricingAccomClasses = new ArrayList<>();
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomClass(accomType.getAccomClass());
        pricingAccomClass.setAccomType(accomType);
        pricingAccomClass.setPriceExcluded(true);
        pricingAccomClasses.add(pricingAccomClass);

        CPConfigMergedOffset cpMergedOffset = new CPConfigMergedOffset();
        cpMergedOffset.setOffsetValue(new BigDecimal("20.00"));
        cpMergedOffset.setOffsetMethod(OffsetMethod.FIXED_PRICE);
        when(pricingConfigurationService.findMergedOffset(1, cpDecisionBAROutput.getArrivalDate(), cpDecisionBAROutput.getAccomType().getId())).thenReturn(cpMergedOffset);
        when(configParamsService.getBooleanParameterValue(PreProductionConfigParamName.ENABLE_PRICING_SCREEN_OPTIMIZATION)).thenReturn(true);
        AccomTypeSupplementValue accomTypeSupplementValue = getAccomTypeSupplementValue(java.time.LocalDate.now(), OffsetMethod.FIXED_PRICE, new BigDecimal("4.95"));
        when(accomTypeSupplementService.getSupplementValueFor(any(Integer.class), any(LocalDate.class), any(Integer.class))).thenReturn(accomTypeSupplementValue);

        service.addTaxAndSupplementsToDemandForecast(1, cpUnqualifedDemandForecastPrices, cpDecisionBAROutput, tax, pricingAccomClasses);

        assertEquals(new BigDecimal("500.00"), cpUnqualifedDemandForecastPrices.get(0).getRate());
        assertEquals(new BigDecimal("500.00"), cpUnqualifedDemandForecastPrices.get(1).getRate());
        verify(accomTypeSupplementService, times(1)).getSupplementValueFor(any(Integer.class), any(LocalDate.class), any(Integer.class));
    }

    @Test
    public void testSaveOverrides_InvalidProductId() {
        OverrideRequest request = getOverrideRequest("100",null, 101,"2023-10-01", "2023-10-01", OverrideRequest.OperationType.ADD);
        List<OverrideRequest> overrideRequests = Collections.singletonList(request);
        Product BAR = buildBarProduct();

        when(crudService.findByNamedQuerySingleResult(Product.GET_BY_ID, QueryParameter.with("productId", 1).parameters())).thenReturn(BAR);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.of(2022, 10, 1));

        ResponseEntity<ResponseDTO> responseDTO = service.performOperationOnOverride("1", 2, overrideRequests);

        assertEquals(HttpStatus.SC_BAD_REQUEST, responseDTO.getBody().getStatus());
        assertEquals(Objects.requireNonNull(responseDTO.getBody().getMessage()),"Product not found for id: 2");
    }

    @Test
    public void testDeleteOverrides_InvalidUserId() {
        OverrideRequest request = getOverrideRequest(null, null, 1, "2023-10-01", "2023-10-01", OverrideRequest.OperationType.REMOVE);
        List<OverrideRequest> overrideRequests = Collections.singletonList(request);
        Product BAR = buildBarProduct();
        AccomClass accomClass = createAccomClass("STANDARD", 1);

        when(crudService.findByNamedQuerySingleResult(Product.GET_BY_ID, QueryParameter.with("productId", 1).parameters()))
                .thenReturn(BAR);
        when(crudService.findByNamedQuery(AccomClass.ALL, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(List.of(accomClass));

        ResponseDTO responseDTO = service.deleteOverrides("12a", 1, overrideRequests);

        assertEquals(HttpStatus.SC_BAD_REQUEST, responseDTO.getStatus());
        assertEquals(Objects.requireNonNull(responseDTO.getMessage()), "Invalid user ID: 12a");
    }

    @Test
    public void testDeleteOverrides_InvalidAccomClass() {
        OverrideRequest request = getOverrideRequest(null, null, 101, "2023-10-01", "2023-10-01", OverrideRequest.OperationType.REMOVE);
        List<OverrideRequest> overrideRequests = Collections.singletonList(request);
        AccomClass accomClass = createAccomClass("STANDARD", 1);
        Product BAR = buildBarProduct();

        when(crudService.findByNamedQuerySingleResult(Product.GET_BY_ID, QueryParameter.with("productId", 1).parameters())).thenReturn(BAR);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.of(2022, 10, 1));
        when(crudService.findByNamedQuery(AccomClass.ALL, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(List.of(accomClass));
        ResponseDTO responseDTO = service.deleteOverrides("1", 1, overrideRequests);

        assertEquals(HttpStatus.SC_BAD_REQUEST, responseDTO.getStatus());
        assertEquals(Objects.requireNonNull(responseDTO.getMessage()), "Accom Class Ids: [101] are not mapped to Product");
    }

    @Test
    public void testDeleteBAROverrides() {
        Product barProduct = buildBarProduct();
        AccomClass accomClass = createAccomClass("STANDARD", 1);
        OverrideRequest override = getOverrideRequest(null, null, 1, "2023-10-01", "2023-10-05", OverrideRequest.OperationType.REMOVE);

        CPDecisionBAROutput barOutput = new CPDecisionBAROutput();

        when(crudService.findByNamedQuerySingleResult(Product.GET_BY_ID, QueryParameter.with("productId", 1).parameters()))
                .thenReturn(barProduct);
        when(crudService.findByNamedQuery(AccomClass.ALL, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(List.of(accomClass));
        when(decisionService.createBAROverrideDecision()).thenReturn(new Decision());
        when(crudService.findByNamedQuery(CPDecisionBAROutput.GET_OVERRIDES_FOR_ACCOM_CLASSES_WITH_DATES_ACTUALLY_BETWEEN,
                QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId())
                        .and("product", barProduct)
                        .and(START_DATE, override.getStartDate())
                        .and(END_DATE, override.getEndDate())
                        .and(ACCOM_CLASS_IDS, Collections.singletonList(accomClass)).parameters())).thenReturn(List.of(barOutput));

        ResponseDTO response = service.deleteOverrides("1", 1, List.of(override));

        assertEquals(HttpStatus.SC_NO_CONTENT, response.getStatus());
        assertEquals("Successful", response.getMessage().trim());
    }

    @Test
    public void testDeleteOptimisedAgileOverrides() {
        Product productA = createLinkedProduct("Product A", 1, true);
        AccomClass accomClass = createAccomClass("STANDARD", 1);
        AccomType accomType = createAccomType(accomClass, 1, "STD");
        ProductAccomType productAccomType = createProductAccomType(productA, accomType);

        OverrideRequest override1 = getOverrideRequest(null, null, 1, "2023-10-01", "2023-10-02", OverrideRequest.OperationType.REMOVE);
        List<OverrideRequest> requests = List.of(override1);

        when(crudService.findByNamedQuerySingleResult(Product.GET_BY_ID, QueryParameter.with("productId", 1).parameters())).thenReturn(productA);
        when(crudService.findByNamedQuery(AccomClass.ALL, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(List.of(accomClass));
        when(crudService.findByNamedQuery(ProductAccomType.HAVING_PRODUCT_STATUS_ACTIVE)).thenReturn(List.of(productAccomType));
        when(productManagementService.getOptimizationLevel()).thenReturn(OptimizationLevel.PER_ROOM_CLASS);
        ResponseDTO response = service.deleteOverrides("1", 1, requests);

        verify(productManagementService, times(1)).deleteExistingOverridesForOptimized(eq(accomClass), eq(productA), any(), any());
        assertEquals(HttpStatus.SC_NO_CONTENT, response.getStatus());
        assertEquals("Successful", response.getMessage().trim());
    }

    @Test
    public void testDeleteNonOptimisedAgileOverrides() {
        Product productA = createLinkedProduct("Product A", 1, false); // Not optimized
        productA.setId(1);
        AccomClass accomClass = createAccomClass("STANDARD", 1);
        AccomType accomType = createAccomType(accomClass, 1, "STD");
        ProductAccomType productAccomType = createProductAccomType(productA, accomType);

        OverrideRequest override = getOverrideRequest(null, null, 1, "2023-10-01", "2023-10-02", OverrideRequest.OperationType.REMOVE);
        List<OverrideRequest> requests = List.of(override);

        when(crudService.findByNamedQuerySingleResult(Product.GET_BY_ID, QueryParameter.with("productId", 1).parameters()))
                .thenReturn(productA);
        when(crudService.findByNamedQuery(AccomClass.ALL, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters()))
                .thenReturn(List.of(accomClass));
        when(crudService.findByNamedQuery(ProductAccomType.HAVING_PRODUCT_STATUS_ACTIVE)).thenReturn(List.of(productAccomType));

        ResponseDTO response = service.deleteOverrides("1", 1, requests);

        verify(productManagementService, times(1)).deleteExistingOverridesForNonOptimized(eq(1), eq(productA), any(), any());
        assertEquals(HttpStatus.SC_NO_CONTENT, response.getStatus());
        assertEquals("Successful", response.getMessage().trim());
    }

    @Test
    void testSaveOverrides_ForLinkedProduct() {
        Product productA = createLinkedProduct("Product A", 1, true);
        AccomClass accomClass = createAccomClass("STANDARD", 1);
        AccomType accomType = createAccomType(accomClass, 1, "STD");
        ProductAccomType productAccomType = createProductAccomType(productA,accomType);

        OverrideRequest override1 = getOverrideRequest("100", "200", 1, "2023-10-01", "2023-10-02", OverrideRequest.OperationType.ADD);
        OverrideRequest override2 = getOverrideRequest("150", "250", 1, "2023-10-03", "2023-10-03", OverrideRequest.OperationType.ADD);
        List<OverrideRequest> requests = List.of(override1, override2);

        Product BAR = buildBarProduct();
        when(crudService.findByNamedQuerySingleResult(Product.GET_BY_ID, QueryParameter.with("productId", 1).parameters())).thenReturn(BAR);
        when(crudService.findByNamedQuery(AccomClass.ALL, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(List.of(accomClass));
        when(crudService.findByNamedQuery(ProductAccomType.HAVING_PRODUCT_STATUS_ACTIVE)).thenReturn(List.of(productAccomType));
        when(productManagementService.getOptimizationLevel()).thenReturn(OptimizationLevel.PER_ROOM_CLASS);
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.of(2022, 10, 1));
        ResponseDTO response = service.saveOrUpdateOverrides("1", 1, requests);

        assertEquals(HttpStatus.SC_NO_CONTENT, response.getStatus());
        assertEquals("Successful", response.getMessage().trim());
    }

    @Test
    void testSaveOverrides_whenInvalidAccomClass_inLinkedProductOverride() {
        Product productA = createLinkedProduct("Product A", 1, true);
        AccomClass accomClass = createAccomClass("STANDARD", 1);
        AccomType accomType = createAccomType(accomClass, 1, "STD");
        ProductAccomType productAAccomType = createProductAccomType(productA,accomType);

        OverrideRequest override1 = getOverrideRequest("100", "200", 1, "2023-10-01", "2023-10-02", OverrideRequest.OperationType.ADD);
        OverrideRequest override2 = getOverrideRequest("150", "250", 3, "2023-10-03", "2023-10-03", OverrideRequest.OperationType.ADD);
        List<OverrideRequest> requests = List.of(override1, override2);

        when(crudService.findByNamedQuery(ProductAccomType.HAVING_PRODUCT_STATUS_ACTIVE)).thenReturn(List.of(productAAccomType));
        when(crudService.findByNamedQuerySingleResult(Product.GET_BY_ID, QueryParameter.with("productId", 5).parameters())).thenReturn(productA);
        when(crudService.findByNamedQuery(AccomClass.ALL, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(List.of(accomClass));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.of(2022, 10, 1));

        ResponseDTO response = service.saveOrUpdateOverrides("1", 5, requests);

        assertEquals(HttpStatus.SC_BAD_REQUEST, response.getStatus());
        assertEquals("Accom Class Ids: [3] are not mapped to Product", response.getMessage().trim());
    }

    @Test
    void testSaveOverrides_whenInvalidAccomClass_inIndependentProductOverride() {
        Product BAR = buildBarProduct();
        AccomClass accomClass = createAccomClass("STANDARD", 1);

        OverrideRequest override1 = getOverrideRequest("100", "200", 1, "2023-10-01", "2023-10-02", OverrideRequest.OperationType.ADD);
        OverrideRequest override2 = getOverrideRequest("150", "250", 3, "2023-10-03", "2023-10-03", OverrideRequest.OperationType.ADD);
        List<OverrideRequest> requests = List.of(override1, override2);

        when(crudService.findByNamedQuerySingleResult(Product.GET_BY_ID, QueryParameter.with("productId", 1).parameters())).thenReturn(BAR);
        when(crudService.findByNamedQuery(AccomClass.ALL, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(List.of(accomClass));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.of(2022, 10, 1));

        ResponseDTO response = service.saveOrUpdateOverrides("1", 1, requests);

        assertEquals(HttpStatus.SC_BAD_REQUEST, response.getStatus());
        assertEquals("Accom Class Ids: [3] are not mapped to Product", response.getMessage().trim());
    }
    @Test
    void testSaveOverrides_whenInvalidOverridesAndInvalidDate() {
        Product BAR = buildBarProduct();
        AccomClass accomClass = createAccomClass("STANDARD", 1);

        OverrideRequest override1 = getOverrideRequest("100", "200", 1, "2023-10-10", "2023-10-01", OverrideRequest.OperationType.ADD);
        OverrideRequest barOverride = getOverrideRequest(null, null, 1, "2023-10-11", "2023-10-03", OverrideRequest.OperationType.ADD);
        List<OverrideRequest> requests = List.of(override1, barOverride);

        when(crudService.findByNamedQuerySingleResult(Product.GET_BY_ID, QueryParameter.with("productId", 1).parameters())).thenReturn(BAR);
        when(crudService.findByNamedQuery(AccomClass.ALL, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(List.of(accomClass));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.of(2022, 10, 1));

        ResponseDTO response = service.saveOrUpdateOverrides("1", 1, requests);

        assertEquals(HttpStatus.SC_BAD_REQUEST, response.getStatus());
        assertEquals("Start date should be less than or equal to end date: [(2023-10-10,2023-10-01), (2023-10-11,2023-10-03)]\n" +
                "Override value must be provided for startDate: 2023-10-11 and endDate: 2023-10-03",response.getMessage().trim());
    }
    @Test
    void testSaveOverrides_whenDuplicateOverrides() {
        Product BAR = buildBarProduct();
        Product productA = createLinkedProduct("Product A", 1, true);
        AccomClass accomClass = createAccomClass("STANDARD", 1);
        AccomType accomType = createAccomType(accomClass, 1, "STD");
        ProductAccomType productAAccomType = createProductAccomType(productA,accomType);

        OverrideRequest override1 = getOverrideRequest("100", "200", 1, "2023-10-01", "2023-10-01", OverrideRequest.OperationType.ADD);
        OverrideRequest override2 = getOverrideRequest("150", "250", 1, "2023-10-01", "2023-10-01", OverrideRequest.OperationType.ADD);
        List<OverrideRequest> requests = List.of(override1, override2);

        when(crudService.findByNamedQuerySingleResult(Product.GET_BY_ID, QueryParameter.with("productId", 1).parameters())).thenReturn(BAR);
        when(crudService.findByNamedQuery(AccomClass.ALL, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(List.of(accomClass));
        when(productManagementService.getOptimizationLevel()).thenReturn(OptimizationLevel.SAME_FOR_ALL_ROOM_CLASSES);
        when(crudService.findByNamedQuery(ProductAccomType.HAVING_PRODUCT_STATUS_ACTIVE)).thenReturn(List.of(productAAccomType));
        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.of(2022, 10, 1));
        when(crudService.findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN_FOR_ACCOM_TYPES,
                CPDecisionBAROutput.params(BAR, JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2023-10-03")),
                        JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2023-10-03")), List.of(accomType))))
                .thenReturn(List.of(prepareCPDecisionBAROutputForCeilingAndFloor(false)));

        ResponseDTO response = service.saveOrUpdateOverrides("1", 1, requests);

        assertEquals(HttpStatus.SC_BAD_REQUEST, response.getStatus());
        assertEquals("Duplicate Override Request found for: roomTypeId=1, startDate=2023-10-01, endDate=2023-10-01",response.getMessage().trim());
    }

    @Test
    void testSaveOverrides_whenInValidOverrideForLinkedProduct() {
        Product productA = createLinkedProduct("Product A", 1, true);
        AccomClass accomClassSTD = createAccomClass("STANDARD", 1);
        AccomClass accomClassDLX = createAccomClass("DELUXE", 2);

        AccomType accomTypeSTD = createAccomType(accomClassSTD, 1, "STD");
        AccomType accomTypeSTT = createAccomType(accomClassSTD, 2, "STT");
        AccomType accomTypeSTF = createAccomType(accomClassSTD, 3, "STF");

        AccomType accomTypeSUP2DI = createAccomType(accomClassDLX, 4, "SUP2DI");
        AccomType accomTypeSUP2DN = createAccomType(accomClassDLX, 5, "SUP2DN");

        List<ProductAccomType> productAccomTypes = new ArrayList<>();
        productAccomTypes.add(createProductAccomType(productA,accomTypeSTD));
        productAccomTypes.add(createProductAccomType(productA,accomTypeSTT));
        productAccomTypes.add(createProductAccomType(productA,accomTypeSTF));

        productAccomTypes.add(createProductAccomType(productA,accomTypeSUP2DI));
        productAccomTypes.add(createProductAccomType(productA,accomTypeSUP2DN));


        when(crudService.findByNamedQuerySingleResult(Product.GET_BY_ID, QueryParameter.with("productId", 5).parameters())).thenReturn(productA);
        when(crudService.findByNamedQuery(AccomClass.ALL, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(List.of(accomClassSTD, accomClassDLX));
        when(crudService.findByNamedQuery(AccomType.ALL_ACTIVE, QueryParameter.with(PROPERTY_ID, PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(List.of(accomTypeSTD, accomTypeSTT, accomTypeSTF, accomTypeSUP2DI, accomTypeSUP2DN));
        when(crudService.findByNamedQuery(ProductAccomType.HAVING_PRODUCT_STATUS_ACTIVE)).thenReturn((List<Object>) (List<?>)productAccomTypes);
        when(productManagementService.getOptimizationLevel()).thenReturn(OptimizationLevel.SAME_FOR_ALL_ROOM_CLASSES);

        OverrideRequest override1 = getOverrideRequest("100", "200", 1, "2023-10-01", "2023-10-01", OverrideRequest.OperationType.ADD);
        List<OverrideRequest> requests = List.of(override1);

        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.of(2022, 10, 1));
        when(crudService.findByNamedQuerySingleResult(Product.GET_BY_ID, QueryParameter.with("productId", 5).parameters())).thenReturn(productA);
        when(crudService.findByNamedQuery(AccomClass.ALL, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(List.of(accomClassSTD,accomClassDLX));

        ResponseDTO response = service.saveOrUpdateOverrides("1", 5, requests);

        assertEquals(HttpStatus.SC_BAD_REQUEST, response.getStatus());
        assertEquals("All Accom Classes  must have exactly one override value for ProductId: 5 for date range: (2023-10-01,2023-10-01)", response.getMessage());
    }

    @Test
    void testSaveOverrides_whenValidOverridesForBAR() {
        Product BAR = buildBarProduct();
        AccomClass accomClass = createAccomClass("STANDARD", 1);
        AccomType accomType = createAccomType(accomClass, 1, "STD");

        OverrideRequest barOverride = getOverrideRequest("100", "300", 1, "2023-10-03", "2023-10-03", OverrideRequest.OperationType.ADD);
        List<OverrideRequest> requests = List.of(barOverride);

        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(java.time.LocalDate.of(2022, 10, 1));
        when(crudService.findByNamedQuerySingleResult(Product.GET_BY_ID, QueryParameter.with("productId", 1).parameters())).thenReturn(BAR);
        when(crudService.findByNamedQuery(AccomClass.ALL, QueryParameter.with("propertyId", PacmanWorkContextHelper.getPropertyId()).parameters())).thenReturn(List.of(accomClass));
        when(crudService.findByNamedQuery(AccomType.ACTIVE_AND_VALID_ID_BY_ACCOM_CLASS_ID,
                QueryParameter.with("accomClassId", accomClass.getId()).parameters())).thenReturn(List.of(accomType));
        when(crudService.findByNamedQuery(CPDecisionBAROutput.GET_DECISIONS_WITH_DATES_BETWEEN_FOR_ACCOM_TYPES,
                CPDecisionBAROutput.params(BAR, JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2023-10-03")),
                JavaLocalDateUtils.toJodaLocalDate(java.time.LocalDate.parse("2023-10-03")), List.of(accomType))))
                .thenReturn(List.of(prepareCPDecisionBAROutputForCeilingAndFloor(false)));

        ResponseDTO response = service.saveOrUpdateOverrides("1", 1, requests);

        assertEquals(HttpStatus.SC_NO_CONTENT, response.getStatus());
    }

    @Test
    void shouldDoNothing_whenUpdatingPricingDecisions_withEmptyInput() {
        StringBuilder message = service.updatePricingDecisions(Collections.emptyList());
        verifyNoInteractions(crudService, pricingConfigurationService, decisionService, productManagementService);
        assertEquals("Success", message.toString());
    }

    @Test
    void shouldShowJobInProgressError_whenSavingPricingDecisions() {
        when(regulatorSpringService.isProcessingInProgress()).thenReturn(true);
        List<PricingDto> dtoList = singletonList(
                getPricingDto(CURRENT_DATE.plusDays(1), CURRENT_DATE.plusDays(2), 0.00, 200.00, 10)
        );

        assertEquals("Overrides not allowed as BDE/IDP/Sync is in progress.",
                service.updatePricingDecisions(dtoList).toString());
    }

    @Test
    void shouldSavePricingDecisions_whenValidOverridesForBAR() {
        setupPricingOverridesMocks(barProduct);
        List<PricingDto> dtoList = singletonList(
                getPricingDto(CURRENT_DATE.plusDays(1), CURRENT_DATE.plusDays(2), 100.00, 200.00, 10)
        );

        StringBuilder message = service.updatePricingDecisions(dtoList);

        assertEquals("Success", message.toString());
    }

    @Test
    void shouldShowPastDateNotAllowedError_whenSavingPricingDecisions_withDateRangeInPast() {
        setupPricingOverridesMocks(barProduct);
        List<PricingDto> dtoList = singletonList(
                getPricingDto(CURRENT_DATE.minusDays(2), CURRENT_DATE, 100.00, 200.00, 10)
        );

        assertEquals("Date range cannot be in the past.\n", service.updatePricingDecisions(dtoList).toString());
    }

    @Test
    void shouldShowStartDateShouldBeLessThanEndDateError_whenSavingPricingDecisions_withStartDateGreaterThanEndDate() {
        setupPricingOverridesMocks(barProduct);
        List<PricingDto> dtoList = singletonList(
                getPricingDto(CURRENT_DATE.plusDays(2), CURRENT_DATE.plusDays(1), 100.00, 200.00, 10)
        );

        assertEquals("Start date should be less than or equal to end date.\n",
                service.updatePricingDecisions(dtoList).toString());
    }

    @Test
    void shouldShowFloorCeilingShouldBeGreaterThanZeroError_whenSavingPricingDecisions() {
        setupPricingOverridesMocks(barProduct);
        List<PricingDto> dtoList = singletonList(
                getPricingDto(CURRENT_DATE.plusDays(1), CURRENT_DATE.plusDays(2), 0.00, 200.00, 10)
        );

        assertEquals("Maximum override and minimum override must be greater than 0 for independent products.\n",
                service.updatePricingDecisions(dtoList).toString());
    }

    @Test
    void shouldSaveBaseRoomTypeOverrides_whenSavingPricingDecisions() {
        setupPricingOverridesMocks(barProduct);
        List<PricingDto> dtoList = singletonList(
                getPricingDto(CURRENT_DATE.plusDays(1), CURRENT_DATE.plusDays(2), 100.00, 200.00, 10)
        );

        StringBuilder message = service.updatePricingDecisions(dtoList);
        assertEquals("Success", message.toString());
    }

    @Test
    void shouldShowInvalidProductAndRoomTypeNotMappedToProductError_whenSavingPricingDecisions() {
        setupPricingOverridesMocks(buildAgileNonOptimizedProduct());
        PricingDto pricingDto1 = getPricingDto(CURRENT_DATE.plusDays(1), CURRENT_DATE.plusDays(2), 100.00, 100.00, 10);
        PricingDto pricingDto2 = getPricingDto(CURRENT_DATE.plusDays(1), CURRENT_DATE.plusDays(2), 200.00, 200.00, 20);
        List<PricingDto> dtoList = Arrays.asList(pricingDto1, pricingDto2);

        assertEquals("Room type Id is invalid or inactive or discontinued Room type or Zero Capacity or Mapped to Unassigned Room Class.\n" +
                        "Room type Id is not mapped to Product.\n",
                service.updatePricingDecisions(dtoList).toString());
    }

    @Test
    void shouldShowFloorCeilingOverridesMismatchForAgileProductsError_whenSavingPricingDecisions() {
        WorkContextType wc = new WorkContextType();
        PacmanWorkContextHelper.setWorkContext(wc);
        wc.setUserId("11403");
        setupPricingOverridesMocks(buildAgileNonOptimizedProduct());
        PricingDto pricingDto = getPricingDto(CURRENT_DATE.plusDays(1), CURRENT_DATE.plusDays(2), 100.00, 200.00, 10);
        List<PricingDto> dtoList = singletonList(pricingDto);

        assertEquals("For Non-Optimized product minimum override should be equal to maximum override.\n",
                service.updatePricingDecisions(dtoList).toString());
    }

    @Test
    void shouldShowFloorCeilingMismatchForNonBaseRoomsError_whenSavingPricingDecisions() {
        setupPricingOverridesMocks(barProduct);
        when(productManagementService.getBaseAccomTypes()).thenReturn(singletonList(new AccomType()));
        PricingDto pricingDto = getPricingDto(CURRENT_DATE.plusDays(1), CURRENT_DATE.plusDays(2), 100.00, 200.00, 10);
        List<PricingDto> dtoList = singletonList(pricingDto);

        assertEquals("Maximum override & minimum override should be the same for non-base room types of independent products.\n",
                service.updatePricingDecisions(dtoList).toString());
    }

    @Test
    void shouldUploadPricingDecisionsSuccessfully() {
        setupUploadPricingDecisionMocks(false, false, false, true);
        when(propertyService.getPropertyStage(5)).thenReturn(Stage.TWO_WAY);
        when(overrideService.isOverrideAvailableForUpload()).thenReturn(true);

        String message = service.uploadPricingDecisions();

        assertEquals("Success", message);
        verify(overrideService, times(1)).uploadBAROverrides();
    }

    @Test
    void shouldShowUploadInProgressError_whenUploadingPricingDecisions() {
        setupUploadPricingDecisionMocks(true, false, false, true);
        when(propertyService.getPropertyStage(5)).thenReturn(Stage.TWO_WAY);
        String message = service.uploadPricingDecisions();
        assertEquals("Error: Decisions are currently being uploaded.", message);
    }

    @Test
    void shouldShowProcessingInProgressError_whenUploadingPricingDecisions() {
        setupUploadPricingDecisionMocks(false, true, false, true);
        when(propertyService.getPropertyStage(5)).thenReturn(Stage.TWO_WAY);
        String message = service.uploadPricingDecisions();
        assertEquals("Error: Decisions cannot be uploaded as BDE/IDP/Sync is in progress.", message);
    }

    @Test
    void shouldShowSyncIsRequiredError_whenUploadingPricingDecisions() {
        setupUploadPricingDecisionMocks(false, false, true, true);
        when(propertyService.getPropertyStage(5)).thenReturn(Stage.TWO_WAY);
        String message = service.uploadPricingDecisions();
        assertEquals("Error: Decisions cannot be uploaded while sync is pending.", message);
    }

    @Test
    void shouldShowManualBarUploadNotEnabledError_whenUploadingPricingDecisions() {
        setupUploadPricingDecisionMocks(false, false, false, false);
        when(propertyService.getPropertyStage(5)).thenReturn(Stage.TWO_WAY);
        String message = service.uploadPricingDecisions();
        assertEquals("Error: The Manual Upload BAR Override feature is not enabled.", message);
    }

    @Test
    void shouldShowPropertyNotInTwoWayError_whenUploadingPricingDecisions() {
        setupUploadPricingDecisionMocks(false, false, false, true);
        when(propertyService.getPropertyStage(5)).thenReturn(Stage.ONE_WAY);
        when(overrideService.isOverrideAvailableForUpload()).thenReturn(true);

        String message = service.uploadPricingDecisions();

        assertEquals("Error: Decisions cannot be uploaded as property is not in two-way stage.", message);
    }

    @Test
    void shouldShowNoOverridesPresentError_whenUploadingPricingDecisions() {
        setupUploadPricingDecisionMocks(false, false, false, true);
        when(propertyService.getPropertyStage(5)).thenReturn(Stage.TWO_WAY);
        when(overrideService.isOverrideAvailableForUpload()).thenReturn(false);

        String message = service.uploadPricingDecisions();

        assertEquals("Error: No overrides are available for upload.", message);
    }

    private void setupUploadPricingDecisionMocks(boolean uploadInProgress, boolean processingInProgress, boolean syncRequired, boolean manualUploadEnabled) {
        when(regulatorSpringService.isDecisionUploadInProgress()).thenReturn(uploadInProgress);
        when(regulatorSpringService.isProcessingInProgress()).thenReturn(processingInProgress);
        when(overrideService.isForceSyncRequired()).thenReturn(syncRequired);
        when(configParamsService.getBooleanParameterValue(FeatureTogglesConfigParamName.ENABLE_MANUAL_BARUPLOAD)).thenReturn(manualUploadEnabled);
    }

    private void setupPricingOverridesMocks(Product product) {
        ACCOM_CLASS.setAccomTypes(Collections.singleton(ACCOM_TYPE));
        setPricingAccomClass(ACCOM_CLASS, ACCOM_TYPE);
        setProductAccomType(product);

        when(dateService.getCaughtUpJavaLocalDate()).thenReturn(CURRENT_DATE);
        when(regulatorSpringService.isProcessingInProgress()).thenReturn(false);
        when(accommodationService.getActiveAccomTypesWithValidCapacityAndDisplayStatus()).thenReturn(List.of(ACCOM_TYPE, createAccomType(ACCOM_CLASS, 11, "STT")));
        when(productManagementService.getBaseAccomTypes()).thenReturn(Collections.singletonList(ACCOM_TYPE));
        when(prettyPricingService.doesNumberMeetPricingRuleForProduct(any(), any())).thenReturn(true);
        when(crudService.findByNamedQuery(AccomType.ALL)).thenReturn(Collections.singletonList(ACCOM_TYPE));
        when(crudService.findByNamedQuery(Product.GET_BY_STATUS, QueryParameter.with(STATUS, TenantStatusEnum.ACTIVE).parameters())).thenReturn(singletonList(product));
    }

    private void setProductAccomType(Product product) {
        ProductAccomType productAccomType = new ProductAccomType();
        productAccomType.setAccomType(ACCOM_TYPE);
        productAccomType.setProduct(product);
        when(crudService.findByNamedQuery(ProductAccomType.HAVING_PRODUCT_STATUS_ACTIVE)).thenReturn(singletonList(productAccomType));
    }

    private PricingDto getPricingDto(java.time.LocalDate startDate, java.time.LocalDate endDate, Double min, Double max, Integer roomTypeId) {
        PricingDto dto = new PricingDto();
        dto.setProductId(1);
        dto.setRoomtypeId(roomTypeId);
        dto.setMinimum(min);
        dto.setMaximum(max);
        dto.setStartDate(startDate);
        dto.setEndDate(endDate);
        return dto;
    }

    private void setPricingAccomClass(AccomClass accomClass, AccomType accomType) {
        PricingAccomClass pricingAccomClass = new PricingAccomClass();
        pricingAccomClass.setAccomType(accomType);
        pricingAccomClass.setAccomClass(accomClass);
        List<PricingAccomClass> pricingAccomClasses = Collections.singletonList(pricingAccomClass);
        when(crudService.findAll(PricingAccomClass.class)).thenReturn(pricingAccomClasses);
    }

    private Product createLinkedProduct(String productName, int dependentPRoductId, boolean isOptimized) {
        Product product = new Product();
        product.setName(productName);
        product.setId(5);
        product.setCode(Product.AGILE_RATES_PRODUCT_CODE);
        product.setActive(true);
        product.setDependentProductId(dependentPRoductId);
        product.setOptimized(isOptimized);
        product.setOffsetMethod(AgileRatesOffsetMethod.FIXED);
        return product;
    }

    private static OverrideRequest getOverrideRequest(String floor, String ceiling, int roomClassId, String startDate, String endDate, OverrideRequest.OperationType operation) {
        OverrideRequest override = new OverrideRequest();
        override.setRoomClassId(Integer.toString(roomClassId));
        override.setStartDate(String.valueOf(startDate));
        override.setEndDate(String.valueOf(endDate));
        override.setFloorOverride(floor);
        override.setCeilingOverride(ceiling);
        override.setOperationType(operation);
        return override;
    }

    private CPUnqualifedDemandForecastPrice createDemandForecastPrice(LocalDate arrivalDate, BigDecimal rate) {
        CPUnqualifedDemandForecastPrice demandForecastPrice = new CPUnqualifedDemandForecastPrice();
        demandForecastPrice.setArrivalDate(arrivalDate);
        demandForecastPrice.setRate(rate);
        return demandForecastPrice;
    }


    private CPBarOverride prepareCPBarOverride(boolean applyOverridesAcrossRoomTypes) {
        BigDecimal floorRate = new BigDecimal("159");
        BigDecimal ceilingRate = new BigDecimal("900");
        CPBarOverride cpBarOverride = new CPBarOverride(prepareCPDecisionBAROutput(), floorRate, ceilingRate, false);
        cpBarOverride.setApplyOverrideAcrossRoomTypes(applyOverridesAcrossRoomTypes);
        return cpBarOverride;
    }

    private CPDecisionBAROutput prepareCPDecisionBAROutput() {
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        cpDecisionBAROutput.setId(Long.valueOf(9130));
        cpDecisionBAROutput.setDecisionId(193);
        Product product = new Product();
        product.setId(1);
        cpDecisionBAROutput.setProduct(product);
        cpDecisionBAROutput.setDecisionReasonTypeId(1);
        cpDecisionBAROutput.setAccomType(getAccomType());
        cpDecisionBAROutput.setArrivalDate(LocalDate.parse("2015-09-05"));
        cpDecisionBAROutput.setLengthOfStay(-1);
        cpDecisionBAROutput.setOptimalBAR(new BigDecimal("367.00"));
        cpDecisionBAROutput.setPrettyBAR(new BigDecimal("255.00"));
        cpDecisionBAROutput.setRoomsOnlyBAR(new BigDecimal("255.00"));
        cpDecisionBAROutput.setFinalBAR(new BigDecimal("255.00"));
        cpDecisionBAROutput.setOverrideType(DecisionOverrideType.USER);
        cpDecisionBAROutput.setSpecificOverride(new BigDecimal("255"));

        return cpDecisionBAROutput;
    }

    private CPDecisionBAROutput prepareCPDecisionBAROutputForCeilingAndFloor(boolean isCeiling) {
        CPDecisionBAROutput cpDecisionBAROutput = new CPDecisionBAROutput();
        cpDecisionBAROutput.setId(Long.valueOf(9130));
        cpDecisionBAROutput.setDecisionId(193);
        Product product = new Product();
        product.setId(1);
        cpDecisionBAROutput.setProduct(product);
        cpDecisionBAROutput.setDecisionReasonTypeId(1);
        cpDecisionBAROutput.setAccomType(getAccomType());
        cpDecisionBAROutput.setArrivalDate(LocalDate.parse("2015-09-05"));
        cpDecisionBAROutput.setLengthOfStay(-1);
        cpDecisionBAROutput.setOptimalBAR(new BigDecimal("367.00"));
        cpDecisionBAROutput.setPrettyBAR(new BigDecimal("255.00"));
        cpDecisionBAROutput.setRoomsOnlyBAR(new BigDecimal("255.00"));
        cpDecisionBAROutput.setFinalBAR(new BigDecimal("255.00"));
        cpDecisionBAROutput.setOverrideType(DecisionOverrideType.USER);
        if (isCeiling)
            cpDecisionBAROutput.setCeilingOverride(new BigDecimal("250"));
        else {
            cpDecisionBAROutput.setCeilingOverride(new BigDecimal("250"));
            cpDecisionBAROutput.setFloorOverride(new BigDecimal("210"));
        }

        return cpDecisionBAROutput;

    }

    private CPBarOverride prepareCPBarOverrideForCeilingAndFloorOverride(boolean applyOverridesAcrossRoomTypes, boolean isCeiling) {
        BigDecimal floorRate = new BigDecimal("159");
        BigDecimal ceilingRate = new BigDecimal("900");
        CPBarOverride cpBarOverride = new CPBarOverride(prepareCPDecisionBAROutputForCeilingAndFloor(isCeiling), floorRate, ceilingRate, false);
        cpBarOverride.setApplyOverrideAcrossRoomTypes(applyOverridesAcrossRoomTypes);
        return cpBarOverride;
    }

    private AccomType getAccomType() {
        AccomType accomType = new AccomType();
        accomType.setAccomTypeCode("CQ");
        AccomClass accomClass = new AccomClass();
        accomClass.setId(3);
        accomClass.setName("A");
        accomType.setAccomClass(accomClass);
        return accomType;
    }

    private CPDecisionBAROutputOverride prepareCPDecisionBAROutputOverride() {
        CPDecisionBAROutputOverride cpDecisionBAROutputOverride = new CPDecisionBAROutputOverride();
        cpDecisionBAROutputOverride.setDecisionId(193);
        Product product = new Product();
        product.setId(1);
        cpDecisionBAROutputOverride.setProduct(product);
        AccomType accomType = getAccomType();
        cpDecisionBAROutputOverride.setAccomType(accomType);
        cpDecisionBAROutputOverride.setArrivalDate(LocalDate.parse("2015-09-05"));
        cpDecisionBAROutputOverride.setLengthOfStay(-1);
        cpDecisionBAROutputOverride.setOldOverrideType(DecisionOverrideType.NONE);
        cpDecisionBAROutputOverride.setNewOverrideType(DecisionOverrideType.USER);
        cpDecisionBAROutputOverride.setOldUserOverride(new BigDecimal("255.00"));
        cpDecisionBAROutputOverride.setNewUserOverride(new BigDecimal("255"));
        return cpDecisionBAROutputOverride;
    }

    private Role populateRole(String clientCode, String roleId) {
        Role role1 = new Role();
        role1.setUniqueIdentifier(roleId);
        role1.setRoleName("role" + roleId);
        role1.setClientCode(clientCode);
        return role1;
    }

    private static AccomTypeSupplementValue getAccomTypeSupplementValue(java.time.LocalDate arrivalDate, OffsetMethod offsetMethod, BigDecimal supplementValue) {
        AccomTypeSupplementValuePK key1 = new AccomTypeSupplementValuePK(1, DateUtil.convertJavaToJodaLocalDate(arrivalDate), 1, SINGLE);
        AccomTypeSupplementValue accomTypeSupplementValue = new AccomTypeSupplementValue();
        accomTypeSupplementValue.setId(key1);
        accomTypeSupplementValue.setValue(supplementValue);
        accomTypeSupplementValue.setOffsetMethod(offsetMethod);
        return accomTypeSupplementValue;
    }
}
