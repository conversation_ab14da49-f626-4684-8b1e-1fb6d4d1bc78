package com.ideas.tetris.pacman.services.eventaggregator;

import com.ideas.infra.tetris.security.jaas.TetrisPrincipal;
import com.ideas.tetris.pacman.common.configparams.SyncConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.utils.systemconfig.SystemConfig;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.configchange.ConfigChangeService;
import com.ideas.tetris.pacman.services.crudservice.TenantCrudServiceBean;
import com.ideas.tetris.pacman.services.marketsegment.entity.ForecastGroup;
import com.ideas.tetris.pacman.services.syncflags.entity.SyncFlag;
import com.ideas.tetris.pacman.services.syncflags.service.SyncDisplayNameService;
import com.ideas.tetris.pacman.services.syncflags.service.SyncFlagService;
import com.ideas.tetris.pacman.services.useractivity.ActivityType;
import com.ideas.tetris.pacman.services.useractivity.UserActivityService;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameter;
import com.ideas.tetris.platform.common.configparams.entities.ConfigParameterValue;
import com.ideas.tetris.platform.common.contextholder.PacmanThreadLocalContextHolder;
import com.ideas.tetris.platform.common.crudservice.CrudService;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import com.ideas.tetris.platform.common.job.JobName;
import com.ideas.tetris.platform.common.job.JobParameterKey;
import com.ideas.tetris.platform.common.job.JobServiceLocal;
import com.ideas.tetris.platform.common.regulator.RegulatorConstants;
import com.ideas.tetris.platform.common.xml.schema.workcontext.v1.WorkContextType;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.regulator.service.RegulatorService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import javax.ws.rs.core.Response;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ideas.g3.test.AbstractG3JupiterTest.inject;
import static com.ideas.tetris.pacman.services.syncflags.entity.SyncFlag.FIND_ENABLED_SYNC_EVENTS;
import static com.ideas.tetris.platform.common.job.JobParameterKey.IS_MANUAL_BAR_TO_BE_UPLOADED;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyObject;
import static org.mockito.Mockito.anySet;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class SyncEventAggregatorServiceTest {
    private WorkContextType workContextType;
    private static final String CLIENT_CODE = "Greenbar";
    private static final String PROPERTY_CODE = "Highland";
    private static final Integer PROPERTY_ID = 913;
    @Mock
    private UserActivityService activityService;
    @Mock
    private AccomConfigAlertComponent accomAlertSyncComponent;
    @Mock
    private MktSegConfigAlertSyncComponent mktSegConfigComponent;
    @Mock
    protected PacmanConfigParamsService configService;
    @Mock
    protected JobServiceLocal jobService;
    @Mock
    protected RegulatorService regulatorService;
    @Mock
    private SyncFlagService syncFlagService;
    @Mock
    @TenantCrudServiceBean.Qualifier
    private CrudService crudService;
    @Mock
    private ConfigChangeService configChangeService;

    @Captor
    ArgumentCaptor<List<SyncFlag>> syncFlagCaptor;

    @InjectMocks
    SyncEventAggregatorService syncEventAggregatorService;

    @Mock
    private SyncDisplayNameService syncDisplayNameService;

    @Mock
    private PropertyStateService propertyStateService;

    PropertyState mockPropertyStateInfo = mock(PropertyState.class);

    @BeforeEach
    public void setup() {
        workContextType = new WorkContextType();
        workContextType.setClientCode(CLIENT_CODE);
        workContextType.setPropertyCode(PROPERTY_CODE);
        workContextType.setPropertyId(PROPERTY_ID);
        PacmanThreadLocalContextHolder.put(Constants.CONTEXT_KEY, workContextType);
        PacmanThreadLocalContextHolder.put(Constants.SSO_USER_PRINCIPAL, new TetrisPrincipal());

        ConfigParameter syncUserInvokedParameter = new ConfigParameter();
        syncUserInvokedParameter.setId(1);
        syncUserInvokedParameter.setName(SyncEvent.USER_INVOKED_SYNC.getGlobalParameter());

        ConfigParameter specialEventChangesParameter2 = new ConfigParameter();
        specialEventChangesParameter2.setId(1);
        specialEventChangesParameter2.setName(SyncEvent.SPECIAL_EVENTS_CHANGED.getGlobalParameter());

        String specialEventsParameterValue2Value = "false";
        ConfigParameterValue specialEventsParameterValue2 = new ConfigParameterValue();
        specialEventsParameterValue2.setConfigParameter(specialEventChangesParameter2);
        specialEventsParameterValue2.setFixedValue(specialEventsParameterValue2Value);

        ConfigParameter accommodationConfigurationChangesParameter2 = new ConfigParameter();
        accommodationConfigurationChangesParameter2.setId(2);
        accommodationConfigurationChangesParameter2.setName(SyncEvent.ACCOMMODATION_CONFIG_CHANGED.getGlobalParameter());

        ConfigParameterValue accommodationConfigurationChangesParameterValue2 = new ConfigParameterValue();
        accommodationConfigurationChangesParameterValue2.setConfigParameter(accommodationConfigurationChangesParameter2);
        accommodationConfigurationChangesParameterValue2.setFixedValue("true");

        PacmanThreadLocalContextHolder.getSessionProxy().clear();
        when(regulatorService.isSpringTXEnableRegulatorService()).thenReturn(false);
        System.setProperty("pacman.regulator.isRegulatorOptimizationsEnabled", "false");
    }

    @Test
    public void testGetPropertyStaleness2_fromTenantDb() {
        setupForSyncFromTenantDB(true);
        List<Object> enabledSyncFlags = new ArrayList<>();
        Arrays.asList(SyncEvent.ACCOMMODATION_CONFIG_CHANGED,
                        SyncEvent.SPECIAL_EVENTS_CHANGED)
                .forEach(syncEvent -> {
                    SyncFlag syncFlag = new SyncFlag();
                    syncFlag.setId(SyncConfigParamName.from(syncEvent.getGlobalParameter()));
                    syncFlag.setEnabled(true);
                    enabledSyncFlags.add(syncFlag);
                });
        when(crudService.findByNamedQuery(FIND_ENABLED_SYNC_EVENTS)).thenReturn(enabledSyncFlags);
        List<StalenessFlag> stalenessFlags = syncEventAggregatorService.getStalenessFlags();

        assertEquals(2, stalenessFlags.size());
        stalenessFlags.forEach(stalenessFlag -> {
            assertTrue(stalenessFlag.isShowSync());
            assertTrue(stalenessFlag.getLabel().equalsIgnoreCase("syncRequiredAccommodationConfigurationChanged") ||
                    stalenessFlag.getLabel().equalsIgnoreCase("syncRequiredSpecialEventsChanged"));
        });
    }

    @Test
    public void testGetPropertyStaleness2_fromGlobalParameter() {
        setupForSyncFromTenantDB(false);
        List syncFlagList = new ArrayList<>();
        SyncFlag syncFlag = new SyncFlag();
        syncFlag.setEnabled(true);
        syncFlag.setId(SyncConfigParamName.ACCOMMODATION_CONFIGURATION_CHANGED);
        syncFlagList.add(syncFlag);
        when(crudService.findByNamedQuery(FIND_ENABLED_SYNC_EVENTS)).thenReturn(syncFlagList);
        when(configService.getValue(any(String.class), eq(SyncEvent.ACCOMMODATION_CONFIG_CHANGED.getGlobalParameter()), eq(true))).thenReturn("true");
        List<StalenessFlag> stalenessFlags = syncEventAggregatorService.getStalenessFlags();
        assertEquals(1, stalenessFlags.size());
        assertEquals("syncRequiredAccommodationConfigurationChanged", stalenessFlags.get(0).getLabel());
    }

    @Test
    public void testClearPropertyStaleness2() {
        syncEventAggregatorService.clearPropertyStaleness(null);
        assertEquals(0, syncDisplayNameService.deleteAllDisplayNames());
        verify(syncDisplayNameService, times(2)).deleteAllDisplayNames();
    }

    @Test
    public void testClearPropertyStalenessTenantDBEnabled() {
        SyncFlag syncFlag = new SyncFlag();
        syncFlag.setId(SyncConfigParamName.ACCOMMODATION_CONFIGURATION_CHANGED);
        syncFlag.setEnabled(true);
        syncFlag.setNote("note");
        List<SyncFlag> syncFlags = Arrays.asList(syncFlag);

        when(syncFlagService.findEnabledSyncFlags()).thenReturn(syncFlags);
        doNothing().when(syncFlagService).save(syncFlagCaptor.capture());
        PacmanThreadLocalContextHolder.getSessionProxy().put(SyncEvent.ACCOMMODATION_CONFIG_CHANGED.getAlertDirtyFlag(), "");

        syncEventAggregatorService.clearPropertyStaleness(null);

        SyncFlag syncFlag1 = syncFlagCaptor.getValue().get(0);
        assertFalse(syncFlag1.isEnabled());
        assertEquals("", syncFlag1.getNote());

        assertTrue(PacmanThreadLocalContextHolder.getSessionProxy().isEmpty());
        verify(syncFlagService).save(syncFlags);
        verify(syncDisplayNameService).deleteAllDisplayNames();
    }


    @SuppressWarnings("unchecked")
    @Test
    public void testRegisterSyncEventWithNoForecastGroups() {
        when(crudService.findByNamedQuerySingleResult(anyObject(), anyObject())).thenReturn(0L);
        boolean actual = syncEventAggregatorService.registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED);
        verify(crudService).findByNamedQuerySingleResult(anyObject(), anyObject());
        assertFalse(actual);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testRegisterSyncEventAsAdd2() {
        when(crudService.findByNamedQuerySingleResult(anyObject(), anyObject())).thenReturn(1L);

        boolean actual = syncEventAggregatorService.registerSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED);

        verify(syncFlagService).enableSyncFor(SyncEvent.SPECIAL_EVENTS_CHANGED, null, Constants.EMPTY_STRING);
        assertTrue(actual);
    }

    @Test
    public void testClearSyncEventAsUpdate2() {
        syncEventAggregatorService.clearSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED);

        verify(syncFlagService).disableSyncFor(eq(SyncEvent.SPECIAL_EVENTS_CHANGED), eq(Constants.EMPTY_STRING));
        verify(syncDisplayNameService).deleteDisplayNamesForSyncEvent(SyncEvent.SPECIAL_EVENTS_CHANGED);
    }

    @Test
    public void testGetStalenessForComponentWhenDirty() {
        when(syncFlagService.findEnabledSyncEvents()).thenReturn(Arrays.asList(SyncEvent.SPECIAL_EVENTS_CHANGED));

        final boolean isSpecialEventDirty = syncEventAggregatorService.isSystemComponentDirty(SystemComponent.SPECIAL_EVENTS);
        final boolean isAccomodationDirty = syncEventAggregatorService.isSystemComponentDirty(SystemComponent.ACCOMMODATION_CONFIGURATION);

        assertTrue(isSpecialEventDirty);
        assertFalse(isAccomodationDirty);
        verify(syncFlagService, times(2)).findEnabledSyncEvents();
    }

    @Test
    public void testGetStalenessForComponent2() {
        when(syncFlagService.findEnabledSyncEvents()).thenReturn(Arrays.asList(SyncEvent.ACCOMMODATION_CONFIG_CHANGED));

        final boolean systemComponentDirty = syncEventAggregatorService.isSystemComponentDirty(SystemComponent.ACCOMMODATION_CONFIGURATION);

        assertTrue(systemComponentDirty);
        verify(syncFlagService).findEnabledSyncEvents();
    }

    @Test
    public void testSyncAllCallingJob() {
        when(jobService.startGuaranteedNewInstance(JobName.ForceSyncCalibration, null)).thenReturn(new Long(1));

        syncEventAggregatorService.syncAll();

        verify(syncFlagService).enableSyncFor(SyncEvent.USER_INVOKED_SYNC, null, Constants.EMPTY_STRING);
        verify(jobService).startGuaranteedNewInstance(JobName.ForceSyncCalibration, null);
    }

    @Test
    public void testsyncAllAndUploadDecisionsCallingJob() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(IS_MANUAL_BAR_TO_BE_UPLOADED, "true");
        when(jobService.startGuaranteedNewInstance(JobName.ForceSyncCalibration, parameters)).thenReturn(new Long(1));

        syncEventAggregatorService.syncAllAndUploadDecisions();

        verify(syncFlagService).enableSyncFor(SyncEvent.USER_INVOKED_SYNC, null, Constants.EMPTY_STRING);
        verify(jobService).startGuaranteedNewInstance(JobName.ForceSyncCalibration, parameters);
    }

    @Test
    public void isAnySystemComponentDirtyFlagTrue() {
        List<SystemComponent> components = Arrays.asList(SystemComponent.values());
        // INTRADAY_PERIOD_IN_HOURS not included in check for some reason
        List<SystemComponent> dirtyFreeComponents = Collections.singletonList(SystemComponent.INTRADAY_PERIOD_IN_HOURS);
        SyncEvent[] syncEvents = SyncEvent.values();

        // First run - nothing dirty | Called size - 1 as one component not checked
        for (SyncEvent syncEvent : syncEvents) {
            when(syncFlagService.isSyncEnabledFor(syncEvent)).thenReturn(false);
        }
        assertFalse(syncEventAggregatorService.isAnySystemComponentDirtyFlagTrue());
        // Now loop over each component being dirty
        Boolean isDirtyResponse;
        for (SystemComponent component : components) {
            final List<SyncEvent> collect = Arrays.stream(SyncEvent.values())
                    .filter(syncEvent -> syncEvent.getSystemComponent().equals(component))
                    .collect(Collectors.toList());
            when(syncFlagService.findEnabledSyncEvents()).thenReturn(collect);

            if (dirtyFreeComponents.contains(component)) {
                // Won't check - never dirty
                assertFalse(syncEventAggregatorService.isAnySystemComponentDirtyFlagTrue());
            } else {
                assertTrue(syncEventAggregatorService.isAnySystemComponentDirtyFlagTrue());
            }
        }
    }

    @Test
    public void test_registerEvent_login() {
        boolean HAS_PROPERTY_GROUP_SCOPE = true;
        SystemEvent event = new SystemEvent();
        event.setEventType(SystemEventType.LOGIN);
        event.setHasPropertyGroupScope(String.valueOf(HAS_PROPERTY_GROUP_SCOPE));
        syncEventAggregatorService.registerEvent(event);
        verify(activityService).startSession(String.valueOf(HAS_PROPERTY_GROUP_SCOPE));
    }

    @Test
    public void test_registerEvent_sessionStarted() {
        boolean HAS_PROPERTY_GROUP_SCOPE = true;
        SystemEvent event = new SystemEvent();
        event.setEventType(SystemEventType.SESSION_STARTED);
        event.setHasPropertyGroupScope(String.valueOf(HAS_PROPERTY_GROUP_SCOPE));
        syncEventAggregatorService.registerEvent(event);
        verify(activityService).startSession(String.valueOf(HAS_PROPERTY_GROUP_SCOPE));
    }

    @Test
    public void test_registerEvent_pageLoaded() {
        boolean HAS_PROPERTY_GROUP_SCOPE = true;
        String PAGE_CODE = "at-a-glance";
        String DETAILS = "myDetails";
        SystemEvent event = new SystemEvent();
        event.setPageCode(PAGE_CODE);
        event.setDetails(DETAILS);
        event.setEventType(SystemEventType.PAGE_LOADED);
        event.setHasPropertyGroupScope(String.valueOf(HAS_PROPERTY_GROUP_SCOPE));
        syncEventAggregatorService.registerEvent(event);
        verify(activityService).startActivity(PAGE_CODE, ActivityType.VIEW_PAGE, DETAILS, String.valueOf(HAS_PROPERTY_GROUP_SCOPE));
    }

    @Test
    public void test_registerEvent_pageUnloaded() {
        boolean HAS_PROPERTY_GROUP_SCOPE = true;
        String PAGE_CODE = "at-a-glance";
        String DETAILS = "myDetails";
        SystemEvent event = new SystemEvent();
        event.setPageCode(PAGE_CODE);
        event.setDetails(DETAILS);
        event.setEventType(SystemEventType.PAGE_UNLOADED);
        event.setHasPropertyGroupScope(String.valueOf(HAS_PROPERTY_GROUP_SCOPE));
        syncEventAggregatorService.registerEvent(event);
        verify(activityService).endActivity(PAGE_CODE, ActivityType.VIEW_PAGE, DETAILS);
    }

    @Test
    public void test_registerEvent_pageUnloaded_marketSegments() {
        boolean HAS_PROPERTY_GROUP_SCOPE = true;
        String PAGE_CODE = "market-segments";
        String DETAILS = "myDetails";
        SystemEvent event = new SystemEvent();
        event.setPageCode(PAGE_CODE);
        event.setDetails(DETAILS);
        event.setEventType(SystemEventType.PAGE_UNLOADED);
        event.setHasPropertyGroupScope(String.valueOf(HAS_PROPERTY_GROUP_SCOPE));
        syncEventAggregatorService.registerEvent(event);
        verify(activityService).endActivity(PAGE_CODE, ActivityType.VIEW_PAGE, DETAILS);
        verify(mktSegConfigComponent).sync();
    }

    @Test
    public void test_registerEvent_workContextChanging() {
        SystemEvent event = new SystemEvent();
        event.setEventType(SystemEventType.WORK_CONTEXT_CHANGING);
        syncEventAggregatorService.registerEvent(event);
        verify(mktSegConfigComponent).sync();
        verify(accomAlertSyncComponent).sync();
    }

    @Test
    public void test_registerSyncEvent_specialEvents() {
        SyncEvent EVENT = SyncEvent.SPECIAL_EVENTS_CHANGED;
        when(crudService.findByNamedQuerySingleResult(ForecastGroup.COUNT_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PROPERTY_ID).and("statusId", 1).parameters())).thenReturn(1L);

        assertTrue(syncEventAggregatorService.registerSyncEvent(EVENT, workContextType));

        verify(crudService).findByNamedQuerySingleResult(ForecastGroup.COUNT_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PROPERTY_ID).and("statusId", 1).parameters());
        verify(syncFlagService).enableSyncFor(EVENT, workContextType, Constants.EMPTY_STRING);
    }

    @Test
    public void test_registerSyncEvent_specialEvents_parameterAlreadyTrue() {
        SyncEvent EVENT = SyncEvent.SPECIAL_EVENTS_CHANGED;
        when(crudService.findByNamedQuerySingleResult(ForecastGroup.COUNT_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PROPERTY_ID).and("statusId", 1).parameters())).thenReturn(1L);

        assertTrue(syncEventAggregatorService.registerSyncEvent(EVENT, workContextType));

        verify(crudService).findByNamedQuerySingleResult(ForecastGroup.COUNT_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PROPERTY_ID).and("statusId", 1).parameters());
        verify(syncFlagService).enableSyncFor(EVENT, workContextType, Constants.EMPTY_STRING);
        verifyNoMoreInteractions(configService);
    }

    @Test
    public void test_registerSyncEvent_nullEvent() {
        SyncEvent EVENT = null;
        assertFalse(syncEventAggregatorService.registerSyncEvent(EVENT, workContextType));
        verifyNoMoreInteractions(configService);
        verifyNoMoreInteractions(crudService);
    }

    @Test
    public void test_registerSyncEvent_noForecastGroups() {
        String context = Constants.getPropertyConfigContext(CLIENT_CODE, PROPERTY_CODE);
        SyncEvent EVENT = SyncEvent.SPECIAL_EVENTS_CHANGED;
        when(configService.getValue(context, EVENT.getGlobalParameter())).thenReturn(Boolean.FALSE.toString());
        when(crudService.findByNamedQuerySingleResult(ForecastGroup.COUNT_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PROPERTY_ID).and("statusId", 1).parameters())).thenReturn(0L);
        assertFalse(syncEventAggregatorService.registerSyncEvent(EVENT, workContextType));
        verify(crudService).findByNamedQuerySingleResult(ForecastGroup.COUNT_BY_PROPERTY_ID,
                QueryParameter.with("propertyId", PROPERTY_ID).and("statusId", 1).parameters());
        verifyNoMoreInteractions(configService);
        verifyNoMoreInteractions(crudService);
    }

    @Test
    public void test_isAnyFlagDirty_whenGettingSyncValuesFromGlobalParameter() {
        setupForSyncFromTenantDB(false);
        when(configService.getValue(any(String.class), eq(SyncEvent.ACCOMMODATION_CONFIG_CHANGED.getGlobalParameter()), eq(true))).thenReturn("true");
        when(configService.getValue(any(String.class), eq(SyncEvent.SPECIAL_EVENTS_CHANGED.getGlobalParameter()), eq(true))).thenReturn("true");
    }

    @Test
    public void test_isAnyFlagDirty_whenGettingSyncValuesFromTenantDB() {
        setupForSyncFromTenantDB(true);
        List<Object> enabledSyncFlags = new ArrayList<>();
        Arrays.asList(SyncEvent.ACCOMMODATION_CONFIG_CHANGED,
                        SyncEvent.SPECIAL_EVENTS_CHANGED, SyncEvent.BAR_RATE_CONFIGURATION_CHANGED)
                .forEach(syncEvent -> {
                    SyncFlag syncFlag = new SyncFlag();
                    syncFlag.setId(SyncConfigParamName.from(syncEvent.getGlobalParameter()));
                    syncFlag.setEnabled(true);
                    enabledSyncFlags.add(syncFlag);
                });
        when(crudService.findByNamedQuery(FIND_ENABLED_SYNC_EVENTS)).thenReturn(enabledSyncFlags);
        assertTrue(syncEventAggregatorService.isAnyFlagDirty());
    }

    private void setupForSyncFromTenantDB(boolean useSyncFromTenantDB) {
        syncFlagService = new SyncFlagService();
        inject(syncEventAggregatorService, "syncFlagService", syncFlagService);
        inject(syncFlagService, "pacmanConfigParamsService", configService);
        inject(syncFlagService, "tenantCrudService", crudService);
    }

    @Test
    public void test_isAnyFlagDirty_false_whenGettingSyncValuesFromGlobalParameter() {
        when(configService.getValue(any(String.class), any(String.class), eq(true))).thenReturn("false");
        assertFalse(syncEventAggregatorService.isAnyFlagDirty());
    }

    @Test
    public void test_isAnyFlagDirty_false_whenGettingSyncValuesFromTenantDB() {
        assertFalse(syncEventAggregatorService.isAnyFlagDirty());
    }

    @Test
    public void test_getPropertyState_propertyLocked() {
        boolean IS_READ_ONLY = true;
        boolean IS_READ_ONLY_OVERRIDE = false;
        when(regulatorService.isPropertyReadOnly()).thenReturn(IS_READ_ONLY);
        when(regulatorService.isPropertyReadOnlyOverride()).thenReturn(IS_READ_ONLY_OVERRIDE);
        PropertyState result = syncEventAggregatorService.getPropertyState();
        assertEquals(IS_READ_ONLY, result.isReadOnly());
        assertEquals(IS_READ_ONLY_OVERRIDE, result.isReadOnlyOverride());
        assertEquals(PropertyRegulatorStatus.PROPERTY_LOCKED, result.getRegulatorStatus());
        assertNull(result.getStaleness());
        verify(regulatorService).isPropertyReadOnly();
        verify(regulatorService).isPropertyReadOnlyOverride();
    }

    @Test
    public void test_getPropertyState_Unlocked_NoDecisionUploadInProgress() {
        boolean IS_READ_ONLY = false;
        boolean IS_READ_ONLY_OVERRIDE = false;
        boolean DECISION_UPLOAD_IN_PROGRESS = false;
        mockRegulatorServiceMethods(IS_READ_ONLY, IS_READ_ONLY_OVERRIDE, DECISION_UPLOAD_IN_PROGRESS);
        PropertyState result = syncEventAggregatorService.getPropertyState();
        assertNull(result.getStaleness());
        assertEquals(IS_READ_ONLY, result.isReadOnly());
        assertEquals(IS_READ_ONLY_OVERRIDE, result.isReadOnlyOverride());
        assertEquals(DECISION_UPLOAD_IN_PROGRESS, result.isDecisionUploadInProgress());
        assertEquals(PropertyRegulatorStatus.UNLOCKED, result.getRegulatorStatus());
        verify(regulatorService).isPropertyReadOnly();
        verify(regulatorService).isPropertyReadOnlyOverride();
        verify(regulatorService).isDecisionUploadInProgress();
    }

    @Test
    public void test_getPropertyState_decisionUploadInProgress() {
        boolean IS_READ_ONLY = false;
        boolean IS_READ_ONLY_OVERRIDE = false;
        boolean DECISION_UPLOAD_IN_PROGRESS = true;
        mockRegulatorServiceMethods(IS_READ_ONLY, IS_READ_ONLY_OVERRIDE, DECISION_UPLOAD_IN_PROGRESS);
        PropertyState result = syncEventAggregatorService.getPropertyState();
        assertNull(result.getStaleness());
        assertEquals(IS_READ_ONLY, result.isReadOnly());
        assertEquals(IS_READ_ONLY_OVERRIDE, result.isReadOnlyOverride());
        assertEquals(DECISION_UPLOAD_IN_PROGRESS, result.isDecisionUploadInProgress());
        assertEquals(PropertyRegulatorStatus.UNLOCKED, result.getRegulatorStatus());
        verify(regulatorService).isPropertyReadOnly();
        verify(regulatorService).isPropertyReadOnlyOverride();
        verify(regulatorService).isDecisionUploadInProgress();
    }

    @Test
    public void test_getPropertyState_forceSyncInProgress() {
        boolean IS_READ_ONLY = false;
        boolean IS_READ_ONLY_OVERRIDE = false;
        boolean DECISION_UPLOAD_IN_PROGRESS = true;
        boolean FORCE_SYNC_IN_PROGRESS = true;
        mockRegulatorServiceMethods(IS_READ_ONLY, IS_READ_ONLY_OVERRIDE, DECISION_UPLOAD_IN_PROGRESS);
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(JobParameterKey.PROPERTY_ID, PROPERTY_ID);
        when(jobService.isJobActive(JobName.ForceSyncCalibration, parameters)).thenReturn(FORCE_SYNC_IN_PROGRESS);
        PropertyState result = syncEventAggregatorService.getPropertyState();
        assertNull(result.getStaleness());
        assertEquals(IS_READ_ONLY, result.isReadOnly());
        assertEquals(IS_READ_ONLY_OVERRIDE, result.isReadOnlyOverride());
        assertEquals(DECISION_UPLOAD_IN_PROGRESS, result.isDecisionUploadInProgress());
        assertEquals(FORCE_SYNC_IN_PROGRESS, result.isForceSyncInProgress());
        assertEquals(PropertyRegulatorStatus.UNLOCKED, result.getRegulatorStatus());
        verify(regulatorService).isPropertyReadOnly();
        verify(regulatorService).isPropertyReadOnlyOverride();
        verify(regulatorService).isDecisionUploadInProgress();
        verify(jobService).isJobActive(JobName.ForceSyncCalibration, parameters);
    }

    @Test
    public void test_getPropertyState_syncNeeded() {
        boolean IS_READ_ONLY = false;
        boolean IS_READ_ONLY_OVERRIDE = false;
        when(regulatorService.isPropertyReadOnly()).thenReturn(IS_READ_ONLY);
        SyncFlag syncFlag = new SyncFlag();
        syncFlag.setId(SyncConfigParamName.ACCOMMODATION_CONFIGURATION_CHANGED);
        when(regulatorService.isPropertyReadOnlyOverride()).thenReturn(IS_READ_ONLY_OVERRIDE);
        when(crudService.find(SyncFlag.class, "ACCOMMODATION_CONFIGURATION_CHANGED")).thenReturn(syncFlag);
        when(syncFlagService.isSyncEnabledFor(SyncEvent.ACCOMMODATION_CONFIG_CHANGED)).thenReturn(true);

        PropertyState result = syncEventAggregatorService.getPropertyState();
        assertEquals(IS_READ_ONLY, result.isReadOnly());
        assertEquals(IS_READ_ONLY_OVERRIDE, result.isReadOnlyOverride());
        assertEquals(PropertyRegulatorStatus.UNLOCKED, result.getRegulatorStatus());
        verify(regulatorService).isPropertyReadOnly();
        verify(regulatorService).isPropertyReadOnlyOverride();
    }

    @Test
    public void test_getPropertyState_syncNeeded_UsingTenantDB() {
        boolean IS_READ_ONLY = false;
        boolean IS_READ_ONLY_OVERRIDE = false;
        when(regulatorService.isPropertyReadOnly()).thenReturn(IS_READ_ONLY);
        when(regulatorService.isPropertyReadOnlyOverride()).thenReturn(IS_READ_ONLY_OVERRIDE);
        when(syncFlagService.findEnabledSyncEvents()).thenReturn(Arrays.asList(SyncEvent.ACCOMMODATION_CONFIG_CHANGED));

        PropertyState result = syncEventAggregatorService.getPropertyState();

        assertFalse(result.getStaleness().isEmpty());
        assertEquals(IS_READ_ONLY, result.isReadOnly());
        assertEquals(IS_READ_ONLY_OVERRIDE, result.isReadOnlyOverride());
        assertEquals(PropertyRegulatorStatus.UNLOCKED, result.getRegulatorStatus());
        verify(regulatorService).isPropertyReadOnly();
        verify(regulatorService).isPropertyReadOnlyOverride();
    }

    private void mockRegulatorServiceMethods(boolean IS_READ_ONLY, boolean IS_READ_ONLY_OVERRIDE, boolean DECISION_UPLOAD_IN_PROGRESS) {
        when(regulatorService.isPropertyReadOnly()).thenReturn(IS_READ_ONLY);
        when(regulatorService.isPropertyReadOnlyOverride()).thenReturn(IS_READ_ONLY_OVERRIDE);
        when(regulatorService.isDecisionUploadInProgress()).thenReturn(DECISION_UPLOAD_IN_PROGRESS);
    }

    private void mockEnhancedRegulatorServiceMethods(boolean isReadOnly, boolean isReadOnlyOverride, boolean decisionUploadInProgress) {
        HashMap<String, Integer> runningServiceNamesWithStatus = new HashMap<>();
        runningServiceNamesWithStatus.put("dummyService", RegulatorConstants.STATUS_CODE_RUNNING);
        when(regulatorService.getRunningServiceNamesAndStatusForContext(CLIENT_CODE, PROPERTY_CODE)).thenReturn(runningServiceNamesWithStatus);

        Set<String> onlyRunningStatusServiceNames = runningServiceNamesWithStatus
                .entrySet()
                .stream()
                .filter(entry -> entry.getValue().equals(RegulatorConstants.STATUS_CODE_RUNNING))
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
        Set<String> runningServiceNames = runningServiceNamesWithStatus.keySet();
        when(regulatorService.isPropertyReadOnly(onlyRunningStatusServiceNames)).thenReturn(isReadOnly);
        when(regulatorService.isPropertyReadOnlyOverride(CLIENT_CODE, PROPERTY_CODE, isReadOnly)).thenReturn(isReadOnlyOverride);
        when(regulatorService.isDecisionUploadInProgress(runningServiceNames)).thenReturn(decisionUploadInProgress);
    }

    private void verifyEnhancedRegulatorServiceMethods(boolean isReadOnly) {
        verify(regulatorService).isPropertyReadOnly(anySet());
        verify(regulatorService).isPropertyReadOnlyOverride(CLIENT_CODE, PROPERTY_CODE, isReadOnly);
        verify(regulatorService).isDecisionUploadInProgress(anySet());
    }

    @Test
    public void shouldMigrateSyncValuesOnPropertyLevel() {
        Map<String, Object> parameters = getParametersForSyncValuesMigration(SyncEventAggregatorService.PROPERTY_LEVEL);
        parameters.put(JobParameterKey.CLIENT_CODE, CLIENT_CODE);
        parameters.put(JobParameterKey.PROPERTY_CODE, PROPERTY_CODE);
        parameters.put(JobParameterKey.PROPERTY_CODES, PROPERTY_CODE);
        Response response = syncEventAggregatorService.migrateSyncValues(true, SyncEventAggregatorService.PROPERTY_LEVEL,
                CLIENT_CODE, PROPERTY_CODE);
        assertJobParameters(parameters, response);
    }

    @Test
    public void shouldMigrateSyncValuesOnClientLevel() {
        Map<String, Object> parameters = getParametersForSyncValuesMigration(SyncEventAggregatorService.CLIENT_LEVEL);
        parameters.put(JobParameterKey.CLIENT_CODE, CLIENT_CODE);
        Response response = syncEventAggregatorService.migrateSyncValues(true, SyncEventAggregatorService.CLIENT_LEVEL,
                CLIENT_CODE, PROPERTY_CODE);
        assertJobParameters(parameters, response);
    }

    @Test
    public void shouldMigrateSyncValuesOnGlobalLevel() {
        Map<String, Object> parameters = getParametersForSyncValuesMigration(SyncEventAggregatorService.GLOBAL_LEVEL);
        Response response = syncEventAggregatorService.migrateSyncValues(true,
                SyncEventAggregatorService.GLOBAL_LEVEL, CLIENT_CODE, PROPERTY_CODE);
        assertJobParameters(parameters, response);
    }

    private void assertJobParameters(Map<String, Object> parameters, Response response) {
        ArgumentCaptor<Map<String, Object>> argument = ArgumentCaptor.forClass(Map.class);
        verify(jobService).startJob(eq(JobName.MigrateSyncValuesJob), argument.capture());
        assertEquals(200, response.getStatus());
        Map<String, Object> jobParameters = argument.getValue();
        assertEquals(parameters.get(JobParameterKey.USE_SYNC_FROM_TENANT_DB), jobParameters.get(JobParameterKey.USE_SYNC_FROM_TENANT_DB));
        assertEquals(parameters.get(JobParameterKey.CONTEXT_LEVEL), jobParameters.get(JobParameterKey.CONTEXT_LEVEL));
        assertEquals(parameters.get(JobParameterKey.USER_ID), jobParameters.get(JobParameterKey.USER_ID));
        assertTrue(jobParameters.get(JobParameterKey.DATE) instanceof Date);
        assertEquals(parameters.get(JobParameterKey.CLIENT_CODE), jobParameters.get(JobParameterKey.CLIENT_CODE));
        assertEquals(parameters.get(JobParameterKey.PROPERTY_CODE), jobParameters.get(JobParameterKey.PROPERTY_CODE));
        assertEquals(parameters.get(JobParameterKey.PROPERTY_CODES), jobParameters.get(JobParameterKey.PROPERTY_CODES));
    }

    @Test
    public void shouldNotMigrateSyncValuesWithInvalidLevel() {
        Map<String, Object> parameters = getParametersForSyncValuesMigration("xyz");
        Response response = syncEventAggregatorService.migrateSyncValues(true, "xyz",
                CLIENT_CODE, PROPERTY_CODE);

        verify(jobService, never()).startJob(eq(JobName.MigrateSyncValuesJob), eq(parameters));
        assertEquals(406, response.getStatus());
    }

    private Map<String, Object> getParametersForSyncValuesMigration(String globalLevel) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(JobParameterKey.USE_SYNC_FROM_TENANT_DB, true);
        parameters.put(JobParameterKey.CONTEXT_LEVEL, globalLevel);
        parameters.put(JobParameterKey.USER_ID, PacmanWorkContextHelper.getUserId());
        parameters.put(JobParameterKey.DATE, new Date());
        return parameters;
    }

    @Test
    public void isSyncFlagDirtyTest() {
        when(syncFlagService.isSyncEnabledFor(SyncEvent.ACCOMMODATION_CONFIG_CHANGED))
                .thenReturn(true);
        Response response = syncEventAggregatorService.isSyncFlagDirty(SyncConfigParamName.ACCOMMODATION_CONFIGURATION_CHANGED.name());
        assertEquals(200, response.getStatus());
        assertTrue(response.hasEntity());
        assertTrue(Boolean.valueOf(response.getEntity().toString()));
    }

    @Test
    public void isSyncFlagDirtyTestResponseForInvalidEvent() {
        verifyResponseNotAcceptableParameter(syncEventAggregatorService.isSyncFlagDirty("ABC"));
    }

    @Test
    public void isSyncFlagDirtyTestResponseForNullEvent() {
        verifyResponseNotAcceptableParameter(syncEventAggregatorService.isSyncFlagDirty(null));
    }

    @Test
    public void isSyncFlagDirtyTestResponseForEmptyEvent() {
        verifyResponseNotAcceptableParameter(syncEventAggregatorService.isSyncFlagDirty(""));
    }

    @Test
    public void testValidSNSNotificationsSentWhenRegisterSyncEventTriggered() {
        SyncEvent syncEvent = SyncEvent.FORECAST_GROUPS_CONFIG_CHANGED;
        syncEventAggregatorService.registerSyncEvent(syncEvent, "");
        verify(configChangeService).sendSNSNotificationsForConfigChanges(syncEvent, syncFlagService.isSyncEnabledFor(syncEvent));
    }

    @Test
    void testGetPropertyStateInformation_OptimizedPath() {
        try (MockedStatic<SystemConfig> mockedStatic = mockStatic(SystemConfig.class)) {
            mockedStatic.when(SystemConfig::isOptimizedPropertyStateRetrievalEnabled).thenReturn(true);

            when(propertyStateService.getPropertyState()).thenReturn(mockPropertyStateInfo);
            PropertyState result = syncEventAggregatorService.getPropertyStateInformation();
            Assertions.assertNotNull(result);
            verify(propertyStateService, times(1)).getPropertyState();
        }
    }

    @Test
    public void isSyncFlagEnabledTest() {
        when(syncFlagService.isSyncEnabledFor(SyncEvent.AGILE_RATES_CHANGED)).thenReturn(true);
        boolean response = syncEventAggregatorService.isSyncFlagEnabled(SyncEvent.AGILE_RATES_CHANGED);
        assertTrue(response);

        when(syncFlagService.isSyncEnabledFor(SyncEvent.AGILE_RATES_CHANGED)).thenReturn(false);
        response = syncEventAggregatorService.isSyncFlagEnabled(SyncEvent.AGILE_RATES_CHANGED);
        assertFalse(response);
        verify(syncFlagService, times(2)).isSyncEnabledFor(SyncEvent.AGILE_RATES_CHANGED);
    }

    @Test
    public void testClearPropertyStalenessSkipsFlagWhenEnabledAfterCurrentCalibrationOrForecastingOrOptimization() {
        SyncFlag syncFlag = new SyncFlag();
        syncFlag.setId(SyncConfigParamName.ACCOMMODATION_CONFIGURATION_CHANGED);
        syncFlag.setEnabled(true);
        syncFlag.setNote("note");
        syncFlag.setCreateDate(LocalDateTime.now());
        syncFlag.setLastUpdatedDate(LocalDateTime.now());
        List<SyncFlag> syncFlags = Arrays.asList(syncFlag);

        when(syncFlagService.findEnabledSyncFlags()).thenReturn(syncFlags);

        syncEventAggregatorService.clearPropertyStaleness(LocalDateTime.now().minusMinutes(5));

        verify(syncFlagService, times(1)).save(syncFlags);
        verify(syncDisplayNameService, times(1)).deleteDisplayNamesExcept(any());
        verify(syncDisplayNameService, times(0)).deleteAllDisplayNames();
    }

    private void verifyResponseNotAcceptableParameter(Response response) {
        assertEquals(406, response.getStatus());
    }
}