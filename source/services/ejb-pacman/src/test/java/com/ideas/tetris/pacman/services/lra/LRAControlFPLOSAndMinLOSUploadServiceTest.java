package com.ideas.tetris.pacman.services.lra;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.common.configparams.IPConfigParamName;
import com.ideas.tetris.pacman.common.configparams.IntegrationConfigParamName;
import com.ideas.tetris.pacman.common.constants.Constants;
import com.ideas.tetris.pacman.common.workcontext.PacmanWorkContextHelper;
import com.ideas.tetris.pacman.services.configsparam.service.ConfigParameterNameService;
import com.ideas.tetris.pacman.services.dateservice.DateService;
import com.ideas.tetris.pacman.services.decision.DecisionService;
import com.ideas.tetris.pacman.services.decisiondelivery.entity.DecisionUploadDateToExternalSystemRepository;
import com.ideas.tetris.pacman.services.decisiontranslator.DecisionUtils;
import com.ideas.tetris.pacman.services.lra.dto.LRARestrictionType;
import com.ideas.tetris.pacman.services.lra.entity.DecisionLRAFPLOS;
import com.ideas.tetris.pacman.services.lra.entity.DecisionLRAMINLOS;
import com.ideas.tetris.pacman.services.lra.entity.LRARestrictionMapping;
import com.ideas.tetris.pacman.services.lra.fplos.LRAFPLOSRecommendationBdeService;
import com.ideas.tetris.pacman.services.lra.minlos.LRAMinLOSRecommendationBdeService;
import com.ideas.tetris.pacman.services.lra.strategy.AbstractLRARestrictionStrategy;
import com.ideas.tetris.pacman.services.lra.strategy.LRARateCategoryStrategy;
import com.ideas.tetris.pacman.services.lra.strategy.LRARateCodeStrategy;
import com.ideas.tetris.pacman.services.lra.strategy.RestrictionLevelStrategyFactory;
import com.ideas.tetris.pacman.services.propertygroup.service.PropertyGroupService;
import com.ideas.tetris.pacman.services.qualifiedrate.service.RateQualifiedService;
import com.ideas.tetris.platform.common.utils.dateutil.DateUtil;
import com.ideas.tetris.platform.services.configparams.PacmanConfigParamsService;
import com.ideas.tetris.platform.services.configparams.PlatformParameterValueComponent;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import org.springframework.beans.factory.annotation.Autowired;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.ideas.tetris.pacman.common.constants.Constants.BAR_DECISION_VALUE_RATEOFDAY;
import static com.ideas.tetris.pacman.common.constants.Constants.LRA_CONTROL_FPLOS;
import static com.ideas.tetris.pacman.common.constants.Constants.LRA_CONTROL_MINLOS;
import static com.ideas.tetris.pacman.common.constants.Constants.OPERA;
import static com.ideas.tetris.pacman.common.constants.Constants.PACMAN_INTEGRATION;
import static com.ideas.tetris.pacman.common.constants.Constants.UPLOAD_TYPE;
import static com.ideas.tetris.pacman.services.lra.LRAObjectMother.buildMockedAllActiveRates;
import static com.ideas.tetris.pacman.services.lra.dto.LRARestrictionType.RATE_CATEGORY_LEVEL;
import static com.ideas.tetris.pacman.services.lra.dto.LRARestrictionType.RATE_CODE_LEVEL;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class LRAControlFPLOSAndMinLOSUploadServiceTest extends AbstractG3JupiterTest {
    private LRAFPLOSRecommendationBdeService fpLOSService = new LRAFPLOSRecommendationBdeService();
    private LRAMinLOSRecommendationBdeService minLOSService = new LRAMinLOSRecommendationBdeService();
    private LRADecisionService lraDecisionService = new LRADecisionService();
    private PacmanConfigParamsService pacmanConfigParamsServiceForMinLOS;
    private PacmanConfigParamsService pacmanConfigParamsServiceForFPLOS;
    private DateService dateService;
    private DecisionService decisionService;
    private DecisionUtils decisionUtils;
    @Mock
    private LRARestrictionService lraRestrictionService;
    @Mock
    public RestrictionLevelStrategyFactory strategyFactory;
    @Autowired
    public LRARateCategoryStrategy rateCategoryStrategy = new LRARateCategoryStrategy();
    @Autowired
    public LRARateCodeStrategy rateCodeStrategy = new LRARateCodeStrategy();
    @Mock
    RateQualifiedService rateQualifiedService;
    @Autowired
    DecisionUploadDateToExternalSystemRepository decisionUploadDateToExternalSystemRepository = new DecisionUploadDateToExternalSystemRepository();

    private List<DecisionLRAFPLOS> fplosDecisions;
    private Date occupancyDate;
    private String occupancyDateStr;
    private List<DecisionLRAMINLOS> minLOSDecisions;

    @BeforeEach
    public void setUp() {
        setWorkContextProperty(TestProperty.H1);
        mockConfigParamService()
                .injectDecisionUploadRepoWithDependencies()
                .injectDateServiceWithDependencies()
                .injectLRADecisionServiceWithDependencies()
                .injectAndMockDecisionServiceWithDependencies()
                .initializeWorkContext()
                .injectLRAService(decisionService, fpLOSService)
                .injectLRAService(decisionService, minLOSService)
                .injectStrategy(rateCategoryStrategy)
                .injectStrategy(rateCodeStrategy)
                .setOccupancyDateToBeUpdated();
    }


    @Test
    public void testLRAFPLOSRecommendationForDecisionUploadWindow_RateCategory() {
        injectDecisionUtilsWithDependencies(pacmanConfigParamsServiceForFPLOS, fpLOSService)
                .mockLRARestrictionService(RATE_CATEGORY_LEVEL)
                .whenLRAFPLOGenerated()
                .thenNewlyGeneratedLRAFPLOSDecisionsAreFetched(null)
                .thenNewlyGeneratedFPLOSDecisionsAreAsserted(30)
                .whenPropertyMovedByOneDay(fplosDecisions.get(0).getDecisionId(), "LRAControlFPLOS")
                .whenLRAFPLOGenerated()
                .whenDecisionIdUpdatedForOneOccupancy()
                .thenNewlyGeneratedLRAFPLOSDecisionsAreFetched(new Date())
                .thenDifferentialFPLOSDecisionsAreAsserted(occupancyDate);
    }

    @Test
    public void testLRAMinLOSRecommendationForDecisionUploadWindow_RateCategory() {
        injectDecisionUtilsWithDependencies(pacmanConfigParamsServiceForMinLOS, minLOSService)
                .mockLRARestrictionService(RATE_CATEGORY_LEVEL)
                .whenMinLOSDecisionsAreGenerated()
                .thenNewlyGeneratedLRAMinLOSDecisionsAreFetched(null)
                .thenNewlyGeneratedMinLOSDecisionsAreAsserted(30)
                .whenPropertyMovedByOneDay(minLOSDecisions.get(0).getDecisionId(), "LRAControlMinLOS")
                .whenMinLOSDecisionsAreGenerated()
                .whenMinLOSDecisionIdUpdatedForOneOccupancy()
                .thenNewlyGeneratedLRAMinLOSDecisionsAreFetched(new Date())
                .thenDifferentialMinLOSDecisionsAreAsserted();
    }

    @Test
    public void testLRAFPLOSRecommendationForDecisionUploadWindow_RateCode() {
        injectDecisionUtilsWithDependencies(pacmanConfigParamsServiceForFPLOS, fpLOSService)
                .mockLRARestrictionService(RATE_CODE_LEVEL)
                .populateRateQualifiedWithDetails()
                .whenLRAFPLOGenerated()
                .thenNewlyGeneratedLRAFPLOSDecisionsAreFetched(null)
                .thenNewlyGeneratedFPLOSDecisionsAreAsserted(6)
                .thenAssertValidAccomTypesWereSelectedForFPLOS(3, 3)
                .whenPropertyMovedByOneDay(fplosDecisions.get(0).getDecisionId(), "LRAControlFPLOS")
                .whenLRAFPLOGenerated()
                .whenDecisionIdUpdatedForOneOccupancy()
                .thenNewlyGeneratedLRAFPLOSDecisionsAreFetched(new Date())
                .thenDifferentialFPLOSDecisionsAreAsserted(occupancyDate)
                .thenAssertValidAccomTypesWereSelectedForFPLOS(0, 1);
    }

    @Test
    public void testLRAMinLOSRecommendationForDecisionUploadWindow_RateCode() {
        injectDecisionUtilsWithDependencies(pacmanConfigParamsServiceForMinLOS, minLOSService)
                .mockLRARestrictionService(RATE_CODE_LEVEL)
                .populateRateQualifiedWithDetails()
                .whenMinLOSDecisionsAreGenerated()
                .thenNewlyGeneratedLRAMinLOSDecisionsAreFetched(null)
                .thenNewlyGeneratedMinLOSDecisionsAreAsserted(6)
                .thenAssertValidAccomTypesWereSelectedForMinLOS(3, 3)
                .whenPropertyMovedByOneDay(minLOSDecisions.get(0).getDecisionId(), "LRAControlMinLOS")
                .whenMinLOSDecisionsAreGenerated()
                .whenMinLOSDecisionIdUpdatedForOneOccupancy()
                .thenNewlyGeneratedLRAMinLOSDecisionsAreFetched(new Date())
                .thenDifferentialMinLOSDecisionsAreAsserted()
                .thenAssertValidAccomTypesWereSelectedForMinLOS(0, 1);
    }

    private LRAControlFPLOSAndMinLOSUploadServiceTest thenAssertValidAccomTypesWereSelectedForFPLOS(int expectedAccomTypeOneDecisionCount, int expectedAccomTypeTwoDecisionCount) {
        if (expectedAccomTypeOneDecisionCount > 0) {
            Assertions.assertEquals(expectedAccomTypeOneDecisionCount, fplosDecisions.stream().filter(decisionLRAMINLOS -> decisionLRAMINLOS.getAccomTypeId() == 5).collect(Collectors.toList()).size());
        }
        Assertions.assertEquals(expectedAccomTypeTwoDecisionCount, fplosDecisions.stream().filter(decisionLRAMINLOS -> decisionLRAMINLOS.getAccomTypeId() == 6).collect(Collectors.toList()).size());
        return this;
    }

    private LRAControlFPLOSAndMinLOSUploadServiceTest thenAssertValidAccomTypesWereSelectedForMinLOS(int expectedAccomTypeOneDecisionCount, int expectedAccomTypeTwoDecisionCount) {
        Assertions.assertEquals(expectedAccomTypeOneDecisionCount, minLOSDecisions.stream().filter(decisionLRAMINLOS -> decisionLRAMINLOS.getAccomTypeId() == 5).collect(Collectors.toList()).size());
        Assertions.assertEquals(expectedAccomTypeTwoDecisionCount, minLOSDecisions.stream().filter(decisionLRAMINLOS -> decisionLRAMINLOS.getAccomTypeId() == 6).collect(Collectors.toList()).size());
        return this;
    }

    private LRAControlFPLOSAndMinLOSUploadServiceTest whenDecisionIdUpdatedForOneOccupancy() {
        tenantCrudService().executeUpdateByNativeQuery("update Decision_LRA_FPLOS set FPLOS = 'NNNNYYYY' where Arrival_DT ='" + occupancyDateStr + "' and Accom_Type_ID = 5");
        return this;
    }

    private LRAControlFPLOSAndMinLOSUploadServiceTest thenDifferentialMinLOSDecisionsAreAsserted() {
        minLOSDecisions.forEach(decisionLRAMINLOS -> {
            Date arrivalDate = decisionLRAMINLOS.getArrivalDate();
            Assertions.assertTrue(arrivalDate.after(occupancyDate) || arrivalDate.equals(occupancyDate));
            if (arrivalDate.equals(occupancyDate)) {
                Assertions.assertEquals(Integer.valueOf(2), decisionLRAMINLOS.getDecision());
            } else {
                Assertions.assertEquals(Integer.valueOf(1), decisionLRAMINLOS.getDecision());
            }
        });
        return this;
    }

    private LRAControlFPLOSAndMinLOSUploadServiceTest whenMinLOSDecisionIdUpdatedForOneOccupancy() {
        tenantCrudService().executeUpdateByNativeQuery("update Decision_LRA_MINLOS set MINLOS = 2 where Arrival_DT ='" + occupancyDateStr + "' and Accom_Type_ID = 5");
        return this;
    }

    private LRAControlFPLOSAndMinLOSUploadServiceTest thenNewlyGeneratedMinLOSDecisionsAreAsserted(int expectedNumberOfDecisions) {
        Assertions.assertEquals(expectedNumberOfDecisions, minLOSDecisions.size());
        minLOSDecisions.forEach(decisionLRAMINLOS -> Assertions.assertEquals(Integer.valueOf(1), decisionLRAMINLOS.getDecision()));
        return this;
    }

    private LRAControlFPLOSAndMinLOSUploadServiceTest thenNewlyGeneratedLRAMinLOSDecisionsAreFetched(Date lastUploadedDate) {
        minLOSDecisions = lraDecisionService.getLRAMinlosDecisions(lastUploadedDate, Constants.LRA_CONTROL_MINLOS, "opera");
        return this;
    }

    private LRAControlFPLOSAndMinLOSUploadServiceTest whenMinLOSDecisionsAreGenerated() {
        minLOSService.processChunk(dateService.getOptimizationWindowStartDate(), dateService.getDecisionUploadWindowEndDateBDEVariable(), null);
        return this;
    }

    private LRAControlFPLOSAndMinLOSUploadServiceTest whenPropertyMovedByOneDay(Integer maxUploadedDecisionId, String decisionType) {
        String instr = "insert into dbo.Decision_Upload_Date_To_External_System (Decision_ID ,Decision_Name,Created_DTTM ,Modify_DTTM ,Last_Upload_DTTM " +
                ",Status,Upload_Type,External_System_Name) values(" + maxUploadedDecisionId + ",'" + decisionType + "',CURRENT_TIMESTAMP,null,CURRENT_TIMESTAMP,null,'differential','opera')";
        tenantCrudService().executeUpdateByNativeQuery(instr);

        tenantCrudService().executeUpdateByNativeQuery("UPDATE File_Metadata SET SnapShot_DT = DATEADD(D,1,SnapShot_DT) WHERE Record_Type_ID = 3 AND Process_Status_ID = 13 AND IsBDE = 1 AND property_Id = 5 ");
        return this;
    }

    private LRAControlFPLOSAndMinLOSUploadServiceTest thenDifferentialFPLOSDecisionsAreAsserted(Date occupanDate) {
        fplosDecisions.forEach(decisionLRAFPLOS -> {
            Date arrivalDate = decisionLRAFPLOS.getArrivalDate();
            Assertions.assertTrue(arrivalDate.after(occupanDate) || arrivalDate.equals(occupanDate));
            if (arrivalDate.equals(occupanDate)) {
                Assertions.assertEquals("NNNNYYYY", decisionLRAFPLOS.getDecision());
            } else {
                Assertions.assertEquals("YYYYYYYY", decisionLRAFPLOS.getDecision());
            }
        });
        return this;
    }

    private LRAControlFPLOSAndMinLOSUploadServiceTest thenNewlyGeneratedFPLOSDecisionsAreAsserted(int expectedNumberOfDecisions) {
        Assertions.assertEquals(expectedNumberOfDecisions, fplosDecisions.size());
        fplosDecisions.forEach(decisionLRAFPLOS -> Assertions.assertEquals("YYYYYYYY", decisionLRAFPLOS.getDecision()));
        return this;
    }

    private LRAControlFPLOSAndMinLOSUploadServiceTest thenNewlyGeneratedLRAFPLOSDecisionsAreFetched(Date lastUploadedDate) {
        fplosDecisions = lraDecisionService.getLRAFPLOSDecisions(lastUploadedDate, Constants.LRA_CONTROL_FPLOS, "opera");
        return this;
    }

    private LRAControlFPLOSAndMinLOSUploadServiceTest whenLRAFPLOGenerated() {
        fpLOSService.processChunk(dateService.getOptimizationWindowStartDate(), dateService.getDecisionUploadWindowEndDate(), null);
        return this;
    }

    private LRAControlFPLOSAndMinLOSUploadServiceTest injectDateServiceWithDependencies() {
        dateService = DateService.createTestInstance();
        PropertyGroupService propertyGroupService = new PropertyGroupService();
        dateService.setCrudService(tenantCrudService());
        dateService.setPropertyGroupService(propertyGroupService);
        dateService.setMultiPropertyCrudService(multiPropertyCrudService());
        dateService.setConfigParamsService(pacmanConfigParamsServiceForMinLOS);
        return this;
    }

    private LRAControlFPLOSAndMinLOSUploadServiceTest injectLRADecisionServiceWithDependencies() {
        inject(lraDecisionService, "decisionUploadDateToExternalSystemRepository", decisionUploadDateToExternalSystemRepository);
        inject(lraDecisionService, "crudService", tenantCrudService());
        inject(lraDecisionService, "dateService", dateService);
        inject(lraDecisionService, "pacmanConfigParamsService", pacmanConfigParamsServiceForMinLOS);
        PlatformParameterValueComponent parameterValueComponent = new PlatformParameterValueComponent();
        parameterValueComponent.setCrudService(globalCrudService());
        lraDecisionService.pacmanConfigParamsService.setParameterValueComponent(parameterValueComponent);
        return this;
    }

    private LRAControlFPLOSAndMinLOSUploadServiceTest injectAndMockDecisionServiceWithDependencies() {
        decisionService = DecisionService.createTestInstance();
        decisionService.setCrudService(tenantCrudService());
        decisionService.setDateServiceLocal(dateService);
        return this;
    }

    private LRAControlFPLOSAndMinLOSUploadServiceTest injectDecisionUtilsWithDependencies(PacmanConfigParamsService pacmanConfigParamsService, AbstractLRADailyBARRecommendationService service) {
        decisionUtils = new DecisionUtils();
        decisionUtils.setPacmanConfigParamsService(pacmanConfigParamsService);

        ConfigParameterNameService configParameterNameService = new ConfigParameterNameService();
        configParameterNameService.setPacmanConfigParamsService(pacmanConfigParamsService);
        decisionUtils.setConfigParameterNameService(configParameterNameService);

        inject(service, "decisionUtils", decisionUtils);
        inject(service, "dateService", dateService);
        return this;
    }

    private LRAControlFPLOSAndMinLOSUploadServiceTest injectLRAService(DecisionService decisionService, AbstractLRADailyBARRecommendationService service) {
        inject(service, "crudService", tenantCrudService());
        inject(service, "configParamsService", pacmanConfigParamsServiceForMinLOS);
        inject(service, "lraRestrictionService", lraRestrictionService);
        inject(service, "decisionService", decisionService);
        inject(service, "restrictionLevelStrategyFactory", strategyFactory);
        return this;
    }

    private LRAControlFPLOSAndMinLOSUploadServiceTest mockConfigParamService() {
        pacmanConfigParamsServiceForMinLOS = new PacmanConfigParamsService() {
            @Override
            public String getParameterValue(String parameterName) {
                if (parameterName.equals(IntegrationConfigParamName.CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value())) {
                    return "8";
                }
                if (parameterName.equals(IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value())) {
                    return "6";
                }
                if (parameterName.equals(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value())) {
                    return "false";
                }
                if (parameterName.equals(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value())) {
                    return "opera";
                }
                if (parameterName.equals(PACMAN_INTEGRATION + OPERA + "." + LRA_CONTROL_MINLOS + UPLOAD_TYPE)) {
                    return "differential";
                }
                if (parameterName.equals(IntegrationConfigParamName.RECEIVING_SYSTEMS.value())) {
                    return "opera";
                }
                if (parameterName.equals(IPConfigParamName.BAR_BAR_DECISION.value())) {
                    return BAR_DECISION_VALUE_RATEOFDAY;
                }
                return null;
            }
        };

        pacmanConfigParamsServiceForFPLOS = new PacmanConfigParamsService() {
            @Override
            public String getParameterValue(String parameterName) {
                if (parameterName.equals(IntegrationConfigParamName.CORE_PROPERTY_QUALIFIED_FPLOSMAX_LOS.value())) {
                    return "8";
                }
                if (parameterName.equals(IntegrationConfigParamName.UPLOAD_DECISION_UPLOAD_WINDOW_BDEDAYS.value())) {
                    return "6";
                }
                if (parameterName.equals(IPConfigParamName.BAR_ENABLE_SINGLE_BAR_DECISION.value())) {
                    return "false";
                }
                if (parameterName.equals(IntegrationConfigParamName.CORE_PROPERTY_EXTERNAL_SYSTEM.value())) {
                    return "opera";
                }
                if (parameterName.equals(PACMAN_INTEGRATION + OPERA + "." + LRA_CONTROL_FPLOS + UPLOAD_TYPE)) {
                    return "differential";
                }
                if (parameterName.equals(IntegrationConfigParamName.RECEIVING_SYSTEMS.value())) {
                    return "opera";
                }
                if (parameterName.equals(IPConfigParamName.BAR_BAR_DECISION.value())) {
                    return BAR_DECISION_VALUE_RATEOFDAY;
                }
                return null;
            }
        };

        return this;
    }

    private LRAControlFPLOSAndMinLOSUploadServiceTest mockLRARestrictionService(LRARestrictionType rateCategoryLevel) {
        when(lraRestrictionService.getExistingRestrictionType()).thenReturn(rateCategoryLevel);
        when(rateQualifiedService.fetchAllActiveRates()).thenReturn(buildMockedAllActiveRates());
        if (RATE_CATEGORY_LEVEL.equals(rateCategoryLevel)) {
            when(lraRestrictionService.getAllRestrictions()).thenReturn(Arrays.asList(new LRARestrictionMapping(RATE_CATEGORY_LEVEL, LRAObjectMother.RATE_CATEGORY_ONE)));
        } else {
            when(lraRestrictionService.getAllRestrictions()).thenReturn(Arrays.asList(new LRARestrictionMapping(RATE_CODE_LEVEL, 1)));
        }
        when(strategyFactory.getStrategy(RATE_CODE_LEVEL)).thenReturn(rateCodeStrategy);
        when(strategyFactory.getStrategy(RATE_CATEGORY_LEVEL)).thenReturn(rateCategoryStrategy);
        return this;
    }

    private LRAControlFPLOSAndMinLOSUploadServiceTest initializeWorkContext() {
        PacmanWorkContextHelper.setPropertyId(5);
        PacmanWorkContextHelper.setClientCode("BSTN");
        PacmanWorkContextHelper.setClientId(2);
        return this;
    }

    private LRAControlFPLOSAndMinLOSUploadServiceTest injectDecisionUploadRepoWithDependencies() {
        inject(decisionUploadDateToExternalSystemRepository, "crudService", tenantCrudService());
        return this;
    }

    private LRAControlFPLOSAndMinLOSUploadServiceTest injectStrategy(AbstractLRARestrictionStrategy rateCategoryStrategy) {
        inject(rateCategoryStrategy, "rateQualifiedService", rateQualifiedService);
        inject(rateCategoryStrategy, "crudService", tenantCrudService());
        inject(rateCategoryStrategy, "lraRestrictionService", lraRestrictionService);
        return this;
    }

    private LRAControlFPLOSAndMinLOSUploadServiceTest setOccupancyDateToBeUpdated() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        occupancyDate = DateUtil.addDaysToDate(dateService.getDecisionUploadWindowEndDate(), -1);
        occupancyDateStr = simpleDateFormat.format(occupancyDate);
        return this;
    }

    private LRAControlFPLOSAndMinLOSUploadServiceTest populateRateQualifiedWithDetails() {

        LocalDate rateStartDate = LocalDate.fromDateFields(DateUtil.removeTimeFromDate(dateService.getOptimizationWindowStartDate()));
        LocalDate rateEndDate = LocalDate.fromDateFields(DateUtil.removeTimeFromDate(dateService.getDecisionUploadWindowEndDate()));
        tenantCrudService().executeUpdateByNativeQuery("INSERT INTO Rate_Qualified VALUES (1,5,'rateOne','rateOne',null,'USD'" +
                ",'" + rateStartDate + "','" + rateEndDate.plusDays(1) + "',1,0,0,0,GETDATE(),1,1,GETDATE(),1,3,0)");

        List<Object[]> result1 = tenantCrudService().findByNativeQuery("SELECT * FROM Rate_Qualified where rate_code_description = 'rateOne' ");
        int rateQualifiedId = Integer.parseInt((result1.get(0)[0]).toString());
        //Rate details for Accom type id 5
        tenantCrudService().executeUpdateByNativeQuery("insert into Rate_Qualified_Details values ( " + rateQualifiedId +
                ", 5, '" + rateStartDate + "', '" + rateStartDate.plusDays(2) + "', 100.00, 200.00, 300.00, 400.00, 500.00, 600.00, 700.00, 1, GETDATE(), 1, GETDATE())");
        //Rate details for Accom type id 5
        tenantCrudService().executeUpdateByNativeQuery("insert into Rate_Qualified_Details values ( " + rateQualifiedId +
                ", 6, '" + rateStartDate.plusDays(3) + "', '" + rateEndDate.plusDays(1) + "', 100.00, 200.00, 300.00, 400.00, 500.00, 600.00, 700.00, 1, GETDATE(), 1, GETDATE())");
        tenantCrudService().flushAndClear();
        return this;
    }

}
