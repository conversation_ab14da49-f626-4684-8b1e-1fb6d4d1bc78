package com.ideas.tetris.pacman.services.centralrms.services.overbooking.override;

import com.ideas.tetris.pacman.services.accommodation.entity.AccomType;
import com.ideas.tetris.pacman.services.activity.service.AccomActivityRepository;
import com.ideas.tetris.pacman.services.bestavailablerate.entity.AccomActivity;
import com.ideas.tetris.pacman.services.businessanalysis.BusinessAnalysisDashboardService;
import com.ideas.tetris.pacman.services.businessanalysis.dto.BusinessAnalysisDailyDataDto;
import com.ideas.tetris.pacman.services.centralrms.models.common.SpecialEventSettings;
import com.ideas.tetris.pacman.services.centralrms.models.overrides.overbooking.CentralRMSOverbookingOverrideRequest;
import com.ideas.tetris.pacman.services.centralrms.models.overrides.overbooking.OverbookingOperation;
import com.ideas.tetris.pacman.services.centralrms.repository.overbooking.CentralRMSOverbookingOverrideRepository;
import com.ideas.tetris.pacman.services.centralrms.repository.rooms.CentralRMSRoomTypeRepository;
import com.ideas.tetris.pacman.services.centralrms.services.overbooking.override.operator.CentralRMSOverbookingOverrideOperator;
import com.ideas.tetris.pacman.services.centralrms.services.overbooking.override.operator.CentralRMSOverbookingOverrideOperatorFactory;
import com.ideas.tetris.pacman.services.centralrms.services.seasons.strategies.CentralRMSSpecialEventOverlapSplitter;
import com.ideas.tetris.pacman.services.dateservice.dto.DateParameter;
import com.ideas.tetris.pacman.services.overbooking.dto.OverbookingOverrideAccomTypeLevelSummary;
import com.ideas.tetris.pacman.services.overbooking.dto.OverbookingOverridePropertyLevelSummary;
import com.ideas.tetris.pacman.services.overbooking.entity.DecisionOvrbkAccomOVR;
import com.ideas.tetris.pacman.services.overbooking.entity.DecisionOvrbkPropertyOVR;
import com.ideas.tetris.platform.common.time.LocalDateUtils;
import org.apache.commons.lang3.Range;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CentralRMSOverbookingOverrideServiceTest {

    @Mock
    private CentralRMSOverbookingOverrideOperatorFactory centralRMSOverbookingOverrideOperatorFactory;

    @Mock
    private CentralRMSSpecialEventOverlapSplitter centralRMSSpecialEventOverlapSplitter;

    @Mock
    private CentralRMSRoomTypeRepository centralRMSRoomTypeRepository;

    @Mock
    private CentralRMSOverbookingOverrideRepository centralRMSOverbookingOverrideRepository;

    @Mock
    private BusinessAnalysisDashboardService businessAnalysisDashboardService;

    @Mock
    private AccomActivityRepository accomActivityRepository;

    @InjectMocks
    private CentralRMSOverbookingOverrideService service;

    @Nested
    class PropertyLevelSuite {

        @Test
        void constructSinglePropertyLevelOverride() {
            OverbookingOperation operation = mock(OverbookingOperation.class);
            SpecialEventSettings specialEventSettings = SpecialEventSettings.builder()
                    .specialEvents(Set.of("WINTER"))
                    .build();
            CentralRMSOverbookingOverrideRequest request = CentralRMSOverbookingOverrideRequest.builder()
                    .propertyLevelOverrideRequests(List.of(
                            CentralRMSOverbookingOverrideRequest.Request.builder()
                                    .startDate(LocalDate.of(2023, 1, 1))
                                    .endDate(LocalDate.of(2023, 2, 1))
                                    .daysOfWeek(Set.of(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY, DayOfWeek.SUNDAY))
                                    .specialEventSettings(specialEventSettings)
                                    .operation(operation)
                                    .build()
                    ))
                    .build();

            CentralRMSOverbookingOverrideOperator operator = mock(CentralRMSOverbookingOverrideOperator.class);

            BusinessAnalysisDailyDataDto dailyData = new BusinessAnalysisDailyDataDto();
            dailyData.setDate(DateParameter.fromDate(LocalDateUtils.toDate(LocalDate.of(2023, 1, 15))));

            OverbookingOverridePropertyLevelSummary summary = new OverbookingOverridePropertyLevelSummary();
            DecisionOvrbkPropertyOVR override = new DecisionOvrbkPropertyOVR();

            when(centralRMSOverbookingOverrideOperatorFactory.getOperator(operation)).thenReturn(operator);
            when(centralRMSSpecialEventOverlapSplitter.split(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 2, 1), specialEventSettings))
                    .thenReturn(List.of(Range.between(LocalDate.of(2023, 1, 15), LocalDate.of(2023, 1, 15), LocalDate::compareTo)));
            when(centralRMSOverbookingOverrideRepository.findAllPropertyLevelSummariesByOccupancyDateBetween(LocalDate.of(2023, 1, 15), LocalDate.of(2023, 1, 15)))
                    .thenReturn(Map.of(LocalDate.of(2023, 1, 15), summary));
            when(businessAnalysisDashboardService.getBusinessAnalysisDailyDataDtos(LocalDateUtils.toDate(LocalDate.of(2023, 1, 15)), LocalDateUtils.toDate(LocalDate.of(2023, 1, 15))))
                    .thenReturn(List.of(dailyData));
            when(operator.operate(summary, dailyData)).thenReturn(Optional.of(override));

            List<DecisionOvrbkPropertyOVR> result = service.constructPropertyLevelOverrides(request.getPropertyLevelOverrideRequests());

            assertEquals(List.of(override), result);
        }

        @Test
        void constructSinglePropertyLevelOverride_no_operation() {
            OverbookingOperation operation = mock(OverbookingOperation.class);
            SpecialEventSettings specialEventSettings = SpecialEventSettings.builder()
                    .specialEvents(Set.of("WINTER"))
                    .build();
            CentralRMSOverbookingOverrideRequest request = CentralRMSOverbookingOverrideRequest.builder()
                    .propertyLevelOverrideRequests(List.of(
                            CentralRMSOverbookingOverrideRequest.Request.builder()
                                    .startDate(LocalDate.of(2023, 1, 1))
                                    .endDate(LocalDate.of(2023, 2, 1))
                                    .daysOfWeek(Set.of(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY, DayOfWeek.SUNDAY))
                                    .specialEventSettings(specialEventSettings)
                                    .operation(operation)
                                    .build()
                    ))
                    .build();

            CentralRMSOverbookingOverrideOperator operator = mock(CentralRMSOverbookingOverrideOperator.class);

            BusinessAnalysisDailyDataDto dailyData = new BusinessAnalysisDailyDataDto();
            dailyData.setDate(DateParameter.fromDate(LocalDateUtils.toDate(LocalDate.of(2023, 1, 15))));

            OverbookingOverridePropertyLevelSummary summary = new OverbookingOverridePropertyLevelSummary();

            when(centralRMSOverbookingOverrideOperatorFactory.getOperator(operation)).thenReturn(operator);
            when(centralRMSSpecialEventOverlapSplitter.split(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 2, 1), specialEventSettings))
                    .thenReturn(List.of(Range.between(LocalDate.of(2023, 1, 15), LocalDate.of(2023, 1, 15), LocalDate::compareTo)));
            when(centralRMSOverbookingOverrideRepository.findAllPropertyLevelSummariesByOccupancyDateBetween(LocalDate.of(2023, 1, 15), LocalDate.of(2023, 1, 15)))
                    .thenReturn(Map.of(LocalDate.of(2023, 1, 15), summary));
            when(businessAnalysisDashboardService.getBusinessAnalysisDailyDataDtos(LocalDateUtils.toDate(LocalDate.of(2023, 1, 15)), LocalDateUtils.toDate(LocalDate.of(2023, 1, 15))))
                    .thenReturn(List.of(dailyData));
            when(operator.operate(summary, dailyData)).thenReturn(Optional.empty());

            List<DecisionOvrbkPropertyOVR> result = service.constructPropertyLevelOverrides(request.getPropertyLevelOverrideRequests());

            assertEquals(Collections.emptyList(), result);
        }

        @Test
        void constructPropertyLevelOverride_multi_operations() {
            OverbookingOperation operationA = mock(OverbookingOperation.class);
            OverbookingOperation operationB = mock(OverbookingOperation.class);
            SpecialEventSettings specialEventSettings = SpecialEventSettings.builder()
                    .specialEvents(Set.of("WINTER"))
                    .build();
            CentralRMSOverbookingOverrideRequest request = CentralRMSOverbookingOverrideRequest.builder()
                    .propertyLevelOverrideRequests(List.of(
                            CentralRMSOverbookingOverrideRequest.Request.builder()
                                    .startDate(LocalDate.of(2023, 1, 1))
                                    .endDate(LocalDate.of(2023, 2, 1))
                                    .daysOfWeek(Arrays.stream(DayOfWeek.values()).collect(Collectors.toSet()))
                                    .specialEventSettings(specialEventSettings)
                                    .operation(operationA)
                                    .build(),
                            CentralRMSOverbookingOverrideRequest.Request.builder()
                                    .startDate(LocalDate.of(2023, 4, 1))
                                    .endDate(LocalDate.of(2023, 5, 1))
                                    .daysOfWeek(Arrays.stream(DayOfWeek.values()).collect(Collectors.toSet()))
                                    .specialEventSettings(specialEventSettings)
                                    .operation(operationB)
                                    .build()
                    ))
                    .build();

            CentralRMSOverbookingOverrideOperator operatorA = mock(CentralRMSOverbookingOverrideOperator.class);
            CentralRMSOverbookingOverrideOperator operatorB = mock(CentralRMSOverbookingOverrideOperator.class);

            BusinessAnalysisDailyDataDto dailyDataA = new BusinessAnalysisDailyDataDto();
            dailyDataA.setDate(DateParameter.fromDate(LocalDateUtils.toDate(LocalDate.of(2023, 1, 15))));

            BusinessAnalysisDailyDataDto dailyDataB = new BusinessAnalysisDailyDataDto();
            dailyDataB.setDate(DateParameter.fromDate(LocalDateUtils.toDate(LocalDate.of(2023, 4, 15))));

            OverbookingOverridePropertyLevelSummary summaryA = new OverbookingOverridePropertyLevelSummary();
            DecisionOvrbkPropertyOVR overrideA = new DecisionOvrbkPropertyOVR();

            OverbookingOverridePropertyLevelSummary summaryB = new OverbookingOverridePropertyLevelSummary();
            DecisionOvrbkPropertyOVR overrideB = new DecisionOvrbkPropertyOVR();

            when(centralRMSOverbookingOverrideOperatorFactory.getOperator(operationA)).thenReturn(operatorA);
            when(centralRMSOverbookingOverrideOperatorFactory.getOperator(operationB)).thenReturn(operatorB);

            when(centralRMSSpecialEventOverlapSplitter.split(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 2, 1), specialEventSettings))
                    .thenReturn(List.of(Range.between(LocalDate.of(2023, 1, 15), LocalDate.of(2023, 1, 15), LocalDate::compareTo)));
            when(centralRMSSpecialEventOverlapSplitter.split(LocalDate.of(2023, 4, 1), LocalDate.of(2023, 5, 1), specialEventSettings))
                    .thenReturn(List.of(Range.between(LocalDate.of(2023, 4, 15), LocalDate.of(2023, 4, 15), LocalDate::compareTo)));

            when(centralRMSOverbookingOverrideRepository.findAllPropertyLevelSummariesByOccupancyDateBetween(LocalDate.of(2023, 1, 15), LocalDate.of(2023, 1, 15)))
                    .thenReturn(Map.of(LocalDate.of(2023, 1, 15), summaryA));
            when(centralRMSOverbookingOverrideRepository.findAllPropertyLevelSummariesByOccupancyDateBetween(LocalDate.of(2023, 4, 15), LocalDate.of(2023, 4, 15)))
                    .thenReturn(Map.of(LocalDate.of(2023, 4, 15), summaryB));

            when(businessAnalysisDashboardService.getBusinessAnalysisDailyDataDtos(LocalDateUtils.toDate(LocalDate.of(2023, 1, 15)), LocalDateUtils.toDate(LocalDate.of(2023, 1, 15))))
                    .thenReturn(List.of(dailyDataA));
            when(businessAnalysisDashboardService.getBusinessAnalysisDailyDataDtos(LocalDateUtils.toDate(LocalDate.of(2023, 4, 15)), LocalDateUtils.toDate(LocalDate.of(2023, 4, 15))))
                    .thenReturn(List.of(dailyDataB));

            when(operatorA.operate(summaryA, dailyDataA)).thenReturn(Optional.of(overrideA));
            when(operatorB.operate(summaryB, dailyDataB)).thenReturn(Optional.of(overrideB));

            List<DecisionOvrbkPropertyOVR> result = service.constructPropertyLevelOverrides(request.getPropertyLevelOverrideRequests());

            assertEquals(List.of(overrideA, overrideB), result);
        }

        @Test
        void constructPropertyLevelOverride_no_operation_unapplicable_dow() {
            OverbookingOperation operation = mock(OverbookingOperation.class);
            SpecialEventSettings specialEventSettings = SpecialEventSettings.builder()
                    .specialEvents(Set.of("WINTER"))
                    .build();
            CentralRMSOverbookingOverrideRequest request = CentralRMSOverbookingOverrideRequest.builder()
                    .propertyLevelOverrideRequests(List.of(
                            CentralRMSOverbookingOverrideRequest.Request.builder()
                                    .startDate(LocalDate.of(2023, 1, 1))
                                    .endDate(LocalDate.of(2023, 2, 1))
                                    .daysOfWeek(Set.of(DayOfWeek.MONDAY))
                                    .specialEventSettings(specialEventSettings)
                                    .operation(operation)
                                    .build()
                    ))
                    .build();

            List<DecisionOvrbkPropertyOVR> result = service.constructPropertyLevelOverrides(request.getPropertyLevelOverrideRequests());

            assertEquals(Collections.emptyList(), result);
            verifyNoInteractions(centralRMSOverbookingOverrideRepository);
            verifyNoInteractions(businessAnalysisDashboardService);
        }
    }

    @Nested
    class RoomTypeLevelSuite {

        @Test
        void constructSingleRoomTypeLevelOverrides() {
            OverbookingOperation operation = mock(OverbookingOperation.class);
            SpecialEventSettings specialEventSettings = SpecialEventSettings.builder()
                    .specialEvents(Set.of("WINTER"))
                    .build();
            CentralRMSOverbookingOverrideRequest request = CentralRMSOverbookingOverrideRequest.builder()
                    .roomClassNameToRoomTypeNameToRequests(Map.of(
                            "DELUXE",
                            Map.of("KNG",
                                    List.of(CentralRMSOverbookingOverrideRequest.Request.builder()
                                            .startDate(LocalDate.of(2023, 1, 1))
                                            .endDate(LocalDate.of(2023, 2, 1))
                                            .daysOfWeek(Set.of(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY, DayOfWeek.SUNDAY))
                                            .specialEventSettings(specialEventSettings)
                                            .operation(operation)
                                            .build()
                                    )
                            )

                    ))
                    .build();

            CentralRMSOverbookingOverrideOperator operator = mock(CentralRMSOverbookingOverrideOperator.class);

            AccomActivity activity = new AccomActivity();
            activity.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2023, 1, 15)));

            AccomType accomType = new AccomType();
            accomType.setName("KNG");

            OverbookingOverrideAccomTypeLevelSummary summary = new OverbookingOverrideAccomTypeLevelSummary();
            DecisionOvrbkAccomOVR override = new DecisionOvrbkAccomOVR();

            when(centralRMSRoomTypeRepository.getActiveRoomTypesByNameIn(Set.of("KNG"))).thenReturn(List.of(accomType));
            when(centralRMSOverbookingOverrideOperatorFactory.getOperator(operation)).thenReturn(operator);
            when(centralRMSSpecialEventOverlapSplitter.split(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 2, 1), specialEventSettings))
                    .thenReturn(List.of(Range.between(LocalDate.of(2023, 1, 15), LocalDate.of(2023, 1, 15), LocalDate::compareTo)));
            when(centralRMSOverbookingOverrideRepository.findAllRoomTypeLevelSummariesByRoomTypeAndOccupancyDateBetween(accomType, LocalDate.of(2023, 1, 15), LocalDate.of(2023, 1, 15)))
                    .thenReturn(Map.of(LocalDate.of(2023, 1, 15), summary));
            when(accomActivityRepository.findByDateRangeAndAccomType(LocalDate.of(2023, 1, 15), LocalDate.of(2023, 1, 15), accomType))
                    .thenReturn(List.of(activity));
            when(operator.operate(summary, activity)).thenReturn(Optional.of(override));

            List<DecisionOvrbkAccomOVR> result = service.constructRoomTypeLevelOverrides(request.getRoomClassNameToRoomTypeNameToRequests());

            assertEquals(List.of(override), result);
        }

        @Test
        void constructRoomTypeLevelOverrides_no_operation() {
            OverbookingOperation operation = mock(OverbookingOperation.class);
            SpecialEventSettings specialEventSettings = SpecialEventSettings.builder()
                    .specialEvents(Set.of("WINTER"))
                    .build();
            CentralRMSOverbookingOverrideRequest request = CentralRMSOverbookingOverrideRequest.builder()
                    .roomClassNameToRoomTypeNameToRequests(Map.of(
                            "DELUXE",
                            Map.of("KNG",
                                    List.of(CentralRMSOverbookingOverrideRequest.Request.builder()
                                            .startDate(LocalDate.of(2023, 1, 1))
                                            .endDate(LocalDate.of(2023, 2, 1))
                                            .daysOfWeek(Set.of(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY, DayOfWeek.SUNDAY))
                                            .specialEventSettings(specialEventSettings)
                                            .operation(operation)
                                            .build()
                                    )
                            )

                    ))
                    .build();

            CentralRMSOverbookingOverrideOperator operator = mock(CentralRMSOverbookingOverrideOperator.class);

            AccomActivity activity = new AccomActivity();
            activity.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2023, 1, 15)));

            AccomType accomType = new AccomType();
            accomType.setName("KNG");

            OverbookingOverrideAccomTypeLevelSummary summary = new OverbookingOverrideAccomTypeLevelSummary();

            when(centralRMSRoomTypeRepository.getActiveRoomTypesByNameIn(Set.of("KNG"))).thenReturn(List.of(accomType));
            when(centralRMSOverbookingOverrideOperatorFactory.getOperator(operation)).thenReturn(operator);
            when(centralRMSSpecialEventOverlapSplitter.split(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 2, 1), specialEventSettings))
                    .thenReturn(List.of(Range.between(LocalDate.of(2023, 1, 15), LocalDate.of(2023, 1, 15), LocalDate::compareTo)));
            when(centralRMSOverbookingOverrideRepository.findAllRoomTypeLevelSummariesByRoomTypeAndOccupancyDateBetween(accomType, LocalDate.of(2023, 1, 15), LocalDate.of(2023, 1, 15)))
                    .thenReturn(Map.of(LocalDate.of(2023, 1, 15), summary));
            when(accomActivityRepository.findByDateRangeAndAccomType(LocalDate.of(2023, 1, 15), LocalDate.of(2023, 1, 15), accomType))
                    .thenReturn(List.of(activity));
            when(operator.operate(summary, activity)).thenReturn(Optional.empty());

            List<DecisionOvrbkAccomOVR> result = service.constructRoomTypeLevelOverrides(request.getRoomClassNameToRoomTypeNameToRequests());

            assertEquals(Collections.emptyList(), result);
        }

        @Test
        void constructRoomTypeLevelOverrides_multi_operation() {
            OverbookingOperation operationA = mock(OverbookingOperation.class);
            OverbookingOperation operationB = mock(OverbookingOperation.class);
            SpecialEventSettings specialEventSettings = SpecialEventSettings.builder()
                    .specialEvents(Set.of("WINTER"))
                    .build();
            CentralRMSOverbookingOverrideRequest request = CentralRMSOverbookingOverrideRequest.builder()
                    .roomClassNameToRoomTypeNameToRequests(Map.of(
                            "DELUXE",
                            Map.of("KNG",
                                    List.of(
                                            CentralRMSOverbookingOverrideRequest.Request.builder()
                                                    .startDate(LocalDate.of(2023, 1, 1))
                                                    .endDate(LocalDate.of(2023, 2, 1))
                                                    .daysOfWeek(Arrays.stream(DayOfWeek.values()).collect(Collectors.toSet()))
                                                    .specialEventSettings(specialEventSettings)
                                                    .operation(operationA)
                                                    .build(),
                                            CentralRMSOverbookingOverrideRequest.Request.builder()
                                                    .startDate(LocalDate.of(2023, 4, 1))
                                                    .endDate(LocalDate.of(2023, 5, 1))
                                                    .daysOfWeek(Arrays.stream(DayOfWeek.values()).collect(Collectors.toSet()))
                                                    .specialEventSettings(specialEventSettings)
                                                    .operation(operationB)
                                                    .build()
                                    )
                            )

                    ))
                    .build();

            CentralRMSOverbookingOverrideOperator operatorA = mock(CentralRMSOverbookingOverrideOperator.class);
            CentralRMSOverbookingOverrideOperator operatorB = mock(CentralRMSOverbookingOverrideOperator.class);

            AccomActivity activityA = new AccomActivity();
            activityA.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2023, 1, 15)));

            AccomActivity activityB = new AccomActivity();
            activityB.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2023, 4, 15)));

            AccomType accomType = new AccomType();
            accomType.setName("KNG");

            OverbookingOverrideAccomTypeLevelSummary summaryA = new OverbookingOverrideAccomTypeLevelSummary();
            DecisionOvrbkAccomOVR overrideA = new DecisionOvrbkAccomOVR();

            OverbookingOverrideAccomTypeLevelSummary summaryB = new OverbookingOverrideAccomTypeLevelSummary();
            DecisionOvrbkAccomOVR overrideB = new DecisionOvrbkAccomOVR();

            when(centralRMSRoomTypeRepository.getActiveRoomTypesByNameIn(Set.of("KNG"))).thenReturn(List.of(accomType));

            when(centralRMSOverbookingOverrideOperatorFactory.getOperator(operationA)).thenReturn(operatorA);
            when(centralRMSOverbookingOverrideOperatorFactory.getOperator(operationB)).thenReturn(operatorB);

            when(centralRMSSpecialEventOverlapSplitter.split(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 2, 1), specialEventSettings))
                    .thenReturn(List.of(Range.between(LocalDate.of(2023, 1, 15), LocalDate.of(2023, 1, 15), LocalDate::compareTo)));
            when(centralRMSSpecialEventOverlapSplitter.split(LocalDate.of(2023, 4, 1), LocalDate.of(2023, 5, 1), specialEventSettings))
                    .thenReturn(List.of(Range.between(LocalDate.of(2023, 4, 15), LocalDate.of(2023, 4, 15), LocalDate::compareTo)));

            when(centralRMSOverbookingOverrideRepository.findAllRoomTypeLevelSummariesByRoomTypeAndOccupancyDateBetween(accomType, LocalDate.of(2023, 1, 15), LocalDate.of(2023, 1, 15)))
                    .thenReturn(Map.of(LocalDate.of(2023, 1, 15), summaryA));
            when(centralRMSOverbookingOverrideRepository.findAllRoomTypeLevelSummariesByRoomTypeAndOccupancyDateBetween(accomType, LocalDate.of(2023, 4, 15), LocalDate.of(2023, 4, 15)))
                    .thenReturn(Map.of(LocalDate.of(2023, 4, 15), summaryB));

            when(accomActivityRepository.findByDateRangeAndAccomType(LocalDate.of(2023, 1, 15), LocalDate.of(2023, 1, 15), accomType))
                    .thenReturn(List.of(activityA));
            when(accomActivityRepository.findByDateRangeAndAccomType(LocalDate.of(2023, 4, 15), LocalDate.of(2023, 4, 15), accomType))
                    .thenReturn(List.of(activityB));

            when(operatorA.operate(summaryA, activityA)).thenReturn(Optional.of(overrideA));
            when(operatorB.operate(summaryB, activityB)).thenReturn(Optional.of(overrideB));

            List<DecisionOvrbkAccomOVR> result = service.constructRoomTypeLevelOverrides(request.getRoomClassNameToRoomTypeNameToRequests());

            assertEquals(List.of(overrideA, overrideB), result);
        }

        @Test
        void constructRoomTypeLevelOverrides_multi_room_type_multi_operation() {
            OverbookingOperation operationA = mock(OverbookingOperation.class);
            OverbookingOperation operationB = mock(OverbookingOperation.class);
            SpecialEventSettings specialEventSettings = SpecialEventSettings.builder()
                    .specialEvents(Set.of("WINTER"))
                    .build();
            CentralRMSOverbookingOverrideRequest request = CentralRMSOverbookingOverrideRequest.builder()
                    .roomClassNameToRoomTypeNameToRequests(Map.of(
                            "DELUXE",
                            Map.of("KNG",
                                    List.of(
                                            CentralRMSOverbookingOverrideRequest.Request.builder()
                                                    .startDate(LocalDate.of(2023, 1, 1))
                                                    .endDate(LocalDate.of(2023, 2, 1))
                                                    .daysOfWeek(Arrays.stream(DayOfWeek.values()).collect(Collectors.toSet()))
                                                    .specialEventSettings(specialEventSettings)
                                                    .operation(operationA)
                                                    .build()
                                    ),
                                    "QN",
                                    List.of(
                                            CentralRMSOverbookingOverrideRequest.Request.builder()
                                                    .startDate(LocalDate.of(2023, 4, 1))
                                                    .endDate(LocalDate.of(2023, 5, 1))
                                                    .daysOfWeek(Arrays.stream(DayOfWeek.values()).collect(Collectors.toSet()))
                                                    .specialEventSettings(specialEventSettings)
                                                    .operation(operationB)
                                                    .build()
                                    )
                            )

                    ))
                    .build();

            CentralRMSOverbookingOverrideOperator operatorA = mock(CentralRMSOverbookingOverrideOperator.class);
            CentralRMSOverbookingOverrideOperator operatorB = mock(CentralRMSOverbookingOverrideOperator.class);

            AccomActivity activityA = new AccomActivity();
            activityA.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2023, 1, 15)));

            AccomActivity activityB = new AccomActivity();
            activityB.setOccupancyDate(LocalDateUtils.toDate(LocalDate.of(2023, 4, 15)));

            AccomType kngAccomType = new AccomType();
            kngAccomType.setName("KNG");

            AccomType qnAccomType = new AccomType();
            qnAccomType.setName("QN");

            OverbookingOverrideAccomTypeLevelSummary summaryA = new OverbookingOverrideAccomTypeLevelSummary();
            DecisionOvrbkAccomOVR overrideA = new DecisionOvrbkAccomOVR();

            OverbookingOverrideAccomTypeLevelSummary summaryB = new OverbookingOverrideAccomTypeLevelSummary();
            DecisionOvrbkAccomOVR overrideB = new DecisionOvrbkAccomOVR();

            when(centralRMSRoomTypeRepository.getActiveRoomTypesByNameIn(Set.of("KNG", "QN"))).thenReturn(List.of(kngAccomType, qnAccomType));

            when(centralRMSOverbookingOverrideOperatorFactory.getOperator(operationA)).thenReturn(operatorA);
            when(centralRMSOverbookingOverrideOperatorFactory.getOperator(operationB)).thenReturn(operatorB);

            when(centralRMSSpecialEventOverlapSplitter.split(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 2, 1), specialEventSettings))
                    .thenReturn(List.of(Range.between(LocalDate.of(2023, 1, 15), LocalDate.of(2023, 1, 15), LocalDate::compareTo)));
            when(centralRMSSpecialEventOverlapSplitter.split(LocalDate.of(2023, 4, 1), LocalDate.of(2023, 5, 1), specialEventSettings))
                    .thenReturn(List.of(Range.between(LocalDate.of(2023, 4, 15), LocalDate.of(2023, 4, 15), LocalDate::compareTo)));

            when(centralRMSOverbookingOverrideRepository.findAllRoomTypeLevelSummariesByRoomTypeAndOccupancyDateBetween(kngAccomType, LocalDate.of(2023, 1, 15), LocalDate.of(2023, 1, 15)))
                    .thenReturn(Map.of(LocalDate.of(2023, 1, 15), summaryA));
            when(centralRMSOverbookingOverrideRepository.findAllRoomTypeLevelSummariesByRoomTypeAndOccupancyDateBetween(qnAccomType, LocalDate.of(2023, 4, 15), LocalDate.of(2023, 4, 15)))
                    .thenReturn(Map.of(LocalDate.of(2023, 4, 15), summaryB));

            when(accomActivityRepository.findByDateRangeAndAccomType(LocalDate.of(2023, 1, 15), LocalDate.of(2023, 1, 15), kngAccomType))
                    .thenReturn(List.of(activityA));
            when(accomActivityRepository.findByDateRangeAndAccomType(LocalDate.of(2023, 4, 15), LocalDate.of(2023, 4, 15), qnAccomType))
                    .thenReturn(List.of(activityB));

            when(operatorA.operate(summaryA, activityA)).thenReturn(Optional.of(overrideA));
            when(operatorB.operate(summaryB, activityB)).thenReturn(Optional.of(overrideB));

            List<DecisionOvrbkAccomOVR> result = service.constructRoomTypeLevelOverrides(request.getRoomClassNameToRoomTypeNameToRequests());

            // Order doesn't matter in production code, but does in tests
            assertEquals(2, result.size());
            assertTrue(result.containsAll(List.of(overrideA, overrideB)));
        }

        @Test
        void constructRoomTypeLevelOverrides_no_operation_unapplicable_dow() {
            OverbookingOperation operation = mock(OverbookingOperation.class);
            SpecialEventSettings specialEventSettings = SpecialEventSettings.builder()
                    .specialEvents(Set.of("WINTER"))
                    .build();
            CentralRMSOverbookingOverrideRequest request = CentralRMSOverbookingOverrideRequest.builder()
                    .roomClassNameToRoomTypeNameToRequests(Map.of(
                            "DELUXE",
                            Map.of("KNG",
                                    List.of(CentralRMSOverbookingOverrideRequest.Request.builder()
                                            .startDate(LocalDate.of(2023, 1, 1))
                                            .endDate(LocalDate.of(2023, 2, 1))
                                            .daysOfWeek(Set.of(DayOfWeek.MONDAY))
                                            .specialEventSettings(specialEventSettings)
                                            .operation(operation)
                                            .build()
                                    )
                            )

                    ))
                    .build();

            AccomType accomType = new AccomType();
            accomType.setName("KNG");

            when(centralRMSRoomTypeRepository.getActiveRoomTypesByNameIn(Set.of("KNG"))).thenReturn(List.of(accomType));
            when(centralRMSSpecialEventOverlapSplitter.split(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 2, 1), specialEventSettings))
                    .thenReturn(List.of(Range.between(LocalDate.of(2023, 1, 15), LocalDate.of(2023, 1, 15), LocalDate::compareTo)));

            List<DecisionOvrbkAccomOVR> result = service.constructRoomTypeLevelOverrides(request.getRoomClassNameToRoomTypeNameToRequests());

            assertEquals(Collections.emptyList(), result);
            verifyNoInteractions(centralRMSOverbookingOverrideRepository);
            verifyNoInteractions(accomActivityRepository);
        }
    }

}
