package com.ideas.tetris.pacman.services.profitpopulation;

import com.ideas.g3.data.TestProperty;
import com.ideas.g3.test.AbstractG3JupiterTest;
import com.ideas.tetris.pacman.services.revenuestreams.RevenueStreamDetailCreator;
import com.ideas.tetris.pacman.services.revenuestreams.entity.RevenueStream;
import com.ideas.tetris.platform.common.crudservice.QueryParameter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

import static com.ideas.tetris.pacman.services.groupblock.GroupMasterTest.DEFINITE;
import static com.ideas.tetris.pacman.services.groupblock.GroupMasterTest.TENTATIVE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.spy;


@MockitoSettings(strictness = Strictness.LENIENT)
public class ProfitPopulationServiceExcludeComplimentaryRevenueDataTest extends AbstractG3JupiterTest {
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    private final int propertyId = 5;
    private final int mktSegId1 = 1;
    private final int mktSegId2 = 2;
    private final int mktSegId3 = 3;
    private final int mktSegId4 = 4;
    private final int accomTypeId4 = 4;
    private final int accomTypeId5 = 5;
    private final int accomTypeId6 = 6;
    private final int accomTypeId7 = 7;

    private final BigDecimal roomRevenueForMktSegId1 = new BigDecimal("50");
    private final BigDecimal roomRevenueForMktSegId2 = new BigDecimal("30");
    private final BigDecimal roomRevenueForMktSegId3 = new BigDecimal("20");
    private final BigDecimal roomRevenueForMktSegId4 = new BigDecimal("70");
    private final BigDecimal blockRateForAccomTypeId4 = new BigDecimal("75");
    private final BigDecimal blockRateForAccomTypeId5 = new BigDecimal("50");
    private final BigDecimal blockRateForAccomTypeId6 = new BigDecimal("25");
    private final BigDecimal blockRateForAccomTypeId7 = new BigDecimal("5");
    ProfitPopulationService profitPopulationService;
    RevenueStreamDetailCreator revenueStreamDetailCreator;
    private LocalDate startDate;
    private LocalDate endDate;
    private LocalDate caughtUpDate;
    private LocalDate BusinessDayEndDt;

    @BeforeEach
    public void setUp() throws ParseException {
        profitPopulationService = spy(new ProfitPopulationService());
        inject(profitPopulationService, "tenantCrudService", tenantCrudService());
        caughtUpDate = getLocalDate();
        startDate = caughtUpDate.plusDays(15);
        endDate = caughtUpDate.plusDays(16);
        BusinessDayEndDt = caughtUpDate.minusDays(1);
        setWorkContextProperty(TestProperty.H1);
        setupLastOptimizationAccomActivity(startDate);
        setupLastOptimizationMktAccomActivityData(startDate);
        setupLastOptimizationTotalActivity(startDate);
        revenueStreamDetailCreator = new RevenueStreamDetailCreator(tenantCrudService());
        setUpRevenueStreamDetail(startDate);
        setUpRevenueStreamCost(caughtUpDate, endDate);
        updateMktSegToMarkComplimentary();
    }

    @Test
    public void verifyProfitWhenServiceCostIsNull() {
        //Given
        setUpReservationNightData();
        setUpGroupMasterData(DEFINITE);
        setUpGroupBlockData();

        //When
        tenantCrudService().executeUpdateByNativeQuery("exec usp_populate_profit_ccv3_exclude_complimentary_revenue " + "'" + caughtUpDate + "'" + ",'" + startDate + "','" + endDate + "'," + propertyId + "," + "1");
        //Then
        //125+0+45+75 = 245 at property level
        verifyTotalProfitAtPropertyLevel(new BigDecimal("245.00000").setScale(5, RoundingMode.DOWN), startDate);
        //50 from reservation night, 75 from group master  75*(9-8) as service cast null
        verifyTotalProfitAtMktSegLevel(new BigDecimal("125.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId1, accomTypeId4);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("125.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId4);
        //0 as complimentary market segment
        verifyTotalProfitAtMktSegLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId2, accomTypeId5);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId5);
        //20 from reservation night, 25 from group master  25*(5-4) as service cast null
        verifyTotalProfitAtMktSegLevel(new BigDecimal("45.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId3, accomTypeId6);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("45.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId6);
        //70 from reservation night, 5 from group master  5*(5-4) as service cast null
        verifyTotalProfitAtMktSegLevel(new BigDecimal("75.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId4, accomTypeId7);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("75.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId7);
    }

    @Test
    public void verifyProfitWhenServiceCostNotApplicableDueToDifferentBusinessType() {
        //Given
        setUpReservationNightData();
        setUpGroupMasterData(DEFINITE);
        setUpGroupBlockData();
        setUpServiceCostCfgData(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN));
        //When
        tenantCrudService().executeUpdateByNativeQuery("exec usp_populate_profit_ccv3_exclude_complimentary_revenue " + "'" + caughtUpDate + "'" + ",'" + startDate + "','" + endDate + "'," + propertyId + "," + "1");
        //Then
        //120+0+40+70 = 230 at property level as profit in first query will return revenue as is and service cost 5 will be considered in 4th query
        verifyTotalProfitAtPropertyLevel(new BigDecimal("230.00000").setScale(5, RoundingMode.DOWN), startDate);
        //50 from reservation night, 70 from group master  70*(9-8)
        verifyTotalProfitAtMktSegLevel(new BigDecimal("120.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId1, accomTypeId4);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("120.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId4);
        //0 as complimentary market segment
        verifyTotalProfitAtMktSegLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId2, accomTypeId5);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId5);
        //20 from reservation night, 20 from group master  20*(5-4)
        verifyTotalProfitAtMktSegLevel(new BigDecimal("40.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId3, accomTypeId6);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("40.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId6);
        //70 from reservation night, 0 from group master  0*(5-4)
        verifyTotalProfitAtMktSegLevel(new BigDecimal("70.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId4, accomTypeId7);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("70.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId7);
    }

    @Test
    public void verifyProfitWhenServiceCostNotApplicableDueOccupancyDateIsNotGreaterThanArrivalDate() {
        //Given
        setUpReservationNightData();
        setUpGroupMasterData(DEFINITE);
        setUpGroupBlockData();
        setUpServiceCostCfgData(new BigDecimal("10.00000").setScale(5, RoundingMode.DOWN));
        //When
        tenantCrudService().executeUpdateByNativeQuery("exec usp_populate_profit_ccv3_exclude_complimentary_revenue " + "'" + caughtUpDate + "'" + ",'" + startDate + "','" + endDate + "'," + propertyId + "," + "1");
        //Then
        //120+0+40+70 = 230 at property level as profit in first query will have revenue value as is and service cost 5 will be considered in 4th query
        verifyTotalProfitAtPropertyLevel(new BigDecimal("230.00000").setScale(5, RoundingMode.DOWN), startDate);
        //50 from reservation night, 70 from group master  70*(9-8)
        verifyTotalProfitAtMktSegLevel(new BigDecimal("120.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId1, accomTypeId4);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("120.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId4);
        //0 as complimentary market segment
        verifyTotalProfitAtMktSegLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId2, accomTypeId5);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId5);
        //20 from reservation night, 20 from group master  20*(5-4)
        verifyTotalProfitAtMktSegLevel(new BigDecimal("40.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId3, accomTypeId6);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("40.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId6);
        //70 from reservation night, 0 from group master  0*(5-4)
        verifyTotalProfitAtMktSegLevel(new BigDecimal("70.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId4, accomTypeId7);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("70.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId7);
    }

    @Test
    public void verifyProfitWhenSomeReservationsAreCancelledOrNoShows() {
        //Given
        setUpReservationNightData();
        setUpGroupMasterData(DEFINITE);
        setUpGroupBlockData();
        setUpServiceCostCfgData(new BigDecimal("10.00000").setScale(5, RoundingMode.DOWN));
        tenantCrudService().executeUpdateByNativeQuery("update Reservation_Night set Individual_status = 'NS' where Reservation_Identifier= 12345678902");
        tenantCrudService().executeUpdateByNativeQuery("update Reservation_Night set Individual_status = 'XX' where Reservation_Identifier= 12345678904");
        //When
        tenantCrudService().executeUpdateByNativeQuery("exec usp_populate_profit_ccv3_exclude_complimentary_revenue " + "'" + caughtUpDate + "'" + ",'" + startDate + "','" + endDate + "'," + propertyId + "," + "1");
        //Then
        //120+0+40+70 = 230 at property level as profit in first query and 3rd query will have revenue value as is and service cost 5 will be considered in 4th query
        verifyTotalProfitAtPropertyLevel(new BigDecimal("230.00000").setScale(5, RoundingMode.DOWN), startDate);
        //50 from reservation night, 70 from group master  70*(9-8)  // first query
        verifyTotalProfitAtMktSegLevel(new BigDecimal("120.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId1, accomTypeId4);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("120.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId4);
        //0 as complimentary market segment
        verifyTotalProfitAtMktSegLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId2, accomTypeId5);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId5);
        //20 from reservation night, 20 from group master  20*(5-4) // first query
        verifyTotalProfitAtMktSegLevel(new BigDecimal("40.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId3, accomTypeId6);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("40.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId6);
        //70 from reservation night, 0 from group master  0*(5-4) // third query as Individual_status = XX
        verifyTotalProfitAtMktSegLevel(new BigDecimal("70.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId4, accomTypeId7);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("70.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId7);
    }

    @Test
    public void verifyProfitWhenGroupMasterEntriesWithGroupStatusCodeIsOtherThanDefinite() {
        //Given
        setUpReservationNightData();
        setUpGroupMasterData(TENTATIVE);
        setUpGroupBlockData();
        setUpServiceCostCfgData(new BigDecimal("10.00000").setScale(5, RoundingMode.DOWN));
        //When
        tenantCrudService().executeUpdateByNativeQuery("exec usp_populate_profit_ccv3_exclude_complimentary_revenue " + "'" + caughtUpDate + "'" + ",'" + startDate + "','" + endDate + "'," + propertyId + "," + "1");
        //Then
        //50+0+20+70 = 140 at property level as profit in first query  will have revenue value as is and nothing from remaining queries
        verifyTotalProfitAtPropertyLevel(new BigDecimal("140.00000").setScale(5, RoundingMode.DOWN), startDate);
        //50 from reservation night, 0 from group master
        verifyTotalProfitAtMktSegLevel(new BigDecimal("50.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId1, accomTypeId4);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("50.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId4);
        //0 as complimentary market segment
        verifyTotalProfitAtMktSegLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId2, accomTypeId5);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId5);
        //20 from reservation night, 0 from group master
        verifyTotalProfitAtMktSegLevel(new BigDecimal("20.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId3, accomTypeId6);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("20.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId6);
        //70 from reservation night, 0 from group master
        verifyTotalProfitAtMktSegLevel(new BigDecimal("70.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId4, accomTypeId7);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("70.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId7);
    }

    @Test
    public void verifyProfitWhenEntriesPresentInGroupData() {
        //Given
        setUpGroupMasterData(DEFINITE);
        setUpGroupBlockData();
        setUpServiceCostCfgData(new BigDecimal("10.00000").setScale(5, RoundingMode.DOWN));
        //When
        tenantCrudService().executeUpdateByNativeQuery("exec usp_populate_profit_ccv3_exclude_complimentary_revenue " + "'" + caughtUpDate + "'" + ",'" + startDate + "','" + endDate + "'," + propertyId + "," + "1");
        //Then
        //70+0+20+0 = 90 at property level profit comes from 4th query with service cost 5
        verifyTotalProfitAtPropertyLevel(new BigDecimal("90.00000").setScale(5, RoundingMode.DOWN), startDate);
        //70*(9-8)
        verifyTotalProfitAtMktSegLevel(new BigDecimal("70.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId1, accomTypeId4);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("70.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId4);
        //0 as complimentary market segment
        verifyTotalProfitAtMktSegLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId2, accomTypeId5);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId5);
        //20 from group master  20*(5-4)
        verifyTotalProfitAtMktSegLevel(new BigDecimal("20.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId3, accomTypeId6);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("20.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId6);
        //0 from group master  0*(5-4)
        verifyTotalProfitAtMktSegLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId4, accomTypeId7);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId7);
    }

    //

    @Test
    public void verifyProfitWhenServiceCostIsNull_non_zero_optimization() {
        //Given
        setUpReservationNightData();
        setUpGroupMasterData(DEFINITE);
        setUpGroupBlockData();

        //When
        tenantCrudService().executeUpdateByNativeQuery("exec usp_populate_profit_ccv3_non_zero_optimization_exclude_complimentary_revenue " + "'" + caughtUpDate + "'" + ",'" + startDate + "','" + endDate + "'," + propertyId + "," + "1");
        //Then
        //125+0+45+75 = 245 at property level
        verifyTotalProfitAtPropertyLevel(new BigDecimal("245.00000").setScale(5, RoundingMode.DOWN), startDate);
        //50 from reservation night, 75 from group master  75*(9-8) as service cast null
        verifyTotalProfitAtMktSegLevel(new BigDecimal("125.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId1, accomTypeId4);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("125.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId4);
        //0 as complimentary market segment
        verifyTotalProfitAtMktSegLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId2, accomTypeId5);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId5);
        //20 from reservation night, 25 from group master  25*(5-4) as service cast null
        verifyTotalProfitAtMktSegLevel(new BigDecimal("45.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId3, accomTypeId6);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("45.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId6);
        //70 from reservation night, 5 from group master  5*(5-4) as service cast null
        verifyTotalProfitAtMktSegLevel(new BigDecimal("75.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId4, accomTypeId7);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("75.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId7);
    }

    @Test
    public void verifyProfitWhenServiceCostNotApplicableDueToDifferentBusinessType_non_zero_optimization() {
        //Given
        setUpReservationNightData();
        setUpGroupMasterData(DEFINITE);
        setUpGroupBlockData();
        setUpServiceCostCfgData(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN));
        //When
        tenantCrudService().executeUpdateByNativeQuery("exec usp_populate_profit_ccv3_non_zero_optimization_exclude_complimentary_revenue " + "'" + caughtUpDate + "'" + ",'" + startDate + "','" + endDate + "'," + propertyId + "," + "1");
        //Then
        //120+0+40+70 = 230 at property level as profit in first query will return revenue as is and service cost 5 will be considered in 4th query
        verifyTotalProfitAtPropertyLevel(new BigDecimal("230.00000").setScale(5, RoundingMode.DOWN), startDate);
        //50 from reservation night, 70 from group master  70*(9-8)
        verifyTotalProfitAtMktSegLevel(new BigDecimal("120.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId1, accomTypeId4);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("120.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId4);
        //0 as complimentary market segment
        verifyTotalProfitAtMktSegLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId2, accomTypeId5);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId5);
        //20 from reservation night, 20 from group master  20*(5-4)
        verifyTotalProfitAtMktSegLevel(new BigDecimal("40.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId3, accomTypeId6);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("40.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId6);
        //70 from reservation night, 0 from group master  0*(5-4)
        verifyTotalProfitAtMktSegLevel(new BigDecimal("70.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId4, accomTypeId7);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("70.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId7);
    }

    @Test
    public void verifyProfitWhenServiceCostNotApplicableDueOccupancyDateIsNotGreaterThanArrivalDate_non_zero_optimization() {
        //Given
        setUpReservationNightData();
        setUpGroupMasterData(DEFINITE);
        setUpGroupBlockData();
        setUpServiceCostCfgData(new BigDecimal("10.00000").setScale(5, RoundingMode.DOWN));
        //When
        tenantCrudService().executeUpdateByNativeQuery("exec usp_populate_profit_ccv3_non_zero_optimization_exclude_complimentary_revenue " + "'" + caughtUpDate + "'" + ",'" + startDate + "','" + endDate + "'," + propertyId + "," + "1");
        //Then
        //120+0+40+70 = 230 at property level as profit in first query will have revenue value as is and service cost 5 will be considered in 4th query
        verifyTotalProfitAtPropertyLevel(new BigDecimal("230.00000").setScale(5, RoundingMode.DOWN), startDate);
        //50 from reservation night, 70 from group master  70*(9-8)
        verifyTotalProfitAtMktSegLevel(new BigDecimal("120.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId1, accomTypeId4);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("120.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId4);
        //0 as complimentary market segment
        verifyTotalProfitAtMktSegLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId2, accomTypeId5);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId5);
        //20 from reservation night, 20 from group master  20*(5-4)
        verifyTotalProfitAtMktSegLevel(new BigDecimal("40.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId3, accomTypeId6);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("40.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId6);
        //70 from reservation night, 0 from group master  0*(5-4)
        verifyTotalProfitAtMktSegLevel(new BigDecimal("70.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId4, accomTypeId7);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("70.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId7);
    }

    @Test
    public void verifyProfitWhenSomeReservationsAreCancelledOrNoShows_non_zero_optimization() {
        //Given
        setUpReservationNightData();
        setUpGroupMasterData(DEFINITE);
        setUpGroupBlockData();
        setUpServiceCostCfgData(new BigDecimal("10.00000").setScale(5, RoundingMode.DOWN));
        tenantCrudService().executeUpdateByNativeQuery("update Reservation_Night set Individual_status = 'NS' where Reservation_Identifier= 12345678902");
        tenantCrudService().executeUpdateByNativeQuery("update Reservation_Night set Individual_status = 'XX' where Reservation_Identifier= 12345678904");
        //When
        tenantCrudService().executeUpdateByNativeQuery("exec usp_populate_profit_ccv3_non_zero_optimization_exclude_complimentary_revenue " + "'" + caughtUpDate + "'" + ",'" + startDate + "','" + endDate + "'," + propertyId + "," + "1");
        //Then
        //120+0+40+70 = 230 at property level as profit in first query and 3rd query will have revenue value as is and service cost 5 will be considered in 4th query
        verifyTotalProfitAtPropertyLevel(new BigDecimal("230.00000").setScale(5, RoundingMode.DOWN), startDate);
        //50 from reservation night, 70 from group master  70*(9-8)  // first query
        verifyTotalProfitAtMktSegLevel(new BigDecimal("120.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId1, accomTypeId4);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("120.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId4);
        //0 as complimentary market segment
        verifyTotalProfitAtMktSegLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId2, accomTypeId5);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId5);
        //20 from reservation night, 20 from group master  20*(5-4) // first query
        verifyTotalProfitAtMktSegLevel(new BigDecimal("40.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId3, accomTypeId6);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("40.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId6);
        //70 from reservation night, 0 from group master  0*(5-4) // third query as Individual_status = XX
        verifyTotalProfitAtMktSegLevel(new BigDecimal("70.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId4, accomTypeId7);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("70.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId7);
    }

    @Test
    public void verifyProfitWhenGroupMasterEntriesWithGroupStatusCodeIsOtherThanDefinite_non_zero_optimization() {
        //Given
        setUpReservationNightData();
        setUpGroupMasterData(TENTATIVE);
        setUpGroupBlockData();
        setUpServiceCostCfgData(new BigDecimal("10.00000").setScale(5, RoundingMode.DOWN));
        //When
        tenantCrudService().executeUpdateByNativeQuery("exec usp_populate_profit_ccv3_non_zero_optimization_exclude_complimentary_revenue " + "'" + caughtUpDate + "'" + ",'" + startDate + "','" + endDate + "'," + propertyId + "," + "1");
        //Then
        //50+0+20+70 = 140 at property level as profit in first query  will have revenue value as is and nothing from remaining queries
        verifyTotalProfitAtPropertyLevel(new BigDecimal("140.00000").setScale(5, RoundingMode.DOWN), startDate);
        //50 from reservation night, 0 from group master
        verifyTotalProfitAtMktSegLevel(new BigDecimal("50.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId1, accomTypeId4);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("50.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId4);
        //0 as complimentary market segment
        verifyTotalProfitAtMktSegLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId2, accomTypeId5);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId5);
        //20 from reservation night, 0 from group master
        verifyTotalProfitAtMktSegLevel(new BigDecimal("20.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId3, accomTypeId6);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("20.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId6);
        //70 from reservation night, 0 from group master
        verifyTotalProfitAtMktSegLevel(new BigDecimal("70.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId4, accomTypeId7);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("70.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId7);
    }

    @Test
    public void verifyProfitWhenEntriesPresentInGroupData_non_zero_optimization() {
        //Given
        setUpGroupMasterData(DEFINITE);
        setUpGroupBlockData();
        setUpServiceCostCfgData(new BigDecimal("10.00000").setScale(5, RoundingMode.DOWN));
        //When
        tenantCrudService().executeUpdateByNativeQuery("exec usp_populate_profit_ccv3_non_zero_optimization_exclude_complimentary_revenue " + "'" + caughtUpDate + "'" + ",'" + startDate + "','" + endDate + "'," + propertyId + "," + "1");
        //Then
        //70+0+20+0 = 90 at property level profit comes from 4th query with service cost 5
        verifyTotalProfitAtPropertyLevel(new BigDecimal("90.00000").setScale(5, RoundingMode.DOWN), startDate);
        //70*(9-8)
        verifyTotalProfitAtMktSegLevel(new BigDecimal("70.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId1, accomTypeId4);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("70.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId4);
        //0 as complimentary market segment
        verifyTotalProfitAtMktSegLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId2, accomTypeId5);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId5);
        //20 from group master  20*(5-4)
        verifyTotalProfitAtMktSegLevel(new BigDecimal("20.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId3, accomTypeId6);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("20.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId6);
        //0 from group master  0*(5-4)
        verifyTotalProfitAtMktSegLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, mktSegId4, accomTypeId7);
        verifyTotalProfitAtAccomTypeLevel(new BigDecimal("0.00000").setScale(5, RoundingMode.DOWN), startDate, accomTypeId7);
    }
    private void updateMktSegToMarkComplimentary() {
        String updateSQL = "Update mkt_seg set complimentary=1 where mkt_seg_id = 2";
        tenantCrudService().executeUpdateByNativeQuery(updateSQL);
    }
    private void setUpReservationNightData() {
        tenantCrudService().executeUpdateByNativeQuery("delete from Reservation_Night");
        insertDataIntoReservationNight(getRnParameters(propertyId, startDate, endDate, "CHTSOC", mktSegId1, accomTypeId4, roomRevenueForMktSegId1, "12345678901"));
        insertDataIntoReservationNight(getRnParameters(propertyId, startDate, endDate, "CHTSOC", mktSegId2, accomTypeId5, roomRevenueForMktSegId2, "12345678902"));
        insertDataIntoReservationNight(getRnParameters(propertyId, startDate, endDate, "CHTSOC", mktSegId3, accomTypeId6, roomRevenueForMktSegId3, "12345678903"));
        insertDataIntoReservationNight(getRnParameters(propertyId, startDate, endDate, "CHTSOC", mktSegId4, accomTypeId7, roomRevenueForMktSegId4, "12345678904"));
    }

    private void setupLastOptimizationAccomActivity(LocalDate occupancyDt) {
        String insertSQL = "INSERT INTO Last_Optimization_Accom_Activity " +
                "SELECT Accom_Activity_ID, Property_ID, Occupancy_DT, Accom_Type_ID, " +
                "Accom_Capacity, Rooms_Sold, Rooms_Not_Avail_Maint, Rooms_Not_Avail_Other, Room_Revenue, Total_Profit " +
                "FROM Accom_Activity WHERE Occupancy_DT ='" +
                occupancyDt +
                "'";
        tenantCrudService().executeUpdateByNativeQuery(insertSQL);
    }

    private void setupLastOptimizationMktAccomActivityData(LocalDate occupancyDt) {
        String insertSQL = "INSERT INTO Last_Optimization_Mkt_Accom_Activity " +
                "SELECT Mkt_Accom_Activity_ID, Property_ID, " +
                "Occupancy_DT, Mkt_Seg_ID, Accom_Type_ID, " +
                "Rooms_Sold, Room_Revenue, File_Metadata_ID , Total_Profit " +
                "FROM Mkt_Accom_Activity " +
                "WHERE Occupancy_DT ='" +
                occupancyDt +
                "'";
        tenantCrudService().executeUpdateByNativeQuery(insertSQL);
    }

    private void setupLastOptimizationTotalActivity(LocalDate occupancyDt) {
        String insertSQL = "INSERT INTO Last_Optimization_Total_Activity " +
                "SELECT Total_Activity_ID, Property_ID, Occupancy_DT, Total_Accom_Capacity, " +
                "Rooms_Sold, Rooms_Not_Avail_Maint, Rooms_Not_Avail_Other, Room_Revenue,Total_Profit " +
                "FROM Total_Activity WHERE Occupancy_DT ='" +
                occupancyDt +
                "'";
        tenantCrudService().executeUpdateByNativeQuery(insertSQL);
    }
    private void setUpGroupMasterData(String groupStatusCode) {
        insertIntoGroupMaster(getGmParameters(propertyId, startDate, endDate, "CHTSOC_1", "USYS SOCCER TOURNAMENT", "CHTSOC", mktSegId1, groupStatusCode));
        insertIntoGroupMaster(getGmParameters(propertyId, startDate, endDate, "CHTSOC_2", "USYS SOCCER TOURNAMENT", "CHTSOC", mktSegId2, groupStatusCode));
        insertIntoGroupMaster(getGmParameters(propertyId, startDate, endDate, "CHTSOC_3", "USYS SOCCER TOURNAMENT", "CHTSOC", mktSegId3, groupStatusCode));
        insertIntoGroupMaster(getGmParameters(propertyId, startDate, endDate, "CHTSOC_4", "USYS SOCCER TOURNAMENT", "CHTSOC", mktSegId4, groupStatusCode));
    }

    private void setUpGroupBlockData() {
        int blocksForAccomTypeId4 = 9;
        int pickUpForAccomTypeId4 = 8;
        insertIntoGroupBlock(getGbParameters(startDate, "CHTSOC_1", accomTypeId4, blockRateForAccomTypeId4, blocksForAccomTypeId4, pickUpForAccomTypeId4));
        int blocksForAccomTypeId5 = 5;
        int pickUpForAccomTypeId5 = 4;
        insertIntoGroupBlock(getGbParameters(startDate, "CHTSOC_2", accomTypeId5, blockRateForAccomTypeId5, blocksForAccomTypeId5, pickUpForAccomTypeId5));
        int blocksForAccomTypeId6 = 5;
        int pickUpForAccomTypeId6 = 4;
        insertIntoGroupBlock(getGbParameters(startDate, "CHTSOC_3", accomTypeId6, blockRateForAccomTypeId6, blocksForAccomTypeId6, pickUpForAccomTypeId6));
        int blocksForAccomTypeId7 = 5;
        int pickUpForAccomTypeId7 = 4;
        insertIntoGroupBlock(getGbParameters(startDate, "CHTSOC_4", accomTypeId7, blockRateForAccomTypeId7, blocksForAccomTypeId7, pickUpForAccomTypeId7));
    }

    private void setUpServiceCostCfgData(BigDecimal interimServicingCost) {
        tenantCrudService().executeUpdateByNativeQuery("update Servicing_Cost_Cfg set Business_Type_ID=2 ");
        tenantCrudService().executeUpdateByNativeQuery("update Servicing_Cost_Cfg set Interim_Servicing_Cost= "+interimServicingCost);
    }


    private void setUpRevenueStreamDetail (LocalDate occupancyDt) {
        RevenueStream casinoRevenueStream = revenueStreamDetailCreator.createRevenueStream("100", "Casino", true);
        createRevenueStreamDetail(casinoRevenueStream.getId(), occupancyDt, mktSegId1, "CHTSOC", null,
                "100.50", "105.50", "50.50", "55.00", 3,
                null, "CPERP1", "STE", null);
        createRevenueStreamDetail(casinoRevenueStream.getId(), occupancyDt, mktSegId2, "CHTSOC", null,
                "110.50", "115.50", "52.50", "58.00", 7,
                null, "CPERP2", "STE", null);
        createRevenueStreamDetail(casinoRevenueStream.getId(), occupancyDt, mktSegId3, "CHTSOC", null,
                "100.50", "105.50", "50.50", "55.00", 3,
                null, "CPERP3", "STE", null);
        createRevenueStreamDetail(casinoRevenueStream.getId(), occupancyDt, mktSegId4, "CHTSOC", null,
                "110.50", "115.50", "52.50", "58.00", 7,
                null, "CPERP4", "STE", null);
    }

    private void setUpRevenueStreamCost(LocalDate startDate, LocalDate endDate) {
        String rn_query = "insert into revenue_stream_cost([Revenue_Stream_ID],[Start_Date],[End_Date],[Percentage_Cost]) "
                + "values( (select Revenue_Stream_ID from Revenue_Stream where code='100'),'"+ startDate + "','"+endDate+"', 10)";
        tenantCrudService().executeUpdateByNativeQuery(rn_query);
    }

    private void createRevenueStreamDetail(Integer casinoRevenueStreamId,
                                           LocalDate occupancyDt, Integer casinoCash11Qyl,
                                           String rateCode, Integer tier, String estimateValue,
                                           String actualValue, String estimateFixedCost, String actualFixedCost,
                                           Integer roomNightCount, Integer LOS, String mktSegCode,
                                           String accomTypeCode, String blockCode) {
        revenueStreamDetailCreator.createRevenueStreamDetail(casinoRevenueStreamId, occupancyDt, casinoCash11Qyl, rateCode,
                tier, estimateValue, actualValue, estimateFixedCost, actualFixedCost,
                roomNightCount, LOS, mktSegCode, accomTypeCode, blockCode);
    }
    private LocalDate getLocalDate() {
        List<Object> caughtUpDates = tenantCrudService().findByNativeQuery("select dbo.ufn_get_caughtup_date_by_property(:propertyId,3,13)", Map.of("propertyId", propertyId));
        String str = caughtUpDates.get(0).toString();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD);
        return LocalDate.parse(str, formatter);
    }

    private void insertDataIntoReservationNight(Map<String, Object> parameters) {
        String rn_query = "insert into Reservation_Night([File_Metadata_ID],[Property_ID],[Reservation_Identifier],[Individual_Status],[Arrival_DT],[Departure_DT], \n"
                + "[Booking_DT],[Cancellation_DT],[Booked_Accom_Type_Code],[Accom_Type_ID],[Mkt_Seg_ID],[Room_Number],[Booking_type],[Room_Revenue],[Food_Revenue],[Total_Revenue],[Rate_Code], \n"
                + "[Rate_Value],[Number_Children],[Number_Adults],[Occupancy_DT])  \n"
                + "values(1, :propertyId, :reservationIdentifier,'SS',:arrivalDate,:departureDate,'2022-11-05',null,'SXBL', :accountTypeId, :mktSegId, 1108, 'IN', :roomRevenue, 0.00, 0.00,:rateCode,50.00,0,2,:arrivalDate);";
        tenantCrudService().executeUpdateByNativeQuery(rn_query, parameters);
    }

    private void insertIntoGroupMaster(Map<String, Object> parameters) {
        String gm_query = "insert into group_master (property_id,Group_Code,Group_Name,Group_Description,Master_Group_Code,mkt_seg_id,Group_Status_Code,Group_Type_Code,Start_DT,End_DT,Booking_DT,Cut_Off_date,Cut_Off_days) " + "values(:propertyId, :groupCode, :groupName, 'Test GM',:masterGroupCode, :mktSegId, :groupStatusCode ,'TRANS',:startDate,:endDate,'2022-01-11','2023-01-11',0)";
        tenantCrudService().executeUpdateByNativeQuery(gm_query, parameters);
    }

    private void insertIntoGroupBlock(Map<String, Object> parameters) {
        String gb_query = " insert into Group_Block (Group_ID, Occupancy_DT, Accom_Type_ID, Blocks, Pickup, Original_Blocks, rate, IsPeakNight) " + "values ((select Group_ID from Group_Master where Group_Code=:groupCode),:occupancyDate,:accountTypeId,:blocks,:pickUp,11,:blockRate,1)";
        tenantCrudService().executeUpdateByNativeQuery(gb_query, parameters);
    }

    private Map<String, Object> getGmParameters(int propertyId, LocalDate startDate, LocalDate endDate, String groupCode, String groupName, String masterGroupCode, int mktSegId, String groupStatusCode) {
        return QueryParameter.with("propertyId", propertyId).and("groupCode", groupCode).and("groupName", groupName).and("masterGroupCode", masterGroupCode).and("startDate", startDate).and("endDate", endDate).and("mktSegId", mktSegId).and("groupStatusCode", groupStatusCode).parameters();
    }

    private Map<String, Object> getRnParameters(int propertyId, LocalDate startDate, LocalDate endDate, String rateCode, int mktSegId, int accountTypeId, BigDecimal roomRevenue, String reservationIdentifier) {
        return QueryParameter.with("propertyId", propertyId).and("arrivalDate", startDate).and("departureDate", endDate).and("rateCode", rateCode).and("mktSegId", mktSegId).and("accountTypeId", accountTypeId).and("roomRevenue", roomRevenue).and("reservationIdentifier", reservationIdentifier).parameters();
    }

    private Map<String, Object> getGbParameters(LocalDate startDate, String groupCode, int accountTypeId, BigDecimal blockRate, int blocks, int pickUp) {
        return QueryParameter.with("groupCode", groupCode).and("occupancyDate", startDate).and("accountTypeId", accountTypeId).and("blockRate", blockRate).and("blocks", blocks).and("pickUp", pickUp).parameters();
    }

    private void verifyTotalProfitAtPropertyLevel(BigDecimal expectedProfitValue, LocalDate occupancyDt) {
        List<Object[]> totalActivityEntries = tenantCrudService().findByNativeQuery("select Total_Profit from Total_Activity where occupancy_dt=" + "'" + occupancyDt + "'");
        assertEquals(expectedProfitValue, totalActivityEntries.get(0));
        List<Object[]> paceTotalActivityEntries = tenantCrudService().findByNativeQuery("select Total_Profit from PACE_Total_Activity where occupancy_dt='" + occupancyDt + "' and Business_Day_End_DT = " + "'" + BusinessDayEndDt + "'");
        assertEquals(expectedProfitValue, paceTotalActivityEntries.get(0));
        List<Object[]> lastOptimizationTotalActivityEntries = tenantCrudService().findByNativeQuery("select Total_Profit from Last_Optimization_Total_Activity where occupancy_dt=" + "'" + occupancyDt + "'");
        assertEquals(expectedProfitValue, lastOptimizationTotalActivityEntries.get(0));
    }
    private void verifyTotalProfitAtMktSegLevel(BigDecimal expectedProfitValue, LocalDate occupancyDt, int mktSegId, int accomTypeId) {
        List<Object[]> mktAccomActivityEntries = tenantCrudService().findByNativeQuery("select Total_Profit from Mkt_Accom_Activity where occupancy_dt='" + occupancyDt + "' and mkt_Seg_id=" + mktSegId + " and accom_type_id=" + accomTypeId);
        assertEquals(expectedProfitValue, mktAccomActivityEntries.get(0));
        List<Object[]> paceMktActivityEntries = tenantCrudService().findByNativeQuery("select Total_Profit from PACE_Mkt_Activity where occupancy_dt='" + occupancyDt + "' and mkt_Seg_id=" + mktSegId + " and Business_Day_End_DT = " + "'" + BusinessDayEndDt + "'");
        assertEquals(expectedProfitValue, paceMktActivityEntries.get(0));
        List<Object[]> lastOptimizationMktAccomActivityEntries = tenantCrudService().findByNativeQuery("select Total_Profit from Last_Optimization_Mkt_Accom_Activity where occupancy_dt=" + "'" + occupancyDt + "' and mkt_Seg_id=" + mktSegId + " and accom_type_id=" + accomTypeId);
        assertEquals(expectedProfitValue, lastOptimizationMktAccomActivityEntries.get(0));
    }
    private void verifyTotalProfitAtAccomTypeLevel(BigDecimal expectedProfitValue, LocalDate occupancyDt, int accomTypeId) {
        List<Object[]> accomActivityEntries = tenantCrudService().findByNativeQuery("select Total_Profit from Accom_Activity where occupancy_dt='" + occupancyDt + "' and accom_type_id=" + accomTypeId);
        assertEquals(expectedProfitValue, accomActivityEntries.get(0));
        List<Object[]> paceAccomActivityEntries = tenantCrudService().findByNativeQuery("select Total_Profit from PACE_Accom_Activity where occupancy_dt='" + occupancyDt + "' and accom_type_id=" + accomTypeId + " and Business_Day_End_DT =" + "'" + BusinessDayEndDt + "'");
        if(accomTypeId == accomTypeId5) {
            assertNull(paceAccomActivityEntries.get(0));
        } else {
            assertEquals(expectedProfitValue, paceAccomActivityEntries.get(0));
        }
        List<Object[]> lastOptimizationAccomActivityEntries = tenantCrudService().findByNativeQuery("select Total_Profit from Last_Optimization_Accom_Activity where occupancy_dt=" + "'" + occupancyDt + "' and accom_type_id=" + accomTypeId);
        assertEquals(expectedProfitValue, lastOptimizationAccomActivityEntries.get(0));
    }
}
